package utils.multichannel

import org.apache.pekko.actor.ActorSystem
import api.accounts.email.models.EmailServiceProvider
import api.accounts.{Account, AccountAccess, AccountDAO, AccountMetadata, AccountService, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, PermType, PermissionLevelForValidation, PermissionOwnershipV2, ProspectCategoriesInDB, ReplyHandling, RolePermV2, RolePermissionDataDAOV2, RolePermissionDataV2, RolePermissionsV2, TeamAccount, TeamAccountRole, TeamId, TeamMember, TeamMemberLite}
import io.sr.billing_common.models.{PlanID, PlanType}
import api.accounts.models.AccountProfileInfo
import api.accounts.service.AccountOrgBillingRelatedService
import io.sr.billing_common.models.{PlanID, PlanType}
import api.accounts.models.{AccountId, AccountProfileInfo, OrgId}
import api.calendar_app.CalendarAppService
import api.calendar_app.models.CalendarAccountData
import api.campaigns.{Campaign, CampaignCreateForm, CampaignDAO, CampaignEditedPreviewEmailDAO, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignProspectDAO, CampaignSettings, CampaignStepDAO, CampaignStepVariant, CampaignStepVariantCreateOrUpdate, CampaignStepVariantDAO, CampaignWithStatsAndEmail, ChannelSettingUuid}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignName, CampaignStepData, CampaignStepType, CampaignType, IgnoreProspectsInOtherCampaigns}
import api.campaigns.services.{CallSettingSenderDetails, CampaignCreationError, CampaignId, CampaignProspectService, CampaignService, CampaignStartService, CampaignStepService, CampaignsMissingMergeTagService, CreateCampaignStepVariantError, DeleteCampaignTasksError, LinkedinSettingSenderDetails, SmsSettingSenderDetails, StartCampaignError, WhatsappSettingSenderDetails}
import api.emails.{CampaignProspectStepScheduleLogsDAO, EmailSetting, SendNewManualEmailV3}
import api.emails.models.DeletionReason
import api.emails.services.SelectAndPublishForDeletionService
import api.prospects.models.{ProspectCategoryRank, UpdateProspectType}
import api.prospects.{CreateOrUpdateProspectsResult, InboxV3Service, InferredQueryTimeline, ProspectCreateFormData, ProspectService, SendNewEmailManuallyError}
import api.reports.AllCampaignStats
import api.scheduler.model.SchedulerInputForIntegrationTest
import api.tags.models.CampaignTag
import api.tasks.models.{SearchTask, Task, TaskPriority, TimeBasedTaskType}
import api.tasks.pgDao.{TaskPgDAO, ValidatedTaskReq}
import api.tasks.services.TaskService
import api.team.TeamUuid
import eventframework.SrResourceTypes
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import play.api.libs.json.Json
import play.api.libs.ws.ahc.AhcWSClient
import sr_scheduler.CampaignStatus
import sr_scheduler.models.ChannelData.{EmailChannelData, LinkedinChannelData}
import sr_scheduler.models.{CampaignDataToAddNextToBeScheduledAtForEmailChannel, CampaignEmailPriority, ChannelData, ChannelType, EmailChannelType}
import utils.SRLogger
import utils.cache_utils.model.CampaignUseStatusForEmailSetting
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import utils.email.models.DeleteEmailsScheduledType
import utils.email.EmailServiceCompanion
import utils.email_notification.service.EmailNotificationService
import utils.featureflags.services.SrFeatureFlags
import utils.mq.channel_scheduler.channels.{LinkedinChannelScheduler, ScheduleTasksData}
import utils.mq.webhook.MQWebhookCompleted
import utils.shuffle.SrShuffleUtils
import utils.templating.TemplateService
import utils.testapp.AllBlacklistDAO.{emailChannelScheduler, emailSettingDAO}
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try


class MultichannelTestUtil(
                            campaignService: CampaignService,
                            prospectService: ProspectService,
                            campaignStepService: CampaignStepService,
                            campaignStartService: CampaignStartService,
                            linkedinChannelScheduler: LinkedinChannelScheduler,
                            accountService: AccountService,
                            accountDAO: AccountDAO,
                            campaignDAO: CampaignDAO,
                            emailNotificationService: EmailNotificationService,
                            campaignProspectDAO: CampaignProspectDAO,
                            campaignProspectService: CampaignProspectService,
                            campaignStepVariantDAO: CampaignStepVariantDAO,
                            campaignStepDAO: CampaignStepDAO,
                            srShuffleUtils: SrShuffleUtils,
                            emailServiceCompanion: EmailServiceCompanion,
                            templateService: TemplateService,
                            taskDAO: TaskPgDAO,
                            taskService: TaskService,
                            campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO,
                            campaignsMissingMergeTagService: CampaignsMissingMergeTagService,
                            srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2,
                            mqWebhookCompleted: MQWebhookCompleted,
                            accountOrgBillingRelatedService: AccountOrgBillingRelatedService,
  srRollingUpdateCoreService: SrRollingUpdateCoreService,
                            calendarAppService: CalendarAppService,
                            selectAndPublishForDeletionService: SelectAndPublishForDeletionService,
                            inboxV3Service: InboxV3Service,
                            campaignProspectStepScheduleLogsDAO: CampaignProspectStepScheduleLogsDAO
                          ) {

  val orgId = 22L
  val accountId = 19L
  val teamId = 17L
  val taId = 17L
  val timezone = "Asia/Kolkata"
  val linkedinAccountSettingId = "linkedin_account_2K5bzImWQivNTkXhGXwOg6sZ9CP"
  implicit lazy val system: ActorSystem = ActorSystem()
  given logger: SRLogger = new SRLogger(logRequestId = "MultichannelTestUtil :: ")
  implicit lazy val actorContext: ExecutionContext = system.dispatcher
  implicit lazy val wSClient: AhcWSClient = AhcWSClient()

  def createAndScheduleCampaign() = {

    val campaignSettings = CampaignSettings(
      // settings
      campaign_email_settings = List(
        CampaignEmailSettings(
          campaign_id = CampaignId(26),
          sender_email_setting_id = EmailSettingId(26),
          receiver_email_setting_id = EmailSettingId(26),
          team_id = TeamId(teamId),
          uuid = CampaignEmailSettingsUuid("temp_setting_id"),
          id = CampaignEmailSettingsId(26),
          sender_email = "<EMAIL>",
          receiver_email = "<EMAIL>",
          max_emails_per_day_from_email_account = 100,
          signature = None,
          error = None,
          from_name = None
        )
      ),
      campaign_linkedin_settings = List(
        LinkedinSettingSenderDetails(
          channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
          team_id = TeamId(id = teamId),
          email = "<EMAIL>",
          first_name ="gokulnath",
          last_name = "S",
          linkedin_profile_url = Some("www.linekdin.com/SRTest"),
          automation_enabled  = false
        ),
        LinkedinSettingSenderDetails(
          channel_setting_uuid = ChannelSettingUuid(uuid = "2"),
          team_id = TeamId(id = teamId),
          email = "<EMAIL>",
          first_name ="Guru",
          last_name = "D",
          linkedin_profile_url = Some("www.linekdin.com/guru"),
          automation_enabled  = true
        )
      ),
      campaign_call_settings = List(
        CallSettingSenderDetails(
          channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
          team_id = TeamId(id = teamId),
          phone_number = Some("0123456789"),
          first_name = "gokulnath",
          last_name = "S"
        )
      ),
      campaign_whatsapp_settings = List(
        WhatsappSettingSenderDetails(
          channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
          team_id = TeamId(id = teamId),
          phone_number = "0123456789",
          first_name = "gokulnath",
          last_name = "S"
        )
      ),
      campaign_sms_settings = List(
        SmsSettingSenderDetails(
          channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
          team_id = TeamId(id = teamId),
          phone_number = "0123456789",
          first_name = "gokulnath",
          last_name = "S"
        )
      ),
      timezone = timezone,
      daily_from_time = 0, // time since beginning of day in seconds
      daily_till_time = 86400, // time since beginning of day in seconds
      sending_holiday_calendar_id = None,

      // Sunday is the first day
      days_preference = List(true, true, true, true, true, true, true),

      mark_completed_after_days = 1,
      max_emails_per_day = 100,
      open_tracking_enabled = true,
      click_tracking_enabled = true,
      enable_email_validation = true,
      ab_testing_enabled = true,

      ai_sequence_status = None,

      // warm up
      warmup_started_at = None,
      warmup_length_in_days = None,
      warmup_starting_email_count = None,
      show_soft_start_setting = false,

      // schedule start
      schedule_start_at = None,
      schedule_start_at_tz = None,
      send_plain_text_email = Some(false),
      campaign_type = CampaignType.MultiChannel,

      email_priority = CampaignEmailPriority.EQUAL,
      append_followups = true,
      opt_out_msg = "Pivot",
      opt_out_is_text = true,
      add_prospect_to_dnc_on_opt_out = true,
      triggers = Seq(),
      sending_mode = None,
      selected_calendar_data = None
    )

    val createCampaignData = CampaignCreateForm(
      name = Some("TestApp Campaign"),
      timezone = Some(timezone),
      campaign_owner_id = Some(19L),
      campaign_type = CampaignType.MultiChannel // Multichannel type campaign is getting created
    )

    val prospectCreateFormData = ProspectCreateFormData(
      email = Some("<EMAIL>"),
      first_name = None,
      last_name = None,
      custom_fields = Json.obj(),

      list = None,
      company = None,
      city = None,
      country = None,
      timezone = Some(timezone),

      state = None,
      job_title = None,
      phone = None,
      phone_2 = None,
      phone_3 = None,
    )

    val accountProfileInfo = AccountProfileInfo(
      first_name = "Aditya",
      last_name = "Sadana",
      company = None,
      timezone = Some(timezone),
      country_code = None,
      mobile_country_code = None,
      mobile_number = None,
      onboarding_phone_number= None,
      twofa_enabled = true,
      has_gauthenticator = true,
      weekly_report_emails = None,
      scheduled_for_deletion_at = None
    )

    val teamMember = TeamMember(
      team_id = teamId,
      team_name = "smartreach",
      user_id = accountId,
      ta_id = taId, // dont send ta_id to frontend / api response, only for internal purpose, its dynamically assigned in AuthUtils
      first_name = None,
      last_name = None,
      email = "<EMAIL>",
      team_role = TeamAccountRole.ADMIN,
      api_key = Some("abcd1234"),
      zapier_key = Some("zapier_key")
    )

    val teamMemberLite = TeamMemberLite(
      user_id = accountId,
      first_name = None,
      last_name = None,
      email = "<EMAIL>",
      active = true,
      timezone = Some(timezone),
      twofa_enabled = true,
      created_at = DateTime.now(),
      user_uuid = AccountUuid(uuid = "uuid"),
      team_role = TeamAccountRole.ADMIN
    )

    val prospectCategoriesInDB = ProspectCategoriesInDB(
      id = 153,
      name = "Do not contact",
      text_id = "do_not_contact",
      label_color = "#d52728",
      is_custom = false,
      team_id = teamId,
      rank = ProspectCategoryRank(rank = 2000),
    )

    val prospectCategoriesInDB2 = ProspectCategoriesInDB(
      id = 152,
      name = "Not Categorized",
      text_id = "not_categorized",
      label_color = "#d52728",
      is_custom = false,
      team_id = teamId,
      rank = ProspectCategoryRank(rank = 2000),
    )

    val adminDefaultPermissions = RolePermissionDataDAOV2.defaultRoles(
      role = TeamAccountRole.ADMIN,
      simpler_perm_flag = false
    )

    val rolePermissionData = RolePermissionDataV2.toRolePermissionApi(
      data = adminDefaultPermissions.copy(id = 2)
    )

    val teamAccount = TeamAccount(

      team_id = teamId,
      org_id = orgId,

      role_from_db = None, // MUST come from db (option type only for cacheservice error), should not be sent to frontend, only intermediate
      role = Some(rolePermissionData), // should be sent to frontend
      active = true,
      is_actively_used = true,
      team_name = "smartreach",
      total_members = 1,
      access_members = Seq(teamMember),
      all_members = Seq(teamMemberLite),

      prospect_categories_custom = Seq(prospectCategoriesInDB, prospectCategoriesInDB2),
      max_emails_per_prospect_per_day = 100L,
      max_emails_per_prospect_per_week = 1000L,
      max_emails_per_prospect_account_per_day = 97,
      max_emails_per_prospect_account_per_week = 497,

      // ADMIN SETTINGS FOR MULTICAMPAIGN
      // allow_assigning_prospects_to_multiple_campaigns: Boolean, FORCEASSIGNISSUE
      reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
      created_at = DateTime.now(),
      selected_calendar_data = None,
      team_uuid = TeamUuid("team_uuid")
    )

    val orgCountData = OrgCountData(
      base_licence_count = 50,
      additional_licence_count = 50,

      current_sending_email_accounts = 50,
      total_sending_email_accounts = 50,

      // max_api_calls_per_month: Int,
      additional_spam_tests = 50,

      max_prospect_limit_org = 50,
      current_prospect_sent_count_org = 50,

      max_li_manual_seats = 67,
      current_li_manual_seats = 12,

      max_li_automation_seats = 50,
      current_li_automation_seats = 25,

      max_calling_accounts = 50,
      current_calling_accounts = 25,

      max_crm_integrations = 50,
      current_crm_integrations = 25,

      max_client_teams = 50,
      current_client_teams = 25,
      current_active_client_teams = 7,

      max_prospects_saved = 50,
      current_prospects_saved = 25,

      max_purchased_domains = 2,
      current_purchased_domains = 1,

      max_purchased_email_accounts = 4,
      current_purchased_email_accounts = 1,

      max_purchased_zapmail_domains = 5,
      current_purchased_zapmail_domains = 4,

      max_purchased_zapmail_email_accounts = 4,
      current_purchased_zapmail_email_accounts = 2,

      current_active_team_inboxes = 3,

      max_phone_number_buying_limit_org = SrFeatureFlags.maxPhoneNumberToBuyLimitForOrg(
        calling_flag = Some(false),
        total_email_limit = 50),
      has_active_call_campaigns = true,
    )

    val orgSettings = OrgSettings(
      enable_ab_testing = true,
      disable_force_send = true,
      bulk_sender = true,
      allow_2fa = true,
      show_2fa_setting = true,
      enforce_2fa = true,

      // for zoho, pipedrive, hubspot crms (salesforce will have a different flag)
      allow_native_crm_integration = true,
        agency_option_allow_changing = false,
        agency_option_show = false
    )

    val orgPlan = OrgPlan(
      new_prospects_paused_till = None,
      is_v2_business_plan = true,
      
      
      fs_account_id = None,
      stripe_customer_id = None,
      payment_gateway = None,
      next_billing_date = None,
      current_cycle_started_at = DateTime.now(),

      payment_due_invoice_link = None,
      payment_due_campaign_pause_at = None,

      plan_type = PlanType.PAID,
      plan_name = "ultimate-annual-user-inr-base-v2",
      plan_id = PlanID.ULTIMATE
    )

    val orgMetadata = OrgMetadata(
      allow_user_level_api_key = None,

      ff_multichannel = Some(true),
      is_onboarding_done = Some(true),
      show_agency_pricing = Some(true),

      show_promo_option = Some(true),
      show_individual_plans = Some(true),
      show_business_plans = Some(true),
      show_business_pro_plan = Some(true),

      ff_emails_sent_report = Some(true),


      show_campaign_tags = Some(true),
      allowed_for_new_google_api_key = Some(true),
      increase_email_delay = Some(true)
    )

    val organizationWithCurrentData = OrganizationWithCurrentData(
      id = orgId,
      name = "smartreach",
      owner_account_id = accountId,
      counts = orgCountData,
      settings = orgSettings,
      plan = orgPlan,

      is_agency = false,
      trial_ends_at = DateTime.now().plusDays(9),
      error = None,
      error_code = None,
      paused_till = None,

      errors = Seq(),
      warnings = Seq(),

      via_referral = false,

      org_metadata = orgMetadata

    )


    val account = Account(
      id = AccountUuid("account_uuid"),
      internal_id = accountId,
      email = "<EMAIL>",
      email_verification_code = None,
      email_verification_code_created_at = None,
      created_at = DateTime.now().minusDays(7),

      first_name = None,
      last_name = None,
      company = None,
      timezone = None,

      profile = accountProfileInfo,

      org_role = Some(OrganizationRole.OWNER),

      teams = Seq(teamAccount),
      account_type = AccountType.TEAM,
      org = organizationWithCurrentData,
      active = true,
      email_notification_summary = "",
      account_metadata = AccountMetadata(is_profile_onboarding_done = Some(true)),
      email_verified = true,
      signupType = None,
      account_access = AccountAccess(inbox_access = true),
      calendar_account_data = None
    )

    val campaignStepDataLinkedin = CampaignStepData.LinkedinInmailData(
      subject = Some("Linkedin Inmail Subject Test Campaign - 1"),
      body = "Linkedin Inmail Body Test Campaign - 1"
    )

    val campaignStepVariantCreateOrUpdate = CampaignStepVariantCreateOrUpdate(
      parent_id = 0,
      step_data = campaignStepDataLinkedin,
      step_delay = 0,
      notes = Some("Linkedin Inmail Step Note"),
      priority = Some(TaskPriority.High),
    )

    for {

      // create a campaign
      campaignWithStatsAndEmailEither:Either[CampaignCreationError, CampaignWithStatsAndEmail] <- campaignService.createCampaign(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        data = createCampaignData,
        campaignSettings = Some(campaignSettings),
        permittedAccountIdsForEditCampaigns = Seq(accountId),
        ownerFirstName = "MultiChannelTest Util"
      )

      campaignWithStatsAndEmail:CampaignWithStatsAndEmail <- campaignWithStatsAndEmailEither match {
        case Left(err) =>
          Future.failed(new Exception(s"Campaign Not Created: $err"))

        case Right(campaignWithStatsAndEmail) => Future.successful(campaignWithStatsAndEmail)
      }

      // add a prospect to the above campaign
      prospects: CreateOrUpdateProspectsResult <- Future.fromTry {
        prospectService.createOrUpdateProspects(
          ownerAccountId = accountId,
          teamId = teamId,
          listName = None,
          prospects = Seq(prospectCreateFormData),
          updateProspectType = UpdateProspectType.ForceUpdate,
          ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,

          doerAccount = account,
          prospectSource = None,
          prospectAccountId = None,

          campaign_id = Some(campaignWithStatsAndEmail.id),
          prospect_tags = None,
          ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
          deduplicationColumns = None,
          auditRequestLogId = None,

          SRLogger = logger
        )
      }

      createdVariant:Either[CreateCampaignStepVariantError, CampaignStepVariant] <- campaignStepService.createVariant(
        orgId = orgId,
        data = campaignStepVariantCreateOrUpdate,
        teamId = teamId,
        userId = accountId,
        taId = taId,
        stepId = 0,
        campaignId = campaignWithStatsAndEmail.id,
        campaignHeadStepId = None
      )

      campaign: Campaign <- Future.successful(
        campaignDAO.findCampaignForCampaignUtilsOnly(
          id = campaignWithStatsAndEmail.id,
          teamId = TeamId(campaignWithStatsAndEmail.team_id)
        ).get
      )

      startedCampaign:Either[StartCampaignError, CampaignWithStatsAndEmail] <- campaignStartService.startCampaign(
          c = campaign,
          team = Some(teamAccount),
          org = organizationWithCurrentData,
          scheduleStartAt = None,
          scheduleStartAtTimeZone = Some("Asia/Kolkata"),
          userId = accountId,
          teamId = teamId,
          Logger = logger
        )


      scheduledTask: ScheduleTasksData <- linkedinChannelScheduler.scheduleTasksForChannel(
        channelData = LinkedinChannelData(
          linkedinSettingId = linkedinAccountSettingId
        ),
        teamId = teamId,
        accountService = accountService,
        //accountDAO = accountDAO,
        emailNotificationService = emailNotificationService,
        campaignService = campaignService,
        campaignProspectDAO = campaignProspectDAO,
        campaignProspectService = campaignProspectService,
        campaignStepVariantDAO = campaignStepVariantDAO,
        campaignStepDAO = campaignStepDAO,
        srShuffleUtils = srShuffleUtils,
        emailServiceCompanion = emailServiceCompanion,
        templateService = templateService,
        taskDAO = taskDAO,
        taskService = taskService,
        campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
        campaignsMissingMergeTagService =  campaignsMissingMergeTagService,
        srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
        mqWebhookCompleted = mqWebhookCompleted,
        accountOrgBillingRelatedService = accountOrgBillingRelatedService,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
        calendarAppService = calendarAppService
      )

      // Deleting so that we can test again with same campaign name
      deletedTasks: Either[DeleteCampaignTasksError, Int] <- Future {
        campaignService.deleteCampaignAndAssociatedTasks(
          campaignId= CampaignId(id = campaign.id),
          teamId = TeamId(id = teamId)
      )}

    } yield {
      assert(scheduledTask.saved_tasks_count == 1)
    }

  }

  /**
    * Duplicate Task Testing for manual emails
    *
    * @param orgId
    * @param accountId
    * @param teamId
    * @param taId
    * @param campaign_name
    * @param timezone
    * @param linkedinAccountSettingId
    * @param receiver_email_setting_id
    * @param sender_email_setting_id
    * @param prospect_categories_custom_not_categorized
    * @param prospect_categories_custom_do_not_contact
    * @param current_sending_email_accounts
    * @param emailSettingId
    * @param schedule_from_time_sec
    * @param schedule_till_time_sec
    * @param enableEmailScheduler
    * @param prospectEmail
    */
  def createAndScheduleCampaignManualEmail(
                                            schedulerInputForIntegrationTest : SchedulerInputForIntegrationTest
                                          ): Future[List[Task]] = {

    val input: SchedulerInputForIntegrationTest = schedulerInputForIntegrationTest
    val campaignSettings = CampaignSettings(
      // settings
      campaign_email_settings = List(
        CampaignEmailSettings(
          campaign_id = CampaignId(26),
          sender_email_setting_id = input.campaign_email_setting_data.sender_email_setting_id,
          receiver_email_setting_id = input.campaign_email_setting_data.receiver_email_setting_id,
          team_id = input.team_id,
          uuid = CampaignEmailSettingsUuid("temp_setting_id"),
          id = CampaignEmailSettingsId(input.email_setting_id.emailSettingId),
          sender_email = "<EMAIL>",
          receiver_email = "<EMAIL>",
          max_emails_per_day_from_email_account = 100,
          signature = None,
          error = None,
          from_name = None
        )
      ),
      campaign_linkedin_settings = List(
        LinkedinSettingSenderDetails(
          channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
          team_id = input.team_id,
          email = "<EMAIL>",
          first_name = "gokulnath",
          last_name = "S",
          linkedin_profile_url = Some("www.linekdin.com/SRTest"),
          automation_enabled  = false
        ),LinkedinSettingSenderDetails(
          channel_setting_uuid = ChannelSettingUuid(uuid = "2"),
          team_id = TeamId(id = teamId),
          email = "<EMAIL>",
          first_name ="Guru",
          last_name = "D",
          linkedin_profile_url = Some("www.linekdin.com/guru"),
          automation_enabled  = true
        )
      ),
      campaign_call_settings = List(
        CallSettingSenderDetails(
          channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
          team_id = input.team_id,
          phone_number = input.campaign_call_settings_data.phone_number,
          first_name = "gokulnath",
          last_name = "S"
        )
      ),
      campaign_whatsapp_settings = List(
        WhatsappSettingSenderDetails(
          channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
          team_id = input.team_id,
          phone_number = input.campaign_whatsapp_settings_data.phone_number,
          first_name = "gokulnath",
          last_name = "S"
        )
      ),
      campaign_sms_settings = List(
        SmsSettingSenderDetails(
          channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
          team_id = input.team_id,
          phone_number = input.campaign_sms_settings_data.phone_number,
          first_name = "gokulnath",
          last_name = "S"
        )
      ),
      timezone = timezone,
      daily_from_time = input.schedule_from_time_sec, // time since beginning of day in seconds
      daily_till_time = input.schedule_till_time_sec, // time since beginning of day in seconds
      sending_holiday_calendar_id = None,

      // Sunday is the first day
      days_preference = List(true, true, true, true, true, true, true),

      mark_completed_after_days = 1,
      max_emails_per_day = 1000,
      open_tracking_enabled = true,
      click_tracking_enabled = true,
      enable_email_validation = false,
      ab_testing_enabled = true,

      ai_sequence_status = None,

      // warm up
      warmup_started_at = None,
      warmup_length_in_days = None,
      warmup_starting_email_count = None,
      show_soft_start_setting = false,

      // schedule start
      schedule_start_at = Option(DateTime.now()),
      schedule_start_at_tz = Option(timezone),

      send_plain_text_email = Some(false),
      campaign_type = CampaignType.MultiChannel,


      email_priority = CampaignEmailPriority.EQUAL,
      append_followups = true,
      opt_out_msg = "Pivot",
      opt_out_is_text = true,
      add_prospect_to_dnc_on_opt_out = true,
      triggers = Seq(),
      sending_mode = None,
      selected_calendar_data = None
    )

    val createCampaignData = CampaignCreateForm(
      name = Some(input.campaign_name.name),
      timezone = Some(timezone),
      campaign_owner_id = Some(19L),
      campaign_type = CampaignType.MultiChannel // Multichannel type campaign is getting created
    )

    val prospectCreateFormData = ProspectCreateFormData(
      email = Some(input.prospect_email),
      first_name = None,
      last_name = None,
      custom_fields = Json.obj(),

      list = None,
      company = None,
      city = None,
      country = None,
      timezone = Some(timezone),

      state = None,
      job_title = None,
      phone = None,
      phone_2 = None,
      phone_3 = None,
    )

    val accountProfileInfo = AccountProfileInfo(
      first_name = "Aditya",
      last_name = "Sadana",
      company = None,
      timezone = Some(timezone),
      country_code = None,
      mobile_country_code = None,
      mobile_number = None,
      onboarding_phone_number = None,
      twofa_enabled = true,
      has_gauthenticator = true,
      weekly_report_emails = None,
      scheduled_for_deletion_at = None
    )

    val teamMember = TeamMember(
      team_id = input.team_id.id,
      team_name = "smartreach",
      user_id = input.account_id.id,
      ta_id = input.ta_id, // dont send ta_id to frontend / api response, only for internal purpose, its dynamically assigned in AuthUtils
      first_name = None,
      last_name = None,
      email = "<EMAIL>",
      team_role = TeamAccountRole.ADMIN,
      api_key = Some("abcd1234"),
      zapier_key = Some("zapier_key")
    )

    val teamMemberLite = TeamMemberLite(
      user_id = input.account_id.id,
      first_name = None,
      last_name = None,
      email = "<EMAIL>",
      active = true,
      timezone = None,
      twofa_enabled = false,
      created_at = DateTime.now(),
      user_uuid = AccountUuid(uuid = "Account_uuid"),
      team_role = TeamAccountRole.OWNER,
    )

    val prospectCategoriesInDB = ProspectCategoriesInDB(
      id = input.prospect_categories_custom_do_not_contact,
      name = "Do not contact",
      text_id = "do_not_contact",
      label_color = "#d52728",
      is_custom = false,
      team_id = input.team_id.id,
      rank = ProspectCategoryRank(rank = 2000),
    )

    val prospectCategoriesInDB2 = ProspectCategoriesInDB(
      id = input.prospect_categories_custom_not_categorized,
      name = "Not Categorized",
      text_id = "not_categorized",
      label_color = "#d52728",
      is_custom = false,
      team_id = input.team_id.id,
      rank = ProspectCategoryRank(rank = 2000),
    )

    val adminDefaultPermissions = RolePermissionDataDAOV2.defaultRoles(
      role = TeamAccountRole.ADMIN,
      simpler_perm_flag = false
    )

    val rolePermissionData = RolePermissionDataV2.toRolePermissionApi(
      data = adminDefaultPermissions.copy(id = 2)
    )

    val teamAccount = TeamAccount(

      team_id = input.team_id.id,
      org_id = input.org_id.id,

      role_from_db = None, // MUST come from db (option type only for cacheservice error), should not be sent to frontend, only intermediate
      role = Some(rolePermissionData), // should be sent to frontend
      active = true,
      is_actively_used = true,
      team_name = "smartreach",
      total_members = 1,
      access_members = Seq(teamMember),
      all_members = Seq(teamMemberLite),

      prospect_categories_custom = Seq(prospectCategoriesInDB, prospectCategoriesInDB2),
      max_emails_per_prospect_per_day = 100L,
      max_emails_per_prospect_per_week = 1000L,
      max_emails_per_prospect_account_per_day = 97,
      max_emails_per_prospect_account_per_week = 497,

      // ADMIN SETTINGS FOR MULTICAMPAIGN
      // allow_assigning_prospects_to_multiple_campaigns: Boolean, FORCEASSIGNISSUE
      reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
      selected_calendar_data = None,
      created_at =  DateTime.now(),
      team_uuid = TeamUuid("team_uuid")
    )

    val orgCountData = OrgCountData(
      base_licence_count = 50,
      additional_licence_count = 50,

      current_sending_email_accounts = input.current_sending_email_accounts,
      total_sending_email_accounts = 50,

      // max_api_calls_per_month: Int,
      additional_spam_tests = 50,

      max_prospect_limit_org = 50,
      current_prospect_sent_count_org = 50,

      max_li_manual_seats = 9,
      current_li_manual_seats = 1,

      max_li_automation_seats = 50,
      current_li_automation_seats = 25,

      max_calling_accounts = 50,
      current_calling_accounts = 25,

      max_crm_integrations = 50,
      current_crm_integrations = 25,

      max_client_teams = 50,
      current_client_teams = 25,
      current_active_client_teams = 9,
      
      max_prospects_saved = 50,
      current_prospects_saved = 25,

      max_purchased_domains = 2,
      current_purchased_domains = 1,

      max_purchased_email_accounts = 4,
      current_purchased_email_accounts = 1,

      max_purchased_zapmail_domains = 5,
      current_purchased_zapmail_domains = 4,

      max_purchased_zapmail_email_accounts = 4,
      current_purchased_zapmail_email_accounts = 2,

      current_active_team_inboxes = 1,

      max_phone_number_buying_limit_org = SrFeatureFlags.maxPhoneNumberToBuyLimitForOrg(
        calling_flag = Some(false),
        total_email_limit = 50),
      has_active_call_campaigns = true,
    )

    val orgSettings = OrgSettings(
      enable_ab_testing = true,
      disable_force_send = true,
      bulk_sender = true,
      allow_2fa = true,
      show_2fa_setting = true,
      enforce_2fa = true,

      // for zoho, pipedrive, hubspot crms (salesforce will have a different flag)
      allow_native_crm_integration = true,
        agency_option_allow_changing = false,
        agency_option_show = false
    )

    val orgPlan = OrgPlan(
      new_prospects_paused_till = None,
      is_v2_business_plan = true,


      fs_account_id = None,
      stripe_customer_id = None,
      payment_gateway = None,
      next_billing_date = None,
      current_cycle_started_at = DateTime.now(),

      payment_due_invoice_link = None,
      payment_due_campaign_pause_at = None,

      plan_type = PlanType.PAID,
      plan_name = "ultimate-annual-user-inr-base-v2",
      plan_id = PlanID.ULTIMATE
    )

    val orgMetadata = OrgMetadata(
      allow_user_level_api_key = None,

      ff_multichannel = Some(true),

      is_onboarding_done = Some(true),
      show_agency_pricing = Some(true),

      show_promo_option = Some(true),
      show_individual_plans = Some(true),
      show_business_plans = Some(true),
      show_business_pro_plan = Some(true),

      ff_emails_sent_report = Some(true),


      show_campaign_tags = Some(true),
      allowed_for_new_google_api_key = Some(true),
      increase_email_delay = Some(true)
    )

    val organizationWithCurrentData = OrganizationWithCurrentData(
      id = input.org_id.id,
      name = "smartreach",
      owner_account_id = input.account_id.id,
      counts = orgCountData,
      settings = orgSettings,
      plan = orgPlan,

      is_agency = false,
      trial_ends_at = DateTime.now().plusDays(9),
      error = None,
      error_code = None,
      paused_till = None,

      errors = Seq(),
      warnings = Seq(),

      via_referral = false,

      org_metadata = orgMetadata

    )


    val account = Account(
      id = AccountUuid("account_uuid"),
      internal_id = input.account_id.id,
      email = "<EMAIL>",
      email_verification_code = None,
      email_verification_code_created_at = None,
      created_at = DateTime.now().minusDays(7),

      first_name = None,
      last_name = None,
      company = None,
      timezone = None,

      profile = accountProfileInfo,

      org_role = Some(OrganizationRole.OWNER),

      teams = Seq(teamAccount),
      account_type = AccountType.TEAM,
      org = organizationWithCurrentData,
      active = true,
      email_notification_summary = "",
      account_metadata = AccountMetadata(is_profile_onboarding_done = Some(true)),
      email_verified = true,
      signupType = None,
      account_access = AccountAccess(inbox_access = true),
      calendar_account_data = None
    )

    val campaignStepDataLinkedin = CampaignStepData.LinkedinInmailData(
      subject = Some("Linkedin Inmail Subject Test Campaign - 1"),
      body = "Linkedin Inmail Body Test Campaign - 1"
    )

    val campaignStepDataAutoManual = CampaignStepData.ManualEmailStep(
      subject = "Email Interagtion test",
      body = "Test"
    )

    //    val campaignStepDataAutoEmail = CampaignStepData.AutoEmailStep(
    //      subject = "Email Interagtion test",
    //      body = "Test"
    //    )

    val campaignStepVariantCreateOrUpdateEmail = CampaignStepVariantCreateOrUpdate(
      parent_id = 0,
      step_data = campaignStepDataAutoManual,
      step_delay = 0,
      notes = Some("Linkedin Inmail Step Note"),
      priority = Some(TaskPriority.High),
    )

    val campaignStepVariantCreateOrUpdateLinkedin = CampaignStepVariantCreateOrUpdate(
      parent_id = 0,
      step_data = campaignStepDataLinkedin,
      step_delay = 0,
      notes = Some("Auto Email Step"),
      priority = Some(TaskPriority.High),
    )
    val emailSetting = EmailSetting(
      id = Option(input.email_setting_id),
      uuid = None,
      org_id = input.org_id,
      owner_id = input.account_id,
      owner_uuid = AccountUuid("uuid"),
      team_id = input.team_id,
      team_uuid = TeamUuid("uuid"),
      message_id_suffix = "message",
      email = "email",
      email_address_host = "email",
      service_provider = EmailServiceProvider.GMAIL_API,
      domain_provider = None,  
      via_gmail_smtp = None,
      owner_name = "owner_name",
      sender_name = "sender_name",
      first_name = "first_name",
      last_name = "last_name",
      cc_emails = None,
      bcc_emails = None,
      smtp_username = None,
      smtp_password = None,
      smtp_host = None,
      smtp_port = None,
      imap_username = None,
      imap_password = None,
      imap_host = None,
      imap_port = None,
      oauth2_access_token = None,
      oauth2_refresh_token = None,
      oauth2_token_type = None,
      oauth2_token_expires_in = None,
      oauth2_access_token_expires_at = None,
      email_domain = None,
      api_key = None,
      mailgun_region = None,
      quota_per_day = 100,
      reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
      last_read_for_replies = None,
      latest_email_scheduled_at = None,
      error = None,
      error_reported_at = None,
      paused_till = None,
      signature = "signature",
      created_at = None,
      current_prospect_sent_count_email = 0,
      default_tracking_domain = "domain",
      default_unsubscribe_domain = "domain",
      rep_tracking_host_id = 1,
      tracking_domain_host = None,
      custom_tracking_domain = None,
      custom_tracking_cname_value = None,
      custom_tracking_domain_is_verified = None,
      custom_tracking_domain_is_ssl_enabled = None,
      rep_mail_server_id = 2,
      rep_mail_server_public_ip = "ip",
      rep_mail_server_host = "host",
      rep_mail_server_reverse_dns = None,
      min_delay_seconds = 2,
      max_delay_seconds = 2,
      tag = None,
      campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.NoRunningCampaign,
      show_rms_ip_in_frontend = false
    )

    for {

      updateLastReadForReplies <- Future.fromTry {

        logger.debug("\n\n\n\n" +
          "Updating Last Read For Replies" +
          "\n\n\n\n")
        emailSettingDAO.updateLastReadForReplies(
          emailSetting = emailSetting,
          lastReadForReplies = DateTime.now(),
          Logger = logger
        )
      }
      // create a campaign
      campaignWithStatsAndEmailEither: Either[CampaignCreationError, CampaignWithStatsAndEmail] <- {

        logger.debug("\n\n\n\n" +
          "campaignWithStatsAndEmailEither" +
          "\n\n\n\n")


        campaignService.createCampaign(
          orgId = input.org_id.id,
          accountId = input.account_id.id,
          teamId = input.team_id.id,
          taId = input.ta_id,
          data = createCampaignData,
          campaignSettings = Some(campaignSettings),
          permittedAccountIdsForEditCampaigns = Seq(input.account_id.id),
          ownerFirstName = "MultiChannelTestUtil"
        )
      }

      campaignWithStatsAndEmail: CampaignWithStatsAndEmail <- campaignWithStatsAndEmailEither match {
        case Left(err) =>
          println(err)
          Future.failed(new Exception(s"Campaign Not Created: $err"))

        case Right(campaignWithStatsAndEmail) =>
          println(campaignWithStatsAndEmailEither)
          Future.successful(campaignWithStatsAndEmail)
      }

      // add a prospect to the above campaign
      prospects: CreateOrUpdateProspectsResult <- Future.fromTry {
        println(campaignWithStatsAndEmail)
        prospectService.createOrUpdateProspects(
          ownerAccountId = input.account_id.id,
          teamId = input.team_id.id,
          listName = None,
          prospects = Seq(prospectCreateFormData),
          updateProspectType = UpdateProspectType.ForceUpdate,
          ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,

          doerAccount = account,
          prospectSource = None,
          prospectAccountId = None,

          campaign_id = Some(campaignWithStatsAndEmail.id),
          prospect_tags = None,
          ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
          deduplicationColumns = None,
          auditRequestLogId = None,

          SRLogger = logger
        )
      }

      createdVariant: Either[CreateCampaignStepVariantError, CampaignStepVariant] <- if (input.enable_email_scheduler) {

        logger.debug("\n\n\n\n" +
          "create Variant " +
          "\n\n\n\n")
        println(prospects)
        campaignStepService.createVariant(
          orgId = input.org_id.id,
          data = campaignStepVariantCreateOrUpdateEmail,
          teamId = input.team_id.id,
          userId = input.account_id.id,
          taId = input.ta_id,
          stepId = 0,
          campaignId = campaignWithStatsAndEmail.id,
          campaignHeadStepId = None
        )
      } else {
        campaignStepService.createVariant(
          orgId = input.org_id.id,
          data = campaignStepVariantCreateOrUpdateLinkedin,
          teamId = input.team_id.id,
          userId = input.account_id.id,
          taId = input.ta_id,
          stepId = 0,
          campaignId = campaignWithStatsAndEmail.id,
          campaignHeadStepId = None
        )
      }

      campaign: Campaign <- Future.successful(

        campaignDAO.findCampaignForCampaignUtilsOnly(
          id = campaignWithStatsAndEmail.id,
          teamId = TeamId(campaignWithStatsAndEmail.team_id)
        ).get
      )

      startedCampaign: Either[StartCampaignError, CampaignWithStatsAndEmail] <- campaignStartService.startCampaign(
        c = campaign,
        team = Some(teamAccount),
        org = organizationWithCurrentData,
        scheduleStartAt = None,
        scheduleStartAtTimeZone = Some(input.timezone),
        userId = input.account_id.id,
        teamId = input.team_id.id,
        Logger = logger
      )

      scheduledTask: ScheduleTasksData <- if (input.enable_email_scheduler) {

        logger.debug("\n\n\n\n" +
          "started Campaign " +
          "\n\n\n\n")

        println(startedCampaign)
        emailChannelScheduler.scheduleTasksForChannel(
          channelData = EmailChannelData(
            emailSettingId = input.email_setting_id.emailSettingId
          ),
          teamId = input.team_id.id,
          accountService = accountService,
          //accountDAO = accountDAO,
          emailNotificationService = emailNotificationService,
          campaignService = campaignService,
          campaignProspectDAO = campaignProspectDAO,
          campaignProspectService = campaignProspectService,
          campaignStepVariantDAO = campaignStepVariantDAO,
          campaignStepDAO = campaignStepDAO,
          srShuffleUtils = srShuffleUtils,
          emailServiceCompanion = emailServiceCompanion,
          templateService = templateService,
          taskDAO = taskDAO,
          taskService = taskService,
          campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
          campaignsMissingMergeTagService = campaignsMissingMergeTagService,
          srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
          mqWebhookCompleted = mqWebhookCompleted,
          accountOrgBillingRelatedService = accountOrgBillingRelatedService,
          srRollingUpdateCoreService = srRollingUpdateCoreService,
          calendarAppService = calendarAppService
        )
      } else {
        linkedinChannelScheduler.scheduleTasksForChannel(
          channelData = LinkedinChannelData(
            linkedinSettingId = input.campaign_linkedin_settings_data.linkedin_account_setting_id
          ),
          teamId = input.team_id.id,
          accountService = accountService,
          //accountDAO = accountDAO,
          emailNotificationService = emailNotificationService,
          campaignService = campaignService,
          campaignProspectDAO = campaignProspectDAO,
          campaignProspectService = campaignProspectService,
          campaignStepVariantDAO = campaignStepVariantDAO,
          campaignStepDAO = campaignStepDAO,
          srShuffleUtils = srShuffleUtils,
          emailServiceCompanion = emailServiceCompanion,
          templateService = templateService,
          taskDAO = taskDAO,
          taskService = taskService,
          campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
          campaignsMissingMergeTagService = campaignsMissingMergeTagService,
          srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
          mqWebhookCompleted = mqWebhookCompleted,
          accountOrgBillingRelatedService = accountOrgBillingRelatedService,
          srRollingUpdateCoreService = srRollingUpdateCoreService,
          calendarAppService = calendarAppService
        )
      }

      // This simulates the deletion of emails_scheduled entries and reverting campaign_prospects back to previous step
      deleteUnsent <- Future.fromTry {

        logger.debug("\n\n\n\n" +
          "deleteUnsent " +
          "\n\n\n\n")

        logger.info(" Before delete-unsent ")
        selectAndPublishForDeletionService.selectAndPublishForDeletion(
          deletion_reason = DeletionReason.Other(reason = "multichannel_util_testing"),
          deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnSentByEmailSettingIdNotManual(
            emailSettingData = Map(input.team_id -> Seq(input.email_setting_id)),
          )
        )
      }

      // Our worker which picks up campaigns from email-setting -ids doesn't picks up if last_read_for_replies
      // is greater than 15 minutes
      updateLastReadForReplies <- Future.fromTry {
        emailSettingDAO.updateLastReadForReplies(
          emailSetting = emailSetting,
          lastReadForReplies = DateTime.now(),
          Logger = logger
        )
      }

      emailChannelData = emailSetting.id.map(id => EmailChannelData(id.emailSettingId))
      // updating update_campaign_next_to_be_scheduled_at so that campaign will get picked by scheduler
      update_campaign_next_to_be_scheduled_at <- Future.fromTry {
        campaignDAO._updateNextToScheduledAt(
          campaignIdsWithNextToBeScheduledAt = Seq(
            CampaignDataToAddNextToBeScheduledAtForEmailChannel(
              campaignId = campaign.id,
              nextToBeScheduledAt =  DateTime.now().minusMinutes(2),
              sender_email_setting_id = emailSetting.id.get.emailSettingId,
              channelType = ChannelType.EmailChannel
            )),
          Logger = logger,
          team_id = input.team_id,
          is_scheduler_flow = false
        )
      }

      scheduledAgain: ScheduleTasksData <- if (input.enable_email_scheduler) {

        println("Second time scheduling the same campaign")

        logger.info("\n\n\n" +
          "Second time scheduling the same campaign" +
          "\n\n\n")
        println(startedCampaign)
        emailChannelScheduler.scheduleTasksForChannel(
          channelData = EmailChannelData(
            emailSettingId = input.email_setting_id.emailSettingId
          ),
          teamId = input.team_id.id,
          accountService = accountService,
          //accountDAO = accountDAO,
          emailNotificationService = emailNotificationService,
          campaignService = campaignService,
          campaignProspectDAO = campaignProspectDAO,
          campaignProspectService = campaignProspectService,
          campaignStepVariantDAO = campaignStepVariantDAO,
          campaignStepDAO = campaignStepDAO,
          srShuffleUtils = srShuffleUtils,
          emailServiceCompanion = emailServiceCompanion,
          templateService = templateService,
          taskDAO = taskDAO,
          taskService = taskService,
          campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
          campaignsMissingMergeTagService = campaignsMissingMergeTagService,
          srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
          mqWebhookCompleted = mqWebhookCompleted,
          accountOrgBillingRelatedService = accountOrgBillingRelatedService,
          srRollingUpdateCoreService = srRollingUpdateCoreService,
          calendarAppService = calendarAppService

        )
      } else {
        linkedinChannelScheduler.scheduleTasksForChannel(
          channelData = LinkedinChannelData(
            linkedinSettingId = linkedinAccountSettingId
          ),
          teamId = input.team_id.id,
          accountService = accountService,
          //accountDAO = accountDAO,
          emailNotificationService = emailNotificationService,
          campaignService = campaignService,
          campaignProspectDAO = campaignProspectDAO,
          campaignProspectService = campaignProspectService,
          campaignStepVariantDAO = campaignStepVariantDAO,
          campaignStepDAO = campaignStepDAO,
          srShuffleUtils = srShuffleUtils,
          emailServiceCompanion = emailServiceCompanion,
          templateService = templateService,
          taskDAO = taskDAO,
          taskService = taskService,
          campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
          campaignsMissingMergeTagService = campaignsMissingMergeTagService,
          srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
          mqWebhookCompleted = mqWebhookCompleted,
          accountOrgBillingRelatedService = accountOrgBillingRelatedService,
          srRollingUpdateCoreService = srRollingUpdateCoreService,
          calendarAppService = calendarAppService
        )
      }

      fetch_tasks <- {

        taskService.getAllTasksForUser(
          isFirst = true,
          team_id = input.team_id.id,
          orgId = input.org_id ,
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
            timeline = InferredQueryTimeline.Range.After(
              dateTime = DateTime.now().withTimeAtStartOfDay()
            ), pageSize = 25
          ),
          searchTask = SearchTask(
            assignee_ids = None,
            task_status = None,
            reply_sentiment_uuids = None,
            task_priority = None,
            campaign_ids = None,
            task_types = None,
            time_based_task_type = Some(TimeBasedTaskType.Today)
          ),
          timeZone = "Asia/Kolkata",
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = Seq(17)
        )
      }

      //       Deleting so that we can test again with same campaign name
      deletedTasks: Either[DeleteCampaignTasksError, Int] <- Future {
        println(scheduledTask)
        campaignService.deleteCampaignAndAssociatedTasks(
          campaignId = CampaignId(id = campaign.id),
          teamId = input.team_id
        )
      }

      deleteScheduledTask: Try[Int] <- Future {
        selectAndPublishForDeletionService.selectAndPublishForDeletion(
          deletion_reason = DeletionReason.Other(reason = "multichannel_util_testing"),
          deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteAllUnSentByEmailSettingId(
            emailSettingData = Map(input.team_id -> Seq(input.email_setting_id)),
          )
        )
      }

    } yield {
      fetch_tasks match {
        case Left(e) =>

          List()

        case Right(paginated_tasks) =>

          paginated_tasks.data

      }
    }

  }



  def sendManualEmailWithoutCampaignIntegrationTest(

                                                     data: SendNewManualEmailV3,
                                                     org_id: Long,
                                                     accountId: Long,
                                                     teamId: Long,
                                                     loggedinAccount: Account,
                                                     auditRequestLogId: String,
                                                     permittedAccountIds: Seq[Long],
                                                     version: String,
                                                     tiid: Option[String]

                                                   ):  Future[Either[SendNewEmailManuallyError, String]] = {


    for {

      sendNewEmailManually:  Either[SendNewEmailManuallyError, String] <- inboxV3Service.sendNewEmailManually(
        data: SendNewManualEmailV3,
        org_id: Long,

        accountId: Long,
        teamId: Long,
        loggedinAccount: Account,
        auditRequestLogId: String,
        permittedAccountIds: Seq[Long],
        version: String,
        tiid: Option[String]
      )


    }yield{

      sendNewEmailManually match {

        case Left(e) =>

          println(s"error ${e}")

        case Right(data) =>

          println(s"result = >  ${data}")


      }

      sendNewEmailManually

    }

  }

  def sendManualEmailWithCampaignIntegrationTest(

                                                     data: SendNewManualEmailV3,
                                                     org_id: Long,
                                                     accountId: Long,
                                                     teamId: Long,
                                                     loggedinAccount: Account,
                                                     auditRequestLogId: String,
                                                     permittedAccountIds: Seq[Long],
                                                     version: String,
                                                     tiid: Option[String]

                                                   ): Future[Either[SendNewEmailManuallyError, String]] = {


    for {



      sendNewEmailManually: Either[SendNewEmailManuallyError, String] <- inboxV3Service.sendNewEmailManually(
        data = data,
        org_id = org_id,

        accountId = accountId,
        teamId = teamId,
        loggedinAccount = loggedinAccount,
        auditRequestLogId = auditRequestLogId,
        permittedAccountIds = permittedAccountIds,
        version = version,
        tiid = tiid
      )


    } yield {

      sendNewEmailManually match {

        case Left(e) =>

          println(s"error ${e}")

        case Right(data) =>

          println(s"result = >  ${data}")


      }

      sendNewEmailManually

    }

  }


}
