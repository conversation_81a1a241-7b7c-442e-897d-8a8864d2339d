package utils.cache_utils

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.OrgId
import api.campaigns.services.CampaignId
import api.prospects.models.ProspectId
import api.triggers.{IntegrationModuleType, IntegrationType}
import api_layer_models.CurrencyType
import utils.cache_utils.model.{CacheIdKeyForLock, CacheIdKeyForSet}
import utils.uuid.dao.SrUuidDbTable
import utils.uuid.models.SrTenantId


// SrRedisKey value class
case class SrRedisKeyNamespace(namespace: String) extends AnyVal {

  override def toString: String = namespace

}

object SrRedisKeyRegister {

  private object Keys {
    
    /*
    * Note: Key names here Must Be Same as value
    * */

    val reply_senti = "reply_senti"

    val mx_tool_box_blacklist_check = "mx_tool_box_blacklist_check"

    val workflow_crm = "workflow_crm"

    val workflow_report = "workflow_report"

    val cp_timezones = "cp_timezones"

    val email_setting = "email_setting"
    
    val email_setting_tags = "email_setting_tags"

    val crm_setting_wf_attempt = "crm_setting_wf_attempt"

    val wf_attempt_set = "wf_attempt_set"

    val crm_setting_rate_limit = "crm_setting_rate_limit"

    val wf_attempt_rate_limit_set = "wf_attempt_rate_limit_set"

    val sr_res = "sr_res"

    val id2uuid = "id2uuid"

    val uuid2id = "uuid2id"
    val email_scheduler_campaign_lock = "email_scheduler_campaign_lock"

    val channel_limit_lock = "channel_limit_lock"

    val usd_exchange_rate_key = "usd_exchange_rate"

    val org_with_current_data = "org_with_current_data"

    val senderRotationStats = "senderRotationStats"
    val reportStatsCampaignIdKey = "reportStatsCampaignIdKey"

    val ts_rp = "ts_rp" // timeseries_report_data
    val top_reports = "top_reports"

    val crm_create_lock = "crm_create_lock"

    val scheduledEmailsStuckCountRedisKey = "email_sending_stuck_count"

  }

  val replySentimentKeyNamespace = SrRedisKeyNamespace(
    namespace = Keys.reply_senti
  )
  val mxToolBoxLockRedisCacheKey = SrRedisKeyNamespace(
    Keys.mx_tool_box_blacklist_check
  )

  val workflowCrmNamespace = SrRedisKeyNamespace(
    Keys.workflow_crm
  )

  val workflowReportNamespace = SrRedisKeyNamespace(
    Keys.workflow_report
  )
  val campaignTimezonesKeyNamespace = SrRedisKeyNamespace(
    Keys.cp_timezones
  )

  val emailSettingKeyNamespace = SrRedisKeyNamespace(
    Keys.email_setting
  )

  val emailSettingTagsKeyNamespace = SrRedisKeyNamespace(
    Keys.email_setting_tags
  )

  val workflowCRMSettingsForWorkFlowAttemptNamespace = SrRedisKeyNamespace(
    namespace = Keys.crm_setting_wf_attempt
  )

  val workFlowAttemptSetNamespace = SrRedisKeyNamespace(
    namespace = Keys.wf_attempt_set
  )


  val crmRateLimitNamespace = SrRedisKeyNamespace(
    namespace = Keys.crm_setting_rate_limit
  )

  val workFlowAttemptRateLimitSetNamespace = SrRedisKeyNamespace(
    namespace = Keys.wf_attempt_rate_limit_set
  )

  val emailSchedulerCampaignLockNamespace = SrRedisKeyNamespace(
    namespace = Keys.email_scheduler_campaign_lock
  )

  val srResourceCacheNamespace = SrRedisKeyNamespace(
    namespace = Keys.sr_res
  )

  val srIdToUuidNamespace = SrRedisKeyNamespace(
    namespace = Keys.id2uuid
  )

  val srUuidToIdNamespace = SrRedisKeyNamespace(
    namespace = Keys.uuid2id
  )

  val srChannelLimitLockNamespace = SrRedisKeyNamespace(
    namespace = Keys.channel_limit_lock
  )

  val UsdExchangeRateKeyNamespace = SrRedisKeyNamespace(
    namespace = Keys.usd_exchange_rate_key
  )

  val orgWithCurrentDataKeyNamespace = SrRedisKeyNamespace(
    namespace = Keys.org_with_current_data
  )
  val senderRotationStatsKeyNamespace = SrRedisKeyNamespace(
    namespace = Keys.senderRotationStats
  )
  val reportStatsCampaignIdKeyNamespace = SrRedisKeyNamespace(
    namespace = Keys.reportStatsCampaignIdKey
  )

  val timeseriesReportData = SrRedisKeyNamespace(
    namespace = Keys.ts_rp
  )

  val topReportsData = SrRedisKeyNamespace(
    namespace = Keys.top_reports
  )

  val crmCreateLock = SrRedisKeyNamespace(
    namespace = Keys.crm_create_lock
  )

  val scheduledEmailsStuckCountKeyNamespace = SrRedisKeyNamespace(
    namespace = Keys.scheduledEmailsStuckCountRedisKey
  )

}

object CacheKeyGen {

  private lazy val keyPrefix: String = AppConfig.redisKeyPrefix

  def getScheduledEmailsStuckCountKey = {
    s"$keyPrefix::${SrRedisKeyRegister.scheduledEmailsStuckCountKeyNamespace.namespace}"
  }

  def getCacheKeyForEmailSchedulerCampaignLock(campaignId: CampaignId): CacheIdKeyForLock = {
    CacheIdKeyForLock(s"$keyPrefix::${SrRedisKeyRegister.emailSchedulerCampaignLockNamespace.namespace}::${campaignId.id}")

  }

  def getUsdExchangeRateCacheKey(fromCurrency: CurrencyType): String = {

    s"$keyPrefix::${SrRedisKeyRegister.UsdExchangeRateKeyNamespace.namespace}::${fromCurrency.toString}"

  }

  def getCrmLockKeyForProspectId(
                                  prospectId: ProspectId,
                                  crmType: IntegrationType,
                                  teamId: TeamId,
                                  module: IntegrationModuleType
                                ): String = {

    s"$keyPrefix::${SrRedisKeyRegister.crmCreateLock.namespace}::${teamId.id}::${crmType.textId}::${module.toString}::${prospectId.id}"

  }

  def getOrgWithCurrentDataKey(orgId: OrgId): String = {

    s"$keyPrefix::${SrRedisKeyRegister.orgWithCurrentDataKeyNamespace.namespace}::${orgId.id}"

  }
  
  def getEmailSettingTagsCacheKey(teamId: TeamId): String = {
    s"$keyPrefix::${SrRedisKeyRegister.emailSettingTagsKeyNamespace.namespace}::team_${teamId.id}"
  }

  def get_key_for_cp_count_for_sender_rotation(campaignId:CampaignId,teamId:TeamId) = s"$keyPrefix::${SrRedisKeyRegister.senderRotationStatsKeyNamespace.namespace}::tid_${teamId.id}::${campaignId.id}"

  def reportStatsCampaignIdKey(campaignId: Long,teamId: Long) = s"$keyPrefix::${SrRedisKeyRegister.reportStatsCampaignIdKeyNamespace.namespace}::tid_$teamId::$campaignId"

  def getUuid2IdKey(
                     uuid: String,
                     tenantId: Option[SrTenantId],
                   ): String = {

    val namespace = SrRedisKeyRegister.srUuidToIdNamespace.namespace

    if (tenantId.isDefined) {

      // e.g. "prefix::uuid2id:org_12::acc_gh12312jfdfiuui"
      s"$keyPrefix::${namespace}::${tenantId.get.toString}::$uuid"

    } else {

      // e.g. "prefix::uuid2id::org_gh12312jfdfiuui"
      s"$keyPrefix::${namespace}::$uuid"

    }


  }

  def getId2UuidKey(
                     dbTable: SrUuidDbTable,
                     internalId: Long,
                     tenantId: Option[SrTenantId],
                   ): String = {

    val namespace = SrRedisKeyRegister.srIdToUuidNamespace.namespace

    if (tenantId.isDefined) {

      // e.g.: "prefix::id2uuid::accounts:org_12::5436"
      s"$keyPrefix::${namespace}::${dbTable.tableName}:${tenantId.get.toString}::$internalId"

    } else {

      // e.g.: "prefix::id2uuid::orgs::12"
      s"$keyPrefix::${namespace}::${dbTable.tableName}::$internalId"


    }
  }

}
