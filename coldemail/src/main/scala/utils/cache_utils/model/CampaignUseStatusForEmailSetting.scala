package utils.cache_utils.model

sealed trait CampaignUseStatusForEmailSetting

object CampaignUseStatusForEmailSetting {

  private val is_not_assigned_to_any_campaign = "is_not_assigned_to_any_campaign"

  private val has_running_campaign = "has_running_campaign"

  private val no_running_campaign = "no_running_campaign"

  case object IsNotAssignedToAnyCampaign extends CampaignUseStatusForEmailSetting {
    override def toString: String = is_not_assigned_to_any_campaign
  }
  case object HasRunningCampaign extends CampaignUseStatusForEmailSetting {
    override def toString: String = has_running_campaign
  }

  case object NoRunningCampaign extends CampaignUseStatusForEmailSetting {
    override def toString: String = no_running_campaign
  }

}