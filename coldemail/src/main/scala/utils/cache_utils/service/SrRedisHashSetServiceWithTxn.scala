package utils.cache_utils.service

import api.CacheServiceJedis
import redis.clients.jedis.{Pipeline, Response, Transaction}
import utils.SRLogger
import utils.cache_utils.model.{CacheIdKeyForLock, CacheIdKeyForSet}

import java.lang
import scala.util.{Failure, Success, Try}

class SrRedisHashSetServiceWithTxn(
                                     cacheServiceJedis: CacheServiceJedis
                                   ){

  def setHashNxWithTransaction[T](
                                   cacheKey: CacheIdKeyForSet,
                                   additionalSetData: Set[CacheIdKeyForLock],
                                   value: T
                                 )(implicit tjs: play.api.libs.json.Writes[T], logger: SRLogger): Try[Set[String]] = {

    val result = cacheServiceJedis.withJedisTxn {implicit transaction =>
      additionalSetData.map{ field =>
        cacheServiceJedis.hsetnxWithTransaction(
          key = cacheKey.id,
          field = field.id,
          value = value
        )
    }

    }
    result match {
      case Success(value) => Success(value._2.toSet)
      case Failure(exception) =>
        logger.fatal(s"Failed to add data from hash", exception)
        Failure(exception)
    }

  }


  def deleteHashWithTransaction(
                                 setKey: CacheIdKeyForSet,
                                 itemsToRemoveFromSet: Set[CacheIdKeyForLock]
                               )(using Logger: SRLogger): Try[Set[String]] = {
    val deleteData = cacheServiceJedis.withJedisTxn { implicit transaction =>
      itemsToRemoveFromSet.map{ cacheKey =>
        cacheServiceJedis.hdelWithTransaction(
          key = setKey.id,
          field = cacheKey.id,
        )
      }
    }

    deleteData match {
      case Success(value) => Success(value._2.toSet)
      case Failure(exception) =>
        Logger.fatal(s"Failed to delete data from hash", exception)

        Failure(exception)
    }


  }


  def getHashKeys(
                   cacheKey: CacheIdKeyForSet
                 )(using logger: SRLogger): Try[Set[CacheIdKeyForLock]] = {


    cacheServiceJedis.hkeys(
      key = cacheKey.id
    ) match {

      case Failure(e) =>

        logger.fatal(s"getHashKeys :: cacheKey: $cacheKey: idData: ${cacheKey.id}", err = e)
        Failure(e)

      case Success(result) =>
        Success(result.map(id => CacheIdKeyForLock(id)))
    }

  }
}