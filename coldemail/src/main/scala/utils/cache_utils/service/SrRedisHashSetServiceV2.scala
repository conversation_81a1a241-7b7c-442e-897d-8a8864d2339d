package utils.cache_utils.service

import api.CacheServiceJedis
import redis.clients.jedis.{Pipeline, Transaction}
import utils.SRLogger
import utils.cache_utils.model.{CacheIdKeyForLock, CacheIdKeyForSet}

import scala.util.{Failure, Success, Try}

class SrRedisHashSetServiceV2 (
                                cacheServiceJedis: CacheServiceJedis
                              ){

  def setHashNx[T](
                    cacheKey: CacheIdKeyForSet,
                    field: CacheIdKeyForLock,
                    value: T,
                  )(implicit tjs: play.api.libs.json.Writes[T], logger: SRLogger): Try[Long] = {


    cacheServiceJedis.hsetnx(
      key = cacheKey.id,
      field = field.id,
      value = value,
    ) match {

      case Failure(e) =>

        logger.fatal(s"setHashNxWithTransaction :: cacheKey: ${cacheKey.id}", err = e)
        Failure(e)

      case Success(value) =>
        if (value == 0) {
          logger.warn(s"field : cacheKey: ${cacheKey.id}: $field already existed")
        }
        //do Nothing
        Success(value)
    }
  }

  def deleteHash(
                  cacheKey: CacheIdKeyForSet,
                  field: String,
                )(using Logger: SRLogger): Try[Long] = {

    cacheServiceJedis.hdel(
      key = cacheKey.id,
      field = field
    ) match {
      case Success(value) =>
        Logger.debug(s"deleteHashWithTransaction: Success! Removed field $field from key ${cacheKey.id}")
        Success(value)
      case Failure(exception) =>
        Logger.debug(s"deleteHashWithTransaction: Failed! to removed field $field from key ${cacheKey.id}")

        Failure(exception)
    }

  }

  def getHashKeys(
                   cacheKey: CacheIdKeyForSet
                 )(using logger: SRLogger): Try[Set[CacheIdKeyForLock]] = {


    cacheServiceJedis.hkeys(
      key = cacheKey.id
    ) match {

      case Failure(e) =>

        logger.fatal(s"getHashKeys :: cacheKey: $cacheKey: idData: ${cacheKey.id}", err = e)
        Failure(e)

      case Success(result) =>
        Success(result.map(id => CacheIdKeyForLock(id)))
    }

  }

}