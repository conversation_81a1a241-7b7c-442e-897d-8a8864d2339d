package utils.cache_utils.service

import utils.SRLogger
import utils.helpers.LogHelpers

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

object ApiFallbackToCacheUtil {

  private def getFromApiAndSetToCache[T](

    getFromApi: => Future[Option[T]],

    setToCache: => T => Try[true]

  )(
    implicit logger: SRLogger,
    ec: ExecutionContext
  ): Future[Option[T]] = {

    getFromApi
      .map {

        case None =>

          None

        case Some(res) =>

          setToCache(res) match {

            case Failure(exception) =>

              logger.error(
                msg = s"setCacheError: ${LogHelpers.getStackTraceAsString(exception)}"
              )

              Some(res)

            case Success(_) =>

              Some(res)

          }

      }
      .recoverWith { case e =>

        logger.error(msg = "Failed to fetch data from API", err = e)

        Future.failed(e)

      }

  }


  final def findFromCacheFallbackToApi[T](

    getFromCache: => Try[Option[T]],

    getFromApi: => Future[Option[T]],

    setToCache: => T => Try[true]

  )(
    implicit logger: SRLogger,
    ec: ExecutionContext
  ): Future[Option[T]] = {

    getFromCache match {

      case Failure(exception) =>

        logger.error(msg = s"getFromCacheError: ${LogHelpers.getStackTraceAsString(exception)}")

        getFromApiAndSetToCache(
          getFromApi = getFromApi,
          setToCache = setToCache
        )

      case Success(resOptFromCache) =>

        resOptFromCache match {

          case None =>

            getFromApiAndSetToCache(
              getFromApi = getFromApi,
              setToCache = setToCache
            )

          case Some(resFromCache) =>

            Future.successful(Some(resFromCache))

        }

    }

  }

}
