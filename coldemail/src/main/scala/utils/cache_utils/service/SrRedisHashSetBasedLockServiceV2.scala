package utils.cache_utils.service

import redis.clients.jedis.Transaction
import utils.cache_utils.model.{CacheIdKeyForLock, CacheIdKeyForSet}
import utils.{Helpers, SRLogger}

import scala.util.Try

class SrRedisHashSetBasedLockServiceV2 (
                                         srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2,
                                         srRedisHashSetServiceV2: SrRedisHashSetServiceV2
                                       ) {



  private def updateHashMap(
                             additionalSetData: Set[CacheIdKeyForLock],
                             setKey: CacheIdKeyForSet
                           )(using Logger: SRLogger): Try[Set[CacheIdKeyForLock]] = {

    val setOfTry: Set[Try[Seq[CacheIdKeyForLock]]] = additionalSetData.map(field => {
      srRedisHashSetServiceV2.setHashNx(
        cacheKey = setKey,
        field = field,
        value = "1",
      ) map(value => {
        if(value == 0) {
//          Logger.fatal(s"updateHashMap: Trying to add to the set but it already exists id: $setKey  field : $field")
          Seq()
        } else {
          Logger.debug(s"updateHashMap: added to the set: $setKey  field : $field")
          Seq(field)
        }
      })
    }) //updating the set and adding the new element

    Helpers.seqTryToTrySeq(setOfTry.toSeq).map(_.flatten.toSet)

  }

  private def getHashMap(
                          setKey: CacheIdKeyForSet
                        )(using Logger: SRLogger): Try[Set[CacheIdKeyForLock]] = {

    srRedisHashSetServiceV2.getHashKeys(
      cacheKey = setKey
    ) //getting the set

  }


  // This call will be made after successfully firing the attempts and we now want to release the lock on the CRM
  def removeFromHashMap(
                         itemsToRemoveFromSet: Set[CacheIdKeyForLock],
                         setKey: CacheIdKeyForSet,
                       )(using Logger: SRLogger): Try[Set[Boolean]] = {

    val result = for {

      deleteFromHash: Set[Boolean] <- {

        // todo: this .map -> we can get rid off
        val result: Set[Try[Boolean]] = itemsToRemoveFromSet.map(item => {

          for {

            _: Long <- srRedisHashSetServiceV2.deleteHash(
              cacheKey = setKey,
              field = item.id,
            )

            releaseLock: Boolean <- srRedisSimpleLockServiceV2.releaseLock(
              cacheKey = item,
            )

          } yield {
            releaseLock
          }
        })

        Helpers.seqTryToTrySeq(result.toSeq).map(_.toSet)
      }

    } yield {
      deleteFromHash
    }

    result

  }

  /*
  * This fn is called after
      we have gone to the redis ongoing attempts directory (which is a hashmap) and found which
         CRMs calls are in progress and hence those are locked and therefore have now gone to the DB
         - saying - get me some attempts which are not currently ongoing and so
      We have to acquire the locks on these new attempts to be made and lock them and make the entry into directory of ongoing attempts
  * */
  def acquireLockAndAddToSet(
                              additionalKeysToLock: Set[CacheIdKeyForLock],
                              setKey: CacheIdKeyForSet,
                              expireInSeconds: Int = srRedisSimpleLockServiceV2.lockDefaultExpiresInSeconds
                            )(using Logger: SRLogger): Try[Set[CacheIdKeyForLock]] = {

    //    hashSetLockService.withJedisTxn { transaction =>

    val result = for {

      acquireLock: Set[CacheIdKeyForLock] <- {
        val setOfTry: Set[Try[Seq[CacheIdKeyForLock]]] = additionalKeysToLock.map(additionalKeyToLock => {

          srRedisSimpleLockServiceV2
            .acquireLock(cacheKey = additionalKeyToLock,
              expireInSeconds = expireInSeconds
              //                  transaction = transaction
            )
            .map(bool => if (bool) {
              Seq(additionalKeyToLock)
            } else Seq())

        }) // adding the lock

        Helpers.seqTryToTrySeq(setOfTry.toSeq).map(_.flatten.toSet)
      }

      addingToSet: Set[CacheIdKeyForLock] <- updateHashMap(
        additionalSetData = acquireLock,
        setKey = setKey
        //            transaction = transaction,
      )

    } yield {
      acquireLock
    }

    result

    //    }
    //      .flatten

  }

  def acquireAndCheckSingleLock(

                                 additionalKeyToLock: CacheIdKeyForLock,
                                 setKey: CacheIdKeyForSet,

                                 expireInSeconds: Int = srRedisSimpleLockServiceV2.lockDefaultExpiresInSeconds

                               )(using Logger: SRLogger): Try[Boolean] = {

    for {
      _: Set[CacheIdKeyForLock] <- acquireLockAndAddToSet(

        additionalKeysToLock = Set(additionalKeyToLock),
        setKey = setKey,
        expireInSeconds = expireInSeconds

      )

      check: Boolean <- checkLock(
        lockKey = additionalKeyToLock
      )

    } yield {

      check

    }

  }

  def checkLock(
                 lockKey: CacheIdKeyForLock
               )(using Logger: SRLogger): Try[Boolean] = {
    srRedisSimpleLockServiceV2.checkLock(lockKey) //checking the lock
  }


  def getLockedKeysFromSet(
                            setKey: CacheIdKeyForSet
                          )(using Logger: SRLogger): Try[Set[CacheIdKeyForLock]] = {
    for {

      keys: Set[CacheIdKeyForLock] <- getHashMap(setKey = setKey)


      keysStatus: Set[(CacheIdKeyForLock, Boolean)] <- {
        val seqOfTry: Set[Try[(CacheIdKeyForLock, Boolean)]] = keys.map(k => {

          checkLock(lockKey = k).map(b => (k, b))

        })
        Helpers.seqTryToTrySeq(seqOfTry.toSeq).map(_.toSet)

      }

      validOnes: Set[(CacheIdKeyForLock, Boolean)] <- Try {
        keysStatus.filter(_._2)
      }

      falseOnes: Set[(CacheIdKeyForLock, Boolean)] <- Try {
        keysStatus.filterNot(_._2)
      }

      _: Set[Boolean] <- removeFromHashMap(falseOnes.map(_._1), setKey)

    } yield {

      validOnes.map(_._1)
    }

  }
}
