package utils.cache_utils

import api.CacheServiceJedis
import utils.SRLogger

import scala.util.{Failure, Success}

trait SimpleCacheServiceTrait[CacheIdData, CachedValue]
  extends SrCacheKeyTrait[CacheIdData] {

  protected val cacheServiceJedis: CacheServiceJedis

  private val logBase: String = s"SimpleCacheService $redisKeyNamespace"

  val defaultExpiresInSeconds: Int

  def set(
    value: CachedValue,
    idData: CacheIdData,
    expireInSeconds: Int = defaultExpiresInSeconds
  )(
    implicit tjs: play.api.libs.json.Writes[CachedValue],

    logger: SRLogger

  ): Unit = {

      cacheServiceJedis.set(
        key = getCacheKey(data = idData),
        value = value,
        expireInSeconds = expireInSeconds
      )

     match {

      case Failure(e) =>

        logger.error(s"$logBase set :: idData: $idData", err = e)

      case Success(_) =>
      // do nothing
    }
  }

  def get(
    idData: CacheIdData
  )(

    implicit rds: play.api.libs.json.Reads[CachedValue],
    logger: SRLogger

  ): Option[CachedValue] = {


    cacheServiceJedis.get[CachedValue](

      key = getCacheKey(data = idData)

    ) match {

      case Failure(e) =>

        logger.error(s"$logBase get :: idData: $idData", err = e)

        delete(idData = idData)

        None

      case Success(cachedValueOpt) =>

        cachedValueOpt
    }

  }

  def delete(

    idData: CacheIdData

  )(
    implicit logger: SRLogger

  ): Unit = {

    cacheServiceJedis.deleteKey(

      key = getCacheKey(data = idData)

    ) match {

      case Failure(e) =>

        logger.fatal(s"$logBase delete :: idData: $idData", err = e)


      case Success(_) =>
      // do nothing
    }
  }

}
