package utils.appentrypoints.workers

import common_auth_middleware.UserRedisKeyService_DI
import utils.Helpers
import utils.dependencyinjectionutils.Calendar.{CalendarAppApiDI, CalendarAppDaoDI, CalendarAppServiceDI}
import utils.dependencyinjectionutils.Intercom.{MQReadOldEmailForSync_DI, MqCampaignSpamMonitorService_DI, OldEmailSyncService_DI}
import utils.dependencyinjectionutils.SmsSettingDI.SmsSettingDAODI
import utils.dependencyinjectionutils.*
import utils.dependencyinjectionutils.BillingV2.BillingAppServiceDI
import utils.dependencyinjectionutils.Campaign.{CampaignStepServiceDI, CampaignTestStepServiceDI}
import utils.dependencyinjectionutils.ChannelSchedulers.{EmailMessageDataDAO_DI, EmailScheduledDAOService_DI, SrRedisSimpleLockServiceV2_DI}
import utils.dependencyinjectionutils.DripHealthCheck.*
import utils.dependencyinjectionutils.LinkedinSettingDI.{LinkedinSettingServiceDI, MqCaptainDataCookieFailureConsumerDI, MqCaptainDataCookieFailurePublisherDI, PlanLimitServiceDI}
import utils.dependencyinjectionutils.ProductOnboardingDI.RollingUpdate.SrRollingUpdateCoreService_DI
import utils.dependencyinjectionutils.Tasks.{TaskDAO_DI, TaskDaoServiceDI, TaskServiceDI}
import utils.dependencyinjectionutils.Team.*
import utils.mq.channel_scheduler.MqCampaignSchedulingMetadataMigration
import utils.mq.services.MQConfig
import utils.mq.spammonitor.MQSpamMonitor

import java.util.concurrent.Executors
import scala.concurrent.ExecutionContext
import scala.util.Try

class MainMQWorkersLoader
  extends MQOpenTrackerDI
    with EmailValidationDAOService_DI
    with MQClickTrackerDI
    with CampaignProspectStepScheduleLogsDAO_DI
    with PhantomBusterAgentServiceDI
    with DeBounceEmailValidationApiDI
    with ListCleanEmailValidationApiDI
    with UserCheckApiServiceDI
    with PlanLimitServiceDI
    with CloudStorageDI
    with OrganizationBillingDAO_DI
    with SrMailServerServiceDI
    with SrMailServerDao_DI
    with SrMailServerRedisService_DI
    with SrDateTimeUtils_DI
    with LlmAuditLogDAO_DI
    with GptApiService_DI
    with AiContentGenerationProcessor_DI
    with AIHyperPersonalizedGeneratorDI
    with MQActivityTriggerPublisher_DI
    with GeneralModuleServiceDI
    with MQServerReportCheck_DI
    with MqResetCacheForReportDI
    with ReportDaoJedisServiceDI
    with MQCampaignAISequenceGeneratorDI
    with MqUpdateLatestTaskDoneOrSkippedAtDI
    with GeneralModuleDI
    with CreateInCRMJedisDAO_DI
    with SrRedisSimpleLockServiceV2_DI
    with GeneralSettingServiceDI
    with PhantomBusterProxyDAO_DI
    with BrightDataServiceDI
    with BrightDataApiDI
    with AccountOrgBillingRelatedInfoDAODI
    with AccountOrgBillingRelatedServiceDI
    with MqLimitValidatorDI
    with MqLimitValidatorPublisherDI
    with PhantomBusterProxyServiceDI
    with MqInsertTeamsMetadataDI
    with TeamsMetadataServiceDI
    with TeamsMetadataDAODI
    with PhantomBusterAgentsDAODI
    with LinkedinSessionCreatorDI
    with ProspectTagServiceDI
    with PhantomBusterExecutionLogsDAO_DI
    with PhantomBusterExecutionLogsServiceDI
    with MqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC_DI
    with BlacklistServiceV2_DI
    with PhantomBusterApiKeysServiceDI
    with PhantomBusterApiKeysDAODI
    with ProspectTagDAO_DI
    with MqRefactorLinkedinUrlMigration_DI
    with CampaignProspectService2DI
    with SrUuidUtilsDI
    with LinkedinTaskServiceDI
    with MQUnsubscriberTrackerDI
    with MQDoNotContactDI
    with MQDoNotContactPublisher_DI
    with MQProspectDeleterDI
    with MQWebhookEmailInvalidDI
    with LinkedinMessagesServiceDI
    with LinkedinMessagesDAODI
    with LinkedinMessageThreadsServiceDI
    with LinkedinConnectionsServiceDI
    with LinkedinConnectionDAODI
    with LinkedinMessageThreadsDAODI
    with EmailSettingDAOServiceDI
    with EmailSettingJedisServiceDI
    with LinkedinSettingServiceDI
    with EmailSettingTagsJedisServiceDI
    with MqCaptainDataConversationExtractionPublisher_DI
    with MqCaptainDataCookieFailurePublisherDI
    with MqCaptainDataConversationExtractionConsumer_DI
    with MqCaptainDataMessageExtractionPublisher_DI
    with MqCaptainDataMessageExtractionConsumer_DI
    with MqCaptainDataCookieFailureConsumerDI
    with CaptainDataServiceDI
    with Template.TemplateUtilsDI
    with CampaignStepServiceDI
    with GPTServiceDI
    with AISequenceGeneratorDI
    with SrRateLimiterDI
    with SrRateLimiterDAO_DI
    with TemplateDAO_DI
    with GPTApiDI
    with MQWebhookCompletedDI
    with ChannelSchedulers.LinkedinChannelSchedulerDI
    with LinkedinSettingDAO_DI
    with CampaignDAOService_DI
    with SrRandomUtilsDI
    with SrShuffleUtilsDI
    with CampaignSendReportsDAO_DI
    with RolePermissionDataDAOV2_DI
    with MQRepliedEventDI
    with CampaignProspectTimezonesJedisServiceDI
      with ProspectBatchActionServiceDI
    with ApiService_DI
    with BlacklistApiService_DI
    with CallApiService_DI
    with TeamsApiService_DI
      with CampaignApiService_DI
      with ProspectEventDAOService_DI
      with CampaignProspectAssignDI
    with ProspectApiService_DI
    with ProspectEventService_DI
    with MQProspectCompletedEventDI
    with MQProspectUnPauseEventDI
    with MQListStatsUpdaterDI
//    with ScyllaCluster_DI
//    with ScyllaDbConnection_DI
    with ReplyFilteringServiceDI
    with EmailReplyTrackingModelV2DI
    with MqSrAiApiPublisher_DI
    with MqSrAiApiConsumer_DI
    with CampaignTemplateServiceDI
    with EmailServiceDI
    with OutlookApiSendEmailServiceDI
    with OutlookApiReplyTrackingServiceDI
    with GmailEmailApiDI
    with GmailApiReplyTrackingServiceDI
    with GmailApiSendEmailServiceDI
//    with CacheEventScyllaService_DI
//    with CacheEventScyllaDAO_DI
    with GmailEmailSendServiceDI
    with GmailSmtpEmailApiDI
    with OutlookEmailApiDI
//    with ScyllaRunSafely_DI
    with MicrosoftOAuthDI
    with SendGridServiceDI
    with SendGridEmailApiDI
    with SmtpImapSendEmailServiceDI
    with SmtpImapReplyTrackingServiceDI
    with SmtpImapEmailApiDI
    with GoogleOAuthDI
    with EmailHandleErrorServiceDI
    with MqCampaignSchedulingMetadataMigration_DI
//    with MQMBLEmailValidatorDI
    with MqIPBlacklistCheckDI
    with WebhookDAO_DI
    with WebhookUtils_V2_DI
    with ProspectQueryDI
    with AccountDAO_DI
    with SrUserFeatureUsageEventServiceDI
    with MqOrgDataDeleterDI
    with MigrationUtils_DI
    with MigrationDAO_DI
    with MigrationService_DI

    with BlacklistServiceDI
    with BlacklistDAO_DI

    with RepTrackingHostServiceDI


    with MQTriggerDI
    with ProspectAddEventDAO_DI
    with CampaignProspectDAO_DI
    with DBCounterDAO_DI
    with SrDBQueryCounterService_DI
    with CampaignDAO_DI
    with TriggerDAO_DI
    with EmailScheduledDAO_DI
    with EmailScheduledServiceDI
    with EmailSettingDAO_DI
    with ProspectUpdateCategoryTemp_DI
    with ProspectUpdateCategoryTemp2_DI
    with EmailThreadDAO_DI
    with EmailReplyTrackingModelDI
    with TriggerServiceDI
    with HandlePushTriggerEventServiceDI
    with HandleSyncTriggerEventServiceDI
    with LeadStatusServiceDI
    with SyncTriggerMQServiceDI
    with HandleActivityTriggerEventServiceDI
    with ActivityTriggerMQServiceDI
    with HandleAddToDNCTriggerEventServiceDI
    with CampaignCacheServiceDI
    with CampaignsMissingMergeTagServiceDI
    with AgencyDashboardCacheServiceDI
    with CacheServiceJedisDI
    with EmailNotificationServiceDI
    with EmailNotificationDAO_DI
    with MailgunServiceDI
    with MailgunEmailApiDI
    with AccountServiceDI
    with SrResourceDI.SrResourceDaoServiceDI
            with SrResourceDI.SrResourceWithoutTenantDaoServiceDI
    with SrResourceDI.SrCacheDI
    with SrUuidInternalDataServiceDI
    with ProspectServiceDI
    with CampaignProspectServiceDI
    with DripLogJedisDAO_DI
    with CampaignServiceDI
    with InternalSchedulerRunLogDAO_DI
    with CampaignSendingVolumeLogsDAO_DI
    with TransformAndSaveEmailValidationResultDI
    with EmailValidationBatchRequestModelDI
    with EmailValidationModelDI
    with EmailValidationApiToolsRecordDAODI
    with EmailDeliveryAnalysisServiceDI
    with WorkerActorSystemDI
    with WorkerWSClientDI
    with WorkerActorMaterializerDI
    with ProspectAccountDAO1DI
    with AccountDAOV2_DI
    with OrganizationDAO_DI
    with FreeEmailDomainListService_DI
    with FreeEmailDomainListDAO_DI

    with EventLogDAO_DI
    with EventLogService_DI
    with EventLogFindServiceDI
   with WorkflowAttemptDAO_DI
   with WorkFlowAttemptService_DI
    with AccessTokenService_DI
    with ClientAccountAccessLogDAO_DI
    with UserRedisKeyService_DI
    with EmailValidationServiceDI
    with BouncerEmailValidationApiDI
    with TagServiceDI
    with ProspectUploadServiceDI
    with SRCsvParserUtilsDI
    with CustomCSVParserDI
    with UnivocityParserDI
    with SRCsvReaderUtilsDI
    with ProspectColumnDef_DI
    with ProspectColumnDefDAO_DI
    with ProspectsEmailsDAO_DI
    with ProspectServiceV2_DI
    with ResetUserCacheUtilDI
    with AccountDAOService_DI
    with CacheServiceDI
    with EmailMessageContactModel_DI
    with TeamsDAO_DI
    with DBUtils_DI
    with EmailSendingStatusDAO_DI
    with SrInternalFeatureUsageDaoDI
    with BlacklistProspectCheckDAO_DI
    with CampaignProspectDAO_2DI
    with GenerateTempId_DI
    with Intercom.MqIntercomUpdaterDI
    with Intercom.IntercomUpdateServiceDI
    with Intercom.IntercomApiDI
    with TemplateServiceDI
    with EmailServiceCompanionDI
    with CampaignStepVariantDAO_DI
    with CampaignStepDAO_DI
    with CampaignEditedPreviewEmailDAO_DI
    with PhishingCheckServiceDI
    with MqCampaignSpamMonitorService_DI
    with SpamMonitorService_DI
    with DomainInfoWhoisDAO_DI
    with DomainDataService_DI
    with ApiLayerAPIService_DI
    with RapidAPIService_DI
    with EmailSendingStatusService_DI
    with SupportAccessToUserAccountDAO_DI
    with MqUnSnoozeSnoozedTasks_DI
    with TaskDAO_DI
    with MqUpdateSubAccountCallingCredit_DI
    with CallServiceDI
    with PusherServiceDI
    with TwilioDialerServiceDI
    with MqWhatsAppChannelScheduler_DI
    with WhatsAppChannelSchduler_DI
    with WhatsappSettingDI.WhatsappSettingDAODI
    with TaskServiceDI
    with TaskDaoServiceDI
//    with Tasks.TaskCacheDAO_DI
//    with Tasks.TaskCacheService_DI
//    with EventFramework.SrEventServiceDI
//    with EventFramework.KafkaServiceDI
    with MQGeneralChannelSchedulerDI
    with ChannelSchedulers.GeneralChannelSchedularDI
    with GeneralSettingDAO_DI
    with MQLinkedinChannelSchedulerDI
    with MqUpdateCampaignSchedulingMetadata_DI
    with MQReadOldEmailForSync_DI
    with TeamInboxDAO_DI
    with OldEmailSyncService_DI
    with MqCampaignStepContentCheckServiceDI
    with ReplyTrackerService_DI
    with MqHandleEventLogDI
    with EmailReplyTrackingDAOService_DI
    with ReplySentimentService_DI
    with ReplySentimentLLMDAO_DI
    with ReplySentimentDAO_DI
    with ReplySentimentJedisDAO_DI
    with MergeTagService_DI
    with ProspectDAOService_DI
    with ProspectDAOServiceV2_DI
    with CampaignProspectDAOService_DI
    with OrganizationDAOService_DI
    with ProspectDAO_DI
    with ReplySentimentDAOService_DI
    with AssociateProspectToEmailThreadService_DI
    with MQAssociateProspectToOldEmails_DI
    with HubspotApiDI
    with HubSpotOAuth_DI
    with PipedriveOAuth_DI
    with SalesforceOAuth_DI
    with ZohoOAuth_DI
    with ZohoApiDI
    with ZohoRecruitOAuth_DI
    with SalesForceApiDI
    with PipedriveApiDI
    with IntegrationTypeService_DI
    with SRTriggerAllowedCombos_DI
    with TriggerServiceV2_DI
    with TriggerJedisDAO_DI
    with TriggerDAOService_DI
    with TIntegrationCRMService_DI
    with InboxV3ServiceDI
    with LinkedinMessagesDAOService_DI
    with EmailSettingService_DI
    with MqCampaignSendReportDI
    with ProspectEmailsDAOService_DI
    with MqPreEmailValidationDI
    with TeamServiceDI
    with TeamDAOService_DI
    with TeamSRAIFlagsJedisDAO_DI
    with SrUuidServiceDI
    with MqSmsChannelScheduler_DI
    with SmsChannelScheduler_DI
    with SmsSettingDAODI
    with CampaignSchedulingMetadataDAO_DI
    with MqCallChannelScheduler_DI
    with CallChannelScheduler_DI
    with CallDAO_DI
    with EmailThreadDAOService_DI 
    with InboxV3DAOService_DI
    with MqInternalAdoptionReportUpdaterDI
    with InternalAdoptionReportUpdateServiceDI
    with OrgMetadataDAO_DI
    with OrgMetadataServiceDI
    with MqEmailAccountDeleterDI
    with MQNewProcessWorkflowAttemptService_DI
    with ProcessAttemptService_DI
    with MqInternalTeamBasedMigration_DI
    with WorkFlowAttemptJedisDAOService_DI
    with CRMSettingsForWorkFlowAttemptJedisDAO_DI
    with WorkFlowAttemptSetJedisDAO_DI
    with WorkflowJedisService_DI
    with WorkflowRateLimitJedisDAOService_DI
    with WorkflowAttemptRateLimitSetJedisDAO_DI
    with CRMSettingsRateLimitJedisDAO_DI
    with MqExecuteDueLinkedinTasksDI
    with PhantomBusterServiceDI
    with PhantomBusterApiDI
    with CaptainDataApiDI
    with Campaign.ChannelSettingServiceDI
    with MQUnsnoozeSnoozedEmails_DI
    with EmailMessageDataDAO_DI
    with MqUuidMigrationScript_DI
    with MqUpdatedAtMigrationScript_DI
    with MigrationFuncDI
    with CampaignEmailSettingsDAO_DI
    with ApiLayerServiceDI
    with ApiLayerDAO_DI
    with MqStopInactiveCampaign_DI
    with ProspectTagDAOLegacy_DI
    with CallUtilsDI
    with EmailsScheduledDeleteService_DI
    with SelectAndPublishForDeletionServiceDI
    with MqCampaignsProspectsMigrationScript_DI
    with CallHistoryLogDAO_DI
    with MqUpdateSubAccountCallHistory_DI
    with OpportunitiesDefaultSetupService_DI
    with PipelineDAO_DI
    with OpportunityStatusDAO_DI
    with MqOpportunitiesDefaultSetupMigration_DI
    with CalendarAppServiceDI
    with CalendarAppDaoDI
    with CalendarAppApiDI
    with TeamsPipelineConfigDAO_DI
    with TeamsPipelineConfigService_DI
    with OrganizationService_DI
    with TeamInboxService_DI
    with LinkedinTeamInboxDAO_DI
    with OrganizationJedisCacheDao_DI
    with DeleteService_DI
    with TeamsListingPaginationService_DI
    with SrRollingUpdateCoreService_DI
    with MqStuckCampaignAddLogAndReport_DI
    with MqDoNotContactType_DI
    with DncTypeMigrationFunc_DI
    with EmailScheduledDAOService_DI
    with MqDeletionAndRevertV2DI
    with MqDeletionAndRevertV2Publisher_DI
    with SchedulerIntegrityServiceDI
    with SchedulerIntegrityReportDaoDI
    with MqSchedulerIntegrityCheckDI
    with CampaignStepDaoServiceDI
    with MqUpdateUuidMigrationScript_DI
    with CampaignTestStepServiceDI
    with EmailSenderServiceDI
    with OutlookUtilsService_DI
    with InboxPlacementCheckDAO_DI
    with PotentialDuplicateProspectService_DI
    with PotentialDuplicateProspectsDAO_DI
    with ProspectCategoryServiceDI
    with MqPotentialDuplicateProspects_DI
    with EmailAccountServiceDI
    with EmailAccountTestServiceDI
    with MqZapmailEmailAccountCreationPublisher_DI
    with MqZapmailEmailAccountCreationConsumer_DI
    with EmailInfraService_DI
    with SocialAuthServiceDI
      with CalendlyOAuthDI
      with CalendlyWebhookServiceDI
    with WorkflowCrmSettingsDAO_DI
    with WorkflowCrmSettingsServiceDI
    with DomainHealthCheckDAO_DI
    with MqDomainHealthBlacklistCheck_DI
    with MqCnameUptimeCheck_DI
    with CustomTrackingDomainServiceDAO_DI
    with CustomTrackingDomainServiceDI
    with CloudflareServiceDI
    with RepTrackingHostDao_DI
    with DomainHealthCheckService_DI
    with MQDomainServiceProviderDNSService_DI
    with SrDNSUtil_DI
    with DomainPublicDNSDAO_DI
    with MqPublisherHandleEventLogDI
    with NotesService_DI
    with NotesPaginationService_DI
    with NotesDAO_DI
      with MqInactiveDomainCustomTrackingDeleter_DI
    with LeadFinderValidationService_DI
    with LeadFinderDAO_DI
    with OpportunityDAO_DI
    with ProspectEventDAO_DI
    with MQUpdateCallNoteId_DI
    with MqMergeDuplicateProspects_DI
    with ProspectColumnService_DI
    with MqIndependentStepScheduler_DI
    with IndependentStepSchedulerService_DI
    with MqProcessAndGenerateMagicColumn_DI
    with MQOpenTrackerPublisher_DI
    with EmailBodyServiceDI
    with MailDosoApi_DI
    with ZapMailAPI_DI
    with MailDosoService_DI
    with EmailInfraDao_DI
    with PurchasedDomainsAndEmailsDeleterService_DI
    with MqPurchasedDomainsDeleter_DI
    with MqPurchasedEmailsDeleter_DI
    with AccountCreateServiceDI
    with MQAffiliateAutoApprovalConsumer_DI
    with MQAffiliateAutoApprovalPublisher_DI
    with BillingAppServiceDI
    with AffiliateTrackingServiceDI
    with AdmitadDI
    with FirstPromoterDI
    with AffiliateDAO_DI
    with MqEmailHealthCheck_DI
    with EmailHealthCheckService_DI
    with EmailHealthDnsCheckService_DI
    with EmailHealthCheckDAO_DI
    with MqHandleAttemptCreationDI
    with MqHandleAttemptCreationPublisherDI
    with MqInternalLeadFinderDataDI
      with MqUpdateManualEmailTaskStatusDI
    with MqForSaveReplySentimentPublisher_DI
    with MqForSaveReplySentimentConsumer_DI
    with MqPauseCampaignOnReplySentimentSelectDI
    with MqExtractLinkedinConnectionResultsDI
    with MqDeleteLinkedinSettingDI
    with LeadFinderService_DI
    with DripCampaignHealthCheckDAO_DI
    with DripCampaignHealthCheckService_DI
    with MqDripCampaignHealthCheckPublisher_DI
    with MqDripCampaignHealthCheckConsumer_DI
    with MqAiContentGenerationConsumer_DI
    with MqAiContentGenerationPublisher_DI
    with PhantomBusterTeamServiceDI
    with SrAiLockJedisDAO_DI
    with SrAiApi_DI
    with ReplySentimentClassificationService_DI
    with EmailCommonServiceDI
    with ContentAnalysisService_DI
    with ContentGenerationService_DI
    with MqAutoUpdateProspectCategoryPublisherDI
    with MqAutoUpdateProspectCategoryConsumerDI
    with CampaignGenerationService_DI
{

  def start(): Try[String] = {

    val system = workerActorSystem

    val clickTrackerEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(25))
    val openTrackerEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(25))
    val unsubscribeTrackerEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(10))
    val doNotContactEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))
    val prospectDeleterEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(25))
    val emailInvalidWebhookEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(25))
    val webhookProspectCategoryUpdatedEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(25))
    val webhookCompletedEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(10))
    val mqRepliedEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(10))
    val mqCompletedEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(10))
    val mqUnPauseEvent = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(10))
    val listStatsUpdaterEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(10))

    // Trigger / workflow related execution contexts
    val triggerEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(25))

    val activityWorkflowEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(25))


    val ipBlacklistCheckEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(1))
    val spamMonitorEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(1))

    mqOpenTracker.startConsumer()(ws = wSClient, system = system, ec = openTrackerEC)
//      .flatMap { _ => mqMBLEmailValidator.startConsumer()(ws = wSClient, system = system, ec = emailValidatorEC) }
      .flatMap { _ => mqClickTracker.startConsumer()(ws = wSClient, system = system, ec = clickTrackerEC) }
      .flatMap { _ => mqUnsubscribeTracker.startConsumer()(ws = wSClient, system = system, ec = unsubscribeTrackerEC) }
      .flatMap { _ => mqDoNotContact.startConsumer()(ws = wSClient, system = system, ec = doNotContactEC) }
      .flatMap { _ => mqProspectDeleter.startConsumer()(ws = wSClient, system = system, ec = prospectDeleterEC) }
      .flatMap { _ => mqWebhookEmailInvalid.startConsumer()(ws = wSClient, system = system, ec = emailInvalidWebhookEC) }
      .flatMap { _ => mqWebhookCompleted.startConsumer()(ws = wSClient, system = system, ec = webhookCompletedEC) }
      .flatMap { _ => mqRepliedEvent.startConsumer()(ws = wSClient, system = system, ec = mqRepliedEC) }
      .flatMap { _ => mqProspectCompletedEvent.startConsumer()(ws = wSClient, system = system, ec = mqCompletedEC) }
      .flatMap { _ => mqProspectUnPauseEvent.startConsumer()(ws = wSClient, system = system, ec = mqUnPauseEvent) }
      .flatMap { _ => mqListStatsUpdater.startConsumer()(ws = wSClient, system = system, ec = listStatsUpdaterEC) }

      // Trigger / workflow related start consumers
      .flatMap { _ => mqTrigger.startConsumer()(ws = wSClient, system = system, ec = triggerEC) }
      .flatMap { _ => mqActivityTriggerService.startConsumer()(ws = wSClient, system = system, ec = activityWorkflowEC) }
      .flatMap { _ => {
        val mqHandleEventLogEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(MQConfig.handleEventLogPrefetchCount * 2))
        mqHandleEventLogConsumer.startConsumer()(ws = wSClient, system = system, ec = mqHandleEventLogEC)
      } }

      .flatMap { _ => mqIPBlacklistCheck.startConsumer()(ws = wSClient, system = system, ec = ipBlacklistCheckEC) }
      .flatMap { _ => MQSpamMonitor.startConsumer()(ws = wSClient, system = system, ec = spamMonitorEC) }
      .flatMap { _ => {
        val mqCampaignSpamMonitorServiceEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(10))

        mqCampaignSpamMonitorService.startConsumer()(ws = wSClient, system = system, ec = mqCampaignSpamMonitorServiceEC)
      }}
      .flatMap { _ => {
        val mqReadOldEmailForSyncEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(10))

        mqReadOldEmailForSync.startConsumer()(ws = wSClient, system = system, ec = mqReadOldEmailForSyncEC)
      }
      }

      .flatMap { _ => {

        val mqOrgDataDeleterEC = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.orgDataDeleterPrefetchCount * 5
        )

        mqOrgDataDeleter.startConsumer()(ws = wSClient, system = system, ec = mqOrgDataDeleterEC)

      }

      }

      .flatMap { _ => {

        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.intercomUpdaterQueuePrefetchCount
        )

        mqIntercomUpdater.startConsumer()(ws = wSClient, system = system, ec = ec)

      }

      }
      .flatMap{ _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.unSnoozeTasksPrefetchCount * 2
        )

        mqUnSnoozeSnoozedTasks.startConsumer()(ws = wSClient, system = system, ec = ec)
      }}
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.updateSubAccountCallingCreditPrefetchCount * 2
        )

        mqUpdateSubAccountCallingCredit.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.updateSubAccountCallHistoryPrefetchCount * 2
        )

        mqUpdateSubAccountCallHistory.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.whatsAppChannelSchedulerPrefetchCount
        )

        mqWhatsAppChannelScheduler.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }

      .flatMap{ _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.generalChannelSchedulerPrefetchCount
        )

        mqGeneralChannelScheduler.startConsumer()(ws = wSClient, system = system, ec = ec)
      }}

      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.callChannelSchedulerPrefetchCount
        )

        mqCallChannelScheduler.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }

      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.linkedinChannelSchedulerPrefetchCount
        )

        mqLinkedinChannelScheduler.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.updateLatestTaskDoneOrSkippedAtPrefetchCount
        )

        mqUpdateLatestTaskDoneOrSkippedAt.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.campaignSchedulingMetadataMigrationPrefetchCount
        )

        mqCampaignSchedulingMetadataMigration.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.independentStepSchedulerPrefetchCount
        )

        mqIndependentStepScheduler.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.updateCampaignSchedulingMetadataPrefetchCount
        )

        mqUpdateCampaignSchedulingMetadata.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }

      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = 10
        )

        mqRefactorLinkedinUrl.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }

      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.associateProspectPrefetchCount
        )

        mqAssociateProspectToOldEmails.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }
      .flatMap { _ => {
        val ec = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(1))

        mqPreEmailValidation.startConsumer()(ws = wSClient, system = system, ec = ec)
      }}
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.smsChannelSchedulerPrefetchCount
        )

        mqSmsChannelScheduler.startConsumer()(ws = wSClient, system = system, ec = ec)
      }}
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.insertTeamsMetadataPrefetchCount
        )

        mqInsertTeamsMetadata.startConsumer()(ws = wSClient, system = system, ec = ec)
      }}
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.dueLinkedinPrefetchCount
        )

        mqExecuteDueLinkedinTasks.startConsumer()(ws = wSClient, system = system, ec = ec)
      }}
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.prospectsContactedLimitCheckQueuePrefetchCount
        )

        mqProspectContactedLimitCheck.startConsumer()(ws = wSClient, system = system, ec = ec)
      }}
// Date: 2025-06-02 : Commenting this code as we are not using phantom buster for linkedin automation
//      .flatMap { _ => {
//        val ec = Helpers.genFixedThreadPoolEC(
//          threads = MQConfig.linkedinRecreateSessionPrefetchCount
//        )
//
//        mqLinkedinRecreateSession.startConsumer()(ws = wSClient, system = system, ec = ec)
//      }}

      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.campaignSendReportPrefetchCount
        )

        mqCampaignSendReport.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }


      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.campaignSendVolumeReportPrefetchCount
        )

        mqStuckCampaignAddLogAndReport.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }

      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.stopInactiveCampaignPrefetchCount
        )

        mqStopInactiveCampaign.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }

      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.campaignStepContentCheckPrefetchCount
        )

        mqCampaignStepContentCheckService.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }

      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.resetCacheForReportPrefetchCount
        )

        mqResetCacheForReport.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }

      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.internalAdoptionReportPrefetchCount
        )
        mqInternalAdoptionReportUpdater.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }

      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = 30
        )
        mqEmailAccountDeleter.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }

      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.campaignAISequenceGeneratorPrefetchCount
        )
        mqCampaignAISequenceGenerator.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }

// Date: 2025-06-02 : Commenting this code as we are not using phantom buster for linkedin automation
//      .flatMap { _ => {
//        val ec = Helpers.genFixedThreadPoolEC(
//          threads = MQConfig.linkedinInboxScraperPrefetchCount
//        )
//        mqLinkedinInboxScraper.startConsumer()(ws = wSClient, system = system, ec = ec)
//      }
//      }
//
//      .flatMap { _ => {
//        val ec = Helpers.genFixedThreadPoolEC(
//          threads = MQConfig.linkedinMessageThreadScraperPrefetchCount
//        )
//        mqLinkedinMessageThreadScraper.startConsumer()(ws = wSClient, system = system, ec = ec)
//      }
//      }

      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = 40
        )
        mqNewProcessWorkflowAttemptService.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.unsnoozeSnoozedEmailsPrefetchCount
        )

        mqUnsnoozeSnoozedEmails.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.internalTeamBasedMigrationPrefetchCount
        )

        mqInternalTeamBasedMigration.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }

      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.uuidMigrationScriptPrefetchCount
        )

        mqUuidMigrationScript.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }

      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.campaignsProspectsMigrationScriptPrefetchCount
        )

        mqCampaignsProspectsMigrationScript.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.opportunitiesSetupDefaultPrefetchCount
        )
        mqOpportunitiesDefaultSetupMigration.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }
      .flatMap { _ =>
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.processAndGenerateMagicColumnPrefetchCount
        )
        mqProcessAndGenerateMagicColumn.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      .flatMap(_ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.updatedAtScriptPrefetchCount
        )

        mqUpdatedAtMigrationScript.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      )
      .flatMap { _ => {

        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.dncTypeMigrationScriptPrefetchCount
        )

        mqDoNotContactType.startConsumer()(ws = wSClient, system = system, ec = ec)

      }}
      .flatMap { _ => {

        val ec = Helpers.genFixedThreadPoolEC(

          threads = MQConfig.schedulerIntegrityCheckPrefetchCount
        )

        mqSchedulerIntegrityCheck.startConsumer()(ws = wSClient, system = system, ec = ec)

      }
      }
      .flatMap { _ => {

      val ec = Helpers.genFixedThreadPoolEC (

      threads = MQConfig.deletionAndRevertV2PrefetchCount
      )

       mqDeletionAndRevertV2.startConsumer () (ws = wSClient, system = system, ec = ec)

      }
      }
//      .flatMap { _ => {
//
//        val ec = Helpers.genFixedThreadPoolEC (
//
//          threads = MQConfig.analyzeInboxPlacementCheckEmailPrefetchCount
//        )
//
//        mqAnalyzeInboxPlacementCheckEmail.startConsumer () (ws = wSClient, system = system, ec = ec)
//
//      }
//      }
      .flatMap { _ => {

          val ec = Helpers.genFixedThreadPoolEC(

              threads = MQConfig.domainHealthCheckPrefetchCount
          )

          mqDomainHealthBlacklistCheck.startConsumer() (ws = wSClient, system = system, ec = ec)
      }
      }
      .flatMap { _ => {

        val ec = Helpers.genFixedThreadPoolEC (

          threads = MQConfig.findPotentialDuplicateProspectsPrefetchCount
        )

        mqPotentialDuplicateProspects.startConsumer () (ws = wSClient, system = system, ec = ec)

      }
      }      .flatMap { _ => {

        val ec = Helpers.genFixedThreadPoolEC(

          threads = MQConfig.domainServiceProviderDNSServicePrefetchCount
        )

        mqDomainServiceProviderDNSService.startConsumer() (ws = wSClient, system = system, ec = ec)
      }
      }.flatMap { _ => {

        val ec = Helpers.genFixedThreadPoolEC(

          threads = MQConfig.updateProspectCategoryForGivenTeamOfOrgForGlobalDNCPrefetchCount
        )

        mqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC.startConsumer() (ws = wSClient, system = system, ec = ec)
      }
      }
      .flatMap {_ => {
          val ec = Helpers.genFixedThreadPoolEC(
              threads = MQConfig.domainCnameUptimeCheckPrefetchCount
          )
          mqCnameUptimeCheck.startConsumer()(ws = wSClient, system = system, ec =ec)
      }

      }
      .flatMap { _ => {

        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.callNotesMigrationScriptPrefetchCount
        )

        mqUpdateCallNoteId.startConsumer()(ws = wSClient, system = system, ec = ec)

      }}
      .flatMap { _ => {

        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.serverReportCheckPrefetchCount
        )

         mqServerReportCheck.startConsumer()(ws = wSClient, system = system, ec = ec)

      }}
      .flatMap { _ => {

        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.mergeDuplicateProspectsPrefetchCount
        )

        mqMergeDuplicateProspects.startConsumer()(ws = wSClient, system = system, ec = ec)

      }}

      .flatMap { _ => {

        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.purchasedDomainsDeleterPrefetchCount
        )

        mqPurchasedDomainsDeleter.startConsumer()(ws = wSClient, system = system, ec = ec)

      }}
      .flatMap { _ => {

        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.purchasedEmailsDeleterPrefetchCount
        )

        mqPurchasedEmailsDeleter.startConsumer()(ws = wSClient, system = system, ec = ec)

      }}
      .flatMap { _ =>

        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.affiliateAutoApprovalPrefetchCount
        )

        mqAffiliateAutoApprovalConsumer.startConsumer()(ws = wSClient, system = system, ec = ec)

      }
      .flatMap { _ => {

        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.handleAttemptCreationPrefetchCount*40
        )

        mqHandleAttemptCreation.startConsumer()(ws = wSClient, system = system, ec = ec)

      }
      }
      .flatMap { _ =>

          val ec = Helpers.genFixedThreadPoolEC(
              threads = MQConfig.emailHealthCheckPrefetchCount
          )

          mqEmailHealthCheck.startConsumer()(ws = wSClient, system = system, ec = ec)

      }
      .flatMap { _ =>

        val ec = Helpers.genFixedThreadPoolEC(
          threads = 10
        )

        mqZapmailEmailAccountCreationConsumer.startConsumer()(ws = wSClient, system = system, ec = ec)

      }.flatMap { _ => {

        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.processDripCampaignHealthCheckPrefetchCount,
        )

        mqDripCampaignHealthCheckConsumer.startConsumer()(ws = wSClient, system = system, ec = ec)

      }
      }
      .flatMap{ _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.aiContentGenerationQueuePrefetchCount
        )

        mqAiContentGenerationConsumer.startConsumer()(ws = wSClient, system = system, ec = ec)

      }}
      .flatMap { _ => {

        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.internalLeadFinderDataUpdatePrefetchCount * 10
        )

        mqInternalLeadFinderData.startConsumer()(ws = wSClient, system = system, ec = ec)

      }
      }
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = 10
        )

        mqUpdateManualEmailTaskStatus.startConsumer()(ws = wSClient, system = system, ec = ec)
      }

      }
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = 10
        )

        mqForSaveReplySentimentConsumer.startConsumer()(ws = wSClient, system = system, ec = ec)
      }
      }
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = 10
        )

        mqPauseCampaignOnReplySentimentSelect.startConsumer()(ws = wSClient, system = system, ec = ec)
      
      }

      }.flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = 10
        )

        mqCaptainDataConversationExtractionConsumer.startConsumer()(ws = wSClient, system = system, ec = ec)

      }}
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = 10
        )

        mqCaptainDataMessageExtractionConsumer.startConsumer()(ws = wSClient, system = system, ec = ec)

      }}
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = 10
        )

        mqCaptainDataCookieFailureConsumer.startConsumer()(ws = wSClient, system = system, ec = ec)

      }}
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = 10
        )

        mqExtractLinkedinConnectionResults.startConsumer()(ws = wSClient, system = system, ec = ec)

      }

      }
      .flatMap { _ => {
        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.autoUpdateProspectCategoryPrefetchCount
        )

        mqAutoUpdateProspectCategoryConsumer.startConsumer()(ws = wSClient, system = system, ec = ec)

        }
      }
      .flatMap { _ => {
          val ec = Helpers.genFixedThreadPoolEC(
            threads = 10
          )
          mqDeleteLinkedinSetting.startConsumer()(ws = wSClient, system = system, ec = ec)
        }
      }
      .flatMap { _ => {

          val ec = Helpers.genFixedThreadPoolEC(
              threads = MQConfig.customTrackingDomainRecordDeleterPrefetchCount,
          )

          mqInactiveDomainCustomTrackingDeleter.startConsumer()(ws = wSClient, system = system, ec = ec)

      }}.flatMap { _ => {

        val ec = Helpers.genFixedThreadPoolEC(
          threads = MQConfig.mqSrAiApiPrefetchCount,
        )

        mqSrAiApiConsumer.startConsumer()(ws = wSClient, system = system, ec = ec)

      }}
    //      .flatMap { _ => {
    //        val ec = Helpers.genFixedThreadPoolEC(
    //
    //          threads = MQConfig.updateUuidMigrationScriptPrefetchCount
    //        )
    //
    //        mqUpdateUuidMigrationScript.startConsumer()(ws = wSClient, system = system, ec = ec)
    //
    //      }
    //      }
  }


}
