package utils.appentrypoints.workers

import org.apache.pekko.actor.Cancellable
import common_auth_middleware.UserRedisKeyService_DI
import utils.Helpers.genFixedThreadPoolEC
import utils.SRAppConfig
import utils.dependencyinjectionutils.Tasks.{TaskDAO_DI, TaskDaoServiceDI, TaskPartitionManagerService_DI, TaskServiceDI}
import utils.dependencyinjectionutils.Intercom.{CampaignForSpamCheckQueueCron_DI, MQReadOldEmailForSync_DI, MqCampaignSpamMonitorService_DI, OldEmailSyncForTeamInboxCron_DI, OldEmailSyncService_DI, ScheduleMagicColumnGenerationCron_DI}
import utils.dependencyinjectionutils.*
import utils.dependencyinjectionutils.BillingV2.BillingAppServiceDI
import utils.dependencyinjectionutils.Calendar.{CalendarAppApiDI, CalendarAppDaoDI, CalendarAppServiceDI}
import utils.dependencyinjectionutils.Campaign.CampaignTestStepServiceDI
import utils.dependencyinjectionutils.ChannelSchedulers.{EmailMessageDataDAO_DI, EmailScheduledDAOService_DI, EmailSchedulerJedisService_DI, SrRedisHashSetBasedLockServiceV2_DI, SrRedisHashSetServiceV2_DI, SrRedisSimpleLockServiceV2_DI}
import utils.dependencyinjectionutils.DripHealthCheck.*
import utils.dependencyinjectionutils.LinkedinSettingDI.{MqCaptainDataCookieFailureConsumerDI, MqCaptainDataCookieFailurePublisherDI}
import utils.mq.limit_check.MqLimitValidatorPublisher
//import utils.dependencyinjectionutils.EventFramework.{KafkaServiceDI, SrEventServiceDI}
import utils.dependencyinjectionutils.LinkedinSettingDI.{LinkedinSettingServiceDI, PlanLimitServiceDI}
import utils.dependencyinjectionutils.ProductOnboardingDI.{ProductOnboardingDAO_DI, ProductOnboardingServiceDI}
import utils.dependencyinjectionutils.ProductOnboardingDI.RollingUpdate.SrRollingUpdateCoreService_DI
import utils.dependencyinjectionutils.SmsSettingDI.SmsSettingDAODI
import utils.dependencyinjectionutils.Team._

import scala.util.Try

class MainSchedulersLoader
  extends EmailValidationBatchRequestModelDI
    with EmailValidationApiToolsRecordDAODI
    with EmailValidationDAOService_DI
    with CampaignProspectStepScheduleLogsDAO_DI
    with EmailValidationServiceDI
    with EmailValidationDailyReportService_DI
    with PhantomBusterAgentServiceDI
    with PhantomBusterAgentsDAODI
    with MQProspectDeleterDI
    with MQCampaignAISequenceGeneratorDI
    with DomainPublicDNSDAO_DI
    with MqAiContentGenerationPublisher_DI
    with MQDomainServiceProviderDNSService_DI
    with MQServerReportCheck_DI
    with SrDNSUtil_DI
    with MQOpenTrackerPublisher_DI
    with AIHyperPersonalizedGeneratorDI
    with EmailMessageDataDAO_DI
    with LlmAuditLogDAO_DI
    with GptApiService_DI
    with UserCheckApiServiceDI
    with EmailScheduledDAOService_DI
    with PlanLimitServiceDI
    with MqLimitValidatorDI
    with MqLimitValidatorPublisherDI
    with BrightDataServiceDI
    with BrightDataApiDI
    with CreateInCRMJedisDAO_DI
    with MigrationFuncDI
    with TeamsMetadataServiceDI
    with TeamsMetadataDAODI
    with SyncTriggerMQServiceDI
    with DeBounceEmailValidationApiDI
    with EmailSettingTagsJedisServiceDI
    with ListCleanEmailValidationApiDI
    with LinkedinAutomationReportServiceDI
    with LinkedinActionsPublishServiceDI
    with LinkedinTaskServiceDI
    with ProspectTagServiceDI
    with ProspectTagDAO_DI
    with PhantomBusterApiKeysServiceDI
    with MqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC_DI
    with BlacklistServiceV2_DI
    with LinkedinSessionCreatorDI
    with PhantomBusterApiKeysDAODI
    with PhantomBusterProxyDAO_DI
    with PhantomBusterProxyServiceDI
    with CloudStorageDI
    with MqCampaignSchedulingMetadataMigration_DI
    with CampaignSendReportService_DI
    with MqStuckCampaignAddLogAndReport_DI
    with PhantomBusterExecutionLogsDAO_DI
    with PhantomBusterExecutionLogsServiceDI
    with ActivityTriggerMQServiceDI
    with MQActivityTriggerPublisher_DI
    with ProspectEmailsDAOService_DI
    with ProspectUploadServiceDI
    with GeneralSettingServiceDI
    with CampaignSchedulingMetadataDAO_DI
    with SpamMonitorCheckServiceDI
    with ActiveCampaignImplDI
    with SRHealthCheckServiceDI
    with MqCampaignStepContentCheckServiceDI
    with EmailSettingDAOServiceDI
    with LinkedinMessagesServiceDI
    with LinkedinMessagesDAODI
    with LinkedinMessageThreadsServiceDI
    with LinkedinConnectionsServiceDI
    with LinkedinConnectionDAODI
    with LinkedinMessageThreadsDAODI
    with EmailSettingJedisServiceDI
    with LinkedinSettingServiceDI
    with CaptainDataServiceDI
    with MqCaptainDataConversationExtractionPublisher_DI
    with MqCaptainDataCookieFailurePublisherDI
    with MqCaptainDataConversationExtractionConsumer_DI
    with MqCaptainDataMessageExtractionPublisher_DI
    with MqCaptainDataMessageExtractionConsumer_DI
    with MqCaptainDataCookieFailureConsumerDI
    with MqExecuteDueLinkedinTasksDI
    with PhantomBusterServiceDI
    with PhantomBusterApiDI
    with CaptainDataApiDI
    with Template.TemplateUtilsDI
    with Campaign.CampaignStepServiceDI
    with GPTServiceDI
    with AISequenceGeneratorDI
    with SrRateLimiterDI
    with SrRateLimiterDAO_DI
    with TemplateDAO_DI
    with GPTApiDI
    with AccountOrgBillingRelatedInfoDAODI // lev : 3

    with AccountOrgBillingRelatedServiceDI // lev : 2 (EmailSenderServiceDI)

    with EmailServiceDI

    with RepTrackingHostServiceDI

    with EmailSenderServiceDI
    with RolePermissionDataDAOV2_DI
    with OrgMetadataServiceDI
    with PusherServiceDI
    with CampaignSendReportsDAO_DI
    with SrRandomUtilsDI
    with CampaignSendReportCronServiceDI
    with MqCampaignSendReportDI
    with CampaignProspectTimezonesJedisServiceDI
    with SrShuffleUtilsDI
    with TaskServiceDI
    with MQGeneralChannelSchedulerDI
    with MQLinkedinChannelSchedulerDI
    with LinkedinSettingDAO_DI
    with ChannelSchedulers.LinkedinChannelSchedulerDI
    with TaskDaoServiceDI
//    with TaskCacheDAO_DI
//    with TaskCacheService_DI
//    with SrEventServiceDI
//    with KafkaServiceDI
    with CampaignDAOService_DI
    with EmailServiceCompanionDI
    with GeneralSettingDAO_DI
    with EmailSpamTesterDI
    with AgencyDashboardCacheServiceDI
    with CacheServiceJedisDI
    with AccountServiceDI
    with SrResourceDI.SrResourceDaoServiceDI
            with SrResourceDI.SrResourceWithoutTenantDaoServiceDI
    with SrResourceDI.SrCacheDI
    with SrUuidInternalDataServiceDI
    with TemplateServiceDI
    with EmailHandleErrorServiceDI
    with OutlookApiSendEmailServiceDI
    with OutlookApiReplyTrackingServiceDI
    with GmailEmailApiDI
    with GmailApiReplyTrackingServiceDI
    with GmailApiSendEmailServiceDI
    with GmailEmailSendServiceDI
    with GmailSmtpEmailApiDI
    with OutlookEmailApiDI
    with MailgunServiceDI
    with MailgunEmailApiDI
    with SendGridServiceDI
    with SendGridEmailApiDI
    with SmtpImapSendEmailServiceDI
    with SmtpImapReplyTrackingServiceDI // FIXME: this doesnt belong here. Need to refactor EmailAccountTestService and remove this
    with SmtpImapEmailApiDI
    with AccountDAO_DI
    with SRCsvParserUtilsDI
    with UnivocityParserDI
    with CustomCSVParserDI
    with SRCsvReaderUtilsDI
    with CampaignServiceDI
    with InternalSchedulerRunLogDAO_DI
    with CampaignSendingVolumeLogsDAO_DI
    with CampaignCacheServiceDI
    with ReplyFilteringServiceDI
    with WebhookDAO_DI
    with WebhookUtils_V2_DI
    with ProspectQueryDI
    with GoogleOAuthDI
    with MicrosoftOAuthDI
    with SrUserFeatureUsageEventServiceDI

    with BlacklistServiceDI
    with BlacklistDAO_DI

    with MQEmailSchedulerV2DI
    with ChannelSchedulers.EmailChannelSchedulerDI
    with ChannelSchedulers.GeneralChannelSchedularDI
    with MQUnsubscriberTrackerDI
    with MQWebhookCompletedDI
    with MQListStatsUpdaterDI
    with MQReplyTrackerPublisher_DI
    with MQDoNotContactDI
    with MQDoNotContactPublisher_DI
    with MQWebhookEmailInvalidDI


    with MQTriggerDI
//    with MQMBLEmailValidatorDI

    with IntegrationTriggerSyncCronServiceDI
    with ScheduleEmailCronServiceV2DI
    with ReadEmailCronServiceDI
    with OneMinutelyCronServiceDI
    with UploadCsvCronServiceDI
//    with UploadLeadFinderCsvCronServiceDI
//    with UploadLeadFinderCsvLinkedinCronServiceDI
//    with LeadFinderUploadServiceDI
//    with LeadFinderUploadDaoDI
    with EmailSpamMonitoringCronDI
    with SpamTestCronServiceDI
    with DailyCronServiceDI
    with FiveMinutelyCronServiceDI
    with HourlyCronServiceDI
      with ProspectBatchActionServiceDI
    with ApiService_DI
    with BlacklistApiService_DI
    with CallApiService_DI
    with TeamsApiService_DI
    with ProspectApiService_DI
    with ProspectEventService_DI
      with CampaignApiService_DI
      with ProspectEventDAOService_DI
      with GeneralModuleServiceDI
      with CampaignProspectAssignDI
      with GeneralModuleDI
      with MQProspectCompletedEventDI
      with MQProspectUnPauseEventDI
      with MQRepliedEventDI
    with TerminateBacklogDBConnectionCronServiceDI
    with EmailNotificationCronServiceDI
    with EmailNotificationDAO_DI
    with OutlookMsgIdFetchAndUpdateCronServiceDI
    with TasksPartitionMaintenanceCron_DI
    with TaskPartitionManagerService_DI
    with DataIntegrityCheckCronDI
    with TaskDAO_DI
    with ProspectAddEventDAO_DI
    with CampaignProspectDAO_DI
    with ProspectUpdateCategoryTemp_DI
    with ProspectUpdateCategoryTemp2_DI

    with DBCounterDAO_DI
    with SrDBQueryCounterService_DI
    with CampaignDAO_DI
    with TriggerDAO_DI
    with EmailScheduledDAO_DI
    with EmailScheduledServiceDI
    with EmailSettingDAO_DI
    with EmailThreadDAO_DI
    with CampaignStepVariantDAO_DI
    with PhishingCheckServiceDI
    with EmailReplyTrackingModelDI
    with TriggerServiceDI
    with HandlePushTriggerEventServiceDI
    with HandleSyncTriggerEventServiceDI
    with HandleActivityTriggerEventServiceDI
    with HandleAddToDNCTriggerEventServiceDI
    with LeadStatusServiceDI
    with ProspectServiceDI
    with CampaignProspectServiceDI
    with DripLogJedisDAO_DI
    with BouncerEmailValidationApiDI
    with CampaignsMissingMergeTagServiceDI

    with WorkerActorSystemDI
    with WorkerWSClientDI
    with WorkerActorMaterializerDI
    with EmailValidationModelDI
    with TransformAndSaveEmailValidationResultDI
    with EmailDeliveryAnalysisServiceDI
    with ProspectAccountDAO1DI
    with NewUserDripCampaignsDAO_DI
    with CampaignStepDAO_DI
    with DeleteOrgSRCron_DI
    with OrganizationDAO_DI
    with DeleteAccountSrCron_DI
    with AccountDAOV2_DI
    with OrganizationBillingDAO_DI
    with EmailNotificationServiceDI
    with MigrationUtils_DI
    with MigrationDAO_DI
    with MigrationService_DI
    with EmailHealthCheckDAO_DI
    with MqEmailHealthCheck_DI
    with EmailHealthCheckService_DI
    with EmailHealthDnsCheckService_DI
    with FreeEmailDomainListService_DI
    with FreeEmailDomainListDAO_DI
    with EventLogDAO_DI
    with EventLogService_DI
    with EventLogFindServiceDI
    with WorkflowAttemptDAO_DI
    with WorkFlowAttemptService_DI
    with AccessTokenService_DI
    with ClientAccountAccessLogDAO_DI
    with UserRedisKeyService_DI
    with CampaignEditedPreviewEmailDAO_DI
    with CampaignTemplateServiceDI
    with TagServiceDI
    with ProspectColumnDef_DI
    with ProspectColumnDefDAO_DI
    with EmailReplyTrackingModelV2DI
    with MqSrAiApiPublisher_DI
    with SrBlacklistMonitorCronDI
    with SrBlacklistCheckServiceDI
    with SrMailServerServiceDI
    with SrMailServerDao_DI
    with SrMailServerRedisService_DI
    with RepBlacklistMonitorCheckResultsDao_DI
    with ProspectsEmailsDAO_DI
    with ProspectServiceV2_DI
    with EmailStuckForSendingAlertCron_DI
    with ResetUserCacheUtilDI
    with AccountDAOService_DI
    with CacheServiceDI
    with ReplyTrackerService_DI
    with EmailMessageContactModel_DI
    with TeamsDAO_DI
    with DBUtils_DI
    with EmailSendingStatusDAO_DI
    with SrInternalFeatureUsageDaoDI
    with BlacklistProspectCheckDAO_DI
    with CampaignSchedulingMetadataCheckCron_DI
    with MqUpdateCampaignSchedulingMetadata_DI
    with NewUserDripCamapignServiceDI
    with CampaignProspectDAO_2DI
    with CloudflareServiceDI
    with CustomTrackingDomainServiceDI
    with CustomTrackingDomainServiceDAO_DI
    with RepTrackingHostDao_DI
    with GenerateTempId_DI

    with Intercom.IntercomUpdateQueueingServiceDI
    with Intercom.MqIntercomUpdaterDI
    with Intercom.IntercomUpdateServiceDI
    with Intercom.IntercomApiDI
//    with CacheEventScyllaService_DI
//    with CacheEventScyllaDAO_DI
//    with ScyllaRunSafely_DI
//    with ScyllaDbConnection_DI
    with HealthChecks.SrEmailSendingStoppedCheckServiceDI
    with CampaignForSpamCheckQueueCron_DI
    with MqCampaignSpamMonitorService_DI
    with SpamMonitorService_DI
    with DomainInfoWhoisDAO_DI
    with DomainDataService_DI
    with ApiLayerAPIService_DI
    with RapidAPIService_DI
    with EmailSendingStatusService_DI
    with SpamTest.SpamTestDAO_DI
    with SupportAccessToUserAccountDAO_DI
    with SupportAccessToTeamInboxExpireCron_DI
    with UnSnoozeSnoozeTaskCron_DI
    with UpdateCallingCreditCron_DI
    with PollMaildosoPurchaseTasksCron_DI
    
    with PurchasedDomainAndEmailDeleterCron_DI
    with PurchaseDomainsAndEmailsCron_DI
    with AiContentGenerationCron_DI
    with EmailInfraDao_DI
    with MqPurchasedEmailsDeleter_DI
    with PurchasedDomainsAndEmailsDeleterService_DI
    with MqPurchasedDomainsDeleter_DI
    with EmailInfraService_DI
    with SocialAuthServiceDI
    with CalendlyOAuthDI
    with CalendlyWebhookServiceDI
    with WorkflowCrmSettingsDAO_DI
    with WorkflowCrmSettingsServiceDI
    with MailDosoApi_DI
    with ZapMailAPI_DI
    with MailDosoService_DI
    with UpdateCallHistoryLogCron_DI
    with IndependentStepSchedulerCron_DI
    with MqIndependentStepScheduler_DI
    with IndependentStepSchedulerService_DI
    with MqUpdateSubAccountCallHistory_DI
    with MqUpdateSubAccountCallingCredit_DI
    with CallServiceDI
    with TwilioDialerServiceDI
    with MqUnSnoozeSnoozedTasks_DI
    with MqWhatsAppChannelScheduler_DI
    with WhatsAppChannelSchduler_DI
    with WhatsappSettingDI.WhatsappSettingDAODI
    with OldEmailSyncForTeamInboxCron_DI
    with MQReadOldEmailForSync_DI
    with TeamInboxDAO_DI
    with OldEmailSyncService_DI
//    with MqHandleEventLogDI
    /*with DummyCronWithAlertingDI*/
    with EmailReplyTrackingDAOService_DI
    with ReplySentimentService_DI
    with ReplySentimentLLMDAO_DI
    with ReplySentimentDAO_DI
    with ReplySentimentJedisDAO_DI
    with MqInternalTeamBasedMigration_DI
    with MergeTagService_DI
    with ProspectDAOService_DI
    with ProspectDAOServiceV2_DI
    with CampaignProspectDAOService_DI
    with OrganizationDAOService_DI
    with ProspectDAO_DI
    with ReplySentimentDAOService_DI
    with AssociateProspectToEmailThreadService_DI
    with MQAssociateProspectToOldEmails_DI
    with HubspotApiDI
    with HubSpotOAuth_DI
    with PipedriveOAuth_DI
    with SalesforceOAuth_DI
    with ZohoOAuth_DI
    with ZohoApiDI
    with ZohoRecruitOAuth_DI
    with PipedriveApiDI
    with SalesForceApiDI
    with IntegrationTypeService_DI
    with SRTriggerAllowedCombos_DI
    with TriggerServiceV2_DI
    with TriggerJedisDAO_DI
    with TriggerDAOService_DI
    with TIntegrationCRMService_DI
    with InboxV3ServiceDI
    with LinkedinMessagesDAOService_DI
    with DeleteService_DI
    with MqEmailAccountDeleterDI
    with EmailSettingService_DI
    with SrBlackListCheckCron_DI
    with MqPreEmailValidationDI
    with SrPreValidationCronDI
    with WorkflowAutomationReportingCronService_DI
    with WorkflowAutomationReportingService_DI
    with WorkflowAutomationReportingDAO_DI
    with SrUuidUtilsDI
    with CampaignProspectService2DI
    with TeamServiceDI
    with TeamDAOService_DI
    with TeamSRAIFlagsJedisDAO_DI
    with SrUuidServiceDI
    with MqSmsChannelScheduler_DI
    with SmsChannelScheduler_DI
    with SmsSettingDAODI
    with MqCallChannelScheduler_DI
    with CallChannelScheduler_DI
    with CallDAO_DI
    with AttemptRetryCronService_DI
    with EmailThreadDAOService_DI
    with InternalAdoptionReportUpdateQueueingServiceDI
    with MqInternalAdoptionReportUpdaterDI
    with InternalAdoptionReportUpdateServiceDI
    with InboxV3DAOService_DI
    with OrgMetadataDAO_DI
    with AdmitadDI
    with BillingAppServiceDI
    with ScheduleAffiliateApprovalsCronServiceDI
    with WorkFlowAttemptJedisDAOService_DI
    with WorkFlowAttemptSetJedisDAO_DI
    with CRMSettingsForWorkFlowAttemptJedisDAO_DI
    with ProcessAttemptService_DI
    with InternalCSDReportDAODI
    with ScheduleUpdateAcquisitionReportServiceDI
    with WorkflowJedisService_DI
    with WorkflowRateLimitJedisDAOService_DI
    with CRMSettingsRateLimitJedisDAO_DI
    with WorkflowAttemptRateLimitSetJedisDAO_DI
    with MQNewProcessWorkflowAttemptService_DI
    with Campaign.ChannelSettingServiceDI
    with MQUnsnoozeSnoozedEmails_DI
    with UnsnoozeSnoozedEmailCron_DI
    with CampaignEmailSettingsDAO_DI
    with StopInactiveCampaignCron_DI
    with MqStopInactiveCampaign_DI
    with ApiLayerServiceDI
    with ApiLayerDAO_DI
    with SrRedisHashSetBasedLockServiceV2_DI
    with SrRedisSimpleLockServiceV2_DI
    with SrRedisHashSetServiceV2_DI
    with EmailSchedulerJedisService_DI
    with MqOrgDataDeleterDI
    with ProspectTagDAOLegacy_DI
    with CallUtilsDI
    with EmailsScheduledDeleteService_DI
    with SelectAndPublishForDeletionServiceDI
    with OrgDeactivateService_DI
    with CallHistoryLogDAO_DI
    with OpportunitiesDefaultSetupService_DI
    with PipelineDAO_DI
    with OpportunityStatusDAO_DI
    with CalendarAppServiceDI
    with CalendarAppDaoDI
    with CalendarAppApiDI
    with TeamsPipelineConfigDAO_DI
    with TeamsPipelineConfigService_DI
    with OrganizationService_DI
    with TeamInboxService_DI
    with LinkedinTeamInboxDAO_DI
    with OrganizationJedisCacheDao_DI
    with SrDateTimeUtils_DI
    with TeamsListingPaginationService_DI
    with SrRollingUpdateCoreService_DI
    with FifteenMinutelyCronService_DI
//    with MqDeletionAndRevertV2DI
    with MqDeletionAndRevertV2Publisher_DI
    with SchedulerIntegrityServiceDI
    with SchedulerIntegrityReportDaoDI
    with InboxPlacementCheckDAO_DI
    with DomainChecksDAO_DI
    with CampaignStepDaoServiceDI
    with MqSchedulerIntegrityCheckDI
    with MqSendEmailForInboxPlacementCheck_DI
    with CampaignTestStepServiceDI
    with OutlookUtilsService_DI
    with DomainHealthCheckDAO_DI
    with MqDomainHealthBlacklistCheck_DI
    with DomainHealthCheckService_DI
    with InboxPlacementCheckLogsReportService_DI
    with PotentialDuplicateProspectService_DI
    with SrAiApiCron_DI
    with PotentialDuplicateProspectsDAO_DI
    with ProspectCategoryServiceDI
    with MqCnameUptimeCheck_DI
      with MqInactiveDomainCustomTrackingDeleter_DI
    with NewSignupReportService_DI
    with NewSignUpReportDAO_DI
    with ProductOnboardingServiceDI
    with ProductOnboardingDAO_DI
    with DomainBlacklistCheckReportService_DI
    with MqAnalyzeInboxPlacementCheckEmail_DI
    with MqPotentialDuplicateProspects_DI
    with MqZapmailEmailAccountCreationPublisher_DI
    with MqZapmailEmailAccountCreationConsumer_DI
    with MqPublisherHandleEventLogDI
    with NotesService_DI
    with NotesPaginationService_DI
    with NotesDAO_DI
    with LeadFinderValidationService_DI
    with OpportunityDAO_DI
    with LeadFinderDAO_DI
    with ProspectEventDAO_DI
    with ScheduleMagicColumnGenerationCron_DI
    with MqProcessAndGenerateMagicColumn_DI
    with EmailAccountServiceDI
    with EmailAccountTestServiceDI
    with EmailAccountUploadService_DI
    with ProspectColumnService_DI
    with AffiliateDAO_DI
    with FirstPromoterDI
    with AffiliateTrackingServiceDI
    with EmailBodyServiceDI
    with AccountCreateServiceDI
    with MQAffiliateAutoApprovalPublisher_DI
    with MqHandleAttemptCreationDI
    with MqHandleAttemptCreationPublisherDI
    with InternalLeadFinderDataUpdateCronDI
    with MqInternalLeadFinderDataDI
    with LeadFinderService_DI
    with DripCampaignHealthCheckDAO_DI
    with DripCampaignHealthCheckService_DI
    with MqDripCampaignHealthCheckPublisher_DI
    with MqForSaveReplySentimentPublisher_DI
    with MqPauseCampaignOnReplySentimentSelectDI
    with MqExtractLinkedinConnectionResultsDI
    with MqDeleteLinkedinSettingDI
    with LinkedinSettingDeletionCronDI
    with MonitorDripCampaignsCron_DI
    with PhantomBusterTeamServiceDI
    with EmailCommonServiceDI 
    with SrAiLockJedisDAO_DI
    with SrAiApi_DI
    with ContentAnalysisService_DI
    with ContentGenerationService_DI
    with MqAutoUpdateProspectCategoryPublisherDI
    with CampaignGenerationService_DI
{

  def start(): Try[Cancellable] = Try {

    val system = workerActorSystem


    {
      // NxD: reduce the no of scheduler threads from 25 to 15, 28-nov-2023, 2206 ist
      val scheduleEmailCronMqEC = genFixedThreadPoolEC(10)

      scheduleEmailCronServiceV2.start()(system = system, wsClient = wSClient, ec = scheduleEmailCronMqEC)
    }


    {
      val readEmailEC = genFixedThreadPoolEC(50)
      readEmailCronService.start()(system = system, ec = readEmailEC, wsClient = wSClient)
    }

    {
      val spamTestEC = genFixedThreadPoolEC(10)

      spamTestCronService.start()(system = system, ec = spamTestEC, wsClient = wSClient)
    }

    {
      val  workflowAutomationReportingEC = genFixedThreadPoolEC(1)

      workflowAutomationReportingCronService.start()(system = system, ec =  workflowAutomationReportingEC, wsClient = wSClient)
    }

    {
      val dailyCronEC = genFixedThreadPoolEC(10)

      dailyCronService.start()(system = system, ec = dailyCronEC, wsClient = wSClient)
    }


    {
      val oneMinutelyCronEC = genFixedThreadPoolEC(20)

      oneMinutelyCronService.start()(system = system, ec = oneMinutelyCronEC, wsClient = wSClient)
    }

    {
      val fiveMinutelyCronEC = genFixedThreadPoolEC(20)

      fiveMinutelyCronService.start()(system = system, ec = fiveMinutelyCronEC, wsClient = wSClient)
    }

    {
      val hourlyCronEC = genFixedThreadPoolEC(10)

      hourlyCronService.start()(system = system, ec = hourlyCronEC, wsClient = wSClient)
    }

  /* 11-May-2024: will add this back later  {
      val terminateBacklogDBCOnnectionCronEC = genFixedThreadPoolEC(1)

      terminateBacklogDBConnectionCronService.start()(system = system, ec = terminateBacklogDBCOnnectionCronEC, wsClient = wSClient)
    }
*/

    {
      val uploadCSVEC = genFixedThreadPoolEC(10)

      uploadCsvCronService.start()(system = system, wsClient = wSClient, ec = uploadCSVEC)
    }
/*
17-jun-2025: [DO_NOT_REMOVE] [LEADFINDERUPLOAD] was slowing down compilation. Only needed for the Lead database upload.

    {
      val uploadCSVEC = genFixedThreadPoolEC(10)

      uploadLeadFinderCsvCronService.start()(system = system, wsClient = wSClient, ec = uploadCSVEC)
    }

    {
      val uploadCSVEC = genFixedThreadPoolEC(10)

      uploadLeadFinderLinkedinCsvCronService.start()(system = system, wsClient = wSClient, ec = uploadCSVEC)
    }

  */

    {
      val emailNotificationCronEC = genFixedThreadPoolEC(10)

      emailNotificationCronService.start()(system = system, wsClient = wSClient, ec = emailNotificationCronEC)
    }

    {
      val outlookMsgIdFetchAndUpdateEC = genFixedThreadPoolEC(10)

      outlookMsgIdFetchAndUpdateCronService.start()(system = system, ec = outlookMsgIdFetchAndUpdateEC, wsClient = wSClient)
    }

    {
      val integrationTriggerSync = genFixedThreadPoolEC(10)

      integrationTriggerSyncCronService.start()(system = system, ec = integrationTriggerSync, wsClient = wSClient)
    }

//    {
//      val emailSpamMonitoringEC = genFixedThreadPoolEC(1)
//
//      emailSpamMonitoringCron.start()(
//        system = system,
//        ec = emailSpamMonitoringEC,
//        wsClient = wSClient
//      )
//    }

   /* {
      val dummyMonitoringEC = genFixedThreadPoolEC(1)

      dummyCronService.start()(
        system = system,
        ec = dummyMonitoringEC,
        wsClient = wSClient
      )
    }*/

    {
      val deleteOrgCronEC = genFixedThreadPoolEC(threads = 2)

      deleteOrgSRCron.start()(
        system = system,
        ec = deleteOrgCronEC,
        wsClient = wSClient
      )
    }
    {
      val supportAccessToTeamInboxExpireCronEC = genFixedThreadPoolEC(threads = 1)

      supportAccessToTeamInboxExpireCron.start()(
        system = system,
        ec = supportAccessToTeamInboxExpireCronEC,
        wsClient = wSClient
      )
    }
    {
      val deleteAccountCronEC = genFixedThreadPoolEC(threads = 1)

      deleteAccountSrCron.start()(
        system = system,
        ec = deleteAccountCronEC,
        wsClient = wSClient
      )
    }

    {
      val srBlacklistMonitorCronEC = genFixedThreadPoolEC(threads = 1)

      srBlacklistMonitorCron.start()(
        system = system,
        ec = srBlacklistMonitorCronEC,
        wsClient = wSClient
      )
    }
    {

      val campaignForSpamCheckQueueCronEC = genFixedThreadPoolEC(threads = 2)

      campaignForSpamCheckQueueCron.start()(
        system = system,
        ec = campaignForSpamCheckQueueCronEC,
        wsClient = wSClient
      )
    }
    {

      val oldEmailSyncForTeamInboxCronEC = genFixedThreadPoolEC(threads = 2)

      oldEmailSyncForTeamInboxCron.start()(
        system = system,
        ec = oldEmailSyncForTeamInboxCronEC,
        wsClient = wSClient
      )
    }
    {
      val tasksPartitionMaintenanceCronEC = genFixedThreadPoolEC(threads = 2)

      taskPartitionMaintenanceCron.start()(
        system = system,
        ec = tasksPartitionMaintenanceCronEC,
        wsClient = wSClient
      )
    }
    {
      val unSnoozeSnoozeTaskCronEC = genFixedThreadPoolEC(4)

      unSnoozeSnoozeTaskCron.start()(system = system, ec = unSnoozeSnoozeTaskCronEC, wsClient = wSClient)
    }
    {
      val scheduleMagicColumnGenerationCronEC = genFixedThreadPoolEC(2)

      scheduleMagicColumnGenerationCron.start()(system = system, ec = scheduleMagicColumnGenerationCronEC, wsClient = wSClient)
    }
    {
      val scheduleMagicColumnGenerationCronEC = genFixedThreadPoolEC(10)

      monitorDripCampaignsCron.start()(system = system, ec = scheduleMagicColumnGenerationCronEC, wsClient = wSClient)
    }
    {
      val updateCreditCronEC = genFixedThreadPoolEC(1)

      updateCallingCreditCron.start()(system = system, ec = updateCreditCronEC, wsClient = wSClient)
    }

    {

      val aiContentCreditCronEC = genFixedThreadPoolEC(5)
      
      aiContentGenerationCron.start()(system = system, ec = aiContentCreditCronEC, wsClient = wSClient )

    }    
    {

      val srAiApiCronEC = genFixedThreadPoolEC(5)

      srAiApiCron.start()(system = system, ec = srAiApiCronEC, wsClient = wSClient )

    }

    {
      val pollMaildosoPurchaseTasksCronEC = genFixedThreadPoolEC(4)

      pollMaildosoPurchaseTasksCron.start()(system = system, ec = pollMaildosoPurchaseTasksCronEC, wsClient = wSClient)
    }

    {
      val purchasedDomainAndEmailDeleterCronEC = genFixedThreadPoolEC(4)

      purchasedDomainAndEmailDeleterCron.start()(system = system, ec = purchasedDomainAndEmailDeleterCronEC, wsClient = wSClient)
    }

    {
      val purchaseDomainsAndEmailsCronEC = genFixedThreadPoolEC(4)

      purchaseDomainsAndEmailsCron.start()(system = system, ec = purchaseDomainsAndEmailsCronEC, wsClient = wSClient)
    }

    {
      val updateCallHistoryLogCronEC = genFixedThreadPoolEC(4)

      updateCallHistoryLogCron.start()(system = system, ec = updateCallHistoryLogCronEC, wsClient = wSClient)
    }

    {
      val independentStepSchedulerCronEC = genFixedThreadPoolEC(4)

      independentStepSchedulerCron.start()(
        system = system,
        ec = independentStepSchedulerCronEC,
        wsClient = wSClient
      )
    }

    {
      val srBlacklistCheckCronEC = genFixedThreadPoolEC(1)

      srBlackListCheckCron.start()(system = system, ec = srBlacklistCheckCronEC, wsClient = wSClient)
    }
    {
      val campaignSendReportCronServiceEC = genFixedThreadPoolEC(1)

      campaignSendReportCronService.start()(system = system, ec = campaignSendReportCronServiceEC, wsClient = wSClient)
    }

    {
      val fifteenMinutelyCronServiceEC = genFixedThreadPoolEC(10)

      fifteenMinutelyCronService.start()(system = system, ec = fifteenMinutelyCronServiceEC, wsClient = wSClient)
    }

    {
      val campaignSchedulingMetadataCheckCronEC = genFixedThreadPoolEC(1)

      campaignSchedulingMetadataCheckCron.start()(system = system, ec = campaignSchedulingMetadataCheckCronEC, wsClient = wSClient)
    }
    {
      val srPreValidationCronEC = genFixedThreadPoolEC(1)

      srPreValidationCron.start()(system = system, ec = srPreValidationCronEC, wsClient = wSClient)
    }
    {
      val attemptRetryCronServiceEC = genFixedThreadPoolEC(10)

      attemptRetryCronService.start()(system = system, ec = attemptRetryCronServiceEC, wsClient = wSClient)
    }
    /*
      12 Mar 2025
        Stopping this CRON until we stop including
        Maildoso and Twilio related subscriptions in affiliate payments.
    {
      val scheduleAffiliateApprovalsCronServiceEC = genFixedThreadPoolEC(1)

      scheduleAffiliateApprovalsCronService.start()(
        system = system,
        ec = scheduleAffiliateApprovalsCronServiceEC,
        wsClient = wSClient
      )
    }
    */
    {
      val scheduleUpdateAcquisitionReportServiceEC = genFixedThreadPoolEC(1)

      scheduleUpdateAcquisitionReportService.start()(
        system = system,
        ec = scheduleUpdateAcquisitionReportServiceEC,
        wsClient = wSClient
      )
    }

    {
      val unSnoozeSnoozedEmailCronEC = genFixedThreadPoolEC(5)

      unsnoozeSnoozedEmailCron.start()(system = system, ec = unSnoozeSnoozedEmailCronEC, wsClient = wSClient)
    }
    {
      val stopInactiveCampaignCronEC = genFixedThreadPoolEC(threads = SRAppConfig.StopInactiveCampaign.stop_inactive_campaign_thread_count)

      stopInactiveCampaignCron.start()(
        system = system,
        ec = stopInactiveCampaignCronEC,
        wsClient = wSClient
      )
    }
    {
      val senderRunAlertCronEC = genFixedThreadPoolEC(threads = 3)

      emailStuckForSendingAlertCron.start()(
        system = system,
        ec = senderRunAlertCronEC,
        wsClient = wSClient
      )
    }
    {
      val dataIntegrationCheckCronEC = genFixedThreadPoolEC(1)

      dataIntegrationCheckCron.start()(
        system = system,
        ec = dataIntegrationCheckCronEC,
        wsClient = wSClient
      )
    }
    {
      val linkedinSettingDeletionCronEC = genFixedThreadPoolEC(1)

      linkedinSettingDeletionCron.start()(
        system = system,
        ec = linkedinSettingDeletionCronEC,
        wsClient = wSClient
      )
    }
    {
      val internalLeadFinderDataUpdateCronEC= genFixedThreadPoolEC(1)

      internalLeadFinderDataUpdateCron.start()(
        system = system,
        ec = internalLeadFinderDataUpdateCronEC,
        wsClient = wSClient
      )
    }
  }
  //22-Jan-2024 SCYLLA_COMMENTED_OUT
//  override val scyllaCluster: Cluster = scyllaDbConnection.initialize()

}
