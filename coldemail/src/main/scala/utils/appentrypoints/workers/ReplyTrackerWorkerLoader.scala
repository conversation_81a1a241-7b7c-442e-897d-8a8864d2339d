package utils.appentrypoints.workers

import api.accounts.email.models.ReplyTrackerName
import common_auth_middleware.UserRedisKeyService_DI
import org.apache.pekko.actor.ActorSystem
import play.api.Logging
import play.api.libs.ws.WSClient
import utils.Helpers
import utils.dependencyinjectionutils.ChannelSchedulers.{EmailMessageDataDAO_DI, EmailScheduledDAOService_DI, EmailSchedulerJedisService_DI, SrRedisHashSetBasedLockServiceV2_DI, SrRedisHashSetServiceV2_DI, SrRedisSimpleLockServiceV2_DI}
import utils.dependencyinjectionutils.*
import utils.dependencyinjectionutils.LinkedinSettingDI.MqCaptainDataCookieFailurePublisherDI

import scala.concurrent.ExecutionContext
//import utils.dependencyinjectionutils.EventFramework.{KafkaServiceDI, SrEventServiceDI}
import utils.dependencyinjectionutils.LinkedinSettingDI.{LinkedinSettingServiceDI, PlanLimitServiceDI}
//import utils.dependencyinjectionutils.LinkedinSettingDI.LinkedinSettingServiceDI
import utils.dependencyinjectionutils.ProductOnboardingDI.RollingUpdate.SrRollingUpdateCoreService_DI
import utils.dependencyinjectionutils.Tasks.{TaskDAO_DI, TaskDaoServiceDI, TaskServiceDI}
import utils.dependencyinjectionutils.Team._
import utils.dependencyinjectionutils.Template.TemplateUtilsDI
import utils.helpers.LogHelpers

import scala.util.{Failure, Success}

class ReplyTrackerWorkerLoader
  extends MQReplyTrackerDI
    with MQReplyTrackerPublisher_DI
//    with EmailValidationDAOService_DI
    with CampaignProspectService2DI
    with PhantomBusterAgentServiceDI
    with PhantomBusterAgentsDAODI
    with CampaignProspectStepScheduleLogsDAO_DI
    //    with MQProspectDeleterDI
    with PlanLimitServiceDI
    with TeamsMetadataServiceDI
    with SrDateTimeUtils_DI
    with MQCampaignAISequenceGeneratorDI
    with SrMailServerServiceDI
    with SrMailServerDao_DI
    with SrMailServerRedisService_DI
    with GPTServiceDI
    with AISequenceGeneratorDI
    with AccountDAOV2_DI
    with Campaign.CampaignStepServiceDI
    with GptApiService_DI
    with LlmAuditLogDAO_DI
    with GPTApiDI
    with UserCheckApiServiceDI
    with MQOpenTrackerPublisher_DI
    with PusherServiceDI
    with SrRateLimiterDI
    with SrRateLimiterDAO_DI
    with MqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC_DI
    with BlacklistServiceV2_DI
    with TemplateUtilsDI
    with TemplateDAO_DI
    with InboxV3ServiceDI
    with LinkedinMessagesDAOService_DI
    with CampaignTemplateServiceDI
    with ProspectAccountDAO1DI
    with CreateInCRMJedisDAO_DI
    with EmailSettingTagsJedisServiceDI
    with LinkedinMessagesDAODI
    with TeamInboxService_DI
    with LinkedinTeamInboxDAO_DI
    with LinkedinMessageThreadsServiceDI
    with LinkedinConnectionsServiceDI
    with LinkedinConnectionDAODI
    with LinkedinSettingServiceDI
    with CaptainDataServiceDI
    with MqCaptainDataConversationExtractionPublisher_DI
    with MqCaptainDataCookieFailurePublisherDI
    with MqCaptainDataMessageExtractionPublisher_DI
    with LinkedinMessagesServiceDI
    with BrightDataServiceDI
    with BrightDataApiDI
    with SrRedisSimpleLockServiceV2_DI
    with PhantomBusterProxyServiceDI
    with TeamInboxDAO_DI
    with PhantomBusterProxyDAO_DI
    with OrganizationBillingDAO_DI
    with DomainPublicDNSDAO_DI
    with MQDomainServiceProviderDNSService_DI
    with SrDNSUtil_DI
    with TeamsMetadataDAODI
//    with EmailValidationBatchRequestModelDI
    with SrUuidUtilsDI
//    with ProspectTagServiceDI
    with AccountOrgBillingRelatedServiceDI
    with AccountOrgBillingRelatedInfoDAODI
    with EmailMessageDataDAO_DI
//    with BrightDataServiceDI
//    with BrightDataApiDI
    with EmailScheduledDAOService_DI
//    with ProspectTagDAO_DI
//    with DeBounceEmailValidationApiDI
//    with ListCleanEmailValidationApiDI
//    with CloudStorageDI
    with PhantomBusterApiKeysServiceDI
    with PhantomBusterApiKeysDAODI
//    with LinkedinMessagesDAODI
    with LinkedinMessageThreadsDAODI
    with LinkedinTaskServiceDI
//    with EmailValidationServiceDI
    with CampaignSchedulingMetadataDAO_DI
    with PhantomBusterApiDI
    with CaptainDataApiDI
    with GeneralSettingServiceDI
//    with ChannelSchedulers.EmailChannelSchedulerDI
//    with ChannelSchedulers.GeneralChannelSchedularDI
    with DeleteService_DI
    with MqEmailAccountDeleterDI
    with EmailSettingService_DI
    with EmailServiceDI
//    with EmailServiceCompanionDI
    with EmailSettingDAOServiceDI
    with EmailHandleErrorServiceDI
    with EmailSettingJedisServiceDI
    with OutlookApiSendEmailServiceDI
    with OutlookApiReplyTrackingServiceDI
//    with CampaignTemplateServiceDI
    with OrgMetadataServiceDI
    with GeneralSettingDAO_DI
    with TaskServiceDI
    with TaskDaoServiceDI
    with SrRandomUtilsDI
    with CampaignSendReportsDAO_DI
//    with SrShuffleUtilsDI
    with CampaignProspectTimezonesJedisServiceDI
//    with TaskCacheDAO_DI
//    with TaskCacheService_DI
    with TaskDAO_DI
//    with SrEventServiceDI
//    with KafkaServiceDI

//    with SrRedisHashSetBasedLockServiceV2_DI
//    with SrRedisSimpleLockServiceV2_DI
//    with SrRedisHashSetServiceV2_DI
//    with EmailSchedulerJedisService_DI
    with RepTrackingHostServiceDI

    with GmailEmailApiDI
    with GmailApiReplyTrackingServiceDI
    with GmailEmailSendServiceDI
    with GmailSmtpEmailApiDI
    with OutlookEmailApiDI
    with MailgunServiceDI
    with MailgunEmailApiDI
    with SendGridServiceDI
    with SendGridEmailApiDI
//    with SmtpImapSendEmailServiceDI
    with SmtpImapReplyTrackingServiceDI
//    with SmtpImapEmailApiDI
//    with BouncerEmailValidationApiDI
    with ReplyFilteringServiceDI
    with TemplateServiceDI
    with MQUnsubscriberTrackerDI
    with MQDoNotContactDI
    with MQDoNotContactPublisher_DI
    with WebhookDAO_DI
    with WebhookUtils_V2_DI
    with ProspectQueryDI
    with GoogleOAuthDI
    with MicrosoftOAuthDI
    with AccountDAO_DI
    with SrUserFeatureUsageEventServiceDI
//    with CampaignsMissingMergeTagServiceDI

    with BlacklistServiceDI
    with BlacklistDAO_DI
    with CampaignDAOService_DI
//    with MQEmailSchedulerV2DI
    with MQWebhookCompletedDI
//    with MQTriggerDI

    with MQWebhookEmailInvalidDI
//    with MQMBLEmailValidatorDI

    with ProspectAddEventDAO_DI
    with CampaignProspectDAO_DI

    with DBCounterDAO_DI
    with SrDBQueryCounterService_DI
    with CampaignDAO_DI
    with EmailScheduledDAO_DI
    with EmailScheduledServiceDI
    with EmailSettingDAO_DI
    with EmailThreadDAO_DI
    with CampaignStepVariantDAO_DI
    with PhishingCheckServiceDI
    with EmailReplyTrackingModelDI
    with TriggerDAO_DI
//    with TriggerServiceDI
    with HandlePushTriggerEventServiceDI
    with HandleActivityTriggerEventServiceDI
    with LeadStatusServiceDI
    with ActivityTriggerMQServiceDI
    with MQActivityTriggerPublisher_DI

    with AgencyDashboardCacheServiceDI
    with CacheServiceJedisDI
    with AccountServiceDI
//    with SrResourceDI.SrResourceDaoServiceDI
            with SrResourceDI.SrResourceWithoutTenantDaoServiceDI
    with SrResourceDI.SrCacheDI
    with SrUuidInternalDataServiceDI
    with CampaignServiceDI
    with InternalSchedulerRunLogDAO_DI
    with CampaignSendingVolumeLogsDAO_DI
    with CampaignCacheServiceDI
//    with ProspectServiceDI
    with WorkerActorSystemDI
    with WorkerActorMaterializerDI
    with WorkerWSClientDI
    with EmailValidationModelDI
//    with EmailValidationApiToolsRecordDAODI
//    with TransformAndSaveEmailValidationResultDI
//    with EmailDeliveryAnalysisServiceDI
//    with ProspectAccountDAO1DI
    with CampaignStepDAO_DI
    with ProspectUpdateCategoryTemp_DI
    with EmailNotificationServiceDI
    with EmailNotificationDAO_DI
    with ProspectUpdateCategoryTemp2_DI
    with FreeEmailDomainListService_DI
    with FreeEmailDomainListDAO_DI

    with EventLogDAO_DI
    with EventLogService_DI
    with EventLogFindServiceDI
    with WorkflowAttemptDAO_DI
    with WorkFlowAttemptService_DI
    with AccessTokenService_DI
    with ClientAccountAccessLogDAO_DI
    with UserRedisKeyService_DI
//    with CampaignEditedPreviewEmailDAO_DI
    with Logging
//    with MqCampaignSchedulingMetadataMigration_DI
    with TagServiceDI
//    with ProspectUploadServiceDI
//    with SRCsvParserUtilsDI
//    with CustomCSVParserDI
//    with UnivocityParserDI
//    with SRCsvReaderUtilsDI
    with ProspectColumnDef_DI
    with ProspectColumnDefDAO_DI
    with EmailReplyTrackingModelV2DI
    with MqSrAiApiPublisher_DI
    with CampaignProspectServiceDI
    with DripLogJedisDAO_DI
    with ProspectsEmailsDAO_DI
    with ProspectServiceV2_DI
    with ResetUserCacheUtilDI
    with AccountDAOService_DI
    with CacheServiceDI
    with ReplyTrackerService_DI
    with EmailMessageContactModel_DI
    with TeamsDAO_DI
    with OrganizationDAO_DI
    with DBUtils_DI
    with EmailSendingStatusDAO_DI
    with SrInternalFeatureUsageDaoDI
//    with BlacklistProspectCheckDAO_DI
    with CampaignProspectDAO_2DI
//    with GenerateTempId_DI
//    with CacheEventScyllaService_DI
//    with CacheEventScyllaDAO_DI
//    with ScyllaRunSafely_DI
//    with ScyllaDbConnection_DI
    with SupportAccessToUserAccountDAO_DI
//    with MqHandleEventLogDI
    with EmailReplyTrackingDAOService_DI
    with ReplySentimentService_DI
    with ReplySentimentDAO_DI
    with ReplySentimentJedisDAO_DI
    with MergeTagService_DI
    with ProspectDAOService_DI
    with ProspectDAOServiceV2_DI
    with CampaignProspectDAOService_DI
    with OrganizationDAOService_DI
    with HubspotApiDI
    with HubSpotOAuth_DI
    with PipedriveOAuth_DI
    with SalesforceOAuth_DI
    with ZohoOAuth_DI
    with ZohoApiDI
    with ZohoRecruitOAuth_DI
    with SalesForceApiDI
    with PipedriveApiDI
    with IntegrationTypeService_DI
    with SRTriggerAllowedCombos_DI
    with TriggerServiceV2_DI
    with TriggerJedisDAO_DI
    with TriggerDAOService_DI
    with TIntegrationCRMService_DI
    with ProspectDAO_DI
    with ReplySentimentDAOService_DI
    with AssociateProspectToEmailThreadService_DI
//    with MQAssociateProspectToOldEmails_DI
//    with InboxV3ServiceDI
    with TeamServiceDI
    with TeamDAOService_DI
    with TeamSRAIFlagsJedisDAO_DI
    with SrUuidServiceDI
    with EmailThreadDAOService_DI
    with InboxV3DAOService_DI
    with OrgMetadataDAO_DI
    with Campaign.ChannelSettingServiceDI
    with CallDAO_DI
    with CampaignEmailSettingsDAO_DI
    with ProspectTagDAOLegacy_DI
    with EmailsScheduledDeleteService_DI
    with SelectAndPublishForDeletionServiceDI
    with OpportunitiesDefaultSetupService_DI
    with PipelineDAO_DI
    with OpportunityStatusDAO_DI
//    with CalendarAppServiceDI
//    with CalendarAppDaoDI
//    with CalendarAppApiDI
    with TeamsPipelineConfigDAO_DI
    with TeamsPipelineConfigService_DI
    with WhatsappSettingDI.WhatsappSettingDAODI
    with SmsSettingDI.SmsSettingDAODI
    with OrganizationService_DI
//    with TeamInboxService_DI
//    with LinkedinMessageThreadsServiceDI
//    with LinkedinTeamInboxDAO_DI
//    with LinkedinSettingServiceDI
//    with TeamInboxDAO_DI
    with LinkedinSettingDAO_DI
//    with PhantomBusterProxyServiceDI
//    with PhantomBusterProxyDAO_DI
    with OrganizationJedisCacheDao_DI
//    with SrDateTimeUtils_DI
    with TeamsListingPaginationService_DI
    with SrRollingUpdateCoreService_DI
//    with MqDeletionAndRevertV2DI
    with MqDeletionAndRevertV2Publisher_DI
    with SchedulerIntegrityServiceDI
    with SchedulerIntegrityReportDaoDI
//    with MqSchedulerIntegrityCheckDI
    with InboxPlacementCheckDAO_DI
    with ProspectCategoryServiceDI
    with OutlookUtilsService_DI
    with CampaignStepDaoServiceDI
    with MqPublisherHandleEventLogDI
    with ProspectServiceDI
    with BlacklistProspectCheckDAO_DI
    with EmailValidationServiceDI
    with GenerateTempId_DI
    with MQAssociateProspectToOldEmails_DI
    with ProspectEmailsDAOService_DI
    with MQTriggerDI
    with PotentialDuplicateProspectService_DI
    with BouncerEmailValidationApiDI
    with CloudStorageDI
    with DeBounceEmailValidationApiDI
    with EmailValidationApiToolsRecordDAODI
    with EmailValidationBatchRequestModelDI
    with EmailValidationDAOService_DI
    with ListCleanEmailValidationApiDI
    with TransformAndSaveEmailValidationResultDI
    with PotentialDuplicateProspectsDAO_DI
    with EmailDeliveryAnalysisServiceDI
    with LeadFinderValidationService_DI
    with OpportunityDAO_DI
    with ProspectEventDAO_DI
    with LeadFinderDAO_DI
    with EmailBodyServiceDI
    with MqHandleAttemptCreationPublisherDI
    with MqForSaveReplySentimentPublisher_DI
    with MqPauseCampaignOnReplySentimentSelectDI
    with MqExtractLinkedinConnectionResultsDI
    with MqDeleteLinkedinSettingDI
    with AccountCreateServiceDI
    with EmailCommonServiceDI
    with SrAiLockJedisDAO_DI
    with SrAiApi_DI
    with ContentGenerationService_DI
    with MqAutoUpdateProspectCategoryPublisherDI
    with CampaignGenerationService_DI
    {
      def executionContext(threads: Int = 100): ExecutionContext = {
        Helpers.genFixedThreadPoolEC(threads = threads)
      }
  def start() = {


    startForReplyTrackerName(ReplyTrackerName.GmailApi)(
      ws = wSClient,
      system = workerActorSystem,
      ec = executionContext()
    )
    startForReplyTrackerName(ReplyTrackerName.GmailAsp)(
      ws = wSClient,
      system = workerActorSystem,
      ec = executionContext()
    )
    startForReplyTrackerName(ReplyTrackerName.Outlook)(
      ws = wSClient,
      system = workerActorSystem,
      ec = executionContext()
    )
    startForReplyTrackerName(ReplyTrackerName.OtherMailDoso)(
      ws = wSClient,
      system = workerActorSystem,
      ec = executionContext()
    )
    startForReplyTrackerName(ReplyTrackerName.OtherZapmail)(
      ws = wSClient,
      system = workerActorSystem,
      ec = executionContext()
    )
    startForReplyTrackerName(ReplyTrackerName.OtherUser)(
      ws = wSClient,
      system = workerActorSystem,
      ec = executionContext(threads = 500)
    )


  }
  //22-Jan-2024 SCYLLA_COMMENTED_OUT
//  override val scyllaCluster: Cluster = scyllaDbConnection.initialize()


      def startForReplyTrackerName(replyTrackerName: ReplyTrackerName)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Unit = {
        mqReplyTracker.startConsumer(replyTrackerName)(
          ws = wSClient,
          system = workerActorSystem,
          ec = ec
        ) match {

          case Failure(e) =>
            logger.error(s"[ReplyTrackerWorkerLoader] FATAL error while starting: ${replyTrackerName.toString}" + LogHelpers.getStackTraceAsString(e))

            wSClient.close()
            workerActorMaterializer.shutdown()

          case Success(startedStr) =>
            logger.info(s"[ReplyTrackerWorkerLoader] started successfully: ${replyTrackerName.toString}" + startedStr)
        }
      }
}
