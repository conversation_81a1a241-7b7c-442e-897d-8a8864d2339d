package utils.appentrypoints.workers

import common_auth_middleware.UserRedisKeyService_DI
import org.apache.pekko.actor.Cancellable
import utils.Helpers
import utils.dependencyinjectionutils.ChannelSchedulers.{EmailMessageDataDAO_DI, EmailScheduledDAOService_DI, EmailSchedulerJedisService_DI, SrRedisHashSetBasedLockServiceV2_DI, SrRedisHashSetServiceV2_DI, SrRedisSimpleLockServiceV2_DI}
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import utils.dependencyinjectionutils.Calendar.{CalendarAppApiDI, CalendarAppDaoDI, CalendarAppServiceDI}
import utils.dependencyinjectionutils.*
import utils.dependencyinjectionutils.LinkedinSettingDI.MqCaptainDataCookieFailurePublisherDI
import utils.dependencyinjectionutils.Tasks.{TaskDAO_DI, TaskDaoServiceDI, TaskServiceDI}
//import utils.dependencyinjectionutils.EventFramework.{KafkaServiceDI, SrEventServiceDI}
import utils.dependencyinjectionutils.LinkedinSettingDI.{LinkedinSettingServiceDI, PlanLimitServiceDI}
//import utils.dependencyinjectionutils.LinkedinSettingDI.LinkedinSettingServiceDI
import utils.dependencyinjectionutils.ProductOnboardingDI.RollingUpdate.SrRollingUpdateCoreService_DI
import utils.dependencyinjectionutils.Team._
import utils.dependencyinjectionutils.Template.TemplateUtilsDI
import utils.mq.services.MQConfig

import scala.util.{Failure, Success, Try}

class EmailSchedulerWorkerLoader
  extends MQEmailSchedulerV2DI
    with SrUuidUtilsDI
    with CampaignProspectStepScheduleLogsDAO_DI
    with EmailValidationDAOService_DI
    with MQWebhookCompletedDI
    //with PhantomBusterAgentServiceDI
    with AISequenceGeneratorDI
    with PlanLimitServiceDI
    //with PhantomBusterAgentsDAODI
    with MQCampaignAISequenceGeneratorDI
    with MqAiContentGenerationPublisher_DI
    with GPTServiceDI
    with SrMailServerServiceDI
    with SrMailServerDao_DI
    with SrMailServerRedisService_DI
    with Campaign.CampaignStepServiceDI
    with GPTApiDI
    with MqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC_DI
    with AIHyperPersonalizedGeneratorDI
    with BlacklistServiceV2_DI
    with GptApiService_DI
    with LlmAuditLogDAO_DI
    with PusherServiceDI
    with SrRateLimiterDI
    with AccountDAOV2_DI
    with UserCheckApiServiceDI
    with CreateInCRMJedisDAO_DI
    with SrRateLimiterDAO_DI
    with TemplateUtilsDI
    with TemplateDAO_DI
    with DomainPublicDNSDAO_DI
    with MQDomainServiceProviderDNSService_DI
    with SrDNSUtil_DI
    with OrganizationBillingDAO_DI
    with EmailMessageDataDAO_DI
    with EmailScheduledDAOService_DI
    with EmailScheduledServiceDI
    with MQTriggerDI
    with TeamsMetadataServiceDI
    with TeamsMetadataDAODI
    with MQProspectDeleterDI
    with BrightDataServiceDI
    with BrightDataApiDI
    with AccountOrgBillingRelatedServiceDI
    with AccountOrgBillingRelatedInfoDAODI
    with EmailSettingTagsJedisServiceDI
    //with PhantomBusterApiKeysServiceDI
    //with PhantomBusterApiKeysDAODI
    with LinkedinMessagesDAODI
    with LinkedinMessageThreadsDAODI
    with DeBounceEmailValidationApiDI
    with ListCleanEmailValidationApiDI
    with CloudStorageDI
    with SrUuidServiceDI
    with MqCampaignSchedulingMetadataMigration_DI

//    with MQMBLEmailValidatorDI
    with AccountDAO_DI
    with SrUserFeatureUsageEventServiceDI
    with LinkedinTaskServiceDI
    with CampaignSchedulingMetadataDAO_DI
    with GeneralSettingServiceDI
    with TaskServiceDI
    // with PhantomBusterApiDI
    with CaptainDataApiDI
    with TaskDaoServiceDI
    with SrRandomUtilsDI
    with EmailSettingDAOServiceDI
    with EmailSettingJedisServiceDI
    with OrgMetadataServiceDI
    with SrShuffleUtilsDI
    with CampaignSendReportsDAO_DI
//    with TaskCacheDAO_DI
    with CampaignProspectTimezonesJedisServiceDI
//    with TaskCacheService_DI
    with TaskDAO_DI
//    with SrEventServiceDI
//    with KafkaServiceDI
    with CampaignProspectService2DI
    with RepTrackingHostServiceDI

//    with MQOpenTrackerDI
    with MQWebhookEmailInvalidDI
    with HandlePushTriggerEventServiceDI
    with HandleActivityTriggerEventServiceDI
    with LeadStatusServiceDI
    with ActivityTriggerMQServiceDI
    with GeneralSettingDAO_DI
    with TriggerDAO_DI
    with TriggerServiceDI
    with WebhookDAO_DI
    with WebhookUtils_V2_DI
    with ProspectQueryDI
    with TemplateServiceDI
    with CacheServiceDI
    with CampaignProspectServiceDI
    with DripLogJedisDAO_DI
    with CampaignServiceDI
    with InternalSchedulerRunLogDAO_DI
    with CampaignSendingVolumeLogsDAO_DI
    with CampaignCacheServiceDI

    with SrRedisHashSetBasedLockServiceV2_DI
    with SrRedisSimpleLockServiceV2_DI
    with SrRedisHashSetServiceV2_DI
    with EmailSchedulerJedisService_DI

    with ChannelSchedulers.EmailChannelSchedulerDI
    with ChannelSchedulers.GeneralChannelSchedularDI
    with EmailValidationServiceDI
    with EmailServiceCompanionDI
    with BouncerEmailValidationApiDI

    with ProspectAddEventDAO_DI
    with CampaignProspectDAO_DI
    with EmailScheduledDAO_DI
    with EmailSettingDAO_DI
    with DBCounterDAO_DI
    with SrDBQueryCounterService_DI
    with CampaignDAO_DI
    with EmailThreadDAO_DI
    with CampaignStepVariantDAO_DI
    with CampaignsMissingMergeTagServiceDI

    with AgencyDashboardCacheServiceDI
    with CacheServiceJedisDI
    with AccountServiceDI
    with SrResourceDI.SrResourceDaoServiceDI
            with SrResourceDI.SrResourceWithoutTenantDaoServiceDI
    with SrResourceDI.SrCacheDI
    with SrUuidInternalDataServiceDI
    with PhishingCheckServiceDI
    with ProspectServiceDI
    with EmailNotificationServiceDI
    with EmailNotificationDAO_DI
    with MailgunServiceDI
    with MailgunEmailApiDI
    with WorkerActorSystemDI
    with WorkerWSClientDI
    with WorkerActorMaterializerDI
    //
    with EmailValidationBatchRequestModelDI
    with EmailValidationModelDI
    with EmailValidationApiToolsRecordDAODI
    with TransformAndSaveEmailValidationResultDI
    with EmailDeliveryAnalysisServiceDI
    with ProspectAccountDAO1DI
    with CampaignStepDAO_DI
    with CampaignDAOService_DI
    //
    with FreeEmailDomainListService_DI
    with FreeEmailDomainListDAO_DI
    with EventLogDAO_DI
    with EventLogService_DI
    with EventLogFindServiceDI
    with WorkflowAttemptDAO_DI
    with WorkFlowAttemptService_DI
    with AccessTokenService_DI
    with ClientAccountAccessLogDAO_DI
    with UserRedisKeyService_DI
    with CampaignEditedPreviewEmailDAO_DI
    with TagServiceDI
    with ProspectUploadServiceDI
    with SRCsvParserUtilsDI
    with CustomCSVParserDI
    with UnivocityParserDI
    with SRCsvReaderUtilsDI
    with ProspectColumnDef_DI
    with ProspectColumnDefDAO_DI
    with ProspectsEmailsDAO_DI
    with ProspectServiceV2_DI
    with ResetUserCacheUtilDI
    with AccountDAOService_DI
    with EmailMessageContactModel_DI
    with TeamsDAO_DI
    with OrganizationDAO_DI
    with DBUtils_DI
    with EmailSendingStatusDAO_DI
    with SrInternalFeatureUsageDaoDI
    with BlacklistProspectCheckDAO_DI
    with CampaignProspectDAO_2DI
    with GenerateTempId_DI
    with DeleteService_DI
    with MqEmailAccountDeleterDI
    with EmailSettingService_DI
    with OutlookApiSendEmailServiceDI
    with OutlookApiReplyTrackingServiceDI
    with GmailEmailApiDI
    with GmailApiReplyTrackingServiceDI
    with GmailApiSendEmailServiceDI
    with CampaignTemplateServiceDI
    with ReplyFilteringServiceDI
    with EmailReplyTrackingModelV2DI
    with MqSrAiApiPublisher_DI
    with SendGridServiceDI
    with SendGridEmailApiDI
    with EmailHandleErrorServiceDI
    with SmtpImapSendEmailServiceDI
    with SmtpImapEmailApiDI
    with ProspectUpdateCategoryTemp_DI
    with ProspectUpdateCategoryTemp2_DI
    with BlacklistDAO_DI
    with GmailEmailSendServiceDI
    with GmailSmtpEmailApiDI
    with OutlookEmailApiDI
    with EmailReplyTrackingModelDI
//    with CacheEventScyllaDAO_DI
//    with CacheEventScyllaService_DI
    with GoogleOAuthDI
    with MicrosoftOAuthDI
    with MQUnsubscriberTrackerDI
//    with ScyllaRunSafely_DI
    with MQDoNotContactDI
    with MQDoNotContactPublisher_DI
    with BlacklistServiceDI
//    with ScyllaDbConnection_DI
//    with ScyllaCluster_DI
    with SupportAccessToUserAccountDAO_DI
//    with MqHandleEventLogDI
    with EmailReplyTrackingDAOService_DI
    with ReplySentimentService_DI 
    with ReplySentimentDAO_DI
    with ReplySentimentJedisDAO_DI
    with MergeTagService_DI
    with ProspectDAOService_DI
    with ProspectDAOServiceV2_DI
    with CampaignProspectDAOService_DI
    with OrganizationDAOService_DI
    with HubspotApiDI
    with HubSpotOAuth_DI
    with PipedriveOAuth_DI
    with SalesforceOAuth_DI
    with ZohoOAuth_DI
    with ZohoApiDI
    with ZohoRecruitOAuth_DI
    with SalesForceApiDI
    with PipedriveApiDI
    with IntegrationTypeService_DI
    with SRTriggerAllowedCombos_DI
    with TriggerServiceV2_DI
    with TriggerJedisDAO_DI
    with TriggerDAOService_DI
    with TIntegrationCRMService_DI
    with ProspectDAO_DI
    with ReplySentimentDAOService_DI
    with AssociateProspectToEmailThreadService_DI
    with MQAssociateProspectToOldEmails_DI
    with TeamServiceDI
    with TeamDAOService_DI
    with TeamSRAIFlagsJedisDAO_DI
    with EmailThreadDAOService_DI
    with InboxV3DAOService_DI
    with OrgMetadataDAO_DI
    with Campaign.ChannelSettingServiceDI
    with CallDAO_DI
    with CampaignEmailSettingsDAO_DI
    with ProspectTagDAOLegacy_DI
    with EmailsScheduledDeleteService_DI
    with SelectAndPublishForDeletionServiceDI
    with OpportunitiesDefaultSetupService_DI
    with PipelineDAO_DI
    with OpportunityStatusDAO_DI
    with CalendarAppServiceDI
    with CalendarAppDaoDI
    with CalendarAppApiDI

    with TeamsPipelineConfigDAO_DI
    with TeamsPipelineConfigService_DI
    with WhatsappSettingDI.WhatsappSettingDAODI
    with SmsSettingDI.SmsSettingDAODI
    with OrganizationService_DI
    with LinkedinMessageThreadsServiceDI
    with LinkedinConnectionsServiceDI
    with LinkedinConnectionDAODI
    with LinkedinSettingServiceDI
    with CaptainDataServiceDI
    with MqCaptainDataConversationExtractionPublisher_DI
    with MqCaptainDataCookieFailurePublisherDI
    with MqCaptainDataMessageExtractionPublisher_DI
    with LinkedinMessagesServiceDI
    with LinkedinSettingDAO_DI
    with PhantomBusterProxyServiceDI
    with PhantomBusterProxyDAO_DI
    with OrganizationJedisCacheDao_DI
    with SrDateTimeUtils_DI
    with TeamsListingPaginationService_DI
    with SrRollingUpdateCoreService_DI
//    with MqDeletionAndRevertV2DI
    with MqDeletionAndRevertV2Publisher_DI
    with SchedulerIntegrityServiceDI
    with SchedulerIntegrityReportDaoDI
    with CampaignStepDaoServiceDI
    with InboxPlacementCheckDAO_DI
    with OutlookUtilsService_DI
    with PotentialDuplicateProspectService_DI
    with PotentialDuplicateProspectsDAO_DI
    with MqPublisherHandleEventLogDI
    with ProspectCategoryServiceDI
    with MqSchedulerIntegrityCheckDI
    with LeadFinderValidationService_DI
    with MQOpenTrackerPublisher_DI
    with OpportunityDAO_DI
    with ProspectEventDAO_DI
    with EmailSenderServiceDI
    with PushToRabbitMqCronServiceDI
    with PushToRabbitMqCronServiceV2DI
    with MQEmailPublisher_DI
    with MQActivityTriggerPublisher_DI
    with ProspectEmailsDAOService_DI
    with LeadFinderDAO_DI
    with EmailBodyServiceDI
    with MqHandleAttemptCreationPublisherDI
    with MqForSaveReplySentimentPublisher_DI
    with MqPauseCampaignOnReplySentimentSelectDI
    with MqExtractLinkedinConnectionResultsDI
    with MqDeleteLinkedinSettingDI
    with TeamInboxDAO_DI
    with AccountCreateServiceDI
    with EmailCommonServiceDI
    with SrAiLockJedisDAO_DI
    with SrAiApi_DI
    with ContentGenerationService_DI
    with MqAutoUpdateProspectCategoryPublisherDI
    with CampaignGenerationService_DI {


  val system = workerActorSystem

  //val emailSchedulerEC = Helpers.genFixedThreadPoolEC(MQConfig.emailChannelSchedulerPrefetchCount * 2)
  val emailSchedulerEC = Helpers.genFixedThreadPoolEC(12)

  def start(): Try[Cancellable] = Try {
    mqEmailSchedulerV2.startConsumer()(ws = wSClient, system = workerActorSystem, ec = emailSchedulerEC)
    match {

      case Failure(e) =>
        wSClient.close()
        workerActorMaterializer.shutdown()
        throw e

      case Success(startedStr) =>
        startedStr
    }


    {
      //val pushToRabbitMqEC = Helpers.genFixedThreadPoolEC(25)
      val pushToRabbitMqEC = Helpers.genFixedThreadPoolEC(16)

      pushToRabbitMqCronService.start()(system = system, ec = pushToRabbitMqEC, wsClient = wSClient)
    }

    {
      //val pushToRabbitMqV2EC = Helpers.genFixedThreadPoolEC(25)

      val pushToRabbitMqV2EC = Helpers.genFixedThreadPoolEC(16)
      pushToRabbitMqCronServiceV2
        .start()(
          system = system,
          ec = pushToRabbitMqV2EC,
          wsClient = wSClient
        )
    }

  }
}
