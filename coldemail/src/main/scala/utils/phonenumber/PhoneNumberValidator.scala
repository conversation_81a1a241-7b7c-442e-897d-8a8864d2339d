package utils.phonenumber
import api.call.models.{PhoneNumber => PhoneNumberValueClass, SrPhoneNumber}
import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.google.i18n.phonenumbers.Phonenumber.PhoneNumber

import scala.util.{Failure, Success, Try}

object PhoneNumberValidator {
  private val phoneNumberUtil = PhoneNumberUtil.getInstance()

  def isValidPhoneNumber(phoneNumberStr: String): Boolean = Try {
      val phoneNumber: PhoneNumber = phoneNumberUtil.parse(phoneNumberStr, null)
      phoneNumberUtil.isValidNumber(phoneNumber)
    } match {
      case Success(value) => value
      case Failure(_) => false
    }

  def parseNumber(phoneNumberStr: String): Try[PhoneNumber] = Try {

    phoneNumberUtil.parse(phoneNumberStr, null)

  }

  //FIXME: USE COUNTRY AND REGION CODE FOR PARSING IF COUNTRY CODE IS NOT PASSED. 
  def parseSrPhoneNumber(phoneNumber: String): Try[SrPhoneNumber] = {

    parseNumber(phoneNumberStr = phoneNumber) match {

      case Failure(exception) => Failure(exception = exception)

      case Success(phone_number_java) => Try {


        val country_code: Option[Int] = if (phone_number_java.hasCountryCode) {
          Some(phone_number_java.getCountryCode)
        } else {
          None
        }
        val phoneNumber: PhoneNumberValueClass = PhoneNumberValueClass(phone_number = phone_number_java.getNationalNumber.toString)
        SrPhoneNumber(
          country_code = country_code,
          phone_number = phoneNumber
        )
      }
    }



  }


  }