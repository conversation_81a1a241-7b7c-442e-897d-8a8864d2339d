/* 10-Dec-2024: Removed Scylla code
package utils.scylladb.cql

import com.datastax.driver.core.{PreparedStatement, ResultSet, ResultSetFuture, Session}
import com.google.common.util.concurrent.{FutureCallback, Futures, ListenableFuture}

import java.util.concurrent.Executor
import scala.concurrent.{ExecutionContext, Future, Promise}

// reference for this entire object is https://www.beyondthelines.net/databases/querying-cassandra-from-scala/
object  CqlImplicits{

  /*
  for now we have used preparedStatementCache to store already prepared query,
  -we can use some different cache for this to prevent memory leak,
   now we only have hardly 6-7 cql queries so it should work fine
   https://stackoverflow.com/questions/18660769/best-practices-for-mixing-in-scala-concurrent-mapi
   */

  var preparedStatementCache: scala.collection.concurrent.TrieMap[Int, ListenableFuture[PreparedStatement]] = scala.collection.concurrent.TrieMap[Int, ListenableFuture[PreparedStatement]]()
  implicit class CqlStrings(val context: StringContext) extends AnyVal {
    def cql(args: Any*)(implicit session: Session, executionContext: ExecutionContext): Future[PreparedStatement] = {

      val stringQuery = context.raw(args*)
      val key = util.hashing.MurmurHash3.stringHash(stringQuery)
      val found = preparedStatementCache.get(key)
      if (found.isDefined) found.get
      else {
        val preparedStatement = session.prepareAsync(stringQuery)
        preparedStatementCache += (key -> preparedStatement)
        preparedStatement
      }


    }


  }


  //https://gist.github.com/ochrons/10681050 reference for this method
  implicit def resultSetFutureToScala(
                                       f: ResultSetFuture
                                     )(implicit ec: ExecutionContext): Future[ResultSet] = {
    //    val ec = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(25))
    //    implicit val es: ExecutorService = Executors.newFixedThreadPool(25)
    val p = Promise[ResultSet]()
    Futures.addCallback(f,
      new FutureCallback[ResultSet] {
        def onSuccess(r: ResultSet) = p success r

        def onFailure(t: Throwable) = p failure t
      }, new Executor {
        override def execute(command: Runnable): Unit = ec.execute(command)
      })
    p.future
  }

  def execute(statement: Future[PreparedStatement], params: Any*)(
    implicit executionContext: ExecutionContext, session: Session
  ): Future[ResultSet] = {
    val p = params.map(_.asInstanceOf[Object])
    statement
      .map(_.bind(p:_*))
      .flatMap((session.executeAsync(_)))
  }

  implicit def listenableFutureToFuture[T](
                                            listenableFuture: ListenableFuture[T]
                                          )(implicit ec: ExecutionContext): Future[T] = {
//    implicit val ecTeam: ExecutionContext = Helpers.genFixedThreadPoolEC(threads = 3)
    val promise = Promise[T]()
    Futures.addCallback(listenableFuture, new FutureCallback[T] {
      def onFailure(error: Throwable): Unit = {
        promise.failure(error)
        ()
      }

      def onSuccess(result: T): Unit = {
        promise.success(result)
        ()
      }
    }, new Executor {
      override def execute(command: Runnable): Unit = ec.execute(command)
    })
    promise.future
  }
}
*/
