// 22-Jan-2024 SCYLLA_COMMENTED_OUT
//package utils.scylladb
//
//import api.AppConfig
//import com.datastax.driver.core._
//import scala.jdk.CollectionConverters._
//import java.net.InetSocketAddress
//import com.datastax.driver.core.PoolingOptions
//
//class ScyllaDbConnection {
//
//  private var runningCluster: Option[Cluster] = None
//
//  private val scyllaVars = AppConfig.ScyllaService
//
//  def initialize():Cluster = {
//
//    runningCluster match {
//      case Some(cluster: Cluster) =>
//        cluster
//      case None =>
//        val poolingOptions = new PoolingOptions().setHeartbeatIntervalSeconds(60)
//          .setCoreConnectionsPerHost(HostDistance.LOCAL,  4)
//          .setMaxConnectionsPerHost( HostDistance.LOCAL, 10)
//          .setCoreConnectionsPerHost(HostDistance.REMOTE, 2)
//          .setMaxConnectionsPerHost( HostDistance.REMOTE, 4)
//
//        //https://java-driver.docs.scylladb.com/scylla-3.11.2.x/manual/pooling/
//        //ref - for pooling, a session is above pool in the hierarchy
//        //https://stackoverflow.com/questions/20421763/cassandra-datastax-driver-connection-pool
//        val cluster: Cluster = Cluster.builder()
//          .addContactPointsWithPorts(
//            getInetSocketAddresses().toList.asJava
//          ).withSSL()
//          .withPoolingOptions(poolingOptions)
//          .withAuthProvider(new PlainTextAuthProvider(scyllaVars.userName,scyllaVars.password))
//          .build()
//        runningCluster = Some(cluster)
//        cluster
//    }
//
//
//
//    // KeySpaces -> https://university.scylladb.com/courses/data-modeling/lessons/basic-data-modeling-2/topic/keyspace/#:~:text=A%20Keyspace%20is%20a%20top,strategy%20used%20by%20the%20Keyspace.
//    // contact points  - https://teddyma.gitbooks.io/learncassandra/content/client/which_node_to_connect.html
//    // AuthProvider -https://docs.datastax.com/en/developer/java-driver/4.2/manual/core/authentication/
//
////    CassandraConnector(conf).withSessionDo { session =>
////      session.execute("CREATE KEYSPACE test2 WITH REPLICATION = {'class': 'SimpleStrategy', 'replication_factor': 1 }")
////      session.execute("CREATE TABLE test2.words (word text PRIMARY KEY, count int)")
////    }
//  }
//
//  private def getInetSocketAddresses(): Array[InetSocketAddress] = {
//
//    val splitNodeWithPort = scyllaVars.nodeAndPorts.split(",")
//
//    splitNodeWithPort.map{nodeAndPort =>
//
//      val nodeAndPortSplit = nodeAndPort.split(":")
//
//      new InetSocketAddress(nodeAndPortSplit(0), nodeAndPortSplit(1).toInt)
//    }
//
//
//  }
//
//}
