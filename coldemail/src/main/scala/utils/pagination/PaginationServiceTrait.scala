package utils.pagination

import api.prospects.{ExactIdToCompareTime, InferredQueryTimeline, NavigationLinks}
import eventframework.PaginationSortedData
import utils.SRLogger


trait PaginationServiceTrait[T <: PaginationSortedData] {

  case class PaginationResponseObject(
                                       data: List[T],
                                       links: NavigationLinks
                                     )

  type GetSameTimeDataFromDbParamsType <: GetSameTimeDataFromDbParams

  final def computeExactlyAt(
                              timeline: InferredQueryTimeline.Range,
                              page_size: Int,
                              sortedData: List[T]
                            )(using Logger: SRLogger): Option[ExactIdToCompareTime] = {

    if (sortedData.size < page_size + 1) {
      None
    } else {
      timeline match {

        case InferredQueryTimeline.Range.Before(_) =>
          val lastObject = sortedData.last
          val penUltimate = sortedData.dropRight(1).last
          if (lastObject.getSortBy == penUltimate.getSortBy) {
            Some(ExactIdToCompareTime(lastObject.getExactlyAtIdOrUuid))
          } else None

        case InferredQueryTimeline.Range.After(_) =>
          val firstObject = sortedData.head
          val secondObject = sortedData.drop(1).head
          if (firstObject.getSortBy == secondObject.getSortBy) {
            Some(ExactIdToCompareTime(firstObject.getExactlyAtIdOrUuid))
          } else None
      }
    }
  }

  //This fn is called if there are at least 2 matching records at end/ which have the same timestamp
  def getSameTimeDataFromDb(
                            data: GetSameTimeDataFromDbParamsType,
                            exactIdToCompareTime: ExactIdToCompareTime
                           )(using logger: SRLogger): Either[GetDataErrorTrait, List[T]]


  /**
   * This fn is called only if sorted data >= page_size , only then the boundary value condition of equality triggers
   *
   * 1. It is given a set of records from the DB
   * 2. It will look at the page size and see if it has to construct a next and previous link
   * 3. If it has to construct a next/previous link - there can be an edge case where
   * the last record have the same timestamp. We pull pageSize+1 records and
   * it determines if the edge case is valid - it uses the trait function get same time records
   * to retrieve those and splice them into the result .
   */
  final def getFinalPaginationResponse(
                                        isFirst: Option[Boolean] = None,
                                        timeline: InferredQueryTimeline.Range,
                                        sortedData: List[T],
                                        limit: Int,
                                        dataToGetSameTimeRecords: GetSameTimeDataFromDbParamsType
                                      )(using logger: SRLogger): Either[GetDataErrorTrait, PaginationResponseObject] = {

    val hasMore = sortedData.size >= limit + 1

    if(sortedData.isEmpty){
      //we will never reach here as sorted data is always nonEmpty
      Right(
        PaginationResponseObject(
          data = sortedData,
          links = NavigationLinks(
            prev = None,
            next = None
          )))
    } else {
      if (!hasMore) {
        timeline match {

          case InferredQueryTimeline.Range.Before(_) =>

            val prev = if (isFirst.isDefined && isFirst.get) None else Some(sortedData.head.getSortBy)

            Right(PaginationResponseObject(
              data = sortedData,
              links = NavigationLinks(
                prev = prev,
                next = None
              )))


          case InferredQueryTimeline.Range.After(_) =>
            val next = if (isFirst.isDefined && isFirst.get) None else Some(sortedData.last.getSortBy)

            Right(PaginationResponseObject(data = sortedData,
              links = NavigationLinks(
                prev = None,
                next = next
              )))
        }
      } else {
        // when records returned >= page_size + 1
        // if there is a boundary case where 2 records have the exact dateTime
        // we need to handle that correctly using exactlyAt
        // if 2 boundary records don't have the same timestamp - we need not retrieve from the DB

        val exactlyAtRecordIdOpt: Option[ExactIdToCompareTime] = computeExactlyAt(
          timeline = timeline,
          page_size = limit,
          sortedData = sortedData
        )

        // now that we have computed the value of exactlyAt - which varies based on the direction
        // we are going in - we can make the call to get the duplicate records
        val atSameTimeDbResults: Option[Either[GetDataErrorTrait, List[T]]] = exactlyAtRecordIdOpt.map(
          exactlyAtRecordId =>
            getSameTimeDataFromDb(
              data = dataToGetSameTimeRecords,
              exactIdToCompareTime = exactlyAtRecordId
            )
        )


        val atSameTimeResults: Either[GetDataErrorTrait, Option[List[T]]] = atSameTimeDbResults match {
          case None => Right(None)
          case Some(res) => res match {
            case Left(e) => Left(e)
            case Right(resList) => Right(Some(resList))
          }
        }

        atSameTimeResults match {
          case Left(e) => Left(e)
          case Right(results) =>

            timeline match {

              case InferredQueryTimeline.Range.Before(_) =>

                results match {

                  case Some(atSameTimeObjects) =>

                    val lastObject = atSameTimeObjects.last
                    val finalObject = sortedData
                      .filterNot {
                        case (data) =>
                          data.getSortBy == lastObject.getSortBy
                      } ++ atSameTimeObjects

                    val prev = if (isFirst.isDefined && isFirst.get) None else Some(sortedData.head.getSortBy)

                    Right(PaginationResponseObject(
                      data = finalObject,
                      links = NavigationLinks(
                        prev = prev,
                        next = Some(lastObject.getSortBy)
                      )))

                  case None =>

                    //dropRight(1) will not throw an error since we are guaranteed to have at least 2 records
                    val penUltimate = sortedData.dropRight(1).last
                    val prev = if (isFirst.isDefined && isFirst.get) None else Some(sortedData.head.getSortBy)

                    Right(PaginationResponseObject(
                      data = sortedData.dropRight(1),
                      links = NavigationLinks(
                        prev = prev,
                        next = Some(penUltimate.getSortBy)
                      )))
                }

              case InferredQueryTimeline.Range.After(_) =>

                results match {

                  case Some(atSameTimeTasks) =>
                    val firstObject = sortedData.head

                    val finalObjects = atSameTimeTasks ++ sortedData.filterNot {
                      case (data) =>
                        data.getSortBy == firstObject.getSortBy
                    }

                    val prev = if (isFirst.isDefined && isFirst.get) None else Some(finalObjects.head.getSortBy)

                    Right(PaginationResponseObject(
                      data = finalObjects,
                      links = NavigationLinks(
                        prev = prev,
                        next = Some(finalObjects.last.getSortBy)
                      )))

                  case None =>

                    val finalObjects = sortedData.drop(1)
                    val prev = if (isFirst.isDefined && isFirst.get) None else Some(finalObjects.head.getSortBy)
                    val next = Some(finalObjects.last.getSortBy)

                    Right(PaginationResponseObject(data = finalObjects,
                      links = NavigationLinks(
                        prev = prev,
                        next = next
                      )))
                }
            }

        }

      }
    }
  }
}
