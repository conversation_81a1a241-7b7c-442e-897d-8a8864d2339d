package utils.pagination

import api.accounts.{Account, RepTrackingHosts, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.blacklist.models.BlacklistFindApiLevel
import api.emails.models.{InboxType, InboxTypeData}
import api.notes.models.GetNotesForm
import api.prospects.models.{ProspectAccountsId, ProspectId}
import api.prospects.{ExactIdToCompareTime, InferredQueryTimeline, ProspectAccount, ProspectEventV2, ValidatedConvReq}
import eventframework.ProspectObject
import api.search.SearchQuery
import utils.SRLogger

import scala.concurrent.ExecutionContext

sealed trait GetSameTimeDataFromDbParams

case class GetSameTimeDataEmailsAndLinkedIn(
                                             getLinkedinConversations: Boolean,
                                             validatedConvReq: ValidatedConvReq.ValidatedMailboxReqRange,
                                             team_id: Long
                                           ) extends GetSameTimeDataFromDbParams

case class GetSameTimeDataProspectsListing(
                                            teamId: Long,
                                            limit: Int,
                                            campaignId: Option[Long],
                                            findActiveCampaignsIfInternalRequest : Boolean,
                                            Logger: SRLogger
                                          ) extends GetSameTimeDataFromDbParams

case class GetSameTimeDataBlacklistListing(
                                            level: BlacklistFindApiLevel,
                                            limit: Int,
                                            Logger: SRLogger
                                          ) extends GetSameTimeDataFromDbParams

case class GetSameTimeDataEmailSetting(
                                        accountIds: Seq[AccountId],
                                        teamIds: Seq[TeamId],
                                        limit: Int,
                                        Logger: SRLogger
                                      ) extends GetSameTimeDataFromDbParams

case class GetSameTimeDataTeamsListing(
                                        accountId: AccountId,
                                        active: Option[Boolean],
                                        limit: Int,
                                        Logger: SRLogger
                                      ) extends GetSameTimeDataFromDbParams

case class GetSameTimeDataUsersListing(
                                          loggedInAccount: Account,
                                          teamId: TeamId,
                                          limit: Int,
                                          Logger: SRLogger
                                        ) extends GetSameTimeDataFromDbParams

case class GetSameTimeDataProspectEventsListing(
                                                 prospectId: Option[ProspectId],
                                                 prospectAccountId: Option[ProspectAccountsId],
                                                 repTrackingHosts: Seq[RepTrackingHosts],
                                                 prospectObjectIfAlreadyThere: Option[ProspectObject],
                                                 prospectAccountObjectIfAlreadyThere: Option[ProspectAccount],
                                                 teamId: TeamId,
                                                 account: Account,
                                                 baseEvent: ProspectEventV2,
                                                 limit: Int,
                                                 ec: ExecutionContext
                                               ) extends GetSameTimeDataFromDbParams
case class GetSameTimeDataProspectAccount(
                                           accountIds: Seq[AccountId],
                                           teamId: TeamId,
                                           orgId: OrgId,
                                           data: SearchQuery,
                                           account: Account,
                                           limit: Int,
                                           Logger: SRLogger,
                                           isApiCall: Boolean
                                         ) extends GetSameTimeDataFromDbParams

case class GetSameTimeLeadFinderBillingLogs(
                                            permittedAccountIds: Seq[AccountId],
                                            teamId: TeamId,
                                            limit: Int,
                                            Logger: SRLogger
                                          ) extends GetSameTimeDataFromDbParams

case class GetSameTimeDataNotesListing(
                                        teamId: TeamId,
                                        limit: Int,
                                        getNotesForm: GetNotesForm
                                      ) extends GetSameTimeDataFromDbParams