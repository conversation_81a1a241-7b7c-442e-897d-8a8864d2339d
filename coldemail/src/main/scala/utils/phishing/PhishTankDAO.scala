package utils.phishing

import play.api.Logging
import play.api.libs.json.{<PERSON>son, Reads}
import scalikejdbc.{AutoSession, DB, SQL, scalikejdbcSQLInterpolationImplicitDef}
import utils.{<PERSON><PERSON>, SRLogger}
import utils.email.BaseBodyLinks

import java.net.URI
import scala.util.Try


case class PhishTankRow(
  phish_id: Long,
  url: String
)

object PhishTankRow {
  implicit val reads: Reads[PhishTankRow] = Json.reads[PhishTankRow]
}

object PhishTankDAO extends Logging {

  implicit val session: AutoSession.type = AutoSession


  def findPhisLinksInPhishTank(baseBodyLinks: Seq[BaseBodyLinks]): List[String] = {

    if(!baseBodyLinks.isEmpty) {

      val clauses = baseBodyLinks.map {
        link =>
          if(link.is_whitelisted) sqls"""url = ${link.url} """ else sqls"""url_host = ${link.url_host} """
      }


      val whereClause = clauses.
        zipWithIndex
        .foldLeft(sqls"") { case (previousLinks, nextLink) =>
          val op = if (nextLink._2 == clauses.length - 1) sqls"" else sqls"OR"
          sqls"$previousLinks ${nextLink._1} $op"
        }

      DB readOnly { implicit session =>

        sql"""
      SELECT url_host
      FROM phish_tank
      WHERE $whereClause
       GROUP BY url_host
      ;
      """
          .map(rs => (rs.string("url_host")))
          .list
          .apply()

      }

    } else {

      List[String]()

    }

  }

  def insertPhishTankRows(
    allRows: Seq[PhishTankRow]
  )(using Logger: SRLogger): Try[List[Long]] = Try {

        val allRowsForDb = allRows.map(r => {
          val url_host = {
            Helpers.stringToURL(
              href = r.url,
              orgIdOpt = None
            )
          }.toOption
          Seq(
            r.phish_id,
            r.url,
            url_host.get
          )
        })

        if (allRowsForDb.length > 20000) {

          logger.error(s"[FATAL] Terminating the _updatePhishTank because phistank rows are greater than 20K ")

          throw new Exception("Terminating the _updatePhishTank because phistank rows are greater than 20K")

        } else {


          allRowsForDb
            .grouped(500)
            .toList
            .flatMap(group => {

              DB autoCommit { implicit session =>

                val valueParameters = group.flatten

                val valuePlaceholder = group.map(r => {
                  s"""(
                    ?,
                    ?,
                    ?
                  )"""
                }).mkString(", ")

                val query =
                  s"""
          INSERT INTO phish_tank
          (
             phish_id,
             url,
             url_host
          )
          VALUES $valuePlaceholder
          ON CONFLICT DO NOTHING
          RETURNING *;
        """

                SQL(query)
                  .bind(valueParameters*)
                  .map(_.long("id"))
                  .list
                  .apply()
              }
            })
    }
  }
//
//
//  def checkIfCampaignHasPhisLinks(campaignId: Long): Try[List[String]] = Try {
//    DB readOnly { implicit session =>
//
//      sql"""
//           SELECT phish_links FROM campaign_steps_variants WHERE campaign_id = $campaignId AND phish_links IS NOT NULL;
//        """
//        .map(rs => rs.string("phish_links"))
//        .list
//        .apply()
//
//    }
//  }


}
