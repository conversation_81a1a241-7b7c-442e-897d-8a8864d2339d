package playloader

// import com.softwaremill.macwire.wire
import common_auth_middleware.*
import controllers.AssetsComponents
import io.smartreach.companies_api.api.enrich_company_info.{CompaniesServiceDI, TheCompaniesApiDI}
import play.api.ApplicationLoader.Context
import play.api.db.evolutions.{DynamicEvolutions, EvolutionsComponents}
import play.api.db.{DBComponents, HikariCPComponents}
import play.api.i18n.I18nComponents
import play.api.libs.ws.ahc.AhcWSComponents
import play.api.mvc.EssentialFilter
import play.api.routing.Router
import play.api.{BuiltInComponentsFromContext, Logging}
import playfilters.FiltersModule
import router.Routes
import scalikejdbc.config.DBs
import utils.dependencyinjectionutils.ChannelSchedulers.*
import utils.dependencyinjectionutils.CommonAuth.CommonAuthControllerDI
import utils.dependencyinjectionutils.LinkedinSettingDI.{MqCaptainDataCookieFailurePublisherDI, PlanLimitServiceDI}
import utils.dependencyinjectionutils.ProductOnboardingDI.RollingUpdate.SrRollingUpdateCoreService_DI
import utils.dependencyinjectionutils.*
import api.accounts.service.AccountCreateService
import utils.dependencyinjectionutils.Tasks.BulkTaskServiceDI

import scala.concurrent.{ExecutionContext, Future}

class AppComponents(context: Context) extends BuiltInComponentsFromContext(context)
  with EmailValidationDAOService_DI
  with Logging
  with AhcWSComponents
  with PhantomBusterApiKeysServiceDI
  with PhantomBusterApiKeysDAODI
  with SocialAuthServiceDI
  with CampaignAISequenceServiceDI
  with PlanLimitServiceDI
  with LeadFinderBillingLogsPaginationService_DI
  with ProspectAccountServiceDI
  with TopReportsDaoJedisServiceDI
  with MQCampaignAISequenceGeneratorDI
  with MqAiContentGenerationPublisher_DI
  with ProspectAccountPaginationServiceDI
  with LinkedinSessionCreatorDI
  with BrightDataServiceDI
  with BrightDataApiDI
  with ReportDaoJedisServiceDI
  with TeamsMetadataServiceDI
  with TeamsMetadataDAODI
  with WorkflowCrmSettingsServiceDI
  with EmailSettingPaginationServiceDI
  with PhantomBusterExecutionLogsDAO_DI
  with GDPRController_DI
  with GDPRService_DI
  with GDPRDAO_DI
  with GDPROTPDBDAOService_DI
  with GDPROTPService_DI
  with CreateInCRMJedisDAO_DI
  with CampaignProspectStepScheduleLogsDAO_DI
  with PhantomBusterExecutionLogsServiceDI
  with CredentialsAuthService_DI
  with CredentialsAuthPaginationService_DI
  with EvolutionsComponents
  with PhantomBusterProxyDAO_DI
  with PhantomBusterProxyServiceDI
  with ProspectBatchActionServiceDI
  with DBComponents
  with LinkedinTaskServiceDI
  with GeneralSettingServiceDI
  with CampaignSchedulingMetadataDAO_DI
  with HikariCPComponents
  with AssetsComponents
  with AccountInvitationServiceDI
  with LinkedinTeamInboxDAO_DI
  with PhantomBusterServiceDI
  with CloudStorageDI
  with LinkedinMessagesServiceDI
  with PhantomBusterAgentServiceDI
  with PhantomBusterApiDI
  with CaptainDataApiDI
  with LinkedinMessagesDAODI
  with LinkedinMessagesDAOService_DI
  with PhantomBusterAgentsDAODI
  with LinkedinMessageThreadsServiceDI
  with LinkedinConnectionsServiceDI
  with LinkedinConnectionDAODI
  with LinkedinMessageThreadsDAODI
  with EmailSettingJedisServiceDI
  with EmailSettingDAOServiceDI
  with I18nComponents
  with CacheServiceDI
  with SrRandomUtilsDI
  with SrShuffleUtilsDI
  with PasswordChangeServiceDI
  with OrgMetadataServiceDI
  with RoleSettingServiceDI
  with FiltersModule
  with CampaignSendReportsDAO_DI
  with LlmAuditLogDAO_DI
  with CacheServiceJedisDI
  with RolePermissionDataDAOV2_DI
  with AccountAddServiceDI
  with EmailSettingTagsJedisServiceDI
  with CampaignProspectTimezonesJedisServiceDI
  with SrUserFeatureUsageEventServiceDI
  with PusherServiceDI
  with GeneralSettingDAO_DI
  with AccountOnboardingServiceDI
  with NewUserDripCamapignServiceDI
  with AffiliateTrackingServiceDI
  with EmailBodyServiceDI
  //  with KafkaServiceDI
  with GptApiService_DI
  with TeamStatsServiceDI
  with MqEmailAccountDeleterDI
  with AgencyDashboardCacheServiceDI
  with SmsSettingDI.SmsSettingDAODI
  with SmsSettingDI.SmsSettingControllerDI
  with SmsSettingDI.SmsSettingServiceDI
  with SmsSettingDI.SmsSettingUtilsDI
  with LinkedinSettingDI.LinkedinSettingControllerDI
  with LinkedinSettingDI.LinkedinSettingDAODI
  with LinkedinSettingDI.LinkedinSettingServiceDI
  with CaptainDataServiceDI
  with MqCaptainDataConversationExtractionPublisher_DI
  with MqCaptainDataCookieFailurePublisherDI
  with MqCaptainDataMessageExtractionPublisher_DI
  with LinkedinSettingDI.LinkedinSettingUtilsDI
  with WhatsappSettingDI.WhatsappSettingControllerDI
  with WhatsappSettingDI.WhatsappSettingDAODI
  with WhatsappSettingDI.WhatsappSettingServiceDI
  with WhatsappSettingDI.WhatsappSettingUtilsDI
  with SrUuidUtilsDI

  with LoggingActionDI
  with AuthUtilsDI
  with ResetUserCacheUtilDI
  with AccountDAOService_DI
  with PermissionUtilsDI
  with EmailMessageDataDAO_DI
  with EmailScheduledDAOService_DI
  with EmailScheduledServiceDI

  with AccountServiceDI
  with SrResourceDI.SrResourceDaoServiceDI
  with SrResourceDI.SrResourceWithoutTenantDaoServiceDI
  with SrResourceDI.SrCacheDI
  with SrUuidInternalDataServiceDI
  with SRCsvParserUtilsDI
  with UnivocityParserDI
  with MQOpenTrackerPublisher_DI
  with CustomCSVParserDI
  with SRCsvReaderUtilsDI
  with CampaignCacheServiceDI
  with CampaignServiceDI
  with InternalSchedulerRunLogDAO_DI
  with CampaignSendingVolumeLogsDAO_DI
  with Campaign.CampaignStartServiceDI
  with Campaign.CampaignStepServiceDI
  with Campaign.CampaignTestStepServiceDI
  with WebhookServiceDI
  with DeleteService_DI
  with CampaignUpdateSettingsServiceDI
  with SrRateLimiterDI
  with SrRateLimiterDAO_DI
  with WebhookDAO_DI
  with WebhookUtils_V2_DI
  with ProspectQueryDI
  with CampaignTemplateServiceDI
  with GoogleOAuthDI
    with CalendlyOAuthDI
  with CalendlyWebhookServiceDI
  with MqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC_DI
  with BlacklistServiceV2_DI
  with MicrosoftOAuthDI
  with GeneralModuleServiceDI
  with CampaignDAOService_DI

  with CampaignEmailSettingsDAO_DI
  with ProspectEventDAO_DI
  with ProspectEventService_DI
  with SrAPIKeyTypeDAO_DI
  with ProspectAddEventDAO_DI
  with ProspectUpdateCategoryTemp_DI
  with ProspectUpdateCategoryTemp2_DI
  with CampaignProspectDAO_DI
  with ProspectTagDAO_DI
  with DBCounterDAO_DI
  with ReportDAO_DI
  with MqCampaignSchedulingMetadataMigration_DI
  with ReportDaoService_DI
  with TemplateDAO_DI
  with CampaignStepVariantDAO_DI
  with SrDBQueryCounterService_DI
  with CampaignDAO_DI
  with SrDBQueryCounterReportControllerDI
  with CampaignTagDAO_DI
  with EmailScheduledDAO_DI
  with EmailSettingDAO_DI
  with EmailThreadDAO_DI
  with TriggerDAO_DI
  with WorkflowCrmSettingsDAO_DI
  with DownloadReportDAO_DI
  with SendingHolidayCalendarDAO_DI
  with SendingHolidayCalendarDateDAO_DI
  with RequestLogDAO_DI
  with RequestLogService_DI
  with FrontendControllerDI

  with RepTrackingHostServiceDI


  with ReplyFilteringServiceDI
  with MQUnsubscriberTrackerDI
  with EmailReplyTrackingModelDI
  with EmailValidationBatchRequestModelDI
  with ProspectTagServiceDI
  with CampaignTagServiceDI
  with SendingHolidayServiceDI

  with BlacklistServiceDI
  with BlacklistDAO_DI

  with SrRedisHashSetBasedLockServiceV2_DI
  with SrRedisSimpleLockServiceV2_DI
  with SrRedisHashSetServiceV2_DI
  with EmailSchedulerJedisService_DI

  with EmailValidationServiceDI
  with SyncTriggerMQServiceDI
  with ActivityTriggerMQServiceDI
  with ProspectEmailsDAOService_DI
  with EmailServiceDI
  with EmailsScheduledDeleteService_DI
  with SelectAndPublishForDeletionServiceDI
  with AccountOrgBillingRelatedInfoDAODI
  with AccountOrgBillingRelatedServiceDI
  with EmailSenderServiceDI
  with EmailServiceCompanionDI
  with EmailHandleErrorServiceDI
  with DomainPublicDNSDAO_DI
  with MQDomainServiceProviderDNSService_DI
  with SrDNSUtil_DI
  with OutlookApiSendEmailServiceDI
  with OutlookApiReplyTrackingServiceDI
  with GmailEmailApiDI
  with GmailApiReplyTrackingServiceDI
  with GmailApiSendEmailServiceDI
  with GmailEmailSendServiceDI
  with GmailReplyTrackingServiceDI
  with GmailSmtpEmailApiDI
  with OutlookEmailApiDI
  with MailgunServiceDI
  with MailgunEmailApiDI
  with SendGridServiceDI
  with SendGridEmailApiDI
  with SmtpImapSendEmailServiceDI
  with SmtpImapReplyTrackingServiceDI
  with SmtpImapEmailApiDI
  with ReportServiceDI
  with DownloadReportServiceDI
  with PhantomBusterWebhookControllerDI
  with PhishingCheckServiceDI

  with MQEmailSchedulerV2DI
  with MQClickTrackerDI
  with MQDoNotContactDI
  with MQDoNotContactPublisher_DI
  with MQProspectDeleterDI
  with MQWebhookCompletedDI
  with MQRepliedEventDI
  with MQProspectCompletedEventDI
  with MQProspectUnPauseEventDI
  with MQWebhookEmailInvalidDI
  with MQActivityTriggerPublisher_DI

  with MQTriggerDI
  //  with MQMBLEmailValidatorDI
  with EmailAccountTestServiceDI
  with EmailAccountServiceDI
  with ChannelSchedulers.EmailChannelSchedulerDI
  with ChannelSchedulers.GeneralChannelSchedularDI
  with ProspectUploadServiceDI
  with ActorMaterializerDI
  with IndexControllerDI
  with CredentialsAuthControllerDI
  with SalesforceOAuth_DI
  with ZohoOAuth_DI
  with ZohoApiDI
  with ZohoRecruitOAuth_DI
  with PipedriveOAuth_DI
  with HubSpotOAuth_DI
  with SalesForceApiDI
  with PipedriveApiDI
  with HubspotApiDI
  with SocialAuthControllerDI
  with GeneralModuleControllerDI
  with ProspectControllerDI
  with ProspectsListingPaginationService_DI
  with CampaignControllerDI
  with ReportControllerDI
  with SearchControllerDI
  with InboxControllerDI
  with InboxV3PaginationService_DI
  with TemplateControllerDI
  with BlacklistControllerDI
  with BlacklistListingPaginationService_DI
  with EmailSettingControllerDI
  with RoleSettingControllerDI
  with TimeZoneControllerDI
  with SendingHolidayControllerDI
  with TriggerControllerDI
  with TriggerServiceDI
  with Tasks.TaskServiceDI
  with Tasks.TaskControllerDI
  with Tasks.TaskDAO_DI
  with Tasks.TaskUtilsDI
  //  with Tasks.TaskCacheDAO_DI
  //  with Tasks.TaskCacheService_DI
  with Tasks.TaskDaoServiceDI
  with Tasks.TaskPartitionManagerService_DI
  //  with EventFramework.SrEventServiceDI
  with HandlePushTriggerEventServiceDI
  with HandleSyncTriggerEventServiceDI
  with HandleActivityTriggerEventServiceDI
  with LeadStatusServiceDI
  with BouncerEmailValidationApiDI
  with DeBounceEmailValidationApiDI
  with ListCleanEmailValidationApiDI
  with ProspectServiceDI
  with DataplatformControllerDI
  with WebhookControllerDI
  with ZapierControllerDI
  with TagControllerDI
  with SrRateLimiterReportControllerDI
  with ProspectAccountControllerDI
  with EmailValidationModelDI
  with EmailValidationApiToolsRecordDAODI
  with TransformAndSaveEmailValidationResultDI
  with EmailDeliveryAnalysisServiceDI
  with ProspectAccountDAO1DI
  with ProspectFeedDAO_DI
  with NewUserDripCampaignsDAO_DI
  with CampaignStepDAO_DI
  with AccountDAOV2_DI
  with OrganizationDAO_DI
  with LeadFinderDAO_DI
  with InternalSupportServicesDI
  with InternalSupportControllerDI
  with InternalSupportAuthService_DI
  with InternalSupportApiDAO_DI
  with GoogleAdsDAO_DI
  with FreeEmailDomainListDAO_DI
  with FreeEmailDomainListService_DI
  with EmailNotificationServiceDI
  with EmailNotificationDAO_DI
  with AccountDAO_DI
  with OrganizationBillingDAO_DI
  with InternalDashboardDAO_DI
  with InternalDashboardControllerDI
  with TemplateServiceDI
  with Template.TemplateUtilsDI
  with FirstPromoterDI
  with AdmitadDI
  with NewAuthController_DI
  with NewAuthFlowService_DI
  with CampaignProspectService2DI

  with InboxV3ServiceDI
  with InboxSendServiceDI

  with NewAuthGoogleSignupService_DI
  with NewAuthOutlookSignupService_DI
  with UserCheckApiServiceDI
  with AccessTokenService_DI
  with CustomerSupportReadOnlyServices_DI
  with CustomerSupportReadOnlyController_DI
  with EventLogDAO_DI
  with EventLogService_DI
  with EventLogFindServiceDI
  with WorkflowAttemptDAO_DI
  with WorkFlowAttemptService_DI
  with ClientAccountAccessLogDAO_DI
  with SRGoogleRecaptchaServices_DI
  with GoogleRecaptchaApiService_DI
  with UserRedisKeyService_DI
  with AccountOTPService_DI
  with AccountOTPDBDAOService_DI
  with OTPJedisDAOService_DI
  with CampaignEditedPreviewEmailDAO_DI
  with WorkerWSClientDI
  with WorkerActorMaterializerDI
  with WorkerActorSystemDI
  with TagServiceDI
  with ProspectColumnDef_DI
  with ProspectColumnDefDAO_DI
  with EmailReplyTrackingModelV2DI
  with MqSrAiApiPublisher_DI
  with CampaignProspectServiceDI
  with DripLogJedisDAO_DI
  with CampaignsMissingMergeTagServiceDI

  with CampaignProspectAssignDI
  with ProspectsEmailsDAO_DI
  with ProspectServiceV2_DI
  with EmailMessageContactModel_DI


  with BillingV2.BillingV2ControllerDI
  with BillingV2.BillingAppServiceDI
  with DomainDataService_DI
  with ApiLayerAPIService_DI
  with RapidAPIService_DI
  with SpamMonitorService_DI
  with TeamsDAO_DI
  with DBUtils_DI
  with EmailSendingStatusDAO_DI
  with BillingV2.BillingAppAuthMiddlewareDI
  with BillingV2.SubscriptionServiceDI
  with BillingV2.SrPlanServiceDI
  with DomainInfoWhoisDAO_DI
  with CsvQueueDaoDI
  with CsvQueueServiceDI
  with GeneralModuleDI
  with SrInternalFeatureUsageDaoDI
  with EmailSendingStatusService_DI
  with BlacklistProspectCheckDAO_DI
  with CampaignProspectDAO_2DI
  //  with ScyllaDbConnection_DI
  //  with ScyllaRunSafely_DI
  //  with TaskCacheDatabase_DI

  with CloudflareServiceDI
  with CustomTrackingDomainServiceDI
  with CustomTrackingDomainServiceDAO_DI
  with RepTrackingHostDao_DI
  with GenerateTempId_DI
  with TeamInboxDAO_DI
  with TeamInboxService_DI
  with TeamInboxControlle_DI
  //  with CacheEventScyllaService_DI
  //  with CacheEventScyllaDAO_DI
  with SpamTest.SpamTestServiceDI
  with SpamTest.SpamTestDAO_DI
  with SupportAccessToUserAccountDAO_DI
  with SupportAccessController_DI
  with ReplySentimentService_DI
  with ReplySentimentDAO_DI
  with ReplySentimentJedisDAO_DI
//  with MqHandleEventLogDI
  with EmailReplyTrackingDAOService_DI
  with Team.TeamServiceDI
  with Team.TeamDAOService_DI
  with Team.TeamSRAIFlagsJedisDAO_DI
  with SrUuidServiceDI
  with Team.TeamControllerDI
  with LeadFinderService_DI
  with MergeTagService_DI
  with EmailSettingService_DI
  with ProspectDAOService_DI
  with ProspectDAOServiceV2_DI
  with CampaignProspectDAOService_DI
  with OrganizationDAOService_DI
  with IntegrationTypeService_DI
  with SRTriggerAllowedCombos_DI
  with ProspectDAO_DI
  with AIHyperPersonalizedGeneratorDI
  with TriggerServiceV2_DI
  with TriggerJedisDAO_DI
  with TriggerDAOService_DI
  with TIntegrationCRMService_DI
  with ReplySentimentDAOService_DI
  with AssociateProspectToEmailThreadService_DI
  with MQAssociateProspectToOldEmails_DI
  with FreeEmailDomainListControllerDI
  with Site24x7ControllerDI
  with Site24x7ServiceDI
  with GPTControllerDI
  with GPTServiceDI
  with GPTInboxServiceDI
  with AISequenceGeneratorDI
  with GPTApiDI
  with EmailThreadDAOService_DI
  with InboxV3DAOService_DI
  with OrgMetadataDAO_DI
  with Calendar.CalendarControllerDI
  with Calendar.CalendarAppServiceDI
  with Calendar.CalendarAppApiDI
  with Calendar.CalendarAppAuthMiddlewareDI
  with Calendar.CalendarAppDaoDI
  with TwilioDialerServiceDI
  with CallControllerDI
  with EmailInfraControllerDI
  with MailDosoApi_DI
  with ZapMailAPI_DI
  with MailDosoService_DI
  with MqPurchasedEmailsDeleter_DI
  with PurchasedDomainsAndEmailsDeleterService_DI
  with MqPurchasedDomainsDeleter_DI
  with EmailInfraService_DI
  with EmailInfraDao_DI
  with CallServiceDI
  with CallDAO_DI
  with InternalCSDReportDAODI
  with InternalCSDReportServiceDI
  with AffiliateDAO_DI
  with ReferralControllerDI
  with Campaign.ChannelSettingServiceDI
  with Campaign.CampaignPreviewServiceDI
  with ProspectEventDAOService_DI
  with EnrichCompanyInfoServiceDI
  with CompaniesServiceDI
  with TheCompaniesApiDI
  with FeatureFlagsServiceV2DI
  with PipelineDAO_DI
  with PipelineService_DI
  with PipelineController_DI
  with OpportunityStatusDAO_DI
  with OpportunityStatusService_DI
  with OpportunityStatusController_DI
  with OpportunityDAO_DI
  with OpportunityService_DI
  with OpportunityController_DI
  with ApiService_DI
  with CampaignApiService_DI
  with CommonAuthControllerDI
  with ProspectApiService_DI
  with CommonAuthServiceDI
  with ApiLayerServiceDI
  with ApiLayerDAO_DI
  with ProspectTagDAOLegacy_DI
  with ProductOnboardingDI.ProductOnboardingControllerDI
  with ProductOnboardingDI.ProductOnboardingServiceDI
  with ProductOnboardingDI.ProductOnboardingDAO_DI
  with AnnualizedValueService_DI
  with OpportunityStatusDAOService_DI
  with OpportunityDAOService_DI
  with CallUtilsDI
  with OpportunityUtils_DI
  with CallHistoryLogDAO_DI
  with OpportunityStatusCommonService_DI
  with CommonAuthMiddlewareDI
  with CommonAuthLoggingActionDI
  with CommonAuthApiDI
  with OpportunitiesDefaultSetupService_DI
  with CallApiService_DI
  with OpportunityJedisDAO_DI
  with SRCommonAuthServiceDI
  with AuthDAODI
  with AuthServiceDI
  with SRCommonAuthRedisServiceDI
  with SRCommonAuthControllerDI
  with BlacklistApiService_DI
  with TeamsPipelineConfigDAO_DI
  with TeamsPipelineConfigService_DI
  with TeamsPipelineConfigController_DI
  with OrganizationService_DI
  with SrDateTimeUtils_DI
  with OrganizationJedisCacheDao_DI
  with TeamsListingPaginationService_DI
  with TeamsApiService_DI
  with SrRollingUpdateCoreService_DI
//  with MqDeletionAndRevertV2DI
  with MqDeletionAndRevertV2Publisher_DI
  with SchedulerIntegrityServiceDI
  with SchedulerIntegrityReportDaoDI
  with MobileAppControllerDI
  with ProspectEventsListingPaginationService_DI
  with MqSchedulerIntegrityCheckDI
  with CampaignStepDaoServiceDI
  with InboxPlacementCheckDAO_DI
  with OutlookUtilsService_DI
  with PotentialDuplicateProspectService_DI
  with PotentialDuplicateProspectsDAO_DI
  with ProspectCategoryServiceDI
  with LeadFinderController_DI
  with DuplicateProspectsController_DI
  with MqZapmailEmailAccountCreationPublisher_DI
  with DomainHealthCheckService_DI
  with DomainHealthCheckDAO_DI
  with MqPublisherHandleEventLogDI
  with InboxPlacementCheckLogsReportService_DI
  with NotesDAO_DI
  with NotesPaginationService_DI
  with NotesService_DI
  with NotesController_DI
  with ProspectServiceWithEmailValidation_DI
  with LeadFinderValidationService_DI
  with WarmupHeroService_DI
  with WarmupHeroController_DI
  with AccountUploadService_DI
  with MqMergeDuplicateProspects_DI
  with EmailHealthCheckService_DI
  with EmailHealthDnsCheckService_DI
  with EmailHealthCheckController_DI
  with EmailHealthCheckDAO_DI
  with Inbox_DI
  with ProspectColumnService_DI
  with ProspectDaddy.ProspectorController_DI
  with SrMailServerServiceDI
  with SrMailServerDao_DI
  with SrMailServerRedisService_DI
    with MqUpdateManualEmailTaskStatusDI
    with BulkTaskServiceDI
  with ProspectDaddy.PdLeadsService_DI
  with ProspectDaddy.PdLeadsDAO_DI
  with ProspectDaddy.AnyMailFinderAPI_DI 
  with AccountCreateServiceDI
  with MqHandleAttemptCreationPublisherDI
  with MqForSaveReplySentimentPublisher_DI
  with MqPauseCampaignOnReplySentimentSelectDI
  with MqExtractLinkedinConnectionResultsDI
  with MqDeleteLinkedinSettingDI
  with WarmupHeroApi_DI
  with PhantomBusterTeamServiceDI
  with EmailCommonServiceDI
  with SrAiLockJedisDAO_DI
  with SrAiApi_DI
  with ContentAnalysisService_DI
  with ContentGenerationService_DI
  with MqAutoUpdateProspectCategoryPublisherDI
  with CampaignGenerationService_DI
  {

  lazy val router: Router = {
    // add the prefix string in local scope for the Routes constructor
    val prefix: String = "/"

    /*
     23-jan-2025: after compiling code, this Routes constructor is found in target folder: router/Routes.scala file
     
     everytime we add a new controller class to the resources/routes file, this will need to be modified. 
     */
    new Routes(
      errorHandler = httpErrorHandler,
      CommonAuthController_28 = commonAuthController,
      SrCommonAuthController_7 = srCommonAuthController,
      MobileAppController_20 = mobileAppController,
      CalendarController_15 = calendarController,
      BillingV2Controller_50 = billingV2Controller,
      CredentialsAuthController_2 = credentialsAuthController,
      PhantomBusterWebhookController_44 = phantomBusterWebhookController,
      Site24x7Controller_22 = site24x7Controller,
      InternalSupportController_30 = internalSupportController,
      CustomerSupportReadOnlyController_13 = customerSupportReadOnlyController,
      GeneralModuleController_4 = generalModuleController,
      ProspectController_29 = prospectController,
      TagController_27 = tagController,
      CampaignController_17 = campaignController,
      ReportController_56 = reportController,
      EmailHealthCheckController_37 = emailHealthCheckController,
      TemplateController_10 = templateController,
      SearchController_38 = searchController,
      ProspectAccountController_31 = prospectAccountController,
      DuplicateProspectsController_24 = duplicateProspectsController, 
      TaskController_40 = taskController,
      EmailInfraController_26 = emailInfraController, 
      CallController_46 = callController, 
      InboxController_32 = inboxController,
      LeadFinderController_34 = leadFinderController, 
      GDPRController_52 = gdprController, 
      GPTController_0 = gptController,
      TeamController_49 = teamController, 
      TeamInboxController_16 = teamInboxController, 
      EmailSettingController_47 = emailApiController,
      LinkedinSettingController_58 = linkedinSettingController, 
      WhatsappSettingController_57 = whatsappSettingController,
      SmsSettingController_36 = smsSettingController, 
      RoleSettingController_42 = roleSettingController, 
      TimeZoneController_11 = timeZoneController,
      NewAuthController_18 = newAuthController, 
      FrontendController_59 = frontendController, 
      SupportAccessController_39 = supportAccessController,
      SocialAuthController_48 = socialAuthController, 
      WarmupHeroController_19 = warmupHeroController, 
      BlacklistController_8 = blacklistController,
      NotesController_41 = notesController, 
      ZapierController_1 = zapierController, 
      TriggerController_3 = triggerController,
      WebhookController_6 = webhookController, 
      DataplatformController_14 = dataplatformController, 
      SendingHolidayCalendarController_23 = sendingHolidayController,
      InternalDashboardController_55 = internalDashboardController, 
      TeamsPipelineConfigController_53 = teamsPipelineConfigController,
      PipelineController_12 = pipelineController, 
      OpportunityStatusController_35 = opportunityStatusController, 
      OpportunityController_21 = opportunityController,
      SrRateLimiterReportController_25 = srRateLimiterReportController, 
      SrDBQueryCounterReportController_33 = srDBQueryCounterReportController,
      ProductOnBoardingController_5 = productOnboardingController, 
      ReferralController_9 = referralController,
      FreeEmailDomainListController_45 = freeEmailDomainListController, 
      IndexController_51 = indexController,
      Assets_54 = assets, 
      ProspectorController_43 = prospectorController
    )

//    wire[Routes]

  }

  implicit lazy val playDefaultExecutionContext: ExecutionContext = controllerComponents.executionContext

  override lazy val dynamicEvolutions = new DynamicEvolutions

  override lazy val httpFilters: Seq[EssentialFilter] = filters.filters


  // private def isTest = environment.mode == Mode.Test

  //  private def isDev = environment.mode == Mode.Dev

  //22-Jan-2024 SCYLLA_COMMENTED_OUT
  //  override val scyllaCluster: Cluster = scyllaDbConnection.initialize()

  applicationLifecycle.addStopHook { () =>
    logger.info("The app is about to stop")
    DBs.closeAll()
    //22-Jan-2024 SCYLLA_COMMENTED_OUT
    //    scyllaCluster.close()
    Future.successful(())
  }


  val onStart = {
    logger.info("The app is about to start")

    DBs.setupAll()

    /*
    if (isTest) {

      logger.info("Test Mode: drop existing tables")

      implicit val session: AutoSession.type = AutoSession
      DB.localTx { implicit session =>
        sql"DROP SCHEMA public CASCADE;".execute.apply()
        sql"CREATE SCHEMA public;".execute.apply()
      }


      logger.info("Test Mode: apply evolutions")

      applicationEvolutions

    } else {

      applicationEvolutions

    }
    */

    applicationEvolutions
  }
}
