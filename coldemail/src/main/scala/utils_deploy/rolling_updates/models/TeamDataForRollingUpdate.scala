package utils_deploy.rolling_updates.models

import api.AppConfig
import api.accounts.{OrgMetadata, TeamId, TeamMetaData}
import api.accounts.models.OrgId
import io.sr.billing_common.models.{PlanType, SrPlanName}


case class TeamDataForRollingUpdate(
  team_id: TeamId,
  is_active_team: <PERSON><PERSON><PERSON>,
  metadata: TeamMetaData,
  enable_new_reply_sentiment: Option[<PERSON><PERSON><PERSON>],
  org_plan_name: SrPlanName,
  org_plan_type: PlanType,
  total_seats: Int,
  is_agency: Boolean,
  org_id: OrgId
)

object TeamDataForRollingUpdate {

  def shouldAllowEarlyRollingUpdate(
    team: TeamDataForRollingUpdate,
    feature: SrRollingUpdateFeature,
  ): Boolean = {


    val planType: PlanType = team.org_plan_type

    val totalSeats: Int = team.total_seats

    val allowEarlyUpdate: Boolean = feature.cohort match {

      case SrRollingUpdateCohort.UnpaidOrLessThan5SeatOrgs =>

        if (planType == PlanType.TRIAL) true
        else if (planType == PlanType.INACTIVE) true
        else if (planType == PlanType.PAID && totalSeats <= 5) true
        else false

      case SrRollingUpdateCohort.UnpaidOrLessThan10SeatOrgs =>

        if (planType == PlanType.TRIAL) true
        else if (planType == PlanType.INACTIVE) true
        else if (planType == PlanType.PAID && totalSeats <= 10) true
        else false

      case SrRollingUpdateCohort.UnpaidOrLessThan25SeatOrgs =>

        if (planType == PlanType.TRIAL) true
        else if (planType == PlanType.INACTIVE) true
        else if (planType == PlanType.PAID && totalSeats <= 25) true
        else false

      case SrRollingUpdateCohort.EveryoneOtherThanLeastRiskOrgs =>
        !AppConfig.RollingUpdates.orgIdsToTakeLeastRiskOnWhileReleasingUpdates.map(_.id).contains(team.org_id.id)

      case data: SrRollingUpdateCohort.FewTeamsInLeastRiskOrgsPlusEveryoneElse =>
        !AppConfig.RollingUpdates.orgIdsToTakeLeastRiskOnWhileReleasingUpdates.map(_.id).contains(team.org_id.id) ||
        data.teamIds.contains(team.team_id)

      case data: SrRollingUpdateCohort.SpecificTeams =>
        feature match {
//          case SrRollingUpdateFeature.EmailNotCompulsory => true

          case SrRollingUpdateFeature.Base64FlowAsFirstMail |
               SrRollingUpdateFeature.ESPMatching  =>
            data.teamIds.contains(team.team_id)
        }



      case data: SrRollingUpdateCohort.SpecificOrgs =>  data.orgIds.contains(team.org_id)


      case data: SrRollingUpdateCohort.ForOrgsGreaterThenASpecificOrg => team.org_id.id > data.latestOrgId.id

      case data: SrRollingUpdateCohort.ForOrgsGreaterThanAOrgIdAndOrgMetadataFlag => team.org_id.id > data.latestOrgId.id ||
        ( team.enable_new_reply_sentiment.isDefined &&
          team.enable_new_reply_sentiment.get) || (!AppConfig.isProd && !AppConfig.isDevDomain)
      

    }

    allowEarlyUpdate
  }
  
}
