package api.sr_ai.dao

import api.{AppConfig, CacheServiceJedis}
import api.sr_ai.models.AiModel

import scala.util.Try

class SrAiLockJedisDAO(cacheServiceJedis: CacheServiceJedis) {

  /**
   * @function genKey
   * @description Generates a Redis key for AI model lock.
   * @param aiModel The AI model for which the key is generated.
   * @return A string representing the Redis key.
   */
  def genKey(aiModel: AiModel): String = {
    s"${AppConfig.redisKeyPrefix}_sr_ai_api_lock_${aiModel.toString}"
  }

  /**
   * @function checkLock
   * @description Checks if a lock exists for the given AI model.
   * @param aiModel The AI model to check the lock for.
   * @return True if the lock exists, false otherwise.
   */
  def checkLock(aiModel: AiModel): Boolean = {
    val key = genKey(aiModel = aiModel)
    cacheServiceJedis.hasLock(cacheKey = key).getOrElse(true)// if failed we will consider it locked and not push
  }

  /**
   * @function acquireLock
   * @description Acquires a lock for the given AI model with an expiration time.
   * @param aiModel The AI model to acquire the lock for.
   * @param expiresInSeconds The expiration time for the lock in seconds.
   * @return True if the lock was successfully acquired, false otherwise.
   */
  def acquireLock(aiModel: AiModel, expiresInSeconds: Int): Try[Boolean] = {
    val key = genKey(aiModel = aiModel)
    cacheServiceJedis.acquireLock(cacheKey = key, expireInSeconds = expiresInSeconds)
  }

} 