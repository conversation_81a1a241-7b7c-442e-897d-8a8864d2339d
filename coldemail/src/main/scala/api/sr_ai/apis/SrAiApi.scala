package api.sr_ai.apis

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.OrgId
import api.campaigns.PreviousFollowUp
import api.columns.ProspectColGenStatus
import api.gpt.ai_hyperpersonalized.StepContext
import api.llm.dao.{LlmAuditLogDAO, LlmAuditLogInsertData, LlmResponseData}
import api.llm.models.LlmFlow
import api.prospects.models.ProspectId
import api.sr_ai.dao.SrAiLockJedisDAO
import api.sr_ai.models.*
import eventframework.ProspectFieldsResult
import org.joda.time.DateTime
import play.api.libs.ws.*

import sr_scheduler.models.CampaignAIGenerationContext
import play.api.libs.json.{JsError, JsSuccess, Json, OFormat, OWrites, JsValue}
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class SrAiApi(
  llmAuditLogDAO: LlmAuditLogDAO,
  srAiLockJedisDAO: SrAiLockJedisDAO
) {

  private val baseUrl = AppConfig.srAiApiBaseUrl

  def generateEmailReplyFromThread(
    generateEmailReplyFromThreadRequest: GenerateEmailReplyFromThreadRequest,
    teamId: TeamId,
  )(
    implicit Logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext,
  ): Future[GenerateEmailReplyFromThreadResult] = {

    ws
      .url(s"$baseUrl/api/content/generate-email-reply-from-thread")
      .withHttpHeaders(
        "X-TRACE-ID" -> Logger.logTraceId
      )
      .post(
        Json.toJson(generateEmailReplyFromThreadRequest)
      )
      .flatMap { response =>

        if (response.status != 200) {

          createSrAiApiAuditLog(
            status = ProspectColGenStatus.Failed,
            teamId = teamId,
            consumedTokenCountGenerationOutput = None,
            consumedTokenCountPromptInput = None,
            model = generateEmailReplyFromThreadRequest.options.model,
            flow = LlmFlow.EmailReplyGeneration,
          )

          Logger.shouldNeverHappen(
            msg = s"Failed to generate email reply from thread: ${response.status} :: ${response.body} :: teamId: $teamId"
          )

          Future.failed(
            SrAiApiFailedException(
              message = s"Failed to generate email reply from thread: ${response.status} :: ${response.body}",
              statusCode = response.status,
            )
          )

        } else {

          Json.parse(response.body)
            .validate[GenerateEmailReplyFromThreadResult] match {

            case JsError(errors) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Failed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = None,
                consumedTokenCountPromptInput = None,
                model = generateEmailReplyFromThreadRequest.options.model,
                flow = LlmFlow.EmailReplyGeneration,
              )

              Logger.shouldNeverHappen(
                msg = s"Failed to parse generate email reply from thread response: ${response.body} :: errors: $errors:: teamId: $teamId"
              )

              Future.failed(
                new Exception(s"Failed to parse generate email reply from thread response: ${response.body} :: errors: $errors")
              )

            case JsSuccess(value, _) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Completed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = value.token_usage.prompt_tokens,
                consumedTokenCountPromptInput = value.token_usage.completion_tokens,
                model = generateEmailReplyFromThreadRequest.options.model,
                flow = LlmFlow.EmailReplyGeneration,
              )

              Future.successful(value)

          }

        }

      }

  }

  def classifyOutOfOffice(
    outOfOfficeRequest: OutOfOfficeRequest,
    teamId: TeamId,
  )(
    implicit Logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext,
  ): Future[OutOfOfficeResult] = {

    ws
      .url(s"$baseUrl/api/out-of-office")
      .withHttpHeaders(
        "X-TRACE-ID" -> Logger.logTraceId
      )
      .post(
        Json.toJson(outOfOfficeRequest)
      )
      .flatMap { response =>

        if (response.status != 200) {

          createSrAiApiAuditLog(
            status = ProspectColGenStatus.Failed,
            teamId = teamId,
            consumedTokenCountGenerationOutput = None,
            consumedTokenCountPromptInput = None,
            model = outOfOfficeRequest.options.model,
            flow = LlmFlow.OutOfOfficeClassification,
          )

          Logger.shouldNeverHappen(
            msg = s"Failed to classify out of office: ${response.status} :: ${response.body} :: teamId: $teamId"
          )

            checkErrorAndLockAi(model = outOfOfficeRequest.options.model, status = response.status, response.json)

          Future.failed(
            SrAiApiFailedException(
              message = s"Failed to classify out of office: ${response.status} :: ${response.body}",
              statusCode = response.status,
            )
          )

        } else {

          Json.parse(response.body)
            .validate[OutOfOfficeResult] match {

            case JsError(errors) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Failed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = None,
                consumedTokenCountPromptInput = None,
                model = outOfOfficeRequest.options.model,
                flow = LlmFlow.OutOfOfficeClassification,
              )

              Logger.shouldNeverHappen(
                msg = s"Failed to parse out of office response: ${response.body} :: errors: $errors:: teamId: $teamId"
              )

              Future.failed(
                new Exception(s"Failed to parse out of office response: ${response.body} :: errors: $errors")
              )

            case JsSuccess(value, _) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Completed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = value.token_usage.prompt_tokens,
                consumedTokenCountPromptInput = value.token_usage.completion_tokens,
                model = outOfOfficeRequest.options.model,
                flow = LlmFlow.OutOfOfficeClassification,
              )

              Future.successful(value)

          }

        }

      }

  }

  def generateCampaign(
    campaignGenerationRequest: CampaignGenerationRequest,
    teamId: TeamId,
  )(
    implicit Logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext,
  ): Future[CampaignGenerationResult] = {

    ws
      .url(s"$baseUrl/api/campaign/generate-campaign")
      .withHttpHeaders(
        "X-TRACE-ID" -> Logger.logTraceId
      )
      .post(
        Json.toJson(campaignGenerationRequest)
      )
      .flatMap { response =>

        if (response.status != 200) {

          createSrAiApiAuditLog(
            status = ProspectColGenStatus.Failed,
            teamId = teamId,
            consumedTokenCountGenerationOutput = None,
            consumedTokenCountPromptInput = None,
            model = campaignGenerationRequest.options.model,
            flow = LlmFlow.CampaignSeqGeneration,
          )

          Logger.shouldNeverHappen(
            msg = s"Failed to generate campaign: ${response.status} :: ${response.body} :: teamId: $teamId"
          )

          Future.failed(
            SrAiApiFailedException(
              message = s"Failed to generate campaign: ${response.status} :: ${response.body}",
              statusCode = response.status,
            )
          )

        } else {

          Json.parse(response.body)
            .validate[CampaignGenerationResult] match {

            case JsError(errors) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Failed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = None,
                consumedTokenCountPromptInput = None,
                model = campaignGenerationRequest.options.model,
                flow = LlmFlow.CampaignSeqGeneration,
              )

              Logger.shouldNeverHappen(
                msg = s"Failed to parse generate campaign response: ${response.body} :: errors: $errors:: teamId: $teamId"
              )

              Future.failed(
                new Exception(s"Failed to parse generate campaign response: ${response.body} :: errors: $errors")
              )

            case JsSuccess(value, _) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Completed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = value.token_usage.prompt_tokens,
                consumedTokenCountPromptInput = value.token_usage.completion_tokens,
                model = campaignGenerationRequest.options.model,
                flow = LlmFlow.CampaignSeqGeneration,
              )

              Future.successful(value)

          }

        }

      }

  }

  private def createSrAiApiAuditLog(
    status: ProspectColGenStatus,
    consumedTokenCountPromptInput: Option[Int],
    consumedTokenCountGenerationOutput: Option[Int],
    teamId: TeamId,
    model: AiModel,
    flow: LlmFlow,
  )(
    implicit Logger: SRLogger,
  ): Try[List[Long]] = {

    val auditLog = LlmAuditLogInsertData(
      team_id = teamId,
      prospect_id = ProspectId(0L),
      llm_tool = model,
      status = status,
      flow = flow,
      column_def_id = None,
      consumed_token_count_prompt_input = consumedTokenCountPromptInput,
      consumed_token_count_generation_output = consumedTokenCountGenerationOutput,
      request_log_id = Logger.logRequestId,
      queue_message = Json.obj()
    )

    llmAuditLogDAO.insertLlmAuditLog(
      logs = List(auditLog)
    ) match {

      case Failure(exception) =>

        Logger.shouldNeverHappen(
          msg = s"Failed to create audit log for out of office classification request. teamId: $teamId :: status: ${status.toString}",
          err = Some(exception),
        )

        Failure(exception)

      case Success(value) =>

        Success(value)

    }

  }

  def shortenText(
    shortenTextRequest: ShortenTextRequest,
    teamId: TeamId,
  )(
    implicit Logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext,
  ): Future[ShortenTextResult] = {

    ws
      .url(s"$baseUrl/api/content/shorten-text")
      .withHttpHeaders(
        "X-TRACE-ID" -> Logger.logTraceId
      )
      .post(
        Json.toJson(shortenTextRequest)
      )
      .flatMap { response =>

        if (response.status != 200) {

          createSrAiApiAuditLog(
            status = ProspectColGenStatus.Failed,
            teamId = teamId,
            consumedTokenCountGenerationOutput = None,
            consumedTokenCountPromptInput = None,
            model = shortenTextRequest.options.model,
            flow = LlmFlow.TextShortening,
          )

          Logger.shouldNeverHappen(
            msg = s"Failed to shorten text: ${response.status} :: ${response.body} :: teamId: $teamId"
          )

          checkErrorAndLockAi(model = shortenTextRequest.options.model, status = response.status, response.json)

          Future.failed(
            SrAiApiFailedException(
              message = s"Failed to shorten text: ${response.status} :: ${response.body}",
              statusCode = response.status,
            )
          )

        } else {

          Json.parse(response.body)
            .validate[ShortenTextResult] match {

            case JsError(errors) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Failed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = None,
                consumedTokenCountPromptInput = None,
                model = shortenTextRequest.options.model,
                flow = LlmFlow.TextShortening,
              )

              Logger.shouldNeverHappen(
                msg = s"Failed to parse shorten text response: ${response.body} :: errors: $errors:: teamId: $teamId"
              )

              Future.failed(
                new Exception(s"Failed to parse shorten text response: ${response.body} :: errors: $errors")
              )

            case JsSuccess(value, _) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Completed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = value.token_usage.prompt_tokens,
                consumedTokenCountPromptInput = value.token_usage.completion_tokens,
                model = shortenTextRequest.options.model,
                flow = LlmFlow.TextShortening,
              )

              Future.successful(value)

          }

        }

      }

  }

  def rephraseText(
    rephraseTextRequest: RephraseTextRequest,
    teamId: TeamId,
  )(
    implicit Logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext,
  ): Future[RephraseTextResult] = {

    ws
      .url(s"$baseUrl/api/content/rephrase-text")
      .withHttpHeaders(
        "X-TRACE-ID" -> Logger.logTraceId
      )
      .post(
        Json.toJson(rephraseTextRequest)
      )
      .flatMap { response =>

        if (response.status != 200) {

          createSrAiApiAuditLog(
            status = ProspectColGenStatus.Failed,
            teamId = teamId,
            consumedTokenCountGenerationOutput = None,
            consumedTokenCountPromptInput = None,
            model = rephraseTextRequest.options.model,
            flow = LlmFlow.TextRephrasing,
          )

          Logger.shouldNeverHappen(
            msg = s"Failed to rephrase text: ${response.status} :: ${response.body} :: teamId: $teamId"
          )

          checkErrorAndLockAi(model = rephraseTextRequest.options.model, status = response.status, response.json)

          Future.failed(
            SrAiApiFailedException(
              message = s"Failed to rephrase text: ${response.status} :: ${response.body}",
              statusCode = response.status,
            )
          )

        } else {

          Json.parse(response.body)
            .validate[RephraseTextResult] match {

            case JsError(errors) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Failed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = None,
                consumedTokenCountPromptInput = None,
                model = rephraseTextRequest.options.model,
                flow = LlmFlow.TextRephrasing,
              )

              Logger.shouldNeverHappen(
                msg = s"Failed to parse rephrase text response: ${response.body} :: errors: $errors:: teamId: $teamId"
              )

              Future.failed(
                new Exception(s"Failed to parse rephrase text response: ${response.body} :: errors: $errors")
              )

            case JsSuccess(value, _) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Completed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = value.token_usage.prompt_tokens,
                consumedTokenCountPromptInput = value.token_usage.completion_tokens,
                model = rephraseTextRequest.options.model,
                flow = LlmFlow.TextRephrasing,
              )

              Future.successful(value)

          }

        }

      }

  }

  def regenerateEmailSubject(
    regenerateEmailSubjectRequest: RegenerateEmailSubjectRequest,
    teamId: TeamId,
  )(
    implicit Logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext,
  ): Future[RegenerateEmailSubjectResult] = {

    ws
      .url(s"$baseUrl/api/content/regenerate-email-subject")
      .withHttpHeaders(
        "X-TRACE-ID" -> Logger.logTraceId
      )
      .post(
        Json.toJson(regenerateEmailSubjectRequest)
      )
      .flatMap { response =>

        if (response.status != 200) {

          createSrAiApiAuditLog(
            status = ProspectColGenStatus.Failed,
            teamId = teamId,
            consumedTokenCountGenerationOutput = None,
            consumedTokenCountPromptInput = None,
            model = regenerateEmailSubjectRequest.options.model,
            flow = LlmFlow.EmailSubjectRegeneration,
          )

          Logger.shouldNeverHappen(
            msg = s"Failed to regenerate email subject: ${response.status} :: ${response.body} :: teamId: $teamId"
          )

          checkErrorAndLockAi(model = regenerateEmailSubjectRequest.options.model, status = response.status, response.json)

          Future.failed(
            SrAiApiFailedException(
              message = s"Failed to regenerate email subject: ${response.status} :: ${response.body}",
              statusCode = response.status,
            )
          )

        } else {

          Json.parse(response.body)
            .validate[RegenerateEmailSubjectResult] match {

            case JsError(errors) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Failed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = None,
                consumedTokenCountPromptInput = None,
                model = regenerateEmailSubjectRequest.options.model,
                flow = LlmFlow.EmailSubjectRegeneration,
              )

              Logger.shouldNeverHappen(
                msg = s"Failed to parse regenerate email subject response: ${response.body} :: errors: $errors:: teamId: $teamId"
              )

              Future.failed(
                new Exception(s"Failed to parse regenerate email subject response: ${response.body} :: errors: $errors")
              )

            case JsSuccess(value, _) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Completed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = value.token_usage.prompt_tokens,
                consumedTokenCountPromptInput = value.token_usage.completion_tokens,
                model = regenerateEmailSubjectRequest.options.model,
                flow = LlmFlow.EmailSubjectRegeneration,
              )

              Future.successful(value)

          }

        }

      }

  }

  def generateEmailSubject(
    generateEmailSubjectRequest: GenerateEmailSubjectRequest,
    teamId: TeamId,
  )(
    implicit Logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext,
  ): Future[GenerateEmailSubjectResult] = {

    ws
      .url(s"$baseUrl/api/content/generate-email-subject")
      .withHttpHeaders(
        "X-TRACE-ID" -> Logger.logTraceId
      )
      .post(
        Json.toJson(generateEmailSubjectRequest)
      )
      .flatMap { response =>

        if (response.status != 200) {

          createSrAiApiAuditLog(
            status = ProspectColGenStatus.Failed,
            teamId = teamId,
            consumedTokenCountGenerationOutput = None,
            consumedTokenCountPromptInput = None,
            model = generateEmailSubjectRequest.options.model,
            flow = LlmFlow.EmailSubjectGeneration,
          )

          Logger.shouldNeverHappen(
            msg = s"Failed to generate email subject: ${response.status} :: ${response.body} :: teamId: $teamId"
          )

          checkErrorAndLockAi(model = generateEmailSubjectRequest.options.model, status = response.status, response.json)

          Future.failed(
            SrAiApiFailedException(
              message = s"Failed to generate email subject: ${response.status} :: ${response.body}",
              statusCode = response.status,
            )
          )

        } else {

          Json.parse(response.body)
            .validate[GenerateEmailSubjectResult] match {

            case JsError(errors) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Failed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = None,
                consumedTokenCountPromptInput = None,
                model = generateEmailSubjectRequest.options.model,
                flow = LlmFlow.EmailSubjectGeneration,
              )

              Logger.shouldNeverHappen(
                msg = s"Failed to parse generate email subject response: ${response.body} :: errors: $errors:: teamId: $teamId"
              )

              Future.failed(
                new Exception(s"Failed to parse generate email subject response: ${response.body} :: errors: $errors")
              )

            case JsSuccess(value, _) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Completed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = value.token_usage.prompt_tokens,
                consumedTokenCountPromptInput = value.token_usage.completion_tokens,
                model = generateEmailSubjectRequest.options.model,
                flow = LlmFlow.EmailSubjectGeneration,
              )

              Future.successful(value)

          }

        }

      }

  }

  def regenerateEmailBody(
    regenerateEmailBodyRequest: RegenerateEmailBodyRequest,
    teamId: TeamId,
  )(
    implicit Logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext,
  ): Future[RegenerateEmailBodyResult] = {

    ws
      .url(s"$baseUrl/api/content/regenerate-email-body")
      .withHttpHeaders(
        "X-TRACE-ID" -> Logger.logTraceId
      )
      .post(
        Json.toJson(regenerateEmailBodyRequest)
      )
      .flatMap { response =>

        if (response.status != 200) {

          createSrAiApiAuditLog(
            status = ProspectColGenStatus.Failed,
            teamId = teamId,
            consumedTokenCountGenerationOutput = None,
            consumedTokenCountPromptInput = None,
            model = regenerateEmailBodyRequest.options.model,
            flow = LlmFlow.EmailBodyRegeneration,
          )

          Logger.shouldNeverHappen(
            msg = s"Failed to regenerate email body: ${response.status} :: ${response.body} :: teamId: $teamId"
          )

          checkErrorAndLockAi(model = regenerateEmailBodyRequest.options.model, status = response.status, response.json)

          Future.failed(
            SrAiApiFailedException(
              message = s"Failed to regenerate email body: ${response.status} :: ${response.body}",
              statusCode = response.status,
            )
          )

        } else {

          Json.parse(response.body)
            .validate[RegenerateEmailBodyResult] match {

            case JsError(errors) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Failed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = None,
                consumedTokenCountPromptInput = None,
                model = regenerateEmailBodyRequest.options.model,
                flow = LlmFlow.EmailBodyRegeneration,
              )

              Logger.shouldNeverHappen(
                msg = s"Failed to parse regenerate email body response: ${response.body} :: errors: $errors:: teamId: $teamId"
              )

              Future.failed(
                new Exception(s"Failed to parse regenerate email body response: ${response.body} :: errors: $errors")
              )

            case JsSuccess(value, _) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Completed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = value.token_usage.prompt_tokens,
                consumedTokenCountPromptInput = value.token_usage.completion_tokens,
                model = regenerateEmailBodyRequest.options.model,
                flow = LlmFlow.EmailBodyRegeneration,
              )

              Future.successful(value)

          }

        }

      }
  }

  def generateEmailBody(
    generateEmailBodyRequest: GenerateEmailBodyRequest,
    teamId: TeamId,
  )(
    implicit Logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext,
  ): Future[GenerateEmailBodyResult] = {

    ws
      .url(s"$baseUrl/api/content/generate-email-body")
      .withHttpHeaders(
        "X-TRACE-ID" -> Logger.logTraceId
      )
      .post(
        Json.toJson(generateEmailBodyRequest)
      )
      .flatMap { response =>

        if (response.status != 200) {

          createSrAiApiAuditLog(
            status = ProspectColGenStatus.Failed,
            teamId = teamId,
            consumedTokenCountGenerationOutput = None,
            consumedTokenCountPromptInput = None,
            model = generateEmailBodyRequest.options.model,
            flow = LlmFlow.EmailBodyGeneration,
          )

          Logger.shouldNeverHappen(
            msg = s"Failed to generate email body: ${response.status} :: ${response.body} :: teamId: $teamId"
          )

          checkErrorAndLockAi(model = generateEmailBodyRequest.options.model, status = response.status, response.json)

          Future.failed(
            SrAiApiFailedException(
              message = s"Failed to generate email body: ${response.status} :: ${response.body}",
              statusCode = response.status,
            )
          )

        } else {

          Json.parse(response.body)
            .validate[GenerateEmailBodyResult] match {

            case JsError(errors) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Failed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = None,
                consumedTokenCountPromptInput = None,
                model = generateEmailBodyRequest.options.model,
                flow = LlmFlow.EmailBodyGeneration,
              )

              Logger.shouldNeverHappen(
                msg = s"Failed to parse generate email body response: ${response.body} :: errors: $errors:: teamId: $teamId"
              )

              Future.failed(
                new Exception(s"Failed to parse generate email body response: ${response.body} :: errors: $errors")
              )

            case JsSuccess(value, _) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Completed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = value.token_usage.prompt_tokens,
                consumedTokenCountPromptInput = value.token_usage.completion_tokens,
                model = generateEmailBodyRequest.options.model,
                flow = LlmFlow.EmailBodyGeneration,
              )

              Future.successful(value)

          }

        }

      }

  }

  def classifySpamContent(
    spamDetectionRequest: SpamDetectionRequest,
    teamId: TeamId,
  )(
    implicit Logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext,
  ): Future[SpamDetectionResult] = {

    ws
      .url(s"$baseUrl/api/classify-spam")
      .withHttpHeaders(
        "X-TRACE-ID" -> Logger.logTraceId
      )
      .post(
        Json.toJson(spamDetectionRequest)
      )
      .flatMap { response =>

        if (response.status != 200) {

          createSrAiApiAuditLog(
            status = ProspectColGenStatus.Failed,
            teamId = teamId,
            consumedTokenCountGenerationOutput = None,
            consumedTokenCountPromptInput = None,
            model = spamDetectionRequest.options.model,
            flow = LlmFlow.SpamClassification,
          )

          Logger.shouldNeverHappen(
            msg = s"Failed to classify spam: ${response.status} :: ${response.body} :: teamId: $teamId"
          )

          checkErrorAndLockAi(model = spamDetectionRequest.options.model, status = response.status, response.json)

          Future.failed(
            SrAiApiFailedException(
              message = s"Failed to classify spam: ${response.status} :: ${response.body}",
              statusCode = response.status,
            )
          )

        } else {

          Json.parse(response.body)
            .validate[SpamDetectionResult] match {

            case JsError(errors) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Failed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = None,
                consumedTokenCountPromptInput = None,
                model = spamDetectionRequest.options.model,
                flow = LlmFlow.SpamClassification,
              )

              Logger.shouldNeverHappen(
                msg = s"Failed to parse spam response: ${response.body} :: errors: $errors:: teamId: $teamId"
              )

              Future.failed(
                new Exception(s"Failed to parse spam response: ${response.body} :: errors: $errors")
              )

            case JsSuccess(value, _) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Completed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = value.token_usage.prompt_tokens,
                consumedTokenCountPromptInput = value.token_usage.completion_tokens,
                model = spamDetectionRequest.options.model,
                flow = LlmFlow.SpamClassification,
              )

              Future.successful(value)

          }

        }

      }

  }

  def moderateContent(
    moderationRequest: ModerationRequest,
    teamId: TeamId,
  )(
    implicit Logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext,
  ): Future[ModerationResult] = {

    ws
      .url(s"$baseUrl/api/moderate")
      .withHttpHeaders(
        "X-TRACE-ID" -> Logger.logTraceId
      )
      .post(
        Json.toJson(moderationRequest)
      )
      .flatMap { response =>

        if (response.status != 200) {

          createSrAiApiAuditLog(
            status = ProspectColGenStatus.Failed,
            teamId = teamId,
            consumedTokenCountGenerationOutput = None,
            consumedTokenCountPromptInput = None,
            model = moderationRequest.options.model,
            flow = LlmFlow.ContentModeration,
          )

          Logger.shouldNeverHappen(
            msg = s"Failed to moderate content: ${response.status} :: ${response.body} :: teamId: $teamId"
          )

          checkErrorAndLockAi(model = moderationRequest.options.model, status = response.status, response.json)

          Future.failed(
            SrAiApiFailedException(
              message = s"Failed to moderate content: ${response.status} :: ${response.body}",
              statusCode = response.status,
            )
          )

        } else {

          Json.parse(response.body)
            .validate[ModerationResult] match {

            case JsError(errors) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Failed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = None,
                consumedTokenCountPromptInput = None,
                model = moderationRequest.options.model,
                flow = LlmFlow.ContentModeration,
              )

              Logger.shouldNeverHappen(
                msg = s"Failed to parse ModerationResult: ${response.body} :: errors: $errors:: teamId: $teamId"
              )

              Future.failed(
                new Exception(s"Failed to parse ModerationResult: ${response.body} :: errors: $errors")
              )

            case JsSuccess(value, _) =>

              createSrAiApiAuditLog(
                status = ProspectColGenStatus.Completed,
                teamId = teamId,
                consumedTokenCountGenerationOutput = value.token_usage.prompt_tokens,
                consumedTokenCountPromptInput = value.token_usage.completion_tokens,
                model = moderationRequest.options.model,
                flow = LlmFlow.ContentModeration,
              )

              Future.successful(value)

          }

        }

      }

  }

  /**
   * @function generateHyperPersonalizedCommunicationV2
   * @description Generates a hyper-personalized email using the AI API.
   * @param prospectData The prospect data
   * @param campaignContext Campaign context details
   * @param stepContext Step-specific context
   * @param previousCommunications Previous communications with prospect
   * @param orgId Organization ID
   * @return A future containing the generated email subject and body
   */
  def generateHyperPersonalizedCommunicationV2(
                                      prospectData: ProspectFieldsResult,
                                      campaignContext: CampaignAIGenerationContext,
                                      stepContext: StepContext,
                                      previousCommunications: List[PreviousFollowUp],
                                      orgId: OrgId
                                    )(
    implicit Logger: SRLogger,
    ws: WSClient,
    materializer: org.apache.pekko.stream.Materializer,
    system: org.apache.pekko.actor.ActorSystem,
    ec: ExecutionContext,
  ): Future[LlmResponseData] = {

    // Create the API request with prospect and campaign data
    val requestBody = Json.obj(
      "prospect_data" -> Json.toJson(prospectData),
      "campaign_context" -> Json.toJson(campaignContext),
      "step_context" -> Json.toJson(stepContext),
      "previous_communications" -> Json.toJson(previousCommunications),
      // Add API options
      "options" -> AiApiOptions.getGoogleAiApiOptions()
    )

    ws
      .url(s"$baseUrl/api/content/hyper-personalized-email")
      .withHttpHeaders(
        "X-TRACE-ID" -> Logger.logTraceId
      )
      .post(requestBody)
      .flatMap { response =>
        if (response.status != 200) {

          Logger.shouldNeverHappen(
            msg = s"Failed to generate hyper-personalized email: ${response.status} :: ${response.body} :: orgId: ${orgId.id}"
          )

          checkErrorAndLockAi(model = GoogleAiModel.Gemini25Flash, status = response.status, response.json)

          Future.failed(
            SrAiApiFailedException(
              message = s"Failed to generate hyper-personalized email: ${response.status} :: ${response.body}",
              statusCode = response.status,
            )
          )
        } else {
          // Parse response body using the case class
          Json.parse(response.body).validate[HyperPersonalizedEmailResult] match {
            case JsSuccess(result, _) =>

              // MARK: - What it does: Create LlmResponseData with actual prompt and output from AI API audit_data
              // MARK: - Why it is like this: Only for hyper_personalized_email_generation flow we store detailed prompt and output information
              val llmResponseData = result.audit_data match {
                case Some(auditData) =>
                  // Use actual prompts and outputs from the AI API
                  LlmResponseData(
                    prompt = Json.obj(
                      "system_prompt" -> auditData.prompts.system_prompt,
                      "user_prompt" -> auditData.prompts.user_prompt,
                      "main_content_prompt" -> auditData.prompts.main_content_prompt,
                      "subject_call" -> Json.obj(
                        "system" -> auditData.prompts.subject_call.system,
                        "prompt" -> auditData.prompts.subject_call.prompt.getOrElse("")
                      ),
                      "body_call" -> Json.obj(
                        "system" -> auditData.prompts.body_call.system,
                        "messages" -> auditData.prompts.body_call.messages.getOrElse(List.empty)
                      )
                    ),
                    output = Json.obj(
                      "subject_response" -> Json.obj(
                        "text" -> auditData.outputs.subject_response.text,
                        "usage" -> Json.obj(
                          "prompt_tokens" -> auditData.outputs.subject_response.usage.prompt_tokens,
                          "completion_tokens" -> auditData.outputs.subject_response.usage.completion_tokens,
                          "total_tokens" -> auditData.outputs.subject_response.usage.total_tokens
                        )
                      ),
                      "body_response" -> Json.obj(
                        "text" -> auditData.outputs.body_response.text,
                        "usage" -> Json.obj(
                          "prompt_tokens" -> auditData.outputs.body_response.usage.prompt_tokens,
                          "completion_tokens" -> auditData.outputs.body_response.usage.completion_tokens,
                          "total_tokens" -> auditData.outputs.body_response.usage.total_tokens
                        )
                      ),
                      "final_output" -> Json.obj(
                        "subject" -> auditData.outputs.final_output.subject,
                        "body" -> auditData.outputs.final_output.body
                      )
                    ),
                    consumedTokenCountPromptInput = result.token_usage.prompt_tokens,
                    consumedTokenCountGenerationOutput = result.token_usage.completion_tokens
                  )
                case None =>
                  // Fallback to basic data if audit_data is not available
                  LlmResponseData(
                    prompt = Json.obj(
                      "prospect_data" -> Json.toJson(prospectData),
                      "campaign_context" -> Json.toJson(campaignContext),
                      "step_context" -> Json.toJson(stepContext),
                      "previous_communications" -> Json.toJson(previousCommunications),
                      "request_body" -> requestBody
                    ),
                    output = Json.obj(
                      "subject" -> result.generated_email.subject,
                      "body" -> result.generated_email.body
                    ),
                    consumedTokenCountPromptInput = result.token_usage.prompt_tokens,
                    consumedTokenCountGenerationOutput = result.token_usage.completion_tokens
                  )
              }

              Future.successful(llmResponseData)

            case JsError(errors) =>

              Logger.shouldNeverHappen(
                msg = s"Failed to parse hyper-personalized email response: ${response.body} :: errors: $errors :: orgId: ${orgId.id}"
              )

              Future.failed(
                new Exception(s"Failed to parse hyper-personalized email response: ${response.body} :: errors: $errors")
              )
          }
        }
      }
  }

  def checkErrorAndLockAi(model: AiModel, status: Int, error_body: JsValue)(implicit Logger: SRLogger): Unit = {
    model match {
      case GoogleAiModel.Gemini25Flash =>
        SrAiApi.checkIfWeLockTheGeminiModel(status, error_body) match {
          case Some(lockDuration) =>
            srAiLockJedisDAO.acquireLock(aiModel = model, lockDuration)
          case None =>
          //Do Nothing
        }
      case OpenAiModel.Gpt4Turbo |
           OpenAiModel.Gpt41Mini |
           OpenAiModel.Gpt4o =>
        SrAiApi.checkIfWeLockOpenAiModels(status, error_body) match {
          case Some(lockDuration) =>
            srAiLockJedisDAO.acquireLock(aiModel = model, lockDuration)
          case None =>
          //Do Nothing
        }
    }
  }

}

object SrAiApi {
  /**
   * @see https://ai.google.dev/gemini-api/docs/troubleshooting
   * 400 INVALID_ARGUMENT: This error occurs when the request body is malformed, such as having a typo or missing a required field. The solution is to check the API reference for the correct request format and ensure all required fields are included.
   * 400 FAILED_PRECONDITION: This indicates that the Gemini API free tier is not available in your country, and you need to enable billing on your project in Google AI Studio.
   * 403 PERMISSION_DENIED: This error means your API key doesn't have the required permissions. You should verify that your API key is set correctly and has the necessary access.
   * 404 NOT_FOUND: This occurs when the requested resource, such as an image, audio, or video file, wasn't found. Ensure all parameters in your request are valid for your API version.
   * 429 RESOURCE_EXHAUSTED: This indicates that you've exceeded the rate limit for requests. You may need to request a quota increase if necessary.
   * 500 INTERNAL: An unexpected error occurred on Google's side, possibly due to an input context that is too long. You might need to reduce your input context or switch to another model temporarily.
   * 503 UNAVAILABLE: The service may be temporarily overloaded or down. You can try switching to another model or waiting and retrying your request.
   * 504 DEADLINE_EXCEEDED: The service couldn't finish processing within the deadline, often due to a prompt that is too large. Consider setting a larger timeout in your client request.
   * This method checks if we should lock the Gemini model based on the status code.
   * @param status The HTTP status code
   * @param error_body The error body as JsValue
   * @param Logger Implicit logger for logging
   * @return Option[Int] indicating lock duration in seconds, if applicable
   */
  def checkIfWeLockTheGeminiModel(status: Int, error_body: JsValue)(implicit Logger: SRLogger): Option[Int] = {
    status match {
      case 429 =>
        // 429 RESOURCE_EXHAUSTED: 
        // This indicates that you've exceeded the rate limit for requests. 
        //You may need to request a quota increase if necessary.
        Logger.fatal(s"Rate limit exceeded for Gemini: $error_body")
        Some(300) // 5 minutes
      case _ =>
        None
    }
  }
    /**
     * Code	Overview
     * 401 - Invalid Authentication	Cause: Invalid Authentication
     * Solution: Ensure the correct API key and requesting organization are being used.
     * 401 - Incorrect API key provided	Cause: The requesting API key is not correct.
     * Solution: Ensure the API key used is correct, clear your browser cache, or generate a new one.
     * 401 - You must be a member of an organization to use the API	Cause: Your account is not part of an organization.
     * Solution: Contact us to get added to a new organization or ask your organization manager to invite you to an organization.
     * 403 - Country, region, or territory not supported	Cause: You are accessing the API from an unsupported country, region, or territory.
     * Solution: Please see this page for more information.
     * 429 - Rate limit reached for requests	Cause: You are sending requests too quickly.
     * Solution: Pace your requests. Read the Rate limit guide.
     * 429 - You exceeded your current quota, please check your plan and billing details	Cause: You have run out of credits or hit your maximum monthly spend.
     * Solution: Buy more credits or learn how to increase your limits.
     * 500 - The server had an error while processing your request	Cause: Issue on our servers.
     * Solution: Retry your request after a brief wait and contact us if the issue persists. Check the status page.
     * 503 - The engine is currently overloaded, please try again later	Cause: Our servers are experiencing high traffic.
     * Solution: Please retry your requests after a brief wait.
     * 503 - Slow Down	Cause: A sudden increase in your request rate is impacting service reliability.
     * Solution: Please reduce your request rate to its original level, maintain a consistent rate for at least 15 minutes, and then gradually increase it.
     *
   * @function checkIfWeLockOpenAiModels
   * @description Determines if a lock should be applied for OpenAI models based on the status code.
   * @param status The HTTP status code
   * @param error_body The error body as JsValue
   * @param Logger Implicit logger for logging
   * @return Option[Int] indicating lock duration in seconds, if applicable
   */
  def checkIfWeLockOpenAiModels(status: Int, error_body: JsValue)(implicit Logger: SRLogger): Option[Int] = {
    status match {
      case 429 =>
        // 429 Rate limit reached for requests
        // Cause: You are sending requests too quickly.
        // Solution: Pace your requests. Read the Rate limit guide.
        Logger.fatal(s"Rate limit exceeded for OpenAI: $error_body")
        Some(300) // 5 minutes
      case _ =>
        None
    }
  }

}
