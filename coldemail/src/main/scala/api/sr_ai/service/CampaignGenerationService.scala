package api.sr_ai.service

import api.accounts.TeamId
import api.gpt.CreateStepsRequest
import api.sr_ai.apis.SrAiApi
import api.sr_ai.models.*
import play.api.libs.ws.WSClient
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}

class CampaignGenerationService(
  srAiApi: SrAiApi,
) {

  def generateCampaign(
    teamId: TeamId,
    userInputs: CreateStepsRequest,
    shouldIncludeCallStep: Boolean,
  )(
    implicit Logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext,
  ): Future[CampaignGenerationResult] = {

    srAiApi.generateCampaign(
      teamId = teamId,
      campaignGenerationRequest = CampaignGenerationRequest(
        user_inputs = userInputs,
        should_include_call_step = shouldIncludeCallStep,
        options = AiApiOptions.getGoogleAiApiOptions(),
      ),
    )

  }

}
