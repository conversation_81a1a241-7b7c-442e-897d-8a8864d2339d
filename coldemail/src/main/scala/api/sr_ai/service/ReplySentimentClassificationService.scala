package api.sr_ai.service

import api.accounts.TeamId
import api.accounts.models.OrgId
import api.accounts.service.OrganizationService
import api.campaigns.CPTuple
import api.campaigns.models.WillResumeAtUpdatedBy
import api.campaigns.services.CampaignService
import api.emails.dao_service.EmailScheduledDAOService
import api.prospects.FindThreadMessageFlow
import api.prospects.dao_service.ProspectDAOService
import api.sr_ai.apis.SrAiApi
import api.sr_ai.models.{AiApiOptions, MqSrAiApiMessage, OutOfOfficeRequest, OutOfOfficeResult, SrAiApiForMQError, TokenUsage}
import api.team_inbox.model.ReplySentimentChannelType.AllChannelSentiments
import api.team_inbox.model.{ReplySentimentChannelType, ReplySentimentTypeData, ReplySentimentUpdatedBy}
import api.team_inbox.service.{ReplySentimentForTeam, ReplySentimentService, UpdateReplySentimentFormForDAO}
import eventframework.MessageObject.EmailMessageObject
import eventframework.ProspectObject
import play.api.libs.ws.WSClient
import sr_scheduler.models.ChannelType.EmailChannel
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class ReplySentimentClassificationService(
                                           emailScheduledDAOService: EmailScheduledDAOService,
                                           srAiApi: SrAiApi,
                                           campaignService: CampaignService,
                                           prospectDAOService: ProspectDAOService,
                                           replySentimentService: ReplySentimentService,
                                           organizationService: OrganizationService
                                         ) {

  def updateWillResumeAtIfOutOfOffice(
                                       msg: MqSrAiApiMessage.MqOutOfOfficeCheckMessage,
                                     )(
                                       implicit Logger: SRLogger,
                                       ec: ExecutionContext,
                                       ws: WSClient
                                     ): Future[Either[SrAiApiForMQError, TokenUsage]] = {

    emailScheduledDAOService.findThreadMessages(
      threadId = msg.email_thread_id,
      teamId = msg.team_id,
      allTrackingDomains = Seq(),
      findThreadMessageFlow = FindThreadMessageFlow.GetConversationEmails
    ) match {
      case Failure(exception) =>
        Future.successful(Left(SrAiApiForMQError.SQLError(exception)))

      case Success(emailThreadMessages) =>

        val latestReplyFromProspectOpt: Option[EmailMessageObject] = emailThreadMessages
          .filter(_.from_user == false)
          .maxByOption(_.sent_at)


        latestReplyFromProspectOpt match {

          case None =>

            val errMsg = s"No reply from prospect found in email thread ${msg.email_thread_id}"

            Logger.shouldNeverHappen(
              msg = s"$errMsg. teamId: ${msg.team_id.id} :: campaignId: ${msg.campaign_id.id} :: prospectIds: ${msg.prospect_ids.map(_.id)}",
            )

            Future.successful(
              Left(SrAiApiForMQError.NotFoundError(new Exception(errMsg)))
            )


          case Some(latestReplyFromProspect) =>
            srAiApi.classifyOutOfOffice(
              teamId = msg.team_id,
              outOfOfficeRequest = OutOfOfficeRequest(
                emailBody = latestReplyFromProspect.body,
                subject = latestReplyFromProspect.subject,
                receivedDate = latestReplyFromProspect.sent_at.toString("yyyy-MM-dd"),
                options = AiApiOptions.getGoogleAiApiOptions(),
              )
            ).flatMap {
              case Left(err) =>
                Future.successful(Left(err))

              case Right(outOfOfficeResult) => {
                if (
                  outOfOfficeResult.is_out_of_office &&
                    outOfOfficeResult.return_date.isDefined
                ) {

                  val tryOfLastReplyProspect: Try[ProspectObject] = prospectDAOService.find(
                    byProspectIds = msg.prospect_ids.map(_.id),
                    byProspectEmails = Seq(latestReplyFromProspect.from.email),
                    teamId = msg.team_id.id,
                    byCampaignId = Some(msg.campaign_id.id),
                    Logger = Logger,
                  ).flatMap { prospects =>

                    prospects.find(p => p.email.contains(latestReplyFromProspect.from.email)) match {

                      case None =>

                        val errMsg = "Campaign prospect not found to update will resume at"

                        Logger.shouldNeverHappen(
                          msg = s"$errMsg. teamId: ${msg.team_id.id} :: campaignId: ${msg.campaign_id.id} :: prospectIds: ${msg.prospect_ids.map(_.id)} :: latestReplyFromProspectEmail: ${latestReplyFromProspect.from.email}"
                        )

                        Failure(
                          new Exception(errMsg)
                        )

                      case Some(value) =>

                        Success(value)

                    }

                  }

                  val returnDate = outOfOfficeResult.return_date.get.plusDays(1)

                  Future.fromTry {

                    tryOfLastReplyProspect.flatMap { lastReplyProspect =>

                      campaignService.setProspectsToResumeLater(
                        willResumeAt = returnDate,

                        willResumeAtTimezone = lastReplyProspect.timezone.getOrElse("UTC"),

                        willResumeAtUpdatedBy = WillResumeAtUpdatedBy.SmartreachAi,

                        teamId = msg.team_id.id,
                        logger = Logger,

                        campaignProspects = msg.prospect_ids.map { p =>
                          CPTuple(
                            campaign_id = msg.campaign_id.id,
                            prospect_id = p.id,
                          )
                        },

                        permittedAccountIdsForProspects = Seq(msg.account_id.id),
                      ) match {

                        case Failure(exception) =>

                          Logger.shouldNeverHappen(
                            msg = s"Failed to set prospects to resume later. teamId: ${msg.team_id.id} :: emailThreadId: ${msg.email_thread_id} :: campaignId: ${msg.campaign_id.id}  :: prospectIds: ${msg.prospect_ids.map(_.id)}",
                            err = Some(exception),
                          )

                          Success(Left(SrAiApiForMQError.SQLError(exception)))

                        case Success(updatedProspectIds) =>

                          if (updatedProspectIds.isEmpty) {

                            Logger.shouldNeverHappen(
                              msg = s"No prospect updated for resume at. teamId: ${msg.team_id.id} :: emailThreadId: ${msg.email_thread_id} :: campaignId: ${msg.campaign_id.id}  :: prospectIds: ${msg.prospect_ids.map(_.id)}",
                            )

                            Success(Right(outOfOfficeResult.token_usage))

                          } else {

                            Success(Right(outOfOfficeResult.token_usage))

                          }

                      }

                    }

                  }

                } else {

                  Future.successful(Right(outOfOfficeResult.token_usage))

                }
              }
            }

        }
    }

  }


  /**
   * A simple wrapper around srAiApi.analyzeEmailConversation
   * that analyzes an email message and returns its sentiment
   *
   * @param message  The email message to analyze
   * @param aiConfig Configuration for the AI provider
   * @param ec       Execution context for async operations
   * @param logger   Logger for logging operations
   * @return Future containing the ReplySentimentTypeData or None if no sentiment is detected
   */
  def analyzeConversation(
                           messages: Seq[EmailMessageObject],
                           aiConfig: AiApiOptions,
                           teamId: TeamId,
                           primaryProspectId: Long,
                           replySentimentsForTeam: List[ReplySentimentForTeam],
                           replySentimentChannelType: ReplySentimentChannelType
                         )(implicit ec: ExecutionContext, wsClient: WSClient, logger: SRLogger): Future[Either[SrAiApiForMQError, (Option[ReplySentimentTypeData], TokenUsage)]] = {


    val empty_token = TokenUsage(
      prompt_tokens = None,
      completion_tokens = None,
      total_tokens = None
    )
    replySentimentChannelType match {
      case ReplySentimentChannelType.AllChannelSentiments => Future.successful(Right((None, empty_token)))
      case ReplySentimentChannelType.CallChannelType => Future.successful(Right((None, empty_token)))
      case ReplySentimentChannelType.EmailChannelType => {
        srAiApi.analyzeEmailConversation(
          messages = messages,
          aiConfig = aiConfig,
          teamId = teamId,
          primaryProspectId = primaryProspectId,
          replySentimentsForTeam = ReplySentimentService.filterSentimentsByChannel(
            replySentimentsForTeam = replySentimentsForTeam,
            channelType = ReplySentimentChannelType.EmailChannelType)

        )
      }
      case ReplySentimentChannelType.LinkedinChannelType => Future.successful(Right((None, empty_token)))
      case ReplySentimentChannelType.SmsChannelType => Future.successful(Right((None, empty_token)))
      case ReplySentimentChannelType.WhatsappChannelType => Future.successful(Right((None, empty_token)))
      case ReplySentimentChannelType.GeneralChannelType => Future.successful(Right((None, empty_token)))
    }


  }

  def analyzeConversationV2(
                             replySentimentData: MqSrAiApiMessage.MQReplySentimentForTrackerData
                           )(implicit ec: ExecutionContext, wsClient: WSClient, Logger: SRLogger): Future[Either[SrAiApiForMQError, TokenUsage]] = {
    for {
      reply_sentimments_for_team: List[ReplySentimentForTeam] <- Future.fromTry {
        replySentimentService.getReplySentimentsForTeam(
          team_id = replySentimentData.team_id.id,
          reply_sentiment_channel_type = AllChannelSentiments
        )
      }
      threadData: Seq[EmailMessageObject] <- Future.fromTry {
        emailScheduledDAOService.findThreadMessages(
          threadId = replySentimentData.email_thread_id,
          teamId = replySentimentData.team_id,
          allTrackingDomains = Seq(),
          findThreadMessageFlow = FindThreadMessageFlow.GetConversationEmails
        )
      }

      orgId: OrgId <- Future.fromTry {
        organizationService.getOrgIdFromTeamId(
          teamId = replySentimentData.team_id
        )
      }

      check_sentiment: Either[SrAiApiForMQError, (Option[ReplySentimentTypeData], TokenUsage)] <- analyzeConversation(
        messages = threadData,
        aiConfig = AiApiOptions.getGoogleAiApiOptions(),
        teamId = replySentimentData.team_id,
        primaryProspectId = replySentimentData.prospect_ids.head.id,
        replySentimentsForTeam = reply_sentimments_for_team.filter(_.reply_sentiment.getReplySentimentChannelType == ReplySentimentChannelType.EmailChannelType),
        replySentimentChannelType = ReplySentimentChannelType.EmailChannelType
      )

    } yield {
      check_sentiment match {
        case Left(err) =>
          Left(err)


        case Right(value) =>
          val (check_sentiment_with_ai, token_use) = value
          if (check_sentiment_with_ai.isDefined) {
            val selectedSentiment = reply_sentimments_for_team.find(
              p =>
                p.reply_sentiment.getDisplayNameForType == check_sentiment_with_ai.get.getDisplayNameForType &&
                  p.reply_sentiment.getReplySentimentType == check_sentiment_with_ai.get.getReplySentimentType &&
                  p.reply_sentiment.getSubcategoryName == check_sentiment_with_ai.get.getSubcategoryName

            )

            selectedSentiment match {
              case Some(value) =>

                replySentimentService.addOrUpdateReplySentiment(
                  updateReplySentimentFormForDAO = List(
                    UpdateReplySentimentFormForDAO.getUpdateReplySentiment(
                      campaign_id = Some(replySentimentData.campaign_id.id),
                      prospect_id = replySentimentData.prospect_ids.map(_.id),
                      email_thread_id = replySentimentData.email_thread_id,
                    )
                  ),
                  channelType = EmailChannel,
                  team_id = replySentimentData.team_id.id,
                  reply_sentiment_uuid = value.uuid,
                  accountId = replySentimentData.account_id.id,
                  reply_sentiment_updated_by = ReplySentimentUpdatedBy.SmartreachAi
                ) match {
                  case Success(value) =>

                    Right(token_use)
                  case Failure(exception) =>

                    Left(SrAiApiForMQError.SQLError(exception))

                }

              case None => Logger.shouldNeverHappen(s"Sentiment given by AI didnt match team Level sentiments --- sentiment from AI - ${check_sentiment_with_ai.get.getDisplayNameForType}: ${check_sentiment_with_ai.get.getReplySentimentType.display_name}")
                Right(token_use)

            }

          } else {
            Right(token_use)
          }

      }

    }
  }
}
