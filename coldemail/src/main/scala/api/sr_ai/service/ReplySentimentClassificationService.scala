package api.sr_ai.service

import api.campaigns.CPTuple
import api.campaigns.models.WillResumeAtUpdatedBy
import api.campaigns.services.CampaignService
import api.emails.dao_service.EmailScheduledDAOService
import api.prospects.FindThreadMessageFlow
import api.prospects.dao_service.ProspectDAOService
import api.sr_ai.apis.SrAiApi
import api.sr_ai.models.{AiApiOptions, MqSrAiApiMessage, OutOfOfficeRequest, OutOfOfficeResult, SrAiApiForMQError, TokenUsage}
import eventframework.MessageObject.EmailMessageObject
import eventframework.ProspectObject
import play.api.libs.ws.WSClient
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class ReplySentimentClassificationService(
                                           emailScheduledDAOService: EmailScheduledDAOService,
                                           srAiApi: SrAiApi,
                                           campaignService: CampaignService,
                                           prospectDAOService: ProspectDAOService,
                                         ) {

  def updateWillResumeAtIfOutOfOffice(
                                       msg: MqSrAiApiMessage.MqOutOfOfficeCheckMessage,
                                     )(
                                       implicit Logger: SRLogger,
                                       ec: ExecutionContext,
                                       ws: WSClient
                                     ): Future[Either[SrAiApiForMQError, TokenUsage]] = {

    emailScheduledDAOService.findThreadMessages(
      threadId = msg.email_thread_id,
      teamId = msg.team_id,
      allTrackingDomains = Seq(),
      findThreadMessageFlow = FindThreadMessageFlow.GetConversationEmails
    ) match {
      case Failure(exception) =>
        Future.successful(Left(SrAiApiForMQError.SQLError(exception)))

      case Success(emailThreadMessages) =>

        val latestReplyFromProspectOpt: Option[EmailMessageObject] = emailThreadMessages
          .filter(_.from_user == false)
          .maxByOption(_.sent_at)


        latestReplyFromProspectOpt match {

          case None =>

            val errMsg = s"No reply from prospect found in email thread ${msg.email_thread_id}"

            Logger.shouldNeverHappen(
              msg = s"$errMsg. teamId: ${msg.team_id.id} :: campaignId: ${msg.campaign_id.id} :: prospectIds: ${msg.prospect_ids.map(_.id)}",
            )

            Future.successful(
              Left(SrAiApiForMQError.NotFoundError(new Exception(errMsg)))
            )


          case Some(latestReplyFromProspect) =>
            srAiApi.classifyOutOfOffice(
              teamId = msg.team_id,
              outOfOfficeRequest = OutOfOfficeRequest(
                emailBody = latestReplyFromProspect.body,
                subject = latestReplyFromProspect.subject,
                receivedDate = latestReplyFromProspect.sent_at.toString("yyyy-MM-dd"),
                options = AiApiOptions.getGoogleAiApiOptions(),
              )
            ).flatMap {
              case Left(err) =>
                Future.successful(Left(err))

              case Right(outOfOfficeResult) => {
                if (
                  outOfOfficeResult.is_out_of_office &&
                    outOfOfficeResult.return_date.isDefined
                ) {

                  val tryOfLastReplyProspect: Try[ProspectObject] = prospectDAOService.find(
                    byProspectIds = msg.prospect_ids.map(_.id),
                    byProspectEmails = Seq(latestReplyFromProspect.from.email),
                    teamId = msg.team_id.id,
                    byCampaignId = Some(msg.campaign_id.id),
                    Logger = Logger,
                  ).flatMap { prospects =>

                    prospects.find(p => p.email.contains(latestReplyFromProspect.from.email)) match {

                      case None =>

                        val errMsg = "Campaign prospect not found to update will resume at"

                        Logger.shouldNeverHappen(
                          msg = s"$errMsg. teamId: ${msg.team_id.id} :: campaignId: ${msg.campaign_id.id} :: prospectIds: ${msg.prospect_ids.map(_.id)} :: latestReplyFromProspectEmail: ${latestReplyFromProspect.from.email}"
                        )

                        Failure(
                          new Exception(errMsg)
                        )

                      case Some(value) =>

                        Success(value)

                    }

                  }

                  val returnDate = outOfOfficeResult.return_date.get.plusDays(1)

                  Future.fromTry {

                    tryOfLastReplyProspect.flatMap { lastReplyProspect =>

                      campaignService.setProspectsToResumeLater(
                        willResumeAt = returnDate,

                        willResumeAtTimezone = lastReplyProspect.timezone.getOrElse("UTC"),

                        willResumeAtUpdatedBy = WillResumeAtUpdatedBy.SmartreachAi,

                        teamId = msg.team_id.id,
                        logger = Logger,

                        campaignProspects = msg.prospect_ids.map { p =>
                          CPTuple(
                            campaign_id = msg.campaign_id.id,
                            prospect_id = p.id,
                          )
                        },

                        permittedAccountIdsForProspects = Seq(msg.account_id.id),
                      ) match {

                        case Failure(exception) =>

                          Logger.shouldNeverHappen(
                            msg = s"Failed to set prospects to resume later. teamId: ${msg.team_id.id} :: emailThreadId: ${msg.email_thread_id} :: campaignId: ${msg.campaign_id.id}  :: prospectIds: ${msg.prospect_ids.map(_.id)}",
                            err = Some(exception),
                          )

                          Success(Left(SrAiApiForMQError.SQLError(exception)))

                        case Success(updatedProspectIds) =>

                          if (updatedProspectIds.isEmpty) {

                            Logger.shouldNeverHappen(
                              msg = s"No prospect updated for resume at. teamId: ${msg.team_id.id} :: emailThreadId: ${msg.email_thread_id} :: campaignId: ${msg.campaign_id.id}  :: prospectIds: ${msg.prospect_ids.map(_.id)}",
                            )

                            Success(Right(outOfOfficeResult.token_usage))

                          } else {

                            Success(Right(outOfOfficeResult.token_usage))

                          }

                      }

                    }

                  }

                } else {

                  Future.successful(Right(outOfOfficeResult.token_usage))

                }
              }
            }

        }
    }

  }

}
