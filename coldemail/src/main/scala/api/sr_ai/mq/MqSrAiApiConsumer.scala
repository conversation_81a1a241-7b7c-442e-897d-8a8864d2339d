package api.sr_ai.mq

import api.AppConfig
import api.accounts.TeamId
import api.accounts.dao.TeamsDAO
import api.emails.dao_service.EmailScheduledDAOService
import api.prospects.FindThreadMessageFlow
import api.sr_ai.models.{AiApiOptions, AiModel, MqSrAiApiMessage, MqSrAiApiMessageWithLogId, SrAiApiFailedException, SrAiApiForMQError, TokenUsage}
import api.sr_ai.service.ReplySentimentClassificationService
import api.team.service.TeamService
import api.team_inbox.model.ReplySentimentChannelType.{AllChannelSentiments, EmailChannelType}
import api.team_inbox.model.{ReplySentimentTypeData, ReplySentimentUpdatedBy}
import api.team_inbox.service.*
import eventframework.MessageObject.EmailMessageObject
import io.sr.billing_common.models.PlanID
import org.apache.pekko.actor.ActorSystem
import play.api.libs.ws.WSClient
import sr_scheduler.models.ChannelType
import utils.SRLogger
import utils.mq.services.{MQConfig, MQConsumer, MQDoNotNackException, MQPublisherService}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import api.accounts.service.OrganizationService
import api.accounts.models.OrgId
import api.llm.dao.{LlmAuditLogDAO, UpdateStatusData}
import api.team_inbox.model.ReplySentimentChannelType
import api.columns.ProspectColGenStatus
import api.llm.models.LlmFlow
import api.sr_ai.dao.SrAiLockJedisDAO

//FIXME SRAiAPI move the publish to SrAiApiAuditLog
class MqSrAiApiConsumer(
                         replySentimentClassificationService: ReplySentimentClassificationService,
                         mqSrAiApiPublisher: MqSrAiApiPublisher,
                         teamService: TeamService,
                         llmAuditLogDAO: LlmAuditLogDAO,
                         srAiLockJedisDAO: SrAiLockJedisDAO
                       ) extends MQConsumer[MqSrAiApiMessageWithLogId] {

  val queueBaseName: String = MQConfig.mqSrAiApi
  val prefetchCount: Int = MQConfig.mqSrAiApiPrefetchCount

  protected val mqPublisherService: MQPublisherService[MqSrAiApiMessageWithLogId] = mqSrAiApiPublisher


  def startConsumer()(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {
    startMsgConsumer(queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  // MARK: - What it does: Helper method to update audit log status on success or failure
  // MARK: - Why it is like this: Provides consistent audit logging across all message processing flows
  private def updateAuditLogStatus(
                                    logId: Long,
                                    teamId: TeamId,
                                    status: ProspectColGenStatus,
                                    logger: SRLogger,
                                    updateCount: Boolean,
                                    consumedTokenCountPromptInput: Option[Int] = None,
                                    consumedTokenCountGenerationOutput: Option[Int] = None
                                  ): Unit = {
    val updateData = UpdateStatusData(
      id = logId,
      teamId = teamId,
      newStatus = status,
      consumedTokenCountPromptInput = consumedTokenCountPromptInput,
      consumedTokenCountGenerationOutput = consumedTokenCountGenerationOutput
    )

    llmAuditLogDAO.updateStatus(
      updateStatusData = List(updateData),
      updateCount = updateCount
    ) match {
      case Success(value) => //Do nothing
      case Failure(exception) =>
        logger.shouldNeverHappen(s"Failed to update audit log status for logId: $logId", Some(exception))

    }
  }

  // MARK: - What it does: Helper method to handle SrAiApiForMQError and update audit log status accordingly
  // MARK: - Why it is like this: Provides consistent error handling and audit logging for all SrAiApiForMQError types
  private def handleSrAiApiForMQError(
                                       error: SrAiApiForMQError,
                                       logId: Long,
                                       teamId: TeamId,
                                       logger: SRLogger
                                     ): Unit = {
    error match {
      case SrAiApiForMQError.SQLError(_) =>
        updateAuditLogStatus(
          logId = logId,
          teamId = teamId,
          status = ProspectColGenStatus.Pending,
          logger = logger,
          updateCount = false
        )
      case SrAiApiForMQError.NotFoundError(_) =>
        updateAuditLogStatus(
          logId = logId,
          teamId = teamId,
          status = ProspectColGenStatus.Pending,
          logger = logger,
          updateCount = false
        )
      case SrAiApiForMQError.SrAiApiFailed(_) =>
        updateAuditLogStatus(
          logId = logId,
          teamId = teamId,
          status = ProspectColGenStatus.Pending,
          logger = logger,
          updateCount = true
        )
      case SrAiApiForMQError.SrAiApiFailedToParse(_) =>
        updateAuditLogStatus(
          logId = logId,
          teamId = teamId,
          status = ProspectColGenStatus.Pending,
          logger = logger,
          updateCount = false
        )
    }
  }

  override def processMessage(
                               msg: MqSrAiApiMessageWithLogId,
                             )(
                               implicit ws: WSClient,
                               ec: ExecutionContext,
                               system: ActorSystem,
                             ): Future[Unit] = {
    val message = msg.message
    val logId = msg.logId

    given Logger: SRLogger = new SRLogger(s"[MqSrApiConsumer] tid_${message.team_id} logId_${logId}")

    teamService.checkIfWePushForSrAiApiCall(
      allow_using_sr_ai_api = message.allow_using_sr_ai_api,
      teamId = message.team_id,
      plan_id = message.plan_id
    ) match {
      case Success(value) =>
        message match {

          case outOfOfficeMsg: MqSrAiApiMessage.MqOutOfOfficeCheckMessage =>
            implicit val oooLogger: SRLogger = Logger.appendLogRequestId(
              appendLogReqId = s"MqOutOfOfficeCheckMessage - tread_id_${outOfOfficeMsg.email_thread_id}",
            )

            processAiCall(
              aiModel = LlmFlow.OutOfOfficeClassification.model,
              allow_using_sr_ai_api = value.allow_using_sr_ai_api_for_ooo,
              logId = logId,
              message = outOfOfficeMsg,
              method = replySentimentClassificationService.updateWillResumeAtIfOutOfOffice(
                msg = outOfOfficeMsg
              )
            )( ec = ec, wsClient = ws, Logger = oooLogger)

          case replySentimentData: MqSrAiApiMessage.MQReplySentimentForTrackerData =>
            given LoggerForReplySentiment: SRLogger = Logger.appendLogRequestId(s"processing tread_id_${replySentimentData.email_thread_id}")
            processAiCall(
              aiModel = LlmFlow.ReplySentiment.model,
              allow_using_sr_ai_api = value.allow_using_sr_ai_api_for_reply_sentiment,
              logId = logId,
              message = replySentimentData,
              method = replySentimentClassificationService.analyzeConversationV2(
                replySentimentData = replySentimentData
              )
            )( ec = ec, wsClient = ws, Logger = LoggerForReplySentiment)

        }
      case Failure(exception) =>
        Logger.shouldNeverHappen(s"Failed to get checkIfWePushForSrAiApiCall", Some(exception))
        updateAuditLogStatus(
          logId = logId,
          teamId = message.team_id,
          status = ProspectColGenStatus.Pending,
          logger = Logger,
          updateCount = false
        )
        Future.failed(MQDoNotNackException(message = exception.getMessage, cause = exception))
    }


  }


  private def processAiCall(
                             aiModel: AiModel,
                             allow_using_sr_ai_api: Boolean,
                             logId: Long,
                             message: MqSrAiApiMessage,
                             method: => Future[Either[SrAiApiForMQError, TokenUsage]]
                           )(implicit ec: ExecutionContext, wsClient: WSClient, Logger: SRLogger): Future[Unit] = {

    if (!srAiLockJedisDAO.checkLock(aiModel = aiModel)) {
      if (allow_using_sr_ai_api) {
        method.map {
          case Left(err) =>
            handleSrAiApiForMQError(
              error = err,
              logId = logId,
              teamId = message.team_id,
              logger = Logger
            )
          case Right(token) =>
            updateAuditLogStatus(
              logId = logId,
              teamId = message.team_id,
              status = ProspectColGenStatus.Completed,
              logger = Logger,
              updateCount = true,
              consumedTokenCountGenerationOutput = token.prompt_tokens,
              consumedTokenCountPromptInput = token.completion_tokens,
            )
        }.recoverWith { err =>

          updateAuditLogStatus(
            logId = logId,
            teamId = message.team_id,
            status = ProspectColGenStatus.Pending,
            logger = Logger,
            updateCount = true
          )
          Logger.shouldNeverHappen(
            msg = s"Failed to process ${message.call_type.key} teamId: ${message.team_id.id} :: emailThreadId: ${message.email_thread_id} :: campaignId: ${message.campaign_id}  :: prospectIds: ${message.prospect_ids}",
            err = Some(err),
          )

          Future.failed(
            MQDoNotNackException(
              message = err.getMessage,
              cause = err,
            )
          )

        }

      } else Future.successful({
        updateAuditLogStatus(
          logId = logId,
          teamId = message.team_id,
          status = ProspectColGenStatus.Failed,
          logger = Logger,
          updateCount = true
        )
      })
    } else {
      updateAuditLogStatus(
        logId = logId,
        teamId = message.team_id,
        status = ProspectColGenStatus.Pending,
        logger = Logger,
        updateCount = false
      )
      Future.failed(MQDoNotNackException("The model is locked we will try again later"))

    }

  }

}

