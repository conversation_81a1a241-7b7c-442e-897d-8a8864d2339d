package api.sr_ai.mq

import api.AppConfig
import api.accounts.TeamId
import api.accounts.dao.TeamsDAO
import api.emails.dao_service.EmailScheduledDAOService
import api.prospects.FindThreadMessageFlow
import api.sr_ai.models.{AiApiOptions, MqSrAiApiMessage, MqSrAiApiMessageWithLogId, SrAiApiFailedException, SrAiApiForMQError}
import api.sr_ai.service.ReplySentimentClassificationService
import api.team.service.TeamService
import api.team_inbox.model.ReplySentimentChannelType.{AllChannelSentiments, EmailChannelType}
import api.team_inbox.model.{ReplySentimentTypeData, ReplySentimentUpdatedBy}
import api.team_inbox.service.*
import eventframework.MessageObject.EmailMessageObject
import io.sr.billing_common.models.PlanID
import org.apache.pekko.actor.ActorSystem
import play.api.libs.ws.WSClient
import sr_scheduler.models.ChannelType
import utils.SRLogger
import utils.mq.services.{MQConfig, MQConsumer, MQDoNotNackException, MQPublisherService}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import api.accounts.service.OrganizationService
import api.accounts.models.OrgId
import api.llm.dao.{LlmAuditLogDAO, UpdateStatusData}
import api.team_inbox.model.ReplySentimentChannelType
import api.columns.ProspectColGenStatus
import api.llm.models.LlmFlow
import api.sr_ai.dao.SrAiLockJedisDAO

//FIXME SRAiAPI move the publish to SrAiApiAuditLog
class MqSrAiApiConsumer(
                         replySentimentClassificationService: ReplySentimentClassificationService,
                         mqSrAiApiPublisher: MqSrAiApiPublisher,
                         replySentimentService: ReplySentimentService,
                         emailScheduledDAOService: EmailScheduledDAOService,
                         teamService: TeamService,
                         organizationService: OrganizationService,
                         llmAuditLogDAO: LlmAuditLogDAO,
                         srAiLockJedisDAO: SrAiLockJedisDAO
                       ) extends MQConsumer[MqSrAiApiMessageWithLogId] {

  val queueBaseName: String = MQConfig.mqSrAiApi
  val prefetchCount: Int = MQConfig.mqSrAiApiPrefetchCount

  protected val mqPublisherService: MQPublisherService[MqSrAiApiMessageWithLogId] = mqSrAiApiPublisher


  def startConsumer()(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {
    startMsgConsumer(queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  // MARK: - What it does: Helper method to update audit log status on success or failure
  // MARK: - Why it is like this: Provides consistent audit logging across all message processing flows
  private def updateAuditLogStatus(
                                    logId: Long,
                                    teamId: TeamId,
                                    status: ProspectColGenStatus,
                                    logger: SRLogger,
                                    updateCount: Boolean,
                                    consumedTokenCountPromptInput: Option[Int] = None,
                                    consumedTokenCountGenerationOutput: Option[Int] = None
                                  ): Unit = {
    val updateData = UpdateStatusData(
      id = logId,
      teamId = teamId,
      newStatus = status,
      consumedTokenCountPromptInput = consumedTokenCountPromptInput,
      consumedTokenCountGenerationOutput = consumedTokenCountGenerationOutput
    )

    llmAuditLogDAO.updateStatus(
      updateStatusData = List(updateData),
      updateCount = updateCount
    ) match {
      case Success(value) => //Do nothing
      case Failure(exception) =>
        logger.shouldNeverHappen(s"Failed to update audit log status for logId: $logId", Some(exception))

    }
  }

  override def processMessage(
                               msg: MqSrAiApiMessageWithLogId,
                             )(
                               implicit ws: WSClient,
                               ec: ExecutionContext,
                               system: ActorSystem,
                             ): Future[Unit] = {
    val message = msg.message
    val logId = msg.logId

    given Logger: SRLogger = new SRLogger(s"[MqSrApiConsumer] tid_${message.team_id} logId_${logId}")

    teamService.checkIfWePushForSrAiApiCall(
      allow_using_sr_ai_api = message.allow_using_sr_ai_api,
      teamId = message.team_id,
      plan_id = message.plan_id
    ) match {
      case Success(value) =>
        message match {

          case outOfOfficeMsg: MqSrAiApiMessage.MqOutOfOfficeCheckMessage =>
            implicit val oooLogger: SRLogger = Logger.appendLogRequestId(
              appendLogReqId = s"MqOutOfOfficeCheckMessage - tread_id_${outOfOfficeMsg.email_thread_id}",
            )
            if (!srAiLockJedisDAO.checkLock(aiModel = LlmFlow.OutOfOfficeClassification.model)) {
              if (!value.allow_using_sr_ai_api_for_ooo) {

                updateAuditLogStatus(
                  logId = logId,
                  teamId = message.team_id,
                  status = ProspectColGenStatus.Failed,
                  logger = Logger,
                  updateCount = true
                )
                Future.successful({})

              } else {

                replySentimentClassificationService.updateWillResumeAtIfOutOfOffice(
                  msg = outOfOfficeMsg,
                ).map {

                  case Left(err) =>
                    err match {
                      case SrAiApiForMQError.SQLError(error) =>
                        updateAuditLogStatus(
                          logId = logId,
                          teamId = message.team_id,
                          status = ProspectColGenStatus.Pending,
                          logger = Logger,
                          updateCount = false
                        )
                      case SrAiApiForMQError.NotFoundError(error) =>
                        updateAuditLogStatus(
                          logId = logId,
                          teamId = message.team_id,
                          status = ProspectColGenStatus.Pending,
                          logger = Logger,
                          updateCount = false
                        )
                      case SrAiApiForMQError.SrAiApiFailed(error) =>
                        updateAuditLogStatus(
                          logId = logId,
                          teamId = message.team_id,
                          status = ProspectColGenStatus.Pending,
                          logger = Logger,
                          updateCount = true
                        )
                      case SrAiApiForMQError.SrAiApiFailedToParse(error) =>
                        updateAuditLogStatus(
                          logId = logId,
                          teamId = message.team_id,
                          status = ProspectColGenStatus.Pending,
                          logger = Logger,
                          updateCount = false
                        )
                    }
                  case Right(token) =>
                  updateAuditLogStatus(
                    logId = logId,
                    teamId = message.team_id,
                    status = ProspectColGenStatus.Completed,
                    logger = Logger,
                    updateCount = true,
                    consumedTokenCountGenerationOutput = token.prompt_tokens,
                    consumedTokenCountPromptInput = token.completion_tokens,
                  )

                }.recoverWith { err =>

                    updateAuditLogStatus(
                      logId = logId,
                      teamId = message.team_id,
                      status = ProspectColGenStatus.Pending,
                      logger = Logger,
                      updateCount = true
                    )
                    oooLogger.shouldNeverHappen(
                      msg = s"Failed to classify and update will resume at if out of office. teamId: ${outOfOfficeMsg.team_id.id} :: emailThreadId: ${outOfOfficeMsg.email_thread_id} :: campaignId: ${outOfOfficeMsg.campaign_id.id}  :: prospectIds: ${outOfOfficeMsg.prospect_ids.map(_.id)}",
                      err = Some(err),
                    )

                    Future.failed(
                      MQDoNotNackException(
                        message = err.getMessage,
                        cause = err,
                      )
                    )

                }

              }
            } else {
              updateAuditLogStatus(
                logId = logId,
                teamId = message.team_id,
                status = ProspectColGenStatus.Pending,
                logger = Logger,
                updateCount = false
              )
              Future.failed(MQDoNotNackException("The model is locked we will try again later"))

            }

          case replySentimentData: MqSrAiApiMessage.MQReplySentimentForTrackerData =>
            given LoggerForReplySentiment: SRLogger = Logger.appendLogRequestId(s"processing tread_id_${replySentimentData.email_thread_id}")

            if (!srAiLockJedisDAO.checkLock(aiModel = LlmFlow.ReplySentiment.model)) {
              if (value.allow_using_sr_ai_api_for_reply_sentiment) {
                val res: Future[Try[Boolean]] = for {
                  reply_sentimments_for_team: List[ReplySentimentForTeam] <- Future.fromTry {
                    replySentimentService.getReplySentimentsForTeam(
                      team_id = replySentimentData.team_id.id,
                      reply_sentiment_channel_type = AllChannelSentiments
                    )
                  }
                  threadData: Seq[EmailMessageObject] <- Future.fromTry {
                    emailScheduledDAOService.findThreadMessages(
                      threadId = replySentimentData.email_thread_id,
                      teamId = replySentimentData.team_id,
                      allTrackingDomains = Seq(),
                      findThreadMessageFlow = FindThreadMessageFlow.GetConversationEmails
                    )
                  }

                  orgId: OrgId <- Future.fromTry {
                    organizationService.getOrgIdFromTeamId(
                      teamId = replySentimentData.team_id
                    )
                  }

                  check_sentiment_with_ai: Option[ReplySentimentTypeData] <- replySentimentService.analyzeConversation(
                    messages = threadData,
                    aiConfig = AiApiOptions.getGoogleAiApiOptions(),
                    teamId = replySentimentData.team_id,
                    primaryProspectId = replySentimentData.prospect_ids.head.id,
                    replySentimentsForTeam = reply_sentimments_for_team.filter(_.reply_sentiment.getReplySentimentChannelType == ReplySentimentChannelType.EmailChannelType), 
                    replySentimentChannelType = ReplySentimentChannelType.EmailChannelType
                    )

                } yield {
                  if (check_sentiment_with_ai.isDefined) {
                    val selectedSentiment = reply_sentimments_for_team.find(
                      p =>
                        p.reply_sentiment.getDisplayNameForType == check_sentiment_with_ai.get.getDisplayNameForType &&
                          p.reply_sentiment.getReplySentimentType == check_sentiment_with_ai.get.getReplySentimentType &&
                          p.reply_sentiment.getSubcategoryName == check_sentiment_with_ai.get.getSubcategoryName

                    )

                    selectedSentiment match {
                      case Some(value) => replySentimentService.addOrUpdateReplySentiment(
                        updateReplySentimentFormForDAO = List(
                          UpdateReplySentimentFormForDAO.getUpdateReplySentiment(
                            campaign_id = Some(replySentimentData.campaign_id.id),
                            prospect_id = replySentimentData.prospect_ids.map(_.id),
                            email_thread_id = replySentimentData.email_thread_id,
                          )
                        ),
                        channelType = ChannelType.EmailChannel,
                        team_id = replySentimentData.team_id.id,
                        reply_sentiment_uuid = value.uuid,
                        accountId = replySentimentData.account_id.id,
                        reply_sentiment_updated_by = ReplySentimentUpdatedBy.SmartreachAi
                      )

                      case None => Logger.shouldNeverHappen(s"Sentiment given by AI didnt match team Level sentiments --- sentiment from AI - ${check_sentiment_with_ai.get.getDisplayNameForType}: ${check_sentiment_with_ai.get.getReplySentimentType.display_name}")
                        Success(false)

                    }

                  } else Success(false)

                }

                res
                  .map(_ => {
                    updateAuditLogStatus(
                      logId = logId,
                      teamId = message.team_id,
                      status = ProspectColGenStatus.Completed,
                      logger = Logger,
                      updateCount = true
                    )
                  })
                  .recoverWith {

                    case err: SrAiApiFailedException if err.statusCode >= 500 =>

                      updateAuditLogStatus(
                        logId = logId,
                        teamId = message.team_id,
                        status = ProspectColGenStatus.Pending,
                        logger = Logger,
                        updateCount = true
                      )
                      LoggerForReplySentiment.shouldNeverHappen(
                        msg = s"Failed to process reply sentiments. statusCode: ${err.statusCode} :: teamId: ${replySentimentData.team_id.id} :: emailThreadId: ${replySentimentData.email_thread_id} :: campaignId: ${replySentimentData.campaign_id}  :: prospectIds: ${replySentimentData.prospect_ids}",
                        err = Some(err),
                      )

                      Future.failed(
                        MQDoNotNackException(
                          message = err.getMessage,
                          cause = err,
                        )
                      )
                    case err =>

                      updateAuditLogStatus(
                        logId = logId,
                        teamId = message.team_id,
                        status = ProspectColGenStatus.Pending,
                        logger = Logger,
                        updateCount = true
                      )
                      LoggerForReplySentiment.shouldNeverHappen(
                        msg = s"Failed to process reply sentiments. teamId: ${replySentimentData.team_id.id} :: emailThreadId: ${replySentimentData.email_thread_id} :: campaignId: ${replySentimentData.campaign_id}  :: prospectIds: ${replySentimentData.prospect_ids}",
                        err = Some(err),
                      )

                      Future.failed(
                        MQDoNotNackException(
                          message = err.getMessage,
                          cause = err,
                        )
                      )

                  }
              } else Future.successful({
                updateAuditLogStatus(
                  logId = logId,
                  teamId = message.team_id,
                  status = ProspectColGenStatus.Failed,
                  logger = Logger,
                  updateCount = true
                )
              })
            } else {
              updateAuditLogStatus(
                logId = logId,
                teamId = message.team_id,
                status = ProspectColGenStatus.Pending,
                logger = Logger,
                updateCount = false
              )
              Future.failed(MQDoNotNackException("The model is locked we will try again later"))

            }


        }
      case Failure(exception) =>
        Logger.shouldNeverHappen(s"Failed to get checkIfWePushForSrAiApiCall", Some(exception))
        updateAuditLogStatus(
          logId = logId,
          teamId = message.team_id,
          status = ProspectColGenStatus.Pending,
          logger = Logger,
          updateCount = false
        )
        Future.failed(MQDoNotNackException(message = exception.getMessage, cause = exception))
    }


  }

}

