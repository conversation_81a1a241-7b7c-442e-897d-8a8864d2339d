package api.sr_ai.models

import api.AppConfig
import play.api.libs.json.{<PERSON><PERSON>, OWrites}

/**
  * Configuration for AI provider used for sentiment analysis
  *
  * @param model      The AI model to use (e.g., "gpt-4o")
  * @param apiKey     API key for the AI provider
  * @param apiBaseUrl The base URL of the AI provider's API. If not provided, defaults openai.
  */
case class AiApiOptions(
  model: AiModel,
  apiKey: String,
  apiBaseUrl: Option[String] = None,
)

object AiApiOptions {

  implicit val writes: OWrites[AiApiOptions] = Json.writes[AiApiOptions]

  def getOpenAiApiOptions(
    model: AiModel = OpenAiModel.Gpt41Mini, // default
  ): AiApiOptions = {

    AiApiOptions(

      model = model,

      apiKey = AppConfig.GPTApiKey,

      apiBaseUrl = None,

    )

  }

  def getGoogleAiApiOptions(
    model: AiModel = GoogleAiModel.Gemini25Flash, // default
  ): AiApiOptions = {

    AiApiOptions(

      model = model,

      apiKey = AppConfig.GeminiApiKey,

      apiBaseUrl = Some(AppConfig.GeminiBaseUrl),

    )

  }

}
