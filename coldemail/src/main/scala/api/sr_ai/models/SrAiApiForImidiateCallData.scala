package api.sr_ai.models

import play.api.libs.json.{Format, Json, Reads}

sealed trait SrAiApiForImidiateCallData {
  val token_usage: TokenUsage
}


case class GenerateEmailReplyFromThreadResult(
                                               generated_reply: String,
                                               token_usage: TokenUsage,
                                             ) extends SrAiApiForImidiateCallData

object GenerateEmailReplyFromThreadResult {
  implicit val reads: Reads[GenerateEmailReplyFromThreadResult] = Json.reads[GenerateEmailReplyFromThreadResult]
}

case class CampaignGenerationResult(
                                     campaign_sequence: List[SrAiGeneratedCampaignStep],
                                     token_usage: TokenUsage,
                                   ) extends SrAiApiForImidiateCallData

object CampaignGenerationResult {
  implicit val reads: Reads[CampaignGenerationResult] = Json.reads[CampaignGenerationResult]
}
case class ShortenTextResult(
                              shortened_text: String,
                              token_usage: TokenUsage,
                            ) extends SrAiApiForImidiateCallData

object ShortenTextResult {
  implicit val reads: Reads[ShortenTextResult] = Json.reads[ShortenTextResult]
}

case class RephraseTextResult(
                               rephrased_text: String,
                               token_usage: TokenUsage,
                             ) extends SrAiApiForImidiateCallData

object RephraseTextResult {
  implicit val reads: Reads[RephraseTextResult] = Json.reads[RephraseTextResult]
}

case class RegenerateEmailBodyResult(
                                      regenerated_email_body: String,
                                      token_usage: TokenUsage
                                    ) extends SrAiApiForImidiateCallData

object RegenerateEmailBodyResult {
  implicit val reads: Reads[RegenerateEmailBodyResult] = Json.reads[RegenerateEmailBodyResult]
}

case class RegenerateEmailSubjectResult(
                                         regenerated_subject: String,
                                         token_usage: TokenUsage,
                                       ) extends SrAiApiForImidiateCallData

object RegenerateEmailSubjectResult {
  implicit val reads: Reads[RegenerateEmailSubjectResult] = Json.reads[RegenerateEmailSubjectResult]
}

case class GenerateEmailSubjectResult(
                                       generated_email_subject: String,
                                       token_usage: TokenUsage,
                                     ) extends SrAiApiForImidiateCallData

object GenerateEmailSubjectResult {
  implicit val reads: Reads[GenerateEmailSubjectResult] = Json.reads[GenerateEmailSubjectResult]
}
