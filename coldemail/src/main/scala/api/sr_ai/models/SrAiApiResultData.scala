package api.sr_ai.models

import org.joda.time.DateTime
import play.api.libs.json.{Format, Json, Reads}
import play.api.libs.json.JodaReads._

sealed trait SrAiApiResultData {
  val token_usage: TokenUsage
}


case class GenerateEmailReplyFromThreadResult(
                                               generated_reply: String,
                                               token_usage: TokenUsage,
                                             ) extends SrAiApiResultData

object GenerateEmailReplyFromThreadResult {
  implicit val reads: Reads[GenerateEmailReplyFromThreadResult] = Json.reads[GenerateEmailReplyFromThreadResult]
}

case class CampaignGenerationResult(
                                     campaign_sequence: List[SrAiGeneratedCampaignStep],
                                     token_usage: TokenUsage,
                                   ) extends SrAiApiResultData

object CampaignGenerationResult {
  implicit val reads: Reads[CampaignGenerationResult] = Json.reads[CampaignGenerationResult]
}
case class ShortenTextResult(
                              shortened_text: String,
                              token_usage: TokenUsage,
                            ) extends SrAiApiResultData

object ShortenTextResult {
  implicit val reads: Reads[ShortenTextResult] = Json.reads[ShortenTextResult]
}

case class RephraseTextResult(
                               rephrased_text: String,
                               token_usage: TokenUsage,
                             ) extends SrAiApiResultData

object RephraseTextResult {
  implicit val reads: Reads[RephraseTextResult] = Json.reads[RephraseTextResult]
}

case class RegenerateEmailBodyResult(
                                      regenerated_email_body: String,
                                      token_usage: TokenUsage
                                    ) extends SrAiApiResultData

object RegenerateEmailBodyResult {
  implicit val reads: Reads[RegenerateEmailBodyResult] = Json.reads[RegenerateEmailBodyResult]
}

case class RegenerateEmailSubjectResult(
                                         regenerated_subject: String,
                                         token_usage: TokenUsage,
                                       ) extends SrAiApiResultData

object RegenerateEmailSubjectResult {
  implicit val reads: Reads[RegenerateEmailSubjectResult] = Json.reads[RegenerateEmailSubjectResult]
}

case class GenerateEmailSubjectResult(
                                       generated_email_subject: String,
                                       token_usage: TokenUsage,
                                     ) extends SrAiApiResultData

object GenerateEmailSubjectResult {
  implicit val reads: Reads[GenerateEmailSubjectResult] = Json.reads[GenerateEmailSubjectResult]
}

case class GenerateEmailBodyResult(
                                    generated_email_body: String,
                                    token_usage: TokenUsage,
                                  ) extends SrAiApiResultData

object GenerateEmailBodyResult {
  implicit val reads: Reads[GenerateEmailBodyResult] = Json.reads[GenerateEmailBodyResult]
}


case class SpamDetectionResult(
                                subject: String,
                                email: String,
                                analysis: SpamAnalysis,
                                token_usage: TokenUsage,
                              ) extends SrAiApiResultData

object SpamDetectionResult {
  implicit val reads: Reads[SpamDetectionResult] = Json.reads[SpamDetectionResult]
}


case class ModerationResult(
                             hate: Boolean,
                             threatening: Boolean,
                             self_harm: Boolean,
                             sexual: Boolean,
                             sexual_minors: Boolean,
                             violence: Boolean,
                             violence_graphic: Boolean,
                             token_usage: TokenUsage,
                           ) extends SrAiApiResultData

object ModerationResult {
  implicit val reads: Reads[ModerationResult] = Json.reads[ModerationResult]
}

case class OutOfOfficeResult(
                              is_out_of_office: Boolean,
                              return_date: Option[DateTime], // Date in YYYY-MM-DD format or null
                              token_usage: TokenUsage,
                            ) extends SrAiApiResultData

object OutOfOfficeResult {

  implicit val reads: Reads[OutOfOfficeResult] = Json.reads[OutOfOfficeResult]

}



sealed trait SrAiApiForMQError

object SrAiApiForMQError {
  
  case class SQLError(error: Throwable) extends SrAiApiForMQError
  case class NotFoundError(error: Throwable) extends SrAiApiForMQError
  case class SrAiApiFailed(error: SrAiApiFailedException) extends SrAiApiForMQError
  case class SrAiApiFailedToParse(error: Throwable) extends SrAiApiForMQError
  
}
