package api.sr_ai.models

import play.api.libs.json.{<PERSON><PERSON>, Reads}

case class ModerationResult(
  hate: Boolean,
  threatening: Bo<PERSON>an,
  self_harm: <PERSON><PERSON>an,
  sexual: Boolean,
  sexual_minors: Bo<PERSON>an,
  violence: Boolean,
  violence_graphic: <PERSON><PERSON>an,
  token_usage: TokenUsage,
)

object ModerationResult {
  implicit val reads: Reads[ModerationResult] = Json.reads[ModerationResult]
}
