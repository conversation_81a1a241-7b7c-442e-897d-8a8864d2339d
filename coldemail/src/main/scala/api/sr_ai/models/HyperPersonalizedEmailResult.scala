package api.sr_ai.models

import play.api.libs.json.{J<PERSON>, Reads, JsValue}

/**
 * Case class representing the generated email content structure
 */
case class GeneratedEmail(
  subject: String,
  body: String
)

object GeneratedEmail {
  implicit val reads: Reads[GeneratedEmail] = Json.reads[GeneratedEmail]
}

// MARK: - What it does: Case classes for audit data structures
// MARK: - Why it is like this: To capture actual LLM prompts and outputs for hyper_personalized_email_generation flow

case class PromptCallData(
  system: String,
  prompt: Option[String] = None,
  messages: Option[List[JsValue]] = None
)

case class ResponseData(
  text: String,
  usage: TokenUsage
)

case class AuditPrompts(
  system_prompt: String,
  user_prompt: String,
  main_content_prompt: String,
  subject_call: PromptCallData,
  body_call: PromptCallData
)

case class AuditOutputs(
  subject_response: ResponseData,
  body_response: ResponseData,
  final_output: GeneratedEmail
)

case class AuditData(
  prompts: AuditPrompts,
  outputs: AuditOutputs
)

object PromptCallData {
  implicit val reads: Reads[PromptCallData] = Json.reads[PromptCallData]
}

object ResponseData {
  implicit val reads: Reads[ResponseData] = Json.reads[ResponseData]
}

object AuditPrompts {
  implicit val reads: Reads[AuditPrompts] = Json.reads[AuditPrompts]
}

object AuditOutputs {
  implicit val reads: Reads[AuditOutputs] = Json.reads[AuditOutputs]
}

object AuditData {
  implicit val reads: Reads[AuditData] = Json.reads[AuditData]
}

/**
 * Response structure from the hyper-personalized email generation endpoint
 */
case class HyperPersonalizedEmailResult(
  generated_email: GeneratedEmail,
  token_usage: TokenUsage,
  audit_data: Option[AuditData] = None
)

object HyperPersonalizedEmailResult {
  implicit val reads: Reads[HyperPersonalizedEmailResult] = Json.reads[HyperPersonalizedEmailResult]
} 