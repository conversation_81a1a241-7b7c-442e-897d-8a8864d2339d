package api.sr_ai.models

import play.api.libs.json.{Json, OWrites}

case class EmailMessageForGeneratingReply(
  from_email: String,
  email_body: String,
)

object EmailMessageForGeneratingReply {
  implicit val writes: OWrites[EmailMessageForGeneratingReply] = Json.writes[EmailMessageForGeneratingReply]
}


case class GenerateEmailReplyFromThreadRequest(
  email_thread: Seq[EmailMessageForGeneratingReply],
  reply_recipient_email: String,
  options: AiApiOptions,
)

object GenerateEmailReplyFromThreadRequest {
  implicit val writes: OWrites[GenerateEmailReplyFromThreadRequest] = Json.writes[GenerateEmailReplyFromThreadRequest]
}
