package api.sr_ai.models

import play.api.libs.json.*

import scala.util.{Failure, Success, Try}

case class SpamAnalysis(
  spam_likelihood: SpamLikelihood,
  reason: String,
)

object SpamAnalysis {
  implicit val reads: Reads[SpamAnalysis] = Json.reads[SpamAnalysis]
}



sealed trait SpamLikelihood

object SpamLikelihood {

  private val low = "low"
  private val medium = "medium"
  private val high = "high"

  case object Low extends SpamLikelihood {
    override def toString: String = low
  }

  case object Medium extends SpamLikelihood {
    override def toString: String = medium
  }

  case object High extends SpamLikelihood {
    override def toString: String = high
  }

  def fromKey(key: String): Try[SpamLikelihood] = Try {

    key match {

      case `low` => Low

      case `medium` => Medium

      case `high` => High

      case _ => throw new IllegalArgumentException(s"Invalid SpamLikelihood key: $key")

    }

  }

  implicit def writes: Writes[SpamLikelihood] = (s: SpamLikelihood) => {
    JsString(s.toString)
  }

  implicit def reads: Reads[SpamLikelihood] = (json: JsValue) => {

    fromKey(key = json.as[String]) match {

      case Success(spamLikelihood: SpamLikelihood) => JsSuccess(spamLikelihood)

      case Failure(e) => JsError(s"Invalid SpamLikelihood value error = ${e.getMessage}")

    }

  }

}
