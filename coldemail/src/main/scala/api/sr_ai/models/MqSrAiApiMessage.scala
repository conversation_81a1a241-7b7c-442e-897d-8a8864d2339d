package api.sr_ai.models

import api.accounts.TeamId
import api.accounts.models.AccountId
import api.call.models.CallType
import api.campaigns.services.CampaignId
import api.prospects.models.ProspectId
import api.sr_ai.models.SrApiCallType.{OOO, ReplySentiment}
import io.sr.billing_common.models.PlanID
import play.api.libs.json.{Format, JsError, JsSuccess, JsResult, JsV<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>s, Writes}

import scala.util.{Try, Success, Failure}

enum SrApiCallType(val key: String) {
  case ReplySentiment extends SrApiCallType("reply_sentiment")
  case OOO extends SrApiCallType("out_of_office")

  override def toString: String = key
}
sealed trait MqSrAiApiMessage extends Serializable {
  val team_id: TeamId
  val email_thread_id: Long
  val account_id: AccountId
  val campaign_id: CampaignId
  val prospect_ids: List[ProspectId]
  val allow_using_sr_ai_api: Boolean
  val plan_id: PlanID
  val call_type: SrApiCallType
}

object MqSrAiApiMessage {

  case class MQReplySentimentForTrackerData(
    team_id: TeamId,
    email_thread_id: Long,
    account_id: AccountId,
    campaign_id: CampaignId,
    prospect_ids: List[ProspectId],
    allow_using_sr_ai_api: Boolean,
    plan_id: PlanID
  ) extends MqSrAiApiMessage {
    override val call_type: SrApiCallType = ReplySentiment
  }
  object MQReplySentimentForTrackerData {
    given format: Format[MQReplySentimentForTrackerData] = new Format[MQReplySentimentForTrackerData] {
      override def writes(o: MQReplySentimentForTrackerData): JsValue = {
        Json.obj(
          "team_id" -> o.team_id.id,
          "email_thread_id" -> o.email_thread_id,
          "account_id" -> o.account_id.id,
          "campaign_id" -> o.campaign_id.id,
          "prospect_ids" -> o.prospect_ids.map(_.id),
          "allow_using_sr_ai_api" -> o.allow_using_sr_ai_api,
          "plan_id" -> o.plan_id.toString,
          "call_type" -> o.call_type.key
        )
      }

      override def reads(json: JsValue): JsResult[MQReplySentimentForTrackerData] = Try {
        MQReplySentimentForTrackerData(
          team_id = TeamId((json \ "team_id").as[Long]),
          email_thread_id = (json \ "email_thread_id").as[Long],
          account_id = AccountId((json \ "account_id").as[Long]),
          campaign_id = CampaignId((json \ "campaign_id").as[Long]),
          prospect_ids = (json \ "prospect_ids").as[List[Long]].map(ProspectId),
          allow_using_sr_ai_api = (json \ "allow_using_sr_ai_api").as[Boolean],
          plan_id = PlanID.withName((json \ "plan_id").as[String]).get
        )
      } match {
        case Failure(e) => JsError(e.getMessage)
        case Success(value) => JsSuccess(value)
      }
    }
  }

  case class MqOutOfOfficeCheckMessage(
    team_id: TeamId,
    email_thread_id: Long,
    account_id: AccountId,
    campaign_id: CampaignId,
    prospect_ids: List[ProspectId],
    allow_using_sr_ai_api: Boolean,
    plan_id: PlanID
  ) extends MqSrAiApiMessage {
    override val call_type: SrApiCallType = OOO
  }

  object MqOutOfOfficeCheckMessage {
    given format: Format[MqOutOfOfficeCheckMessage] = new Format[MqOutOfOfficeCheckMessage] {
      override def writes(o: MqOutOfOfficeCheckMessage): JsValue = {
        Json.obj(
          "team_id" -> o.team_id.id,
          "email_thread_id" -> o.email_thread_id,
          "account_id" -> o.account_id.id,
          "campaign_id" -> o.campaign_id.id,
          "prospect_ids" -> o.prospect_ids.map(_.id),
          "allow_using_sr_ai_api" -> o.allow_using_sr_ai_api,
          "plan_id" -> o.plan_id.toString,
          "call_type" -> o.call_type.key
        )
      }

      override def reads(json: JsValue): JsResult[MqOutOfOfficeCheckMessage] = Try {
        MqOutOfOfficeCheckMessage(
          team_id = TeamId((json \ "team_id").as[Long]),
          email_thread_id = (json \ "email_thread_id").as[Long],
          account_id = AccountId((json \ "account_id").as[Long]),
          campaign_id = CampaignId((json \ "campaign_id").as[Long]),
          prospect_ids = (json \ "prospect_ids").as[List[Long]].map(ProspectId),
          allow_using_sr_ai_api = (json \ "allow_using_sr_ai_api").as[Boolean],
          plan_id = PlanID.withName((json \ "plan_id").as[String]).get
        )
      } match {
        case Failure(e) => JsError(e.getMessage)
        case Success(value) => JsSuccess(value)
      }
    }

  }
  
  // MARK: - What it does: JSON format for the sealed trait MqSrAiApiMessage
  // MARK: - Why it is like this: Enables proper serialization/deserialization for the sealed trait hierarchy
  implicit val format: Format[MqSrAiApiMessage] = new Format[MqSrAiApiMessage] {
    override def writes(message: MqSrAiApiMessage): JsValue = message match {
      case msg: MQReplySentimentForTrackerData => MQReplySentimentForTrackerData.format.writes(msg)
      case msg: MqOutOfOfficeCheckMessage => MqOutOfOfficeCheckMessage.format.writes(msg)
    }

    override def reads(json: JsValue): JsResult[MqSrAiApiMessage] = {
      val callType = (json \ "call_type").asOpt[String]
      callType match {
        case Some("reply_sentiment") => MQReplySentimentForTrackerData.format.reads(json)
        case Some("out_of_office") => MqOutOfOfficeCheckMessage.format.reads(json)
        case _ => JsError("Unable to determine MqSrAiApiMessage subtype from call_type field")
      }
    }
  }

}

case class MqSrAiApiMessageWithLogId(
  message: MqSrAiApiMessage,
  logId: Long
) extends Serializable

object MqSrAiApiMessageWithLogId {
  implicit val format: Format[MqSrAiApiMessageWithLogId] = Json.format[MqSrAiApiMessageWithLogId]
}
