package api.sr_ai.models

import api.accounts.TeamId
import api.accounts.models.AccountId
import api.campaigns.services.CampaignId
import api.prospects.models.ProspectId
import io.sr.billing_common.models.PlanID

sealed trait MqSrAiApiMessage extends Serializable {
  val team_id: TeamId
  val allow_using_sr_ai_api: Boolean
  val plan_id: PlanID
}

object MqSrAiApiMessage {

  case class MQReplySentimentForTrackerData(
    team_id: TeamId,
    email_thread_id: Long,
    accunt_id: Long,
    campaign_id: Long,
    prospect_ids: List[Long],
    allow_using_sr_ai_api: Boolean,
    plan_id: PlanID
  ) extends MqSrAiApiMessage

  case class MqOutOfOfficeCheckMessage(
    team_id: TeamId,
    email_thread_id: Long,
    account_id: AccountId,
    campaign_id: CampaignId,
    prospect_ids: List[ProspectId],
    allow_using_sr_ai_api: Boolean,
    plan_id: PlanID
  ) extends MqSrAiApiMessage

}
