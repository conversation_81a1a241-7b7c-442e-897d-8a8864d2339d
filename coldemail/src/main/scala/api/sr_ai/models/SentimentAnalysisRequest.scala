package api.sr_ai.models

import api.AppConfig
import api.accounts.TeamId
import api.columns.ProspectColGenStatus
import api.columns.models.ColumnDefsProspectsId
import api.emails.EmailReplySavedV3
import api.llm.dao.{LlmAuditLogDAO, LlmAuditLogInsertData}
import api.llm.models.LlmFlow
import api.prospects.models.ProspectId
import api.sr_ai.models.{AiApiOptions, AiModel, SrAiApiFailedException, TokenUsage}
import api.team_inbox.model.{ReplySentimentChannelType, ReplySentimentType, ReplySentimentTypeData}
import api.team_inbox.service.ReplySentimentForTeam
import eventframework.MessageObject
import eventframework.MessageObject.EmailMessageObject
import play.api.libs.json.*
import play.api.libs.ws.{WSClient, writeableOf_JsValue}
import sr_scheduler.models.ChannelType
import utils.{CommandExecutor, SRLogger}

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}



/**
 * Request payload for sentiment analysis API
 */
case class SentimentAnalysisRequest(
  conversationMessages: Seq[ConversationMessage],
  options: AiApiOptions,
  replySentimentsForTeam: List[String]
)

/**
 * Conversation message for sentiment analysis
 */
case class ConversationMessage(
  sender: String,
  content: String
)

object SentimentAnalysisRequest {
  implicit val writes: Writes[SentimentAnalysisRequest] = Json.writes[SentimentAnalysisRequest]
}

object ConversationMessage {
  implicit val writes: Writes[ConversationMessage] = Json.writes[ConversationMessage]
}
