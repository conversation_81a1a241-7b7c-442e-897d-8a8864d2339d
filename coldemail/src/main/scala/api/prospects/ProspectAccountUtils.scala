package api.prospects

import api.SRAPIResponse
import api.accounts._
import api.accounts.models.{OrgId, ProspectAccountUuid}
import play.api.mvc.{ActionRefiner, Request, Result, WrappedRequest}
import utils.SRLogger
import utils.uuid.services.SrUuidService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Left, Success}


case class ProspectAccountRequest[A](
  prospectAccount: ProspectAccount,
  permittedAccountIds: Seq[Long],
  actingTeamAccount: TeamMember,
  loggedinAccount: Account,
  isApiCall: <PERSON><PERSON><PERSON>,
  Logger: SRLogger,
  Response: SRAPIResponse,
  request: Request[A]
) extends WrappedRequest[A](request)


trait ProspectAccountUtils {
  protected val prospectAccountDAO: ProspectAccountDAO1
  protected val srUuidService: SrUuidService
  // TEAMEDITION: this check and all apis where this check is called

  def hasProspectAccountWithUuid(prospectAccountUuid: ProspectAccountUuid)(
    implicit ec: ExecutionContext
  ) = new ActionRefiner[PermissionRequest, ProspectAccountRequest] {

    override protected def executionContext: ExecutionContext = ec

    override def refine[A](request: PermissionRequest[A]): Future[Either[Result, ProspectAccountRequest[A]]] = {

      Future {
        val Logger = request.Logger
        val Res = request.Response
        given logger: SRLogger = request.Logger
        if (request.actingTeamAccount.isEmpty) {
          Left(Res.ForbiddenError("Please select a team first"))
        } else {

          srUuidService.getProspectAccountIdFromUuid(
            prospectAccountUuid = prospectAccountUuid,
            teamId = TeamId(request.actingTeamAccount.get.team_id)
          ) match {
            case Failure(exception) =>
              Left(Res.ServerError("There was an error. Please contact support", e = Some(exception)))
            case Success(prospectAccountId) =>
              prospectAccountDAO.findFromMaster(prospectAccountId = prospectAccountId.id) match {

                case None => Left(Res.NotFoundError("Account not found"))

                case Some(prospectAccount) =>

                  if (!request.permittedAccountIds.contains(prospectAccount.owner_id)) {
                    Left(Res.ForbiddenError("You are not authorized to do this"))
                  } else if (request.actingTeamAccount.get.team_id != prospectAccount.team_id) {
                    Left(Res.ForbiddenError("Account does not belong to the current team. Please switch view to the respective team."))
                  } else {

                    val newLogger = new SRLogger(logRequestId = s"${Logger.logRequestId} paid_${prospectAccount.id} :: ")

                    Right(ProspectAccountRequest(
                      prospectAccount = prospectAccount,
                      permittedAccountIds = request.permittedAccountIds,
                      actingTeamAccount = request.actingTeamAccount.get,
                      loggedinAccount = request.loggedinAccount,
                      isApiCall = request.isApiCall,
                      Logger = newLogger,
                      Response = new SRAPIResponse(Logger = newLogger),
                      request
                    ))
                  }

              }
          }
        }

      }


    }
  }

  def hasProspectAccount(prospectAccountId: Long)(
                        implicit ec: ExecutionContext
  ) = new ActionRefiner[PermissionRequest, ProspectAccountRequest] {

    override protected def executionContext: ExecutionContext = ec

    override def refine[A](request: PermissionRequest[A]): Future[Either[Result, ProspectAccountRequest[A]]] = {

      Future {
        val Logger = request.Logger
        val Res = request.Response

        if (request.actingTeamAccount.isEmpty) {
          Left(Res.ForbiddenError("Please select a team first"))
        } else {

          prospectAccountDAO.findFromMaster(prospectAccountId = prospectAccountId) match {

            case None => Left(Res.NotFoundError("Account not found"))

            case Some(prospectAccount) =>

              if (!request.permittedAccountIds.contains(prospectAccount.owner_id)) {
                Left(Res.ForbiddenError("You are not authorized to do this"))
              } else if (request.actingTeamAccount.get.team_id != prospectAccount.team_id) {
                Left(Res.ForbiddenError("Account does not belong to the current team. Please switch view to the respective team."))
              } else {

                val newLogger = new SRLogger(logRequestId = s"${Logger.logRequestId} paid_${prospectAccount.id} :: ")

                Right(ProspectAccountRequest(
                  prospectAccount = prospectAccount,
                  permittedAccountIds = request.permittedAccountIds,
                  actingTeamAccount = request.actingTeamAccount.get,
                  loggedinAccount = request.loggedinAccount,
                  isApiCall = request.isApiCall,
                  Logger = newLogger,
                  Response = new SRAPIResponse(Logger = newLogger),
                  request
                ))
              }

          }
        }

      }


    }
  }
}
