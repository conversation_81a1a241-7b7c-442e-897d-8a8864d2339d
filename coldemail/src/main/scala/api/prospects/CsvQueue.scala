package api.prospects

import api.accounts.TeamId
import api.accounts.models.AccountId
import api.csv_uploads.models.CsvUploadType
import api.prospects.models.SrProspectColumns
import org.joda.time.DateTime
import play.api.libs.json.*
import play.api.libs.json.JodaWrites.*
import scalikejdbc.*
import scalikejdbc.jodatime.JodaWrappedResultSet.*
import org.postgresql.util.PGobject
import play.api.Logging
import utils.{Help<PERSON>, SRLogger}
import utils.dbutils.DBUtils

import scala.util.{Failure, Success, Try}

// fixme: remove this
case class CsvQueueCreateFormDate(
  file_name: String,
  file_url: String,
  campaign_id: Option[Long],
  column_map: JsValue,
  owner_id: Option[Long] = None, // so that old ui doesnt break
  tags: Option[String],
  // encoding: Option[String],
  // force_assign_to_campaign: Option[<PERSON><PERSON><PERSON>],
  force_change_ownership: Option[<PERSON>olean],
  force_update_prospects: Option[<PERSON><PERSON><PERSON>],
  ignore_prospects_active_in_other_campaigns: Option[<PERSON>olean],
  ignore_prospects_in_other_campaigns: Option[String]
)

object CsvQueueCreateFormDate {
  implicit val reads: Reads[CsvQueueCreateFormDate] = Json.reads[CsvQueueCreateFormDate]
}


case class CsvQueueCreateFormDataV2(
  list_name: Option[String],
  file_url: String,
  campaign_id: Option[Long],
  column_map: JsValue,
  owner_id: Option[Long] = None, // so that old ui doesnt break
  tags: Option[String],
  force_change_ownership: Option[Boolean],
  force_update_prospects: Option[Boolean],
  ignore_email_empty_rows: Option[Boolean],
  ignore_prospects_active_in_other_campaigns: Option[Boolean],
  ignore_prospects_in_other_campaigns: Option[String],
  deduplication_columns: Option[Seq[SrProspectColumns]],
  csv_upload_type: CsvUploadType
)

object CsvQueueCreateFormDataV2 {
  implicit val reads: Reads[CsvQueueCreateFormDataV2] = Json.reads[CsvQueueCreateFormDataV2]
}


// delete form case class

case class CsvLog(
                   file_name: String,
                   has_been_uploaded: Boolean,
                   error: Option[String],
                   error_at:  Option[DateTime],
                   csv_upload_type: CsvUploadType,
                   uploaded_at: Option[DateTime],
                   uploaded_by: String,
                   deduplication_columns: Seq[SrProspectColumns],
                   result: JsValue
                 )

object CsvLog{
    implicit val writes: Writes[CsvLog] = Json.writes[CsvLog]
}

case class CsvQueue(
  id: Long,
  account_id: Long,
  team_id: Option[Long],
  ta_id: Option[Long],
//  loggedin_id:Long,
  file_name: String,
  file_url: String,
  list_name: Option[String],
  created_at: DateTime,
  campaign_id: Option[Long],
  tags: Option[String],
  uploaded_at: Option[DateTime],
  has_been_uploaded: Boolean,
  // encoding: Option[String],
  force_update_prospects: Option[Boolean],
  // force_assign_to_campaign: Option[Boolean],
  force_change_ownership: Option[Boolean],
  ignore_prospects_active_in_other_campaigns: Option[Boolean],
  ignore_prospects_in_other_campaigns: Option[String],
  ignore_email_empty_rows: Option[Boolean],
  column_map: JsValue,
  error: Option[String],
  error_at:  Option[DateTime],
  deduplication_columns: Option[Seq[SrProspectColumns]],
  csv_upload_type: CsvUploadType

)


object CsvQueue extends Logging {

  implicit val writes: OWrites[CsvQueue] = Json.writes[CsvQueue]

  implicit val session: AutoSession.type = AutoSession


  def hasTeamIdAndTaId(csvQueue: CsvQueue):Boolean =  {
    csvQueue.team_id.isDefined && csvQueue.ta_id.isDefined
  }


  def fromDb(rs: WrappedResultSet): CsvQueue = CsvQueue(
    id = rs.long("id"),
    team_id = rs.longOpt("team_id"),
    account_id = rs.long("account_id"),
    ta_id = rs.longOpt("ta_id"),
//    loggedin_id = rs.long("loggedin_id"),
    file_url = rs.string("file_url"),
    file_name = rs.string("file_name"),
    list_name = rs.stringOpt("list_name"),
    has_been_uploaded = rs.boolean("has_been_uploaded"),
    force_update_prospects = rs.booleanOpt("force_update_prospects"),
    // force_assign_to_campaign = rs.booleanOpt("force_assign_to_campaign"),
    force_change_ownership = rs.booleanOpt("force_change_ownership"),
    ignore_prospects_active_in_other_campaigns = rs.booleanOpt("ignore_prospects_active_in_other_campaigns"),
    ignore_prospects_in_other_campaigns = rs.stringOpt("ignore_prospects_in_other_campaigns"),
    ignore_email_empty_rows = rs.booleanOpt("ignore_email_empty_rows"),
    campaign_id = rs.longOpt("campaign_id"),
    tags = rs.stringOpt("tags"),
    created_at = rs.jodaDateTime("created_at"),
    uploaded_at = rs.jodaDateTimeOpt("uploaded_at"),
    column_map = Json.parse(rs.any("column_map").asInstanceOf[PGobject].getValue),
    error = rs.stringOpt("error"),
    error_at = rs.jodaDateTimeOpt("error_at"),
    deduplication_columns = Some(Helpers.generateSrProspectColumnList(rs.stringOpt("deduplication_columns")).getOrElse(Seq())),
    csv_upload_type = CsvUploadType.fromString(rs.string("csv_upload_type")).get
    // encoding = rs.stringOpt("encoding")
  )


  def findById(
  id: Long
  ):Option[CsvQueue] = {
    DB.readOnly{ implicit session =>
      sql"""
        SELECT * FROM csv_queue
        WHERE id = $id AND has_been_uploaded = FALSE AND error IS NULL ORDER BY created_at asc LIMIT 1;
      """
        .map(fromDb)
        .single
        .apply()

    }
  }

  def find(
    csvUploadType: CsvUploadType
  ): Try[Option[CsvQueue]] = Try {
    DB.readOnly{ implicit session =>
      sql"""
        SELECT * FROM csv_queue
        WHERE has_been_uploaded = FALSE AND error IS NULL AND csv_upload_type = ${csvUploadType.toString} ORDER BY created_at asc LIMIT 1;
      """
      .map(fromDb)
      .single
      .apply()

    }
  }

  def update(
    id: Long,
    charsetDetected: Option[String],
    result: JsValue,
    parserName: Option[String],
    parserContext: Option[JsValue],
    logger: SRLogger
  ): Try[Int] = {

    logger.info(s"CsvQueue.update called: csv_$id  :: parserName: $parserName :: parserContext: $parserContext :: result: $result ")

    DBUtils.autoCommit(
      logger = logger.appendLogRequestId("CsvQueue.update")
    ){ implicit session =>

      val parserContextSql: SQLSyntax = if (parserContext.isEmpty) sqls" " else {

        sqls" parser_context = to_jsonb(${parserContext.get.toString}::jsonb), "

      }

      sql"""
        UPDATE csv_queue SET
      has_been_uploaded = true,
      encoding = $charsetDetected,

      parser_name = $parserName,

      $parserContextSql

      result = to_json(${result.toString()}::json)

      WHERE id = $id;
"""
        .update.apply()
    }
  }

  def updateOnFail(
    id: Long,
    charsetDetected: Option[String],
    error: String,
    logger: SRLogger
  ): Try[Int] = {
    DBUtils.autoCommit(
      logger = logger.appendLogRequestId("CsvQueue.updateOnFail")
    ) { implicit session =>
      sql"""
         UPDATE csv_queue SET error = ${error}, error_at = now(), encoding = $charsetDetected WHERE id = ${id};
        """.update.apply()
    }
  }


  // fixme: remove this
  def create(
  accountId: Long,
  teamId: Long,
  ta_id: Long,
  loggedin_id: Long,
  data: CsvQueueCreateFormDate
  ): Try[CsvQueue] = Try {
    DB autoCommit { implicit session =>
      logger.info("[DATA FILE]"+data.toString)
          sql"""
        INSERT INTO csv_queue
        (
           account_id,
           team_id,
           ta_id,
           loggedin_id,
           file_name,
           file_url,
           campaign_id,
           tags,
           force_update_prospects,
           force_change_ownership,
           ignore_prospects_active_in_other_campaigns,
           ignore_prospects_in_other_campaigns,
           column_map
        )
        VALUES (
          $accountId,
          $teamId,
          $ta_id,
          $loggedin_id,
          ${data.file_name},
          ${data.file_url},
          ${data.campaign_id},
          ${data.tags},
          ${data.force_update_prospects},
          ${data.force_change_ownership},
          ${data.ignore_prospects_active_in_other_campaigns},
          ${data.ignore_prospects_in_other_campaigns},
          to_json(${data.column_map.toString}::json)
        )
        RETURNING *;
      """
      .map(fromDb)
      .single
      .apply()
        .get

    }


  }


  /*
   * Use case: prospect upload via upload csv - save csv in google after getting presigned url - Step 2.
   */

  def createV2(
    accountId: Long,
    teamId: Option[Long],
    ta_id: Option[Long],
    loggedin_id: Long,
    data: CsvQueueCreateFormDataV2
  ): Try[CsvQueue] = Try {

    val filenameEncoded = data.file_url.split("/", 0).last.split("_", 2).last
    val filename = java.net.URLDecoder.decode(filenameEncoded, java.nio.charset.StandardCharsets.UTF_8.name())

    val deduplicationColumns = if(data.deduplication_columns.isDefined && data.deduplication_columns.get.nonEmpty){
      Some(data.deduplication_columns.get.map(_.toString).mkString(","))
    } else {
      None
    }

    DB autoCommit { implicit session =>
      logger.info("[DATA FILE]"+data.toString)
      sql"""
        INSERT INTO csv_queue
        (
           account_id,
           team_id,
           ta_id,
           loggedin_id,
           file_name,
           file_url,
           list_name,
           campaign_id,
           tags,
           force_update_prospects,
           force_change_ownership,
           ignore_email_empty_rows,
           ignore_prospects_active_in_other_campaigns,
           ignore_prospects_in_other_campaigns,
           column_map,
           deduplication_columns,
           csv_upload_type
        )
        VALUES (
          $accountId,
          $teamId,
          $ta_id,
          $loggedin_id,
          $filename,
          ${data.file_url},
          ${data.list_name},
          ${data.campaign_id},
          ${data.tags},
          ${data.force_update_prospects},
          ${data.force_change_ownership},
          ${data.ignore_email_empty_rows},
          ${data.ignore_prospects_active_in_other_campaigns},
          ${data.ignore_prospects_in_other_campaigns},
          to_json(${data.column_map.toString}::json),
          $deduplicationColumns,
          ${data.csv_upload_type.toString}
        )
        RETURNING *;
      """
        .map(fromDb)
        .single
        .apply()
        .get

    }


  }


    def getCsvLogs(teamId: TeamId): Try[List[CsvLog]] = Try {
        DB.readOnly{implicit  session =>
            sql"""
                 SELECT
                    csv_queue.*,
                    CONCAT(accounts.first_name, ' ', accounts.last_name) AS full_name
                 FROM
                    csv_queue
                 JOIN accounts ON csv_queue.account_id = accounts.id
                 WHERE team_id = ${teamId.id}
                 ORDER BY id DESC
                 LIMIT 100;
               """
              .map(rs => CsvLog(
                  file_name = rs.string("file_name"),
                  has_been_uploaded = rs.boolean("has_been_uploaded"),
                  error = rs.stringOpt("error"),
                  error_at = rs.jodaDateTimeOpt("error_at"),
                  csv_upload_type = CsvUploadType.fromString(rs.string("csv_upload_type")) match {
                      case Success(uploadType) => uploadType
                      case Failure(exception) => throw exception
                  },
                  uploaded_at = rs.jodaDateTimeOpt("created_at"),
                  uploaded_by = rs.string("full_name"),
                  deduplication_columns = Helpers.generateSrProspectColumnList(rs.stringOpt("deduplication_columns")).getOrElse(Seq()),
                  result = Json.parse(rs.any("result").asInstanceOf[PGobject].getValue)
              ))
              .list
              .apply()
        }
    }

  def getLastCsvMapping(
                         teamId: TeamId,
                         accountId: AccountId
                       ): Try[Option[JsValue]] = Try {
    DB.readOnly { implicit session =>
      sql"""
         SELECT
            csv_queue.column_map
         FROM
            csv_queue
         WHERE team_id = ${teamId.id}
         and account_id = ${accountId.id}
         and has_been_uploaded = true
         and error is null
         ORDER BY id DESC
         LIMIT 1;
       """
        .map(rs => Json.parse(rs.any("column_map").asInstanceOf[PGobject].getValue))
        .single
        .apply()
    }
  }

}

