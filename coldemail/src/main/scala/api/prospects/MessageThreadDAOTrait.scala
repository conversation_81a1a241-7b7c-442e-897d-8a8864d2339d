package api.prospects

import api.accounts.TeamId
import api.emails.models.{InboxType, InboxTypeData}
import api.emails.{ConversationsSearchResponse, EmailThreadDAO, ThreadForProspectInternal}
import api.linkedin_message_threads.LinkedinMessageThreadsDAO
import api.prospects.models.ProspectId
import api.team_inbox.model.{FolderType, ReplySentimentUpdatedBy}
import api.team_inbox.service.ReplySentimentUuid
import scalikejdbc.interpolation.SQLSyntax
import scalikejdbc.{DB, DBSession, WrappedResultSet, scalikejdbcSQLInterpolationImplicitDef}
import eventframework.ConversationObjectInboxV3
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import play.api.libs.json.{JsNumber, JsSuccess, JsValue, Writes}
import utils.SRLogger
import utils.dbutils.DBUtils
import utils.uuid.services.SrUuidService

import scala.util.Try

sealed trait ThreadId {
  def id: Long

  override def toString: String
}

object ThreadId {

  case class EmailThreadId(id: Long) extends ThreadId {
    override def toString: String = id.toString
  }

  object EmailThreadId {
    implicit val writes: Writes[EmailThreadId] = new Writes[EmailThreadId] {
      override def writes(o: EmailThreadId): JsValue = JsNumber(o.id)
    }
  }

  case class LinkedinThreadId(id: Long) extends ThreadId {
    override def toString: String = id.toString
  }

}

trait MessageThreadDAOTrait {
  
  type ConversationObjectInboxV3Type <: ConversationObjectInboxV3

  type ThreadForProspectInternalType <: ThreadForProspectInternal

  def getTimelineQueryCondition(
                                 timeline: InferredQueryTimeline,
                                 mailboxFolder: MailboxFolder,
                                 team_id: Long
                               ): (SQLSyntax, SQLSyntax)

  def getMailboxQuery(mailboxFolder : MailboxFolder, teamId: Long): SQLSyntax

  def getQueryForSearchInboxEmailThreads(
                                          teamId: Long,
                                          searchKey: String,
                                          settingsIds: Seq[Long],
                                          inbox_type: InboxType,
                                          date_within: Option[DateTime],
                                          are_all_esets_accessible: Boolean,
                                          sent_at: Option[Long],
                                          last_sent_id: Option[String],
                                          folder_type: Option[FolderType]
                                        )(using Logger:SRLogger): SQLSyntax

  def getConversationsForSearch(
                                 query: SQLSyntax,
                                 inbox_type: InboxType
                               )(using logger: SRLogger): Try[List[ConversationsSearchResponse]]

  def getPaginationClauses(
                            teamId: Long,
                            validatedConvReq: ValidatedConvReq
                          ): SQLSyntax = {

    validatedConvReq match {

      case vmq: ValidatedConvReq.ValidatedMailboxReqRange =>
        val hasProspectsCondition: SQLSyntax = getMailboxQuery(mailboxFolder = vmq.mailboxFolderRequest, teamId = teamId)

        val (reply_at_condition: SQLSyntax, orderBy: SQLSyntax) = getTimelineQueryCondition(
          timeline = vmq.timeline,
          mailboxFolder = vmq.mailboxFolderRequest,
          team_id = teamId
        )

        val remaining_clauses =
          sqls""" ${hasProspectsCondition}
                 ${reply_at_condition}
                 $orderBy
                 limit ${vmq.pageSize}
                """
        remaining_clauses

      case vmq: ValidatedConvReq.ValidatedMailboxReqExact =>
        val hasProspectsCondition: SQLSyntax = getMailboxQuery(mailboxFolder = vmq.mailboxFolderRequest, teamId = teamId)

        val (reply_at_condition: SQLSyntax, orderBy: SQLSyntax) = getTimelineQueryCondition(
          timeline = vmq.timeline,
          mailboxFolder = vmq.mailboxFolderRequest,
          team_id = teamId
        )

        val remaining_clauses =
          sqls""" ${hasProspectsCondition}
                 ${reply_at_condition}
                 $orderBy
                 limit ${vmq.pageSize}
                """
        remaining_clauses


      case ValidatedConvReq.ValidatedSingleConvReq(mailboxFolderRequest, conv_id) =>

        val final_sql_clause = matchIdConditionSQLS(conv_id)
        final_sql_clause
    }

  }

  def countSelectedThreadsCanBeAccessed(
                                         thread_ids: List[Long],
                                         setting_id: Long,
                                         teamId: TeamId
                                       ): Try[Int]

  def matchIdConditionSQLS(conv_id: Long): SQLSyntax

  def toConversationSummaryView(rs: WrappedResultSet, teamId: Long, mailboxFolderRequest : MailboxFolder)(using Logger:SRLogger): Try[ConversationObjectInboxV3Type]

  def getConversationsForProspects(
                                    qry: SQLSyntax,
                                    teamId: Long,
                                    mailboxFolderRequest : MailboxFolder
                                  )(using logger: SRLogger): Try[List[ConversationObjectInboxV3Type]] = {

    val conversations = DBUtils.readOnlyWithExplain(
      query = qry,
      logger = logger,
      always_explain_if_exceeds_threshold = true
    ) { implicit session =>
      sql""" $qry """
        .map(rs =>
          toConversationSummaryView(rs, teamId = teamId, mailboxFolderRequest = mailboxFolderRequest).get
        )
        .list
        .apply()
    }
    conversations

  }

  def getConversationQueryV3(teamId: Long,
                             validatedConvReq: ValidatedConvReq,
                             replySentimentUuid: List[ReplySentimentUuid]
                            ): SQLSyntax


  def getQueryAndGetConversations(
                                   teamId: Long,
                                   validatedConvReq: ValidatedConvReq,
                                   replySentimentUuid: List[ReplySentimentUuid]
                                 )(using logger: SRLogger): Try[List[ConversationObjectInboxV3Type]] = {

    for {
      query <- Try {
        getConversationQueryV3(
          teamId = teamId,
          validatedConvReq = validatedConvReq,
          replySentimentUuid = replySentimentUuid
        )
      }

      convs <- getConversationsForProspects(
        qry = query,
        teamId = teamId,
        mailboxFolderRequest = validatedConvReq.mailboxFolderRequest
      )
    } yield {
      convs
    }
    
  }

  def findByIdInternalV3(
                          id: Long,
                          teamId: TeamId,
                          inboxEmailSettingId: Option[Long] = None
                        ): Option[ThreadForProspectInternalType]

  def getSingleConversation(
                             qry: SQLSyntax,
                             teamId: Long,
                             mailboxFolderRequest : MailboxFolder
                           )(using logger: SRLogger)
  : Try[Option[ConversationObjectInboxV3Type]] = {
    val conversation = DBUtils.readOnlyWithExplain(
      query = qry,
      logger = logger
    ) { implicit session =>
      sql""" $qry """
        .map(rs =>
          toConversationSummaryView(rs, teamId = teamId, mailboxFolderRequest = mailboxFolderRequest).get
        )
        .single
        .apply()
    }
    conversation
  }

  def changeFolderToSnooze(
                            threadIds: List[Long],
                            snoozed_till: Option[DateTime],
                            teamId: TeamId
                          ): Try[Int]

  def updateInboxFolderTypeTry(
                                threadIds: Seq[Long],
                                teamId: TeamId,
                                folder_type: FolderType
                              ): Try[Int] = {
    DB.autoCommit { implicit session =>
      updateInboxFolderType(
        threadIds = threadIds,
        teamId = teamId,
        folder_type = folder_type
      )
    }
  }
  
  def updateInboxFolderType(
                             threadIds: Seq[Long],
                             teamId: TeamId,
                             folder_type: FolderType,
                             is_automatic_unsnooze: Boolean = false,
                             updated_at_unsnooze_cron: Option[DateTime] = None
                           )(implicit session: DBSession): Try[Int]


  def addReplySentimentToEmailThread(
                                      threadIds: List[Long],
                                      team_id: Long,
                                      reply_sentiment_uuid: ReplySentimentUuid,
                                      reply_sentiment_updated_by: ReplySentimentUpdatedBy
                                    )(implicit session: DBSession): Try[List[Long]]

}

object MessageThreadDAOTrait {

  def findThreadByUuid(
                        uuid: String,
                        teamId: TeamId,
                        srUuidService: SrUuidService,
                        emailThreadDAO: EmailThreadDAO,
                        linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO
                      )(using logger: SRLogger): Try[Option[ThreadForProspectInternal]] = {

    srUuidService.getThreadIdFromUuid(
      uuid = uuid,
      teamId = teamId
    )
      .map {
        case ThreadId.EmailThreadId(id) =>
          emailThreadDAO.findByIdInternalV3(
            id = id,
            teamId = teamId
          )

        case ThreadId.LinkedinThreadId(id) =>
          linkedinMessageThreadsDAO.findByIdInternalV3(
            id = id,
            teamId = teamId
          )
      }

  }

}
