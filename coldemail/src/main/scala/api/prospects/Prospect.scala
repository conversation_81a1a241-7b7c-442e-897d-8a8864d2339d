package api.prospects

import api.accounts.models.ProspectAccountUuid
import api.{ApiVersion, prospects}
import api.accounts.{Account, AccountUuid, TeamId}
import api.prospects.models.ProspectId
import api.blacklist.Blacklist
import api.campaigns.dao.CampaignProspectDAO_2
import api.campaigns.models.IgnoreProspectsInOtherCampaigns
import api.campaigns.services.MergeTagService
import api.columns.{ColumnDef, ProspectColumnDef}
import api.prospects.dao.{DeDuplicateColumnTypeAndValue, DuplicateProspectResult, NewlyCreatedProspect, ProspectAddEventDAO}
import api.search.{ProspectQuery, SearchQuery}
import api.team_inbox.service.UpdateReplySentimentFormForDAO.CampaignProspectThread
import eventframework.ProspectObject
import io.smartreach.esp.utils.email.EmailReplyBounceType
import org.joda.time.DateTime
import org.postgresql.util.PGobject
import play.api.Logging
import play.api.libs.json._
import play.api.libs.json.JodaWrites._
import play.api.libs.json.JodaReads._
import scalikejdbc._
import scalikejdbc.jodatime.JodaWrappedResultSet._
import utils.dbutils.{DBUtils, SQLUtils}
import utils.emailvalidation.EmailValidationService
import utils.{Helpers, SRLogger}
import utils.enum_sr_utils.EnumUtils
import utils.timezones.TimezoneUtils

import scala.concurrent.{ExecutionContext, Future, blocking}
import scala.util.{Failure, Success, Try}

case class ProspectForMQTriggerPublish(
  prospect_id: Long,
  prospect_owner_id: Long
)

case class UpsertSQLProspectData(
                                  prospect_email: String,
                                  prospect_data: InsertOrUpdateProspectResult
                                )

case class UpsertSQLResult(
                            createdProspectEmails: Seq[NewlyCreatedProspect],
                            createdProspectIds: Seq[Long],
                            updatedIds: Seq[Long],

                            duplicateProspectsData: Seq[DuplicateProspectResult],
                            duplicatesIgnoredEmailsBecauseOfNoEditPermission: Seq[String],

                            // will be used for assigning prospects to campaign when force-update is false but campaign is selected
                            // earlier if force-update was false, duplicate prospects were not getting assigned to campaign also
                            duplicateProspectIdsWithEditPermission: Seq[Long],
                            totalDNCProspects: Int,
                            prospectDataForApi: List[UpsertSQLProspectData]
                          )

case class AssignProspectsToMember(
  prospect_ids: List[Long],
  assign_to: Long,
  team_id: Long
  // force_assign: Boolean
)

object AssignProspectsToMember {
  implicit val reads: Reads[AssignProspectsToMember] = Json.reads[AssignProspectsToMember]
}

case class ProspectCheckForIsSentEmail(
  prospect_id: Long,
  email: String,
  owner_id: Long,
  list_id: Option[Long],
  last_contacted_at: Option[DateTime]
)

//Only For public api
case class ProspectCreateFormDataV2(
                                     email: Option[String],
                                     first_name: Option[String],
                                     last_name: Option[String],
                                     custom_fields: Option[JsValue],

                                     owner_id: Option[Long] = None,

                                     list: Option[String],
                                     company: Option[String],
                                     city: Option[String],
                                     country: Option[String],
                                     timezone: Option[String],
                                     created_at: Option[DateTime] = None,

                                     state: Option[String],
                                     job_title: Option[String],
                                     phone_number: Option[String],
                                     linkedin_url: Option[String] = None,

                                     tags: Option[Seq[String]]
                                   )

object ProspectCreateFormDataV2 {
  implicit val prospectCreateFormDataV2: Format[ProspectCreateFormDataV2] = Json.format[ProspectCreateFormDataV2]
}


// create form case class
// PROSPECTCOLUMNCHANGETAG
// NOTE: 15 May 2023: if any field is modified / added to this case class ProspectCreateFormData,
// then also modify the shouldUpdateInDb fn below accordingly
case class ProspectCreateFormData(
  email: Option[String],
  first_name: Option[String],
  last_name: Option[String],
  custom_fields: JsValue,

  // should not break in old ui/integrations, currently used only inside createOrUpdateOne controller
  owner_id: Option[Long] = None,

  list: Option[String],
  company: Option[String],
  city: Option[String],
  country: Option[String],
  timezone: Option[String],
  created_at: Option[DateTime] = None,

  state: Option[String],
  job_title: Option[String],
  phone: Option[String],
  phone_2: Option[String],
  phone_3: Option[String],
  linkedin_url: Option[String] = None
)

object ProspectCreateFormData {
  implicit val createProspectFormat: Format[ProspectCreateFormData] = Json.format[ProspectCreateFormData]

  // NOTE: 15-May-2023: while updating prospect in queryForUpdateProspects
  // if user is sending null, we are storing empty string "" in the default columns
  // so this sanitize fn essentially treats empty string and null as the same and avoids those updates

  def getFromRb2BData(
                     data: JsValue,
                     team_id: TeamId
                     )(implicit Logger: SRLogger): JsResult[ProspectCreateFormData] = {
    /**
     * https://devapi5.sreml.com/api/v3/prospect_from_rb2b?api_key=<API_KEY>&team_id=<TEAM_ID>&source=rb2b
     * {
     * "LinkedIn URL": "https://www.linkedin.com/in/retentionadam/",
     * "First Name": "Adam",
     * "Last Name": "Robinson",
     * "Title": "CEO @ Retention.com. We help Ecomm brands grow & monetize their first-party audience",
     * "Company Name": "Retention.com",
     * "Business Email": "<EMAIL>",
     * "Website": "https://retention.com",
     * "Industry": "Internet Technology & Services",
     * "Employee Count": "1-10",
     * "Estimate Revenue": "$22M rev",
     * "City": "Austin",
     * "State": "Texas",
     * "Zipcode": "73301",
     * "Seen At": "2024-01-01T12:34:56:00.00+00.00",
     * "Referrer": "https://retention.com",
     * "Captured URL": "https://rb2b.com/pricing",
     * "Tags": "Hot Pages, ICP",
     * }
     */
    if(team_id == TeamId(22273)) {
      Logger.doNotTruncate(s"getFromRb2BData for tid_22273 --- $data")
    }

    val result = for {

      linkedin_url: Option[String] <- Try{(data \ "LinkedIn URL").asOpt[String]}
      first_name: Option[String] <- Try{(data \ "First Name").asOpt[String]}
      last_name: Option[String] <- Try{(data \ "Last Name").asOpt[String]}
      title: Option[String] <- Try{(data \ "Title").asOpt[String]}
      company_name <- Try{(data \ "Company Name").asOpt[String]}
      email <- Try{(data \ "Business Email").asOpt[String]}
      website <- Try{(data \ "Website").asOpt[String]}
      industry <- Try{(data \ "Industry").asOpt[String]}
      employee_count <- Try{(data \ "Employee Count").asOpt[String]}
      estimate_revenue <- Try{(data \ "Estimate Revenue").asOpt[String]}
      city <- Try{(data \ "City").asOpt[String]}
      state <- Try{(data \ "State").asOpt[String]}
      zipcode <- Try{(data \ "Zipcode").asOpt[String]}
      seen_at <- Try{(data \ "Seen At").asOpt[String]}
      referrer <- Try{(data \ "Referrer").asOpt[String]}
      capture_url <- Try{(data \ "Captured URL").asOpt[String]}
      tags <- Try{(data \ "Tags").asOpt[String]}
    } yield {
      ProspectCreateFormData(
        email,
        first_name,
        last_name,
        custom_fields = Json.obj(),
        owner_id = None,
        list = None,
        company = company_name,
        city = city,
        country = None,
        timezone = None,

        state = state,
        job_title = title,
        phone = None,
        phone_2 = None,
        phone_3 = None,
        linkedin_url = linkedin_url
      )
    }

    result match {
      case Success(value) => JsSuccess(value = value)
      case Failure(exception) => JsError(Option(exception.getMessage).getOrElse("getFromRb2BData failed"))
    }
  }
  private def sanitizeStr(
    str: Option[String]
  ) = {
    if (str.nonEmpty && str.get.trim.isEmpty) None
    else str
  }

  private def shouldUpdatePartialFn(
    ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads: Boolean
  )(
    userInput: Option[String],
    valueInDb: Option[String],
  ): Boolean = {

    if (

      (userInput.isEmpty || userInput.get.trim.isEmpty)
        && ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads

    ) {

      false

    } else {

      sanitizeStr(userInput) != sanitizeStr(valueInDb)

    }


  }

  final def isUserInputAndProspectInDbMatching(
    ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads: Boolean,
    userInput: ProspectCreateFormData,
    prospectInDb: ProspectCreateFormData,
    customColumnDefs: Seq[ColumnDef],
  ): Try[(Boolean, List[String])] = {

    val userIn = userInput
    val dbVal = prospectInDb

    ColumnDef.customFieldsMatchingForUserInputAndProspectInDb(
      customFieldsUserInput = userIn.custom_fields,
      customFieldsInDb = dbVal.custom_fields,
      ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads,
      customColumnDefs = customColumnDefs
    ) match {

      case Failure(ex) => Failure(ex)

      case Success(false) =>

        Success((false, List("custom_fields")))
//        Success(false)

      case Success(true) =>

        // custom fields are matching, so we are here

        var differingFields: List[String] = List()

        if (userIn.email.isDefined && (userIn.email.get.trim.toLowerCase != dbVal.email.get.trim.toLowerCase)) {

          differingFields = differingFields :+ "email"

        }

        // sometimes the user-input maynot have the owner_id,
        // but prospect-from-db will always have owner_id
        if (userIn.owner_id.nonEmpty && userIn.owner_id != dbVal.owner_id) {

          differingFields = differingFields :+ "owner_id"

        }

        val shouldUpdate = shouldUpdatePartialFn(
          ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads
        )


        if (shouldUpdate(userIn.first_name, dbVal.first_name)) {

          differingFields = differingFields :+ "first_name"

        }

        if (shouldUpdate(userIn.last_name, dbVal.last_name)) {

          differingFields = differingFields :+ "last_name"

        }

        if (shouldUpdate(userIn.list, dbVal.list)) {

          differingFields = differingFields :+ "list"

        }

        if (shouldUpdate(userIn.company, dbVal.company)) {

          differingFields = differingFields :+ "company"

        }

        if (shouldUpdate(userIn.city, dbVal.city)) {

          differingFields = differingFields :+ "city"

        }

        if (shouldUpdate(userIn.country, dbVal.country)) {

          differingFields = differingFields :+ "country"

        }

        if (shouldUpdate(userIn.timezone, dbVal.timezone)) {

          differingFields = differingFields :+ "timezone"

        }

        if (shouldUpdate(userIn.state, dbVal.state)) {

          differingFields = differingFields :+ "state"

        }

        if (shouldUpdate(userIn.job_title, dbVal.job_title)) {

          differingFields = differingFields :+ "job_title"

        }

        if (shouldUpdate(userIn.phone, dbVal.phone)) {

          differingFields = differingFields :+ "phone"

        }

        if (shouldUpdate(userIn.linkedin_url, dbVal.linkedin_url)) {

          differingFields = differingFields :+ "linkedin_url"

        }

        Success((differingFields.isEmpty, differingFields))

    }

  }

}


case class ProspectCreateManuallyFormData(
                                           prospect: ProspectCreateFormData,
                                           ignoreProspectsInOtherCampaigns: Option[IgnoreProspectsInOtherCampaigns]
                                         )
object ProspectCreateManuallyFormData {
  implicit val createBatchProspectFormat: Format[ProspectCreateManuallyFormData] = Json.format[ProspectCreateManuallyFormData]
}


// batch create form case class
// PROSPECTCOLUMNCHANGETAG
case class ProspectBatchCreateFormData(
                                      prospects: Seq[ProspectCreateFormData],
                                      tags: Option[Seq[String]]
                                 )

object ProspectBatchCreateFormData {
  implicit val createBatchProspectFormat: Format[ProspectBatchCreateFormData] = Json.format[ProspectBatchCreateFormData]
}


// update form case class
// PROSPECTCOLUMNCHANGETAG
case class ProspectUpdateFormData(
  email: Option[String],
  first_name: Option[String],
  last_name: Option[String],
  custom_fields: JsValue,

  prospect_category_id_custom: Option[Long] = None,

  list: Option[String],
  company: Option[String],
  city: Option[String],
  country: Option[String],
  timezone: Option[String],

  state: Option[String] = None,
  job_title: Option[String] = None,
  phone: Option[String] = None,
  phone_2: Option[String] = None,
  phone_3: Option[String] = None,
  linkedin_url: Option[String] = None,

  prospect_account_id: Option[Long]
)

object ProspectUpdateFormData {
  implicit val createProspectReads: Reads[ProspectUpdateFormData] = Json.reads[ProspectUpdateFormData]

  def fromProspectObject(
    prospectObject: ProspectObject
  ): ProspectUpdateFormData = {

    ProspectUpdateFormData(
      email = prospectObject.email,
      first_name = prospectObject.first_name,
      last_name = prospectObject.last_name,
      custom_fields = prospectObject.custom_fields,
      prospect_category_id_custom = Some(prospectObject.internal.prospect_category_id_custom),
      list = prospectObject.list,
      company = prospectObject.company,
      city = prospectObject.city,
      country = prospectObject.country,
      timezone = prospectObject.timezone,
      state = prospectObject.state,
      job_title = prospectObject.job_title,
      phone = prospectObject.phone,
      linkedin_url = prospectObject.linkedin_url,
      prospect_account_id = prospectObject.internal.prospect_account_id,
    )

  }

}

case class ProspectBounceResponseInfo (
  prospectId: Long,
  prospectEmail: String,
  bouncedAt: DateTime,
  campaignId: Option[Long],
  bounceType: Option[EmailReplyBounceType],
  bounceReason: Option[String]
)

// update status form case class

case class ProspectUpdateCategoryFormData(
  prospect_category_id_custom: Long
)

object ProspectUpdateCategoryFormData {
//  implicit val prospectCategoryReads = EnumUtils.enumReads(ProspectCategory)
  implicit val reads: Reads[ProspectUpdateCategoryFormData] = Json.reads[ProspectUpdateCategoryFormData]
}


// delete form case class

case class ProspectDeleteFormData(
  prospect_ids: List[Long]
)

object ProspectDeleteFormData {
  implicit val reads: Reads[ProspectDeleteFormData] = Json.reads[ProspectDeleteFormData]
}


// force send form case class

case class ProspectMarkAsValidToForceSend(
  prospect_ids: Seq[Long]
)

object ProspectMarkAsValidToForceSend {
  implicit val reads: Reads[ProspectMarkAsValidToForceSend] = Json.reads[ProspectMarkAsValidToForceSend]
}


// pauseUnpause form case class
case class ProspectPauseStatusData(
  prospect_ids: List[Long],
  new_pause_status: Boolean,
  campaign_ids: Option[Seq[Long]] = None // setting default to ensure old APIs dont break, remove this later
)

object ProspectPauseStatusData {
  implicit val reads: Reads[ProspectPauseStatusData] = Json.reads[ProspectPauseStatusData]
}

case class ProspectCustomCategoryCreateOrUpdateFormData(
  name: String,
  label_color: String
)

object ProspectCustomCategoryCreateOrUpdateFormData {
  implicit val createOrUpdateProspectCustomCategoryReads: Reads[ProspectCustomCategoryCreateOrUpdateFormData] = Json.reads[ProspectCustomCategoryCreateOrUpdateFormData]
}

//case class ProspectBatchUpdate(
//                                action_name: String,
//                                action_value: String,
//                                prospect_ids: Option[List[Long]],
//                                is_select_all: Boolean,
//                                filters: Option[SearchQuery]
//  )
//
//object ProspectBatchUpdate {
//  implicit val reads = Json.reads[ProspectBatchUpdate]
//}

case class ProspectBatchActions(
//                                action_name: String,
                                //action_value: String,
                                prospect_ids: Option[List[String]], //need for manual selection and normal select all
                                is_select_all: Option[Boolean],
                                filters: Option[SearchQuery],

                              )

object ProspectBatchActions {
  implicit val reads: Reads[ProspectBatchActions] = Json.reads[ProspectBatchActions]
}

case class ProspectBatchActionUpdateCategory(
                                 category_id: Long
                               )

object ProspectBatchActionUpdateCategory {
  implicit val reads: Reads[ProspectBatchActionUpdateCategory] = Json.reads[ProspectBatchActionUpdateCategory]
}

case class ProspectBatchActionAssignToCampaign (
                                                ignore_prospects_in_other_campaigns: Option[String],
                                                force_assign: Option[Boolean],
                                                campaign_id: Option[String],
                                               )

object ProspectBatchActionAssignToCampaign {
  implicit val reads: Reads[ProspectBatchActionAssignToCampaign] = Json.reads[ProspectBatchActionAssignToCampaign]
}

case class ProspectBatchActionUnAssignFromCampaign (
                                                  campaign_id: Option[String],
                                                 )

object ProspectBatchActionUnAssignFromCampaign {
  implicit val reads: Reads[ProspectBatchActionUnAssignFromCampaign] = Json.reads[ProspectBatchActionUnAssignFromCampaign]
}

case class ProspectBatchActionAddOrRemoveTag (
                                               tags: Option[Seq[String]]
                                             )

object ProspectBatchActionAddOrRemoveTag {
  implicit val reads: Reads[ProspectBatchActionAddOrRemoveTag] = Json.reads[ProspectBatchActionAddOrRemoveTag]
}

case class ProspectBatchActionChangeOwnerShip (
                                                owner_id: Long
                                              )
object ProspectBatchActionChangeOwnerShip {
  implicit val reads: Reads[ProspectBatchActionChangeOwnerShip] = Json.reads[ProspectBatchActionChangeOwnerShip]
}

case class ProspectBatchActionCampaignPauseUnpauseForProspect (
                                                                new_pause_status: Boolean,
                                                                campaign_ids: Seq[String],
                                                              )
object ProspectBatchActionCampaignPauseUnpauseForProspect {
  implicit val reads: Reads[ProspectBatchActionCampaignPauseUnpauseForProspect] = Json.reads[ProspectBatchActionCampaignPauseUnpauseForProspect]
}

case class ProspectBatchActionProspectAccount (
                                                prospect_account_id: Long
                                              )

object ProspectBatchActionProspectAccount {
  implicit val reads: Reads[ProspectBatchActionProspectAccount] = Json.reads[ProspectBatchActionProspectAccount]
}

case class ProspectBatchActionStatusChange (
                                             campaign_ids: Seq[String],
                                             prospect_status: String,
                                             will_resume_at: Option[DateTime] ,
                                             will_resume_at_tz: Option[String],
                                           )

object ProspectBatchActionStatusChange {
  implicit val reads: Reads[ProspectBatchActionStatusChange] = Json.reads[ProspectBatchActionStatusChange]
}

case class ProspectUpdateCategoriesInDB(
     name: String,
     text_id: String,
     is_custom: Boolean,
     team_id: Long
   )


//case class ProspectCategoryInDB(
//       id: Long,
//       name: String,
//       text_id: String,
//       is_custom: Boolean,
//       team_id: Long
//     )
//
//object ProspectCategoryInDB {
//
//  def fromDb(rs: WrappedResultSet) = ProspectCategoryInDB(
//    id = rs.long("id"),
//    name = rs.string("name"),
//    text_id = rs.string("text_id"),
//    is_custom = rs.booleanOpt("is_custom").getOrElse(false),
//    team_id = rs.long("team_id")
//  )
//}

//////////////////




//////////////////

case class ProspectForValidation(
  id: Long,
  email: String,
  email_checked: Boolean,
  email_sent_for_validation: Boolean,
  email_sent_for_validation_at: Option[DateTime]
)

//////////////////

case class ProspectList(
  id: Int,
  name: String
)

object ProspectList {
  implicit val writes: OWrites[ProspectList] = Json.writes[ProspectList]

  def apiStructure(list: ProspectList): JsObject = {
    Json.obj(
      "name" -> list.name
    )
  }
}


case class ProspectBlacklistMatches(
  id: ProspectId,
  email: String,
  email_domain: String,
  phone: Option[String],
  prospect_category_id_custom: Long
)



case class ProspectListStats(
    id: Long,
    name: String,
    created_at: DateTime,
    last_contacted: DateTime,
    total_prospects: Int,
    total_emails_sent: Int,
    total_emails_opened: Int,
    total_emails_clicked: Int,
    total_emails_opted_out: Int,
    total_emails_replied: Int,
    total_emails_bounced: Int,
    stats_last_updated: Option[DateTime]
  )

object ProspectListStats {
  implicit val writes: OWrites[ProspectListStats] = Json.writes[ProspectListStats]

  def fromDb(rs: WrappedResultSet): ProspectListStats = ProspectListStats(
    id = rs.long("id"),
    name = rs.string("name"),
    created_at = rs.jodaDateTime("created_at"),
    last_contacted = rs.jodaDateTime("last_contacted"),
    total_prospects = rs.int("total_prospects"),
    total_emails_sent = rs.int("total_emails_sent"),
    total_emails_opened = rs.int("total_emails_opened"),
    total_emails_clicked = rs.int("total_emails_clicked"),
    total_emails_opted_out = rs.int("total_emails_opted_out"),
    total_emails_replied = rs.int("total_emails_replied"),
    total_emails_bounced = rs.int("total_emails_bounced"),
    stats_last_updated = rs.jodaDateTimeOpt("stats_last_updated")
  )
}


case class FetchProspectListStats(
    total_prospects: Int,
    total_emails_sent: Int,
    total_emails_opened: Int,
    total_emails_clicked: Int,
    total_emails_opted_out: Int,
    total_emails_replied: Int,
    total_emails_bounced: Int
  )

object FetchProspectListStats {

  def fromDb(rs: WrappedResultSet): FetchProspectListStats = FetchProspectListStats(
    total_prospects = rs.int("total_prospects"),
    total_emails_sent = rs.int("total_emails_sent"),
    total_emails_opened = rs.int("total_emails_opened"),
    total_emails_clicked = rs.int("total_emails_clicked"),
    total_emails_opted_out = rs.int("total_emails_opted_out"),
    total_emails_replied = rs.int("total_emails_replied"),
    total_emails_bounced = rs.int("total_emails_bounced")
  )
}

case class ProspectName(
   id: Long,
   name: String
)

object ProspectName {

  def fromDb(rs: WrappedResultSet): ProspectName = ProspectName(
    id = rs.long("id"),
    name = Helpers.getProspectNameForFeed(rs.stringOpt("first_name"), rs.stringOpt("last_name"), rs.stringOpt("email"))
  )
}

sealed trait ProspectBasicDetails {
  def id: Long
}

object ProspectBasicDetails {
  case class EmailProspectBasicDetails(
                                        id: Long,
                                        first_name: Option[String],
                                        last_name: Option[String],
                                        email: String
                                      ) extends ProspectBasicDetails

  object EmailProspectBasicDetails {
    def fromDb(rs: WrappedResultSet): EmailProspectBasicDetails = EmailProspectBasicDetails(
      id = rs.long("id"),
      first_name = rs.stringOpt("first_name"),
      last_name = rs.stringOpt("last_name"),
      email = rs.string("email")
    )

    implicit val writes: OWrites[EmailProspectBasicDetails] = Json.writes[EmailProspectBasicDetails]
  }

  case class LinkedinProspectBasicDetails(
                                           id: Long,
                                           first_name: Option[String],
                                           last_name: Option[String],
                                           profile_url: String
                                         ) extends ProspectBasicDetails

  object LinkedinProspectBasicDetails {
    def fromDb(rs: WrappedResultSet): LinkedinProspectBasicDetails = LinkedinProspectBasicDetails(
      id = rs.long("id"),
      first_name = rs.stringOpt("first_name"),
      last_name = rs.stringOpt("last_name"),
      profile_url = rs.string("profile_url")
    )

    implicit val writes: OWrites[LinkedinProspectBasicDetails] = Json.writes[LinkedinProspectBasicDetails]
  }

  implicit def writes: Writes[ProspectBasicDetails] = new Writes[ProspectBasicDetails] {
    override def writes(o: ProspectBasicDetails): JsValue = {
      o match {
        case data: EmailProspectBasicDetails => Json.toJson(data)
        case data: LinkedinProspectBasicDetails => Json.toJson(data)
      }
    }
  }
}



object ProspectSource extends Enumeration with Logging { //these will store prospects table as prospect_source
  type ProspectSource = Value

  val HUBSPOT = Value("hs")
  val ZOHO = Value("zh")
  val ZOHO_RECRUIT = Value("zhr")
  val PIPEDRIVE = Value("pd")
  val SALESFORCE = Value("sf")
  val ZAPIER = Value("zp")
  val PROSPECTDADDY = Value("prospectdaddy")
  val SMARTREACH_PROSPECTS = Value("smartreach_prospects")
  val SMARTREACH_REPLY = Value("srreply")
  val HUNTER = Value("hunter")
  val UPLEAD = Value("uplead")
  val DROPCONTACT = Value("dropcontact")
  val ANYMAILFINDER = Value("anymailfinder")
  val CLEARBIT = Value("clearbit")
  val AEROLEADS = Value("aeroleads")
  val CLAY = Value("clay")
  val MAKE_COM = Value("make.com")
  val BITSCALE = Value("bitscale")
  val PERSANA = Value("persana")
  val SMARTREACH_LEADFINDER = Value("sr_leadfinder")
  val RB2B = Value("rb2b")
  given format: Format[prospects.ProspectSource.Value] = EnumUtils.enumFormat(ProspectSource)


  def getFullNameFromSource(source: String) = {

    source match {
      case "hs" => "HubSpot"
      case "zh" => "Zoho"
      case "zhr" => "Zoho Recruit"
      case "pd" => "Pipedrive"
      case "sf" => "Salesforce"
      case "zp" => "Zapier"
      case "prospectdaddy" => "ProspectDaddy"
      case "srreply" => "SmartReach Conversation"
      case "hunter" => "Hunter"
      case "uplead" => "Uplead"
      case "dropcontact" => "Dropcontact"
      case "anymailfinder" => "Anymailfinder"
      case "clearbit" => "Clearbit"
      case "aeroleads" => "Aeroleads"
      case "sr_leadfinder" => "Smartreach LeadFinder"
      case "rb2b" => "Rb2b"
      case "make.com" => "make.com"
      case "clay" => "Clay"
      case "bitscale" => "Bitscale"
      case "persana" => "Persana"
      case _ =>

        logger.error(s"FATAL: unmatched source name: $source")

        source
    }

  }

}

object SearchQuerySelectType extends Enumeration {
  type SearchQuerySelectType = Value

  val COUNT_ONLY = Value("count_only")
  val ID_ONLY = Value("id_only")
  val FULL_OBJECT = Value("full_object")

  given format: Format[prospects.SearchQuerySelectType.Value] = EnumUtils.enumFormat(SearchQuerySelectType)

}

case class ProspectIdEmail(id: Long, email: String, owner_id: Long)

case class ProspectDeletingStats(
  total_deleted: Int,
  invalid_email: Int,
  email_bounced: Int,
  force_sent: Int,
  force_sent_and_bounced: Int,
  prospect_ids_for_deletion: Seq[Long]
)


case class ProspectBasicForBatchUpdate(
  id: Long,
  prospect_category_id_custom: Long,
  prospect_account_id: Option[Long]
)
object ProspectBasicForBatchUpdate {

  def fromDb(rs: WrappedResultSet): ProspectBasicForBatchUpdate = ProspectBasicForBatchUpdate(
    id = rs.long("id"),
    prospect_category_id_custom = rs.long("prospect_category_id_custom"),
    prospect_account_id = rs.longOpt("prospect_account_id")
  )
}

case class OwnedProspectsForAssigning(
     prospect_id: Long,
     invalid_email: Option[Boolean],
     email_bounced_at: Option[DateTime],
     email_bounced: Option[Boolean],
     prospect_category_id_custom: Long,
     synced_previously_sent_step_for_deleted_prospect_step_id: Option[Long]
   )

case class ProspectsWithInvalidEmail (
       prospectId: Long,
       invalidEmail: Boolean,
       accountId: Long,
       teamId: Long
     )

case class InsertOrUpdateProspectResult(
                                         prospect_id: Long,
                                         prospect_uuid: Option[String],
                                         is_updated_internal: Boolean,
                                         account_id: Long
                                       )

object InsertOrUpdateProspectResult {
  def fromDb(rs: WrappedResultSet): InsertOrUpdateProspectResult = InsertOrUpdateProspectResult(
    prospect_id = rs.long("id"),
    prospect_uuid = rs.stringOpt("uuid"),
    is_updated_internal = rs.booleanOpt("is_updated_internal").getOrElse(false),
    account_id = rs.long("account_id")
  )
}

case class SecondaryProspectEmails(
                                  prospect_email_id: Long,
                                  email: String
                                  )
object SecondaryProspectEmails {
  implicit val writes: Writes[SecondaryProspectEmails] = Json.writes[SecondaryProspectEmails]
}

case class DeletedProspect(
                            prospect_id: ProspectId,
                            team_id: TeamId,
                            account_id: Long
                          )


case class ProspectUuid(uuid: String) extends AnyVal {

  override def toString: String = uuid

}

object ProspectUuid{

  implicit val reads: Reads[ProspectUuid] = new Reads[ProspectUuid] {
    override def reads(ev: JsValue): JsResult[ProspectUuid] = {
      ev match {
        case JsString(uuid) => JsSuccess(ProspectUuid(uuid = uuid))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[ProspectUuid] = new Writes[ProspectUuid] {
    override def writes(o: ProspectUuid): JsValue = JsString(o.uuid)
  }
}


case class CampaignProspectData(
                                 to_check: Boolean,
                                 to_check_fields: Option[List[String]],
                                 total_opens_in_campaign: Long,
                                 total_clicks_in_campaign: Long,
                                 sent: Boolean,
                                 opened: Boolean,
                                 replied: Boolean,
                                 replied_marked_by_adminid: Option[Long],
                                 opted_out: Boolean,
                                 bounced: Boolean,
                                 completed: Boolean,
                                 will_resume_at: Option[DateTime],
                                 will_resume_at_tz: Option[String],
                                 clicked: Boolean,
                                 invalid_email: Boolean,
                                 auto_reply: Boolean,
                                 out_of_office: Boolean,
                                 campaign_name: String,
                                 campaign_id: Long,
                                 campaign_uuid: Option[String],
                                 step: Option[String],
                                 sending_holiday_calendar_name: Option[String],
                                 current_holiday_name: Option[String],
                                 current_step_type: Option[String],
                                 latest_task_done_at: Option[DateTime]
                               )

object CampaignProspectData {
  implicit val writes: Writes[CampaignProspectData] = new Writes[CampaignProspectData] {
    def writes(data: CampaignProspectData) = {

      Json.obj(
        "to_check" -> data.to_check,
        "to_check_fields" -> data.to_check_fields,
        "total_opens_in_campaign" -> data.total_opens_in_campaign,
        "total_clicks_in_campaign" -> data.total_clicks_in_campaign,
        "sent" -> data.sent,
        "opened" -> data.opened,
        "replied" -> data.replied,
        "replied_marked_by_adminid" -> data.replied_marked_by_adminid,
        "opted_out" -> data.opted_out,
        "bounced" -> data.bounced,
        "completed" -> data.completed,
        "will_resume_at" -> data.will_resume_at,
        "will_resume_at_tz" -> data.will_resume_at_tz,
        "clicked" -> data.clicked,
        "invalid_email" -> data.invalid_email,
        "auto_reply" -> data.auto_reply,
        "out_of_office" -> data.out_of_office,
        "campaign_name" -> data.campaign_name,
        "campaign_id" -> data.campaign_id,
        "step" -> data.step,
        "sending_holiday_calendar_name" -> data.sending_holiday_calendar_name,
        "current_holiday_name" -> data.current_holiday_name,
        "current_step_type" -> data.current_step_type,
        "latest_task_done_at" -> data.latest_task_done_at
      )
    }

  }

  implicit val reads: Reads[CampaignProspectData] = new Reads[CampaignProspectData] {
    override def reads(json: JsValue): JsResult[CampaignProspectData] = Try {
      CampaignProspectData(
        to_check = (json \ "to_check").as[Boolean],
        to_check_fields = (json \ "to_check_fields").asOpt[List[String]],
        total_opens_in_campaign = (json \ "total_opens_in_campaign").as[Long],
        total_clicks_in_campaign = (json \ "total_clicks_in_campaign").as[Long],
        sent = (json \ "sent").as[Boolean],
        opened = (json \ "opened").as[Boolean],
        replied = (json \ "replied").as[Boolean],
        replied_marked_by_adminid = (json \ "replied_marked_by_adminid").asOpt[Long],
        opted_out = (json \ "opted_out").as[Boolean],
        bounced = (json \ "bounced").as[Boolean],
        completed = (json \ "completed").as[Boolean],
        will_resume_at = (json \ "will_resume_at").asOpt[DateTime],
        will_resume_at_tz = (json \ "will_resume_at_tz").asOpt[String],
        clicked = (json \ "clicked").as[Boolean],
        invalid_email = (json \ "invalid_email").as[Boolean],
        auto_reply = (json \ "auto_reply").as[Boolean],
        out_of_office = (json \ "out_of_office").as[Boolean],
        campaign_name = (json \ "campaign_name").as[String],
        campaign_id = (json \ "campaign_id").as[Long],
        step = (json \ "step").asOpt[String],
        sending_holiday_calendar_name = (json \ "sending_holiday_calendar_name").asOpt[String],
        current_holiday_name = (json \ "current_holiday_name").asOpt[String],
        current_step_type = (json \ "current_step_type").asOpt[String],
        latest_task_done_at = (json \ "latest_task_done_at").asOpt[DateTime],
        campaign_uuid = (json \ "campaign_uuid").asOpt[String]
      )
    } match {
      case Success(value) => JsSuccess(value)
      case Failure(exception) => JsError(Option(exception.getMessage).getOrElse("Error While getting CampaignDataForAssignProspects"))
    }
  }
}
