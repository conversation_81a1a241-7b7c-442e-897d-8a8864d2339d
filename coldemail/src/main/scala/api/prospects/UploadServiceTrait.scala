package api.prospects

import api.accounts.TeamId
import api.emails.services.BulkEmailUploadError
import play.api.Logger
import play.api.libs.json.{JsN<PERSON>, <PERSON>sVal<PERSON>, <PERSON><PERSON>, OWrites}
import utils.{Help<PERSON>, SRLogger}
import utils.dependencyinjectionutils.ProductOnboardingDI.RollingUpdate.SrRollingUpdateCoreService_DI
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService


sealed trait UploadCSVResult

object UploadCSVResult {

    case class UploadProspectCSVResult(
                                 total_rows: Int,
                                 total_created: Int,
                                 total_duplicates_updated: Int,

                                 total_empty_or_invalid_rows: Int,
                                 total_duplicates_found: Int,
                                 total_assigned: Int,
                                 totaL_duplicates_ignored_for_no_edit_permission: Int,
                                 total_ignored_internal_emails: Option[Int],
                                 total_dnc_prospects_created: Int,
                                 total_duplicate_emails_in_csv:Option[Int]
                               ) extends UploadCSVResult

    case class UploadDncCSVResult(
                                total_rows: Int,
                                total_created: Int
                                ) extends UploadCSVResult
    
    case class UploadEmailCSVResult(
                                   total_rows: Int,
                                   total_created: Int,
                                   email_errors: List[BulkEmailUploadError]
                                   ) extends UploadCSVResult


    object UploadProspectCSVResult {
        implicit val writes: OWrites[UploadProspectCSVResult] = Json.writes[UploadProspectCSVResult]
    }

    object UploadDncCSVResult {
        implicit val writes: OWrites[UploadDncCSVResult] = Json.writes[UploadDncCSVResult]
    }

    object UploadEmailCSVResult {
        implicit val writes: OWrites[UploadEmailCSVResult] = Json.writes[UploadEmailCSVResult]
    }


    implicit val writes: OWrites[UploadCSVResult] = OWrites[UploadCSVResult] {
        case result1: UploadProspectCSVResult => Json.toJsObject(result1)(UploadProspectCSVResult.writes)
        case result2: UploadDncCSVResult => Json.toJsObject(result2)(UploadDncCSVResult.writes)
        case result3: UploadEmailCSVResult => Json.toJsObject(result3)(UploadEmailCSVResult.writes)    
    }
}

trait UploadServiceTrait{


  final def safeParseJsonNumber(str: String): Option[JsValue] = {

    if (str.isEmpty) None
    else {

      try {
        val n = Json.parse(str)
        if (n == JsNull) None
        else Some(n)

      } catch {
        case _: com.fasterxml.jackson.core.JsonParseException => None
      }

    }
  }

  /*
  final def getReaderForCSVUrl(
                          url: String
                          // encoding: Option[String]
                        ): Try[(InputStreamReader, String)] = Try {

    /**
      * NOTE: inputForCharsetCheck Must be different from inputForParsingRows
      * inputForCharsetCheck goes through the Apache Tika library which changes some state in the input stream,
      * and the later univocity parser is not able to parse the csv rows
      *
      * So, I've created another input stream inputForParsingRows for passing it to univocity parsers to get the rows
      */
    val inputForCharsetCheck = new URL(url).openStream

    val charsetDetected: Charset = Try {
      new AutoDetectReader(inputForCharsetCheck).getCharset
    } match {

      case Failure(e) =>
        Logger.error(s"FATAL getReaderForCSVUrl getCharset: $url :: Taking default charset :: ${Helpers.getStackTraceAsString(e)}")

        StandardCharsets.ISO_8859_1

      case Success(cset) =>
        Logger.info(s"getReaderForCSVUrl getCharset: $url :: $cset")

        cset

    }
    // REF: https://commons.apache.org/proper/commons-csv/user-guide.html
    // handle files that start with a Byte Order Mark (BOM)
    val inputForParsingRows = new BOMInputStream(new URL(url).openStream)

    (new InputStreamReader(inputForParsingRows, charsetDetected), charsetDetected.displayName)

    /*
    if (encoding.isDefined) {

      //      Logger.info(s"\n\ngetreader codec: ${Charset.forName(encoding.get)}  :: ${encoding.get}")

      new InputStreamReader(input, Charset.forName(encoding.get))
    } else {
      new InputStreamReader(input)

    }
    */

  }
  */


  final def getCSVValueV2(
                           csvRow: Map[String, String],
                           mappingFromClient: Map[String, String],
                           dbColName: String,
//                           emailNotCompulsoryEnabled: Boolean
                         )(using Logger:SRLogger): Option[String] = {
    val csvValue: Option[String] = mappingFromClient.get(dbColName).map(_.trim).map(csvHeader => {
      // second Option.getOrElse to handle null values
      Option(csvRow.getOrElse(csvHeader, "")).getOrElse("").trim
    })

//    if (emailNotCompulsoryEnabled) {
      csvValue match {
        case None => None
        case Some(value) => if (value.isEmpty) None else Some(value)
      }
//    } else {
//      csvValue
//    }
  }




}

/*
object UploadService {

  final def getReaderForCSVFile(
    file: File
    // encoding: Option[String]
  ): Try[(InputStreamReader, String)] = Try {

    /**
      * NOTE: inputForCharsetCheck Must be different from inputForParsingRows
      * inputForCharsetCheck goes through the Apache Tika library which changes some state in the input stream,
      * and the later univocity parser is not able to parse the csv rows
      *
      * So, I've created another input stream inputForParsingRows for passing it to univocity parsers to get the rows
      */
    val inputForCharsetCheck = new FileInputStream(file)

    val charsetDetected: Charset = Try { new AutoDetectReader(inputForCharsetCheck).getCharset } match {

      case Failure(e) =>
        Logger.error(s"FATAL getReaderForCSVFile getCharset: $file :: Taking default charset :: ${Helpers.getStackTraceAsString(e)}")

        StandardCharsets.ISO_8859_1

      case Success(cset) =>
        Logger.info(s"getReaderForCSVFile getCharset: $file :: $cset")

        cset

    }

    // REF: https://commons.apache.org/proper/commons-csv/user-guide.html
    // handle files that start with a Byte Order Mark (BOM)
    val inputForParsingRows = new BOMInputStream(new FileInputStream(file))

    (new InputStreamReader(inputForParsingRows, charsetDetected), charsetDetected.displayName)


    /*
    if (encoding.isDefined) {

      new InputStreamReader(inputForParsingRows, Charset.forName(encoding.get))

    } else {

      new InputStreamReader(inputForParsingRows)

    }
    */

  }

  def createMapFromCSVHeaderAndRows(

    csvHeaderRow: Seq[String],
    dataRows: Seq[Seq[String]]

  ): Try[Seq[Map[String, String]]] = Try {

    val totalColumns = csvHeaderRow.size

    dataRows
      .zipWithIndex
      .map { case (row, rowIndex) => {

        if (row.size < totalColumns) {
          Logger.error(s"Bad Row [not enough columns]: rowIndex: $rowIndex")
        }

        csvHeaderRow
          .zipWithIndex
          .map { case (headerName, index) => {

            (headerName, row(index))

          }
          }
          .filter { case (headername, rowVal) => headername != null && headername.trim.nonEmpty } // ignore columns with empty headers
          .toMap


      }
      }
  }


  final def extractRowsFromCSV(
    inputStreamReader: InputStreamReader,
    Logger: SRLogger
  ): Try[Seq[Map[String, String]]] = Try {

    val settings = new com.univocity.parsers.csv.CsvParserSettings
    settings.setMaxCharsPerColumn(10000)
    settings.setMaxColumns(1024) // max columns allowed in excel sheets

    /* Added on 23rd Sep 2021
    sometimes users were uploading csvs where there were newlines inside double quotes for example, "Company \n name",
    this was breaking the parsing

    https://app.intercom.com/a/apps/xmya8oga/inbox/inbox/1492751/conversations/84281100091036

    25 Sep 21: commenting this out for now
     */
    // settings.getFormat.setNormalizedNewline(' ')

    /* before setting the allowed-delimiters explicity (, ;)

      if the user uploaded a single-column csv with just "email" column lets say and no delimiter,
      the auto-detect formatting was actually taking "@" from the emails as the delimiter, and no rows were getting uploaded
      */
    settings.detectFormatAutomatically(",".charAt(0), ";".charAt(0))

    val parser = new com.univocity.parsers.csv.CsvParser(settings)

    // val detectedFormat = parser.getDetectedFormat


    val parsedRows = parser
      .parseAll(inputStreamReader)
      .asScala
      .map(row => {
        row.toList
      })


    val csvHeaderRow = parsedRows.head
    val totalColumns = csvHeaderRow.size

    if (totalColumns > 500) {
      Logger.fatal(s"csv being uploaded with more than 500 columns ($totalColumns columns)")
    } else if (totalColumns > 100) {
      Logger.fatal(s"csv being uploaded with more than 100 columns ($totalColumns columns)")
    }
    // Logger.info(s"$logPrepend CSV PARSE: settings: $settings :: getDetectedFormat: ${parser.getDetectedFormat} :: parsedRows: ${parsedRows.length} :: csvHeaderRow: $csvHeaderRow")

    val dataRows = parsedRows
      .drop(1) // ignore header row


    val rowMap = createMapFromCSVHeaderAndRows(
      csvHeaderRow = csvHeaderRow,
      dataRows = dataRows
    )
      .get

    /*
      Logger.info(s"univocity parsed ${parser.getDetectedFormat} :: $parsedRows\n\n")
      Logger.info(s"\n\nHEADER parsed\n $rowMap\n\n")
      Logger.info(s"\n\nfirst parsed\n ${parsedRows(1)}\n\n")
      */

    rowMap

  }
}
*/
