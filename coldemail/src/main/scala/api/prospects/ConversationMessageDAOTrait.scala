package api.prospects

import api.accounts.TeamId
import eventframework.MessageObject
import utils.SRLogger

import scala.util.Try

sealed trait FindThreadMessageFlow

object FindThreadMessageFlow {
  case object InboxReplyDraft extends FindThreadMessageFlow
  case object GetConversationEmails extends FindThreadMessageFlow
  case object Inbox<PERSON><PERSON>ward<PERSON>mailDraft extends FindThreadMessageFlow
}

trait ConversationMessageDAOTrait {
  
  type MessageObjectType <: MessageObject

  def findThreadMessages(
                          threadId: Long,
                          teamId: TeamId,
                          allTrackingDomains: Seq[String],
                          findThreadMessageFlow: FindThreadMessageFlow
                        )(using logger: SRLogger): Try[Seq[MessageObject]]

}
