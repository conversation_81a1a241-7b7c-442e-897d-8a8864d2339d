package api.prospects.dao_service

import api.AppConfig
import api.accounts.dao.OrganizationDAO
import api.accounts.dao_service.OrganizationDAOService
import api.accounts.models.{AccountId, OrgId}
import api.accounts.service.OrganizationService
import api.accounts.{Account, AccountDAO, AccountService, OrganizationWithCurrentData, ProspectCategoriesInDB, TeamId}
import api.campaigns.dao.CampaignProspectDAO_2
import api.columns.{ColumnDef, ProspectColumnDef}
import api.prospects.{BlacklistStatusForProspectUuid, DeletedProspect, ExistingProspect, FetchProspectListStats, InferredQueryTimeline, InsertOrUpdateProspectResult, NewProspectsToBeAdded, ProspectAccountCreateFormData, ProspectAccountIdAndCustomId, ProspectBasicForBatchUpdate, ProspectBlacklistMatches, ProspectCreateFormData, ProspectEmailsTobeAddedInForceUpdateProspect, ProspectForMQTriggerPublish, ProspectList, ProspectListStats, ProspectNotesForIncomingCall, ProspectSource, ProspectUpdateFormData, ProspectsApiSearchParams, ProspectsToBeForceUpdated, SearchQuerySelectType}
import api.prospects.dao.{DuplicateProspectResult, ProspectCategoryDetails, ProspectDAO, ReorderProspectCategoryData, ReturnValueForFindOneForZapierSample}
import api.prospects.models.{ProspectCategory, ProspectCategoryId, ProspectCategoryNew, ProspectCategoryRank, ProspectDeletionReason, ProspectId, SrProspectColumns}
import api.search.{ProspectQuery, SearchQuery}
import eventframework.{ProspectBasicInfo, ProspectObject}
import org.joda.time.DateTime
import play.api.Logging
import play.api.libs.json.{JsObject, Json}
import utils.{AddonLimitReachedException, Helpers, ParseUtils, PlanLimitService, SRLogger}
import utils.dbutils.SQLUtils
import utils.emailvalidation.EmailValidationService
import utils.timezones.TimezoneUtils
import api.blacklist.Blacklist
import api.call.DAO.ProspectDetailsForIncomingCalls
import api.call.models.PhoneNumber
import api.campaigns.services.{CampaignId, MergeTagService}
import api.emails.EmailThreadDAO
import api.prospects.service.{ProspectDeduplicationDataWithId, ProspectEmail, ProspectEmailData}
import api.sr_audit_logs.models.EventType
import api.tasks.models.TaskStatus
import api.tasks.pgDao.TaskPgDAO
import api.tasks.services.{LinkedinTaskService, TaskDaoService}
import api.team_inbox.dao.ReplySentimentDAO
import api.team_inbox.dao_service.ReplySentimentDAOService
import api.team_inbox.model.ReplySentimentChannelType
import api.team_inbox.service.ReplySentimentForTeam
import api.teams_metadata.TeamsMetadataService
import io.sr.billing_common.models.AddonLicenceType
import scalikejdbc.*
import sr_scheduler.models.ChannelType
import utils.cache.models.SrResetCacheInterval
import utils.dbutils.DBUtils
import utils.linkedin.LinkedinHelperFunctions
import utils.mq.email.MQDomainServiceProviderDNSService
import utils.mq.webhook.model.TriggerSource
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

case class DuplicationFindProspectData(
                                        data: Seq[String],
                                        columnType: SrProspectColumns,
                                      )

case class DuplicationFindProspectDataV2(
                                          data: String,
                                          columnType: SrProspectColumns,
                                        )

class ProspectDAOService(
                          prospectDAO: ProspectDAO,
                          prospectQuery: ProspectQuery,
                          prospectColumnDef: ProspectColumnDef,
                          mergeTagService: MergeTagService,
                          replySentimentDAOService: ReplySentimentDAOService,
                          emailThreadDAO: EmailThreadDAO,
                          organizationDAOService: OrganizationDAOService,
                          accountDAO: AccountDAO,
                          planLimitService: PlanLimitService,
                          teamsMetadataService: TeamsMetadataService,
                          linkedinTaskService: LinkedinTaskService,
                          dbUtils: DBUtils,
                          mqDomainServiceProviderDNSService: MQDomainServiceProviderDNSService,
                          srRollingUpdateCoreService: SrRollingUpdateCoreService
                        ) extends Logging {

  // TEAMEDITION, MULTICAMPAIGN (DONE)
  //Prateek, Sathish
  def query(
             accountIds: Seq[Long],
             teamId: Long,
             orgId: Long,
             loggedinAccountId: Long,
             prospectQueryData: SearchQuery,
             account: Account,
             isInternalRequest: Boolean,
             Logger: SRLogger
           )(implicit ec: ExecutionContext): Future[JsObject] = {

    given srLogger: SRLogger = Logger // fixme given

//    val emailNotCompulsoryEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//      teamId = TeamId(teamId),
//      feature = SrRollingUpdateFeature.EmailNotCompulsory
//    )(Logger)

    val prospectSearchQuery = prospectQuery.getQuerySQL(
        permittedAccountIds = accountIds,
        teamId = teamId,
        orgId = orgId,
        data = prospectQueryData,
        account = account,
        isInternalRequest = isInternalRequest,
        fetchType = SearchQuerySelectType.FULL_OBJECT,
//        emailNotCompulsoryEnabled = Some(emailNotCompulsoryEnabled),
        Logger = Logger
      )


    Future.fromTry(prospectSearchQuery) map { case (rowsPerPage, _, query) =>

      val foundProspects: List[ProspectObject] = Helpers.logTimeTaken(
        Logger = Logger.appendLogRequestId("Prospect.querySQL"),
        thresholdInMillis = 500
      ) {

        prospectDAO.query(
          query = query,
          is_campaign = prospectQueryData.is_campaign
        )

      }

      val totalCount = foundProspects.length

      val showLoadMore = totalCount > rowsPerPage

      val takeProspectsForPage = getProspectObjectForFrontend(
        prospectObjects = foundProspects.take(rowsPerPage),
        team_id = teamId
      ).get

      Json.obj(
        "rows_per_page" -> rowsPerPage,
        "show_load_more" -> showLoadMore,
        "prospects" -> takeProspectsForPage.map(p => ProspectObject.apiStructure(
          prospect = p,
          isInternalRequest = isInternalRequest
        ))
        //        "page" -> prospectQuery.page
      )

    }


  }

  def updateLatestTaskDoneAt(prospectId: ProspectId, teamId: TeamId, time: DateTime) = {
    prospectDAO.updateLatestTaskDoneAt(
      prospectId = prospectId,
      teamId = teamId,
      time = time
    )
  }

  // TEAMEDITION, MULTICAMPAIGN (DONE)
  //Sathish
  def countQuery(
                  accountIds: Seq[Long],
                  teamId: Long,
                  orgId: Long,
                  loggedinAccountId: Long,
                  prospectQueryData: SearchQuery,
                  account: Account,
                  isInternalRequest: Boolean,
                  Logger: SRLogger
                )(implicit ec: ExecutionContext): Future[JsObject] = {

//    val emailNotCompulsoryEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//      teamId = TeamId(teamId),
//      feature = SrRollingUpdateFeature.EmailNotCompulsory
//    )(Logger)

    val queryRes = prospectQuery.getQuerySQL(
        permittedAccountIds = accountIds,
        teamId = teamId,
        orgId = orgId,
        data = prospectQueryData,
        account = account,
        isInternalRequest = isInternalRequest,
        fetchType = SearchQuerySelectType.COUNT_ONLY,
//        emailNotCompulsoryEnabled = Some(emailNotCompulsoryEnabled),
        Logger = Logger
      )


    Future.fromTry(queryRes) map { case (_, _, query) =>


      Logger.info(s"\n\n\n isInternalRequest $isInternalRequest")


      Helpers.logTimeTaken(
        Logger = Logger.appendLogRequestId("Prospect.countQuerySQL"),
        thresholdInMillis = 500
      ) {

        val totalCount = prospectDAO.countQuery(query = query)

        Json.obj(
          "total_prospects_count_in_applied_filters" -> totalCount
        )

      }
    }


  }

  def calculateSavedProspectsCountAndInsertToTeamsMetadata(teamId: TeamId)(
    implicit session: DBSession
  ): Try[Int] = {

    for {
      savedProspectsCount: Int <- prospectDAO.getTotalProspectsCountForTeam(teamId = teamId)

      insertCount: Int <- teamsMetadataService.insertSavedProspectsCountForTeam(
        teamId = teamId,
        savedProspectsCount = savedProspectsCount
      )
    } yield {
      insertCount
    }

  }

  //Sathish
  def IdsOnlyQuery(
                    accountIds: Seq[Long],
                    teamId: Long,
                    orgId: Long,
                    loggedinAccountId: Long,
                    prospectQueryData: SearchQuery,
                    account: Account,
                    isInternalRequest: Boolean,
                    Logger: SRLogger
                  )(implicit ec: ExecutionContext): Future[List[Long]] = {


//    val emailNotCompulsoryEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//      teamId = TeamId(teamId),
//      feature = SrRollingUpdateFeature.EmailNotCompulsory
//    )(Logger)

    val querySql: Try[(Int, Int, SQL[?, ?])] = prospectQuery.getQuerySQL(
        permittedAccountIds = accountIds,
        teamId = teamId,
        orgId = orgId,
        data = prospectQueryData,
        account = account,
        isInternalRequest = isInternalRequest,
        fetchType = SearchQuerySelectType.ID_ONLY,
//        emailNotCompulsoryEnabled = Some(emailNotCompulsoryEnabled),
        Logger = Logger
      )

    Future.fromTry(querySql) map { case (_, _, query) =>


      Logger.info(s"IdsOnlyQuery isInternalRequest $isInternalRequest")

      // 24th August 2021: no need to have localTx anymore because we are also removing the sql" SET LOCAL work_mem = '256MB' clause below
      val prospectIds = prospectDAO.IdsOnlyQuery(query = query)


      Logger.info(s"IdsOnlyQuery prospectIdsLength: ${prospectIds.length}")

      // FIXME VALUECLASS
      prospectIds.map(_.id)


    }


  }

  def findProspects(
                     byProspectIds: Seq[Long] = Seq(),

                     byProspectEmails: Seq[String] = Seq(),

                     byProspectAccountId: Option[Long] = None,

                     byCampaignId: Option[Long] = None,

                     teamId: Long,
                     org_id: OrgId,
                     permittedOwnerIds: Option[Seq[Long]] = None,

                     limit: Int = 500, // max count
                     offset: Int = 0, // only if pagination is there

                     isInternalRequest: Boolean = true,
                     findActiveCampaignsIfInternalRequest: Boolean = false,

                     queryTimelineForApi: Option[InferredQueryTimeline] = None
                   )(using logger: SRLogger): Try[Seq[ProspectObject]] = Try {

//    val emailNotCompulsoryEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//      teamId = TeamId(teamId),
//      feature = SrRollingUpdateFeature.EmailNotCompulsory
//    )
    prospectDAO.find(
      byProspectIds = byProspectIds,
      byProspectEmails = byProspectEmails,
      byProspectAccountId = byProspectAccountId,
      byCampaignId = byCampaignId,
      teamId = teamId,
      permittedOwnerIds = permittedOwnerIds,
      limit = limit,
      offset = offset,
      isInternalRequest = isInternalRequest,
      findActiveCampaignsIfInternalRequest = findActiveCampaignsIfInternalRequest,
      queryTimelineForApi = queryTimelineForApi,
      org_id = Some(org_id),
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    ) match {
      case Failure(exception) => throw exception

      case Success(prospects) => {

//        val isEmailNotCompulsoryEnabled = srRollingUpdateCoreService
//          .checkIfTeamCanReceiveEarlyUpdates(
//            teamId = TeamId(teamId),
//            feature = SrRollingUpdateFeature.EmailNotCompulsory
//          )


//        if (isEmailNotCompulsoryEnabled) {
          prospects
//        } else {
//          //if all prospect objects have email defined then it's valid
//          val filteredProspects: List[ProspectObject] = prospects.filter(_.email.isDefined).toList
//          if (filteredProspects.length != prospects.length) {
//            logger.shouldNeverHappen(s"EMAIL_COMPULSORY flow is having some emails as none. Prospects with empty email: ${prospects.filter(_.email.isEmpty).map(_.id)}")
//          }
//          filteredProspects
//        }
      }
    }
  }


  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  // use case : WebhookUtils.callCompletedHooks
  // use case : WebhookUtils.callInvalidEmailHooks
  // use case : WebhookUtils.callUnsubscribedHooks
  // use case : WebhookUtils_V2.getEmailReceivedForTriggerByReplyIds 1 use case : getEmailReceivedForTriggerByReplyIds, callNewRepliesHooks
  // use case : WebhookUtils_V2.getEmailReceivedForTriggerByReplyIds 2 use case : ZapierController.getSampleData
  // use case : WebhookUtils_V2._createSentHookResponseStructure 1 use case : WebhookUtils.callSentHooks
  // use case : WebhookUtils_V2._createSentHookResponseStructure 2 use case : ZapierController.getSampleData EMAIL_SENT
  // use case : WebHookUtils.callClickedHooks
  // use case : ZapierController.getSampleData .EventType.EMAIL_OPENED
  // use case : InboxController.getInboxDraft POST     /api/:v/inbox/conversations/get_inbox_draft
  // use case : CampaignControler.testStep POST    /api/:v/campaigns/:id/steps/send_test
  // use case : createSpamTest. POST    /api/:v/campaigns/:id/spam_tests
  // use case : createSpamTestsFor SpamTestCronService
  // use case : ProspectController.postFindByEmailForZapier POST    /api/:v/prospects/search_by_email_for_zapier
  // use case : ProspectAccountController.getCampaignsForProspectAccount GET     /api/:v/accounts/:id/campaigns
  // use case : ProspectController.findAll GET     /api/:v/prospects
  // use case : ProspectController.find GET     /api/:v/prospects/:prospectId
  // Use case : ProspectController.createOrUpdateOne - end-user uploads a single prospect via the form : POST    /api/:v/prospects
  // Use case : _handleEventAction SRTriggerActionType.ADD_PROSPECT_TO_CAMPAIGN HandleActivityTriggerEventService.__addProspectToCampaign
  // Use case : _handleEventAction SRTriggerActionType.REMOVE_PROSPECT_FROM_CAMPAIGN HandleActivityTriggerEventService.__removeProspectToCampaign
  // Use case : _handleEventAction SRTriggerActionType.COMPLETE_CAMPAIGN_FOR_PROSPECT HandleActivityTriggerEventService.__markProspectAsCompleted
  // use case : _handleEventAction.__createContactDataInCRM EventType.CREATED_PROSPECT_IN_SMARTREACH | EventType.UPDATED_PROSPECT_IN_SMARTREACH | EventType.CREATED_OR_UPDATED_PROSPECT_IN_SMARTREACH
  // use case : _handleEventAction.__createContactDataInCRM EventType.PROSPECT_CATEGORY_UPDATED
  // use case : handleUpdateLeadStatusInCRM
  // use case : handleUpdateActivityInCRM
  // use case : findIntegrationsForInvokeNewFlow
  // all conditions are treated as AND conditions
  //Prateek
  def find(
            byProspectIds: Seq[Long] = Seq(),

            byProspectEmails: Seq[String] = Seq(),

            byProspectAccountId: Option[Long] = None,

            byCampaignId: Option[Long] = None,

            teamId: Long,
            permittedOwnerIds: Option[Seq[Long]] = None,

            limit: Int = 500, // max count
            offset: Int = 0, // only if pagination is there

            isInternalRequest: Boolean = true,
            findActiveCampaignsIfInternalRequest: Boolean = false,

            queryTimelineForApi: Option[InferredQueryTimeline] = None,

            Logger: SRLogger

          ): Try[Seq[ProspectObject]] = {

    given srLogger: SRLogger = Logger // fixme given

    //      if (
    //        byProspectIds.length > 10000 ||
    //          byProspectEmails.length > 500
    //      ) {
    //
    //        Logger.error(s"FATAL: more than 500 Prospect.findMultiple: ${byProspectIds.length} ids :: ${byProspectEmails.length} emails :: $teamId $permittedOwnerIds $isInternalRequest")
    //
    //        throw new Exception("You can find upto 500 prospects at a time")
    //
    //
    //      } else {

    // PROSPECTS_EMAILS_TODO_READ_CLEANED

    for {
      //fetching orgId from teamId because this is getting called in many places and few paths don't have orgId
      org_id: OrgId <- organizationDAOService.getOrgIdFromTeamId(teamId = TeamId(teamId))

      prospectObjects: Seq[ProspectObject] <- findProspects(
        byProspectIds = byProspectIds,
        byProspectEmails = byProspectEmails,
        byProspectAccountId = byProspectAccountId,
        byCampaignId = byCampaignId,
        teamId = teamId,
        org_id = org_id,
        permittedOwnerIds = permittedOwnerIds,
        limit = limit,
        offset = offset,
        isInternalRequest = isInternalRequest,
        findActiveCampaignsIfInternalRequest = findActiveCampaignsIfInternalRequest,
        queryTimelineForApi = queryTimelineForApi
      ).flatMap { list =>
        getProspectObjectForFrontend(
          prospectObjects = list,
          team_id = teamId
        )
      }
    } yield {
      prospectObjects
    }

  }


  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  // use case : CampaignController.testStep POST    /api/:v/campaigns/:id/steps/send_test
  // use case : createSpamTest. POST    /api/:v/campaigns/:id/spam_tests
  // use case : createSpamTestsFor SpamTestCronService
  //Prateek
  def findOneByCampaignId(campaignId: Long, teamId: Long, Logger: SRLogger): Option[ProspectObject] = {

    find(
      byCampaignId = Some(campaignId),
      teamId = teamId,
      limit = 1,
      Logger = Logger
    ).toOption.flatMap(_.headOption)

  }

  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  // use case : ProspectController.postFindByEmailForZapier POST    /api/:v/prospects/search_by_email_for_zapier
  //Prateek
  def findByEmailForZapier(prospectEmail: String, teamId: Long, Logger: SRLogger): Try[Option[JsObject]] = Try {

    val prospectOpt = find(
      byProspectEmails = Seq(prospectEmail),
      teamId = teamId,
      limit = 1,
      isInternalRequest = false,
      Logger = Logger
    ).get.headOption

    prospectOpt.map(p => ProspectObject.apiStructure(p, isWebhook = true))
  }

  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  // use this to fetch value from master db immediately after update
  // ALSO: Using DB bcz we are using it zapier webhook
  // use case : callProspectCategoryUpdateHooks
  // use case : ZapierController.getSampleData EventType.PROSPECT_COMPLETED
  // use case : ZapierController.getSampleData EventType.PROSPECT_OPTED_OUT
  // use case : ZapierController.getSampleData EventType.PROSPECT_CATEGORY_UPDATED
  // use case : ZapierController.getSampleData EventType.EMAIL_INVALID
  // use case : ProspectController.updateForEmail PUT     /api/:v/prospects/update_for_email
  // use case : ProspectController.update PUT     /api/:v/prospects/:id (2 calls)
  // use case : ProspectAccountController.getEvents GET     /api/:v/accounts/:id/events
  // use case : hasProspect trait
  // use case : CampaignController.processTestStep
  // use case : MQEmail.processMessage
  //Prateek
  def findFromMaster(
                      teamId: Long,
                      prospectId: Long,
                      isInternalRequest: Boolean = true,
                      Logger: SRLogger,
                      orgId: Option[OrgId] = None
                    ): Try[Option[ProspectObject]] = Try {

//    val emailNotCompulsoryEnabled = srRollingUpdateCoreService
//      .checkIfTeamCanReceiveEarlyUpdates(
//        teamId = TeamId(teamId),
//        feature = SrRollingUpdateFeature.EmailNotCompulsory
//      )(Logger)

    prospectDAO.findFromMaster(
      prospectId = prospectId,
      isInternalRequest = isInternalRequest,
      orgId = orgId,
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    ) match {
      case Failure(exception) => throw exception
      case Success(value) =>
        value match {
          case None => None
          case Some(prospectObject) =>

            val orgId = organizationDAOService.getOrgIdFromTeamId(
              TeamId(teamId)
            ) match {
              case Failure(exception) => throw exception

              case Success(org_id) => org_id
            }


//            val emailNotCompulsoryEnabled = srRollingUpdateCoreService
//              .checkIfTeamCanReceiveEarlyUpdates(
//                teamId = TeamId(teamId),
//                feature = SrRollingUpdateFeature.EmailNotCompulsory
//              )(Logger)

//            val validProspectObject = if (emailNotCompulsoryEnabled) {
//              List(prospectObject)
//            } else {
//              //if all prospect objects have email defined then it's valid
//              val filteredProspects: List[ProspectObject] = List(prospectObject).filter(_.email.isDefined).toList
//              if (filteredProspects.length != List(prospectObject).length) {
//                logger.warn(s"EMAIL_COMPULSORY flow is having some emails as none. Prospects with empty email: ${List(prospectObject).filter(_.email.isEmpty).map(_.id)}")
//              }
//              filteredProspects
//            }

            getProspectObjectForFrontend(prospectObject = prospectObject)(
              logger = Logger
            ) match {
              case Failure(err) =>

                Logger.fatal(s"Error while getting ReplySentimentForTeam For ProspectObject id - ${prospectObject.id} teamId- ${prospectObject.team_id}", err)
                Some(prospectObject)

              case Success(value) =>
                Some(value)
            }


        }
    }

  }

  def getProspectDetailsForIncomingCallFromNumber(phone_number: PhoneNumber, teamId: TeamId): Try[ProspectDetailsForIncomingCalls] = {
    prospectDAO.getProspectIdsFromPhoneNumber(
      phone_number = phone_number,
      teamId = teamId
    ).map(prospectIds => {

      if (prospectIds.isEmpty) {
        ProspectDetailsForIncomingCalls(
          prospectId = None,
          prospectNotes = None
        )

      } else if (prospectIds.length == 1) {
        ProspectDetailsForIncomingCalls(
          prospectId = Some(prospectIds.head),
          prospectNotes = None
        )

      } else {
        ProspectDetailsForIncomingCalls(
          prospectId = None,
          prospectNotes = Some(ProspectNotesForIncomingCall.MULTIPLE_IDS_PRESENT)
        )

      }

    })

  }

  def findProspectAndFailIfNotPresent(
                                       prospectId: ProspectId,
                                       teamId: TeamId,
                                       Logger: SRLogger
                                     ): Try[ProspectObject] = {

    findFromMaster(
      prospectId = prospectId.id,
      teamId = teamId.id,
      Logger = Logger
    )
      .flatMap {
        case None => Failure(new Exception(s"${prospectId} not present."))
        case Some(prospectObject) => Success(prospectObject)
      }

  }

  def getLinkedinUrlFromProspectObject(prospectObject: ProspectObject) = {
    prospectObject.linkedin_url match {
      case None => Failure(new Exception(s"Linkedin URL not present for prospect_${prospectObject.id}"))
      case Some(url) => Success(url)
    }
  }

  def getLinkedinUrlByProspectId(
                                  teamId: TeamId,
                                  prospectId: ProspectId,
                                  Logger: SRLogger
                                ): Try[String] = {

    for {
      prospectObj: ProspectObject <- findProspectAndFailIfNotPresent(
        prospectId = prospectId,
        teamId = teamId,
        Logger = Logger
      )

      linkedinUrl <- getLinkedinUrlFromProspectObject(
        prospectObject = prospectObj
      )

    } yield {
      linkedinUrl
    }

  }

  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  // use case : callProspectCategoryUpdateHooks
  // use case : webhookUtils.callOpenedHooks
  //use case : callClickedHooks
  //Prachi
  def findForWebhook(
                      teamId: TeamId,
                      prospectId: ProspectId,
                      Logger: SRLogger
                    ): Option[ProspectObject] = {

    val result = findFromMaster(
      // FIXME VALUECLASS
      prospectId = prospectId.id,
      teamId = teamId.id,
      isInternalRequest = false,
      Logger = Logger
    )

    result match {
      case Failure(exception) =>
        Logger.error(s"FAILED: Prospect.findForWebhook: ", exception)
        None
      case Success(prospectObject) =>
        prospectObject
    }
  }

  def getProspectIdByLinkedinUrlAndTeamId(
                                           teamId: TeamId,
                                           linkedin_url: String
                                         ): Try[List[ProspectId]] = {

    prospectDAO.getProspectIdByLinkedinUrlAndTeamId(
      teamId = teamId,
      linkedinUrl = LinkedinHelperFunctions.normalizeLinkedInURL(Some(linkedin_url)).get
    )

  }

  // TEAMEDITION check: DONE
  // PROSPECTS_EMAILS_TODO_READ_CLEANED calling findFromMaster
  // use case : ZapierController.getSampleData EventType.PROSPECT_COMPLETED
  // use case : ZapierController.getSampleData EventType.PROSPECT_OPTED_OUT
  //Prateek
  def findOneForZapierSample(

                              permittedAccountIds: Seq[Long],
                              teamId: Long,
                              eventType: EventType,
                              Logger: SRLogger

                            ): Try[Option[(ProspectObject, String, Long)]] = {


    for {
      findOneForZapierSampleOpt: Option[ReturnValueForFindOneForZapierSample] <- prospectDAO.findOneForZapierSample(
        permittedAccountIds = permittedAccountIds,
        teamId = teamId,
        eventType = eventType,
        logger = Logger
      )
      fromMasterOpt: Option[ProspectObject] <- findFromMaster(
        prospectId = findOneForZapierSampleOpt.get.prospect_id,
        teamId = teamId,
        Logger = Logger
      )

    } yield {

      for {
        fromMaster: ProspectObject <- fromMasterOpt
        findOneForZapierSample: ReturnValueForFindOneForZapierSample <- findOneForZapierSampleOpt
      } yield {
        (fromMaster, findOneForZapierSample.campaign_name, findOneForZapierSample.campaign_id)
      }
    }
  }


  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  // used while exporting prospect lists, we were getting a I/O exception in the other find
  // use case : ProspectController.batchActions "export"
  //Prateek
  def findForExport(
                     prospectIds: Seq[Long],
                     teamId: Long,
                   )(
                     using Logger: SRLogger
                   ): Try[Seq[ProspectObject]] = Try {


//    val emailNotCompulsoryEnabled = srRollingUpdateCoreService
//      .checkIfTeamCanReceiveEarlyUpdates(
//        teamId = TeamId(teamId),
//        feature = SrRollingUpdateFeature.EmailNotCompulsory
//      )(Logger)

    val result = prospectIds
      .distinct
      .sorted
      // NOTE 1st Nov 2021: we tested groups of 2500 vs 5000 and found the 5000 groups to be overall faster for large prospect sets (>50000)
      .grouped(5000)
      .flatMap(groupedPids => {

        val startPidInGroup = groupedPids.head
        val endPidInGroup = groupedPids.last
        prospectDAO.findForExport(
          teamId = teamId,
          startPidInGroup = startPidInGroup,
          endPidInGroup = endPidInGroup,
          groupedPids = groupedPids,
//          emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
        )

      }).toSeq

    getProspectObjectForFrontend(
      prospectObjects = result,
      team_id = teamId
    )


  }.flatten


  // TEAMEDITION check: DONE
  //Prateek
  def findOneForZapierClickedSample(
                                     permittedAccountIds: Seq[Long],
                                     teamId: Long
                                   ): Option[String] = {
    prospectDAO.findOneForZapierClickedSample(
      permittedAccountIds = permittedAccountIds,
      teamId = teamId
    )
  }


  // TEAMEDITION check: DONE
  // PROSPECTS_EMAILS_TODO_READ_CLEANED calling findFromMaster
  // use case : ZapierController.use case : ZapierController.getSampleData EventType.PROSPECT_CATEGORY_UPDATED
  //Prateek
  def findOneForZapierUpdatedProspectCategorySample(
                                                     permittedAccountIds: Seq[Long],
                                                     teamId: Long,
                                                     Logger: SRLogger
                                                   ): Try[Option[(ProspectObject, String)]] = {

    for {

      dataOpt: Option[(Long, String)] <- prospectDAO.findOneForZapierUpdatedProspectCategorySample(
        permittedAccountIds = permittedAccountIds, teamId = teamId
      )
      fromMasterOpt: Option[ProspectObject] <- {
        dataOpt match {
          case Some(value) => findFromMaster(prospectId = value._1, teamId = teamId, Logger = Logger)
          case None => Failure(new Throwable("findOneForZapierUpdatedProspectCategorySample didn't find any data"))
        }
      }
    } yield {

      for {
        data: (Long, String) <- dataOpt
        fromMaster: ProspectObject <- fromMasterOpt
      } yield {
        (fromMaster, data._2)
      }

    }


  }

  // TEAMEDITION check: DONE
  // PROSPECTS_EMAILS_TODO_READ_CLEANED calling findFromMaster
  // use case : ZapierController.getSampleData EventType.EMAIL_INVALID
  //Prachi
  def findOneForZapierEmailInvalidSample(
                                          permittedAccountIds: Seq[Long],
                                          teamId: Long,
                                          Logger: SRLogger
                                        ): Try[Option[ProspectObject]] = {


    for {
      prospectIdOpt: Option[Long] <- prospectDAO.findOneForZapierEmailInvalidSample(
        permittedAccountIds = permittedAccountIds,
        teamId = teamId,
        Logger = Logger
      )

      prospectObject: Option[ProspectObject] <- {
        prospectIdOpt match {
          case None => Success(None)
          case Some(prospectId) => findFromMaster(
            teamId = teamId,
            prospectId = prospectId,
            Logger = Logger
          )
        }
      }
    } yield prospectObject

  }

  def updateProspectsForEmailOptional(
                                       ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads: Boolean,
                                       prospectCustomColumnDefs: Seq[ColumnDef],
                                       listIdOpt: Option[Long],
                                       prospectsToBeUpdated: Iterable[ProspectsToBeForceUpdated],
                                       deduplicationColumns: Seq[SrProspectColumns],
                                       newProspectEmailsTobeAdded: List[ProspectEmailsTobeAddedInForceUpdateProspect],
                                       teamId: Long
                                     )(using Logger:SRLogger): Try[List[InsertOrUpdateProspectResult]] = {
    prospectDAO.updateProspectsForEmailOptional(
      ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads,
      prospectCustomColumnDefs = prospectCustomColumnDefs,
      listIdOpt = listIdOpt,
      prospectsToBeUpdated = prospectsToBeUpdated,
      newProspectEmailsTobeAdded = newProspectEmailsTobeAdded,
      deduplicationColumns = deduplicationColumns,
      teamId = teamId
    )
  }

  def updateProspects(
                       ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads: Boolean,
                       prospectCustomColumnDefs: Seq[ColumnDef],
                       listIdOpt: Option[Long],
                       prospectsToBeUpdated: Iterable[ProspectsToBeForceUpdated],
                       teamId: Long
                     ): Try[List[InsertOrUpdateProspectResult]] = {
    prospectDAO.updateProspects(
      ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads,
      prospectCustomColumnDefs = prospectCustomColumnDefs,
      listIdOpt = listIdOpt,
      prospectsToBeUpdated = prospectsToBeUpdated,
      teamId = teamId
    )
  }

  def insertNewProspects(
                          newProspectsToBeAdded: Iterable[NewProspectsToBeAdded],
                          blacklistStatusForProspectUuid: Seq[BlacklistStatusForProspectUuid],
                          do_not_contact_category_id: Long,
                          not_categorized_category_id: Long,
                          prospectSource: Option[ProspectSource.Value],
                          prospectAccountId: Option[Long],
                          prospectAccounts: Seq[ProspectAccountIdAndCustomId],
                          ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads: Boolean,
                          prospectCustomColumnDefs: Seq[ColumnDef],
                          prospectOwnerAccountId: Long,
                          prospectAddedByAccountId: Long,
                          teamId: Long,
                          triggerSource: TriggerSource,
                          taId: Long,
                          listIdOpt: Option[Long],
                          Logger: SRLogger
                        ): Try[List[InsertOrUpdateProspectResult]] = {

    given logger: SRLogger = Logger

    for {
      orgId: OrgId <- organizationDAOService.getOrgIdFromTeamId(teamId = TeamId(teamId))

      addonLicenseTypeToCheck: AddonLicenceType = AddonLicenceType.ProspectsSaved

      /*
      1. in all flows, if limit is hit, we should not add anymore prospects
      2. in all flows, if limit is hit, we should bring up the banner
      3. in non-crm flows, if limit is hit, we should return a error that the user can see
      4. in crm flows, if limit is hit also, we will not add prospects, and we will also not throw error (because that will pause all the workflows for the crm). Instead we will just log an error, and enable the banner for the user, and return success only from this fn.

       */

      insertedProspects: List[InsertOrUpdateProspectResult] <- {
        planLimitService.checkLimitAccordingToAddonLicenseTypeUsingOrgId(
          orgId = orgId,
          addonLicenceType = addonLicenseTypeToCheck,
          isAddingNewTeam = false,
        ) match {
          case Failure(AddonLimitReachedException(message, cause)) =>
            if (triggerSource == TriggerSource.CRM) {
              logger.error(s"Ignoring insertNewProspects :: plan limit hit :: TriggerPath: CRM :: $message")
              Success(List())
            }
            else {
              Failure(AddonLimitReachedException(message, cause))
            }

          case Failure(e) =>
            Failure(e)

          case Success(_) =>
            for {
              insertedNewProspects: List[InsertOrUpdateProspectResult] <- prospectDAO.insertNewProspects(
                newProspectsToBeAdded = newProspectsToBeAdded,
                blacklistStatusForProspectUuid = blacklistStatusForProspectUuid,
                do_not_contact_category_id = do_not_contact_category_id,
                not_categorized_category_id = not_categorized_category_id,
                prospectSource = prospectSource,
                prospectAccountId = prospectAccountId,
                prospectAccounts = prospectAccounts,
                ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads,
                prospectCustomColumnDefs = prospectCustomColumnDefs,
                prospectOwnerAccountId = prospectOwnerAccountId,
                prospectAddedByAccountId = prospectAddedByAccountId,
                teamId = teamId,
                taId = taId,
                listIdOpt = listIdOpt,
                Logger = Logger
              )

              incrementedCount: Int <- teamsMetadataService.incrementOrDecrementSavedProspectsCount(
                teamId = TeamId(teamId),
                incrementOrDecrementCount = insertedNewProspects.size
              )
            } yield {
              insertedNewProspects
            }

        }
      }

    } yield {
      insertedProspects
    }

  }


  /*
    def insertOrUpdateMultiRow(
                                filteredProspectsData: Iterable[(ProspectAccountCreateFormData, ProspectCreateFormData, String)],
                                blacklist: Seq[Blacklist],
                                do_not_contact_category_id: Long,
                                not_categorized_category_id: Long,
                                prospectSource: Option[ProspectSource.Value],
                                prospectAccountId: Option[Long],
                                prospectAccounts: Seq[ProspectAccountIdAndCustomId],
                                ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads: Boolean,
                                forceUpdateProspects: Boolean,
                                prospectCustomColumnDefs: Seq[ColumnDef],
                                prospectOwnerAccountId: Long,
                                prospectAddedByAccountId: Long,
                                teamId: Long,
                                taId: Long,
                                listIdOpt: Option[Long],
                                Logger: SRLogger
                              ): Try[List[DBResult]] = {
      prospectDAO.insertOrUpdateMultiRow(
        filteredProspectsData = filteredProspectsData,
        blacklist = blacklist,
        do_not_contact_category_id = do_not_contact_category_id,
        not_categorized_category_id = not_categorized_category_id,
        prospectSource = prospectSource,
        prospectAccountId = prospectAccountId,
        prospectAccounts = prospectAccounts,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads,
        forceUpdateProspects = forceUpdateProspects,
        prospectCustomColumnDefs = prospectCustomColumnDefs,
        prospectOwnerAccountId = prospectOwnerAccountId,
        prospectAddedByAccountId = prospectAddedByAccountId,
        teamId = teamId,
        taId = taId,
        listIdOpt = listIdOpt,
        Logger = Logger
      )
    }
  */

  // TEAMEDITION check DONE
  // CALL_UPDATE_PROSPECT_TRIGGER: DONE in ProspectController
  /*
    * PROSPECTS_EMAILS_TODO_UPDATE --- CLEANUP_AFTER_READS
   * email in this field impacts prospects_emails change
   */
  def update(
              permittedAccountIds: Seq[Long],
              actingAccountId: Long,
              prospect_owner_account_id: AccountId,
              id: Long,
              teamId: Long,
              updateProspect: ProspectUpdateFormData
            )(
              implicit Logger: SRLogger
            ): Try[Option[Long]] = Try {

    val prospectCustomColumnDefs = prospectColumnDef.findCustomColumns(teamId = teamId).filter(_.is_custom)

    val p = updateProspect

    val timezone = if (
      p.timezone.nonEmpty
        && p.timezone.get.nonEmpty
        && TimezoneUtils.isValidTimezone(tz = p.timezone.get.trim)
    ) {
      TimezoneUtils.checkAndReturnValidTimezone(p.timezone.get.trim)
    } else {
      TimezoneUtils.getTimezoneForCountry(p.country)
    }

    //      println(s"update prospect\n\n ${p.timezone.nonEmpty} ${p.timezone.get.nonEmpty} ${p.timezone} \n\n ${p.country} \n\n ${TimezoneUtils.getTimezoneForCountry(p.country)} \n\n ${timezone}\n")

    // if listName is there, find or create list and return list's id
    val listIdOpt: Option[Long] = findOrCreateList(
      listName = updateProspect.list,
      permittedAccountIds = permittedAccountIds,
      listOwnerAccountId = actingAccountId,
      teamId = teamId
    )

    val customFields = ColumnDef.parseCustomFields(
      customFieldsInput = p.custom_fields,
      customColumnDefs = prospectCustomColumnDefs,
      ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = false // the update function used here doesnt support this
    )

    val updateProspectsTable = () => {
      prospectDAO.updateProspectsTableResult(
        id = id,
        teamId = teamId,
        permittedAccountIds = permittedAccountIds,
        p = p,
        customFields = customFields,
        timezone = timezone,
        listIdOpt = listIdOpt
      )
    }

    val upsertProspectsEmails = () => {

      if (p.email.isDefined && p.email.get.nonEmpty) {

        val (_, emailDomain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(p.email.get)
        val upsertedProspect = prospectDAO.upsertProspectsEmailsTableResult(
          id = id,
          teamId = teamId,
          p = p,
          prospect_owner_account_id = prospect_owner_account_id,
          emailDomain = emailDomain
        )
        mqDomainServiceProviderDNSService.publish(emailDomain) match {
          case Success(value) => //DO NOTHING
          case Failure(exception) => Logger.fatal(s"failed to push to mqDomainServiceProviderDNSService", exception)
        }
        upsertedProspect
      } else {
        None
      }

    }

    val updatedProspect: Try[Option[Long]] = Try {
      updateProspectsTable() match {
        case None =>
          throw new Throwable("UPDATE was not successful on prospects table")

        case Some(id) =>

          upsertProspectsEmails()
          Some(id)
      }
    }

    updatedProspect.map { prospectId =>

      if (prospectId.isDefined) {
        // on updating a prospect, remove any to_check errors that may have been associated with the prospect earlier
        mergeTagService._removeMissingErrorMessage(prospectIds = Seq(prospectId.get))
        if (p.linkedin_url.isDefined && p.linkedin_url.get.trim != "") {
          linkedinTaskService.moveTasksToDueIfProspectLinkedinUrlIssueIsResolved(
            prospectId = ProspectId(prospectId.get),
            teamId = TeamId(teamId)
          )
        }

      }

      prospectId
    }

  }.flatten


  // TEAMEDITION check DONE
  def delete(
              prospectIds: List[ProspectId],
              permittedAccountIds: Seq[Long],
              teamId: TeamId,
              logger: SRLogger
            ): Try[Int] = Try {

    given srLogger: SRLogger = logger // fixme given

    val deleting = prospectIds
      .grouped(500)
      .zipWithIndex
      .flatMap { case (groupedPids, index) => {


        val dbAndSession = dbUtils.startLocalTx()

        val db = dbAndSession.db
        implicit val session = dbAndSession.session

        val deletedProspects: Try[List[Long]] = for {

          //1. find all threads with folder as prospect and change to non-prospect
          _: Int <- emailThreadDAO.updateFolderToNonProspect(
            prospect_ids = groupedPids,
            team_id = teamId
          )

          //2. delete prospect
          deletedProspects: List[Long] <- prospectDAO.delete(
            groupedPids = groupedPids,
            permittedAccountIds = permittedAccountIds,
            teamId = teamId,
            index = index,
            logger = logger
          )

          _: Int <- teamsMetadataService.incrementOrDecrementSavedProspectsCount(
            teamId = teamId,
            incrementOrDecrementCount = -deletedProspects.size
          )
        } yield {
          deletedProspects
        }

        dbUtils.commitAndCloseSession(db = db)

        deletedProspects.get


      }

      }
      .toSeq


    //      ProspectDeletingStats(
    //        total_deleted = deleting.size,
    //        invalid_email = deleting.count(_._2),
    //        email_bounced = deleting.count(_._3),
    //        force_sent = deleting.count(_._4),
    //        force_sent_and_bounced = deleting.count(e => e._3 && e._4),
    //        prospect_ids_for_deletion = deleting.map(_._1)
    //      )

    deleting.size
  }


  def deleteOneProspectById(
                             prospectId: ProspectId,
                             teamId: TeamId
                           )(using logger: SRLogger): Try[Option[DeletedProspect]] = {

    val dbAndSession = dbUtils.startLocalTx()

    val db = dbAndSession.db
    implicit val session = dbAndSession.session

    val deletedProspect: Try[Option[DeletedProspect]] = for {

      //1. find all threads with folder as prospect and change to non-prospect
      _: Int <- emailThreadDAO.updateFolderToNonProspect(
        prospect_ids = Seq(prospectId),
        team_id = teamId
      )

      //2. delete prospect
      deletedProspect: Option[DeletedProspect] <- prospectDAO.deleteOneById(
        prospectId = prospectId,
        teamId = teamId
      )

      _: Int <- teamsMetadataService.incrementOrDecrementSavedProspectsCount(
        teamId = teamId,
        incrementOrDecrementCount = if (deletedProspect.isDefined) -1 else 0
      )

    } yield {
      deletedProspect
    }

    dbUtils.commitAndCloseSession(db = db)

    deletedProspect
  }

  // TEAMEDITION check DONE
  //Prateek
  def scheduleForDeletion(
                           prospectIds: Seq[ProspectId],
                           permittedAccountIds: Seq[Long],
                           teamId: TeamId,
                           deletionReason: ProspectDeletionReason,
                           logger: SRLogger
                         ): Try[(Int, Seq[Long])] = Try {

    val savedForDeletion = prospectIds
      .distinct
      .grouped(500)
      .zipWithIndex
      .flatMap { case (groupedPids, index) => {

        val will_delete_at = deletionReason match {
          case ProspectDeletionReason.ByUser => DateTime.now()
          case ProspectDeletionReason.ByMergeDuplicates => DateTime.now().plusDays(AppConfig.daysAfterMergedDuplicateProspectsGetDeleted)
        }

        prospectDAO.scheduleForDeletion(
          groupedPids = groupedPids,
          permittedAccountIds = permittedAccountIds,
          teamId = teamId,
          index = index,
          deletionReason = deletionReason,
          will_delete_at = will_delete_at,
          logger = logger
        ).get

      }
      }
      .toSeq

    //    ProspectDeletingStats(
    //      total_deleted = savedForDeletion.size,
    //      invalid_email = savedForDeletion.count(_._2),
    //      email_bounced = savedForDeletion.count(_._3),
    //      force_sent = savedForDeletion.count(_._4),
    //      force_sent_and_bounced = savedForDeletion.count(e => e._3 && e._4),
    //      prospect_ids_for_deletion = savedForDeletion.map(_._1)
    //    )

    (savedForDeletion.size, savedForDeletion)

  }


  // Updating prospect last_contacted_at
  // savedRepliesWithProspectIdOnly: Seq[(prospectId, sentAt)]
  //Sathish
  def _updateProspectLastContactedAt(
                                      savedRepliesWithProspectIdOnly: Seq[(Long, DateTime)],
                                      team_id: TeamId,
                                      channel_type: ChannelType
                                    ): Try[Seq[Long]] = {

    prospectDAO._updateProspectLastContactedAt(
      savedRepliesWithProspectIdOnly = savedRepliesWithProspectIdOnly,
      channel = channel_type,
      team_id = team_id
    )

  }


  /*
  * PROSPECTS_EMAILS_TODO_UPDATE
 * use case for secondary email and primary emails
 * need to confirm that any email view/link click increments this count
 */
  // prospectData: Seq[(prospectId, repliedAtDate)]
  //Prateek
  def updateLastRepliedAt(
                           prospectData: Seq[(Long, DateTime)],
                           teamId: Long
                         ): Try[Seq[Long]] = {

    prospectDAO.updateLastRepliedAt(
      prospectData = prospectData,
      teamId = teamId
    )
  }


  def getPermittedProspects(
                             prospectIds: List[Long],
                             teamId: Long,
                             permittedAccountIds: Seq[Long]
                           ): Try[List[Long]] = {

    prospectDAO.getPermittedProspects(
      prospectIds = prospectIds,
      teamId = teamId,
      permittedAccountIds = permittedAccountIds
    )
  }


  def getTaId(
               assignToAccountId: Long,
               teamId: Long
             ): Option[Long] = {
    prospectDAO.getTaId(
      assignToAccountId = assignToAccountId,
      teamId = teamId
    )

  }

  //PROSPECTS_EMAILS_TODO_UPDATE_KEEP_BOTH_UPDATE
  //Prateek
  def assignToTeamMember(
                          prospectIdsToBeAssignedToNewTeamMember: List[Long],
                          assignToAccountId: Long,
                          taId: Option[Long]
                        ): Try[Seq[Long]] = {

    prospectDAO.assignToTeamMember(
      prospectIdsToBeAssignedToNewTeamMember = prospectIdsToBeAssignedToNewTeamMember,
      assignToAccountId = assignToAccountId,
      taId = taId
    )

  }


  /*
  * PROSPECTS_EMAILS_TODO_UPDATE_KEEP_BOTH_UPDATE
  *
 * since the team_id / prospect_id are in the prospects_emails table
 * hence this update has to be modified to also handle that scenario.
 *
 * one question:
 * if a campaign is running and prospects are part of the running campaign
 * do we allow an ownership change ?
 *
 * Use case : Prospects and then uploaded into the prospects table via createOrUpdateProspects
 *
 */

  // FIXME: should we CALL_UPDATE_PROSPECT_TRIGGER ?
  //Prateek
  def forceChangeOwnershipFromUpload(
                                      accountId: Long,
                                      teamId: Long,
                                      taId: Long,
                                      prospectIds: List[Long]
                                    ): Try[List[Long]] = {

    prospectDAO.forceChangeOwnershipFromUpload(
      accountId = accountId,
      teamId = teamId,
      taId = taId,
      prospectIds = prospectIds
    )

  }


  /*
   * // use case : MQDoNotContact createOrUpdateBlacklist PROSPECTS_EMAILS_TODO_READ_CLEANED
   *
   * email and email_domain have moved to prospects_emails table hence
   * there is an impact on this code.
   */

  def findProspectIdsMatchingExcludedEmailsAndDNC(
                                                   teamId: Long,
                                                   excludedEmails: Seq[String],
                                                   donotContactCategoryIdCustom: Long,
                                                   Logger: SRLogger
                                                 ): Try[Seq[ProspectBlacklistMatches]] = {

    //Try{
    //DB readOnly { implicit session =>

    val excludedEmailsLower: Seq[String] = excludedEmails.map(_.toLowerCase.trim)

    prospectDAO.findProspectIdsMatchingExcludedEmailsAndDNC(
      teamId = teamId,
      excludedEmailsLower = excludedEmailsLower,
      donotContactCategoryIdCustom = donotContactCategoryIdCustom,
      Logger = Logger
    )


  }




  /*
  * PROSPECTS_EMAILS_TODO_READ_CLEANED
  *
   * impact here as email, email_domain has moved to prospects_emails table
   *
   * USE CASE : While create/update blacklist (BlacklistService.createOrUpdateBlacklist) need to check if the prospect_id already in blacklist
   */
  /*
    def findProspectIdsMatchingBlacklistedEmailsAndDomains(
      teamId: Long,
      excludedEmailsLower: Seq[String],
      domainsLower: Seq[String],
      emailsLower: Seq[String]
    ): Try[Seq[ProspectBlacklistMatches]] = Try {

      DB readOnly { implicit session =>

        // check against lower(email), lower(email_domain)
        /*
        * we can do this as
        * 1. double migration and moving the code to the new table after the reads are modified
        * 2. If the data is present in prospects_emails table then use that query
        *    otherwise use the older query to get from prospects and at same time populate the data into prospects_emails
        */


        (domainsLower.nonEmpty, emailsLower.nonEmpty) match {

          case (false, false) => Seq()

          case (false, true) =>

            sql"""
                SELECT id, email, email_domain, prospect_category_id_custom FROM prospects
                WHERE team_id = $teamId
                AND lower(email) IN ${SQLUtils.generateSQLValuesClause(emailsLower)}
                ;
              """
              .map(rs => ProspectBlacklistMatches(
                id = rs.long("id"),
                email = rs.string("email"),
                email_domain = rs.string("email_domain"),
                prospect_category_id_custom = rs.long("prospect_category_id_custom")
              ))
              .list
              .apply()

          case (true, false) =>

            val excludedEmailsclause = if(excludedEmailsLower.nonEmpty)
              sqls"  AND lower(email) NOT IN ${SQLUtils.generateSQLValuesClause(excludedEmailsLower)}"
            else
              sqls""

            sql"""
                 SELECT id, email, email_domain, prospect_category_id_custom FROM prospects
                 WHERE team_id = $teamId
                 AND lower(email_domain) IN ${SQLUtils.generateSQLValuesClause(domainsLower)} $excludedEmailsclause
                 ;
               """
              .map(rs => ProspectBlacklistMatches(
                id = rs.long("id"),
                email = rs.string("email"),
                email_domain = rs.string("email_domain"),
                prospect_category_id_custom = rs.long("prospect_category_id_custom")
              ))
              .list
              .apply()

          case (true, true) =>

            val domainsClauseWithExcludedEmailsCheck = if(excludedEmailsLower.nonEmpty)
              sqls" OR (lower(email_domain) IN ${SQLUtils.generateSQLValuesClause(domainsLower)} AND lower(email) NOT IN ${SQLUtils.generateSQLValuesClause(excludedEmailsLower)})"
            else
              sqls" OR lower(email_domain) IN ${SQLUtils.generateSQLValuesClause(domainsLower)}"

            sql"""
                 SELECT id, email, email_domain, prospect_category_id_custom FROM prospects
                 WHERE team_id = $teamId
                 AND (
                   lower(email) IN ${SQLUtils.generateSQLValuesClause(emailsLower)}
                   $domainsClauseWithExcludedEmailsCheck
                 )
                 ;
               """
              .map(rs => ProspectBlacklistMatches(
                id = rs.long("id"),
                email = rs.string("email"),
                email_domain = rs.string("email_domain"),
                prospect_category_id_custom = rs.long("prospect_category_id_custom")

              ))
              .list
              .apply()

        }


      }

    }
   */

  ////////////////////////////////
  ////// Prospect List START
  ////////////////////////////////

  //Prateek
  def createProspectList(
                          name: String,
                          listOwnerAccountId: Long,
                          teamId: Long
                        ): Try[Option[Long]] = {

    prospectDAO.createProspectList(
      name = name,
      listOwnerAccountId = listOwnerAccountId,
      teamId = teamId
    )

  }

  // TEAMEDITION check: DONE
  //Prateek
  def findProspectListId(
                          name: String,
                          permittedAccountIds: Seq[Long],
                          teamId: Long,
                          listOwnerAccountId: Long
                        ): Try[Option[Long]] = {

    prospectDAO.findProspectListId(
      name = name,
      permittedAccountIds = permittedAccountIds,
      teamId = teamId,
      listOwnerAccountId = listOwnerAccountId
    )
  }


  // TEAMEDITION check: DONE
  // Prateek
  def findProspectLists(
                         accountIds: Seq[Long],
                         teamId: Long
                       ): Try[Seq[ProspectList]] = {
    prospectDAO.findProspectLists(
      accountIds = accountIds,
      teamId = teamId
    )
  }

  // TEAMEDITION check
  def findOrCreateList(
                        listName: Option[String],
                        listOwnerAccountId: Long,
                        permittedAccountIds: Seq[Long],
                        teamId: Long
                      ): Option[Long] = {

    if (listName.isDefined && listName.get.trim.nonEmpty) {

      val l = listName.get.trim

      val foundListId: Option[Long] = prospectDAO.findProspectListId(name = l, permittedAccountIds = permittedAccountIds, teamId = teamId, listOwnerAccountId = listOwnerAccountId).get

      if (foundListId.isDefined) foundListId
      else {
        prospectDAO.createProspectList(name = l, listOwnerAccountId = listOwnerAccountId, teamId = teamId).get
      }

    } else None
  }

  /*
   * not clear about the last_contacted at - does it mean
   * any prospect in this list - when they were last contacted?
   */
  //Sathish
  def listUpdateLastContactedAt(listId: Long): Try[Int] = {

    prospectDAO.listUpdateLastContactedAt(
      listId = listId
    )
  }


  ////////////////////////////////
  ////// Prospect List END
  ////////////////////////////////


  ////////////////////////////////
  ////// Prospect Stats START
  ////////////////////////////////


  /*
   * once we have made the correct decisions on : total_emails_opened , total_emails_sent
   * in prospects - then there is no impact here . these counts can just be pulled out
   */
  //Prateek
  def fetchOverallProspectListStatsForUpdatingStats(
                                                     list_id: Long,
                                                     teamId: Long
                                                   ): Option[FetchProspectListStats] = {
    prospectDAO.fetchOverallProspectListStatsForUpdatingStats(
      list_id = list_id,
      teamId = teamId
    )
  }

  /*
   * if we take decisions on summing up the counts when 2 prospects are unified then
   * we may have to update the prospect_lists summary counts as well.
   */

  // TEAMEDITION check: DONE
  //Sathish
  def getOverallProspectListStats(
                                   permittedAccountIds: Seq[Long],
                                   teamId: Long
                                 ): List[ProspectListStats] = {
    prospectDAO.getOverallProspectListStats(
      permittedAccountIds = permittedAccountIds,
      teamId = teamId
    )
  }

  /*
   * this update will be impacted by whatever decisions we take when
   * unifying counts related to prospects having multiple emails.
   */

  //Sathish
  def updateProspectListStatsOnly(
                                   list_id: Long,
                                   teamId: Long
                                 ): Try[Long] = Try {

    val stats = prospectDAO.fetchOverallProspectListStatsForUpdatingStats(
      list_id = list_id,
      teamId = teamId
    )

    if (stats.nonEmpty) {
      prospectDAO.updateProspectListStatsOnly(
        list_id = list_id,
        stats = stats
      )

    } else {

      logger.error(s"List id: ${list_id} => No Stats found")

    }

    list_id
  }


  // (listId, teamId)
  //Prateek
  def getActiveListIdsForUpdatingListStats: Try[Seq[(Long, Long)]] = prospectDAO.getActiveListIdsForUpdatingListStats


  ////////////////////////////////
  ////// Prospect Stats END
  ////////////////////////////////


  ////////////////////////////////
  ////// New user drip campaigns START
  ////////////////////////////////
  //Sathish
  def findProspectsToAssignDripCampaignStep2(welcome_campaign_id: Long): Try[Seq[Long]] = {
    prospectDAO.findProspectsToAssignDripCampaignStep2(
      welcome_campaign_id = welcome_campaign_id
    )
  }

  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  //use case : HourlyCronService
  //it fetch who not run a spam test 3 days after step 2
  //Sathish
  def findProspectsToAssignDripCampaignStep3(
                                              step2_campaign_id: Long
                                            ): Try[Seq[(Long, Boolean)]] = {

    prospectDAO.findProspectsToAssignDripCampaignStep3(
      step2_campaign_id = step2_campaign_id
    )

  }


  //Sathish
  def findProspectsToAssignDripCampaignStep4(step3_campaign_id: Long): Try[Seq[Long]] = {
    prospectDAO.findProspectsToAssignDripCampaignStep4(
      step3_campaign_id = step3_campaign_id
    )
  }


  //it fetch the accounts who's trial ends in 2 days
  //Sathish
  def findProspectsToAssignDripCampaignStep5(): Try[Seq[String]] = prospectDAO.findProspectsToAssignDripCampaignStep5()


  //it fetch the accounts who's trial ends 10 days back
  //Sathish
  def findProspectsToAssignDripCampaignStep6(): Try[Seq[String]] = prospectDAO.findProspectsToAssignDripCampaignStep6()


  //drip V2 start
  //Sathish
  def findProspectsFromStep1ToAssignDripCampaignStep2(
                                                       welcome_campaign_id: Long
                                                     ): Try[Seq[(Long, Boolean)]] = {
    prospectDAO.findProspectsFromStep1ToAssignDripCampaignStep2(
      welcome_campaign_id = welcome_campaign_id
    )
  }


  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  //Sathish
  def findProspectsFromStep2ToAssignDripCampaignStep3(
                                                       step2A_campaign_id: Long,
                                                       step2B_campaign_id: Long
                                                     ): Try[Seq[(Long, Boolean)]] = {
    prospectDAO.findProspectsFromStep2ToAssignDripCampaignStep3(
      step2A_campaign_id = step2A_campaign_id,
      step2B_campaign_id = step2B_campaign_id
    )
  }

  //Sathish
  def findProspectsFromStep3ToAssignDripCampaignStep4(): Try[Seq[(String, Boolean)]] = prospectDAO.findProspectsFromStep3ToAssignDripCampaignStep4()

  //Sathish
  def findProspectsFromStep4ToAssignDripCampaignStep5(): Try[Seq[(String, Boolean)]] = prospectDAO.findProspectsFromStep4ToAssignDripCampaignStep5()

  //Sathish
  def findProspectsFromStep5ToAssignDripCampaignStep6(): Try[Seq[(String, Boolean)]] = prospectDAO.findProspectsFromStep5ToAssignDripCampaignStep6()


  def getProspectCategoryId(
                             teamId: TeamId,
                             text_id: ProspectCategory.Value,
                             account: Option[Account]
                           )(using Logger: SRLogger): Try[ProspectCategoryId] = Try {

    val isNewCategory: Boolean = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
      teamId = teamId,
      feature = SrRollingUpdateFeature.NewReplySentiments
    )(Logger)

    val finalTextId = if (!isNewCategory) {
      text_id.toString
    } else {
      ProspectCategoryNew.getNewCategoryFromOldCategory(oldCategory = text_id).toString
    }

    val prospectCategoryId: Option[ProspectCategoryId] = if (account.isDefined && account.nonEmpty) {

      val categories = account.get.teams.find(t => t.team_id == teamId.id)

      if (categories.isEmpty) {
        Logger.error(s"FATAL Prospct.getProspectCategoryId empty categories:: team_id: ${teamId.id} :: text_id Value: ${finalTextId}  :: account: ${account} ")
        None
      } else {

        val cate = categories.get.prospect_categories_custom.find(c => c.text_id == finalTextId)

        if (cate.isEmpty) {
          Logger.error(s"FATAL Prospct.getProspectCategoryId empty cate:: team_id: ${teamId.id} :: text_id value: ${finalTextId}  :: account: ${account} ")
          None
        } else {
          Some(ProspectCategoryId(cate.get.id))
        }

      }

    } else {
      prospectDAO.getProspectCategoryId(
        teamId = teamId,
        text_id = finalTextId
      ).get
    }

    prospectCategoryId match {
      case None => throw new Throwable(s"ProspectCategoryId not found text_id::$finalTextId team_id::$teamId")
      case Some(prospectCategoryId: ProspectCategoryId) => prospectCategoryId
    }
  }

  def getCurrentProspectCategoryDetails(
                                         teamId: TeamId,
                                         prospectIds: Seq[ProspectId],
                                       ): Try[List[ProspectCategoryDetails]] = {

    prospectDAO.getCurrentProspectCategoryDetails(
      teamId = teamId,
      prospectIds = prospectIds
    )

  }

  //Sathish
  def findProspectCategoryById(
                                team_id: Long,
                                category_custom_id: Long
                              ): Try[Option[Long]] = {

    prospectDAO.findProspectCategoryById(
      team_id = team_id,
      category_custom_id = category_custom_id
    )

  }

  def getProspectCategoryDisplayName(
                                      team_id: Long,
                                      text_id: String,
                                      account: Option[Account]
                                    ): Option[String] = {
    prospectDAO.getProspectCategoryDisplayName(
      team_id = team_id,
      text_id = text_id,
      account = account
    )
  }

  def getProspectCategoryById(
                               team_id: Long,
                               category_id: Long,
                               account: Option[Account]
                             ): Option[ProspectCategoriesInDB] = {
    prospectDAO.getProspectCategoryById(
      team_id = team_id,
      category_id = category_id,
      account = account
    )
  }

  //Sathish
  def createOneCustomProspectCategory(
                                       accountId: Long,
                                       teamId: Long,
                                       accountName: String,
                                       categoryName: String,
                                       text_id: String,
                                       label_color: String): Try[Seq[Long]] = {

    prospectDAO.getHighestProspectCategoryRankInTeam(
      teamId = TeamId(id = teamId)
    ).flatMap { highestProspectCategoryRankInTeam =>

      prospectDAO.createOneCustomProspectCategory(
        accountId = accountId,
        teamId = teamId,
        accountName = accountName,
        categoryName = categoryName,
        text_id = text_id,
        label_color = label_color,
        newHighestProspectCategoryRankInTeam = ProspectCategoryRank(
          rank = highestProspectCategoryRankInTeam.rank + AppConfig.newProspectCategoryRankOffset
        )
      )

    }

  }

  def reorderProspectCategory(
                               teamId: TeamId,
                               reorderProspectCategoryData: ReorderProspectCategoryData,
                               prospectCategoryId: ProspectCategoryId,
                             ): Try[Either[String, List[ProspectCategoriesInDB]]] = {

    /**
     * To reorder the category, we need to know where to place it,
     * for that we use the to-be-previous and to-be-next category (rank).
     *
     * 1
     * 2
     * 3
     * 4
     *
     * If the user drags 2 below 3, then to-be-previous would be 3 and to-be-next would be 4.
     *
     * 1
     * 3 - prev
     * 2 - reordered category
     * 4 - next
     *
     * To maintain the ordering, previous category rank should be less than next category rank.
     *
     * To maintain unique rank, previous rank and next rank cannot be equal.
     */
    if (
      reorderProspectCategoryData.to_be_prev_prospect_category_rank.rank >=
        reorderProspectCategoryData.to_be_next_prospect_category_rank.rank
    ) {

      val errMsg = "Previous prospect category rank cannot be greater than or equal to next prospect category rank"

      logger.error(
        message = s"Failed to reorder prospect categories - $errMsg. teamId: $teamId :: prospectCategoryId: $prospectCategoryId :: reorderProspectCategoryData: $reorderProspectCategoryData"
      )

      Success(Left(errMsg))

    } else {

      prospectDAO.reorderProspectCategory(
        teamId = teamId,
        reorderProspectCategoryData = reorderProspectCategoryData,
        prospectCategoryId = prospectCategoryId,
      ).flatMap {

        case None =>

          val errMsg = "Not found prospect category"

          logger.error(
            message = s"$errMsg. teamId: $teamId :: prospectCategoryId: $prospectCategoryId :: reorderProspectCategoryData: $reorderProspectCategoryData"
          )

          Success(Left(errMsg))

        case Some(_) =>

          prospectDAO.getProspectCategories(
              teamId = teamId
            )
            .map(Right(_))

      }

    }

  }

  //Sathish
  def updateOneCustomProspectCategory(
                                       id: Long,
                                       accountId: Long,
                                       teamId: Long,
                                       accountName: String,
                                       categoryName: String,
                                       text_id: String,
                                       label_color: String
                                     ): Try[Option[Long]] = {
    prospectDAO.updateOneCustomProspectCategory(
      id = id,
      accountId = accountId,
      teamId = teamId,
      accountName = accountName,
      categoryName = categoryName,
      text_id = text_id,
      label_color = label_color
    )
  }


  //Sathish
  def findCustomProspectCategoryTaggedProspectsCount(
                                                      prospect_category_id_custom: Long,
                                                      team_id: Long
                                                    ): Try[Int] = {
    prospectDAO.findCustomProspectCategoryTaggedProspectsCount(
      prospect_category_id_custom = prospect_category_id_custom,
      team_id = team_id
    )
  }

  //Sathish
  def deleteCustomProspectCategory(
                                    id: Long,
                                    replacement_category_id: Long,
                                    permittedAccountIds: Seq[Long],
                                    teamId: Long
                                  ): Try[Int] = {

    prospectDAO.deleteCustomProspectCategory(
      id = id,
      replacement_category_id = replacement_category_id,
      permittedAccountIds = permittedAccountIds,
      teamId = teamId
    )

  }




  // FIXME: should we CALL_UPDATE_PROSPECT_TRIGGER ?
  /*
   * since this key prospect_account_id will be present in prospects_emails
   * this will have an impact there too.
   *
   * USE CASE: ProspectController.batchActions => update case prospect_account
   */
  //Sathish
  def updateBatchAccount(
                          prospects: Seq[ProspectBasicForBatchUpdate],
                          newProspectAccountId: Long
                        ): Try[Int] = Try {

    if (prospects.isEmpty) 0 else {

      val prospectsToBeUpdated = prospects.filter(p => {
        p.prospect_account_id.isEmpty ||
          p.prospect_account_id.get != newProspectAccountId
      })

      if (prospectsToBeUpdated.nonEmpty) {

        val prospectIds = prospectsToBeUpdated.map(p => p.id)

        val prospectsIdWithUpdatedAccount = prospectDAO.updateBatchAccount(
          prospectIds = prospectIds,
          newProspectAccountId = newProspectAccountId
        )
        prospectsIdWithUpdatedAccount.length

      } else {

        0

      }
    }

  }


  //Prateek
  def findAllForMQTriggerPublish(
                                  prospectIds: Seq[Long],
                                  teamId: Long
                                ): Try[List[ProspectForMQTriggerPublish]] = {

    prospectDAO.findAllForMQTriggerPublish(
      prospectIds = prospectIds,
      teamId = teamId
    )
  }


  //Prachi
  def getExistingProspects(
                            search_string: String,
                            team_id: Long
                          ): Try[List[ExistingProspect]] = {
    prospectDAO.getExistingProspects(
      search_string = search_string,
      team_id = team_id
    )
  }

  def getEmailDataForProspectIds(
                                  prospectIds: Seq[ProspectId],
                                  teamId: TeamId
                                ): Try[List[ProspectEmailData]] = Try {

    prospectIds
      .grouped(100)
      .flatMap(pList => {
        prospectDAO.getEmailDataForProspectIds(
          prospect_ids = pList,
          teamId = teamId
        ) match {
          case Failure(exception) => throw exception
          case Success(peList) => peList
        }
      }).toList
  }

  def getDataForAssignProspectsV3(
                                   id: Seq[Long],
                                   team_id: TeamId,
                                   campaign_id: CampaignId,
                                   org_id: OrgId
                                 )(using logger: SRLogger): Try[Seq[ProspectObject]] = {
    if (id.isEmpty) {
      Success(List())

    } else {

      findProspects(
        byProspectIds = id,
        teamId = team_id.id,
        org_id = org_id,
        byCampaignId = Some(campaign_id.id),
        findActiveCampaignsIfInternalRequest = true
      )
    }
  }


  private def getProspectObjectForFrontend(
                                            prospectObject: ProspectObject
                                          )(
                                            implicit logger: SRLogger

                                          ): Try[ProspectObject] = {

    replySentimentDAOService.getReplySentimentsForATeam(
      team_id = prospectObject.team_id,
      reply_sentiment_channel_type = ReplySentimentChannelType.AllChannelSentiments
    ).map { replySentimentForTeam =>

      val latestReplySentimentForTeam: Option[ReplySentimentForTeam] = prospectObject.latest_reply_sentiment_uuid.flatMap { reply_sentiment_uuid =>
        replySentimentForTeam.find(_.uuid == reply_sentiment_uuid)
      }

      prospectObject.copy(
        internal = prospectObject.internal.copy(
          latest_reply_sentiment = latestReplySentimentForTeam
        )
      )


    }
  }


  private def getProspectObjectForFrontend(
                                            prospectObjects: Seq[ProspectObject],
                                            team_id: Long
                                          )(
                                            using logger: SRLogger

                                          ): Try[Seq[ProspectObject]] = {

    replySentimentDAOService.getReplySentimentsForATeam(
      team_id = team_id,
      reply_sentiment_channel_type = ReplySentimentChannelType.AllChannelSentiments
    ).map { replySentimentForTeam =>

      prospectObjects.map { prospectObject =>

        val latestReplySentimentForTeam: Option[ReplySentimentForTeam] = prospectObject.latest_reply_sentiment_uuid.flatMap { reply_sentiment_uuid =>
          replySentimentForTeam.find(_.uuid == reply_sentiment_uuid)
        }

        prospectObject.copy(
          internal = prospectObject.internal.copy(
            latest_reply_sentiment = latestReplySentimentForTeam
          )
        )

      }


    }
  }


  def getProspectsDeduplicationColumnsFromDb(
                                              data: Seq[String],
                                              columnType: SrProspectColumns,
                                              teamId: TeamId
                                            ): Try[Map[String, Long]] = {

    if (data.isEmpty) {
      Success(Map[String, Long]())
    } else {
      val res = columnType match {
        case SrProspectColumns.Email => prospectDAO.selectProspectIdForEmail(
          prospect_emails = data.map(_.trim.toLowerCase()),
          teamId = teamId
        )
        case SrProspectColumns.Phone => prospectDAO.selectProspectIdForPhone(
          prospect_phones = data.map(_.trim.toLowerCase()),
          teamId = teamId
        )
        case SrProspectColumns.LinkedinUrl => prospectDAO.selectProspectIdForLinkedinUrl(
          prospect_linkedins = data.map(_.trim.toLowerCase()),
          teamId = teamId
        )
        case SrProspectColumns.CompanyFirstnameLastname => prospectDAO.selectProspectIdForCompanyFirstnameLastname(
          prospectCompanyFirstnameLastnames = data.map(_.trim.toLowerCase()),
          teamId = teamId
        )
      }

      res.map(prospects =>
        prospects.foldLeft(Map.empty[String, Long]) {
          (p1, p2) => p1 ++ p2
        }
      )
    }
  }

  def findDuplicateProspectsForForceUpdate(
                                            duplicationFindProspectData: Seq[DuplicationFindProspectData],
                                            teamId: TeamId
                                          )(using Logger: SRLogger): Try[List[DuplicateProspectResult]] = {
    if (duplicationFindProspectData.isEmpty) {
      Success(List())
    } else {
      prospectDAO.findDuplicateProspectsForForceUpdate(
        duplicationFindProspectData = duplicationFindProspectData,
        teamId = teamId
      )
    }
  }

  def findDuplicateProspectsForForceUpdateV2(
                                              duplicationFindProspectData: Seq[Seq[DuplicationFindProspectDataV2]],
                                              teamId: TeamId
                                            )(using Logger: SRLogger): Try[List[DuplicateProspectResult]] = {
    if (duplicationFindProspectData.isEmpty) {
      Success(List())
    } else {
      prospectDAO.findDuplicateProspectsForForceUpdateV2(
        duplicationFindProspectData = duplicationFindProspectData,
        teamId = teamId
      )
    }
  }
  def findDuplicateProspectsForUpdatedProspect(
                                                prospectDeduplicationDataWithId: ProspectDeduplicationDataWithId,
                                                teamId: TeamId
                                              ): Try[List[ProspectDeduplicationDataWithId]] = {
    prospectDAO.getDuplicateProspectsForGivenProspect(
      prospectDeduplicationDataWithId = prospectDeduplicationDataWithId,
      teamId = teamId
    )
  }

  def updateProspectBasicInfo(
                               data: ProspectBasicInfo,
                               masterProspectId: ProspectId,
                               permittedAccountIds: Seq[AccountId],
                               doerAccountId: AccountId,
                               teamId: TeamId
                             ): Try[Option[ProspectId]] = {
    prospectDAO.updateProspectBasicInfoForMasterProspect(
      data = data,
      masterProspectId = masterProspectId,
      permittedAccountIds = permittedAccountIds,
      doerAccountId = doerAccountId,
      teamId = teamId
    )
  }
}
