package api.prospects

import org.apache.pekko.actor.ActorSystem
import api.accounts.models.{AccountId, OrgId}
import api.accounts.service.AccountOrgBillingRelatedService
import api.accounts.{Account, AccountBillingData, AccountService, PermType, PermissionMethods, RepTrackingHosts, TeamId}
import api.{APIBadRequestException, APIManualEmailSentButSavingFailedException, APINotFoundException, AppConfig}
import api.campaigns.models.{CampaignStepData, CampaignStepRelatedDataForProspect, CampaignStepType, CurrentStepId, SendEmailFromCampaignDetails, StepDetails}
import api.campaigns.{AssignedCampaignForProspect, CPMarkAsCompleted, CampaignProspectDAO}
import api.campaigns.services.{CampaignId, CampaignService}
import api.emails.dao_service.EmailScheduledDAOService
import api.emails.dao_service.EmailThreadDAOService
import api.emails.models.EmailServiceProviderSendEmail.getEmailServiceProviderSendEmail
import api.emails.models.{DateWithin, EmailMessageContactModel, EmailReplyType, FolderId, InboxType, InboxTypeData, ManualEmailThreadData, ProspectEmailMessage, UpdateEmailScheduledFromExtensionData}
import api.emails.{AssociateEmailThreadProspect, ConversationsSearchResponse, DBEmailMessagesSavedResponse, EmailCommonService, EmailMessageTracked, EmailReplyTrackingModelV2, EmailScheduled, EmailScheduledDAO, EmailSetting, EmailSettingDAO, EmailThreadDAO, EmailToBeSent, EmailsScheduledUuid, MessageSentAtByProspect, SearchedEmailThreads, SendNewManualEmailV2, SendNewManualEmailV3, ThreadAndTeamId, ThreadForProspectInternal, models}
import api.linkedin_message_threads.LinkedinMessageThreadsDAO
import api.linkedin_messages.LinkedinMessagesDAO
import api.linkedin_messages.dao_service.LinkedinMessagesDAOService
import api.prospects.service.ProspectServiceV2
import api.team_inbox.dao.{EmailTeamInboxDAO, SettingIdsAndChannelType}
import play.api.libs.json.JodaReads.*
import eventframework.ConversationObject.ConversationCampaignAndProspect
import eventframework.{ConversationObjectInboxV3, MessageObject, ProspectObject}
import play.api.libs.json.{Json, Reads, Writes}
import play.api.libs.ws.WSClient
import api.prospects.models.{ProspectAccountsId, ProspectCategory, ProspectId, ProspectTouchedType, SendNewEmailManualData, StepId, ValidateSendNewEmailManuallyError}
import utils.{CampaignStepDataForEmail, Helpers, SRLogger}
import utils.email.services.InternalTrackingNote
import utils.email.{EmailReplyStatus, EmailSendDetail, EmailSendMailRequest, EmailService, EmailServiceBody, GenerateInboxDraft}
import utils.templating.TemplateService
import utils.dbutils.DBUtils

import scala.concurrent.{ExecutionContext, Future}
import api.prospects.InboxV3Service.{getAllCcEmails, max_page_limit_from_user, min_page_limit_from_user}
import api.prospects.dao.NewlyCreatedProspect
import api.prospects.dao_service.{InboxV3DAOService, ProspectDAOService}
import api.rep_tracking_hosts.service.RepTrackingHostService
import api.sr_audit_logs.models.{EventType, ProspectIdWithOldProspectDeduplicationColumn}
import api.team.service.TeamService
import api.team_inbox.dao_service.ReplySentimentDAOService
import api.team_inbox.model.{FolderType, ReplySentimentType}
import api.team_inbox.service.{ReplySentimentForTeam, ReplySentimentService, ReplySentimentUuid, TeamInboxService, ValidateInboxTypeAndGetEsetsError}
import org.joda.time.DateTime
import utils.jodatimeutils.JodaTimeUtils
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import utils.helpers.LogHelpers
import play.api.libs.json.JodaWrites.*
import play.api.libs.json.JodaReads.*

import scala.util.{Failure, Success}
import play.api.Logging
import sr_scheduler.models.ChannelType
import utils.Helpers.getCampaignStepDataForEmail
import utils.email.models.SendScheduleEmailType
import utils.featureflags.services.SrFeatureFlags
import utils.mq.email.MQEmailMessage
import utils.mq.webhook.mq_activity_trigger.{MQActivityTriggerMsgForm, MQActivityTriggerPublisher}
import utils.pagination.{GetDataErrorTrait, GetSameTimeDataEmailsAndLinkedIn, PaginationServiceTrait}
import utils.uuid.services.SrUuidService

import scala.util.Try


case class NavigationLinks(
                            prev: Option[DateTime],
                            next: Option[DateTime]
                          )

case object NavigationLinks {
  given reads: Reads[NavigationLinks] = Json.reads[NavigationLinks]
  given writes: Writes[NavigationLinks] = Json.writes[NavigationLinks]
}

case class ConvSummaryResponse(
                                convs: List[ConversationObjectInboxV3],
                                links: NavigationLinks,
                                show_upgrade_inbox_prompt: Boolean = false
                              )

sealed trait InboxV3ServiceGetNextConversationError

object InboxV3ServiceGetNextConversationError {
  case class DBFailure(exception: Throwable) extends InboxV3ServiceGetNextConversationError
}


sealed trait InboxV3ServiceGetConversationsError extends GetDataErrorTrait


object InboxV3ServiceGetConversationsError {
  case class DBFailure(exception: Throwable) extends InboxV3ServiceGetConversationsError

  case class AbsurdAtSameTimeConvsNotFound(error: String) extends InboxV3ServiceGetConversationsError

  case class GetIdFromUuidFailure(exception: Throwable) extends InboxV3ServiceGetConversationsError

  case class ShouldNotThrow(error: String) extends InboxV3ServiceGetConversationsError
}

sealed trait SendNewEmailManuallyError

object SendNewEmailManuallyError {
  case class ToEmailEmpty(error: String) extends SendNewEmailManuallyError

  case class ApiNotFound(error: String) extends SendNewEmailManuallyError

  case class ApiBadRequest(error: String) extends SendNewEmailManuallyError

  case class InvalidTemplateSyntax(error: String) extends SendNewEmailManuallyError

  case class InvalidMergeTag(error: String) extends SendNewEmailManuallyError

  case class APIManualEmailSentButSavingFailed(error: String) extends SendNewEmailManuallyError

  case class FatalWhileSending(exception: Throwable) extends SendNewEmailManuallyError

  case class ValidateInboxError(err: ValidateSendNewEmailManuallyError) extends SendNewEmailManuallyError

}

sealed trait GetDraftForTeamInboxError

object GetDraftForTeamInboxError {
  case class ErrorWhileFetchingDraft(exception: Throwable) extends GetDraftForTeamInboxError
}

sealed trait GetEmailsInConversationError

object GetEmailsInConversationError {
  case class ErrorWhileFetchingEmails(exception: Throwable) extends GetEmailsInConversationError
}

sealed trait ChangeArchiveOrSnoozeStatusOfConvError

object ChangeArchiveOrSnoozeStatusOfConvError {
  case class DBFailure(exception: Throwable) extends ChangeArchiveOrSnoozeStatusOfConvError

  case class UnarchiveOrUnsnoozeFailed(err: String) extends ChangeArchiveOrSnoozeStatusOfConvError
}

case class GetConversationIdsForSearchFailure(err: Throwable)

sealed trait ValidatedConvReq {

  def mailboxFolderRequest: MailboxFolder

}

object ValidatedConvReq {
  case class ValidatedMailboxReqRange(
                                       mailboxFolderRequest: MailboxFolder,
                                       timeline: InferredQueryTimeline.Range,
                                       pageSize: Int
                                     ) extends ValidatedConvReq

  case class ValidatedMailboxReqExact(
                                       mailboxFolderRequest: MailboxFolder,
                                       timeline: InferredQueryTimeline.Exact,
                                       pageSize: Int = 500
                                     ) extends ValidatedConvReq

  case class ValidatedSingleConvReq(
                                     mailboxFolderRequest: MailboxFolder,
                                     conv_id: Long
                                   ) extends ValidatedConvReq


}

case class ProspectAssociatedToThreads(
                                        email: String,
                                        scheduled_id: Long,
                                        email_thread_id: Long,
                                        prospect_id: Long
                                      )


object InboxV3Service {
  // if there are duplicate conversations at the page boundary
  // we try to give all of these to the customer. 500 is the
  // max limit we will fetch from the db
  val max_page_size_for_fetch_at_time = 500
  val max_page_limit_from_user = 100
  val min_page_limit_from_user = 3
  val default_page_size = 20


  /*
   23 Apr 2022: used to not show warmup emails in inbox
   issue related: https://app.intercom.com/a/apps/xmya8oga/inbox/inbox/search/conversations/84281100180365?q=valeri%40hypernovamarketing.com&teamAssigneeIdentifier=all&teammateAssigneeIdentifier=all
   */
  val warmupToolsSubjectKeywords = Seq(
    "GWarm",
    "wrmpbx",
    "lemwarm"
  )

  def getAllCcEmails(
                      sender_email: EmailSetting,
                      cc_emails: Option[Seq[IEmailAddress]],
                      Logger: SRLogger
                    ): Seq[IEmailAddress] = {

    val fromInput: Seq[IEmailAddress] = cc_emails.getOrElse(Seq())
    val fromEmailSetting: Seq[IEmailAddress] = if (sender_email.cc_emails.isEmpty) Seq() else {

      IEmailAddress.parse(
        emailsStr = sender_email.cc_emails.get)(
        logger = Logger
      ) match {
        case Failure(e) =>

          Logger.fatal(s"failure (IGNORING) while parsing ccEmails: ${sender_email.cc_emails}", err = e)
          Seq()

        case Success(ccEmails) =>
          ccEmails
      }
    }

    val processed_cc_emails: Seq[IEmailAddress] = if (fromInput.isEmpty) fromEmailSetting
    else if (fromEmailSetting.isEmpty) fromInput
    else fromInput ++ fromEmailSetting

    processed_cc_emails
  }

  def getAllBccEmails(
                       senderEmail: EmailSetting,
                       input_bcc_emails: Option[Seq[IEmailAddress]],
                       Logger: SRLogger
                     ): Seq[IEmailAddress] = {

    val fromInput: Seq[IEmailAddress] = input_bcc_emails.getOrElse(Seq())

    val fromEmailSetting: Seq[IEmailAddress] = if (senderEmail.bcc_emails.isEmpty) Seq() else {

      IEmailAddress.parse(
        emailsStr = senderEmail.bcc_emails.get)(
        logger = Logger
      ) match {
        case Failure(e) =>

          Logger.fatal(s"failure (IGNORING) while parsing bccEmails: ${senderEmail.bcc_emails}", err = e)
          Seq()

        case Success(bccEmails) =>
          bccEmails
      }
    }

    val bcc_emails: Seq[IEmailAddress] = if (fromInput.isEmpty) fromEmailSetting
    else if (fromEmailSetting.isEmpty) fromInput
    else fromInput ++ fromEmailSetting

    bcc_emails
  }

  def getEmailSendDetail(
                          senderEmail: EmailSetting,
                          replyToEmail: EmailSetting,
                          org_id: Long,
                          teamId: Long,
                          accountId: Long,
                          sendEmailFromCampaignDetails: Option[SendEmailFromCampaignDetails],
                          emailContent: EmailServiceBody,
                          data: SendNewManualEmailV3,
                          allCC: Seq[IEmailAddress],
                          allBCC: Seq[IEmailAddress],
                          replyingToMessage: Option[EmailScheduled],
                          emailThread: Option[ThreadForProspectInternal.EmailThreadForProspectInternal]

                        ) = {

    EmailSendDetail(

      sender_email_settings_id = senderEmail.id.get.emailSettingId, // FIXME VALUECLASS
      receiving_email_settings_id = replyToEmail.id.get.emailSettingId, // FIXME VALUECLASS
      sender_message_id_suffix = senderEmail.message_id_suffix,

      id = 0,
      org_id = org_id,
      prospect_id = None, // only used to generate smtp message-ids: we anyways are randomizing that with the current timestamp
      team_id = teamId,
      account_id = accountId,
      sendEmailFromCampaignDetails = sendEmailFromCampaignDetails,

      subject = emailContent.subject,
      body = emailContent.htmlBody,
      text_body = emailContent.textBody,

      scheduled_from_campaign = false,

      service_provider = senderEmail.service_provider,
      via_gmail_smtp = senderEmail.via_gmail_smtp,

      smtp_username = senderEmail.smtp_username,
      smtp_host = senderEmail.smtp_host,
      smtp_port = senderEmail.smtp_port,
      smtp_password = senderEmail.smtp_password,
      oauth2_refresh_token = senderEmail.oauth2_refresh_token,
      oauth2_access_token = senderEmail.oauth2_access_token,
      oauth2_access_token_expires_at = senderEmail.oauth2_access_token_expires_at,
      custom_tracking_domain = senderEmail.custom_tracking_domain,
      rep_tracking_host_id = senderEmail.rep_tracking_host_id,
      gmail_fbl = None,
      list_unsubscribe_header = None,

      // for mailgun
      email_domain = senderEmail.email_domain,
      api_key = senderEmail.api_key,
      mailgun_region = senderEmail.mailgun_region,


      from_email = senderEmail.email,
      from_name = senderEmail.sender_name,

      // replies should come to the same inbox from which admin manually sends this message
      reply_to_email = None,
      reply_to_name = None,

      to_emails = data.to,

      cc_emails = allCC,

      bcc_emails = allBCC,

      in_reply_to_id = replyingToMessage.flatMap(_.message_id),
      in_reply_to_references_header = replyingToMessage.flatMap(_.references_header),
      in_reply_to_sent_at = replyingToMessage.flatMap(_.sent_at),
      in_reply_to_subject = replyingToMessage.flatMap(_.subject),
      in_reply_to_outlook_msg_id = replyingToMessage.flatMap(_.outlook_msg_id),

      email_thread_id = replyingToMessage.flatMap(_.email_thread_id),
      send_plain_text_email = Some(false), // SEND_PLAIN_TEXT_EMAIL_FIXME: this is correct we need to send false here i checked the frontend for both app and extension and we dont have anything related to campaign there
      gmail_thread_id = emailThread.flatMap(_.gmail_thread_id),

      sender_email_setting_paused_till = None
    )

  }

  def getEmailMessageTracked(
                              senderEmail: EmailSetting,
                              sentEmail: EmailToBeSent,
                              toEmails: Seq[IEmailAddress],
                              emailContent: EmailServiceBody,
                              campaign_step_data: CampaignStepDataForEmail,
                              dateTimeNow: DateTime,
                              ccEmailAddrs: Seq[IEmailAddress],
                              teamId: Long,
                              accountId: Long,

                              prospectId:Option[Long]

                            )(using Logger: SRLogger): EmailMessageTracked = {

    EmailMessageTracked(
      inbox_email_setting_id = senderEmail.id.get.emailSettingId, // FIXME VALUECLASS
      from = IEmailAddress(
        email = senderEmail.email,
        name = Some(sentEmail.from_name)
      ),

      to_emails = toEmails,
      subject = sentEmail.subject,

      body = sentEmail.htmlBody,

      base_body = emailContent.baseBody,
      text_body = sentEmail.textBody,

      prospect_id_in_campaign = prospectId,
      campaign_id = campaign_step_data.campaign_id,
      campaign_name = campaign_step_data.campaign_name,
      step_id = campaign_step_data.step_id,
      step_name = campaign_step_data.step_name,

      references_header = sentEmail.references_header,

      prospect_account_id_in_campaign = None,
      received_at = dateTimeNow,
      recorded_at = dateTimeNow,

      email_status = EmailReplyStatus.getEmailReplyStatusBeforeSaving(
        emailBaseBody = sentEmail.htmlBody,
        subject = sentEmail.subject,
        fromEmail = senderEmail.email,
        fromName = sentEmail.from_name,
        toEmailAddresses = toEmails.map(_.email),
        byAccount = true,
        fullHeaders = Json.obj(), // doesnt matter
        received_at = dateTimeNow
      ).copy(

        /*
            Note: 28 Apr 2025
            This function is called in the service sendNewEmailManually.
            We are marking this as contacted by default. Later it should automatically get updated by reply tracker
            if delivery failed/bounced.
         */
        replyType = EmailReplyType.CONTACTED
      ),

      message_id = if (sentEmail.message_id.isDefined) sentEmail.message_id.get else "",
      full_headers = Json.obj(),

      scheduled_manually = true,
      sr_inbox_read = true,

      reply_to = sentEmail.reply_to_email.map(replyToAddress => {
        IEmailAddress(
          email = replyToAddress,
          name = sentEmail.reply_to_name
        )
      }),

      email_thread_id = sentEmail.email_thread_id,
      gmail_msg_id = sentEmail.gmail_msg_id,
      gmail_thread_id = sentEmail.gmail_thread_id,

      outlook_msg_id = sentEmail.outlook_msg_id,
      outlook_conversation_id = sentEmail.outlook_conversation_id,
      outlook_response_json = sentEmail.outlook_response_json,

      cc_emails = ccEmailAddrs,
      in_reply_to_header = sentEmail.in_reply_to_id,
      original_inbox_folder = None,

      internal_tracking_note = InternalTrackingNote.NONE,

      tempThreadId = Some(0),

      team_id = teamId,
      account_id = accountId
    )

  }

}

sealed trait MailboxFolder {
  def replySentimentType: Option[ReplySentimentType]
}

object MailboxFolder {

  sealed trait TeamInboxFolder extends MailboxFolder

  object TeamInboxFolder {
    case class Prospects(
                          esetIds: Seq[Long],
                          prospect_category: Option[Long],
                          replySentimentType: Option[ReplySentimentType]
                        ) extends TeamInboxFolder

    case class NonProspects(
                             esetIds: Seq[Long],
                             replySentimentType: Option[ReplySentimentType]
                           ) extends TeamInboxFolder

    case class SnoozedFolder(
                              esetIds: Seq[Long],
                              replySentimentType: Option[ReplySentimentType]
                            ) extends TeamInboxFolder

    case class DoneFolder(
                           esetIds: Seq[Long],
                           prospect_category: Option[Long],
                           replySentimentType: Option[ReplySentimentType]
                         ) extends TeamInboxFolder

    case class SentFolder(
                           esetIds: Seq[Long],
                           replySentimentType: Option[ReplySentimentType]
                         ) extends TeamInboxFolder

    case class IrrelevantFolder(
                                 esetIds: Seq[Long],
                                 replySentimentType: Option[ReplySentimentType]
                               ) extends TeamInboxFolder
  }

  sealed trait CampaignInboxFolder extends MailboxFolder {
    def prospectId: Option[ProspectId]  //for prospect_id filter on campaign inbox
    def prospectAccountsId: Option[ProspectAccountsId]
  }

  object CampaignInboxFolder {
    case class Prospects(
                          campaign_id: Option[CampaignId],
                          prospectId: Option[ProspectId],
                          prospectAccountsId: Option[ProspectAccountsId],
                          esetIds: Seq[Long],
                          prospect_category: Option[Long],
                          replySentimentType: Option[ReplySentimentType]
                        ) extends CampaignInboxFolder

    case class SnoozedFolder(
                              campaign_id: Option[CampaignId],
                              prospectId: Option[ProspectId],
                              prospectAccountsId: Option[ProspectAccountsId],
                              esetIds: Seq[Long],
                              replySentimentType: Option[ReplySentimentType]
                            ) extends CampaignInboxFolder

    case class DoneFolder(
                           campaign_id: Option[CampaignId],
                           prospectId: Option[ProspectId],
                           prospectAccountsId: Option[ProspectAccountsId],
                           esetIds: Seq[Long],
                           prospect_category: Option[Long],
                           replySentimentType: Option[ReplySentimentType]
                         ) extends CampaignInboxFolder

    case class SentFolder(
                           campaign_id: Option[CampaignId],
                           prospectId: Option[ProspectId],
                           prospectAccountsId: Option[ProspectAccountsId],
                           esetIds: Seq[Long],
                           replySentimentType: Option[ReplySentimentType]
                         ) extends CampaignInboxFolder

    case class IrrelevantFolder(
                                 campaign_id: Option[CampaignId],
                                 esetIds: Seq[Long],
                                 prospectId: Option[ProspectId],
                                 prospectAccountsId: Option[ProspectAccountsId],
                                 replySentimentType: Option[ReplySentimentType]
                               ) extends CampaignInboxFolder
  }

  /*
    20-may-2024:
      This function is designed only for `InboxController.getConversationV3`
      Please don't use anywhere else, otherwise inside data may be incorrect for you.

   */
  def getMailBoxFolderTypeBasedOnInboxType(inbox_type: InboxType): MailboxFolder = {

    val mail_box_folder_type = inbox_type match {

      case InboxType.SINGLE | InboxType.CONSOLIDATED =>

        /*
          inside values doesn't matter as we only need TeamInboxFolder or CampaignInboxFolder types

         */
        MailboxFolder.TeamInboxFolder.Prospects(esetIds = List(), prospect_category = None, replySentimentType = None)

      case InboxType.AllCampaigns =>

        MailboxFolder.CampaignInboxFolder.Prospects(campaign_id = None, prospectId = None, prospectAccountsId = None, esetIds = List(), prospect_category = None, replySentimentType = None)


    }

    mail_box_folder_type

  }
}


sealed trait FindConversationByIdError

object FindConversationByIdError {
  case class DatabaseError(exception: Throwable) extends FindConversationByIdError

  case class PermissionDeniedError(permissionDeniedError: String) extends FindConversationByIdError
}

// TODO add error codes for handleGetCampaignForConversationInboxV3
sealed trait FindConversationCampaignError

object FindConversationCampaignError {
  case class DatabaseError(exception: Throwable) extends FindConversationCampaignError

  case class CampaignNotFoundError(errMesg: String) extends FindConversationCampaignError

  case class EmailThreadNotPartOfACampaign(errMesg: String) extends FindConversationCampaignError
}


sealed trait IncomingQueryTimeLine

object IncomingQueryTimeLine {
  case class NewerThan(datetime: Long) extends IncomingQueryTimeLine

  case class OlderThan(datetime: Long) extends IncomingQueryTimeLine

  case object None extends IncomingQueryTimeLine
}


sealed trait InferredQueryTimeline

object InferredQueryTimeline {
  sealed trait Range extends InferredQueryTimeline

  object Range {
    case class Before(dateTime: DateTime) extends Range

    case class After(dateTime: DateTime) extends Range
  }

  //15 July: We were not able to match the exact date timezone in db for the fetching exactly at emails
  //So we decided to take the thread_id/ task_id of the same time emails/tasks
  //and select their updated_at for thread_id/due_at for task_id and compare in query itself
  case class Exact(exactIdToCompareTime: ExactIdToCompareTime) extends InferredQueryTimeline
}

case class ExactIdToCompareTime(
                                 id: String
                               )


class InboxV3Service(
                      emailThreadDAOService: EmailThreadDAOService,
                      campaignService: CampaignService,
                      emailSettingDAO: EmailSettingDAO,
                      prospectServiceV2: ProspectServiceV2,
                      //                      accountService: AccountService,
                      emailScheduledDAO: EmailScheduledDAO,
                      emailScheduledDAOService: EmailScheduledDAOService,
                      linkedinMessagesDAOService: LinkedinMessagesDAOService,
                      emailReplyTrackingModelV2: EmailReplyTrackingModelV2,
                      templateService: TemplateService,
                      emailService: EmailService,
                      srUuidService: SrUuidService,
                      linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO,
                      prospectDAOService: ProspectDAOService,
                      prospectAccountDAO: ProspectAccountDAO1,
                      //                      teamInboxDAO: TeamInboxDAO,
                      dbUtils: DBUtils,
                      emailMessageContactModel: EmailMessageContactModel,
                      replySentimentDAOService: ReplySentimentDAOService,
                      campaignProspectDAO: CampaignProspectDAO,
                      teamService: TeamService,
                      inboxV3DAOService: InboxV3DAOService,
                      repTrackingHostService: RepTrackingHostService,
                      teamInboxService: TeamInboxService,
                      mqActivityTriggerPublisher: MQActivityTriggerPublisher,
                      accountOrgBillingRelatedService: AccountOrgBillingRelatedService,
                      emailCommonService: EmailCommonService
                    ) extends Logging {


  def getTeamInboxFolderTypeAndCategory(
                                         folderId: FolderId,
                                         esets: List[Long],
                                         replySentimentTypeOpt: Option[ReplySentimentType],
                                         prospect_category: Option[Long]
                                       ): Either[String, MailboxFolder.TeamInboxFolder] = {

    (folderId, prospect_category) match {

      case (FolderId.PROSPECTS, None) =>
        Right(MailboxFolder.TeamInboxFolder.Prospects(esetIds = esets, prospect_category = None, replySentimentType = replySentimentTypeOpt))

      case (FolderId.PROSPECTS, Some(category)) =>
        Right(MailboxFolder.TeamInboxFolder.Prospects(esetIds = esets, prospect_category = Some(category), replySentimentType = replySentimentTypeOpt))

      case (FolderId.NON_PROSPECTS, None) =>
        Right(MailboxFolder.TeamInboxFolder.NonProspects(esetIds = esets, replySentimentType = replySentimentTypeOpt))

      case (FolderId.DONE, None) =>
        Right(MailboxFolder.TeamInboxFolder.DoneFolder(esetIds = esets, prospect_category = None, replySentimentType = replySentimentTypeOpt))

      case (FolderId.DONE, Some(category)) =>
        Right(MailboxFolder.TeamInboxFolder.DoneFolder(esetIds = esets, prospect_category = Some(category), replySentimentType = replySentimentTypeOpt))

      case (FolderId.SNOOZED, None) =>
        Right(MailboxFolder.TeamInboxFolder.SnoozedFolder(esetIds = esets, replySentimentType = replySentimentTypeOpt))

      case (FolderId.SENT, None) =>
        Right(MailboxFolder.TeamInboxFolder.SentFolder(esetIds = esets, replySentimentType = replySentimentTypeOpt))
      case (FolderId.IRRELEVANT, None) =>
        Right(MailboxFolder.TeamInboxFolder.IrrelevantFolder(esetIds = esets, replySentimentType = replySentimentTypeOpt))

      case (FolderId.NON_PROSPECTS, Some(_)) |
           (FolderId.SNOOZED, Some(_)) |
           (FolderId.SENT, Some(_)) |
           (FolderId.IRRELEVANT, Some(_)) =>
        Left("Prospect category should not be passed to this folder.")
    }

  }

  def validateGetConversationsRequest(
                                       esets: List[Long],
                                       folder_id: Option[String],
                                       prospect_category: Option[Long],
                                       older_than: Option[Long],
                                       newer_than: Option[Long],
                                       page_size: Option[Int],
                                       rs_id: Option[String],
                                       inbox_type_data: InboxTypeData
                                     )(using Logger: SRLogger): Either[String, ValidatedConvReq.ValidatedMailboxReqRange] = {

    val replySentimentOptionTry = rs_id match {
      case None => Success(None)
      case Some(key) => ReplySentimentType.getReplySentimentTypeFromCode(key)
    }

    val folder_validation: Either[String, MailboxFolder] = replySentimentOptionTry match {
      case Failure(err) =>
        Logger.fatal(s"Error while getting Reply sentiment type from reply sentiment code", err)
        Left("Invalid Reply Sentiment")
      case Success(replySentimentTypeOpt) => folder_id match {
        case None => Left("Folder cannot be empty for inbox.")

        case Some(folder) =>
          FolderId.withName(folder) match {

            case Failure(_) => Left("Invalid folder name for the inbox.")

            case Success(folderId) =>

              inbox_type_data match {

                case _: InboxTypeData.SINGLE_DATA | _: InboxTypeData.CONSOLIDATED_DATA =>

                  getTeamInboxFolderTypeAndCategory(
                    folderId = folderId,
                    esets = esets,
                    replySentimentTypeOpt = replySentimentTypeOpt,
                    prospect_category = prospect_category
                  )


                case data: InboxTypeData.ALLCAMPAIGNS_DATA =>

                  (folderId, prospect_category) match {

                    case (FolderId.PROSPECTS, None) =>
                      Right(MailboxFolder.CampaignInboxFolder.Prospects(
                        campaign_id = data.campaign_id,
                        prospectId = data.prospect_id,
                        prospectAccountsId = data.prospect_account_id,
                        esetIds = esets,
                        prospect_category = None,
                        replySentimentType = replySentimentTypeOpt
                      ))

                    case (FolderId.PROSPECTS, Some(category)) =>
                      Right(MailboxFolder.CampaignInboxFolder.Prospects(
                        campaign_id = data.campaign_id,
                        prospectId = data.prospect_id,
                        prospectAccountsId = data.prospect_account_id,
                        esetIds = esets,
                        prospect_category = Some(category),
                        replySentimentType = replySentimentTypeOpt
                      ))

                    case (FolderId.DONE, None) =>
                      Right(MailboxFolder.CampaignInboxFolder.DoneFolder(
                        campaign_id = data.campaign_id,
                        prospectId = data.prospect_id,
                        prospectAccountsId = data.prospect_account_id,
                        esetIds = esets,
                        prospect_category = None,
                        replySentimentType = replySentimentTypeOpt
                      ))

                    case (FolderId.DONE, Some(category)) =>
                      Right(MailboxFolder.CampaignInboxFolder.DoneFolder(
                        campaign_id = data.campaign_id,
                        prospectId = data.prospect_id,
                        prospectAccountsId = data.prospect_account_id,
                        esetIds = esets,
                        prospect_category = Some(category),
                        replySentimentType = replySentimentTypeOpt
                      ))

                    case (FolderId.SNOOZED, None) =>
                      Right(MailboxFolder.CampaignInboxFolder.SnoozedFolder(
                        campaign_id = data.campaign_id,
                        prospectId = data.prospect_id,
                        prospectAccountsId = data.prospect_account_id,
                        esetIds = esets,
                        replySentimentType = replySentimentTypeOpt
                      ))

                    case (FolderId.SENT, None) =>
                      Right(MailboxFolder.CampaignInboxFolder.SentFolder(
                        campaign_id = data.campaign_id,
                        prospectId = data.prospect_id,
                        prospectAccountsId = data.prospect_account_id,
                        esetIds = esets,
                        replySentimentType = replySentimentTypeOpt
                      ))

                    case (FolderId.IRRELEVANT, None) =>
                      Right(MailboxFolder.CampaignInboxFolder.IrrelevantFolder(
                        campaign_id = data.campaign_id,
                        prospectId = data.prospect_id,
                        prospectAccountsId = data.prospect_account_id,
                        esetIds = esets,
                        replySentimentType = replySentimentTypeOpt
                      ))

                    case (FolderId.NON_PROSPECTS, _) |
                         (FolderId.IRRELEVANT, Some(_)) |
                         (FolderId.SNOOZED, Some(_)) |
                         (FolderId.SENT, Some(_)) =>

                      Left(s"Folder :: ${folderId.textId} not allowed to be viewed from campaigns inbox")

                  }


              }
          }
      }
    }


    val timeLineValidation =
      if (older_than.isDefined && newer_than.isDefined) {
        Left("Cannot specify older_than and newer than together ")
      } else if (older_than.isDefined) {
        Right(InferredQueryTimeline.Range.Before(new DateTime(older_than.get)))
      } else if (newer_than.isDefined) {
        Right(InferredQueryTimeline.Range.After(new DateTime(newer_than.get)))
      } else {
        Right(InferredQueryTimeline.Range.Before(DateTime.now()))
      }

    val pageSize = {
      if (page_size.isDefined) {
        val pg_sz = page_size.get

        if (pg_sz > max_page_limit_from_user) {
          Left(s"Page limit should not exceed ${max_page_limit_from_user}")
        } else if (pg_sz < min_page_limit_from_user) {
          Left(s"Page limit should not be less than ${min_page_limit_from_user}")
        } else {
          Right(pg_sz)
        }
      } else {
        Right(20)
      }
    }

    val res = for {
      validated_folder <- folder_validation
      validated_timeline <- timeLineValidation
      validated_page_size <- pageSize
    } yield ValidatedConvReq.ValidatedMailboxReqRange(
      mailboxFolderRequest = validated_folder,
      timeline = validated_timeline,
      pageSize = validated_page_size)

    res
  }


  def handleGetCampaignAndProspectForConversationInboxV3(
                                                          conversation_id: Long,
                                                          campaign_id: Option[Long],
                                                          team_id: Long,
                                                          Logger: SRLogger
                                                        ): Either[FindConversationCampaignError, ConversationCampaignAndProspect] = {

    campaign_id match {
      case Some(cmp_id) =>
        val campaign_prospects = campaignService.getCampaignAndProspectByConversationId(
          team_id = team_id,
          conversation_id = conversation_id,
          campaign_id = cmp_id,
          Logger = Logger
        )

        campaign_prospects match {
          case Failure(exception) =>
            Left(FindConversationCampaignError.DatabaseError(exception = exception))

          case Success(conversation_campaign) =>
            conversation_campaign match {
              case None =>
                Left(FindConversationCampaignError.CampaignNotFoundError(
                  "Campaign details not found: Prospect might have been unassigned from campaign"
                ))

              case Some(result) => Right(result)
            }
        }

      case None =>
        Left(FindConversationCampaignError.EmailThreadNotPartOfACampaign(s"This Conversation is not part of any campaign"))
    }

  }

  def getMessageThreadDAOByChannelType(channelType: ChannelType) = {
    channelType match {
      case ChannelType.EmailChannel => Success(emailThreadDAOService)
      case ChannelType.LinkedinChannel => Success(linkedinMessageThreadsDAO)
      case _ => Failure(new Exception(s"Team Inbox is not supported for ${channelType.toString}"))
    }
  }

  def findConversationByIdNew(
                               teamId: Long,
                               channelType: ChannelType,
                               //conv_id: Long,
                               validatedConvReq: ValidatedConvReq.ValidatedSingleConvReq
                             )(using Logger: SRLogger): Either[FindConversationByIdError, ConversationObjectInboxV3] = {

    val threadDAOTry: Try[MessageThreadDAOTrait] = getMessageThreadDAOByChannelType(channelType)

    threadDAOTry.flatMap(threadDAO => {
      val qry = threadDAO.getConversationQueryV3(teamId = teamId,
        validatedConvReq = validatedConvReq,
        replySentimentUuid = List() //list is empty because we are looking for a specific thread, rather than filtering
      )

      threadDAO.getSingleConversation(qry = qry, teamId = teamId, mailboxFolderRequest = validatedConvReq.mailboxFolderRequest)
    }) match {
      case Failure(exception) =>
        println(s"\n\n\nError from DB: ${exception.getMessage}\n\n\n")
        Left(FindConversationByIdError.DatabaseError(exception = exception))
      case Success(None) =>
        Left(FindConversationByIdError.PermissionDeniedError("You don't have access to this conversation"))
      case Success(Some(conv)) =>
        Right(conv)
    }

  }

  /*
   * Decision making for the API
   * 1. We will retrieve all the records < the given timestamp
   *    (not <= as it currently is) this is important as i will document a case
   *    which can otherwise occur.
   * 2. We will check for duplicate entries at the end of the result set.
   *    If there are 2 records with the same timestamp, apart from the records
   *    we have selected , we will also select all the records with that duplicate
   *    time stamp. This may exceed the page size we are publishing in the API.
   *    Hence for this case we will have to document that.
   *    WE will fetch the emails with the same timestamp upto a limit of 500.
   *    There is an unlikely scenario that this could exceed even 500
   *    If that happens - we will also add some metadata to the response
   *    that our response is probably invalid and you should contact support
   *    and get this resolved.
   *
   *    Hence let's say we have a page size of 20 and if we hit the above edge case
   *    we can return a maximum of 520 records.
   *
   *    Furthermore - in non-error scenarios it is never possible for duplicate timestamp
   *    records to cross an call API boundary. Hence the condition of the timestamp for the next
   *    page can be a strict < and not a <= . If we had a <= - we could keep producing the same
   *    data again and again without paginating forward if we had more than 520 records
   *    with the same timestamp

   */


  def getSearchConversationIdQueryAndResult(
                                             teamId: Long,
                                             searchKey: String,
                                             esets: Seq[Long],
                                             lsets: Seq[Long],
                                             inbox_type: InboxType,
                                             date_within: Option[DateTime],
                                             prospectId: Option[ProspectId],
                                             prospect_account_id: Option[ProspectAccountsId],
                                             are_all_esets_accessible: Boolean,
                                             sent_at: Option[Long],
                                             last_sent_id: Option[String],
                                             folder_type: Option[FolderType]
                                           )(using logger: SRLogger): Try[List[ConversationsSearchResponse]] = {
    val emailQuery = if (prospectId.isEmpty && prospect_account_id.isEmpty) {
      emailThreadDAOService.getQueryForSearchInboxEmailThreads(
        teamId = teamId,
        searchKey = searchKey,
        settingsIds = esets,
        inbox_type = inbox_type,
        date_within = date_within,
        are_all_esets_accessible = are_all_esets_accessible,
        sent_at = sent_at,
        last_sent_id = last_sent_id,
        folder_type = folder_type
      )
    } else {
      emailThreadDAOService.getQueryForSearchInboxEmailThreadsForProspect(
        teamId = teamId,
        settingsIds = esets,
        prospectId = prospectId,
        prospect_account_id = prospect_account_id,
        are_all_esets_accessible = are_all_esets_accessible,
        sent_at = sent_at,
        last_sent_id = last_sent_id,
        inbox_type = inbox_type
      )
    }

    val linkedinQuery = linkedinMessageThreadsDAO.getQueryForSearchInboxEmailThreads(
      teamId = teamId,
      searchKey = searchKey,
      settingsIds = lsets,
      inbox_type = inbox_type,
      date_within = date_within,
      are_all_esets_accessible = are_all_esets_accessible,
      sent_at = sent_at,
      last_sent_id = last_sent_id,
      folder_type = folder_type
    )

    val emailConvsTry = if (esets.nonEmpty) {
      emailThreadDAOService.getConversationsForSearch(query = emailQuery, inbox_type = inbox_type
      )
    } else if(prospectId.isDefined && prospect_account_id.isDefined){
      logger.shouldNeverHappen("Cannot pass both prospect and prospect account id")
      Failure(new Throwable("Cannot pass both prospect and prospect account id"))
    } else {
      Success(List())
    }

    val linkedinConvsTry = if (lsets.nonEmpty) {
      linkedinMessageThreadsDAO.getConversationsForSearch(query = linkedinQuery, inbox_type = inbox_type)
    }
    else {
      Success(List())
    }

    emailConvsTry
      .flatMap(emailConvs => {
        linkedinConvsTry
          .map(linkedinConvs => {
            emailConvs ++ linkedinConvs
          })
      })
  }

  def getConversationIdsForSearch(
                                   team_id: Long,
                                   search_key: Option[String],
                                   esets: Seq[Long],
                                   lsets: Seq[Long],
                                   inbox_type: InboxType,
                                   duration: Option[String],
                                   prospect_id: Option[ProspectId],
                                   prospect_account_id: Option[ProspectAccountsId],
                                   are_all_esets_accessible: Boolean,
                                   sent_at: Option[Long],
                                   last_sent_id: Option[String],
                                   folder_type: Option[String]
                                 )(using logger: SRLogger): Either[GetConversationIdsForSearchFailure, List[ConversationsSearchResponse]] = {

    search_key match {

      case Some(search) if search.length >= 3 =>

        val searchResult: Try[List[ConversationsSearchResponse]] = for {
          date_within: Option[DateTime] <- Try {
            duration match {
              case None => None
              case Some(d) =>
                val today = DateTime.now()
                DateWithin.withName(d) match {
                  case Failure(_) => None
                  case Success(search_duration) =>
                    search_duration match {
                      case DateWithin.ONE_WEEK => Some(today.minusDays(7))
                      case DateWithin.ONE_MONTH => Some(today.minusMonths(1))
                      case DateWithin.SIX_MONTHS => Some(today.minusMonths(6))
                      case DateWithin.ONE_YEAR => Some(today.minusYears(1))
                    }
                }
            }
          }

          folder_type: Option[FolderType] <- Try {
            if (folder_type.isDefined) {
              FolderType.withName(folder_type.get) match {
                case Failure(e) =>
                  logger.error("Error while parsing inbox folder_type", e)
                  throw e
                case Success(name) => Some(name)
              }
            } else {
              None
            }

          }

          searchedIds: List[ConversationsSearchResponse] <- getSearchConversationIdQueryAndResult(
            teamId = team_id,
            searchKey = search,
            esets = esets,
            lsets = lsets,
            inbox_type = inbox_type,
            date_within = date_within,
            prospectId = prospect_id,
            prospect_account_id = prospect_account_id,
            are_all_esets_accessible = are_all_esets_accessible,
            sent_at = sent_at,
            last_sent_id = last_sent_id,
            folder_type = folder_type
          )

        } yield {
          searchedIds
        }

        searchResult match {
          case Failure(exception) =>
            logger.error(s"INBOX_ALERT: Error while searching email for tid ${team_id}", exception)
            Left(GetConversationIdsForSearchFailure(exception))

          case Success(convThreads) =>
            Right(convThreads)
        }

      case _ =>

        getSearchConversationIdQueryAndResult(
          teamId = team_id,
          searchKey = "",
          esets = esets,
          lsets = lsets,
          inbox_type = inbox_type,
          date_within = None,
          prospectId = prospect_id,
          prospect_account_id = prospect_account_id,
          are_all_esets_accessible = are_all_esets_accessible,
          sent_at = sent_at,
          last_sent_id = last_sent_id,
          folder_type = None
        ) match {
          case Failure(exception) =>
            logger.error(s"INBOX_ALERT: Error while searching email for pid ${prospect_id} paccid ${prospect_account_id} and tid ${team_id}", exception)
            Left(GetConversationIdsForSearchFailure(exception))

          case Success(convThreads) =>
            Right(convThreads)
        }
    }

  }

  def validateSendNewEmailManual(
                                  version: String,
                                  loggedInAccountId: Account,
                                  tiid: Option[String],
                                  accountId: Long,
                                  teamId: Long,
                                  orgId: Long,
                                  campaign_step_id: Option[Long],
                                  permittedAccountIds: Seq[Long],
                                  to_emails: Seq[IEmailAddress]

                                )(implicit ec: ExecutionContext, Logger: SRLogger): Future[Either[ValidateSendNewEmailManuallyError, SendNewEmailManualData]] = {

    val toEmails = to_emails.filter(_.email.trim.nonEmpty)
    if (toEmails.isEmpty) {
      Future.successful(
        Left(ValidateSendNewEmailManuallyError.ToEmailEmpty("Please provide valid To email address"))
      )
    } else {

      val toEmailAddresses = toEmails.map(_.email.trim.toLowerCase).distinct

      /**
       * fixme newinbox InboxV3: this check is just to verify how many users are sending to non-prospect emails
       * should be removed with InboxV3
       */
      val toProspectIdsCheckF: Future[Seq[ProspectIdEmail]] = Future.fromTry(
        prospectServiceV2.findByEmail(
          logger = Logger,
          emails = toEmailAddresses,
          teamId = teamId
        )
      ).map { pids =>

        if (pids.length != toEmails.length) {
          val existingProspectEmails = pids.map(_.email.toLowerCase.trim)
          val nonProspectToEmails = toEmailAddresses.filterNot(em => existingProspectEmails.contains(em.trim.toLowerCase))
          Logger.info(s"inboxv3 check: manual reply: toEmails are not prospects already: nonProspectToEmails: $nonProspectToEmails :: existingProspectEmails: $existingProspectEmails")
        }

        pids
      }

      (campaign_step_id, tiid) match {

        // Manual email without campaign

        case (None, None) =>

          Future.successful(Right(
            SendNewEmailManualData.ExtensionFlowWithoutCampaign(
              to_emails = toEmails
            )
          ))

        case (Some(cs_id), Some(t_iid)) =>

          Future.successful(Left(ValidateSendNewEmailManuallyError.CampaignStepIdAndTIIDBothDefinedError))

        case (Some(cs_id), None) =>

          for {

            campaign_step_data: CampaignStepRelatedDataForProspect <- campaignProspectDAO.findCampaignStepDataForProspect(
              current_step_id = CurrentStepId(step_id = cs_id),
              team_id = TeamId(id = teamId)
            ) match {

              case Failure(e) =>

                Future.failed(e)

              case Success(None) =>

                Logger.error(s"Error while sending manual email, campaign details not found ${cs_id} teamid: ${teamId}")
                Future.failed(new Exception(s"Campaign details not found while sending email [${cs_id}]"))

              case Success(Some(campaign_step_data)) =>

                Future.successful(campaign_step_data)
            }

            toProspects <- toProspectIdsCheckF

            email_scheduled_id: Long <- emailScheduledDAO.getEmailScheduledIdForManualEmailSending(
              prospect_ids = toProspects.map(prospecData => ProspectId(prospecData.id)),
              team_id = TeamId(id = teamId),
              campaign_id = campaign_step_data.campaign_id,
              step_id = StepId(id = cs_id)
            ) match {

              case Failure(err) =>

                Logger.error(s"Error while fetching email_scheduled id for while sending manual email step_id: ${cs_id} teamid: ${teamId} campaign_id: ${campaign_step_data.campaign_id}")
                Future.failed(err)

              case Success(None) =>

                Logger.error(s"Error while fetching email_scheduled id for while sending manual email step_id: ${cs_id} teamid: ${teamId} campaign_id: ${campaign_step_data.campaign_id}")
                Future.failed(new Exception(s"EmailScheduledId not found while sending email [${cs_id}"))

              case Success(Some(es_id)) =>

                Future.successful(es_id)
            }

          } yield {

            Right(SendNewEmailManualData.ExtensionFlowData(
              campaign_step_id = CurrentStepId(cs_id),
              campaign_step_related_data = campaign_step_data,
              to_emails = toEmails,
              email_scheduled_id = email_scheduled_id,
              to_prospects = toProspects.map(p => ProspectId(p.id))
            ))
          }


        case (None, Some(t_iid)) =>

          //allow_for_consolidated_inbox is false from inbox flow
          val inbox_type_data = InboxTypeData.getInboxTypeData(
            show_campaign_inbox = loggedInAccountId.org.org_metadata.show_campaign_inbox.getOrElse(true),
            team_inbox_id = t_iid, // long or string
            org_id = OrgId(id = orgId),
            prospect_id = None,
            prospect_account_id = None,
            campaign_id = None
          )

          inbox_type_data match {

            case Failure(err) =>
              Logger.error(s"validateSendNewEmailManual :: team_id: ${teamId}  :: team_inbox_id :${t_iid}  org_id: ${orgId}", err)
              Future.successful(Left(ValidateSendNewEmailManuallyError.InvalidInboxIdError(err.getMessage)))


            case Success(inbox_type_data) =>

              teamInboxService.validateInboxTypeAndGetSettingIds(
                inbox_type_data = inbox_type_data,
                team_id = teamId,
                version = version,
                loggedInAccount = loggedInAccountId,
                permittedAccountIds = permittedAccountIds,
                Logger = Logger
              ) match {
                case Left(e) =>

                  Future.successful(Left(ValidateSendNewEmailManuallyError.ValidateInboxTypeError(e)))

                case Right(validatedInboxAndSettingData) =>

                  Future.successful(Right(SendNewEmailManualData.InboxFlowData(
                    settingIdsAndChannelType = validatedInboxAndSettingData.settingsAndChannelType,
                    to_emails = toEmails
                  )))

              }
          }
      }
    }
  }

  //TODO: duplicate code in controller for old inbox
  def sendNewEmailManually(
                            data: SendNewManualEmailV3,
                            org_id: Long,
                            accountId: Long,
                            teamId: Long,
                            loggedinAccount: Account,
                            auditRequestLogId: String,
                            permittedAccountIds: Seq[Long],
                            version: String,
                            tiid: Option[String]
                          )(
                            using ec: ExecutionContext,
                            Logger: SRLogger,
                            wSClient: WSClient,
                          ): Future[Either[SendNewEmailManuallyError, String]] = {

    validateSendNewEmailManual(
      tiid = tiid,
      accountId = accountId,
      teamId = teamId,
      orgId = org_id,
      campaign_step_id = data.campaign_step_id,
      to_emails = data.to,
      permittedAccountIds = permittedAccountIds,
      version = version,
      loggedInAccountId = loggedinAccount
    ).flatMap {
        case Left(e) =>

          Future.successful(Left(SendNewEmailManuallyError.ValidateInboxError(e)))

        case Right(validated_data) =>

          /**
           * check if user has permission to send from the "sender_email_setting_id" email-account
           */
          val senderEmailSettingF = Future.fromTry {
            emailService.getSenderEmailSettingForManualEmailTask(
              sender_email_setting_id = EmailSettingId(data.sender_email_setting_id),
              permittedAccountIdsForEmailSetting = permittedAccountIds,
              team_id = TeamId(teamId)
            )
          }
          /**
           * check if user has permission to use the receive-replies-to inbox
           *
           * FIXME: currently SEND_MANUAL_EMAIL permission is being checked for this also
           */
          val replyToEmailSettingF = if (
            data.receiver_email_setting_id.isEmpty ||
              data.receiver_email_setting_id.get == data.sender_email_setting_id
          ) {
            senderEmailSettingF
          } else Future {
            emailSettingDAO.find(id = EmailSettingId(data.receiver_email_setting_id.get), teamId = TeamId(teamId)) match {
              case None => throw new APINotFoundException(s"Reply-to email account not found [${data.receiver_email_setting_id.get}]")
              case Some(em) =>
                if (!permittedAccountIds.contains(em.owner_id.id)) { // FIXME VALUECLASS

                  throw new APIBadRequestException("You do not have the permission for the selected reply-to email")

                } else {
                  em
                }
            }
          }

          /**
           * check if email thread belongs to the team
           *
           * FIXME: I think we should check whether user has permission to send from the inbox associated with this thread
           */
          val emailThreadF: Future[Option[ThreadForProspectInternal.EmailThreadForProspectInternal]] = {

            val email_thread_id: Future[Option[String]] = validated_data match {

              case _: SendNewEmailManualData.ExtensionFlowWithoutCampaign =>
                Future.successful(None)

              case extensionFlowData: SendNewEmailManualData.ExtensionFlowData =>


                senderEmailSettingF.flatMap(em => {

                  emailThreadDAOService.getEmailThreadUuidWhenSendingManualEmail(
                    campaign_id = extensionFlowData.campaign_step_related_data.campaign_id,
                    prospect_ids = extensionFlowData.to_prospects.map(_.id),
                    team_id = TeamId(id = teamId),
                    email_settings = em
                  ) match {

                    case Failure(e) => {

                      logger.error(s"getEmailThreadUuidWhenSendingManualEmail : Error ", e)

                      Future.failed(e)
                    }

                    case Success(threadData) =>

                      Future.successful {
                        val matchingThreads: Seq[ManualEmailThreadData] = threadData.filter(td => {
                          Helpers.subjectsMatching(sub1 = data.subject, sub2 = td.subject)
                        })

                        if (matchingThreads.isEmpty) {
                          None
                        } else {
                          Some(matchingThreads.head.uuid.uuid)
                        }
                      }
                  }

                })


              case _: SendNewEmailManualData.InboxFlowData =>

                Future.successful(data.email_thread_id)

            }

            email_thread_id
              .map(id => {

                id.flatMap(threadUuid => {

                  srUuidService.getThreadIdFromUuid(
                    uuid = threadUuid,
                    teamId = TeamId(teamId)
                  ) match {
                    case Failure(e) =>
                      Logger.error(s"Error while fetching emailThread with uuid: $threadUuid", e)
                      None

                    case Success(ThreadId.EmailThreadId(id)) =>
                      emailThreadDAOService.findByIdInternalV3(
                        id = id,
                        teamId = TeamId(teamId),
                        inboxEmailSettingId = Some(data.sender_email_setting_id)
                      ) match {
                        case None =>

                          /**
                           * DO NOT throw here
                           *
                           * its a valid case, where the user may change the From Email while
                           * forwarding / replying emails
                           *
                           * we will just send such emails in a new thread, rather than throwing the
                           * error that Thread was not found
                           */
                          // throw new APINotFoundException(s"Email conversation not found [$threadId]")
                          None

                        case Some(em) =>
                          if (em.team_id != teamId) {
                            throw new APINotFoundException(s"Email conversation not found [$id.2]")
                          } else {
                            Some(em)
                          }
                      }

                    case Success(ThreadId.LinkedinThreadId(id)) =>
                      throw new APINotFoundException(s"Sending Linkedin Messages is not allowed: $threadUuid")
                  }

                })
              })
          }

          val allTrackingDomainsUsedF = Future.fromTry {
            repTrackingHostService.getRepTrackingHosts()
          }.map(_.map(_.host_url))

          /**
           * if user has selected, the "Mark as done & Send" option, then archive the thread
           */
          val archiveThreadF: Future[Int] = emailThreadF.map(thread => {
            if (
              thread.isDefined &&
                data.mark_as_done.getOrElse(false)
            ) {
              val archivedThreads: Try[Int] = changeArchiveStatusOfConvInboxV3(
                threadIds = Seq(thread.get.id),
                channelType = ChannelType.EmailChannel,
                archive = true,
                teamId = TeamId(id = teamId)
              ) match {
                case Left(_) => Failure(new Exception("changeArchiveStatusOfConvInboxV3 failed"))
                case Right(result) => Success(result)
              }

              archivedThreads.get
            } else {
              0
            }
          })


          val sendEmailStepByStep = for {

            // wasn't used anywhere
            //        toProspectIdsCheckDone : Seq[ProspectIdEmail] <- toProspectIdsCheckF

            senderEmail: EmailSetting <- senderEmailSettingF

            replyToEmail: EmailSetting <- replyToEmailSettingF

            emailThread: Option[ThreadForProspectInternal.EmailThreadForProspectInternal] <- emailThreadF

            campaign_step_data: CampaignStepDataForEmail <- Future.successful {

              validated_data match {

                case _: SendNewEmailManualData.InboxFlowData =>

                  getCampaignStepDataForEmail(
                    emailThread = emailThread,
                    campaign_step_details = None
                  )

                case extension_flow: SendNewEmailManualData.ExtensionFlowData =>

                  getCampaignStepDataForEmail(
                    emailThread = emailThread,
                    campaign_step_details = Some(extension_flow.campaign_step_related_data)
                  )

                case extension_flow_without_campaign: SendNewEmailManualData.ExtensionFlowWithoutCampaign =>

                  getCampaignStepDataForEmail(
                    emailThread = None,
                    campaign_step_details = None
                  )

              }
            }

            manualEmailFromSRInbox: Boolean <- Future.successful {

              validated_data match {
                case data: SendNewEmailManualData.ExtensionFlowWithoutCampaign => true
                case data: SendNewEmailManualData.ExtensionFlowData => false
                case data: SendNewEmailManualData.InboxFlowData => true
              }

            }


            replyingToMessage: Option[EmailScheduled] <- Future {
              emailThread.map(thread => {
                emailScheduledDAO.findEmailToReplyToV3(emailThreadId = thread.id, teamId = TeamId(id = teamId)) match {
                  case Failure(e) => throw e
                  case Success(None) => throw new APINotFoundException(s"Email conversation message not found [${thread.id}]")
                  case Success(Some(msg)) => msg
                }
              })
            }

            allTrackingDomainsUsed: Seq[String] <- allTrackingDomainsUsedF

            emailContent: EmailServiceBody <- Future.fromTry(
              emailService.getBodiesForInboxMessage(

                // fixme newinbox oldinbox also: open/click tracking arent going to work with emailschduledid as 0
                emailsScheduledUuid = EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),

                custom_tracking_domain = senderEmail.custom_tracking_domain,
                default_tracking_domain = senderEmail.default_tracking_domain,

                body = data.body,
                subject = data.subject,

                trackOpens = data.enable_open_tracking.getOrElse(false),
                trackClicks = data.enable_click_tracking.getOrElse(false),

                allTrackingDomainsUsed = allTrackingDomainsUsed,
                teamId = TeamId(id = teamId),
                orgId = OrgId(org_id)
              )
            )


            sentEmail: EmailToBeSent <- {

              val allCC: Seq[IEmailAddress] = {

                InboxV3Service.getAllCcEmails(
                  sender_email = senderEmail,
                  cc_emails = data.cc_emails,
                  Logger = Logger
                )

              }

              val allBCC: Seq[IEmailAddress] = {

                InboxV3Service.getAllBccEmails(
                  senderEmail = senderEmail,
                  input_bcc_emails = data.bcc_emails,
                  Logger = Logger
                )


              }

              val sendEmailFromCampaignDetails: Option[SendEmailFromCampaignDetails] = {

                validated_data match {

                  case _: SendNewEmailManualData.InboxFlowData =>

                    emailThread.flatMap(_.campaign_id).map(
                      campaignId => {
                        SendEmailFromCampaignDetails(
                          campaign_id = campaignId,
                          campaign_name = replyingToMessage.map(_.sendEmailFromCampaignDetails.get.campaign_name).get,
                          stepDetails = None
                        )
                      }
                    )

                  case extension_flow_data: SendNewEmailManualData.ExtensionFlowData =>

                    Some(SendEmailFromCampaignDetails(
                      campaign_id = extension_flow_data.campaign_step_related_data.campaign_id.id,
                      campaign_name = extension_flow_data.campaign_step_related_data.campaign_name.name,
                      stepDetails = Some(StepDetails(
                        step_id = extension_flow_data.campaign_step_related_data.current_step_id.step_id,
                        step_name = extension_flow_data.campaign_step_related_data.step_label.label,
                        step_type = CampaignStepType.ManualEmailStep
                      ))
                    ))


                  case _: SendNewEmailManualData.ExtensionFlowWithoutCampaign =>

                    // Since campaign is not present so no such data exists
                    None

                }
              }

              val email_send_detail = InboxV3Service.getEmailSendDetail(

                senderEmail = senderEmail,
                replyToEmail = replyToEmail,
                org_id = org_id,
                teamId = teamId,
                accountId = accountId,
                sendEmailFromCampaignDetails = sendEmailFromCampaignDetails,
                emailContent = emailContent,
                data = data,
                allCC = allCC,
                allBCC = allBCC,
                replyingToMessage = replyingToMessage,
                emailThread = emailThread

              )

              //used EmailService.sendEmailViaSpecificHost instead of the block below

              // val sendingUrl = senderEmail.rep_mail_server_host + "/api/v2/inbox/p_s_m_1"

              // for testing: val sendingUrl = "http://localhost:9000/api/v2/inbox/p_s_m_1"
              //
              //          wSClient.url(sendingUrl)
              //            .post(Json.toJson(sendData))
              //            .map(res => {
              //
              //              if (res.status == 200) {
              //
              //                val sentResponseData = (res.json \ "data")
              //
              //                sentResponseData.validate[EmailToBeSent] match {
              //                  case JsError(errors) =>
              //                    Logger.fatal(s"FATAL APIManualEmailSentButSavingFailedException external api response failure: errors: $errors : resBody: ${res.body}")
              //                    throw new APIManualEmailSentButSavingFailedException(s"Email successfully sent but failed while saving to your account")
              //
              //
              //                  case JsSuccess(sentEmailData, _) =>
              //                    sentEmailData
              //                }
              //
              //              } else {
              //
              //                Logger.fatal(s"FATAL:: send_manual_email 123 :: sendingUrl: $sendingUrl :: resbody: ${res.body} :: sendData: $sendData")
              //
              //                throw new APIBadRequestException(
              //                  "Sending failed. Your email account is not connected properly. Please reconnect, or contact support"
              //                )
              //
              //              }
              //
              //            })
              //            .recover { case e => {
              //
              //              Logger.fatal(s"FATAL:: send_manual_email 2:: sendingUrl: $sendingUrl :: sendData: $sendData", err = e)
              //
              //              throw e
              //            }
              //            }

              emailService.sendEmailViaSpecificHost(
                sendData = EmailSendMailRequest(
                  email_send_detail = email_send_detail,
                  log_request_id = Logger.logRequestId,
                  rep_mail_server_reverse_dns = senderEmail.rep_mail_server_reverse_dns
                ),
                repMailServerHost = senderEmail.rep_mail_server_host
              )(wSClient, Logger, ec)
            }
            markProspectAsCompleted: Boolean <- Future.successful {
              validated_data match {
                case data: SendNewEmailManualData.ExtensionFlowWithoutCampaign => true
                case data: SendNewEmailManualData.ExtensionFlowData => false
                case data: SendNewEmailManualData.InboxFlowData => true
              }
            }

            savedEmail: DBEmailMessagesSavedResponse <- Future {

              val toEmails = sentEmail.to_emails

              val ccEmailAddrs = sentEmail.cc_emails
              val dateTimeNow = DateTime.now


              /*
               * FIXME: We should ideally push event for all prospects to which email was sent , currently only considering toEmailProspect
               *  As later we are joining on prospect id with email scheduled , in the email scheduled we only store
               *  one prospect id
               */
              val prospectData = if(toEmails.nonEmpty) {
                prospectServiceV2.findByIdOrEmail(orProspectIds = Seq(),orProspectEmails = Seq(toEmails.head.email), teamId = teamId, logger= Logger) match {
                  case Failure(exception) => {
                    Logger.error(s"sendNewEmailManually Error occurred while search prospect with email : ${toEmails.head.email}", err = exception)
                    None
                  }
                  case Success(prospects) => {
                    if(prospects.nonEmpty) Some(prospects.head) else None
                  }
                }
              }else{
                None
              }

              val email = InboxV3Service.getEmailMessageTracked(
                senderEmail = senderEmail,
                sentEmail = sentEmail,
                toEmails = toEmails,
                emailContent = emailContent,
                campaign_step_data = campaign_step_data,
                dateTimeNow = dateTimeNow,
                ccEmailAddrs = ccEmailAddrs,
                teamId = teamId,
                accountId = accountId,
                prospectId =  prospectData.map(_.prospect_id)
              )

              // PROSPECTS_EMAILS_TODO_READ_CLEANED
              emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
                accountId = accountId,
                team_id = teamId,
                emailMessages = Seq(email),
                replyHandling = senderEmail.reply_handling,

                inboxEmailSetting = senderEmail,
                senderEmails = Seq(senderEmail.email),

                adminReplyFromSRInbox = manualEmailFromSRInbox,
                account = loggedinAccount,
                auditRequestLogId = auditRequestLogId,
                markProspectAsCompleted = markProspectAsCompleted

              ) match {

                case Failure(e) =>

                  Logger.fatal(s"APIManualEmailSentButSavingFailedException: Email successfully sent but failed while saving to your account 2 : ${LogHelpers.getStackTraceAsString(e)}")
                  throw new APIManualEmailSentButSavingFailedException(s"Email successfully sent but failed while saving to your account", e)

                case Success(savedReplies) =>

                  val emailScheduledIds = savedReplies.savedMessages.map(_.email_scheduled_id)
                  if (prospectData.nonEmpty) {
                    // If propsect Id is present then only pushing to mq activity
                    val msg = MQActivityTriggerMsgForm(accountId = accountId,
                      teamId = teamId,
                      campaignId = None,
                      prospectIds = Seq(ProspectIdWithOldProspectDeduplicationColumn(
                        prospectId = ProspectId(prospectData.get.prospect_id),
                        oldProspectDeduplicationColumn = None
                      )),
                      clickedUrl = None,
                      campaignName = None,
                      emailScheduledIds = if (emailScheduledIds.isEmpty) None else Some(emailScheduledIds),
                      event = EventType.EMAIL_SENT.toString,
                      threadId = emailThread.map(_.id),
                      replySentimentUuid = None
                    )


                    prospectData match {
                      case Some(pData) => {

                        accountOrgBillingRelatedService.accountBillingDateAndCurrentSentAccount(
                          accountId = AccountId(id = loggedinAccount.internal_id) // FIXME VALUECLASS
                        ) match {
                          case Failure(e) =>
                            Logger.fatal(s"Error while fetching billing account detals for  accountId:${loggedinAccount.internal_id}", e)

                          case Success(accountBillingData: AccountBillingData) => {
                            accountOrgBillingRelatedService.checkAndUpdateProspectsContacted(
                              prospectId = ProspectId(pData.prospect_id),
                              teamId = TeamId(teamId),
                              prospectTouchedType = ProspectTouchedType.TaskDone,
                              channelType = ChannelType.EmailChannel,
                              updateLastContactedAt = true,
                              logger = Logger
                            ) match {
                              case Failure(e) => Logger.fatal(s"Error while incrementing prospects contacted count prospectId:${pData.prospect_id}", e)
                              case Success(_) =>
                            }
                            if (senderEmail.id.nonEmpty) {
                              emailCommonService.incrementSentProspectCountIfNeeded(
                                prospectInCampaign = Some(pData),
                                currentBillingCycleStartedAt = accountBillingData.currentBillingCycleStartedAt,
                                emailSettingId = senderEmail.id.get.emailSettingId,
                              )
                            }
                          }
                        }
                      }
                      case None => {
                        if(toEmails.nonEmpty) {
                          Logger.fatal(s"No Prospect Found for prospect with email :${toEmails.head.email}")
                        } 

                      }
                    }
                    mqActivityTriggerPublisher.publishEvents(message = msg)
                  }
                  savedReplies
              }
            }
            

            archived: Int <- archiveThreadF

          } yield {

            savedEmail

          }

          sendEmailStepByStep
            .map { sentAndSavedEmail =>

              // FIXME newinbox webhooks should be called: sent / completed etc.

              Right("Email sent")

            }
            .recover {

              case e: APINotFoundException =>
                Left(SendNewEmailManuallyError.ApiNotFound(Option(e.getMessage).getOrElse("")))

              case e: APIBadRequestException =>
                Left(SendNewEmailManuallyError.ApiBadRequest(Option(e.getMessage).getOrElse("")))

              case e: templateService.InvalidTemplateSyntaxException =>
                Left(SendNewEmailManuallyError.InvalidTemplateSyntax(Option(e.getMessage).getOrElse("")))

              case e: templateService.InvalidMergeTagException =>
                Left(SendNewEmailManuallyError.InvalidMergeTag(Option(e.getMessage).getOrElse("")))

              case e: APIManualEmailSentButSavingFailedException =>
                Left(SendNewEmailManuallyError.APIManualEmailSentButSavingFailed(Option(e.getMessage).getOrElse("")))

              case e =>
                Logger.fatal("FATAL while sending manual email", err = e)
                Left(SendNewEmailManuallyError.FatalWhileSending(e))

            }


      }
      .recover(e => {
        Logger.fatal("FATAL while sending manual email", err = e)
        Left(SendNewEmailManuallyError.FatalWhileSending(e))
      })
  }

  def getDraftForTeamInbox(
                            data: GenerateInboxDraft,
                            teamId: Long,
                            Logger: SRLogger
                          )(
                            implicit ec: ExecutionContext,
                            logger: SRLogger
                          ): Future[Either[GetDraftForTeamInboxError, (String, String)]] = {

    val senderEmailSettingF = Future {
      emailSettingDAO.find(id = EmailSettingId(data.sender_email_setting_id), teamId = TeamId(teamId)) match {

        case None => throw new APINotFoundException(s"Sender email account not found [${data.sender_email_setting_id}]")

        case Some(em) => em
      }
    }

    val prospectF = if (data.to_email.isEmpty || data.to_email.get.trim.isEmpty) {

      Future.successful(ProspectObject.dummyProspectObj)

    } else {

      Future {
        // PROSPECTS_EMAILS_TODO_READ_CLEANED
        prospectDAOService.find(
          byProspectEmails = Seq(data.to_email.get.trim),
          teamId = teamId,
          Logger = Logger
        ) match {
          case Failure(e) => throw e
          case Success(prospectSeq) =>

            val p = prospectSeq.headOption

            if (p.isEmpty) {

              ProspectObject.dummyProspectObj
                .copy(
                  email = Some(data.to_email.get.trim) //TODO: EMAIL_OPTIONAL check the path and test code (dummyProspectObj is having Some("") for email)
                )
            } else {

              //Note Aug 2022: For new Team_Inbox as we are allowing all with whom team_inbox is shared
              // permittedAccountIds(PermType.EDIT_PROSPECTS).contains(p.get.owner_id) this check is removed

              p.get
            }

        }
      }
    }

    val sendEmailStepByStep = for {

      senderEmail <- senderEmailSettingF

      prospect <- prospectF

      prospectAccount <- if (prospect.id == 0) {
        // dummy prospect obj
        Future.successful(None)
      } else {
        Future {
          prospectAccountDAO.find(id = prospect.id, teamId = prospect.team_id)
        }
      }

      emailContent <- Future.fromTry(
        emailService.generateDraftForInbox(

          signature = Some(senderEmail.signature),

          bodyTemplate = data.body_template,
          subjectTemplate = data.subject_template,

          email_sender_name = senderEmail.sender_name,
          sender_first_name = senderEmail.first_name,
          sender_last_name = senderEmail.last_name,
          prospect = prospect,
          prospectAccount = prospectAccount,
          applying_email_template = data.applying_email_template
        )
      )

    } yield {

      emailContent

    }

    sendEmailStepByStep
      .map { case (body, subject) =>
        Right(body, subject)
      }
      .recover { case e => Left(GetDraftForTeamInboxError.ErrorWhileFetchingDraft(e))
      }
  }

  def getEmailsInConversation(
                               thread: ThreadForProspectInternal,
                               email_thread_id: Long,
                               isSupportAppModeRequest: Boolean,
                               account_id: Long,
                               teamId: Long
                             )(
                               implicit ec: ExecutionContext,
                               logger: SRLogger
                             ): Future[Either[GetEmailsInConversationError, Seq[MessageObject]]] = {
    val repTrackingHostsFut = Future.fromTry {
      repTrackingHostService.getRepTrackingHosts()
    }
    val emailMessagesFuture: Future[Seq[MessageObject]] = repTrackingHostsFut
      .map(repTrackingHosts => {
        findThreadMessages(
          threadId = thread.id,
          allTrackingDomains = repTrackingHosts.map(_.host_url),
          channelType = thread.channelType,
          teamId = TeamId(teamId)
        ).get
      })

    val markedAsRead2: Future[Int] = Future {

      val isProspectOwner = thread.owner_id == account_id

      if (isProspectOwner && !isSupportAppModeRequest) {
        emailScheduledDAO.markAsReadInSRInboxV2(emailThreadId = email_thread_id, teamId = TeamId(teamId)).get
      } else {
        0
      }
    }

    val dbResults = for {
      emailMessages <- emailMessagesFuture
      _ <- markedAsRead2
    } yield {

      emailMessages
    }

    dbResults
      .map(emailMessages => Right(emailMessages))
      .recover { case e => Left(GetEmailsInConversationError.ErrorWhileFetchingEmails(e)) }
  }


  /**
   * This function get called while unsnooze and unarchive
   * It first finds out whether there is an entry in email_threads_prospects table for given thread_id
   * if yes then it will go to prospects folder else to non-prospects
   */
  private def folderToProspectsOrNonProspects(
                                               threadIds: Seq[Long],
                                               teamId: TeamId,
                                               is_automatic_unsnooze: Boolean = false
                                             ): Try[Int] = {

    val dbAndSession = dbUtils.startLocalTx()

    val db = dbAndSession.db
    implicit val session = dbAndSession.session

    val result: Try[Int] = for {
      //1. find whether the threads belongs to prospects/non-prospects (find if thread_id exists in email_threads_prospects)
      prospectThreads: List[Long] <- {
        emailThreadDAOService.getThreadIdsExistingInEmailThreadsProspects(
          thread_ids = threadIds,
          teamId = teamId
        )
      }

      //2. get non-prospect related threads
      nonProspectThreads: Seq[Long] <- Try {
        threadIds.filterNot(t => prospectThreads.contains(t))
      }

      //3. update folder
      updatedFolderProspects: Int <- {
        if (prospectThreads.nonEmpty) {
          emailThreadDAOService.updateInboxFolderType(
            threadIds = prospectThreads,
            teamId = teamId,
            folder_type = FolderType.PROSPECTS,
            is_automatic_unsnooze = is_automatic_unsnooze
          )
        } else {
          Success(0)
        }
      }

      updatedFolderNonProspects: Int <- {
        if (nonProspectThreads.nonEmpty) {
          emailThreadDAOService.updateInboxFolderType(
            threadIds = nonProspectThreads,
            teamId = teamId,
            folder_type = FolderType.NON_PROSPECTS,
            is_automatic_unsnooze = is_automatic_unsnooze
          )
        } else {
          Success(0)
        }
      }
    } yield {
      updatedFolderProspects + updatedFolderNonProspects
    }

    dbUtils.commitAndCloseSession(db = db)

    result

  }

  def changeArchiveStatusOfConvInboxV3(
                                        threadIds: Seq[Long],
                                        channelType: ChannelType,
                                        archive: Boolean,
                                        teamId: TeamId
                                      ): Either[ChangeArchiveOrSnoozeStatusOfConvError, Int] = {

    val new_update: Either[ChangeArchiveOrSnoozeStatusOfConvError, Int] = archive match {
      case true =>
        //archival process is straightforward
        getMessageThreadDAOByChannelType(channelType).flatMap(threadDAO => {
          threadDAO.updateInboxFolderTypeTry(
            threadIds = threadIds,
            teamId = teamId,
            folder_type = FolderType.DONE
          )
        }) match {
          case Failure(exception) => Left(ChangeArchiveOrSnoozeStatusOfConvError.DBFailure(exception))
          case Success(result) => Right(result)
        }

      case false =>
        //when unarchiving - we have to think if the thread moves to
        // prospects or non-prospects folder
        val result: Try[Int] = folderToProspectsOrNonProspects(
          threadIds = threadIds,
          teamId = teamId
        )

        result match {
          case Failure(exception) =>
            Left(ChangeArchiveOrSnoozeStatusOfConvError.UnarchiveOrUnsnoozeFailed(exception.getMessage))

          case Success(result) =>
            Right(result)
        }
    }

    new_update

  }

  def changeSnoozeStatusOfConvInboxV3(
                                       threadIds: List[Long],
                                       channelType: ChannelType,
                                       snooze: Boolean,
                                       snoozed_till: Option[DateTime],
                                       teamId: TeamId,
                                       is_automatic_unsnooze: Boolean = false
                                     ): Either[ChangeArchiveOrSnoozeStatusOfConvError, Int] = {

    val new_update: Either[ChangeArchiveOrSnoozeStatusOfConvError, Int] = snooze match {
      case true =>
        //snoozing process is straightforward

        getMessageThreadDAOByChannelType(channelType)
          .flatMap(messageThreadDAO => {
            messageThreadDAO.changeFolderToSnooze(
              threadIds = threadIds,
              snoozed_till = snoozed_till,
              teamId = teamId
            )
          }) match {
          case Failure(exception) => Left(ChangeArchiveOrSnoozeStatusOfConvError.DBFailure(exception))
          case Success(result) => Right(result)
        }

      case false =>
        //when unsnoozing - we have to think if the thread moves to
        // prospects or non-prospects folder
        val result: Try[Int] = channelType match {
          case ChannelType.EmailChannel =>
            folderToProspectsOrNonProspects(threadIds = threadIds.map(_.toLong).toSeq,
              teamId = teamId,
              is_automatic_unsnooze = is_automatic_unsnooze
            )

          case ChannelType.LinkedinChannel =>
            linkedinMessageThreadsDAO.changeFolderToSnooze(
              threadIds = threadIds,
              snoozed_till = snoozed_till,
              teamId = teamId
            )

          case _ =>
            Failure(new Exception(s"Team Inbox not supported for ${channelType.toString}"))
        }

        result match {
          case Failure(exception) =>
            Left(ChangeArchiveOrSnoozeStatusOfConvError.UnarchiveOrUnsnoozeFailed(exception.getMessage))

          case Success(result) =>
            Right(result)
        }
    }

    new_update
  }

  def findThreadMessages(
                          threadId: Long,
                          teamId: TeamId,
                          channelType: ChannelType,
                          allTrackingDomains: Seq[String]
                        )(using logger: SRLogger): Try[Seq[MessageObject]] = {

    val messageDAOTry: Try[ConversationMessageDAOTrait] = channelType match {
      case ChannelType.EmailChannel =>
        Success(emailScheduledDAOService)

      case ChannelType.LinkedinChannel =>
        Success(linkedinMessagesDAOService)

      case _ =>
        Failure(new Exception(s"Team Inbox not supported for ${channelType.toString}"))
    }

    messageDAOTry.flatMap(messageDAO => {
      messageDAO.findThreadMessages(
        threadId = threadId,
        teamId = teamId,
        allTrackingDomains = allTrackingDomains,
        findThreadMessageFlow = FindThreadMessageFlow.GetConversationEmails
      )
    })

  }

  // Email Specific Function
  def getPreviousEmailsBeforeReply(
                                    emailThreadId: Long,
                                    team_id: TeamId,
                                    timezone: Option[String]
                                  )(using logger: SRLogger, ec: ExecutionContext): Future[String] = {
    val repTrackingHostsFut: Future[Seq[RepTrackingHosts]] = Future.fromTry {
      repTrackingHostService.getRepTrackingHosts()
    }

    repTrackingHostsFut
      .map(repTrackingHosts => {
        emailScheduledDAOService.findThreadMessages(
          threadId = emailThreadId,
          allTrackingDomains = repTrackingHosts.map(_.host_url),
          teamId = team_id,
          findThreadMessageFlow = FindThreadMessageFlow.InboxReplyDraft
        ).get
      })
      .map(emailMessages => {
        emailService.makeReplyEmailDraft(
          emailMessagesRaw = emailMessages,
          accountTimezone = timezone
        )
      })

  }

  def getSnoozedTillCrossedThreads()
                                  (implicit ec: ExecutionContext): Future[List[ThreadAndTeamId]] = {
    emailThreadDAOService.getEmailThreadsCrossedSnoozedTill()
  }

  private def folderToProspectsOrNonProspectsForAutomaticUnsnooze(
                                                                   threadIds: Seq[Long],
                                                                   teamId: TeamId,
                                                                   is_automatic_unsnooze: Boolean = false
                                                                 ): Try[Int] = {

    val dbAndSession = dbUtils.startLocalTx()

    val db = dbAndSession.db
    implicit val session = dbAndSession.session

    val result: Try[Int] = for {
      //1. find whether the threads belongs to prospects/non-prospects (find if thread_id exists in email_threads_prospects)
      prospectThreads: List[Long] <- {
        emailThreadDAOService.getThreadIdsExistingInEmailThreadsProspects(
          thread_ids = threadIds,
          teamId = teamId
        )
      }

      //2. get non-prospect related threads
      nonProspectThreads: Seq[Long] <- Try {
        threadIds.filterNot(t => prospectThreads.contains(t))
      }

      //3. update folder
      updatedFolderProspects: Seq[Int] <- {

        if (prospectThreads.nonEmpty) {
          var updated_at_time = DateTime.now()
          val update_res: Seq[Try[Int]] = prospectThreads.map(pt => {
            updated_at_time = updated_at_time.minusMillis(4)
            emailThreadDAOService.updateInboxFolderType(
              threadIds = Seq(pt),
              teamId = teamId,
              folder_type = FolderType.PROSPECTS,
              is_automatic_unsnooze = is_automatic_unsnooze,
              updated_at_unsnooze_cron = Some(updated_at_time)
            )
          }).toSeq

          Helpers.seqTryToTrySeq(update_res)
        } else {
          Success(Seq())
        }
      }

      updatedFolderNonProspects: Seq[Int] <- {

        if (nonProspectThreads.nonEmpty) {
          var updated_at_time = DateTime.now()
          val update_res: Seq[Try[Int]] = prospectThreads.map(pt => {
            updated_at_time = updated_at_time.minusMillis(4)

            emailThreadDAOService.updateInboxFolderType(
              threadIds = nonProspectThreads,
              teamId = teamId,
              folder_type = FolderType.NON_PROSPECTS,
              is_automatic_unsnooze = is_automatic_unsnooze
            )
          }).toSeq

          Helpers.seqTryToTrySeq(update_res)
        } else {
          Success(Seq())
        }
      }
    } yield {
      updatedFolderProspects.length + updatedFolderNonProspects.length
    }

    dbUtils.commitAndCloseSession(db = db)

    result

  }


  def unsnoozeThreadAutomatically(
                                   threadsAndTeamIds: List[ThreadAndTeamId]
                                 ): Future[Either[ChangeArchiveOrSnoozeStatusOfConvError, Seq[Int]]] = {

    val unsnoozeResult: Try[Seq[Int]] = for {

      //1. Group it by team_id

      groupedThreadsInTeam: Map[Long, List[Int]] <- Try {
        val mapTidListOfThreadAndTeamId = threadsAndTeamIds.groupBy(_.teamId)

        mapTidListOfThreadAndTeamId.map(element => (element._1, element._2.map(_.thread_id)))
      }

      //2. find in which folder(prospects/non-prospects) the thread should go ---
      // can use folderToProspectsOrNonProspects

      result: Seq[Int] <- {
        val res = groupedThreadsInTeam.map(t => {
          folderToProspectsOrNonProspectsForAutomaticUnsnooze(
            threadIds = t._2.map(_.toLong).toSeq,
            teamId = TeamId(t._1),
            is_automatic_unsnooze = true
          )
        }).toSeq

        Helpers.seqTryToTrySeq(res)
      }

    } yield {
      result
    }

    unsnoozeResult match {
      case Failure(exception) => Future.failed(exception)
      case Success(res) => Future.successful(Right(res))
    }

  }


}
