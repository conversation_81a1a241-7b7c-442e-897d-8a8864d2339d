package api.prospects

import api.AppConfig
import api.accounts.dao_service.OrganizationDAOService
import api.accounts.{Account, ProspectCategoriesInDB, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.accounts.service.OrganizationService
import api.blacklist.models.BlacklistTypeData.TeamLevelBlacklistData
import api.blacklist.models.{BlacklistEmailAndUuid, BlacklistUuid}
import api.blacklist.{Blacklist, BlacklistCreateUpdateFormWithUuid, BlacklistDAO}
import api.campaigns.CampaignProspectDAO
import api.prospects.dao.ProspectAddEventDAO
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.{ProspectCategory, ProspectCategoryId, ProspectCategoryNew, ProspectCategoryUpdateFlow, ProspectCategoryUpdateValidatedData, ProspectId}
import api.sr_audit_logs.models.{CreateEventLogError, EventDataType, EventType}
import api.sr_audit_logs.services.EventLogService
import api.team_inbox.dao.ReplySentimentDAO
import api.team_inbox.model.ReplySentimentTypeData
import api.team_inbox.service.{ReplySentimentForTeam, ReplySentimentUuid}
import org.joda.time.DateTime
import scalikejdbc.{AutoSession, DB, DBSession, SQLSyntax, scalikejdbcSQLInterpolationImplicitDef}
import utils.dbutils.DBUtils
import utils.{Helpers, SRLogger}
import utils.helpers.LogHelpers
import utils.uuid.SrUuidUtils
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.util.{Failure, Success, Try}

/*
NOTE: 17th Sep 21
This is a temp class thats created only for making the Prospect object Dependency injectable for now

This updateCategory logic needs to be refactored and moved back to Prospect and ProspectService
 */
class ProspectUpdateCategoryTemp(
  prospectDAOService: ProspectDAOService,
  prospectUpdateCategoryTemp2: ProspectUpdateCategoryTemp2,
  prospectAddEventDAO: ProspectAddEventDAO,
  eventLogService: EventLogService,
  campaignProspectDAO: CampaignProspectDAO,
  organizationDAOService: OrganizationDAOService, // Check and remove this DI once Rolling Update for autoUpdateProspectCategory is removed.
  srRollingUpdateCoreService: SrRollingUpdateCoreService,
  replySentimentDAO: ReplySentimentDAO,
  dbUtils: DBUtils,
) {

  private implicit val session: AutoSession.type = AutoSession

  def validateUpdateProspect(
                              prospectCategoryUpdateFlow: ProspectCategoryUpdateFlow,
                              account: Option[Account],
                              teamId: TeamId
                            )(using logger: SRLogger): ProspectCategoryUpdateValidatedData = {

    val newProspectCategoryId = prospectCategoryUpdateFlow.new_prospect_category_id
    val newProspectCategoriesFromDB: Option[ProspectCategoriesInDB] = prospectDAOService.getProspectCategoryById(team_id = teamId.id, category_id = newProspectCategoryId.id, account = account)

    val res: ProspectCategoryUpdateValidatedData = {
      prospectCategoryUpdateFlow match {

        case data: ProspectCategoryUpdateFlow.AdminUpdate =>

          data.old_prospect_category_id match {

            case None =>

              ProspectCategoryUpdateValidatedData(
                newProspectCategoryId = newProspectCategoryId,
                oldProspectCategoryId = None,
                newProspectCategoryFromDB = newProspectCategoriesFromDB,
                oldProspectCategoryFromDB = None
              )

            case Some(old_category_id) =>

              val old_prospect_category_from_db = prospectDAOService.getProspectCategoryById(team_id = teamId.id, category_id = old_category_id.id, account = account)

              ProspectCategoryUpdateValidatedData(
                newProspectCategoryId = newProspectCategoryId,
                oldProspectCategoryId = Some(old_category_id),
                newProspectCategoryFromDB = newProspectCategoriesFromDB,
                oldProspectCategoryFromDB = old_prospect_category_from_db
              )
          }

        case data: ProspectCategoryUpdateFlow.AutoUpdate =>

          val oldProspectCategoriesFromDB = prospectDAOService.getProspectCategoryById(team_id = teamId.id, category_id = data.old_prospect_category_id.id, account = account)

          ProspectCategoryUpdateValidatedData(
            newProspectCategoryId = newProspectCategoryId,
            oldProspectCategoryId = Some(data.old_prospect_category_id),
            newProspectCategoryFromDB = newProspectCategoriesFromDB,
            oldProspectCategoryFromDB = oldProspectCategoriesFromDB
          )


      }
    }

    res


  }


  def updateCategoryAndCreateEvent(
                                    prospectIds: Seq[Long],
                                    doerAccountId: Long,
                                    teamId: Long,
                                    accountName: String,
                                    prospectCategoryUpdateFlow: ProspectCategoryUpdateFlow,
                                    account: Option[Account],
                                    event_created_at: DateTime = DateTime.now(),
                                    logger: SRLogger,
                                    auditRequestLogId: Option[String]
                                  ): Try[Int] = Try {

    given Logger: SRLogger = logger

    if (prospectIds.isEmpty) 0
    else {

      val newProspectCategoryIdCustom: ProspectCategoryId = prospectCategoryUpdateFlow.new_prospect_category_id
      val prospectStatusUpdatedManually: Boolean = prospectCategoryUpdateFlow.getProspectStatusUpdatedManually

      val dbAndSession = dbUtils.startLocalTx()
      val db = dbAndSession.db
      val session = dbAndSession.session

      val prospectsIdsWithUpdatedCategory: Seq[Long] = Try {

        val prospectsUpdated: List[Long] = prospectUpdateCategoryTemp2._updateCategoryDB(
          prospectIds = prospectIds,
          teamId = teamId,
          prospectStatusUpdatedManually = prospectStatusUpdatedManually,
          logger = logger,
          newProspectCategoryIdCustom = newProspectCategoryIdCustom
        )

        if (prospectsUpdated.nonEmpty) {

          // update prospect category id at campaign level as well
          campaignProspectDAO._updateProspectCategory(
            teamId = TeamId(id = teamId),
            prospectIds = prospectsUpdated,
            prospectCategoryIdCustom = newProspectCategoryIdCustom
          )

        }

        prospectsUpdated
      } match {
        case Success(prospectsUpdated) =>
          dbUtils.commitAndCloseSession(db = db)
          prospectsUpdated
        case Failure(exception) =>
          dbUtils.commitAndCloseSession(db = db)
          throw exception
      }

      val prospectcategoryData: ProspectCategoryUpdateValidatedData = validateUpdateProspect(
        prospectCategoryUpdateFlow = prospectCategoryUpdateFlow,
        account = account,
        teamId = TeamId(id = teamId)
      )

      val newProspectCategoryString = prospectcategoryData.newProspectCategoryFromDB.map(_.text_id).getOrElse("")
      val oldProspectCategoryStringOpt = prospectcategoryData.oldProspectCategoryFromDB.map(_.text_id)

      // add change category events
      val events = prospectsIdsWithUpdatedCategory.map { p =>
        CreateProspectEventDB(

          event_type = EventType.PROSPECT_CATEGORY_UPDATED,
          doer_account_id = Some(doerAccountId),
          doer_account_name = Some(accountName),

          assigned_to_account_id = None,
          assigned_to_account_name = None,

          old_category = oldProspectCategoryStringOpt,
          new_category = Some(newProspectCategoryString),

          prospect_id = p,
          email_thread_id = None,

          campaign_id = None,
          campaign_name = None,

          step_id = None,
          step_name = None,

          clicked_url = None,

          account_id = doerAccountId,
          team_id = teamId,
          email_scheduled_id = None,

          created_at = event_created_at,

          task_type = None,
          channel_type = None,
          task_uuid = None,
          call_conference_uuid = None,

          duplicates_merged_at = None,
          total_merged_prospects = None,
          potential_duplicate_prospect_id = None

        )
      }


      prospectAddEventDAO.addEvents(events = events).get

      //publishing trigger events
      // CREATED_OR_UPDATED_PROSPECT_IN_SMARTREACH is also implicitly called in Trigger.findAllforMQProcess
      if (prospectsIdsWithUpdatedCategory.isEmpty) {
        logger.warn(s"prospectsWithUpdatedCategory List is Empty")
      } else {

        val updateProspectCategoryEventDataLogs: Either[CreateEventLogError, Seq[String]] = eventLogService.createEventLogBatch(
          event_data_type = prospectsIdsWithUpdatedCategory.map(prospectId => {
            EventDataType.PushEventDataType.UpdateProspectCategoryEventData(
              prospectId = prospectId,
              doerAccountId = doerAccountId,
              teamId = teamId,
              newProspectCategoryIdCustom = newProspectCategoryIdCustom.id,
              triggerPath = None
            )
          }),
          teamId = teamId,
          accountId = doerAccountId
        )


        val updatedProspectsEventDataLogs: Either[CreateEventLogError, Seq[String]] = eventLogService.createEventLogBatch(
          event_data_type = prospectsIdsWithUpdatedCategory.map(prospectId => {
            EventDataType.PushEventDataType.UpdatedProspectsEventData(
              updated_id = prospectId,
              ownerAccountId = doerAccountId,
              teamId = teamId,
              triggerPath = None,
              oldProspectDeduplicationColumn = None //Sending it as None because though it is a UpdatedProspectsEventData it is not updating any Deduplication column
            )
          }),
          teamId = teamId,
          accountId = doerAccountId
        )


        val eventLogRes: Either[CreateEventLogError, Seq[String]] = for {
          seq1 <- updateProspectCategoryEventDataLogs
          seq2 <- updatedProspectsEventDataLogs
        } yield seq1 ++ seq2

        eventLogRes match {

          case Left(err) =>
            err match {
              case CreateEventLogError.SQLException(error) =>
                logger.fatal(s"CRITICAL: createEventLog for event_log_id_for_created_ids failed:", error)

              case CreateEventLogError.CreateEventLogFailedInsert(message) =>
                logger.fatal(s"CRITICAL: createEventLog for event_log_id_for_created_ids failed: message $message")

            }
            None

          case Right(eventLogIds) =>
            Some(eventLogIds.length)
        }
      }


      prospectsIdsWithUpdatedCategory.length
    }
  }

  // TEAMEDITION check DONE
  def updateCategory(
                      prospect: ProspectBasicForBatchUpdate,
                      doerAccountId: Long,
                      accountName: String,
                      teamId: Long,
                      prospectCategoryUpdateFlow: ProspectCategoryUpdateFlow,
                      account: Account,
                      logger: SRLogger,
                      auditRequestLogId: Option[String]
                    ): Try[Int] = {
    updateBatchCategory(
      prospects = Seq(prospect),
      doerAccountId = doerAccountId,
      accountName = accountName,
      teamId = teamId,
      prospectCategoryUpdateFlow = prospectCategoryUpdateFlow,
      account = account,
      logger = logger,
      auditRequestLogId = auditRequestLogId
    )
  }


  
  def autoUpdateProspectCategory(
    teamId: TeamId,
    prospectIds: Seq[ProspectId],
    doerAccountId: AccountId,
    newProspectCategory: ProspectCategory.Value
  )(
    implicit logger: SRLogger
  ): Try[Int] = {

    val isNewCategory: Boolean = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
      teamId = teamId,
      feature = SrRollingUpdateFeature.NewReplySentiments
    )(logger)

      logger.info(
        msg = s"autoUpdateProspectCategory - start - teamId: $teamId :: prospectIds: $prospectIds :: doerAccountId: $doerAccountId :: newProspectCategory: ${newProspectCategory.toString}"
      )

      prospectDAOService.getProspectCategoryId(
        teamId = teamId,
        text_id = newProspectCategory,
        account = None
      ) match {

        case Failure(exception) =>

          logger.shouldNeverHappen(
            msg = s"Failed to find prospect category id. teamId: $teamId :: textId: ${newProspectCategory.toString}",
            err = Some(exception)
          )

          Failure(exception)

        case Success(newProspectCategoryId) =>

          prospectDAOService.getCurrentProspectCategoryDetails(
            teamId = teamId,
            prospectIds = prospectIds
          ) match {

            case Failure(exception) =>

              logger.shouldNeverHappen(
                msg = s"Failed to get currentProspectCategoryDetails. teamId: $teamId :: prospectIds: $prospectIds",
                err = Some(exception)
              )

              Failure(exception)

            case Success(oldProspectCategoryDetails) =>
              
              val orgId: OrgId = organizationDAOService.getOrgIdFromTeamId(
                teamId = teamId
              ) match {
                case Failure(exception) => 
                  logger.shouldNeverHappen(
                    msg = s"Failed to get orgId. teamId: $teamId",
                    err = Some(exception)
                  )
                  OrgId(0)
                case Success(orgId) => orgId
              }

              val validOldProspectCategoryDetails = oldProspectCategoryDetails.filter { opd =>

                if(!isNewCategory){
                  ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValid(
                    orgId = orgId,
                    currProspectCategoryTextId = opd.prospectCategoryTextId,
                    newProspectCategoryTextId = newProspectCategory.toString
                  )
                } else {
                  ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValidNew(
                    currProspectCategoryTextId = opd.prospectCategoryTextId,
                    newProspectCategoryTextId = ProspectCategoryNew.getNewCategoryFromOldCategory(oldCategory = newProspectCategory).toString
                  )
                }
              }

              // just used for logging
              val invalidProspectCategoryDetails = oldProspectCategoryDetails.filter { opc =>

                !validOldProspectCategoryDetails.map(_.prospectId).contains(opc.prospectId)

              }

              if (invalidProspectCategoryDetails.nonEmpty) {

                logger.info(
                  msg = s"Cannot update prospect categories to ${newProspectCategory.toString} for these prospects - invalidProspectCategoryDetails: $invalidProspectCategoryDetails :: teamId: $teamId"
                )

              }

              if (validOldProspectCategoryDetails.isEmpty) {

                Success(0)

              } else {

                val validProspectCategoryDetailsGrouped =
                  validOldProspectCategoryDetails.groupBy(_.prospectCategoryId)

                val updateCountList = validProspectCategoryDetailsGrouped.map {

                  case (oldProspectCategoryId, vpcd) =>

                    updateCategoryAndCreateEvent(
                      doerAccountId = doerAccountId.id,
                      prospectIds = vpcd.map(_.prospectId.id),
                      accountName = "SmartReach", // TODO: check this
                      teamId = teamId.id,
                      prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AutoUpdate(
                        old_prospect_category_id = oldProspectCategoryId,
                        new_prospect_category_id = newProspectCategoryId,
                      ),
                      logger = logger,
                      auditRequestLogId = Some(logger.logRequestId),
                      account = None,
                    ) match {

                      case Failure(exception) =>

                        logger.shouldNeverHappen(
                          msg = s"Failed update prospect categories to ${newProspectCategory.toString} for these prospects - prospects: $vpcd :: teamId: $teamId",
                          err = Some(exception),
                        )

                        0

                      case Success(updateCount) =>

                        updateCount

                    }

                }

                Success(updateCountList.sum)

              }

          }


    }

  }


  def updateProspectCategoryOnReplySentimentChange(
                                                    reply_sentiment_uuid: ReplySentimentUuid,
                                                    teamId: TeamId,
                                                    accountId: AccountId,
                                                    prospectIds: Seq[ProspectId]
                                                  )(using logger: SRLogger): Try[Int] = {
    for {
      replySentiment: Option[ReplySentimentForTeam] <- replySentimentDAO.getReplySentimentsForUUID(
        team_id = teamId.id,
        uuid = reply_sentiment_uuid
      )

      categoryForReplySentiment: Option[ProspectCategory.Value] <- {
        replySentiment match {
          case None =>
            logger.error(s"replysentiment not found for uuid ${reply_sentiment_uuid} :: ${teamId}")
            Failure(new Exception(s"Replysentiment not found"))
          case Some(sentiment) => Success(ReplySentimentTypeData.getNewProspectCategoryToMoveToBasedOnReplySentimentTypeData(
            replySentimentTypeData = sentiment.reply_sentiment
          ))
        }
      }

      autoUpdateCategory: Int <- Try {
        categoryForReplySentiment match {
          case None => 0

          case Some(category) => autoUpdateProspectCategory(
            teamId = teamId,
            prospectIds = prospectIds,
            doerAccountId = accountId,
            newProspectCategory = category
          ).get
        }

      }
    } yield {
      autoUpdateCategory
    }
  }



  def updateBatchCategory(
                           prospects: Seq[ProspectBasicForBatchUpdate],
                           doerAccountId: Long,
                           accountName: String,
                           teamId: Long,
                           prospectCategoryUpdateFlow: ProspectCategoryUpdateFlow,
                           account: Account,
                           logger: SRLogger,
                           auditRequestLogId: Option[String]
                         ): Try[Int] = {

    val prospectsToBeUpdated: Seq[ProspectBasicForBatchUpdate] = prospects.filter(
      p => p.prospect_category_id_custom != prospectCategoryUpdateFlow.new_prospect_category_id.id
    )

    updateCategoryAndCreateEvent(
      prospectIds = prospectsToBeUpdated.map(_.id),
      doerAccountId = doerAccountId,
      teamId = teamId,
      accountName = accountName,
      prospectCategoryUpdateFlow = prospectCategoryUpdateFlow,
      account = Some(account),
      logger = logger,
      auditRequestLogId = auditRequestLogId
    )
  }
}
