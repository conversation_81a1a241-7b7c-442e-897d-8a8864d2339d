package api.prospects

import api.{ApiResponseModuleForPermissionedApis, ApiVersion}
import api.accounts._
import api.accounts.models.OrgId
import api.campaigns.models.GetCampaignIdFromSrIdentifierError
import api.campaigns.{CampaignRequest, OptionalCampaignRequest}
import api.campaigns.services.{CampaignService, GetProspectIdFromUuidError}
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.ProspectId
import eventframework.ProspectObject
import play.api.mvc.{ActionRefiner, Request, Result, WrappedRequest}
import utils.SRLogger
import utils.uuid.{SrId, SrIdentifier, SrUuid}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Success


case class ProspectRequest[A](
  prospect: ProspectObject,
  permittedAccountIds: Seq[Long],
  actingTeamAccount: TeamMember,
  loggedinAccount: Account,
  isApiCall: <PERSON><PERSON><PERSON>,
  Logger: SRLogger,
  Response: ApiResponseModuleForPermissionedApis,
  request: Request[A],
  auditRequestLogId: String,
  permissionRequest: PermissionRequest[A]
) extends WrappedRequest[A](request)


trait ProspectUtils {


  val prospectDAOService: ProspectDAOService
  val prospectService: ProspectService
  val campaignService: CampaignService

  // TEAMEDITION: this check and all apis where this check is called
  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  // use case : hasProspect trait
  final def hasProspect(prospectId: Long)(
                       implicit ec: ExecutionContext
  ) = new ActionRefiner[PermissionRequest, ProspectRequest] {

    override protected def executionContext: ExecutionContext = ec

    override def refine[A](request: PermissionRequest[A]): Future[Either[Result, ProspectRequest[A]]] = {

      given Logger: SRLogger = request.Logger
      val Res = request.Response

      if (request.actingTeamAccount.isEmpty) {
        Future.successful(Left(Res.ForbiddenError("Please select a team first")))
      } else {

        _hasProspect(
          request = request,
          prospectId = ProspectId(prospectId),
          actingTeamAccount = request.actingTeamAccount.get
        )

      }


    }
  }

  /*
  Date: 30-Mar-2024

  we are receiving SrIdentifier here, so we first convert to ProspectId
  and rest of the flow is same as hasProspect()
   */
  final def hasProspectV2(
                           id: SrIdentifier,
                           v: ApiVersion
                       )(
    implicit ec: ExecutionContext
  ) = new ActionRefiner[PermissionRequest, ProspectRequest] {

    override protected def executionContext: ExecutionContext = ec

    override def refine[A](request: PermissionRequest[A]): Future[Either[Result, ProspectRequest[A]]] = {

      given Logger: SRLogger = request.Logger
      implicit val apiVersion: ApiVersion = v
      val Res = request.Response

      if (request.actingTeamAccount.isEmpty) {
        Future.successful(Left(Res.ForbiddenError("Please select a team first")))
      } else {

        _hasProspectV2(
          request = request,
          id = id,
          actingTeamAccount = request.actingTeamAccount.get
        )

      }

    }
  }

  private def _hasProspect[A](
                               request: PermissionRequest[A],
                               prospectId: ProspectId,
                               actingTeamAccount: TeamMember
                             )(using Logger: SRLogger, ec: ExecutionContext): Future[Either[Result, ProspectRequest[A]]] = Future {

    val Res = request.Response

    // PROSPECTS_EMAILS_TODO_READ_CLEANED
    prospectDAOService.findFromMaster(
      prospectId = prospectId.id,
      teamId = actingTeamAccount.team_id,
      Logger = Logger,
      orgId = Some(OrgId(id = request.loggedinAccount.org.id))
    ).get match {

      case None => Left(Res.NotFoundError("Prospect not found"))

      case Some(prospect) =>

        if (!request.permittedAccountIds.contains(prospect.owner_id)) {
          Left(Res.ForbiddenError("You do not have the permission to access this prospect."))
        } else if (actingTeamAccount.team_id != prospect.team_id) {
          Left(Res.ForbiddenError("Prospect does not belong to the current team. Please switch view to the respective team."))
        } else {
          val newLogger = new SRLogger(logRequestId = s"${Logger.logRequestId} pid_${prospect.id} :: ")

          /** *
           * 27 Jan 2022
           * before Response param accepting new SRAPIResponse(Logger = newLogger)
           * from now SRAPIResponse api response split into two parts ApiResponseModuleForPermissionedApis and SRAPIResponse (for NONPermissionedApis)
           * in the old flow anyways Logger is overriding and only using for prospectUtils everywhere it is default
           * So removed from here
           * */
          Right(ProspectRequest(
            prospect = prospect,
            permittedAccountIds = request.permittedAccountIds,
            actingTeamAccount = actingTeamAccount,
            loggedinAccount = request.loggedinAccount,
            isApiCall = request.isApiCall,
            Logger = newLogger,
            Response = request.Response,
            request = request.request,
            auditRequestLogId = request.auditRequestLogId,
            permissionRequest = request
          ))
        }

    }
  }

  private final def checkHasCampaign[A](
                                         Res: ApiResponseModuleForPermissionedApis,
                                         request: PermissionRequest[A],
                                         id: Long
                                       )(implicit apiVersion: ApiVersion = ApiVersion.V2): Either[Result, CampaignRequest[A]] = {
    if (request.actingTeamAccount.isEmpty) {
      Left(Res.ForbiddenError("Please select a team first"))
    }
    else {

      val teamId = TeamId(request.actingTeamAccount.get.team_id)
      campaignService.findCampaignForCampaignUtilsOnly(id = id, teamId = teamId) match {

        case None => Left(Res.NotFoundError("Campaign not found"))

        case Some(campaign) =>

          if (!request.permittedAccountIds.contains(campaign.account_id)) {
            Left(Res.ForbiddenError("You do not have the permission to access this campaign"))
          } else if (request.actingTeamAccount.get.team_id != campaign.team_id) {
            Left(Res.ForbiddenError("Campaign does not belong to the current team. Please switch view to the respective team."))
          } else {
            Right(
              CampaignRequest(
                campaign = campaign,
                actingTeamAccount = request.actingTeamAccount.get,
                loggedinAccount = request.loggedinAccount,
                isApiCall = request.isApiCall,
                permittedAccountIds = request.permittedAccountIds,
                Logger = request.Logger,
                Response = request.Response,
                request
              )
            )
          }

      }
    }


  }


  final def hasCampaignEditPermissionWithOptionId(
                                     id: Option[SrIdentifier],
                                     v: ApiVersion = ApiVersion.V2
                                   )(
                                     implicit ec: ExecutionContext
                                   ) = new ActionRefiner[PermissionRequest, OptionalCampaignRequest] {

    override protected def executionContext: ExecutionContext = ec

    override def refine[A](request: PermissionRequest[A]): Future[Either[Result, OptionalCampaignRequest[A]]] = {

      Future {

        val Logger = request.Logger
        val Res = request.Response
        val teamId = TeamId(request.actingTeamAccount.get.team_id)

        implicit val version: ApiVersion = v

        if (request.actingTeamAccount.isEmpty) {
          Left(Res.ForbiddenError("Please select a team first"))
        } else {
          if (id.isDefined) {
            campaignService.getCampaignIdFromSrIdentifier(
              teamId = teamId,
              id = id.get
            ) match {
              case Left(GetCampaignIdFromSrIdentifierError.DbFailure(e)) =>
                Left(Res.ServerError(s"Error while getting campaign's id.", e = Some(e)))

              case Left(GetCampaignIdFromSrIdentifierError.CampaignIdNotFound(err_msg)) =>
                Left(Res.NotFoundError(err_msg))

              case Right(campaign_id) =>
                checkHasCampaign(
                  Res = Res,
                  request = request,
                  id = campaign_id
                ) match {
                  case Left(err) =>
                    Left(err)
                  case Right(campaignRequest) =>
                    val campaignEditPermissionAccountIds = PermissionMethods.getPermittedAccountIdsForAccountAndPermission(
                      loggedinAccount = campaignRequest.loggedinAccount,
                      actingTeamId = request.actingTeamAccount.get.team_id,
                      actingAccountId = request.actingTeamAccount.get.user_id,
                      version = v.toString,
                      Logger = Logger
                    )(permission = PermType.EDIT_CAMPAIGNS)

                    if (campaignEditPermissionAccountIds.contains(campaignRequest.loggedinAccount.internal_id)) {
                      Right(OptionalCampaignRequest(
                        campaign = Some(campaignRequest.campaign),
                        actingTeamAccount = Some(campaignRequest.actingTeamAccount),
                        loggedinAccount = campaignRequest.loggedinAccount,
                        isApiCall = campaignRequest.isApiCall,
                        permittedAccountIds = campaignRequest.permittedAccountIds,
                        Logger = campaignRequest.Logger,
                        Response = campaignRequest.Response,
                        request = campaignRequest.request
                      ))
                    } else {
                      Left(Res.ForbiddenError("You don't have permission to Edit this Campaign"))
                    }
                }
                }

          } else {
            Right(
              OptionalCampaignRequest(
                campaign = None,
                actingTeamAccount = request.actingTeamAccount,
                loggedinAccount = request.loggedinAccount,
                isApiCall = request.isApiCall,
                permittedAccountIds = request.permittedAccountIds,
                Logger = request.Logger,
                Response = request.Response,
                request = request
              )
            )
          }
        }


      }


    }
  }


  /*
  Date: 30-Mar-2024

    Converts SrIdentifier to ProspectId and uses _hasProspect()
   */
  private def _hasProspectV2[A](
                                                 request: PermissionRequest[A],
                                                 id: SrIdentifier,
                                                 actingTeamAccount: TeamMember
                                               )(using Logger: SRLogger, apiVersion: ApiVersion, ec: ExecutionContext): Future[Either[Result, ProspectRequest[A]]] = {


    val Res = request.Response

    val teamId = TeamId(actingTeamAccount.team_id)

    prospectService.getProspectIdFromSrIdentifier(
      id = id,
      teamId = teamId
    ) match {

      case Left(GetProspectIdFromSrIdentifierError.GetProspectIdError(err)) =>
        Future.successful(Left(Res.ServerError(message = "Server error, please contact support", e = Some(err))))

      case Left(GetProspectIdFromSrIdentifierError.InvalidProspectIdError) =>
        Future.successful(Left(Res.NotFoundError("Please pass a valid prospect_id")))

      case Right(prospectId: ProspectId) =>
        _hasProspect(
          request = request,
          prospectId = prospectId,
          actingTeamAccount = actingTeamAccount
        )

    }

  }

}
