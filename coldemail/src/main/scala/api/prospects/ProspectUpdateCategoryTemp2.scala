package api.prospects


import api.prospects.models.{ProspectCategory, ProspectCategoryId, ProspectId}
import scalikejdbc.{AutoSession, DB, DBSession, SQLSyntax, scalikejdbcSQLInterpolationImplicitDef}
import utils.{Help<PERSON>, SRLogger}
import utils.dbutils.SQLUtils

import scala.util.{Failure, Success, Try}

/*
NOTE: 20th Dec 21
This is a temp class thats created only for removing the cyclic dependency between
BlacklistDAO and ProspectUpdateCategoryTemp

This needs to refactored into separate service and dao classes
 */
class ProspectUpdateCategoryTemp2 {
  private implicit val session: AutoSession.type = AutoSession

  // TEAMEDITION check
  // CALL_UPDATE_PROSPECT_TRIGGER: DONE below
  /*
    _updateCategoryDB used from below flows

      1. BlackListDAO : removing prospects from DoNotContact category
      2. BlackListService : When adding a prospect_category to DNC, we aren't checking their previous category
      3. 3 times ProspectUpdateCategoryTemp : Passing old category of prospect
      4. 3 times ProspectUpdateCategoryTemp : internally from updateBatchCategory :
          we are not passing older category but  prospectStatusUpdatedManually is always true

   */
  def _updateCategoryDB(
                         prospectIds: Seq[Long],
                         teamId: Long,
                         prospectStatusUpdatedManually: Boolean,
                         logger: SRLogger,
                         newProspectCategoryIdCustom: ProspectCategoryId
                       )(implicit session: DBSession): List[Long] =  {

    if (prospectIds.isEmpty) List()
    else {
      // if prospect status was updated manually before OR prospect category is not not_categorized, do not overwrite it
      val categoryUpdatedAutomaticallyCheck: SQLSyntax = if (!prospectStatusUpdatedManually)
        sqls""" and not prospect_category_updated_manually """ else sqls" "

      val prospectUpdateQuery =
        sql"""
              UPDATE prospects
              SET
                prospect_category_id_custom = ${newProspectCategoryIdCustom.id},
                prospect_category_updated_manually = $prospectStatusUpdatedManually,
                prospect_category_updated_at = now(),
                updated_at = now()
              WHERE id IN ${SQLUtils.generateSQLValuesClause(prospectIds)}
              AND team_id = ${teamId}

              $categoryUpdatedAutomaticallyCheck

              RETURNING id, account_id, team_id
          """


      prospectUpdateQuery
        .map(rs => rs.long("id"))
        .list
        .apply()

    }
  }
}
