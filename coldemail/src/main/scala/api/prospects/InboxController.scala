package api.prospects

import org.apache.pekko.actor.ActorSystem
import api.accounts.*
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.models.{CampaignStepType, SendEmailFromCampaignDetails}
import api.campaigns.services.{CampaignId, CampaignService}
import api.columns.FieldTypeEnum
import api.emails.dao_service.EmailScheduledDAOService
import api.emails.dao_service.EmailThreadDAOService
import api.emails.models.EmailServiceProviderSendEmail.getEmailServiceProviderSendEmail
import api.emails.{InboxV3Folder, *}
import api.emails.models.{EmailMessageContactModel, EmailReplyType, EmailServiceProviderSendEmail, InboxType, InboxTypeData}
import api.linkedin_message_threads.LinkedinMessageThreadsDAO
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.{ProspectAccountsId, ProspectCategory, ProspectId, ValidateSendNewEmailManuallyError}
import api.prospects.service.{InboxSendService, ProspectServiceV2}
import api.rep_tracking_hosts.service.RepTrackingHostService
import api.search.{SearchQuery, SearchQueryColumnOperator, SearchQueryMainClause, SearchQueryOperators, SearchQuerySubClause, SearchQuerySubFilterData}
import api.tasks.models.ChangeStatusPermissionCheck
import api.tasks.models.UpdateTaskStatus.{Done, QueuedToMq}
import api.tasks.pgDao.TaskPgDAO
import api.tasks.{MqUpdateManualEmailTaskStatus, TaskStatusChangesData}
import api.team_inbox.{InboxUtils, TeamInboxAccessRequest, TeamInboxAccessRequestV2}
import api.team_inbox.model.FolderType
import api.team_inbox.service.ValidateInboxTypeAndGetEsetsError.EsetsEmptyForCampaignInbox
import api.team_inbox.service.{GetCampaignAccessError, InboxV3PaginationService, TeamInboxService, ValidateInboxTypeAndGetEsetsError}
import api.{APIBadRequestException, APIManualEmailSentButSavingFailedException, APINotFoundException, AppConfig, CONSTANTS, ErrorCode, SRAPIResponse}
import eventframework.{ConversationObjectInboxV3, MessageObject, ProspectObject}
import utils.{Helpers, SRLogger}
import utils.email.{EmailReplyStatus, EmailSendDetail, EmailSendMailRequest, EmailSenderService, EmailService, EmailServiceBody, GenerateInboxDraft}
import org.joda.time.DateTime
import play.api.libs.json.*
import play.api.libs.ws.WSClient
import play.api.mvc.*
import utils.email.services.InternalTrackingNote
import utils.templating.TemplateService
import utils.helpers.LogHelpers

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import io.lemonlabs.uri.Url
import sr_scheduler.models.ChannelType
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import utils.Helpers.{getNextLinkForConvThread, getPrevLinkForConvThread}
import utils.mq.email.MQEmailMessage
import utils.uuid.services.SrUuidService
import play.api.libs.ws.WSBodyWritables.*

import scala.collection.immutable.List


class InboxController(
                       protected val controllerComponents: ControllerComponents,
                       emailService: EmailService,
                       emailSenderService: EmailSenderService,
                       inbox: Inbox,
                       templateService: TemplateService,
                       campaignService: CampaignService,
                       emailSettingDAO: EmailSettingDAO,
                       prospectDAOService: ProspectDAOService,
                       emailScheduledDAO: EmailScheduledDAO,
                       emailScheduledDAOService: EmailScheduledDAOService,
                       emailReplyTrackingModelV2: EmailReplyTrackingModelV2,
                       protected val srUuidService: SrUuidService,
                       override protected val emailThreadDAOService: EmailThreadDAOService,
                       override protected val linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO,
                       accountService: AccountService,
                       permissionUtils: PermissionUtils,
                       prospectAccountDAO: ProspectAccountDAO1,
                       inboxV3Service: InboxV3Service,
                       taskPgDAO: TaskPgDAO,
                       inboxSendService: InboxSendService,
                       prospectServiceV2: ProspectServiceV2,
                       implicit val wSClient: WSClient,
                       implicit val system: ActorSystem,
                       emailMessageContactModel: EmailMessageContactModel,
                       repTrackingHostService: RepTrackingHostService,
                       mqUpdateManualEmailTaskStatus:MqUpdateManualEmailTaskStatus,
                       override protected val teamInboxService: TeamInboxService,
                       inboxV3PaginationService: InboxV3PaginationService
                     ) extends BaseController with EmailUtils with InboxUtils {
  private implicit val ec: ExecutionContext = controllerComponents.executionContext

  //New inbox API and has LinkedIn Flow
  def getConversationV3(v: String, uuid: String, aid: Option[Long], tid: Option[Long], tiid: String): Action[AnyContent] = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.VIEW_CHANNELS,
      tidOpt = tid

    ) andThen hasTeamInboxAccess(
      teamInboxIdFromReq = tiid,
      emailThreadUuid = uuid,
    )
    ) { (request: TeamInboxAccessRequest[AnyContent]) =>
    given Logger: SRLogger = request.Logger
    val Res = request.Response
    val ta = request.actingTeamAccount.get
    val permittedAccountIds = request.permittedAccountIds
    val inbox_type_data = request.inbox_type_data
    val mail_box_folder: MailboxFolder = MailboxFolder.getMailBoxFolderTypeBasedOnInboxType(
      inbox_type = inbox_type_data.inbox_type
    )

    inboxV3Service.findConversationByIdNew(
      teamId = ta.team_id,
      channelType = request.thread.channelType,
      validatedConvReq = ValidatedConvReq.ValidatedSingleConvReq(conv_id = request.thread.id, mailboxFolderRequest = mail_box_folder)
    ) match {
      case Left(FindConversationByIdError.DatabaseError(ex)) =>
        Res.BadRequestError(message = ex.getMessage)

      case Left(FindConversationByIdError.PermissionDeniedError(permissionDeniedError)) =>
        Res.ForbiddenError(permissionDeniedError)
      case Right(conv) =>
        Res.Success("Email conversation found", Json.obj(
          "conversation" -> conv
        ))

    }

    /*
    // todo replace with findConversationByIdNew

    inboxV3Service.findConversationById(
      teamId = request.actingTeamAccount.get.team_id,
      permittedOwnerIds = request.permittedAccountIds,
      conv_id = request.thread.id,
      Logger =  Logger
    ) match {
      case Left(FindConversationByIdError.DatabaseError(ex)) =>
        Res.BadRequestError(message = ex.getMessage)

      case Left(FindConversationByIdError.PermissionDeniedError(permissionDeniedError)) =>
        Res.ForbiddenError("permission denied")
      case Right(conv) =>
        Res.Success("Email conversation found", Json.obj(
          "conversation" -> conv
        ))

    }*/
    // Res.ForbiddenError("permission denied")

  }

  //New inbox API and has LinkedIn Flow
  def getConversationEmailsV2(v: String, uuid: String, aid: Option[Long], tid: Option[Long], tiid: String) =
    (
      permissionUtils.checkPermission(
        version = v,
        permission = PermType.VIEW_CHANNELS,
        tidOpt = tid

      ) andThen hasTeamInboxAccess(
        teamInboxIdFromReq = tiid,
        emailThreadUuid = uuid,
      )

      ).async { request =>
      given Logger: SRLogger= request.Logger
      val Res = request.Response
      val thread = request.thread
      val ta = request.actingTeamAccount.get

      inboxV3Service.getEmailsInConversation(
        thread = thread,
        email_thread_id = thread.id,
        isSupportAppModeRequest = request.isSupportAppModeRequest,
        account_id = ta.user_id,
        teamId = ta.team_id
      ).map(result => {
        result match {
          case Left(GetEmailsInConversationError.ErrorWhileFetchingEmails(e)) =>
            Logger.error(s"INBOX_ALERT: Error while getting emails in conversation for thread_id ${thread.id}", e)
            Res.ServerError("Error while fetching conversation", e = Some(e))

          case Right(emailMessages) =>
            Res.Success(

              "Messages found", Json.obj(
                "messages" -> emailMessages
              )
            )
        }
      })
    }

  // Aug 2022: team_inbox api
  //New inbox API and LinkedIn Flow not required because it's email specific
  def getForwardEmailDraftV2(v: String, uuid: String, aid: Option[Long], tid: Option[Long], tiid: String) =
    (
      permissionUtils.checkPermission(
        version = v,
        permission = PermType.VIEW_CHANNELS,
        tidOpt = tid

      ) andThen hasTeamInboxAccess(
        teamInboxIdFromReq = tiid,
        emailThreadUuid = uuid,
      )

      ).async { request =>
      given Logger: SRLogger= request.Logger
      val Res = request.Response
      val thread = request.thread
      val ta = request.actingTeamAccount

      thread.channelType match {
        case ChannelType.EmailChannel =>
          val repTrackingHostsFut = Future.fromTry {
            repTrackingHostService.getRepTrackingHosts()
          }

          repTrackingHostsFut
            .map(repTrackingHosts => {
              emailScheduledDAOService.findThreadMessages(
                threadId = thread.id,
                allTrackingDomains = repTrackingHosts.map(_.host_url),
                teamId = TeamId(request.actingTeamAccount.get.team_id),
                findThreadMessageFlow = FindThreadMessageFlow.InboxForwardEmailDraft
              ).get
            })
            .map(emailMessages => {

              val (forwardingBody, subject) = emailService.makeForwardingEmailDraft(
                emailMessagesRaw = emailMessages,
                accountTimezone = request.loggedinAccount.timezone
              )

              Res.Success(

                "Forwarding draft email", Json.obj(
                  "body" -> forwardingBody,
                  "subject" -> subject
                )
              )

            })
            .recover { case e =>
              Res.ServerError("Error while fetching conversation for forwarding", e = Some(e)
              )
            }

        case _ =>
          Future.successful(Res.BadRequestError(s"Message Forwarding in Team Inbox not supported for ${thread.channelType.toString}"))
      }

    }

  // 24 March 2023: team_inbox api
  //Old inbox API and LinkedIn Flow not required
  def getReplyEmailDraft(v: String, uuid: String, tid: Option[Long], tiid: String) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.VIEW_CHANNELS,
      tidOpt = tid

    ) andThen hasTeamInboxAccess(
      teamInboxIdFromReq = tiid,
      emailThreadUuid = uuid,
    )

    ).async { request =>
    given Logger: SRLogger= request.Logger
    val Res = request.Response
    val thread = request.thread
    val ta = request.actingTeamAccount
    val timezone: Option[String] = request.loggedinAccount.timezone

    thread.channelType match {
      case ChannelType.EmailChannel =>
        inboxV3Service.getPreviousEmailsBeforeReply(
            emailThreadId = thread.id,
            team_id = TeamId(id = ta.get.team_id),
            timezone = timezone
          )
          .map(result => {
            Res.Success(
              "Reply draft email", Json.obj(
                "body" -> result
              )
            )
          })
          .recover { case e =>
            Logger.error(s"INBOX_ALERT: Error while getting reply email draft for thread_id ${thread.id}", e)
            Res.ServerError("Error while fetching conversation for reply", e = Some(e)
            )
          }

      case _ =>
        Future.successful(Res.BadRequestError(s"Replying to Messages through Team Inbox not supported for ${thread.channelType.toString}"))
    }

  }

  //New inbox API and has LinkedIn Flow
  def changeArchiveStatusOfEmailThread_NewV2(v: String, aid: Option[Long], tid: Option[Long], tiid: String, is_campaign_inbox_flow: Option[Boolean]) =
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CHANNELS,
      tidOpt = tid
    ).async(parse.json) { request =>

      Future {
        given Logger: SRLogger = request.Logger
        val Res = request.Response
        val account_id: AccountId = AccountId(request.loggedinAccount.internal_id)
        val teamId: TeamId = TeamId(request.actingTeamAccount.get.team_id)
        val org_id: OrgId = OrgId(request.loggedinAccount.org.id)
        val tidOpt: Option[TeamId] = request.actingTeamAccount
          .map(ta => TeamId(id = ta.team_id))

        if (tidOpt.isEmpty) {
          Res.ForbiddenError("Please send a valid team id")
        } else {

          val validateData = request.body.validate[InboxThreadArchiveFormData_V2]

          validateData match {

            case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

            case JsSuccess(data, _) =>

              srUuidService.separateThreadUuidsByChannel(uuids = data.team_inbox_conversations.flatMap(_.conversation_ids))
                .map {
                  case (channelType, uuids) =>
                    srUuidService.convertThreadUuidsToIds(
                      threadUuids = uuids.toList,
                      teamId = teamId
                    ) match {
                      case Failure(e) =>
                        Res.ServerError("Error while converting threadUuids to id", Some(e))

                      case Success(uuidToIdOptMap) =>
                        val team_inbox_conversations_map: Map[Option[Long], List[Long]] = data.team_inbox_conversations.groupBy(_.team_inbox_id).map {
                          case (team_inbox_id, teamInboxIdsConvsList) =>
                            val threadIds = teamInboxIdsConvsList.flatMap(_.conversation_ids).map(
                                uuidToIdOptMap(_)
                              )
                              .filter(_.isDefined)
                              .map(_.get)
                            (team_inbox_id, threadIds)
                        }.filter {
                          case (_, teamInboxIdsConvsList) => teamInboxIdsConvsList.nonEmpty
                        }

                        teamInboxService.validateTeamInboxesBelongToAccount(
                          account_id = account_id,
                          org_id = org_id,
                          team_id = teamId,
                          channelType = channelType,
                          team_inbox_ids = team_inbox_conversations_map.keys.toList,
                          is_campaign_inbox_flow = is_campaign_inbox_flow,
                          team_inbox_conversations_map = team_inbox_conversations_map,
                          permittedAccountIds = request.permittedAccountIds.toList
                        ) match {

                          case Left(ValidateInboxTypeAndGetEsetsError.DBFailure(e)) =>
                            Res.ServerError(e)

                          case Left(ValidateInboxTypeAndGetEsetsError.PermissionDenied(str)) =>
                            Res.ForbiddenError(str)

                          case Left(ValidateInboxTypeAndGetEsetsError.EmailSettingNotFound(str)) =>
                            Res.NotFoundError(str)

                          case Left(ValidateInboxTypeAndGetEsetsError.InvalidTeamInboxId(str)) =>
                            Res.NotFoundError(str)

                          case Left(ValidateInboxTypeAndGetEsetsError.EsetsEmptyForCampaignInbox) =>
                            Res.NotFoundError("Email account not found")

                          case Left(ValidateInboxTypeAndGetEsetsError.ErrorWhileGettingAllCampaignsAccess(err)) =>

                            err match {

                              case GetCampaignAccessError.DBError(e) =>

                                Res.ServerError(e)

                              case GetCampaignAccessError.NoCampaignAccessFound =>
                                Res.BadRequestError(CONSTANTS.API_MSGS.NO_CAMPAIGNS_ACCESS_FOUND_FROM_ALL_CAMPAIGNS_INBOX)

                              case GetCampaignAccessError.NoReceiverEmailSettingFound =>
                                Res.BadRequestError(CONSTANTS.API_MSGS.NO_RECEIVER_SETTINGS_FOUND_FROM_ALL_CAMPAIGNS_INBOX)

                            }

                          case Left(ValidateInboxTypeAndGetEsetsError.CampaignInboxFlowCheckError(err)) =>
                            Logger.error(s"changeArchiveStatusOfEmailThread_NewV2 :: TeamInboxIdsAre Empty account_id : $account_id, org_id : $org_id, team_id  $teamId, is_campaign_inbox_flow: ${is_campaign_inbox_flow}", err)
                            Res.BadRequestError(err.getMessage)

                          case Left(ValidateInboxTypeAndGetEsetsError.TeamInboxIdsAreEmpty) =>

                            Logger.error(s"changeArchiveStatusOfEmailThread_NewV2 :: TeamInboxIdsAre Empty account_id : $account_id, org_id : $org_id, team_id  $teamId, is_campaign_inbox_flow: ${is_campaign_inbox_flow}")
                            Res.BadRequestError("There was an error. Please contact support.")

                          case Right(success_msg) =>

                            val thread_ids: List[Long] = team_inbox_conversations_map.values.flatten.toList.map(_.toLong)

                            inboxV3Service.changeArchiveStatusOfConvInboxV3(
                              threadIds = thread_ids,
                              channelType = channelType,
                              archive = data.archive,
                              teamId = tidOpt.get,
                            ) match {

                              case Left(ChangeArchiveOrSnoozeStatusOfConvError.DBFailure(e)) =>
                                Res.ServerError("Problem while archiving the thread. Could you try again or contact support ? ", e = Some(e))

                              case Left(ChangeArchiveOrSnoozeStatusOfConvError.UnarchiveOrUnsnoozeFailed(_)) =>
                                Res.NotFoundError("Problem while unarchive. Could you try again or contact support ? ")

                              case Right(count) =>

                                if (count == 0) {
                                  Res.NotFoundError("Email thread not found. Could you reload and try again ?")
                                } else {
                                  Res.Success(if (data.archive) s"Email moved to ${if (v == "v1") "Archived" else "Done"}" else "Email moved to Inbox", Json.obj("total_updated" -> count))
                                }
                            }
                        }
                    }
                }
                .head // FIXME: Remove this and move logic to service
          }
        }
      }
    }


  //New inbox API and has LinkedIn Flow
  def changeSnoozedStatusOfEmailThreadV2(v: String, aid: Option[Long], tid: Option[Long], tiid: String, is_campaign_inbox_flow: Option[Boolean]) =
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CHANNELS,
      tidOpt = tid
    ).async(parse.json) {
      request =>

        Future {
          given Logger: SRLogger = request.Logger
          val Res = request.Response
          val account_id: AccountId = AccountId(request.loggedinAccount.internal_id)
          val teamId: TeamId = TeamId(request.actingTeamAccount.get.team_id)
          val org_id: OrgId = OrgId(request.loggedinAccount.org.id)
          val tidOpt: Option[TeamId] = request.actingTeamAccount
            .map(ta => TeamId(id = ta.team_id))


          if (tidOpt.isEmpty) {
            Res.ForbiddenError("Please send a valid team id")
          } else {

            val validateData = request.body.validate[InboxThreadSnoozeFormDataV2]

            validateData match {

              case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

              case JsSuccess(data, _) =>

                srUuidService.separateThreadUuidsByChannel(
                    uuids = data.team_inbox_conversations.flatMap(_.conversation_ids)
                  )
                  .map {
                    case (channelType, uuids) =>
                      srUuidService.convertThreadUuidsToIds(
                        threadUuids = uuids.toList,
                        teamId = teamId
                      ) match {
                        case Failure(e) =>
                          Res.ServerError("Error while converting threadUuids to id", Some(e))

                        case Success(uuidToIdOptMap) =>
                          val team_inbox_conversations_map: Map[Option[Long], List[Long]] = data.team_inbox_conversations.groupBy(_.team_inbox_id).map {
                            case (team_inbox_id, teamInboxIdsConvsList) =>
                              val threadIds = teamInboxIdsConvsList.flatMap(_.conversation_ids).map(
                                  uuidToIdOptMap(_)
                                )
                                .filter(_.isDefined)
                                .map(_.get)
                              (team_inbox_id, threadIds)
                          }.filter {
                            case (_, teamInboxIdsConvsList) => teamInboxIdsConvsList.nonEmpty
                          }

                          teamInboxService.validateTeamInboxesBelongToAccount(
                            account_id = account_id,
                            org_id = org_id,
                            team_id = teamId,
                            channelType = channelType,
                            team_inbox_ids = team_inbox_conversations_map.keys.toList,
                            is_campaign_inbox_flow = is_campaign_inbox_flow,
                            team_inbox_conversations_map = team_inbox_conversations_map,
                            permittedAccountIds = request.permittedAccountIds.toList
                          ) match {

                            case Left(ValidateInboxTypeAndGetEsetsError.DBFailure(e)) =>
                              Res.ServerError(e)

                            case Left(ValidateInboxTypeAndGetEsetsError.PermissionDenied(str)) =>
                              Res.ForbiddenError(str)

                            case Left(ValidateInboxTypeAndGetEsetsError.EmailSettingNotFound(str)) =>
                              Res.NotFoundError(str)

                            case Left(ValidateInboxTypeAndGetEsetsError.InvalidTeamInboxId(str)) =>
                              Res.NotFoundError(str)

                            case Left(ValidateInboxTypeAndGetEsetsError.EsetsEmptyForCampaignInbox) =>
                              Res.NotFoundError("Email account not found")

                            case Left(ValidateInboxTypeAndGetEsetsError.ErrorWhileGettingAllCampaignsAccess(err)) =>

                              err match {

                                case GetCampaignAccessError.DBError(e) =>

                                  Res.ServerError(e)

                                case GetCampaignAccessError.NoCampaignAccessFound =>
                                  Res.BadRequestError(CONSTANTS.API_MSGS.NO_CAMPAIGNS_ACCESS_FOUND_FROM_ALL_CAMPAIGNS_INBOX)

                                case GetCampaignAccessError.NoReceiverEmailSettingFound =>
                                  Res.BadRequestError(CONSTANTS.API_MSGS.NO_RECEIVER_SETTINGS_FOUND_FROM_ALL_CAMPAIGNS_INBOX)

                              }

                            case Left(ValidateInboxTypeAndGetEsetsError.CampaignInboxFlowCheckError(err)) =>
                              Logger.error(s"changeSnoozedStatusOfEmailThreadV2 :: TeamInboxIdsAre Empty account_id : $account_id, org_id : $org_id, team_id  $teamId, is_campaign_inbox_flow: ${is_campaign_inbox_flow}", err)
                              Res.BadRequestError(err.getMessage)

                            case Left(ValidateInboxTypeAndGetEsetsError.TeamInboxIdsAreEmpty) =>

                              Logger.error(s"changeSnoozedStatusOfEmailThreadV2 :: TeamInboxIdsAre Empty account_id : $account_id, org_id : $org_id, team_id  $teamId, is_campaign_inbox_flow: ${is_campaign_inbox_flow}")
                              Res.BadRequestError("There was an error. Please contact support.")

                            case Right(success_msg) =>

                              val snoozedTill: Option[DateTime] = if (data.snoozed_till.isDefined) Some(new DateTime(data.snoozed_till.get)) else None
                              val thread_ids: List[Long] = team_inbox_conversations_map.values.flatten.toList

                              inboxV3Service.changeSnoozeStatusOfConvInboxV3(
                                threadIds = thread_ids,
                                channelType = channelType,
                                snooze = data.is_snoozed,
                                snoozed_till = snoozedTill,
                                teamId = teamId,
                              ) match {

                                case Left(ChangeArchiveOrSnoozeStatusOfConvError.DBFailure(e)) =>
                                  Res.ServerError("Problem while snoozing the thread. Could you try again or contact support ? ", e = Some(e))

                                case Left(ChangeArchiveOrSnoozeStatusOfConvError.UnarchiveOrUnsnoozeFailed(_)) =>
                                  Res.NotFoundError("Problem while unsnooze. Could you try again or contact support ? ")

                                case Right(count) =>

                                  if (count == 0) {
                                    Res.NotFoundError("Email thread not found. Could you reload and try again ?")
                                  } else {
                                    Res.Success(s"Email successfully ${if (data.is_snoozed) "snoozed" else "unsnoozed"}", Json.obj("total_updated" -> count))
                                  }
                              }
                          }
                      }
                  }
                  .head // FIXME: Remove this and move logic to service

            }
          }
        }

    }


  /*
  NOTE: this is an Internal api request, only used for sending manual emails (as of now)
  from the sending mail server for that team/email account

  This is triggered from the "sendNewManualEmailV2" api below
   */
  def processSendMailRequest(v: String) = Action.async(parse.json) {
    request =>

      request.body.validate[EmailSendMailRequest] match {
        case JsError(errors) =>

          val Logger = new SRLogger(logRequestId = s"FATAL FATAL Inbox.processSendMailRequest : ${request.body} : ${request.host}: jserrors: $errors")
          val Res = new SRAPIResponse(Logger = Logger)

          Future.successful(
            Res.JsValidationError(errors)
          )

        case JsSuccess(data, _) =>
          given Logger: SRLogger= new SRLogger(logRequestId = s"Inbox.processSendMailRequest: ${data.log_request_id}")
          val Res = new SRAPIResponse(Logger = Logger)
          Logger.info("sending email")
          emailSenderService.sendEmailToProspect(
              data = data.email_send_detail,
              rep_smtp_reverse_dns_host = data.rep_mail_server_reverse_dns
            )
            .map(sentEmail => {
              Logger.success(s"sent email")

              Res.Success(s"Sent email", Json.toJson(sentEmail))
            })
            .recover { case e =>

              Logger.fatal(s"error while sending", err = e)

              Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + ": " + e.getMessage, e = Some(e))
            }
      }

  }

  // PROSPECTS_EMAILS_TODO_UPDATE
  /*
  * Use case : sendNewManualEmailV2 In inbox when user replies to email thread manually. PROSPECTS_EMAILS_TODO_READ_CLEANED
   */

  //TODO: shift logic in service
  //TODO: duplicate code in inboxV3service for new inbox
  //Old inbox API and LinkedIn Flow not required

  // FIXME: Drop This api Since We have sendNewManualEmailV3
  def sendNewManualEmailV2(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.SEND_MANUAL_EMAIL,
    tidOpt = tid
  ).async(parse.json) { request =>
    given Logger: SRLogger= request.Logger
    val Res = request.Response

    request.body.validate[SendNewManualEmailV2] match {

      case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

      case JsSuccess(data, _) =>

        data.email_message_id match {

          case None =>

            val toEmails = data.to.filter(_.email.trim.nonEmpty)
            val toEmailAddresses = toEmails.map(_.email.trim.toLowerCase).distinct

            if (toEmails.isEmpty) {
              Future.successful(
                Res.BadRequestError("Please provide valid To email address")
              )
            } else {

              val ta = request.actingTeamAccount.get
              val accountId = ta.user_id
              val teamId = ta.team_id
              val manualEmailFromSRInbox = true
              val orgId = OrgId(request.loggedinAccount.org.id)

              val permittedAccountIds = PermissionMethods.getPermittedAccountIdsForAccountAndPermission(
                loggedinAccount = request.loggedinAccount,
                actingTeamId = ta.team_id,
                actingAccountId = ta.user_id,
                version = v,
                Logger = Logger
              )

              val permittedAccountIdsForEmailSetting = permittedAccountIds(PermType.SEND_MANUAL_EMAIL) // fixme newinbox: check if this permission needed

              /**
               * fixme newinbox InboxV3: this check is just to verify how many users are sending to non-prospect emails
               * should be removed with InboxV3
               */
              val toProspectIdsCheckF: Future[Seq[ProspectIdEmail]] = Future.fromTry(
                prospectServiceV2.findByEmail(
                  logger = Logger,
                  emails = toEmailAddresses,
                  teamId = teamId
                )
              ).map { pids =>

                if (pids.length != toEmails.length) {
                  val existingProspectEmails = pids.map(_.email.toLowerCase.trim)
                  val nonProspectToEmails = toEmailAddresses.filterNot(em => existingProspectEmails.contains(em.trim.toLowerCase))
                  Logger.info(s"inboxv3 check: manual reply: toEmails are not prospects already: nonProspectToEmails: $nonProspectToEmails :: existingProspectEmails: $existingProspectEmails")
                }

                pids
              }

              /**
               * check if user has permission to send from the "sender_email_setting_id" email-account
               */
              val senderEmailSettingF = Future.fromTry {
                emailService.getSenderEmailSettingForManualEmailTask(
                  sender_email_setting_id = EmailSettingId(data.sender_email_setting_id),
                  permittedAccountIdsForEmailSetting = permittedAccountIdsForEmailSetting,
                  team_id = TeamId(teamId)
                )
              }

              /**
               * check if user has permission to use the receive-replies-to inbox
               *
               * FIXME: currently SEND_MANUAL_EMAIL permission is being checked for this also
               */
              val replyToEmailSettingF = if (
                data.receiver_email_setting_id.isEmpty ||
                  data.receiver_email_setting_id.get == data.sender_email_setting_id
              ) {
                senderEmailSettingF
              } else Future {
                emailSettingDAO.find(id = EmailSettingId(data.receiver_email_setting_id.get), teamId = TeamId(teamId)) match {
                  case None => throw new APINotFoundException(s"Reply-to email account not found [${data.receiver_email_setting_id.get}]")
                  case Some(em) =>
                    if (!permittedAccountIdsForEmailSetting.contains(em.owner_id.id)) { // FIXME VALUECLASS

                      throw new APIBadRequestException("You do not have the permission for the selected reply-to email")

                    } else {
                      em
                    }
                }
              }

              /**
               * check if email thread belongs to the team
               *
               * FIXME: I think we should check whether user has permission to send from the inbox associated with this thread
               */
              val emailThreadF: Future[Option[ThreadForProspectInternal.EmailThreadForProspectInternal]] = Future {
                data.email_thread_id.flatMap(threadId => {
                  emailThreadDAOService.findByIdInternalV3(
                    id = threadId,
                    teamId = TeamId(teamId),
                    inboxEmailSettingId = Some(data.sender_email_setting_id)
                  ) match {
                    case None =>

                      /**
                       * DO NOT throw here
                       *
                       * its a valid case, where the user may change the From Email while
                       * forwarding / replying emails
                       *
                       * we will just send such emails in a new thread, rather than throwing the
                       * error that Thread was not found
                       */
                      // throw new APINotFoundException(s"Email conversation not found [$threadId]")
                      None

                    case Some(em) =>
                      if (em.team_id != teamId) {
                        throw new APINotFoundException(s"Email conversation not found [$threadId.2]")
                      } else {
                        Some(em)
                      }
                  }

                })
              }

              val allTrackingDomainsUsedF = Future.fromTry {
                repTrackingHostService.getRepTrackingHosts()
              }.map(_.map(_.host_url))

              /**
               * if user has selected, the "Mark as done & Send" option, then archive the thread
               */
              val archiveThreadF: Future[Int] = emailThreadF.map(thread => {
                if (
                  thread.isDefined &&
                    data.mark_as_done.getOrElse(false)
                ) {
                  val archivedThreads: Try[Int] = inboxV3Service.changeArchiveStatusOfConvInboxV3(
                    threadIds = Seq(thread.get.id),
                    channelType = ChannelType.EmailChannel,
                    archive = true,
                    teamId = TeamId(id = teamId)
                  ) match {
                    case Left(_) => Failure(new Exception("changeArchiveStatusOfConvInboxV3 failed"))
                    case Right(result) => Success(result)
                  }

                  archivedThreads.get
                } else {
                  0
                }
              })


              val sendEmailStepByStep: Future[DBEmailMessagesSavedResponse] = for {

                toProspectIdsCheckDone: Seq[ProspectIdEmail] <- toProspectIdsCheckF

                senderEmail <- senderEmailSettingF

                replyToEmail <- replyToEmailSettingF

                emailThread: Option[ThreadForProspectInternal.EmailThreadForProspectInternal] <- emailThreadF

                replyingToMessage: Option[EmailScheduled] <- Future {
                  emailThread.map(thread => {
                    emailScheduledDAO.findEmailToReplyToV3(emailThreadId = thread.id, teamId = TeamId(id = teamId)) match {
                      case Failure(e) => throw e
                      case Success(None) => throw new APINotFoundException(s"Email conversation message not found [${thread.id}]")
                      case Success(Some(msg)) => msg
                    }
                  })
                }

                allTrackingDomainsUsed: Seq[String] <- allTrackingDomainsUsedF

                emailContent: EmailServiceBody <- Future.fromTry(
                  emailService.getBodiesForInboxMessage(

                    // fixme newinbox oldinbox also: open/click tracking arent going to work with emailschduledid as 0
                    emailsScheduledUuid = EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),

                    custom_tracking_domain = senderEmail.custom_tracking_domain,
                    default_tracking_domain = senderEmail.default_tracking_domain,

                    body = data.body,
                    subject = data.subject,

                    trackOpens = data.enable_open_tracking.getOrElse(false),
                    trackClicks = data.enable_click_tracking.getOrElse(false),

                    allTrackingDomainsUsed = allTrackingDomainsUsed,
                    teamId = TeamId(teamId),
                    orgId = orgId
                  )
                )


                sentEmail: EmailToBeSent <- {

                  val allCC: Seq[IEmailAddress] = {

                    val fromInput: Seq[IEmailAddress] = data.cc_emails.getOrElse(Seq())
                    val fromEmailSetting: Seq[IEmailAddress] = if (senderEmail.cc_emails.isEmpty) Seq() else {

                      IEmailAddress.parse(
                        emailsStr = senderEmail.cc_emails.get)(
                        logger = Logger
                      ) match {
                        case Failure(e) =>

                          Logger.fatal(s"failure (IGNORING) while parsing ccEmails: ${senderEmail.cc_emails}", err = e)
                          Seq()

                        case Success(ccEmails) =>
                          ccEmails
                      }
                    }

                    val cc_emails: Seq[IEmailAddress] = if (fromInput.isEmpty) fromEmailSetting
                    else if (fromEmailSetting.isEmpty) fromInput
                    else fromInput ++ fromEmailSetting

                    cc_emails
                  }

                  val allBCC: Seq[IEmailAddress] = {

                    val fromInput: Seq[IEmailAddress] = data.bcc_emails.getOrElse(Seq())

                    val fromEmailSetting: Seq[IEmailAddress] = if (senderEmail.bcc_emails.isEmpty) Seq() else {

                      IEmailAddress.parse(
                        emailsStr = senderEmail.bcc_emails.get)(
                        logger = Logger
                      ) match {
                        case Failure(e) =>

                          Logger.fatal(s"failure (IGNORING) while parsing bccEmails: ${senderEmail.bcc_emails}", err = e)
                          Seq()

                        case Success(bccEmails) =>
                          bccEmails
                      }
                    }

                    val bcc_emails: Seq[IEmailAddress] = if (fromInput.isEmpty) fromEmailSetting
                    else if (fromEmailSetting.isEmpty) fromInput
                    else fromInput ++ fromEmailSetting

                    bcc_emails
                  }

                  val sendEmailFromCampaignDetails = emailThread.flatMap(_.campaign_id).map(
                    campaignId => {
                      SendEmailFromCampaignDetails(
                        campaign_id = campaignId,
                        campaign_name = replyingToMessage.map(_.sendEmailFromCampaignDetails.get.campaign_name).get,
                        stepDetails = None
                      )
                    }
                  )


                  val sendData: EmailSendMailRequest = EmailSendMailRequest(

                    log_request_id = Logger.logRequestId,

                    rep_mail_server_reverse_dns = senderEmail.rep_mail_server_reverse_dns,

                    email_send_detail = EmailSendDetail(

                      sender_email_settings_id = senderEmail.id.get.emailSettingId, // FIXME VALUECLASS
                      receiving_email_settings_id = replyToEmail.id.get.emailSettingId, // FIXME VALUECLASS
                      sender_message_id_suffix = senderEmail.message_id_suffix,

                      id = 0,
                      org_id = request.loggedinAccount.org.id,
                      prospect_id = None, // only used to generate smtp message-ids: we anyways are randomizing that with the current timestamp
                      team_id = teamId,
                      account_id = accountId,
                      sendEmailFromCampaignDetails = sendEmailFromCampaignDetails,

                      subject = emailContent.subject,
                      body = emailContent.htmlBody,
                      text_body = emailContent.textBody,

                      scheduled_from_campaign = false,

                      service_provider = senderEmail.service_provider,
                      via_gmail_smtp = senderEmail.via_gmail_smtp,

                      smtp_username = senderEmail.smtp_username,
                      smtp_host = senderEmail.smtp_host,
                      smtp_port = senderEmail.smtp_port,
                      smtp_password = senderEmail.smtp_password,
                      oauth2_refresh_token = senderEmail.oauth2_refresh_token,
                      oauth2_access_token = senderEmail.oauth2_access_token,
                      oauth2_access_token_expires_at = senderEmail.oauth2_access_token_expires_at,
                      custom_tracking_domain = senderEmail.custom_tracking_domain,
                      rep_tracking_host_id = senderEmail.rep_tracking_host_id,
                      gmail_fbl = None,
                      list_unsubscribe_header = None,

                      // for mailgun
                      email_domain = senderEmail.email_domain,
                      api_key = senderEmail.api_key,
                      mailgun_region = senderEmail.mailgun_region,


                      from_email = senderEmail.email,
                      from_name = senderEmail.sender_name,

                      // replies should come to the same inbox from which admin manually sends this message
                      reply_to_email = None,
                      reply_to_name = None,

                      to_emails = data.to,

                      cc_emails = allCC,

                      bcc_emails = allBCC,

                      in_reply_to_id = replyingToMessage.flatMap(_.message_id),
                      in_reply_to_references_header = replyingToMessage.flatMap(_.references_header),
                      in_reply_to_sent_at = replyingToMessage.flatMap(_.sent_at),
                      in_reply_to_subject = replyingToMessage.flatMap(_.subject),
                      in_reply_to_outlook_msg_id = replyingToMessage.flatMap(_.outlook_msg_id),

                      email_thread_id = replyingToMessage.flatMap(_.email_thread_id),
                      gmail_thread_id = emailThread.flatMap(_.gmail_thread_id),
                      send_plain_text_email = Some(false), //  SEND_PLAIN_TEXT_EMAIL_FIXME: this is okay since this is not a campaign flow

                      sender_email_setting_paused_till = None
                    )
                  )


                  // TODO: use EmailService.sendEmailViaSpecificHost instead of the block below

                  val sendingUrl = senderEmail.rep_mail_server_host + "/api/v2/inbox/p_s_m_1"

                  // for testing: val sendingUrl = "http://localhost:9000/api/v2/inbox/p_s_m_1"

                  val v: Future[EmailToBeSent] = wSClient.url(sendingUrl)
                    .post(Json.toJson(sendData))
                    .map(res => {

                      if (res.status == 200) {

                        val sentResponseData = (res.json \ "data")

                        sentResponseData.validate[EmailToBeSent] match {
                          case JsError(errors) =>
                            Logger.fatal(s"FATAL APIManualEmailSentButSavingFailedException external api response failure: errors: $errors : resBody: ${res.body}")
                            throw new APIManualEmailSentButSavingFailedException(s"Email successfully sent but failed while saving to your account")


                          case JsSuccess(sentEmailData, _) =>
                            sentEmailData
                        }

                      } else {

                        Logger.doNotTruncate(s"FATAL:: send_manual_email 123 :: sendingUrl: $sendingUrl :: resbody: ${res.body} :: sendData: $sendData")

                        throw new APIBadRequestException(
                          "Sending failed. Your email account is not connected properly. Please reconnect, or contact support"
                        )

                      }

                    })
                    .recover { case e => {

                      Logger.fatal(s"FATAL:: send_manual_email 2:: sendingUrl: $sendingUrl :: sendData: $sendData", err = e)

                      throw e
                    }
                    }
                  v
                }

                savedEmail: DBEmailMessagesSavedResponse <- Future {

                  val toEmails = sentEmail.to_emails

                  val ccEmailAddrs = sentEmail.cc_emails
                  val dateTimeNow = DateTime.now

                  val email = EmailMessageTracked(
                    inbox_email_setting_id = senderEmail.id.get.emailSettingId, // FIXME VALUECALSS
                    from = IEmailAddress(
                      email = senderEmail.email,
                      name = Some(sentEmail.from_name)
                    ),

                    to_emails = toEmails,
                    subject = sentEmail.subject,

                    body = sentEmail.htmlBody,

                    base_body = emailContent.baseBody,
                    text_body = sentEmail.textBody,

                    prospect_id_in_campaign = None,
                    campaign_id = emailThread.flatMap(_.campaign_id),
                    campaign_name = emailThread.flatMap(_.campaign_name),
                    step_id = None,
                    step_name = None,

                    references_header = sentEmail.references_header,

                    prospect_account_id_in_campaign = None,
                    received_at = dateTimeNow,
                    recorded_at = dateTimeNow,

                    email_status = EmailReplyStatus.getEmailReplyStatusBeforeSaving(
                      emailBaseBody = sentEmail.htmlBody,
                      subject = sentEmail.subject,
                      fromEmail = senderEmail.email,
                      fromName = sentEmail.from_name,
                      toEmailAddresses = toEmails.map(_.email),
                      byAccount = true,
                      fullHeaders = Json.obj(), // doesnt matter
                      received_at = dateTimeNow
                    ).copy(

                      // manual sent emails should always be set as Not_categorized
                      replyType = EmailReplyType.NOT_CATEGORIZED
                    ),

                    message_id = if (sentEmail.message_id.isDefined) sentEmail.message_id.get else "",
                    full_headers = Json.obj(),

                    scheduled_manually = true,
                    sr_inbox_read = true,

                    reply_to = sentEmail.reply_to_email.map(replyToAddress => {
                      IEmailAddress(
                        email = replyToAddress,
                        name = sentEmail.reply_to_name
                      )
                    }),

                    email_thread_id = sentEmail.email_thread_id,
                    gmail_msg_id = sentEmail.gmail_msg_id,
                    gmail_thread_id = sentEmail.gmail_thread_id,

                    outlook_msg_id = sentEmail.outlook_msg_id,
                    outlook_conversation_id = sentEmail.outlook_conversation_id,
                    outlook_response_json = sentEmail.outlook_response_json,

                    cc_emails = ccEmailAddrs,
                    in_reply_to_header = sentEmail.in_reply_to_id,
                    original_inbox_folder = None,

                    internal_tracking_note = InternalTrackingNote.NONE,

                    tempThreadId = Some(0),

                    team_id = teamId,
                    account_id = accountId
                  )

                  // PROSPECTS_EMAILS_TODO_READ_CLEANED
                  emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
                    accountId = accountId,
                    team_id = teamId,
                    emailMessages = Seq(email),
                    replyHandling = senderEmail.reply_handling,

                    inboxEmailSetting = senderEmail,
                    senderEmails = Seq(senderEmail.email),

                    adminReplyFromSRInbox = manualEmailFromSRInbox,
                    account = request.loggedinAccount,
                    auditRequestLogId = request.auditRequestLogId,
                    markProspectAsCompleted = manualEmailFromSRInbox
                    //NOTE:
                    // manualEmailFromSRInbox is hardcoded to true in this flow.
                    // This is hardcoded because it is coming via the manual email flow ,and
                    // for any manual intervention we mark prospect as completed

                  ) match {

                    case Failure(e) =>

                      Logger.fatal(s"APIManualEmailSentButSavingFailedException: Email successfully sent but failed while saving to your account 2 : ${LogHelpers.getStackTraceAsString(e)}")
                      throw new APIManualEmailSentButSavingFailedException(s"Email successfully sent but failed while saving to your account", e)

                    case Success(savedReplies) =>
                      savedReplies

                  }
                }

                archived: Int <- archiveThreadF

              } yield {

                savedEmail

              }

              sendEmailStepByStep
                .map { sentAndSavedEmail =>
                  if(data.task_id.isDefined) {
                      val currentTime = DateTime.now()
                      taskPgDAO.changeStatus(
                          task_id = data.task_id.get,
                          changeTime = currentTime,
                          task_status = QueuedToMq(),
                          team_id = teamId,
                          changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
                            doer = Some(request.loggedinAccount.internal_id),
                            permittedAccountIds = request.permittedAccountIds
                          )
                      ).map { _=>
                          val taskData: TaskStatusChangesData = TaskStatusChangesData(
                              doer = request.loggedinAccount.internal_id,
                              task_id = data.task_id,
                              task_status = Done(),
                              orgId = orgId,
                              team_id = teamId,
                              permittedAccountIds = request.permittedAccountIds
                          )


                          mqUpdateManualEmailTaskStatus.publish(taskData)
                      }
                      Res.Success("Email is being sent")
                  }else {
                      // FIXME newinbox webhooks should be called: sent / completed etc.

                      Res.Success("Email sent", Json.obj())
                  }

                }
                .recover {

                  case e: APINotFoundException =>
                    Res.NotFoundError(Option(e.getMessage).getOrElse(""))

                  case e: APIBadRequestException =>
                    Res.BadRequestError(Option(e.getMessage).getOrElse(""))

                  case e: templateService.InvalidTemplateSyntaxException =>
                    Res.BadRequestError(Option(e.getMessage).getOrElse(""))

                  case e: templateService.InvalidMergeTagException =>
                    Res.BadRequestError(Option(e.getMessage).getOrElse(""))

                  case e: APIManualEmailSentButSavingFailedException =>
                    Res.BadRequestError(Option(e.getMessage).getOrElse(""))

                  case e =>
                    Logger.fatal("FATAL while sending manual email", err = e)
                    Res.ServerError(e)

                }

            }
          /*
          28-august-2024:

            id, is email_message_id which is coming from extension flow for manual emails which are scheduled by campaigns.


           */
          case Some(id) =>

            /*
               0. update the email_scheduled_body

               1. email -> ( emails || task )

               2. fetch email service provider and pass it here.

               3. emailSettingDAO.find(and pass it here) from here service provider and rep_smtp_reverse_dns_host will come
            */

            val ta = request.actingTeamAccount.get

            if(tid.get == 8835L || tid.get == 20489L){
              Logger.doNotTruncate(s"data passed to api : ${data.body}")
            }

            
            inboxSendService.sendEmailFromExtension(
              data = data,
              permittedAccountIdsForEmailSetting = request.permittedAccountIds,
              team_id = TeamId(id = tid.get)
            ).map(rs => {

                if(data.task_id.isDefined) {


                    val currentTime = DateTime.now()
                    taskPgDAO.changeStatus(
                        task_id = data.task_id.get,
                        changeTime = currentTime,
                        task_status = QueuedToMq(),
                        changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
                          doer = Some(request.loggedinAccount.internal_id),
                          permittedAccountIds = request.permittedAccountIds
                        ),
                        team_id = ta.team_id
                    ).map { _ =>
                        val taskData: TaskStatusChangesData = TaskStatusChangesData(
                            doer = request.loggedinAccount.internal_id,
                            task_id = data.task_id,
                            task_status = Done(),
                            orgId = OrgId(request.loggedinAccount.org.id),
                            team_id = ta.team_id,
                            permittedAccountIds = request.permittedAccountIds
                        )


                        mqUpdateManualEmailTaskStatus.publish(taskData)
                    }
                    Res.Success("Email is being sent")
                }else {

                    Res.Success("Email Sent")
                }


            }).recover(err => {

              Logger.error(s"sendNewManualEmailV2 Error While sending email :: TeamId : ${tid} :: sender_email_setting_id: ${data.sender_email_setting_id} ", err)

              Res.BadRequestError("Error while sending email. Please try again or contact support.")

            })

        }

    }
  }

  //New inbox API and LinkedIn Flow not required as it's email specific
  def sendNewManualEmailV3(v: String, aid: Option[Long], tid: Option[Long], tiid: Option[String]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.SEND_MANUAL_EMAIL, 
    tidOpt = tid
  ).async(parse.json) { request =>

    given Logger: SRLogger = request.Logger.appendLogRequestId(s" teamInboxId_${tiid} :: ")

    val Res = request.Response

    request.body.validate[SendNewManualEmailV3] match {

      case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

      case JsSuccess(data, _) =>

        val ta = request.actingTeamAccount.get

        inboxV3Service.sendNewEmailManually(
          data = data,
          org_id = request.loggedinAccount.org.id,
          accountId = ta.user_id,
          teamId = ta.team_id,
          loggedinAccount = request.loggedinAccount,
          auditRequestLogId = request.auditRequestLogId,
          permittedAccountIds = request.permittedAccountIds,
          version = v,
          tiid = tiid
        ).map {

          case Left(SendNewEmailManuallyError.ValidateInboxError(e)) =>

            e match {

              case ValidateSendNewEmailManuallyError.ValidateInboxTypeError(er) =>

                er match {

                  case ValidateInboxTypeAndGetEsetsError.DBFailure(err) =>
                    Res.ServerError(err)

                  case ValidateInboxTypeAndGetEsetsError.PermissionDenied(str) =>
                    Res.ForbiddenError(str)

                  case ValidateInboxTypeAndGetEsetsError.EmailSettingNotFound(str) =>
                    Res.NotFoundError(str)

                  case ValidateInboxTypeAndGetEsetsError.InvalidTeamInboxId(str) =>
                    Res.NotFoundError(str)

                  case ValidateInboxTypeAndGetEsetsError.EsetsEmptyForCampaignInbox =>
                    Res.NotFoundError("Email account not found")

                  case ValidateInboxTypeAndGetEsetsError.ErrorWhileGettingAllCampaignsAccess(err) =>

                    err match {

                      case GetCampaignAccessError.DBError(e) =>

                        Res.ServerError(e)

                      case GetCampaignAccessError.NoCampaignAccessFound =>
                        Res.BadRequestError(CONSTANTS.API_MSGS.NO_CAMPAIGNS_ACCESS_FOUND_FROM_ALL_CAMPAIGNS_INBOX)

                      case GetCampaignAccessError.NoReceiverEmailSettingFound =>
                        Res.BadRequestError(CONSTANTS.API_MSGS.NO_RECEIVER_SETTINGS_FOUND_FROM_ALL_CAMPAIGNS_INBOX)

                    }

                  case ValidateInboxTypeAndGetEsetsError.CampaignInboxFlowCheckError(err) =>
                    Logger.error(s"sendNewManualEmailV3 :: CampaignInboxFlowCheckError account_id : ${request.loggedinAccount.internal_id}, org_id : ${request.loggedinAccount.org.id}, team_id  ${request.actingTeamAccount.get.team_id}, tiid: ${tiid}", err)
                    Res.BadRequestError(err.getMessage)

                  case ValidateInboxTypeAndGetEsetsError.TeamInboxIdsAreEmpty =>

                    Logger.error(s"sendNewManualEmailV3 :: TeamInboxIdsAre Empty account_id : ${request.loggedinAccount.internal_id}, org_id : ${request.loggedinAccount.org.id}, team_id  ${request.actingTeamAccount.get.team_id}, tiid: ${tiid}")
                    Res.BadRequestError("There was an error. Please contact support.")


                }

              case ValidateSendNewEmailManuallyError.CampaignStepIdAndTIIDBothDefinedError =>

                Res.BadRequestError("Campaign step id and team inbox id both present")

              case ValidateSendNewEmailManuallyError.ToEmailEmpty(str) =>

                Res.BadRequestError(str)

              case ValidateSendNewEmailManuallyError.InvalidInboxIdError(str) =>

                Res.BadRequestError(str)

            }

          case Left(SendNewEmailManuallyError.ToEmailEmpty(str)) => Res.BadRequestError(str)

          case Left(SendNewEmailManuallyError.ApiNotFound(str)) => Res.NotFoundError(str)

          case Left(SendNewEmailManuallyError.ApiBadRequest(str)) => Res.BadRequestError(str)

          case Left(SendNewEmailManuallyError.InvalidTemplateSyntax(str)) => Res.BadRequestError(str)

          case Left(SendNewEmailManuallyError.InvalidMergeTag(str)) => Res.BadRequestError(str)

          case Left(SendNewEmailManuallyError.APIManualEmailSentButSavingFailed(str)) => Res.BadRequestError(str)

          case Left(SendNewEmailManuallyError.FatalWhileSending(e)) => Res.ServerError(e)

          case Right(success_meg) => Res.Success(success_meg, Json.obj())
        }
    }

  }


  // POST     /api/:v/inbox/conversations/get_inbox_draft
  // USED IN FE - prospect popout compose new email
  def getInboxDraft(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.JUST_LOGGEDIN, // ignore send_manual_email permission
    tidOpt = tid
  ).async(parse.json) { request =>
    given Logger: SRLogger= request.Logger
    val Res = request.Response

    request.body.validate[GenerateInboxDraft] match {

      case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

      case JsSuccess(data, _) =>

        val ta = request.actingTeamAccount.get
        val teamId = ta.team_id

        val permittedAccountIds = PermissionMethods.getPermittedAccountIdsForAccountAndPermission(
          loggedinAccount = request.loggedinAccount,
          actingTeamId = ta.team_id,
          actingAccountId = ta.user_id,
          version = v,
          Logger = Logger
        )


        val senderEmailSettingF = Future {
          emailSettingDAO.find(id = EmailSettingId(data.sender_email_setting_id), teamId = TeamId(teamId)) match {

            case None => throw new APINotFoundException(s"Sender email account not found [${data.sender_email_setting_id}]")

            case Some(em) =>
              em
          }
        }


        val prospectF = if (data.to_email.isEmpty || data.to_email.get.trim.isEmpty) {

          Future.successful(ProspectObject.dummyProspectObj)

        } else {

          Future {
            // PROSPECTS_EMAILS_TODO_READ_CLEANED
            prospectDAOService.find(
              byProspectEmails = Seq(data.to_email.get.trim),
              teamId = teamId,
              Logger = Logger
            ) match {
              case Failure(e) => throw e
              case Success(prospectSeq) =>

                val p = prospectSeq.headOption

                if (p.isEmpty) {

                  ProspectObject.dummyProspectObj
                    .copy(
                      email = Some(data.to_email.get.trim) //TODO: EMAIL_OPTIONAL check the path and test code (dummyProspectObj is having Some("") for email)
                    )


                } else if (!permittedAccountIds(PermType.EDIT_PROSPECTS).contains(p.get.owner_id)) {

                  throw new APIBadRequestException("You do not have the permission for this prospect")

                } else {
                  p.get
                }

            }
          }
        }

        val sendEmailStepByStep = for {

          senderEmail <- senderEmailSettingF

          prospect <- prospectF

          prospectAccount <- if (prospect.id == 0) {
            // dummy prospect obj
            Future.successful(None)
          } else {
            Future {
              prospectAccountDAO.find(id = prospect.id, teamId = prospect.team_id)
            }
          }

          emailContent <- Future.fromTry(
            emailService.generateDraftForInbox(

              signature = Some(senderEmail.signature),

              bodyTemplate = data.body_template,
              subjectTemplate = data.subject_template,

              email_sender_name = senderEmail.sender_name,
              sender_first_name = senderEmail.first_name,
              sender_last_name = senderEmail.last_name,
              prospect = prospect,
              prospectAccount = prospectAccount,

              applying_email_template = data.applying_email_template
            )
          )

        } yield {

          emailContent

        }

        sendEmailStepByStep
          .map { case (body, subject) =>

            Res.Success(

              "Draft email", Json.obj(
                "body" -> body,
                "subject" -> subject
              )
            )
          }
          .recover { case e =>
            Res.ServerError("Error while fetching email draft", e = Some(e)
            )
          }

    }

  }

  //New inbox API and LinkedIn Flow not required
  def getInboxDraftV2(v: String, aid: Option[Long], tid: Option[Long], tiid: String) = permissionUtils.checkPermission(
      version = v,
      permission = PermType.JUST_LOGGEDIN, // ignore send_manual_email permission
      tidOpt = tid
    )
    .andThen(hasTeamInboxAccessV2(
      version = v,
      teamInboxId = tiid
    ))
    .async(parse.json) { request =>

      given Logger: SRLogger= request.Logger
      val Res = request.Response

      request.body.validate[GenerateInboxDraft] match {

        case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

        case JsSuccess(data, _) =>

          val ta = request.actingTeamAccount.get
          val teamId = ta.team_id

          inboxV3Service.getDraftForTeamInbox(
            data = data,
            teamId = teamId,
            Logger = Logger
          ).map(result => {
            result match {
              case Left(GetDraftForTeamInboxError.ErrorWhileFetchingDraft(e)) =>
                Res.ServerError("Error while fetching email draft", e = Some(e)
                )
              case Right((body, subject)) =>
                Res.Success(

                  "Draft email", Json.obj(
                    "body" -> body,
                    "subject" -> subject
                  )
                )
            }
          })
      }

    }


  //New inbox API and has LinkedIn Flow
  def getInboxesInboxV3(
                         v: String,
                         tid: Option[Long]
                       ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_CHANNELS,
    tidOpt = tid

  ).async { request =>

    Future {
      val Logger = request.Logger
      val Res = request.Response

      val loggedInAccountId = request.loggedinAccount.internal_id

      /**
       * fixme InboxV3: unread count below
       */


      teamInboxService.getTeamInboxesWithAccess(
        account_id = loggedInAccountId,
        teamId = tid.get
      ) match {
        case Failure(exception) =>
          Res.ServerError("Error while fetching the team inboxes", Some(exception))

        case Success(team_inboxes) =>

          val team_inboxes_folders = team_inboxes.map(t => InboxV3Folder(
            text_id = t.name,
            email_setting_id = t.email_setting_id,
            inbox_name = t.name,
            unread = -1,
            team_inbox_id = Some(t.id),
            email = Some(t.email),
            initial_sync_required = t.initial_sync_required,
            initial_sync_data = t.initial_sync_data
          ))

          //todo: get unread count for all inboxes
          val myFolders = Seq(
            InboxV3Folder(
              text_id = "inbox",
              email_setting_id = None,
              inbox_name = "Inbox",
              unread = -1,
              initial_sync_required = false,
              initial_sync_data = None
            ),

            InboxV3Folder(
              text_id = "done",
              email_setting_id = None,
              inbox_name = "Done",
              unread = -1,
              initial_sync_required = false,
              initial_sync_data = None
            ),


            InboxV3Folder(
              text_id = "snoozed",
              email_setting_id = None,
              inbox_name = "Snoozed",
              unread = -1,
              initial_sync_required = false,
              initial_sync_data = None
            ),

            InboxV3Folder(
              text_id = "sent",
              email_setting_id = None,
              inbox_name = "Sent",
              unread = -1,
              initial_sync_required = false,
              initial_sync_data = None
            )
          )


          Res.Success("Inbox folders found", Json.obj(
            "my_folders" -> myFolders,
            "specific_inboxes" -> team_inboxes_folders
          ))
      }


    }

  }

  case class NavigationLinksCntrl(
                                   prev: Option[String],
                                   next: Option[String]
                                 )

  object NavigationLinksCntrl {
    implicit val writes: OWrites[NavigationLinksCntrl] = Json.writes[NavigationLinksCntrl]
  }

  case class ConvSummaryResponseCntrl(
                                       data: List[ConversationObjectInboxV3],
                                       links: NavigationLinksCntrl,
                                       show_upgrade_inbox_prompt: Boolean = false,
                                       `object`: String = "list" // ,
                                       //`type`: String = ConvSummaryResponseCntrl.getClass.toString
                                     )

  object ConvSummaryResponseCntrl {
    implicit val writes: OWrites[ConvSummaryResponseCntrl] = Json.writes[ConvSummaryResponseCntrl]
  }


  //New inbox API and has LinkedIn Flow
  def getConversationsForProspectsInboxV3(
                                           v: String,
                                           inbox_id: String, //team_inbox only
                                           tiid: String, //team_inbox_id
                                           folder_id: Option[String], //done, snoozed, prospect, non-prospect, sent, irrelevant
                                           prospect_category: Option[Long],
                                           older_than: Option[Long],
                                           newer_than: Option[Long],
                                           page_size: Option[Int],
                                           tid: Option[Long],
                                           rs_id: Option[String],
                                           email_setting_id: Option[Long],
                                           prospect_id: Option[Long],
                                           prospect_account_id: Option[Long],
                                           campaign_id: Option[Long]
                                         ): Action[AnyContent] =
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.VIEW_CHANNELS,
      tidOpt = tid).andThen(hasTeamInboxAccessV2(
      version = v,
      teamInboxId = tiid,
      allow_for_consolidated_inbox = true,
      email_setting_id = email_setting_id.map(EmailSettingId(_)),
      prospect_id = prospect_id.map(ProspectId(_)),
      prospect_account_id = prospect_account_id.map(ProspectAccountsId(_)),
      campaign_id = campaign_id.map(CampaignId(_))
    )) { (request: TeamInboxAccessRequestV2[AnyContent]) =>
      given Logger: SRLogger = request.Logger
      val Res = request.Response

      val tm = request.actingTeamAccount.get
      val org_plan_id = request.loggedinAccount.org.plan.plan_id
      val org_id = request.loggedinAccount.org.id

      val res = inboxV3Service.validateGetConversationsRequest(
        esets = request.esets,
        folder_id = folder_id,
        prospect_category = prospect_category,
        older_than = older_than,
        newer_than = newer_than,
        page_size = page_size,
        rs_id = rs_id,
        inbox_type_data = request.inbox_type_data
      )

      res match {
        case Left(str) =>
          Res.BadRequestError(str)

        case Right(validatedConvReq: ValidatedConvReq.ValidatedMailboxReqRange) =>
          val convs = inboxV3PaginationService.handleGetConversationsForProspectsInboxV3New(
            team_id = tm.team_id,
            channelType = request.channelType,
            validatedConvReq = validatedConvReq,
            org_plan_id = org_plan_id,
            show_full_inbox_v3 = request.loggedinAccount.org.org_metadata.show_whole_inbox_v3.getOrElse(false),
            getLinkedinConversations = request.loggedinAccount.org.org_metadata.show_linkedin_inbox.getOrElse(false),
            org_id = org_id
          )

          convs match {

            case Left(InboxV3ServiceGetConversationsError.AbsurdAtSameTimeConvsNotFound(error)) =>
              Res.ServerError(err = new Exception(error))

            case Left(InboxV3ServiceGetConversationsError.DBFailure(ex)) =>
              Logger.error(s"INBOX_ALERT: DBError while getting inbox emails", ex)
              Res.ServerError(err = ex)

            case Left(InboxV3ServiceGetConversationsError.GetIdFromUuidFailure(e)) =>
              Logger.error(s"INBOX_ALERT: GetIdFromUuidFailure", e)
              Res.BadRequestError("Please contact support")

            case Left(InboxV3ServiceGetConversationsError.ShouldNotThrow(errMsg)) =>
              Logger.error(s"INBOX_ALERT: $errMsg")
              Res.BadRequestError("Please contact support")

            case Right(value) =>

              val uri = Url.parse(request.uri)
              val page_data = value.links

              val next_link = getNextLinkForConvThread(uri, page_data)
              val prev_link = getPrevLinkForConvThread(uri, page_data)

              val resp = ConvSummaryResponseCntrl(
                data = value.convs,
                links = NavigationLinksCntrl(
                  prev = prev_link,
                  next = next_link
                ),
                show_upgrade_inbox_prompt = value.show_upgrade_inbox_prompt
              )

              Res.Success("Inbox conversations found", Json.toJson(resp))

          }
      }
    }



  /*
     // val possibleFolder = MailboxFolder.from(folder_id = folder_id, inbox_id = inbox_id, eset = eset)

        //if (request.actingTeamAccount.isEmpty)
      if(eset_check_result.isFailure){
        Res.ForbiddenError("You dont have permission to access this")
      } else if (tid.isEmpty ) {
        Res.ForbiddenError("You dont have permission to view this")
      }  else if (possibleFolder.isFailure) {
        Res.BadRequestError("Combination of inbox_id and folder_id is invalid")
      } else {
          val folderCleaned = possibleFolder.get

        val team_id = request.actingTeamAccount.get.team_id
        val permittedOwnerIds = request.permittedAccountIds
        //val team_id = tid.get
        //val permittedOwnerIds = Seq(3987L)

        //val inboxV3Service = new InboxV3Service(emailThreadDAO = emailThreadDAO)
        val convs = inboxV3Service.handleGetConversationsForProspectsInboxV3(
          team_id = team_id,
          folder = folderCleaned,
          prospect_category_custom = prospect_category,
          older_than = older_than,
          newer_than = newer_than,
          permittedOwnerIds =  permittedOwnerIds,
          page_size = page_size,
          Logger = Logger
        )

        convs match {
          case Right(value) =>

            val uri = Url.parse(request.uri)
            val page_data = value.links
            // move this into a URL utility class.
            val next_link = page_data.next.map(nxt =>
              uri
                      .replaceParams("older_than", nxt.getMillis.toString)
                      .removeParams("newer_than"))
            val prev_link = page_data.prev.map(prv =>
              uri
                      .replaceParams("newer_than", prv.getMillis.toString)
                      .removeParams("older_than"))

            val resp = ConvSummaryResponseCntrl(
              data = value.convs,
              links = NavigationLinksCntrl(
                prev = prev_link.map(_.toString()),
                next = next_link.map(_.toString())
              )
            )

            Res.Success("Inbox conversations found", Json.toJson(resp))
          case Left(InboxV3ServiceGetConversationsError.PageLimitExceeded(error))  =>
            Res.BadRequestError(error)

          case Left(InboxV3ServiceGetConversationsError.PageLimitUnderFlow(error)) =>
            Res.BadRequestError(error)

          case Left(InboxV3ServiceGetConversationsError.InvocationConfusion(error)) =>
            Res.BadRequestError(error)

          case Left(InboxV3ServiceGetConversationsError.AbsurdAtSameTimeConvsNotFound(error)) =>
            Res.ServerError(err = new Exception(error))

          //case Failure(exception) =>
          //  Res.ServerError(exception)
          case Left(InboxV3ServiceGetConversationsError.DBFailure( ex)) =>
            Res.ServerError(err = ex)
        }
      }*/


  /*
  def getConversationsInboxV3(
    v: String,
    inbox_id: String,
    folder_id: String,
    older_than: Option[Long],
    tid: Option[Long]
  ) = checkPermission(
    version = v,
    permission = PermType.VIEW_CHANNELS,
    aidOpt = None, // only supported for NewUI and new api versions
    tidOpt = tid

  ) .async { request =>

    val Logger = request.Logger
    val Res = request.Response

    val permittedOwnerIds = request.permittedAccountIds

    emailThreadDAO.getConversationsInboxV3(
      permittedOwnerIds = permittedOwnerIds,
      teamId = request.actingTeamAccount.get.team_id,
      inboxId = inbox_id,
      folderId = folder_id,
      olderThan = older_than.map(timeMillis => new DateTime(timeMillis)),
      Logger = Logger
    )
      .map(conversations => {

        /**
          * fixme InboxV3: we should call it something other than "conversations", in view of a multichannel inbox with tasks
          */
        Res.Success("Inbox conversations found", Json.toJson(conversations))

      })
      .recover { case e =>

        Res.ServerError(e)

      }

  }

   */


  //New inbox API and has LinkedIn Flow
  def getConversationContactsInboxV3(
                                      //v: String,
                                      uuid: String,
                                      tid: Option[Long],
                                      tiid: String
                                    ): Action[AnyContent] = permissionUtils.checkPermission(
      version = "v2",
      permission = PermType.VIEW_CHANNELS,
      tidOpt = tid

    )
    .andThen(hasTeamInboxAccess(
      teamInboxIdFromReq = tiid,
      emailThreadUuid = uuid,
    )) { (request: TeamInboxAccessRequest[AnyContent]) =>

      val Logger = request.Logger
      val Res = request.Response

      Logger.info(s"getConversationContactsInboxV3 conv_id: ${request.thread.id}, tid: ${tid}")

      teamInboxService.getConversationContacts(
        threadId = request.thread.id,
        teamId = TeamId(request.thread.team_id),
        channelType = request.thread.channelType
      ) match {

        case Failure(e) =>

          Res.ServerError(e)

        case Success(contacts) =>
          Res.Success("Conversation contacts", Json.obj(
            "contacts" -> contacts
          ))

      }

    }

  // nxd: 14-feb-2022 autoschedule
  // create new api to get campaign information for a conversation
  // conversation/:conv_id/campaign
  // return type is ConversationCampaign.
  //New inbox API and has LinkedIn Flow
  def getConversationCampaignInboxV3(
                                      //v: String,
                                      uuid: String,
                                      tid: Option[Long],
                                      tiid: String
                                    ): Action[AnyContent] = permissionUtils.checkPermission(
      version = "v2",
      permission = PermType.VIEW_CHANNELS,
      tidOpt = tid

    )
    .andThen(hasTeamInboxAccess(
      teamInboxIdFromReq = tiid,
      emailThreadUuid = uuid,
    )) { (request: TeamInboxAccessRequest[AnyContent]) =>

      val Logger = request.Logger
      val Res = request.Response

      val campaign_conv_res = inboxV3Service.handleGetCampaignAndProspectForConversationInboxV3(
        conversation_id = request.thread.id,
        team_id = request.actingTeamAccount.get.team_id,
        // todo nxd: cross check - we expect this to be not null
        //           after validation
        campaign_id = request.thread.campaign_id,
        Logger = Logger
      )

      campaign_conv_res match {
        case Left(FindConversationCampaignError.DatabaseError(exception)) =>
          Res.ServerError(exception)
        case Left(FindConversationCampaignError.CampaignNotFoundError(err)) =>
          Res.NotFoundError(err)
        case Left(FindConversationCampaignError.EmailThreadNotPartOfACampaign(str)) =>
          Res.Success(str, Json.obj())
        case Right(campaign_conv) =>
          Res.Success("Campaign and Prospect found for the conversation", Json.obj(
            "conversation_campaign" -> campaign_conv
          ))
      }


    }

  //New inbox API and has LinkedIn Flow
  def getSearchedConversations(
                                tid: Option[Long],
                                search_key: Option[String],
                                duration: Option[String],
                                folder_type: Option[String],
                                prospect_id: Option[Long],
                                prospect_account_id: Option[Long],
                                sent_at: Option[Long],
                                last_sent_id: Option[String],
                                tiid: String
                              ): Action[AnyContent] = permissionUtils.checkPermission(
    version = "v2",
    permission = PermType.VIEW_CHANNELS,
    tidOpt = tid

  ).andThen(hasTeamInboxAccessV2(
    version = "v2",
    teamInboxId = tiid,
    allow_for_consolidated_inbox = true,
    prospect_id = prospect_id.map(ProspectId(_))
  )) { (request: TeamInboxAccessRequestV2[AnyContent]) =>

    val query_string = request.getQueryString("search_key")
    val folder_type_filter: Option[String] = if (folder_type.isDefined && folder_type.get.trim.nonEmpty) {
      folder_type
    } else {
      None
    }

    given Logger: SRLogger = request.Logger
    val Res = request.Response
    val teamId = request.actingTeamAccount.get.team_id

    inboxV3Service.getConversationIdsForSearch(
      team_id = teamId,
      search_key = query_string,
      esets = request.esets,
      lsets = request.linkedinSettingIds,
      inbox_type = request.inbox_type_data.inbox_type,
      duration = duration,
      prospect_id = prospect_id.map(ProspectId(_)),
      prospect_account_id = prospect_account_id.map(ProspectAccountsId(_)),
      are_all_esets_accessible = request.are_all_esets_accessible,
      sent_at = sent_at,
      last_sent_id = last_sent_id,
      folder_type = folder_type_filter
    ) match {
      case Left(GetConversationIdsForSearchFailure(e)) =>
        Res.ServerError("Error while searching the email.", e = Some(e))
      case Right(value) =>
        Res.Success("Conversations for search", Json.toJson(value))
    }

  }


}

