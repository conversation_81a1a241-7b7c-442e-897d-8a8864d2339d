package api.prospects

import api.accounts.models.{AccountId, OrgId}
import api.accounts.{PermType, PermissionRequest, PermissionUtils, TeamId}
import api.call.models.DoerAccountName
import api.campaigns.services.CampaignId
import api.prospects.models.{MergeDuplicatesForm, PotentialDuplicateProspectLogId, ProspectId}
import api.prospects.service.{PotentialDuplicateProspectService, SearchPotentialDuplicateProspectsError}
import play.api.libs.json.{JsError, JsResult, JsSuccess, Json}
import play.api.mvc.{Action, AnyContent, BaseController, ControllerComponents}
import utils.{Helpers, SRLogger}
import utils.helpers.LogHelpers
import utils.mq.merge_duplicate_prospects.{MergeDuplicatesLogDetails, MqMergeDuplicateProspects}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class DuplicateProspectsController(
                                    protected val controllerComponents: ControllerComponents,
                                    permissionUtils: PermissionUtils,
                                    mqMergeDuplicateProspects: MqMergeDuplicateProspects,
                                    potentialDuplicateProspectService: PotentialDuplicateProspectService
                                  ) extends BaseController {
  private implicit val ec: ExecutionContext = controllerComponents.executionContext

  def searchPotentialDuplicateProspects(v: String, tid: Option[Long], pid: Option[Long], cid: Option[Long], search: Option[String]): Action[AnyContent] = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_PROSPECTS,
    tidOpt = tid
  ) { (request: PermissionRequest[AnyContent]) =>
    given Logger: SRLogger = request.Logger
    val Res = request.Response

    val teamId: TeamId = TeamId(request.actingTeamAccount.get.team_id)
    val orgId: OrgId = OrgId(request.loggedinAccount.org.id)

    potentialDuplicateProspectService.searchPotentialDuplicateProspects(
      prospectId = pid.map(ProspectId(_)),
      campaignId = cid.map(CampaignId(_)),
      searchString = search,
      teamId = teamId,
      orgId = orgId
    ) match {
      case Left(SearchPotentialDuplicateProspectsError.DBFailure(e)) =>
        Logger.fatal(s"Error while fetching potential duplicate prospects", e)
        Res.ServerError("Please try again or contact support", e = Some(e))

      case Right(prospects) =>
        Res.Success(message = "Duplicates found", data = Json.obj("duplicates" -> prospects))
    }
  }

  def keepSeparate(v: String, tid: Option[Long], id: Long) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = tid
  ).async(parse.json) { request =>
    Future {
      given Logger: SRLogger = request.Logger
      val Res = request.Response

      val teamId: TeamId = TeamId(request.actingTeamAccount.get.team_id)
      val accountId: AccountId = AccountId(request.loggedinAccount.internal_id)
      val accountEmail: String = request.loggedinAccount.email

      potentialDuplicateProspectService.updateStatusToKeepSeparate(
        potentialDuplicateProspectLogId = PotentialDuplicateProspectLogId(id = id),
        status_updated_by = accountId,
        status_updated_by_login_account_email = accountEmail,
        status_updated_req_trace_id = request.auditRequestLogId,
        teamId = teamId
      ) match {
        case Failure(exception) =>
          Logger.error(s"Error while updating status: ${LogHelpers.getStackTraceAsString(exception)}")
          Res.ServerError("Error while updating status", Some(exception))
        case Success(_) => Res.Success("Keep separate status updated", Json.obj())
      }

    }
  }

  def mergeDuplicates(v: String, tid: Option[Long], id: Long) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PROSPECTS, //TODO: Confirm this as there are multiple actions on multiple tables
    tidOpt = tid
  ).async(parse.json) { request =>
      given Logger: SRLogger = request.Logger
      val Res = request.Response

      val teamId: TeamId = TeamId(request.actingTeamAccount.get.team_id)
      val accountId: AccountId = AccountId(request.loggedinAccount.internal_id)
      val accountName: String = Helpers.getAccountName(request.loggedinAccount)
      val accountEmail: String = request.loggedinAccount.email

      val validateData: JsResult[MergeDuplicatesForm] = request.body.validate[MergeDuplicatesForm]

      validateData match {
        case JsError(e) =>Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

        case JsSuccess(data, _) =>

          mqMergeDuplicateProspects.publish(msg = MergeDuplicatesLogDetails(
            logId = PotentialDuplicateProspectLogId(id = id),
            masterProspectId = data.master_prospect_id,
            permittedAccountIds = request.permittedAccountIds.map(aid=> AccountId(aid)),
            doerAccountId = accountId,
            doerAccountName = DoerAccountName(accountName),
            teamId = teamId
          )) match {
            case Failure(exception) =>
              Logger.error(s"Error while sending duplicates to merge: ${LogHelpers.getStackTraceAsString(exception)}")
              Future.successful(Res.ServerError("Error while merging", Some(exception)))
            case Success(_) =>
              potentialDuplicateProspectService.updateStatusToQueuedForMerge(
                potentialDuplicateProspectLogId = PotentialDuplicateProspectLogId(id = id),
                status_updated_by = accountId,
                status_updated_by_login_account_email = accountEmail,
                status_updated_req_trace_id = request.auditRequestLogId,
                masterProspectBasicInfo = data.masterProspectData,
                teamId = teamId
              ) match {
                case Failure(exception) =>
                  Logger.error(s"Error while merging duplicates: ${LogHelpers.getStackTraceAsString(exception)}")
                  Future.successful(Res.ServerError("Error while merging", Some(exception)))
                case Success(_) => Future.successful(Res.Success("Duplicate prospects merge started", Json.obj()))
              }
          }


      }

  }
}
