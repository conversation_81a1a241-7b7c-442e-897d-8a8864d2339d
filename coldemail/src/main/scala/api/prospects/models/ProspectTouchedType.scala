package api.prospects.models

sealed trait ProspectTouchedType

object ProspectTouchedType {

  private val email_validation_check = "email_validation_check"
  private val task_done = "task_done"
  private val manual_task_scheduled = "manual_task_scheduled"

  case class EmailValidationCheck(
    invalidEmail: <PERSON><PERSON><PERSON>,
  ) extends ProspectTouchedType {

    override def toString: String = email_validation_check

  }

  case object TaskDone extends ProspectTouchedType {

    override def toString: String = task_done

  }

  case object ManualTaskScheduled extends ProspectTouchedType {

    override def toString: String = manual_task_scheduled

  }

}
