package api.prospects.models

import api.team_inbox.service.ValidateInboxTypeAndGetEsetsError

sealed trait ValidateSendNewEmailManuallyError

object ValidateSendNewEmailManuallyError {

  case object CampaignStepIdAndTIIDBothDefinedError extends  ValidateSendNewEmailManuallyError

  case class ValidateInboxTypeError(e: ValidateInboxTypeAndGetEsetsError) extends ValidateSendNewEmailManuallyError

  case class ToEmailEmpty(str: String) extends ValidateSendNewEmailManuallyError

  case class InvalidInboxIdError(str: String) extends ValidateSendNewEmailManuallyError


}

