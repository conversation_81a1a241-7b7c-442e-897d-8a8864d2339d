package api.prospects.models

import api.accounts.models.OrgId
import api.accounts.{ProspectCategoriesInDB, TeamId}
import utils.SRLogger

import scala.util.{Success, Try}

sealed trait ProspectCategoryUpdateFlow {

  def new_prospect_category_id: ProspectCategoryId

  def getProspectStatusUpdatedManually: Boolean

}

object ProspectCategoryUpdateFlow {

  case class AutoUpdate(
                          old_prospect_category_id: ProspectCategoryId,
                          new_prospect_category_id: ProspectCategoryId
                        ) extends ProspectCategoryUpdateFlow {


    override def getProspectStatusUpdatedManually: Boolean = false

  }

  case class AdminUpdate(
                          old_prospect_category_id: Option[ProspectCategoryId],
                          new_prospect_category_id: ProspectCategoryId
                        ) extends ProspectCategoryUpdateFlow {


    override def getProspectStatusUpdatedManually: Boolean = true

  }


  def isProspectCategoryAutoUpdateValid(
    currProspectCategoryTextId: String,
    newProspectCategoryTextId: String,
    orgId: OrgId,
  )(using logger: SRLogger): Bo<PERSON>an = {

    /**
      * 21-Jun-2024
      *
      * Only use this fn to validate auto update of prospect category,
      * we are not validating when prospect category update happens through
      * reply tracker or admin.
      */

    if (currProspectCategoryTextId == newProspectCategoryTextId) {

      false

    } else {

      val tryOfIsValid: Try[Boolean] = for {

        currProspectCategory: ProspectCategory.Value <- Try {
          ProspectCategory.withName(s = currProspectCategoryTextId)
        }

        newProspectCategory: ProspectCategory.Value <- Try {
          ProspectCategory.withName(s = newProspectCategoryTextId)
        }

      } yield {

        ProspectCategory
          .validTransitions(currentCategory = currProspectCategory, orgId = orgId)
          .contains(newProspectCategory)

      }

      // will return false for custom prospect categories
      tryOfIsValid.getOrElse(false)

    }

  }

  def isProspectCategoryAutoUpdateValidNew(
    currProspectCategoryTextId: String,
    newProspectCategoryTextId: String,
  )(using logger: SRLogger): Boolean = {

    if (currProspectCategoryTextId == newProspectCategoryTextId) {

      false

    } else {

      val tryOfIsValid: Try[Boolean] = for {

        currProspectCategory: ProspectCategoryNew.Value <- Try {
          ProspectCategoryNew.withName(s = currProspectCategoryTextId)
        }

        newProspectCategory: ProspectCategoryNew.Value <- Try {
          ProspectCategoryNew.withName(s = newProspectCategoryTextId)
        }

      } yield {

        ProspectCategoryNew
          .validTransitions(currentCategory = currProspectCategory)
          .contains(newProspectCategory)

      }

      // will return false for custom prospect categories
      tryOfIsValid.getOrElse(false)

    }

  }

}
