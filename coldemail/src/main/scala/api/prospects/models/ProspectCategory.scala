package api.prospects.models

import api.accounts.TeamId
import api.accounts.models.OrgId
import api.emails.models.EmailReplyType
import api.{AppConfig, prospects}
import play.api.libs.json.Format
import utils.SRLogger
import utils.enum_sr_utils.EnumUtils

case class ProspectCategoryDisplyNameColor(
                                            displayName: String,
                                            color: String,
                                            prospectCategoryRank: ProspectCategoryRank,
                                          )

object ProspectCategory extends Enumeration {
  type ProspectCategory = Value

  val INTERESTED = Value("interested")
  val NOT_INTERESTED = Value("not_interested")
  val NOT_NOW = Value("not_now")
  val DO_NOT_CONTACT = Value("do_not_contact")
  // val FORWARDED = Value("forwarded")
  val OUT_OF_OFFICE = Value("out_of_office")
  val DELIVERY_FAILED = Value("delivery_failed") //Bad contact info
  val AUTO_REPLY = Value("auto_reply")
  val NOT_CATEGORIZED = Value("not_categorized") // New
  val CONVERTED = Value("converted")
  val MEETING_BOOKED = Value("meeting_booked")


  // Adding few extra default types of prospect categories.
  val OPEN = Value("open")
  val CONTACTED = Value("contacted")
  val REPLIED = Value("replied")
  val UNRESPONSIVE = Value("unresponsive")

  implicit val format: Format[prospects.models.ProspectCategory.Value] = EnumUtils.enumFormat(ProspectCategory)

  def validTransitions(
    currentCategory: ProspectCategory.Value,
    orgId: OrgId,
  )(
    using Logger: SRLogger,
  ): Set[ProspectCategory.Value] = {

    if(AppConfig.stopAutoUpdatecategoryForOrgIdList.contains(orgId.id)){
      prospectCategoryTransitionsNonAutoUpdate.get(currentCategory) match {

        case None =>

          Logger.shouldNeverHappen(
            msg = s"Failed to get valid prospect category transitions for $currentCategory",
          )

          // Return empty, as there are no valid transitions from current category.

          // This should fail the auto update.

          Set.empty

        case Some(validTransitions) =>

          validTransitions
      }
    } else {
    prospectCategoryTransitions.get(currentCategory) match {

      case None =>

        Logger.shouldNeverHappen(
          msg = s"Failed to get valid prospect category transitions for $currentCategory",
        )

        // Return empty, as there are no valid transitions from current category.

        // This should fail the auto update.

        Set.empty

      case Some(validTransitions) =>

        validTransitions
    }
    }

  }

  val displayNamesOfDefaultCategories = Map(

    // ProspectCategory.ALL -> "Inbox",

    ProspectCategory.NOT_CATEGORIZED -> ProspectCategoryDisplyNameColor(
      displayName = "New",
      color = "#c7c7c7",
      prospectCategoryRank = ProspectCategoryRank(rank = 1000)
    ),

    ProspectCategory.OPEN -> ProspectCategoryDisplyNameColor(
      displayName = "Open",
      color = "#dadb8d",
      prospectCategoryRank = ProspectCategoryRank(rank = 2000),
    ),

    ProspectCategory.CONTACTED -> ProspectCategoryDisplyNameColor(
      displayName = "Contacted",
      color = "#9edae5",
      prospectCategoryRank = ProspectCategoryRank(rank = 3000)
    ),

    ProspectCategory.MEETING_BOOKED -> ProspectCategoryDisplyNameColor(
      displayName = "Meeting Booked",
      color = "#0000FF",
      prospectCategoryRank = ProspectCategoryRank(rank = 3400) 
    ),

    ProspectCategory.OUT_OF_OFFICE -> ProspectCategoryDisplyNameColor(
      displayName = "Out of office",
      color = "#fe7f0e",
      prospectCategoryRank = ProspectCategoryRank(rank = 4000)
    ),

    // ProspectCategory.FORWARDED -> ProspectCategoryDisplyNameColor("Forwarded", "#dadb8d"),

    ProspectCategory.AUTO_REPLY -> ProspectCategoryDisplyNameColor(
      displayName = "Auto reply",
      color = "#ffbc78",
      prospectCategoryRank = ProspectCategoryRank(rank = 5000)
    ),

    ProspectCategory.REPLIED -> ProspectCategoryDisplyNameColor(
      displayName = "Replied",
      color = "#17bed0",
      prospectCategoryRank = ProspectCategoryRank(rank = 6000),
    ),

    ProspectCategory.INTERESTED -> ProspectCategoryDisplyNameColor(
      displayName = "Interested",
      color = "#97df89",
      prospectCategoryRank = ProspectCategoryRank(rank = 7000)
    ),

    ProspectCategory.CONVERTED -> ProspectCategoryDisplyNameColor(
      displayName = "Converted",
      color = "#2ba02d",
      prospectCategoryRank = ProspectCategoryRank(rank = 8000)
    ),

    ProspectCategory.NOT_INTERESTED -> ProspectCategoryDisplyNameColor(
      displayName = "Not interested",
      color = "#ff9897",
      prospectCategoryRank = ProspectCategoryRank(rank = 9000)
    ),

    ProspectCategory.NOT_NOW -> ProspectCategoryDisplyNameColor(
      displayName = "Not now",
      color = "#c4b0d5",
      prospectCategoryRank = ProspectCategoryRank(rank = 10000)
    ),

    ProspectCategory.UNRESPONSIVE -> ProspectCategoryDisplyNameColor(
      displayName = "Unresponsive",
      color = "#9467bc",
      prospectCategoryRank = ProspectCategoryRank(rank = 11000),
    ),

    ProspectCategory.DELIVERY_FAILED -> ProspectCategoryDisplyNameColor(
      displayName = "Bad contact info",
      color = "#f7b7d2",
      prospectCategoryRank = ProspectCategoryRank(rank = 12000)
    ),

    ProspectCategory.DO_NOT_CONTACT -> ProspectCategoryDisplyNameColor(
      displayName = "Do not contact",
      color = "#d52728",
      prospectCategoryRank = ProspectCategoryRank(rank = 13000)
    ),

    // ProspectCategory.ARCHIVED -> "Archived"

  )

  def getOldCategoryFromNewCategory(
                                     newCategory: ProspectCategoryNew.Value
                                   )(using logger: SRLogger): ProspectCategory.Value = {
    newCategory match {
      case ProspectCategoryNew.NOT_CONTACTED => ProspectCategory.NOT_CATEGORIZED
      case ProspectCategoryNew.APPROACHING => ProspectCategory.CONTACTED
      case ProspectCategoryNew.ENGAGING => ProspectCategory.REPLIED
      case ProspectCategoryNew.INTERESTED => ProspectCategory.INTERESTED
      case ProspectCategoryNew.MEETING_BOOKED => ProspectCategory.MEETING_BOOKED
      case ProspectCategoryNew.CONVERTED => ProspectCategory.CONVERTED
      case ProspectCategoryNew.NOT_INTERESTED => ProspectCategory.NOT_INTERESTED
      case ProspectCategoryNew.NOT_NOW => ProspectCategory.NOT_NOW
      case ProspectCategoryNew.DO_NOT_CONTACT => ProspectCategory.DO_NOT_CONTACT
      case ProspectCategoryNew.UNRESPONSIVE => ProspectCategory.UNRESPONSIVE
      case ProspectCategoryNew.BAD_CONTACT_INFO => ProspectCategory.DELIVERY_FAILED
    }
  }

  def getProspectCategoryDisplyNameAndId(cate: ProspectCategory.Value) = displayNamesOfDefaultCategories(cate)

  /**
   * Converts an EmailReplyType value to the corresponding ProspectCategory value
   *
   * @param replyType The EmailReplyType value to convert
   * @return The corresponding ProspectCategory value
   */
  def fromEmailReplyType(replyType: EmailReplyType.Value): ProspectCategory.Value = {
    replyType match {
      case EmailReplyType.INTERESTED => INTERESTED
      case EmailReplyType.DO_NOT_CONTACT => DO_NOT_CONTACT
      case EmailReplyType.OUT_OF_OFFICE => OUT_OF_OFFICE
      case EmailReplyType.DELIVERY_FAILED => DELIVERY_FAILED
      case EmailReplyType.AUTO_REPLY => AUTO_REPLY
      case EmailReplyType.NOT_CATEGORIZED => NOT_CATEGORIZED
      case EmailReplyType.CONTACTED => CONTACTED
    }
  }

  lazy val prospectCategoryTransitions: Map[ProspectCategory.Value, Set[ProspectCategory.Value]] =
    Map(

      ProspectCategory.DO_NOT_CONTACT -> Set.empty,

      ProspectCategory.CONVERTED -> Set(ProspectCategory.DO_NOT_CONTACT),

      ProspectCategory.NOT_CATEGORIZED -> ProspectCategory.values.filter(_ != ProspectCategory.NOT_CATEGORIZED),

      ProspectCategory.OPEN -> ProspectCategory.values.filter(_ != ProspectCategory.NOT_CATEGORIZED),

      ProspectCategory.CONTACTED -> Set(
        ProspectCategory.INTERESTED,
        ProspectCategory.NOT_INTERESTED,
        ProspectCategory.NOT_NOW,
        ProspectCategory.DO_NOT_CONTACT,
        ProspectCategory.OUT_OF_OFFICE,
        ProspectCategory.DELIVERY_FAILED,
        ProspectCategory.AUTO_REPLY,
        ProspectCategory.CONVERTED,
        ProspectCategory.MEETING_BOOKED,
        ProspectCategory.REPLIED,
        ProspectCategory.UNRESPONSIVE
      ),

      ProspectCategory.OUT_OF_OFFICE -> Set(
        ProspectCategory.INTERESTED,
        ProspectCategory.NOT_INTERESTED,
        ProspectCategory.NOT_NOW,
        ProspectCategory.DO_NOT_CONTACT,
        ProspectCategory.DELIVERY_FAILED,
        ProspectCategory.CONVERTED,
        ProspectCategory.MEETING_BOOKED,
        ProspectCategory.REPLIED,
        ProspectCategory.UNRESPONSIVE
      ),

      ProspectCategory.AUTO_REPLY -> Set(
        ProspectCategory.INTERESTED,
        ProspectCategory.NOT_INTERESTED,
        ProspectCategory.NOT_NOW,
        ProspectCategory.DO_NOT_CONTACT,
        ProspectCategory.OUT_OF_OFFICE,
        ProspectCategory.DELIVERY_FAILED,
        ProspectCategory.CONVERTED,
        ProspectCategory.MEETING_BOOKED,
        ProspectCategory.REPLIED,
        ProspectCategory.UNRESPONSIVE,
      ),

      ProspectCategory.DELIVERY_FAILED -> Set(
        ProspectCategory.DO_NOT_CONTACT,
        ProspectCategory.OPEN,
        ProspectCategory.CONTACTED,
        ProspectCategory.REPLIED,
      ),

      ProspectCategory.REPLIED -> Set(
        ProspectCategory.INTERESTED,
        ProspectCategory.MEETING_BOOKED,
        ProspectCategory.NOT_INTERESTED,
        ProspectCategory.NOT_NOW,
        ProspectCategory.DO_NOT_CONTACT,
        ProspectCategory.CONVERTED,
      ),

      ProspectCategory.INTERESTED -> Set(
        ProspectCategory.MEETING_BOOKED,
        ProspectCategory.NOT_INTERESTED,
        ProspectCategory.NOT_NOW,
        ProspectCategory.DO_NOT_CONTACT,
        ProspectCategory.CONVERTED,
      ),

      ProspectCategory.MEETING_BOOKED -> Set(
        ProspectCategory.NOT_INTERESTED,
        ProspectCategory.NOT_NOW,
        ProspectCategory.DO_NOT_CONTACT,
        ProspectCategory.CONVERTED,
      ),

      ProspectCategory.NOT_INTERESTED -> Set(
        ProspectCategory.DO_NOT_CONTACT,
        ProspectCategory.INTERESTED,
        ProspectCategory.MEETING_BOOKED,
      ),

      ProspectCategory.NOT_NOW -> Set(
        ProspectCategory.INTERESTED,
        ProspectCategory.MEETING_BOOKED,
        ProspectCategory.NOT_INTERESTED,
        ProspectCategory.DO_NOT_CONTACT,
        ProspectCategory.CONVERTED,
      ),

      ProspectCategory.UNRESPONSIVE -> Set(
        ProspectCategory.REPLIED,
        ProspectCategory.INTERESTED,
        ProspectCategory.MEETING_BOOKED,
        ProspectCategory.NOT_INTERESTED,
        ProspectCategory.NOT_NOW,
        ProspectCategory.DO_NOT_CONTACT,
        ProspectCategory.CONVERTED,
      )

    )

  lazy val prospectCategoryTransitionsNonAutoUpdate: Map[ProspectCategory.Value, Set[ProspectCategory.Value]] =
    Map(

      ProspectCategory.DO_NOT_CONTACT -> Set.empty,

      ProspectCategory.CONVERTED -> Set.empty,

      ProspectCategory.NOT_CATEGORIZED -> Set(
        ProspectCategory.DELIVERY_FAILED,
        ProspectCategory.AUTO_REPLY,
        ProspectCategory.OUT_OF_OFFICE,
        ProspectCategory.DO_NOT_CONTACT,
      ),

      ProspectCategory.OPEN -> Set(
        ProspectCategory.DELIVERY_FAILED,
        ProspectCategory.AUTO_REPLY,
        ProspectCategory.OUT_OF_OFFICE,
        ProspectCategory.DO_NOT_CONTACT,
      ),

      ProspectCategory.CONTACTED -> Set(
        ProspectCategory.DELIVERY_FAILED,
        ProspectCategory.AUTO_REPLY,
        ProspectCategory.OUT_OF_OFFICE,
        ProspectCategory.DO_NOT_CONTACT,
      ),

      ProspectCategory.OUT_OF_OFFICE -> Set(
        ProspectCategory.DELIVERY_FAILED,
        ProspectCategory.AUTO_REPLY,
        ProspectCategory.DO_NOT_CONTACT,
      ),

      ProspectCategory.AUTO_REPLY -> Set(
        ProspectCategory.DELIVERY_FAILED,
        ProspectCategory.OUT_OF_OFFICE,
        ProspectCategory.DO_NOT_CONTACT,
      ),

      ProspectCategory.DELIVERY_FAILED -> Set(
        ProspectCategory.DO_NOT_CONTACT,
      ),

      ProspectCategory.REPLIED -> Set.empty,

      ProspectCategory.INTERESTED -> Set.empty,

      ProspectCategory.MEETING_BOOKED -> Set.empty,

      ProspectCategory.NOT_INTERESTED -> Set.empty,

      ProspectCategory.NOT_NOW -> Set.empty,

      ProspectCategory.UNRESPONSIVE -> Set.empty,
    )

}

object ProspectCategoryNew extends Enumeration {
  type ProspectCategoryNew = Value

  val NOT_CONTACTED = Value("not_contacted") //old-> new

  //Actively being contacted in one or more campaigns, but no meaningful response yet.
  val APPROACHING = Value("approaching") //old-> contacted

  //Responded positively or neutrally, needs follow-up/nurturing (replaces vague Replied or Contacted as a state).
  val ENGAGING = Value("engaging") //old-> replied

  //Explicit positive interest shown (e.g., requested demo, meeting)
  val INTERESTED = Value("interested")

  val MEETING_BOOKED = Value("meeting_booked") //new category

  val NOT_INTERESTED = Value("not_interested")
  val NOT_NOW = Value("not_now")
  val DO_NOT_CONTACT = Value("do_not_contact")

  val CONVERTED = Value("converted")

  val BAD_CONTACT_INFO = Value("bad_contact_info")

  val UNRESPONSIVE = Value("unresponsive")

  implicit val format: Format[prospects.models.ProspectCategoryNew.Value] = EnumUtils.enumFormat(ProspectCategoryNew)

  def validTransitions(
    currentCategory: ProspectCategoryNew.Value,
  )(
    using Logger: SRLogger,
  ): Set[ProspectCategoryNew.Value] = {

    prospectCategoryNewTransitions.get(currentCategory) match {

      case None =>

        Logger.shouldNeverHappen(
          msg = s"Failed to get valid new prospect category transitions for $currentCategory",
        )

        // Return empty, as there are no valid transitions from current category.

        // This should fail the auto update.

        Set.empty

      case Some(validTransitions) =>

        validTransitions

    }

  }


  val displayNamesOfDefaultCategories = Map(


    //fixme: check colors
    ProspectCategoryNew.NOT_CONTACTED -> ProspectCategoryDisplyNameColor(
      displayName = "Not contacted",
      color = "#c7c7c7",
      prospectCategoryRank = ProspectCategoryRank(rank = 1000)
    ),

    ProspectCategoryNew.APPROACHING -> ProspectCategoryDisplyNameColor(
      displayName = "Approaching",
      color = "#9edae5",
      prospectCategoryRank = ProspectCategoryRank(rank = 3000)
    ),

    ProspectCategoryNew.ENGAGING -> ProspectCategoryDisplyNameColor(
      displayName = "Engaging",
      color = "#17bed0",
      prospectCategoryRank = ProspectCategoryRank(rank = 6000),
    ),

    ProspectCategoryNew.INTERESTED -> ProspectCategoryDisplyNameColor(
      displayName = "Interested",
      color = "#97df89",
      prospectCategoryRank = ProspectCategoryRank(rank = 7000)
    ),

    ProspectCategoryNew.MEETING_BOOKED -> ProspectCategoryDisplyNameColor(
      displayName = "Meeting booked",
      color = "#329f23",
      prospectCategoryRank = ProspectCategoryRank(rank = 7500)
    ),


    ProspectCategoryNew.CONVERTED -> ProspectCategoryDisplyNameColor(
      displayName = "Converted",
      color = "#2ba02d",
      prospectCategoryRank = ProspectCategoryRank(rank = 8000)
    ),

    ProspectCategoryNew.NOT_INTERESTED -> ProspectCategoryDisplyNameColor(
      displayName = "Not interested",
      color = "#ff9897",
      prospectCategoryRank = ProspectCategoryRank(rank = 9000)
    ),

    ProspectCategoryNew.NOT_NOW -> ProspectCategoryDisplyNameColor(
      displayName = "Not now",
      color = "#c4b0d5",
      prospectCategoryRank = ProspectCategoryRank(rank = 10000)
    ),

    ProspectCategoryNew.UNRESPONSIVE -> ProspectCategoryDisplyNameColor(
      displayName = "Unresponsive",
      color = "#9467bc",
      prospectCategoryRank = ProspectCategoryRank(rank = 11000),
    ),

    ProspectCategoryNew.BAD_CONTACT_INFO -> ProspectCategoryDisplyNameColor(
      displayName = "Bad contact info",
      color = "#f7b7d2",
      prospectCategoryRank = ProspectCategoryRank(rank = 12000)
    ),

    ProspectCategoryNew.DO_NOT_CONTACT -> ProspectCategoryDisplyNameColor(
      displayName = "Do not contact",
      color = "#d52728",
      prospectCategoryRank = ProspectCategoryRank(rank = 13000)
    ),


  )

  def getProspectCategoryDisplyNameAndId(cate: ProspectCategoryNew.Value) = displayNamesOfDefaultCategories(cate)

  def getNewCategoryFromOldCategory(
                                     oldCategory: ProspectCategory.Value
                                   )(using logger: SRLogger): ProspectCategoryNew.Value = {
    oldCategory match {
      case ProspectCategory.NOT_CATEGORIZED => ProspectCategoryNew.NOT_CONTACTED
      case ProspectCategory.OPEN => ProspectCategoryNew.APPROACHING
      case ProspectCategory.CONTACTED => ProspectCategoryNew.APPROACHING
      case ProspectCategory.REPLIED => ProspectCategoryNew.ENGAGING
      case ProspectCategory.INTERESTED => ProspectCategoryNew.INTERESTED
      case ProspectCategory.MEETING_BOOKED => ProspectCategoryNew.MEETING_BOOKED
      case ProspectCategory.CONVERTED => ProspectCategoryNew.CONVERTED
      case ProspectCategory.NOT_INTERESTED => ProspectCategoryNew.NOT_INTERESTED
      case ProspectCategory.NOT_NOW => ProspectCategoryNew.NOT_NOW
      case ProspectCategory.DO_NOT_CONTACT => ProspectCategoryNew.DO_NOT_CONTACT
      case ProspectCategory.UNRESPONSIVE => ProspectCategoryNew.UNRESPONSIVE
      case ProspectCategory.DELIVERY_FAILED => ProspectCategoryNew.BAD_CONTACT_INFO
      case ProspectCategory.AUTO_REPLY | ProspectCategory.OUT_OF_OFFICE =>
        ProspectCategoryNew.APPROACHING
    }
  }

  lazy val prospectCategoryNewTransitions: Map[ProspectCategoryNew.Value, Set[ProspectCategoryNew.Value]] =
    Map(

      ProspectCategoryNew.DO_NOT_CONTACT -> Set.empty,

      ProspectCategoryNew.CONVERTED -> Set(ProspectCategoryNew.DO_NOT_CONTACT),

      ProspectCategoryNew.NOT_CONTACTED -> ProspectCategoryNew.values.filter(_ != ProspectCategoryNew.NOT_CONTACTED),

      ProspectCategoryNew.APPROACHING -> ProspectCategoryNew.values.filter(_ != ProspectCategoryNew.NOT_CONTACTED),

      ProspectCategoryNew.ENGAGING -> Set(
        ProspectCategoryNew.INTERESTED,
        ProspectCategoryNew.MEETING_BOOKED,
        ProspectCategoryNew.NOT_INTERESTED,
        ProspectCategoryNew.NOT_NOW,
        ProspectCategoryNew.DO_NOT_CONTACT,
        ProspectCategoryNew.CONVERTED,
        ProspectCategoryNew.BAD_CONTACT_INFO,
      ),

      ProspectCategoryNew.INTERESTED -> Set(
        ProspectCategoryNew.MEETING_BOOKED,
        ProspectCategoryNew.NOT_INTERESTED,
        ProspectCategoryNew.NOT_NOW,
        ProspectCategoryNew.DO_NOT_CONTACT,
        ProspectCategoryNew.CONVERTED,
        ProspectCategoryNew.BAD_CONTACT_INFO,
      ),

      ProspectCategoryNew.MEETING_BOOKED -> Set(
        ProspectCategoryNew.NOT_INTERESTED,
        ProspectCategoryNew.NOT_NOW,
        ProspectCategoryNew.DO_NOT_CONTACT,
        ProspectCategoryNew.CONVERTED,
        ProspectCategoryNew.BAD_CONTACT_INFO,
      ),

      ProspectCategoryNew.NOT_INTERESTED -> Set(
        ProspectCategoryNew.DO_NOT_CONTACT,
        ProspectCategoryNew.INTERESTED,
        ProspectCategoryNew.MEETING_BOOKED,
      ),

      ProspectCategoryNew.NOT_NOW -> Set(
        ProspectCategoryNew.INTERESTED,
        ProspectCategoryNew.MEETING_BOOKED,
        ProspectCategoryNew.NOT_INTERESTED,
        ProspectCategoryNew.DO_NOT_CONTACT,
        ProspectCategoryNew.CONVERTED,
        ProspectCategoryNew.BAD_CONTACT_INFO,
      ),

      ProspectCategoryNew.BAD_CONTACT_INFO -> Set(
        ProspectCategoryNew.DO_NOT_CONTACT,
        ProspectCategoryNew.APPROACHING,
        ProspectCategoryNew.ENGAGING,
      ),

      ProspectCategoryNew.UNRESPONSIVE -> Set(
        ProspectCategoryNew.ENGAGING,
        ProspectCategoryNew.INTERESTED,
        ProspectCategoryNew.MEETING_BOOKED,
        ProspectCategoryNew.NOT_INTERESTED,
        ProspectCategoryNew.NOT_NOW,
        ProspectCategoryNew.DO_NOT_CONTACT,
        ProspectCategoryNew.CONVERTED,
        ProspectCategoryNew.BAD_CONTACT_INFO,
      ),

    )

}
