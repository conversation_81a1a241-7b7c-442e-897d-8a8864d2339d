package api.prospects.models

import play.api.libs.json.{Js<PERSON><PERSON><PERSON>, <PERSON>sS<PERSON>, JsSuc<PERSON>, Js<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Writes}

import scala.util.{Failure, Success, Try}

sealed trait ProspectDeletionReason

object ProspectDeletionReason {
  private val by_user = "by_user"
  private val by_merge_duplicates = "by_merge_duplicates"

  case object ByUser extends ProspectDeletionReason {
    override def toString: String = by_user
  }
  case object ByMergeDuplicates extends ProspectDeletionReason {
    override def toString: String = by_merge_duplicates
  }

  def fromString(key: String): Try[ProspectDeletionReason] = Try {
    key match {
      case `by_user` => ByUser
      case `by_merge_duplicates` => ByMergeDuplicates
    }
  }

  implicit def writes: Writes[ProspectDeletionReason] = new Writes[ProspectDeletionReason] {
    def writes(i: ProspectDeletionReason): JsString = {
      JsString(i.toString)
    }
  }

  implicit def reads: Reads[ProspectDeletionReason] = (json: JsValue) => {

    fromString(key = json.as[String]) match {

      case Failure(e) => JsError(e.toString)

      case Success(value) => JsSuccess(value)

    }
  }

}
