package api.prospects.models

import play.api.libs.json.{Format, <PERSON>s<PERSON><PERSON><PERSON>, JsResult, JsString, JsSuccess, JsValue}

import scala.util.{Failure, Success, Try}

sealed trait PotentialDuplicateProspectStatus {
  def toString: String
}

object PotentialDuplicateProspectStatus {

  private val to_be_checked = "to_be_checked"
  private val merged = "merged"
  private val keep_separate = "keep_separate"
  private val queued_to_merge = "queued_to_merge"

  case object ToBeChecked extends PotentialDuplicateProspectStatus {
    override def toString: String = to_be_checked
  }
  case object Merged extends PotentialDuplicateProspectStatus {
    override def toString: String = merged
  }
  case object KeepSeparate extends PotentialDuplicateProspectStatus {
    override def toString: String = keep_separate
  }
  case object QueuedToMerge extends PotentialDuplicateProspectStatus {
    override def toString: String = queued_to_merge
  }

  def fromString(key: String): Try[PotentialDuplicateProspectStatus] = Try{

    key match {

      case `to_be_checked` => ToBeChecked
      case `merged` => Merged
      case `keep_separate` => KeepSeparate
      case `queued_to_merge` => QueuedToMerge
    }
  }

  given format: Format[PotentialDuplicateProspectStatus] = new Format[PotentialDuplicateProspectStatus] {
    override def writes(o: PotentialDuplicateProspectStatus): JsValue = {
      JsString(o.toString)
    }

    override def reads(json: JsValue): JsResult[PotentialDuplicateProspectStatus] = {
      fromString(json.as[String]) match {
        case Success(value) => JsSuccess(value = value)
        case Failure(_) => JsError(s"Invalid PotentialDuplicateProspectStatus :: $json")

      }
    }
  }
}
