package api.prospects.models

sealed trait TouchedIncrementReason

object TouchIncrementReason {

  private val invalid_email = "invalid_email"
  private val task_done = "task_done"
  private val manual_task_scheduled = "manual_task_scheduled"


  case object InvalidEmail extends TouchedIncrementReason {

    override def toString: String = invalid_email

  }

  case object TaskDone extends TouchedIncrementReason {

    override def toString: String = task_done

  }

  case object ManualTaskScheduled extends TouchedIncrementReason {

    override def toString: String = manual_task_scheduled

  }

}
