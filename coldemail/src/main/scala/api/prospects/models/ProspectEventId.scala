package api.prospects.models

import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON>ber, JsResult, JsSuccess, JsValue, Reads, Writes}

case class ProspectEventId(id: Long) extends AnyVal {
  override def toString: String = id.toString
}

object ProspectEventId {
  implicit val writes: Writes[ProspectEventId] = new Writes[ProspectEventId] {
    override def writes(o: ProspectEventId): JsValue = JsNumber(o.id)
  }

  implicit val reads: Reads[ProspectEventId] = new Reads[ProspectEventId] {
    override def reads(json: JsValue): JsResult[ProspectEventId] = {
      json match {
        case JsNumber(id) => JsSuccess(ProspectEventId(id = id.toLong))
        case randomValue => JsError(s"expected number, got some random value - $randomValue")
      }
    }
  }
}