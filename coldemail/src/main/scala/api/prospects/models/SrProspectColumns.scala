package api.prospects.models

import play.api.libs.json.{Js<PERSON><PERSON>r, JsString, JsSuccess, JsValue, <PERSON>s, Writes}

import scala.util.{Failure, Success, Try}

sealed trait SrProspectColumns

object SrProspectColumns {
  private val email = "email"
  private val phone = "phone"
  private val linkedin_url = "linkedin_url"
  private val company_firstname_lastname = "company_firstname_lastname"
  case object Email extends SrProspectColumns {
    override def toString: String = email
  }

  case object Phone extends SrProspectColumns {
    override def toString: String = phone
  }

  case object LinkedinUrl extends SrProspectColumns {
    override def toString: String = linkedin_url
  }

  case object CompanyFirstnameLastname extends SrProspectColumns {
    override def toString: String = company_firstname_lastname
  }

  def fromString(key: String): Try[SrProspectColumns] = Try {
    key match {
      case `email` => Email
      case `phone` => Phone
      case `linkedin_url` => LinkedinUrl
      case `company_firstname_lastname` => CompanyFirstnameLastname
    }
  }

  implicit def writes: Writes[SrProspectColumns] = new Writes[SrProspectColumns] {
    def writes(i: SrProspectColumns): JsString = {
      JsString(i.toString)
    }
  }

  implicit def reads: Reads[SrProspectColumns] = (json: JsValue) => {

    fromString(key = json.as[String]) match {

      case Failure(e) => JsError(e.toString)

      case Success(value) => JsSuccess(value)

    }
  }
}

