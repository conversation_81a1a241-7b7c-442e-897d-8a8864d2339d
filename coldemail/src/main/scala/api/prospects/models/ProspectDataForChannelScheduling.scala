package api.prospects.models

import api.campaigns.models.{CurrentStepStatusForScheduler, CurrentStepStatusForSchedulerData}
import eventframework.ProspectObject
import org.joda.time.DateTime
import scalikejdbc.WrappedResultSet
import scalikejdbc.jodatime.JodaWrappedResultSet.fromWrappedResultSetToJodaWrappedResultSet
import sr_scheduler.models.ChannelType
import utils.SRLogger

sealed trait ProspectDataForChannelScheduling {
  def channelType: ChannelType

  def prospect: ProspectObject

  def current_step_id: Option[Long]

  def current_step_status_data: Option[CurrentStepStatusForSchedulerData]

}

object ProspectDataForChannelScheduling {

  case class EmailChannelProspectForScheduling(
                                                prospect: ProspectObject,
                                                current_step_id: Option[Long],
                                                current_step_status_data: Option[CurrentStepStatusForSchedulerData],
                                                email_checked: Boolean,
                                                email_sent_for_validation: <PERSON><PERSON><PERSON>,
                                                email_sent_for_validation_at: Option[DateTime]
                                              ) extends ProspectDataForChannelScheduling {
    override def channelType: ChannelType = ChannelType.EmailChannel

  }

  case class LinkedinChannelProspectForScheduling(
                                                   prospect: ProspectObject,
                                                   current_step_status_data: Option[CurrentStepStatusForSchedulerData],
                                                   current_step_id: Option[Long]
                                                 ) extends ProspectDataForChannelScheduling {
    override def channelType: ChannelType = ChannelType.LinkedinChannel

  }

  case class CallChannelProspectForScheduling(
                                               prospect: ProspectObject,
                                               current_step_status_data: Option[CurrentStepStatusForSchedulerData],
                                               current_step_id: Option[Long]
                                             ) extends ProspectDataForChannelScheduling {
    override def channelType: ChannelType = ChannelType.CallChannel

  }

  case class WhatsappChannelProspectForScheduling(
                                                   prospect: ProspectObject,
                                                   current_step_status_data: Option[CurrentStepStatusForSchedulerData],
                                                   current_step_id: Option[Long]
                                                 ) extends ProspectDataForChannelScheduling {
    override def channelType: ChannelType = ChannelType.WhatsappChannel

  }

  case class SmsChannelProspectForScheduling(
                                              prospect: ProspectObject,
                                              current_step_status_data: Option[CurrentStepStatusForSchedulerData],
                                              current_step_id: Option[Long]
                                            ) extends ProspectDataForChannelScheduling {
    override def channelType: ChannelType = ChannelType.SmsChannel

  }

  case class GeneralChannelProspectForScheduling(
                                                  prospect: ProspectObject,
                                                  current_step_status_data: Option[CurrentStepStatusForSchedulerData],
                                                  current_step_id: Option[Long]
                                                ) extends ProspectDataForChannelScheduling {
    override def channelType: ChannelType = ChannelType.GeneralChannel

  }

  case class IndependentChannelProspectForScheduling(
                                                  prospect: ProspectObject,
                                                  current_step_status_data: Option[CurrentStepStatusForSchedulerData],
                                                  current_step_id: Option[Long]
                                                ) extends ProspectDataForChannelScheduling {
    override def channelType: ChannelType = ChannelType.IndependentChannel

  }

  private def currentStepStatusForSchedulerDataFromDB(rs: WrappedResultSet)(using Logger: SRLogger): Option[CurrentStepStatusForSchedulerData] = {
    rs.stringOpt("current_step_status_for_scheduler")
      .map(CurrentStepStatusForScheduler.fromString(_).get).map {
        case CurrentStepStatusForScheduler.Done =>
          CurrentStepStatusForSchedulerData.Done(done_at = rs.jodaDateTime("latest_task_done_or_skipped_at"))

        case CurrentStepStatusForScheduler.Due =>
          Logger.shouldNeverHappen("Due prospects should not come as a result from fetchProspectsV3MultichannelQuery.")
          CurrentStepStatusForSchedulerData.Due(due_at = rs.jodaDateTime("last_scheduled"))

        case CurrentStepStatusForScheduler.Skipped =>
          CurrentStepStatusForSchedulerData.Skipped(skipped_at = rs.jodaDateTime("latest_task_done_or_skipped_at"))

        case CurrentStepStatusForScheduler.PendingApproval =>
          // When we are scheduling the prospects to pending approval, we are updating the last_scheduled field.
          // so we can use that as the pending_approval_at field.
          // FIXME_MAGIC_CONTENT
          CurrentStepStatusForSchedulerData.PendingApproval(pending_approval_at = rs.jodaDateTime("last_scheduled"))

        case CurrentStepStatusForScheduler.Approved =>
          // FIXME_MAGIC_CONTENT
          // if its approved we don't need any last_task_done_or_skipped_at related stuffs, as we are not interested in that.
          // We only want to schedule the prospects email to emails_scheduled table.
          CurrentStepStatusForSchedulerData.Approved()

        case CurrentStepStatusForScheduler.AiContentQueued =>
          // Assuming AiContentQueued also updates last_scheduled when set.
          CurrentStepStatusForSchedulerData.AiContentQueued(ai_content_queued_at = rs.jodaDateTime("last_scheduled"))
      }
  }

  private def fromDBEmailChannel(rs: WrappedResultSet)(using Logger: SRLogger): ProspectDataForChannelScheduling.EmailChannelProspectForScheduling = {
    ProspectDataForChannelScheduling.EmailChannelProspectForScheduling(
      prospect = ProspectObject.fromDb(rs),
      current_step_id = rs.longOpt("current_step_id"),
      email_checked = rs.boolean("email_checked"),
      email_sent_for_validation = rs.boolean("email_sent_for_validation"),
      current_step_status_data = currentStepStatusForSchedulerDataFromDB(rs),
      email_sent_for_validation_at = rs.jodaDateTimeOpt("email_sent_for_validation_at")
    )
  }

  private def fromDBLinkedinChannel(rs: WrappedResultSet)(using Logger: SRLogger): ProspectDataForChannelScheduling.LinkedinChannelProspectForScheduling = {
    ProspectDataForChannelScheduling.LinkedinChannelProspectForScheduling(
      prospect = ProspectObject.fromDb(rs),
      current_step_id = rs.longOpt("current_step_id"),
      current_step_status_data = currentStepStatusForSchedulerDataFromDB(rs)
    )
  }

  private def fromDBWhatsappChannel(rs: WrappedResultSet)(using Logger: SRLogger): ProspectDataForChannelScheduling.WhatsappChannelProspectForScheduling = {
    ProspectDataForChannelScheduling.WhatsappChannelProspectForScheduling(
      prospect = ProspectObject.fromDb(rs),
      current_step_id = rs.longOpt("current_step_id"),
      current_step_status_data = currentStepStatusForSchedulerDataFromDB(rs)
    )
  }

  private def fromDBSmsChannel(rs: WrappedResultSet)(using Logger: SRLogger): ProspectDataForChannelScheduling.SmsChannelProspectForScheduling = {
    ProspectDataForChannelScheduling.SmsChannelProspectForScheduling(
      prospect = ProspectObject.fromDb(rs),
      current_step_id = rs.longOpt("current_step_id"),
      current_step_status_data = currentStepStatusForSchedulerDataFromDB(rs)
    )
  }

  private def fromDBCallChannel(rs: WrappedResultSet)(using Logger: SRLogger): ProspectDataForChannelScheduling.CallChannelProspectForScheduling = {
    ProspectDataForChannelScheduling.CallChannelProspectForScheduling(
      prospect = ProspectObject.fromDb(rs),
      current_step_id = rs.longOpt("current_step_id"),
      current_step_status_data = currentStepStatusForSchedulerDataFromDB(rs)
    )
  }

  private def fromDBGeneralChannel(rs: WrappedResultSet)(using Logger: SRLogger): ProspectDataForChannelScheduling.GeneralChannelProspectForScheduling = {
    ProspectDataForChannelScheduling.GeneralChannelProspectForScheduling(
      prospect = ProspectObject.fromDb(rs),
      current_step_id = rs.longOpt("current_step_id"),
      current_step_status_data = currentStepStatusForSchedulerDataFromDB(rs)
    )
  }

  private def fromDBIndependentChannel(rs: WrappedResultSet)(using Logger: SRLogger): ProspectDataForChannelScheduling.IndependentChannelProspectForScheduling = {
    ProspectDataForChannelScheduling.IndependentChannelProspectForScheduling(
      prospect = ProspectObject.fromDb(rs),
      current_step_id = rs.longOpt("current_step_id"),
      current_step_status_data = currentStepStatusForSchedulerDataFromDB(rs)
    )
  }

  def fromDBMultichannel(rs: WrappedResultSet, channelType: ChannelType)(using Logger: SRLogger): ProspectDataForChannelScheduling = {

    channelType match {
      case ChannelType.EmailChannel => fromDBEmailChannel(rs)
      case ChannelType.LinkedinChannel => fromDBLinkedinChannel(rs)
      case ChannelType.WhatsappChannel => fromDBWhatsappChannel(rs)
      case ChannelType.SmsChannel => fromDBSmsChannel(rs)
      case ChannelType.CallChannel => fromDBCallChannel(rs)
      case ChannelType.GeneralChannel => fromDBGeneralChannel(rs)
      case ChannelType.IndependentChannel => fromDBIndependentChannel(rs)
    }
  }
}
