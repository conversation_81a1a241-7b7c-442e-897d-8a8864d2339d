package api.prospects

import api.APIErrorResponse.ErrorResponseUpdateProspectStatusApi
import api.{ApiService, BadRequestErrorException}
import api.accounts.ReplyHandling.ReplyHandling
import api.accounts.models.{AccountId, OrgId}
import api.accounts.{Account, TeamId, TeamMember}
import api.campaigns.{CPCompletedEvent, CPRepliedEvent, CPTuple, CPUnPausedEvent, CampaignProspectDAO}
import api.campaigns.models.{CampaignProspectCompletedReason, NewProspectStatus, WillResumeAtUpdatedBy}
import api.campaigns.services.GetProspectIdFromUuidError.GetProspectIdError
import api.campaigns.services.UpdateProspectStatusErrorV3.{FilterOwnedProspectError, GetCampaignIdFromUuidErrors, GetProspectIdFromUuidErrors, GetProspectUuidFromId, UpdateProspectsStatusValidationError}
import api.campaigns.services.{CampaignId, CampaignProspectDAOService, CampaignProspectService, CampaignService, CampaignUuid, GetCampaignIdFromUuidError, GetProspectIdFromUuidError, UpdateProspectStatusErrorV3}
import api.columns.ProspectColumnDef
import api.generalmodule.GeneralModuleService
import api.prospects.dao_service.{ProspectDAOService, ProspectDAOServiceV2}
import api.prospects.models.{ProspectCategoryId, ProspectCategoryUpdateFlow, ProspectDeletionReason, ProspectId}
import api.prospects.service.{ProspectServiceV2, UpdateProspectStatusErrorType, UpdateProspectStatusValidationResult}
import api.tags.models.{CampaignTagUuid, TagAndUuid}
import api.tags.services.ProspectTagService
import com.github.tototoshi.csv.CSVWriter
import org.joda.time.DateTime
import play.api.http.FileMimeTypes
import play.api.libs.Files.SingletonTemporaryFileCreator
import play.api.libs.json.{JsObject, Json}
import play.api.mvc.Result
import play.api.mvc.Results.Ok
import utils.{Helpers, SRLogger}
import utils.helpers.LogHelpers
import utils.mq.prospectdeleter.{MQProspectDeleter, MQProspectDeleterMsg}
import utils.mq.srevents.{MQProspectCompletedEvent, MQProspectUnPauseEvent, MQRepliedEvent, MQRepliedEventMsg}
import utils.mq.webhook.MQWebhookCompleted
import utils.uuid.SrUuidUtils

import java.io.File
import java.net.URLEncoder
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Right, Success, Try}
import com.github.tototoshi.csv.defaultCSVFormat

sealed trait BatchActionErrors

object BatchActionErrors {
  case class BadRequestError(error: String) extends BatchActionErrors
  case class ServerError(error: Throwable) extends BatchActionErrors
}

case class BatchActionResponse(
                               responseMsg: String,
                               responseObj: JsObject
                              )

class ProspectBatchActionService(
                                  prospectDAOService: ProspectDAOService,
                                  prospectDAOServiceV2: ProspectDAOServiceV2,
                                  prospectServiceV2: ProspectServiceV2,
                                  prospectUpdateCategoryTemp: ProspectUpdateCategoryTemp,
                                  mqProspectDeleter: MQProspectDeleter,
                                  prospectService: ProspectService,
                                  campaignProspectService: CampaignProspectService,
                                  campaignService: CampaignService,
                                  prospectTagService: ProspectTagService,
                                  srUuidUtils: SrUuidUtils,
                                  generalModuleService: GeneralModuleService,
                                  prospectColumnDef: ProspectColumnDef,
                                  campaignProspectDAO: CampaignProspectDAO,
                                  campaignProspectDAOService: CampaignProspectDAOService,
                                  mqWebhookCompleted: MQWebhookCompleted,
                                  prospectAccountDAO: ProspectAccountDAO1,
                                  mqRepliedEvent: MQRepliedEvent,
                                  mqProspectCompletedEvent: MQProspectCompletedEvent,
                                  mqProspectUnPauseEvent: MQProspectUnPauseEvent,
                                  apiService: ApiService
                                ) {
  def getProspectIdsForAction(
                               data: ProspectBatchActions,
                               actingTeam: TeamMember,
                               loginAccount: Account,
                               permittedAccountIds: Seq[Long],
                               permittedAccountIdsForEditingProspects: Seq[Long],
                               isApiCall : Boolean = false
                             )(implicit ec: ExecutionContext,Logger: SRLogger): Future[Seq[Long]] = {

    val teamId = actingTeam.team_id
    if (data.is_select_all.isDefined && data.is_select_all.get && data.filters.isDefined) {

      val queryIdsFuture = prospectDAOService.IdsOnlyQuery(
        accountIds = permittedAccountIds,
        teamId = actingTeam.team_id,
        orgId = loginAccount.org.id,
        loggedinAccountId = loginAccount.internal_id,
        prospectQueryData = data.filters.get,
        account = loginAccount,
        isInternalRequest = true,
        Logger = Logger
      )

      queryIdsFuture


    } else {

      // FIXED: status: check permissions for prospectIds
      // fixme status [FOR FUTURE]: exports should have a different permission check: not needed to check EDIT permission in that case

      if (data.prospect_ids.isDefined) {

        val prospectUuIdsUniq = data.prospect_ids.get.distinct.map(prospectUuids => ProspectUuid(prospectUuids))
        prospectService.getProspectIdsFromUuid(
          data = prospectUuIdsUniq,
          teamId = TeamId(id=teamId)
        ) match {
          case Left(error) =>
            error match {
              case GetProspectIdError(error) => Future.failed(new Exception(s"Please try again: ${LogHelpers.getStackTraceAsString(error)}"))
            }
          case Right(value) =>
            val validProspectIds = value.valid_prospect_ids.map(ProspectId=>ProspectId.id)
            val invalidProspectUuidIds = value.invalid_uuids

            if(invalidProspectUuidIds.nonEmpty){
              Future.failed(new Throwable("Invalid Prospect Ids Passed"))
            }
            if(isApiCall){
              Future.successful(validProspectIds)
            }
            else {
              prospectDAOServiceV2.checkIfUserHasPermissionForProspects(
                prospectsIds = validProspectIds,
                permittedAccountIds = permittedAccountIdsForEditingProspects,
                teamId = teamId
              ) match {

                case Failure(e) =>

                  Future.failed(
                    new Exception(s"Please try again: ${LogHelpers.getStackTraceAsString(e)}")
                  )

                case Success(prospectIdsWithPermission) =>

                  if (validProspectIds.length != prospectIdsWithPermission.length) {
                    Logger.fatal(s"IGNORING Prospect.checkIfUserHasPermissionForProspects: prospectIdsUniq.length != prospectIdsWithPermission.length ${validProspectIds.length} != ${prospectIdsWithPermission.length}")
                  }

                  Future.successful(
                    prospectIdsWithPermission
                  )

              }
            }

        }


      } else Future.successful(
        Seq()
      )
    }
  }

  def getProspectIdsFromUuids(
                              data: ProspectBatchActions,
                              teamId: Long,
                             )(using Logger: SRLogger): Either[BatchActionErrors,Seq[Long]] = {
    if (data.prospect_ids.isDefined) {

      val prospectUuIdsUniq = data.prospect_ids.get.distinct.map(prospectUuids => ProspectUuid(prospectUuids))
      prospectService.getProspectIdsFromUuid(
        data = prospectUuIdsUniq,
        teamId = TeamId(teamId)
      ) match {
        case Left(error) =>
          error match {
            case GetProspectIdError(error) => Left(BatchActionErrors.ServerError(error))
          }
        case Right(value) =>
          val validProspectIds = value.valid_prospect_ids.map(ProspectId => ProspectId.id)
          val invalidProspectUuidIds = value.invalid_uuids

          if (invalidProspectUuidIds.nonEmpty) {
            Left(BatchActionErrors.BadRequestError("InvalidUuids Passed"))
          } else {
            Right(validProspectIds)
          }
      }
    } else {
      Left(BatchActionErrors.BadRequestError("ProspectIds not define for status change"))
    }
  }

  def updateCategory(
                      data: ProspectBatchActionUpdateCategory,
                      teamId: Long,
                      loginAccount: Account,
                      prospectIds: Seq[Long],
                      Logger: SRLogger,
                      permittedAccountIds: Seq[Long],
                      auditRequestLogId: String
                    ): Either[BatchActionErrors, BatchActionResponse] = {

      val category_custom_id = data.category_id

      prospectDAOService.findProspectCategoryById(team_id = teamId, category_custom_id = category_custom_id) match {

        case Failure(e) =>

          Left(BatchActionErrors.ServerError(e))

        case Success(None) =>

          Logger.error(s"FATAL ProspectController.updateBatch findProspectCategoryById Invalid category_custom_id: $category_custom_id")

          Left(BatchActionErrors.BadRequestError("Invalid category id"))


        case Success(Some(pc)) =>

          prospectServiceV2.fetchProspectsByIdsForBatchUpdate(
            prospectsIds = prospectIds,
            permittedAccountIds = permittedAccountIds,
            teamId = teamId,
            logger = Logger
          ) match {

            case Failure(e) =>

              Left(BatchActionErrors.ServerError(e))

            case Success(prospects) =>
                Logger.info(s"prospectServiceV2.fetchProspectsByIdsForBatchUpdate found prospects")

              prospectUpdateCategoryTemp.updateBatchCategory(
                prospects = prospects,
                doerAccountId = loginAccount.internal_id,
                accountName = Helpers.getAccountName(a = loginAccount),
                teamId = teamId,
                prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AdminUpdate(
                  new_prospect_category_id = ProspectCategoryId(id = category_custom_id),
                  old_prospect_category_id = None
                ),
                account = loginAccount,
                logger = Logger,
                auditRequestLogId = Some(auditRequestLogId)) match {

                case Failure(e) => Left(BatchActionErrors.ServerError(e))

                case Success(_) =>
                  Right(
                    BatchActionResponse(
                      responseMsg=s"Prospect status has been updated!",
                      responseObj =Json.obj()
                    )
                  )

              }
          }

      }
  }

  def deleteProspect(
                       teamId: Long,
                       prospectIds: Seq[Long],
                       Logger: SRLogger,
                       permittedAccountIds: Seq[Long],
                     ): Either[BatchActionErrors, BatchActionResponse] = {
    if (prospectIds.length > 10000) {
      Logger.warn(s"prospect_delete : >10000 user trying to delete ${prospectIds.length} prospects")
    } else {
      Logger.info(s"prospect_delete : user trying to delete ${prospectIds.length} prospects")
    }

    if (prospectIds.size > 5) {

      // account should have permission to delete the prospects
      prospectDAOService.scheduleForDeletion(
        prospectIds = prospectIds.map(pid => ProspectId(id = pid)),
        permittedAccountIds = permittedAccountIds,
        teamId = TeamId(id = teamId),
        deletionReason = ProspectDeletionReason.ByUser,
        logger = Logger
      ) match {

        case Failure(e) =>
          Left(BatchActionErrors.ServerError(e))

        case Success(stats) =>


          for (pid <- stats._2) {

            mqProspectDeleter.publish(message = MQProspectDeleterMsg(prospectId = pid, teamId = teamId))

          }

          Logger.info(s"[updateBatch] [ProspectDeleteAPI] ta ($teamId) :: requested (${prospectIds.length}) :: deleting (${stats._1})")

          Right(
            BatchActionResponse(
              responseMsg = s"${stats._1} prospects have been scheduled for deletion. They should get deleted within a minute",
              responseObj = Json.obj("total_deleted" -> 0, "total_scheduled" -> stats._1)
            )
          )
      }


    } else {

      // account should have permission to delete the prospects
      prospectDAOService.delete(
        prospectIds = prospectIds.map(p => ProspectId(id = p)).toList,
        permittedAccountIds = permittedAccountIds,
        teamId = TeamId(id = teamId),
        logger = Logger
      ) match {
        case Failure(e) =>
          Left(BatchActionErrors.ServerError(e))

        case Success(total_deleted) =>

          Logger.info(s"[updateBatch] [ProspectDeleteAPI] account ($teamId) :: requested (${prospectIds.length}) :: deleting (${total_deleted}) :: immediately")


          Right(
            BatchActionResponse(
              responseMsg = s"${total_deleted} prospects have been deleted",
              responseObj = Json.obj("total_deleted" -> total_deleted, "total_scheduled" -> 0)
            )
          )
      }

    }
  }

  def assignToCampaign(
                        data: ProspectBatchActionAssignToCampaign,
                        teamId: Long,
                        userId: Long,
                        loginAccount: Account,
                        prospectIds: Seq[Long],
                        permittedAccountIdsForEditingProspects: Seq[Long],
                        permittedAccountIdsForEditingCampaigns: Seq[Long]
                      )(using Logger:SRLogger): Either[BatchActionErrors, BatchActionResponse] = {
    CampaignService.getIgnoreProspectsInOtherCampaigns(
      ignore_prospects_in_other_campaigns = None,
      force_assign = data.force_assign
    ) match {
      case Failure(_) =>
        Logger.error(s"Wrong data sent for ignore_prospects_in_other_campaigns  data sent is - ignore_prospects_in_other_campaigns - ${data.ignore_prospects_in_other_campaigns}")
        Left(BatchActionErrors.BadRequestError(s"Error while Assigning Campaign. Please contact us "))
      case Success(ignore_prospects_in_other_campaigns) =>
        campaignService.getCampaignIdsFromUuid(
          data = List(CampaignUuid(data.campaign_id.get)),
          teamId = TeamId(id=teamId)
        ) match {
          case Left(error) =>
            error match {
              case GetCampaignIdFromUuidError.GetCampaignIdError(err2) =>
                Left(BatchActionErrors.ServerError(err2))
            }
          case Right(campaignIds) =>

            //Fixme: revisit this and refactor to throw error if invalid ids found
            val valid_campaigns_ids: List[CampaignId] = campaignIds.valid_campaigns_ids.filter(p => p._2.isDefined).map(p => p._2.get).toList

            val res = prospectService.assignProspectsToCampaign( //change here too
              teamId = teamId,
              campaignId = Option(valid_campaigns_ids.head.id),
              prospectIds = prospectIds,
              permittedAccountIdsForEditingCampaigns = permittedAccountIdsForEditingCampaigns,
              permittedAccountIdsForEditingProspects = permittedAccountIdsForEditingProspects,
              account = loginAccount,
              ignore_prospects_in_other_campaigns = ignore_prospects_in_other_campaigns,
              accountId = userId,
              Logger = Logger
            )

            res match {
              case Left(e: AssignProspectsToCampaignError) =>
                e match {
                  case AssignProspectsToCampaignError.CampaignFetchError(error) =>
                    Left(BatchActionErrors.BadRequestError(error))
                  case AssignProspectsToCampaignError.ProspectIsAlreadyAssigned(error) =>
                    Left(BatchActionErrors.BadRequestError(error))
                  case AssignProspectsToCampaignError.ProspectNotFoundError(error) =>
                    Left(BatchActionErrors.BadRequestError(error))
                  case AssignProspectsToCampaignError.CampaignNotFoundError(error) =>
                    Left(BatchActionErrors.BadRequestError(error))
                  case AssignProspectsToCampaignError.ErrorWhileAssigningProspect(error) =>
                    Left(BatchActionErrors.BadRequestError(error))
                }
              case Right(r: AssignProspectsToCampaignResponse) =>
                Right(
                  BatchActionResponse(
                    responseMsg = r.responseMsg,
                    responseObj = Json.obj("total_assigned" -> r.assignedProspectIdsLength, "prospect_ids" -> r.assignedProspectIds, "campaign_id" -> r.campaignId)
                  )
                )
            }
        }
    }
  }

  def unAssignFromCampaign(
                            data: ProspectBatchActionUnAssignFromCampaign,
                            teamId: Long,
                            userId: Long,
                            prospectIds: Seq[Long],
                            doerAccountName: String,
                            permittedAccountIds: Seq[Long],
                            permittedAccountIdsForEditingCampaigns: Seq[Long]
                          )(using Logger:SRLogger): Either[BatchActionErrors, BatchActionResponse] = {
    if (data.campaign_id.isDefined) {
      campaignService.getCampaignIdsFromUuid(
        data = List(CampaignUuid(data.campaign_id.get)),
        teamId = TeamId(id = teamId)
      ) match {
        case Left(error) =>
          error match {
            case GetCampaignIdFromUuidError.GetCampaignIdError(err2) =>
              Left(BatchActionErrors.ServerError(err2))
          }
        case Right(campaignIds) =>

          //Fixme: revisit this and refactor to throw error if invalid ids found
          val valid_campaigns_ids: List[CampaignId] = campaignIds.valid_campaigns_ids.filter(p => p._2.isDefined).map(p => p._2.get).toList

          campaignService.findBasicDetailsWithPermission(
            id = valid_campaigns_ids.head.id,
            permittedAccountIds = permittedAccountIdsForEditingCampaigns,
            teamId = teamId,
          ) match {

            case Failure(e) =>

              Left(BatchActionErrors.ServerError(e))

            case Success(None) =>

              Left(BatchActionErrors.BadRequestError("Campaign with given id not found"))

            case Success(Some(campaign)) =>
              val valid_campaigns_ids: List[CampaignId] = campaignIds.valid_campaigns_ids.filter(p => p._2.isDefined).map(p => p._2.get).toList
              campaignProspectService.unassign(
                permittedOwnerIds = permittedAccountIds,
                doerAccountId = userId,
                teamId = teamId,
                doerAccountName = doerAccountName,
                campaignId = valid_campaigns_ids.head.id,
                prospectIds = prospectIds,
                Logger = Logger
              ) match {

                case Failure(e) => Left(BatchActionErrors.BadRequestError("Error while unassigning prospects: " + e.getMessage))

                case Success(total) =>
                  Right(
                    BatchActionResponse(
                      responseMsg = s"$total prospects have been unassigned from campaign '${campaign.name}'",
                      responseObj = Json.obj("total_unassigned" -> total)
                    )
                  )
              }
          }
      }

    } else {

      Left(BatchActionErrors.BadRequestError("Please provide campaign_id"))

    }
  }

  def addTags(
               data: ProspectBatchActionAddOrRemoveTag,
               teamId: Long,
               userId: Long,
               prospectIds: Seq[Long],
               Logger: SRLogger
             ): Either[BatchActionErrors, BatchActionResponse] = {
    if (data.tags.isEmpty) {

      Left(BatchActionErrors.BadRequestError("Please provide tags"))

    } else {

      val tagsAndUuids: Seq[TagAndUuid] = data.tags.get.map(t => {
        val camapaign_tag_uuid = srUuidUtils.generateTagsUuid()
        TagAndUuid(tag = t, uuid = CampaignTagUuid(camapaign_tag_uuid))
      })

      prospectTagService.addTagsToObjects(
        objectIds = prospectIds,
        tags = tagsAndUuids,
        teamId = teamId,
        accountId = userId,
        logger = Logger.appendLogRequestId("prospectTagService.addTagsToObjects")
      ) match {

        case Failure(e) => Left(BatchActionErrors.ServerError(e))

        case Success(_) =>
          Right(
            BatchActionResponse(
              responseMsg = s"Tags added successfully",
              responseObj =Json.obj()
            )
          )
      }

    }
  }

  def removeTags(
                  data: ProspectBatchActionAddOrRemoveTag,
                  teamId: Long,
                  userId: Long,
                  prospectIds: Seq[Long],
                  Logger: SRLogger
                ): Either[BatchActionErrors, BatchActionResponse] = {
    if (data.tags.isEmpty) {

      Left(BatchActionErrors.BadRequestError("Please provide tags"))

    } else {

      prospectTagService.removeTagsFromObjects(
        objectIds = prospectIds,
        tags = data.tags.get,
        teamId = teamId,
        accountId = userId,
        logger = Logger.appendLogRequestId("prospectTagService.removeTagsFromObjects")
      ) match {

        case Failure(e) => Left(BatchActionErrors.ServerError(e))

        case Success(_) =>
          Right(
            BatchActionResponse(
              responseMsg = s"Tags removed successfully",
              responseObj = Json.obj()
            )
          )

      }

    }
  }

  def changeOwnerShip(
                       data: ProspectBatchActionChangeOwnerShip,
                       teamId: Long,
                       userId: Long,
                       prospectIds: Seq[Long],
                       Logger: SRLogger,
                       loginAccount: Account,
                       v: String
                     ): Either[BatchActionErrors, BatchActionResponse] = {
      generalModuleService.changeOwnership(
        module = "prospects",
        team_id = teamId,
        user_id = userId,
        owner_id = data.owner_id,
        ids = prospectIds,
        loggedinAccount = loginAccount,
        version = v,
        Logger = Logger
      ) match {

        case Failure(e) => Left(BatchActionErrors.ServerError(e))

        case Success(updatedIds) =>
          Right(
            BatchActionResponse(
              responseMsg = s"ownership changed successfully",
              responseObj = Json.obj("updated_ids" -> updatedIds.updated_ids)
            )
          )
      }
  }

  def batchActionExport(
                         teamId: Long,
                         teamName: String,
                         prospectIds: Seq[Long],
                         
                       )(using Logger: SRLogger, fm: FileMimeTypes, ec: ExecutionContext): Either[BatchActionErrors, Result] = {
    Helpers.logTimeTaken(
      Logger = Logger.appendLogRequestId(s"prospectDAO.findForExport prospectIdCount: ${prospectIds.length}")
    ) {

      prospectDAOService.findForExport(
        prospectIds = prospectIds, teamId = teamId,
      )
    } match {

      case Failure(e) => Left(BatchActionErrors.ServerError(e))

      case Success(rows) =>

        val tName = teamName

        val filename = URLEncoder.encode(s"prospects_${tName}_${DateTime.now().getMillis}", "UTF-8") + "_export.csv"

        val f = SingletonTemporaryFileCreator.create(new File("/tmp/" + filename).toPath)

        val writer = CSVWriter.open(f.toFile)

        val customColumns = prospectColumnDef.findCustomColumns(teamId = teamId)
          .map(col => {
            val displayName = col.name.replace("_", " ").capitalize
            val key = col.name

            (displayName, key)
          })
          .toList

        val csvHeaders = List(
          "Email",
          "First name",
          "Last name",
          "Prospect owner",
          "Account name",

          "Prospect Category",
          //                              "Current email step",
          "Tags",
          "List",

          "Company",
          "City",
          "State",
          "Phone",

          "Job title",
          "LinkedIn url",
          "Country",
          "Time zone",

          "Source",
          "Added on",

          "Last contacted at",
          "Last replied at",
          "Last opened at",

          "Email opens",
          "Email link clicks"
        ) ++ customColumns.map(_._1)

        writer.writeRow(csvHeaders)


        rows.foreach(r => {

          val customColData = customColumns.map(_._2).map(col => {

            // check and parse for String value, then Int value, then Float value

            val strVal = (r.custom_fields \ col).asOpt[String]

            if (strVal.isDefined) strVal.getOrElse("")
            else {

              val intVal = (r.custom_fields \ col).asOpt[Int].map(_.toString)

              if (intVal.isDefined) intVal.getOrElse("")
              else {

                val floatVal = (r.custom_fields \ col).asOpt[Float].map(_.toString)

                if (floatVal.isDefined) floatVal.getOrElse("") else ""

              }

            }
          })

          val tags = if (r.internal.tags.isDefined) r.internal.tags.get.map(_.tag).mkString(", ") else "";
          val row = List(
            r.email.getOrElse(""), //TODO: EMAIL_OPTIONAL prospectDAOService.findForExport is having inner join prospect_emails so email will not be empty
            r.first_name.getOrElse(""),
            r.last_name.getOrElse(""),
            r.internal.owner_name,
            r.internal.prospect_account.getOrElse(""),

            r.prospect_category,
            //                                r.internal.c //email step pending
            tags,
            r.list.getOrElse(""),

            r.company.getOrElse(""),
            r.city.getOrElse(""),
            r.state.getOrElse(""),
            r.phone.getOrElse(""),

            r.job_title.getOrElse(""),
            r.linkedin_url.getOrElse(""),
            r.country.getOrElse(""),
            r.timezone.getOrElse(""),

            r.internal.prospect_source.getOrElse(""),
            r.created_at,

            r.internal.last_contacted_at.getOrElse(""),
            r.internal.last_replied_at.getOrElse(""),
            r.internal.last_opened_at.getOrElse(""),

            r.internal.total_opens,
            r.internal.total_clicks
          ) ++ customColData

          writer.writeRow(row)

        })

        writer.close()


        Right(Ok
          .sendFile(f.toFile, onClose = () => {
            f.delete()
          })
          .withHeaders(
            "Content-Disposition" -> s"attachment;filename=$filename",
            "Access-Control-Expose-Headers" -> "Content-Disposition"
          )
          .as("text/csv"))

    }
  }

  def campaignPauseUnPauseForProspect(
                                       data: ProspectBatchActionCampaignPauseUnpauseForProspect,
                                       teamId: Long,
                                       userId: Long,
                                       prospectIds: Seq[Long],
                                       permittedAccountIds: Seq[Long]
                                     )(using Logger:SRLogger): Either[BatchActionErrors, BatchActionResponse] = {
    campaignService.getCampaignIdsFromUuid(
      data = data.campaign_ids.map(campaignId => CampaignUuid(campaignId)).toList,
      teamId = TeamId(id = teamId)
    ) match {
      case Left(error) =>
        error match {
              case GetCampaignIdFromUuidError.GetCampaignIdError(err2) =>
                Left(BatchActionErrors.ServerError(err2))
        }
      case Right(campaign_ids) =>
        if (campaign_ids.valid_campaigns_ids.isEmpty) {

          Left(BatchActionErrors.BadRequestError("Please provide campaign_id"))

        } else {
          val valid_campaigns_ids: List[Long] = campaign_ids.valid_campaigns_ids.filter(p => p._2.isDefined).map(p => p._2.get.id).toList
          val campaignsDb = campaignService.findBasicDetailsBatch(
            ids = valid_campaigns_ids,
            teamId = teamId
          )


          if (campaignsDb.length != valid_campaigns_ids.length) {

            Logger.fatal(s"You do not have enough permission to edit the campaign")

            Left(BatchActionErrors.BadRequestError("You do not have enough permission to edit the campaign"))

          } else {
            // account should own the prospects to be deleted
            campaignProspectDAOService.pauseStatusChangedByAdmin(
              permittedAccountIds = permittedAccountIds,
              statusChangedByAccountId = AccountId(userId),
              teamId = teamId,
              prospectEmails = Seq(),
              prospectIds = prospectIds,
              newPauseStatus = data.new_pause_status,
              campaignIds = valid_campaigns_ids,
              Logger = Logger
            ) match {


              case Failure(e) =>
                Left(BatchActionErrors.ServerError(e))

              case Success(cpCompleteds) =>

                val publishWebhook = mqWebhookCompleted.publishCompletedProspects(
                  accountId = userId,
                  teamId = teamId,
                  cpCompleteds = cpCompleteds
                )

                publishWebhook match {

                  case Failure(e1) =>
                    Left(BatchActionErrors.ServerError(e1))

                  case Success(_) =>

                    val total = cpCompleteds.length
                    // val msg = s"Campaign has been ${if (data.new_pause_status) "paused" else "unpaused"} for ${if (total == 1) "prospect" else s"$total prospects"}"
                    val msg = s"Campaign has been ${if (data.new_pause_status) "completed" else "resumed"} for selected prospects"

                    //                Res.Success(s"$total ${if (total == 1) "prospect has" else "prospects have"} been ${if (data.new_pause_status) "paused" else "unpaused"}", Json.obj("total_status_changed" -> total))
                    Right(
                      BatchActionResponse(
                        responseMsg = msg,
                        responseObj = Json.obj("total_status_changed" -> total)
                      )
                    )

                }


            }
          }
        }
    }
  }

  def prospectAccount(
                       data: ProspectBatchActionProspectAccount,
                       teamId: Long,
                       prospectIds: Seq[Long],
                       Logger: SRLogger,
                       permittedAccountIds: Seq[Long]
                     ): Either[BatchActionErrors, BatchActionResponse] = {
      val prospect_account_id = data.prospect_account_id

      prospectAccountDAO.find(id = prospect_account_id, teamId = teamId) match {

        case None =>

          Logger.error(s"FATAL ProspectController.updateBatch prospectAccountDAO.find Invalid account id: $prospect_account_id")

          Left(BatchActionErrors.BadRequestError("Invalid account id"))

        case Some(pa) =>

          prospectServiceV2.fetchProspectsByIdsForBatchUpdate(
            prospectsIds = prospectIds,
            permittedAccountIds = permittedAccountIds,
            teamId = teamId,
            logger = Logger
          ) match {

            case Failure(e) =>

              Left(BatchActionErrors.ServerError(e))

            case Success(prospects) =>

              prospectDAOService.updateBatchAccount(
                prospects = prospects,
                newProspectAccountId = prospect_account_id
              ) match {

                case Failure(e) => Left(BatchActionErrors.ServerError(e))

                case Success(_) =>
                  Right(
                    BatchActionResponse(
                      responseMsg = s"Account has been updated!",
                      responseObj = Json.obj()
                    )
                  )
              }
          }

      }
  }

  def validateDataForUpdatingProspectStatus(
                                             data: ProspectBatchActionStatusChange,
                                             teamId: TeamId,
                                             orgId: OrgId,
                                             permittedAccountIds: Seq[Long],
                                             prospectIds: Seq[Long],
                                             isApiCall: Boolean,
                                           )(using Logger: SRLogger): Either[UpdateProspectStatusErrorV3, UpdateProspectStatusValidationResult] = {
    var error_list: List[ErrorResponseUpdateProspectStatusApi] = List()
    error_list = error_list ++ ProspectService.validateUpdateProspectsStatusData(
      isApiCall = isApiCall,
      data = data,
      prospectIds = prospectIds
    )

    if (error_list.isEmpty) {
      campaignService.getCampaignIdsFromUuid(
        data = data.campaign_ids.map(campaignId => CampaignUuid(campaignId)).toList,
        teamId  = teamId
      ) match {
        case Left(error) =>
          Left(UpdateProspectStatusErrorV3.GetCampaignIdFromUuidErrors(error))

        case Right(campaign_ids) =>

          if (campaign_ids.invalid_uuids.nonEmpty) {

            Left(UpdateProspectStatusErrorV3.UpdateProspectsStatusError("Please provide valid campaign_id"))

          } else {
            val valid_campaigns_ids: List[Long] = campaign_ids.valid_campaigns_ids.filter(p => p._2.isDefined).map(p => p._2.get.id).toList
            val campaignsDb = campaignService.findBasicDetailsBatch(
              ids = valid_campaigns_ids,
              teamId = teamId.id
            )

            if (campaignsDb.length != valid_campaigns_ids.length) {

              Logger.fatal(s"You do not have enough permission to edit the campaign")

              Left(UpdateProspectStatusErrorV3.UpdateProspectsStatusError("You do not have enough permission to edit the campaign"))

            } else {
              prospectServiceV2.filterOwnedProspects(
                prospectIds = prospectIds,
                permittedAccountIds = permittedAccountIds,
                teamId = teamId.id,
                orgId = orgId,
                SRLogger = Logger
              ) match {

                case Failure(exception) => Left(UpdateProspectStatusErrorV3.FilterOwnedProspectError(err = exception))

                case Success(accountOwnedProspects: List[OwnedProspectsForAssigning]) =>
                  if (prospectIds.length != accountOwnedProspects.length) {

                    val msg = "Given prospects do not exist in your account"

                    // REF: https://stackoverflow.com/a/********
                    val accountOwnedProspectsSet = accountOwnedProspects.map(_.prospect_id).toSet
                    val prospectIdsThatAreNotOwned: List[ProspectId] = prospectIds.filterNot(accountOwnedProspectsSet).map(ProspectId(_)).toList


                    prospectService.getProspectUuidFromId(
                      data = prospectIdsThatAreNotOwned,
                      teamId = teamId
                    ) match {

                      case Failure(exception) =>
                        Logger.fatal(s"Prospect.updateStatus Error while getting prospect uuid from id", err = exception)
                        Left(UpdateProspectStatusErrorV3.GetProspectUuidFromId(err = exception))

                      case Success(notOwnedProspectUuids) =>
                        Logger.fatal(s"Prospect.updateStatus doNotExistError: $msg :: prospectIds.length: ${prospectIds.length} :: ${accountOwnedProspects.length} :: prospectIdsThatAreNotOwnedCount: ${prospectIdsThatAreNotOwned.length} :: prospectIdsCount : ${prospectIds.length} :: accountOwnedProspectIdsCount: ${accountOwnedProspects.map(_.prospect_id).length}")

                        val err = ErrorResponseUpdateProspectStatusApi(
                          message = "Given prospects do not exist in your account",
                          data = Some(notOwnedProspectUuids.mkString(",")),
                          error_type = UpdateProspectStatusErrorType.FORBIDDEN
                        )

                        error_list = error_list.appended(err)
                        Left(UpdateProspectStatusErrorV3.UpdateProspectsStatusValidationError(err = error_list))

                    }


                  } else {
                    Try {
                      valid_campaigns_ids.map(campaignId => {
                        campaignProspectDAO.getProspectsInCampaignFromGivenProspects(
                          prospectIds = prospectIds,
                          campaignId = CampaignId(campaignId),
                          teamId = teamId
                        ) match {
                          case Failure(exception) =>
                            throw new Throwable("FilterCampaignOwnedProspectError", exception)

                          case Success(prospectsInCampaigns) =>
                            if (prospectsInCampaigns.length!=prospectIds.length) {

                              val msg = "Given prospects do not exist in your campaign"

                              val prospectNotInCampaign: Seq[Long] = prospectIds.filter(!prospectsInCampaigns.contains(_))
                              Try {
                                prospectService.getProspectUuidFromId(
                                  data = prospectNotInCampaign.map(p => ProspectId(p)).toList,
                                  teamId = teamId
                                ) match {

                                  case Failure(exception) =>
                                    Logger.fatal(s"Campaign.updateStatus Error while getting prospect uuid from id", err = exception)
                                    throw new Throwable("GetProspectUuidFromId", exception)

                                  case Success(notOwnedProspectUuids) =>
                                    Logger.fatal(s"Campaign.updateStatus doNotExistError: $msg :: prospectIds.length: ${prospectIds.length} :: ${prospectsInCampaigns.length} :: prospectIdsThatAreNotOwnedCount: ${prospectNotInCampaign.length} :: prospectIdsCount : ${prospectIds.length} :: accountOwnedProspectIdsCount: ${accountOwnedProspects.map(_.prospect_id).length}")
                                    val err = ErrorResponseUpdateProspectStatusApi(
                                      message = s"${msg} : ${campaign_ids.valid_campaigns_ids.find(_._2.get.id == campaignId).map(_._1)}",
                                      data = Some(notOwnedProspectUuids.mkString(",")),
                                      error_type = UpdateProspectStatusErrorType.FORBIDDEN
                                    )

                                    error_list = error_list.appended(err)

                                }
                              } match {
                                case Failure(exception) =>
                                  throw exception
                                case Success(_) =>
                                  None
                              }

                            }
                        }
                      }
                      )
                    } match {
                      case Failure(exception) =>
                        if(exception.getMessage.equals("GetProspectUuidFromId")){
                          Left(UpdateProspectStatusErrorV3.GetProspectUuidFromId(exception))
                        } else {
                          Left(UpdateProspectStatusErrorV3.FilterCampaignOwnedProspectError(exception))
                        }
                      case _ =>
                        if (error_list.isEmpty) {
                          Right(UpdateProspectStatusValidationResult(
                            campaign_ids = valid_campaigns_ids,
                            prospect_status = data.prospect_status,
                            will_resume_at = data.will_resume_at,
                            will_resume_at_tz = data.will_resume_at_tz
                          ))
                        }
                        else {
                          Left(UpdateProspectStatusErrorV3.UpdateProspectsStatusValidationError(err = error_list))
                        }
                    }
                  }
              }
            }
          }
      }

    }
    else {
      Left(UpdateProspectStatusErrorV3.UpdateProspectsStatusValidationError(err = error_list))
    }
  }


  def prospectBatchActionStatusChange(
                                       data: ProspectBatchActionStatusChange,
                                       teamId: Long,
                                       orgId: OrgId,
                                       prospectIds: Seq[Long],
                                       doerAccountId: Long,
                                       replyHandling: ReplyHandling,
                                       doerAccountName: String,
                                       permittedAccountIds: Seq[Long],
                                       isApiCall: Boolean
                                     )(using Logger:SRLogger): Either[UpdateProspectStatusErrorV3, BatchActionResponse] = {
    validateDataForUpdatingProspectStatus(
      data = data,
      teamId = TeamId(id = teamId),
      orgId = orgId,
      permittedAccountIds = permittedAccountIds,
      prospectIds = prospectIds,
      isApiCall = isApiCall
    ) match {
      case Left(error) => Left(error)
      case Right(validationResult: UpdateProspectStatusValidationResult) =>

        val newProspectStatus = NewProspectStatus.fromKey(data.prospect_status).get

        val action_happened_at = DateTime.now()

        val campaignIds = validationResult.campaign_ids

        val prospectIdsDistinct = prospectIds.distinct


        val prospectsToStatusUpdated: Seq[CPTuple] = prospectIdsDistinct.flatMap(p => {
          campaignIds.map(c => {
            CPTuple(
              prospect_id = p,
              campaign_id = c
            )
          })
        })

        val campaignsDb = campaignService.findBasicDetailsBatch(
          ids = campaignIds,
          teamId = teamId
        )

        val publishWebhook: Try[Any] = newProspectStatus match {

          case NewProspectStatus.REPLIED =>

            mqRepliedEvent.publishRepliedProspects(msg = MQRepliedEventMsg(
              accountId = doerAccountId,
              teamId = teamId,
              cpReplieds = prospectsToStatusUpdated.map(u => {
                val campaignBasic = campaignsDb.find(c => c.campaign_id == u.campaign_id)
                CPRepliedEvent(
                  campaignId = u.campaign_id,
                  prospectId = u.prospect_id,
                  replied_at = action_happened_at,
                  campaignName = campaignBasic.map(_.campaign_name)
                )
              }),
              reply_handling = replyHandling.toString,
              doerAccountName = doerAccountName,
              permittedAccountIds = permittedAccountIds,
              logRequestId = Logger.logRequestId
            ))

          case NewProspectStatus.PAUSE =>

            mqProspectCompletedEvent.publishCompletedProspects(
              accountId = doerAccountId,
              teamId = teamId,
              cpCompleteds = prospectsToStatusUpdated.map(u => {
                val campaignBasic = campaignsDb.find(c => c.campaign_id == u.campaign_id)
                CPCompletedEvent(
                  campaignId = u.campaign_id,
                  prospectId = u.prospect_id,
                  completed_at = action_happened_at,
                  campaignName = campaignBasic.map(_.campaign_name)
                )
              }),
              reply_handling = replyHandling,
              isFromRepliedFlow = false,
              permittedAccountIds = permittedAccountIds,
              completed_reason = CampaignProspectCompletedReason.NewProspectStatusPause,
              SRLogger = Logger
            )

          case NewProspectStatus.UNPAUSE =>

            mqProspectUnPauseEvent.publishUnpauseProspects(
              accountId = doerAccountId,
              teamId = teamId,
              permittedAccountIds = permittedAccountIds,
              cpUnpaused = prospectsToStatusUpdated.map(u => {
                val campaignBasic = campaignsDb.find(c => c.campaign_id == u.campaign_id)
                CPUnPausedEvent(
                  campaignId = u.campaign_id,
                  prospectId = u.prospect_id,
                  unpaused_at = action_happened_at,
                  campaignName = campaignBasic.map(_.campaign_name)
                )
              }),
              Logger = Logger
            )

          case NewProspectStatus.RESUME_LATER =>

            val willResumeAt = data.will_resume_at
            val willResumeAtTimezone: Option[String] = data.will_resume_at_tz

            campaignService.setProspectsToResumeLater(
              willResumeAt = willResumeAt.get,
              willResumeAtTimezone = willResumeAtTimezone.get,
              campaignProspects = prospectsToStatusUpdated,
              willResumeAtUpdatedBy = WillResumeAtUpdatedBy.User,
              permittedAccountIdsForProspects = permittedAccountIds,
              teamId = teamId,
              logger = Logger
            )

        }

        publishWebhook match {

          case Failure(e1: BadRequestErrorException) =>
            Left(UpdateProspectStatusErrorV3.UpdateProspectsStatusError(e1.getMessage))

          case Failure(e1) =>
            Left(UpdateProspectStatusErrorV3.ServerError(e1))

          case Success(_) =>
            apiService.prospectBatchActionStatusApiResponse(
              prospectsToStatusUpdated = prospectsToStatusUpdated,
              teamId = TeamId(id = teamId)
            ) match {
              case Left(errors) => Left(errors)
              case Right(value) => Right(value)
            }
        }
    }
  }

}
