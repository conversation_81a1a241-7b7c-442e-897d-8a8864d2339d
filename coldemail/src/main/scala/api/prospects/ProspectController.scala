package api.prospects

import api.APIErrorResponse.ErrorResponseUpdateProspectStatusApi

import java.io.File
import java.net.URLEncoder
import api.accounts.*
import api.accounts.models.{AccountId, OrgId}
import api.accounts.service.{OrganizationService, ResetUserCacheUtil}
import api.call.models.NavigationLinksPrevNextResponse
import api.campaigns.*
import api.campaigns.models.{CampaignProspectCompletedReason, GetCampaignIdFromSrIdentifierError, NavigationLinksResponse, NewProspectStatus}
import api.campaigns.services.UpdateProspectStatusErrorV3.GetCampaignIdFromUuidErrors
import api.campaigns.services.{CalendlyWebhookService, CampaignId, CampaignProspectService,CampaignProspectDAOService, CampaignService, GetCampaignIdFromUuidError, GetProspectIdFromUuidError, UpdateProspectStatusErrorV3, WebhookSignatureValidationResult}
import api.columns.services.{EditMagicColumnGenerationData, ProspectColumnService}
import api.columns.{CustomColumnDefCreateForm, CustomColumnDefCreateMultipleForm, CustomColumnDeleteError, FieldTypeEnum, GenerateCustomColumnData, ProspectColumnDef, ProspectColumnDefUtils, UpdateMagicPromptForm}
import api.emails.*
import api.emails.dao_service.EmailThreadDAOService
import api.generalmodule.GeneralModuleService
import api.linkedin_message_threads.LinkedinMessageThreadsDAO
import api.prospects.dao.ReorderProspectCategoryData
import api.prospects.dao_service.ProspectDAOService
import api.prospects.service.{ProspectEventsListingPaginationError, ProspectEventsListingPaginationService, ProspectEventsListingResponse, ProspectServiceV2, ProspectServiceWithEmailValidation, ProspectsListingPaginationService, UpdateProspectStatusErrorType}
import api.prospects.models.{ProspectCategoryId, ProspectCategoryUpdateFlow, ProspectDeletionReason, ProspectId}
import api.rep_tracking_hosts.service.RepTrackingHostService
import api.search.RequestBodyValidationError.ValidationError
import api.search.{ProspectQuery, SearchQuery}
import api.tags.services.ProspectTagService
import api.{ApiService, ApiVersion, AppConfig, BadRequestErrorException, CONSTANTS, CreateOrUpdateProspectsForApiError}
import com.github.tototoshi.csv.CSVWriter
import eventframework.ProspectObject
import org.joda.time.DateTime
import play.api.libs.Files.{SingletonTemporaryFileCreator, logger}
import utils.{AppConfigUtil, Helpers, ParseUtils, SRLogger, StringUtils, StringUtilsV2}
import utils.logging.{SRInternalLog, SRLogStatus}
import utils.mq.srevents.{MQProspectCompletedEvent, MQProspectUnPauseEvent, MQRepliedEvent, MQRepliedEventMsg}
import utils.mq.webhook.*
import play.api.libs.json.*
import play.api.libs.json.JodaReads.*
import play.api.libs.ws.WSClient
import play.api.mvc.*
import utils.emailvalidation.EmailValidationService
import utils.mq.prospectdeleter.{MQProspectDeleter, MQProspectDeleterMsg}
import utils.GCP.CloudStorage
import sr_scheduler.models.ChannelType
import utils.helpers.LogHelpers
import api.sr_audit_logs.models.{CreateEventLogError, EventDataType, EventType}
import api.sr_audit_logs.services.EventLogService
import api.tags.models.{CampaignTagUuid, TagAndUuid}
import io.lemonlabs.uri.Url
import play.api.http.FileMimeTypes
import utils.cronjobs.AttemptRetryCronService
import utils.email_notification.service.EmailNotificationService
import utils.input_validations.SanitizeInputStringUtils
import utils.mq.webhook.model.TriggerSource
import utils.security.{EncryptionHelpers, SignatureValidationService}
import utils.uuid.{SrIdentifier, SrUuidUtils}
import utils.uuid.services.SrUuidService
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Either, Failure, Success, Try}


class ProspectController(
                          resetUserCacheUtil: ResetUserCacheUtil,
                          protected val controllerComponents: ControllerComponents,
                          val campaignService: CampaignService,
                          //                          campaignProspectService: CampaignProspectService,
                          campaignProspectDAO: CampaignProspectDAO,
                          campaignProspectDAOService: CampaignProspectDAOService,
                          override protected val srUuidService: SrUuidService,
                          //                          prospectTagService: ProspectTagService,
                          //                          prospectEventDAO: ProspectEventDAO,
                          //                          prospectEventService: ProspectEventService,
                          prospectUpdateCategoryTemp: ProspectUpdateCategoryTemp,
                          val prospectDAOService: ProspectDAOService,
                          val prospectService: ProspectService,
                          prospectServiceWithEmailValidation: ProspectServiceWithEmailValidation,
                          prospectBatchActionService: ProspectBatchActionService,
                          //                          generalModuleService: GeneralModuleService,
                          mqTrigger: MQTrigger,
                          //                          mqProspectUnPauseEvent: MQProspectUnPauseEvent,
                          //                          mqProspectCompletedEvent: MQProspectCompletedEvent,
                          //                          mqRepliedEvent: MQRepliedEvent,
                          prospectColumnDef: ProspectColumnDef,
                          mqWebhookCompleted: MQWebhookCompleted,
                          mqProspectDeleter: MQProspectDeleter,
                          emailNotificationService: EmailNotificationService,
                          override protected val emailThreadDAOService: EmailThreadDAOService,
                          override protected val linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO,
                          //                          prospectAccountDAO: ProspectAccountDAO1,
                          permissionUtils: PermissionUtils,
                          prospectColumnService: ProspectColumnService,
                          organizationService: OrganizationService,
                          //  authUtils: AuthUtils,
                          accountService: AccountService,
                          val prospectFeedDAO: ProspectFeedDAO,
                          prospectServiceV2: ProspectServiceV2,
                          prospectQueryService: ProspectQuery,
                          apiService: ApiService,
                          calendlyWebhookService: CalendlyWebhookService,
                          //                          srUuidUtils: SrUuidUtils,
                          repTrackingHostService: RepTrackingHostService,
                          prospectsListingPaginationService: ProspectsListingPaginationService,
                          prospectEventsListingPaginationService: ProspectEventsListingPaginationService,
                          srRollingUpdateCoreService: SrRollingUpdateCoreService,
                          implicit val wSClient: WSClient
                        ) extends BaseController
  with ProspectUtils with EmailUtils with SanitizeInputStringUtils {

  private implicit val ec: ExecutionContext = controllerComponents.executionContext
  private implicit val fm: FileMimeTypes = controllerComponents.fileMimeTypes

  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  // use case : ProspectController.findAll GET     /api/:v/prospects
  // [PUBLIC API]: Mentioned in [API DOCS]
  def findAll(v: String, page: Option[Int], onlyUnassigned: Option[Boolean], aid: Option[Long], tid: Option[Long]) =
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.VIEW_PROSPECTS,
      tidOpt = tid,
      apiAccess = true
    ).async { request =>
      Future {
        val Logger = request.Logger
        val Res = request.Response

        // for column defs
        // column should be associated with the team
        val t = request.actingTeamAccount


        if (t.isEmpty) {

          Res.BadRequestError("Invalid team")

        } else {

          val accountIds = request.permittedAccountIds
          if (onlyUnassigned.isDefined && onlyUnassigned.get) {

            Logger.error(s"FATAL ProspectController.findAll onlyUnassigned called, no longer supported: $accountIds :: ${t.get.team_id} :: $page")

            /*
            Prospect.findUnassigned(
              accountIds = accountIds,
              teamId = t.get.team_id,
              page = page.getOrElse(1)
            )
            */

            Res.BadRequestError("Invalid param onlyUnassigned")

          } else {

            val pageNo: Int = if (page.isDefined && page.get > 0) page.get else 1
            val limit = 500
            val offset = (pageNo - 1) * limit
            // PROSPECTS_EMAILS_TODO_READ_CLEANED
            prospectDAOService.find(
              permittedOwnerIds = Some(accountIds),
              teamId = t.get.team_id,
              limit = limit,
              offset = offset,
              Logger = Logger
            ) match {

              case Failure(e) =>

                Logger.error(s"FATAL ProspectController.findAll: $accountIds : ${t.get.team_id} :: ${LogHelpers.getStackTraceAsString(e)}")

                Res.ServerError("There was an error. Please try again.", e = Some(e))

              case Success(prospects) =>

                if (request.isApiCall) {

                  Res.Success("Prospects found", Json.obj(
                    "prospects" -> prospects.map(p => ProspectObject.apiStructure(prospect = p))
                  ))

                } else {


                  val allColumns = prospectColumnDef.allColumns(teamId = t.get.team_id, channel = None)

                  val json = Json.obj(
                    "columns" -> allColumns,
                    "prospects" -> prospects.map(p => {
                      ProspectObject.apiStructure(
                        prospect = p,
                        isInternalRequest = !request.isApiCall
                      )
                    })
                  )

                  Res.Success("Prospects found", json)
                }

            }


          }

        }
      }
    }

  /*def postFindAllWithSortingAndFiltering(aid: Option[Long], tid: Option[Long]) =
    checkPermission(
      permission = PermType.VIEW_PROSPECTS,
      aidOpt = aid,
      tidOpt = tid
    ).async(parse.json) { request =>


    if (tid.isEmpty) {

      Future.successful(Res.BadRequestError("Please send valid tid"))

    } else {
      val accountIds = request.showDataForAccountIds

      val isAllView = request.isAllView

      request.body.validate[GetProspects] match {

        case JsError(e) => Future.successful(JsValidationError(e))

        case JsSuccess(data, _) =>


          Prospect.searchV2(
            accountIds = accountIds,
            teamId = tid.get,
            loggedinAccountId = request.loggedinAccount.id,
            isAllView = isAllView,
            getProspects = data
          )
            .map { prospects =>
              Res.Success("Prospects found", prospects)
            }
            .recover { case e => ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e)) }

      }

    }
  }*/

  def getCampaignsForProspect(v: String, prospect_id: Long, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.VIEW_PROSPECTS,
      tidOpt = tid
    ) andThen hasProspect(prospect_id)
    ) { (request: ProspectRequest[AnyContent]) =>
    given Logger: SRLogger = request.Logger
    val Res = request.Response
    val teamId = TeamId(request.actingTeamAccount.team_id)

//    val emailNotCompulsoryEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//      teamId = TeamId(tid.get),
//      feature = SrRollingUpdateFeature.EmailNotCompulsory
//    )(Logger)

    campaignProspectDAO.findCampaignsForProspect(
      loggedinAccountId = request.loggedinAccount.internal_id,
      prospectIds = Seq(request.prospect.id),
      onlyActive = true, // only show campaigns that prospect is currently assigned to
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled,
      teamId = teamId
    ) match {
      case Failure(e) =>
        Res.ServerError("There was an error, please contact support.", e = Some(e))
      case Success(campaigns) =>
        Res.Success("Campaigns found", Json.obj(
          "campaigns" -> campaigns,
          "prospect_category" -> request.prospect.prospect_category
        ))
    }


  }

  def prospectQuery(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_PROSPECTS,
    tidOpt = tid
  ).async(parse.json) { request =>
    val Logger = request.Logger
    val Res = request.Response
    val isApiCall = request.isApiCall
    if (tid.isEmpty) {

      Future.successful(Res.BadRequestError("Please send valid tid"))

    } else {
      val accountIds = request.permittedAccountIds

      // val isAllView = request.isAllView

      request.body.validate[SearchQuery] match {

        case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

        case JsSuccess(data, _) =>

          prospectQueryService.validateRequestBody(
            searchQuery = data,
            isApiCall = isApiCall,
            Logger = Logger
          ) match {
            case Left(error) =>
              error match {
                case ValidationError(err) =>
                  Future {
                    Res.BadRequestError(
                      message = err
                    )
                  }
              }
            case Right(searchQuery) =>
              prospectDAOService.query(
                  accountIds = accountIds,
                  teamId = tid.get,
                  orgId = request.loggedinAccount.org.id,
                  loggedinAccountId = request.loggedinAccount.internal_id,
                  prospectQueryData = searchQuery,
                  account = request.loggedinAccount,
                  isInternalRequest = !request.isApiCall,
                  Logger = Logger
                )
                .map { prospects =>
                  Res.Success("Prospects found", prospects)
                }
                .recover { case e => Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e)) }
          }
      }

    }

  }

  def saveProspectFilters(v: String, aid: Option[Long], tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = tid
  )(parse.json) { (request: PermissionRequest[JsValue]) =>
    val Logger = request.Logger
    val Res = request.Response

    val teamAccount = request.actingTeamAccount
    if (teamAccount.isEmpty) {

      Res.BadRequestError("Please send valid tid")

    } else {

      request.body.validate[ProspectSavedFiltersInputQuery] match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>

          if (data.label.isEmpty) {

            Res.BadRequestError("Label cannot be empty")

          } else {

            ProspectSavedFilters.saveFilters(accountId = teamAccount.get.user_id, teamId = teamAccount.get.team_id, teamAccountId = teamAccount.get.ta_id, data = data) match {

              case Failure(e) =>
                Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))

              case Success(filters) =>
                Res.Success("Prospects filters saved", Json.obj(
                  "all_filters" -> filters
                ))
            }
          }
      }

    }

  }

  def findSavedProspectFilters(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_PROSPECTS,
    tidOpt = tid
  ).async { request =>
    val Logger = request.Logger
    val Res = request.Response

    Future {
      val teamAccount = request.actingTeamAccount

      val currentAccountId = request.loggedinAccount.internal_id
      if (teamAccount.isEmpty) {

        Res.BadRequestError("Please send valid tid")

      } else {

        ProspectSavedFilters.findSavedFilters(currentAccountId = currentAccountId, teamId = teamAccount.get.team_id) match {

          case Failure(e) =>
            Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))

          case Success(filters) =>
            Res.Success("Prospects filters found", Json.obj(
              "all_filters" -> filters
            ))
        }

      }
    }

  }


  def updateSavedProspectFilters(v: String, id: Int, aid: Option[Long], tid: Option[Long]) =
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_PROSPECTS,
      tidOpt = tid
    ).async(parse.json) { request =>
      given Logger: SRLogger= request.Logger
      val Res = request.Response

      Future {

        val teamAccount = request.actingTeamAccount

        if (teamAccount.isEmpty) {

          Res.BadRequestError("Please send valid tid")

        } else {

          request.body.validate[ProspectUpdateFiltersInputQuery] match {

            case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

            case JsSuccess(data, _) =>

              ProspectSavedFilters.updateSavedProspectFilter(
                id = data.id,
                filters = data.filters,
                label = data.label,
                shared_with_team = data.shared_with_team,
                teamId = teamAccount.get.team_id,
                permittedAccountIds = request.permittedAccountIds) match {

                case Failure(e) =>

                  Logger.error(s"FATAL DELETE deleteSavedProspectFilters Prospect.deleteSavedProspectFilter ${request.loggedinAccount.internal_id} :: SavedProspectFiltersId : $id :: ${LogHelpers.getStackTraceAsString(e)}")

                  Res.ServerError("There was an error. Please try again or contact support.", e = Some(e))

                case Success(filters) =>

                  resetUserCacheUtil._resetTeamCache(request.loggedinAccount.internal_id)

                  if (filters.nonEmpty) Res.Success("Updated saved prospect filter successfully", Json.obj(
                    "all_filters" -> filters
                  ))
                  else Res.NotFoundError(s"You don't have the permission to update this saved prospect filter. Could you please check and retry ?")

              }
          }
        }
      }

    }

  def deleteSavedProspectFilters(v: String, id: Int, aid: Option[Long], tid: Option[Long]) =
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_PROSPECTS,
      tidOpt = tid
    ).async { request =>
      given Logger: SRLogger= request.Logger
      val Res = request.Response

      Future {

        val teamAccount = request.actingTeamAccount

        if (teamAccount.isEmpty) {

          Res.BadRequestError("Please send valid tid")

        } else {

          Logger.info(s"DELETE deleteSavedProspectFilters called by ${request.loggedinAccount.internal_id} :: SavedProspectFiltersId : $id")

          ProspectSavedFilters.deleteSavedProspectFilter(savedProspectFilterId = id,
            permittedAccountIds = request.permittedAccountIds,
            teamId = teamAccount.get.team_id) match {

            case Failure(e) =>

              Logger.error(s"FATAL DELETE deleteSavedProspectFilters Prospect.deleteSavedProspectFilter ${request.loggedinAccount.internal_id} :: SavedProspectFiltersId : $id :: ${LogHelpers.getStackTraceAsString(e)}")

              Res.ServerError("There was an error. Please try again or contact support.", e = Some(e))

            case Success(n) =>

              resetUserCacheUtil._resetTeamCache(request.loggedinAccount.internal_id)

              if (n == 1) Res.Success(s"Deleted saved prospect filter successfully", data = Json.obj())
              else Res.NotFoundError(s"You don't have the permission to delete this saved prospect filter. Could you please check and retry ?")

          }
        }
      }

    }

  // use case : ProspectController.postFindByEmailForZapier POST    /api/:v/prospects/search_by_email_for_zapier
  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  // ZAPIER: used in search action
  def postFindByEmailForZapier(v: String): Action[JsValue] = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_PROSPECTS,
    apiAccess = true,
    webAccess = false
  )(parse.json) { (request: PermissionRequest[JsValue]) =>
    val Logger = request.Logger
    val Res = request.Response

    val email: Option[String] = (request.body \ "email").asOpt[String]

    Logger.info(msg = s"[Zapier API called] org_id :: ${request.loggedinAccount.org.id}")

    if (email.isEmpty) {

      Res.BadRequestError("Please send email to find")

    } else if (request.actingTeamAccount.isEmpty) {

      Res.ServerError(s"[${request.loggedinAccount.internal_id}] Team not found. Please contact support", e = None)

    } else {

      // PROSPECTS_EMAILS_TODO_READ_CLEANED
      prospectDAOService.findByEmailForZapier(prospectEmail = email.get, teamId = request.actingTeamAccount.get.team_id, Logger = Logger) match {
        case Failure(e) =>
          Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))

        case Success(None) =>
          Res.NotFoundError(s"Prospect not found: ${email.get}")

        case Success(Some(prospect)) =>
          Res.Success("Prospects found", Json.obj("prospect" -> prospect))

      }

    }
  }


  // use case : ProspectController.find GET     /api/:v/prospects/:prospectId
  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  def find(v: String, prospectId: Long, aid: Option[Long], tid: Option[Long]) = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_PROSPECTS,
    apiAccess = true, // Added on 27th July 2022: Tyler Kemp request
    localOrStagingApiTestAccess = true,
    tidOpt = tid
  )) { (request: PermissionRequest[AnyContent]) =>
    val Logger = request.Logger
    val Res = request.Response

    prospectDAOService.find(
      byProspectIds = Seq(prospectId),
      teamId = request.actingTeamAccount.get.team_id,
      permittedOwnerIds = Some(request.permittedAccountIds),
      isInternalRequest = !request.isApiCall,
      findActiveCampaignsIfInternalRequest = true,
      limit = 1,
      Logger = Logger
    ) match {

      case Failure(e) =>
        Res.ServerError(e)

      case Success(prospects) =>

        if (prospects.isEmpty) {

          Res.ForbiddenError("Prospect either doesn't exist, or you do not have access to this prospect")

        } else {
          val prospect = prospects.headOption.get

          /*
        val campaigns = CampaignProspect.findCampaignsForProspect(
          loggedinAccountId = request.loggedinAccount.id,
          prospectIds = Seq(prospect.id),
          onlyActive = true
        )
          */

          Res.Success("Prospect found", Json.obj(
            "prospect" -> ProspectObject.apiStructure(
              prospect = prospect,
              isInternalRequest = !request.isApiCall
            )
          ))
        }

    }
    //      if (request.isApiCall) {
    //
    //        Res.Success("Prospect found", Json.obj("prospect" -> request.prospect))
    //
    //      } else {
    //
    //        // val campaigns = CampaignProspect.findCampaignsForProspect(prospectId = prospectId)
    //
    //        Res.Success("Prospect found", Json.obj(
    //          "prospect" -> request.prospect
    //          // "campaigns" -> campaigns
    //        ))
    //
    //      }

  }

  /**
   * * Examples of passing and failing test cases because of sanitizeInputStrings refiner
   *
   * 1. Passing
   * curl 'http://localhost:3001/api/v2/prospects?tid=47' \
   * -H 'Accept: application/json' \
   * -H 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8' \
   * -H 'Connection: keep-alive' \
   * -H 'Content-Type: application/json' \
   * -H 'Cookie: xxxxxxx' \
   * -H 'Origin: http://localhost:3001' \
   * -H 'Referer: http://localhost:3001/dashboard/prospects?tid=47' \
   * -H 'Sec-Fetch-Dest: empty' \
   * -H 'Sec-Fetch-Mode: cors' \
   * -H 'Sec-Fetch-Site: same-origin' \
   * -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
   * -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"' \
   * -H 'sec-ch-ua-mobile: ?0' \
   * -H 'sec-ch-ua-platform: "macOS"' \
   * --data-raw '{"custom_fields":{"salutation":null,"website":null,"continent":null,"description":null,"region":null},"email":"<EMAIL>","owner_id":49,"first_name":"Abc","last_name":"Def","list":null,"company":"comp","city":"Pune","state":"MH","phone":"**********","job_title":null,"linkedin_url":null,"country":null,"timezone":null,"latest_task_done_at":null}'
   * {"status":"success","message":"New prospect has been saved!","data":{"prospect":{"object":"prospect","id":4560423,"owner_id":49,"team_id":47,"first_name":"Abc","last_name":"Def","email":"<EMAIL>","custom_fields":{},"list":null,"company":"comp","job_title":"","linkedin_url":"","phone":"**********","city":"Pune","state":"MH","country":"","timezone":null,"prospect_category":"Not categorized","last_contacted_at":null,"created_at":"2023-04-11T12:12:29.024+05:30","latest_reply_sentiment_uuid":null,"current_step_type":null,"latest_task_done_at":null,"internal":{"owner_name":"Prachi Mane","owner_email":"<EMAIL>","invalid_email":null,"list_id":null,"last_contacted_at":null,"last_replied_at":null,"last_opened_at":null,"total_opens":0,"total_clicks":0,"prospect_category_id_custom":358,"prospect_category_label_color":"#c7c7c7","prospect_source":null,"prospect_account_id":null,"prospect_account":null,"active_campaigns":null,"tags":[],"current_campaign_id":null,"flags":{"will_delete":false,"email_bounced":false,"force_send_invalid_email":false},"latest_reply_sentiment":null}}}}%
   *
   * 2. Failing
   *
   * curl 'http://localhost:3001/api/v2/prospects?tid=47' \
   * -H 'Accept: application/json' \
   * -H 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8' \
   * -H 'Connection: keep-alive' \
   * -H 'Content-Type: application/json' \
   * -H 'Cookie: xxxxxxxx' \
   * -H 'Origin: http://localhost:3001' \
   * -H 'Referer: http://localhost:3001/dashboard/prospects?tid=47' \
   * -H 'Sec-Fetch-Dest: empty' \
   * -H 'Sec-Fetch-Mode: cors' \
   * -H 'Sec-Fetch-Site: same-origin' \
   * -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
   * -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"' \
   * -H 'sec-ch-ua-mobile: ?0' \
   * -H 'sec-ch-ua-platform: "macOS"' \
   * --data-raw '{"custom_fields":{"salutation":null,"website":null,"continent":null,"description":null,"region":null},"email":"<EMAIL>","owner_id":49,"first_name":"Abc","last_name":"Def","list":null,"company":"<script></script>","city":"Pune","state":"MH","phone":"**********","job_title":null,"linkedin_url":null,"country":null,"timezone":null,"latest_task_done_at":null}'
   * {"status":"error","message":"Invalid input <script></script> for field company","data":{"error_type":"forbidden"}}%
   */

  // [PUBLIC API]: Mentioned in [API DOCS]
  // NOTE: [ZAPIER] used in zapier app
  // PROSPECTS_EMAILS_TODO_UPDATE / PROSPECTS_EMAILS_TODO_INSERT / PROSPECTS_EMAILS_TODO_READ_CLEANED
  // Use case : ProspectController.createOrUpdateOne - end-user uploads a single prospect via the form : POST    /api/:v/prospects
  def createOrUpdateOne(
                         v: String,
                         campaign_id: Option[Long],
                         prospect_account_id: Option[Long],
                         aid: Option[Long],
                         tid: Option[Long],
                         force_update: Option[Boolean],
                         // force_assign_to_new_campaign: Option[Boolean],
                         zapier: Option[Boolean], //FIXME remove and replace with source
                         source: Option[String],

                         // update_only: if true, then no upsert.
                         // Return error if prospect no present.
                         // Used in zapier update prospect action.
                         update_only: Option[Boolean]

                       ) = permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_PROSPECTS,
      tidOpt = tid,
      apiAccess = true,
      url_api_key_allowed = true
    )
    .andThen(sanitizeInputStringsWithPermissionReq())
    .async(parse.json) { request =>
      val Logger = request.Logger
      val Res = request.Response
        .enableLogRequestToDb(request = request)
      val ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads: Boolean = request.isApiCall
      val doer = request.loggedinAccount
      val teamAccount = request.actingTeamAccount
      val permittedAccountIds = request.permittedAccountIds
      val auditRequestLogId = request.auditRequestLogId
      val isApiCall = request.isApiCall

      if (request.isApiCall && zapier.isDefined && zapier.get) {
        Logger.info(msg = s"[Zapier API called] org_id :: ${request.loggedinAccount.org.id}")
      }

      Future {
        if (teamAccount.isEmpty) {
          Res.BadRequestError("Invalid Team")
        } else {
          val prospectSource: Option[ProspectSource.Value] = if (zapier.isDefined && zapier.get) {
            Some(ProspectSource.ZAPIER)
          } else if (source.isDefined) {
            Some(ProspectSource.withName(source.get))
          } else None
          val data: JsResult[ProspectCreateFormData] = if (prospectSource.isDefined && prospectSource.get == ProspectSource.RB2B) {
            ProspectCreateFormData.getFromRb2BData(data = request.body, team_id = TeamId(teamAccount.get.team_id))(Logger)
          } else {
            request.body.validate[ProspectCreateFormData]
          }

          data match {

            case JsError(errors) =>
              Res.JsValidationError(errors = errors, requestBody = Some(request.body))

            case JsSuccess(data, _) =>
              val teamAcc = teamAccount.get
              val org_id = doer.org.id

              //if( old flow and email defined) or new flow call service layer else throw error id email option for old flow

              val res = prospectServiceWithEmailValidation.createOrUpdateOne(Logger = Logger,
                v = v,
                force_update = force_update,
                campaign_id = campaign_id,
                prospect_account_id = prospect_account_id,
                //                  zapier = zapier,
                update_only = update_only,
                ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads,
                doer = doer,
                //teamAccount = teamAccount,
                team_id = teamAcc.team_id,
                user_id = teamAcc.user_id,
                permittedAccountIds = permittedAccountIds,
                auditRequestLogId = auditRequestLogId,
                isApiCall = isApiCall,
                data = ProspectCreateManuallyFormData(
                  prospect = data,
                  ignoreProspectsInOtherCampaigns = None //passing none if api is not used in FE
                ),
                source = prospectSource
              )


              res match {
                case Left(e: CreateOrUpdateOneError) =>
                  e match {
                    case CreateOrUpdateOneError.ErrorWhileCreateOrUpdateOne(error) =>
                      Res.BadRequestError(error)
                    case CreateOrUpdateOneError.ProspectNotFound(error) =>
                      Res.BadRequestError(error)
                    case CreateOrUpdateOneError.CampaignNotFound(error) =>
                      Res.BadRequestError(error)
                    case CreateOrUpdateOneError.EmailAndPhoneNumberAndLinkedInValidationError(error) =>
                      error match {
                        case ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidEmailAddress(e) => Res.BadRequestError(e)
                        case ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidPhoneNumber(e) => Res.BadRequestError(e)
                        case ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidLinkedinUrl(e) => Res.BadRequestError(e)
                        case ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidEmailAddressAndPhone(e) => Res.BadRequestError(e)
                        case ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidPhoneAndLinkedIn(e) => Res.BadRequestError(e)
                        case ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidEmailAddressAndLinkedIn(e) => Res.BadRequestError(e)
                        case ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidEmailAndPhoneNumberAndLinkedIn(e) => Res.BadRequestError(e)
                      }
                    case CreateOrUpdateOneError.ExceptionOccurred(e) =>
                      Res.BadRequestError(e.getMessage)
                    case CreateOrUpdateOneError.InternalServerError(e) =>
                      Res.ServerError(s"Error while creating prospect: ${e.getMessage}", e = Some(e))
                    case CreateOrUpdateOneError.InternalEmailCannotBeAdded(e) =>
                      Res.BadRequestError(e)

                    case CreateOrUpdateOneError.AtleastOneDeduplicateColumnMustBePresent(error) =>
                      Res.BadRequestError(error)

                    case CreateOrUpdateOneError.TeamCannotReceiveEarlyUpdates =>
                      Logger.shouldNeverHappen(s"createOrUpdateOne: Email cannot be optional for old flow/ secondary phones cannot be passed")
                      Res.BadRequestError("Error while creating prospect. Please contact support.")

                    case CreateOrUpdateOneError.PrimaryPhoneNotPresent(errMsg) =>
                      Res.BadRequestError(errMsg)

                    case CreateOrUpdateOneError.Phone3PresentButNotPhone2(errMsg) =>
                      Res.BadRequestError(errMsg)

                    case CreateOrUpdateOneError.CannotPassSecondaryPhones(errMsg) =>
                      Res.BadRequestError(errMsg)
                  }
                case Right(value) =>
                  Res.Success(value.responseMsg, value.responseObj)

              }


          }

        }
      }

    }


  //This api is only for FE
  def createOrUpdateManually(
                              v: String,
                              campaign_id: Option[Long],
                              prospect_account_id: Option[Long],
                              aid: Option[Long],
                              tid: Option[Long],
                              force_update: Option[Boolean],
                            ) = permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_PROSPECTS,
      tidOpt = tid,
      apiAccess = true
    )
    .andThen(sanitizeInputStringsWithPermissionReq())
    .async(parse.json) { request =>
      val Logger = request.Logger
      val Res = request.Response
        .enableLogRequestToDb(request = request)
      val ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads: Boolean = request.isApiCall
      val doer = request.loggedinAccount
      val teamAccount = request.actingTeamAccount
      val permittedAccountIds = request.permittedAccountIds
      val auditRequestLogId = request.auditRequestLogId
      val isApiCall = request.isApiCall

      Future {
        if (teamAccount.isEmpty) {
          Res.BadRequestError("Invalid Team")
        } else {

          val data = request.body.validate[ProspectCreateManuallyFormData]

          data match {

            case JsError(errors) =>
              Res.JsValidationError(errors = errors, requestBody = Some(request.body))

            case JsSuccess(data, _) =>
              val teamAcc = teamAccount.get
              val org_id = doer.org.id

              //if( old flow and email defined) or new flow call service layer else throw error id email option for old flow

              val res = prospectServiceWithEmailValidation.createOrUpdateOne(Logger = Logger,
                v = v,
                force_update = force_update,
                campaign_id = campaign_id,
                prospect_account_id = prospect_account_id,
                //                  zapier = None,
                update_only = None,
                ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads,
                doer = doer,
                //teamAccount = teamAccount,
                team_id = teamAcc.team_id,
                user_id = teamAcc.user_id,
                permittedAccountIds = permittedAccountIds,
                auditRequestLogId = auditRequestLogId,
                isApiCall = isApiCall,
                data = data,
                source = None
              )


              res match {
                case Left(e: CreateOrUpdateOneError) =>
                  e match {
                    case CreateOrUpdateOneError.ErrorWhileCreateOrUpdateOne(error) =>
                      Res.BadRequestError(error)
                    case CreateOrUpdateOneError.ProspectNotFound(error) =>
                      Res.BadRequestError(error)
                    case CreateOrUpdateOneError.CampaignNotFound(error) =>
                      Res.BadRequestError(error)
                    case CreateOrUpdateOneError.EmailAndPhoneNumberAndLinkedInValidationError(error) =>
                      error match {
                        case ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidEmailAddress(e) => Res.BadRequestError(e)
                        case ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidPhoneNumber(e) => Res.BadRequestError(e)
                        case ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidLinkedinUrl(e) => Res.BadRequestError(e)
                        case ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidEmailAddressAndPhone(e) => Res.BadRequestError(e)
                        case ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidPhoneAndLinkedIn(e) => Res.BadRequestError(e)
                        case ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidEmailAddressAndLinkedIn(e) => Res.BadRequestError(e)
                        case ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidEmailAndPhoneNumberAndLinkedIn(e) => Res.BadRequestError(e)
                      }

                    case CreateOrUpdateOneError.AtleastOneDeduplicateColumnMustBePresent(error) =>
                      Res.BadRequestError(error)

                    case CreateOrUpdateOneError.ExceptionOccurred(e) =>
                      Res.BadRequestError(e.getMessage)
                    case CreateOrUpdateOneError.InternalServerError(e) =>
                      Res.ServerError(s"Error while creating prospect: ${e.getMessage}", e = Some(e))
                    case CreateOrUpdateOneError.InternalEmailCannotBeAdded(e) =>
                      Res.BadRequestError(e)

                    case CreateOrUpdateOneError.TeamCannotReceiveEarlyUpdates => {
                      Logger.shouldNeverHappen(s"createOrUpdateOne: Email cannot be optional for old flow/ secondary phones cannot be passed")
                      Res.BadRequestError("Error while creating prospect. Please contact support.")
                    }

                    case CreateOrUpdateOneError.PrimaryPhoneNotPresent(errMsg) =>
                      Res.BadRequestError(errMsg)

                    case CreateOrUpdateOneError.Phone3PresentButNotPhone2(errMsg) =>
                      Res.BadRequestError(errMsg)


                    case CreateOrUpdateOneError.CannotPassSecondaryPhones(errMsg) =>
                      Res.BadRequestError(errMsg)

                  }
                case Right(value) =>
                  Res.Success(value.responseMsg, value.responseObj)

              }


          }

        }
      }

    }

  //PUBLIC_API_V3
  def createOrUpdateProspectsBatch(
                                    tid: Option[Long],
                                    source: Option[String],
                                    campaign_id: Option[SrIdentifier]
                                  ) = (permissionUtils.checkPermissionV2(
    id = None,
    version = ApiVersion.V3,
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = tid,
    apiAccess = true
  ) andThen hasCampaignEditPermissionWithOptionId(id = campaign_id, v = ApiVersion.V3))
    .async(parse.json) { request =>

      Future {

        given Logger: SRLogger = request.Logger.appendLogRequestId(appendLogReqId = " createOrUpdateProspectsBatch ")
        implicit val version: ApiVersion = ApiVersion.V3

        val Res = request.Response
          .enableLogRequestToDb(request = request.request)

        if (request.actingTeamAccount.isEmpty) {
          Res.BadRequestError("Invalid team")
        } else {

          val doer: Account = request.loggedinAccount
          val teamAccount: TeamMember = request.actingTeamAccount.get

          val campaignId: Option[CampaignId] = request.campaign.map(
            campaign => CampaignId(campaign.id)
          )

          val validateData: JsResult[Seq[ProspectCreateFormDataV2]] = request.body.validate[Seq[ProspectCreateFormDataV2]]

          validateData match {

            case JsError(e) =>
              Logger.fatal(s"jserror :: $e :: body: ${request.body}")

              Res.JsValidationError(errors = e, requestBody = Some(request.body))

            case JsSuccess(data, _) =>

              val org_id = doer.org.id

              //if( old flow and email defined) or new flow call service layer else throw error if email option for old flow

              prospectService.createOrUpdateProspectsBatchV3(
                data = data,
                source = source,
                teamAccount = teamAccount,
                campaignId = campaignId,
                doer = doer,
                auditRequestLogId = Some(request.request.auditRequestLogId)
              ) match {

                case Left(CreateOrUpdateProspectsBatchApiError.BadRequestError(errors)) =>
                  Res.BadRequestError(message = s"Error while validating  prospects. Contact Support team", version = ApiVersion.V3, errorResponse = errors)

                case Left(CreateOrUpdateProspectsBatchApiError.ErrorWhileCreatingProspect(e)) =>
                  Res.ServerError(s"Error while creating/updating prospects. Contact Support", e = Some(e))

                case Left(CreateOrUpdateProspectsBatchApiError.ErrorWhileFetchingProspectsData(e)) =>
                  Res.ServerError(s"Error while fetching prospects. Contact Support", e = Some(e))

                case Left(CreateOrUpdateProspectsBatchApiError.EmailCannotBeOptionalInTheOldFlow) =>
                  Logger.fatal(s"createOrUpdateProspectsBatchV3: Email cannot be optional for old flow")
                  Res.BadRequestError("Error while creating prospect. Please contact support.")

                case Right(result) =>

                  apiService.constructResponseForCreateOrUpdateProspects(
                    data = result,
                    team_id = teamAccount.team_id
                  ) match {
                    case Left(CreateOrUpdateProspectsForApiError.ErrorWhileMarkingProspectAsInvalid(e)) =>
                      Res.ServerError(s"Error while marking prospect as invalid. Contact Support", e = Some(e))

                    case Left(CreateOrUpdateProspectsForApiError.ErrorWhileConstructingResponse(e)) =>
                      Res.ServerError(s"Error while returning response. Contact Support", e = Some(e))

                    case Right(prospects) =>
                      Res.Success(data = Json.toJson(prospects), apiVersion = ApiVersion.V3.toString)
                  }

              }

          }
        }
      }

    }

  // NOTE: [PUBLIC API]: Not Mentioned in [API DOCS] but used in extension
  /*
   * Use case : Adding prospects to the system via API call shared with customers.
   */
  def createOrUpdateBatch(
                           v: ApiVersion,
                           campaign_id: Option[Long],
                           ignore_prospects: Option[String],
                           source: Option[String],
                           tid: Option[Long]
                         ) = permissionUtils.checkPermission(
      version = v.toString,
      permission = PermType.EDIT_PROSPECTS,
      tidOpt = tid,
      apiAccess = true
    )
    .async(parse.json) { request =>

      Future {

        given Logger: SRLogger= request.Logger.appendLogRequestId(appendLogReqId = " createOrUpdateBatch ")
        val Res = request.Response
          .enableLogRequestToDb(request = request)

        if (request.actingTeamAccount.isEmpty) {
          Res.BadRequestError("Invalid team")
        } else {

          val doer: Account = request.loggedinAccount
          val teamAccount: TeamMember = request.actingTeamAccount.get

          val validateData: JsResult[ProspectBatchCreateFormData] = request.body.validate[ProspectBatchCreateFormData]

          validateData match {

            case JsError(e) =>
              Logger.fatal(s"jserror :: $e :: body: ${request.body}")

              Res.JsValidationError(errors = e, requestBody = Some(request.body))

            case JsSuccess(data, _) =>

              prospectService.createOrUpdateProspectsBatch(
                ignore_prospects = ignore_prospects,
                data = data,
                teamAccount = teamAccount,
                source = source,
                campaign_id = campaign_id,
                doer = doer,
                auditRequestLogId = Some(request.auditRequestLogId)
              ) match {
                case Left(CreateOrUpdateProspectBatchError.InvalidProspectSource(errMsg)) =>
                  Res.BadRequestError(errMsg)

                case Left(CreateOrUpdateProspectBatchError.ProspectListEmpty(errMsg)) =>
                  Res.BadRequestError(errMsg)

                case Left(CreateOrUpdateProspectBatchError.InvalidEmails(errMsg)) =>
                  Res.BadRequestError(errMsg)

                case Left(CreateOrUpdateProspectBatchError.MoreThanOneOwnerInvolved(errMsg)) =>
                  Res.BadRequestError(errMsg)

                case Left(CreateOrUpdateProspectBatchError.FoundMultipleProspectsList(errMsg)) =>
                  Res.BadRequestError(errMsg)

                case Left(CreateOrUpdateProspectBatchError.BadRequestError(errMsg)) =>
                  Res.BadRequestError(errMsg)

                case Left(CreateOrUpdateProspectBatchError.FailedToAssignProspect(errMsg)) =>
                  Res.BadRequestError(errMsg)

                case Left(CreateOrUpdateProspectBatchError.ErrorWhileCreatingProspect(e)) =>
                  Res.ServerError(s"Error while creating prospect: ${e.getMessage}", e = Some(e))

                case Right(successMsg) => Res.Success(successMsg, Json.obj())

              }
          }
        }
      }

    }

  def updateProspectCategory(v: String, prospectId: Long, aid: Option[Long], tid: Option[Long]) = (

    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_PROSPECTS,
      tidOpt = tid

    ) andThen hasProspect(prospectId)

    ).async(parse.json) { request =>
    given Logger: SRLogger= request.Logger
    val Res = request.Response
      .enableLogRequestToDb(request = request.permissionRequest)

    Future {

      val validateData = request.body.validate[ProspectUpdateCategoryFormData]

      validateData match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>

          val ta = request.actingTeamAccount
          val p = request.prospect
          val a = request.loggedinAccount

          val newCategory = prospectDAOService.getProspectCategoryById(team_id = tid.get, category_id = data.prospect_category_id_custom, account = Some(a))

          if (newCategory.isEmpty) {

            Res.BadRequestError(s"Invalid prospect_category ${data.prospect_category_id_custom}")

          }
          else if (p.internal.prospect_category_id_custom == newCategory.get.id) {

            Res.BadRequestError(s"Prospect is already in ${data.prospect_category_id_custom} category")

          } else {

//            val emailNotCompulsoryEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//              teamId = TeamId(tid.get),
//              feature = SrRollingUpdateFeature.EmailNotCompulsory
//            )(Logger)

            prospectUpdateCategoryTemp.updateCategory(
              prospect = ProspectBasicForBatchUpdate(
                id = prospectId,
                prospect_category_id_custom = p.internal.prospect_category_id_custom,
                prospect_account_id = p.internal.prospect_account_id
              ),
              doerAccountId = ta.user_id,
              teamId = p.team_id,
              accountName = Helpers.getAccountName(ta),
              prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AdminUpdate(
                old_prospect_category_id = Some(ProspectCategoryId(id = request.prospect.internal.prospect_category_id_custom)),
                new_prospect_category_id = ProspectCategoryId(id = newCategory.get.id),
              ),
              account = a,
              logger = Logger,
//              emailNotCompulsoryEnabled = emailNotCompulsoryEnabled,
              auditRequestLogId = Some(request.auditRequestLogId)
            ) match {

              case Failure(e) => Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))

              case Success(0) =>
//                if (emailNotCompulsoryEnabled) {
                  Res.BadRequestError(s"Prospect category could not be updated because prospect is likely in Do Not Contact List. Could you check this ? If not, contact support.")
//                } else {
//                  val (_, emailDomain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(p.email.get)
//                  Res.BadRequestError(s"Prospect category could not be updated because '$emailDomain' domain is likely in Do Not Contact List. Could you check this ? If not, contact support.")
//                }

              case Success(_) => Res.Success(s"Prospect status has been updated!", Json.obj())

            }
          }
      }
    }

  }

  /**
   * * Examples of passing and failing test cases because of sanitizeInputStrings refiner
   *
   * 1. Passing
   * curl 'http://localhost:3001/api/v2/prospects/4560422?tid=47' \
   * -X 'PUT' \
   * -H 'Accept: application/json' \
   * -H 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8' \
   * -H 'Connection: keep-alive' \
   * -H 'Content-Type: application/json' \
   * -H 'Cookie: xxxxxx' \
   * -H 'Origin: http://localhost:3001' \
   * -H 'Referer: http://localhost:3001/dashboard/prospects/4560422/activity?tid=47' \
   * -H 'Sec-Fetch-Dest: empty' \
   * -H 'Sec-Fetch-Mode: cors' \
   * -H 'Sec-Fetch-Site: same-origin' \
   * -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
   * -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"' \
   * -H 'sec-ch-ua-mobile: ?0' \
   * -H 'sec-ch-ua-platform: "macOS"' \
   * --data-raw '{"object":"prospect","id":4560422,"owner_id":49,"team_id":47,"first_name":"Newp","last_name":"Def","email":"<EMAIL>","custom_fields":{},"list":null,"company":"comp","job_title":"","linkedin_url":"","phone":"**********","city":"Pune","state":"MH","country":"","timezone":null,"prospect_category":"Not categorized","last_contacted_at":null,"created_at":"2023-04-11T12:11:48.709+05:30","latest_reply_sentiment_uuid":null,"current_step_type":null,"latest_task_done_at":null,"internal":{"owner_name":"Prachi Mane","owner_email":"<EMAIL>","invalid_email":null,"list_id":null,"last_contacted_at":null,"last_replied_at":null,"last_opened_at":null,"total_opens":0,"total_clicks":0,"prospect_category_id_custom":358,"prospect_category_label_color":"#c7c7c7","prospect_source":null,"prospect_account_id":null,"prospect_account":null,"active_campaigns":[],"tags":[],"current_campaign_id":null,"flags":{"will_delete":false,"email_bounced":false,"force_send_invalid_email":false},"latest_reply_sentiment":null}}'
   * {"status":"success","message":"Prospect '<EMAIL>' has been updated!","data":{"prospect":{"object":"prospect","id":4560422,"owner_id":49,"team_id":47,"first_name":"Newp","last_name":"Def","email":"<EMAIL>","custom_fields":{},"list":null,"company":"comp","job_title":"","linkedin_url":"","phone":"**********","city":"Pune","state":"MH","country":"","timezone":null,"prospect_category":"Not categorized","last_contacted_at":null,"created_at":"2023-04-11T12:11:48.709+05:30","latest_reply_sentiment_uuid":null,"current_step_type":null,"latest_task_done_at":null,"internal":{"owner_name":"Prachi Mane","owner_email":"<EMAIL>","invalid_email":null,"list_id":null,"last_contacted_at":null,"last_replied_at":null,"last_opened_at":null,"total_opens":0,"total_clicks":0,"prospect_category_id_custom":358,"prospect_category_label_color":"#c7c7c7","prospect_source":null,"prospect_account_id":null,"prospect_account":null,"active_campaigns":null,"tags":[],"current_campaign_id":null,"flags":{"will_delete":false,"email_bounced":false,"force_send_invalid_email":false},"latest_reply_sentiment":null}}}}%
   *
   * 2. Failing
   * curl 'http://localhost:3001/api/v2/prospects/4560421?tid=47' \
   * -X 'PUT' \
   * -H 'Accept: application/json' \
   * -H 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8' \
   * -H 'Connection: keep-alive' \
   * -H 'Content-Type: application/json' \
   * -H 'Cookie: xxxxxxx \
   * -H 'Origin: http://localhost:3001' \
   * -H 'Referer: http://localhost:3001/dashboard/prospects/4560421/activity?tid=47' \
   * -H 'Sec-Fetch-Dest: empty' \
   * -H 'Sec-Fetch-Mode: cors' \
   * -H 'Sec-Fetch-Site: same-origin' \
   * -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
   * -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"' \
   * -H 'sec-ch-ua-mobile: ?0' \
   * -H 'sec-ch-ua-platform: "macOS"' \
   * --data-raw '{"object":"prospect","id":4560421,"owner_id":49,"team_id":47,"first_name":"","last_name":"","email":"<EMAIL>","custom_fields":{},"list":null,"company":"","job_title":"","linkedin_url":"","phone":"**********","city":"","state":"<script>alert(1)</script>","country":"","timezone":null,"prospect_category":"Not categorized","last_contacted_at":null,"created_at":"2023-04-10T16:14:40.558+05:30","latest_reply_sentiment_uuid":null,"current_step_type":null,"latest_task_done_at":null,"internal":{"owner_name":"Prachi Mane","owner_email":"<EMAIL>","invalid_email":null,"list_id":null,"last_contacted_at":null,"last_replied_at":null,"last_opened_at":null,"total_opens":0,"total_clicks":0,"prospect_category_id_custom":358,"prospect_category_label_color":"#c7c7c7","prospect_source":null,"prospect_account_id":null,"prospect_account":null,"active_campaigns":[],"tags":[],"current_campaign_id":null,"flags":{"will_delete":false,"email_bounced":false,"force_send_invalid_email":false},"latest_reply_sentiment":null}}'
   * {"status":"error","message":"Invalid input <script>alert(1)</script> for field state","data":{"error_type":"forbidden"}}%
   */


  // NOTE: This API is probably used in Colton's integration.
  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  // use case : update PUT     /api/:v/prospects/:id
  def update(v: String, prospectId: Long, aid: Option[Long], tid: Option[Long]) = (

    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_PROSPECTS,
      tidOpt = tid,
      apiAccess = true
    )
      andThen (sanitizeInputStringsWithPermissionReq())
      andThen hasProspect(prospectId)

    ).async(parse.json) { request =>
    given Logger: SRLogger= request.Logger
    val Res = request.Response

    Future {


      val t = request.actingTeamAccount
      val a = request.loggedinAccount
      //val ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = request.isApiCall

      Logger.info(s"ProspectController.update function called CHECKME $t : ${a.internal_id} : ${a.email} : ${request.body}")
      val validateData = request.body.validate[ProspectUpdateFormData]

      validateData match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>

          prospectService.updateSpecificProspect(
            data = data,
            prospect = request.prospect,
            prospectId = prospectId,
            team_id = t.team_id,
            user_id = t.user_id,
            doerAccount = a,
            accountName = Helpers.getAccountName(t),
            permittedAccountIds = request.permittedAccountIds,
            Logger = Logger,
            auditRequestLogId = request.auditRequestLogId
          ) match {
            case Left(UpdateSpecificProspectError.ServerErrorWhileUpdate(e)) =>
              e.getMessage match {

                case msg if msg.contains("sr_prospects_emails_tm_eml_unique") => Res.BadRequestError("You already have this prospect email in your team.")

                case msg => Res.ServerError("Problem while updating the prospect. Could you try again or contact support?", e = Some(e))
              }

            case Left(UpdateSpecificProspectError.InvalidEmailError(errMsg)) =>
              Res.BadRequestError(errMsg)

            case Left(UpdateSpecificProspectError.InvalidPhoneNumber(errMsg)) =>
              Res.BadRequestError(errMsg)

            case Left(UpdateSpecificProspectError.InternalEmailError(errMsg)) =>
              Res.BadRequestError(errMsg)

            case Left(UpdateSpecificProspectError.ProspectNotFound(errMsg)) =>
              Res.NotFoundError(errMsg)

            case Left(UpdateSpecificProspectError.ProspectNotUpdated(errMsg)) =>
              Res.NotFoundError(errMsg)

            case Right(prospectObject) =>
              val pr = ProspectObject.apiStructure(
                prospect = prospectObject,
                isInternalRequest = !request.isApiCall
              )

              Res.Success(s"Prospect '${request.prospect.email}' has been updated!", Json.obj(
                "prospect" -> pr
              ))
          }
      }
    }
  }

  /*
  =========
  REMOVED ON 17-Apr-2024
  This api is not used anywhere. Checked zapier app, checked post-login, checked logs for last 7 days.
  So removing it and the connected service / dao / route alongwith it.
  =========



  // Used in Zapier
  // use case : updateForEmail PUT     /api/:v/prospects/update_for_email
  def updateForEmail(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = tid
  ).async(parse.json) { request =>

    Future {
      given Logger: SRLogger= request.Logger
      val Res = request.Response


      val t = request.actingTeamAccount
      val a = request.loggedinAccount

      // val ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = request.isApiCall
      Logger.info(s"ProspectController.updateForEmail function called CHECKME :: ${a.email} : ${request.body}")


      if (t.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {
        val validateData = request.body.validate[ProspectUpdateFormData]

        validateData match {

          case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

          case JsSuccess(data, _) =>

            val isEmailValid = EmailValidationService.validateEmailFormat(data.email)

            val newProspectCategory = prospectDAOService.getProspectCategoryById(team_id = request.actingTeamAccount.get.team_id, category_id = data.prospect_category_id_custom.get, account = Some(a))

            if (!isEmailValid) {

              Res.BadRequestError("Invalid email address")

            } else if (newProspectCategory.isEmpty) {

              Res.BadRequestError(s"Invalid prospect_category ${data.prospect_category_id_custom}")

            } else {

              val (_, emailDomain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(data.email)
              // PROSPECTS_EMAILS_TODO_READ_CLEANED
              // use case : updateForEmail PUT     /api/:v/prospects/update_for_email
              prospectServiceV2.updateForEmail(
                permittedAccountIds = request.permittedAccountIds,
                actingAccountId = t.get.user_id,
                teamId = t.get.team_id,
                email = data.email,
                updateProspect = data,
                Logger = Logger
              ) match {

                case Failure(e) => Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))

                case Success(None) => Res.NotFoundError("Prospect not found. Could you check and try again ?")

                case Success(Some(prospect)) =>

                  if (newProspectCategory.isDefined && prospect.internal.prospect_category_id_custom != newProspectCategory.get.id) {

                    val newCatId = newProspectCategory.get.id

                    prospectUpdateCategoryTemp.updateCategory(
                      prospectId = prospect.id,
                      prospectEmail = prospect.email.get, //TODO: EMAIL_OPTIONAL check the path and test code (updateForEmail fn call findFromMaster which is having inner join prospects emails)
                      prospectEmailDomain = prospect.internal.email_domain.get,
                      teamId = prospect.team_id,
                      taId = prospect.internal.ta_id,
                      doerAccountId = t.get.user_id,
                      accountName = Helpers.getAccountName(t.get),
                      oldProspectCategoryIdCustom = ProspectCategoryId(id = prospect.internal.prospect_category_id_custom),
                      newProspectCategoryIdCustom = ProspectCategoryId(id = newCatId),
                      prospectStatusUpdatedManually = true,
                      account = a,
                      logger = Logger,
                      auditRequestLogId = Some(request.auditRequestLogId)
                    ) match {

                      case Failure(e) => Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))

                      case Success(0) => Res.BadRequestError(s"Prospect category could not be updated because '$emailDomain' domain is likely in Do Not Contact List. Could you check this ? If not, contact support.")

                      case Success(_) =>

                        //publishing trigger events
                        // CREATED_OR_UPDATED_PROSPECT_IN_SMARTREACH is also implicitly called in Trigger.findAllforMQProcess
                        mqTrigger.publishEvents(message = MQTriggerMsg(
                          accountId = t.get.user_id,
                          teamId = t.get.team_id,
                          prospectIds = Seq(prospect.id),
                          event = EventType.UPDATED_PROSPECT_IN_SMARTREACH.toString,
                          updatedProspectCategoryId = None,
                          triggerPath = Some(TriggerSource.OTHER)
                        ))

                        // PROSPECTS_EMAILS_TODO_READ_CLEANED
                        prospectDAOService.findFromMaster(
                          prospectId = prospect.id,
                          teamId = t.get.team_id,
                          Logger = Logger
                        ) match {
                          case Failure(e) =>
                            Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))

                          case Success(None) =>
                            Res.NotFoundError(s"Prospect '${prospect.email}' has not been updated. Could you check and try again ?")

                          case Success(Some(prospectObject)) =>

                            val pr = ProspectObject.apiStructure(
                              prospect = prospectObject,
                              isInternalRequest = !request.isApiCall
                            )

                            Res.Success(s"Prospect '${prospect.email}' has been updated!", Json.obj(
                              "prospect" -> pr
                            ))
                        }

                    }

                  } else {

                    val pr = ProspectObject.apiStructure(
                      prospect = prospect,

                      isInternalRequest = !request.isApiCall
                    )

                    Res.Success(s"Prospect '${prospect.email}' has been updated!", Json.obj("prospect" -> pr))


                  }


              }
            }
        }
      }
    }

  }

  */

  /* REMOVED at 7/4/2022
  * because it is not being used in frontend,
  * and as the service was extracted out from this method,
  * it is not getting used anywhere else either. So we comment out the service and the spec file too.
  *
  * the route was
  *
  * POST    /api/:v/prospects/upload
  *
  * which is also commented out
  *
  * verified in frontend by Sathish and commented out there too.
  *
  * */


  //  def uploadCSV(v: String, campaignId: Option[Long], aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
  //    version = v,
  //    permission = PermType.EDIT_PROSPECTS,
  //    aidOpt = aid,
  //    tidOpt = tid
  //  ).async { request =>
  //
  //    Future {
  //      val Logger = request.Logger
  //      val Res = request.Response
  //
  //
  //      val doer = request.loggedinAccount
  //      val t = request.actingTeamAccount
  //
  //      if (t.isEmpty) {
  //
  //        Res.BadRequestError("Invalid team")
  //
  //      } else {
  //        val bodyOpt = request.body.asMultipartFormData
  //
  //        bodyOpt match {
  //
  //          case None => Res.BadRequestError("Please send valid csv file")
  //
  //          case Some(body) => {
  //
  //            val file = body.file("csv")
  //
  //            prospectService.uploadCSV(
  //              mapping =  body.dataParts,
  //              v = v,
  //              permittedAccountIds = request.permittedAccountIds,
  //              t = t,
  //              file = file,
  //              doer = doer,
  //              campaignId = campaignId,
  //              Logger = Logger
  //            ) match {
  //              case Left(UploadCSVError.NoFileFound) =>
  //                Res.ServerError("Please send 'csv' file", e = None)
  //
  //              case Left(UploadCSVError.NoPermissionToEdit) =>
  //                  Res.BadRequestError("You do not have permission to edit prospects for the selected owner")
  //
  //              case Left(UploadCSVError.ErrorWhileFindingTeamAccount(e)) =>
  //                      Res.ServerError("There was an error. Please contact support.", e = Some(e))
  //
  //              case Left(UploadCSVError.NewOwnerTeamMemberNone) =>
  //                      Res.ServerError("There was an error. Please contact support. [2]", e = None)
  //
  //
  //              case Left(UploadCSVError.UploadCSVOrParseFailed(e)) =>
  //                          Res.ServerError(e, e = None)
  //
  //              case Right(data) =>
  //
  //                          Res.Success(s"${(data.total_created + data.total_duplicates_updated)} prospects have been created/updated", Json.toJson(data))
  //
  //            }
  //
  //          }
  //        }
  //      }
  //    }
  //
  //  }


  def assignProspectsToTeamMember(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = tid
  ).async(parse.json) { request =>

    /**
     *
     * 1. any team member (including admin) should be able to assign to another team member in same team
     * 2. On assigning to a different team member, existing campaigns if any, would paused
     *
     * NOTE (24th Feb, 2020): Removing the condition below: it was causing inconvenience, changed after Codeforce use-case
     * x 3. the assignor should either be admin or the current owner
     */


    Future {
      given Logger: SRLogger= request.Logger
      val Res = request.Response


      request.body.validate[AssignProspectsToMember] match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>

          val assignor = request.actingTeamAccount
          val assigneeId = data.assign_to

          val teamId = data.team_id


          // does assignor belong to team ?
          // if (!accountBelongsToTeam(assignor, teamId = teamId)) {
          if (assignor.get.team_id != teamId) {

            Res.ForbiddenError(s"You are not authorized to do this")

          } else {

            // does assignee belong to team ?

            accountService.find(id = assigneeId).toOption match {
              case None => Res.NotFoundError("Assignee account not found")

              case Some(assignee) =>

                if (!AuthUtils.accountBelongsToTeam(teams = assignee.teams, teamId = teamId)) {

                  Res.ForbiddenError("You are not authorized to do this")

                } else {

                  prospectServiceV2.assignToTeamMember(

                    assignToAccountId = assignee.internal_id,
                    assignToAccountName = Helpers.getAccountName(assignee),
                    assignorAccountId = assignor.get.user_id,
                    assignorAccountName = s"${assignor.get.first_name.getOrElse("")} ${assignor.get.last_name.getOrElse("")}",
                    permittedAccountIds = request.permittedAccountIds,
                    teamId = teamId,
                    prospectIds = data.prospect_ids
                    // force_assign = data.force_assign

                  ) match {

                    case Failure(e) => Res.BadRequestError(e.getMessage)

                    case Success(assigned) =>

                      val totalAssigned = assigned.length

                      //publishing trigger events
                      // CREATED_OR_UPDATED_PROSPECT_IN_SMARTREACH is also implicitly called in Trigger.findAllforMQProcess

                      if (assigned.isEmpty) {
                        Logger.warn(s"Assigned List is Empty")
                      } else {
                        mqTrigger.publishEvents(message = MQTriggerMsg(
                          accountId = assignor.get.user_id,
                          teamId = assignor.get.team_id,
                          prospectIds = assigned,
                          event = EventType.UPDATED_PROSPECT_IN_SMARTREACH.toString,

                          updatedProspectCategoryId = None,
                          triggerPath = Some(TriggerSource.OTHER),
                          oldProspectDeduplicationColumn = None // Sending it as None as we are Not updating any deduplication column as it is a UPDATED_PROSPECT_IN_SMARTREACH event 
                        ))
                      }


                      val successMsg = if (data.prospect_ids.size == 1) {
                        s"Prospect assigned successfully to '${Helpers.getAccountName(assignee)}'"
                      } else {
                        s"$totalAssigned prospects assigned successfully to '${Helpers.getAccountName(assignee)}'"
                      }

                      Res.Success(successMsg, Json.obj("total_assigned" -> totalAssigned))


                  }


                }
            }


          }



        /*
                  _checkIfValidMemberAndTeam(assignor, assigneeId, data.team_id) match {

                    case None => ForbiddenError(s"You are not authorized to do this")

                    case Some((acc, teamAcc)) =>

                      Prospect.assignToTeamMember(assignToAccountId = acc.id, teamId = data.team_id, data.prospect_ids) match {

                        case Failure(e) => BadRequestError(e.getMessage)

                        case Success(totalAssigned) => Res.Success(s"$totalAssigned prospects assigned successfully to '${acc.first_name} ${acc.last_name}'", Json.obj("total_assigned" -> totalAssigned))


                      }

                  }
        */

      }
    }

  }

  // PROSPECTS_EMAILS_TODO_UPDATE
  /*
  * Use case : when user force mark as valid
   */

  def markProspectAsValidToForceSendEmails(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = tid,
    apiAccess = true
  ).async(parse.json) { request =>

    Future {
      val Logger = request.Logger
      val Res = request.Response
      request.body.validate[ProspectMarkAsValidToForceSend] match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>

          val logData = SRInternalLog(
            function = "markProspectAsValidToForceSendEmails",
            data = Json.obj("prospect_ids" -> data.prospect_ids.map(id => s"pr_$id"))
          )
          SRInternalLog.logApi(request, status = SRLogStatus.START, logData = logData, logMsg = "Force send attempted")

          if (request.loggedinAccount.org.settings.disable_force_send) {

            SRInternalLog.logApi(request, status = SRLogStatus.FATAL, logData = logData, logMsg = "Force send disabled")

            Res.BadRequestError("You are not allowed to this. Please contact support")

          } else if (data.prospect_ids.isEmpty) {
            Res.BadRequestError("Please select prospect")

          } else if (data.prospect_ids.length > 1) {
            Res.BadRequestError("You can mark-as-valid 1 prospect at a time")
          } else {

            prospectServiceV2.prospectForceSentCount(
              accountId = request.actingTeamAccount.get.user_id,
              teamId = request.actingTeamAccount.get.team_id
            ) match {

              case Failure(e) =>
                Res.ServerError(s"There was an error. Please try again or contact support.", e = Some(e))

              case Success(forceSentToday) =>


                // increase limit to 200 per day for Pathrise (Quinn)
                val forceSendDailyLimit = if (request.loggedinAccount.org.id == 1510) 200
                else if (request.loggedinAccount.org.id == 2015) 25 // increase limit to 25 per day for DrakeDigital
                else if (request.loggedinAccount.org.id == 3442) 50 // increase limit to 50 per day for Glenlia

                /* 17th Nov 21
                 increase limit to 25 per day for Superhumansales
                 */
                else if (request.loggedinAccount.org.id == 2956) 25
                else 5

                if (forceSentToday >= forceSendDailyLimit) {

                  Res.BadRequestError(s"You can force-send emails to upto $forceSendDailyLimit prospects per 24 hours")

                } else {
                  prospectServiceV2.markProspectAsValidToForceSendEmails(
                    prospectIds = data.prospect_ids.distinct,
                    // accountId = request.actingTeamAccount.get.account_id,
                    teamId = request.actingTeamAccount.get.team_id,
                    Logger = Logger
                  ) match {

                    case Failure(e) => Res.ServerError(s"There was an error. Please try again or contact support.", e = Some(e))

                    case Success(updatedProspectIds) =>

                      /*
                      if (data.prospect_ids.length != updatedProspectIds.length) {

                        BadRequestError(s"You can not force-send emails to prospects that bounced before (hard-bounce). Please contact support.")

                      } else

                        */

                      if (data.prospect_ids.length == 1) {

                        if (updatedProspectIds.length == 1) {

                          Logger.info(s"marked_as_valid_to_force_send: pid_${data.prospect_ids.head}")
                          Res.Success(s"Prospect's email has been marked as valid.", Json.obj())

                        } else {

                          Res.NotFoundError(s"Prospect not found")

                        }

                      } else {
                        Res.Success(s"${updatedProspectIds.length} prospects' emails have been marked as valid.", Json.obj())
                      }


                  }
                }


            }

          }
      }
    }

  }

  /**
   *
   * The `editMagicColumnPrompt` is intended to be used when the user wants
   * to do a quick fix to the prompt input after saving the prompt but before generation.
   */
  def editMagicColumnPrompt(
                             v: String,
                             magicColumnId: Long,
                             tid: Option[Long]
                           ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = tid
  ).async(parse.json) { request =>

    Future {

      given Logger: SRLogger = request.Logger

      val Res = request.Response

      request.actingTeamAccount match {

        case None =>

          Res.BadRequestError("Invalid team")

        case Some(t) =>

          val teamId = TeamId(id = t.team_id)

          val validateData = request.body.validate[UpdateMagicPromptForm]

          validateData match {

            case JsError(e) =>

              Logger.error(msg = s"Failed to validate UpdateMagicPromptForm. errors: $e")

              Res.JsValidationError(errors = e, requestBody = Some(request.body))

            case JsSuccess(data, _) =>

              prospectColumnDef.updateMagicPrompt(
                teamId = teamId,
                magicColumnId = magicColumnId,
                updatedMagicPrompt = data.updated_magic_prompt,
                channel = None,
              ) match {

                case Failure(exception) =>

                  Logger.error(
                    msg = s"Failed to update magic column - updateMagicPrompt. teamId: $teamId :: columnId: $magicColumnId",
                    err = exception,
                  )

                  Res.ServerError(
                    message = s"Failed to update magic prompt - ${exception.getMessage}",
                    e = None,
                  )

                case Success(None) =>

                  Logger.error(
                    msg = s"Failed to update magic column - updateMagicPrompt - Not Found. teamId: $teamId :: columnId: $magicColumnId",
                  )

                  Res.BadRequestError(message = "Failed to update magic prompt not found.")

                case Success(_) =>

                  Res.Success(message = "Successfully updated magic prompt")

              }

          }

      }

    }

  }

  def addColumn(v: String, aid: Option[Long], tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = tid
  ).async(parse.json) {
    (request: PermissionRequest[JsValue]) =>


      Future {
        given Logger: SRLogger = request.Logger
        val Res = request.Response

        val t = request.actingTeamAccount

        if (t.isEmpty) {

          Res.BadRequestError("Invalid team")

        } else {
          val validateData = request.body.validate[CustomColumnDefCreateForm]

          validateData match {

            case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

            case JsSuccess(data, _) =>

              if (
                data.field_type == FieldTypeEnum.MAGIC &&
                  (data.magic_prompt.isEmpty || data.magic_prompt.get.trim.isEmpty)
              ) {

                Logger.warn(
                  msg = s"Not prompt found for magic column. colName: ${data.name} :: teamId: ${t.get.team_id}"
                )

                Res.BadRequestError(
                  message = "Magic column should contain the prompt"
                )

              } else {

                val saveTry = prospectColumnDef.create(t.get.user_id, t.get.team_id, t.get.ta_id, data, channel = None)

                saveTry match {

                  case Failure(e) =>

                    e.getMessage match {

                      case msg if msg.contains("duplicate key value violates unique constraint") => Res.BadRequestError("Column with name already exists. Provide a new name")

                      case msg => Res.ServerError("Error while creating column definition: " + msg, e = Some(e))

                    }

                  case Success(rowOpt) =>


                    rowOpt match {

                      case None => Res.ServerError("Error while creating column definition", e = None)

                      case Some(row) => Res.Success(s"Column '${row.name}' has been saved", Json.obj("column" -> row))

                    }
                }
              }
          }
        }
      }
  }

  def generateMagicColumn(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = tid,
  ).async(parse.json) { request =>

    Future {

      given Logger: SRLogger = request.Logger
      val Res = request.Response
      val loggedInAccount = request.loggedinAccount

      val t = request.actingTeamAccount

      if (t.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {

        val validateData = request.body.validate[GenerateCustomColumnData]

        validateData match {

          case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

          case JsSuccess(data, _) =>

            val creditsRequired = AppConfig.LeadFinderCharges.singleMagicColGeneration * data.prospect_ids.length

            organizationService.getRemainingLeadFinderCredits(
              orgId = OrgId(id = loggedInAccount.org.id)
            ) match {
              case Failure(exception) =>
                Logger.error(msg = "Failed to get remaining leadfinder credit count", err = exception)
                Res.ServerError(message = "Failed to generate magic column", e = None)

              case Success(creditsRemaining) =>

                if (creditsRemaining < creditsRequired) {

                  Logger.error(msg = s"Failed to generate magic columns as insufficient credits creditsRemaining:${creditsRemaining} creditsRequired:${creditsRequired}")
                  Res.BadRequestError(message = "you don't have enough credits to generate these magic columns")

                } else {

                  prospectColumnService.addProspectsForGeneratingMagicColumn(
                    teamId = TeamId(id = t.get.team_id),
                    accountId = AccountId(id = request.loggedinAccount.internal_id),
                    generateCustomColumnData = data,
                  ) match {

                    case Failure(exception) =>

                      Logger.error(msg = "Failed to generated magic column", err = exception)

                      Res.ServerError(message = "Failed to generate magic column", e = None)

                    case Success(updateCount) =>

                      Res.Success(message = s"Successfully queued $updateCount prospects for magic column generation.")

                  }
                }
            }


        }

      }

    }

  }

  /**
   * 4 Dec 2024
   *
   * This controller will be called when the user edits the LLM generated output,
   * if they want to add or correct the LLM generated output.
   */
  def editMagicColumnGenerationOutput(
                                       v: String,
                                       tid: Option[Long],
                                     ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = tid,
  ).async(parse.json) { request =>

    Future {

      given Logger: SRLogger = request.Logger

      val Res = request.Response

      request.actingTeamAccount match {

        case None =>

          Res.BadRequestError("Invalid team")

        case Some(t) =>

          val validateData = request.body.validate[EditMagicColumnGenerationData]

          validateData match {

            case JsError(e) =>

              Logger.error(msg = s"Failed to validate request body - EditMagicColumnGenerationData. errors: $e")

              Res.JsValidationError(errors = e, requestBody = Some(request.body))

            case JsSuccess(data, _) =>

              prospectColumnService.updateGeneratedOutputOfTheMagicColumn(
                teamId = TeamId(id = t.team_id),
                editMagicColumnGenerationData = data,
              ) match {

                case Failure(exception) =>

                  Logger.shouldNeverHappen(
                    msg = s"Failed to edit magic column output. teamId: ${t.team_id} :: prospectId: ${data.prospectId} :: columnId: ${data.columnId}",
                    err = Some(exception)
                  )

                  Res.ServerError(message = "Failed to edit magic column output", e = None)

                case Success(None) =>

                  Logger.error(
                    msg = s"Failed to edit magic column output - Not Found. teamId: ${t.team_id} :: prospectId: ${data.prospectId} :: columnId: ${data.columnId}",
                  )

                  Res.BadRequestError(message = s"Failed to edit magic column output - Not Found.")

                case Success(_) =>

                  Res.Success(message = s"Successfully saved edited magic column output.")

              }

          }

      }

    }

  }

  def createMutipleColumns(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = tid
  ).async(parse.json) {
    request =>


      Future {
        given Logger: SRLogger = request.Logger
        val Res = request.Response

        val t = request.actingTeamAccount
        if (t.isEmpty) {

          Res.BadRequestError("Invalid team")

        } else {
          val validateData = request.body.validate[CustomColumnDefCreateMultipleForm]

          validateData match {

            case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

            case JsSuccess(data, _) =>

              prospectColumnDef.createMultiple(t.get.user_id, t.get.team_id, taId = t.get.ta_id, data, channel = None) match {

                case Failure(e) =>

                  e.getMessage match {

                    case msg if msg.contains("duplicate key value violates unique constraint") => Res.BadRequestError("Column with same name already exists in accounts. Provide a new name")

                    case msg => Res.ServerError("Error while creating column definition: " + msg, e = Some(e))

                  }

                case Success(newColumnsWithSubmittedName) =>

                  val allColumns = prospectColumnDef.allColumns(teamId = t.get.team_id, channel = None)
                  val newColumns = newColumnsWithSubmittedName.map(_._1)
                  val newColsSubmittedNames = newColumnsWithSubmittedName.map { case (colDef, submittedName) => Json.obj(
                    "submitted_name" -> submittedName,
                    "saved_column" -> colDef
                  )
                  }

                  Res.Success(s"${newColumns.size} columns have been saved", Json.obj(
                    "all_columns" -> allColumns,
                    "new_columns" -> newColumns,
                    "new_columns_with_submitted_names" -> newColsSubmittedNames
                  ))


              }


          }
        }
      }
  }


  // NOTE: [ZAPIER] used in zapier app
  def findColumns(
                   v: String,
                   onlyCustom: Option[Boolean],
                   onlyTag: Option[Boolean],
                   onlyForAdding: Option[Boolean],
                   aid: Option[Long],
                   tid: Option[Long]
                 ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_PROSPECTS,
    tidOpt = tid,
    apiAccess = true
  ).async {
    request =>

      Future {
        val Logger = request.Logger
        val Res = request.Response

        val t = request.actingTeamAccount
        if (t.isEmpty) {

          Res.BadRequestError("Invalid team")

        } else {

          // val behavioralColumns = ProspectColumnDef.behavioralColumns

          val dbColumns = prospectColumnDef.allColumns(teamId = t.get.team_id, channel = None)

          val allColumns = if (v == "v1") dbColumns.filter(_.show_in_v1) else dbColumns

          val customColumns = allColumns.filter(_.is_custom)

          // val allColumnsForFilter = (defaultColumns ++ customColumns ++ behavioralColumns ).filter(_.filterable)

          // val allColumns = defaultColumns ++ customColumns

          val cols1 = (onlyCustom, onlyTag) match {

            case (Some(true), Some(true)) => customColumns.filter(_.tag_name.isDefined)

            case (_, Some(true)) => allColumns.filter(_.tag_name.isDefined) ++ prospectColumnDef.allowedInternalMergeTags(channel = None)

            case (Some(true), _) => customColumns

            case _ => allColumns

          }


          val cols = if (onlyForAdding.getOrElse(false)) {

            val ig = ProspectColumnDefUtils.ignoreFieldsOnAddOrUploadProspect

            cols1.filterNot(c => ig.contains(c.name)).filter(c => c.show_in_datagrid)

          } else cols1

          Res.Success("Columns found", Json.obj(
            "columns" -> cols,
            "all_columns_for_filter" -> allColumns.filter(_.filterable),
            // "behavioral_columns" -> behavioralColumns,
            "ignore_fields_while_add_or_upload" -> ProspectColumnDefUtils.ignoreFieldsOnAddOrUploadProspect,
            "ignore_fields_while_update" -> ProspectColumnDefUtils.ignoreFieldsOnUpdateProspect
          ))
        }
      }
  }


  def getTemplateTags(v: String, aid: Option[Long], tid: Option[Long], channel: Option[String]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_TEAM_CONFIG,
    tidOpt = tid
  ).async {
    request =>

      Future {
        val Logger = request.Logger
        val Res = request.Response
        val t = request.actingTeamAccount
        if (t.isEmpty) {

          Res.BadRequestError("Invalid team")

        } else {
          val channelType: Option[ChannelType] = channel.flatMap(ch => {

            ChannelType.fromKey(ch).toOption

          })

          if (channel.isDefined && channelType.isEmpty) {
            Res.BadRequestError("Invalid channel passed")

          } else {

            val allTagNames = prospectColumnDef.getAvailableTagNames(teamId = t.get.team_id, channel = channelType)

            Res.Success("Template tags found", Json.obj("template_tags" -> allTagNames))
          }
        }
      }

  }


  // NOTE: [ZAPIER] used in zapier app
  def getProspectLists(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_PROSPECTS,
    tidOpt = tid,
    apiAccess = true
  ).async {
    request =>

      Future {
        val Logger = request.Logger
        val Res = request.Response
        val t = request.actingTeamAccount
        val accountIds = request.permittedAccountIds


        if (t.isEmpty) {

          Res.BadRequestError("Invalid team")

        } else {
          prospectDAOService.findProspectLists(accountIds = accountIds, teamId = t.get.team_id) match {

            case Failure(e) =>
              Res.ServerError(s"There was an error.", e = Some(e))

            case Success(lists) =>

              if (request.isApiCall) {

                Res.Success("Prospect lists found", Json.obj(
                  "prospect_lists" -> lists.map(l => ProspectList.apiStructure(list = l))
                ))

              } else {

                Res.Success("Prospect lists found", Json.obj(
                  "prospect_lists" -> lists
                ))
              }


          }

        }
      }

  }


  def deleteV2(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.DELETE_PROSPECTS,
    tidOpt = tid
  ).async(parse.json) {
    request =>


      Future {
        val Logger = request.Logger
        val Res = request.Response

        val validateData = request.body.validate[ProspectDeleteFormData]

        validateData match {

          case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

          case JsSuccess(data, _) =>
            Logger.info(s"DELETE deleteV2 prospect called by ${request.loggedinAccount.internal_id} :: ${data.prospect_ids}")

            val ta = request.actingTeamAccount

            if (data.prospect_ids.size > 5) {


              // account should have permission to delete the prospects
              prospectDAOService.scheduleForDeletion(
                prospectIds = data.prospect_ids.map(pid => ProspectId(id = pid)),
                permittedAccountIds = request.permittedAccountIds,
                teamId = TeamId(id = ta.get.team_id),
                deletionReason = ProspectDeletionReason.ByUser,
                logger = Logger
              ) match {

                case Failure(e) =>
                  Res.ServerError(s"Error while deleting prospects. Please contact us.", e = Some(e))

                case Success(stats) =>


                  for (pid <- stats._2) {

                    mqProspectDeleter.publish(message = MQProspectDeleterMsg(prospectId = pid, teamId = ta.get.team_id))

                  }

                  Logger.info(s"[ProspectDeleteAPI] ta ($ta) :: requested (${data.prospect_ids.length}) :: deleting (${stats._1})")

                  Res.Success(s"${stats._1} prospects have been scheduled for deletion. They should get deleted within a day", Json.obj("total_deleted" -> 0, "total_scheduled" -> stats._1))

              }


            } else {

              // account should have permission to delete the prospects
              prospectDAOService.delete(
                prospectIds = data.prospect_ids.map(pid => ProspectId(id = pid)),
                permittedAccountIds = request.permittedAccountIds,
                teamId = TeamId(id = ta.get.team_id),
                logger = Logger
              ) match {
                case Failure(e) =>
                  Res.ServerError(s"Error while deleting prospects. Please contact us.", e = Some(e))

                case Success(total_deleted) =>

                  Logger.info(s"[ProspectDeleteAPI] account ($ta) :: requested (${data.prospect_ids.length}) :: deleting (${total_deleted}) :: immediately")


                  Res.Success(s"${total_deleted} prospects have been deleted", Json.obj("total_deleted" -> total_deleted, "total_scheduled" -> 0))

              }

            }


        }
      }

  }


  // NOTE: [ZAPIER] used in zapier app Action to mark prospects as completed
  // use case : ProspectController.markAsCompletedZapier POST    /api/:v/prospects/change_pause_status
  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  def markAsCompletedZapier(v: String) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PROSPECTS,
    apiAccess = true,
    webAccess = false
  ).async(parse.json) { request =>

    Future {
      given Logger: SRLogger = request.Logger
      val Res = request.Response

      val ta = request.actingTeamAccount.get
      val teamId = ta.team_id
      val accountId = ta.user_id
      val emails: Option[Seq[String]] = (request.body \ "emails").asOpt[Seq[String]]

      Logger.info(s"[Zapier API called] org_id :: ${request.loggedinAccount.org.id} : ProspectController markAsCompletedZapier: ($accountId, $teamId) :: $emails")

      if (emails.isEmpty || emails.get.isEmpty) {

        Res.BadRequestError("Please send valid email")

      } else {

        // account should own the prospects to be deleted
        campaignProspectDAOService.pauseStatusChangedByAdmin(
          permittedAccountIds = request.permittedAccountIds,
          statusChangedByAccountId = AccountId(id = accountId),
          teamId = teamId,
          prospectEmails = emails.get,
          prospectIds = Seq(),
          newPauseStatus = true,
          campaignIds = Seq(),
          Logger = Logger
        ) match {


          case Failure(e) =>
            Res.ServerError("Error while change pause status", e = Some(e))

          case Success(cpCompleteds) =>

            mqWebhookCompleted.publishCompletedProspects(
              accountId = accountId,
              teamId = teamId,
              cpCompleteds = cpCompleteds
            ) match {

              case Failure(e1) =>
                Res.ServerError("Error while change pause status", e = Some(e1))

              case Success(_) =>

                // val total = cpCompleteds.length
                val msg = s"Campaign has been completed for selected prospect(s)"

                Res.Success(msg, Json.obj("saved" -> true))


            }


        }


      }
    }

  }

  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  // use case : ProspectController.pauseUnpauseCampaign POST    /api/:v/inbox/change_pause_status
  def pauseUnpauseCampaign(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = tid,
    apiAccess = true
  ).async(parse.json) {
    request =>


      Future {
        given Logger: SRLogger = request.Logger
        val Res = request.Response

        val validateData = request.body.validate[ProspectPauseStatusData]

        validateData match {

          case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

          case JsSuccess(data, _) =>


            // account should own the prospects to be deleted
            campaignProspectDAOService.pauseStatusChangedByAdmin(
              permittedAccountIds = request.permittedAccountIds,
              statusChangedByAccountId = AccountId(id = request.loggedinAccount.internal_id),
              teamId = request.actingTeamAccount.get.team_id,
              prospectEmails = Seq(),
              prospectIds = data.prospect_ids,
              newPauseStatus = data.new_pause_status,
              campaignIds = data.campaign_ids.getOrElse(Seq()),
              Logger = Logger
            ) match {


              case Failure(e) =>
                Res.ServerError("Error while change pause status", e = Some(e))

              case Success(cpCompleteds) =>

                val publishWebhook = mqWebhookCompleted.publishCompletedProspects(
                  accountId = request.actingTeamAccount.get.user_id,
                  teamId = request.actingTeamAccount.get.team_id,
                  cpCompleteds = cpCompleteds
                )

                publishWebhook match {

                  case Failure(e1) =>
                    Res.ServerError("Error while change pause status", e = Some(e1))

                  case Success(_) =>

                    val total = cpCompleteds.length
                    // val msg = s"Campaign has been ${if (data.new_pause_status) "paused" else "unpaused"} for ${if (total == 1) "prospect" else s"$total prospects"}"
                    val msg = s"Campaign has been ${if (data.new_pause_status) "completed" else "resumed"} for selected prospects"

                    //                Res.Success(s"$total ${if (total == 1) "prospect has" else "prospects have"} been ${if (data.new_pause_status) "paused" else "unpaused"}", Json.obj("total_status_changed" -> total))
                    Res.Success(msg, Json.obj("total_status_changed" -> total))


                }


            }


        }
      }

  }

  def getEventsV3(v: ApiVersion, id: SrIdentifier, aid: Option[Long], tidOpt: Option[Long]): Action[AnyContent] = permissionUtils.checkPermissionV2(
    id = Some(id),
    version = v,
    permission = PermType.VIEW_PROSPECTS,
    tidOpt = tidOpt,

    apiAccess = true
  ) andThen hasProspectV2(
    id = id,
    v = v
  ) async { request =>
    Future {

      implicit val apiVersion: ApiVersion = v
      given Logger: SRLogger = request.Logger

      val Res = request.Response
      val team = request.actingTeamAccount
      val teamId = TeamId(team.team_id)
      val orgId = OrgId(request.loggedinAccount.org.id)
      val prospect = request.prospect
      val prospectId = ProspectId(prospect.id)
      val account = request.loggedinAccount
      val params: Map[String, Seq[String]] = request.queryString
      val accountId = AccountId(account.internal_id)

      val repTrackingHosts = repTrackingHostService.getRepTrackingHosts().get
      val allTrackingDomains: Seq[String] = repTrackingHosts.map(_.host_url)

      val prospectEventsRes: Either[ProspectEventsListingPaginationError, ProspectEventsListingResponse] = prospectEventsListingPaginationService.getProspectEvents(
        prospectId = prospectId,
        repTrackingHosts = repTrackingHosts,
        prospectObject = prospect,
        teamId = teamId,
        account = account,
        params = params,
        uri = request.uri
      )

      prospectEventsRes match {

        case Left(ProspectEventsListingPaginationError.PaginationError(msg)) =>
          Logger.fatal(msg = msg)
          Res.ServerError(message = "Server error, please contact support.", e = None)

        case Left(ProspectEventsListingPaginationError.DBFailure(err)) =>
          Logger.fatal(msg = s"[ProspectController.getEventsV3 prospectEventsListingPaginationService.getProspectEvents] prospectId::$prospectId teamId::$teamId", err)
          Res.ServerError(message = "Server error, please contact support.", e = Some(err))

        case Right(data: ProspectEventsListingResponse) =>

          apiService.getProspectEventsListingResponse(
            events = data.events,
            navigationLinksPrevNextResponse = data.nav_links,
            has_more = data.has_more,
            prospect = prospect,
            prospectId = prospectId,
            teamId = teamId,
            orgId = orgId,
            isApiCall = request.isApiCall,
            allTrackingDomains = allTrackingDomains,
            permittedAccountIds = request.permittedAccountIds,
            accountId = accountId
          ) match {

            case Left(ProspectEventError.DbFailure(err)) =>
              Logger.fatal(msg = s"[ProspectController.getEventsV3 apiService.getProspectEventsListingResponse] prospectId::$prospectId teamId::$teamId", err)
              Res.ServerError(message = "Server error, please contact support.", e = Some(err))

            case Right(finalResponse) =>
              Res.Success(
                message = "Timeline found",
                data = Json.toJson(finalResponse),
                apiVersion = apiVersion.toString
              )

          }

      }

      //    Res.SuccessV3(data = Json.obj("prospect_id" -> prospectId))

    }
  }

  // used for "Prospect Timeline" modal
  //  def getEvents(v: String, prospect_id: Long, aid: Option[Long], tid: Option[Long]) =
  //    (
  //      permissionUtils.checkPermission(
  //        version = v,
  //        permission = PermType.VIEW_PROSPECTS,
  //        tidOpt = tid
  //
  //      ) andThen hasProspect(prospectId = prospect_id)
  //
  //      ).async { request => Future {
  //
  //      given Logger: SRLogger = request.Logger
  //      val Res = request.Response
  //      val params: Map[String, Seq[String]] = request.queryString
  //
  //      val prospect = request.prospect
  //      val teamId = TeamId(request.actingTeamAccount.team_id)
  //      val account = request.loggedinAccount
  //      val prospectId = ProspectId(prospect.id)
  //
  //      val repTrackingHosts = repTrackingHostService.getRepTrackingHosts().get
  //
  //      val prospectEventsRes: Either[ProspectEventsListingPaginationError, (Seq[ProspectEventV2], NavigationLinksPrevNextResponse, Boolean)] = prospectEventsListingPaginationService.getProspectEvents(
  //        prospectId = prospectId,
  //        repTrackingHosts = repTrackingHosts,
  //        prospectObject = prospect,
  //        teamId = teamId,
  //        account = account,
  //        params = params,
  //        uri = request.uri
  //      )
  //
  //      prospectEventsRes match {
  //
  //        case Left(ProspectEventsListingPaginationError.PaginationError(msg)) =>
  //          Logger.fatal(msg = msg)
  //          Res.ServerError(message = "Server error, please contact support.", e = None)
  //
  //        case Left(ProspectEventsListingPaginationError.DBFailure(err)) =>
  //          Logger.fatal(msg = s"[ProspectController.getEvents prospectEventsListingPaginationService.getProspectEvents] prospectId::$prospectId teamId::$teamId")
  //          Res.ServerError(message = "Server error, please contact support.", e = Some(err))
  //
  //        case Right(data) =>
  //
  //          Res.Success(
  //            "Timeline found", Json.obj(
  //              "events" -> data._1,
  //              "prospect" -> ProspectObject.apiStructure(
  //                prospect = request.prospect,
  //                isInternalRequest = !request.isApiCall
  //              ),
  //              "next_page" -> data._2.next,
  //              "has_more" -> data._3
  //            )
  //          )
  //
  //      }
  //
  //    }}

  /*
   * Use case: Upload CSV file - step 1 - get a pre-signed url from Google to which we can upload a csv file
   */

  def genPresignedUrlForUploadingCSV(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = tid,
    allowInAgencyDashboard = true
  ) { (request: PermissionRequest[AnyContent]) =>

    val Res = request.Response
    val filename = if (request.body.asJson.isDefined) (request.body.asJson.get \ "filename").asOpt[String] else None

    if (filename.isEmpty) {

      Res.BadRequestError(s"Send valid filename")

    } else {

      CloudStorage.genSignedUrlForCSV(filename = filename.get) match {

        case Failure(e) =>
          Res.ServerError(s"Error while generating presigned url: $e", e = Some(e))

        case Success(url) =>
          Res.Success("Presigned url", Json.obj("presigned_url" -> url))

      }


    }


  }


  // FIXME: remove this api after full migration to the saveUploadedCsv api in frontend
  def createCsvFileEntry(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = tid

  ).async(parse.json) { request =>


    Future {
      val Logger = request.Logger
      val Res = request.Response

      val t = request.actingTeamAccount
      val loggedInAccountId = request.loggedinAccount.internal_id

      if (t.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {

        val validateData = request.body.validate[CsvQueueCreateFormDate]

        validateData match {

          case JsError(e) => Res.JsValidationError(errors = e)

          case JsSuccess(data, _) =>

            if (v == "v2" && data.owner_id.isEmpty) {
              Logger.fatal(s"owner_id is empty :: data: $data")
            }

            val prospectOwnerAccountId = data.owner_id.getOrElse(t.get.user_id)

            /*
            val foundOwnerTeamMember = Helpers.checkTeamMember(
              doer = request.loggedinAccount,
              teamId = t.get.team_id,
              checkAccountId = prospectOwnerAccountId
            )
            */


            if (!request.permittedAccountIds.contains(prospectOwnerAccountId)) {

              Res.BadRequestError("You do not have permission to edit prospects for the selected owner")

            } else {


              accountService.findNewOwnerTeamMember(
                accountId = AccountId(id = prospectOwnerAccountId), // FIXME VALUECLASS
                teamId = TeamId(id = t.get.team_id) // FIXME VALUECLASS
              ) match {

                case Failure(e) =>
                  Res.ServerError("There was an error. Please contact support.", e = Some(e))

                case Success(None) =>
                  Res.ServerError("There was an error. Please contact support. [2]", e = None)

                case Success(Some(foundOwnerTeamMember)) =>
                  CsvQueue.create(
                    accountId = foundOwnerTeamMember.user_id,
                    teamId = foundOwnerTeamMember.team_id,
                    ta_id = foundOwnerTeamMember.ta_id,
                    loggedin_id = loggedInAccountId,
                    data = data
                  ) match {

                    case Failure(e) =>
                      Res.ServerError(s"There was an error. Please try again or contact support", e = Some(e))

                    case Success(obj) =>
                      Res.Success("CSV will be uploaded in a few minutes", Json.obj("csv_file" -> obj))

                  }
              }
            }
        }
      }
    }

  }

  // createCsvFileEntry: version 2
  /*
   * Use case: prospect upload via upload csv - save csv in google after getting presigned url - Step 2.
   */
  def saveUploadedCsv(tid: Option[Long]) = permissionUtils.checkPermission(
    version = "v2",
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = tid

  ).async(parse.json) { request =>


    Future {
      val Logger = request.Logger
      val Res = request.Response

      val t = request.actingTeamAccount
      val loggedInAccountId = request.loggedinAccount.internal_id
      val orgId = OrgId(request.loggedinAccount.org.id)

      if (t.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {

        val validateData = request.body.validate[CsvQueueCreateFormDataV2]

        validateData match {

          case JsError(e) => Res.JsValidationError(errors = e)

          case JsSuccess(data, _) =>

            if (data.owner_id.isEmpty) {
              Logger.fatal(s"owner_id is empty :: data: $data")
            }

            Logger.info(s"saveUploadedCsv: initiated with request body: data: $data")

            val prospectOwnerAccountId = data.owner_id.getOrElse(t.get.user_id)

            if (!request.permittedAccountIds.contains(prospectOwnerAccountId)) {

              Res.BadRequestError("You do not have permission to edit prospects for the selected owner")

            } else {


              accountService.findNewOwnerTeamMember(
                accountId = AccountId(id = prospectOwnerAccountId), // FIXME VALUECLASS
                teamId = TeamId(id = t.get.team_id) // FIXME VALUECLASS
              ) match {

                case Failure(e) =>
                  Res.ServerError("There was an error. Please contact support.", e = Some(e))

                case Success(None) =>
                  Res.ServerError("There was an error. Please contact support. [2]", e = None)

                case Success(Some(foundOwnerTeamMember)) =>

                  prospectService.checkIfUploadCsvDataIsValid(
                    data.column_map,
                    teamId = TeamId(foundOwnerTeamMember.team_id)
                  )(Logger) match {

                    case Right(true) =>
                      CsvQueue.createV2(
                        accountId = foundOwnerTeamMember.user_id,
                        teamId = Some(foundOwnerTeamMember.team_id),
                        ta_id = Some(foundOwnerTeamMember.ta_id),
                        loggedin_id = loggedInAccountId,
                        data = data
                      ) match {

                        case Failure(e) =>
                          Res.ServerError(s"There was an error. Please try again or contact support", e = Some(e))

                        case Success(obj) =>
                          Res.Success("CSV will be uploaded in a few minutes", Json.obj("csv_file" -> obj))

                      }

                    case Left(errMsg: String) => Res.ForbiddenError(errMsg)

                  }

              }
            }
        }
      }
    }

  }

  def getCsvUploadLogs(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = "v2",
    permission = PermType.VIEW_PROSPECTS,
    tidOpt = tid,
    localOrStagingApiTestAccess = true
  ).async { request =>
    Future {
      val logger = request.Logger
      val res = request.Response
      val accountId = request.loggedinAccount.internal_id
      if (tid.isEmpty) {
        res.BadRequestError("Invalid team")
      } else {
        val teamId = TeamId(tid.get)

        CsvQueue.getCsvLogs(teamId = teamId) match {
          case Failure(e) =>
            logger.error(s"Error while fetching csv upload logs for teamId :: ${teamId}", err = e)
            res.ServerError("Could not fetch csv upload logs", Some(e))
          case Success(csvUploadLogs) =>
            res.Success("Successfully fetched csv upload logs", Json.obj("csv_upload_logs" -> csvUploadLogs))
        }

      }
    }

  }

  def getLastCsvMapping(tid: Option[Long]) = permissionUtils.checkPermission(
    version = "v2",
    permission = PermType.VIEW_PROSPECTS,
    tidOpt = tid,
    localOrStagingApiTestAccess = true
  ).async { request =>
    Future {
      val logger = request.Logger
      val res = request.Response
      val accountId = request.loggedinAccount.internal_id
      if (tid.isEmpty) {
        res.BadRequestError("Invalid team")
      } else {
        val teamId = TeamId(tid.get)

        CsvQueue.getLastCsvMapping(
          teamId = teamId,
          accountId = AccountId(accountId)
        ) match {
          case Failure(e) =>
            logger.error(s"Error while fetching csv column map for accountId :: ${accountId}", err = e)
            res.ServerError("Error while fetching last column map. Please contact support.", Some(e))

          case Success(csvColumnMapOpt) =>
            csvColumnMapOpt match {
              case None => res.Success(
                "Success. no column map found"
              )
              case Some(csvColumnMap) => res.Success(
                "Successfully fetched last csv column map",
                Json.obj("last_column_map" -> csvColumnMap)
              )
            }
        }

      }
    }

  }


  def getStatsProspectListOverall(
                                   v: String,
                                   aid: Option[Long],
                                   tid: Option[Long]
                                 ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_REPORTS,
    tidOpt = tid

  ) { (request: PermissionRequest[AnyContent]) =>
    val Logger = request.Logger
    val Res = request.Response

    if (request.actingTeamAccount.isEmpty) {
      Res.BadRequestError("Select a team")
    } else {

      val allStats = prospectDAOService.getOverallProspectListStats(
        permittedAccountIds = request.permittedAccountIds,
        teamId = request.actingTeamAccount.get.team_id
      )

      Res.Success(
        "Stats found", Json.obj(
          "overall_stats" -> allStats
        )
      )
    }
  }

  // use case : ProspectController.getFeed GET     /api/:v/feed
  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  def getFeed(v: String, prospect_id: Option[Long], last_event_at: Option[Long], aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_PROSPECTS,
    tidOpt = tid

  ).async { request =>
    val Logger = request.Logger
    val Res = request.Response

    val accountId = request.actingTeamAccount.get.user_id
    val teamId = request.actingTeamAccount.get.team_id

    val prospectFeedFuture: Future[List[ProspectFeed]] = prospectFeedDAO.getFeed(
      prospectId = prospect_id,
      last_event_at = last_event_at,
      permittedAccountIds = request.permittedAccountIds,
      tid = teamId,
      Logger = Logger
    )

    prospectFeedFuture.map { case (prospectFeed) =>

      if (request.isSupportAppModeRequest) {

        Res.Success(
          "Feed found", Json.obj(
            "feed" -> prospectFeed
          )
        )

      } else {
        prospectFeedDAO.updateFeedLastSeen(aid = accountId, tid = teamId) match {

          case Failure(e) =>
            Res.ServerError("Error while fetching feed", e = Some(e))

          case Success(value) =>
            Res.Success(
              "Feed found", Json.obj(
                "feed" -> prospectFeed
              )
            )
        }
      }

    } recover { case e => Res.ServerError("Error while fetching feed", e = Some(e)) }

  }


  def getFeedEventsCount(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_PROSPECTS,
    tidOpt = tid

  ) { (request: PermissionRequest[AnyContent]) =>
    val Logger = request.Logger
    val Res = request.Response

    prospectFeedDAO.getFeedEventsCount(
      permittedAccountIds = request.permittedAccountIds,
      loggedinAccountId = request.actingTeamAccount.get.user_id,
      tid = request.actingTeamAccount.get.team_id
    ) match {

      case Failure(e) =>
        Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))

      case Success(None) =>
        Res.NotFoundError("Feed not found")

      case Success(Some(feed)) =>
        Res.Success("Feed events found", Json.obj("has_feed" -> feed))

    }
  }


  def createNewCustomProspectCategory(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_TEAM_CONFIG,
      tidOpt = tid
    )
    .async(parse.json) { request =>


      Future {
        given Logger: SRLogger= request.Logger
        val Res = request.Response
        
        val cannotAddCustomCategories = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
          teamId = TeamId(tid.get),
          feature = SrRollingUpdateFeature.NewReplySentiments
        )(Logger)

        if (request.actingTeamAccount.isEmpty) {

          Res.BadRequestError("Invalid team")

        } else if(cannotAddCustomCategories) {
          Res.BadRequestError("You cannot add custom categories in this team")
        } else {

          val teamAccount = request.actingTeamAccount.get


          val validateData = request.body.validate[ProspectCustomCategoryCreateOrUpdateFormData]

          validateData match {

            case JsError(e) =>
              Logger.error(s"createNewProspectCategory jserrror: ${teamAccount.team_id} :: ${teamAccount.user_id} :: $e")

              Res.JsValidationError(errors = e, requestBody = Some(request.body))

            case JsSuccess(data, _) =>

              val text_id = data.name.trim.replaceAll(" ", "_").toLowerCase()

              if (text_id.isEmpty) {

                Res.BadRequestError("Invalid category name")

              }
              else if (!data.label_color.matches("^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$")) {

                Res.BadRequestError("Invalid label color")

              }
              else {

                val saveTry = prospectDAOService.createOneCustomProspectCategory(
                  accountId = teamAccount.user_id,
                  teamId = teamAccount.team_id,
                  accountName = Helpers.getAccountName(a = teamAccount),
                  categoryName = data.name,
                  text_id = text_id,
                  label_color = data.label_color
                )

                saveTry match {


                  case Failure(e) =>

                    e.getMessage match {

                      case msg if msg.contains("violates unique constraint") => Res.BadRequestError("Another category with this name already exists")

                      case msg => Res.ServerError("Error while creating category: " + msg, e = Some(e))

                    }

                  case Success(rowOpt) =>

                    if (rowOpt.isEmpty) {

                      Res.BadRequestError("Another category with this name already exists")
                    }
                    else {

                      resetUserCacheUtil._resetTeamCache(aid = teamAccount.user_id)

                      val responseJsonObj = Json.obj("account" -> accountService.find(id = teamAccount.user_id).toOption)
                      Res.Success("New prospect has been saved!", responseJsonObj)

                    }

                }
              }
          }
        }
      }

    }

  def reorderProspectCategory(
                               v: String,
                               id: Long,
                               tid: Option[Long],
                             ): Action[JsValue] = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_TEAM_CONFIG,
    tidOpt = tid
  ).async(parse.json) { request =>

    Future {

      given logger: SRLogger = request.Logger

      val Res = request.Response

      request.actingTeamAccount match {

        case None =>

          logger.error(
            msg = s"reorderProspectCategory - actingTeamAccount None - tid: $tid :: id: $id"
          )

          Res.BadRequestError(message = "Invalid team")

        case Some(teamAccount) =>

          request.body.validate[ReorderProspectCategoryData] match {

            case JsError(e) =>

              logger.error(
                msg = s"reorderProspectCategory JsError - teamId: ${teamAccount.team_id} :: userId: ${teamAccount.user_id} :: errors: $e"
              )

              Res.JsValidationError(
                errors = e,
                requestBody = Some(request.body)
              )

            case JsSuccess(reorderProspectCategoryData, _) =>

              val teamId = TeamId(id = teamAccount.team_id)

              val prospectCategoryId = ProspectCategoryId(id = id)

              prospectDAOService.reorderProspectCategory(
                teamId = teamId,
                prospectCategoryId = prospectCategoryId,
                reorderProspectCategoryData = reorderProspectCategoryData
              ) match {

                case Failure(exception) =>

                  logger.error(
                    msg = s"Failed to reorder prospect categories. teamId: $teamId :: prospectCategoryId: $prospectCategoryId :: reorderProspectCategoryData: $reorderProspectCategoryData",
                    err = exception,
                  )

                  Res.ServerError(
                    message = "Failed to reorder prospect categories.",
                    e = None,
                  )

                case Success(Left(errMsg)) =>

                  logger.error(
                    msg = s"Failed to reorder prospect categories - $errMsg. teamId: $teamId :: prospectCategoryId: $prospectCategoryId :: reorderProspectCategoryData: $reorderProspectCategoryData"
                  )

                  Res.BadRequestError(
                    message = errMsg
                  )

                case Success(Right(orderedProspectCategories)) =>

                  // Need to reset the cache,
                  // or else on page refresh api will return the stale account object.
                  resetUserCacheUtil._resetTeamCache(
                    aid = teamAccount.user_id
                  )

                  Res.Success(
                    message = "Successfully reordered prospect categories!",
                    data = Json.toJson(orderedProspectCategories)
                  )

              }

          }

      }

    }

  }

  def updateCustomProspectCategory(v: String, id: Long, aid: Option[Long], tid: Option[Long]) =

    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_TEAM_CONFIG,
      tidOpt = tid
    ).async(parse.json) { request =>

      Future {
        given Logger: SRLogger= request.Logger
        val Res = request.Response

        val cannotUpdateCustomCategories = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
          teamId = TeamId(tid.get),
          feature = SrRollingUpdateFeature.NewReplySentiments
        )(Logger)

        if (request.actingTeamAccount.isEmpty) {
          Logger.shouldNeverHappen(
            msg = s"updateCustomProspectCategory - actingTeamAccount None - teamId: $tid :: accountId: ${request.loggedinAccount.internal_id}"
          )
          Res.BadRequestError("Invalid team")

        } else if(cannotUpdateCustomCategories) {
          Res.BadRequestError("You cannot update custom categories in this team")
        } else {

          val t = request.actingTeamAccount
          val a = request.loggedinAccount

          val validateData = request.body.validate[ProspectCustomCategoryCreateOrUpdateFormData]

          validateData match {

            case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

            case JsSuccess(data, _) =>

              val text_id = data.name.trim.replaceAll(" ", "_").toLowerCase()

              val category = prospectDAOService.getProspectCategoryById(team_id = t.get.team_id, category_id = id, None)

              if (category.isEmpty) {

                Res.BadRequestError("Invalid category id")

              }
              else if (category.nonEmpty && !category.get.is_custom) {

                Res.BadRequestError("You can't edit default category")

              }
              else if (text_id.isEmpty) {

                Res.BadRequestError("Invalid category name")

              }
              else if (!data.label_color.matches("^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$")) {

                Res.BadRequestError("Invalid label color")

              }
              else {

                prospectDAOService.updateOneCustomProspectCategory(
                  id = id,
                  accountId = a.internal_id,
                  teamId = t.get.team_id,
                  accountName = Helpers.getAccountName(a = a),
                  categoryName = data.name,
                  text_id = text_id,
                  label_color = data.label_color
                ) match {

                  case Failure(e) =>

                    e.getMessage match {

                      case msg if msg.contains("violates unique constraint") => Res.BadRequestError("Another category with this name already exists")

                      case msg => Res.ServerError("Error while updating category: " + msg, e = Some(e))

                    }

                  case Success(None) => Res.NotFoundError(s"Category not found. Could you check and try again ?")

                  case Success(Some(updatedCategory)) =>

                    resetUserCacheUtil._resetTeamCache(aid = request.loggedinAccount.internal_id)

                    Res.Success(s"Category '${data.name}' has been updated!",
                      Json.obj("account" -> accountService.find(id = a.internal_id).toOption))

                }
              }
          }

        }

      }
    }


  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  // use case : ProspectController.deleteCustomProspectCategory DELETE     /api/:v/settings/prospect_categories/:id
  def deleteCustomProspectCategory(v: String, id: Long, aid: Option[Long], tid: Option[Long]) =

    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_TEAM_CONFIG,
      tidOpt = tid
    ).async(parse.json) { request =>

      Future {
        given Logger: SRLogger= request.Logger
        val Res = request.Response

        val cannotDeleteCustomCategories = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
          teamId = TeamId(tid.get),
          feature = SrRollingUpdateFeature.NewReplySentiments
        )(Logger)

        if (request.actingTeamAccount.isEmpty) {

          Res.BadRequestError("Invalid team")

        } else if(cannotDeleteCustomCategories) {
          Logger.shouldNeverHappen(
            msg = s"deleteCustomProspectCategory - cannotDeleteCustomCategories - teamId: ${request.actingTeamAccount.get.team_id} :: accountId: ${request.loggedinAccount.internal_id}"
          )
          Res.BadRequestError("You cannot delete custom categories in this team")
        } else {

          val replacement_category_id: Option[Long] = (request.body \ "replacement_category_id").asOpt[Long]

          if (replacement_category_id.isEmpty) {

            Res.BadRequestError("Please send new_category_id to delete")

          } else {

            val t = request.actingTeamAccount.get
            val a = request.loggedinAccount

            val category = prospectDAOService.getProspectCategoryById(team_id = t.team_id, category_id = id, account = Some(a))

            val new_category = prospectDAOService.getProspectCategoryById(team_id = t.team_id, category_id = replacement_category_id.get, account = Some(a))

            if (category.isEmpty) {

              Res.BadRequestError("Invalid category id")

            }
            else if (!category.get.is_custom) {

              Res.BadRequestError("You can't delete default category")

            } else if (new_category.isEmpty) {

              Res.BadRequestError("Invalid new category id")

            } else {

              prospectDAOService.findCustomProspectCategoryTaggedProspectsCount(prospect_category_id_custom = id, team_id = t.team_id) match {

                case Failure(e) =>

                  Res.ServerError("There was an error. Please try again or contact support.", e = Some(e))

                case Success(prospects) =>

                  if (prospects >= 10000) {

                    val accountName = Helpers.getAccountName(request.loggedinAccount)
                    val accountEmail = request.loggedinAccount.email

                    emailNotificationService.sendMailFromAdmin(
                      toEmail = "<EMAIL>",
                      toName = Some("SmartReach Support"),

                      ccEmail = Some("<EMAIL>, <EMAIL>"),

                      subject = s"[SR_TASK] Delete prospect category request from : ${accountEmail} : ${accountName}",
                      body =
                        s"""
                       <br/>Name: ${accountName}
                       <br/>Email: ${accountEmail}
                       <br/>Account Id: ${a.internal_id}
                       <br/>Team Id: ${t.team_id}
                       <br/>Category Name: ${category.get.name}
                       <br/>Category text_id: ${category.get.text_id}
                       <br/>Category id: ${category.get.id}
                       <br/>Category team Id: ${category.get.team_id}
                        """.stripMargin
                    )

                    Res.Success(s"Prospect category is scheduled for deletion. It should get deleted in 24 hours.",
                      Json.obj("account" -> accountService.find(id = a.internal_id).toOption))

                  } else {

                    //PROSPECTS_EMAILS_TODO_READ_CLEANED
                    prospectServiceV2.fetchProspectsByProspectCategoryIdCustom(
                      prospect_category_id_custom = id,
                      teamId = t.team_id,
                      Logger = Logger
                    ) match {

                      case Failure(e) =>

                        Res.ServerError("Error while deleting category: ", e = Some(e))

                      case Success(prospects) =>

                        prospectUpdateCategoryTemp.updateBatchCategory(prospects = prospects,
                          doerAccountId = a.internal_id,
                          accountName = Helpers.getAccountName(a = a),
                          teamId = t.team_id,
                          prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AdminUpdate(
                            old_prospect_category_id = None,
                            new_prospect_category_id = ProspectCategoryId(id = replacement_category_id.get)
                          ),
                          account = a,
                          logger = Logger,
                          auditRequestLogId = Some(request.auditRequestLogId)) match {

                          case Failure(e) => Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))

                          case Success(prospectsUpdated) =>

                            if (prospectsUpdated == 0) {
                              Logger.error(s"Prospect category could not be updated because some prospects is likely in Do Not Contact List. OR ALL Prospects are in Do Not Contact list: ${request.loggedinAccount.internal_id} :: team: $t :: category: $category")
                            }

                            prospectDAOService.deleteCustomProspectCategory(
                              id = id,
                              replacement_category_id = replacement_category_id.get,
                              permittedAccountIds = request.permittedAccountIds,
                              teamId = t.team_id
                            ) match {

                              case Failure(e) =>

                                Res.ServerError("Error while deleting category: ", e = Some(e))

                              case Success(n) =>

                                resetUserCacheUtil._resetTeamCache(aid = request.loggedinAccount.internal_id)

                                if (n == 1) Res.Success(s"Category has been deleted!",
                                  Json.obj("account" -> accountService.find(id = a.internal_id).toOption))
                                else Res.NotFoundError(s"You don't have the permission to delete this Category. Could you please check and retry ?")

                            }
                        }
                    }

                  }
              }
            }

          }
        }

      }
    }

  def deleteCustomColumn(v: String, id: Long, aid: Option[Long], tid: Option[Long]) =

    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_TEAM_CONFIG,
      tidOpt = tid
    ).async(parse.json) { request =>

      Future {
        given Logger: SRLogger= request.Logger
        val Res = request.Response

        if (request.actingTeamAccount.isEmpty) {

          Res.BadRequestError("Invalid team")

        } else {

          val name: Option[String] = (request.body \ "name").asOpt[String]

          if (name.isEmpty) {

            Res.BadRequestError("Please send column name to delete")

          } else {

            val t = request.actingTeamAccount
            val a = request.loggedinAccount

            val isColumnBelongsToTeam = prospectColumnDef.checkIfColumnBelongsToTeam(id = id, name = name.get, teamId = t.get.team_id)

            if (!isColumnBelongsToTeam) {

              Res.BadRequestError(s"Column $name does not belong to team")

            }
            else {

              prospectColumnDef.getProspectsCount(team_id = t.get.team_id) match {

                case Failure(e) =>
                  Logger.error(s"Error while deleting column: getProspectsCount", e)

                  Res.ServerError("There was an error. Please try again or contact support.", e = Some(e))

                case Success(prospects) =>

                  prospectColumnDef.checkIfColumnToBeDeletedIsBeingUsed(
                    teamId = TeamId(id = t.get.team_id),
                    colToBeDeletedColId = id,
                    colToBeDeletedName = name.get,
                  ) match {

                    case Left(CustomColumnDeleteError.InternalError(exception)) =>

                      val errMsg = s"Failed to delete custom column - ${exception.getMessage}"

                      Logger.shouldNeverHappen(
                        msg = s"getMagicColsUsingColToBeDeleted failed - $errMsg. teamId: ${t.get.team_id} :: columnId: $id :: columnName: $name",
                        err = Some(exception)
                      )

                      Res.ServerError(message = errMsg, e = None)

                    case Left(CustomColumnDeleteError.CustomColumnUsedInMagicColumn(errMsg)) =>

                      Logger.error(
                        msg = s"$errMsg. teamId: ${t.get.team_id} :: columnId: $id :: columnName: $name"
                      )

                      Res.BadRequestError(message = errMsg)

                    case Left(CustomColumnDeleteError.CustomColumnUsedInTasks) =>

                      val errMsg = "Column to be deleted is being used in some tasks, Please the merge tag from those tasks."

                      Logger.error(
                        msg = s"$errMsg. teamId: ${t.get.team_id} :: columnId: $id :: columnName: $name"
                      )

                      Res.BadRequestError(message = errMsg)

                    case Left(CustomColumnDeleteError.CustomColumnUsedInCampaignSteps) =>

                      val errMsg = "Column to be deleted is being used in some campaign steps, Please the merge tag from those campaign steps."

                      Logger.error(
                        msg = s"$errMsg. teamId: ${t.get.team_id} :: columnId: $id :: columnName: $name"
                      )

                      Res.BadRequestError(message = errMsg)

                    case Left(CustomColumnDeleteError.CustomColumnUsedInTemplates) =>

                      val errMsg = "Column to be deleted is being used in some templates, Please the merge tag from those templates."

                      Logger.error(
                        msg = s"$errMsg. teamId: ${t.get.team_id} :: columnId: $id :: columnName: $name"
                      )

                      Res.BadRequestError(message = errMsg)

                    case Right(_) =>

                      if (prospects >= 10000) {

                        val accountName = Helpers.getAccountName(request.loggedinAccount)
                        val accountEmail = request.loggedinAccount.email

                        emailNotificationService.sendMailFromAdmin(
                          toEmail = "<EMAIL>",
                          toName = Some("SmartReach Support"),

                          ccEmail = Some("<EMAIL>, <EMAIL>"),

                          subject = s"[SR_TASK] Column delete request from : ${accountEmail} : ${accountName}",
                          body =
                            s"""
                       <br/>Name: ${accountName}
                       <br/>Email: ${accountEmail}
                       <br/>Account Id: ${a.internal_id}
                       <br/>Team Id: ${t.get.team_id}
                       <br/>Column Id: ${id}
                       <br/>Column Name: ${name.getOrElse("")}
                        """.stripMargin
                        )

                        Res.Success(s"Column is scheduled for deletion. It should get deleted in 24 hours.",
                          Json.obj("account" -> accountService.find(id = a.internal_id).toOption)
                        )

                      } else {

                        prospectColumnDef.deleteCustomColumn(
                          columnId = id,
                          name = name.get,
                          permittedAccountIds = request.permittedAccountIds,
                          teamId = t.get.team_id
                        ) match {

                          case Failure(e) =>

                            Logger.error(s"Error while deleting column: ", e)

                            Res.ServerError("Error while deleting column: ", e = Some(e))

                          case Success(n) =>

                            resetUserCacheUtil._resetTeamCache(aid = request.loggedinAccount.internal_id)

                            if (n == 1) Res.Success(s"Column has been deleted!",
                              Json.obj("account" -> accountService.find(id = a.internal_id).toOption)
                            )
                            else Res.NotFoundError(s"You don't have the permission to delete this column. Could you please check and retry ?")

                        }
                      }
                  }
              }
            }

          }
        }

      }
    }


  /*
  def updateBatch(v: String, aid: Option[Long], tid: Option[Long]) =

    checkPermission(
      version = v,
      permission = PermType.EDIT_PROSPECTS,
      aidOpt = aid,
      tidOpt = tid
    ).async(parse.json) { request =>

      Future {
        val Logger = request.Logger
        val Res = request.Response

        if (request.actingTeamAccount.isEmpty) {

          Res.BadRequestError("Invalid team")

        } else {

          val t = request.actingTeamAccount.get
          val a = request.loggedinAccount

          val data = request.body.validate[ProspectBatchUpdate]

          data match {

            case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

            case JsSuccess(data, _) =>

              val prospectIds = if (data.is_select_all) {

                var ids: Seq[Long] = Seq()
                val queryIdsFuture = Prospect.IdsOnlyQuery(
                  accountIds = request.permittedAccountIds,
                  teamId = t.team_id,
                  loggedinAccountId = a.id,
                  prospectQuery = data.filters.get,
                  account = a,
                  isInternalRequest = true,
                  Logger = Logger
                )

                queryIdsFuture.map(queryIds => {
                  ids = queryIds
                })

                ids


              } else {

                if (data.prospect_ids.isDefined) data.prospect_ids.get else Seq()

              }


              val action = data.action_name

              if (prospectIds.length > 500) {

                Res.BadRequestError("Selected prospects could not be more that 500")

              } else {


                ParseUtils.parseLong(data.action_value) match {

                  case None =>

                    Res.BadRequestError("Invalid action value")

                  case Some(actionValue) =>

                    Prospect.fetchProspectsByIdsForBatchUpdate(
                      prospectsIds = prospectIds,
                      permittedAccountIds = request.permittedAccountIds,
                      teamId = t.team_id
                    ) match {

                      case Failure(e) =>

                        Res.ServerError("Invalid prospect ids: ", e = Some(e))

                      case Success(prospects) =>

                        action match {

                          case "category_update" =>


                            Prospect.findProspectCategoryById(team_id = t.team_id, category_custom_id = actionValue) match {

                              case Failure(e) =>

                                Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))

                              case Success(None) =>

                                Logger.error(s"FATAL ProspectController.updateBatch findProspectCategoryById Invalid category_custom_id: $actionValue")

                                Res.BadRequestError("Invalid category id")


                              case Success(Some(pc)) =>

                                Prospect.updateBatchCategory(
                                  prospects = prospects,
                                  newProspectCategoryIdCustom = actionValue,
                                  doerAccountId = a.id,
                                  accountName = Helpers.getAccountName(a = a),
                                  teamId = t.team_id,
                                  taId = t.ta_id,
                                  prospectStatusUpdatedManually = true,
                                  account = a) match {

                                  case Failure(e) => Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))

                                  case Success(_) => Res.Success(s"Prospect status has been updated!", Json.obj())

                                }


                            }


                          case "prospect_account" =>

                            ProspectAccount.find(id = actionValue, teamId = t.team_id) match {

                              case None =>

                                Logger.error(s"FATAL ProspectController.updateBatch ProspectAccount.find Invalid account id: $actionValue")

                                Res.BadRequestError("Invalid account id")

                              case Some(pa) =>

                                Prospect.updateBatchAccount(
                                  prospects = prospects,
                                  newProspectAccountId = actionValue
                                ) match {

                                  case Failure(e) => Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))

                                  case Success(_) => Res.Success(s"Account has been updated!", Json.obj())

                                }


                            }


                        }
                    }
                }
              }


          }

        }

      }
    }
*/

  // use case : ProspectController.batchActions "export"
  def batchActions(v: ApiVersion, action_type: String, aid: Option[Long], tid: Option[Long]) = {

    permissionUtils.checkPermissionV2(
      version = v,
      permission = PermType.EDIT_PROSPECTS,
      tidOpt = tid,
      id = None
    ).async(parse.json) { request =>

      //      Future {
      given Logger: SRLogger = request.Logger
      implicit val apiVersion: ApiVersion = v
      val Res = request.Response

      val isApiCall = request.isApiCall

      if (request.actingTeamAccount.isEmpty) {

        Future.successful(Res.BadRequestError("Invalid team"))

      } else {

        val t = request.actingTeamAccount.get
        val teamId = t.team_id
        val a = request.loggedinAccount
        val doerAccountId = t.user_id
        val doerAccountName = Helpers.getAccountName(a = a)
        val currentTeam = a.teams.find(team => team.team_id == teamId).get
        val permittedAccountIds = request.permittedAccountIds
        val auditRequestLogId = request.auditRequestLogId
        val userId = t.user_id
        val teamName = t.team_name.trim.toLowerCase.replace(" ", "_").replace(".", "_")
        val replyHandling = currentTeam.reply_handling

        val getPermittedAccountIds = PermissionMethods.getPermittedAccountIdsForAccountAndPermission(
          loggedinAccount = a,
          actingTeamId = teamId,
          actingAccountId = t.user_id,
          version = v.toString,
          Logger = Logger
        )

        val permittedAccountIdsForEditingProspects = request.permittedAccountIds
        val permittedAccountIdsForEditingCampaigns = getPermittedAccountIds(PermType.EDIT_CAMPAIGNS)

        val validateData = request.body.validate[ProspectBatchActions]
        validateData match {

          case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

          case JsSuccess(data, _) =>

            if (isApiCall) {

              Future.successful(
                Res.ForbiddenError("Your are not authorized to do this")
              )

            } else if (isApiCall &&
              (data.is_select_all.isDefined ||
                data.filters.isDefined
                )
            ) {

              Future.successful(
                Res.ForbiddenError("filters are not support via API. Contact support if you have any questions")
              )

            } else if (isApiCall &&
              data.prospect_ids.isEmpty
            ) {

              Future.successful(
                Res.BadRequestError("prospect_ids cannot be empty")
              )

            } else if (isApiCall &&
              data.prospect_ids.size > 500
            ) {

              Future.successful(
                Res.BadRequestError("You can send upto 500 ids in prospect_ids at a time")
              )

            } else {

              val prospectIdsFuture: Future[Seq[Long]] = prospectBatchActionService.getProspectIdsForAction(
                data = data,
                actingTeam = t,
                loginAccount = a,
                permittedAccountIds = permittedAccountIds,
                permittedAccountIdsForEditingProspects = permittedAccountIdsForEditingProspects
              )


              prospectIdsFuture.flatMap(prospectIds => {
                  if (prospectIds.isEmpty) {
                    val msg = "We could not find any prospects with applied filters, Please try again or contact support"
                    Future.successful(Res.BadRequestError(
                      message = msg,
                      version = v,
                      errorResponse = List(ErrorResponseUpdateProspectStatusApi(
                        error_type = UpdateProspectStatusErrorType.BAD_REQUEST,
                        message = msg,
                        data = None
                      ))
                    ))

                  } else {

                    Future {
                      action_type match {

                        case "category_update" =>
                          val validateCategoryUpdateData = request.body.validate[ProspectBatchActionUpdateCategory]

                          validateCategoryUpdateData match {

                            case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

                            case JsSuccess(data1, _) =>
                              prospectBatchActionService.updateCategory(
                                data = data1,
                                teamId = teamId,
                                loginAccount = a,
                                prospectIds = prospectIds,
                                Logger = Logger,
                                permittedAccountIds = permittedAccountIds,
                                auditRequestLogId = auditRequestLogId
                              ) match {
                                case Left(e: BatchActionErrors) =>
                                  e match {
                                    case BatchActionErrors.BadRequestError(error) =>
                                      Logger.fatal(s"BatchActionErrors updateCategory $action_type error $error")
                                      Res.BadRequestError(message = error, version = v)
                                    case BatchActionErrors.ServerError(error) =>
                                      Logger.fatal(s"BatchActionErrors updateCategory $action_type", error)
                                      Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${error.getMessage}", e = Some(error))
                                  }
                                case Right(batchActionResponse: BatchActionResponse) =>
                                  Res.Success(
                                    message = batchActionResponse.responseMsg,
                                    data = batchActionResponse.responseObj,
                                    apiVersion = v.toString
                                  )
                              }
                          }

                        case "prospect_delete" =>
                          prospectBatchActionService.deleteProspect(
                            teamId = teamId,
                            prospectIds = prospectIds,
                            Logger = Logger,
                            permittedAccountIds = permittedAccountIds,
                          ) match {
                            case Left(e: BatchActionErrors) =>
                              e match {
                                case BatchActionErrors.BadRequestError(error) =>
                                  Logger.fatal(s"BatchActionErrors deleteProspect $action_type error $error")
                                  Res.BadRequestError(message = error, version = v)
                                case BatchActionErrors.ServerError(error) =>
                                  Logger.fatal(s"BatchActionErrors deleteProspect $action_type", error)
                                  Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${error.getMessage}", e = Some(error))
                              }
                            case Right(batchActionResponse: BatchActionResponse) =>
                              Res.Success(
                                message = batchActionResponse.responseMsg,
                                data = batchActionResponse.responseObj,
                                apiVersion = v.toString
                              )
                          }

                        case "assign_to_campaign" =>
                          val validateAssignToCampaignData = request.body.validate[ProspectBatchActionAssignToCampaign]

                          validateAssignToCampaignData match {

                            case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

                            case JsSuccess(data1, _) =>
                              prospectBatchActionService.assignToCampaign(
                                data = data1,
                                teamId = teamId,
                                userId = userId,
                                loginAccount = a,
                                prospectIds = prospectIds,
                                permittedAccountIdsForEditingProspects = permittedAccountIdsForEditingProspects,
                                permittedAccountIdsForEditingCampaigns = permittedAccountIdsForEditingCampaigns
                              ) match {
                                case Left(e: BatchActionErrors) =>
                                  e match {
                                    case BatchActionErrors.BadRequestError(error) =>
                                      Logger.fatal(s"BatchActionErrors assignToCampaign $action_type error $error")
                                      Res.BadRequestError(message = error, version = v)
                                    case BatchActionErrors.ServerError(error) =>
                                      Logger.fatal(s"BatchActionErrors assignToCampaign $action_type", error)
                                      Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${error.getMessage}", e = Some(error))
                                  }
                                case Right(batchActionResponse: BatchActionResponse) =>
                                  Res.Success(
                                    message = batchActionResponse.responseMsg,
                                    data = batchActionResponse.responseObj,
                                    apiVersion = v.toString
                                  )
                              }
                          }

                        case "unassign_from_campaign" =>
                          val validateUnAssignFromCampaignData = request.body.validate[ProspectBatchActionUnAssignFromCampaign]

                          validateUnAssignFromCampaignData match {

                            case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

                            case JsSuccess(data1, _) =>
                              prospectBatchActionService.unAssignFromCampaign(
                                data = data1,
                                teamId = teamId,
                                userId = userId,
                                doerAccountName = doerAccountName,
                                prospectIds = prospectIds,
                                permittedAccountIds = permittedAccountIds,
                                permittedAccountIdsForEditingCampaigns = permittedAccountIdsForEditingCampaigns
                              ) match {
                                case Left(e: BatchActionErrors) =>
                                  e match {
                                    case BatchActionErrors.BadRequestError(error) =>
                                      Logger.fatal(s"BatchActionErrors unAssignFromCampaign $action_type error $error")
                                      Res.BadRequestError(message = error, version = v)
                                    case BatchActionErrors.ServerError(error) =>
                                      Logger.fatal(s"BatchActionErrors unAssignFromCampaign $action_type", error)
                                      Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${error.getMessage}", e = Some(error))
                                  }
                                case Right(batchActionResponse: BatchActionResponse) =>
                                  Res.Success(
                                    message = batchActionResponse.responseMsg,
                                    data = batchActionResponse.responseObj,
                                    apiVersion = v.toString
                                  )
                              }
                          }


                        case "add_tags" =>
                          val validateAddTagData = request.body.validate[ProspectBatchActionAddOrRemoveTag]

                          validateAddTagData match {

                            case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

                            case JsSuccess(data1, _) =>
                              prospectBatchActionService.addTags(
                                data = data1,
                                teamId = teamId,
                                userId = userId,
                                prospectIds = prospectIds,
                                Logger = Logger
                              ) match {
                                case Left(e: BatchActionErrors) =>
                                  e match {
                                    case BatchActionErrors.BadRequestError(error) =>
                                      Logger.fatal(s"BatchActionErrors addTags $action_type error $error")
                                      Res.BadRequestError(message = error, version = v)
                                    case BatchActionErrors.ServerError(error) =>
                                      Logger.fatal(s"BatchActionErrors addTags $action_type", error)
                                      Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${error.getMessage}", e = Some(error))
                                  }
                                case Right(batchActionResponse: BatchActionResponse) =>
                                  Res.Success(
                                    message = batchActionResponse.responseMsg,
                                    data = batchActionResponse.responseObj,
                                    apiVersion = v.toString
                                  )
                              }
                          }


                        case "remove_tags" =>
                          val validateRemoveTagData = request.body.validate[ProspectBatchActionAddOrRemoveTag]

                          validateRemoveTagData match {

                            case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

                            case JsSuccess(data1, _) =>
                              prospectBatchActionService.removeTags(
                                data = data1,
                                teamId = teamId,
                                userId = userId,
                                prospectIds = prospectIds,
                                Logger = Logger
                              ) match {
                                case Left(e: BatchActionErrors) =>
                                  e match {
                                    case BatchActionErrors.BadRequestError(error) =>
                                      Logger.fatal(s"BatchActionErrors removeTags $action_type error $error")
                                      Res.BadRequestError(message = error, version = v)
                                    case BatchActionErrors.ServerError(error) =>
                                      Logger.fatal(s"BatchActionErrors removeTags $action_type", error)
                                      Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${error.getMessage}", e = Some(error))
                                  }
                                case Right(batchActionResponse: BatchActionResponse) =>
                                  Res.Success(
                                    message = batchActionResponse.responseMsg,
                                    data = batchActionResponse.responseObj,
                                    apiVersion = v.toString
                                  )
                              }
                          }


                        case "ownership_change" =>
                          val validateChangeOwnerShipData = request.body.validate[ProspectBatchActionChangeOwnerShip]

                          validateChangeOwnerShipData match {

                            case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

                            case JsSuccess(data1, _) =>
                              prospectBatchActionService.changeOwnerShip(
                                data = data1,
                                teamId = teamId,
                                userId = userId,
                                prospectIds = prospectIds,
                                Logger = Logger,
                                loginAccount = a,
                                v = v.toString
                              ) match {
                                case Left(e: BatchActionErrors) =>
                                  e match {
                                    case BatchActionErrors.BadRequestError(error) =>
                                      Logger.fatal(s"BatchActionErrors changeOwnerShip $action_type error $error")
                                      Res.BadRequestError(message = error, version = v)
                                    case BatchActionErrors.ServerError(error) =>
                                      Logger.fatal(s"BatchActionErrors changeOwnerShip $action_type", error)
                                      Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${error.getMessage}", e = Some(error))
                                  }
                                case Right(batchActionResponse: BatchActionResponse) =>
                                  Res.Success(
                                    message = batchActionResponse.responseMsg,
                                    data = batchActionResponse.responseObj,
                                    apiVersion = v.toString
                                  )
                              }
                          }

                        // use case : ProspectController.batchActions "export"
                        case "export" =>
                          prospectBatchActionService.batchActionExport(
                            teamId = teamId,
                            teamName = teamName,
                            prospectIds = prospectIds,
                          ) match {
                            case Left(e: BatchActionErrors) =>
                              e match {
                                case BatchActionErrors.BadRequestError(error) =>
                                  Logger.fatal(s"BatchActionErrors batchActionExport $action_type error $error")
                                  Res.BadRequestError(message = error, version = v)
                                case BatchActionErrors.ServerError(error) =>
                                  Logger.fatal(s"BatchActionErrors batchActionExport $action_type", error)
                                  Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${error.getMessage}", e = Some(error))
                              }
                            case Right(result) =>
                              result
                          }


                        case "campaign_pause_unpause_for_the_prospects" =>
                          val validateCampaignPauseUnpauseForProspectData = request.body.validate[ProspectBatchActionCampaignPauseUnpauseForProspect]

                          validateCampaignPauseUnpauseForProspectData match {

                            case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

                            case JsSuccess(data1, _) =>
                              prospectBatchActionService.campaignPauseUnPauseForProspect(
                                data = data1,
                                teamId = teamId,
                                userId = userId,
                                prospectIds = prospectIds,
                                permittedAccountIds = permittedAccountIds
                              ) match {
                                case Left(e: BatchActionErrors) =>
                                  e match {
                                    case BatchActionErrors.BadRequestError(error) =>
                                      Logger.fatal(s"BatchActionErrors campaignPauseUnPauseForProspect $action_type error $error")
                                      Res.BadRequestError(message = error, version = v)
                                    case BatchActionErrors.ServerError(error) =>
                                      Logger.fatal(s"BatchActionErrors campaignPauseUnPauseForProspect $action_type", error)
                                      Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${error.getMessage}", e = Some(error))
                                  }
                                case Right(batchActionResponse: BatchActionResponse) =>
                                  Logger.debug(s"batchActionResponse campaignPauseUnPauseForProspect by accountId - ${userId} :: prospectIds - ${prospectIds} :: campaignIds - ${data1.campaign_ids} :: newStatus - ${data1.new_pause_status}")
                                  Res.Success(
                                    message = batchActionResponse.responseMsg,
                                    data = batchActionResponse.responseObj,
                                    apiVersion = v.toString
                                  )
                              }
                          }


                        case "prospect_status_change" =>
                          val validateProspectStatusChangeData = request.body.validate[ProspectBatchActionStatusChange]

                          validateProspectStatusChangeData match {

                            case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

                            case JsSuccess(data1, _) =>
                              prospectBatchActionService.prospectBatchActionStatusChange(
                                data = data1,
                                teamId = teamId,
                                orgId = OrgId(a.org.id),
                                prospectIds = prospectIds,
                                doerAccountId = doerAccountId,
                                replyHandling = replyHandling,
                                doerAccountName = doerAccountName,
                                permittedAccountIds = permittedAccountIds,
                                isApiCall = isApiCall
                              ) match {
                                case Left(error: UpdateProspectStatusErrorV3) =>
                                  error match {
                                    case UpdateProspectStatusErrorV3.UpdateProspectsStatusError(err) =>
                                      Logger.fatal(s"BatchActionErrors prospectBatchActionStatusChange $action_type error $error")
                                      Res.BadRequestError(
                                        message = err,
                                        version = v,
                                        errorResponse = List(ErrorResponseUpdateProspectStatusApi(
                                          error_type = UpdateProspectStatusErrorType.BAD_REQUEST,
                                          message = err,
                                          data = None
                                        )))

                                    case UpdateProspectStatusErrorV3.UpdateProspectsStatusValidationError(err) =>
                                      Logger.fatal(s"BatchActionErrors prospectBatchActionStatusChange $action_type error $error")
                                      Res.BadRequestError(
                                        message = "Error while validating data",
                                        version = v,
                                        errorResponse = err
                                      )

                                    case UpdateProspectStatusErrorV3.FilterOwnedProspectError(err) =>
                                      Logger.fatal(s"BatchActionErrors prospectBatchActionStatusChange $action_type error $error")
                                      Res.ServerError("There was an error. Please contact support", e = None)

                                    case UpdateProspectStatusErrorV3.GetProspectIdFromUuidErrors(err) =>
                                      err match {
                                        case GetProspectIdFromUuidError.GetProspectIdError(err) =>
                                          Logger.error("BatchActionErrors prospectBatchActionStatusChange Error while parsing uuids", err = err)
                                          Res.ServerError("There was an error. Please contact support", e = None)
                                      }

                                    case UpdateProspectStatusErrorV3.GetCampaignIdFromUuidErrors(err) =>
                                      err match {
                                        case GetCampaignIdFromUuidError.GetCampaignIdError(err) =>
                                          Logger.error("BatchActionErrors prospectBatchActionStatusChange Error while parsing uuids", err = err)
                                          Res.ServerError("There was an error. Please contact support", e = None)
                                      }

                                    case UpdateProspectStatusErrorV3.GetProspectUuidFromId(err) =>
                                      Logger.error("BatchActionErrors prospectBatchActionStatusChange Error while parsing ids to get uuid", err = err)
                                      Res.ServerError("There was an error. Please contact support", e = None)

                                    case UpdateProspectStatusErrorV3.ServerError(error) =>
                                      Logger.fatal(s"BatchActionErrors prospectBatchActionStatusChange $action_type", error)
                                      Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${error.getMessage}", e = Some(error))

                                    case UpdateProspectStatusErrorV3.FilterCampaignOwnedProspectError(err) =>
                                      Logger.fatal(s"BatchActionErrors prospectBatchActionStatusChange prospect_batch_action error $error")
                                      Res.ServerError("There was an error. Please contact support", e = None)

                                  }
                                case Right(batchActionResponse: BatchActionResponse) =>
                                  Res.Success(
                                    message = batchActionResponse.responseMsg,
                                    data = batchActionResponse.responseObj,
                                    apiVersion = v.toString
                                  )
                              }
                          }

                        case "prospect_account" =>
                          val validateProspectAccountData = request.body.validate[ProspectBatchActionProspectAccount]

                          validateProspectAccountData match {

                            case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

                            case JsSuccess(data1, _) =>
                              prospectBatchActionService.prospectAccount(
                                data = data1,
                                teamId = teamId,
                                prospectIds = prospectIds,
                                Logger = Logger,
                                permittedAccountIds = permittedAccountIds
                              ) match {
                                case Left(e: BatchActionErrors) =>
                                  e match {
                                    case BatchActionErrors.BadRequestError(error) =>
                                      Logger.fatal(s"BatchActionErrors prospectAccount $action_type error $error")
                                      Res.BadRequestError(message = error, version = v)
                                    case BatchActionErrors.ServerError(error) =>
                                      Logger.fatal(s"BatchActionErrors prospectAccount $action_type", error)
                                      Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${error.getMessage}", e = Some(error))
                                  }
                                case Right(batchActionResponse: BatchActionResponse) =>
                                  Res.Success(
                                    message = batchActionResponse.responseMsg,
                                    data = batchActionResponse.responseObj,
                                    apiVersion = v.toString
                                  )
                              }
                          }
                      }
                    }
                  }
                })
                .recover {
                  case e => Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))
                }

            }

        }
      }
    }
  }

  def prospectStatusChangeV3(aid: Option[Long], tid: Option[Long]) =
    permissionUtils.checkPermission(
      version = ApiVersion.V3.textId,
      permission = PermType.EDIT_PROSPECTS,
      tidOpt = tid,
      apiAccess = true
    ).async(parse.json) { request =>
      given Logger: SRLogger = request.Logger
      implicit val apiVersion: ApiVersion = ApiVersion.V3
      val Res = request.Response

      if (request.actingTeamAccount.isEmpty) {

        Future.successful(Res.BadRequestError("Invalid team"))

      } else {

        val t = request.actingTeamAccount.get
        val teamId = t.team_id
        val a = request.loggedinAccount
        val doerAccountId = t.user_id
        val doerAccountName = Helpers.getAccountName(a = a)
        val currentTeam = a.teams.find(team => team.team_id == teamId).get
        val permittedAccountIds = request.permittedAccountIds
        val replyHandling = currentTeam.reply_handling
        val isApiCall = request.isApiCall

        val permittedAccountIdsForEditingProspects = request.permittedAccountIds

        val validateData = request.body.validate[ProspectBatchActions]
        validateData match {

          case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

          case JsSuccess(data, _) =>

            if (
              data.is_select_all.isDefined || data.filters.isDefined
            ) {

              Future.successful(
                Res.ForbiddenError("filters are not support via API. Contact support if you have any questions")
              )

            } else {

              prospectBatchActionService.getProspectIdsFromUuids(
                data = data,
                teamId = teamId
              ) match {
                case Left(errors) =>
                  errors match {
                    case BatchActionErrors.ServerError(error) =>
                      Logger.fatal(s"BatchActionErrors prospectBatchActionStatusChange prospect_batch_action error $error")
                      Future {
                        Res.ServerError("There was an error while updating status. Please contact support", e = None)
                      }
                    case BatchActionErrors.BadRequestError(error) =>
                      Future {
                        Res.BadRequestError(
                          message = error,
                          version = ApiVersion.V3,
                          errorResponse = List(ErrorResponseUpdateProspectStatusApi(
                            error_type = UpdateProspectStatusErrorType.BAD_REQUEST,
                            message = error,
                            data = None
                          ))
                        )
                      }
                  }
                case Right(prospectIds) =>
                  Future {
                    val validateProspectStatusChangeData = request.body.validate[ProspectBatchActionStatusChange]

                    validateProspectStatusChangeData match {

                      case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

                      case JsSuccess(data1, _) =>

                        prospectBatchActionService.prospectBatchActionStatusChange(
                          data = data1,
                          teamId = teamId,
                          orgId = OrgId(a.org.id),
                          prospectIds = prospectIds,
                          doerAccountId = doerAccountId,
                          replyHandling = replyHandling,
                          doerAccountName = doerAccountName,
                          permittedAccountIds = permittedAccountIds,
                          isApiCall = isApiCall
                        ) match {
                          case Left(error: UpdateProspectStatusErrorV3) =>
                            error match {
                              case UpdateProspectStatusErrorV3.UpdateProspectsStatusError(err) =>
                                Logger.fatal(s"BatchActionErrors prospectBatchActionStatusChange prospect_batch_action error $error")
                                Res.BadRequestError(
                                  message = err,
                                  version = ApiVersion.V3,
                                  errorResponse = List(ErrorResponseUpdateProspectStatusApi(
                                    error_type = UpdateProspectStatusErrorType.BAD_REQUEST,
                                    message = err,
                                    data = None
                                  )))

                              case UpdateProspectStatusErrorV3.UpdateProspectsStatusValidationError(err) =>
                                Logger.fatal(s"BatchActionErrors prospectBatchActionStatusChange prospect_batch_action error $error")
                                Res.BadRequestError(
                                  message = "Error while validating data",
                                  version = ApiVersion.V3,
                                  errorResponse = err
                                )

                              case UpdateProspectStatusErrorV3.FilterOwnedProspectError(err) =>
                                Logger.fatal(s"BatchActionErrors prospectBatchActionStatusChange prospect_batch_action error $error")
                                Res.ServerError("There was an error. Please contact support", e = None)

                              case UpdateProspectStatusErrorV3.GetProspectIdFromUuidErrors(err) =>
                                err match {
                                  case GetProspectIdFromUuidError.GetProspectIdError(err) =>
                                    Logger.error("BatchActionErrors prospectBatchActionStatusChange Error while parsing uuids", err = err)
                                    Res.ServerError("There was an error. Please contact support", e = None)
                                }

                              case UpdateProspectStatusErrorV3.GetCampaignIdFromUuidErrors(err) =>
                                err match {
                                  case GetCampaignIdFromUuidError.GetCampaignIdError(err) =>
                                    Logger.error("BatchActionErrors prospectBatchActionStatusChange Error while parsing uuids", err = err)
                                    Res.ServerError("There was an error. Please contact support", e = None)
                                }

                              case UpdateProspectStatusErrorV3.GetProspectUuidFromId(err) =>
                                Logger.error("BatchActionErrors prospectBatchActionStatusChange Error while parsing ids to get uuid", err = err)
                                Res.ServerError("There was an error. Please contact support", e = None)

                              case UpdateProspectStatusErrorV3.ServerError(error) =>
                                Logger.fatal(s"BatchActionErrors prospectBatchActionStatusChange prospect_batch_action", error)
                                Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${error.getMessage}", e = Some(error))

                              case UpdateProspectStatusErrorV3.FilterCampaignOwnedProspectError(err) =>
                                Logger.fatal(s"BatchActionErrors prospectBatchActionStatusChange prospect_batch_action error $error")
                                Res.ServerError("There was an error. Please contact support", e = None)

                            }
                          case Right(batchActionResponse: BatchActionResponse) =>
                            Res.Success(
                              message = batchActionResponse.responseMsg,
                              data = batchActionResponse.responseObj,
                              apiVersion = ApiVersion.V3.textId
                            )
                        }
                    }
                  }
              }
            }
        }
      }

    }

  def getInternalEmailsDomains(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_TEAM_CONFIG,
    tidOpt = tid

  ) { (request: PermissionRequest[AnyContent]) =>
    val Logger = request.Logger
    val Res = request.Response
    val team_id = request.actingTeamAccount.get.team_id
    val org_id = request.loggedinAccount.org.id

    prospectService.getAllInternalEmailsInTeam(
      team_id = team_id,
      org_id = org_id,
      Logger = Logger
    ) match {
      case Failure(exception) => Res.ServerError("Error while fetching internal emails and domains", Some(exception))

      case Success(result) => Res.Success("Internal emails & domains found", Json.obj(
        "internal_emails_and_domains" -> result
      ))
    }
  }

  def getExistingProspectsInTeam(v: String, search_key: String, aid: Option[Long], tid: Option[Long]) =
    permissionUtils.checkPermission(
      version = "v2", // only v2 supported
      permission = PermType.EDIT_PROSPECTS,
      tidOpt = tid,
      apiAccess = true
    ) { (request: PermissionRequest[AnyContent]) =>

      val Logger = request.Logger
      val Res = request.Response
      val team_id = request.actingTeamAccount.get.team_id

      val query_string = request.getQueryString("search_key")

      prospectService.searchProspectsForAddToExisting(
        search_string = query_string,
        team_id = team_id
      ) match {
        case Left(SearchProspectsForAddToExisting.DBFailure(e)) => Res.ServerError(e)

        case Left(SearchProspectsForAddToExisting.EmptySearchString(errMsg)) => Res.NotFoundError(errMsg)

        case Right(prospects) => Res.Success("Existing prospects found", Json.obj("prospects" -> prospects))
      }

    }

  def addSecondaryProspectEmail(v: String, pid: Long, tid: Option[Long]) =
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_PROSPECTS,
      tidOpt = tid
    ).andThen(hasProspect(pid)).async(parse.json) {
      (request: ProspectRequest[JsValue]) =>

        Future {
          given Logger: SRLogger= request.Logger
          val Res = request.Response
          val team_id = request.actingTeamAccount.team_id
          val added_by_account_id = request.loggedinAccount.internal_id

          val validateData = request.body.validate[AddEmailToExistingProspectForm]

          validateData match {

            case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

            case JsSuccess(data, _) =>

              prospectService.insertSecondaryProspectEmail(
                team_id = team_id,
                added_by_account_id = added_by_account_id,
                prospect_id = data.prospect_id,
                email = data.email,
                orgId = OrgId(request.loggedinAccount.org.id),
                active_campaign_ids = data.active_campaign_ids
              ) match {

                case Left(InsertSecondaryProspectEmailError.DBFailure(e)) =>
                  Res.ServerError("There was an error. Please try again.", e = Some(e))

                case Left(InsertSecondaryProspectEmailError.InvalidEmailFormat(errMsg)) =>
                  Res.BadRequestError(errMsg)

                case Left(InsertSecondaryProspectEmailError.DuplicateProspect(errMsg)) =>
                  Res.BadRequestError(errMsg)

                case Left(InsertSecondaryProspectEmailError.CampaignUpdateFailed(errMsg)) =>
                  Res.BadRequestError(errMsg)

                case Left(InsertSecondaryProspectEmailError.InternalEmailOrDomainFound(errMsg)) =>
                  Res.BadRequestError(errMsg)

                case Right(_) =>
                  Res.Success("Prospect email added.", Json.obj())
              }
          }
        }

    }

  def findSecondaryProspectEmails(v: String, prospectId: Long, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_PROSPECTS,
    tidOpt = tid
  ) { (request: PermissionRequest[AnyContent]) =>
    val Logger = request.Logger
    val Res = request.Response

    val team_id = request.actingTeamAccount.get.team_id

    prospectServiceV2.getSecondaryProspectEmails(
      prospect_id = prospectId,
      team_id = team_id
    ) match {
      case Failure(exception) => Res.ServerError("Error while fetching secondary prospect emails", Some(exception))

      case Success(result) => Res.Success("Secondary emails found", Json.obj(
        "secondary_emails" -> result
      ))
    }

  }

  //Used in public api v3
  def findAllProspects(
                        tid: Option[Long],
                        older_than: Option[Long],
                        newer_than: Option[Long]
                      ) = permissionUtils.checkPermission(
    version = ApiVersion.V3.toString,
    permission = PermType.VIEW_PROSPECTS,
    tidOpt = tid,
    apiAccess = true
  ) { (request: PermissionRequest[AnyContent]) =>

    val ta = request.actingTeamAccount.get
    val teamId = ta.team_id
    given Logger: SRLogger = request.Logger
    val Res = request.Response

    implicit val version: ApiVersion = ApiVersion.V3

    val query = request.uri
    val parsedQueryParams: Map[String, Vector[String]] = StringUtilsV2.getParams(query)
    val queryParams = ProspectService.validateQueryParams(
      parsedParams = parsedQueryParams
    )

    queryParams match {
      case Failure(exception) => Res.ServerError(exception)
      case Success(filters) =>
        prospectsListingPaginationService.findProspectsListingPageForApi(
          teamId = TeamId(teamId),
          filters = filters
        ) match {
          case Failure(exception) => Res.ServerError(exception)
          case Success(prospects) =>

            val uri = Url.parse(request.uri)
            val page_data = prospects.links

            val nav_links = NavigationLinksResponse(
              next = Helpers.getNextLinkForListingApi(uri = uri, page_data = page_data, search_params = filters),
            )

            apiService.prospectListingApiResponse(
              prospects = prospects.data,
              teamId = TeamId(teamId)
            ) match {
              case Failure(exception) => Res.ServerError(exception)
              case Success(prospectObjectsForApi) =>

                Res.Success(message = "Prospects found",
                  data = Json.obj(
                    "prospects" -> prospectObjectsForApi,
                    "links" -> nav_links
                  ),
                  apiVersion = ApiVersion.V3.toString
                )
            }
        }
    }
  }

    def handleWebhookForCalendly(v: String = "v2") = Action.async(parse.raw) { implicit request =>
        val logRequestId = StringUtils.genLogTraceId
        implicit val logger: SRLogger = new SRLogger(logRequestId = s"$logRequestId handleWebhookForCalendly")

        // Extract query parameters
        val teamId = request.getQueryString("teamId").map(id => TeamId(id.toLong))
        val accountId = request.getQueryString("accountId").map(id => AccountId(id.toLong))
        val orgId = request.getQueryString("orgId").map(id => OrgId(id.toLong))

        logger.info(s"Calendly webhook received for team $teamId, account $accountId")
        logger.debug(s"Webhook Headers: ${request.headers}")
        logger.debug(s"Webhook Body: ${request.body}")
        logger.debug(s"Signature: ${request.headers.get("Calendly-Webhook-Signature")}")

        // Check for missing query parameters
        if (teamId.isEmpty || accountId.isEmpty || orgId.isEmpty) {
            logger.error(s"Missing required parameters: teamId=$teamId, accountId=$accountId, orgId=$orgId")
            Future.successful(BadRequest(Json.obj("error" -> "Missing required parameters")))
        } else {
            // Get raw body for signature validation
            val rawBodyOpt = request.body.asBytes().map(_.utf8String)

            // Validate webhook signature and process payload
            calendlyWebhookService.validateWebhookSignatureAndProcess(
                signatureHeader = request.headers.get("Calendly-Webhook-Signature"),
                rawBody = rawBodyOpt
            ) match {
                case WebhookSignatureValidationResult.InvalidBody(message) =>
                    Future.successful(BadRequest(Json.obj("error" -> message)))

                case WebhookSignatureValidationResult.InvalidSignature(message) =>
                    Future.successful(Unauthorized(Json.obj("error" -> message)))

                case WebhookSignatureValidationResult.InvalidPayload(message) =>
                    Future.successful(BadRequest(Json.obj("error" -> message)))

                case WebhookSignatureValidationResult.UnsupportedEvent(message) =>
                    Future.successful(Ok(Json.obj("message" -> message)))

                case WebhookSignatureValidationResult.Success(inviteeEmail) =>
                    // Call the webhook service to handle the event
                    calendlyWebhookService.handleWebhookEvent(
                        teamId = teamId.get,
                        accountId = accountId.get,
                        orgId = orgId.get,
                        prospectEmail = inviteeEmail
                    ).map { result =>
                        Ok(Json.obj(
                            "message" -> "Webhook processed successfully",
                            "status" -> "success"
                        ))
                    }.recover {
                        case e: Exception =>
                            logger.error(s"Error processing Calendly webhook", e)
                            InternalServerError(Json.obj(
                                "message" -> "Error processing webhook",
                                "error" -> e.getMessage
                            ))
                    }
            }
        }
    }


}
