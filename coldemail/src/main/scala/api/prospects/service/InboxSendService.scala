package api.prospects.service

import api.accounts.models.OrgId
import api.{APIBadRequestException, APINotFoundException, AppConfig}
import api.accounts.{PermType, PermissionMethods, TeamId}
import api.campaigns.CampaignProspectDAO
import api.campaigns.dao.CampaignEmailSettingsDAO
import api.emails.{EmailScheduled, EmailScheduledDAO, EmailSetting, EmailSettingDAO, EmailsScheduledUuid, SendNewManualEmailV2}
import api.emails.models.EmailServiceProviderSendEmail.getEmailServiceProviderSendEmail
import api.emails.models.UpdateEmailScheduledFromExtensionData
import api.rep_tracking_hosts.service.RepTrackingHostService
import api.tasks.services.TaskService
import io.smartreach.esp.api.emails.EmailSettingId
import org.apache.pekko.actor.ActorSystem
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.email.{EmailSenderService, EmailService}
import utils.email.models.SendScheduleEmailType
import utils.mq.email.MQEmailMessage

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class InboxSendService(
  emailScheduledDAO: EmailScheduledDAO,
  taskService: TaskService,
  emailSettingDAO: EmailSettingDAO,
  emailService: EmailService,
  repTrackingHostService: RepTrackingHostService,
  emailSenderService: EmailSenderService,
  campaignEmailSettingsDAO: CampaignEmailSettingsDAO,
  campaignProspectDAO: CampaignProspectDAO

                      ) {

  def updateEmailScheduledAndTaskForManualEmail(
    email_message_id: Long,
    team_id: TeamId,
    permittedAccountIdsForEmailSetting: Seq[Long],
    data: SendNewManualEmailV2
  )(using logger: SRLogger): Try[Option[EmailSetting]] = {

    for {

      /*
        1. updateEmailAndSubjectBody in tasks and emails_scheduled
        2. select required things in one shot
        3. execute send email
       */


      //Fixme: these steps of getting senderEmailSetting and EmailContent is repeated in two flows of this api. Extract in separate function and use in both places
      allTrackingDomainsUsed <- repTrackingHostService.getRepTrackingHosts().map(_.map(_.host_url))

      senderEmailSetting: EmailSetting <- emailService.getSenderEmailSettingForManualEmailTask(
        sender_email_setting_id = EmailSettingId(data.sender_email_setting_id),
        permittedAccountIdsForEmailSetting = permittedAccountIdsForEmailSetting,
        team_id = team_id
      )

      emailContent <- emailService.getBodiesForInboxMessage(

        // fixme newinbox oldinbox also: open/click tracking arent going to work with emailschduledid as 0
        emailsScheduledUuid = EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),

        custom_tracking_domain = senderEmailSetting.custom_tracking_domain,
        default_tracking_domain = senderEmailSetting.default_tracking_domain,

        body = data.body,
        subject = data.subject,

        trackOpens = data.enable_open_tracking.getOrElse(false),
        trackClicks = data.enable_click_tracking.getOrElse(false),

        allTrackingDomainsUsed = allTrackingDomainsUsed,
        teamId = team_id,
        orgId = senderEmailSetting.org_id
      )




      udpate_email_scheduled_subject_bodyOpt: Option[UpdateEmailScheduledFromExtensionData] <- senderEmailSetting.id match {
        case Some(senderEmailSettingIdFound) => {
          if (senderEmailSetting.org_id == OrgId(7949L) || senderEmailSetting.org_id == OrgId(18453L)) {
            logger.doNotTruncate(s"getBodiesForInboxMessage : htmlBody:: ${emailContent} ")
          }
          emailScheduledDAO.updateEmailScheduledSubjectAndBody(
            email_message_id = email_message_id,
            team_id = team_id,
            subject = emailContent.subject,
            text_body = emailContent.textBody,
            base_body = emailContent.baseBody,
            body = emailContent.htmlBody,
            sender_email_settings_id = senderEmailSettingIdFound.emailSettingId
          ).map(Some(_))
        }
        case None => {
          logger.shouldNeverHappen(s"Not calling updateEmailScheduledSubjectAndBody senderEmailSettingIdFound Not Found while sending email tid_${team_id.id} email_message_id:${email_message_id}")
          Success(None)
        }
      }

      udpate_email_message_data_subject_bodyOpt <- senderEmailSetting.id match {
        case Some(senderEmailSettingIdFound) => {
          if (senderEmailSetting.org_id == OrgId(7949L) || senderEmailSetting.org_id == OrgId(18453L)) {
            logger.doNotTruncate(s"getBodiesForInboxMessage : htmlBody:: ${emailContent} ")
          }
          emailScheduledDAO.updateEmailMessageDataSubjectAndBody(
            email_message_id = email_message_id,
            team_id = team_id,
            subject = emailContent.subject,
            text_body = emailContent.textBody,
            base_body = emailContent.baseBody,
            body = emailContent.htmlBody
          ).map(Some(_))
        }
        case None => {
          logger.shouldNeverHappen(s"Not calling updateEmailMessageDataSubjectAndBody senderEmailSettingIdFound Not Found while sending email tid_${team_id.id} email_message_id:${email_message_id}")
          Success(None)
        }
      }



      _: String <- {
        udpate_email_scheduled_subject_bodyOpt match {
          case Some(udpate_email_scheduled_subject_body) => taskService.updateSubjectAndBodyOfTask(
            subject = emailContent.subject,
            body = emailContent.htmlBody,
            step_id = udpate_email_scheduled_subject_body.step_id,
            prospect_id = udpate_email_scheduled_subject_body.prospect_id,
            campaign_id = udpate_email_scheduled_subject_body.campaign_id
          )
          case None => {
            logger.shouldNeverHappen(s"Not Calling updateSubjectAndBodyOfTask  senderEmailSettingIdFound Not Found while sending email tid_${team_id.id} email_message_id:${email_message_id}")

            Success("")
          }
        }
      }

      campaingEmailSettingIdOpt: Option[Long]<- udpate_email_scheduled_subject_bodyOpt match {

        case Some(udpate_email_scheduled_subject_body) => campaignEmailSettingsDAO.getCampaignEmailSettingIdByEmailSettingId(
            emailSettingId = udpate_email_scheduled_subject_body.inbox_email_setting_id,
            campaignId =udpate_email_scheduled_subject_body.campaign_id,
            teamId = team_id
          )

        case None => {
          logger.shouldNeverHappen(s"Not Calling getCampaignEmailSettingIdByEmailSettingId  senderEmailSettingIdFound Not Found while sending email tid_${team_id.id} email_message_id:${email_message_id}")
          Success(None)
        }
        
      }

      _: Int <- (campaingEmailSettingIdOpt,udpate_email_scheduled_subject_bodyOpt) match {
        case (Some(campaignEmailSettingId),Some(udpate_email_scheduled_subject_body)) =>{
          campaignProspectDAO.updateCampaignEmailSettingId(
            campaignId = udpate_email_scheduled_subject_body.campaign_id,
            prospectId = udpate_email_scheduled_subject_body.prospect_id,
            teamId = team_id,
            currentCampaignEmailSettingsId = campaignEmailSettingId
          )
          
        }
        case _ => {
          logger.shouldNeverHappen(s"Not Calling updateCampaignEmailSettingId  senderEmailSettingIdFound Not Found while sending email tid_${team_id.id} email_message_id:${email_message_id}")

          Success(0)
        }
      }



      fetch_email_setting_sender_details: Option[EmailSetting] <- Try {

        udpate_email_scheduled_subject_bodyOpt match {
          case Some(udpate_email_scheduled_subject_body) =>  emailSettingDAO.find(
            id = udpate_email_scheduled_subject_body.inbox_email_setting_id.emailSettingId
          )
          case None => {
            logger.shouldNeverHappen(s"Not Calling emailSettingDAO.find  senderEmailSettingIdFound Not Found while sending email tid_${team_id.id} email_message_id:${email_message_id}")
            None
          }
        }
      }

    } yield {
      fetch_email_setting_sender_details
    }

  }


  def sendEmailFromExtension(
    data: SendNewManualEmailV2,
    permittedAccountIdsForEmailSetting: Seq[Long],
    team_id: TeamId
  )(using logger: SRLogger,
    wsclient: WSClient,
    executionContext: ExecutionContext,
    system: ActorSystem
  ): Future[EmailScheduled] = {


    updateEmailScheduledAndTaskForManualEmail(
      email_message_id = data.email_message_id.get,
      permittedAccountIdsForEmailSetting = permittedAccountIdsForEmailSetting,
      team_id = team_id,
      data = data
    ) match {

      case Failure(err) =>

        logger.criticalSmsAlert(s"sendEmailFromExtension :: updateEmailScheduledAndTaskForManualEmail :: Error ::  ${err.getMessage}", Some(err))

        Future.failed(new Exception(err))

      case Success(emailServiceData) =>

        if (emailServiceData.isEmpty) {

          Future.failed(new Exception("sendEmailFromExtension :: emailServiceData isEmpty"))

        } else if (emailServiceData.get.rep_mail_server_reverse_dns.isEmpty) {

          Future.failed(new Exception("sendEmailFromExtension :: rep_mail_server_reverse_dns isEmpty"))

        } else {

          emailSenderService.sendScheduleEmail(
            msg = MQEmailMessage(
              emailScheduledId = data.email_message_id.get,
              team_id = team_id,
              emailServiceProviderSendEmail = getEmailServiceProviderSendEmail(
                emailServiceProvider = emailServiceData.get.service_provider
              ),
              logRequestId = Some(logger.logRequestId)
            ),
            rep_smtp_reverse_dns_host = emailServiceData.get.rep_mail_server_reverse_dns.get,
            sendEmailType = SendScheduleEmailType.ManualExtension,
          )(executionContext, wsclient, logger, system)

        }

    }

  }

}
