package api.prospects.service

import api.accounts.{Account, TeamId}
import api.accounts.models.OrgId
import api.accounts.service.AccountOrgBillingRelatedService
import api.blacklist.BlacklistCreateForm
import api.campaigns.dao.{CampaignEmailSettingsDAO, CampaignProspectDAO_2}
import api.campaigns.services.MergeTagService
import api.prospects.{CreateProspectEventDB, OwnedProspectsForAssigning, ProspectBasicDetails, ProspectBasicForBatchUpdate, ProspectBlacklistMatches, ProspectBounceResponseInfo, ProspectCheckForIsSentEmail, ProspectForValidation, ProspectIdEmail, ProspectName, ProspectUpdateFormData, ProspectsWithInvalidEmail, SecondaryProspectEmails}
import api.columns.{ColumnDef, ProspectColumnDef}
import api.emails.EmailSettingDAO
import utils.{Helpers, SRLogger}
import api.prospects.dao.{ProspectAddEventDAO, ProspectEmailUpdateResult, ProspectsEmailsDAO}
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.{ProspectCategory, ProspectCategoryId, ProspectEmailId, ProspectId, ProspectTouchedType}
import api.sr_audit_logs.models.EventType
import api.team_inbox.dao.ReplySentimentDAO
import api.team_inbox.service.ReplySentimentForTeam
import eventframework.ProspectObject
import org.joda.time.DateTime
import scalikejdbc.DBSession
import sr_scheduler.models.ChannelType
import utils.emailvalidation.models.EmailValidationInitiator
import utils.emailvalidation.models.EmailValidationInitiator.EmailValidationInitiatorType
import utils.emailvalidation.{EmailValidationModel, EmailValidationResult, ProspectEmailValidationResult, ProspectEmailValidationResultWithTeamId, ValidationResultWithTeamIdAndAnalysisId}
import utils.timezones.TimezoneUtils
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.util.{Failure, Success, Try}

class ProspectServiceV2(
                         prospectDAOService: ProspectDAOService,
                         prospectsEmailsDAO: ProspectsEmailsDAO,
                         prospectAddEventDAO: ProspectAddEventDAO,
//                         prospectColumnDef: ProspectColumnDef,
                         campaignProspectDAO_2: CampaignProspectDAO_2,
                         accountOrgBillingRelatedService: AccountOrgBillingRelatedService,
//                         mergeTagService: MergeTagService,
                         emailValidationModel: EmailValidationModel,
                         srRollingUpdateCoreService: SrRollingUpdateCoreService
                       ){
  def markProspectAsValidToForceSendEmails(
                                            teamId: Long,
                                            prospectIds: Seq[Long],
                                            Logger: SRLogger
                                          ): Try[Seq[Long]] = Try {

    prospectsEmailsDAO.markProspectAsValidToForceSendEmailsV2(
      teamId = teamId,
      prospectIds = prospectIds
    ) match {
      case Failure(exception) =>
        Logger.error("prospectsEmailsDAO.markProspectAsValidToForceSendEmailsV2 failed: ", exception)
        throw exception

      case Success(ids) =>
        campaignProspectDAO_2._markProspectAsValidToForceSendEmails(prospectIds = ids)
        ids
    }
  }

  def getProspectCategoryId(
                             teamId: TeamId,
                             text_id: ProspectCategory.Value,
                             account: Option[Account]
                           )(using Logger: SRLogger): Try[ProspectCategoryId] = {
    prospectDAOService.getProspectCategoryId(
      teamId = teamId,
      text_id = text_id,
      account = account
    )
  }

  def markAsInvalid(id: Long, team_id: Long)(using Logger: SRLogger): Try[Int] = Try {

    prospectsEmailsDAO.markAsInvalidV2(id = id, team_id = team_id) match {
      case Failure(exception) =>
        Logger.error("prospectsEmailsDAO.markAsInvalidV2 failed: ", exception)
        throw exception

      case Success(isMarked) =>
        campaignProspectDAO_2._emailIsInvalid(
          prospect_ids = List(id),
          logger = Logger
        ).get

        isMarked
    }
  }


  def markAsSentForValidatingEmail(prospectIdsAndEmails: Seq[(Long, String)], teamId: TeamId, Logger:SRLogger): Try[Int] =  Try {

    prospectsEmailsDAO.markAsSentForValidatingEmailV2(prospectIdsAndEmails = prospectIdsAndEmails, teamId = teamId) match {

      case Failure(exception) =>
        Logger.error("prospectsEmailsDAO.markAsSentForValidatingEmailV2 failed: ", exception)
        throw exception

      case Success(n) =>
        n
    }
  }

  //FUNCTION NOT IN USE
  /*
  def updateProspectEmailHadBouncedEarlier(
                                            email: String,
                                            team_id: Long
                                          )(
                                            implicit Logger: SRLogger
                                          ): Try[(Int, Seq[(Long, Boolean, Long, Long)])] = Try {


    prospectDAO.updateProspectEmailHadBouncedEarlier(
      email = email,
      team_id = team_id) match {
      case Failure(err) =>
        throw err

      case Success(res) =>

        prospectsEmailsDAO.updateProspectEmailHadBouncedEarlierV2(
          email = email,
          team_id = team_id
        )

        res

    }
  }
   */


  def _updateEmailValidationDataAfterHardBouncingV3(
                                                 hardBouncedReplies: Seq[ProspectBounceResponseInfo],
                                                 teamId: TeamId,
                                                 logger: SRLogger
                                               ): Try[Int] = Try {

    val tryOfProspectEmailUpdateResults = for {

      prospectEmailUpdateResultsForHardBounce: List[ProspectEmailUpdateResult] <- prospectsEmailsDAO._updateEmailValidationDataAfterHardBouncingV3_PEV2(
        bouncedReplies = hardBouncedReplies,
        // FIXME VALUECLASS
        teamId = teamId.id
      )

      _: Int <- campaignProspectDAO_2._emailIsInvalid(
        // FIXME VALUECLASS
        prospect_ids = prospectEmailUpdateResultsForHardBounce.map(_.prospect_id.id),
        logger = logger
      )
      // map of prospect_id -> prospect_email_id
      prospectIdToProspectEmailId: Map[ProspectId, ProspectEmailId] =
        prospectEmailUpdateResultsForHardBounce
          .map(p => (p.prospect_id, p.prospect_email_id))
          .toMap

      // update email_validations table entries with hard bounced replies
      numOfEmailValidationsUpdated: Int <- emailValidationModel.updatePreviouslyValidatedEmailsWithBounceResponse(
        hardBouncedReplies = hardBouncedReplies,
        prospectIdToProspectEmailId = prospectIdToProspectEmailId
      )
    } yield {

      logger.info(
        s"ProspectServiceV2._updateEmailValidationDataAfterBouncingV3 - number of email validation entries updated: $numOfEmailValidationsUpdated"
      )

      prospectEmailUpdateResultsForHardBounce
    }

    tryOfProspectEmailUpdateResults match {
      case Failure(exception) =>
        throw exception

      case Success(prospectEmailUpdateResults: List[ProspectEmailUpdateResult]) =>
        prospectEmailUpdateResults.length
    }
  }


  def updateCampaignProspectDataAfterSoftBounced(
                                                 softBouncedReplies: Seq[ProspectBounceResponseInfo],
                                                 logger: SRLogger
                                               ): Try[Int] = {

    val tryOfCampaignProspectSoftBounceUpdate = for {

      result: Seq[Int] <- {
        val seqOfTry: Seq[Try[Int]] = softBouncedReplies
          .filter(_.campaignId.isDefined)
          .groupBy(_.campaignId.get).map { softBouncedRepliesForACampaign =>
          campaignProspectDAO_2._emailSoftBounced(
            prospect_ids = softBouncedRepliesForACampaign._2.map(_.prospectId),
            campaign_id = softBouncedRepliesForACampaign._1,
            logger = logger
          )
        }.toSeq

        Helpers.seqTryToTrySeq(seqOfTry)
      }
    } yield {
      if(result.isEmpty){
        0
      } else {
        result.sum
      }

    }

    tryOfCampaignProspectSoftBounceUpdate
  }


  def assignToTeamMember(
                          assignToAccountId: Long,
                          assignToAccountName: String,
                          assignorAccountId: Long,
                          assignorAccountName: String,
                          permittedAccountIds: Seq[Long],
                          teamId: Long,
                          prospectIds: List[Long]
                          // force_assign: Boolean
                        ): Try[Seq[Long]] = Try {

    if (prospectIds.isEmpty) {

      Seq()

    } else {
      /*
       // prospects must belong to team and assignee must be either admin or
       // prospect owner
       val ownedProspects = if (assignorIsAdmin) {
         sql"""
             SELECT p.id FROM prospects p
               INNER JOIN teams_accounts ta ON (ta.team_id = p.team_id)
             WHERE p.id IN ($prospectIds)
             AND p.team_id = $teamId
             AND ta.role = 'admin'
             AND ta.account_id = $assignorAccountId
           """
           .map(rs => rs.long("id"))
           .list
           .apply()
       } else {
         sql"""
             SELECT p.id FROM prospects p
               INNER JOIN teams_accounts ta
                ON (ta.team_id = p.team_id AND ta.account_id = p.account_id)
             WHERE p.id IN ($prospectIds)
             AND p.team_id = $teamId
             AND p.account_id = $assignorAccountId
             AND ta.role = 'member' ;
           """
           .map(rs => rs.long("id"))
           .list
           .apply()
       }
       */

      prospectDAOService.getPermittedProspects(
        prospectIds = prospectIds,
        teamId = teamId,
        permittedAccountIds = permittedAccountIds
      ) match {
        case Failure(err) =>
          throw err

        case Success(permittedProspects) =>

          if (prospectIds.length != permittedProspects.length) {

            throw new Exception("You do not have the permission to edit all these prospects.")

          } else {

            val taId = prospectDAOService.getTaId(assignToAccountId = assignToAccountId, teamId = teamId)
            if (taId.isEmpty) {

              throw new Exception("New account does not belong to the team")

            } else {
              val prospectIdsToBeAssignedToNewTeamMember = permittedProspects
              /*
              NOTE: commenting out
              for now during FORCEASSIGNISSUE removal
              , will look into it later
              // prospects may already be actively assigned to other campaigns
              val alreadyActiveProspectIds =
                sql"""
  SELECT prospect_id FROM campaigns_prospects WHERE prospect_id IN ($permittedProspects) AND active = TRUE;
"""
                  .map(rs => rs.long("prospect_id"))
                  .list
                  .apply()


              val prospectIdsToBeAssignedToNewTeamMember: List[Long] = if (alreadyActiveProspectIds.isEmpty) {

                permittedProspects

              } else {

                //  throw new Exception("Cannot assign prospects which are assigned to an existing campaign")

                if (force_assign) {

                  // pause any existing campaigns of the prospect
                  // deactivate current campaigns for prospects that are currently assigned to some prospect
                  val unassignedProspects: List[(Long, Long, String)] =
                  sql"""
        UPDATE campaigns_prospects cp SET active = FALSE
          FROM campaigns c
          WHERE c.id = cp.campaign_id
          AND cp.prospect_id IN ($alreadyActiveProspectIds) AND cp.active
        RETURNING cp.campaign_id, cp.prospect_id, c.name
      """
                    .map(rs => (rs.long("prospect_id"), rs.long("campaign_id"), rs.string("name")))
                    .list
                    .apply()


                  // add unassigned events
                  val events = unassignedProspects.map { case (prospectId, cid, cname) => CreateProspectEventDB(

                    event_type = EventType.UNASSIGNED_CAMPAIGN,
                    doer_account_id = Some(assignorAccountId),
                    doer_account_name = Some(assignorAccountName),

                    assigned_to_account_id = None,
                    assigned_to_account_name = None,

                    old_category = None,
                    new_category = None,

                    prospect_id = prospectId,
                    email_thread_id = None,

                    campaign_id = Some(cid),
                    campaign_name = Some(cname),

                    step_id = None,
                    step_name = None,

                    clicked_url = None,

                    account_id = assignorAccountId,
                    team_id = teamId,
                    email_scheduled_id = None,

                    created_at = DateTime.now()

                  )
                  }

                  ProspectEvent.addEvents(events = events)


                  // since force_assign, all prospects are returned
                  permittedProspects

                } else {

                  // only prospects who are not currently assigned to any active campaign
                  permittedProspects.filterNot(pId => alreadyActiveProspectIds.contains(pId))

                }
              }
              */

              if (prospectIdsToBeAssignedToNewTeamMember.isEmpty) {
                Seq()
              } else {

                /*
                  * we can do this as
                  * 1. double migration and moving the code to the new table after the reads are modified
                  * 2. on the update - also do the insert into the prospects_emails table using the returning fields
                  *    to fetch the data for prospects_emails  doing the insert (into prospects_emails) or update on conflict
                  *    WHERE id = $id;
                  */

                prospectDAOService.assignToTeamMember(
                  prospectIdsToBeAssignedToNewTeamMember = prospectIdsToBeAssignedToNewTeamMember,
                  assignToAccountId = assignToAccountId,
                  taId = taId
                ) match {
                  case Failure(err) =>
                    throw err
                  case Success(updatedProspectIds) =>

                    // add assigne to team member events
                    val events = updatedProspectIds.map { prospectId =>
                      CreateProspectEventDB(

                        event_type = EventType.TEAM_MEMBER_ASSIGNED,
                        doer_account_id = Some(assignorAccountId),
                        doer_account_name = Some(assignorAccountName),

                        assigned_to_account_id = Some(assignToAccountId),
                        assigned_to_account_name = Some(assignToAccountName),

                        old_category = None,
                        new_category = None,

                        prospect_id = prospectId,
                        email_thread_id = None,

                        campaign_id = None,
                        campaign_name = None,

                        step_id = None,
                        step_name = None,

                        clicked_url = None,

                        account_id = assignorAccountId,
                        team_id = teamId,
                        email_scheduled_id = None,

                        created_at = DateTime.now(),

                        task_type = None,
                        channel_type = None,
                        task_uuid = None,
                        call_conference_uuid = None,

                        duplicates_merged_at = None,
                        total_merged_prospects = None,
                        potential_duplicate_prospect_id = None

                      )
                    }

                    prospectsEmailsDAO.assignToTeamMemberV2(
                      prospectIdsToBeAssignedToNewTeamMember = prospectIdsToBeAssignedToNewTeamMember,
                      assignToAccountId = assignToAccountId,
                      team_id = teamId
                    ).get
                    prospectAddEventDAO.addEvents(events = events).get
                    /*
                // pause any existing campaigns of the prospect
                sql"""
                    UPDATE campaigns_prospects SET active = FALSE WHERE active AND prospect_id IN ($updatedProspectIds)
                  """
                  .update
                  .apply()

                updatedProspectIds.size

                  */
                    // }

                    updatedProspectIds
                }

              }

            }

          }
      }
    }
  }


  def forceChangeOwnershipFromUpload(
                                      accountId: Long,
                                      accountName: String,
                                      teamId: Long,
                                      taId: Long,
                                      prospectIds: List[Long],
                                      Logger: SRLogger
                                    ): Try[Int] = Try {



    if (prospectIds.isEmpty) {

      0

    } else {
      // NOTE: ignoring publishEvents because it is calling inside uploadCSVViaCronV2
      //if this method used somewhere else should call  publishEvents

      prospectDAOService.forceChangeOwnershipFromUpload(
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        prospectIds = prospectIds
      ) match {
        case Failure(err) => throw err

        case Success(updatedProspectIds) =>
          if(updatedProspectIds.nonEmpty) {

            prospectsEmailsDAO.forceChangeOwnershipFromUploadV2(
              accountId = accountId,
              teamId = teamId,
              prospectIds = prospectIds)
          }


          // add assigne to team member events
          val events = updatedProspectIds.map { prospectId =>
            CreateProspectEventDB(

              event_type = EventType.TEAM_MEMBER_ASSIGNED,
              doer_account_id = Some(accountId),
              doer_account_name = Some(accountName),

              assigned_to_account_id = Some(accountId),
              assigned_to_account_name = Some(accountName),

              old_category = None,
              new_category = None,

              prospect_id = prospectId,
              email_thread_id = None,

              campaign_id = None,
              campaign_name = None,

              step_id = None,
              step_name = None,

              clicked_url = None,

              account_id = accountId,
              team_id = teamId,
              email_scheduled_id = None,

              created_at = DateTime.now(),

              task_type = None,
              channel_type = None,
              task_uuid = None,
              call_conference_uuid = None,

              duplicates_merged_at = None,
              total_merged_prospects = None,
              potential_duplicate_prospect_id = None

            )
          }

          prospectAddEventDAO.addEvents(events = events).get
          updatedProspectIds.size
      }
    }
  }

  //FUNCTION NOT IN USE
  /*
  def updateEmailValidationData(
                                 data: MBLApiResponse,
                                 team_id: Long,
                                 isInvalidEmail: Boolean,
                                 emailValidationId: Long,
                                 logger: SRLogger
                               ): Try[(Int, Seq[(Long, Boolean, Long, Long)])] = {

    prospectDAO.updateEmailValidationData(      data = data,
      team_id = team_id,
      isInvalidEmail = isInvalidEmail,
      emailValidationId = emailValidationId,
      logger = logger
    ) match{
      case Failure(exception) =>
        Failure(exception)
      case Success(prospects) =>



        val prospectsWithInvalidEmail = prospects.filter { case (prospectId, invalidEmail, accountId, teamId) => invalidEmail }

        val idsWithInvalidEmail = prospectsWithInvalidEmail.map(_._1)

        if (idsWithInvalidEmail.nonEmpty) {

          CampaignProspectDAO_2._emailIsInvalid(
            prospect_ids = idsWithInvalidEmail,
            logger = logger
          ).get
        }

        prospectsEmailsDAO.updateEmailValidationData_PEV2(
          email = data.email,
          isInvalidEmail = isInvalidEmail,
          format_valid = data.format_valid,
          emailValidationId = emailValidationId,

          prospects = prospects.toList
        )

        Success((prospects.length, prospectsWithInvalidEmail)
        )
    }

  }
   */


  def updateEmailValidationDataV2(
                                   data: Seq[ValidationResultWithTeamIdAndAnalysisId],
                                   Logger: SRLogger,
                                   emailValidationInitiatorType: EmailValidationInitiatorType
                                 ): Try[(Int, Seq[ProspectsWithInvalidEmail])] = Try{

    prospectsEmailsDAO.updateEmailValidationDataV2_PEV2(
      data = data,
      Logger = Logger
    ) match {
      case Failure(exception) =>
        throw exception
      case Success(prospects) =>
        if(emailValidationInitiatorType == EmailValidationInitiator.InitiatedByCampaign) {
          Try {
            prospects.map(p => {
              accountOrgBillingRelatedService.checkAndUpdateProspectsContacted(
                  prospectId = ProspectId(p.prospectId),
                  teamId = TeamId(p.teamId),
                  updateLastContactedAt = false,
                  channelType = ChannelType.EmailChannel,
                  prospectTouchedType = ProspectTouchedType.EmailValidationCheck(
                    invalidEmail = p.invalidEmail
                  ),
                  logger = Logger,
                )
                .get
            })
          } match {
            case Failure(e) => Logger.fatal("Error while updating prospects contacted :: updateEmailValidationDataV2", e)
            case Success(_) =>
          }
        }

        val prospectsWithInvalidEmail = prospects.filter { case p => p.invalidEmail }
        val prospectsWithValidEmail = prospects.filterNot { case p => p.invalidEmail }

        val idsWithInvalidEmail = prospectsWithInvalidEmail.map(_.prospectId)


        if (idsWithInvalidEmail.nonEmpty) {

          campaignProspectDAO_2._emailIsInvalid(
            prospect_ids = idsWithInvalidEmail,
            logger = Logger.appendLogRequestId(s"updateEmailValidationDataV2 inputCount: ${data.length}")
          ).get
        }

        if(prospectsWithValidEmail.nonEmpty) {
          prospectsWithValidEmail
            .groupBy(_.teamId)
            .map(validProspectForATeam => {
              val (teamId, prospectsForTeam) = validProspectForATeam
              campaignProspectDAO_2.updateNextCheckForSchedulerForCampaignProspects(
                prospect_ids = prospectsForTeam.map(a => ProspectId(a.prospectId)),
                teamId = TeamId(teamId)
              )  match {
                case Success(value) => //DO NOTHING
                case Failure(exception) => Logger.shouldNeverHappen("failed updateNextCheckForSchedulerForCampaignProspects for campaign_email_settings", Some(exception))
              }
            })

        }



        (prospects.length, prospectsWithInvalidEmail)

    }
  }


  def findFromMasterForValidating(
                                   prospectIds: Seq[Long],
                                   teamId: TeamId,
                                   logger: SRLogger
                                 ): Try[Seq[ProspectForValidation]] = Try {

    if (prospectIds.isEmpty) {

      Seq()

    } else {

      val result = prospectsEmailsDAO.findFromMasterForValidatingV2(
        prospectIds = prospectIds,
        teamId = teamId,
        logger = logger
      )

      result match{
        case Failure(exception) =>
          logger.error("prospectsEmailsDAO.findFromMasterForValidatingV2 failed: ", exception)
          throw exception

        case Success(prospectsForValidation) =>
          prospectsForValidation
      }
    }
  }

  def findByEmail(
                   emails: Seq[String],
                   teamId: Long,
                   logger: SRLogger
                 ): Try[Seq[ProspectIdEmail]] = Try {

    prospectsEmailsDAO.findByEmailV2(
      emails = emails,
      teamId = teamId,
      logger = logger
    ) match {
      case Failure(exception) =>
        logger.error("prospectsEmailsDAO.findByEmailV2 failed: ", exception)
        throw exception

      case Success(prospects) => prospects
    }
  }
  /*
   * Use case : 10 prospects uploaded via csv are 1st sent for validation
   * Use case : Prospects and then uploaded into the prospects table via createOrUpdateProspects
   */
  def findByEmailOwnedByOthers(
                                emails: Seq[String],
                                teamId: Long,
                                currentAccountId: Long
                              )(using Logger: SRLogger): Try[Seq[ProspectIdEmail]] = Try {

    prospectsEmailsDAO.findByEmailOwnedByOthersV2(
      emails = emails,
      teamId = teamId,
      currentAccountId = currentAccountId
    ) match {
      case Failure(exception) =>
        Logger.error("prospectsEmailsDAO.findByEmailOwnedByOthersV2 failed: ", exception)
        throw exception

      case Success(prospects) => prospects
    }


  }

  /*
  * PROSPECTS_EMAILS_TODO_READ_CLEANED
  *
   * impact here as email, email_domain has moved to prospects_emails table
   *
   * USE CASE : While create/update blacklist (BlacklistService.createOrUpdateBlacklist) need to check if the prospect_id already in blacklist
   */

  def findProspectIdsMatchingBlacklistedEmailsAndDomains(
                                                          teamId: Long,
//                                                          emailNotCompulsoryEnabled: Boolean,
                                                          data: BlacklistCreateForm
                                                        )(using Logger: SRLogger): Try[Seq[ProspectBlacklistMatches]] = Try {

    prospectsEmailsDAO.findProspectIdsMatchingBlacklistedEmailsAndDomainsV2(
      teamId = teamId,
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled,
      data = data
    ) match {

      case Failure(exception) =>
        Logger.error("prospectsEmailsDAO.findProspectIdsMatchingBlacklistedEmailsAndDomainsV2 failed: ", exception)
        throw exception

      case Success(matches) => matches
    }


  }


  def findByEmailAndAccountId(
                               emails: Seq[String],
                               accountIds: Seq[Long],
                               teamId: Long,
                               Logger: SRLogger
                             ): Try[Seq[ProspectIdEmail]] = Try {

    prospectsEmailsDAO.findByEmailAndAccountIdV2(
      emails = emails,
      accountIds = accountIds,
      teamId = teamId
    ) match {
      case Failure(exception) =>
        Logger.error("prospectsEmailsDAO.findByEmailAndAccountIdV2 failed: ", exception)
        throw exception

      case Success(prospects) => prospects
    }
  }

  def findByIdOrEmail(
                       orProspectIds: Seq[Long],
                       orProspectEmails: Seq[String],
                       teamId: Long,
                       logger: SRLogger
                     ): Try[Seq[ProspectCheckForIsSentEmail]] = Try {

    if (orProspectIds.isEmpty && orProspectEmails.isEmpty) Seq()
    else {
      prospectsEmailsDAO.findByIdOrEmailV2(
        orProspectIds = orProspectIds,
        orProspectEmails = orProspectEmails,
        teamId = teamId,
        logger = logger
      ) match {
        case Failure(exception) =>
          logger.error("", exception)
          throw exception

        case Success(prospects) => prospects
      }
    }

  }

  def filterOwnedProspects(

                              prospectIds: Seq[Long],
                              permittedAccountIds: Seq[Long],
                              teamId: Long,
                              orgId: OrgId,
                              SRLogger: SRLogger

                            ): Try[List[OwnedProspectsForAssigning]] = Try {

//    val emailNotCompulsoryEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//      teamId = TeamId(teamId),
//      feature = SrRollingUpdateFeature.EmailNotCompulsory
//    )(SRLogger)

    prospectsEmailsDAO.filterOwnedProspectsV2(
      prospectIds = prospectIds,
      permittedAccountIds = permittedAccountIds,
      teamId = teamId,
      orgId = orgId,
      SRLogger = SRLogger,
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    ) match {
      case Failure(exception) =>
        SRLogger.error("prospectsEmailsDAO.filterOwnedProspectsV2 failed", exception)
        throw exception
      case Success(listOfProspects) =>
        listOfProspects
    }

  }


  /*
    commented-out this service fn on 17-Apr-2024 as this api was not used anywhere.

  //FUNCTION NOT IN USE
  def updateForEmail(
                      permittedAccountIds: Seq[Long],
                      actingAccountId: Long,
                      teamId: Long,
                      email: String,
                      updateProspect: ProspectUpdateFormData,
                      Logger: SRLogger
                    ): Try[Option[ProspectObject]] = Try {

    val prospectCustomColumnDefs = prospectColumnDef.findCustomColumns(teamId = teamId).filter(_.is_custom)

    val p = updateProspect

    val timezone = if (p.timezone.nonEmpty && p.timezone.get.nonEmpty && TimezoneUtils.isValidTimezone(tz = p.timezone.get.trim)) p.timezone.map(_.trim) else TimezoneUtils.getTimezoneForCountry(p.country)

    // if listName is there, find or create list and return list's id
    val listIdOpt: Option[Long] = prospectDAOService.findOrCreateList(listName = updateProspect.list, permittedAccountIds = permittedAccountIds, listOwnerAccountId = actingAccountId, teamId = teamId)

    //      println(s"update prospect\n\n ${p.timezone.nonEmpty} ${p.timezone.get.nonEmpty} ${p.timezone} \n\n ${p.country} \n\n ${TimezoneUtils.getTimezoneForCountry(p.country)} \n\n ${timezone}\n")

    val customFields = ColumnDef.parseCustomFields(
      customFieldsInput = p.custom_fields,
      customColumnDefs = prospectCustomColumnDefs,
      ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = false // the update function used here doesnt support this
    )
    /*
     * prospects_email to be done after migration.
     *
     * fetch the id via the email and team_id
     * then fire the update on prospects for the given fields.
     */

    val savedProspectOptId =

      prospectsEmailsDAO.updateForEmailV2(
        permittedAccountIds = permittedAccountIds,
        teamId = teamId,
        email = email,
        p = updateProspect,
        customFields = customFields,
        timezone= timezone,
        listIdOpt = listIdOpt) match{
        case Failure(exception) =>
          throw exception
        case Success(savedProspectOptId) =>
          savedProspectOptId
      }



    savedProspectOptId flatMap  { savedProspectId =>

      // on updating a prospect, remove any to_check errors that may have been associated with the prospect earlier
      mergeTagService._removeMissingErrorMessage(prospectIds = Seq(savedProspectId))

      // PROSPECTS_EMAILS_TODO_READ_CLEANED
      prospectDAOService.findFromMaster(prospectId = savedProspectId, teamId = teamId, Logger = Logger).get

    }

  }
  */

  def fetchProspectsByProspectCategoryIdCustom(
                                                prospect_category_id_custom: Long,
                                                teamId: Long,
                                                Logger: SRLogger
                                              ): Try[Seq[ProspectBasicForBatchUpdate]] = Try {

    prospectsEmailsDAO.fetchProspectsByProspectCategoryIdCustomV2(
      prospect_category_id_custom = prospect_category_id_custom,
      teamId = teamId
    ) match {
      case Failure(exception) =>
        Logger.error("prospectsEmailsDAO.fetchProspectsByProspectCategoryIdCustomV2 failed: ", exception)
        throw exception

      case Success(prospects) => prospects
    }
  }

  def fetchProspectsByIdsForBatchUpdate(
                                         prospectsIds: Seq[Long],
                                         permittedAccountIds: Seq[Long],
                                         teamId: Long,
                                         logger: SRLogger
                                       ): Try[Seq[ProspectBasicForBatchUpdate]] = Try {

    prospectsEmailsDAO.fetchProspectsByIdsForBatchUpdateV2(
      prospectsIds = prospectsIds,
      permittedAccountIds = permittedAccountIds,
      teamId = teamId,
      logger = logger
    ) match {
      case Failure(exception) =>
        logger.error("prospectsEmailsDAO.fetchProspectsByIdsForBatchUpdateV2 failed: ", exception)
        throw exception

      case Success(prospects) => prospects
    }


  }

  def getProspectFullNameById(
                               prospectIds: Seq[Long],
                               teamId: Long,
                               Logger: SRLogger
                             ): Try[List[ProspectName]] = Try {

    val result = prospectsEmailsDAO.getProspectFullNameById_PEV2(prospectIds = prospectIds)

    result match {
      case Failure(exception) =>
        Logger.error("prospectsEmailsDAO.getProspectFullNameById_PEV2 failed: ", exception)
        throw exception

      case Success(names) => names
    }

  }

  def getProspectBasicDetailsById(
                                   prospectIds: Seq[Long],
                                   teamId: Long,
                                   Logger:SRLogger
                                 ): Try[List[ProspectBasicDetails.EmailProspectBasicDetails]] = Try {

    val result = prospectsEmailsDAO.getProspectBasicDetailsById_PEV2(prospectIds = prospectIds)

    result match {
      case Failure(exception) =>
        Logger.error("prospectsEmailsDAO.getProspectBasicDetailsById_PEV2 failed: ", exception)
        throw exception

      case Success(details) => details
    }

  }

  def hasAlreadyValidatedButNotSentProspects(
                                              teamId: Long,
                                              Logger: SRLogger
                                            ): Try[Boolean] = Try {

    prospectsEmailsDAO.hasAlreadyValidatedButNotSentProspectsV2(teamId = teamId) match {
      case Failure(exception) =>
        Logger.error(s"prospectsEmailsDAO.hasAlreadyValidatedButNotSentProspectsV2 failed: ${exception.getMessage}")
        throw exception

      case Success(flag) =>
        flag
    }
  }




  def prospectForceSentCount(accountId: Long, teamId: Long): Try[Int] = {
    prospectsEmailsDAO.prospectForceSentCountV2(accountId = accountId, teamId = teamId)
  }

  def findProspectsEmailsIdForEmail(
                                     prospect_id: Long,
                                     prospect_email: String,
                                     team_id: Long
                                   ): Try[Long] = Try {
    prospectsEmailsDAO.getProspectsEmailsIdForProspectEmail(
      prospect_id = prospect_id,
      prospect_email = prospect_email ,
      team_id = team_id
    ) match {
      case None => throw new Throwable(s"prospects_emails.id not found for email ${prospect_email}")

      case Some(peid) => peid
    }
  }

  def getSecondaryProspectEmails(
                                  prospect_id: Long,
                                  team_id: Long
                                ) = {
    prospectsEmailsDAO.getSecondaryProspectEmails(
      prospect_id = prospect_id,
      team_id = team_id
    )
  }
}