package api.prospects.service

import api.AppConfig
import api.accounts.TeamId
import api.campaigns.models.NavigationTime
import api.prospects.{ExactIdToCompareTime, InboxV3ServiceGetConversationsError, InferredQueryTimeline, ProspectsApiSearchParams}
import api.prospects.dao_service.ProspectDAOService
import eventframework.ProspectObject
import utils.SRLogger
import utils.jodatimeutils.JodaTimeUtils
import utils.pagination.{GetDataErrorTrait, GetSameTimeDataProspectsListing, PaginationServiceTrait}

import scala.util.{Failure, Success, Try}

sealed trait ProspectListingGetProspectsError extends GetDataErrorTrait

object ProspectListingGetProspectsError {
  case class DBFailure( exception: Throwable) extends ProspectListingGetProspectsError
}

class ProspectsListingPaginationService(
                                         prospectDAOService: ProspectDAOService
                                       ) extends PaginationServiceTrait[ProspectObject] {
  override type GetSameTimeDataFromDbParamsType = GetSameTimeDataProspectsListing

  override def getSameTimeDataFromDb(
                                      data: GetSameTimeDataProspectsListing,
                                      exactIdToCompareTime: ExactIdToCompareTime
                                    )(using logger: SRLogger): Either[ProspectListingGetProspectsError, List[ProspectObject]] = {
    prospectDAOService.find(
      teamId = data.teamId,
      byCampaignId = data.campaignId,
      queryTimelineForApi = Some(InferredQueryTimeline.Exact(exactIdToCompareTime)),
      findActiveCampaignsIfInternalRequest = data.findActiveCampaignsIfInternalRequest,
      limit = data.limit + 1,
      Logger = logger
    ) match {
      case Failure(exception) => Left(ProspectListingGetProspectsError.DBFailure(exception))
      case Success(exactlyAtProspects) => Right(exactlyAtProspects.toList)
    }
  }


  def findProspectsListingPageForApi(
                                      filters: ProspectsApiSearchParams,
                                      findActiveCampaignsIfInternalRequest: Boolean = false,
                                      teamId: TeamId
                                    )(using logger: SRLogger): Try[ProspectsListingApiResult] = Try {
    val limit = AppConfig.prospectListingApiRowsPerPage

    prospectDAOService.find(
      teamId = teamId.id,
      byCampaignId = filters.campaign_id,
      queryTimelineForApi = Some(filters.range),
      findActiveCampaignsIfInternalRequest = findActiveCampaignsIfInternalRequest,
      limit = limit + 1,
      Logger = logger
    ) match {
      case Failure(exception) => throw exception
      case Success(prospectObjects) =>

        //Sorting in descending order of created_at
        val sortedProspects: Seq[ProspectObject] = prospectObjects.sortBy(_.created_at)(JodaTimeUtils.dateTimeOrdering)
          .reverse

        getFinalPaginationResponse(
          timeline = filters.range,
          sortedData = sortedProspects.toList,
          limit = limit,
          dataToGetSameTimeRecords = GetSameTimeDataProspectsListing(
            teamId = teamId.id,
            campaignId = filters.campaign_id,
            findActiveCampaignsIfInternalRequest = findActiveCampaignsIfInternalRequest,
            limit = limit,
            Logger = logger
          )
        ) match {
          case Left(error) => error match {
            case _: InboxV3ServiceGetConversationsError => throw new Throwable(s"Impossible case. This error should not throw")
            case e: ProspectListingGetProspectsError => e match {
              case ProspectListingGetProspectsError.DBFailure(exception) => throw exception
            }
          }
          case Right(res) =>
            val nextTimeline: Option[Long] = if (res.links.next.isDefined) {
              Some(res.links.next.get.toInstant.getMillis)
            } else {
              None
            }
            ProspectsListingApiResult(
              data = res.data, links = NavigationTime(next = nextTimeline)
            )
        }
    }
  }


}
