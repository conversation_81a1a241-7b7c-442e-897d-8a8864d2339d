package api.prospects.service

import api.accounts.{AccountUuid, TeamId}
import api.campaigns.CPTuple
import api.campaigns.models.NavigationTime
import api.campaigns.services.{CampaignId, CampaignProspectService, CampaignUuid}
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.ProspectId
import api.prospects.{CampaignProspectData, ProspectBatchActionStatusChange, ProspectService, ProspectUuid, ProspectsApiSearchParams}
import api.sr_audit_logs.models.ProspectEventDataType
import api.tags.models.{ProspectTag, TagUuid}
import api.tags.services.ProspectTagService
import eventframework.ProspectObject
import org.apache.commons.math3.analysis.function.Log
import org.joda.time.DateTime
import org.postgresql.util.PGobject
import play.api.libs.json.{Format, JsError, JsLookupResult, JsResult, JsSuccess, JsValue, <PERSON><PERSON>, Reads, Writes}
import play.api.libs.json.JodaReads._
import play.api.libs.json.JodaWrites._
import scalikejdbc.WrappedResultSet
import scalikejdbc.jodatime.JodaWrappedResultSet.fromWrappedResultSetToJodaWrappedResultSet
import utils.SRLogger
import utils.enum_sr_utils.EnumUtils

import scala.util.{Failure, Success, Try}


case class ProspectsListingApiResult(
                                      data: Seq[ProspectObject],
                                      links: NavigationTime
                                    )


case class ProspectEmailData(
                              prospect_id: Long,
                              email_data: ProspectEmail
                            )

case class UpdateProspectStatusValidationResult(
                                                 campaign_ids: Seq[Long],
                                                 prospect_status: String,
                                                 will_resume_at: Option[DateTime],
                                                 will_resume_at_tz: Option[String],
                                               )

case class ProspectEmail(
                          email: String,
                          is_valid: Boolean,
                          is_primary: Boolean
                        )

case object ProspectEmail {
  implicit val writes: Writes[ProspectEmail] = new Writes[ProspectEmail] {
    def writes(data: ProspectEmail) = {
      Json.obj(
        "email" -> data.email,
        "is_valid" -> data.is_valid,
        "is_primary" -> data.is_primary
      )
    }
  }

  implicit val reads: Reads[ProspectEmail] = new Reads[ProspectEmail] {
    override def reads(json: JsValue): JsResult[ProspectEmail] = Try {
      ProspectEmail(
        email = (json \ "email").as[String],
        is_valid = (json \ "is_valid").as[Boolean],
        is_primary = (json \ "is_primary").as[Boolean]
      )
    } match {
      case Failure(e) => JsError(e.getMessage)
      case Success(prospectEmailForApi) => JsSuccess(prospectEmailForApi)
    }
  }
}

case class ProspectTagForApi(
                              uuid: String,
                              tag: String
                            )

case object ProspectTagForApi {
  implicit val writes: Writes[ProspectTagForApi] = new Writes[ProspectTagForApi] {
    def writes(data: ProspectTagForApi) = {
      Json.obj(
        "id" -> data.uuid,
        "tag" -> data.tag
      )
    }
  }

  implicit val reads: Reads[ProspectTagForApi] = new Reads[ProspectTagForApi] {
    override def reads(json: JsValue): JsResult[ProspectTagForApi] = Try {
      ProspectTagForApi(
        uuid = (json \ "id").as[String],
        tag = (json \ "tag").as[String]
      )
    } match {
      case Failure(e) => JsError(e.getMessage)
      case Success(prospectTagForApi) => JsSuccess(prospectTagForApi)
    }
  }
}

case class ProspectObjectForApi(
                                 id: Option[String],
                                 emails: List[ProspectEmail],
                                 account_id: String,
                                 first_name: Option[String],
                                 last_name: Option[String],
                                 phone_numbers: List[String],
                                 linkedin_url: Option[String],
                                 city: Option[String],
                                 company: Option[String],
                                 state: Option[String],
                                 country: Option[String],
                                 timezone: Option[String],
                                 list: Option[String],
                                 tags: Option[List[ProspectTagForApi]],
                                 custom_fields: JsValue,
                                 updated_at: DateTime,
                                 created_at: DateTime
                               ) extends ProspectEventDataType

case object ProspectObjectForApi {

  implicit val writes: Writes[ProspectObjectForApi] = new Writes[ProspectObjectForApi] {
    def writes(data: ProspectObjectForApi) = {
      Json.obj(
        "object" -> "prospect",
        "id" -> data.id,
        "emails" -> data.emails,
        "account_id" -> data.account_id,
        "first_name" -> data.first_name,
        "last_name" -> data.last_name,
        "phone_numbers" -> data.phone_numbers,
        "linkedin_url" -> data.linkedin_url,
        "city" -> data.city,
        "company" -> data.company,
        "state" -> data.state,
        "country" -> data.country,
        "timezone" -> data.timezone,
        "list" -> data.list,
        "tags" -> data.tags,
        "custom_fields" -> data.custom_fields,
        "updated_at" -> data.updated_at,
        "created_at" -> data.created_at
      )
    }
  }

  implicit val reads: Reads[ProspectObjectForApi] = new Reads[ProspectObjectForApi] {
    override def reads(json: JsValue): JsResult[ProspectObjectForApi] = Try {
      ProspectObjectForApi(
        id = (json \ "id").asOpt[String],
        emails = (json \ "emails").as[List[ProspectEmail]],
        account_id = (json \ "account_id").as[String],
        first_name = (json \ "first_name").asOpt[String],
        last_name = (json \ "last_name").asOpt[String],
        phone_numbers = (json \ "phone_numbers").as[List[String]],
        linkedin_url = (json \ "linkedin_url").asOpt[String],
        city = (json \ "city").asOpt[String],
        company = (json \ "company").asOpt[String],
        state = (json \ "state").asOpt[String],
        country = (json \ "country").asOpt[String],
        timezone = (json \ "timezone").asOpt[String],
        list = (json \ "list").asOpt[String],
        tags = (json \ "tags").asOpt[List[ProspectTagForApi]],
        custom_fields = (json \ "custom_fields").as[JsValue],
        updated_at = (json \ "updated_at").as[DateTime],
        created_at = (json \ "created_at").as[DateTime]
      )
    } match {
      case Failure(e) => JsError(e.getMessage)
      case Success(prospectObjectForApi) => JsSuccess(prospectObjectForApi)
    }
  }
}

case class CampaignProspectDataForApi(

                                       //NOTE: Suppressing fields - 22-02-2024
                                       //                                 to_check: Boolean,
                                       //                                 to_check_fields: Option[List[String]],
                                       total_opens_in_campaign: Long,
                                       total_clicks_in_campaign: Long,
                                       sent: Boolean,
                                       opened: Boolean,
                                       replied: Boolean,
                                       opted_out: Boolean,
                                       bounced: Boolean,
                                       completed: Boolean,
                                       will_resume_at: Option[DateTime],
                                       will_resume_at_tz: Option[String],
                                       clicked: Boolean,
                                       invalid_email: Boolean,
                                       auto_reply: Boolean,
                                       out_of_office: Boolean,
                                       campaign_name: String,
                                       campaign_uuid: CampaignUuid,
                                       //                                 sending_holiday_calendar_name: Option[String],
                                       //                                 latest_task_done_at: Option[DateTime]
                                     )


case object CampaignProspectDataForApi {

  implicit val writes: Writes[CampaignProspectDataForApi] = new Writes[CampaignProspectDataForApi] {
    def writes(data: CampaignProspectDataForApi) = {
      Json.obj(
        //        "to_check" -> data.to_check,
        //        "to_check_fields" -> data.to_check_fields,
        "total_opens_in_campaign" -> data.total_opens_in_campaign,
        "total_clicks_in_campaign" -> data.total_clicks_in_campaign,
        "sent" -> data.sent,
        "opened" -> data.opened,
        "replied" -> data.replied,
        "opted_out" -> data.opted_out,
        "bounced" -> data.bounced,
        "completed" -> data.completed,
        "will_resume_at" -> data.will_resume_at,
        "will_resume_at_tz" -> data.will_resume_at_tz,
        "clicked" -> data.clicked,
        "invalid_email" -> data.invalid_email,
        "auto_reply" -> data.auto_reply,
        "out_of_office" -> data.out_of_office,
        "campaign_name" -> data.campaign_name,
        "campaign_id" -> data.campaign_uuid,
        //        "sending_holiday_calendar_name" -> data.sending_holiday_calendar_name,
        //        "latest_task_done_at" -> data.latest_task_done_at
      )
    }
  }

  implicit val reads: Reads[CampaignProspectDataForApi] = new Reads[CampaignProspectDataForApi] {
    override def reads(json: JsValue): JsResult[CampaignProspectDataForApi] = Try {
      CampaignProspectDataForApi(
        //        to_check = (json \ "to_check").as[Boolean],
        //        to_check_fields = (json \ "to_check_fields").asOpt[List[String]],
        total_opens_in_campaign = (json \ "total_opens_in_campaign").as[Long],
        total_clicks_in_campaign = (json \ "total_clicks_in_campaign").as[Long],
        sent = (json \ "sent").as[Boolean],
        opened = (json \ "opened").as[Boolean],
        replied = (json \ "replied").as[Boolean],
        opted_out = (json \ "opted_out").as[Boolean],
        bounced = (json \ "bounced").as[Boolean],
        completed = (json \ "completed").as[Boolean],
        will_resume_at = (json \ "will_resume_at").asOpt[DateTime],
        will_resume_at_tz = (json \ "will_resume_at_tz").asOpt[String],
        clicked = (json \ "clicked").as[Boolean],
        invalid_email = (json \ "invalid_email").as[Boolean],
        auto_reply = (json \ "auto_reply").as[Boolean],
        out_of_office = (json \ "out_of_office").as[Boolean],
        campaign_name = (json \ "campaign_name").as[String],
        campaign_uuid = (json \ "campaign_id").as[CampaignUuid],
        //        sending_holiday_calendar_name = (json \ "sending_holiday_calendar_name").asOpt[String],
        //        latest_task_done_at = (json \ "latest_task_done_at").asOpt[DateTime],
      )
    } match {
      case Failure(e) => JsError(e.getMessage)
      case Success(campaignProspectDataObjectForApi) => JsSuccess(campaignProspectDataObjectForApi)
    }

  }


}


case class CampaignProspectObjectForApi(
                                         prospect: ProspectObjectForApi,
                                         prospect_status_in_campaign: CampaignProspectDataForApi
                                       ) extends ProspectEventDataType

case object CampaignProspectObjectForApi {

  implicit val writes: Writes[CampaignProspectObjectForApi] = new Writes[CampaignProspectObjectForApi] {
    def writes(data: CampaignProspectObjectForApi) = {
      Json.obj(
        "object" -> "campaign_prospect",
        "prospect" -> data.prospect,
        "prospect_status_in_campaign" -> data.prospect_status_in_campaign
      )
    }
  }

  implicit val reads: Reads[CampaignProspectObjectForApi] = new Reads[CampaignProspectObjectForApi] {
    override def reads(json: JsValue): JsResult[CampaignProspectObjectForApi] = Try {
      CampaignProspectObjectForApi(
        prospect = (json \ "prospect").as[ProspectObjectForApi],
        prospect_status_in_campaign = (json \ "prospect_status_in_campaign").as[CampaignProspectDataForApi],
      )
    } match {
      case Failure(e) => JsError(e.getMessage)
      case Success(campaignProspectObjectForApi) => JsSuccess(campaignProspectObjectForApi)
    }

  }
}


class ProspectApiService(
                          prospectService: ProspectService,
                          prospectDAOService: ProspectDAOService
                        ) {
  def constructResponseForCreateOrUpdate(
                                          data: Seq[ProspectObject],
                                          invalid_prospects: Seq[Option[ProspectUuid]]
                                        ): Try[Seq[ProspectObjectForApi]] = Try {
    data.map(prospect_data => {

      val tags: Option[List[ProspectTagForApi]] = if (prospect_data.internal.tags.isDefined) {
        val prospect_tags: List[ProspectTag] = prospect_data.internal.tags.get
        Some(prospect_tags.map(t => {
          ProspectTagForApi(
            uuid = t.tag_uuid.uuid,
            tag = t.tag
          )
        }))
      } else None

      ProspectObjectForApi(
        id = prospect_data.prospect_uuid.map(_.uuid),
        emails = List(ProspectEmail(
          email = prospect_data.email.get, //TODO: EMAIL_OPTIONAL new path will fail (prospects coming fromDb so old path will always have email)
          is_valid = !invalid_prospects.contains(prospect_data.prospect_uuid),
          is_primary = true //for create/update prospects flow it's always true
        )),
        account_id = AccountUuid.unwrap(prospect_data.owner_uuid),
        first_name = prospect_data.first_name,
        last_name = prospect_data.last_name,
        phone_numbers = if (prospect_data.phone.isDefined && prospect_data.phone.get.trim.nonEmpty) List(prospect_data.phone.get) else List(),
        linkedin_url = prospect_data.linkedin_url,
        city = prospect_data.city,
        company = prospect_data.company,
        state = prospect_data.state,
        country = prospect_data.country,
        timezone = prospect_data.timezone,
        list = prospect_data.list,
        tags = tags,
        custom_fields = prospect_data.custom_fields,
        updated_at = prospect_data.updated_at,
        created_at = prospect_data.created_at
      )
    })
  }

  def constructResponseForGetProspects(
                                        email_data: List[ProspectEmailData],
                                        prospect_data: Seq[ProspectObject],
                                      )(using logger: SRLogger): Seq[ProspectObjectForApi] = {

    val prospectEmailsMap: Map[Long, List[ProspectEmailData]] = email_data.groupBy(_.prospect_id)

    prospect_data.map(prospect => {
      val tags: Option[List[ProspectTagForApi]] = if (prospect.internal.tags.isDefined) {
        val prospect_tags: List[ProspectTag] = prospect.internal.tags.get
        Some(prospect_tags.map(t => {
          ProspectTagForApi(
            uuid = t.tag_uuid.uuid,
            tag = t.tag
          )
        }))
      } else None

      val prospectEmailsData: List[ProspectEmail] = prospectEmailsMap(prospect.id).map(_.email_data)

      ProspectObjectForApi(
        id = prospect.prospect_uuid.map(_.uuid),
        emails = prospectEmailsData,
        phone_numbers = if (prospect.phone.isDefined && prospect.phone.get.trim.nonEmpty) {
          List(prospect.phone.get)
        } else {
          List()
        },
        linkedin_url = prospect.linkedin_url,
        account_id = prospect.owner_uuid.toString,
        first_name = prospect.first_name,
        last_name = prospect.last_name,
        city = prospect.city,
        company = prospect.company,
        state = prospect.state,
        country = prospect.country,
        list = prospect.list,
        timezone = prospect.timezone,
        custom_fields = prospect.custom_fields,
        created_at = prospect.created_at,
        updated_at = prospect.updated_at,
        tags = tags
      )
    })

  }

  def constructResponseForGetCampaignProspects(
                                                email_data: List[ProspectEmail],
                                                prospect_data: ProspectObject,
                                                campaign_data: CampaignProspectData
                                              )(using logger: SRLogger): CampaignProspectObjectForApi = {

    val tags: Option[List[ProspectTagForApi]] = if (prospect_data.internal.tags.isDefined) {
      val prospect_tags: List[ProspectTag] = prospect_data.internal.tags.get
      Some(prospect_tags.map(t => {
        ProspectTagForApi(
          uuid = t.tag_uuid.uuid,
          tag = t.tag
        )
      }))
    } else None

    CampaignProspectObjectForApi(
      prospect = ProspectObjectForApi(
        id = prospect_data.prospect_uuid.map(_.uuid),
        emails = email_data,
        phone_numbers = if (prospect_data.phone.isDefined && prospect_data.phone.get.trim.nonEmpty) {
          List(prospect_data.phone.get)
        } else {
          List()
        },
        linkedin_url = prospect_data.linkedin_url,
        account_id = prospect_data.owner_uuid.toString,
        first_name = prospect_data.first_name,
        last_name = prospect_data.last_name,
        city = prospect_data.city,
        company = prospect_data.company,
        state = prospect_data.state,
        country = prospect_data.country,
        list = prospect_data.list,
        timezone = prospect_data.timezone,
        custom_fields = prospect_data.custom_fields,
        created_at = prospect_data.created_at,
        updated_at = prospect_data.updated_at,
        tags = tags
      ),
      prospect_status_in_campaign = CampaignProspectDataForApi(
        //        to_check = campaign_data.to_check,
        //        to_check_fields = campaign_data.to_check_fields,
        total_opens_in_campaign = campaign_data.total_opens_in_campaign,
        total_clicks_in_campaign = campaign_data.total_clicks_in_campaign,
        sent = campaign_data.sent,
        opened = campaign_data.opened,
        replied = campaign_data.replied,
        opted_out = campaign_data.opted_out,
        bounced = campaign_data.bounced,
        completed = campaign_data.completed,
        will_resume_at = campaign_data.will_resume_at,
        will_resume_at_tz = campaign_data.will_resume_at_tz,
        clicked = campaign_data.clicked,
        invalid_email = campaign_data.invalid_email,
        auto_reply = campaign_data.auto_reply,
        out_of_office = campaign_data.out_of_office,
        campaign_name = campaign_data.campaign_name,
        campaign_uuid = CampaignUuid(campaign_data.campaign_uuid.get),
        //        sending_holiday_calendar_name = campaign_data.sending_holiday_calendar_name,
        //        latest_task_done_at = campaign_data.latest_task_done_at
      ))
  }

  def constructResponseForProspectStatusChange(
                                                prospectsToStatusUpdated: Seq[CPTuple],
                                                teamId: TeamId
                                              )(using logger: SRLogger): Try[Seq[CampaignProspectObjectForApi]] = {
    val ApiResponse =
      prospectsToStatusUpdated.map(tuple => {
        prospectService.getEmailDataForProspectIds(
          prospectIds = Seq(ProspectId(tuple.prospect_id)),
          team_id = teamId
        ) match {
          case Failure(exception) =>
            logger.error("Error while fetching emails data for prospects", err = exception)
            Failure(new Exception("Error while fetching emails data for prospects"))

          case Success(email_data) =>
            prospectDAOService.find(
              byProspectIds = Seq(tuple.prospect_id),
              byCampaignId = Option(tuple.campaign_id),
              Logger = logger,
              teamId = teamId.id,
              findActiveCampaignsIfInternalRequest = true
            ) match {
              case Failure(err) => Failure(err)
              case Success(prospects) =>
                prospects.head.internal.active_campaigns match {
                  case Some(active_campaign_seq) =>
                    /*
                    There is a check in the fromDb call of prospects objects which checks if empty then return active_campaigns as None
                    So if this enters the Some block, it is guaranteed to have a value
                    * */
                    val campaigns_data_js: JsLookupResult = active_campaign_seq.head
                    val campaign_data = campaigns_data_js.as[CampaignProspectData]
                    Success(constructResponseForGetCampaignProspects(
                      email_data = email_data.map(email => email.email_data),
                      prospect_data = prospects.head,
                      campaign_data = campaign_data,
                    ))

                  case None => Failure(new Exception("No campaign data found"))
                }
            }
        }
      }
      )
    utils.Helpers
      .seqTryToTrySeq(ApiResponse)
  }
}
