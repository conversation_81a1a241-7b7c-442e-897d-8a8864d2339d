package api.prospects

import api.APIErrorResponse.{ErrorResponseProspectsPostApi, ErrorResponseUpdateProspectStatusApi}
import api.accounts.email.models.SrMxCheckESPType
import api.accounts.models.{AccountId, OrgId}
import api.{AppConfig, BadRequestErrorException, ServerErrorException}
import api.accounts.{Account, AccountDAO, AccountService, PermType, PermissionMethods, ReplyHandling, TeamId, TeamMember}
import api.blacklist.Blacklist
import api.blacklist.dao.BlacklistProspectCheckDAO
import api.blacklist.models.DoNotContactType
import api.campaigns.dao.CampaignProspectDAO_2
import api.campaigns.models.{IgnoreProspectsInOtherCampaigns, NavigationTime, NewProspectStatus}
import api.campaigns.services.{CampaignId, CampaignProspectService, CampaignProspectTimezonesJedisService, CampaignService, GetProspectIdFromUuidError, MergeTagService}
import api.campaigns.{AssignedCampaignForProspect, CPCompleted, CPMarkAsCompleted, CampaignBasicDetails, CampaignProspectDAO}
import api.columns.{ColumnDef, FieldTypeEnum, ProspectColumnDef, ProspectColumnDefUtils}
import api.emails.EmailSettingDAO
import api.prospects.ProspectService.{checkIfAllPhonesAreValidIfDefined, checkIfFormDataMatchingDb, constructProspectAccountData, extractUniqueProspectsForMultiRowInsert, getCreatedProspectIdsFromUpsertSQLResult, getDuplicateProspectEmails}
import api.prospects.dao.{DeDuplicateColumnTypeAndValue, DuplicateProspectResult, NewlyCreatedProspect, ProspectsEmailsDAO}
import api.prospects.dao_service.{DuplicationFindProspectData, DuplicationFindProspectDataV2, ProspectDAOService}
import api.prospects.InferredQueryTimeline.Range
import api.prospects.models.{ProspectCategory, ProspectCategoryId, ProspectCategoryUpdateFlow, ProspectId, SrProspectColumns, SrProspectDeduplicationColumns, UpdateProspectType}
import api.prospects.service.{PotentialDuplicateProspectService, ProspectEmail, ProspectEmailData, ProspectErrorType, ProspectObjectForApi, ProspectServiceV2, ProspectUpdateFormWithId, ProspectsListingApiResult, UpdateProspectStatusErrorType}
import api.sr_audit_logs.models.{CreateEventLogError, EventDataType, EventType, OldProspectDeduplicationColumn}
import api.sr_audit_logs.services.EventLogService
import api.tags.{ProspectsAndTags, TagService}
import api.tags.models.{CampaignTagUuid, ProspectTagUuid, TagAndUuid}
import api.triggers.AddOrRemoveTagAction
import eventframework.ProspectObject
import io.smartreach.sr_dns_utils.{DNSService, DomainPublicDNSAddOrUpdateData}
import org.joda.time.DateTime
import play.api.libs.json.{Format, JsError, JsObject, JsPath, JsResult, JsSuccess, JsValue, Json, JsonValidationError, OWrites, Reads}
import sr_scheduler.CampaignStatus
import utils.cronjobs.AttemptRetryCronService
import utils.dbutils.SQLUtils
import utils.emailvalidation.EmailValidationService
import utils.emailvalidation.models.EmailValidationPriority
import utils.enum_sr_utils.EnumUtils
import utils.helpers.LogHelpers
import utils.input_validations.{LinkedInUrlValidation, PhoneNumberValidation}
import utils.jodatimeutils.JodaTimeUtils
import utils.linkedin.LinkedinHelperFunctions
import utils.mq.email.{AssociateProspectsData, MQAssociateProspectToOldEmails, MQDomainServiceProviderDNSService}
import utils.mq.webhook.model.TriggerSource
import utils.mq.webhook.model.TriggerSource.CRM
import utils.{Helpers, SRLogger}
import utils.{AddonLimitReachedException, Helpers, SRLogger}
import utils.mq.webhook.{MQTrigger, MQTriggerMsg}
import utils.uuid.{SrId, SrIdentifier, SrUuid, SrUuidUtils}
import utils.uuid.services.SrUuidService
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.util.{Failure, Success, Try}

case class CreateOrUpdateProspectsResult(
                                          invalid_emails: Seq[String],
                                          duplicate_prospects_data: Seq[DuplicateProspectResult],
                                          duplicate_ids_with_edit_permission: Seq[Long],
                                          created_ids: Seq[Long],
                                          updated_ids: Seq[Long],
                                          assigned_ids: Seq[Long],
                                          duplicates_ignored_for_no_edit_permission: Seq[String],
                                          ignored_internal_emails: Seq[String],
                                          total_duplicate_emails: Int,
                                          total_dnc_prospects: Int,
                                          inserted_or_updated_prospects: List[UpsertSQLProspectData]
                                        )

sealed trait ValidateEmailAndPhoneForCreateOrUpdateOneErr

object ValidateEmailAndPhoneForCreateOrUpdateOneErr {
  case class InvalidEmailAddress(error: String) extends ValidateEmailAndPhoneForCreateOrUpdateOneErr

  case class InvalidPhoneNumber(error: String) extends ValidateEmailAndPhoneForCreateOrUpdateOneErr

  case class InvalidLinkedinUrl(error: String) extends ValidateEmailAndPhoneForCreateOrUpdateOneErr

  case class InvalidEmailAddressAndPhone(error: String) extends ValidateEmailAndPhoneForCreateOrUpdateOneErr

  case class InvalidPhoneAndLinkedIn(error: String) extends ValidateEmailAndPhoneForCreateOrUpdateOneErr

  case class InvalidEmailAddressAndLinkedIn(error: String) extends ValidateEmailAndPhoneForCreateOrUpdateOneErr

  case class InvalidEmailAndPhoneNumberAndLinkedIn(error: String) extends ValidateEmailAndPhoneForCreateOrUpdateOneErr

}

sealed trait CreateOrUpdateOneError

object CreateOrUpdateOneError {
  case class CampaignNotFound(error: String) extends CreateOrUpdateOneError

  case class ProspectNotFound(error: String) extends CreateOrUpdateOneError

  case class InternalServerError(err: Throwable) extends CreateOrUpdateOneError

  case class ExceptionOccurred(err: Throwable) extends CreateOrUpdateOneError

  case class ErrorWhileCreateOrUpdateOne(error: String) extends CreateOrUpdateOneError

  case class InternalEmailCannotBeAdded(error: String) extends CreateOrUpdateOneError

  case class EmailAndPhoneNumberAndLinkedInValidationError(error: ValidateEmailAndPhoneForCreateOrUpdateOneErr) extends CreateOrUpdateOneError

  case class AtleastOneDeduplicateColumnMustBePresent(error: String) extends CreateOrUpdateOneError


  case object TeamCannotReceiveEarlyUpdates extends CreateOrUpdateOneError

  case class PrimaryPhoneNotPresent(errMsg: String) extends CreateOrUpdateOneError

  case class Phone3PresentButNotPhone2(errMsg: String) extends CreateOrUpdateOneError

  case class CannotPassSecondaryPhones(errMsg: String) extends CreateOrUpdateOneError
}

sealed trait UploadCSVError

object UploadCSVError {
  case object NoFileFound extends UploadCSVError

  case object NoPermissionToEdit extends UploadCSVError

  case class ErrorWhileFindingTeamAccount(error: Throwable) extends UploadCSVError

  case object NewOwnerTeamMemberNone extends UploadCSVError

  case class UploadCSVOrParseFailed(error: String) extends UploadCSVError
}

case class CreateOrUpdateOneResponse(
                                      responseMsg: String,
                                      responseObj: JsObject
                                    )

sealed trait AssignProspectsToCampaignError

object AssignProspectsToCampaignError {
  case class CampaignNotFoundError(error: String) extends AssignProspectsToCampaignError

  case class ProspectNotFoundError(error: String) extends AssignProspectsToCampaignError

  case class ProspectIsAlreadyAssigned(error: String) extends AssignProspectsToCampaignError

  case class ErrorWhileAssigningProspect(error: String) extends AssignProspectsToCampaignError

  case class CampaignFetchError(error: String) extends AssignProspectsToCampaignError
}

sealed trait SearchProspectsForAddToExisting

object SearchProspectsForAddToExisting {
  case class DBFailure(error: Throwable) extends SearchProspectsForAddToExisting

  case class EmptySearchString(error: String) extends SearchProspectsForAddToExisting
}

sealed trait UpdateSpecificProspectError

object UpdateSpecificProspectError {
  case class InvalidEmailError(error: String) extends UpdateSpecificProspectError

  case class ServerErrorWhileUpdate(error: Throwable) extends UpdateSpecificProspectError

  case class ProspectNotFound(error: String) extends UpdateSpecificProspectError

  case class ProspectNotUpdated(error: String) extends UpdateSpecificProspectError

  case class InternalEmailError(error: String) extends UpdateSpecificProspectError

  case class InvalidPhoneNumber(error: String) extends UpdateSpecificProspectError
}

sealed trait InsertSecondaryProspectEmailError

object InsertSecondaryProspectEmailError {
  case class DBFailure(err: Throwable) extends InsertSecondaryProspectEmailError

  case class InvalidEmailFormat(err: String) extends InsertSecondaryProspectEmailError

  case class DuplicateProspect(err: String) extends InsertSecondaryProspectEmailError

  case class CampaignUpdateFailed(err: String) extends InsertSecondaryProspectEmailError

  case class InternalEmailOrDomainFound(err: String) extends InsertSecondaryProspectEmailError
}

sealed trait CreateOrUpdateProspectBatchError

object CreateOrUpdateProspectBatchError {
  case class FailedToAssignProspect(errStr: String) extends CreateOrUpdateProspectBatchError

  case class InvalidProspectSource(errStr: String) extends CreateOrUpdateProspectBatchError

  case class ProspectListEmpty(errStr: String) extends CreateOrUpdateProspectBatchError

  case class InvalidEmails(errStr: String) extends CreateOrUpdateProspectBatchError

  case class MoreThanOneOwnerInvolved(errStr: String) extends CreateOrUpdateProspectBatchError

  case class FoundMultipleProspectsList(errStr: String) extends CreateOrUpdateProspectBatchError

  case class BadRequestError(errStr: String) extends CreateOrUpdateProspectBatchError

  case class ErrorWhileCreatingProspect(err: Throwable) extends CreateOrUpdateProspectBatchError
}

sealed trait CreateOrUpdateProspectsBatchApiError

object CreateOrUpdateProspectsBatchApiError {
  case class BadRequestError(errors: List[ErrorResponseProspectsPostApi]) extends CreateOrUpdateProspectsBatchApiError

  case class ErrorWhileCreatingProspect(err: Throwable) extends CreateOrUpdateProspectsBatchApiError

  case class ErrorWhileFetchingProspectsData(err: Throwable) extends CreateOrUpdateProspectsBatchApiError

  case object EmailCannotBeOptionalInTheOldFlow extends CreateOrUpdateProspectsBatchApiError
}

sealed trait GetProspectIdFromSrIdentifierError

object GetProspectIdFromSrIdentifierError {

  case class GetProspectIdError(err: Throwable) extends GetProspectIdFromSrIdentifierError

  case object InvalidProspectIdError extends GetProspectIdFromSrIdentifierError


}

case class AssignProspectsToCampaignResponse(
                                              responseMsg: String,
                                              assignedProspectIdsLength: Int,
                                              assignedProspectIds: List[Long],
                                              campaignId: Long
                                            )

case class ProspectData(
                         prospectAccountCreateFormData: Option[ProspectAccountCreateFormData],
                         prospectCreateFormData: ProspectCreateFormData
                       )

case class NewProspectsToBeAdded(
                                  prospectAccountCreateFormData: Option[ProspectAccountCreateFormData],
                                  prospectCreateFormData: ProspectCreateFormData,
                                  prospect_uuid: String
                                )

case class InternalEmailsOrDomains(
                                    internal_emails: List[String],
                                    internal_domain: List[String]
                                  )

object InternalEmailsOrDomains {
  implicit val writes: OWrites[InternalEmailsOrDomains] = Json.writes[InternalEmailsOrDomains]
}

case class ExistingProspect(
                             prospect_id: Long,
                             email: String,
                             name: String
                           )

object ExistingProspect {
  implicit val writes: OWrites[ExistingProspect] = Json.writes[ExistingProspect]
}

case class AddEmailToExistingProspectForm(
                                           prospect_id: Long,
                                           email: String,
                                           active_campaign_ids: Seq[Long]
                                         )

object AddEmailToExistingProspectForm {
  implicit val enumTypeFormat: Format[FieldTypeEnum.Value] = EnumUtils.enumFormat(FieldTypeEnum)

  given formats: Reads[AddEmailToExistingProspectForm] = Json.reads[AddEmailToExistingProspectForm]
}

case class SecondaryProspectEmail(
                                   prospect_id: Long,
                                   added_by_account_id: Long,
                                   team_id: Long,
                                   email: String,
                                   email_domain: String,
                                   email_format_valid: Boolean,
                                   is_primary: Boolean
                                 )

object SecondaryProspectEmail {
  implicit val read: Reads[SecondaryProspectEmail] = Json.reads[SecondaryProspectEmail]
}

case class ProspectInternalEmails(
                                   prospects_with_internal_emails: Seq[ProspectCreateFormData],
                                   prospects_without_internal_emails: Seq[ProspectCreateFormData]
                                 )

case class ValidAndInvalidProspectUuidIdList(
                                              invalid_uuids: List[ProspectUuid],
                                              valid_prospect_ids: List[ProspectId]
                                            )

case class ProspectsApiSearchParams(
                                     range: InferredQueryTimeline.Range,
                                     is_first: Boolean,
                                     campaign_id: Option[Long] = None
                                   )

case class ProspectsKeyMatchingInDB(
                                     prospect_id: Long,
                                     column_name: SrProspectColumns,
                                     column_value: String
                                   )

case class ListOfProspectsWithDataFromDifferentRowsInDb(
                                                         prospectsDuplicateFound: List[ProspectsKeyMatchingInDB],
                                                         originalProspectRow: ProspectCreateFormData
                                                       )

case class ProspectsDeduplicationColumnsFromDb(
                                                prospect_emails: Map[String, Long],
                                                prospect_phones: Map[String, Long],
                                                prospect_linkedin: Map[String, Long],
                                                prospect_company_firstname_lastname: Map[String, Long]
                                              )

case class ProspectsToBeForceUpdated(
                                      prospect: ProspectCreateFormData,
                                      prospect_id: Long,
                                      deDuplicationColumnTypes: Seq[SrProspectColumns]
                                    )

case class ProspectDeduplicationColumnsData(
                                             email: Option[String],
                                             phone: Option[String],
                                             linkedin_url: Option[String],
                                             company: Option[String],
                                             first_name: Option[String],
                                             last_name: Option[String]
                                           )


case class BlacklistStatusForProspectUuid(
                                           prospectUuid: ProspectUuid,
                                           isInBlacklist: Boolean,
                                         )

case class ProspectEmailsTobeAddedInForceUpdateProspect(
                                                         prospectEmailData: ProspectEmailData,
                                                         ownerId: Option[Long]
                                                       )

object ProspectService {
  def findNumberOfDuplicateEmailsOfProspects(prospects: Seq[ProspectCreateFormData]): Int = {

    prospects.count(_.email.isDefined) - prospects.filter(_.email.isDefined).groupBy(_.email.get.toLowerCase).map(x => x._2.head).toSeq.length

  }

  def prospectFromUserInputIsMatchingProspectInDb(

                                                   ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads: Boolean,

                                                   prospectFromUserInput: ProspectCreateFormData,

                                                   duplicateProspectFromDatabase: DuplicateProspectResult,

                                                   prospectCustomColumnDefs: Seq[api.columns.ColumnDef]

                                                 ): (Boolean, List[String]) = {


    val matching: (Boolean, List[String]) = ProspectCreateFormData
      .isUserInputAndProspectInDbMatching(
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads,
        userInput = prospectFromUserInput,
        prospectInDb = duplicateProspectFromDatabase.prospectCreateFormData,
        customColumnDefs = prospectCustomColumnDefs,
      )
      .getOrElse((false, List()))

    matching

  }

  /**
   * prospect_inserted_emails is the result of prospect emails inserted in prospects_emails table
   * which is having prospect's email and id
   * prospect_inserted_data is the result of prospect data inserted in prospects table
   * which is having prospect's id, uuid and other fields
   * Using these two list we need to construct single object having email and data both
   * by finding prospect_data in prospect_inserted_emails which matches the prospect_id in prospect_inserted_emails
   *
   * Similarly, prospect_updated_emails is having prospect_id and ProspectCreateFormData with email and other data
   * prospect_updated_data is having updated_prospects data along with prospect_id
   */
  def constructProspectDataForApi(
                                   prospect_inserted_emails: List[NewlyCreatedProspect],
                                   prospect_inserted_data: List[InsertOrUpdateProspectResult],
                                   prospect_updated_emails: List[ProspectsToBeForceUpdated],
                                   prospect_updated_data: List[InsertOrUpdateProspectResult]
                                 )(using logger: SRLogger): Try[List[UpsertSQLProspectData]] = Try {

    //These conditions should not happen as data getting inserted/updated cannot be more that what is passed to insert/update
    if (prospect_updated_data.length > prospect_updated_emails.length) {
      logger.info(s"prospect_updated_emails:: ${prospect_updated_emails.map(_.prospect_id)}, prospect_updated_data:: ${prospect_updated_data.map(_.prospect_id)}")
    }
    if (prospect_inserted_data.length > prospect_inserted_emails.length) {
      logger.info(s"prospect_inserted_emails:: ${prospect_inserted_emails.map(_.prospect_id)}, prospect_inserted_data:: ${prospect_inserted_data.map(_.prospect_id)}")
    }

    val allInsertedProspectsData: List[UpsertSQLProspectData] = prospect_inserted_data.flatMap(p => {
      prospect_inserted_emails
        .find(pe => pe.prospect_id == p.prospect_id)
        .map { prospect_inserted_email =>
          UpsertSQLProspectData(
            prospect_email = prospect_inserted_email.email, prospect_data = p
          )
        }
    })

    val allUpdatedProspectData: List[UpsertSQLProspectData] = prospect_updated_data.flatMap(p => {
      prospect_updated_emails.find(pe => {
          pe.prospect_id == p.prospect_id
        })
        .filter(_.prospect.email.isDefined)
        .map { prospect_updated_email =>
          UpsertSQLProspectData(
            prospect_email = prospect_updated_email.prospect.email.get, prospect_data = p)
        }
    })

    allUpdatedProspectData ++ allInsertedProspectsData
  }

  def getDuplicateProspectEmails(prospect_emails: Seq[String]): List[String] = {
    val seen = scala.collection.mutable.Set[String]()
    val duplicates = scala.collection.mutable.Set[String]()

    for (str <- prospect_emails) {
      if (seen.contains(str)) {
        duplicates.add(str)
      } else {
        seen.add(str)
      }
    }

    duplicates.toList
  }

  def validateQueryParams(
                           parsedParams: Map[String, Vector[String]],
                           campaign_id: Option[Long] = None
                         )(using logger: SRLogger): Try[ProspectsApiSearchParams] = {
    for {

      (is_first: Boolean, range: InferredQueryTimeline.Range) <- Helpers.getTimeLineRange(parsedParams = parsedParams)

      searchParams <- Success(ProspectsApiSearchParams(
        range = range,
        is_first = is_first,
        campaign_id = campaign_id
      ))

    } yield {
      searchParams

    }
  }

  def validateUpdateProspectsStatusData(
                                         isApiCall: Boolean,
                                         data: ProspectBatchActionStatusChange,
                                         prospectIds: Seq[Long]
                                       ): List[ErrorResponseUpdateProspectStatusApi] = {
    var error_list: List[ErrorResponseUpdateProspectStatusApi] = List()
    if (isApiCall && (data.campaign_ids.isEmpty)) {
      val err = ErrorResponseUpdateProspectStatusApi(
        message = "campaign_ids cannot be empty.",
        error_type = UpdateProspectStatusErrorType.BAD_REQUEST,
        data = None
      )
      error_list = error_list.appended(err)
    }
    if (isApiCall && (prospectIds.isEmpty)) {
      val err = ErrorResponseUpdateProspectStatusApi(
        message = "prospect_ids cannot be empty.",
        error_type = UpdateProspectStatusErrorType.BAD_REQUEST,
        data = None
      )
      error_list = error_list.appended(err)
    }
    if (isApiCall && (prospectIds.size > AppConfig.prospectStatusChangeProspectsLimit)) {
      val err = ErrorResponseUpdateProspectStatusApi(
        message = "You can send upto 500 ids in prospect_ids at a time.",
        error_type = UpdateProspectStatusErrorType.BAD_REQUEST,
        data = None
      )
      error_list = error_list.appended(err)
    }
    if (isApiCall &&
      (NewProspectStatus.fromKey(data.prospect_status).isFailure)
    ) {
      val err = ErrorResponseUpdateProspectStatusApi(
        message = "Please send a valid prospect status.",
        error_type = UpdateProspectStatusErrorType.INVALID_PROSPECT_STATUS,
        data = None
      )
      error_list = error_list.appended(err)
    }
    if (isApiCall && (data.prospect_status.equals(NewProspectStatus.RESUME_LATER.key) && (data.will_resume_at.isEmpty || data.will_resume_at_tz.isEmpty))) {
      val err = ErrorResponseUpdateProspectStatusApi(
        message = "Status change passed resume_later but  \"will_resume_at\"  or \"will_resume_at_tz\" fields not specified.",
        error_type = UpdateProspectStatusErrorType.RESUME_LATER_DATA_ERROR,
        data = None
      )
      error_list = error_list.appended(err)
    }
    error_list
  }

  def checkIfPhoneIsValid(phone: Option[String]) = {
    phone.isDefined && phone.get.trim.nonEmpty && PhoneNumberValidation.isValidPhoneNumber(phone.get)
  }

  def checkIfLinkedInUrlIsValid(linkedInUrl: Option[String]): Boolean = {
    linkedInUrl.isDefined &&
      linkedInUrl.get.trim.nonEmpty &&
      LinkedinHelperFunctions.normalizeLinkedInURL(linkedInUrl).isDefined &&
      LinkedInUrlValidation.isValidProfileUrl(LinkedinHelperFunctions.normalizeLinkedInURL(linkedInUrl).get)
  }

  //Fixme revisit this function for returning true when phone is not defined
  def validateEmailAndPhoneAndLinkedinForCreateOrUpdateOne(
                                                            //FIXME: Use Value Classes
                                                            phone: Option[String],
                                                            phone_2: Option[String],
                                                            phone_3: Option[String],
                                                            email: Option[String],
                                                            linkedInUrl: Option[String]
                                                          ): Either[ValidateEmailAndPhoneForCreateOrUpdateOneErr, Boolean] = {


    val isPhoneNumberValid: Boolean = {

      val isPhoneValid = checkIfPhoneIsValid(phone)
      val isPhone2Valid = checkIfPhoneIsValid(phone_2)
      val isPhone3Valid = checkIfPhoneIsValid(phone_3)

      //if primary phone defined then only check - other validations will be in createOrUpdateOne function
      if (phone.isDefined && phone.get.trim.nonEmpty) {
        isPhoneValid || isPhone2Valid || isPhone3Valid
      } else true
    }

    val isEmailValid: Boolean = if (email.isDefined) {
      EmailValidationService.validateEmailFormat(email.get)
    } else false

    val isLinkedUrlValid: Boolean = if (linkedInUrl.isDefined && linkedInUrl.get.trim.nonEmpty) {
      LinkedInUrlValidation.isValidProfileUrl(LinkedinHelperFunctions.normalizeLinkedInURL(linkedInUrl).get)
    } else true


    if (!isPhoneNumberValid && !isEmailValid && !isLinkedUrlValid) {
      Left(ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidEmailAndPhoneNumberAndLinkedIn(s"Provide valid email and phone number and LinkedIn"))
    } else if (email.isDefined && phone.isDefined && !isEmailValid && !isPhoneNumberValid) {
      Left(ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidEmailAddressAndPhone("Provide a correct Phone and Email Address"))
    } else if (phone.isDefined && linkedInUrl.isDefined && !isLinkedUrlValid && !isPhoneNumberValid) {
      Left(ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidPhoneAndLinkedIn("Provide a correct Phone and Linkedin Url"))
    } else if (email.isDefined && linkedInUrl.isDefined && !isEmailValid && !isLinkedUrlValid) {
      Left(ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidEmailAddressAndLinkedIn("Provide a correct Email Address and linkedin url"))
    } else if (email.isDefined && !isEmailValid) {
      Left(ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidEmailAddress("Provide a valid email address"))
    } else if (phone.isDefined && !isPhoneNumberValid) {
      Left(ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidPhoneNumber(s"${phone.get} is invalid phone number."))
    } else if (linkedInUrl.isDefined && !isLinkedUrlValid) {
      Left(ValidateEmailAndPhoneForCreateOrUpdateOneErr.InvalidLinkedinUrl(s"${linkedInUrl.get} is invalid linkedin url"))
    } else {
      Right(true)
    }
  }

  private def hasEmailInCreateProspectForm(email: Option[String]): Boolean = {
    if (email.isEmpty || (email.isDefined && email.get.trim.isEmpty)) {
      false
    } else {
      EmailValidationService.validateEmailFormat(email.get)
    }

  }

  private def hasPhoneInCreateProspectForm(phone: Option[String]): Boolean = {
    if (phone.isEmpty || (phone.isDefined && phone.get.trim.isEmpty)) false else {
      checkIfPhoneIsValid(phone)
    }
  }

  private def hasLinkedInInCreateProspectForm(linkedin: Option[String]): Boolean = {
    if (linkedin.isEmpty || (linkedin.isDefined && linkedin.get.trim.isEmpty)) false else {
      LinkedInUrlValidation.isValidProfileUrl(LinkedinHelperFunctions.normalizeLinkedInURL(linkedin).get)
    }
  }

  private def hasSrDeduplicatingCriteria(
                                          company: Option[String],
                                          first_name: Option[String],
                                          last_name: Option[String]
                                        ): Boolean = {
    if (
      (company.isEmpty || (company.isDefined && company.get.trim.isEmpty)) ||
        (first_name.isEmpty || (first_name.isDefined && first_name.get.trim.isEmpty)) ||
        (last_name.isEmpty || (last_name.isDefined && last_name.get.trim.isEmpty))
    ) false else true
  }

  def areAnyOfTheDeduplicationColumnsPresentForProspect(
                                                         prospect: ProspectDeduplicationColumnsData
                                                       ): Boolean = {
    //a. Can have email
    //b. Can have Phone number
    //c. Can have LinkedIn Url
    //d. Can have Company name, first name and Last name

    val (email, phone, linkedin_url, company, first_name, last_name) = (prospect.email, prospect.phone, prospect.linkedin_url, prospect.company, prospect.first_name, prospect.last_name)

    if (
      hasEmailInCreateProspectForm(email) ||
        hasPhoneInCreateProspectForm(phone) ||
        hasLinkedInInCreateProspectForm(linkedin_url) ||
        hasSrDeduplicatingCriteria(company = company, first_name = first_name, last_name = last_name)
    ) {
      true
    } else false
  }

  /**
   * List of test cases covered:
   * 1.Should return success if no any rows from ProspectCreateFormData have data from two different prospects in db
   * 2.Should return success if there are any rows with email and company are having same company in input but different firstName and lastName") {
   * 3.Should return failure if there are any rows with email and phone are from two different prospects in db
   * 4.Should return failure if there are any rows with email and linkedin are from two different prospects in db
   * 5.Should return failure if there are any rows with email and company-firstname-lastname are from two different prospects in db
   * 6.Should return failure if there are any rows with (linkedin and phone) or (linkedin and company-firstname-lastname) are from two different prospects in db
   * 7.Should return failure if there are any rows with phone and company_firstname_lastname are from two different prospects in db
   */
  def checkIfFormDataMatchingDb(
                                 prospect_emails: Map[String, Long],
                                 prospect_phones: Map[String, Long],
                                 prospect_linkedin: Map[String, Long],
                                 prospect_cfl: Map[String, Long],
                                 prospects_input: List[ProspectCreateFormData]
                               ): List[Either[ListOfProspectsWithDataFromDifferentRowsInDb, ProspectCreateFormData]] = {
    prospects_input
      .map(p => {

        var list_prospects: List[ProspectsKeyMatchingInDB] = List()

        if (p.email.isDefined) {
          val prospect_id = prospect_emails.get(p.email.get.trim.toLowerCase)
          list_prospects = if (prospect_id.isDefined) {

            val prospect = ProspectsKeyMatchingInDB(
              prospect_id = prospect_id.get,
              column_name = SrProspectColumns.Email,
              column_value = p.email.get
            )

            list_prospects.appended(prospect)

          } else list_prospects
        }

        if (p.linkedin_url.isDefined) {
          val prospect_id = prospect_linkedin.get(p.linkedin_url.get.trim.toLowerCase)

          list_prospects = if (prospect_id.isDefined) {

            val prospect = ProspectsKeyMatchingInDB(
              prospect_id = prospect_id.get,
              column_name = SrProspectColumns.LinkedinUrl,
              column_value = p.linkedin_url.get
            )

            list_prospects.appended(prospect)

          } else list_prospects
        }

        if (p.phone.isDefined) {
          val prospect_id = prospect_phones.get(p.phone.get.trim.toLowerCase)
          list_prospects = if (prospect_id.isDefined) {

            val prospect = ProspectsKeyMatchingInDB(
              prospect_id = prospect_id.get,
              column_name = SrProspectColumns.Phone,
              column_value = p.phone.get
            )

            list_prospects.appended(prospect)

          } else list_prospects
        }

        if (p.company.isDefined && p.first_name.isDefined && p.last_name.isDefined) {
          val cflKey: String = p.company.get.trim.toLowerCase + p.first_name.get.trim.toLowerCase + p.last_name.get.trim.toLowerCase

          val prospect_id = prospect_cfl.get(cflKey)
          list_prospects = if (prospect_id.isDefined) {

            val prospect = ProspectsKeyMatchingInDB(
              prospect_id = prospect_id.get,
              column_name = SrProspectColumns.CompanyFirstnameLastname,
              column_value = cflKey
            )

            list_prospects.appended(prospect)

          } else list_prospects
        }


        val distinctProspectsMatchingInDB: List[ProspectsKeyMatchingInDB] = list_prospects.distinctBy(_.prospect_id)

        if (distinctProspectsMatchingInDB.length == 1 || list_prospects.isEmpty) {
          Right(p)
        } else {
          Left(ListOfProspectsWithDataFromDifferentRowsInDb(
            prospectsDuplicateFound = distinctProspectsMatchingInDB,
            originalProspectRow = p
          )
          )
        }

      })

  }

  def extractUniqueProspectsForMultiRowInsert(
                                               prospects: Seq[ProspectCreateFormData],
                                               orgId: Long,
//                                               emailNotCompulsoryEnabled: Boolean
                                             ): Iterable[ProspectCreateFormData] = {
    val uniqueEmailProspects: Iterable[ProspectCreateFormData] = prospects
      .filter(_.email.isDefined)
      .groupBy(_.email.get.toLowerCase.trim)
      .map(_._2.head)

//    if (emailNotCompulsoryEnabled) {

      val uniqueLinkedinProspects = prospects
        .filter(p => p.linkedin_url.isDefined && p.email.isEmpty)
        .groupBy(_.linkedin_url.get.toLowerCase.trim)
        .map(_._2.head)

      val uniquePhoneProspects = prospects
        .filter(p => p.phone.isDefined && p.email.isEmpty && p.linkedin_url.isEmpty)
        .groupBy(_.phone.get.trim)
        .map(_._2.head)

      val uniqueCompanyFirstNameLastNameProspects = prospects
        .filter(p => p.phone.isEmpty && p.email.isEmpty && p.linkedin_url.isEmpty && (p.company.isDefined && p.first_name.isDefined && p.last_name.isDefined))
        .groupBy(p => (p.company.get.toLowerCase.trim + p.first_name.get.toLowerCase.trim + p.last_name.get.toLowerCase.trim))
        .map(_._2.head)

      uniqueEmailProspects ++ uniqueLinkedinProspects ++ uniquePhoneProspects ++ uniqueCompanyFirstNameLastNameProspects
//    } else {
//      uniqueEmailProspects
//    }
  }

  def constructProspectAccountData(
                                    prospects: Iterable[ProspectCreateFormData],
                                    org_id: Long,
//                                    emailNotCompulsaryEnabled: Boolean
                                  )(using Logger: SRLogger): List[ProspectData] = {

    val prospectsAccountDataWithEmail: List[ProspectData] = prospects
      .filter(_.email.isDefined)
      .map(prospect => {

        val (_, emailDomain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(prospect.email.get)

        val prospectAccountToCreate = ProspectAccountCreateFormData(
          name = emailDomain.trim.toLowerCase,
          custom_id = None,
          description = None,
          source = None,
          website = None,
          industry = None,
          linkedin_url = None,
          created_at = None,
          custom_fields = Option(Json.obj()),
          update_account = None
        )

        ProspectData(prospectAccountCreateFormData = Some(prospectAccountToCreate), prospectCreateFormData = prospect)
      })
      .toList
      .sortBy(_.prospectCreateFormData.email) //this is sorted to pass the test case which needs prospects in order

//    if (emailNotCompulsaryEnabled) {
      prospects.filter(_.email.isEmpty).map(prospect => {
        ProspectData(prospectAccountCreateFormData = None, prospectCreateFormData = prospect)
      }).toList ++ prospectsAccountDataWithEmail

//    } else {
//      prospectsAccountDataWithEmail
//    }
  }

  def getCreatedProspectIdsFromUpsertSQLResult(
                                                result: UpsertSQLResult,
                                                org_id: Long,
//                                                emailNotCompulsoryEnabled: Boolean
                                              ): Seq[Long] = {
    //ideally both createdProspectIds & createdProspectEmails should be always equal for old flow but still differentiating the paths
//    if (emailNotCompulsoryEnabled) {
      result.createdProspectIds
//    } else {
//      result.createdProspectEmails.map(_.prospect_id)
//    }
  }

  def checkIfAllPhonesAreValidIfDefined(phones: List[Option[String]]): Boolean = {
    phones.forall(ph => {
      ph.isEmpty || (ph.isDefined && ph.get.isEmpty) || (ph.isDefined && PhoneNumberValidation.isValidPhoneNumber(ph.get))
    })
  }

  def findProspectsInBlacklistForMultiRowInsert(
                                                 blacklist: Seq[Blacklist],
                                                 newProspects: Seq[NewProspectsToBeAdded]
                                               )(using logger: SRLogger): Seq[BlacklistStatusForProspectUuid] = {
    newProspects.map(p => {
      val prospect = p.prospectCreateFormData
      val isInBlacklist: Boolean = blacklist.exists(b => {
        val p_domain: Option[String] = prospect.email.map(EmailValidationService.getLowercasedNameAndDomainFromEmail(_)._2)

        b.do_not_contact_type match {
          case DoNotContactType.EMAIL =>
            prospect.email.isDefined && b.name.toLowerCase == prospect.email.get.toLowerCase
          case DoNotContactType.DOMAIN =>
            p_domain.isDefined && prospect.email.isDefined && b.name.toLowerCase == p_domain.get.toLowerCase && !b.excluded_emails.contains(prospect.email.get)
          case DoNotContactType.PHONE =>
            prospect.phone.isDefined && b.name == prospect.phone.get
        }
      })

      BlacklistStatusForProspectUuid(
        isInBlacklist = isInBlacklist,
        prospectUuid = ProspectUuid(p.prospect_uuid)
      )
    })
  }

  def findNewProspectEmailsDataForForceUpdate(
                                               prospectsToBeUpdated: Iterable[ProspectsToBeForceUpdated],
                                               prospectEmailData: List[ProspectEmailData]
                                             ): List[ProspectEmailsTobeAddedInForceUpdateProspect] = {

    prospectsToBeUpdated
      .filter(_.prospect.email.isDefined)
      .filter(_.prospect.email.get.trim.nonEmpty)
      .map(pr => {
        val pid = pr.prospect_id

        //prospect email data of this prospect
        val prospectEmailDataForThis: List[ProspectEmailData] = prospectEmailData.filter(ped => ped.prospect_id == pid)

        //new prospect email data in update flow
        val newProspectEmails = prospectEmailDataForThis.filter(ped => ped.email_data.email != pr.prospect.email.get)

        val newEmail = pr.prospect.email.get.trim.toLowerCase
        val emailFormatValid = EmailValidationService.validateEmailFormat(newEmail)

        if (prospectEmailDataForThis.isEmpty) {

          //if existing emails are empty then add new primary
          Some(ProspectEmailsTobeAddedInForceUpdateProspect(
            prospectEmailData = ProspectEmailData(
              prospect_id = pid,
              email_data = ProspectEmail(
                email = newEmail,
                is_valid = emailFormatValid,
                is_primary = true
              )),
            ownerId = pr.prospect.owner_id
          ))
        } else {
          val isSameEmail = prospectEmailDataForThis.map(_.email_data.email).contains(newEmail)

          if (isSameEmail) {
            None
          } else {
            //if existing emails are there then add secondary
            Some(ProspectEmailsTobeAddedInForceUpdateProspect(
              prospectEmailData = ProspectEmailData(
                prospect_id = pid,
                email_data = ProspectEmail(
                  email = newEmail,
                  is_valid = emailFormatValid,
                  is_primary = false
                )),
              ownerId = pr.prospect.owner_id
            ))
          }


        }

      }).toList.filter(_.isDefined).map(_.get)
  }

  /*

   15-april-2024 : found out that linkedin urls being provided doesn't contains
   https:// because of which its failing in frontend when we are opening via
   window.open(linkedin_url). hence this function is added

   */

}

class ProspectService(
                       prospectDAOService: ProspectDAOService,

                       accountDAO: AccountDAO,
                       accountService: AccountService,
                       campaignService: CampaignService,
                       mqTrigger: MQTrigger,
                       // emailValidationService: EmailValidationService,
                       campaignProspectService: CampaignProspectService,
                       tagService: TagService,
                       prospectServiceV2: ProspectServiceV2,
                       prospectAccountDAO1: ProspectAccountDAO1,
                       prospectColumnDef: ProspectColumnDef,
                       prospectsEmailsDAO: ProspectsEmailsDAO,
                       blacklistProspectCheckDAO: BlacklistProspectCheckDAO,
                       campaignProspectTimzonesJedisService: CampaignProspectTimezonesJedisService,
                       generateTempId: GenerateTempId,
                       emailSettingDAO: EmailSettingDAO,
                       prospectUpdateCategoryTemp: ProspectUpdateCategoryTemp,
                       mergeTagService: MergeTagService,
                       mqAssociateProspectToOldEmails: MQAssociateProspectToOldEmails,
                       potentialDuplicateProspectService: PotentialDuplicateProspectService,
                       srUuidUtils: SrUuidUtils,
                       srUuidService: SrUuidService,
                       mqDomainServiceProviderDNSService: MQDomainServiceProviderDNSService,
                       srRollingUpdateCoreService: SrRollingUpdateCoreService
                     ) {


  /*STRICTLY TO be used in SPEC*/
  def createUpdateOrAssignProspectOnlyForTestSPEC(
                                                   accountId: AccountId,
                                                   teamId: TeamId,
                                                   account: Account,
                                                   campaignId: Option[CampaignId] = None,
                                                   generateProspectCountIfNoGivenProspect: Int = 5,
                                                   givenProspect: Seq[ProspectCreateFormData]
                                                 )(using Logger: SRLogger): Try[Seq[ProspectObject]] = {


    for {
      create: CreateOrUpdateProspectsResult <- createOrUpdateProspects(
        ownerAccountId = accountId.id,
        teamId = teamId.id,
        listName = None,
        prospects = givenProspect,
        updateProspectType = UpdateProspectType.ForceUpdate,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,

        doerAccount = account,
        prospectSource = None,
        prospectAccountId = None,

        campaign_id = campaignId.map(p => p.id),
        prospect_tags = None,
        ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        auditRequestLogId = None,

        deduplicationColumns = None,

        SRLogger = Logger
      )

      findProspect: Seq[ProspectObject] <- {
        prospectDAOService.find(
          byProspectIds = create.created_ids ++ create.updated_ids ++ create.assigned_ids,
          teamId = teamId.id,
          Logger = Logger
        )
      }


    } yield {
      findProspect
    }

  }

  def checkIfUploadCsvDataIsValid(
                                   csvData: JsValue,
                                   teamId: TeamId
                                 )(Logger: SRLogger): Either[String, true] = {

//    val emailNotCompulsoryEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//      teamId = teamId,
//      feature = SrRollingUpdateFeature.EmailNotCompulsory
//    )(Logger)

//    if (emailNotCompulsoryEnabled) {

      val isValidCSVData: Boolean = SrProspectDeduplicationColumns.checkIfJsonDataHasAnyDeduplicationColumn(csvData)

      if (isValidCSVData) Right(true)
      else Left("Please provide valid data for at least one of these deduplication checks: email, phone, linkedin_url, (first_name, last_name, company)")

//    } else {
//
//      val emailField: Option[String] = (csvData \ "email").asOpt[String]
//
//      if (emailField.isDefined) Right(true)
//      else Left("Please pass email column in csv file")
//
//    }
  }

  def createProspectsAndTagsSeq(
                                 prospectCreateFormDataV2: Seq[ProspectCreateFormDataV2],
                                 createdOrUpdatedProspectsData: List[UpsertSQLProspectData]
                               ): List[ProspectsAndTags] = {
    val prospectIdAndTagsDataMap: List[(Long, Option[ProspectCreateFormDataV2])] = createdOrUpdatedProspectsData.map(data => {
      (data.prospect_data.prospect_id, prospectCreateFormDataV2.filter(_.email.isDefined).find(_.email.get.trim.toLowerCase == data.prospect_email))
    })

    prospectIdAndTagsDataMap.filter(_._2.isDefined)
      .filter(_._2.get.tags.isDefined)
      .map(pnt => {
        val prospect_tag_uuid = srUuidUtils.generateTagsUuid()
        ProspectsAndTags(
          prospect_id = ProspectId(pnt._1),
          tags = pnt._2.get.tags.get.map(TagAndUuid(_, ProspectTagUuid(prospect_tag_uuid)))
        )
      })

  }

  /*
  * PROSPECTS_EMAILS_TODO_UPDATE
 * use case for secondary email and primary emails
 * need to confirm that any email view/link click increments this count
 */
  // prospectData: Seq[(prospectId, repliedAtDate)]
  //Prateek
  def updateLastRepliedAt(
                           prospectData: Seq[(Long, DateTime)],
                           teamId: Long,
                           doerAccountId: AccountId,
                         )(using logger: SRLogger): Try[Seq[Long]] = {

    prospectDAOService.updateLastRepliedAt(
      prospectData = prospectData,
      teamId = teamId
    ).map { prospectIds =>

      // Even if this fails, it will not affect anything else,
      // and there are logs inside that will log and alert regarding the failures.
      prospectUpdateCategoryTemp.autoUpdateProspectCategory(
        teamId = TeamId(id = teamId),
        prospectIds = prospectIds.map(pid => ProspectId(id = pid)),
        doerAccountId = doerAccountId,
        newProspectCategory = ProspectCategory.REPLIED,
      )

      prospectIds

    }
  }



  // PROSPECTS_EMAILS_TODO_INSERT / PROSPECTS_EMAILS_TODO_UPDATE / PROSPECTS_EMAILS_TODO_READ_CLEANED
  /*
   * Use cases:
   * 1. ProspectController.createOrUpdateOne - end-user uploads a single prospect via the form
   *
   * 2. Use case : Adding prospects to the system via API call shared with customers.
   *  ProspectService.createOrUpdateProspectsBatch
   *  ProspectService.createOrUpdateProspectsBatchV3
   *
   * 3. Use case : CREATE_OR_UPDATE_PROSPECT_IN_SMARTREACH CRM
   *  HandleSyncTriggerEventService._syncContactDataFromCRM
   *  TriggerController.manageTPExtensionData (Pipedrive)
   *
   * 4. Use case : CSV uploaded prospects are inserted into prospects table via UploadCsvCronService
   *  ProspectUploadService.uploadCSVViaCronV2
   */

  def createOrUpdateProspects(
                               ownerAccountId: Long,
                               teamId: Long,
                               listName: Option[String],
                               prospects: Seq[ProspectCreateFormData],
                               updateProspectType: UpdateProspectType,
                               ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads: Boolean,

                               doerAccount: Account,
                               prospectSource: Option[ProspectSource.Value],
                               prospectAccountId: Option[Long],

                               // campaign to assign to
                               campaign_id: Option[Long],
                               prospect_tags: Option[String],
                               ignoreProspectInOtherCampaign: IgnoreProspectsInOtherCampaigns,
                               auditRequestLogId: Option[String],

                               prospectsWithTagsForApi: Option[Seq[ProspectCreateFormDataV2]] = None,
                               deduplicationColumns: Option[Seq[SrProspectColumns]],

                               SRLogger: SRLogger,
                               triggerPath: TriggerSource = TriggerSource.OTHER,
                               batchInsertLimit: Int = 100,

                             ): Try[CreateOrUpdateProspectsResult] = Try {

    /* Cannot create test case for new SRLogger
    val Logger = new SRLogger(
      logRequestId = s" ${SRLogger.logRequestId} :: ProspectService.createOrUpdateProspects :: lid_${doerAccount.id} :: aid_$ownerAccountId :: tid_$teamId :: cid_$campaign_id :: paId_$prospectAccountId :: p_count: ${prospects.length} :: "
    )*/


    given Logger: SRLogger = SRLogger
    
    if (teamId == 22382L && triggerPath == CRM) {
      Logger.debug(msg = s"teamId: 22382L and triggerPath: CRM  ${prospects.map(_.linkedin_url)}")
    }

//    val emailNotCompulsoryEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//      teamId = TeamId(teamId),
//      feature = SrRollingUpdateFeature.EmailNotCompulsory
//    )

    // _ at end denotes a curried function ::
    // REF: https://dzone.com/articles/currying-functions-in-scala-1
    val getPermittedAccountIds = PermissionMethods
      .getPermittedAccountIdsForAccountAndPermission(
        loggedinAccount = doerAccount,
        actingTeamId = teamId,
        actingAccountId = doerAccount.internal_id,
        version = "v2", // assuming

        Logger = Logger
      )

    if (teamId == 22382L && triggerPath == CRM) {
      Logger.debug(msg = s"teamId: 22382L and triggerPath: CRM getPermittedAccountIds ${getPermittedAccountIds.toString()}")
    }
    /*
    val foundOwnerTeamMember = Helpers.checkTeamMember(
      doer = doerAccount,
      teamId = teamId,
      checkAccountId = ownerAccountId
    )
    */

    val tagsToBeAdded: Seq[String] = if (prospectsWithTagsForApi.isDefined) {
      if (prospectsWithTagsForApi.get.nonEmpty) {
        prospectsWithTagsForApi.get.filter(_.tags.isDefined).flatMap(_.tags.get)
      } else Seq()
    } else if (prospect_tags.isEmpty || prospect_tags.get.trim.isEmpty) Seq() else {
      prospect_tags.get.trim.split(",").toSeq.distinct
    }

    // better to validate tags before queries for adding/updating prospects
    val invalidTag = tagsToBeAdded.find(tag => !TagService.validateTagName(tag))

    val permAccIds = getPermittedAccountIds(PermType.EDIT_PROSPECTS)

    if (teamId == 22382L && triggerPath == CRM) {
      Logger.debug(msg = s"teamId: 22382L and triggerPath: CRM permAccIds ${permAccIds.toString()} with edit prospects")
    }
    

    if (invalidTag.isDefined) {

      throw BadRequestErrorException(s"Invalid tag: ${invalidTag.get} :: only alphanumeric names with space/hyphens allowed")

    } else if (!permAccIds.contains(ownerAccountId)) {
      Logger.fatal(s"You do not have permission to edit prospects for the selected owner")

      throw BadRequestErrorException("You do not have permission to edit prospects for the selected owner")

    } else {

      accountService.findNewOwnerTeamMember(
        accountId = AccountId(id = ownerAccountId), // FIXME VALUECLASS
        teamId = TeamId(id = teamId) // FIXME VALUECLASS
      ) match {

        case Failure(e) =>
          throw ServerErrorException("Invalid owner selected. Please try again or contact support [2]", cause = e)

        case Success(None) =>
          throw ServerErrorException("Invalid owner selected. Please try again or contact support [3]")


        case Success(Some(foundOwnerTeamMember)) =>

          //  if campaign selected but editing campaign is not permissible to user,
          //  then throw error in the beginning itself
          val campaignDb = campaign_id.map(cid => campaignService.findWithPermission(
            id = cid,
            permittedAccountIds = getPermittedAccountIds(PermType.EDIT_CAMPAIGNS),
            account = Some(doerAccount),
            teamId = teamId) match {
            case None =>
              throw ServerErrorException("Error while fetching campaign details.Please try again or contact support ")

            case Some(campaignDetails) => campaignDetails
          }
          )

          if (campaign_id.nonEmpty && campaignDb.isEmpty) {

            Logger.fatal(s"Selected owner (or you) do not have permission to edit this campaign")

            throw BadRequestErrorException("Selected owner (or you) do not have permission to edit this campaign")

          } else {

            val validateEmailForProspects: Seq[(ProspectCreateFormData, Boolean)] = prospects
              .filter(_.email.isDefined)
              .map(p => (p, EmailValidationService.validateEmailFormat(p.email.get.trim)))

            val prospectsWithValidEmailFormat = validateEmailForProspects.filter(_._2).map(_._1)
            val prospectsWithInvalidEmailFormat = validateEmailForProspects.filter(!_._2)
            val invalidEmails = prospectsWithInvalidEmailFormat.map(_._1.email)

            //Delete internal emails from prospectsWithValidEmailFormat

            val (prospects_data_with_internal_email, prospectsWithoutInternalEmailsAndDomains) = getProspectListWithoutInternalEmails(
              teamId = teamId,
              org_id = doerAccount.org.id,
              Logger = Logger,
              prospectsWithValidEmailFormat = prospectsWithValidEmailFormat
            ) match {
              case Failure(exception) => throw exception
              case Success(result) => (result.prospects_with_internal_emails, result.prospects_without_internal_emails)
            }

            val totalNumberOfDuplicateEmail = ProspectService.findNumberOfDuplicateEmailsOfProspects(prospects = prospects)

            val org_id = doerAccount.org.id

            val filteredProspectsWithoutEmail = prospects.filter(p => p.email.isEmpty || p.email.get.trim.isEmpty)
            val prospectsToBeCreatedOrUpdated: Seq[ProspectCreateFormData] = prospectsWithoutInternalEmailsAndDomains ++ filteredProspectsWithoutEmail


            val deduplicationColumnsForForceUpdate: Seq[SrProspectColumns] = if (deduplicationColumns.isDefined && deduplicationColumns.get.nonEmpty) {
              deduplicationColumns.get
            } else {
              Seq(SrProspectColumns.Email)
            }

            val saveTry =
              __createFromUploadMultiRowInsertNew(
                ownerAccountId = foundOwnerTeamMember.user_id,
                teamId = teamId,
                taId = foundOwnerTeamMember.ta_id,
                listName = listName,
                prospects = prospectsToBeCreatedOrUpdated,
                deduplicationColumns = deduplicationColumnsForForceUpdate, //by default email
                updateProspect = updateProspectType,
                ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads,
                doerAccount = doerAccount,
                prospectSource = prospectSource,
                prospectAccountId = prospectAccountId,
                triggerSource = triggerPath,
                permittedAccountIdsForEditingProspects = getPermittedAccountIds(PermType.EDIT_PROSPECTS),
                permittedAccountIdsForEditingProspectAccounts = getPermittedAccountIds(PermType.EDIT_PROSPECTS), //fixme: remove this
                is_public_api = prospectsWithTagsForApi.isDefined && prospectsWithTagsForApi.get.nonEmpty,
                batchInsertLimit = batchInsertLimit,
                Logger = Logger
              )


            saveTry match {

              case Failure(AddonLimitReachedException(message, _)) =>
                throw BadRequestErrorException(message)

              case Failure(e) =>
                Logger.fatal(s"Prospect.__createFromUploadMultiRowInsert", err = e)
                e.getMessage match {

                  case msg if msg.contains("violates unique constraint") =>

                    if (prospectsWithValidEmailFormat.length == 1) {
                      throw BadRequestErrorException("Another prospect with this email already exists")
                    } else {
                      throw BadRequestErrorException("Some prospects with duplicate email already exist in your account/team")
                    }

                  case msg =>

                    Logger.fatal("Error Occurred while creating prospect ", err = e)
                    throw ServerErrorException("Error while creating prospect", e)

                }


              case Success(results) =>

                val createdProspectIds: Seq[Long] = getCreatedProspectIdsFromUpsertSQLResult(
                  result = results,
                  org_id = org_id,
//                  emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
                )

                Logger.info(s"SUCCESS: Prospect.createFromUploadMultiRowInsert:: res count: createdIds: ${createdProspectIds.length} :: duplicateProspectsData: ${results.duplicateProspectsData.length} :: updatedIds: ${results.updatedIds.length} :: duplicateProspectIdsWithEditPermission: ${results.duplicateProspectIdsWithEditPermission.length} :: duplicatesIgnoredEmailsBecauseOfNoEditPermission: ${results.duplicatesIgnoredEmailsBecauseOfNoEditPermission.length} :: ignored_internal_emails: ${prospects_data_with_internal_email.length}")

                //publishing trigger events
                val createdTriggerMsg = MQTriggerMsg(
                  event = EventType.CREATED_PROSPECT_IN_SMARTREACH.toString,
                  prospectIds = createdProspectIds,
                  accountId = ownerAccountId,
                  teamId = teamId,
                  updatedProspectCategoryId = None,
                  triggerPath = Some(triggerPath),
                  oldProspectDeduplicationColumn = None // sending it as None as it is CREATED_PROSPECT_IN_SMARTREACH
                )

                if (createdProspectIds.nonEmpty) {

                  if (campaign_id.isDefined) {
                    campaignProspectTimzonesJedisService.delete(CampaignId(campaign_id.get))
                  }


                  // created trigger
                  // CREATED_OR_UPDATED_PROSPECT_IN_SMARTREACH is also implicitly called in Trigger.findAllforMQProcess
                  //event_log_id is created for each id in createdProspectIds, this is getting called for the entire Seq, the event_log_id doesn't make sense in this flow
                  //if we are creating the prospect from CRM we should not try to push it back to the CRM
                  mqTrigger.publishEvents(
                    message = createdTriggerMsg
                  )
                  //To associate newly created prospects to the historical conversations
                  mqAssociateProspectToOldEmails.publish(
                    msg = AssociateProspectsData(
                      new_prospects = results.createdProspectEmails,
                      team_id = teamId,
                      campaign_ids = None
                    )
                  )
                }
                //if we are updating the prospect from CRM we should not try to push it back to the CRM

                if (results.updatedIds.nonEmpty) {

                  // update trigger
                  // CREATED_OR_UPDATED_PROSPECT_IN_SMARTREACH is also implicitly called in Trigger.findAllforMQProcess
                  // event_log_id is created for each id in createdProspectIds, this is getting called for the entire Seq, the event_log_id doesn't make sense in this flow
                  mqTrigger.publishEvents(
                    message = createdTriggerMsg.copy(
                      event = EventType.UPDATED_PROSPECT_IN_SMARTREACH.toString,
                      prospectIds = results.updatedIds
                    )
                  )
                }

                /**
                 * 24-Sep-2024: We updated logic to allow adding tags on force-update
                 * even though no any prospects are created or updated
                 *
                 * we are
                 * 1. adding tags for createdProspectIds if force-update is false
                 * 2. adding tags for all prospects created + updated + duplicate-but-not-updated prospect ids if force-update true
                 */

                val prospectIdsToAddTags: Seq[Long] = if (updateProspectType == UpdateProspectType.ForceUpdate) {
                  (createdProspectIds ++ results.updatedIds ++ results.duplicateProspectsData.map(_.prospect_id)).distinct
                } else createdProspectIds

                if (prospectsWithTagsForApi.isDefined && prospectsWithTagsForApi.get.nonEmpty) {
                  //Public api flow for tags

                  tagService.updateTagsForCreatedOrUpdatedProspects(
                    prospectAndTags = createProspectsAndTagsSeq(
                      prospectCreateFormDataV2 = prospectsWithTagsForApi.get,
                      createdOrUpdatedProspectsData = results.prospectDataForApi
                    ),
                    teamId = teamId,
                    accountId = ownerAccountId
                  ) match {

                    case Failure(tagAddErr) =>
                      Logger.fatal(s"error while adding tags to saved/updated prospects in api v3. tags: $tagsToBeAdded", err = tagAddErr)

                    case Success(tagAddedRes) =>
                      Logger.info(s"tags added successfully to saved/updated prospects in api v3")

                  }
                } else {

                  //Other than public API flow: Upload csv, CRM sync prospects, createOrUpdateOne prospect
                  val tagsAndUuids: Seq[TagAndUuid] = tagsToBeAdded.map(t => {
                    val prospect_tag_uuid = srUuidUtils.generateTagsUuid()
                    TagAndUuid(tag = t, uuid = ProspectTagUuid(prospect_tag_uuid))
                  })

                  tagService.updateTagsForProspects(
                    action = AddOrRemoveTagAction.ADD,
                    tags = tagsAndUuids,
                    prospectIdsWithPermission = prospectIdsToAddTags,
                    teamId = teamId,
                    accountId = ownerAccountId
                  ) match {

                    case Failure(tagAddErr) =>
                      Logger.fatal(s"error while adding tags to saved/updated prospects. tags: $tagsToBeAdded", err = tagAddErr)

                    case Success(tagAddedRes) =>
                      Logger.info(s"tags added successfully to saved/updated prospects")

                  }
                }

                if (campaignDb.isEmpty) {

                  CreateOrUpdateProspectsResult(
                    invalid_emails = invalidEmails.filter(_.isDefined).map(_.get),

                    duplicate_prospects_data = results.duplicateProspectsData,
                    duplicate_ids_with_edit_permission = results.duplicateProspectIdsWithEditPermission,

                    created_ids = createdProspectIds,
                    updated_ids = results.updatedIds,
                    duplicates_ignored_for_no_edit_permission = results.duplicatesIgnoredEmailsBecauseOfNoEditPermission,

                    assigned_ids = Seq(),
                    ignored_internal_emails = prospects_data_with_internal_email.filter(_.email.isDefined).map(_.email.get),
                    total_duplicate_emails = totalNumberOfDuplicateEmail,
                    total_dnc_prospects = results.totalDNCProspects,
                    inserted_or_updated_prospects = results.prospectDataForApi
                  )

                } else {

                  // FORCEASSIGNISSUE
                  val assignTry = campaignProspectService.assignProspectsToCampaign(
                    orgId = doerAccount.org.id,
                    doerAccountId = doerAccount.internal_id,
                    doerAccountName = Helpers.getAccountName(doerAccount),
                    permittedAccountIds = getPermittedAccountIds(PermType.EDIT_PROSPECTS),
                    accountId = ownerAccountId,
                    teamId = teamId,
                    campaignId = campaignDb.get.id,
                    campaignName = campaignDb.get.name,
                    campaignSettings = campaignDb.get.settings,

                    // FIX: In case of no force-update, prospects should get assigned regardless
                    // "duplicateProspectIdsWithEditPermission" is used for assigning prospects to campaign when force-update is false but campaign is selected
                    // earlier if force-update was false, duplicate prospects were not getting assigned to campaign also
                    prospectIds = (createdProspectIds ++ results.updatedIds ++ results.duplicateProspectIdsWithEditPermission).toList.distinct,
                    ignoreProspectsInOtherCampaigns = ignoreProspectInOtherCampaign,
                    Logger = Logger

                  )

                  assignTry match {

                    case Failure(e) =>
                      Logger.fatal(s"CampaignProspect.assign", err = e)

                      throw ServerErrorException("Prospects successfully added, but error while assigning uploaded prospects to campaign", e)

                    case Success(assignResult) =>

                      //Logger.info(s"SUCCESS: CampaignProspect.assign success:: assignResult: $assignResult")

                      val assignedIds = assignResult.newlyAssignedProspectIds

                      CreateOrUpdateProspectsResult(
                        invalid_emails = invalidEmails.filter(_.isDefined).map(_.get),

                        duplicate_prospects_data = results.duplicateProspectsData,
                        duplicate_ids_with_edit_permission = results.duplicateProspectIdsWithEditPermission,

                        created_ids = createdProspectIds,
                        updated_ids = results.updatedIds,
                        duplicates_ignored_for_no_edit_permission = results.duplicatesIgnoredEmailsBecauseOfNoEditPermission,

                        assigned_ids = assignedIds,
                        ignored_internal_emails = prospects_data_with_internal_email.filter(_.email.isDefined).map(_.email.get),
                        total_duplicate_emails = totalNumberOfDuplicateEmail,
                        total_dnc_prospects = results.totalDNCProspects,
                        inserted_or_updated_prospects = results.prospectDataForApi
                      )

                  }
                }
            }

          }
      }

    }
  }

  def getCampaignFromDb(
                         campaign_id: Option[Long],
                         permittedAccountIds: Seq[Long],
                         teamId: Long,
                         account: Some[Account]
                       ): Try[Option[CampaignBasicDetails]] = {

    val campaignDb = campaign_id match {
      case None => Success(None)
      case Some(cid) => campaignService.findBasicDetailsWithPermission(id = cid, permittedAccountIds = permittedAccountIds, teamId = teamId)
    }

    campaignDb
  }

  def assignProspectsToCampaign(
                                 teamId: Long,
                                 campaignId: Option[Long],
                                 prospectIds: Seq[Long],
                                 permittedAccountIdsForEditingCampaigns: Seq[Long],
                                 permittedAccountIdsForEditingProspects: Seq[Long],
                                 account: Account,
                                 ignore_prospects_in_other_campaigns: IgnoreProspectsInOtherCampaigns,
                                 accountId: Long,
                                 Logger: SRLogger
                               ): Either[AssignProspectsToCampaignError, AssignProspectsToCampaignResponse] = {

    given logger: SRLogger = Logger

    if (campaignId.isEmpty) {

      Left(AssignProspectsToCampaignError.CampaignNotFoundError("Please provide campaign_id"))

    } else if (prospectIds.isEmpty) {

      Left(AssignProspectsToCampaignError.ProspectNotFoundError("Please select a few prospects to assign"))

    } else {


      Try {
        campaignService.findWithPermission(
          id = campaignId.get,
          account = Some(account),
          permittedAccountIds = permittedAccountIdsForEditingCampaigns,
          teamId = teamId
        )
      } match {

        case Failure(exception) =>
          Left(AssignProspectsToCampaignError.CampaignFetchError("Campaign with given id not found"))

        case Success(None) => {
          Left(AssignProspectsToCampaignError.CampaignNotFoundError("Campaign with given id not found"))
        }
        case Success(Some(campaign)) => {

          val doer = account

          // FORCEASSIGNISSUE
          val tryAssign = campaignProspectService.assignProspectsToCampaign(
            orgId = doer.org.id,
            accountId = accountId,
            teamId = teamId,
            doerAccountId = doer.internal_id,
            doerAccountName = Helpers.getAccountName(doer),
            permittedAccountIds = permittedAccountIdsForEditingProspects,
            campaignId = campaignId.get,
            campaignName = campaign.name,
            campaignSettings = campaign.settings,
            prospectIds = prospectIds.asInstanceOf[List[Long]],
            ignoreProspectsInOtherCampaigns = ignore_prospects_in_other_campaigns,
            Logger = Logger
          )

          tryAssign match {

            case Failure(e) =>

              e.getMessage match {

                case msg if msg.contains("campaigns_prospects_pkey") =>
                  Left(AssignProspectsToCampaignError.ProspectIsAlreadyAssigned("Prospect is already assigned to the given campaign"))

                case msg if msg.contains("campaigns_prospects_campaign_id_fkey") =>
                  Left(AssignProspectsToCampaignError.CampaignNotFoundError("Campaign with given id not found"))

                case msg if msg.contains("campaigns_prospects_prospect_id_fkey") =>
                  Left(AssignProspectsToCampaignError.ProspectNotFoundError("Prospect with given id not found"))

                case msg =>
                  Left(AssignProspectsToCampaignError.ErrorWhileAssigningProspect("Error while assigning prospects: " + msg))

              }

            case Success(assignResult) =>
              val assignedProspectIds = assignResult.newlyAssignedProspectIds

              if (assignedProspectIds.nonEmpty) {
                campaignProspectTimzonesJedisService.delete(CampaignId(campaignId.get))
              }

              val responseMsg = if (assignedProspectIds.isEmpty) {

                if (ignore_prospects_in_other_campaigns == IgnoreProspectsInOtherCampaigns.IgnoreProspectsAddedInOtherCampaigns) {

                  "No prospect assigned. Selected prospects are either already assigned to the campaign, or are currently added in other campaigns"

                } else if (ignore_prospects_in_other_campaigns == IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns) {

                  "No prospect assigned. Selected prospects are either already assigned to the campaign, or are currently active in other campaigns"

                } else {

                  "No prospect assigned. Selected prospects are already assigned to the campaign"

                }

              } else {

                s"${assignedProspectIds.size} prospects assigned to campaign '${campaign.name}'"

              }

              // NOTE: since we are running the scheduler every 15 minutes, no need to use the MQCampaign queue
              //              MQCampaign.publish(MQCampaignMessage(campaignId))

              Right(AssignProspectsToCampaignResponse(responseMsg, assignedProspectIds.length, assignedProspectIds, campaign.id))
          }


        }
      }
    }
  }


  //  def uploadCSV(
  //                 mapping: Map[String, Seq[String]],
  //                 v: String,
  //                 permittedAccountIds: Seq[Long],
  //                 t: Option[TeamMember],
  //                 file: Option[MultipartFormData.FilePart[TemporaryFile]],
  //                 doer: Account,
  //                 campaignId: Option[Long],
  //                 Logger: SRLogger
  //               )(implicit ec: ExecutionContext): Either[UploadCSVError, UploadCSVResult] = {
  //
  //    file match {
  //
  //      case None =>
  //
  //        Left(UploadCSVError.NoFileFound)
  //
  //      case Some(csv) =>
  //        val forceUpdateProspectsKeyName = "force_update_prospects"
  //        // val forceAssignProspectsToCampaignKeyName = "force_assign_to_campaign"
  //        val forceChangeProspectsOwnershipToMeKeyName = "force_change_ownership"
  //        val ignoreProspectsActiveInOtherCampaignsKeyName = "ignore_prospects_active_in_other_campaigns"
  //        val prospectOwnerAccountIdKeyName = "owner_id"
  //        val prospectTagsKeyName = "tags"
  //
  //        val allFieldsInBody = mapping.mapValues(_.head)
  //        val forceUpdateProspects = allFieldsInBody.get(forceUpdateProspectsKeyName).map(value => value == "true").getOrElse(false)
  //        // val forceAssignProspectsToCampaign = allFieldsInBody.get(forceAssignProspectsToCampaignKeyName).map(value => value == "true").getOrElse(false)
  //        val forceChangeProspectsOwnershipToMe = allFieldsInBody.get(forceChangeProspectsOwnershipToMeKeyName).map(value => value == "true").getOrElse(false)
  //        val ignoreProspectsActiveInOtherCampaigns = allFieldsInBody.get(ignoreProspectsActiveInOtherCampaignsKeyName).map(value => value == "true").getOrElse(false)
  //
  //        val prospectOwnerAccountIdFromInput = allFieldsInBody
  //          .get(prospectOwnerAccountIdKeyName)
  //          .flatMap(id => ParseUtils.parseLong(id))
  //
  //        val tagsFromInput = allFieldsInBody
  //          .get(prospectTagsKeyName)
  //          .map(value => value).getOrElse("")
  //
  //        Logger.info(s"\n\n\n\n tagsFromInput $tagsFromInput \n\n\n\n")
  //
  //        if (v == "v2" && prospectOwnerAccountIdFromInput.isEmpty) {
  //          Logger.fatal(s"owner_id is empty :: data: $allFieldsInBody")
  //        }
  //
  //        val prospectOwnerAccountId = prospectOwnerAccountIdFromInput.getOrElse(t.get.user_id)
  //
  //        /*
  //        val foundOwnerTeamMember = Helpers.checkTeamMember(
  //          doer = doer,
  //          teamId = t.get.team_id,
  //          checkAccountId = prospectOwnerAccountId
  //        )
  //        */
  //        if (!permittedAccountIds.contains(prospectOwnerAccountId)) {
  //
  //          Left(UploadCSVError.NoPermissionToEdit)
  //
  //        } else {
  //
  //          accountDAO.findNewOwnerTeamMember(accountId = prospectOwnerAccountId, teamId = t.get.team_id) match {
  //
  //            case Failure(e) =>
  //              Left(UploadCSVError.ErrorWhileFindingTeamAccount(e))
  //
  //            case Success(None) =>
  //              Left(UploadCSVError.NewOwnerTeamMemberNone)
  //
  //            case Success(Some(foundOwnerTeamMember)) =>
  //
  //              val fileName = if (file.isDefined) file.get.filename else "FileNotFound"
  //
  //              val mappingFromClient = allFieldsInBody
  //                .filter(f => {
  //                  f._1 != forceUpdateProspectsKeyName &&
  //                    f._1 != prospectOwnerAccountIdKeyName &&
  //                    f._1 != ignoreProspectsActiveInOtherCampaignsKeyName &&
  //                    f._1 != forceChangeProspectsOwnershipToMeKeyName
  //                })
  //
  //              val result = for {
  //
  //                parserResult <- srCsvParserUtils.parseCSV(
  //                  fileObj = CSVParseFileType.FileInstance(
  //                    file = csv.ref.toFile
  //                  ),
  //                  logger = Logger
  //                )
  //
  //                uploadResult <- prospectUploadService.uploadCSVViaCronV2(
  //                  doerAccount = doer,
  //                  campaignId = campaignId,
  //                  ownerAccountId = prospectOwnerAccountId,
  //                  teamId = t.get.team_id,
  //                  taId = foundOwnerTeamMember.ta_id,
  //                  prospectTags = Some(tagsFromInput),
  //                  ignoreProspectsActiveInOtherCampaigns = ignoreProspectsActiveInOtherCampaigns,
  //                  // forceAssignProspectsToThisCampaign = forceAssignProspectsToCampaign,
  //                  forceChangeOwnershipToMe = forceChangeProspectsOwnershipToMe,
  //                  forceUpdateProspects = forceUpdateProspects,
  //                  fileName = fileName,
  //                  mappingFromClient = mappingFromClient,
  //                  rowMapFromCSV = parserResult.rowMapFromCSV,
  //                  SRLogger = Logger
  //                )
  //
  //
  //              } yield {
  //                (uploadResult, parserResult)
  //              }
  //
  //              result match {
  //
  //                case Failure(e) =>
  //
  //
  //                  val errorMsg = if (e.getMessage != null && e.getMessage.nonEmpty) e.getMessage else "There was an error while uploading your csv. Please try again or contact support."
  //
  //                  Logger.fatal(s"error while uploading directly: fileName: $fileName", err = e)
  //
  //                  prospectUploadService.csvDirectSaveUploadToS3SaveInDB(
  //                    file = csv.ref.toFile,
  //                    fileName = fileName,
  //                    Logger = Logger,
  //                    ownerTeamMember = foundOwnerTeamMember,
  //                    loggedInAccountId = doer.id,
  //                    campaignId = campaignId,
  //                    mappingFromClient = mappingFromClient,
  //                    prospectTags = tagsFromInput,
  //                    forceChangeProspectsOwnershipToMe = forceChangeProspectsOwnershipToMe,
  //                    forceUpdateProspects = forceUpdateProspects,
  //                    ignoreProspectsActiveInOtherCampaigns = ignoreProspectsActiveInOtherCampaigns,
  //                    charsetDetected = None,
  //                    parserName = None,
  //                    parserContext = None,
  //                    uploadResult = None,
  //                    error = Some(e.getMessage)
  //                  )
  //
  //                  Left(UploadCSVError.UploadCSVOrParseFailed(errorMsg))
  //
  //
  //                case Success((uploadResult, parserResult)) =>
  //                  val data = uploadResult
  //
  //                  Logger.info(s"uploaded directly: fileName: $fileName :: results: $data")
  //
  //                  prospectUploadService.csvDirectSaveUploadToS3SaveInDB(
  //                    file = csv.ref.toFile,
  //                    fileName = fileName,
  //                    Logger = Logger,
  //                    ownerTeamMember = foundOwnerTeamMember,
  //                    loggedInAccountId = doer.id,
  //                    campaignId = campaignId,
  //                    prospectTags = tagsFromInput,
  //                    mappingFromClient = mappingFromClient,
  //                    forceChangeProspectsOwnershipToMe = forceChangeProspectsOwnershipToMe,
  //                    forceUpdateProspects = forceUpdateProspects,
  //                    ignoreProspectsActiveInOtherCampaigns = ignoreProspectsActiveInOtherCampaigns,
  //                    charsetDetected = parserResult.charsetDetected,
  //                    parserName = Some(parserResult.parserName),
  //                    parserContext = parserResult.parserContext,
  //                    uploadResult = Some(Json.toJson(data)),
  //                    error = None
  //                  )
  //
  //                  Right(data)
  //              }
  //
  //          }
  //        }
  //
  //    }
  //  }

  def findDuplicateProspectsForMultiRowInsert(
                                               prospects: Seq[ProspectCreateFormData],
                                               teamId: Long,
                                               orgId: Long,
//                                               emailNotCompulsoryEnabled: Boolean
                                             )(using Logger: SRLogger): Try[List[DuplicateProspectResult]] = {


//    if (emailNotCompulsoryEnabled) {

      for {

        duplicateProspectsForEmailPhoneLinkedin <- {
          val emailSeq = prospects.filter(p => p.email.isDefined).map(_.email.get)
          val phoneSeq = prospects.filter(p => p.phone.isDefined).map(_.phone.get)
          val linkedinSeq = prospects.filter(p => p.linkedin_url.isDefined).map(p => LinkedinHelperFunctions.normalizeLinkedInURL(p.linkedin_url).get)
          if (emailSeq.isEmpty || phoneSeq.isEmpty || linkedinSeq.isEmpty) {
            Success(List())
          } else {
            prospectDAOService.findDuplicateProspectsForForceUpdate(
              duplicationFindProspectData = Seq(
                DuplicationFindProspectData(
                  data = emailSeq,
                  columnType = SrProspectColumns.Email
                ),
                DuplicationFindProspectData(
                  data = phoneSeq,
                  columnType = SrProspectColumns.Phone
                ),
                DuplicationFindProspectData(
                  data = linkedinSeq,
                  columnType = SrProspectColumns.LinkedinUrl
                )
              ),
              teamId = TeamId(teamId)
            )
          }
        }

        duplicateProspectsForEmailLinkedin <- {
          val emailSeq = prospects.filter(p => p.phone.isEmpty && p.email.isDefined).map(_.email.get)
          val linkedinSeq = prospects.filter(p => p.phone.isEmpty && p.linkedin_url.isDefined).map(p => LinkedinHelperFunctions.normalizeLinkedInURL(p.linkedin_url).get)
          if (emailSeq.isEmpty || linkedinSeq.isEmpty) {
            Success(List())
          } else {
            prospectDAOService.findDuplicateProspectsForForceUpdate(
              duplicationFindProspectData = Seq(
                DuplicationFindProspectData(
                  data = emailSeq,
                  columnType = SrProspectColumns.Email
                ),
                DuplicationFindProspectData(
                  data = linkedinSeq,
                  columnType = SrProspectColumns.LinkedinUrl
                )
              ),
              teamId = TeamId(teamId)
            )
          }
        }

        duplicateProspectsForEmailPhone <- {
          val emailSeq = prospects.filter(p => p.linkedin_url.isEmpty && p.email.isDefined).map(_.email.get)
          val phoneSeq = prospects.filter(p => p.linkedin_url.isEmpty && p.phone.isDefined).map(_.phone.get)
          if (emailSeq.isEmpty || phoneSeq.isEmpty) {
            Success(List())
          } else {
            prospectDAOService.findDuplicateProspectsForForceUpdate(
              duplicationFindProspectData = Seq(
                DuplicationFindProspectData(
                  data = emailSeq,
                  columnType = SrProspectColumns.Email
                ),
                DuplicationFindProspectData(
                  data = phoneSeq,
                  columnType = SrProspectColumns.Phone
                )
              ),
              teamId = TeamId(teamId)
            )
          }
        }

        duplicateProspectsForPhoneLinkedin <- {
          val phoneSeq = prospects.filter(p => p.email.isEmpty && p.phone.isDefined).map(_.phone.get)
          val linkedinSeq = prospects.filter(p => p.email.isEmpty && p.linkedin_url.isDefined).map(p => LinkedinHelperFunctions.normalizeLinkedInURL(p.linkedin_url).get)
          if (phoneSeq.isEmpty || linkedinSeq.isEmpty) {
            Success(List())
          } else {
            prospectDAOService.findDuplicateProspectsForForceUpdate(
              duplicationFindProspectData = Seq(
                DuplicationFindProspectData(
                  data = phoneSeq,
                  columnType = SrProspectColumns.Phone
                ),
                DuplicationFindProspectData(
                  data = linkedinSeq,
                  columnType = SrProspectColumns.LinkedinUrl
                )
              ),
              teamId = TeamId(teamId)
            )
          }
        }

        duplicateProspectsForEmail <- {
          val emailSeq = prospects.filter(p => p.phone.isEmpty && p.linkedin_url.isEmpty && p.email.isDefined).map(_.email.get)
          if (emailSeq.isEmpty) {
            Success(List())
          } else {
            prospectDAOService.findDuplicateProspectsForForceUpdate(
              duplicationFindProspectData = Seq(
                DuplicationFindProspectData(
                  data = emailSeq,
                  columnType = SrProspectColumns.Email
                )
              ),
              teamId = TeamId(teamId)
            )
          }
        }

        duplicateProspectsForPhone: List[DuplicateProspectResult] <- {
          val phoneSeq = prospects.filter(p => p.email.isEmpty && p.linkedin_url.isEmpty && p.phone.isDefined).map(_.phone.get)
          if (phoneSeq.isEmpty) {
            Success(List())
          } else {
            prospectDAOService.findDuplicateProspectsForForceUpdate(
              duplicationFindProspectData = Seq(
                DuplicationFindProspectData(
                  data = phoneSeq,
                  columnType = SrProspectColumns.Phone
                )
              ),
              teamId = TeamId(teamId)
            )
          }
        }

        duplicateProspectsForLinkedin: List[DuplicateProspectResult] <- {
          val linkedinSeq = prospects.filter(p => p.phone.isEmpty && p.email.isEmpty && p.linkedin_url.isDefined).map(p => LinkedinHelperFunctions.normalizeLinkedInURL(p.linkedin_url).get)
          if (linkedinSeq.isEmpty) {
            Success(List())
          } else {
            prospectDAOService.findDuplicateProspectsForForceUpdate(
              duplicationFindProspectData = Seq(
                DuplicationFindProspectData(
                  data = linkedinSeq,
                  columnType = SrProspectColumns.LinkedinUrl
                )
              ),
              teamId = TeamId(teamId)
            )
          }
        }

        duplicateProspectsForCompanyFirstNameLastName: List[DuplicateProspectResult] <- {
          val companyFirstNameLastName = prospects.filter(p => p.email.isEmpty && p.phone.isEmpty && p.linkedin_url.isEmpty && (p.company.isDefined && p.first_name.isDefined && p.last_name.isDefined)).map(p => p.company.get + p.first_name.get + p.last_name.get)
          if (companyFirstNameLastName.isEmpty) {
            Success(List())
          } else {
            prospectDAOService.findDuplicateProspectsForForceUpdate(
              duplicationFindProspectData = Seq(
                DuplicationFindProspectData(
                  data = companyFirstNameLastName,
                  columnType = SrProspectColumns.CompanyFirstnameLastname
                )
              ),
              teamId = TeamId(teamId)
            )
          }
        }
      } yield {
        duplicateProspectsForEmailPhoneLinkedin ++ duplicateProspectsForEmailLinkedin ++ duplicateProspectsForEmailPhone ++ duplicateProspectsForPhoneLinkedin ++ duplicateProspectsForEmail ++ duplicateProspectsForPhone ++ duplicateProspectsForLinkedin ++ duplicateProspectsForCompanyFirstNameLastName
      }

//    } else {
//      prospectsEmailsDAO.findDuplicateProspectsV2(
//        Logger = Logger,
//        emails = prospects.filter(_.email.isDefined).map(_.email.get.toLowerCase.trim).toSeq.distinct,
//        teamId = teamId
//      )
//    }
  }

  /**
   * Find duplicates for deduplicationColumns separately
   * NOTE: Using existing functions here.
   * Later on we can create new function to select all duplicates at a time
   */
  //fixme: remove these old flow. not used anywhere
  def findDuplicateProspectsFromDbForSelectedColumns(
                                                      prospects: Seq[ProspectCreateFormData],
                                                      teamId: Long,
                                                      orgId: Long,
                                                      deduplicationColumns: Seq[SrProspectColumns],
//                                                      emailNotCompulsoryEnabled: Boolean
                                                    )(using Logger: SRLogger): Try[List[DuplicateProspectResult]] = {
//    if (emailNotCompulsoryEnabled) {
      val emailSeq = prospects.filter(p => p.email.isDefined)
        .map(_.email.get)

      val duplicationFindProspectDataEmail: Option[DuplicationFindProspectData] = if (emailSeq.isEmpty || !deduplicationColumns.contains(SrProspectColumns.Email)) {
        None
      } else {
        Some(DuplicationFindProspectData(
          data = emailSeq,
          columnType = SrProspectColumns.Email
        ))
      }

      val phoneSeq = prospects.filter(p => p.phone.isDefined)
        .map(_.phone.get)

      val duplicationFindProspectDataPhone: Option[DuplicationFindProspectData] = if (phoneSeq.isEmpty || !deduplicationColumns.contains(SrProspectColumns.Phone)) {
        None
      } else {
        Some(DuplicationFindProspectData(
          data = phoneSeq,
          columnType = SrProspectColumns.Phone
        ))
      }

      val linkedinSeq = prospects.filter(p => p.linkedin_url.isDefined)
        .map(p => LinkedinHelperFunctions.normalizeLinkedInURL(p.linkedin_url).get)

      val duplicationFindProspectDataLinkedin = if (linkedinSeq.isEmpty || !deduplicationColumns.contains(SrProspectColumns.LinkedinUrl)) {
        None
      } else {
        Some(DuplicationFindProspectData(
          data = linkedinSeq,
          columnType = SrProspectColumns.LinkedinUrl
        ))
      }

      val companyFirstNameLastNameSeq = prospects.filter(p => p.company.isDefined && p.first_name.isDefined && p.last_name.isDefined)
        .map(p => p.company.get.toLowerCase() + p.first_name.get.toLowerCase + p.last_name.get.toLowerCase) // in query we are using lower thats why converted to lower case 

      val duplicationFindProspectDataCompanyFirstnameLastname = if (companyFirstNameLastNameSeq.isEmpty || !deduplicationColumns.contains(SrProspectColumns.CompanyFirstnameLastname)) {
        None
      } else {
        Some(DuplicationFindProspectData(
          data = companyFirstNameLastNameSeq,
          columnType = SrProspectColumns.CompanyFirstnameLastname
        ))
      }

      var duplicationFindProspectDataCombined: Seq[DuplicationFindProspectData] = Seq()

      if (duplicationFindProspectDataEmail.isDefined) {
        duplicationFindProspectDataCombined = duplicationFindProspectDataCombined :+ duplicationFindProspectDataEmail.get
      }
      if (duplicationFindProspectDataPhone.isDefined) {
        duplicationFindProspectDataCombined = duplicationFindProspectDataCombined :+ duplicationFindProspectDataPhone.get
      }
      if (duplicationFindProspectDataLinkedin.isDefined) {
        duplicationFindProspectDataCombined = duplicationFindProspectDataCombined :+ duplicationFindProspectDataLinkedin.get
      }
      if (duplicationFindProspectDataCompanyFirstnameLastname.isDefined) {
        duplicationFindProspectDataCombined = duplicationFindProspectDataCombined :+ duplicationFindProspectDataCompanyFirstnameLastname.get
      }


      prospectDAOService.findDuplicateProspectsForForceUpdate(
        duplicationFindProspectData = duplicationFindProspectDataCombined,
        teamId = TeamId(teamId)
      )


//    } else {
//      prospectsEmailsDAO.findDuplicateProspectsV2(
//        Logger = Logger,
//        emails = prospects.filter(_.email.isDefined).map(_.email.get.toLowerCase.trim).toSeq.distinct,
//        teamId = teamId
//      )
//    }
  }

  /***
   * 27-Jan-25:
   * To find duplicate for each row in ProspectCreateFormData
   * Constructing this here Seq[Seq[DuplicationFindProspectDataV2]]
   *
   * Example
   * Seq(
   * Data(Some("<EMAIL>"), Some(*********)),
   * Data(Some(<EMAIL>), none)
   * )
   *
   * Seq(
   *  Seq(DuplicationFindProspectData(
   *  data = Seq("<EMAIL>"),
   *  columnType = SrProspectColumns.Email
   *  ),
   *  DuplicationFindProspectData(
   *  data = Seq("*********"),
   *  columnType = SrProspectColumns.Phone
   *  )
   *  ),
   *
   * Seq(DuplicationFindProspectData(
   *   data = Seq("<EMAIL>"),
   *  columnType = SrProspectColumns.Email
   *  )
   *)
   * )
   */

  def findDuplicateProspectsFromDbForSelectedColumnsV2(
                                                        prospects: Seq[ProspectCreateFormData],
                                                        teamId: Long,
                                                        orgId: Long,
                                                        deduplicationColumns: Seq[SrProspectColumns],
//                                                        emailNotCompulsoryEnabled: Boolean
                                                      )(using Logger: SRLogger): Try[List[DuplicateProspectResult]] = {

    //fixme: get rid of this flag
//    if (emailNotCompulsoryEnabled) {
      val res = prospects.map(p => {
        val duplicationFindProspectDataEmail: Option[DuplicationFindProspectDataV2] = if (p.email.isEmpty || !deduplicationColumns.contains(SrProspectColumns.Email)) {
          None
        } else {
          Some(DuplicationFindProspectDataV2(
            data = p.email.get.trim.toLowerCase,
            columnType = SrProspectColumns.Email
          ))
        }

        val duplicationFindProspectDataPhone: Option[DuplicationFindProspectDataV2] = if (p.phone.isEmpty || !deduplicationColumns.contains(SrProspectColumns.Phone)) {
          None
        } else {
          Some(DuplicationFindProspectDataV2(
            data = p.phone.get,
            columnType = SrProspectColumns.Phone
          ))
        }

        val duplicationFindProspectDataLinkedin: Option[DuplicationFindProspectDataV2] = if (p.linkedin_url.isEmpty || !deduplicationColumns.contains(SrProspectColumns.LinkedinUrl)) {
          None
        } else {
          Some(DuplicationFindProspectDataV2(
            data = p.linkedin_url.get,
            columnType = SrProspectColumns.LinkedinUrl
          ))
        }

        val companyFirstNameLastName = p.company.isDefined && p.first_name.isDefined && p.last_name.isDefined

        val duplicationFindProspectDataCompanyFirstnameLastname: Option[DuplicationFindProspectDataV2] = if (!companyFirstNameLastName || !deduplicationColumns.contains(SrProspectColumns.CompanyFirstnameLastname)) {
          None
        } else {

          Some(DuplicationFindProspectDataV2(
            data = p.company.get.toLowerCase() + p.first_name.get.toLowerCase + p.last_name.get.toLowerCase,
            columnType = SrProspectColumns.CompanyFirstnameLastname
          ))
        }


        var duplicationFindProspectDataCombined: Seq[DuplicationFindProspectDataV2] = Seq()

        if (duplicationFindProspectDataEmail.isDefined) {
          duplicationFindProspectDataCombined = duplicationFindProspectDataCombined :+ duplicationFindProspectDataEmail.get
        }
        if (duplicationFindProspectDataPhone.isDefined) {
          duplicationFindProspectDataCombined = duplicationFindProspectDataCombined :+ duplicationFindProspectDataPhone.get
        }
        if (duplicationFindProspectDataLinkedin.isDefined) {
          duplicationFindProspectDataCombined = duplicationFindProspectDataCombined :+ duplicationFindProspectDataLinkedin.get
        }
        if (duplicationFindProspectDataCompanyFirstnameLastname.isDefined) {
          duplicationFindProspectDataCombined = duplicationFindProspectDataCombined :+ duplicationFindProspectDataCompanyFirstnameLastname.get
        }

        duplicationFindProspectDataCombined
      })

      prospectDAOService.findDuplicateProspectsForForceUpdateV2(
        duplicationFindProspectData = res.filter(_.nonEmpty),
        teamId = TeamId(teamId)
      )


//    } else {
//      prospectsEmailsDAO.findDuplicateProspectsV2(
//        Logger = Logger,
//        emails = prospects.filter(_.email.isDefined).map(_.email.get.toLowerCase.trim).toSeq.distinct,
//        teamId = teamId
//      )
//    }
  }


  def getBlacklistedProspects(
                               logger: SRLogger,
                               teamId: Long,
                               prospectsAndAccountData: Iterable[ProspectData],
                               org_id: Long
                             ): Try[Seq[Blacklist]] = {
    //Fixme: currently only email blacklist is present and later on need to update logic for non-email blacklists
    val prospects = prospectsAndAccountData.filter(_.prospectCreateFormData.email.isDefined)
    blacklistProspectCheckDAO.findByEmailsAndDomains(
      logger = logger,
      teamId = teamId,
      orgId = OrgId(org_id),
      emails = prospects.map(_.prospectCreateFormData.email.get).toSeq,
      domains = prospects.filter(_.prospectAccountCreateFormData.isDefined).map(_.prospectAccountCreateFormData.get.name).toSeq // for now, email_domain is set as the ProspectAccount name
    )
  }

  /*
  23-apr-2024
    checked its being called from create or updateProspects flow
   */

  def constructProspectsToBeUpdated(
                                     duplicateProspectsFromDb: List[DuplicateProspectResult],
                                     permittedAccountIdsForEditingProspects: Seq[Long],
                                     pgroup: Iterable[ProspectData],
                                     ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads: Boolean,
                                     prospectCustomColumnDefs: Seq[ColumnDef],
                                     is_public_api: Boolean,
                                     Logger: SRLogger,
                                     org_id: Long,
//                                     emailNotCompulsoryEnabled: Boolean
                                   ): List[ProspectsToBeForceUpdated] = {

    val duplicateProspectsWithEditPermission: List[DuplicateProspectResult] = duplicateProspectsFromDb
      .filter(p => permittedAccountIdsForEditingProspects.contains(p.account_id.get))

//    if (emailNotCompulsoryEnabled) {

      /**
       * mapDeDuplicationColumnAndDuplicateProspect: is a map of deDuplicationColumn
       * value string(either email/phone/linkedin/company-firstname-lastname) and respective DuplicateProspectResult
       * e.g. ("linkedin.com/sales101" -> DuplicateProspectResult(
       * prospectCreateFormData(linkedin_url = "linkedin.com/sales101"),
       * deDuplicationColumnValue, deDuplicationColumnType, prospect_id, account_id))
       * In above map prospectCreateFormData object is selected from db and it's not from csv
       *
       * columnAndUserInputMap: is a map of deDuplicationColumn and prospectCreateFormData which is from csv
       *
       * Based on matching deDuplicationColumn value of above maps we will create pair of
       * ProspectCreateFormData from columnAndUserInputMap and prospect_id from mapDeDuplicationColumnAndDuplicateProspect
       *
       * This response will be used to forceUpdate the prospects in db 
       */

      val mapDeDuplicationColumnAndDuplicateProspect: Map[String, DuplicateProspectResult] = duplicateProspectsWithEditPermission
        .flatMap(data => data.deDuplicateColumnAndValue.map(d => d.deDuplicationColumnValue.trim.toLowerCase -> data).toMap)
        .toMap

      val columnAndUserInputMap: Map[String, ProspectCreateFormData] = pgroup.flatMap(p => {
        val prospectUserInput = p.prospectCreateFormData
        var columnAndUserInputList: List[(String, ProspectCreateFormData)] = List();
        if (prospectUserInput.email.isDefined) {
          columnAndUserInputList = columnAndUserInputList :+ (prospectUserInput.email.get.trim.toLowerCase -> prospectUserInput)
        }
        if (prospectUserInput.phone.isDefined) {
          columnAndUserInputList = columnAndUserInputList :+ (prospectUserInput.phone.get -> prospectUserInput)
        }
        val url = LinkedinHelperFunctions.normalizeLinkedInURL(prospectUserInput.linkedin_url.map(_.trim.toLowerCase))
        if (url.isDefined) {
          columnAndUserInputList = columnAndUserInputList :+ (url.get) -> prospectUserInput
        } else if (prospectUserInput.linkedin_url.isDefined) {
          Logger.error(s"for prospect: ${prospectUserInput.email.getOrElse("")} , linkedin_url sent as ${prospectUserInput.linkedin_url} which is invalid. so, Ignoring this field.")
        }
        if (prospectUserInput.company.isDefined && prospectUserInput.first_name.isDefined && prospectUserInput.last_name.isDefined) {
          //here we are always assuming that if above 3 are empty the these are always defined
          //fixme: we have to pass SrProspectColumn type  ro ProspectCreateFormData itself after parsing csv
          //todo: this has to be extracted out as a utility method to prevent code duplication
          val company_firstname_lastname = prospectUserInput.company.get.toLowerCase + prospectUserInput.first_name.get.toLowerCase ++ prospectUserInput.last_name.get.toLowerCase
          columnAndUserInputList = columnAndUserInputList :+ (company_firstname_lastname -> prospectUserInput)
        }
        columnAndUserInputList
      }).toMap

      mapDeDuplicationColumnAndDuplicateProspect.map(dup => {
        val (email, duplicateProspectFromDb) = dup
        ProspectsToBeForceUpdated(
          prospect = columnAndUserInputMap.apply(email.trim.toLowerCase),
          prospect_id = duplicateProspectFromDb.prospect_id,
          deDuplicationColumnTypes = duplicateProspectFromDb.deDuplicateColumnAndValue.map(d => d.deDuplicationColumnType)
        )
      }).toList

//    } else { TODO:: ADD in the ENC flow too
//
//      //2.2 extract prospects data from pgroup and add prospect_id
//      val mapEmailPgroup: Map[String, ProspectData] =
//        pgroup.map(data => data.prospectCreateFormData.email.get.trim.toLowerCase -> data).toMap
//
//      val mapEmailDuplicateProspect = duplicateProspectsWithEditPermission.flatMap(data => data.deDuplicateColumnAndValue.map(d => d.deDuplicationColumnValue.trim.toLowerCase -> data).toMap).toMap
//
//      //Below filter will ignore prospect update if
//      //prospect's user input is exactly matching the existing data in db for prospects
//      //It also checks if custom fields are matching
//      mapEmailDuplicateProspect.map(p => {
//          val (email, duplicateProspectFromDb) = p
//          (mapEmailPgroup.apply(email.trim.toLowerCase).prospectCreateFormData, duplicateProspectFromDb)
//        }).toList
//        .filter { case (prospectFromUserInput, duplicateProspectFromDatabase) =>
//
//          val (matching, notMatchingFields) = ProspectService.prospectFromUserInputIsMatchingProspectInDb(
//            ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads,
//            prospectFromUserInput = prospectFromUserInput,
//            duplicateProspectFromDatabase = duplicateProspectFromDatabase,
//            prospectCustomColumnDefs = prospectCustomColumnDefs,
//          )
//
//          if (matching) {
//
//            Logger.debug(s"prospect.update ignored because prospectFromUserInputIsMatchingProspectInDb: prospectFromUserInput: ${Json.toJson(prospectFromUserInput)} :: duplicateProspectFromDatabase: ${Json.toJson(duplicateProspectFromDatabase.prospectCreateFormData)} :: prospectCustomColumnDefs: ${prospectCustomColumnDefs.map(_.name)}")
//
//          } else {
//
//            Logger.debug(s"prospect.update NOT ignored because prospectFromUserInputIsMatchingProspectInDb: notMatchingFields: ${notMatchingFields} :: prospectFromUserInput: ${Json.toJson(prospectFromUserInput)} :: duplicateProspectFromDatabase: ${Json.toJson(duplicateProspectFromDatabase.prospectCreateFormData)} :: prospectCustomColumnDefs: ${prospectCustomColumnDefs.map(_.name)}")
//
//          }
//
//          //for public api we want all duplicate records to be force updated
//          if (is_public_api) true else !matching
//        }
//        .map { case (prospectFromUserInput, duplicateProspectFromDatabase) =>
//
//          ProspectsToBeForceUpdated(
//            prospect = prospectFromUserInput,
//            prospect_id = duplicateProspectFromDatabase.prospect_id,
//            deDuplicationColumnTypes = Seq(SrProspectColumns.Email)
//          )
//        }
//    }
  }

  def forceUpdateProspectsForMultiRowInsert(
                                             ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads: Boolean,
                                             prospectCustomColumnDefs: Seq[ColumnDef],
                                             listIdOpt: Option[Long],
                                             prospectsToBeUpdated: Iterable[ProspectsToBeForceUpdated],
                                             deduplicationColumns: Seq[SrProspectColumns],
                                             teamId: Long,
                                             org_id: Long,
//                                             emailNotCompulsoryEnabled: Boolean
                                           )(using Logger: SRLogger): Try[List[InsertOrUpdateProspectResult]] = {
//    if (emailNotCompulsoryEnabled) {

      var newProspectEmailsTobeAdded: List[ProspectEmailsTobeAddedInForceUpdateProspect] = List()

      /**
       * if deduplicationColumns is not having emails then we have to update emails data
       * since it is in prospects_emails table
       *  First we fetch all emails of prospect and filter out emails which are new
       *  Pass new email to insert in prospects_emails table
       */
      if(!deduplicationColumns.contains(SrProspectColumns.Email)) {
        prospectDAOService.getEmailDataForProspectIds(
          prospectIds = prospectsToBeUpdated.map(p => ProspectId(p.prospect_id)).toSeq,
          teamId = TeamId(teamId)
        ) match {
          case Failure(exception) => Logger.shouldNeverHappen("Failed to fetch email data for force update email")

          case Success(pemails) =>

            val newProspectEmails: List[ProspectEmailsTobeAddedInForceUpdateProspect] = ProspectService.findNewProspectEmailsDataForForceUpdate(
              prospectsToBeUpdated = prospectsToBeUpdated,
              prospectEmailData = pemails
            )

            newProspectEmailsTobeAdded = newProspectEmails
        }
      }

      //updating prospect data other than Deduplication columns
      prospectDAOService.updateProspectsForEmailOptional(
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads,
        prospectCustomColumnDefs = prospectCustomColumnDefs,
        listIdOpt = listIdOpt,
        prospectsToBeUpdated = prospectsToBeUpdated,
        newProspectEmailsTobeAdded = newProspectEmailsTobeAdded,
        deduplicationColumns = deduplicationColumns,
        teamId = teamId
      )
//    } else {
//      prospectDAOService.updateProspects(
//        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads,
//        prospectCustomColumnDefs = prospectCustomColumnDefs,
//        listIdOpt = listIdOpt,
//        prospectsToBeUpdated = prospectsToBeUpdated,
//        teamId = teamId
//      )
//    }
  }

  /**
   * We are filtering out duplicates from pgroup(user's prospects input list)
   * to insert new prospect in db
   */
  /*
  23-april-2024:

   check this : checked this flow is called from createOrUpdateProspects so linkedin_urls would be fixed
   */
  def constructProspectsToBeInserted(
                                      pgroup: Iterable[ProspectData],
                                      duplicateProspectsFromDb: List[DuplicateProspectResult],
                                      deduplicationColumns: Seq[SrProspectColumns],   //selected by user
                                      org_id: Long,
//                                      emailNotCompulsoryEnabled: Boolean
                                    ): Iterable[NewProspectsToBeAdded] = {
//    if (emailNotCompulsoryEnabled) {
      pgroup.filterNot(p => {

        val deduplicationColumnTypeAndValues: List[DeDuplicateColumnTypeAndValue] = duplicateProspectsFromDb.
          flatMap(_.deDuplicateColumnAndValue)

        val deDuplicationColumnValuesEmail: List[String] = deduplicationColumnTypeAndValues
          .filter(_.deDuplicationColumnType == SrProspectColumns.Email)
          .map(_.deDuplicationColumnValue)

        val deDuplicationColumnValuesPhone: List[String] = deduplicationColumnTypeAndValues
          .filter(_.deDuplicationColumnType == SrProspectColumns.Phone)
          .map(_.deDuplicationColumnValue)

        val deDuplicationColumnValuesLinkedin: List[String] = deduplicationColumnTypeAndValues
          .filter(_.deDuplicationColumnType == SrProspectColumns.LinkedinUrl)
          .map(_.deDuplicationColumnValue)

        val deDuplicationColumnValuesCompanyFirstAndLastNames: List[String] = deduplicationColumnTypeAndValues
          .filter(_.deDuplicationColumnType == SrProspectColumns.CompanyFirstnameLastname)
          .map(_.deDuplicationColumnValue)


        val prospect = p.prospectCreateFormData

        //todo: extract if else in other function
        if (prospect.email.isDefined && deduplicationColumns.contains(SrProspectColumns.Email)) {
          //Fixme: we need to clean up the data when we are reading it from the user. LowercasedTrimmedEmail - value class can be used to read and dump it there.
          deDuplicationColumnValuesEmail.contains(p.prospectCreateFormData.email.get.trim.toLowerCase)
        } else if (prospect.phone.isDefined && deduplicationColumns.contains(SrProspectColumns.Phone)) {

          deDuplicationColumnValuesPhone.contains(p.prospectCreateFormData.phone.get.trim.toLowerCase)

        } else if (prospect.linkedin_url.isDefined && deduplicationColumns.contains(SrProspectColumns.LinkedinUrl)) {

          deDuplicationColumnValuesLinkedin.contains(LinkedinHelperFunctions.normalizeLinkedInURL(p.prospectCreateFormData.linkedin_url.map(_.trim.toLowerCase)).get)

        } else if (prospect.company.isDefined &&
          prospect.first_name.isDefined &&
          prospect.last_name.isDefined &&
          deduplicationColumns.contains(SrProspectColumns.CompanyFirstnameLastname)) {

          val checkContains = prospect.company.get + prospect.first_name.get + prospect.last_name.get
          deDuplicationColumnValuesCompanyFirstAndLastNames.contains(checkContains)

        } else {
          false
        }

      }).map(p => {
        NewProspectsToBeAdded(
          prospectAccountCreateFormData = p.prospectAccountCreateFormData,
          prospectCreateFormData = p.prospectCreateFormData,
          prospect_uuid = srUuidUtils.generateProspectUuid()
        )
      })
//    } else {
//      pgroup.filterNot(p => {
//        duplicateProspectsFromDb.flatMap(_.deDuplicateColumnAndValue.map(_.deDuplicationColumnValue)).contains(p.prospectCreateFormData.email.get.trim.toLowerCase)
//      }).map(p => {
//        NewProspectsToBeAdded(
//          prospectAccountCreateFormData = p.prospectAccountCreateFormData,
//          prospectCreateFormData = p.prospectCreateFormData,
//          prospect_uuid = srUuidUtils.generateProspectUuid()
//        )
//      })
//    }
  }

  // TEAMEDITION check: FIXME issue with findOrCreateList
  // is_updated_internal column is to segregate between inserted (new) & updated prospects,
  // so that the Triggers can be correctly called for new prospects created vs prospects updated
  /*
   * has impact on prospects_emails
   */
  /*
    * PROSPECTS_EMAILS_TODO_INSERT / PROSPECTS_EMAILS_TODO_UPDATE
   * Use cases:
   * 1. Use case : ProspectController.createOrUpdateOne - end-user uploads a single prospect via the form
   * 2. Use case : Adding prospects to the system via API call shared with customers.
   * 3. Use case : CREATE__createFromUploadMultiRowInsertV2_New_OR_UPDATE_PROSPECT_IN_SMARTREACH
   * 4. Use case : CSV uploaded prospects are inserted into prospects table via UploadCsvCronService
   */
  //TODO: revisit this function to remove .get by creating a new case class without option email - after purifying the list
  def __createFromUploadMultiRowInsertNew(
                                           ownerAccountId: Long,
                                           teamId: Long,
                                           taId: Long,
                                           listName: Option[String],
                                           prospects: Seq[ProspectCreateFormData],
                                           deduplicationColumns: Seq[SrProspectColumns],
                                           updateProspect: UpdateProspectType,
                                           ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads: Boolean,
                                           doerAccount: Account,
                                           prospectSource: Option[ProspectSource.Value],
                                           prospectAccountId: Option[Long],
                                           permittedAccountIdsForEditingProspects: Seq[Long],
                                           permittedAccountIdsForEditingProspectAccounts: Seq[Long],
                                           is_public_api: Boolean,
                                           triggerSource: TriggerSource,
                                           batchInsertLimit: Int = 100,
                                           Logger: SRLogger
                                         ): Try[UpsertSQLResult] = Try {

    given srLogger: SRLogger = Logger // fixme given

    if (prospects.isEmpty) {
      UpsertSQLResult(
        createdProspectEmails = Seq(),
        createdProspectIds = Seq(),
        updatedIds = Seq(),
        duplicateProspectsData = Seq(),
        duplicatesIgnoredEmailsBecauseOfNoEditPermission = Seq(),
        duplicateProspectIdsWithEditPermission = Seq(),
        totalDNCProspects = 0,
        prospectDataForApi = List()
      )
    } else {

      val org_id = doerAccount.org.id

      val prospectOwnerAccountId = ownerAccountId
      val prospectAddedByAccountId = doerAccount.internal_id

      val prospectCustomColumnDefs = prospectColumnDef.findCustomColumns(teamId = teamId).filter(_.is_custom)

      // update Linkedin urls of prospects /
//      val emailNotCompulsoryEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        teamId = TeamId(teamId),
//        feature = SrRollingUpdateFeature.EmailNotCompulsory
//      )(Logger)

      val uniqueProspects: Iterable[ProspectCreateFormData] = extractUniqueProspectsForMultiRowInsert(
        prospects = prospects,
        orgId = org_id,
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
      )

      val uniqueProspectAndAccountData: Iterable[ProspectData] = constructProspectAccountData(
        prospects = uniqueProspects,
        org_id = org_id,
//        emailNotCompulsaryEnabled = emailNotCompulsoryEnabled
      )

      prospectDAOService.getProspectCategoryId(
        teamId = TeamId(id = teamId),
        text_id = ProspectCategory.DO_NOT_CONTACT,
        account = Some(doerAccount)
      ) match {

        case Failure(err) => {

          Logger.fatal(s"Error Occurred getProspectCategoryId DO_NOT_CONTACT", err = err)
          throw err
        }

        case Success(do_not_contact_category_id: ProspectCategoryId) =>

          prospectDAOService.getProspectCategoryId(
            teamId = TeamId(id = teamId),
            text_id = ProspectCategory.NOT_CATEGORIZED,
            account = Some(doerAccount)
          ) match {

            case Failure(err) => {
              Logger.fatal(s"Error Occurred getProspectCategoryId NOT_CATEGORIZED", err = err)

              throw err
            }

            case Success(not_categorized_category_id) =>

              // if listName is there, find or create list and return list's id
              // DONE: check this permittedAccountIds case: in All View, if user adding prospects issue
              val listIdOpt: Option[Long] = prospectDAOService.findOrCreateList(
                listName = listName,
                permittedAccountIds = permittedAccountIdsForEditingProspects,
                listOwnerAccountId = ownerAccountId,
                teamId = teamId
              )

              // if account is there, find or create account and return account Id and name
              val prospectAccounts: Seq[ProspectAccountIdAndCustomId] = if (prospectAccountId.isDefined) {
                val pa = prospectAccountDAO1.find(id = prospectAccountId.get, teamId = teamId)
                if (pa.nonEmpty) {
                  Seq(ProspectAccountIdAndCustomId(
                    id = pa.get.id,
                    custom_id = pa.get.custom_id.trim.toLowerCase,
                    is_updated_internal = false
                  ))
                } else {
                  throw new Exception("Invalid account.")
                }
              } else {
                val paRes = prospectAccountDAO1.findOrCreateProspectAccount(
                  prospectAccountstoCreate = uniqueProspectAndAccountData
                    .filter(_.prospectAccountCreateFormData.isDefined)
                    .map(_.prospectAccountCreateFormData.get)
                    .toSeq
                    .groupBy(_.name)
                    .map(_._2.head)
                    .toSeq,
                  permittedAccountIdsForEditingProspectAccounts = permittedAccountIdsForEditingProspectAccounts,
                  ownerAccountId = ownerAccountId,
                  Logger = Logger,
                  teamId = teamId
                )

                (paRes.prospectAccountsCreated ++ paRes.prospectAccountsUpdated)
              }

              val sqlResult = uniqueProspectAndAccountData
                .grouped(batchInsertLimit)
                .toList
                .map(pgroup => {

                  val blacklist: Seq[Blacklist] = getBlacklistedProspects(
                    logger = Logger,
                    teamId = teamId,
                    prospectsAndAccountData = pgroup,
                    org_id = org_id
                  ).get

                  /** Separate the prospectsBatch in 2 sets
                   * 1. Existing Prospects to be updated
                   * 2. New Prospects to be inserted
                   */

                  //1. Find Existing Prospects

                  // (prospectCreateFormData, email, prospect_id, Some(account_id))
                  val duplicateProspectsFromDb: List[DuplicateProspectResult] = findDuplicateProspectsFromDbForSelectedColumnsV2(
                    prospects = pgroup.map(_.prospectCreateFormData).toSeq,
                    teamId = teamId,
                    orgId = org_id,
                    deduplicationColumns = deduplicationColumns,
//                    emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
                  ) match {
                    case Failure(exception) =>
                      Logger.error("Error while finding duplicate Prospects findDuplicateProspectsFromDbForSelectedColumns", exception)
                      throw exception

                    case Success(res) => {
                      if (teamId == 22382L && triggerSource == TriggerSource.CRM) {
                        Logger.debug(msg = s"teamId: 22382L and triggerPath: CRM possible we make it None here ${res.map(_.prospectCreateFormData.linkedin_url)} ")
                      }
                      res
                    }
                  }

                  val duplicateProspectsData = duplicateProspectsFromDb
                  val duplicateProspectEmailsWithNoEditPermission: List[Seq[DeDuplicateColumnTypeAndValue]] = duplicateProspectsFromDb
                    .filterNot(p => permittedAccountIdsForEditingProspects.contains(p.account_id.get))
                    .map(_.deDuplicateColumnAndValue)

                  if (teamId == 22382L && triggerSource == TriggerSource.CRM) {
                    Logger.debug(msg = s"teamId: 22382L and triggerPath: CRM  duplicateProspectEmailsWithNoEditPermission ${duplicateProspectEmailsWithNoEditPermission} ")
                  }

                  val duplicateProspectIdsWithEditPermission: List[Long] = duplicateProspectsFromDb
                    .filter(p => permittedAccountIdsForEditingProspects.contains(p.account_id.get))
                    .map(_.prospect_id)

                  if (teamId == 22382L && triggerSource == TriggerSource.CRM) {
                    Logger.debug(msg = s"teamId: 22382L and triggerPath: CRM  duplicateProspectIdsWithEditPermission ${duplicateProspectIdsWithEditPermission} ")
                  }

                  //2. Based on forceUpdateProspects condition update the prospects data in prospects table
                  //2.1 If forceUpdateProspects: ignore duplicate prospects where the user doesn't have edit-permission
                  //instead of this we can also pass all duplicate prospects with permittedAccountIdsForEditingProspects

                  val prospectsToBeUpdated: List[ProspectsToBeForceUpdated] = constructProspectsToBeUpdated(
                    duplicateProspectsFromDb = duplicateProspectsFromDb,
                    permittedAccountIdsForEditingProspects = permittedAccountIdsForEditingProspects,
                    pgroup = pgroup,
                    ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads,
                    prospectCustomColumnDefs = prospectCustomColumnDefs,
                    is_public_api = is_public_api,
                    Logger = Logger,
                    org_id = org_id,
//                    emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
                  ).distinct

                  if (teamId == 22382L && triggerSource == TriggerSource.CRM) {
                    Logger.debug(msg = s"teamId: 22382L and triggerPath: CRM  prospectsToBeUpdated ${prospectsToBeUpdated.map(_.prospect.linkedin_url)} ")
                  }

                  //To update the duplicate prospects we need prospect_id and the ProspectCreateFormdata
                  // prospect_id is in DuplicateProspectWithEditPermission
                  // ProspectCreateFormdata is in pgroup
                  // email is the common field to map
                  //2.3 update prospects if forceUpdateProspects
                  var updated_ids: List[Long] = List()
                  var updated_prospects: List[InsertOrUpdateProspectResult] = List()
                  updateProspect match {
                    case UpdateProspectType.ForceUpdate =>
                      forceUpdateProspectsForMultiRowInsert(
                        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads,
                        prospectCustomColumnDefs = prospectCustomColumnDefs,
                        listIdOpt = listIdOpt,
                        prospectsToBeUpdated = prospectsToBeUpdated,
                        deduplicationColumns = deduplicationColumns,
                        teamId = teamId,
                        org_id = org_id,
//                        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
                      ) match {
                        case Failure(exception) =>
                          Logger.error(s"prospectDAO.updateProspects failed: ", exception)
                          throw exception

                        case Success(result) =>
                          updated_ids = result.map(_.prospect_id)
                          updated_prospects = result
                      }

                    case UpdateProspectType.None =>
                    // this will always be empty so that user have option of not updating the the prospect with any of the type
                  }

                  //3. Insert prospect's data in prospects table and email in prospects_emails

                  //3.1 Filter new prospects from pgroup by removing duplicate and map to add temp_prospect_id
                  val newProspectsToBeAdded: Iterable[NewProspectsToBeAdded] = constructProspectsToBeInserted(
                    pgroup = pgroup,
                    duplicateProspectsFromDb = duplicateProspectsFromDb,
                    deduplicationColumns = deduplicationColumns,
                    org_id = org_id,
//                    emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
                  )

                  if (teamId == 22382L && triggerSource == TriggerSource.CRM) {
                    Logger.debug(msg = s"teamId: 22382L and triggerPath: CRM  newProspectsToBeAdded ${newProspectsToBeAdded.map(_.prospectCreateFormData.linkedin_url)} ")
                  }

                  if (prospectSource.isDefined && (
                    prospectSource.get.toString == ProspectSource.ZOHO.toString || prospectSource.get.toString == ProspectSource.HUBSPOT.toString
                    )) {
                    Logger.debug(s"zoho/hubspot syncProspects: newProspectsToBeAdded: ${newProspectsToBeAdded}")
                  }

                  val findBlacklistedProspects: Seq[BlacklistStatusForProspectUuid] = ProspectService.findProspectsInBlacklistForMultiRowInsert(
                    blacklist = blacklist,
                    newProspects = newProspectsToBeAdded.toSeq
                  )

                  val totalBlacklistsInNewProspects: Int = findBlacklistedProspects.count(_.isInBlacklist)

                  //3.2 Insert Prospects Data in prospects table
                  val insertedProspects: List[InsertOrUpdateProspectResult] = prospectDAOService.insertNewProspects(
                    blacklistStatusForProspectUuid = findBlacklistedProspects,
                    do_not_contact_category_id = do_not_contact_category_id.id,
                    not_categorized_category_id = not_categorized_category_id.id,
                    prospectSource = prospectSource,
                    prospectAccountId = prospectAccountId,
                    prospectAccounts = prospectAccounts,
                    newProspectsToBeAdded = newProspectsToBeAdded,
                    ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads,
                    prospectCustomColumnDefs = prospectCustomColumnDefs,
                    prospectOwnerAccountId = prospectOwnerAccountId,
                    prospectAddedByAccountId = prospectAddedByAccountId,
                    triggerSource = triggerSource,
                    teamId = teamId,
                    taId = taId,
                    listIdOpt = listIdOpt,
                    Logger = Logger
                  ) match {
                    case Failure(exception) =>
                      Logger.error(s"prospectDAO.insertNewProspects failed: ", exception)
                      throw exception
                    case Success(result) => result
                  }

                  //3.3 Find prospects emails to be inserted in prospects_emails table with the help of temp_prospect_id
                  val mapTempPidNewProspectsToBeAdded: Map[String, NewProspectsToBeAdded] = newProspectsToBeAdded.map(data => data.prospect_uuid -> data).toMap

                  //NOTE: prospect_uuid.get because inserted prospects will always return uuid
                  // and after migration it's going to be non-option
                  val mapTempPidInsertedProspects: Map[String, InsertOrUpdateProspectResult] = insertedProspects.map(data => data.prospect_uuid.get -> data).toMap

                  val prospects_emails_tobeAdded: Map[NewProspectsToBeAdded, Long] = mapTempPidNewProspectsToBeAdded.map(p => {
                    (p._2, mapTempPidInsertedProspects.apply(p._1).prospect_id)
                  }).filter(_._1.prospectCreateFormData.email.isDefined)

                  //3.4 insert prospects_emails if prospects_emails_tobeAdded non_empty

                  val upsertResult = if (prospects_emails_tobeAdded.nonEmpty) {

                    prospectsEmailsDAO.__createFromUploadMultiRowInsertV2_New(
                      prospects_emails_tobeAdded = prospects_emails_tobeAdded,
                      prospectOwnerAccountId = prospectOwnerAccountId,
                      Logger = Logger,
                      teamId = teamId
                    ) match {

                      case Failure(exception) =>
                        Logger.error(s"prospectsEmailsDAO.__createFromUploadMultiRowInsertV2_New failed: ", exception)
                        throw exception

                      case Success(prospectEmailsCreatedResult) =>
                        val domains: Set[String] = prospectEmailsCreatedResult.map { prospect =>
                          val (_, emailDomain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(prospect.email)
                          emailDomain
                        }.toSet
                        domains.map { emailDomain =>

                          mqDomainServiceProviderDNSService.publish(emailDomain) match {
                            case Success(value) => //DO NOTHING
                            case Failure(exception) => Logger.fatal(s"failed to push to mqDomainServiceProviderDNSService", exception)
                          }
                        }

                        val groupedResult = UpsertSQLResult(
                          createdProspectEmails = prospectEmailsCreatedResult,
                          createdProspectIds = insertedProspects.map(_.prospect_id),
                          updatedIds = updated_ids,
                          duplicateProspectsData = duplicateProspectsData,
                          duplicatesIgnoredEmailsBecauseOfNoEditPermission = duplicateProspectEmailsWithNoEditPermission.flatMap(p => p.filter(p => p.deDuplicationColumnType == SrProspectColumns.Email).map(v => v.deDuplicationColumnValue)),
                          duplicateProspectIdsWithEditPermission = duplicateProspectIdsWithEditPermission,
                          totalDNCProspects = totalBlacklistsInNewProspects,
                          prospectDataForApi = List() //API_NOTE: This is getting calculated at end combining updated and inserted result
                        )


                        /**
                         * NOTE: 10th August 2021
                         * Adding this Thread.sleep just to give the db some relief after heavy writes.
                         *
                         * Don't follow this practice at other places without discussing with the team first.
                         */
                        //Thread.sleep(50)

                        groupedResult
                    }

                  } else {
                    UpsertSQLResult(
                      createdProspectEmails = List(),
                      createdProspectIds = insertedProspects.map(_.prospect_id),
                      updatedIds = updated_ids,
                      duplicateProspectsData = duplicateProspectsData,
                      duplicatesIgnoredEmailsBecauseOfNoEditPermission = duplicateProspectEmailsWithNoEditPermission.flatMap(p => p.filter(p => p.deDuplicationColumnType == SrProspectColumns.Email).map(v => v.deDuplicationColumnValue)),
                      duplicateProspectIdsWithEditPermission = duplicateProspectIdsWithEditPermission,
                      totalDNCProspects = totalBlacklistsInNewProspects,
                      prospectDataForApi = List() //API_NOTE: This is getting calculated at end combining updated and inserted result
                    )
                  }

                  //API_NOTE: Combining updated and inserted result
                  val prospect_data_for_api = ProspectService.constructProspectDataForApi(
                    prospect_inserted_emails = upsertResult.createdProspectEmails.toList,
                    prospect_inserted_data = insertedProspects,
                    prospect_updated_emails = prospectsToBeUpdated,
                    prospect_updated_data = updated_prospects
                  ) match {
                    case Failure(exception) =>
                      Logger.error(s"Error while constructing prospect data for api ${exception.getMessage}")
                      List()
                    case Success(res) => res
                  }

                  upsertResult.copy(prospectDataForApi = prospect_data_for_api)

                })

              val allUpdatedIds = sqlResult.flatMap(_.updatedIds)

              // on updating a prospect, remove any to_check errors that may have been associated with the prospect earlier
              mergeTagService._removeMissingErrorMessage(prospectIds = allUpdatedIds)

              val createdProspectEmails = sqlResult.flatMap(_.createdProspectEmails)
              val duplicateProspectsData = sqlResult.flatMap(_.duplicateProspectsData)

              val totalBlacklistsInNewProspects: Int = sqlResult.map(_.totalDNCProspects).sum

              UpsertSQLResult(
                createdProspectEmails = createdProspectEmails,
                createdProspectIds = sqlResult.flatMap(_.createdProspectIds),
                updatedIds = allUpdatedIds,
                duplicateProspectsData = duplicateProspectsData,
                duplicatesIgnoredEmailsBecauseOfNoEditPermission = sqlResult.flatMap(_.duplicatesIgnoredEmailsBecauseOfNoEditPermission),
                duplicateProspectIdsWithEditPermission = sqlResult.flatMap(_.duplicateProspectIdsWithEditPermission),
                totalDNCProspects = totalBlacklistsInNewProspects,
                prospectDataForApi = sqlResult.flatMap(_.prospectDataForApi)
              )

          }


      }

    }

  }

  def getAllInternalEmailsInTeam(
                                  team_id: Long,
                                  org_id: Long,
                                  Logger: SRLogger
                                ): Try[InternalEmailsOrDomains] = {

    given srLogger: SRLogger = Logger // fixme given

    val internal_emails_or_domains: Try[InternalEmailsOrDomains] = for {
      emails_in_team: List[String] <- emailSettingDAO.getAllEmailSettingsInTeam(
        team_id = team_id
      )
      email_settings: List[String] <- accountService.getEmailsOfTeamMembers(
        teamId = TeamId(id = team_id) // FIXME VALUECLASS
      )
      internal_domain: String <- accountService.getEmailOfOrgOwner(org_id = OrgId(id = org_id)) // FIXME VALUECLASS
    } yield {
      val internal_emails: Set[String] = (emails_in_team ++ email_settings).toSet
      InternalEmailsOrDomains(
        internal_emails.toList,
        List(EmailValidationService.getLowercasedNameAndDomainFromEmail(internal_domain)._2)
      )
    }

    internal_emails_or_domains
  }

  def getProspectListWithoutInternalEmails(
                                            teamId: Long,
                                            org_id: Long,
                                            Logger: SRLogger,
                                            prospectsWithValidEmailFormat: Seq[ProspectCreateFormData]
                                          ): Try[ProspectInternalEmails] = Try {

    given srLogger: SRLogger = Logger // fixme given

    val internal_emails_or_domains = getAllInternalEmailsInTeam(
      team_id = teamId,
      org_id = org_id,
      Logger = Logger
    ) match {
      case Failure(exception) =>
        Logger.fatal("ProspectService.getAllInternalEmailsInTeam failed")
        throw exception

      case Success(res) => res
    }

    val prospects_data_with_internal_email = prospectsWithValidEmailFormat
      .filter(_.email.isDefined)
      .filter(p => {
        val p_domain = EmailValidationService.getLowercasedNameAndDomainFromEmail(p.email.get)._2
        checkIsInternalEmail(
          internal_emails_or_domains = internal_emails_or_domains,
          prospect_email = p.email.get,
          prospect_domain = p_domain
        )
      })

    val prospectsWithoutInternalEmailsAndDomains = prospectsWithValidEmailFormat.filterNot(p => {
      prospects_data_with_internal_email.map(_.email).contains(p.email)
    })

    ProspectInternalEmails(
      prospects_with_internal_emails = prospects_data_with_internal_email,
      prospects_without_internal_emails = prospectsWithoutInternalEmailsAndDomains
    )
  }

  def checkIsInternalEmail(
                            internal_emails_or_domains: InternalEmailsOrDomains,
                            prospect_email: String,
                            prospect_domain: String
                          ): Boolean = {
    internal_emails_or_domains.internal_emails.contains(prospect_email) ||
      internal_emails_or_domains.internal_domain.contains(prospect_domain)
  }

  def updateSpecificProspect(
                              data: ProspectUpdateFormData,
                              prospect: ProspectObject,
                              prospectId: Long,
                              team_id: Long,
                              user_id: Long,
                              doerAccount: Account,
                              accountName: String,
                              permittedAccountIds: Seq[Long],
                              Logger: SRLogger,
                              auditRequestLogId: String
                            ): Either[UpdateSpecificProspectError, ProspectObject] = {

    given srLogger: SRLogger = Logger // fixme given
//    val emailNotCompulsoryEnabled: Boolean = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//      teamId = TeamId(team_id),
//      feature = SrRollingUpdateFeature.EmailNotCompulsory
//    )(Logger)


    val newProspectCategory = data.prospect_category_id_custom.flatMap(pcid => {
      prospectDAOService.getProspectCategoryById(team_id = team_id, category_id = pcid, account = Some(doerAccount))
    })

    if (data.email.isDefined && !EmailValidationService.validateEmailFormat(data.email.get) &&
      !((data.email.isEmpty || data.email.get.isEmpty))) {

      Left(UpdateSpecificProspectError.InvalidEmailError("Provide a valid email address"))

    } else {

      val isPhoneNumberValid: Boolean = checkIfAllPhonesAreValidIfDefined(List(data.phone, data.phone_2, data.phone_3))

      if (!isPhoneNumberValid) {
        Left(UpdateSpecificProspectError.InvalidPhoneNumber(s"Please pass valid phone numbers."))

      } else {

        val isInternalEmail: Either[UpdateSpecificProspectError.ServerErrorWhileUpdate, Boolean] = if (data.email.isDefined) {
          val (_, emailDomain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(data.email.get)

          getAllInternalEmailsInTeam(
            team_id = team_id,
            org_id = doerAccount.org.id,
            Logger = Logger
          ) match {
            case Failure(e) => Left(UpdateSpecificProspectError.ServerErrorWhileUpdate(e))

            case Success(internal_emails_or_domains) =>

              Right(checkIsInternalEmail(
                internal_emails_or_domains = internal_emails_or_domains,
                prospect_email = data.email.get,
                prospect_domain = emailDomain
              ))
          }
        } else {
          Right(false)
        }
        isInternalEmail match {
          case Left(err) => Left(err)

          case Right(is_internal_email) =>
            if (is_internal_email) {
              val errorMsg = "Internal email (i.e. any email which you have integrated as a sending email, or emails of any team members, or emails belonging to your organization's domain) should not be added as a prospect. If you want to test out a campaign, please add any other email not belonging to your integrated domain."
              Left(UpdateSpecificProspectError.InternalEmailError(errorMsg))

            } else {

              // Getting prospect Before Update to pass the data for Updating in crm

              val phone1 = if (data.phone.isDefined && data.phone.get.nonEmpty) data.phone else None
              val phone2 = if (data.phone_2.isDefined && data.phone_2.get.nonEmpty) data.phone_2 else None
              val phone3 = if (data.phone_3.isDefined && data.phone_3.get.nonEmpty) data.phone_3 else None
              val finalPhones: (Option[String], Option[String], Option[String]) = ProspectUploadService.assignValuesToPhones(
                phone1 = phone1,
                phone2 = phone2,
                phone3 = phone3
              )

              val updateProspectData: ProspectUpdateFormData = data.copy(
                phone = finalPhones._1,
                phone_2 = finalPhones._2,
                phone_3 = finalPhones._3
              )

              prospectDAOService.update(
                permittedAccountIds = permittedAccountIds,
                actingAccountId = user_id,
                id = prospectId,
                teamId = team_id,
                prospect_owner_account_id = AccountId(prospect.owner_id),
                updateProspect = updateProspectData
              )(Logger = Logger) match {

                case Failure(e) =>
                  Left(UpdateSpecificProspectError.ServerErrorWhileUpdate(e))

                case Success(None) =>
                  Left(UpdateSpecificProspectError.ProspectNotFound(s"Prospect not found. Could you check and try again ?"))

                case Success(Some(updatedProspectId)) =>

                  //publishing trigger events
                  // CREATED_OR_UPDATED_PROSPECT_IN_SMARTREACH is also implicitly called in Trigger.findAllforMQProcess

                  mqTrigger.publishEvents(message = MQTriggerMsg(
                    accountId = user_id,
                    teamId = team_id,
                    prospectIds = Seq(prospect.id),
                    event = EventType.UPDATED_PROSPECT_IN_SMARTREACH.toString,
                    updatedProspectCategoryId = None,
                    triggerPath = Some(TriggerSource.OTHER),
                    oldProspectDeduplicationColumn = Some(OldProspectDeduplicationColumn(
                      email = prospect.email, // old prospect email
                      phone = prospect.phone // old prospect phone
                    ))
                  ))

//                  if (emailNotCompulsoryEnabled) {
                    potentialDuplicateProspectService.checkIfUniqueColUpdatedAndMarkNeedDuplicateCheck(
                      prospects = Seq(ProspectUpdateFormWithId(
                        prospectId = ProspectId(prospectId),
                        prospectUpdateFormData = ProspectDeduplicationColumnsData(
                          email = data.email,
                          phone = data.phone,
                          linkedin_url = data.linkedin_url,
                          company = data.company,
                          first_name = data.first_name,
                          last_name = data.last_name
                        ),
                        prospectDataFromDb = ProspectDeduplicationColumnsData(
                          email = prospect.email,
                          phone = prospect.phone,
                          linkedin_url = prospect.linkedin_url,
                          company = prospect.company,
                          first_name = prospect.first_name,
                          last_name = prospect.last_name
                        )
                      )),
                      team_id = TeamId(team_id)
                    ) match {
                      case Failure(exception) => Logger.shouldNeverHappen("checkIfUniqueColUpdatedAndMarkNeedDuplicateCheck failed", Some(exception))
                      case Success(value) => //do nothing
                    }
//                  }

                  if (newProspectCategory.isDefined && newProspectCategory.get.id != prospect.internal.prospect_category_id_custom) {

                    val newCatId: Long = newProspectCategory.get.id

                    prospectUpdateCategoryTemp.updateCategory(
                      prospect = ProspectBasicForBatchUpdate(
                        id = prospectId,
                        prospect_category_id_custom = prospect.internal.prospect_category_id_custom,
                        prospect_account_id = prospect.internal.prospect_account_id
                      ),
                      teamId = prospect.team_id,
                      doerAccountId = user_id,
                      accountName = accountName,
                      prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AdminUpdate(
                        old_prospect_category_id = Some(ProspectCategoryId(id = prospect.internal.prospect_category_id_custom)),
                        new_prospect_category_id = ProspectCategoryId(id = newCatId)
                      ),
                      account = doerAccount,
                      logger = Logger,
//                      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled,
                      auditRequestLogId = Some(auditRequestLogId)
                    ) match {

                      case Failure(e) =>
                        Left(UpdateSpecificProspectError.ServerErrorWhileUpdate(e))

                      case Success(0) =>
                        if (data.email.isDefined) {
                          val (_, emailDomain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(data.email.get)
                          Left(UpdateSpecificProspectError.ProspectNotUpdated(
                            s"Prospect category could not be updated because '$emailDomain' domain is likely in Do Not Contact List. Could you check this ? If not, contact support."
                          ))
                        } else {
                          Left(UpdateSpecificProspectError.ProspectNotUpdated(
                            s"Prospect category could not be updated because prospect is likely in Do Not Contact List. Could you check this ? If not, contact support."
                          ))
                        }


                      case Success(_) =>

                        // PROSPECTS_EMAILS_TODO_READ_CLEANED
                        prospectDAOService.findFromMaster(
                          prospectId = prospectId,
                          teamId = team_id,
                          orgId = Some(OrgId(doerAccount.org.id)),
                          Logger = Logger
                        ) match {
                          case Failure(e) =>
                            Left(UpdateSpecificProspectError.ServerErrorWhileUpdate(e))

                          case Success(None) =>
                            Left(UpdateSpecificProspectError.ProspectNotUpdated(
                              s"Prospect '${prospect.email}' has not been updated. Could you check and try again ?"
                            ))

                          case Success(Some(prospectObject)) =>

                            Right(prospectObject)
                        }

                    }

                  } else {

                    // PROSPECTS_EMAILS_TODO_READ_CLEANED
                    prospectDAOService.findFromMaster(
                      prospectId = prospectId,
                      teamId = team_id,
                      orgId = Some(OrgId(doerAccount.org.id)),
                      Logger = Logger
                    ) match {
                      case Failure(e) =>
                        Left(UpdateSpecificProspectError.ServerErrorWhileUpdate(e))

                      case Success(None) =>
                        Left(UpdateSpecificProspectError.ProspectNotUpdated(
                          s"Prospect '${prospect.email}' has not been updated. Could you check and try again ?"
                        ))

                      case Success(Some(prospectObject)) =>

                        Right(prospectObject)
                    }
                  }
              }
            }
        }
      }
    }
  }

  /**
   *
   *
   */
  def searchProspectsForAddToExisting(
                                       search_string: Option[String],
                                       team_id: Long
                                     ): Either[SearchProspectsForAddToExisting, List[ExistingProspect]] = {

    search_string match {
      case None => Left(SearchProspectsForAddToExisting.EmptySearchString("Search string not found"))

      case Some(search_string) =>

        prospectDAOService.getExistingProspects(
          search_string = search_string, team_id = team_id
        ) match {
          case Failure(e) => Left(SearchProspectsForAddToExisting.DBFailure(e))

          case Success(prospects) => Right(prospects)
        }
    }

  }

  def isInternalEmail(
                       email: String,
                       email_domain: String,
                       team_id: Long,
                       org_id: Long,
                       Logger: SRLogger
                     ): Either[InsertSecondaryProspectEmailError, Boolean] = {
    getAllInternalEmailsInTeam(
      team_id = team_id,
      org_id = org_id,
      Logger = Logger
    ) match {
      case Failure(exception) => Left(InsertSecondaryProspectEmailError.DBFailure(exception))

      case Success(internalEmailsAndDomains) =>
        Right(internalEmailsAndDomains.internal_emails.contains(email) ||
          internalEmailsAndDomains.internal_domain.contains(email_domain))
    }
  }

  def validateSecondaryEmail(
                              team_id: Long,
                              email: String,
                              orgId: OrgId,
                              email_domain: String,
                              email_format_valid: Boolean
                            )(using Logger: SRLogger): Either[InsertSecondaryProspectEmailError, true] = {

    if (!email_format_valid) {

      Left(InsertSecondaryProspectEmailError.InvalidEmailFormat("Please send valid email format"))

    } else {
      isInternalEmail(
        email = email,
        email_domain = email_domain,
        team_id = team_id,
        org_id = orgId.id,
        Logger = Logger
      ) match {
        case Left(err) => Left(err)

        case Right(isInternalEmail) =>
          if (isInternalEmail) {

            Left(InsertSecondaryProspectEmailError.InternalEmailOrDomainFound("Internal email cannot be added as secondary email"))

          } else {
            prospectsEmailsDAO.findByEmailV2(
              emails = Seq(email),
              teamId = team_id,
              logger = Logger
            ) match {
              case Failure(e) => Left(InsertSecondaryProspectEmailError.DBFailure(e))

              case Success(duplicate_prospect) =>

                if (duplicate_prospect.nonEmpty) {
                  Left(InsertSecondaryProspectEmailError.DuplicateProspect("Email is already present as prospect"))

                } else {
                  Right(true)
                }
            }
          }
      }
    }
  }

  /**
   * addSecondaryProspectEmail API is getting called from Inbox Non-Prospect folder sidebar 'add to existing prospect'
   * In return we are sending active campaigns associated with the existing prospect
   * to get input from user whether to pause those campaigns
   */
  def insertSecondaryProspectEmail(
                                    team_id: Long,
                                    added_by_account_id: Long,
                                    prospect_id: Long,
                                    email: String,
                                    orgId: OrgId,
                                    active_campaign_ids: Seq[Long]
                                  )(using logger: SRLogger): Either[InsertSecondaryProspectEmailError, String] = {

    val (_, emailDomain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(email)
    val isEmailValid = EmailValidationService.validateEmailFormat(email)

    validateSecondaryEmail(
      team_id = team_id,
      email = email,
      orgId = orgId,
      email_domain = emailDomain,
      email_format_valid = isEmailValid
    ) match {
      case Left(err) => Left(err)

      case Right(true) =>
        prospectsEmailsDAO.addSecondaryProspectEmail(
          SecondaryProspectEmail(
            prospect_id = prospect_id,
            added_by_account_id = added_by_account_id,
            team_id = team_id,
            email = email,
            email_domain = emailDomain,
            email_format_valid = isEmailValid,
            is_primary = false
          )
        ) match {
          case Failure(e) => Left(InsertSecondaryProspectEmailError.DBFailure(e))

          case Success(prospect) =>
            val (_, emailDomain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(prospect.email)
            mqDomainServiceProviderDNSService.publish(emailDomain) match {
              case Success(value) => //DO NOTHING
              case Failure(exception) => logger.fatal(s"failed to push to mqDomainServiceProviderDNSService", exception)
            }
            mqAssociateProspectToOldEmails.publish(msg = AssociateProspectsData(
              new_prospects = Seq(prospect),
              team_id = team_id,
              campaign_ids = Some(active_campaign_ids)
            ))

            Right("Success")


        }
    }
  }

  /**
   * This function is called in ProspectController.createOrUpdateBatch v1/v2 api
   * input ProspectBatchCreateFormData(prospects: Seq[ProspectCreateFormData], tags: Option[Seq[String])
   * is having prospects and tags separate lists
   *
   * Also this function returns error wherever encountered
   * unlike v3 api which created error list
   * */
  /*
   23-april-2024:

   check this : checked  its calling createOrUpdateProspects inside
   */

  def createOrUpdateProspectsBatch(
                                    ignore_prospects: Option[String],
                                    data: ProspectBatchCreateFormData,
                                    teamAccount: TeamMember,
                                    source: Option[String],
                                    campaign_id: Option[Long],
                                    doer: Account,
                                    auditRequestLogId: Option[String]
                                  )(using logger: SRLogger): Either[CreateOrUpdateProspectBatchError, String] = {

    val ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads: Boolean = true
    val forceUpdateProspects: Boolean = true

    CampaignService.getIgnoreProspectsInOtherCampaigns(
      ignore_prospects_in_other_campaigns = ignore_prospects,
      force_assign = None
    ) match {

      case Failure(err) =>
        logger.error(s"Error while getting the ignoreProspectsInOtherCampaigns, data sent from frontend - ${ignore_prospects}", err = err)
        Left(CreateOrUpdateProspectBatchError.FailedToAssignProspect("Failed to Assign Prospect, Please contact Support"))

      case Success(ignoreProspectIfActiveInAnyotherCampaign) =>

        val prospects: Seq[ProspectCreateFormData] = data.prospects
        val tags: Option[String] = if (data.tags.isDefined && data.tags.get.nonEmpty) Some(data.tags.get.mkString(",")) else None

        // check if email format is valid
        val invalidEmails: Seq[ProspectCreateFormData] = prospects
          .filter(_.email.isDefined)
          .filterNot(p => EmailValidationService.validateEmailFormat(p.email.get))
          .distinct

        val ownerAccountIds: Seq[Long] = prospects
          .filter(_.owner_id.isDefined)
          .map(_.owner_id.get)
          .distinct

        val prospectLists: Seq[String] = prospects
          .filter(_.list.isDefined)
          .map(_.list.get)
          .distinct

        val prospectSource: Option[Try[ProspectSource.Value]] = source.map(sourceDefined => Try {
          ProspectSource.withName(sourceDefined)
        })

        if (prospectSource.isDefined && prospectSource.get.isFailure) {

          Left(CreateOrUpdateProspectBatchError.InvalidProspectSource(s"Invalid Source: $source"))

        } else if (prospects.isEmpty) {

          Left(CreateOrUpdateProspectBatchError.ProspectListEmpty(s"Please send a list of prospects. Empty request found."))

        } else if (invalidEmails.nonEmpty) {

          Left(CreateOrUpdateProspectBatchError.InvalidEmails(
            s"Provide valid email addresses. Invalid emails found: ${invalidEmails.mkString(", ")}"
          ))

        } else if (ownerAccountIds.size > 1) {

          Left(CreateOrUpdateProspectBatchError.MoreThanOneOwnerInvolved(
            s"In a single request, Prospects can be added to one specific Owner. Send multiple requests to add prospects to different owners."
          ))

        } else if (prospectLists.size > 1) {

          Left(CreateOrUpdateProspectBatchError.FoundMultipleProspectsList(
            s"In a single request, Prospects can be added to one specific List. Send multiple requests to add prospects to different Lists."
          ))

        } else {

          val teamId: Long = teamAccount.team_id

          // NOTE: ownership / permission checking is happening anyways inside ProspectService.createOrUpdateProspects
          val prospectOwnerAccountId: Long = if (ownerAccountIds.nonEmpty) {
            ownerAccountIds.head
          } else {
            teamAccount.user_id
          }

          val prospectListName: Option[String] = prospectLists.headOption


          val saveTry: Try[CreateOrUpdateProspectsResult] = createOrUpdateProspects(
            ownerAccountId = prospectOwnerAccountId,
            teamId = teamId,
            listName = prospectListName,
            prospects = prospects,
            updateProspectType = UpdateProspectType.getForceUpdateType(forceUpdateProspects),
            ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads,
            doerAccount = doer,
            prospectSource = prospectSource.flatMap(_.toOption),
            prospectAccountId = None,

            campaign_id = campaign_id,
            prospect_tags = tags,
            ignoreProspectInOtherCampaign = ignoreProspectIfActiveInAnyotherCampaign,
            deduplicationColumns = None, //none for public api
            auditRequestLogId = auditRequestLogId,
            SRLogger = logger

          )

          saveTry match {

            case Failure(e: BadRequestErrorException) =>

              Left(CreateOrUpdateProspectBatchError.BadRequestError(e.getMessage))

            case Failure(e) =>

              Left(CreateOrUpdateProspectBatchError.ErrorWhileCreatingProspect(e))

            case Success(results) =>

              val totalCreatedOrUpdated = results.created_ids.size + results.updated_ids.size

              if (results.ignored_internal_emails.nonEmpty) {
                logger.debug(s"Internal emails ${results.ignored_internal_emails} cannot be added as prospects")
              }

              if (totalCreatedOrUpdated > 1) {

                Right(s"$totalCreatedOrUpdated prospects added/updated")

              } else if (totalCreatedOrUpdated == 1) {

                Right(s"1 prospect added/updated")

              } else if (results.duplicates_ignored_for_no_edit_permission.nonEmpty) {

                Right("Prospects already exist in your team and you do not have permission to edit them")

              } else {

                Left(CreateOrUpdateProspectBatchError.BadRequestError(s"0 prospects added/updated"))

              }
          }
        }
    }
  }


  private def validateIncomingProspectData(
                                            prospects: Seq[ProspectCreateFormData],
                                            prospect_tags: Seq[String],
                                            source: Option[String],
                                            prospectSource: Option[Try[ProspectSource.Value]],
                                            ownerAccountIds: Seq[Long],
                                            prospectLists: Seq[String],
                                            teamId: Long,
                                            org_id: Long
                                          )(using logger: SRLogger): List[ErrorResponseProspectsPostApi] = {

    var errorsList: List[ErrorResponseProspectsPostApi] = List()
    val duplicateProspectEmailsList: List[String] = getDuplicateProspectEmails(prospects.filter(_.email.isDefined).map(_.email.get))
    val existingProspectCustomColumnDefs: Seq[ColumnDef] = prospectColumnDef.findCustomColumns(teamId = teamId).filter(_.is_custom)
    val newProspectCustomColumnDef: Seq[JsValue] = prospects.map(_.custom_fields)

    val columnNamesNotExistingInDb: Seq[String] = ProspectColumnDefUtils.getColumnNamesNotExistingInDb(
      existingColumnDefs = existingProspectCustomColumnDefs,
      inputCustomCols = newProspectCustomColumnDef
    )

    getProspectListWithoutInternalEmails(
      teamId = teamId,
      org_id = org_id,
      Logger = logger,
      prospectsWithValidEmailFormat = prospects
    ) match {
      case Failure(e) =>
        logger.error("Error while internal emails", e)
      //do nothing
      case Success(result) =>
        if (result.prospects_with_internal_emails.nonEmpty) {
          val err = ErrorResponseProspectsPostApi(
            error_type = ProspectErrorType.InternalEmailsFound,
            message = "Internal emails cannot be added as prospects",
            data = Some(result.prospects_with_internal_emails.map(_.email).mkString(",")),
            owned_prospects = None
          )
          errorsList = errorsList.appended(err)
        }
    }

    val invalidTags = prospect_tags.filter(!TagService.validateTagName(_))

    if (columnNamesNotExistingInDb.nonEmpty) {
      val err = ErrorResponseProspectsPostApi(
        error_type = ProspectErrorType.CustomFieldNotFound,
        message = "Custom field/s does not exist. Please add it via UI",
        data = Some(columnNamesNotExistingInDb.mkString(",")),
        owned_prospects = None
      )
      errorsList = errorsList.appended(err)
    }
    if (invalidTags.nonEmpty) {
      val err = ErrorResponseProspectsPostApi(
        error_type = ProspectErrorType.InvalidTags,
        message = "Invalid tag. Only alphanumeric names with space/hyphens allowed",
        data = Some(invalidTags.mkString(",")),
        owned_prospects = None
      )
      errorsList = errorsList.appended(err)
    }
    if (prospects.length > 100) {
      val err = ErrorResponseProspectsPostApi(
        error_type = ProspectErrorType.LimitExceeded,
        message = "Limit exceeded. Cannot create/update more than 100 prospects.",
        data = None,
        owned_prospects = None
      )
      errorsList = errorsList.appended(err)
    }
    if (duplicateProspectEmailsList.nonEmpty) {
      val err = ErrorResponseProspectsPostApi(
        error_type = ProspectErrorType.DuplicateEmailsFound,
        message = "Duplicate prospect emails found",
        data = Some(duplicateProspectEmailsList.mkString(",")),
        owned_prospects = None
      )
      errorsList = errorsList.appended(err)
    }
    if (prospectSource.isDefined && prospectSource.get.isFailure) {

      val err = ErrorResponseProspectsPostApi(
        error_type = ProspectErrorType.InvalidSource,
        message = "Please send valid prospect source.",
        data = source,
        owned_prospects = None
      )
      errorsList = errorsList.appended(err)
    }
    if (ownerAccountIds.size > 1) {

      val err = ErrorResponseProspectsPostApi(
        error_type = ProspectErrorType.MultipleOwnersFound,
        message = "In a single request, Prospects can be added to one specific Owner. Send multiple requests to add prospects to different owners.",
        data = None, //Fixme PUBLIC_API: if more than 1 owner passed in req body then return owner uuid strings
        owned_prospects = None
      )
      errorsList = errorsList.appended(err)
    }
    if (prospectLists.size > 1) {

      val err = ErrorResponseProspectsPostApi(
        error_type = ProspectErrorType.MultipleListFound,
        message = "In a single request, Prospects can be added to one specific List. Send multiple requests to add prospects to different Lists.",
        data = Some(prospectLists.mkString(",")),
        owned_prospects = None
      )
      errorsList = errorsList.appended(err)
    }

    errorsList
  }


  /**
   * This function is called in ProspectController.createOrUpdateProspectsBatch v3 api
   * input ProspectBatchCreateFormDataV2
   * is having prospect's data and tags inside single case class
   *
   * Also this function returns errors in list before calling multiRowInsert function
   * */
  /*
  23-april-2024

    check this : Its, calling createOrUpdateProspects inside

   */
  def createOrUpdateProspectsBatchV3(
                                      data: Seq[ProspectCreateFormDataV2],
                                      source: Option[String],
                                      teamAccount: TeamMember,
                                      doer: Account,
                                      auditRequestLogId: Option[String],
                                      campaignId: Option[CampaignId]
                                    )(using logger: SRLogger): Either[CreateOrUpdateProspectsBatchApiError, Seq[ProspectObject]] = {

//
//    val emailNotCompulsoryEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//      teamId = TeamId(teamAccount.team_id),
//      feature = SrRollingUpdateFeature.EmailNotCompulsory
//    )(logger)


//    if (isValidFlow) {
      val ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads: Boolean = true
      val forceUpdateProspects: Boolean = true

      val prospect_tags: Seq[String] = data.filter(_.tags.isDefined).flatMap(_.tags.get)

      val prospects: Seq[ProspectCreateFormData] = data.map(p => {
        ProspectCreateFormData(
          email = p.email,
          first_name = p.first_name,
          last_name = p.last_name,
          custom_fields = p.custom_fields.getOrElse(Json.obj()),
          owner_id = p.owner_id,
          list = p.list,
          company = p.company,
          city = p.city,
          country = p.country,
          timezone = p.timezone,
          created_at = p.created_at,

          state = p.state,
          job_title = p.job_title,
          phone = p.phone_number,
          phone_2 = None, //fixme: SECONDARY_PHONES - pass it from ProspectCreateFormDataV2
          phone_3 = None,
          linkedin_url = p.linkedin_url
        )
      })

      if (prospects.isEmpty) {

        val err = ErrorResponseProspectsPostApi(
          error_type = ProspectErrorType.EmptyList,
          message = "Please send a list of prospects. Empty request found.",
          data = None,
          owned_prospects = None
        )
        Left(CreateOrUpdateProspectsBatchApiError.BadRequestError(List(err)))

      } else {

        // not checking if email format is valid for api v3

        val ownerAccountIds: Seq[Long] = prospects
          .filter(_.owner_id.isDefined)
          .map(_.owner_id.get)
          .distinct

        val prospectLists: Seq[String] = prospects
          .filter(_.list.isDefined)
          .map(_.list.get)
          .distinct

        val prospectSource: Option[Try[ProspectSource.Value]] = source.map(sourceDefined => Try {
          ProspectSource.withName(sourceDefined)
        })

        val errorsList: List[ErrorResponseProspectsPostApi] = validateIncomingProspectData(
          prospects = prospects,
          prospect_tags = prospect_tags,
          source = source,
          prospectSource = prospectSource,
          ownerAccountIds = ownerAccountIds,
          prospectLists = prospectLists,
          teamId = teamAccount.team_id,
          org_id = doer.org.id
        )

        if (errorsList.isEmpty) {

          val teamId: Long = teamAccount.team_id

          // NOTE: ownership / permission checking is happening anyways inside ProspectService.createOrUpdateProspects
          val prospectOwnerAccountId: Long = if (ownerAccountIds.nonEmpty) {
            ownerAccountIds.head
          } else {
            teamAccount.user_id
          }

          val prospectListName: Option[String] = prospectLists.headOption

          val saveTry: Try[CreateOrUpdateProspectsResult] = createOrUpdateProspects(
            ownerAccountId = prospectOwnerAccountId,
            teamId = teamId,
            listName = prospectListName,
            prospects = prospects,
            updateProspectType = UpdateProspectType.getForceUpdateType(forceUpdateProspects),
            ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads,
            doerAccount = doer,
            prospectSource = prospectSource.flatMap(_.toOption),
            prospectAccountId = None,

            campaign_id = campaignId.map(campaignId => campaignId.id),
            prospect_tags = None, //for public api we are passing tags inside each prospectObject
            ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.DoNotIgnore, //Fixme PUBLIC_API: check if valid value
            deduplicationColumns = None, //none for public api
            auditRequestLogId = auditRequestLogId,
            prospectsWithTagsForApi = Some(data),
            SRLogger = logger
          )

          saveTry match {

            case Failure(e: BadRequestErrorException) =>

              logger.error(s"Error while saving prospect data: ${LogHelpers.getStackTraceAsString(e)}")
              val err = ErrorResponseProspectsPostApi(
                error_type = ProspectErrorType.BadRequest,
                message = "Error while create/update prospects. Please contact support.",
                data = None,
                owned_prospects = None
              )

              Left(CreateOrUpdateProspectsBatchApiError.BadRequestError(List(err)))

            case Failure(e) =>

              Left(CreateOrUpdateProspectsBatchApiError.ErrorWhileCreatingProspect(e))

            case Success(results) =>

              if (results.inserted_or_updated_prospects.nonEmpty) {

                prospectDAOService.find(
                  byProspectIds = results.inserted_or_updated_prospects.map(_.prospect_data.prospect_id),
                  Logger = logger,
                  teamId = teamId
                ) match {
                  case Failure(exception) =>
                    Left(CreateOrUpdateProspectsBatchApiError.ErrorWhileCreatingProspect(exception))

                  case Success(res) => Right(res)
                }

              } else {
                val err = ErrorResponseProspectsPostApi(
                  error_type = ProspectErrorType.BadRequest, //Fixme PUBLIC_API: add exact error why 0 added/updated
                  message = "0 prospects added/updated",
                  data = None,
                  owned_prospects = None
                )

                Left(CreateOrUpdateProspectsBatchApiError.BadRequestError(List(err)))

              }
          }
        } else {
          Left(CreateOrUpdateProspectsBatchApiError.BadRequestError(errorsList))
        }

      }
//    } else {
//      Left(CreateOrUpdateProspectsBatchApiError.EmailCannotBeOptionalInTheOldFlow)
//    }

  }

  def getProspectIdsFromUuid(
                              data: List[ProspectUuid],
                              teamId: TeamId
                            )(using logger: SRLogger): Either[GetProspectIdFromUuidError, ValidAndInvalidProspectUuidIdList] = {
    srUuidService.getProspectIdFromUuid(
      prospectUuids = data,
      team_id = teamId
    ) match {
      case Failure(exception) => Left(GetProspectIdFromUuidError.GetProspectIdError(err = exception))
      case Success(uuidIdMap) =>
        val invalid_uuids = uuidIdMap.filter(p => p._2.isEmpty).keys.toList
        val valid_ids = uuidIdMap.filter(p => p._2.isDefined).map(p => p._2.get).toList

        Right(ValidAndInvalidProspectUuidIdList(
          invalid_uuids = invalid_uuids,
          valid_prospect_ids = valid_ids
        ))
    }
  }

  def getProspectIdFromSrIdentifier(
                                     id: SrIdentifier,
                                     teamId: TeamId
                                   )(using Logger: SRLogger): Either[GetProspectIdFromSrIdentifierError, ProspectId] = {

    id match {

      case SrId(pid) => Right(ProspectId(id = pid))

      case SrUuid(uuid) =>

        val prospectUuid = ProspectUuid(uuid)

        val getProspectIdResp: Either[GetProspectIdFromUuidError, ValidAndInvalidProspectUuidIdList] = getProspectIdsFromUuid(
          data = List(prospectUuid),
          teamId = teamId
        )

        getProspectIdResp match {

          case Left(GetProspectIdFromUuidError.GetProspectIdError(err)) =>
            Logger.fatal(msg = s"[ProspectUtils.hasProspectV2 prospectService.getProspectIdsFromUuid] data::${List(prospectUuid)} teamId::$teamId")
            Left(GetProspectIdFromSrIdentifierError.GetProspectIdError(err = err))

          case Right(validAndInvalidProspectUuidIdList: ValidAndInvalidProspectUuidIdList) =>

            if (validAndInvalidProspectUuidIdList.invalid_uuids.nonEmpty) {
              Left(GetProspectIdFromSrIdentifierError.InvalidProspectIdError)
            } else {

              val prospectId = validAndInvalidProspectUuidIdList.valid_prospect_ids.head

              Right(prospectId)

            }

        }

    }
  }

  def getProspectUuidFromId(
                             data: List[ProspectId],
                             teamId: TeamId
                           )(using logger: SRLogger): Try[List[ProspectUuid]] = {
    srUuidService.getProspectUuidFromId(
      prospectIds = data,
      team_id = teamId
    )
  }

  def markProspectsAsInvalidForApi(
                                    data: Seq[ProspectObject],
                                    team_id: Long
                                  )(using logger: SRLogger): Try[Seq[Option[ProspectUuid]]] = Try {

    val validateEmailForProspects: Seq[(ProspectObject, Boolean)] = data.map(p => (p, EmailValidationService.validateEmailFormat(p.email.get))) //TODO: EMAIL_OPTIONAL check the path and test code (prospects coming from prospectDAOService.find() having inner join prospects_emails so .get will not fail)

    val prospectsWithInvalidEmailFormat: Seq[(ProspectObject, Boolean)] = validateEmailForProspects.filter(!_._2)

    if (prospectsWithInvalidEmailFormat.nonEmpty) {

      //Fixme PUBLIC_API: create new DAO call to update all in one call
      val updateRes: Seq[Try[Int]] = prospectsWithInvalidEmailFormat.map(i => {
        prospectServiceV2.markAsInvalid(
          id = i._1.id,
          team_id = team_id
        )
      }).toSeq

      Helpers.seqTryToTrySeq(updateRes) match {
        case Failure(exception) => throw exception
        case Success(_) => prospectsWithInvalidEmailFormat.map(_._1.prospect_uuid)
      }

    } else {
      List()
    }


  }

  def getCampaignProspectDataForApiV3(
                                       data: Seq[Long],
                                       team_id: TeamId,
                                       org_id: OrgId,
                                       campaign_id: CampaignId
                                     )(using logger: SRLogger): Try[Seq[ProspectObject]] = {

    prospectDAOService.getDataForAssignProspectsV3(
      id = data,
      team_id = team_id,
      campaign_id = campaign_id,
      org_id = org_id
    )
  }

  def getEmailDataForProspectIds(
                                  prospectIds: Seq[ProspectId],
                                  team_id: TeamId
                                )(using logger: SRLogger): Try[List[ProspectEmailData]] = {

    prospectDAOService.getEmailDataForProspectIds(
      prospectIds = prospectIds,
      teamId = team_id

    )

  }

  def getProspectsDeduplicationColumnsFromDb(
                                              prospects: Seq[ProspectCreateFormData],
                                              teamId: TeamId
                                            ): Try[ProspectsDeduplicationColumnsFromDb] = {

    for {
      prospect_emails: Map[String, Long] <- prospectDAOService.getProspectsDeduplicationColumnsFromDb(
        data = Helpers.filterSeqOfOptStringToSeqString(prospects.map(_.email)),
        columnType = SrProspectColumns.Email,
        teamId = teamId
      )
      prospect_linkedins: Map[String, Long] <- prospectDAOService.getProspectsDeduplicationColumnsFromDb(
        data = Helpers.filterSeqOfOptStringToSeqString(prospects.map(_.linkedin_url)),
        columnType = SrProspectColumns.LinkedinUrl,
        teamId = teamId
      )
      prospect_phones: Map[String, Long] <- prospectDAOService.getProspectsDeduplicationColumnsFromDb(
        data = Helpers.filterSeqOfOptStringToSeqString(prospects.map(_.phone)),
        columnType = SrProspectColumns.Phone,
        teamId = teamId
      )
      prospect_cfl: Map[String, Long] <- {
        val company_firstname_last_name: Seq[String] = prospects.filter(p => {
          p.company.isDefined && p.first_name.isDefined && p.last_name.isDefined
        }).map(p => {
          p.company.get + p.first_name.get + p.last_name.get
        })
        //TODO: this needs to be efficient query. Add index and check if it pickups that
        prospectDAOService.getProspectsDeduplicationColumnsFromDb(
          data = company_firstname_last_name.filter(_.nonEmpty),
          columnType = SrProspectColumns.CompanyFirstnameLastname,
          teamId = teamId
        )
      }
    } yield {
      ProspectsDeduplicationColumnsFromDb(
        prospect_emails = prospect_emails,
        prospect_phones = prospect_phones,
        prospect_linkedin = prospect_linkedins,
        prospect_company_firstname_lastname = prospect_cfl
      )
    }
  }

  /**
   * For each row of prospects list (coming for multirowInsert  e.g. csv flow)
   * we are checking if we have more than one prospects matching the data in db for that row
   * If more than 1 prospects in db matches the data then we are returning Left(ListOfProspectsWithDataFromDifferentRowsInDb)
   * which is having data that is duplicate in db and need to be updated by user
   * else we are returning the ProspectCreateFormData as it is so that it's filtered for further actions
   */
  def getProspectsDbValidationData(
                                    prospects: Seq[ProspectCreateFormData],
                                    teamId: TeamId
                                  ): Try[List[Either[ListOfProspectsWithDataFromDifferentRowsInDb, ProspectCreateFormData]]] = {

    getProspectsDeduplicationColumnsFromDb(
      prospects = prospects,
      teamId = teamId
    ).map(prospectsDeduplicationColumnsFromDb => {
      checkIfFormDataMatchingDb(
        prospect_emails = prospectsDeduplicationColumnsFromDb.prospect_emails,
        prospect_linkedin = prospectsDeduplicationColumnsFromDb.prospect_linkedin,
        prospect_phones = prospectsDeduplicationColumnsFromDb.prospect_phones,
        prospect_cfl = prospectsDeduplicationColumnsFromDb.prospect_company_firstname_lastname,
        prospects_input = prospects.toList
      )
    })

  }

}
