package api.linkedin_messages

import api.accounts.TeamId
import api.phantombuster.{LinkedinMessageThreadId, PhantomBusterLinkedinMessage}
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import scalikejdbc.jodatime.JodaWrappedResultSet._
import api.phantombuster.{LinkedinMessageDetails, LinkedinMessageId}
import api.prospects.{ConversationMessageDAOTrait, FindThreadMessageFlow}
import eventframework.{ILinkedinProfile, MessageObject}
import io.smartreach.esp.utils.email.EmailHelperCommon
import utils.SRLogger

import scala.util.Try

class LinkedinMessagesDAO {

  def saveLinkedinMessages(
                            messageThreadId: LinkedinMessageThreadId,
                            teamId: TeamId,
                            message: PhantomBusterLinkedinMessage,
                            uuid: LinkedinMessageUuid
                          ): Try[LinkedinMessageDetails] = Try {

    /*
    Left message_type as "message" and subject as None because currently PhantomBuster does not show
    whether the message is an InMail or not and does not have the subject either in case of InMails.

    Reply from PhantomBuster Support on 4th Aug, 2023:
    Unfortunately, the information about if the message was an inmail (with a subject) or a standard message isn't available at the moment
    */
    DB autoCommit {implicit session =>
      sql"""
           INSERT INTO linkedin_messages (
              uuid,
              team_id,
              linkedin_message_thread_id,
              message_type,
              subject,
              body,
              sent_by_user,
              message_timestamp
           ) VALUES (
              ${uuid.uuid},
              ${teamId.id},
              ${messageThreadId.id},
              'message',
              ${None},
              ${message.message_body},
              ${message.sent_by_user},
              ${message.timestamp}
           )

           RETURNING id, sent_by_user, message_timestamp
           ;
           """
        .map(rs => LinkedinMessageDetails(
          id = LinkedinMessageId(rs.long("id")),
          sent_by_user = rs.boolean("sent_by_user"),
          timestamp = rs.jodaDateTime("message_timestamp")
        ))
        .single
        .apply()
        .get
    }

  }

  def findThreadMessages(
                          threadId: Long,
                          teamId: TeamId,
                          allTrackingDomains: Seq[String],
                          findThreadMessageFlow: FindThreadMessageFlow
                        )(using logger: SRLogger): Try[Seq[MessageObject.LinkedinMessageObject]] = Try{

    DB readOnly {implicit session =>
      sql"""
           SELECT
              lm.uuid,
              lm.sent_by_user,
              ls.profile_url as from_profile_url,
              lmt.to_profile_url as to_profile_url,
              lm.subject,
              lm.body,
              lm.message_timestamp
           FROM linkedin_messages lm
           INNER JOIN linkedin_message_threads lmt ON lmt.id = lm.linkedin_message_thread_id AND lm.team_id = lmt.team_id
           INNER JOIN linkedin_settings ls ON ls.id = lmt.linkedin_setting_id
           WHERE lm.linkedin_message_thread_id = $threadId
           AND lm.team_id = ${teamId.id}
           ;
           """
        .map(rs => {
          val htmlBody = rs.string("body")
          val sent_by_user = rs.boolean("sent_by_user")

          val (fromProfileUrl, toProfileUrl) = if (sent_by_user) {
            (rs.stringOpt("from_profile_url").getOrElse(""), rs.stringOpt("to_profile_url").getOrElse(""))
          }
          else {
            (rs.stringOpt("to_profile_url").getOrElse(""), rs.stringOpt("from_profile_url").getOrElse(""))
          }

          MessageObject.LinkedinMessageObject(
            uuid = rs.stringOpt("uuid"),
            from_user = sent_by_user,

            from_profile_url = ILinkedinProfile(profile_url = fromProfileUrl),
            to_profile_url = ILinkedinProfile(profile_url = toProfileUrl),

            subject = rs.stringOpt("subject").getOrElse(""),

            body = htmlBody,
            body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(htmlBody),

            sent_at = rs.jodaDateTime("message_timestamp")
          )
        })
        .list
        .apply()
    }

  }

}
