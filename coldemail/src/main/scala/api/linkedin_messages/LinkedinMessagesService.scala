package api.linkedin_messages

import api.accounts.TeamId
import api.phantombuster.LinkedinMessageDetails
import api.phantombuster.{LinkedinMessageThreadId, PhantomBusterLinkedinMessage}
import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, Js<PERSON><PERSON>ult, JsString, JsSuccess, JsValue, Reads, Writes}
import utils.uuid.SrUuidUtils

import scala.util.Try

case class LinkedinMessageUuid(uuid: String) extends AnyVal {
  override def toString: String = uuid
}
object LinkedinMessageUuid {
  implicit val reads: Reads[LinkedinMessageUuid] = new Reads[LinkedinMessageUuid] {
    override def reads(json: JsValue): JsResult[LinkedinMessageUuid] = {
      json match {
        case JsString(uuid) => JsSuccess(LinkedinMessageUuid(uuid = uuid))
        case randomValue => JsError(s"expected String, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[LinkedinMessageUuid] = new Writes[LinkedinMessageUuid] {
    override def writes(o: LinkedinMessageUuid): JsValue = JsString(o.uuid)
  }
}


class LinkedinMessagesService (
                                linkedinMessagesDAO: LinkedinMessagesDAO,
                                srUuidUtils: SrUuidUtils
                              ) {

  /*
  Success(None) is not an error case because this means out of all the new messages none of them is a reply.
  We would still save the messages without marking prospects as completed.
   */
  def saveLinkedinMessages(
                            messageThreadId: LinkedinMessageThreadId,
                            teamId: TeamId,
                            messages: List[PhantomBusterLinkedinMessage]
                          ): Try[Option[LinkedinMessageDetails]] = Try {

    messages.map(linkedinMessage => {


      val uuid = LinkedinMessageUuid(uuid = srUuidUtils.generateLinkedinMessagesUuid())
      linkedinMessagesDAO.saveLinkedinMessages(
        messageThreadId = messageThreadId,
        teamId = teamId,
        message = linkedinMessage,
        uuid = uuid
      )
        .map(messageDetails => {
          if (!messageDetails.sent_by_user) {
            Some(messageDetails)
          }
          else {
            None
          }
        })
        .get
    })
      .find(_.isDefined)
      .flatten

  }

}
