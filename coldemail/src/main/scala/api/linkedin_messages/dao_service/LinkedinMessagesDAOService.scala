package api.linkedin_messages.dao_service

import api.accounts.TeamId
import api.linkedin_messages.{LinkedinMessageUuid, LinkedinMessagesDAO}
import api.phantombuster.{LinkedinMessageDetails, LinkedinMessageThreadId, PhantomBusterLinkedinMessage}
import api.prospects.{ConversationMessageDAOTrait, FindThreadMessageFlow}
import eventframework.MessageObject
import utils.SRLogger

import scala.util.Try


class LinkedinMessagesDAOService(
                                  linkedinMessagesDAO: LinkedinMessagesDAO
                                ) extends ConversationMessageDAOTrait {
  override type MessageObjectType = MessageObject.LinkedinMessageObject

  def saveLinkedinMessages(
                            messageThreadId: LinkedinMessageThreadId,
                            teamId: TeamId,
                            message: PhantomBusterLinkedinMessage,
                            uuid: LinkedinMessageUuid
                          ): Try[LinkedinMessageDetails] = {
    linkedinMessagesDAO.saveLinkedinMessages(
      messageThreadId = messageThreadId,
      teamId = teamId,
      message = message,
      uuid = uuid
    )
  }

  override def findThreadMessages(
                                   threadId: Long,
                                   teamId: TeamId,
                                   allTrackingDomains: Seq[String],
                                   findThreadMessageFlow: FindThreadMessageFlow
                                 )(using logger: SRLogger): Try[Seq[MessageObjectType]] = {
    linkedinMessagesDAO.findThreadMessages(
      threadId = threadId,
      teamId = teamId,
      allTrackingDomains = allTrackingDomains,
      findThreadMessageFlow = findThreadMessageFlow
    )
  }
}
