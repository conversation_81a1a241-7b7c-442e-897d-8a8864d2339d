package api.teams_metadata

import api.AppConfig
import api.accounts.TeamId
import api.accounts.dao_service.OrganizationDAOService
import api.accounts.service.OrganizationService
import scalikejdbc.DBSession
import utils.SRLogger
import utils.cache.models.SrResetCacheInterval

import scala.util.Try

class TeamsMetadataService(
                            teamsMetadataDAO: TeamsMetadataDAO,
                            organizationDAOService: OrganizationDAOService
                          ) {
  
  def insertSavedProspectsCountForTeam(teamId: TeamId, savedProspectsCount: Int)(
    implicit session: DBSession
  ): Try[Int] = {
    teamsMetadataDAO.insertSavedProspectsCountForTeam(
      teamId = teamId,
      savedProspectsCount = savedProspectsCount
    )
  }
  
  def incrementOrDecrementSavedProspectsCount(
                                               teamId: TeamId, 
                                               incrementOrDecrementCount: Int // decrement count should be negative
                                             )(using logger: SRLogger): Try[Int] = {
    
    for {
      count: Int <- teamsMetadataDAO.incrementOrDecrementSavedProspectsCount(
        teamId = teamId,
        incrementOrDecrementCount = incrementOrDecrementCount
      )

      _: Boolean <- organizationDAOService.resetOrgDataFromCacheUsingTeamId(
        teamId = teamId,
        resetCacheInterval = SrResetCacheInterval.WithMinimumExpiryThreshold(
          seconds = AppConfig.orgDataResetFromCacheMaxTimeInSeconds
        )
      )
    } yield {
      count
    }
    
  }

  def getProspectsSavedCountForTeam(teamId: TeamId): Try[Int] = {
    teamsMetadataDAO.getProspectsSavedCountForTeam(teamId = teamId)
  }

}
