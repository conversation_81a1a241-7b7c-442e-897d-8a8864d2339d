package api.teams_metadata

import api.accounts.TeamId
import scalikejdbc.{DB, DBSession, scalikejdbcSQLInterpolationImplicitDef}

import scala.util.Try

class TeamsMetadataDAO {
  
  def insertSavedProspectsCountForTeam(teamId: TeamId, savedProspectsCount: Int)(
    implicit session: DBSession
  ): Try[Int] = Try {

      sql"""
           INSERT INTO teams_metadata(
              team_id,
              saved_prospects_count
           ) VALUES (
              ${teamId.id},
              $savedProspectsCount
           )
           ;
           """
        .update
        .apply()
  }

  def incrementOrDecrementSavedProspectsCount(teamId: TeamId, incrementOrDecrementCount: Int): Try[Int] = Try {
    DB autoCommit {implicit session =>
      sql"""
           UPDATE teams_metadata
           SET
              saved_prospects_count = saved_prospects_count + $incrementOrDecrementCount,
              updated_at = now()
           WHERE
              team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }
  }
  
  def getProspectsSavedCountForTeam(teamId: TeamId): Try[Int] = Try {
    DB readOnly {implicit session =>
      sql"""
           SELECT saved_prospects_count FROM teams_metadata
           WHERE team_id = ${teamId.id}
           ;
           """
        .map(rs => rs.intOpt("saved_prospects_count").getOrElse(0))
        .single
        .apply()
        .getOrElse(0)
    }
  }

}
