package api.triggers

import api.AppConfig
import api.accounts.dao.TeamsDAO
import api.accounts.dao_service.AccountDAOService
import api.accounts.service.OrganizationService
import api.accounts.TeamId
import api.integrations.services.TIntegrationCRMService
import api.integrations.{CRMIntegrations, FetchTokensFromDBAndRefreshAccessTokenError, GetCRMUsersError, GetLeadStatusColumnsError, IntegrationTPAccessTokenResponse, IntegrationTPColumns, IntegrationTPUsersList, TIntegrationCRMTrait, UninstallError}
import play.api.libs.ws.WSClient
import utils.SRLogger
import api.sr_audit_logs.models.EventType

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import utils.cache.models.SrResetCacheInterval


sealed trait UninstallIntegrationError

object UninstallIntegrationError {

  case class SQLException(err: Throwable) extends UninstallIntegrationError

  case class CRMTokensError(err: FetchTokensFromDBAndRefreshAccessTokenError) extends UninstallIntegrationError

  case class CRMUninstallError(err: UninstallError) extends UninstallIntegrationError
  
  case class ResetOrgDataCacheError(err: Throwable) extends UninstallIntegrationError
}

sealed trait FetchLeadStatusColumnsError

object FetchLeadStatusColumnsError {

  case class CRMTokensError(err: FetchTokensFromDBAndRefreshAccessTokenError) extends FetchLeadStatusColumnsError

  case class CRMLeadStatusColumnsError(err: GetLeadStatusColumnsError) extends FetchLeadStatusColumnsError

}


sealed trait UpdateCRMModuleLevelDNCSettingsError

object UpdateCRMModuleLevelDNCSettingsError {

  case class SQLException(err: Throwable) extends UpdateCRMModuleLevelDNCSettingsError

  case object DNCSettingsEmptyEmptyError extends UpdateCRMModuleLevelDNCSettingsError

}

sealed trait ValidateCRMTypeModuleTypeError

object ValidateCRMTypeModuleTypeError {

  case class InvalidCRMError(err: Throwable) extends ValidateCRMTypeModuleTypeError

  case class InvalidModuleError(err: Throwable)  extends ValidateCRMTypeModuleTypeError

  case class InvalidCRMForIntegrationServiceError(err: Throwable)  extends ValidateCRMTypeModuleTypeError

}

sealed trait ValidateCRMTypeModuleTypeAndGetEventTypeError

object ValidateCRMTypeModuleTypeAndGetEventTypeError {

  case class InvalidCRMError(err: Throwable) extends ValidateCRMTypeModuleTypeAndGetEventTypeError

  case class InvalidModuleError(err: Throwable)  extends ValidateCRMTypeModuleTypeAndGetEventTypeError

  case object InvalidCRMOrModuleForEventTypeError  extends ValidateCRMTypeModuleTypeAndGetEventTypeError

  case class SQLException(err: Throwable) extends ValidateCRMTypeModuleTypeAndGetEventTypeError

  case object NOTFoundSyncsError extends ValidateCRMTypeModuleTypeAndGetEventTypeError

}

sealed trait FindUpdatedUserMappingError {
  val message: String
}

object FindUpdatedUserMappingError {

  private val KEY_SQLException = "SQLException"
  private val KEY_CRMGetTpUsersError = "CRMGetTpUsersError"
  case class SQLException(err: Throwable) extends FindUpdatedUserMappingError {
    override val message: String = s"$KEY_SQLException." + Option(err.getMessage).getOrElse("No Message")
  }

  case class CRMGetTpUsersError(err: GetCRMUsersError) extends FindUpdatedUserMappingError {
    override val message: String = s"$KEY_CRMGetTpUsersError." + err.message
  }


}


case class ParsedCRMTypeModuleTypeAndIntegrationService (
                                                          crm_type: IntegrationType,
                                                          module_type: IntegrationModuleType,
                                                        )

class TriggerService(
  triggerDAO: Trigger,
  organizationService: OrganizationService,
  teamDAO: TeamsDAO,
  tIntegrationCRMService: TIntegrationCRMService
) {



  def validateAndGetParsedCRMTypeModuleTypeAndIntegrationService(
                                                       crm_type: String,
                                                       module_type: String
                          )(implicit ws: WSClient, Logger: SRLogger):
                          Either[ValidateCRMTypeModuleTypeError, ParsedCRMTypeModuleTypeAndIntegrationService] = {

    IntegrationType.withName(crm_type) match {

      case Failure(e) =>

        Left(ValidateCRMTypeModuleTypeError.InvalidCRMError(err = e))

      case Success(crmType) =>

        IntegrationModuleType.withName(module_type) match {

          case Failure(e) =>

            Left(ValidateCRMTypeModuleTypeError.InvalidModuleError(err = e))

          case Success(moduleType) =>

                Right(
                  ParsedCRMTypeModuleTypeAndIntegrationService(
                    crm_type = crmType,
                    module_type = moduleType,
                  )
                )
        }
    }
  }

  def validateAndGetParsedCRMTypeModuleTypeAndWorkflowEventName(
                                                                  crm_type: String,
                                                                  module_type: String
                                                                ):Either[ValidateCRMTypeModuleTypeAndGetEventTypeError, EventType] = {

    IntegrationType.withName(crm_type) match {

      case Failure(e) =>

        Left(ValidateCRMTypeModuleTypeAndGetEventTypeError.InvalidCRMError(err = e))

      case Success(crmType) =>

        IntegrationModuleType.withName(module_type) match {

          case Failure(e) =>

            Left(ValidateCRMTypeModuleTypeAndGetEventTypeError.InvalidModuleError(err = e))

          case Success(moduleType) =>

            (crmType, moduleType) match {

              case (IntegrationType.HUBSPOT, IntegrationModuleType.CONTACTS) =>
                Right(EventType.HUBSPOT_PROSPECT_SYNC)
              case (IntegrationType.PIPEDRIVE, IntegrationModuleType.CONTACTS) =>
                Right(EventType.PIPEDRIVE_PROSPECT_SYNC)
              case (IntegrationType.SALESFORCE, IntegrationModuleType.CONTACTS) =>
                Right(EventType.SALESFORCE_PROSPECT_SYNC)
              case (IntegrationType.SALESFORCE, IntegrationModuleType.LEADS) =>
                Right(EventType.SALESFORCE_LEAD_SYNC)
              case (IntegrationType.ZOHO, IntegrationModuleType.CONTACTS) =>
                Right(EventType.ZOHO_PROSPECT_SYNC)
              case (IntegrationType.ZOHO, IntegrationModuleType.LEADS) =>
                  Right(EventType.ZOHO_LEAD_SYNC)
              case (IntegrationType.ZOHO_RECRUIT, IntegrationModuleType.CONTACTS) =>
                Right(EventType.ZOHO_RECRUIT_PROSPECT_SYNC)
              case (IntegrationType.ZOHO_RECRUIT, IntegrationModuleType.CANDIDATES) =>
                Right(EventType.ZOHO_RECRUIT_CANDIDATE_SYNC)

              case _ =>
                Left(ValidateCRMTypeModuleTypeAndGetEventTypeError.InvalidCRMOrModuleForEventTypeError)

            }
        }
    }
  }

  def findAllCampaignTriggersByEventType(teamId: Long,
                                         permittedAccountIds: Seq[Long],
                                         eventType: EventType,
                                         logger: SRLogger
                                        ): Either[ValidateCRMTypeModuleTypeAndGetEventTypeError, Seq[CampaignSyncInDB]] = {

    triggerDAO.findAllCampaignTriggersByEventType(
      teamId = teamId,
      permittedAccountIds = permittedAccountIds,
      eventType = eventType,
      logger = logger
    ) match {

      case Failure(e) =>
        Left(ValidateCRMTypeModuleTypeAndGetEventTypeError.SQLException(err = e))

      case Success(Seq()) =>
        Left(ValidateCRMTypeModuleTypeAndGetEventTypeError.NOTFoundSyncsError)

      case Success(syncs) =>
        Right(syncs)

    }
  }


  def getLeadStatusColumns(module_type: IntegrationModuleType,
                           integration_type: IntegrationType,
                           team_id: Long
                          )(implicit ws: WSClient,
                            Logger: SRLogger,
                            ec: ExecutionContext
                          ): Future[Either[FetchLeadStatusColumnsError, Seq[IntegrationTPColumns]]] = {


   fetchLeadStatusColumnsFromCRM(
      integration_type = integration_type,
      moduleType = module_type,
      team_id = team_id
    ).map { res =>

     res

    }
  }


  def fetchLeadStatusColumnsFromCRM(
           integration_type: IntegrationType,
           moduleType: IntegrationModuleType,
           team_id: Long
         )(implicit ws: WSClient,
           Logger: SRLogger,
           ec: ExecutionContext
          ): Future[Either[FetchLeadStatusColumnsError, Seq[IntegrationTPColumns]]] = {

    tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
      teamId = team_id,
      integration_type = integration_type
    )
      .flatMap {

        case Left(err) =>

          Future.successful(
            Left(
              FetchLeadStatusColumnsError.CRMTokensError(err = err)
            )
          )

        case Right(accessTokenResponse) =>

          tIntegrationCRMService.getLeadStatusColumns(
            integrationType = integration_type,
            accessTokenData = accessTokenResponse,
            moduleType = moduleType
          )
          .map {

            case Left(err) =>

              Left(
                FetchLeadStatusColumnsError.CRMLeadStatusColumnsError(err = err)
              )

            case Right(leadStatusColumns) =>

              Right(leadStatusColumns)

          }


      }

    }





  def updateLeadStatusMapping(crm_type: IntegrationType,
                              module_type: IntegrationModuleType,
                              team_id: Long,
                              data: UpdateStatusMappingForm,
                              Logger: SRLogger
                             ):Try[Option[Long]] = {

    triggerDAO.updateLeadStatusMapping(
      teamId = team_id,
      data = data,
      integration_type = crm_type,
      module_type = module_type,
      logger = Logger
    )
  }


  def getLeadStatusMapping(crm_type: IntegrationType,
                              module_type: IntegrationModuleType,
                              team_id: Long,
                              Logger: SRLogger
                             ):Try[Option[UpdateStatusMappingForm]] = {

    triggerDAO.findLeadStatusMapping(
      teamId = team_id,
      integration_type = crm_type,
      module_type = module_type,
      logger = Logger
    )
  }


  def getCRMModuleLevelSettings(crm_type: IntegrationType,
                                module_type: IntegrationModuleType,
                                team_id: Long,
                                Logger: SRLogger
                          ):Try[Option[UpdateCRMModuleLevelSettingsForm]] = {

    triggerDAO.findCRMModuleLevelSettings(
      teamId = team_id,
      integration_type = crm_type,
      module_type = module_type,
      logger = Logger
    )
  }

  def updateCRMModuleLevelSettings(crm_type: IntegrationType,
                                   module_type: IntegrationModuleType,
                                   team_id: Long,
                                   data: UpdateCRMModuleLevelSettingsForm,
                                   Logger: SRLogger
                             ):Try[Option[Long]] = {

    triggerDAO.updateCRMModuleLevelSettings(
      teamId = team_id,
      data = data,
      integration_type = crm_type,
      module_type = module_type,
      logger = Logger
    )
  }


  def getCRMModuleLevelDNCSettings(crm_type: IntegrationType,
                                team_id: Long,
                                Logger: SRLogger
                               ):Try[Seq[UpdateCRMModuleLevelDNCSettingsForm]] = {

    triggerDAO.findCRMModuleLevelDNCSettings(
      teamId = team_id,
      integration_type = crm_type,
      logger = Logger
    )
  }

  def updateCRMModuleLevelDNCSettings(crm_type: IntegrationType,
                                   team_id: Long,
                                   data: Seq[UpdateCRMModuleLevelDNCSettingsForm],
                                   Logger: SRLogger
                                  ):Either[UpdateCRMModuleLevelDNCSettingsError, Seq[Long]] = {

    if(data.isEmpty) {

        Left(UpdateCRMModuleLevelDNCSettingsError.DNCSettingsEmptyEmptyError)

    } else {

      triggerDAO.updateCRMModuleLevelDNCSettings(
        teamId = team_id,
        data = data,
        integration_type = crm_type,
        logger = Logger
      ) match {

        case Failure(exception) =>

          Left(UpdateCRMModuleLevelDNCSettingsError.SQLException(err = exception))

        case Success(res) =>

          Right(res)
      }
    }
  }


  def getConfiguredModulesByCRM(crm_type: IntegrationType,
                           team_id: Long,
                           Logger: SRLogger
                          ):Try[Seq[CRMConfiguredModules]] = {

    triggerDAO.findConfiguredModulesByCRM(
      teamId = team_id,
      crm_type = crm_type,
      logger = Logger
    )
  }
  
  private def resetOrgDataCache(teamId: TeamId)(using logger: SRLogger): Future[Either[UninstallIntegrationError, Boolean]] = {
    organizationService.resetOrgDataFromCacheUsingTeamId(
      teamId = teamId,
      resetCacheInterval = SrResetCacheInterval.Immediately
    ) match {
      case Failure(e) =>
        Future.successful(Left(UninstallIntegrationError.ResetOrgDataCacheError(e)))

      case Success(bool) =>
        Future.successful(Right(bool))
    }
  }


  def deleteIntegration(
                         teamId: Long,
                         integration_type: IntegrationType
                       )
                       (implicit ws: WSClient,
                        ec: ExecutionContext,
                        Logger: SRLogger
                       ): Future[Either[UninstallIntegrationError, Int]] = {


    triggerDAO.deleteIntegrationByTeamIdAccountIdV3(
      teamId = teamId,
      integration_type = integration_type
    ) match {

      case Failure(e) =>

        Future.successful(Left(UninstallIntegrationError.SQLException(err = e)))

      case Success(_) =>

        tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
            teamId = teamId,
            integration_type = integration_type
          )
          .flatMap {

            case Left(err) =>

              Future.successful(
                Left(
                  UninstallIntegrationError.CRMTokensError(err = err)
                )
              )

            case Right(tokens) =>

              tIntegrationCRMService.Uninstall(
                  integrationType = integration_type,
                  accessTokenData = tokens,
                  teamId = teamId
                )
                .flatMap {

                  case Left(err1) =>
                    Future.successful(
                      Left(
                        UninstallIntegrationError.CRMUninstallError(err = err1)
                      )
                    )

                  case Right(uninstallFromCRM) =>

                    resetOrgDataCache(teamId = TeamId(teamId))
                      .map {

                        case Left(err2) =>
                          Left(err2)

                        case Right(bool) =>
                          Right(uninstallFromCRM)

                      }

                }


          }


    }

  }

  def findAllCrmIntegrationByOrgId(orgId: Long): Try[CRMIntegrations] =  Try {

      triggerDAO.findAllCRMIntegrationsByOrgID(orgId = orgId) match {

        case Failure(err) => throw err

        case Success(crms) =>
          val crm_integrations = CRMIntegrations(

            hubspot = crms.contains(IntegrationType.HUBSPOT.toString),

            pipedrive = crms.contains(IntegrationType.PIPEDRIVE.toString),

            salesforce = crms.contains(IntegrationType.SALESFORCE.toString),

            zoho = crms.contains(IntegrationType.ZOHO.toString),

            zoho_recruit = crms.contains(IntegrationType.ZOHO_RECRUIT.toString),

              calendly = false

          )
          crm_integrations

      }
    }

    def getAllIntegrations(teamId: TeamId
                          )(implicit executionContext: ExecutionContext): Future[CRMIntegrations] = {
        for {
            crmIntegrations <- Future.fromTry(triggerDAO.findAllCRMIntegrations(teamId = teamId.id))

            calendlyStatus <- Future.fromTry(teamDAO.findCalendlyIntegrationStatus(teamId = teamId))
        } yield {
            CRMIntegrations(
                hubspot = crmIntegrations.contains(IntegrationType.HUBSPOT.toString),
                pipedrive = crmIntegrations.contains(IntegrationType.PIPEDRIVE.toString),
                salesforce = crmIntegrations.contains(IntegrationType.SALESFORCE.toString),
                zoho = crmIntegrations.contains(IntegrationType.ZOHO.toString),
                zoho_recruit = crmIntegrations.contains(IntegrationType.ZOHO_RECRUIT.toString),
                calendly = calendlyStatus
            )
        }
    }


}


class TriggerServiceV2(
                        triggerDAO: Trigger,

                        accountDAOService: AccountDAOService,
                      ) {


  def getIntegrationUsersDefaultMapping(teamId: Long, tp_users: Seq[IntegrationTPUsersList], tp_user_id: Option[String]) = {

    // FIXME VALUECLASS
    val accessTeamMembers = accountDAOService.getTeamMatesByTeamId(teamId = TeamId(id = teamId))

    val tp_admin_user = if (tp_user_id.isDefined)
      tp_users.find(tp_user => tp_user.id == tp_user_id.get).getOrElse(tp_users.head)
    else
      tp_users.head

    val userMapping = accessTeamMembers.map(sr_user => {

      val tp_user = tp_users.find(tp_user => tp_user.email == sr_user.email)

      if (tp_user.isDefined) {

        TriggerUsers(
          sr_user_id = sr_user.user_id.id,
          sr_user_email = sr_user.email,
          tp_user_id = tp_user.get.id,
          tp_user_email = tp_user.get.email
        )
      } else {

        TriggerUsers(
          sr_user_id = sr_user.user_id.id,
          sr_user_email = sr_user.email,
          tp_user_id = tp_admin_user.id,
          tp_user_email = tp_admin_user.email
        )

      }
    })

    userMapping

  }

  def findUpdatedUserMapping(
                              teamId: Long,
                              integration_type: IntegrationType,
                              tpUsers: Seq[IntegrationTPUsersList]
                            )(implicit ws: WSClient,
                              ec: ExecutionContext,
                              Logger: SRLogger): Try[UpdateUserMappingForm] = {

    for {

      userMappingInDB: Option[UpdateUserMappingForm] <- {
        triggerDAO.findUserMapping(
          teamId = teamId,
          integration_type = integration_type
        )
      }

      updatedMapping: UpdateUserMappingForm <- {
        val res = if (userMappingInDB.nonEmpty) {

          val mapping = userMappingInDB.get

          if (mapping.users.isEmpty) {

            val userMapping = getIntegrationUsersDefaultMapping(teamId = teamId, tp_users = tpUsers, tp_user_id = None)

            UpdateUserMappingForm(users = Some(userMapping))

          } else {

            /* NOTE:
             As verified all the CRMs in there tp_users apis response sending owner or admin will be the first record
             So here unmapped SR users will default mapped to CRM admin
             */
            val tp_admin_user = tpUsers.head

            // FIXME VALUECLASS
            val accessMembers = accountDAOService.getTeamMatesByTeamId(teamId = TeamId(id = teamId))

            val mappedUsersWithUpdatedSRandTPUsers = accessMembers.map(am => {

              val foundInLatestSR = mapping.users.get.find(mu => mu.sr_user_id == am.user_id.id)

              if (foundInLatestSR.isDefined) {
                val foundInLatestTP = tpUsers.find(tpu => tpu.id == foundInLatestSR.get.tp_user_id)

                if (foundInLatestTP.isDefined) {
                  foundInLatestSR.get
                } else {
                  TriggerUsers(
                    sr_user_id = am.user_id.id,
                    sr_user_email = am.email,
                    tp_user_id = tp_admin_user.id,
                    tp_user_email = tp_admin_user.email
                  )
                }
              } else {
                TriggerUsers(
                  sr_user_id = am.user_id.id,
                  sr_user_email = am.email,
                  tp_user_id = tp_admin_user.id,
                  tp_user_email = tp_admin_user.email
                )
              }
            })


            UpdateUserMappingForm(users = Some(mappedUsersWithUpdatedSRandTPUsers))

          }

        } else {

          val userMapping = getIntegrationUsersDefaultMapping(teamId = teamId, tp_users = tpUsers, tp_user_id = None)

          UpdateUserMappingForm(users = Some(userMapping))

        }

        Success(res)
      }

      _: Option[Long] <- {
        triggerDAO.updateUserMapping(
          teamId = teamId,
          data = updatedMapping,
          integration_type = integration_type
        )
      }


    } yield {

      updatedMapping

    }


  }

  def gettingCRMSuccessCountForLastSixHoursOverall(): Try[List[CRMCountForLastSixHoursOverall]]= Try{

    triggerDAO.gettingCRMSuccessCountForLastSixHoursOverall()

  }

  def gettingCRMSuccessCountForLastSixHoursTeamWise(): Try[List[CRMCountForLastSixHoursTeamWise]] = Try {

    triggerDAO.gettingCRMSuccessCountForLastSixHoursTeamWise()

  }
}
