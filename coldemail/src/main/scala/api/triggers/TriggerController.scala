package api.triggers


import org.apache.pekko.actor.ActorSystem
import api.accounts._
import api.campaigns.{CampaignProspectDAO, CampaignStepDAO, CampaignStepVariantDAO, CampaignUtils}
import api.integrations._
import api.prospects.ProspectService
import api._
import api.accounts.service.{OrganizationService, ResetUserCacheUtil}
import api.prospects.service.ProspectServiceV2
import api.campaigns.models.IgnoreProspectsInOtherCampaigns
import api.campaigns.services.{CampaignProspectService, CampaignService}
import api.integrations.services.TIntegrationCRMService
import api.tags.ProspectTagDAOLegacy
import play.api.libs.json._
import play.api.libs.ws.WSClient
import play.api.mvc.{AnyContent, BaseController, ControllerComponents, Request}
import utils.helpers.LogHelpers
import utils.{Helpers, SRLogger, StringUtils}
import utils.mq.webhook.SyncTriggerMQService
import api.sr_audit_logs.models.{EventDataType, EventType}
import api.accounts.models.AccountId
import api.prospects.models.UpdateProspectType
import api.tags.models.{CampaignTagUuid, TagAndUuid}
import play.api.Logger
import utils.cache.models.SrResetCacheInterval
import utils.uuid.SrUuidUtils
import utils.uuid.services.SrUuidService
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService
import api.accounts.dao.{CalendlySettings, TeamsDAO}
import api.campaigns.services.{CalendlyWebhookService, CalendlyDisconnectionError}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class TriggerController(
                         resetUserCacheUtil: ResetUserCacheUtil,
                         protected val controllerComponents: ControllerComponents,
                         override protected val campaignStepVariantDAO: CampaignStepVariantDAO,
                         //  campaignProspectDAO: CampaignProspectDAO,
                         //  prospectDAO: Prospect,
                         prospectService: ProspectService,
                         prospectServiceV2: ProspectServiceV2,
                         //  mqTrigger: MQTrigger,
                         override protected val triggerDAO: Trigger,
                         syncTriggerMQService: SyncTriggerMQService,

                         accountService: AccountService,
                         triggerService: TriggerService,
                         pipedriveOAuth: PipedriveOAuth,
                         tIntegrationCRMService: TIntegrationCRMService,
                         override protected val campaignService: CampaignService, // required in CampaignUtils
                         override protected val campaignStepDAO: CampaignStepDAO,
                         //  authUtils: AuthUtils,
                         permissionUtils: PermissionUtils,
                         campaignProspectService: CampaignProspectService,
                         organizationService: OrganizationService,
                         srUuidUtils: SrUuidUtils,
                         prospectTagDAOLegacy: ProspectTagDAOLegacy,
                         srRollingUpdateCoreService: SrRollingUpdateCoreService,
                         teamDAO: TeamsDAO, // Inject TeamsDAO here
                         implicit val wsClient: WSClient,
                         implicit val system: ActorSystem,
                         override protected val srUuidService: SrUuidService,
                         calendlyWebhookService: CalendlyWebhookService
                       ) extends BaseController
  with TriggerAuthUtils
  with CampaignUtils {

  private implicit val ec: ExecutionContext = controllerComponents.executionContext


  def find(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_WORKFLOWS,
    tidOpt = tid
  ).async { request =>

    Future {
      val Logger = request.Logger
      val Res = request.Response

      val accountIds = request.permittedAccountIds
      val t = request.actingTeamAccount

      if (t.isEmpty) {

        Logger.fatal(s"Invalid team :: aid: $aid :: tid: $tid")

        Res.BadRequestError("Invalid team")

      } else {

        triggerDAO.find(id = id, accountIds = accountIds, teamId = t.get.team_id) match {

          case Failure(e) =>

            Logger.fatal(s"find sql : id:$id ::  accountIds: $accountIds : ${t.get.team_id}", err = e)

            Res.ServerError("There was an error. Please try again.", e = Some(e))

          case Success(None) =>

            Logger.fatal(s"Workflow not found id: $id :: accountIds: $accountIds : ${t.get.team_id}")

            Res.NotFoundError("Workflow not found")

          case Success(triggers) =>

            Res.Success("Workflow found", Json.obj(
              "triggers" -> triggers)
            )

        }
      }

    }

  }

  def findAll(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_WORKFLOWS,
    tidOpt = tid
  ).async { request =>

    Future {
      val Logger = request.Logger
      val Res = request.Response

      val accountIds = request.permittedAccountIds
      val t = request.actingTeamAccount

      if (t.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {

        val teamId = t.get.team_id

        triggerDAO.findAll(
          accountIds = Seq(t.get.user_id),
          teamId = teamId
        ) match {

          case Failure(e) =>

            Logger.fatal(s"findAll: $accountIds : ${t.get.team_id}", err = e)

            Res.ServerError("There was an error. Please try again.", e = Some(e))

          case Success(triggers) =>

            Res.Success("Workflows found", Json.obj(
              "triggers" -> triggers.sortBy(_.active).reverse)
            )

        }
      }

    }

  }


  def create(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_WORKFLOWS,
    tidOpt = tid
  ).async(parse.json) { request =>

    Future {
      val Logger = request.Logger
      val Res = request.Response

      val ta = request.actingTeamAccount

      if (ta.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {

        val t = ta.get

        val validateData = request.body.validate[CreateTriggerForm]

        validateData match {

          case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

          case JsSuccess(data, _) =>

            if (data.actions.isDefined && data.actions.get.exists(_.action_app_type != IntegrationType.SMARTREACH)) {

              val not_allowed_app = data.actions.get.find(_.action_app_type != IntegrationType.SMARTREACH).get.action_app_type
              Res.BadRequestError(s"$not_allowed_app is not allowed in the action app, This feature is deprecated.")
            } else {
              var createDat = CreateTriggerForm(
                label = data.label,
                event = data.event,
                conditions = data.conditions,
                actions = data.actions,
                campaign_id = data.campaign_id,
                source = data.source,
                active = data.active
              )

              triggerDAO.createBasic(accountId = t.user_id, teamId = t.team_id, data = createDat) match {

                case Failure(e) =>

                  Logger.fatal(s"Error while creating trigger:${data}", err = e)

                  Res.ServerError(s"Error while creating workflow.", e = Some(e))

                case Success(None) =>

                  Logger.fatal(s"Error while creating trigger:${data} : case None")

                  Res.ServerError(s"Error while creating workflow.", e = None)

                case Success(Some(row)) => Res.Success("Congrats! A new workflow has been added.", Json.obj("trigger" -> row))

              }

            }


        }

      }
    }
  }


  // fixme triggers: must check hasTrigger under checkPermission itself
  def update(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_WORKFLOWS,
      tidOpt = tid
    )
      andThen hasTrigger(id)
    ).async(parse.json) { request =>

    Future {

      val Logger = request.Logger
      val Res = request.Response

      val t = request.actingTeamAccount
      val trigger = request.trigger

      val hasIncompleteAction: Option[(JsValue, Int)] = (request.body \ "actions").asOpt[Seq[JsValue]].flatMap(actions => {
        val incompleteAction = actions.zipWithIndex.find {
          case (action, index) => {
            val actionType = (action \ "action_type").asOpt[String]
            actionType.isEmpty || actionType.get.trim.isEmpty
          }
        }
        incompleteAction
      })

      if (hasIncompleteAction.isDefined) {
        val actionIndex = hasIncompleteAction.get._2
        Res.BadRequestError(s"Action #${actionIndex + 1}: Invalid action ")

      } else {


        val validateData = request.body.validate[UpdateTriggerForm]

        validateData match {

          case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

          case JsSuccess(data, _) =>

            var error: Option[String] = None

            if (data.actions.isDefined && data.actions.get.exists(_.action_app_type != IntegrationType.SMARTREACH)) {

              val not_allowed_app = data.actions.get.find(_.action_app_type != IntegrationType.SMARTREACH).get.action_app_type
              Res.BadRequestError(s"$not_allowed_app is not allowed in the action app, This feature is deprecated.")
            } else if (data.event_app_type.isDefined && data.event_app_type.get != IntegrationType.SMARTREACH && !triggerDAO.isCRMIntegrated(teamId = t.team_id, data.event_app_type.get).get) {
              error = Some(s"Selected event app ${data.event_app_type} is not integrated try again once after integrated")
            } else if (data.actions.isDefined) {
              data.actions.get.zipWithIndex.foreach {
                case (ac, index) => {
                  if (ac.action_app_type != IntegrationType.SMARTREACH && !triggerDAO.isCRMIntegrated(teamId = t.team_id, ac.action_app_type).get) {
                    error = Some(s"Action #${index + 1}: Selected action app ${ac.action_app_type} is not integrated try again once after integrated")
                  } else if (ac.tags.isDefined && ac.tags.get.isEmpty) {
                    error = Some(s"Action #${index + 1}: Tags cannot be empty")
                  } else if (ac.fields.isDefined && ac.fields.get.length < 1) {
                    error = Some(s"Action #${index + 1}: Fields cannot be empty")
                  } else if (ac.fields.isDefined) {
                    val emailField = ac.fields.get.find(f => f.tp_field.toLowerCase.trim == "email" && f.sr_field.toLowerCase.trim == "email")
                    if (emailField.isEmpty) {
                      error = Some(s"Action #${index + 1}: Email field must map to email for this action")
                    }
                  }
                }
              }
            }

            if (error.isDefined) {

              Logger.fatal(s"err: ${error.get} :: data:${data}")

              Res.BadRequestError(s"${error.get}")

            } else {

              triggerDAO.update(
                id = trigger.id,
                permittedAccountIds = request.permittedAccountIds,
                teamId = t.team_id,
                data = data
              ) match {

                case Failure(e) =>

                  Logger.fatal(s"Error while updating trigger:${data}", err = e)

                  Res.ServerError(s"Error while updating workflow.", e = Some(e))


                case Success(None) =>

                  Res.NotFoundError("Workflow not found. Could you reload and try again?")

                case Success(Some(row)) =>


                  //TODO recheck bellow code block
                  //Creating Tags for tags realated action
                  row.actions match {
                    case None => Res.NotFoundError("No Action found for the workflow")
                    case Some(value) =>
                      value.foreach(a => {

                        if (TriggerUtils.isTagsRelatedAction(action = a.action_type)) {

                          val tags = a.tags.get.split(",").toSeq
                          val tagsAndUuids: Seq[TagAndUuid] = tags.map(t => {
                            val camapaign_tag_uuid = srUuidUtils.generateTagsUuid()
                            TagAndUuid(tag = t, uuid = CampaignTagUuid(camapaign_tag_uuid))
                          })
                          prospectTagDAOLegacy.findOrCreateTags(
                            tags = tagsAndUuids,
                            teamId = row.team_id,
                            accountId = row.owner_id
                          ) match {

                            case Failure(e) =>

                              Logger.fatal(s"TriggerController.update Tag.findOrCreateTags Error Tags:${
                                a.tags.get
                              }", err = e)

                            case Success(value) =>

                              Logger.info(s"TriggerController.update Tag.findOrCreateTags Success Tags:${
                                a.tags.get
                              }")
                          }

                        }
                      })


                      Res.Success("Workflow has been updated.", Json.obj("trigger" -> row))
                  }

              }
            }

        }

      }
    }
  }


  // fixme triggers: id must be used from the url, add hasTrigger check below checkPermission itself
  def delete(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_WORKFLOWS,
      tidOpt = tid
    )
      andThen hasTrigger(id)
    ).async(parse.json) { request =>
    Future {

      val Res = request.Response
      val trigger = request.trigger

      val t = request.actingTeamAccount
      val accountIds = request.permittedAccountIds

      triggerDAO.delete(
        ids = Seq(trigger.id),
        permittedAccountIds = accountIds,
        teamId = t.team_id
      ) match {

        case Failure(e) =>

          Res.ServerError(s"Error while deleting workflow.", e = Some(e))

        case Success(res) =>

          Res.Success("workflow has been deleted.", Json.obj())

      }

    }
  }


  // fixme triggers: id must be used from the url, add hasTrigger check below checkPermission itself
  def activate(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_WORKFLOWS,
      tidOpt = tid
    )
      andThen hasTrigger(id)
    ).async(parse.json) { request =>
    Future {
      val Logger = request.Logger
      val Res = request.Response

      val trigger = request.trigger

      if (trigger.event.isEmpty) {

        Res.BadRequestError(s"Please select event for the workflow, Incomplete workflow cannot be activated")

      }
      else if (trigger.actions.isEmpty) {

        Res.BadRequestError(s"Please select at least one action for the workflow, Incomplete workflow cannot be activated")

      }

      else {

        triggerDAO.updateTriggerActiveStatus(id = trigger.id, isActive = true) match {

          case Failure(e) =>

            Res.ServerError(s"Error while activating workflow.", e = Some(e))

          case Success(None) =>

            Res.NotFoundError("Workflow not found. Could you reload and try again?")

          case Success(Some(updatedTriggerRow)) =>

            triggerDAO.clearErrorV2(id = updatedTriggerRow.id) match {

              case Failure(e) =>

                Res.ServerError(s"Error while activating workflow.", e = Some(e))

              case Success(None) =>

                Logger.fatal(s"Workflow not found error while clearError trigger:${trigger}")

                Res.NotFoundError("Workflow not found. Could you reload and try again?")

              case Success(Some(row)) =>

                if (TriggerUtils.isSyncTrigger(trigger.event.get)) {

                  val msg = EventDataType.getEventDataFromMQTriggerSyncMessage(triggerId = trigger.id, eventType = trigger.event.get).get

                  syncTriggerMQService.publishSyncTrigger(
                    message = msg,
                    integrationType = trigger.event_app_type.get,
                    eventType = trigger.event.get,
                    teamId = TeamId(trigger.team_id),
                    accountId = AccountId(trigger.owner_id)
                  )(Logger = Logger)

                }

                Res.Success("workflow has been activated.", Json.obj("trigger" -> updatedTriggerRow))

            }

        }
      }

    }
  }


  // fixme triggers: id must be used from the url, add hasTrigger check below checkPermission itself
  def deactivate(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_WORKFLOWS,
      tidOpt = tid
    )
      andThen hasTrigger(id)
    ).async(parse.json) { request =>
    Future {
      val Logger = request.Logger
      val Res = request.Response

      val trigger = request.trigger

      triggerDAO.updateTriggerActiveStatus(id = trigger.id, isActive = false) match {

        case Failure(e) =>

          Res.ServerError(s"Error while deactivating workflow.", e = Some(e))

        case Success(None) =>

          Logger.fatal(s"TriggerController.updateTriggerActiveStatus :: Invalid trigger id")

          Res.BadRequestError(s"Invalid workflow id ${trigger.id}")

        case Success(Some(row)) =>

          Res.Success("workflow has been deactivated.", Json.obj("trigger" -> row))

      }

    }
  }


  def updateSharedWithTeam(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_WORKFLOWS,
      tidOpt = tid
    )
      andThen hasTrigger(id)
    ).async(parse.json) { request =>

    Future {

      val Res = request.Response

      val trigger = request.trigger

      val shared_with_team = (request.body \ "shared_with_team").asOpt[Boolean]

      if (shared_with_team.isEmpty) {
        Res.BadRequestError(s"Please send valid value for shared_with_team")
      } else {

        triggerDAO.updateSharedWithTeamPermission(
          id = trigger.id,
          shared_with_team = shared_with_team.get
        ) match {

          case Failure(e) =>

            Res.ServerError(s"Error while updating workflow shared with team permission.", e = Some(e))

          case Success(None) =>

            Res.ServerError(s"Error while updating workflow shared with team permission. [2]", e = None)

          case Success(row) =>

            Res.Success("Updated workflow shared with team permission.", Json.obj("trigger" -> row))

        }
      }

    }
  }

  def syncTrigger(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_WORKFLOWS,
      tidOpt = tid
    )
      andThen hasTrigger(id)
    ).async(parse.json) { request =>

    Future {

      val Res = request.Response

      val trigger = request.trigger

      if (!trigger.active) {

        Res.BadRequestError(s"Please activate the workflow first")

      } else if (trigger.in_queue_for_sync) {

        Res.BadRequestError(s"Workflow is already in queue for sync")

      } else {

        if (trigger.event.isDefined && trigger.event_app_type.isDefined) {

          val msg = EventDataType.getEventDataFromMQTriggerSyncMessage(triggerId = trigger.id, eventType = trigger.event.get).get

          syncTriggerMQService.publishSyncTrigger(
            message = msg,
            integrationType = trigger.event_app_type.get,
            eventType = trigger.event.get,
            teamId = TeamId(trigger.team_id),
            accountId = AccountId(trigger.owner_id)
          )(Logger = request.Logger)

          Res.Success("Syncing has started now.", Json.obj("trigger" -> trigger))

        } else {

          Res.BadRequestError(s"Invalid event or integration")

        }


      }


    }
  }


  def fetchTPColumns(v: String, aid: Option[Long], tid: Option[Long], crm_type: String, module_type: Option[String]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_WORKFLOWS,
    tidOpt = tid
  ).async { request =>

    given Logger: SRLogger = request.Logger
    val Res = request.Response
    val ta = request.actingTeamAccount

    if (ta.isEmpty) {

      Future.successful(Res.BadRequestError("Invalid team"))

    } else {

      IntegrationType.withName(crm_type) match {

        case Failure(e) =>

          Future.successful(Res.BadRequestError("Invalid CRM Type"))

        case Success(integration_type) =>

          val getColAndSample = tIntegrationCRMService
            .getSampleContactData(
              integration_type = integration_type,
              module_type = module_type,
              teamId = ta.get.team_id
            )

          getColAndSample
            .map(resData => {

              Res.Success("Columns found", Json.obj(
                "columns" -> resData.columns,
                "sample_data" -> resData.res
              ))

            })

            .recover {

              case e =>

                Logger.fatal(s"crmIntegrationService: $crm_type", err = e)
                Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))
            }
      }


    }

  }


  def getLeadStatusColumns(
                            v: String,
                            tid: Option[Long],
                            crm_type: String,
                            module_type: String
                          ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_WORKFLOWS,
    tidOpt = tid
  ).async { request =>

    given Logger: SRLogger = request.Logger
    val Res = request.Response
    val ta = request.actingTeamAccount

    if (ta.isEmpty) {

      Future.successful(Res.BadRequestError("Invalid team"))

    } else {

      triggerService.validateAndGetParsedCRMTypeModuleTypeAndIntegrationService(
        crm_type = crm_type,
        module_type = module_type
      ) match {

        case Left(ValidateCRMTypeModuleTypeError.InvalidCRMError(e)) =>

          Future.successful(Res.BadRequestError(e.getMessage))

        case Left(ValidateCRMTypeModuleTypeError.InvalidModuleError(e)) =>

          Future.successful(Res.BadRequestError(e.getMessage))

        case Left(ValidateCRMTypeModuleTypeError.InvalidCRMForIntegrationServiceError(e)) =>

          Future.successful(Res.BadRequestError(e.getMessage))

        case Right(data) =>

          triggerService.getLeadStatusColumns(
            integration_type = data.crm_type,
            module_type = data.module_type,
            team_id = ta.get.team_id
          ).flatMap {


            case Left(FetchLeadStatusColumnsError.CRMTokensError(err)) =>
              Future.successful(
                Res.BadRequestError(err.message)
              )

            case Left(FetchLeadStatusColumnsError.CRMLeadStatusColumnsError(err)) =>

              Future.successful(
                Res.NotFoundError(err.message)
              )

            case Right(resData) =>

              Future.successful(Res.Success("Columns found", Json.toJson(
                Json.obj("columns" -> resData)
              ))
              )


          }
      }


    }

  }


  def fetchTPFilters(v: String, aid: Option[Long], tid: Option[Long], crm_type: String, module_type: Option[String]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_WORKFLOWS,
    tidOpt = tid
  ).async { request =>

    given Logger: SRLogger = request.Logger
    val Res = request.Response
    val ta = request.actingTeamAccount

    if (ta.isEmpty) {

      Future.successful(Res.BadRequestError("Invalid team"))

    } else {

      IntegrationType.withName(crm_type) match {

        case Failure(e) =>

          Future.successful(Res.BadRequestError("Invalid CRM Type"))

        case Success(integration_type) =>

          tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
              teamId = ta.get.team_id,
              integration_type = integration_type
            ).flatMap {

              case Left(_) =>
                /*TODO_EitherT handle */
                Future.successful(Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, e = None))

              case Right(accessTokenResponse) =>

                tIntegrationCRMService.getTPFilters(
                  integrationType = integration_type,
                  accessTokenData = accessTokenResponse,
                  module_type = module_type
                ).map {

                  case Left(error) =>
                    Res.NotFoundError(error.message)

                  case Right(filters) =>

                    Res.Success("Filters found", Json.obj(
                      "filters" -> filters
                    ))

                }

            }
            .recover {

              case e: TPInvalidGrantException =>
                Res.ServerError(e.getMessage, e = Some(e))

              case e =>

                Logger.fatal(s"fetchTPFilters: crm :: crmIntegrationService: $integration_type :: aid: $aid :: tid: $tid", err = e)

                Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, e = Some(e))
            }
      }


    }

  }


  def getTriggerEventsObj(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_WORKFLOWS,
    tidOpt = tid
  ).async { request =>

    val Logger = request.Logger
    val Res = request.Response
    Future.successful(Res.Success(
      "Events found",
      Json.obj(

        "allowed_events" -> SRTriggerAllowedCombos.allowedTriggerActions,
        "allowed_events_for_webhooks" -> SRTriggerAllowedCombos.allowedTriggerActionsForWebhooks,

        "allowed_apps" -> Seq(
          SRTriggerAllowedCombos._smartreachApp,
          SRTriggerAllowedCombos._hubspotApp,
          SRTriggerAllowedCombos._pipedriveApp,
          SRTriggerAllowedCombos._zohoApp,
          SRTriggerAllowedCombos._zohoRecruitApp,
          SRTriggerAllowedCombos._salesforceApp
        )
      )
    ))


  }

  def fetchEventsFilters(
                          v: String,
                          aid: Option[Long],
                          tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_WORKFLOWS,
    tidOpt = tid
  ).async { request =>

    val Res = request.Response
    Future.successful(Res.Success(
      "Event filters found",
      Json.obj(
        "filters" -> EventType.getFiltersForEvents()
    )))
  }



  def updateTPFieldsMapping(v: String, crm_type: String, module_type: Option[String], aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_WORKFLOWS,
    tidOpt = tid
  ).async((parse.json)) { request =>

    Future {

      val Logger = request.Logger
      val Res = request.Response
      val ta = request.actingTeamAccount

      if (ta.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {

        IntegrationType.withName(crm_type) match {

          case Failure(e) =>

            Logger.fatal(s"updateTPFieldsMapping: Invalid crm_type: $crm_type :: aid: $aid :: tid: $tid", err = e)

            Res.BadRequestError("Invalid CRM Type")

          case Success(integration_type) =>

            if (module_type.isEmpty) {

              Logger.fatal(s"updateTPFieldsMapping: Empty module_type: $module_type :: aid: $aid :: tid: $tid")

              Res.BadRequestError("Module cannot be empty")

            } else {

              IntegrationModuleType.withName(module_type.get) match {

                case Failure(e) =>

                  Logger.fatal(s"updateTPFieldsMapping: Invalid module_type: $module_type :: aid: $aid :: tid: $tid", err = e)

                  Res.BadRequestError("Invalid Module Type")

                case Success(moduleType) =>

                  val t = ta.get

                  val validateData = request.body.validate[UpdateFieldsMappingForm]

                  validateData match {

                    case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

                    case JsSuccess(data, _) =>

                      if ((data.fields.isDefined && data.fields.get.nonEmpty)) {

                        val mapData = data

                        val emailField = data.fields.get.find(f => f.tp_field.toLowerCase.trim == "email" && f.sr_field.toLowerCase.trim == "email")

                        //NOTE checking tpfield duplicates and not allowing if there. bcz we cannot send multiple columns data for crms
                        val tpFields = data.fields.get.filter(f => {
                          f.tp_field.nonEmpty
                        }).map(fields =>
                          fields.tp_field
                        ).groupBy(f => f).map {
                          case (field, groupField) =>
                            (field, groupField.length)
                        }

                        val tpFieldsMultiple = tpFields.filter(tpf => {
                          tpf._2 > 1
                        }).map(tpf => tpf._1).toList

                        if (emailField.isEmpty) {

                          Res.BadRequestError(s"Email field must map to email for this action")

                        } else if (tpFieldsMultiple.nonEmpty) {

                          Res.BadRequestError(s"You have mapped these $crm_type column(s) more than once ${tpFieldsMultiple.mkString(", ")}. Please ensure any $crm_type column is mapped at most once. If you any queries, Please contact support.")

                        } else {

                          //filtering this to save only mapped fields in DB
                          mapData.copy(
                            fields = Some(data.fields.get.filter(f => {
                              f.sr_field.nonEmpty
                            }))
                          )

                        }

                        triggerDAO.updateFieldMapping(
                          teamId = t.team_id,
                          data = mapData,
                          integration_type = integration_type,
                          module_type = moduleType
                        ) match {

                          case Failure(e) =>

                            Logger.fatal(s"updateTPFieldsMapping Error while updating mapping:${data} :: aid: $aid :: tid: $tid", err = e)

                            Res.ServerError(s"Error while updating mapping.", e = Some(e))

                          case Success(None) =>

                            Res.NotFoundError("Workflow not found. Could you reload and try again?")

                          case Success(Some(row)) =>

                            Res.Success("Field mapping has been updated.", Json.obj())

                        }

                      } else {
                        Res.BadRequestError(s"Invalid mapping")
                      }
                  }
              }
            }
        }

      }
    }

  }

  def getTPFieldsMapping(v: String, crm_type: String, module_type: Option[String], aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_WORKFLOWS,
    tidOpt = tid
  ).async { request =>
    Future {
      val Logger = request.Logger
      val Res = request.Response
      val accountIds = request.permittedAccountIds
      val t = request.actingTeamAccount

      if (t.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {

        IntegrationType.withName(crm_type) match {

          case Failure(e) =>

            Logger.fatal(s"getTPFieldsMapping: Invalid crm_type: $crm_type :: aid: $aid :: tid: $tid", err = e)

            Res.BadRequestError("Invalid CRM Type")

          case Success(integration_type) =>

            if (module_type.isEmpty) {

              Logger.fatal(s"getTPFieldsMapping: Empty module_type: $module_type :: aid: $aid :: tid: $tid")

              Res.BadRequestError("Module cannot be empty")

            } else {

              IntegrationModuleType.withName(module_type.get) match {

                case Failure(e) =>

                  Logger.fatal(s"getTPFieldsMapping: Invalid module_type: $module_type :: aid: $aid :: tid: $tid", err = e)

                  Res.BadRequestError("Invalid Module Type")

                case Success(moduleType) =>

                  triggerDAO.findFieldMapping(teamId = t.get.team_id, integration_type = integration_type, module_type = moduleType) match {

                    case Failure(e) =>

                      Logger.fatal(s"getTPFieldsMapping: $accountIds : ${t.get.team_id}", err = e)

                      Res.ServerError("There was an error. Please try again.", e = Some(e))

                    case Success(mapping) =>

                      if (mapping.nonEmpty) {

                        val defaultFiledMapping = TriggerUtils.getIntegrationDefaultMapping(teamId = t.get.team_id, integration_type = integration_type, module_type = moduleType)

                        val allFieldNamesInDb = (
                          mapping.get.fields.get.map(_.sr_field) ++ defaultFiledMapping.map(_.sr_field)).distinct

                        val fieldsInDBMapping = mapping.get.fields.get
                        val fieldNamesInDBMapping = fieldsInDBMapping.map(_.sr_field)
                        val newCustomFieldsNotInDBMapping = defaultFiledMapping.filter(field => {

                          !fieldNamesInDBMapping.contains(field.sr_field)

                        })

                        val mappingFields = fieldsInDBMapping ++ newCustomFieldsNotInDBMapping

                        val mappingWithMergedNewNewFieldsCheck = UpdateFieldsMappingForm(
                          fields = Some(mappingFields)
                        )

                        Res.Success("Mapping found", Json.toJson(mappingWithMergedNewNewFieldsCheck))


                      } else {

                        //this case won't exists bcz while integration only we are building default mapping and saving
                        Res.Success("Mapping not found", Json.obj())

                      }

                  }

              }


            }
        }
      }

    }

  }


  def getTPUserMapping(v: String, crm_type: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_WORKFLOWS,
    tidOpt = tid
  ).async { request =>

    given Logger: SRLogger = request.Logger
    val Res = request.Response
    val t = request.actingTeamAccount

    if (t.isEmpty) {

      Future.successful(Res.BadRequestError("Invalid team"))

    } else {

      val teamId = t.get.team_id
      val userId = t.get.user_id

      IntegrationType.withName(crm_type) match {

        case Failure(e) =>

          Logger.fatal(s"getTPFieldsMapping: Invalid crm_type: $crm_type :: aid: $aid :: tid: $tid", err = e)

          Future.successful(Res.BadRequestError("Invalid CRM Type"))

        case Success(integration_type) =>

          tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
              teamId = teamId,
              integration_type = integration_type
            ).flatMap {

              case Left(_) =>
                /*TODO_EitherT handle */
                Future.successful(Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, e = None))

              case Right(accessTokenResponse) =>

                tIntegrationCRMService.findUpdatedUserMapping(
                    accessTokenData = accessTokenResponse,
                    teamId = teamId,
                    userId = userId,
                    integration_type = integration_type
                  )
                  .flatMap({

                    case Left(err) =>
                      /*LD_STS TODO_EitherT handle */
                      Logger.fatal(s"getTPUserMapping: findUpdatedUserMapping:  crm :: crmIntegrationService: $integration_type :: aid: $aid :: tid: $tid error ${err.toString}")
                      Future.successful(Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, e = None))

                    case Right(mapping) =>
                      Future.successful(Res.Success("Mapping found", Json.toJson(mapping)))

                  })

            }
            .recover {

              case e: TPInvalidGrantException =>
                Res.ServerError(e.getMessage, e = Some(e))

              case e =>

                Logger.fatal(s"getTPUserMapping: crm :: crmIntegrationService: $integration_type :: aid: $aid :: tid: $tid", err = e)

                Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, e = Some(e))
            }
      }

    }
  }


  def updateTPUserMapping(v: String, crm_type: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_WORKFLOWS,
    tidOpt = tid
  ).async((parse.json)) { request =>

    Future {

      val Logger = request.Logger
      val Res = request.Response
      val ta = request.actingTeamAccount

      if (ta.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {

        IntegrationType.withName(crm_type) match {

          case Failure(e) =>

            Logger.fatal(s"updateTPFieldsMapping: Invalid crm_type: $crm_type :: aid: $aid :: tid: $tid", err = e)

            Res.BadRequestError("Invalid CRM Type")

          case Success(integration_type) =>

            val t = ta.get

            val validateData = request.body.validate[UpdateUserMappingForm]

            validateData match {

              case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

              case JsSuccess(data, _) =>

                var error: Option[String] = None

                if (data.users.isDefined && data.users.get.nonEmpty) {

                  triggerDAO.updateUserMapping(teamId = t.team_id, data = data, integration_type = integration_type) match {

                    case Failure(e) =>

                      Logger.fatal(s"updateTPFieldsMapping Error while updating mapping:${data} :: aid: $aid :: tid: $tid", err = e)

                      Res.ServerError(s"Error while updating mapping.", e = Some(e))

                    case Success(None) =>

                      Res.NotFoundError("Workflow not found. Could you reload and try again?")

                    case Success(Some(row)) =>

                      Res.Success("Field mapping has been updated.", Json.obj())

                  }

                } else {

                  Res.BadRequestError(s"Invalid mapping")

                }
            }

        }

      }
    }

  }


  def getLeadStatusMapping(
                            v: String,
                            crm_type: String,
                            module_type: String,
                            tid: Option[Long]
                          ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_WORKFLOWS,
    tidOpt = tid
  ).async { request =>

    Future {

      given Logger: SRLogger = request.Logger
      val Res = request.Response
      val t = request.actingTeamAccount


      if (t.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {

        val teamId = t.get.team_id

        IntegrationType.withName(crm_type) match {

          case Failure(e) =>

            Logger.fatal(s"getLeadStatusMapping: Invalid crm_type: $crm_type :: tid: $tid", err = e)

            Res.BadRequestError("Invalid CRM Type")

          case Success(integration_type) =>

            IntegrationModuleType.withName(module_type) match {

              case Failure(e) =>

                Logger.fatal(s"getLeadStatusMapping: Invalid module_type: $module_type :: tid: $tid", err = e)

                Res.BadRequestError("Invalid Module Type")

              case Success(moduleType) =>

                triggerService.getLeadStatusMapping(
                  crm_type = integration_type,
                  module_type = moduleType,
                  team_id = teamId,
                  Logger = Logger
                ) match {

                  case Failure(e) =>

                    Logger.fatal(s"getLeadStatusMapping: $teamId : ${t.get.team_id}", err = e)

                    Res.ServerError("There was an error. Please try again.", e = Some(e))

                  case Success(statusMapping) =>

                    Res.Success("Mapping found", Json.toJson(statusMapping))
                }
            }
        }

      }
    }
  }


  def updateLeadStatusMapping(
                               v: String,
                               crm_type: String,
                               module_type: String,
                               tid: Option[Long]
                             ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_WORKFLOWS,
    tidOpt = tid
  ).async((parse.json)) { request =>

    Future {

      val Logger = request.Logger
      val Res = request.Response
      val ta = request.actingTeamAccount

      if (ta.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {

        IntegrationType.withName(crm_type) match {

          case Failure(e) =>

            Logger.fatal(s"updateTPFieldsMapping: Invalid crm_type: $crm_type :: tid: $tid", err = e)

            Res.BadRequestError("Invalid CRM Type")

          case Success(integration_type) =>

            IntegrationModuleType.withName(module_type) match {

              case Failure(e) =>

                Logger.fatal(s"updateTPFieldsMapping: Invalid module_type: $module_type :: tid: $tid", err = e)

                Res.BadRequestError("Invalid Module Type")

              case Success(moduleType) =>

                val t = ta.get

                val validateData = request.body.validate[UpdateStatusMappingForm]

                validateData match {

                  case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

                  case JsSuccess(data, _) =>

                    if (data.crm_column.isEmpty) {

                      Res.BadRequestError(s"Invalid status column")

                    } else if (data.activity_to_status_mapping.isEmpty || (data.activity_to_status_mapping.isDefined && data.activity_to_status_mapping.get.isEmpty)) {

                      Res.BadRequestError(s"Invalid mapping activity to status mapping")

                    } else if (data.category_to_status_mapping.isEmpty || (data.category_to_status_mapping.isDefined && data.category_to_status_mapping.get.isEmpty)) {

                      Res.BadRequestError(s"Invalid mapping category to status mapping")

                    } else {

                      triggerService.updateLeadStatusMapping(
                        crm_type = integration_type,
                        module_type = moduleType,
                        team_id = t.team_id,
                        data = data,
                        Logger = Logger
                      ) match {

                        case Failure(e) =>

                          Logger.fatal(s"updateTPFieldsMapping Error while updating mapping:${data} :: tid: $tid", err = e)

                          Res.ServerError(s"Error while updating mapping.", e = Some(e))

                        case Success(None) =>

                          Res.NotFoundError("Mapping not found. Could you reload and try again?")

                        case Success(Some(_)) =>

                          Res.Success("Field mapping has been updated.", Json.obj())

                      }

                    }
                }
            }

        }

      }
    }

  }


  //FIXME should optimize this funciton discuss with prateek
  //FIXME should flexible for other extension like zoho, hubspot
  // use case : TriggerController.manageTPExtensionData
  def manageTPExtensionData(v: String, crm_type: String, action: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,

    // should check edit_prospects permission because this deals with creating/assigning/unassigning prospects
    // however, for Campaign.find check edit_campaigns permission below
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = tid
  ).async(parse.json) { request =>

    given Logger: SRLogger = request.Logger
    val Res = request.Response
    val ta = request.actingTeamAccount

    if (ta.isEmpty) {

      Future.successful(Res.BadRequestError("Invalid team"))

    } else {

      val teamId = ta.get.team_id
      val accountId = ta.get.user_id

      val campaignId = (request.body \ "camapign_id").asOpt[Long]
      val companyId = (request.body \ "companyId").asOpt[String]
      val userId = (request.body \ "userId").asOpt[String]
      val selectedIds = (request.body \ "selectedIds").asOpt[String]
      val excludedIds = (request.body \ "excludedIds").asOpt[String]
      val filterKey = (request.body \ "filter").asOpt[String]

      val filterId = if (filterKey.isDefined && filterKey.nonEmpty) {
        (Json.parse(filterKey.get) \ "filter_id").asOpt[Long]
      } else None

      val filter = if (filterId.isDefined) Some(filterId.get.toString) else None

      // _ at end denotes a curried function ::
      // REF: https://dzone.com/articles/currying-functions-in-scala-1
      val getPermittedAccountIds = PermissionMethods
        .getPermittedAccountIdsForAccountAndPermission(
          loggedinAccount = request.loggedinAccount,
          actingTeamId = teamId,
          actingAccountId = accountId,
          version = "v2", // assuming
          Logger = Logger
        )

      if (companyId.nonEmpty && userId.nonEmpty) {
        IntegrationType.withName(crm_type) match {

          case Failure(e) =>

            Logger.fatal(s"manageTPExtensionData: Invalid crm_type: $crm_type :: aid: $aid :: tid: $tid")

            Future.successful(Res.BadRequestError("Invalid CRM Type."))

          case Success(integration_type) =>

            triggerDAO.findTokensByIntegrationAndCompanyId(
              integration_type = integration_type,
              companyId = companyId.get,
              userId = userId.get,
              teamId = teamId
            ) match {

              case Failure(e) =>

                Logger.fatal(s"manageTPExtensionData findTokensByIntegrationAndCompanyId there was an error. Please try again: $crm_type :: companyId: $companyId :: userId: ${userId.get}", err = e)

                Future.successful(Res.ServerError("There was an error. Please try again.", e = Some(e)))

              case Success(None) =>

                Logger.fatal(s"manageTPExtensionData findTokensByIntegrationAndCompanyId no integration found with this CompanyId: $companyId and userId: $userId")

                Future.successful(Res.NotFoundError(s"No ${integration_type.toString.capitalize} integration found with this company Id: ${companyId.get}"))

              case Success(Some(tokensData)) =>

                Try {
                  val selectedIdsSeq = if (selectedIds.isDefined && selectedIds.get.nonEmpty) selectedIds.get.split(",").map(_.trim.toLong).toSeq else Seq()
                  val excludedIdsSeq = if (excludedIds.isDefined && excludedIds.get.nonEmpty) excludedIds.get.split(",").map(_.trim.toLong).toSeq else Seq()

                  (selectedIdsSeq, excludedIdsSeq)

                } match {

                  case Failure(e) =>

                    Logger.fatal(s"manageTPExtensionData: Invalid selectedIds or excludedIds: $crm_type :: aid: $aid :: tid: $tid", err = e)

                    Future.successful(Res.BadRequestError("Invalid selectedIds or excludedIds."))

                  case Success(ids) =>

                    val selectedIdsSeq = ids._1
                    val excludedIdsSeq = ids._2

                    pipedriveOAuth.refreshAccessToken(refreshTokenData = tokensData)(Logger = Logger.appendLogRequestId("refreshAccessToken"), ws = wsClient, ec = ec)
                      .flatMap {
                        case Left(_) =>
                          Future.successful(Res.ServerError(s"Error while creating prospect", e = None))

                        case Right(accessTokenResponse) =>


                          integration_type match {

                            case IntegrationType.PIPEDRIVE =>

                              action match {

                                case "add_to_smartreach" =>


                                  pipedriveOAuth.getPersonsTotalCount(accessTokenData = accessTokenResponse)
                                    .flatMap { total_person_count =>

                                      if (total_person_count.isDefined) {

                                        if (
                                          (selectedIdsSeq.nonEmpty && selectedIdsSeq.length < 1000) ||
                                            total_person_count.get < 15000
                                        ) { //FIXME this should in global level

                                          pipedriveOAuth.getPersons(accessTokenData = accessTokenResponse,
                                              teamId = teamId,
                                              accountId = accountId,
                                              selectedIdsSeq = selectedIdsSeq,
                                              excludedIdsSeq = excludedIdsSeq,
                                              filter = filter,
                                              crm_type = crm_type,
                                              action = action
                                            )
                                            .flatMap { prospectsToBeCreate =>

                                              Logger.info(s"prospectsToBeCreate ${prospectsToBeCreate.length}")

                                              val org_id = request.loggedinAccount.org.id

                                              if (prospectsToBeCreate.nonEmpty) {

                                                prospectService.createOrUpdateProspects(
                                                  ownerAccountId = accountId,
                                                  teamId = teamId,
                                                  listName = None,
                                                  prospects = prospectsToBeCreate,
                                                  updateProspectType = UpdateProspectType.ForceUpdate,
                                                  ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
                                                  doerAccount = request.loggedinAccount,
                                                  prospectSource = Some(pipedriveOAuth.prospectSource),
                                                  prospectAccountId = None,
                                                  prospect_tags = None,


                                                  campaign_id = None,
                                                  ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
                                                  deduplicationColumns = None,

                                                  /**
                                                   * FIXME AUDITLOGFIXNEEDED auditRequestLogId None Bcz for now we are not covering this path
                                                   * this will be updated with request.auditRequestLogId replacing with None when this path covered
                                                   * */
                                                  auditRequestLogId = None,
                                                  SRLogger = Logger.appendLogRequestId(
                                                    appendLogReqId = s"manageTPExtensionData add_to_smartreach"
                                                  )
                                                ) match {

                                                  case Failure(e: BadRequestErrorException) =>

                                                    Future.successful(Res.BadRequestError(e.getMessage))

                                                  case Failure(e) =>

                                                    Future.successful(Res.ServerError(
                                                      s"Error while creating prospect: ${e.getMessage}",
                                                      e = Some(e)
                                                    ))

                                                  case Success(res) =>
                                                    if (res.ignored_internal_emails.nonEmpty) {
                                                      Logger.debug(s"Internal emails ${res.ignored_internal_emails} cannot be added as prospects")
                                                    }

                                                    Logger.info(s"manageTPExtensionData add_to_smartreach Persons added to SmartReach successfully!! :: $res")

                                                    Future.successful(Res.Success("Persons added to SmartReach successfully!", Json.obj()))

                                                }


                                              } else {

                                                Logger.fatal(s"manageTPExtensionData add_to_smartreach prospectsToBeCreate empty crm_type: $crm_type :: aid: $aid :: tid: $tid")

                                                Future.successful(Res.BadRequestError("Persons cannot be empty"))

                                              }
                                            }

                                        } else {

                                          //FIXME add to queue
                                          Logger.fatal(s"manageTPExtensionData add_to_smartreach_campaign selectedIdsSeq (${selectedIdsSeq.length}) > 1000 OR total_person_count is more than 1000 $total_person_count need to push into queue ")
                                          Future.successful(Res.Success("Process Started, Persons will be add to SmartReach soon!!", Json.obj()))

                                        }

                                      } else {

                                        Future.successful(Res.BadRequestError("Persons cannot be empty"))

                                      }
                                    }


                                case "add_to_smartreach_campaign" =>

                                  if (campaignId.isDefined) {
                                    campaignService.findBasicDetailsWithPermission(
                                      id = campaignId.get,
                                      teamId = teamId,
                                      permittedAccountIds = getPermittedAccountIds(PermType.EDIT_CAMPAIGNS),
                                    ) match {
                                      case Failure(e) =>
                                        Future.successful(Res.ServerError(s"Error occurred while fetching campaign Details", e = Some(e)))
                                      case Success(campaignDetails) => campaignDetails match {

                                        case None =>

                                          Logger.fatal(s"Campaign.find :: Invalid campaign Id")
                                          Future.successful(Res.BadRequestError("Invalid campaign Id"))

                                        case Some(campaign) =>

                                          val campaign_id = campaign.id

                                          pipedriveOAuth.getPersonsTotalCount(accessTokenData = accessTokenResponse)
                                            .flatMap { total_person_count =>

                                              if (total_person_count.isDefined) {

                                                if (
                                                  (selectedIdsSeq.nonEmpty && selectedIdsSeq.length < 1000) ||
                                                    total_person_count.get < 15000
                                                ) { //FIXME this should in global level


                                                  pipedriveOAuth.getPersons(accessTokenData = accessTokenResponse,
                                                      teamId = teamId,
                                                      accountId = accountId,
                                                      selectedIdsSeq = selectedIdsSeq,
                                                      excludedIdsSeq = excludedIdsSeq,
                                                      filter = filter,
                                                      crm_type = crm_type,
                                                      action = action
                                                    )
                                                    .flatMap { prospectsToBeCreate =>

                                                      val org_id = request.loggedinAccount.org.id

                                                      prospectService.createOrUpdateProspects(
                                                        ownerAccountId = accountId,
                                                        teamId = teamId,
                                                        listName = None,
                                                        prospects = prospectsToBeCreate,
                                                        updateProspectType = UpdateProspectType.ForceUpdate,
                                                        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
                                                        doerAccount = request.loggedinAccount,
                                                        prospectSource = Some(pipedriveOAuth.prospectSource),
                                                        prospectAccountId = None,
                                                        prospect_tags = None,

                                                        campaign_id = Some(campaign_id),
                                                        ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
                                                        deduplicationColumns = None,

                                                        /**
                                                         * FIXME AUDITLOGFIXNEEDED auditRequestLogId None Bcz for now we are not covering this path
                                                         * this will be updated with request.auditRequestLogId replacing with None when this path covered
                                                         * */
                                                        auditRequestLogId = None,
                                                        SRLogger = new SRLogger(
                                                          logRequestId = s"${Logger.logRequestId} manageTPExtensionData add_to_smartreach_campaign"
                                                        )
                                                      ) match {

                                                        case Failure(e: BadRequestErrorException) =>

                                                          Future.successful(Res.BadRequestError(e.getMessage))

                                                        case Failure(e) =>

                                                          Future.successful(Res.ServerError(
                                                            s"Error while creating or assigning prospect: ${e.getMessage}", e = Some(e)))

                                                        case Success(results) =>

                                                          if (results.ignored_internal_emails.nonEmpty) {
                                                            Logger.debug(s"Internal emails ${results.ignored_internal_emails} cannot be added as prospects")
                                                          }

                                                          if (results.assigned_ids.isEmpty) {

                                                            Logger.fatal(s"manageTPExtensionData add_to_smartreach_campaign Prospects could not be assigned to campaign")
                                                            Future.successful(Res.ServerError("Prospects could not be assigned to campaign", e = None))

                                                          } else {

                                                            Logger.info(s"Persons assigned campaign successfully")

                                                            Future.successful(Res.Success("Persons assigned campaign successfully!!", Json.obj()))

                                                          }

                                                      }


                                                    }

                                                } else {

                                                  //FIXME add to queue
                                                  Logger.fatal(s"manageTPExtensionData add_to_smartreach_campaign selectedIdsSeq (${selectedIdsSeq.length}) > 1000 OR total_person_count is more than 15000 $total_person_count need to push into queue ")
                                                  Future.successful(Res.Success("Process Started, Persons will be add to SmartReach soon!!", Json.obj()))

                                                }
                                              } else {

                                                Future.successful(Res.BadRequestError("Persons cannot be empty"))

                                              }
                                            }
                                      }
                                    }
                                  } else {

                                    Logger.fatal(s"manageTPExtensionData add_to_smartreach_campaign Invalid campaign_id")

                                    Future.successful(Res.BadRequestError("Invalid campaign_id"))

                                  }

                                case "remove_from_smartreach_campaign" =>

                                  if (campaignId.isDefined) {

                                    campaignService.findBasicDetailsWithPermission(
                                      id = campaignId.get,
                                      teamId = teamId,
                                      permittedAccountIds = getPermittedAccountIds(PermType.EDIT_CAMPAIGNS)
                                    ) match {
                                      case Failure(e) =>
                                        Future.successful(Res.ServerError(s"Error occurred while fetching campaign Details", e = Some(e)))

                                      case Success(campaignDetails) => campaignDetails match {

                                        case None =>

                                          Logger.fatal(s"Campaign.find Invalid campaign Id")
                                          Future.successful(Res.BadRequestError("Invalid campaign Id"))

                                        case Some(campaign) =>

                                          val campaign_id = campaign.id

                                          pipedriveOAuth.getPersonsTotalCount(accessTokenData = accessTokenResponse)
                                            .flatMap { total_person_count =>

                                              if (total_person_count.isDefined) {

                                                if (
                                                  (selectedIdsSeq.nonEmpty && selectedIdsSeq.length < 1000) ||
                                                    total_person_count.get < 15000
                                                ) { //FIXME this should in global level

                                                  pipedriveOAuth.getPersons(accessTokenData = accessTokenResponse,
                                                      teamId = teamId,
                                                      accountId = accountId,
                                                      selectedIdsSeq = selectedIdsSeq,
                                                      excludedIdsSeq = excludedIdsSeq,
                                                      filter = filter,
                                                      crm_type = crm_type,
                                                      action = action
                                                    )
                                                    .flatMap { prospectsToBeCreate =>

                                                      val personsEmails: Seq[String] = prospectsToBeCreate.filter(_.email.isDefined).map(_.email.get)
                                                      val prospects = prospectServiceV2.findByEmail(
                                                        logger = Logger,
                                                        emails = personsEmails,
                                                        teamId = teamId
                                                      ) match {
                                                        case Failure(e) =>
                                                          Logger.fatal(s"Prospect.findByEmail: ${LogHelpers.getStackTraceAsString(e)}")

                                                          // we are already inside a future, so throwing should not be a problem here
                                                          throw e

                                                        case Success(pids) =>
                                                          pids
                                                      }

                                                      val doer = request.loggedinAccount

                                                      campaignProspectService.unassignProspectsFromMainPage(
                                                        permittedOwnerIds = request.permittedAccountIds,
                                                        doerAccountId = accountId,
                                                        teamId = teamId,
                                                        doerAccountName = Helpers.getAccountName(a = doer),
                                                        prospectIds = prospects.map(_.id).distinct.toList,
                                                        campaignIds = Seq(campaign_id),
                                                        Logger = Logger
                                                      ) match {

                                                        case Failure(e) =>

                                                          Logger.fatal(s"manageTPExtensionData remove_from_smartreach_campaign Error while unassigning prospects", err = e)
                                                          Future.successful(Res.ServerError("There was an error. Please try again.", e = Some(e)))

                                                        case Success(total) =>

                                                          Logger.info(s" total: $total")

                                                          if (total == 0) {

                                                            Logger.fatal(s"manageTPExtensionData remove_from_smartreach_campaign Prospect is not assigned to any campaign")
                                                            Future.successful(Res.NotFoundError("Person is not assigned to any campaign."))


                                                          } else {

                                                            Logger.info(s"manageTPExtensionData remove_from_smartreach_campaign $total Persons have been unassigned from campaigns")

                                                            Future.successful(Res.Success("Persons have been unassigned from SmarReach campaigns.", Json.obj()))

                                                          }

                                                      }
                                                    }
                                                } else {

                                                  //FIXME add to queue
                                                  Logger.fatal(s"manageTPExtensionData remove_from_smartreach_campaign selectedIdsSeq (${selectedIdsSeq.length}) > 1000 OR total_person_count is more than 1000 $total_person_count need to push into queue ")
                                                  Future.successful(Res.Success("Process Started, Persons will be add to SmartReach soon!!", Json.obj()))

                                                }
                                              } else {

                                                Future.successful(Res.BadRequestError("Persons cannot be empty"))

                                              }
                                            }
                                            .recover { case e =>

                                              Logger.fatal(s"manageTPExtensionData remove_from_smartreach_campaign PipedriveOAuth.getPersonsTotalCount exception: ${LogHelpers.getStackTraceAsString(e)} ")

                                              Res.ServerError(new Exception("There was an error. Please try again or contact support."))

                                            }

                                      }
                                    }
                                  } else {

                                    Logger.fatal(s"manageTPExtensionData add_to_smartreach_campaign Invalid campaign_id")

                                    Future.successful(Res.BadRequestError("Invalid campaign_id"))

                                  }
                                case _ =>
                                  Logger.fatal(s"manageTPExtensionData Invalid Extension action $action :: body: ${request.body}")
                                  Future.successful(Res.BadRequestError(s"Invalid Extension action $action"))
                              }


                            case _ =>

                              Logger.fatal(s"manageTPExtensionData Invalid App Extension $integration_type body: ${request.body}")
                              Future.successful(Res.BadRequestError(s"Invalid App Extension $integration_type"))

                          }

                      }
                      .recover {

                        case e: TPInvalidGrantException =>
                          Res.ServerError(e.getMessage, e = Some(e))

                        case e =>

                          Logger.fatal(s"manageTPExtensionData :: integration_type: ${integration_type.toString} :: aid: $aid :: tid: $tid", err = e)

                          Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, e = Some(e))
                      }
                }
            }

        }
      } else {
        Future.successful(Res.BadRequestError(s"Invalid Company or User"))
      }
    }

  }


  def tpInegrationOauthRedirect(crm_type: String,
                                aid: Option[Long],
                                tid: Option[Long]
                               ) = Action { (request: Request[AnyContent]) =>

    val logRequestId = s"${StringUtils.genLogTraceId} tpInegrationOauthRedirect "
    val Logger = new SRLogger(logRequestId = logRequestId)

    val params = request.queryString
    var query = "?"
    params.map(param => {
      query = (query + param._1 + "=" + param._2.head + "&")
    })

    val dashboardDomain = AppConfig.dashboardDomain
    val redirectTo = dashboardDomain + "/dashboard/account_settings/integrations" + query

    Logger.info(s"TriggerController.tpInegrationOauthRedirect: crm_type: $crm_type :: aid: $aid :: tid: $tid :: params: $params :: redirectTo: $redirectTo")


    Redirect(redirectTo)
  }

  def tpInegrationDisconnect(crm_type: String,
                             aid: Option[Long],
                             tid: Option[Long]) =
    Action.async(parse.json) { request =>

      val logRequestId = s"${StringUtils.genLogTraceId} tpInegrationDisconnect "
      given Logger: SRLogger = new SRLogger(logRequestId = logRequestId)
      val Res = new SRAPIResponse(Logger = Logger)

      val companyId = (request.body \ "company_id").asOpt[Long].map(_.toString)
      val userId = (request.body \ "user_id").asOpt[Long].map(_.toString)
      val clientId = (request.body \ "client_id").asOpt[String]

      Logger.info(s"TriggerController.tpInegrationDisconnect: crm_type: $crm_type :: aid: $aid :: tid: $tid :: companyId: $companyId :: userId: $userId :: client_id: $clientId:: body: ${request.body}")

      if (
        companyId.nonEmpty &&
          userId.nonEmpty &&
          clientId.nonEmpty &&
          clientId.get == AppConfig.pipedriveOAuthSettings.clientID
      ) {

        IntegrationType.withName(crm_type) match {

          case Failure(e) =>

            Logger.error(s"FATAL TriggerController.tpInegrationDisconnect: Invalid crm_type: $crm_type :: aid: $aid :: tid: $tid")

            Future.successful(Res.BadRequestError("Invalid CRM Type."))

          case Success(integration_type) =>

            //TODO all delete func came need to do this by integration type currently only pipedrive has
            triggerDAO.findTeamIdAccountIdByPipedriveIntegrationAndCompanyId(companyId = companyId.get, userId = userId.get) match {

              case Failure(e) =>

                Logger.error(s"FATAL TriggerController.tpInegrationDisconnect findTeamIdAccountIdByIntegrationAndCompanyId there was an error. Please try again: $crm_type :: companyId: $companyId :: userId: ${userId.get}", err = e)

                Future.successful(Res.ServerError("There was an error. Please try again.", e = Some(e)))


              case Success(tidAidSeq) =>

                if (tidAidSeq.isEmpty) {
                  Logger.error(s"FATAL TriggerController.tpInegrationDisconnect findTeamIdAccountIdByIntegrationAndCompanyId no integration found with this CompanyId: $companyId and userId: $userId")

                  Future.successful(Res.NotFoundError(s"No ${integration_type.toString.capitalize} integration found with this company Id: ${companyId.get}"))
                } else {

                  triggerDAO.deletePipedriveIntegrationByUserIdCompanyIdV2(companyId = companyId.get, userId = userId.get) match {

                    case Failure(e) =>

                      Logger.error(s"FATAL TriggerController.tpInegrationDisconnect deleteIntegrationByTeamIdAccountId there was an error. Please try again: $crm_type :: companyId: $companyId :: userId: ${userId.get}", err = e)

                      Future.successful(Res.ServerError("There was an error. Please try again.", e = Some(e)))

                    case Success(None) =>

                      Logger.error(s"FATAL TriggerController.tpInegrationDisconnect deleteIntegrationByTeamIdAccountId no integration found with this CompanyId: $companyId and userId: $userId")

                      Future.successful(Res.NotFoundError(s"No ${integration_type.toString.capitalize} integration found with this company Id: ${companyId.get}"))

                    case Success(Some(tp_team_id)) =>

                      organizationService.resetOrgDataFromCacheUsingTeamId(
                        teamId = TeamId(tp_team_id),
                        resetCacheInterval = SrResetCacheInterval.Immediately
                      ) match {
                        case Failure(e) =>
                          Logger.fatal("Error while resetting Org Data Cache", e)
                          Future.successful(Res.ServerError(e))

                        case Success(_) =>
                          accountService.getTeamMatesByTeamId(teamId = TeamId(id = tp_team_id))
                            .map(_.user_id)
                            .distinct
                            .foreach(aid => resetUserCacheUtil._resetTeamCache(aid = aid.id))

                          Future.successful(Res.Success(s"${integration_type.toString.capitalize} disconnected successfully", Json.obj()))
                      }

                  }

                }
            }

        }

      } else {
        Future.successful(Res.BadRequestError(s"Invalid Company or User"))
      }
    }


  def uninstallTPIntegration(v: String, crm_type: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_WORKFLOWS,
    tidOpt = tid
  ).async { request =>

    given Logger: SRLogger = request.Logger
    val Res = request.Response
    val ta = request.actingTeamAccount

    if (ta.isEmpty) {

      Future.successful(Res.BadRequestError("Invalid team"))

    } else if (ta.get.team_role != TeamAccountRole.OWNER && ta.get.team_role != TeamAccountRole.ADMIN) {

      Future.successful(Res.ForbiddenError("Please ask the team admin to disconnect this tool. Contact support if there are any questions."))

    } else {

      IntegrationType.withName(crm_type) match {

        case Failure(e) =>

          Logger.fatal(s"UninstallTPIntegration: Invalid crm_type: $crm_type :: aid: $aid :: tid: $tid")

          Future.successful(Res.BadRequestError("Invalid CRM Type"))

        case Success(integration_type) =>

          triggerService.deleteIntegration(
            teamId = ta.get.team_id,
            integration_type = integration_type
          ).flatMap {

            case Left(UninstallIntegrationError.SQLException(sqlErr)) =>

              Future.successful(Res.ServerError(sqlErr))

            case Left(UninstallIntegrationError.ResetOrgDataCacheError(err)) =>

              Future.successful(Res.ServerError(err))

            case Left(UninstallIntegrationError.CRMTokensError(_)) =>

              /**
               * here we are deleted credentials from DB and triggering uninstall API to CRM
               * InvalidGrantException: throws when refresh token expires (No activity from long time or uninstalled app from the CRM dashboard)
               * in that case also returning success bcz credentials are deleted from DB, So user can retry integration again
               * */

              Logger.fatal(s"UninstallTPIntegration refreshAccessToken RefreshAccessTokenInvalidGrantException :: crm_type: $crm_type :: aid: $aid :: tid: $tid")

              Future.successful(Res.Success("Uninstalled successfully", Json.obj()))

            case Left(UninstallIntegrationError.CRMUninstallError(_)) =>

              /**
               * here we are deleted credentials from DB and triggering uninstall API to CRM
               * InvalidGrantException: throws when refresh token expires (No activity from long time or uninstalled app from the CRM dashboard)
               * in that case also returning success bcz credentials are deleted from DB, So user can retry integration again
               * */

              Logger.fatal(s"UninstallTPIntegration refreshAccessToken UninstallError never hit path :: crm_type: $crm_type :: aid: $aid :: tid: $tid")

              Future.successful(Res.Success("Uninstalled successfully", Json.obj()))

            case Right(_) =>

              Future.successful(Res.Success("Uninstalled successfully", Json.obj()))
          }

      }

    }
  }

  def createOrUpdateCampaignTriggers(v: String, campaign_id: Long, aid: Option[Long], tid: Option[Long]) = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_WORKFLOWS,
    tidOpt = tid
  ) andThen hasCampaign(campaign_id)
    ).async(parse.json) { request =>

    Future {
      given Logger: SRLogger = request.Logger
      val Res = request.Response
      val campaign = request.campaign
      val t = request.actingTeamAccount

      val validateData = request.body.validate[CreateOrUpdateCampaignTriggerForm]

      validateData match {

        case JsError(e) => Res.JsValidationError(e, requestBody = Some(request.body))

        case JsSuccess(value, _) =>
        val data: CreateOrUpdateCampaignTriggerForm = value

          val synAndActivityEventAndActions = TriggerUtils.getSyncEventAndActivityActionByIntegrationAndModuleTypes(integration_type = data.integration_type, module_type = data.module_type)

          val syncCreateOrUpdateTAction = TriggerAction(
            action_type = SRTriggerActionType.CREATE_OR_UPDATE_PROSPECT_IN_SMARTREACH,
            action_app_type = IntegrationType.SMARTREACH,
            fields = None,
            campaign_id = Some(campaign.id),
            ignore_prospects_active_in_other_campaigns = data.ignore_prospects_active_in_other_campaigns,
            ignore_prospects_in_other_campaigns = data.ignore_prospects_in_other_campaigns,
            tags = None,
            // webhook_url = None,
            create_contact_or_lead_if_not_exists = None,
            last_sync_at = None,
            prospect_category_id_custom = None
          )

          val syncCreateData = CreateTriggerForm(
            label = s"${data.label} - ${campaign.name} - New ${data.module_type.toString.capitalize} added in ${data.integration_type.toString.capitalize}",
            event = Some(synAndActivityEventAndActions._1),
            conditions = None,
            actions = Some(Seq(syncCreateOrUpdateTAction)),
            campaign_id = Some(campaign.id),
            source = None,
            active = Some(true)
          )

          val syncUpdateData = UpdateTriggerForm(
            label = s"${data.label} - ${campaign.name} - New ${data.module_type.toString.capitalize} added in ${data.integration_type.toString.capitalize}",
            event = Some(synAndActivityEventAndActions._1),
            event_app_type = Some(data.integration_type),
            conditions = None,
            actions = Some(Seq(syncCreateOrUpdateTAction)),
            campaign_id = Some(campaign.id),
            tp_filter_id = Some(data.filter_id),
            source = None,
            active = true
          )

          triggerDAO.findCampaignTriggersByCampaignId(campaignId = campaign.id) match {

            case Failure(e) =>

              Logger.fatal(s"find sql : campaign_id:${campaign.id} event:${synAndActivityEventAndActions._1}", err = e)

              Res.ServerError("There was an error. Please try again.", e = Some(e))

            case Success(triggers) =>

              val syncTrigger = triggers.find(t => t.event_app_type.get != IntegrationType.SMARTREACH)

              if (syncTrigger.isDefined) {

                triggerDAO.update(
                  id = syncTrigger.get.id,
                  permittedAccountIds = request.permittedAccountIds,
                  teamId = t.team_id, data = syncUpdateData
                ) match {

                  case Failure(e) => Res.ServerError("There was an error. Please try again.", e = Some(e))

                  case Success(_) =>

                    Res.Success(s"Updated successfully", Json.obj("campaign_trigger" -> Json.toJson(data)))

                }

              } else {

                triggerDAO.createBasic(accountId = t.user_id, teamId = t.team_id, data = syncCreateData) match {

                  case Failure(e) => Res.ServerError("There was an error. Please try again.", e = Some(e))

                  case Success(syncTriggerId) =>

                    triggerDAO.update(
                      id = syncTriggerId.get,
                      permittedAccountIds = request.permittedAccountIds,
                      teamId = t.team_id,
                      data = syncUpdateData
                    ) match {

                      case Failure(e) => Res.ServerError("There was an error. Please try again.", e = Some(e))

                      case Success(syncTriggerRes) =>

                        val msg = EventDataType.getEventDataFromMQTriggerSyncMessage(triggerId = syncTriggerRes.get.id, eventType = syncTriggerRes.get.event.get).get

                        syncTriggerMQService.publishSyncTrigger(
                          message = msg,
                          integrationType = syncTriggerRes.get.event_app_type.get,
                          eventType = syncTriggerRes.get.event.get,
                          teamId = TeamId(syncTriggerRes.get.team_id),
                          accountId = AccountId(syncTriggerRes.get.owner_id)
                        )

                        Res.Success(s"Created successfully", Json.obj("campaign_trigger" -> Json.toJson(data)))

                    }
                }

              }

          }
      }
    }
  }

  def fetchAllCampaignTriggers(
                                v: String,
                                tid: Option[Long],
                                crm_type: String,
                                module_type: String
                              ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_WORKFLOWS,
    tidOpt = tid
  ).async { request =>


    Future {
      given Logger: SRLogger = request.Logger
      val Res = request.Response
      val ta = request.actingTeamAccount

      if (ta.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {

        triggerService.validateAndGetParsedCRMTypeModuleTypeAndWorkflowEventName(
          crm_type = crm_type,
          module_type = module_type
        ) match {

          case Left(ValidateCRMTypeModuleTypeAndGetEventTypeError.InvalidCRMError(e)) =>

            Res.BadRequestError(e.getMessage)

          case Left(ValidateCRMTypeModuleTypeAndGetEventTypeError.InvalidModuleError(e)) =>

            Res.BadRequestError(e.getMessage)

          case Left(ValidateCRMTypeModuleTypeAndGetEventTypeError.InvalidCRMOrModuleForEventTypeError) =>

            Res.BadRequestError("Invalid CRM or Module type")

          case Left(ValidateCRMTypeModuleTypeAndGetEventTypeError.NOTFoundSyncsError) =>

            Res.Success("Campaign syncs not found", Json.obj())

          case Left(ValidateCRMTypeModuleTypeAndGetEventTypeError.SQLException(e)) =>

            Logger.fatal(s"triggerService validateAndGetParsedCRMTypeModuleTypeAndWorkflowEventName error:", err = e)

            Res.ServerError("There was an error. Please try again.", e = None)

          case Right(event_type) =>

            triggerService.findAllCampaignTriggersByEventType(
              teamId = ta.get.team_id,
              permittedAccountIds = request.permittedAccountIds,
              eventType = event_type,
              logger = Logger
            ) match {

              case Left(ValidateCRMTypeModuleTypeAndGetEventTypeError.InvalidCRMError(e)) =>

                Res.BadRequestError(e.getMessage)

              case Left(ValidateCRMTypeModuleTypeAndGetEventTypeError.InvalidModuleError(e)) =>

                Res.BadRequestError(e.getMessage)

              case Left(ValidateCRMTypeModuleTypeAndGetEventTypeError.InvalidCRMOrModuleForEventTypeError) =>

                Res.BadRequestError("Invalid CRM or Module type")

              case Left(ValidateCRMTypeModuleTypeAndGetEventTypeError.SQLException(e)) =>

                Logger.fatal(s"triggerService findAllCampaignTriggersByEventType error:", err = e)

                Res.ServerError("There was an error. Please try again.", e = Some(e))

              case Left(ValidateCRMTypeModuleTypeAndGetEventTypeError.NOTFoundSyncsError) =>

                Res.Success("Campaign syncs not found", Json.obj())

              case Right(syncTriggers) =>

                Res.Success("Campaign syncs found.", Json.obj("syncs" -> syncTriggers))

            }

        }
      }
    }
  }

  def findCampaignTriggers(v: String, campaign_id: Long, aid: Option[Long], tid: Option[Long]) = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_WORKFLOWS,
    tidOpt = tid
  )
    andThen hasCampaign(campaign_id)
    ).async { request =>

    Future {

      given Logger: SRLogger = request.Logger
      val Res = request.Response
      val ta = request.actingTeamAccount

      triggerDAO.findCampaignTriggersByCampaignId(campaignId = campaign_id) match {

        case Failure(e) =>

          Logger.fatal(s"find sql : campaign_id:${campaign_id}", err = e)

          Res.ServerError("There was an error. Please try again.", e = Some(e))

        case Success(triggers) =>

          if (triggers.nonEmpty) {

            val syncTrigger = triggers.find(t => t.event_app_type.get != IntegrationType.SMARTREACH)
            val activityTrigger = triggers.find(t => t.event.get == EventType.ANY_EMAIL_ACTIVITY)

            if (syncTrigger.isDefined && syncTrigger.get.actions.isDefined && syncTrigger.get.actions.get.nonEmpty) {

              val syncTriggerInDB = syncTrigger.get
              val module_type = TriggerUtils.getModuleNameByActionOrEvent(syncTriggerInDB.event.get.toString)
              val integration_type = syncTriggerInDB.event_app_type.get
              val filter_id = syncTriggerInDB.tp_filter_id
              val update_activity = (activityTrigger.isDefined)
              val ignore_prospects_active_in_other_campaigns = syncTriggerInDB.actions.get.head.ignore_prospects_active_in_other_campaigns
              val ignore_prospects_in_other_campaigns = syncTriggerInDB.actions.get.head.ignore_prospects_in_other_campaigns
              val campaign_id = syncTrigger.get.campaign_id.get

              val campaign_trigger = CampaignTriggerInDB(
                module_type = module_type,
                integration_type = integration_type,
                ignore_prospects_active_in_other_campaigns = ignore_prospects_active_in_other_campaigns,
                ignore_prospects_in_other_campaigns = ignore_prospects_in_other_campaigns,
                campaign_id = campaign_id,
                filter_id = if (filter_id.isDefined) filter_id.get else "-1",
                last_ran_at = syncTriggerInDB.last_ran_at
              )
              Res.Success("Workflow found.", Json.obj("campaign_trigger" -> campaign_trigger))

            } else {

              Res.Success("Workflow not found", Json.obj())

            }

          } else {

            Res.Success("Workflow not found", Json.obj())

          }

      }

    }
  }


  def deleteCampaignTriggers(v: String, campaign_id: Long, aid: Option[Long], tid: Option[Long]) = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CAMPAIGNS, // campaign owner should be able to delete campaign triggers
    tidOpt = tid
  )
    andThen hasCampaign(campaign_id)
    ).async(parse.json) { request =>
    Future {
      val Logger = request.Logger
      val Res = request.Response

      val t = request.actingTeamAccount
      val campaign = request.campaign

      triggerDAO.findCampaignTriggersByCampaignId(campaignId = request.campaign.id) match {

        case Failure(e) =>

          Res.ServerError("There was an error, please try again or contact support", e = Some(e))

        case Success(triggers) =>


          if (triggers.nonEmpty) {

            val ids = triggers.map((t => t.id))

            triggerDAO.delete(
              ids = ids,
              permittedAccountIds = request.permittedAccountIds,
              teamId = t.team_id
            ) match {

              case Failure(e) => Res.ServerError("There was an error. Please try again.", e = Some(e))

              case Success(res) => Res.Success(s"Stopped successfully", Json.obj())

            }

          } else {
            Res.BadRequestError(s"no active workflows found");
          }

      }
    }
  }


  def fetchTPUsers(v: String, aid: Option[Long], tid: Option[Long], crm_type: String) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_WORKFLOWS,
    tidOpt = tid,
    apiAccess = true
  ).async { request =>

    given Logger: SRLogger = request.Logger
    val Res = request.Response
    val ta = request.actingTeamAccount

    if (ta.isEmpty) {

      Future.successful(Res.BadRequestError("Invalid team"))

    } else {

      IntegrationType.withName(crm_type) match {

        case Failure(e) =>

          Future.successful(Res.BadRequestError("Invalid CRM Type"))

        case Success(integration_type) =>

          tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
              teamId = ta.get.team_id,
              integration_type = integration_type
            ).flatMap {

              case Left(_) =>
                /*TODO_EitherT handle */
                Future.successful(Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, e = None))

              case Right(accessTokenResponse) =>

                tIntegrationCRMService.getTPUsers(integrationType = integration_type, accessTokenData = accessTokenResponse).map {

                  case Left(GetCRMUsersError.MalformedUsersResponseError(message)) =>

                    Res.ServerError(message = message, e = None)

                  case Left(GetCRMUsersError.CommonCRMAPIError(err)) =>

                    Res.BadRequestError(err.message)

                  case Right(res) =>

                    Res.Success("Users found", Json.obj(
                      "users" -> res
                    ))
                }


            }
            .recover {

              case e =>

                Logger.fatal(s"fetchTPUsers: crmIntegrationService.refreshAccessToken :: crmIntegrationService: $integration_type :: aid: $aid :: tid: $tid", err = e)

                Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))
            }

      }


    }

  }


  def findAllCRMIntegrations(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_WORKFLOWS,
    tidOpt = tid
  ).async { request =>


      given Logger: SRLogger = request.Logger
      val Res = request.Response
      val ta = request.actingTeamAccount

      if (ta.isEmpty) {

        Future.successful(Res.BadRequestError("Invalid team"))

      } else {

          triggerService.getAllIntegrations(teamId = TeamId(ta.get.team_id))
            .map{integrations =>
                    Res.Success(s"CRM Integrations found", Json.obj("crm_integrations" -> integrations))
            }
            .recover{e =>
              Logger.fatal(s"findAllIntegrations : teamId : ${ta.get.team_id}",err = e)
                Res.ServerError("There was an error, please try again or contact support", e = Some(e))
            }

      }

  }


  def getConfiguredModulesByCRM(
                                 v: String,
                                 crm_type: String,
                                 tid: Option[Long]
                               ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_WORKFLOWS,
    tidOpt = tid
  ).async { request =>

    Future {

      given Logger: SRLogger = request.Logger
      val Res = request.Response
      val t = request.actingTeamAccount


      if (t.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {

        val teamId = t.get.team_id

        IntegrationType.withName(crm_type) match {

          case Failure(e) =>

            Logger.fatal(s"getTPFieldsMapping: Invalid crm_type: $crm_type :: tid: $tid", err = e)

            Res.BadRequestError("Invalid CRM Type")

          case Success(crm_type) =>

            triggerService.getConfiguredModulesByCRM(
              crm_type = crm_type,
              team_id = teamId,
              Logger = Logger
            ) match {

              case Failure(e) =>

                Logger.fatal(s"getTPFieldsMapping: $teamId : ${t.get.team_id}", err = e)

                Res.ServerError("There was an error. Please try again.", e = Some(e))

              case Success(statusMapping) =>

                Res.Success("Modules found", Json.obj("modules" -> Json.toJson(statusMapping)))
            }
        }


      }
    }
  }


  def getCRMSettings(
                      v: String,
                      crm_type: String,
                      module_type: String,
                      tid: Option[Long]
                    ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_WORKFLOWS,
    tidOpt = tid
  ).async { request =>

    Future {

      given Logger: SRLogger = request.Logger
      val Res = request.Response
      val t = request.actingTeamAccount


      if (t.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {

        val teamId = t.get.team_id

        IntegrationType.withName(crm_type) match {

          case Failure(e) =>

            Logger.fatal(s"getCRMSettings: Invalid crm_type: $crm_type :: tid: $tid", err = e)

            Res.BadRequestError("Invalid CRM Type")

          case Success(integration_type) =>

            IntegrationModuleType.withName(module_type) match {

              case Failure(e) =>

                Logger.fatal(s"getCRMSettings: Invalid module_type: $module_type :: tid: $tid", err = e)

                Res.BadRequestError("Invalid Module Type")

              case Success(moduleType) =>

                triggerService.getCRMModuleLevelSettings(
                  crm_type = integration_type,
                  module_type = moduleType,
                  team_id = teamId,
                  Logger = Logger
                ) match {

                  case Failure(e) =>

                    Logger.fatal(s"getCRMSettings: $teamId : ${t.get.team_id}", err = e)

                    Res.ServerError("There was an error. Please try again.", e = Some(e))

                  case Success(settings) =>

                    Res.Success("Settings found", Json.toJson(settings))
                }
            }
        }

      }
    }
  }


  def updateCRMSettings(
                         v: String,
                         crm_type: String,
                         module_type: String,
                         tid: Option[Long]
                       ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_WORKFLOWS,
    tidOpt = tid
  ).async((parse.json)) { request =>

    Future {

      val Logger = request.Logger
      val Res = request.Response
      val ta = request.actingTeamAccount

      if (ta.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {

        IntegrationType.withName(crm_type) match {

          case Failure(e) =>

            Logger.fatal(s"updateCRMSettings: Invalid crm_type: $crm_type :: tid: $tid", err = e)

            Res.BadRequestError("Invalid CRM Type")

          case Success(integration_type) =>

            IntegrationModuleType.withName(module_type) match {

              case Failure(e) =>

                Logger.fatal(s"updateCRMSettings: Invalid module_type: $module_type :: tid: $tid", err = e)

                Res.BadRequestError("Invalid Module Type")

              case Success(moduleType) =>

                val t = ta.get

                val validateData = request.body.validate[UpdateCRMModuleLevelSettingsForm]

                validateData match {

                  case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

                  case JsSuccess(data, _) =>

                    triggerService.updateCRMModuleLevelSettings(
                      crm_type = integration_type,
                      module_type = moduleType,
                      team_id = t.team_id,
                      data = data,
                      Logger = Logger
                    ) match {

                      case Failure(e) =>

                        Logger.fatal(s"updateCRMSettings Error while updating mapping:${data} :: tid: $tid", err = e)

                        Res.ServerError(s"Error while updating mapping.", e = Some(e))

                      case Success(None) =>

                        Res.NotFoundError("Settings not found. Could you reload and try again?")

                      case Success(Some(_)) =>

                        Res.Success("Module settings has been updated.", Json.obj())

                    }
                }
            }

        }

      }
    }

  }


  def getCRMDNCSettings(
                         v: String,
                         crm_type: String,
                         tid: Option[Long]
                       ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_WORKFLOWS,
    tidOpt = tid
  ).async { request =>

    Future {

      given Logger: SRLogger = request.Logger
      val Res = request.Response
      val t = request.actingTeamAccount

      if (t.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {

        val teamId = t.get.team_id

        IntegrationType.withName(crm_type) match {

          case Failure(e) =>

            Logger.fatal(s"getCRMSettings: Invalid crm_type: $crm_type :: tid: $tid", err = e)

            Res.BadRequestError("Invalid CRM Type")

          case Success(integration_type) =>

            triggerService.getCRMModuleLevelDNCSettings(
              crm_type = integration_type,
              team_id = teamId,
              Logger = Logger
            ) match {

              case Failure(e) =>

                Logger.fatal(s"getCRMSettings: $teamId : ${t.get.team_id}", err = e)

                Res.ServerError("There was an error. Please try again.", e = Some(e))

              case Success(settings) =>

                Res.Success("Settings found", Json.toJson(CRMModuleLevelDNCSettingsForm(settings)))
            }
        }

      }
    }
  }

  def updateCRMDNCSettings(
                            v: String,
                            crm_type: String,
                            tid: Option[Long]
                          ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_WORKFLOWS,
    tidOpt = tid
  ).async((parse.json)) { request =>

    Future {

      val Logger = request.Logger
      val Res = request.Response
      val ta = request.actingTeamAccount

      if (ta.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {

        IntegrationType.withName(crm_type) match {

          case Failure(e) =>

            Logger.fatal(s"updateCRMDNCSettings: Invalid crm_type: $crm_type :: tid: $tid", err = e)

            Res.BadRequestError("Invalid CRM Type")

          case Success(integration_type) =>

            val t = ta.get

            val validateData = request.body.validate[CRMModuleLevelDNCSettingsForm]

            validateData match {

              case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

              case JsSuccess(data, _) =>

                val dnc_settings_data = data.dnc_settings

                triggerService.updateCRMModuleLevelDNCSettings(
                  crm_type = integration_type,
                  team_id = t.team_id,
                  data = dnc_settings_data,
                  Logger = Logger
                ) match {

                  case Left(UpdateCRMModuleLevelDNCSettingsError.DNCSettingsEmptyEmptyError) =>

                    Res.BadRequestError(message = s"DNC settings cannot be empty")

                  case Left(UpdateCRMModuleLevelDNCSettingsError.SQLException(sqlErr)) =>

                    Res.ServerError(sqlErr)

                  case Right(_) =>

                    Res.Success("DNC settings settings has been updated.", Json.obj())


                }

            }
        }

      }
    }

  }

  def updateCalendlySettings(
                            v:String,
                            tid:Option[Long]
                            )= permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_WORKFLOWS,
      tidOpt = tid
  ).async((parse.json)) { request =>
      Future {

          val Logger = request.Logger
          val res = request.Response
          val ta = request.actingTeamAccount

          if (ta.isEmpty) {

              res.BadRequestError("Invalid team")

          } else {

            request.body.validate[CalendlySettings] match {
                case JsError(errors) =>
                    res.JsValidationError(errors)

                case JsSuccess(value, _) =>
                    teamDAO.updateCalendlyOptions(
                        teamId = TeamId(ta.get.team_id),
                        calendlyOptions = value
                    ) match {
                        case Failure(err) =>
                            res.ServerError("Could not update Calendly settings", Some(err))
                        case Success(value) =>
                            res.Success("Successfully updated calendly settings", Json.obj("success" -> true))
                    }
            }

          }
      }
  }

  def disconnectCalendlyIntegration(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_WORKFLOWS,
    tidOpt = tid
  ).async { request =>

    given Logger: SRLogger = request.Logger
    val Res = request.Response
    val ta = request.actingTeamAccount

    if (ta.isEmpty) {
      Future.successful(Res.BadRequestError("Invalid team"))
    } else if (ta.get.team_role != TeamAccountRole.OWNER && ta.get.team_role != TeamAccountRole.ADMIN) {
      Future.successful(Res.ForbiddenError("Please ask the team admin to disconnect this tool. Contact support if there are any questions."))
    } else {

      calendlyWebhookService.disconnectCalendlyIntegration(
        teamId = TeamId(ta.get.team_id)
      ).map {
        case Right(_) =>
          // Reset cache for the team
          organizationService.resetOrgDataFromCacheUsingTeamId(
            teamId = TeamId(ta.get.team_id),
            resetCacheInterval = SrResetCacheInterval.Immediately
          ) match {
            case Failure(e) =>
              Logger.fatal("Error while resetting Org Data Cache after Calendly disconnection", e)
              Res.ServerError(e)

            case Success(_) =>
              // Reset team cache for all team members
              accountService.getTeamMatesByTeamId(teamId = TeamId(id = ta.get.team_id))
                .map(_.user_id)
                .distinct
                .foreach(aid => resetUserCacheUtil._resetTeamCache(aid = aid.id))

              Res.Success("Calendly integration disconnected successfully", Json.obj(
                "message" -> "Calendly integration has been disconnected and all data has been cleared.",
                "integration_status" -> false
              ))
          }

        case Left(error) =>
          error match {
            case CalendlyDisconnectionError.IntegrationNotFoundError(message) =>
              Res.NotFoundError(message)
            case CalendlyDisconnectionError.TokenRetrievalError(message) =>
              Res.ServerError("Failed to retrieve integration tokens", e = Some(new RuntimeException(message)))
            case CalendlyDisconnectionError.OAuthRevocationError(message) =>
              Res.ServerError("Failed to revoke OAuth tokens", e = Some(new RuntimeException(message)))
            case CalendlyDisconnectionError.WebhookDeletionError(message) =>
              Res.ServerError("Failed to delete webhook", e = Some(new RuntimeException(message)))
            case CalendlyDisconnectionError.DatabaseError(message, _) =>
              Res.ServerError("Database error during disconnection", e = Some(new RuntimeException(message)))
          }
      }.recover {
        case e: Exception =>
          Logger.error(s"Unexpected error during Calendly disconnection for team ${ta.get.team_id}", err = e)
          Res.ServerError("An unexpected error occurred during disconnection. Please try again or contact support.", e = Some(e))
      }
    }
  }

}