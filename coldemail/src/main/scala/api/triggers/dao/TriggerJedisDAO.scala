package api.triggers.dao

import api.integrations.IntegrationTPAccessTokenResponse
import api.{AppConfig, CacheServiceJedis}
import api.triggers.IntegrationType
import play.api.Logging
import utils.Helpers
import utils.cache_utils.{SimpleCacheServiceTrait, SrRedisKeyRegister, SrRedisKeyNamespace}

import scala.util.{Failure, Success, Try}


case class CacheKeyForTrigger(
                             teamId: Long,
                             integration_type: IntegrationType
                             )
class TriggerJedisDAO(
                       val cacheServiceJedis: CacheServiceJedis
                     ) extends SimpleCacheServiceTrait[CacheKeyForTrigger, IntegrationTPAccessTokenResponse.FullTokenData] {

  override protected val redisKeyNamespace: SrRedisKeyNamespace = SrRedisKeyRegister.workflowCrmNamespace


  val defaultExpiresInSeconds = 300

  override def getCacheId(data: CacheKeyForTrigger): String = {
    s"${data.teamId}::${data.integration_type.toString}"
  }

}
