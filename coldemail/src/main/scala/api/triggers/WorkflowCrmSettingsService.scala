package api.triggers

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.accounts.service.OrganizationService
import api.integrations.{IntegrationTPAccessTokenResponse, IntegrationTPUsersList}
import api.triggers.dao.WorkflowCrmSettingsDAO
import io.sr.billing_common.models.AddonLicenceType
import utils.cache.models.SrResetCacheInterval
import utils.{AddonLimitReachedException, PlanLimitService, SRLogger}

import scala.util.{Failure, Success, Try}

sealed trait CheckLimitAndAddOrUpdateCRMError

object CheckLimitAndAddOrUpdateCRMError {
  case object CRMIntegrationLimitReached extends CheckLimitAndAddOrUpdateCRMError

  case class SQLException(err: Throwable) extends CheckLimitAndAddOrUpdateCRMError
}

class WorkflowCrmSettingsService(
                                  workflowCrmSettingsDAO: WorkflowCrmSettingsDAO,
                                  organizationService: OrganizationService,
                                  planLimitService: PlanLimitService
                                ) {

  def checkLimitAndAddOrUpdateCRM(
                                   teamId: TeamId,
                                   accountId: AccountId,
                                   tokens: IntegrationTPAccessTokenResponse.FullTokenData,
                                   service_provider: IntegrationType,
                                   tp_user_id: Option[String],
                                   tp_company_id: Option[String], //in Hubpost case company id will be portal id,
                                   tp_owner_id: Option[String], //this param specific for Hubspot
                                   tp_users: Seq[IntegrationTPUsersList],
                                   is_sandbox: Boolean
                                 )(using logger: SRLogger): Either[CheckLimitAndAddOrUpdateCRMError, Option[Long]] = {
    
    val res = for {
      crmSettingIdOpt: Option[Long] <- workflowCrmSettingsDAO.getCrmSettingId(
        teamId = teamId,
        service_provider = service_provider
      )

      /**
        * 22-June-2024
        *
        * A user was getting plan limits exceeded error when reconnecting CRM.
        *
        * - `crmSettingIdOpt` will be None when connecting new crm - check plan limits.
        * - `crmSettingIdOpt` will be defined when reconnecting an existing crm - skip plan limits check.
        */
      _: Boolean <- if (crmSettingIdOpt.isEmpty) {

        logger.info(
          msg = s"Checking plan limits connecting new crm. teamId: $teamId :: accountId: $accountId :: service_provider: $service_provider :: crmSettingId: $crmSettingIdOpt"
        )

        planLimitService.checkLimitAccordingToAddonLicenseTypeUsingTeamId(
          teamId = teamId,
          addonLicenceType = AddonLicenceType.CrmIntegrationSeats,
          isAddingNewTeam = false,
        )
          .map(_ => true)

      } else {

        logger.info(
          msg = s"Skipping plan limit check reconnecting existing crm. teamId: $teamId :: accountId: $accountId :: service_provider: $service_provider :: crmSettingId: $crmSettingIdOpt"
        )

        Success(false)

      }

      crmSettingId: Option[Long] <- workflowCrmSettingsDAO.insertOrUpdateTPAccessTokens(
        teamId = teamId.id,
        accountId = accountId.id,
        tokens = tokens,
        tp_company_id = tp_company_id,
        tp_user_id = tp_user_id,
        tp_owner_id = tp_owner_id,
        service_provider = service_provider,
        tp_users = tp_users,
        is_sandbox = is_sandbox
      )

      _: Boolean <- organizationService.resetOrgDataFromCacheUsingTeamId(
        teamId = teamId,
        resetCacheInterval = SrResetCacheInterval.Immediately
      )

    } yield {
      crmSettingId
    }
    

    res match {
      case Failure(AddonLimitReachedException(message, _)) =>
        Left(CheckLimitAndAddOrUpdateCRMError.CRMIntegrationLimitReached)

      case Failure(exception) =>
        Left(CheckLimitAndAddOrUpdateCRMError.SQLException(exception))

      case Success(value) =>
        Right(value)
    }

  }

}
