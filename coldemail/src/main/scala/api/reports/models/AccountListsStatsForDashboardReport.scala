package api.reports.models

import play.api.libs.json.{<PERSON><PERSON>, Writes}
import scalikejdbc.WrappedResultSet

case class AccountListsStatsForDashboardReport(
  name: String,
  owner_name: String,
  total_prospects: Int,
  prospects_contacted: Int,
  prospects_opened: Int,
  bounced: Int,
  clicked: Int,
  opted_out: Int,
  replied: Int,

  percentage_prospects_opened: Int,
  percentage_bounced: Int,
  percentage_clicked: Int,
  percentage_opted_out: Int,
  percentage_replied: Int
)

object AccountListsStatsForDashboardReport {
  given writes: Writes[AccountListsStatsForDashboardReport] = Json.writes[AccountListsStatsForDashboardReport]


  def fromDb(rs: WrappedResultSet): AccountListsStatsForDashboardReport = {

    val sent = rs.int("prospects_contacted")
    val opened = rs.int("prospects_opened")
    val bounced = rs.int("bounced")
    val clicked = rs.int("clicked")
    val opted_out = rs.int("opted_out")
    val replied = rs.int("replied")

    AccountListsStatsForDashboardReport(
      name = rs.string("name"),
      owner_name = rs.string("owner_name"),
      total_prospects = rs.int("total_prospects"),
      prospects_contacted = sent,
      prospects_opened = opened,
      bounced = bounced,
      clicked = clicked,
      opted_out = opted_out,
      replied = replied,

      percentage_prospects_opened = Math.ceil(opened.toFloat * 100 / sent).toInt,
      percentage_bounced = Math.ceil(bounced.toFloat * 100 / sent).toInt,
      percentage_clicked = Math.ceil(clicked.toFloat * 100 / sent).toInt,
      percentage_opted_out = Math.ceil(opted_out.toFloat * 100 / sent).toInt,
      percentage_replied = Math.ceil(replied.toFloat * 100 / sent).toInt
    )
  }
}