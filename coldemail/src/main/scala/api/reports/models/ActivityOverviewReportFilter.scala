package api.reports.models

import api.accounts.TeamId
import api.campaigns.services.CampaignId
import play.api.libs.json.{Json, OFormat}
import utils.cronjobs.FromAndTillDate

case class ActivityOverviewReportFilter(

                                         from_and_till_date : FromAndTillDate,
                                         campaign_ids: List[CampaignId],
                                         list_ids: List[Long],
                                         auto_linkedin_tasks : Option[Boolean]

                                       )


object ActivityOverviewReportFilter{

  given format: OFormat[ActivityOverviewReportFilter] = Json.format[ActivityOverviewReportFilter]

}


