package api.reports.models

import play.api.libs.json.{<PERSON>at, <PERSON>s<PERSON><PERSON><PERSON>, JsR<PERSON>ult, JsString, JsSuccess, JsValue}

import scala.util.{Failure, Success, Try}

sealed trait InternalAudienceReportType {
  def toKey: String

  override def toString: String = toKey
}

object InternalAudienceReportType {
  private val subscribedActive = "subscribed-active"
  private val subscribedCancelled = "subscribed-cancelled"
  private val trialInactive = "trial-inactive"

  case object SubscribedActive extends InternalAudienceReportType {
    override def toKey: String = subscribedActive
  }

  case object SubscribedCancelled extends InternalAudienceReportType {
    override def toKey: String = subscribedCancelled
  }

  case object TrialInactive extends InternalAudienceReportType {
    override def toKey: String = trialInactive
  }

  def withName(key: String): Try[InternalAudienceReportType] = Try {
    key match {
      case `subscribedActive` => SubscribedActive
      case `subscribedCancelled` => SubscribedCancelled
      case `trialInactive` => TrialInactive
    }
  }

  implicit def format: Format[InternalAudienceReportType] = new Format[InternalAudienceReportType] {
    override def reads(json: JsValue): JsResult[InternalAudienceReportType] = {
      withName(json.as[String]) match {
        case Failure(e) => JsError(e.toString)
        case Success(value) => JsSuccess(value)
      }
    }

    override def writes(o: InternalAudienceReportType): JsValue = JsString(o.toKey)
  }
}