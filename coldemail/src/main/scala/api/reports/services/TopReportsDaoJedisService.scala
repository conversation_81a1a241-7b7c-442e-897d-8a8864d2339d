package api.reports.services

import api.CacheServiceJedis
import api.reports.models.TopReportsData
import utils.cache_utils.{SimpleCacheServiceTrait, SrRedisKeyNamespace, SrRedisKeyRegister}

class TopReportsDaoJedisService (
                                  protected val cacheServiceJedis: CacheServiceJedis
                                ) extends SimpleCacheServiceTrait[String, List[TopReportsData]]{

  override protected val redisKeyNamespace: SrRedisKeyNamespace = SrRedisKeyRegister.topReportsData

  override val defaultExpiresInSeconds: Int = 7 * 24 * 60 * 60 // 1 week

  override def getCacheId(data: String): String = data

}
