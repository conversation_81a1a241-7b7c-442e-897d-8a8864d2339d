package api.reports.services

import api.columns.ProspectColumnDef
import api.reports.models.{DownloadReportProspect, ProspectEngagementReport}
import api.reports.services.DownloadReportService.getCustomColumnData
import api.reports.{CSVData, DownloadReportDAO, SanitizeReportInputParamService}
import api.tags.dao.CampaignTagDAO
import org.joda.time.{DateTime, Days}
import play.api.libs.json.JsValue
import utils.SRLogger

import java.net.URLEncoder
import scala.util.{Failure, Success}

sealed trait DownloadProspectEngagementReportErrors

object DownloadProspectEngagementReportErrors {

  case object MaxInterval2WeeksError extends DownloadProspectEngagementReportErrors

  case class SQLError(err: Throwable) extends DownloadProspectEngagementReportErrors

}

class DownloadReportService(
  downloadReportDAO: DownloadReportDAO,
  override protected val campaignTagDAO: CampaignTagDAO,
  prospectColumnDef: ProspectColumnDef
) extends SanitizeReportInputParamService {

  private def getCommonProspectCsvHeaders(
    teamId: Long
  ): (List[String], List[String]) = {

    val customColumnHeaders = prospectColumnDef.findCustomColumns(teamId = teamId)
      .map(_.name)
      .toList

    val baseColumnHeaders = List(

      "email",
      "first_name",
      "last_name",
      "company",

      "city",
      "state",
      "country",
      "phone",
      "job_title",
      "linkedin_url"

    )

    (baseColumnHeaders, customColumnHeaders)
  }

  def getCommonProspectCsvRowData(
    prospect: DownloadReportProspect,
    customColumnHeaders: List[String]
  ): (List[String], List[String]) = {

    val p = prospect

    val customColumnData: List[String] = getCustomColumnData(
      customColumns = customColumnHeaders,
      customFieldsFromDB = p.custom_fields
    )

    val baseColumnData = List(

      p.email,
      p.first_name.getOrElse(""),
      p.last_name.getOrElse(""),
      p.company.getOrElse(""),

      p.city.getOrElse(""),
      p.state.getOrElse(""),
      p.country.getOrElse(""),
      p.phone.getOrElse(""),
      p.job_title.getOrElse(""),
      p.linkedin_url.getOrElse("")

    )

    (baseColumnData, customColumnData)
  }

  private def getProspectEngagementReportCSVRows(
    teamId: Long,
    data: List[ProspectEngagementReport]
  ): CSVData = {

    val (baseProspectColumnHeaders, customProspectColumnHeaders) = getCommonProspectCsvHeaders(
      teamId = teamId
    )

    val csvHeader = baseProspectColumnHeaders ++
      customProspectColumnHeaders ++
      List(

        "last_contacted_at",
        "last_opened_at",
        "last_clicked_at",
        "last_replied_at",


        "active_campaign_names",
        "active_campaign_tags",
        "total_opens_in_active_campaigns",
        "total_clicks_in_active_campaigns",
        "replied"

      )


    val dataRows = data.map(r => {

      val (baseProspectRowData, customColumnsProspectRowData) = getCommonProspectCsvRowData(
        prospect = r.prospect,
        customColumnHeaders = customProspectColumnHeaders
      )

      val row = baseProspectRowData ++
        customColumnsProspectRowData ++
        List(

          r.last_contacted_at.getOrElse(""),
          r.last_opened_at.getOrElse(""),
          r.last_clicked_at.getOrElse(""),
          r.last_replied_at.getOrElse(""),

          r.active_campaign_names.mkString(" , "),
          r.active_campaign_tags.mkString(" , "),
          r.total_opens_in_active_campaigns,
          r.total_clicks_in_active_campaigns,
          r.replied

        )

      row

    })


    CSVData(
      headerRow = csvHeader,
      dataRows = dataRows
    )

  }

  def genFileName(
    from: Long,
    till: Long,
    teamName: String,
    reportNamePrefix: String

  ): String = {

    val sanitizedTeamName = teamName.trim.toLowerCase.replace(" ", "_").replace(".", "_")

    val filename = URLEncoder.encode(

      s"${reportNamePrefix}_${sanitizedTeamName}_${from / 1000}_${till / 1000}",

      "UTF-8"

    ) + "_report.csv"

    filename
  }

  // use case : ReportController.downloadProspectEngagementReport GET     /api/v2/reports/downloads/prospect_engagement
  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  def downloadProspectEngagementReport(
    fromMillis: Long,
    tillMillis: Long,
    teamId: Long,
    permittedAccountIds: Seq[Long],
    campaignIds: Seq[Long],
    campaignTagIds: Seq[Long]
  )(using Logger: SRLogger): Either[DownloadProspectEngagementReportErrors, CSVData] = {

    val fromTime = new DateTime(fromMillis)
    val tillTime = new DateTime(tillMillis)

    if (Days.daysBetween(fromTime, tillTime).getDays > 14) {

      Left(DownloadProspectEngagementReportErrors.MaxInterval2WeeksError)

    } else {

      val finalCampaignIds = campaignIdsForFiltering(

        campaignIds = if (campaignIds.isEmpty) None else Some(campaignIds),

        campaignTagIds = if (campaignTagIds.isEmpty) None else Some(campaignTagIds),

        teamId = teamId
      )

      downloadReportDAO
        .getProspectEngagementReportData(
          teamId = teamId,
          permittedAccountIds = permittedAccountIds,
          campaignIds = finalCampaignIds,
          fromMillis = fromMillis,
          tillMillis = tillMillis
        ) match {

        case Failure(dbError) =>

          Logger.error(s"Error while getProspectEngagementReportData, for teamID - $teamId", dbError)

          Left(DownloadProspectEngagementReportErrors.SQLError(err = dbError))

        case Success(data) =>

          Right(
            getProspectEngagementReportCSVRows(
              teamId = teamId,
              data = data
            )
          )

      }

    }

  }

}

object DownloadReportService {

  def getCustomColumnData(

    customColumns: List[String],
    customFieldsFromDB: JsValue

  ): List[String] = {

    customColumns.map(col => {

      // check and parse for String value, then Int value, then Float value

      val strVal = (customFieldsFromDB \ col).asOpt[String]

      if (strVal.isDefined) strVal.getOrElse("")
      else {

        val intVal = (customFieldsFromDB \ col).asOpt[Int].map(_.toString)

        if (intVal.isDefined) intVal.getOrElse("")
        else {

          val floatVal = (customFieldsFromDB \ col).asOpt[Float].map(_.toString)

          if (floatVal.isDefined) floatVal.getOrElse("") else ""

        }

      }
    })
  }
}
