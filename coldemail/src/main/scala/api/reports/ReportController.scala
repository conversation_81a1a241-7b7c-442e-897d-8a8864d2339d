package api.reports

import api.{ApiVersion, CONSTANTS}
import api.accounts.{PermType, PermissionMethods, PermissionRequest, PermissionUtils, TeamId}
import api.prospects.ProspectSavedFiltersInputQuery
import api.reports.services.{DownloadProspectEngagementReportErrors, DownloadReportService, InboxPlacementCheckLogsReportService}
import api.tags.ProspectTagDAOLegacy
import com.github.tototoshi.csv.CSVWriter
import org.joda.time.{DateTime, Duration, Months}
import play.api.libs.Files.SingletonTemporaryFileCreator
import play.api.libs.json._
import play.api.mvc.{Action, AnyContent, BaseController, ControllerComponents}
import utils.{Helpers, SRLogger}
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.dao.DomainInboxPlacementLog
import api.campaigns.models.CampaignTagId
import api.campaigns.services.CampaignId
import api.domain_health.{DomainHealthCheckService, GetDomainHealthCheckServiceError}
import api.reports.models.{ActivityOverviewReportFilter, SrReportType}
import api.spamtest.{GetSpamTestServiceError, SpamTestService}
import api.tasks.models.TaskStatsInputQuery
import play.api.libs.ws.WSClient
import utils.helpers.LogHelpers

import java.io.File
import java.net.URLEncoder
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import com.github.tototoshi.csv.defaultCSVFormat

class ReportController(
  protected val controllerComponents: ControllerComponents,
  reportService: ReportService,
  reportDAO: ReportDAO,
  spamTestService: SpamTestService,
  domainHealthCheckService: DomainHealthCheckService,
  downloadReportService: DownloadReportService,
  prospectTagDAOLegacy: ProspectTagDAOLegacy,
  inboxPlacementCheckLogsReportService: InboxPlacementCheckLogsReportService,
  permissionUtils: PermissionUtils,
  implicit val wsClient: WSClient
) extends BaseController {

  private implicit val ec: ExecutionContext = controllerComponents.executionContext

  def getStatsAccount(v: String, statsType: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_REPORTS,
    tidOpt = tid
  ).async(parse.json) { request =>

    given Logger: SRLogger= request.Logger
    val Res = request.Response

    val taOpt = request.actingTeamAccount
    if (taOpt.isEmpty) {

      Future {
        Res.ServerError("Invalid Team", e = None)
      }

    }
    else {
      val ta = taOpt.get
      val loggedinAccount = request.loggedinAccount
      val teamId = ta.team_id

      request.body.validate[StatsInputQuery] match {

        case JsError(e) => Future {
          Res.JsValidationError(errors = e, requestBody = Some(request.body))
        }

        case JsSuccess(data, _) =>

          // "0" is a special case -> "All team members", so it would have permittedAccountIds only
          val accountIdIsNotSubordinate = data.account_ids
            .filter(aid => aid != 0)
            .find { id =>
              !request.permittedAccountIds.contains(id)
            }

          if (accountIdIsNotSubordinate.isDefined) {

            Logger.info(s"[FATAL] getStatsAccountOverall accountIdIsNotSubordinate :: ${aid} :: ${tid}  :: Invalid team_members_account_ids: $accountIdIsNotSubordinate ")

            Future {
              Res.ForbiddenError("You are not authorized to do this")
            }

          } else {

            val accountIds: Set[Long] = ReportService.getAllowedAccountIdsForFilteringReportQuery(
              requestPermittedAccountIds = request.permittedAccountIds,
              teamId = teamId,
              loggedinAccount = loggedinAccount,
              data = data,
              ta = ta,
              Logger = Logger
            )

            statsType match {

              case "overall" =>

                reportService.getAccountStatsOverallOnly(
                  accountIds = accountIds,
                  teamId = ta.team_id,
                  data = data,
                    showWithManualData = request.loggedinAccount.org.org_metadata.show_manual_tasks_in_reports.getOrElse(false)
                ).map { overallStatsWithOpportunityStats =>

                  Res.Success(
                    message = "Account stats found",
                    data = Json.toJson(overallStatsWithOpportunityStats)
                  )

                }.recover { case err =>

                  Res.ServerError(err)

                }

              case "timewise" =>

                var dayWeek = "day"

                if (Months.monthsBetween(new DateTime(data.from), new DateTime(data.till)).getMonths() > 1) {

                  dayWeek = "week"

                }

                Future {

                  // from and till must be in seconds after epoch
                  reportService.getAccountStatsTimewiseWeeklyOnly(
                    accountIds = accountIds,
                    teamId = taOpt.get.team_id,
                    dayWeek = dayWeek,
                    data = data,
                      showWithManualData = request.loggedinAccount.org.org_metadata.show_manual_tasks_in_reports.getOrElse(false)

                  ) match {
                    case Failure(err) =>
                      Res.ServerError(err)
                    case Success(value) =>
                      Res.Success(
                        "Account stats found",
                        value
                      )
                  }
                }


              case _ => Future {
                Res.NotFoundError(s"API not found: $statsType")
              }
            }


          }

      }

    }

  }

  def getProspectsContactedReportForCampaign(
                                              v: String,
                                              tid: Option[Long],
                                              report_type: String
                                            ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_REPORTS,
    tidOpt = tid
  ).async(parse.json) { request =>

    given logger: SRLogger = request.Logger
    val Res = request.Response
    val timezone = request.loggedinAccount.timezone

    if (tid.isEmpty) {
      Future.successful(Res.ForbiddenError("Invalid team."))
    }
    else {
      request.body.validate[StatsInputQuery] match {
        case JsError(errors) =>
          Future.successful(Res.BadRequestError("Invalid Parameters"))

        case JsSuccess(filterObj, _) =>

          reportService.validateStatsQuery(
            filterObj = filterObj
          ) match {
            case Left(ValidateStatsQueryError.InvalidFromAndTillTime) =>
              Future.successful(Res.BadRequestError("Invalid from and till time."))

            case Left(ValidateStatsQueryError.CannotFetchDataOlderThan180Days) =>
              Future.successful(Res.BadRequestError("Cannot fetch data older than 180 days. Contact support."))

            case Left(ValidateStatsQueryError.InvalidCompareDuration) =>
              Future.successful(Res.BadRequestError("Invalid Compare Duration."))

            case Right(validatedStatsQuery: ValidatedStatsQuery) =>
              val teamId = TeamId(tid.get)
              val reportType = SrReportType.fromKey(report_type)

              reportType match {

                case Failure(e) =>
                  Future.successful(Res.BadRequestError("Unrecognized Report Type"))

                case Success(SrReportType.ProspectsContacted) =>
                  reportService.getProspectsContactedTimewiseReport(
                      campaignIds = validatedStatsQuery.campaignIds,
                      teamId = teamId,
                      accountIds = validatedStatsQuery.accountIds,
                      prospectListIds = validatedStatsQuery.prospectListIds,
                      campaignTagIds = validatedStatsQuery.campaignTagIds,
                      dayWeek = validatedStatsQuery.dayWeek,
                      compareFrom = validatedStatsQuery.compareFromInSeconds,
                      compareTill = validatedStatsQuery.compareTillInSeconds,
                      from = validatedStatsQuery.fromInSeconds,
                      till = validatedStatsQuery.tillInSeconds
                    )
                    .map(report => {
                      Res.Success("Fetched Prospects Contacted Timewise Report Successfully", Json.toJson(report))
                    })
                    .recover {
                      case e =>
                        Res.ServerError("Error while fetching Prospects Contacted Timewise report", Some(e))
                    }

                case Success(SrReportType.ProspectsContactedByChannel) =>
                  reportService.getProspectsContactedTimewiseReportByChannel(
                      campaignIds = validatedStatsQuery.campaignIds,
                      teamId = teamId,
                      accountIds = validatedStatsQuery.accountIds,
                      prospectListIds = validatedStatsQuery.prospectListIds,
                      campaignTagIds = validatedStatsQuery.campaignTagIds,
                      dayWeek = validatedStatsQuery.dayWeek,
                      from = validatedStatsQuery.fromInSeconds,
                      till = validatedStatsQuery.tillInSeconds
                    )
                    .map(report => {
                      Res.Success("Fetched Prospects Contacted Report By Channel", Json.toJson(report))
                    })
                    .recover {
                      case e =>
                        Res.ServerError("Error while fetching Prospects Contacted Report By Channel", Some(e))
                    }

                case Success(SrReportType.ProspectReplies) =>
                  reportService.getProspectRepliesCount(
                      campaignIds = validatedStatsQuery.campaignIds,
                      teamId = teamId,
                      accountIds = validatedStatsQuery.accountIds,
                      prospectListIds = validatedStatsQuery.prospectListIds,
                      campaignTagIds = validatedStatsQuery.campaignTagIds,
                      dayWeek = validatedStatsQuery.dayWeek,
                      compareFrom = validatedStatsQuery.compareFromInSeconds,
                      compareTill = validatedStatsQuery.compareTillInSeconds,
                      from = validatedStatsQuery.fromInSeconds,
                      till = validatedStatsQuery.tillInSeconds
                    )
                    .map(report => {
                      Res.Success("Prospect Replies Report Fetch Successful", Json.toJson(report))
                    })
                    .recover {
                      case e =>
                        Res.ServerError("Error while fetching Prospect Replies Count", Some(e))
                    }

                case Success(SrReportType.TopCampaigns) =>
                  reportService.getTopCampaignsByProspectsContacted(
                      from = validatedStatsQuery.fromInSeconds,
                      till = validatedStatsQuery.tillInSeconds,
                      teamId = TeamId(tid.get)
                    )
                    .map(report => {
                      Res.Success("Top Campaigns By Prospects Contacted Fetch Successful", Json.toJson(report))
                    })
                    .recover {
                      case e =>
                        Res.ServerError("Error while fetching Top Campaigns By Prospects Contacted", Some(e))
                    }

                case Success(SrReportType.TopTeamMembers) =>
                  reportService.getTopTeamMembersByProspectsContacted(
                      from = validatedStatsQuery.fromInSeconds,
                      till = validatedStatsQuery.tillInSeconds,
                      teamId = teamId
                    )
                    .map(report => {
                      Res.Success("Top Team Members By Prospects Contacted Fetch Successful", Json.toJson(report))
                    })
                    .recover{
                      case e =>
                        Res.ServerError("Error while fetching Top Team Members By Prospects Contacted", Some(e))
                    }

                case Success(SrReportType.TopProspectLists) =>
                  reportService.getTopProspectListsByProspectsContacted(
                      from = validatedStatsQuery.fromInSeconds,
                      till = validatedStatsQuery.tillInSeconds,
                      teamId = teamId
                    )
                    .map(report => {
                      Res.Success("Top Prospect Lists By Prospects Contacted Fetch Successful", Json.toJson(report))
                    })
                    .recover{
                      case e =>
                        Res.ServerError("Error while fetching Top Prospect Lists By Prospects Contacted", Some(e))
                    }

                case Success(SrReportType.ProspectsAdded) =>
                  reportService.getProspectsAddedTimeseriesReport(
                    campaignIds = validatedStatsQuery.campaignIds,
                    teamId = teamId,
                    accountIds = validatedStatsQuery.accountIds,
                    prospectListIds = validatedStatsQuery.prospectListIds,
                    campaignTagIds = validatedStatsQuery.campaignTagIds,
                    dayWeek = validatedStatsQuery.dayWeek,
                    compareFrom = validatedStatsQuery.compareFromInSeconds,
                    compareTill = validatedStatsQuery.compareTillInSeconds,
                    from = validatedStatsQuery.fromInSeconds,
                    till = validatedStatsQuery.tillInSeconds
                  ).map(report => {
                      Res.Success(s"${reportType.get.label} Report Fetch Successful", Json.toJson(report))
                    })
                    .recover{
                      case e =>
                        Res.ServerError(s"Error while fetching ${reportType.get.label}", Some(e))
                    }

                case Success(SrReportType.EngagementTouchpoints) =>
                  reportService.getEngagementTouchpointsReport(
                    campaignIds = validatedStatsQuery.campaignIds,
                    teamId = teamId,
                    accountIds = validatedStatsQuery.accountIds,
                    prospectListIds = validatedStatsQuery.prospectListIds,
                    campaignTagIds = validatedStatsQuery.campaignTagIds,
                    dayWeek = validatedStatsQuery.dayWeek,
                    compareFrom = validatedStatsQuery.compareFromInSeconds,
                    compareTill = validatedStatsQuery.compareTillInSeconds,
                    from = validatedStatsQuery.fromInSeconds,
                    till = validatedStatsQuery.tillInSeconds
                  )
                    .map(report => {
                      Res.Success(s"${reportType.get.label} Report Fetch Successful", Json.toJson(report))
                    })
                    .recover{
                      case e =>
                        Res.ServerError(s"Error while fetching ${reportType.get.label}", Some(e))
                    }

                case Success(SrReportType.TopCampaignsByProspectReplies) =>
                  reportService.getTopCampaignsByProspectReplies(
                    from = validatedStatsQuery.fromInSeconds,
                    till = validatedStatsQuery.tillInSeconds,
                    teamId = teamId
                  )
                    .map(report => {
                      Res.Success(s"${reportType.get.label} Report Fetch Successful", Json.toJson(report))
                    })
                    .recover{
                      case e =>
                        Res.ServerError(s"Error while fetching ${reportType.get.label}", Some(e))
                    }

                case Success(SrReportType.TopEmailSendingAccounts) =>
                  reportService.getTopEmailSendingAccountsReport(
                    from = validatedStatsQuery.fromInSeconds,
                    till = validatedStatsQuery.tillInSeconds,
                    teamId = teamId
                  )
                    .map(report => {
                      Res.Success(s"${reportType.get.label} Report Fetch Successful", Json.toJson(report))
                    })
                    .recover{
                      case e =>
                        Res.ServerError(s"Error while fetching ${reportType.get.label}", Some(e))
                    }

                case Success(SrReportType.TopLinkedinEngagementTouchpoints) |
                     Success(SrReportType.TopCallingAgents) =>
                  reportService.getTopChannelEngagementTouchpoints(
                    from = validatedStatsQuery.fromInSeconds,
                    till = validatedStatsQuery.tillInSeconds,
                    reportType = reportType.get,
                    teamId = teamId
                  )
                    .map(report => {
                      Res.Success(s"${reportType.get.label} Report Fetch Successful", Json.toJson(report))
                    })
                    .recover{
                      case e =>
                        Res.ServerError(s"Error while fetching ${reportType.get.label}", Some(e))
                    }

                case Success(SrReportType.CallsConnected) =>
                  reportService.getCallsConnectedTimewiseReport(
                    campaignIds = validatedStatsQuery.campaignIds,
                    teamId = teamId,
                    accountIds = validatedStatsQuery.accountIds,
                    prospectListIds = validatedStatsQuery.prospectListIds,
                    campaignTagIds = validatedStatsQuery.campaignTagIds,
                    dayWeek = validatedStatsQuery.dayWeek,
                    compareFrom = validatedStatsQuery.compareFromInSeconds,
                    compareTill = validatedStatsQuery.compareTillInSeconds,
                    timezone = timezone,
                    from = validatedStatsQuery.fromInSeconds,
                    till = validatedStatsQuery.tillInSeconds
                  )
                    .map(report => {
                      Res.Success(s"${reportType.get.label} Report Fetch Successful", Json.toJson(report))
                    })
                    .recover{
                      case e =>
                        Res.ServerError(s"Error while fetching ${reportType.get.label}", Some(e))
                    }

                case Success(SrReportType.TotalCalls) =>
                  reportService.getTotalCallsTimeseriesReport(
                    campaignIds = validatedStatsQuery.campaignIds,
                    teamId = teamId,
                    accountIds = validatedStatsQuery.accountIds,
                    prospectListIds = validatedStatsQuery.prospectListIds,
                    campaignTagIds = validatedStatsQuery.campaignTagIds,
                    dayWeek = validatedStatsQuery.dayWeek,
                    compareFrom = validatedStatsQuery.compareFromInSeconds,
                    compareTill = validatedStatsQuery.compareTillInSeconds,
                    timezone = timezone,
                    from = validatedStatsQuery.fromInSeconds,
                    till = validatedStatsQuery.tillInSeconds
                  )
                    .map(report => {
                      Res.Success(s"${reportType.get.label} Report Fetch Successful", Json.toJson(report))
                    })
                    .recover{
                      case e =>
                        Res.ServerError(s"Error while fetching ${reportType.get.label}", Some(e))
                    }

                case Success(SrReportType.CallsDialed) =>
                  reportService.getCallsDialedReport(
                      campaignIds = validatedStatsQuery.campaignIds,
                      teamId = teamId,
                      accountIds = validatedStatsQuery.accountIds,
                      prospectListIds = validatedStatsQuery.prospectListIds,
                      campaignTagIds = validatedStatsQuery.campaignTagIds,
                      dayWeek = validatedStatsQuery.dayWeek,
                      compareFrom = validatedStatsQuery.compareFromInSeconds,
                      compareTill = validatedStatsQuery.compareTillInSeconds,
                      timezone = timezone,
                      from = validatedStatsQuery.fromInSeconds,
                      till = validatedStatsQuery.tillInSeconds
                    )
                    .map(report => {
                      Res.Success(s"${reportType.get.label} Report Fetch Successful", Json.toJson(report))
                    })
                    .recover{
                      case e =>
                        Res.ServerError(s"Error while fetching ${reportType.get.label}", Some(e))
                    }

                case Success(SrReportType.TotalCallDuration) =>
                  reportService.getTotalCallsDurationTimewiseReport(
                      campaignIds = validatedStatsQuery.campaignIds,
                      teamId = teamId,
                      accountIds = validatedStatsQuery.accountIds,
                      prospectListIds = validatedStatsQuery.prospectListIds,
                      campaignTagIds = validatedStatsQuery.campaignTagIds,
                      dayWeek = validatedStatsQuery.dayWeek,
                      compareFrom = validatedStatsQuery.compareFromInSeconds,
                      compareTill = validatedStatsQuery.compareTillInSeconds,
                      timezone = timezone,
                      from = validatedStatsQuery.fromInSeconds,
                      till = validatedStatsQuery.tillInSeconds
                    )
                    .map(report => {
                      Res.Success(s"${reportType.get.label} Report Fetch Successful", Json.toJson(report))
                    })
                    .recover{
                      case e =>
                        Res.ServerError(s"Error while fetching ${reportType.get.label}", Some(e))
                    }

                case Success(SrReportType.AverageCallDuration) =>
                  reportService.getAverageCallDurationTimewiseReport(
                      campaignIds = validatedStatsQuery.campaignIds,
                      teamId = teamId,
                      accountIds = validatedStatsQuery.accountIds,
                      prospectListIds = validatedStatsQuery.prospectListIds,
                      campaignTagIds = validatedStatsQuery.campaignTagIds,
                      dayWeek = validatedStatsQuery.dayWeek,
                      compareFrom = validatedStatsQuery.compareFromInSeconds,
                      compareTill = validatedStatsQuery.compareTillInSeconds,
                      timezone = timezone,
                      from = validatedStatsQuery.fromInSeconds,
                      till = validatedStatsQuery.tillInSeconds
                    )
                    .map(report => {
                      Res.Success(s"${reportType.get.label} Report Fetch Successful", Json.toJson(report))
                    })
                    .recover{
                      case e =>
                        Res.ServerError(s"Error while fetching ${reportType.get.label}", Some(e))
                    }

                case Success(SrReportType.LinkedinTasks) |
                     Success(SrReportType.ConnectionRequests) |
                     Success(SrReportType.LinkedinMessages) |
                     Success(SrReportType.LinkedinViewProfile) |
                     Success(SrReportType.LinkedinInmails) =>

                  reportService.fetchLinkedinTasksByStatusTimeseries(
                      campaignIds = validatedStatsQuery.campaignIds,
                      teamId = teamId,
                      accountIds = validatedStatsQuery.accountIds,
                      prospectListIds = validatedStatsQuery.prospectListIds,
                      campaignTagIds = validatedStatsQuery.campaignTagIds,
                      reportType = reportType.get,
                      dayWeek = validatedStatsQuery.dayWeek,
                      compareFrom = validatedStatsQuery.compareFromInSeconds,
                      compareTill = validatedStatsQuery.compareTillInSeconds,
                      from = validatedStatsQuery.fromInSeconds,
                      till = validatedStatsQuery.tillInSeconds
                    ).map(report => {
                      Res.Success(s"${reportType.get.label} Report Fetch Successful", Json.toJson(report))
                    })
                    .recover{
                      case e =>
                        Res.ServerError(s"Error while fetching ${reportType.get.label}", Some(e))
                    }
              }
          }
      }
    }
  }

  def getLinkedinTasksByTypes(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_REPORTS,
    tidOpt = tid
  ).async(parse.json) {request =>

    given logger: SRLogger = request.Logger
    val Res = request.Response

    if (tid.isEmpty) {

      Future.successful(Res.ServerError("Invalid team", e = None))

    }
    else if (!(tid.get == 0 || request.loggedinAccount.teams.exists(team => team.team_id == tid.get))) {

      Future.successful(Res.ForbiddenError("You are not Authorized"))

    }
    else {
      request.body.validate[StatsInputQuery] match {
        case JsError(errors) =>
          Future.successful(Res.JsValidationError(errors))

        case JsSuccess(filterObj, _) =>

          reportService.validateStatsQuery(
            filterObj = filterObj
          ) match {
            case Left(ValidateStatsQueryError.InvalidFromAndTillTime) =>
              Future.successful(Res.BadRequestError("Invalid from and till time."))

            case Left(ValidateStatsQueryError.CannotFetchDataOlderThan180Days) =>
              Future.successful(Res.BadRequestError("Cannot fetch data older than 180 days. Contact support."))

            case Left(ValidateStatsQueryError.InvalidCompareDuration) =>
              Future.successful(Res.BadRequestError("Invalid Compare Duration."))

            case Right(validatedStatsQuery: ValidatedStatsQuery) =>
              reportService.getTotalLinkedinTasksByTypes(
                  campaignIds = validatedStatsQuery.campaignIds,
                  teamId = TeamId(tid.get),
                  accountIds = validatedStatsQuery.accountIds,
                  prospectListIds = validatedStatsQuery.prospectListIds,
                  campaignTagIds = validatedStatsQuery.campaignTagIds,
                  compareFrom = validatedStatsQuery.compareFromInSeconds,
                  compareTill = validatedStatsQuery.compareTillInSeconds,
                  from = validatedStatsQuery.fromInSeconds,
                  till = validatedStatsQuery.tillInSeconds
                )
                .map(counts => {
                  Res.Success("Linkedin Tasks Counts fetch Successful", Json.obj("linkedin_tasks_by_types" -> counts))
                })
                .recover {
                  case e =>
                    Res.ServerError("Error while fetching tasks completed counts", Some(e))
                }
          }
      }
    }

  }

  def getCallDispositionCountByType(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_REPORTS,
    tidOpt = tid
  ).async(parse.json) { request =>

    given logger: SRLogger = request.Logger
    val Res = request.Response

    if (tid.isEmpty) {

      Future.successful(Res.ServerError("Invalid team", e = None))

    }
    else if (!(tid.get == 0 || request.loggedinAccount.teams.exists(team => team.team_id == tid.get))) {

      Future.successful(Res.ForbiddenError("You are not Authorized"))

    }
    else {
      request.body.validate[StatsInputQuery] match {
        case JsError(errors) =>
          Future.successful(Res.JsValidationError(errors))

        case JsSuccess(filterObj, _) =>

          reportService.validateStatsQuery(
            filterObj = filterObj
          ) match {
            case Left(ValidateStatsQueryError.InvalidFromAndTillTime) =>
              Future.successful(Res.BadRequestError("Invalid from and till time."))

            case Left(ValidateStatsQueryError.CannotFetchDataOlderThan180Days) =>
              Future.successful(Res.BadRequestError("Cannot fetch data older than 180 days. Contact support."))

            case Left(ValidateStatsQueryError.InvalidCompareDuration) =>
              Future.successful(Res.BadRequestError("Invalid Compare Duration."))

            case Right(validatedStatsQuery: ValidatedStatsQuery) =>
              Future {
                reportService.getDispositionCountsByType(
                  campaignIds = validatedStatsQuery.campaignIds,
                  teamId = TeamId(tid.get),
                  accountIds = validatedStatsQuery.accountIds,
                  prospectListIds = validatedStatsQuery.prospectListIds,
                  replySentimentUuids = validatedStatsQuery.replySentimentUuids,
                  compareFrom = validatedStatsQuery.compareFromInSeconds,
                  compareTill = validatedStatsQuery.compareTillInSeconds,
                  from = validatedStatsQuery.fromInSeconds,
                  till = validatedStatsQuery.tillInSeconds
                ) match {

                  case Failure(exception) =>
                    logger.error(s"[ReportsController.getCallDispositionCountByType] Error while fetching reports for :: tid:: ${tid.get}", err = exception)
                    Res.ServerError("Error while fetching reports", e = Some(exception))

                  case Success(counts) =>
                    Res.Success("Call Disposition Report Fetch Successful", Json.obj("call_disposition_counts" -> counts))

                }

              }
          }
      }

    }
  }


  def getTasksCompletedCounts(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_REPORTS,
    tidOpt = tid
  ).async(parse.json) { request =>

    given logger: SRLogger = request.Logger
    val Res = request.Response
    if (tid.isEmpty) {

      Future.successful(Res.ServerError("Invalid team", e = None))

    }
    else if (!(tid.get == 0 || request.loggedinAccount.teams.exists(team => team.team_id == tid.get))) {

      Future.successful(Res.ForbiddenError("You are not Authorized"))

    }
    else {
      request.body.validate[StatsInputQuery] match {
        case JsError(errors) =>
          Future.successful(Res.JsValidationError(errors))

        case JsSuccess(filterObj, _) =>

          reportService.validateStatsQuery(
            filterObj = filterObj
          ) match {
            case Left(ValidateStatsQueryError.InvalidFromAndTillTime) =>
              Future.successful(Res.BadRequestError("Invalid from and till time."))

            case Left(ValidateStatsQueryError.CannotFetchDataOlderThan180Days) =>
              Future.successful(Res.BadRequestError("Cannot fetch data older than 180 days. Contact support."))

            case Left(ValidateStatsQueryError.InvalidCompareDuration) =>
              Future.successful(Res.BadRequestError("Invalid Compare Duration."))

            case Right(validatedStatsQuery: ValidatedStatsQuery) =>
              reportService.getTotalTasksCompletedCountsByChannel(
                  campaignIds = validatedStatsQuery.campaignIds,
                  teamId = TeamId(tid.get),
                  accountIds = validatedStatsQuery.accountIds,
                  prospectListIds = validatedStatsQuery.prospectListIds,
                  compareFrom = validatedStatsQuery.compareFromInSeconds,
                  compareTill = validatedStatsQuery.compareTillInSeconds,
                  from = validatedStatsQuery.fromInSeconds,
                  till = validatedStatsQuery.tillInSeconds
                )
                .map(counts => {
                  Res.Success("Tasks Completed Counts fetch Successful", Json.obj("tasks_completed_counts_and_change" -> counts))
                })
                .recover {
                  case e =>
                    Res.ServerError("Error while fetching tasks completed counts", Some(e))
                }
          }
      }
    }
  }

  def getLinkedinConnectionRate(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_REPORTS,
    tidOpt = tid
  ).async(parse.json) { request =>

    given logger: SRLogger = request.Logger

    val Res = request.Response
    if (tid.isEmpty) {

      Future.successful(Res.ServerError("Invalid team", e = None))

    }
    else if (!(tid.get == 0 || request.loggedinAccount.teams.exists(team => team.team_id == tid.get))) {

      Future.successful(Res.ForbiddenError("You are not Authorized"))

    }
    else {
      request.body.validate[StatsInputQuery] match {
        case JsError(errors) =>

          logger.shouldNeverHappen(
            msg = s"Failed to parse stats input query tid :: ${tid.get} :: errors: $errors"
          )
          Future.successful(Res.JsValidationError(errors))

        case JsSuccess(filterObj, _) =>

          reportService.validateStatsQuery(
            filterObj = filterObj
          ) match {
            case Left(ValidateStatsQueryError.InvalidFromAndTillTime) =>

              logger.error(s"Invalid from and till time for tid :: ${tid.get} filterObj:: ${filterObj}")
              Future.successful(Res.BadRequestError("Invalid from and till time."))

            case Left(ValidateStatsQueryError.CannotFetchDataOlderThan180Days) =>
              logger.error(s"Cannot fetch data older than 180 days for tid :: ${tid.get} filterObj:: ${filterObj}")
              Future.successful(Res.BadRequestError("Cannot fetch data older than 180 days. Contact support."))

            case Left(ValidateStatsQueryError.InvalidCompareDuration) =>
              logger.error(s"Invalid Compare Duration for tid :: ${tid.get} filterObj:: ${filterObj}")
              Future.successful(Res.BadRequestError("Invalid Compare Duration."))

            case Right(validatedStatsQuery: ValidatedStatsQuery) =>
              reportService.getLinkedinConnectionRateReport(
                  team_id = TeamId(id = tid.get),
                  data = validatedStatsQuery
                )
              match {
                case Success(connectionRateStats) =>
                  Future.successful(Res.Success("LinkedIn Connection Rate fetch Successful", Json.obj("data" -> connectionRateStats)))
                case Failure(e) =>

                  logger.error(s"Error while fetching LinkedIn connection rate for tid:: ${tid.get}", err = e )
                  Future.successful(Res.ServerError("Error while fetching LinkedIn connection rate", Some(e)))
              }
          }
      }
    }
  }

  def getCampaignSendStartReport(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_REPORTS,
    tidOpt = tid,
    allowInAgencyDashboard = true
  ) { (request: PermissionRequest[AnyContent]) =>

      given Logger: SRLogger = request.Logger
      val Res = request.Response

      Logger.info(s"\n\n\ngetCampaignSendStartReport called: tid = $tid")

      if (tid.isEmpty) {

        Res.ServerError("Invalid team", e = None)

      }
      else if (!(tid.get == 0 || request.loggedinAccount.teams.exists(team => team.team_id == tid.get))) {

        Res.ForbiddenError("You are not Authorized")

      }
      else {
        reportService.getCampaignSendStartReport(
          orgId = OrgId(request.loggedinAccount.org.id),
          teamId = TeamId(tid.get)
        ) match {

          case Failure(err) =>
            Logger.error(s"Error While fetching campaign send start reports:: $err")
            Res.ServerError("Error While fetching campaign send start reports", e = Some(err))

          case Success(reports) =>
            Res.Success(
              data = Json.obj(
              "campaign_send_start_report" -> reports
            ))
        }

      }
  }


  def getStatsCampaign(v: String, statsType: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_REPORTS,
    tidOpt = tid
  ).async(parse.json) { request =>

    given Logger: SRLogger= request.Logger
    val Res = request.Response

    val t = request.actingTeamAccount
    if (t.isEmpty) {

      Future {
        Res.ServerError("Invalid team", e = None)
      }

    } else {

      request.body.validate[StatsInputQuery] match {

        case JsError(e) => Future {
          Res.JsValidationError(errors = e, requestBody = Some(request.body))
        }

        case JsSuccess(data, _) =>


          // "0" is a special case -> "All team members", so it would have permittedAccountIds only
          val accountIdIsNotSubordinate = data.account_ids
            .filter(aid => aid != 0)
            .find { id =>
              !request.permittedAccountIds.contains(id)
            }


          if (accountIdIsNotSubordinate.isDefined) {

            Logger.info(s"[FATAL] getStatsCampaignOverall accountIdIsNotSubordinate :: ${aid.get} :: ${tid.get} :: Invalid team_members_account_ids accountIdIsNotSubordinate: $accountIdIsNotSubordinate ")

            Future {
              Res.ForbiddenError("You are not authorized to do this")
            }

          } else if (data.campaign_ids.isEmpty || data.campaign_ids.get.isEmpty) {

            Logger.info(s"[FATAL] getStatsCampaignOverall :: ${aid.get} :: ${tid.get} :: Invalid campaignIds: ${data.campaign_ids} ")

            Future {
              Res.BadRequestError("Invalid campaignIds")
            }

          } else {

            val ta = t.get
            val loggedinAccount = request.loggedinAccount
            val teamId = ta.team_id

            val accountIds: Set[Long] = ReportService.getAllowedAccountIdsForFilteringReportQuery(
              requestPermittedAccountIds = request.permittedAccountIds,
              teamId = teamId,
              loggedinAccount = loggedinAccount,
              data = data,
              ta = ta,
              Logger = Logger
            )


            statsType match {

              case "overall" =>

                reportService.getCampaignStatsOverallOnly(
                  accountIds = accountIds,
                  teamId = t.get.team_id,
                  data = data
                ).map { overallStatsWithOpportunityStats =>

                  Res.Success(
                    message = "Campaign stats found",
                    data = Json.toJson(overallStatsWithOpportunityStats)
                  )

                }.recover { case err =>

                  Res.ServerError(err)

                }

              case "timewise" =>

                var dayWeek = "day"

                if (Months.monthsBetween(new DateTime(data.from), new DateTime(data.till)).getMonths() > 1) {

                  dayWeek = "week"

                }

                Future {
                  reportService.getCampaignStatsTimeWiseWeeklyOnly(
                    accountIds = accountIds,
                    teamId = t.get.team_id,
                    dayWeek = dayWeek,
                    data = data
                  ) match {
                    case Failure(err) =>
                      Res.ServerError(err)
                    case Success(value) =>
                      Res.Success(
                        "Campaign stats found",
                        value
                      )
                  }
                }

              case "stepwise" =>

                Future {

                  reportService.getCampaignStatsStepWiseOnly(
                    accountIds = accountIds,
                    teamId = t.get.team_id,
                    data = data,
                    isABTestingEnabled = request.loggedinAccount.org.settings.enable_ab_testing
                  ) match {
                    case Failure(err) => Res.ServerError(err)
                    case Success(value) =>

                      Res.Success(
                        "Campaign stats found",
                        value
                      )
                  }

                }

              case _ => Future {
                Res.NotFoundError(s"API not found: $statsType")
              }

            }

          }
      }

    }

  }

  def getEmailsSentReport(
    v: String,
    statsType: String,
    tid: Option[Long]
  ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_REPORTS,
    tidOpt = tid
  ).async(parse.json) { request =>
    Future {
      val Logger = request.Logger
      val Res = request.Response

      val t = request.actingTeamAccount
      if (t.isEmpty) {

        Res.ServerError("Invalid team", e = None)

      } else {

        request.body.validate[StatsInputQuery] match {

          case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

          case JsSuccess(data, _) =>

            statsType match {

              case "overall" =>

                reportService.getEmailsSentReportOverall(
                  permittedAccountIds = request.permittedAccountIds,
                  loggedinAccountId = request.loggedinAccount.internal_id,
                  teamId = t.get.team_id,
                  data = data,
                    showWithManualData = request.loggedinAccount.org.org_metadata.show_manual_tasks_in_reports.getOrElse(false)
                ) match {

                  case Left(ReportGetError.SQLException(e)) => Res.ServerError(e)

                  case Left(ReportGetError.PermissionError(_)) => Res.ForbiddenError

                  case Right(report) =>
                    Res.Success(
                      "Email sent report found",
                      Json.obj(
                        "report" -> report
                      )
                    )

                }


              case "timewise" =>

                val reportInterval = reportService.getReportInterval(
                  fromMillis = data.from,
                  tillMillis = data.till
                )

                reportService.getEmailsSentReportTimewise(
                  permittedAccountIds = request.permittedAccountIds,
                  loggedinAccountId = request.loggedinAccount.internal_id,
                  teamId = t.get.team_id,
                  dayWeek = reportInterval,
                  data = data,
                    showWithManualData = request.loggedinAccount.org.org_metadata.show_manual_tasks_in_reports.getOrElse(false)

                ) match {

                  case Left(ReportGetError.SQLException(e)) => Res.ServerError(e)

                  case Left(ReportGetError.PermissionError(_)) => Res.ForbiddenError

                  case Right(report) =>
                    Res.Success(
                      "Email sent report found",
                      Json.obj(
                        "interval" -> reportInterval.toString,
                        "report" -> report
                      )
                    )
                }

              case _ => Res.NotFoundError(s"API not found: $statsType")


            }

        }

      }
    }

  }

  def downloadReport(
    v: String,
    campaignIds: Seq[Long],
    from: Long,
    till: Long,
    aid: Option[Long],
    tid: Option[Long]
  ) =   permissionUtils.checkPermission(
    version = v,
    permission = PermType.DOWNLOAD_REPORTS,
    tidOpt = tid
  ) { (request: PermissionRequest[AnyContent]) =>
    given Logger: SRLogger = request.Logger
    val Res = request.Response
    val org = request.loggedinAccount.org

    val fromDateTime = if(from < 9999999999L) {
      new DateTime(from * 1000)
    } else {
      new DateTime(from)
    }
    val tillDateTime =  if(from < 9999999999L) {
      new DateTime(till * 1000)
    } else {
      new DateTime(till)
    }
    val durationInDays = new Duration(fromDateTime, tillDateTime).getStandardDays

    if (request.actingTeamAccount.isEmpty) {
      Res.ServerError("Invalid team", e = None)
    } else if (campaignIds.isEmpty) {
      Res.BadRequestError("Please send valid campaign_ids")
    } else if (durationInDays > 94){
      Res.BadRequestError("The requested period is too large, you can download reports in a batch of three months.")
    } else {

      val ta = request.actingTeamAccount.get


      val includeProspectTimestampColumns: Boolean = (
        org.id == 3805 // MedicalLeverage - katie moore
          || org.id == 23 // Heaplabs
          || org.id == 2
        )

      reportService.getReportCSVRows(
        teamId = ta.team_id,
        campaignIds = campaignIds,
        permittedAccountIds = request.permittedAccountIds,
        fromMillis = from,
        tillMillis = till,
        includeProspectTimestampColumns = includeProspectTimestampColumns,
      ) match {

        case Failure(e) =>

          Res.ServerError(err = e)


        case Success(CSVData(headerRow, dataRows)) =>

          val allCsvRows = Seq(headerRow) ++ dataRows


          val filename = URLEncoder.encode(s"prospects_report_${ta.team_name.trim.toLowerCase.replace(" ", "_").replace(".", "_")}_${from / 1000}_${till / 1000}", "UTF-8") + "_report.csv"

          val f = SingletonTemporaryFileCreator.create(new File("/tmp/" + filename).toPath)

          val writer = CSVWriter.open(f.toFile)

          allCsvRows.foreach(r => {
            writer.writeRow(r)
          })

          writer.close()


          Ok
            .sendFile(f.toFile, onClose = () => {
              f.delete()
            })
            .withHeaders(
              "Content-Disposition" -> s"attachment;filename=$filename",
              "Access-Control-Expose-Headers" -> "Content-Disposition"
            )
            .as("text/csv")

      }

    }

    //    val path: java.nio.file.Path = f.toPath
    //    val source: Source[ByteString, _] = FileIO.fromPath(path)
    //
    //    //Logger.info(s"\n\n\nFILE cReAted: ${f}")
    //
    //    Result(
    //      header = ResponseHeader(200, Map("Content-Disposition" -> s"attachment;filename=$filename")),
    //      body = HttpEntity.Streamed(source, Some(f.length), Some("text/csv"))
    //    )

  }

  // use case : ReportController.downloadProspectEngagementReport GET     /api/v2/reports/downloads/prospect_engagement
  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  def downloadProspectEngagementReport(
    from: Long,
    till: Long,
    campaign_ids: Seq[Long],
    campaign_tag_ids: Seq[Long],
    tid: Option[Long]
  ) = permissionUtils.checkPermission(
    permission = PermType.DOWNLOAD_REPORTS,
    tidOpt = tid
  ) { (request: PermissionRequest[AnyContent]) =>

    val Res = request.Response
    given Logger: SRLogger= request.Logger
    if (request.actingTeamAccount.isEmpty) {
      Res.ServerError("Invalid team", e = None)
    } else {

      val ta = request.actingTeamAccount.get

      downloadReportService.downloadProspectEngagementReport(
        teamId = ta.team_id,
        campaignIds = campaign_ids,
        campaignTagIds = campaign_tag_ids,
        permittedAccountIds = request.permittedAccountIds,
        fromMillis = from,
        tillMillis = till
      ) match {

        case Left(DownloadProspectEngagementReportErrors.MaxInterval2WeeksError) =>

          Res.BadRequestError("You can select an interval of upto 2 weeks for downloading this report")

        case Left(DownloadProspectEngagementReportErrors.SQLError(err)) =>

          Res.ServerError(err = err)

        case Right(CSVData(headerRow, dataRows)) =>

          val allCsvRows = Seq(headerRow) ++ dataRows

          val filename = downloadReportService.genFileName(
            from = from,
            till = till,
            teamName = ta.team_name,
            reportNamePrefix = "prospect_engagement"
          )

          val f = SingletonTemporaryFileCreator.create(new File("/tmp/" + filename).toPath)

          val writer = CSVWriter.open(f.toFile)

          allCsvRows.foreach(r => {
            writer.writeRow(r)
          })

          writer.close()


          Ok
            .sendFile(f.toFile, onClose = () => {
              f.delete()
            })
            .withHeaders(
              "Content-Disposition" -> s"attachment;filename=$filename",
              "Access-Control-Expose-Headers" -> "Content-Disposition"
            )
            .as("text/csv")

      }

    }

  }

  def getOtherReport(v: String, statsType: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_REPORTS,
    tidOpt = tid
  ).async(parse.json) { request =>
    Future {
      given Logger: SRLogger = request.Logger
      val Res = request.Response

      val taOpt = request.actingTeamAccount
      if (taOpt.isEmpty) {

        Res.ServerError("Invalid Team", e = None)

      }
      else {

        request.body.validate[StatsInputQuery] match {

          case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

          case JsSuccess(data, _) =>

            // "0" is a special case -> "All team members", so it would have permittedAccountIds only
            val accountIdIsNotSubordinate = data.account_ids
              .filter(aid => aid != 0)
              .find { id =>
                !request.permittedAccountIds.contains(id)
              }


            if (accountIdIsNotSubordinate.isDefined) {

              Logger.error(s"[FATAL] getAllListReport accountIdIsNotSubordinate :: ${aid.get} :: ${tid.get}  :: Invalid team_members_account_ids accountIdIsNotSubordinate: $accountIdIsNotSubordinate ")

              Res.ForbiddenError("You are not authorized to do this")

            } else {


              val ta = taOpt.get
              val loggedinAccount = request.loggedinAccount
              val teamId = ta.team_id

              val accountIds: Set[Long] = ReportService.getAllowedAccountIdsForFilteringReportQuery(
                requestPermittedAccountIds = request.permittedAccountIds,
                teamId = teamId,
                loggedinAccount = loggedinAccount,
                data = data,
                ta = ta,
                Logger = Logger
              )


              statsType match {

                case "list" =>

                  Res.Success(
                    "List report found",
                    reportDAO.getListReportOnly(accountIds = accountIds, teamId = teamId, data = data)
                  )


                case "template" =>

                  val teamName = request.actingTeamAccount.get.team_name

                  reportService
                    .getTemplateReportOnly(
                      teamName = teamName,
                      loggedInAccountId = AccountId(loggedinAccount.internal_id),
                      accountIds = accountIds,
                      tid = teamId,
                      data = data
                    ) match {
                    case Failure(exception) =>

                      Res.ServerError(exception)


                    case Success(templateStats) =>

                      Res.Success(
                        "Templates report found",
                        Json.obj(
                          "templates_stats" -> templateStats
                        )
                      )
                  }



                case "team" =>

                  // in this report, we need the accountIds, so retaining the earlier logic for accountIds
                  val teamMemberAccountIds = if (data.account_ids.contains(0)) request.permittedAccountIds
                  else if (data.account_ids.nonEmpty) data.account_ids
                  else Seq (ta.user_id)

                  val showWithManualData: Boolean = request.loggedinAccount.org.org_metadata.show_manual_tasks_in_reports.getOrElse(false)

                  Res.Success(
                    "Teams report found",
                    reportDAO.getTeamMemberReportOnly(team_member_account_ids = teamMemberAccountIds, tid = teamId, data = data,showWithManualData = showWithManualData)
                  )

                case "link" =>

                  Res.Success(
                    "Links report found",
                    reportDAO.getTopLinksClickReportOnly(accountIds = accountIds, tid = teamId, data = data)
                  )

                case "best_time_of_day" =>

                  reportDAO.getBestTimeOfDayReportOnly(accountIds = accountIds, tid = teamId,
                    timezone = request.loggedinAccount.timezone.getOrElse("UTC"),
                    data = data
                  ) match {

                    case Failure(e) =>
                      Logger.error(s"[FATAL] getAllBestTimeOfDayReport.getBestTimeOfDayReportOnly:: ${LogHelpers.getStackTraceAsString(e)}")
                      Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}", e = Some(e))

                    case Success(stats) =>
                      Res.Success(
                        "Best time of day report found",
                        stats
                      )
                  }

                case "prospect_category" =>
                  reportDAO.getProspectCategoriesReport(
                    accountIds = accountIds,
                    tid = teamId,
                    data = data
                  ) match {

                    case Failure(e) =>
                      Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}", e = Some(e))

                    case Success(statsFromDB) =>

                      val categoriesInTeam = request.loggedinAccount.teams.find(t => t.team_id == teamId)
                          .map(_.prospect_categories_custom)
                          .getOrElse(Seq())

                      val statsFoundForCategories = statsFromDB.map(_.prospect_category_id)

                      var statsResponse = statsFromDB

                      // for categories, where no data was found in db, just append those categories to the result with 0 counts
                      val zeroReportCount = ReportCount(
                        prospects_contacted = 0,
                        prospects_opened = 0,
                        clicked = 0,
                        bounced = 0,
                        opted_out = 0,
                        replied = 0
                      )
                      categoriesInTeam.foreach(cat => {
                        if (!statsFoundForCategories.contains(cat.id)) {
                          statsResponse = statsResponse :+ ReportForProspectCategories(
                            prospect_category = cat.name,
                            prospect_category_id = cat.id,
                            total = zeroReportCount,
                            percentage = zeroReportCount
                          )
                        }
                      })

                      Res.Success("Report found", Json.obj("report" -> statsResponse))
                  }

                case "sender_email_rotation" =>
                  reportService.getSenderEmailReport(
                    accountIds = accountIds,
                    tid = teamId,
                    data = data
                  ) match {

                    case Failure(e) =>
                      Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}", e = Some(e))

                    case Success(statsFromDB) =>

                      Res.Success("Report found", Json.obj("report" -> statsFromDB))
                  }


                case "tags" =>
                  reportDAO.getTagsReport(
                    accountIds = accountIds,
                    tid = teamId,
                    data = data
                  ) match {

                    case Failure(e) =>
                      Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}", e = Some(e))

                    case Success(statsFromDB) =>

                      val tagsInTeam = prospectTagDAOLegacy.getTags(teamId = teamId, tags = Seq())
                        .get

                      val statsFoundForTags = statsFromDB.map(_.tag_id)

                      var statsResponse = statsFromDB

                      // for tags, where no data was found in db, just append those categories to the result with 0 counts
                      val zeroReportCount = ReportCount(
                        prospects_contacted = 0,
                        prospects_opened = 0,
                        clicked = 0,
                        bounced = 0,
                        opted_out = 0,
                        replied = 0
                      )
                      tagsInTeam.foreach(tag => {
                        if (!statsFoundForTags.contains(tag.tag_id)) {
                          statsResponse = statsResponse :+ ReportForTags(
                            tag = tag.tag,
                            tag_id = tag.tag_id,
                            total_prospects = 0,
                            total = zeroReportCount,
                            percentage = zeroReportCount
                          )
                        }
                      })

                      Res.Success("Report found", Json.obj("report" -> statsResponse))
                  }

                case "click_feed" =>

                  Res.Success(
                    "Click tracking report found",
                    reportDAO.getClickTrackingReportOnly(accountIds = accountIds, tid = teamId, data = data)
                  )

                case _ => Res.NotFoundError(s"API not found: $statsType")

              }

            }
        }
      }

    }
  }

  def saveStatsReportFilters(v: String, aid: Option[Long], tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_REPORTS,
    tidOpt = tid
  )(parse.json) { (request: PermissionRequest[JsValue]) =>
    val Logger = request.Logger
    val Res = request.Response

    if (request.actingTeamAccount.isEmpty) {

      Res.BadRequestError("Please send valid tid")

    } else {

      request.body.validate[ProspectSavedFiltersInputQuery] match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>

          val ta = request.actingTeamAccount.get
          reportDAO.saveStatsReportFilters(accountId = ta.user_id, teamId = ta.team_id, teamAccountId = ta.ta_id, data = data) match {

            case Failure(e) =>
              Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))

            case Success(filters) =>
              Res.Success("Stats report filters saved", Json.obj(
                "all_filters" -> filters
              ))
          }
      }

    }

  }

  def findSavedStatsReportFilters(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_REPORTS,
    tidOpt = tid
  ).async { request =>

    Future {
      val Logger = request.Logger
      val Res = request.Response

      if (tid.isEmpty) {

        Res.BadRequestError("Please send valid tid")

      } else {

        val ta = request.actingTeamAccount.get
        reportDAO.findSavedStatsReportFilters(accountIds = request.permittedAccountIds, teamId = ta.team_id) match {

          case Failure(e) =>
            Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))

          case Success(filters) =>
            Res.Success("Stats report filters found", Json.obj(
              "all_filters" -> filters
            ))
        }

      }
    }

  }


  def getReplySentimentStatsReport(
                              v: String,
                              tid: Option[Long]
                            ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_REPORTS,
    tidOpt = tid
  ).async(parse.json) { request =>


    Future {
      given Logger: SRLogger= request.Logger
      val Res = request.Response

      request.body.validate[StatsInputQuery] match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>

          tid match {
            case None =>
              Res.BadRequestError("Please send team id")
            case Some(team_id) =>

              reportService.getReplySentimentReport(
                team_id = team_id,
                data = data
              ) match {
                case Failure(err) =>
                  Res.ServerError(err)
                case Success(value) =>
                  Res.Success(
                    "Account stats found",
                    Json.toJson(value)
                  )
              }
          }
      }
    }
  }

  def getMultichannelTasksStats(v: String, tid: Option[Long]) = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_REPORTS,
    tidOpt = tid
  )).async(parse.json) { request =>
    val Res = request.Response
    given Logger: SRLogger= request.Logger
    val orgId = OrgId(id = request.loggedinAccount.org.id)
    val ta = request.actingTeamAccount


    if (tid.isEmpty || ta.isEmpty) {
      Future.successful(Res.ForbiddenError(s"You are not authorized"))
    }
    else {

      request.body.validate[TaskStatsInputQuery] match {
        case JsError(e) =>
          Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

        case JsSuccess(data, _) =>

          reportService.getMultiChannelTasksStats(
            fromTime = data.from_time,
            tillTime = data.till_time,
            campaignId = data.campaign_id,
            teamId = TeamId(id = ta.get.team_id),
            orgId = orgId
          ).map {
            case Left(MultiChannelReportsError.FromTimeGreaterThanTillTimeError(msg)) => Res.BadRequestError(msg)

            case Left(MultiChannelReportsError.RangeExceeded(msg)) => Res.BadRequestError(msg)

            case Right(overall_stats) =>
              Res.Success("Multichannel All Task Stats Found", Json.obj("multichannel_task_stats" -> overall_stats))
          }.recover {
            case e => Res.ServerError(e)
          }

      }
    }
  }

  def getTopContributorStats(v: String, tid: Option[Long]) = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CHANNELS, //Fixme Multichannel
    tidOpt = tid
  )).async(parse.json) { request =>

    val Res = request.Response
    given Logger: SRLogger= request.Logger
    val orgId = OrgId(id = request.loggedinAccount.org.id)
    val ta = request.actingTeamAccount

    if (tid.isEmpty || ta.isEmpty) {
      Future.successful(Res.ForbiddenError(s"You are not authorized"))
    }
    else {

      request.body.validate[TaskStatsInputQuery] match {
        case JsError(e) =>
          Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

        case JsSuccess(data, _) =>

          reportService.getTopContributorStats(
            fromTime = data.from_time,
            tillTime = data.till_time,
            campaignId = data.campaign_id,
            teamId = TeamId(id = ta.get.team_id),
            orgId = orgId
          ).map {
            case Left(MultiChannelReportsError.FromTimeGreaterThanTillTimeError(msg)) => Res.BadRequestError(msg)

            case Left(MultiChannelReportsError.RangeExceeded(msg)) => Res.BadRequestError(msg)

            case Right(top_contributor_stats) =>
              Res.Success("Top contributor stats found", Json.obj("top_contributor_stats" -> top_contributor_stats))

          }.recover {
            case e => Res.ServerError(e)
          }

      }
    }

  }

  def getTimeWiseStatsPerChannelType(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_REPORTS,
    tidOpt = tid
  ).async(parse.json) { request =>

    val Res = request.Response
    given Logger: SRLogger= request.Logger
    val ta = request.actingTeamAccount
    val orgId = OrgId(id = request.loggedinAccount.org.id)

    if (tid.isEmpty || ta.isEmpty) {
      Future.successful(Res.ForbiddenError(s"You are not authorized"))
    }
    else {
      request.body.validate[TaskStatsInputQuery] match {
        case JsError(e) =>
          Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))
        case JsSuccess(data, _) =>


          reportService.getTimeWiseStatsPerChannelType(
            channelType = data.channel_type.get,
            fromTime = data.from_time,
            tillTime = data.till_time,
            campaignId = data.campaign_id,
            teamId = TeamId(id = ta.get.team_id),
            orgId = orgId
          ).map {
            case Left(MultiChannelReportsError.FromTimeGreaterThanTillTimeError(msg)) => Res.BadRequestError(msg)

            case Left(MultiChannelReportsError.RangeExceeded(msg)) => Res.BadRequestError(msg)

            case Right(timewise_stats) =>
              Res.Success(s"Timewise stats found for channelType", Json.obj("timewise_stats" -> timewise_stats))

          }.recover {
            case e => Res.ServerError(e)
          }

      }
    }


  }

  def getActivityOverviewReportStats(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_REPORTS,
    tidOpt = tid
  ).async(parse.json) { request =>

    val Res = request.Response
    given logger: SRLogger= request.Logger
    val ta = request.actingTeamAccount
    val orgId = OrgId(id = request.loggedinAccount.org.id)

    Future {

      if (tid.isEmpty || ta.isEmpty) {
        Res.ForbiddenError(s"You are not authorized")
      }
      else {
        request.body.validate[ActivityOverviewReportFilter] match {
          case JsError(e) =>
            Res.JsValidationError(errors = e, requestBody = Some(request.body))
          case JsSuccess(data, _) =>

            reportService.getActivityOverviewReport(
              team_id = TeamId(id = tid.get),
              filter = data,
              org_id = orgId,
              permitted_account_ids = request.permittedAccountIds
            ) match {

              case Left(ActivityOverviewReportError.ValidationError(err)) =>

                err match {

                  case MultiChannelReportsError.FromTimeGreaterThanTillTimeError(msg) => Res.BadRequestError(msg)

                  case MultiChannelReportsError.RangeExceeded(msg) => Res.BadRequestError(msg)

                }

              case Left(ActivityOverviewReportError.ErrorWhileGettingReport(err)) =>

                logger.fatal(s"Error while getting activity overview report ${err} org_id: ${orgId.id} ")

                Res.ServerError(err)


              case Right(activity_stats) =>
                Res.Success(s"Activity overview report found", Json.obj("activity_stats" -> activity_stats))

            }

        }
      }
    }


  }

  def getLinkedinActivityReport(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_REPORTS,
    tidOpt = tid
  ).async(parse.json) { request =>

    val Res = request.Response
    given logger: SRLogger= request.Logger
    val ta = request.actingTeamAccount
    val orgId = OrgId(id = request.loggedinAccount.org.id)

    Future {

      if (tid.isEmpty || ta.isEmpty) {
        Res.ForbiddenError(s"You are not authorized")
      }
      else {
        request.body.validate[ActivityOverviewReportFilter] match {
          case JsError(e) =>
            Res.JsValidationError(errors = e, requestBody = Some(request.body))
          case JsSuccess(data, _) =>

            reportService.getLinkedinActivityReport(
              team_id = TeamId(id = tid.get),
              filter = data,
              org_id = orgId,
              permittedAccountIds = request.permittedAccountIds
            ) match {

              case Left(ActivityOverviewReportError.ValidationError(err)) =>

                err match {

                  case MultiChannelReportsError.FromTimeGreaterThanTillTimeError(msg) => Res.BadRequestError(msg)

                  case MultiChannelReportsError.RangeExceeded(msg) => Res.BadRequestError(msg)

                }

              case Left(ActivityOverviewReportError.ErrorWhileGettingReport(err)) =>

                logger.fatal(s"Error while getting linkedin activity report ${err} org_id: ${orgId.id} ")

                Res.ServerError(err)


              case Right(linkedin_stats) =>
                Res.Success(s"Linkedin activity report found", Json.obj("linkedin_stats" -> linkedin_stats))

            }

        }
      }
    }


  }


    def findDomainSpamTest(v: ApiVersion, aid: Option[Long], tid: Option[Long],domain: String)=
      permissionUtils.checkPermission(
          permission = PermType.VIEW_REPORTS,
          tidOpt = tid,
          localOrStagingApiTestAccess = true

      ).async { request =>

        val logger = request.Logger
        val res = request.Response
        val accountId = request.loggedinAccount.internal_id

        val result = spamTestService.getSpecificDomainSpamTestReport(domain = domain,teamId = TeamId(tid.get))

        result match {
            case Left(GetSpamTestServiceError.FetchException(e)) =>
                logger.fatal(s"ReportController findDomainSpamTest error while fetching spamtest for tid_${tid} : aid_${accountId}",e)
                Future.successful(res.ServerError(s"Error while fetching Spam Test, ${CONSTANTS.API_MSGS.DEFAULT_ERROR_MESSAGE}",e = Some(e)))
            case Right(testResult) =>
                Future.successful(res.Success(s"Spam Test Report for $domain found ", Json.toJson(testResult)))
        }

    }

    def findDomainBlacklistReport(v: ApiVersion,aid: Option[Long],tid: Option[Long],domain: String) =
        permissionUtils.checkPermission(
            permission = PermType.VIEW_REPORTS,
            tidOpt = tid,
            localOrStagingApiTestAccess = true
        ).async {request =>

            val logger = request.Logger
            val res = request.Response
            val accountId = request.loggedinAccount.internal_id

            val result = domainHealthCheckService.getDomainBlacklistReport(
                domain = domain,
                teamId = TeamId(tid.get)
            )

            result match {
                case Left(GetDomainHealthCheckServiceError.FetchException(e)) =>
                    logger.fatal(s"ReportController findDomainBlacklistReport error while fetching blacklist report for tid_${tid} : aid_${accountId}",e)
                    Future.successful(res.ServerError(s"Error while fetching Blacklist report, ${CONSTANTS.API_MSGS.DEFAULT_ERROR_MESSAGE}",e = Some(e)))

                case Left(GetDomainHealthCheckServiceError.BlacklistReportNotFound) =>
                    logger.error(s"Blacklist Report not found for $domain")
                    Future.successful(res.NotFoundError("Report not found"))

                case Right(blacklistReport) =>
                    Future.successful(res.Success(s"Blacklist Report for $domain found ", Json.toJson(blacklistReport)))


            }
        }

  def findInboxPlacementReport(v: ApiVersion,aid: Option[Long],tid: Option[Long],domain: String) =
    permissionUtils.checkPermission(
      permission = PermType.VIEW_REPORTS,
      tidOpt = tid,
      localOrStagingApiTestAccess = true
    ) {(request: PermissionRequest[AnyContent]) =>

      val logger = request.Logger
      val Res = request.Response
      val accountId = request.loggedinAccount.internal_id

      val result: Try[List[DomainInboxPlacementLog]] = inboxPlacementCheckLogsReportService.getInboxPlacementLogsForDomain(
        domain = domain,
        teamId = TeamId(tid.get)
      )

      result match {
        case Failure(exception) => Res.ServerError(s"Error while fetching inbox placement report, ${CONSTANTS.API_MSGS.DEFAULT_ERROR_MESSAGE}",e = Some(exception))

        case Success(data) =>
          if(data.isEmpty){
            Res.NotFoundError("Report not found")
          } else {
            Res.Success("Report found", Json.toJson(data))
          }
      }
    }
}
