package api.templates

import api.campaigns.services.{CampaignTemplateService, ValidateCampaignTemplateError, ValidateStepData}
import api.accounts.{PermType, PermissionUtils}
import api.campaigns.models.CampaignStepData
import play.api.libs.json._
import play.api.mvc.{BaseController, ControllerComponents}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class TemplateController(
  protected val controllerComponents: ControllerComponents,
  templateUtils: TemplateUtils,
  templateDAO: TemplateDAO,
  permissionUtils: PermissionUtils,
  campaignTemplateService: CampaignTemplateService
) extends BaseController {

  implicit val ec: ExecutionContext = controllerComponents.executionContext

  def findAll(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_TEMPLATES,
    tidOpt = tid
  ).async { request =>

    Future {
      val Logger = request.Logger
      val Res = request.Response


      if (request.actingTeamAccount.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {


        val accountIds = request.permittedAccountIds
        val ta = request.actingTeamAccount.get

        val personalTemplates: Seq[Template] = templateDAO.findAllPersonalOrTeamTemplates(
          accountIds = accountIds,
          teamId = ta.team_id,
          checkIfOwnerAccountId = ta.user_id
        )

        val srLibraryTemplates: Seq[Template] = templateDAO.findSRLibraryTemplates()

        val teamTemplates = personalTemplates.filter(_.shared_with_team).map(_.copy(category = "Team"))


        Res.Success("Templates found", Json.obj(
          "templates" -> personalTemplates, // for legacy
          "personal_templates" -> personalTemplates.sortBy(_.id).reverse,
          "team_templates" -> teamTemplates.sortBy(_.id).reverse,
          "library_templates" -> srLibraryTemplates
        ))

      }

    }
  }


  def duplicate(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_TEMPLATES,
    tidOpt = tid
  ).async(parse.json) { request =>

    Future {
      val Logger = request.Logger
      val Res = request.Response


      if (request.actingTeamAccount.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {
        val ta = request.actingTeamAccount.get

        request.body.validate[TemplateDuplicateForm] match {

          case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

          case JsSuccess(data, _) =>

            templateUtils.duplicateTemplate(
              templateId = data.template_id,
              isFromLibrary = data.template_is_from_library,
              ownerAccountId = ta.user_id,
              ownerTeamId = ta.team_id,
              ownerTaId = ta.ta_id,
              Logger = Logger
            ) match {

              case Failure(e) =>
                Res.ServerError(Option(e.getMessage).getOrElse("Please try again or contact support"), e = Some(e))

              case Success(row) =>
                Res.Success("Template has been saved", Json.obj("template" -> row))

            }

        }


      }
    }

  }


  def create(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_TEMPLATES,
    tidOpt = tid
  ).async(parse.json) { request =>

    Future {
      val Logger = request.Logger
      val Res = request.Response


      if (request.actingTeamAccount.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {
        val ta = request.actingTeamAccount.get


        request.body.validate[TemplateCreateUpdateForm] match {

          case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

          case JsSuccess(data, _) =>

            if (data.subject.isEmpty || data.subject.get.trim.isEmpty) {

              Res.BadRequestError("Please provide a valid subject")

            } else {

              val validateCampaignTemplateData = ValidateStepData.ValidateCampaignTemplateData(
                campaignStepData = CampaignStepData.AutoEmailStep(
                  subject = data.subject.get,
                  body = data.body
                )
              )

              campaignTemplateService.validateCampaignTemplate(
                validateStepData = validateCampaignTemplateData,
                teamId = ta.team_id,
                notes = None, // there are no notes present
              ) match {

                case Left(ValidateCampaignTemplateError.BodyCantHavePreviousSubject(err)) =>

                  Res.BadRequestError(err.getMessage)

                case Left(ValidateCampaignTemplateError.SubjectCantHaveSignature(err)) =>

                  Res.BadRequestError(err.getMessage)

                case Left(ValidateCampaignTemplateError.PreviousSubInFirstEmail(err)) =>

                  Res.BadRequestError(err.getMessage)

                case Left(ValidateCampaignTemplateError.ErrorWhileValidatingSubject(e)) =>

                  Res.BadRequestError(e.getMessage)

                case Left(ValidateCampaignTemplateError.ErrorWhileValidatingBody(e)) =>

                  Res.BadRequestError(e.getMessage)

                case Left(ValidateCampaignTemplateError.NotesCantHavePreviousSubject(err)) =>

                  Res.BadRequestError(err.getMessage)

                case Left(ValidateCampaignTemplateError.ErrorWhileValidatingNotes(err)) =>

                  Res.BadRequestError(err.getMessage)

                case Right(false) =>

                  Res.BadRequestError("please send a valid template")

                case Right(true) =>

                  templateDAO.create(accountId = ta.user_id, teamId = ta.team_id, taId = ta.ta_id, data) match {

                    case Failure(e) =>

                      if (e.getMessage != null && e.getMessage.contains("templates_account_id_label_key")) {

                        Res.BadRequestError(s"You have another template labelled '${data.label}'. Try saving with a different label.")

                      } else {

                        Res.ServerError(s"Error while saving template: $e", e = Some(e))
                      }

                    case Success(None) => Res.ServerError("Error while saving template", e = None)

                    case Success(Some(row)) => Res.Success("Template has been saved", Json.obj("template" -> row))

                  }
              }
            }
        }
      }
    }

  }


  def update(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = (

    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_TEMPLATES,
      tidOpt = tid
    )
    andThen templateUtils.hasTemplate(templateId = id)).async(parse.json) { request =>

    Future {
      val Logger = request.Logger
      val Res = request.Response

      val validateData = request.body.validate[TemplateCreateUpdateForm]

      validateData match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>

          Logger.info(s"""
            Template.update request: loggedinAccount: ${request.loggedinAccount.internal_id} ::
            update_for_all_linked_emails: ${data.update_for_all_linked_emails} :: data: $data
          """)

          if (data.subject.isEmpty || data.subject.get.trim.isEmpty) {

            Res.BadRequestError("Please provide a valid subject")

          } else {

            val validateCampaignTemplateData = ValidateStepData.ValidateCampaignTemplateData(
              campaignStepData = CampaignStepData.AutoEmailStep(
                subject = data.subject.get,
                body = data.body
              )
            )

            campaignTemplateService.validateCampaignTemplate(
              teamId = request.actingTeamAccount.team_id,
              notes = None, // there are no notes present
              validateStepData = validateCampaignTemplateData
            ) match {

              case Left(ValidateCampaignTemplateError.BodyCantHavePreviousSubject(err)) =>

                Res.BadRequestError(err.getMessage)

              case Left(ValidateCampaignTemplateError.SubjectCantHaveSignature(err)) =>

                Res.BadRequestError(err.getMessage)

              case Left(ValidateCampaignTemplateError.PreviousSubInFirstEmail(err)) =>

                Res.BadRequestError(err.getMessage)

              case Left(ValidateCampaignTemplateError.ErrorWhileValidatingBody(e)) =>

                Res.BadRequestError(e.getMessage)
              case Left(ValidateCampaignTemplateError.ErrorWhileValidatingSubject(e)) =>

                Res.BadRequestError(e.getMessage)
              case Left(ValidateCampaignTemplateError.ErrorWhileValidatingNotes(e)) =>

                Res.BadRequestError(e.getMessage)

              case Left(ValidateCampaignTemplateError.NotesCantHavePreviousSubject(e)) =>

                Res.BadRequestError(e.getMessage)
              case Right(false) =>

                Res.BadRequestError("please send a valid template")
              case Right(true) =>

                templateDAO.update(
                  id = id,
                  loggedinAccountId = request.actingTeamAccount.user_id,
                  permittedAccountIds = request.permittedAccountIds,
                  teamId = request.actingTeamAccount.team_id,
                  data = data
                ) match {

                  case Failure(e) =>

                    e.getMessage match {

                      case msg if msg.contains("unique") => Res.BadRequestError("You have another template with the same label. Try saving with a different label.")

                      case msg => Res.ServerError("Error while updating template: " + msg, e = Some(e))
                    }


                  case Success(None) => Res.NotFoundError("Template not found. Could you check and try again ?")

                  case Success(Some(row)) => Res.Success("Template has been updated", Json.obj("template" -> row))

                }
            }
          }

      }
    }

  }



  def shareWithTeam(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = (

    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_TEMPLATES,
      tidOpt = tid
    )
      andThen templateUtils.hasTemplate(templateId = id)).async(parse.json) { request =>

    Future {
      val Logger = request.Logger
      val Res = request.Response

        request.body.validate[TemplateShareForm] match {

          case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

          case JsSuccess(data, _) =>

            val wantsToShare = data.share

            templateDAO.shareWithTeam(
              id = id,
              teamId = request.actingTeamAccount.team_id,
              accountIds = request.permittedAccountIds,
              shared = wantsToShare
            ) match {

              case Failure(e) => Res.ServerError(s"Error while ${if (wantsToShare) "sharing template" else "making template private"}: " + e.getMessage, e = Some(e))

              case Success(None) => Res.NotFoundError("Template not found. Could you check and try again ?")

              case Success(Some(row)) => Res.Success(s"Template has been ${if (wantsToShare) "shared" else "made private"}", Json.obj("template" -> row))

            }

        }
    }

  }


  def delete(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = (

    permissionUtils.checkPermission(
      version = v,
      permission = PermType.DELETE_TEMPLATES,
      tidOpt = tid
    )
      andThen templateUtils.hasTemplate(templateId = id)).async { request =>

    Future {
      val Logger = request.Logger
      val Res = request.Response
      Logger.info(s"DELETE template called by ${request.actingTeamAccount.user_id} :: $id")

      templateDAO.delete(id = id, permittedAccountIds = request.permittedAccountIds) match {

        case Failure(e) => Res.ServerError("Error while deleting template: " + e.getMessage, e = Some(e))

        case Success(_) => Res.Success("Template has been deleted", Json.obj())

      }
    }

  }

  def getStatsTemplateOverall(v: String, template_id: Long, from: Long, till: Long, aid: Option[Long], tid: Option[Long], list_id: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.VIEW_TEMPLATES,
      tidOpt = tid
    )
    andThen templateUtils.hasTemplate(template_id)).async { request =>
    Future {
      val Logger = request.Logger
      val Res = request.Response

      // from and till must be in seconds after epoch

      Res.Success(
        "Template stats found",
        templateDAO.getTemplateStatsOverallOnly(templateId = template_id, from = from / 1000, till = till / 1000, list_id = list_id)
      )

    }

  }

}
