package api.calendar_app

import api.accounts.models.{AccountId, OrgId}
import api.accounts.{PermType, PermissionRequest, PermissionUtils, TeamId}
import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Js<PERSON>umber, JsObject, JsResult, JsString, JsSuccess, JsValue, <PERSON><PERSON>, <PERSON><PERSON>}
import play.api.libs.ws.WSClient
import play.api.mvc.{Action, AnyContent, BaseController, ControllerComponents}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import utils.helpers.LogHelpers
import api.calendar_app.models.{CalendarAccountData, CalendarBookingData, CalendarTeamNameAndTeamId, CalendarUserDetails, SelectedCalendarDataToBeUpdatedInDb}
import api.calendar_app.middleware.CalendarAppAuthMiddleware
import api.campaigns.services.CampaignId
import api.prospects.models.ProspectId
import common_auth_middleware.CommonAuthMiddleware
import eventframework.ProspectObject
import utils.{SRLogger, StringUtils}


class CalendarController(
                          protected val controllerComponents: ControllerComponents,
                          calendarAppService: CalendarAppService,
                          permissionUtils: PermissionUtils,
                          calendarAppAuthMiddleware: CalendarAppAuthMiddleware,
                          commonAuthMiddleware: CommonAuthMiddleware,
                          implicit val wSClient: WSClient
                        ) extends BaseController {

  private implicit val ec: ExecutionContext = controllerComponents.executionContext

  /***
    * Whenever a booking is done by prospect through the meeting link  this api creates an event in activity timeline
    * @param tid
    * @param cid
    * @param sid
    * @param pid
    * @return
    */

    //seq diagram present in seq-diagrams/calendar-app-diag/creation-meeting-booked-event-activitytimeline
  def createCalendarBookingEventForActivityTimeLine(tid:Option[Long],cid:Option[Long],sid:Option[Long],pid:Option[Long]) =
    calendarAppAuthMiddleware.isCalendarApp.async(parse.json){ request =>
      
    given Logger: SRLogger= request.Logger

    val Res = request.Response

    val validate_data = request.body.validate[CalendarBookingData]

    validate_data match {

      case JsError(err) => Future.successful(Res.JsValidationError(err))

      case JsSuccess(data, path) =>
        if(tid.isEmpty || cid.isEmpty || sid.isEmpty || pid.isEmpty) {
          Future.successful(Res.BadRequestError("Error Incorrect Url"))
        }else {
          calendarAppService.createCalendarBookingEventForProspect(calBookData = data, tid = tid.get, cid = cid.get, sid = sid.get, pid = pid.get).map(_ => {
            Res.Success("Created Booking Event Successfully", Json.obj())
          }).recover(e =>
            Res.ServerError(e)
          )
        }

    }
  }


  /**
    *
    *
    * The Function is used to decrypt the calendar link , shared with the prospect to extract stepid ,campaign id , prospect id
    * so that we can prefill all prospect related information directly, while booking a meeting .
    *
    * The call comes from api/auth/decrypt from calendar app backend.
    *
    * @param tid
    * @return
    */


  def decryptCalendarCredential(tid: Option[Long]) =calendarAppAuthMiddleware.isCalendarApp.async(parse.json) { request =>
    given Logger: SRLogger= request.Logger
    val Res = request.Response

    val encryptedValue = (request.body \"encrypt_url").as[String]

    val decryptedValue = calendarAppService.decryptCalendarInfo(encryptedValue = encryptedValue)

    if(!decryptedValue.contains("pid") || !decryptedValue.contains("sid") || !decryptedValue.contains("cid")){
     Future.successful( Res.BadRequestError("Request does not contain required details"))
    }

    else{
      Future.successful(Res.Success("Successfully fetched required info", Json.obj("decrypt_value" -> decryptedValue)))
    }
  }

  /**
    *
    * The below function is used to get Prospect Details based on tid and pid so that the details are prefilled for the
    * prospect in the invitee questions when the prospect is booking a meeting.
    * @param tid
    * @param pid
    * @return
    */
  def getProspectObjForCalendar(tid:Option[Long],pid:Option[Long]) = calendarAppAuthMiddleware.isCalendarApp.async{ request =>
    given Logger: SRLogger= request.Logger
    val Res = request.Response

    if(tid.isEmpty){
      Future.successful(Res.BadRequestError("Missing tid in request"))
    }else if(pid.isEmpty){
      Future.successful(Res.BadRequestError("Missing pid in request"))
    }
    else {

      Future {

        calendarAppService.getProspectDetailsForCalendar(
          prospectId = ProspectId(id = pid.get),
          teamId = TeamId(id = tid.get),
          Logger = Logger
        ) match {

          case Failure(e) =>
            Res.ServerError(e)

          case Success(prospects) =>

            if (prospects.isEmpty) {

              Res.ForbiddenError("Prospect either doesn't exist, or you do not have access to this prospect")

            } else {
              val prospect = prospects.headOption.get


              Res.Success("Prospect found", Json.obj(
                "prospect" -> ProspectObject.apiStructure(
                  prospect = prospect
                )
              ))
            }

        }
      }
    }

  }


  /**
    *
    * The use of the below controller function  is to get all the data of teams which the logged in user is part of in calendar app.
    * The data is used to show the list of teams available to the user if the user is trying to use a team level link and also ,
    * the associated event-types made on behalf of that team
    *
    **/


  def getAllEventTypesForCalendarTeam(tid: Option[Long]) = permissionUtils.checkPermission(
    permission = PermType.EDIT_CHANNELS, // FIXME CALENDAR: Create New PermType For Calendar
    tidOpt = tid
  ).async { (request: PermissionRequest[AnyContent]) =>
    given Logger: SRLogger= request.Logger
    val Res = request.Response


    val teamId = TeamId(id = tid.get)
    val orgId = OrgId(id = request.loggedinAccount.org.id)
    calendarAppService
      .getAllTeamEventTypeOptions(teamId = teamId,orgId = orgId)
      .map(calendarTeamData => {

        Logger.debug(s"Success result is ${calendarTeamData.toString}")

        Res.Success("Successfully Fetched all teams  data for user from calendar", data = Json.toJson(calendarTeamData))

      }).recover { case e =>

        Logger.fatal(s"Error while fetching all teams data  from calendar  : ${LogHelpers.getStackTraceAsString(e)}")
        Res.ServerError(err = e)
    }
  }


  /**
   *
   * The use of the below controller function is to get all the data of teams which the logged in user is part of in calendar app.
   * The data is used to show the list of  individual eventTypes available to the user.
   *
   * */


  def getAllIndividualEventTypeOptions(tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    permission = PermType.EDIT_CHANNELS, // FIXME CALENDAR: Create New PermType For Calendar
    tidOpt = tid
  ).async(parse.json) { (request: PermissionRequest[JsValue]) =>
    given Logger: SRLogger = request.Logger
    val Res = request.Response

    request.body.validate[CalendarAndAccountId] match {
      case JsError(exception) =>
        Logger.fatal(s"Validation Error Occurred :: ${exception.toString}")
        Future.successful(Res.JsValidationError(exception))
      case JsSuccess(value, path) => {

        calendarAppService
          .getAllIndividualEventType(
            smartreachAccountId = value.smartreach_account_id,
            calendar_data = value.calendar_data
          )
          .map(eventTypes => {

            Logger.debug(s"Success result is ${eventTypes.toString}")

            Res.Success("Successfully Fetched all event types for user from calendar", data = Json.toJson(eventTypes))

          }).recover { case e =>

            Logger.fatal(s"error while fetching all event types  from calendar  : ${LogHelpers.getStackTraceAsString(e)}")

            Res.ServerError(err = e)

          }
      }
    }
  }


  def updateCalendarUserDataInAccounts() = commonAuthMiddleware.isUsingCommonAuth.async(parse.json) { request =>
    val logRequestId = s"${StringUtils.genLogTraceId} CommonAuthController.updateCalendarUserDataInAccounts: "
    given Logger: SRLogger= new SRLogger(logRequestId = logRequestId)
    val Res = request.Response


    request.body.validate[CalendarUserDetails] match {
      case JsError(e) => Logger.fatal(s"Validation error occurred:: ${e.toString()}")
        Future.successful(Res.JsValidationError(e))

      case JsSuccess(value, path) =>

        Future.fromTry(calendarAppService.updateCalendarUserData(
          orgId = OrgId(id = value.smartreach_org_id),
          accountId = AccountId(id = value.smartreach_acc_id),
          calendarUserId = CalendarUserId(id = value.calendar_user_id),
          calendarUsernameSlug = value.calendar_user_name,
        ).map(_ => {
          Res.Success("Successfully Updated Calendar Data")
        }).recover { case e =>
          Logger.fatal(s"Error occurred while updating Calendar Data :: ${e.getMessage}")
          Res.BadRequestError(s"Some Error Occurred :: ${e.getMessage}")
        })

    }
  }




  def updateCalendarTeamDataForSmartreachTeam = commonAuthMiddleware.isUsingCommonAuth.async(parse.json) { request =>
    val logRequestId = s"${StringUtils.genLogTraceId} CommonAuthController.updateCalendarTeamDataForSmartreachTeam: "
    given Logger: SRLogger= new SRLogger(logRequestId = logRequestId)
    val Res = request.Response


    request.body.validate[CalendarTeamNameAndTeamId] match {

      case JsError(errors) => {
        Logger.fatal(s"JsValidation Error Occurred :: ${errors.toString()}")
        Future.successful(Res.JsValidationError(errors = errors))
      }

      case JsSuccess(value, path) => {
        Future.fromTry(
          calendarAppService
            .updateCalendarTeamData(
              accountId = AccountId(id = request.accountId),
              orgId = value.smartreach_org_id,
              teamId = value.smartreach_team_id,
              calendarTeamName = value.calendar_team_name,
              calendarTeamId = value.calendar_team_id
            )
        ).map(res => {
          Res.Success("Updated Calendar Team Data Successfully")
        }).recover{case e => {
          Res.ServerError(err = e)
        }}
      }
    }

  }


  def updateCalendarDataInDB(tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    permission = PermType.EDIT_CHANNELS, // FIXME CALENDAR: Create New PermType For Calendar
    tidOpt = tid
  ).async(parse.json) { (request: PermissionRequest[JsValue]) =>
    val logRequestId = s"${StringUtils.genLogTraceId} CommonAuthController.updateCalendarDataInDB: "
    given Logger: SRLogger = new SRLogger(logRequestId = logRequestId)
    val Res = request.Response

    request.

      request.body.validate[SelectedCalendarDataToBeUpdatedInDb] match {
      case JsError(errors) => {
        Logger.fatal(s"JsValidation Error Occurred :: ${errors.toString()}")
        Future.successful(Res.JsValidationError(errors = errors))
      }

      case JsSuccess(value, path) =>


        val teamId = TeamId(id = request.actingTeamAccount.get.team_id)
        val orgId = OrgId(id = request.loggedinAccount.org.id)

        Future.fromTry {
          calendarAppService
            .updateSelectedCalendarDataInDB(
              selectedCalendarData = value.selected_calendar_data,
              teamId = teamId,
              orgId = orgId,
              campaignId = value.campaign_id
            )
        }.map(res => {
          Res.Success("Updated Selected Calendar Data");
        }).recover { case e =>
          Logger.fatal("An Error Occurred while updating calendar Data in Db ")
          Res.ServerError(e)
        }

    }
  }


  def getSelectedCalendarDataFromDB(tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    permission = PermType.EDIT_CHANNELS, // FIXME CALENDAR: Create New PermType For Calendar
    tidOpt = tid
  ).async(parse.json) { (request: PermissionRequest[JsValue]) =>
    val logRequestId = s"${StringUtils.genLogTraceId} CommonAuthController.getSelectedCalendarDataFromDB: "
    given Logger: SRLogger = new SRLogger(logRequestId = logRequestId)
    val Res = request.Response


    Try {
      (request.body \ "campaign_id").asOpt[CampaignId]
    } match {
      case Failure(error) => {
        Logger.fatal(s"Error Occurred :: ${error.toString()}")
        Future.successful(Res.ServerError(error))
      }

      case Success(value) =>


        val teamId = TeamId(id = request.actingTeamAccount.get.team_id)
        val orgId = OrgId(id = request.loggedinAccount.org.id)
        val campaignId = value

        Future {
          calendarAppService
            .getSelectedCalendarDataFromDB(
              teamId = teamId,
              orgId = orgId,
              campaignId = campaignId
            )
        }.map {

          case Left(GetSelectedCalendarDataFromDbError.CalendarSettingsForTeamsNotConfigured(teamId)) => {
            Logger.warn(s"Calendar Settings not configured for team : ${teamId.id}")
            Res.NotFoundError("Please configure calendar settings  either for this team or for this particular campaign")
          }

          case Left(GetSelectedCalendarDataFromDbError.FromCampaignsDbFailure(err)) => {
            Logger.fatal("Error occurred while fetching details from db:: table campaigns ", err)
            Res.ServerError(err)
          }
          case Left(GetSelectedCalendarDataFromDbError.FromTeamsDbFailure(err)) => {
            Logger.fatal("Error occurred while fetching details from db:: table teams ", err)
            Res.ServerError(err)
          }

          case Right(selectedCalendarData) =>
            Res.Success("Successfully fetched Selected Data from Db", Json.toJson(selectedCalendarData));
        } recover { case e =>
          Logger.fatal("An Error Occurred while updating calendar Data in Db ")
          Res.ServerError(e)
        }

    }
  }


}
