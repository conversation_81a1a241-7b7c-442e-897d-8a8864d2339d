package api.calendar_app.models
import api.accounts.models.AccountId
import api.calendar_app.CalendarUserId
import play.api.libs.json.{Format, JsError, JsResult, JsSuccess, JsValue, <PERSON>son, Reads, Writes}

import scala.util.{Failure, Success, Try}


case class IndividualCalendarDetails(
                                      calendar_user_id: CalendarUserId,
                                      calendar_username_slug:String,
                                      smartreach_account_id: AccountId,
                                      calendar_event_types: List[CalendarEventType]
                                    )

object IndividualCalendarDetails{
  implicit val writes: Writes[IndividualCalendarDetails] = Json.writes[IndividualCalendarDetails]
  implicit val reads: Reads[IndividualCalendarDetails] = Json.reads[IndividualCalendarDetails]
}
