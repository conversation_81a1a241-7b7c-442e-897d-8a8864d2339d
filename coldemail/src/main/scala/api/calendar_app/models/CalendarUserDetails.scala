package api.calendar_app.models

import play.api.libs.json.{<PERSON><PERSON>, OFormat}

case class CalendarUserDetails(

                                smartreach_org_id: Long, // FIXME: CHANGE TO VALUE CLASS
                                smartreach_acc_id: Long,
                                calendar_user_id: Int, //because of js limitation
                                calendar_user_name: String,
                              )

object CalendarUserDetails {
  given format: OFormat[CalendarUserDetails] = Json.format[CalendarUserDetails]
}