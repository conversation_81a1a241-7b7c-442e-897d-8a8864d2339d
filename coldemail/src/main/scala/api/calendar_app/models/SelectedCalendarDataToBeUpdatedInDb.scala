package api.calendar_app.models

import api.campaigns.services.CampaignId
import sr_scheduler.models.SelectedCalendarData
import play.api.libs.json.{Json, Reads}

case class SelectedCalendarDataToBeUpdatedInDb(
                                                selected_calendar_data: SelectedCalendarData,
                                                campaign_id: Option[CampaignId]
                                              )

object SelectedCalendarDataToBeUpdatedInDb {
  implicit val reads: Reads[SelectedCalendarDataToBeUpdatedInDb] = Json.reads[SelectedCalendarDataToBeUpdatedInDb]
}
