package api.sr_audit_logs.dao_service

import api.sr_audit_logs.dao.{CRMSettingsRateLimitJedisDAO, WorkflowAttemptRateLimitSetJedisDAO}
import utils.cache_utils.{SrRedisHashSetBasedLockService, SrRedisHashSetServiceTrait, SrRedisSimpleLockServiceTrait}

class WorkflowRateLimitJedisDAOService(
                                        crmSettingsRateLimitJedisDAO: CRMSettingsRateLimitJedisDAO,
                                        workflowAttemptRateLimitSetJedisDAO: WorkflowAttemptRateLimitSetJedisDAO
                                      ) extends SrRedisHashSetBasedLockService {

  override protected val simpleLockService: SrRedisSimpleLockServiceTrait[String] = crmSettingsRateLimitJedisDAO

  override protected val hashSetLockService: SrRedisHashSetServiceTrait[String] = workflowAttemptRateLimitSetJedisDAO


}