package api.sr_audit_logs.dao

import api.sr_audit_logs.models.{APIRequestMethod, RequestLog}
import scalikejdbc._

import scala.util.Try

class RequestLogDAO extends RequestLogDAOTrait {

  def insertRequestLogInDB(
                            data: RequestLog
                          ): Try[Option[String]] = Try {
    DB autoCommit { implicit session =>

      val requestBodySql = if(data.request_body.isEmpty){
        sqls""" '{}'::jsonb"""
      }else{
        sqls"""to_json (${data.request_body}::jsonb)"""
      }

      val responseBody = if(data.response_body.isEmpty){
        sqls""" '{}'::jsonb"""
      }else{
        sqls"""to_json (${data.response_body}::jsonb)"""
      }

      sql"""
          INSERT INTO audit_request_logs
          (
           audit_request_log_id,
           team_id,
           account_id,
           actor_name,
           request_end_point,
           request_body,
           request_origin,
           request_method,
           request_ip,
           request_version,
           response_body,
           response_code,
           created_at
          )
           VALUES
           (
            ${data.audit_request_log_id},
            ${data.team_id},
            ${data.account_id},
            ${data.actor_name},
            ${data.request_end_point},
            $requestBodySql,
            ${data.request_origin},
            ${data.request_method.toString},
            ${data.request_ip},
            ${data.request_version},
            $responseBody,
            ${data.response_http_status},
            ${data.created_at}
           )
          RETURNING audit_request_log_id;
      """
        .map(rs => rs.string("audit_request_log_id"))
        .single
        .apply()
    }
  }

  def findRequestLogs(
                       teamId: Long,
                       accountId: Long,
                       audit_request_log_id: Option[String],
                       created_greater_than: Option[Long],
                       created_less_than: Option[Long],
                       method: Option[APIRequestMethod],
                       is_success: Option[Boolean],
                       page: Int
                     ): Try[Seq[RequestLog]] = {
    Try {
      Seq()
    }
  }

}
