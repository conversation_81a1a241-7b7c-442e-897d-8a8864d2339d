package api.sr_audit_logs.dao

import api.CacheServiceJedis
import utils.cache_utils.{SimpleCacheServiceTrait, SrRedisKeyNamespace, SrRedisKeyRegister, SrRedisSimpleLockServiceTrait}

class CRMSettingsForWorkFlowAttemptJedisDAO(
                                             protected val cacheServiceJedis: CacheServiceJedis
                                           ) extends SrRedisSimpleLockServiceTrait[String] {


  override protected val redisKeyNamespace: SrRedisKeyNamespace = SrRedisKeyRegister.workflowCRMSettingsForWorkFlowAttemptNamespace

  override def getCacheId(data: String): String = data

  override val lockDefaultExpiresInSeconds: Int = 900

}
