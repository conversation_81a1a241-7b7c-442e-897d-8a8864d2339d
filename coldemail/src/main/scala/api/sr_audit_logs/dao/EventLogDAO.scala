package api.sr_audit_logs.dao

import api.accounts.TeamId
import api.sr_audit_logs.models.{EventLog, EventType}
import play.api.libs.json.Json
import utils.SRLogger
import scalikejdbc._
import utils.dbutils.DBUtils

import scala.util.Try

class EventLogDAO extends EventLogDAOTrait {

  def insertEventLogInDB(
                          data: EventLog
                        ): Try[Option[String]] = Try {

    DB autoCommit { implicit session =>

      sql"""
          INSERT INTO audit_event_logs
          (
           event_log_id,
           audit_request_log_id,
           event_type,
           event_object_type,
           event_data,
           team_id,
           account_id,
           created_at
          )
           VALUES
           (
            ${data.event_log_id},
            ${data.audit_request_log_id},
            ${data.event_data_type.eventType.toString},
            ${data.event_data_type.eventObjectType.toString},
            to_json (${Json.toJson(data.event_data_type).toString()}::jsonb),
            ${data.team_id},
            ${data.account_id},
            ${data.created_at}
           )
          RETURNING event_log_id;
      """
        .map(rs => rs.string("event_log_id"))
        .single
        .apply()
    }
  }

  def insertEventLogBatchInDB(
                               data: Seq[EventLog]
                             ): Try[Seq[String]] = Try {

    if(data.isEmpty) {
      Seq()
    } else {

      var valueParameters = List[Any]()

      val valuePlaceholder = data
        .map(data => {

          valueParameters = valueParameters ::: List(
            data.event_log_id,
            data.audit_request_log_id,

            data.event_data_type.eventType.toString,
            data.event_data_type.eventObjectType.toString,
            Json.toJson(data.event_data_type).toString(),

            data.team_id,
            data.account_id,
            data.created_at
          )

          sqls"""
                   (
                   ?,
                   ?,

                   ?,
                   ?,
                   to_json(?::jsonb),

                   ?,
                   ?,
                   ?

                   )
                   """
        }).reduce((vp1, vp2) => sqls"$vp1, $vp2")


      DB autoCommit { implicit session =>

        val qry = sql"""
            INSERT INTO audit_event_logs
            (
             event_log_id,
             audit_request_log_id,

             event_type,
             event_object_type,
             event_data,

             team_id,
             account_id,
             created_at
            )
             VALUES $valuePlaceholder

            RETURNING event_log_id;
        """
          qry
          .bind(valueParameters*)
          .map(rs => rs.string("event_log_id"))
          .list
          .apply()
      }

    }

  }


  def findEventLogs(
                     teamId: Long,
                     accountId: Long,
                     audit_request_log_id: Option[String],
                     event_log_id: Option[String],
                     created_greater_than: Option[Long],
                     event_type: Option[EventType],
                     page: Int
                   ): Try[Seq[EventLog]] = {
    Try {
      Seq()
    }
  }


  private def __findEventLogsSQL(whereClause: SQLSyntax):SQL[?, ?] = {

    sql"""
        SELECT * FROM audit_event_logs e WHERE $whereClause
    """
  }


  def findEventLogByTeamIdAndEventLogId(
                                         teamId: Long,
                                         event_log_id: String,
                                         logger: SRLogger
                                       ): Try[Option[EventLog]] = {

    DBUtils.readOnly(
      logger = logger.appendLogRequestId("EventLogDAO.findEventLogByTeamIdAndEventLogId")
    ) { implicit session =>

      __findEventLogsSQL(whereClause =
        sqls"""
          e.event_log_id = $event_log_id and
          e.team_id = $teamId
        """)
        .map(rs => EventLog.fromDb(rs = rs).get)
        .single
        .apply()
    }
  }


  def updatePushedForAttemptCreationAt(
                                      teamId: TeamId,
                                      event_log_id: String
                                      ): Try[String] = Try {

    DB autoCommit  { implicit session =>
      sql"""
           UPDATE audit_event_logs
           SET pushed_for_attempt_creation_at = now()
           WHERE
           team_id = ${teamId.id}
           AND
           event_log_id = ${event_log_id}
           RETURNING event_log_id;
        """
        .map(rs => rs.string("event_log_id"))
        .single
        .apply()
        .get

    }

  }

}
