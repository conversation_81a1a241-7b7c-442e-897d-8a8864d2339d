package api.sr_audit_logs.dao

import api.sr_audit_logs.models.{AttemptLog, AttemptLogId, AttemptLogsFoundForTryOrRetry, AttemptSettingData, AttemptStatus, AttemptTriesLog, AttemptTriesLogId, AttemptTryErrorReason, AttemptType, EventDataType, EventType, UpdateAttemptLogStatusError, WorkflowSettingData}
import api.accounts.TeamId
import api.accounts.models.AccountId
import play.api.libs.json.JsValue
import scalikejdbc.{DB, DBSession}
import utils.SRLogger
import utils.dbutils.DBUtils

import scala.util.Try

trait AttemptDAOTrait[T <: AttemptSettingData, E <: AttemptTryErrorReason] {

  def insertAttemptLogInDB(
                            data: AttemptLog[T]
                          ): Try[Option[AttemptLogId]]

  def findAttemptLogs(
                       teamId: TeamId,
                       accountId: AccountId,
                       attempt_log_id: Option[AttemptLogId],
                       event_log_id: Option[String],
                       created_greater_than: Option[Long],
                       created_less_than: Option[Long],
                       attempt_type: Option[AttemptType],
                       page: Int
                     ): Try[Seq[AttemptLog[T]]]


  def insertAttemptTriesLogInDB(
                                 data: AttemptTriesLog[E]
                               )(implicit session: DBSession): Try[Option[AttemptTriesLogId]]


  def findAttemptTriesLogs(
                            teamId: TeamId,
                            accountId: AccountId,
                            attempt_log_id: AttemptLogId,
                            isSuccess: Option[Boolean]
                          )(using Logger: SRLogger): Try[Seq[AttemptTriesLog[E]]]

  def findSingleAttemptLogBasedOnSyncDirectionAndWorkflowCRMSetting(
                                                     ignoreTheseWorkflowIds: Set[String],
                                                     workflowCrmSettingId: Long,
                                                     teamId:TeamId,
                                                     logger: SRLogger,
                                                     crmToSR: Boolean
                                                   ): Try[Option[AttemptLogsFoundForTryOrRetry[T]]]

  def getBatchAttemptLogsForQueuing(
                                     workflowSettingId: Long,
                                     teamId: TeamId,
                                     count: Int,
                                     eventType: EventType,
                                     logger: SRLogger
                                   ): Try[Seq[AttemptLogsFoundForTryOrRetry[T]]]

  def findEventDataTypeForWorkflowCRM(
                                       attempts: AttemptLogsFoundForTryOrRetry[WorkflowSettingData],
                                       logger: SRLogger
                                     ): Try[List[EventAndAttemptDataForProcessing]]

  def addAttemptLogsToQueuing(
                               attempt_log_ids: Seq[AttemptLogId]
                             ): Try[Seq[AttemptLogId]]

  def removeAttemptLogsFromQueuing(
                                    attempt_log_ids: Seq[AttemptLogId],
                                    teamId: TeamId
                                  ): Try[Seq[AttemptLogId]]

  def updateAttemptLogStatus(
                              attempt_log_id: AttemptLogId,
                              teamId: TeamId,
                              attempt_status: AttemptStatus,
                              last_attempt_tries_log_id: AttemptTriesLogId,
                              attempt_retry_count: Int,
                              Logger: SRLogger
                            )(implicit session: DBSession): Try[Option[AttemptLogId]]

}
