package api.sr_audit_logs.models
import scala.util.{Failure, Try, Success}

sealed trait AttemptStatus

object AttemptStatus {

  private val YET_TO_ATTEMPT = "yet_to_attempt"
  private val FAILED_ATTEMPT = "failed_attempt"
  private val SUCCESS_ATTEMPT = "success_attempt"
  private val STARTED_ATTEMPT = "started_attempt"
  private val IGNORED_ATTEMPT = "ignored_attempt"

  case object YetToAttempt extends AttemptStatus {
    override def toString: String = YET_TO_ATTEMPT
  }

  case object FailedAttempt extends AttemptStatus {
    override def toString: String = FAILED_ATTEMPT
  }

  case object SuccessAttempt extends AttemptStatus {
    override def toString: String = SUCCESS_ATTEMPT
  }

  case object StartedAttempt extends AttemptStatus {
    override def toString: String = STARTED_ATTEMPT
  }

  case object IgnoredAttempt extends AttemptStatus {
    override def toString: String = IGNORED_ATTEMPT
  }

  def getIsSuccessForTriesLog(attemptStatus: AttemptStatus): Try[Boolean] = {
    attemptStatus match {
      case YetToAttempt =>  Success(false)
      case StartedAttempt =>  Failure(new Exception("Wrong Status Sent"))
      case FailedAttempt => Success(false)
      case SuccessAttempt => Success(true)
      case IgnoredAttempt => Success(false)
    }
  }

}
