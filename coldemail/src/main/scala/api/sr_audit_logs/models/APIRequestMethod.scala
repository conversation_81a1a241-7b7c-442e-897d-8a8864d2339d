package api.sr_audit_logs.models

import utils.enum_sr_utils.SREnumJsonUtils

import scala.util.Try

sealed abstract class APIRequestMethod {
  protected val key: String

  override def toString: String = key
}

object APIRequestMethod
  extends SREnumJsonUtils[APIRequestMethod] {

  override protected val enumName: String = "APIRequestMethod"

  private val KEY_GET = "GET"
  private val KEY_POST = "POST"
  private val KEY_PUT = "PUT"
  private val KEY_DELETE = "DELETE"
  private val KEY_PATCH = "PATCH"

  case object GET extends APIRequestMethod {

    override val key: String = KEY_GET

  }

  case object POST extends APIRequestMethod {

    override val key: String = KEY_POST

  }

  case object PUT extends APIRequestMethod {

    override val key: String = KEY_PUT

  }

  case object DELETE extends APIRequestMethod {

    override val key: String = KEY_DELETE

  }

  case object PATCH extends APIRequestMethod {

    override val key: String = KEY_PATCH

  }


  override def toKey(value: APIRequestMethod): String = value.key

  override def fromKey(key: String): Try[APIRequestMethod] = Try {

    key match {

      case KEY_GET => GET

      case KEY_POST => POST

      case KEY_PUT => PUT

      case KEY_DELETE => DELETE

      case KEY_PATCH => PATCH

    }

  }
}