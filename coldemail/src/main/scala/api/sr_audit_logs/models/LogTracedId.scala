package api.sr_audit_logs.models

import play.api.libs.json._

case class LogTracedId(id: String) extends AnyVal {

  override def toString: String = id

}

object LogTracedId {

  implicit val reads: Reads[LogTracedId] = new Reads[LogTracedId] {
    override def reads(ev: JsValue): JsResult[LogTracedId] = {
      ev match {
        case JsString(id) => JsSuccess(LogTracedId(id))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[LogTracedId] = new Writes[LogTracedId] {
    override def writes(o: LogTracedId): JsValue = JsString(o.id)
  }
}