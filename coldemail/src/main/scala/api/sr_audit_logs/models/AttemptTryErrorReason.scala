package api.sr_audit_logs.models

import org.joda.time.DateTime
import play.api.libs.json.{JsObject, JsValue, Json}
import play.api.libs.json.JodaReads._
import play.api.libs.json.JodaWrites._
import api.integrations.BatchContactResponse
import utils.SRLogger
import utils.mq.webhook.LeadStatusOrderingError

sealed trait AttemptTryErrorReason {
  val attemptErrorReasonType: AttemptTryErrorReasonType
  val message: String
  val failure_additional_data: Option[JsValue] = None
}

sealed trait WorkflowAttemptTryErrorReason extends AttemptTryErrorReason {
  val pause_crm: Boolean
}

object WorkflowAttemptTryErrorReason {

  /**OAuthError will trigger form OAuthErrorTypes like InvalidGrantError while creating access_tokens */
  case class OAuthError(message: String) extends WorkflowAttemptTryErrorReason {
    override val attemptErrorReasonType: AttemptTryErrorReasonType = AttemptTryErrorReasonType.OAUTH_ERROR
    override val pause_crm: Boolean = true

  }

  case class SRInternalServerError(err: WorkflowAttemptInternalServerError) extends WorkflowAttemptTryErrorReason{
    override val attemptErrorReasonType: AttemptTryErrorReasonType = AttemptTryErrorReasonType.INTERNAL_SERVER_ERROR
    override val message = err.message
    override val pause_crm: Boolean = err.pause_crm

  }
  case class SettingsNotEnabled(msg: String) extends WorkflowAttemptTryErrorReason{
    override val attemptErrorReasonType: AttemptTryErrorReasonType = AttemptTryErrorReasonType.INTERNAL_SERVER_ERROR
    override val message = msg
    override val pause_crm: Boolean = false

  }
  case class ContactCreationDelayInCRM(msg: String) extends WorkflowAttemptTryErrorReason{
    override val attemptErrorReasonType: AttemptTryErrorReasonType = AttemptTryErrorReasonType.INTERNAL_SERVER_ERROR
    override val message = msg
    override val pause_crm: Boolean = false

  }

  case class CRMInternalServerError(message: String) extends WorkflowAttemptTryErrorReason {
    override val attemptErrorReasonType: AttemptTryErrorReasonType = AttemptTryErrorReasonType.CRM_INTERNAL_SERVER_ERROR
    override val pause_crm: Boolean = false
  }

  case class TooManyRequestsError(message: String, nextAttemptAt: Option[WorkflowAttemptAdditionalData.TooManyRequestsAdditionalData] = None) extends WorkflowAttemptTryErrorReason {
    override val attemptErrorReasonType: AttemptTryErrorReasonType = AttemptTryErrorReasonType.TOO_MANY_REQUESTS_ERROR
    override val failure_additional_data: Option[JsValue] = nextAttemptAt.map(at => Json.obj("nextAttemptAt" -> at.nextAttemptAt))
    override val pause_crm: Boolean = false
  }

  /**UnAuthorizedError will trigger form CommonCRMAPIErrors*/
  case class UnAuthorizedError(message: String) extends WorkflowAttemptTryErrorReason {
    override val attemptErrorReasonType: AttemptTryErrorReasonType = AttemptTryErrorReasonType.UNAUTHORIZED_ERROR
    override val pause_crm: Boolean = false
  }

  case class UnKnownError(message: String) extends WorkflowAttemptTryErrorReason {
    override val attemptErrorReasonType: AttemptTryErrorReasonType = AttemptTryErrorReasonType.UNKNOWN_ERROR
    override val pause_crm: Boolean = false
  }


  case class MalformedResponseStructureError(message: String) extends WorkflowAttemptTryErrorReason {
    override val attemptErrorReasonType: AttemptTryErrorReasonType = AttemptTryErrorReasonType.MALFORMED_RESPONSE_STRUCTURE_ERROR
    override val pause_crm: Boolean = false
  }

  case class UnableToFindPersonIdError(message: String) extends WorkflowAttemptTryErrorReason { //todo make WorkflowAttemptInternalServerError.NotFoundError and create a sub error type
    override val attemptErrorReasonType: AttemptTryErrorReasonType = AttemptTryErrorReasonType.UNABLE_TO_FIND_PERSON_ID_ERROR
    override val pause_crm: Boolean = false
  }

  case class InvalidRefreshTokenError(message: String) extends WorkflowAttemptTryErrorReason { //todo move to OAuthError
    override val attemptErrorReasonType: AttemptTryErrorReasonType = AttemptTryErrorReasonType.INVALID_REFRESH_TOKEN_ERROR
    override val pause_crm: Boolean = false

  }

  def fromKey(message: String, attemptTryErrorReasonType: AttemptTryErrorReasonType, additionalData: Option[JsValue])(using Logger: SRLogger): WorkflowAttemptTryErrorReason = {
    attemptTryErrorReasonType match {
      case AttemptTryErrorReasonType.OAUTH_ERROR => WorkflowAttemptTryErrorReason.OAuthError(message = message)
      case AttemptTryErrorReasonType.INTERNAL_SERVER_ERROR => WorkflowAttemptTryErrorReason.SRInternalServerError(err = WorkflowAttemptInternalServerError.getFromMessage(message))
      case AttemptTryErrorReasonType.TOO_MANY_REQUESTS_ERROR =>

        val nextAttemptAtOpt = additionalData.flatMap(failure_additional_data =>
          WorkflowAttemptAdditionalData.getFromJsValue(
            failure_additional_data = failure_additional_data,
            attemptTryErrorReasonType = attemptTryErrorReasonType
          )
        )

        val nextAttemptAt: Option[WorkflowAttemptAdditionalData.TooManyRequestsAdditionalData] = nextAttemptAtOpt match {
          case Some(value) => value match {
            case data: WorkflowAttemptAdditionalData.TooManyRequestsAdditionalData => Some(data)
          }

          case None => None
        }
        WorkflowAttemptTryErrorReason.TooManyRequestsError(
          message = message,
          nextAttemptAt = nextAttemptAt
        )
      case AttemptTryErrorReasonType.UNAUTHORIZED_ERROR => WorkflowAttemptTryErrorReason.UnAuthorizedError(message = message)
      case AttemptTryErrorReasonType.UNKNOWN_ERROR => WorkflowAttemptTryErrorReason.UnKnownError(message = message)
      case AttemptTryErrorReasonType.MALFORMED_RESPONSE_STRUCTURE_ERROR => WorkflowAttemptTryErrorReason.MalformedResponseStructureError(message = message)
      case AttemptTryErrorReasonType.UNABLE_TO_FIND_PERSON_ID_ERROR => WorkflowAttemptTryErrorReason.UnableToFindPersonIdError(message = message)
      case AttemptTryErrorReasonType.INVALID_REFRESH_TOKEN_ERROR => WorkflowAttemptTryErrorReason.InvalidRefreshTokenError(message = message)
      case AttemptTryErrorReasonType.CRM_INTERNAL_SERVER_ERROR => WorkflowAttemptTryErrorReason.CRMInternalServerError(message = message)
    }
  }

}

sealed trait WorkflowAttemptInternalServerError{
  val message: String
  val pause_crm: Boolean
}

object WorkflowAttemptInternalServerError {

  private val sqlExceptionKey = "SqlException"
  private val tokensNotFoundInDBErrorKey = "TokensNotFoundInDBError"
  private val invalidModuleErrorKey = "InvalidModuleError"
  private val wrongProcessTypeKey = "wrongProcessTypeKey"
  private val invalidEventErrorKey = "InvalidEventError"
  private val hubspotFailureBatchErrorKey = "HubspotFailureBatchError"
  private val salesforceFailureBatchErrorKey = "SalesForceFailureBatchError"

  private val integrationNotFoundKey = "IntegrationNotFound"
  private val futureExceptionKey = "FutureException"
  private val prospectIdEmptyKey = "ProspectIdEmpty"
  private val campaignIdEmptyKey = "CampaignIdEmpty"
  private val updateSentNoneKey = "UpdateSentNone"
  private val fieldMappingNotFoundKey = "FieldMappingNotFound"
  private val triggerNotFoundKey = "TriggerNotFound"
  private val settingNotEnabledKey = "SettingNotEnabled"
  private val unableToLockRowKey = "UnableToLockRow"
  private val errorLeadStatusOrderingKey = "ErrorLeadStatusOrdering"


  case class ErrorLeadStatusOrdering(err: LeadStatusOrderingError) extends WorkflowAttemptInternalServerError {
    override val message = s"$errorLeadStatusOrderingKey ${err.message}"
    override val pause_crm: Boolean = false

  }
  case class SqlException(err: Throwable) extends WorkflowAttemptInternalServerError{
    override val message = s"$sqlExceptionKey ${Option(err.getMessage).getOrElse("No message")}"
    override val pause_crm: Boolean = false
  }

  case class WrongProcessType(msg: String) extends WorkflowAttemptInternalServerError {
    override val message: String = s"$wrongProcessTypeKey $msg"
    override val pause_crm: Boolean = false
  }

  case class FutureException(err: Throwable) extends WorkflowAttemptInternalServerError {
    override val message = s"$futureExceptionKey ${Option(err.getMessage).getOrElse("No message")}"
    override val pause_crm: Boolean = false
  }
  case class TokensNotFoundInDBError(msg: String) extends WorkflowAttemptInternalServerError {
    override val message: String = s"$tokensNotFoundInDBErrorKey $msg"
    override val pause_crm: Boolean = true
  }

  case class InvalidModuleError(msg: String) extends WorkflowAttemptInternalServerError {
    override val message: String = s"$invalidModuleErrorKey $msg"
    override val pause_crm: Boolean = false
  }

  case class InvalidEventError(msg: String) extends WorkflowAttemptInternalServerError {
    override val message: String = s"$invalidEventErrorKey $msg"
    override val pause_crm: Boolean = false
  }

  case class UnableToLockRow(msg: String) extends WorkflowAttemptInternalServerError {
    override val message: String = s"$unableToLockRowKey $msg"
    override val pause_crm: Boolean = false
  }

  case class HubspotFailureBatchError(msg: String, failureRecords: Seq[BatchContactResponse]) extends WorkflowAttemptInternalServerError {
    override val message: String = s"$hubspotFailureBatchErrorKey $msg failureRecords: ${failureRecords.map(_.email)}"
    override val pause_crm: Boolean = false
  }

  case class SalesforceFailureBatchError(msg: String, failureRecords: Seq[BatchContactResponse]) extends WorkflowAttemptInternalServerError {
    override val message: String = s"$salesforceFailureBatchErrorKey $msg failureRecords: ${failureRecords.map(_.email)}"
    override val pause_crm: Boolean = false
  }

  case class IntegrationNotFound(msg: String) extends WorkflowAttemptInternalServerError {
    override val message: String = s"$integrationNotFoundKey $msg"
    override val pause_crm: Boolean = false
  }

  case class TriggerNotFound(msg: String) extends WorkflowAttemptInternalServerError {
    override val message: String = s"$triggerNotFoundKey $msg"
    override val pause_crm: Boolean = false
  }


  case class FieldMappingNotFound(msg: String) extends WorkflowAttemptInternalServerError {
    override val message: String = s"$fieldMappingNotFoundKey $msg"
    override val pause_crm: Boolean = false
  }

  case class ProspectIdEmpty(msg: String) extends WorkflowAttemptInternalServerError {
    override val message: String = s"$prospectIdEmptyKey $msg"
    override val pause_crm: Boolean = false
  }

  case class CampaignIdEmpty(msg: String) extends WorkflowAttemptInternalServerError {
    override val message: String = s"$campaignIdEmptyKey $msg"
    override val pause_crm: Boolean = false
  }

  case class UpdateSentNone(msg: String) extends WorkflowAttemptInternalServerError {
    override val message: String = s"$updateSentNoneKey $msg"
    override val pause_crm: Boolean = false
  }

  case class Others(message: String) extends WorkflowAttemptInternalServerError {
    override val pause_crm: Boolean = false
  }

  def getFromMessage(message: String): WorkflowAttemptInternalServerError = {
    if (message.contains(sqlExceptionKey)) {
      WorkflowAttemptInternalServerError.SqlException(new Throwable(message.replace(sqlExceptionKey + " ", "")))

    } else if (message.contains(futureExceptionKey)) {
      WorkflowAttemptInternalServerError.FutureException(new Throwable(message.replace(sqlExceptionKey + " ", "")))

    } else if (message.contains(tokensNotFoundInDBErrorKey)) {
      WorkflowAttemptInternalServerError.TokensNotFoundInDBError(message.replace(tokensNotFoundInDBErrorKey + " ", ""))

    } else if (message.contains(invalidModuleErrorKey)) {
      WorkflowAttemptInternalServerError.InvalidModuleError(message.replace(invalidModuleErrorKey + " ", ""))

    } else if (message.contains(invalidEventErrorKey)) {
      WorkflowAttemptInternalServerError.InvalidEventError(message.replace(invalidEventErrorKey + " ", ""))

    } else if (message.contains(fieldMappingNotFoundKey)) {
      WorkflowAttemptInternalServerError.FieldMappingNotFound(message.replace(fieldMappingNotFoundKey + " ", ""))

    } else if (message.contains(triggerNotFoundKey)) {
      WorkflowAttemptInternalServerError.TriggerNotFound(message.replace(triggerNotFoundKey + " ", ""))

    } else if (message.contains(prospectIdEmptyKey)) {
      WorkflowAttemptInternalServerError.ProspectIdEmpty(message.replace(prospectIdEmptyKey + " ", ""))

    } else if (message.contains(hubspotFailureBatchErrorKey)) {
      WorkflowAttemptInternalServerError.HubspotFailureBatchError(message.replace(hubspotFailureBatchErrorKey + " ", ""), Seq())

    } else if (message.contains(errorLeadStatusOrderingKey)) {
      WorkflowAttemptInternalServerError.ErrorLeadStatusOrdering(LeadStatusOrderingError.getErrorFromMessage(message.replace(errorLeadStatusOrderingKey + " ", "")))
    } else if (message.contains(integrationNotFoundKey)) {
      WorkflowAttemptInternalServerError.IntegrationNotFound(message.replace(integrationNotFoundKey+ " ", ""))
    } else if (message.contains(campaignIdEmptyKey)) {
      WorkflowAttemptInternalServerError.CampaignIdEmpty(message.replace(campaignIdEmptyKey + " ", ""))
    } else if (message.contains(updateSentNoneKey)) {
      WorkflowAttemptInternalServerError.UpdateSentNone(message.replace(updateSentNoneKey + " ", ""))
    } else if (message.contains(wrongProcessTypeKey)) {
      WorkflowAttemptInternalServerError.WrongProcessType(message.replace(wrongProcessTypeKey + " ", ""))
    } else {
      WorkflowAttemptInternalServerError.Others(message)

    }

  }

}

sealed trait WebhookAttemptTryErrorReason extends AttemptTryErrorReason

object WebhookAttemptTryErrorReason {

}


sealed trait WorkflowAttemptAdditionalData

object WorkflowAttemptAdditionalData {

  case class TooManyRequestsAdditionalData(nextAttemptAt: DateTime) extends WorkflowAttemptAdditionalData

  // nxd - notes: PR review for error surfacing and retry
  // nxd - notes: need to check if this signature will have to change
  // nxd - notes: once all the cases are handled. Does this still stay as
  // nxd - notes: option or will it become a Try
  def getFromJsValue(
                      failure_additional_data: JsValue,
                      attemptTryErrorReasonType: AttemptTryErrorReasonType
                    ): Option[WorkflowAttemptAdditionalData] = {

    attemptTryErrorReasonType match {
      case AttemptTryErrorReasonType.MALFORMED_RESPONSE_STRUCTURE_ERROR => None
      case AttemptTryErrorReasonType.INVALID_REFRESH_TOKEN_ERROR => None
      case AttemptTryErrorReasonType.UNABLE_TO_FIND_PERSON_ID_ERROR => None
      case AttemptTryErrorReasonType.OAUTH_ERROR => None
      case AttemptTryErrorReasonType.INTERNAL_SERVER_ERROR => None
      case AttemptTryErrorReasonType.TOO_MANY_REQUESTS_ERROR =>
        val nextAttemptAtOpt = (failure_additional_data \ "nextAttemptAt").asOpt[DateTime]
        nextAttemptAtOpt.map(dt => WorkflowAttemptAdditionalData.TooManyRequestsAdditionalData(nextAttemptAt = dt))
      case AttemptTryErrorReasonType.UNAUTHORIZED_ERROR => None
      case AttemptTryErrorReasonType.UNKNOWN_ERROR => None
      case AttemptTryErrorReasonType.CRM_INTERNAL_SERVER_ERROR => None
    }

  }
}

