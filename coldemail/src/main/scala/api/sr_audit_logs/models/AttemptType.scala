package api.sr_audit_logs.models

import utils.enum_sr_utils.SREnumJsonUtils

import scala.util.Try

sealed abstract class AttemptType {
  protected val key: String

  override def toString: String = key
}


object AttemptType
  extends SREnumJsonUtils[AttemptType] {

  override protected val enumName: String = "AttemptType"

  private val KEY_WORKFLOW_ATTEMPT = "workflow_attempt"

  private val KEY_WEBHOOK_ATTEMPT = "webhook_attempt"

  case object WORKFLOW_ATTEMPT extends AttemptType {

    override val key: String = KEY_WORKFLOW_ATTEMPT

  }

  case object WEBHOOK_ATTEMPT extends AttemptType {

    override val key: String = KEY_WEBHOOK_ATTEMPT

  }

  override def toKey(value: AttemptType): String = value.key

  override def fromKey(key: String): Try[AttemptType] = Try {

    key match {

      case KEY_WORKFLOW_ATTEMPT => WORKFLOW_ATTEMPT

      case KEY_WEBHOOK_ATTEMPT => WEBHOOK_ATTEMPT
    }

  }

}



