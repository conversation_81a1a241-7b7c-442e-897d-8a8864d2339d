package api.sr_audit_logs.models

import utils.enum_sr_utils.SREnumJsonUtils

import scala.util.Try

sealed abstract class AttemptTryErrorReasonType {
  protected val key: String

  override def toString: String = key
}

object AttemptTryErrorReasonType
  extends SREnumJsonUtils[AttemptTryErrorReasonType] {

  override protected val enumName: String = "AttemptTryErrorReasonType"

  private val KEY_OAUTH_ERROR = "OAUTH_ERROR"

  private val KEY_INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR"

  private val KEY_CRM_INTERNAL_SERVER_ERROR = "CRM_INTERNAL_SERVER_ERROR"

  private val KEY_TOO_MANY_REQUESTS_ERROR = "TOO_MANY_REQUESTS_ERROR"

  private val KEY_UNAUTHORIZED_ERROR = "UNAUTHORIZED_ERROR"

  private val KEY_UNKNOWN_ERROR = "UNKNOWN_ERROR"
  private val KEY_MALFORMED_RESPONSE_STRUCTURE_ERROR = "MALFORMED_RESPONSE_STRUCTURE_ERROR"
  private val KEY_UNABLE_TO_FIND_PERSON_ID_ERROR = "UNABLE_TO_FIND_PERSON_ID_ERROR"
  private val KEY_INVALID_REFRESH_TOKEN_ERROR = "INVALID_REFRESH_TOKEN_ERROR"

  case object MALFORMED_RESPONSE_STRUCTURE_ERROR extends AttemptTryErrorReasonType {
    override protected val key: String = KEY_MALFORMED_RESPONSE_STRUCTURE_ERROR
  }
  
  case object INVALID_REFRESH_TOKEN_ERROR extends AttemptTryErrorReasonType {
    override protected val key: String = KEY_INVALID_REFRESH_TOKEN_ERROR
  }
  case object UNABLE_TO_FIND_PERSON_ID_ERROR extends AttemptTryErrorReasonType {
    override protected val key: String = KEY_UNABLE_TO_FIND_PERSON_ID_ERROR
  }

  case object OAUTH_ERROR extends AttemptTryErrorReasonType {

    override val key: String = KEY_OAUTH_ERROR

  }

  case object INTERNAL_SERVER_ERROR extends AttemptTryErrorReasonType {

    override val key: String = KEY_INTERNAL_SERVER_ERROR

  }

  case object CRM_INTERNAL_SERVER_ERROR extends AttemptTryErrorReasonType {

    override val key: String = KEY_CRM_INTERNAL_SERVER_ERROR

  }

  case object TOO_MANY_REQUESTS_ERROR extends AttemptTryErrorReasonType {

    override val key: String = KEY_TOO_MANY_REQUESTS_ERROR

  }

  case object UNAUTHORIZED_ERROR extends AttemptTryErrorReasonType {

    override val key: String = KEY_UNAUTHORIZED_ERROR

  }

  case object UNKNOWN_ERROR extends AttemptTryErrorReasonType {

    override val key: String = KEY_UNKNOWN_ERROR

  }


  override def toKey(value: AttemptTryErrorReasonType): String = value.key

  override def fromKey(key: String): Try[AttemptTryErrorReasonType] = Try {

    key match {

      case KEY_OAUTH_ERROR => OAUTH_ERROR

      case KEY_INTERNAL_SERVER_ERROR => INTERNAL_SERVER_ERROR

      case KEY_TOO_MANY_REQUESTS_ERROR => TOO_MANY_REQUESTS_ERROR

      case KEY_UNAUTHORIZED_ERROR => UNAUTHORIZED_ERROR

      case KEY_UNKNOWN_ERROR => UNKNOWN_ERROR

    }

  }

}






