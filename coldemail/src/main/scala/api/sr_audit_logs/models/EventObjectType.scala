package api.sr_audit_logs.models

import utils.enum_sr_utils.SREnumJsonUtils

import scala.util.Try

sealed abstract class EventObjectType {
  protected val key: String

  override def toString: String = key
}


object EventObjectType
  extends SREnumJsonUtils[EventObjectType] {

  override protected val enumName: String = "EventObjectType"

  private val KEY_PROSPECT = "prospect"

  private val KEY_CAMPAIGN = "campaign"

  private val KEY_WORKFLOW = "workflow"

  private val KEY_CONVERSATION = "conversation"

  private val KEY_AUTO_LINKEDIN_TASK = "auto_linkedin_task"

  private val KEY_CAMPAIGN_PROSPECT = "campaign_prospect"

  private val KEY_EMAIL_EVENT = "email_event"

  private val KEY_USER_TYPE = "user"

  private val KEY_TASK_EVENT = "task_event"


  case object PROSPECT extends EventObjectType {

    override val key: String = KEY_PROSPECT

  }

  case object CONVERSATION extends EventObjectType {

    override val key: String = KEY_CONVERSATION

  }

  case object CAMPA<PERSON>N extends EventObjectType {

    override val key: String = KEY_CAMPAIGN

  }

  case object WORKFLOW extends EventObjectType {

    override val key: String = KEY_WORKFLOW

  }

  case object AUTO_KEY_LINKEDIN_TASK extends EventObjectType {
    override val key: String = KEY_AUTO_LINKEDIN_TASK
  }

  case object CAMPAIGN_PROSPECT extends EventObjectType {
    override val key: String = KEY_CAMPAIGN_PROSPECT
  }

  case object EMAIL_EVENT extends EventObjectType {
    override val key: String = KEY_EMAIL_EVENT
  }

  case object USER extends EventObjectType {
    override val key: String = KEY_USER_TYPE
  }

  case object TASK_EVENT extends EventObjectType {
    override val key: String = KEY_TASK_EVENT
  }

  override def toKey(value: EventObjectType): String = value.key

  override def fromKey(key: String): Try[EventObjectType] = Try {

    key match {

      case KEY_PROSPECT => PROSPECT

      case KEY_CAMPAIGN => CAMPAIGN

      case KEY_WORKFLOW => WORKFLOW

      case KEY_CONVERSATION => CONVERSATION

      case KEY_AUTO_LINKEDIN_TASK => AUTO_KEY_LINKEDIN_TASK

      case KEY_CAMPAIGN_PROSPECT => CAMPAIGN_PROSPECT

      case KEY_EMAIL_EVENT => EMAIL_EVENT

      case KEY_USER_TYPE => USER

      case KEY_TASK_EVENT => TASK_EVENT
    }

  }

}



