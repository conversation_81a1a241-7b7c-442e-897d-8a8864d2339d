package api.sr_audit_logs.models

import api.accounts.EmailScheduledIdOrTaskId.TaskId
import api.{CallEventData, ProspectAccountApiResponse, TaskEventData}
import api.accounts.TeamId
import api.accounts.models.AccountId
import api.accounts.service.UserFromAccountApiResponse
import api.calendar_app.CalendarEventData
import api.campaigns.models.CampaignName
import api.campaigns.services.CampaignId
import api.prospects.EmailEventTypeData
import api.prospects.ThreadId.EmailThreadId
import api.prospects.models.ProspectId
import api.prospects.service.{CampaignProspectObjectForApi, ProspectObjectForApi}
import api.tasks.services.TaskUuid
import api.team_inbox.service.ReplySentimentUuid
import org.joda.time.DateTime
import play.api.libs.json.{JsValue, Json, OFormat, Reads, Writes}
import utils.mq.webhook.model.TriggerSource
import utils.{Help<PERSON>, SRLogger}
import utils.mq.webhook.MQTriggerMsg

import scala.util.{Failure, Success, Try}
import play.api.libs.json.JodaWrites._
import utils.mq.webhook.mq_activity_trigger.{MQActivityTriggerMsgForm, MqActivityTriggerMsgFormBase, MqActivityTriggerMsgFormOld}

trait ProspectEventDataType

trait EventDataType {
  val eventType: EventType
  val eventObjectType: EventObjectType
}

object EventDataType {

  sealed trait EventDataTypeV3 extends EventDataType

  object EventDataTypeV3 {

    // prospect type event
    case class ProspectCreated(
                              prospect_id: ProspectId,
                              created_at: Long
                            ) extends EventDataTypeV3 {
      override val eventType: EventType = EventType.PROSPECT_CREATED
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object ProspectCreated {
      implicit val writes: Writes[ProspectCreated] = new Writes[ProspectCreated] {
        override def writes(o: ProspectCreated): JsValue = Json.obj(
          "object" -> "event",
          "event_type" -> o.eventType,
          "created_at" -> o.created_at,
          "prospect_id" -> o.prospect_id
        )
      }

      implicit val reads: Reads[ProspectCreated] = Json.reads[ProspectCreated]
    }

    case class ProspectCategoryUpdated(
                                        prospect_id: ProspectId,
                                        from_category: String,
                                        to_category: String,
                                        created_at: Long
                                      ) extends EventDataTypeV3 {
      override val eventType: EventType = EventType.PROSPECT_CATEGORY_UPDATED
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object ProspectCategoryUpdated {
      implicit val writes: Writes[ProspectCategoryUpdated] = new Writes[ProspectCategoryUpdated] {
        override def writes(o: ProspectCategoryUpdated): JsValue = Json.obj(
          "object" -> "event",
          "event_type" -> o.eventType,
          "created_at" -> o.created_at,
          "from_category" -> o.from_category,
          "to_category" -> o.to_category,
          "prospect_id" -> o.prospect_id
        )
      }
    }

    // campaign type event
    case class ProspectOptedOutFromCampaign(
                                             prospect_id: ProspectId,
                                             campaign_id: CampaignId,
                                             campaign_name: CampaignName,
                                             created_at: Long
                                           ) extends EventDataTypeV3 {
      override val eventType: EventType = EventType.EVENT_V3_PROSPECT_OPTED_OUT
      override val eventObjectType = EventObjectType.CAMPAIGN_PROSPECT
    }

    object ProspectOptedOutFromCampaign {
      implicit val writes: Writes[ProspectOptedOutFromCampaign] = new Writes[ProspectOptedOutFromCampaign] {
        override def writes(o: ProspectOptedOutFromCampaign): JsValue = Json.obj(
          "object" -> "event",
          "event_type" -> o.eventType,
          "created_at" -> o.created_at,
          "prospect_id" -> o.prospect_id,
          "campaign_id" -> o.campaign_id,
          "campaign_name" -> o.campaign_name
        )
      }


      implicit val reads: Reads[ProspectOptedOutFromCampaign] = Json.reads[ProspectOptedOutFromCampaign]
    }

    case class ProspectCompleted(
                                  prospect_id: ProspectId,
                                  campaign_id: CampaignId,
                                  campaign_name: CampaignName,
                                  created_at: Long
                                ) extends EventDataTypeV3 {
      override val eventType: EventType = EventType.EVENT_V3_PROSPECT_COMPLETED
      override val eventObjectType = EventObjectType.CAMPAIGN_PROSPECT
    }

    object ProspectCompleted {
      implicit val writes: Writes[ProspectCompleted] = new Writes[ProspectCompleted] {
        override def writes(o: ProspectCompleted): JsValue = Json.obj(
          "object" -> "event",
          "event_type" -> o.eventType,
          "created_at" -> o.created_at,
          "prospect_id" -> o.prospect_id,
          "campaign_id" -> o.campaign_id,
          "campaign_name" -> o.campaign_name
        )
      }

      implicit val reads: Reads[ProspectCompleted] = Json.reads[ProspectCompleted]
    }

    case class ProspectAddedToCampaign(
                                        prospect_id: ProspectId,
                                        campaign_id: CampaignId,
                                        campaign_name: CampaignName,
                                        created_at: Long
                                      ) extends EventDataTypeV3 {
      override val eventType: EventType = EventType.EVENT_V3_PROSPECT_ADDED
      override val eventObjectType = EventObjectType.CAMPAIGN_PROSPECT
    }

    object ProspectAddedToCampaign {
      implicit val writes: Writes[ProspectAddedToCampaign] = new Writes[ProspectAddedToCampaign] {
        override def writes(o: ProspectAddedToCampaign): JsValue = Json.obj(
          "object" -> "event",
          "event_type" -> o.eventType,
          "created_at" -> o.created_at,
          "prospect_id" -> o.prospect_id,
          "campaign_id" -> o.campaign_id,
          "campaign_name" -> o.campaign_name
        )
      }

      implicit val reads: Reads[ProspectAddedToCampaign] = Json.reads[ProspectAddedToCampaign]
    }

    case class ProspectRemovedFromCampaign(
                                            prospect_id: ProspectId,
                                            campaign_id: CampaignId,
                                            campaign_name: CampaignName,
                                            created_at: Long
                                          ) extends EventDataTypeV3 {
      override val eventType: EventType = EventType.EVENT_V3_PROSPECT_REMOVED
      override val eventObjectType = EventObjectType.CAMPAIGN_PROSPECT
    }

    object ProspectRemovedFromCampaign {
      implicit val writes: Writes[ProspectRemovedFromCampaign] = new Writes[ProspectRemovedFromCampaign] {
        override def writes(o: ProspectRemovedFromCampaign): JsValue = Json.obj(
          "object" -> "event",
          "event_type" -> o.eventType,
          "created_at" -> o.created_at,
          "prospect_id" -> o.prospect_id,
          "campaign_id" -> o.campaign_id,
          "campaign_name" -> o.campaign_name
        )
      }

      implicit val reads: Reads[ProspectRemovedFromCampaign] = Json.reads[ProspectRemovedFromCampaign]
    }

    case class ProspectPausedFromCampaign(
                                           prospect_id: ProspectId,
                                           campaign_id: CampaignId,
                                           campaign_name: CampaignName,
                                           created_at: Long
                                         ) extends EventDataTypeV3 {
      override val eventType: EventType = EventType.EVENT_V3_PROSPECT_PAUSED
      override val eventObjectType = EventObjectType.CAMPAIGN_PROSPECT
    }

    object ProspectPausedFromCampaign {
      implicit val writes: Writes[ProspectPausedFromCampaign] = new Writes[ProspectPausedFromCampaign] {
        override def writes(o: ProspectPausedFromCampaign): JsValue = Json.obj(
          "object" -> "event",
          "event_type" -> o.eventType,
          "created_at" -> o.created_at,
          "prospect_id" -> o.prospect_id,
          "campaign_id" -> o.campaign_id,
          "campaign_name" -> o.campaign_name
        )
      }

      implicit val reads: Reads[ProspectPausedFromCampaign] = Json.reads[ProspectPausedFromCampaign]
    }

    case class ProspectMarkedRepliedManuallyByAdmin(
                                                     prospect_id: ProspectId,
                                                     campaign_id: CampaignId,
                                                     campaign_name: CampaignName,
                                                     created_at: Long
                                                   ) extends EventDataTypeV3{
      override val eventType: EventType = EventType.MARKED_REPLIED_MANUALLY_BY_ADMIN
      override val eventObjectType = EventObjectType.CAMPAIGN_PROSPECT
    }

    object ProspectMarkedRepliedManuallyByAdmin {
      implicit val writes: Writes[ProspectMarkedRepliedManuallyByAdmin] = new Writes[ProspectMarkedRepliedManuallyByAdmin] {
        override def writes(o: ProspectMarkedRepliedManuallyByAdmin): JsValue = Json.obj(
          "object" -> "event",
          "event_type" -> o.eventType,
          "created_at" -> o.created_at,
          "prospect_id" -> o.prospect_id,
          "campaign_id" -> o.campaign_id,
          "campaign_name" -> o.campaign_name
        )
      }

      implicit val reads: Reads[ProspectMarkedRepliedManuallyByAdmin] = Json.reads[ProspectMarkedRepliedManuallyByAdmin]
    }

    sealed trait ProspectEmailEventType extends EventDataTypeV3

    object ProspectEmailEventType {

      case class ProspectEmailEventSent(
                                         prospectId: ProspectId,
                                         campaignId: CampaignId,
                                         campaignName: CampaignName,
                                         email_scheduled_id: Long,
                                         created_at: Long
                                       ) extends ProspectEmailEventType {
        override val eventType: EventType = EventType.EMAIL_SENT
        override val eventObjectType = EventObjectType.EMAIL_EVENT
      }

      object ProspectEmailEventSent {
        implicit val writes: Writes[ProspectEmailEventSent] = new Writes[ProspectEmailEventSent] {
          override def writes(o: ProspectEmailEventSent): JsValue = Json.obj(
            "object" -> "event",
            "event_type" -> o.eventType,
            "created_at" -> o.created_at,
            "prospect_id" -> o.prospectId,
            "campaign_id" -> o.campaignId,
            "campaign_name" -> o.campaignName,
            "email_scheduled_id" -> o.email_scheduled_id
          )
        }
      }

      case class ProspectEmailEventBounced(
                                            prospectId: ProspectId,
                                            campaignId: CampaignId,
                                            campaignName: CampaignName,
                                            email_scheduled_id: Long,
                                            created_at: Long
                                          ) extends ProspectEmailEventType {
        override val eventType: EventType = EventType.EMAIL_BOUNCED
        override val eventObjectType = EventObjectType.EMAIL_EVENT
      }

      object ProspectEmailEventBounced {
        implicit val writes: Writes[ProspectEmailEventBounced] = new Writes[ProspectEmailEventBounced] {
          override def writes(o: ProspectEmailEventBounced): JsValue = Json.obj(
            "object" -> "event",
            "event_type" -> o.eventType,
            "created_at" -> o.created_at,
            "prospect_id" -> o.prospectId,
            "campaign_id" -> o.campaignId,
            "campaign_name" -> o.campaignName,
            "email_scheduled_id" -> o.email_scheduled_id
          )
        }
      }

      case class ProspectEmailEventReplied(
                                            prospectId: ProspectId,
                                            campaignId: CampaignId,
                                            campaignName: CampaignName,
                                            email_scheduled_id: Long,
                                            created_at: Long
                                          ) extends ProspectEmailEventType {
        override val eventType: EventType = EventType.NEW_REPLY
        override val eventObjectType = EventObjectType.EMAIL_EVENT
      }

      object ProspectEmailEventReplied {
        implicit val writes: Writes[ProspectEmailEventReplied] = new Writes[ProspectEmailEventReplied] {
          override def writes(o: ProspectEmailEventReplied): JsValue = Json.obj(
            "object" -> "event",
            "event_type" -> o.eventType,
            "created_at" -> o.created_at,
            "prospect_id" -> o.prospectId,
            "campaign_id" -> o.campaignId,
            "campaign_name" -> o.campaignName,
            "email_scheduled_id" -> o.email_scheduled_id
          )
        }
      }

      case class ProspectEmailEventClicked(
                                            prospectId: ProspectId,
                                            campaignId: CampaignId,
                                            campaignName: CampaignName,
                                            email_scheduled_id: Long,
                                            created_at: Long
                                          ) extends ProspectEmailEventType {
        override val eventType: EventType = EventType.EMAIL_LINK_CLICKED
        override val eventObjectType = EventObjectType.EMAIL_EVENT
      }

      object ProspectEmailEventClicked {
        implicit val writes: Writes[ProspectEmailEventClicked] = new Writes[ProspectEmailEventClicked] {
          override def writes(o: ProspectEmailEventClicked): JsValue = Json.obj(
            "object" -> "event",
            "event_type" -> o.eventType,
            "created_at" -> o.created_at,
            "prospect_id" -> o.prospectId,
            "campaign_id" -> o.campaignId,
            "campaign_name" -> o.campaignName,
            "email_scheduled_id" -> o.email_scheduled_id
          )
        }
      }

      case class ProspectEmailEventOpened(
                                           prospectId: ProspectId,
                                           campaignId: CampaignId,
                                           campaignName: CampaignName,
                                           email_scheduled_id: Long,
                                           created_at: Long
                                         ) extends ProspectEmailEventType {
        override val eventType: EventType = EventType.EMAIL_OPENED
        override val eventObjectType = EventObjectType.EMAIL_EVENT
      }

      object ProspectEmailEventOpened {
        implicit val writes: Writes[ProspectEmailEventOpened] = new Writes[ProspectEmailEventOpened] {
          override def writes(o: ProspectEmailEventOpened): JsValue = Json.obj(
            "object" -> "event",
            "event_type" -> o.eventType,
            "created_at" -> o.created_at,
            "prospect_id" -> o.prospectId,
            "campaign_id" -> o.campaignId,
            "campaign_name" -> o.campaignName,
            "email_scheduled_id" -> o.email_scheduled_id
          )
        }
      }

      implicit val writes: Writes[ProspectEmailEventType] = new Writes[ProspectEmailEventType] {
        override def writes(o: ProspectEmailEventType): JsValue = o match {
          case t: ProspectEmailEventSent => Json.toJson(t)
          case t: ProspectEmailEventBounced => Json.toJson(t)
          case t: ProspectEmailEventReplied => Json.toJson(t)
          case t: ProspectEmailEventClicked => Json.toJson(t)
          case t: ProspectEmailEventOpened => Json.toJson(t)
        }
      }
    }

    case class TeamMemberAssigned(
                                   data: UserFromAccountApiResponse,
                                   created_at: Long
                                 ) extends EventDataTypeV3 {
      override val eventType: EventType = EventType.TEAM_MEMBER_ASSIGNED
      override val eventObjectType = EventObjectType.USER
    }

    object TeamMemberAssigned {
      implicit val writes: Writes[TeamMemberAssigned] = new Writes[TeamMemberAssigned] {
        override def writes(o: TeamMemberAssigned): JsValue = Json.obj(
          "object" -> "event",
          "event_type" -> o.eventType,
          "created_at" -> o.created_at,
          "data" -> o.data
        )
      }

      implicit val reads: Reads[TeamMemberAssigned] = Json.reads[TeamMemberAssigned]
    }

    sealed trait TaskEvent extends EventDataTypeV3

    object TaskEvent {

      case class TaskEventCreated(
                                   data: TaskEventData,
                                   created_at: Long
                                 ) extends TaskEvent {
        override val eventType: EventType = EventType.TASK_CREATED
        override val eventObjectType: EventObjectType = EventObjectType.TASK_EVENT
      }

      object TaskEventCreated {
        implicit val writes: Writes[TaskEventCreated] = new Writes[TaskEventCreated] {
          override def writes(o: TaskEventCreated): JsValue = Json.obj(
            "object" -> "event",
            "event_type" -> o.eventType,
            "created_at" -> o.created_at,
            "data" -> o.data
          )
        }

        implicit val reads: Reads[TaskEventCreated] = Json.reads[TaskEventCreated]
      }


      case class InternalTaskEventCreated(
                                   data: TaskEventData,
                                   created_at: Long
                                 ) extends TaskEvent {
        override val eventType: EventType = EventType.INTERNAL_TASK_CREATED
        override val eventObjectType: EventObjectType = EventObjectType.TASK_EVENT
      }

      object InternalTaskEventCreated {
        implicit val writes: Writes[InternalTaskEventCreated] = new Writes[InternalTaskEventCreated] {
          override def writes(o: InternalTaskEventCreated): JsValue = Json.obj(
            "object" -> "event",
            "event_type" -> o.eventType,
            "created_at" -> o.created_at,
            "data" -> o.data
          )
        }

        implicit val reads: Reads[InternalTaskEventCreated] = Json.reads[InternalTaskEventCreated]
      }

      case class TaskEventDue(
                               data: TaskEventData,
                               created_at: Long
                             ) extends TaskEvent {
        override val eventType: EventType = EventType.TASK_DUE
        override val eventObjectType: EventObjectType = EventObjectType.TASK_EVENT
      }

      object TaskEventDue {
        implicit val writes: Writes[TaskEventDue] = new Writes[TaskEventDue] {
          override def writes(o: TaskEventDue): JsValue = Json.obj(
            "object" -> "event",
            "event_type" -> o.eventType,
            "created_at" -> o.created_at,
            "data" -> o.data
          )
        }

        implicit val reads: Reads[TaskEventDue] = Json.reads[TaskEventDue]
      }

      case class TaskEventSnoozed(
                               data: TaskEventData,
                               created_at: Long
                             ) extends TaskEvent {
        override val eventType: EventType = EventType.TASK_SNOOZED
        override val eventObjectType: EventObjectType = EventObjectType.TASK_EVENT
      }

      object TaskEventSnoozed {
        implicit val writes: Writes[TaskEventSnoozed] = new Writes[TaskEventSnoozed] {
          override def writes(o: TaskEventSnoozed): JsValue = Json.obj(
            "object" -> "event",
            "event_type" -> o.eventType,
            "created_at" -> o.created_at,
            "data" -> o.data
          )
        }

        implicit val reads: Reads[TaskEventSnoozed] = Json.reads[TaskEventSnoozed]
      }

      case class TaskEventDone(
                               data: TaskEventData,
                               created_at: Long
                             ) extends TaskEvent {
        override val eventType: EventType = EventType.TASK_DONE
        override val eventObjectType: EventObjectType = EventObjectType.TASK_EVENT
      }

      object TaskEventDone {
        implicit val writes: Writes[TaskEventDone] = new Writes[TaskEventDone] {
          override def writes(o: TaskEventDone): JsValue = Json.obj(
            "object" -> "event",
            "event_type" -> o.eventType,
            "created_at" -> o.created_at,
            "data" -> o.data
          )
        }

        implicit val reads: Reads[TaskEventDone] = Json.reads[TaskEventDone]
      }

      case class TaskEventSkipped(
                               data: TaskEventData,
                               created_at: Long
                             ) extends TaskEvent {
        override val eventType: EventType = EventType.TASK_SKIPPED
        override val eventObjectType: EventObjectType = EventObjectType.TASK_EVENT
      }

      object TaskEventSkipped {
        implicit val writes: Writes[TaskEventSkipped] = new Writes[TaskEventSkipped] {
          override def writes(o: TaskEventSkipped): JsValue = Json.obj(
            "object" -> "event",
            "event_type" -> o.eventType,
            "created_at" -> o.created_at,
            "data" -> o.data
          )
        }

        implicit val reads: Reads[TaskEventSkipped] = Json.reads[TaskEventSkipped]
      }

      case class TaskEventArchived(
                               data: TaskEventData,
                               created_at: Long
                             ) extends TaskEvent {
        override val eventType: EventType = EventType.TASK_ARCHIVED
        override val eventObjectType: EventObjectType = EventObjectType.TASK_EVENT
      }

      object TaskEventArchived {
        implicit val writes: Writes[TaskEventArchived] = new Writes[TaskEventArchived] {
          override def writes(o: TaskEventArchived): JsValue = Json.obj(
            "object" -> "event",
            "event_type" -> o.eventType,
            "created_at" -> o.created_at,
            "data" -> o.data
          )
        }

        implicit val reads: Reads[TaskEventArchived] = Json.reads[TaskEventArchived]
      }


      implicit val writes: Writes[TaskEvent] = new Writes[TaskEvent] {
        override def writes(o: TaskEvent): JsValue = o match {
          case t: TaskEventCreated => Json.toJson(t)
          case t: TaskEventDue => Json.toJson(t)
          case t: TaskEventSnoozed => Json.toJson(t)
          case t: TaskEventDone => Json.toJson(t)
          case t: TaskEventSkipped => Json.toJson(t)
          case t: TaskEventArchived => Json.toJson(t)
          case t: InternalTaskEventCreated => Json.toJson(t)
        }
      }

    }

    sealed trait CallEvent extends EventDataTypeV3

    object CallEvent {

      case class CallEventPlaced(
                                  data: CallEventData,
                                  created_at: Long
                                ) extends CallEvent {
        override val eventType: EventType = EventType.CALL_PLACED
        override val eventObjectType: EventObjectType = EventObjectType.TASK_EVENT
      }

      object CallEventPlaced {
        implicit val writes: Writes[CallEventPlaced] = new Writes[CallEventPlaced] {
          override def writes(o: CallEventPlaced): JsValue = Json.obj(
            "object" -> "event",
            "event_type" -> o.eventType,
            "created_at" -> o.created_at,
            "data" -> o.data
          )
        }

        implicit val reads: Reads[CallEventPlaced] = Json.reads[CallEventPlaced]
      }

      case class CallEventReceived(
                                  data: CallEventData,
                                  created_at: Long
                                ) extends CallEvent {
        override val eventType: EventType = EventType.CALL_RECEIVED
        override val eventObjectType: EventObjectType = EventObjectType.TASK_EVENT
      }

      object CallEventReceived {
        implicit val writes: Writes[CallEventReceived] = new Writes[CallEventReceived] {
          override def writes(o: CallEventReceived): JsValue = Json.obj(
            "object" -> "event",
            "event_type" -> o.eventType,
            "created_at" -> o.created_at,
            "data" -> o.data
          )
        }

        implicit val reads: Reads[CallEventReceived] = Json.reads[CallEventReceived]
      }

      implicit val writes: Writes[CallEvent] = new Writes[CallEvent] {
        override def writes(o: CallEvent): JsValue = o match {
          case t: CallEventPlaced => Json.toJson(t)
          case t: CallEventReceived => Json.toJson(t)
        }
      }

    }

    case class CalendarMeetingBookedEvent(
                                           data: CalendarEventData,
                                           created_at: Long
                                         ) extends EventDataTypeV3 {
      override val eventType: EventType = EventType.CALENDAR_MEETING_BOOKED
      override val eventObjectType: EventObjectType = EventObjectType.TASK_EVENT
    }

    object CalendarMeetingBookedEvent {
      implicit val writes: Writes[CalendarMeetingBookedEvent] = new Writes[CalendarMeetingBookedEvent] {
        override def writes(o: CalendarMeetingBookedEvent): JsValue = Json.obj(
          "object" -> "event",
          "event_type" -> o.eventType,
          "created_at" -> o.created_at
        )
      }

      implicit val reads: Reads[CalendarMeetingBookedEvent] = Json.reads[CalendarMeetingBookedEvent]
    }

    case class AddedProspectAccount(
                                     data: ProspectAccountApiResponse,
                                     created_at: Long
                                   ) extends EventDataTypeV3 {
      override val eventType: EventType = EventType.PROSPECT_ACCOUNT_ADDED
      override val eventObjectType: EventObjectType = EventObjectType.PROSPECT
    }

    object AddedProspectAccount {
      implicit val writes: Writes[AddedProspectAccount] = new Writes[AddedProspectAccount] {
        override def writes(o: AddedProspectAccount): JsValue = Json.obj(
          "object" -> "event",
          "event_type" -> o.eventType,
          "created_at" -> o.created_at,
          "data" -> o.data
        )
      }

      implicit val reads: Reads[AddedProspectAccount] = Json.reads[AddedProspectAccount]
    }

    implicit val writes: Writes[EventDataTypeV3] = new Writes[EventDataTypeV3] {
      override def writes(o: EventDataTypeV3): JsValue = {

        o match {
          case t: ProspectCreated => Json.toJson(t)
          case t: ProspectCategoryUpdated => Json.toJson(t)
          case t: ProspectOptedOutFromCampaign => Json.toJson(t)
          case t: ProspectCompleted => Json.toJson(t)
          case t: ProspectAddedToCampaign => Json.toJson(t)
          case t: ProspectRemovedFromCampaign => Json.toJson(t)
          case t: ProspectPausedFromCampaign => Json.toJson(t)
          case t: ProspectEmailEventType => Json.toJson(t)
          case t: TeamMemberAssigned => Json.toJson(t)
          case t: TaskEvent => Json.toJson(t)
          case t: CallEvent => Json.toJson(t)
          case t: CalendarMeetingBookedEvent => Json.toJson(t)
          case t: AddedProspectAccount => Json.toJson(t)
          case t: ProspectMarkedRepliedManuallyByAdmin => Json.toJson(t)
        }

      }
    }

  }

  sealed trait PushEventDataType extends EventDataType {
    val prospectId: Long
    val accountId: Long
    val teamId: Long
    val prospectCategoryId: Option[Long] = None
    val triggerPath: Option[TriggerSource]
  }

  object PushEventDataType {
    case class UpdateProspectCategoryEventData(
                                                prospectId: Long,
                                                doerAccountId: Long,
                                                teamId: Long,
                                                newProspectCategoryIdCustom: Long,
                                                triggerPath: Option[TriggerSource]
                                              ) extends PushEventDataType {

      override val eventType = EventType.PROSPECT_CATEGORY_UPDATED
      override val eventObjectType = EventObjectType.PROSPECT
      override val accountId: Long = doerAccountId
      override val prospectCategoryId: Option[Long] = Some(newProspectCategoryIdCustom)
    }

    object UpdateProspectCategoryEventData {
      implicit val writes: Writes[UpdateProspectCategoryEventData] = Json.writes[UpdateProspectCategoryEventData]
      implicit val reads: Reads[UpdateProspectCategoryEventData] = Json.reads[UpdateProspectCategoryEventData]
    }


    case class CreatedProspectsEventData(
                                          created_id: Long,
                                          ownerAccountId: Long,
                                          teamId: Long,
                                          triggerPath: Option[TriggerSource]
                                        ) extends PushEventDataType {

      override val eventType = EventType.CREATED_PROSPECT_IN_SMARTREACH
      override val eventObjectType = EventObjectType.PROSPECT
      override val prospectId: Long = created_id
      override val accountId: Long = ownerAccountId
    }

    object CreatedProspectsEventData {
      implicit val writes: Writes[CreatedProspectsEventData] = Json.writes[CreatedProspectsEventData]
      implicit val reads: Reads[CreatedProspectsEventData] = Json.reads[CreatedProspectsEventData]
    }


    case class UpdatedProspectsEventData(
                                          updated_id: Long,
                                          ownerAccountId: Long,
                                          oldProspectDeduplicationColumn: Option[OldProspectDeduplicationColumn],
                                          teamId: Long,
                                          triggerPath: Option[TriggerSource],

                                        ) extends PushEventDataType {

      override val eventType = EventType.UPDATED_PROSPECT_IN_SMARTREACH
      override val eventObjectType = EventObjectType.PROSPECT
      override val accountId: Long = ownerAccountId
      override val prospectId: Long = updated_id
    }

    object UpdatedProspectsEventData {
      implicit val writes: Writes[UpdatedProspectsEventData] = Json.writes[UpdatedProspectsEventData]
      implicit val reads: Reads[UpdatedProspectsEventData] = Json.reads[UpdatedProspectsEventData]
    }


    case class CreateOrUpdatedProspectsEventData(
                                                  prospectId: Long,
                                                  doerAccountId: Long,
                                                  teamId: Long,
                                                  triggerPath: Option[TriggerSource] // FIXME Check if i also have to pass OlDDeduplication column data for this event
                                                ) extends PushEventDataType {

      override val eventType = EventType.CREATED_OR_UPDATED_PROSPECT_IN_SMARTREACH
      override val eventObjectType = EventObjectType.PROSPECT
      override val accountId: Long = doerAccountId
    }

    object CreateOrUpdatedProspectsEventData {
      implicit val writes: Writes[CreateOrUpdatedProspectsEventData] = Json.writes[CreateOrUpdatedProspectsEventData]
      implicit val reads: Reads[CreateOrUpdatedProspectsEventData] = Json.reads[CreateOrUpdatedProspectsEventData]
    }


    implicit val writes: Writes[PushEventDataType] = new Writes[PushEventDataType] {
      def writes(event: PushEventDataType): JsValue = {

        event match {
          case t: UpdateProspectCategoryEventData => Json.toJson(t)
          case t: CreatedProspectsEventData => Json.toJson(t)
          case t: UpdatedProspectsEventData => Json.toJson(t)
          case t: CreateOrUpdatedProspectsEventData => Json.toJson(t)
        }

      }
    }

  }

  sealed trait ActivityEventDataType extends EventDataType {
    val teamId: TeamId
    val accountId: AccountId
  }

  object ActivityEventDataType {

    sealed trait ReplySentimentUpdatedEventData extends ActivityEventDataType {
      val teamId: TeamId
      val accountId: AccountId
      val replySentimentUuid: ReplySentimentUuid
    }

    object ReplySentimentUpdatedEventData {
      case class ReplySentimentUpdatedEventDataEmail(
                                                      accountId: AccountId,
                                                      teamId: TeamId,
                                                      threadId: Long, // this will give us, prospectIds, campaignId, campaignName
                                                      replySentimentUuid: ReplySentimentUuid
                                                    ) extends ReplySentimentUpdatedEventData {

        override val eventType = EventType.REPLY_SENTIMENT_UPDATED
        override val eventObjectType = EventObjectType.CONVERSATION

      }

      object ReplySentimentUpdatedEventDataEmail {
        implicit val writes: Writes[ReplySentimentUpdatedEventDataEmail] = Json.writes[ReplySentimentUpdatedEventDataEmail]
        implicit val reads: Reads[ReplySentimentUpdatedEventDataEmail] = Json.reads[ReplySentimentUpdatedEventDataEmail]
      }

      case class ReplySentimentUpdatedEventDataTask(
                                                     accountId: AccountId,
                                                     teamId: TeamId,
                                                     prospectId: ProspectId,
                                                     taskId: TaskUuid,
                                                     replySentimentUuid: ReplySentimentUuid
                                                   ) extends ReplySentimentUpdatedEventData {

        override val eventType = EventType.TASK_REPLY_SENTIMENT_UPDATED
        override val eventObjectType = EventObjectType.TASK_EVENT

      }

      object ReplySentimentUpdatedEventDataTask {
        implicit val writes: Writes[ReplySentimentUpdatedEventDataTask] = Json.writes[ReplySentimentUpdatedEventDataTask]
        implicit val reads: Reads[ReplySentimentUpdatedEventDataTask] = Json.reads[ReplySentimentUpdatedEventDataTask]
      }


    }



    case class EmailOpenEventData(
                                   email_scheduled_id: Long,
                                   prospect_id: ProspectId,
                                   teamId: TeamId,
                                   accountId: AccountId,
                                   campaignId: Option[CampaignId],
                                   campaignName: Option[CampaignName]
                                 ) extends ActivityEventDataType {

      override val eventType = EventType.EMAIL_OPENED
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object EmailOpenEventData {
      implicit val writes: Writes[EmailOpenEventData] = Json.writes[EmailOpenEventData]
      implicit val reads: Reads[EmailOpenEventData] = Json.reads[EmailOpenEventData]
    }

    case class EmailSentEventData(
                                   email_scheduled_id: Long,
                                   prospect_id: ProspectId,
                                   teamId: TeamId,
                                   campaignId: Option[CampaignId],
                                   campaignName: Option[CampaignName],
                                   accountId: AccountId,
                                 ) extends ActivityEventDataType {

      override val eventType = EventType.EMAIL_SENT
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object EmailSentEventData {
      implicit val writes: Writes[EmailSentEventData] = Json.writes[EmailSentEventData]
      implicit val reads: Reads[EmailSentEventData] = Json.reads[EmailSentEventData]
    }

    case class EmailBouncedEventData(
                                      prospectId: Option[ProspectId],
                                      accountId: AccountId,
                                      teamId: TeamId,
                                      campaignId: Option[CampaignId],
                                      campaignName: Option[CampaignName],
                                      email_scheduled_id: Option[Long],
                                    ) extends ActivityEventDataType {

      override val eventType = EventType.EMAIL_BOUNCED
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object EmailBouncedEventData {
      implicit val writes: Writes[EmailBouncedEventData] = Json.writes[EmailBouncedEventData]
      implicit val reads: Reads[EmailBouncedEventData] = Json.reads[EmailBouncedEventData]
    }


    case class EmailLinkClickedEventData(
                                          prospectId: ProspectId,
                                          accountId: AccountId,
                                          teamId: TeamId,
                                          campaignId: Option[CampaignId],
                                          campaignName: Option[CampaignName],
                                          clickedUrl: String,
                                          email_scheduled_id: Long
                                        ) extends ActivityEventDataType {

      override val eventType = EventType.EMAIL_LINK_CLICKED
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object EmailLinkClickedEventData {
      implicit val writes: Writes[EmailLinkClickedEventData] = Json.writes[EmailLinkClickedEventData]
      implicit val reads: Reads[EmailLinkClickedEventData] = Json.reads[EmailLinkClickedEventData]
    }

    case class EmailInvalidEventData(
                                      prospectId: ProspectId,
                                      accountId: AccountId,
                                      teamId: TeamId
                                    ) extends ActivityEventDataType {

      override val eventType = EventType.EMAIL_INVALID
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object EmailInvalidEventData {
      implicit val writes: Writes[EmailInvalidEventData] = Json.writes[EmailInvalidEventData]
      implicit val reads: Reads[EmailInvalidEventData] = Json.reads[EmailInvalidEventData]
    }

    case class NewReplyEventData(
                                  teamId: TeamId,
                                  accountId: AccountId,
                                  campaignId: Option[CampaignId],
                                  campaignName: Option[CampaignName],
                                  prospectId: Option[ProspectId],
                                  email_scheduled_id: Option[Long],
                                ) extends ActivityEventDataType {

      override val eventType = EventType.NEW_REPLY
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object NewReplyEventData {
      implicit val writes: Writes[NewReplyEventData] = Json.writes[NewReplyEventData]
      implicit val reads: Reads[NewReplyEventData] = Json.reads[NewReplyEventData]
    }

    case class AutoReplyEventData(
                                   teamId: TeamId,
                                   accountId: AccountId,
                                   email_scheduled_id: Long
                                 ) extends ActivityEventDataType {

      override val eventType = EventType.AUTO_REPLY
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object AutoReplyEventData {
      implicit val writes: Writes[AutoReplyEventData] = Json.writes[AutoReplyEventData]
      implicit val reads: Reads[AutoReplyEventData] = Json.reads[AutoReplyEventData]
    }


    case class NoteAddedData(
                              prospectId: ProspectId,
                              accountId: AccountId,
                              teamId: TeamId,
                              // TODO: add relevant data
                            ) extends ActivityEventDataType {

      override val eventType = EventType.NOTE_ADDED
      override val eventObjectType = EventObjectType.PROSPECT
    }


    object NoteAddedData {
      implicit val writes: Writes[NoteAddedData] = Json.writes[NoteAddedData]
      implicit val reads: Reads[NoteAddedData] = Json.reads[NoteAddedData]
    }


    case class MergedDuplicatesData(
                                     prospectId: ProspectId,
                                     accountId: AccountId,
                                     teamId: TeamId,
                                     // TODO: add relevant data
                                   ) extends ActivityEventDataType {

      override val eventType = EventType.MERGED_DUPLICATES
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object MergedDuplicatesData {
      implicit val writes: Writes[MergedDuplicatesData] = Json.writes[MergedDuplicatesData]
      implicit val reads: Reads[MergedDuplicatesData] = Json.reads[MergedDuplicatesData]
    }

    case class OutOfOfficeReplyEventData(
                                          teamId: TeamId,
                                          accountId: AccountId,
                                          email_scheduled_id: Long,
                                        ) extends ActivityEventDataType {

      override val eventType = EventType.OUT_OF_OFFICE_REPLY
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object OutOfOfficeReplyEventData {
      implicit val writes: Writes[OutOfOfficeReplyEventData] = Json.writes[OutOfOfficeReplyEventData]
      implicit val reads: Reads[OutOfOfficeReplyEventData] = Json.reads[OutOfOfficeReplyEventData]
    }

    case class ProspectOptedOutEventData(
                                          accountId: AccountId,
                                          teamId: TeamId,
                                          campaignId: Option[CampaignId],
                                          prospect_id: ProspectId,
                                          campaignName: Option[CampaignName]
                                        ) extends ActivityEventDataType {

      override val eventType = EventType.PROSPECT_OPTED_OUT
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object ProspectOptedOutEventData {
      implicit val writes: Writes[ProspectOptedOutEventData] = Json.writes[ProspectOptedOutEventData]
      implicit val reads: Reads[ProspectOptedOutEventData] = Json.reads[ProspectOptedOutEventData]
    }


    case class ProspectCompletedEventData(
                                           accountId: AccountId,
                                           teamId: TeamId,
                                           campaignId: Option[CampaignId],
                                           prospect_id: ProspectId,
                                           campaignName: Option[CampaignName]
                                         ) extends ActivityEventDataType {

      override val eventType = EventType.PROSPECT_COMPLETED
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object ProspectCompletedEventData {
      implicit val writes: Writes[ProspectCompletedEventData] = Json.writes[ProspectCompletedEventData]
      implicit val reads: Reads[ProspectCompletedEventData] = Json.reads[ProspectCompletedEventData]
    }


    case class AnyEmailActivityEventData(
                                          accountId: AccountId,
                                          teamId: TeamId,
                                          prospectId: Long
                                        ) extends ActivityEventDataType {

      override val eventType = EventType.ANY_EMAIL_ACTIVITY
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object AnyEmailActivityEventData {
      implicit val writes: Writes[AnyEmailActivityEventData] = Json.writes[AnyEmailActivityEventData]
      implicit val reads: Reads[AnyEmailActivityEventData] = Json.reads[AnyEmailActivityEventData]
    }

    case class AutoLinkedinTaskCompletedEventData(
                                                   teamId: TeamId,
                                                   accountId: AccountId,
                                                   resultObj: JsValue
                                                 ) extends ActivityEventDataType {
      override val eventType: EventType = EventType.AUTO_LINKEDIN_TASK_COMPLETED
      override val eventObjectType: EventObjectType = EventObjectType.AUTO_KEY_LINKEDIN_TASK
    }

    object AutoLinkedinTaskCompletedEventData {
      given format: OFormat[AutoLinkedinTaskCompletedEventData] = Json.format[AutoLinkedinTaskCompletedEventData]
    }

    case class AutoLinkedinTaskFailedEventData(
                                                teamId: TeamId,
                                                accountId: AccountId,
                                                resultObj: JsValue
                                              ) extends ActivityEventDataType {
      override val eventType: EventType = EventType.AUTO_LINKEDIN_TASK_COMPLETED
      override val eventObjectType: EventObjectType = EventObjectType.AUTO_KEY_LINKEDIN_TASK
    }

    object AutoLinkedinTaskFailedEventData {
      given format: OFormat[AutoLinkedinTaskFailedEventData] = Json.format[AutoLinkedinTaskFailedEventData]
    }

    implicit val writes: Writes[ActivityEventDataType] = new Writes[ActivityEventDataType] {
      def writes(event: ActivityEventDataType): JsValue = {

        event match {
          case t: EmailOpenEventData => Json.toJson(t)
          case t: EmailSentEventData => Json.toJson(t)
          case t: EmailBouncedEventData => Json.toJson(t)
          case t: EmailLinkClickedEventData => Json.toJson(t)
          case t: EmailInvalidEventData => Json.toJson(t)
          case t: NewReplyEventData => Json.toJson(t)
          case t: AutoReplyEventData => Json.toJson(t)
          case t: OutOfOfficeReplyEventData => Json.toJson(t)
          case t: ProspectOptedOutEventData => Json.toJson(t)
          case t: ProspectCompletedEventData => Json.toJson(t)
          case t: AnyEmailActivityEventData => Json.toJson(t)
          case t: ReplySentimentUpdatedEventData.ReplySentimentUpdatedEventDataEmail => Json.toJson(t)
          case t: ReplySentimentUpdatedEventData.ReplySentimentUpdatedEventDataTask => Json.toJson(t)
          case t: AutoLinkedinTaskCompletedEventData => Json.toJson(t)
          case t: AutoLinkedinTaskFailedEventData => Json.toJson(t)
          case t: NoteAddedData => Json.toJson(t)
          case t: MergedDuplicatesData => Json.toJson(t)
        }

      }
    }

    def getEmailScheduledId(eventDataType: EventDataType.ActivityEventDataType): Option[Seq[Long]] = {
      eventDataType match {
        case data: EmailOpenEventData =>
          Some(Seq(data.email_scheduled_id))

        case data: EmailSentEventData =>
          Some(Seq(data.email_scheduled_id))

        case data: EmailBouncedEventData =>
          data.email_scheduled_id.map(v => Seq(v))

        case data: EmailLinkClickedEventData =>
          Some(Seq(data.email_scheduled_id))

        case _: EmailInvalidEventData =>
          None

        case data: NewReplyEventData =>
          data.email_scheduled_id.map(v => Seq(v))

        case data: AutoReplyEventData =>
          Some(Seq(data.email_scheduled_id))

        case data: OutOfOfficeReplyEventData =>
          Some(Seq(data.email_scheduled_id))

        case _: ProspectOptedOutEventData =>
          None

        case _: ProspectCompletedEventData =>
          None

        case _: AnyEmailActivityEventData =>
          None
        case _: ReplySentimentUpdatedEventData =>
          None

        case _: AutoLinkedinTaskCompletedEventData =>
          None

        case _: AutoLinkedinTaskFailedEventData =>
          None

        case _: NoteAddedData =>
          None
        case _: MergedDuplicatesData =>
          None

      }

    }
    def getCampaignId(eventDataType: EventDataType.ActivityEventDataType): Option[Long] = {
      eventDataType match {
        case data: EmailOpenEventData =>
          data.campaignId.map(_.id)

        case _: EmailSentEventData =>
          None

        case data: EmailBouncedEventData =>
          data.campaignId.map(_.id)

        case data: EmailLinkClickedEventData =>
          data.campaignId.map(_.id)

        case _: EmailInvalidEventData =>
          None

        case data: NewReplyEventData =>
          data.campaignId.map(_.id)

        case _: AutoReplyEventData =>
          None

        case _: OutOfOfficeReplyEventData =>
          None

        case data: ProspectOptedOutEventData =>
          data.campaignId.map(_.id)

        case data: ProspectCompletedEventData =>
          data.campaignId.map(_.id)

        case data: ReplySentimentUpdatedEventData =>
         None

        case _: AnyEmailActivityEventData =>
          None

        case _: AutoLinkedinTaskCompletedEventData =>
          None

        case _: AutoLinkedinTaskFailedEventData =>
          None

        case _: MergedDuplicatesData =>
          None

        case _: NoteAddedData =>
          None
      }
    }

    def getCampaignName(eventDataType: EventDataType.ActivityEventDataType): Option[String] = {
      eventDataType match {
        case data: EmailOpenEventData =>
          data.campaignName.map(_.name)

        case _: EmailSentEventData =>
          None

        case data: EmailBouncedEventData =>
          data.campaignName.map(_.name)

        case data: EmailLinkClickedEventData =>
          data.campaignName.map(_.name)

        case _: EmailInvalidEventData =>
          None

        case data: NewReplyEventData =>
          data.campaignName.map(_.name)

        case _: AutoReplyEventData =>
          None

        case _: OutOfOfficeReplyEventData =>
          None

        case data: ProspectOptedOutEventData =>
          data.campaignName.map(_.name)

        case data: ProspectCompletedEventData =>
          data.campaignName.map(_.name)

        case _: AnyEmailActivityEventData =>
          None
        case data: ReplySentimentUpdatedEventData =>
          None

        case _: AutoLinkedinTaskCompletedEventData =>
          None

        case _: AutoLinkedinTaskFailedEventData =>
          None

        case _: MergedDuplicatesData =>
          None

        case _: NoteAddedData =>
          None

      }
    }

    def getProspectIds(eventDataType: EventDataType.ActivityEventDataType): Seq[Long] = {
      eventDataType match {
        case data: EmailOpenEventData =>
          Seq(data.prospect_id.id)

        case data: EmailSentEventData =>
          Seq(data.prospect_id.id)

        case data: EmailBouncedEventData =>
          data.prospectId.map(v => Seq(v.id)).getOrElse(Seq())

        case data: EmailLinkClickedEventData =>
          Seq(data.prospectId.id)

        case data: EmailInvalidEventData =>
          Seq(data.prospectId.id)

        case data: NewReplyEventData =>
          data.prospectId.map(v => Seq(v.id)).getOrElse(Seq())

        case _: AutoReplyEventData =>
          Seq()

        case _: OutOfOfficeReplyEventData =>
          Seq()

        case data: ProspectOptedOutEventData =>
          Seq(data.prospect_id.id)

        case data: ProspectCompletedEventData =>
          Seq(data.prospect_id.id)

        case data: AnyEmailActivityEventData =>
          Seq(data.prospectId)

        case data: ReplySentimentUpdatedEventData =>
          data match {

            case _: ReplySentimentUpdatedEventData.ReplySentimentUpdatedEventDataEmail =>
              Seq()

            case t: ReplySentimentUpdatedEventData.ReplySentimentUpdatedEventDataTask =>
              Seq(t.prospectId.id)
          }


        case _: AutoLinkedinTaskCompletedEventData =>
          Seq()

        case _: AutoLinkedinTaskFailedEventData =>
          Seq()

        case data: MergedDuplicatesData =>
          Seq(data.prospectId.id)

        case data: NoteAddedData =>
          Seq(data.prospectId.id)

      }
    }


  }

  sealed trait SyncEventDataType extends EventDataType {
    val triggerId: Long
  }

  object SyncEventDataType {

    case class HubspotProspectSyncEventData(
                                             triggerId: Long
                                           ) extends SyncEventDataType {

      override val eventType = EventType.HUBSPOT_PROSPECT_SYNC
      override val eventObjectType = EventObjectType.PROSPECT

    }

    object HubspotProspectSyncEventData {
      implicit val writes: Writes[HubspotProspectSyncEventData] = Json.writes[HubspotProspectSyncEventData]
      implicit val reads: Reads[HubspotProspectSyncEventData] = Json.reads[HubspotProspectSyncEventData]
    }


    case class ZohoProspectSyncEventData(
                                          triggerId: Long
                                        ) extends SyncEventDataType {

      override val eventType = EventType.ZOHO_PROSPECT_SYNC
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object ZohoProspectSyncEventData {
      implicit val writes: Writes[ZohoProspectSyncEventData] = Json.writes[ZohoProspectSyncEventData]
      implicit val reads: Reads[ZohoProspectSyncEventData] = Json.reads[ZohoProspectSyncEventData]
    }

    case class ZohoLeadSyncEventData(
                                      triggerId: Long
                                    ) extends SyncEventDataType {

      override val eventType = EventType.ZOHO_LEAD_SYNC
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object ZohoLeadSyncEventData {
      implicit val writes: Writes[ZohoLeadSyncEventData] = Json.writes[ZohoLeadSyncEventData]
      implicit val reads: Reads[ZohoLeadSyncEventData] = Json.reads[ZohoLeadSyncEventData]
    }

    case class ZohoRecruitProspectSyncEventData(
                                                 triggerId: Long
                                               ) extends SyncEventDataType {

      override val eventType = EventType.ZOHO_RECRUIT_PROSPECT_SYNC
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object ZohoRecruitProspectSyncEventData {
      implicit val writes: Writes[ZohoRecruitProspectSyncEventData] = Json.writes[ZohoRecruitProspectSyncEventData]
      implicit val reads: Reads[ZohoRecruitProspectSyncEventData] = Json.reads[ZohoRecruitProspectSyncEventData]
    }

    case class ZohoRecruitCandidateSyncEventData(
                                                  triggerId: Long
                                                ) extends SyncEventDataType {

      override val eventType = EventType.ZOHO_RECRUIT_CANDIDATE_SYNC
      override val eventObjectType = EventObjectType.PROSPECT

    }

    object ZohoRecruitCandidateSyncEventData {
      implicit val writes: Writes[ZohoRecruitCandidateSyncEventData] = Json.writes[ZohoRecruitCandidateSyncEventData]
      implicit val reads: Reads[ZohoRecruitCandidateSyncEventData] = Json.reads[ZohoRecruitCandidateSyncEventData]
    }


    case class PipedriveProspectSyncEventData(
                                               triggerId: Long
                                             ) extends SyncEventDataType {

      override val eventType = EventType.PIPEDRIVE_PROSPECT_SYNC
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object PipedriveProspectSyncEventData {
      implicit val writes: Writes[PipedriveProspectSyncEventData] = Json.writes[PipedriveProspectSyncEventData]
      implicit val reads: Reads[PipedriveProspectSyncEventData] = Json.reads[PipedriveProspectSyncEventData]
    }

    case class SalesforceProspectSyncEventData(
                                                triggerId: Long
                                              ) extends SyncEventDataType {

      override val eventType = EventType.SALESFORCE_PROSPECT_SYNC
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object SalesforceProspectSyncEventData {
      implicit val writes: Writes[SalesforceProspectSyncEventData] = Json.writes[SalesforceProspectSyncEventData]
      implicit val reads: Reads[SalesforceProspectSyncEventData] = Json.reads[SalesforceProspectSyncEventData]
    }

    case class SalesforceLeadSyncEventData(
                                            triggerId: Long
                                          ) extends SyncEventDataType {

      override val eventType = EventType.SALESFORCE_LEAD_SYNC
      override val eventObjectType = EventObjectType.PROSPECT
    }

    object SalesforceLeadSyncEventData {
      implicit val writes: Writes[SalesforceLeadSyncEventData] = Json.writes[SalesforceLeadSyncEventData]
      implicit val reads: Reads[SalesforceLeadSyncEventData] = Json.reads[SalesforceLeadSyncEventData]
    }

    implicit val writes: Writes[SyncEventDataType] = new Writes[SyncEventDataType] {
      def writes(event: SyncEventDataType): JsValue = {

        event match {
          case t: HubspotProspectSyncEventData => Json.toJson(t)
          case t: ZohoProspectSyncEventData => Json.toJson(t)
          case t: ZohoLeadSyncEventData => Json.toJson(t)
          case t: ZohoRecruitProspectSyncEventData => Json.toJson(t)
          case t: ZohoRecruitCandidateSyncEventData => Json.toJson(t)
          case t: PipedriveProspectSyncEventData => Json.toJson(t)
          case t: SalesforceProspectSyncEventData => Json.toJson(t)
          case t: SalesforceLeadSyncEventData => Json.toJson(t)
        }

      }
    }
  }


  case class AddToDoNotContact(
                                team_id: TeamId,
                                module_id: Long
                              ) extends EventDataType {

    override val eventType = EventType.ADD_TO_DO_NOT_CONTACT
    override val eventObjectType = EventObjectType.PROSPECT

  }

  object AddToDoNotContact {
    implicit val writes: Writes[AddToDoNotContact] = Json.writes[AddToDoNotContact]
    implicit val reads: Reads[AddToDoNotContact] = Json.reads[AddToDoNotContact]
  }


  implicit val writes: Writes[EventDataType] = new Writes[EventDataType] {
    def writes(event: EventDataType): JsValue = {

      event match {
        case t: PushEventDataType => Json.toJson(t)
        case t: ActivityEventDataType => Json.toJson(t)
        case t: SyncEventDataType => Json.toJson(t)
        case t: AddToDoNotContact => Json.toJson(t)
        case t: EventDataOutputV3 => Json.toJson(t)
      }

    }
  }


  def getEventDataFromMQTriggerMsg(
                                    msg: MQTriggerMsg
                                  )(using Logger: SRLogger): Try[Seq[EventDataType.PushEventDataType]] = {
    EventType.fromKey(msg.event).flatMap {

      case EventType.PROSPECT_CATEGORY_UPDATED =>
        val seqOfTry = msg.prospectIds.map { prospectId =>
          msg.updatedProspectCategoryId match {
            case None =>
             Logger.fatal(s"No newProspectCategoryIdCustom sent msg : $msg")
              Success(None) //we dont want the entire set to fail because of one fail

            case Some(newProspectCategoryIdCustom) =>

              Success(
                Some(EventDataType.PushEventDataType.UpdateProspectCategoryEventData(
                  prospectId = prospectId,
                  doerAccountId = msg.accountId,
                  teamId = msg.teamId,
                  newProspectCategoryIdCustom = newProspectCategoryIdCustom,
                  triggerPath = msg.triggerPath
                ))
              )
          }

        }

        Helpers.seqTryToTrySeq(seqOfTry)
          .map(
            _.filter(
              _.isDefined
            )
              .map(_.get)
          )

      case EventType.CREATED_PROSPECT_IN_SMARTREACH =>

        Success(msg.prospectIds.map { created_id =>
          EventDataType.PushEventDataType.CreatedProspectsEventData(
            created_id = created_id,
            ownerAccountId = msg.accountId,
            teamId = msg.teamId,
            triggerPath = msg.triggerPath
          )
        })


      case EventType.UPDATED_PROSPECT_IN_SMARTREACH =>

        Success(
          msg.prospectIds.map { updated_id =>
            EventDataType.PushEventDataType.UpdatedProspectsEventData(
              updated_id = updated_id,
              oldProspectDeduplicationColumn = msg.oldProspectDeduplicationColumn,
              ownerAccountId = msg.accountId,
              teamId = msg.teamId,
              triggerPath = msg.triggerPath
            )
          }
        )


      case EventType.EMAIL_OPENED |
        EventType.CREATED_OR_UPDATED_PROSPECT_IN_SMARTREACH |
        EventType.HUBSPOT_PROSPECT_SYNC |
        EventType.ZOHO_PROSPECT_SYNC |
        EventType.ZOHO_LEAD_SYNC |
        EventType.ZOHO_RECRUIT_PROSPECT_SYNC |
        EventType.ZOHO_RECRUIT_CANDIDATE_SYNC |
        EventType.PIPEDRIVE_PROSPECT_SYNC |
        EventType.SALESFORCE_PROSPECT_SYNC |
        EventType.SALESFORCE_LEAD_SYNC |
        EventType.EMAIL_SENT |
        EventType.EMAIL_BOUNCED |
        EventType.EMAIL_LINK_CLICKED |
        EventType.EMAIL_INVALID |
        EventType.NEW_REPLY |
        EventType.AUTO_REPLY |
        EventType.OUT_OF_OFFICE_REPLY |
        EventType.PROSPECT_OPTED_OUT |
        EventType.PROSPECT_COMPLETED |
        EventType.ANY_EMAIL_ACTIVITY |
        EventType.ADD_TO_DO_NOT_CONTACT |
        EventType.AUTO_LINKEDIN_TASK_COMPLETED |
        EventType.AUTO_LINKEDIN_TASK_FAILED |
        EventType.REPLY_SENTIMENT_UPDATED |
        EventType.TASK_REPLY_SENTIMENT_UPDATED |
        EventType.TEAM_MEMBER_ASSIGNED |
        /*
         * Date: 08-Apr-2024, fixme - we need to move these events to CRM flow
        */
        EventType.EVENT_V3_PROSPECT_OPTED_OUT |
        EventType.EVENT_V3_PROSPECT_COMPLETED |
        EventType.EVENT_V3_PROSPECT_ADDED |
        EventType.EVENT_V3_PROSPECT_REMOVED |
        EventType.EVENT_V3_PROSPECT_PAUSED |
        EventType.PROSPECT_CREATED |
        EventType.CALENDAR_MEETING_BOOKED |
        EventType.CALL_PLACED |
        EventType.CALL_RECEIVED |
        EventType.MARKED_REPLIED_MANUALLY_BY_ADMIN |
        EventType.PROSPECT_ACCOUNT_ADDED |
        EventType.TASK_CREATED |
        EventType.INTERNAL_TASK_CREATED |
        EventType.TASK_DUE |
        EventType.TASK_SNOOZED |
        EventType.TASK_DONE |
        EventType.TASK_SKIPPED |
        EventType.TASK_ARCHIVED |
        EventType.NOTE_ADDED |
        EventType.MERGED_DUPLICATES =>

        Failure(new Throwable("Wrong EVENT TYPE"))

    }

  }

  def getEventDataTypes(msg: MqActivityTriggerMsgFormBase)(using Logger: SRLogger) = {

    EventType.fromKey(msg.event).flatMap {

      case EventType.PROSPECT_OPTED_OUT =>
        Success(msg.pids.map(prospectId => {
          EventDataType.ActivityEventDataType.ProspectOptedOutEventData(
            accountId = AccountId(msg.accountId),
            teamId = TeamId(msg.teamId),
            campaignId = msg.campaignId.map(cid => CampaignId(cid)),
            prospect_id = prospectId,
            campaignName = msg.campaignName.map(cname => CampaignName(cname))
          )
        }))

      case EventType.NEW_REPLY =>
        /*
         * Date: 30-Dec-2024
         *
         * REPLY can be marked only by admin or system tracked
         * If admin marked the replied, then we don't contain the emailScheduledIdSeq
         * If system tracked the replied, then we contain the emailScheduledIdSeq
         *
         * Currently we are calling this EventDataType with help of MQActivityTriggerMsgForm : where either of emailScheduledIdSeq or prospectIdSeq is empty
         * If Both values are present, then we will get a fatal error
         */

        Success {

          if (msg.pids.nonEmpty && msg.emailScheduledIds.exists(_.nonEmpty)) {
            Logger.fatal(s"CRITICAL getEventDataFromMQActivityTriggerMsg EventType.NEW_REPLY both prospectIdSeq and emailScheduledIdSeq are nonEmpty :: prospectIdSeq : ${msg.pids} :: emailScheduledIdSeq : ${msg.emailScheduledIds.get}")
          }

          if (msg.emailScheduledIds.exists(_.nonEmpty)){
            msg.emailScheduledIds.get.map(emailScheduledId =>
              EventDataType.ActivityEventDataType.NewReplyEventData(
                email_scheduled_id = Some(emailScheduledId),
                accountId = AccountId(msg.accountId),
                teamId = TeamId(msg.teamId),
                campaignId = msg.campaignId.map(cid => CampaignId(cid)),
                prospectId = None,
                campaignName = msg.campaignName.map(cname => CampaignName(cname))
              )
            )
          } else {
            msg.pids.map(prospectId =>
              EventDataType.ActivityEventDataType.NewReplyEventData(
                email_scheduled_id = None,
                accountId = AccountId(msg.accountId),
                teamId = TeamId(msg.teamId),
                campaignId = msg.campaignId.map(cid => CampaignId(cid)),
                prospectId = Some(prospectId),
                campaignName = msg.campaignName.map(cname => CampaignName(cname))
              )
            )
          }


        }

      case EventType.EMAIL_BOUNCED =>
        Success {
          val prospectIdSeq = msg.pids.map(prospectId =>
            EventDataType.ActivityEventDataType.EmailBouncedEventData(
              email_scheduled_id = None,
              accountId = AccountId(msg.accountId),
              teamId = TeamId(msg.teamId),
              campaignId = msg.campaignId.map(cid => CampaignId(cid)),
              prospectId = Some(prospectId),
              campaignName = msg.campaignName.map(cname => CampaignName(cname))
            )
          )

          val emailScheduledIdSeq = msg.emailScheduledIds.map(emailScheduledIds =>
            emailScheduledIds.map(emailScheduledId =>
              EventDataType.ActivityEventDataType.EmailBouncedEventData(
                email_scheduled_id = Some(emailScheduledId),
                accountId = AccountId(msg.accountId),
                teamId = TeamId(msg.teamId),
                campaignId = msg.campaignId.map(cid => CampaignId(cid)),
                prospectId = None,
                campaignName = msg.campaignName.map(cname => CampaignName(cname))
              )
            )

          ).getOrElse(Seq())

          if (prospectIdSeq.nonEmpty && emailScheduledIdSeq.nonEmpty) {
            Logger.fatal(s"CRITICAL getEventDataFromMQActivityTriggerMsg EventType.EMAIL_BOUNCED both prospectIdSeq and emailScheduledIdSeq are nonEmpty :: prospectIdSeq : $prospectIdSeq :: emailScheduledIdSeq : $emailScheduledIdSeq")
          }
          prospectIdSeq ++ emailScheduledIdSeq

        }

      case EventType.EMAIL_SENT =>
        Success(
          Seq(EventDataType.ActivityEventDataType.EmailSentEventData(
            accountId = AccountId(msg.accountId),
            teamId = TeamId(msg.teamId),
            email_scheduled_id = msg.emailScheduledIds.get.headOption.get,
            prospect_id = msg.pids.headOption.get,
            campaignId = msg.campaignId.map(cid => CampaignId(cid)),
            campaignName = msg.campaignName.map(cname => CampaignName(cname))
          ))
        )

      case EventType.AUTO_REPLY =>
        Success{
          msg.emailScheduledIds.map(emailScheduledIds =>
            emailScheduledIds.map(emailScheduledId =>
              EventDataType.ActivityEventDataType.AutoReplyEventData(
                email_scheduled_id = emailScheduledId,
                accountId = AccountId(msg.accountId),
                teamId = TeamId(msg.teamId),
              )
            )

          ).getOrElse(Seq())
        }

      case EventType.OUT_OF_OFFICE_REPLY =>
        Success {
          msg.emailScheduledIds.map(emailScheduledIds =>
            emailScheduledIds.map(emailScheduledId =>
              EventDataType.ActivityEventDataType.OutOfOfficeReplyEventData(
                email_scheduled_id = emailScheduledId,
                accountId = AccountId(msg.accountId),
                teamId = TeamId(msg.teamId),
              )
            )

          ).getOrElse(Seq())
        }
      case EventType.PROSPECT_COMPLETED =>
        Success(msg.pids.map( prospectId =>
          EventDataType.ActivityEventDataType.ProspectCompletedEventData(
            prospect_id = prospectId,
            accountId = AccountId(msg.accountId),
            teamId = TeamId(msg.teamId),
            campaignId = msg.campaignId.map(cid => CampaignId(cid)),
            campaignName = msg.campaignName.map(cname => CampaignName(cname))
          )
        ))

      case EventType.EMAIL_LINK_CLICKED =>

        msg.emailScheduledIds match {
          case None =>
            Logger.fatal(s"No EmailScheduledId sent msg : $msg")
            Success(Seq())
          case Some(seq) =>

            seq.headOption match {
              case None =>
                Logger.fatal(s"No EmailScheduledId sent msg : $msg")
                Success(Seq())
              case Some(emailScheduledId) =>
                Success(msg.pids.map(prospectId =>
                  EventDataType.ActivityEventDataType.EmailLinkClickedEventData(
                    prospectId = prospectId,
                    accountId = AccountId(msg.accountId),
                    teamId = TeamId(msg.teamId),
                    campaignId = msg.campaignId.map(cid => CampaignId(cid)),
                    campaignName = msg.campaignName.map(cname => CampaignName(cname)),
                    clickedUrl = msg.clickedUrl.get,
                    email_scheduled_id = emailScheduledId
                  )
                ))
            }
        }



      case EventType.EMAIL_OPENED =>
        msg.emailScheduledIds match {
          case None =>
            Logger.fatal(s"No EmailScheduledId sent msg : $msg")
            Success(Seq())
          case Some(value) =>
            value.headOption match {
              case None =>
                Logger.fatal(s"No EmailScheduledId sent msg : $msg")
                Success(Seq())
              case Some(emailScheduledId) =>
                Success(msg.pids.map(prospectId => {
                  EventDataType.ActivityEventDataType.EmailOpenEventData(
                    email_scheduled_id = emailScheduledId,
                    prospect_id =  prospectId,
                    teamId = TeamId(msg.teamId),
                    accountId = AccountId(msg.accountId),
                    campaignId = msg.campaignId.map(cid => CampaignId(cid)),
                    campaignName = msg.campaignName.map(cname => CampaignName(cname))
                  )
                }))
            }

        }
      case EventType.EMAIL_INVALID =>
        //TODO
        Success(msg.pids.map(prospectId =>
          EventDataType.ActivityEventDataType.EmailInvalidEventData(
            prospectId =  prospectId,
            teamId = TeamId(msg.teamId),
            accountId = AccountId(msg.accountId)
          )
        ))

      case EventType.REPLY_SENTIMENT_UPDATED =>
        Success(
          Seq(EventDataType.ActivityEventDataType.ReplySentimentUpdatedEventData.ReplySentimentUpdatedEventDataEmail(
            teamId = TeamId(msg.teamId),
            accountId = AccountId(msg.accountId),
            threadId = msg.threadId.get,
            replySentimentUuid = msg.replySentimentUuid.get
          ))

        )

      case EventType.TASK_REPLY_SENTIMENT_UPDATED =>
        Success(
          Seq(EventDataType.ActivityEventDataType.ReplySentimentUpdatedEventData.ReplySentimentUpdatedEventDataTask(
            teamId = TeamId(msg.teamId),
            accountId = AccountId(msg.accountId),
            prospectId = msg.pids.head,
            replySentimentUuid = msg.replySentimentUuid.get,
            taskId = msg.taskUuid.get
          ))

        )
      case EventType.PROSPECT_CATEGORY_UPDATED |
           EventType.CREATED_PROSPECT_IN_SMARTREACH |
           EventType.UPDATED_PROSPECT_IN_SMARTREACH |
           EventType.CREATED_OR_UPDATED_PROSPECT_IN_SMARTREACH |
           EventType.HUBSPOT_PROSPECT_SYNC |
           EventType.ZOHO_PROSPECT_SYNC |
           EventType.ZOHO_LEAD_SYNC |
           EventType.ZOHO_RECRUIT_PROSPECT_SYNC |
           EventType.ZOHO_RECRUIT_CANDIDATE_SYNC |
           EventType.PIPEDRIVE_PROSPECT_SYNC |
           EventType.SALESFORCE_PROSPECT_SYNC |
           EventType.SALESFORCE_LEAD_SYNC |
           EventType.ANY_EMAIL_ACTIVITY |
           EventType.ADD_TO_DO_NOT_CONTACT |
           // We don't need to add a conversion for these events since we are directly pushing to the eventLogService.createEventLog rather than mqActivityTriggerService.publishEvents
           EventType.AUTO_LINKEDIN_TASK_COMPLETED |
           EventType.AUTO_LINKEDIN_TASK_FAILED |
           EventType.TEAM_MEMBER_ASSIGNED |
           EventType.TEAM_MEMBER_ASSIGNED |
           /*
            * Date: 08-Apr-2024, fixme - we need to move these events to CRM flow
           */
           EventType.EVENT_V3_PROSPECT_OPTED_OUT |
           EventType.EVENT_V3_PROSPECT_COMPLETED |
           EventType.EVENT_V3_PROSPECT_ADDED |
           EventType.EVENT_V3_PROSPECT_REMOVED |
           EventType.EVENT_V3_PROSPECT_PAUSED |
           EventType.PROSPECT_CREATED |
           EventType.CALENDAR_MEETING_BOOKED |
           EventType.CALL_PLACED |
           EventType.CALL_RECEIVED |
           EventType.MARKED_REPLIED_MANUALLY_BY_ADMIN |
           EventType.PROSPECT_ACCOUNT_ADDED |
           EventType.TASK_CREATED |
           EventType.INTERNAL_TASK_CREATED |
           EventType.TASK_DUE |
           EventType.TASK_SNOOZED |
           EventType.TASK_DONE |
           EventType.TASK_SKIPPED |
           EventType.TASK_ARCHIVED |
           EventType.NOTE_ADDED |
           EventType.MERGED_DUPLICATES =>
        Failure(new Throwable("Wrong EVENT TYPE"))

    }

  }


  def getEventDataFromMQActivityTriggerMsg(
                                            data: MqActivityTriggerMsgFormBase
                                          )(using Logger: SRLogger): Try[Seq[EventDataType.ActivityEventDataType]] = {

    getEventDataTypes(msg = data)

  }

  def getEventDataFromMQTriggerSyncMessage(
                                            triggerId: Long,
                                            eventType: EventType
                                          ): Try[EventDataType.SyncEventDataType] = {
    eventType match {
      case EventType.HUBSPOT_PROSPECT_SYNC =>
        Success(
          EventDataType.SyncEventDataType.HubspotProspectSyncEventData(
            triggerId = triggerId
          )
        )
      case EventType.ZOHO_PROSPECT_SYNC =>
        Success(
          EventDataType.SyncEventDataType.ZohoProspectSyncEventData(
            triggerId = triggerId
          )
        )
      case EventType.ZOHO_LEAD_SYNC =>
        Success(
          EventDataType.SyncEventDataType.ZohoLeadSyncEventData(
            triggerId = triggerId
          )
        )
      case EventType.ZOHO_RECRUIT_PROSPECT_SYNC =>
        Success(
          EventDataType.SyncEventDataType.ZohoRecruitProspectSyncEventData(
            triggerId = triggerId
          )
        )
      case EventType.ZOHO_RECRUIT_CANDIDATE_SYNC =>
        Success(
          EventDataType.SyncEventDataType.ZohoRecruitCandidateSyncEventData(
            triggerId = triggerId
          )
        )
      case EventType.PIPEDRIVE_PROSPECT_SYNC =>
        Success(
          EventDataType.SyncEventDataType.PipedriveProspectSyncEventData(
            triggerId = triggerId
          )
        )
      case EventType.SALESFORCE_PROSPECT_SYNC =>
        Success(
          EventDataType.SyncEventDataType.SalesforceProspectSyncEventData(
            triggerId = triggerId
          )
        )
      case EventType.SALESFORCE_LEAD_SYNC =>
        Success(
          EventDataType.SyncEventDataType.SalesforceLeadSyncEventData(
            triggerId = triggerId
          )
        )
      case EventType.PROSPECT_OPTED_OUT |
           EventType.NEW_REPLY |
           EventType.EMAIL_BOUNCED |
           EventType.EMAIL_SENT |
           EventType.AUTO_REPLY |
           EventType.OUT_OF_OFFICE_REPLY |
           EventType.PROSPECT_COMPLETED |
           EventType.EMAIL_LINK_CLICKED |
           EventType.EMAIL_OPENED |
           EventType.EMAIL_INVALID |
           EventType.PROSPECT_CATEGORY_UPDATED |
           EventType.CREATED_PROSPECT_IN_SMARTREACH |
           EventType.UPDATED_PROSPECT_IN_SMARTREACH |
           EventType.CREATED_OR_UPDATED_PROSPECT_IN_SMARTREACH |
           EventType.ANY_EMAIL_ACTIVITY |
           EventType.ADD_TO_DO_NOT_CONTACT |
           EventType.AUTO_LINKEDIN_TASK_COMPLETED |
           EventType.AUTO_LINKEDIN_TASK_FAILED |
           EventType.REPLY_SENTIMENT_UPDATED |
           EventType.TASK_REPLY_SENTIMENT_UPDATED |
           EventType.TEAM_MEMBER_ASSIGNED |
           EventType.TEAM_MEMBER_ASSIGNED |
           /*
            * Date: 08-Apr-2024, fixme - we need to move these events to CRM flow
           */
           EventType.EVENT_V3_PROSPECT_OPTED_OUT |
           EventType.EVENT_V3_PROSPECT_COMPLETED |
           EventType.EVENT_V3_PROSPECT_ADDED |
           EventType.EVENT_V3_PROSPECT_REMOVED |
           EventType.EVENT_V3_PROSPECT_PAUSED |
           EventType.PROSPECT_CREATED |
           EventType.CALENDAR_MEETING_BOOKED |
           EventType.CALL_PLACED |
           EventType.CALL_RECEIVED |
           EventType.MARKED_REPLIED_MANUALLY_BY_ADMIN |
           EventType.PROSPECT_ACCOUNT_ADDED |
           EventType.TASK_CREATED |
           EventType.INTERNAL_TASK_CREATED |
           EventType.TASK_DUE |
           EventType.TASK_SNOOZED |
           EventType.TASK_DONE |
           EventType.TASK_SKIPPED |
           EventType.TASK_ARCHIVED|
           EventType.NOTE_ADDED |
           EventType.MERGED_DUPLICATES =>
        Failure(new Throwable("Wrong EVENT TYPE"))

    }
  }
//
//  def getMqMsgFromEventDataType(
//                                 event_data_type: EventDataType,
//                                 crmType:  IntegrationType,
//                                 attempt_data: List[AttemptData]
//                               ): Option[MQMsg] = {
//
//    event_data_type match {
//
//      case data: api.sr_audit_logs.models.EventDataType.CreatedProspectsEventData =>
//
//        Some(MQPushTriggerMsg(
//          integrationType = crmType,
//          accountId = data.ownerAccountId,
//          teamId = data.teamId,
//          prospectIds = Seq(data.created_id),
//          updatedProspectCategoryId = None,
//          event = EventType.CREATED_PROSPECT_IN_SMARTREACH.toString,
//          attempt_data = attempt_data
//        ))
//
//      case data: api.sr_audit_logs.models.EventDataType.UpdatedProspectsEventData =>
//        Some(MQPushTriggerMsg(
//          integrationType = crmType,
//          accountId = data.ownerAccountId,
//          teamId = data.teamId,
//          prospectIds = Seq(data.updated_id),
//          updatedProspectCategoryId = None,
//          event = EventType.UPDATED_PROSPECT_IN_SMARTREACH.toString,
//          attempt_data = attempt_data
//        ))
//
//      case data: api.sr_audit_logs.models.EventDataType.EmailOpenEventData =>
//        Some(MQActivityTriggerMsg(
//          accountId = data.accountId.id, // FIXME VALUECLASS
//          teamId = data.teamId.id, // FIXME VALUECLASS
//          campaignId = data.campaignId.map(_.id),
//          prospectIds = Seq(data.prospect_id.id),
//          clickedUrl = None,
//          campaignName = data.campaignName.map(_.name),
//          emailScheduledIds = Some(Seq(data.email_scheduled_id)),
//          event = EventType.EMAIL_OPENED.toString,
//          attempt_data = attempt_data
//        ))
//
//      case data: api.sr_audit_logs.models.EventDataType.UpdateProspectCategoryEventData =>
//        Some(MQPushTriggerMsg(
//          integrationType = crmType,
//          accountId = data.doerAccountId,
//          teamId = data.teamId,
//          prospectIds = Seq(data.prospectId),
//          updatedProspectCategoryId = Some(data.newProspectCategoryIdCustom),
//          event = EventType.PROSPECT_CATEGORY_UPDATED.toString,
//          attempt_data = attempt_data
//        ))
//
//      case data: api.sr_audit_logs.models.EventDataType.CreateOrUpdatedProspectsEventData =>
//        None
//
//      case data: api.sr_audit_logs.models.EventDataType.HubspotProspectSyncEventData =>
//        Some(MQTriggerSyncMessage(triggerId = data.triggerId, attempt_data = attempt_data))
//
//      case data: api.sr_audit_logs.models.EventDataType.ZohoProspectSyncEventData =>
//        Some(MQTriggerSyncMessage(triggerId = data.triggerId, attempt_data = attempt_data))
//
//      case data: api.sr_audit_logs.models.EventDataType.ZohoLeadSyncEventData =>
//        Some(MQTriggerSyncMessage(triggerId = data.triggerId, attempt_data = attempt_data))
//
//      case data: api.sr_audit_logs.models.EventDataType.ZohoRecruitProspectSyncEventData =>
//        Some(MQTriggerSyncMessage(triggerId = data.triggerId, attempt_data = attempt_data))
//
//      case data: api.sr_audit_logs.models.EventDataType.ZohoRecruitCandidateSyncEventData =>
//        Some(MQTriggerSyncMessage(triggerId = data.triggerId, attempt_data = attempt_data))
//
//      case data: api.sr_audit_logs.models.EventDataType.PipedriveProspectSyncEventData =>
//        Some(MQTriggerSyncMessage(triggerId = data.triggerId, attempt_data = attempt_data))
//
//      case data: api.sr_audit_logs.models.EventDataType.SalesforceProspectSyncEventData =>
//        Some(MQTriggerSyncMessage(triggerId = data.triggerId, attempt_data = attempt_data))
//
//      case data: api.sr_audit_logs.models.EventDataType.SalesforceLeadSyncEventData =>
//        Some(MQTriggerSyncMessage(triggerId = data.triggerId, attempt_data = attempt_data))
//
//
//      case data: api.sr_audit_logs.models.EventDataType.EmailSentEventData =>
//        Some(MQActivityTriggerMsg(
//          accountId = data.accountId.id, // FIXME VALUECLASS
//          teamId = data.teamId.id, // FIXME VALUECLASS
//          campaignId = None,
//          prospectIds = Seq(data.prospect_id.id),
//          clickedUrl = None,
//          campaignName = None,
//          emailScheduledIds = Some(Seq(data.email_scheduled_id)),
//          event = EventType.EMAIL_SENT.toString,
//          attempt_data = attempt_data
//        ))
//
//      case data: api.sr_audit_logs.models.EventDataType.EmailBouncedEventData =>
//        Some(MQActivityTriggerMsg(
//          accountId = data.accountId.id, // FIXME VALUECLASS
//          teamId = data.teamId.id, // FIXME VALUECLASS
//          campaignId = data.campaignId.map(_.id),
//          prospectIds = data.prospectId.map(pid => Seq(pid.id)).getOrElse(Seq()), // FIXME VALUECLASS
//          clickedUrl = None,
//          campaignName = data.campaignName.map(_.name),
//          emailScheduledIds = data.email_scheduled_id.map(esid => Seq(esid)),
//          event = EventType.EMAIL_BOUNCED.toString,
//          attempt_data = attempt_data
//        ))
//
//      case data: api.sr_audit_logs.models.EventDataType.EmailLinkClickedEventData =>
//        Some(MQActivityTriggerMsg(
//          accountId = data.accountId.id, // FIXME VALUECLASS
//          teamId = data.teamId.id, // FIXME VALUECLASS
//          campaignId = data.campaignId.map(_.id),
//          prospectIds = Seq(data.prospectId.id), // FIXME VALUECLASS
//          clickedUrl = Some(data.clickedUrl),
//          campaignName = data.campaignName.map(_.name),
//          emailScheduledIds = Some(Seq(data.email_scheduled_id)),
//          event = EventType.EMAIL_LINK_CLICKED.toString,
//          attempt_data = attempt_data
//        ))
//
//      case data: api.sr_audit_logs.models.EventDataType.EmailInvalidEventData =>
//        Some(MQActivityTriggerMsg(
//          accountId = data.accountId.id, // FIXME VALUECLASS
//          teamId = data.teamId.id, // FIXME VALUECLASS
//          campaignId = None,
//          prospectIds = Seq(data.prospectId.id), // FIXME VALUECLASS
//          clickedUrl = None,
//          campaignName = None,
//          emailScheduledIds = None,
//          event = EventType.EMAIL_INVALID.toString,
//          attempt_data = attempt_data
//        ))
//
//      case data: api.sr_audit_logs.models.EventDataType.NewReplyEventData =>
//        Some(MQActivityTriggerMsg(
//          accountId = data.accountId.id, // FIXME VALUECLASS
//          teamId = data.teamId.id, // FIXME VALUECLASS
//          campaignId = data.campaignId.map(_.id),
//          prospectIds = data.prospectId.map(pid => Seq(pid.id)).getOrElse(Seq()), // FIXME VALUECLASS
//          clickedUrl = None,
//          campaignName = data.campaignName.map(_.name),
//          emailScheduledIds = data.email_scheduled_id.map(esid => Seq(esid)),
//          event = EventType.NEW_REPLY.toString,
//          attempt_data = attempt_data
//        ))
//
//
//      case data: api.sr_audit_logs.models.EventDataType.AutoReplyEventData =>
//        Some(MQActivityTriggerMsg(
//          accountId = data.accountId.id, // FIXME VALUECLASS
//          teamId = data.teamId.id, // FIXME VALUECLASS
//          campaignId = None,
//          prospectIds = Seq(),
//          clickedUrl = None,
//          campaignName = None,
//          emailScheduledIds = Some(Seq(data.email_scheduled_id)),
//          event = EventType.AUTO_REPLY.toString,
//          attempt_data = attempt_data
//        ))
//
//
//      case data: api.sr_audit_logs.models.EventDataType.OutOfOfficeReplyEventData =>
//        Some(MQActivityTriggerMsg(
//          accountId = data.accountId.id, // FIXME VALUECLASS
//          teamId = data.teamId.id, // FIXME VALUECLASS
//          campaignId = None,
//          prospectIds = Seq(),
//          clickedUrl = None,
//          campaignName = None,
//          emailScheduledIds = Some(Seq(data.email_scheduled_id)),
//          event = EventType.OUT_OF_OFFICE_REPLY.toString,
//          attempt_data = attempt_data
//        ))
//
//      case data: api.sr_audit_logs.models.EventDataType.ProspectOptedOutEventData =>
//        Some(MQActivityTriggerMsg(
//          accountId = data.accountId.id, // FIXME VALUECLASS
//          teamId = data.teamId.id, // FIXME VALUECLASS
//          campaignId = data.campaignId.map(_.id),
//          prospectIds = Seq(data.prospect_id.id), // FIXME VALUECLASS
//          clickedUrl = None,
//          campaignName = data.campaignName.map(_.name),
//          emailScheduledIds = None,
//          event = EventType.PROSPECT_OPTED_OUT.toString,
//          attempt_data = attempt_data
//        ))
//
//      case data: api.sr_audit_logs.models.EventDataType.ProspectCompletedEventData =>
//        Some(MQActivityTriggerMsg(
//          accountId = data.accountId.id, // FIXME VALUECLASS
//          teamId = data.teamId.id, // FIXME VALUECLASS
//          campaignId = data.campaignId.map(_.id),
//          prospectIds = Seq(data.prospect_id.id), // FIXME VALUECLASS
//          clickedUrl = None,
//          campaignName = data.campaignName.map(_.name),
//          emailScheduledIds = None,
//          event = EventType.PROSPECT_COMPLETED.toString,
//          attempt_data = attempt_data
//        ))
//
//      case data: api.sr_audit_logs.models.EventDataType.AnyEmailActivityEventData =>
//        None
//
//      case data: api.sr_audit_logs.models.EventDataType.AddToDoNotContact =>
//        Some(MQAddToDNCMessage(
//          team_id = data.team_id.id,
//          module_id = data.module_id,
//          attempt_data = attempt_data
//        ))
//
//    }
//
//  }
}
