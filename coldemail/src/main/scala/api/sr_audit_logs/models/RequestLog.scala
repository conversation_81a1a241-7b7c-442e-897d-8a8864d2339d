package api.sr_audit_logs.models

import org.joda.time.DateTime


sealed trait CreateRequestLogError

object CreateRequestLogError {

  case class SQLException(error: Throwable) extends CreateRequestLogError

  case class InValidRequestMethod(error: Throwable) extends CreateRequestLogError

  case class CreateRequestLogFailedInsert(message: String) extends CreateRequestLogError

}

sealed trait GetRequestLogsError

object GetRequestLogsError {

  case class SQLException(error: Throwable) extends GetRequestLogsError

}

case class RequestLog(
                       audit_request_log_id: String,
                       request_body: String,
                       request_ip: String,
                       request_end_point: String,
                       request_method: APIRequestMethod,
                       request_version: String,
                       request_origin: String,
                       response_body: String,
                       response_http_status: Int,
                       team_id: Option[Long],
                       account_id: Option[Long],
                       actor_name: String,
                       created_at: DateTime
                     )