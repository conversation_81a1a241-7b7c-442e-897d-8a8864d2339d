package api.sr_audit_logs.models

import api.prospects.models.ProspectId
import eventframework.ProspectObject


case class ProspectIdWithOldProspectDeduplicationColumn(
                                                       prospectId: ProspectId,
                                                       oldProspectDeduplicationColumn: Option[OldProspectDeduplicationColumn]
                                                       // keeping it as Option as it is only required for the updateflow
                                                       )



case class ProspectObjectWithOldProspectDeduplicationColumn(
                                                         prospectObject: ProspectObject,
                                                         oldProspectDeduplicationColumn: Option[OldProspectDeduplicationColumn]
                                                         // keeping it as Option as it is only required for the updateflow
                                                       )
