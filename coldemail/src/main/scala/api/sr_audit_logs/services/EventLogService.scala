package api.sr_audit_logs.services

import api.accounts.TeamId
import api.sr_audit_logs.dao.EventLogDAO
import api.sr_audit_logs.models.{CreateEventLogError, EventDataType, EventLog, EventType, GetEventLogsError}
import org.joda.time.DateTime

import scala.util.{Failure, Random, Success, Try}
import utils.{Help<PERSON>, SRLogger}
import utils.mq.webhook.handle_event_log.MqHandleEventLogPublisher
import utils.uuid.SrUuidUtils


case class MqHandleEventLogMessage(event_log_id: String, team_id: Long)

class EventLogService(
                       eventLogDAO: EventLogDAO,
                       mqHandleEventLogPublisher: MqHandleEventLogPublisher,
                       srUuidUtils: SrUuidUtils
                     ) {

  /**
   * @return ex: evt_1642675705646_42_24_4WNxCJjZqU
   */
  def generateEventLogId(teamId: Long, accountId: Long): String = {
    val preFix = "evt"
    val mills = DateTime.now().getMillis
    val rand = Random.alphanumeric.take(10).mkString
    val eventId = s"${preFix}_${mills}_${accountId}_${teamId}_$rand"
    eventId
  }

  /*
  *  Called from 5 different paths:
  *   ProspectUpdateCategoryTemp2._updateCategoryDB
  *   IntegrationTriggerSyncCronService.publishAddToDNCFromCRMToSR
  *   MQActivityTriggerService.processMessage
  *   MQTrigger.processMessage
  *   SyncTriggerMQService.publishSyncTrigger */
  def createEventLog(
                     event_data_type: EventDataType,
                     teamId: Long,
                     accountId: Long,
                     created_at: DateTime = DateTime.now()
                    )(using Logger: SRLogger):Either[CreateEventLogError, String] = {

    val audit_request_log_id = srUuidUtils.generateMqRequestLogId()
    val event_log_id = generateEventLogId(teamId = teamId, accountId = accountId)
    val eventLog = EventLog(
      event_log_id = event_log_id,
      audit_request_log_id = audit_request_log_id,
      event_data_type = event_data_type,
      team_id = teamId,
      account_id = accountId,
      created_at = created_at
    )

     val addEventAndPushToQueue = for {

      addEvent: Option[String] <- eventLogDAO.insertEventLogInDB(data = eventLog)

      pushEventToQueue: Unit <- mqHandleEventLogPublisher.publish(msg = MqHandleEventLogMessage(event_log_id = event_log_id, team_id = teamId))

    } yield {

      addEvent

    }

    addEventAndPushToQueue match {
       case Failure(e) =>
         Logger.fatal("Failed to add event to the queue", e)
         Left(CreateEventLogError.SQLException(error = e))
       case Success(None) =>
         Logger.fatal("No event found after creating")

         Left(CreateEventLogError.CreateEventLogFailedInsert(message = "CreateEventLogFailedInsert"))
       case Success(Some(value)) =>
         Right(value)
     }

  }

  def createEventLogBatch(
                           event_data_type: Seq[EventDataType],
                           teamId: Long,
                           accountId: Long,
                           created_at: DateTime = DateTime.now()
                         )(using Logger: SRLogger): Either[CreateEventLogError, Seq[String]] = {

    val eventLogs: Seq[EventLog] = event_data_type.map(edt => {
      val audit_request_log_id = srUuidUtils.generateMqRequestLogId()
      val event_log_id = generateEventLogId(teamId = teamId, accountId = accountId)
      val eventLog = EventLog(
        event_log_id = event_log_id,
        audit_request_log_id = audit_request_log_id,
        event_data_type = edt,
        team_id = teamId,
        account_id = accountId,
        created_at = created_at
      )

      eventLog
    })


    val addEventsAndPushToQueue: Try[Seq[String]] = for {

      addEvents: Seq[String] <- {
        val inserted_events = eventLogs.grouped(100)
          .toSeq
          .map(eventLogs => {
            eventLogDAO.insertEventLogBatchInDB(data = eventLogs)
          })

        Helpers.seqTryToTrySeq(inserted_events).map(_.flatten)
      }

      pushEventToQueue: Seq[Unit] <- {
        val res = addEvents.map(event_log_id => {
          mqHandleEventLogPublisher.publish(msg = MqHandleEventLogMessage(event_log_id = event_log_id, team_id = teamId))
        })
        Helpers.seqTryToTrySeq(res)
      }

    } yield {

      addEvents

    }

    addEventsAndPushToQueue match {
      case Failure(e) =>
        Logger.fatal("Failed to add event to the queue", e)
        Left(CreateEventLogError.SQLException(error = e))
      case Success(res) =>
        if(event_data_type.nonEmpty && res.isEmpty) {
          Left(CreateEventLogError.CreateEventLogFailedInsert(message = "CreateEventLogFailedInsert"))
        } else {
          Right(res)
        }
    }

  }


  def getEventLogs(
                      teamId: Long,
                      accountId: Long,
                      audit_request_log_id: Option[String],
                      event_log_id: Option[String],
                      created_greatar_than: Option[Long],
                      event_type: Option[EventType],
                      page: Int
                    ): Either[GetEventLogsError, Seq[EventLog]] = {


    for {
      eventLogs <- eventLogDAO.findEventLogs(
          teamId = teamId,
          accountId = accountId,
          audit_request_log_id = audit_request_log_id,
          event_log_id = event_log_id,
          created_greater_than = created_greatar_than,
          event_type = event_type,
          page = page
        )
        .toEither
        .left.map(e => GetEventLogsError.SQLException(error = e))
    } yield {
      eventLogs
    }
  }
  
  def updatePushedForAttemptCreationAt(
                                      teamId: TeamId,
                                      event_log_id: String
                                      ): Try[String] = {
    eventLogDAO.updatePushedForAttemptCreationAt(
      teamId = teamId, event_log_id = event_log_id
    )
  }

}

