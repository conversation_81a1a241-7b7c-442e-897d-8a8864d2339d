package api.sr_audit_logs.services

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.sr_audit_logs.models.{AttemptLogId, AttemptLogsFoundForTryOrRetry, AttemptStatus, EventDataType, EventType, WorkflowAttemptInternalServerError, WorkflowAttemptTryErrorReason, WorkflowSettingData}
import api.triggers.{IntegrationModuleType, IntegrationType, Trigger}
import play.api.libs.ws.WSClient
import utils.{Helpers, SRLogger}
import utils.mq.webhook.{HandleActivityTriggerEventService, HandleAddToDNCTriggerEventService, HandlePushTriggerEventService, HandleSyncTriggerEventService}
import api.accounts.TeamId
import api.accounts.models.AccountId
import api.sr_audit_logs.models.EventDataType.PushEventDataType
import org.joda.time.{DateTime, Duration}
import utils.cronjobs.AttemptRetryCronService

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success, Try}
import scala.concurrent.Future


class ProcessAttemptService(
                             workFlowAttemptService: WorkFlowAttemptService,
                             handleActivityTriggerEventService: HandleActivityTriggerEventService,
                             handlePushTriggerEventService: HandlePushTriggerEventService,
                             triggerDAO: Trigger,
                             handleSyncTriggerEventService: HandleSyncTriggerEventService,
                             handleAddToDNCTriggerEventService: HandleAddToDNCTriggerEventService,
                             workflowJedisService: WorkflowJedisService
                           ) {


  def processSingleAttempt(
                            atmpt: AttemptLogsFoundForTryOrRetry[WorkflowSettingData]

                          )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem, logger: SRLogger): Future[Either[WorkflowAttemptTryErrorReason, Seq[Int]]] = {

    // Check if CRM is rate-limted already
    workflowJedisService.checkLockForRateLimit(lockKey = atmpt.attempt_setting.workflow_crm_setting_id.toString) match {
      case Failure(exception) =>
        //since this is an error on the smartreach side and something we are not supposed to add to the db as an error we are sending it to the MQ so the attempt stays in the queue
        Future.failed(exception)

      case Success(isLocked) =>
        if (isLocked) {
          // crm is rate-limited
          if (AppConfig.Debug_Logs_For_Teams.allowLogging(teamId = 16247)) {
            logger.debug("Crm is rate Limited ")
          }
          Future.successful(Left(WorkflowAttemptTryErrorReason.TooManyRequestsError(s"Workflow is in timeout for wid_${atmpt.attempt_setting.workflow_crm_setting_id}")))
        } else {

          val result: Future[Either[WorkflowAttemptTryErrorReason, Seq[Int]]] = workFlowAttemptService.getSingleMsgSetForProcessing(
            atmpt = atmpt,
            logger = logger
          ) match {

            case Failure(err) => Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.SqlException(err))))
            case Success(listOfEventAndAttemptData) =>

              // if we are at this point, we can remove the workflow_setting_id from the set for RateLimit
              //FIXME: we dont need set for either of the lock anymore, we need to remove the lock and set, and just use lock
              workflowJedisService.removeFromHashMapForRateLimit(itemsToRemoveFromSet = Set(atmpt.attempt_setting.workflow_crm_setting_id.toString))

              if (listOfEventAndAttemptData.length == 1) {

                listOfEventAndAttemptData.head.event_data match {
                  case data: EventDataType.PushEventDataType =>
                    processPushActivity(atmpt = atmpt, message = List(data))
                  case data: EventDataType.ActivityEventDataType =>
                    processActivityAttempt(
                      message = data,
                      crm_type = atmpt.attempt_setting.crm_type,
                      module_type = atmpt.attempt_setting.module_type,
                      event = atmpt.eventType,
                    )
                  case data: EventDataType.SyncEventDataType => handleSyncTriggerEventService.processEventMessageForNewAuditFlow(data.triggerId)
                  case data: EventDataType.AddToDoNotContact => processAddToDNCActivity(data.team_id.id, data.module_id)
                  case data: EventDataType.EventDataTypeV3 => Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.WrongProcessType("Wrong Type of ProcessType"))))
                }
              } else {
                var pushProcessDataType: List[PushEventDataType] = List()
                val getBatchData = listOfEventAndAttemptData.map(_.event_data).map {
                  case data: PushEventDataType =>
                    if (EventType.isBatch(data.eventType)) {
                      if (!atmpt.attempt_setting.crm_type.batchUpdateSupported) {
                        logger.shouldNeverHappen(s"getting more than one attempts for a CRM that doesnt support batch updates")
                      }
                      pushProcessDataType = pushProcessDataType ++ List(data)
                      Right({})
                    } else {
                      Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.WrongProcessType("Wrong Type of ProcessType")))
                    }

                  case _: EventDataType.ActivityEventDataType =>
                    Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.WrongProcessType("Wrong Type of ProcessType")))

                  case _: EventDataType.SyncEventDataType =>
                    Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.WrongProcessType("Wrong Type of ProcessType")))

                  case _: EventDataType.AddToDoNotContact =>
                    Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.WrongProcessType("Wrong Type of ProcessType")))

                  case _: EventDataType.EventDataTypeV3 =>
                    Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.WrongProcessType("Wrong Type of ProcessType")))

                }

                Helpers.seqEitherToEitherSeq(xs = getBatchData) match {
                  case Left(err) => Future.successful(Left(err))
                  case Right(_) =>

                    processPushActivity(
                      atmpt = atmpt,
                      message = pushProcessDataType
                    )
                }


              }

          }


          result.map {
            case Left(err) =>

              atmpt.attempt_data.map { attempt_data =>
                sideEffectsOfWorkflowAttempt(
                  workflowAttemptTryErrorReason = Some(err),
                  attempt_log_id = attempt_data.attempt_log_id,
                  teamId = atmpt.team_id,
                  accountId = atmpt.account_id,
                  Logger = logger,
                  attempt_retry_count = attempt_data.attempt_retry_count,
                  workflow_crm_setting_id = atmpt.attempt_setting.workflow_crm_setting_id
                )
              }

              Left(err)
            case Right(result) =>

              atmpt.attempt_data.map { attempt_data =>
                sideEffectsOfWorkflowAttempt(
                  workflowAttemptTryErrorReason = None,
                  attempt_log_id = attempt_data.attempt_log_id,
                  teamId = atmpt.team_id,
                  accountId = atmpt.account_id,
                  Logger = logger,
                  attempt_retry_count = attempt_data.attempt_retry_count,
                  workflow_crm_setting_id = atmpt.attempt_setting.workflow_crm_setting_id
                )
              }
              Right(result)
          }.recover { e =>
            val err = WorkflowAttemptTryErrorReason.UnKnownError(Option(e.getMessage).getOrElse("UNKNOWN ERROR"))
            atmpt.attempt_data.map { attempt_data =>
              sideEffectsOfWorkflowAttempt(
                workflowAttemptTryErrorReason = Some(err),
                attempt_log_id = attempt_data.attempt_log_id,
                teamId = atmpt.team_id,
                accountId = atmpt.account_id,
                Logger = logger,
                attempt_retry_count = attempt_data.attempt_retry_count,
                workflow_crm_setting_id = atmpt.attempt_setting.workflow_crm_setting_id
              )
            }
            Left(err)
          }
        }
    }


  }

  def processActivityAttempt(message: EventDataType.ActivityEventDataType,
                             crm_type: IntegrationType,
                             module_type: IntegrationModuleType,
                             event: EventType,
                            )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem, srLogger: SRLogger): Future[Either[WorkflowAttemptTryErrorReason, Seq[Int]]] = {

    //    srLogger.debug(s"Step 3: Starting processActivityAttempt")

    triggerDAO.findIntegrationByTeamIdAndCRMTypeAndModuleType(
      team_id = message.teamId.id,
      crm_type = crm_type,
      module_type = module_type,
      logger = srLogger
    ) match {

      case Failure(e) =>

        srLogger.error(s"FATAL processAttemptForUpdateActivity findIntegrationsByTeamIdWhileRunnigTriggers error:", err = e)

        Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.SqlException(e))))

      case Success(None) =>

        /*
        FIXME AUDI_TRAIL
        Confirm what to do when not-found/deactivated integration after creating the attempt
        1. crete attempt_try with the error
        2. it should not retry again
         */

        srLogger.error(s"FATAL processAttemptForUpdateActivity findIntegrationsByTeamIdWhileRunnigTriggers integration not found, team_id : ${message.teamId} :: crm_type : ${crm_type.toString} :: module_type : $module_type")

        Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.IntegrationNotFound(s"team_id : ${message.teamId} :: crm_type : ${crm_type.toString} :: module_type : $module_type"))))

      case Success(Some(integration)) =>

        //        srLogger.debug(s"Step 4: Found Integration Going for handleActivityTriggerEventService.matchEventAndTriggerCRMNewFlow  crm : ${integration.crm} ::  module : ${integration.module}  ::  team_id : ${integration.team_id}")

        handleActivityTriggerEventService.matchEventAndTriggerCRMNewFlow(
          intg = integration,
          message = message,
          event = event
        )

    }


  }

  def processPushActivity(
                           atmpt: AttemptLogsFoundForTryOrRetry[WorkflowSettingData],
                           message: List[EventDataType.PushEventDataType],
                         )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem, Logger: SRLogger): Future[Either[WorkflowAttemptTryErrorReason, Seq[Int]]] = {
    triggerDAO.findIntegrationByTeamIdAndCRMType(team_id = atmpt.team_id.id, crm_type = atmpt.attempt_setting.crm_type, logger = Logger) match {

      case Failure(e) =>

        Logger.error(s"FATAL findIntegrations error:", err = e)

        Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.SqlException(e))))

      case Success(integrations) =>

        //B. check the found integrations not empty
        if (integrations.nonEmpty) {

          // C. create or update contact data in CRM
          //  1. check if global setting create_or_update_record_in_crm is enabled
          //  2. if global setting create_or_update_record_in_crm is not enabled run via old flow which is find workflows
          //  3. check is updated category event and global setting for category to status mapping is exists
          //  4. update lead status if mapping found for the category
          //  5. merging createOrUpdateContactInCRMF(Future[Seq[Int]]) and  update lead status (Future[Seq[Int]]) responses
          //  6. returning merged responses Future[Seq[Int]]

          //purpose is it prevents triggering findAllforMQProcess multiple time for each crm which found

          val result = integrations
            .map(integration => {
              handlePushTriggerEventService.findIntegrationsForInvokeNewFlowV2(
                event = atmpt.eventType,
                message = message,
                integration = integration,
                teamId = atmpt.team_id.id,
                accountId = atmpt.account_id.id
              )
            })

          Future.sequence(result).map(seqOfEither => Helpers.seqEitherToEitherSeq(seqOfEither).map(_.flatten))

        } else {

          Future(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.IntegrationNotFound(s"team_id : ${atmpt.team_id.id}, crm_type : ${atmpt.attempt_setting.crm_type}"))))

        }
    }
  }

  def processAddToDNCActivity(
                               team_id: Long,
                               module_id: Long
                             )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem, srLogger: SRLogger): Future[Either[WorkflowAttemptTryErrorReason, Seq[Int]]] = {

    //    srLogger.info("START")

    triggerDAO.findIntegrationByTeamIdAndModuleId(
      team_id = team_id,
      module_id = module_id,
      logger = srLogger
    ) match {

      case Failure(e) =>
        srLogger.fatal(s"triggerDAO.findIntegrationsByTeamIdAndModuleId error", err = e)

        Future(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.SqlException(e))))

      case Success(None) =>

        srLogger.fatal(s"triggerDAO.findIntegrationsByTeamIdAndModuleId none found")

        Future(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.IntegrationNotFound(s"team_id: ${team_id} module_id: ${module_id}"))))

      case Success(Some(integration)) =>
        handleAddToDNCTriggerEventService.processEventMessageNewFlow(integration = integration)
    }

  }


  def sideEffectsOfWorkflowAttempt(
                                    workflowAttemptTryErrorReason: Option[WorkflowAttemptTryErrorReason],
                                    attempt_log_id: AttemptLogId,
                                    teamId: TeamId,
                                    accountId: AccountId,
                                    Logger: SRLogger,
                                    workflow_crm_setting_id: Long,
                                    attempt_retry_count: Int
                                  ): Try[AttemptLogId] = {

    given srLogger: SRLogger = Logger // fixme given

    workflowAttemptTryErrorReason match {
      case None =>
        workFlowAttemptService.updateAttemptLogStatus(
          attempt_log_id = attempt_log_id, //FIXME VALUECLASS
          teamId = teamId,
          accountId = accountId,
          Logger = Logger
        )(
          attempt_status = AttemptStatus.SuccessAttempt,
          failure_reason = None,
          attempt_retry_count = attempt_retry_count
        ) match {
          case Right(_) =>
          //            Logger.info(s"STEP 12: Attempt Success attempt_log_id ${attempt_log_id}")
          case Left(value) =>
            Logger.fatal(s"STEP 12: Failed to add \"Attempt SUCCESS\" event for  attempt_log_id ${attempt_log_id} error $value")
        }
        triggerDAO.updateErrorCount(
          teamId = teamId,
          workflow_setting_id = workflow_crm_setting_id,
          total_consecutive_error_attempts = 0
        ) match {
          case Success(value) => //Do NOTHING
          case Failure(exception) => Logger.shouldNeverHappen(s"Failed triggerDAO.updateErrorCount", Some(exception))
        }
        Success(attempt_log_id)
      case Some(err) =>
        // After making a failed attempt, we decide if we want to pause the CRM Setting id or not.
        // if we are getting Some(DateTime) we add it to a column in the workflow_crm_settings table so that the whole crm is paused
        val nextAttemptAt = AttemptRetryCronService.getNextAttemptAtFromError(
          err = err,
          attempt_retry_count = attempt_retry_count
        )

        err match {
          case _: WorkflowAttemptTryErrorReason.TooManyRequestsError =>
            //Duration gives us the difference between the second and the first parameters, we use toStandardSeconds to get joda.Seconds and getSeconds to get int
            if (nextAttemptAt.isEmpty) {
              Logger.fatal(s"IMPOSSIBLE CASE rate limit error without next attempt at sent ${attempt_log_id}")
            }

            val expiresInSeconds: Int = nextAttemptAt
              .map(nxtAttmt => new Duration(DateTime.now(), nxtAttmt).toStandardSeconds.getSeconds)
              .getOrElse(AppConfig.AuditTrail.defaultRatelimitWaitTimeInSeconds)

            Logger.debug(s"Pausing wf_$workflow_crm_setting_id for $expiresInSeconds seconds")
            workflowJedisService.acquireLockAndAddToSetForRatelimit(
              additionalKeysToLock = Set(workflow_crm_setting_id.toString),
              expireInSeconds = expiresInSeconds
            ) match {
              case Success(_) => //DO Nothing
              case Failure(exception) => Logger.fatal(s"Failed to acquireLockAndAddToSet workflow_crm_setting_id: $workflow_crm_setting_id", exception)
            }


          case _ =>
            val attempt_status = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
              attempt_status = AttemptStatus.FailedAttempt,
              attempt_retry_count = attempt_retry_count,
              workflowAttemptTryErrorReason = err
            ).get
            workFlowAttemptService.updateAttemptLogStatus(
              attempt_log_id = attempt_log_id, //FIXME VALUECLASS
              teamId = teamId,
              accountId = accountId,
              Logger = Logger
            )(
              attempt_status = attempt_status,
              failure_reason = Some(err),
              attempt_retry_count = attempt_retry_count
            ) match {
              case Right(_) => {
                if (teamId.id == 16247) {
                  Logger.debug(s"Updated attempt_status to ${attempt_status} attempt_log_id_${attempt_log_id} tid_${teamId.id} with ${attempt_retry_count} because of ${err.message}")
                }

              }
              case Left(value) =>
                Logger.debug(s"STEP 12: Failed to add \"Attempt failed\" event for  attempt_log_id ${attempt_log_id} error $value")
            }
        }
        checkAndPauseCRMForError(
          err = err,
          workflow_setting_id = workflow_crm_setting_id,
          teamId = teamId,
        )

        nextAttemptAt match {
          case Some(nextAttemptAtDateTime) =>

            triggerDAO.setNextAttemptAt(
              nextAttemptAt = nextAttemptAtDateTime,
              workflow_crm_setting_id = workflow_crm_setting_id,
              team_id = teamId
            ) match {
              case Success(_) => {
                if (teamId.id == 16247) {
                  Logger.debug(s"Setting Next Attempt At : ${nextAttemptAt} because of err ${err.message} tid_${teamId.id} ")
                }
                Success(attempt_log_id)
              }
              case Failure(exception) =>
                Logger.fatal(s"Failed to setNextAttemptAt for attempt_log_id $attempt_log_id", exception)
                Failure(exception)
            }
          case None =>
            // None in nextAttemptAt means that we dont need to pause the CRM
            Success(attempt_log_id)
        }


    }
  }


  private def checkAndPauseCRMForError(
                                        err: WorkflowAttemptTryErrorReason,
                                        workflow_setting_id: Long,
                                        teamId: TeamId,
                                      )(using Logger: SRLogger): Unit = {

    if (err.pause_crm) {
      triggerDAO.addIntegrationError(
        teamId = teamId.id,
        workflow_setting_id = workflow_setting_id,
        error = err.message,
      ) match {
        case Success(None) =>
          Logger.warn(s"Trying to add error for a CRM that doesn't exist workflow_setting_id: $workflow_setting_id :: team_id : ${teamId.id}")
        case Success(Some(_)) => //DO NOTHING
        case Failure(exception) =>
          Logger.fatal(s"Error while Trying to add error for a CRM that doesn't exist workflow_setting_id: $workflow_setting_id :: team_id : ${teamId.id}", exception)

      }

    } else {
      triggerDAO.updateErrorCount(
        teamId = teamId,
        workflow_setting_id = workflow_setting_id,
        total_consecutive_error_attempts = 0
      ) match {
        case Success(value) => //Do NOTHING
        case Failure(exception) => Logger.shouldNeverHappen(s"Failed triggerDAO.updateErrorCount", Some(exception))
      }
    }

  }


}

object ProcessAttemptService {


  def getNewAttemptStatusForProcessedAttempt(
                                              attempt_status: AttemptStatus,
                                              attempt_retry_count: Int,
                                              workflowAttemptTryErrorReason: WorkflowAttemptTryErrorReason
                                            ): Try[AttemptStatus] = {
    attempt_status match {
      case AttemptStatus.YetToAttempt =>
        Failure(new Throwable("Wrong Attempt Status"))
      case AttemptStatus.FailedAttempt =>
        workflowAttemptTryErrorReason match {
          case WorkflowAttemptTryErrorReason.SRInternalServerError(err) =>
            err match {
              case _@(
                _: WorkflowAttemptInternalServerError.SqlException |
                _: WorkflowAttemptInternalServerError.FutureException |
                _: WorkflowAttemptInternalServerError.HubspotFailureBatchError |
                _: WorkflowAttemptInternalServerError.SalesforceFailureBatchError |
                _: WorkflowAttemptInternalServerError.UnableToLockRow |
                _: WorkflowAttemptInternalServerError.Others
                ) => Success(getYetToAttemptOrFailedAttempt(attempt_retry_count = attempt_retry_count))

              case _@(
                _: WorkflowAttemptInternalServerError.TokensNotFoundInDBError |
                _: WorkflowAttemptInternalServerError.InvalidModuleError |
                _: WorkflowAttemptInternalServerError.InvalidEventError |
                _: WorkflowAttemptInternalServerError.IntegrationNotFound |
                _: WorkflowAttemptInternalServerError.TriggerNotFound |
                _: WorkflowAttemptInternalServerError.FieldMappingNotFound |
                _: WorkflowAttemptInternalServerError.ProspectIdEmpty |
                _: WorkflowAttemptInternalServerError.CampaignIdEmpty |
                _: WorkflowAttemptInternalServerError.UpdateSentNone |
                _: WorkflowAttemptInternalServerError.ErrorLeadStatusOrdering |
                _: WorkflowAttemptInternalServerError.WrongProcessType
                ) => Success(AttemptStatus.IgnoredAttempt)

            }
          case _@(
            _: WorkflowAttemptTryErrorReason.OAuthError |
            _: WorkflowAttemptTryErrorReason.CRMInternalServerError |
            _: WorkflowAttemptTryErrorReason.UnAuthorizedError |
            _: WorkflowAttemptTryErrorReason.UnKnownError |
            _: WorkflowAttemptTryErrorReason.MalformedResponseStructureError |
            _: WorkflowAttemptTryErrorReason.UnableToFindPersonIdError |
            _: WorkflowAttemptTryErrorReason.InvalidRefreshTokenError
            ) =>
            Success(getYetToAttemptOrFailedAttempt(attempt_retry_count = attempt_retry_count))


          case _: WorkflowAttemptTryErrorReason.SettingsNotEnabled => {
            Success(AttemptStatus.IgnoredAttempt)
          }

          case _: WorkflowAttemptTryErrorReason.ContactCreationDelayInCRM =>
            Success(AttemptStatus.YetToAttempt)
          case _: WorkflowAttemptTryErrorReason.TooManyRequestsError =>
            Success(AttemptStatus.YetToAttempt)
        }

      case AttemptStatus.SuccessAttempt =>
        Failure(new Throwable("Wrong Attempt Status"))
      case AttemptStatus.StartedAttempt =>
        Failure(new Throwable("Wrong Attempt Status"))
      case AttemptStatus.IgnoredAttempt =>
        Success(AttemptStatus.IgnoredAttempt)

    }
  }

  def getYetToAttemptOrFailedAttempt(attempt_retry_count: Int) = {
    if (attempt_retry_count > 4) {
      AttemptStatus.FailedAttempt
    } else {
      AttemptStatus.YetToAttempt
    }
  }
}
