package api.sr_audit_logs.services

import api.AppConfig
import api.accounts.TeamId
import api.sr_audit_logs.dao.{EventAndAttemptDataForProcessing, WorkflowAttemptDAO}
import api.sr_audit_logs.models.{AttemptLogId, AttemptLogsFoundForTryOrRetry, EventType, FindAttemptLogsForQueuingError, WorkflowAttemptTryErrorReason, WorkflowSettingData}
import api.triggers.IntegrationType
import utils.cronjobs.AttemptRetryCronService
import utils.{Helpers, SRLogger}
import utils.dbutils.DBUtils
import utils.uuid.SrUuidUtils

import scala.util.{Failure, Success, Try}

class WorkFlowAttemptService(
                      override protected val attemptDAO: WorkflowAttemptDAO,
                      override protected val dbUtils: DBUtils,
                      override protected val srUuidUtils: SrUuidUtils
                    ) extends AttemptServiceTrait[WorkflowSettingData, WorkflowAttemptTryErrorReason]{


  /*
  * Creating a method that takes
  * atmpt: AttemptLogsFoundForTryOrRetry[WorkflowSettingData]
  * returning Try[MQMsg] with the attempt_data: List[AttemptData] in the MQMsg
  * if it is a single AttemptType data we will have only one attempt_data in the AttemptLogsFoundForTryOrRetry and it will make one MqMsg with one attempt_data
  * if it is a batch AttemptType data we will have a list of attempt_data up to 100 in the AttemptLogsFoundForTryOrRetry and it will make one MqMsg with the list of attempt_data*/

  def getSingleMsgSetForProcessing(
                                 atmpt: AttemptLogsFoundForTryOrRetry[WorkflowSettingData],
                                 logger: SRLogger
                               ): Try[List[EventAndAttemptDataForProcessing]] = {
    attemptDAO.findEventDataTypeForWorkflowCRM(
      attempts = atmpt,
      logger = logger
    )

  }
  /*
      Note: 31 Dec 2024
        Here instead of pulling attempt records by last_attempted_at for each event type
        we are splitting the attempt count for this by
        events  crmToSmartReach: 0.2
        and SmartReachToCrm: 0.8
        out of the total 30 attempts we are finding
        in each cron cycle

        This is done because if there is accumulation of lots of events for smartreach to crm , then
        crm to smartreach are also getting delayed.
   */

  private def findAllAttemptLogsForQueuing(
                                           ignoreTheseWorkflowIds: Set[String],
                                           logger: SRLogger,
                                           crmToSR: Boolean
                                         ): Try[List[AttemptLogsFoundForTryOrRetry[WorkflowSettingData]]] = {

//    val limit = if(crmToSR) 30 else AttemptRetryCronService.totalNumberOfAttemptsToPickInCurrentCycle
  /*
      Note: 16 Jan 2025
      Instead of 30 limit i am picking the default because crm to sr events were starving
   */
    val limit =  AttemptRetryCronService.totalNumberOfAttemptsToPickInCurrentCycle
    attemptDAO.findWorkflowCrmSettingIdsByLimit(limit = limit) match {
      case Failure(exception) => Failure(exception)
      case Success(workflowCRMSettingIds) => {
      Success(workflowCRMSettingIds.map(wid =>{
        attemptDAO.findSingleAttemptLogBasedOnSyncDirectionAndWorkflowCRMSetting(
          ignoreTheseWorkflowIds = ignoreTheseWorkflowIds,
          workflowCrmSettingId = wid.workflow_crm_setting_id,
          teamId =wid.teamId,
          logger = logger,
          crmToSR = crmToSR
        ) match {
          case Failure(exception) => {
            logger.fatal(s"findAllAttemptLogsForQueuing An Error while fetching single attempt for workflowCrmSettingId ${wid} ")

            updateLastProcessedAt(
              workflowCRMSettingId = wid.workflow_crm_setting_id,
              teamId =wid.teamId
            )(Logger = logger)
            None
          }

          case Success(attemptOpt) => {
            if(attemptOpt.isEmpty){
              updateLastProcessedAt(
                workflowCRMSettingId = wid.workflow_crm_setting_id,
                teamId =wid.teamId
              )(Logger = logger)
            }
            attemptOpt
          }
        }
        })
        .filter(_.isDefined)
        .map(_.get))

      }
    }

  }

  private def findAttemptLogsForQueuingByClubbingAttempts(logger: SRLogger):Try[Seq[AttemptLogsFoundForTryOrRetry[WorkflowSettingData]]] ={

    for{
      crmToSRAttempts: Seq[AttemptLogsFoundForTryOrRetry[WorkflowSettingData]] <- findAllAttemptLogsForQueuing(
      ignoreTheseWorkflowIds = Set(),
      logger = logger ,
      crmToSR = true)

      srToCRMAttempts: Seq[AttemptLogsFoundForTryOrRetry[WorkflowSettingData]] <- findAllAttemptLogsForQueuing(
      ignoreTheseWorkflowIds = Set(),
      logger = logger ,
      crmToSR = false)

    } yield {

      /*
          scala> Math.floor(0.2*30).toInt
          val res0: Int = 6
       */

      val totalFinalAttempsToTake = AttemptRetryCronService.totalNumberOfAttemptsToPickInCurrentCycle*2
      val numberOfEventsToTake = Math.floor((totalFinalAttempsToTake)*AttemptRetryCronService.crmToSREventRatio).toInt
      val crmToSR = crmToSRAttempts.take(numberOfEventsToTake)
      val printStringCRMToSR: Seq[String] = crmToSR.map(atmpts =>{
        "tid_" + atmpts.team_id.id +" " + atmpts.attempt_data.map(_.attempt_log_id.id).toString()
      })
      logger.debug(s"crmToSR Events Found with length:${crmToSR.length} in  current cycle ${printStringCRMToSR}")

      val srToCRM = srToCRMAttempts.take((totalFinalAttempsToTake) - crmToSR.length)
      val printStringSRToCRM: Seq[String] = srToCRM.map(atmpts =>{
        "tid_" + atmpts.team_id.id +" " + atmpts.attempt_data.map(_.attempt_log_id.id).toString()
      })
      logger.debug(s"srToCRM Events Found length:${srToCRM.length} in current cycle ${printStringSRToCRM}")

      crmToSR ++ srToCRM
    }
  }

  def updateLastProcessedAt(
                           workflowCRMSettingId: Long,
                           teamId: TeamId
                           )(Logger:SRLogger): Unit = {

    attemptDAO.updateLastProcessedAt(
      workflow_crm_setting_id = workflowCRMSettingId ,
      teamId=teamId
    ) match {
      case Failure(exception) => {
        Logger.fatal(s"updateLastProcessedAt An Error Occurred while updating last Processed at ",err = exception)
      }
      case Success(widOpt) => {
        widOpt match {
          case Some(wid) =>
//          {
//            Logger.debug(s"updateLastProcessedAt findAllAttemptLogsForQueuing Successfully update lastProcessed at for wid ${wid}")
//          }
          case None => {
            Logger.shouldNeverHappen(s"updateLastProcessedAt findAllAttemptLogsForQueuing Workflow CRM setting id not found while processing workflow_crm_setting_id: ${workflowCRMSettingId}")
          }
        }

      }
    }
  }


  def findAttemptLogsForQueuing(
                                 logger: SRLogger
                               ): Either[FindAttemptLogsForQueuingError, Seq[AttemptLogsFoundForTryOrRetry[WorkflowSettingData]]] = {

    val result = for {
      //: Seq[AttemptLogsFoundForTryOrRetry[WorkflowAttemptData]]
      // when we fetch the items from the DB  - we will get a mixed group of workflow settings , attemps and event types
      // we are filtering out the workflow_crm_setting_id that are rate limited but using the next_attempt_at col from the workflow_crm_settings table
      attemptLogs <- findAttemptLogsForQueuingByClubbingAttempts(logger = logger)


    } yield {

      //  we want to make these events homogeneously grouped by workflow and event_type
      // for example - if we get 10 attempts, 4 of teamId1 and 6 of teamId 2, where we have only 1 crm for each
      // and for teamId1 we have 1 Grouped attempt type (4 attempts) - we see only 1 attempt (with 4 attempt data)
      // and for teamId2 we have 2 attempt type 1 grouped (3 attempts) and 3 single process (3 attempts)- we'll wee 4 attempts (1 grouped and 3 single)
      // see the unit test for a more complex grouping
      val grouped = WorkflowSettingData.getGroupedAttemptsForWebhookSettingData(attemptLogs.toList)

      val listOfTry = grouped.map { attemptLog =>

        if (
          EventType.isBatch(attemptLog.eventType)  &&
            (attemptLog.attempt_setting.crm_type.batchUpdateSupported) //since in pipedrive we dont have batch contacts update or create we do one prospect at a time, so we dont need to get batch here
        ) {

          // here we are checking if the grouped batch event has 100 attempts grouped so that the processing is the most efficient
          // if we don't have 100 attempts grouped attempts, we get more attempts to make it so that we have 100 attempts at once
          if (attemptLog.attempt_data.length < 100) {
            attemptDAO.getBatchAttemptLogsForQueuing(
              workflowSettingId = attemptLog.attempt_setting.workflow_crm_setting_id,
              teamId = attemptLog.team_id,
              count = 100 - attemptLog.attempt_data.length,
              eventType = attemptLog.eventType,
              logger = logger
            ) map { restOfTheBatch =>
              attemptLog.copy(
                attempt_data = attemptLog.attempt_data ++ restOfTheBatch.flatMap(_.attempt_data).toList
              )

            }


          } else {
            Success(attemptLog)
          }

        } else {
          Success(attemptLog)
        }


      }

      Helpers.seqTryToTrySeq(listOfTry)

    }

    result.flatten match {
      case Success(value) => Right(value)
      case Failure(exception) => Left(FindAttemptLogsForQueuingError.SQLExceptionFind(exception))
    }

  }
}
