package api.sr_audit_logs.services

import api.sr_audit_logs.dao.EventLogDAO
import api.sr_audit_logs.models.{CreateEventLogError, EventDataType, EventLog, EventType, GetEventLogsError}
import org.joda.time.DateTime
import play.api.libs.json.JsValue
import scala.util.{Failure, Random, Success}

import utils.SRLogger


class EventLogFindService(
                       eventLogDAO: EventLogDAO
                     ) {


  def findEventLogByTeamIdEventLogId(
                                      teamId: Long,
                                      event_log_id: String,
                                      logger: SRLogger,
                                    ): Either[GetEventLogsError, Option[EventLog]] = {


    for {
      eventLogs <- {
        
          eventLogDAO.findEventLogByTeamIdAndEventLogId(
            teamId = teamId,
            event_log_id = event_log_id,
            logger = logger
          )
            .toEither
          .left.map(e => GetEventLogsError.SQLException(error = e))
      }
    } yield {
      eventLogs
    }
  }


}


