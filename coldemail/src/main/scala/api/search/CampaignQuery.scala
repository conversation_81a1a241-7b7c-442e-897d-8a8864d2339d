package api.search

import api.AppConfig
import api.accounts.{Account, TeamId}
import io.sr.billing_common.models.PlanType
import api.campaigns.CampaignIdAndTeamId
import api.campaigns.models.{CampaignStepType, CurrentStepStatusForScheduler, InactiveCampaignCheckType, SearchParams}
import api.campaigns.services.CampaignId
import api.columns.{CampaignColumnDef, FieldTypeEnum}
import api.integrations.SRTriggerSource
import api.prospects.models.ProspectCategory
import api.prospects.{InferredQueryTimeline, SearchQuerySelectType}
import api.search.RequestBodyValidationError.ValidationError
import api.team_inbox.service.ReplySentimentUuid
import scalikejdbc._
import sr_scheduler.CampaignStatus
import sr_scheduler.models.ChannelType
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus
import utils.dbutils.SQLUtils
import utils.pagination.GetDataErrorTrait
import utils.{ParseUtils, SRAppConfig, SRLogger}

import scala.util.{Failure, Success, Try}

object CampaignQuery extends SearchQueryTrait {

  def getStatementClauseV2(
                            field: String,
                            operator: SearchQueryOperators.Value,
                            value: String,
                            // clause: SQLSyntax,
                            field_type: FieldTypeEnum.Value,
                            is_custom: Boolean,
                            team_id: Long,
                            account: Account,
                            Logger: SRLogger
                          ): SearchQueryGetStatementV2 = {
    SearchQueryGetStatementV2(
      whereClauseInner = None,
      whereClauseOuter = None
    )
  }

  def campaignSelectQuery(
                           show_tags: Boolean,
                           teamId: TeamId,
                           whereClause: SQLSyntax
                         ): SQL[Nothing, NoExtractor] = {

    val campaignTagsSubQuery: SQLSyntax = CampaignQuery.genCampaignTagsSubquery(
      teamId = teamId.id,
      showCampaignTags = show_tags
    )
    val campaign_email_settings_select: SQLSyntax = campaignEmailSettingsSelect()

    val show_soft_start_query: SQLSyntax = CampaignQuery.showSoftStartQuery
    val campaign_linkedin_settings_select: SQLSyntax = campaignLinkedinSettingSelect()
    val campaign_whatsapp_setting_select: SQLSyntax = campaignWhatsappSettingSelect()
    val campaign_sms_setting_select: SQLSyntax = campaignSmsSettingSelect()
    val campaign_call_setting_select: SQLSyntax = campaignCallSettingSelect()

    sql"""
          SELECT
            a.email AS owner_email,
            a.id AS owner_account_id,
            CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,

            COALESCE(json_agg(trig) FILTER (WHERE trig.id IS NOT NULL), '[]') AS triggers,
            c.id,
            c.account_id,
            c.name,
            c.status,
            c.is_archived,
            c.timezone,
            c.daily_from_time,
            c.daily_till_time,
            c.days_preference,
            c.ai_sequence_status,
            c.ai_generation_context,
            c.created_at,
            c.head_step_id,
            c.last_scheduled_at,
            c.email_priority,
            c.opt_out_msg,
            c.opt_out_is_text,
            c.team_id,
            c.shared_with_team,
            c.ta_id,
            c.max_emails_per_day,
            c.mark_completed_after_days,
            c.open_tracking_enabled,
            c.warmup_started_at,
            c.warmup_starting_email_count,
            c.warmup_length_in_days,
            c.click_tracking_enabled,
            c.latest_email_scheduled_at,
            c.append_followups,
            c.status_changed_at,
            c.will_delete,
            c.will_delete_at,
            c.enable_email_validation,
            c.ab_testing_enabled,
            c.add_prospect_to_dnc_on_opt_out,
            c.schedule_start_at,
            c.schedule_start_at_tz,
            c.last_analysed_at,
            c.pushed_to_queue_for_analysis_at,
            c.sending_holiday_calendar_id,
            -- c.next_to_be_scheduled_at,
            c.last_email_sent_at,
            c.latest_campaign_send_start_reports_id,
            c.pushed_to_queue_for_analysing_start_report_at,
            c.in_queue_for_analyzing_start_report,
            c.last_pushed_for_preemailvalidation_at,
            c.uuid,
            c.calendar_event_type_id,
            c.calendar_event_type_slug,
            c.calendar_is_individual,
            c.calendar_selected_user_id,
            c.calendar_selected_username_slug,
            c.calendar_smartreach_account_id,
            c.calendar_team_id,
            c.send_plain_text_email,
            c.campaign_type,
            c.calendar_team_slug,
            c.sending_mode,
            $campaign_email_settings_select,
            $campaignTagsSubQuery,
            $campaign_linkedin_settings_select,
            $campaign_call_setting_select,
            $campaign_whatsapp_setting_select,
            $campaign_sms_setting_select,

            $show_soft_start_query,

            has_spam_test.exists AS spam_test_exists,
            (SELECT EXISTS(
              SELECT cs.id FROM campaign_steps cs 
              WHERE cs.campaign_id = c.id 
              AND (
                cs.step_type = ${CampaignStepType.AutoEmailStep.toKey}
                OR
                cs.step_type = ${CampaignStepType.ManualEmailStep.toKey}
              )
            )) AS campaign_has_email_step,

            (case when c.warmup_started_at is null or c.warmup_length_in_days is null THEN false
              else c.warmup_started_at + c.warmup_length_in_days * interval '1 day' > now() end) as warmup_is_on


          FROM campaigns c
            INNER JOIN accounts a ON a.id = c.account_id

            LEFT JOIN triggers trig ON (trig.campaign_id = c.id AND trig.source = ${SRTriggerSource.CAMPAIGN_SETTINGS.toString})

            LEFT JOIN LATERAL (
             SELECT EXISTS(SELECT id FROM spam_tests spamtest WHERE spamtest.campaign_id = c.id)
            ) AS has_spam_test ON true

          $whereClause
        """
  }


  def getQueryFindingCampaignForCampaignUtilsOnly(
                                                   campaign_id: CampaignId,
                                                   team_id: TeamId
                                                 ): SQL[Nothing, NoExtractor] = {


    val campaign_email_settings_select: SQLSyntax = campaignEmailSettingsSelect()
    val campaign_linkedin_settings_select: SQLSyntax = campaignLinkedinSettingSelect()
    val campaign_whatsapp_setting_select: SQLSyntax = campaignWhatsappSettingSelect()
    val campaign_sms_setting_select: SQLSyntax = campaignSmsSettingSelect()
    val campaign_call_setting_select: SQLSyntax = campaignCallSettingSelect()
    val show_soft_start_query: SQLSyntax = CampaignQuery.showSoftStartQuery

    sql"""
    SELECT
      COALESCE(json_agg(trig) FILTER (WHERE trig.id IS NOT NULL), '[]') AS triggers,
      c.id,
      c.account_id,
      c.name,
      c.status,
      c.is_archived,
      c.timezone,
      c.daily_from_time,
      c.daily_till_time,
      c.days_preference,
      c.ai_sequence_status,
      c.ai_generation_context,
      c.created_at,
      c.head_step_id,
      c.last_scheduled_at,
      c.email_priority,
      c.opt_out_msg,
      c.opt_out_is_text,
      c.team_id,
      c.shared_with_team,
      c.ta_id,
      c.max_emails_per_day,
      c.mark_completed_after_days,
      c.open_tracking_enabled,
      c.warmup_started_at,
      c.warmup_starting_email_count,
      c.warmup_length_in_days,
      c.click_tracking_enabled,
      c.latest_email_scheduled_at,
      c.append_followups,
      c.status_changed_at,
      c.will_delete,
      c.will_delete_at,
      c.enable_email_validation,
      c.ab_testing_enabled,
      c.add_prospect_to_dnc_on_opt_out,
      c.schedule_start_at,
      c.schedule_start_at_tz,
      c.last_analysed_at,
      c.pushed_to_queue_for_analysis_at,
      c.sending_holiday_calendar_id,
      -- c.next_to_be_scheduled_at,
      c.last_email_sent_at,
      c.latest_campaign_send_start_reports_id,
      c.pushed_to_queue_for_analysing_start_report_at,
      c.in_queue_for_analyzing_start_report,
      c.last_pushed_for_preemailvalidation_at,
      c.uuid,
      c.calendar_event_type_id,
      c.calendar_event_type_slug,
      c.calendar_is_individual,
      c.calendar_selected_user_id,
      c.calendar_selected_username_slug,
      c.calendar_smartreach_account_id,
      c.calendar_team_id,
      c.send_plain_text_email,
      c.campaign_type,
      c.calendar_team_slug,
      c.sending_mode,


      $show_soft_start_query,

      $campaign_email_settings_select,
      $campaign_linkedin_settings_select,
      $campaign_call_setting_select,
      $campaign_whatsapp_setting_select,
      $campaign_sms_setting_select

    FROM campaigns c
    INNER JOIN accounts a ON a.id = c.account_id
    LEFT JOIN triggers trig ON (trig.campaign_id = c.id AND trig.source = ${SRTriggerSource.CAMPAIGN_SETTINGS.toString})
    WHERE c.id = ${campaign_id.id} AND c.team_id = ${team_id.id}
    GROUP BY c.id, a.id

  """


  }

  def getQueryForInactiveCampaignsForStopping(
                                               inactiveCampaignCheckType: InactiveCampaignCheckType,
                                               campaignIdAndTeamId: CampaignIdAndTeamId,
                                               stepTypesInCampaign: Set[CampaignStepType]
                                             ): SQLSyntax = {
    val stop_inactive_campaigns_after_days = SQLSyntax.createUnsafely(SRAppConfig.StopInactiveCampaign.stop_inactive_campaigns_after_days.toString)
    val emailOnlyCampaign = CampaignStepType.isEmailOnlyCampaign(steps = stepTypesInCampaign)

    val whereForCampaign: SQLSyntax =
      inactiveCampaignCheckType match {
        case InactiveCampaignCheckType.ThirtyDayCheck =>
          sqls"""
          c.id = ${campaignIdAndTeamId.campaign_id}
          AND c.team_id = ${campaignIdAndTeamId.team_id}
          """
        case InactiveCampaignCheckType.OnHoldCheck =>
          sqls"""
          c.id = ${campaignIdAndTeamId.campaign_id}
          AND c.team_id = ${campaignIdAndTeamId.team_id}
          """
      }


    val whereForTimeCheckForCampaign: SQLSyntax = inactiveCampaignCheckType match {
      case InactiveCampaignCheckType.ThirtyDayCheck =>
        sqls"""
          AND c.status_changed_at < now() - interval '$stop_inactive_campaigns_after_days days'
          AND (
          c.last_email_sent_at < now() - interval '$stop_inactive_campaigns_after_days days'
          OR  c.last_email_sent_at IS NULL
          )
          AND (
          last_checked_for_completed_campaign_cron_at IS NULL OR
          last_checked_for_completed_campaign_cron_at < now() - interval '24 hours'
          )

            """
      case InactiveCampaignCheckType.OnHoldCheck => sqls""""""
    }

    val whereForCampaignProspects: SQLSyntax = inactiveCampaignCheckType match { //FIXME: send the final step id from top
      case InactiveCampaignCheckType.ThirtyDayCheck =>
        sqls""""""

      case InactiveCampaignCheckType.OnHoldCheck =>
        sqls"""
             AND NOT cp.to_check

          """
      //            --AND NOT (
      //        --cp.current_step_status_for_scheduler is null
      //        --OR cp.current_step_status_for_scheduler in (${CurrentStepStatusForScheduler.Done.toString}, ${CurrentStepStatusForScheduler.Skipped.toString})
      //        --OR
      //        --(
      //          --cp.current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Due.toString}
      //          --AND
      //          --cp.current_step_id = (SELECT cs.id FROM campaign_steps cs WHERE cs.campaign_id = c.id ORDER BY cs.id DESC LIMIT 1 )
      //        --)
      //      --)
    }

    val whereForTimeCheckForCampaignProspects: SQLSyntax = inactiveCampaignCheckType match {
      case InactiveCampaignCheckType.ThirtyDayCheck =>
        sqls"""
        AND NOT (
            cp.last_contacted_at > now() - interval '$stop_inactive_campaigns_after_days days' OR
            cp.last_contacted_at is null
        )
        AND NOT ((
            cp.latest_task_done_or_skipped_at > now() - interval '$stop_inactive_campaigns_after_days days'
            AND cp.current_step_status_for_scheduler not in  (${CurrentStepStatusForScheduler.Done.toString})
           )
        OR cp.current_step_status_for_scheduler in  (${CurrentStepStatusForScheduler.Due.toString})
        )
            """
      case InactiveCampaignCheckType.OnHoldCheck =>
        sqls""""""
    }

    val whereForWillResumeAt: SQLSyntax = inactiveCampaignCheckType match {
      case InactiveCampaignCheckType.ThirtyDayCheck =>
        sqls"""
    AND (
          cp.will_resume_at is null OR
          cp.will_resume_at > now()
         )
    """
      case InactiveCampaignCheckType.OnHoldCheck =>
        sqls"""
          AND (
                cp.will_resume_at is null OR
                (
                cp.will_resume_at > now()
                AND cp.will_resume_at < now() + interval '${SQLSyntax.createUnsafely(AppConfig.CampaignSchedulingMetadataConstants.refresh_after_hours.toString)} hours'
                )
               )
          """

    }


    val email_step_only_campaign_check = if (emailOnlyCampaign) {
      sqls"""
            AND NOT cp.invalid_email
          """
    } else {
      sqls""
    }
    sqls"""
             SELECT
          o.name as org_name,
          o.created_at AS org_created_at,
          a.email as campaign_owner_email,
          c.id AS cid,
          c.created_at AS campaign_created_at,
          c.team_id,
          c.name AS campaign_name,
          t.name AS team_name
        FROM
          campaigns c
          JOIN teams t ON c.team_id = t.id
          JOIN accounts a ON a.id = c.account_id
          JOIN organizations o ON o.id = a.org_id
        WHERE
        $whereForCampaign
        $whereForTimeCheckForCampaign

        AND NOT EXISTS (
          SELECT
            *
          FROM
            campaigns_prospects cp
            JOIN prospect_categories_custom cat ON cat.id = cp.prospect_category_id_custom
          WHERE
            cp.campaign_id = c.id
            AND cp.active
            AND NOT cat.text_id = ${ProspectCategory.DO_NOT_CONTACT.toString}
            $email_step_only_campaign_check

            AND (
              cp.unpaused_by_admin
              OR (
                -- email-specific checks to stop campaign
                NOT cp.replied
                AND NOT cp.opted_out
                AND NOT cp.out_of_office
                AND NOT cp.auto_reply
                AND NOT cp.bounced
                AND NOT cp.completed
                AND NOT cp.paused
                $whereForWillResumeAt
                -- TODO: linkedin-specific checks to stop campaign - ideally any such case should result in cp.completed to be true
              )
            )
            $whereForCampaignProspects
            $whereForTimeCheckForCampaignProspects
          );
           """
    //            AND (
    //            (
    //              EXISTS(SELECT cs.id from campaign_steps cs where cs.campaign_id = c.id and step_type in (${CampaignStepType.AutoEmailStep.toKey}, ${CampaignStepType.ManualEmailStep.toKey}))
    //              AND NOT cp.invalid_email
    //            ) OR
    //            NOT EXISTS(SELECT cs.id from campaign_steps cs where cs.campaign_id = c.id and step_type in (${CampaignStepType.AutoEmailStep.toKey}, ${CampaignStepType.ManualEmailStep.toKey}))
    //
    //            )
  }

  def campaignLinkedinSettingSelect(): SQLSyntax = {
    sqls"""
      (
        SELECT json_agg(
          json_build_object(
            'channel_setting_uuid', ccs.channel_settings_uuid,
            'team_id', c.team_id,
            'email', ls.email,
            'first_name', ls.first_name,
            'last_name',  ls.last_name,
            'linkedin_profile_url', ls.profile_url,
            'automation_enabled', ls.captain_data_account_id IS NOT NULL
          )) AS campaign_linkedin_settings
        FROM linkedin_settings ls
        LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ls.uuid AND ccs.channel_type = ${ChannelType.LinkedinChannel.toString})
        WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_linkedin_settings
        """
  }

  def campaignWhatsappSettingSelect(): SQLSyntax = {
    sqls"""
      (
        SELECT json_agg(
          json_build_object(
            'channel_setting_uuid', ccs.channel_settings_uuid,
            'team_id', c.team_id,
            'phone_number', ws.whatsapp_number,
            'first_name', ws.first_name,
            'last_name',  ws.last_name
          )) AS campaign_whatsapp_settings
        FROM whatsapp_settings ws
        LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ws.uuid AND ccs.channel_type = ${ChannelType.WhatsappChannel.toString})
        WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_whatsapp_settings
        """
  }

  def campaignSmsSettingSelect(): SQLSyntax = {
    sqls"""
      (
        SELECT json_agg(
          json_build_object(
            'channel_setting_uuid', ccs.channel_settings_uuid,
            'team_id', c.team_id,
            'phone_number', ss.phone_number,
            'first_name', ss.first_name,
            'last_name',  ss.last_name
          )) AS campaign_sms_settings
        FROM sms_settings ss
        LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ss.uuid AND ccs.channel_type = ${ChannelType.SmsChannel.toString})
        WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_sms_settings
        """
  }

  def campaignCallSettingSelect(): SQLSyntax = {
    sqls"""
      (
        SELECT json_agg(
          json_build_object(
            'channel_setting_uuid', ccs.channel_settings_uuid,
            'team_id', c.team_id,
            'phone_number', cs.phone_number,
            'first_name', cs.first_name,
            'last_name',  cs.last_name
          )) AS campaign_call_settings
        FROM call_settings cs
        LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = cs.uuid AND ccs.channel_type = ${ChannelType.CallChannel.toString})
        WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_call_settings
        """
  }


  def campaignEmailSettingsSelect(): SQLSyntax = {

    sqls"""
     (
      SELECT
        json_agg(
          json_build_object(
            'id', ces.id,
            'campaign_id', ces.campaign_id,
            'sender_email_setting_id', ces.sender_email_setting_id,
            'receiver_email_setting_id', ces.receiver_email_setting_id,
            'team_id', ces.team_id,
            'uuid', ces.uuid,
            'sender_email', sender.email,
            'receiver_email', receiver.email,
            'from_name', sender.sender_name,
            'signature', sender.signature,
            'max_emails_per_day_from_email_account', sender.quota_per_day,
            'error', (
              CASE WHEN (sender.paused_till IS NOT NULL
                AND sender.paused_till > now()) THEN
                sender.error
              WHEN (receiver.paused_till IS NOT NULL
                AND receiver.paused_till > now()) THEN
                receiver.error
              WHEN (sender.is_under_review IS TRUE
                OR receiver.is_under_review IS TRUE) THEN
                'Your email account is under manual review. Please contact support.'
              WHEN (a.active IS FALSE) THEN
                concat('Campaign owner''s (', a.email, ') account has been deactivated by your team''s admin. Please change the owner to restart sending.')
              WHEN (sender_owner.active IS FALSE) THEN
                concat('You are using an email account (', sender.email, ') whose owner''s SmartReach.io account (', sender_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
              WHEN (receiver_owner.active IS FALSE) THEN
                concat('You are using an email account (', receiver.email, ') whose owner''s SmartReach.io account (', receiver_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
              WHEN (sender_domain_check.is_in_spam_blacklist IS TRUE) THEN
                'Your sending email domain is found in a global spam blacklist. Please check the status by going to Settings -> Team Settings -> Domain Health.'
              ELSE
                NULL
              END))) AS campaign_email_settings
      FROM
        campaign_email_settings ces
        JOIN email_settings sender ON sender.id = ces.sender_email_setting_id
          AND ces.team_id = sender.team_id
        JOIN email_settings receiver ON receiver.id = ces.receiver_email_setting_id
          AND ces.team_id = receiver.team_id
      LEFT JOIN accounts sender_owner ON sender.account_id = sender_owner.id
      LEFT JOIN accounts receiver_owner ON receiver.account_id = receiver_owner.id
      LEFT JOIN domain_health_checks sender_domain_check ON sender_domain_check.domain = sender.email_address_host
    WHERE
      ces.campaign_id = c.id
      and sender.status = ${EmailSettingStatus.Active.toString}
      and receiver.status = ${EmailSettingStatus.Active.toString}
      AND ces.team_id = c.team_id) AS campaign_email_settings
        """
  }

  def genCampaignTagsSubquery(
                               teamId: Long,
                               showCampaignTags: Boolean
                             ): SQLSyntax = {


    val campaignTagsSubQuery = if (showCampaignTags) {
      sqls"""
          (
            SELECT
              json_agg(json_build_object('tag_id', tlfc.id, 'tag', tlfc.tag, 'tag_uuid', tlfc.uuid)) AS camtags
            FROM
              tags_list_for_campaigns tlfc
              JOIN tags_campaigns ON (
                  tlfc.id = tags_campaigns.tag_id
                  AND tlfc.team_id = $teamId
                  AND tags_campaigns.campaign_id = c.id
                  AND tags_campaigns.team_id = $teamId
              )
					) AS ctags
        """
    } else {
      sqls" null as ctags "
    }

    campaignTagsSubQuery
  }

  def whereClauseFromSearchParams(
                                   searchParams: SearchParams
                                 ): (SQLSyntax, SQLSyntax) = {
    var query: SQLSyntax = sqls""

    if (searchParams.name.isDefined) {
      query =
        sqls"""
              $query
              AND c.name ILIKE ${searchParams.name.get}
              """
    }

    if (searchParams.receiver_email_setting.isDefined) {
      query =
        sqls"""
              $query
             AND EXISTS( select
                         ces.id
                       from campaign_email_settings ces
                       JOIN email_settings receiver ON receiver.id = ces.receiver_email_setting_id AND ces.team_id = receiver.team_id
                       where
                     receiver.email ILIKE ${searchParams.receiver_email_setting.get}
                     and ces.campaign_id = c.id
                     and ces.team_id = c.team_id
                      and receiver.status = ${EmailSettingStatus.Active.toString}
                     LIMIT 1
                 )
              """
    }

    if (searchParams.sender_email_setting.isDefined) {
      query =
        sqls"""
              $query
              AND EXISTS( select
                          ces.id
                        from campaign_email_settings ces
                        JOIN email_settings sender ON sender.id = ces.sender_email_setting_id AND ces.team_id = sender.team_id
                        where
                      sender.email ILIKE ${searchParams.sender_email_setting.get}
                      and ces.campaign_id = c.id
                      and ces.team_id = c.team_id
                      and sender.status = ${EmailSettingStatus.Active.toString}
                      LIMIT 1
                  ) -- check if lower is required
              """
    }

    if (searchParams.status.isDefined) {
      query =
        sqls"""
              $query
              AND c.status ILIKE ${searchParams.status.get.toString}
              """
    }

    val (where_query, order_by_query) = searchParams.range match {
      case InferredQueryTimeline.Range.Before(dateTime) =>
        (sqls" $query AND c.created_at AT TIME ZONE 'UTC' < ${dateTime}", sqls" ORDER BY c.created_at desc")

      case InferredQueryTimeline.Range.After(dateTime) =>
        (sqls" $query AND c.created_at AT TIME ZONE 'UTC' > ${dateTime}", sqls" ORDER BY c.created_at asc")

    }
    (where_query, order_by_query)
  }

  def getQuerySQL(
                   permittedAccountIds: Seq[Long],
                   teamId: Long,
                   orgId: Long,
                   data: SearchQuery,
                   account: Account,
                   isInternalRequest: Boolean = false,
                   fetchType: SearchQuerySelectType.Value,
                   queryTimeline: Option[InferredQueryTimeline] = None,
                   limit: Int = 500,
                   isApiCall: Boolean = false,
                   Logger: SRLogger,
//                   shouldUseNewQuery: Boolean = false
                 ): Try[(Int, Int, SQL[?, ?])] = Try {

    val rowsPerPage = AppConfig.AllCampaignsPage.rowsPerPage
    val limit = rowsPerPage + 1
    val offset = (data.page.get - 1) * rowsPerPage

    var whereStatement: SQLSyntax = sqls""
    var searchWhereClause: SQLSyntax = sqls""
    // var havingStatement: SQLSyntax = sqls""

    if (data.query.isDefined) {

      whereStatement = getWhereClause(
        query = data.query.get,
        teamId = teamId,
        account = account,
        Logger = Logger
      )

      val search = data.query.get.search
      if (search.nonEmpty && search.get.trim.nonEmpty) {

        val param = s"%${search.get.trim}%"

        searchWhereClause =
          sqls""" AND
                    (
                      c.name ILIKE $param

                        OR

                      EXISTS( select
                         ces.id
                        from campaign_email_settings ces
                        JOIN email_settings sender ON sender.id = ces.sender_email_setting_id AND ces.team_id = sender.team_id
                        where
                        ces.campaign_id = c.id AND ces.team_id = c.team_id
                        and sender.status = ${EmailSettingStatus.Active.toString}
                        AND sender.email ILIKE $param
                        LIMIT 1
                         )
                    )
                    """
      }

    }


    val orderByStatement = getSortStatementWithLimit(
      teamId = teamId,
      sort = data.sort,
      Logger = Logger,
      queryTimeline = queryTimeline,
      limit = limit,
      isApiCall = isApiCall,
      offset = offset,
    )

    val query = campaignSelectQuery(
      show_tags = AppConfig.RollingUpdates.showCampaignTags(orgId),
      teamId = TeamId(id = teamId),
      whereClause =
        sqls"""
             WHERE
              c.team_id = $teamId

              AND c.account_id IN (${getForOwnerIds(query = data.query, permittedAccountIds = permittedAccountIds)})

              $searchWhereClause
              $whereStatement

            GROUP BY c.id, a.id,has_spam_test.exists

            ORDER BY

                 CASE
                   WHEN c.status = ${CampaignStatus.RUNNING.toString} THEN 1
                   WHEN c.status = ${CampaignStatus.ON_HOLD.toString} THEN 2
                   WHEN c.status = ${CampaignStatus.SCHEDULED.toString} THEN 3
                   WHEN c.status = ${CampaignStatus.NOT_STARTED.toString} THEN 4
                   WHEN c.status = ${CampaignStatus.STOPPED.toString} THEN 5
                   WHEN c.status = ${CampaignStatus.UNDER_REVIEW.toString} THEN 6
                   WHEN c.status = ${CampaignStatus.SUSPENDED.toString} THEN 7
                   WHEN c.status = ${CampaignStatus.ARCHIVED.toString} THEN 8

                 END, c.id DESC

            LIMIT $limit
            OFFSET $offset

        """
    )


    (
      -1,
      -1,
      query
    )

  }

  def getCampaignsV3QuerySQL(
                              permittedAccountIds: Seq[Long],
                              teamId: Long,
                              orgId: Long,
                              search_params: SearchParams,
                              isInternalRequest: Boolean = false,
                              Logger: SRLogger
                            ): Try[SQL[Nothing, NoExtractor]] = Try {

    val rowsPerPage = AppConfig.AllCampaignsPage.rowsPerPage
    val limit = rowsPerPage + 1

    val (search_query, order_by_query) = CampaignQuery.whereClauseFromSearchParams(searchParams = search_params)

    //
    //    val search = data.query.get.search
    //      if (search.nonEmpty && search.get.trim.nonEmpty) {
    //
    //        val param = s"%${search.get.trim}%"
    //
    //        searchWhereClause =
    //          sqls""" AND (
    //              c.name ILIKE $param OR
    //              esettings.email ILIKE $param
    //          ) """
    //      }
    //
    //    }


    val query = campaignSelectQuery(
      teamId = TeamId(id = teamId),
      show_tags = AppConfig.RollingUpdates.showCampaignTags(orgId),
      whereClause =
        sqls"""

      WHERE
        c.team_id = $teamId
        AND c.account_id IN (${permittedAccountIds})
        $search_query

      GROUP BY c.id, a.id,has_spam_test.exists

      ${order_by_query}

      LIMIT $limit
    """
    )


    query

  }

  def getModifiedQuerySQL(

                           teamId: Long,
                           cId: Long
                         ): Try[SQL[?, ?]] = Try {

    val query = campaignSelectQuery(
      show_tags = true,
      teamId = TeamId(id = teamId),
      whereClause =
        sqls"""
          WHERE
              c.team_id = $teamId
              AND c.id = $cId

            GROUP BY c.id, a.id,has_spam_test.exists
            """
    )

    query


  }

  def getModifiedQuerySQLWithPermission(

                                         teamId: Long,
                                         cId: Long,
                                         permittedAccountIds: Seq[Long]
                                       ): Try[SQL[?, ?]] = Try {

    val query = campaignSelectQuery(
      show_tags = true,
      teamId = TeamId(id = teamId),
      whereClause =
        sqls"""
          WHERE
              c.team_id = $teamId
              AND c.id = $cId
              AND c.account_id IN ($permittedAccountIds)

            GROUP BY c.id, a.id,has_spam_test.exists
            """
    )

    query


  }


  def getCampaignStatsQuerySQL(
                                cId: Long,
                                teamId: Long,
                                doNotContactCategoryId: Long,
                                listOfPositiveUuid: List[ReplySentimentUuid],
                                Logger: SRLogger,
                                campaignHasEmailStep: Boolean
                              ): SQL[?, ?] = {


    val DO_NOT_CONTACT_CATEGORYID: Long = doNotContactCategoryId

    val campaignHasEmailStepSQLS: SQLSyntax = if (campaignHasEmailStep) {
      sqls"""
            COUNT(cp.*) FILTER (WHERE cp.active AND cp.opted_out) AS current_opted_out,
            
            COUNT(cp.*) FILTER (WHERE cp.active AND cp.bounced) AS current_bounced,
            
            COUNT(cp.*) FILTER (WHERE cp.active and cp.to_check) AS current_to_check,
            
            COUNT(cp.*) FILTER (WHERE cp.active AND cp.invalid_email) AS current_failed_email_validation,
            
            COUNT(cp.*) FILTER (WHERE cp.active
              AND cp.completed
              AND not cp.opted_out
              AND not cp.invalid_email
            ) AS current_completed,
              
            COUNT(cp.*) FILTER (WHERE cp.active AND cp.sent AND not cp.completed AND not cp.paused AND not cp.bounced AND not cp.to_check AND not cp.invalid_email
              AND not cp.opted_out
              AND cp.prospect_category_id_custom != $DO_NOT_CONTACT_CATEGORYID
            ) AS current_in_progress,
            
            COUNT(cp.*) FILTER (WHERE cp.active
              AND not cp.sent
              -- AND cp.current_step_id IS NULL
              AND not cp.to_check AND not cp.invalid_email
              AND not cp.opted_out
              AND cp.prospect_category_id_custom != $DO_NOT_CONTACT_CATEGORYID

              AND not cp.completed
            ) AS current_unsent_prospects,
            """
    }
    else {
      sqls"""
            0 AS current_opted_out,
            
            0 AS current_bounced,
            
            COUNT(cp.*) FILTER (WHERE cp.active and cp.to_check) AS current_to_check,
            
            0 AS current_failed_email_validation,
            
            COUNT(cp.*) FILTER (WHERE cp.active AND cp.completed) AS current_completed,
            
            COUNT(cp.*) FILTER (WHERE cp.active AND cp.sent AND not cp.completed AND not cp.paused
               AND not cp.to_check AND cp.prospect_category_id_custom != $DO_NOT_CONTACT_CATEGORYID
            ) AS current_in_progress,
            
            COUNT(cp.*) FILTER (WHERE cp.active
              AND not cp.sent
              AND not cp.to_check
              AND cp.prospect_category_id_custom != $DO_NOT_CONTACT_CATEGORYID
              AND not cp.completed
            ) AS current_unsent_prospects,
            """
    }

    val rsPositiveCountSql: SQLSyntax = if (listOfPositiveUuid.nonEmpty) {

      sqls""" COUNT(cp.*) FILTER(WHERE cp.reply_sentiment_uuid in ${SQLUtils.generateSQLValuesClause(listOfPositiveUuid.map(_.uuid))}) """

    } else {

      sqls""" 0 """

    }

    val query =
      sql"""
      SELECT

        COUNT(cp.*) FILTER (WHERE cp.sent) AS total_sent,

        COUNT(cp.*) FILTER (WHERE c.open_tracking_enabled and cp.opened) AS total_opened,

        COUNT(cp.*) FILTER (WHERE cp.clicked) AS total_clicked,

        COUNT(cp.*) FILTER (WHERE cp.replied) AS total_replied,

        COUNT(cp.*) FILTER (WHERE cp.active) AS current_prospects,

        $rsPositiveCountSql AS rs_positive,

        $campaignHasEmailStepSQLS

        COUNT(cp.*) FILTER (WHERE cp.active
        --AND not cp.sent
        AND not cp.completed
        AND not cp.to_check AND not cp.invalid_email
        AND cp.prospect_category_id_custom = $DO_NOT_CONTACT_CATEGORYID
        ) AS current_do_not_contact,

        total_steps_count.count AS total_steps


      FROM campaigns c
        LEFT JOIN campaigns_prospects cp ON cp.campaign_id = c.id and cp.team_id = c.team_id

        LEFT JOIN LATERAL (
         SELECT COUNT(*) AS count FROM campaign_steps csteps WHERE csteps.campaign_id = c.id
        ) AS total_steps_count ON true

      WHERE
        c.team_id = $teamId

        AND c.id = $cId

      GROUP BY c.id, total_steps_count.count;
    """


    query

  }


  // return the (whereClause, havingGroupByClause, havingClause)
  def getStatementClause(
                          field: String,
                          operator: SearchQueryOperators.Value,
                          value: String,
                          field_type: FieldTypeEnum.Value,
                          is_custom: Boolean,
                          team_id: Long,
                          account: Account,
                          Logger: SRLogger
                        ): SearchQueryGetStatement = {

    var whereClause: Option[SQLSyntax] = None
    val havingClause: Option[SQLSyntax] = None // NOT USED

    if (is_custom) {

      throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value :: custom filters not available for campaigns")

    } else {
      field match {

        case "name" =>

          val fieldStr = if (field == "name") {

            s"c.name"

          } else {

            throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value :: invalid field name for this match")

          }

          val fieldName = SQLSyntax.createUnsafely(fieldStr.trim)

          var inputVal: Any = value

          if (field_type == FieldTypeEnum.NUMBER) {
            inputVal = ParseUtils.parseInt(value)
          }

          operator match {

            case SearchQueryOperators.CONTAINS =>

              val param = s"%${inputVal.toString}%"

              whereClause = Some(sqls" $fieldName ILIKE $param ")

            case SearchQueryOperators.NOT_CONTAINS =>

              val param = s"%${inputVal.toString}%"

              whereClause = Some(sqls" $fieldName NOT ILIKE $param ")

            case SearchQueryOperators.EQUAL =>

              whereClause = Some(sqls" $fieldName = $inputVal ")

            case SearchQueryOperators.NOT_EQUAL =>

              whereClause = Some(sqls" $fieldName != $inputVal ")

            case SearchQueryOperators.GREATER_THAN =>

              whereClause = Some(sqls" $fieldName > $inputVal ")

            case SearchQueryOperators.LESS_THAN =>

              whereClause = Some(sqls" $fieldName < $inputVal ")

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "from_email" =>

          val fieldStr = if (field == "from_email") {

            s"esettings.email"

          } else {

            throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value :: invalid field name for this match")

          }

          val fieldName = SQLSyntax.createUnsafely(fieldStr.trim)

          var inputVal: Any = value

          if (field_type == FieldTypeEnum.NUMBER) {
            inputVal = ParseUtils.parseInt(value)
          }

          operator match {

            case SearchQueryOperators.CONTAINS =>

              val param = s"%${inputVal.toString}%"

              whereClause = Some(
                sqls"""
                     EXISTS( SELECT ces.id from  campaign_email_settings ces
                     INNER JOIN email_settings esettings ON esettings.id = ces.sender_email_setting_id AND ces.team_id = esettings.team_id
                    where $fieldName ILIKE $param
                    AND ces.campaign_id = c.id
                    AND ces.team_id = c.team_id
                    and esettings.status = ${EmailSettingStatus.Active.toString}
                    )
                   """)

            case SearchQueryOperators.NOT_CONTAINS =>

              val param = s"%${inputVal.toString}%"

              whereClause = Some(
                sqls"""
                   NOT EXISTS( SELECT ces.id from  campaign_email_settings ces
                   INNER JOIN email_settings esettings ON esettings.id = ces.sender_email_setting_id AND ces.team_id = esettings.team_id
                  where $fieldName ILIKE $param
                  AND ces.campaign_id = c.id
                  AND ces.team_id = c.team_id
                  and esettings.status = ${EmailSettingStatus.Active.toString}
                  )
                 """)

            case SearchQueryOperators.EQUAL =>

              whereClause = Some(
                sqls"""
                   EXISTS( SELECT ces.id from  campaign_email_settings ces
                   INNER JOIN email_settings esettings ON esettings.id = ces.sender_email_setting_id AND ces.team_id = esettings.team_id
                  where $fieldName = $inputVal
                  AND ces.campaign_id = c.id
                  AND ces.team_id = c.team_id
                  and esettings.status = ${EmailSettingStatus.Active.toString}
                  )
                 """)

            case SearchQueryOperators.NOT_EQUAL =>

              whereClause = Some(
                sqls"""
                   NOT EXISTS( SELECT ces.id from  campaign_email_settings ces
                   INNER JOIN email_settings esettings ON esettings.id = ces.sender_email_setting_id AND ces.team_id = esettings.team_id
                  where $fieldName = $inputVal
                  AND ces.campaign_id = c.id
                  AND ces.team_id = c.team_id
                  and esettings.status = ${EmailSettingStatus.Active.toString}
                  )
                 """)

            case SearchQueryOperators.GREATER_THAN =>
              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")


            case SearchQueryOperators.LESS_THAN =>
              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")


            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "campaign_status" =>


          CampaignStatus.fromKey(value) match {

            case Failure(e) =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value :: ${e.getMessage}")

            case Success(status) =>

              operator match {

                case SearchQueryOperators.EQUAL =>

                  whereClause = Some(sqls" c.status = ${status.toString} ")

                case _ =>

                  throw new Exception(s"Invalid operator: $operator :: field: $field")
              }

          }

        case "owner_id" =>

          operator match {

            case SearchQueryOperators.EQUAL =>

              ParseUtils.parseLong(value) match {

                case None =>

                  throw new Exception("Invalid owner filter")

                case Some(0) =>

                  whereClause = Some(sqls" a.id IS NULL ")

                case Some(ownerAccountId) =>

                  whereClause = Some(sqls" a.id = $ownerAccountId ")
              }


            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "created_at" =>

          val timestamp = ParseUtils.parseLong(value).get

          val queryFieldName: SQLSyntax = field match {

            case "created_at" => sqls" c.created_at "

          }


          operator match {

            case SearchQueryOperators.GREATER_THAN =>

              whereClause = Some(sqls" $queryFieldName >= TO_TIMESTAMP($timestamp / 1000) ")

            case SearchQueryOperators.LESS_THAN =>

              whereClause = Some(sqls" $queryFieldName <= TO_TIMESTAMP($timestamp / 1000) ")

            case SearchQueryOperators.EQUAL =>

              whereClause = Some(sqls" ($queryFieldName >= TO_TIMESTAMP($timestamp / 1000) AND $queryFieldName < TO_TIMESTAMP(($timestamp + ********) / 1000)) ")

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case _ =>

          Logger.error(s"ProspectQuery FATAL filter prospects table: field name is $field")

          throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

      }
    }

    SearchQueryGetStatement(
      whereClause = whereClause,
      havingClause = havingClause
    )
  }

  def getSortStatementWithLimitV2(
                                   teamId: Long,
                                   sort: Option[SearchQuerySort],
                                   Logger: SRLogger,
                                   queryTimeline: Option[InferredQueryTimeline],
                                   limit: Int,
                                   offset: Int,
                                   maxCountLimit: Int,
                                   isApiCall: Boolean
                                 ): SQLSyntax = {
    sqls""
  }

  def getSortStatementWithLimit(
                                 teamId: Long,
                                 sort: Option[SearchQuerySort],
                                 Logger: SRLogger,
                                 queryTimeline: Option[InferredQueryTimeline] = None,
                                 limit: Int = 500,
                                 offset: Int = 0,
                                 maxCountLimit: Int = 1000,
                                 isApiCall: Boolean = false,
                               ): SQLSyntax = {

    if (sort.isEmpty) sqls"" else {

      // FIXME
      val sortD = getSortDirectionSQLS(sort = sort)
      val s = sort.get

      val orderByQuery: SQLSyntax = if (s.is_custom || s.is_numeric) {

        throw new Exception(s"Invalid sort query: custom / numeric not acceptable for campaign search: $sort")

      } else {

        // check field name against allowed default field names: To prevent sql injection
        val isValidField = CampaignColumnDef
          .allColumns(teamId = teamId)
          .filter(_.sortable)
          .exists(col => col.name == s.field.trim)

        if (!isValidField) {

          Logger.error(s"ProspectPaginationUtil FATAL order by prospects table: field name is ${s.field} :: $sort")

          throw new Exception(s"Invalid sort query: $sort")

        } else {

          // REF: https://github.com/scalikejdbc/scalikejdbc/issues/320#issuecomment-64640377
          val field = if (s.field == "latest_reply_sentiment") "latest_reply_sentiment_uuid"
          else s.field
          val fieldName = SQLSyntax.createUnsafely(field)

          sqls" ORDER BY prospects.$fieldName $sortD "
        }

      }
      orderByQuery

    }
  }

  def validateRequestBody(
                           searchQuery: SearchQuery,
                           isApiCall: Boolean,
                           Logger: SRLogger
                         ): Either[RequestBodyValidationError, SearchQuery] = {
    if (!isApiCall) {
      if (searchQuery.page.nonEmpty) {
        Right(searchQuery)
      } else {
        Left(ValidationError("Page parameter in the request body not passed"))
      }
    } else {
      Right(searchQuery)
    }
  }

  val showSoftStartQuery: SQLSyntax = {

    sqls"""
           (
                SELECT EXISTS(
                      SELECT step_type from campaign_steps cs WHERE cs.campaign_id = c.id AND step_type IN
                   (
                        ${CampaignStepType.AutoEmailStep.toKey},
                        ${CampaignStepType.ManualEmailStep.toKey}
                   )
                )
            ) as show_soft_start_setting

    """
  }

}
