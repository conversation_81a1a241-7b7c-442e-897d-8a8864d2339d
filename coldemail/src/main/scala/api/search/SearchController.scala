package api.search

import api.APIErrorResponse.ErrorResponseV3
import api.{APIErrorResponse, ApiService, ApiVersion, AppConfig, CONSTANTS, ErrorType}
import api.accounts.*
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.CampaignBasicInfo
import api.campaigns.services.{CampaignId, CampaignService}
import api.columns.{CampaignColumnDef, EmailThreadColumnDef, ProspectAccountsColumnDef}
import api.emails.EmailThreadDAO
import api.prospects.*
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.ProspectAccountsId
import api.prospects.service.ProspectAccountListingError.PaginationError
import api.prospects.service.{ProspectAccountError, ProspectAccountListingError, ProspectAccountPaginationService, ProspectAccountService}
import api.tasks.services.{TaskDaoService, TaskService}
import play.api.libs.json.*
import play.api.mvc.{<PERSON><PERSON><PERSON><PERSON>, BaseController, ControllerComponents}
import utils.{Help<PERSON>, <PERSON>Logger, StringUtilsV2}
import utils.helpers.LogHelpers
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

case class ProspectAccountAPIParams(
                                     range: InferredQueryTimeline.Range,
                                     is_first: Boolean
                                   )

class SearchController(
                        protected val controllerComponents: ControllerComponents,
                        campaignService: CampaignService,
                        prospectDAOService: ProspectDAOService,
                        emailThreadDAO: EmailThreadDAO,
                        taskService: TaskService,
                        permissionUtils: PermissionUtils,
                        val prospectAccountDAO: ProspectAccountDAO1,
                        prospectAccountPaginationService: ProspectAccountPaginationService,
                        prospectAccountService: ProspectAccountService,
                        prospectQuery: ProspectQuery,
                        apiService: ApiService,
                        srRollingUpdateCoreService: SrRollingUpdateCoreService
                      ) extends BaseController {

  private implicit val ec: ExecutionContext = controllerComponents.executionContext

  def validateProspectAccountListingQueryParams(
                                                 parsedParams: Map[String, Vector[String]]
                                               ): Try[ProspectAccountAPIParams] = {
    for {
      (is_first: Boolean, range: InferredQueryTimeline.Range) <- Helpers.getTimeLineRange(parsedParams = parsedParams)

    } yield {
      ProspectAccountAPIParams(
        range,
        is_first
      )
    }
  }

  def getColumns(
                  searchType: String,
                  onlyCustom: Option[Boolean],
                  tid: Option[Long]
                ) = permissionUtils.checkPermission(
    version = "v2",
    permission = PermType.JUST_LOGGEDIN,
    tidOpt = tid
  ) { (request: PermissionRequest[AnyContent]) =>
    val Logger = request.Logger
    val Res = request.Response

    val ta = request.actingTeamAccount

    if (tid.isEmpty || ta.isEmpty) {

      Res.ForbiddenError("You are not authorized")

    } else {

      searchType match {

        case "campaigns" =>

          val filterableColumns = CampaignColumnDef
            .allColumns(teamId = ta.get.team_id)
            .filter(_.filterable)

          Res.Success("Campaign columns found", Json.obj(
            "all_columns_for_filter" -> filterableColumns
          ))


        case "prospect_accounts" =>

          val allColumns = ProspectAccountsColumnDef.allColumns(teamId = ta.get.team_id)

          val customColumns = allColumns.filter(_.is_custom)

          val cols = onlyCustom match {

            case Some(true) => customColumns

            case _ => allColumns
          }

          val filterableColumns = cols
            .filter(_.filterable)

          Res.Success("Accounts Columns found", Json.obj(
            "columns" -> cols,
            "all_columns_for_filter" -> filterableColumns,
            "ignore_fields_while_add_or_upload" -> ProspectAccountsColumnDef.ignoreFieldsOnAddOrUploadProspectAccount,
            "ignore_fields_while_update" -> ProspectAccountsColumnDef.ignoreFieldsOnUpdateProspectAccount
          ))

        case "inbox_threads" =>

          val cols = EmailThreadColumnDef.allColumns(teamId = ta.get.team_id)
          val filterableColumns = cols
            .filter(_.filterable)
          Res.Success("Inbox columns found", Json.obj(
            "columns" -> cols,
            "all_columns_for_filter" -> filterableColumns
          ))

        case _ =>
          Res.BadRequestError("Invalid columns request")

      }

    }

  }


  /*
  def search(searchType: String, tid: Option[Long]) = checkPermission(
    version = "v2",
    permission = PermType.JUST_LOGGEDIN,
    tidOpt = tid
  )
    .async(parse.json) { request =>
      val Logger = request.Logger
      val Res = request.Response

      val l = request.loggedinAccount // logged in account
      val taOpt = request.actingTeamAccount
      val org = request.loggedinAccount.org
      val orgId = org.id

      if (tid.isEmpty || taOpt.isEmpty) {

        Future.successful(Res.ForbiddenError("You are not authorized"))

      } else {

        val ta = taOpt.get

        // _ at end denotes a curried function ::
        // REF: https://dzone.com/articles/currying-functions-in-scala-1
        val getPermittedAccountIds = PermissionMethods
          .getPermittedAccountIdsForAccountAndPermission(
            loggedinAccount = l,
            isAllView = true,
            actingTeamId = ta.team_id,
            actingAccountId = ta.user_id,
            version = "v2",
            Logger = Logger
          )


        searchType match {

          case "campaigns" =>

            val permittedAccountIds = getPermittedAccountIds(PermType.VIEW_CAMPAIGNS)

            request.body.validate[SearchQuery] match {

              case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

              case JsSuccess(data, _) =>

                Future.fromTry(
                  campaignDAO.query(
                    accountIds = permittedAccountIds,
                    teamId = ta.team_id,
                    orgId = orgId,
                    loggedinAccountId = l.id,
                    data = data,
                    account = l,
                    Logger = Logger
                  )
                )
                  .map(campaigns =>
                    Res.Success("Campaigns found", Json.obj("campaigns" -> campaigns))
                  )
                  .recover { case e =>
                    Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))
                  }

            }

          case "prospect_accounts" =>

            val permittedAccountIds = getPermittedAccountIds(PermType.VIEW_PROSPECTS)

            request.body.validate[SearchQuery] match {

              case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

              case JsSuccess(data, _) =>

                prospectAccountDAO.query(
                  accountIds = permittedAccountIds,
                  teamId = ta.team_id,
                  orgId = orgId,
                  data = data,
                  account = l,
                  Logger = Logger
                ).map { prospect_accounts =>
                  Res.Success("Prospect Accounts found", prospect_accounts)
                }
                  .recover { case e => Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}", e = Some(e)) }
            }

          case "prospects" =>

            val permittedAccountIds = getPermittedAccountIds(PermType.VIEW_PROSPECTS)

            request.body.validate[SearchQuery] match {

              case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

              case JsSuccess(data, _) =>

                prospectDAO.query(
                  accountIds = permittedAccountIds,
                  teamId = ta.team_id,
                  orgId = orgId,
                  loggedinAccountId = l.id,
                  prospectQuery = data,
                  account = l,
                  isInternalRequest = !request.isApiCall,
                  Logger = Logger
                )
                  .map { prospects =>
                    Res.Success("Prospects found", prospects)
                  }
                  .recover { case e => Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e)) }
            }


          case "inbox_threads" =>

            val permittedAccountIds = getPermittedAccountIds(PermType.VIEW_CHANNELS)

            request.body.validate[SearchQuery] match {

              case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

              case JsSuccess(data, _) =>

                val showAutoReschedulingOption = org.org_metadata.ff_autoreply_rescheduling.getOrElse(false)

                emailThreadDAO.query(
                  accountIds = permittedAccountIds,
                  teamId = ta.team_id,
                  orgId = orgId,
                  data = data,
                  account = l,
                  showAutoReschedulingOption = showAutoReschedulingOption,
                  Logger = Logger
                ).map { replies =>
                  Res.Success("Email conversations found", Json.toJson(replies))
                }
                  .recover { case e => Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}", e = Some(e)) }
            }

          case _ =>
            Future.successful(Res.BadRequestError("Invalid search request"))

        }

      }


    }
  */

  def searchCampaigns(tid: Option[Long]) = permissionUtils.checkPermission(
      permission = PermType.VIEW_CAMPAIGNS,
      localOrStagingApiTestAccess = true,
      tidOpt = tid
    )
    .async(parse.json) { request =>
      val Logger = request.Logger
      val Res = request.Response

      val l = request.loggedinAccount // logged in account
      val taOpt = request.actingTeamAccount
      val org = request.loggedinAccount.org
      val orgId = org.id
      val isApiCall = request.isApiCall

      if (tid.isEmpty || taOpt.isEmpty) {

        Future.successful(Res.ForbiddenError("You are not authorized"))

      } else {

        val ta = taOpt.get


        request.body.validate[SearchQuery] match {

          case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

          case JsSuccess(data, _) =>

            prospectQuery.validateRequestBody(
              searchQuery = data,
              isApiCall = isApiCall,
              Logger = Logger
            ) match {
              case Left(error) =>
                error match {
                  case RequestBodyValidationError.ValidationError(err) =>
                    Future {
                      Res.BadRequestError(
                        message = err
                      )
                    }
                }
              case Right(searchQuery) =>
                Future.fromTry(
                    campaignService.query(
                      accountIds = request.permittedAccountIds,
                      teamId = ta.team_id,
                      orgId = orgId,
                      loggedinAccountId = l.internal_id,
                      data = searchQuery,
                      account = l,
                      Logger = Logger
                    )
                  )
                  .map(campaigns => {

                    val res = Res.Success("Campaigns found", Json.obj("campaigns" -> campaigns._1,
                      "has_more" -> campaigns._2))


                    if (request.isSupportAppModeRequest) {

                      res

                    } else {

                      res
                        /*
                     13 Jun 2023 - added for audit SECAUDIT
                     we are just extending the cookie expiry time, using this frequently called api
                     */
                        .withSession("account_id" -> request.loggedinAccount.internal_id.toString)
                    }


                  })
                  .recover { case e =>
                    Res.ServerError(err = e)
                  }
            }
        }

      }


    }


  def searchProspectAccounts(tid: Option[Long]) = permissionUtils.checkPermission(
      permission = PermType.VIEW_PROSPECTS,
      tidOpt = tid
    )
    .async(parse.json) { request =>
      val Logger = request.Logger
      val Res = request.Response

      val l = request.loggedinAccount // logged in account
      val taOpt = request.actingTeamAccount
      val org = request.loggedinAccount.org
      val orgId = org.id
      val isApiCall = request.isApiCall

      if (tid.isEmpty || taOpt.isEmpty) {

        Future.successful(Res.ForbiddenError("You are not authorized"))

      } else {

        val ta = taOpt.get

        request.body.validate[SearchQuery] match {

          case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

          case JsSuccess(data, _) =>

            ProspectAccountQuery.validateRequestBody(
              searchQuery = data,
              isApiCall = isApiCall,
              Logger = Logger
            ) match {
              case Left(error) =>
                error match {
                  case PaginationError(error_list) =>
                    Future {
                      Res.BadRequestError(
                        message = error_list.head
                      )
                    }
                }
              case Right(searchQuery) =>
                prospectAccountDAO.query(
                  accountIds = request.permittedAccountIds.map(p => AccountId(p)),
                  teamId = TeamId(ta.team_id),
                  orgId = OrgId(orgId),
                  data = searchQuery,
                  account = l,
                  isApiCall = isApiCall,
                  Logger = Logger
                ) match {
                  case Failure(e) => Future.successful(Res.ServerError(e))
                  case Success(prospectAccountsResponse) => Future.successful(Res.Success("Prospect Accounts found", data = Json.toJson(prospectAccountsResponse)))
                }
            }
        }


      }


    }

  def searchProspectAccountsForPublicApi(tid: Option[Long]) = permissionUtils.checkPermission(
    permission = PermType.VIEW_PROSPECTS,
    tidOpt = tid,
    apiAccess = true,
    version = "v3"
  ).async { request =>

    val Res = request.Response

    val loggedInAccount = request.loggedinAccount // logged in account
    val taOpt = request.actingTeamAccount
    val org = request.loggedinAccount.org
    val requestBody = request.body.asJson
    val isApiCall = request.isApiCall
    val params = request.uri
    val parsedParams: Map[String, Vector[String]] = StringUtilsV2.getParams(params)
    val queryParams = validateProspectAccountListingQueryParams(parsedParams = parsedParams)
    val permittedAccountIds = request.permittedAccountIds.map(p => AccountId(p))
    val orgId = OrgId(org.id)
    given logger: SRLogger = request.Logger

    Future {
      if (taOpt.isEmpty) {

        Res.ForbiddenError("You are not authorized")

      } else {
        val ta = taOpt.get
        val teamId = TeamId(ta.team_id)

        val requestBodyData = requestBody match {
          case None => Right(SearchQuery(
            query = None,
            page = None,
            custom_flags = None,
            sort = None,
            is_campaign = None,
            older_than = None,
            newer_than = None
          ))
          case Some(searchQuery) =>
            requestBody.get.validate[SearchQuery] match {

              case JsError(err) =>
                Left(err)

              case JsSuccess(data, _) =>
                Right(data)
            }
        }

        requestBodyData match {
          case Left(err) =>
            Res.JsValidationError(errors = err, requestBody = requestBody)
          case Right(searchQuery: SearchQuery) =>
            queryParams match {
              case Failure(exception) => Res.ServerError(exception)

              case Success(filters) =>
                prospectAccountPaginationService.findProspectAccountListingPage(
                  teamId = teamId,
                  filters = filters,
                  permittedAccountIds = permittedAccountIds,
                  orgId = orgId,
                  loggedInAccount = loggedInAccount,
                  isApiCall = isApiCall,
                  data = searchQuery,
                  params = params
                ) match {
                  case Left(error) =>
                    error match {
                      case ProspectAccountListingError.DBFailure(err) =>
                        Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${err.getMessage}", e = Some(err))
                      case ProspectAccountListingError.PaginationError(error_list) =>
                        Res.BadRequestError(
                          message = "BadRequest Error while getting Prospect Account",
                          version = ApiVersion.V3,
                          errorResponse = error_list.map(e => ErrorResponseV3(
                            error_type = ErrorType.BAD_REQUEST,
                            message = e
                          ))
                        )
                    }
                  case Right(prospectAccountListingApiResult) =>
                    Res.Success(
                      message = "ProspectAccount found",
                      data = Json.obj(
                        "accounts" -> Json.toJson(prospectAccountListingApiResult.prospectAccount.map(p => apiService.getProspectAccountResponse(
                          prospectAccountId = ProspectAccountsId(id = p.id),
                          teamId = teamId,
                          orgId = orgId
                        ).get)),
                        "links" -> prospectAccountListingApiResult.links
                      ),
                      apiVersion = ApiVersion.V3.toString
                    )
                }
            }
        }
      }
    }
  }

  def searchFailedTasks(tid: Option[Long], cid: String) = permissionUtils.checkPermission(
      permission = PermType.VIEW_TASKS,
      tidOpt = tid
    )
    .async { request =>
      val Logger = request.Logger
      val Res = request.Response

      if (request.actingTeamAccount.isEmpty) {
        Future.successful(Res.ForbiddenError("You are not authorized"))
      }
      else {
        Try {
          cid.toLong
        } match {
          case Failure(e) =>
            Future.successful(Res.BadRequestError(e.getMessage))

          case Success(campaignId) =>
            taskService.fetchFailedTasksOfCampaign(
              campaignId = CampaignId(campaignId),
              teamId = TeamId(request.actingTeamAccount.get.team_id),
              orgId = OrgId(request.loggedinAccount.org.id)
            )(Logger) match {
              case Failure(e) =>
                Future.successful(Res.BadRequestError(e.getMessage))

              case Success(tasks) =>
                Future.successful(Res.Success("Found failed tasks", Json.obj("failed_tasks" -> tasks)))
            }
        }

      }
    }

  // # from FE api/v2/search/prospects
  def searchProspects(tid: Option[Long], to_create_task: Option[Boolean]) = permissionUtils.checkPermission(
      permission = PermType.VIEW_PROSPECTS,
      tidOpt = tid
    )
    .async(parse.json) { request =>
      val Logger = request.Logger
      val Res = request.Response

      val l = request.loggedinAccount // logged in account
      val taOpt = request.actingTeamAccount
      val org = request.loggedinAccount.org
      val orgId = org.id
      val isApiCall = request.isApiCall

      if (tid.isEmpty || taOpt.isEmpty) {

        Future.successful(Res.ForbiddenError("You are not authorized"))

      } else {

        val ta = taOpt.get

        request.body.validate[SearchQuery] match {

          case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

          case JsSuccess(data, _) =>

            prospectQuery.validateRequestBody(
              searchQuery = data,
              isApiCall = isApiCall,
              Logger = Logger
            ) match {
              case Left(error) =>
                error match {
                  case RequestBodyValidationError.ValidationError(err) =>
                    Future {
                      Res.BadRequestError(
                        message = err
                      )
                    }
                }
              case Right(searchQuery) =>

                val permitted_account_ids: Seq[Long] = if (to_create_task.isDefined && to_create_task.get) {

                  val permittedAccountIdsForEditProspects = PermissionMethods.getPermittedAccountIdsForAccountAndPermission(
                    loggedinAccount = request.loggedinAccount,
                    actingAccountId = ta.user_id,
                    actingTeamId = ta.team_id,
                    version = "v2",
                    Logger = Logger
                  )(PermType.EDIT_PROSPECTS)

                  permittedAccountIdsForEditProspects

                } else request.permittedAccountIds

                prospectDAOService.query(
                    accountIds = permitted_account_ids,
                    teamId = ta.team_id,
                    orgId = orgId,
                    loggedinAccountId = l.internal_id,
                    prospectQueryData = searchQuery,
                    account = l,
                    isInternalRequest = !request.isApiCall,
                    Logger = Logger
                  )
                  .map { prospects =>
                    val res = Res.Success("Prospects found", prospects)


                    if (request.isSupportAppModeRequest) {

                      res

                    } else {

                      res

                        /*
                     13 Jun 2023 - added for audit SECAUDIT
                     we are just extending the cookie expiry time, using this frequently called api
                     */
                        .withSession("account_id" -> request.loggedinAccount.internal_id.toString)

                    }


                  }
                  .recover { case e => Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e)) }
            }
        }


      }


    }

  def searchCount(searchType: String, tid: Option[Long]) = permissionUtils.checkPermission(
      version = "v2",
      permission = PermType.JUST_LOGGEDIN,
      tidOpt = tid
    )
    .async(parse.json) { request =>
      val Logger = request.Logger
      val Res = request.Response

      val orgId = request.loggedinAccount.org.id
      val l = request.loggedinAccount // logged in account
      val isApiCall = request.isApiCall
      val taOpt = request.actingTeamAccount

      if (tid.isEmpty || taOpt.isEmpty) {

        Future.successful(Res.ForbiddenError("You are not authorized"))

      } else {

        val ta = taOpt.get

        // _ at end denotes a curried function ::
        // REF: https://dzone.com/articles/currying-functions-in-scala-1
        val getPermittedAccountIds = PermissionMethods
          .getPermittedAccountIdsForAccountAndPermission(
            loggedinAccount = l,
            actingTeamId = ta.team_id,
            actingAccountId = ta.user_id,
            version = "v2",
            Logger = Logger
          )


        searchType match {

          case "prospects" =>

            val permittedAccountIds = getPermittedAccountIds(PermType.VIEW_PROSPECTS)

            request.body.validate[SearchQuery] match {

              case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

              case JsSuccess(data, _) =>

                prospectQuery.validateRequestBody(
                  searchQuery = data,
                  isApiCall = isApiCall,
                  Logger = Logger
                ) match {
                  case Left(error) =>
                    error match {
                      case RequestBodyValidationError.ValidationError(err) =>
                        Future {
                          Res.BadRequestError(
                            message = err
                          )
                        }
                    }
                  case Right(searchQuery) =>
                    prospectDAOService.countQuery(
                        accountIds = permittedAccountIds,
                        teamId = ta.team_id,
                        orgId = orgId,
                        loggedinAccountId = l.internal_id,
                        prospectQueryData = searchQuery,
                        account = l,
                        isInternalRequest = !request.isApiCall,
                        Logger = Logger
                      )
                      .map { prospects =>
                        Res.Success("Prospects count found", prospects)
                      }
                      .recover { case e => Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e)) }
                }
            }


          case _ =>
            Future.successful(Res.BadRequestError("Invalid search request"))

        }

      }


    }


  def autocomplete(searchType: String, tid: Option[Long]) = permissionUtils.checkPermission(
      version = "v2",
      permission = PermType.JUST_LOGGEDIN,
      tidOpt = tid
    )
    .async(parse.json) { request =>
      val Logger = request.Logger
      val Res = request.Response


      val l = request.loggedinAccount // logged in account
      val taOpt = request.actingTeamAccount

      if (tid.isEmpty || taOpt.isEmpty) {

        Future.successful(Res.ForbiddenError("You are not authorized"))

      } else {

        val ta = taOpt.get

        val getPermittedAccountIds = PermissionMethods
          .getPermittedAccountIdsForAccountAndPermission(
            loggedinAccount = l,
            actingTeamId = ta.team_id,
            actingAccountId = ta.user_id,
            version = "v2",
            Logger = Logger
          )

        searchType match {

          case "prospect_accounts" =>

            val permittedAccountIds = getPermittedAccountIds(PermType.VIEW_PROSPECTS)

            request.body.validate[AutoCompleteQuery] match {

              case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

              case JsSuccess(data, _) =>

                prospectAccountDAO.autoCompleteProspectAccounts(
                  permittedAccountIds = permittedAccountIds,
                  teamId = ta.team_id,
                  search = data.search
                ) match {

                  case Failure(e) =>

                    Future.successful(Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = None))

                  case Success(prospect_accounts) =>

                    Future.successful(Res.Success("Prospect Accounts found", Json.obj("prospect_accounts" -> prospect_accounts)))

                }
            }

          case _ =>

            Future.successful(Res.BadRequestError("Invalid search request"))

        }
      }
    }

}
