package api.search

import api.AppConfig
import api.accounts.models.OrgId
import api.accounts.{Account, PermissionMethods}
import api.campaigns.models.CampaignStepType
import api.columns.{FieldTypeEnum, ProspectColumnDef}
import api.prospects.models.ProspectCategory
import api.prospects.{InferredQueryTimeline, SearchQuerySelectType}
import api.search.CampaignQuery.getSortStatementWithLimit
import api.search.RequestBodyValidationError.ValidationError
import org.joda.time.DateTime
import play.api.libs.json.JsValue
import scalikejdbc.{SQL, SQLSyntax}
import utils.{Helpers, ParseUtils, SRLogger}
import scalikejdbc._
import utils.pagination.GetDataErrorTrait
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus
import utils.linkedin.LinkedinHelperFunctions

import scala.util.{Failure, Success, Try}

case class ProspectFilterSelectAndJoinClauses(
                                               prospectSelectInnerSQL: SQLSyntax,
                                               prospectSelectOuterSQL: SQLSyntax,
                                               prospectJoinInner: SQLSyntax,
                                               prospectJoinOuter: SQLSyntax
                                             )

object ProspectQuery {

  // we send the step/holiday details by default, unless we explicitly get the flag not to send these
  val defaultForFindActiveCampaignDetailedWithStepAndHolidayData: Boolean = true


  final def getMagicColsLeftLateralJoin: SQLSyntax = {

    sqls"""
          LEFT JOIN LATERAL (
            SELECT
              json_agg(json_build_object(
               'column_output', cdp.magic_prompt_generated_output,
                'failed_message', cdp.failed_message,
                'column_name', cd.name,
                'status', cdp.status
              )) AS prospect_magic_columns
            FROM
              column_defs_prospects AS cdp
              JOIN column_defs AS cd ON cdp.column_id = cd.id
                AND cd.team_id = cdp.team_id
            WHERE
              cdp.prospect_id = prospects.id
              AND cdp.team_id = prospects.team_id
          ) AS magic_columns ON TRUE
        """

  }


  final def getMagicColsLeftLateralJoinV2: SQLSyntax = {

    sqls"""
          LEFT JOIN LATERAL (
            SELECT
              json_agg(json_build_object(
               'column_output', cdp.magic_prompt_generated_output,
                'failed_message', cdp.failed_message,
                'column_name', cd.name,
                'status', cdp.status
              )) AS prospect_magic_columns
            FROM
              column_defs_prospects AS cdp
              JOIN column_defs AS cd ON cdp.column_id = cd.id
                AND cd.team_id = cdp.team_id
            WHERE
              cdp.prospect_id = pt1.id
              AND cdp.team_id = pt1.team_id
          ) AS magic_columns ON TRUE
        """

  }

  final def _getProspectJustSelectSQLSyntaxV2(

                                               isInternalRequestSelectForActiveCampaigns: Option[SQLSyntax] = None,
                                               getProspectTags: Boolean = true,
                                               isForSomeCampaign: Option[Boolean] = None
                                             ): SQLSyntax = {
    // Todo : Unit test the whole query generation , at all the places where this is called
    // do not need tags in CanmpaignProspect.fetchProspects
    val prospectTagSelect = if (getProspectTags) sqls" COALESCE(ptags.prospecttags, '[]') " else sqls" null "
    val magicColsSelect = sqls" COALESCE(magic_columns.prospect_magic_columns, '[]') "

    val activeCampaignsSQL: SQLSyntax = isInternalRequestSelectForActiveCampaigns.getOrElse(sqls" null ")

    val currentStepTypeSQL: SQLSyntax = isForSomeCampaign.map(x => if(x) {sqls"cp.current_step_type,"}else {sqls""}).getOrElse(sqls"")

    // todo change below fields for prospects_emails join
    /*
    * email, email_domain, email_checked, invalid_email, email_bounced, force_send_invalid_email
    * */

    sqls"""
        SELECT
         -- DISTINCT (prospects.id),

         prospects.id,

          a.id AS owner_id,
          a.email AS owner_email,
          a.uuid AS owner_uuid,
          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,

          prospects.uuid AS prospect_uuid,

          prospects.team_id,

          prospects.first_name,
          prospects.last_name,
          pe.email,
          pe.email_domain,
          prospects.ta_id as ta_id,

          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,

          prospect_lists.name AS list,
          prospects.list_id,
          prospects.company,
          prospects.city,
          prospects.country,
          prospects.timezone,

          prospects.created_at,
          prospects.updated_at,
          prospects.last_contacted_at,
          prospects.last_contacted_at_phone,
          prospects.last_replied_at,
          prospects.last_opened_at,
          prospects.total_opens,
          prospects.total_clicks,
          prospects.custom_fields,
          prospects.will_delete,
          pe.email_bounced,
          pe.force_send_invalid_email,
          pcat.name as prospect_category_custom,
          pcat.label_color as prospect_category_label_color,
          pcat.id as prospect_category_id_custom,

          prospects.prospect_source,
          prospects.prospect_account_id as prospect_account_id,
          pa.uuid as prospect_account_uuid,
          pa.name as prospect_account_name,
          $prospectTagSelect as tags,
          $magicColsSelect as magic_columns,

          $activeCampaignsSQL as active_campaigns,

          prospects.state,
          prospects.job_title,
          prospects.phone,
          prospects.phone_2,
          prospects.phone_3,
          prospects.linkedin_url,
          ${currentStepTypeSQL}
          prospects.latest_reply_sentiment_uuid,
          prospects.latest_task_done_at,
          ccl.completed_at as last_call_made_at
    """

  }


  final def _getProspectJustSelectOuterSQL(
                                            isInternalRequestSelectForActiveCampaigns: Option[SQLSyntax] = None,
                                            getProspectTags: Boolean = true,
                                            isForSomeCampaign: Option[Boolean] = None
                                          ) = {
    val prospectTagSelect = if (getProspectTags) sqls" COALESCE(ptags.prospecttags, '[]') " else sqls" null " //OUTER SELECT:: prospects_tags
    val magicColsSelect = sqls" COALESCE(magic_columns.prospect_magic_columns, '[]') " //OUTER SELECT:: magic columns

    val activeCampaignsSQL: SQLSyntax = isInternalRequestSelectForActiveCampaigns.getOrElse(sqls" null ") //OUTER SELECT:: cpa campaigns_prospects

    val currentStepTypeSQL: SQLSyntax = isForSomeCampaign.map(x => if (x) {
      sqls"pt1.current_step_type,"
    } else {
      sqls""
    }).getOrElse(sqls"")

    sqls"""
          pt1.id,
          a.id AS owner_id,
          a.email AS owner_email,
          a.uuid AS owner_uuid,
          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,

          pt1.prospect_uuid,

          pt1.team_id,

          pt1.first_name,
          pt1.last_name,
          pt1.email,
          pt1.email_domain,
          pt1.ta_id as ta_id,

          pt1.invalid_email,

          prospect_lists.name AS list,
          pt1.list_id,
          pt1.company,
          pt1.city,
          pt1.country,
          pt1.timezone,

          pt1.created_at,
          pt1.updated_at,
          pt1.last_contacted_at,
          pt1.last_contacted_at_phone,
          pt1.last_replied_at,
          pt1.last_opened_at,
          pt1.total_opens,
          pt1.total_clicks,
          pt1.custom_fields,
          pt1.will_delete,
          pt1.email_bounced,
          pt1.force_send_invalid_email,
          pcat.name as prospect_category_custom,
          pcat.label_color as prospect_category_label_color,
          pcat.id as prospect_category_id_custom,

          pt1.prospect_source,
          pt1.prospect_account_id as prospect_account_id,
          pa.uuid as prospect_account_uuid,
          pa.name as prospect_account_name,

          $prospectTagSelect as tags,
          $magicColsSelect as magic_columns,

          $activeCampaignsSQL as active_campaigns,

          pt1.state,
          pt1.job_title,
          pt1.phone,
          pt1.phone_2,
          pt1.phone_3,
          pt1.linkedin_url,
          ${currentStepTypeSQL}
          pt1.latest_reply_sentiment_uuid,
          pt1.latest_task_done_at,
          ccl.completed_at as last_call_made_at
        """
  }

  //INNER-SELECT-QUERY
  final def _getProspectJustSelectInnerSQL(
                                            isForSomeCampaign: Option[Boolean] = None
                                          ): SQLSyntax = {

    val currentStepTypeSQL: SQLSyntax = isForSomeCampaign.map(x => if(x) {sqls"cp.current_step_type,"}else {sqls""}).getOrElse(sqls"")

    sqls"""
        SELECT

         prospects.id,
          prospects.uuid AS prospect_uuid,
          prospects.account_id,

          prospects.team_id,

          prospects.first_name,
          prospects.last_name,
          pe.email,
          pe.email_domain,
          prospects.ta_id as ta_id,

          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,

          prospects.list_id,
          prospects.list,
          prospects.company,
          prospects.city,
          prospects.country,
          prospects.timezone,
          prospects.sending_holiday_calendar_id,

          prospects.created_at,
          prospects.updated_at,
          prospects.last_contacted_at,
          prospects.last_contacted_at_phone,
          prospects.last_replied_at,
          prospects.last_opened_at,
          prospects.total_opens,
          prospects.total_clicks,
          prospects.custom_fields,
          prospects.will_delete,
          pe.email_bounced,
          pe.force_send_invalid_email,

          prospects.prospect_source,
          prospects.prospect_account_id as prospect_account_id,
          prospects.prospect_category_id_custom,

          prospects.state,
          prospects.job_title,
          prospects.phone,
          prospects.phone_2,
          prospects.phone_3,
          prospects.linkedin_url,
          ${currentStepTypeSQL}
          prospects.latest_reply_sentiment_uuid,
          prospects.latest_task_done_at
    """

  }


  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  //FIXME add team_id on join for campaign_prospects
  final def _genProspectSelectAndJoinSQLsV2(

                                             isInternalRequest: Boolean,

                                             // if we want to populate prospect.internal.active_campaigns array
                                             findActiveCampaignsIfInternalRequest: Boolean = false,

                                             // specific campaign page
                                             // for queries where we are only finding prospects for a campaign-id
                                             specificCampaignPageCampaignId: Option[Long] = None,

                                             findActiveCampaignDetailedWithStepAndHolidayData: Boolean = ProspectQuery.defaultForFindActiveCampaignDetailedWithStepAndHolidayData,
                                             org_id: Option[OrgId] = None,
//                                             emailNotCompulsoryEnabled: Option[Boolean]
                                           ): (SQLSyntax, SQLSyntax) = {


    val isForSpecificCampaign: Boolean = specificCampaignPageCampaignId.isDefined


    var isInternalRequestSelectForActiveCampaigns: Option[SQLSyntax] = None

    var isInternalRequestJoinForActiveCampaigns = sqls""

    if (isInternalRequest && findActiveCampaignsIfInternalRequest) {

      isInternalRequestSelectForActiveCampaigns = Some(sqls" COALESCE(cpa.active_campaigns, '[]') ")

      isInternalRequestJoinForActiveCampaigns = {


        val isCampaignLateralJoinWhere: SQLSyntax = if (isForSpecificCampaign) {
          sqls" AND cpp.campaign_id = ${specificCampaignPageCampaignId.get} "
        } else sqls""


        val lateralJoinQuery = if (findActiveCampaignDetailedWithStepAndHolidayData) {

          // NOTE: 5th November 2021:
          // interval '${AppConfig.emailScheduleIntervalInMinutes} minutes' sqls was failing, we tried everything we could think of.
          // it was because ${AppConfig.emailScheduleIntervalInMinutes} was being passed as an external string / int ultimately.
          // below we are forcing AppConfig.emailScheduleIntervalInMinutes to be treated as a SQLSyntax query and not as a string / int etc. This change made it work.
          val intervalTime: SQLSyntax = SQLSyntax.createUnsafely(
            AppConfig.emailScheduleIntervalInMinutes.toString
          )

          // current date with start of day time in relevant timezone
          val currentTime: SQLSyntax = {
            sqls"""
            date_trunc('day',

              current_timestamp
              AT TIME ZONE (
                CASE

                WHEN
                  (prospects.timezone IS NOT NULL AND prospects.timezone != '')

                THEN prospects.timezone

                ELSE campaigns.timezone

                END

              )
              AT TIME ZONE 'UTC' + interval '$intervalTime minutes'

            )

            """
          }


          sqls"""
            left join lateral (
              select

              json_agg(
                json_build_object(

                  'to_check', cpp.to_check,
                  'to_check_fields', cpp.to_check_fields,

                  'total_opens_in_campaign', cpp.total_opens,
                  'total_clicks_in_campaign', cpp.total_clicks,

                  'sent', cpp.sent,
                  'opened', cpp.opened,
                  'replied', cpp.replied,
                  'replied_marked_by_adminid', cpp.replied_marked_by_adminid,
                  'opted_out', cpp.opted_out,
                  'bounced', cpp.bounced,
                  'completed', cpp.completed,
                  'will_resume_at', cpp.will_resume_at,
                  'will_resume_at_tz', cpp.will_resume_at_tz,
                  'clicked', cpp.clicked,
                  'invalid_email', cpp.invalid_email,
                  'auto_reply', cpp.auto_reply,
                  'out_of_office', cpp.out_of_office,
                  'campaign_name', campaigns.name,
                  'campaign_id', campaigns.id,
                  'campaign_uuid', campaigns.uuid,

                  'step', campaign_steps.label,

                  'sending_holiday_calendar_name', shc.calendar_name,
                  'current_holiday_name', shcd.holiday_name,
                  'current_step_type',cpp.current_step_type,
                  'latest_task_done_at',cpp.latest_task_done_or_skipped_at,
                  'sender_email', sender.email

                  )
                ) as active_campaigns

              from campaigns_prospects cpp
              INNER JOIN campaigns ON campaigns.id = cpp.campaign_id and cpp.team_id = campaigns.team_id
              LEFT JOIN campaign_email_settings ces ON (
                   cpp.current_campaign_email_settings_id = ces.id
                   AND
                   cpp.campaign_id = ces.campaign_id
                   AND
                   cpp.team_id = ces.team_id
              )
              LEFT JOIN email_settings sender ON (
                  sender.id = ces.sender_email_setting_id
                  AND
                  sender.team_id = ces.team_id
                  and sender.status = ${EmailSettingStatus.Active.toString}
              )
              LEFT JOIN campaign_steps ON campaign_steps.id = cpp.current_step_id

              LEFT JOIN sending_holiday_calendars shc on (

                shc.id = (
                  CASE WHEN
                    prospects.sending_holiday_calendar_id is not null
                  THEN
                    prospects.sending_holiday_calendar_id
                  ELSE
                    campaigns.sending_holiday_calendar_id
                  END
                )

              )
              LEFT JOIN sending_holiday_calendar_dates shcd ON (

                shcd.sending_holiday_calendar_id = shc.id

                and check_if_holiday(

                      -- holiday start
                      shcd.start_date_utc,

                      -- holiday end
                      shcd.end_date_utc,

                      -- current date with start of day time in relevant timezone
                      $currentTime

                )
              )

              where
              cpp.prospect_id = prospects.id
              AND cpp.active

              $isCampaignLateralJoinWhere

            ) as cpa on true
      """
        } else {

          sqls"""
              left join lateral (
                select

                json_agg(
                  json_build_object(

                    'to_check', cpp.to_check,
                    'to_check_fields', cpp.to_check_fields,

                    'total_opens_in_campaign', cpp.total_opens,
                    'total_clicks_in_campaign', cpp.total_clicks,

                    'sent', cpp.sent,
                    'opened', cpp.opened,
                    'replied', cpp.replied,
                    'replied_marked_by_adminid', cpp.replied_marked_by_adminid,
                    'opted_out', cpp.opted_out,
                    'bounced', cpp.bounced,
                    'completed', cpp.completed,
                    'will_resume_at', cpp.will_resume_at,
                    'will_resume_at_tz', cpp.will_resume_at_tz,
                    'clicked', cpp.clicked,
                    'invalid_email', cpp.invalid_email,
                    'auto_reply', cpp.auto_reply,
                    'out_of_office', cpp.out_of_office,
                    'campaign_name', campaigns.name,
                    'campaign_id', campaigns.id,
                    'campaign_uuid', campaigns.uuid,

                    'current_step_type',cpp.current_step_type,
                    'latest_task_done_at',cpp.latest_task_done_or_skipped_at,
                    'sender_email', sender.email

                    )
                  ) as active_campaigns

                from campaigns_prospects cpp
                INNER JOIN campaigns ON campaigns.id = cpp.campaign_id and campaigns.team_id = cpp.team_id
                LEFT JOIN campaign_email_settings ces ON (
                   cpp.current_campaign_email_settings_id = ces.id
                   AND
                   cpp.campaign_id = ces.campaign_id
                   AND
                   cpp.team_id = ces.team_id
                )
                LEFT JOIN email_settings sender ON (
                    sender.id = ces.sender_email_setting_id
                    AND
                    sender.team_id = ces.team_id
                    and sender.status = ${EmailSettingStatus.Active.toString}
                )
                where
                cpp.prospect_id = prospects.id
                AND cpp.active

                $isCampaignLateralJoinWhere

              ) as cpa on true
        """

        }

        lateralJoinQuery

      }


    }

    // PROSPECTS_EMAILS_TODO_READ_CLEANED
    val prospectSelectClause = ProspectQuery._getProspectJustSelectSQLSyntaxV2(
      isInternalRequestSelectForActiveCampaigns = isInternalRequestSelectForActiveCampaigns,
      isForSomeCampaign = Some(isForSpecificCampaign)
    )


    val campaignIdInnerJoinClause = if (specificCampaignPageCampaignId.isDefined) {

      val campaignId = specificCampaignPageCampaignId.get

      sqls""" INNER JOIN campaigns_prospects cp ON
                (
                  cp.prospect_id = prospects.id
                    AND
                    cp.active
                    AND
                    cp.campaign_id = $campaignId
                    AND
                    cp.team_id = prospects.team_id
                )
          """
    } else sqls""




    // todo adding join with prospects_emails
    val prospectJoinCluase =
      sqls"""

          FROM prospects

          LEFT JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)

          INNER JOIN accounts a ON a.id = prospects.account_id

          INNER JOIN prospect_categories_custom pcat on pcat.id = prospects.prospect_category_id_custom

          $campaignIdInnerJoinClause

          LEFT JOIN prospect_accounts pa ON pa.id = prospects.prospect_account_id

            LEFT JOIN LATERAL (
              SELECT

              json_agg(
                json_build_object(
                  'tag_id', tags.id,
                  'tag', tags.tag,
                  'tag_uuid', tags.uuid
                  )
                ) as prospecttags

              FROM tags
              JOIN tags_prospects ON tags_prospects.tag_id = tags.id
              WHERE tags_prospects.prospect_id = prospects.id
              AND NOT tags.hidden

            ) AS ptags ON true

            ${ProspectQuery.getMagicColsLeftLateralJoin}

          LEFT JOIN prospect_lists ON (
            prospect_lists.id = prospects.list_id
            AND prospect_lists.team_id = prospects.team_id
          )

          LEFT JOIN LATERAL (
    SELECT completed_at
    FROM call_conference_logs
    WHERE primary_prospect_id = prospects.id
      AND team_id = prospects.team_id
    ORDER BY completed_at DESC
    LIMIT 1
  ) AS ccl ON TRUE

          $isInternalRequestJoinForActiveCampaigns

      """

    (prospectSelectClause, prospectJoinCluase)
  }

  //for new filters flow
  final def _genProspectSelectAndJoinSQLsV3(

                                             isInternalRequest: Boolean,

                                             // if we want to populate prospect.internal.active_campaigns array
                                             findActiveCampaignsIfInternalRequest: Boolean = false,

                                             // specific campaign page
                                             // for queries where we are only finding prospects for a campaign-id
                                             specificCampaignPageCampaignId: Option[Long] = None,

                                             findActiveCampaignDetailedWithStepAndHolidayData: Boolean = ProspectQuery.defaultForFindActiveCampaignDetailedWithStepAndHolidayData,
                                             org_id: Option[OrgId] = None,
//                                             emailNotCompulsoryEnabled: Option[Boolean]
                                           ): ProspectFilterSelectAndJoinClauses = {


    val isForSpecificCampaign: Boolean = specificCampaignPageCampaignId.isDefined


    var isInternalRequestSelectForActiveCampaigns: Option[SQLSyntax] = None

    var isInternalRequestJoinForActiveCampaigns = sqls""

    if (isInternalRequest && findActiveCampaignsIfInternalRequest) { //OUTER SELECT:: campaign lateral join

      isInternalRequestSelectForActiveCampaigns = Some(sqls" COALESCE(cpa.active_campaigns, '[]') ")

      isInternalRequestJoinForActiveCampaigns = {


        val isCampaignLateralJoinWhere: SQLSyntax = if (isForSpecificCampaign) {
          sqls" AND cpp.campaign_id = ${specificCampaignPageCampaignId.get} "
        } else sqls""


        val lateralJoinQuery = if (findActiveCampaignDetailedWithStepAndHolidayData) {

          // NOTE: 5th November 2021:
          // interval '${AppConfig.emailScheduleIntervalInMinutes} minutes' sqls was failing, we tried everything we could think of.
          // it was because ${AppConfig.emailScheduleIntervalInMinutes} was being passed as an external string / int ultimately.
          // below we are forcing AppConfig.emailScheduleIntervalInMinutes to be treated as a SQLSyntax query and not as a string / int etc. This change made it work.
          val intervalTime: SQLSyntax = SQLSyntax.createUnsafely(
            AppConfig.emailScheduleIntervalInMinutes.toString
          )

          // current date with start of day time in relevant timezone
          val currentTime: SQLSyntax = {
            sqls"""
            date_trunc('day',

              current_timestamp
              AT TIME ZONE (
                CASE

                WHEN
                  (pt1.timezone IS NOT NULL AND pt1.timezone != '')

                THEN pt1.timezone

                ELSE campaigns.timezone

                END

              )
              AT TIME ZONE 'UTC' + interval '$intervalTime minutes'

            )

            """
          }


          sqls"""
            left join lateral (
              select

              json_agg(
                json_build_object(

                  'to_check', cpp.to_check,
                  'to_check_fields', cpp.to_check_fields,

                  'total_opens_in_campaign', cpp.total_opens,
                  'total_clicks_in_campaign', cpp.total_clicks,

                  'sent', cpp.sent,
                  'opened', cpp.opened,
                  'replied', cpp.replied,
                  'replied_marked_by_adminid', cpp.replied_marked_by_adminid,
                  'opted_out', cpp.opted_out,
                  'bounced', cpp.bounced,
                  'completed', cpp.completed,
                  'will_resume_at', cpp.will_resume_at,
                  'will_resume_at_tz', cpp.will_resume_at_tz,
                  'clicked', cpp.clicked,
                  'invalid_email', cpp.invalid_email,
                  'auto_reply', cpp.auto_reply,
                  'out_of_office', cpp.out_of_office,
                  'campaign_name', campaigns.name,
                  'campaign_id', campaigns.id,
                  'campaign_uuid', campaigns.uuid,

                  'step', campaign_steps.label,

                  'sending_holiday_calendar_name', shc.calendar_name,
                  'current_holiday_name', shcd.holiday_name,
                  'current_step_type',cpp.current_step_type,
                  'latest_task_done_at',cpp.latest_task_done_or_skipped_at,
                  'sender_email', sender.email

                  )
                ) as active_campaigns

              from campaigns_prospects cpp
              INNER JOIN campaigns ON campaigns.id = cpp.campaign_id and cpp.team_id = campaigns.team_id
              LEFT JOIN campaign_email_settings ces ON (
                   cpp.current_campaign_email_settings_id = ces.id
                   AND
                   cpp.campaign_id = ces.campaign_id
                   AND
                   cpp.team_id = ces.team_id
              )
              LEFT JOIN email_settings sender ON (
                  sender.id = ces.sender_email_setting_id
                  AND
                  sender.team_id = ces.team_id
                  and sender.status = ${EmailSettingStatus.Active.toString}
              )
              LEFT JOIN campaign_steps ON campaign_steps.id = cpp.current_step_id

              LEFT JOIN sending_holiday_calendars shc on (

                shc.id = (
                  CASE WHEN
                    pt1.sending_holiday_calendar_id is not null
                  THEN
                    pt1.sending_holiday_calendar_id     -----TODO: check should come from pt1
                  ELSE
                    campaigns.sending_holiday_calendar_id
                  END
                )

              )
              LEFT JOIN sending_holiday_calendar_dates shcd ON (

                shcd.sending_holiday_calendar_id = shc.id

                and check_if_holiday(

                      -- holiday start
                      shcd.start_date_utc,

                      -- holiday end
                      shcd.end_date_utc,

                      -- current date with start of day time in relevant timezone
                      $currentTime

                )
              )

              where
              cpp.prospect_id = pt1.id
              AND cpp.active

              $isCampaignLateralJoinWhere

            ) as cpa on true
      """
        } else {

          sqls"""
              left join lateral (
                select

                json_agg(
                  json_build_object(

                    'to_check', cpp.to_check,
                    'to_check_fields', cpp.to_check_fields,

                    'total_opens_in_campaign', cpp.total_opens,
                    'total_clicks_in_campaign', cpp.total_clicks,

                    'sent', cpp.sent,
                    'opened', cpp.opened,
                    'replied', cpp.replied,
                    'replied_marked_by_adminid', cpp.replied_marked_by_adminid,
                    'opted_out', cpp.opted_out,
                    'bounced', cpp.bounced,
                    'completed', cpp.completed,
                    'will_resume_at', cpp.will_resume_at,
                    'will_resume_at_tz', cpp.will_resume_at_tz,
                    'clicked', cpp.clicked,
                    'invalid_email', cpp.invalid_email,
                    'auto_reply', cpp.auto_reply,
                    'out_of_office', cpp.out_of_office,
                    'campaign_name', campaigns.name,
                    'campaign_id', campaigns.id,
                    'campaign_uuid', campaigns.uuid,

                    'current_step_type',cpp.current_step_type,
                    'latest_task_done_at',cpp.latest_task_done_or_skipped_at,
                    'sender_email', sender.email

                    )
                  ) as active_campaigns

                from campaigns_prospects cpp
                INNER JOIN campaigns ON campaigns.id = cpp.campaign_id and campaigns.team_id = cpp.team_id
                LEFT JOIN campaign_email_settings ces ON (
                   cpp.current_campaign_email_settings_id = ces.id
                   AND
                   cpp.campaign_id = ces.campaign_id
                   AND
                   cpp.team_id = ces.team_id
                )
                LEFT JOIN email_settings sender ON (
                    sender.id = ces.sender_email_setting_id
                    AND
                    sender.team_id = ces.team_id
                    and sender.status = ${EmailSettingStatus.Active.toString}
                )
                where
                cpp.prospect_id = pt1.id
                AND cpp.active

                $isCampaignLateralJoinWhere

              ) as cpa on true
        """

        }

        lateralJoinQuery

      }


    }

    val prospectSelectClauseInner: SQLSyntax = ProspectQuery._getProspectJustSelectInnerSQL(
      isForSomeCampaign = Some(isForSpecificCampaign)
    )

    val prospectSelectClauseOuter: SQLSyntax = ProspectQuery._getProspectJustSelectOuterSQL(
      isInternalRequestSelectForActiveCampaigns = isInternalRequestSelectForActiveCampaigns,
      isForSomeCampaign = Some(isForSpecificCampaign)
    )


    val campaignIdInnerJoinClause = if (specificCampaignPageCampaignId.isDefined) {

      val campaignId = specificCampaignPageCampaignId.get

      sqls""" INNER JOIN campaigns_prospects cp ON
                (
                  cp.prospect_id = prospects.id
                    AND
                    cp.active
                    AND
                    cp.campaign_id = $campaignId
                    AND
                    cp.team_id = prospects.team_id
                )
          """
    } else sqls""


    val prospect_emails_join: SQLSyntax = sqls" LEFT JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary) "

    val prospectJoinClauseInner =
      sqls"""
          FROM prospects
          $prospect_emails_join
          $campaignIdInnerJoinClause
        """

    val prospectJoinClauseOuter =
      sqls"""

          INNER JOIN accounts a ON a.id = pt1.account_id

          INNER JOIN prospect_categories_custom pcat on pcat.id = pt1.prospect_category_id_custom

          LEFT JOIN prospect_accounts pa ON pa.id = pt1.prospect_account_id

            LEFT JOIN LATERAL (
              SELECT

              json_agg(
                json_build_object(
                  'tag_id', tags.id,
                  'tag', tags.tag,
                  'tag_uuid', tags.uuid
                  )
                ) as prospecttags

              FROM tags
              JOIN tags_prospects ON tags_prospects.tag_id = tags.id
              WHERE tags_prospects.prospect_id = pt1.id
              AND NOT tags.hidden

            ) AS ptags ON true

            ${ProspectQuery.getMagicColsLeftLateralJoinV2}

          LEFT JOIN prospect_lists ON (
            prospect_lists.id = pt1.list_id
            AND prospect_lists.team_id = pt1.team_id
          )

           LEFT JOIN LATERAL (
    SELECT completed_at
    FROM call_conference_logs
    WHERE primary_prospect_id = pt1.id
      AND team_id = pt1.team_id
    ORDER BY completed_at DESC
    LIMIT 1
  ) AS ccl ON TRUE

          $isInternalRequestJoinForActiveCampaigns

      """

    ProspectFilterSelectAndJoinClauses(
      prospectSelectInnerSQL = prospectSelectClauseInner,
      prospectSelectOuterSQL = prospectSelectClauseOuter,
      prospectJoinInner = prospectJoinClauseInner,
      prospectJoinOuter = prospectJoinClauseOuter
    )
  }


}

/*
 * use case: msg received from queue contains emailSetting id - schedule emails to be sent from emailSettingId - CampaignProspectDAO.fetchProspectsV2
 * use case: indirect usage in inbox used to construct  prospectDAO
 * use case : prospectQuery.getQuerySQL  prospectQuery._genProspectSelectAndJoinSQLs in Prospect.scala,
 *
 */
class ProspectQuery(
                     prospectColumnDef: ProspectColumnDef
                   ) extends SearchQueryTrait {

  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  // # from FE api/v2/search/prospects

  def getQuerySQL(
                     permittedAccountIds: Seq[Long],
                     teamId: Long,
                     orgId: Long,
                     data: SearchQuery,
                     account: Account,
                     isInternalRequest: Boolean,
                     fetchType: SearchQuerySelectType.Value,
                     queryTimeline: Option[InferredQueryTimeline] = None,
                     limit: Int = 500,
                     isApiCall: Boolean = false,
//                     emailNotCompulsoryEnabled: Option[Boolean],
                     Logger: SRLogger,
//                     shouldUseNewQuery: Boolean = false
                   ): Try[(Int, Int, SQL[?, ?])] = Try {

    val rowsPerPage = 500
    val maxCountLimit = rowsPerPage + 1 // slightly higher than rowsPerPage so we know next page of results exists

    val offset = (data.page.getOrElse(1) - 1) * rowsPerPage


    var whereStatement: (SQLSyntax, SQLSyntax) = (sqls"", sqls"")
    var searchWhereClause: SQLSyntax = sqls""
    // var havingStatement: SQLSyntax = sqls""

    if (data.query.isDefined) {

      whereStatement = getWhereClauseV2(
        query = data.query.get,
        teamId = teamId,
        account = account,
        Logger = Logger
      )


      val search = data.query.get.search
      if (search.nonEmpty && search.get.trim.nonEmpty) {

        val param = s"%${search.get.trim}%"
        val linkedin_url_normalized = LinkedinHelperFunctions.normalizeLinkedInURL(Some(search.get.trim)).get

        if(linkedin_url_normalized.isEmpty){
          Logger.shouldNeverHappen(s"linkedin_url_normalized :: is_empty :: search string :: ${search.get.trim} team_id :: ${teamId} :: account_uuid ${account.id}")
        }

        // PROSPECTS_EMAILS_TODO_READ_CLEANED
        searchWhereClause =
          sqls""" AND (
              lower(pe.email) LIKE ${param.toLowerCase} OR
              prospects.first_name ILIKE $param OR
              prospects.last_name ILIKE $param OR
              prospects.linkedin_url = $linkedin_url_normalized OR
              prospects.phone ILIKE $param OR
              prospects.company ILIKE $param
          ) """
      }

    }



    val orderByStatement: SQLSyntax = getSortStatementWithLimitV2(
      teamId = teamId,
      sort = data.sort,
      Logger = Logger,
      queryTimeline = queryTimeline,
      limit = limit,
      isApiCall = isApiCall,
      offset = offset,
    )


    val customFlags: Option[JsValue] = data.custom_flags

    /**
     * we want to join and fetch current_campaign_step and holiday related data only in campaigns_prospects page
     */
    val getActiveCampaignDetailed: Boolean = customFlags
      .flatMap(flagsJs => {

        (flagsJs \ "active_campaigns_detailed").asOpt[Boolean]

      })
      // we send the step/holiday details by default, unless we explicitly get the flag not to send these
      .getOrElse(ProspectQuery.defaultForFindActiveCampaignDetailedWithStepAndHolidayData)

    // PROSPECTS_EMAILS_TODO_READ_CLEANED
    val prospectFilterSelectAndJoinClauses: ProspectFilterSelectAndJoinClauses = ProspectQuery._genProspectSelectAndJoinSQLsV3(
      isInternalRequest = isInternalRequest,
      findActiveCampaignsIfInternalRequest = true,
      findActiveCampaignDetailedWithStepAndHolidayData = getActiveCampaignDetailed,
      specificCampaignPageCampaignId = data.is_campaign,
      org_id = Some(OrgId(orgId)),
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    )


    val accountIdsQueryClause: SQLSyntax = {  //INNER SELECT:: where clause

      val allTeamMemberAccountIds: Set[Long] = PermissionMethods
        .getAllMembersInTeam(
          loggedinAccount = account,
          teamId = teamId
        )
        .map(_.user_id)
        .toSet


      val allowedOwnerIdsForQuery: Set[Long] = getForOwnerIds(query = data.query, permittedAccountIds = permittedAccountIds).toSet


      val areAllTeamMembersSelectedForQuery = allTeamMemberAccountIds.equals(allowedOwnerIdsForQuery)

      if (areAllTeamMembersSelectedForQuery) {

        // 13 Jan 2023: if all team members are anyways selected, we can avoid this clause altogether.
        // This speeds up the query significantly

        Logger.debug("[ProspectQuery] avoiding accountIdsQueryClause because all team members are being selected")

        sqls""

      } else {

        Logger.debug(s"[ProspectQuery] cannot avoid accountIdsQueryClause because not-all team members are being selected: allowedOwnerIdsForQuery: $allowedOwnerIdsForQuery")

        sqls""" AND prospects.account_id IN (${allowedOwnerIdsForQuery}) """

      }

    }



    val query = fetchType match {

      case SearchQuerySelectType.COUNT_ONLY =>

        sql"""


        SELECT COUNT(*) as total_count

          FROM
          (
          ${prospectFilterSelectAndJoinClauses.prospectSelectInnerSQL}
          ${prospectFilterSelectAndJoinClauses.prospectJoinInner}

           WHERE

           prospects.team_id = $teamId

           $accountIdsQueryClause

           AND NOT prospects.will_delete

           $searchWhereClause

           ${whereStatement._1}
          ) pt1

          ${prospectFilterSelectAndJoinClauses.prospectJoinOuter}

          WHERE

           pt1.team_id = $teamId

           ${whereStatement._2}

        """


      case SearchQuerySelectType.ID_ONLY =>

        sql"""


        SELECT pt1.id as id

          FROM
          (
          ${prospectFilterSelectAndJoinClauses.prospectSelectInnerSQL}
          ${prospectFilterSelectAndJoinClauses.prospectJoinInner}

           WHERE

           prospects.team_id = $teamId

           $accountIdsQueryClause

           AND NOT prospects.will_delete

           $searchWhereClause

           ${whereStatement._1}
          ) pt1

          ${prospectFilterSelectAndJoinClauses.prospectJoinOuter}

          WHERE

           pt1.team_id = $teamId

           ${whereStatement._2}

        """

      case SearchQuerySelectType.FULL_OBJECT =>

        // # from FE api/v2/search/prospects
        sql"""

          SELECT
          ${prospectFilterSelectAndJoinClauses.prospectSelectOuterSQL}

          FROM
          (
          ${prospectFilterSelectAndJoinClauses.prospectSelectInnerSQL}
          ${prospectFilterSelectAndJoinClauses.prospectJoinInner}

           WHERE

           prospects.team_id = $teamId

           $accountIdsQueryClause

           AND NOT prospects.will_delete

           $searchWhereClause

           ${whereStatement._1}
          ) pt1

          ${prospectFilterSelectAndJoinClauses.prospectJoinOuter}

          WHERE

           pt1.team_id = $teamId

           ${whereStatement._2}

         $orderByStatement

         LIMIT $maxCountLimit OFFSET $offset

        """
    }


    (
      rowsPerPage,
      0, // NOTE: This must not be used, we remove max_count from prospects search api response on 22nd December 2022.
      query
    )
  }


  def getStatementClauseV2(
                            field: String,
                            operator: SearchQueryOperators.Value,
                            value: String,
                            // clause: SQLSyntax,
                            field_type: FieldTypeEnum.Value,
                            is_custom: Boolean,
                            team_id: Long,
                            account: Account,
                            Logger: SRLogger
                          ): SearchQueryGetStatementV2 = {

    var whereClauseInner: Option[SQLSyntax] = None
    var whereClauseOuter: Option[SQLSyntax] = None


    if (!is_custom) {
      field match {

        case "email" =>  //FIELD::1:: inner select

          val isPrimary = sqls" AND pe.is_primary "

          var inputVal: Any = value

          if (field_type == FieldTypeEnum.NUMBER) {
            inputVal = ParseUtils.parseInt(value)
          }

          operator match {

            case SearchQueryOperators.CONTAINS =>

              val param = s"%${inputVal.toString.toLowerCase}%"

              whereClauseInner = Some(sqls" lower(pe.email) LIKE $param $isPrimary ")

            case SearchQueryOperators.NOT_CONTAINS =>

              val param = s"%${inputVal.toString.toLowerCase}%"

              whereClauseInner = Some(sqls" lower(pe.email) NOT LIKE $param $isPrimary ")

            case SearchQueryOperators.EQUAL =>

              whereClauseInner = Some(sqls" lower(pe.email) = $inputVal $isPrimary ")

            case SearchQueryOperators.NOT_EQUAL =>

              whereClauseInner = Some(sqls" lower(pe.email) != $inputVal $isPrimary ")

            case SearchQueryOperators.GREATER_THAN =>

              whereClauseInner = Some(sqls" lower(pe.email) > $inputVal $isPrimary ")

            case SearchQueryOperators.LESS_THAN =>

              whereClauseInner = Some(sqls" lower(pe.email) < $inputVal $isPrimary ")

            case SearchQueryOperators.IS_EMPTY =>
              whereClauseInner = Some(sqls" (pe.email IS NULL OR pe.email = '') ")

            case SearchQueryOperators.IS_NOT_EMPTY =>
              whereClauseInner = Some(sqls" (pe.email IS NOT NULL AND pe.email != '') ")

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "current_sender_email" => //FIELD::2:: inner select

          val inputVal: Option[Int] = ParseUtils.parseInt(value)

          inputVal match {
            case None =>
              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

            case Some(ces_id) =>
              operator match {

                case SearchQueryOperators.EQUAL =>

                  whereClauseInner = Some(sqls" cp.current_campaign_email_settings_id = $ces_id ")


                case _ =>

                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

              }

          }


        // PROSPECTCOLUMNCHANGETAG
        case "first_name" | "last_name" | "company" | "city" | "country" | "domain" | "timezone" | "total_opens" | "total_clicks" | "prospect_source"
             | "state" | "phone" | "job_title" | "linkedin_url" => //FIELD::3:: inner select


          val fieldName = SQLSyntax.createUnsafely(field.trim)

          var inputVal: Any = value

          if (field_type == FieldTypeEnum.NUMBER) {
            inputVal = ParseUtils.parseInt(value)
          } else if(field == "linkedin_url") {    // search on linkedin_url will come with EQUAL operator from FE.
            inputVal = LinkedinHelperFunctions.normalizeLinkedInURL(url = Some(inputVal.toString)).getOrElse("")
          }

          operator match {

            case SearchQueryOperators.CONTAINS =>

              val param = s"%${inputVal.toString}%"

              whereClauseInner = Some(sqls" prospects.$fieldName ILIKE $param ")

            case SearchQueryOperators.NOT_CONTAINS =>

              val param = s"%${inputVal.toString}%"

              whereClauseInner = Some(sqls" prospects.$fieldName NOT ILIKE $param ")

            case SearchQueryOperators.EQUAL =>

              whereClauseInner = if (field == "linkedin_url") {

                // adding not null clause so that db engine pics the partial index for linkedin_url
                Some(sqls" prospects.linkedin_url = $inputVal AND prospects.linkedin_url IS NOT NULL ")

              } else Some(sqls" prospects.$fieldName = $inputVal ")

            case SearchQueryOperators.NOT_EQUAL =>

              whereClauseInner = Some(sqls" prospects.$fieldName != $inputVal ")

            case SearchQueryOperators.GREATER_THAN =>

              whereClauseInner = Some(sqls" prospects.$fieldName > $inputVal ")

            case SearchQueryOperators.LESS_THAN =>

              whereClauseInner = Some(sqls" prospects.$fieldName < $inputVal ")

            case SearchQueryOperators.IS_EMPTY =>

              whereClauseInner = Some(sqls" (prospects.$fieldName IS NULL OR prospects.$fieldName = '') ")

            case SearchQueryOperators.IS_NOT_EMPTY =>

              whereClauseInner = Some(sqls" (prospects.$fieldName IS NOT NULL AND prospects.$fieldName != '') ")

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "in_list" => //FIELD::4:: inner select

          operator match {

            case SearchQueryOperators.EQUAL =>

              ParseUtils.parseLong(value) match {
                case None =>
                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

                case Some(listId) =>
                  if (listId == 0) //any
                    whereClauseInner = Some(sqls" prospects.list_id is not null ")
                  else if (listId == -1) //none
                    whereClauseInner = Some(sqls" prospects.list_id is null ")
                  else //specific campaign
                    whereClauseInner = Some(sqls" prospects.list_id = $listId ")

              }

            case _ =>

              throw new Exception(s"Invalid operator: ${operator} :: field: ${field}")

          }

        case "not_in_list" => //FIELD::5:: inner select

          operator match {

            case SearchQueryOperators.EQUAL =>

              ParseUtils.parseLong(value) match {
                case None =>
                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

                case Some(listId) =>
                  if (listId == 0) //any
                    whereClauseInner = Some(sqls" prospects.list_id is null ")

                  else if (listId == -1) //none
                    throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

                  else //specific campaign
                    // REF: IS DISTINCT FROM makes it null safe: https://www.postgresql.org/docs/current/functions-comparison.html
                    whereClauseInner = Some(sqls" prospects.list_id IS DISTINCT FROM $listId ")

              }

            case _ =>

              throw new Exception(s"Invalid operator: ${operator} :: field: ${field}")

          }

        case "owner_name" => //FIELD::6:: outer select

          operator match {

            case SearchQueryOperators.EQUAL =>

              ParseUtils.parseLong(value) match {

                case None =>

                  throw new Exception("Invalid owner filter")

                case Some(0) =>

                  whereClauseOuter = Some(sqls" a.id IS NULL ")

                case Some(ownerAccountId) =>

                  whereClauseOuter = Some(sqls" a.id = $ownerAccountId ")
              }



            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "email_is_valid" => //FIELD::7:: inner select

          operator match {

            case SearchQueryOperators.EQUAL =>

              value match {

                case "true" =>

                  whereClauseInner = Some(sqls" (pe.invalid_email = FALSE AND pe.email_checked = TRUE AND pe.is_primary = TRUE) ")

                case "false" =>

                  whereClauseInner = Some(sqls" pe.invalid_email = TRUE AND pe.is_primary = TRUE ")

                case _ =>

                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

              }

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "in_do_not_contact_list" => //FIELD::8:: inner select

          //val in_do_not_contact_list = Prospect.getProspectCategoryId(team_id = team_id, text_id = ProspectCategory.DO_NOT_CONTACT, account = account)
          val in_do_not_contact_list: Long = Helpers.getCategoryByTextId(
            team_id = team_id,
            account = account,
            textId = ProspectCategory.DO_NOT_CONTACT,
            Logger = Logger
          ).get.id

          operator match {

            case SearchQueryOperators.EQUAL =>

              value match {

                case "true" =>

                  whereClauseInner = Some(sqls" prospects.prospect_category_id_custom = ${in_do_not_contact_list} ")

                case "false" =>

                  whereClauseInner = Some(sqls" prospects.prospect_category_id_custom != ${in_do_not_contact_list} ")

                case _ =>

                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

              }

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        // for last_call_made_at
        case "created_at" |

             "last_contacted_at" |

             "last_replied_at" |

             "last_opened_at" |

             "latest_task_done_at" => //FIELD::9:: inner select

          val timestamp = ParseUtils.parseLong(value).getOrElse(DateTime.now())

          val queryFieldName: SQLSyntax = field match {

            case "created_at" => sqls" prospects.created_at "

            case "last_contacted_at" => sqls" prospects.last_contacted_at "

            case "last_replied_at" => sqls" prospects.last_replied_at "

            case "last_opened_at" => sqls" prospects.last_opened_at "

            case "latest_task_done_at" => sqls" prospects.latest_task_done_at"

          }


          operator match {

            case SearchQueryOperators.GREATER_THAN =>

              whereClauseInner = Some(sqls" $queryFieldName >= TO_TIMESTAMP($timestamp / 1000) ")

            case SearchQueryOperators.LESS_THAN =>

              whereClauseInner = Some(sqls" $queryFieldName <= TO_TIMESTAMP($timestamp / 1000) ")

            case SearchQueryOperators.EQUAL =>

              whereClauseInner = Some(sqls" ($queryFieldName >= TO_TIMESTAMP($timestamp / 1000) AND $queryFieldName < TO_TIMESTAMP(($timestamp + 86400000) / 1000)) ")

            case SearchQueryOperators.IS_EMPTY =>

              whereClauseInner = Some(sqls" $queryFieldName IS NULL")

            case SearchQueryOperators.IS_NOT_EMPTY =>

              whereClauseInner = Some(sqls" $queryFieldName IS NOT NULL")

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "last_call_made_at" =>
            val timestamp = ParseUtils.parseLong(value).getOrElse(DateTime.now())

            val queryFieldName = sqls"ccl.completed_at"
            operator match {
                case SearchQueryOperators.GREATER_THAN =>
                    whereClauseOuter = Some(sqls"$queryFieldName >= TO_TIMESTAMP($timestamp / 1000)")
                case SearchQueryOperators.LESS_THAN =>
                    whereClauseOuter = Some(sqls"$queryFieldName <= TO_TIMESTAMP($timestamp / 1000)")
                case SearchQueryOperators.EQUAL =>
                    whereClauseOuter = Some(sqls" ($queryFieldName >= TO_TIMESTAMP($timestamp / 1000) AND $queryFieldName < TO_TIMESTAMP(($timestamp + 86400000) / 1000)) ")
                case SearchQueryOperators.IS_EMPTY =>
                    whereClauseOuter = Some(sqls"$queryFieldName IS NULL")
                case SearchQueryOperators.IS_NOT_EMPTY =>
                    whereClauseOuter = Some(sqls"$queryFieldName IS NOT NULL")
                case _ =>
                    throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")
            }

        case "prospect_category" => //FIELD::10:: inner select

          Helpers.getCategoryById(
            team_id = team_id,
            account = account,
            categoryId = value.toLong,
            Logger = Logger
          ) match {

            case None => throw new Exception(s"Invalid prospect category filter: $value")

            case Some(catDB) =>

              operator match {

                case SearchQueryOperators.EQUAL =>

                  whereClauseInner = Some(sqls" (prospects.prospect_category_id_custom = ${catDB.id}) ")

                case SearchQueryOperators.NOT_EQUAL =>

                  whereClauseInner = Some(sqls" (prospects.prospect_category_id_custom != ${catDB.id}) ")

                case _ =>

                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

              }
          }

        case "current_step_type" => //FIELD::11:: outer select

          CampaignStepType.fromKey(str = value) match {
            case  Failure(e) => throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")
            case Success(value) =>
              operator match {
                case SearchQueryOperators.EQUAL =>
                  whereClauseOuter = Some(sqls" cpa.current_step_type = ${value.toKey} ")

                case SearchQueryOperators.NOT_EQUAL =>
                  whereClauseOuter = Some(sqls" cpa.current_step_type != ${value.toKey} ")

                case _ =>

                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")
              }
          }

        case "prospect_id" => //FIELD::12:: inner select

          operator match {

            case SearchQueryOperators.EQUAL =>

              ParseUtils.parseLong(value) match {

                case None =>
                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")


                case Some(prospectId) =>

                  whereClauseInner = Some(sqls" prospects.id = $prospectId ")

              }

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "has_tag" | "not_has_tag"=> //FIELD::13:: outer exists

          operator match {

            case SearchQueryOperators.EQUAL =>

              ParseUtils.parseLong(value) match {
                case None =>
                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

                case Some(tagId) =>

                  val tagIdCheck = if (tagId == 0) {
                    // Special case: prospect should have any one tag, so no check
                    sqls" "

                  } else {

                    sqls" AND tags_prospects.tag_id = $tagId "

                  }

                  val tpSql = sqls"""
                    SELECT * FROM tags_prospects
                    inner join tags on tags.id = tags_prospects.tag_id AND tags.team_id = $team_id
                    WHERE tags_prospects.prospect_id = pt1.id $tagIdCheck
                   """

                  val whereClauseForCondition = field match {

                    case "has_tag" =>
                      sqls""" EXISTS($tpSql) """

                    case "not_has_tag" =>
                      sqls""" NOT EXISTS($tpSql) """
                  }
                  whereClauseOuter = Some(whereClauseForCondition)
              }

            case _ =>

              throw new Exception(s"Invalid operator: ${operator} :: field: ${field}")

          }

        case "has_call_recording" =>

          operator match {

            case SearchQueryOperators.EQUAL =>

              val callRecordingSql =
                sqls"""
                      SELECT
                        *
                      FROM
                        tasks AS t
                        JOIN call_conference_logs AS ccl ON ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id
                      WHERE
                        t.team_id = pt1.team_id
                        AND t.prospect_id = pt1.id
                        AND ccl.recording_url IS NOT NULL
                    """

              value match {

                case "true" =>

                  whereClauseOuter = Some(sqls" EXISTS($callRecordingSql) ")

                case "false" =>

                  whereClauseOuter = Some(sqls" NOT EXISTS($callRecordingSql) ")

                case _ =>

                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

              }

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "has_notes" =>
            operator match {

                case SearchQueryOperators.EQUAL =>

                    val notesSql = sqls"SELECT * FROM notes WHERE notes.prospect_id = pt1.id AND notes.team_id = pt1.team_id"

                    value match {

                        case "true" =>

                            whereClauseOuter = Some(sqls""" EXISTS($notesSql) """)

                        case "false" =>

                            whereClauseOuter = Some(sqls""" NOT EXISTS($notesSql) """)

                        case _ =>

                            throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

                    }

                case _ =>

                    throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

            }

        case "step" => //FIELD::14:: inner select

          operator match {

            case SearchQueryOperators.EQUAL =>

              ParseUtils.parseLong(value) match {

                case None =>

                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

                case Some(stepId) =>

                  whereClauseInner = Some( //UPDATE:: removed exists
                    sqls""" EXISTS(
                          SELECT *
                          FROM campaigns_prospects cp_steps
                          where cp_steps.prospect_id = prospects.id
                          AND cp_steps.current_step_id = $stepId
                          AND cp_steps.active
                          and cp_steps.team_id = $team_id
                        )
                      """
                  )

              }

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "prospect_account_name" => //FIELD::15:: outer select  //TODO: confirm

          var inputVal = value

          operator match {

            case SearchQueryOperators.CONTAINS =>

              val param = s"%${inputVal.toString}%"

              whereClauseOuter = Some(sqls" pa.name ILIKE $param ")

            case SearchQueryOperators.NOT_CONTAINS =>

              val param = s"%${inputVal.toString}%"

              whereClauseOuter = Some(sqls" pa.name NOT ILIKE $param ")


            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "prospect_account_id" => //FIELD::16:: outer select

          operator match {

            case SearchQueryOperators.EQUAL =>

              whereClauseOuter = Some(sqls" pa.uuid = $value ")

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "latest_reply_sentiment" => //FIELD::17:: inner select
          operator match {
            case SearchQueryOperators.EQUAL => whereClauseInner = Some(sqls" prospects.latest_reply_sentiment_uuid = ${value}")

            case SearchQueryOperators.NOT_EQUAL => whereClauseInner = Some(sqls" prospects.latest_reply_sentiment_uuid != ${value}")

            case _ =>
              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")
          }

        case "clicked" | "not_clicked" => //FIELD::18:: outer select

          val inputVal = value

          val linkClause = operator match {

            case SearchQueryOperators.CONTAINS =>

              val param = s"%${inputVal.toString}%"

              sqls" AND p_ev.clicked_url ILIKE $param "

            case SearchQueryOperators.NOT_CONTAINS =>

              val param = s"%${inputVal.toString}%"

              sqls" AND p_ev.clicked_url NOT ILIKE $param "

            case SearchQueryOperators.EQUAL =>

              sqls" AND p_ev.clicked_url = $inputVal "

            case SearchQueryOperators.NOT_EQUAL =>

              sqls" AND p_ev.clicked_url != $inputVal "

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }


          val cpSql =
            sqls""" SELECT * FROM prospect_events p_ev
                      WHERE p_ev.prospect_id = pt1.id
                      AND p_ev.team_id = $team_id
                      AND p_ev.event_type = 'clicked' $linkClause"""

          val whereClauseForCondition = field match {

            case "clicked" =>

              sqls""" EXISTS($cpSql) """

            case "not_clicked" =>

              sqls""" NOT EXISTS($cpSql) """

          }

          Logger.info(s"\n\n\n\n\n whereClauseForCondition $whereClauseForCondition \n\n\n\n")
          whereClauseOuter = Some(whereClauseForCondition)


        // THESE SHOULD not BE IN THE "HAVING" CLAUSE: update: having clause was causing issues in sub-filters
        // These are all the queries on the cp_all join
        //handled in different way
        case "assigned_to_campaign" |

             "not_assigned_to_campaign" |

             "has_completed_campaign" |

             "has_not_completed_campaign" |

             "has_sent" |

             "has_not_sent" |

             "to_check" |

             "has_unsubscribed" |

             "opened" |

             "not_opened" |

             "replied" |

             "not_replied" |

             "bounced" |

             "not_bounced" |

             "has_clicked" |

             "has_not_clicked" |

             "has_not_unsubscribed" => //FIELD::19:: inner select

//          operator match {
//
//            case SearchQueryOperators.EQUAL =>
//
//              ParseUtils.parseLong(value) match {
//                case None =>
//                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")
//
//                case Some(campaignId) =>
//
//
//
//                  val cmIdClause = campaignId match {
//                    case 0 =>
//                      sqls""
//
//                    case -1 =>
//                      throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")
//
//                    case _ =>
//                      sqls" AND cp_all.campaign_id = $campaignId"
//                  }
//
//
//                  val cpSql = sqls" SELECT * FROM campaigns_prospects cp_all where cp_all.team_id = $team_id and cp_all.prospect_id = prospects.id $cmIdClause"
//
//                  val whereClauseForCondition = field match {
//
//                    case "assigned_to_campaign" =>
//                      sqls""" EXISTS($cpSql AND cp_all.active) """
//
//                    case "not_assigned_to_campaign" =>
//
//                      sqls""" NOT EXISTS($cpSql AND cp_all.active) """
//
//                    case "has_completed_campaign" =>
//
//                      sqls""" EXISTS($cpSql AND cp_all.completed) """
//
//                    case "has_not_completed_campaign" =>
//
//                      sqls""" NOT EXISTS($cpSql AND cp_all.completed) """
//
//                    case "has_sent" =>
//
//                      sqls""" EXISTS($cpSql AND cp_all.sent) """
//
//                    case "has_not_sent" =>
//
//                      sqls""" NOT EXISTS($cpSql AND cp_all.sent) """
//
//                    case "to_check" =>
//
//                      sqls""" EXISTS($cpSql AND cp_all.to_check) """
//
//                    case "has_unsubscribed" =>
//
//                      sqls""" EXISTS($cpSql AND cp_all.opted_out) """
//
//                    case "has_not_unsubscribed" =>
//
//                      sqls""" NOT EXISTS($cpSql AND cp_all.opted_out) """
//
//                    case "opened" =>
//
//                      sqls""" EXISTS($cpSql AND cp_all.opened) """
//
//                    case "not_opened" =>
//
//                      sqls""" NOT EXISTS($cpSql AND cp_all.opened) """
//
//                    case "has_clicked" =>
//
//                      sqls""" EXISTS($cpSql AND cp_all.clicked) """
//
//                    case "has_not_clicked" =>
//
//                      sqls""" NOT EXISTS($cpSql AND cp_all.clicked) """
//
//                    case "replied" =>
//
//                      sqls""" EXISTS($cpSql AND cp_all.replied) """
//
//                    case "not_replied" =>
//
//                      sqls""" NOT EXISTS($cpSql AND cp_all.replied) """
//
//                    case "bounced" =>
//
//                      sqls""" EXISTS($cpSql AND cp_all.bounced) """
//
//                    case "not_bounced" =>
//
//                      sqls""" NOT EXISTS($cpSql AND cp_all.bounced) """
//
//                  }
//                  whereClauseInner = Some(whereClauseForCondition)
//              }
//
//            case _ =>
//
//              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")
//
//          }


        case _ =>

          Logger.error(s"ProspectQuery FATAL filter prospects table: field name is $field")

          throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

      }
    } else {

      // handle filtering custom_fields

      if (field_type == FieldTypeEnum.TEXT) { //FIELD::20:: inner select

        val param = s"%${value.toString}%"

        // Regarding json ?? operator: https://github.com/jdbi/jdbi/issues/174#issuecomment-142068731
        val q = operator match {

          case SearchQueryOperators.CONTAINS =>

            sqls" prospects.custom_fields ->> $field ILIKE $param "

          case SearchQueryOperators.NOT_CONTAINS =>

            sqls" (NOT prospects.custom_fields ?? $field) OR (prospects.custom_fields ->> $field NOT ILIKE $param) "

          case SearchQueryOperators.EQUAL =>

            sqls" prospects.custom_fields ->> $field = $value "

          case SearchQueryOperators.NOT_EQUAL =>

            sqls" (NOT prospects.custom_fields ?? $field) OR (prospects.custom_fields ->> $field != $value) "

          case SearchQueryOperators.IS_EMPTY =>

            sqls" (prospects.custom_fields -> $field) IS NULL "

          case SearchQueryOperators.IS_NOT_EMPTY =>

            sqls" (prospects.custom_fields -> $field) IS NOT NULL "

          case _ =>

            throw new Exception(s"Invalid operator: $operator :: custom_field: $field :: $value")

        }

        whereClauseInner = Some(q)

      } else if (field_type == FieldTypeEnum.NUMBER) { //FIELD::21:: inner select

        val parsedVal: Option[Long] = if (operator == SearchQueryOperators.IS_NOT_EMPTY || operator == SearchQueryOperators.IS_EMPTY) Some(1L) // passing a dummy value as it is not used
        else ParseUtils.parseLong(value)

        parsedVal match {

          case None => throw new Exception(s"Invalid value: $operator :: custom_field: $field :: $value")


          case Some(numValue) =>

            operator match {

              case SearchQueryOperators.EQUAL =>

                whereClauseInner = Some(sqls" CAST (prospects.custom_fields ->> $field AS NUMERIC) = $numValue ")

              case SearchQueryOperators.GREATER_THAN =>

                whereClauseInner = Some(sqls" CAST (prospects.custom_fields ->> $field AS NUMERIC) > $numValue ")

              case SearchQueryOperators.LESS_THAN =>

                whereClauseInner = Some(sqls" CAST (prospects.custom_fields ->> $field AS NUMERIC) < $numValue ")

              case SearchQueryOperators.IS_EMPTY =>

                whereClauseInner = Some(sqls" (prospects.custom_fields -> $field) IS NULL ")

              case SearchQueryOperators.IS_NOT_EMPTY =>

                whereClauseInner = Some(sqls" (prospects.custom_fields -> $field) IS NOT NULL ")

              case _ =>

                throw new Exception(s"Invalid operator: $operator :: custom_field: $field :: $value")

            }
        }


      } else {

        throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")
      }
    }


    SearchQueryGetStatementV2(
      whereClauseInner = whereClauseInner,
      whereClauseOuter = whereClauseOuter
    )
  }

  // return the (whereClause, havingGroupByClause, havingClause)
  def getStatementClause(
    field: String,
    operator: SearchQueryOperators.Value,
    value: String,
    // clause: SQLSyntax,
    field_type: FieldTypeEnum.Value,
    is_custom: Boolean,
    team_id: Long,
    account: Account,
    Logger: SRLogger
  ): SearchQueryGetStatement = {

    var whereClause: Option[SQLSyntax] = None
    val havingClause: Option[SQLSyntax] = None // NOT USED

    if (!is_custom) {
      field match {

        case "email" =>

          val isPrimary = sqls" AND pe.is_primary "

          var inputVal: Any = value

          if (field_type == FieldTypeEnum.NUMBER) {
            inputVal = ParseUtils.parseInt(value)
          }

          operator match {

            case SearchQueryOperators.CONTAINS =>

              val param = s"%${inputVal.toString.toLowerCase}%"

              whereClause = Some(sqls" lower(pe.email) LIKE $param $isPrimary ")

            case SearchQueryOperators.NOT_CONTAINS =>

              val param = s"%${inputVal.toString.toLowerCase}%"

              whereClause = Some(sqls" lower(pe.email) NOT LIKE $param $isPrimary ")

            case SearchQueryOperators.EQUAL =>

              whereClause = Some(sqls" lower(pe.email) = $inputVal $isPrimary ")

            case SearchQueryOperators.NOT_EQUAL =>

              whereClause = Some(sqls" lower(pe.email) != $inputVal $isPrimary ")

            case SearchQueryOperators.GREATER_THAN =>

              whereClause = Some(sqls" lower(pe.email) > $inputVal $isPrimary ")

            case SearchQueryOperators.LESS_THAN =>

              whereClause = Some(sqls" lower(pe.email) < $inputVal $isPrimary ")

            case SearchQueryOperators.IS_EMPTY =>
              whereClause = Some(sqls" (pe.email IS NULL OR pe.email = '') ")

            case SearchQueryOperators.IS_NOT_EMPTY =>
              whereClause = Some(sqls" (pe.email IS NOT NULL AND pe.email != '') ")

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "current_sender_email" =>

        val inputVal: Option[Int] = ParseUtils.parseInt(value)

          inputVal match {
            case None =>
              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

            case Some(ces_id) =>
              operator match {

                case SearchQueryOperators.EQUAL =>

                  whereClause = Some(sqls" cp.current_campaign_email_settings_id = $ces_id ")


                case _ =>

                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

              }

          }


        // PROSPECTCOLUMNCHANGETAG
        case "first_name" | "last_name" | "company" | "city" | "country" | "domain" | "timezone" | "total_opens" | "total_clicks" | "prospect_source"
        | "state" | "phone" | "job_title" | "linkedin_url" =>


          val fieldName = SQLSyntax.createUnsafely(field.trim)

          var inputVal: Any = value

          if (field_type == FieldTypeEnum.NUMBER) {
            inputVal = ParseUtils.parseInt(value)
          } else if(field == "linkedin_url") {    // search on linkedin_url will come with EQUAL operator from FE.
            inputVal = LinkedinHelperFunctions.normalizeLinkedInURL(url = Some(inputVal.toString)).getOrElse("")
          }

          operator match {

            case SearchQueryOperators.CONTAINS =>

              val param = s"%${inputVal.toString}%"

              whereClause = Some(sqls" prospects.$fieldName ILIKE $param ")

            case SearchQueryOperators.NOT_CONTAINS =>

              val param = s"%${inputVal.toString}%"

              whereClause = Some(sqls" prospects.$fieldName NOT ILIKE $param ")

            case SearchQueryOperators.EQUAL =>

              whereClause = if (field == "linkedin_url") {

                // adding not null clause so that db engine pics the partial index for linkedin_url
                Some(sqls" prospects.linkedin_url = $inputVal AND prospects.linkedin_url IS NOT NULL ")

              } else Some(sqls" prospects.$fieldName = $inputVal ")

            case SearchQueryOperators.NOT_EQUAL =>

              whereClause = Some(sqls" prospects.$fieldName != $inputVal ")

            case SearchQueryOperators.GREATER_THAN =>

              whereClause = Some(sqls" prospects.$fieldName > $inputVal ")

            case SearchQueryOperators.LESS_THAN =>

              whereClause = Some(sqls" prospects.$fieldName < $inputVal ")

            case SearchQueryOperators.IS_EMPTY =>

              whereClause = Some(sqls" (prospects.$fieldName IS NULL OR prospects.$fieldName = '') ")

            case SearchQueryOperators.IS_NOT_EMPTY =>

              whereClause = Some(sqls" (prospects.$fieldName IS NOT NULL AND prospects.$fieldName != '') ")

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "in_list" =>

          operator match {

            case SearchQueryOperators.EQUAL =>

              ParseUtils.parseLong(value) match {
                case None =>
                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

                case Some(listId) =>
                  if (listId == 0) //any
                    whereClause = Some(sqls" prospects.list_id is not null ")
                  else if (listId == -1) //none
                    whereClause = Some(sqls" prospects.list_id is null ")
                  else //specific campaign
                    whereClause = Some(sqls" prospects.list_id = $listId ")

              }

            case _ =>

              throw new Exception(s"Invalid operator: ${operator} :: field: ${field}")

          }

        case "not_in_list" =>

          operator match {

            case SearchQueryOperators.EQUAL =>

              ParseUtils.parseLong(value) match {
                case None =>
                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

                case Some(listId) =>
                  if (listId == 0) //any
                    whereClause = Some(sqls" prospects.list_id is null ")

                  else if (listId == -1) //none
                    throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

                  else //specific campaign
                  // REF: IS DISTINCT FROM makes it null safe: https://www.postgresql.org/docs/current/functions-comparison.html
                    whereClause = Some(sqls" prospects.list_id IS DISTINCT FROM $listId ")

              }

            case _ =>

              throw new Exception(s"Invalid operator: ${operator} :: field: ${field}")

          }

        case "owner_name" =>

          operator match {

            case SearchQueryOperators.EQUAL =>

                ParseUtils.parseLong(value) match {

                  case None =>

                    throw new Exception("Invalid owner filter")

                  case Some(0) =>

                    whereClause = Some(sqls" a.id IS NULL ")

                  case Some(ownerAccountId) =>

                    whereClause = Some(sqls" a.id = $ownerAccountId ")
                }



            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "email_is_valid" =>

          operator match {

            case SearchQueryOperators.EQUAL =>

              value match {

                case "true" =>

                  whereClause = Some(sqls" (pe.invalid_email = FALSE AND pe.email_checked = TRUE AND pe.is_primary = TRUE) ")

                case "false" =>

                  whereClause = Some(sqls" pe.invalid_email = TRUE AND pe.is_primary = TRUE ")

                case _ =>

                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

              }

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "in_do_not_contact_list" =>

          //val in_do_not_contact_list = Prospect.getProspectCategoryId(team_id = team_id, text_id = ProspectCategory.DO_NOT_CONTACT, account = account)
          val in_do_not_contact_list: Long = Helpers.getCategoryByTextId(
            team_id = team_id,
            account = account,
            textId = ProspectCategory.DO_NOT_CONTACT,
            Logger = Logger
          ).get.id

          operator match {

            case SearchQueryOperators.EQUAL =>

              value match {

                case "true" =>

                  whereClause = Some(sqls" prospects.prospect_category_id_custom = ${in_do_not_contact_list} ")

                case "false" =>

                  whereClause = Some(sqls" prospects.prospect_category_id_custom != ${in_do_not_contact_list} ")

                case _ =>

                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

              }

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "created_at" |

             "last_contacted_at" |

             "last_replied_at" |

             "last_opened_at" |

             "latest_task_done_at" =>

          val timestamp = ParseUtils.parseLong(value).getOrElse(DateTime.now())

          val queryFieldName: SQLSyntax = field match {

            case "created_at" => sqls" prospects.created_at "

            case "last_contacted_at" => sqls" prospects.last_contacted_at "

            case "last_replied_at" => sqls" prospects.last_replied_at "

            case "last_opened_at" => sqls" prospects.last_opened_at "

            case "latest_task_done_at" => sqls" prospects.latest_task_done_at"

          }


          operator match {

            case SearchQueryOperators.GREATER_THAN =>

              whereClause = Some(sqls" $queryFieldName >= TO_TIMESTAMP($timestamp / 1000) ")

            case SearchQueryOperators.LESS_THAN =>

              whereClause = Some(sqls" $queryFieldName <= TO_TIMESTAMP($timestamp / 1000) ")

            case SearchQueryOperators.EQUAL =>

              whereClause = Some(sqls" ($queryFieldName >= TO_TIMESTAMP($timestamp / 1000) AND $queryFieldName < TO_TIMESTAMP(($timestamp + 86400000) / 1000)) ")

            case SearchQueryOperators.IS_EMPTY =>

              whereClause = Some(sqls" $queryFieldName IS NULL")

            case SearchQueryOperators.IS_NOT_EMPTY =>

              whereClause = Some(sqls" $queryFieldName IS NOT NULL")

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "prospect_category" =>

          Helpers.getCategoryById(
            team_id = team_id,
            account = account,
            categoryId = value.toLong,
            Logger = Logger
          ) match {

            case None => throw new Exception(s"Invalid prospect category filter: $value")

            case Some(catDB) =>

              operator match {

                case SearchQueryOperators.EQUAL =>

                  whereClause = Some(sqls" (prospects.prospect_category_id_custom = ${catDB.id}) ")

                case SearchQueryOperators.NOT_EQUAL =>

                  whereClause = Some(sqls" (prospects.prospect_category_id_custom != ${catDB.id}) ")

                case _ =>

                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

              }
          }

        case "current_step_type" =>

          CampaignStepType.fromKey(str = value) match {
            case  Failure(e) => throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")
            case Success(value) =>
              operator match {
                case SearchQueryOperators.EQUAL =>
                  whereClause = Some(sqls" cp.current_step_type = ${value.toKey} ")

                case SearchQueryOperators.NOT_EQUAL =>
                  whereClause = Some(sqls" cp.current_step_type != ${value.toKey} ")

                case _ =>

                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")
              }
          }

        case "prospect_id" =>

          operator match {

            case SearchQueryOperators.EQUAL =>

              ParseUtils.parseLong(value) match {

                case None =>
                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")


                case Some(prospectId) =>

                  whereClause = Some(sqls" prospects.id = $prospectId ")

              }

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "has_tag" | "not_has_tag"=>

          operator match {

            case SearchQueryOperators.EQUAL =>

              ParseUtils.parseLong(value) match {
                case None =>
                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

                case Some(tagId) =>

                  val tagIdCheck = if (tagId == 0) {
                    // Special case: prospect should have any one tag, so no check
                    sqls" "

                  } else {

                    sqls" AND tags_prospects.tag_id = $tagId "

                  }

                  val tpSql = sqls"SELECT * FROM tags_prospects WHERE tags_prospects.prospect_id = prospects.id $tagIdCheck"

                  val whereClauseForCondition = field match {

                    case "has_tag" =>
                      sqls""" EXISTS($tpSql) """

                    case "not_has_tag" =>
                      sqls""" NOT EXISTS($tpSql) """
                  }
                  whereClause = Some(whereClauseForCondition)
              }

            case _ =>

              throw new Exception(s"Invalid operator: ${operator} :: field: ${field}")

          }

        case "step" =>

          operator match {

            case SearchQueryOperators.EQUAL =>

              ParseUtils.parseLong(value) match {

                case None =>

                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

                case Some(stepId) =>

                  whereClause = Some(
                    sqls""" EXISTS(
                          SELECT *
                          FROM campaigns_prospects cp_steps
                          where cp_steps.prospect_id = prospects.id
                          AND cp_steps.current_step_id = $stepId
                          AND cp_steps.active
                          and cp_steps.team_id = $team_id
                        )
                      """
                  )

              }

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "prospect_account_name" =>

          var inputVal = value

          operator match {

            case SearchQueryOperators.CONTAINS =>

              val param = s"%${inputVal.toString}%"

              whereClause = Some(sqls" pa.name ILIKE $param ")

            case SearchQueryOperators.NOT_CONTAINS =>

              val param = s"%${inputVal.toString}%"

              whereClause = Some(sqls" pa.name NOT ILIKE $param ")


            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "prospect_account_id" =>

          operator match {

            case SearchQueryOperators.EQUAL =>

                  whereClause = Some(sqls" pa.uuid = $value ")

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }

        case "latest_reply_sentiment" =>
          operator match {
            case SearchQueryOperators.EQUAL => whereClause = Some(sqls" prospects.latest_reply_sentiment_uuid = ${value}")

            case SearchQueryOperators.NOT_EQUAL => whereClause = Some(sqls" prospects.latest_reply_sentiment_uuid != ${value}")

            case _ =>
              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")
          }

        case "clicked" | "not_clicked" =>

          val inputVal = value

          val linkClause = operator match {

            case SearchQueryOperators.CONTAINS =>

              val param = s"%${inputVal.toString}%"

              sqls" AND p_ev.clicked_url ILIKE $param "

            case SearchQueryOperators.NOT_CONTAINS =>

              val param = s"%${inputVal.toString}%"

              sqls" AND p_ev.clicked_url NOT ILIKE $param "

            case SearchQueryOperators.EQUAL =>

              sqls" AND p_ev.clicked_url = $inputVal "

            case SearchQueryOperators.NOT_EQUAL =>

              sqls" AND p_ev.clicked_url != $inputVal "

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }


              val cpSql =
                sqls""" SELECT * FROM prospect_events p_ev
                      WHERE p_ev.prospect_id = prospects.id
                      AND p_ev.team_id = $team_id
                      AND p_ev.event_type = 'clicked' $linkClause"""

              val whereClauseForCondition = field match {

                case "clicked" =>

                  sqls""" EXISTS($cpSql) """

                case "not_clicked" =>

                  sqls""" NOT EXISTS($cpSql) """

              }

          Logger.info(s"\n\n\n\n\n whereClauseForCondition $whereClauseForCondition \n\n\n\n")
              whereClause = Some(whereClauseForCondition)


        // THESE SHOULD not BE IN THE "HAVING" CLAUSE: update: having clause was causing issues in sub-filters
        // These are all the queries on the cp_all join
        case "assigned_to_campaign" |

             "not_assigned_to_campaign" |

             "has_completed_campaign" |

             "has_not_completed_campaign" |

             "has_sent" |

             "has_not_sent" |

             "to_check" |

             "has_unsubscribed" |

             "opened" |

             "not_opened" |

             "replied" |

             "not_replied" |

             "bounced" |

             "not_bounced" |

             "has_clicked" |

             "has_not_clicked" |

             "has_not_unsubscribed" =>

          operator match {

            case SearchQueryOperators.EQUAL =>

              ParseUtils.parseLong(value) match {
                case None =>
                  throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

                case Some(campaignId) =>



                  val cmIdClause = campaignId match {
                    case 0 =>
                      sqls""

                    case -1 =>
                      throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

                    case _ =>
                      sqls" AND cp_all.campaign_id = $campaignId"
                  }


                  val cpSql = sqls" SELECT * FROM campaigns_prospects cp_all where cp_all.team_id = $team_id and cp_all.prospect_id = prospects.id $cmIdClause"

                  val whereClauseForCondition = field match {

                    case "assigned_to_campaign" =>
                      sqls""" EXISTS($cpSql AND cp_all.active) """

                    case "not_assigned_to_campaign" =>

                      sqls""" NOT EXISTS($cpSql AND cp_all.active) """

                    case "has_completed_campaign" =>

                      sqls""" EXISTS($cpSql AND cp_all.completed) """

                    case "has_not_completed_campaign" =>

                      sqls""" NOT EXISTS($cpSql AND cp_all.completed) """

                    case "has_sent" =>

                      sqls""" EXISTS($cpSql AND cp_all.sent) """

                    case "has_not_sent" =>

                      sqls""" NOT EXISTS($cpSql AND cp_all.sent) """

                    case "to_check" =>

                      sqls""" EXISTS($cpSql AND cp_all.to_check) """

                    case "has_unsubscribed" =>

                      sqls""" EXISTS($cpSql AND cp_all.opted_out) """

                    case "has_not_unsubscribed" =>

                      sqls""" NOT EXISTS($cpSql AND cp_all.opted_out) """

                    case "opened" =>

                      sqls""" EXISTS($cpSql AND cp_all.opened) """

                    case "not_opened" =>

                      sqls""" NOT EXISTS($cpSql AND cp_all.opened) """

                    case "has_clicked" =>

                      sqls""" EXISTS($cpSql AND cp_all.clicked) """

                    case "has_not_clicked" =>

                      sqls""" NOT EXISTS($cpSql AND cp_all.clicked) """

                    case "replied" =>

                      sqls""" EXISTS($cpSql AND cp_all.replied) """

                    case "not_replied" =>

                      sqls""" NOT EXISTS($cpSql AND cp_all.replied) """

                    case "bounced" =>

                      sqls""" EXISTS($cpSql AND cp_all.bounced) """

                    case "not_bounced" =>

                      sqls""" NOT EXISTS($cpSql AND cp_all.bounced) """

                  }
                  whereClause = Some(whereClauseForCondition)
              }

            case _ =>

              throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

          }


        case _ =>

          Logger.error(s"ProspectQuery FATAL filter prospects table: field name is $field")

          throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")

      }
    } else {

      // handle filtering custom_fields

      if (field_type == FieldTypeEnum.TEXT) {

        val param = s"%${value.toString}%"

        // Regarding json ?? operator: https://github.com/jdbi/jdbi/issues/174#issuecomment-142068731
        val q = operator match {

          case SearchQueryOperators.CONTAINS =>

            sqls" prospects.custom_fields ->> $field ILIKE $param "

          case SearchQueryOperators.NOT_CONTAINS =>

            sqls" (NOT prospects.custom_fields ?? $field) OR (prospects.custom_fields ->> $field NOT ILIKE $param) "

          case SearchQueryOperators.EQUAL =>

            sqls" prospects.custom_fields ->> $field = $value "

          case SearchQueryOperators.NOT_EQUAL =>

            sqls" (NOT prospects.custom_fields ?? $field) OR (prospects.custom_fields ->> $field != $value) "

          case SearchQueryOperators.IS_EMPTY =>

            sqls" (prospects.custom_fields -> $field) IS NULL "

          case SearchQueryOperators.IS_NOT_EMPTY =>

            sqls" (prospects.custom_fields -> $field) IS NOT NULL "

          case _ =>

            throw new Exception(s"Invalid operator: $operator :: custom_field: $field :: $value")

        }

        whereClause = Some(q)

      } else if (field_type == FieldTypeEnum.NUMBER) {

        val parsedVal: Option[Long] = if (operator == SearchQueryOperators.IS_NOT_EMPTY || operator == SearchQueryOperators.IS_EMPTY) Some(1L) // passing a dummy value as it is not used
        else ParseUtils.parseLong(value)

        parsedVal match {

          case None => throw new Exception(s"Invalid value: $operator :: custom_field: $field :: $value")


          case Some(numValue) =>

            operator match {

              case SearchQueryOperators.EQUAL =>

                whereClause = Some(sqls" CAST (prospects.custom_fields ->> $field AS NUMERIC) = $numValue ")

              case SearchQueryOperators.GREATER_THAN =>

                whereClause = Some(sqls" CAST (prospects.custom_fields ->> $field AS NUMERIC) > $numValue ")

              case SearchQueryOperators.LESS_THAN =>

                whereClause = Some(sqls" CAST (prospects.custom_fields ->> $field AS NUMERIC) < $numValue ")

              case SearchQueryOperators.IS_EMPTY =>

                whereClause = Some(sqls" (prospects.custom_fields -> $field) IS NULL ")

              case SearchQueryOperators.IS_NOT_EMPTY =>

                whereClause = Some(sqls" (prospects.custom_fields -> $field) IS NOT NULL ")

              case _ =>

                throw new Exception(s"Invalid operator: $operator :: custom_field: $field :: $value")

            }
        }


      } else {

        throw new Exception(s"Invalid filter: $operator :: field: $field :: value: $value")
      }
    }


    SearchQueryGetStatement(
      whereClause = whereClause,
      havingClause = havingClause
    )
  }


  def getSortStatementWithLimit(
    teamId: Long,
    sort: Option[SearchQuerySort],
    Logger: SRLogger,
    queryTimeline: Option[InferredQueryTimeline] = None,
    limit: Int = 500,
    offset: Int = 0,
    maxCountLimit: Int = 1000,
    isApiCall: Boolean = false,
  ): SQLSyntax = {

    val sortD = getSortDirectionSQLS(sort = sort)

    val orderByQuery: SQLSyntax = if (sort.isEmpty) {


      /*
      This sorting was VERY SLOW

      // show latest prospects by default, this was first requested by Toptal team

      if (showLoggedinUserDataFirstByDefault) {
        sqls""" ORDER BY
            CASE prospects.account_id
              WHEN $loggedinAccountId THEN 1
              ELSE 2
            END ASC,

            prospects.id DESC
        """
      } else {

        sqls""" ORDER BY prospects.id DESC """

      }
      */

      sqls""" ORDER BY prospects.team_id, prospects.id DESC """


    } else {

      val s = sort.get

      if (!s.is_custom) {

        // check field name against allowed default field names: To prevent sql injection
        val isValidField = prospectColumnDef
          .allColumns(teamId = teamId, channel = None)
          .filter(_.sortable)
          .exists(col => col.name == s.field.trim)

        if (!isValidField) {

          Logger.error(s"ProspectPaginationUtil FATAL order by prospects table: field name is ${s.field} :: $sort")

          throw new Exception(s"Invalid sort query: $sort")

        } else {

          s.field match {

            case "prospect_account" =>

              sqls" ORDER BY pa.name $sortD "

            case "last_contacted_at" | "last_replied_at" | "last_opened_at" =>

              // REF: https://github.com/scalikejdbc/scalikejdbc/issues/320#issuecomment-********
              val fieldName = SQLSyntax.createUnsafely(s.field.toString)

              sqls""" ORDER BY prospects.$fieldName $sortD NULLS LAST """

            case "email" =>

              sqls" ORDER BY lower(pe.email) $sortD "

            case "latest_reply_sentiment" =>
              sqls" ORDER BY prospects.latest_reply_sentiment_uuid $sortD "


            case _ =>

              // REF: https://github.com/scalikejdbc/scalikejdbc/issues/320#issuecomment-********
              val fieldName = SQLSyntax.createUnsafely(s.field.toString)

              sqls" ORDER BY prospects.$fieldName $sortD "

          }

        }
      } else if (s.is_numeric) {
        sqls" ORDER BY CAST (NULLIF(prospects.custom_fields ->> ${s.field}, '') AS NUMERIC) $sortD "

      } else {
        sqls" ORDER BY prospects.custom_fields ->> ${s.field} $sortD "
      }
    }

    orderByQuery

  }

  def getSortStatementWithLimitV2(
                                   teamId: Long,
                                   sort: Option[SearchQuerySort],
                                   Logger: SRLogger,
                                   queryTimeline: Option[InferredQueryTimeline] = None,
                                   limit: Int = 500,
                                   offset: Int = 0,
                                   maxCountLimit: Int = 1000,
                                   isApiCall: Boolean = false,
                                 ): SQLSyntax = {

    val sortD = getSortDirectionSQLS(sort = sort)

    val orderByQuery: SQLSyntax = if (sort.isEmpty) {


      sqls""" ORDER BY pt1.team_id, pt1.id DESC """


    } else {

      val s = sort.get

      if (!s.is_custom) {

        // check field name against allowed default field names: To prevent sql injection
        val isValidField = prospectColumnDef
          .allColumns(teamId = teamId, channel = None)
          .filter(_.sortable)
          .exists(col => col.name == s.field.trim)

        if (!isValidField) {

          Logger.error(s"ProspectPaginationUtil FATAL order by prospects table: field name is ${s.field} :: $sort")

          throw new Exception(s"Invalid sort query: $sort")

        } else {

          s.field match {

            case "prospect_account" =>

              sqls" ORDER BY pa.name $sortD "

            case "last_contacted_at" | "last_replied_at" | "last_opened_at" =>

              // REF: https://github.com/scalikejdbc/scalikejdbc/issues/320#issuecomment-********
              val fieldName = SQLSyntax.createUnsafely(s.field.toString)

              sqls""" ORDER BY pt1.$fieldName $sortD NULLS LAST """

            case "email" =>

              sqls" ORDER BY lower(pe.email) $sortD "

            case "latest_reply_sentiment" =>
              sqls" ORDER BY pt1.latest_reply_sentiment_uuid $sortD "  //TODO: check add in pt1 if not


            case "last_call_made_at" =>

                sqls""" ORDER BY ccl.completed_at $sortD NULLS LAST """

            case _ =>

              // REF: https://github.com/scalikejdbc/scalikejdbc/issues/320#issuecomment-********
              val fieldName = SQLSyntax.createUnsafely(s.field.toString)

              sqls" ORDER BY pt1.$fieldName $sortD "

          }

        }
      } else if (s.is_numeric) {
        sqls" ORDER BY CAST (NULLIF(pt1.custom_fields ->> ${s.field}, '') AS NUMERIC) $sortD "

      } else {
        sqls" ORDER BY pt1.custom_fields ->> ${s.field} $sortD "
      }
    }

    orderByQuery

  }

  def validateRequestBody(
                           searchQuery: SearchQuery,
                           isApiCall: Boolean,
                           Logger: SRLogger
                         ): Either[RequestBodyValidationError, SearchQuery] = {
    if (!isApiCall) {
      if (searchQuery.page.nonEmpty) {
        Right(searchQuery)
      } else {
        Left(ValidationError("Page parameter in the request body not passed"))
      }
    } else {
      Right(searchQuery)
    }
  }

}
