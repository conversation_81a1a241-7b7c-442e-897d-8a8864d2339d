package api.middleware

import api.SRAPIResponse
import api.internal_support.dao.InternalSupportApiDAO
import play.api.mvc.{ActionBuilder, ActionRefiner, AnyContent, Request, Result, WrappedRequest}
import utils.{Help<PERSON>, SRLogger}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}
case class SupportRequest[A](
                             logger: SRLogger,
                             response: SRAPIResponse,
                             request: Request[A]
                            ) extends WrappedRequest[A](request)


class InternalSupportAuthService(
  loggingAction: LoggingAction,
  ec: ExecutionContext,
                                  internalSupportApiDAO: InternalSupportApiDAO
                                ) {

  def isSupportCallAuthorized: ActionBuilder[SupportRequest, AnyContent] = loggingAction andThen new ActionRefiner[LoggingRequest, SupportRequest] {

    override def executionContext: ExecutionContext = ec

    override protected def refine[A](request: LoggingRequest[A]): Future[Either[Result, SupportRequest[A]]] = {

      val apiKeyOpt = request.headers.get("X-API-KEY").map(_.trim)
      val userIp = Helpers.getClientIPFromGCPLoadbalancer(request = request)
        .getOrElse("0.0.0.0")

      val Logger = request.Logger

      val Res = new SRAPIResponse(Logger = Logger)
      if (apiKeyOpt.isEmpty) {

        Future.successful(Left(Res.UnauthorizedError("Send an API Key")))

      } else {
        val srSupportAppConfigTry = internalSupportApiDAO.getAPIAndIp()

        srSupportAppConfigTry match {
          case Failure(err) =>
            Future.successful(Left(Res.ServerError("Error while authenticating request", Some(err))))

          case Success(None) =>
            Future.successful(Left(Res.NotFoundError("Couldn't find API and IP in database")))

          case Success(Some(srSupportAppConfig)) =>


            if (srSupportAppConfig.sr_support_key != apiKeyOpt.get) {

              Future.successful(Left(Res.UnauthorizedError("Send correct API Key")))

            } else if (userIp != srSupportAppConfig.sr_support_app_origin_ip) {

              Future.successful(Left(Res.UnauthorizedError("WRONG IP access not allowed")))

            } else {

              val accountRequest = SupportRequest(
                logger = request.Logger,
                response = request.Response,
                request = request
              )
              Future.successful(Right(accountRequest))

            }
        }
      }


    }
  }

}
