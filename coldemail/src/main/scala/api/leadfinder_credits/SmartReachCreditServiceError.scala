package api.leadfinder_credits


sealed trait SmartReachCreditServiceError extends Exception

object SmartReachCreditServiceError {

  case class InSufficientCreditsError(
                                       message: String = "",
                                       err: Throwable = None.orNull
                                     ) extends SmartReachCreditServiceError {
    override def getMessage: String = message
    override def getCause: Throwable = err
  }

  case class FailedOperationError(
                                   message: String = "",
                                   err: Throwable = None.orNull
                                 ) extends SmartReachCreditServiceError {
    override def getMessage: String = message
    override def getCause: Throwable = err
  }

  case class SQLFailureError(
                                   message: String = "",
                                   err: Throwable = None.orNull
                                 ) extends SmartReachCreditServiceError {
    override def getMessage: String = message

    override def getCause: Throwable = err
  }

  case class ErrorOnRevertingCreditsBackForFailedOperation(
                                                            message: String = "",
                                                            err: Throwable = None.orNull
                                                          ) extends SmartReachCreditServiceError {
    override def getMessage: String = message
    override def getCause: Throwable = err
  }
}
