package api.tasks.pgDao

import api.AppConfig
import api.accounts.EmailScheduledIdOrTaskId.TaskId
import api.accounts.models.{AccountId, OrgId}
import api.accounts.TeamId
import api.campaigns.models.{CampaignStepType, ChannelStepType}
import api.campaigns.services.CampaignId
import api.emails.{CampaignIdAndProspectId, OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId}
import api.linkedin.models.{CDJobUid, CaptainDataWebhookResponse, CaptainDataWorkflowStatus, LinkedinSettingId, LinkedinSettingUuid}
import api.phantombuster.{ExecuteAutoLinkedinTasksError, PhantomBusterApiKey, PhantomBusterContainerId, PhantomBusterContainerOutput, RunningLinkedinTaskDetails, TaskDetailsForPhantomBusterWebhook}
import api.prospects.InferredQueryTimeline
import api.prospects.dao.ProspectIdAndPotentialDuplicateProspectId
import io.sr.billing_common.models.PlanType
import api.tasks.models.*
import api.tasks.pgDao.TaskPgDAO.{assigneeIdCheck, getAssigneeSQLS, getChangeStatusSQLS, getChangedDueDateSQLS, getFetchTaskScheduledQuery, getPrioritySQL, getRecordingUrlForTaskSQL, getStatusSQL, getTaskDataSQLS, getTaskPrioritySQLS, getTaskStatusSQLS, getTaskTypeSQL, getTaskTypeSQLS, getUpdatePrioritySQLS, getUpdateStatusSQLS, getUpdateTaskDataSQLS, taskDataFromDB, taskDetailsToProcessWebhookUpdateFromDb, taskFromDB}
import api.tasks.pgDao.TaskPgDAO.{getAssigneeSQLS, getChangeStatusSQLS, getChangedDueDateSQLS, getFetchTaskScheduledQuery, getPrioritySQL, getRecordingUrlForTaskSQL, getStatusSQL, getTaskDataSQLS, getTaskPrioritySQLS, getTaskStatusSQLS, getTaskTypeSQL, getTaskTypeSQLS, getUpdatePrioritySQLS, getUpdateStatusSQLS, getUpdateTaskDataSQLS, taskDataFromDB, taskDetailsToProcessWebhookUpdateFromDb, taskFromDB}
import api.tasks.pgDao.TaskPgDAO.{assigneeIdCheck, getAssigneeSQLS, getChangeStatusSQLS, getChangedDueDateSQLS, getFetchTaskScheduledQuery, getPrioritySQL, getRecordingUrlForTaskSQL, getStatusSQL, getTaskDataSQLS, getTaskPrioritySQLS, getTaskStatusSQLS, getTaskTypeSQL, getTaskTypeSQLS, getUpdatePrioritySQLS, getUpdateStatusSQLS, getUpdateTaskDataSQLS, taskDataFromDB, taskFromDB}
import api.prospects.models.{ProspectId, StepId}
import api.tasks.models
import api.tasks.models.TaskStatusType.QueuedToMq
import api.tasks.services.{GroupDuplicateTaskDetails, TaskIdAndType, TaskUuid}
import api.team_inbox.service.ReplySentimentUuid
import app_services.db_query_counter.SrDBQueryCounterService
import org.joda.time.{DateTime, DateTimeZone}
import org.postgresql.util.PSQLException
import scalikejdbc.{DBSession, SQLSyntax, sqls}
import play.api.libs.json.{JsValue, Json, Writes}
import utils.dbutils.SQLUtils
import utils.email.EmailHelper
import utils.jodatimeutils.JodaTimeUtils
import utils.linkedin.LinkedinHelperFunctions
import utils.security.EncryptionHelpers
import utils.timezones.TimezoneUtils

import scala.util.Failure
//import scalikejdbc.interpolation.SQLSyntax
import play.api.libs.json.JodaWrites._
import play.api.libs.json.JodaReads._
import scalikejdbc.jodatime.JodaWrappedResultSet.fromWrappedResultSetToJodaWrappedResultSet
import scalikejdbc.{DB, NoExtractor, SQL, SQLSyntax, WrappedResultSet, scalikejdbcSQLInterpolationImplicitDef}

import scala.concurrent.{ExecutionContext, Future}
import sr_scheduler.CampaignStatus
import sr_scheduler.models.{ChannelType, ScheduledProspectsCountForCampaign}
import utils.{Helpers, SRLogger}

import java.security.SecureRandom
import scala.util.{Success, Try}
import scala.concurrent.blocking
import api.captain_data.CaptainDataJobUID

case class TaskIdAndTeamId(taskId: String, teamId: TeamId)

sealed trait Settings_Table {
  def toKey: SQLSyntax
  
}

object Settings_Table {

  private val WHATSAPP_SETTING = sqls"""whatsapp_settings"""
  private val GENERAL_SETTING = sqls"""general_channel_settings"""
  private val LINKEDIN_SETTING = sqls"""linkedin_settings"""
  private val SMS_SETTING = sqls"""sms_settings"""
  private val CALL_SETTING = sqls"""call_settings"""

  case object WhatsAppSetting extends Settings_Table {
    override def toKey: SQLSyntax = WHATSAPP_SETTING

  }

  case object GeneralSetting extends Settings_Table {
    override def toKey: SQLSyntax = GENERAL_SETTING

  }

  case object LinkedinSetting extends Settings_Table {
    override def toKey: SQLSyntax = LINKEDIN_SETTING

  }

  case object  SmsSetting extends  Settings_Table {
    override  def toKey:SQLSyntax = SMS_SETTING
  }

  case object CallSetting extends Settings_Table {
    override def toKey: SQLSyntax = CALL_SETTING
  }
}

case class ChannelSettings(
                          team_id: Long,
                          channelSettingId: String
                          )

case class TaskDetailsToProcessWebhookUpdate(
                                              taskId: TaskUuid,
                                              orgId: OrgId,
                                              teamId: TeamId
                                            )

class TaskPgDAO(srDBQueryCounterService: SrDBQueryCounterService) {


  private def retryOnSpecificError[T](maxRetries: Int, logger: SRLogger)(block: => Try[T]): Try[T] = {
    var retries = 0
    var result: Try[T] = Failure(new Exception("Initial failure"))

    while (retries < maxRetries) {
      try {
        result = block
        return result
      } catch {
        case e: PSQLException if e.getSQLState == "55000" && e.getMessage.contains("tuple to be locked was already moved") =>
          logger.warn(s"Retrying due to concurrent update error on partitioned table. Attempt ${retries + 1} of $maxRetries")
          retries += 1
          if (retries < maxRetries) {
            Thread.sleep(1000) // Sleep for 1000 ms before the next retry
          }
          if (retries >= maxRetries) {
            return Failure(e)
          }
      }
    }
    result
  }


  def updateEmailAndSubjectBody(
                                 subject: String,
                                 body: String,
                                 prospect_id: ProspectId,
                                 campaign_id: CampaignId,
                                 step_id: StepId
                               ): Try[String] = Try{

    DB.autoCommit { implicit session =>

      sql"""
           Update Tasks
              SET task_subject = ${subject},
              task_body = ${body}
           WHERE
              prospect_id = ${prospect_id.id}
              AND step_id = ${step_id.id}
              AND campaign_id = ${campaign_id.id}

           RETURNING task_id

         """
        .map(rs => rs.string("task_id"))
        .single
        .apply()
        .get

    }

  }

  def createNewTask(
                     task_data: NewTask,
                     taskId: String,
                     account_id: Long,
                     team_id: Long,
                     created_at: DateTime
                   )(implicit ec:ExecutionContext):Future[String] = Future{

    val (taskDataSpecificColumns,taskDataSpecificColumnValues) = getTaskDataSQLS(task_data = task_data)

    val (prioritySpecificColumns, prioritySpecificColumnValues) = getPrioritySQL(task_data = task_data)

    val (taskStatusSpecificColumns,taskStatusSpecificColumnValues) = getStatusSQL(task_data = task_data)

    val (taskTypeSpecificColumns, taskTypeSpecificColumnValues) = getTaskTypeSQL(task_data = task_data)


    val res = DB autoCommit { implicit session =>

      sql"""
           INSERT INTO tasks (
             $taskDataSpecificColumns
             campaign_id,
             campaign_name,
             step_id,
             step_label,
             created_via,
             is_opening_step,
             task_id,
             is_auto_task,
             $taskTypeSpecificColumns
             assignee_id,
             team_id,
             prospect_id,
             $prioritySpecificColumns
             added_by,
             note,
             channel_type,
             $taskStatusSpecificColumns
             emails_scheduled_uuid,
             created_at
           )

           VALUES (
             $taskDataSpecificColumnValues
             ${task_data.campaign_id},
             ${task_data.campaign_name},
             ${task_data.step_id},
             ${task_data.step_label},
             ${task_data.created_via.toKey},
             ${task_data.is_opening_step},
             $taskId,
             ${task_data.is_auto_task},
             $taskTypeSpecificColumnValues
             ${task_data.assignee_id},
             ${team_id},
             ${task_data.prospect_id},
             $prioritySpecificColumnValues
             ${account_id},
             ${task_data.note.getOrElse(None)},
             ${task_data.task_type.channelType.toString},
             $taskStatusSpecificColumnValues
             ${task_data.emailsScheduledUuid.map(_.uuid)}::uuid,
             ${created_at}
           )

            RETURNING task_id
           """
        .map(rs => rs.string("task_id"))
        .single
        .apply()
        .get

    }
    res
  }


  def deleteTaskForRevert(
                           taskIds: List[String],
                           teamId: TeamId,
                           status: Option[TaskStatusType],
                           permittedAccountIds: Seq[Long]
                         )(using Logger: SRLogger): Try[List[OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId]] = {

    DB.autoCommit { implicit session => {
      Logger.info(s"deleteTaskForRevertWith : deleting task from revert flow : ${taskIds.map(t => s"task_id_$t")} team_id_${teamId}")

      deleteWithSession(
        taskIds = taskIds,
        teamId = teamId,
        status = status,
        permittedAccountIds = permittedAccountIds
      )

    }
    }

  }


  def deleteWithSession(
                         taskIds: List[String],
                         teamId: TeamId,
                         status: Option[TaskStatusType],
                         permittedAccountIds: Seq[Long]
                       )(using Logger: SRLogger, session: DBSession): Try[List[OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId]] = Try {
    if (taskIds.isEmpty) {
      Logger.shouldNeverHappen(s"taskIds empty for deleteTaskForRevert")
      List()
    } else {

      Logger.info(s"deleteTaskForRevertWithSession : deleting task from revert flow : ${taskIds.map(t => s"task_id_$t")} team_id_${teamId}")

      val statusCondition = status match {
        case Some(value) => sqls"AND status = ${value.toKey}"
        case None => sqls""
      }

      val accountCond = if (permittedAccountIds.nonEmpty) {
        sqls"and assignee_id IN (${permittedAccountIds})"
      } else sqls""

      sql"""
         DELETE FROM tasks
         WHERE task_id IN (${taskIds})
          AND team_id = ${teamId.id}
          $statusCondition
          $accountCond
         RETURNING prospect_id, campaign_id, task_id, step_id
         """
        .map(rs => {
          OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId(
            campaignId = rs.longOpt("campaign_id").map(CampaignId(_)),
            prospectId = rs.longOpt("prospect_id").map(ProspectId(_)),
            emailSettingId = None,
            team_id = teamId,
            taskId = Some(TaskId(rs.string("task_id"))),
            step_id = rs.longOpt("step_id").map(v => StepId(v)),
          )
        })
        .list
        .apply()

    }
  }

  def findLastContactedProspectByChannel(
                                                  prospectIds: List[ProspectId],
                                                  channelType: ChannelType,
                                                  teamId: TeamId
                                                ): Try[Option[ProspectId]] = Try {

    if (prospectIds.isEmpty) {
      None
    }
    else {
      DB readOnly {implicit session =>
        sql"""
           SELECT prospect_id FROM tasks
           WHERE prospect_id IN (${prospectIds.map(_.id)})
           AND team_id = ${teamId.id}
           AND channel_type = ${channelType.toString}
           AND status = ${TaskStatusType.Done.toKey}

           ORDER BY done_at DESC
           LIMIT 1
           ;
           """
          .map(rs => rs.longOpt("prospect_id").map(ProspectId(_)))
          .single
          .apply()
          .flatten
      }
    }

  }


  def changeStatus(
                    task_id: String,
                    changeTime: DateTime,
                    task_status: UpdateTaskStatus,
                    team_id: Long,
                    changeStatusPermissionCheck: ChangeStatusPermissionCheck
                  )(implicit ec: ExecutionContext
  ): Future[String] = Future{
  
    val doerOpt = ChangeStatusPermissionCheck.getDoer(changeStatusPermissionCheck = changeStatusPermissionCheck)
    val (taskStatusSpecificColumns) = getChangeStatusSQLS(status = task_status, doer = doerOpt, changeTime = changeTime)
    val currentTime = DateTime.now()
    val assigneeCheck = assigneeIdCheck(changeStatusPermissionCheck = changeStatusPermissionCheck)

    DB autoCommit { implicit session =>
      sql"""
           UPDATE tasks
           SET
              updated_at = $currentTime,
              $taskStatusSpecificColumns
           WHERE 
           task_id = ${task_id}
           ${assigneeCheck}
           AND team_id = $team_id

           RETURNING task_id
         """
        .map(rs => rs.string("task_id"))
        .single
        .apply()
        .get


    }

  }
// used in migration only
  def deleteDueTasksByCampaignIdAndProspectId(
                                               campaignId: CampaignId,
                                               prospectId: ProspectId,
                                               teamId: TeamId
                                             ): Try[Int] = Try {
    DB autoCommit {implicit session =>
      sql"""
           DELETE FROM tasks
           WHERE campaign_id = ${campaignId.id}
           AND prospect_id = ${prospectId.id}
           AND team_id = ${teamId.id}
           AND status = ${TaskStatusType.Due.toKey}
           ;
           """
        .update
        .apply()
    }
  }

  def deleteNotDoneTasksDuplicateProspects(
                                            teamId: TeamId,
                                            prospects: Seq[ProspectId]
                                          )(implicit session: DBSession): Try[List[ProspectId]] = Try {
    if (prospects.isEmpty) {
      List()
    } else {
        sql"""
           DELETE FROM tasks
            WHERE prospect_id IN (${prospects.map(_.id)})
              AND team_id = ${teamId.id}
              AND status != ${TaskStatusType.Done.toKey}
            RETURNING
              prospect_id
           ;
           """
          .map(rs => ProspectId(rs.long("prospect_id")))
          .list
          .apply()
    }
  }

  def updateMasterProspectForDoneTasks(
                                        teamId: TeamId,
                                        prospects: List[ProspectIdAndPotentialDuplicateProspectId],
                                        masterProspectId: ProspectId
                                      )(implicit session: DBSession): Try[List[ProspectId]] = Try {

    var valueParameters = List[Any]()

    val valuePlaceholder: SQLSyntax = prospects.map(p => {

        valueParameters = valueParameters ::: List(
          masterProspectId.id,
          p.prospectId.id,
          p.potentialDuplicateProspectId.id,

          TaskStatusType.Done.toKey,
          teamId.id
        )

        sqls"""
           (
              ?,
              ?,
              ?,

              ?,
              ?
            )
          """

      })
      .reduce((vp1, vp2) => sqls"$vp1, $vp2")

    sql"""
           UPDATE tasks
            SET
	            prospect_id = temp.master_prospect,
              potential_duplicate_prospect_id = temp.potential_duplicate_prospect_id

              FROM (
                  VALUES $valuePlaceholder
                )
                AS temp(
                  master_prospect,
                  prospect_id,
                  potential_duplicate_prospect_id,

                  task_status,
                  team_id
                )
          WHERE
            tasks.team_id = temp.team_id
              AND tasks.prospect_id = temp.prospect_id
              AND tasks.status = temp.task_status
          returning tasks.prospect_id
            ;
           """
      .bind(valueParameters*)
      .map(rs => ProspectId(rs.long("prospect_id")))
      .list
      .apply()
  }

  def findLatestCampaignFromWhichLinkedinTaskIsDoneForProspect(
                                                                prospectId: ProspectId,
                                                                teamId: TeamId
                                                              ): Try[Option[CampaignId]] = Try {

    DB readOnly { implicit session =>
      sql"""
           SELECT t.campaign_id FROM tasks t
           WHERE t.prospect_id = ${prospectId.id}
           AND t.campaign_id IS NOT NULL
           AND t.channel_type = ${ChannelType.LinkedinChannel.toString}
           AND t.status = ${TaskStatusType.Done.toKey}
           AND t.team_id = ${teamId.id}
           ORDER BY t.done_at DESC
           LIMIT 1
           ;
           """
        .map(rs => CampaignId(rs.long("campaign_id")))
        .single
        .apply()
    }
  }

  def changePriority(
                  task_id: String,
                  task_priority: TaskPriority,
                  team_id: Long,
                  permittedAccountIds: Seq[Long]
                  )(implicit ec: ExecutionContext
      ):Future[Option[String]] = Future{

    val (prioritySpecificColumn) = getUpdatePrioritySQLS(priority = task_priority)
    val currentTime = DateTime.now()

    DB autoCommit { implicit session =>
      sql"""
           UPDATE tasks
           SET
               updated_at = $currentTime,
               $prioritySpecificColumn
           WHERE assignee_id IN (${permittedAccountIds})
           AND task_id = ${task_id}
           AND team_id = $team_id

           RETURNING task_id
         """
        .map(rs => rs.string("task_id"))
        .single
        .apply()

    }

  }

  def addNote(
                  task_id: String,
                  note: String,
                  team_id: Long,
                  permittedAccountIds: Seq[Long]
                  )(implicit executionContext: ExecutionContext
  ):Future[Option[String]] = Future{

    val currentTime = DateTime.now()

    DB autoCommit { implicit session =>
      sql"""
           UPDATE tasks
             SET
               updated_at = $currentTime,
               note = ${note}
             WHERE assignee_id IN (${permittedAccountIds})
             AND
               task_id = ${task_id}
             AND
               team_id = $team_id

           RETURNING task_id
         """
        .map(rs => rs.string("task_id"))
        .single
        .apply()
    }

  }

  def getTasksForCount(
                        team_id: Long,
                        org_id: OrgId,
                        assignee_id: Option[Long],
                        campaign_id: Option[Long],
                        reply_sentiment_uuid_opt: Option[ReplySentimentUuid],
                        task_priority: List[TaskPriority],
                        doNotFetchAutomatedDueTasks: Boolean,
                        permittedAccountIds: Seq[Long],
                        dateRange: Option[DateRangeBetween],
                        //                        emailNotCompulsoryEnabled: Boolean
                      )(implicit ec: ExecutionContext): Future[List[Task]] = Future {

    val taskPrioritySqls = if(task_priority.isEmpty) {
      sqls""""""
    } else{
        sqls"""AND t.priority IN (${task_priority.map(_.toKey)})"""
    }

//    val taskStatusType = getTaskStatusSQLSforCount(task_status = task_status)

    val assigneeIdSqls = assignee_id match {
      case None =>
        sqls""""""
      case Some(value) =>
        sqls"""AND t.assignee_id = $value"""
    }


    val campaignIdSqls = campaign_id match {
      case None =>
        sqls""

      case Some(cid) =>
        sqls"""AND t.campaign_id = $cid"""
    }

    val replySentimentUuidSqls = reply_sentiment_uuid_opt match {

      case None =>
        sqls""

      case Some(reply_sentiment_uuid) =>
        sqls"""
              AND t.reply_sentiment_uuid = ${reply_sentiment_uuid.uuid}
            """
    }
    
    val timelineCheck = if (dateRange.isDefined) {

      sqls"""
                  AND CASE WHEN t.status = ${TaskStatusType.Done.toKey} THEN t.done_at 
                    BETWEEN ${dateRange.get.startDate} AND ${dateRange.get.endDate}
                  ELSE
                    t.created_at IS NOT NULL
                  END
                  """
    }
    else {
      sqls""
    }
    

    val hideAutomatedTasksQuery = if (doNotFetchAutomatedDueTasks) {
      sqls"""
            AND CASE WHEN t.status = ${TaskStatusType.Due.toKey} THEN t.task_type NOT IN (
              ${TaskType.autoTaskTypes.map(_.toKey)}
            ) ELSE 
              -- Redundant but had to add because it was not working if else statement wasn't there.
              t.task_id != '' 
            END
            """
    }
    else {
      sqls""
    }


    val includeCallActionTaskCheck = sqls"""

                                    AND CASE WHEN t.status = ${TaskStatusType.Done.toKey} THEN
                                          t.task_id != ''
                                       ELSE
                                        t.created_via != ${TaskCreatedVia.Call_action.toKey}
                                      END

                                    """

        DB readOnly { implicit session =>

          sql"""
               SELECT t.*,
                pe.email AS prospect_email,
                CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
                CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
                ${getRecordingUrlForTaskSQL},
                p.linkedin_url as prospect_linkedin_url,
                p.phone as prospect_phone_number,
                p.company as prospect_company,
                p.job_title as prospect_designation,
                p.timezone as prospect_timezone,
                es.id as email_message_id
               FROM tasks t
               INNER JOIN prospects p ON (p.id = t.prospect_id AND p.team_id = t.team_id)
               LEFT JOIN emails_scheduled es on (es.team_id = t.team_id AND es.prospect_id = t.prospect_id AND es.campaign_id = t.campaign_id AND t.step_id = es.step_id )
               LEFT JOIN prospects_emails pe ON (pe.prospect_id = p.id AND pe.team_id = p.team_id AND pe.is_primary)
               INNER JOIN accounts a ON a.id = t.assignee_id
               WHERE t.assignee_id IN (${permittedAccountIds})
               AND t.team_id = ${team_id}
               $includeCallActionTaskCheck
               $hideAutomatedTasksQuery
               $taskPrioritySqls
               $assigneeIdSqls
               ${timelineCheck}
               $replySentimentUuidSqls
               $campaignIdSqls
             """
            .map(rs => taskFromDB(rs))
            .list
            .apply()
        }
    }

  def getSentOrScheduledProspectsCount(
    channelType: ChannelType,
    ownerAccountId: Long,
    account_timezone: String
  ): Try[Map[CampaignStepType, Int]] = Try {

    // start of day in account timezone
    val startOfDay = TimezoneUtils.getStartOfDayWithTimezone(timezone = account_timezone)

    DB readOnly { implicit session =>
      sql"""

  select

  task_type,
  count(*) as count

  from tasks
  where assignee_id = $ownerAccountId

  AND channel_type = ${channelType.toString}

  AND created_via = ${TaskCreatedVia.Scheduler.toKey}

  AND (

    due_at >= $startOfDay

    OR

    done_at >= $startOfDay

  )

  group by task_type
  """
        .map(rs => {
          val count = rs.int("count")
          val taskType = rs.string("task_type")

          // campaignStepType string is same as taskType in our sealed traits (CampaignStepType vs TaskType)

          val campaignStepType: CampaignStepType = CampaignStepType
            .fromKey(str = taskType)
            .get

          (campaignStepType, count)
        })
        .list
        .apply()
        .toMap
    }

  }

  def getScheduledProspectsCountForChannelType(
    channelType: ChannelType,
    ownerAccountId: Long,
    campaignIdAndTimezone: Seq[(Long, String)],
    teamId: Long,
    stepTypes: Seq[CampaignStepType],
    Logger: SRLogger
  ): Try[Seq[ScheduledProspectsCountForCampaign]] = if (campaignIdAndTimezone.isEmpty) {

    Logger.fatal(s"getScheduledProspectsCountForChannelType campaignIdAndTimezone.isEmpty: ownerAccountId_$ownerAccountId")

    Success(Seq())

  } else blocking {

    Try {
      campaignIdAndTimezone
        .grouped(5)
        .flatMap(cgroup => {
          /*
          srDBQueryCounterService.logDBQueryCallFrequency(
            dbQueryName = "getScheduledProspectsCountForChannelType",
            logger = Logger
          )
          */

          val campaignIdAndTzWhereClause = cgroup
            .map { case (cid, campaignTimezone) =>

              // start of day in campaign timezone
              val startOfDay = TimezoneUtils.getStartOfDayWithTimezone(timezone = campaignTimezone)

              sqls""" (
                  campaign_id = $cid

                  AND (

                    due_at >= $startOfDay

                    OR

                    done_at >= $startOfDay

                  )
                )
            """

            }
            .reduce((c1, c2) => sqls" $c1 OR $c2 ")

          Helpers.logTimeTaken(
            Logger = Logger.appendLogRequestId(s"getScheduledProspectsCountForChannelTypeSQL ownerAccountId_$ownerAccountId"),
            thresholdInMillis = 250
          ) {
            DB readOnly { implicit session =>
              sql"""
          select
          campaign_id,

          task_type,

          count(*) FILTER (WHERE is_opening_step AND (status = 'done')) as newCount,
          count(*) FILTER (WHERE NOT is_opening_step AND (status = 'done')) AS followupCount,
          count(*) FILTER (WHERE is_opening_step AND not (status = 'done')) as newCountNotSent,
          count(*) FILTER (WHERE NOT is_opening_step AND not (status = 'done')) as followupCountNotSent

          from tasks
          -- inner join campaigns c on c.id = campaign_id

          where assignee_id = $ownerAccountId

          AND channel_type = ${channelType.toString}
          AND team_id = $teamId

          AND (
            $campaignIdAndTzWhereClause
          )

          AND team_id = $teamId

          AND task_type in (${stepTypes.map(_.toKey)})

          GROUP BY campaign_id, task_type
          ;
          """
                .map(rs => {

                  val taskType = rs.string("task_type")

                  // taskType string is same as campaignStepType string as defined in their sealed traits

                  val campaignStepType = CampaignStepType.fromKey(str = taskType).get

                  ScheduledProspectsCountForCampaign(
                    campaignId = rs.long("campaign_id"),
                    campaignStepType = campaignStepType,
                    newCount = rs.int("newCount"),
                    followupCount = rs.int("followupCount"),
                    newCountNotSent = rs.int("newCountNotSent"),
                    followupCountNotSent = rs.int("followupCountNotSent")
                  )
                })
                .list
                .apply()
            }
          }
        })
        .toSeq
    }
  }

  def getTaskIdsForUserV2(
                         query: SQL[Nothing, NoExtractor]
                       )(implicit ec: ExecutionContext): Future[List[String]] = Future {

    DB readOnly { implicit session =>

      query
        .map(rs => rs.string("task_id"))
        .list
        .apply()
    }

  }

  def moveTasksToDue(
                      taskIds: Seq[String],
                      teamId: TeamId
                    )(implicit ec: ExecutionContext): Future[Int] = Future {

    DB autoCommit {implicit session =>
      sql"""
           UPDATE tasks
           SET
              status = ${TaskStatusType.Due.toKey}
           WHERE task_id IN ($taskIds)
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }

  }

  def getTaskIdsForUser(
                       team_id: Long,
                       assignee_ids: Option[List[Long]],
                       task_type: Option[List[String]],
                       task_status: Option[List[String]],
                       task_priority: Option[List[String]],
                       permittedAccountIds: Seq[Long]
                       )(implicit ec: ExecutionContext):Future[List[String]] = Future {
    val taskPriority = getTaskPrioritySQLS(task_priority = task_priority)

    val taskStatusType = getTaskStatusSQLS(task_status = task_status)

    val taskType = getTaskTypeSQLS(task_type = task_type)

    val assigneeIds = getAssigneeSQLS(assignee_ids = assignee_ids)

    DB readOnly { implicit session =>

      sql"""
           SELECT t.task_id
           FROM tasks t
           WHERE t.assignee_id IN (${permittedAccountIds})
           AND team_id = ${team_id}
           $taskStatusType
           $taskPriority
           $taskType
           $assigneeIds
         """
        .map(rs => rs.string("task_id"))
        .list
        .apply()
    }


  }

  def findTaskbyTaskID(
                        taskId: String,
                        teamId: Long,
                        orgId: OrgId,
                        changeStatusPermissionCheck: ChangeStatusPermissionCheck
//                        emailNotCompulsoryEnabled : Boolean
                      )(implicit ec: ExecutionContext
        ): Future[Option[Task]] = Future {

    DB readOnly  { implicit session =>
      val assigneeCheck = assigneeIdCheck(
        changeStatusPermissionCheck = changeStatusPermissionCheck
      )
      
      TaskPgDAO.__findTaskSQL(
        whereClause =
      sqls"""
           t.team_id = $teamId AND t.task_id = $taskId ${assigneeCheck}
         """,
        orgId = Some(orgId),
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
      )
        .map(rs => taskFromDB(rs))
        .single
        .apply()

    }

  }

  def findTaskById(
                        taskId: String,
                        teamId: Long,
                        orgId : OrgId,
                        permittedAccountIds: Seq[Long],
//                        emailNotCompulsoryEnabled: Boolean
                      )(implicit ec: ExecutionContext
                      ): Try[Option[Task]] = Try {

    if(permittedAccountIds.isEmpty) {
      None
    } else {

      DB readOnly { implicit session =>
        TaskPgDAO.__findTaskSQL(
            whereClause =
              sqls"""
             t.assignee_id IN (${permittedAccountIds}) AND t.team_id = $teamId AND t.task_id = $taskId
           """,
          orgId= Some(orgId),
//          emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
          )
          .map(rs => taskFromDB(rs))
          .single
          .apply()

      }

    }

  }

  def findDueTaskbyTaskIDAndTeamId(
                                    taskId: String,
                                    teamId: TeamId,
                                    orgId: OrgId,
//                                    emailNotCompulsoryEnabled: Boolean
                                  )(implicit ec: ExecutionContext): Future[Option[Task]] = Future {

    DB readOnly { implicit session =>
      TaskPgDAO.__findTaskSQL(
        whereClause =
          sqls"""
           t.team_id = ${teamId.id} AND t.task_id = $taskId AND t.status = ${TaskStatusType.PushedToLinkedinExecutionQueue.toKey}
         """,
        orgId = Some(orgId),
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
      )
        .map(rs => taskFromDB(rs))
        .single
        .apply()

    }

  }

  def updateStatusOfAutoLinkedinTasks(taskIds: List[String], teamId: TeamId)(implicit ec: ExecutionContext): Future[Int] = Future {

    DB autoCommit {implicit session =>
      sql"""
           UPDATE tasks
           SET
             status = ${TaskStatusType.PushedToLinkedinExecutionQueue.toKey},
             pushed_to_phantombuster_queue_at = now()
           WHERE
             task_id IN ($taskIds)
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }

  }

  def fetchDueAutoLinkedinTasks(
                                 linkedinSettingUuid: LinkedinSettingUuid,
                                 teamId: TeamId
                               )(implicit ec: ExecutionContext): Future[List[TaskIdAndType]] = Future {

    val stuckLimit = SQLSyntax.createUnsafely(AppConfig.Linkedin_Automation.stuckTaskTimeLimitInMinutes.toString)

    DB readOnly {implicit session =>
      sql"""
           SELECT t.task_id, t.task_type, t.campaign_id
           FROM tasks t
           INNER JOIN campaign_channel_settings ccs ON t.campaign_id = ccs.campaign_id AND t.team_id = ccs.team_id AND ccs.channel_type = ${ChannelType.LinkedinChannel.toString}
           INNER JOIN campaigns c ON c.id = t.campaign_id AND c.team_id = t.team_id
           WHERE t.status = ${TaskStatusType.Due.toKey}
           AND (
              t.task_type = ${TaskType.AutoLinkedinConnectionRequest.toKey}
              OR
              t.task_type = ${TaskType.AutoLinkedinInmail.toKey}
              OR
              t.task_type = ${TaskType.AutoViewLinkedinProfile.toKey}
              OR
              t.task_type = ${TaskType.AutoLinkedinMessage.toKey}
           )
           AND t.execution_attempts < 2
           AND t.cd_job_uid IS NULL
           AND c.status = ${CampaignStatus.RUNNING.toString}
           AND ccs.channel_settings_uuid = ${linkedinSettingUuid.uuid}
           AND t.team_id = ${teamId.id}
           ORDER BY t.due_at
           ;
           """
        .map(rs => TaskIdAndType(
          task_id = rs.string("task_id"),
          campaign_id = CampaignId(id = rs.long("campaign_id")),
          task_type = TaskType.fromString(rs.string("task_type")).get
        ))
        .list
        .apply()
    }

  }

  /*
    - Count completed LinkedIn tasks for today by task type for a specific LinkedIn setting.
    - This is used to check daily limits before executing new tasks.
   */
  def countTodayCompletedLinkedinTasksByType(
                                              linkedinSettingUuid: LinkedinSettingUuid,
                                              teamId: TeamId
                                            )(implicit ec: ExecutionContext): Future[Map[TaskType, Int]] = Future {

    DB readOnly { implicit session =>
      sql"""
           SELECT t.task_type, COUNT(*) as task_count
           FROM tasks t
           INNER JOIN campaign_channel_settings ccs ON t.campaign_id = ccs.campaign_id AND t.team_id = ccs.team_id AND ccs.channel_type = ${ChannelType.LinkedinChannel.toString}
           WHERE t.status = ${TaskStatusType.Done.toKey}
           AND (
              t.task_type = ${TaskType.AutoLinkedinConnectionRequest.toKey}
              OR
              t.task_type = ${TaskType.AutoLinkedinInmail.toKey}
              OR
              t.task_type = ${TaskType.AutoViewLinkedinProfile.toKey}
              OR
              t.task_type = ${TaskType.AutoLinkedinMessage.toKey}
           )
           AND ccs.channel_settings_uuid = ${linkedinSettingUuid.uuid}
           AND t.team_id = ${teamId.id}
           AND t.done_at >= CURRENT_DATE
           AND t.done_at < CURRENT_DATE + INTERVAL '1 day'
           GROUP BY t.task_type
           ;
           """
        .map(rs => {
          val taskType = TaskType.fromString(rs.string("task_type")).get
          val count = rs.int("task_count")
          taskType -> count
        })
        .list
        .apply()
        .toMap
    }
  }

  def fetchTaskDataById(
                         taskId: TaskUuid,
                         teamId: TeamId)
                       (using logger: SRLogger): Try[Option[TaskData]] = Try {
    DB readOnly {implicit session =>
      sql"""
           SELECT
              t.task_type,
              t.task_subject,
              t.task_body,
              t.email_scheduled_text_body,
              t.email_scheduled_base_body,
              ${getRecordingUrlForTaskSQL}
           FROM tasks t
           WHERE
              task_id = ${taskId.uuid}
           AND team_id = ${teamId.id}
           ;
           """
        .map(taskDataFromDB)
        .single
        .apply()
    }
  }

  def hasAnyActiveCampaignTasksInCaptainData(
                          campaignId: CampaignId,
                          teamId: TeamId
                                    )(implicit ec: ExecutionContext): Try[Boolean] = Try{

    DB readOnly { implicit session =>
      sql"""
           SELECT EXISTS (
              SELECT 1 FROM tasks t
              WHERE t.campaign_id = ${campaignId.id}
              AND t.team_id = ${teamId.id}
              AND t.status = ${TaskStatusType.PushedToLinkedinExecutionQueue.toString}
              );
           """
        .map(rs => rs.boolean("exists"))
        .single
        .apply()
        .getOrElse(false)
    }

  }

  def getLinkedinSettingUuidFromPhantomBusterContainerId(
                                                          containerId: PhantomBusterContainerId,
                                                          teamId: TeamId
                                                        )(implicit ec: ExecutionContext): Future[Option[LinkedinSettingUuid]] = Future{

    DB readOnly { implicit session =>
      sql"""
           SELECT ccs.channel_settings_uuid
           FROM tasks t
           INNER JOIN campaign_channel_settings ccs ON ccs.campaign_id = t.campaign_id AND ccs.team_id = t.team_id AND ccs.channel_type = ${ChannelType.LinkedinChannel.toString}
           WHERE t.phantombuster_container_id = ${containerId.id}
           AND t.team_id = ${teamId.id}
           LIMIT 1
           ;
           """
        .map(rs => {
          LinkedinSettingUuid(rs.string("channel_settings_uuid"))
        })
        .single
        .apply()
    }

  }

  def fetchTaskData(taskId: String, teamId: TeamId)(implicit ec: ExecutionContext): Future[Option[TaskData]] = Future {
    DB readOnly {implicit session =>
      sql"""
           SELECT
              t.task_type,
              t.task_subject,
              t.task_body,
              t.email_scheduled_text_body,
              t.email_scheduled_base_body,
              ${getRecordingUrlForTaskSQL}
           FROM tasks t
           WHERE
              task_id = $taskId
           AND team_id = ${teamId.id}
           ;
           """
        .map(taskDataFromDB)
        .single
        .apply()
    }
  }

  def getLinkedinProfileUrlOfProspectByTaskId(
                                               taskId: String,
                                               teamId: TeamId
                                             )(implicit ec: ExecutionContext): Future[Option[String]] = Future {
    DB readOnly {implicit session =>
      sql"""
           SELECT linkedin_url FROM tasks t
           INNER JOIN prospects p ON p.id = t.prospect_id AND t.team_id = p.team_id
           WHERE t.task_id = $taskId
           AND t.team_id = ${teamId.id}
           ;
           """
        .map(rs => {
          rs.string("linkedin_url")
        })
        .single
        .apply()
    }
  }

  def failTask(
                taskId: String,
                teamId: TeamId,
                failureReason: String
              )(implicit ec: ExecutionContext): Future[Int] = Future {

    DB autoCommit {implicit session =>
      sql"""
           UPDATE tasks
           SET
              status = ${TaskStatusType.Failed.toKey},
              failure_reason = $failureReason,
              failed_at = now()
           WHERE
              task_id = $taskId
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }

  }


  def updateContainerIdForTasks(
                                taskIds: Seq[String],
                                containerId: PhantomBusterContainerId,
                                teamId: TeamId
                              )(implicit ec: ExecutionContext): Future[Int] = Future {

    DB autoCommit {implicit session =>
      sql"""
           UPDATE tasks
           SET
              phantombuster_container_id = ${containerId.id},
              status = ${TaskStatusType.PushedToLinkedinExecutionQueue.toKey},
              execution_attempts = execution_attempts + 1
           WHERE task_id IN ($taskIds)
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }

  }

  def updateCaptainDataTaskStatusToInitiate(
                                 taskId: String,
                                 jobUid: CaptainDataJobUID,
                                 teamId: TeamId
                               )(implicit ec: ExecutionContext): Future[Int] = Future {

    DB autoCommit { implicit session =>
      sql"""
             UPDATE tasks
             SET
                cd_job_uid = ${jobUid.toString},
                cd_job_status = ${CaptainDataWorkflowStatus.INITIATED.toString},
                execution_attempts = execution_attempts + 1
             WHERE task_id = ${taskId}
             AND team_id = ${teamId.id}
             ;
             """
        .update
        .apply()
    }

  }

  def fetchLinkedinTasksInPhantomBusterQueue()(implicit ec: ExecutionContext): Future[List[RunningLinkedinTaskDetails]] = Future {
    DB readOnly {implicit session =>
      sql"""
           SELECT
              ta.task_id,
              ta.phantombuster_container_id,
              ta.team_id,
              te.phantombuster_api_key_enc
           FROM tasks ta
           INNER JOIN teams te ON te.id = ta.team_id
           WHERE status = ${TaskStatusType.PushedToLinkedinExecutionQueue.toKey}
           ;
           """
        .map(rs => {
          RunningLinkedinTaskDetails(
            apiKey = PhantomBusterApiKey(api_key = EncryptionHelpers.decryptPhantomBusterApiKey(rs.string("phantombuster_api_key_enc"))),
            container_id = PhantomBusterContainerId(id = rs.string("phantombuster_container_id")),
            team_id = TeamId(rs.long("team_id")),
            task_id = rs.string("task_id")
          )
        })
        .list
        .apply()
    }
  }

  def updateStatusOfAllRunningLinkedinTasks(
                                             containerId: PhantomBusterContainerId,
                                             teamId: TeamId,
                                             status: TaskStatus
                                           )(implicit ec: ExecutionContext): Future[List[AccountId]] = Future {

    val updateStatus = getUpdateStatusSQLS(status = status)

    DB autoCommit { implicit session =>
      sql"""
           UPDATE tasks
           SET $updateStatus
           WHERE team_id = ${teamId.id}
           AND phantombuster_container_id = ${containerId.id}
           RETURNING assignee_id
           ;
           """
        .map(rs => AccountId(rs.long("assignee_id")))
        .list
        .apply()
    }

  }
  /*
    15-april-2024 :
    running linkedin task status update, by linkedin url

   */

  
  def updateRunningLinkedinTaskStatusByProspectLinkedinUrl(
                                                            containerId: PhantomBusterContainerId,
                                                            teamId: TeamId,
                                                            linkedinProfileUrl: String,
                                                            status: TaskStatus
                                                          )(implicit ec: ExecutionContext,
                                                            logger: SRLogger): Future[Option[TaskDetailsForPhantomBusterWebhook]] = Future {

    val updateStatus = getUpdateStatusSQLS(status = status)

    DB autoCommit { implicit session =>
      sql"""
           UPDATE tasks t1
           SET
              $updateStatus
           FROM (
              SELECT t.task_id, t.team_id
              FROM tasks t
              INNER JOIN prospects p ON t.prospect_id = p.id AND p.team_id = t.team_id
              WHERE t.phantombuster_container_id = ${containerId.id}
              AND t.team_id = ${teamId.id}
              AND p.linkedin_url = ${LinkedinHelperFunctions.normalizeLinkedInURL(Some(linkedinProfileUrl)).get}
           ) filtered_task
           WHERE filtered_task.task_id = t1.task_id AND filtered_task.team_id = t1.team_id
           RETURNING *
           ;
           """
        .map(rs => {
          TaskDetailsForPhantomBusterWebhook(
            task_id = rs.string("task_id"),
            assignee_id = rs.longOpt("assignee_id").map(AccountId(_)),
            prospect_id = rs.longOpt("prospect_id").map(ProspectId(_)),
            campaign_id = rs.longOpt("campaign_id").map(CampaignId(_))
          )
        })
        .single
        .apply()
    }

  }

  def failLinkedinTasksWhichAreStuck()(implicit ec: ExecutionContext): Future[List[String]] = Future{

    val stuckTimeLimit = SQLSyntax.createUnsafely(AppConfig.stuckAutoTaskTimeLimitInMinutes.toString)

    DB autoCommit {implicit session =>
      sql"""
           UPDATE tasks
           SET
              status = ${TaskStatusType.Failed.toKey},
              failure_reason = 'Did not receive result after performing multiple times.',
              failed_at = now()
           WHERE
              execution_attempts = 2
           AND status = ${TaskStatusType.PushedToLinkedinExecutionQueue.toKey}
           AND pushed_to_phantombuster_queue_at < now() - interval '$stuckTimeLimit minutes'
           AND channel_type = ${ChannelType.LinkedinChannel.toString}
           RETURNING task_id
           ;
           """
        .map(rs => rs.string("task_id"))
        .list
        .apply()
    }

  }
  
  def updateTaskStatus(
                        status: TaskStatus,
                        taskId: String,
                        teamId: TeamId
                      ): Try[Int] = Try {
    
    val updateStatement = getUpdateStatusSQLS(status = status)
    
    DB autoCommit {implicit session =>
      sql"""
           UPDATE tasks
           SET 
              $updateStatement
           WHERE
              task_id = $taskId
           AND team_id = ${teamId.id}
           """
        .update
        .apply()
    }
  }

  def fetchTasksToBeMarkedDueOnLinkedinSessionCookieUpdate(
                                           linkedinSettingUuid: String,
                                           teamId: TeamId
                                         ): Try[List[TaskIdAndTeamId]] = Try {

    DB readOnly {implicit session =>
      sql"""
            SELECT t.task_id, t.team_id
            FROM tasks t
            INNER JOIN campaign_channel_settings ccs
              ON ccs.campaign_id = t.campaign_id
              AND ccs.team_id = t.team_id
              AND ccs.channel_type = ${ChannelType.LinkedinChannel.toString}
            WHERE
              ccs.channel_settings_uuid = $linkedinSettingUuid
            AND t.failure_reason IN (
              ${ExecuteAutoLinkedinTasksError.LinkedinAccountNotSynced.toString},
              ${ExecuteAutoLinkedinTasksError.InvalidOrExpiredLinkedinSessionCookie.toString}
            )
            AND t.team_id = ${teamId.id}
            AND t.status = ${TaskStatusType.Failed.toKey}
           ;
           """
        .map(rs => {
          TaskIdAndTeamId(
            taskId = rs.string("task_id"), 
            teamId = TeamId(rs.long("team_id"))
          )
        })
        .list
        .apply()
    }


  }
  
  def fetchTasksToBeMarkedAsDueOnProspectLinkedinUrlUpdate(
                                                            prospectId: ProspectId,
                                                            teamId: TeamId
                                                          ): Try[List[TaskIdAndTeamId]] = Try {
    
    DB readOnly {implicit session =>
      sql"""
           SELECT task_id, team_id
           FROM tasks
           WHERE prospect_id = ${prospectId.id}
           AND team_id = ${teamId.id}
           AND failure_reason IN (
              ${ExecuteAutoLinkedinTasksError.ProspectDoesNotHaveLinkedinUrl.toString},
              ${ExecuteAutoLinkedinTasksError.ProspectHasInvalidLinkedinUrl.toString}
           )
           AND status = ${TaskStatusType.Failed.toKey}
           ;
           """
        .map(rs => {
          TaskIdAndTeamId(
            taskId = rs.string("task_id"), 
            teamId = TeamId(rs.long("team_id"))
          )
        })
        .list
        .apply()
    }
    
  }

  def fetchFailedTaskOfCampaign(
                                 campaignId: CampaignId,
                                 teamId: TeamId,
                                 orgId: OrgId,
//                                 emailNotCompulsoryEnabled: Boolean
                               ): Try[List[Task]] = Try {

    DB readOnly {implicit session =>

      TaskPgDAO.__findTaskSQL(
        whereClause =
          sqls"""
                  t.campaign_id = ${campaignId.id} AND
                  t.team_id = ${teamId.id} AND
                  t.status = ${TaskStatusType.Failed.toKey}
                """,
        orgId = Some(orgId),
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
      )
        .map(rs => taskFromDB(rs))
        .list
        .apply()

    }

  }

  def checkBatchTaskByTaskIdAndTeamIds(
                                    taskIds: List[String],
                                    orgId: OrgId,
                                    teamId: Long,
                                    permittedAccountIds: Seq[Long],
//                                    emailNotCompulsoryEnabled: Boolean
                                    )( implicit ec: ExecutionContext): Future[List[String]] = Future {
    if(taskIds.isEmpty || permittedAccountIds.isEmpty) {
      List()
    } else {
//      val prospectEmailSql = if (emailNotCompulsoryEnabled) {
//        sqls""" LEFT JOIN prospects_emails pe ON (pe.prospect_id = p.id AND pe.team_id = p.team_id AND pe.is_primary) """
//      } else {
//        sqls""" INNER JOIN prospects_emails pe ON (pe.prospect_id = p.id AND pe.team_id = p.team_id AND pe.is_primary) """
//      }

      DB readOnly  { implicit session =>
      sql"""
           SELECT task_id
           FROM tasks t
           INNER JOIN prospects p ON (p.id = t.prospect_id AND p.team_id = t.team_id)
           LEFT JOIN prospects_emails pe ON (pe.prospect_id = p.id AND pe.team_id = p.team_id AND pe.is_primary)
           INNER JOIN accounts a ON a.id = t.assignee_id
           WHERE t.assignee_id IN (${permittedAccountIds}) AND t.team_id = $teamId AND t.task_id IN ($taskIds)
         """
        .map(rs => rs.string("task_id"))
        .list
        .apply()
    }
    }
  }

  def deleteNotCompletedTaskForDeletedCampaign( campaignId:CampaignId ,teamId: TeamId)(implicit session: DBSession):Try[List[String]] = Try {

      sql"""
        DELETE FROM tasks t
        WHERE t.campaign_id = ${campaignId.id}
        AND t.team_id = ${teamId.id}
        AND t.status != ${TaskStatusType.Done.toKey}
        RETURNING t.task_id;
      """
        .map(rs => rs.string("task_id"))
        .list
        .apply()

  }

// TODO: Add Unit Tests For This
  def updateTask(
                  update_task_data: UpdateTask,
                  task_id: String,
                  team_id: Long,
                  permittedAccountIds: Seq[Long]
                )(implicit ec: ExecutionContext): Future[Option[String]] = Future{

    val taskDatChangedValues = getUpdateTaskDataSQLS(task_data = update_task_data.task_data)
    val priorityChangedValues = getUpdatePrioritySQLS(priority = update_task_data.priority)
    // This is here to change due date only  -
    val dueDateChangedValues = getChangedDueDateSQLS(status = update_task_data.status)
    val currentTime = DateTime.now()

    DB autoCommit { implicit session =>

      sql"""
         UPDATE tasks
         SET
             assignee_id = ${update_task_data.assignee_id},
             prospect_id = ${update_task_data.prospect_id},
             updated_at = $currentTime,
             $taskDatChangedValues
             $priorityChangedValues
             $dueDateChangedValues
         WHERE assignee_id IN (${permittedAccountIds})
         AND task_id = ${task_id}
         AND team_id = ${team_id}

         RETURNING task_id
       """
        .map(rs => rs.string("task_id"))
        .single
        .apply()

    }
  }

  def findBatchTasksFromDB(
                          taskIds: List[String],
                          teamId: Long,
                          orgId: OrgId,
                          changeStatusPermissionCheck: ChangeStatusPermissionCheck
//                          emailNotCompulsoryEnabled : Boolean
                          )( implicit ec: ExecutionContext, logger: SRLogger): Future[List[Task]] = Future {

    if(teamId == 20848){

      logger.doNotTruncate(s"[task_issue] findBatchTasksFromDB :: taskIds: ${taskIds} teamId: ${teamId} changeStatusPermissionCheck: ${
        changeStatusPermissionCheck match {
          case ChangeStatusPermissionCheck.ManualTaskCheck(doer, permittedIds) =>
            s"ManualTaskCheck(doer=$doer, permittedIds=$permittedIds)"
          case ChangeStatusPermissionCheck.AutomatedTaskCheck(jobUid) =>
            s"AutomatedTaskCheck(jobUid=$jobUid)"
          case ChangeStatusPermissionCheck.NoStatusPermissionCheck(accountId) =>
            s"NoStatusPermissionCheck(accountId=$accountId)"
        }
      }")
    }
    
    val assigneeCheck = assigneeIdCheck(changeStatusPermissionCheck = changeStatusPermissionCheck)


    if(taskIds.isEmpty) {
      List()
    } else {

    DB readOnly { implicit session =>
      TaskPgDAO.__findTaskSQL(
        whereClause =
      sqls"""
          t.team_id = ${teamId} AND t.task_id IN ($taskIds)
          ${assigneeCheck}
         """,
        orgId = Some(orgId),
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
      )
        .map(rs => taskFromDB(rs))
        .list
        .apply()
    }}
  }

  def moveToArchive(retention_time: String,
                    except_status: List[String])(implicit ec: ExecutionContext):Future[Int] = Future{

    val current_time = DateTime.now()
    val query = SQLSyntax.createUnsafely(s"""now() - INTERVAL '$retention_time'""")
    DB.autoCommit {implicit session =>
      sql"""
         UPDATE tasks
         SET
            status = ${TaskStatusType.Archive.toKey},
            archive_at = $current_time
         WHERE
            status NOT IN ($except_status)
         AND
            due_at < $query

       """
        .update
        .apply()
    }

  }

  def callPartitionManager()(implicit ec: ExecutionContext) = Future{
    DB.autoCommit { implicit session =>
      sql"""
         CALL partman.run_maintenance_proc()
       """
        .execute
        .apply()

    }
  }

  def moveDataToTemporaryTableFromDefaultPartition(parent_table: PartitionedParentTableName)(implicit ec: ExecutionContext): Future[Boolean] = Future {
    DB.autoCommit { implicit session =>
      sql"""
         CALL partman.partition_data_proc(${parent_table.parent_table})
       """
        .execute
        .apply()

    }
  }

  def partitionGapFill(parent_table: PartitionedParentTableName)(implicit ec: ExecutionContext): Future[(PartitionedParentTableName, Int)] = Future {
    DB.autoCommit { implicit session =>
      sql"""
         SELECT partman.partition_gap_fill(${parent_table.parent_table});
       """
        .map(rs => parent_table -> rs.int("partition_gap_fill"))
        .single
        .apply()
        .getOrElse((parent_table, 0))
    }
  }


  def getPartitionedTableNames()(implicit ec: ExecutionContext): Future[List[PartitionedParentTableName]] = Future {

    DB.readOnly { implicit session =>

      sql"""
           SELECT parent_table FROM partman.part_config
         """
        .map(rs => PartitionedParentTableName(parent_table = rs.string("parent_table")))
        .list
        .apply()

    }
  }

  def checkIfOldPartitionsAreEmpty(retention_time: String,
                    except_status: List[String])(implicit ec: ExecutionContext):Future[Int] = Future{

    val query = SQLSyntax.createUnsafely(s"""now() - INTERVAL '$retention_time'""")
    DB.autoCommit {implicit session =>
      sql"""
         SELECT count(*)
         FROM tasks
         WHERE
            status NOT IN ($except_status)
         AND
            due_at < $query
       """
        .map(res => res.int("count"))
        .single
        .apply()
        .get
    }

  }

  def getSnoozedTasksBeforeTime(tillTime: DateTime,
                                orgId: Option[OrgId],
//                                emailNotCompulsoryEnabled : Boolean
                               )(implicit ec: ExecutionContext): Future[List[Task]] = Future{

    DB.readOnly {implicit session =>
      TaskPgDAO.__findTaskSQL(
        whereClause =
          sqls"""
              a.active = true
              AND t.status = ${TaskStatusType.Snoozed.toKey}
              AND t.snoozed_till < ${tillTime}
              AND t.snoozed_till > ${tillTime.minusHours(1)}
             """,
        orgId = orgId,
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
      )
        .map(rs => taskFromDB(rs))
        .list
        .apply()

    }
  }

  def unSnoozeSnoozedTasks(
                            taskId : String,
                            team_id: Long
                          )(implicit ec: ExecutionContext): Future[Option[String]] = Future{

    DB.autoCommit { implicit session =>
      sql"""
           UPDATE tasks
             SET status = ${TaskStatusType.Due.toKey},
                 snoozed_till = NULL
           WHERE
             status = ${TaskStatusType.Snoozed.toKey}  --   THIS WILL INCREASE QUERY PERFORMANCE BY USING PARTITIONED TABLE
             AND task_id = $taskId
             AND team_id = $team_id

           RETURNING task_id
         """
        .map(res => res.string("task_id"))
        .single
        .apply()
    }
  }

  def fetchTasksToBeScheduled(table: Settings_Table): Try[List[ChannelSettings]] = Try {

    DB autoCommit { implicit session =>

      getFetchTaskScheduledQuery(table = table)
        .map(rs => ChannelSettings(
          team_id = rs.long("team_id"),
          channelSettingId = rs.string("uuid")
        ))
        .list
        .apply()
    }

  }

  def getCompletedTaskIdsForProspectInCampaign(
                                              campaign_id: Long,
                                              prospect_id: Long,
                                              team_id: Long
                                              )(implicit  ec: ExecutionContext): Future[List[String]] = Future {
    DB.readOnly { implicit session =>
      sql"""
           SELECT task_id
           FROM tasks
           WHERE prospect_id = $prospect_id
            AND campaign_id = $campaign_id
            AND team_id = $team_id
            AND status = ${TaskStatusType.Done.toKey}
         """
        .map(
          rs => rs.string("task_id")
        )
        .list
        .apply()

    }
  }
  /**
    * Feb - 24, 2023
    * Todo: We can get task due_at and status type to do these insertions/updates which will improve the query performance
    * as we have a partitioned table based on due_at and status_type
    *
    * @param taskUuid
    * @param teamId
    * @param replySentimentUuid
    * @param session
    * @return
    */
  def saveSentimentsForTask(
                           taskUuid: TaskUuid,
                           teamId: TeamId,
                           replySentimentUuid: ReplySentimentUuid
                           )(
//    implicit ec : ExecutionContext,
                             implicit session: DBSession, logger: SRLogger): Try[TaskUuid] = {
    retryOnSpecificError[TaskUuid](maxRetries = 3, logger = logger) {
      Try{

        sql"""
         UPDATE tasks
            SET reply_sentiment_uuid = ${replySentimentUuid.uuid},
            updated_at = now()
         WHERE
            task_id = ${taskUuid.uuid}
            AND team_id = ${teamId.id}


         RETURNING task_id;
       """
          .map(rs => TaskUuid(
            uuid = rs.string("task_id"))
          )
          .single
          .apply()
          .getOrElse(throw new Exception("Task not found or update failed"))
      }
    }
  }


  def groupDuplicateTasks()(implicit session: DBSession): Try[List[GroupDuplicateTaskDetails]] = Try{
    sql"""
         SELECT
            step_id,
            campaign_id,
            prospect_id,
            team_id,
            COUNT(*)
         FROM tasks
         WHERE step_id IS NOT NULL AND campaign_id IS NOT NULL AND prospect_id IS NOT NULL
         GROUP BY (step_id, campaign_id, prospect_id, team_id)
         HAVING COUNT(*) > 1;
       """
      .map(rs => GroupDuplicateTaskDetails(
        step_id = StepId(id = rs.long("step_id")),
        campaignId = CampaignId(id = rs.long("campaign_id")),
        prospectId = ProspectId(id = rs.long("prospect_id")),
        count = rs.long("count"),
        team_id = TeamId(rs.long("team_id"))
      ))
      .list
      .apply()
  }


  /*
  Email Not Compulsory Notes:
   - Used in TestApp so directly changing to Left Join
   */
  def fetchAllTasksRealtedToGroupForTestAppOnly(
                                 step_id: StepId,
                                 campaignId: CampaignId,
                                 prospectId: ProspectId,
                                 team_id: TeamId
                                 )(implicit session : DBSession): Try[List[Task]] = Try{

    sql"""
         SELECT t.*,
          pe.email AS prospect_email,
          CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          p.linkedin_url as prospect_linkedin_url,
          p.phone as prospect_phone_number,
          p.company as prospect_company,
          es.id as email_message_id
         FROM tasks t
         INNER JOIN prospects p ON (p.id = t.prospect_id AND p.team_id = t.team_id)
         LEFT JOIN prospects_emails pe ON (pe.prospect_id = p.id AND pe.team_id = p.team_id AND pe.is_primary)
         LEFT JOIN emails_scheduled es on (es.team_id = t.team_id AND es.prospect_id = t.prospect_id AND es.campaign_id = t.campaign_id AND t.step_id = es.step_id)
         INNER JOIN accounts a ON a.id = t.assignee_id
         WHERE
            t.step_id = ${step_id.id}
            AND t.campaign_id = ${campaignId.id}
            AND t.prospect_id = ${prospectId.id}
            AND t.team_id = ${team_id.id}
         """
      .map(rs => taskFromDB(rs))
      .list
      .apply()

  }
  
  def fetchTaskDetailsViaTaskIdAndJobUid(
                                        task_uuid: TaskUuid,
                                        job_uid: CDJobUid
                                        ) : Try[TaskDetailsToProcessWebhookUpdate]= Try{

    DB readOnly { implicit session =>
      sql"""
        SELECT 
          t.task_id,
          tm.org_id AS org_id,
          t.team_id
        FROM 
          tasks t
        JOIN 
          teams tm ON t.team_id = tm.id
        WHERE 
          t.task_id = ${task_uuid.uuid}
          AND t.cd_job_uid = ${job_uid.uid}
            
       """
        .map(rs => taskDetailsToProcessWebhookUpdateFromDb(rs = rs))
        .single
        .apply()
        .get
      
    }
    
  }


  def updateCaptainDataWebhookStatus(
                                      data: CaptainDataWebhookResponse,
                                      status: CaptainDataWorkflowStatus): Try[TaskUuid] = Try{

    val currentTime = DateTime.now()

    DB autoCommit { implicit session =>

      val update_statement = TaskPgDAO.getUpdateWebhookStatusSQLS(
        data = data,
        status = status
      )
      sql"""
               UPDATE tasks
               SET
                  ${update_statement},
                  updated_at = ${currentTime}
               WHERE
                  cd_job_uid = ${data.job_uid.uid}

               RETURNING task_id
             """
        .map(rs => TaskUuid(uuid = rs.string("task_id")))
        .single
        .apply()
        .get

    }


  }


  def checkIfTaskAlreadyExists(
                              step_id: StepId,
                              prospect_id: ProspectId,
                              team_id: TeamId,
                              is_magic_step: Boolean
                          ): Try[Option[TaskUuid]] = Try {

    DB readOnly { implicit session =>

      sql"""
           SELECT
              task_id
           FROM
              tasks
           WHERE
              step_id = ${step_id.id}
              AND prospect_id = ${prospect_id.id}
              AND team_id = ${team_id.id}
              AND (
               ${!is_magic_step}
               
              OR (
              status != ${TaskStatusType.Approved.toKey}
              and $is_magic_step
              )
              )

           Limit 1
         """
        .map(rs => TaskUuid(uuid = rs.string("task_id")))
        .single
        .apply()

    }
  }

  def getTaskDataForProspectRevert(
                                  campaign_id: CampaignId,
                                  prospect_id: ProspectId,
                                  step_id: StepId
                                  )(implicit session : DBSession): Try[Option[RevertData.TaskRevertData]] = Try{


    sql"""
         SELECT
            due_at,
            task_id,
            status,
            skipped_at,
            done_at
         FROM tasks
         where campaign_id = ${campaign_id.id}
         AND prospect_id = ${prospect_id.id}
         AND step_id = ${step_id.id}

       """
      .map(rs => {

        RevertData.TaskRevertData(
          task_due_at = rs.jodaDateTime("due_at"),
          task_id = rs.string("task_id"),
          status_type = TaskStatusType.fromString(rs.string("status")).get,
          skipped_at = rs.jodaDateTimeOpt("skipped_at"),
          done_at = rs.jodaDateTimeOpt("done_at")
        )

      })
      .single
      .apply()

  }

  /**
   * Finds a SendEmail task for a specific prospect, campaign, and step, returning its subject and body.
   * Used for fetching pre-approved magic content.
   */
  def findMagicContentTaskByDetails(
                                     campaignId: CampaignId,
                                     stepId: StepId,
                                     prospectId: ProspectId,
                                     teamId: TeamId
                                   ): Try[Option[TaskData]] = Try {
    DB.readOnly { implicit session =>
      sql"""
        SELECT
          task_subject,
          task_body,
          email_scheduled_text_body,
          email_scheduled_base_body,
          task_type
        FROM tasks
        WHERE
          campaign_id = ${campaignId.id}
          AND step_id = ${stepId.id}
          AND prospect_id = ${prospectId.id}
          AND team_id = ${teamId.id}
          AND task_type IN (${TaskType.ManualEmailMagicContent.toKey}, ${TaskType.AutoEmailMagicContent.toKey})
          AND status = ${TaskStatusType.Approved.toKey}
        LIMIT 1
      """
        .map(rs => {
          TaskType.fromString(rs.string("task_type")).get match {
            case TaskType.SendLinkedinConnectionRequest |
                 TaskType.AutoLinkedinInmail |
                 TaskType.AutoLinkedinConnectionRequest |
                 TaskType.AutoLinkedinMessage |
                 TaskType.AutoViewLinkedinProfile |
                 TaskType.ViewLinkedinProfile |
                 TaskType.SendLinkedinInMail |
                 TaskType.SendWhatsAppMessage |
                 TaskType.CallTask |
                 TaskType.SendSms |
                 TaskType.SendLinkedinMessage |
                 TaskType.GeneralTask |
                 TaskType.SendEmail

            => None

            case TaskType.AutoEmailMagicContent =>

             Some( TaskData.AutoEmailMagicContentData(
                generated_subject = rs.string("task_subject"),
                generated_body = rs.string("task_body"),
                email_scheduled_text_body = rs.string("email_scheduled_text_body"),
                email_scheduled_base_body = rs.string("email_scheduled_base_body")
              ))

            case TaskType.ManualEmailMagicContent =>

             Some( TaskData.ManualEmailMagicContentData(
                generated_subject = rs.string("task_subject"),
                generated_body = rs.string("task_body"),
                email_scheduled_text_body = rs.string("email_scheduled_text_body"),
                email_scheduled_base_body = rs.string("email_scheduled_base_body"),
                email_message_id = None
              ))

          }

        })
        .single
        .apply()
        .get
    }
  }

}

case class DateRangeBetween(
                             startDate: DateTime,
                             endDate: DateTime
                           )

sealed trait ValidatedTaskReq

object ValidatedTaskReq {
  case class ValidatedTaskReqRange(
                                    timeline: InferredQueryTimeline.Range,
                                    pageSize: Int,
                                    dateRange: Option[DateRangeBetween] = None
                                  ) extends ValidatedTaskReq

  case class ValidatedTaskReqExact(
                                  timeline: InferredQueryTimeline.Exact,
                                  pageSize: Int = 500
                                  ) extends ValidatedTaskReq
}

object TaskPgDAO {

  def getFetchTaskScheduledQuery(table: Settings_Table): SQL[Nothing, NoExtractor] = {

    val status_condition: SQLSyntax = table match {
      case Settings_Table.WhatsAppSetting => sqls""""""
      case Settings_Table.GeneralSetting => sqls""""""
      case Settings_Table.LinkedinSetting => sqls""""""
      case Settings_Table.SmsSetting => sqls""""""
      case Settings_Table.CallSetting =>
        sqls"""
          and s.is_active
            """
    }

    sql"""
           UPDATE ${table.toKey} s_rows
           SET
             in_queue_for_scheduling = true,
             pushed_to_queue_for_scheduling_at = now(),
             updated_at = now()
           FROM (
             SELECT s.uuid, s.team_id
             FROM ${table.toKey} s
             INNER JOIN campaigns c ON (
             c.team_id = s.team_id AND
             c.status in (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString}))
             INNER JOIN teams t ON t.id = s.team_id
             INNER JOIN accounts a ON a.id = s.owner_account_id
             INNER JOIN organizations o ON o.id = a.org_id
             WHERE  o.plan_type != ${PlanType.INACTIVE.toString} AND t.active AND a.active $status_condition

             -- if payment due for a while: no need to schedule
             AND (o.payment_due_campaign_pause_at IS NULL OR o.payment_due_campaign_pause_at > now())


             AND (o.paused_till IS NULL OR o.paused_till < now())
             AND (
                    (
                      s.in_queue_for_scheduling = FALSE
                      AND EXISTS (
                        SELECT ces1.campaign_id from campaign_channel_settings ces1
                        INNER JOIN campaigns c1 ON (c1.id = ces1.campaign_id and c1.team_id = ces1.team_id AND c1.status = ${CampaignStatus.RUNNING.toString})
                        where ces1.channel_settings_uuid = s.uuid
                        and ces1.team_id = s.team_id
                        and (
                              ces1.next_to_be_scheduled_at IS NULL OR
                              ces1.next_to_be_scheduled_at < now()
                            )
                        LIMIT 1

                      )
                    )
                    OR
                    s.pushed_to_queue_for_scheduling_at < now() - interval '60 minutes'
                 )

              ORDER BY s.uuid, s.pushed_to_queue_for_scheduling_at ASC

             ) filtered_campaign_rows
           WHERE s_rows.uuid = filtered_campaign_rows.uuid
           RETURNING filtered_campaign_rows.uuid, filtered_campaign_rows.team_id
         """
  }

  def getTimeLineQuery(
                        timeZone: String,
                        timeline: InferredQueryTimeline,
                        time_based_task_type: Option[TimeBasedTaskType],
                        date_range: Option[DateRangeBetween],
                        teamId: Long
                      ): (SQLSyntax, SQLSyntax) = {
    val startoftheday =   Helpers.startOfTheDay(
      timeZone =  timeZone
    )

    val endoftheDay = Helpers.endOfTheDay(
      timeZone = timeZone
    )


    time_based_task_type match{


      case None =>

        (sqls"", sqls"")  // Todo : recheck this logic if its not needed remove it earlier

      case Some(tbtt) => tbtt match {

        case TimeBasedTaskType.TodayAndDue =>

          timeline match {
            case InferredQueryTimeline.Range.Before(dateTime) =>
              (sqls" AND t.due_at < ${dateTime} AND t.status IN (${TaskStatusType.Due.toKey} , ${TaskStatusType.PendingApproval.toKey})", sqls" ORDER BY t.due_at desc")

            case InferredQueryTimeline.Range.After(dateTime) =>
              // Fixing endoftheDay as boundary then setting after using limit - this will be hit 2nd time only
              (sqls" AND t.due_at > ${dateTime} AND t.due_at < ${endoftheDay} AND t.status IN (${TaskStatusType.Due.toKey} , ${TaskStatusType.PendingApproval.toKey}) ", sqls" ORDER BY t.due_at asc")

            case InferredQueryTimeline.Exact(exactlyAtTask) =>
              val due_at_of_exctlyAtTask = sqls" (select due_at from tasks where task_id = ${exactlyAtTask.id} and team_id = $teamId) "
              (sqls" AND t.due_at = ${due_at_of_exctlyAtTask} AND t.status IN (${TaskStatusType.Due.toKey} , ${TaskStatusType.PendingApproval.toKey})", sqls" ")
          }


        case TimeBasedTaskType.Today =>

          timeline match {
            case InferredQueryTimeline.Range.Before(dateTime) =>
              // fixing StartOftheDay as we will not go before that
              (sqls" AND t.due_at < ${dateTime} AND t.due_at >${startoftheday} AND t.status = ${TaskStatusType.Due.toKey}", sqls" ORDER BY t.due_at desc")

            case InferredQueryTimeline.Range.After(dateTime) =>
              // Fixing end of the day as we will not go after that
              (sqls" AND t.due_at > ${dateTime} AND t.due_at < ${endoftheDay} AND t.status = ${TaskStatusType.Due.toKey}", sqls" ORDER BY t.due_at asc")

            case InferredQueryTimeline.Exact(exactlyAtTask) =>
              val due_at_of_exctlyAtTask = sqls" (select due_at from tasks where task_id = ${exactlyAtTask.id} and team_id = $teamId) "
              (sqls" AND t.due_at = ${due_at_of_exctlyAtTask} AND t.status = ${TaskStatusType.Due.toKey}", sqls" ")
          }

        case TimeBasedTaskType.Due =>

          timeline match {
            case InferredQueryTimeline.Range.Before(dateTime) =>
              // passing startoftheday as first time then setting based on limit
              (sqls" AND t.due_at < ${dateTime} AND t.status = ${TaskStatusType.Due.toKey}", sqls" ORDER BY t.due_at desc")

            case InferredQueryTimeline.Range.After(dateTime) =>
              // Fixing startofTheday as boundary then setting after using limit - this will be hit 2nd time only
              (sqls" AND t.due_at > ${dateTime} AND t.due_at < ${startoftheday} AND t.status = ${TaskStatusType.Due.toKey} ", sqls" ORDER BY t.due_at asc")

            case InferredQueryTimeline.Exact(exactlyAtTask) =>
              val due_at_of_exctlyAtTask = sqls" (select due_at from tasks where task_id = ${exactlyAtTask.id} and team_id = $teamId) "
              (sqls" AND t.due_at = ${due_at_of_exctlyAtTask} AND t.status = ${TaskStatusType.Due.toKey}", sqls" ")
          }

        case TimeBasedTaskType.Upcoming =>

          timeline match {
            case InferredQueryTimeline.Range.Before(dateTime) => // Todo : Check if validation is required to test whether date is greater than midnight
              // Fixing endOfTheDay - 2nd time fetch only
              (sqls" AND t.due_at < ${dateTime} AND t.due_at > ${endoftheDay} AND t.status = ${TaskStatusType.Due.toKey}" , sqls" ORDER BY t.due_at desc")

            case InferredQueryTimeline.Range.After(dateTime) =>
              // First Time endofTheDay will be passed then limit set thing
              (sqls" AND t.due_at > ${dateTime} AND t.status = ${TaskStatusType.Due.toKey}", sqls" ORDER BY t.due_at asc")

            case InferredQueryTimeline.Exact(exactlyAtTask) =>
              val due_at_of_exctlyAtTask = sqls" (select due_at from tasks where task_id = ${exactlyAtTask.id} and team_id = $teamId) "
              (sqls" AND t.due_at = ${due_at_of_exctlyAtTask} AND t.status = ${TaskStatusType.Due.toKey}", sqls" ")
          }

        case TimeBasedTaskType.Completed =>
          val dateRangeCondition: SQLSyntax = date_range.map(dr => sqls" AND t.done_at BETWEEN ${dr.startDate} AND ${dr.endDate} ").getOrElse(sqls"")

          timeline match {
            case InferredQueryTimeline.Range.Before(dateTime) =>
              (sqls"${dateRangeCondition} AND t.done_at < ${dateTime} AND t.status IN (${TaskStatusType.Done.toKey} , ${TaskStatusType.Approved.toKey})", sqls" ORDER BY t.done_at desc")

            case InferredQueryTimeline.Range.After(dateTime) =>
              (sqls"${dateRangeCondition} AND t.done_at > ${dateTime} AND t.status IN (${TaskStatusType.Done.toKey} , ${TaskStatusType.Approved.toKey})", sqls" ORDER BY t.done_at asc")  // may_be due_at after but it got completed

            case InferredQueryTimeline.Exact(exactlyAtTask) =>
              val done_at_of_exctlyAtTask = sqls" (select done_at from tasks where task_id = ${exactlyAtTask.id} and team_id = $teamId) "
              (sqls" AND t.done_at = ${done_at_of_exctlyAtTask} AND t.status IN (${TaskStatusType.Done.toKey} , ${TaskStatusType.Approved.toKey}) ", sqls" ")
          }

        case TimeBasedTaskType.Skipped =>

          timeline match {
            case InferredQueryTimeline.Range.Before(dateTime) =>
              (sqls" AND t.skipped_at < ${dateTime} AND t.status = ${TaskStatusType.Skipped.toKey}", sqls" ORDER BY t.skipped_at desc")

            case InferredQueryTimeline.Range.After(dateTime) =>
              (sqls" AND t.skipped_at > ${dateTime} AND t.status = ${TaskStatusType.Skipped.toKey}", sqls" ORDER BY t.skipped_at asc")

            case InferredQueryTimeline.Exact(exactlyAtTask) =>
              val skipped_at_of_exctlyAtTask = sqls" (select skipped_at from tasks where task_id = ${exactlyAtTask.id} and team_id = $teamId) "
              (sqls" AND t.skipped_at = ${skipped_at_of_exctlyAtTask} AND t.status = ${TaskStatusType.Skipped.toKey}", sqls" ")
          }

        case TimeBasedTaskType.Snoozed =>

          timeline match {
            case InferredQueryTimeline.Range.Before(dateTime) =>
              (sqls" AND t.snoozed_at < ${dateTime} AND t.status = ${TaskStatusType.Snoozed.toKey}", sqls" ORDER BY t.snoozed_at desc")

            case InferredQueryTimeline.Range.After(dateTime) =>
              (sqls" AND t.snoozed_at > ${dateTime} AND t.status = ${TaskStatusType.Snoozed.toKey}", sqls" ORDER BY t.snoozed_at asc")

            case InferredQueryTimeline.Exact(exactlyAtTask) =>
              val snoozed_at_of_exctlyAtTask = sqls" (select snoozed_at from tasks where task_id = ${exactlyAtTask.id} and team_id = $teamId) "
              (sqls" AND t.snoozed_at = ${snoozed_at_of_exctlyAtTask} AND t.status = ${TaskStatusType.Snoozed.toKey}", sqls" ")
          }

        case TimeBasedTaskType.Archived =>

          timeline match {
            case InferredQueryTimeline.Range.Before(dateTime) =>
              (sqls" AND t.archive_at < ${dateTime} AND t.status = ${TaskStatusType.Archive.toKey}", sqls" ORDER BY t.archive_at desc")

            case InferredQueryTimeline.Range.After(dateTime) =>
              (sqls" AND t.archive_at > ${dateTime} AND t.status = ${TaskStatusType.Archive.toKey}", sqls" ORDER BY t.archive_at asc")

            case InferredQueryTimeline.Exact(exactlyAtTask) =>
              val archive_at_of_exctlyAtTask = sqls" (select archived_at from tasks where task_id = ${exactlyAtTask.id} and team_id = $teamId) "
              (sqls" AND t.archive_at = ${archive_at_of_exctlyAtTask} AND t.status = ${TaskStatusType.Archive.toKey}", sqls" ")
          }

        case TimeBasedTaskType.Failed =>

          timeline match {
            case InferredQueryTimeline.Range.Before(dateTime) =>
              (sqls" AND t.failed_at < ${dateTime} AND t.status = ${TaskStatusType.Failed.toKey}", sqls" ORDER BY t.failed_at desc")

            case InferredQueryTimeline.Range.After(dateTime) =>
              (sqls" AND t.failed_at > ${dateTime} AND t.status = ${TaskStatusType.Failed.toKey}", sqls" ORDER BY t.failed_at asc")

            case InferredQueryTimeline.Exact(exactlyAtTask) =>
              val failed_at_of_exctlyAtTask = sqls" (select failed_at from tasks where task_id = ${exactlyAtTask.id} and team_id = $teamId) "
              (sqls" AND t.failed_at = ${failed_at_of_exctlyAtTask} AND t.status = ${TaskStatusType.Failed.toKey}", sqls" ")
          }

      }
    }
  }

  def getFilterTaskQuery(team_id: Long, // Todo add a unit test for this
                         orgId: OrgId,
                         assignee_ids: Option[List[Long]],
                         task_type: Option[List[String]],
                         task_status: Option[List[String]],
                         task_priority: Option[List[String]],
                         campaign_ids: Option[List[CampaignId]],
                         reply_sentiment_uuids: Option[List[ReplySentimentUuid]],
                         doNotFetchAutomatedDueTasks: Boolean,
                         permittedAccountIds: Seq[Long],
                         time_based_task_type: Option[TimeBasedTaskType] = None,
//                         emailNotCompulsoryEnabled : Boolean
                        ): SQLSyntax = {

    val taskPriority = getTaskPrioritySQLS(task_priority = task_priority)

    val taskStatusType = getTaskStatusSQLS(task_status = task_status)

    val taskType = getTaskTypeSQLS(task_type = task_type)

    val assigneeIds = getAssigneeSQLS(assignee_ids = assignee_ids)

    val campaignIds = getCampaignSQLS(campaign_ids = campaign_ids)

    val replySentimentUuidsWhereClause = getReplySentimentSQLS(
      reply_sentiment_uuids = reply_sentiment_uuids
    )

    val hideAutomatedTasksQuery = if (doNotFetchAutomatedDueTasks) {
      sqls"""
            AND CASE WHEN t.status = ${TaskStatusType.Due.toKey} THEN t.task_type NOT IN (
              ${TaskType.autoTaskTypes.map(_.toKey)}
            ) ELSE 
              -- Redundant but had to add because it was not working if else statement wasn't there.
              t.task_id != '' 
            END
            """
    }
    else {
      sqls""
    }

    val includeCallActionTasksIfCompleted = time_based_task_type.contains(TimeBasedTaskType.Completed)

    val callActionFilterClause = if (includeCallActionTasksIfCompleted) {
      sqls""
    } else {
      sqls"AND t.created_via != ${TaskCreatedVia.Call_action.toKey}"
    }



    primaryQuery(
      whereClause =
        sqls"""
           t.team_id = ${team_id} AND
           t.assignee_id IN (${permittedAccountIds})
           $hideAutomatedTasksQuery
           $callActionFilterClause
           $taskStatusType
           $taskPriority
           $taskType
           $assigneeIds
           $replySentimentUuidsWhereClause
           $campaignIds
         """,
      orgId = orgId,
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    )
  }


  def getAllTaskQuery(
                       teamId: Long,
                       orgId: OrgId,
                       timeZone: String,
                       validatedTaskReq: ValidatedTaskReq,
                       time_based_task_type: Option[TimeBasedTaskType],
                       assignee_ids: Option[List[Long]],
                       reply_sentiment_uuids: Option[List[ReplySentimentUuid]],
                       campaign_ids: Option[List[CampaignId]],
                       task_type: Option[List[String]],
                       task_status: Option[List[String]],
                       task_priority: Option[List[String]],
                       doNotFetchAutomatedDueTasks: Boolean,
                       permittedAccountIds: Seq[Long],
//                       emailNotCompulsoryEnabled: Boolean
                     ): SQL[Nothing, NoExtractor] = {

    val query: SQL[Nothing, NoExtractor] = validatedTaskReq match {
      case vq: ValidatedTaskReq.ValidatedTaskReqRange =>

        val filterQueryCondition: SQLSyntax = getFilterTaskQuery(
          team_id = teamId,
          orgId = orgId,
          assignee_ids = assignee_ids,
          campaign_ids = campaign_ids,
          reply_sentiment_uuids = reply_sentiment_uuids,
          task_type = task_type,
          task_status = task_status,
          task_priority = task_priority,
          doNotFetchAutomatedDueTasks = doNotFetchAutomatedDueTasks,
          permittedAccountIds = permittedAccountIds,
          time_based_task_type = time_based_task_type,
//          emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
        )

        val (timeLineQuery: SQLSyntax, orderBy: SQLSyntax) = getTimeLineQuery(
          timeZone = timeZone,
          timeline = vq.timeline,
          time_based_task_type = time_based_task_type,
          date_range = vq.dateRange,
          teamId = teamId
        )

          sql"""
                $filterQueryCondition
                $timeLineQuery
                $orderBy
                limit ${vq.pageSize}
                """

      case vq: ValidatedTaskReq.ValidatedTaskReqExact =>

        val filterQueryCondition: SQLSyntax = getFilterTaskQuery(
          team_id = teamId,
          orgId = orgId,
          reply_sentiment_uuids = reply_sentiment_uuids,
          assignee_ids = assignee_ids,
          task_type = task_type,
          task_status = task_status,
          task_priority = task_priority,
          campaign_ids = campaign_ids,
          doNotFetchAutomatedDueTasks = doNotFetchAutomatedDueTasks,
          permittedAccountIds = permittedAccountIds,
          time_based_task_type = time_based_task_type,
//          emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
        )

        val (timeLineQuery: SQLSyntax, orderBy: SQLSyntax) = getTimeLineQuery(
          timeZone = timeZone,
          timeline = vq.timeline,
          date_range = None,
          time_based_task_type = time_based_task_type,
          teamId = teamId
        )


        sql"""
              $filterQueryCondition
              $timeLineQuery
              $orderBy
              limit ${vq.pageSize}
              """
    }

    query

  }

    def primaryQuery(
                      whereClause: SQLSyntax,
                      orgId: OrgId,
//                      emailNotCompulsoryEnabled : Boolean
                    ): SQLSyntax = {
//      val prospectEmailSql = if(emailNotCompulsoryEnabled) {
//        sqls""" LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary) """
//
//      }else{
//        sqls""" INNER JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary) """
//      }
      sqls"""
              SELECT t.*,
                pe.email AS prospect_email,
                CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
                CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
                ${getRecordingUrlForTaskSQL}
              FROM tasks t
              INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
              LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
              INNER JOIN accounts a on a.id = t.assignee_id
              WHERE $whereClause
          """
    }

    /*
      OrgId is option Here:

        Touches 1 flow TaskPgDao.getSnoozedTasksBeforeTime
        which is called in cron - UnSnoozeSnoozeTaskCron

     */
    def __findTaskSQL(whereClause: SQLSyntax ,
                      orgId: Option[OrgId],
//                      emailNotCompulsoryEnabled : Boolean

                     ): SQL[?,?] = {

      /*
      * NOTE:
      * 29 July 2024
      * Org Id currently is empty in only 1 flow that is UnSnoozeSnoozeTaskCron to find task to snooze/unsnooze for all orgs ,
      * if its empty we can just use left join by default for non email prospects directly without checking org,
      * should not affect existing flows.
      * */
      val prospectEmailSql = if ( orgId.isEmpty || orgId.isDefined) {

        sqls""" LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary) """

      } else {
        sqls""" INNER JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary) """
      }

      sql"""
          SELECT t.*,
            pe.email AS prospect_email,
            CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
            CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
            ${getRecordingUrlForTaskSQL},
            p.linkedin_url as prospect_linkedin_url,
            p.phone as prospect_phone_number,
            p.company as prospect_company,
            p.job_title as prospect_designation,
            p.timezone as prospect_timezone,
            es.id as email_message_id
          FROM tasks t
          INNER JOIN prospects p ON (p.id = t.prospect_id AND p.team_id = t.team_id)
          LEFT JOIN emails_scheduled es on (es.team_id = t.team_id AND es.prospect_id = t.prospect_id AND es.campaign_id = t.campaign_id AND t.step_id = es.step_id)
          ${prospectEmailSql}
          INNER JOIN accounts a ON a.id = t.assignee_id
          WHERE $whereClause
      """
    }

  private def getTaskPrioritySQLS(
                                   task_priority: Option[List[String]]
                                 ): SQLSyntax = {
    task_priority match {
      case None =>
        sqls""""""

      case Some(taskPriority) =>
        sqls"""
              AND t.priority IN (${taskPriority})
            """
    }

  }

  private def getTaskStatusSQLSforCount(
                                       task_status: Option[List[String]]
                                       ): SQLSyntax = {
    task_status match {
      case None =>
        sqls""""""

      case Some(taskStatus) =>
        sqls"""
             AND status IN (${taskStatus})
           """
    }
  }

  private def getTaskStatusSQLS(
                                 task_status: Option[List[String]]
                               ): SQLSyntax = {
    task_status match {
      //
      case None =>
        sqls""""""
      case Some(taskStatus) =>
        sqls"""
             AND t.status IN (${taskStatus})
           """
    }

  }

  private def getTaskTypeSQLS(
                               task_type: Option[List[String]]
                             ): SQLSyntax = {
    task_type match {
      case None =>
        sqls""""""
      case Some(taskType) =>
        sqls"""
            AND t.task_type IN (${taskType})
            """
    }

  }

  private def getAssigneeSQLS(assignee_ids: Option[List[Long]]) = {
    assignee_ids match {
      case None =>
        sqls""""""
      case Some(assigneeIds) =>
        sqls"""
            AND t.assignee_id IN (${assigneeIds})
            """
    }
  }

  private def getCampaignSQLS(campaign_ids: Option[List[CampaignId]]) = {
    campaign_ids match {
      case None => sqls""
      case Some(campaignIds) =>
        sqls"""
              AND t.campaign_id IN (${campaignIds.map(_.id)})
              """
    }
  }

  private def getReplySentimentSQLS(
    reply_sentiment_uuids: Option[List[ReplySentimentUuid]]
  ): SQLSyntax = {

    val reply_sentiment_uuids_list: List[ReplySentimentUuid] =
      reply_sentiment_uuids.toList.flatten

    if (reply_sentiment_uuids_list.isEmpty) {

      sqls""

    } else {

      sqls"""
            AND t.reply_sentiment_uuid IN ${SQLUtils.generateSQLValuesClause(arr = reply_sentiment_uuids_list.map(_.uuid))}
          """

    }

  }

  private def getUpdateTaskDataSQLS(
                                     task_data: TaskData
                                   ): SQLSyntax = {

    task_data match {
      case data: TaskData.SendEmailData =>
          sqls"""
            task_subject = ${data.subject},
            task_body = ${data.body},
          """

      case data: TaskData.SendLinkedinInMailData =>
        sqls"""
              task_subject = ${data.subject.getOrElse(None)},
              task_body = ${data.body},
            """

      case data: TaskData.ViewLinkedinProfileData =>
        sqls""""""

      case data: TaskData.LinkedinConnectionRequestData =>
          sqls"""
            task_body = ${data.request_message},
          """

      case data: TaskData.SendLinkedinMessageData =>
          sqls"""
            task_body = ${data.body},
          """

      case data: TaskData.AutoLinkedinInmail =>
        sqls"""
                task_subject = ${data.subject},
                task_body = ${data.body},
              """

      case data: TaskData.AutoViewLinkedinProfile =>
        sqls""""""

      case data: TaskData.AutoLinkedinConnectionRequest =>
        sqls"""
              task_body = ${data.body},
            """

      case data: TaskData.AutoLinkedinMessage =>
        sqls"""
              task_body = ${data.body},
            """

      case data: TaskData.SendSmsData =>
          sqls"""
            task_body = ${data.body},
          """

      case data: TaskData.SendWhatsAppMessageData =>
          sqls"""
            task_body =  ${data.body},
          """

      case data: TaskData.GeneralTaskData =>
          sqls"""
            task_body = ${data.task_notes},
          """

      case data: TaskData.CallTaskData =>
          sqls"""
            task_body = ${data.body},
          """

      case data: TaskData.AutoEmailMagicContentData =>
        sqls"""
              task_subject = ${data.generated_subject},
              task_body = ${data.generated_body},
            """

      case data: TaskData.ManualEmailMagicContentData =>
        sqls"""
              task_subject = ${data.generated_subject},
              task_body = ${data.generated_body},
            """
    }

  }

  private def getUpdatePrioritySQLS(
                                     priority: TaskPriority
                                   ): SQLSyntax = {
      sqls"""
              priority = ${priority.toKey}
            """

  }

  private def assigneeIdCheck(
                       changeStatusPermissionCheck: ChangeStatusPermissionCheck
                     ): SQLSyntax = {

    changeStatusPermissionCheck match {
      case data: ChangeStatusPermissionCheck.ManualTaskCheck =>
        sqls"""
              AND assignee_id IN (${data.permittedAccountIds})
            """

      case data: ChangeStatusPermissionCheck.AutomatedTaskCheck =>
        sqls""""""

      case data: ChangeStatusPermissionCheck.NoStatusPermissionCheck => 
        sqls""""""

    }
  }

  private def getChangeStatusSQLS(
                                 doer: Option[Long],
                                 changeTime: DateTime,
                                 status: UpdateTaskStatus
                                 ): SQLSyntax = {

    status match {
      case status: UpdateTaskStatus.Done =>
          sqls"""
              status = ${TaskStatusType.done},
              done_at = $changeTime,
              done_by = ${doer.orNull}
            """

      case status: UpdateTaskStatus.Due =>
        // FIXME: Instead of changing it to due_at time, it should be changeTime.
          sqls"""
              status = ${TaskStatusType.due},
              due_at = ${status.due_at}
            """

      case status: UpdateTaskStatus.Snoozed =>
          sqls"""
              status = ${TaskStatusType.snoozed},
              snoozed_till = ${status.snoozed_till},
              snoozed_at = ${changeTime},
              snoozed_by = $doer
            """

      case status: UpdateTaskStatus.Skipped =>
          sqls"""
              status = ${TaskStatusType.skipped},
              skipped_at = $changeTime,
              skipped_by = $doer
            """

      case status: UpdateTaskStatus.Archive =>
          sqls"""
              status = ${TaskStatusType.archive},
              archive_at = $changeTime
            """

      case status: UpdateTaskStatus.QueuedToMq =>
          sqls"""
                status = ${TaskStatusType.queuedToMq},
                pushed_to_mq_at = $changeTime
              """

      case status: UpdateTaskStatus.Approved =>
          sqls"""
                status = ${TaskStatusType.approved},
                done_by = ${doer.orNull},
                done_at = $changeTime
              """

    }
  }

  private def getChangedDueDateSQLS(
                                   status: TaskStatus
                                   ): SQLSyntax = {
    status match {
      case status: TaskStatus.Due =>
        sqls"""
              due_at = ${status.due_at}
            """
      case _ =>
        sqls""""""  //  Update Task Api will only give Due status from frontend
    }
  }

  private def getUpdateStatusSQLS(
                                   status: TaskStatus
                                 ): SQLSyntax = {

    status match {
      case status: TaskStatus.Failed =>
        sqls"""
              status = ${status.status_type.toKey},
              failure_reason = ${status.failure_reason},
              failed_at = ${status.failed_at}
              """

      case status: TaskStatus.PushedToPhantomBusterQueue =>
        sqls"""
              status = ${status.status_type.toKey},
              pushed_to_phantombuster_queue_at = ${status.pushed_at}
              """

      case status: TaskStatus.Done =>
          sqls"""
              status = ${status.status_type.toKey},
              done_at = ${status.done_at},
              done_by = ${status.done_by}
            """

        /*
        When Bright Data blocks the PhantomBuster IP,
        task fails but we update the task status as due to be executed again,
        so we should reset the execution_attempts also
        because we do not pick tasks which have execution_attempts = 2
         */
      case status: TaskStatus.Due =>
          sqls"""
              status = ${status.status_type.toKey},
              execution_attempts = 0,
              due_at = ${status.due_at}
            """


      case status: TaskStatus.Snoozed =>
          sqls"""
              status = ${status.status_type.toKey},
              snoozed_till = ${status.snoozed_till},
              snoozed_by = ${status.snoozed_by}
            """

      case status: TaskStatus.Skipped =>
          sqls"""
              status = ${status.status_type.toKey},
              skipped_at = ${status.skipped_at},
              skipped_by = ${status.skipped_by}
            """

      case status: TaskStatus.Archive =>
          sqls"""
              status = ${status.status_type.toKey},
              archive_at = ${status.archive_at}
            """

      case status: TaskStatus.QueuedToMq =>
          sqls"""
                status = ${status.status_type.toKey},
                pushed_to_mq_at =${status.pushed_to_mq_at}
              """

      case status: TaskStatus.PendingApproval =>

        sqls"""
              status = ${status.status_type.toKey},
              due_at = ${status.due_at}
            """

      case status: TaskStatus.Approved =>

        sqls"""
              status = ${status.status_type.toKey},
              done_at = ${status.done_at},
              done_by = ${status.done_by}
            """



    }
  }

  def getUpdateWebhookStatusSQLS(
                                  data: CaptainDataWebhookResponse,
                                  status: CaptainDataWorkflowStatus
                                 ): SQLSyntax = {

    status match {

      case CaptainDataWorkflowStatus.INITIATED |
           CaptainDataWorkflowStatus.CREATED  =>
        sqls"""
                cd_job_status = ${status.toString}
                """
      case CaptainDataWorkflowStatus.SUCCESSFUL =>
        sqls"""
                cd_job_status = ${status.toString},
                status = ${TaskStatusType.Done.toString},
                done_at = ${DateTime.now()}
                """
      case CaptainDataWorkflowStatus.FAILURE =>
        sqls"""
                cd_job_status = ${status.toString},
                failure_reason = ${data.error},
                status = ${TaskStatusType.Failed.toString},
                failed_at = ${DateTime.now()}
                """
    }

  }

  private def taskDataFromDB(rs: WrappedResultSet): TaskData = {
    TaskType.fromString(rs.string("task_type")).get match {
      case TaskType.SendEmail =>
        TaskData.SendEmailData(
          subject = rs.string("task_subject"),
          body = rs.string("task_body"),
          email_message_id = rs.longOpt("email_message_id")
        )

      case TaskType.SendSms =>
        TaskData.SendSmsData(
          body = rs.string("task_body")
        )

      case TaskType.CallTask =>
        TaskData.CallTaskData(
          body = rs.string("task_body"),
          recording_link = rs.stringOpt("latest_recording_url")
        )

      case TaskType.SendWhatsAppMessage =>
        TaskData.SendWhatsAppMessageData(
          body = rs.string("task_body")
        )

      case TaskType.GeneralTask =>
        TaskData.GeneralTaskData(
          task_notes = rs.string("task_body")
        )

      case TaskType.SendLinkedinMessage =>
        TaskData.SendLinkedinMessageData(
          body = rs.string("task_body")
        )

      case TaskType.SendLinkedinConnectionRequest =>
        TaskData.LinkedinConnectionRequestData(
          request_message = rs.stringOpt("task_body")
        )

      case TaskType.SendLinkedinInMail =>
        TaskData.SendLinkedinInMailData(
          subject = rs.stringOpt("task_subject"),
          body = rs.string("task_body")
        )

      case TaskType.ViewLinkedinProfile =>
        TaskData.ViewLinkedinProfileData()

      case TaskType.AutoLinkedinMessage =>
        TaskData.AutoLinkedinMessage(
          body = rs.string("task_body")
        )

      case TaskType.AutoLinkedinConnectionRequest =>
        TaskData.AutoLinkedinConnectionRequest(
          body = rs.stringOpt("task_body")
        )

      case TaskType.AutoLinkedinInmail =>
        TaskData.AutoLinkedinInmail(
          subject = rs.string("task_subject"),
          body = rs.string("task_body")
        )

      case TaskType.AutoViewLinkedinProfile =>
        TaskData.AutoViewLinkedinProfile()

      case TaskType.AutoEmailMagicContent =>
        TaskData.AutoEmailMagicContentData(
          generated_subject = rs.string("task_subject"),
          generated_body = rs.string("task_body"),
          email_scheduled_text_body = rs.string("email_scheduled_text_body"),
          email_scheduled_base_body = rs.string("email_scheduled_base_body")
        )

      case TaskType.ManualEmailMagicContent =>
        TaskData.ManualEmailMagicContentData(
          generated_subject = rs.string("task_subject"),
          generated_body = rs.string("task_body"),
          email_scheduled_text_body = rs.string("email_scheduled_text_body"),
          email_scheduled_base_body = rs.string("email_scheduled_base_body"),
          email_message_id = rs.longOpt("email_message_id")
        )

    }
  }
  
  private def taskDetailsToProcessWebhookUpdateFromDb(rs: WrappedResultSet): TaskDetailsToProcessWebhookUpdate = {
    TaskDetailsToProcessWebhookUpdate(
      taskId = TaskUuid(uuid = rs.string("task_id")),
      orgId = OrgId(id =rs.long("org_id")),
      teamId = TeamId(id =rs.long("team_id"))
    )
  }

  private def taskFromDB(rs: WrappedResultSet): Task = {
    //FIXME : MATCH ON TaskType to make it exhaustive -> here, matching is being done on a string, so it is not exhaustive (even if cases do not include some objects of TaskType, the code will compile). We should match on the case class to make it exhaustive.
    val (taskType, taskData) = rs.string("task_type") match {

      case TaskType.sendEmail =>
        val task_type = TaskType.SendEmail
        val task_data = TaskData.SendEmailData(
          subject = rs.string("task_subject"),
          body = rs.string("task_body"),
          email_message_id = rs.longOpt("email_message_id")
        )
        (task_type, task_data)

      case TaskType.sendSms =>
        val task_type = TaskType.SendSms
        val task_data = TaskData.SendSmsData(
          body = rs.string("task_body")
        )
        (task_type, task_data)

      case TaskType.callTask =>
        val task_type = TaskType.CallTask
        val task_data = TaskData.CallTaskData(
          body = rs.string("task_body"),
          recording_link = rs.stringOpt("latest_recording_url")
        )
        (task_type, task_data)

      case TaskType.sendWhatsappMessage =>
        val task_type = TaskType.SendWhatsAppMessage
        val task_data = TaskData.SendWhatsAppMessageData(
          body = rs.string("task_body")
        )
        (task_type, task_data)

      case TaskType.`generalTask` =>
        val task_type = TaskType.GeneralTask
        val task_data = TaskData.GeneralTaskData(
          task_notes = rs.string("task_body")
        )
        (task_type, task_data)

      case TaskType.sendLinkedinMessage =>
        val task_type = TaskType.SendLinkedinMessage
        val task_data = TaskData.SendLinkedinMessageData(
          body = rs.string("task_body")
        )
        (task_type, task_data)

      case TaskType.sendLinkedinConnectionRequest =>
        val task_type = TaskType.SendLinkedinConnectionRequest
        val task_data = TaskData.LinkedinConnectionRequestData(
          request_message = rs.stringOpt("task_body")
        )
        (task_type, task_data)

      case TaskType.sendLinkedinInMail =>
        val task_type = TaskType.SendLinkedinInMail
        val task_data = TaskData.SendLinkedinInMailData(
          subject = rs.stringOpt("task_subject"),
          body = rs.string("task_body")
        )
        (task_type, task_data)

      case TaskType.viewLinkedinProfile =>
        val task_type = TaskType.ViewLinkedinProfile
        val task_data = TaskData.ViewLinkedinProfileData()
        (task_type, task_data)

      case TaskType.autoLinkedinMessage =>
        val task_type = TaskType.AutoLinkedinMessage
        val task_data = TaskData.AutoLinkedinMessage(
          body = rs.string("task_body")
        )
        (task_type, task_data)

      case TaskType.autoLinkedinConnectionRequest =>
        val task_type = TaskType.AutoLinkedinConnectionRequest
        val task_data = TaskData.AutoLinkedinConnectionRequest(
          body = rs.stringOpt("task_body")
        )
        (task_type, task_data)

      case TaskType.autoLinkedinInmail =>
        val task_type = TaskType.AutoLinkedinInmail
        val task_data = TaskData.AutoLinkedinInmail(
          subject = rs.string("task_subject"),
          body = rs.string("task_body")
        )
        (task_type, task_data)

      case TaskType.autoViewLinkedinProfile =>
        val task_type = TaskType.AutoViewLinkedinProfile
        val task_data = TaskData.AutoViewLinkedinProfile()
        (task_type, task_data)

      case TaskType.autoEmailMagicContent =>
          val task_type = TaskType.AutoEmailMagicContent
          val task_data = TaskData.AutoEmailMagicContentData(
              generated_subject = rs.string("task_subject"),
              generated_body = rs.string("task_body"),
              email_scheduled_text_body = rs.string("email_scheduled_text_body"),
              email_scheduled_base_body = rs.string("email_scheduled_base_body")
          )
          (task_type,task_data)

      case TaskType.manualEmailMagicContent =>
          val task_type = TaskType.ManualEmailMagicContent
          val task_data = TaskData.ManualEmailMagicContentData(
              generated_subject = rs.string("task_subject"),
              generated_body = rs.string("task_body"),
              email_scheduled_text_body = rs.string("email_scheduled_text_body"),
              email_scheduled_base_body = rs.string("email_scheduled_base_body"),
              email_message_id = rs.longOpt("email_message_id")
          )
          (task_type,task_data)

    }

    val taskStatus = TaskStatusType.fromString(rs.string("status")).get match {
      case TaskStatusType.Done =>
        TaskStatus.Done(
          done_at = rs.jodaDateTime("done_at"),
          done_by = rs.longOpt("done_by")
        )

      case TaskStatusType.Snoozed =>
        TaskStatus.Snoozed(
          snoozed_till = rs.jodaDateTime("snoozed_till"),
          snoozed_at = rs.jodaDateTime("snoozed_at"),
          snoozed_by = rs.long("snoozed_by")
        )

      case TaskStatusType.Skipped =>
        TaskStatus.Skipped(
          skipped_at = rs.jodaDateTime("skipped_at"),
          skipped_by = rs.longOpt("skipped_by")
        )

      case TaskStatusType.Due =>
        TaskStatus.Due(
          due_at = rs.jodaDateTime("due_at")
        )

      case TaskStatusType.Archive =>
        TaskStatus.Archive(
          archive_at = rs.jodaDateTime("archive_at")
        )

      case TaskStatusType.PushedToLinkedinExecutionQueue =>
        TaskStatus.PushedToPhantomBusterQueue(
          pushed_at = rs.jodaDateTime("pushed_to_phantombuster_queue_at")
        )

      case TaskStatusType.Failed =>
        TaskStatus.Failed(
          failure_reason = rs.string("failure_reason"),
          failed_at = rs.jodaDateTime("failed_at")
        )

      case TaskStatusType.QueuedToMq =>
          TaskStatus.QueuedToMq(
              pushed_to_mq_at = rs.jodaDateTime("pushed_to_mq_at")
          )

      case TaskStatusType.PendingApproval =>
        TaskStatus.PendingApproval(
          due_at = rs.jodaDateTime("due_at")
        )

      case TaskStatusType.Approved =>
        TaskStatus.Approved(
          done_at = rs.jodaDateTime("done_at"),
          done_by = rs.longOpt("done_by")
        )
    }

    val assignee_name = rs.string("assignee_name")
    val taskPriority = TaskPriority.fromString(rs.string("priority")).get
    val assignee_id = rs.long("assignee_id")
    val prospect_id = rs.longOpt("prospect_id")
    val prospect_name = rs.stringOpt("prospect_name")
    val prospect_email = rs.stringOpt("prospect_email")
    val prospect_linkedin_url = rs.stringOpt("prospect_linkedin_url")
    val prospect_phone_number = rs.stringOpt("prospect_phone_number")
    val prospect_company = rs.stringOpt("prospect_company")
    val timezone = rs.stringOpt("prospect_timezone")
    val designation = rs.stringOpt("prospect_designation")

    val taskProspect = if (prospect_id.isDefined) {
      Some(
        TaskProspect(
          id = prospect_id.get,
          name = prospect_name,
          email = prospect_email,
          linkedin_url = prospect_linkedin_url,
          phone_number = prospect_phone_number,
          company = prospect_company,
          timezone =  timezone,
          designation = designation
        )
      )
    } else None

    Task(
      task_id = rs.string("task_id"),
      is_auto_task = rs.boolean("is_auto_task"),
      task_type = taskType,
      added_by = rs.long("added_by"),
      task_data = taskData,
      status = taskStatus,
      assignee = Some(Assignee(
        id = assignee_id,
        name = assignee_name
      )),
      team_id = rs.long("team_id"),
      prospect = taskProspect,
      priority = taskPriority,
      note = rs.stringOpt("note"),
      created_at = rs.jodaDateTime("created_at"),
      updated_at = rs.jodaDateTimeOpt("updated_at"),
      due_at = rs.jodaDateTime("due_at"),
      created_via = TaskCreatedVia.fromString(rs.string("created_via")).get,
      campaign_id = rs.longOpt("campaign_id"),
      campaign_name = rs.stringOpt("campaign_name"),
      step_id = rs.longOpt("step_id"),
      step_label = rs.stringOpt("step_label"),
      is_opening_step = rs.booleanOpt("is_opening_step"),
      reply_sentiment_uuid = rs.stringOpt("reply_sentiment_uuid").map(rs => ReplySentimentUuid(uuid = rs))
    )
  }

  private def getTaskDataSQLS(
                               task_data: NewTask
                             ): (SQLSyntax, SQLSyntax) = {

    task_data.task_data match {
      case data: TaskData.SendEmailData =>
        val columns =
          sqls"""
              task_subject,
              task_body,
            """
        val columnValues =
          sqls"""
              ${data.subject},
              ${data.body},
            """
        (columns, columnValues)

      case data: TaskData.LinkedinConnectionRequestData =>
        val columns =
          sqls"""
              task_body,
            """
        val columnValues =
          sqls"""
              ${data.request_message},
            """
        (columns, columnValues)

      case data: TaskData.SendLinkedinMessageData =>
        val columns =
          sqls"""
              task_body,
            """
        val columnValues =
          sqls"""
              ${data.body},
            """
        (columns, columnValues)

      case data: TaskData.SendLinkedinInMailData =>
        val columns =
          sqls"""
                task_subject,
                task_body,
              """
        val columnValues =
          sqls"""
                ${data.subject.getOrElse(None)},
                ${data.body},
              """
        (columns, columnValues)

      case data: TaskData.ViewLinkedinProfileData =>
        val columns =
          sqls"""task_body,"""
        val columnValues =
          sqls"""${""},"""
        (columns, columnValues)

      case data: TaskData.AutoLinkedinConnectionRequest =>
        val columns =
          sqls"""
                task_body,
              """
        val columnValues =
          sqls"""
                ${data.body},
              """
        (columns, columnValues)

      case data: TaskData.AutoLinkedinMessage =>
        val columns =
          sqls"""
                task_body,
              """
        val columnValues =
          sqls"""
                ${data.body},
              """
        (columns, columnValues)

      case data: TaskData.AutoLinkedinInmail =>
        val columns =
          sqls"""
                  task_subject,
                  task_body,
                """
        val columnValues =
          sqls"""
                  ${data.subject},
                  ${data.body},
                """
        (columns, columnValues)

      case data: TaskData.AutoViewLinkedinProfile =>
        val columns =
          sqls"""task_body,"""
        val columnValues =
          sqls"""${""},"""
        (columns, columnValues)

      case data: TaskData.SendSmsData =>
        val columns =
          sqls"""
              task_body,
            """
        val columnValues =
          sqls"""
              ${data.body},
            """
        (columns, columnValues)

      case data: TaskData.CallTaskData =>
        val columns =
          sqls"""
                task_body,
              """
        val columnValues =
          sqls"""
                ${data.body},
              """
        (columns, columnValues)

      case data: TaskData.SendWhatsAppMessageData =>
        val columns =
          sqls"""
              task_body,
            """
        val columnValues =
          sqls"""
              ${data.body},
            """
        (columns, columnValues)

      case data: TaskData.GeneralTaskData =>
        val columns =
          sqls"""
              task_body,
            """
        val columnValues =
          sqls"""
              ${data.task_notes},
            """
        (columns, columnValues)


      case data: TaskData.AutoEmailMagicContentData =>

        val columns =
          sqls"""
              task_subject,
              task_body,
              email_scheduled_text_body,
              email_scheduled_base_body,
            """
        val columnValues =
          sqls"""
              ${data.generated_subject},
              ${EmailHelper.convertNewlinesToBrTags(data.generated_body)},
              ${data.email_scheduled_text_body},
              ${data.email_scheduled_base_body},
            """
        (columns, columnValues)


      case data: TaskData.ManualEmailMagicContentData =>

        val columns =
          sqls"""
              task_subject,
              task_body,
              email_scheduled_text_body,
              email_scheduled_base_body,
            """
        val columnValues =
          sqls"""
              ${data.generated_subject},
              ${EmailHelper.convertNewlinesToBrTags(data.generated_body)},
              ${data.email_scheduled_text_body},
              ${data.email_scheduled_base_body},
            """
        (columns, columnValues)


    }

  }

  private def getPrioritySQL(task_data: NewTask) = {

    val columns =
      sqls"""
              priority,
            """
    val columnValues =
      sqls"""
             ${task_data.priority.toKey},
            """
    (columns, columnValues)
  }

  private def getStatusSQL(task_data: NewTask) = {

    task_data.status match {
      case status: TaskStatus.Done =>
        val columns =
          sqls"""
              status,
              done_at,
              done_by,
            """
        val columnValues =
          sqls"""
              ${status.status_type.toKey},
              ${status.done_at},
              ${status.done_by},
            """
        (columns, columnValues)

      case status: TaskStatus.Due =>
        val columns =
          sqls"""
              status,
              due_at,
            """
        val columnValues =
          sqls"""
              ${status.status_type.toKey},
              ${status.due_at},
            """
        (columns, columnValues)


      case status: TaskStatus.Snoozed =>
        val columns =
          sqls"""
              status,
              snoozed_till,
              snoozed_by,
            """
        val columnValues =
          sqls"""
              ${status.status_type.toKey},
              ${status.snoozed_till},
              ${status.snoozed_by},
            """

        (columns, columnValues)


      case status: TaskStatus.Skipped =>
        val columns =
          sqls"""
              status,
              skipped_at,
              skipped_by,
            """
        val columnValues =
          sqls"""
              ${status.status_type.toKey},
              ${status.skipped_at},
              ${status.skipped_by},
            """
        (columns, columnValues)

      case status: TaskStatus.Archive =>
        val columns =
          sqls"""
              status,
              archive_at,
            """
        val columnValues =
          sqls"""
              ${status.status_type.toKey},
              ${status.archive_at},
            """
        (columns, columnValues)

      case status: TaskStatus.PushedToPhantomBusterQueue =>
        val columns =
          sqls"""
                status,
                pushed_to_phantombuster_queue_at,
                """
        val columnValues =
          sqls"""
                ${status.status_type.toKey},
                ${status.pushed_at},
                """
        (columns, columnValues)

      case status: TaskStatus.Failed =>
        val columns =
          sqls"""
                  status,
                  failure_reason,
                  """
        val columnValues =
          sqls"""
                  ${status.status_type.toKey},
                  ${status.failure_reason},
                  """
        (columns, columnValues)

      case status: TaskStatus.QueuedToMq =>
          val columns =
              sqls"""
                    status,
                    pushed_to_mq_at,
                  """

          val columnValues =
              sqls"""
                    ${status.status_type.toKey},
                    ${status.pushed_to_mq_at},
                  """

          (columns,columnValues)

      case status: TaskStatus.PendingApproval =>

        val columns =sqls"""
                    status,
                    due_at,
                  """
        
        val columnValues = sqls"""
                    ${status.status_type.toKey},
                    ${status.due_at},
                  """
          
        (columns, columnValues)

      case status: TaskStatus.Approved =>

        val columns =
          sqls"""
                    status,
                    done_at,
                    done_by,
                    due_at,
                  """

        val columnValues =
          sqls"""
                    ${status.status_type.toKey},
                    ${status.done_at},
                    ${status.done_by},
                    ${status.done_at},
                  """

        (columns, columnValues)








    }

  }

  private def getTaskTypeSQL(task_data: NewTask) = {

    task_data.task_type match {
      case TaskType.SendEmail =>
        val columns =
          sqls"""
              task_type,
            """
        val columnValues =
          sqls"""
             ${TaskType.sendEmail},
            """
        (columns, columnValues)

      case TaskType.SendLinkedinConnectionRequest =>
        val columns =
          sqls"""
              task_type,
            """
        val columnValues =
          sqls"""
             ${TaskType.sendLinkedinConnectionRequest},
            """
        (columns, columnValues)

      case TaskType.SendLinkedinMessage =>
        val columns =
          sqls"""
              task_type,
            """
        val columnValues =
          sqls"""
             ${TaskType.sendLinkedinMessage},
            """
        (columns, columnValues)

      case TaskType.SendLinkedinInMail =>
        val columns =
          sqls"""
                task_type,
              """
        val columnValues =
          sqls"""
               ${TaskType.sendLinkedinInMail},
              """
        (columns, columnValues)

      case TaskType.ViewLinkedinProfile =>
        val columns =
          sqls"""
                task_type,
              """
        val columnValues =
          sqls"""
               ${TaskType.viewLinkedinProfile},
              """
        (columns, columnValues)

      case TaskType.AutoLinkedinConnectionRequest =>
        val columns =
          sqls"""
                task_type,
              """
        val columnValues =
          sqls"""
               ${TaskType.autoLinkedinConnectionRequest},
              """
        (columns, columnValues)

      case TaskType.AutoLinkedinMessage =>
        val columns =
          sqls"""
                task_type,
              """
        val columnValues =
          sqls"""
               ${TaskType.autoLinkedinMessage},
              """
        (columns, columnValues)

      case TaskType.AutoLinkedinInmail =>
        val columns =
          sqls"""
                  task_type,
                """
        val columnValues =
          sqls"""
                 ${TaskType.autoLinkedinInmail},
                """
        (columns, columnValues)

      case TaskType.AutoViewLinkedinProfile =>
        val columns =
          sqls"""
                  task_type,
                """
        val columnValues =
          sqls"""
                 ${TaskType.autoViewLinkedinProfile},
                """
        (columns, columnValues)

      case TaskType.GeneralTask =>
        val columns =
          sqls"""
              task_type,
            """
        val columnValues =
          sqls"""
             ${TaskType.generalTask},
            """
        (columns, columnValues)

      case TaskType.SendSms =>
        val columns =
          sqls"""
              task_type,
            """
        val columnValues =
          sqls"""
             ${TaskType.sendSms},
            """
        (columns, columnValues)

      case TaskType.CallTask =>
        val columns =
          sqls"""
              task_type,
            """
        val columnValues =
          sqls"""
             ${TaskType.callTask},
            """
        (columns, columnValues)


      case TaskType.SendWhatsAppMessage =>
        val columns =
          sqls"""
              task_type,
            """
        val columnValues =
          sqls"""
             ${TaskType.sendWhatsappMessage},
            """
        (columns, columnValues)


      case TaskType.AutoEmailMagicContent =>

        val columns =
          sqls"""
              task_type,
            """
        val columnValues =
          sqls"""
             ${TaskType.autoEmailMagicContent},
            """
        (columns, columnValues)


      case TaskType.ManualEmailMagicContent =>

        val columns =
          sqls"""
              task_type,
            """
        val columnValues =
          sqls"""
             ${TaskType.manualEmailMagicContent},
            """
        (columns, columnValues)



    }
  }

  def getRecordingUrlForTaskSQL: SQLSyntax = {
    sqls"""

         (
            SELECT
                ccl.recording_url as latest_recording_url
            FROM
                call_conference_logs ccl
            WHERE
                ccl.team_id = t.team_id
                AND ccl.task_uuid = t.task_id
                AND ccl.recording_url is not null

            ORDER BY
                ccl.log_created_at DESC
            LIMIT 1
        ) AS latest_recording_url

        """

  }
}
