package api.tasks.controllers

import api.AppConfig
import api.accounts.models.{AccountId, OrgId}
import api.accounts.{PermType, PermissionMethods, PermissionUtils, TeamId}
import api.tasks.TaskUtils
import api.tasks.models.{ChangeStatusPermissionCheck, NewTask, Task, TaskPriority, UpdateTask, UpdateTaskStatus}
import api.tasks.services.{AddNoteError, BulkTaskService, ChangePriorityError, ChangeStatusTypeError, CreateTaskError, DeleteBatchTaskError, GetAllTaskForUserError, ParamValidationError, TaskAccessError, TaskReplySentiment, TaskService, TaskUuid, UpdateTaskStatusWithTaskIds}
import api.team_inbox.service.{ReplySentimentService, ReplySentimentUuid}
import play.api.libs.json.{JsError, JsSuccess, Json, Writes}
import play.api.mvc.{Action, AnyContent, BaseController, ControllerComponents}
import utils.{Help<PERSON>, SRLogger}
import utils.StringUtilsV2
import io.lemonlabs.uri.Url
import sr_scheduler.models.ChannelType
import utils.Helpers.{getNextLinkForConvThread, getPrevLinkForConvThread}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}


case class NavigationLinksCntrl(
                                 prev: Option[String],
                                 next: Option[String]
                               )

object NavigationLinksCntrl {
  given writes: Writes[NavigationLinksCntrl] = Json.writes[NavigationLinksCntrl]
}

case class TaskPaginationResults(
                                  tasks: List[Task],
                                  links: NavigationLinksCntrl
                                )

object TaskPaginationResults {
  given writes: Writes[TaskPaginationResults] = Json.writes[TaskPaginationResults]
}

class TaskController(
                      protected val controllerComponents: ControllerComponents,
                      permissionUtils: PermissionUtils,
                      taskService: TaskService,
                      taskUtils: TaskUtils,
                      replySentimentService: ReplySentimentService,
                      bulkTaskService: BulkTaskService
                    ) extends BaseController {

  private implicit val ec: ExecutionContext = controllerComponents.executionContext

  def createTask(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_TASKS,
    tidOpt = tid,
    localOrStagingApiTestAccess = true
  ).async(parse.json) { request =>

    val Res = request.Response
    given logger: SRLogger = request.Logger
    val ta = request.actingTeamAccount
    val permittedAccountIdsForEditingTasks = request.permittedAccountIds

    if (ta.isEmpty) {
      Future.successful(Res.BadRequestError(s"Invalid team id"))
    } else {
      request.body.validate[NewTask] match {

        case JsError(e) =>

          Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

        case JsSuccess(data, _) =>

          val permittedAccountIdsForEditProspects = PermissionMethods.getPermittedAccountIdsForAccountAndPermission(
            loggedinAccount = request.loggedinAccount,
            actingAccountId = ta.get.user_id,
            actingTeamId = ta.get.team_id,
            version = v,
            Logger = logger
          )(PermType.EDIT_PROSPECTS)

          // additional edit prospect permission -
          // intent = Edit prospect while searching prospects

          val task = taskService.createTaskWithProspectValidation(
            task_data = data,
            creatorAccountId = ta.get.user_id,
            teamId = ta.get.team_id,
            permittedAccountIdsForEditTask = permittedAccountIdsForEditingTasks,
            permittedAccountIdsForEditProspect = permittedAccountIdsForEditProspects // rename permittedAccountIdsForEditingTasks
          )

          task.map {

            case Left(CreateTaskError.MightBeDuplicateTaskError(err)) =>
              logger.error(s"createTask Error, might be duplicate task error  :", err)
              Res.ServerError(err)

            case Left(CreateTaskError.DueDateIsOutOfBoundsError(status)) =>

              logger.error(s"createTask, due date provided was far away in future : ${status.due_at}")

              Res.BadRequestError(s"Please keep due date under 180 days")

            case Left(CreateTaskError.AssigneeDontHavePermissionForEditTask) =>

              Res.BadRequestError(s"Assignee doesn't have edit task permission ")

            case Left(CreateTaskError.LinkedinConnectionRequestAddNoteTooLong) =>

              Res.BadRequestError(s"Linkedin connection request message body shouldn't be more than ${AppConfig.MultiChannel.linkedin_connection_request_add_note_character_limit} characters")

            case Left(CreateTaskError.LinkedinInMailBodyTooLong) =>
              Res.BadRequestError(s"Linkedin in-mail message body shouldn't be more than ${AppConfig.MultiChannel.linkedin_in_mail_message_body_character_limit} characters ")


            case Left(CreateTaskError.LinkedinInMailSubjectTooLong) =>
              Res.BadRequestError(s"Linkedin in-mail subject shouldn't be more than ${AppConfig.MultiChannel.linkedin_in_mail_subject_character_limit} characters ")


            case Left(CreateTaskError.LinkedinMessageBodyTooLong) =>
              Res.BadRequestError(s"Linkedin message body shouldn't be more than ${AppConfig.MultiChannel.linkedin_message_body_character_limit} characters ")


            case Left(CreateTaskError.WhatsAppMessageBodyTooLong) =>
              Res.BadRequestError(s"Whatsapp  message body shouldn't be more than ${AppConfig.MultiChannel.whatsapp_message_body_character_limit} characters ")

            case Left(CreateTaskError.LinkedinAccountNotActive) =>
              logger.error(s"Linkedin account is not active for campaign: ${data.campaign_id.get} :: teamId: ${ta.get.team_id}")
              Res.BadRequestError("Linkedin account is not active")

            case Left(CreateTaskError.DontHavePermissionToCreateTaskForProspect) =>
              Res.ForbiddenError("You Don't have permission to create task for the prospect.")

            case Left(CreateTaskError.FailedToValidateProspect(e)) =>
              Res.ServerError("Failed to validate the prospect", Some(e))

            case Left(CreateTaskError.ErrorWhileCreatingTask) =>
              Res.BadRequestError("Not able to create task")

            case Right(data) =>
              Res.Success("Task Created Successfully", data = Json.obj("task_id" -> data))
          }.recover {
            case e => Res.ServerError(e)
          }
      }
    }

  }

  def getTasks(v: String,
               tid: Option[Long],
               older_than: Option[Long],
               newer_than: Option[Long],
               page_size: Option[Int]
              ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_TASKS,
    tidOpt = tid,
    localOrStagingApiTestAccess = true
  ).async { request =>

    val Res = request.Response

    val query = request.uri
    val parsedQueryParams: Map[String, Vector[String]] = StringUtilsV2.getParams(query)

    given Logger: SRLogger = request.Logger
    val ta = request.actingTeamAccount
    val tz = request.loggedinAccount.timezone
    val paIds = request.permittedAccountIds

    val assigneeAccessCheck = TaskService.assigneeValidation(
      parsedParams = parsedQueryParams,
      permittedAccountIds = request.permittedAccountIds.toList
    )
    val validatedParams = TaskService.validateParams( // Todo : Add unit test for validateParams
      parsedParams = parsedQueryParams
    )

    assigneeAccessCheck match {
      case Left(ParamValidationError.ParamValidationFailed(msg)) =>
        Future.successful(Res.BadRequestError(s"Invalid Assignee id: ${msg}"))

      case Right(ids) =>

        validatedParams match {
          case Failure(e) =>
            Future.successful(Res.BadRequestError(s"Invalid Params :${e.getMessage}")
            )

          case Success(searchTask) =>

            TaskService.paginationValidation( // Todo : move three logics to service layer
              timeBasedTaskType = searchTask.time_based_task_type,
              older_than = older_than,
              newer_than = newer_than,
              page_size = page_size,
              duration_from = searchTask.duration_from,
              duration_to = searchTask.duration_to,
              timeZone = tz.getOrElse("UTC")
            ) match {

              case Left(ParamValidationError.ParamValidationFailed(msg)) =>
                Future.successful(Res.BadRequestError(s"Error : $msg"))

              case Right(validatedTaskReq) =>

                taskService.getAllTasksForUser(
                  isFirst = validatedTaskReq._1,
                  orgId = OrgId(request.loggedinAccount.org.id),
                  team_id = ta.get.team_id,
                  validatedTaskReq = validatedTaskReq._2,
                  searchTask = searchTask.copy(assignee_ids = Some(ids)),
                  timeZone = tz.getOrElse("UTC"),
                  doNotFetchAutomatedDueTasks = true,
                  permittedAccountIds = paIds
                ).map { result =>
                  result match {
                    case Left(GetAllTaskForUserError.ServerError(e)) =>
                      Res.ServerError(e) //  Sending server error

                    case Left(GetAllTaskForUserError.ErrorWhileGettingTasks) =>
                      Res.BadRequestError("Error While Getting Tasks")

                    case Right(taskPagination) =>


                      val uri = Url.parse(request.uri)
                      val page_data = taskPagination.links

                      val next_link = getNextLinkForConvThread(uri, page_data)
                      val prev_link = getPrevLinkForConvThread(uri, page_data)

                      val resp = TaskPaginationResults(
                        tasks = taskPagination.data,
                        links = NavigationLinksCntrl(
                          prev = prev_link, next = next_link
                        )
                      )

                      Res.Success(
                        "Success",
                        data = Json.toJson(resp)
                      )

                  }
                }
            }
        }
    }
  }


  def changeStatus(v: String, taskID: String, tid: Option[Long]) = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_TASKS,
    tidOpt = tid,
    localOrStagingApiTestAccess = true
  ) andThen taskUtils.hasTask(taskIds = List(taskID))).async(parse.json) { request =>

    val Res = request.Response
    val teamId = request.actingTeamAccount.team_id
    val paIds = request.permittedAccountIds
    given Logger: SRLogger = request.Logger

    request.body.validate[UpdateTaskStatus] match {
      case JsError(e) =>
        Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

      case JsSuccess(data, _) =>

        taskService.changeStatus(
          task_id = taskID,
          orgId = OrgId(id = request.loggedinAccount.org.id),
          changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
            doer = Some(request.loggedinAccount.internal_id),
            permittedAccountIds = paIds,
          ),
          task_status = data,
          team_id = teamId,
        ).map {

          case Left(ChangeStatusTypeError.ErrorWhileChangingStatus) =>
            Res.BadRequestError("Error while changing Status")

          //          case Right(None) =>
          //            Res.NotFoundError("Task not found")

          case Right(task_id) =>

            Res.Success(
              "Success",
              data = Json.obj(
                "task_id" -> task_id
              )
            )
        }.recover {
          case e => Res.ServerError(e)
        }
    }
  }


    def changeBulkTaskStatus(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
        version = v,
        permission = PermType.EDIT_TASKS,
        tidOpt = tid,
        localOrStagingApiTestAccess = true
    ).async(parse.json) { request =>

        val Res = request.Response
        val teamId = request.actingTeamAccount.get.team_id
        val paIds = request.permittedAccountIds
        given Logger: SRLogger = request.Logger

        request.body.validate[UpdateTaskStatusWithTaskIds] match {
            case JsError(e) =>
                Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

            case JsSuccess(data, _) =>
                    bulkTaskService.bulkChangeTaskStatus(
                          doer = request.loggedinAccount.internal_id,
                          task_ids = data.task_ids,
                          orgId = OrgId(id = request.loggedinAccount.org.id),
                          task_status = data.status,
                          team_id = teamId,
                          permittedAccountIds = paIds,
                      ).map {
                          case Left(TaskAccessError.AccessNotFound) =>
                              Res.NotFoundError("Task not found")

                          case Left(TaskAccessError.NoAccessForAllTask(invalidTasks)) =>
                              Res.BadRequestError("You can only do bulk actions on tasks that are owned by you. To ensure this use the filter to select Assignee and then choose the tasks.")

                          case Right(_) =>
                          Res.Success(
                              message = "Success"
                          )
                      }
                      .recover {
                          case e => Res.ServerError(e)
                      }

        }
    }

  def changePriority(v: String, taskID: String, tid: Option[Long]) = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_TASKS,
    tidOpt = tid
  ) andThen taskUtils.hasTask(taskIds = List(taskID))).async(parse.json) { request =>

    val Res = request.Response
    val teamId = request.actingTeamAccount.team_id
    val paIds = request.permittedAccountIds
    given Logger: SRLogger = request.Logger

    request.body.validate[TaskPriority] match {
      case JsError(e) =>
        Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

      case JsSuccess(data, _) =>


        taskService.changePriority(
          task_id = taskID,
          task_priority = data,
          team_id = teamId, permittedAccountIds = paIds
        ).map {

          case Left(ChangePriorityError.ErrorWhileChangingPriority) =>
            Res.BadRequestError("Error while changing priority")

          case Right(None) =>
            Res.NotFoundError("Task not found")

          case Right(Some(task_id)) =>

            Res.Success(
              "Success",
              data = Json.obj(
                "task_id" -> task_id
              )
            )
        }.recover {
          case e => Res.ServerError(e)
        }
    }
  }

  def addNote(v: String, taskID: String, tid: Option[Long]) = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_TASKS,
    tidOpt = tid
  ) andThen taskUtils.hasTask(taskIds = List(taskID))).async(parse.json) { request =>

    val Res = request.Response
    val teamId = request.actingTeamAccount.team_id
    val paIds = request.permittedAccountIds
    given Logger: SRLogger = request.Logger

    val note = (request.body \ "note").asOpt[String]

    note match {
      case None =>
        Future.successful(Res.BadRequestError("No note found"))

      case Some(value) =>

        taskService.addNote(
          task_id = taskID,
          note = value,
          team_id = teamId,
          permittedAccountIds = paIds
        ).map {

          case Left(AddNoteError.ErrorWhileAddingNote) =>
            Res.BadRequestError("error while adding note")

          case Right(None) =>
            Res.NotFoundError("Task not found")

          case Right(Some(task_id)) =>

            Res.Success(
              "Success",
              data = Json.obj(
                "task_id" -> task_id
              )
            )
        }.recover {
          case e => Res.ServerError(e)
        }
    }
  }

  def deleteTask(v: String, taskID: String, tid: Option[Long]) = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.DELETE_TASKS,
    tidOpt = tid,
    localOrStagingApiTestAccess = true
  ) andThen taskUtils.hasTask(taskIds = List(taskID))).async(parse.json) { request =>

    val Res = request.Response
    val teamId = request.actingTeamAccount.team_id
    val paIds = request.permittedAccountIds
    given logger: SRLogger = request.Logger

    taskService.deleteTaskFromFrontend(
        taskId = taskID,
        team_id = teamId,
        permittedAccountIds = paIds
      ).map { data =>
        /*case Left(DeleteTaskError.ErrorWhileDeletingFromPg(e)) =>

          logger.error(s"Error while Deleting the taskId ->  ${Helpers.getStackTraceAsString(e)}")

          Res.ServerError(err = e)

        case Left(DeleteTaskError.TaskNotFoundWhileDeleting) =>

          logger.fatal(s"Delete Task Error: This shouldn't have been possible -> ${taskID}")

          Res.NotFoundError("Task not found")
          */
        data.taskId match {
          case None =>
            logger.fatal(s"Delete Task Error: This shouldn't have been possible -> ${taskID}")

            Res.NotFoundError("Task not found")

          case Some(taskId) =>

            Res.Success("Success",
              data = Json.obj(
                "task_id" -> taskId.id
              ))
        }

      }
      .recover {
        case e =>
          Res.ServerError(e)
      }

  }

  def updateTask(v: String, taskID: String, tid: Option[Long]) = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_TASKS,
    tidOpt = tid
  ) andThen taskUtils.hasTask(taskIds = List(taskID))).async(parse.json) { request =>

    val Res = request.Response
    given Logger: SRLogger = request.Logger
    val paIds = request.permittedAccountIds

    request.body.validate[UpdateTask] match {

      case JsError(e) =>

        Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

      case JsSuccess(data, _) =>

        val task = taskService.updateTask(
          update_task_data = data,
          taskId = request.task.head.task_id,
          teamId = request.actingTeamAccount.team_id,
          permittedAccountIds = paIds,
          orgId = OrgId(id = request.loggedinAccount.org.id),
          changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
              doer = Some(request.loggedinAccount.internal_id),
              permittedAccountIds = paIds,
          )
        )
        task.map {
          case None =>
            Res.NotFoundError("Task not found")

          case Some(task_id) =>
            Res.Success("Task updated Successfully",
              data = Json.obj(
                "task_id" -> task_id)
            )
        }.recover {
          case e => Res.ServerError(e)
        }
    }

  }


  def deleteBatchTasks(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.DELETE_TASKS,
    tidOpt = tid
  ).async(parse.json) { request =>

    val Res = request.Response
    val teamId = request.actingTeamAccount.get.team_id
    val paIds = request.permittedAccountIds

    given logger: SRLogger = request.Logger

    val taskIds = (request.body \ "task_ids").as[List[String]]

    taskService.deleteBatchTasks(
        taskIds = taskIds,
        teamId = teamId,
        orgId = OrgId(id = request.loggedinAccount.org.id),
        permittedAccountIds = paIds
      ).map {

        case Left(DeleteBatchTaskError.BatchDeleteLimitError) =>
          logger.error(s"Batch Delete Stopped as TaskIds size > 100")

          Res.BadRequestError(s"Please provide less than 100 tasks to Delete")

        case Left(DeleteBatchTaskError.DontHaveAccessError(task_ids)) =>

          logger.error(s"TeamId ->  ${teamId} don't have access to these taksIds -> ${task_ids}")

          Res.BadRequestError(s"Don't have access to Following task_ids -> ${task_ids}")
        case Right(task_ids) =>

          Res.Success("Success",
            data = Json.obj(
              "task_ids" -> task_ids
            ))
      }
      .recover {
        case e =>
          Res.ServerError(e)
      }
  }

  // 22-Jan-2024 SCYLLA_COMMENTED_OUT
  //GET /api/:v/scylla/count/xKEA3EbPYBaLpJnEqL3AL89JBEGu3k             api.tasks.controllers.TaskController.checkScyllaCount(v: String)
  //  def checkScyllaCount(v: String):Action[AnyContent] = Action.async {
  //    given Logger: SRLogger= new SRLogger("TaskController.checkScyllaCount")
  //
  //    taskService.checkScyllaCount()
  //      .map{res => Ok(s"$res")
  //      }
  //      .recover{
  //        case e => Ok("Connection failed")
  //      }
  //  }

  def getTasksCount(v: String, tid: Option[Long], assignee_id: Option[Long], task_priority: Option[String], campaign_id: Option[Long], reply_sentiment_uuid: Option[String], duration_from: Option[Long], duration_to: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_TASKS,
    tidOpt = tid,
    localOrStagingApiTestAccess = true
  ).async { request =>

    val Res = request.Response
    given Logger: SRLogger = request.Logger
    val ta = request.actingTeamAccount
    val paIds = request.permittedAccountIds

    val query = request.uri
    val parsedQueryParams: Map[String, Vector[String]] = StringUtilsV2.getParams(query)
    val task_priority: List[TaskPriority] = TaskService.getPriorityValidation(parsedParams = parsedQueryParams)

    val replySentimentUuidOpt = reply_sentiment_uuid.map(uuid => ReplySentimentUuid(uuid = uuid))

    if (ta.isEmpty) {

      Future.successful(Res.BadRequestError(s"Invalid team id"))

    } else {
      if (Helpers.assigneeValidation(assigneeId = assignee_id,
        permittedAccountIds = paIds)) {

        Future.successful(Res.BadRequestError("Access not available"))

      } else {

        taskService.getTaskFilterCount(
            team_id = ta.get.team_id,
            orgId = OrgId(id = request.loggedinAccount.org.id),
            assignee_id = assignee_id,
            reply_sentiment_uuid_opt = replySentimentUuidOpt,
            task_priority = task_priority,
            campaign_id = campaign_id,
            duration_from = duration_from,
            duration_to = duration_to,
            timeZone = request.loggedinAccount.timezone.getOrElse("UTC"),
            doNotFetchAutomatedDueTasks = true,
            permittedAccountIds = paIds // Todo - validate assignee ids in validation
          ).map { result =>
            Res.Success(
              "Success",
              data = Json.obj(
                "task_count" -> result
              )
            )
          }
          .recover {
            case e => Res.ServerError(e)
          }
      }
    }
  }


  def getTaskByTaskId(v: String, tid: Option[Long], taskID: String) = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_TASKS,
    tidOpt = tid
  ) andThen taskUtils.hasTask(taskIds = List(taskID))).async { request =>

    val Res = request.Response

    if(request.task.isEmpty){
        Future.successful(Res.NotFoundError("No task found"))
    }
    else {
        val task: Task = request.task.head
        Future.successful(Res.Success(
            "Success",
            data = Json.obj(
                "task" -> task
            )
        ))
    }

  }

  def addReplySentimentForTask(v: String, taskID: String, tid: Option[Long]) = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_TASKS,
    tidOpt = tid,
    localOrStagingApiTestAccess = true
  ) andThen taskUtils.hasTask(taskIds = List(taskID))).async(parse.json) { request =>

    val Res = request.Response
    given logger: SRLogger = request.Logger
    val permitted_account_ids = request.permittedAccountIds

    val validate = request.body.validate[TaskReplySentiment]

    //    logger.info(s"\n\nvalidate: ${validate}\n\n")

    validate match {
      case JsError(e) =>
        Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

      case JsSuccess(data, _) =>
        if (data.reply_sentiment_uuid.uuid.isEmpty) {
          Future.successful(Res.BadRequestError(s"Empty Reply Sentiment sent"))
        } else {

//        taskService.addReplySentimentForTask(
//          campaignId = data.campaign_id,
//          accountId = AccountId(id = request.loggedinAccount.internal_id),
//          teamId = TeamId(id = tid.get),
//          taskUuid = TaskUuid(uuid = taskID),
//          replySentimentUuid = data.reply_sentiment_uuid,
//          prospect_id = data.prospect_id,
//          call_sp_sid = data.call_sp_sid,
//          conference_uuid = data.conference_uuid,
//          task_type = data.task_type
//        ) match {
//
//            case Success(res) =>
//
//              Future.successful(Res.Success(
//                "Successfully added reply sentiment",
//                data = Json.obj(
//                  "response" -> res
//                )
//              ))
//
//            case Failure(err) =>
//              Future.successful(Res.ServerError(err))
//          }

            replySentimentService.publishReplySentimentToMq(
                campaignId = data.campaign_id,
                accountId = AccountId(id = request.loggedinAccount.internal_id),
                teamId = TeamId(id = tid.get),
                taskUuid = TaskUuid(uuid = taskID),
                replySentimentUuid = data.reply_sentiment_uuid,
                permitted_account_ids = permitted_account_ids,
                prospect_id = data.prospect_id,
                call_sp_sid = data.call_sp_sid,
                conference_uuid = data.conference_uuid,
                task_type = data.task_type
            ) match {
                case Failure(e) =>

                    Future.successful(Res.ServerError(e.getMessage, e = Some(e)))

                case Success(_) =>

                    Future.successful(Res.Success("Reply sentiment is being saved ", Json.obj("saved" -> true)))

            }
        }
    }
  }


}
