
// 22-Jan-2024 SCYLLA_COMMENTED_OUT
//package api.tasks.scylladb.dao
//
//import api.tasks.models.Task
//import api.tasks.scylladb.model.TaskCache
//import org.joda.time.DateTime
//import play.api.libs.json.{JsError, JsSuc<PERSON>, <PERSON>son}
//import utils.SRLogger
//
//import scala.concurrent.{ExecutionContext, Future}
//
//sealed trait GetTaskError
//
//object GetTaskError{
//
//  case object TaskNotFoundInCache extends GetTaskError
//
//  case object JsValidationErrorFromCache extends GetTaskError
//
//}
//
//class TaskCacheDAO (
//                     taskScyllaCacheDAO: TaskScyllaCacheDAO
//                   ) {
//
//  def createNewTaskInCache(created_task: Task)
//                          (implicit ec: ExecutionContext, logger: SRLogger):Future[Int]= {
//
//    taskScyllaCacheDAO.updateTask(
//      taskData = TaskCache(
//        task_id = created_task.task_id,
//        task_json = Json.toJson(created_task).toString(),
//        created_at = DateTime.now()
//      )
//    )
//    // use this
//    //Future.failed(new Exception("CQL Error while creating the task in cache"))
//  }
////
//  def findTaskInCache(taskId: String)
//                     (implicit ec: ExecutionContext, logger: SRLogger):Future[Either[GetTaskError,Task]]= {
//
//
//    taskScyllaCacheDAO.getByTaskId(
//      task_id = taskId
//    ).map {
//      case Some(task_cache) =>
//        //            Use this to test failure condition -
//        //            val validateData = Json.parse("{}").validate[Task] // task_cache.task_json
//        val validateData = Json.parse(task_cache.task_json).validate[Task]
//
//        validateData match {
//
//          case JsError(e) =>
//            logger.fatal(s"Js validation error while parsing data ${e}")
//
//            //Res.JsValidationError(errors = e, requestBody = Some(request.body))
//
//            Left(GetTaskError.JsValidationErrorFromCache)
//
//          case JsSuccess(task, _) =>
//
//            Right(task)
//        }
//      case None =>
//
//
//        Left(GetTaskError.TaskNotFoundInCache)
//    }
//
//  }
//
//  def updateTask(task_data: TaskCache
//                    )(implicit ec: ExecutionContext, logger: SRLogger):Future[Int] = {
//
//      taskScyllaCacheDAO.updateTask(
//      taskData = task_data
//    )
//  }
//
//  def deleteTask(
//                taskId: String
//                )(implicit ec: ExecutionContext,
//                  logger: SRLogger):Future[Int] = {
//
//    taskScyllaCacheDAO.deleteTask(
//      taskId = taskId
//    )
//  }
//
//  def batchDeleteTasks(
//                      taskIds: List[String]
//                      )(implicit ec: ExecutionContext,
//                        logger: SRLogger): Future[Int] = {
//    taskScyllaCacheDAO.batchDeleteTasks(
//      taskIds = taskIds
//    )
//  }
//
//  def batchFetchTasks(
//                        taskIds: List[String]
//                      )(implicit ec: ExecutionContext,
//                        logger: SRLogger): Future[List[TaskCache]] = {
//    taskScyllaCacheDAO.batchFetchTasks(
//      taskIds = taskIds
//    )
//  }
//
//  def checkScyllaCount()(implicit ec: ExecutionContext, Logger: SRLogger):Future[Int] = {
//    taskScyllaCacheDAO.checkScyllaCount()
//  }
//
//  def truncateTaskCacheTable()(implicit ec: ExecutionContext, Logger: SRLogger): Future[Int] = {
//    taskScyllaCacheDAO.truncateTable()
//  }
//
//  def updateTtlValueOfTasks(ttl: Int)(implicit  ec: ExecutionContext, Logger: SRLogger): Future[Int] = {
//    taskScyllaCacheDAO.updateTtlValueOfTable(
//      ttl
//    )
//  }
//
//
//}
