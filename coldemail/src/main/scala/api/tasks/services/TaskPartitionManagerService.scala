package api.tasks.services

import api.tasks.models.PartitionedParentTableName
import api.tasks.pgDao.TaskPgDAO
import utils.{Help<PERSON>, SRLogger}

import scala.concurrent.{ExecutionContext, Future}

class TaskPartitionManagerService (
                                   taskPgDAO: TaskPgDAO
                                   ) {


  def moveToArchive(retention_time : String,
                    except_status : List[String]
                   )(implicit ec: ExecutionContext):Future[Int] = {

    taskPgDAO.moveToArchive(
      retention_time = retention_time,
      except_status = except_status
    )

  }

  /*
  Date: 29 Dec 2023, We came across an issue related with child partition creation.

  The issue came as default partition tables had data in them which also belonged to newly created child
  partition  therefore new partition weren't created.

  we came across a solution by running a procedure `partition_data_proc` which is responsible for moving data
  out from default partition to temporary partition. so now when new child partitions are created data from
  temporary tables get moved into child tables. [ by running  `run_maintenance_proc` ]

  this was helpful : https://github.com/pgpartman/pg_partman/issues/273

  -----

  10-jan 2024:

  after fixing previous issue, we got an issue related to new child partitions were not being created.
  Therefore we raised an issue on partman side and from there response we implemented another fix to fill the
  gap between child partitions using partition_gap_fill() function.

  please go through this to know more : https://github.com/pgpartman/pg_partman/issues/605

  also basecamp discussion : https://3.basecamp.com/5674686/buckets/34274386/messages/6911507057

  we are also trying to restrict user creating new task very far in future current restrictions were 29 days
  as our premake is 30 days, i.e., we always have 30 days pre-created child partitions so for doubling security
  restricted the user to only 29 days.

   */
  def partition_maintenance(
                             retention_time : String,
                             except_status: List[String]
                           )(implicit ec: ExecutionContext, logger: SRLogger) = {
    for {
      movedToArchive: Int <- moveToArchive(retention_time = retention_time,
        except_status = except_status
      )

      count_older_partition_rows:Int <- taskPgDAO.checkIfOldPartitionsAreEmpty(retention_time = retention_time,
        except_status = except_status)

      // Below check is to stop for-comprehension when older partitions aren't empty
      // as on linke 43 we tried to move data to archive but on checking they are still present
      // as count is not equal to 0
      if(count_older_partition_rows == 0)

      get_partitioned_table_names: List[PartitionedParentTableName] <- taskPgDAO.getPartitionedTableNames()

      // here we don't want the below partition related functions to execute as there are no partition
      // tables are present. so failing the future right here that no parent table was found.
      if(get_partitioned_table_names.nonEmpty)

      move_data_out_of_default: List[Boolean] <- Future.sequence(
        get_partitioned_table_names.map(parent_table => taskPgDAO.moveDataToTemporaryTableFromDefaultPartition(
        parent_table = parent_table
      ))
      )

      callPartitionManager:Boolean <- {
          taskPgDAO.callPartitionManager()
      }

      // fill partition table gaps
      fill_partition_gap: List[(PartitionedParentTableName, Int)] <- Future.sequence(
        get_partitioned_table_names.map(parent_table => {

          taskPgDAO.partitionGapFill(
            parent_table = parent_table
          )

        })
      )

    }yield{

      logger.info(s"partition_maintenance : filled-partition-gap : ${fill_partition_gap}")

      movedToArchive
    }
  }

}
