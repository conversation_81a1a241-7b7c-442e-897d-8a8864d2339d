package api.tasks.services

//import api.AppConfig

import api.AppConfig
import api.accounts.EmailScheduledIdOrTaskId.TaskId
import api.accounts.{Account, AccountDAO, EmailScheduledIdOrTaskId, TeamId}
import api.accounts.models.AccountId
import api.campaigns.services.CampaignId
import api.accounts.models.OrgId
import api.accounts.service.AccountOrgBillingRelatedService
import api.call.models.{CallSID, ConferenceUuid}
import api.campaigns.models.CampaignStepType
import api.captain_data.CaptainDataJobUID
import api.linkedin.models.{CD<PERSON>obUid, CaptainDataWebhookResponse, CaptainDataWorkflowStatus}
import api.sr_audit_logs.models.EventType
import api.tasks.models.ChangeStatusPermissionCheck
import api.tasks.models.TaskType.{AutoEmailMagicContent, ManualEmailMagicContent}
import api.tasks.pgDao.DateRangeBetween
import api.tasks.pgDao.{DateRangeBetween, TaskDetailsToProcessWebhookUpdate}
//import api.campaigns.CampaignProspectDAO
//import api.campaigns.models.CampaignStepType
import api.prospects.{CreateProspectEventDB, ExactIdToCompareTime, InferredQueryTimeline, ProspectEventDAO}
import api.prospects.dao.{ProspectDAO, ProspectIdAndPotentialDuplicateProspectId}
//import api.campaigns.services.CampaignId
import api.emails.models.CampaignProspectStepScheduleEventType
import api.emails.{CampaignProspectStepScheduleLogData, CampaignProspectStepScheduleLogsDAO, OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId}
import api.linkedin.models.{LinkedinSettingId, LinkedinSettingUuid, LinkedinAccountSettings}
import api.linkedin.LinkedinSettingDAO
import api.phantombuster.{PhantomBusterContainerId, PhantomBusterContainerOutput, RunningLinkedinTaskDetails, TaskDetailsForPhantomBusterWebhook}
import api.prospects.models.{ProspectId, ProspectTouchedType, StepId}
import api.tasks.models.{CommonLinkedinTaskDetails, LinkedinTaskToBeGrouped, NewTask, RevertData, SubTaskType, Task, TaskCreatedVia, TaskData, TaskPriority, TaskStatus, TaskStatusType, TaskType, TasksGroupedByTypeAndLinkedinSetting, TimeBasedTaskType, UpdateTask, UpdateTaskStatus}
import api.tasks.pgDao.{TaskPgDAO, ValidatedTaskReq}
//import utils.dateTime.SrDateTimeUtils
import utils.dbutils.{DBUtils, DbAndSession}

import scala.util
//import api.tasks.scylladb.dao.{GetTaskError, TaskCacheDAO}
//import api.tasks.scylladb.model.TaskCache
import api.team_inbox.service.ReplySentimentUuid
//import eventframework.services.SrEventService
import org.joda.time.DateTime
import play.api.libs.json.{JsError, JsNumber, JsResult, JsString, JsSuccess, JsValue, Json, Reads}
import scalikejdbc.DBSession
//import scalikejdbc.{NoExtractor, SQL, SQLSyntax, scalikejdbcSQLInterpolationImplicitDef}
import sr_scheduler.models.ChannelType
import utils.{Helpers, SRLogger}
import api.prospects.dao.ProspectAddEventDAO
import utils.random.SrRandomUtils
import utils.uuid.SrUuidUtils

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

case class TaskIdAndType(
                          task_id: String,
                          campaign_id: CampaignId,
                          task_type: TaskType)

case class LinkedinDailyLimitsAndUsage(
                                        viewProfileLimit: Int,
                                        inmailLimit: Int,
                                        messageLimit: Int,
                                        connectionRequestLimit: Int,
                                        viewProfileUsage: Int,
                                        inmailUsage: Int,
                                        messageUsage: Int,
                                        connectionRequestUsage: Int
                                      )

object LinkedinDailyLimitsAndUsage {
  def hasCapacity(dailyLimitsAndUsage: LinkedinDailyLimitsAndUsage, taskType: TaskType)
                 (implicit logger: SRLogger): Boolean = {
    taskType match {
      case TaskType.AutoViewLinkedinProfile => dailyLimitsAndUsage.viewProfileUsage < dailyLimitsAndUsage.viewProfileLimit
      case TaskType.AutoLinkedinInmail => dailyLimitsAndUsage.inmailUsage < dailyLimitsAndUsage.inmailLimit
      case TaskType.AutoLinkedinMessage => dailyLimitsAndUsage.messageUsage < dailyLimitsAndUsage.messageLimit
      case TaskType.AutoLinkedinConnectionRequest => dailyLimitsAndUsage.connectionRequestUsage < dailyLimitsAndUsage.connectionRequestLimit
      case TaskType.SendLinkedinConnectionRequest |
           TaskType.SendEmail |
           TaskType.GeneralTask |
           TaskType.SendLinkedinMessage |
           TaskType.SendSms |
           TaskType.CallTask |
           TaskType.SendWhatsAppMessage |
           TaskType.SendLinkedinInMail |
           TaskType.ViewLinkedinProfile |
           TaskType.AutoEmailMagicContent |
           TaskType.ManualEmailMagicContent =>
        logger.shouldNeverHappen(s"hasCapacity :: IMPOSSIBLE taskType: ${taskType.toKey} found in LinkedinDailyLimitsAndUsage")
        false // Unknown task type, reject
    }
  }
}

sealed trait CreateTaskError

object CreateTaskError {

  case class MightBeDuplicateTaskError(err: Throwable) extends CreateTaskError

  case object ErrorWhileCreatingTask extends CreateTaskError

  case class FailedToValidateProspect(e: Throwable) extends CreateTaskError

  case class DueDateIsOutOfBoundsError(status: TaskStatus.Due) extends CreateTaskError

  case object DontHavePermissionToCreateTaskForProspect extends CreateTaskError

  case object AssigneeDontHavePermissionForEditTask extends CreateTaskError

  case object LinkedinMessageBodyTooLong extends CreateTaskError

  case object LinkedinConnectionRequestAddNoteTooLong extends CreateTaskError

  case object LinkedinInMailBodyTooLong extends CreateTaskError

  case object LinkedinInMailSubjectTooLong extends CreateTaskError

  case object WhatsAppMessageBodyTooLong extends CreateTaskError

  case object LinkedinAccountNotActive extends CreateTaskError


}

sealed trait GetAllTaskForUserError

object GetAllTaskForUserError {

  case object ErrorWhileGettingTasks extends GetAllTaskForUserError

  case class ServerError(e: Throwable) extends GetAllTaskForUserError

}

sealed trait ChangeStatusTypeError

object ChangeStatusTypeError {

  case object ErrorWhileChangingStatus extends ChangeStatusTypeError

}

sealed trait ChangePriorityError

object ChangePriorityError {

  case object ErrorWhileChangingPriority extends ChangePriorityError

}

sealed trait AddNoteError

object AddNoteError {

  case object ErrorWhileAddingNote extends AddNoteError

}

sealed trait DeleteTaskError

object DeleteTaskError {

  case class ErrorWhileDeletingFromPg(
                                       e: Throwable
                                     ) extends DeleteTaskError


  case object TaskNotFoundWhileDeleting extends DeleteTaskError

}

case class TaskReplySentiment(
                               campaign_id: Option[CampaignId],
                               prospect_id: ProspectId,
                               reply_sentiment_uuid: ReplySentimentUuid,
                               task_type: TaskType,
                               call_sp_sid: Option[CallSID],
                               conference_uuid: Option[ConferenceUuid]
                               //                               task_uuid: TaskUuid
                             )

object TaskReplySentiment {

  given reads: Reads[TaskReplySentiment] = Json.reads[TaskReplySentiment]

}

case class DeleteAndUpdateTaskResultForMergeDuplicates(
                                                        deletedTasksForProspects: List[ProspectId],
                                                        updatedTasksForProspects: List[ProspectId]
                                                      )


class TaskDaoService(
                      //                      srEventService: SrEventService,
                      taskPgDAO: TaskPgDAO,
                      //                      srDateTimeUtils: SrDateTimeUtils,
                      srRandomUtils: SrRandomUtils,
                      // 22-Jan-2024 SCYLLA_COMMENTED_OUT
                      //                      taskCacheDAO: TaskCacheDAO,
                      srUuidUtils: SrUuidUtils,
                      //                      prospectDAO: ProspectDAO,
                      accountOrgBillingRelatedService: AccountOrgBillingRelatedService,
                      //                      campaignProspectDAO: CampaignProspectDAO,
                      prospectAddEventDAO: ProspectAddEventDAO,
                      accountDAO: AccountDAO,
                      dbUtils: DBUtils,
                      campaignProspectStepScheduleLogsDAO: CampaignProspectStepScheduleLogsDAO,
                      linkedinSettingDAO: LinkedinSettingDAO
                    ) {

  def updateEmailSubjectAndBody(subject: String, body: String, prospect_id: ProspectId, step_id: StepId, campaignId: CampaignId) = {

    taskPgDAO.updateEmailAndSubjectBody(
      subject = subject,
      body = body,
      prospect_id = prospect_id,
      step_id = step_id,
      campaign_id = campaignId
    )

  }


  def findTaskById(
                    taskId: String,
                    teamId: Long,
                    orgId: OrgId,
                    permittedAccountIds: Seq[Long],
//                    emailNotCompulsoryEnabled: Boolean
                  )(implicit ec: ExecutionContext
                  ): Try[Option[Task]] = {

    taskPgDAO.findTaskById(
      taskId = taskId,
      teamId = teamId,
      orgId = orgId,
      permittedAccountIds = permittedAccountIds,
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    )

  }

  def checkIfTaskAlreadyExists(
                                step_id: StepId,
                                teamId: TeamId,
                                prospectId: ProspectId,
                                is_magic_step: Boolean
                              )(using logger: SRLogger): Try[false] = {

    taskPgDAO.checkIfTaskAlreadyExists(
      step_id = step_id,
      prospect_id = prospectId,
      team_id = teamId,
      is_magic_step = is_magic_step
    ) match {

      case Failure(err) =>

        logger.error(s"Error happened while checkIfTaskAlreadyExists  step_id: ${step_id} prospect_id: ${prospectId} team_id: ${teamId} ", err)

        Failure(err)

      case Success(None) =>

        Success(false) // false means tasks doesn't exists

      case Success(Some(task_id)) =>

        logger.fatal(s"Prevented_duplicate_task_insertion step_id: ${step_id} prospect_id: ${prospectId} team_id: ${teamId} already existing task_id: ${task_id}")

        Failure(new Exception(s"checkIfTaskAlreadyExists  Task exists : ${task_id} for step_id: ${step_id}  prospect_id: ${prospectId} team_id: ${teamId}"))

    }

  }

  //Only used in migration
  def deleteDueTasksByCampaignIdAndProspectId(
                                               campaignId: CampaignId,
                                               prospectId: ProspectId,
                                               teamId: TeamId
                                             ): Try[Int] = {

    taskPgDAO.deleteDueTasksByCampaignIdAndProspectId(
      campaignId = campaignId,
      prospectId = prospectId,
      teamId = teamId
    )

  }

  def createTask(
                  task_data: NewTask,
                  accountId: Long,
                  teamId: Long,
                )(
                  implicit ec: ExecutionContext,
                  logger: SRLogger
                ): Future[Either[CreateTaskError, String]] = {

    val createdAt = DateTime.now()

    val newTaskUuid = srUuidUtils.generateTaskUuid()

    val createdTask = for {

      check_if_tasks_already_exists: Boolean <- {

        if (task_data.step_id.isDefined && task_data.prospect_id.isDefined) {

          Future.fromTry(checkIfTaskAlreadyExists(
            step_id = StepId(id = task_data.step_id.get),
            teamId = TeamId(id = teamId),
            prospectId = ProspectId(id = task_data.prospect_id.get),
            is_magic_step = (task_data.task_type == AutoEmailMagicContent || task_data.task_type == ManualEmailMagicContent)
          ))

        } else {

          Future.successful(false) // false means tasks doesn't exists

        }

      }

      new_task: String <- taskPgDAO.createNewTask(
        task_data = task_data,
        taskId = newTaskUuid,
        account_id = accountId,
        team_id = teamId,
        created_at = createdAt
      )
      addingScheduleLog <- Future.fromTry {
        if (task_data.campaign_id.isDefined && task_data.prospect_id.isDefined && task_data.step_id.isDefined) {
          campaignProspectStepScheduleLogsDAO.insert(
            data = Seq(CampaignProspectStepScheduleLogData(
              campaignProspectStepScheduleEventType = CampaignProspectStepScheduleEventType.Scheduled,
              emailScheduledIdOrTaskId = TaskId(new_task),
              team_id = TeamId(teamId),
              c_id = CampaignId(task_data.campaign_id.get),
              p_id = ProspectId(task_data.prospect_id.get),
              step_id = task_data.step_id.get.toString,
              step_type = Some(TaskType.getCampaignStepType(task_data.task_type))
            ))
          ) match {
            case Success(value) => Success(value)
            case Failure(exception) =>
              logger.shouldNeverHappen(s"campaignProspectStepScheduleLogsDAO.insert failed", Some(exception))
              Success(List())
          }
        } else Success(List())
      }

      acc: Option[String] <- Future.fromTry(accountDAO.getAccountNameById(accId = AccountId(id = accountId)))

      assigneeAcc: Option[String] <- Future.fromTry(accountDAO.getAccountNameById(accId = AccountId(id = task_data.assignee_id.get)))

      _: List[Long] <- Future.fromTry {
        val eventType: EventType = task_data.created_via match {

          case TaskCreatedVia.Scheduler => EventType.TASK_CREATED

          case TaskCreatedVia.Call_action => EventType.INTERNAL_TASK_CREATED

          case TaskCreatedVia.Manual => EventType.TASK_CREATED

          case TaskCreatedVia.AiGenerated => EventType.INTERNAL_TASK_CREATED
        }

        prospectAddEventDAO.addEvents(Seq(
          CreateProspectEventDB(
            event_type = eventType,

            doer_account_id = Some(accountId),
            doer_account_name = acc,

            // for assigned team member
            assigned_to_account_id = task_data.assignee_id,
            assigned_to_account_name = assigneeAcc,

            // changed category
            old_category = None,
            new_category = None,

            prospect_id = task_data.prospect_id.get,
            email_thread_id = None,

            campaign_id = task_data.campaign_id,
            campaign_name = task_data.campaign_name,

            step_id = task_data.step_id,
            step_name = task_data.step_label,

            clicked_url = None,

            account_id = accountId,
            team_id = teamId,
            email_scheduled_id = None,

            created_at = DateTime.now(),

            task_type = Some(task_data.task_type),
            channel_type = Some(task_data.task_type.channelType),
            task_uuid = Some(new_task),
            call_conference_uuid = None,

            duplicates_merged_at = None,
            total_merged_prospects = None,
            potential_duplicate_prospect_id = None

          )
        ))
      }
      // 22-Jan-2024 SCYLLA_COMMENTED_OUT
      //      createdEvent: SrEventObject <- {
      //
      //        srEventService
      //          .createEvent(
      //            eventData = SrEventData (
      //              `object` = SrResource.TaskResource(
      //                object_data = new_task
      //              )
      //            ),
      //            eventType = SrEventType.TaskCreated
      //          )
      //      }


    } yield {
      new_task
    }

    createdTask.map(
      res => {
        Right(res)
      }
    ).recover(err => {

      logger.fatal(s"[TaskDAOService] Error while creating task team_id: ${teamId} accountId: ${accountId} ", err)

      Left(CreateTaskError.MightBeDuplicateTaskError(err))

    })
  }


  def findBatchTasksAndAddtoScylla(
                                    taskIds: List[String],
                                    teamId: Long,
                                    orgId: OrgId,
                                    changeStatusPermissionCheck: ChangeStatusPermissionCheck
//                                    emailNotCompulsoryEnabled: Boolean
                                  )(implicit ec: ExecutionContext, logger: SRLogger): Future[List[Task]] = {
    if (taskIds.isEmpty) {
      Future.successful(List())
    } else {
      for {
        tasksNotFoundInScylla: List[Task] <- taskPgDAO.findBatchTasksFromDB(
          taskIds = taskIds,
          teamId = teamId,
          changeStatusPermissionCheck = changeStatusPermissionCheck,
          orgId = orgId,
//          emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
        )
        // 22-Jan-2024 SCYLLA_COMMENTED_OUT
        //      insertTasksToScylla: List[Int] <- Future.sequence(tasksNotFoundInScylla
        //        .map { task =>
        //        taskCacheDAO.updateTask(
        //          TaskCache(task_id = task.task_id,
        //            task_json = Json.toJson(task).toString(),
        //            created_at = task.created_at)
        //        ).map(res => 0)
        //          .recover {
        //            case e =>
        //              logger.fatal(s"Scylla Action Error: Error While inserting Task ${task}")
        //              0
        //          }
        //      })

      } yield {
        tasksNotFoundInScylla
      }
    }


  }

  def findLatestCampaignFromWhichLinkedinTaskIsDoneForProspect(
                                                                prospectId: ProspectId,
                                                                teamId: TeamId
                                                              ): Try[Option[CampaignId]] = {

    taskPgDAO.findLatestCampaignFromWhichLinkedinTaskIsDoneForProspect(
      prospectId = prospectId,
      teamId = teamId
    )

  }

  def findLastContactedProspectByLinkedinChannel(
                                                  prospectIds: List[ProspectId],
                                                  channelType: ChannelType,
                                                  teamId: TeamId
                                                ): Try[Option[ProspectId]] = {

    taskPgDAO.findLastContactedProspectByChannel(
      prospectIds = prospectIds,
      channelType = channelType,
      teamId = teamId
    )

  }

  def findBatchTasksFromScyllaAndPg(
                                     taskIds: List[String],
                                     team_id: Long,
                                     changeStatusPermissionCheck: ChangeStatusPermissionCheck,
                                     orgId: OrgId,
//                                     emailNotCompulsoryEnabled: Boolean
                                   )(
                                     implicit ec: ExecutionContext, logger: SRLogger
                                   ): Future[List[Task]] = {

    findBatchTasksAndAddtoScylla(
      taskIds = taskIds,
      teamId = team_id,
      orgId = orgId,
      changeStatusPermissionCheck = changeStatusPermissionCheck
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled

    )
    // 22-Jan-2024 SCYLLA_COMMENTED_OUT
    //    for {
    //      tasksFoundInScylla: List[TaskCache] <- taskCacheDAO.batchFetchTasks(
    //        taskIds = taskIds
    //      ).recover{
    //        case e => logger.fatal("scala")
    //          List[TaskCache]()
    //      }
    //
    //      tasksFoundAndValid: List[Task] <- Future.sequence(tasksFoundInScylla.map{task_cache => {
    //        val parsed̦Task: Future[Task] = Json.parse(task_cache.task_json).validate[Task] match {
    //
    //          case JsError(e) =>
    //            logger.fatal(s"Js validation error while parsing data ${e}")
    //
    //            for {
    //              taskfoundfromPg: Task <- taskPgDAO.findTaskbyTaskID(
    //                taskId = task_cache.task_id,
    //                teamId = team_id,
    //                permittedAccountIds = permittedAccountIds
    //              ).map(_.get)
    //
    //              insertTaskInScylla: Int <- taskCacheDAO.updateTask(
    //                TaskCache(task_id = taskfoundfromPg.task_id,
    //                  task_json = Json.toJson(taskfoundfromPg).toString(),
    //                  created_at = taskfoundfromPg.created_at)
    //              ).map(res => 0)
    //                .recover {
    //                  case e =>
    //                    logger.fatal(s"Scylla Action Error: Error While inserting Task ${task_cache}")
    //                    0
    //                }
    //
    //            } yield {
    //              taskfoundfromPg
    //            }
    //
    //
    //          case JsSuccess(task, _) =>
    //
    //            Future.successful(task)
    //
    //        }
    //
    //        parsed̦Task
    //
    //      }})
    //
    //      totalTasksFromScyllaAndPg: List[Task] <- if(taskIds.size == tasksFoundAndValid.size) Future.successful(tasksFoundAndValid) else {
    //        val taskFoundFromScyllaTaskIds = tasksFoundAndValid.map(_.task_id)
    //        val notFoundTasks = taskIds diff taskFoundFromScyllaTaskIds
    //        val leftOverTasksFromPg = findBatchTasksAndAddtoScylla(
    //          taskIds = notFoundTasks,
    //          teamId = team_id,
    //          permittedAccountIds = permittedAccountIds
    //        )
    //        leftOverTasksFromPg.map(res => res ++ tasksFoundAndValid)
    //      }
    //    }yield{
    //
    //      totalTasksFromScyllaAndPg
    //
    //    }
  }

  def fetchDueAutoLinkedinTasks(
                                 linkedinSettingUuid: LinkedinSettingUuid,
                                 teamId: TeamId,
                                 orgId: OrgId
                               )(implicit ec: ExecutionContext, logger: SRLogger): Future[List[TasksGroupedByTypeAndLinkedinSetting]] = {


    for {
      // Get LinkedIn settings with daily limits
      linkedinSettingsOpt: Option[LinkedinAccountSettings] <- Future.fromTry(linkedinSettingDAO.findLinkedinAccountByIdOrUuid(
        id = None,
        uuid = Some(linkedinSettingUuid.uuid),
        teamId = teamId
      ))

      // Get today's completed task counts by type
      todayUsage: Map[TaskType, Int] <- taskPgDAO.countTodayCompletedLinkedinTasksByType(
        linkedinSettingUuid = linkedinSettingUuid,
        teamId = teamId
      )

      // Get due tasks
      taskIdList: List[TaskIdAndType] <- taskPgDAO.fetchDueAutoLinkedinTasks(
        linkedinSettingUuid = linkedinSettingUuid,
        teamId = teamId
      )

      result: List[TasksGroupedByTypeAndLinkedinSetting] <- {
        linkedinSettingsOpt match {
          case None =>
            // No LinkedIn settings found, return empty
            logger.error(s"Linkedin Setting not found for uuid: ${linkedinSettingUuid.uuid}")
            Future.successful(List())

          case Some(linkedinSettings: LinkedinAccountSettings) =>
            // Create daily limits and usage object
            val dailyLimitsAndUsage = LinkedinDailyLimitsAndUsage(
              viewProfileLimit = linkedinSettings.view_profile_limit_per_day,
              inmailLimit = linkedinSettings.inmail_limit_per_day,
              messageLimit = linkedinSettings.message_limit_per_day,
              connectionRequestLimit = linkedinSettings.connection_request_limit_per_day,
              viewProfileUsage = todayUsage.getOrElse(TaskType.AutoViewLinkedinProfile, 0),
              inmailUsage = todayUsage.getOrElse(TaskType.AutoLinkedinInmail, 0),
              messageUsage = todayUsage.getOrElse(TaskType.AutoLinkedinMessage, 0),
              connectionRequestUsage = todayUsage.getOrElse(TaskType.AutoLinkedinConnectionRequest, 0)
            )

            if (taskIdList.nonEmpty) {
              processTasksWithDailyLimits(
                taskIdList = taskIdList,
                dailyLimitsAndUsage = dailyLimitsAndUsage,
                linkedinSettingUuid = linkedinSettingUuid,
                teamId = teamId,
                orgId = orgId
              )
            } else {
              Future.successful(
                List(TasksGroupedByTypeAndLinkedinSetting(
                  commonLinkedinTaskDetails = CommonLinkedinTaskDetails(
                    teamId = teamId,
                    linkedinSettingUuid = linkedinSettingUuid.uuid,
                    taskType = TaskType.AutoLinkedinConnectionRequest,
                    orgId = orgId
                  ),
                  taskIds = Seq()
                ))
              )
            }
        }
      }
    } yield result
  }

  /*
   - Process tasks with daily limits checking. Ensures only one task per campaign is processed,
    but tries different task types if the first one exceeds daily limits.
   */
  private def processTasksWithDailyLimits(
                                           taskIdList: List[TaskIdAndType],
                                           dailyLimitsAndUsage: LinkedinDailyLimitsAndUsage,
                                           linkedinSettingUuid: LinkedinSettingUuid,
                                           teamId: TeamId,
                                           orgId: OrgId
                                         )(implicit ec: ExecutionContext, logger: SRLogger): Future[List[TasksGroupedByTypeAndLinkedinSetting]] = {

    // Group tasks by campaign and take only the nearest due task from each campaign
    val tasksByCampaign = taskIdList.groupBy(_.campaign_id)

    // For each campaign, check if it has active tasks in CaptainData
    // and filter out campaigns that have active tasks
    Future.sequence(
      tasksByCampaign.map { case (campaignId, campaignTasks) =>
        Future.fromTry(
          taskPgDAO.hasAnyActiveCampaignTasksInCaptainData(
            campaignId = campaignId,
            teamId = teamId
          )
        ).map(hasActiveTasks => (campaignId, campaignTasks, hasActiveTasks))
      }
    ).flatMap(campaignTasksWithStatus => {
      // Filter out campaigns with active tasks and apply daily limits
      val eligibleCampaignTasks = campaignTasksWithStatus
        .filter { case (_, _, hasActiveTasks) => !hasActiveTasks }
        .flatMap { case (campaignId, campaignTasks, _) =>
          // For each campaign, find the first task type that has available capacity
          campaignTasks.find(task => LinkedinDailyLimitsAndUsage.hasCapacity(dailyLimitsAndUsage, task.task_type))
        }

      if (eligibleCampaignTasks.nonEmpty) {
        // Take limited number of tasks as per config
        val selectedTasks = eligibleCampaignTasks.take(AppConfig.CaptainData.taskPerPickupPerAccount)

        // Group them by task type to maintain type safety
        val tasksByType = selectedTasks.groupBy(_.task_type)

        taskPgDAO.updateStatusOfAutoLinkedinTasks(
            taskIds = selectedTasks.toList.map(_.task_id),
            teamId = teamId
          )
          .map(_ => {
            tasksByType.map { case (taskType, tasks) =>
              TasksGroupedByTypeAndLinkedinSetting(
                commonLinkedinTaskDetails = CommonLinkedinTaskDetails(
                  teamId = teamId,
                  linkedinSettingUuid = linkedinSettingUuid.uuid,
                  taskType = taskType,
                  orgId = orgId
                ),
                taskIds = tasks.map(_.task_id).toSeq
              )
            }.toList
          })
      } else {
        Future.successful(List())
      }
    })
  }

  def getLinkedinSettingUuidFromPhantomBusterContainerId(
                                                          containerId: PhantomBusterContainerId,
                                                          teamId: TeamId
                                                        )(implicit ec: ExecutionContext): Future[LinkedinSettingUuid] = {

    taskPgDAO.getLinkedinSettingUuidFromPhantomBusterContainerId(
        containerId = containerId,
        teamId = teamId
      )
      .flatMap {
        case None =>
          Future.failed(new Exception(s"No Task found with running container of id: ${containerId.id}."))

        case Some(uuid) =>
          Future.successful(uuid)
      }

  }

  def findDueTaskbyTaskIDAndTeamId(
                                    taskId: String,
                                    teamId: TeamId,
                                    orgId: OrgId,
//                                    emailNotCompulsoryEnabled: Boolean
                                  )(implicit ec: ExecutionContext): Future[Task] = {

    taskPgDAO.findDueTaskbyTaskIDAndTeamId(
        taskId = taskId,
        teamId = teamId,
        orgId = orgId,
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
      )
      .flatMap {
        case None => Future.failed(new Exception(s"${taskId} is deleted or not in due state."))
        case Some(task) => Future.successful(task)
      }

  }

  def updateStatusOfAllRunningLinkedinTasks(
                                             containerId: PhantomBusterContainerId,
                                             teamId: TeamId,
                                             status: TaskStatus
                                           )(implicit ec: ExecutionContext,
                                             logger: SRLogger): Future[List[AccountId]] = {

    taskPgDAO.updateStatusOfAllRunningLinkedinTasks(
      containerId = containerId,
      teamId = teamId,
      status = status
    )

  }

  def updateRunningLinkedinTaskStatusByProspectLinkedinUrl(
                                                            containerId: PhantomBusterContainerId,
                                                            teamId: TeamId,
                                                            linkedinProfileUrl: String,
                                                            status: TaskStatus
                                                          )(implicit ec: ExecutionContext,
                                                            logger: SRLogger): Future[Option[TaskDetailsForPhantomBusterWebhook]] = {

    taskPgDAO.updateRunningLinkedinTaskStatusByProspectLinkedinUrl(
      containerId = containerId,
      teamId = teamId,
      linkedinProfileUrl = linkedinProfileUrl,
      status = status
    )

  }

  def failLinkedinTasksWhichAreStuck()(implicit ec: ExecutionContext): Future[List[String]] = {

    taskPgDAO.failLinkedinTasksWhichAreStuck()

  }

  def fetchFailedTasksOfCampaign(
                                  campaignId: CampaignId,
                                  teamId: TeamId,
                                  orgId: OrgId,
//                                  emailNotCompulsoryEnabled: Boolean
                                ): Try[List[Task]] = {

    taskPgDAO.fetchFailedTaskOfCampaign(
      campaignId = campaignId,
      teamId = teamId,
      orgId = orgId,
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    )

  }

  def moveTasksToDueOnSessionCookieUpdate(
                                           linkedinSettingUuid: String,
                                           teamId: TeamId
                                         ): Try[Int] = {

    taskPgDAO.fetchTasksToBeMarkedDueOnLinkedinSessionCookieUpdate(
        linkedinSettingUuid = linkedinSettingUuid,
        teamId = teamId
      )
      .map(tasksList => {
        tasksList.map(task => {
            taskPgDAO.updateTaskStatus(
                status = TaskStatus.Due(
                  due_at = DateTime.now()
                ),
                taskId = task.taskId,
                teamId = teamId
              )
              .get
          })
          .sum
      })

  }

  def getLinkedinUrlOfProspectByTaskId(
                                        taskId: String,
                                        teamId: TeamId
                                      )(implicit ec: ExecutionContext): Future[Option[String]] = {

    taskPgDAO.getLinkedinProfileUrlOfProspectByTaskId(
      taskId = taskId,
      teamId = teamId
    )

  }

  def failTask(
                taskId: String,
                teamId: TeamId,
                failureReason: String
              )(implicit ec: ExecutionContext): Future[Int] = {

    taskPgDAO.failTask(
      taskId = taskId,
      teamId = teamId,
      failureReason = failureReason
    )

  }


  def updateContainerIdForTasks(
                                 taskIds: Seq[String],
                                 containerId: PhantomBusterContainerId,
                                 teamId: TeamId
                               )(implicit ec: ExecutionContext): Future[Int] = {

    taskPgDAO.updateContainerIdForTasks(
      taskIds = taskIds,
      containerId = containerId,
      teamId = teamId
    )

  }

  def updateCaptainDataTaskStatusToInitiate(
                                   taskId: String,
                                   jobUid: CaptainDataJobUID,
                                   teamId: TeamId
                                 )(implicit ec: ExecutionContext): Future[Int] = {
    taskPgDAO.updateCaptainDataTaskStatusToInitiate(
      taskId = taskId,
      jobUid = jobUid,
      teamId = teamId
    )
  }

  //NOT USED
  //  def fetchLinkedinTasksInPhantomBusterQueue()(implicit ec: ExecutionContext): Future[List[RunningLinkedinTaskDetails]] = {
  //
  //    taskPgDAO.fetchLinkedinTasksInPhantomBusterQueue()
  //
  //  }

  def deleteTask(taskId: String, teamId: TeamId)(implicit ec: ExecutionContext, Logger: SRLogger): Future[OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId] = Future.fromTry {

    deleteTaskForRevert(
      taskIds = List(taskId),
      team_id = teamId,
      status = None,
      permittedAccountIds = Seq()
    ).map(_.headOption.getOrElse(
      OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId(
        campaignId = None,
        prospectId = None,
        emailSettingId = None,
        team_id = teamId,
        taskId = None,
        step_id = None
      )
    ))

  }


  def getAllTasksForCount(
                           team_id: Long,
                           orgId: OrgId,
                           assignee_id: Option[Long],
                           campaign_id: Option[Long],
                           reply_sentiment_uuid_opt: Option[ReplySentimentUuid],
                           task_priority: List[TaskPriority],
                           doNotFetchAutomatedDueTasks: Boolean,
                           dateRange: Option[DateRangeBetween],
                           permittedAccountIds: Seq[Long],
                           //                           emailNotCompulsoryEnabled: Boolean
                         )(
                           implicit ec: ExecutionContext,
                           logger: SRLogger
                         ): Future[List[Task]] = {
    taskPgDAO.getTasksForCount(
      team_id = team_id,
      org_id = orgId,
      assignee_id = assignee_id,
      campaign_id = campaign_id,
      reply_sentiment_uuid_opt = reply_sentiment_uuid_opt,
      task_priority = task_priority,
      doNotFetchAutomatedDueTasks = doNotFetchAutomatedDueTasks,
      permittedAccountIds = permittedAccountIds,
      dateRange = dateRange
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    )
  }

  def getAtSameTimeTasksFromDB(
                                team_id: Long,
                                orgId: OrgId,
                                exactlyAtTaskId: ExactIdToCompareTime,
                                timeZone: String,
                                assignee_ids: Option[List[Long]],
                                campaign_ids: Option[List[CampaignId]],
                                reply_sentiment_uuids: Option[List[ReplySentimentUuid]],
                                task_type: Option[List[String]],
                                task_status: Option[List[String]],
                                task_priority: Option[List[String]],
                                timeBasedTaskType: Option[TimeBasedTaskType],
                                doNotFetchAutomatedDueTasks: Boolean,
                                permittedAccountIds: Seq[Long],
//                                emailNotCompulsoryEnabled: Boolean
                              )(implicit ec: ExecutionContext, logger: SRLogger): Future[Either[GetAllTaskForUserError, List[Task]]] = {


    val exactlyAtQuery = TaskPgDAO.getAllTaskQuery(
      teamId = team_id,
      orgId = orgId,
      time_based_task_type = timeBasedTaskType,
      timeZone = timeZone,
      validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqExact(
        timeline = InferredQueryTimeline.Exact(exactlyAtTaskId)
      ),
      assignee_ids = assignee_ids,
      campaign_ids = campaign_ids,
      reply_sentiment_uuids = reply_sentiment_uuids,
      task_type = task_type,
      task_status = task_status,
      task_priority = task_priority,
      doNotFetchAutomatedDueTasks = doNotFetchAutomatedDueTasks,
      permittedAccountIds = permittedAccountIds,
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    )

    for {
      taskIds <- taskPgDAO.getTaskIdsForUserV2(
        query = exactlyAtQuery
      )

      tasks <- findBatchTasksFromScyllaAndPg(
        taskIds = taskIds,
        team_id = team_id,
        changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
          permittedAccountIds = permittedAccountIds,
          doer = None
        ),
        orgId = orgId,
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
      )
    } yield {
      Right(tasks)
    }
  }


  def getAllTaskForUserV2(
                           team_id: Long,
                           orgId: OrgId,
                           timeZone: String,
                           validatedTaskReq: ValidatedTaskReq,
                           reply_sentiment_uuids: Option[List[ReplySentimentUuid]],
                           assignee_ids: Option[List[Long]],
                           campaign_ids: Option[List[CampaignId]],
                           task_type: Option[List[String]],
                           task_status: Option[List[String]],
                           task_priority: Option[List[String]],
                           timeBasedTaskType: Option[TimeBasedTaskType],
                           doNotFetchAutomatedDueTasks: Boolean,
                           permittedAccountIds: Seq[Long],
//                           emailNotCompulsoryEnabled: Boolean
                         )(
                           implicit ec: ExecutionContext,
                           logger: SRLogger
                         ): Future[Either[GetAllTaskForUserError, List[Task]]] = {

    val query = TaskPgDAO.getAllTaskQuery(
      time_based_task_type = timeBasedTaskType,
      teamId = team_id,
      orgId = orgId,
      timeZone = timeZone,
      validatedTaskReq = validatedTaskReq,
      assignee_ids = assignee_ids,
      reply_sentiment_uuids = reply_sentiment_uuids,
      campaign_ids = campaign_ids,
      task_type = task_type,
      task_status = task_status,
      doNotFetchAutomatedDueTasks = doNotFetchAutomatedDueTasks,
      task_priority = task_priority,
      permittedAccountIds = permittedAccountIds,
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    )

    if(team_id == 20848){

      logger.doNotTruncate(s"[task_issue] getAllTaskForUserV2 : Query :: ${query} permittedAccountIds: ${permittedAccountIds} :" +
        s" doNotFetchAutomatedDueTasks :: ${doNotFetchAutomatedDueTasks} : timeBasedTaskType ::${timeBasedTaskType}" +
        s"task_priority : ${task_priority} task_type:: ${task_type} campaign_ids: ${campaign_ids} assignee_ids: ${assignee_ids}" +
        s"reply_sentiment_uuids: ${reply_sentiment_uuids} : timeZone:: ${timeZone} : orgId:: ${orgId} : team_id::${team_id} task_status:: ${task_status}" +
//        s"emailNotCompulsoryEnabled:: ${emailNotCompulsoryEnabled}" +
        s" :  validatedTaskReq:: ${validatedTaskReq}")

    }

    for {
      taskIds <- taskPgDAO.getTaskIdsForUserV2(
        query = query
      )

      tasks <- findBatchTasksFromScyllaAndPg(
        taskIds = taskIds,
        team_id = team_id,
        changeStatusPermissionCheck= ChangeStatusPermissionCheck.ManualTaskCheck(
          permittedAccountIds = permittedAccountIds,
          doer = None
        ),
        orgId = orgId,
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
      )

    } yield {
      Right(tasks)
    }
  }

  /*def getAllTasksForUser(
                          team_id: Long,
                          assignee_ids: Option[List[Long]],
                          task_type: Option[List[String]],
                          task_status: Option[List[String]],
                          task_priority: Option[List[String]],
                          permittedAccountIds: Seq[Long]
                        )(
    implicit ec: ExecutionContext,
    logger: SRLogger
  ):Future[List[Task]] = {

    for {

      tasks: List[Task] <- taskPgDAO.getTasksForUser(
      team_id = team_id,
      assignee_ids = assignee_ids,
      task_type = task_type,
      task_status = task_status,
        task_priority = task_priority,
        permittedAccountIds = permittedAccountIds
    )

      taskCache: List[TaskCache] <- Future.successful(tasks.map( task =>
        TaskCache(task_id = task.task_id,
          task_json = Json.toJson(task).toString(),
          created_at = task.created_at)
      ))

      insertTaskIntoCache: List[Int] <- Future.sequence(taskCache.map(taskCache =>
        taskCacheDAO.updateTask(task_data = taskCache)
          .map(rs => 0)
          .recover{
            case e => logger.fatal(s"Scylla Action: Error while updating taskCache, taskCache->  ${taskCache}")
                      0
          }
      ))

    }yield{
      tasks
    }
  }
  */

  // delete from cache when implementing this
  def deleteTask(
                  taskId: String,
                  team_id: Long,
                  permittedAccountIds: Seq[Long]
                )(implicit ec: ExecutionContext,
                  logger: SRLogger
                ): Future[OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId] = Future.fromTry {

    for {
      deletedTaskIdFromPg: List[OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId] <- deleteTaskForRevert(
        taskIds = List(taskId),
        team_id = TeamId(team_id),
        permittedAccountIds = permittedAccountIds,
        status = None
      )
      // 22-Jan-2024 SCYLLA_COMMENTED_OUT
      //      deleteTaskFromCache:Int <- taskCacheDAO.deleteTask(
      //        taskId = taskId
      //      ).map(res =>res
      //      ).recover {
      //        case e =>
      //          logger.fatal(s"Scylla Action: Error while deleting the task from cache error -> ${e.getMessage}")
      //          0
      //      }
    } yield {
      deletedTaskIdFromPg.headOption.getOrElse(
        OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId(
          campaignId = None,
          prospectId = None,
          emailSettingId = None,
          team_id = TeamId(team_id),
          taskId = None,
          step_id = None
        ))
    }
  }

  // delete from cache when implementing this

  def deleteBatchTasks(
                        taskIds: List[String],
                        teamId: Long,
                        permittedAccountIds: Seq[Long]
                      )(implicit ec: ExecutionContext, logger: SRLogger): Future[List[String]] = {
    for {
      deletedTaskIdsFromPg: List[OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId] <- Future.fromTry {
        deleteTaskForRevert(
          taskIds = taskIds,
          team_id = TeamId(teamId),
          permittedAccountIds = permittedAccountIds,
          status = None
        )
      }

      // 22-Jan-2024 SCYLLA_COMMENTED_OUT
      //      batchDeleteFromScylla: Int <- taskCacheDAO.batchDeleteTasks(
      //        taskIds =  taskIds
      //      ).map{res =>
      //        res
      //      }.recover{
      //        case e =>
      //          logger.fatal(s"Scylla Action Error, failed to delete task ${e.getMessage} ")
      //          0
      //      }

    } yield {

      deletedTaskIdsFromPg.flatMap(_.taskId.map(_.id))
    }
  }

  def updateTask(
                  update_task_data: UpdateTask,
                  taskId: String,
                  teamId: Long,
                  permittedAccountIds: Seq[Long]
                )(
                  implicit ec: ExecutionContext, logger: SRLogger
                ): Future[Option[String]] = {

    for {
      updateTask: Option[String] <- taskPgDAO.updateTask(
        update_task_data = update_task_data,
        task_id = taskId,
        team_id = teamId,
        permittedAccountIds = permittedAccountIds
      )
      // 22-Jan-2024 SCYLLA_COMMENTED_OUT
      //      deleteTaskFromCache: Int <- taskCacheDAO.deleteTask(
      //        taskId = taskId
      //      ).map{res =>
      //        res
      //      }.recover{
      //        case e =>
      //          logger.fatal(s"Scylla Action Error, failed to delete task ${e.getMessage} ")
      //          0
      //      }

    } yield {
      updateTask
    }
  }

  def moveTasksToDue(taskIds: Seq[String], teamId: TeamId)(implicit ec: ExecutionContext): Future[Int] = {

    taskPgDAO.moveTasksToDue(
      taskIds = taskIds,
      teamId = teamId
    )

  }


  def changeStatus(
                    task_id: String,
                    orgId: OrgId,
                    task_status: UpdateTaskStatus,
                    changeTime: DateTime,
                    team_id: Long,
                    changeStatusPermissionCheck: ChangeStatusPermissionCheck,
//                    emailNotCompulsoryEnabled: Boolean
                  )(implicit ec: ExecutionContext,
                    logger: SRLogger
                  ): Future[Either[ChangeStatusTypeError, String]] = {

    val updatedTask = for {

      updated_task: String <- taskPgDAO.changeStatus(
        task_id = task_id,
        task_status = task_status,
        changeTime = changeTime,
        changeStatusPermissionCheck = changeStatusPermissionCheck,
        team_id = team_id
      )

      taskObj: Option[Task] <- taskPgDAO.findTaskbyTaskID(
        taskId = updated_task,
        teamId = team_id,
        changeStatusPermissionCheck = changeStatusPermissionCheck,
        orgId = orgId,
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
      )

      _: Int <- Future.fromTry {
        if (task_status.status_type == TaskStatusType.Done && taskObj.isDefined && taskObj.get.prospect.isDefined) {
          accountOrgBillingRelatedService.checkAndUpdateProspectsContacted(
            prospectId = ProspectId(taskObj.get.prospect.get.id),
            teamId = TeamId(taskObj.get.team_id),
            prospectTouchedType = ProspectTouchedType.TaskDone,
            channelType = taskObj.get.task_type.channelType,
            updateLastContactedAt = true,
            logger = logger,
          )
        } else {
          Success(1)
        }
      }
      
      
      doer: Option[Long] <- Future.successful{
        changeStatusPermissionCheck match {
          case data: ChangeStatusPermissionCheck.ManualTaskCheck => 
            data.doer
          case data: ChangeStatusPermissionCheck.AutomatedTaskCheck =>  
            taskObj.flatMap(t => t.assignee.map(p => p.id))
          case data: ChangeStatusPermissionCheck.NoStatusPermissionCheck => 
            None
        }
      }

      /*
        * doer_account_id and account_id are different, although they point to accounts.id.
        * doer is the person who changed the status.
        * account_id is for logging or report purpose to map the task to the account who created it.
       */
      accountId: Long <- Future.successful{
        changeStatusPermissionCheck match {
          case data: ChangeStatusPermissionCheck.ManualTaskCheck =>
            doer.get // existing logic
          case data: ChangeStatusPermissionCheck.AutomatedTaskCheck =>
            doer.get // existing logic
          case ChangeStatusPermissionCheck.NoStatusPermissionCheck(accountId:AccountId) =>
            accountId.id
        }
      }

      acc: Option[String] <- Future.fromTry(
        if(doer.isDefined){
        accountDAO.getAccountNameById(accId = AccountId(id = doer.get))
        }else{
          Success(None)
        })

      _: List[Long] <- if (taskObj.isDefined) {
        Future.fromTry {

          val et = ProspectEventDAO.getEventFromTaskType(taskObj.get.status.status_type).get

          prospectAddEventDAO.addEvents(Seq(
            CreateProspectEventDB(
              event_type = et,
              doer_account_id = doer,
              doer_account_name = if (taskObj.get.status.status_type.toKey == TaskStatusType.Due.toKey) {
                None
              } else {
                acc
              },
              assigned_to_account_id = taskObj.flatMap(_.assignee.map(_.id)),
              assigned_to_account_name = taskObj.flatMap(_.assignee.map(_.name)),
              old_category = None,
              new_category = None,
              prospect_id = taskObj.get.prospect.get.id,
              email_thread_id = None,
              campaign_id = taskObj.get.campaign_id,
              campaign_name = taskObj.get.campaign_name,
              step_id = taskObj.flatMap(_.step_id),
              step_name = taskObj.flatMap(_.step_label),
              clicked_url = None,
              account_id = accountId,
              team_id = team_id,
              email_scheduled_id = None,
              created_at = ProspectEventDAO.getCreatedAtByTaskType(taskObj = taskObj.get).get,
              task_type = taskObj.map(_.task_type),
              channel_type = taskObj.map(_.task_data.task_type.channelType),
              task_uuid = Some(task_id),
              call_conference_uuid = None,

              duplicates_merged_at = None,
              total_merged_prospects = None,
              potential_duplicate_prospect_id = None
            )
          ))
        }
      } else {
        logger.error("Task was updated successfully ,but failed to add it in prospect events")
        Future.successful(List())
      }

      // 22-Jan-2024 SCYLLA_COMMENTED_OUT
      //      deleteFromCache : Int <- taskCacheDAO.deleteTask(
      //        taskId = task_id
      //      ).map(rs => rs)
      //        .recover{
      //          case e => logger.fatal(s"Scylla ActionError: Failed while deleting task -> ${task_id}")
      //            0
      //        }

      //      createdEvent: SrEventObject <- {
      //
      //        srEventService
      //          .createEvent(
      //            eventData = SrEventData(
      //              `object` = SrResource.TaskResource(
      //                object_data = updated_task
      //              )
      //            ),
      //            eventType = SrEventType.TaskUpdated
      //          )
      //      }


    } yield {
      updated_task
    }


    updatedTask.map(
      res => {

        Right(res)
      }
    )

  }

  def changePriority(
                      task_id: String,
                      task_priority: TaskPriority,
                      team_id: Long,
                      permittedAccountIds: Seq[Long]
                    )(implicit ec: ExecutionContext,
                      logger: SRLogger
                    ): Future[Either[ChangePriorityError, Option[String]]] = {

    val updatedTask = for {

      updated_task: Option[String] <- taskPgDAO.changePriority(
        task_id = task_id,
        task_priority = task_priority,
        team_id = team_id,
        permittedAccountIds = permittedAccountIds
      )

      // 22-Jan-2024 SCYLLA_COMMENTED_OUT
      //      deleteFromCache : Int <- taskCacheDAO.deleteTask(
      //      taskId = task_id
      //      ).map(rs => rs)
      //        .recover{
      //          case e => logger.fatal(s"Scylla ActionError: Failed while deleting task -> ${task_id}")
      //            0
      //        }

      //      createdEvent: SrEventObject <- {
      //
      //        srEventService
      //          .createEvent(
      //            eventData = SrEventData(
      //              `object` = SrResource.TaskResource(
      //                object_data = updated_task
      //              )
      //            ),
      //            eventType = SrEventType.TaskPriorityChanged
      //          )
      //      }


    } yield {
      updated_task
    }

    updatedTask.map(
      res => {

        Right(res)
      }
    )

  }

  def assignOwner() = ???

  def addNote(
               task_id: String,
               note: String,
               team_id: Long,
               permittedAccountIds: Seq[Long]
             )(implicit ec: ExecutionContext,
               logger: SRLogger
             ): Future[Either[AddNoteError, Option[String]]] = {

    val updatedTask = for {

      updated_task: Option[String] <- taskPgDAO.addNote(
        task_id = task_id,
        note = note,
        team_id = team_id,
        permittedAccountIds = permittedAccountIds
      )

      // 22-Jan-2024 SCYLLA_COMMENTED_OUT
      //      deleteFromCache : Int <- taskCacheDAO.deleteTask(
      //        taskId = task_id
      //      ).map(rs => rs)
      //        .recover{
      //          case e => logger.fatal(s"Scylla ActionError: Failed while deleting task -> ${task_id}")
      //            0
      //        }

      //      createdEvent: SrEventObject <- {
      //
      //        srEventService
      //          .createEvent(
      //            eventData = SrEventData(
      //              `object` = SrResource.TaskResource(
      //                object_data = updated_task
      //              )
      //            ),
      //            eventType = SrEventType.TaskNoteUpdated
      //          )
      //      }


    } yield {
      updated_task
    }

    updatedTask.map(
      res => {

        Right(res)
      }
    )

  }

  //  def executeTask() = ???


  def checkBatchTaskAccessByTaskIdAndTeamId(
                                             taskIds: List[String],
                                             teamId: Long,
                                             orgId: OrgId,
                                             permittedAccountIds: Seq[Long],
//                                             emailNotCompulsoryEnabled: Boolean
                                           )(implicit ec: ExecutionContext): Future[List[String]] = {
    taskPgDAO.checkBatchTaskByTaskIdAndTeamIds(
      taskIds = taskIds,
      orgId = orgId,
      teamId = teamId,
      permittedAccountIds = permittedAccountIds,
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    )
  }

  def findSingleTaskFromScyllaAndPG(
                                     taskId: String,
                                     teamId: Long,
                                     permittedAccountIds: Seq[Long],
                                     orgId: OrgId,
//                                     emailNotCompulsoryEnabled: Boolean
                                   )(implicit ec: ExecutionContext,
                                     logger: SRLogger
                                   ): Future[Either[GetAllTaskForUserError, Task]] = {

    // 22-Jan-2024 SCYLLA_COMMENTED_OUT
    //    taskCacheDAO.findTaskInCache( // cache
    //      taskId = taskId
    //    ) flatMap {
    //      case Left(cacheFetchError) =>

    taskPgDAO.findTaskbyTaskID( // main db
      taskId = taskId,
      teamId = teamId,
      changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
        doer = None,
        permittedAccountIds = permittedAccountIds
      ),
      orgId = orgId,
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    ) flatMap {
      case Some(task) =>

        // 22-Jan-2024 SCYLLA_COMMENTED_OUT
        //            cacheFetchError match { // matching over cache error
        //
        //              case GetTaskError.TaskNotFoundInCache =>
        //
        //                taskCacheDAO.createNewTaskInCache(
        //                  created_task = task
        //                )
        //                  .map(
        //                    result =>
        //                      Right(task)
        //                  )
        //                  .recover {
        //                    case e =>
        //                      logger.fatal(s"Error while creating task in Cache ${e.getMessage}  taskId: ${taskId}")
        //                      Right(task)
        //                  }
        //
        //              case GetTaskError.JsValidationErrorFromCache =>
        //
        //                val res = for {
        //
        //                  _: Int <- taskCacheDAO.createNewTaskInCache(
        //                    created_task = task
        //                  )
        //
        //                } yield ()
        //
        //                res
        //                  .map { result =>
        //                    Right(task)
        //                  }
        //                  .recover {
        //                    case e =>
        //                      logger.fatal(s"scylla action error ${e.getMessage} taskId: ${taskId}")
        Future(Right(task))
      //                  }
      //
      //            }
      //
      case None =>
        Future.successful(Left(GetAllTaskForUserError.ErrorWhileGettingTasks))
    }
    //      case Right(task) =>
    //
    //        logger.debug(s"ScyllaDb: Task found in Cache task ->  ${task}")
    //
    //        Future.successful(Right(task))
    //    }
  }

  //NOT USED
  //  def FindTaskByTaskIdAndTeamId(
  //                      taskId: String,
  //                      teamId: Long,
  //                      permittedAccountIds: Seq[Long]
  //                      )(implicit ec: ExecutionContext,
  //                        logger: SRLogger): Future[Option[Task]] = {
  //
  //    taskPgDAO.findTaskbyTaskID(
  //      taskId = taskId,
  //      teamId = teamId,
  //      permittedAccountIds = permittedAccountIds
  //    )
  //
  //  }

  def fetchTaskDataByTaskId(taskId: TaskUuid, teamId: TeamId)(using logger: SRLogger): Try[Option[TaskData]] = {

    taskPgDAO.fetchTaskDataById(
      taskId = taskId,
      teamId = teamId
    )

  }

  def deleteNotCompletedTaskForDeletedCampaign(
                                                campaignId: CampaignId,
                                                teamId: TeamId
                                              )(implicit session: DBSession): Try[List[String]] = {
    taskPgDAO.deleteNotCompletedTaskForDeletedCampaign(campaignId = campaignId, teamId = teamId)
  }
  // 22-Jan-2024 SCYLLA_COMMENTED_OUT
  //  def checkScyllaCount()(implicit ec: ExecutionContext, Logger: SRLogger): Future[Int] = {
  //    taskCacheDAO.checkScyllaCount()
  //  }

  def fetchTaskData(taskId: String, teamId: TeamId)(implicit ec: ExecutionContext): Future[TaskData] = {

    taskPgDAO.fetchTaskData(
        taskId = taskId,
        teamId = teamId
      )
      .flatMap {
        case None =>
          Future.failed(new Exception(s"TaskData not found for $taskId"))

        case Some(taskData) =>
          Future.successful(taskData)
      }

  }

  def fetchTask(
                 taskId: String,
                 team_id: Long,
                 orgId: OrgId,
                 changeStatusPermissionCheck: ChangeStatusPermissionCheck
//                 emailNotCompulsoryEnabled: Boolean
               )
               (implicit ec: ExecutionContext, Logger: SRLogger): Future[List[Task]] = {
    findBatchTasksFromScyllaAndPg(
      taskIds = List(taskId),
      team_id = team_id,
      changeStatusPermissionCheck = changeStatusPermissionCheck,
      orgId = orgId,
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    )
  }

  def getCompletedTasksForProspectInCampaign(
                                              campaign_id: Long,
                                              prospect_id: Long,
                                              orgId: OrgId,
                                              team_id: Long,
                                              permittedAccountIds: Seq[Long],
//                                              emailNotCompulsoryEnabled: Boolean
                                            )(implicit ec: ExecutionContext, Logger: SRLogger): Future[List[Task]] = {

    for {
      completedTaskIds: List[String] <- taskPgDAO.getCompletedTaskIdsForProspectInCampaign(
        campaign_id = campaign_id,
        prospect_id = prospect_id,
        team_id = team_id
      )

      fetchedTasks: List[Task] <- findBatchTasksFromScyllaAndPg(
        taskIds = completedTaskIds,
        team_id = team_id,
        changeStatusPermissionCheck =  ChangeStatusPermissionCheck.ManualTaskCheck(
          permittedAccountIds = permittedAccountIds,
          doer = None
        ),
        orgId = orgId,
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
      )
    } yield {
      fetchedTasks
    }

  }


  def saveSentimentForTask(
                            taskUuid: TaskUuid,
                            teamId: TeamId,
                            replySentimentUuid: ReplySentimentUuid
                          )(implicit session: DBSession, logger: SRLogger): Try[TaskUuid] = {

    taskPgDAO.saveSentimentsForTask(
      taskUuid = taskUuid,
      teamId = teamId,
      replySentimentUuid = replySentimentUuid
    )
  }

  def deleteDuplicateTasks(
                            tasks: List[Task],
                          )(implicit session: DBSession, Logger: SRLogger): Try[List[String]] = {

    // added this check 
    if (tasks.nonEmpty) {
      val team_id = tasks.head.team_id
      val task_ids = tasks.map(_.task_id)

      taskPgDAO.deleteWithSession(
        taskIds = task_ids,
        teamId = TeamId(id = team_id),
        status = None,
        permittedAccountIds = Seq()
      ).map { data =>
        val list = data.flatMap { d =>
          if (d.prospectId.isDefined && d.campaignId.isDefined && d.taskId.isDefined && d.step_id.isDefined) {
            Seq(CampaignProspectStepScheduleLogData(
              campaignProspectStepScheduleEventType = CampaignProspectStepScheduleEventType.Deleted,
              emailScheduledIdOrTaskId = d.taskId.get,
              team_id = d.team_id,
              c_id = d.campaignId.get,
              p_id = d.prospectId.get,
              step_id = d.step_id.get.id.toString,
              step_type = None
            ))
          } else Seq()
        }
        campaignProspectStepScheduleLogsDAO.insert(
          data = list
        ) match {
          case Success(value) => //DO NOTHING
          case Failure(exception) => Logger.shouldNeverHappen(s"Failed campaignProspectStepScheduleLogsDAO.insert", Some(exception))
        }
        data
      }.map(_.flatMap(_.taskId.map(_.id)))

    } else {

      Try {
        List()
      }
    }
  }


  def getGroupDuplicatedTasks()(implicit session: DBSession): Try[List[GroupDuplicateTaskDetails]] = {

    taskPgDAO.groupDuplicateTasks()

  }
  
  def fetchTaskDetailsViaTaskIdAndJobUid(
                                          task_uuid: TaskUuid,
                                          job_uid: CDJobUid
                                        
                                        ): Try[TaskDetailsToProcessWebhookUpdate] = {
    taskPgDAO.fetchTaskDetailsViaTaskIdAndJobUid(
      task_uuid = task_uuid,
      job_uid = job_uid
      
    )
  }

  def handleCaptainDataWebhookStatusUpdate(
                                            data: CaptainDataWebhookResponse,
                                            status: CaptainDataWorkflowStatus)
                                          (using logger: SRLogger): Try[TaskUuid] = {

    taskPgDAO.updateCaptainDataWebhookStatus(data = data, status = status)
    
    

  }

  def findAllTasksRelatedToGroup(
                                  step_id: StepId,
                                  campaignId: CampaignId,
                                  prospectId: ProspectId,
                                  team_id: TeamId
                                )(implicit session: DBSession): Try[List[Task]] = {

    taskPgDAO.fetchAllTasksRealtedToGroupForTestAppOnly(
      step_id = step_id,
      campaignId = campaignId,
      prospectId = prospectId,
      team_id = team_id
    )
  }

  def getTaskDataForProspectRevert(
                                    campaign_id: CampaignId,
                                    prospect_id: ProspectId,
                                    step_id: StepId
                                  )(implicit session: DBSession): Try[Option[RevertData.TaskRevertData]] = {

    taskPgDAO.getTaskDataForProspectRevert(
      campaign_id: CampaignId,
      prospect_id: ProspectId,
      step_id: StepId
    )


  }

  def deleteTaskForRevert(
                           taskIds: List[String],
                           team_id: TeamId,
                           status: Option[TaskStatusType],
                           permittedAccountIds: Seq[Long]
                         )(using Logger: SRLogger): Try[List[OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId]] = {

    taskPgDAO.deleteTaskForRevert(
      taskIds = taskIds,
      teamId = team_id,
      status = status,
      permittedAccountIds = permittedAccountIds
    ).map { data =>
      val list = data.flatMap { d =>
        if (d.prospectId.isDefined && d.campaignId.isDefined && d.taskId.isDefined && d.step_id.isDefined) {
          Seq(CampaignProspectStepScheduleLogData(
            campaignProspectStepScheduleEventType = CampaignProspectStepScheduleEventType.Deleted,
            emailScheduledIdOrTaskId = d.taskId.get,
            team_id = d.team_id,
            c_id = d.campaignId.get,
            p_id = d.prospectId.get,
            step_id = d.step_id.get.id.toString,
            step_type = None
          ))
        } else Seq()
      }
      campaignProspectStepScheduleLogsDAO.insert(
        data = list
      ) match {
        case Success(value) => //DO NOTHING
        case Failure(exception) => Logger.shouldNeverHappen(s"Failed campaignProspectStepScheduleLogsDAO.insert", Some(exception))
      }
      data
    }

  }

  def deleteNotDoneTasksAndUpdateMasterProspectIdDone(
                                                       teamId: TeamId,
                                                       masterProspectId: ProspectId,
                                                       duplicateProspects: List[ProspectIdAndPotentialDuplicateProspectId]
                                                     ): Try[DeleteAndUpdateTaskResultForMergeDuplicates] = {
    val dbAndSession: DbAndSession = dbUtils.startLocalTx()
    val db = dbAndSession.db
    implicit val session: DBSession = dbAndSession.session

    val res: Try[DeleteAndUpdateTaskResultForMergeDuplicates] = for {
      //1. delete tasks of duplicate prospects which are not done
      deletedTasksForProspects: List[ProspectId] <- taskPgDAO.deleteNotDoneTasksDuplicateProspects(
        teamId = teamId,
        prospects = duplicateProspects.map(_.prospectId)
      )

      //2. update master prospect_id for tasks which are done
      updatedTasks: List[ProspectId] <- {
        taskPgDAO.updateMasterProspectForDoneTasks(
          teamId = teamId,
          prospects = duplicateProspects,
          masterProspectId = masterProspectId
        )
      }
    } yield {
      DeleteAndUpdateTaskResultForMergeDuplicates(
        deletedTasksForProspects = deletedTasksForProspects, updatedTasksForProspects = updatedTasks
      )
    }
    dbUtils.commitAndCloseSession(db = db)
    res
  }

  def findMagicContentTaskByDetails(
                                     campaignId: CampaignId,
                                     stepId: StepId,
                                     prospectId: ProspectId,
                                     teamId: TeamId
                                   ): Try[Option[TaskData]] = {
    // Directly call the TaskPgDAO function
    taskPgDAO.findMagicContentTaskByDetails(
      campaignId = campaignId,
      stepId = stepId,
      prospectId = prospectId,
      teamId = teamId
    )
  }

}
