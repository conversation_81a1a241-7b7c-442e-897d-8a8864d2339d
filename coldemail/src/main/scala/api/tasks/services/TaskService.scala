package api.tasks.services

import api.AppConfig
import api.accounts.EmailScheduledIdOrTaskId.TaskId
import api.campaigns.models.{CampaignStepData, CampaignStepType, ChannelSettingSenderDetails, CurrentStepStatusForScheduler, CurrentStepStatusForSchedulerData, PreviousFollowUpData, StepContext}
import api.accounts.{EmailScheduledIdOrTaskId, TeamId}
import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.call.DAO.CallDAO
import api.call.models.{CallSID, CallSidOrConfUuid, ConferenceUuid}
import api.campaigns.services.{CampaignCacheService, CampaignId, CampaignService}
import api.prospects.{CreateProspectEventDB, ExactIdToCompareTime, InferredQueryTimeline, NavigationLinks}
import api.tasks.models.{ChangeStatusPermissionCheck, NewTask, RevertData, SearchTask, SubCount, SubTaskType, Task, TaskCount, TaskCreatedVia, TaskData, TaskPriority, TaskStatus, TaskStatusType, TaskType, TasksGroupedByTypeAndLinkedinSetting, TimeBasedTaskType, UpdateTask, UpdateTaskStatus}
import api.tasks.models.{NewTask, RevertData, SearchTask, SubCount, SubTaskType, Task, TaskCount, TaskCreatedVia, TaskData, TaskPriority, TaskStatus, TaskStatusType, TaskType, TasksGroupedByTypeAndLinkedinSetting, TimeBasedTaskType, UpdateTask, UpdateTaskStatus}
import api.tasks.pgDao
import api.tasks.pgDao.{DateRangeBetween, TaskDetailsToProcessWebhookUpdate, ValidatedTaskReq}
import api.tasks.services.TaskService.{getDueTasks, getSubCountForTaskCount, getTaskPriorityParsed, getTaskStatusParsed, getTodayAndDueTasks, getTodayTasks, getUpcomingTasks}
import utils.{Helpers, SRLogger}
import org.joda.time.{DateTime, DateTimeZone, Months}
import utils.jodatimeutils.JodaTimeUtils
import api.campaigns.{Campaign, CampaignDAO, CampaignProspectDAO, CampaignProspectUpdateScheduleStatus, PreviousFollowUp}
import api.captain_data.CaptainDataJobUID
import api.emails.models.CampaignProspectStepScheduleEventType
import api.emails.{CampaignProspectStepScheduleLogData, CampaignProspectStepScheduleLogsDAO, EmailSetting, OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId}
import api.emails.{EmailSetting, OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId}
import api.linkedin.models.{CDJobUid, CaptainDataWebhookResponse, CaptainDataWorkflowStatus, LinkedinAccountStatus, LinkedinSettingUuid}
import api.phantombuster.{PhantomBusterContainerId, TaskDetailsForPhantomBusterWebhook}
import api.prospects.dao.{ProspectAddEventDAO, ProspectDAO, ProspectIdAndPotentialDuplicateProspectId}
import api.prospects.models.{ProspectId, StepId}
import api.team_inbox.service.{ReplySentimentForTeam, ReplySentimentService, ReplySentimentUuid}
import play.api.libs.json.{JsError, JsResult, JsString, JsSuccess, JsValue, Json, OFormat, OWrites, Reads, Writes}
import sr_scheduler.models.{CampaignDataToAddNextToBeScheduledAt, CampaignDataToAddNextToBeScheduledAtForEmailChannel, ChannelType}
import api.prospects.dao_service.ProspectDAOService
import sr_scheduler.models.ChannelType
import api.prospects.dao_service.{ProspectDAOService, ProspectDAOServiceV2}
import api.sr_audit_logs.models.{CreateEventLogError, EventDataType, EventType}
import api.sr_audit_logs.services.EventLogService
import api.tasks.models.TaskStatusType.{Approved, PendingApproval}
import api.tasks.models.TaskType.{AutoEmailMagicContent, ManualEmailMagicContent}
import api.team_inbox.dao.ReplySentimentDAO
import api.team_inbox.dao_service.ReplySentimentDAOService
import api.team_inbox.model.ReplySentimentType
import api.team_inbox.model.ReplySentimentUpdatedBy.User
import io.smartreach.esp.api.emails.EmailSettingId
import scalikejdbc.DBSession
import utils.dateTime.SrDateTimeUtils
import utils.dbutils.DBUtils
import utils.dependencyinjectionutils.CallDAO_DI
import utils.mq.campaigns.{MqPauseCampaignOnReplySentimentSelect, PauseCampaignData}
import utils.mq.prospect_category.{MqAutoUpdateProspectCategoryMsg, MqAutoUpdateProspectCategoryPublisher}
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

sealed trait DeleteBatchTaskError

object DeleteBatchTaskError {

  case class DontHaveAccessError(
                                  task_ids: List[String]
                                ) extends DeleteBatchTaskError

  case object BatchDeleteLimitError extends DeleteBatchTaskError

}


case class TaskPagination(
                           data: List[Task],
                           links: NavigationLinks
                         )

sealed trait TaskAccessError

object TaskAccessError {
  case object AccessNotFound extends TaskAccessError
  
  case class NoAccessForAllTask(invalidTasks: Int) extends TaskAccessError
}



//sealed trait TaskCampaignRelation
//
//object TaskCampaignRelation {
//
//  case object TaskWithoutCampai
//}

case class TaskUuid(uuid: String) extends AnyVal {
  override def toString: String = uuid
}

object TaskUuid {

  given reads: Reads[TaskUuid] = new Reads[TaskUuid] {
    override def reads(ev: JsValue): JsResult[TaskUuid] = {
      ev match {
        case JsString(uuid) => JsSuccess(TaskUuid(uuid = uuid))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }
  }

  given writes: Writes[TaskUuid] = new Writes[TaskUuid] {
    override def writes(taskUUid: TaskUuid): JsValue = JsString(taskUUid.uuid)
  }
}

class TaskService (
                    taskDaoService: TaskDaoService,
                    campaignProspectDAO: CampaignProspectDAO,
                    campaignCacheService: CampaignCacheService,
                    dbUtils: DBUtils,
                    srDateTimeUtils: SrDateTimeUtils,
                    prospectDAO: ProspectDAO,
                    prospectDAOService: ProspectDAOService,
                    prospectDAOServiceV2: ProspectDAOServiceV2,
                    prospectAddEventDAO: ProspectAddEventDAO,
                    callDAO: CallDAO,
                    replySentimentDAO: ReplySentimentDAO,
                    mqAutoUpdateProspectCategoryPublisher: MqAutoUpdateProspectCategoryPublisher,
                    srRollingUpdateCoreService: SrRollingUpdateCoreService,
                    mqPauseCampaignOnReplySentimentSelect: MqPauseCampaignOnReplySentimentSelect,
                    eventLogService: EventLogService,
                    campaignProspectStepScheduleLogsDAO: CampaignProspectStepScheduleLogsDAO,
                    campaignDAO: CampaignDAO
                  ) {


  def updateSubjectAndBodyOfTask(
                                  subject: String,
                                  body: String,
                                  step_id: StepId,
                                  prospect_id: ProspectId,
                                  campaign_id: CampaignId
                                ): Try[String] = {

    taskDaoService.updateEmailSubjectAndBody(
      subject = subject,
      body = body,
      step_id = step_id,
      prospect_id = prospect_id,
      campaignId = campaign_id
    )

  }


  def findTaskById(
                    taskId: String,
                    teamId: Long,
                    orgId: OrgId,
                    permittedAccountIds: Seq[Long],
                  )(implicit ec: ExecutionContext,
                    Logger: SRLogger
                  ): Try[Option[Task]] = {

    //    val emailNotCompulsoryEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
    //      teamId = TeamId(teamId),
    //      feature = SrRollingUpdateFeature.EmailNotCompulsory
    //    )(Logger)
    taskDaoService.findTaskById(
      taskId = taskId,
      teamId = teamId,
      orgId = orgId,
      permittedAccountIds = permittedAccountIds,
      //      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    )

  }

  def failLinkedinTasksWhichAreStuck()(implicit ec: ExecutionContext): Future[List[String]] = {
    taskDaoService.failLinkedinTasksWhichAreStuck()
  }

  def moveTasksToDue(taskIds: Seq[String], teamId: TeamId)(implicit ec: ExecutionContext): Future[Int] = {

    taskDaoService.moveTasksToDue(
      taskIds = taskIds,
      teamId = teamId
    )
  }

  def fetchDueAutoLinkedinTasks(
                                 linkedinSettingUuid: LinkedinSettingUuid,
                                 teamId: TeamId,
                                 orgId: OrgId
                               )(implicit ec: ExecutionContext, Logger: SRLogger): Future[List[TasksGroupedByTypeAndLinkedinSetting]] = {

    taskDaoService.fetchDueAutoLinkedinTasks(
      linkedinSettingUuid = linkedinSettingUuid,
      teamId = teamId,
      orgId = orgId
    )
  }

  def moveTasksToDueOnSessionCookieUpdate(
                                           linkedinSettingUuid: String,
                                           teamId: TeamId
                                         ): Try[Int] = {
    taskDaoService.moveTasksToDueOnSessionCookieUpdate(
      linkedinSettingUuid = linkedinSettingUuid,
      teamId = teamId
    )
  }

  def fetchFailedTasksOfCampaign(
                                  campaignId: CampaignId,
                                  teamId: TeamId,
                                  orgId: OrgId
                                )(Logger: SRLogger): Try[List[Task]] = {

    //    val emailNotCompulsoryEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
    //      teamId = teamId,
    //      feature = SrRollingUpdateFeature.EmailNotCompulsory
    //    )(Logger)

    taskDaoService.fetchFailedTasksOfCampaign(
      campaignId = campaignId,
      teamId = teamId,
      orgId = orgId,
      //      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    )

  }

  def getLinkedinSettingUuidFromPhantomBusterContainerId(
                                                          containerId: PhantomBusterContainerId,
                                                          teamId: TeamId
                                                        )(implicit ec: ExecutionContext): Future[LinkedinSettingUuid] = {
    taskDaoService.getLinkedinSettingUuidFromPhantomBusterContainerId(
      containerId = containerId,
      teamId = teamId
    )
  }

  def findDueTaskbyTaskIDAndTeamId(
                                    taskId: String,
                                    teamId: TeamId,
                                    orgId: OrgId
                                  )(implicit ec: ExecutionContext, Logger: SRLogger): Future[Task] = {

    //    val emailNotCompulsoryEnabled = srRollingUpdateCoreService
    //      .checkIfTeamCanReceiveEarlyUpdates(
    //        teamId = teamId,
    //        feature = SrRollingUpdateFeature.EmailNotCompulsory
    //      )(Logger)

    taskDaoService.findDueTaskbyTaskIDAndTeamId(
      taskId = taskId,
      teamId = teamId,
      orgId = orgId,
      //      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    )
  }

  def deleteTask(taskId: String, teamId: TeamId)(implicit ec: ExecutionContext, Logger: SRLogger): Future[OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId] = {

    taskDaoService.deleteTask(
      taskId = taskId,
      teamId = teamId
    )

  }

  def findLatestCampaignFromWhichLinkedinTaskIsDoneForProspect(
                                                                prospectId: ProspectId,
                                                                teamId: TeamId
                                                              ): Try[Option[CampaignId]] = {

    taskDaoService.findLatestCampaignFromWhichLinkedinTaskIsDoneForProspect(
      prospectId = prospectId,
      teamId = teamId
    )

  }

  def findLastContactedProspectByLinkedinChannel(
                                                  prospectIds: List[ProspectId],
                                                  channelType: ChannelType,
                                                  teamId: TeamId
                                                ): Try[Option[ProspectId]] = {

    taskDaoService.findLastContactedProspectByLinkedinChannel(
      prospectIds = prospectIds,
      channelType = channelType,
      teamId = teamId
    )

  }

  def updateStatusOfAllRunningLinkedinTasks(
                                             containerId: PhantomBusterContainerId,
                                             teamId: TeamId,
                                             status: TaskStatus
                                           )(implicit ec: ExecutionContext,
                                             logger: SRLogger): Future[List[AccountId]] = {

    taskDaoService.updateStatusOfAllRunningLinkedinTasks(
      containerId = containerId,
      teamId = teamId,
      status = status
    )

  }

  def updateRunningLinkedinTaskStatusByProspectLinkedinUrl(
                                                            containerId: PhantomBusterContainerId,
                                                            teamId: TeamId,
                                                            linkedinProfileUrl: String,
                                                            status: TaskStatus
                                                          )(implicit ec: ExecutionContext,
                                                            logger: SRLogger): Future[Option[TaskDetailsForPhantomBusterWebhook]] = {

    taskDaoService.updateRunningLinkedinTaskStatusByProspectLinkedinUrl(
      containerId = containerId,
      teamId = teamId,
      linkedinProfileUrl = linkedinProfileUrl,
      status = status
    )

  }

  def failTask(
                taskId: String,
                teamId: TeamId,
                failureReason: String
              )(implicit ec: ExecutionContext): Future[Int] = {

    taskDaoService.failTask(
      taskId = taskId,
      teamId = teamId,
      failureReason = failureReason

    )
  }

  def getLinkedinUrlOfProspectByTaskId(
                                        taskId: String,
                                        teamId: TeamId
                                      )(implicit ec: ExecutionContext): Future[Option[String]] = {


    taskDaoService.getLinkedinUrlOfProspectByTaskId(
      taskId = taskId,
      teamId = teamId
    )
  }

  def fetchTaskData(taskId: String, teamId: TeamId)(implicit ec: ExecutionContext): Future[TaskData] = {

    taskDaoService.fetchTaskData(
      taskId = taskId,
      teamId = teamId
    )
  }

  def updateContainerIdForTasks(
                                 taskIds: Seq[String],
                                 containerId: PhantomBusterContainerId,
                                 teamId: TeamId
                               )(implicit ec: ExecutionContext): Future[Int] = {

    taskDaoService.updateContainerIdForTasks(
      taskIds = taskIds,
      containerId = containerId,
      teamId = teamId
    )

  }


  def updateCaptainDataTaskStatusToInitiate(
                                             taskId: String,
                                             jobUid: CaptainDataJobUID,
                                             teamId: TeamId
                                           )(implicit ec: ExecutionContext): Future[Int] = {

    taskDaoService.updateCaptainDataTaskStatusToInitiate(
      taskId = taskId,
      jobUid = jobUid,
      teamId = teamId
    )

  }

  private def getTaskTypeParsed(task_type: Option[List[TaskType]]): Option[List[String]] = {

    task_type match {
      case None =>
        None
      case Some(task_types) =>

        Some(task_types.map(_.toKey))
    }

  }

  //only used in migration
  def deleteDueTasksByCampaignIdAndProspectId(
                                               campaignId: CampaignId,
                                               prospectId: ProspectId,
                                               teamId: TeamId
                                             ): Try[Int] = {

    taskDaoService.deleteDueTasksByCampaignIdAndProspectId(
      campaignId = campaignId,
      prospectId = prospectId,
      teamId = teamId
    )

  }

  // TODO: Merge the createTaskValidation method of companion object to this
  private def createTaskValidationService(
                                   new_task: NewTask,
                                   teamId: TeamId
                                 )( implicit logger: SRLogger): Either[CreateTaskError, NewTask] = {
    new_task.task_type match {

      case TaskType.AutoViewLinkedinProfile |
           TaskType.AutoLinkedinMessage |
           TaskType.AutoLinkedinConnectionRequest |
           TaskType.AutoLinkedinInmail =>
        if (new_task.campaign_id.isDefined) {
          val campaignId = new_task.campaign_id.get
          campaignDAO.getLinkedinAccountUuidAndStatusForCampaign(
            campaignId = CampaignId(campaignId),
            teamId = teamId
          ) match {
            case Success(Some(linkedinAccountUuidAndStatus)) =>
              if (linkedinAccountUuidAndStatus.status == LinkedinAccountStatus.Active) {
                Right(new_task)
              } else {
                logger.error(s"linkedin account is not active for campaign: $campaignId :: linkedinAccountUuid: ${linkedinAccountUuidAndStatus.uuid} :: teamId: $teamId")
                Left(CreateTaskError.LinkedinAccountNotActive)
              }
            case Success(None) =>
              logger.shouldNeverHappen(s" Unable to find Linkedin account for campaign: $campaignId :: teamId: $teamId")
              Left(CreateTaskError.ErrorWhileCreatingTask)
            case Failure(e) =>
              logger.error(s"Error while fetching linkedin account for campaign: $campaignId :: teamId: $teamId", err = e)
              Left(CreateTaskError.ErrorWhileCreatingTask)
          }
        } else {
          logger.shouldNeverHappen(s" Found Automatic Linkedin task without campaign  :: teamId: $teamId")
          Left(CreateTaskError.ErrorWhileCreatingTask)
        }

      case TaskType.SendLinkedinConnectionRequest | TaskType.SendEmail | TaskType.GeneralTask
           | TaskType.SendLinkedinMessage | TaskType.SendSms | TaskType.CallTask
           | TaskType.SendWhatsAppMessage | TaskType.SendLinkedinInMail | TaskType.ViewLinkedinProfile
           | TaskType.AutoEmailMagicContent | TaskType.ManualEmailMagicContent =>
        Right(new_task)
    }

  }



  def createTask(
                  task_data: NewTask,
                  accountId: Long,
                  teamId: Long
                )(
                  implicit ec: ExecutionContext,
                  logger: SRLogger
                ): Future[Either[CreateTaskError, String]] = {

    TaskService.createTaskValidation(
      task = task_data
    ) match {
      case Left(err) =>
        Future.successful(Left(err))

      case Right(new_task) =>

        createTaskValidationService(
          new_task = new_task,
          teamId = TeamId(teamId)
        ) match {
          case Left(error) =>
            logger.error(s"createTaskValidationService failed for task_data : ${task_data} :: accountId :: $accountId :: error : $error")
            Future.successful(Left(error))
          case Right(validatedTask) =>
            taskDaoService.createTask(
              task_data = task_data,
              accountId = accountId,
              teamId = teamId
            )
        }

    }

  }


  // This new service function is created as a cover for createTask ,since we want to check if user has permission over
  // prospectId to create task , which is coming from controller, and don't affect other places where createTask is used.
  def createTaskWithProspectValidation(
                                        task_data: NewTask,
                                        creatorAccountId: Long,
                                        teamId: Long,
                                        permittedAccountIdsForEditTask: Seq[Long],
                                        permittedAccountIdsForEditProspect: Seq[Long]
                                      )(
                                        implicit ec: ExecutionContext,
                                        logger: SRLogger
                                      ): Future[Either[CreateTaskError, String]] = {

    prospectDAOServiceV2.checkIfUserHasPermissionForProspects(prospectsIds = Seq(task_data.prospect_id.get),
      permittedAccountIds = permittedAccountIdsForEditProspect, teamId = teamId) match {
      case Failure(e) => Future.successful(Left(CreateTaskError.FailedToValidateProspect(e)))
      case Success(pids) =>
        if (pids.isEmpty) {
          Future.successful(Left(CreateTaskError.DontHavePermissionToCreateTaskForProspect))
        } else if (!permittedAccountIdsForEditTask.contains(task_data.assignee_id.get)) {
          Future.successful(Left(CreateTaskError.AssigneeDontHavePermissionForEditTask))
        } else {
          TaskService.createTaskValidation(
            task = task_data
          ) match {
            case Left(err) =>
              Future.successful(Left(err))

            case Right(new_task) =>

              createTaskValidationService(
                new_task = new_task,
                teamId = TeamId(teamId)
              ) match {
                case Left(error) =>
                  logger.error(s"createTaskValidationService failed for task_data : ${task_data} :: accountId :: $creatorAccountId :: error : $error")
                  Future.successful(Left(error))
                case Right(validatedTask) =>
                  taskDaoService.createTask(
                    task_data = task_data,
                    accountId = creatorAccountId,
                    teamId = teamId
                  )
              }

          }
        }

    }


  }

  def getTaskResponse(
                       isFirst: Boolean,
                       validatedTaskReq: ValidatedTaskReq.ValidatedTaskReqRange,
                       atSameTimeTasksRes: Option[List[Task]],
                       sortedTask: List[Task]
                     )(implicit ec: ExecutionContext
                     ): TaskPagination = {

    validatedTaskReq.timeline match {

      case InferredQueryTimeline.Range.Before(dateTime) =>

        atSameTimeTasksRes match {

          case Some(atSameTimeTasks) =>

            val lastTask = atSameTimeTasks.last
            val lastTaskTime: DateTime = TaskService.getStatusBasedTime(lastTask)
            val final_task = sortedTask
              .filterNot {
                case (task) => {

                  val taskTime: DateTime = TaskService.getStatusBasedTime(task)
                  taskTime == lastTaskTime

                }

              } ++ atSameTimeTasks

            val prev = if (isFirst) {
              None
            } else {
              Some(


                TaskService.getStatusBasedTime(sortedTask.head)


              )
            }

            TaskPagination(
              data = final_task,
              links = NavigationLinks(
                prev = prev,
                next = Some(lastTaskTime)
              ))

          case None =>

            val penUltimate = sortedTask.dropRight(1).last
            val penUltimateTaskTime: DateTime = TaskService.getStatusBasedTime(penUltimate)
            val prev = if (isFirst) {
              None
            } else {
              Some(
                TaskService.getStatusBasedTime(sortedTask.head)
              )
            }

            TaskPagination(
              data = sortedTask.dropRight(1),
              links = NavigationLinks(
                prev = prev,
                next = Some(penUltimateTaskTime)
              ))

        }

      case InferredQueryTimeline.Range.After(dateTime) =>

        atSameTimeTasksRes match {

          case Some(atSameTimeTasks) =>
            val firstTask = sortedTask.head

            val firstTaskTime = TaskService.getStatusBasedTime(firstTask)

            val final_tasks: List[Task] = atSameTimeTasks ++ sortedTask.filterNot {
              case (task) =>

                val taskTime = TaskService.getStatusBasedTime(task)

                taskTime == firstTaskTime
            }

            val prev = if (isFirst) {
              None
            } else {
              Some(

                TaskService.getStatusBasedTime(final_tasks.head)

              )
            }

            TaskPagination(
              data = final_tasks,
              links = NavigationLinks(
                prev = prev,
                next = Some(TaskService.getStatusBasedTime(final_tasks.last))
              ))

          case None =>

            val final_tasks = sortedTask.drop(1)
            val prev = if (isFirst) {
              None
            } else {
              Some(TaskService.getStatusBasedTime(final_tasks.head))
            }
            val next = Some(TaskService.getStatusBasedTime(final_tasks.last))

            TaskPagination(data = final_tasks,
              links = NavigationLinks(
                prev = prev,
                next = next
              ))

        }
    }

  }

  def computeExactlyAt(
                        validatedTaskReq: ValidatedTaskReq.ValidatedTaskReqRange,
                        page_size: Int,
                        tasks: List[Task]
                      ): Option[ExactIdToCompareTime] = {

    if (tasks.size < page_size + 1) {
      None
    } else {
      validatedTaskReq.timeline match {

        case InferredQueryTimeline.Range.Before(_) =>
          val lastTask = tasks.last
          val lastTaskTime = TaskService.getStatusBasedTime(lastTask)
          val penUltimate = tasks.dropRight(1).last
          val penUltimateTime = TaskService.getStatusBasedTime(penUltimate)
          if (lastTaskTime == penUltimateTime) {

            Some(ExactIdToCompareTime(lastTask.task_id))
          } else None

        case InferredQueryTimeline.Range.After(_) =>
          val firstTask = tasks.head
          val firstTaskTime = TaskService.getStatusBasedTime(firstTask)
          val secondTask = tasks.drop(1).head
          val secondTaskTime = TaskService.getStatusBasedTime(secondTask)
          if (firstTaskTime == secondTaskTime) {
            Some(ExactIdToCompareTime(firstTask.task_id))
          } else None
      }
    }
  }

  def getAllTasksForUser(
                          isFirst: Boolean,
                          team_id: Long,
                          orgId: OrgId,
                          validatedTaskReq: ValidatedTaskReq.ValidatedTaskReqRange,
                          searchTask: SearchTask,
                          timeZone: String,
                          doNotFetchAutomatedDueTasks: Boolean,
                          permittedAccountIds: Seq[Long]
                        )(
                          implicit ec: ExecutionContext, logger: SRLogger
                        ): Future[Either[GetAllTaskForUserError, TaskPagination]] = {

    val searchTaskParsed: SearchTask = searchTask

    val timeBasedTaskType: Option[TimeBasedTaskType] = searchTask.time_based_task_type

    val taskParsed: Option[List[String]] = getTaskTypeParsed(task_type = searchTaskParsed.task_types)

    val statusParsed: Option[List[String]] = getTaskStatusParsed(task_status = searchTaskParsed.task_status)

    val priorityParsed: Option[List[String]] = getTaskPriorityParsed(task_priority = searchTaskParsed.task_priority)

//    val emailNotCompulsoryEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//      teamId = TeamId(team_id),
//      feature = SrRollingUpdateFeature.EmailNotCompulsory
//    )

    for {

      tasks: Either[GetAllTaskForUserError, List[Task]] <- taskDaoService.getAllTaskForUserV2(
        team_id = team_id,
        orgId = orgId,
        timeZone = timeZone,
        validatedTaskReq = validatedTaskReq.copy(pageSize = validatedTaskReq.pageSize + 1),
        assignee_ids = searchTaskParsed.assignee_ids,
        task_type = taskParsed,
        task_status = statusParsed,
        task_priority = priorityParsed,
        reply_sentiment_uuids = searchTaskParsed.reply_sentiment_uuids,
        campaign_ids = searchTask.campaign_ids,
        doNotFetchAutomatedDueTasks = doNotFetchAutomatedDueTasks,
        timeBasedTaskType = searchTask.time_based_task_type,
        permittedAccountIds = permittedAccountIds,
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
      )

      final_tasks: Either[GetAllTaskForUserError, TaskPagination] <- {

        tasks match {

          case Left(GetAllTaskForUserError.ServerError(e)) =>
            Future.successful(Left(GetAllTaskForUserError.ServerError(e)))

          case Left(GetAllTaskForUserError.ErrorWhileGettingTasks) =>

            Future.successful(Left(GetAllTaskForUserError.ErrorWhileGettingTasks))

          case Right(listOfTasks) =>

            val sortedTasks: List[Task] = listOfTasks.sortBy(task => {

                TaskService.getStatusBasedTime(task)

              })(JodaTimeUtils.dateTimeOrdering)
              .reverse


            val limit = validatedTaskReq.pageSize
            val hasMore = sortedTasks.length >= limit + 1

            if (!hasMore && listOfTasks.nonEmpty) {

              validatedTaskReq.timeline match {
                case InferredQueryTimeline.Range.Before(dateTime) =>
                  val prev = if (isFirst) {
                    None
                  } else {

                    val prev_time: DateTime = TaskService.getStatusBasedTime(sortedTasks.head)

                    Some(prev_time)

                  } // same order of tasks maintained ( descending order )

                  Future.successful(Right(TaskPagination(
                    data = sortedTasks, links = NavigationLinks(
                      prev = prev,
                      next = None
                    )
                  )))


                case InferredQueryTimeline.Range.After(dateTime) =>
                  val next = if (isFirst) {
                    None
                  } else {

                    val next_time: DateTime = TaskService.getStatusBasedTime(sortedTasks.last)

                    Some(next_time)

                  } // This time tasks will be reversed ( descending order )

                  Future.successful(Right(TaskPagination(
                    data = sortedTasks, links = NavigationLinks(
                      prev = None,
                      next = next
                    )
                  )))
              }

            } else if (!hasMore && listOfTasks.isEmpty) {

              Future.successful(Right(TaskPagination(
                data = List(), links = NavigationLinks(
                  prev = None,
                  next = None
                )
              )))

            } else {

              val exactlyAtCheck: Option[ExactIdToCompareTime] = computeExactlyAt(
                validatedTaskReq = validatedTaskReq, page_size = validatedTaskReq.pageSize,
                tasks = sortedTasks
              )

//              val emailNotCompulsoryEnabled = srRollingUpdateCoreService
//                .checkIfTeamCanReceiveEarlyUpdates(
//                  teamId = TeamId(team_id),
//                  feature = SrRollingUpdateFeature.EmailNotCompulsory
//                )

              val atSameTimeTasks: Option[Future[Either[GetAllTaskForUserError, List[Task]]]]
              = exactlyAtCheck.map(exactlyAt =>
                taskDaoService.getAtSameTimeTasksFromDB(
                  timeBasedTaskType = searchTask.time_based_task_type,
                  exactlyAtTaskId = exactlyAt,
                  team_id = team_id,
                  orgId = orgId,
                  timeZone = timeZone,
                  reply_sentiment_uuids = searchTaskParsed.reply_sentiment_uuids,
                  assignee_ids = searchTaskParsed.assignee_ids,
                  campaign_ids = searchTask.campaign_ids,
                  task_type = taskParsed,
                  task_status = statusParsed,
                  task_priority = priorityParsed,
                  doNotFetchAutomatedDueTasks = doNotFetchAutomatedDueTasks,
                  permittedAccountIds = permittedAccountIds,
//                  emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
                ))

              atSameTimeTasks match {
                case None =>
                  Future.successful(Right(getTaskResponse(
                    isFirst = isFirst,
                    validatedTaskReq = validatedTaskReq,
                    atSameTimeTasksRes = None,
                    sortedTask = sortedTasks
                  )))

                case Some(tasks) =>
                  tasks
                    .map {
                      case Left(GetAllTaskForUserError.ErrorWhileGettingTasks) =>
                        Left(GetAllTaskForUserError.ErrorWhileGettingTasks)

                      case Left(GetAllTaskForUserError.ServerError(e)) =>
                        Left(GetAllTaskForUserError.ServerError(e))

                      case Right(tasks) =>
                        Right(getTaskResponse(
                          isFirst = isFirst,
                          validatedTaskReq = validatedTaskReq,
                          atSameTimeTasksRes = Some(tasks),
                          sortedTask = sortedTasks
                        ))
                    }
                    .recover { case e => Left(GetAllTaskForUserError.ServerError(e)) }
              }
            }
        }
      }
    } yield {
      final_tasks
    }

  }


  // delete from cache when implementing this
  def deleteTaskFromFrontend(
                              taskId: String,
                              team_id: Long,
                              permittedAccountIds: Seq[Long]
                            )(implicit ec: ExecutionContext,
                              logger: SRLogger
                            ): Future[OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId] = {
    if (permittedAccountIds.isEmpty) {
      Future.failed(new Throwable("No permitted account is given"))
    } else
      taskDaoService.deleteTask(
        taskId = taskId,
        team_id = team_id,
        permittedAccountIds = permittedAccountIds
      )
  }

  def updateNextCheckForSchedulingInCampaignChannelSettingForMagicContent(
                                                                           taskId: String, 
                                                                           teamId: Long, 
                                                                           orgId: OrgId, 
                                                                           permittedAccountIds: Seq[Long]
                                                                         )(
                                                                           implicit ec: ExecutionContext, logger: SRLogger
                                                                         ) = {

    for {
      task: Option[Task] <- taskDaoService.findTaskById(
        taskId = taskId,
        teamId = teamId,
        orgId = orgId,
        permittedAccountIds = permittedAccountIds,
      )
      campaign: Option[Campaign] <- Try {
        if(task.isDefined && task.get.campaign_id.isDefined){
          campaignDAO.findCampaignForCampaignUtilsOnly(
            id = task.get.campaign_id.get, 
            teamId = TeamId(teamId)
          )
        } else None

      }
    } yield {
      if(campaign.isDefined ) {
        val data: List[CampaignDataToAddNextToBeScheduledAtForEmailChannel] = task.get.task_type match {
          case TaskType.SendLinkedinConnectionRequest |
               TaskType.SendEmail|
               TaskType.GeneralTask |
               TaskType.SendLinkedinMessage |
               TaskType.SendSms |
               TaskType.CallTask |
               TaskType.SendWhatsAppMessage |
               TaskType.SendLinkedinInMail |
               TaskType.ViewLinkedinProfile |
               TaskType.AutoViewLinkedinProfile |
               TaskType.AutoLinkedinMessage |
               TaskType.AutoLinkedinConnectionRequest |
               TaskType.AutoLinkedinInmail  => List()
          
          case TaskType.AutoEmailMagicContent |
               TaskType.ManualEmailMagicContent => campaign.get.settings.campaign_email_settings.map(e =>
            CampaignDataToAddNextToBeScheduledAtForEmailChannel(
              campaignId = e.campaign_id.id,
              nextToBeScheduledAt = DateTime.now().plusMinutes(5),
              sender_email_setting_id = e.sender_email_setting_id.emailSettingId,
              channelType = ChannelType.EmailChannel
            )
          )
        }

        campaignDAO._updateNextToScheduledAt(
          campaignIdsWithNextToBeScheduledAt = data,
          Logger = logger,
          team_id = TeamId(teamId),
          is_scheduler_flow = false
        ) match {
          case Success(value) => //Do Nothing
          case Failure(exception) =>
            logger.shouldNeverHappen(s"Failed to _updateNextToScheduledAt", Some(exception))
        }
      }
    }
      

  }
  // delete from cache when implementing this
  def updateTask(
                  update_task_data: UpdateTask,
                  taskId: String,
                  teamId: Long,
                  permittedAccountIds: Seq[Long],
                  orgId: OrgId,
                  changeStatusPermissionCheck: ChangeStatusPermissionCheck
                )(
                  implicit ec: ExecutionContext, logger: SRLogger
                ): Future[Option[String]] = {

      // Call the DAO to update non-status fields first
      val updateDbFuture: Future[Option[String]] = taskDaoService.updateTask(
        update_task_data = update_task_data,
        taskId = taskId,
        teamId = teamId,
        permittedAccountIds = permittedAccountIds
      )

      updateDbFuture.flatMap {
          case Some(updatedTaskId) =>
              if (update_task_data.status.status_type == TaskStatusType.Approved) {
                  changeStatus(
                      task_id = updatedTaskId,
                      task_status = UpdateTaskStatus.Approved(),
                      orgId = orgId,
                      changeStatusPermissionCheck = changeStatusPermissionCheck,
                      team_id = teamId

                  ).map {
                      case Left(changeStatusError) =>

                          logger.error(s"Task $updatedTaskId update successful, but subsequent changeStatus(Approved) call failed: $changeStatusError")
                          Some(updatedTaskId)
                      case Right(_) =>
                          logger.info(s"Task $updatedTaskId subsequent changeStatus(Approved) call successful.")

                          Some(updatedTaskId)
                  }
              } else {
                  Future.successful(Some(updatedTaskId))
              }

          case None =>

              logger.warn(s"Initial task update via taskDaoService.updateTask failed for task ID $taskId (returned None).")
              Future.successful(None)
      }
    }

  def changeStatus(
                    task_id: String,
                    task_status: UpdateTaskStatus,
                    orgId: OrgId,
                    changeStatusPermissionCheck: ChangeStatusPermissionCheck,
                    team_id: Long,
                  )(implicit ec: ExecutionContext,
                    logger: SRLogger
                  ): Future[Either[ChangeStatusTypeError, String]] = {

//    val emailNotCompulsoryEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//      teamId = TeamId(team_id),
//      feature = SrRollingUpdateFeature.EmailNotCompulsory
//    )

    task_status.status_type match {
      case TaskStatusType.Done | TaskStatusType.Skipped | TaskStatusType.Approved =>

        /*
        We are only updating the campaign_prospects current_step_scheduler_status to done or skipped when the status is changing to done/skipped for tasks. i.e, when tasks got completed or marked skipped
        Hence, we are doing this match
        */

        for {

          changeTime: DateTime <- Future.successful(srDateTimeUtils.getDateTimeNow())

          changeStatus: Either[ChangeStatusTypeError, String] <- taskDaoService.changeStatus(
            task_id = task_id,
            task_status = task_status,
            orgId = orgId,
            changeTime = changeTime,
            team_id = team_id,
            changeStatusPermissionCheck = changeStatusPermissionCheck,
//            emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
          )

          task: Task <- taskDaoService.
            fetchTask(
              taskId = task_id,
              team_id = team_id,
              orgId = orgId,
              changeStatusPermissionCheck = changeStatusPermissionCheck,
//              emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
            ).map(t => t.head)

          updateProspect: Int <- Future.fromTry {
            if (task.prospect.isDefined && task.status.status_type == TaskStatusType.Done) {
              prospectDAOService.updateLatestTaskDoneAt(
                prospectId = ProspectId(task.prospect.get.id),
                teamId = TeamId(team_id),
                time = changeTime
              )
            }
            else {
              Success(0)
            }
          }

        } yield {
          if(task_status.status_type == TaskStatusType.Approved) {
            updateNextCheckForSchedulingInCampaignChannelSettingForMagicContent(
              taskId = task_id,
              teamId = team_id,
              orgId = orgId,
              permittedAccountIds = changeStatusPermissionCheck.permittedAccountIds
            )
          }
          if (task_status.status_type == TaskStatusType.Done && task.step_id.isDefined && task.campaign_id.isDefined && task.prospect.isDefined) {
            campaignProspectStepScheduleLogsDAO.insert(
              data = Seq(CampaignProspectStepScheduleLogData(
                campaignProspectStepScheduleEventType = CampaignProspectStepScheduleEventType.Sent,
                emailScheduledIdOrTaskId = EmailScheduledIdOrTaskId.TaskId(task_id),
                team_id = TeamId(team_id),
                c_id = CampaignId(task.campaign_id.get),
                p_id = ProspectId(task.prospect.get.id),
                step_id = task.step_id.get.toString,
                step_type = Some(TaskType.getCampaignStepType(task.task_type))
              ))
            ) match {
              case Success(value) => //do nothing
              case Failure(exception) =>
                logger.shouldNeverHappen("campaignProspectStepScheduleLogsDAO.insert", Some(exception))
            }
          }

          task.created_via match {

            case TaskCreatedVia.Scheduler | TaskCreatedVia.AiGenerated =>

              val current_step_type = CampaignStepType.fromKey(task.task_type.toKey).get
              // this will never fail as we are already coming here only if we have a task_status type done/skipped which will be converted from fromKey

              val currentStepStatusForSchedulerData: CurrentStepStatusForSchedulerData = CurrentStepStatusForSchedulerData.getDataFromTaskStatus(task.status).get

              val update_campaign_prospect: Try[Seq[Long]] = for {

                mark_as_sent: Int <- {

                  if (task_status.status_type == TaskStatusType.Done && task.campaign_id.isDefined && task.prospect.isDefined) {
                    campaignProspectDAO._hasBeenSent(
                      campaignId = task.campaign_id.get,
                      prospect_id = task.prospect.get.id
                    )
                  } else {
                    Try {
                      0
                    }
                  }

                }

                /*
                using .get below because they will be always be present when tasks are created via scheduler
                */
                update_campaign_prospect: Seq[Long] <- campaignProspectDAO._updateScheduledStatus(
                  Seq(CampaignProspectUpdateScheduleStatus(
                    current_step_status_for_scheduler_data = currentStepStatusForSchedulerData,
                    current_step_type = current_step_type,
                    current_step_task_id = task.task_id,
                    step_id = task.step_id.get,
                    campaign_id = task.campaign_id.get,
                    prospect_id = task.prospect.get.id,
                    email_message_id = None,
                    current_campaign_email_settings_id = None //this is none as this is not a email step
                  ))
                )

                resetCache: Unit <- Try {
                  campaignCacheService.resetCampaignStats(
                    campaignId = task.campaign_id.get,
                    teamId = team_id
                  )
                }

              } yield {

                update_campaign_prospect
              }

              update_campaign_prospect match {
                case Failure(exception) =>
                  logger.fatal("TaskService.changeStatus _updateScheduledStatus db failure", exception)
                case Success(updateCPprospectIds) =>
                  if (updateCPprospectIds.length != 1) {
                    logger.fatal(s"TaskService.changeStatus _updateScheduledStatus:: result length cannot be zero")
                  }
              }

              changeStatus


            case TaskCreatedVia.Manual | TaskCreatedVia.Call_action =>

              changeStatus

          }
        }


      case _ =>
        // Fixme_magic_content handle when task status is changed to approved also updated the campaigns prospect from here
        val currentTime = srDateTimeUtils.getDateTimeNow()

        taskDaoService.changeStatus(
          task_id = task_id,
          orgId = orgId,
          changeTime = currentTime,
          task_status = task_status,
          team_id = team_id,
          changeStatusPermissionCheck = changeStatusPermissionCheck,
//          emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
        )

    }
  }

  def changePriority(
                      task_id: String,
                      task_priority: TaskPriority,
                      team_id: Long,
                      permittedAccountIds: Seq[Long]
                    )(implicit ec: ExecutionContext,
                      logger: SRLogger
                    ): Future[Either[ChangePriorityError, Option[String]]] = {

    taskDaoService.changePriority(
      task_id = task_id,
      task_priority = task_priority,
      team_id = team_id,
      permittedAccountIds = permittedAccountIds
    )

  }

  def assignOwner() = ???

  def addNote(
               task_id: String,
               note: String,
               team_id: Long,
               permittedAccountIds: Seq[Long]
             )(implicit ec: ExecutionContext,
               logger: SRLogger
             ): Future[Either[AddNoteError, Option[String]]] = {

    taskDaoService.addNote(
      task_id = task_id,
      note = note,
      team_id = team_id,
      permittedAccountIds = permittedAccountIds
    )

  }

  def executeTask() = ???

  def hasTaskCheck(
                    taskIds: List[String],
                    teamId: Long,
                    orgId: OrgId,
                    permittedAccountIds: Seq[Long]
                  )(implicit ec: ExecutionContext,
                    logger: SRLogger): Future[Either[TaskAccessError, List[Task]]] = {

//    val emailNotCompulsoryEnabled = srRollingUpdateCoreService
//      .checkIfTeamCanReceiveEarlyUpdates(
//        teamId = TeamId(teamId),
//        feature = SrRollingUpdateFeature.EmailNotCompulsory
//      )
    taskDaoService.findBatchTasksFromScyllaAndPg(
      taskIds = taskIds,
      team_id = teamId,
      changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
        permittedAccountIds = permittedAccountIds,
        doer = None
      ),
      orgId = orgId,
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    ).map { res => {
      if (res.nonEmpty) {
        res.head.assignee match {
          case None =>
            Right(res)

          case Some(assignee) =>
            if (permittedAccountIds.contains(assignee.id)) {
              Right(res)
            } else {
              Left(TaskAccessError.AccessNotFound)
            }
        }
      } else {
        Left(TaskAccessError.AccessNotFound)
      }
    }
    }
  }

  def findTaskbyTaskId(
                        taskId: String,
                        teamId: Long,
                        permittedAccountIds: Seq[Long],
                        orgId: OrgId
                      )(implicit ec: ExecutionContext,
                        logger: SRLogger)
  : Future[Either[GetAllTaskForUserError, Task]] = {

//    val emailNotCompulsoryEnabled = srRollingUpdateCoreService
//      .checkIfTeamCanReceiveEarlyUpdates(
//        teamId = TeamId(teamId),
//        feature = SrRollingUpdateFeature.EmailNotCompulsory
//      )

    taskDaoService.findSingleTaskFromScyllaAndPG(
      taskId = taskId,
      teamId = teamId,
      permittedAccountIds = permittedAccountIds,
      orgId = orgId,
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    )

  }

  def deleteBatchTasks(taskIds: List[String],
                       teamId: Long,
                       orgId: OrgId,
                       permittedAccountIds: Seq[Long]
                      )(
                        implicit ec: ExecutionContext, logger: SRLogger
                      ): Future[Either[DeleteBatchTaskError, List[String]]] = {


    if (taskIds.size > 100) {
      Future.successful(Left(DeleteBatchTaskError.BatchDeleteLimitError))
    }
    else {

//      val emailNotCompulsoryEnabled = srRollingUpdateCoreService
//        .checkIfTeamCanReceiveEarlyUpdates(
//          teamId = TeamId(teamId),
//          feature = SrRollingUpdateFeature.EmailNotCompulsory
//        )
      for {

        taskCheck <- taskDaoService.checkBatchTaskAccessByTaskIdAndTeamId(
          taskIds = taskIds,
          teamId = teamId,
          orgId = orgId,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
        )

        notFoundTasks <- Future.successful(taskIds diff taskCheck)

        result <- {
          if (notFoundTasks.nonEmpty) {
            Future.successful(Left(DeleteBatchTaskError.DontHaveAccessError(
              task_ids = notFoundTasks
            )))
          } else {
            taskDaoService.deleteBatchTasks(
              taskIds = taskIds, teamId = teamId,
              permittedAccountIds = permittedAccountIds
            ).map { res =>
              Right(res)
            }
          }
        }

      } yield {
        result
      }

    }
  }

  // 22-Jan-2024 SCYLLA_COMMENTED_OUT
  //  def checkScyllaCount()(implicit ec: ExecutionContext, Logger: SRLogger): Future[Int] = {
  //    taskDaoService.checkScyllaCount()
  //  }

  def getTaskFilterCount(
                          team_id: Long,
                          orgId: OrgId,
                          assignee_id: Option[Long],
                          campaign_id: Option[Long],
                          reply_sentiment_uuid_opt: Option[ReplySentimentUuid],
                          task_priority: List[TaskPriority],
                          timeZone: String,
                          doNotFetchAutomatedDueTasks: Boolean,
                          duration_from: Option[Long],
                          duration_to: Option[Long],
                          permittedAccountIds: Seq[Long]
                        )(implicit ec: ExecutionContext, logger: SRLogger) = {


//    val emailNotCompulsoryEnabled = srRollingUpdateCoreService
//      .checkIfTeamCanReceiveEarlyUpdates(
//        teamId = TeamId(team_id),
//        feature = SrRollingUpdateFeature.EmailNotCompulsory
//      )
    for {
      dateRange <- Future.fromTry {
        TaskService.validateDateRange(
          duration_from = duration_from,
          duration_to = duration_to
        )
      }

      tasks <- taskDaoService.getAllTasksForCount(
        team_id = team_id,
        orgId = orgId,
        assignee_id = assignee_id,
        campaign_id = campaign_id,
        reply_sentiment_uuid_opt = reply_sentiment_uuid_opt,
        task_priority = task_priority,
        dateRange = dateRange,
        doNotFetchAutomatedDueTasks = doNotFetchAutomatedDueTasks,
        permittedAccountIds = permittedAccountIds,
        //        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
      )

    } yield {


      val todayTasks: List[Task] = getTodayTasks(tasks = tasks, timeZone = timeZone)

      val todayAndDueTasks: List[Task] = getTodayAndDueTasks(tasks = tasks, timeZone = timeZone)

      val upcomingTasks: List[Task] = getUpcomingTasks(tasks = tasks, timeZone = timeZone)

      val dueTasks: List[Task] = getDueTasks(tasks = tasks, timeZone = timeZone)

      val completedTasks: List[Task] = tasks.filter((t) => t.status match {
        case status: TaskStatus.Done  =>
          true
        case status: TaskStatus.Approved =>

          true

        case _ =>
          false
      })

      val skippedTasks: List[Task] = tasks.filter((t) => t.status match {
        case status: TaskStatus.Skipped =>
          true
        case _ =>
          false
      })

      val snoozedTasks: List[Task] = tasks.filter((t) => t.status match {
        case status: TaskStatus.Snoozed =>
          true
        case _ =>
          false
      })

      val failedTasks: List[Task] = tasks.filter((t) => t.status match {
        case status: TaskStatus.Failed =>
          true
        case _ =>
          false
      })

      TaskCount(
        todayAndDue = getSubCountForTaskCount(todayAndDueTasks),
        today = getSubCountForTaskCount(todayTasks),
        upcoming = getSubCountForTaskCount(upcomingTasks),
        due = getSubCountForTaskCount(dueTasks),
        completed = getSubCountForTaskCount(completedTasks),
        skipped = getSubCountForTaskCount(skippedTasks),
        snoozed = getSubCountForTaskCount(snoozedTasks),
        failed = getSubCountForTaskCount(failedTasks)
      )

    }
  }

  def getTaskByTaskId(
                       team_id: Long,
                       task_id: String,
                       orgId: OrgId,
                       changeStatusPermissionCheck: ChangeStatusPermissionCheck
                     )(
                       implicit ec: ExecutionContext, logger: SRLogger
                     ): Future[Option[Task]] = {


//    val emailNotCompulsoryEnabled = srRollingUpdateCoreService
//      .checkIfTeamCanReceiveEarlyUpdates(
//        teamId = TeamId(team_id),
//        feature = SrRollingUpdateFeature.EmailNotCompulsory
//      )

    taskDaoService
      .findBatchTasksFromScyllaAndPg(
        taskIds = List(task_id),
        team_id = team_id,
        orgId = orgId,
        changeStatusPermissionCheck = changeStatusPermissionCheck,
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
      )
      .map(tasks => {
        if (tasks.length == 1) {
          Some(tasks.head)
        } else {
          None
        }
      })
  }

  // SENDER_ROTATION
  // Called from -
  // CampaignPreviewService.previewGetStepsForProspect
  // campaignController.previewGetStepsForProspect
  def getCompletedTasksForProspectInCampaign(
                                              campaign_id: Long,
                                              prospect_id: Long,
                                              orgId: OrgId,
                                              team_id: Long,
                                              permittedAccountIds: Seq[Long],
                                              timeZone: String,
                                              channel_setting_details: List[ChannelSettingSenderDetails],
                                              es: Option[EmailSetting]
                                            )(
                                              implicit ec: ExecutionContext, logger: SRLogger
                                            ): Future[Seq[PreviousFollowUp]] = {


//    val emailNotCompulsoryEnabled = srRollingUpdateCoreService
//      .checkIfTeamCanReceiveEarlyUpdates(
//        teamId = TeamId(team_id),
//        feature = SrRollingUpdateFeature.EmailNotCompulsory
//      )
    for {

      completedTasks: List[Task] <- taskDaoService.
        getCompletedTasksForProspectInCampaign(
          campaign_id = campaign_id,
          prospect_id = prospect_id,
          team_id = team_id,
          permittedAccountIds = permittedAccountIds,
          orgId = orgId,
//          emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
        )


    } yield {

      completedTasks.map(task => {


        val channel_follow_up_data = task.task_data match {

          case data: TaskData.SendEmailData =>

            // Sender name - Settings data
            // FIXME SENDER_ROTATION
            val from_name = es.get.first_name + " " + es.get.last_name

            // sender email - Settings data
            // FIXME SENDER_ROTATION
            val from_email = es.get.email

            PreviousFollowUpData.ManualEmailFollowUp(
              email_thread_id = None,
              from_name = from_name,
              subject = data.subject,
              body = data.body, // Fixme Multichhanel  - confirm usage
              base_body = data.body, // Fixme Multichhanel  - confirm this with prateek whether we need html body
              from_email = from_email,
              is_edited_preview_email = false // Confirm this for manual email
            )

          case data: TaskData.ViewLinkedinProfileData =>

            val sender_details = channel_setting_details.find(t => t.channel_type == ChannelType.LinkedinChannel)

            if (sender_details.isEmpty) {

              throw new Exception(CampaignService.linkedinChannelSettingsNotFoundError)

            } else {
              // Sender name - Settings data
              val from_name = sender_details.get.first_name + " " + sender_details.get.last_name

              // sender email - Settings data
              val from_email = sender_details.get.email.get // this will never fail as we won't allow a linkedin setting to be added without email

              PreviousFollowUpData.LinkedinViewProfileFollowUp(
                from_name = from_name,
                from_email = from_email
              )
            }


          case data: TaskData.LinkedinConnectionRequestData =>

            val sender_details = channel_setting_details.find(t => t.channel_type == ChannelType.LinkedinChannel)

            if (sender_details.isEmpty) {

              throw new Exception(CampaignService.linkedinChannelSettingsNotFoundError)

            } else {


              val from_name = sender_details.get.first_name + " " + sender_details.get.last_name

              val from_email = sender_details.get.email.get

              PreviousFollowUpData.LinkedinConnectionRequestFollowUp(
                body = data.request_message,
                base_body = data.request_message,
                from_name = from_name,
                from_email = from_email
              )

            }

          case data: TaskData.SendLinkedinInMailData =>

            val sender_details = channel_setting_details.find(t => t.channel_type == ChannelType.LinkedinChannel)

            if (sender_details.isEmpty) {

              throw new Exception(CampaignService.linkedinChannelSettingsNotFoundError)

            } else {

              val from_name = sender_details.get.first_name + " " + sender_details.get.last_name

              val from_email = sender_details.get.email.get

              PreviousFollowUpData.LinkedinInMailFollowUp(
                subject = data.subject,
                body = data.body,
                base_body = data.body,
                from_name = from_name,
                from_email = from_email
              )

            }


          case data: TaskData.SendLinkedinMessageData =>

            val sender_details = channel_setting_details.find(t => t.channel_type == ChannelType.LinkedinChannel)

            if (sender_details.isEmpty) {

              throw new Exception(CampaignService.linkedinChannelSettingsNotFoundError)

            } else {

              val from_name = sender_details.get.first_name + " " + sender_details.get.last_name

              // linkedin_settings have email field as not null we are taking this input while creating linkedin_settings
              val from_email = sender_details.get.email.get

              PreviousFollowUpData.LinkedinMessageFollowUp(
                body = data.body,
                base_body = data.body,
                from_name = from_name,
                from_email = from_email
              )

            }

          case data: TaskData.AutoViewLinkedinProfile =>

            val sender_details = channel_setting_details.find(t => t.channel_type == ChannelType.LinkedinChannel)

            if (sender_details.isEmpty) {

              throw new Exception(CampaignService.linkedinChannelSettingsNotFoundError)

            } else {
              // Sender name - Settings data
              val from_name = sender_details.get.first_name + " " + sender_details.get.last_name

              // sender email - Settings data
              val from_email = sender_details.get.email.get // this will never fail as we won't allow a linkedin setting to be added without email

              PreviousFollowUpData.AutoLinkedinViewProfileFollowUp(
                from_name = from_name,
                from_email = from_email
              )
            }


          case data: TaskData.AutoLinkedinConnectionRequest =>

            val sender_details = channel_setting_details.find(t => t.channel_type == ChannelType.LinkedinChannel)

            if (sender_details.isEmpty) {

              throw new Exception(CampaignService.linkedinChannelSettingsNotFoundError)

            } else {


              val from_name = sender_details.get.first_name + " " + sender_details.get.last_name

              val from_email = sender_details.get.email.get

              PreviousFollowUpData.AutoLinkedinConnectionRequestFollowUp(
                body = data.body,
                base_body = data.body,
                from_name = from_name,
                from_email = from_email
              )

            }

          case data: TaskData.AutoLinkedinInmail =>

            val sender_details = channel_setting_details.find(t => t.channel_type == ChannelType.LinkedinChannel)

            if (sender_details.isEmpty) {

              throw new Exception(CampaignService.linkedinChannelSettingsNotFoundError)

            } else {

              val from_name = sender_details.get.first_name + " " + sender_details.get.last_name

              val from_email = sender_details.get.email.get

              PreviousFollowUpData.AutoLinkedinInMailFollowUp(
                subject = data.subject,
                body = data.body,
                base_body = data.body,
                from_name = from_name,
                from_email = from_email
              )

            }


          case data: TaskData.AutoLinkedinMessage =>

            val sender_details = channel_setting_details.find(t => t.channel_type == ChannelType.LinkedinChannel)

            if (sender_details.isEmpty) {

              throw new Exception(CampaignService.linkedinChannelSettingsNotFoundError)

            } else {

              val from_name = sender_details.get.first_name + " " + sender_details.get.last_name

              // linkedin_settings have email field as not null we are taking this input while creating linkedin_settings
              val from_email = sender_details.get.email.get

              PreviousFollowUpData.AutoLinkedinMessageFollowUp(
                body = data.body,
                base_body = data.body,
                from_name = from_name,
                from_email = from_email
              )

            }


          case data: TaskData.SendSmsData =>

            val sender_details = channel_setting_details.find(t => t.channel_type == ChannelType.SmsChannel)

            if (sender_details.isEmpty) {

              throw new Exception(CampaignService.smsChannelSettingsNotFoundError)

            } else {
              val from_name = sender_details.get.first_name + " " + sender_details.get.last_name

              val phone_number = sender_details.get.phone_number.get

              PreviousFollowUpData.SmsFollowUp(
                body = data.body,
                base_body = data.body,
                from_name = from_name,
                phone_number = phone_number
              )
            }


          case data: TaskData.SendWhatsAppMessageData =>

            val sender_details = channel_setting_details.find(t => t.channel_type == ChannelType.WhatsappChannel)

            if (sender_details.isEmpty) {

              throw new Exception(CampaignService.whatsappChannelSettingsNotFoundError)

            } else {

              val from_name = sender_details.get.first_name + " " + sender_details.get.last_name

              val whatsapp_number = sender_details.get.phone_number.get

              PreviousFollowUpData.WhatsappFollowUp(
                body = data.body,
                base_body = data.body,
                from_name = from_name,
                whatsapp_number = whatsapp_number
              )
            }

          case data: TaskData.CallTaskData =>

            val sender_details = channel_setting_details.find(t => t.channel_type == ChannelType.CallChannel)

            if (sender_details.isEmpty) {

              throw new Exception(CampaignService.callChannelSettingsNotFoundError)

            } else {

              val from_name = sender_details.get.first_name + " " + sender_details.get.last_name

              val phone_number = sender_details.get.phone_number.get

              PreviousFollowUpData.CallFollowUp(
                body = data.body,
                base_body = data.body,
                from_name = from_name,
                phone_number = phone_number
              )
            }

          case data: TaskData.GeneralTaskData =>

            PreviousFollowUpData.GeneralFollowUp(
              body = data.task_notes,
              base_body = data.task_notes
            )

          case data: TaskData.AutoEmailMagicContentData =>

            logger.shouldNeverHappen("getCompletedTasksForProspectInCampaign AutoEmailMagicContentData this task has only two status -> PendingApproval and Approved ")
            throw new Exception("getCompletedTasksForProspectInCampaign :: TaskData.AutoEmailMagicContentData :: found ")


          case data: TaskData.ManualEmailMagicContentData =>

            logger.shouldNeverHappen("getCompletedTasksForProspectInCampaign ManualEmailMagicContentData this task has only two status -> PendingApproval and Approved ")
            throw new Exception("getCompletedTasksForProspectInCampaign :: TaskData.ManualEmailMagicContentData :: found ")



        }

        val completedAt = task.status match {
          case status: TaskStatus.Done =>
            status.done_at

          case _ =>
            logger.debug(s"Impossible case, We are only fetching done tasks from db but found of type: ${task.status} taskId: ${task.task_id}")
            DateTime.now() // This case should never happen as we are only find done tasks from db
        }

        PreviousFollowUp(
          channel_follow_up_data = channel_follow_up_data,
          sent_at = completedAt,
          timezone = timeZone,
          step_id = task.step_id,
          completed_reason = None,
        )
      })

    }
  }

  // No cache changes currently -
  // Please verify I think updated at should also be changed when updating reply sentiment

  /**
   * Get clarity over doer
   */
  def addReplySentimentForTask(
                      campaignId: Option[CampaignId],
                      accountId: AccountId,
                      teamId: TeamId,
                      taskUuid: TaskUuid,
                      replySentimentUuid: ReplySentimentUuid,
                      prospect_id: ProspectId,
                      call_sp_sid: Option[CallSID],
                      conference_uuid: Option[ConferenceUuid],
                      permittedAccountIds: Seq[Long],
                      task_type: TaskType
                      )(implicit ec: ExecutionContext, logger: SRLogger): Try[true] = {
    val dbAndSession = dbUtils.startLocalTx()
    val db = dbAndSession.db
    val session = dbAndSession.session
    val result: Try[true] = for {
      saveTasksSentiment: TaskUuid <- taskDaoService.saveSentimentForTask( // Task table
        taskUuid = taskUuid,
        teamId = teamId,
        replySentimentUuid = replySentimentUuid
      )(session)

      reply_sentiment: Option[ReplySentimentForTeam] <- replySentimentDAO.getReplySentimentsForUUID(
        team_id = teamId.id,
        uuid = replySentimentUuid
      )

      campaignProspect: Option[Long] <- {
        if (campaignId.isEmpty) {
          Success(None) // Its for tasks without campaign
        } else {
          campaignProspectDAO.addReplySentimentViaTask( // campaign Prospect Table
            campaignId = campaignId.get, //will always be there
            prospect_id = prospect_id,
            reply_sentiment_uuid = replySentimentUuid,
            teamId = teamId,
            reply_sentiment_updated_by = User
          )(session)
        }
      }

      prospectsTable: List[Long] <- prospectDAO.addingLatestReplySentimentForProspect( // prospect
        team_id = teamId.id,
        prospectIds = Seq(prospect_id.id),
        reply_sentiment_uuid = replySentimentUuid
      )(session)



      call_conference_logs_table: Int <- {

        task_type match {

          case TaskType.CallTask =>



            if (call_sp_sid.isEmpty) {

              if (conference_uuid.isEmpty) {
                /*Currently in the mark as done flow from extension and mobile app,
                the conf_uuid and call_sp_sid will not be present*/

                Try {
                  0
                }
              } else {

                callDAO.addReplySentimentUUIDToConferenceLogs(
                  reply_sentiment_uuid = replySentimentUuid,
                  callSidOrConfUuid = CallSidOrConfUuid.WithConfUuid(conference_uuid.get),
                  team_id = teamId
                )(session)

              }

            } else {

              callDAO.addReplySentimentUUIDToConferenceLogs(
                reply_sentiment_uuid = replySentimentUuid,
                callSidOrConfUuid = CallSidOrConfUuid.WithCallSid(call_sp_sid.get),
                team_id = teamId
              )(session)

            }




          case TaskType.GeneralTask | TaskType.SendSms | TaskType.SendEmail | TaskType.SendLinkedinConnectionRequest |
               TaskType.SendLinkedinInMail | TaskType.SendWhatsAppMessage | TaskType.SendLinkedinMessage | TaskType.ViewLinkedinProfile
               | TaskType.AutoViewLinkedinProfile | TaskType.AutoLinkedinMessage
               | TaskType.AutoLinkedinInmail | TaskType.AutoLinkedinConnectionRequest
               | TaskType.AutoEmailMagicContent | TaskType.ManualEmailMagicContent =>
            Try {
              0
            }

        }
      }



      call_participant_table: Int <- {

        task_type match {

          case TaskType.CallTask =>

            if (call_sp_sid.isEmpty) {

              Try {
                0
              }

            } else {

              callDAO.addReplySentimentUUID(
                reply_sentiment_uuid = replySentimentUuid,
                call_sp_sid = call_sp_sid.get,
                team_id = teamId
              )(session)

            }

          case _ =>
            Try {
              0
            }

        }
      }

      _ <- eventLogService.createEventLog(
        event_data_type =
          EventDataType.ActivityEventDataType.ReplySentimentUpdatedEventData.ReplySentimentUpdatedEventDataTask(
            teamId = teamId,
            accountId = accountId,
            prospectId = prospect_id,
            replySentimentUuid = replySentimentUuid,
            taskId = taskUuid
          ),
        teamId = teamId.id,
        accountId = accountId.id
        
        
      ) match {

        case Left(exception) => exception match {
          case CreateEventLogError.SQLException(error) =>
            logger.fatal(s"CRITICAL: createEventLog for event_log_id_for_created_ids failed:", error)

          case CreateEventLogError.CreateEventLogFailedInsert(message) =>
            logger.fatal(s"CRITICAL: createEventLog for event_log_id_for_created_ids failed: message $message")
        }
          Try{0}

        case Right(_) => Try{0}
      }

      prospect_events <- {
        val events = CreateProspectEventDB(

          event_type = EventType.REPLY_SENTIMENT_UPDATED,
          doer_account_id = Some(accountId.id),
          doer_account_name = None,

          assigned_to_account_id = None,
          assigned_to_account_name = None,

          old_category = None,
          new_category = None,

          prospect_id = prospect_id.id,
          email_thread_id = None,

          campaign_id = campaignId.map(_.id),
          campaign_name = None,

          step_id = None,
          step_name = None,

          clicked_url = None,

          account_id = accountId.id,
          team_id = teamId.id,
          email_scheduled_id = None,

          created_at = DateTime.now(),

          task_type = Some(task_type),
          channel_type = None,
          task_uuid = Some(taskUuid.uuid),
          call_conference_uuid = conference_uuid,
          new_reply_sentiment_uuid = Some(replySentimentUuid),

          duplicates_merged_at = None,
          total_merged_prospects = None,
          potential_duplicate_prospect_id = None

        )


        prospectAddEventDAO.addEvents(events = Seq(events))
      }

      _ : Boolean <- if(reply_sentiment.isDefined && campaignId.isDefined) {
        reply_sentiment.get.reply_sentiment.getReplySentimentType match {
          case ReplySentimentType.Positive |
               ReplySentimentType.Negative |
               ReplySentimentType.Referral |
               ReplySentimentType.Objection |
               ReplySentimentType.DoNotContact => {

            val pauseCampaignData: PauseCampaignData = PauseCampaignData(
              campaignId = campaignId.get,
              accountId = accountId,
              teamId = teamId,
              taskUuid = taskUuid,
              replySentimentUuid = replySentimentUuid,
              permittedAccountIds = permittedAccountIds,
              prospect_id = prospect_id,
              task_type = task_type
            )


            mqPauseCampaignOnReplySentimentSelect.publish(msg = pauseCampaignData) match {

              case Failure(exception) =>
                Failure(exception)

              case Success(_) =>
                Success(true)

            }
          }
          case ReplySentimentType.Other |
               ReplySentimentType.Uncategorized |
               ReplySentimentType.FollowUpNeeded  // FIXME Critical: Pausing campaign on follow up needed should happen via sub category check 
          =>
            Success(true)
        }
      } else {
        Success(true)
      }

    } yield {
      true
    }

    dbUtils.commitAndCloseSession(db = db)

    result match {
      case Failure(exception) => Failure(exception)

      case Success(_) =>
        mqAutoUpdateProspectCategoryPublisher.publish(
          msg = MqAutoUpdateProspectCategoryMsg(
          replySentimentUuid = Some(replySentimentUuid),
          teamId = teamId,
          doerAccountId = accountId,
          prospectIds = Seq(prospect_id),
          newProspectCategory = None
        )).map(_ => true)

    }
  }

  def deleteNotCompletedTaskForDeletedCampaign(campaignId: CampaignId, teamId: TeamId)(implicit session: DBSession): Try[List[String]] = {
    taskDaoService.deleteNotCompletedTaskForDeletedCampaign(campaignId = campaignId, teamId = teamId)
  }

  def groupDuplicatedTasks()(using logger: SRLogger, session: DBSession): Try[List[GroupDuplicateTaskDetails]] = {

    val res = taskDaoService.getGroupDuplicatedTasks()

    res.map(data => {
      logger.info(s"[DeletingDuplicates] duplicate tasks found : ${data}")
    })


    res

  }

  def findAllTasksForGroupDetails(
                                   step_id: StepId,
                                   campaignId: CampaignId,
                                   prospectId: ProspectId,
                                   team_id: TeamId
                                 )
                                 (using logger: SRLogger, session: DBSession): Try[List[Task]] = {

    taskDaoService.findAllTasksRelatedToGroup(
      step_id = step_id,
      campaignId = campaignId,
      prospectId = prospectId,
      team_id = team_id
    )

  }

  private def checkAndPassErrorMessageCaptainDataWebhook(error: Option[String]): Boolean = {
    // Check if error is defined
    if (error.isDefined) {
      val errorMessage = error.get

      // Hard-coded error conditions - add more as needed
      val errorConditions = List(
        "re-send"
      )

      // Check if any of the conditions are met
      errorConditions.exists(condition => errorMessage.contains(condition))
    } else {
      false
    }
  }


  def handleCaptainDataWebhookStatusUpdate(
                                            data: CaptainDataWebhookResponse,
                                            status: CaptainDataWorkflowStatus
                                          )(using logger: SRLogger, ec: ExecutionContext):  Future[CDJobUid] = {
    for {
      task_uuid: TaskUuid <- Future.fromTry(
          taskDaoService.handleCaptainDataWebhookStatusUpdate(data = data, status = status)
      )

      task_data: TaskDetailsToProcessWebhookUpdate <-Future.fromTry(taskDaoService.fetchTaskDetailsViaTaskIdAndJobUid(
          task_uuid = task_uuid,
          job_uid = data.job_uid,
        ))

      _ <- status match {
        case CaptainDataWorkflowStatus.INITIATED |
             CaptainDataWorkflowStatus.CREATED =>
          Future.successful(
            logger.info(s"Task Status update not required task_id :: ${task_uuid.uuid} :: status :: ${status.toString}")
          )

        case CaptainDataWorkflowStatus.FAILURE =>
          if(checkAndPassErrorMessageCaptainDataWebhook(error = data.error)){
            changeStatus(
              task_id = task_data.taskId.uuid,
              task_status = UpdateTaskStatus.Done(),
              orgId = task_data.orgId,
              changeStatusPermissionCheck = ChangeStatusPermissionCheck.AutomatedTaskCheck(
                job_uid = data.job_uid.uid
              ),
              team_id = task_data.teamId.id,
            ).map {
              case Left(ChangeStatusTypeError.ErrorWhileChangingStatus) =>
                logger.shouldNeverHappen(s"Error while changing Status for task_id :: ${task_uuid.uuid} :: team_id :: ${task_data.teamId.id}")

              case Right(task_id) =>
                logger.info(s"Task Status updated to success successfully for task_id :: ${task_uuid.uuid} :: team_id :: ${task_data.teamId.id} :: status :: ${status.toString} failure reason:: ${data.error.getOrElse("")} ")
            }

          }else{
            Future.successful(
              logger.info(s"Task Status update not required task_id :: ${task_uuid.uuid} :: status :: ${status.toString}")
            )

          }

        case CaptainDataWorkflowStatus.SUCCESSFUL =>
         changeStatus(
            task_id = task_data.taskId.uuid,
            task_status = UpdateTaskStatus.Done(),
            orgId = task_data.orgId,
            changeStatusPermissionCheck = ChangeStatusPermissionCheck.AutomatedTaskCheck(
              job_uid = data.job_uid.uid
            ),
            team_id = task_data.teamId.id,
          ).map {
           case Left(ChangeStatusTypeError.ErrorWhileChangingStatus) =>
             logger.shouldNeverHappen(s"Error while changing Status for task_id :: ${task_uuid.uuid} :: team_id :: ${task_data.teamId.id}")

           case Right(task_id) =>
             logger.info(s"Task Status updated successfully for task_id :: ${task_uuid.uuid} :: team_id :: ${task_data.teamId.id} :: status :: ${status.toString}")
         }
      }

    } yield {
      data.job_uid
    }
  }


  def allDueDuplicate(
                       step_id: StepId,
                       campaign_id: CampaignId,
                       prospect_id: ProspectId,
                       tasks: List[Task]
                     )(using logger: SRLogger, session: DBSession): Try[List[String]] = Try {


    if (tasks.nonEmpty) {


      val sorted_tasks = tasks.sortBy(_.created_at).tail

      taskDaoService.deleteDuplicateTasks(
        tasks = sorted_tasks
      ) match {

        case Failure(e) =>

          logger.error(s"[DeletingDuplicates] Error while deleting all due duplicate tasks ", e)
          throw e

        case Success(listOfTaskIds) =>

          val sorted_task_task_ids = sorted_tasks.map(_.task_id)
          val task_not_deleted: List[String] = sorted_task_task_ids.diff(listOfTaskIds)

          if (task_not_deleted.isEmpty) {

            logger.info(s"[DeletingDuplicates] : all task deleted deleted: ${listOfTaskIds}")

            listOfTaskIds


          } else {


            logger.error(s"[DeletingDuplicates] : these tasks got not deleted : ${task_not_deleted}")

            listOfTaskIds

          }

      }

      // taskDaoService.deleteTasks(tasks = sorted_tasks)

    } else {

      logger.info(s"[DeletingDuplicates] the task list provided was empty : all due duplicate")
      List()

    }
  }

  def updateCampaignProspectForDuplicatedTasks(
                                                task: Task,
                                                step_id: StepId,
                                                prospect_id: ProspectId,
                                                campaign_id: CampaignId
                                              )(implicit session: DBSession, logger: SRLogger) = {

    campaignProspectDAO.checkIfCampaignProspectUpdated(
      current_step_task_id = task.task_id,
      current_step_id = step_id,
      campaignId = campaign_id,
      prospectId = prospect_id,
      teamId = TeamId(task.team_id)
    ) match {
      case Failure(e) =>

        logger.error(s"[DeletingDuplicates] :Error while fetching status of this task: ${task.task_id} :: stepId : ${step_id.id} :: campaignId: ${campaign_id.id} :: prospectID : ${prospect_id.id}", e)

      case Success(None) =>

      // pass its not present

      case Success(Some(data)) =>

        data.currentStepStatusForScheduler match {

          case CurrentStepStatusForScheduler.Done |
               CurrentStepStatusForScheduler.PendingApproval |
               CurrentStepStatusForScheduler.AiContentQueued |
               CurrentStepStatusForScheduler.Approved =>

          // no change in this case


          case CurrentStepStatusForScheduler.Skipped =>


            if (task.status.status_type == TaskStatusType.Done) {

              val current_step_type = CampaignStepType.fromKey(task.task_type.toKey).get
              // this will never fail as we are already coming here only if we have a task_status type done/skipped which will be converted from fromKey
              val current_step_status_data: CurrentStepStatusForSchedulerData = CurrentStepStatusForSchedulerData.getDataFromTaskStatus(task.status).get

              /*
               using .get below because they will be always be present when tasks are created via scheduler
               */
              campaignProspectDAO._updateScheduledStatus(
                Seq(CampaignProspectUpdateScheduleStatus(
                  current_step_status_for_scheduler_data = current_step_status_data,
                  current_step_type = current_step_type,
                  current_step_task_id = task.task_id,
                  step_id = task.step_id.get,
                  campaign_id = task.campaign_id.get,
                  prospect_id = task.prospect.get.id,
                  email_message_id = None,
                  current_campaign_email_settings_id = None //this is none as this is not a email step
                ))
              ) match {
                case Failure(exception) =>
                  logger.fatal("[DeletingDuplicates] : _updateScheduledStatus db failure", exception)
                case Success(updateCPprospectIds) =>
                  if (updateCPprospectIds.length != 1) {
                    logger.fatal(s"[DeletingDuplicates] : _updateScheduledStatus:: result length cannot be zero")
                  }
                  logger.info(s"[DeletingDuplicates] : successfully updated the campaign_prospect for task_id: ${task.task_id}")
              }
            }


          case CurrentStepStatusForScheduler.Due =>

            val current_step_type = CampaignStepType.fromKey(task.task_type.toKey).get
            // this will never fail as we are already coming here only if we have a task_status type done/skipped which will be converted from fromKey
            val current_step_status_data: CurrentStepStatusForSchedulerData = CurrentStepStatusForSchedulerData.getDataFromTaskStatus(task.status).get

            /*
             using .get below because they will be always be present when tasks are created via scheduler
             */
            campaignProspectDAO._updateScheduledStatus(
              Seq(CampaignProspectUpdateScheduleStatus(
                current_step_status_for_scheduler_data = current_step_status_data,
                current_step_type = current_step_type,
                current_step_task_id = task.task_id,
                step_id = task.step_id.get,
                campaign_id = task.campaign_id.get,
                prospect_id = task.prospect.get.id,
                email_message_id = None,
                current_campaign_email_settings_id = None //this is none as this is not a email step
              ))
            ) match {
              case Failure(exception) =>
                logger.fatal("[DeletingDuplicates] : _updateScheduledStatus db failure", exception)
              case Success(updateCPprospectIds) =>
                if (updateCPprospectIds.length != 1) {
                  logger.fatal(s"[DeletingDuplicates] : _updateScheduledStatus:: result length cannot be zero")
                }
                logger.info(s"[DeletingDuplicates] : successfully updated the campaign_prospect for task_id: ${task.task_id}")
            }
        }
    }

  }

  def deleteDulplicateTasksAndUpdateCampaignProspectIfNeeded(
                                                              step_id: StepId,
                                                              campaign_id: CampaignId,
                                                              prospect_id: ProspectId,
                                                              all_tasks: List[Task],
                                                              task_not_to_delete: List[Task],
                                                              teamId: TeamId
                                                            )(using logger: SRLogger, session: DBSession): Try[List[String]] = {


    /*
    8. leaving that task delete all other tasks [ duplicate deleted ]
    9. check if campaign_prospect table is updated for this.
    10. select data from campaign_prospect table for step_id -> , campaign_id -> , prospect_id ->
    11. now check is step_status is done or not, if not done
    12. update campaign_prospect table set current_step_status for scheduler : done,
    13. fetch next campaign step for this campaign. then update its data in db
     */


    // tasks_to_delete // DELETE THESE TASKS


    // check campaign_prospect table is updated
    // if an entry found for them same step_id, prospect_id, campaign_id in table. its not updated.


    // if not updated fetch details of campaign and update details


    // else leave it

    // we are filtering out all the tasks which are not done/skipped

    val tasks_to_delete = all_tasks.filterNot(t => task_not_to_delete.map(_.task_id).contains(t.task_id))


    for {

      delete_due_tasks: List[String] <- {
        taskDaoService.deleteDuplicateTasks(
          tasks = tasks_to_delete
        )
      }

      update_campaign_prospect_if_needed: Unit <- Try {

        if (task_not_to_delete.nonEmpty) {

          val task_head = task_not_to_delete.head
          task_head.created_via match {

            case TaskCreatedVia.Scheduler =>

              // Campaign Prospect table only changes when tasks are done or skipped
              task_head.status.status_type match {

                case TaskStatusType.Done =>

                  updateCampaignProspectForDuplicatedTasks(
                    task = task_head,
                    step_id = step_id,
                    prospect_id = prospect_id,
                    campaign_id = campaign_id
                  )


                case TaskStatusType.Skipped =>


                  updateCampaignProspectForDuplicatedTasks(
                    task = task_head,
                    step_id = step_id,
                    prospect_id = prospect_id,
                    campaign_id = campaign_id
                  )


                case TaskStatusType.Snoozed =>

                // pass no changed needed in CampaignProspect table
                case TaskStatusType.Due =>

                // pass no changed needed in CampaignProspect table

                case TaskStatusType.Archive =>

                // pass no changed needed in CampaignProspect table [ discussed with aditya ]
                case TaskStatusType.PushedToLinkedinExecutionQueue =>

                // pass no changed needed in CampaignProspect table [ discussed with aditya ]
                case TaskStatusType.Failed =>

                // pass no changed needed in CampaignProspect table [ discussed with aditya ]

                case TaskStatusType.QueuedToMq =>

                  // pass no changed needed in CampaignProspect table

                case TaskStatusType.PendingApproval =>

                // pass no changed needed in CampaignProspect table

                case TaskStatusType.Approved =>

                // pass no changed needed in CampaignProspect table

              }


            case TaskCreatedVia.Manual | TaskCreatedVia.Call_action | TaskCreatedVia.AiGenerated =>

            // no need to update campaign_prospect table

          }
        }
      }


    } yield {

      delete_due_tasks

    }
  }

  def checkIfTaskAlreadyExists(
                                step_id: StepId,
                                teamId: TeamId,
                                prospectId: ProspectId,
                                is_magic_step: Boolean

                              )(using logger: SRLogger): Try[false] = {

    taskDaoService.checkIfTaskAlreadyExists(
      step_id = step_id,
      teamId = teamId,
      prospectId = prospectId,
      is_magic_step = is_magic_step
    )

  }

  def deleteDuplicateTasks()(using logger: SRLogger): Try[Map[GroupDuplicateTaskDetails, List[String]]] = {

    /*
    1. group all tasks with same step_id, campaign_id, prospect_id and count > 1
    2. iterate through each of these step_id, campaign_id, prospect_id selected
    3. find all tasks related to that step_id, campaign_id, prospect_id
    4. check if the task is done or Skipped-> missed
    5. (Not Done | skipped)
    6. then sort all the tasks based on created_at and delete all tasks leaving first one: [ duplicate deleted ]
    7. ( Done found | Skipped Found )
    8. leaving that task delete all other tasks [ duplicate deleted ]
    9. check if campaign_prospect table is updated for this.
    10. select data from campaign_prospect table for step_id -> , campaign_id -> , prospect_id ->
    11. now check is step_status is done or skipped, if not done or skipped then
    12. update campaign_prospect table set current_step_status for scheduler : done | skipped,
    13. fetch next campaign step for this campaign. then update its data in db
    */


    val dbAndSession = dbUtils.startLocalTx()
    val db = dbAndSession.db
    implicit val session = dbAndSession.session

    val deleted_tasks = for {

      group_duplicated_tasks_details: List[GroupDuplicateTaskDetails] <- groupDuplicatedTasks()

      iterate_through_each_group_details_and_find_tasks: Map[GroupDuplicateTaskDetails, List[Task]] <- {

        var result: Map[GroupDuplicateTaskDetails, List[Task]] = Map()

        group_duplicated_tasks_details.foreach(group => {

          findAllTasksForGroupDetails(
            step_id = group.step_id,
            campaignId = group.campaignId,
            prospectId = group.prospectId,
            team_id = group.team_id
          ) match {
            case Failure(e) =>

              logger.error(s"[DeletingDuplicates] error while finding tasks for for team_id: ${group.team_id.id} step_id : ${group.step_id}, campaign_id: ${group.campaignId}, prospect_id: ${group.prospectId} : err", e)

            case Success(data) =>

              result += (group -> data)

              logger.info(s"[DeletingDuplicates] found tasks for team_id: ${group.team_id.id}  step_id : ${group.step_id}, campaign_id: ${group.campaignId}, prospect_id: ${group.prospectId} : tasks: ${data}")


          }
        })

        logger.info(s"[DeletingDuplicates] mapped tasks : ${result}")
        Try {
          result
        }
      }

      delete_tasks: Map[GroupDuplicateTaskDetails, List[String]] <- {

        var deleted_items: Map[GroupDuplicateTaskDetails, List[String]] = Map()

        iterate_through_each_group_details_and_find_tasks.foreach(key_value => {

          // check if single task is done:
          val checking_if_at_least_single_task_done: List[Task] = key_value._2.filter(_.status.status_type == TaskStatusType.Done)
          val checking_if_at_least_single_task_skipped: List[Task] = key_value._2.filter(_.status.status_type == TaskStatusType.Skipped)
          val checking_if_at_least_single_task_snoozed: List[Task] = key_value._2.filter(_.status.status_type == TaskStatusType.Snoozed)
          val checking_if_at_least_single_task_archived: List[Task] = key_value._2.filter(_.status.status_type == TaskStatusType.Archive)

          val deleted_tasks: Try[List[String]] = if (checking_if_at_least_single_task_done.nonEmpty) {

            deleteDulplicateTasksAndUpdateCampaignProspectIfNeeded(
              step_id = key_value._1.step_id,
              campaign_id = key_value._1.campaignId,
              prospect_id = key_value._1.prospectId,
              all_tasks = key_value._2,
              task_not_to_delete = checking_if_at_least_single_task_done,
              teamId = key_value._1.team_id
            )

          } else if (checking_if_at_least_single_task_skipped.nonEmpty) {

            deleteDulplicateTasksAndUpdateCampaignProspectIfNeeded(
              step_id = key_value._1.step_id,
              campaign_id = key_value._1.campaignId,
              prospect_id = key_value._1.prospectId,
              all_tasks = key_value._2,
              task_not_to_delete = checking_if_at_least_single_task_skipped,
              teamId = key_value._1.team_id
            )


          } else if (checking_if_at_least_single_task_snoozed.nonEmpty) {

            deleteDulplicateTasksAndUpdateCampaignProspectIfNeeded(
              step_id = key_value._1.step_id,
              campaign_id = key_value._1.campaignId,
              prospect_id = key_value._1.prospectId,
              all_tasks = key_value._2,
              task_not_to_delete = checking_if_at_least_single_task_snoozed,
              teamId = key_value._1.team_id
            )

          } else if (checking_if_at_least_single_task_archived.nonEmpty) {

            deleteDulplicateTasksAndUpdateCampaignProspectIfNeeded(
              step_id = key_value._1.step_id,
              campaign_id = key_value._1.campaignId,
              prospect_id = key_value._1.prospectId,
              all_tasks = key_value._2,
              task_not_to_delete = checking_if_at_least_single_task_archived,
              teamId = key_value._1.team_id
            )

          } else {

            allDueDuplicate(
              step_id = key_value._1.step_id,
              campaign_id = key_value._1.campaignId,
              prospect_id = key_value._1.prospectId,
              tasks = key_value._2
            )


          }

          deleted_tasks match {

            case Failure(e) =>

              logger.error(s"[DeletingDuplicates] : error while deletion ", e)

              deleted_items += ((key_value._1) -> List()) // dont' want to throw error

            case Success(d) =>

              logger.info(s"[DeletingDuplicates] : success while deletion key: ${key_value._1}, task_ids that got deleted : ${d}")

              deleted_items += ((key_value._1) -> d)


          }

        })

        Try {
          deleted_items
        }


      }


      check_grouped_tasks_again_if_any_not_deleted: Int <- {

        groupDuplicatedTasks() match {

          case Failure(e) =>

            logger.error(s"Error while fetching grouping duplicate task again", e)
            Try {
              0
            }

          case Success(d) =>

            d.foreach(group => {

              logger.info(s"[DeletingDuplicates] This group is remaining ::  step_id :  ${group.step_id} :: campaign_id: ${group.campaignId} :: prospect_id : ${group.prospectId} :: count : ${group.count}")

            })
            Try {
              0
            }

        }

      }

    } yield {
      delete_tasks
    }

    dbUtils.commitAndCloseSession(db = db)

    deleted_tasks

  }

  def getTaskDataForProspectRevert(
                                    campaign_id: CampaignId,
                                    prospect_id: ProspectId,
                                    step_id: StepId
                                  )(implicit session: DBSession): Try[Option[RevertData.TaskRevertData]] = {

    taskDaoService.getTaskDataForProspectRevert(
      campaign_id = campaign_id,
      prospect_id = prospect_id,
      step_id = step_id
    )

  }

  def deleteTaskForRevert(
                           task_id: String,
                           team_id: TeamId
                         )(
                           using Logger: SRLogger
                         ): Try[OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId] = {

    taskDaoService.deleteTaskForRevert(
      taskIds = List(task_id),
      team_id = team_id,
      status = Some(TaskStatusType.Due),
      permittedAccountIds = Seq()
    ) match {

      case Failure(err) =>

        Logger.error(s"deleteTaskForRevert : error while deleting task task_id_${task_id} team_id: ${team_id.id}", err)

        Failure(new Exception(err))


      case Success(value) =>

        Logger.info(s"deleteTaskForRevert : success while deleting task task_id_${task_id} team_id: ${team_id.id}")

        Success(value.headOption.getOrElse(
          OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId(
            campaignId = None,
            prospectId = None,
            emailSettingId = None,
            taskId = Some(TaskId(task_id)),
            step_id = None,
            team_id = team_id
          )
        ))

    }

  }

  def updateTasksDataForMergeDuplicates(
                                         duplicateProspects: List[ProspectIdAndPotentialDuplicateProspectId],
                                         masterProspectId: ProspectId,
                                         teamId: TeamId
                                       ): Try[DeleteAndUpdateTaskResultForMergeDuplicates] = {

    taskDaoService.deleteNotDoneTasksAndUpdateMasterProspectIdDone(
      teamId = teamId,
      masterProspectId = masterProspectId,
      duplicateProspects = duplicateProspects
    )

  }

  def findMagicContentTaskByDetails(
                                     campaignId: CampaignId,
                                     stepId: StepId,
                                     prospectId: ProspectId,
                                     teamId: TeamId
                                   )(implicit ec: ExecutionContext): Future[Option[TaskData]] = {
    Future.fromTry(
      taskDaoService.findMagicContentTaskByDetails(
        campaignId = campaignId,
        stepId = stepId,
        prospectId = prospectId,
        teamId = teamId
      )
    )
  }

}

case class GroupDuplicateTaskDetails(
                                      step_id: StepId,
                                      campaignId: CampaignId,
                                      prospectId: ProspectId,
                                      team_id: TeamId,
                                      count: Long
                                    )

sealed trait ParamValidationError

object ParamValidationError {

  //  case class InvalidTaskTypeError(msg: String) extends ParamValidationError
  //  case class InvalidAssigneeIdError(msg: String) extends ParamValidationError
  //  case class InvalidTaskStatusError(msg: String) extends ParamValidationError
  //  case class InvalidTaskPriorityError(msg: String) extends ParamValidationError

  case class ParamValidationFailed(msg: String) extends ParamValidationError


}

object TaskService {

  private def getSubCountForTaskCount(
                                       tasks: List[Task]
                                     ): SubCount = {

    val all: Int = tasks.length
    var emailCount: Int = 0
    var linkedinCount: Int = 0
    var smsCount: Int = 0
    var callCount: Int = 0
    var whatsAppCount: Int = 0
    var genericCount: Int = 0
    var approvalEmailCount = 0
    var approvalLinkedinCount = 0
    var approvalCallCount = 0
    var approvalSmsCount = 0
    var approvalGenericCount = 0
    var approvalWhatsappCount = 0

      val nonApprovalTasks = tasks.filter(task => task.status.status_type != PendingApproval)

    nonApprovalTasks.foreach(t => t.task_type.channelType match {
      case ChannelType.EmailChannel =>
        emailCount += 1

      case ChannelType.LinkedinChannel =>
        linkedinCount += 1

      case ChannelType.SmsChannel =>
        smsCount += 1

      case ChannelType.CallChannel =>
        callCount += 1

      case ChannelType.WhatsappChannel =>
        whatsAppCount += 1

      case ChannelType.GeneralChannel =>
        genericCount += 1

      case ChannelType.IndependentChannel =>
        println(" getSubCountForTaskCount ")
    }
    )

      val approvalTasks = tasks.filter(task => task.status.status_type == PendingApproval)


      approvalTasks.foreach(t => t.task_type.channelType match {
          case ChannelType.EmailChannel =>
              approvalEmailCount += 1

          case ChannelType.LinkedinChannel =>
              approvalLinkedinCount += 1

          case ChannelType.SmsChannel =>
              approvalSmsCount  += 1

          case ChannelType.CallChannel =>
              approvalCallCount += 1

          case ChannelType.WhatsappChannel =>
              approvalWhatsappCount += 1

          case ChannelType.GeneralChannel =>
              approvalGenericCount += 1

          case ChannelType.IndependentChannel =>
              println(" getSubCountForTaskCount ")
      })

    SubCount(
      all = all,
      email = emailCount,
      linkedin = linkedinCount,
      sms = smsCount,
      whatsapp = whatsAppCount,
      call = callCount,
      generic = genericCount,
        approval_email = approvalEmailCount,
        approval_call = approvalCallCount,
        approval_linkedin = approvalLinkedinCount,
        approval_sms = approvalSmsCount,
        approval_whatsapp = approvalWhatsappCount,
        approval_generic = approvalGenericCount
    )

  }

  def validateDateRange(duration_from: Option[Long], duration_to: Option[Long]): Try[Option[DateRangeBetween]] = {

      (duration_from, duration_to) match {
        case (Some(from), Some(to)) if from > to =>
          Failure(new Exception(("duration_from cannot be later than duration_to")))
        case (Some(from), Some(to)) =>
          Success(Some(DateRangeBetween(new DateTime(from), new DateTime(to))))
        case (Some(_), None) =>
          Failure(new Exception(("duration_to must be specified if duration_from is provided")))
        case (None, Some(_)) =>
          Failure(new Exception("duration_from must be specified if duration_to is provided"))
        case (None, None) =>
          Success(None)
      }

  }

  def paginationValidation(
                            timeBasedTaskType: Option[TimeBasedTaskType],
                            older_than: Option[Long],
                            newer_than: Option[Long],
                            page_size: Option[Int],
                            duration_from: Option[Long],
                            duration_to: Option[Long],
                            timeZone: String
                          ): Either[ParamValidationError, (Boolean, ValidatedTaskReq.ValidatedTaskReqRange)] = {

    val max_page_size_for_fetch_at_time = 500
    val max_page_limit_from_user = 100
    val min_page_limit_from_user = 3
    val default_page_size = 20

    val startOfDay = startOfTheDay(timeZone = timeZone)
    val endOftheDay = endOfTheDay(timeZone = timeZone)

    val dateRangeValidation: Either[ParamValidationError, Option[DateRangeBetween]] = {
      validateDateRange(
        duration_from = duration_from,
        duration_to = duration_to
      ) match {

        case Failure(exception) => Left(ParamValidationError.ParamValidationFailed(exception.getMessage))

        case Success(duration) => Right(duration)
      }
    }

    val timeLineValidation: Either[ParamValidationError, (Boolean, InferredQueryTimeline.Range)] = if (older_than.isDefined && newer_than.isDefined) {
      Left(ParamValidationError.ParamValidationFailed("Cannot specify older_than and newer than together "))
    } else {
      timeBasedTaskType match {
        case None =>

          Right((false, InferredQueryTimeline.Range.Before(endOftheDay)))

        case Some(tbtt) => tbtt match {

          case TimeBasedTaskType.TodayAndDue =>

            if (older_than.isDefined) {
              Right((false, InferredQueryTimeline.Range.Before(new DateTime(older_than.get))))
            } else if (newer_than.isDefined) {
              Right((false, InferredQueryTimeline.Range.After(new DateTime(newer_than.get))))
            } else {
              Right((true, InferredQueryTimeline.Range.Before(endOftheDay)))
            }

          case TimeBasedTaskType.Today =>
            if (older_than.isDefined) {
              Right((false, InferredQueryTimeline.Range.Before(new DateTime(older_than.get))))
            } else if (newer_than.isDefined) {
              Right((false, InferredQueryTimeline.Range.After(new DateTime(newer_than.get))))
            } else {
              Right((true, InferredQueryTimeline.Range.Before(endOftheDay)))
            }

          case TimeBasedTaskType.Upcoming =>
            if (older_than.isDefined) {
              Right((false, InferredQueryTimeline.Range.Before(new DateTime(older_than.get))))
            } else if (newer_than.isDefined) {
              Right((false, InferredQueryTimeline.Range.After(new DateTime(newer_than.get))))
            } else {
              Right((true, InferredQueryTimeline.Range.After(endOftheDay)))
            }

          case TimeBasedTaskType.Due =>
            if (older_than.isDefined) {
              Right((false, InferredQueryTimeline.Range.Before(new DateTime(older_than.get))))
            } else if (newer_than.isDefined) {
              Right((false, InferredQueryTimeline.Range.After(new DateTime(newer_than.get))))
            } else {
              Right((true, InferredQueryTimeline.Range.Before(startOfDay)))
            }

          case TimeBasedTaskType.Completed =>
            if (older_than.isDefined) {
              Right((false, InferredQueryTimeline.Range.Before(new DateTime(older_than.get))))
            } else if (newer_than.isDefined) {
              Right((false, InferredQueryTimeline.Range.After(new DateTime(newer_than.get))))
            } else {
              Right((true, InferredQueryTimeline.Range.Before(endOftheDay)))
            }

          case TimeBasedTaskType.Skipped =>
            if (older_than.isDefined) {
              Right((false, InferredQueryTimeline.Range.Before(new DateTime(older_than.get))))
            } else if (newer_than.isDefined) {
              Right((false, InferredQueryTimeline.Range.After(new DateTime(newer_than.get))))
            } else {
              Right((true, InferredQueryTimeline.Range.Before(endOftheDay)))
            }

          case TimeBasedTaskType.Snoozed =>
            if (older_than.isDefined) {
              Right((false, InferredQueryTimeline.Range.Before(new DateTime(older_than.get))))
            } else if (newer_than.isDefined) {
              Right((false, InferredQueryTimeline.Range.After(new DateTime(newer_than.get))))
            } else {
              Right((true, InferredQueryTimeline.Range.Before(endOftheDay)))
            }

          case TimeBasedTaskType.Archived =>
            if (older_than.isDefined) {
              Right((false, InferredQueryTimeline.Range.Before(new DateTime(older_than.get))))
            } else if (newer_than.isDefined) {
              Right((false, InferredQueryTimeline.Range.After(new DateTime(newer_than.get))))
            } else {
              Right((true, InferredQueryTimeline.Range.Before(startOfDay)))
            }

          case TimeBasedTaskType.Failed =>
            if (older_than.isDefined) {
              Right((false, InferredQueryTimeline.Range.Before(new DateTime(older_than.get))))
            } else if (newer_than.isDefined) {
              Right((false, InferredQueryTimeline.Range.After(new DateTime(newer_than.get))))
            } else {
              Right((true, InferredQueryTimeline.Range.Before(endOftheDay)))
            }

        }
      }
    }


    val pageSize = {
      if (page_size.isDefined) {
        val pg_sz = page_size.get

        if (pg_sz > max_page_limit_from_user) {
          Left(ParamValidationError.ParamValidationFailed(s"Page limit should not exceed ${max_page_limit_from_user}"))
        } else if (pg_sz < min_page_limit_from_user) {
          Left(ParamValidationError.ParamValidationFailed(s"Page limit should not be less than ${min_page_limit_from_user}"))
        } else {
          Right(pg_sz)
        }
      } else {
        Right(default_page_size)
      }
    }

    val res = for {
      validated_timeline <- timeLineValidation
      validated_page_size <- pageSize
      dateRange <- dateRangeValidation
    } yield (validated_timeline._1, ValidatedTaskReq.ValidatedTaskReqRange(
      timeline = validated_timeline._2,
      pageSize = validated_page_size,
      dateRange = dateRange
    ))

    res


  }

  def assigneeValidation(
                          parsedParams: Map[String, Vector[String]],
                          permittedAccountIds: List[Long]
                        ): Either[ParamValidationError, List[Long]] = {

    parsedParams.get("assignee_id") match {
      case None =>
        Right(permittedAccountIds)
      case Some(value) =>
        Try(value.toList.map(a => a.toLong)) match {
          case Failure(e) =>
            Left(ParamValidationError.ParamValidationFailed(msg = "Error while validating assignee Ids"))
          case Success(ids) =>
            val validAssignees = permittedAccountIds.intersect(ids)
            if (validAssignees.isEmpty) {
              Left(ParamValidationError.ParamValidationFailed(msg = "Assignee Access Error"))
            } else {
              Right(validAssignees)
            }
        }
    }
  }

  def validateParams(
                      parsedParams: Map[String, Vector[String]],
                    )(using logger: SRLogger): Try[SearchTask] = Try {


    var flag: Boolean = true

    val task_type_from_frontend: Option[List[TaskType]] = parsedParams.get("sub_task_type") match {
      case None =>
        flag = false
        None

      case Some(value) => SubTaskType.fromString(value.head) match {

        case Failure(e) =>
          logger.fatal(s"[ValidationError]: ${e.getMessage}")
          None

        case Success(subTask) =>

          subTask match {

            case SubTaskType.Email =>
              Some(List(TaskType.SendEmail,
                  TaskType.AutoEmailMagicContent,
                  ManualEmailMagicContent))

            case SubTaskType.Whatsapp =>
              Some(List(TaskType.SendWhatsAppMessage))

            case SubTaskType.Sms =>
              Some(List(TaskType.SendSms))

            case SubTaskType.Call =>
              Some(List(TaskType.CallTask))

            case SubTaskType.Linkedin =>
              Some(List(TaskType.SendLinkedinInMail,
                TaskType.SendLinkedinMessage,
                TaskType.ViewLinkedinProfile,
                TaskType.SendLinkedinConnectionRequest,
                TaskType.AutoLinkedinMessage,
                TaskType.AutoViewLinkedinProfile,
                TaskType.AutoLinkedinInmail,
                TaskType.AutoLinkedinConnectionRequest
              ))

            case SubTaskType.General =>
              Some(List(TaskType.GeneralTask))

            case SubTaskType.All =>
              None
          }
      }
    }

    val assignee_ids: Option[List[Long]] = parsedParams.get("assignee_id") match {
      case None =>
        None
      case Some(value) =>
        Try(value.toList.map(a => a.toLong)) match {
          case Failure(e) =>
            logger.fatal(s"[ValidationError]: ${e.getMessage}")
            None

          case Success(ids) =>
            Some(ids)
        }
    }

    val task_status_from_extension: Option[List[TaskStatusType]] = parsedParams.get("task_status") match {
      case None =>
        None
      case Some(value) =>
        Try(value.toList.map { status => TaskStatusType.fromString(status).get }) match {
          case Failure(e) =>
            logger.fatal(s"[ValidationError]: ${e.getMessage}")
            None
          case Success(value) =>
            Some(value)
        }
    }

    val task_priority: List[TaskPriority] = getPriorityValidation(parsedParams = parsedParams)

    val task_priorities = if (task_priority.isEmpty) {
      None
    } else {
      Some(task_priority)
    }


    val task_types_from_extension: Option[List[TaskType]] = parsedParams.get("task_types") match {
      case None =>
        None
      case Some(value) =>
        Try(value.toList.map(a => TaskType.fromString(a).get)) match {
          case Failure(e) =>
            logger.fatal(s"[ValidationError]: ${e.getMessage}")
            None
          case Success(value) =>
            Some(value)
        }
    }

    val task_types: Option[List[TaskType]] = if (flag) {
      task_type_from_frontend
    } else {
      task_types_from_extension
    }

    val time_based_task_type: Option[TimeBasedTaskType] = parsedParams.get("time_based_task_type") match {
      case None =>
        None
      case Some(value) =>
        Try(TimeBasedTaskType.fromString(value.head).get) match {
          case Failure(e) =>
            logger.fatal(s"[ValidationError]: ${e.getMessage}\n\n")
            None
          case Success(value) =>
            Some(value)
        }
    }

    val campaignIds: Option[List[CampaignId]] = parsedParams.get("campaign_id") match {
      case None => None
      case Some(cids) =>
        Try {
          cids.map(c => CampaignId(c.toLong))
        } match {
          case Failure(e) =>
            logger.fatal(s"[ValidationError]: Cannot parse campaign_ids", e)
            None

          case Success(values) =>
            Some(values.toList)
        }
    }

    val replySentimentUuids: Option[List[ReplySentimentUuid]] = parsedParams.get("reply_sentiment_uuid") match {

      case None => None

      case Some(rids) =>

        Some(rids.map(c => ReplySentimentUuid(uuid = c)).toList)

    }

    val duration_from: Option[Long] = parsedParams.get("duration_from") match {
      case None => None
      case Some(duration) =>
        Try {
          duration.head.toLong
        } match {
          case Failure(e) =>
            logger.fatal(s"[ValidationError]: Cannot parse duration_from", e)
            None

          case Success(value) =>
            Some(value)
        }
    }

    val duration_to: Option[Long] = parsedParams.get("duration_to") match {
      case None => None
      case Some(duration) =>
        Try {
          duration.head.toLong
        } match {
          case Failure(e) =>
            logger.fatal(s"[ValidationError]: Cannot parse duration_to", e)
            None

          case Success(value) =>
            Some(value)
        }
    }


    SearchTask(
      assignee_ids = assignee_ids,
      task_status = task_status_from_extension,

      reply_sentiment_uuids = replySentimentUuids,

      task_priority = task_priorities,

      campaign_ids = campaignIds,

      task_types = task_types,
      duration_from = duration_from,
      duration_to = duration_to,
      time_based_task_type = time_based_task_type

    )
  }

  def getPriorityValidation(
                             parsedParams: Map[String, Vector[String]]
                           )(using logger: SRLogger): List[TaskPriority] = {
    val task_priority: List[TaskPriority] = parsedParams.get("task_priority") match {
      case None =>
        List()
      case Some(value) =>
        Try(value.toList.map { priority => TaskPriority.fromString(priority).get }) match {
          case Failure(e) =>
            logger.fatal(s"[ValidationError]: ${e.getMessage}")
            List()
          case Success(value) =>
            value
        }
    }

    task_priority

  }

  private def getTaskStatusParsed(task_status: Option[List[TaskStatusType]]): Option[List[String]] = {

    task_status match {
      case None =>
        None
      case Some(task_status) =>
        Some(task_status.map(_.toKey))
    }
  }

  private def getTaskPriorityParsed(task_priority: Option[List[TaskPriority]]): Option[List[String]] = {

    task_priority match {

      case None =>
        None
      case Some(task_priority) =>
        Some(task_priority.map(_.toKey))
    }
  }

  private def getTodayAndDueTasks(
                                   tasks: List[Task],
                                   timeZone: String
                                 ): List[Task] = {

    val endTime = endOfTheDay(timeZone = timeZone)

    tasks.filter(t => t.status match {
      case status: TaskStatus.Due  =>
        if (status.due_at.getMillis < endTime.getMillis) {
          true
        } else {
          false
        }

      case status: TaskStatus.PendingApproval =>
        
        if (status.due_at.getMillis < endTime.getMillis) {
          true
        } else {
          false
        }
        

      case _: TaskStatus.Done |
           _: TaskStatus.Snoozed |
           _: TaskStatus.Archive |
           _: TaskStatus.Failed |
           _: TaskStatus.PushedToPhantomBusterQueue |
           _: TaskStatus.Skipped |
        _: TaskStatus.QueuedToMq |
        _:TaskStatus.Approved
      => false
    })

  }

  private def getTodayTasks(
                             tasks: List[Task],
                             timeZone: String
                           ): List[Task] = {

    val startTime = startOfTheDay(timeZone = timeZone)
    val endTime = endOfTheDay(timeZone = timeZone)

    tasks.filter(t => t.status match {
      case status: TaskStatus.Due =>
        if ((status.due_at.getMillis >= startTime.getMillis) && (status.due_at.getMillis < endTime.getMillis)) {
          true
        } else {
          false
        }

      case _ => false
    })

  }

  private def getUpcomingTasks(
                                tasks: List[Task],
                                timeZone: String
                              ): List[Task] = {

    val endTime = endOfTheDay(timeZone = timeZone)

    tasks.filter(t => t.status match {
      case status: TaskStatus.Due =>
        if (status.due_at.getMillis > endTime.getMillis) {
          true
        } else {
          false
        }

      case _ => false
    })

  }

  private def getDueTasks(
                           tasks: List[Task],
                           timeZone: String
                         ): List[Task] = {

    val startTime = startOfTheDay(timeZone = timeZone)

    tasks.filter(t => t.status match {
      case status: TaskStatus.Due =>
        if (status.due_at.getMillis < startTime.getMillis) {
          true
        } else {
          false
        }

      case _ =>
        false

    })

  }



  /*private def getFilterTasksByDueAt(
                                     tasks: List[Task],
                                     task_filter: TimeBasedTaskType,
                                     timeZone: String
                           ): List[Task] = {

    val result = task_filter match {
      case TimeBasedTaskType.Today =>

        getTodayTasks(
          tasks = tasks,
          timeZone = timeZone
        )

      case TimeBasedTaskType.Upcoming =>
        getUpcomingTasks(
          tasks = tasks,
          timeZone = timeZone
        )
      case TimeBasedTaskType.Due =>

        getDueTasks(
          tasks = tasks,
          timeZone = timeZone
        )

      case TimeBasedTaskType.Completed =>
        tasks

      case TimeBasedTaskType.Skipped =>
        tasks

      case TimeBasedTaskType.Snoozed =>
        tasks
    }
    result
  }*/

  private def startOfTheDay(timeZone: String): DateTime = {
    DateTime.now()
      .withZone(DateTimeZone.forID(timeZone))
      .withTimeAtStartOfDay()
  }

  private def endOfTheDay(timeZone: String): DateTime = {
    DateTime.now()
      .withZone(DateTimeZone.forID(timeZone))
      .withTimeAtStartOfDay()
      .withHourOfDay(23)
      .plusMinutes(59)
      .plusSeconds(59)
      .plusMillis(999)
  }

  /*
  30-apr-2025:
    - Checked this function is not being used anywhere
  private def convertTaskDataToStepData(task_data: TaskData): CampaignStepData = {
    task_data match {

      case data: TaskData.SendEmailData =>
        CampaignStepData.ManualEmailStep(
          subject = data.subject,
          body = data.body
        )

      case data: TaskData.LinkedinConnectionRequestData =>
        CampaignStepData.LinkedinConnectionRequestData(
          body = data.request_message
        )

      case data: TaskData.SendLinkedinMessageData =>
        CampaignStepData.LinkedinMessageData(
          body = data.body
        )

      case data: TaskData.SendLinkedinInMailData =>
        CampaignStepData.LinkedinInmailData(
          subject = data.subject,
          body = data.body
        )

      case data: TaskData.ViewLinkedinProfileData =>
        CampaignStepData.LinkedinViewProfile()

      case data: TaskData.AutoLinkedinConnectionRequest =>
        CampaignStepData.AutoLinkedinConnectionRequest(
          body = data.body
        )

      case data: TaskData.AutoLinkedinMessage =>
        CampaignStepData.AutoLinkedinMessage(
          body = data.body
        )

      case data: TaskData.AutoLinkedinInmail =>
        CampaignStepData.AutoLinkedinInmail(
          subject = data.subject,
          body = data.body
        )

      case data: TaskData.AutoViewLinkedinProfile =>
        CampaignStepData.AutoLinkedinViewProfile()

      case data: TaskData.GeneralTaskData =>
        CampaignStepData.GeneralTaskData()

      case data: TaskData.SendWhatsAppMessageData =>
        CampaignStepData.WhatsappMessageData(
          body = data.body
        )

      case data: TaskData.SendSmsData =>
        CampaignStepData.SmsMessageData(
          body = data.body
        )

      case data: TaskData.CallTaskData =>
        CampaignStepData.CallTaskData(
          body = data.body
        )

      case data: TaskData.ManualEmailMagicContentData =>

        // No Need as the function itself is not used anywhere and we cant create step based on taskData
        CampaignStepData.CallTaskData(
          body = data.generated_body
        )

      case data: TaskData.AutoEmailMagicContentData =>
        // No Need as the function itself is not used anywhere and we cant create step based on taskData
        CampaignStepData.CallTaskData(
          body = data.generated_body
        )


    }
  }
  */

  def dueDateValidation(due_at: DateTime): Boolean = {

    if (due_at.isAfter(DateTime.now().plusDays(360))) {

      false

    } else {

      true

    }

  }

  def createTaskValidation(
                            task: NewTask
                          ): Either[CreateTaskError, NewTask] = {

    for {

      // NewTask
      due_date_validation <- task.status match {

        case data: TaskStatus.Due =>

          if (!dueDateValidation(data.due_at)) {

            Left(CreateTaskError.DueDateIsOutOfBoundsError(data))

          } else {

            Right(task)


          }


        case _ => Right(task)


      }

      // NewTask
      task_data_validation <- task.task_data match {

        case data: TaskData.ViewLinkedinProfileData =>
          Right(task)

        case data: TaskData.LinkedinConnectionRequestData =>
          /*
            17-sep-2024 :
              * removing limit related errors from
            if (data.request_message.isDefined && data.request_message.get.length > AppConfig.MultiChannel.linkedin_connection_request_add_note_character_limit) {
              Left(CreateTaskError.LinkedinConnectionRequestAddNoteTooLong)
            } else {
           */
          Right(task)
        //          }

        case data: TaskData.SendLinkedinInMailData =>

          /*
            17-sep-2024 :
              * removing limit related errors from
          if (data.subject.isDefined && data.subject.get.length > AppConfig.MultiChannel.linkedin_in_mail_subject_character_limit) {
            Left(CreateTaskError.LinkedinInMailSubjectTooLong)
          } else if (data.body.length > AppConfig.MultiChannel.linkedin_in_mail_message_body_character_limit) {
            Left(CreateTaskError.LinkedinInMailBodyTooLong)
          } else {
           */
          Right(task)
        //          }

        case data: TaskData.SendLinkedinMessageData =>

          /*
              17-sep-2024 :
                * removing limit related errors from
              if (data.body.length > AppConfig.MultiChannel.linkedin_message_body_character_limit) {
                Left(CreateTaskError.LinkedinMessageBodyTooLong)
              } else {
           */

          Right(task)

        //          }

        case data: TaskData.AutoViewLinkedinProfile =>
          Right(task)

        case data: TaskData.AutoLinkedinConnectionRequest =>
          if (data.body.isDefined && data.body.get.length > AppConfig.MultiChannel.linkedin_connection_request_add_note_character_limit) {
            Left(CreateTaskError.LinkedinConnectionRequestAddNoteTooLong)
          } else {
            Right(task)
          }

        case data: TaskData.AutoLinkedinInmail =>
          if (data.subject.length > AppConfig.MultiChannel.linkedin_in_mail_subject_character_limit) {
            Left(CreateTaskError.LinkedinInMailSubjectTooLong)
          } else if (data.body.length > AppConfig.MultiChannel.linkedin_in_mail_message_body_character_limit) {
            Left(CreateTaskError.LinkedinInMailBodyTooLong)
          } else {
            Right(task)
          }

        case data: TaskData.AutoLinkedinMessage =>
          if (data.body.length > AppConfig.MultiChannel.linkedin_message_body_character_limit) {
            Left(CreateTaskError.LinkedinMessageBodyTooLong)
          } else {
            Right(task)
          }

        case data: TaskData.SendWhatsAppMessageData =>
          if (data.body.length > AppConfig.MultiChannel.whatsapp_message_body_character_limit) {
            Left(CreateTaskError.WhatsAppMessageBodyTooLong)
          } else {
            Right(task)
          }

        case data: TaskData.GeneralTaskData =>
          Right(task)

        case data: TaskData.CallTaskData =>
          Right(task)

        case data: TaskData.SendSmsData =>
          Right(task)

        case data: TaskData.SendEmailData =>
          Right(task)

        case data: TaskData.AutoEmailMagicContentData =>
          Right(task)

        case data: TaskData.ManualEmailMagicContentData =>
          Right(task)

      }

    } yield {

      task_data_validation

    }
  }

  def getStatusBasedTime(
                          task: Task
                        ): DateTime = {

    task.status match {
      case TaskStatus.Due(due_at, status_type) => task.due_at
      case TaskStatus.Snoozed(snoozed_till, snoozed_by, snoozed_at, status_type) => snoozed_at
      case TaskStatus.Done(done_at, done_by, status_type) => done_at
      case TaskStatus.Skipped(skipped_at, skipped_by, status_type) => skipped_at
      case TaskStatus.Archive(archive_at, status_type) => archive_at
      case TaskStatus.PushedToPhantomBusterQueue(pushed_at, status_type) => pushed_at
      case TaskStatus.Failed(failure_reason, failed_at, status_type) => failed_at
      case TaskStatus.QueuedToMq(status_type,pushed_to_mq_at) => pushed_to_mq_at
      case TaskStatus.PendingApproval(due_at,status_type) => due_at
      case TaskStatus.Approved(done_at,done_by,status_type) => done_at
    }

  }


}
