package api.tasks.models

import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, JsSuc<PERSON>, <PERSON>sV<PERSON><PERSON>, <PERSON>son, OFormat, Reads, Writes}
import utils.email.EmailHelper

import scala.util.{Failure, Success, Try}

sealed trait TaskData {

  def task_type: TaskType

}

object TaskData{

  case class LinkedinConnectionRequestData(
                                          request_message: Option[String],
                                          task_type: TaskType = TaskType.SendLinkedinConnectionRequest
                                          ) extends TaskData

  object LinkedinConnectionRequestData {
    implicit def format: OFormat[LinkedinConnectionRequestData] = Json.format[LinkedinConnectionRequestData]
  }

  case class SendLinkedinMessageData(
                                      body: String,
                                      task_type: TaskType = TaskType.SendLinkedinMessage
                                    ) extends TaskData

  object SendLinkedinMessageData {
    implicit def format: OFormat[SendLinkedinMessageData] = Json.format[SendLinkedinMessageData]
  }

  case class SendEmailData(
                          subject: String,
                          body: String,
                          /*
                            22-Aug-2024:
                              1. added email_message_id for updating existing entry

                           */
                          email_message_id: Option[Long],
                          task_type: TaskType = TaskType.SendEmail
                          ) extends TaskData

  object SendEmailData {

    implicit def format: OFormat[SendEmailData] = Json.format[SendEmailData]
  }

  case class GeneralTaskData(
                              task_notes: String,
                              task_type: TaskType = TaskType.GeneralTask
                        ) extends TaskData

  object GeneralTaskData {

    implicit def format: OFormat[GeneralTaskData] = Json.format[GeneralTaskData]
  }

  case class SendSmsData(
                          body: String,
                          task_type: TaskType = TaskType.SendSms
                        ) extends TaskData

  object SendSmsData {
    implicit def format: OFormat[SendSmsData] = Json.format[SendSmsData]
  }
  
  case class  CallTaskData(
                            body: String,
                            recording_link : Option[String],
                            task_type: TaskType = TaskType.CallTask
                          ) extends TaskData

  object  CallTaskData {
    implicit def format: OFormat[CallTaskData] = Json.format[CallTaskData]
  }

  case class SendWhatsAppMessageData(
                                      body: String,
                                      task_type: TaskType = TaskType.SendWhatsAppMessage
                                    ) extends TaskData

  object SendWhatsAppMessageData {
    implicit def format: OFormat[SendWhatsAppMessageData] = Json.format[SendWhatsAppMessageData]
  }

  case class SendLinkedinInMailData(
                                      subject: Option[String],
                                      body: String,
                                      task_type: TaskType = TaskType.SendLinkedinInMail
                                    ) extends TaskData

  object SendLinkedinInMailData {
    implicit def format: OFormat[SendLinkedinInMailData] = Json.format[SendLinkedinInMailData]
  }

  case class ViewLinkedinProfileData(
                                      task_type: TaskType = TaskType.ViewLinkedinProfile
                                    ) extends TaskData

  object ViewLinkedinProfileData {
    implicit def format: OFormat[ViewLinkedinProfileData] = Json.format[ViewLinkedinProfileData]
  }

  case class AutoLinkedinInmail(
                                 task_type: TaskType = TaskType.AutoLinkedinInmail,
                                 subject: String,
                                 body: String
                               ) extends TaskData

  object AutoLinkedinInmail {
    implicit def format: OFormat[AutoLinkedinInmail] = Json.format[AutoLinkedinInmail]
  }

  case class AutoLinkedinMessage(
                                 task_type: TaskType = TaskType.AutoLinkedinMessage,
                                 body: String
                               ) extends TaskData

  object AutoLinkedinMessage {
    implicit def format: OFormat[AutoLinkedinMessage] = Json.format[AutoLinkedinMessage]
  }

  case class AutoLinkedinConnectionRequest(
                                            task_type: TaskType = TaskType.AutoLinkedinConnectionRequest,
                                            body: Option[String]
                                          ) extends TaskData

  object AutoLinkedinConnectionRequest {
    implicit def format: OFormat[AutoLinkedinConnectionRequest] = Json.format[AutoLinkedinConnectionRequest]
  }

  case class AutoViewLinkedinProfile(
                                      task_type: TaskType = TaskType.AutoViewLinkedinProfile
                                    ) extends TaskData

  object AutoViewLinkedinProfile {
    implicit def format: OFormat[AutoViewLinkedinProfile] = Json.format[AutoViewLinkedinProfile]
  }

  /*
    Only going to be used for approval
   */
  case class AutoEmailMagicContentData(
                                task_type: TaskType = TaskType.AutoEmailMagicContent,
                                generated_subject: String,
                                generated_body: String
                              ) extends TaskData

  object AutoEmailMagicContentData {
    implicit def format: OFormat[AutoEmailMagicContentData] = Json.format[AutoEmailMagicContentData]
  }

  case class ManualEmailMagicContentData(
                                        task_type: TaskType = TaskType.ManualEmailMagicContent,
                                        generated_subject: String,
                                        generated_body: String,
                                        email_message_id: Option[Long]
                                      ) extends TaskData

  object ManualEmailMagicContentData {
    implicit def format: OFormat[ManualEmailMagicContentData] = Json.format[ManualEmailMagicContentData]
  }

  implicit def writes: Writes[TaskData] = new Writes[TaskData] {
    def writes(ev: TaskData): JsValue = {

      ev match {
        case data: CallTaskData => Json.toJson(data)

        case data: LinkedinConnectionRequestData => Json.toJson(data)

        case data: SendEmailData => Json.toJson(data)

        case data: GeneralTaskData => Json.toJson(data)

        case data: SendLinkedinMessageData => Json.toJson(data)

        case data: SendSmsData => Json.toJson(data)

        case data: SendWhatsAppMessageData => Json.toJson(data)

        case data: SendLinkedinInMailData => Json.toJson(data)

        case data: ViewLinkedinProfileData => Json.toJson(data)

        case data: AutoViewLinkedinProfile => Json.toJson(data)

        case data: AutoLinkedinInmail => Json.toJson(data)

        case data: AutoLinkedinMessage => Json.toJson(data)

        case data: AutoLinkedinConnectionRequest => Json.toJson(data)

        case data: AutoEmailMagicContentData => Json.toJson(data)

        case data: ManualEmailMagicContentData => Json.toJson(data)

      }

    }
  }

  implicit def reads: Reads[TaskData] = (ev: JsValue) => Try {

    //FIXME : MATCH ON TaskType to make it exhaustive -> here, matching is being done on a string, so it is not exhaustive (even if cases do not include some objects of TaskType, the code will compile). We should match on the case class to make it exhaustive.
    (ev \ "task_type").as[String] match {

      case TaskType.sendEmail =>

        JsSuccess(
          SendEmailData(
            subject = (ev \ "subject").as[String],
            body = (ev \ "body").as[String],
            email_message_id = (ev \ "email_message_id").asOpt[Long]
          )
        )

      case TaskType.sendLinkedinConnectionRequest =>

        JsSuccess(
          LinkedinConnectionRequestData (
            request_message = (ev \ "request_message").asOpt[String],
          )
        )

      case TaskType.`generalTask` =>

        JsSuccess(
          GeneralTaskData(
            task_notes = (ev \ "task_notes").as[String]
          )
        )

      case TaskType.sendLinkedinMessage =>

        JsSuccess(
          SendLinkedinMessageData(
            body = (ev \ "body").as[String]
          )
        )

      case TaskType.sendSms =>

        JsSuccess(
          SendSmsData(
            body = (ev \ "body").as[String]
          )
        )

      case TaskType.callTask =>

        JsSuccess(
          CallTaskData(
            body = (ev \ "body").as[String],
            recording_link = (ev \ "recording_link").asOpt[String]
          )
        )

      case TaskType.sendWhatsappMessage =>
        JsSuccess(
          SendWhatsAppMessageData(
            body = (ev \ "body").as[String]
          )
        )

      case TaskType.sendLinkedinInMail =>
        JsSuccess(
          SendLinkedinInMailData(
            subject = (ev \ "subject").asOpt[String],
            body = (ev \ "body").as[String]
          )
        )

      case TaskType.viewLinkedinProfile =>
        JsSuccess(
          ViewLinkedinProfileData()
        )

      case TaskType.autoViewLinkedinProfile =>
        JsSuccess(
          AutoViewLinkedinProfile()
        )

      case TaskType.autoLinkedinConnectionRequest =>
        JsSuccess(
          AutoLinkedinConnectionRequest(
            body = (ev \ "body").asOpt[String]
          )
        )

      case TaskType.autoLinkedinInmail =>
        JsSuccess(
          AutoLinkedinInmail(
            subject = (ev \ "subject").as[String],
            body = (ev \ "body").as[String]
          )
        )

      case TaskType.autoLinkedinMessage =>
        JsSuccess(
          AutoLinkedinMessage(
            body = (ev \ "body").as[String]
          )
        )


      case TaskType.manualEmailMagicContent =>

        JsSuccess(ManualEmailMagicContentData(
          generated_subject = (ev \ "generated_subject").as[String],
          generated_body = EmailHelper.convertNewlinesToBrTags((ev \ "generated_body").as[String]),
          email_message_id = (ev \ "email_message_id").asOpt[Long]
        ))


      case TaskType.autoEmailMagicContent =>
        JsSuccess(
        AutoEmailMagicContentData(
          generated_subject = (ev \ "generated_subject").as[String],
          generated_body = EmailHelper.convertNewlinesToBrTags((ev \ "generated_body").as[String]),
        ))

    }

  } match {
    case Failure(e) => JsError(e.toString)
    case Success(v) => v
  }

}

