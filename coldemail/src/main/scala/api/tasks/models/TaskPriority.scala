package api.tasks.models

import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON>, JsSuc<PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Writes}

import scala.util.{Failure, Success, Try}

sealed trait TaskPriority{
  def toKey: String

  override def toString: String = toKey
}

object TaskPriority extends play.api.Logging {

  private val critical = "critical"
  private val high = "high"
  private val normal = "normal"
  private val low = "low"

  case object Critical extends TaskPriority {
    def toKey: String = critical
  }
  case object High extends TaskPriority  {
    def toKey: String = high
  }
  case object Normal extends TaskPriority  {
    def toKey: String = normal
  }
  case object Low extends TaskPriority  {
    def toKey: String = low
  }

  def fromString(key: String): Try[TaskPriority] = Try{

    key match {
      case `critical` => Critical
      case `high` => High
      case `normal` => Normal
      case `low` => Low
    }
  }

  implicit def reads: Reads[TaskPriority] ={

    case JsString(value) =>
      fromString(key = value) match {

        case Failure(exception) => JsError(exception.toString)

        case Success(data) => JsSuccess(value = data)
      }

    case _ =>

      logger.error("we aren't intreseted in this as it will be JsString only")
      JsError(s"""Expected a priority string, got something else """)
  }


  implicit def writes: Writes[TaskPriority] = new Writes[TaskPriority] {
    def writes(ev: TaskPriority): JsValue = {

      ev match {
        case Critical => Json.toJson(critical)

        case High => Json.toJson(high)

        case Normal => Json.toJson(normal)

        case Low => Json.toJson(low)


      }

    }
  }
}
