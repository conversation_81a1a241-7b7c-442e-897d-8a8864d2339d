package api.tasks.models

import api.campaigns.services.CampaignId
import api.emails.EmailsScheduledUuid
import api.team_inbox.service.ReplySentimentUuid
import eventframework.PaginationSortedData
import org.joda.time.DateTime
import play.api.libs.json.{Json, OFormat, Reads}
import play.api.libs.json.JodaWrites.*
import play.api.libs.json.JodaReads.*
import sr_scheduler.models.ChannelType

case class Assignee(
                   id: Long,
                   name: String
                   )
object Assignee{
  given format: OFormat[Assignee] = Json.format[Assignee]

}

case class TaskProspect(
                     id: Long,
                     company: Option[String],
                     name: Option[String],
                     email: Option[String],
                     phone_number: Option[String],
                     linkedin_url: Option[String],
                     timezone: Option[String],
                     designation: Option[String]
                    )
object TaskProspect{
  given format: OFormat[TaskProspect] = Json.format[TaskProspect]
}


case class Task( // Todo There is an extra due_at at in case class we can think about it
                 task_id: String,
                 is_auto_task: Boolean,
                 task_type: TaskType,
                 added_by: Long,
                 task_data: TaskData,
                 status: TaskStatus,
                 assignee: Option[Assignee],
                 team_id: Long,
                 prospect: Option[TaskProspect],
                 priority: TaskPriority,
                 note: Option[String],
                 created_at: DateTime,
                 updated_at: Option[DateTime],
                 due_at: DateTime,
                 campaign_id: Option[Long],
                 campaign_name: Option[String],
                 step_id: Option[Long],
                 step_label: Option[String],
                 is_opening_step: Option[Boolean],
                 created_via: TaskCreatedVia,
                 reply_sentiment_uuid: Option[ReplySentimentUuid]
               ) extends PaginationSortedData {
  override def getSortBy: DateTime = due_at

  override def getExactlyAtIdOrUuid: String = task_id
}

object Task {
  given format: OFormat[Task] = Json.format[Task]
}

case class NewTask(
                    campaign_id: Option[Long],
                    campaign_name: Option[String],
                    step_id: Option[Long],
                    step_label: Option[String],
                    created_via: TaskCreatedVia,
                    is_opening_step: Option[Boolean],
                    task_type: TaskType,
                    is_auto_task: Boolean,
                    task_data: TaskData,
                    status: TaskStatus,
                    assignee_id: Option[Long],
                    prospect_id: Option[Long],
                    priority: TaskPriority,
                    note: Option[String],
                    emailsScheduledUuid: Option[EmailsScheduledUuid],
                  )

object NewTask {
  given reads: Reads[NewTask] = Json.reads[NewTask]
}

case class UpdateTask(
                    task_data: TaskData,
                    status: TaskStatus,
                    assignee_id: Option[Long],
                    prospect_id: Option[Long],
                    priority: TaskPriority,
                    note: Option[String]
                  )

object UpdateTask {
  given reads: Reads[UpdateTask] = Json.reads[UpdateTask]
}


case class TaskStatsInputQuery(
                                from_time: DateTime,
                                till_time: DateTime,
                                campaign_id: Option[CampaignId],
                                channel_type: Option[ChannelType]
                          )

object TaskStatsInputQuery{
  import utils.sr_json_utils.ChannelTypeJsonUtil.jsonFormatChannelType

  implicit  val reads: Reads[TaskStatsInputQuery] = Json.reads[TaskStatsInputQuery]
}

case class SearchTask(

                       assignee_ids: Option[List[Long]],

                       reply_sentiment_uuids: Option[List[ReplySentimentUuid]],

                       task_status: Option[List[TaskStatusType]],

                       task_priority: Option[List[TaskPriority]],

                       task_types: Option[List[TaskType]],

                       campaign_ids: Option[List[CampaignId]],

                       time_based_task_type: Option[TimeBasedTaskType],

                       duration_from: Option[Long] = None,

                       duration_to: Option[Long] = None

)

object SearchTask {
  given reads: Reads[SearchTask] = Json.reads[SearchTask]
}

case class TaskCount(
                    todayAndDue: SubCount,
                    today: SubCount,
                    upcoming: SubCount,
                    due: SubCount,
                    completed: SubCount,
                    skipped: SubCount,
                    snoozed: SubCount,
                    failed: SubCount
                    )

case class SubCount(
                   all: Int,
                   email: Int,
                   linkedin: Int,
                   sms: Int,
                   whatsapp: Int,
                   call: Int,
                   generic: Int,
                   approval_email: Int,
                   approval_call: Int,
                   approval_linkedin: Int,
                   approval_sms: Int,
                   approval_whatsapp: Int,
                   approval_generic: Int
                   )

object SubCount {
  given format: OFormat[SubCount] = Json.format[SubCount]
}

object TaskCount {
  given format: OFormat[TaskCount] = Json.format[TaskCount]
}

case class TaskMessage(
                        task_id: String,
                        team_id: Long,
                        status: TaskStatus
                      )
