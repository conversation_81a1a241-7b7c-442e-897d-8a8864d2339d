package api.general

import api.accounts.TeamId
import api.accounts.models.AccountId
import play.api.libs.json.{Js<PERSON><PERSON>r, JsResult, JsString, JsSuccess, JsValue, Reads}
import scalikejdbc.DBSession

import scala.util.Try

case class GeneralSettingUuid(uuid: String) extends AnyVal

object GeneralSettingUuid {
  given reads: Reads[GeneralSettingUuid] = new Reads[GeneralSettingUuid] {
    override def reads(ev: JsValue): JsResult[GeneralSettingUuid] = {
      ev match {
        case JsString(uuid) => JsSuccess(GeneralSettingUuid(uuid = uuid))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }
  }
}

class GeneralSettingService(generalSettingDAO: GeneralSettingDAO) {

  def getSettingUuidForAccount(accountId: AccountId, teamId: TeamId): Try[GeneralSettingUuid] = {

    // TODO: Cache this call.
    generalSettingDAO.getSettingUuidForAccount(
      accountId = accountId,
      teamId = teamId
    )

  }

  def addGeneralSetting(teamId: Long,
                        uuid: String,
                        ownerAccountId: Long,
                        quota_per_day: Int,
                        min_delay_seconds: Int)(implicit session: DBSession): Try[Long] = {

    generalSettingDAO.addGeneralSetting(
      teamId = teamId,
      uuid = uuid,
      ownerAccountId = ownerAccountId,
      quota_per_day = quota_per_day,
      min_delay_seconds = min_delay_seconds
    )

  }

}
