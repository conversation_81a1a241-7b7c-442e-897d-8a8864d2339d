package api.phantombuster_proxy

import api.AppConfig
import api.accounts.TeamId
import api.accounts.service.OrganizationService
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.cache.models.SrResetCacheInterval
import utils.proxy.brightdata.BrightDataService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class PhantomBusterProxyService(
                                 phantomBusterProxyDAO: PhantomBusterProxyDAO, 
                                 brightDataService: BrightDataService,
                                 organizationService: OrganizationService
                               ) {

  def fetchRandomProxyForACountry(
                                   country: String
                                 ): Try[ProxyDetails] = {

    phantomBusterProxyDAO.fetchAndUpdateRandomProxyForACountry(
      country = country
    )
      .flatMap {
        case None =>
          Failure(new Exception("No Proxy available to assign to the linkedin account."))

        case Some(proxyDetails) =>
          Success(proxyDetails)
      }

  }

  def decreaseNumberOfAccountsUsingForProxy(
                                             id: PhantomBusterProxyId,
                                             teamId: TeamId
                                           )(using logger: SRLogger): Try[Int] = {
    for {
      updateCount: Int <- phantomBusterProxyDAO.decreaseNumberOfAccountsUsingForProxy(id = id)

      _: Boolean <- organizationService.resetOrgDataFromCacheUsingTeamId(
        teamId = teamId,
        resetCacheInterval = SrResetCacheInterval.Immediately
      )
    } yield {
      updateCount
    }
  }

  def fetchProxyUsageByCountry(): Try[List[ProxyCountryAndUsage]] = {
    phantomBusterProxyDAO.fetchProxyUsageByCountry()
  }

  def addPhantomBusterProxyAndWhitelistDomains(
                                                zone: String,
                                                ipAddress: String,
                                                username: String,
                                                password: String,
                                                country: String
                                              )(implicit ws: WSClient,
                                                ec: ExecutionContext): Future[Long] = {

    for {
      proxyId: Long <- Future.fromTry {
        phantomBusterProxyDAO.addPhantomBusterProxy(
          ipAddress = ipAddress,
          username = username,
          password = password,
          country = country
        )
      }
      
      _: Boolean <- brightDataService.whiteListOrBlackListDomains(
        proxyZone = zone,
        requestType = "whitelist",
        domains = AppConfig.Linkedin_Automation.whiteListedDomainsForLinkedinAutomation
      )
      
      _: Boolean <- brightDataService.whiteListOrBlackListDomains(
        proxyZone = zone,
        requestType = "blacklist",
        domains = List("*") // Blacklists all other domains
      )
    } yield {
      proxyId
    }

  }

}

object PhantomBusterProxyService {
  
  def convertProxyUsernameToZone(username: String): String = {
    username.replace(AppConfig.BrightData.usernamePrefix, "")
  }
  
}
