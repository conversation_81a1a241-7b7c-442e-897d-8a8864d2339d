package api.linkedin

import api.AppConfig
import api.accounts.TeamId
import io.sr.billing_common.models.PlanType
import api.accounts.models.{AccountId, OrgId}
import api.captain_data.{Captain<PERSON><PERSON><PERSON><PERSON>untUI<PERSON>, CaptainDataJobUID, Captain<PERSON><PERSON><PERSON>serUI<PERSON>}
import api.linkedin.models.{CDJobUid, CreateOrUpdateLinkedinAccountSettings, LinkedInServiceProvider, LinkedinAccountAndLastInboxScrapeTime, LinkedinAccountAndUserAgent, LinkedinAccountCredentialsAndUserAgent, LinkedinAccountSettings, LinkedinAccountStatus, LinkedinAccountUuidAndTeamId, LinkedinSessionCookieAndUserAgent, LinkedinSettingId, LinkedinSettingUuid, ProxyIdAndLoginCountry, SessionSyncStatusDetails, UserAgent}
import api.phantombuster.{ExecuteAutoLinkedinTasksError, LinkedinMessageThreadUrl, PhantomBusterContainerId, PhantomBusterContainerStatus, PhantomBusterLinkedinMessage, PhantomBusterRunningInboxContainerDetails, PhantomBusterRunningMessageThreadContainerDetails, PhantomBusterWebhookStatus}
import api.phantombuster_proxy.{PhantomBusterProxyId, ProxyIPAndCredentials}
import org.joda.time.DateTime
import scalikejdbc.*
import scalikejdbc.jodatime.JodaWrappedResultSet.*
import sr_scheduler.CampaignStatus
import sr_scheduler.models.{ChannelDataForScheduling, LinkedinSettingCreateSchedule}
import utils.mq.captainData.LinkedinSettingDeletionData
import utils.mq.channel_scheduler.channels.ChannelId
import utils.security.EncryptionHelpers

import scala.util.Try

case class LinkedinDependenciesToRemove(
                                           phantomBusterProxyId: Option[PhantomBusterProxyId],
                                           captainDataUserUID: Option[CaptainDataUserUID],
                                           captainDataAccountUID: Option[CaptainDataAccountUID],
                                           linkedinSettingId: LinkedinSettingId
                                           )

case class LinkedinSettingIdAndOwner(
                                      id: LinkedinSettingId,
                                      ownerAccountId: AccountId
                                    )

case class LinkedinSettingBasicInfo(
  uuid: LinkedinSettingUuid,
  status: LinkedinAccountStatus,
  owner_account_id: AccountId,
  team_id: TeamId
)

case class LinkedinSettingLastScrapedInfo(
                                         id: LinkedinSettingId,
                                         accountId: AccountId,
                                         uuid: LinkedinSettingUuid,
                                         team_id: TeamId,
                                         last_inbox_scraping_done_at: Option[DateTime],
                                         conversations_recovery_job_uid: Option[CaptainDataJobUID]
                                         )

object LinkedinSettingBasicInfo {
  def fromDB(rs: WrappedResultSet): LinkedinSettingBasicInfo = {
    LinkedinSettingBasicInfo(
      uuid = LinkedinSettingUuid(uuid = rs.string("uuid")),
      status = LinkedinAccountStatus.fromString(rs.string("status")).get,
      owner_account_id = AccountId(id = rs.long("owner_account_id")),
      team_id = TeamId(id = rs.long("team_id"))
    )
  }
}

class LinkedinSettingDAO {

  def createLinkedinAccount(
                             data: CreateOrUpdateLinkedinAccountSettings,
                             teamId: Long,
                             uuid: String
                           ): Try[Long] = Try {

    // FIXME MULTICHANNEL : Verfiy values (min_delay_seconds and max_delay_seconds)
    DB.autoCommit { implicit session =>
      sql"""
                INSERT INTO linkedin_settings(
                  uuid,
                  first_name,
                  last_name,
                  email,
                  password_enc,
                  profile_url,
                  login_country,
                  owner_account_id,
                  team_id,
                  view_profile_limit_per_day,
                  inmail_limit_per_day,
                  message_limit_per_day,
                  connection_request_limit_per_day,
                  min_delay_seconds,
                  max_delay_seconds
                ) VALUES (
                  $uuid,
                  ${data.first_name},
                  ${data.last_name},
                  ${data.email.trim.toLowerCase()},
                  ${data.password.map(EncryptionHelpers.encryptLinkedinAccountPassword)},
                  ${data.profile_url.trim.toLowerCase()},
                  ${data.country},
                  ${data.owner_id},
                  $teamId,
                  ${data.view_profile_limit_per_day},
                  ${data.inmail_limit_per_day},
                  ${data.message_limit_per_day},
                  ${data.connection_request_limit_per_day},
                  ${AppConfig.MultiChannel.linkedin_min_delay_seconds},
                  ${AppConfig.MultiChannel.linkedin_max_delay_seconds}
                )
                RETURNING *;
               """
        .map(rs => rs.long("id"))
        .single
        .apply()
        .get
    }

  }

  def getProxyDetailsForLinkedinAccount(
                                         linkedinSettingUuid: LinkedinSettingUuid,
                                         teamId: TeamId
                                       ): Try[Option[ProxyIPAndCredentials]] = Try {

    DB readOnly {implicit session =>
      sql"""
           SELECT
              pp.ip_address,
              pp.username,
              pp.password_enc
           FROM linkedin_settings ls
           INNER JOIN phantombuster_proxies pp ON ls.phantombuster_proxy_id = pp.id
           WHERE ls.uuid = ${linkedinSettingUuid.uuid}
           AND ls.team_id = ${teamId.id}
           ;
           """
        .map(rs => {
          ProxyIPAndCredentials(
            ip_address = rs.string("ip_address"),
            username = rs.stringOpt("username"),
            password = rs.stringOpt("password_enc").map(EncryptionHelpers.decryptPhantomBusterProxyPassword)
          )
        })
        .single
        .apply()
    }

  }

  def updateProxyId(
                     linkedinSettingUuid: LinkedinSettingUuid,
                     teamId: TeamId,
                     proxyId: Long
                   ): Try[Int] = Try {

    DB autoCommit {implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              phantombuster_proxy_id = $proxyId
           WHERE uuid = ${linkedinSettingUuid.uuid}
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }

  }

  def getLoginCountryOfLinkedinAccount(
                                        linkedinSettingUuid: LinkedinSettingUuid,
                                        teamId: TeamId
                                      ): Try[Option[String]] = Try {

    DB readOnly {implicit session =>
      sql"""
           SELECT login_country
           FROM linkedin_settings
           WHERE uuid = ${linkedinSettingUuid.uuid}
           AND team_id = ${teamId.id}
           ;
           """
        .map(rs => rs.stringOpt("login_country"))
        .single
        .apply()
        .flatten
    }

  }

  def getLinkedinSettingUuidAndTeamIdFromJobUid(

                                               job_uid: CDJobUid
                                               ): Try[Option[LinkedinSettingUuidAndTeamId]]= Try{

    DB readOnly { implicit session =>
      sql"""
           SELECT
           uuid,
           team_id
           FROM linkedin_settings
           WHERE 
            initial_li_connection_sync_job_uid = ${job_uid.uid}
            AND initial_li_connection_sync_completed = false
         """
        .map(rs => LinkedinSettingUuidAndTeamId(
          linkedinSettingUuid = LinkedinSettingUuid(uuid = rs.string("uuid")),
          teamId = TeamId(id = rs.long("team_id"))
        ))
        .single
        .apply()
      
    }

  }

  def updateLinkedinAccountStatus(
                                   uuid: LinkedinSettingUuid,
                                   teamId: TeamId,
                                   status: LinkedinAccountStatus
                                 ): Try[Int] = Try{


    val statusUpdate = if (status == LinkedinAccountStatus.Active) {
      sqls"status = ${status.toString}, cookie_reconnected_at = now()"
    } else {
      sqls"status = ${status.toString}, last_cookie_failed_at = now()"
    }

    DB autoCommit {implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET $statusUpdate
           WHERE uuid = ${uuid.uuid}
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()

    }

  }
  
  def updateInitialSyncStatus(
                                 settings_data: LinkedinSettingUuidAndTeamId,
                                 initial_sync_completed: Boolean
                                 ): Try[Int] = Try{
    
    DB autoCommit {implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              initial_li_connection_sync_completed = $initial_sync_completed
              
           WHERE 
           uuid = ${settings_data.linkedinSettingUuid.uuid}
           AND team_id = ${settings_data.teamId.id}
           
           ;
           """
        .update
        .apply()
      
    }
    
  }

  def setNextTaskToBeExecutedAt(
                                 linkedinSettingUuid: LinkedinSettingUuid,
                                 teamId: TeamId,
                                 nextTaskExecuteTime: DateTime,
                               ): Try[Int] = Try{

    DB autoCommit {implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              next_task_to_be_executed_at = $nextTaskExecuteTime,
              in_queue_for_task_execution_or_scraping = false
           WHERE uuid = ${linkedinSettingUuid.uuid}
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }

  }

  def updateLinkedinAccountSessionCookie(uuid: String, teamId: TeamId, cookieValue: String): Try[Int] = Try {
    DB autoCommit {implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              session_cookie = ${EncryptionHelpers.encryptLinkedinSessionCookie(cookieValue)},
              status = ${LinkedinAccountStatus.Active.toString},
              consecutive_recreate_session_attempts = 0,
              next_session_sync_retry_to_be_done_at = NULL,
              updated_at = now()
           WHERE
              uuid = ${uuid}
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }
  }

  def getLinkedinAccountSettings(teamId: Long, permittedAccountIds: Seq[Long]): Try[List[LinkedinAccountSettings]] = Try {
    DB.readOnly{ implicit session =>
      sql"""
           SELECT
            ls.uuid,
            ls.first_name,
            ls.last_name,
            ls.email,
            ls.profile_url,
            ls.login_country,
            ls.owner_account_id,
            ls.team_id,
            a.first_name as owner_first_name,
            a.last_name as owner_last_name,
            ls.view_profile_limit_per_day,
            ls.inmail_limit_per_day,
            ls.message_limit_per_day,
            ls.connection_request_limit_per_day,
            ls.session_cookie,
            ls.status,
            ls.captain_data_user_id,
            ls.captain_data_account_id,
            ls.service_provider
           FROM linkedin_settings ls
           INNER JOIN accounts a
           ON a.id = ls.owner_account_id
           WHERE team_id = $teamId
           AND ls.owner_account_id IN ($permittedAccountIds)
           AND ls.is_active
         """
        .map(rs =>
          LinkedinSettingDAO.linkedinAccountSettingsFromDB(rs = rs)
        )
        .list
        .apply()
    }
  }

  def getInactiveLinkedinSettings: Try[List[LinkedinSettingDeletionData]] = Try {
    DB.readOnly { implicit session =>
      sql"""
        SELECT uuid, team_id
        FROM linkedin_settings
        WHERE is_active = false
        AND is_deleted = false
        LIMIT 10
      """
        .map(rs => LinkedinSettingDeletionData(
          linkedinSettingUuid = LinkedinSettingUuid(rs.string("uuid")),
          teamId = TeamId(rs.long("team_id"))
        ))
        .list
        .apply()
    }
  }


  def getProxyIdAndLoginCountry(uuid: LinkedinSettingUuid, teamId: TeamId): Try[ProxyIdAndLoginCountry] = Try {
    
    DB readOnly {implicit session =>
      sql"""
           SELECT phantombuster_proxy_id, login_country
           FROM linkedin_settings
           WHERE uuid = ${uuid.uuid}
           AND team_id = ${teamId.id}
           ;
           """
        .map(rs => {
          ProxyIdAndLoginCountry(
            proxyId = rs.intOpt("phantombuster_proxy_id").map(a => PhantomBusterProxyId(a)),
            loginCountry = rs.stringOpt("login_country")
          )
        })
        .single
        .apply()
        .get
    }
    
  }

  def updateLinkedinAccountSettings(data: CreateOrUpdateLinkedinAccountSettings,
                                    uuid: String,
                                    teamId: Long): Try[LinkedinAccountSettings] = Try {

    val passwordUpdate = if (data.password.isDefined) {
      sqls"""password_enc = ${EncryptionHelpers.encryptLinkedinAccountPassword(data.password.get)},"""
    }
    else {
      sqls""
    }

    DB.autoCommit { implicit session =>
      sql"""
           UPDATE linkedin_settings ls
           SET
            first_name = ${data.first_name},
            last_name = ${data.last_name},
            email = ${data.email.trim.toLowerCase()},
            $passwordUpdate
            profile_url = ${data.profile_url.trim.toLowerCase()},
            login_country = ${data.country},
            owner_account_id = ${data.owner_id},
            view_profile_limit_per_day = ${data.view_profile_limit_per_day},
            inmail_limit_per_day = ${data.inmail_limit_per_day},
            message_limit_per_day = ${data.message_limit_per_day},
            connection_request_limit_per_day = ${data.connection_request_limit_per_day},
            updated_at = now()
           FROM accounts a
           WHERE ls.uuid = $uuid
           AND ls.team_id = $teamId
           AND a.id = ls.owner_account_id
           RETURNING 
            ls.uuid,
            ls.first_name,
            ls.last_name,
            ls.email,
            ls.profile_url,
            ls.login_country,
            ls.owner_account_id,
            ls.team_id,
            a.first_name as owner_first_name,
            a.last_name as owner_last_name,
            ls.view_profile_limit_per_day,
            ls.inmail_limit_per_day,
            ls.message_limit_per_day,
            ls.status,
            ls.connection_request_limit_per_day,
            ls.session_cookie,
            ls.captain_data_user_id,
            ls.captain_data_account_id,
            ls.service_provider;
         """
        .map(rs => LinkedinSettingDAO.linkedinAccountSettingsFromDB(rs))
        .single
        .apply()
        .get
    }
  }

  def setLinkedinAccountToInactive(uuid: LinkedinSettingUuid, team_id: TeamId): Try[Int] = Try{
    DB.autoCommit{ implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              is_active = FALSE,
              marked_as_deleted_at = now(),
              updated_at = now()
           WHERE
              uuid = ${uuid.uuid}
           AND team_id = ${team_id.id}
           ;
           """
        .update
        .apply()
    }
  }

  def deleteLinkedinAccountAndGetDependenciesToRemove(uuid: String, team_id: Long): Try[LinkedinDependenciesToRemove] = Try {

    DB.autoCommit { implicit session =>
      sql"""
           DELETE FROM linkedin_settings
           WHERE uuid = $uuid
           AND team_id = $team_id
           
           RETURNING phantombuster_proxy_id, captain_data_user_id, captain_data_account_id, id
           ;
         """
        .map(rs => {
          val phantombusterProxyId = rs.intOpt("phantombuster_proxy_id").map(a => PhantomBusterProxyId(a))
          val captainDataUserId = rs.stringOpt("captain_data_user_id").map(cd => CaptainDataUserUID(uid = cd))
          val captainDataAccountId = rs.stringOpt("captain_data_account_id").map(cd => CaptainDataAccountUID(uid = cd))
          val id = LinkedinSettingId(id = rs.long("id"))
          LinkedinDependenciesToRemove(
            phantomBusterProxyId = phantombusterProxyId,
            captainDataUserUID = captainDataUserId,
            captainDataAccountUID = captainDataAccountId,
            linkedinSettingId = id)
        }
        )
        .single
        .apply()
        .get
    }
  }

  def markLinkedinAccountAsDeleted(uuid: String, team_id: Long): Try[Int] = Try {

    DB.autoCommit { implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              is_deleted = true,
              deleted_at = now(),
              updated_at = now()
           WHERE
              uuid = $uuid
              AND deleted_at IS NULL
              AND team_id = $team_id
           ;
         """
        .update
        .apply()
    }
  }

  def getLinkedinDependenciesToRemove(uuid: String, team_id: Long): Try[LinkedinDependenciesToRemove] = Try {

    DB.autoCommit { implicit session =>
      sql"""
           SELECT phantombuster_proxy_id, captain_data_user_id, captain_data_account_id, id
           FROM linkedin_settings
           WHERE uuid = $uuid
           AND deleted_at IS NULL
           AND team_id = $team_id
           ;
         """
        .map(rs => {
          val phantombusterProxyId = rs.intOpt("phantombuster_proxy_id").map(a => PhantomBusterProxyId(a))
          val captainDataUserId = rs.stringOpt("captain_data_user_id").map(cd => CaptainDataUserUID(uid = cd))
          val captainDataAccountId = rs.stringOpt("captain_data_account_id").map(cd => CaptainDataAccountUID(uid = cd))
          val id = LinkedinSettingId(id = rs.long("id"))
          LinkedinDependenciesToRemove(
            phantomBusterProxyId = phantombusterProxyId,
            captainDataUserUID = captainDataUserId,
            captainDataAccountUID = captainDataAccountId,
            linkedinSettingId = id)
        }
        )
        .single
        .apply()
        .get
    }
  }

  def findLinkedinSettingIdFromUuidAndTeamId(uuid: String, teamId: TeamId): Try[Long] = Try {
    DB readOnly {implicit session =>
      sql"""
           SELECT id
           FROM linkedin_settings
           WHERE uuid = $uuid
           AND team_id = ${teamId.id}
           ;
           """
        .map(rs => rs.long("id"))
        .single
        .apply()
        .get
    }
  }

  def findLinkedinSettingIdAndOwnerFromUuidAndTeamId(uuid: String, teamId: TeamId): Try[LinkedinSettingIdAndOwner] = Try {
    DB readOnly {implicit session =>
      sql"""
           SELECT id, owner_account_id
           FROM linkedin_settings
           WHERE uuid = $uuid
           AND team_id = ${teamId.id}
           ;
           """
        .map(rs => LinkedinSettingIdAndOwner(
          id = LinkedinSettingId(rs.long("id")),
          ownerAccountId = AccountId(rs.long("owner_account_id"))
        ))
        .single
        .apply()
        .get
    }
  }

  def findLinkedinAccountByIdOrUuid(id: Option[Long], uuid: Option[String], teamId: TeamId): Try[Option[LinkedinAccountSettings]] = Try {

    val uniqueConditionOpt = if (id.isDefined) {
      Some(sqls"""ls.id = ${id.get}""")
    }
    else if (uuid.isDefined) {
      Some(sqls"""ls.uuid = ${uuid.get}""")
    }
    else {
      None
    }

    uniqueConditionOpt.flatMap(uniqueCondition => {
      DB.readOnly { implicit session =>
        sql"""
             SELECT
                    ls.uuid,
                    ls.first_name,
                    ls.last_name,
                    ls.email,
                    ls.profile_url,
                    ls.login_country,
                    ls.owner_account_id,
                    ls.team_id,
                    a.first_name as owner_first_name,
                    a.last_name as owner_last_name,
                    ls.view_profile_limit_per_day,
                    ls.inmail_limit_per_day,
                    ls.message_limit_per_day,
                    ls.connection_request_limit_per_day,
                    ls.session_cookie,
                    ls.status,
                    ls.captain_data_user_id,
                    ls.captain_data_account_id,
                    ls.service_provider
                   FROM linkedin_settings ls
                   INNER JOIN accounts a
                   ON a.id = ls.owner_account_id
                   WHERE $uniqueCondition
                   AND team_id = ${teamId.id};
           """
          .map(rs => LinkedinSettingDAO.linkedinAccountSettingsFromDB(rs))
          .single
          .apply()
      }
    })

  }

  def getSessionCookieAndUserAgentForLinkedinSetting(
                                          linkedinSettingUuid: String,
                                          teamId: TeamId
                                        ): Try[Option[LinkedinSessionCookieAndUserAgent]] = Try{
    DB readOnly {implicit session =>
      sql"""
           SELECT session_cookie, user_agent FROM linkedin_settings
           WHERE uuid = ${linkedinSettingUuid}
           AND team_id = ${teamId.id}
           ;
           """
        .map(rs => {
          for {
            cookie: String <- rs.stringOpt("session_cookie").map(EncryptionHelpers.decryptLinkedinSessionCookie)
            userAgent: String <- rs.stringOpt("user_agent")
          } yield {
            LinkedinSessionCookieAndUserAgent(
              cookie = cookie,
              userAgent = UserAgent(userAgent)
            )
          }
        })
        .single
        .apply()
        .flatten
    }
  }

  def fetchLinkedinAccountsToExecuteTasksOrScrapeMessages(): Try[List[LinkedinAccountAndLastInboxScrapeTime]] = Try {

    val extractionInterval = SQLSyntax.createUnsafely(AppConfig.CaptainData.RetrieveLinkedinConversations.extractionIntervalInMinutes.toString)

    DB readOnly {implicit session =>
      sql"""
           SELECT ls.id, ls.uuid, ls.team_id, ls.last_inbox_scraping_done_at, t.org_id
           FROM linkedin_settings ls
           INNER JOIN teams t ON t.id = ls.team_id
           INNER JOIN organizations o ON o.id = t.org_id
           WHERE (
              ls.next_task_to_be_executed_at IS NULL
              OR
              ls.next_task_to_be_executed_at < now()
           )
           AND ((o.metadata ->> 'enable_linkedin_automation'):: boolean OR o.plan_li_automation_seats_max > 0)
           AND o.plan_type != ${PlanType.INACTIVE.toString}
           AND ls.captain_data_account_id IS NOT NULL
           AND ls.status = ${LinkedinAccountStatus.Active.toString}
           AND ls.last_inbox_scraping_done_at IS NOT NULL
           AND ls.last_inbox_scraping_done_at >= now() - interval '$extractionInterval minutes'
           AND (ls.last_inbox_scrape_result IS NULL OR ls.last_inbox_scrape_result != ${ExecuteAutoLinkedinTasksError.InvalidOrExpiredLinkedinSessionCookie.toString})
           ORDER BY ls.pushed_to_queue_for_task_execution_or_scraping_at ASC NULLS FIRST
           LIMIT 100
           ;
           """
        .map(rs => {
          LinkedinAccountAndLastInboxScrapeTime(
            id = LinkedinSettingId(rs.long("id")),
            uuid = LinkedinSettingUuid(rs.string("uuid")),
            teamId = TeamId(rs.long("team_id")),
            lastInboxScrapedAt = rs.jodaDateTimeOpt("last_inbox_scraping_done_at"),
            orgId = OrgId(rs.long("org_id"))
          )
        })
        .list
        .apply()
    }

  }



  def updatePushedToQueueStatus(linkedinSettingId: LinkedinSettingId, teamId: TeamId): Try[Int] = Try {

    DB autoCommit { implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              in_queue_for_task_execution_or_scraping = true,
              pushed_to_queue_for_task_execution_or_scraping_at = now()
           WHERE
              id = ${linkedinSettingId.id}
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }

  }

  def removeSessionCookieErrorOnUpdate(linkedinSettingUuid: String, teamId: TeamId): Try[Int] = Try {

    DB autoCommit {implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              last_inbox_scrape_result = NULL
           WHERE
              uuid = $linkedinSettingUuid
           AND team_id = ${teamId.id}
           AND last_inbox_scrape_result = ${ExecuteAutoLinkedinTasksError.InvalidOrExpiredLinkedinSessionCookie.toString}
           ;
           """
        .update
        .apply()
    }

  }

  def updateLastInboxScrapeStatus(containerId: PhantomBusterContainerId,
                                  teamId: TeamId,
                                  status: PhantomBusterWebhookStatus): Try[Int] = Try {

    val result = status match {
      case PhantomBusterWebhookStatus.Successful =>
        PhantomBusterWebhookStatus.Successful.toString

      case PhantomBusterWebhookStatus.Failed(error) =>
        error
    }

    DB autoCommit {implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              in_queue_for_inbox_scraping = false,
              last_inbox_scraping_done_at = now(),
              last_inbox_scrape_result = $result
           WHERE
              phantombuster_inbox_scraping_container_id = ${containerId.id}
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }

  }

  // Linkedin setting will be marked as active only if the Linkedin account cookie is synced and the accountId is created in captain data
  def updateLinkedinCaptainDataUserDetails(
               captainDataUserUID: CaptainDataUserUID,
               linkedinAccountUuid: LinkedinSettingUuid,
               teamId: TeamId
             ): Try[Int] = Try {

    DB autoCommit { implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              captain_data_user_id = ${captainDataUserUID.toString},
              service_provider = ${LinkedInServiceProvider.CaptainData.toString}
           WHERE
              uuid = ${linkedinAccountUuid.uuid}
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }
  }

  // captain data account id is created here after the user is created. so we can now mark the linkedin account as active
  def updateLinkedinCaptainDataAccountDetails(
    captainDataAccountUID: CaptainDataAccountUID,
    linkedinAccountUuid: LinkedinSettingUuid,
    linkedinProfileUrl: String,
    initial_sync_li_connection_job_uid: Option[String],
    teamId: TeamId
  ): Try[Int] = Try {

    DB autoCommit { implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              captain_data_account_id = ${captainDataAccountUID.toString},
              initial_li_connection_sync_job_uid = ${initial_sync_li_connection_job_uid},
              status = ${LinkedinAccountStatus.Active.toString},
              profile_url = ${linkedinProfileUrl}
           WHERE
              uuid = ${linkedinAccountUuid.uuid}
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }
  }

  def fetchSessionSyncStatusForLinkedinAccount(
                                                uuid: LinkedinSettingUuid, 
                                                teamId: TeamId, 
                                                ownerAccountId: AccountId
                                              ): Try[Option[SessionSyncStatusDetails]] = Try {

    DB readOnly { implicit session =>
      sql"""
           SELECT 
              phantombuster_proxy_id, 
              consecutive_recreate_session_attempts,
              status,
              session_cookie
           FROM linkedin_settings
           WHERE uuid = ${uuid.uuid}
           AND team_id = ${teamId.id}
           AND owner_account_id = ${ownerAccountId.id}
           ;
           """
        .map(rs => {
          SessionSyncStatusDetails(
            phantombuster_proxy_id = rs.intOpt("phantombuster_proxy_id"),
            consecutive_recreate_session_attempts = rs.int("consecutive_recreate_session_attempts"),
            status = LinkedinAccountStatus.fromString(rs.string("status")).get,
            encryptedSessionCookie = rs.stringOpt("session_cookie")
          )
        })
        .single
        .apply()
    }

  }

  def getConsecutiveRecreateSessionAttemptsCount(uuid: LinkedinSettingUuid, teamId: TeamId): Try[Int] = Try {

    DB readOnly {implicit session =>
      sql"""
           SELECT consecutive_recreate_session_attempts
           FROM linkedin_settings
           WHERE uuid = ${uuid.uuid}
           AND team_id = ${teamId.id}
           ;
           """
        .map(rs => rs.int("consecutive_recreate_session_attempts"))
        .single
        .apply()
        .get
    }

  }

  def updateNextSessionSyncRetryToBeDoneAt(
                                            uuid: LinkedinSettingUuid,
                                            teamId: TeamId,
                                            nextSessionSyncRetryToBeDoneAt: DateTime
                                          ): Try[Int] = Try {

    DB autoCommit {implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              status = ${LinkedinAccountStatus.NeedToRecreateSession.toString},
              next_session_sync_retry_to_be_done_at = $nextSessionSyncRetryToBeDoneAt
           WHERE
              uuid = ${uuid.uuid}
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }

  }


  def changeStatus(uuid: LinkedinSettingUuid, teamId: TeamId, status: LinkedinAccountStatus): Try[Int] = Try {

    val consecutiveSessionUpdate = status match {
      case LinkedinAccountStatus.PushedToQueueForRecreatingSession =>
        sqls"""
            consecutive_recreate_session_attempts = consecutive_recreate_session_attempts + 1,
            pushed_to_queue_for_recreating_session_at = now(),
            """

      case LinkedinAccountStatus.Active | LinkedinAccountStatus.NeedToRecreateSession | LinkedinAccountStatus.NeedToAssignProxy =>
        sqls"""consecutive_recreate_session_attempts = 0,"""
    }

    DB autoCommit {implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              $consecutiveSessionUpdate
              status = ${status.toString}
           WHERE uuid = ${uuid.uuid}
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }

  }
  
  def getLinkedinAccountCredentialsAndUserAgent(uuid: LinkedinSettingUuid, teamId: TeamId): Try[Option[LinkedinAccountCredentialsAndUserAgent]] = Try {
    
    DB readOnly {implicit session =>
      sql"""
           SELECT email, password_enc, user_agent
           FROM linkedin_settings
           WHERE uuid = ${uuid.uuid}
           AND team_id = ${teamId.id}
           ;
           """
        .map(rs => {
          LinkedinAccountCredentialsAndUserAgent(
            email = rs.string("email"),
            password = rs.stringOpt("password_enc").map(EncryptionHelpers.decryptLinkedinAccountPassword),
            userAgent = rs.stringOpt("user_agent").map(UserAgent(_))
          )
        })
        .single
        .apply()
    }
    
  }

  def fetchLinkedinAccountsToRecreateSession(): Try[List[LinkedinAccountUuidAndTeamId]] = Try {
    
    val stuckLimit = SQLSyntax.createUnsafely(AppConfig.Linkedin_Automation.stuckTaskTimeLimitInMinutes.toString)

    DB readOnly {implicit session =>
      sql"""
           SELECT uuid, team_id
           FROM linkedin_settings
           WHERE (
              status = ${LinkedinAccountStatus.NeedToRecreateSession.toString}
              OR
              (
                  status = ${LinkedinAccountStatus.PushedToQueueForRecreatingSession.toString}
                  AND
                  pushed_to_queue_for_recreating_session_at < now() - interval '$stuckLimit minutes'
              )
           )
           AND password_enc IS NOT NULL
           AND (
             next_session_sync_retry_to_be_done_at IS NULL
             OR
             next_session_sync_retry_to_be_done_at < now()
           )
           ;
           """
        .map(rs => {
          LinkedinAccountUuidAndTeamId(
            uuid = LinkedinSettingUuid(rs.string("uuid")),
            teamId = TeamId(rs.long("team_id"))
          )
        })
        .list
        .apply()
    }
    
  }

  def updateUserAgent(linkedinAccountAndUserAgent: LinkedinAccountAndUserAgent) = Try {

    DB autoCommit {implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              user_agent = ${linkedinAccountAndUserAgent.userAgent.name}
           WHERE
              uuid = ${linkedinAccountAndUserAgent.uuid.uuid}
           AND team_id = ${linkedinAccountAndUserAgent.teamId.id}
           ;
           """
        .update
        .apply()
    }

  }

  def getLinkedinSettingUuidFromContainerId(
                                             containerId: PhantomBusterContainerId, 
                                             teamId: TeamId
                                           ): Try[Option[LinkedinSettingUuid]] = Try{
    
    DB readOnly {implicit session =>
      sql"""
           SELECT uuid FROM linkedin_settings
           WHERE team_id = ${teamId.id}
           AND phantombuster_inbox_scraping_container_id = ${containerId.id}
           ;
           """
        .map(rs => LinkedinSettingUuid(rs.string("uuid")))
        .single
        .apply()
    }
    
  }
  

  def getLinkedinSettingIdFromContainerId(
                                           containerId: PhantomBusterContainerId,
                                           teamId: TeamId
                                         ): Try[Option[Long]] = Try {

    DB readOnly {implicit session =>
      sql"""
           SELECT id FROM linkedin_settings
           WHERE team_id = ${teamId.id}
           AND phantombuster_inbox_scraping_container_id = ${containerId.id}
           ;
           """
        .map(rs => rs.long("id"))
        .single
        .apply()
    }

  }


  def updateContainerIdForInboxScraping(
                                         linkedinSettingUuid: String,
                                         containerId: PhantomBusterContainerId,
                                         teamId: TeamId
                                       ): Try[Int] = Try {

    DB autoCommit {implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              phantombuster_inbox_scraping_container_id = ${containerId.id}
           WHERE
              uuid = ${linkedinSettingUuid}
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }

  }

  def fetchRunningInboxScrapingContainers(): Try[List[PhantomBusterRunningInboxContainerDetails]] = Try {
    DB readOnly { implicit session =>
      sql"""
           SELECT
              phantombuster_inbox_scraping_container_id,
              id,
              team_id
           FROM linkedin_settings
           WHERE inbox_scraper_container_status = ${PhantomBusterContainerStatus.Running.toString}
           """
        .map(rs => {
          PhantomBusterRunningInboxContainerDetails(
            containerId = PhantomBusterContainerId(rs.string("phantombuster_inbox_scraping_container_id")),
            linkedinSettingId = rs.long("id"),
            teamId = TeamId(rs.long("team_id"))
          )
        })
        .list
        .apply()
    }
  }

  def updateInboxScraperContainerStatus(linkedinSettingId: Long, teamId: TeamId): Try[Int] = Try {
    DB autoCommit {implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              inbox_scraper_container_status = ${PhantomBusterContainerStatus.Idle.toString}
              last_inbox_scraping_done_at = now()
           WHERE
              id = ${linkedinSettingId}
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }
  }

  def saveLinkedinMessages(
                          messageThreadId: Long,
                          teamId: TeamId,
                          messages: List[PhantomBusterLinkedinMessage]
                          ): Try[Int] = ???

  def getChannelDataForScheduling(
                                   linkedinSettingId: ChannelId,
                                   teamId: TeamId
                                 ): Option[ChannelDataForScheduling.LinkedinChannelDataForScheduling] = {

    // TODO Multichannel: Use teamId in query

    DB.readOnly { implicit session =>

      sql"""
            SELECT
                  ls.uuid,
                  ls.first_name,
                  ls.last_name,
                  ls.email,
                  ls.owner_account_id,
                  ls.team_id,
                  ls.latest_task_scheduled_at,
                  a.timezone,

                  ls.message_limit_per_day,
                  ls.view_profile_limit_per_day,
                  ls.inmail_limit_per_day,
                  ls.connection_request_limit_per_day,

                  ls.min_delay_seconds,
                  ls.max_delay_seconds,
                  a.org_id
                 FROM linkedin_settings ls
                 INNER JOIN accounts a
                 ON a.id = ls.owner_account_id
                 WHERE ls.uuid = ${linkedinSettingId.id}
                 AND team_id = ${teamId.id}
           """
        .map(rs => {
          val linkedinSetting = LinkedinSettingCreateSchedule(
            uuid = linkedinSettingId.id,
            team_id = rs.int("team_id"),
            account_id = rs.int("owner_account_id"),
            email = rs.string("email"),
            first_name = rs.string("first_name"),
            last_name = rs.string("last_name"),

            linkedin_message_limit_per_day = rs.int("message_limit_per_day"),
            linkedin_view_profile_limit_per_day = rs.int("view_profile_limit_per_day"),
            linkedin_inmail_limit_per_day = rs.int("inmail_limit_per_day"),
            linkedin_connection_request_limit_per_day = rs.int("connection_request_limit_per_day"),
            
            latest_task_scheduled_at = rs.jodaDateTimeOpt("latest_task_scheduled_at"), // Todo add it to linkedin_settings table
            account_timezone = rs.string("timezone"),
            min_delay_seconds = rs.int("min_delay_seconds"), // Todo add it to linkedin_settings table
            max_delay_seconds = rs.int("max_delay_seconds") // Todo add it to linkedin_settings
          )

          ChannelDataForScheduling.LinkedinChannelDataForScheduling(
            linkedinSetting = linkedinSetting,
            channelTeamId = linkedinSetting.team_id,
            account_timezone = linkedinSetting.account_timezone,
            channelOwnerAccountId = linkedinSetting.account_id,
            channelOrgId = OrgId(rs.long("org_id"))
          )
        })
        .headOption
        .apply()

    }
  }

  def updateLastScheduled(
                           linkedinSettingId: String,
                           latestTaskScheduledAt: Option[DateTime]
                         ): Try[Int] = Try {

    // TODO Multichannel: Use teamId in query

    val subquery = latestTaskScheduledAt match {
      case None =>
        sqls"""
            """

      case Some(time) =>
        sqls"""
              latest_task_scheduled_at = $time,
            """
    }

    DB autoCommit {implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
             $subquery
             in_queue_for_scheduling = false,
             updated_at = now()
           WHERE
             uuid = $linkedinSettingId;
         """
        .update
        .apply()
    }

  }

  def updateLastInboxScrapingDoneAt(
                                    linkedinSettingId: LinkedinSettingId,
                                    teamId: TeamId,
                                    lastInboxScrapingDoneAt: DateTime
                                  ): Try[Int] = Try {

    DB autoCommit {implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              last_inbox_scraping_done_at = $lastInboxScrapingDoneAt
           WHERE
              id = ${linkedinSettingId.id}
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }

  }

  def updateConversationsRecoveryJobUid(
                                         linkedinSettingUuid: LinkedinSettingUuid,
                                         teamId: TeamId,
                                         conversationsRecoveryJobUid: CaptainDataJobUID
                                       ): Try[Int] = Try {

    DB autoCommit { implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              conversations_recovery_job_uid = ${conversationsRecoveryJobUid.toString},
              recovery_job_launched_at = now()
           WHERE
              uuid = ${linkedinSettingUuid.uuid}
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }

  }


  def getLastInboxScrapingDoneAt(
                                  captainDataAccountUID: CaptainDataAccountUID
                                ): Try[LinkedinSettingLastScrapedInfo] = Try {

    DB readOnly {implicit session =>
      sql"""
           SELECT id, owner_account_id, uuid, team_id,
           last_inbox_scraping_done_at, conversations_recovery_job_uid
           FROM linkedin_settings
           WHERE
              captain_data_account_id = ${captainDataAccountUID.toString}
           ;
           """
        .map(rs => {
          LinkedinSettingLastScrapedInfo(
            id = LinkedinSettingId(rs.long("id")),
            accountId = AccountId(rs.long("owner_account_id")),
            uuid = LinkedinSettingUuid(rs.string("uuid")),
            team_id = TeamId(rs.long("team_id")),
            last_inbox_scraping_done_at = rs.jodaDateTimeOpt("last_inbox_scraping_done_at"),
            conversations_recovery_job_uid = rs.stringOpt("conversations_recovery_job_uid").map(CaptainDataJobUID(_))
          )
        })
        .single
        .apply()
        .get
    }

  }

  def updateLastInboxScrapingDoneAt(
                                     captainDataAccountUID: CaptainDataAccountUID,
                                     recoveryJobUidIfAny: Option[CaptainDataJobUID],
                                     lastInboxScrapingDoneAt: DateTime
                                   ): Try[Int] = Try {

    val conditionalUpdate = if (recoveryJobUidIfAny.isDefined) {
      sqls", conversations_recovery_job_uid = NULL, prev_conversations_recovery_job_uid = ${recoveryJobUidIfAny.get.toString}"
    } else {
      sqls""
    }

    DB autoCommit {implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              last_inbox_scraping_done_at = $lastInboxScrapingDoneAt
              $conditionalUpdate
           WHERE
              captain_data_account_id = ${captainDataAccountUID.toString}
           ;
           """
        .update
        .apply()
    }

  }

  def getAutomatedLinkedinSettingsByOwnerId(ownerAccountId: AccountId): Try[List[LinkedinAccountSettings]] = Try {
    DB.readOnly { implicit session =>
      sql"""
           SELECT
            ls.uuid,
            ls.first_name,
            ls.last_name,
            ls.email,
            ls.profile_url,
            ls.login_country,
            ls.owner_account_id,
            ls.team_id,
            a.first_name as owner_first_name,
            a.last_name as owner_last_name,
            ls.view_profile_limit_per_day,
            ls.inmail_limit_per_day,
            ls.message_limit_per_day,
            ls.status,
            ls.connection_request_limit_per_day,
            ls.session_cookie,
            ls.captain_data_user_id,
            ls.captain_data_account_id,
            ls.service_provider
           FROM linkedin_settings ls
           INNER JOIN accounts a
           ON a.id = ls.owner_account_id
           WHERE ls.owner_account_id = ${ownerAccountId.id}
           AND ls.captain_data_account_id IS NOT NULL;
         """
        .map(rs => LinkedinSettingDAO.linkedinAccountSettingsFromDB(rs))
        .list
        .apply()
    }
  }

  def fetchLinkedinAccountBatchWithIds(lastProcessedId: Long, batchSize: Int, teamId: Option[TeamId] = None): Try[List[(Long, LinkedinAccountSettings)]] = Try {
    val whereClause = teamId match {
      case Some(tid) => sqls"WHERE ls.team_id = ${tid.id} AND ls.id > $lastProcessedId"
      case None => sqls"WHERE ls.id > $lastProcessedId"
    }

    DB.readOnly { implicit session =>
      sql"""
           SELECT
              ls.id,
              ls.uuid,
              ls.first_name,
              ls.last_name,
              ls.email,
              ls.profile_url,
              ls.login_country,
              ls.owner_account_id,
              ls.team_id,
              a.first_name as owner_first_name,
              a.last_name as owner_last_name,
              ls.view_profile_limit_per_day,
              ls.inmail_limit_per_day,
              ls.message_limit_per_day,
              ls.connection_request_limit_per_day,
              ls.session_cookie,
              ls.status,
              ls.captain_data_user_id,
              ls.captain_data_account_id,
              ls.service_provider
           FROM linkedin_settings ls
           INNER JOIN accounts a
           ON a.id = ls.owner_account_id
           $whereClause
           ORDER BY ls.id
           LIMIT $batchSize
           """
        .map(rs => (rs.long("id"), LinkedinSettingDAO.linkedinAccountSettingsFromDB(rs)))
        .list
        .apply()
    }
  }

  def updateLinkedinProfileUrl(id : Long, uuid: String, teamId: TeamId, normalizedUrl: String): Try[Int] = Try {
    DB.autoCommit { implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET profile_url = $normalizedUrl
           WHERE id = $id AND team_id = ${teamId.id} AND uuid = $uuid
           """
        .update
        .apply()
    }
  }

  def existsLinkedinAccountWithProfileUrlAndCaptainData(profileUrl: String): Try[Boolean] = Try {
    DB.readOnly { implicit session =>
      sql"""
           SELECT EXISTS (
             SELECT 1
             FROM linkedin_settings
             WHERE profile_url = $profileUrl
             AND captain_data_account_id IS NOT NULL
             AND is_active = true
             AND is_deleted = false
             AND deleted_at IS NULL
           ) as exists_account
           """
        .map(rs => rs.boolean("exists_account"))
        .single
        .apply()
        .getOrElse(false)
    }
  }

  def getLinkedinSettingUuidAndStatusFromCaptainDataAccountId(
                                                               captainDataAccountId: CaptainDataAccountUID
                                                             ): Try[LinkedinSettingBasicInfo] = Try {

    DB readOnly { implicit session =>
      sql"""
           SELECT 
            uuid,
            status,
            owner_account_id,
            team_id
           FROM linkedin_settings
           WHERE captain_data_account_id = ${captainDataAccountId.toString}
           """
        .map(rs => LinkedinSettingBasicInfo.fromDB(rs))
        .single
        .apply()
        .get
    }
  }

}

object LinkedinSettingDAO {

  def linkedinAccountSettingsFromDB(rs: WrappedResultSet): LinkedinAccountSettings = {

    val liSess = rs.stringOpt("session_cookie")

    LinkedinAccountSettings(
      uuid = rs.string("uuid"),
      first_name = rs.string("first_name"),
      last_name = rs.string("last_name"),
      email = rs.string("email"),
      profile_url = rs.stringOpt("profile_url").getOrElse(""),
      country = rs.stringOpt("login_country"),
      owner_id = rs.long("owner_account_id"),
      team_id = rs.long("team_id"),
      owner_first_name = rs.string("owner_first_name"),
      owner_last_name = rs.string("owner_last_name"),
      view_profile_limit_per_day = rs.int("view_profile_limit_per_day"),
      inmail_limit_per_day = rs.int("inmail_limit_per_day"),
      message_limit_per_day = rs.int("message_limit_per_day"),
      connection_request_limit_per_day = rs.int("connection_request_limit_per_day"),
      status = LinkedinAccountStatus.fromString(rs.string("status")).get,
      is_session_cookie_synced = liSess.getOrElse("").trim.nonEmpty,
      captain_data_user_id = rs.stringOpt("captain_data_user_id"),
      captain_data_account_id = rs.stringOpt("captain_data_account_id"),
      serviceProvider = rs.stringOpt("service_provider").flatMap(sp => LinkedInServiceProvider.fromKey(sp).getOrElse(None))
    )
  }

}
