package api.linkedin

import api.accounts.models.AccountId
import api.accounts.{AccountRequest, PermType, PermissionRequest, PermissionUtils, TeamId}
import api.campaigns.ChannelSettingUuid
import api.campaigns.services.CampaignService
import api.linkedin.models.CaptainDataWorkflowStatus.CREATED
import api.linkedin.models.{CaptainDataWebhookResponse, CaptainDataWorkflowStatus, CreateOrUpdateLinkedinAccountSettings, LinkedinAccountAndUserAgent, LinkedinSettingUuid, UpdateLinkedinCookieRequest, UserAgent}
import api.linkedin.{LinkedinSettingService, LinkedinSettingUtils, LinkedinValidationError}
import api.middleware.LoggingAction
import api.phantombuster.PhantomBusterService
import play.api.libs.json.{JsError, JsSuccess, JsValue, Json, Reads}
import play.api.libs.ws.WSClient
import play.api.mvc.{<PERSON>, AnyContent, BaseC<PERSON>roller, ControllerComponents}
import utils.{Help<PERSON>, SRLogger, StringUtilsV2}
import utils.helpers.LogHelpers
import utils.testapp.Test_TaskPgDAO.authUtils

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

case class LinkedinCookie(
                         li_at: String,
                         li_a: Option[String]
                         )

object LinkedinCookie {
  given reads: Reads[LinkedinCookie] = Json.reads[LinkedinCookie]
}

class LinkedinSettingController(
                                 loggingAction: LoggingAction,
                                 protected val controllerComponents: ControllerComponents,
                                 linkedinSettingService: LinkedinSettingService,
                                 phantomBusterService: PhantomBusterService,
                                 linkedinSettingUtils: LinkedinSettingUtils,
                                 campaignService: CampaignService,
                                 permissionUtils: PermissionUtils,
                                 implicit val wSClient: WSClient
                               ) extends BaseController {

  private implicit val ec: ExecutionContext = controllerComponents.executionContext

  def addLinkedinAccountSettings(
                                  v: String,
                                  tid: Option[Long]
                                ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CHANNELS, // FIXME
    tidOpt = tid
  ).async(parse.json) { request =>

    given logger: SRLogger = request.Logger
    val res = request.Response
    val teamId = request.actingTeamAccount.get.team_id

    logger.info(s"createLinkedinAccount Data: ${request.body}")


      request.body.validate[CreateOrUpdateLinkedinAccountSettings] match {
        case JsError(errors) =>
          Future.successful(res.JsValidationError(errors))

        case JsSuccess(value, _) =>
          linkedinSettingService.addLinkedinAccountSettings(
            accountSettings = value,
            tid = teamId
          ).map {
            case Left(err) =>
              err match {
                case LinkedinValidationError.SQLException(err) =>
                  res.ServerError("Could not add Linkedin Account.", Some(err))

                case LinkedinValidationError.PlanLimitError(err) =>
                  logger.error(msg = s"Linkedin manual seats limit reach - $err")
                  res.BadRequestError(
                    message = s"$err - Please contact our support if you have any questions."
                  )

                case LinkedinValidationError.ViewProfileLimitError(error) =>
                  res.BadRequestError(error)

                case LinkedinValidationError.MessageLimitError(error) =>
                  res.BadRequestError(error)

                case LinkedinValidationError.InmailLimitError(error) =>
                  res.BadRequestError(error)

                case LinkedinValidationError.ConnectionRequestLimitError(error) =>
                  res.BadRequestError(error)

                case LinkedinValidationError.LoginCountryCannotBeChanged =>
                  res.ServerError("Impossible case", None)

                case LinkedinValidationError.CaptainDataLinkingError(error) =>
                  res.ServerError(
                    "Failed to link Captain Data", Some(error)
                  )

                case LinkedinValidationError.CookieNotPresentError => 
                  res.ServerError(
                    "Cookie not present", None
                  )
                
                case LinkedinValidationError.InvalidLinkedinProfileUrl => 
                  res.BadRequestError("Invalid LinkedIn profile URL. Please provide a valid URL.")

                case LinkedinValidationError.DuplicateLinkedinAccount =>
                  logger.error(s"Duplicate LinkedIn account found for profile URL: ${value.profile_url}")
                  res.BadRequestError("A LinkedIn account with this profile URL already exists in your team.")
              }

            case Right(value) =>
              res.Success("Successfully added Linkedin Account Settings", Json.obj("success" -> true))
          }.recover {
            case ex: Exception =>
              logger.error(s"Unexpected error in addLinkedinAccountSettings: ${ex.getMessage}", ex)
              res.ServerError("An unexpected error occurred while adding LinkedIn account settings.", Some(ex))
          }
      }


  }
  
  def showLoginToLinkedinOption(v: String, tid: Option[Long], uuid: String) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_CHANNELS,
    tidOpt = tid
  ).async {request => 
    
    val res = request.Response
    
    Future {
      linkedinSettingService.showLoginToLinkedinOptionInExtension(
        uuid = LinkedinSettingUuid(uuid),
        teamId = TeamId(request.actingTeamAccount.get.team_id),
        ownerAccountId = AccountId(request.actingTeamAccount.get.user_id)
      ) match {
        case Failure(e) =>
          res.ServerError("Error while fetching Session Sync Status", Some(e))

        case Success(bool) =>
          res.Success("Show Login Option Status", Json.obj("visibility" -> bool))
      }
    }
    
  }

  def linkCaptainDataToLinkedinAccount(v: String, uuid: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CHANNELS,
    tidOpt = tid
  ).async(parse.json) {request =>

    given logger: SRLogger = request.Logger
    val res = request.Response

    if (tid.isEmpty) {
      Future.successful(res.ForbiddenError("Please select a team."))
    }
    else {

      request.body.validate[LinkedinCookie] match {
        case JsError(errors) =>
          Future.successful(res.JsValidationError(errors))

        case JsSuccess(cookie, _) =>
          linkedinSettingService.linkCaptainDataToLinkedinAccount(
              linkedinSettingUuid = LinkedinSettingUuid(uuid),
              liat_cookie = cookie.li_at,
              lia_cookie = cookie.li_a,
              teamId = TeamId(tid.get)
            )
            .map(profile_url => {
              res.Success("Successfully Linked Linkedin Account", Json.obj("profile_url" -> profile_url.toString))
            }).recover(
              e => {
                res.ServerError(f"Error while linking Linkedin Account: ${e.getMessage}", Some(e))
              }
            )
      }

    }}

  
  def updateCaptainDataCookie(v: String, uuid: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CHANNELS,
    tidOpt = tid
  ).async(parse.json) { request =>

    given logger: SRLogger = request.Logger
    val res = request.Response

    if (tid.isEmpty) {
      Future.successful(res.ForbiddenError("Please select a team."))
    }
    else {

      request.body.validate[LinkedinCookie] match {
        case JsError(errors) =>
          Future.successful(res.JsValidationError(errors))

        case JsSuccess(cookie, _) =>
          linkedinSettingService.updateCaptainDataCookie(
              linkedinSettingUuid = LinkedinSettingUuid(uuid),
              liat_cookie = cookie.li_at,
              lia_cookie = cookie.li_a,
              teamId = TeamId(tid.get)
            )
            .map(profile_url => {
              res.Success("Successfully Linked Linkedin Account", Json.obj("profile_url" -> profile_url))
            }).recover(
              e => {
                res.ServerError("Error while linking Linkedin Account", Some(e))
              }
            )
      }

    }
  }

  def removeUserIPFromBlacklist(v: String, uuid: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CHANNELS,
    tidOpt = tid
  ).async {request =>
    
    val res = request.Response
    
    if (tid.isEmpty) {
      Future.successful(res.ForbiddenError("Please select a team."))
    }
    else {
      linkedinSettingService.removeUsersIPFromBlacklist(
        linkedinSettingUuid = LinkedinSettingUuid(uuid),
        teamId = TeamId(tid.get)
      )
        .map(removed => {
          res.Success("Removed IP from blacklist", Json.obj("removed" -> removed))
        })
        .recover {
          case e =>
            res.ServerError("Error while removing User IP from Blacklist", Some(e))
        }
    }
    
  }

  def updateUserAgentAndAssignProxy(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CHANNELS,
    tidOpt = tid
  ).async(parse.json) { request =>

    given logger: SRLogger = request.Logger
    val res = request.Response

    request.body.validate[LinkedinAccountAndUserAgent] match {
      case JsError(errors) =>
        Future.successful(res.JsValidationError(errors))

      case JsSuccess(linkedinAccountAndUserAgent, _) =>
        linkedinSettingService.updateUserAgentAndAssignProxy(
            linkedinAccountAndUserAgent = linkedinAccountAndUserAgent
          )
          .map(proxyIPAndCredentials => {
            res.Success("User Agent Updated", Json.obj("proxy" -> proxyIPAndCredentials))
          })
          .recover {
            case e =>
              res.BadRequestError(e.getMessage)
          }
    }

  }

  def createLinkedinAccountSession(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CHANNELS,
    tidOpt = tid
  ).async(parse.json) {request =>
    given logger: SRLogger= request.Logger
    val res = request.Response

    request.body.validate[LinkedinSettingUuid] match {
      case JsError(e) =>
        Future.successful(res.JsValidationError(e))

      case JsSuccess(uuid, _) =>
        phantomBusterService.createLinkedinAccountSession(
            uuid = uuid,
            teamId = TeamId(request.actingTeamAccount.get.team_id)
          )
          .map(count => {
            if (count == 1) {
              res.Success("Session Create Successful", Json.obj("success" -> true))
            }
            else {
              res.BadRequestError("Unable to create session.")
            }
          })
          .recover {
            case e =>
              logger.error("Error while creating Linkedin session", e)
              res.ServerError(e)
          }
    }

  }

  def updateLinkedinSessionCookie(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CHANNELS,
    tidOpt = tid
  ).async(parse.json) {request =>

    val logger = request.Logger
    val res = request.Response
    val teamId = request.actingTeamAccount.get.team_id

    Future.successful {
      request.body.validate[UpdateLinkedinCookieRequest] match {
        case JsError(errors) =>
          res.JsValidationError(errors)

        case JsSuccess(updateLinkedinCookieRequest, _) =>
          linkedinSettingService.updateLinkedinAccountSessionCookie(
            uuid = updateLinkedinCookieRequest.linkedin_setting_uuid,
            teamId = TeamId(tid.get),
            cookieValue = updateLinkedinCookieRequest.cookie_value
          ) match {
            case Failure(e) =>
              res.ServerError(s"Error while updating linkedin_session_cookie for ${updateLinkedinCookieRequest.linkedin_setting_uuid}", Some(e))

            case Success(value) =>
              res.Success(s"Updated Session Cookie for $value linkedin account.", Json.obj("success" -> true))
          }
      }
    }
  }

  def getLinkedinAccountSettings(
                                  v: String,
                                  tid: Option[Long]
                                ): Action[AnyContent] = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_CHANNELS, // FIXME
    tidOpt = tid
  ) { (request: PermissionRequest[AnyContent]) =>

    val logger = request.Logger
    val res = request.Response
    val permittedAccountIds = request.permittedAccountIds
    val teamId = request.actingTeamAccount.get.team_id

    linkedinSettingService.getLinkedinAccountSettings(
      tid = teamId,
      permittedAccountIds = permittedAccountIds
    ) match {
      case Failure(e) =>
        res.ServerError("Could not fetch Linkedin Account Settings", Some(e))

      case Success(value) =>
        res.Success("Successfully fetched Linkedin Account Settings", Json.obj("accounts" -> value))
    }
  }

  def updateLinkedinAccountSettings(
                                     v: String,
                                     tid: Option[Long],
                                     uuid: String
                                   ) = (permissionUtils.checkPermission(
    version = v,
    permission =PermType.EDIT_CHANNELS, // FIXME
    tidOpt = tid
  ) andThen linkedinSettingUtils.hasLinkedinSetting(uuid = uuid)).async(parse.json) { request =>

    given logger: SRLogger = request.Logger
    val res = request.Response
    val teamId = request.actingTeamAccount.team_id


      request.body.validate[CreateOrUpdateLinkedinAccountSettings] match {
        case JsError(errors) =>
          Future.successful(res.JsValidationError(errors))

        case JsSuccess(value, _) =>
          linkedinSettingService
            .updateLinkedinAccountSettings(
              data = value,
              uuid = uuid,
              teamId =  teamId
            ).map {
            case Left(err) =>
              err match {
                case LinkedinValidationError.SQLException(err) =>
                  res.ServerError("Could not add Linkedin Account.", Some(err))

                case LinkedinValidationError.PlanLimitError(err) =>

                  logger.shouldNeverHappen(
                    msg = s"Got Linkedin manual seats plan limit err - updateLinkedinAccountSettings - $err",
                    err = None,
                  )

                  res.ServerError(
                    message = "Failed to add linkedin account, please contact support.",
                    e = None,
                  )

                case LinkedinValidationError.ViewProfileLimitError(err) =>
                  res.BadRequestError(err)

                case LinkedinValidationError.MessageLimitError(err) =>
                  res.BadRequestError(err)

                case LinkedinValidationError.InmailLimitError(err) =>
                  res.BadRequestError(err)

                case LinkedinValidationError.ConnectionRequestLimitError(err) =>
                  res.BadRequestError(err)

                case LinkedinValidationError.LoginCountryCannotBeChanged =>
                  res.BadRequestError("Login Country cannot be changed.")

                case LinkedinValidationError.CaptainDataLinkingError(error) =>
                  res.ServerError(
                    "Failed to link Captain Data", Some(error)
                  )

                case LinkedinValidationError.CookieNotPresentError =>
                  res.ServerError(
                    "Cookie not present", None
                  )

                case LinkedinValidationError.InvalidLinkedinProfileUrl =>
                  logger.error(s"Invalid LinkedIn profile URL: ${value.profile_url}")
                  res.BadRequestError("Invalid LinkedIn profile URL. Please provide a valid URL.")

                case LinkedinValidationError.DuplicateLinkedinAccount =>
                  logger.error(s"Duplicate LinkedIn account found for profile URL: ${value.profile_url}")
                  res.BadRequestError("A LinkedIn account with this profile URL already exists in your team.")
              }

            case Right(_) =>
              res.Success("Successfully updated Linkedin Account Settings", Json.obj("success" -> true))
          }.recover {
              case ex: Exception =>
                logger.error(s"Unexpected error in updateLinkedinAccountSettings: ${ex.getMessage}", ex)
                res.ServerError("An unexpected error occurred while updating LinkedIn account settings.", Some(ex))
            }

    }
  }

  def handleCaptainDataCreatedWebhook(v: String)= loggingAction.async(parse.json) { request =>
    val uri = request.uri
    val parsedParams = StringUtilsV2.getParams(request.uri)
    val body = request.body
    val Res = request.Response

//    println("uri ----> " + uri)
//    println("parsed params ----> " + parsedParams)
//    println("body ----> " + body)

    /*
    {
    "uid": "2d63fd8d-3ea9-4706-8d1a-f88b6c11d815",
    "job_uid": "da14f706-f5f7-486f-9938-9dc1f762f48e",
    "message": "Job successfully created.",
    "project_uid": "515a9e1f-6690-4e37-9c02-956f559a65eb",
    "error": null,
    "recipe_ref": "da14f706-f5f7-486f-9938-9dc1f762f48e",
    "workflow_uid": "10330e77-e400-458d-bd08-3b2c5432305e",
    "workflow_name": "Connect LinkedIn Profile",
    "job_name": "Connect with LinkedIn Profile: https://www.linkedin.com/in/tulika-t/"
}
     */


    body.validate[CaptainDataWebhookResponse] match {

      case JsError(errors) =>
        request.Logger.fatal(s"JsError ---- Failed to parse error --- $errors data ----- $body")

        Future.successful(Res.BadRequestError("data parsing failed"))


      case JsSuccess(value, _) =>
        given logger: SRLogger = request.Logger.appendLogRequestId(value.job_uid.uid);
        logger.info(s"[handleCaptainDataCreatedWebhook] - received created webhook ${body}")

        linkedinSettingService.handleCaptainDataWebhookStatusUpdate(
          data = value,
          status = CaptainDataWorkflowStatus.CREATED
        ).map( value => {

          logger.info(s"[handleCaptainDataCreatedWebhook] - updated status of job_uid ${value.uid} to created")
          Ok("Success")

        })
          .recover(exception => {
            logger.error("[handleCaptainDataCreatedWebhook] - error while updating data in db", exception)
            Res.BadRequestError("Error while updating data in db")

          })
    }
  }

  def handleCaptainDataSuccessWebhook(v: String) = loggingAction.async(parse.json) { request =>
    val uri = request.uri
    val parsedParams = StringUtilsV2.getParams(request.uri)
    val body = request.body
    val Res = request.Response

    //    println("uri ----> " + uri)
    //    println("parsed params ----> " + parsedParams)
    //    println("body ----> " + body)

    /*
    {
    "uid": "5a7c156d-056e-44ea-be72-ac5447e7b998",
    "job_uid": "da14f706-f5f7-486f-9938-9dc1f762f48e",
    "message": "Job successfully finished.",
    "project_uid": "515a9e1f-6690-4e37-9c02-956f559a65eb",
    "error": null,
    "recipe_ref": "da14f706-f5f7-486f-9938-9dc1f762f48e",
    "workflow_uid": "10330e77-e400-458d-bd08-3b2c5432305e",
    "workflow_name": "Connect LinkedIn Profile",
    "job_name": "Connect with LinkedIn Profile: https://www.linkedin.com/in/tulika-t/"
}
     */


    body.validate[CaptainDataWebhookResponse] match {

      case JsError(errors) =>
        request.Logger.fatal(s"JsError ---- Failed to parse error --- $errors data ----- $body")

        Future.successful(Res.BadRequestError("data parsing failed"))


      case JsSuccess(value, _) =>
        given logger: SRLogger = request.Logger.appendLogRequestId(value.job_uid.uid);
        logger.info(s"[handleCaptainDataSuccessWebhook] - received created webhook ${body}")

        linkedinSettingService.handleCaptainDataWebhookStatusUpdate(
          data = value,
          status = CaptainDataWorkflowStatus.SUCCESSFUL
        ).map( value => {

            logger.info(s"[handleCaptainDataSuccessWebhook] - updated status of job_uid ${value.uid} to successful")
            Ok("Success")

          })
          .recover(exception => {
            logger.error("[handleCaptainDataSuccessWebhook] - error while updating data in db", exception)
            Res.BadRequestError("Error while updating data in db")

          })
    }
  }


  def handleCaptainDataFailedWebhook(v: String) = loggingAction.async(parse.json) { request =>
    val uri = request.uri
    val parsedParams = StringUtilsV2.getParams(request.uri)
    val body = request.body
    val Res = request.Response

    //    println("uri ----> " + uri)
    //    println("parsed params ----> " + parsedParams)
    //    println("body ----> " + body)

    /*
    {
    "uid": "52b6bdd8-2473-4c9a-990a-afae8c8b1177",
    "job_uid": "1ba89537-a7f5-453f-97ec-233e19b96fd8",
    "message": "Job failed.",
    "project_uid": "515a9e1f-6690-4e37-9c02-956f559a65eb",
    "error": "Error while completing action: cannot re-send connection request yet.",
    "recipe_ref": "1ba89537-a7f5-453f-97ec-233e19b96fd8",
    "workflow_uid": "10330e77-e400-458d-bd08-3b2c5432305e",
    "workflow_name": "Connect LinkedIn Profile",
    "job_name": "Connect with LinkedIn Profile: https://www.linkedin.com/in/tulika-tiwari-80960635b/"
    }
     */


    body.validate[CaptainDataWebhookResponse] match {

      case JsError(errors) =>
        request.Logger.fatal(s"JsError ---- Failed to parse error --- $errors data ----- $body")

        Future.successful(Res.BadRequestError("data parsing failed"))


      case JsSuccess(value, _) =>
        given logger: SRLogger = request.Logger.appendLogRequestId(value.job_uid.uid);
        logger.info(s"[handleCaptainDataSuccessWebhook] - received created webhook ${body}")

        linkedinSettingService.handleCaptainDataWebhookStatusUpdate(
          data = value,
          status = CaptainDataWorkflowStatus.FAILURE
        ) .map( value => {

            logger.info(s"[handleCaptainDataSuccessWebhook] - updated status of job_uid ${value.uid} to successful")
            Ok("Success")

          })
          .recover(exception => {
            logger.error("[handleCaptainDataSuccessWebhook] - error while updating data in db", exception)
            Res.BadRequestError("Error while updating data in db")

          })
    }
  }

  def deleteLinkedinAccount(
                             v: String,
                             tid: Option[Long],
                             uuid: String
                           ): Action[AnyContent] = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CHANNELS,
    tidOpt = tid
  ) andThen linkedinSettingUtils.hasLinkedinSetting(uuid = uuid)).async { (request: LinkedinRequest[AnyContent]) =>

    given logger: SRLogger = request.Logger
    val res = request.Response
    val teamId = request.actingTeamAccount.team_id

    logger.info(s"DELETE linkedinSetting called by ${request.loggedinAccount.internal_id} :: $uuid")

    campaignService.pauseAllCampaignsAndDeleteSettingIdFromCampaigns(
      channelSettingUuid = ChannelSettingUuid(
        uuid = uuid
      ),
      team_id = TeamId(teamId),
      status_changed_by = s"deleteLinkedinAccount  by ${request.loggedinAccount.email}"
    ) match {
      case Failure(e) =>
        logger.error(s"[FATAL] LinkedinSettingController.deleteLinkedinAccount Campaign.pauseAllCampaignsAndDeleteSettingIdFromCampaigns :: linkedinSettingUuid: $uuid ${LogHelpers.getStackTraceAsString(e)}")

        Future.successful(res.ServerError("Could not delete Linkedin Account", Some(e)))

      case Success(pausedCampaignIds) =>

        logger.info(s"DELETE deleteLinkedinSetting $uuid: paused campaigns: ${pausedCampaignIds.mkString(",")}")

        linkedinSettingService
          .setLinkedinAccountAsInactive(uuid = LinkedinSettingUuid(uuid = uuid), team_id = TeamId(id = teamId)).map { n =>
          if (n == 1) res.Success(s"Deleted linkedin account successfully", data = Json.obj())
          else res.NotFoundError(s"linkedin account not found. Could you please check and retry ?")
        } recover {
          case e =>
            res.ServerError("Could not delete Linkedin Account", Some(e))
        }

    }

  }

  def updateCookieToLinkedinAccountsOfOwner(v: String) = authUtils.isLoggedIn().async(parse.json) { (request: AccountRequest[JsValue]) =>

    val Res = request.Response
    val account = request.account

    given logger: SRLogger = request.Logger

    request.body.validate[LinkedinCookie] match {
        case JsError(errors) =>
            logger.error(s"Failed to parse LinkedinCookie: ${request.body} , accountId: ${account.internal_id}")
            Future.successful(Res.JsValidationError(errors))

        case JsSuccess(cookie, _) =>
          linkedinSettingService.updateCookieToLinkedinAccountsOfOwner(
            ownerAccountId = AccountId(account.internal_id),
            cookie = cookie
          ).map(_ => {
            Res.Success(
              "Successfully validated cookie",
              Json.obj(
                "li_at" -> cookie.li_at,
                "li_a" -> cookie.li_a
              )
            )
          }).recover {
            case e => 
              logger.error("Failed to update cookie for LinkedIn accounts of owner with accountId: " + account.internal_id, e)
              Res.ServerError("Failed to update LinkedIn cookie", Some(e))
          }
    }
  }
}
