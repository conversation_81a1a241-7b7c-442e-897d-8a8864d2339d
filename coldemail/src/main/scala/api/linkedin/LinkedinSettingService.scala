package api.linkedin

import api.{AppConfig, ChannelSettingServiceTrait}
import api.accounts.{Account, AccountService, TeamId}
import api.accounts.models.OrgId
import api.accounts.service.{AccountOrgBillingRelatedService, OrganizationService}
import api.accounts.models.AccountId
import api.captain_data.{<PERSON><PERSON><PERSON><PERSON><PERSON>, Captain<PERSON><PERSON><PERSON><PERSON>untD<PERSON><PERSON>, Captain<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>in<PERSON><PERSON>, Captain<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Captain<PERSON><PERSON><PERSON><PERSON><PERSON>n<PERSON>, Captain<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Captain<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CaptainDataUserCreateRequest, CaptainDataUserUID}
import api.linkedin.models.{<PERSON><PERSON><PERSON><PERSON><PERSON>, Captain<PERSON><PERSON>Web<PERSON>okResponse, Captain<PERSON><PERSON><PERSON>ork<PERSON>N<PERSON>, CaptainDataWorkflowStatus, CreateOrUpdateLinkedinAccountSettings, LinkedinAccountAndLastInboxScrapeTime, LinkedinAccountAndUserAgent, LinkedinAccountCredentialsAndUserAgent, LinkedinAccountSettings, LinkedinAccountStatus, LinkedinAccountUuidAndTeamId, LinkedinAccountsWithLastScrapeTime, LinkedinSessionCookieAndUserAgent, LinkedinSettingId, LinkedinSettingUuid, ProxyIdAndLoginCountry, UserAgent}
import api.linkedin.models.{CDJobUid, CaptainDataWebhookResponse, CaptainDataWorkflowName, CaptainDataWorkflowStatus, CreateOrUpdateLinkedinAccountSettings, LinkedinAccountAndLastInboxScrapeTime, LinkedinAccountAndUserAgent, LinkedinAccountCredentialsAndUserAgent, LinkedinAccountSettings, LinkedinAccountStatus, LinkedinAccountUuidAndTeamId, LinkedinAccountsWithLastScrapeTime, LinkedinSessionCookieAndUserAgent, LinkedinSettingId, LinkedinSettingUuid, ProxyIdAndLoginCountry, UserAgent}
import api.linkedin.LinkedinSettingDAO
import api.phantombuster.{CaptainDataService, ExecuteAutoLinkedinTasksError, LinkedinMessageThreadUrl, PhantomBusterContainerId, PhantomBusterLinkedinMessage, PhantomBusterRunningInboxContainerDetails, PhantomBusterRunningMessageThreadContainerDetails, PhantomBusterService, PhantomBusterWebhookStatus}
import api.phantombuster_proxy.{PhantomBusterProxyService, ProxyDetails, ProxyIPAndCredentials}
import api.tasks.models.{ChangeStatusPermissionCheck, Task, TaskData, TaskType, TasksGroupedByTypeAndLinkedinSetting, UpdateTaskStatus}
import api.tasks.services.{ChangeStatusTypeError, TaskDaoService, TaskService}
import io.sr.billing_common.models.AddonLicenceType
import org.joda.time.DateTime
import play.api.libs.ws.WSClient
import sr_scheduler.models.ChannelType
import utils.cache.models.SrResetCacheInterval
import utils.{AddonLimitReachedException, Helpers, MaxPlanLimitsAndCurrentUsage, PlanLimitService, SRLogger}
import utils.cache_utils.model.CacheIdKeyForLock
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import utils.email_notification.service.EmailNotificationService
import utils.linkedin.LinkedinHelperFunctions
import utils.mq.captainData.{ExtractLinkedinConnectionResultsData, LinkedinSettingDeletionData, MqExtractLinkedinConnectionResults}
import utils.mq.captain_data.{MqCaptainDataConversationExtractionMessage, MqCaptainDataConversationExtractionPublisher, MqCaptainDataCookieFailureMessage, MqCaptainDataCookieFailurePublisher, MqCaptainDataMessageExtractionMessage, MqCaptainDataMessageExtractionPublisher}
import utils.mq.channel_scheduler.channels.{ChannelId, ChannelSchedulerTrait}
import utils.proxy.brightdata.BrightDataService
import utils.random.SrRandomUtils
import utils.security.EncryptionService
import utils.timezones.TimezoneUtils
import utils.uuid.SrUuidUtils

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

sealed trait LinkedinValidationError

object LinkedinValidationError {
  case class ViewProfileLimitError(err: String) extends LinkedinValidationError

  case class ConnectionRequestLimitError(err: String) extends LinkedinValidationError

  case class MessageLimitError(err: String) extends LinkedinValidationError

  case class InmailLimitError(err: String) extends LinkedinValidationError

  case class PlanLimitError(err: String) extends LinkedinValidationError

  case object LoginCountryCannotBeChanged extends LinkedinValidationError

  case class SQLException(err: Throwable) extends LinkedinValidationError

  case class CaptainDataLinkingError(err: Throwable) extends LinkedinValidationError

  case object CookieNotPresentError extends LinkedinValidationError
  
  case object InvalidLinkedinProfileUrl extends LinkedinValidationError
  
  case object DuplicateLinkedinAccount extends LinkedinValidationError
}


case class LinkedinSettingUuidAndTeamId(
                                       linkedinSettingUuid: LinkedinSettingUuid,
                                       teamId: TeamId
                                       )

class LinkedinSettingService(linkedinSettingDAO: LinkedinSettingDAO,
                             taskService: TaskService,
                             organizationService: OrganizationService,
                             accountService: AccountService,
                             emailNotificationService: EmailNotificationService,
                             planLimitService: PlanLimitService,
                             phantomBusterProxyService: PhantomBusterProxyService,
                             brightDataService: BrightDataService,
                             accountOrgBillingRelatedService: AccountOrgBillingRelatedService,
                             mqExtractLinkedinConnectionResults: MqExtractLinkedinConnectionResults,
                             srRandomUtils: SrRandomUtils,
                             override protected val srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2,
                             captainDataService: CaptainDataService,
                             mqCaptainDataConversationExtractionPublisher: MqCaptainDataConversationExtractionPublisher,
                             mqCaptainDataMessageExtractionPublisher: MqCaptainDataMessageExtractionPublisher,
                             mqCaptainDataCookieFailurePublisher: MqCaptainDataCookieFailurePublisher,
                             srUuidUtils: SrUuidUtils,
                             linkedinConnectionsDAO: LinkedinConnectionsDAO,
                             captainDataAPI: CaptainDataAPI) extends ChannelSettingServiceTrait {

  override val channelType: ChannelType = ChannelType.LinkedinChannel

  def addLinkedinAccountSettings(
                                  accountSettings: CreateOrUpdateLinkedinAccountSettings,
                                  tid: Long
                                )(using Logger: SRLogger, WSClient: WSClient, ec: ExecutionContext): Future[Either[LinkedinValidationError, Long]] = {

    if (accountSettings.allow_automated_task.contains(true) && accountSettings.li_at_cookie.isEmpty) {
      Future.successful(Left(LinkedinValidationError.CookieNotPresentError))
    } else {
      // Normalize LinkedIn profile URL
      val normalizedProfileUrlOpt = LinkedinHelperFunctions.normalizeLinkedInURL(Some(accountSettings.profile_url))

      if (normalizedProfileUrlOpt.isEmpty) {
        Future.successful(Left(LinkedinValidationError.InvalidLinkedinProfileUrl))
      } else {
        val normalizedUrl = normalizedProfileUrlOpt.get

        // Check if a LinkedIn account with the same normalized URL already exists with Captain Data
        linkedinSettingDAO.existsLinkedinAccountWithProfileUrlAndCaptainData(normalizedUrl) match {
          case Success(true) =>
            // Account with the same URL and Captain Data account ID already exists
            Future.successful(Left(LinkedinValidationError.DuplicateLinkedinAccount))

          case Success(false) =>
            // No duplicate account found, proceed with creation
            // Create a copy of accountSettings with normalized URL
            val normalizedAccountSettings = accountSettings.copy(
              profile_url = normalizedUrl
            )

            planLimitService.isLinkedManualOrAutomatedLimitReached(
              teamId = TeamId(id = tid),
              allow_automated_task = normalizedAccountSettings.allow_automated_task.getOrElse(false)
            ) match {

              case Failure(exception: AddonLimitReachedException) =>
                Future.successful(Left(LinkedinValidationError.PlanLimitError(err = exception.getMessage)))

              case Failure(exception) =>
                Future.successful(Left(
                  LinkedinValidationError.SQLException(
                    err = exception,
                  )
                ))

              case Success(_) =>
                val linkedinAccountId = srUuidUtils.generateLinkedinAccountUuid

                LinkedinSettingService.validateLimit(accountSettings = normalizedAccountSettings) match {
                  case Some(err) => Future.successful(Left(err))

                  case None =>
                    linkedinSettingDAO.createLinkedinAccount(
                      data = normalizedAccountSettings,
                      teamId = tid,
                      uuid = linkedinAccountId
                    ) match {
                      case Failure(e) => Future.successful(Left(LinkedinValidationError.SQLException(e)))

                      case Success(id) =>
                        (normalizedAccountSettings.allow_automated_task, normalizedAccountSettings.li_at_cookie) match {
                          case (Some(true), Some(liat_cookie)) =>
                            // Convert the UUID to LinkedinSettingUuid type
                            val linkedinSettingUuid = LinkedinSettingUuid(uuid = linkedinAccountId)

                            // Call linkCaptainDataToLinkedinAccount
                            linkCaptainDataToLinkedinAccount(
                              linkedinSettingUuid = linkedinSettingUuid,
                              liat_cookie = liat_cookie,
                              lia_cookie = normalizedAccountSettings.li_a_cookie,
                              teamId = TeamId(id = tid)
                            ).map(_ => Right(id))
                              .recover {
                                case ex: Exception =>
                                  Logger.error(s"Failed to link Captain Data to LinkedIn account: ${ex.getMessage}", ex)
                                  // Create a custom LinkedinValidationError for Captain Data failures
                                  Left(LinkedinValidationError.CaptainDataLinkingError(err = ex))
                              }

                          case _ =>
                            // Conditions not met, just return the ID
                            Future.successful(Right(id))
                        }
                    }
                }
            }
          case Failure(err) =>
            Logger.error(s"Error while checking for duplicate LinkedIn account: ${err.getMessage}", err)
            Future.successful(Left(LinkedinValidationError.SQLException(err)))
        }
      }
    }
  }

  def getLinkedinAccountSettings(tid: Long, permittedAccountIds: Seq[Long]): Try[List[LinkedinAccountSettings]] = {
    linkedinSettingDAO.getLinkedinAccountSettings(
      teamId = tid,
      permittedAccountIds = permittedAccountIds
    )
  }
  
  def getInactiveLinkedinSettings: Try[List[LinkedinSettingDeletionData]] = {
    linkedinSettingDAO.getInactiveLinkedinSettings
    
  }
  
  def showLoginToLinkedinOptionInExtension(
                                            uuid: LinkedinSettingUuid, 
                                            teamId: TeamId, 
                                            ownerAccountId: AccountId
                                          ): Try[Boolean] = {
    
    linkedinSettingDAO.fetchSessionSyncStatusForLinkedinAccount(
      uuid = uuid,
      teamId = teamId,
        ownerAccountId = ownerAccountId
    )
      .map {
        case None => false

        case Some(sessionSyncStatusDetails) =>
          if (sessionSyncStatusDetails.phantombuster_proxy_id.isEmpty ||
            sessionSyncStatusDetails.consecutive_recreate_session_attempts >= AppConfig.Linkedin_Automation.maxLinkedinSessionCreateRetriesPerDay ||
            sessionSyncStatusDetails.status == LinkedinAccountStatus.NeedToAssignProxy ||
            sessionSyncStatusDetails.encryptedSessionCookie.isEmpty
          ) {
            true
          }
          else {
            false
          }
      }
    
  }

  def updateLinkedinAccountSessionCookie(uuid: String, teamId: TeamId, cookieValue: String): Try[Int] = {

    for {
      updateSessionCookie: Int <- linkedinSettingDAO.updateLinkedinAccountSessionCookie(
        uuid = uuid,
        teamId = teamId,
        cookieValue = cookieValue
      )

      updateTaskStatus: Int <- taskService.moveTasksToDueOnSessionCookieUpdate(
        linkedinSettingUuid = uuid,
        teamId = teamId
      )

      _: Int <- linkedinSettingDAO.removeSessionCookieErrorOnUpdate(
        linkedinSettingUuid = uuid,
        teamId = teamId
      )
    } yield {
      updateSessionCookie
    }
  }

  def removeUsersIPFromBlacklist(
                                  linkedinSettingUuid: LinkedinSettingUuid,
                                  teamId: TeamId
                                )(implicit ws: WSClient,
                                  ec: ExecutionContext): Future[Boolean] = {
    for {
      proxyIPAndCredentials: ProxyIPAndCredentials <- Future.fromTry {
        linkedinSettingDAO.getProxyDetailsForLinkedinAccount(
          linkedinSettingUuid = linkedinSettingUuid,
          teamId = teamId
        )
          .flatMap {
            case None =>
              Failure(new Exception("Linkedin account does not have a proxy."))

            case Some(p) =>
              Success(p)
          }
      }

      removedIPs: Boolean <- brightDataService.getAndRemoveIPFromBlackList(
        zone = PhantomBusterProxyService.convertProxyUsernameToZone(proxyIPAndCredentials.username.get)
      )
    } yield {
      removedIPs
    }
  }

  def setNextTaskToBeExecutedAt(
                                 linkedinSettingUuid: LinkedinSettingUuid, 
                                 teamId: TeamId,
                                 cooldownTimeInMinutes: Int
                               ): Try[Int] = {

    val nextTaskExecuteTime = DateTime.now().plusMinutes(cooldownTimeInMinutes)

    linkedinSettingDAO.setNextTaskToBeExecutedAt(
      linkedinSettingUuid = linkedinSettingUuid,
      teamId = teamId,
      nextTaskExecuteTime = nextTaskExecuteTime
    )

  }

  def handleExtractLinkedinConnectionsWebhook( data: CaptainDataWebhookResponse,
                                        status: CaptainDataWorkflowStatus)
                                      (using logger: SRLogger, ec: ExecutionContext):  Future[CDJobUid] = {
    logger.info(s"[handleExtractLinkedinConnections] ::: data :: ${data}")
    status match {
      case CaptainDataWorkflowStatus.INITIATED | CaptainDataWorkflowStatus.CREATED =>
        logger.info(s"workflow created for ${data.workflow_name.toString} for job_uid :: ${data.job_uid.uid}")
        Future.successful(data.job_uid)


      case CaptainDataWorkflowStatus.SUCCESSFUL =>
       Future.fromTry(mqExtractLinkedinConnectionResults.publish(

          msg = ExtractLinkedinConnectionResultsData(
            data = data
          )

        )).map(_ => data.job_uid)

      case CaptainDataWorkflowStatus.FAILURE =>
        if (isErrorRelatedToCookieFailure(data.error.getOrElse(""))) {
          logger.info(s"Job failed due to cookie failure. job_uid :: ${data.job_uid.toString}")
        } else {
          logger.shouldNeverHappen(s"workflow failed for extract linkedin connections:: data :: ${data.job_uid.uid} error:: ${data.error}")
        }
        Future.successful(data.job_uid)
    }


  }

  private def updateStatusAndSendEmailNotificationForCookieFailure(
                                   linkedinSettingBasicInfo: LinkedinSettingBasicInfo
                                 )(using logger: SRLogger, ec: ExecutionContext, ws: WSClient): Try[Int] ={
    for {
      account: Account <- accountService.find(id = linkedinSettingBasicInfo.owner_account_id.id)

      _: Unit <- emailNotificationService.sendMailFromAdmin(
        toEmail = account.email,
        toName = Some(Helpers.getAccountName(account)),
        subject = s"Action Required: Reconnect Your LinkedIn Account on SmartReach",
        body =
          s"""
               <br/>Hi ${account.first_name.getOrElse("User")},
               <br/>
               <br/>Your LinkedIn account linked to SmartReach faces connection issues. Your automated LinkedIn tasks are paused.
               <br/>Please reconnect your LinkedIn account with SmartReach. <a href="https://app.smartreach.io/dashboard/account_settings/linkedin_accounts?tid=${linkedinSettingBasicInfo.team_id.id}">click here </a> to reconnect.
               <br/>
               <br/>
               <br/>Regards,
               <br/>SmartReach.io Team
            """.stripMargin
      )

      rowsUpdated: Int <- linkedinSettingDAO.updateLinkedinAccountStatus(
        uuid = linkedinSettingBasicInfo.uuid,
        teamId = linkedinSettingBasicInfo.team_id,
        status = LinkedinAccountStatus.NeedToRecreateSession
      )
    } yield {
      rowsUpdated
    }
  }

  def handleCookieFailure(
                           jobUid: CaptainDataJobUID
                         )(using ws: WSClient, ec: ExecutionContext, logger: SRLogger): Future[Int]={
    logger.info(s"handleCookieFailure :: job_uid :: ${jobUid.toString}")
    for {
      jobInfo: CaptainDataJobInfo <- captainDataAPI.getJobInfo(
        jobUid = jobUid
      )

      linkedinSettingBasicInfo: LinkedinSettingBasicInfo <- Future.fromTry(linkedinSettingDAO.getLinkedinSettingUuidAndStatusFromCaptainDataAccountId(
        captainDataAccountId = CaptainDataAccountUID(jobInfo.accounts.head)
      ))

      rowsUpdated: Int <- if (linkedinSettingBasicInfo.status != LinkedinAccountStatus.NeedToRecreateSession) {
        Future.fromTry(updateStatusAndSendEmailNotificationForCookieFailure(linkedinSettingBasicInfo))
      } else {
        Future.successful(0)
      }

    } yield {
      rowsUpdated
    }
  }

  private def isErrorRelatedToCookieFailure(errorMsg: String): Boolean = {
    errorMsg.toLowerCase.contains("your cookie is not valid")
  }

  def handleCookieFailureIfAny(
                                jobUid: CaptainDataJobUID,
                                errorMsg: String
                              )(using ws: WSClient, ec: ExecutionContext, logger: SRLogger): Boolean = {
    val isJobFailedDueToCookie = isErrorRelatedToCookieFailure(errorMsg)

    if (isJobFailedDueToCookie) {
      mqCaptainDataCookieFailurePublisher.publishMessage(
        msg = MqCaptainDataCookieFailureMessage(
          jobUid = jobUid
        )
      )
    }

    isJobFailedDueToCookie
  }


  def handleCaptainDataWebhookStatusUpdate(
                                            data: CaptainDataWebhookResponse,
                                            status: CaptainDataWorkflowStatus)
                                          (using logger: SRLogger, ec: ExecutionContext, ws: WSClient): Future[CDJobUid] = {

    data.workflow_name match {
      case CaptainDataWorkflowName.CONNECT_LINKEDIN_PROFILE |
           CaptainDataWorkflowName.MESSAGE_LINKEDIN_PROFILE |
           CaptainDataWorkflowName.VISIT_LINKEDIN_PROFILE =>

        if (status == CaptainDataWorkflowStatus.FAILURE &&
          handleCookieFailureIfAny(jobUid = CaptainDataJobUID(uid = data.job_uid.uid), errorMsg = data.error.getOrElse(""))) {
          logger.info(s"Job failed due to cookie failure. job_uid :: ${data.job_uid.toString}")
          taskService.handleCaptainDataWebhookStatusUpdate(data = data.copy(
            error = Some("Your LinkedIn account is disconnected from SmartReach. please reconnect to resume automated tasks.")
          ), status = status)
        } else {
          taskService.handleCaptainDataWebhookStatusUpdate(data = data, status = status)
        }

      case CaptainDataWorkflowName.EXTRACT_LINKEDIN_CONVERSATIONS =>
        if (status == CaptainDataWorkflowStatus.FAILURE &&
          handleCookieFailureIfAny(jobUid = CaptainDataJobUID(uid = data.job_uid.uid), errorMsg = data.error.getOrElse(""))) {
          logger.info(s"Job failed due to cookie failure. job_uid :: ${data.job_uid.toString}")
        } else {

          mqCaptainDataConversationExtractionPublisher.publishMessage(
            msg = MqCaptainDataConversationExtractionMessage(
              jobUid = data.job_uid,
              status = status,
              error = data.error
            )
          )
        }
        Future.successful(data.job_uid)

      case CaptainDataWorkflowName.EXTRACT_LINKEDIN_MESSAGES =>
        if (status == CaptainDataWorkflowStatus.FAILURE &&
          handleCookieFailureIfAny(jobUid = CaptainDataJobUID(uid = data.job_uid.uid), errorMsg = data.error.getOrElse(""))) {
          logger.info(s"Job failed due to cookie failure. job_uid :: ${data.job_uid.toString}")
        } else {

          mqCaptainDataMessageExtractionPublisher.publishMessage(
            msg = MqCaptainDataMessageExtractionMessage(
              jobUid = data.job_uid,
              status = status,
              error = data.error
            ))
        }
        Future.successful(data.job_uid)

      case CaptainDataWorkflowName.EXTRACT_LINKEDIN_CONNECTIONS =>
        if (status == CaptainDataWorkflowStatus.FAILURE &&
          handleCookieFailureIfAny(jobUid = CaptainDataJobUID(uid = data.job_uid.uid), errorMsg = data.error.getOrElse(""))) {
          logger.info(s"Job failed due to cookie failure. job_uid :: ${data.job_uid.toString}")
          Future.successful(data.job_uid)
        } else {
          handleExtractLinkedinConnectionsWebhook(data = data, status = status)
        }
    }



  }


  def updateLinkedinAccountSettings(
                                     data: CreateOrUpdateLinkedinAccountSettings,
                                     uuid: String,
                                     teamId: Long
                                   )(using logger: SRLogger, WSClient: WSClient, ec: ExecutionContext): Future[Either[LinkedinValidationError, LinkedinAccountSettings]] = {

    if (data.allow_automated_task.contains(true) && data.li_at_cookie.isEmpty) {
      Future.successful(Left(LinkedinValidationError.CookieNotPresentError))
    } else {
      // Normalize LinkedIn profile URL
      val normalizedProfileUrlOpt = LinkedinHelperFunctions.normalizeLinkedInURL(Some(data.profile_url))

      if (normalizedProfileUrlOpt.isEmpty) {
        Future.successful(Left(LinkedinValidationError.InvalidLinkedinProfileUrl))
      } else {
        val normalizedUrl = normalizedProfileUrlOpt.get
        
        LinkedinSettingService.validateLimit(accountSettings = data) match {
          case Some(err) => Future.successful(Left(err))
          case None =>
            linkedinSettingDAO.getProxyIdAndLoginCountry(uuid = LinkedinSettingUuid(uuid), teamId = TeamId(teamId)) match {
              case Failure(e) =>
                Future.successful(Left(LinkedinValidationError.SQLException(e)))

              case Success(proxyIdAndLoginCountry: ProxyIdAndLoginCountry) =>
                if (proxyIdAndLoginCountry.proxyId.isDefined &&
                  proxyIdAndLoginCountry.loginCountry != data.country) {

                  Future.successful(Left(LinkedinValidationError.LoginCountryCannotBeChanged))

                }
                else {
                  
                  linkedinSettingDAO.updateLinkedinAccountSettings(
                    data = data.copy(profile_url = normalizedUrl),
                    uuid = uuid,
                    teamId = teamId
                  ) match {
                    case Failure(e) =>
                      Future.successful(Left(LinkedinValidationError.SQLException(e)))

                    case Success(linkedinSetting: LinkedinAccountSettings) =>

                      if (linkedinSetting.captain_data_user_id.isDefined){
                        logger.info(s"Skipping linking to captain data as Captain data user id is already present for linkedin account :: ${linkedinSetting.uuid}")
                        Future.successful(Right(linkedinSetting))
                      } else {
                        (data.allow_automated_task, data.li_at_cookie) match {
                          case (Some(true), Some(liat_cookie)) =>
                            // Convert the UUID to LinkedinSettingUuid type
                            val linkedinSettingUuid = LinkedinSettingUuid(uuid = uuid)

                            // Call linkCaptainDataToLinkedinAccount
                            linkCaptainDataToLinkedinAccount(
                              linkedinSettingUuid = linkedinSettingUuid,
                              liat_cookie = liat_cookie,
                              lia_cookie = data.li_a_cookie,
                              teamId = TeamId(id = teamId)
                            ).map(_ => {
                                releaseChannelLevelLimitLock(
                                  channelId = ChannelId(id = uuid),
                                  teamId = teamId
                                )

                                Right(linkedinSetting)
                              }
                              )
                              .recover {
                                case ex: Exception =>
                                  logger.error(s"Failed to link Captain Data to LinkedIn account: ${ex.getMessage}", ex)
                                  // Create a custom LinkedinValidationError for Captain Data failures
                                  Left(LinkedinValidationError.CaptainDataLinkingError(err = ex))
                              }

                          case _ =>

                            releaseChannelLevelLimitLock(
                              channelId = ChannelId(id = uuid),
                              teamId = teamId
                            )

                            Future.successful(Right(linkedinSetting))
                          // Conditions not met, just return the ID
                        }
                      }
                    
                  }
                }

            }

        }
      }
    }

  }

  def getSessionCookieAndUserAgentForLinkedinSetting(linkedinSettingUuid: String, teamId: TeamId): Try[LinkedinSessionCookieAndUserAgent] = {

    linkedinSettingDAO.getSessionCookieAndUserAgentForLinkedinSetting(
      linkedinSettingUuid = linkedinSettingUuid,
      teamId = teamId
    )
      .flatMap{
        case None => Failure(new Exception(ExecuteAutoLinkedinTasksError.LinkedinAccountNotSynced.toString))
        case Some(value) => Success(value)
      }
    
  }

  def updateContainerIdForInboxScraping(
                                         linkedinSettingUuid: String,
                                         containerId: PhantomBusterContainerId,
                                         teamId: TeamId
                                       ): Try[Int] = {

    linkedinSettingDAO.updateContainerIdForInboxScraping(
      linkedinSettingUuid = linkedinSettingUuid,
      teamId = teamId,
      containerId = containerId
    )

  }

  def fetchLinkedinAccountsToExecuteTasksOrScrapeMessages(): Try[List[LinkedinAccountAndLastInboxScrapeTime]] = {
    linkedinSettingDAO.fetchLinkedinAccountsToExecuteTasksOrScrapeMessages()
  }

  def updatePushedToQueueStatus(linkedinSettingId: LinkedinSettingId, teamId: TeamId): Try[Int] = {
    linkedinSettingDAO.updatePushedToQueueStatus(
      linkedinSettingId = linkedinSettingId,
      teamId = teamId
    )
  }

  def getProxyDetailsForLinkedinAccount(
                                         linkedinSettingUuid: LinkedinSettingUuid,
                                         teamId: TeamId
                                       ): Try[Option[ProxyIPAndCredentials]] = {

    linkedinSettingDAO.getProxyDetailsForLinkedinAccount(
      linkedinSettingUuid = linkedinSettingUuid,
      teamId = teamId
    )

  }

  def updateNextSessionSyncRetryToBeDoneAt(
                                            uuid: LinkedinSettingUuid,
                                            teamId: TeamId
                                          ): Try[Int] = {

    linkedinSettingDAO.getConsecutiveRecreateSessionAttemptsCount(
      uuid = uuid,
      teamId = teamId
    )
      .flatMap(retryCount => {

        val increaseInterval: Int = if (retryCount % AppConfig.Linkedin_Automation.maxLinkedinSessionCreateRetriesPerDay == 0) {
          60 * 24 // one day
        }
        else {
          (AppConfig.Linkedin_Automation.minDelayBetweenSessionCreateAttempsInMinutes * (retryCount - 1)) + srRandomUtils.getRandomDelay(2, 7)
        }

        linkedinSettingDAO.updateNextSessionSyncRetryToBeDoneAt(
          uuid = uuid,
          teamId = teamId,
          nextSessionSyncRetryToBeDoneAt = DateTime.now().plusMinutes(increaseInterval)
        )
      })

  }

  def getOrUpdateProxyIPAndCredentialsForLinkedinAccount(
                                                  linkedinSettingUuid: LinkedinSettingUuid,
                                                  teamId: TeamId
                                                )(using logger: SRLogger): Try[ProxyIPAndCredentials] = {

    getProxyDetailsForLinkedinAccount(
        linkedinSettingUuid = linkedinSettingUuid,
        teamId = teamId
      )
      .flatMap {
        case Some(proxyIPAndCredentials) => Success(proxyIPAndCredentials)

        case None =>
          for {
            orgId: OrgId <- planLimitService.checkLimitAccordingToAddonLicenseTypeUsingTeamId(
              teamId = teamId,
              addonLicenceType = AddonLicenceType.LinkedinAutomationSeats,
              isAddingNewTeam = false
            )

            country: String <- getLoginCountryOfLinkedinAccount(
              linkedinSettingUuid = linkedinSettingUuid,
              teamId = teamId
            )

            proxyDetails: ProxyDetails <- phantomBusterProxyService.fetchRandomProxyForACountry(
              country = country
            )

            _: Int <- updateProxyId(
              linkedinSettingUuid = linkedinSettingUuid,
              teamId = teamId,
              proxyId = proxyDetails.id
            )
          } yield {
            proxyDetails.proxyIPAndCredentials
          }

      }

  }

  def updateUserAgentAndAssignProxy(
                                     linkedinAccountAndUserAgent: LinkedinAccountAndUserAgent
                                   )(implicit ec: ExecutionContext,
                                     logger: SRLogger): Future[ProxyIPAndCredentials] = {

    for {
      _: Int <- Future.fromTry {
        linkedinSettingDAO.updateUserAgent(
          linkedinAccountAndUserAgent = linkedinAccountAndUserAgent
        )
      }

      proxyIPAndCredentials: ProxyIPAndCredentials <- Future.fromTry {
        getOrUpdateProxyIPAndCredentialsForLinkedinAccount(
          linkedinSettingUuid = linkedinAccountAndUserAgent.uuid,
          teamId = linkedinAccountAndUserAgent.teamId
        )
      }

      currentTime: DateTime <- Future(DateTime.now())

      encryptedPassword: String <- EncryptionService.encryptWithCryptoJs(
        encryption_secret_key = s"${linkedinAccountAndUserAgent.uuid.uuid}_${currentTime.getDayOfMonth}-${currentTime.getMonthOfYear}-${currentTime.getYearOfEra}",
        text_to_be_encrypted = proxyIPAndCredentials.password.get
      )
      
    } yield {
      ProxyIPAndCredentials(
        ip_address = proxyIPAndCredentials.ip_address,
        username = proxyIPAndCredentials.username,
        password = Some(encryptedPassword)
      )
    }

  }

  def getLoginCountryOfLinkedinAccount(
                                        linkedinSettingUuid: LinkedinSettingUuid,
                                        teamId: TeamId
                                      ): Try[String] = {

    linkedinSettingDAO.getLoginCountryOfLinkedinAccount(
      linkedinSettingUuid = linkedinSettingUuid,
      teamId = teamId
    )
      .flatMap {
        case None =>
          Failure(new Exception("Login Country not specified for Linkedin Account."))

        case Some(country) => Success(country)
      }

  }

  def updateProxyId(
                     linkedinSettingUuid: LinkedinSettingUuid,
                     teamId: TeamId,
                     proxyId: Long
                   )(using logger: SRLogger): Try[Int] = {
    
    for {
      updateCount: Int <- linkedinSettingDAO.updateProxyId(
        linkedinSettingUuid = linkedinSettingUuid,
        teamId = teamId,
        proxyId = proxyId
      )

      _: Boolean <- organizationService.resetOrgDataFromCacheUsingTeamId(
        teamId = teamId,
        resetCacheInterval = SrResetCacheInterval.Immediately
      )
    } yield {
      updateCount
    }

  }

  def updateLastInboxScrapeStatus(
                                   containerId: PhantomBusterContainerId,
                                   teamId: TeamId,
                                   status: PhantomBusterWebhookStatus
                                 ): Try[Int] = {

    linkedinSettingDAO.updateLastInboxScrapeStatus(
      containerId = containerId,
      teamId = teamId,
      status = status
    )

  }
  
  def getLinkedinAccountCredentials(uuid: LinkedinSettingUuid, teamId: TeamId): Try[LinkedinAccountCredentialsAndUserAgent] = {
    
    linkedinSettingDAO.getLinkedinAccountCredentialsAndUserAgent(
      uuid = uuid,
      teamId = teamId
    ).flatMap {
      case None =>
        Failure(new Exception("Linkedin Account not found."))

      case Some(credentials) =>
        Success(credentials)
    }
    
  }
  
  def changeStatus(uuid: LinkedinSettingUuid, teamId: TeamId, status: LinkedinAccountStatus): Try[Int] = {
    
    linkedinSettingDAO.changeStatus(
      uuid = uuid,
      teamId = teamId,
      status = status
    )
    
  }

  def getLinkedinSettingUuidFromContainerId(
                                             containerId: PhantomBusterContainerId,
                                             teamId: TeamId
                                           ): Try[LinkedinSettingUuid] = {

    linkedinSettingDAO.getLinkedinSettingUuidFromContainerId(
        containerId = containerId,
        teamId = teamId
      )
      .flatMap {
        case None =>
          Failure(new Exception(s"No Linkedin account has an inbox scraper running with container_id: ${containerId.id}"))

        case Some(uuid) =>
          Success(uuid)
      }

  }

  def fetchAndUpdateStatusOfLinkedinAccountsToRecreateSession(): Try[List[LinkedinAccountUuidAndTeamId]] = {

    linkedinSettingDAO.fetchLinkedinAccountsToRecreateSession()
      .flatMap(accountList => {
        Try {
          accountList.map(uuidAndTeamId => {
            linkedinSettingDAO.changeStatus(
                uuid = uuidAndTeamId.uuid,
                teamId = uuidAndTeamId.teamId,
                status = LinkedinAccountStatus.PushedToQueueForRecreatingSession
              )
              .get
          })

          accountList
        }
      })

  }

  def getLinkedinSettingIdFromContainerId(
                                           containerId: PhantomBusterContainerId,
                                           teamId: TeamId
                                         ): Try[Long] = {

    linkedinSettingDAO.getLinkedinSettingIdFromContainerId(
      containerId = containerId,
      teamId = teamId
    )
      .flatMap {
        case None => Failure(new Exception(s"No linkedin_setting_id found with inbox_scraping_container_id: ${containerId.id} :: team_id: ${teamId.id}"))

        case Some(id) => Success(id)
      }

  }
  
  def updateInboxScraperContainerStatus(linkedinSettingId: Long, teamId: TeamId): Try[Int] = {
    linkedinSettingDAO.updateInboxScraperContainerStatus(
      linkedinSettingId = linkedinSettingId,
      teamId = teamId
    )
  }
  
  def saveLinkedinMessages(
                            messageThreadId: Long,
                            teamId: TeamId,
                            messages: List[PhantomBusterLinkedinMessage]
                          ): Try[Int] = {

    linkedinSettingDAO.saveLinkedinMessages(
      messageThreadId = messageThreadId,
      teamId = teamId,
      messages = messages
    )

  }
  
  def deleteCaptainDataWorkflows( 
                                  uuid: String,
                                  id: LinkedinSettingId
                                )
                                (implicit logger: SRLogger,
                                 WSClient: WSClient, ec: ExecutionContext): Future[Boolean]={
    val extractLinkedinConnectionJobName = CaptainDataWorkflowName.EXTRACT_LINKEDIN_CONNECTIONS.toString + " - " + uuid
    val extractLinkedinThreadJobName = CaptainDataWorkflowName.EXTRACT_LINKEDIN_CONVERSATIONS.toString + " - " + id.toString


    for {

      _: Boolean <- captainDataAPI.deleteJobSchedule(job_name = extractLinkedinConnectionJobName)

      _: Boolean <- captainDataAPI.deleteJobSchedule(job_name = extractLinkedinThreadJobName)

    } yield {

      true

    }
  
  }

  def setLinkedinAccountAsInactive(
                                    uuid: LinkedinSettingUuid,
                                    team_id: TeamId
                                  )(using logger: SRLogger,
                                    WSClient: WSClient, ec: ExecutionContext): Future[Int] = {
    Future.fromTry(
      linkedinSettingDAO.setLinkedinAccountToInactive(uuid = uuid, team_id = team_id)
    )
  }


  def deleteLinkedinAccount(
                             uuid: String,
                             team_id: Long
                           )(using logger: SRLogger,
                             WSClient: WSClient, ec: ExecutionContext): Future[Int] = {
    Future.fromTry(
      linkedinSettingDAO.getLinkedinDependenciesToRemove(uuid = uuid, team_id = team_id)
    ).flatMap { dependencies =>
      for {
        _: Int <- Future.fromTry(
          if (dependencies.phantomBusterProxyId.isDefined) {
            phantomBusterProxyService.decreaseNumberOfAccountsUsingForProxy(
              id = dependencies.phantomBusterProxyId.get,
              teamId = TeamId(team_id)
            )
          } else Success(1)
        )
        
        
        _: Boolean <- if(dependencies.captainDataUserUID.isDefined){
          deleteCaptainDataWorkflows(uuid = uuid, id = dependencies.linkedinSettingId)
        }else{
          Future.successful(true)
        }
        
        _: Boolean <- if (dependencies.captainDataAccountUID.isDefined) {
          logger.info(s"Deleting captain data account: ${dependencies.captainDataAccountUID.get} for team id: ${team_id} ")
          captainDataAPI.deleteAccount(accountUid = dependencies.captainDataAccountUID.get)
        }else Future.successful(true)

        _: Boolean <- if (dependencies.captainDataUserUID.isDefined) {
          logger.info(s"Deleting captain data user: ${dependencies.captainDataUserUID.get} for team id: ${team_id} ")
          captainDataAPI.deleteUser(userUid = dependencies.captainDataUserUID.get)
        } else Future.successful(true)

        _: Int <- Future.fromTry(
          linkedinSettingDAO.markLinkedinAccountAsDeleted(uuid = uuid, team_id = team_id)
        )

      } yield 1
    }
  }

  // TODO: Pass Logger here and log in case of Failure
  def findLinkedinAccount(id: Option[Long], uuid: Option[String], teamId: TeamId): Option[LinkedinAccountSettings] = {
    linkedinSettingDAO.findLinkedinAccountByIdOrUuid(id = id, uuid = uuid, teamId = teamId) match {
      case Failure(err) => None
      case Success(data) => data
    }
  }
  
  def findLinkedinSettingIdFromUuidAndTeamId(uuid: String, teamId: TeamId): Try[Long] = {
    linkedinSettingDAO.findLinkedinSettingIdFromUuidAndTeamId(uuid, teamId)
  }


  private def handleSendLinkedinConnection(
                                            accountUid: CaptainDataAccountUID,
                                            linkedinProfileUrl: String,
                                            messageBody: Option[String],
                                            taskId: String,
                                            accountId: AccountId,
                                            teamId: TeamId,
                                            linkedinSettingUuid: LinkedinSettingUuid,
                                            orgId: OrgId
                                          )(implicit ws: WSClient,
                                            ec: ExecutionContext,
                                            logger: SRLogger): Future[CaptainDataJobUID] = {

    for {
      connectedJobUid: Option[CaptainDataJobUID] <- Future.fromTry(linkedinConnectionsDAO.findExistingConnectionJobUid(
        linkedinProfileUrl = linkedinProfileUrl,
        linkedinSettingUuid = linkedinSettingUuid,
        teamId = teamId
      ))
      result: CaptainDataJobUID <- if (connectedJobUid.isDefined) {
        logger.info(
          s"""Skipping LinkedIn connection request as profile is already connected:
             |Captain Account UID: ${accountUid}
             |Profile URL: $linkedinProfileUrl
             |Task ID: $taskId
             |Team ID: ${teamId.id}""".stripMargin
        )
        // Update the task status to indicate profile is already connected
        updateTaskStatusForAlreadyConnected(
          taskId = taskId,
          accountId = accountId,
          teamId = teamId,
          orgId = orgId).map(_ => connectedJobUid.get)
      } else {
        sendLinkedinConnectionRequest(
          accountUid = accountUid,
          linkedinProfileUrl = linkedinProfileUrl,
          messageBody = messageBody,
          taskId = taskId,
          teamId = teamId)
      }
    } yield result
  }

  def sendLinkedinConnectionRequest(
                                    accountUid: CaptainDataAccountUID,
                                    linkedinProfileUrl: String,
                                    messageBody: Option[String],
                                    taskId: String,
                                    teamId: TeamId
                                  )(implicit ws: WSClient,
                                     ec: ExecutionContext,
                                     logger: SRLogger): Future[CaptainDataJobUID] = {

    for {
      jobUid: CaptainDataJobUID <- captainDataAPI.sendLinkedInConnection(
        accountUid = accountUid,
        linkedinProfileUrl = linkedinProfileUrl,
        messageOpt = messageBody
      ).recoverWith { case err: Exception =>
        logger.error(
          s"""Failed to send LinkedIn connection request:
             |Captain Account UID: ${accountUid}
             |Profile URL: $linkedinProfileUrl
             |Task ID: $taskId
             |Team ID: ${teamId.id}""".stripMargin,
          err = err
        )
        Future.failed(err)
      }

      _:Int <- taskService.updateCaptainDataTaskStatusToInitiate(
        taskId = taskId,
        jobUid = jobUid,
        teamId = teamId
      ).recoverWith { case err: Exception =>
        logger.error(
          s"""Failed to update task status:
             |Task ID: $taskId
             |Job UID: ${jobUid}
             |Team ID: ${teamId.id}""".stripMargin,
          err = err
        )
        Future.failed(err)
      }

    } yield {
      jobUid
    }

  }


  private def updateTaskStatusForAlreadyConnected(
                                                   taskId: String,
                                                   accountId: AccountId,
                                                   teamId: TeamId,
                                                   orgId: OrgId
                                                 )(implicit ec: ExecutionContext,
                                                   logger: SRLogger): Future[Unit] = {
    taskService.changeStatus(
      task_id = taskId,
      task_status = UpdateTaskStatus.Done(),
      orgId = orgId,
      changeStatusPermissionCheck = ChangeStatusPermissionCheck.NoStatusPermissionCheck(
        accountId = accountId
      ),
      team_id = teamId.id,
    ).map {
        case Left(ChangeStatusTypeError.ErrorWhileChangingStatus) =>
          logger.shouldNeverHappen(s"Error while changing Status for task_id :: ${taskId} :: team_id :: ${teamId.id}")

        case Right(task_id) =>
          logger.info(s"Task Status updated to success successfully for task_id :: ${taskId} :: team_id :: ${teamId.id} when connection was already made ")
      }

  }

  def handleViewLinkedinProfile(
                                            accountUid: CaptainDataAccountUID,
                                            linkedinProfileUrl: String,
                                            taskId: String,
                                            teamId: TeamId
                                          )(implicit ws: WSClient,
                                            ec: ExecutionContext,
                                            logger: SRLogger): Future[CaptainDataJobUID] = {

    for {
      jobUid: CaptainDataJobUID <- captainDataAPI.viewLinkedInProfile(
        accountUid = accountUid,
        linkedinProfileUrl = linkedinProfileUrl
      ).recoverWith { case err: Exception =>
        logger.error(
          s"""Failed to send LinkedIn connection request:
             |Captain Account UID: ${accountUid}
             |Profile URL: $linkedinProfileUrl
             |Task ID: $taskId
             |Team ID: ${teamId.id}""".stripMargin,
          err = err
        )
        Future.failed(err)
      }

      _: Int <- taskService.updateCaptainDataTaskStatusToInitiate(
        taskId = taskId,
        jobUid = jobUid,
        teamId = teamId
      ).recoverWith { case err: Exception =>
        logger.error(
          s"""Failed to update task status:
             |Task ID: $taskId
             |Job UID: ${jobUid}
             |Team ID: ${teamId.id}""".stripMargin,
          err = err
        )
        Future.failed(err)
      }

    } yield {
      jobUid
    }

  }


  def handleSendLinkedinMessage(
                                            accountUid: CaptainDataAccountUID,
                                            linkedinProfileUrl: String,
                                            message: String,
                                            taskId: String,
                                            teamId: TeamId
                                          ) (implicit ws: WSClient,
                                             ec: ExecutionContext,
                                             logger: SRLogger): Future[CaptainDataJobUID] = {

    for {
      jobUid: CaptainDataJobUID <- captainDataAPI.sendLinkedInMessage(
        accountUid =  accountUid,
        linkedinProfileUrl =  linkedinProfileUrl,
        message = message
      ).recoverWith { case err: Exception =>
        logger.error(
          s"""Failed to send LinkedIn connection request:
             |Captain Account UID: ${accountUid}
             |Profile URL: $linkedinProfileUrl
             |Task ID: $taskId
             |Team ID: ${teamId.id}""".stripMargin,
          err = err
        )
        Future.failed(err)
      }

      _:Int <- taskService.updateCaptainDataTaskStatusToInitiate(
        taskId = taskId,
        jobUid = jobUid,
        teamId = teamId
      ).recoverWith { case err: Exception =>
        logger.error(
          s"""Failed to update task status:
             |Task ID: $taskId
             |Job UID: ${jobUid}
             |Team ID: ${teamId.id}""".stripMargin,
          err = err
        )
        Future.failed(err)
      }

    } yield {
      jobUid
    }

  }


  def handleCaptainDataTask(
                              tasksGroupedByTypeAndLinkedinSetting: TasksGroupedByTypeAndLinkedinSetting
                            )(implicit ws: WSClient,
                              ec: ExecutionContext,
                              logger: SRLogger): Future[Boolean] = {
    for {

      linkedinSettingOpt: Option[LinkedinAccountSettings] <- Future.fromTry(linkedinSettingDAO.findLinkedinAccountByIdOrUuid(
        id = None,
        uuid = Some(tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails.linkedinSettingUuid),
        teamId = tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails.teamId
      ))

      linkedinAccount: LinkedinAccountSettings <- linkedinSettingOpt match {
        case None =>
          logger.error(s"Linkedin account not found for uuid: ${tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails.linkedinSettingUuid}")
          Future.failed(new Exception("Linkedin account not found."))
        case Some(linkedinAccount) => Future.successful(linkedinAccount)
      }

      tasks: Seq[Task] <- Future.sequence(tasksGroupedByTypeAndLinkedinSetting.taskIds.map(
        taskId => taskService.findDueTaskbyTaskIDAndTeamId(
          taskId = taskId,
          teamId = tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails.teamId,
          orgId = tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails.orgId
        ))
      )

      result: Seq[CaptainDataJobUID] <- Future.sequence(tasks.map(task => {
        (task.prospect.flatMap(_.linkedin_url), task.task_data) match {
          case (Some(linkedinUrl), data: TaskData.AutoLinkedinConnectionRequest) =>
            logger.info(s"Sending LinkedIn connection request to profile: $linkedinUrl")
            handleSendLinkedinConnection(
              accountUid = CaptainDataAccountUID(uid = linkedinAccount.captain_data_account_id.get),
              accountId = AccountId(linkedinAccount.owner_id),
              linkedinProfileUrl = linkedinUrl,
              orgId = tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails.orgId,
              linkedinSettingUuid = LinkedinSettingUuid(uuid = tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails.linkedinSettingUuid),
              messageBody = data.body,
              taskId = task.task_id,
              teamId = tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails.teamId
            ).recoverWith { case error =>
              logger.error(s"Failed to send LinkedIn connection request to $linkedinUrl", error)
              Future.failed(error)
            }

          case (Some(linkedinUrl), _: TaskData.AutoViewLinkedinProfile) =>
            logger.info(s"Viewing LinkedIn profile: $linkedinUrl")
            handleViewLinkedinProfile(
              accountUid = CaptainDataAccountUID(uid = linkedinAccount.captain_data_account_id.get),
              linkedinProfileUrl = linkedinUrl,
              taskId = task.task_id,
              teamId = tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails.teamId
            ).recoverWith { case error =>
              logger.error(s"Failed to view LinkedIn profile $linkedinUrl", error)
              Future.failed(error)
            }

          case (Some(linkedinUrl), data: TaskData.AutoLinkedinMessage) =>
            logger.info(s"Sending LinkedIn message to profile: $linkedinUrl")
            handleSendLinkedinMessage(
              accountUid = CaptainDataAccountUID(uid = linkedinAccount.captain_data_account_id.get),
              linkedinProfileUrl = linkedinUrl,
              message = data.body,
              taskId = task.task_id,
              teamId = tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails.teamId
            ).recoverWith { case error =>
              logger.error(s"Failed to send LinkedIn message to $linkedinUrl", error)
              Future.failed(error)
            }

          case (None, _) =>
            val errorMsg = s"LinkedIn URL not found for prospect ID: ${task.prospect.map(_.id).getOrElse("unknown")}"
            logger.error(errorMsg)
            Future.failed(new Exception(errorMsg))

          case (_, actualData) =>
            val errorMsg = s"Invalid task data type for task with id: ${task.task_id}. Got: ${actualData.task_type.toKey}"
            logger.error(errorMsg)
            Future.failed(new Exception(errorMsg))
        }
      }))


    } yield {
      true
    }
  }

  def validateAndHandleCaptainDataTask(
                             tasksGroupedByTypeAndLinkedinSetting: TasksGroupedByTypeAndLinkedinSetting
                           )(implicit ws: WSClient,
                             ec: ExecutionContext,
                             logger: SRLogger): Future[Boolean] = {
    val taskType = tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails.taskType

    taskType match {
      case TaskType.AutoLinkedinMessage | TaskType.AutoLinkedinConnectionRequest |TaskType.AutoViewLinkedinProfile =>
        handleCaptainDataTask(tasksGroupedByTypeAndLinkedinSetting)

      case _ =>
        logger.error(s"${taskType.toKey} is not a Linkedin action type. taskIds: ${tasksGroupedByTypeAndLinkedinSetting.taskIds}")
        Future.failed(new Exception(s"${taskType.toKey} automation not supported."))

    }

  }


  def linkCaptainDataToLinkedinAccount(
                                             linkedinSettingUuid: LinkedinSettingUuid,
                                             liat_cookie: String,
                                             lia_cookie: Option[String],
                                             teamId: TeamId
                                           )(using logger: SRLogger, WSClient: WSClient, ec: ExecutionContext): Future[CaptainDataAccountLinkedinUrl] = {

    linkedinSettingDAO.findLinkedinAccountByIdOrUuid(
      uuid = Some(linkedinSettingUuid.uuid),
      id = None,
      teamId = teamId
    ) match {
      case Failure(exception) =>
        val errMsg = s"Error while finding linkedin account with uuid: ${linkedinSettingUuid.uuid} :: error: ${exception.getMessage}"
        logger.error(errMsg, err = exception)
        Future.failed(exception)

      case Success(None) =>
        val errMsg = s"unable to find linkedin account with uuid: ${linkedinSettingUuid.uuid}"
        logger.error(errMsg)
        Future.failed(Exception(errMsg))

      case Success(Some(linkedinAccount)) =>
        if (linkedinAccount.country.isEmpty){
          Future.failed(Exception("Linkedin account country is not specified."))
        } else if ( linkedinAccount.captain_data_user_id.isDefined && linkedinAccount.captain_data_account_id.isDefined){
          Future.failed(Exception("Linkedin account captain_data_user_id and captain_data_account_id are already specified."))
        } else {
          val name = s"${linkedinAccount.first_name} ${linkedinAccount.last_name}"
          val country = linkedinAccount.country.get

          for {

            account: Account <- Future.fromTry(accountService.find(id = linkedinAccount.owner_id))

            iso_country_code:String = TimezoneUtils.getISO2CountryCodeFromCountryName(country).get

            timeZone: String <- Future.fromTry(LinkedinSettingService.getVaildTimeZoneForLinkedinSetting(
              iso_country_code = iso_country_code,
              account_timezone = account.timezone
            ))

            userId: CaptainDataUserUID <-if (linkedinAccount.captain_data_user_id.isDefined){
              Future(CaptainDataUserUID(uid=linkedinAccount.captain_data_user_id.get))
            } else {
              captainDataAPI.createUser(
              request = CaptainDataUserCreateRequest(
                email = linkedinAccount.email,
                fullName = name,
                country = iso_country_code,
                timezone =timeZone
              )
            )}

            _: Int <- if (linkedinAccount.captain_data_user_id.isDefined){
                Future(1)
              } else {
              Future.fromTry(linkedinSettingDAO.updateLinkedinCaptainDataUserDetails(
                captainDataUserUID = userId,
                linkedinAccountUuid = linkedinSettingUuid,
                teamId = teamId
              ))
            }

            accountDetails: CaptainDataAccountDetails <- captainDataAPI.addLinkedInAccount(
              name = name,
              liAtCookie =  liat_cookie,
              liACookie = lia_cookie,
              userUid = userId
            )

            linkedinAccountIdAndOwner: LinkedinSettingIdAndOwner <- Future.fromTry(
              linkedinSettingDAO.findLinkedinSettingIdAndOwnerFromUuidAndTeamId(
                uuid = linkedinSettingUuid.uuid,
                teamId = teamId)
            ).recoverWith { case e =>
              logger.shouldNeverHappen(s"Failed to find LinkedIn setting ID and owner for uuid: ${linkedinSettingUuid.uuid}, teamId: ${teamId.id}", err = Some(e))
              Future.failed(e)
            }

            initiating_li_conversations_workflow: CaptainDataJobUID <- {
              captainDataAPI.retrieveLinkedInConversations(
                accountUid = accountDetails.accountUID,
                linkedinSettingId = linkedinAccountIdAndOwner.id,
                accountId = linkedinAccountIdAndOwner.ownerAccountId,
                repetitiveMinutes = AppConfig.CaptainData.RetrieveLinkedinConversations.repetitiveIntervalInMinutes,
                fromTimeInMinutes = AppConfig.CaptainData.RetrieveLinkedinConversations.extractionIntervalInMinutes,
                teamId = teamId
              ).recoverWith { case e =>
                logger.shouldNeverHappen(s"Failed to initiate LinkedIn conversations workflow for accountUid: ${accountDetails.accountUID}, linkedinSettingId: ${linkedinAccountIdAndOwner.id}", err = Some(e))
                Future.failed(e)
              }
            }

            extract_li_connections_workflow: CaptainDataJobUID <- {
              captainDataAPI.extractLinkedInConnections(
                accountUid = accountDetails.accountUID,
                team_id = teamId,
                linkedinSettingUuid = linkedinSettingUuid,
                extract_all = true
              ).map(job_uid => {

                  logger.info(s"Initial linkedin connection sync for linkedinSettingUuid:: ${linkedinSettingUuid.uuid} initiated with job_uid: ${job_uid.toString} team_id:: ${teamId.id} cp_account_uid:: ${accountDetails.accountUID.toString}")

                job_uid
              }).recoverWith(e => {
                logger.shouldNeverHappen(s"error creating initial connections sync workflow:: linkedinSettingUuid:: ${linkedinSettingUuid.uuid} team_id:: ${teamId.id} cp_account_uid:: ${accountDetails.accountUID.toString}")
                Future.failed(e)
              }
              )
            }

            colsUpdated <- Future.fromTry(linkedinSettingDAO.updateLinkedinCaptainDataAccountDetails(
              captainDataAccountUID = accountDetails.accountUID,
              linkedinAccountUuid = linkedinSettingUuid,
              linkedinProfileUrl = accountDetails.linkedinProfileUrl.toString,
              teamId = teamId,
              initial_sync_li_connection_job_uid = Some(extract_li_connections_workflow.toString)
            ))

          } yield {
            accountDetails.linkedinProfileUrl
          }
        }
    }
  }

  def updateCaptainDataCookie(
                               linkedinSettingUuid: LinkedinSettingUuid,
                               liat_cookie: String,
                               lia_cookie: Option[String],
                               teamId: TeamId
                             )(using logger: SRLogger, WSClient: WSClient, ec: ExecutionContext): Future[Int] = {

    Future.fromTry(
      linkedinSettingDAO.findLinkedinAccountByIdOrUuid(
        uuid = Some(linkedinSettingUuid.uuid),
        id = None,
        teamId = teamId
      )
    ).flatMap {
      case Some(linkedinAccount) =>
        if (linkedinAccount.captain_data_user_id.isEmpty){
          Future.failed(Exception("Linkedin account captain data user id is not specified."))
        } else if (linkedinAccount.captain_data_account_id.isEmpty){
          Future.failed(Exception("Linkedin account data account id is not specified."))
        }

        for {
          _: CaptainDataAccountUID <- captainDataAPI.updateLinkedInAccount(
              name = s"${linkedinAccount.first_name} ${linkedinAccount.last_name}",
              liAtCookie = liat_cookie,
              liACookie = lia_cookie,
              userUid = CaptainDataUserUID(uid = linkedinAccount.captain_data_user_id.get),
              accountUid = CaptainDataAccountUID(uid = linkedinAccount.captain_data_account_id.get)
            )

          updateCount: Int <- Future.fromTry(linkedinSettingDAO.updateLinkedinAccountStatus(
            uuid = linkedinSettingUuid,
            teamId = teamId,
            status = LinkedinAccountStatus.Active
          ))
        } yield {
          updateCount
        }

      case None =>
        Future.failed(Exception(s"Unable to find LinkedIn account with uuid: ${linkedinSettingUuid.uuid}"))
    }.recoverWith { case e =>
      val errMsg = s"Error while finding LinkedIn account with uuid: ${linkedinSettingUuid.uuid} :: error: ${e.getMessage}"
      logger.error(errMsg, err = e)
      Future.failed(e)
    }
  }


  def updateCookieToLinkedinAccountsOfOwner(
                                             ownerAccountId: AccountId,
                                             cookie: LinkedinCookie
                                           )(using logger: SRLogger, ec: ExecutionContext, ws: WSClient): Future[Boolean] = {
    for {

      settings: List[LinkedinAccountSettings] <- Future.fromTry(linkedinSettingDAO.getAutomatedLinkedinSettingsByOwnerId(
        ownerAccountId = ownerAccountId
      ))

      _: Seq[Boolean] <- Future.sequence(settings.map(setting =>
        captainDataAPI.updateLinkedInAccount(
          name = s"${setting.first_name} ${setting.last_name}",
          liAtCookie = cookie.li_at,
          liACookie = cookie.li_a,
          userUid = CaptainDataUserUID(uid = setting.captain_data_user_id.get),
          accountUid = CaptainDataAccountUID(uid = setting.captain_data_account_id.get)
        ).map { (_: CaptainDataAccountUID) => {
          logger.info(s"Successfully updated LinkedIn cookie for account: ${setting.uuid}, user: ${setting.first_name} ${setting.last_name}")

          // Update LinkedIn account status to Active after successful cookie update
          linkedinSettingDAO.updateLinkedinAccountStatus(
            uuid = LinkedinSettingUuid(setting.uuid),
            teamId = TeamId(setting.team_id),
            status = LinkedinAccountStatus.Active
          ) match {
            case Success(_) =>
              logger.info(s"Updated LinkedIn account status to Active for account: ${setting.uuid}")
              true
            case Failure(e) =>
              logger.error(s"Failed to update LinkedIn account status for account: ${setting.uuid}", err = e)
              false
          }

        }
        }.recover { case e =>
          logger.error(s"Failed to update LinkedIn cookie for account: ${setting.uuid}, user: ${setting.first_name} ${setting.last_name}", err = e)
          false
        }
      ))


    } yield {
      true
    }
  }

  // This function is only for test app: for migration
  def normalizeLinkedinProfileUrls(teamId: Option[TeamId] = None, batchSize: Int = 600)
                                (implicit Logger: SRLogger, ec: ExecutionContext): Future[Int] = {
    Future {
      var totalUpdated = 0
      var lastProcessedId = 0L
      var hasMoreRecords = true

      while (hasMoreRecords) {
        // Get a batch of records with their IDs
        val recordsWithIds = linkedinSettingDAO.fetchLinkedinAccountBatchWithIds(
          lastProcessedId = lastProcessedId,
          batchSize = batchSize,
          teamId = teamId
        ).getOrElse(List.empty)

        if (recordsWithIds.isEmpty) {
          hasMoreRecords = false
        } else {
          // Process each record in the batch
          recordsWithIds.foreach { case (id, account) =>
            if (account.profile_url != null && account.profile_url.nonEmpty) {
              val normalizedUrlOpt = LinkedinHelperFunctions.normalizeLinkedInURL(Some(account.profile_url))
              
              normalizedUrlOpt match {
                case Some(normalizedUrl) if normalizedUrl != account.profile_url =>
                  // Only update if the URL has changed
                  linkedinSettingDAO.updateLinkedinProfileUrl(
                    id = id,
                    uuid = account.uuid,
                    teamId = TeamId(account.team_id),
                    normalizedUrl = normalizedUrl
                  ) match {
                    case Success(updated) => 
                      totalUpdated += updated
                      Logger.info(s"Normalized LinkedIn profile URL for account ${account.uuid}: ${account.profile_url} -> $normalizedUrl")
                    case Failure(e) =>
                      Logger.error(s"Failed to update LinkedIn profile URL for account ${account.uuid}: ${e.getMessage}", e)
                  }
                case _ => // URL is already normalized or normalization failed, skip
              }
            }
            lastProcessedId = id
          }
        }
      }
      
      totalUpdated
    }
  }

}

object LinkedinSettingService {

  val viewProfileMinLimit = AppConfig.LinkedinLimits.viewProfileMinLimit
  val viewProfileMaxLimit = AppConfig.LinkedinLimits.viewProfileMaxLimit
  
  val inmailsMinLimit = AppConfig.LinkedinLimits.inmailsMinLimit
  val inmailsMaxLimit = AppConfig.LinkedinLimits.inmailsMaxLimit
  
  val messagesMinLimit = AppConfig.LinkedinLimits.messagesMinLimit
  val messagesMaxLimit = AppConfig.LinkedinLimits.messagesMaxLimit
  
  val connectionRequestsMinLimit = AppConfig.LinkedinLimits.connectionRequestsMinLimit
  val connectionRequestsMaxLimit = AppConfig.LinkedinLimits.connectionRequestsMaxLimit

  def validateLimit(accountSettings: CreateOrUpdateLinkedinAccountSettings): Option[LinkedinValidationError] = {
    
    if (viewProfileMinLimit > accountSettings.view_profile_limit_per_day || accountSettings.view_profile_limit_per_day > viewProfileMaxLimit) {
      Some(LinkedinValidationError.ViewProfileLimitError(s"View Profile Limit should be between $viewProfileMinLimit and $viewProfileMaxLimit"))
    }
      
    else if (connectionRequestsMinLimit > accountSettings.connection_request_limit_per_day || accountSettings.connection_request_limit_per_day > connectionRequestsMaxLimit) {
      Some(LinkedinValidationError.ConnectionRequestLimitError(s"Connection Request Limit should be between $connectionRequestsMinLimit and $connectionRequestsMaxLimit"))
    }
      
    else if (inmailsMinLimit > accountSettings.inmail_limit_per_day || accountSettings.inmail_limit_per_day > inmailsMaxLimit) {
      Some(LinkedinValidationError.InmailLimitError(s"Inmail Limit should be between $inmailsMinLimit and $inmailsMaxLimit"))
    }
      
    else if (messagesMinLimit > accountSettings.message_limit_per_day || accountSettings.message_limit_per_day > messagesMaxLimit) {
      Some(LinkedinValidationError.MessageLimitError(s"Message Limit should be between $messagesMinLimit and $messagesMaxLimit"))
    }
      
    else None
  }

  def getVaildTimeZoneForLinkedinSetting(
                   iso_country_code: String,
                   account_timezone: Option[String]
                 )(implicit logger: SRLogger): Try[String] = {
    val timeZone: Option[String] = TimezoneUtils.getTimezoneForCountry(Some(iso_country_code))

    timeZone match {
      case Some(tz) =>
        Success(tz)

      case None =>
        account_timezone match {
          case None =>
            logger.error(s"Timezone for country: $iso_country_code is not valid. account timezone is also not set.")
            Failure(new Exception(s"Timezone for country: $iso_country_code is not valid."))

          case Some(accountTz) =>
            if (TimezoneUtils.isCountryAndTimezoneValid(
              countryCode = iso_country_code,
              timezone = accountTz)) {
              Success(accountTz)
            } else {
              logger.error(s"Timezone for country: $iso_country_code is not valid. account timezone: $accountTz is also not validly aligns with country.")
              Failure(new Exception(s"Timezone for country: $iso_country_code is not valid. account timezone: $accountTz is also not validly aligns with country."))
            }
        }
    }
  }


}
