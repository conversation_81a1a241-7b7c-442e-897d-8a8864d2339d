package api.linkedin.models

import scala.util.Try

sealed trait LinkedinAccountStatus

object LinkedinAccountStatus {
  private val active = "active"
  private val needToAssignProxy = "need_to_assign_proxy"
  private val needToRecreateSession = "need_to_recreate_session"
  private val pushedToQueueForRecreatingSession = "pushed_to_queue_for_recreating_session"
  private val reAssignCookie = "re_assign_cookie"

  case object Active extends LinkedinAccountStatus {
    override def toString: String = active
  }
  
  case object NeedToAssignProxy extends LinkedinAccountStatus {
    override def toString: String = needToAssignProxy
  }

  case object NeedToRecreateSession extends LinkedinAccountStatus {
    override def toString: String = needToRecreateSession
  }

  case object PushedToQueueForRecreatingSession extends LinkedinAccountStatus {
    override def toString: String = pushedToQueueForRecreatingSession
  }

  def fromString(key: String): Try[LinkedinAccountStatus] = Try {
    key match {
      case `active` => Active
      case `needToAssignProxy` => NeedToAssignProxy
      case `needToRecreateSession` => NeedToRecreateSession
      case `pushedToQueueForRecreatingSession` => PushedToQueueForRecreatingSession
    }
  }
}
