package api.linkedin.models

import play.api.libs.json.{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>sS<PERSON>, JsSuc<PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>s, Writes}

import scala.util.{Failure, Success, Try}

sealed trait CaptainDataWorkflowName {
  def toString: String
}

object CaptainDataWorkflowName {

  private val connect_linkedin_profile = "Connect LinkedIn Profile"
  private val message_linkedin_profile = "Message LinkedIn Profile"
  private val visit_linkedin_profile = "Visit LinkedIn Profile"
  private val extract_linkedin_conversations = "Extract LinkedIn Conversations"
  private val extract_linkedin_messages = "Extract LinkedIn Messages"

  private val  extract_linkedin_connections = "Extract LinkedIn Connections"

  case object CONNECT_LINKEDIN_PROFILE extends CaptainDataWorkflowName {
    override def toString: String = connect_linkedin_profile
  }

  case object MESSAGE_LINKEDIN_PROFILE extends CaptainDataWorkflowName {
    override def toString: String = message_linkedin_profile
  }

  case object VISIT_LINKEDIN_PROFILE extends CaptainDataWorkflowName {
    override def toString: String = visit_linkedin_profile
  }

  case object EXTRACT_LINKEDIN_CONNECTIONS extends CaptainDataWorkflowName {
    override def toString: String = extract_linkedin_connections
  }

  case object EXTRACT_LINKEDIN_CONVERSATIONS extends CaptainDataWorkflowName {
    override def toString: String = extract_linkedin_conversations
  }

  case object EXTRACT_LINKEDIN_MESSAGES extends CaptainDataWorkflowName {
    override def toString: String = extract_linkedin_messages
  }

  def fromString(key: String): Try[CaptainDataWorkflowName] = Try {
    key match {
      case `connect_linkedin_profile` => CONNECT_LINKEDIN_PROFILE
      case `message_linkedin_profile` => MESSAGE_LINKEDIN_PROFILE
      case `visit_linkedin_profile` => VISIT_LINKEDIN_PROFILE
      case `extract_linkedin_conversations` => EXTRACT_LINKEDIN_CONVERSATIONS
      case `extract_linkedin_messages` => EXTRACT_LINKEDIN_MESSAGES
      case `extract_linkedin_connections` => EXTRACT_LINKEDIN_CONNECTIONS
    }
  }

  given format: Format[CaptainDataWorkflowName] = new Format[CaptainDataWorkflowName] {
    override def writes(o: CaptainDataWorkflowName): JsValue = {
      JsString(o.toString)
    }

    override def reads(json: JsValue): JsResult[CaptainDataWorkflowName] = {
      fromString(json.as[String]) match {
        case Success(value) => JsSuccess(value = value)
        case Failure(_) => JsError(s"Invalid workflow name :: $json")
      }
    }
  }
}



case class CDJobUid(uid: String) extends AnyVal {
  override def toString: String = uid
}

object CDJobUid {
  implicit val reads: Reads[CDJobUid] = new Reads[CDJobUid] {
    override def reads(json: JsValue): JsResult[CDJobUid] = {
      json match {
        case JsString(uid) => JsSuccess(CDJobUid(uid = uid))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[CDJobUid] = new Writes[CDJobUid] {
    override def writes(o: CDJobUid): JsValue = JsString(o.uid)
  }
}


case class CaptainDataWebhookResponse(
                                       job_uid: CDJobUid,
                                       workflow_name: CaptainDataWorkflowName,
                                       error: Option[String]
                                     )

object CaptainDataWebhookResponse {
  given format: OFormat[CaptainDataWebhookResponse] = Json.format[CaptainDataWebhookResponse]
}
