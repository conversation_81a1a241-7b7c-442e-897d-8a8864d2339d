package api.linkedin.models

import api.accounts.TeamId
import api.accounts.models.OrgId
import org.joda.time.DateTime
import play.api.libs.json.{JsError, JsNumber, JsResult, JsSuccess, JsValue, Reads, Writes}

import scala.util.{Failure, Success, Try}

case class LinkedinSettingUuid(uuid: String) extends AnyVal

object LinkedinSettingUuid {
  given reads: Reads[LinkedinSettingUuid] = (json: JsValue) => Try {
    json.as[String]
  } match {
    case Failure(e) => JsError(e.getMessage)
    case Success(uuid) => JsSuccess(LinkedinSettingUuid(uuid))
  }
}

case class LinkedinSettingId(id: Long) extends AnyVal

object LinkedinSettingId {
  implicit val reads: Reads[LinkedinSettingId] = new Reads[LinkedinSettingId] {
    override def reads(json: JsValue): JsResult[LinkedinSettingId] = {
      json match {
        case JsNumber(id) => JsSuccess(LinkedinSettingId(id = id.toLong))
        case randomValue => JsError(s"expected number, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[LinkedinSettingId] = new Writes[LinkedinSettingId] {
    override def writes(o: LinkedinSettingId): JsValue = JsNumber(o.id)
  }
}


case class LinkedinAccountAndLastInboxScrapeTime(
                                                  id: LinkedinSettingId,
                                                  uuid: LinkedinSettingUuid,
                                                  teamId: TeamId,
                                                  lastInboxScrapedAt: Option[DateTime],
                                                  orgId: OrgId
                                                )
