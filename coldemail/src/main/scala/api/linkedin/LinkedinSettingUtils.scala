package api.linkedin

import api.ApiResponseModuleForPermissionedApis
import api.accounts._
import api.linkedin.models.LinkedinAccountSettings
import play.api.mvc.{ActionRefiner, Request, Result, WrappedRequest}
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}

case class LinkedinRequest[A] (
                                linkedinAccountSettings: LinkedinAccountSettings,
                                loggedinAccount: Account,
                                actingTeamAccount: TeamMember,
                                permittedAccountIds: Seq[Long],
                                Logger: SRLogger,
                                Response: ApiResponseModuleForPermissionedApis,
                                request: Request[A]
                              ) extends WrappedRequest[A](request)

class LinkedinSettingUtils(linkedinSettingService: LinkedinSettingService) {

  final def hasLinkedinSetting(uuid: String)(implicit ec: ExecutionContext) =
    new ActionRefiner[PermissionRequest, LinkedinRequest] {

      override protected def executionContext: ExecutionContext = ec

      override protected def refine[A](request: PermissionRequest[A]): Future[Either[Result, LinkedinRequest[A]]] = {

        Future {
          val res = request.Response
          val permittedAccountIds = request.permittedAccountIds
          val actingAccount = request.actingTeamAccount.get

          linkedinSettingService.findLinkedinAccount(uuid = Some(uuid), id = None, teamId = TeamId(actingAccount.team_id)) match {
            case None =>
              Left(res.NotFoundError("Linkedin Account not present."))

            case Some(linkedinAccount) =>
              if (!permittedAccountIds.contains(linkedinAccount.owner_id)) {
                Left(res.ForbiddenError("You are not authorized to access this linkedin account."))
              }
              else if (actingAccount.team_id != linkedinAccount.team_id) {
                Left(res.ForbiddenError("This linkedin account does not belong to the current team. Please switch view to the respective team."))
              }
              else {
                Right(
                  LinkedinRequest(
                    linkedinAccountSettings = linkedinAccount,
                    loggedinAccount = request.loggedinAccount,
                    actingTeamAccount = actingAccount,
                    permittedAccountIds = permittedAccountIds,
                    Logger = request.Logger,
                    Response = res,
                    request = request
                  )
                )
              }
          }
        }

      }

    }

}