package api.phantombuster

import api.accounts.TeamId
import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, JsSuc<PERSON>}
import play.api.mvc.{Action, BaseController, ControllerComponents}
import utils.SRLogger
import utils.security.EncryptionHelpers

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class PhantomBusterWebhookController(
                                      phantomBusterService: PhantomBusterService,
                                      protected val controllerComponents: ControllerComponents
                                    ) extends BaseController {

  private implicit val ec: ExecutionContext = controllerComponents.executionContext

  def handleWebhook(secret: Option[String]) = Action.async(parse.json) { implicit request =>

    given logger: SRLogger = new SRLogger(logRequestId = "PhantomBusterWebhookService.handleWebhook")

    logger.info(s"Received data: ${request.body}")

    request.body.validate[PhantomBusterWebhookResult] match {
      case JsError(errors) =>
        Future.successful(BadRequest("Invalid Data Format"))

      case JsSuccess(phantomBusterWebhookResult, _) =>
        logger.info(s"Parsed Webhook Result: $phantomBusterWebhookResult")

        phantomBusterService.handleWebhook(
            secretKey = secret,
            phantomBusterWebhookResult = phantomBusterWebhookResult
          )
          .map(count => {
            Ok(s"Updated count: $count")
          })
          .recover {
            case e =>
              logger.error(e.getMessage, e)
              BadRequest(e.getMessage)
          }

    }
  }


}