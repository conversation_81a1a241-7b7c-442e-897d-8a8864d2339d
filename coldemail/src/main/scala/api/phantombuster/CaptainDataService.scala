package api.phantombuster

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.AccountId
import api.campaigns.services.{CampaignId, CampaignProspectService}
import api.captain_data.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>J<PERSON><PERSON>n<PERSON>, CaptainDataJobResponse, CaptainDataJobUID}
import api.emails.MessageSentAtByProspect
import api.linkedin.{LinkedinSettingDAO, LinkedinSettingLastScrapedInfo}
import api.linkedin.models.{CDJobUid, CaptainDataWorkflowStatus, LinkedinSettingId}
import api.linkedin_message_threads.{GetLastMessageTimestampError, LinkedinMessageThreadUuid, LinkedinMessageThreadsDAO}
import api.linkedin_messages.LinkedinMessagesService
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.ProspectId
import api.tasks.services.TaskService
import play.api.libs.ws.WSClient
import sr_scheduler.models.ChannelType
import utils.SRLogger
import utils.testapp.Test_TaskPgDAO.srUuidUtils

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import org.joda.time.{DateTime, Duration}


class CaptainDataService(
                  captainDataApi: CaptainDataAPI,
                  linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO,
                  linkedinSettingDAO: LinkedinSettingDAO,
                  prospectDAOService: ProspectDAOService,
                  linkedinMessagesService: LinkedinMessagesService,
                  taskService: TaskService,
                  campaignProspectService: CampaignProspectService
                        ) {

   def handleLinkedinConversationExtraction(
                                                    jobUid: CDJobUid,
                                                    status: CaptainDataWorkflowStatus,
                                                    error: Option[String])
                                                  (using logger: SRLogger, ec: ExecutionContext,
                                                   ws: WSClient): Future[List[String]] = {
    val processingStartedAt: DateTime = DateTime.now()

    status match {
      case CaptainDataWorkflowStatus.INITIATED | CaptainDataWorkflowStatus.CREATED =>
        logger.info(s"LinkedIn conversation extraction ${status.toString} for job_uid ${jobUid.uid}")
        Future.successful(List.empty)

      case CaptainDataWorkflowStatus.FAILURE =>
        /*
          These jobs are created by captain data for every x minute repeated interval. so we cant map job-uid to database. so just logging and leaving this part.
         */
        logger.error(s"LinkedIn conversation extraction failed for job_uid ${jobUid.uid} ${error.getOrElse("Unknown error")}")
        Future.successful(List.empty)

      case CaptainDataWorkflowStatus.SUCCESSFUL =>

        for {
          isRequestValid: Boolean <- getAccountFromJobUidAndMarkInboxLastScrapedAt(
            jobUid = CaptainDataJobUID(uid = jobUid.uid),
            conversationsProcessingStartedAt = processingStartedAt
          ).recover{
            case err =>
              logger.error(s"Failed to get account from job uid ${jobUid.uid} and updating it in database with exception", err)
              false
          }

          result: List[String] <- if (isRequestValid) {
            captainDataApi.getLinkedinThreadsFromJobUidWithPagination(
              jobUid = CaptainDataJobUID(uid = jobUid.uid),
              page=None
            ).map { threadObj =>
              threadObj.map { thread =>
                logger.info(s"Extracting messages from LinkedIn thread ${thread.thread_id}")

                // Sequential message retrieval
                saveMessageThreadsForCaptainData(
                  linkedInThreadsResponse = thread
                ).recover {
                  case ex =>
                    logger.error(s"Failed to retrieve messages for thread ${thread.thread_id}", ex)
                }

                thread.linkedin_thread_url
              }
            }
          } else {
            logger.error(s"Request is not valid for job_uid ${jobUid.uid}")
            Future.successful(List.empty)
          }


        } yield {
          result
        }
    }
  }


   private def getAccountFromJobUidAndMarkInboxLastScrapedAt(
                                                              jobUid: CaptainDataJobUID,
                                                              conversationsProcessingStartedAt: DateTime
                                                    )(implicit logger: SRLogger, ec: ExecutionContext, ws: WSClient): Future[Boolean] = {
    for {
      jobInfo: CaptainDataJobInfo <- captainDataApi.getJobInfo(
        jobUid = jobUid
      )

      accountUid: CaptainDataAccountUID = CaptainDataAccountUID(jobInfo.accounts.head)

      linkedinSettingLastScrapedInfo: LinkedinSettingLastScrapedInfo <- Future.fromTry(linkedinSettingDAO.getLastInboxScrapingDoneAt(
        captainDataAccountUID = accountUid
      ))

      isRequestValid: Boolean = if (linkedinSettingLastScrapedInfo.conversations_recovery_job_uid.isDefined
        && linkedinSettingLastScrapedInfo.conversations_recovery_job_uid.get == jobUid) {
        true
      } else {
        CaptainDataService.isConversationScrapingRequestValid(
          lastScrapedAt = linkedinSettingLastScrapedInfo.last_inbox_scraping_done_at,
          conversationsProcessingStartedAt = conversationsProcessingStartedAt
        )
      }

      _: Int <- if (isRequestValid) {
        Future.fromTry(linkedinSettingDAO.updateLastInboxScrapingDoneAt(
          captainDataAccountUID = accountUid,
          lastInboxScrapingDoneAt = conversationsProcessingStartedAt
        ))
      } else {

        handleConversationRecovery(
          accountUid = accountUid,
          linkedinSettingLastScrapedInfo = linkedinSettingLastScrapedInfo
        ).map {
          result =>
            logger.warn(s"Request is not valid for job_uid ${jobUid.toString} but recovery is initiated for account ${accountUid.toString} with job_uid ${result.toString}")
            0
        }.recoverWith {
          case ex =>
            logger.criticalSmsAlert(s"Failed to handle conversation recovery for account ${accountUid.toString} ", err=Some(ex))
            Future.failed(ex)
        }


      }

    } yield {
      isRequestValid
    }
  }

  private def handleConversationRecovery(
                                         accountUid: CaptainDataAccountUID,
                                         linkedinSettingLastScrapedInfo: LinkedinSettingLastScrapedInfo)
                                        (implicit logger: SRLogger, ec: ExecutionContext, ws: WSClient):Future[CaptainDataJobUID] = {

    for {
      fromTimeInMinutes: Long <- Future.fromTry(linkedinSettingLastScrapedInfo.last_inbox_scraping_done_at match {
        case Some(lastScrapedAt) =>
          val timeDifferenceMinutes = new Duration(lastScrapedAt, DateTime.now())
            .getStandardMinutes
          val bufferTimeInMinutes = 3 * 60
          Success(timeDifferenceMinutes + bufferTimeInMinutes)
        case None =>
          logger.shouldNeverHappen(s"handleConversationRecovery :: Last scraped time is not present")
          Failure(new Exception("Last scraped time is not present"))
      })

      jobUid: CaptainDataJobUID <- captainDataApi.retrieveLinkedInConversationsWithOutRepeat(
        accountUid = accountUid,
        linkedinSettingId = linkedinSettingLastScrapedInfo.id,
        accountId = linkedinSettingLastScrapedInfo.accountId,
        teamId = linkedinSettingLastScrapedInfo.team_id,
        fromTimeInMinutes = fromTimeInMinutes
      )

      _:Int <- Future.fromTry(
        linkedinSettingDAO.updateConversationsRecoveryJobUid(
          linkedinSettingUuid = linkedinSettingLastScrapedInfo.uuid,
          teamId = linkedinSettingLastScrapedInfo.team_id,
          conversationsRecoveryJobUid = jobUid
        )
      )

    } yield {
      jobUid
    }
  }

  def handleLinkedinMessagesFromJobUid(
                                                jobUid: CDJobUid,
                                                status: CaptainDataWorkflowStatus,
                                                error: Option[String]
                                              )(
                                                implicit logger: SRLogger,
                                                ec: ExecutionContext, ws: WSClient
                                              ): Future[Boolean] = {

    status match {
      case CaptainDataWorkflowStatus.INITIATED | CaptainDataWorkflowStatus.CREATED =>
        logger.info(s"LinkedIn message extraction ${status.toString} for job_uid ${jobUid.uid}")
        Future.successful(false)

      case CaptainDataWorkflowStatus.FAILURE =>
        // TODO: mark the threads table that this job failed
        logger.shouldNeverHappen(s"LinkedIn message extraction failed for job_uid ${jobUid.uid} : ${error.getOrElse("Unknown error")}")
        Future.failed(Exception(
          s"LinkedIn message extraction failed for job_uid ${jobUid.uid} : ${error.getOrElse("Unknown error")}"
        ))

      case CaptainDataWorkflowStatus.SUCCESSFUL =>
        for {
          messageObj: CaptainDataJobResponse.RetrieveMessageResponse <- captainDataApi.getLinkedinMessagesFromJobUid(
            jobUid = CaptainDataJobUID(uid = jobUid.uid)
          )

          _: Option[Int] <- Future.fromTry(verifyAndHandleMessageExtraction(
            messageObj = messageObj
          ))

        } yield {
          true
        }
    }
  }


  def verifyAndHandleMessageExtraction(
    messageObj: CaptainDataJobResponse.RetrieveMessageResponse
  )(implicit ws: WSClient, ec: ExecutionContext, logger: SRLogger): Try[Option[Int]] = {

    getProspectIdToAssignThread(
      contact_profile_url = messageObj.linkedin_profile_url,
      teamId = messageObj.meta.team_id,
      threadId = messageObj.meta.linkedin_thread_id
    ).flatMap {
      case Some(prospectId) =>
        handleMessageExtraction(
          prospectId = prospectId,
          messageObj = messageObj
        ).map(Some(_))

      case None =>
        logger.info(s"skipping the message extraction as this thread: ${messageObj.thread_id} doesn't have any prospect associated with it.")
        Success(None)
    }.recoverWith { case err =>
      logger.error(s"Error while fetching prospectId for linkedin_url: ${messageObj.linkedin_profile_url}", err)
      Failure(err)
    }
  }


  // This Function is almost same as phantomBusterService.saveLinkedinMessagesAndTrackProspectReply from step 3
  private def handleMessageExtraction(
                             prospectId: ProspectId,
                             messageObj: CaptainDataJobResponse.RetrieveMessageResponse
                             )(implicit ws: WSClient, ec: ExecutionContext, logger: SRLogger): Try[Int] = {

    val threadId = LinkedinMessageThreadId(id = messageObj.meta.linkedin_thread_id)
    val teamId = messageObj.meta.team_id

    for {
      campaignId: Option[CampaignId] <- taskService.findLatestCampaignFromWhichLinkedinTaskIsDoneForProspect(
          prospectId = prospectId,
          teamId = teamId
        ).recoverWith { case err =>
          logger.error(s"Failed to find latest campaign for prospect_${prospectId.id} team_${teamId.id}", err)
          Failure(err)
        }

      _ : Int <- linkedinMessageThreadsDAO.updateProspectIdAndCampaignId(
          messageThreadId = threadId,
          prospectId = prospectId,
          campaignId = campaignId,
          teamId = teamId
        ).recoverWith { case err =>
          logger.error(s"Failed to update prospect_${prospectId.id} and campaign_${campaignId.map(_.id)} for thread_${threadId.id} team_${teamId.id}", err)
          Failure(err)
        }

      parsedLinkedinMessages: List[PhantomBusterLinkedinMessage] = {
          messageObj.messages.map { msg =>PhantomBusterLinkedinMessage(
            message_body = msg.content,
            sent_by_user = msg.linkedin_profile_id != messageObj.linkedin_profile_id,
            timestamp = DateTime(msg.created_at)
          )}
        }

      filteredMessages: List[PhantomBusterLinkedinMessage] <- filterMessagesToBeSaved(
          parsedLinkedinMessages = parsedLinkedinMessages,
          messageThreadId = threadId,
          teamId = teamId
        ).recoverWith { case err =>
          logger.error(s"Failed to filter messages for thread_${threadId.id} team_${teamId.id} :: messages count: ${parsedLinkedinMessages.length}", err)
          Failure(err)
        }

      reply: Option[LinkedinMessageDetails] <- {
          linkedinMessagesService.saveLinkedinMessages(
            messageThreadId = threadId,
            teamId = teamId,
            messages = filteredMessages
          ).recoverWith { case err =>
            logger.error(s"Failed to save LinkedIn messages for thread_${threadId.id} team_${teamId.id} :: filtered messages count: ${filteredMessages.length}", err)
            Failure(err)
          }
        }

      campaignIdsPausedForProspect: List[Long] <- {
          if (reply.isDefined) {
            trackReplyAndMarkProspectsAsCompleted(
              messageThreadId = threadId,
              linkedinMessageDetails = reply.get,
              teamId = teamId
            ).recoverWith { case err =>
              logger.error(s"Failed to track reply and mark prospects as completed for thread_${threadId.id} team_${teamId.id} :: reply timestamp: ${reply.get.timestamp}", err)
              Failure(err)
            }
          }
          else {
            Success(List())
          }
        }

      lastMessageTimestamp: Option[DateTime] = filteredMessages.lastOption.map(_.timestamp)

      messageThreadUpdate: Int <- linkedinMessageThreadsDAO.updateContainerStatusAndTimeStamp(
          messageThreadId = threadId,
          lastMessageTimestamp = lastMessageTimestamp,
          teamId = messageObj.meta.team_id
        ).recoverWith { case err =>
          logger.error(s"Failed to update container status and timestamp for thread_${threadId.id} team_${teamId.id} :: lastMessageTimestamp: ${lastMessageTimestamp.map(_.toString)}", err)
          Failure(err)
        }

    } yield {
      messageThreadUpdate
    }
  }



   def saveMessageThreadsForCaptainData(
    linkedInThreadsResponse: CaptainDataJobResponse.LinkedInThreadsResponse
    )(implicit
        ec: ExecutionContext,
        ws: WSClient,
        logger: SRLogger
    ): Future[CaptainDataJobUID] = {

      logger.info(
        s"Starting to save message threads for Captain Data - threadUrl: ${linkedInThreadsResponse.linkedin_thread_url}"
      )

      for {
        tableThreadId: Long <- Future.fromTry{
          linkedinMessageThreadsDAO.saveCaptainDataLinkedinThread(
            teamId = linkedInThreadsResponse.meta.team_id,
            uuid = LinkedinMessageThreadUuid(
              uuid = srUuidUtils.generateLinkedinMessageThreadUuid()
            ),
            ownerAccountId = linkedInThreadsResponse.meta.account_id,
            isLastMessageFromUser = linkedInThreadsResponse.last_message.linkedin_profile_id == linkedInThreadsResponse.linkedin_profile_id,
            linkedinSettingId = linkedInThreadsResponse.meta.linkedin_setting_id.id,
            threadUrl = LinkedinMessageThreadUrl(linkedInThreadsResponse.linkedin_thread_url),
            isLastMessageRead = linkedInThreadsResponse.read
          )
        }.recoverWith { case error =>
          logger.shouldNeverHappen(s"Failed to save Captain Data LinkedIn thread :: thread_url: ${linkedInThreadsResponse.linkedin_thread_url} :: team_${linkedInThreadsResponse.meta.team_id.id} :: account_${linkedInThreadsResponse.meta.account_id} :: linkedin_setting_${linkedInThreadsResponse.meta.linkedin_setting_id.id} :: cd_account_uid: ${linkedInThreadsResponse.meta.cd_account_uid} ", Some(error))
          Future.failed(error)
        }

        retrieveMessagesResult: CaptainDataJobUID <- {
          captainDataApi.retrieveLinkedInMessages(
            accountUid = CaptainDataAccountUID(uid = linkedInThreadsResponse.meta.cd_account_uid),
            linkedinMessageThreadUrl = LinkedinMessageThreadUrl(linkedInThreadsResponse.linkedin_thread_url),
            fromTimeInMinutes = 0, // TODO: make it 60 mins
            linkedinSettingId = linkedInThreadsResponse.meta.linkedin_setting_id,
            accountId = linkedInThreadsResponse.meta.account_id,
            teamId = linkedInThreadsResponse.meta.team_id,
            threadsTableId = tableThreadId
          )
        }.recoverWith { case error =>
          logger.shouldNeverHappen(s"Failed to retrieve LinkedIn messages :: thread_url: ${linkedInThreadsResponse.linkedin_thread_url} :: team_${linkedInThreadsResponse.meta.team_id.id} :: account_${linkedInThreadsResponse.meta.account_id} :: linkedin_setting_${linkedInThreadsResponse.meta.linkedin_setting_id.id} :: cd_account_uid: ${linkedInThreadsResponse.meta.cd_account_uid} :: extracted_at: ${linkedInThreadsResponse.extracted_at} :: linkedin_profile_id: ${linkedInThreadsResponse.linkedin_profile_id} :: is_read: ${linkedInThreadsResponse.read}", Some(error))
          Future.failed(error)
        }

        updateScrapingResult: Int <- Future.fromTry{
          linkedinSettingDAO.updateLastInboxScrapingDoneAt(
            linkedinSettingId = linkedInThreadsResponse.meta.linkedin_setting_id,
            teamId = linkedInThreadsResponse.meta.team_id,
            lastInboxScrapingDoneAt = linkedInThreadsResponse.extracted_at
          )
        }.recoverWith { case error =>
          logger.shouldNeverHappen(s"Failed to update last inbox scraping timestamp :: linkedin_setting_id: ${linkedInThreadsResponse.meta.linkedin_setting_id.id} :: team_id: ${linkedInThreadsResponse.meta.team_id.id} :: account_id: ${linkedInThreadsResponse.meta.account_id} :: cd_account_uid: ${linkedInThreadsResponse.meta.cd_account_uid} :: extracted_at: ${linkedInThreadsResponse.extracted_at} :: thread_url: ${linkedInThreadsResponse.linkedin_thread_url}", Some(error))
          Future.failed(error)
        }
      } yield {
        retrieveMessagesResult
      }

      }


  // This function is copied from LinkedinMessageThreadsService.getProspectIdToAssignThread
  def getProspectIdToAssignThread(
                                   threadId: Long,
                                   contact_profile_url: String,
                                   teamId: TeamId
                                 )(using logger: SRLogger): Try[Option[ProspectId]] = {

    linkedinMessageThreadsDAO.getProspectIdFromThreadId(
      threadId = threadId,
      teamId = teamId
    ).flatMap {
      case Some(existingProspectId) =>
        logger.info(s"Found existing prospect ${existingProspectId.id} assigned to thread ${threadId}")
        Success(Some(existingProspectId))

      case None =>
        for {
          prospectIds: List[ProspectId] <- prospectDAOService.getProspectIdByLinkedinUrlAndTeamId(
            teamId = teamId,
            linkedin_url = contact_profile_url
          )

          /*
          We can assign only one prospect to one thread.
          There can be multiple prospects with same linkedin_url
          We are fetching the prospect most recently contacted through linkedin_channel.
          And assigning the thread to this prospect.
          */
          prospectId: Option[ProspectId] <- if (prospectIds.size == 1) {
            Success(Some(prospectIds.head))
          }
          else {
            // prospectIds.isEmpty case is handle inside this.
            taskService.findLastContactedProspectByLinkedinChannel(
                prospectIds = prospectIds,
                channelType = ChannelType.LinkedinChannel,
                teamId = teamId
              )
              .map {
                case None => None
                case Some(pid) =>
                  logger.warn(s"Found multiple prospects with same linkedin_url: $contact_profile_url :: prospectIds: $prospectIds :: Assigning the thread to prospect_id: ${pid.id}")
                  Some(pid)
              }
          }
        } yield {
          prospectId
        }
    }
  }


  // this function is copied from LinkedinMessageThreadsService.filterMessagesToBeSaved
  def filterMessagesToBeSaved(
                               parsedLinkedinMessages: List[PhantomBusterLinkedinMessage],
                               messageThreadId: LinkedinMessageThreadId,
                               teamId: TeamId
                             )(using logger: SRLogger): Try[List[PhantomBusterLinkedinMessage]] = {

    linkedinMessageThreadsDAO.getLastMessageTimestampForThread(
      messageThreadId = Some(messageThreadId),
      threadUrl = None,
      teamId = teamId
    ) match {
      case Left(GetLastMessageTimestampError.SQLException(err)) =>
        logger.error(s"Failed to get last message timestamp :: message_thread_id: ${messageThreadId.id} :: team_id: ${teamId.id} :: messages_count: ${parsedLinkedinMessages.size}", err)
        Failure(err)

      case Left(GetLastMessageTimestampError.MessageThreadNotPresent) =>
        logger.error(s"Failed to get last message timestamp - thread not found :: message_thread_id: ${messageThreadId.id} :: team_id: ${teamId.id} :: messages_count: ${parsedLinkedinMessages.size}")
        Failure(new Exception(s"Message Thread: ${messageThreadId.id} Not Found In DB"))

      case Left(GetLastMessageTimestampError.MessageThreadHasNotBeenScraped) =>
        Success(parsedLinkedinMessages)

      case Right(lastMessageTimestamp) =>
        Success(parsedLinkedinMessages.filter(_.timestamp.isAfter(lastMessageTimestamp)))
    }

  }

  // this function is copied from PhantomBusterService.trackReplyAndMarkProspectsAsCompleted
  private def trackReplyAndMarkProspectsAsCompleted(
                                                     messageThreadId: LinkedinMessageThreadId,
                                                     linkedinMessageDetails: LinkedinMessageDetails,
                                                     teamId: TeamId
                                                   ): Try[List[Long]] = {

    for {
      prospectId: Option[ProspectId] <- linkedinMessageThreadsDAO.getProspectIdOfMessageThread(
        messageThreadId = messageThreadId,
        teamId = teamId
      )

      campaignIds: List[CampaignId] <- campaignProspectService.getCampaignsWhoseReplyWillBeTrackedInThisMessageThread(
        messageThreadId = messageThreadId,
        teamId = teamId
      )

      markAsReplied: List[Long] <- {
        if (prospectId.isDefined) {
          campaignProspectService.markCampaignsAsRepliedAndCompleted(
            campaign_ids = campaignIds.map(_.id),
            completed_because_secondary_prospect_email_id = None,
            messageSentAtByProspect = MessageSentAtByProspect.LinkedinMessageSentAtByProspect(
              sent_at = linkedinMessageDetails.timestamp,
              linkedin_message_id = linkedinMessageDetails.id,
              prospect_id = prospectId.get
            )
          )
        }
        else {
          Success(List())
        }
      }
    } yield {
      markAsReplied
    }

  }

}


object CaptainDataService {

  def isConversationScrapingRequestValid(
                                          lastScrapedAt: Option[DateTime],
                                          conversationsProcessingStartedAt: DateTime
                                        )(implicit logger: SRLogger, ec: ExecutionContext): Boolean = {
    val minimumGapInMinutes = AppConfig.CaptainData.RetrieveLinkedinConversations.repetitiveIntervalInMinutes
      + AppConfig.CaptainData.RetrieveLinkedinConversations.deltaInMinutes

    lastScrapedAt match {
      case Some(lastScrapedAt) =>
        if (conversationsProcessingStartedAt.minusMinutes(
          minimumGapInMinutes
        ).isAfter(lastScrapedAt)) {
          logger.error(s" isConversationScrapingRequestValid false as lastScrappedAt ${lastScrapedAt} is before conversationsProcessingStartedAt ${conversationsProcessingStartedAt.minusMinutes(minimumGapInMinutes)}")
          false
        } else {
          true
        }
      case None =>
        true
    }
  }

}
