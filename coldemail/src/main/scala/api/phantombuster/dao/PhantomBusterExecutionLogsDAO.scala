package api.phantombuster.dao

import api.phantombuster.{PhantomBusterScript, TeamIdAndInternalApiKeyId}
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}

import scala.util.Try

class PhantomBusterExecutionLogsDAO {

  def saveLog(
               teamIdAndInternalApiKeyId: TeamIdAndInternalApiKeyId,
               execution_time_used: Int,
               agentScript: PhantomBusterScript
             ): Try[Long] = Try {

    DB autoCommit { implicit session =>
      sql"""
           INSERT INTO phantombuster_execution_logs(
              phantombuster_api_key_id,
              team_id,
              execution_time_used,
              agent_script,
              execution_timestamp
           ) VALUES (
              ${teamIdAndInternalApiKeyId.internalApiKeyId},
              ${teamIdAndInternalApiKeyId.teamId.id},
              $execution_time_used,
              ${agentScript.toString},
              now()
           )
           RETURNING id;
           """
        .map(rs => rs.long("id"))
        .single
        .apply()
        .get
    }

  }

}
