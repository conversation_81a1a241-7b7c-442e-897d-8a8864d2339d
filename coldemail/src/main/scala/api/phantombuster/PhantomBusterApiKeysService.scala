package api.phantombuster

import api.phantombuster.dao.PhantomBusterApiKeysDAO

import scala.util.{Failure, Success, Try}

class PhantomBusterApiKeysService(phantomBusterApiKeysDAO: PhantomBusterApiKeysDAO) {

  def fetchApiKey(): Try[PhantomBusterApiKeyDetails] = {
    phantomBusterApiKeysDAO.fetchApiKey()
      .flatMap {
        case None =>
          Failure(new Exception(ExecuteAutoLinkedinTasksError.AllAccountsHaveExhaustedExecutionTimeLimit.toString))

        case Some(apiKey) =>
          Success(apiKey)
      }
  }

  def updateExecutionTimeLeft(internalApiKeyId: Long, executionTimeUsed: Int): Try[Int] = {
    phantomBusterApiKeysDAO.updateExecutionTimeLeft(
      internalApiKeyId = internalApiKeyId, 
      executionTimeUsed = executionTimeUsed
    )
  }

  def resetExecutionTimeOnResetDate(): Try[List[Int]] = {
    phantomBusterApiKeysDAO.resetExecutionTimeOnResetDate()
  }

  def fetchAccountsWithLowExecutionTimeLeft(): Try[List[String]] = {
    phantomBusterApiKeysDAO.fetchAccountsWithLowExecutionTimeLeft()
  }

  def fetchExecutionTimeLimitsAndUsage(): Try[List[AccountExecutionLimitsAndUsage]] = {
    phantomBusterApiKeysDAO.fetchExecutionTimeLimitsAndUsage()
  }

}
