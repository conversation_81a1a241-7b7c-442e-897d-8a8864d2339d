package api.product_onboarding.models

import scalikejdbc.WrappedResultSet

case class ProductOnboardingFlags(
                                 is_inbox_setup_done_flag:<PERSON><PERSON><PERSON>,
                                 is_multichannel_setup_completed_flag:<PERSON><PERSON><PERSON>,
                                 is_team_invite_sent_flag:<PERSON><PERSON><PERSON>,
                                 is_spam_test_done_flag:<PERSON><PERSON><PERSON>,
                                 is_warm_up_hero_enabled_flag:<PERSON><PERSON><PERSON>,
                                 is_prospect_daddy_setup_flag:<PERSON><PERSON><PERSON>,
                                 is_prospect_list_validated:<PERSON><PERSON><PERSON>,
                                 is_crm_connected_flag:<PERSON><PERSON><PERSON>,
                                 is_zapier_integration_done:<PERSON><PERSON><PERSON>,
                                 is_email_finder_used:<PERSON><PERSON><PERSON>,
                                 is_campaign_created_for_org:<PERSON><PERSON><PERSON>,
                                 is_email_account_added:<PERSON><PERSON><PERSON>,
                                 is_linkedin_account_added:<PERSON><PERSON><PERSON>,
                                 is_calling_account_added:<PERSON><PERSON><PERSON>,
                                 is_campaign_not_started: <PERSON><PERSON><PERSON>,
                                 is_whatsapp_account_added: <PERSON><PERSON><PERSON>,
                                 is_leadfinder_used: <PERSON><PERSON>an
                                 )
object ProductOnboardingFlags{
  def fromDb(rs: WrappedResultSet): ProductOnboardingFlags = {
    val is_inbox_setup_done_flag = rs.boolean("inbox_setup")
    val is_multichannel_setup_completed_flag = rs.boolean("multichannel_setup")
    val is_team_invite_sent_flag = rs.boolean("invited_team_members")
    val is_spam_test_done_flag = rs.boolean("spam_test")
    val is_warm_up_hero_enabled_flag = rs.boolean("warmup_used")
    val is_prospect_daddy_setup_flag = rs.boolean("prospect_daddy")
    val is_crm_connected_flag = rs.boolean("crm_integration")
    val is_zapier_integration_done = rs.boolean("integration_via_zapier")
    val is_email_finder_used = rs.boolean("email_finder")
    val is_campaign_created_for_org = rs.boolean("org_campaign")
    val is_prospect_list_validated = rs.boolean("email_validated")
    val is_email_account_added  = rs.boolean("email_account_added")
    val is_linkedin_account_added  = rs.boolean("linkedin_account_added")
    val is_calling_account_added = rs.boolean("call_account_added")
    val is_campaign_not_started = rs.boolean("campaign_not_started")
    val is_whatsapp_account_added = rs.boolean("whatsapp_account_added")
    val is_leadfinder_used =rs.boolean("is_leadfinder_used")

    ProductOnboardingFlags(
      is_inbox_setup_done_flag = is_inbox_setup_done_flag,
      is_multichannel_setup_completed_flag=is_multichannel_setup_completed_flag,
      is_team_invite_sent_flag=is_team_invite_sent_flag,
      is_spam_test_done_flag=is_spam_test_done_flag,
      is_warm_up_hero_enabled_flag=is_warm_up_hero_enabled_flag,
      is_prospect_daddy_setup_flag=is_prospect_daddy_setup_flag,
      is_crm_connected_flag=is_crm_connected_flag,
      is_zapier_integration_done=is_zapier_integration_done,
      is_email_finder_used=is_email_finder_used,
      is_campaign_created_for_org = is_campaign_created_for_org ,
      is_prospect_list_validated = is_prospect_list_validated,
      is_email_account_added = is_email_account_added,
      is_linkedin_account_added = is_linkedin_account_added,
      is_calling_account_added  = is_calling_account_added,
      is_campaign_not_started = is_campaign_not_started,
      is_whatsapp_account_added = is_whatsapp_account_added,
      is_leadfinder_used = is_leadfinder_used
    )
  }
}
