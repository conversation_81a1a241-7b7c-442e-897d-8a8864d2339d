package api.product_onboarding.models

import org.postgresql.util.PGobject
import play.api.libs.json.{Json, OWrites, Reads}
import scalikejdbc.WrappedResultSet

case class ProductOnboardingDataOrg(
                                     is_warm_up_hero_setup_done: <PERSON><PERSON><PERSON>,
                                     is_installing_prospect_daddy_done: <PERSON><PERSON><PERSON>,
                                   )
object ProductOnboardingDataOrg{
  implicit val reads: Reads[ProductOnboardingDataOrg] = Json.reads[ProductOnboardingDataOrg]
  implicit val writes: OWrites[ProductOnboardingDataOrg] = Json.writes[ProductOnboardingDataOrg]
}
