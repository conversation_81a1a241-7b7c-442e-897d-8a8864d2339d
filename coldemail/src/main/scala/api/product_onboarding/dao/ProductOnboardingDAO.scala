package api.product_onboarding.dao

import api.accounts.{OrganizationRole, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.models.CampaignStepType
import api.campaigns.services.CampaignId
import api.integrations.WebhookSourceType
import api.linkedin.models.LinkedinAccountStatus
import api.product_onboarding.models.{ProductOnboardingDataOrg, ProductOnboardingDataTeams, ProductOnboardingFlags, UpdateAndGetProductOnboardingData}
import api.prospects.ProspectSource
import api.search.CampaignQuery
import play.api.libs.json.{Json, OWrites, Reads}
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import sr_scheduler.CampaignStatus
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus

import scala.util.{Failure, Success, Try}


case class CampaignFlags(
                          campaign_id: Option[Long],
                          has_created_sequence: <PERSON><PERSON><PERSON>,
                          has_added_prospects: <PERSON><PERSON><PERSON>,
                          is_channel_setup_done: Boolean
                        )



object CampaignFlags {
    implicit val reads: Reads[CampaignFlags] = Json.reads[CampaignFlags]
    implicit val writes: OWrites[CampaignFlags] = Json.writes[CampaignFlags]
}

class ProductOnboardingDAO {

  def updateProductOnboardingDataOrg(
                                   productOnboardingDataOrg: ProductOnboardingDataOrg,
                                   orgId: OrgId
                                 ): Try[Long] = Try {
    DB.autoCommit { implicit session =>
      sql"""
          update
            organizations
          set
            product_onboarding_data = to_jsonb(${Json.toJson(productOnboardingDataOrg).toString()}::jsonb)
          where
            id = ${orgId.id}
        """
        .update
        .apply()}
  }

  def updateProductOnboardingDataTeams(
                                      productOnboardingDataTeams: ProductOnboardingDataTeams,
                                      orgId: OrgId,
                                      tid: TeamId
                                    ): Try[Long] = Try {
    DB.autoCommit { implicit session =>
      sql"""
          update
            teams
          set
            product_onboarding_data = to_jsonb(${Json.toJson(productOnboardingDataTeams).toString()}::jsonb)
          where
            org_id = ${orgId.id}
          and
            id = ${tid.id}
        """
        .update
        .apply()
    }
  }

  def getProductOnboardingData(
                                orgId: OrgId,
                                tid: TeamId,
                                accountId: AccountId
                              ): Try[UpdateAndGetProductOnboardingData] = Try {
    DB readOnly  { implicit session =>
      sql"""
         select
            o.product_onboarding_data as product_onboarding_data_org,
            t.product_onboarding_data as product_onboarding_data_teams,
            a.onboarding_data as product_onboarding_data_accounts
         from
            teams t
         join
            organizations o
         on t.org_id = o.id
         join
            accounts a
         on a.org_id = t.org_id and a.id = ${accountId.id}
         where
            t.org_id = ${orgId.id}
         and
            t.id = ${tid.id}
     """
        .map(rs => UpdateAndGetProductOnboardingData.fromDb(rs))
        .single
        .apply()
        .get
    }
  }

  def getProductOnboardingFlags(
                                 orgId: OrgId,
                                 tid: TeamId
                               ): Try[ProductOnboardingFlags] =Try {
    DB readOnly { implicit session =>
      sql"""
          SELECT
                        ( select exists
                                (
                                  select
                                    cs.step_type
                                  from
                                    campaign_steps cs
                                  join
                                    campaigns c
                                  on c.id = cs.campaign_id
                                  where
                                    c.team_id = t.id
                                  and
                                    cs.step_type != ${CampaignStepType.AutoEmailStep.toKey}
                                ) as multichannel_setup
                        ),
                       (select exists
                               (
                                 select
                                   *
                                 from
                                   team_inbox ti
                                 where
                                   ti.team_id = t.id
                               )
                             ) as inbox_setup,
                       (select exists
                               (
                                 select
                                    *
                                 from
                                    accounts a
                                 where
                                    a.org_id = t.org_id
                                 and
                                    (a.org_role!=${OrganizationRole.OWNER.toString} or a.org_role is null)
                               )
                             ) as invited_team_members,
                       (select exists
                               (
                                 select
                                    st.id
                                 from
                                   spam_tests st
                                 inner join
                                    campaigns c on st.campaign_id = c.id
                                 where
                                    t.id = c.team_id
                               )
                             ) as spam_test,
                       (select exists
                              (
                                select
                                  crm_type
                                FROM
                                  workflow_crm_settings wc
                                WHERE
                                   t.id = wc.team_id
                              )
                            ) as crm_integration,
                       (select exists
                                   (
                                     select
                                        o1.warmupbox_key_enc
                                     from
                                       organizations o1
                                     where
                                        o1.id=t.org_id
                                     and o1.warmupbox_key_enc is not null
                                   )
                                 ) as warmup_used,
                      (select exists
                                (
                                   select * from  webhooks w
                                   where t.id = w.team_id
                                   and w.active
                                   and w.source = ${WebhookSourceType.ZAPIER.toString}
                                )
                            ) as integration_via_zapier,
                      (select exists
                                  (
                                     select
                                       *
                                     from
                                       teams as t1
                                     where
                                       (
                                         t1.uplead_api_key_enc IS NOT NULL
                                         or t1.dropcontact_api_key_enc IS NOT NULL
                                         or t1.anymailfinder_api_key_enc IS NOT NULL
                                         or t1.clearbit_api_key_enc IS NOT NULL
                                         or t1.hunter_api_key_enc IS NOT NULL
                                         or t1.aeroleads_api_key_enc IS NOT NULL
                                       )
                                       and t1.id = t.id
                                  )
                           ) as email_finder,

                      (select exists
                                   (
                                      select
                                        *
                                      from
                                        accounts as acc
                                      where
                                        acc.sr_pd_api_key_enc IS NOT NULL
                                        and acc.active = TRUE
                                        and acc.org_id = t.org_id
                                   )
                            ) as prospect_daddy,
                      (select exists
                                   (
                                      select
                                        c.id
                                      from
                                        campaigns c
                                      where
                                        t.id = c.team_id
                                      and c.status != ${CampaignStatus.NOT_STARTED.toString}
                                   )
                            ) as org_campaign,
                      (select exists
                                   (
                                      select
                                        pe.email_checked
                                      from
                                        prospects_emails pe
                                      where
                                        pe.email_checked = TRUE
                                      and
                                        t.id = pe.team_id
                                   )
                            ) as email_validated,
                      (select exists
                                    (
                                    select
                                        *
                                    from
                                        email_settings es
                                    where
                                        es.status = ${EmailSettingStatus.Active.toString}
                                    and
                                       t.id = es.team_id
                                    )

                      ) as email_account_added,
                      (select exists
                                    (
                                    select
                                        *
                                    from
                                        linkedin_settings ls
                                    where
                                        ls.status in (${LinkedinAccountStatus.Active.toString},${LinkedinAccountStatus.NeedToAssignProxy.toString})
                                    and
                                       t.id = ls.team_id
                                    )

                      ) as linkedin_account_added,
                      (select exists
                                    (
                                    select
                                        *
                                    from
                                        call_settings cs
                                    where
                                       t.id = cs.team_id
                                    )

                      ) as call_account_added,
                      (select exists
                                   (
                                        select
                                        *
                                        from
                                           campaigns c
                                      where
                                        t.id = c.team_id
                                      and c.status = ${CampaignStatus.NOT_STARTED.toString}
                                   )
                      ) as campaign_not_started,
                      (
                      select exists
                                    (
                                    select
                                        *
                                    from
                                        whatsapp_settings ws
                                    where
                                        t.id = ws.team_id
                                    )
                      ) as whatsapp_account_added,
                      (
                       select exists
                         (
                         select
                         *
                         from
                            prospects p
                         where
                            p.prospect_source in (${ProspectSource.PROSPECTDADDY.toString},${ProspectSource.SMARTREACH_LEADFINDER.toString})
                         and t.id = p.team_id)
                         ) as is_leadfinder_used
                      FROM
                        teams t
                      WHERE
                        t.id=${tid.id}
                      and
                        t.org_id = ${orgId.id}
                      LIMIT 1
           """
        .map(rs => ProductOnboardingFlags.fromDb(rs))
        .single
        .apply()
        .get
    }
  }

    def getLatestNotStartedCampaign(teamId: TeamId,accountId:AccountId): Try[Option[CampaignId]] = Try {
        DB readOnly { implicit session =>
            sql"""
        SELECT id
        FROM campaigns
        WHERE team_id = ${teamId.id}
        AND account_id = ${accountId.id}
        AND status = ${CampaignStatus.NOT_STARTED.toString}
        ORDER BY created_at DESC
        LIMIT 1
      """.map(rs => CampaignId(rs.long("id")))
              .single
              .apply()
        }
    }

    def getCampaignStepTypes(campaignId: CampaignId): Try[List[CampaignStepType]] = Try {
        DB readOnly { implicit session =>
            sql"""
        SELECT DISTINCT step_type
        FROM campaign_steps
        WHERE campaign_id = ${campaignId.id}
      """.map{
                rs =>
                    CampaignStepType.fromKey(rs.string("step_type")) match {
                        case Success(stepType) => stepType
                        case Failure(ex) => throw ex
                    }}.list.apply()
        }
    }

    def hasProspects(campaignId: CampaignId): Try[Boolean] = Try {
        DB readOnly { implicit session =>
            sql"""
        SELECT EXISTS (
          SELECT * FROM campaigns_prospects
          WHERE campaign_id = ${campaignId.id}
        ) as has_prospects
      """.map(rs => rs.boolean("has_prospects")).single.apply().get
        }
    }
}

