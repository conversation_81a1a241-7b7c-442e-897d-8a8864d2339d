package api.product_onboarding

import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.{Campaign, CampaignSettings}
import api.campaigns.models.CampaignStepType
import api.campaigns.services.{CampaignId, CampaignService}
import api.linkedin.LinkedinValidationError
import api.product_onboarding.dao.{CampaignFlags, ProductOnboardingDAO}
import api.product_onboarding.models.{ProductOnboardingData, ProductOnboardingDataOrg, ProductOnboardingDataTeams, ProductOnboardingFlags, UpdateAndGetProductOnboardingData, UseCaseFlags}

import scala.util.{Failure, Success, Try}

sealed trait ProductOnboardValidationError

object ProductOnboardValidationError {
  case class SQLException(err: Throwable) extends ProductOnboardValidationError
}
class ProductOnboardingService(
                              productOnboardingDAO: ProductOnboardingDAO,
                              campaignService: CampaignService
                              ) {

  def updateProductOnboardingData(
                               productOnboardingData: UpdateAndGetProductOnboardingData,
                               orgId: OrgId,
                               tid: TeamId
                             ): Try[List[Long]] = {
    val productOnboardingDataOrg = ProductOnboardingDataOrg(
      is_warm_up_hero_setup_done = productOnboardingData.is_warm_up_hero_setup_done,
      is_installing_prospect_daddy_done = productOnboardingData.is_installing_prospect_daddy_done
    )
    val productOnboardingDataTeams = ProductOnboardingDataTeams(
      is_intro_completed = productOnboardingData.is_intro_completed,
      is_inbox_setup_completed = productOnboardingData.is_inbox_setup_completed,
      is_spam_test_done = productOnboardingData.is_spam_test_done,
      is_email_finder_setup_done = productOnboardingData.is_email_finder_setup_done,
      is_connect_crm_done = productOnboardingData.is_connect_crm_done,
      is_team_invite_sent = productOnboardingData.is_team_invite_sent,
      is_multi_channel_setup_completed = productOnboardingData.is_multi_channel_setup_completed,
      is_prospect_list_validated = productOnboardingData.is_prospect_list_validated,
      is_zapier_integration_done = productOnboardingData.is_zapier_integration_done,
      is_campaign_created_for_org = productOnboardingData.is_campaign_created_for_org
    )
    for{


      productOnboardingDataOrg: Long <- productOnboardingDAO.updateProductOnboardingDataOrg(
        productOnboardingDataOrg = productOnboardingDataOrg,
        orgId = orgId
      )

      productOnboardingDataTeams: Long <- productOnboardingDAO.updateProductOnboardingDataTeams(
        productOnboardingDataTeams = productOnboardingDataTeams,
        orgId = orgId,
        tid = tid
      )
    } yield {
      List(productOnboardingDataOrg,productOnboardingDataTeams)
    }
  }

  def getProductOnboardingData(
                                orgId: OrgId,
                                tid: TeamId,
                                accountId:AccountId
                              ): Try[UpdateAndGetProductOnboardingData] = {

    for{
      productOnboardingData : UpdateAndGetProductOnboardingData <- productOnboardingDAO.getProductOnboardingData(
        orgId = orgId,
        tid = tid,
        accountId = accountId
      )

      productOnboardingFlags :  ProductOnboardingFlags <- productOnboardingDAO.getProductOnboardingFlags(
        orgId = orgId,
        tid = tid
      )

      useCaseFlags : UseCaseFlags <- Try {
          productOnboardingData.use_case_flags
      }

      productOnboardingDataWithUpdatedFlags : UpdateAndGetProductOnboardingData <- Try {
        UpdateAndGetProductOnboardingData(
          is_intro_completed = productOnboardingData.is_intro_completed,
          is_inbox_setup_completed = productOnboardingData.is_inbox_setup_completed || productOnboardingFlags.is_inbox_setup_done_flag,
          is_multi_channel_setup_completed = productOnboardingData.is_multi_channel_setup_completed || productOnboardingFlags.is_multichannel_setup_completed_flag,
          is_spam_test_done = productOnboardingData.is_spam_test_done || productOnboardingFlags.is_spam_test_done_flag,
          is_prospect_list_validated = productOnboardingData.is_prospect_list_validated || productOnboardingFlags.is_prospect_list_validated,
          is_warm_up_hero_setup_done = productOnboardingData.is_warm_up_hero_setup_done || productOnboardingFlags.is_warm_up_hero_enabled_flag,
          is_team_invite_sent = productOnboardingData.is_team_invite_sent || productOnboardingFlags.is_team_invite_sent_flag,
          is_installing_prospect_daddy_done = productOnboardingData.is_installing_prospect_daddy_done || productOnboardingFlags.is_prospect_daddy_setup_flag,
          is_email_finder_setup_done = productOnboardingData.is_email_finder_setup_done || productOnboardingFlags.is_email_finder_used,
          is_connect_crm_done = productOnboardingData.is_connect_crm_done || productOnboardingFlags.is_crm_connected_flag,
          is_zapier_integration_done = productOnboardingData.is_zapier_integration_done || productOnboardingFlags.is_zapier_integration_done,
          is_campaign_created_for_org = productOnboardingData.is_campaign_created_for_org || productOnboardingFlags.is_campaign_created_for_org,
            is_sender_email_added = productOnboardingData.is_sender_email_added || productOnboardingFlags.is_email_account_added,
            is_linkedin_added = productOnboardingData.is_linkedin_added || productOnboardingFlags.is_linkedin_account_added,
            is_calling_account_added =  productOnboardingData.is_calling_account_added || productOnboardingFlags.is_calling_account_added,
            is_campaign_not_started = productOnboardingData.is_campaign_not_started || productOnboardingFlags.is_campaign_not_started,
            is_whatsapp_account_added = productOnboardingData.is_whatsapp_account_added || productOnboardingFlags.is_whatsapp_account_added,
            use_case_flags = useCaseFlags,
            is_leadfinder_used = productOnboardingData.is_leadfinder_used || productOnboardingFlags.is_leadfinder_used
        )
      }
    } yield {
      productOnboardingDataWithUpdatedFlags
    }
  }

    def isChannelSetupComplete(settings: CampaignSettings, campaignStep: CampaignStepType): Boolean = {
        campaignStep match {
            case CampaignStepType.LinkedinInmail |
                 CampaignStepType.LinkedinMessage |
                 CampaignStepType.LinkedinViewProfile |
                 CampaignStepType.LinkedinConnectionRequest |
                 CampaignStepType.AutoLinkedinInmail |
                 CampaignStepType.AutoLinkedinMessage |
                 CampaignStepType.AutoLinkedinViewProfile |
                 CampaignStepType.AutoLinkedinConnectionRequest =>
                settings.campaign_linkedin_settings.nonEmpty
            case CampaignStepType.WhatsappMessage =>
                settings.campaign_whatsapp_settings.nonEmpty
            case CampaignStepType.SmsMessage =>
                settings.campaign_sms_settings.nonEmpty
            case CampaignStepType.CallStep =>
                settings.campaign_call_settings.nonEmpty
            case CampaignStepType.ManualEmailStep | CampaignStepType.AutoEmailStep
                 | CampaignStepType.AutoEmailMagicContent | CampaignStepType.ManualEmailMagicContent =>
                settings.campaign_email_settings.nonEmpty
            case CampaignStepType.GeneralTask | CampaignStepType.MoveToAnotherCampaignStep =>
                true
        }
    }

    def checkCampaignCreationFlow(campaignId: CampaignId,teamId: TeamId):Try[CampaignFlags] = {
        for {
            stepTypes: List[CampaignStepType] <- productOnboardingDAO.getCampaignStepTypes(campaignId)
            hasCampaignSteps: Boolean = if (stepTypes.isEmpty) {
                false
            } else {
                true
            }
            hasProspects: Boolean <- productOnboardingDAO.hasProspects(campaignId)
            channelSettings:Option[Campaign] = campaignService.findCampaignForCampaignUtilsOnly(id = campaignId.id, teamId = teamId)

            isChannelSetupDone:Boolean = if (stepTypes.isEmpty) {
                false
            } else {
                if (channelSettings.isDefined) {
                    stepTypes.forall(stepType => isChannelSetupComplete(channelSettings.get.settings, stepType))
                } else {
                    false
                }
            }

        } yield CampaignFlags(
            campaign_id = Some(campaignId.id),
            has_created_sequence = hasCampaignSteps,
            has_added_prospects = hasProspects,
            is_channel_setup_done = isChannelSetupDone
        )
    }

    def getCampaignFlags(teamId: TeamId,accountId: AccountId): Try[CampaignFlags] = {

         productOnboardingDAO.getLatestNotStartedCampaign(teamId,accountId).map{ campaignId =>


             checkCampaignCreationFlow(campaignId = campaignId.get,teamId = teamId)
            }.getOrElse(
             Success(CampaignFlags(
                 campaign_id = None,
                 has_created_sequence = false,
                 has_added_prospects = false,
                 is_channel_setup_done = false
             ))
         )
    }



    def getProductOnboardingDataWithCampaignFlags(orgId: OrgId,
                                                  tid: TeamId,
                                                  accountId:AccountId
                                                 ): Try[ProductOnboardingData] = {
        for{
            productOnboardingData:UpdateAndGetProductOnboardingData <- getProductOnboardingData(orgId = orgId, tid = tid, accountId = accountId)

            campaignFlags:CampaignFlags <- getCampaignFlags(teamId = tid,accountId = accountId)

            productOnboardingDataWithCampaignFlags:ProductOnboardingData <- Try {
                ProductOnboardingData(
                    onboarding_data = productOnboardingData, campaign_flags = campaignFlags
                )
            }
        } yield {
            productOnboardingDataWithCampaignFlags
        }
    }
}

