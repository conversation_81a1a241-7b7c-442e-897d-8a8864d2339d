package api.product_onboarding

import api.accounts.models.OrgId
import api.accounts.{AuthUtils, PermType, PermissionRequest, PermissionUtils, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.accounts.{AuthUtils, PermType, PermissionUtils, TeamId}
import api.product_onboarding.models.{ProductOnboardingData, UpdateAndGetProductOnboardingData}
import play.api.libs.json.{JsError, JsObject, JsSuccess, Json, Writes}
import play.api.mvc.{Action, AnyContent, BaseController, ControllerComponents}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class ProductOnBoardingController(
                                   protected val controllerComponents: ControllerComponents,
                                   permissionUtils: PermissionUtils,
                                   productOnboardingService: ProductOnboardingService,
                                   authUtils: AuthUtils
                                 ) extends BaseController {
  private implicit val ec: ExecutionContext = controllerComponents.executionContext

  def updateProductOnboardingData(
                                   v: String,
                                   tid: Option[Long]
                                 ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.JUST_LOGGEDIN,
    tidOpt = tid
  ).async(parse.json) { request =>
    val logger = request.Logger
    val res = request.Response
    val teamId = TeamId(id = request.actingTeamAccount.get.team_id)
    val orgId = OrgId(id = request.loggedinAccount.org.id)
    logger.info(s"updateProductOnboarding Data: ${request.body}")
    Future {
      request.body.validate[UpdateAndGetProductOnboardingData] match {
        case JsError(errors) =>
          res.JsValidationError(errors)

        case JsSuccess(value, _) =>
          productOnboardingService.updateProductOnboardingData(
            productOnboardingData = value,
            orgId = orgId,
            tid = teamId
          ) match {
            case Failure(err) =>
              res.ServerError("Could not update Product Onboarding Data", Some(err))
            case Success(value) =>
              res.Success("Successfully updated product Onboarding Data", Json.obj("success" -> true))
          }
      }
    }
  }

  def getProductOnboardingData(
                                v: String,
                                tid: Option[Long]
                              ): Action[AnyContent] = permissionUtils.checkPermission(
    version = v,
    permission = PermType.JUST_LOGGEDIN,
    tidOpt = tid
  ) { (request: PermissionRequest[AnyContent]) =>
    val logger = request.Logger
    val res = request.Response
    val teamId = TeamId(id = request.actingTeamAccount.get.team_id)
    val orgId = OrgId(id = request.loggedinAccount.org.id)
    val accountId = AccountId(request.loggedinAccount.internal_id)
    logger.info(s"getProductOnboarding Data: ${request.body}")
      productOnboardingService.getProductOnboardingDataWithCampaignFlags(
        orgId = orgId,
        tid = teamId,
          accountId = accountId
      ) match {
        case Failure(e) =>
          res.ServerError("Could not fetch Product Onboarding Data", Some(e))
        case Success(value) => {
          implicit val personWrites: Writes[ProductOnboardingData] = Json.writes[ProductOnboardingData]
          res.Success("Successfully fetched Product Onboarding data", Json.obj("product_onboarding_data" -> Json.toJson(value)))
        }
      }
    }
}
