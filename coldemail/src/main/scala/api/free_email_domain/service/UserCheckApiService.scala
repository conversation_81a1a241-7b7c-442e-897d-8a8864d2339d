package api.free_email_domain.service
import api.AppConfig
import play.api.libs.json._
import play.api.libs.ws.WSClient
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}

// Define a case class for the 'data' field
case class UserCheckData(status:Int,
                          domain: String,
                         mx: Boolean,
                         disposable: Boolean,
                         relay_domain: <PERSON>olean,
                         public_domain: Boolean,
                         spam: Option[<PERSON><PERSON><PERSON>],
                         did_you_mean: Option[String]) // Add other fields as needed

object UserCheckData {
    given format: OFormat[UserCheckData] = Json.format[UserCheckData]
}

// Define the response class
case class UserCheckResponse(status: Int, data: UserCheckData)

object UserCheckResponse {
    given format: OFormat[UserCheckResponse] = Json.format[UserCheckResponse]
}


class UserCheckApiService {

    def userCheckDomainCheck(
                      domain:String
                    )(implicit ws: WSClient,
                      ec:ExecutionContext,
                      logger:SRLogger): Future[UserCheckData] = {
        val url = s"https://api.usercheck.com/domain/${domain}"

        /* 19-Mar-2025 :
        Added the Bearer  before the actual key here as specified int he usercheck api documentation
        rate limit error was occuring beacuse the authorization was failing and considering the reuqest as an unauthorized request
        for unauthorized the limit is 5 per hour
        adding bearer will fix it tested it via testapp
        "Authorization" -> s"Bearer ${AppConfig.userCheckApiKey}"

         */
        ws
          .url(url)
          .withHttpHeaders(
              "Content-Type" -> "application/json",
              "Authorization" -> s"Bearer ${AppConfig.userCheckApiKey}"
          )
          .get()
          .flatMap { response =>
              response.status match {
                  case 200 =>
                      Future.successful(response.json.as[UserCheckData])
                  case _   =>
                      logger.shouldNeverHappen(s"UserCheckApiService :: userCheckDomainCheck error :: ${response.body}")
                      Future.failed(new Exception("Error while checking Domain"))
              }
          }
    }

}
