package api.free_email_domain.dao

import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import utils.Helpers

import scala.util.Try

class FreeEmailDomainListDAO{



  def checkIfDomainIsFree(domain:String): Try[Boolean] = Try{
    DB.readOnly{implicit session =>
      sql"""
           SELECT EXISTS(
           SELECT *
           FROM free_email_service_domains
           WHERE domain_name = ${Helpers.trimToLower(domain)}
           )
         """
        .map(_.boolean("exists"))
        .single
        .apply()
        .get
    }
  }

  def checkIfWhiteListedForSignUp(email: String): Try[Boolean] = Try{
    DB.readOnly{implicit session =>
      sql"""
          SELECT EXISTS(
          SELECT *
            FROM whitelisted_free_email_allowed_for_signup
           WHERE lower(email) = ${Helpers.trimToLower(email)}
          )
         """
        .map(_.boolean("exists"))
        .single
        .apply()
        .get
    }
  }


  def addDomainToFree(domain: String): Try[Option[Long]] = Try{
    DB.localTx{implicit session =>
      sql"""
           INSERT INTO free_email_service_domains (domain_name)
           VALUES(${Helpers.trimToLower(domain)})
           RETURNING id;
      """
        .map(_.long("id"))
        .single
        .apply()
    }
  }

  def addEmailToWhileList(email: String): Try[Option[Long]] = Try{
    DB.localTx{implicit session =>
      sql"""
           INSERT INTO whitelisted_free_email_allowed_for_signup (email)
           VALUES(${Helpers.trimToLower(email)})
         RETURNING id;
      """
        .map(_.long("id"))
        .single
        .apply()
    }
  }

  def getAllInFreeList: Try[Set[String]] = Try{

    DB readOnly{implicit session =>
      sql"""
           SELECT domain_name
           FROM free_email_service_domains;
         """
        .map(_.string("domain_name"))
        .list
        .apply()
        .toSet
    }
  }


}
