package api.spamtest

import api.accounts.TeamId
import org.joda.time.DateTime
import org.postgresql.util.PGobject
import play.api.libs.json.{Format, JsError, JsResult, JsString, JsSuccess, JsV<PERSON>ue, <PERSON><PERSON>, <PERSON><PERSON>}
import play.api.libs.json.JodaWrites._
import scalikejdbc._
import scalikejdbc.jodatime.JodaWrappedResultSet._
import sr_scheduler.CampaignStatus
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus

import scala.util.{Failure, Success, Try}


object SpamTestType extends Enumeration {
  type SpamTestType = Value
  val AUTH = Value("auth") // via MT
  /***
  *** Commenting on Date:1/10/2022 ***
  when we were working on solving the empty response from mailtester issue,we decided to remove DELIVERABILITY Usage
  as all the spamTests is performed by MailTester and not EOA,
    ***/

//  val DELIVERABILITY = Value("deliverability") // via EOA
}

//FIXME : value class can be used for campaignID,step_id
case class CreateSpamTest(
  test_name: String,

  test_type: SpamTestType.Value,

  email_settings_id: Long,
  email: String,

  campaign_id: Long,
  step_id: Long,
  email_domain: String


)


case class UpdateSpamTest(

  body: String,
  subject: String,

  mt_id: Option[String],
  mt_created_at: Option[DateTime]

)

case class SpamTestNotificationData(
  first_name: String,
  last_name: String,
  email: String,
  campaign_status: CampaignStatus,
  account_id: Long,
  team_id: Long
)

case class SpamTest(
  id: Long,
  test_name: String,
  test_type: SpamTestType.Value,

  email_settings_id: Long,
  email: String,

  campaign_id: Option[Long],
  step_id: Option[Long],

  body: Option[String],
  subject: Option[String],

  mt_id: Option[String],
  mt_results: JsValue,
  mt_results_last_checked_at: Option[DateTime],
  mt_created_at: DateTime,
  error: Option[String],
  created_at: DateTime,
  teamId: TeamId

)
object SpamTest{
  implicit val writes: Writes[SpamTest] = new Writes[SpamTest] {
    def writes(e: SpamTest) = {

      //      val status = if (e.eoa_id.isEmpty || e.mt_id.isEmpty) "not_started"
      //      else if (e.eoa_results_last_checked_at.isEmpty || e.mt_results_last_checked_at.isEmpty) "testing"
      //      else "completed"
      //
      val status:SpamTestStatusType =
        if(e.error.isDefined) SpamTestStatusType.ERROR
        else if (e.mt_id.isEmpty) SpamTestStatusType.NOT_STARTED
        else if (e.mt_results_last_checked_at.isEmpty) SpamTestStatusType.TESTING
        else SpamTestStatusType.COMPLETED




      Json.obj(
        "id" -> e.id,
        "test_type" -> e.test_type.toString,

        "email" -> e.email,
        "email_settings_id" -> e.email_settings_id,
        "status" -> SpamTestStatusType.toKey(status),
        "settings_results" -> e.mt_results,
        "error" -> e.error,
        "created_at" -> e.created_at

      )
    }
  }


  implicit val session: AutoSession.type = AutoSession

  def fromDb(rs: WrappedResultSet, teamId: TeamId): SpamTest = {


    SpamTest(
      id = rs.long("id"),
      test_name = rs.string("test_name"),
      test_type = SpamTestType.withName(rs.string("test_type")),
      email_settings_id = rs.long("email_settings_id"),
      email = rs.string("email"),
      campaign_id = rs.longOpt("campaign_id"),
      step_id = rs.longOpt("step_id"),
      body = rs.stringOpt("body"),
      subject = rs.stringOpt("subject"),
      mt_id = rs.stringOpt("mt_id"),
      mt_results = Json.parse(rs.any("mt_results").asInstanceOf[PGobject].getValue),
      mt_results_last_checked_at = rs.jodaDateTimeOpt("mt_results_last_checked_at"),
      mt_created_at = rs.jodaDateTime("mt_created_at"),
      error = rs.stringOpt("error"),
      created_at = rs.jodaDateTime("created_at"),
      teamId = teamId
    )
  }


}
sealed  trait  SpamTestStatusType
object SpamTestStatusType {

    private val notStarted = "not_started"
    private val testing = "testing"
    private val completed = "completed"
    private val error = "error"

    case object NOT_STARTED extends SpamTestStatusType {
        override def toString: String = notStarted
    }

    case object TESTING extends SpamTestStatusType {
        override def toString: String = testing
    }

    case object COMPLETED extends SpamTestStatusType {
        override def toString: String = completed
    }

    case object ERROR extends SpamTestStatusType {
        override def toString: String = error
    }

    def toKey(spamTestStatusType: SpamTestStatusType): String = spamTestStatusType.toString

    def fromString(str: String): Try[SpamTestStatusType] = Try {
        str match {
            case `notStarted` => NOT_STARTED
            case `testing` => TESTING
            case `completed` => COMPLETED
            case `error` => ERROR
        }
    }
    given format: Format[SpamTestStatusType] = new Format[SpamTestStatusType] {
        override def writes(o: SpamTestStatusType): JsValue = {
            JsString(o.toString)
        }

        override def reads(json: JsValue): JsResult[SpamTestStatusType] = {
            fromString(json.as[String]) match {
                case Failure(err) => JsError(err.getMessage)
                case Success(t) => JsSuccess(t)
            }
        }
    }


}

case class DomainSpamTestResult (
      domain : String,
      lastSpamTestRanAt :Option[DateTime],
      status : SpamTestStatusType

   )


class SpamTestDAO() {
  // ignore oauth2 credentials while returning account as JSON


  def findAll(cid: Long): Option[SpamTest] = DB readOnly { implicit session =>

    sql"""
         SELECT st.*, c.team_id
         FROM spam_tests st
         inner join campaigns c on c.id = st.campaign_id
         WHERE st.campaign_id = $cid
         ORDER BY created_at DESC
         LIMIT 1"""
      .map(rs => SpamTest.fromDb(rs = rs, teamId = TeamId(rs.long("team_id"))))
      .single
      .apply()

  }

    /*
    11 July 2024 : Not added the balcklist check here as we are not pausing the spam test check for the domain on blacklist reason
     */
  def findOneForSendingTests(): Option[SpamTest] = DB readOnly { implicit session =>

    /*
    NOTE: email on acid is no longer used
    sql"""SELECT *
         FROM spam_tests
         INNER JOIN email_settings ses ON ses.id = spam_tests.email_settings_id
         WHERE campaign_id IS NOT NULL
         AND (ses.paused_till IS NULL OR ses.paused_till < now())
         AND (

            (eoa_id IS NULL AND test_type = ${SpamTestType.DELIVERABILITY.toString})
            OR

            (mt_id IS NULL AND test_type = ${SpamTestType.AUTH.toString})
            )
         ORDER BY spam_tests.created_at ASC
         LIMIT 1"""
    */

    sql"""SELECT spam_tests.*, ses.team_id
         FROM spam_tests
         INNER JOIN email_settings ses ON ses.id = spam_tests.email_settings_id
         WHERE campaign_id IS NOT NULL
         AND (ses.paused_till IS NULL OR ses.paused_till < now())
         AND (mt_id IS NULL AND test_type = ${SpamTestType.AUTH.toString})
         AND spam_tests.error IS NULL
         and ses.status = ${EmailSettingStatus.Active.toString}
         ORDER BY spam_tests.created_at ASC
         LIMIT 1"""
      .map(rs => SpamTest.fromDb(rs = rs, teamId = TeamId(rs.long("team_id"))))
      .single
      .apply()

  }

  def campaignHasRunningTest(campaignId: Long): Boolean = DB readOnly { implicit session =>
    sql"""
    SELECT EXISTS (SELECT 1 FROM spam_tests where campaign_id = $campaignId and created_at > now() - interval '15 minutes')
"""
      .map(rs => rs.boolean("exists"))
      .headOption
      .apply()
      .get

  }

  def create(data: CreateSpamTest, teamId: TeamId): Try[SpamTest] = Try {
    DB autoCommit { implicit session =>

      sql"""
          INSERT INTO spam_tests
          (
           test_name,
           test_type,
           email_settings_id,
           email,
           campaign_id,
           step_id,
           mt_results,
           team_id,
           email_domain

          )
          VALUES (
            ${data.test_name},
            ${data.test_type.toString},
            ${data.email_settings_id},
            ${data.email},
            ${data.campaign_id},
            ${data.step_id},
            ${Json.obj().toString()}::jsonb,
            ${teamId.id},
            ${data.email_domain.trim.toLowerCase}

          )
          RETURNING *;
        """
        .map(rs => SpamTest.fromDb(rs = rs, teamId = teamId))
        .single
        .apply()
        .get

    }
  }


  def update(spamTestId: Long, data: UpdateSpamTest, team_id: TeamId): Try[Option[SpamTest]] = Try {
    DB autoCommit { implicit session =>
      sql"""
          UPDATE spam_tests SET

           body = ${data.body},
           subject = ${data.subject},


           mt_id = ${data.mt_id},
           mt_created_at = ${data.mt_created_at}

          WHERE id = $spamTestId
          RETURNING *;
        """
        .map(rs => SpamTest.fromDb(rs = rs, teamId = team_id))
        .single
        .apply()

    }
  }

  def findOneForCheckingResults(): Option[SpamTest] = DB readOnly { implicit session =>

    /*
    sql"""

      SELECT *
        FROM spam_tests
        WHERE
        eoa_id IS NOT NULL
        AND mt_id IS NOT NULL
        AND campaign_id IS NOT NULL
        AND step_id IS NOT NULL

        AND

        (
          eoa_created_at > now() - interval '1 day'
          OR mt_created_at > now() - interval '1 day'
        )

        AND
        (
            eoa_results_last_checked_at IS NULL
            OR mt_results_last_checked_at IS NULL
            OR (
              eoa_results_last_checked_at < now() - interval '30 minutes'
              AND eoa_results_last_checked_at > now() - interval '1 day'
            )
            OR (
              mt_results_last_checked_at < now() - interval '30 minutes'
              AND mt_results_last_checked_at > now() - interval '1 day'
            )
        )

        ORDER BY created_at DESC
        LIMIT 1;


      """
    */

    /*
    sql"""
      SELECT *
      FROM spam_tests
      WHERE

      campaign_id IS NOT NULL
      AND step_id IS NOT NULL


      AND (

        (
          test_type = 'deliverability'
          AND eoa_id IS NOT NULL
          AND eoa_created_at > now() - interval '1 day'
          AND (

            eoa_results_last_checked_at IS NULL
            OR (
              eoa_results_last_checked_at < now() - interval '30 minutes'
              AND eoa_results_last_checked_at > now() - interval '1 day'
            )
          )
        )

        OR

        (

          test_type = 'auth'
          AND mt_id IS NOT NULL
          AND mt_created_at > now() - interval '1 day'
          AND (
            mt_results_last_checked_at IS NULL

            OR (
              mt_results_last_checked_at < now() - interval '30 minutes'
              AND mt_results_last_checked_at > now() - interval '1 day'
            )
          )

        )

      )

      ORDER BY created_at DESC
      LIMIT 1
    """
    */


    sql"""
      SELECT spam_tests.*, c.team_id
      FROM spam_tests
      inner join campaigns c on c.id = spam_tests.campaign_id
      WHERE

        campaign_id IS NOT NULL
        AND step_id IS NOT NULL


        AND test_type = 'auth'
        AND mt_id IS NOT NULL
        AND mt_created_at > now() - interval '1 day'
        AND mt_created_at < now() - interval '15 minutes'
        AND mt_results = '{}'::jsonb
        AND mt_results_last_checked_at IS NULL
        AND error IS NULL

      ORDER BY created_at DESC
      LIMIT 1
    """


      .map(rs => SpamTest.fromDb(rs = rs, teamId = TeamId(rs.long("team_id"))))
      .single
      .apply()

  }

  def updateResults(id: Long, mtResults: JsValue, team_id: TeamId): Try[Option[SpamTest]] = Try {

    DB autoCommit { implicit session =>


      sql"""
          UPDATE spam_tests
          SET

            mt_results = regexp_replace(${mtResults.toString()}, '\\u0000', '', 'g')::jsonb,
            mt_results_last_checked_at = now()


          WHERE id = $id
          RETURNING *;
        """
        .map(rs => SpamTest.fromDb(rs = rs, teamId = team_id))
        .single
        .apply()

    }

  }

  def updateError(id: Long, errorMsg: String): Try[Int] = Try {

    DB autoCommit { implicit session =>

      sql"""
          UPDATE spam_tests
          SET
            error = $errorMsg,
            error_at = now()
          WHERE id = $id;
        """
        .update
        .apply()

    }

  }


  def getSpamTestsDoneInCurrentCycle(orgId: Long): Int = DB readOnly { implicit session =>

    sql"""
    SELECT count(*) FROM spam_tests st
      JOIN email_settings eset on st.email_settings_id = eset.id
    JOIN teams t on eset.team_id = t.id
    JOIN organizations org on org.id = t.org_id
    WHERE t.org_id = $orgId
      AND st.created_at > org.current_cycle_started_at
      and eset.status = ${EmailSettingStatus.Active.toString}
    ;
    """
      .map(rs => rs.int("count"))
      .headOption
      .apply()
      .get
  }


  def getSpamTestNotificationData(campaignId: Long): Option[SpamTestNotificationData] = DB readOnly { implicit session =>

    sql"""
         SELECT
          a.first_name,
          a.last_name,
          a.email,
          c.status,
          a.id as account_id,
          c.team_id as team_id
         FROM accounts a
         INNER JOIN campaigns c ON a.id = c.account_id WHERE c.id = ${campaignId}
      """
      //.map(rs => (rs.string("first_name"), rs.string("last_name"), CampaignStatus.withName(rs.string("status"))))
      .map(rs => SpamTestNotificationData(
      first_name = rs.string("first_name"),
      last_name = rs.string("last_name"),
      email = rs.string("email"),
      campaign_status = CampaignStatus.fromKey(rs.string("status")).get,
      account_id = rs.long("account_id"),
      team_id = rs.long("team_id")
    )
    )
      .single
      .apply()
  }

    def getLatestSenderDomainSpamTestRanAt(domainListToCheckTheLatestSpamTest : List[String],
                                           teamId: TeamId
                                          ):Try[List[DomainSpamTestResult]] = Try {
        DB readOnly { implicit session =>

         val domainResult = sql"""
                 SELECT email_domain,mt_results_last_checked_at,error,error_at
                  FROM (
                            SELECT email_domain,mt_results_last_checked_at,error,error_at,
                                   ROW_NUMBER() OVER (PARTITION BY email_domain ORDER BY id DESC) as rn
                            FROM spam_tests
                            WHERE email_domain in ($domainListToCheckTheLatestSpamTest) AND team_id = ${teamId.id}
                            AND created_at > now() - INTERVAL '1 year'
                            AND campaign_id IS NOT NULL
                            AND step_id IS NOT NULL

                        ) as subquery
                  WHERE rn =1
               """
              .map { rs =>

                val errorAt = rs.jodaDateTimeOpt("error_at")
                val domain = rs.string("email_domain")
                val lastSpamTestDone = rs.jodaDateTimeOpt("mt_results_last_checked_at")
                val error = rs.stringOpt("error")
                  DomainSpamTestResult(
                      domain = domain,
                      lastSpamTestRanAt = if(lastSpamTestDone.isDefined){
                          lastSpamTestDone
                      }else if(errorAt.isDefined){
                         errorAt
                      }else{
                          None
                      },
                      status = if(error.isDefined){
                                    SpamTestStatusType.ERROR
                                }else{
                                    lastSpamTestDone match {
                                        case None =>
                                            SpamTestStatusType.TESTING

                                        case Some(value) =>
                                            SpamTestStatusType.COMPLETED
                                    }
                                }
                  )

              }
              .list
              .apply()

            domainListToCheckTheLatestSpamTest.map{ domain =>
                domainResult.find(_.domain == domain)
                  .getOrElse(DomainSpamTestResult(
                      domain = domain,
                      lastSpamTestRanAt = None,
                      status = SpamTestStatusType.NOT_STARTED
                  ))

            }

        }
    }


    def findSpamTestViaDomain(domain: String,teamId: TeamId): Try[Option[SpamTest]] = Try {

        DB readOnly { implicit session =>

            sql"""
         SELECT st.*
         FROM spam_tests st
         WHERE  st.email_domain = $domain
         AND st.mt_results_last_checked_at IS NOT NULL
         AND st.team_id = ${teamId.id}
         ORDER BY id DESC
         LIMIT 1"""
              .map(rs => SpamTest.fromDb(rs = rs, teamId = TeamId(teamId.id)))
              .single
              .apply()

        }
    }




}
