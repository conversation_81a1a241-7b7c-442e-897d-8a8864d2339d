package api.spamtest

import api.AppConfig
import api.accounts.TeamId
import api.accounts.email.models.{EmailProvidedBy, EmailServiceProvider}
import api.accounts.models.OrgId
import io.sr.billing_common.models.PlanID
import api.campaigns.CampaignStepDAO
import api.campaigns.dao.InboxPlacementCheckDAO
import api.campaigns.services.CampaignId
import api.domain_health.{DomainBlacklistRecord, DomainHealthCheckDAO}
import api.emails.{EmailSetting, EmailSettingDAO}
import api.emails.models.{DomainProvider, EmailSettingDomain}
import api.spamtest.{CreateSpamTest, SpamTest, SpamTestDAO, SpamTestType}
import api.prospects.dao_service.ProspectDAOService
import app_services.blacklist_monitoring.models.BlacklistCheckStatus
import org.joda.time.DateTime
import play.api.libs.json.*
import play.api.libs.json.JodaWrites.*
import utils.SRLogger
import utils.emailvalidation.EmailValidationService

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future
import scala.util.{Failure, Success, Try}

sealed trait CreateSpamTestError

object CreateSpamTestError {

  object PlanIdInActive extends CreateSpamTestError

  case object TestTypeNotFound extends CreateSpamTestError

  case object MaxSpamTestLimitReached extends CreateSpamTestError

  case object SpamTestRunningError extends CreateSpamTestError

  case object EmptySenderEmailAccount extends CreateSpamTestError

  case object EmptySteps extends CreateSpamTestError

  case object NoProspectAdded extends CreateSpamTestError

  case class SQLException(err: Throwable) extends CreateSpamTestError

}

sealed trait GetSpamTestServiceError

object GetSpamTestServiceError {
  case class FetchException(err: Throwable) extends GetSpamTestServiceError
}

case class SpamTestReportResponse(
                                   spam_test: Option[Seq[SpamTest]],
                                   service_provider: String,
                                   max_spam_tests: Int,
                                   current_spam_tests_done_count: Int
                                 )

object SpamTestReportResponse {

  implicit val writes: Writes[SpamTestReportResponse] = Json.writes[SpamTestReportResponse]
}


sealed trait GetDomainSpamTestError

object GetDomainSpamTestError {

  case object NoSenderEmails extends GetDomainSpamTestError

  case class DomainFetchException(err: Throwable) extends GetDomainSpamTestError

  case class ErrorWhileFetchingInboxPlacementData(err: Throwable) extends GetDomainSpamTestError
}

case class SingleDomainSpamTestLatestResponse(
                                               domain: String,
                                               last_spam_test_ran_at: Option[DateTime],
                                               status: SpamTestStatusType,
                                               blacklist_status: Option[BlacklistCheckStatus],
                                               last_inbp_analyzed_at: Option[DateTime],
                                               email_associated_with_domain: List[String]
                                             )

object SingleDomainSpamTestLatestResponse {
  implicit val writes: Writes[SingleDomainSpamTestLatestResponse] = Json.writes[SingleDomainSpamTestLatestResponse]
}

case class AllSenderDomainSpamTestResponse(
                                            campaign_sender_email_spam_test: List[SingleDomainSpamTestLatestResponse],
                                            max_spam_tests: Int,
                                            current_spam_tests_done_count: Int
                                          )

object AllSenderDomainSpamTestResponse {
  implicit val writes: Writes[AllSenderDomainSpamTestResponse] = Json.writes[AllSenderDomainSpamTestResponse]
}

case class DomainSpamTestReportResponse(
                                         spam_test: Option[SpamTest],
                                         service_provider: EmailServiceProvider,
                                         domain_provider: Option[EmailProvidedBy]
                                       )

object DomainSpamTestReportResponse {
  implicit val writes: Writes[DomainSpamTestReportResponse] = Json.writes[DomainSpamTestReportResponse]
}

class SpamTestService(
                       emailSettingDAO: EmailSettingDAO,
                       prospectDAOService: ProspectDAOService,
                       campaignStepDAO: CampaignStepDAO,
                       spamTestDAO: SpamTestDAO,
                       inboxPlacementCheckDAO: InboxPlacementCheckDAO,
                       domainHealthCheckDAO: DomainHealthCheckDAO
                     ) {

  def createSpamTest(
                      planID: PlanID,
                      test_type: String,
                      maxSpamTests: Int,
                      org_Id: Long,
                      sender_email_settings_id: Option[Int],
                      head_step_id: Option[Long],
                      campaign_id: Long,
                      team_id: Long,
                      logger: SRLogger,
                      isAfterDomainChanges: Boolean
                    ): Either[CreateSpamTestError, SpamTest] = {

    given srLogger: SRLogger = logger // fixme given

    if (planID == PlanID.INACTIVE) {

      Left(CreateSpamTestError.PlanIdInActive)

    }
    else if (test_type != "deliverability" && test_type != "auth") {

      Left(CreateSpamTestError.TestTypeNotFound)

    }
    else {
      val spamTestsDoneInLastMonth = spamTestDAO.getSpamTestsDoneInCurrentCycle(org_Id)
      if (spamTestsDoneInLastMonth >= maxSpamTests) {

        Left(CreateSpamTestError.MaxSpamTestLimitReached)

      }
      else {

        val testType: SpamTestType.Value = SpamTestType.AUTH


        val headStepId = head_step_id
        val senderEmailSettingId = sender_email_settings_id
        val campaignHasRunningTest = spamTestDAO.campaignHasRunningTest(campaign_id)
        if (campaignHasRunningTest && !isAfterDomainChanges) {

          Left(CreateSpamTestError.SpamTestRunningError)

        } else if (senderEmailSettingId.isEmpty) {

          Left(CreateSpamTestError.EmptySenderEmailAccount)

        } else if (headStepId.isEmpty) {

          Left(CreateSpamTestError.EmptySteps)

        }
        else {
          val es = emailSettingDAO.find(senderEmailSettingId.get).get
          val step = campaignStepDAO.findById(stepId = headStepId.get, campaignId = campaign_id).get
          val prospectOpt = prospectDAOService.findOneByCampaignId(
            campaignId = campaign_id,
            teamId = team_id,
            Logger = logger
          )
          if (prospectOpt.isEmpty) {

            Left(CreateSpamTestError.NoProspectAdded)

          }
          else {
            val mtUsername = AppConfig.mailtesterUsername
            val testName = s"$mtUsername-${es.id.get.emailSettingId}_${DateTime.now().getMillis}"
            val email_domain: String = EmailValidationService.getLowercasedNameAndDomainFromEmail(email = es.email)._2
            val newSpamTest = CreateSpamTest(
              test_name = testName,
              test_type = SpamTestType.AUTH,
              email_settings_id = es.id.get.emailSettingId, // FIXME VALUECLASS
              email = es.email,
              campaign_id = campaign_id,
              step_id = step.id,
              email_domain = email_domain
            )
            spamTestDAO.create(newSpamTest, teamId = TeamId(team_id)) match {
              case Failure(e) => {
                Left(CreateSpamTestError.SQLException(e))
              }
              case Success(spamTest) =>
                Right(spamTest);
            }

          }

        }


      }

    }
  }

  def getSpamTestReport(
                         id: Long,
                         maxSpamTests: Int,
                         org_Id: Long
                       ): Future[Either[GetSpamTestServiceError, SpamTestReportResponse]] = {
    val spamTestFut = Future {
      spamTestDAO.findAll(cid = id)
    }

    val stDoneCountFut = Future {
      spamTestDAO.getSpamTestsDoneInCurrentCycle(orgId = org_Id)
    }


    val resFut = for {
      spamTest <- spamTestFut
      currentSTCount <- stDoneCountFut
    } yield {

      (spamTest, currentSTCount)

    }
    resFut
      .map { case (spamTest, currentSTCount) =>

        if (spamTest.isDefined) {

          val service_provider = emailSettingDAO.find(spamTest.get.email_settings_id).get.service_provider.toString


          Right(SpamTestReportResponse(
            spam_test = Some(Seq(spamTest.get)),
            service_provider = service_provider,
            max_spam_tests = maxSpamTests,
            current_spam_tests_done_count = currentSTCount
          ))

        } else {

          Right(SpamTestReportResponse(
            spam_test = None,
            service_provider = "",
            max_spam_tests = maxSpamTests,
            current_spam_tests_done_count = currentSTCount
          ))
        }
      }
      .recover { case e =>
        Left(GetSpamTestServiceError.FetchException(e))
      }

  }

  def getDomainLatestCheckRecord(
                                  campaignId: Option[CampaignId],
                                  orgId: OrgId,
                                  teamId: TeamId,
                                  maxSpamTests: Int,
                                  logger: SRLogger
                                ): Either[GetDomainSpamTestError, AllSenderDomainSpamTestResponse] = {

    given srLogger: SRLogger = logger // fixme given
    val currentSpamTestsDoneCount = spamTestDAO.getSpamTestsDoneInCurrentCycle(orgId.id)

    for {
      senderEmailList <- emailSettingDAO.getSenderEmails(teamId = teamId, campaignId = campaignId).toEither
        .left.map(ex => GetDomainSpamTestError.DomainFetchException(ex))
      _ <- if (senderEmailList.isEmpty) {
        logger.info(s"No Sender Emails Found for Campaign with id ${campaignId} and team_id : ${teamId.id}")
        Left(GetDomainSpamTestError.NoSenderEmails)
      } else Right(())

      campaignSenderDomainList = senderEmailList.map { email =>
        EmailValidationService.getLowercasedNameAndDomainFromEmail(email)._2
      }.distinct

      campaignSenderDomainSpamTestResults <- spamTestDAO.getLatestSenderDomainSpamTestRanAt(
        domainListToCheckTheLatestSpamTest = campaignSenderDomainList,
        teamId = teamId
      ).toEither.left.map(ex => GetDomainSpamTestError.DomainFetchException(ex))

      domainBlacklistResults <- domainHealthCheckDAO.getBlacklistResultOfDomain(
        domainList = campaignSenderDomainList,
        teamId = teamId
      ).toEither.left.map(ex => GetDomainSpamTestError.DomainFetchException(ex))

      domainBlacklistResultsWithDefault: List[DomainBlacklistRecord] =
        campaignSenderDomainList.map { domain =>
          domainBlacklistResults.find(_.domain == domain)
            .getOrElse(DomainBlacklistRecord(
              domain = domain,
              domain_blacklist_status = BlacklistCheckStatus.NOT_FOUND
            ))
        }

      // List[EmailDomainAnalysedAt]
      inbox_placement_checks <- if (AppConfig.RollingUpdates.getShowInbpLogsReport(orgId)) {
        inboxPlacementCheckDAO.getDomainsLastAnalyzedAt(
          domains = campaignSenderDomainList.map(EmailSettingDomain(_)),
          teamId = teamId
        ).toEither.left.map(ex => GetDomainSpamTestError.ErrorWhileFetchingInboxPlacementData(ex))
      } else Right(List())

      response = campaignSenderDomainSpamTestResults.map { domainSpamTest =>
        /*
        Problem : We are storing the domain in a lowercase form and the emails
        are stored in the same as the user has given so condition occurs when
        domain is in lowercase in backend but in email the domain has some
        letters to be capital in this case the filter might fail

        Example : For an email "<EMAIL>" in this case while storing in BE
         we will store domain as "example.com" and while filtering the emails
         with the domains the "<EMAIL>" will not match with the domain
         "example.com"

        Solution : So, during filtering i am matching it with lowercase of the email
        If we consider the above problem , so now "<EMAIL>" will be converted to
        "<EMAIL>" and the check will happen and now the filter will work fine
         */
        val emailAssociatedWithDomain = senderEmailList.filter(e => e.toLowerCase.endsWith(s"${domainSpamTest.domain}"))
        val domainBlacklistResult: Option[BlacklistCheckStatus] = if (campaignId.isDefined) {
          None
        } else {
          Option(domainBlacklistResultsWithDefault.find(_.domain == domainSpamTest.domain).get.domain_blacklist_status)
        }

        val inbp_analyzed_at: Option[DateTime] = inbox_placement_checks
          .find(_.emailDomain.email_domain == domainSpamTest.domain)
          .map(_.analyzed_at)

        SingleDomainSpamTestLatestResponse(
          domain = domainSpamTest.domain,
          last_spam_test_ran_at = domainSpamTest.lastSpamTestRanAt,
          status = domainSpamTest.status,
          blacklist_status = domainBlacklistResult,
          last_inbp_analyzed_at = inbp_analyzed_at,
          email_associated_with_domain = emailAssociatedWithDomain
        )
      }
    } yield AllSenderDomainSpamTestResponse(
      campaign_sender_email_spam_test = response,
      max_spam_tests = maxSpamTests,
      current_spam_tests_done_count = currentSpamTestsDoneCount
    )
  }


  def getSpecificDomainSpamTestReport(
                                       domain: String,
                                       teamId: TeamId
                                     ): Either[GetSpamTestServiceError, DomainSpamTestReportResponse] = {

    spamTestDAO.findSpamTestViaDomain(domain = domain, teamId = teamId) match {

      case Success(spamTestReport) =>

          val spamTestEmailSetting: EmailSetting = emailSettingDAO.find(spamTestReport.get.email_settings_id).get
        val service_provider:EmailServiceProvider = spamTestEmailSetting.service_provider
        val domain_provider:Option[EmailProvidedBy] = spamTestEmailSetting.domain_provider

        Right(DomainSpamTestReportResponse(
          spam_test = spamTestReport,
          service_provider = service_provider,
          domain_provider = domain_provider
        ))

      case Failure(exception) =>

        Left(GetSpamTestServiceError.FetchException(exception))

    }

  }


}
