package api.blacklist

import api.APIErrorResponse.{<PERSON>rrorResponse, ErrorResponseCreateUpdateBlacklistApi, ErrorResponseV3}
import api.accounts.*
import api.accounts.models.{AccountId, OrgId}
import api.blacklist.BlacklistResource.{BlacklistCreateV3ApiResponse, BlacklistDeleteApiResponse, BlacklistV3}
import api.blacklist.models.{BlacklistCreateOrDeleteApiLevel, BlacklistFindApiLevel, DoNotContactType}
import api.prospects.{CsvQueue, CsvQueueCreateFormDataV2, InferredQueryTimeline}
import api.{ApiService, ApiVersion, BadRequestErrorException, ErrorType}
import api.blacklist.service.BlacklistErrorType
import api.migration_utils.MigrateProspectEmails.logger
import org.joda.time.DateTime
import play.api.libs.Files.SingletonTemporaryFileCreator
import play.api.libs.json.*
import play.api.mvc.{Action, AnyContent, BaseController, ControllerComponents}
import utils.{Help<PERSON>, SRLogger, StringUtilsV2}
import utils.helpers.LogHelpers

import java.io.File
import java.net.URLEncoder
import com.github.tototoshi.csv.CSVWriter
import io.lemonlabs.uri.Url
import utils.uuid.SrIdentifier

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import com.github.tototoshi.csv.defaultCSVFormat
import utils.mq.do_not_contact.{MQDoNotContactConsumer, MQDoNotContactMessage, MQDoNotContactPublisher}

case class BlacklistListingAPIParams(
                                      range: InferredQueryTimeline.Range,
                                      level: BlacklistFindApiLevel,
                                      is_first: Boolean
                                   )

class BlacklistController(
                           protected val controllerComponents: ControllerComponents,
                           mqDoNotContactPublisher: MQDoNotContactPublisher,
                           blacklistDAO: BlacklistDAO,
                           blacklistService: BlacklistService,
                           authUtils: AuthUtils,
                           permissionUtils: PermissionUtils,
                           blacklistListingPaginationService: BlacklistListingPaginationService,
                           apiService: ApiService
) extends BaseController {

  private implicit val ec: ExecutionContext = controllerComponents.executionContext

  private def fromAPIInputParam(

    level: Option[String],
    org_id: OrgId,
    team_id: Option[TeamId],
    ta_id: Option[Long]

  )(using logger: SRLogger): Try[BlacklistFindApiLevel] = {

    if (level.isEmpty || level.get == "team") {

      if (team_id.isEmpty || ta_id.isEmpty) {

        logger.shouldNeverHappen("team_id is missing, level.isEmpty")

        Failure(new Exception("team_id is missing"))

      } else {

        Success(
          BlacklistFindApiLevel.Team(
            team_id = team_id.get,
            org_id = org_id,
            ta_id = ta_id.get
          )
        )
      }


    } else if (level.get == "all") {

      if (team_id.isEmpty) {

        logger.shouldNeverHappen("team_id is missing, level == 'all'")

        Failure(new Exception("team_id is missing"))

      } else {

        Success(
          BlacklistFindApiLevel.All(
            team_id = team_id.get,
            org_id = org_id
          )
        )

      }

    }
    else if (level.get == "global") {

      Success(
        BlacklistFindApiLevel.Global(
          org_id = org_id
        )
      )
    }
    else {

      logger.shouldNeverHappen(s"invalid level value: ${level}")

      Success(
        BlacklistFindApiLevel.Global(
          org_id = org_id
        )
      )
    }

  }

  def validateBlacklistListingQueryParams(
                           parsedParams: Map[String, Vector[String]],
                           teamId: Option[TeamId],
                           orgId: OrgId,
                           ta_id: Option[Long]
                         ): Try[BlacklistListingAPIParams] = {
    val level = if (parsedParams.contains("level")) parsedParams("level").headOption else None
    given Logger: SRLogger = new SRLogger("Blacklist [validateBlacklistListingQueryParams]")
    for {
      (is_first: Boolean, range: InferredQueryTimeline.Range) <- Helpers.getTimeLineRange(parsedParams = parsedParams)

      level: BlacklistFindApiLevel <- fromAPIInputParam(level = level,team_id = teamId,org_id = orgId, ta_id=ta_id)

    } yield {
      BlacklistListingAPIParams(
        range = range,
        level = level,
        is_first = is_first
      )
    }
  }

  // [Public API] mentioned in API docs and used in Frontend
  // level could be none, all, team, global. If its none, we will assume it as "team"
  def findAll(search: Option[String], tid: Option[Long], level: Option[String]) = permissionUtils.checkPermission(
    version = ApiVersion.V3.toString,
    permission = PermType.VIEW_BLACKLIST,
    tidOpt = tid,
    allowInAgencyDashboard = true,
    apiAccess = true
  ).async { (request: PermissionRequest[AnyContent]) =>

    Future {

      val ta = request.actingTeamAccount
      val Res = request.Response
      given Logger: SRLogger = request.Logger
      val params = request.uri
      val teamId = ta.map(team=>TeamId(team.team_id))
      val orgId = OrgId(request.loggedinAccount.org.id)
      val parsedParams: Map[String, Vector[String]] = StringUtilsV2.getParams(params)
      implicit val apiVersion: ApiVersion = ApiVersion.V3


      val queryParams = validateBlacklistListingQueryParams(parsedParams = parsedParams,teamId = teamId,orgId = orgId,ta_id = ta.map(_.ta_id))

      queryParams match {
        case Failure(exception) => Res.ServerError(exception)

        case Success(filters) =>
          blacklistListingPaginationService.findBlacklistListingPage(
            searchKeyword = search,
            filters = filters,
            teamId = teamId,
            Logger = Logger
          ) match {
            case Left(err) =>
              err match {
                case BlacklistListingError.DBFailure(e: Throwable) =>
                  Logger.error("This should not happen. DB failure in blacklist listing api", err = e)
                  Res.ServerError(
                    message = "Server error, please contact support.",
                    e = None
                  )

                case BlacklistListingError.PaginationError(msg: String) =>
                  Logger.error(msg)
                  Res.ServerError(
                    message = "Server error, please contact support.",
                    e = None
                  )
              }

            case Right(blacklistApiResult: BlacklistListingApiResult) =>

              apiService.getBlacklistListingApiResponse(
                blacklistListingApiResult = blacklistApiResult,
                orgId = orgId,
                teamId = teamId,
                uri = request.uri
              ) match {

                case Left(err: BlacklistApiError) =>
                  Logger.error(msg = s"[BlacklistController.findAll] error::$err")
                  Res.ServerError(message = "Server error, please contact support", e = None)

                case Right(res) =>

                  Res.SuccessV3(data = Json.toJson(res))

              }
          }

      }

    }

  }

  /**
   * Gives the exact count of results for the findAll api above
   *
   * level could be none, all, team, global. If its none, we will assume it as "team"
   */
  def findAllCount(search: Option[String], tid: Option[Long], level: Option[String]) = permissionUtils.checkPermission(
    version = ApiVersion.V3.toString,
    permission = PermType.VIEW_BLACKLIST,
    tidOpt = tid,
    localOrStagingApiTestAccess = true,
    allowInAgencyDashboard = true
  ).async { (request: PermissionRequest[AnyContent]) =>

    Future {

      val ta = request.actingTeamAccount
      val Res = request.Response
      given Logger: SRLogger = request.Logger

      val _level: Try[BlacklistFindApiLevel] = fromAPIInputParam(level, OrgId(request.loggedinAccount.org.id), ta.map(_team => TeamId(_team.team_id)),ta_id = ta.map(_.ta_id))

      _level match {
        case Success(level) => blacklistService.findAllCount(
          level = level,
          searchKeyword = search,
          logger = Logger,

        ) match {
          case Failure(exception) =>

            Logger.shouldNeverHappen("blacklist count failed", err = Some(exception))

            Res.ServerError(err = exception)


          case Success(count) =>

            Res.SuccessV3(
              data = Json.obj(
                "search" -> search,
                "do_not_contact_count" -> count
              )
            )
        }

        case Failure(err) =>

          Logger.error(f"Invalid level param $err")

          Res.BadRequestError("Invalid level param")
      }

    }

  }


  // Only used before update blacklist call in frontend
  //15-oct-2024: We are only validating domains with excluded emails
  //so passing the do_not_contact_type as domain directly from FE
  def validateBlacklist(v: String, aid: Option[Long], tid: Option[Long], do_not_contact_type: DoNotContactType): Action[JsValue] = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_BLACKLIST,
    tidOpt = tid,

    // Date: 04/01/24, removing apiAccess as we checked the logs there were no usages for this api.
    // apiAccess = true
  ).async(parse.json) { (request: PermissionRequest[JsValue]) =>

    val Logger = request.Logger
    val Res = request.Response

    Future {

      if (request.actingTeamAccount.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {

        val validateData: JsResult[BlacklistCreateForm] = blacklistService.validateCreateBlacklistRequestBody(
          json = request.body,
          do_not_contact_type = do_not_contact_type
        )

        validateData match {

          case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

          case JsSuccess(data, _) =>

            val ta = request.actingTeamAccount.get
            val accountName = Helpers.getAccountName(ta)


            val validateTry = blacklistService.validateCreateOrUpdateBlacklist(
              data = data,
              level = BlacklistCreateOrDeleteApiLevel.Team(team_id = TeamId(ta.team_id), ta_id = ta.ta_id, org_id = OrgId(request.loggedinAccount.org.id)),
              doerAccount = request.loggedinAccount,
              teamId = Some(TeamId(ta.team_id)),
              SRLogger = Logger
            )

            validateTry match {

              case Left(EmailAlreadyExistsError.EmailAlreadyExistsInBlacklistError(emails)) =>

                Res.BadRequestError(
                  s"emails: ${emails.mkString(",")} already exists in blacklist"
                )

              case Left(EmailAlreadyExistsError.EmailAlreadyExistsInExceptionListError(emails)) =>

                Res.BadRequestError(
                  s"emails: $emails already exists in excluded_emails of domains"
                )

              case Left(EmailAlreadyExistsError.DBFailure(err)) =>

                Res.ServerError(
                  s"Error while validating blacklist, please try again or contact support",
                  e = Some(err)
                )

              case Left(EmailAlreadyExistsError.PhoneNumberAlreadyExistInBlacklist(phones)) =>
                Res.BadRequestError(
                  s"phones: ${phones.mkString(",")} already exists in blacklist"
                )


              case Right(_) =>

                Res.Success("Blacklists validated successfully", Json.obj())

            }
        }


      }
    }
  }

  // TODO - this API is for zapier calls only, remove this controller and route when the integration is closed.
  // Blacklist create - asynchronous

  /*
   * Date: 06-Apr-2024
   * Since we have switched to the V3 version from the V2 version in FE,
   * so this is only used for Zapier flow,
   * hence webAccess = false will prevent this from being used in FE by mistake.
   */
  def create(v: api.ApiVersion, aid: Option[Long], tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    version = v.toString,
    permission = PermType.EDIT_BLACKLIST,
    tidOpt = tid,

    apiAccess = true,
    webAccess = false
  ).async(parse.json) { (request: PermissionRequest[JsValue]) =>

    val Res = request.Response
    val logger = request.Logger

    logger.info(msg = s"[Zapier API called] org_id :: ${request.loggedinAccount.org.id}")

    Future {

      if (request.actingTeamAccount.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {

        val validateData = request.body.validate[BlacklistCreateUpdateApiRequest]

        validateData match {

          case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

          case JsSuccess(data, _) =>

            val ta = request.actingTeamAccount.get

            val accountName = Helpers.getAccountName(ta)

            /*
              * domains: comes from zapier only
              * here BlacklistCreateUpdateApiRequest converting to BlacklistCreateUpdateForm to accept zapier api calls
              * */
            val domains = if (data.domains.isDefined && data.domains.get.nonEmpty) {
              data.domains.get.map(d => {
                DomainsWithExcludedEmails(domain = d, excluded_emails = Seq())
              })
            } else Seq()


            /*domains_with_excluded_emails: only comes from SmartReach frontend */
            val domains_with_excluded_emails = if (data.domains_with_excluded_emails.isDefined)
              data.domains_with_excluded_emails.get
            else Seq()

            if (domains.nonEmpty && domains_with_excluded_emails.nonEmpty) {
              logger.fatal(s"domains.nonEmpty && domains_with_excluded_emails.nonEmpty:: ${request.body}")
            }

            val prospect_domains_with_excluded_emails: Seq[(String, Seq[String])] = (domains ++ domains_with_excluded_emails)
              .map(d => (d.domain, d.excluded_emails.distinct))

            val msg: MQDoNotContactMessage = MQDoNotContactMessage(
              prospect_emails = data.emails.distinct,
              prospect_domains_with_excluded_emails = prospect_domains_with_excluded_emails,
              is_req_via_dnc_form = true,
              account_id = ta.user_id,
              account_name = accountName,
              team_id = ta.team_id,
              ta_id = ta.ta_id,
              campaign_id = None,
              campaign_name = None
            )

            mqDoNotContactPublisher.publishMultiple(
              msg
            ) match {

              case Failure(e) =>

                Res.ServerError(e.getMessage, e = Some(e))

              case Success(_) =>

                Res.Success("Blacklist is being updated (This can take upto 5 minutes)", Json.obj("saved" -> true))

            }
        }
      }
    }

  }

  // Blacklist add emails/domains - synchronous
  // [Public API] mentioned in API Docs and used in Frontend
  def createV3(aid: Option[Long], tid: Option[Long], do_not_contact_type: DoNotContactType): Action[JsValue] = permissionUtils.checkPermission(
    version = ApiVersion.V3.toString,
    permission = PermType.EDIT_BLACKLIST,
    tidOpt = tid,
    allowInAgencyDashboard= true,
    apiAccess = true
  ).async(parse.json) { (request: PermissionRequest[JsValue]) =>

    val Res = request.Response
    given logger: SRLogger = request.Logger
    val account = request.loggedinAccount
    val orgId = OrgId(request.loggedinAccount.org.id)
    implicit val apiVersion: ApiVersion = ApiVersion.V3

    val teamId: Option[TeamId] = request.actingTeamAccount.map(t => TeamId(t.team_id))

    val level: BlacklistCreateOrDeleteApiLevel = if (request.actingTeamAccount.isEmpty) {
      BlacklistCreateOrDeleteApiLevel.Global(org_id = orgId)
    } else {
      BlacklistCreateOrDeleteApiLevel.Team(org_id = orgId, team_id = teamId.get, ta_id = request.actingTeamAccount.get.ta_id)
    }

    val accountName = Helpers.getAccountName(request.loggedinAccount)
    val accountId = AccountId(request.loggedinAccount.internal_id)

      val validateData = blacklistService.validateCreateBlacklistRequestBody(
        json = request.body,
        do_not_contact_type = do_not_contact_type
      )

      validateData match {

        case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

        case JsSuccess(data, _) =>

          blacklistService.createOrUpdateBlacklistService(
            accountId = accountId.id,
            addedByName = accountName, // Could be SmartReach.io api Unsubscribed, or user adding it
            level = level,
            data = data,
            is_req_via_dnc_form = true,
            opted_out_from_campaign_id = None,
            opted_out_from_campaign_name = None,
            account = account,
            auditRequestLogId = None,
            isApiCall = request.isApiCall,
            team_id = teamId,
            Logger = logger
          ) match {

            case Left(err) =>

              err match {

                case CreateOrUpdateBlacklistError.DBFailure(err: Throwable) =>
                  Future.successful(Res.ServerError(
                    message = "Server Error, please contact support",
                    e = Some(err)
                  ))

                case CreateOrUpdateBlacklistError.AccountIDNotFoundError(err: Throwable) =>
                  Future.successful(Res.ServerError(
                    message = "Server Error, please contact support",
                    e = Some(err)
                  ))

                case CreateOrUpdateBlacklistError.BadRequestErrors(errors) =>
                  Future.successful(Res.BadRequestErrorV3(errorResponse = errors.toList))


              }

            case Right(blacklists) =>

              val teamIdOpt: Option[TeamId] = level match {
                case data: BlacklistCreateOrDeleteApiLevel.Team => Some(data.team_id)
                case _: BlacklistCreateOrDeleteApiLevel.Global => None
              }
              apiService.getBlacklistCreateV3ApiResponse(
                data = blacklists,
                orgId = orgId,
                teamId = teamIdOpt
              ) match {

                case Left(err: BlacklistApiError) =>
                  logger.error(msg = s"[BlacklistController.createV3] error::$err")
                  Future.successful(Res.ServerError(message = "Server error, please contact support", e = None))

                case Right(res: BlacklistCreateV3ApiResponse) =>

                  Future.successful(Res.SuccessV3(data = Json.toJson(res)))

              }

          }
      }

  }


  def saveUploadedDncCsv(tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    version = ApiVersion.V3.toString,
    permission = PermType.EDIT_BLACKLIST,
    tidOpt = tid,
    localOrStagingApiTestAccess = true,
    allowInAgencyDashboard = true

  ).async(parse.json) { (request: PermissionRequest[JsValue]) =>


    Future {
      val Logger = request.Logger
      val Res = request.Response

      val t = request.actingTeamAccount
      val loggedInAccountId = request.loggedinAccount.internal_id
      implicit val apiVersion: ApiVersion = ApiVersion.V3


      val validateData = request.body.validate[CsvQueueCreateFormDataV2]

      validateData match {

        case JsError(e) =>
          Res.JsValidationError(errors = e)

        case JsSuccess(data, _) =>

          Logger.info(s"saveUploadedDncCsv: initiated with request body: data: $data")
          CsvQueue.createV2(
            accountId = loggedInAccountId,
            teamId = t.map(te => te.team_id),
            ta_id = t.map(te => te.ta_id),
            loggedin_id = loggedInAccountId,
            data = data
          ) match {

            case Failure(e) =>
              Res.ServerError(s"There was an error. Please try again or contact support", e = Some(e))

            case Success(obj) =>
              Res.Success("CSV will be uploaded in a few minutes", Json.obj("csv_file" -> obj))

          }

      }
    }

  }

  // Used for Zapier calls and in Frontend. Not exposed as Public API hence not mentioned in API Docs
  def update(v: String, id: SrIdentifier, aid: Option[Long], tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_BLACKLIST,
    tidOpt = tid,

    apiAccess = true
  ).async(parse.json) { (request: PermissionRequest[JsValue]) =>


    val Res = request.Response
    given logger: SRLogger = request.Logger

    if (request.isApiCall) {
      logger.info(msg = s"[Zapier API called] org_id :: ${request.loggedinAccount.org.id}")
    }

    Future {

      if (request.actingTeamAccount.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else if (v == ApiVersion.V3.toString && request.isApiCall) {

        Res.ForbiddenError

      } else {

        val ta = request.actingTeamAccount.get
        val teamId = TeamId(ta.team_id)

        val validateData = request.body.validate[BlacklistCreateUpdateApiRequest]

        validateData match {

          case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

          case JsSuccess(data, _) =>

            blacklistService.getBlacklistIdFromSrIdentifier(
              id = id,
              teamId = teamId
            ) match {
              case Left(err) =>
                err match {
                  case BlacklistIdToUuidError.BlacklistIdNotFound(err_msg) =>
                    Res.NotFoundError(err_msg)
                  case BlacklistIdToUuidError.DbFailure(e) =>
                    Res.ServerError(message = "Server error, please contact support.", e = Some(e))
                }

              case Right(blacklistId: BlacklistId) =>
                val blacklists = blacklistDAO.checkBlacklistByIds(teamId = teamId.id, ids = Seq(blacklistId.id))

                blacklists match {
                  case Failure(e) =>
                    Res.ServerError(message = "Server error, please contact support.", e = Some(e))

                  case Success(blacklist) =>
                    if (blacklist.isEmpty) {

                      Res.BadRequestError("Invalid blacklist id")

                    } else {

                      val accountName = Helpers.getAccountName(ta)

                      /*
                      * domains: comes from zapier only
                      * here BlacklistCreateUpdateApiRequest converting to BlacklistCreateUpdateForm to accept zapier api calls
                      * */
                      val domains = if (data.domains.isDefined && data.domains.get.nonEmpty) {
                        data.domains.get.map(d => {
                          DomainsWithExcludedEmails(domain = d, excluded_emails = Seq())
                        })
                      } else Seq()

                      /*domains_with_excluded_emails: only comes from SmartReach frontend */
                      val domains_with_excluded_emails = if (data.domains_with_excluded_emails.isDefined)
                        data.domains_with_excluded_emails.get
                      else Seq()


                      if (domains.nonEmpty && domains_with_excluded_emails.nonEmpty) {
                        logger.fatal(s"domains.nonEmpty && domains_with_excluded_emails.nonEmpty:: ${request.body}")
                      }

                      val prospect_domains_with_excluded_emails: Seq[(String, Seq[String])] = (domains ++ domains_with_excluded_emails)
                        .map(d => (d.domain, d.excluded_emails.distinct))

                      val msg: MQDoNotContactMessage = MQDoNotContactMessage(
                        prospect_emails = data.emails.distinct,
                        prospect_domains_with_excluded_emails = prospect_domains_with_excluded_emails,
                        is_req_via_dnc_form = true,
                        account_id = ta.user_id,
                        account_name = accountName,
                        team_id = teamId.id,
                        ta_id = ta.ta_id,
                        campaign_id = None,
                        campaign_name = None
                      )

                      mqDoNotContactPublisher.publishMultiple(
                        msg
                      ) match {

                        case Failure(e) =>

                          Res.ServerError(e.getMessage, e = Some(e))

                        case Success(_) =>

                          Res.Success("Blacklist is being updated (This can take upto 5 minutes)", Json.obj("saved" -> true))

                      }

                    }
                }
            }

        }
      }
    }

  }

  def addToAllTeamsInOrg(v: String) = authUtils.isLoggedIn()
    .async(parse.json) { (request: AccountRequest[JsValue]) =>

      Future {
        val Logger = request.Logger
        val Res = request.Response

        if (!AuthUtils._isAgencyAndRoleIsOwnerOrAgencyAdmin(a = request.account)) {

          Res.ForbiddenError("Unauthorized access. Contact support.")

        } else {

          request.body.validate[BlacklistCreateUpdateForm] match {

            case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

            case JsSuccess(data, _) =>

              val a = request.account
              val allTeamsInOrg = request.account.teams
                .map(t => t.access_members.find(mem => mem.user_id == a.internal_id))
                .filter(_.isDefined)
                .map(_.get)


              val accountName = Helpers.getAccountName(a)

              var errorWhileAdding: Option[Throwable] = None

              val addedToTeamNames: Seq[String] = allTeamsInOrg
                .map(t => {

                  mqDoNotContactPublisher.publishMultiple(
                    msg = MQDoNotContactMessage(
                      prospect_emails = data.emails.distinct,
                      prospect_domains_with_excluded_emails = data.domains_with_excluded_emails.map(d => (d.domain, d.excluded_emails.distinct)),
                      is_req_via_dnc_form = true,
                      account_id = a.internal_id,
                      account_name = accountName,
                      team_id = t.team_id,
                      ta_id = t.ta_id,
                      campaign_id = None,
                      campaign_name = None
                    )
                  ) match {

                    case Failure(e) =>
                      Logger.error(s"addToAllTeamsInOrg (${a.internal_id} ${a.org.name}): There was an error. added to team ${t.team_name} :: input: $data :: ${LogHelpers.getStackTraceAsString(e)}")

                      errorWhileAdding = Some(e)

                      ""

                    case Success(_) =>
                      Logger.info(s"addToAllTeamsInOrg (${a.internal_id} ${a.org.name}): Do not contact list is being updated. added to team ${t.team_name} :: input: $data")
                      t.team_name

                  }
                })
                .filter(_.trim.nonEmpty)

              if (errorWhileAdding.isDefined) {
                Res.ServerError(s"There was an error. Please try again or contact support.", e = errorWhileAdding)

              } else {
                Res.Success("Do not contact list is being updated (This can take upto 5 minutes)", Json.obj("added_to_teams" -> addedToTeamNames))
              }
          }
        }
      }
    }

  // TODO - this API is for zapier calls only, remove this controller and route when the integration is closed.
  /*
   * Date: 06-Apr-2024
   * Since we have switched to the V3 version from the V2 version in FE,
   * so this is only used for Zapier flow,
   * hence webAccess = false will prevent this from being used in FE by mistake.
   */
  def delete(v: ApiVersion, aid: Option[Long], tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    version = v.toString,
    permission = PermType.EDIT_BLACKLIST,
    tidOpt = tid,

    apiAccess = true,
    webAccess = false
  ).async(parse.json) { (request: PermissionRequest[JsValue]) =>

    Future {

      given Logger: SRLogger = request.Logger
      val Res = request.Response

      Logger.info(msg = s"[Zapier API called] org_id :: ${request.loggedinAccount.org.id}")

      if (request.actingTeamAccount.isEmpty) {
        Res.BadRequestError("Invalid team")
      }
      else {

        val ta = request.actingTeamAccount.get
        val teamId = TeamId(request.actingTeamAccount.get.team_id)

        val validateData: JsResult[BlacklistDeleteFormData] = request.body.validate[BlacklistDeleteFormData]

        validateData match {

          case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

          case JsSuccess(data, _) =>

            blacklistService.deleteBlacklist(
              accountId = ta.user_id,
              blacklistIds = data.blacklist_ids,
              accountName = Helpers.getAccountName(ta),
              account = Some(request.loggedinAccount),
              auditRequestLogId = Some(request.auditRequestLogId),
              Logger = Logger,
              teamId = Some(teamId),
              level = BlacklistCreateOrDeleteApiLevel.Team(
                org_id = OrgId(request.loggedinAccount.org.id), team_id = teamId, ta_id = ta.ta_id
              ) //FIXME GDNC : used by zapier api calls [ this flow is team based ]
            ) match {
              case Left(error: DeleteBlacklistError.DbFailure) =>
                Res.ServerError(message = "There was an error.", e = Some(error.e))
              case Right(_) =>
                // as current V2 api is being used by our customers, we will not be changing the response structure for V2.
                // this message is returned in the V2 response.
                val response_message = "Do not contact list updated"
                Res.Success(response_message, Json.obj())
            }
        }
      }
    }
  }

  // [Public API] mentioned in API Docs and used in Frontend
  def deleteV3(aid: Option[Long], tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    version = ApiVersion.V3.toString,
    permission = PermType.EDIT_BLACKLIST,
    tidOpt = tid,
    allowInAgencyDashboard = true,
    apiAccess = true
  ).async(parse.json) { (request: PermissionRequest[JsValue]) =>

    Future {
      given Logger: SRLogger = request.Logger
      val Res = request.Response

      val orgId = OrgId(request.loggedinAccount.org.id)
      implicit val apiVersion: ApiVersion = ApiVersion.V3

      val level: BlacklistCreateOrDeleteApiLevel = if (request.actingTeamAccount.isEmpty) {
        BlacklistCreateOrDeleteApiLevel.Global(org_id = orgId)
      } else {
        BlacklistCreateOrDeleteApiLevel.Team(org_id = orgId, team_id = TeamId(request.actingTeamAccount.get.team_id), ta_id = request.actingTeamAccount.get.ta_id)
      }



        val validateData: JsResult[BlacklistDeleteFormDataV3] = request.body.validate[BlacklistDeleteFormDataV3]

        validateData match {
          case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

          case JsSuccess(data, _) =>

            blacklistService.deleteBlacklistV3(
              data = data,
              accountId = request.loggedinAccount.internal_id,
              level = level,
              accountName = Helpers.getAccountName(request.loggedinAccount),
              account = request.loggedinAccount,
              teamId = request.actingTeamAccount.map(t => TeamId(t.team_id)),
              auditRequestLogId = request.auditRequestLogId
            ) match {

              case Left(err: DeleteBlacklistError) =>

                err match {
                  case DeleteBlacklistError.BlacklistIdsNotFound(ids) =>
                    Res.BadRequestErrorV3(errorResponse = List(
                      ErrorResponseCreateUpdateBlacklistApi(
                        error_type = BlacklistErrorType.InvalidIds,
                        message = "some ids are invalid",
                        data = Some(ids)
                      )
                    ))

                  case DeleteBlacklistError.DbFailure(e) =>
                    Logger.error(msg = s"[BlacklistController.delete] DbFailure in blacklistService.deleteBlacklistV3 uuids::$data error::$e")
                    Res.ServerError(e)
                }

              case Right(_) =>

                Res.SuccessNoContent()

            }
        }
    }

  }

  def downloadAgencyDNC(v: String, for_emails: Option[Boolean], aid: Option[Long], tid: Option[Long]) =  authUtils.isLoggedIn() { (request: AccountRequest[AnyContent]) =>
    val Logger = request.Logger
    val Res = request.Response

    if (!AuthUtils._isAgencyAndRoleIsOwnerOrAgencyAdmin(a = request.account)) {

      Res.ForbiddenError("Unauthorized access. Contact support.")

    } else if (for_emails.isEmpty) {
      Res.BadRequestError("Please send valid for_emails")
    } else {

      blacklistDAO.agencyDNCExport(
        orgId = request.account.org.id,
        forEmails = for_emails.getOrElse(true)
      ) match {
        case Failure(e) =>

          Res.ServerError(e)

        case Success(dncList) =>

          val filename = URLEncoder.encode(
            s"dnc_export_${if (for_emails.getOrElse(true)) "emails" else "domains"}_${DateTime.now().getMillis}",

            "UTF-8"

          ) + ".csv"

          val f = SingletonTemporaryFileCreator.create((new File("/tmp/" + filename).toPath))

          val writer = CSVWriter.open(f.toFile)

          val excludedEmailsHeader = if(for_emails.get) List("email", "") else List("domain", "excluded_emails")

          writer.writeRow(excludedEmailsHeader)

          dncList.foreach(d => {

            val row = List(d._1, d._2.getOrElse(""))

            writer.writeRow(row)

          })

          writer.close()

          Ok
            .sendFile(f.toFile, onClose = () => {
              f.delete()
            })
            .withHeaders(
              "Content-Disposition" -> s"attachment;filename=$filename",
              "Access-Control-Expose-Headers" -> "Content-Disposition"
            )
            .as("text/plain")
      }


    }
  }


}
