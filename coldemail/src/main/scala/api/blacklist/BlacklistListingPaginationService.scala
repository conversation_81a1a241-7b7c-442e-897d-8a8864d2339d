package api.blacklist

import api.AppConfig
import api.accounts.TeamId
import api.blacklist.models.DoNotContactType
import api.prospects.{ExactIdToCompareTime, InferredQueryTimeline, NavigationLinks}
import utils.SRLogger
import utils.jodatimeutils.JodaTimeUtils
import utils.pagination.{GetDataErrorTrait, GetSameTimeDataBlacklistListing, PaginationServiceTrait}
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.util.{Failure, Success, Try}

sealed trait BlacklistListingError extends GetDataErrorTrait

object BlacklistListingError {

  case class DBFailure(err: Throwable) extends BlacklistListingError

  case class PaginationError(msg: String) extends BlacklistListingError

}

case class BlacklistListingApiResult(
                                      blacklist: Seq[Blacklist],
                                      links: NavigationLinks
                                    )

class BlacklistListingPaginationService(
                                         blacklistDAO: BlacklistDAO,
                                         srRollingUpdateCoreService: SrRollingUpdateCoreService
                                       ) extends PaginationServiceTrait[Blacklist] {
  override type GetSameTimeDataFromDbParamsType = GetSameTimeDataBlacklistListing

  override def getSameTimeDataFromDb(
                                      data: GetSameTimeDataBlacklistListing,
                                      exactIdToCompareTime: ExactIdToCompareTime
                                    )(using logger: SRLogger): Either[BlacklistListingError, List[Blacklist]] = {
    blacklistDAO.findAll(
      SRLogger = logger,
      level = data.level,
      limit = data.limit,
      queryTimeline = Some(InferredQueryTimeline.Exact(exactIdToCompareTime))
    ) match {
      case Failure(exception) => Left(BlacklistListingError.DBFailure(exception))
      case Success(exactlyAtProspects) => Right(exactlyAtProspects.toList)
    }
  }

  def findBlacklistListingPage(
                                searchKeyword: Option[String],
                                filters: BlacklistListingAPIParams,
                                teamId: Option[TeamId],
                                Logger: SRLogger
                              ): Either[BlacklistListingError, BlacklistListingApiResult] = {

    given srLogger: SRLogger = Logger // fixme given

//    val emailNotCompulsoryEnabled = if(teamId.isDefined) {
//      srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        teamId = teamId.get,
//        feature = SrRollingUpdateFeature.EmailNotCompulsory
//      )(Logger)
//    } else false

    val limit = AppConfig.blacklistListingRowsPerPage
//      val limit = 10

    blacklistDAO.findAll(
      SRLogger = Logger,
      level = filters.level,
      searchKeyword = searchKeyword,
      limit = limit + 1,
      queryTimeline = Some(filters.range)
    ) match {
      case Failure(exception) => Left(BlacklistListingError.DBFailure(exception))
      case Success(blacklistData: Seq[Blacklist]) =>

        if(blacklistData.isEmpty) {
          Right(
            BlacklistListingApiResult(
            blacklist = Seq(),
            links = NavigationLinks(prev = None, next = None)
          ))
        } else {
          //Sorting in descending order of created_at
          val sortedBlacklist: Seq[Blacklist] = blacklistData.sortBy(_.created_at)(JodaTimeUtils.dateTimeOrdering).reverse

          getFinalPaginationResponse(
            isFirst = Some(filters.is_first),
            timeline = filters.range,
            sortedData = sortedBlacklist.toList,
            limit = limit,
            dataToGetSameTimeRecords = GetSameTimeDataBlacklistListing(
              level = filters.level,
              limit = limit,
              Logger = Logger
            )
          ) match {
            case Left(err) => Left(BlacklistListingError.PaginationError(msg = "Error in Blacklist pagination"))

            case Right(paginationResObject) =>
              val blacklistData: Seq[Blacklist] = paginationResObject.data.toSeq

              Right(
                BlacklistListingApiResult(
                  blacklist = blacklistData,
                  links = paginationResObject.links
                )
              )

          }
        }

    }
  }

}
