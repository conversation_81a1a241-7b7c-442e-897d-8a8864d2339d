package api.blacklist.models

import play.api.libs.json.{<PERSON>at, <PERSON>s<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>sString, JsSuccess, JsValue}
import play.api.mvc.PathBindable

import scala.util.{Failure, Success, Try}

sealed trait DoNotContactType {

  def toString: String
}

object DoNotContactType {

  private val email = "email"
  private val domain = "domain"
  private val phone = "phone"

  case object EMAIL extends DoNotContactType {
    override def toString: String = email
  }

  case object DOMAIN extends DoNotContactType {
    override def toString: String = domain
  }

  case object PHONE extends DoNotContactType {
    override def toString: String = phone
  }

  def fromString(key: String): Try[DoNotContactType] = Try {
    key match {

      case `email` => EMAIL

      case `domain` => DOMAIN

      case  `phone` => PHONE

    }
  }

  given format: Format[DoNotContactType] = new Format[DoNotContactType] {
    override def writes(o: DoNotContactType): JsValue = {
      JsString(o.toString)
    }

    override def reads(json: JsValue): JsResult[DoNotContactType] = {
      fromString(json.as[String]) match {
        case Failure(err) => JsError(err.getMessage)
        case Success(t) => JsSuccess(t)
      }
    }
  }

  implicit def userIdentifierBinder: PathBindable[DoNotContactType] = new PathBindable[DoNotContactType] {

    override def bind(key: String, value: String): Either[String, DoNotContactType] = {

      val input_val = DoNotContactType.fromString(value)
      input_val match {
        case Success(dnc_type) => Right(dnc_type)
        case Failure(_) => Left("Invalid do_not_contact_type")
      }
    }

    override def unbind(key: String, do_not_contact_type: DoNotContactType): String = {

      do_not_contact_type.toString
    }
  }
}
