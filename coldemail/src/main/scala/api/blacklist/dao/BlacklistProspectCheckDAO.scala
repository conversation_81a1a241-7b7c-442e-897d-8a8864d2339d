package api.blacklist.dao

import api.accounts.models.OrgId
import api.blacklist.Blacklist
import api.blacklist.BlacklistDAO.fromDb
import scalikejdbc.scalikejdbcSQLInterpolationImplicitDef
import utils.SRLogger
import utils.dbutils.{DBUtils, SQLUtils}

import scala.util.{Success, Try}

// 14th December 2021: temporary object to
// remove cyclic dependency between BlacklistDAO and ProspectDAO, and allow DI over there
class BlacklistProspectCheckDAO {


  // Added on 6th September 2021: to optimise slow Blacklist.findAll that is being used while creating/updating prospects
  def findByEmailsAndDomains(
    logger: SRLogger,
    teamId: Long,
    orgId: OrgId,
    emails: Seq[String],
    domains: Seq[String]
  ): Try[Seq[Blacklist]] = {

    val allNamesToSearch = (emails ++ domains)
      .map(_.trim.toLowerCase)
      .distinct

    if (allNamesToSearch.isEmpty) {

      logger.error("empty allNamesToSearch in Blacklist.findByEmailsAndDomains")
      Success(Seq())

    } else {

      DBUtils.readOnly(
        logger = logger.appendLogRequestId("Blacklist.findByEmailsAndDomains")
      ) { implicit session =>

        sql"""
          SELECT *
          FROM blacklist
          WHERE
            (team_id = $teamId
            OR ( org_id = ${orgId.id} AND is_global)
            )
            AND name IN ${SQLUtils.generateSQLValuesClause(allNamesToSearch)}
            ;
        """
          .map(fromDb)
          .list
          .apply()

      }
    }
  }
}
