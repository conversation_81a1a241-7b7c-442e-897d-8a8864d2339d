package api.internal_dashboard.controllers

import api.accounts.{AccountRequest, AuthUtils}
import api.internal_dashboard.dao.InternalDashboardDAO
import api.{ApiVersion, AppConfig, CONSTANTS, CacheKeys, CacheService}
import play.api.libs.json.{JsValue, Json}
import play.api.mvc.{Action, AnyContent, Request}

import scala.util.{Failure, Success}

class InternalDashboardController(
  internalDashboardDAO: InternalDashboardDAO,
  cacheService: CacheService,
  authUtils: AuthUtils
) {


  def getPaidAccountsForMasterDashboard(v: String, aid: Option[Long], tid: Option[Long]) =  authUtils.isLoggedIn() { (request: AccountRequest[AnyContent]) =>
    val Logger = request.Logger
    val Res = request.Response
    if (request.isSupportAppRequest || AppConfig.masterAccountIds.contains(request.account.internal_id)) {


      if (aid.isDefined && aid.get != request.account.internal_id) {

        Res.ForbiddenError("You are not authorized to do this")

      } else {

        val paidAccountsFromCache = cacheService.getJsValue(CacheKeys.PAID_ACCOUNTS.toString)

        if (paidAccountsFromCache.isDefined) {

          Res.Success("Paid accounts found", Json.obj("master_dashboard" -> Json.obj("accounts" -> paidAccountsFromCache)))

        } else {

          internalDashboardDAO.getPaidAccountsForMasterDashboard() match {

            case Failure(e) =>

              Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + " :" + e.getMessage, e = Some(e))

            case Success(paidAccounts) =>

              cacheService.setJsValue(CacheKeys.PAID_ACCOUNTS.toString, Json.toJson(paidAccounts))

              Res.Success("Paid accounts found", Json.obj("master_dashboard" -> Json.obj("accounts" -> paidAccounts)))
          }

        }
      }
    } else {
      Res.ForbiddenError("You are not authorized to do this")
    }

  }


  def getTrialAccountsForMasterDashboard(v: String, aid: Option[Long], tid: Option[Long]) =  authUtils.isLoggedIn() { (request: AccountRequest[AnyContent]) =>
    val Logger = request.Logger
    val Res = request.Response
    if (request.isSupportAppRequest || AppConfig.masterAccountIds.contains(request.account.internal_id)) {

      if (aid.isDefined && aid.get != request.account.internal_id) {

        Res.ForbiddenError("You are not authorized to do this")

      } else {

        val trialAccountsFromCache = cacheService.getJsValue(CacheKeys.TRIAL_ACCLOUNTS.toString)

        if (trialAccountsFromCache.isDefined) {

          Res.Success("Trial Accounts found", Json.obj("master_dashboard" -> Json.obj("accounts" -> trialAccountsFromCache)))

        } else {

          internalDashboardDAO.getTrialAccountsForMasterDashboard() match {

            case Failure(e) =>

              Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + " :" + e.getMessage, e = Some(e))

            case Success(trialAccounts) =>

              cacheService.setJsValue(CacheKeys.TRIAL_ACCLOUNTS.toString, Json.toJson(trialAccounts))

              Res.Success("Trial Accounts found", Json.obj("master_dashboard" -> Json.obj("accounts" -> trialAccounts)))

          }
        }
      }
    } else {
      Res.ForbiddenError("You are not authorized to do this")
    }

  }

  def getPaidRiskyAccountsForMasterDashboard(v: String, aid: Option[Long], tid: Option[Long]) =  authUtils.isLoggedIn() { (request: AccountRequest[AnyContent]) =>
    val Logger = request.Logger
    val Res = request.Response
    if (request.isSupportAppRequest || AppConfig.masterAccountIds.contains(request.account.internal_id)) {

      if (aid.isDefined && aid.get != request.account.internal_id) {

        Res.ForbiddenError("You are not authorized to do this")

      } else {

        val fromCache = cacheService.getJsValue(CacheKeys.PAID_RISKY_ACCOUNTS.toString)

        if (fromCache.isDefined) {

          Res.Success("accounts found", Json.obj("accounts" -> fromCache))

        } else {

          internalDashboardDAO.getPaidRiskyAccounts() match {

            case Failure(e) =>

              Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + " :" + e.getMessage, e = Some(e))

            case Success(accounts) =>

              cacheService.setJsValue(CacheKeys.PAID_RISKY_ACCOUNTS.toString, Json.toJson(accounts))

              Res.Success("paid risky accounts found", Json.obj("accounts" -> accounts))

          }
        }
      }
    } else {
      Res.ForbiddenError("You are not authorized to do this")
    }

  }


  def statsDataForMasterDashboard(
    v: String,
    aid: Option[Long],
    tid: Option[Long]
  ) = authUtils.isLoggedIn() { (request: AccountRequest[AnyContent]) =>
    val Logger = request.Logger
    val Res = request.Response
    if (request.isSupportAppRequest || AppConfig.masterAccountIds.contains(request.account.internal_id)) {

      if (aid.isDefined && aid.get != request.account.internal_id) {

        Res.ForbiddenError("You are not authorized to do this")

      } else {

        internalDashboardDAO.getAllOrgs() match {

          case Failure(e) =>

            Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + " :" + e.getMessage, e = Some(e))

          case Success(orgs) =>

            Res.Success("orgs found", Json.obj("orgs" -> orgs))

        }
      }
    } else {
      Res.ForbiddenError("You are not authorized to do this")
    }

  }

  def statsExplorerForMasterDashboard(
                                       v: String,
                                       group_by: String,
                                       sub_group_by: Option[String],
                                       sub_group_by_2: Option[String],
                                       org_id: Option[Long],
                                       aid: Option[Long],
                                       tid: Option[Long]
                                     ): Action[AnyContent] = authUtils.isLoggedIn() { (request: AccountRequest[AnyContent]) =>
    val Logger = request.Logger
    val Res = request.Response
    if (request.isSupportAppRequest || AppConfig.masterAccountIds.contains(request.account.internal_id)) {

      if (aid.isDefined && aid.get != request.account.internal_id) {

        Res.ForbiddenError("You are not authorized to do this")

      } else {

        val cacheKey = s"md::statsexplorer::$group_by::${sub_group_by.orNull}::${sub_group_by_2.orNull}::${org_id.orNull}"

        val statsFromCache = cacheService.getJsValue(cacheKey)

        if (statsFromCache.isDefined) {

          Res.Success("Stats found", Json.obj("stats" -> statsFromCache.get))

        } else {

          internalDashboardDAO.explorerStatsForMasterDashboard(
            groupBy = group_by,
            subGroupByOpt = sub_group_by,
            subGroupByTwoOpt = sub_group_by_2,
            orgId = org_id
          ) match {

            case Failure(e) =>

              Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + " :" + e.getMessage, e = Some(e))

            case Success(stats) =>

              cacheService.setJsValue(cacheKey, Json.toJson(stats), expirySeconds = 86400)

              Res.Success("Stats found", Json.obj("stats" -> stats))

          }
        }
      }
    } else {
      Res.ForbiddenError("You are not authorized to do this")
    }

  }
}
