package api.dataplatforms

import org.apache.pekko.actor.ActorSystem
import api.prospects.ProspectCreateFormData
import play.api.libs.json.JsValue
import play.api.libs.ws.{WSClient, WSResponse}
import utils.helpers.LogHelpers
import utils.{Help<PERSON>, SRLogger}

import scala.concurrent.{ExecutionContext, Future}

trait TDataPlatformService {

  def searchContact(
    data: DataplatformSearchRequest,
    apiKey: String,
    logRawResponseInDB: (WSResponse) => Unit
  )
    (implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger, system: ActorSystem): Future[Seq[ProspectCreateFormData]]

  def batchSearch(
    data: Seq[DataplatformSearchRequestBatch],
    apiKey: String,
    logRawResponseInDB: (WSResponse) => Unit
  )
    (implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger, system: ActorSystem): Future[Seq[ProspectCreateFormData]]

  def checkAPIAccess(data: DataplatformSearchRequest, apiKey: String)(implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger): Future[Boolean]


}

object DataPlatformService {

  def getDomainFromCompanyName(
    company: String
  )(implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger): Future[Option[String]] = {

    //NOTE: this end point is used to get domain from company name
    //ref: https://medium.com/the-red-fish/automate-finding-a-company-url-with-a-company-name-on-google-sheets-for-free-in-3-easy-steps-7ea77280bcdc

    ws.url(s"https://autocomplete.clearbit.com/v1/companies/suggest?query=$company")
      .get()
      .map(res => {

        if (res.status == 200) {

          val domainFromCompany = (res.json).as[List[JsValue]]
            .headOption
            .flatMap(resFirst => (resFirst \ "domain").asOpt[String])


          domainFromCompany

        } else {

          None

        }
      })
      .recover { case e =>

        Logger.fatal(s"getDomainFromCompanyName: company: $company :: ${LogHelpers.getStackTraceAsString(e)}")

        None
      }
  }

}
