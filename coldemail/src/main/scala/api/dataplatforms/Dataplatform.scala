package api.dataplatforms

import play.api.Logging
import play.api.libs.json.{JsValue, Json, OFormat, OWrites}
import scalikejdbc._
import utils.Helpers
import utils.security.EncryptionHelpers

import scala.util.Try


case class DataplatformInDB(
                             api_key: String
                           )


object DataplatformInDB {
  given format: OFormat[DataplatformInDB] = Json.format[DataplatformInDB]

  def fromDb(rs: WrappedResultSet): DataplatformInDB = {
    DataplatformInDB(
      api_key = EncryptionHelpers.decryptEmailDataplatformCredential(rs.string("api_key"))
    )

  }
}

case class allowedDropdownFiledOptions (
                                       key: String,
                                       value: String,
                                       text: String
                                       )

object allowedDropdownFiledOptions {
  given format: OFormat[allowedDropdownFiledOptions] = Json.format[allowedDropdownFiledOptions]
}

case class allowedFields(
                                      display_name: String,
                                      key: String,
                                      field_type: String,
                                      options: Seq[allowedDropdownFiledOptions] = Seq()
                                    )
object allowedFields {
  given format: OFormat[allowedFields] = Json.format[allowedFields]
}

case class Dataplatform(
  api_key: Option[String],
  text_id: DataplatformType,
  display_name: String,
  logo_url: String,
  allowed_fields: Seq[allowedFields] = Seq()
)

object Dataplatform extends Logging {

  implicit val writes: OWrites[Dataplatform] = Json.writes[Dataplatform]


  def fromDb(rs: WrappedResultSet): Seq[Dataplatform] = {

    val hunter = rs.stringOpt("hunter_api_key_enc").map(EncryptionHelpers.decryptEmailDataplatformCredential)
    val uplead = rs.stringOpt("uplead_api_key_enc").map(EncryptionHelpers.decryptEmailDataplatformCredential)
    val dropcontact = rs.stringOpt("dropcontact_api_key_enc").map(EncryptionHelpers.decryptEmailDataplatformCredential)
    val anymailfinder = rs.stringOpt("anymailfinder_api_key_enc").map(EncryptionHelpers.decryptEmailDataplatformCredential)
    val clearbit = rs.stringOpt("clearbit_api_key_enc").map(EncryptionHelpers.decryptEmailDataplatformCredential)
    val aeroleads = rs.stringOpt("aeroleads_api_key_enc").map(EncryptionHelpers.decryptEmailDataplatformCredential)

    val apiKeys: Seq[(Option[String], DataplatformType)] = Seq(

      (hunter, DataplatformType.HUNTER),

      (uplead, DataplatformType.UPLEAD),

      (clearbit, DataplatformType.CLEARBIT),

      (anymailfinder, DataplatformType.ANYMAILFINDER),

      (dropcontact, DataplatformType.DROPCONTACT),

      (aeroleads, DataplatformType.AEROLEADS)

    )

    apiKeys.map(dp => {

      // do not expose the full api keys in the api response
      val maskedApiKey = if (dp._1.isEmpty) None else {
        val fullKey = dp._1.get

        Some(Helpers.maskApiKey(key = fullKey))
      }

      Dataplatform(
        api_key = maskedApiKey,
        text_id = dp._2,
        display_name = dp._2.displayName,
        logo_url = dp._2.logoUrl,
        allowed_fields = getAllowedFiledsByDataplatformType(dataplatformType = dp._2)
      )
    })

  }




  implicit val session: AutoSession.type = AutoSession

  def findAllDataplatforms(teamId: Long): Try[Seq[Dataplatform]] =  Try {
    DB readOnly { implicit session =>

      sql"""
           SELECT t.hunter_api_key_enc,
            t.uplead_api_key_enc,
            t.dropcontact_api_key_enc,
            t.anymailfinder_api_key_enc,
            t.clearbit_api_key_enc,
            t.aeroleads_api_key_enc
          from teams t
          WHERE t.id = $teamId
        """
        .map(fromDb)
        .single
        .apply()
        .get

    }



  }

  def getColumnNameByDataplatformType(dataplatformType: DataplatformType): SQLSyntax = {

    val columnName: SQLSyntax = dataplatformType match {

      case DataplatformType.HUNTER =>

        sqls"""hunter_api_key_enc"""

      case DataplatformType.UPLEAD =>

        sqls"""uplead_api_key_enc"""

      case DataplatformType.DROPCONTACT =>

        sqls"""dropcontact_api_key_enc"""

      case DataplatformType.ANYMAILFINDER =>

        sqls"""anymailfinder_api_key_enc"""

      case DataplatformType.CLEARBIT =>

        sqls"""clearbit_api_key_enc"""

      case DataplatformType.AEROLEADS =>

        sqls"""aeroleads_api_key_enc"""

    }

    columnName

  }


  def findApiKeyByDataplatform(teamId: Long, dataplatformType: DataplatformType) = Try {
    DB readOnly { implicit session =>

      val apiKeyColumns: SQLSyntax = getColumnNameByDataplatformType(dataplatformType = dataplatformType)
      sql"""
           SELECT $apiKeyColumns as api_key from teams t
        WHERE t.id = $teamId
        """
        .map(DataplatformInDB.fromDb)
        .single
        .apply()

    }
  }


  def addOrUpdateDataplatform(teamId: Long, dataplatformType: DataplatformType, api_key: String): Try[Option[Long]] = Try {
    DB autoCommit { implicit session =>

      val dataplatformDelete: SQLSyntax = getColumnNameByDataplatformType(dataplatformType = dataplatformType)

      sql"""
           UPDATE teams SET
             $dataplatformDelete = ${EncryptionHelpers.encryptEmailDataplatformCredential(api_key)}
         WHERE id = $teamId
          RETURNING id;
      """
        .map(rs => rs.long("id"))
        .single
        .apply()
    }
  }


  def deleteDataplatform(teamId: Long, dataplatformType: DataplatformType): Try[Option[Long]] = Try {
    DB autoCommit { implicit session =>

      val dataplatformDelete: SQLSyntax = getColumnNameByDataplatformType(dataplatformType = dataplatformType)

      sql"""
           UPDATE teams SET
             $dataplatformDelete = null
         WHERE id = $teamId
          RETURNING id;
      """
        .map(rs => rs.long("id"))
        .single
        .apply()
    }
  }

  def getAllowedFiledsByDataplatformType(dataplatformType: DataplatformType) = {

    dataplatformType match {

      case DataplatformType.UPLEAD =>
        Seq(
          allowedFields(
            display_name = "Job function",
            key = "job_function",
            field_type = "dropdown",
            options = Seq(
              allowedDropdownFiledOptions(key = "Business Development", value = "Business Development", text = "Business Development"),
              allowedDropdownFiledOptions(key = "Community and Social Services", value = "Community and Social Services", text = "Community and Social Services"),
              allowedDropdownFiledOptions(key = "Consulting", value = "Consulting", text = "Consulting"),
              allowedDropdownFiledOptions(key = "Education", value = "Education", text = "Education"),
              allowedDropdownFiledOptions(key = "Engineering and Technical", value = "Engineering and Technical", text = "Engineering and Technical"),
              allowedDropdownFiledOptions(key = "Finance", value = "Finance", text = "Finance"),
              allowedDropdownFiledOptions(key = "Healthcarex1 and Medical", value = "Healthcare and Medical", text = "Healthcare and Medical"),
              allowedDropdownFiledOptions(key = "Human Resources", value = "Human Resources", text = "Human Resources"),
              allowedDropdownFiledOptions(key = "Information Technology", value = "Information Technology", text = "Information Technology"),
              allowedDropdownFiledOptions(key = "Legal", value = "Legal", text = "Legal"),
              allowedDropdownFiledOptions(key = "Marketing", value = "Marketing", text = "Marketing"),
              allowedDropdownFiledOptions(key = "Media and Communications and PR", value = "Media and Communications and PR", text = "Media and Communications and PR"),
              allowedDropdownFiledOptions(key = "Program & Product Management", value = "Program & Product Management", text = "Program & Product Management"),
              allowedDropdownFiledOptions(key = "Purchasing and Buyers", value = "Purchasing and Buyers", text = "Purchasing and Buyers"),
              allowedDropdownFiledOptions(key = "Real Estate", value = "Real Estate", text = "Real Estate"),
              allowedDropdownFiledOptions(key = "Research", value = "Research", text = "Research"),
              allowedDropdownFiledOptions(key = "Sales", value = "Sales", text = "Sales")
            )
          )
        )

      case DataplatformType.CLEARBIT =>

        Seq(
          allowedFields(
            display_name = "Role",
            key = "role",
            field_type = "dropdown",
            options = Seq(
              allowedDropdownFiledOptions(key = "ceo", value = "ceo", text = "CEO"),
              allowedDropdownFiledOptions(key = "communications", value = "communications", text = "Communications"),
              allowedDropdownFiledOptions(key = "consulting", value = "consulting", text = "Consulting"),
              allowedDropdownFiledOptions(key = "customer_service", value = "customer_service", text = "Customer service"),
              allowedDropdownFiledOptions(key = "education", value = "education", text = "Education"),
              allowedDropdownFiledOptions(key = "engineering", value = "engineering", text = "engineering"),
              allowedDropdownFiledOptions(key = "finance", value = "finance", text = "Finance"),
              allowedDropdownFiledOptions(key = "founder", value = "founder", text = "Founder"),
              allowedDropdownFiledOptions(key = "health_professional", value = "health_professional", text = "Health professional"),
              allowedDropdownFiledOptions(key = "human_resources", value = "human_resources", text = "Human resources"),
              allowedDropdownFiledOptions(key = "information_technology", value = "information_technology", text = "Information technology"),
              allowedDropdownFiledOptions(key = "legal", value = "legal", text = "Legal"),
              allowedDropdownFiledOptions(key = "marketing", value = "marketing", text = "Marketing"),
              allowedDropdownFiledOptions(key = "operations", value = "operations", text = "Operations"),
              allowedDropdownFiledOptions(key = "owner", value = "owner", text = "Owner"),
              allowedDropdownFiledOptions(key = "president", value = "president", text = "President"),
              allowedDropdownFiledOptions(key = "product", value = "product", text = "Product"),
              allowedDropdownFiledOptions(key = "public_relations", value = "public_relations", text = "Public relations"),
              allowedDropdownFiledOptions(key = "real_estate", value = "real_estate", text = "Real estate"),
              allowedDropdownFiledOptions(key = "recruiting", value = "recruiting", text = "Recruiting"),
              allowedDropdownFiledOptions(key = "research", value = "research", text = "Research"),
              allowedDropdownFiledOptions(key = "sales", value = "sales", text = "Sales")
            )
          )
        )

      case _ =>

        Seq()

    }

  }


  def addSearchLog(teamId: Long, accountId: Long, dataplatformType: DataplatformType, input: JsValue, output: JsValue): Option[Long] = {
    DB autoCommit { implicit session =>


      logger.info(s""" INSERT INTO email_finder_search_requests_log ( account_id, team_id, dataplatform_type, request_input, request_output ) VALUES ( $accountId, $teamId, ${dataplatformType.toString}, to_jsonb(${input.toString()}::jsonb) ${output.toString()} ) RETURNING id """)

      sql"""
          INSERT INTO email_finder_search_requests_log
          (
            account_id,
            team_id,
            dataplatform_type,
            request_input,
            request_output
          )
          VALUES (
            $accountId,
            $teamId,
            ${dataplatformType.toString},
            to_jsonb(${input.toString()}::jsonb),
            to_jsonb(${output.toString()}::jsonb)

          )
          RETURNING id;
      """
        .map(rs => rs.long("id"))
        .single
        .apply()
    }
  }

}
