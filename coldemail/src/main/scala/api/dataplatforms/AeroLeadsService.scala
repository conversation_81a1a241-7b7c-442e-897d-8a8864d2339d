package api.dataplatforms

import org.apache.pekko.actor.ActorSystem
import api.CONSTANTS
import api.prospects.ProspectCreateFormData
import play.api.libs.json.{JsValue, Json}
import play.api.libs.ws.{WSClient, WSResponse}
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}

object AeroLeadsService extends TDataPlatformService {


  def searchContact(
    data: DataplatformSearchRequest,
    apiKey: String,
    logRawResponseInDB: (WSResponse) => Unit
  )(implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger, system: ActorSystem): Future[Seq[ProspectCreateFormData]]  = {

    val baseUrl = "https://aeroleads.com/apis/details"

    ws.url(s"$baseUrl")
      .withQueryStringParameters(
        "first_name" -> data.first_name.getOrElse(""),
        "last_name" -> data.last_name.getOrElse(""),
        "company_url" -> data.domain,
        "api_key" -> apiKey
      )
      .get()
      .map(res => {

        logRawResponseInDB(res)

        if(res.status == 200) {

          val contacts = (res.json \  "emails").as[List[JsValue]]

          if(contacts.nonEmpty && res.body != "Credit Limit Reached.") {

            contacts
              .filter(contact => ((contact \ "status").asOpt[String].getOrElse("")) == "1.0" && ((contact \ "status").as[String]) == "1")
              .map(contact => {
              ProspectCreateFormData(
                email = (contact \ "email").asOpt[String],
                first_name = data.first_name,
                last_name = data.last_name,
                custom_fields = Json.obj(),

                list = None,
                company = None,
                city = None,
                state = None,
                country = None,
                timezone = None,
                created_at = None,

                job_title = None,
                phone = None,
                phone_2 = None,
                phone_3 = None,
                linkedin_url = None
              )
            })
          } else {

            throw new Exception(CONSTANTS.DATAPLATFROM_API_ERROR_KEYS.CONTACTS_NOT_FOUND_ERROR)

          }
        } else if(res.body == "Credit Limit Reached.") {

          throw new Exception("Credit Limit Reached.")

        } else if(res.status == 524) {

          throw new Exception("Request Timed out")

        } else {

          throw new Exception((res.json \ "error" \ "message").as[String])

        }
      })
  }

  def batchSearch(
    data: Seq[DataplatformSearchRequestBatch],
    apiKey: String,
    logRawResponseInDB: (WSResponse) => Unit
  )(implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger, system: ActorSystem): Future[Seq[ProspectCreateFormData]]  = {

    val baseUrl = "https://aeroleads.com/apis/details"

    val req = data.head

    val domain = req.domain.getOrElse(req.company)

      ws.url(s"$baseUrl")
        .withQueryStringParameters(
          "first_name" -> req.first_name,
          "last_name" -> req.last_name,
          "company_url" -> domain,
          "api_key" -> apiKey
        )
        .get()
        .map(res => {

          logRawResponseInDB(res)

          if (res.status == 200 && res.body != "Credit Limit Reached.") {

            val contacts = (res.json \ "emails").as[List[JsValue]]

            if (contacts.nonEmpty) {

              contacts
                .filter(contact => ((contact \ "status").asOpt[String].getOrElse("") == "1.0" || (contact \ "status").asOpt[String].getOrElse("") == "1" ))
                .map(contact => {
                  ProspectCreateFormData(
                    email = (contact \ "email").asOpt[String],
                    first_name = Some(req.first_name),
                    last_name = Some(req.last_name),
                    custom_fields = Json.obj(),

                    list = None,
                    company = None,
                    city = None,
                    state = None,
                    country = None,
                    timezone = None,
                    created_at = None,

                    job_title = None,
                    phone = None,
                    phone_2 = None,
                    phone_3 = None,
                    linkedin_url = None
                  )
                })
            } else {

              throw new Exception(CONSTANTS.DATAPLATFROM_API_ERROR_KEYS.CONTACTS_NOT_FOUND_ERROR)

            }
          } else if(res.body == "Credit Limit Reached.") {

            throw new Exception("Credit Limit Reached.")

          } else if(res.status == 524) {

            throw new Exception("Request Timed out")

          } else {

            throw new Exception((res.json \ "error" \ "message").as[String])

          }
        })

  }


  def checkAPIAccess(data: DataplatformSearchRequest, apiKey: String)(implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger): Future[Boolean] = {
    val baseUrl = "https://aeroleads.com/apis/details"
    ws.url(baseUrl)
      .withQueryStringParameters(
        "first_name" -> data.first_name.getOrElse(""),
        "last_name" -> data.last_name.getOrElse(""),
        "company_url" -> data.domain,
        "api_key" -> apiKey
      )
      .get()
      .map(res => {
        if (res.status == 200) true else false
      })
  }
}

