package api.twilio.service

import api.AppConfig
import api.AppConfig.{RollingUpdates, srApiBaseUrl}
import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.call.DAO.{ParticipantDetails, ParticipantUpdateDetails}
import api.call.controller.TwilioIdentityAndToken
import api.call.models.CallLogResource.{CallLogUUID, NewCallLog}
import api.call.service.CallService
import api.call.models.{CallAccountSettings, CallDetails, CallFeedBackReasons, CallParticipationMode, CallSID, CallStatus, CallStatusChangeData, CallType, CallerIdentity, CallingRemainingCredits, CallingServiceProvider, ConferenceFriendlyName, ConferenceSid, ConferenceUuid, InitialCallParticipationModeData, NumberPriceObject, OutgoingBasicCallStatusCallbackData, OutgoingCallerIdSid, ParticipantCallStatus, PhoneNumberCreditRecordType, PhoneNumberCreditsRecords, PhoneNumberUuid, PhoneSID, PriceObject, SubAccountDetails, SubAccountUuid, TeamIdAndAccountDetails, TwlAuthToken, TwlSubAccountSid, UsageDetails, PhoneNumber as PhoneNumberValueClass}
import api.call.traits.{CallForwardingError, GetNumberPriceError, GetSubAccountUsageError}
import api.prospects.models.ProspectId
import api.tasks.services.TaskUuid
import api.twilio.service.TwilioDialerService.parseCurrencyInCents
import api_layer_models.ConvertedCurrency.getPriceInMillis
import api_layer_models.CurrencyType
import com.twilio.rest.api.v2010.account.{Application, Balance, Call, NewKey, OutgoingCallerId, ValidationRequest, Conference as ConferenceUpdater}
import com.twilio.`type`.PhoneNumberPrice
import com.twilio.base.{Resource, ResourceSet}
import com.twilio.http.{HttpMethod, TwilioRestClient}
import com.twilio.rest.api.v2010.Account
import com.twilio.rest.api.v2010.account.usage.{Record, Trigger}
import com.twilio.rest.api.v2010.account.call.Feedback
import com.twilio.rest.api.v2010.account.conference.Participant
import com.twilio.rest.api.v2010.account.usage.record.{LastMonth, ThisMonth}
import com.twilio.rest.insights.v1.call.Annotation
import com.twilio.twiml.voice.{Conference, Enqueue, Gather, Pause, Play, Redirect}
import com.twilio.security.RequestValidator
import utils.jodatimeutils.JodaTimeUtils
import org.joda.time.{DateTime, DateTimeZone, LocalDateTime}
import play.api.libs.json.{Json, OFormat}
import play.api.libs.json.JodaReads.*
import play.api.libs.json.JodaWrites.*
import utils.jodatimeutils.JodaTimeUtils.JavaTimeLocalDateToDateTime
import utils.uuid.SrUuidUtils
import utils.{SRLogger, StringUtilsV2}

import java.net.URI
import java.time.{Instant, ZoneId, ZoneOffset, ZonedDateTime}
import java.util
import java.util.{Collections, HashMap, TimeZone}
import scala.util.{Failure, Success, Try}
// Token generation imports
import com.twilio.jwt.accesstoken.{AccessToken, VoiceGrant}
// TwiML generation imports
import com.twilio.twiml.VoiceResponse
import com.twilio.twiml.voice.{Client, Dial, Number, Say}

import com.twilio.Twilio
import com.twilio.rest.api.v2010.account.AvailablePhoneNumberCountry
import com.twilio.rest.api.v2010.account.availablephonenumbercountry.Local
import com.twilio.rest.api.v2010.account.IncomingPhoneNumber
import com.twilio.`type`.PhoneNumber
import scala.jdk.CollectionConverters._


import com.twilio.rest.pricing.v1.phonenumber.Country


case class ApiKey(key: String) extends AnyVal

case class TwlTwimlAppSid(
                           sid: String
                         ) extends AnyVal

case class TwlTwimlAppName(
                            name: String
                          ) extends AnyVal

case class SubAccountTwimlApplication(
                                       app_sid: TwlTwimlAppSid,
                                       app_name: TwlTwimlAppName,
                                       voice_url: String
                                     )

case class ApiSecret(secret: String) extends AnyVal

case class SubAccountApiKeyAndSecret(
                                      api_key: ApiKey,
                                      api_secret: ApiSecret
                                    )













case class ParticipantDetailsFromWebhook(
                                          call_from: PhoneNumberValueClass,
                                          participant_label: String,
                                          call_participant_phone: PhoneNumberValueClass,
                                          is_prospect: Boolean,
                                          call_sid: Option[CallSID]
                                        )

case class NewConfCallLog(
                           conference_sid: Option[ConferenceSid],
                           conference_name: Option[ConferenceFriendlyName],
                           from_phone_number: PhoneNumberValueClass,
                           call_setting_uuid: PhoneNumberUuid,
                           service_provider: CallingServiceProvider,
                           status: CallStatus,
                           task_uuid: TaskUuid,
                           primary_prospect_id: ProspectId,
                           to_phone_number: PhoneNumberValueClass,
                         )

object NewConfCallLog {
  given format: OFormat[NewConfCallLog] = Json.format[NewConfCallLog]
}

case class CallEndLogRequest(
                              call_status: CallStatus,
                              call_ended_at: DateTime,
                              call_picked_up_at: Option[DateTime]
                            )

object CallEndLogRequest {
  given format: OFormat[CallEndLogRequest] = Json.format[CallEndLogRequest]
}

case class CallLog(
                    conference_uuid: ConferenceUuid,
                    conference_sid: Option[ConferenceSid],
                    conference_name: Option[ConferenceFriendlyName],
                    initiated_by: PhoneNumberValueClass,
                    service_provider: CallingServiceProvider,
                    status: CallStatus,
                    task_uuid: TaskUuid,
                    primary_prospect_id: Option[ProspectId]
                  )

object CallLog {
  given format: OFormat[CallLog] = Json.format[CallLog]
}

case class ConferenceCallUpdateLog(
                                    call_uuid: ConferenceUuid,
                                    call_ended_at: DateTime,
                                    call_status: CallStatus,
                                  )

object ConferenceCallUpdateLog {
  given format: OFormat[ParticipantCallUpdateLog] = Json.format[ParticipantCallUpdateLog]
}

case class ParticipantCallUpdateLog(
                                     call_uuid: ConferenceUuid,
                                     call_ended_at: DateTime,
                                     picked_up_at: Option[DateTime],
                                     call_status: CallStatus,
                                   )

object ParticipantCallUpdateLog {
  given format: OFormat[ParticipantCallUpdateLog] = Json.format[ParticipantCallUpdateLog]
}

case class ConferenceDetailsFromWebhook(
                                         conference_sid: Option[String],
                                         conference_name: Option[String],
                                         initiated_by: PhoneNumberValueClass,
                                         service_provider: CallingServiceProvider,
                                         callType: CallType
                                       )

case class TwilioConferenceAndParticipantDetails(
                                                  response: String,
                                                  conference_details: Option[ConferenceDetailsFromWebhook],
                                                  participantDetails: Seq[ParticipantDetailsFromWebhook],

                                                )

/*
 owner_account_id: parent_account_id of the sub-account
 */
case class TwilioSubAccountDetails(
                                    sub_account_sid: TwlSubAccountSid,
                                    auth_token: TwlAuthToken,
                                    sub_account_name: String,
                                    status: String,
                                    owner_account_sid: String,
                                  )

/*
  API DOCS : https://www.twilio.com/docs/usage/api/usage-trigger?code-sample=code-update-a-usagetrigger-resource&code-language=curl&code-sdk-version=json
  trigger_value: The value at which the trigger will fire. Must be a positive, numeric value.
  trigger_current_value: The current value of the field the trigger is watching.
 */
case class TwilioUsageTrigger(
                               trigger_id: String,
                               trigger_value: String,
                               trigger_by: String,
                               trigger_name: String,
                               trigger_webhook: String,
                               trigger_recurring: String,
                               trigger_current_value: String,
                               trigger_usage_category: String,
                               trigger_last_fired: DateTime,
                               sub_account_sid: String
                             )


//import com.twilio.twiml.{Client, Dial, Number, Say}

class TwilioDialerService {

  private val twilioVars = AppConfig.TwilioService
  private val trigger_webhook = AppConfig.srApiBaseUrl


  def createJsonAccessToken(
                             caller_identity: CallerIdentity,
                             api_key: ApiKey,
                             api_secret: ApiSecret,
                             twiml_sid: TwlTwimlAppSid,
                             sub_account_sid: TwlSubAccountSid
                           ): Try[TwilioIdentityAndToken] = Try {
    //    val acctSid = twilioVars.acctSid
    //    val applicationSid = twilioVars.applicationSid
    //    val apiKey = twilioVars.apiKey
    //    val apiSecret = twilioVars.apiSecret

    val acctSid = sub_account_sid.id
    val applicationSid = twiml_sid.sid
    val apiKey = api_key.key
    val apiSecret = api_secret.secret
    // Create Voice grant
    val grant = new VoiceGrant
    grant.setOutgoingApplicationSid(applicationSid)
    // Optional: add to allow incoming calls
    grant.setIncomingAllow(true)
    // Create access token
    //        System.out.println('\n\n debug acSID' ,${acctSid} ,'apiKey', ${apiKey}, 'apiSecret',  ${apiSecret});
    //    println("\n\n debug acSID " + acctSid + " apiKey " + apiKey + " apiSecret " + apiSecret)
    // printf('\n\n debug acSID ', acctSid, " apiKey ", apiKey, " apiSecret ", apiSecret , " \n\n");

    getAccessToken(
      acctSid = acctSid,
      apiKey = apiKey,
      apiSecret = apiSecret,
      identity = caller_identity.identity,
      grant = grant
    ) match {

      case Failure(err) =>
        throw new Exception(err)

      case Success(accessToken) =>

        val token = accessToken.toJwt
        // create JSON response payload
        //    val json = new util.HashMap[String, String]
        //    (identity, token)
        TwilioIdentityAndToken(
          identity = caller_identity.identity,
          token = token
        )

    }
    //    json.put("identity", identity)
    //    json.put("token", token)
    //    json
  }

  private def getAccessToken(
                              acctSid: String,
                              apiKey: String,
                              apiSecret: String,
                              identity: String,
                              grant: VoiceGrant
                            ): Try[AccessToken] = Try {
    new AccessToken.Builder(acctSid, apiKey, apiSecret).ttl(21600).identity(identity).grant(grant).build
  }

  private def isPhoneNumber(to: String) = TwilioDialerService.isPhoneNumber(number = to)

  private def addChildReceiver(builder: Dial.Builder, to: String, tid: TeamId): Dial.Builder = { // wrap the phone number or client name in the appropriate TwiML verb
    // by checking if the number given has only digits and format symbols

    val client_event_list = List(
      Client.Event.COMPLETED,
      Client.Event.INITIATED,
      Client.Event.RINGING,
      Client.Event.ANSWERED
    )
    val event_list_number = List(
      Number.Event.COMPLETED,
      Number.Event.INITIATED,
      Number.Event.RINGING,
      Number.Event.ANSWERED
    )
    if (isPhoneNumber(to)) {
      builder.number(new Number.Builder(to)
        .statusCallback(TwilioDialerService.outgoingCallStatusCallback(tid = tid))
        .statusCallbackEvents(event_list_number.asJava)
        .build)
    } else builder.client(new Client.Builder(to)
      .statusCallback(TwilioDialerService.outgoingCallStatusCallback(tid = tid))
      .statusCallbackEvents(client_event_list.asJava)
      .build)
  }

  def getConferenceBuilder(
                            conference_id: ConferenceSid,
                            record_call: Boolean,
                            tid: TeamId
                          ): Conference.Builder = if (record_call) {

    new Conference
    .Builder(conference_id.sid)
      //      .participantLabel("extension_id_2")
      .startConferenceOnEnter(true)
      .record(Conference.Record.RECORD_FROM_START)
      .recordingStatusCallback(TwilioDialerService.recordCallWebhookReplyUrl(tid = tid))
  } else {

    new Conference
    .Builder(conference_id.sid)
      //      .participantLabel("extension_id_2")
      .startConferenceOnEnter(true)
  }

  def handleIncomingCall(
                          caller_identity: CallerIdentity,
                          is_forward_enabled: Boolean,
                          forward_to: Option[String],
                          call_details: CallDetails,
                          twl_sub_account_sid: TwlSubAccountSid,
                          record_call: Boolean,
                          tid: TeamId,
                          twl_auth_token: TwlAuthToken,
                          orgId: OrgId
                        )(using logger: SRLogger): Try[TwilioConferenceAndParticipantDetails] = Try {

    val ACCOUNT_SID = twl_sub_account_sid.id
    val AUTH_TOKEN = twl_auth_token.token

    Twilio.init(ACCOUNT_SID, AUTH_TOKEN)
    val event_list = List(
      Client.Event.COMPLETED,
      Client.Event.INITIATED,
      Client.Event.RINGING,
      Client.Event.ANSWERED
    )


    logger.info(s"[hanldeIncomingCall] tid: ${tid} identity : ${caller_identity.identity}, " +
      s"call_details :: ${call_details}")
    if (RollingUpdates.orgIdsForIncomingCalls.contains(orgId.id)) {
      val client = new Client.Builder(caller_identity.identity)
        .statusCallback(TwilioDialerService.incomingCallStatusCallback(tid = tid))
        //registered callback for incoming call
        .statusCallbackEvents(event_list.asJava)
        .build()

      val voice_response = TwilioDialerService.handlingIncomingCalls(
        client = client,
        is_forward_enabled = is_forward_enabled,
        forward_to = forward_to
      )

      TwilioConferenceAndParticipantDetails(
        conference_details = Some(ConferenceDetailsFromWebhook(
          conference_sid = None,
          conference_name = None,
          initiated_by = call_details.from,
          service_provider = CallingServiceProvider.TWILIO,
          callType = CallType.TWL_BASIC
        )),
        participantDetails = Seq(ParticipantDetailsFromWebhook(
          call_from = call_details.from,
          participant_label = AppConfig.Calling.customer_label,
          call_participant_phone = call_details.from,
          is_prospect = true,
          call_sid = Some(call_details.call_sid)
        ),
          ParticipantDetailsFromWebhook(
            call_from = call_details.from,
            participant_label = "", //FIXME : CHECK THIS -> change this to option[string] and pass none
            call_participant_phone = call_details.to,
            is_prospect = false,
            call_sid = Some(call_details.call_sid)
          )
        ),
        response = voice_response.toXml(),
      )

    } else if (is_forward_enabled && forward_to.nonEmpty) {

      val voice_response = TwilioDialerService.forwardingIncomingCalls(
        is_forward_enabled = is_forward_enabled,
        forward_to = forward_to
      )

      TwilioConferenceAndParticipantDetails(
        conference_details = None,
        participantDetails = Seq(),
        response = voice_response.toXml(),

      )

    } else {

      throw new Exception("Incoming call not possible")

    }
  }

  def handleVerifyCallerId(
                            twl_sub_account_sid: TwlSubAccountSid,
                            twl_auth_token: TwlAuthToken,
                            phone_number: PhoneNumberValueClass,
                            friendlyName: String,
                            teamId: TeamId
                          ): Try[String] = Try {
    val ACCOUNT_SID = twl_sub_account_sid.id
    val AUTH_TOKEN = twl_auth_token.token

    Twilio.init(ACCOUNT_SID, AUTH_TOKEN)


    val validationRequest: ValidationRequest =
      ValidationRequest.creator(new PhoneNumber(phone_number.phone_number))
        .setFriendlyName(friendlyName)
        .setStatusCallback(TwilioDialerService.verifyCallerIdStatusCallback(tid = teamId))
        .create();


    //    println("verification id::" ,validationRequest.getValidationCode )


    validationRequest.getValidationCode

  }

  def removeCallerId(
                      twl_sub_account_sid: TwlSubAccountSid,
                      twl_auth_token: TwlAuthToken,
                      outgoingCallerIdSid: OutgoingCallerIdSid,
                      teamId: TeamId
                    ): Try[Boolean] = Try {

    val ACCOUNT_SID = twl_sub_account_sid.id
    val AUTH_TOKEN = twl_auth_token.token

    Twilio.init(ACCOUNT_SID, AUTH_TOKEN)


    val res = OutgoingCallerId.deleter(
      outgoingCallerIdSid.toString
    ).delete()

    res

  }

  def handleOutgoingBasicCall(
                               twl_sub_account_sid: TwlSubAccountSid,
                               twl_auth_token: TwlAuthToken,
                               call_details: CallDetails,
                               call_from_number: String,
                               record_call: Boolean,
                               tid: TeamId
                             )(using logger: SRLogger): Try[TwilioConferenceAndParticipantDetails] = Try {

    val ACCOUNT_SID = twl_sub_account_sid.id
    val AUTH_TOKEN = twl_auth_token.token

    Twilio.init(ACCOUNT_SID, AUTH_TOKEN)
    val record = if (record_call) {
      Dial.Record.RECORD_FROM_ANSWER
    } else {
      Dial.Record.DO_NOT_RECORD
    }

    val dialBuilder = new Dial.Builder().
      callerId(call_from_number)
      .record(record)
      .recordingStatusCallbackEvents(Dial.RecordingEvent.COMPLETED)
      .recordingStatusCallback(TwilioDialerService.recordCallWebhookReplyUrl(tid = tid))

    val dialBuilderWithReceiver = addChildReceiver(
      builder = dialBuilder,
      to = call_details.to.phone_number,
      tid = tid
    )


    val conference_details: ConferenceDetailsFromWebhook = ConferenceDetailsFromWebhook(
      conference_sid = None,
      conference_name = None,
      initiated_by = call_details.from,
      service_provider = CallingServiceProvider.TWILIO,
      callType = CallType.TWL_BASIC

    )

    val participants: Seq[ParticipantDetailsFromWebhook] = Seq(
      ParticipantDetailsFromWebhook(
        call_from = call_details.from,
        participant_label = AppConfig.Calling.customer_label,
        call_participant_phone = call_details.to,
        is_prospect = true, //assuming all outgoing calls are to prospects
        call_sid = Some(call_details.call_sid)
      ),
      ParticipantDetailsFromWebhook(
        call_from = call_details.from,
        participant_label = "initiator",
        call_participant_phone = call_details.from,
        is_prospect = false, //assuming all outgoing calls are to prospects
        call_sid = Some(call_details.call_sid)
      )
    )

    TwilioConferenceAndParticipantDetails(
      conference_details = Some(conference_details),
      participantDetails = participants,
      response = new VoiceResponse.Builder().dial(dialBuilderWithReceiver.build).build.toXml()
    )


  }


  def playVoiceDrop(
                     customerCallSid: CallSID,
                     initiatorCallSid: CallSID,
                     voiceDropUrl: String,
                     twl_sub_account_sid: TwlSubAccountSid,
                     twl_auth_token: TwlAuthToken
                   )(using logger: SRLogger): Try[CallSID] =
    Try {
      // Initialize Twilio client
      val ACCOUNT_SID = twl_sub_account_sid.id
      val AUTH_TOKEN = twl_auth_token.token

      Twilio.init(ACCOUNT_SID, AUTH_TOKEN)

      // TwiML for SID B (Play voice drop)
      val responseForSidB = new VoiceResponse.Builder()
        .play(new Play.Builder(voiceDropUrl).build()) // Play voice drop for SID B
        .build()


      Call.updater(customerCallSid.sid).setTwiml(responseForSidB.toXml).update() // Play voice drop for SID B


      initiatorCallSid
    }



  def getCreditsSpentOnPhoneNumbers(
                                     twl_sub_account_sid: TwlSubAccountSid,
                                     twl_auth_token: TwlAuthToken,

                                   ): Try[Seq[PhoneNumberCreditsRecords]] = Try {
    val ACCOUNT_SID = twl_sub_account_sid.id
    val AUTH_TOKEN = twl_auth_token.token

    /*
    Record(accountSid=ACad43ca9078d9f3f85323d66ab8844e2a,
    apiVersion=2010-04-01,
    asOf=2024-08-28T11:10:23+00:00,
    category=phonenumbers,
    count=56,
    countUnit=numbers,
    description=Phone Numbers,
    endDate=2024-08-28,
    price=64.4,
    priceUnit=USD,
    startDate=2023-08-18,
    subresourceUris={
    all_time=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/AllTime.json?Category=phonenumbers,
    today=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Today.json?Category=phonenumbers,
    yesterday=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Yesterday.json?Category=phonenumbers,
    this_month=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/ThisMonth.json?Category=phonenumbers,
    last_month=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/LastMonth.json?Category=phonenumbers,
    daily=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Daily.json?Category=phonenumbers,
    monthly=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Monthly.json?Category=phonenumbers,
    yearly=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Yearly.json?Category=phonenumbers},
    uri=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records.json?Category=phonenumbers&StartDate=2023-08-18&EndDate=2024-08-28,
    usage=56, usageUnit=numbers)


    Record(accountSid=ACad43ca9078d9f3f85323d66ab8844e2a,
    apiVersion=2010-04-01,
    asOf=2024-08-28T11:10:19+00:00,
    category=phonenumbers,
    count=56,
    countUnit=numbers,
    description=Phone Numbers,
    endDate=2024-08-28,
    price=64.4,
    priceUnit=USD,
    startDate=2023-08-18,
    subresourceUris={
    all_time=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/AllTime.json?Category=phonenumbers,
    today=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Today.json?Category=phonenumbers,
    yesterday=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Yesterday.json?Category=phonenumbers,
    this_month=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/ThisMonth.json?Category=phonenumbers,
    last_month=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/LastMonth.json?Category=phonenumbers,
    daily=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Daily.json?Category=phonenumbers,
    monthly=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Monthly.json?Category=phonenumbers,
    yearly=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Yearly.json?Category=phonenumbers},
    uri=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records.json?Category=phonenumbers&StartDate=2023-08-18&EndDate=2024-08-28,
    usage=56, usageUnit=numbers))

    LastMonth(accountSid=ACad43ca9078d9f3f85323d66ab8844e2a,
    apiVersion=2010-04-01,
    asOf=2024-09-02T12:50:19+00:00,
    category=phonenumbers,
    count=5,
    countUnit=numbers,
    description=Phone Numbers,
    endDate=2024-08-31,
    price=5.75,
    priceUnit=USD,
    startDate=2024-08-01,
    subresourceUris={
    all_time=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/AllTime.json?Category=phonenumbers,
    today=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Today.json?Category=phonenumbers,
    yesterday=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Yesterday.json?Category=phonenumbers,
    this_month=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/ThisMonth.json?Category=phonenumbers,
    last_month=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/LastMonth.json?Category=phonenumbers,
    daily=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Daily.json?Category=phonenumbers,
    monthly=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Monthly.json?Category=phonenumbers,
    yearly=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Yearly.json?Category=phonenumbers},
    uri=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/LastMonth.json?Category=phonenumbers&StartDate=2024-08-01&EndDate=2024-08-31,
    usage=5, usageUnit=numbers)



    ThisMonth(accountSid=ACad43ca9078d9f3f85323d66ab8844e2a,
    apiVersion=2010-04-01,
    asOf=2024-09-03T07:38:38+00:00,
    category=phonenumbers,
    count=0,
    countUnit=numbers,
    description=Phone Numbers,
    endDate=2024-09-03,
    price=0,
    priceUnit=USD,
    startDate=2024-09-01,
    subresourceUris=
    {all_time=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/AllTime.json?Category=phonenumbers,
    today=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Today.json?Category=phonenumbers,
    yesterday=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Yesterday.json?Category=phonenumbers,
    this_month=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/ThisMonth.json?Category=phonenumbers,
    last_month=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/LastMonth.json?Category=phonenumbers,
    daily=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Daily.json?Category=phonenumbers,
    monthly=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Monthly.json?Category=phonenumbers,
    yearly=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/Yearly.json?Category=phonenumbers},
    uri=/2010-04-01/Accounts/ACad43ca9078d9f3f85323d66ab8844e2a/Usage/Records/ThisMonth.json?Category=phonenumbers&StartDate=2024-09-01&EndDate=2024-09-03,
    usage=0,
    usageUnit=numbers)
     */

    Twilio.init(ACCOUNT_SID, AUTH_TOKEN)

    val thisMonthRecords: ThisMonth = ThisMonth.reader.
      setCategory(ThisMonth.Category.PHONENUMBERS)
      .limit(1).read().asScala.head

    val totalUsageRecords: Record = Record.reader()
      .setCategory(Record.Category.PHONENUMBERS)
      .limit(1)
      .read().asScala.head

    val lastMonthUsageRecord = LastMonth.reader.
      setCategory(LastMonth.Category.PHONENUMBERS)
      .limit(1).read().asScala.head

    // Output the total cost

    Seq(PhoneNumberCreditsRecords(
      credits_type = PhoneNumberCreditRecordType.ThisMonth,
      price = thisMonthRecords.getPrice.doubleValue(),
      unit = thisMonthRecords.getPriceUnit.toString,
      from = DateTime.parse(thisMonthRecords.getAsOf),
      till = JavaTimeLocalDateToDateTime(thisMonthRecords.getEndDate)

    ), PhoneNumberCreditsRecords(
      credits_type = PhoneNumberCreditRecordType.LastMonth,
      price = lastMonthUsageRecord.getPrice.doubleValue(),
      unit = lastMonthUsageRecord.getPriceUnit.toString,
      from = DateTime.parse(lastMonthUsageRecord.getAsOf),
      till = JavaTimeLocalDateToDateTime(lastMonthUsageRecord.getEndDate)

    ), PhoneNumberCreditsRecords(
      credits_type = PhoneNumberCreditRecordType.AllTime,
      price = totalUsageRecords.getPrice.doubleValue(),
      unit = totalUsageRecords.getPriceUnit.toString,
      from = DateTime.parse(totalUsageRecords.getAsOf),
      till = JavaTimeLocalDateToDateTime(totalUsageRecords.getEndDate)

    )


    )

  }


  def getCallStatus(
                     call_sid: CallSID,
                     twl_sub_account_sid: TwlSubAccountSid,
                     twl_auth_token: TwlAuthToken,
                     conf_uuid: ConferenceUuid
                   ): Try[CallStatusChangeData] = Try {

    val ACCOUNT_SID = twl_sub_account_sid.id
    val AUTH_TOKEN = twl_auth_token.token

    Twilio.init(ACCOUNT_SID, AUTH_TOKEN)

    val call: Call = Call.fetcher(call_sid.sid).fetch();

    val status: ParticipantCallStatus = ParticipantCallStatus.fromString(call.getStatus.toString).get

    val ended_at: Option[DateTime] = status match {
      case ParticipantCallStatus.RINGING |
           ParticipantCallStatus.INITIATED |
           ParticipantCallStatus.QUEUED |
           ParticipantCallStatus.IN_PROGRESS => None

      case ParticipantCallStatus.COMPLETED |
           ParticipantCallStatus.NO_ANSWER |
           ParticipantCallStatus.BUSY |
           ParticipantCallStatus.CANCELED |
           ParticipantCallStatus.FAILED =>


        Some(new DateTime(
          call.getEndTime.toInstant().toEpochMilli()
        ))
    }


    CallStatusChangeData(
      ended_at = ended_at,
      status = status,
      call_sid = call_sid,
      conference_uuid = conf_uuid,
    )


  }

  def handleOutGoingCall(
                          twl_sub_account_sid: TwlSubAccountSid,
                          twl_auth_token: TwlAuthToken,
                          call_details: CallDetails,
                          call_from_number: String,
                          record_call: Boolean,
                          tid: TeamId,
                          conference_friendly_name: Option[ConferenceFriendlyName]
                        )(using logger: SRLogger): Try[TwilioConferenceAndParticipantDetails] = Try {

    val voice_response: Try[TwilioConferenceAndParticipantDetails] = call_details.call_participant_data match {

      case data: InitialCallParticipationModeData.ListenModeData =>

        conference_friendly_name match {

          case None => Failure(new Exception(s"no conference friendly name found for conference uuid:: ${data.conference_uuid.conf_uuid}"))

          case Some(conf_name) =>
            val conference = new Conference
            .Builder(conf_name.name)
              //      .participantLabel("extension_id_2")
              //              .startConferenceOnEnter(true)
              //          .record(Conference.Record.RECORD_FROM_START)
              //          .recordingStatusCallback("https://sendwebhookhere.com/")
              .muted(true)

            val dial_builder = new Dial.Builder().conference(
              conference
                .build()
            )

            val participants: Seq[ParticipantDetailsFromWebhook] = Seq(
              ParticipantDetailsFromWebhook(
                call_from = call_details.from,
                participant_label = "listener", //FIXME CALL: Need to discuss what labels to use
                call_participant_phone = call_details.from,
                is_prospect = false, // no prospect can listen
                call_sid = Some(call_details.call_sid))
            )
            Success(TwilioConferenceAndParticipantDetails(
              conference_details = None,
              participantDetails = participants,
              response = new VoiceResponse.Builder().dial(dial_builder.build).build.toXml(),
            ))
        }

      case InitialCallParticipationModeData.InitiatorModeData =>

        val ACCOUNT_SID = twl_sub_account_sid.id
        val AUTH_TOKEN = twl_auth_token.token

        Twilio.init(ACCOUNT_SID, AUTH_TOKEN)

        val conference_name = "conf_" + call_details.call_sid // Fixme call - recheck this

        val conference_id = ConferenceSid(sid = conference_name) // Fixme -call call_details.conference_id


        val conference = getConferenceBuilder(
          conference_id = conference_id,
          record_call = record_call,
          tid = tid
        )

        // outbound calls logic
        val dialBuilder = new Dial.Builder().conference(
          conference.build()
        )
        //        .callerId(call_from_number)
        // System.getenv("TWILIO_CALLER_ID")
        //      val dialBuilderWithReceiver = addChildReceiver(dialBuilder, call_details.to.phone_number)

        val participant_a: Participant =
          Participant
            .creator(conference_id.sid,
              new PhoneNumber(call_from_number),
              new PhoneNumber(call_details.to.toString))
            .setLabel("customer") // Fixme call
            .setEarlyMedia(true)
            .setBeep("onEnter")
            .setEndConferenceOnExit(true)
            .setStatusCallbackMethod(HttpMethod.POST)
            .setStatusCallback(TwilioDialerService.prospectConferenceStatusCallbackUrl(tid = tid))
            //Registering the webhook for prospect status [CallController.prospectStatusCallback]
            .setStatusCallbackEvent(List("initiated", "ringing", "completed", "answered").asJava)
            .setConferenceStatusCallback(TwilioDialerService.conferenceParticipantStatusCallbackUrl(tid = tid))
            //Registering the webhook for participant status [CallController.conferenceParticipantStatus] ->
            // all participants except prospect
            .setConferenceStatusCallbackEvent(List("join", "leave", "start", "end").asJava)
            .setRecord(true).create

        //        logger.info(s"conference sid : ${participant_a.getConferenceSid}")
        //        logger.info(s"call sid : ${participant_a.getCallSid}")
        //        logger.info(s"call sid to coach  : ${participant_a.getCallSidToCoach}")
        //        logger.info(s"call status : ${participant_a.getStatus}")
        //        logger.info(s"participant_a label : ${participant_a.getLabel}")


        //        .callerId(call_from_number)
        // System.getenv("TWILIO_CALLER_ID")
        //      val dialBuilderWithReceiver = addChildReceiver(dialBuilder, call_details.to.phone_number)
        //
        //        logger.info(s"dialBuilder callerId : ${dial_builder.build().getCallerId}")
        //        logger.info(s"call_details  call-sid : ${call_details.call_sid.call_sid}")

        //    logger.info(s"call status : ${participant_a.}")


        val conference_details: ConferenceDetailsFromWebhook = ConferenceDetailsFromWebhook(
          conference_sid = Some(participant_a.getConferenceSid),
          conference_name = Some(conference_name),
          initiated_by = call_details.from,
          service_provider = CallingServiceProvider.TWILIO,
          callType = CallType.TWL_CONF

        )

        val participants: Seq[ParticipantDetailsFromWebhook] = Seq(
          ParticipantDetailsFromWebhook(
            call_from = call_details.from,
            participant_label = participant_a.getLabel,
            call_participant_phone = call_details.to,
            is_prospect = true, //assuming all outgoing calls are to prospects
            call_sid = Some(CallSID(sid = participant_a.getCallSid))
          ),
          ParticipantDetailsFromWebhook(
            call_from = call_details.from,
            participant_label = "initiator",
            call_participant_phone = call_details.from,
            is_prospect = false, //assuming all outgoing calls are to prospects
            call_sid = Some(call_details.call_sid)
          )
        )

        Success(TwilioConferenceAndParticipantDetails(
          conference_details = Some(conference_details),
          participantDetails = participants,
          response = new VoiceResponse.Builder().dial(dialBuilder.build).build.toXml()
        ))

      //    logger.info(s"[Conference] conference_id : ${participant_a.getConferenceSid}")
      //    participant_a.getConferenceSid


    }

    voice_response
  }.flatten

  def completeConference(
                          subAccountDetails: SubAccountDetails,
                          conferenceSid: ConferenceSid
                        ): Try[ConferenceSid] = Try {

    val ACCOUNT_SID = subAccountDetails.sub_account_id.id
    val AUTH_TOKEN = subAccountDetails.sub_auth_token.token

    Twilio.init(ACCOUNT_SID, AUTH_TOKEN)

    val conference: ConferenceUpdater = ConferenceUpdater.updater(conferenceSid.sid)
      .setStatus(ConferenceUpdater.UpdateStatus.COMPLETED)
      .update


    conferenceSid
  }

  def updateParticipantMode(
                             subAccountDetails: SubAccountDetails,
                             call_participant_update_data: ParticipantUpdateDetails,
                             new_participant_mode: CallParticipationMode,
                             call_with_prospect_initiator_call_sid: CallSID
                           ): Try[ParticipantUpdateDetails] = Try {
    val ACCOUNT_SID = subAccountDetails.sub_account_id.id
    val AUTH_TOKEN = subAccountDetails.sub_auth_token.token

    Twilio.init(ACCOUNT_SID, AUTH_TOKEN)

    val conference_sid = call_participant_update_data.call_conference_sid
    val initiator_call_sid = call_participant_update_data.call_sp_sid.sid

    new_participant_mode match {
      case CallParticipationMode.ListenMode =>

        Participant
          .updater(
            conference_sid,
            initiator_call_sid
          )
          .setCoaching(false)
          .setMuted(true)
          .update

        ParticipantUpdateDetails(
          call_conference_sid = conference_sid,
          call_sp_sid = CallSID(initiator_call_sid),
          status = CallStatus.ACTIVE.toString,
          latest_participant_mode = CallParticipationMode.ListenMode.toString,
          was_listening = Some(true),
          listened_at = Some(DateTime.now()),
          was_coaching = call_participant_update_data.was_coaching, //preserving old data
          coached_at = call_participant_update_data.coached_at,
          was_barged_in = call_participant_update_data.was_barged_in,
          barged_in_at = call_participant_update_data.barged_in_at,
          added_to_conference_at = call_participant_update_data.added_to_conference_at,
          is_prospect = call_participant_update_data.is_prospect
        )


      case CallParticipationMode.WhisperMode =>

        Participant
          .updater(
            conference_sid,
            initiator_call_sid
          )
          .setCoaching(true)
          .setMuted(false)
          .setCallSidToCoach(call_with_prospect_initiator_call_sid.sid)
          .update

        ParticipantUpdateDetails(
          call_conference_sid = conference_sid,
          call_sp_sid = CallSID(initiator_call_sid),
          status = CallStatus.ACTIVE.toString,
          latest_participant_mode = CallParticipationMode.WhisperMode.toString,
          was_listening = call_participant_update_data.was_listening,
          listened_at = call_participant_update_data.listened_at,
          was_coaching = Some(true),
          coached_at = Some(DateTime.now()),
          was_barged_in = call_participant_update_data.was_barged_in,
          barged_in_at = call_participant_update_data.barged_in_at,
          added_to_conference_at = call_participant_update_data.added_to_conference_at,
          is_prospect = call_participant_update_data.is_prospect
        )

      case CallParticipationMode.BargeInMode =>
        Participant
          .updater(
            conference_sid,
            initiator_call_sid
          )
          .setCoaching(false)
          .setMuted(false)
          .update

        ParticipantUpdateDetails(
          call_conference_sid = conference_sid,
          call_sp_sid = CallSID(initiator_call_sid),
          status = CallStatus.ACTIVE.toString,
          latest_participant_mode = CallParticipationMode.BargeInMode.toString,
          was_listening = call_participant_update_data.was_listening,
          listened_at = call_participant_update_data.listened_at,
          was_coaching = call_participant_update_data.was_coaching,
          coached_at = call_participant_update_data.coached_at,
          was_barged_in = Some(true),
          barged_in_at = Some(DateTime.now()),
          added_to_conference_at = call_participant_update_data.added_to_conference_at,
          is_prospect = call_participant_update_data.is_prospect
        )

      case CallParticipationMode.InitiatorMode =>

        throw new Throwable("Participant cannot be updated to initiator mode")

    }


  }

  def getPricing(countryCode: String): Either[GetNumberPriceError, NumberPriceObject] = Try {
    Twilio.init(twilioVars.acctSid, twilioVars.authToken)
    Country.fetcher(countryCode).fetch()
  } match {
    /*
    In order to segregate the error messages, we are matching here itself.
    * */

    case Failure(exception) => Left(GetNumberPriceError.GetNumberPriceApiError(err = exception))

    case Success(price_object_country) => Try {
      if (price_object_country.getCountry == null) {
        Left(GetNumberPriceError.NoPriceObjectFoundError(err = s"No price object found for country iso : $countryCode"))
      }
      else {
        val prices_list: List[PhoneNumberPrice] = price_object_country.getPhoneNumberPrices.asScala.toList

        //    var prices: List[PriceObject] = List.empty[PriceObject]
        val new_list: List[PriceObject] = prices_list.map(p => {
          PriceObject(
            phone_type = p.getType.toString,
            base_price = p.getBasePrice,
            current_price = p.getCurrentPrice
          )
        })
        Right(NumberPriceObject(
          country = price_object_country.getCountry,
          countryISO = price_object_country.getIsoCountry,
          prices = new_list,
          currency = price_object_country.getPriceUnit.toString
        ))
      }
    } match {
      /*
      This match is done to catch errors while accessing java package.
       */

      case Failure(exception) => Left(GetNumberPriceError.ErrorWhileParsingJavaList(err = exception))

      case Success(value) => value

    }
  }


  def getAvailableNumbers(
                           countryCode: String,
                           sub_account_sid: TwlSubAccountSid,
                           sub_account_auth_token: TwlAuthToken

                         )(using Logger: SRLogger): Try[Option[PhoneNumber]] = Try {

    Twilio.init(sub_account_sid.id, sub_account_auth_token.token)
    val availablePhoneNumberCountry = AvailablePhoneNumberCountry.fetcher(countryCode).fetch()
    val local: ResourceSet[Local] = Local.reader(countryCode).setExcludeAllAddressRequired(true).limit(1).read()
    /*  FIXME CALL:
    * Initially instead of handing address requirements, only those available numbers will be fetched whose address requirements are -> none
    * */

    var num: Option[PhoneNumber] = None

    Logger.info(s"available numbers:=> ${availablePhoneNumberCountry.getSubresourceUris()}")
    Logger.info(s"local numbers:=> ${local}")

    local.forEach(record => {
      /*
        local contains only one record-> as we have set limit to 1.
        to fetch the details of the record we need to use foreach as it is stored as ResourceSet[Local]
      * */
      num = Some(record.getPhoneNumber())

    })
    num
  }

  def buyANumber(
                  number: PhoneNumber,
                  sub_account_sid: TwlSubAccountSid,
                  sub_auth_token: TwlAuthToken
                )(using Logger: SRLogger): Try[IncomingPhoneNumber] = Try {

    Twilio.init(sub_account_sid.id, sub_auth_token.token)
    val incomingPhoneNumber: IncomingPhoneNumber = IncomingPhoneNumber.creator(number)
      .setVoiceUrl(AppConfig.ApiUrls.voice_webhook_api)
      .setVoiceMethod(HttpMethod.GET)
      .create()

    Logger.info(s"created number: ${incomingPhoneNumber.getPhoneNumber.toString}")
    incomingPhoneNumber
  }

  def deleteNumber(phoneSID: PhoneSID,
                   sub_account_sid: TwlSubAccountSid,
                   sub_account_auth_token: TwlAuthToken): Try[Boolean] = Try {

    Twilio.init(sub_account_sid.id, sub_account_auth_token.token)

    val result: Boolean = IncomingPhoneNumber.deleter(phoneSID.phone_sid)
      .delete()

    result
  }


  /*
    we are here matching over the current call status, if its no-answer we are trying to forward the call
  this function is still in progress
  */
  def getVoiceResponseBasedOnCallAction(
                                         uri: String,
                                         body: Option[Map[String, Seq[String]]],
                                         forward_to: String
                                       )(using logger: SRLogger): Try[Either[CallForwardingError, String]] = Try {

    val parsedParams = StringUtilsV2.getParams(uri)


    logger.info(s"\n\ninfo :  request uri : ${uri} parsedParams: ${parsedParams}: Body: ${body} \n\n")

    val dialCallStatus = body.get("DialCallStatus").headOption

    dialCallStatus match {
      case None =>
        logger.error("DialCallStatus not found")
        Left(CallForwardingError.DialCallStatusNoneFound)

      case Some(dialCallStatus) =>


        //        println(s"\n\ndialCallStatus : ${dialCallStatus}\n\n")
        /*
        different types on call-status we can recieve
            completed: 	The called party answered the call and was connected to the caller.
            answered: 	When calling a conference, the called party answered the call and was connected to the caller.
            busy: 	Twilio received a busy signal when trying to connect to the called party.
            no-answer: 	The called party did not pick up before the timeout period passed.
            failed: Twilio was unable to route to the given phone number. This is frequently caused by dialing a properly formatted but non-existent phone number.
            canceled: The call was canceled via the REST API before it was answered.
         */

        if (dialCallStatus == "no-answer" || dialCallStatus == "failed") { // Fixme-call make a sealed trait

          val dial2: Dial = new Dial.Builder(forward_to).build()
          val voiceTwimlResponse: VoiceResponse = new VoiceResponse.Builder()
            .say(new Say.Builder("Forwarding your call").build)
            .dial(dial2)
            .build()

          Right(voiceTwimlResponse.toXml())

        } else {
          // These call status we aren't doing anything currently
          Left(CallForwardingError.IgnoreThisCallStatus)
        }

    }

  }

  def createSubAccount()/*(using Logger: SRLogger)*/: Try[TwilioSubAccountDetails] = Try {
    Twilio.init(twilioVars.acctSid, twilioVars.authToken)
    val account = Account.creator
      //      .setFriendlyName("Submarine")  //
      .create()

    // Logger.info(s"account created account sid:  ${account.getSid}")

    TwilioSubAccountDetails(
      sub_account_sid = TwlSubAccountSid(id = account.getSid),
      auth_token = TwlAuthToken(token = account.getAuthToken),
      sub_account_name = account.getFriendlyName,
      status = account.getStatus.toString,
      owner_account_sid = account.getOwnerAccountSid,
    )
  }

  /*
   it will just see when the usage goes above certain limit
   it will throw then
   if the person adds more credit, we will delete the previous trigger
   add create new trigger with new limit.
   */
  def createUsageTrigger(
                          org_id: OrgId,
                          usage_limit: String,
                          subaccount_sid: TwlSubAccountSid,
                          subaccount_auth_token: TwlAuthToken
                        )(using Logger: SRLogger): Try[TwilioUsageTrigger] = Try {


    Twilio.init(subaccount_sid.id, subaccount_auth_token.token)

    val trigger: Trigger = Trigger
      .creator(
        new URI(s"${trigger_webhook}/api/v2/twilio/${org_id.id}/usage_limit_crossed"),
        usage_limit,
        Trigger.UsageCategory.TOTALPRICE
      )
      .setFriendlyName(s"${org_id.id} monthly usage trigger")
      //      .setRecurring(Trigger.Recurring.YEARLY)
      .setTriggerBy(Trigger.TriggerField.PRICE)
      .create()

    Logger.info(s"triggerId : -> ${trigger.getSid} triggerData : ${trigger}")

    TwilioUsageTrigger(
      trigger_id = trigger.getSid,
      trigger_name = trigger.getFriendlyName,
      trigger_webhook = trigger.getCallbackUrl.toString,
      trigger_value = trigger.getTriggerValue,
      trigger_current_value = trigger.getCurrentValue,
      trigger_recurring = "recurring", // we are creating non-recurring triggers : trigger.getRecurring.toString,
      trigger_by = trigger.getTriggerBy.toString,
      trigger_usage_category = trigger.getUsageCategory.toString,
      trigger_last_fired = DateTime.now(), //DateTime.parse(trigger.getDateFired.toLocalDate.toString), // Fixme call
      sub_account_sid = trigger.getAccountSid
    )

  }

  def deleteTrigger(
                     sub_account_sid: TwlSubAccountSid,
                     sub_account_auth_token: TwlAuthToken,
                     trigger_sid: String
                   )(using Logger: SRLogger): Try[Boolean] = Try {

    Twilio.init(sub_account_sid.id, sub_account_auth_token.token)
    Trigger.deleter(trigger_sid).delete()

  }

  /*
    When users crosses their set limit in twilio, twilio sends us webhook, in that case we are calling this function
    to suspend user's account in twilio so that he won't be able to use any voice-services

   */

  def suspendSubAccount(
                         sub_account_sid: TwlSubAccountSid
                       )(using Logger: SRLogger): Try[TwilioSubAccountDetails] = Try {
    Twilio.init(twilioVars.acctSid, twilioVars.authToken)
    val account = Account.updater(sub_account_sid.id).setStatus(Account.Status.SUSPENDED).update()
    Logger.info(s" suspended account ac-sid : ${account.getSid} ac-status : ${account.getStatus} ")

    TwilioSubAccountDetails(
      sub_account_sid = TwlSubAccountSid(id = account.getSid),
      auth_token = TwlAuthToken(token = account.getAuthToken),
      sub_account_name = account.getFriendlyName,
      status = account.getStatus.toString,
      owner_account_sid = account.getOwnerAccountSid
    )
  }

  def activateSubAccount(
                          sub_account_sid: TwlSubAccountSid,
                        )(using Logger: SRLogger): Try[TwilioSubAccountDetails] = Try {
    Twilio.init(twilioVars.acctSid, twilioVars.authToken)
    val account = Account.updater(
      sub_account_sid.id
    ).setStatus(Account.Status.ACTIVE).update()
    Logger.info(s" ac-sid : ${account.getSid} ac-status : ${account.getStatus} ")

    TwilioSubAccountDetails(
      sub_account_sid = TwlSubAccountSid(id = account.getSid),
      auth_token = TwlAuthToken(token = account.getAuthToken),
      sub_account_name = account.getFriendlyName,
      status = account.getStatus.toString,
      owner_account_sid = account.getOwnerAccountSid
    )
  }

  def getUsageOfSubAccount(
                            sub_account_sid: TwlSubAccountSid,
                            sub_account_auth_token: TwlAuthToken
                          )(using logger: SRLogger): Either[GetSubAccountUsageError, UsageDetails] = {

    logger.info(s"Fetching usage record for sub-account : ${sub_account_sid.id}")

    val usage_details: Either[GetSubAccountUsageError, UsageDetails] = Try {

      Twilio.init(sub_account_sid.id, sub_account_auth_token.token)
      //    val localdate = DateTimeToJavaTimeLocalDate(date_time = from_time)

      val usasge: ResourceSet[Record] = Record.reader()
        .setCategory(Record.Category.TOTALPRICE)
        .limit(1)
        .read()

      usasge

    } match {

      case Failure(err) =>

        Left(GetSubAccountUsageError.ErrorWhileFetchingFromTwilio(err))

      case Success(usage) =>

        var usageDetail: List[UsageDetails] = List()

        val usage_details_from_twilio: Either[GetSubAccountUsageError, UsageDetails] = Try {

          usage.forEach(usageDetails => {

            val usage_in_cents = TwilioDialerService.parseCurrencyInCents(usageDetails.getUsage)

            val usage_of_subAccount = UsageDetails(
              sub_account_sid = TwlSubAccountSid(id = usageDetails.getAccountSid),
              total_usage_in_cents = usage_in_cents.get,
              usage_unit = CurrencyType.fromKey(usageDetails.getUsageUnit).get,
              usage_from = JavaTimeLocalDateToDateTime(usageDetails.getStartDate),
              usage_till = DateTime.parse(usageDetails.getAsOf)
            )

            usageDetail = usageDetail ++ List(usage_of_subAccount)

          })
        } match {
          case Failure(err) =>

            Left(GetSubAccountUsageError.ErrorWhileParsingUsage(err))

          case Success(d) =>

            if (usageDetail.nonEmpty) {

              logger.info(s"FetchingUsageOfSubAccount: usage_details for org_id : ${sub_account_sid.id} , usage: ${usageDetail.head}")

              Right(usageDetail.head)
            } else Left(GetSubAccountUsageError.EmptyUsageDetailFoundError)
        }

        usage_details_from_twilio

    }
    usage_details
  }

  def createApiKeySecretForSubAccount(
                                       sub_account_sid: TwlSubAccountSid,
                                       sub_account_auth_token: TwlAuthToken,
                                       sub_account_uuid: SubAccountUuid
                                     )(using logger: SRLogger): Try[SubAccountApiKeyAndSecret] = Try {

    logger.info(s"creating sub-account sid and secret key for sub-account uuid: ${sub_account_uuid.uuid}")

    Twilio.init(sub_account_sid.id, sub_account_auth_token.token)
    val newKey = NewKey.creator.setFriendlyName(sub_account_uuid.uuid).create()

    SubAccountApiKeyAndSecret(
      api_key = ApiKey(key = newKey.getSid),
      api_secret = ApiSecret(secret = newKey.getSecret)
    )

  }

  def createTwimlAppForSubAccounts(
                                    sub_account_sid: TwlSubAccountSid,
                                    sub_account_auth_token: TwlAuthToken,
                                    sub_account_uuid: SubAccountUuid
                                  )(using logger: SRLogger): Try[SubAccountTwimlApplication] = Try {

    logger.info(s"creating twiml app for sub-account uuid: ${sub_account_uuid.uuid}")

    Twilio.init(sub_account_sid.id, sub_account_auth_token.token)

    val application = Application
      .creator
      .setVoiceMethod(HttpMethod.GET)
      .setVoiceUrl(URI.create(AppConfig.ApiUrls.voice_webhook_api))
      .setFriendlyName(sub_account_uuid.uuid)
      .create()


    SubAccountTwimlApplication(
      app_sid = TwlTwimlAppSid(sid = application.getSid),
      voice_url = application.getVoiceUrl.toString,
      app_name = TwlTwimlAppName(name = application.getFriendlyName)
    )

  }

  def authenticateTwilioWebhook(
                                 sub_account_auth_token: TwlAuthToken,
                                 params: Map[String, String],
                                 webhook_url: String,
                                 twilio_webhook_signature: String
                               )(using logger: SRLogger): Try[Boolean] = Try {

    val request_validator = new RequestValidator(sub_account_auth_token.token)

    // Store Twilio's request URL (the url of your webhook) as a variable// Store Twilio's request URL (the url of your webhook) as a variable

    //    val url = "https://example.com/myapp"

    // Store the application/x-www-form-urlencoded parameters from Twilio's request as a variable
    // In practice, this MUST include all received parameters, not a
    // hardcoded list of parameters that you receive today. New parameters
    // may be added without notice.

    //              params.("CallSid", "*****************")
    //              params.put("Caller", "+***********")
    //              params.put("Digits", "1234")
    //              params.put("From", "+***********")
    //              params.put("To", "+***********")

    // Store the X-Twilio-Signature header attached to the request as a variable
    //    val twilioSignature = "Np1nax6uFoY6qpfT5l9jWwJeit0="

    logger.info("[WebhookAuthenticator] validating twilio webhook url twilioDialerService")
    // Check if the incoming signature is valid for your application URL and the incoming parameters
    request_validator.validate(webhook_url, params.asJava, twilio_webhook_signature)

    //    println(1)


  }

  def twilioCallQualityFeedback(
                                 call_score: Int,
                                 call_issues: List[CallFeedBackReasons],
                                 call_sid: CallSID,
                                 authToken: TwlAuthToken,
                                 accountSid: TwlSubAccountSid
                               )(using logger: SRLogger): Try[TwlSubAccountSid] = Try {

    logger.info(s"[twilioCallQualityFeedback] updating the call quality Feedback for call_sid : ${call_sid.sid}")

    Twilio.init(accountSid.id, authToken.token)

    /*
      changed these on 12-March 2024:  as feedback api was depreciated
      Ref: https://www.twilio.com/docs/voice/voice-insights/api/call/call-annotation-resource#update-the-call-annotation-for-a-specific-call

     */
    val call_issues_java: Option[Annotation.ConnectivityIssue] = TwilioDialerService
      .convertCallIssues(
        call_issues
      )

    val call_quality_issue: String = TwilioDialerService.convertQualityIssues(
      call_issues
    )

    val res: Annotation = call_issues_java match {

      case None =>

        Annotation
          .updater(call_sid.sid)
          .setCallScore(call_score)
          .setQualityIssues(
            call_quality_issue
          )
          .update


      case Some(connectivity_issue) =>
        Annotation
          .updater(call_sid.sid)
          .setCallScore(call_score)
          .setConnectivityIssue(connectivity_issue)
          .setQualityIssues(
            call_quality_issue
          )
          .update


    }


    TwlSubAccountSid(id = res.getAccountSid)

  }

  def fetchCallLogsForSubAccount(

                                  twl_sub_account_sid: TwlSubAccountSid,
                                  twl_auth_token: TwlAuthToken,
                                  last_call_history_updated_at: Option[DateTime]

                                )(using logger: SRLogger): Try[ResourceSet[Call]] = Try {


    Twilio.init(
      twl_sub_account_sid.id, twl_auth_token.token
    )

    val calls = if (last_call_history_updated_at.isEmpty) {

      Call.reader()
        .read()


    } else {

      val last_log_fetched_at = last_call_history_updated_at.get

      /* Tried converting zone_date_time to jodaDateTime : using this

      https://chat.openai.com/share/ce5a919c-4d12-4175-9f3d-4594bcf32835
      https://stackoverflow.com/questions/********/how-to-convert-from-org-joda-time-datetime-to-java-time-zoneddatetime

      val zone_date_time  = ZonedDateTime.ofLocal(
        LocalDateTime.of(
          last_log_fetched_at.getYear(),
          last_log_fetched_at.getMonthOfYear(),
          last_log_fetched_at.getDayOfMonth(),
          last_log_fetched_at.getHourOfDay(),
          last_log_fetched_at.getMinuteOfHour(),
          last_log_fetched_at.getSecondOfMinute(),
          last_log_fetched_at.getMillisOfSecond() * 1_000_000),
        ZoneId.of(last_log_fetched_at.getZone().getID(), ZoneId.SHORT_IDS),
        ZoneOffset.ofTotalSeconds(last_log_fetched_at.getZone().getOffset(last_log_fetched_at) / 1000));

        since LocalDateTime.of was not working, we have used below approach.
       */


      val zone_date_time_2 = ZonedDateTime
        .ofInstant(
          Instant.ofEpochMilli(
            last_log_fetched_at.getMillis
          ),
          ZoneId.of(
            last_log_fetched_at.getZone.getID, ZoneId.SHORT_IDS
          ))

      Call.reader()
        .setStartTimeAfter(
          zone_date_time_2
            .minusMinutes(90)
        )
        .read()

    }

    calls

  }

  def getMainAccountRemainingCredits(): Try[CallingRemainingCredits] = Try {

    Twilio.init(twilioVars.acctSid, twilioVars.authToken)

    val balance_details = Balance.fetcher(twilioVars.acctSid)
      .fetch()

    CallingRemainingCredits(
      remaining_credit = balance_details.getBalance.toFloat,
      currency_type = CurrencyType.fromKey(key = balance_details.getCurrency.toLowerCase).get // as USD is coming
    )

  }

}

object TwilioDialerService {

  def convertQualityIssues(call_issues: List[CallFeedBackReasons]): String = {

    var quality_issues: List[String] = List()

    call_issues.foreach(ev => ev match {

      case CallFeedBackReasons.DroppedCall =>

        quality_issues = quality_issues

      case CallFeedBackReasons.OneWayAudio =>

        quality_issues = quality_issues ++ List("owa") // stands for `one way audio`

      case CallFeedBackReasons.AudioLatency =>

        quality_issues = quality_issues ++ List("latency")

      case CallFeedBackReasons.DigitsNotCaptured =>

        quality_issues = quality_issues ++ List()

      case CallFeedBackReasons.ImperfectAudio =>

        quality_issues = quality_issues ++ List("static_noise") ++ List("choppy_robotic")

      case CallFeedBackReasons.UnSolicitedCall =>

        quality_issues = quality_issues ++ List("echo")

      case CallFeedBackReasons.IncorrectCallerId =>

        quality_issues = quality_issues

      case CallFeedBackReasons.PostDialDelay =>

        quality_issues = quality_issues


    })

    quality_issues.mkString(",")

  }

  /*
    Fixme call: Take single input for connectivity issue

   */
  def convertCallIssues(call_issues: List[CallFeedBackReasons]): Option[Annotation.ConnectivityIssue] = {

    var call_feedback_reason: Option[Annotation.ConnectivityIssue] = None

    call_issues.foreach(ev => ev match {

      case CallFeedBackReasons.DroppedCall =>

        call_feedback_reason = Some(Annotation.ConnectivityIssue.DROPPED_CALL)

      case CallFeedBackReasons.OneWayAudio =>

        call_feedback_reason = Some(Annotation.ConnectivityIssue.UNKNOWN_CONNECTIVITY_ISSUE)

      case CallFeedBackReasons.AudioLatency =>

        call_feedback_reason = Some(Annotation.ConnectivityIssue.UNKNOWN_CONNECTIVITY_ISSUE)

      case CallFeedBackReasons.DigitsNotCaptured =>

        call_feedback_reason = Some(Annotation.ConnectivityIssue.UNKNOWN_CONNECTIVITY_ISSUE)

      case CallFeedBackReasons.ImperfectAudio =>

        call_feedback_reason = Some(Annotation.ConnectivityIssue.UNKNOWN_CONNECTIVITY_ISSUE)

      case CallFeedBackReasons.UnSolicitedCall =>

        call_feedback_reason = Some(Annotation.ConnectivityIssue.UNKNOWN_CONNECTIVITY_ISSUE)

      case CallFeedBackReasons.IncorrectCallerId =>

        call_feedback_reason = Some(Annotation.ConnectivityIssue.CALLER_ID)

      case CallFeedBackReasons.PostDialDelay =>

        call_feedback_reason = Some(Annotation.ConnectivityIssue.CALLER_ID)

    })

    call_feedback_reason

  }

  def parseCurrencyInCents(str: String): Try[Long] = Try {


    val usage = Math.ceil(str.toFloat * 100).toLong


    usage

  }


  def forwardingIncomingCalls(
                               is_forward_enabled: Boolean,
                               forward_to: Option[String]
                             ): VoiceResponse = {

    val dial: Dial = new Dial.Builder().number(forward_to.get.toString)
      .build()

    new VoiceResponse.Builder().dial(dial)
      .build()

  }

  def handlingIncomingCalls(
                             client: Client,
                             is_forward_enabled: Boolean,
                             forward_to: Option[String]
                           ): VoiceResponse = {

    if (is_forward_enabled) {
      val dial: Dial = new Dial.Builder().client(client)
        .timeout(20)
        .number(forward_to.get)
        .action(AppConfig.ApiUrls.action_webhook_api + s"?forward_to=${forward_to.get}")
        .build()

      new VoiceResponse.Builder().dial(dial)
        .build()

    } else {
      val dial: Dial = new Dial.Builder().client(client).build()
      new VoiceResponse.Builder().dial(dial).build()
    }

  }


  def isPhoneNumber(number: String): Boolean = {
    number.matches("^[\\d\\+\\-\\(\\) ]+$")
  }

  def recordCallWebhookReplyUrl(tid: TeamId) = s"$srApiBaseUrl/api/v2/voice/${tid.id}/twilio/recording_data"

  /*
  conferenceParticipantStatusCallbackUrl ->
  it is used to receive the webhook for when a conference participant call status is changed
  [all participants excluding the prospect]
  This is only used for statuses when a participant joins and leaves the conference
  and when the conference starts and ends

  prospectConferenceStatusCallbackUrl ->
  it is used to receive the webhook for when the prospect call status is changed [only the prospect]
  the status includes -> initiated / ringing / in-progress / completed / no-answer / busy

  incomingCallStatusCallback ->
  it is used to receive the webhook for when a basic call participant's call status is changed
  [all participants (includes only the caller and receiver)]
  the status includes -> initiated / ringing / in-progress / completed / no-answer / busy

   */
  def conferenceParticipantStatusCallbackUrl(tid: TeamId) = s"$srApiBaseUrl/api/v2/voice/${tid.id}/twilio/conference_participant_status_callback"

  def prospectConferenceStatusCallbackUrl(tid: TeamId) = s"$srApiBaseUrl/api/v2/voice/${tid.id}/twilio/prospect_status_callback"

  def incomingCallStatusCallback(tid: TeamId) = s"$srApiBaseUrl/api/v2/voice/${tid.id}/twilio/incoming_call_participant_status_callback"

  def outgoingCallStatusCallback(tid: TeamId) = s"$srApiBaseUrl/api/v2/voice/${tid.id}/twilio/outgoing_basic_call_participant_status_callback"

  def verifyCallerIdStatusCallback(tid: TeamId) = s"$srApiBaseUrl/api/v2/voice/${tid.id}/twilio/verify_callerid_status_callback"


  def addTeamIdToCallLogs(
                           sub_account_uuid: SubAccountUuid,
                           calls: ResourceSet[Call],
                           call_account_settings: List[CallAccountSettings],
                           srUuidUtils: SrUuidUtils
                         ): Try[List[NewCallLog]] = Try {

    val teamIdMap: Map[String, TeamIdAndAccountDetails] = CallService.createHashMapOfCallSettingAndTeamId(
      call_account_settings = call_account_settings
    )

    var call_logs: List[NewCallLog] = List()

    calls.forEach { record => {

      val call_direction = record.getDirection

      val is_outgoing = call_direction == "outbound-api"

      val from = record.getFrom
      val to = record.getTo

      val (team_id, ownerAccountId): (Option[TeamId], Option[AccountId]) =

        if (teamIdMap.contains(from)) {

          (Some(teamIdMap(from).team_id), Some(teamIdMap(from).account_id))

        } else if (teamIdMap.contains(to)) {

          (Some(teamIdMap(to).team_id), Some(teamIdMap(to).account_id))

        } else {

          (None, None)
        }

      val start_time: DateTime = new DateTime(record.getStartTime.toInstant.toEpochMilli, DateTimeZone.forTimeZone(TimeZone.getTimeZone(record.getStartTime.getZone)))
      val end_time: DateTime = new DateTime(record.getEndTime.toInstant.toEpochMilli, DateTimeZone.forTimeZone(TimeZone.getTimeZone(record.getEndTime.getZone)))

      val price: Long = if (record.getPrice != null) {
        getPriceInMillis(record.getPrice).get
      } else {
        0L
      }

      call_logs = call_logs ++ List(NewCallLog(

        sub_account_uuid = sub_account_uuid,
        uuid = CallLogUUID(uuid = srUuidUtils.generateCallLogUuid()),
        team_id = team_id,
        user_id = ownerAccountId,
        service_provider = CallingServiceProvider.TWILIO,
        call_sid = CallSID(sid = record.getSid),
        is_outgoing = is_outgoing,
        start_time = start_time,
        end_time = end_time,
        from_number = from,
        to_number = to,
        duration = record.getDuration.toLong,
        price = price,
        price_unit = CurrencyType.fromKey(record.getPriceUnit.toString).getOrElse(CurrencyType.USD) // twilio sends currency in usd.

      ))

    }
    }

    call_logs

  }
}
