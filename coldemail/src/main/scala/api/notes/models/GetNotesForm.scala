package api.notes.models

import api.prospects.models.ProspectId
import api.tasks.services.TaskUuid
import eventframework.SrResourceTypes
import play.api.libs.json.{JsError, JsSuccess, JsValue, Reads}
import scalikejdbc.WrappedResultSet

import scala.util.{Failure, Success, Try}

case class GetNotesForm(
                         prospectId: Option[ProspectId],
                         taskUuid: Option[TaskUuid]
                       )

object GetNotesForm {

  def fromDB(rs: WrappedResultSet): GetNotesForm = {

    GetNotesForm(
      prospectId = rs.longOpt("prospect_id").map(ProspectId(_)),
      taskUuid = rs.stringOpt("task_uuid").map(TaskUuid(_))
    )

  }

}
