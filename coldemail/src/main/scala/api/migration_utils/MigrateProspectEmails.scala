package api.migration_utils

import org.joda.time.DateTime
import scalikejdbc.jodatime.JodaWrappedResultSet.fromWrappedResultSetToJodaWrappedResultSet
import scalikejdbc.{DB, SQLSyntax, WrappedResultSet, scalikejdbcSQLInterpolationImplicitDef}
import utils.{Helpers, SRLogger}

import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext, Future}


object MigrateProspectEmails {

  case class ProspectsEmails(
                              email: String,
                              prospect_id: Long,
                              team_id: Long,
                              account_id: Long,
                              email_format_valid: <PERSON><PERSON><PERSON>,
                              email_checked: <PERSON><PERSON><PERSON>,
                              email_checked_at: Option[DateTime],
                              invalid_email: <PERSON><PERSON>an,
                              email_validation_id: Option[Long],
                              email_domain: String,
                              email_sent_for_validation: Option[Boolean],
                              email_sent_for_validation_at: Option[DateTime],
                              email_bounced: <PERSON>olean,
                              email_bounced_campaign: Option[String],
                              email_bounced_at: Option[DateTime],
                              force_send_invalid_email: Option[<PERSON><PERSON><PERSON>],
                              force_send_invalid_email_at: Option[DateTime],
                              email_bounced_earlier: Option[<PERSON>olean],
                              email_bounce_type: Option[String],
                              email_bounce_reason: Option[String],
                              created_at: Option[DateTime],
                              updated_at: Option[DateTime]
                            )

  given logger: SRLogger = new SRLogger("Migrate ProspectsEmails")

  object ProspectsEmails {


    def fromDb(rs: WrappedResultSet) = {
      ProspectsEmails(
        email = rs.string("email"),
        prospect_id = rs.long("prospect_id"),
        team_id = rs.long("team_id"),
        account_id = rs.long("account_id"),
        email_format_valid = rs.boolean("email_format_valid"),
        email_checked = rs.boolean("email_checked"),
        email_checked_at = rs.jodaDateTimeOpt("email_checked_at"),
        invalid_email = rs.boolean("invalid_email"),
        email_validation_id = rs.longOpt("email_validation_id"),
        email_domain = rs.string("email_domain"),
        email_sent_for_validation = rs.booleanOpt("email_sent_for_validation"),
        email_sent_for_validation_at = rs.jodaDateTimeOpt("email_sent_for_validation_at"),
        email_bounced = rs.boolean("email_bounced"),
        email_bounced_campaign = rs.stringOpt("email_bounced_campaign"),
        email_bounced_at = rs.jodaDateTimeOpt("email_bounced_at"),
        force_send_invalid_email = rs.booleanOpt("force_send_invalid_email"),
        force_send_invalid_email_at = rs.jodaDateTimeOpt("force_send_invalid_email_at"),
        email_bounced_earlier = rs.booleanOpt("email_bounced_earlier"),
        email_bounce_type = rs.stringOpt("email_bounce_type"),
        email_bounce_reason = rs.stringOpt("email_bounce_reason"),
        created_at = rs.jodaDateTimeOpt("created_at"),
        updated_at = rs.jodaDateTimeOpt("updated_at")
      )
    }
  }



  /*migration_teamIds
    .foreach
    {
      migration_teamId =>

      val lowestProspectsIdForTeam = DB readOnly{ implicit session =>
        sql"""SELECT id FROM prospects where
                 team_id = $migration_teamId
                 ORDER BY id ASC
                 LIMIT 1
                 """
          .map(rs => rs.long("id"))
          .single
          .apply()

      } match{
        case None => 0
        case Some(value) => value
      }


      var currentStartingProspectId = lowestProspectsIdForTeam
      val limit = 1000
      var totalNumberOfProspectInTheCycle = limit
      var totalMigrated = 0

      while (totalNumberOfProspectInTheCycle == limit && currentStartingProspectId!=0) {

        val prospectsEmailsInProspectsTable = DB readOnly{ implicit session =>
          sql"""SELECT prospects.email, prospects.id as prospect_id, prospects.team_id, prospects.account_id,
            prospects.email_format_valid, prospects.email_checked, prospects.email_checked_at, prospects.invalid_email,
             prospects.email_validation_id, prospects.email_domain, prospects.email_sent_for_validation,
             prospects.email_sent_for_validation_at, prospects.email_bounced, prospects.email_bounced_campaign,
             prospects.email_bounced_at, prospects.force_send_invalid_email, prospects.force_send_invalid_email_at,
             prospects.email_bounced_earlier, prospects.email_bounce_type, prospects.email_bounce_reason,
             prospects.created_at, prospects.updated_at

            FROM prospects
            LEFT JOIN prospects_emails on prospects_emails.prospect_id = prospects.id AND prospects_emails.team_id = prospects.team_id

            WHERE prospects.team_id = $migration_teamId AND
            prospects_emails.migrated_at IS NULL
            AND prospects.id >= $currentStartingProspectId
            ORDER BY prospects.id
            LIMIT $limit
            """
            .map(rs => ProspectsEmails.fromDb(rs))
            .list
            .apply()

        }

        totalNumberOfProspectInTheCycle = prospectsEmailsInProspectsTable.length

        if (totalNumberOfProspectInTheCycle == limit){
          currentStartingProspectId = prospectsEmailsInProspectsTable.maxBy(_.prospect_id).prospect_id + 1
        }

        logger.info(s"prospectsEmailsInProspectsTable - ${prospectsEmailsInProspectsTable.length}")

        if (prospectsEmailsInProspectsTable.isEmpty) {
          logger.info("Nothing to add")
        } else {
          var valueParameters = List[Any]()
          val valuePlaceholder: SQLSyntax = prospectsEmailsInProspectsTable
            .map(p => {

              valueParameters = valueParameters ::: List(
                p.email,
                p.prospect_id,
                p.team_id,

                p.account_id,
                p.email_format_valid,
                p.email_checked,

                p.email_checked_at.getOrElse(null),
                p.invalid_email,
                p.email_validation_id.getOrElse(null),

                p.email_domain,
                p.email_sent_for_validation.getOrElse(null),
                p.email_sent_for_validation_at.getOrElse(null),

                p.email_bounced,
                p.email_bounced_campaign.getOrElse("").trim,
                p.email_bounced_at.getOrElse(null),

                p.force_send_invalid_email.getOrElse(null),
                p.force_send_invalid_email_at.getOrElse(null),
                p.email_bounced_earlier.getOrElse(null),

                p.email_bounce_type.getOrElse("").trim,
                p.email_bounce_reason.getOrElse("").trim,
                p.created_at.getOrElse(DateTime.now()),

                p.updated_at,
                true,
                DateTime.now()
              )

              sqls"""
             (
                ?,
                ?,
                ?,

                ?,
                ?,
                ?,

                ?,
                ?,
                ?,

                ?,
                ?,
                ?,

                ?,
                ?,
                ?,

                ?,
                ?,
                ?,

                ?,
                ?,
                ?,

                ?,
                ?,
                ?

              )
            """

            })
            .reduce((vp1, vp2) => sqls"$vp1, $vp2")


          val prospects_emails_list = DB autoCommit { implicit session =>
            sql"""
                INSERT INTO prospects_emails (email, prospect_id, team_id, account_id, email_format_valid, email_checked, email_checked_at,
                 invalid_email, email_validation_id, email_domain, email_sent_for_validation, email_sent_for_validation_at, email_bounced,
                 email_bounced_campaign, email_bounced_at, force_send_invalid_email, force_send_invalid_email_at, email_bounced_earlier,
                 email_bounce_type, email_bounce_reason, created_at, updated_at, is_primary, migrated_at)

                 SELECT email, id, team_id, account_id, email_format_valid, email_checked, email_checked_at::timestamptz,
                 invalid_email, email_validation_id::bigint, email_domain, email_sent_for_validation, email_sent_for_validation_at::timestamptz, email_bounced,
                 email_bounced_campaign, email_bounced_at::timestamptz, force_send_invalid_email::boolean, force_send_invalid_email_at::timestamptz, email_bounced_earlier::boolean,
                 email_bounce_type, email_bounce_reason, created_at::timestamptz, updated_at::timestamptz, is_primary, migrated_at::timestamptz
                 FROM (VALUES $valuePlaceholder)
                 AS prospects(
                  email, id, team_id, account_id, email_format_valid, email_checked, email_checked_at,
                 invalid_email, email_validation_id, email_domain, email_sent_for_validation, email_sent_for_validation_at, email_bounced,
                 email_bounced_campaign, email_bounced_at, force_send_invalid_email, force_send_invalid_email_at, email_bounced_earlier,
                 email_bounce_type, email_bounce_reason, created_at, updated_at, is_primary, migrated_at
              )
                 ON CONFLICT(team_id, prospect_id, lower(email)) DO UPDATE SET

                 email = excluded.email, prospect_id = excluded.prospect_id , team_id = excluded.team_id, account_id = excluded.account_id,
                 email_format_valid = excluded.email_format_valid, email_checked = excluded.email_checked, email_checked_at = excluded.email_checked_at,
                 invalid_email = excluded.invalid_email, email_validation_id = excluded.email_validation_id, email_domain = excluded.email_domain,
                 email_sent_for_validation = excluded.email_sent_for_validation, email_sent_for_validation_at = excluded.email_sent_for_validation_at,
                 email_bounced = excluded.email_bounced, email_bounced_campaign = excluded.email_bounced_campaign, email_bounced_at = excluded.email_bounced_at,
                 force_send_invalid_email = excluded.force_send_invalid_email, force_send_invalid_email_at = excluded.force_send_invalid_email_at,
                 email_bounced_earlier = excluded.email_bounced_earlier, email_bounce_type = excluded.email_bounce_type,
                 email_bounce_reason = excluded.email_bounce_reason, created_at = excluded.created_at, updated_at = excluded.updated_at, is_primary = excluded.is_primary,
                 migrated_at = excluded.migrated_at
                 RETURNING id;
              """
              .bind(valueParameters*)
              .map(rs => rs.long("id"))
              .list.apply()
          }

          logger.info(s"prospects_emails_list - ${prospects_emails_list.length}")

          totalMigrated = totalMigrated + prospects_emails_list.length

        }
      }
      logger.info(s"totalMigrated - ${totalMigrated}")
    }*/

  def getProspectsToMigrate(migration_teamId: Int
                            , currentStartingProspectId: Long
                            , limit: Int): List[ProspectsEmails] = {
    val prospectsEmailsInProspectsTable = DB readOnly { implicit session =>
      sql"""SELECT prospects.email, prospects.id as prospect_id, prospects.team_id, prospects.account_id,
            prospects.email_format_valid, prospects.email_checked, prospects.email_checked_at, prospects.invalid_email,
             prospects.email_validation_id, prospects.email_domain, prospects.email_sent_for_validation,
             prospects.email_sent_for_validation_at, prospects.email_bounced, prospects.email_bounced_campaign,
             prospects.email_bounced_at, prospects.force_send_invalid_email, prospects.force_send_invalid_email_at,
             prospects.email_bounced_earlier, prospects.email_bounce_type, prospects.email_bounce_reason,
             prospects.created_at, prospects.updated_at

            FROM prospects
            LEFT JOIN prospects_emails on prospects_emails.prospect_id = prospects.id AND prospects_emails.team_id = prospects.team_id

            WHERE prospects.team_id = $migration_teamId AND
            prospects_emails.migrated_at IS NULL
            AND prospects.id >= $currentStartingProspectId
            ORDER BY prospects.id
            LIMIT $limit
            """
        .map(rs => ProspectsEmails.fromDb(rs))
        .list
        .apply()
    }
    prospectsEmailsInProspectsTable

  }

  def prepareAndInsertMigrationRecords(prospectsEmailsInProspectsTable: List[ProspectsEmails]): Int = {

    logger.info(s"prepareAndInsertMigrationRecords started for teamId ${prospectsEmailsInProspectsTable.head.team_id}")


    var valueParameters = List[Any]()
    val valuePlaceholder: SQLSyntax = prospectsEmailsInProspectsTable
      .map(p => {

        valueParameters = valueParameters ::: List(
          p.email,
          p.prospect_id,
          p.team_id,

          p.account_id,
          p.email_format_valid,
          p.email_checked,

          p.email_checked_at.getOrElse(null),
          p.invalid_email,
          p.email_validation_id.getOrElse(null),

          p.email_domain,
          p.email_sent_for_validation.getOrElse(null),
          p.email_sent_for_validation_at.getOrElse(null),

          p.email_bounced,
          p.email_bounced_campaign.getOrElse("").trim,
          p.email_bounced_at.getOrElse(null),

          p.force_send_invalid_email.getOrElse(null),
          p.force_send_invalid_email_at.getOrElse(null),
          p.email_bounced_earlier.getOrElse(null),

          p.email_bounce_type.getOrElse("").trim,
          p.email_bounce_reason.getOrElse("").trim,
          p.created_at.getOrElse(DateTime.now()),

          p.updated_at,
          true,
          DateTime.now()
        )

        sqls"""
             (
                ?,
                ?,
                ?,

                ?,
                ?,
                ?,

                ?,
                ?,
                ?,

                ?,
                ?,
                ?,

                ?,
                ?,
                ?,

                ?,
                ?,
                ?,

                ?,
                ?,
                ?,

                ?,
                ?,
                ?

              )
            """

      })
      .reduce((vp1, vp2) => sqls"$vp1, $vp2")


    val prospects_emails_list = DB autoCommit { implicit session =>
      sql"""
                INSERT INTO prospects_emails (email, prospect_id, team_id, account_id, email_format_valid, email_checked, email_checked_at,
                 invalid_email, email_validation_id, email_domain, email_sent_for_validation, email_sent_for_validation_at, email_bounced,
                 email_bounced_campaign, email_bounced_at, force_send_invalid_email, force_send_invalid_email_at, email_bounced_earlier,
                 email_bounce_type, email_bounce_reason, created_at, updated_at, is_primary, migrated_at)

                 SELECT email, id, team_id, account_id, email_format_valid, email_checked, email_checked_at::timestamptz,
                 invalid_email, email_validation_id::bigint, email_domain, email_sent_for_validation, email_sent_for_validation_at::timestamptz, email_bounced,
                 email_bounced_campaign, email_bounced_at::timestamptz, force_send_invalid_email::boolean, force_send_invalid_email_at::timestamptz, email_bounced_earlier::boolean,
                 email_bounce_type, email_bounce_reason, created_at::timestamptz, updated_at::timestamptz, is_primary, migrated_at::timestamptz
                 FROM (VALUES $valuePlaceholder)
                 AS prospects(
                  email, id, team_id, account_id, email_format_valid, email_checked, email_checked_at,
                 invalid_email, email_validation_id, email_domain, email_sent_for_validation, email_sent_for_validation_at, email_bounced,
                 email_bounced_campaign, email_bounced_at, force_send_invalid_email, force_send_invalid_email_at, email_bounced_earlier,
                 email_bounce_type, email_bounce_reason, created_at, updated_at, is_primary, migrated_at
              )
                 ON CONFLICT(team_id, prospect_id, lower(email)) DO UPDATE SET

                 email = excluded.email, prospect_id = excluded.prospect_id , team_id = excluded.team_id, account_id = excluded.account_id,
                 email_format_valid = excluded.email_format_valid, email_checked = excluded.email_checked, email_checked_at = excluded.email_checked_at,
                 invalid_email = excluded.invalid_email, email_validation_id = excluded.email_validation_id, email_domain = excluded.email_domain,
                 email_sent_for_validation = excluded.email_sent_for_validation, email_sent_for_validation_at = excluded.email_sent_for_validation_at,
                 email_bounced = excluded.email_bounced, email_bounced_campaign = excluded.email_bounced_campaign, email_bounced_at = excluded.email_bounced_at,
                 force_send_invalid_email = excluded.force_send_invalid_email, force_send_invalid_email_at = excluded.force_send_invalid_email_at,
                 email_bounced_earlier = excluded.email_bounced_earlier, email_bounce_type = excluded.email_bounce_type,
                 email_bounce_reason = excluded.email_bounce_reason, created_at = excluded.created_at, updated_at = excluded.updated_at, is_primary = excluded.is_primary,
                 migrated_at = excluded.migrated_at
                 RETURNING id;
              """
        .bind(valueParameters*)
        .map(rs => rs.long("id"))
        .list.apply()
    }
    logger.info(s"prospects_emails_list  - ${prospects_emails_list.length}")
    logger.info(s"prepareAndInsertMigrationRecords end for teamId ${prospectsEmailsInProspectsTable.head.team_id}")
    prospects_emails_list.length


  }

  def do_migrate(migration_teamId: Int, ec: ExecutionContext): Future[Unit] = {

    Future {
      logger.info(s"do_migrate started for teamId: $migration_teamId")

      val lowestProspectsIdForTeam = DB readOnly { implicit session =>
        sql"""SELECT id FROM prospects where
                 team_id = $migration_teamId
                 ORDER BY id ASC
                 LIMIT 1
                 """
          .map(rs => rs.long("id"))
          .single
          .apply()

      } match {
        case None => 0
        case Some(value) => value
      }


      var currentStartingProspectId = lowestProspectsIdForTeam
      val limit = 10000
      var totalNumberOfProspectInTheCycle = limit
      var totalMigrated = 0


      while (totalNumberOfProspectInTheCycle == limit
      //&& currentStartingProspectId!=0
      ) {

        logger.info(s"While loop started for teamId $migration_teamId, totalNumberOfProspectInTheCycle: ${totalNumberOfProspectInTheCycle}, limit: ${limit},")

        val prospectsEmailsInProspectsTable = getProspectsToMigrate(migration_teamId = migration_teamId,
          currentStartingProspectId = currentStartingProspectId,
          limit = limit
        )

        totalNumberOfProspectInTheCycle = prospectsEmailsInProspectsTable.length

        if (totalNumberOfProspectInTheCycle == limit) {
          currentStartingProspectId = prospectsEmailsInProspectsTable.maxBy(_.prospect_id).prospect_id + 1
        }

        logger.info(s"prospectsEmailsInProspectsTable in this cycle for teamId $migration_teamId - ${prospectsEmailsInProspectsTable.length}")

        if (prospectsEmailsInProspectsTable.isEmpty) {
          logger.info(s"Nothing to add for teamId $migration_teamId")
          Future.successful(0)
        } else {


          implicit val ec: ExecutionContext = Helpers.genFixedThreadPoolEC(10)

          val totalMigratedInThisRound: Future[Int] = Future.sequence(

              prospectsEmailsInProspectsTable
                .grouped(1000)
                .toSeq
                .map(pgroup => {

                  Future {
                    prepareAndInsertMigrationRecords(pgroup)
                  }
                })
            )
            .map(_.sum)
            .map(prospects_emails_list_len => {

              totalNumberOfProspectInTheCycle = prospects_emails_list_len

              totalMigrated = totalMigrated + prospects_emails_list_len

              totalMigrated

            })


          Await.result(

            totalMigratedInThisRound,

            Duration.Inf

          )

          logger.info(s"For teamId $migration_teamId => totalMigratedInThisRound: $totalMigratedInThisRound, totalNumberOfProspectInTheCycle: $totalNumberOfProspectInTheCycle, limit: $limit")


        }
      }

      logger.info(s"Out of while loop migration done for teamId $migration_teamId")
    }(ec)
  }


}
