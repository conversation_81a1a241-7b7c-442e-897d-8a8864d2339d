package api.migration_utils

import play.api.Logging
import scalikejdbc.interpolation.SQLSyntax
import scalikejdbc.{AutoSession, DB, SQL, scalikejdbcSQLInterpolationImplicitDef}
import utils.security.EncryptionService

import scala.util.Try

object MigrateNewEncryptionKey extends Logging {

  private implicit val session: AutoSession.type = AutoSession

  private case class RowsToBeUpdated(
    id: Long,
    oldColumnValue: String
  )

  def migrate(
    tableName: String,
    oldColumnName: String,
    newColumnName: String,
    encKey: String
  ) = Try {

    def log(txt: String) = {

      logger.info("\n=====\n")
      logger.info(txt)
      logger.info("\n=====\n")

    }


    log(s"MigrateNewEncryptionKey.migrate: migrating $oldColumnName to $newColumnName in $tableName table")

    /* since the tables that we are dealing with now are all small in size/rows-count, we will just go ahead and select
    all relevant columns in one-go
     */

    val tableNameSql = SQLSyntax.createUnsafely(tableName)

    val oldColumnNameSql = SQLSyntax.createUnsafely(oldColumnName)

    val newColumnNameSql = SQLSyntax.createUnsafely(newColumnName)

    val rowsToBeUpdated: List[RowsToBeUpdated] = DB.readOnly { implicit session =>

      sql"""
           select

            id,
            $oldColumnNameSql

           from $tableNameSql

           where $oldColumnNameSql is not null
             AND $oldColumnNameSql != ''
             AND $newColumnNameSql is null
         """
        .map(rs => {
          RowsToBeUpdated(
            id = rs.long("id"),
            oldColumnValue = rs.string(oldColumnName)
          )
        })
        .list
        .apply()

    }

    log(s"found ${rowsToBeUpdated.length} rowsToBeUpdated")

    /*

    rowsToBeUpdated.foreach(row => {

      log(s"updating row with id ${row.id}")


      val encryptedValue = EncryptionService.encrypt(
        key = encKey,
        value = row.oldColumnValue
      )

      val updatedRowCount: Int = DB.autoCommit { implicit session =>

        sql"""
             update $tableNameSql set
              $newColumnNameSql = $encryptedValue
             where
              id = ${row.id}
           """
          .update
          .apply()

      }

      val isUpdated = updatedRowCount == 1

      if (!isUpdated) {

        log(s"FAILED updating row with id ${row.id}")

        throw new Exception(s"FAILED updating row with id ${row.id}")

      } else {

        log(s"Successfully updated row with id ${row.id}")

      }


    })
    */

    val updatedIds: Seq[Long] = rowsToBeUpdated
      .grouped(100)
      .flatMap(pgroup => {

        var valueParameters = List[Any]()

        val valuePlaceholder: String = pgroup
          .map(row => {

            val encryptedValue = EncryptionService.encrypt(
              key = encKey,
              value = row.oldColumnValue
            )

            valueParameters = valueParameters ::: List(
              row.id,
              encryptedValue
            )

            s"""
              (
                ?,
                ?
              )
            """

          })
          .mkString(", ")
        //      .reduce((vp1, vp2) => sqls"$vp1, $vp2")

        DB.autoCommit { implicit session =>


          // using without "sql" or "SQLSyntax" here because its difficult if not possible to pass the parametrized tableNameSql with a batchUpdate with those options
          SQL(
            s"""
              UPDATE $tableName cp
              SET
                $newColumnName = temp.newcolval

              FROM (VALUES $valuePlaceholder)
              AS temp(
                   id,
                   newcolval
              )
              WHERE cp.id = temp.id
              RETURNING cp.id
            """
              )
            .bind(valueParameters*)
            .map(rs => rs.long("id"))
            .list
            .apply()
        }


      })
      .toSeq

    log(s"successfully done migrating all rows (total updated: ${updatedIds.size})")


  }


}
