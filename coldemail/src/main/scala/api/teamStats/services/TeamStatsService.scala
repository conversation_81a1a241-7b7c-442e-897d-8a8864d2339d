package api.teamStats.services

import api.AppConfig
import io.sr.billing_common.models.PlanType
import api.accounts.models.{AccountId, OrgId}
import api.accounts.{ AccountService, ReportTeamStats, TeamId}
import api.teamStats.models.TeamInfo
import api.team_inbox.dao_service.ReplySentimentDAOService
import api.team_inbox.model.ReplySentimentType
import api.team_inbox.service.{ReplySentimentService, ReplySentimentUuid}
import org.joda.time.{DateTime, DateTimeZone}
import utils.{AppConfigUtil, SRLogger}

import scala.util.Try

class TeamStatsService(

                      accountService: AccountService,
                      agencyDashboardCacheService: AgencyDashboardCacheService,
                      replySentimentDAOService: ReplySentimentDAOService
                      ) {

  def getTeamNamesAndIdsBySendDate(
                                    orgId: Long,
                                    tids: Seq[Long]
                                  ): Try[List[TeamInfo]] = {

    accountService.getTeamNamesAndIdsBySendDate(
      orgId = OrgId(id = orgId), // FIXME VALUECLASS
      tids = tids.map(t => TeamId(id = t)) // FIXME VALUELASS
    )

  }

  def getTeamSummary(
                      tid: Long,
                      orgId: Long,
                      planType: PlanType,
                      aid: Long,
                      timePeriod: String,
                      from: Option[Long],
                      till: Option[Long]
                    )(using logger: SRLogger): Try[ReportTeamStats] = {

    val replySentimentUuidTry: Try[List[ReplySentimentUuid]] = replySentimentDAOService.getUuidsForTeamIdAndReplySentimentType(
      team_id = tid,
      replySentimentType = ReplySentimentType.Positive
    )
    replySentimentUuidTry.flatMap{replySentimentUuid =>
    planType match {
      case PlanType.TRIAL =>
        accountService.getTeamSummary(
          orgId = OrgId(id = orgId), // FIXME VALUECLASS
          tid = TeamId(id = tid), // FIXME VALUECLASS
          aid = AccountId(id = aid), // FIXME VALUECLASS
          from = from.map(d => new DateTime((d), DateTimeZone.UTC)),
          till = till.map(d => new DateTime((d), DateTimeZone.UTC)),
          positive_reply_sentiments = replySentimentUuid
        )


      case _ =>
        agencyDashboardCacheService.getTeamSummary(teamId = tid, timePeriod = timePeriod) match {
          case Some(team_summary) =>
            Try(team_summary)

          case None =>
            accountService.getTeamSummary(
              orgId = OrgId(id = orgId), // FIXME VALUECLASS
              tid = TeamId(id = tid), // FIXME VALUECLASS
              aid = AccountId(id = aid), // FIXME VALUECLASS
              from = from.map(d => new DateTime((d), DateTimeZone.UTC)),
              till = till.map(d => new DateTime((d), DateTimeZone.UTC)),
              positive_reply_sentiments = replySentimentUuid
            )
              .map(summary => {
                agencyDashboardCacheService.setTeamSummary(summary = summary, teamId = tid, timePeriod = timePeriod)
                summary
              })
        }
    }
    }
  }

}
