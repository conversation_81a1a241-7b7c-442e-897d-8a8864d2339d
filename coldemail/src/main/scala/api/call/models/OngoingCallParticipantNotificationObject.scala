package api.call.models

import api.accounts.models.AccountId
import play.api.libs.json.{Js<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Writes}

case class OngoingCallParticipantNotificationObject(
  participant_uuid: CallParticipantUuid,
  conference_uuid: ConferenceUuid,
  participant_account_id: Option[AccountId],
  ongoingCallProspectDetails: Option[OngoingCallProspectDetails],
  task_id: Option[String],
  latest_participation_mode: CallParticipationMode,
  participant_first_name: Option[String],
  calling_device: Option[CallingDevice],
  participant_last_name: Option[String],
  call_sid: Option[CallSID]
)

case object OngoingCallParticipantNotificationObject {
  implicit def writes: Writes[OngoingCallParticipantNotificationObject] = new Writes[OngoingCallParticipantNotificationObject] {

    override def writes(rs: OngoingCallParticipantNotificationObject): JsValue = {
      Json.obj(
        "participant_uuid" -> rs.participant_uuid,
        "conference_uuid" -> rs.conference_uuid,
        "participant_account_id" -> rs.participant_account_id,
        "ongoingCallProspectDetails" -> rs.ongoingCallProspectDetails,
        "task_id" -> rs.task_id,
        "latest_participation_mode" -> rs.latest_participation_mode,
        "participant_first_name" -> rs.participant_first_name,
        "participant_last_name" -> rs.participant_last_name,
        "calling_device" -> rs.calling_device
      )
    }
  }

}