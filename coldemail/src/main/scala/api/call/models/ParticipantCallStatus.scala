package api.call.models

import play.api.libs.json.{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON>ult, JsString, JsSuccess, JsValue}

import scala.util.{Failure, Success, Try}



sealed trait ParticipantCallStatus{

  def toString: String

}

object ParticipantCallStatus {

  private val ringing = "ringing"
  private val completed = "completed"
  private val no_answer = "no-answer"
  private val busy = "busy"
  private val initiated = "initiated"
  private val in_progress = "in-progress"
  private val canceled = "canceled"
  private val failed = "failed"
  private val queued = "queued"

  case object RINGING extends ParticipantCallStatus {
    override def toString: String = ringing
  }

  case object COMPLETED extends ParticipantCallStatus {
    override def toString: String = completed
  }

  case object NO_ANSWER extends ParticipantCallStatus {
    override def toString: String = no_answer
  }

  case object BUSY extends ParticipantCallStatus {
    override def toString: String = busy
  }

  case object INITIATED extends ParticipantCallStatus {
    override def toString: String = initiated
  }

  case object IN_PROGRESS extends ParticipantCallStatus {
    override def toString: String = in_progress
  }

  case object CANCELED extends ParticipantCallStatus {
    override def toString: String = canceled
  }

  case object FAILED extends ParticipantCallStatus {
    override def toString: String = failed
  }

  case object QUEUED extends ParticipantCallStatus {
    override def toString: String = queued
  }



  def fromString(key: String): Try[ParticipantCallStatus] = Try{

    key match {

      case `ringing` => RINGING

      case `completed` => COMPLETED

      case `no_answer` => NO_ANSWER

      case `busy` => BUSY

      case `initiated` => INITIATED

      case `in_progress` => IN_PROGRESS

      case `canceled` => CANCELED

      case `failed` => FAILED

      case `queued` => QUEUED

    }
  }

  given format: Format[ParticipantCallStatus] = new Format[ParticipantCallStatus] {

    def fromMap(values: Map[String, Seq[String]]): Try[ParticipantCallStatus] = {
      ParticipantCallStatus.fromString(values("CallStatus").head)
    }

    override def writes(o: ParticipantCallStatus): JsValue = {
      JsString(o.toString)
    }

    override def reads(json: JsValue): JsResult[ParticipantCallStatus] = {
      fromString(json.as[String]) match {
        case Success(value) => JsSuccess(value = value)
        case Failure(_) =>
          JsError(s"Invalid participant call status :: $json")
      }
    }
  }

}

