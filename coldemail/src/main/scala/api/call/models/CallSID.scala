package api.call.models

import api.call.traits.GetCallDetailsError
import play.api.libs.json.{JsError, JsResult, JsString, JsSuccess, JsValue, Reads, Writes}

case class CallSID(
  sid: String
) extends AnyVal

object CallSID {

  implicit val reads: Reads[CallSID] = new Reads[CallSID] {
    override def reads(ev: JsValue): JsResult[CallSID] = {
      ev match {
        case JsString(call_sid) => JsSuccess(CallSID(sid = call_sid))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[CallSID] = new Writes[CallSID] {
    override def writes(call_sid: CallSID): JsValue = JsString(call_sid.sid)
  }

  def extractCallSid(params: Map[String, String]): Either[GetCallDetailsError, CallSID] = {
    params.get("CallSid") match {
      case None =>
        Left(GetCallDetailsError.CallSidNotFound)

      case Some(sid) =>

        Right(CallSID(sid = sid))
    }
  }
}