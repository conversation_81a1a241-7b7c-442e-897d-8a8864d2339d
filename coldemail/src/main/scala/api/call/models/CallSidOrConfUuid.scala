package api.call.models

sealed trait CallSidOrConfUuid

object CallSidOrConfUuid{

  case class WithCallSid(
                          call_sid: CallSID
                        ) extends CallSidOrConfUuid


  case class WithConfUuid(
                          conferenceUuid: ConferenceUuid
                        ) extends CallSidOrConfUuid

}


sealed trait ConfSidOrConfUuid

object ConfSidOrConfUuid{

  case class WithConfSid(
                          conf_sid: ConferenceSid
                        ) extends ConfSidOrConfUuid


  case class WithConfUuid(
                           conferenceUuid: ConferenceUuid
                         ) extends ConfSidOrConfUuid

}

