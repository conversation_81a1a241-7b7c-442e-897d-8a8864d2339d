package api.call.models


sealed trait UpdateCallLogsError


object UpdateCallLogsError {

  case class ErrorWhileGettingSubAccount(err: Throwable) extends UpdateCallLogsError

  case class ErrorWhileUpdatingCallLogsLastUpdatedAt(err: Throwable) extends UpdateCallLogsError

  case class ErrorWhileSavingCallingLogs(err: Throwable) extends UpdateCallLogsError

  case class ErrorWhileAttachingTeamIdToCallLog(err: Throwable) extends UpdateCallLogsError

  case class ErrorWhileFetchingCallLogs(err: Throwable) extends UpdateCallLogsError

  case class ErrorWhileFetchingCallAccountSettings(err: Throwable) extends UpdateCallLogsError


}
