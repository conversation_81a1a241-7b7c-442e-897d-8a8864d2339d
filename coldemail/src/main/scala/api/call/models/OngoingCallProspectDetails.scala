package api.call.models

import api.prospects.models.ProspectId
import play.api.libs.json.{<PERSON><PERSON>, Writes}

case class OngoingCallProspectDetails(
  prospect_id: ProspectId,
  first_name: Option[String],
  last_name: Option[String],
  phone_number: Option[String]
)

case object OngoingCallProspectDetails {
  implicit def writes: Writes[OngoingCallProspectDetails] = Json.writes[OngoingCallProspectDetails]

}