package api.call.models



import play.api.libs.json.{Format, Js<PERSON>rror, JsResult, JsString, JsSuccess, JsValue}

import scala.util.{Failure, Success, Try}



sealed trait CallerIdVerificationStatus{

  def toString: String

}

object CallerIdVerificationStatus {

  private val initialised = "initialised"
  private val failed = "failed"
  private val success = "success"
  private val deleted = "deleted"

  case object INITIALISED extends CallerIdVerificationStatus {
    override def toString: String = initialised
  }

  case object FAILED extends CallerIdVerificationStatus {
    override def toString: String = failed
  }

  case object SUCCESS extends CallerIdVerificationStatus {
    override def toString: String = success
  }

  case object DELETED extends CallerIdVerificationStatus {
    override def toString: String = deleted
  }


  def fromString(key: String): Try[CallerIdVerificationStatus] = Try{

    key match {

      case `initialised` => INITIALISED

      case `failed` => FAILED

      case `success` => SUCCESS

      case `deleted` => DELETED

    }
  }

  given format: Format[CallerIdVerificationStatus] = new Format[CallerIdVerificationStatus] {
    override def writes(o: CallerIdVerificationStatus): JsValue = {
      JsString(o.toString)
    }

    override def reads(json: JsValue): JsResult[CallerIdVerificationStatus] = {
      fromString(json.as[String]) match {
        case Success(value) => JsSuccess(value = value)
        case Failure(_) => JsError(s"Invalid caller id verification status :: $json")

      }
    }
  }

}

