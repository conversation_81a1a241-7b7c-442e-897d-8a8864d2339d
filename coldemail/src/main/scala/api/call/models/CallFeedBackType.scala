package api.call.models

import play.api.libs.json.{<PERSON>at, <PERSON>s<PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON>ult, JsString, <PERSON>sSuc<PERSON>, JsValue}

sealed trait CallFeedBackType {

  def toString: String

}

object CallFeedBackType {

  private val POSITIVE = "positive"
  private val NEGATIVE = "negative"

  case object Positive extends CallFeedBackType {

    override def toString : String = POSITIVE

  }

  case object Negative extends CallFeedBackType {

    override def toString : String = NEGATIVE

  }

  def withName(key: String): Option[CallFeedBackType] = {
    key match {
      case `POSITIVE` => Some(Positive)
      case `NEGATIVE` => Some(Negative)
      case _ => None
    }
  }

  given format: Format[CallFeedBackType] = new Format[CallFeedBackType] {
    override def writes(o: CallFeedBackType): JsValue = {
      JsString(o.toString)
    }

    override def reads(json: JsValue): JsResult[CallFeedBackType] = {
      withName(json.as[String]) match {
        case None => JsError("Invalid call_feedback_type")
        case Some(value) => JsSuccess(value)
      }
    }
  }



}
