package api.call.models

import api.accounts.TeamId
import api.accounts.models.AccountId
import play.api.libs.json.{Json, OFormat, Reads, Writes}


case class CallAccountSettings(
                                uuid: PhoneNumberUuid,
                                phone_number: Option[PhoneNumber],
                                first_name: String,
                                last_name: String,
                                owner_account_id: AccountId,
                                team_id: TeamId,
                                call_limit_per_day: Int,
                                phone_type: Option[PhoneType],
                                country: Option[String],
                                enable_forwarding: Boolean,
                                forward_to: Option[String],
                                service_provider: CallingServiceProvider,
                                org_sub_account_uuid: Option[SubAccountUuid],
                                is_active: <PERSON><PERSON><PERSON>,
                                record_call: <PERSON><PERSON><PERSON>,
                                caller_id: Option[CallerIdUuid]
                              )

object CallAccountSettings {
  given writes: Writes[CallAccountSettings] = Json.writes[CallAccountSettings]
  given reads: Reads[CallAccountSettings] = Json.reads[CallAccountSettings]
}


case class VerifiedCallerIds(
                              uuid: CallerIdUuid,
                              phone_number: <PERSON>N<PERSON><PERSON>,
                              name: String,
                              owner_account_id: AccountId,
                              team_id: TeamId,
                              status: CallerIdVerificationStatus
                            )

object VerifiedCallerIds {
  given writes: Writes[VerifiedCallerIds] = Json.writes[VerifiedCallerIds]
  given reads: Reads[VerifiedCallerIds] = Json.reads[VerifiedCallerIds]
}
