package api.call.models

import play.api.libs.json.{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, JsString, JsSuc<PERSON>, JsV<PERSON>ue}

sealed trait CallFeedBackReasons {

  def toString: String

}

object CallFeedBackReasons {

  private val oneWayAudio = "one-way-audio"
  private val imperfectAudio = "imperfect-audio"
  private val audioLatency = "audio-latency"
  private val droppedCall = "dropped-call"
  private val postDialDelay = "post-dial-delay"
  private val incorrectCallerId = "incorrect-caller-id"
  private val digitsNotCaptured = "digits-not-captured"
  private val unsolicitedCall = "unsolicited-call"


  case object OneWayAudio extends CallFeedBackReasons {

    override def toString: String = oneWayAudio

  }

  case object ImperfectAudio extends CallFeedBackReasons {

    override def toString: String = imperfectAudio

  }

  case object AudioLatency extends CallFeedBackReasons {

    override def toString: String = audioLatency

  }

  case object DroppedCall extends CallFeedBackReasons {

    override def toString: String = droppedCall

  }

  case object PostDialDelay extends CallFeedBackReasons {

    override def toString: String = postDialDelay

  }

  case object IncorrectCallerId extends CallFeedBackReasons {

    override def toString: String = incorrectCallerId

  }

  case object DigitsNotCaptured extends CallFeedBackReasons {

    override def toString: String = digitsNotCaptured

  }

  case object UnSolicitedCall extends CallFeedBackReasons {

    override def toString: String = unsolicitedCall

  }

  def fromString(value: String): Option[CallFeedBackReasons]  = {
    value match {
    case `oneWayAudio` => Some(OneWayAudio)
    case `imperfectAudio` => Some(ImperfectAudio)
    case `audioLatency` => Some(AudioLatency)
    case `droppedCall` => Some(DroppedCall)
    case `postDialDelay` => Some(PostDialDelay)
    case `incorrectCallerId` => Some(IncorrectCallerId)
    case `digitsNotCaptured` => Some(DigitsNotCaptured)
    case `unsolicitedCall` => Some(UnSolicitedCall)
    case _ => None
    }
  }

  given format: Format[CallFeedBackReasons] = new Format[CallFeedBackReasons] {
    override def writes(o: CallFeedBackReasons): JsValue = {
      JsString(o.toString)
    }

    override def reads(json: JsValue): JsResult[CallFeedBackReasons] = {
      fromString(json.as[String]) match {
        case None => JsError("Invalid user_issues type")
        case Some(value) => JsSuccess(value)
      }
    }
  }

}
