package api.call.models

import scala.util.Try

case class IncomingCallParticipantStatusCallbackData(
  /* NOTE:
                                                        a basic incoming call to twilio contains two legs
                                                        1. customer -> twilio server
                                                        2. twilio server -> registered client
                                                        This corresponds to two call sids
                                                        The main initiator leg sid (customer -> twilio) is the parent_call_sid
                                                        the second leg sid (twilio server -> registered client) is the call_sid
                                                        the webhook is received for the various status to the call sid
                                                        -> initiated/in-progress/ ringing etc
                                                       */
  status: ParticipantCallStatus,
  parent_call_sid: CallSID,
  call_sid: CallSID,
  call_from: PhoneNumber

)

object IncomingCallParticipantStatusCallbackData {
  def fromMap(values: Map[String, Seq[String]]): Try[IncomingCallParticipantStatusCallbackData] = Try {

    IncomingCallParticipantStatusCallbackData(
      status = ParticipantCallStatus.fromString(key = values("CallStatus").head).get,
      call_sid = CallSID(sid = values("CallSid").head),
      parent_call_sid = CallSID(sid = values("ParentCallSid").head),
      call_from = PhoneNumber(phone_number = values("From").head)
    )

  }
}