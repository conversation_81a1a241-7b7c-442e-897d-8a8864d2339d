package api.call.models

import scala.util.Try

case class OutgoingBasicCallStatusCallbackData(
  status: ParticipantCallStatus,
  parent_call_sid: CallSID,
  call_sid: CallSID,
  call_from: PhoneNumber
)

object OutgoingBasicCallStatusCallbackData {
  def fromMap(values: Map[String, Seq[String]]): Try[OutgoingBasicCallStatusCallbackData] = Try {
    OutgoingBasicCallStatusCallbackData(
      status = ParticipantCallStatus.fromString(key = values("CallStatus").head).get,
      call_sid = CallSID(sid = values("CallSid").head),
      parent_call_sid = CallSID(sid = values("ParentCallSid").head),
      call_from = PhoneNumber(phone_number = values("From").head)
    )

  }
}