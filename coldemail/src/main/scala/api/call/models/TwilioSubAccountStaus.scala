package api.call.models

import play.api.libs.json.{Format, JsError, JsResult, JsString, JsSuccess, JsValue}

import scala.util.{Failure, Success, Try}

sealed trait TwilioSubAccountStatus{

  def toString: String

}

object TwilioSubAccountStatus {

  private val active = "active"
  private val suspended = "suspended"

  case object ACTIVE extends TwilioSubAccountStatus {
    override def toString: String = active
  }

  case object SUSPENDED extends TwilioSubAccountStatus {
    override def toString: String = suspended
  }

  def fromString(key: String): Try[TwilioSubAccountStatus] = Try{

    key match {

      case `active` => ACTIVE
      case `suspended` => SUSPENDED

    }
  }

  given format: Format[TwilioSubAccountStatus] = new Format[TwilioSubAccountStatus] {
    override def writes(o: TwilioSubAccountStatus): JsValue = {
      JsString(o.toString)
    }

    override def reads(json: JsValue): JsR<PERSON>ult[TwilioSubAccountStatus] = {
      fromString(json.as[String]) match {
        case Success(value) => JsSuccess(value = value)
        case Failure(_) => JsError(s"Invalid sub account status :: $json")

      }
    }
  }

}