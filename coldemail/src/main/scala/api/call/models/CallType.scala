package api.call.models

import api.call.traits.GetCallDetailsError
import play.api.libs.json.{Format, JsErro<PERSON>, Js<PERSON><PERSON>ult, JsString, JsSuccess, JsValue}

import scala.util.{Failure, Success, Try}

sealed trait CallType {
  def toString: String

}


object CallType {
//  native-outgoing / twl-basic / twl-conf

  private val twl_basic = "twl_basic"
  private val twl_conf = "twl_conf"
  private val native_outgoing = "native_outgoing"

  case object TWL_BASIC extends CallType {
    override def toString: String = twl_basic
  }

  case object TWL_CONF extends CallType {
    override def toString: String = twl_conf
  }

  case object NATIVE_OUTGOING extends CallType {
    override def toString: String = native_outgoing
  }


  def fromString(key: String): Try[CallType] = Try{

    key match {

      case `twl_basic` => TWL_BASIC

      case `twl_conf` => TWL_CONF

      case `native_outgoing` => NATIVE_OUTGOING

    }
  }

  def extractCallChoice(params: Map[String, String]): Either[GetCallDetailsError, CallType] = {
    params.get("call_choice") match {
      case None => Right(CallType.TWL_CONF)


      case Some(device) =>

        CallType.fromString(key = device) match {

          case Success(value) => Right(value)

          case Failure(exception) => Left(GetCallDetailsError.IncorrectCallChoice(err = exception))

        }
    }
  }

  given format: Format[CallType] = new Format[CallType] {
    override def writes(o: CallType): JsValue = {
      JsString(o.toString)
    }

    override def reads(json: JsValue): JsResult[CallType] = {
      fromString(json.as[String]) match {
        case Success(value) => JsSuccess(value = value)
        case Failure(_) => JsError(s"Invalid call type :: $json")

      }
    }
  }

}

