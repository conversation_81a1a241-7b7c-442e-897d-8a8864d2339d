package api.call.models

import scala.util.Try

case class VerifyCallerIdStatusCallbackData(
  phone_number: PhoneNumber,
  verificationStatus: CallerIdVerificationStatus,
  outgoingCallerIdSid: Option[OutgoingCallerIdSid]
)

object VerifyCallerIdStatusCallbackData {
  def fromMap(values: Map[String, Seq[String]]): Try[VerifyCallerIdStatusCallbackData] = Try {
    VerifyCallerIdStatusCallbackData(
      phone_number = PhoneNumber(phone_number = values("Called").head),
      verificationStatus = CallerIdVerificationStatus.fromString(values("VerificationStatus").head).get,
      outgoingCallerIdSid = values.get("OutgoingCallerIdSid").map(sid => OutgoingCallerIdSid(sid.head)),
    )
  }
}