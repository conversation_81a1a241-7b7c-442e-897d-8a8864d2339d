package api.call.models

import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, JsResult, JsString, JsSuccess, JsValue, Reads, Writes}

case class ConferenceFriendlyName(name: String) extends AnyVal {

  override def toString: String = name

}

object ConferenceFriendlyName{

  given reads: Reads[ConferenceFriendlyName] = new Reads[ConferenceFriendlyName] {
    override def reads(ev: JsValue): JsResult[ConferenceFriendlyName] = {
      ev match {
        case JsString(name) => JsSuccess(ConferenceFriendlyName(name = name))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }
  }

  given writes: Writes[ConferenceFriendlyName] = new Writes[ConferenceFriendlyName] {
    override def writes(uuid: ConferenceFriendlyName): JsValue = JsString(uuid.name)
  }

}