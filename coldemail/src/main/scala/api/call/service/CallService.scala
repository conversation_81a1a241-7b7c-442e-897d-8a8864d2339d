package api.call.service

import api_layer_models.{ConvertedCurrency, CurrencyType, ErrorWhileCurrencyConversion}
import api.{ApiVersion, AppConfig, CONSTANTS, ChannelSettingServiceTrait}
import api.AppConfig.Calling.call_logs_per_page
import api.AppConfig.RollingUpdates
import api.accounts.AccountWarningCodeType.LowCallingCredit
import api.accounts.dao.OrganizationDAO
import api.accounts.dao_service.OrganizationDAOService
import api.accounts.{Account, AccountDAO, AccountWarningCodeType, OrgPlan, OrganizationWithCurrentData, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.accounts.service.{OrganizationService, ResetUserCacheUtil}
import api.call.DAO.{CallDAO, CallHistoryLogDAO, ParticipantDetails, ParticipantUpdateDetails, ProspectDetailsForIncomingCalls}
import api.twilio.service.{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>og, ConferenceCallUpdateLog, ConferenceDetailsFromWebhook, NewConfCallLog, ParticipantCallUpdateLog, ParticipantDetailsFromWebhook, SubAccountApiKeyAndSecret, SubAccountTwimlApplication, TwilioConferenceAndParticipantDetails, TwilioDialerService, TwilioSubAccountDetails, TwilioUsageTrigger, TwlTwimlAppName}
import api.call.models.{AddCallingCreditDetails, CallAccountSettings, CallDetails, CallFeedBack, CallFeedBackResponse, CallFeedBackType, CallNote, CallParticipantUuid, CallParticipationMode, CallParticipationModeData, CallSID, CallSidOrConfUuid, CallStatus, CallStatusChangeData, CallType, CallerIdUuid, CallerIdVerificationStatus, CallerIdentity, CallingDevice, CallingRemainingCredits, CallingServiceProvider, ConfSidOrConfUuid, ConferenceFriendlyName, ConferenceSid, ConferenceUuid, CountryObject, GetCallLogForUserError, GetRemainingCallingCreditError, IncomingCallParticipantStatusCallbackData, InitialCallParticipationModeData, NewSubAccountDetails, NumberPriceObject, OngoingCallParticipantNotificationObject, OngoingCallProspectDetails, OrgCallWarningErrorDetails, OutgoingBasicCallStatusCallbackData, OutgoingCallerIdSid, ParentCallingAlertData, ParticipantCallStatus, ParticipantDetailsToNotify, ParticipantStatusCallbackData, PhoneNumberCreditsRecords, PhoneNumberFormatData, PhoneNumberToReleaseData, PhoneNumberUuid, PhoneNumberValidationError, PhoneSID, PriceObject, RecordingData, ReleasePhoneNumberForOrgError, RemainingCallingCreditDetails, SrPhoneNumber, StatusCallbackData, StatusCallbackEvents, SubAccountDetails, SubAccountDetailsForUpdate, SubAccountDetailsFound, SubAccountStatus, SubAccountUuid, TaskUuidAndProspectID, TeamIdAndAccountDetails, TimezoneToCountryCodeList, TwilioUsageLimitCrossedDetails, TwlAuthToken, TwlSubAccountSid, UpdateCallLogsError, UpdateRecordCallForm, UsageDetails, ValidateCallHistoryReq, ValidatedParamsForCallLogs, VerifiedCallerIds, VerifyCallerIdStatusCallbackData, VoiceDropDetails, WebhookValidateData, PhoneNumber as PhoneNumberValueClass}
import api.call.controller.{CreateOrUpdateCallAccountData, NativeCallingAccountData, TwilioIdentityAndToken, ValidateCallerIdData}
import api.call.models.CallLogResource.{CallLogFromDB, CallLogPagination, NewCallLog}
import api.call.models.CallSidOrConfUuid.{WithCallSid, WithConfUuid}
import api.call.models.ConfSidOrConfUuid.WithConfSid
import api_layer_models.CurrencyType
import api.call.models.InitialCallParticipationModeData.ListenModeData
import api.call.{models, service}
import api.call.traits.ActivatingSubAccountError.ErrorWhileActivatingSubAccount
import api.call.service.CallService.validatePhoneNumberBeforeCallCompanion
import api.call.traits.GetCallDetailsError.IncorrectPathForCallType
import api.call.traits.{ActivatingSubAccountError, AddCallingCreditsError, AddOrgCallingCreditWarningError, BuyNumberError, CallQualityFeedBackError, ConferenceParticipantStatusUpdateError, CreateApiKeyAndSecretError, CreateTwimlApplicationError, CreateUsageTriggerError, DeleteNumberError, DeleteUsageTriggerError, GetAccessTokenError, GetAvailableNumberError, GetCallDetailsError, GetNumberPriceError, GetSubAccountUsageError, HandleCallError, HandlingUsageWebhookError, IdentityGenerationError, IncomingCallParticipantStatusUpdateError, OutgoingBasicCallStatusUpdateError, ParticipantStatusUpdateError, ProspectCallStatusUpdateError, RecordingStatusCallbackError, RemoveCallerIdError, SaveCallingAccountError, SaveLogError, SaveNoteError, SubAccountCreationError, UpdateCallSettingsError, UpdateSubAccountCreditError, ValidateWebhookError, VerifyCallerIdError}
import api.campaigns.models.VoicemailId
import api.campaigns.services.CampaignId
import api.notes.dao.NotesDAO
import api.notes.models.NotesError.{DoesNotHavePermissionError, NotesDBFailure, NotesEmptyError, NotesNotFoundError, NotesPaginationError}
import api.notes.models.{CreateNotesForm, GetNotesForm, Note, NoteId, NotesError}
import api.prospects.dao.ProspectDAO
import api.prospects.{CreateProspectEventDB, InferredQueryTimeline, NavigationLinks}
import api.prospects.dao.ProspectAddEventDAO
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.ProspectId
import api.sr_audit_logs.models.EventType
import api.tasks.models.TaskType
import api.tasks.pgDao.TaskPgDAO
import api.tasks.services.TaskPagination
import api.tasks.services.TaskUuid
import com.twilio.`type`.{PhoneNumber, PhoneNumberPrice}
import com.twilio.rest.api.v2010.account.{Call, IncomingPhoneNumber}
import org.joda.time.DateTime
import play.api.mvc.{AnyContent, Request}
import utils.{AddonLimitReachedException, CallUtils, Helpers, PlanLimitService, PusherService, SRLogger, StringUtilsV2}
import utils.uuid.SrUuidUtils
import play.api.libs.json.{JsError, JsResult, JsString, JsSuccess, JsValue, Json, OFormat, Reads, Writes}
import play.api.libs.json.JodaReads.*
import com.twilio.rest.api.v2010.account.conference.Participant
import play.api.libs.ws.WSClient
import api_layer_service.ApiLayerService
import com.google.i18n.phonenumbers.{NumberParseException, PhoneNumberUtil}
import com.google.i18n.phonenumbers.PhoneNumberUtil.PhoneNumberFormat
import com.twilio.base.Resource
import eventframework.ProspectObject
import io.sr.billing_common.models.{AddonLicenceType, PlanID, PlanType}
import sr_scheduler.models.ChannelType
import utils.cache.models.SrResetCacheInterval
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import utils.email_notification.models.{EmailNotificationLog, NotificationType}
import utils.email_notification.service.{EmailNotificationService, ErrorWhileGettingLatestEmailLog}
import utils.jodatimeutils.JodaTimeUtils
import utils.mq.channel_scheduler.channels.{ChannelId, ChannelSchedulerTrait}
import utils.phonenumber.PhoneNumberValidator
import utils.phonenumber.PhoneNumberValidator.{isValidPhoneNumber, parseNumber}
import utils.pusher.{SrPushEventNames, SrPushEventNamespace}

import java.time.ZoneId
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import java.util.TimeZone
import com.neovisionaries.i18n.CountryCode
import utils.helpers.LogHelpers

import scala.jdk.CollectionConverters.ListHasAsScala













































































































































































































class CallService(
                   twilioDialerService: TwilioDialerService,
                   srUuidUtils: SrUuidUtils,
                   callDAO: CallDAO,
                   prospectAddEventDAO: ProspectAddEventDAO,
                   pusherService: PusherService,
                   organisationDAO: OrganizationDAO,
                   prospectDAO: ProspectDAO,
                   resetCacheUtil: ResetUserCacheUtil,
                   accountDAO: AccountDAO,
                   planLimitService: PlanLimitService,
                   apiLayerService: ApiLayerService,
                   organizationDAOService: OrganizationDAOService,
                   override protected val srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2,
                   emailNotificationService: EmailNotificationService,
                   callUtils: CallUtils,
                   callHistoryLogDAO: CallHistoryLogDAO,
                   prospectDAOService: ProspectDAOService,
                   notesDAO: NotesDAO
                 ) extends ChannelSettingServiceTrait {

  override val channelType: ChannelType = ChannelType.CallChannel

  def getAvailableNumber(
                          countryCode: String,
                          sub_account_sid: TwlSubAccountSid,
                          sub_account_auth_token: TwlAuthToken
                        )(using Logger: SRLogger): Either[GetAvailableNumberError, PhoneNumber] = {

    twilioDialerService.getAvailableNumbers(
      countryCode = countryCode,
      sub_account_sid = sub_account_sid,
      sub_account_auth_token = sub_account_auth_token
    )
    match {
      case Failure(exception) => Left(GetAvailableNumberError.GetAvailableNumberApiError(err = exception))

      case Success(None) => Left(GetAvailableNumberError.NoNumberFound(err = "No number found"))

      case Success(Some(value)) => Right(value)
    }


  }

  def getPricing(countryCode: String): Either[GetNumberPriceError, NumberPriceObject] = {
    twilioDialerService.getPricing(countryCode = countryCode)
  }

  def getCallAccountSettings(teamId: TeamId, permittedAccountIds: Seq[Long]): Try[List[CallAccountSettings]] = {
    callDAO.getCallAccountSettings(
      teamId = teamId,
      permittedAccountIds = permittedAccountIds
    )
  }


  def getAllCallerIds(teamId: TeamId): Try[List[VerifiedCallerIds]] = {

    callDAO.getVerifiedCallerIds(
      status = None,
      teamId = teamId
    )
  }

  def getVerifiedCallerIds(teamId: TeamId): Try[List[VerifiedCallerIds]] = {
    callDAO.getVerifiedCallerIds(
      status = Some(CallerIdVerificationStatus.SUCCESS),
      teamId = teamId
    )
  }

  def getTotalCallAccountCountForOrg(org_id: OrgId): Try[Int] = {

    callDAO.getTotalCallAccountsCountInOrg(org_id = org_id)

  }

  def getSubACcountDetailsForOrgUsingSubAccountUuid(subAccountUuid: SubAccountUuid): Try[SubAccountDetails] = {

    callDAO.getSubAccountDetailsForOrgUsingSubAccountUuid(
      sub_account_uuid = subAccountUuid
    ) match {

      case Failure(err) =>
        
        Failure(err)

      case Success(None) =>

        Failure(new Exception(CONSTANTS.API_MSGS.ERR_NO_SUBACCOUNTS_FOUND))

      case Success(Some(data)) =>

        Success(data)

    }

  }

  def setRecordCall(
                     updateRecordCallForm: UpdateRecordCallForm,
                     teamId: TeamId,
                     accountId: AccountId
                   ): Try[Int] = {

    callDAO.setRecordCall(
      updateRecordCallForm = updateRecordCallForm,
      teamId = teamId,
      accountId = accountId
    )

  }

  def getSubAccountDetails(
                            sub_account_uuid: Option[SubAccountUuid],
                            org_id: OrgId)(using Logger: SRLogger): Try[SubAccountDetails] = {

    callDAO.getSubAccountDetailsForOrg(
      sub_account_uuid = sub_account_uuid,
      org_id = org_id
    ).flatMap {
      case None => Failure(new Exception(CONSTANTS.API_MSGS.ERR_NO_SUBACCOUNTS_FOUND))
      case Some(data) => Success(data)
    }
  }

  private def createNewSubAccountInTwilio(
                                           usage_limit_via_plan: Long,
                                           org_id: OrgId
                                         )
                                         (using logger: SRLogger
                                         ): Either[SubAccountCreationError.ErrorWhileCreatingSubAccountForOrg, NewSubAccountDetails] = {

    twilioDialerService.createSubAccount() match {

      case Failure(err) =>

        logger.fatal(s"Error while creating sub-account on twilio :: org_id : ${org_id.id}")

        Left(SubAccountCreationError.ErrorWhileCreatingSubAccountForOrg(err))

      case Success(twilioSubAccountDetails) =>

        val newSubAccount = NewSubAccountDetails(
          uuid = SubAccountUuid(uuid = srUuidUtils.generateSubAccountUuid()),
          sub_account_sid = twilioSubAccountDetails.sub_account_sid,
          auth_token = twilioSubAccountDetails.auth_token,
          sub_account_name = twilioSubAccountDetails.sub_account_name,
          status = twilioSubAccountDetails.status,
          owner_account_sid = twilioSubAccountDetails.owner_account_sid,
          call_remaining_credit = usage_limit_via_plan * 100,
          call_credit_updated_at = DateTime.now(), // One time only, this will keep updating when call_credit_updated_at
          credit_unit = CurrencyType.USD, // Twilio only supports usd currency
          org_id = org_id,
          call_credit = usage_limit_via_plan * 100,
          check_usage_from = DateTime.now(), // One time only, this will keep varying  // Currently not using it, might be dropping the row later
          previous_usage_deducted = None
        )
        Right(newSubAccount)

    }
  }

  private def saveNewlyCreatedSubAccountDetails(
                                                 subAccountDetails: NewSubAccountDetails
                                               )(using logger: SRLogger): Either[SubAccountCreationError, SubAccountUuid] = {
    callDAO.saveSubAccountDetails(
      subAccountDetails = subAccountDetails
    ) match {
      case Failure(err) =>
        Left(SubAccountCreationError.ErrorWhileSaveSubAccountDetailsInDB(err))

      case Success(sub_account_uuid) =>
        Right(sub_account_uuid)

    }

  }

  private def createUsageTriggerForSubAccount(
                                               newly_created_sub_account: NewSubAccountDetails,
                                               usage_limit: Long
                                             )(using logger: SRLogger): Either[SubAccountCreationError, TwilioUsageTrigger] = {

    twilioDialerService.createUsageTrigger(
      org_id = newly_created_sub_account.org_id,
      usage_limit = usage_limit.toString,
      subaccount_sid = newly_created_sub_account.sub_account_sid,
      subaccount_auth_token = newly_created_sub_account.auth_token
    ) match {

      case Failure(err) =>

        Left(SubAccountCreationError.FailureWhileCreatingUsageTrigger(err))

      case Success(newly_created_trigger_details) =>

        Right(newly_created_trigger_details)

    }
  }

  private def saveTwilioTriggerDetails(usage_trigger: TwilioUsageTrigger) = {

    callDAO.saveTwilioTriggerDetails(
      usageTrigger = usage_trigger
    ) match {

      case Failure(err) =>

        Left(SubAccountCreationError.ErrorWhileSaveTriggerDetailsInDB(err))

      case Success(newly_created_trigger_id) =>
        Right(newly_created_trigger_id)

    }
  }

  def sendWebNotificationForOngoingCall(
                                         teamId: TeamId,
                                         orgId: OrgId,
                                         conferenceParticipantDetails: Seq[OngoingCallParticipantNotificationObject],
                                         event_name: SrPushEventNamespace
                                       )(using Logger: SRLogger): Try[String] = {

    // TODO: unused event name
    val callStartedEventName = "conference.started" //TODO : check this

    /*
    We will be creating a channel which will be shared across the team and passing data relating to conference and participants in active mode
    This will help reflect the changes in the frontend in real time.
    The pusher will trigger the event and pass the notification to whoever is subscribed to the channel and will get the responses as specifies.
     */

    pusherService.sendMessageUsingPusher(
      channel_name = PusherService.getPusherChannelNameForCallConferenceParticipantDetails(teamId = teamId, orgId = orgId),
      event_name = event_name,
      message = Json.toJson(conferenceParticipantDetails),
      logger = Logger
    )

  }

  def createSubAccount(
                        org_id: OrgId,
                        org_plan: OrgPlan,
                      )(using Logger: SRLogger): Either[SubAccountCreationError, NewSubAccountDetails] = {

    Logger.info(s"No sub-account found for the org: ${org_id}")

    // fixme call
    val usage_limit_via_plan = PlanID.getInitialCallingCreditForOrg(
      planId = org_plan.plan_id
    )

    for {
      //Type: NewSubAccountDetails
      create_new_sub_account <- createNewSubAccountInTwilio(
        usage_limit_via_plan = usage_limit_via_plan
        , org_id = org_id
      )

      //Type: SubAccountUuid
      saveSubAccountDetails <- saveNewlyCreatedSubAccountDetails(
        subAccountDetails = create_new_sub_account
      )

      //Type: TwilioUsageTrigger
      create_usage_trigger_for_sub_account <- createUsageTriggerForSubAccount(
        newly_created_sub_account = create_new_sub_account,
        usage_limit = usage_limit_via_plan
      )

      //Type: String
      save_newly_created_trigger <- saveTwilioTriggerDetails(
        usage_trigger = create_usage_trigger_for_sub_account
      )

      // Type : SubAccountApiKeyAndSecret
      create_api_key_and_secret_for_sub_account <- createApiKeyAndSecretForSubAccount(
        sub_account_sid = create_new_sub_account.sub_account_sid,
        sub_auth_token = create_new_sub_account.auth_token,
        sub_account_uuid = create_new_sub_account.uuid
      ).left.map(err => SubAccountCreationError.ErrorWhileCreatingApiKeyAndSecret(err))

      // Type: ApiKey
      save_api_key_and_secret <- saveSubAccountApiKeyAndSecret(
        api_data = create_api_key_and_secret_for_sub_account,
        sub_account_uuid = create_new_sub_account.uuid,
        org_id = create_new_sub_account.org_id
      ).left.map(err => SubAccountCreationError.ErrorWhileCreatingApiKeyAndSecret(err))

      // Type: SubAccountTwimlApplication
      create_twiml_app_for_sub_account <- createSubAccountTwimlApplication(
        sub_account_sid = create_new_sub_account.sub_account_sid,
        sub_auth_token = create_new_sub_account.auth_token,
        sub_account_uuid = create_new_sub_account.uuid
      ).left.map(err => SubAccountCreationError.ErrorWhileCreatingTwimlApp(err))


      save_twiml_app_details <- saveSubAccountTwimlApplicationData(
        sub_account_uuid = create_new_sub_account.uuid,
        twiml_app = create_twiml_app_for_sub_account,
        org_id = create_new_sub_account.org_id
      ).left.map(err => SubAccountCreationError.ErrorWhileCreatingTwimlApp(err))

    } yield {
      create_new_sub_account
    }

  }

  def createSubAccountIfNotExist(
                                  org_id: OrgId,
                                  org_plan: OrgPlan
                                )(using Logger: SRLogger): Either[SubAccountCreationError, SubAccountDetailsFound] = {

    getSubAccountDetails(
      sub_account_uuid = None,
      org_id = org_id
    ) match {

      case Failure(exception) =>
        Logger.fatal(s"Error while getting sub account details for org ${org_id}")
        
        // Check if this is a "not found" error vs SQL error
        if (exception.getMessage == CONSTANTS.API_MSGS.ERR_NO_SUBACCOUNTS_FOUND) {
          createSubAccount(
            org_id = org_id,
            org_plan = org_plan
          ) match {
            case Left(err) =>
              Left(err)

            case Right(new_sub_account) =>
              Right(SubAccountDetailsFound(
                sub_account_sid = new_sub_account.sub_account_sid,
                sub_auth_token = new_sub_account.auth_token,
                org_sub_account_uuid = new_sub_account.uuid,
                call_remaining_credit_cents = new_sub_account.call_remaining_credit
              ))
          }
        } else {
          Left(SubAccountCreationError.ErrorWhileGettingSubAccount(err = exception))
        }

      case Success(subAccountDetails) =>
        Right(SubAccountDetailsFound(
          sub_account_sid = subAccountDetails.sub_account_id,
          sub_auth_token = subAccountDetails.sub_auth_token,
          org_sub_account_uuid = subAccountDetails.uuid,
          call_remaining_credit_cents = subAccountDetails.call_remaining_credit_cents
        ))

    }
  }


  def addNumberToDBAndResetCache(
                                  orgID: OrgId,
                                  accountId: AccountId,
                                  teamId: TeamId,
                                  addNumberData: CreateOrUpdateCallAccountData,
                                  phoneUUID: PhoneNumberUuid,
                                  callingServiceProvider: CallingServiceProvider = CallingServiceProvider.TWILIO,
                                  phoneNumber: models.PhoneNumber,
                                  phoneNumberSID: PhoneSID,
                                  orgSubAccountUUID: SubAccountUuid,
                                  phone_number_cost_cents: Double,
                                  price_unit: String
                                )(using logger: SRLogger): Either[BuyNumberError.SQLException, PhoneNumberValueClass] = {

    callDAO.addNumberToDB(
      accountId = accountId,
      teamId = teamId,
      data = addNumberData,
      phone_uuid = phoneUUID,
      callingServiceProvider = callingServiceProvider,
      phone_number = phoneNumber,
      phone_number_sid = phoneNumberSID,
      org_sub_account_uuid = orgSubAccountUUID,
      phone_number_cost_cents = phone_number_cost_cents,
      price_unit = price_unit
    ) match {

      case Failure(exception) =>
        Left(BuyNumberError.SQLException(err = exception, phoneNumber = phoneNumber))

      case Success(value: PhoneNumberValueClass) =>
        organizationDAOService.resetOrgDataFromCache(
          orgId = orgID,
          resetCacheInterval = SrResetCacheInterval.Immediately
        )
        Right(value)
    }

  }

  def updateNumberToDBAndResetCache(
                                     orgID: OrgId,
                                     teamId: TeamId,
                                     addNumberData: CreateOrUpdateCallAccountData,
                                     phoneUUID: PhoneNumberUuid,
                                     callingServiceProvider: CallingServiceProvider = CallingServiceProvider.TWILIO,
                                     phoneNumber: models.PhoneNumber,
                                     phoneNumberSID: PhoneSID,
                                     orgSubAccountUUID: SubAccountUuid
                                   )(using logger: SRLogger): Either[BuyNumberError.SQLException, PhoneNumberValueClass] = {

    callDAO.upgradeCallingSettings(
      teamId = teamId,
      data = addNumberData,
      phone_uuid = phoneUUID,
      callingServiceProvider = callingServiceProvider,
      phone_number = phoneNumber,
      phone_number_sid = phoneNumberSID,
      org_sub_account_uuid = orgSubAccountUUID
    ) match {

      case Failure(exception) =>
        Left(BuyNumberError.SQLException(err = exception, phoneNumber = phoneNumber))

      case Success(value: PhoneNumberValueClass) =>
        organizationDAOService.resetOrgDataFromCache(
          orgId = orgID,
          resetCacheInterval = SrResetCacheInterval.Immediately
        )
        Right(value)
    }

  }

  def checkBalance(
                    addNumberData: CreateOrUpdateCallAccountData,
                    priceObject: NumberPriceObject,
                    remaining_balance_cents: Long,
                  ): Try[Boolean] = {

    val priceForLocal = priceObject.prices.find(p => p.phone_type == addNumberData.phone_type.toString)
    val remaining_balance_dollars: Double = remaining_balance_cents / 100.0

    priceForLocal match {

      case None => Failure(new Exception("Number price object not found"))

      case Some(value) => if (remaining_balance_dollars < value.base_price) {
        Success(false)
      } else {
        Success(true)
      }
    }


  }

  def checkBalanceAndBuyNumber(
                                addNumberData: CreateOrUpdateCallAccountData,
                                subAccountDetails: SubAccountDetailsFound,
                                priceObject: NumberPriceObject
                              )(using Logger: SRLogger): Either[BuyNumberError, PhoneNumber] = {

    for {


      hasEnoughBalance <- checkBalance(
        addNumberData = addNumberData,
        priceObject = priceObject,
        remaining_balance_cents = subAccountDetails.call_remaining_credit_cents
      ).toEither.left.map(_ => BuyNumberError.PriceObjectNotFound)



      phoneNumber <- if (hasEnoughBalance) {
        getAvailableNumber(
          countryCode = addNumberData.country_code,
          sub_account_sid = subAccountDetails.sub_account_sid,
          sub_account_auth_token = subAccountDetails.sub_auth_token
        ).left.map(err => BuyNumberError.GetNumberError(err))
      } else {
        Left(BuyNumberError.InsufficientBalance)

      }
    }

    yield {
      phoneNumber

    }


  }

  def updateVerificationStatus(
                                data: VerifyCallerIdStatusCallbackData,
                                teamId: TeamId
                              )(using Logger: SRLogger): Try[CallerIdUuid] = {

    callDAO.updateCallerIdVerificationStatus(
      status = data.verificationStatus,
      outgoingCallerIdSid = data.outgoingCallerIdSid,
      phoneNumber = data.phone_number,
      teamId = teamId
    )


  }

  def validatedVerifyCallerIdRequest(
                                      data: ValidateCallerIdData,
                                      teamId: TeamId,
                                      accountId: AccountId,

                                    )(using Logger: SRLogger): Either[VerifyCallerIdError, CallAccountSettings] = {

    getCallSettingOfUser(
      teamId = teamId,
      accountId = accountId
    ) match {

      case Failure(exception) => Left(VerifyCallerIdError.ErrorWhileFetchingCallSettings(error = exception))

      case Success(value) => value match {

        case None => Left(VerifyCallerIdError.NoCallSettingFound)

        case Some(call_account_setting) => Right(call_account_setting)


      }
    }
  }

  def verifyVerificationAttempts(
                                  data: ValidateCallerIdData,
                                  teamId: TeamId
                                ): Either[VerifyCallerIdError, Int] = {

    for {

      //Option[Int]
      attempts <- callDAO.getTotalVerificationAttempts(
        caller_id = data.phone_number,
        teamId = teamId
      ).toEither.left.map(err => VerifyCallerIdError.ErrorFetchingAttempts(err))

      //Int
      res <- attempts match {

        case None => Right(0)

        case Some(value) => if (value < AppConfig.Calling.max_verify_caller_id_attempts) {
          Right(value)
        } else {
          Left(VerifyCallerIdError.VerificationAttemptsExceeded)
        }
      }

    } yield {
      res
    }

  }

  def verifyCallerId(
                      data: ValidateCallerIdData,
                      teamId: TeamId,
                      accountId: AccountId,
                      orgId: OrgId


                    )(using Logger: SRLogger): Either[VerifyCallerIdError, String] = {

    val caller_id_uuid: CallerIdUuid = CallerIdUuid(uuid = srUuidUtils.generateCallerIdUuid)


    for {
      //CallAccountSettings
      _ <- validatedVerifyCallerIdRequest(
        data = data,
        teamId = teamId,
        accountId = accountId
      )

      // Int
      attempts <- verifyVerificationAttempts(
        data = data,
        teamId = teamId
      )

      // SubAccountDetails
      subaccount_details <- getSubAccountDetails(
        sub_account_uuid = None,
        org_id = orgId
      ).toEither.left.map(e =>
        VerifyCallerIdError.GetSubAccountError(e)
      )

      // String
      verification_code <- twilioDialerService.handleVerifyCallerId(
        twl_sub_account_sid = subaccount_details.sub_account_id,
        twl_auth_token = subaccount_details.sub_auth_token,
        phone_number = data.phone_number,
        friendlyName = data.friendly_name,
        teamId = teamId
      ).toEither.left.map(e => VerifyCallerIdError.TwilioServiceError(error = e))

      //CallerIdUuid
      _ <- callDAO.saveCallerIdDetails(
        data = data,
        attempts = attempts,
        teamId = teamId,
        accountId = accountId,
        caller_id_uuid = caller_id_uuid
      ).toEither.left.map(e => VerifyCallerIdError.SQLException(error = e))

    } yield {

      verification_code

    }

  }

  def buyANumber(
                  accountId: AccountId,
                  teamId: TeamId,
                  addNumberData: CreateOrUpdateCallAccountData,
                  org_id: OrgId,
                  org_plan: OrgPlan,
                  phone_uuid: Option[PhoneNumberUuid]
                )(using Logger: SRLogger): Either[BuyNumberError, PhoneNumberValueClass] = {

    planLimitService.checkLimitAccordingToAddonLicenseTypeUsingTeamId(
      teamId = teamId,
      addonLicenceType = AddonLicenceType.CallingSeats,
      isAddingNewTeam = false
    ) match {

      case Failure(AddonLimitReachedException(message, _)) =>
        Left(BuyNumberError.MaximumNumberLimitReached)

      case Failure(e) =>
        Left(BuyNumberError.ErrorWhileFetchingTotalCallAccountsInOrg(e))

      case Success(orgId) =>
        createSubAccountIfNotExist(
          org_id = org_id,
          org_plan = org_plan
        ) match {
          case Left(err) =>

            Left(BuyNumberError.SubAccountError(err_type = err))


          case Right(subAccountDetails) =>
            getPricing(addNumberData.country_code) match {
              case Left(err) => Left(BuyNumberError.ErrorWhileFetchingPriceObject(err))


              case Right(po) =>
                checkBalanceAndBuyNumber(
                  addNumberData = addNumberData,
                  subAccountDetails = subAccountDetails,
                  priceObject = po
                ) match {

                  case Left(error) => Left(error)

                  case Right(phoneNumber: PhoneNumber) =>
                    println(s"guru $phoneNumber")
                    twilioDialerService.buyANumber(
                      number = phoneNumber,
                      sub_account_sid = subAccountDetails.sub_account_sid,
                      sub_auth_token = subAccountDetails.sub_auth_token
                    ) match {

                      case Failure(exception) => Left(BuyNumberError.BuyNumberApiError(err = exception))

                      case Success(incomingPhoneNumber: IncomingPhoneNumber) =>
                        val priceObject = po.prices.find(p => p.phone_type == addNumberData.phone_type.toString)
                        phone_uuid match {

                          case None =>
                            val phone_number_uuid: PhoneNumberUuid = PhoneNumberUuid(phone_number_uuid = srUuidUtils.generatePhoneNumberUuid)
                            addNumberToDBAndResetCache(
                              orgID = org_id,
                              accountId = accountId,
                              teamId = teamId,
                              addNumberData = addNumberData,
                              phoneUUID = phone_number_uuid,
                              callingServiceProvider = CallingServiceProvider.TWILIO,
                              phoneNumber = models.PhoneNumber(
                                phone_number = incomingPhoneNumber.getPhoneNumber.toString
                              ),
                              phoneNumberSID = PhoneSID(
                                phone_sid = incomingPhoneNumber.getSid
                              ),
                              orgSubAccountUUID = subAccountDetails.org_sub_account_uuid,
                              phone_number_cost_cents = priceObject.map(p => p.base_price * 100).getOrElse(0),
                              price_unit = po.currency

                            )
                          case Some(phone_uuid) =>
                            updateNumberToDBAndResetCache(
                              orgID = org_id,
                              teamId = teamId,
                              addNumberData = addNumberData,
                              phoneUUID = phone_uuid,
                              callingServiceProvider = CallingServiceProvider.TWILIO,
                              phoneNumber = models.PhoneNumber(
                                phone_number = incomingPhoneNumber.getPhoneNumber.toString
                              ),
                              phoneNumberSID = PhoneSID(
                                phone_sid = incomingPhoneNumber.getSid
                              ),
                              orgSubAccountUUID = subAccountDetails.org_sub_account_uuid
                            )

                        }

                    }

                }

            }


        }
    }
  }

  def updateCallAccountData(data: CreateOrUpdateCallAccountData, uuid: PhoneNumberUuid, teamId: TeamId)(using logger: SRLogger): Either[UpdateCallSettingsError, Int] = {
    callDAO.updateCallAccountSettings(
      data = data,
      uuid = uuid,
      teamId = teamId
    ) match {
      //FIXME CALL: Add limits check
      case Failure(e) =>
        Left(UpdateCallSettingsError.SQLException(e))

      case Success(1) =>
        releaseChannelLevelLimitLock(
          channelId = ChannelId(id = uuid.phone_number_uuid),
          teamId = teamId.id
        )
        Right(1)

      case Success(_) =>
        Left(UpdateCallSettingsError.AccountNotFoundError(err = "Account not found"))
    }


  }


  def getAvailableCountries(plan_id: PlanID): List[CountryObject] = {

    if (plan_id == PlanID.TRIAL) {

      callUtils.availableCountryLists
        .filter(
          country => List("US", "GB")
            .contains(country.countryISO_code)
        )

    } else {

      callUtils.availableCountryLists

    }


  }

  private def deleteCallNumberFromTwilio(
                                          phone_sid: PhoneSID,
                                          sub_account_sid: TwlSubAccountSid,
                                          sub_account_auth_token: TwlAuthToken
                                        ): Either[DeleteNumberError, true] = {

    twilioDialerService.deleteNumber(
      phoneSID = phone_sid,
      sub_account_sid = sub_account_sid,
      sub_account_auth_token = sub_account_auth_token
    ) match {
      case Failure(exception) => Left(DeleteNumberError.ErrorWhileDeletingFromTwilio(err = exception))

      case Success(value) =>
        if (!value) {
          Left(DeleteNumberError.NumberCouldNotBeDeletedFromTwilio(err = "Number could not be deleted from twilio"))
        }
        else {
          Right(true)
        }
    }

  }

  private def updateCallAccountToInActiveFromDB(
                                                 phone_uuid: PhoneNumberUuid,
                                                 orgId: OrgId,
                                                 team_id: TeamId
                                               )(using logger: SRLogger): Either[DeleteNumberError, Int] = {

    callDAO.updateCallAccountStatusToInActive(uuid = phone_uuid, teamId = team_id) match {

      case Failure(exception) => Left(DeleteNumberError.SQLExceptionWhileDeleting(err = exception))

      case Success(value) =>
        if (value == 1) {
          organizationDAOService.resetOrgDataFromCache(
            orgId = orgId,
            resetCacheInterval = SrResetCacheInterval.Immediately
          ) match {
            case Failure(exception) =>
              Left(DeleteNumberError.ErrorWhileResettingCache(exception))

            case Success(bool) =>
              Right(value)
          }
        }
        else
          Left(DeleteNumberError.AccountNotFoundError(err = "No account found"))
    }
  }

  def updateCallAccountToInActive(uuid: PhoneNumberUuid, teamId: TeamId, org_id: OrgId)(using logger: SRLogger): Either[DeleteNumberError, Int] = {

    val phone_sid = callDAO.getPhoneSIDFromUuid(uuid = uuid, teamId = teamId)

    phone_sid match {
      case Failure(err) =>
        Left(DeleteNumberError.ErrorWhileFetchingPhoneSID(err))

      case Success(None) =>
        updateCallAccountToInActiveFromDB(
          phone_uuid = uuid,
          orgId = org_id,
          team_id = teamId
        )

      case Success(Some(get_phone_sid)) => for {
        get_sub_account_details <- getSubAccountDetails(org_id = org_id, sub_account_uuid = None)
          .toEither.left.map(err => DeleteNumberError.ErrorWhileFetchingAccountFromDB(err))

        // true
        release_number_from_twilio <- deleteCallNumberFromTwilio(
          phone_sid = get_phone_sid,
          sub_account_sid = get_sub_account_details.sub_account_id,
          sub_account_auth_token = get_sub_account_details.sub_auth_token
        )

        // Int
        make_call_account_status_inactive <- updateCallAccountToInActiveFromDB(
          phone_uuid = uuid,
          orgId = org_id,
          team_id = teamId
        )


      } yield {

        make_call_account_status_inactive

      }
    }

  }

  def getCallSettingOfUser(teamId: TeamId, accountId: AccountId): Try[Option[CallAccountSettings]] = {
    callDAO.getCallSettingOfUser(
      teamId = teamId,
      accountId = accountId
    )
  }

  def generateIdentityOfUser(teamId: TeamId, accountId: AccountId): Either[IdentityGenerationError, CallerIdentity] = {
    val call_settings = getCallSettingOfUser(
      teamId = teamId,
      accountId = accountId
    )

    call_settings match {
      case Failure(err) =>

        Left(IdentityGenerationError.SqlExceptionErr(err = err))

      case Success(callSettings) =>

        if (callSettings.isEmpty) {
          Left(IdentityGenerationError.NoSettingFoundError)
        }
        else if (callSettings.get.phone_number.isEmpty) {
          Left(IdentityGenerationError.TwilioNumberNotFoundError)
        }
        else {
          val identity = callSettings.get.uuid.phone_number_uuid // We can create whatever identity we want to create in near future

          Right(CallerIdentity(
            identity = identity
          ))
        }


    }

  }

  def getAccessToken(teamId: TeamId, accountId: AccountId, org_id: OrgId)(using logger: SRLogger): Either[GetAccessTokenError, TwilioIdentityAndToken] = {

    for {
      identity <- generateIdentityOfUser(
        teamId = teamId,
        accountId = accountId
      ).left.map(err => GetAccessTokenError.IdentityCreationError(err))

      get_subAccount_details <- getSubAccountDetails(org_id = org_id, sub_account_uuid = None)
        .toEither.left.map(e =>
          GetAccessTokenError.ErrorWhileFetchingAccount(e)
        )

      create_access_token <- twilioDialerService.createJsonAccessToken(
        api_key = get_subAccount_details.twl_api_key,
        api_secret = get_subAccount_details.twl_secret_key,
        twiml_sid = get_subAccount_details.twl_twiml_app_sid,
        sub_account_sid = get_subAccount_details.sub_account_id,
        caller_identity = identity
      ).toEither.left.map(err => GetAccessTokenError.TwilioServiceError(err))

    } yield {
      create_access_token
    }
  }

  // FIXME CALLS : use value calls
  def getCallSettingDataFromPhoneNumber(phone_number: PhoneNumberValueClass): Either[HandleCallError, CallAccountSettings] = {
    callDAO.getCallSettingDataFromPhoneNumber(
      phone_number = phone_number
    ) match {

      case Failure(err) =>

        Left(HandleCallError.SQLExceptionError(err = err))


      case Success(None) =>

        Left(HandleCallError.NoSettingIdFoundError)


      case Success(Some(call_account_settings)) =>

        Right(call_account_settings)

    }
  }

  def getCallSettingDataFromCallerIdentity(
                                            call_details: CallDetails
                                          ): Either[HandleCallError, CallAccountSettings] = {
    callDAO.getCallSettingDataFromCallerIdentity(
      phone_uuid = PhoneNumberUuid(phone_number_uuid = call_details.from.phone_number)
    ) match {

      case Failure(err) =>

        Left(HandleCallError.SQLExceptionError(err = err))


      case Success(None) =>

        Left(HandleCallError.NoSettingIdFoundError)


      case Success(Some(call_setting)) =>

        Right(call_setting)

    }
  }

  def handleOutgoingBasicCallInitiated(
                                        data: OutgoingBasicCallStatusCallbackData,
                                        conferenceUuid: ConferenceUuid,
                                        teamId: TeamId)(using Logger: SRLogger): Either[OutgoingBasicCallStatusUpdateError, CallSID] = {

    for {
      phone_number <- callDAO.getPhoneSettingUuidFromNumber(
        number = data.call_from,
        teamId = teamId
      ).toEither.left.map(err => OutgoingBasicCallStatusUpdateError.ErrorWhileFetchingPhoneDetails(err = err))

      _ <- callDAO.updateCallSIDForBasicCall(
        conferenceUuid = conferenceUuid,
        callSID = data.call_sid,
        initiating_number = phone_number,
        teamId = teamId).toEither.left.map(err => OutgoingBasicCallStatusUpdateError.ErrorWhileUpdatingCallSID(err = err))

    } yield {


      data.call_sid
    }

  }

  def handleOutgoingBasicCallEndStatuses(
                                          conferenceUuid: ConferenceUuid,
                                          data: OutgoingBasicCallStatusCallbackData,
                                          teamId: TeamId)(using Logger: SRLogger)
  : Either[OutgoingBasicCallStatusUpdateError, CallSID] = {

    for {
      //ConferenceUuid
      _ <- callDAO.updateConferenceStatus(
        newStatus = CallStatus.COMPLETED,
        confSidOrConfUuid = ConfSidOrConfUuid.WithConfUuid(conferenceUuid = conferenceUuid),
        teamId = teamId
      ) match {

        case Failure(exception) =>
          Left(OutgoingBasicCallStatusUpdateError.ErrorWhileCompletingConference(err = exception))

        case Success(value) =>
          Right(value)
      }

      //CallSid
      _ <- handleParticipantCallStatusUpdate(
        callSID = data.call_sid,
        newStatus = data.status,
        conferenceUuid = Some(conferenceUuid),
        teamId = teamId)
        .left.map(err =>
          OutgoingBasicCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(err = err.err)
        )

      notification_details <-callDAO.getActiveParticipantAndConferenceDetails(
        teamId = teamId,
        callParticipantUuid = None,
        conferenceUuid = Some(conferenceUuid),
        call_type = CallType.TWL_BASIC
      ).toEither.left.map(err => OutgoingBasicCallStatusUpdateError.ErrorWhileFetchingNotificationObjectDetails(err = err))


      // OrgId
      org_id <- organisationDAO.getOrgIdFromTeamId(
        teamId = teamId
      ).toEither.left.map(err => OutgoingBasicCallStatusUpdateError.ErrorWhileFetchingOrgId(err = err))


      // String
      pusherMsg <-sendWebNotificationForOngoingCall(
        teamId = teamId,
        orgId = org_id,
        conferenceParticipantDetails = notification_details,
        event_name = SrPushEventNames.participantLeftEventKey
      ).toEither.left.map(err => OutgoingBasicCallStatusUpdateError.ErrorWhileSendingPusherNotification(err = err))


    } yield {
      data.call_sid

    }

  }

  def handleOutgoingBasicCallParticipantCallbackStatus(
                                                        data: OutgoingBasicCallStatusCallbackData,
                                                        teamId: TeamId)
                                                      (using Logger: SRLogger): Either[OutgoingBasicCallStatusUpdateError, CallSID] = {


    callDAO.getConferenceUuidFromParentCallSID(
      callSID = data.parent_call_sid,
      teamId = teamId
    ) match {

      case Failure(exception) =>
        Left(OutgoingBasicCallStatusUpdateError.ErrorWhileFetchingConfUuid(err = exception))

      case Success(conferenceUuid: ConferenceUuid) =>
        data.status match {

          case ParticipantCallStatus.INITIATED =>
            handleOutgoingBasicCallInitiated(
              data = data,
              conferenceUuid = conferenceUuid,
              teamId = teamId
            )

          case ParticipantCallStatus.RINGING | ParticipantCallStatus.IN_PROGRESS | ParticipantCallStatus.QUEUED =>
            handleParticipantCallStatusUpdate(
              callSID = data.call_sid,
              newStatus = data.status,
              conferenceUuid = Some(conferenceUuid),
              teamId = teamId)
              .left.map(err =>
                OutgoingBasicCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(err = err.err)
              )


          case ParticipantCallStatus.CANCELED |
               ParticipantCallStatus.FAILED |
               ParticipantCallStatus.COMPLETED |
               ParticipantCallStatus.NO_ANSWER |
               ParticipantCallStatus.BUSY =>

            handleOutgoingBasicCallEndStatuses(conferenceUuid = conferenceUuid, data = data, teamId = teamId)


        }

    }


  }

  def handleIncomingCallInitiated(data: IncomingCallParticipantStatusCallbackData, conferenceUuid: ConferenceUuid, teamId: TeamId)(using Logger: SRLogger): Either[IncomingCallParticipantStatusUpdateError, CallSID] = {
    callDAO.updateCallSIDForBasicCall(
      conferenceUuid = conferenceUuid,
      callSID = data.call_sid,
      initiating_number = data.call_from,
      teamId = teamId
    ) match {

      case Failure(exception) =>
        Left(IncomingCallParticipantStatusUpdateError.ErrorWhileUpdatingCallSID(err = exception))

      case Success(_: ConferenceUuid) =>
        Right(data.call_sid)
    }
  }


  def handleIncomingCallParticipantCallbackStatus(data: IncomingCallParticipantStatusCallbackData, teamId: TeamId)(using Logger: SRLogger): Either[IncomingCallParticipantStatusUpdateError, CallSID] = {


    callDAO.getConferenceUuidFromParentCallSID(
      callSID = data.parent_call_sid,
      teamId = teamId
    ) match {

      case Failure(exception) =>
        Left(IncomingCallParticipantStatusUpdateError.ErrorWhileFetchingConfUuid(err = exception))

      case Success(conferenceUuid: ConferenceUuid) =>
        data.status match {

          case ParticipantCallStatus.RINGING =>
            handleParticipantCallStatusUpdate(
              callSID = data.call_sid,
              newStatus = data.status,
              conferenceUuid = None,
              teamId = teamId)
              .left.map(err =>
                IncomingCallParticipantStatusUpdateError.ErrorWhileUpdatingParticipantStatus(err = err.err)
              )


          case ParticipantCallStatus.INITIATED =>
            handleIncomingCallInitiated(
              data = data,
              conferenceUuid = conferenceUuid,
              teamId = teamId
            )

          case ParticipantCallStatus.IN_PROGRESS | ParticipantCallStatus.QUEUED =>
            handleParticipantCallStatusUpdate(
              callSID = data.call_sid,
              newStatus = data.status,
              conferenceUuid = Some(conferenceUuid),
              teamId = teamId)
              .left.map(err =>
                IncomingCallParticipantStatusUpdateError.ErrorWhileUpdatingParticipantStatus(err = err.err)
              )


          case ParticipantCallStatus.CANCELED |
               ParticipantCallStatus.FAILED |
               ParticipantCallStatus.COMPLETED |
               ParticipantCallStatus.NO_ANSWER |
               ParticipantCallStatus.BUSY =>

            handleIncomingCallEndStatuses(conferenceUuid = conferenceUuid, data = data, teamId = teamId)


        }

    }


  }

  def triggerVoiceDrop(
                        callSid: CallSID,
                        campaignId: CampaignId,
                        org_id: OrgId,
                        teamId: TeamId,
                        task_id: TaskUuid,
                        voiceDropDetails: VoiceDropDetails
                      )( implicit logger: SRLogger ): Try[CallSID] = {
    for {
      sub_account_details: Option[SubAccountDetails] <- callDAO.getSubAccountDetailsForOrg(
        org_id = org_id,
        sub_account_uuid = None
      )

      customerCallSid: CallSID <- callDAO.getCustomerCallSidFromInitiatorCallSid(
        callSid = callSid,
        teamId = teamId
      )


      playedCallSid: CallSID <- twilioDialerService.playVoiceDrop(customerCallSid = customerCallSid,
        initiatorCallSid = callSid,
        voiceDropUrl = voiceDropDetails.url,
        twl_sub_account_sid = sub_account_details.get.sub_account_id,
        twl_auth_token = sub_account_details.get.sub_auth_token)


      // Only if voice drop succeeds, update the database
      _ : ConferenceUuid <- callDAO.updateVoicemailIdInConferenceLogs(
        voicemail_id = voiceDropDetails.id,
        task_id = task_id,
        team_id = teamId,
        callSid = callSid
      )
      
      _: TaskUuid <- callDAO.updateVoicemailIdInTasks(
        voicemail_id = voiceDropDetails.id,
        task_id = task_id,
        team_id = teamId,
      )

      
    } yield {
      playedCallSid
    }
  }


  def handleIncomingCallEndStatuses(
                                     conferenceUuid: ConferenceUuid,
                                     data: IncomingCallParticipantStatusCallbackData,
                                     teamId: TeamId)(using Logger: SRLogger)
  : Either[IncomingCallParticipantStatusUpdateError, CallSID] = {

    for {
      //ConferenceUuid
      _ <- callDAO.updateConferenceStatus(
        newStatus = CallStatus.COMPLETED,
        confSidOrConfUuid = ConfSidOrConfUuid.WithConfUuid(conferenceUuid = conferenceUuid),
        teamId = teamId
      ) match {

        case Failure(exception) =>
          Left(IncomingCallParticipantStatusUpdateError.ErrorWhileCompletingConference(err = exception))

        case Success(value) =>
          Right(value)
      }

      //CallParticipantUuid
      _ <- handleParticipantCallStatusUpdate(
        callSID = data.call_sid,
        newStatus = data.status,
        conferenceUuid = Some(conferenceUuid),
        teamId = teamId)
        .left.map(err =>
          IncomingCallParticipantStatusUpdateError.ErrorWhileUpdatingParticipantStatus(err = err.err)
        )


    } yield {
      data.call_sid

    }

  }

  def handleParticipantCallbackStatus(data: ParticipantStatusCallbackData, teamId: TeamId)(using Logger: SRLogger): Either[ProspectCallStatusUpdateError, CallSID] = {

    data.status match {

      //FIXME CALL: Add proper implementation for other statuses.

      case ParticipantCallStatus.RINGING =>
        Right(data.call_sid)

      case ParticipantCallStatus.INITIATED =>
        Right(data.call_sid)

      case ParticipantCallStatus.QUEUED =>
        Right(data.call_sid)

      case ParticipantCallStatus.IN_PROGRESS =>

        handleParticipantCallStatusUpdate(
          callSID = data.call_sid,
          newStatus = data.status,
          conferenceUuid = None,
          teamId = teamId)
          .left.map(err => ProspectCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(err = err))

      case ParticipantCallStatus.CANCELED =>
        handleProspectNoAnswerAndBusyAndCancelledAndFailed(data = data, teamId = teamId)

      case ParticipantCallStatus.FAILED =>
        handleProspectNoAnswerAndBusyAndCancelledAndFailed(data = data, teamId = teamId)

      case ParticipantCallStatus.COMPLETED =>
        handleProspectLeave(data = data, teamId = teamId)

      case ParticipantCallStatus.NO_ANSWER =>
        handleProspectNoAnswerAndBusyAndCancelledAndFailed(data = data, teamId = teamId)

      case ParticipantCallStatus.BUSY =>
        handleProspectNoAnswerAndBusyAndCancelledAndFailed(data = data, teamId = teamId)

    }
  }

  def handleParticipantCallStatusUpdate(
                                         callSID: CallSID,
                                         newStatus: ParticipantCallStatus,
                                         conferenceUuid: Option[ConferenceUuid],
                                         teamId: TeamId
                                       )(using Logger: SRLogger)
  : Either[ParticipantStatusUpdateError.ErrorWhileUpdatingParticipantStatusInDB, CallSID] = {

    val callSidOrConfUuid: CallSidOrConfUuid = conferenceUuid match {
      case Some(conf_uuid) => WithConfUuid(conferenceUuid = conf_uuid)
      case None => WithCallSid(call_sid = callSID)
    }

    callDAO.updateParticipantStatus(
      callSidOrConfUuid = callSidOrConfUuid,
      teamId = teamId,
      newStatus = newStatus,

    ) match {

      case Failure(e) =>

        Left(
          ParticipantStatusUpdateError.ErrorWhileUpdatingParticipantStatusInDB(
            err = e
          ))

      case Success(uuid) =>

        Right(callSID)

    }
  }

  def handleProspectLeave(data: ParticipantStatusCallbackData, teamId: TeamId)(using Logger: SRLogger): Either[ProspectCallStatusUpdateError, CallSID] = {
    for {

      //ConferenceSid
      conf_sid <- callDAO.getConferenceSIDFromCallSID(
        callSID = data.call_sid,
        teamId = teamId
      ).toEither.left.map(err => ProspectCallStatusUpdateError.ErrorWhileFetchingConferenceSid(err = err))

      conf_uuid <- handleParticipantStatusChange(
        data = StatusCallbackData(
          conferenceSid = conf_sid,
          statusCallbackEvent = StatusCallbackEvents.PARTICIPANT_LEAVE,
          callSID = Some(data.call_sid),
          participant_label = None
        ),
        newStatus = data.status,
        teamId = teamId
      ) match {

        case Right(conf_uuid) => Right(conf_uuid)
        case Left(error) => Left(ProspectCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(err = error))
      }
    } yield {
      data.call_sid
    }
  }

  def handleProspectNoAnswerAndBusyAndCancelledAndFailed(data: ParticipantStatusCallbackData, teamId: TeamId)(using Logger: SRLogger): Either[ProspectCallStatusUpdateError, CallSID] = {

    for {
      //ConferenceSid
      conf_sid <- callDAO.getConferenceSIDFromCallSID(
        callSID = data.call_sid,
        teamId = teamId
      ).toEither.left.map(err => ProspectCallStatusUpdateError.ErrorWhileFetchingConferenceSid(err = err))

      //ConferenceSid
      _ <- markConferenceAsComplete(
        conferenceSid = conf_sid,
        teamId = teamId
      ).toEither.left.map(err => ProspectCallStatusUpdateError.ErrorWhileCompletingConference(err = err))

      //ConferenceUuid
      conf_uuid <- handleParticipantStatusChange(
        data = StatusCallbackData(
          conferenceSid = conf_sid,
          statusCallbackEvent = StatusCallbackEvents.PARTICIPANT_LEAVE,
          callSID = Some(data.call_sid),
          participant_label = None
        ),
        newStatus = data.status,
        teamId = teamId
      ) match {

        case Right(conf_uuid) => Right(conf_uuid)
        case Left(error) => Left(ProspectCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(err = error))
      }

    } yield {
      data.call_sid

    }

  }

  def handleConferenceParticipantStatusChange(data: StatusCallbackData, teamId: TeamId)(using Logger: SRLogger): Either[ConferenceParticipantStatusUpdateError, ConferenceUuid] = {

    if (data.participant_label.contains("customer")) {
      Left(ConferenceParticipantStatusUpdateError.ProspectStatusCallback)

    } else {

      data.statusCallbackEvent match {
        case StatusCallbackEvents.CONFERENCE_START =>

          callDAO.updateConferenceStatus(
            confSidOrConfUuid = WithConfSid(conf_sid = data.conferenceSid),
            newStatus = CallStatus.ACTIVE,
            teamId = teamId
          ) match {
            case Success(value) => Right(value)
            case Failure(exception) => Left(ConferenceParticipantStatusUpdateError.ErrorWhileUpdatingConferenceStatusInDB(err = exception))
          }

        case StatusCallbackEvents.CONFERENCE_END =>

          callDAO.updateConferenceStatus(
            confSidOrConfUuid = WithConfSid(conf_sid = data.conferenceSid),
            newStatus = CallStatus.COMPLETED,
            teamId = teamId
          ) match {
            case Success(value) => Right(value)
            case Failure(exception) => Left(ConferenceParticipantStatusUpdateError.ErrorWhileUpdatingConferenceStatusInDB(err = exception))
          }


        case StatusCallbackEvents.PARTICIPANT_JOIN =>

          handleParticipantStatusChange(
            data = data,
            newStatus = ParticipantCallStatus.IN_PROGRESS,
            teamId = teamId
          ) match {

            case Right(value) => Right(value)

            case Left(error) => Left(ConferenceParticipantStatusUpdateError.ErrorWhileUpdatingParticipantStatus(err = error))
          }

        case StatusCallbackEvents.PARTICIPANT_LEAVE =>

          handleParticipantStatusChange(
            data = data,
            newStatus = ParticipantCallStatus.COMPLETED,
            teamId = teamId
          ) match {

            case Right(value) => Right(value)

            case Left(error) => Left(ConferenceParticipantStatusUpdateError.ErrorWhileUpdatingParticipantStatus(err = error))
          }
      }

    }

  }


  def checkConferenceParticipants(
                                   conferenceSid: ConferenceSid,
                                   teamId: TeamId
                                 ): Try[Boolean] = {

    callDAO.getRemainingParticipantDetails(conferenceSid = conferenceSid, teamId = teamId)
      .map(data => {
        val should_end_conference: Boolean = !data.exists(p =>
          (p.latest_participation_mode == CallParticipationMode.InitiatorMode && p.participant_label == "initiator") //initiator present
            || p.latest_participation_mode == CallParticipationMode.BargeInMode) //a person in barge-in mode present
        if (data.nonEmpty && (data.length < 2 || should_end_conference)) {

          true
        }
        else {

          false
        }

      })
  }

  def markConferenceAsComplete(
                                conferenceSid: ConferenceSid,
                                teamId: TeamId
                              )(using logger: SRLogger): Try[ConferenceSid] = {

    for {
      org_id: OrgId <- organisationDAO.getOrgIdFromTeamId(
        teamId = teamId
      )

      sub_account_details_of_org: Option[SubAccountDetails] <- callDAO.getSubAccountDetailsForOrg(
        org_id = org_id,
        sub_account_uuid = None
      )

      conf_uuid: ConferenceUuid <- callDAO.updateConferenceStatus(
        newStatus = CallStatus.COMPLETED,
        confSidOrConfUuid = WithConfSid(conf_sid = conferenceSid),
        teamId = teamId
      )

      conf_sid: ConferenceSid <- twilioDialerService.completeConference(
        conferenceSid = conferenceSid,
        subAccountDetails = sub_account_details_of_org.get) match {

        // Silently failing this path so that we can update the status of participants in db to complete or other
        // as it can raise some other bugs in code.
        case Failure(e) =>

          logger.fatal("[markConferenceAsComplete] Error while completing a conference", e)
          Success(conferenceSid)

        case Success(conf_sid) =>
          Success(conferenceSid)

      }

    } yield {
      conf_sid

    }

  }

  def checkParticipantsAndCompleteConference(
                                              conferenceSid: ConferenceSid,
                                              teamId: TeamId
                                            )(using logger: SRLogger): Try[ConferenceSid] = {
    for {
      should_end_conference: Boolean <- checkConferenceParticipants(
        conferenceSid = conferenceSid,
        teamId = teamId
      )


      conf_sid: ConferenceSid <- if (should_end_conference) {
        markConferenceAsComplete(
          conferenceSid = conferenceSid,
          teamId = teamId
        )
      }
      else {
        Success(conferenceSid)
      }
    } yield {
      conf_sid
    }
  }

  def handleParticipantStatusChange(data: StatusCallbackData, newStatus: ParticipantCallStatus, teamId: TeamId)(using Logger: SRLogger): Either[ParticipantStatusUpdateError, ConferenceUuid] = {

    //FIXME CALL: CHECK THIS
    val event_name: SrPushEventNamespace = newStatus match {
      case ParticipantCallStatus.RINGING => SrPushEventNames.conferenceStartedEventKey
      case ParticipantCallStatus.QUEUED => SrPushEventNames.conferenceStartedEventKey
      case ParticipantCallStatus.COMPLETED => SrPushEventNames.participantLeftEventKey
      case ParticipantCallStatus.NO_ANSWER => SrPushEventNames.participantLeftEventKey
      case ParticipantCallStatus.BUSY => SrPushEventNames.participantLeftEventKey
      case ParticipantCallStatus.INITIATED => SrPushEventNames.conferenceStartedEventKey
      case ParticipantCallStatus.IN_PROGRESS => SrPushEventNames.conferenceStartedEventKey
      case ParticipantCallStatus.CANCELED => SrPushEventNames.participantLeftEventKey
      case ParticipantCallStatus.FAILED => SrPushEventNames.participantLeftEventKey
    }
    data.callSID match {

      case None => Left(ParticipantStatusUpdateError.NoCallSIDFoundForParticipantStatusUpdate)

      case Some(call_sid: CallSID) =>

        for {

          // CallParticipantUuid
          participant_uuid <- callDAO.updateParticipantStatus(
            callSidOrConfUuid = WithCallSid(call_sid = call_sid),
            newStatus = newStatus,
            teamId = teamId
          ).toEither.left.map(err => ParticipantStatusUpdateError.ErrorWhileUpdatingParticipantStatusInDB(err = err))

          //ConferenceSid
          _ <- if (newStatus == ParticipantCallStatus.COMPLETED) {
            checkParticipantsAndCompleteConference(
              conferenceSid = data.conferenceSid,
              teamId = teamId
            ).toEither.left.map(err => ParticipantStatusUpdateError.ErrorWhileCheckingParticipantsAndCompletingConference(err = err))

          } else {

            Right(data.conferenceSid)

          }

          // Seq[OngoingCallParticipantNotificationObject]
          notification_details <- callDAO.getActiveParticipantAndConferenceDetails(
            teamId = teamId,
            callParticipantUuid = Some(participant_uuid),
            conferenceUuid = None
          ).toEither.left.map(err => ParticipantStatusUpdateError.ErrorWhileFetchingNotificationObjectDetails(err = err))


          // OrgId
          org_id <- organisationDAO.getOrgIdFromTeamId(
            teamId = teamId
          ).toEither.left.map(err => ParticipantStatusUpdateError.ErrorWhileFetchingOrgId(err = err))


          // String
          pusherMsg <- sendWebNotificationForOngoingCall(
            teamId = teamId,
            orgId = org_id,
            conferenceParticipantDetails = notification_details,
            event_name = event_name //FIXME CALL: CHECK THIS
          ).toEither.left.map(err => ParticipantStatusUpdateError.ErrorWhileSendingPusherNotification(err = err))


        } yield {

          notification_details.head.conference_uuid

        }
    }
  }

  def getConferenceNameForListenMode(
                                      callDetails: CallDetails,
                                      teamId: TeamId
                                    )(using logger: SRLogger): Try[Option[ConferenceFriendlyName]] = {

    callDetails.call_participant_data match {

      case InitialCallParticipationModeData.InitiatorModeData => Success(None)

      case data: InitialCallParticipationModeData.ListenModeData => callDAO.getConferenceNameFromConferenceUuid(
        conferenceUuid = data.conference_uuid,
        teamId = teamId
      )
    }

  }

  def getCallerIdNumberFromCallerIdUuid(
                                         caller_id_uuid: CallerIdUuid,
                                         team_id: TeamId
                                       ): Try[PhoneNumberValueClass] = {
    callDAO.getCallerIdFromCallerIdUuid(caller_id_uuid = caller_id_uuid, teamId = team_id)
  }

  def handleOutGoingCalls(
                           call_details: CallDetails,
                           validate_webhook_data: WebhookValidateData
                         )(using logger: SRLogger): Either[HandleCallError, String] = {

    // outgoing calls

    // add a check here if the service_provider is twilio

    for {

      //Type CallAccountSettings
      get_call_setting_data <- getCallSettingDataFromCallerIdentity(
        call_details = call_details
      )

      phone_number <- {
        get_call_setting_data.phone_number match {
          case Some(number) => get_call_setting_data.caller_id match {

            case Some(caller_id) => getCallerIdNumberFromCallerIdUuid(
              caller_id_uuid = caller_id,
              team_id = get_call_setting_data.team_id

            ) match {

              case Failure(exception) =>
                Left(HandleCallError.ErrorWhileFetchingCallerIdDetails(err = exception))

              case Success(num) => Right(num)
            }

            case None => Right(number)
          }

          case None => Left(HandleCallError.TwilioNumberNotFoundError(new Exception("cant find twilio number for this account")))
        }
      }

      org_sub_account_uuid <- {
        get_call_setting_data.org_sub_account_uuid match {
          case Some(org_sub_account_uuid) => Right(org_sub_account_uuid)
          case None => Left(HandleCallError.TwilioSubAccountNotFoundError(new Exception("cant find twilio number for this account")))
        }
      }

      // Type: SubAccountDetails
      get_sub_account_details_for_org <- getSubACcountDetailsForOrgUsingSubAccountUuid(
        subAccountUuid = org_sub_account_uuid
      ).toEither.left.map(err => HandleCallError.ErrorWhileFetchingSubAccounDetails(err))


      // Type: Boolean: true
      validate_webhook_request <- validateWebhookRequest(
        twilio_signature = validate_webhook_data.twilio_signature,
        webhook_url = validate_webhook_data.webhook_url,
        params = validate_webhook_data.params,
        sub_account_details = get_sub_account_details_for_org
      ).left.map(err => HandleCallError.WebhookValidationError(err))

      //Type: Option[ConferenceFriendlyName]
      conference_friendly_name <- getConferenceNameForListenMode(
        callDetails = call_details,
        teamId = get_call_setting_data.team_id
      ).toEither.left.map(err => HandleCallError.ErrorWhileFetchingConferenceNameForListenMode(err = err))

      //Type TwilioConferenceAndParticipantDetails
      handle_out_going_calls <- handleOutgoingCallOrConf(
        call_details = call_details,
        conference_friendly_name = conference_friendly_name,
        call_setting_data = get_call_setting_data,
        sub_account_details_for_org = get_sub_account_details_for_org,
        phone_number = phone_number.phone_number
      ).toEither.left.map(err => HandleCallError.TwilioServiceError(err))

      taskUuidAndProspectID <- getTaskUuidAndProspectId(
        params = validate_webhook_data.params,
        call_account_settings = get_call_setting_data).toEither.left.map(err => HandleCallError.TwilioServiceError(err))

      save_done_by <- (taskUuidAndProspectID match {
        
        case Some(tData) => 
          if(call_details.call_participant_data.callParticipantMode == CallParticipationMode.InitiatorMode) {
            callDAO.updateDoneByForCallTask(
              done_by = get_call_setting_data.owner_account_id,
              taskUuid = TaskUuid(uuid = tData.task_uuid),
              teamId = get_call_setting_data.team_id
            )
          }else {
            Success(0)
          }
        
        case None => Success(0)
      }).toEither.left.map(err => HandleCallError.SQLExceptionError(err))
        
      

      // OngoingCallParticipantNotificationObject
      participant_details_for_pusher <- saveCallDetails(
        call_details = call_details,
        org_sub_account_uuid = get_sub_account_details_for_org.uuid,
        call_account_settings = get_call_setting_data,
        conferenceAndParticipantDetails = handle_out_going_calls,
        taskUuidAndProspectID = taskUuidAndProspectID,
        is_incoming = false
      ).toEither.left.map(err => HandleCallError.ErrorWhileSavingCallingDetails(err))

      // Type: List[Long]
      adding_prospect_event <- {

        call_details.call_participant_data match {

          case InitialCallParticipationModeData.InitiatorModeData =>

            addingProspectEvent(
              taskUuidAndProspectID = taskUuidAndProspectID,
              get_call_account_settings = get_call_setting_data,
              event_type = EventType.CALL_PLACED,
              participant_details_for_pusher.head.conference_uuid
            )

          case _: InitialCallParticipationModeData.ListenModeData =>

            Right(List())

        }
      }

      //String
      pusherMsg <- sendWebNotificationForOngoingCall(
        teamId = get_call_setting_data.team_id,
        orgId = get_sub_account_details_for_org.org_id,
        conferenceParticipantDetails = participant_details_for_pusher,
        event_name = SrPushEventNames.conferenceStartedEventKey
      ).toEither.left.map(err => HandleCallError.ErrorWhileSendingNotificationViaPusher(err = err))

    } yield {
      /*
        <?xml version="1.0" encoding="UTF-8"?>
        <Response>
            <Dial>
                <Conference muted="true" record="record-from-start" recordingStatusCallback="https://sendwebhookhere.com/" startConferenceOnEnter="true">conf_listen_testing</Conference>
            </Dial>
        </Response>

       */
      handle_out_going_calls.response
    }

  }

  def handleOutgoingCallOrConf(
                                call_details: CallDetails,
                                call_setting_data: CallAccountSettings,
                                phone_number: String,
                                sub_account_details_for_org: SubAccountDetails,
                                conference_friendly_name: Option[ConferenceFriendlyName]
                              )(using logger: SRLogger): Try[TwilioConferenceAndParticipantDetails] = {
    call_details.call_type match {

      case CallType.TWL_BASIC => twilioDialerService.handleOutgoingBasicCall(
        call_details = call_details,
        call_from_number = phone_number,
        twl_sub_account_sid = sub_account_details_for_org.sub_account_id,
        twl_auth_token = sub_account_details_for_org.sub_auth_token,
        record_call = call_setting_data.record_call,
        tid = call_setting_data.team_id
      )

      case CallType.TWL_CONF => twilioDialerService.handleOutGoingCall(
        call_details = call_details,
        call_from_number = phone_number,
        twl_sub_account_sid = sub_account_details_for_org.sub_account_id,
        twl_auth_token = sub_account_details_for_org.sub_auth_token,
        record_call = call_setting_data.record_call,
        tid = call_setting_data.team_id,
        conference_friendly_name = conference_friendly_name
      )

      case CallType.NATIVE_OUTGOING => Failure(new Exception("this should never happen"))
    }

  }

  private def addingProspectEvent(
                                   taskUuidAndProspectID: Option[TaskUuidAndProspectID],
                                   get_call_account_settings: CallAccountSettings,
                                   event_type: EventType,
                                   conference_uuid: ConferenceUuid
                                 ): Either[HandleCallError.SQLExceptionError, List[Long]] = {

    if (taskUuidAndProspectID.isDefined) {
      prospectAddEventDAO.addEvents(List(CreateProspectEventDB(
        event_type = event_type,
        doer_account_id = Some(get_call_account_settings.owner_account_id.id),
        doer_account_name = Some(get_call_account_settings.first_name + " " + get_call_account_settings.last_name),
        assigned_to_account_id = Some(get_call_account_settings.owner_account_id.id),
        assigned_to_account_name = Some(get_call_account_settings.first_name + " " + get_call_account_settings.last_name),
        old_category = None,
        new_category = None,
        prospect_id = taskUuidAndProspectID.get.prospect_id.id,
        email_thread_id = None,
        campaign_id = None,
        campaign_name = None,
        step_id = None,
        step_name = None,
        clicked_url = None,
        account_id = get_call_account_settings.owner_account_id.id,
        team_id = get_call_account_settings.team_id.id,
        email_scheduled_id = None,
        created_at = DateTime.now(),
        task_type = Some(TaskType.CallTask),
        channel_type = Some(ChannelType.CallChannel),
        task_uuid = Some(taskUuidAndProspectID.get.task_uuid),
        call_conference_uuid = Some(conference_uuid),

        duplicates_merged_at = None,
        total_merged_prospects = None,
        potential_duplicate_prospect_id = None
      ))) match {
        case Success(value) => Right(value)
        case Failure(exception) => Left(HandleCallError.SQLExceptionError(exception))
      }
    } else {
      Right(List())
    }
  }

  def notifyUserForIncomingCall(
                                 call_account_setting: CallAccountSettings,
                                 call_details: CallDetails,
                                 sub_account_details: SubAccountDetails,
                                 prospect_id: Option[ProspectId]
                               )(implicit ws: WSClient, ec: ExecutionContext, logger: SRLogger): Try[Boolean] = {

    val res = if (!call_account_setting.enable_forwarding) {

      for {

        get_account_id_from_org_id: AccountId <- accountDAO.getOwnerAccountIdByOrgId(
          orgId = sub_account_details.org_id) match {

          case Failure(err) =>
            Failure(err)

          case Success(None) =>

            Failure(new Exception("Found None while fetching owner account id"))

          case Success(Some(id)) =>

            Success(id)

        }

        org_id: OrgId <- organisationDAO.getOrgIdFromTeamId(
          teamId = call_account_setting.team_id
        )

        prospect_details: Option[Try[Seq[ProspectObject]]] = prospect_id.map(id => prospectDAOService.findProspects(
          byProspectIds = Seq(id.id),
          teamId = call_account_setting.team_id.id,
          org_id = org_id
        ))


        //  Unit
        send_notification <- emailNotificationService.sendIncomingCallReceivedMail(
          to_account_id = get_account_id_from_org_id,
          cc_account_id = call_account_setting.owner_account_id,
          prospect = prospect_details,
          call_to = call_details.to,
          call_from = call_details.from
        )

      } yield {

        true


      }

    } else {

      Success(false)

    }

    res match {

      case Failure(e) =>

        logger.error("Error while sending notification", e)

        Success(false)


      case Success(b) =>

        Success(true)


    }


  }

  def handleIncomingCall(
                          call_details: CallDetails,
                          validate_data: WebhookValidateData
                        )(using logger: SRLogger, ws: WSClient, ec: ExecutionContext): Either[HandleCallError, String] = {


    for {
      // Type: CallAccountSettings
      get_call_account_settings <- getCallSettingDataFromPhoneNumber(
        phone_number = call_details.to
      )


      org_sub_account_uuid <- {
        get_call_account_settings.org_sub_account_uuid match {
          case Some(org_sub_account_uuid) => Right(org_sub_account_uuid)
          case None => Left(HandleCallError.TwilioSubAccountNotFoundError(new Exception("cant find twilio number for this account")))
        }
      }

      // Type: SubAccountDetails
      get_sub_account_details_for_org <- getSubACcountDetailsForOrgUsingSubAccountUuid(
        subAccountUuid = org_sub_account_uuid
      ).toEither.left.map(err => HandleCallError.ErrorWhileFetchingSubAccounDetails(err))

      // boolean : true
      validate_webhook_request <- validateWebhookRequest(
        twilio_signature = validate_data.twilio_signature,
        webhook_url = validate_data.webhook_url,
        params = validate_data.params,
        sub_account_details = get_sub_account_details_for_org
      ).left.map(err => HandleCallError.WebhookValidationError(err))

      /* 2 Jan we are sending emails to user and person who recieved the incoming call
        saying please setup your call forward number
      */
      if_forwarding_not_enabled_notify <- notifyUserForIncomingCall(
        call_account_setting = get_call_account_settings,
        call_details = call_details,
        sub_account_details = get_sub_account_details_for_org,
        prospect_id = None // Fixme check for prospect and pass it here
      ).toEither.left.map(err => HandleCallError.ErrorWhileSendingNotificationForIncomingCall(err))

      //type: SrPhoneNumber
      srPhoneNumber <-
        PhoneNumberValidator.parseSrPhoneNumber(phoneNumber = call_details.from.phone_number)
        match {

          case Failure(exception) =>
            logger.shouldNeverHappen("error while parsing phone number", err = Some(exception))

            Right(
              SrPhoneNumber(
                phone_number = call_details.from,
                country_code = None
              )
            )

          case Success(value) => Right(value)
        }


      // Type: ProspectDetailsForIncomingCalls
      prospect_details <- prospectDAOService.getProspectDetailsForIncomingCallFromNumber(
        phone_number = srPhoneNumber.phone_number,
        teamId = get_call_account_settings.team_id
      ).toEither.left.map(err => HandleCallError.ErrorWhileFetchingProspectDetailsForIncomingCall(err))



      // Type: TwilioConferenceAndParticipantDetails
      handle_incoming_call <- twilioDialerService.handleIncomingCall(
        is_forward_enabled = get_call_account_settings.enable_forwarding, // FIMXE CALL
        forward_to = get_call_account_settings.forward_to,
        caller_identity = CallerIdentity(
          identity = get_call_account_settings.uuid.phone_number_uuid
        ),
        call_details = call_details,
        twl_sub_account_sid = get_sub_account_details_for_org.sub_account_id,
        twl_auth_token = get_sub_account_details_for_org.sub_auth_token,
        record_call = get_call_account_settings.record_call,
        tid = get_call_account_settings.team_id,
        orgId = get_sub_account_details_for_org.org_id
      ).toEither.left.map(err => HandleCallError.TwilioServiceError(err))

      // Type: Seq[OngoingCallParticipantNotificationObject]
      participant_details <- (
        if (RollingUpdates.orgIdsForIncomingCalls.contains(get_sub_account_details_for_org.org_id.id)) {
          saveCallDetails(
            call_details = call_details,
            org_sub_account_uuid = get_sub_account_details_for_org.uuid,
            call_account_settings = get_call_account_settings,
            conferenceAndParticipantDetails = handle_incoming_call,
            taskUuidAndProspectID = None,
            is_incoming = true,
            prospect_details_for_incoming_call = Some(prospect_details)
          )
        } else {
          Success(
            Seq()
          )
        }
        ).toEither.left.map(err => HandleCallError.ErrorWhileSavingCallingDetails(err))

      /*
      30 DEC 2023:  Found we were using task_id to get prospect_id but for incoming calls

        taskUuidAndProspectID <- Either.fromTry(getTaskUuidAndProspectId(
          params = validate_data.params,
          call_account_settings = get_call_account_settings)).left.map(err => HandleCallError.TwilioServiceError(err))
      */
      /*
     Note 21 NOV 2023: Commented out this code as we are not creating  conference for incoming calls, we are directly
                   forwarding the calls to provided number.

                   we need to figure out how to store incoming call details. as conference_uuid is necessary in
                   call_participants_table.

                   and if we create conference that will cost more money of user.


      Type: Seq[OngoingCallParticipantNotificationObject]
       save_incoming_call_details <- Either.fromTry(saveCallDetails(
         call_details = call_details,
         org_sub_account_uuid = get_sub_account_details_for_org.uuid,
         call_account_settings = get_call_account_settings,
         conferenceAndParticipantDetails = handle_incoming_call,
         taskUuidAndProspectID = taskUuidAndProspectID,
         is_incoming = true
       )).left.map(err => HandleCallError.ErrorWhileSavingCallingDetails(err))

        Type: List[Long]
       adding_prospect_event <- {

         call_details.call_participant_data match {

           case InitialCallParticipationModeData.InitiatorModeData =>

            addingProspectEvent(
              taskUuidAndProspectID = taskUuidAndProspectID,
              get_call_account_settings = get_call_account_settings,
              event_type = EventType.CALL_RECEIVED,
              conference_uuid = save_incoming_call_details.head.conference_uuid  // Fixme Recheck this why  save call details is a seq ?
            )

           case _: InitialCallParticipationModeData.ListenModeData =>

             Right(List())

         }

       }*/

    } yield {
      handle_incoming_call.response
    }

  }

  def getTaskUuidAndProspectId(
                                params: Map[String, String],
                                call_account_settings: CallAccountSettings

                              )(using logger: SRLogger): Try[Option[TaskUuidAndProspectID]] = {

    params.get("task_id") match {

      case None => Success(None)

      case Some(task_uuid) => callDAO.getPrimaryProspectIdFromTask(
        task_uuid = task_uuid,
        teamId = call_account_settings.team_id
      ) match {
        case Success(value) => Success(Some(value))

        case Failure(exception) =>
          logger.error(s"Failure while fetching task uuid and prospect id :: task_uuid :: $task_uuid  team_id:: ${call_account_settings.team_id}", err = exception)
          Failure(new Exception(s"Failure while fetching task uuid and prospect id :: task_uuid :: $task_uuid  team_id:: ${call_account_settings.team_id}"))
      }
    }
  }


  def saveConferenceDetails(
                             call_details: CallDetails,
                             org_sub_account_uuid: SubAccountUuid,
                             call_account_settings: CallAccountSettings,
                             conference_details: Option[ConferenceDetailsFromWebhook],
                             taskUuidAndProspectID: Option[TaskUuidAndProspectID],
                             is_incoming: Boolean,
                             prospect_details_for_incoming_call: Option[ProspectDetailsForIncomingCalls]
                           )(using logger: SRLogger): Try[ConferenceUuid] = {

    conference_details match {

      case Some(conf_details) =>

        val conference_uuid = ConferenceUuid(conf_uuid = srUuidUtils.generateConferenceUuid())

        callDAO.saveConferenceDetails(
          conference_uuid = conference_uuid,
          conference_details = conf_details,
          taskUuidAndProspectID = taskUuidAndProspectID,
          is_incoming = is_incoming,
          team_id = call_account_settings.team_id,
          prospect_details_for_incoming_call = prospect_details_for_incoming_call
        ).map(data => data.conference_uuid)


      case None =>
        call_details.call_participant_data match {

          case data: InitialCallParticipationModeData.ListenModeData =>

            Success(data.conference_uuid)

          case InitialCallParticipationModeData.InitiatorModeData =>
            logger.error("initiator mode should have conference object")
            Failure(new Exception("initiator mode should have conference object"))
        }

    }
  }


  def updateCompletedCallConfStatus(
                                     data: OngoingCallParticipantNotificationObject,
                                     status: ParticipantCallStatus,
                                     callSID: CallSID,
                                     completed_at: Option[DateTime],
                                     teamId: TeamId

                                   ): Try[ConferenceUuid] = {
    for {

      conf_uuid: ConferenceUuid <- if (data.latest_participation_mode == CallParticipationMode.InitiatorMode) {
        callDAO.updateConferenceStatus(
          newStatus = CallStatus.COMPLETED,
          confSidOrConfUuid = ConfSidOrConfUuid.WithConfUuid(conferenceUuid = data.conference_uuid),
          completed_at = completed_at,
          teamId = teamId
        )
      } else {
        Success(data.conference_uuid)
      }

      call_participant_uuid: CallParticipantUuid <- if (data.latest_participation_mode == CallParticipationMode.InitiatorMode) {
        callDAO.updateParticipantStatus(
          callSidOrConfUuid = CallSidOrConfUuid.WithConfUuid(conferenceUuid = data.conference_uuid),
          newStatus = status,
          completed_at = completed_at,
          teamId = teamId
        )
      } else {
        callDAO.updateParticipantStatus(
          callSidOrConfUuid = CallSidOrConfUuid.WithCallSid(call_sid = callSID),
          newStatus = status,
          teamId = teamId
        )
      }

    } yield {

      conf_uuid

    }

  }

  def getCallStatusAndUpdate(data: OngoingCallParticipantNotificationObject,
                             sub_account_details_of_org: SubAccountDetails,
                             teamId: TeamId
                            ): Try[ConferenceUuid] = {

    for {

      status_details: CallStatusChangeData <- twilioDialerService.getCallStatus(
        call_sid = data.call_sid.get,
        twl_sub_account_sid = sub_account_details_of_org.sub_account_id,
        twl_auth_token = sub_account_details_of_org.sub_auth_token,
        conf_uuid = data.conference_uuid
      )

      updated_conf <- status_details.status match {
        case ParticipantCallStatus.RINGING |
             ParticipantCallStatus.INITIATED |
             ParticipantCallStatus.QUEUED |
             ParticipantCallStatus.IN_PROGRESS => Success(data.conference_uuid)

        case ParticipantCallStatus.COMPLETED |
             ParticipantCallStatus.NO_ANSWER |
             ParticipantCallStatus.BUSY |
             ParticipantCallStatus.CANCELED |
             ParticipantCallStatus.FAILED =>
          updateCompletedCallConfStatus(
            data = data,
            status = status_details.status,
            completed_at = status_details.ended_at,
            callSID = data.call_sid.get,
            teamId = teamId
          )
      }


    } yield {
      data.conference_uuid

    }

  }

  def getAmountDeductedForNumbers(
                                   orgId: OrgId,
                                   teamId: TeamId

                                 ): Try[Seq[PhoneNumberCreditsRecords]] = {
    for {

      sub_account_details_of_org_opt: Option[SubAccountDetails] <- callDAO.getSubAccountDetailsForOrg(
        org_id = orgId,
        sub_account_uuid = None
      )

      sub_account_details_for_org <- sub_account_details_of_org_opt match {

        case None => Failure(new Exception(CONSTANTS.API_MSGS.ERR_NO_SUBACCOUNTS_FOUND))

        case Some(value) => Success(value)
      }

      res <- twilioDialerService.getCreditsSpentOnPhoneNumbers(
        twl_sub_account_sid = sub_account_details_for_org.sub_account_id,
        twl_auth_token = sub_account_details_for_org.sub_auth_token
      )
    }
    yield {
      res

    }

  }

  def getActiveParticipantAndConferenceDetails(
                                                orgId: OrgId,
                                                teamId: TeamId
                                              ): Try[Seq[OngoingCallParticipantNotificationObject]] = {

    for {

      sub_account_details_of_org_opt: Option[SubAccountDetails] <- callDAO.getSubAccountDetailsForOrg(
        org_id = orgId,
        sub_account_uuid = None
      )

      sub_account_details_for_org <- sub_account_details_of_org_opt match {

        case None => Failure(new Exception(CONSTANTS.API_MSGS.ERR_NO_SUBACCOUNTS_FOUND))

        case Some(value) => Success(value)
      }

      active_participant_details <- callDAO.getActiveParticipantAndConferenceDetails(
        teamId = teamId,
        callParticipantUuid = None,
        conferenceUuid = None
      )

      res <- Helpers.seqTryToTrySeq(active_participant_details.map(
        participant_detail =>
          getCallStatusAndUpdate(
            data = participant_detail,
            sub_account_details_of_org = sub_account_details_for_org,
            teamId = teamId
          )

      ))

      final_results <- callDAO.getActiveParticipantAndConferenceDetails(
        teamId = teamId,
        callParticipantUuid = None,
        conferenceUuid = None
      )

    } yield {
      final_results
    }

  }

  def getActiveParticipantDetailsForConference(
                                                teamId: TeamId,
                                                conferenceUuid: ConferenceUuid
                                              ): Try[Seq[OngoingCallParticipantNotificationObject]] = {
    callDAO.getActiveParticipantAndConferenceDetails(
      teamId = teamId,
      callParticipantUuid = None,
      conferenceUuid = Some(conferenceUuid)
    )

  }

  def removeCallerId(
                      orgId: OrgId,
                      callerIdUuid: CallerIdUuid,
                      teamId: TeamId
                    )(using logger: SRLogger): Either[RemoveCallerIdError, CallerIdUuid] = {
    for {

      // Option[SubAccountDetails]
      sub_account_details_of_org_opt <- callDAO.getSubAccountDetailsForOrg(
        org_id = orgId,
        sub_account_uuid = None
      ).toEither.left.map(err => RemoveCallerIdError.ErrorWhileFetchingCallSettingsDetails(err = err))

      // SubAccountDetails
      sub_account_details_for_org <- sub_account_details_of_org_opt match {

        case None => Left(RemoveCallerIdError.NoCallSettingsFoundForOrg)

        case Some(value) => Right(value)
      }

      // Boolean
      caller_id_association <- callDAO.checkCallerIdAssociation(
        caller_id_uuid = callerIdUuid,
        teamId = teamId
      ).toEither.left.map(err => RemoveCallerIdError.ErrorWhileFetchingCallerIdAssociation(err))

      // Boolean
      check <- if (caller_id_association) {
        Left(RemoveCallerIdError.CallerIdAssociatedWithCallSetting)
      } else {
        Right(false)
      }

      //CallerIdDetails
      callerIdDetails <- callDAO.getCallerIdDetailsFromCallerIdUuid(
        caller_id_uuid = callerIdUuid,
        teamId = teamId
      ).toEither.left.map(err => RemoveCallerIdError.ErrorWhileFetchingCallerIdDetails(err = err))

      // Boolean
      _ <- twilioDialerService.removeCallerId(
        twl_sub_account_sid = sub_account_details_for_org.sub_account_id,
        twl_auth_token = sub_account_details_for_org.sub_auth_token,
        teamId = teamId,
        outgoingCallerIdSid = callerIdDetails.outgoingCallerIdSid
      ).toEither.left.map(err => RemoveCallerIdError.TwilioServiceError(err = err))

      // CallerIdUuid
      res <- callDAO.updateCallerIdVerificationStatus(
        status = CallerIdVerificationStatus.DELETED,
        outgoingCallerIdSid = Some(callerIdDetails.outgoingCallerIdSid),
        phoneNumber = callerIdDetails.phoneNumber,
        teamId = teamId
      ).toEither.left.map(err => RemoveCallerIdError.ErrorWhileUpdatingStatus(err))


    } yield {
      res

    }
  }

  def saveParticipantDetails(
                              call_participant_mode: InitialCallParticipationModeData,
                              conference_uuid: ConferenceUuid,
                              participantDetails: Seq[ParticipantDetailsFromWebhook],
                              teamId: TeamId,
                              accountId: AccountId,
                              call_setting_uuid: Option[String],
                              calling_device: Option[CallingDevice],
                              taskUuidAndProspectID: Option[TaskUuidAndProspectID],
                            )(using logger: SRLogger): Try[Seq[ParticipantDetailsToNotify]] = {

    val result: Seq[Try[ParticipantDetailsToNotify]] = participantDetails.map(

      participant_details => {

        if (participant_details.is_prospect && taskUuidAndProspectID.isDefined) {
          prospectDAO.saveLastContactedAtPhone(
            phone_number = participant_details.call_participant_phone,
            prospect_id = taskUuidAndProspectID.get.prospect_id,
            teamId = teamId
          ) match {
            case Success(value) => //do nothing
            case Failure(e) =>
              logger.shouldNeverHappen(s"Error while saving last contacted phone:: ${LogHelpers.getStackTraceAsString(e)}")
          }
        }

        val call_participant_uuid = CallParticipantUuid(participant_uuid = srUuidUtils.generateParticipantUuid())

        logger.info(s"participant_details :: ${participant_details}")

        callDAO.saveParticipantDetails(
          latest_participant_mode = call_participant_mode,
          participant_uuid = call_participant_uuid,
          conference_uuid = conference_uuid,
          taskUuidAndProspectID = taskUuidAndProspectID,
          participant_details = participant_details,
          team_id = teamId,
          call_setting_uuid = call_setting_uuid,
          calling_device = calling_device,
          owner_account_id = accountId
        )
        //          .map(details => {
        //          val account_id: Option[AccountId] = if(participant_details.is_prospect)
        //            None
        //          else
        //            Some(call_account_settings.owner_account_id)
        //
        //          OngoingCallParticipantNotificationObject(
        //            conference_uuid = details.conferenceUuid,
        //            participant_uuid = details.participantUuid,
        //            latest_participation_mode = details.latest_participation_mode,
        //            prospect_id = details.prospectId,
        //            task_id = taskUuidAndProspectID.map(data => data.task_uuid),
        //            participant_account_id = account_id
        //          )
        //        })
      }
    )

    Helpers.seqTryToTrySeq(result)
  }

  def saveCallDetails(
                       call_details: CallDetails,
                       org_sub_account_uuid: SubAccountUuid,
                       call_account_settings: CallAccountSettings,
                       conferenceAndParticipantDetails: TwilioConferenceAndParticipantDetails,
                       taskUuidAndProspectID: Option[TaskUuidAndProspectID],
                       is_incoming: Boolean,
                       prospect_details_for_incoming_call: Option[ProspectDetailsForIncomingCalls] = None
                     )(using logger: SRLogger): Try[Seq[OngoingCallParticipantNotificationObject]] = {

    logger.info(s" \n\ncall_details : ${call_details}, org_uuid: ${org_sub_account_uuid.uuid}, call_uuid: ${call_account_settings.uuid}\n\n")


    for {

      conference_uuid: ConferenceUuid <- saveConferenceDetails(
        call_details = call_details,
        org_sub_account_uuid = org_sub_account_uuid,
        conference_details = conferenceAndParticipantDetails.conference_details,
        taskUuidAndProspectID = taskUuidAndProspectID,
        is_incoming = is_incoming,
        prospect_details_for_incoming_call = prospect_details_for_incoming_call,
        call_account_settings = call_account_settings
      )

      participant_details: Seq[ParticipantDetailsToNotify] <- saveParticipantDetails(
        call_participant_mode = call_details.call_participant_data,
        conference_uuid = conference_uuid,
        taskUuidAndProspectID = taskUuidAndProspectID,
        participantDetails = conferenceAndParticipantDetails.participantDetails,
        teamId = call_account_settings.team_id,
        accountId = call_account_settings.owner_account_id,
        calling_device = call_details.calling_device,
        call_setting_uuid = Some(call_account_settings.uuid.phone_number_uuid)
      )

      participant_details_for_pusher: Seq[OngoingCallParticipantNotificationObject] <- getDataForPusher(
        data = participant_details,
        teamId = call_account_settings.team_id,
        taskUuidAndProspectID = taskUuidAndProspectID,
        calling_device = call_details.calling_device,
        call_account_settings = call_account_settings
      )
    } yield {

      participant_details_for_pusher

    }
  }

  def getDataForPusher(
                        data: Seq[ParticipantDetailsToNotify],
                        teamId: TeamId,
                        taskUuidAndProspectID: Option[TaskUuidAndProspectID],
                        calling_device: Option[CallingDevice],
                        call_account_settings: CallAccountSettings
                      ): Try[Seq[OngoingCallParticipantNotificationObject]] = Try {
    data.map(details => {

      val prospect_details: Option[OngoingCallProspectDetails] = details.prospectId match {

        case None => None

        case Some(prospect_id) => Some(prospectDAO.getProspectDetailsForPusherNotification(
          prospectId = prospect_id,
          teamId = teamId
        ).get)
      }
      val participant_first_name = details.accountId.map(_ => call_account_settings.first_name)
      val participant_last_name = details.accountId.map(_ => call_account_settings.last_name)

      OngoingCallParticipantNotificationObject(
        conference_uuid = details.conferenceUuid,
        participant_uuid = details.participantUuid,
        latest_participation_mode = details.latest_participation_mode,
        ongoingCallProspectDetails = prospect_details,
        task_id = taskUuidAndProspectID.map(data => data.task_uuid),
        participant_account_id = details.accountId,
        participant_first_name = participant_first_name,
        calling_device = calling_device,
        participant_last_name = participant_last_name,
        call_sid = None
      )

    }

    )


  }


  // WEBHOOK HANDLE FUNCTION
  // FIXME CALL : USE VALUE CLASS FOR VOICERESPONSE WHICH IS CURRENTLY SENT AS STRING
  def handleCall(
                  validate_webhook_data: WebhookValidateData
                )(using logger: SRLogger, ws: WSClient, ec: ExecutionContext): Either[HandleCallError, String] = {

    val call_details = CallDetails.getCallDetailsFromParams(params = validate_webhook_data.params)

    call_details match {
      case Left(err) =>

        Left(HandleCallError.BadRequestError(err = err))

      case Right(callDetails) =>

        if (!callDetails.is_incoming) { // FIXME CALL this logic is twilio based move it to somewhere else
          // outgoing call

          logger.info(s"[HandleCalls] outgoing call request came : ${callDetails}")
          handleOutGoingCalls(
            call_details = callDetails,
            validate_webhook_data = validate_webhook_data
          )
          // outgoing calls

          // add a check here if the service_provider is twilio

        }
        else {
          // Incoming call

          logger.info(s"[HandleCalls] incoming calls call request came : ${callDetails}")
          handleIncomingCall(
            call_details = callDetails,
            validate_data = validate_webhook_data
          )


        }
    }

  }


  // FIXME CALL : This is unpaginated data.

  def getSubAccountsForUpdatingCredit(): Try[List[SubAccountDetailsForUpdate]] = {

    callDAO.getSubAccountsForUpdatingCredit()

  }

  def getSubAccountsForUpdatingCallHistory(): Try[List[SubAccountUuid]] = {

    callDAO.getSubAccountsForUpdatingCallHistoryLogs()

  }

  /*
    Use case :
      This function updates the remaining credit for SubAccount, We have pushed org_id and account-uuid in MqUpdateSubAccountCallingCredit,
      then when process message we are calling this function and passing org_id, uuid to this function.

      Now, this function is responsible for fetching usage from twilio and deducting the new usage from remaining balance then updating it in db

      * How this works, is
        1. Firstly we fetch SubAccountDetails related to org_id , sub-account uuid
        2. Fetching usage from twilio for the sub-account
        3. Checking if there is any new - usage .
            * We have already_subtracted column in db, this tell us old-usage,
            * we compare the fetched detailed with this field value,
            * if fetched_usage > already_subtracted, there is new usage and we are subtracting it from db
            * if not we are not doing the db call.
   */
  def updateSubAccountCredit(
                              sub_account_uuid: SubAccountUuid,
                              org_id: OrgId
                            )(using logger: SRLogger, ws: WSClient, ec: ExecutionContext): Either[UpdateSubAccountCreditError, Long] = {
    for {
      /*
            here we have sub_account_uuid, which is coming from the queue ( MqUpdateSubAccountCallingCredit ),
            hence we are passing this to db to fetch account details

            account details can be found with only org_id, but we are passing uuid also so that, the query will be fast.

            hence at some places where we only have org_id of the organization, we just pass org_id to fetch the sub-account details.
            that's why its sub_account_uuid is option.

           */
      // data-type : SubAccountDetails, Not added due to either withFilter issue
      sub_account_details <- getSubAccountDetails(sub_account_uuid = Some(sub_account_uuid), org_id = org_id)
        .toEither.left.map(e =>
          UpdateSubAccountCreditError.ErrorWhileGettingSubAccountFromDB(e)
        )

      // data-type : UsageDetails
      sub_acount_usage_details <- getUsageOfSubAccount(
        sub_account_sid = sub_account_details.sub_account_id,
        auth_token = sub_account_details.sub_auth_token,
        org_id = org_id
      )
      // data-type : Int
      update_Credit_in_db <- updateRemainingCreditInDB(
        sub_account_details = sub_account_details,
        usage_details = sub_acount_usage_details
      )

      // is threshold crossed warning + cache reset
      // Boolean
      check_if_low_credit_and_update_it_in_db <- checkIfLowCredit(
        org_id = org_id,
        current_credits = update_Credit_in_db,
        sub_account_details = sub_account_details
      )


    } yield {
      update_Credit_in_db
    }
  }


  def getUsageOfSubAccount(
                            sub_account_sid: TwlSubAccountSid,
                            auth_token: TwlAuthToken,
                            org_id: OrgId
                          )(using logger: SRLogger): Either[UpdateSubAccountCreditError, UsageDetails] = {

    twilioDialerService.getUsageOfSubAccount(
      sub_account_sid = sub_account_sid,
      sub_account_auth_token = auth_token
    ) match {

      case Left(err) =>
        Left(UpdateSubAccountCreditError.ErrorWhileGettingUsageFromTwilio(err))


      case Right(usageDetails) =>

        Right(usageDetails)
    }


  }

  def updateRemainingCreditInDB(
                                 sub_account_details: SubAccountDetails,
                                 usage_details: UsageDetails
                               )(using logger: SRLogger): Either[UpdateSubAccountCreditError, Long] = {

    val new_credit_details = CallService.calculateCredit(
      previously_deducted_cents = sub_account_details.already_subtracted_cents.getOrElse(0),
      usage_till_now_cents = usage_details.total_usage_in_cents,
      current_credits_left_cents = sub_account_details.call_remaining_credit_cents
    )

    // val already_subtracted: Long = orgSubAccountDetails.already_subtracted.getOrElse(0)
    // val remaining_to_subtract: Long = getUsageCredit.total_usage - already_subtracted
    // val updated_credit: Long = orgSubAccountDetails.call_remaining_credit - remaining_to_subtract

    // When there is no usage by the sub-account we are not doing db call
    if (new_credit_details.current_deducted_credit_cents == 0) {

      logger.info(s"Organization ${sub_account_details.org_id.id} didn't had any new usage hence avoiding the db call s_uuid: ${sub_account_details.uuid.uuid}")
      Right(sub_account_details.call_remaining_credit_cents)
    } else {

      logger.info(s"Organization ${sub_account_details.org_id.id} had new usage updating the db s_uuid: ${sub_account_details.uuid.uuid}  new_credit_details: ${new_credit_details} sub_account_details: ${sub_account_details}")

      callDAO.updateCreditForSubAccount(
        sub_account_uuid = sub_account_details.uuid,
        org_id = sub_account_details.org_id,
        credit_remaining = new_credit_details.updated_credit_for_org_cents,
        previous_usage_deducted = new_credit_details.current_deducted_credit_cents,
        already_subtracted = new_credit_details.current_already_subtracted_credit_cents
      ) match {

        case Failure(e) =>
          logger.info(s"Error while updating credits in db for organization ${sub_account_details.org_id.id}  s_uuid: ${sub_account_details.uuid.uuid} current_usage: ${new_credit_details.current_deducted_credit_cents}")
          Left(UpdateSubAccountCreditError.CreditSaveErrorInDB(e))

        case Success(updated_credit_remaining) =>

          Right(updated_credit_remaining)
      }
    }
  }

  def updateAddedToCallingCreditQueue(
                                       sub_account_uuid: SubAccountUuid,
                                       org_id: OrgId
                                     ): Try[Long] = {

    callDAO.addedToTheQueue(
      sub_account_uuid = sub_account_uuid,
      org_id = org_id
    )

  }

  def updateRemovedFromQueue(
                              sub_account_uuid: SubAccountUuid,
                              org_id: OrgId
                            )(using logger: SRLogger): Try[Int] = {

    logger.info(s"Updating pushed to queue as false as credit got updated for org_id: ${org_id.id} uuid: ${sub_account_uuid.uuid}")

    callDAO.updateRemovedFromQueue(
      sub_account_uuid = sub_account_uuid,
      org_id = org_id
    )

  }

  def updateAddedToCallHistoryLogsMq(
                                      sub_account_uuid: SubAccountUuid
                                    ): Try[Long] = {

    callDAO.addedToTheQueueCallHistoryLogs(
      sub_account_uuid = sub_account_uuid
    )

  }

  def updateRemovedFromQueueForCallHistoryMq(
                                              sub_account_uuid: SubAccountUuid
                                            )(using logger: SRLogger): Try[Int] = {

    logger.info(s"Updating pushed to queue as false as call history logs got updated for sub_account: ${sub_account_uuid.uuid}")

    callDAO.updateRemovedFromQueueForCallHistoryMq(
      sub_account_uuid = sub_account_uuid
    )

  }


  /*
    This webhook will be triggered when user crossed its set limit for sub-account in twilio
      * we are then suspending the user account
      * and saving details in db

      Todo: Convert into for-comprehension

   */
  def handleUsageLimitCrossedWebhook(
                                      webhook_details: TwilioUsageLimitCrossedDetails,
                                      org_id: OrgId
                                    )(using logger: SRLogger, ec: ExecutionContext, ws: WSClient): Either[HandlingUsageWebhookError, Int] = {

    val suspended_account_warning_message = "Your credit limit for calling has been reached. Please add more credits to ensure smooth calling with prospects"

    for {

      // SubAccountDetails
      sub_account_details_from_db <- getSubAccountDetails(org_id = org_id, sub_account_uuid = None)
        .toEither.left.map(e =>
          HandlingUsageWebhookError.ErrorWhileGettingSubAccountError(e)
        )

      // Boolean
      is_idempotency_token_different <- {
        if (sub_account_details_from_db.twl_idempotency_token.isEmpty || (sub_account_details_from_db.twl_idempotency_token.get != webhook_details.idempotency_token)) {

          Right(true)

        } else {
          Left(HandlingUsageWebhookError.SameTriggerIdempotencyTokenError)
        }
      }

      // TwilioSubAccountDetails
      suspend_twilio_sub_account <- twilioDialerService.suspendSubAccount(
        sub_account_sid = webhook_details.twl_sub_account_sid
      ).toEither.left.map(err => HandlingUsageWebhookError.ErrorWhileSuspendingAccount(err))

      // Int
      save_suspended_account_details_in_db <- callDAO.saveSuspendedSubAccountDetails(
        sub_account_details = suspend_twilio_sub_account,
        suspend_account_webhook = webhook_details,
        org_id = org_id
      ).toEither.left.map(err => HandlingUsageWebhookError.ErrorWhileUpdatingAccountStatusInDB(err))

      // String
      add_call_features_suspended_warning_in_db <- addWarningMessageInOrgCallingCredit(
        org_call_warning_details = OrgCallWarningErrorDetails(
          warning_message = suspended_account_warning_message,
          warning_at = DateTime.now(),
          warning_code = AccountWarningCodeType.CallingFeatureSuspended
        ),
        org_id = org_id,
        call_credit_remaining = None,
        subAccountDetails = sub_account_details_from_db,
        ws = ws,
        ec = ec
      ).left.map(err => HandlingUsageWebhookError.ErrorWhileAddingWarningMessage(err))

    } yield {

      save_suspended_account_details_in_db

    }


  }

  def addWarningMessageInOrgCallingCredit(
                                           call_credit_remaining: Option[Long],
                                           org_call_warning_details: OrgCallWarningErrorDetails,
                                           org_id: OrgId,
                                           ws: WSClient,
                                           ec: ExecutionContext,
                                           subAccountDetails: SubAccountDetails
                                         )(using logger: SRLogger): Either[AddOrgCallingCreditWarningError, String] = {


    for {

      // AccountId
      get_account_id_from_org_id <- accountDAO.getOwnerAccountIdByOrgId(
        orgId = org_id
      ) match {
        case Failure(err) =>
          Left(AddOrgCallingCreditWarningError.ErrorWhileFetchingOwnerAccountId(err))

        case Success(None) =>

          Left(AddOrgCallingCreditWarningError.NoneReturnedAfterFetchingOwnerAccountId)

        case Success(Some(owner_id)) =>

          Right(owner_id)
      }

      //Unit
      send_email_notification <- {

        sendEmailNotificationIfNotSentToday(
          account_id = get_account_id_from_org_id,
          sub_account_details = subAccountDetails,
          accountWarningCodeType = org_call_warning_details.warning_code,
          call_credit_remaining = call_credit_remaining
        )(ws, ec, logger)

      }

      // Option[String]
      add_warning_in_db <- callDAO.addWarningMessageInOrgCallingCredits(
        warning_message = org_call_warning_details.warning_message,
        warning_at = org_call_warning_details.warning_at,
        warning_code = org_call_warning_details.warning_code,
        org_id = org_id
      ) match {

        case Failure(e) =>
          Left(AddOrgCallingCreditWarningError.ErrorWhileAddingErrorInDB(err = e))

        case Success(None) =>
          Left(AddOrgCallingCreditWarningError.NoneReturnedAfterSavingWarningError)

        case Success(Some(msg)) =>

          Right(msg)

      }

      // Unit
      reset_cache_after_warning <- Right(resetCache(
        accountId = get_account_id_from_org_id
      ))

    } yield {

      add_warning_in_db

    }
  }

  //  def getLatestEmailSentAndCheckIf24HourPassed(
  //                                              notification_type: NotificationType,
  //                                              org_id: OrgId
  //                                              ): Try[Boolean] = {
  //
  //
  //   val email_log: Option[EmailNotificationLog] =  emailNotificationService.getLatestEmailSendLog(
  //      notification_type = NotificationType.LowCallingCreditAvailable,
  //      org_id = org_id
  //    )
  //
  //    if(email_log.nonEmpty && CallService.is24hrPassed(time = email_log.get.sent_at) ){
  //
  //      true
  //
  //    } else {
  //
  //      false
  //    }
  //
  //  }

  def sendEmailNotificationIfNotSentToday(
                                           sub_account_details: SubAccountDetails,
                                           account_id: AccountId,
                                           accountWarningCodeType: AccountWarningCodeType,
                                           call_credit_remaining: Option[Long]

                                         )(implicit ws: WSClient, ec: ExecutionContext, logger: SRLogger): Either[AddOrgCallingCreditWarningError, Unit] = {

    val res: Either[AddOrgCallingCreditWarningError, Unit] = accountWarningCodeType match {

      case AccountWarningCodeType.LowCallingCredit =>
        // add a check here

        val b: Either[AddOrgCallingCreditWarningError, Unit] = for {

          // Option[EmailNotificationLog]
          get_last_email_send_log <- emailNotificationService.getLatestEmailSendLog(
            notification_type = NotificationType.LowCallingCreditAvailable,
            org_id = sub_account_details.org_id,
            teamId = None
          ).left.map(err => AddOrgCallingCreditWarningError.ErrorWhileFetchingNotificationEmailLog(err = err))

          // Boolean
          should_send_new_email <- {
            if (
              get_last_email_send_log.isEmpty
            ) {

              Right(true)

            } else if (
              CallService.is24hrPassed(
                time = get_last_email_send_log.get.sent_at
              )
            ) {

              Right(true)

            } else {

              Right(false)
            }

          }

          isPlanInactive: Boolean = organizationDAOService.getOrgWithCurrentData(
            orgId = sub_account_details.org_id,
          ) match {

            case Failure(exception) =>

              logger.shouldNeverHappen(
                msg = s"sendEmailNotificationIfNotSentToday - failed to fetch organization. orgId: ${sub_account_details.org_id} :: accountId: $account_id",
                err = Some(exception),
              )

              true

            case Success(None) =>

              logger.shouldNeverHappen(
                msg = s"sendEmailNotificationIfNotSentToday - failed to fetch organization - None. orgId: ${sub_account_details.org_id} :: accountId: $account_id",
              )

              true

            case Success(Some(org)) =>

              org.plan.plan_type == PlanType.INACTIVE

          }

          // Boolean
          send_notification <- {

            if (should_send_new_email && !isPlanInactive) {
              emailNotificationService.sentLimitReachedWarning(
                  accountId = account_id,
                  remainingCredit = Some(math.floor(call_credit_remaining.get / 100.0).toInt),
                  orgId = sub_account_details.org_id,
                  accountWarningCodeType = LowCallingCredit,
                  pausedTill = None,
                  prospectLimit = None
                )(ws, ec, logger).toEither
                .left.map(err => AddOrgCallingCreditWarningError.ErrorWhileSendingWarningEmails(err))

            } else {
              if(isPlanInactive){
                logger.info(s"[addWarningMessageInOrgCallingCredit] is_low_credit_last_email_sent_at email not sent as plan is inactive orgId :: ${sub_account_details.org_id}")
                
              }else{
                logger.info(s"[addWarningMessageInOrgCallingCredit] is_low_credit_last_email_sent_at: ${get_last_email_send_log.get.sent_at} which is not before 24hrs")


              }


              Right(true)

            }
          }

        } yield {

          ()

        }

        b


      case AccountWarningCodeType.CallingFeatureSuspended =>


        val a: Either[AddOrgCallingCreditWarningError, Unit] = for {

          // Option[EmailNotificationLog]
          get_last_email_send_log <- emailNotificationService.getLatestEmailSendLog(
            notification_type = NotificationType.CallingFeatureSuspended,
            org_id = sub_account_details.org_id,
            teamId = None
          ).left.map(err => AddOrgCallingCreditWarningError.ErrorWhileFetchingNotificationEmailLog(err = err))

          // Boolean
          should_send_new_email <- {
            if (
              get_last_email_send_log.isEmpty
            ) {

              Right(true)

            } else if (
              CallService.is24hrPassed(
                time = get_last_email_send_log.get.sent_at
              )
            ) {

              Right(true)

            } else {

              Right(false)

            }

          }

          // Boolean
          send_email_notification <- {

            if (should_send_new_email) {
              emailNotificationService.sentLimitReachedWarning(
                  accountId = account_id,
                  orgId = sub_account_details.org_id,
                  accountWarningCodeType = AccountWarningCodeType.CallingFeatureSuspended,
                  prospectLimit = None,
                  pausedTill = None,
                  remainingCredit = None
                )(ws, ec, logger).toEither
                .left.map(err => AddOrgCallingCreditWarningError.ErrorWhileSendingWarningEmails(err))
            } else {

              logger.info(s"[addWarningMessageInOrgCallingCredit] call_feature_suspended email already sent: ${get_last_email_send_log.get.sent_at} which is not before 24hrs")

              Right(true)

            }

          }

        } yield {

          ()

        }

        a

      case AccountWarningCodeType.ProspectLimitExceeded
           | AccountWarningCodeType.ProspectLimit80Percent
           | AccountWarningCodeType.ProspectSavedWarning =>

        logger.shouldNeverHappen(
          msg = s"AddOrgCallingCreditWarningError invalid warning type found. accountId: $account_id :: accountWarningCodeType: $accountWarningCodeType"
        )

        Left(AddOrgCallingCreditWarningError.InvalidWarningTypeFound(warning = accountWarningCodeType))

    }

    res
  }


  def saveCallRecordingForConf(
                                RecordingUrl: String,
                                conferenceSid: ConferenceSid,
                                teamId: TeamId
                              ): Either[RecordingStatusCallbackError, Int] = {

    callDAO.saveCallRecordingData(
      RecordingUrl = RecordingUrl,
      confSidOrConfUuid = ConfSidOrConfUuid.WithConfSid(conferenceSid),
      tid = teamId
    ) match {

      case Failure(exception) => Left(RecordingStatusCallbackError.ErrorWhileUpdatingRecordingUrlInDb(err = exception))

      case Success(res) => Right(res)
    }
  }

  def saveCallRecordingForBasicCall(
                                     RecordingUrl: String,
                                     callSID: CallSID,
                                     teamId: TeamId
                                   ): Either[RecordingStatusCallbackError, Int] = {

    for {
      //ConferenceUuid
      conf_uuid <- callDAO.getConferenceUuidFromParentCallSID(
        callSID = callSID,
        teamId = teamId
      ).toEither.left.map(err => RecordingStatusCallbackError.ErrorWhileFetchingConferenceUuid(err = err))

      //Int
      res <- 
        callDAO.saveCallRecordingData(
          RecordingUrl = RecordingUrl,
          confSidOrConfUuid = ConfSidOrConfUuid.WithConfUuid(conf_uuid),
          tid = teamId
        ).toEither.left.map(err => RecordingStatusCallbackError.ErrorWhileUpdatingRecordingUrlInDb(err = err))

    } yield {
      res

    }
  }


  def saveCallRecordingData(
                             data: RecordingData,
                             tid: TeamId
                           ): Either[RecordingStatusCallbackError, Int] = {


    data.ConferenceSid match {
      case Some(confSid: ConferenceSid) =>
        saveCallRecordingForConf(
          RecordingUrl = data.RecordingUrl,
          conferenceSid = confSid,
          teamId = tid
        )

      case None =>
        data.CallSid match {
          case Some(callSid: CallSID) =>
            saveCallRecordingForBasicCall(
              RecordingUrl = data.RecordingUrl,
              callSID = callSid,
              teamId = tid
            )

          case None => Left(RecordingStatusCallbackError.NoConferenceAndCallSidFound)
        }

    }


  }

  private def updateSubAccountStatus(
                                      sub_account_details: TwilioSubAccountDetails,
                                      org_id: OrgId
                                    ): Either[ActivatingSubAccountError.ErrorWhileUpdatingStatusInDB, Int] = {

    callDAO.updateSubAccountStatus(
      sub_account_details = sub_account_details,
      org_id = org_id
    ) match {
      case Failure(err) =>
        Left(ActivatingSubAccountError.ErrorWhileUpdatingStatusInDB(err))

      case Success(rows_updated) =>

        Right(rows_updated)

    }
  }

  private def activateSubAccountIfSusPended(
                                             sub_account_details: SubAccountDetails
                                           )(using logger: SRLogger): Either[ActivatingSubAccountError, TwilioSubAccountDetails] = {

    if (sub_account_details.status == SubAccountStatus.Suspended) {
      twilioDialerService.activateSubAccount(
        sub_account_sid = sub_account_details.sub_account_id
      ) match {

        case Failure(err) =>
          Left(ActivatingSubAccountError.ErrorWhileActivatingSubAccount(err))

        case Success(data) =>

          Right(data)

      }
    } else {
      Left(ActivatingSubAccountError.AccountNotSuspended)
    }
  }

  /*
    this function is for activating suspended sub-account this will be used when user adds credit
    and we again need to activate their account at twilios end.

    Fixme call: This function will change based on tulika's billing changes

   */

  def addNativeCallingNumber(
                              callAccountDetails: NativeCallingAccountData,
                              teamId: TeamId,
                              accountId: AccountId
                            ): Either[SaveCallingAccountError, PhoneNumberUuid] = {
    val phone_number_uuid: PhoneNumberUuid = PhoneNumberUuid(phone_number_uuid = srUuidUtils.generatePhoneNumberUuid)

    callDAO.addNativeCallingNumber(
      firstName = callAccountDetails.first_name,
      lastName = callAccountDetails.last_name,
      call_limit_per_day = callAccountDetails.call_limit,
      teamId = teamId,
      accountId = accountId,
      phone_uuid = phone_number_uuid
    ) match {
      case Failure(err) =>
        Left(SaveCallingAccountError.SQlExceptionError(err))

      case Success(uuid) =>
        Right(uuid)
    }
  }


  def activateSubAccountForOrg(
                                org_id: OrgId
                              )(using logger: SRLogger): Either[ActivatingSubAccountError, Int] = {

    for {
      sub_account_details <- getSubAccountDetails(
        org_id = org_id,
        sub_account_uuid = None
      ).toEither.left.map(err => ActivatingSubAccountError.ErrorWhileFetchingDetailsFromDB(err))

      activate_sub_account_if_suspended <- activateSubAccountIfSusPended(
        sub_account_details = sub_account_details
      )

      save_updated_status <- updateSubAccountStatus(
        sub_account_details = activate_sub_account_if_suspended,
        org_id = org_id
      )

    } yield {
      save_updated_status
    }
  }

  def deleteUsageTrigger(
                          twl_sub_account_sid: TwlSubAccountSid,
                          twl_sub_account_auth_token: TwlAuthToken,
                          trigger_id: Option[String]
                        )(using logger: SRLogger): Either[DeleteUsageTriggerError, Boolean] = {


    trigger_id match {

      case None =>

        Left(DeleteUsageTriggerError.TriggerIdNotFoundInDB)

      case Some(id) =>
        twilioDialerService.deleteTrigger(
          sub_account_sid = twl_sub_account_sid,
          sub_account_auth_token = twl_sub_account_auth_token,
          trigger_sid = id
        ) match {

          case Failure(e) =>

            Left(DeleteUsageTriggerError.TwilioErrorWhileDeletingTrigger(err = e))

          case Success(boolean) =>

            logger.info(s"Trigger deleted ${trigger_id} account sid : ${twl_sub_account_sid.id}")

            Right(boolean)

        }
    }
  }


  // FIXME CALL make return type generic
  def createNewUsageTrigger(
                             org_id: OrgId,
                             usage_limit: String,
                             twl_sub_account_sid: TwlSubAccountSid,
                             twl_sub_account_auth_token: TwlAuthToken
                           )(using logger: SRLogger): Either[CreateUsageTriggerError, TwilioUsageTrigger] = {


    for {

      //Type:  TwilioUsageTrigger
      create_usage_trigger <- twilioDialerService.createUsageTrigger(
        org_id = org_id,
        usage_limit = usage_limit,
        subaccount_sid = twl_sub_account_sid,
        subaccount_auth_token = twl_sub_account_auth_token
      ).toEither.left.map(err => CreateUsageTriggerError.ErrorWhileCreatingNewUsageTriggerInTwilio(err))

      //Type:  String
      save_twilio_trigger_details <- callDAO.saveTwilioTriggerDetails(
        usageTrigger = create_usage_trigger
      ).toEither.left.map(err => CreateUsageTriggerError.ErrorWhileSavingUsageTriggerInDB(err))
    }
    yield {

      logger.info(s"New trigger created ${org_id.id} account sid : ${twl_sub_account_sid.id}")
      create_usage_trigger
    }

  }

  def updateMainCreditInDB(
                            sub_account_details: SubAccountDetails,
                            new_credit_details: AddCallingCreditDetails
                          )(using logger: SRLogger): Either[AddCallingCreditsError, Int] = {

    callDAO.updateMainCreditForSubAccount(
      sub_account_details = sub_account_details,
      new_credit_details = new_credit_details
    ) match {
      case Failure(err) =>

        logger.fatal(s"Error while updating credits in db for org: ${sub_account_details.org_id.id} ")
        Left(AddCallingCreditsError.ErrorWhileUpdatingCreditsInDB(err))


      case Success(rows_updated) =>

        if (rows_updated.toLong == 0) {

          //Fixme call
          // return a left : These needs to be fixed here and at other places
          // https://github.com/heaplabs/team_discussions/discussions/60#discussioncomment-6277984
          logger.fatal(s"0 rows updated while updated credits of org: ${sub_account_details.org_id.id}")

        } else {

          logger.info(s"Successfully updated the credits for org : ${sub_account_details.org_id.id} new_credits: ${new_credit_details.credit_added_cents}")
        }

        Right(rows_updated)

    }
  }

  def createApiKeyAndSecretForSubAccount(
                                          sub_account_sid: TwlSubAccountSid,
                                          sub_auth_token: TwlAuthToken,
                                          sub_account_uuid: SubAccountUuid
                                        )(using logger: SRLogger): Either[CreateApiKeyAndSecretError, SubAccountApiKeyAndSecret] = {

    twilioDialerService.createApiKeySecretForSubAccount(
      sub_account_sid = sub_account_sid,
      sub_account_auth_token = sub_auth_token,
      sub_account_uuid = sub_account_uuid
    ) match {

      case Failure(err) =>

        Left(CreateApiKeyAndSecretError.ErrorWhileCreatingApiKeyAndSecret(err))


      case Success(api_key_and_secret) =>

        Right(api_key_and_secret)
    }

  }


  private def addCallingCreditsInActiveAccounts(
                                                 sub_account_details: SubAccountDetails,
                                                 org_id: OrgId,
                                                 credit_details: AddCallingCreditDetails
                                               )(using logger: SRLogger): Either[AddCallingCreditsError, Int] = {
    /*
      Status is active hence, we will be creating new trigger,  then deleting previous trigger,
      then updating db with new trigger details, then updating credit balance in db

      call_credits coming from db is in multiple of hundreds hence, we are dividing it with 100
     */

    for {

      create_new_usage_trigger <- createNewUsageTrigger(
        org_id = org_id,
        usage_limit = ((sub_account_details.call_credits_cents + credit_details.credit_added_cents) / 100).toString,
        twl_sub_account_sid = sub_account_details.sub_account_id,
        twl_sub_account_auth_token = sub_account_details.sub_auth_token
      ).left.map(err => AddCallingCreditsError.TriggerCreateError(err))


      delete_previous_usage_trigger <- deleteUsageTrigger(
        twl_sub_account_sid = sub_account_details.sub_account_id,
        twl_sub_account_auth_token = sub_account_details.sub_auth_token,
        trigger_id = sub_account_details.trigger_id
      ).left.map(err => AddCallingCreditsError.TriggerDeleteError(err))


      update_main_credit_in_db <- updateMainCreditInDB(
        sub_account_details = sub_account_details,
        new_credit_details = credit_details
      )

    } yield {
      // todo fixme call check if rows-updated is 0 throw it as error
      update_main_credit_in_db
    }

  }

  private def addCallingCreditsInSuspendedSubAccount(
                                                      sub_account_details: SubAccountDetails,
                                                      org_id: OrgId,
                                                      credit_details: AddCallingCreditDetails // Added credit should be in multiple of 100
                                                    )(using logger: SRLogger): Either[AddCallingCreditsError, Int] = {

    /*
    if status is suspended then

    * check if he has paid previous due ( over-usage charges )
    * activate the account
    * delete previous usage trigger
    * create new usage trigger
    * update credits in db

     */

    for {

      checking_if_over_usage_paid <- CallService.checkIfOverUsagePaid(
        sub_account_details = sub_account_details,
        credit_detail = credit_details
      )

      activate_sub_account_for_org <- activateSubAccountForOrg(
        org_id = org_id
      ).left.map(err => AddCallingCreditsError.NotAbleToActivateAccount(err))

      delete_usage_trigger <- deleteUsageTrigger(
        twl_sub_account_sid = sub_account_details.sub_account_id,
        twl_sub_account_auth_token = sub_account_details.sub_auth_token,
        trigger_id = sub_account_details.trigger_id
      ).left.map(err => AddCallingCreditsError.TriggerDeleteError(err))

      create_new_usage_trigger <- createNewUsageTrigger(
        org_id = org_id,
        usage_limit = ((sub_account_details.call_credits_cents + credit_details.credit_added_cents) / 100).toString,
        twl_sub_account_sid = sub_account_details.sub_account_id,
        twl_sub_account_auth_token = sub_account_details.sub_auth_token
      ).left.map(err => AddCallingCreditsError.TriggerCreateError(err))


      update_main_credit_for_org <- updateMainCreditInDB(
        sub_account_details = sub_account_details,
        new_credit_details = credit_details,
      )

    } yield {
      // Todo check if this value is 0 and throw an error for that Fixme call
      update_main_credit_for_org
    }
  }

  /*
  Checking if currency type is other than usd, then converting it to usd via
  api_layer_currency_api
   */
  def convertCurrencyToUSD(
                            credit_details: AddCallingCreditDetails
                          )(
                            implicit ec: ExecutionContext,
                            ws: WSClient,
                            logger: SRLogger
                          ): Future[Either[ErrorWhileCurrencyConversion, AddCallingCreditDetails]] = {


    credit_details.credit_unit match {

      case CurrencyType.USD =>

        Future.successful(Right(credit_details))

      case _ =>

        val result = for {

          convert_currency_api: Option[ConvertedCurrency] <- apiLayerService.convertCurrencyRate(

            from = credit_details.credit_unit,
            to = CurrencyType.USD,
            amount = (credit_details.credit_added_dollar).toFloat,
            apiKey = AppConfig.ApiLayerCurrencyService.apiKey

          )

        } yield {

          convert_currency_api match {

            case None =>

              Left(ErrorWhileCurrencyConversion.NoneFoundAfterConversion)

            case Some(usd_currency) =>

              Right(credit_details.copy(
                credit_added_dollar = usd_currency.result.toLong,
                credit_added_cents = (usd_currency.result * 100).toLong,
                credit_unit = CurrencyType.USD
              ))

          }

        }

        result.recover(err => {
          Left(ErrorWhileCurrencyConversion.ExceptionOccured(err))
        })

    }

  }

  def addCallingCredits(
                         org_id: OrgId,
                         credit_details: AddCallingCreditDetails
                       )(
                         implicit logger: SRLogger,
                         ec: ExecutionContext,
                         ws: WSClient
                       ): Future[Either[AddCallingCreditsError, Int]] = {

    convertCurrencyToUSD(
      credit_details = credit_details
    ).map {

      case Left(ErrorWhileCurrencyConversion.NoneFoundAfterConversion) =>

        Left(AddCallingCreditsError.CurrencyConversionError(ErrorWhileCurrencyConversion.NoneFoundAfterConversion))

      case Left(ErrorWhileCurrencyConversion.ExceptionOccured(e)) =>

        Left(AddCallingCreditsError.CurrencyConversionError(ErrorWhileCurrencyConversion.ExceptionOccured(e)))

      case Right(converted_credit_details) =>


        for {
          // OrganizationWithCurrentData
          org <- organizationDAOService.getOrgWithCurrentData(orgId = org_id) match {


            case Failure(exception) => Left(AddCallingCreditsError.ErrorWhileFetchingOrgDetails(exception))


            case Success(org_opt) => org_opt match {

              case None =>
                Left(AddCallingCreditsError.ErrorWhileFetchingOrgDetails(new Exception(s"Organization with id: ${org_id.id} not found.")))


              case Some(value) => Right(value)
            }

          }

          // SubAccountDetailsFound
          create_sub_account_if_not_exists <-
            createSubAccountIfNotExist(
              org_id = org_id,
              org_plan = org.plan
            ) match {
              case Left(err) => Left(AddCallingCreditsError.SubAccountError(err))
              case Right(value) => Right(value)

            }
          //          .left.map(err => )


          //SubAccountDetails
          get_sub_account_details <- {
            getSubAccountDetails(
              sub_account_uuid = None,
              org_id = org_id

            )
          }.toEither.left.map(e =>
            AddCallingCreditsError.ErrorWhileFetchingSubAccountDetails(e)
          )

          // Int
          add_call_credit_based_on_status <- {

            if (get_sub_account_details.status == SubAccountStatus.Active) {

              addCallingCreditsInActiveAccounts(
                sub_account_details = get_sub_account_details,
                org_id = org_id,
                credit_details = converted_credit_details
              )


            } else {

              addCallingCreditsInSuspendedSubAccount(
                sub_account_details = get_sub_account_details,
                org_id = org_id,
                credit_details = converted_credit_details
              )

            }
          }

        } yield {
          add_call_credit_based_on_status
        }


    }

  }

  def createSubAccountTwimlApplication(
                                        sub_account_sid: TwlSubAccountSid,
                                        sub_auth_token: TwlAuthToken,
                                        sub_account_uuid: SubAccountUuid
                                      )(using logger: SRLogger): Either[CreateTwimlApplicationError, SubAccountTwimlApplication] = {

    twilioDialerService.createTwimlAppForSubAccounts(
      sub_account_sid = sub_account_sid,
      sub_account_auth_token = sub_auth_token,
      sub_account_uuid = sub_account_uuid
    ) match {

      case Failure(err) =>

        Left(CreateTwimlApplicationError.ErrorWhileCreatingTwimlApplicationForSubAccount(err))


      case Success(twimlApplication) =>

        Right(twimlApplication)
    }

  }

  def saveSubAccountTwimlApplicationData(
                                          twiml_app: SubAccountTwimlApplication,
                                          sub_account_uuid: SubAccountUuid,
                                          org_id: OrgId
                                        ): Either[CreateTwimlApplicationError, TwlTwimlAppName] = {

    callDAO.saveSubAccountTwimlApp(
      twl_app = twiml_app,
      subAccountUuid = sub_account_uuid,
      org_Id = org_id
    ) match {

      case Failure(err) =>

        Left(CreateTwimlApplicationError.ErrorWhileSavingTwimlApplicationForSubAccount(err))


      case Success(None) =>

        Left(CreateTwimlApplicationError.NoneReturnedAfterSavingError)


      case Success(Some(twl_app_name)) =>

        Right(twl_app_name)

    }

  }


  def saveSubAccountApiKeyAndSecret(
                                     api_data: SubAccountApiKeyAndSecret,
                                     sub_account_uuid: SubAccountUuid,
                                     org_id: OrgId
                                   ): Either[CreateApiKeyAndSecretError, ApiKey] = {

    callDAO.saveSubAccountEncryptionKeys(
      api_data = api_data,
      subAccountUuid = sub_account_uuid,
      org_Id = org_id
    ) match {

      case Failure(err) =>

        Left(CreateApiKeyAndSecretError.ErrorWhileSavingApiKeyAndSecret(err))


      case Success(None) =>

        Left(CreateApiKeyAndSecretError.NoneReturnedWhileSavingError)


      case Success(Some(api_key)) =>

        Right(api_key)

    }

  }

  def validateWebhookRequest(
                              twilio_signature: Option[String],
                              webhook_url: String,
                              params: Map[String, String],
                              sub_account_details: SubAccountDetails
                            )(using logger: SRLogger): Either[ValidateWebhookError, true] = {

    logger.info(s"[WebhookAuthenticator] going to validate webhook request : ${sub_account_details.uuid.uuid} :: webhook_url : ${webhook_url} :: params: ${params} :: twilio_signature : ${twilio_signature}")

    if (twilio_signature.isEmpty) {

      logger.info(s"[WebhookAuthenticator] twilio_signature is empty : ${twilio_signature}")

      Left(ValidateWebhookError.TwilioSignatureIsEmptyError)

    } else {
      authenticateCallingWebhook(
        webhook_url = webhook_url,
        params = params,
        sub_account_details = sub_account_details,
        webhook_header_signature = twilio_signature.get
      )
    }
  }

  def authenticateCallingWebhook(
                                  webhook_url: String,
                                  webhook_header_signature: String,
                                  params: Map[String, String],
                                  sub_account_details: SubAccountDetails
                                )(using logger: SRLogger): Either[ValidateWebhookError, true] = {

    val is_validated: Either[ValidateWebhookError, Boolean] = for {

      // Type: Boolean
      validate_request <- twilioDialerService.authenticateTwilioWebhook(
        sub_account_auth_token = sub_account_details.sub_auth_token,
        params = params,
        webhook_url = webhook_url,
        twilio_webhook_signature = webhook_header_signature
      ).toEither.left.map(err => ValidateWebhookError.TwilioErrorWhileValidatingWebhook(err))


    } yield {

      //      validate_request
      true // Fixme call

    }

    is_validated match {
      case Left(err) =>

        Left(err)

      case Right(boolean) =>

        if (!boolean) {

          logger.info("[WebhookAuthenticator] twilio validator returned false")

          Left(ValidateWebhookError.WebhookValidationFailedError)
        } else {

          logger.debug("[WebhookAuthenticator] validated successfully")

          Right(true)
        }

    }
  }

  def updateCallNote(
                      call_note: CallNote,
                      callSidOrConfUuid: CallSidOrConfUuid,
                      team_id: TeamId,
                      loggedInAccount: Account
                    ): Either[SaveNoteError, CallNote] = {

    callDAO.getNoteIdFromCallParticipantsLogs(
      callSidOrConfUuid = callSidOrConfUuid,
      teamId = team_id
    ) match {

      case Failure(err) =>

        Left(SaveNoteError.SQlExceptionError(err))

      case Success(None) =>

        createCallNote(
          callSidOrConfUuid = callSidOrConfUuid,
          teamId = team_id,
          loggedInAccount = loggedInAccount,
          callNote = call_note
        ) match {

          case Failure(exception) => Left(SaveNoteError.SQlExceptionError(exception))

          case Success(callNote) => Right(callNote)

        }

      case Success(Some(noteId: NoteId)) =>

        notesDAO.update(
          note = call_note.note,
          noteId = noteId,
          teamId = team_id
        ) match {

          case Failure(exception) => Left(SaveNoteError.SQlExceptionError(exception))

          case Success(None) => Left(SaveNoteError.CallNoteNotUpdated)

          case Success(Some(value)) =>

            Right(CallNote(value.note))

        }

    }

  }

  def createCallNote(
                      callSidOrConfUuid: CallSidOrConfUuid,
                      teamId: TeamId,
                      loggedInAccount: Account,
                      callNote: CallNote
                    ): Try[CallNote] = {

    val accountId = AccountId(loggedInAccount.internal_id)

    for {

      prospectIdAndTaskUuidOpt: Option[GetNotesForm] <- callDAO.getProspectIdAndTaskUuidForConference(
        callSidOrConfUuid = callSidOrConfUuid,
        teamId = teamId
      )

      prospectIdAndTaskUuid: GetNotesForm <- prospectIdAndTaskUuidOpt match {
        case None => Failure(new Exception("invalid callSidOrConfUuid passed"))
        case Some(value) if (value.taskUuid.isEmpty && value.prospectId.isEmpty) => Failure(new Exception(s"Both task_uuid and prospect_id are empty for callSidOrConfUuid : $callSidOrConfUuid"))
        case Some(value) => Success(value)
      }

      createNotesForm: CreateNotesForm = CreateNotesForm(
        note = callNote.note,
        added_by = accountId,
        team_id = teamId,
        prospect_id = prospectIdAndTaskUuid.prospectId,
        task_uuid = prospectIdAndTaskUuid.taskUuid
      )

      noteCreated: Note <- notesDAO.create(
        createNotesForm = createNotesForm
      )

      _: NoteId <- callDAO.updateNoteIdInCallParticipantsLogs(
        callSidOrConfUuid = callSidOrConfUuid,
        teamId = teamId,
        noteId = noteCreated.noteId
      )

    } yield {

      CallNote(noteCreated.note)

    }
  }

  def updateCallParticipantMode(
                                 org_id: OrgId,
                                 teamId: TeamId,
                                 participant_account_id: AccountId,
                                 prospect_id_in_conference: Option[ProspectId],
                                 new_participant_mode: CallParticipationMode
                               ): Try[Int] = Try { //Fixme call : change to Either return type and return valid return type like UpdatedParticipant case class other than Int

    val update_result: Try[Int] = for {

      sub_account_details_of_org: Option[SubAccountDetails] <- callDAO.getSubAccountDetailsForOrg(
        org_id = org_id,
        sub_account_uuid = None
      )

      call_account_settings: Option[CallAccountSettings] <- getCallSettingOfUser(
        teamId = teamId,
        accountId = participant_account_id
      )

      call_participant_details: ParticipantUpdateDetails <- callDAO.getParticipantDetailsForCallSetting(
        participant_phone_number_uuid = call_account_settings.get.uuid.phone_number_uuid,
        teamId = teamId
      )

      call_with_prospect_initiator_call_sid: CallSID <- callDAO.getCallInitiatorCallSid(
        prospectId = prospect_id_in_conference.get,
        teamId = teamId
      )

      participant_updated: ParticipantUpdateDetails <- twilioDialerService.updateParticipantMode(
        subAccountDetails = sub_account_details_of_org.get,
        call_participant_update_data = call_participant_details,
        new_participant_mode = new_participant_mode,
        call_with_prospect_initiator_call_sid = call_with_prospect_initiator_call_sid
      )

      update_participant_in_db <- callDAO.updateCallParticipantLogs(
        data = participant_updated,
        teamId = teamId
      )

    } yield {
      update_participant_in_db
    }

    update_result match {
      case Failure(exception) => throw exception
      case Success(res) => res
    }

  }

  def saveCallFeedBack(
                        call_feedback: CallFeedBack,
                        team_id: TeamId,
                        call_sid: CallSID
                      ): Either[CallQualityFeedBackError, CallFeedBackResponse] = {

    callDAO.saveCallFeedback(
      call_sp_sid = call_sid,
      team_id = team_id,
      call_feedback_type = call_feedback.feedback_type,
      feedback_reason_for_db = call_feedback.feedback_reason.map(ev => ev.toString).mkString(","),
      user_comment = call_feedback.user_comment.getOrElse("")
    ) match {

      case Failure(err) =>

        Left(CallQualityFeedBackError.SqlExceptionError(err))


      case Success(None) =>

        Left(CallQualityFeedBackError.NoneReceivedWhileSavingFeedback)


      case Success(Some(response)) =>

        Right(response)

    }

  }

  def addNewCallLog(newCallLog: NewConfCallLog, teamId: TeamId, accountId: AccountId)(using logger: SRLogger): Try[CallLog] = {
    val conference_uuid: ConferenceUuid = ConferenceUuid(conf_uuid = srUuidUtils.generateConferenceUuid())

    val conf_details = ConferenceDetailsFromWebhook(
      conference_sid = newCallLog.conference_sid.map(conf_sid => conf_sid.sid),
      conference_name = newCallLog.conference_name.map(conf_name => conf_name.name),
      initiated_by = newCallLog.from_phone_number,
      service_provider = newCallLog.service_provider,
      callType = CallType.NATIVE_OUTGOING
    )


    val initiator: ParticipantDetailsFromWebhook = ParticipantDetailsFromWebhook(
      call_from = newCallLog.from_phone_number,
      participant_label = "initiator",
      call_participant_phone = newCallLog.from_phone_number,
      is_prospect = false,
      call_sid = None)

    val customer: ParticipantDetailsFromWebhook = ParticipantDetailsFromWebhook(
      call_from = newCallLog.from_phone_number,
      participant_label = "customer",
      call_participant_phone = newCallLog.to_phone_number,
      is_prospect = true,
      call_sid = None)


    val taskUuidAndProspectID = TaskUuidAndProspectID(
      task_uuid = newCallLog.task_uuid.uuid,
      prospect_id = newCallLog.primary_prospect_id
    )

    for {
      call_setting_data: CallAccountSettings <- callDAO.getCallSettingDataFromCallerIdentity(
        phone_uuid = newCallLog.call_setting_uuid
      ) match {
        case Failure(exception) => Failure(exception)
        case Success(None) => Failure(new Exception("Caller account not found"))
        case Success(Some(value)) => Success(value)
      }

      conferenceCallLog: CallLog <- callDAO.saveConferenceDetails(
        conference_uuid = conference_uuid,
        conference_details = conf_details,
        taskUuidAndProspectID = Some(taskUuidAndProspectID),
        is_incoming = false,
        team_id = teamId,
        prospect_details_for_incoming_call = None
      )

      _: Seq[ParticipantDetailsToNotify] <- saveParticipantDetails(
        call_participant_mode = InitialCallParticipationModeData.InitiatorModeData,
        conference_uuid = conference_uuid,
        taskUuidAndProspectID = Some(taskUuidAndProspectID),
        participantDetails = Seq(
          initiator, customer
        ),
        teamId = teamId,
        accountId = accountId,
        calling_device = Some(CallingDevice.MOBILE), // This path is specifically used via the mobile app
        call_setting_uuid = Some(newCallLog.call_setting_uuid.phone_number_uuid)
      )

      _: List[Long] <- addingProspectEvent(
        taskUuidAndProspectID = Some(taskUuidAndProspectID),
        get_call_account_settings = call_setting_data,
        event_type = EventType.CALL_PLACED,
        conference_uuid
      ) match {
        case Left(err) => Failure(err.err)
        case Right(value) => Success(value)
      }

    } yield {
      conferenceCallLog
    }


  }

  def updateCallLog(
                     callUUID: ConferenceUuid,
                     callStatus: CallStatus,
                     completedAt: DateTime,
                     pickedUpAt: Option[DateTime],
                     teamId: TeamId
                   )(using logger: SRLogger): Either[SaveLogError, ParticipantCallUpdateLog] = {
    

      val result = for {
        call_uuid: ConferenceCallUpdateLog <- callDAO.updateConferenceCallLog(
          conf_uuid = callUUID,
          completedAt = completedAt,
          callStatus = callStatus,
          teamId = teamId
        )
        call_update_log: ParticipantCallUpdateLog <- callDAO.addNativeCallParticipantLog(
          completed_at = completedAt,
          picked_up_at = pickedUpAt,
          callStatus = callStatus,
          call_conference_uuid = callUUID,
          team_id = teamId
        )
      } yield {
        call_update_log
      }
        result.toEither.left.map(
      err => {
        logger.error(s"[updateCallLogError]: ${err.getMessage}")
        SaveLogError.SQlExceptionError(err)
      }
    )

  }

  def callQualityFeedback(
                           call_sid: CallSID,
                           team_id: TeamId,
                           call_feedback: CallFeedBack,
                           org_id: OrgId
                         )(using logger: SRLogger): Either[CallQualityFeedBackError, CallFeedBackResponse] = {


    for {

      // SubAccountDetails
      get_org_sub_account_details <- getSubAccountDetails(
        sub_account_uuid = None,
        org_id = org_id
      ).toEither.left.map(err => CallQualityFeedBackError.ErrorWhileGettingSubAccount(err))

      // CallFeedBackResponse
      save_feedback_data <- saveCallFeedBack(
        call_feedback = call_feedback,
        team_id = team_id,
        call_sid = call_sid
      )

      // TwlAccountSid
      tell_twilio_about_feedback <- twilioDialerService.twilioCallQualityFeedback(
        call_score = CallService.getCallScoreForFeedback(feedback_type = call_feedback.feedback_type),
        call_issues = call_feedback.feedback_reason,
        call_sid = call_sid,
        authToken = get_org_sub_account_details.sub_auth_token,
        accountSid = get_org_sub_account_details.sub_account_id
      ).toEither.left.map(err => CallQualityFeedBackError.ErrorWhileSendingFeedbackToTwilio(err))

    } yield {

      save_feedback_data

    }

    /*
        1. save call feedback in db
        2. if possible provide feedback about call to twilio
        3. send reponse back


     */


  }

  def checkIfLowCredit(
                        org_id: OrgId,
                        current_credits: Long,
                        sub_account_details: SubAccountDetails
                      )(using logger: SRLogger, ws: WSClient, ec: ExecutionContext): Either[UpdateSubAccountCreditError, Boolean] = {

    for {

      // Int
      org_calling_accounts <- getTotalCallAccountCountForOrg(org_id = org_id).toEither
        .left.map(err => UpdateSubAccountCreditError.ErrorWhileFetchingTotalCallingAccount(err))

      // Boolean
      is_low_credit <- {

        /*
         1 - june - 2024:

           current_credits -> in cents

           We are now checking if current_credits are equal to or more than 25 Dollars we don 't show low
           calling account balance.

           If not we will check that the user should have at least 2 dollars per calling account.
           */

        // 2 dollars is the threshold credit per number, multiplied by 100, as that's how we store it in the database.
        val isLowCredit: Boolean = if (current_credits >= 25 * 100) {

          false

        } else if (current_credits < org_calling_accounts * 2 * 100) {

          true

        } else {

          false

        }

        Right(isLowCredit)

      }

      // String
      add_warning_message_in_org_calling_credits_table <- {

        if (is_low_credit) {

          val warningMessage = s"Your calling balance is low. Add $$${Math.max(0, org_calling_accounts * 2 - current_credits / 100)} or more to maintain at least $$2 per number for smooth calling."
          addWarningMessageInOrgCallingCredit(
            org_id = org_id,
            org_call_warning_details = OrgCallWarningErrorDetails(
              warning_message = warningMessage,
              warning_at = DateTime.now(),
              warning_code = AccountWarningCodeType.LowCallingCredit
            ),
            call_credit_remaining = Some(current_credits),
            ws = ws,
            ec = ec,
            subAccountDetails = sub_account_details
          ).left.map(err => UpdateSubAccountCreditError.ErrorWhileAddingWarning(err))

        } else {
          Right("")
        }

      }

    } yield {

      is_low_credit

    }


  }

  def resetCache(accountId: AccountId)(
    implicit logger: SRLogger
  ): Unit = {
    resetCacheUtil._resetTeamCache(
      aid = accountId.id
    )
  }

  def triggerMigration()(using logger: SRLogger): Either[Object, Seq[OrgId]] = {

    for {

      get_all_orgs_from_org_calling_credit <- callDAO.fetchOrgIdsFromOrgCallingCredits().toEither

      get_sub_account_details <- {

        Helpers.seqEitherToEitherSeq(get_all_orgs_from_org_calling_credit.map(orgId => {

          for {

            //Either[GetSubAccountDetailsError, SubAccountDetails]
            get_sub_account_details <- getSubAccountDetails(
              sub_account_uuid = None,
              org_id = orgId
            ).toEither

            // Either[DeleteUsageTriggerError, Boolean]
            delete_previous_trigger <- deleteUsageTrigger(
              twl_sub_account_sid = get_sub_account_details.sub_account_id,
              twl_sub_account_auth_token = get_sub_account_details.sub_auth_token,
              trigger_id = get_sub_account_details.trigger_id
            )

            create_new_trigger <- {

              val call_credit_added: Long = get_sub_account_details.call_credits_cents / 100
              val already_subtracted: Long = (get_sub_account_details.already_subtracted_cents.get) / 100

              val usage: Long = if (call_credit_added > already_subtracted) {
                call_credit_added
              } else {
                already_subtracted + 1 // adding 1 dollar extra so that trigger can be activated when it reaches 1 dollar
              }

              createNewUsageTrigger(
                org_id = orgId,
                usage_limit = usage.toString,
                twl_sub_account_sid = get_sub_account_details.sub_account_id,
                twl_sub_account_auth_token = get_sub_account_details.sub_auth_token
              )
            }

          } yield {


            logger.info(s"Ran Migration for org ${orgId.id} triggerMigration")
            orgId


          }

        }))
      }

    } yield {

      get_sub_account_details

    }

  }

  def saveCallLogsInDB(
                        call_logs: List[NewCallLog],
                        sub_account_uuid: SubAccountUuid
                      ): Try[List[CallSID]] = {

    callHistoryLogDAO.saveCallLogsForSubAccount(
      sub_account_uuid = sub_account_uuid,
      call_logs = call_logs
    )

  }

  def getRemainingCallingCreditForSubAccount(
                                              sub_account_uuid: SubAccountUuid
                                            ): Try[CallingRemainingCredits] = {

    callDAO.getRemainingCallingCredits(
      sub_account_uuid = sub_account_uuid
    )

  }

  def updateCallLogs(
                      sub_account_uuid: SubAccountUuid
                    )(using logger: SRLogger): Either[UpdateCallLogsError, List[CallSID]] = {

    for {

      // SubAccountDetails
      get_sub_account_details <- getSubACcountDetailsForOrgUsingSubAccountUuid(
        subAccountUuid = sub_account_uuid
      ).toEither.left.map(err => UpdateCallLogsError.ErrorWhileGettingSubAccount(err))

      // List[CallAccountSettings]
      fetch_call_setting_ids_with_team_ids <- callDAO.fetchCallSettingIdAndDetails(
        sub_account_uuid = sub_account_uuid
      ).toEither.left.map(err => UpdateCallLogsError.ErrorWhileFetchingCallAccountSettings(err))

      //ResourceSet[Call]
      fetch_call_logs <- twilioDialerService.fetchCallLogsForSubAccount(
        twl_sub_account_sid = get_sub_account_details.sub_account_id,
        twl_auth_token = get_sub_account_details.sub_auth_token,
        last_call_history_updated_at = get_sub_account_details.last_call_history_updated_at
      ).toEither.left.map(err => UpdateCallLogsError.ErrorWhileFetchingCallLogs(err))

      //List[CallLog]
      attach_team_ids_account_id_to_logs <-TwilioDialerService.addTeamIdToCallLogs(
        calls = fetch_call_logs,
        call_account_settings = fetch_call_setting_ids_with_team_ids,
        sub_account_uuid = sub_account_uuid,
        srUuidUtils = srUuidUtils
      ).toEither.left.map(err => UpdateCallLogsError.ErrorWhileAttachingTeamIdToCallLog(err))

      // List[CallSID]
      save_logs_in_db <- saveCallLogsInDB(
        sub_account_uuid = sub_account_uuid,
        call_logs = attach_team_ids_account_id_to_logs
      ).toEither.left.map(err => UpdateCallLogsError.ErrorWhileSavingCallingLogs(err))

      // Int
      update_last_logs_updated_at <- callDAO.updateCallLogLastFetchedAt(sub_account_uuid = sub_account_uuid).toEither
      .left.map(err => UpdateCallLogsError.ErrorWhileUpdatingCallLogsLastUpdatedAt(err))


    } yield {

      save_logs_in_db

    }

  }

  def getRemainingCallingCredit(
                                 org_id: OrgId
                               )(using logger: SRLogger): Either[GetRemainingCallingCreditError, CallingRemainingCredits] = {

    for {

      // SubAccountDetails
      get_sub_account_detail_for_org <- getSubAccountDetails(
        org_id = org_id,
        sub_account_uuid = None
      ).toEither.left.map(err => GetRemainingCallingCreditError.ErrorWhileFetchingSubAccount(err))

      // CallCreditRemainingInUSD
      fetch_remaining_calling_credit <- getRemainingCallingCreditForSubAccount(
        sub_account_uuid = get_sub_account_detail_for_org.uuid
      ).toEither.left.map(err => GetRemainingCallingCreditError.ErrorWhileFetchingCallingCredit(err))

    } yield {

      fetch_remaining_calling_credit

    }

  }

  def getCallLogForUser(
                         isFirst: Boolean,
                         team_id: TeamId,
                         validatedCallHistoryReqRange: models.ValidateCallHistoryReq.ValidatedCallHistoryReqRange,
                         org_id: OrgId
                       )(
                         implicit logger: SRLogger
                       ): Either[GetCallLogForUserError, CallLogPagination] = {


    for {

      // sub_account_uuid
      sub_account_details <- getSubAccountDetails(
        org_id = org_id, sub_account_uuid = None
      ).toEither.left.map(err => GetCallLogForUserError.ErrorWhileGettignSubAccount(err))

      // List[CallLogFromDB]
      call_logs <- callDAO.getCallLogsForUser(
        sub_account_uuid = sub_account_details.uuid,
        team_id = team_id,
        validatedCallHistoryReqRange = validatedCallHistoryReqRange.copy(pageSize = validatedCallHistoryReqRange.pageSize + 1)
      ).toEither.left.map(err => GetCallLogForUserError.ErrorWhileGettingCallLogs(err))

      //  CallLogPagination
      final_logs <- CallService.generalCallLogPagination(
        isFirst = isFirst,
        validatedCallHistoryReqRange = validatedCallHistoryReqRange,
        call_logs = call_logs
      )
        .toEither.left.map(err => GetCallLogForUserError.ErrorWhileGeneratingCallLogResponse(err))

    } yield {
      final_logs
    }

  }

  def getSumOfRemainingBalanceForAllSubAccounts(): Try[CallingRemainingCredits] = {

    callDAO.getSumOfRemainingBalanceForAllSubAccounts()

  }

  def getAllPhoneNumberToReleaseOfSubAccount(
                                              sub_account_uuid: SubAccountUuid
                                            ): Try[List[PhoneNumberToReleaseData]] = {

    callDAO.getActivePhoneNumbersToReleaseForSubAccount(
      sub_account_uuid = sub_account_uuid
    )

  }

  def releasePhoneNumberFromOrg(
                                 org_id: OrgId
                               )
                               (using logger: SRLogger
                               ): Either[ReleasePhoneNumberForOrgError, List[Boolean]] = {


    for {

      // SubAccountDetails
      get_sub_account_details <- getSubAccountDetails(
        sub_account_uuid = None,
        org_id = org_id
      ).toEither.left.map(err => ReleasePhoneNumberForOrgError.ErrorWhileGettingSubAccount(err))

      // List[PhoneNumberToReleaseData]
      get_phone_numbers_to_release <- getAllPhoneNumberToReleaseOfSubAccount(
        sub_account_uuid = get_sub_account_details.uuid
      ).toEither.left.map(err => ReleasePhoneNumberForOrgError.ErrorWhileGettingPhoneNumbersToDelete(err))

      // List[Boolean]
      release_phone_numbers <- Try(get_phone_numbers_to_release.map(phone_number_data => {

        val deleted_phone_number = for {

          // true
          release_phone_number_from_twilio <- deleteCallNumberFromTwilio(
            phone_sid = phone_number_data.phone_sid,
            sub_account_sid = get_sub_account_details.sub_account_id,
            sub_account_auth_token = get_sub_account_details.sub_auth_token
          )
          // Int
          update_call_account_to_inactive_in_db <- updateCallAccountToInActiveFromDB(
            phone_uuid = phone_number_data.phone_number_uuid,
            orgId = org_id,
            team_id = phone_number_data.team_id
          )

        } yield {

          release_phone_number_from_twilio

        }

        deleted_phone_number match {

          case Left(err) =>

            logger.error(s"Error While deleting a phone number : phone_number_uuid: ${phone_number_data.phone_number_uuid}: err: ${err} ")
            false

          case Right(_) => true

        }

      })).toEither.left.map(err => ReleasePhoneNumberForOrgError.ErrorWhileReleasingPhoneNumbers(err))

    } yield {

      release_phone_numbers

    }


  }

  def getAllOrgIdsToReleasingPhoneNumbers(): Try[List[OrgId]] = {

    // will check the accounts who's trial has expired more than 3 days ago.

    // contains sub-account -- i.e. used calling feature

    callDAO.getTrialExpiredOrgsToReleasePhoneNumbers()

  }


  def releasePhoneNumberOnOrgDeactivation()(using logger: SRLogger): Try[List[true]] = {


    for {

      get_org_id_to_release_numbers_from: List[OrgId] <- getAllOrgIdsToReleasingPhoneNumbers()

    } yield {

      get_org_id_to_release_numbers_from.map(org_id => {

        releasePhoneNumberFromOrg(
          org_id = org_id
        ) match {

          case Left(ReleasePhoneNumberForOrgError.ErrorWhileGettingSubAccount(error)) =>

            logger.error(s"[releasePhoneNumberForTrialExpiredOrgs] Error while fetching sub_account org_id : ${org_id} sql_error:", error)
            true


          case Left(ReleasePhoneNumberForOrgError.ErrorWhileGettingPhoneNumbersToDelete(err)) =>

            logger.error(s"[releasePhoneNumberForTrialExpiredOrg] Error while fetching phone numbers to delete for org-id: ${org_id}", err)
            true


          case Left(ReleasePhoneNumberForOrgError.ErrorWhileReleasingPhoneNumbers(err)) =>

            logger.error(s"[releasePhoneNumberForTrialExpiredOrg] Error while releasing phone numbers for org-id: ${org_id}", err)
            true

          case Right(data) =>

            logger.info(s"[releasePhoneNumberForTrialExpiredOrg] Released following numbers for org_id:${org_id}, count: ${data.length} data: ${data}")
            true


        }

      })

    }

  }

  def getMainAccountRemainingCredits(): Try[CallingRemainingCredits] = {

    twilioDialerService.getMainAccountRemainingCredits()

  }

  def checkIfMainAccountCallingBalanceIsSufficient(): Try[ParentCallingAlertData] = {

    for {

      fetch_sum_of_all_sub_account_remaining_balance: CallingRemainingCredits <- getSumOfRemainingBalanceForAllSubAccounts()


      fetch_main_account_remaining_credits: CallingRemainingCredits <- getMainAccountRemainingCredits()


    } yield {

      ParentCallingAlertData(
        main_account_balance = fetch_main_account_remaining_credits,
        total_sub_account_requirement = fetch_sum_of_all_sub_account_remaining_balance
      )

    }

  }

  def subAccountLevelValidation(
                                 org_id: OrgId,
                                 account_id: AccountId,
                                 team_id: TeamId
                               )(using logger: SRLogger): Either[PhoneNumberValidationError, true] = {


    for {

      // SubAccountDetails
      get_sub_account_details_for_org <- getSubAccountDetails(
        org_id = org_id,
        sub_account_uuid = None
      ).toEither.left.map(err => PhoneNumberValidationError.ErrorWhileFetchingSubAccountDetails(err))

      // true
      validate_remaining_credit <- if (get_sub_account_details_for_org.call_remaining_credit_cents > 0) {
        Right(true)
      } else {
        Left(PhoneNumberValidationError.NotEnoughBalanceToMakeCall)
      }

      // true
      validate_account_status <- if (get_sub_account_details_for_org.status == SubAccountStatus.Active) {
        Right(true)
      } else {
        Left(PhoneNumberValidationError.AccountSuspended)
      }

      // true
      fetch_call_setting_of_the_user <- getCallSettingOfUser(
        teamId = team_id,
        accountId = account_id
      ) match {

        case Failure(err) =>

          logger.error(s"[validatePhoneNumberBeforeCall] error while getting call setting of user account_id_${account_id.id} team_id_${team_id.id} ", err)

          Left(PhoneNumberValidationError.ErrorWhileFetchingCallSettingForUser(err))

        case Success(None) =>

          logger.error(s"[validatePhoneNumberBeforeCall] error while getting call setting of user account_id_${account_id.id} team_id_${team_id.id} ")

          Left(PhoneNumberValidationError.BuyNumberFirst)

        case Success(Some(setting)) =>

          if (setting.service_provider != CallingServiceProvider.TWILIO) {

            logger.error(s"[validatePhoneNumberBeforeCall] twilio account not created for this calling account : user account_id_${account_id.id} team_id_${team_id.id} ")
            Left(PhoneNumberValidationError.TwilioPhoneAccountNotCreated)

          } else {

            Right(true)
          }

      }

    } yield {

      true

    }

  }

  // Function to convert any phone number to E.164 format
  def convertToE164(phoneNumber: PhoneNumberValueClass, defaultRegion: String): Try[PhoneNumberValueClass] = Try {
    val phoneUtil = PhoneNumberUtil.getInstance()


    // Parse the phone number based on the input string and default country/region code
    val parsedNumber = phoneUtil.parse(phoneNumber.phone_number, defaultRegion)

    // Format the number in E.164 format
    val e164Format = phoneUtil.format(parsedNumber, PhoneNumberFormat.E164)

    // Return the formatted number
    PhoneNumberValueClass(e164Format)
  }


  def getCountryCodeFromTimezone(timezone: String): Try[Option[String]] = Try {

    val timeZone: TimeZone = TimeZone.getTimeZone(ZoneId.of(timezone))

    val region: Option[String] = TimezoneToCountryCodeList.timezoneToCountryMap.get(timezone)
    match {

      case None => None

      case Some(value) => Some(value.head)
    }

    region

  }

  def getCountryCodeFromName(countryName: String): Try[Option[String]] = Try {

    val countryCode = CountryCode.findByName(countryName).asScala


    // Check if there is a valid country code match
    countryCode.headOption match {
      case Some(code) => Some(code.getAlpha2)
      case None => None
    }
  }

  def validatePhoneFormatData(data: PhoneNumberFormatData): PhoneNumberFormatData = {
    val prospect_tz = data.prospect_tz match {

      case None => None

      case Some(value) => if (value.trim.nonEmpty) {
        Some(value)
      } else {
        None
      }
    }

    val prospect_country = data.prospect_country match {
      case None => None

      case Some(value) => if (value.trim.nonEmpty) {
        Some(value)
      } else {
        None
      }
    }

    PhoneNumberFormatData(
      prospect_tz = prospect_tz,
      prospect_country = prospect_country,
      phone_numbers = data.phone_numbers,
      phone_number = data.phone_number,
      user_tz = data.user_tz
    )

  }

  def getRegionCodeFromPhoneNumber(
                                    phoneNumber: PhoneNumberValueClass
                                  ): Try[(PhoneNumberValueClass, String)] = Try {
    val phoneUtil = PhoneNumberUtil.getInstance()

    val parsedNumber = phoneUtil.parse(phoneNumber.phone_number, "")
    // Get the region (ISO country code) for the parsed number
    val regionCode: String = phoneUtil.getRegionCodeForNumber(parsedNumber)

    (phoneNumber, regionCode)

  }

  def getFormattedPhoneNumber(
                               data: PhoneNumberFormatData,
                               tid: TeamId
                             )(using logger: SRLogger): Try[Seq[PhoneNumberValueClass]] = {

    val validated_data = validatePhoneFormatData(data = data)

    val phone_numbers: Seq[PhoneNumberValueClass] = if (validated_data.phone_numbers.nonEmpty) validated_data.phone_numbers
    else if (validated_data.phone_number.isDefined) Seq(validated_data.phone_number.get)
    else Seq()

    if(phone_numbers.nonEmpty) {

      if (phone_numbers.forall(_.phone_number.startsWith("+"))) {

      val phoneCountryCode: Seq[Try[(PhoneNumberValueClass, String)]] = phone_numbers
        .map(getRegionCodeFromPhoneNumber)

      Helpers.seqTryToTrySeq(phoneCountryCode) match {
        case Failure(exception) => Failure(exception = exception)

        case Success(phoneRegCodeMap) =>
          val validatedPhones = phoneRegCodeMap.map(phoneCode => {
            convertToE164(
              phoneNumber = phoneCode._1,
              defaultRegion = phoneCode._2
            )
          })

          Helpers.seqTryToTrySeq(validatedPhones)

      }

    } else {

      val countryCode: Try[Option[String]] = validated_data.prospect_tz match {

        case Some(tz) => getCountryCodeFromTimezone(tz)

        case None => validated_data.prospect_country match {

          case Some(country) => getCountryCodeFromName(country)

          case None => getCountryCodeFromTimezone(validated_data.user_tz)

        }
      }


      countryCode match {

        case Failure(exception) => Failure(exception)

        case Success(countryCodeOpt) => countryCodeOpt match {

          case None => Failure(new Exception("there was an error while fetching country code"))

          case Some(code) =>
            val validatedPhones: Seq[Try[PhoneNumberValueClass]] = phone_numbers
              .map(ph => convertToE164(
                phoneNumber = ph,
                defaultRegion = code
              ))

            Helpers.seqTryToTrySeq(validatedPhones)

        }

      }

    }
    } else {
      Success(Seq())
    }

  }


  def validatePhoneNumberBeforeCall(
                                     phone_number: PhoneNumberValueClass,
                                     org_id: OrgId,
                                     team_id: TeamId,
                                     account_id: AccountId
                                   )(using logger: SRLogger): Either[PhoneNumberValidationError, true] = {

    for {

      // true
      basic_validation <- validatePhoneNumberBeforeCallCompanion(
        phone_number = phone_number,
        call_util = callUtils
      )

      // true
      account_level_validation <- subAccountLevelValidation(
        org_id = org_id,
        team_id = team_id,
        account_id = account_id
      )

    } yield {

      true

    }


  }

}























































object CallService {

  def validateSubAccountUuid(sub_account_uuid: Option[Vector[String]]): Try[SubAccountUuid] = {
    sub_account_uuid match {
      case Some(uuid) =>
        if (uuid.nonEmpty)
          Success(SubAccountUuid(uuid = uuid.head))
        else
          Failure(new Exception("No sub_account_id found"))

      case None => Failure(new Exception("No sub_account_id found"))
    }
  }

  def validateParamsForCallLog(
                                parsedParams: Map[String, Vector[String]]
                              ): Try[ValidatedParamsForCallLogs] = {

    for {
      (is_first: Boolean, range: InferredQueryTimeline.Range) <- Helpers.getTimeLineRange(parsedParams = parsedParams)

    } yield {

      ValidatedParamsForCallLogs(
        isFirst = is_first,
        timelineRange = ValidateCallHistoryReq.ValidatedCallHistoryReqRange(
          timeline = range,
          pageSize = AppConfig.Calling.call_logs_per_page
        )
      )

    }


  }


  def generalCallLogPagination(
                                isFirst: Boolean,
                                validatedCallHistoryReqRange: ValidateCallHistoryReq.ValidatedCallHistoryReqRange,
                                call_logs: List[CallLogFromDB]
                              ): Try[CallLogPagination] = Try {


    val sortedCallLogs: List[CallLogFromDB] = call_logs.sortBy(_.start_time)(JodaTimeUtils.dateTimeOrdering)
      .reverse

    val limit: Int = validatedCallHistoryReqRange.pageSize
    val hasMore: Boolean = sortedCallLogs.length >= limit + 1

    if (!hasMore && call_logs.nonEmpty) {

      validatedCallHistoryReqRange.timeline match {
        case InferredQueryTimeline.Range.Before(dateTime) =>
          val prev = if (isFirst) {
            None
          } else {
            Some(sortedCallLogs.head.start_time)
          } // same order of call logs maintained ( descending order )

          CallLogPagination(
            data = sortedCallLogs, links = NavigationLinks(
              prev = prev,
              next = None
            )
          )

        case InferredQueryTimeline.Range.After(dateTime) =>
          val next = if (isFirst) {
            None
          } else {
            Some(sortedCallLogs.last.start_time)
          } // This time call logs will be reversed ( descending order )

          CallLogPagination(
            data = sortedCallLogs, links = NavigationLinks(
              prev = None,
              next = next
            )
          )
      }

    } else if (!hasMore && call_logs.isEmpty) {

      CallLogPagination(
        data = List(), links = NavigationLinks(
          prev = None,
          next = None
        )
      )

    } else {

      validatedCallHistoryReqRange.timeline match {

        case InferredQueryTimeline.Range.Before(dateTime) =>

          val penUltimate = sortedCallLogs.dropRight(1).last
          val prev = if (isFirst) {
            None
          } else {
            Some(sortedCallLogs.head.start_time)
          }

          CallLogPagination(
            data = sortedCallLogs.dropRight(1),
            links = NavigationLinks(
              prev = prev,
              next = Some(penUltimate.start_time)
            ))


        case InferredQueryTimeline.Range.After(dateTime) =>

          val final_tasks = sortedCallLogs.drop(1)
          val prev = if (isFirst) {
            None
          } else {
            Some(final_tasks.head.start_time)
          }
          val next = Some(final_tasks.last.start_time)

          CallLogPagination(data = final_tasks,
            links = NavigationLinks(
              prev = prev,
              next = next
            ))

      }
    }
  }

  def is24hrPassed(
                    time: DateTime
                  ): Boolean = {

    time.isBefore(DateTime.now().minusDays(1))

  }

  def checkIfOverUsagePaid(
                            sub_account_details: SubAccountDetails,
                            credit_detail: AddCallingCreditDetails
                          ): Either[AddCallingCreditsError, Long] = {

    sub_account_details.twl_trigger_current_value_cents match {

      case None =>

        Left(AddCallingCreditsError.TwilioTriggerCurrentValueNotPresetError)


      case Some(trigger_current_value_cents) =>

        val over_usage = trigger_current_value_cents - sub_account_details.call_credits_cents

        if (over_usage < credit_detail.credit_added_cents) {

          Right(over_usage)

        } else {

          Left(AddCallingCreditsError.LessCreditAddedError(
            over_usage = over_usage,
            credit_added = credit_detail.credit_added_cents
          ))
        }


    }

  }

  // Fixme call fix this function more accurately
  def getCallScoreForFeedback(
                               feedback_type: CallFeedBackType
                             ): Int = {

    feedback_type match {

      case CallFeedBackType.Negative =>
        2

      case CallFeedBackType.Positive =>

        5

    }

  }

  /**
   * @param previously_deducted_cents  : already_deducted_credit, so we don't need to deduct it again
   * @param usage_till_now_cents       : Total_usage_till now
   * @param current_credits_left_cents : Currently how much credit is left
   * @return new_remaining_credits
   */

  def calculateCredit(
                       previously_deducted_cents: Long,
                       usage_till_now_cents: Long, // Total_usage_till_date
                       current_credits_left_cents: Long // current_credits_left
                     ): RemainingCallingCreditDetails = {

    val remaining_to_subtract: Long = usage_till_now_cents - previously_deducted_cents
    val new_remaining_credits: Long = current_credits_left_cents - remaining_to_subtract

    RemainingCallingCreditDetails(
      updated_credit_for_org_cents = new_remaining_credits,
      current_deducted_credit_cents = remaining_to_subtract,
      current_already_subtracted_credit_cents = remaining_to_subtract + previously_deducted_cents
    )
  }

  def getParsedAttributesFromBody(
                                   body: Option[Map[String, Seq[String]]]
                                 ): Try[TwilioUsageLimitCrossedDetails] = Try {

    val account_sid: String = Try {
      body.get("AccountSid").head
    } match {
      case Failure(err) =>

        throw new Exception("AccountSid not present")


      case Success(value) =>

        value
    }

    val usage_trigger_sid: String = Try {
      body.get("UsageTriggerSid").head
    } match {
      case Failure(err) =>

        throw new Exception("UsageTriggerSid not present")

      case Success(value) =>

        value
    }

    //    val date_fired: DateTime = body.get("DateFired").headOption match {
    //      case None =>
    //
    //        throw new Exception("date_fired not present")
    //
    //      case Some(value) =>
    //
    //        DateTime.parse(value)
    //
    //    }

    val trigger_value: Long = Try {
      body.get("TriggerValue").head
    } match {
      case Failure(err) =>
        throw new Exception("TriggerValue not present")

      case Success(value) =>

        TwilioDialerService.parseCurrencyInCents(value) match {
          case Failure(e) =>
            throw e
          case Success(data) =>

            data
        }

    }

    val current_value: Long = Try {
      body.get("CurrentValue").head
    } match {
      case Failure(err) =>
        throw new Exception("CurrentValue not present")

      case Success(value) =>
        TwilioDialerService.parseCurrencyInCents(value) match {
          case Failure(e) =>
            throw e

          case Success(data) =>
            data
        }

    }

    val idempotency_token: String = Try {
      body.get("IdempotencyToken").head
    } match {
      case Failure(err) =>
        throw new Exception("IdempotencyToken not present")

      case Success(value) =>
        value


    }

    //    val recurring: String = body.get("Recurring").headOption match {
    //      case None =>
    //        throw new Exception("Recurring not present")
    //
    //      case Some(value) =>
    //        value
    //
    //    }


    TwilioUsageLimitCrossedDetails(
      twl_sub_account_sid = TwlSubAccountSid(
        id = account_sid
      ),
      twl_usage_trigger_sid = usage_trigger_sid, // Fixme use uuid
      date_fired = DateTime.now(), // Didn't received date_fired in webhook event so, manually adding it
      trigger_value = trigger_value,
      current_value = current_value,
      idempotency_token = idempotency_token,
    )
  }


  def createHashMapOfCallSettingAndTeamId(
                                           call_account_settings: List[CallAccountSettings]
                                         ): Map[String, TeamIdAndAccountDetails] = {

    var map: Map[String, TeamIdAndAccountDetails] = Map()

    call_account_settings.foreach(setting => {

      val team_id_and_account_details: TeamIdAndAccountDetails = TeamIdAndAccountDetails(
        team_id = setting.team_id,
        account_id = setting.owner_account_id
      )

      if (setting.phone_number.isDefined) {
        map = map ++ Map("client:" + setting.uuid.phone_number_uuid -> team_id_and_account_details,
          setting.phone_number.get.phone_number -> team_id_and_account_details
        )
      }

      if (setting.forward_to.isDefined) {

        map = map ++ Map(setting.forward_to.get -> team_id_and_account_details)

      }

    })

    map

  }

  def validatePhoneNumberBeforeCallCompanion(
                                              phone_number: PhoneNumberValueClass,
                                              call_util: CallUtils
                                            ): Either[PhoneNumberValidationError, true] = {

    for {

      // true
      basic_validations <- Try {
        val check = isValidPhoneNumber(
          phone_number.phone_number
        )

        println("")

        if (check) {
          check
        } else {
          throw new Exception("Number not in correct format")
        }
      }.toEither.left.map(err => PhoneNumberValidationError.BasicErrorValidationFailed(err))

      // PhoneNumber
      parse_number_to_get_country_code <- parseNumber(
        phoneNumberStr = phone_number.phone_number
      ).toEither.left.map(err => PhoneNumberValidationError.ErrorWhileValidating(err))

      // true
      /*

      18-Feb-2025:
      One customer was facing issue when calling to Jordon it was allowed in twilio but wasn't allowed here.
      so country blocked checked removed as this is maintained on twilio side, we can come back and check this if we want to
      implement this check on smartreach side. 

      check_if_country_not_blocked <- Try {

        val is_call_allowed_in_called_country = call_util.allowed_countries_list
          .find(
            country => country.country_phone_code == (
              "+" +
                parse_number_to_get_country_code.getCountryCode
                  .toString
              )
          )

        if (is_call_allowed_in_called_country.isDefined) {
          true
        } else {
          throw new Exception("Call not allowed in this country")
        }

      }.toEither.left.map(err => PhoneNumberValidationError.CountryNotAllowed(err.getMessage))
      
       */

    } yield {

      true

    }

  }


}
