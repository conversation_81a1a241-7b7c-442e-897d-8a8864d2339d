package api.call.DAO

import api_layer_models.CurrencyType
import api_layer_models.ConvertedCurrency.convertCentToDollar
import api.AppConfig
import api.AppConfig.Calling
import api.AppConfig.Calling.{call_history_logs_interval_hours, release_phone_number_when_trial_expires_after_interval_days}
import io.sr.billing_common.models.PlanType
import api.accounts.{AccountWarningCodeType, TeamId}
import api.call.models.{AddCallingCreditDetails, CallAccountSettings, CallDetails, CallFeedBack, CallFeedBackResponse, CallFeedBackType, CallNote, CallParticipantUuid, CallParticipationMode, CallSID, CallSidOrConfUuid, CallStatus, CallType, CallerIdDetails, CallerIdUuid, CallerIdVerificationStatus, CallerIdentity, CallingDevice, CallingRemainingCredits, CallingServiceProvider, ConfSidOrConfUuid, ConferenceFriendlyName, ConferenceSid, ConferenceUuid, DoerAccountName, InitialCallParticipationModeData, NewSubAccountDetails, OngoingCallParticipantNotificationObject, OngoingCallProspectDetails, OutgoingCallerIdSid, ParticipantCallStatus, ParticipantDetailsToNotify, PhoneNumber, PhoneNumberToReleaseData, PhoneNumberUuid, PhoneSID, PhoneType, RecordingData, StatusCallbackData, SubAccountDetails, SubAccountDetailsForUpdate, SubAccountStatus, SubAccountUuid, TaskUuidAndProspectID, TwilioUsageLimitCrossedDetails, TwlAuthToken, TwlSubAccountSid, UpdateRecordCallForm, VerifiedCallerIds, VerifyCallerIdStatusCallbackData}
import api.call.controller.{CreateOrUpdateCallAccountData, ValidateCallerIdData}
import api.accounts.models.AccountId
import api.accounts.models.{AccountId, OrgId}
import api.call.DAO.CallDAO.{getCallerIdWhereClause, getCompletedAt, getParticipantCompletedAt, getWhereClauseConfSidOrConfUuid, getWhereClauseForNotification, getWhereClauseParticipantStatusUpdate, getWhereClauseReplySentimentUpdate}
import api.call.models.CallLogResource.{CallLogFromDB, CallLogUUID, NewCallLog}
import api.call.models.ValidateCallHistoryReq.ValidatedCallHistoryReqRange
import api.campaigns.models.VoicemailId
import api.notes.models.{GetNotesForm, NoteId}
import api.prospects.dao.ProspectIdAndPotentialDuplicateProspectId
import api.prospects.{InferredQueryTimeline, ProspectNotesForIncomingCall}
import api.prospects.models.ProspectId
import api.tasks.models.{TaskCreatedVia, TaskStatusType}
import api.tasks.services.TaskUuid
import api.team_inbox.service.ReplySentimentUuid
import api.twilio.service.{ApiKey, ApiSecret, CallLog, ConferenceCallUpdateLog, ConferenceDetailsFromWebhook, ParticipantCallUpdateLog, ParticipantDetailsFromWebhook, SubAccountApiKeyAndSecret, SubAccountTwimlApplication, TwilioSubAccountDetails, TwilioUsageTrigger, TwlTwimlAppName, TwlTwimlAppSid}
import com.twilio.rest.api.v2010.account.IncomingPhoneNumber
import org.joda.time.DateTime
import scalikejdbc._
import scalikejdbc.interpolation.SQLSyntax
import scalikejdbc.jodatime.JodaWrappedResultSet._
import sr_scheduler.models.{CallSettingCreateSchedule, ChannelDataForScheduling}
import sr_scheduler.models.ChannelDataForScheduling.CallChannelDataForScheduling
import utils.mq.channel_scheduler.channels.ChannelId
import utils.security.EncryptionHelpers
import utils.{SRLogger, SrLoggerTrait}

import scala.util.Try


case class RemainingParticipantsDetails(
                                       latest_participation_mode: CallParticipationMode,
                                       participant_label: String
                                       )

case class ProspectDetailsForIncomingCalls(
                                         prospectId: Option[ProspectId],
                                         prospectNotes: Option[ProspectNotesForIncomingCall]
                                       )


class CallDAO {

  def addNumberToDB(teamId: TeamId, accountId: AccountId, data: CreateOrUpdateCallAccountData, phone_uuid: PhoneNumberUuid, phone_number: PhoneNumber, phone_number_sid: PhoneSID,  callingServiceProvider: CallingServiceProvider, org_sub_account_uuid: SubAccountUuid, phone_number_cost_cents: Double,
                    price_unit: String): Try[PhoneNumber] = Try {

    /*FIXME CALL:
    1. PASS LIMIT FROM APP.CONFIG (Default) and frontend (pass as parameter)
    4. REMOVE HARDCODED VALUES
    */
    DB.autoCommit { implicit session =>
        sql"""
           INSERT INTO call_settings(
             uuid,
             first_name,
             last_name,
             owner_account_id,
             team_id,
             phone_number,
             call_limit_per_day,
             phone_sid,
             phone_type,
             service_provider,
             country,
             enable_forwarding,
             forward_to,
             record_call,
             org_sub_account_uuid,
             price,
             price_unit
           ) VALUES (
             ${phone_uuid.phone_number_uuid},
             ${data.first_name},
             ${data.last_name},
             ${accountId.id},
             ${teamId.id},
             ${phone_number.phone_number},
             ${data.call_limit},
             ${phone_number_sid.phone_sid},
             ${data.phone_type.toString},
             ${callingServiceProvider.toString},
             ${data.country_code},
             ${data.enable_forward},
             ${data.forward_number.map(_.phone_number)},
             ${data.enable_call_recording},
             ${org_sub_account_uuid.uuid},
             ${phone_number_cost_cents},
             ${price_unit}
           )
           RETURNING phone_number;
          """
          .map(rs =>
             PhoneNumber( phone_number = rs.string("phone_number")))
          .single
          .apply()
          .get
    }

  }

  def addNativeCallingNumber(teamId: TeamId, firstName: String, lastName: String, accountId: AccountId, phone_uuid: PhoneNumberUuid, call_limit_per_day: Int): Try[PhoneNumberUuid] = Try {
    DB.readOnly {
      implicit session =>
        sql"""
         INSERT INTO call_settings (
                uuid,
                first_name,
                last_name,
                owner_account_id,
                team_id,
                service_provider,
                call_limit_per_day)
        VALUES (
        ${phone_uuid.phone_number_uuid},
        ${firstName},
        ${lastName},
        ${accountId.id},
        ${teamId.id},
        ${CallingServiceProvider.NativeCalling.toString},
        ${call_limit_per_day}
        ) RETURNING uuid;
         """.map(rs => PhoneNumberUuid(phone_number_uuid = rs.string("uuid")))
          .single
          .apply()
          .get
    }
  }

  def upgradeCallingSettings(teamId: TeamId, data: CreateOrUpdateCallAccountData, phone_uuid: PhoneNumberUuid, phone_number: PhoneNumber, phone_number_sid: PhoneSID,callingServiceProvider: CallingServiceProvider, org_sub_account_uuid: SubAccountUuid): Try[PhoneNumber] = Try {

    DB.autoCommit { implicit session =>
      sql"""
            UPDATE call_settings
            SET
              first_name = ${data.first_name},
              last_name = ${data.last_name},
              phone_number = ${phone_number.phone_number},
              call_limit_per_day = ${data.call_limit},
              phone_sid = ${phone_number_sid.phone_sid},
              phone_type = ${data.phone_type.toString},
              service_provider = ${callingServiceProvider.toString},
              country = ${data.country_code},
              enable_forwarding = ${data.enable_forward},
              forward_to = ${data.forward_number.map(_.phone_number)},
              org_sub_account_uuid = ${org_sub_account_uuid.uuid}
            WHERE uuid = ${phone_uuid.phone_number_uuid}
              AND team_id = ${teamId.id}
            RETURNING phone_number;
            """
        .map(rs =>
          PhoneNumber(phone_number = rs.string("phone_number")))
        .single
        .apply()
        .get
    }

  }

  def getTotalVerificationAttempts(caller_id: PhoneNumber, teamId: TeamId): Try[Option[Int]] = Try{
    DB.readOnly { implicit session =>

      sql"""
            SELECT
            attempts
            FROM
             twl_caller_ids
             WHERE
             team_id = ${teamId.id}
             AND
             phone_number = ${caller_id.phone_number}
          """
        .map(rs => {
          rs.int("attempts")

        })
        .single
        .apply()

    }
  }

  def checkCallerIdAssociation(
                                caller_id_uuid: CallerIdUuid,
                                teamId: TeamId
                              ): Try[Boolean] = Try{
    DB.readOnly { implicit session =>

      sql"""
           SELECT
            EXISTS(
            SELECT
              uuid
            FROM call_settings
            WHERE
              caller_id = ${caller_id_uuid.uuid}
             AND
              is_active
             AND
              team_id = ${teamId.id}
            ) as exists
         """
        .map(rs =>
          rs.boolean("exists")
        )
        .single
        .apply()
        .get

    }

  }

  def getCallerIdDetailsFromCallerIdUuid(caller_id_uuid: CallerIdUuid, teamId: TeamId): Try[CallerIdDetails] = Try {
    DB.readOnly { implicit session =>

      sql"""
            SELECT
            twl_sp_sid,
            phone_number
            FROM
             twl_caller_ids
             WHERE
             team_id = ${teamId.id}
             AND
             uuid = ${caller_id_uuid.uuid};
          """
        .map(rs =>
          CallerIdDetails(
            outgoingCallerIdSid = OutgoingCallerIdSid(sid = rs.string("twl_sp_sid")),
            phoneNumber = PhoneNumber(phone_number = rs.string("phone_number"))
          )

        )
        .single
        .apply()
        .get
    }
  }


  def getCallerIdFromCallerIdUuid(caller_id_uuid: CallerIdUuid, teamId: TeamId): Try[PhoneNumber] = Try {
    DB.readOnly { implicit session =>

      sql"""
            SELECT
            phone_number
            FROM
             twl_caller_ids
             WHERE
             team_id = ${teamId.id}
             AND
             uuid = ${caller_id_uuid.uuid};
          """
        .map(rs =>
          PhoneNumber(phone_number = rs.string("phone_number"))
        )
        .single
        .apply()
        .get
    }
  }


  def getVerifiedCallerIds(
                            status: Option[CallerIdVerificationStatus],
                            teamId: TeamId): Try[List[VerifiedCallerIds]] = Try {
    DB.readOnly { implicit session =>

      val whereClause = getCallerIdWhereClause( status = status)

      sql"""
            SELECT
            uuid,
            phone_number,
            friendly_name,
            added_by,
            status,
            team_id
            FROM
             twl_caller_ids
             WHERE
             team_id = ${teamId.id}
             AND
              ${whereClause}
          """
        .map(rs =>
          CallDAO.verifiedCallerIdsFromDB(rs = rs)
        )
        .list
        .apply()
    }
  }

  def getCallAccountSettings(teamId: TeamId, permittedAccountIds: Seq[Long]): Try[List[CallAccountSettings]] = Try {
    DB.readOnly { implicit session =>

      CallDAO.getCallAccountSettingsQuery(
        where_clause =
          sqls"""
            WHERE
               team_id = ${teamId.id}
               AND
               owner_account_id in ($permittedAccountIds)
           """
      )
        .map(rs =>
          CallDAO.callAccountSettingsFromDB(rs = rs)
        )
        .list
        .apply()
    }
  }

  def getTotalCallAccountsCountInOrg(org_id: OrgId): Try[Int] = Try {
    DB.readOnly { implicit session =>
      sql"""
           SELECT
              COUNT(*) as count
           FROM call_settings cs
           INNER JOIN org_calling_credits occ ON ( occ.sub_account_uuid = cs.org_sub_account_uuid AND org_id = ${org_id.id})
           WHERE
              occ.org_id = ${org_id.id}
              AND is_active
         """
        .map(rs =>
          rs.int("count")
        )
        .single
        .apply()
        .getOrElse(0)
    }
  }


  def updateVoicemailIdInConferenceLogs(
                                       voicemail_id: VoicemailId,
                                       task_id: TaskUuid,
                                       team_id: TeamId,
                                       callSid: CallSID
                                       ): Try[ConferenceUuid] = Try{

    DB.autoCommit{ implicit session =>

      sql"""
           UPDATE
            call_conference_logs
           SET
            voicemail_id = ${voicemail_id.id}
           WHERE
            team_id = ${team_id.id}
            AND
              task_uuid = ${task_id.uuid}
            AND
             conference_uuid = (
              SELECT call_conference_uuid from call_participants_logs
              WHERE
               call_sp_sid = ${callSid.sid}
              AND
               team_id = ${team_id.id}
              )

             RETURNING
             conference_uuid;
         """
        .map(rs => ConferenceUuid(rs.string("conference_uuid")))
        .single
        .apply()
        .get
    }

  }


  def updateVoicemailIdInTasks(
                                         voicemail_id: VoicemailId,
                                         task_id: TaskUuid,
                                         team_id: TeamId
                                       ): Try[TaskUuid] = Try {

    DB.autoCommit { implicit session =>

      sql"""
               UPDATE
                tasks
               SET
                voicemail_id = ${voicemail_id.id},
                updated_at = ${DateTime.now()}
               WHERE
                team_id = ${team_id.id}
                AND
                  task_id = ${task_id.uuid}
                 RETURNING
                 task_id;
             """
        .map(rs => TaskUuid(rs.string("task_id")))
        .single
        .apply()
        .get
    }

  }

  def updateCallAccountSettings(data: CreateOrUpdateCallAccountData,
                                    uuid:PhoneNumberUuid,
                                    teamId: TeamId): Try[Int] = Try {
    DB.autoCommit { implicit session =>
      sql"""
           UPDATE call_settings
           SET
            first_name = ${data.first_name},
            last_name = ${data.last_name},
            call_limit_per_day = ${data.call_limit},
            record_call = ${data.enable_call_recording},
            enable_forwarding= ${data.enable_forward},
            forward_to = ${data.forward_number.map(_.phone_number)},
            caller_id = ${data.caller_id.map(_.uuid)},
            updated_at = now()
           WHERE uuid = ${uuid.phone_number_uuid}
           AND team_id = ${teamId.id};
         """
        .update
        .apply()
    }
  }

//  def deleteCallAccountSetting(phoneNumberUuid: PhoneNumberUuid, teamId: TeamId): Try[Int] = Try {
//    DB autoCommit { implicit session =>
//      sql"""
//             DELETE FROM call_settings
//             WHERE uuid=${phoneNumberUuid.phone_number_uuid}
//             AND team_id = ${teamId.id};
//             """
//        .update
//        .apply()
//    }
//  }

  def saveCallRecordingData(

                            RecordingUrl: String,
                            confSidOrConfUuid: ConfSidOrConfUuid,
                            tid: TeamId
                           ): Try[Int] = Try{

    DB.autoCommit { implicit session =>

      val getWhereClause = getWhereClauseConfSidOrConfUuid(confSidOrConfUuid = confSidOrConfUuid)


      sql"""
           UPDATE call_conference_logs
           SET
            recording_url = ${RecordingUrl}
           WHERE
           ${getWhereClause}
           AND team_id = ${tid.id};
         """
        .update
        .apply()
    }

  }

  def getChannelForScheduling(
                               channelId: ChannelId,
                               teamId: TeamId,
                               srLogger: SrLoggerTrait
                             ): Option[CallChannelDataForScheduling] = {

    DB readOnly { implicit session =>
      sql"""
        Select
          ps.first_name,
          ps.last_name,
          ps.phone_number,
          ps.owner_account_id,
          ps.team_id,
          ps.call_limit_per_day,
          ps.latest_task_scheduled_at,
          a.first_name as owner_first_name,
          a.last_name as owner_last_name,
          a.timezone,
          ps.record_call,
          a.org_id

          From call_settings ps
          INNER JOIN accounts a
          on a.id = ps.owner_account_id
          WHERE ps.uuid = ${channelId.id}
          AND team_id = ${teamId.id}
          AND ps.is_active
          ;
         """
        .map(rs => {
          val callSetting = CallSettingCreateSchedule(

            uuid = channelId.id,

            team_id = rs.long("team_id"),

            account_id = rs.long("owner_account_id"),

            phone_number = rs.string("phone_number"),

            sender_name = rs.string("owner_first_name") + " " + rs.string("owner_last_name"),

            first_name = rs.string("first_name"),

            last_name = rs.string("last_name"),

            call_limit_per_day = rs.int("call_limit_per_day"),

            latest_task_scheduled_at = rs.jodaDateTimeOpt("latest_task_scheduled_at"),

            account_timezone = rs.string("timezone"),

            min_delay_seconds = AppConfig.MultiChannel.call_min_delay_seconds,

            max_delay_seconds = AppConfig.MultiChannel.call_max_delay_seconds
          )


          ChannelDataForScheduling.CallChannelDataForScheduling(
            callSettings = callSetting,
            channelTeamId = callSetting.team_id,
            account_timezone = callSetting.account_timezone,
            channelOwnerAccountId = callSetting.account_id,
            channelOrgId = OrgId(rs.long("org_id"))
          )
        })
        .headOption
        .apply()


    }
  }


  def _updateLastScheduled(
                            callSettingUuid: String,
                            latestTaskScheduledAt: Option[DateTime],
                            Logger: SRLogger,
                            team_id: Long
                          ): Try[Int] = Try {

    DB autoCommit { implicit session =>
      Logger.info(s"_updateLastScheduled latest_task_scheduled_at: $callSettingUuid :: $latestTaskScheduledAt")
      // NOTE: for the task which got created by task runner directly will have latest_scheduled_at as empty
      if (latestTaskScheduledAt.isEmpty) {

        sql"""
            UPDATE call_settings
              SET
              in_queue_for_scheduling = false
            WHERE uuid = $callSettingUuid
              AND team_id = $team_id
            """
          .update
          .apply()
      } else {

        sql"""
            UPDATE call_settings
            SET
            in_queue_for_scheduling = false,
            latest_task_scheduled_at = ${latestTaskScheduledAt.get}
            WHERE uuid = $callSettingUuid
              AND team_id = $team_id
            """
          .update
          .apply()

      }

    }
  }

  def getCallSettingOfUser(teamId: TeamId, accountId: AccountId): Try[Option[CallAccountSettings]] = Try {
    DB.readOnly { implicit session =>

      CallDAO.getCallAccountSettingsQuery(
        where_clause =
          sqls"""
            WHERE
                 is_active
               AND
                 team_id = ${teamId.id}
               AND
                 owner_account_id = ${accountId.id}
            LIMIT 1
              """
      )
        .map(rs =>
          CallDAO.callAccountSettingsFromDB(rs = rs)
        )
        .single
        .apply()
    }
  }

  def getCallSettingByUuid(uuid: PhoneNumberUuid, teamId: TeamId): Try[Option[CallAccountSettings]] = Try {
    DB.readOnly { implicit session =>

      CallDAO.getCallAccountSettingsQuery(
          where_clause =
            sqls"""
            WHERE
                 is_active
               AND
                 uuid = ${uuid.phone_number_uuid}
               AND
                 team_id = ${teamId.id}
            LIMIT 1
              """
        )
        .map(rs =>
          CallDAO.callAccountSettingsFromDB(rs = rs)
        )
        .single
        .apply()
    }
  }

  def getPhoneSIDFromUuid(uuid: PhoneNumberUuid, teamId: TeamId): Try[Option[PhoneSID]] = Try {

    DB.readOnly { implicit session =>
      sql"""
      SELECT phone_sid
      FROM call_settings
      where uuid = ${uuid.phone_number_uuid}
      AND team_id = ${teamId.id}
    """
        .map(rs => rs.stringOpt("phone_sid").map(sid => PhoneSID(phone_sid = sid)))
        .single
        .apply()
        .get
    }

  }

  def updateCallAccountStatusToInActive(uuid: PhoneNumberUuid, teamId: TeamId): Try[Int] = Try {
    DB.autoCommit { implicit session =>
      sql"""
           UPDATE call_settings
            SET
              is_active = false
           WHERE uuid = ${uuid.phone_number_uuid}
           AND team_id = ${teamId.id}
         """
        .update
        .apply()
    }

  }

  // WEBHOOK SUPPORTING DAO FUNCTION
  def getCallSettingDataFromPhoneNumber(phone_number: PhoneNumber): Try[Option[CallAccountSettings]] = Try {
    DB.readOnly { implicit session =>

      CallDAO.getCallAccountSettingsQuery(
        where_clause =
          sqls"""
            WHERE phone_number = ${phone_number.phone_number}
              """
      )
        .map(rs =>
          CallDAO.callAccountSettingsFromDB(rs = rs)
        )
        .single
        .apply()
    }
  }


  def getPhoneSettingUuidFromNumber(number: PhoneNumber, teamId: TeamId): Try[PhoneNumber] = Try {
    DB.readOnly { implicit session =>
      sql"""
           SELECT
           uuid
           FROM call_settings
           WHERE phone_number = ${number.phone_number}
           AND
           team_id = ${teamId.id}
         """
        .map(rs =>
          PhoneNumber(phone_number = rs.string("uuid")))
        .single
        .apply()
        .get

    }
  }
  // WEBHOOK SUPPORTING DAO
  def getCallSettingDataFromCallerIdentity(phone_uuid: PhoneNumberUuid): Try[Option[CallAccountSettings]] = Try {

    DB.readOnly { implicit session =>

      CallDAO.getCallAccountSettingsQuery(
        where_clause =
          sqls"""
            WHERE uuid = ${phone_uuid.phone_number_uuid}
              """
      )
        .map(rs =>
          CallDAO.callAccountSettingsFromDB(rs = rs)
        )
        .single
        .apply()
    }
  }

  def updateCallSIDForBasicCall(conferenceUuid: ConferenceUuid, callSID: CallSID, initiating_number:PhoneNumber, teamId: TeamId): Try[ConferenceUuid] = Try{
    DB.autoCommit{ implicit session =>
      sql"""
           UPDATE
            call_participants_logs
           SET
            call_sp_sid = ${callSID.sid}
           WHERE
            call_conference_uuid = ${conferenceUuid.conf_uuid}
           AND
            call_participant_phone != ${initiating_number.phone_number}
           AND
            team_id =  ${teamId.id}
           RETURNING
            call_conference_uuid
         """
        .map(rs =>
          ConferenceUuid(conf_uuid = rs.string("call_conference_uuid")))
        .single
        .apply()
        .get
    }
  }

  def getConferenceUuidFromParentCallSID(callSID: CallSID, teamId: TeamId): Try[ConferenceUuid] = Try{

    DB.readOnly { implicit session =>

      sql"""
        SELECT call_conference_uuid FROM call_participants_logs
         WHERE call_sp_sid = ${callSID.sid}
         AND team_id = ${teamId.id}
         LIMIT 1;
        """

        .map(rs =>
          ConferenceUuid(conf_uuid = rs.string("call_conference_uuid"))
        )
        .single
        .apply()
        .get
    }

  }

  // details regarding this function:
  // https://github.com/heaplabs/team_discussions/discussions/60#discussioncomment-6331948
  /*
  Pr: 3154:

  What is Twilio Trigger ?

  Twilio provides us a way to monitor certain usage parameters on their behalf. We can set trigger for any sub-account
  and also the parameter for which we want twilio to monitor and notify us when certain limits are crossed.

  Where are we using twilio Trigger?

  Currently we are monitoring totalprice parameter, which tells us total usage of the sub-account. and while giving the
  webhook url to twilio trigger, I'm giving org_id in it also. so when the webhook request comes I can directly search
  sub-account details.

  We can change this behaviour later on like we can send uuid instead of org_id and that will also complete our use_case
   of fetching the sub-account details from db

  Now, Regarding the function callDAO.getSubAccountDetailsForOrg this function is used at verious places to fetch
  sub-account details. its signature is

  def getSubAccountDetailsForOrg(
  sub_account_uuid: Option[SubAccountUuid],
  org_id: OrgId

                              ): Try[Option[SubAccountDetails]]
  Now at some point like when twilio trigger webhook comes in, I only have org_id so I'm just passing that and getting
  the sub-account details.

  but in other use cases like when we want the sub-account details for updating the credit , we have pushed uuid to the
   queue along with org_id, that time I can use both uuid and org_id to fetch the details of sub-account.

  Basically instead of creating two functions for fetching sub-account details using differnt set of parameters I've
   tried to use one function.

  further optimizations can be done.


   */
  def getSubAccountDetailsForOrg(
                                sub_account_uuid: Option[SubAccountUuid],
                                org_id: OrgId

                                ): Try[Option[SubAccountDetails]] = Try{

    val whereClause = CallDAO.generateWhereClause(
      subAccountUuid = sub_account_uuid,
      orgId = org_id
    )

    DB.readOnly{ implicit session =>

      sql"""
          SELECT
            sub_account_uuid,
            org_id,
            twl_sub_account_sid,
            twl_sub_account_auth_token,
            twl_sub_account_status,
            twl_sub_account_name,
            call_credits_remaining,
            call_credits_updated_at,
            call_credits,
            credit_unit,
            previous_usage_deducted,
            already_subtracted,
            twl_check_usage_from,
            twl_trigger_id,
            twl_trigger_name,
            twl_trigger_by,
            twl_trigger_value,
            twl_trigger_webhook,
            twl_trigger_recurring,
            twl_trigger_usage_category,
            twl_trigger_last_fired,
            twl_idempotency_token,
            twl_trigger_current_value,
            twl_api_key_enc,
            twl_secret_enc,
            twl_twiml_app_sid,
            twl_twiml_app_voice_url,
            twl_twiml_app_name,
            last_call_history_updated_at,
            created_at,
            updated_at
          FROM org_calling_credits
          ${whereClause}

       """
        .map(rs =>
          CallDAO.subAccountDetailsFromDB(rs = rs)
        )
        .single
        .apply()

    }


  }

  def getSubAccountDetailsForOrgUsingSubAccountUuid(
                                  sub_account_uuid: SubAccountUuid
                                ): Try[Option[SubAccountDetails]] = Try {

    DB.readOnly { implicit session =>

      sql"""
          SELECT
            sub_account_uuid,
            org_id,
            twl_sub_account_sid,
            twl_sub_account_auth_token,
            twl_sub_account_status,
            twl_sub_account_name,
            call_credits_remaining,
            call_credits_updated_at,
            call_credits,
            credit_unit,
            previous_usage_deducted,
            already_subtracted,
            twl_check_usage_from,
            twl_trigger_id,
            twl_trigger_name,
            twl_trigger_by,
            twl_trigger_value,
            twl_trigger_webhook,
            twl_trigger_recurring,
            twl_trigger_usage_category,
            twl_trigger_last_fired,
            twl_idempotency_token,
            twl_trigger_current_value,
            twl_api_key_enc,
            twl_secret_enc,
            twl_twiml_app_sid,
            twl_twiml_app_voice_url,
            twl_twiml_app_name,
            last_call_history_updated_at,
            created_at,
            updated_at
          FROM org_calling_credits
          WHERE
            sub_account_uuid = ${sub_account_uuid.uuid}

       """
        .map(rs =>
          CallDAO.subAccountDetailsFromDB(rs = rs)
        )
        .single
        .apply()

    }


  }

  def updateConferenceCallLog(conf_uuid: ConferenceUuid, completedAt: DateTime, callStatus: CallStatus, teamId: TeamId): Try[ConferenceCallUpdateLog] = Try {
    DB autoCommit {
      implicit session =>
        sql"""
           UPDATE call_conference_logs
              SET
              completed_at = ${completedAt},
              status = ${callStatus.toString}
              WHERE conference_uuid = ${conf_uuid.conf_uuid}
              AND team_id = ${teamId.id}
            RETURNING conference_uuid,completed_at,status
         """
          .map(rs => ConferenceCallUpdateLog(
            call_uuid = ConferenceUuid(conf_uuid = rs.string("conference_uuid")) ,
            call_ended_at = rs.jodaDateTime("completed_at"),
            call_status=CallStatus.fromString(rs.string("status")).get
          ))
          .single
          .apply()
          .get
    }
  }

  def updateConferenceStatus(confSidOrConfUuid: ConfSidOrConfUuid,
                             newStatus: CallStatus,
                             completed_at: Option[DateTime] = None,
                             teamId: TeamId): Try[ConferenceUuid]= Try {

    val conf_complete_check = getCompletedAt(newStatus = newStatus, completed_at = completed_at)
    val getWhereClause = getWhereClauseConfSidOrConfUuid(confSidOrConfUuid = confSidOrConfUuid)

    DB autoCommit {
      implicit session =>
        sql"""
             UPDATE call_conference_logs
                SET
                ${conf_complete_check}
                status = ${newStatus.toString}
                WHERE
                ${getWhereClause}
                AND team_id = ${teamId.id}
              RETURNING conference_uuid
           """
          .map(rs => ConferenceUuid(conf_uuid = rs.string("conference_uuid")))
          .single
          .apply()
          .get
    }

  }

  def updateParticipantStatus(
                               callSidOrConfUuid: CallSidOrConfUuid,
                               newStatus: ParticipantCallStatus,
                               completed_at: Option[DateTime] = None,
                               teamId: TeamId): Try[CallParticipantUuid] = Try {

    val participant_complete_check: SQLSyntax = getParticipantCompletedAt(newStatus = newStatus, completed_at = completed_at)
    val whereClause: SQLSyntax = getWhereClauseParticipantStatusUpdate(
      callSidOrConfUuid = callSidOrConfUuid
    )

    DB autoCommit {
      implicit session =>
        sql"""
           UPDATE call_participants_logs
            SET
              ${participant_complete_check}
              status = ${newStatus.toString}
            WHERE
              ${whereClause}
              AND team_id = ${teamId.id}
            RETURNING call_participant_uuid;
           """
          .map(rs => CallParticipantUuid(participant_uuid = rs.string("call_participant_uuid")))
          .list
          .apply()
          .head
        /*
        In the above case :
        1. if the callSID is used (conference uuid is None) a single row will get updated
        2. if conference uuid is passed -> multiple rows will be updated
          but the uuid received is not utilised anywhere so using
          .head won't cause any issues
         */
    }

  }

  def getRemainingParticipantDetails(
                                      conferenceSid: ConferenceSid,
                                      teamId: TeamId
                                    ): Try[List[RemainingParticipantsDetails]] = Try {
    //Added call_type check
    DB readOnly { implicit session =>
      sql"""
           SELECT
            cp.participant_label, cp.latest_participant_mode
            FROM call_participants_logs cp
            INNER JOIN call_conference_logs cc
            ON cc.conference_uuid = cp.call_conference_uuid
            WHERE cc.conference_sp_sid = ${conferenceSid.sid}
            AND cp.status = ${ParticipantCallStatus.IN_PROGRESS.toString}
            AND cc.status = ${CallStatus.ACTIVE.toString}
            AND cc.call_type = ${CallType.TWL_CONF.toString}
            AND cc.team_id = ${teamId.id};
         """
        .map(rs => {
          RemainingParticipantsDetails(
            latest_participation_mode = CallParticipationMode.fromString(rs.string("latest_participant_mode")).get,
            participant_label = rs.string("participant_label")
          )

        })
        .list
        .apply()
    }
  }

  def getActiveParticipantAndConferenceDetails(
                                                teamId: TeamId,
                                                callParticipantUuid: Option[CallParticipantUuid],
                                                conferenceUuid: Option[ConferenceUuid],
                                                call_type: CallType = CallType.TWL_CONF
                                              ): Try[Seq[OngoingCallParticipantNotificationObject]] = Try {

    val whereClauseForParticipant: SQLSyntax = getWhereClauseForNotification(
      call_participant_uuid = callParticipantUuid,
      conference_uuid = conferenceUuid,
      callType = call_type
    )

    //Added call_type check

    DB readOnly { implicit session =>
      sql"""
         SELECT
           cp.call_participant_uuid,
           cp.call_conference_uuid,
           cp.latest_participant_mode,
           cs.owner_account_id,
           cs.first_name as participant_first_name,
           cs.last_name as participant_last_name,
           cc.task_uuid,
           cp.prospect_id,
           cp.calling_device,
           p.first_name as prospect_first_name,
           p.last_name as prospect_last_name,
           p.phone,
           cp.call_sp_sid
         FROM
         call_participants_logs cp
         INNER JOIN call_conference_logs cc ON cp.call_conference_uuid = cc.conference_uuid
         LEFT JOIN call_settings cs ON cs.uuid = cp.call_setting_uuid
         LEFT JOIN prospects p ON p.id = cp.prospect_id
         WHERE
          ${whereClauseForParticipant}
          AND cc.team_id = ${teamId.id}
          ORDER BY cp.connected_at DESC
          ;

      """
        .map(rs => CallDAO.activeCallParticipantDetails(rs))
        .list
        .apply()
    }
  }

  def setRecordCall(
                     updateRecordCallForm: UpdateRecordCallForm,
                     teamId: TeamId,
                     accountId: AccountId
                   ): Try[Int] = Try{
    DB.autoCommit {implicit session =>
      sql"""
          UPDATE call_settings
            SET
            record_call = ${updateRecordCallForm.record_call}
          WHERE uuid = ${updateRecordCallForm.call_setting_uuid}
            AND team_id = ${teamId.id}
            AND owner_account_id = ${accountId.id}
          """
        .update
        .apply()
    }
  }


  def saveSubAccountDetails(subAccountDetails: NewSubAccountDetails): Try[SubAccountUuid] =  Try{

    DB.autoCommit { implicit session =>
      sql"""
           INSERT INTO org_calling_credits(
              sub_account_uuid,
              org_id,
              twl_sub_account_sid,
              twl_sub_account_auth_token,
              twl_sub_account_status,
              twl_sub_account_name,
              call_credits_remaining,
              call_credits_updated_at,
              call_credits,
              credit_unit,
              previous_usage_deducted,
              twl_check_usage_from,
              created_at,
              updated_at
           ) VALUES (
             ${subAccountDetails.uuid.uuid},
             ${subAccountDetails.org_id.id},
             ${subAccountDetails.sub_account_sid.id},
             ${subAccountDetails.auth_token.token},
             ${subAccountDetails.status},
             ${subAccountDetails.sub_account_name},
             ${subAccountDetails.call_remaining_credit},
             ${subAccountDetails.call_credit_updated_at},
             ${subAccountDetails.call_credit},
             ${subAccountDetails.credit_unit.toString},
             ${subAccountDetails.previous_usage_deducted},
             ${subAccountDetails.check_usage_from},
             ${DateTime.now()},
             ${DateTime.now()}
           )
           RETURNING sub_account_uuid
          """
        .map(rs =>
          SubAccountUuid(uuid = rs.string("sub_account_uuid")))
        .single
        .apply()
        .get
    }

  }

  def updateCallerIdVerificationStatus(
                       status:  CallerIdVerificationStatus,
                       outgoingCallerIdSid: Option[OutgoingCallerIdSid],
                       phoneNumber: PhoneNumber,
                       teamId: TeamId
                     ): Try[CallerIdUuid] = Try {

    DB autoCommit { implicit session =>

      sql"""
          UPDATE twl_caller_ids
          SET
          twl_sp_sid = ${outgoingCallerIdSid.map(sid => sid.toString)},
          status = ${status.toString},
          updated_at= ${DateTime.now()}
          WHERE
           phone_number = ${phoneNumber.phone_number}
          AND
           team_id = ${teamId.id}
           RETURNING uuid;

         """
        .map(rs =>
          CallerIdUuid(uuid = rs.string("uuid")))
        .single
        .apply()
        .get

    }
  }


  def saveCallerIdDetails(
                           data: ValidateCallerIdData,
                           attempts: Int,
                           teamId: TeamId,
                           accountId: AccountId,
                           caller_id_uuid: CallerIdUuid
                          ): Try[CallerIdUuid] = Try {



    DB.autoCommit { implicit session =>
      sql"""
           INSERT INTO twl_caller_ids(
              uuid,
              phone_number,
              friendly_name,
              added_by,
              team_id,
              status
           ) VALUES (
             ${caller_id_uuid.uuid},
             ${data.phone_number.phone_number},
             ${data.friendly_name},
             ${accountId.id},
             ${teamId.id},
             ${CallerIdVerificationStatus.INITIALISED.toString}
           )
           ON CONFLICT (team_id, phone_number)
            DO UPDATE SET
            status= ${CallerIdVerificationStatus.INITIALISED.toString},
            attempts = ${attempts + 1},
            friendly_name = ${data.friendly_name},
            updated_at = ${DateTime.now()}
           RETURNING uuid
          """
        .map(rs =>
          CallerIdUuid(uuid = rs.string("uuid")))
        .single
        .apply()
        .get
    }

  }

  def saveTwilioTriggerDetails(usageTrigger: TwilioUsageTrigger): Try[String] = Try{

    DB autoCommit { implicit session =>
      sql"""
           UPDATE org_calling_credits SET
              twl_trigger_id = ${usageTrigger.trigger_id},
              twl_trigger_name = ${usageTrigger.trigger_name},
              twl_trigger_by = ${usageTrigger.trigger_by},
              twl_trigger_value = ${usageTrigger.trigger_value},
              twl_trigger_webhook = ${usageTrigger.trigger_webhook},
              twl_trigger_recurring = ${usageTrigger.trigger_recurring},
              twl_trigger_usage_category = ${usageTrigger.trigger_usage_category},
              updated_at = ${DateTime.now()}
           WHERE
              twl_sub_account_sid = ${usageTrigger.sub_account_sid}
           RETURNING twl_trigger_id;
          """
        .map(rs =>
          rs.string("twl_trigger_id"))
        .single
        .apply()
        .get


    }


  }

  def addedToTheQueue(
                       sub_account_uuid: SubAccountUuid,
                       org_id: OrgId
                     ): Try[Long] = Try{

    DB autoCommit  { implicit session =>

      sql"""
           UPDATE org_calling_credits
              SET
                in_queue_for_updating_credit = true,
                last_pushed_to_queue_for_updating_credit = NOW(),
                updated_at = now()
              WHERE
                sub_account_uuid = ${sub_account_uuid.uuid}
              AND
                org_id = ${org_id.id}
         """
        .update
        .apply()

    }
  }

  def addedToTheQueueCallHistoryLogs(
                       sub_account_uuid: SubAccountUuid
                     ): Try[Long] = Try {

    DB autoCommit { implicit session =>

      sql"""
           UPDATE org_calling_credits
              SET
                in_queue_for_updating_call_history = true,
                last_pushed_to_queue_for_updating_call_history = NOW(),
                updated_at = now()
              WHERE
                sub_account_uuid = ${sub_account_uuid.uuid}
         """
        .update
        .apply()

    }
  }

  def getSubAccountsForUpdatingCredit(): Try[List[SubAccountDetailsForUpdate]] =  Try{

    // fixme add more checks as updated at like that

    DB readOnly { implicit session =>
      sql"""
           SELECT
               sub_account_uuid,
               org_id
             FROM org_calling_credits
           WHERE
             twl_sub_account_status = 'active'
           AND (
                    in_queue_for_updating_credit = false
                  OR
                    (
                      in_queue_for_updating_credit = true
                       AND
                      last_pushed_to_queue_for_updating_credit < NOW() - INTERVAL '1 HOUR'
                    )
               )

         """
        .map(rs => SubAccountDetailsForUpdate(
          sub_account_uuid = SubAccountUuid(uuid = rs.string("sub_account_uuid")),
          org_id = OrgId(id = rs.long("org_id"))
        ))
        .list
        .apply()

    }

  }


  def getSubAccountsForUpdatingCallHistoryLogs(): Try[List[SubAccountUuid]] = Try {

    val interval_time = SQLSyntax.createUnsafely(Calling.call_history_logs_interval_hours.toString)
    val in_queue_stuck_time = SQLSyntax.createUnsafely(Calling.call_history_logs_in_stuck_sub_accounts_interval.toString)

    DB readOnly { implicit session =>
      sql"""
           SELECT
               sub_account_uuid

           FROM org_calling_credits occ
                JOIN  organizations o ON (occ.org_id = o.id)

           WHERE
             occ.twl_sub_account_status = 'active'
             AND o.plan_type != ${PlanType.INACTIVE.toString}
             AND (
                    ( occ.in_queue_for_updating_call_history = false
                     AND
                      (
                        occ.last_pushed_to_queue_for_updating_call_history IS NULL
                       OR
                        occ.last_pushed_to_queue_for_updating_call_history < NOW() - INTERVAL '$interval_time HOUR'
                      )
                    )
                    OR
                      (
                        occ.in_queue_for_updating_call_history = true
                         AND
                        occ.last_pushed_to_queue_for_updating_call_history < NOW() - INTERVAL '$in_queue_stuck_time HOUR'
                      )
                 )

           ORDER BY occ.last_pushed_to_queue_for_updating_call_history ASC NULLS FIRST

         """
        .map(rs => SubAccountUuid(uuid = rs.string("sub_account_uuid")))
        .list
        .apply()

    }

  }

  def updateCreditForSubAccount(
                                 sub_account_uuid: SubAccountUuid,
                                 previous_usage_deducted: Long,
                                 already_subtracted: Long,
                                 org_id: OrgId,
                                 credit_remaining: Long
                               ): Try[Long] = Try{

    DB autoCommit { implicit session =>
      sql"""
           UPDATE org_calling_credits
           SET
            call_credits_remaining = ${credit_remaining},
            already_subtracted = ${already_subtracted},
            previous_usage_deducted = ${previous_usage_deducted},
            updated_at = now()
           WHERE sub_account_uuid = ${sub_account_uuid.uuid}
           AND org_id = ${org_id.id}
           RETURNING call_credits_remaining;
         """
        .map(res => res.long("call_credits_remaining"))
        .single
        .apply()
        .get

    }

  }

  def getPrimaryProspectIdFromTask(task_uuid: String, teamId: TeamId): Try[TaskUuidAndProspectID] = Try {
    DB readOnly { implicit session =>
      sql"""
           SELECT task_id , prospect_id
           FROM tasks
           WHERE
           task_id = ${task_uuid}
           AND
           team_id = ${teamId.id}
         """
        .map(rs => {
          TaskUuidAndProspectID(
            task_uuid = rs.string("task_id"),
            prospect_id = ProspectId(id = rs.long("prospect_id"))
          )
        })
        .single
        .apply()
        .get

    }

  }

  def updateMainCreditForSubAccount(
                                   sub_account_details: SubAccountDetails,
                                   new_credit_details: AddCallingCreditDetails,
                                   ): Try[Int] = Try{

    // FIXME CALL: in future if we can find how to set usage limit in trigger from a certain date to next date
    // saving as multiple of hundred
    val total_credit_added = new_credit_details.credit_added_cents + sub_account_details.call_credits_cents
    val total_credit_remaining = new_credit_details.credit_added_cents + sub_account_details.call_remaining_credit_cents

    DB autoCommit { implicit session =>

      sql"""
           UPDATE org_calling_credits
           SET
              call_credits = ${total_credit_added},
              call_credits_updated_at = ${new_credit_details.credit_added_at},
              credit_unit = ${new_credit_details.credit_unit.toString},
              call_credits_remaining = ${total_credit_remaining},
              warning_msg = NULL,
              warning_at = NULL,
              warning_code = NULL,
              updated_at = now()
           WHERE
              sub_account_uuid = ${sub_account_details.uuid.uuid}
           AND
              org_id = ${new_credit_details.org_id.id}

         """
        .update
        .apply()
    }

  }

  def updateRemovedFromQueue(
                                                       sub_account_uuid: SubAccountUuid,
                                                       org_id: OrgId
                                                     ): Try[Int] = Try{

    DB autoCommit { implicit session =>

      sql"""
           UPDATE org_calling_credits
              SET
                in_queue_for_updating_credit = false,
                updated_at = now()
              WHERE
                sub_account_uuid = ${sub_account_uuid.uuid}
              AND
                org_id = ${org_id.id}
         """
        .update
        .apply()
    }

  }

  def updateRemovedFromQueueForCallHistoryMq(
                              sub_account_uuid: SubAccountUuid
                            ): Try[Int] = Try {

    DB autoCommit { implicit session =>

      sql"""
           UPDATE org_calling_credits
              SET
                in_queue_for_updating_call_history = false,
                updated_at = now()
              WHERE
                sub_account_uuid = ${sub_account_uuid.uuid}
         """
        .update
        .apply()
    }

  }

  def saveSuspendedSubAccountDetails(
                                      sub_account_details: TwilioSubAccountDetails,
                                      suspend_account_webhook: TwilioUsageLimitCrossedDetails, // fixme call create a generic class
                                      org_id: OrgId
                                    ): Try[Int] = Try{

    DB autoCommit { implicit session =>

      sql"""
           UPDATE org_calling_credits
              SET
                twl_sub_account_status = ${sub_account_details.status}, -- FIXME CALL sealed trait
                twl_trigger_last_fired = ${suspend_account_webhook.date_fired},
                twl_trigger_current_value = ${suspend_account_webhook.current_value},
                twl_idempotency_token = ${suspend_account_webhook.idempotency_token},
                updated_at = now()

              WHERE
                twl_sub_account_sid = ${suspend_account_webhook.twl_sub_account_sid.id}
              AND
                org_id = ${org_id.id}

         """
        .update
        .apply()

    }
  }

  // Fixme call fix this function as it needs to update the credit in future
  def updateSubAccountStatus(
                              sub_account_details: TwilioSubAccountDetails,
                              org_id: OrgId
                            ): Try[Int] = Try{

    DB autoCommit { implicit session =>

      sql"""
           UPDATE org_calling_credits
            SET
               twl_sub_account_status = ${sub_account_details.status}
            WHERE
               twl_sub_account_sid = ${sub_account_details.sub_account_sid.id}
            AND
               org_id = ${org_id.id}
         """
        .update
        .apply()

    }

  }

  // Todo: TwlAppName we can change this to something unique Fixme call
  def saveSubAccountTwimlApp(
                              twl_app: SubAccountTwimlApplication,
                              subAccountUuid: SubAccountUuid,
                              org_Id: OrgId
                            ): Try[Option[TwlTwimlAppName]] = Try{

    DB autoCommit { implicit session =>
      sql"""
           UPDATE org_calling_credits
              SET
                twl_twiml_app_sid = ${twl_app.app_sid.sid},
                twl_twiml_app_voice_url = ${twl_app.voice_url},
                twl_twiml_app_name = ${twl_app.app_name.name},
                updated_at = now()

              WHERE
                sub_account_uuid = ${subAccountUuid.uuid}
              AND
                org_id = ${org_Id.id}

           RETURNING
              twl_twiml_app_name
         """
        .map(res =>
          TwlTwimlAppName(
            name = res.string("twl_twiml_app_name")
          )
        )
        .single
        .apply()
    }
  }

  def saveSubAccountEncryptionKeys(
                              api_data: SubAccountApiKeyAndSecret,
                              subAccountUuid: SubAccountUuid,
                              org_Id: OrgId
                            ): Try[Option[ApiKey]] = Try {

    DB autoCommit { implicit session =>
      sql"""
           UPDATE org_calling_credits
              SET
                twl_api_key_enc = ${EncryptionHelpers.encryptSubAccountApiKey(api_data.api_key.key)},
                twl_secret_enc = ${EncryptionHelpers.encryptSubAccountApiSecret(api_data.api_secret.secret)},
                updated_at = now()
              WHERE
                sub_account_uuid = ${subAccountUuid.uuid}
              AND
                org_id = ${org_Id.id}

           RETURNING
              twl_api_key_enc
         """
        .map(res =>
          ApiKey(
            key = EncryptionHelpers.decryptSubAccountApiKey(res.string("twl_api_key_enc"))
          )
        )
        .single
        .apply()
    }
  }
  
  def updateDoneByForCallTask(
                             done_by: AccountId,
                             taskUuid: TaskUuid,
                             teamId: TeamId
                             ): Try[Int] = Try{

    DB autoCommit { implicit session =>

      sql"""
           UPDATE tasks
              SET
                done_by = ${done_by.id},
                done_at = ${DateTime.now()},
                updated_at = ${DateTime.now()},
                status = CASE 
                           WHEN created_via = ${TaskCreatedVia.Call_action.toKey} 
                           THEN ${TaskStatusType.Done.toKey} 
                           ELSE status 
                         END
              WHERE
                task_id = ${taskUuid.uuid}
                AND team_id = ${teamId.id}
         """
        .update
        .apply()
    }
    
  }


  def saveConferenceDetails(
                             conference_uuid: ConferenceUuid,
                             conference_details: ConferenceDetailsFromWebhook,
                             taskUuidAndProspectID: Option[TaskUuidAndProspectID],
                             team_id: TeamId,
                             is_incoming: Boolean,
                             prospect_details_for_incoming_call: Option[ProspectDetailsForIncomingCalls]
                     ): Try[CallLog] = Try{
    DB autoCommit { implicit session =>

      val prospectId: Option[Long] = if (is_incoming) {
        prospect_details_for_incoming_call.flatMap(p => p.prospectId.map(pr => pr.id))
      }
      else {
        taskUuidAndProspectID.map(
          details => details.prospect_id.id)
      }

      val internal_prospect_notes: Option[String] = if (is_incoming) {
        prospect_details_for_incoming_call.flatMap(p => p.prospectNotes.map(notes => notes.toString))
      } else {
        None
      }


      sql"""

        INSERT INTO call_conference_logs (
          conference_uuid,
          service_provider,
          conference_sp_sid,
          conference_sp_name,
          team_id,
          status,
          is_incoming,
          task_uuid,
          primary_prospect_id,
          call_initiated_by,
          initialized_at,
          log_created_at,
          call_type,
          internal_prospect_notes
      )

      VALUES (
      ${conference_uuid.conf_uuid},
      ${conference_details.service_provider.toString},
      ${conference_details.conference_sid},
      ${conference_details.conference_name},
      ${team_id.id},
      ${CallStatus.ACTIVE.toString},
      $is_incoming,
      ${taskUuidAndProspectID.map(details => details.task_uuid)},
      ${prospectId},
      ${conference_details.initiated_by.phone_number},
      now(),
      now(),
      ${conference_details.callType.toString},
      ${internal_prospect_notes}
      )
      RETURNING conference_uuid,conference_sp_sid,conference_sp_name,call_initiated_by,service_provider,status,task_uuid,primary_prospect_id
      """
        .map(rs => {

          val conf_sid = rs.stringOpt("conference_sp_sid").map(uuid=>ConferenceSid(uuid))
          val conf_name = rs.stringOpt("conference_sp_name").map(name=>ConferenceFriendlyName(name))

          CallLog(
            conference_uuid = ConferenceUuid(rs.string("conference_uuid")),
            conference_sid = conf_sid,
            conference_name = conf_name,
            initiated_by = PhoneNumber(phone_number = rs.string("call_initiated_by")),
            service_provider = CallingServiceProvider.fromString(rs.string("service_provider")).get,
            status = CallStatus.fromString(rs.string("status")).get,
            task_uuid = TaskUuid(rs.string("task_uuid")),
            primary_prospect_id = rs.intOpt("primary_prospect_id").map(p=> ProspectId(id=p))
          )
        }
        )
        .single
        .apply()
        .get
    }

  }

  def getConferenceNameFromConferenceUuid(conferenceUuid: ConferenceUuid, teamId: TeamId): Try[Option[ConferenceFriendlyName]] = Try {
    DB readOnly { implicit session =>
      sql"""
           SELECT conference_sp_name
           FROM call_conference_logs
           WHERE conference_uuid=${conferenceUuid.conf_uuid}
           AND team_id = ${teamId.id};
         """
        .map(rs=> ConferenceFriendlyName(name = rs.string("conference_sp_name")))
        .single
        .apply()

    }
  }

  def getConferenceSIDFromCallSID(callSID: CallSID, teamId: TeamId): Try[ConferenceSid] = Try {

    //Added call_type check
    DB readOnly { implicit session =>
      sql"""
           SELECT
           cc.conference_sp_sid
           FROM call_conference_logs cc
           INNER JOIN call_participants_logs cp
           ON cc.conference_uuid = cp.call_conference_uuid
           WHERE
           cp.call_sp_sid = ${callSID.sid}
           AND cc.call_type = ${CallType.TWL_CONF.toString}
           AND cc.team_id = ${teamId.id}
         """
        .map(rs => {
          ConferenceSid(sid = rs.string("conference_sp_sid"))
        })
        .single
        .apply()
        .get
    }

  }

  def saveParticipantDetails(
                             latest_participant_mode: InitialCallParticipationModeData,
                             participant_uuid: CallParticipantUuid,
                             conference_uuid: ConferenceUuid,
                             owner_account_id: AccountId,
                             team_id: TeamId,
                             call_setting_uuid: Option[String],
                             taskUuidAndProspectID: Option[TaskUuidAndProspectID],
                             calling_device: Option[CallingDevice],
                             participant_details: ParticipantDetailsFromWebhook
                           ): Try[ParticipantDetailsToNotify] = Try {

    DB autoCommit { implicit session =>

      sql"""

        INSERT INTO call_participants_logs (
          call_conference_uuid,
          call_participant_uuid,
          call_setting_uuid,
          call_sp_sid,
          call_from,
          calling_device,
          call_participant_phone,
          participant_label,
          status,
          latest_participant_mode,
          team_id,
          is_prospect,
          prospect_id,
          connected_at
      )

      VALUES (
      ${conference_uuid.conf_uuid},
      ${participant_uuid.participant_uuid},
      ${call_setting_uuid},
      ${participant_details.call_sid.map((callSID)=>callSID.sid)},
      ${participant_details.call_from.phone_number},
      ${calling_device.map(device => device.toString)},
      ${participant_details.call_participant_phone.phone_number},
      ${participant_details.participant_label},
      ${ParticipantCallStatus.IN_PROGRESS.toString},
      ${latest_participant_mode.callParticipantMode.toString},
      ${team_id.id},
      ${participant_details.is_prospect},
      ${taskUuidAndProspectID.map(details => details.prospect_id.id)},
      now()
      )
      RETURNING prospect_id, call_conference_uuid, call_participant_uuid, latest_participant_mode
      """
        .map(rs => {

          val prospect_id = rs.longOpt("prospect_id")
          val account_id: Option[AccountId] = Some(owner_account_id)

          ParticipantDetailsToNotify(
            conferenceUuid = ConferenceUuid(rs.string("call_conference_uuid")),
            participantUuid = CallParticipantUuid(rs.string("call_participant_uuid")),
            latest_participation_mode = CallParticipationMode.fromString(rs.string("latest_participant_mode")).get,
            prospectId = prospect_id.map(pid => ProspectId(id = pid)),
            accountId = account_id

          )
        })

//          CallParticipantUuid(participant_uuid = rs.string("call_participant_uuid")))
        .single
        .apply()
        .get
    }

  }



  def getConferenceDetails(
                          team_id: TeamId,
                          conferenceId: ConferenceSid
                          ) : Try[ConferenceDetails] = ???

  def getNoteIdFromCallParticipantsLogs(
                                         callSidOrConfUuid: CallSidOrConfUuid,
                                         teamId: TeamId
                                       ): Try[Option[NoteId]] = Try {

    val whereClause: SQLSyntax = getWhereClauseParticipantStatusUpdate(
      callSidOrConfUuid = callSidOrConfUuid
    )

    DB readOnly { implicit session =>

      sql"""
           SELECT
            note_id
           FROM call_participants_logs
           WHERE
            $whereClause
            AND team_id = ${teamId.id}
            AND is_prospect IS false;
         """
        .map(rs => rs.longOpt("note_id").map(NoteId(_)))
        .single
        .apply()
        .get

    }

  }

  def getProspectIdAndTaskUuidForConference(
                                             callSidOrConfUuid: CallSidOrConfUuid,
                                             teamId: TeamId
                                           ): Try[Option[GetNotesForm]] = Try {

    val whereClause: SQLSyntax = getWhereClauseParticipantStatusUpdate(
      callSidOrConfUuid = callSidOrConfUuid
    )

    DB readOnly { implicit session =>

      sql"""
           SELECT
            ccl.primary_prospect_id as prospect_id,
            ccl.task_uuid as task_uuid

           FROM call_participants_logs cpl
            INNER JOIN call_conference_logs ccl
            ON cpl.call_conference_uuid = ccl.conference_uuid

           WHERE
             $whereClause
             AND cpl.team_id = ${teamId.id}
           LIMIT 1;
         """
        .map(GetNotesForm.fromDB)
        .single
        .apply()

    }

  }

  /*
    Date: 23-Jul-2024
    So either we can have call_sp_sid (unique for each call_participant) or conf_uuid (not unique for call_participant).
    The normal calling one sends call_sp_sid which returns single row on match but the native call sends the conf_uuid and it returns 2 rows from call_participants_logs,
    one of initiator and other of prospect. So there so get the initiator's row we have that condition 'is_prospect IS false'
   */
  def updateNoteIdInCallParticipantsLogs(
                                          callSidOrConfUuid: CallSidOrConfUuid,
                                          teamId: TeamId,
                                          noteId: NoteId
                                        ): Try[NoteId] = Try {

    val whereClause: SQLSyntax = getWhereClauseParticipantStatusUpdate(
      callSidOrConfUuid = callSidOrConfUuid
    )

    DB autoCommit { implicit session =>

      sql"""
           UPDATE call_participants_logs
           SET note_id = ${noteId.id}
           WHERE
             $whereClause
             AND team_id = ${teamId.id}
             AND is_prospect IS false

            RETURNING note_id;
         """
        .map(rs => NoteId(rs.long("note_id")))
        .single
        .apply()
        .get

    }
  }

  def saveCallNote(
                    call_note: CallNote,
                    callSidOrConfUuid: CallSidOrConfUuid,
                    team_id : TeamId
                   ): Try[Option[CallNote]] = Try {

    val whereClause: SQLSyntax = getWhereClauseParticipantStatusUpdate(
      callSidOrConfUuid = callSidOrConfUuid
    )

    DB.autoCommit {implicit session =>

      sql"""
           UPDATE call_participants_logs
              SET call_note = ${call_note.note},
                  call_note_updated_at = now()
           WHERE
              ${whereClause}
           AND
              team_id = ${team_id.id}
           RETURNING
             call_note
          """
        .map(rs => CallNote(note = rs.string("call_note")))
        .list
        .apply()
        .headOption
    }

  }

  def saveCallFeedback(
                        call_sp_sid: CallSID,
                        team_id : TeamId,
                        call_feedback_type: CallFeedBackType,
                        feedback_reason_for_db: String,
                        user_comment: String
                      ): Try[Option[CallFeedBackResponse]] = Try{

    DB.autoCommit { implicit session =>

    sql"""

          UPDATE call_participants_logs
            SET
              feedback_type = ${call_feedback_type.toString},
              feedback_reason = ${feedback_reason_for_db},
              feedback_description = ${user_comment},
              feedback_at = now()

          WHERE
            call_sp_sid = ${call_sp_sid.sid}
          AND
            team_id = ${team_id.id}

          RETURNING

            call_participant_uuid,
            feedback_type
        """
      .map(rs => CallFeedBackResponse.fromDB(rs))
      .single
      .apply()

    }

  }

  //fixme call : Here the CallStatus is only active and we have to manage other statuses
  def getParticipantDetailsForCallSetting(
                                           participant_phone_number_uuid: String,
                                           teamId: TeamId
                                         ): Try[ParticipantUpdateDetails] = Try {

    DB.readOnly { implicit session =>
      sql"""
           select ccl.conference_sp_sid as call_conference_sid,
           cpl.call_sp_sid,
           cpl.prospect_id,
           cpl.status,
           cpl.latest_participant_mode,
           cpl.was_listening,
           cpl.listened_at,
           cpl.was_coaching,
           cpl.coached_at,
           cpl.was_barged_in,
           cpl.barged_in_at,
           cpl.added_to_conference_at,
           is_prospect

           from call_participants_logs cpl

           inner join call_conference_logs ccl
           on cpl.call_conference_uuid = ccl.conference_uuid

           where cpl.call_participant_phone = ${participant_phone_number_uuid}
           and cpl.status = ${ParticipantCallStatus.IN_PROGRESS.toString}
           and cpl.team_id = ${teamId.id}
           ;
        """
        .map(rs =>
          CallDAO.callParticipantUpdateDetailsFomDB(rs)
        )
        .single
        .apply()
        .get
    }

  }

  def getCallInitiatorCallSid(
                               prospectId: ProspectId,      //fixme call we will not get prospect_id always
                               teamId: TeamId
                             ): Try[CallSID] = Try {
    DB readOnly { implicit session =>

      sql"""
           select call_sp_sid from call_participants_logs

           where prospect_id = ${prospectId.id}
           and status = ${ParticipantCallStatus.IN_PROGRESS.toString}
           and participant_label= ${CallParticipationMode.InitiatorMode.toString}
           and team_id = ${teamId.id}
           ;
         """
        .map(rs => CallSID(rs.string("call_sp_sid")))
        .single
        .apply()
        .get

    }
  }

  def getCustomerCallSidFromInitiatorCallSid(
                               callSid: CallSID,
                               teamId: TeamId
                             ): Try[CallSID] = Try {
    DB readOnly { implicit session =>

      sql"""
             select call_sp_sid from call_participants_logs
             where
             team_id = ${teamId.id}
             and status = ${ParticipantCallStatus.IN_PROGRESS.toString}
             and participant_label= ${AppConfig.Calling.customer_label}
             and call_conference_uuid = (
             Select call_conference_uuid
             from call_participants_logs where
              team_id = ${teamId.id}
              AND
              call_sp_sid = ${callSid.sid}
             )
             ;
           """
        .map(rs => CallSID(rs.string("call_sp_sid")))
        .single
        .apply()
        .get

    }
  }

  def updateCallParticipantLogs(
                                 data: ParticipantUpdateDetails,
                                 teamId: TeamId
                               ): Try[Int] = Try {
    DB.autoCommit { implicit session =>
      sql"""
           UPDATE call_participants_logs
           SET
            latest_participant_mode = ${data.latest_participant_mode},
            was_listening = ${data.was_listening},
            listened_at = ${data.listened_at},
            was_coaching = ${data.was_coaching},
            coached_at = ${data.coached_at},
            was_barged_in = ${data.was_barged_in},
            barged_in_at = ${data.barged_in_at}

            where call_sp_sid = ${data.call_sp_sid.sid}
            and team_id = ${teamId.id}
            ;
         """
        .update
        .apply()
    }
  }

  def addNativeCallParticipantLog(
                             completed_at: DateTime,
                             picked_up_at: Option[DateTime],
                             callStatus: CallStatus,
                             call_conference_uuid: ConferenceUuid,
                             team_id: TeamId
                           ): Try[ParticipantCallUpdateLog] = Try {
    DB autoCommit {
      implicit session =>

        sql"""
            UPDATE call_participants_logs
              SET
                completed_at = ${completed_at},
                call_picked_up_at = ${picked_up_at},
                status = ${callStatus.toString}
              WHERE
                call_conference_uuid = ${call_conference_uuid.conf_uuid}
              AND
                team_id = ${team_id.id}
              RETURNING call_conference_uuid,completed_at,status,call_picked_up_at
          """
          .map(rs => ParticipantCallUpdateLog(
            call_uuid=ConferenceUuid(conf_uuid = rs.string("call_conference_uuid")),
            picked_up_at = rs.jodaDateTimeOpt("call_picked_up_at"),
            call_ended_at = rs.jodaDateTime("completed_at"),
            call_status = CallStatus.fromString(rs.string("status")).get,
          ))
          .list
          .apply()
          .head

    }
  }

  def addReplySentimentUUID(
                           reply_sentiment_uuid: ReplySentimentUuid,
                           call_sp_sid: CallSID,
                           team_id: TeamId
                           )( implicit session : DBSession ): Try[Int] = Try{

      sql"""
            UPDATE call_participants_logs
              SET
                reply_sentiment_uuid = ${reply_sentiment_uuid.uuid}
              WHERE
                call_sp_sid = ${call_sp_sid.sid}
              AND
                team_id = ${team_id.id}

          """
        .update
        .apply()

  }

  def addReplySentimentUUIDToConferenceLogs(
                             reply_sentiment_uuid: ReplySentimentUuid,
                             callSidOrConfUuid: CallSidOrConfUuid,
                             team_id: TeamId
                           )(implicit session: DBSession): Try[Int] = Try {

    val whereClause = getWhereClauseReplySentimentUpdate(callSidOrConfUuid = callSidOrConfUuid, teamId = team_id)

    sql"""
            UPDATE call_conference_logs
              SET
                reply_sentiment_uuid = ${reply_sentiment_uuid.uuid}
              WHERE
                ${whereClause}
              AND
                team_id = ${team_id.id}

          """
      .update
      .apply()

  }

  def addWarningMessageInOrgCallingCredits(
                                          org_id: OrgId,
                                          warning_message: String,
                                          warning_at: DateTime,
                                          warning_code : AccountWarningCodeType
                                          ) : Try[Option[String]] = Try{

    DB.autoCommit { implicit session =>
      sql"""
           UPDATE org_calling_credits
              SET warning_msg = ${warning_message},
                  warning_at = ${warning_at},
                  warning_code = ${warning_code.toString},
                  updated_at = now()
              WHERE
                  org_id = ${org_id.id}
           RETURNING warning_msg
         """
        .map(rs => rs.string("warning_msg"))
        .single
        .apply()
    }
  }

  def fetchOrgIdsFromOrgCallingCredits(): Try[List[OrgId]] = Try{

    DB.readOnly{ implicit session =>

      sql"""
           SELECT  org_id
           from org_calling_credits
         """
        .map(rs => OrgId(id = rs.long("org_id")))
        .list
        .apply()

    }



  }


  def fetchCallSettingIdAndDetails(
                                  sub_account_uuid : SubAccountUuid
                                  ): Try[List[CallAccountSettings]] = Try{
    DB.readOnly { implicit session =>

      CallDAO.getCallAccountSettingsQuery(
        where_clause =
          sqls"""
            WHERE
               org_sub_account_uuid = ${sub_account_uuid.uuid}
              """
      )
        .map(rs =>
          CallDAO.callAccountSettingsFromDB(rs = rs)
        )
        .list
        .apply()
    }



  }



  def updateCallLogLastFetchedAt(
                                sub_account_uuid: SubAccountUuid
                                ): Try[Int] = Try{

    DB.autoCommit{ implicit session =>

      sql"""
           UPDATE org_calling_credits
              SET last_call_history_updated_at = now()
           WHERE sub_account_uuid = ${sub_account_uuid.uuid}
         """
        .update
        .apply()

    }
  }

  def getRemainingCallingCredits(
                                sub_account_uuid: SubAccountUuid
                                ): Try[CallingRemainingCredits] = Try{

    DB.readOnly { implicit session =>

      sql"""
           SELECT
              call_credits_remaining,
              credit_unit
           FROM org_calling_credits
           WHERE sub_account_uuid = ${sub_account_uuid.uuid}

         """
        .map(rs => CallingRemainingCredits(
          remaining_credit = convertCentToDollar(rs.long("call_credits_remaining")),
          currency_type = CurrencyType.fromKey(key = rs.string("credit_unit")).getOrElse(CurrencyType.USD)
        ))
        .single
        .apply()
        .get

      }
  }


  def getCallLogsForUser(
                        sub_account_uuid: SubAccountUuid,
                        team_id: TeamId,
                        validatedCallHistoryReqRange: ValidatedCallHistoryReqRange
                        ): Try[List[CallLogFromDB]] = Try{

    DB.autoCommit { implicit session =>

      CallDAO.getCallLogQuery(
        sub_account_uuid = sub_account_uuid,
        team_id = team_id,
        validatedCallHistoryReqRange = validatedCallHistoryReqRange,
        limit = validatedCallHistoryReqRange.pageSize
      )
        .map(rs => CallDAO.getCallLogsFromDB(rs))
        .list
        .apply()

    }
  }

  def getSumOfRemainingBalanceForAllSubAccounts(): Try[CallingRemainingCredits] = Try{

    DB.readOnly { implicit session =>

      sql"""
           SELECT
              COALESCE(SUM(occ.call_credits_remaining), 0) AS total_required_credits
           FROM
              org_calling_credits occ
           WHERE
             occ.twl_sub_account_status = ${SubAccountStatus.Active.toString}
         """
        .map(rs => CallingRemainingCredits(
          remaining_credit = convertCentToDollar(rs.long("total_required_credits")),
          currency_type = CurrencyType.USD
        ))
        .single
        .apply()
        .get

    }

  }

  /*
    Explanation: We are fetching users whose plan is in-active and their trial has expired before 3 days

    1. if for a certain user trial expires then after 3 days. their numbers will be released.
    2. If user cancels and were using calling features then their number will also get released immediately.

   */
  def getTrialExpiredOrgsToReleasePhoneNumbers(): Try[List[OrgId]] = Try{

    val release_phone_number_when_trial_expires_after_interval_days = SQLSyntax.createUnsafely(AppConfig.Calling.release_phone_number_when_trial_expires_after_interval_days.toString)

    DB.readOnly { implicit session =>

      sql"""

        SELECT
          occ.org_id as occ_org_id
        FROM
          org_calling_credits occ
          JOIN call_settings cs on (cs.org_sub_account_uuid = occ.sub_account_uuid AND cs.is_active)
          JOIN organizations o ON (o.id = occ.org_id)
        WHERE
          o.plan_type = ${PlanType.INACTIVE.toString}
          AND
           (
             (
               o.trial_ends_at < now() - INTERVAL '$release_phone_number_when_trial_expires_after_interval_days days'
               AND o.cancelled_at is null
             )
             OR
             (
              o.cancelled_at IS NOT NULL
             AND
              o.cancelled_at < now() - INTERVAL '$release_phone_number_when_trial_expires_after_interval_days days'
               AND
              o.trial_ends_at < now() - INTERVAL '$release_phone_number_when_trial_expires_after_interval_days days'
             )
           )
        GROUP BY  occ_org_id

       """
        .map(rs => OrgId(id = rs.long("occ_org_id")))
        .list
        .apply()

    }
  }

  def getActivePhoneNumbersToReleaseForSubAccount(
                                           sub_account_uuid: SubAccountUuid
                                         ): Try[List[PhoneNumberToReleaseData]] = Try{

    DB.readOnly { implicit session => {

      sql"""
          SELECT
            uuid,
            phone_sid,
            team_id
          FROM
            call_settings
          WHERE
            org_sub_account_uuid = ${sub_account_uuid.uuid}
            AND is_active

         """
        .map(rs => PhoneNumberToReleaseData(
          phone_number_uuid = PhoneNumberUuid(phone_number_uuid = rs.string("uuid")),
          phone_sid = PhoneSID(phone_sid = rs.string("phone_sid")),
          team_id = TeamId(id = rs.long("team_id"))
        ))
        .list
        .apply()

    }}
  }

  def updateMasterProspectAfterMergeInCallConferenceLogs(
                                                          duplicateProspects: List[ProspectIdAndPotentialDuplicateProspectId],
                                                          masterProspectId: ProspectId,
                                                          teamId: TeamId
                                                        )(implicit session: DBSession): Try[List[ConferenceUuid]] = Try {
    var valueParameters = List[Any]()

    val valuePlaceholder: SQLSyntax = duplicateProspects.map(p => {

        valueParameters = valueParameters ::: List(
          p.prospectId.id,
          p.potentialDuplicateProspectId.id,

          masterProspectId.id,
          teamId.id
        )

        sqls"""
           (
              ?,
              ?,

              ?,
              ?
            )
          """

      })
      .reduce((vp1, vp2) => sqls"$vp1, $vp2")

    sql"""
       UPDATE call_conference_logs ccl
        SET
          primary_prospect_id = temp.master_prospect_id,
          potential_duplicate_prospect_id = temp.potential_duplicate_prospect_id

          FROM (
              VALUES $valuePlaceholder
            )
            AS temp(
              prospect_id,
              potential_duplicate_prospect_id,

              master_prospect_id,
              team_id
            )
      WHERE
        ccl.team_id = temp.team_id
        AND ccl.primary_prospect_id = temp.prospect_id
        RETURNING ccl.conference_uuid
        ;
       """
      .bind(valueParameters*)
      .map(rs => ConferenceUuid(rs.string("conference_uuid")))
      .list
      .apply()
  }

  def updateMasterProspectAfterMergeInCallParticipantsLogs(
                                                            duplicateProspects: List[ProspectIdAndPotentialDuplicateProspectId],
                                                            masterProspectId: ProspectId,
                                                            teamId: TeamId
                                                          )(implicit session: DBSession): Try[List[ConferenceUuid]] = Try {
    var valueParameters = List[Any]()

    val valuePlaceholder: SQLSyntax = duplicateProspects.map(p => {

        valueParameters = valueParameters ::: List(
          p.prospectId.id,
          p.potentialDuplicateProspectId.id,

          masterProspectId.id,
          teamId.id
        )

        sqls"""
           (
              ?,
              ?,

              ?,
              ?
            )
          """

      })
      .reduce((vp1, vp2) => sqls"$vp1, $vp2")

    sql"""
       UPDATE call_participants_logs cpl
        SET
          prospect_id = temp.master_prospect_id,
          potential_duplicate_prospect_id = temp.potential_duplicate_prospect_id

          FROM (
              VALUES $valuePlaceholder
            )
            AS temp(
              prospect_id,
              potential_duplicate_prospect_id,

              master_prospect_id,
              team_id
            )
      WHERE
        cpl.team_id = temp.team_id
        AND cpl.prospect_id = temp.prospect_id
        RETURNING cpl.call_conference_uuid
        ;
       """
      .bind(valueParameters*)
      .map(rs => ConferenceUuid(rs.string("call_conference_uuid")))
      .list
      .apply()
  }

}

case class ConferenceDetails(
                              conference_uuid : String,
                              service_provider : String,
                              conference_sp_id: String,
                              conference_sp_name : String,
                              team_id: TeamId,
                              is_incoming: Boolean,
                              status : String,
                              task_id: String,
                              call_initiated_by : String,  // phone number of initiator
                              reply_sentiment_uuid : Option[String],
                              primary_prospect_id : Option[ProspectId],
                              recording_url: Option[String],
                              feedback_type: Option[String],
                              feedback_reason: Option[String],
                              feedback_description: Option[String],
                              feedback_at: Option[DateTime],
                              call_notes: Option[String],
                              initialized_at: DateTime,
                              completed_at: Option[DateTime],
                              log_created_at: DateTime
                            )
case class ParticipantDetails(
                               call_conference_uuid: String,
                               call_participant_uuid: String,
                               call_setting_uuid : String,
                               call_sp_sid: CallSID,
                               call_from: String,
                               prospect_id: Option[ProspectId],
                               call_participant_phone: String,
                               participant_label: String,
                               status: String,
                               latest_participant_mode: String,
                               was_listening: Option[Boolean],
                               listened_at: Option[DateTime],
                               was_coaching: Option[Boolean],
                               coached_at: Option[DateTime],
                               was_barged_in : Option[Boolean],
                               barged_in_at : Option[DateTime],
                               added_to_conference_at: Option[DateTime],
                               is_prospect: Option[Boolean],
                               connected_at : Option[DateTime],
                               completed_at: Option[DateTime],
                               did_transfer : Option[Boolean],
                               transferred_to: Option[String],
                               team_id: TeamId,
                               joined_via_transfer : Option[Boolean],
                               is_forwarded: Option[Boolean],
                               forwarded_to: Option[String]
                             )

case class ParticipantUpdateDetails(
                                     call_conference_sid: String,
                                     call_sp_sid: CallSID,
                                     status: String,
                                     latest_participant_mode: String,
                                     was_listening: Option[Boolean],
                                     listened_at: Option[DateTime],
                                     was_coaching: Option[Boolean],
                                     coached_at: Option[DateTime],
                                     was_barged_in: Option[Boolean],
                                     barged_in_at: Option[DateTime],
                                     added_to_conference_at: Option[DateTime],
                                     is_prospect: Option[Boolean]
                                   )
object CallDAO {


  def getCallLogsFromDB(rs: WrappedResultSet): CallLogFromDB = {

    val tid = rs.longOpt("team_id").map(tid => TeamId(id = tid))

    val user_id = rs.longOpt("user_id").map(aid => AccountId(id = aid))

    val name = rs.stringOpt("user_name").map(name => DoerAccountName(name = name))

    CallLogFromDB(
      user_name = name,
      sub_account_uuid = SubAccountUuid(uuid = rs.string("sub_account_uuid")),
      team_id = tid,
      uuid = CallLogUUID(uuid = rs.string("uuid")),
      user_id = user_id,
      service_provider = CallingServiceProvider.fromString(rs.string("service_provider")).get,
      call_sid = CallSID(sid = rs.string("call_sid")),
      is_outgoing = rs.boolean("is_outgoing"),
      start_time = rs.jodaDateTime("start_time"),
      end_time = rs.jodaDateTime("end_time"),
      from_number = rs.string("from_number"),
      to_number = rs.string("to_number"),
      duration = rs.long("duration"),
      price = rs.long("price"),
      // FIXME: prices from twilio are in usd only.// we need to check this kind of logic when
      // adding other service providers
      price_unit = CurrencyType.fromKey(rs.string("price_unit")).getOrElse(CurrencyType.USD)
    )

  }


  def getCallLogQuery(
                       validatedCallHistoryReqRange: ValidatedCallHistoryReqRange,
                     team_id: TeamId,
                     sub_account_uuid: SubAccountUuid,
                       limit: Long
                     ): SQL[Nothing, NoExtractor] = {

    val (timelineQuery, order_by_query )= validatedCallHistoryReqRange.timeline match {

      case InferredQueryTimeline.Range.Before(dateTime) =>

        (sqls"  AND start_time AT TIME ZONE 'UTC' < ${dateTime}", sqls" ORDER BY start_time desc")


      case InferredQueryTimeline.Range.After(dateTime) =>

        (sqls"  AND start_time AT TIME ZONE 'UTC' > ${dateTime}", sqls" ORDER BY start_time asc")

    }

    val teamIDQuery = sqls"""AND team_id = ${team_id.id}"""






    sql"""

         SELECT
            CONCAT(a.first_name, ' ', a.last_name) AS user_name,
            chl.uuid,
            chl.sub_account_uuid,
            chl.team_id,
            chl.user_id,
            chl.service_provider,
            chl.call_sid,
            chl.is_outgoing,
            chl.start_time,
            chl.end_time,
            chl.from_number,
            chl.to_number,
            chl.duration,
            chl.price,
            chl.price_unit
         FROM call_history_logs chl
              LEFT JOIN accounts a ON a.id = chl.user_id
         WHERE
            sub_account_uuid = ${sub_account_uuid.uuid}
            ${timelineQuery}
            ${teamIDQuery}

            ${order_by_query}

         LIMIT ${limit}
       """

  }


  def getCallAccountSettingsQuery(where_clause: SQLSyntax): SQL[Nothing, NoExtractor] = {

    sql"""
          SELECT
               uuid,
               phone_number,
               owner_account_id,
               team_id,
               first_name,
               last_name,
               call_limit_per_day,
               phone_type,
               country,
               enable_forwarding,
               forward_to,
               service_provider,
               org_sub_account_uuid,
               is_active,
               caller_id,
               record_call
          FROM call_settings
          ${where_clause}
       """
  }

  def getCompletedAt(newStatus: CallStatus, completed_at: Option[DateTime]): SQLSyntax = {

    completed_at match {

      case None => if (newStatus == CallStatus.COMPLETED) {

        sqls" completed_at = now(),"

      } else {

        sqls""

      }

      case Some(dateTime) => sqls" completed_at = ${dateTime},"
    }

  }
  def getWhereClauseConfSidOrConfUuid(
                                         confSidOrConfUuid: ConfSidOrConfUuid
                                        ): SQLSyntax ={
    confSidOrConfUuid match {
      case ConfSidOrConfUuid.WithConfSid(conf_sid) =>
        sqls"""
              conference_sp_sid = ${conf_sid.sid}
            """

      case ConfSidOrConfUuid.WithConfUuid(conf_uuid) =>
        sqls"""
              conference_uuid = ${conf_uuid.conf_uuid}
            """
    }


  }

  def getCallerIdWhereClause(
                            status: Option[CallerIdVerificationStatus]
                            ): SQLSyntax = {
    status match {

      case None =>
        sqls"""
          (status = ${CallerIdVerificationStatus.SUCCESS.toString}
           OR
          status = ${CallerIdVerificationStatus.DELETED.toString})
            """

      case Some(value) =>
        sqls"""
              status = ${value.toString}
            """
    }
  }

  def getParticipantCompletedAt(newStatus: ParticipantCallStatus, completed_at: Option[DateTime]): SQLSyntax = {
    completed_at match {

      case None => if (newStatus == ParticipantCallStatus.COMPLETED) {

        sqls" completed_at = now(),"

      } else if (newStatus == ParticipantCallStatus.IN_PROGRESS) {

        sqls""" call_picked_up_at = now(), """

      } else {

        sqls""

      }


      case Some(completed_at) =>
        sqls" completed_at = ${completed_at},"
    }



  }

  def getWhereClauseParticipantStatusUpdate(
                                             callSidOrConfUuid: CallSidOrConfUuid
                                             ): SQLSyntax = {
    /*
    Note: 19-APR-2024
      In the incoming call flow, we get the participant status corresponding to one participant only, i.e. the user.
      We are passing conference_uuid only when update is taking place via the incoming call flow.
      To simplify the update, we are updating both the entries corresponding to the customer and user.
      Here the common field is the conference_uuid so both of them will be updated.
     */

    callSidOrConfUuid match {
      case CallSidOrConfUuid.WithCallSid(callSID) =>
        sqls"""
              call_sp_sid = ${callSID.sid}
            """

      case CallSidOrConfUuid.WithConfUuid(conf_uuid) =>
        sqls"""
              call_conference_uuid = ${conf_uuid.conf_uuid}
            """
    }



  }


  def getWhereClauseReplySentimentUpdate(
                                             callSidOrConfUuid: CallSidOrConfUuid,
                                             teamId: TeamId

                                           ): SQLSyntax = {


    callSidOrConfUuid match {
      case CallSidOrConfUuid.WithCallSid(callSID) =>
        sqls"""
              conference_uuid IN ( SELECT DISTINCT(call_conference_uuid ) FROM call_participants_logs where
              call_sp_sid = ${callSID.sid} AND team_id = ${teamId.id})
            """

      case CallSidOrConfUuid.WithConfUuid(conf_uuid) =>
        sqls"""
              conference_uuid = ${conf_uuid.conf_uuid}
            """
    }


  }

  def generateWhereClause(
                         subAccountUuid: Option[SubAccountUuid],
                         orgId: OrgId
                         ): SQLSyntax = {

    subAccountUuid match {
      case None =>

        sqls"""
              WHERE org_id = ${orgId.id}
            """

      case Some(sub_account_uuid) =>

        sqls"""
              WHERE
                org_id = ${orgId.id}
              AND
                sub_account_uuid = ${sub_account_uuid.uuid}
            """

    }

  }

  def getWhereClauseForNotification(
                                     call_participant_uuid: Option[CallParticipantUuid],
                                     conference_uuid: Option[ConferenceUuid],
                                     callType: CallType
                                   ): SQLSyntax = {
    call_participant_uuid match {
      case Some(callParticipantUuid: CallParticipantUuid) =>

        sqls"""
              cp.call_participant_uuid = ${callParticipantUuid.participant_uuid}
            """

      case None =>

        conference_uuid match {

          case Some(conferenceUuid: ConferenceUuid) =>
            callType match {
              //This case is added for a normal call when the participant exits
              case CallType.TWL_BASIC =>
                sqls"""
                      cp.call_conference_uuid = ${conferenceUuid.conf_uuid}

                    """

              case CallType.TWL_CONF | CallType.NATIVE_OUTGOING =>
                sqls"""
                      cp.call_conference_uuid = ${conferenceUuid.conf_uuid}
                      AND cp.status = ${ParticipantCallStatus.IN_PROGRESS.toString}
                    """

            }


          case None =>
            sqls"""
                (
                cc.status = ${CallStatus.ACTIVE.toString}
                OR cp.status = ${ParticipantCallStatus.IN_PROGRESS.toString}
                )
                AND cc.call_type != ${CallType.NATIVE_OUTGOING.toString}
                AND cp.call_sp_sid IS NOT NULL
                """
        }

    }

  }

  def subAccountDetailsFromDB(rs: WrappedResultSet): SubAccountDetails = {

    SubAccountDetails(
      uuid = SubAccountUuid(uuid = rs.string("sub_account_uuid")),
      sub_account_id = TwlSubAccountSid(id = rs.string("twl_sub_account_sid")),
      sub_auth_token = TwlAuthToken(token = rs.string("twl_sub_account_auth_token")),
      sub_account_name = rs.string("twl_sub_account_name"),
      call_remaining_credit_cents = rs.long("call_credits_remaining"),
      call_credit_updated_at = rs.jodaDateTimeOpt("call_credits_updated_at"),
      credit_unit = CurrencyType.fromKey(rs.string("credit_unit")).get,
      trigger_name= rs.stringOpt("twl_trigger_name"),
      trigger_webhook = rs.stringOpt("twl_trigger_webhook"),
      trigger_recurring = rs.stringOpt("twl_trigger_recurring"),
      trigger_usage_category = rs.stringOpt("twl_trigger_usage_category"),
      trigger_last_fired = rs.jodaDateTimeOpt("twl_trigger_last_fired"),
      created_at = rs.jodaDateTime("created_at"),
      updated_at = rs.jodaDateTime("updated_at"),
      org_id = OrgId(id = rs.long("org_id")),
      status = SubAccountStatus.fromKey(rs.string("twl_sub_account_status")).get,
      call_credits_cents = rs.long("call_credits"),
      trigger_id = rs.stringOpt("twl_trigger_id"),
      trigger_value = rs.stringOpt("twl_trigger_value"),
      trigger_by = rs.stringOpt("twl_trigger_by"),
      check_usage_from = rs.jodaDateTime("twl_check_usage_from"),
      previous_usage_deducted_cents = rs.longOpt("previous_usage_deducted"),
      already_subtracted_cents = rs.longOpt("already_subtracted"),
      twl_idempotency_token = rs.stringOpt("twl_idempotency_token"),
      twl_trigger_current_value_cents = rs.longOpt("twl_trigger_current_value"),
      twl_api_key = ApiKey(key = EncryptionHelpers.decryptSubAccountApiKey(rs.string("twl_api_key_enc"))),
      twl_secret_key = ApiSecret(secret = EncryptionHelpers.decryptSubAccountApiSecret(rs.string("twl_secret_enc"))),
      twl_twiml_app_sid = TwlTwimlAppSid(sid = rs.string("twl_twiml_app_sid")),
      twl_twiml_app_voice_url = rs.string("twl_twiml_app_voice_url"),
      twl_twiml_app_name = TwlTwimlAppName(name = rs.string("twl_twiml_app_name")),
      last_call_history_updated_at = rs.jodaDateTimeOpt("last_call_history_updated_at")
    )
  }

  def callAccountSettingsFromDB(rs: WrappedResultSet): CallAccountSettings = {

    CallAccountSettings(
      uuid = PhoneNumberUuid (phone_number_uuid = rs.string("uuid")),
      phone_number = rs.stringOpt("phone_number").map(phone_number=>PhoneNumber(phone_number=phone_number)),
      first_name = rs.string("first_name"),
      last_name = rs.string("last_name"),
      owner_account_id = AccountId(id = rs.long("owner_account_id")),
      team_id = TeamId( id = rs.long("team_id")),
      call_limit_per_day = rs.int("call_limit_per_day"),
      phone_type= rs.stringOpt("phone_type").map((phone_type)=>PhoneType.fromString(phone_type).get),
      country = rs.stringOpt("country"),
      enable_forwarding = rs.boolean("enable_forwarding"),
      forward_to = rs.stringOpt("forward_to"),
      service_provider = CallingServiceProvider.fromString(rs.string("service_provider")).get,
      org_sub_account_uuid = rs.stringOpt("org_sub_account_uuid").map(subUuid => SubAccountUuid(uuid = subUuid)),
      is_active = rs.boolean("is_active"),
      record_call = rs.boolean("record_call"),
      caller_id = rs.stringOpt("caller_id").map(cid => CallerIdUuid(uuid = cid))
    )
  }

  def verifiedCallerIdsFromDB(rs: WrappedResultSet): VerifiedCallerIds = {

    VerifiedCallerIds(
      uuid = CallerIdUuid(uuid = rs.string("uuid")),
      phone_number = PhoneNumber(phone_number = rs.string("phone_number")),
      name = rs.string("friendly_name"),
      owner_account_id = AccountId(id = rs.long("added_by")),
      team_id = TeamId(id = rs.long("team_id")),
      status = CallerIdVerificationStatus.fromString(key =  rs.string("status")).get
    )
  }

  def callParticipantUpdateDetailsFomDB(rs: WrappedResultSet): ParticipantUpdateDetails = {
    ParticipantUpdateDetails(
      call_conference_sid = rs.string("call_conference_sid"),
      call_sp_sid = CallSID(sid = rs.string("call_sp_sid")),
      status = rs.string("status"),
      latest_participant_mode = rs.string("latest_participant_mode"),
      was_listening = rs.booleanOpt("was_listening"),
      listened_at = rs.jodaDateTimeOpt("listened_at"),
      was_coaching = rs.booleanOpt("was_coaching"),
      coached_at = rs.jodaDateTimeOpt("coached_at"),
      was_barged_in = rs.booleanOpt("was_barged_in"),
      barged_in_at = rs.jodaDateTimeOpt("barged_in_at"),
      added_to_conference_at = rs.jodaDateTimeOpt("added_to_conference_at"),
      is_prospect = rs.booleanOpt("is_prospect")
    )
  }

  def activeCallParticipantDetails(rs: WrappedResultSet): OngoingCallParticipantNotificationObject = {
    val prospectId = rs.longOpt("prospect_id")
    val account_id = rs.longOpt("owner_account_id")
    OngoingCallParticipantNotificationObject(
      participant_uuid = CallParticipantUuid(participant_uuid = rs.string("call_participant_uuid")),
      conference_uuid = ConferenceUuid(conf_uuid = rs.string("call_conference_uuid")),
      participant_account_id = account_id.map(aid => AccountId(id = aid)),
      ongoingCallProspectDetails = prospectId.map(pid =>
        OngoingCallProspectDetails(
          prospect_id = ProspectId(id = pid),
          first_name = rs.stringOpt("prospect_first_name"),
          last_name = rs.stringOpt("prospect_last_name"),
          phone_number = rs.stringOpt("phone")

        )),
      task_id = rs.stringOpt("task_uuid"),
      latest_participation_mode = CallParticipationMode.fromString(rs.string("latest_participant_mode")).get,
      participant_first_name = rs.stringOpt("participant_first_name"),
      calling_device = rs.stringOpt("calling_device").map(device => CallingDevice.fromString(device).get),
      participant_last_name = rs.stringOpt("participant_last_name"),
      call_sid = rs.stringOpt("call_sp_sid").map(s => CallSID(s)),
    )
  }

}
