package api

import api.accounts.Account
import play.api.Logging
import play.api.libs.json.JsValue
import utils.Helpers
import utils.helpers.LogHelpers

import scala.util.{Failure, Success, Try}

object CacheKeys extends Enumeration {

  type CacheKeys = Value

  val PAID_ACCOUNTS = Value("master_dashboard_paid_accounts_01")
  val PAID_RISKY_ACCOUNTS = Value("master_dashboard::paidrisky")
  val TRIAL_ACCLOUNTS = Value("master_dashboard_trial_accounts_01")
  val SENDING_IPS = Value("master_dashboard_sending_ips_01")
  val TRACKING_HOSTS = Value("master_dashboard_tracking_hosts_01")

  //  given format = EnumUtils.enumFormat(CacheKeys)

}

/*
21st March 2022: this is the old CacheService that relied on the play-redis dependency.
During migration to Play 2.6 and upwards that plugin was causing issues so, to remove that dependency
we are now using CacheServiceJedis internally here.
 */
class CacheService(cacheServiceJedis: CacheServiceJedis) extends Logging {

  val keyPrefix = AppConfig.redisKeyPrefix


  def apiAccountKey(apiKey: String) = s"$keyPrefix::apiKey::$apiKey"


  // account mapped by api key

  def setApiAccount(account: Account, apiKey: String): Unit = {
    Try {
      cacheServiceJedis.set(
        key = apiAccountKey(apiKey),
        value = account,
        expireInSeconds = 900
      )(Account.writesForCache)
    } match {

      case Failure(e) =>
        logger.error(s"CacheService.setApiAccount FATAL: ${LogHelpers.getStackTraceAsString(e)} :: $account")

      case Success(_) =>
      // do nothing
    }
  }

  def getApiAccount(apiKey: String): Option[Account] = {


    cacheServiceJedis.get[Account](
      key = apiAccountKey(apiKey)
    ) match {
      case Failure(e) =>
        logger.error(s"CacheService.getApiAccount FATAL $apiKey: ${LogHelpers.getStackTraceAsString(e)}")
        removeApiAccount(apiKey = apiKey)
        None

      case Success(accOpt) =>
        accOpt
    }

  }

  def removeApiAccount(apiKey: String): Unit = {
    cacheServiceJedis.deleteKey(
      key = apiAccountKey(apiKey)
    ) match {

      case Failure(e) =>
        logger.error(s"CacheService.removeApiAccount FATAL $apiKey: ${LogHelpers.getStackTraceAsString(e)}")


      case Success(_) =>
      // do nothing
    }
  }

  def setJsValue(key: String, data: JsValue, expirySeconds: Int = 3600): Unit = {
    Try {

      cacheServiceJedis.set(
        key = key,
        value = data,
        expireInSeconds = expirySeconds
      )

    } match {

      case Failure(e) =>
        logger.error(s"CacheService.setJsValue FATAL key: $key :: ${LogHelpers.getStackTraceAsString(e)} :: $data")

      case Success(_) =>
      // do nothing
    }
  }


  def getJsValue(key: String): Option[JsValue] = {

    cacheServiceJedis.get[JsValue](key) match {

      case Failure(e) =>
        logger.error(s"CacheService.getJsValue FATAL $key: ${LogHelpers.getStackTraceAsString(e)}")
        None

      case Success(None) =>
        None

      case Success(Some(data)) =>
        Some(data)
    }

  }

  def removeJsValue(key: String): Unit = {
    cacheServiceJedis.deleteKey(key = key) match {

      case Failure(e) =>
        logger.error(s"CacheService.removeJsValue FATAL $key: ${LogHelpers.getStackTraceAsString(e)}")


      case Success(_) =>
      // do nothing
    }


  }


  /// START: get / set normal string values ///


  def setStr(key: String, value: String, expirySeconds: Int = 3600): Unit = {
    Try {

      cacheServiceJedis.set(
        key = key,
        value = value,
        expireInSeconds = expirySeconds
      )

    } match {

      case Failure(e) =>
        logger.error(s"CacheService.setStr FATAL key: $key :: $value :: ${LogHelpers.getStackTraceAsString(e)}")

      case Success(_) =>
      // do nothing
    }
  }


  def getStr(key: String): Option[String] = {

    cacheServiceJedis.get[String](key) match {

      case Failure(e) =>
        logger.error(s"CacheService.getStr FATAL $key: ${LogHelpers.getStackTraceAsString(e)}")
        None

      case Success(None) =>
        None

      case Success(Some(data)) =>
        Some(data)
    }

  }

  def removeStr(key: String): Unit = {
    cacheServiceJedis.deleteKey(
      key = key
    ) match {

      case Failure(e) =>
        logger.error(s"CacheService.removeStr FATAL $key: ${LogHelpers.getStackTraceAsString(e)}")


      case Success(_) =>
      // do nothing
    }
  }

  /// END: get / set normal string values ///


}

/*
object CacheService {

  val keyPrefix = AppConfig.redisKeyPrefix

  def sessionAccountKey(accountId: Long) = s"$keyPrefix::accountId::$accountId"

}
*/



