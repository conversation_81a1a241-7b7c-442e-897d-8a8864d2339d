package api.tags.services

import api.tags.AddTagsToObject
import api.tags.TagService
import api.tags.dao.TagDAOTrait
import api.tags.models.{TagAndUuid, TagType}
import api.triggers.AddOrRemoveTagAction
import utils.{Help<PERSON>, SRLogger}
import utils.helpers.LogHelpers

import scala.util.{Failure, Success, Try}

case class AddTagToObject[T <: TagType](
  objectId: Long,
  tag: T
)

trait TagServiceTrait[T <: TagType] {

  protected val tagDAO: TagDAOTrait[T]

  def getSampleTagsForNewUsers: List[T]

  final def findTags(
    tags: Seq[String],
    teamId: Long
  ): Try[Seq[T]] = {

    tagDAO.findTagsFromDB(
      tags = tags,
      teamId = teamId
    )

  }

  final def findTagsByObjectId(
    teamId: Long,
    objectId: Long
  ): Try[Seq[T]] = {

    tagDAO.getTagsByObjectId(
      objectId = objectId,
      teamId = teamId
    )

  }

  final def getAllTags(
    teamId: Long
  ): Try[Seq[T]] = {

    findTags(
      teamId = teamId,

      // handling empty sequence as a get-all case in the dao
      tags = Seq()
    )

  }

  final def findOrCreateTags(
    tags: Seq[TagAndUuid],
    teamId: Long,
    accountId: Long,
    logger: SRLogger
  ): Try[Seq[T]] = {

    findTags(
      tags = tags.map(_.tag),
      teamId = teamId
    ) match {
      case Failure(e1) =>
        Failure(e1)

      case Success(existingTags) =>

        val existingTagNames = existingTags.map(_.getTagName.toLowerCase.trim).distinct
        val nonExistingTags = tags.filter(t => !existingTagNames.contains(t.tag.toLowerCase.trim)).distinct

        val allTags: Seq[T] = if (nonExistingTags.isEmpty) {
          existingTags
        } else {
          tagDAO.insertNewTagsInDB(
            tags = nonExistingTags,
            teamId = teamId,
            accountId = accountId
          ) match {

            case Failure(e2) =>

              logger.error(s"FATAL findOrCreateTags: $tags :: tid_$teamId :: aid_$accountId :: ${LogHelpers.getStackTraceAsString(e2)}")

              throw new Exception("Error while creating tags. Please try again")

            case Success(newTags) =>
              existingTags ++ newTags

          }

        }

        Success(allTags)

    }


  }

  final def addTagsToObjects(
    data: Seq[AddTagsToObject],
    teamId: Long,
    accountId: Long,
    logger: SRLogger
  ): Try[List[Long]] = {
    
    given srLogger: SRLogger = logger // fixme given

    if (data.isEmpty) Success(List()) else {

      val tagList = data.flatMap(_.tags).distinct
      // validate tag is alphanumeric etc. only when adding tags
      val invalidTag = tagList.find(tag => !TagService.validateTagName(tag.tag))

      if (invalidTag.isDefined) {

        Failure(new Exception(s"Invalid tag: ${invalidTag.get} :: only alphanumeric names with space/hyphens allowed"))

      } else Try {


        val tagObjs = data.filter(_.tags.nonEmpty)

        val foundTags = findOrCreateTags(
          tags = tagObjs.flatMap(_.tags).distinct,
          teamId = teamId,
          accountId = accountId,
          logger = logger
        ).get


        tagObjs
          .flatMap(tagObj => {

            tagObj.tags
              .map(tagObjTag => {

                AddTagToObject(

                  // tags need be case-insensitive
                  tag = foundTags.find(foundTag => {
                    foundTag.getTagName.toLowerCase.trim == tagObjTag.tag.toLowerCase.trim
                  }).get,

                  objectId = tagObj.objectId
                )
              })


          })
          .grouped(500)
          .toList
          .flatMap(tagObjGroup => {

            tagDAO.assignTagsToObjectsInDB(
              data = tagObjGroup,
              teamId = teamId,
              accountId = accountId,
              logger = logger
            )
              .get

          })
      }
    }
  }


  final def addTagsToObjects(
                              objectIds: Seq[Long],
                              tags: Seq[TagAndUuid],
                              teamId: Long,
                              accountId: Long,
                              logger: SRLogger
  ): Try[List[Long]] = {

    addTagsToObjects(
      data = objectIds.map(oid => {
        AddTagsToObject(
          objectId = oid,
          tags = tags
        )
      }),
      teamId = teamId,
      accountId = accountId,
      logger = logger
    )

  }

  final def removeTagsFromObjects(
    objectIds: Seq[Long],
    tags: Seq[String],
    teamId: Long,
    accountId: Long,
    logger: SRLogger
  ): Try[Int] = Try {


    if (objectIds.isEmpty || tags.isEmpty) 0 else {

      val existingTags = findTags(
        teamId = teamId,
        tags = tags
      ).get

      if (existingTags.isEmpty) {
        logger.info(s"FATAL Tag.removeTagsFromObjects $objectIds : $tags : $teamId : $accountId : empty existingTags")
        0
      } else {

        objectIds
          .grouped(1000)
          .toList
          .map(oidGroup => {

            tagDAO.deleteTagsFromObjectsInDB(
              objectIds = oidGroup,
              existingTags = existingTags
            )
              .get

          })
          .sum
      }

    }
  }

  final def updateTagsForObjects(
    action: AddOrRemoveTagAction.Value,
    objectIdsWithPermission: Seq[Long],
    tags: Seq[TagAndUuid],
    teamId: Long,
    accountId: Long,
    logger: SRLogger
  ): Try[Int] = {

    action match {

      case AddOrRemoveTagAction.ADD =>

        addTagsToObjects(
          tags = tags,
          objectIds = objectIdsWithPermission,
          teamId = teamId,
          accountId = accountId,
          logger = logger
        )
          .map(addedIds => {

            // FIXME: add prospectEvents, webhook, trigger calls
            addedIds.length
          })

      case AddOrRemoveTagAction.REMOVE =>

        removeTagsFromObjects(
          objectIds = objectIdsWithPermission,
          tags = tags.map(_.tag),
          teamId = teamId,
          accountId = accountId,
          logger = logger
        )
      // FIXME: add prospectEvents, webhook, trigger calls


    }


  }
}