package api.tags

import api.accounts.{PermType, PermissionMethods, PermissionRequest, PermissionUtils}
import api.campaigns.services.CampaignService
import api.campaigns.{CampaignRequest, CampaignStepDAO, CampaignStepVariantDAO, CampaignUtils}
import api.prospects.dao_service.{ProspectDAOService, ProspectDAOServiceV2}
import api.prospects.service.ProspectServiceV2
import api.tags.models.{AddOrRemoveCampaignsTagsAPIRequest, AddOrRemoveProspectTagsAPIRequest, AddOrRemoveTagsAPIRequestForZapier, CampaignTagUuid, ProspectTag, TagAndUuid, TagType, TagUuid}
import api.tags.services.{CampaignTagService, ProspectTagService}
import api.triggers.AddOrRemoveTagAction
import play.api.libs.json._
import play.api.mvc.{AnyContent, BaseController, ControllerComponents}
import utils.SRLogger
import utils.uuid.SrUuidUtils
import utils.uuid.services.SrUuidService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class TagController(
                     protected val controllerComponents: ControllerComponents,
                     prospectDAOService: ProspectDAOService,
                     prospectDAOServiceV2: ProspectDAOServiceV2,
                     protected val campaignService: CampaignService,
                     prospectTagService: ProspectTagService,
                     campaignTagService: CampaignTagService,
                     override protected val campaignStepVariantDAO: CampaignStepVariantDAO,
                     permissionUtils: PermissionUtils,
                     override protected val campaignStepDAO: CampaignStepDAO,
                     tagService: TagService,
                     prospectServiceV2: ProspectServiceV2,
                     srUuidUtils: SrUuidUtils,
                     prospectTagDAOLegacy: ProspectTagDAOLegacy,
                     override protected val srUuidService: SrUuidService

                   ) extends BaseController
  with CampaignUtils {
  private implicit val ec: ExecutionContext = controllerComponents.executionContext

  def addORRemoveTags(v: String, module: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.JUST_LOGGEDIN,
    tidOpt = tid
  ).async(parse.json) { request =>


    val l = request.loggedinAccount
    val taOpt = request.actingTeamAccount
    val Res = request.Response
    given Logger: SRLogger = request.Logger


    // if DELETE HTTP request, remove tags
    // else Add tags
    val addOrRemove: AddOrRemoveTagAction.Value = if (request.method.toLowerCase.trim == "delete") {
      AddOrRemoveTagAction.REMOVE
    } else {
      AddOrRemoveTagAction.ADD
    }

    if (tid.isEmpty || taOpt.isEmpty) {

      Future.successful(Res.ForbiddenError("You are not authorized"))

    } else {

      request.body.validate[AddOrRemoveProspectTagsAPIRequest] match {
        case JsError(err) =>
          Logger.fatal(s"jserror:: $err")

          Future.successful(Res.JsValidationError(errors = err, requestBody = Some(request.body)))

        case JsSuccess(data, _) =>

          // validate tag is alphanumeric etc. only when adding tags
          val invalidTag = if (addOrRemove == AddOrRemoveTagAction.ADD) data.tags.find(tag => !TagService.validateTagName(tag)) else None

          if (invalidTag.isDefined) {
            Future.successful(
              Res.BadRequestError(s"Invalid tag: ${invalidTag.get} :: only alphanumeric names with space/hyphens allowed")
            )

          } else {

            val ta = taOpt.get

            // _ at end denotes a curried function ::
            // REF: https://dzone.com/articles/currying-functions-in-scala-1
            val getPermittedAccountIds = PermissionMethods
              .getPermittedAccountIdsForAccountAndPermission(
                loggedinAccount = l,
                actingTeamId = ta.team_id,
                actingAccountId = ta.user_id,
                version = v,
                Logger = Logger
              )

            val permittedAccountIds = getPermittedAccountIds(PermType.EDIT_PROSPECTS)

            val uniqProspectIds = data.prospect_ids.distinct
            prospectDAOServiceV2.checkIfUserHasPermissionForProspects(
              prospectsIds = uniqProspectIds,
              permittedAccountIds = permittedAccountIds,
              teamId = ta.team_id
            ) match {

              case Failure(e) =>
                Future.successful(
                  Res.ServerError("Please try again", e = Some(e))
                )

              case Success(prospectIdsWithPermission) =>

                if (prospectIdsWithPermission.length != uniqProspectIds.length) {
                  Future.successful(
                    Res.BadRequestError(
                      "You do not have permission to edit all/some of the selected prospects"
                    )
                  )
                } else {

                  val tagsAndUuids: Seq[TagAndUuid] = data.tags.map(t => {
                    val camapaign_tag_uuid = srUuidUtils.generateTagsUuid()
                    TagAndUuid(tag = t, uuid = CampaignTagUuid(camapaign_tag_uuid))
                  })

                  tagService.updateTagsForProspects(
                    action = addOrRemove,
                    prospectIdsWithPermission = prospectIdsWithPermission,
                    tags = tagsAndUuids,
                    teamId = ta.team_id,
                    accountId = ta.user_id
                  ) match {

                    case Failure(e) =>

                      Future.successful(Res.ServerError("Please try again", e = Some(e)))

                    case Success(_) =>

                      Future.successful(Res.Success("Success!", Json.obj()))

                  }

                }
            }
          }

      }
    }
  }

  def addTagsToCampaigns(tid: Option[Long]) = permissionUtils.checkPermission(
    permission = PermType.EDIT_CAMPAIGNS,
    tidOpt = tid
  ).async(parse.json) { request =>


    val taOpt = request.actingTeamAccount
    val Res = request.Response
    given Logger: SRLogger = request.Logger
    val permittedAccountIds = request.permittedAccountIds


    if (tid.isEmpty || taOpt.isEmpty) {

      Future.successful(Res.ForbiddenError("You are not authorized"))

    } else {

      request.body.validate[AddOrRemoveCampaignsTagsAPIRequest] match {
        case JsError(err) =>

          Future.successful(Res.JsValidationError(errors = err, requestBody = Some(request.body)))

        case JsSuccess(data, _) =>

          // validate tag is alphanumeric etc. only when adding tags
          val invalidTag = data.tags.find(tag => !TagService.validateTagName(tag))

          if (invalidTag.isDefined) {
            Future.successful(
              Res.BadRequestError(s"Invalid tag: ${invalidTag.get} :: only alphanumeric names with space/hyphens allowed")
            )

          } else {

            val ta = taOpt.get
            val teamId = ta.team_id
            val accountId = ta.user_id


            val uniqIds = data.campaign_ids.distinct
            campaignService.checkIfUserHasPermissionForCampaigns(
              campaignIds = uniqIds,
              permittedAccountIds = permittedAccountIds,
              teamId = ta.team_id
            ) match {

              case Failure(e) =>
                Future.successful(
                  Res.ServerError("Please try again", e = Some(e))
                )

              case Success(idsWithPermission) =>

                if (idsWithPermission.length != uniqIds.length) {
                  Future.successful(
                    Res.BadRequestError(
                      "You do not have permission to edit all/some of the selected campaigns"
                    )
                  )
                } else {

                  val tagsAndUuids: Seq[TagAndUuid] = data.tags.map(t => {
                    val camapaign_tag_uuid = srUuidUtils.generateTagsUuid()
                    TagAndUuid(tag = t, uuid = CampaignTagUuid(camapaign_tag_uuid))
                  })

                  campaignTagService.addTagsToObjects(
                    objectIds = idsWithPermission,
                    tags = tagsAndUuids,
                    teamId = teamId,
                    accountId = accountId,
                    logger = Logger
                  ) match {

                    case Failure(e) =>

                      Future.successful(Res.ServerError("Please try again", e = Some(e)))

                    case Success(_) =>

                      Future.successful(Res.Success("Success!", Json.obj()))

                  }

                }
            }
          }

      }
    }
  }

  def removeTagsFromCampaigns(tid: Option[Long]) = permissionUtils.checkPermission(
    permission = PermType.EDIT_CAMPAIGNS,
    tidOpt = tid
  ).async(parse.json) { request =>


    val taOpt = request.actingTeamAccount
    val Res = request.Response
    val Logger = request.Logger
    val permittedAccountIds = request.permittedAccountIds


    if (tid.isEmpty || taOpt.isEmpty) {

      Future.successful(Res.ForbiddenError("You are not authorized"))

    } else {

      request.body.validate[AddOrRemoveCampaignsTagsAPIRequest] match {
        case JsError(err) =>

          Future.successful(Res.JsValidationError(errors = err, requestBody = Some(request.body)))

        case JsSuccess(data, _) =>

          val ta = taOpt.get
          val teamId = ta.team_id
          val accountId = ta.user_id


          val uniqIds = data.campaign_ids.distinct
          campaignService.checkIfUserHasPermissionForCampaigns(
            campaignIds = uniqIds,
            permittedAccountIds = permittedAccountIds,
            teamId = ta.team_id
          ) match {

            case Failure(e) =>
              Future.successful(
                Res.ServerError("Please try again", e = Some(e))
              )

            case Success(idsWithPermission) =>

              if (idsWithPermission.length != uniqIds.length) {
                Future.successful(
                  Res.BadRequestError(
                    "You do not have permission to edit all/some of the selected campaigns"
                  )
                )
              } else {

                campaignTagService.removeTagsFromObjects(
                  objectIds = idsWithPermission,
                  tags = data.tags,
                  teamId = teamId,
                  accountId = accountId,
                  logger = Logger
                ) match {

                  case Failure(e) =>

                    Future.successful(Res.ServerError("Please try again", e = Some(e)))

                  case Success(_) =>

                    Future.successful(Res.Success("Success!", Json.obj()))

                }

              }
          }

      }
    }
  }


  // new api: 2nd Nov 2021
  def findAllTags(module: String, tid: Option[Long]) = permissionUtils.checkPermission(
    permission = PermType.JUST_LOGGEDIN,
    tidOpt = tid
  ) { (request: PermissionRequest[AnyContent]) =>

    val t = request.actingTeamAccount
    val Logger = request.Logger
    val Res = request.Response

    if (t.isEmpty) {

      Res.BadRequestError("Invalid team")

    } else {

      val tagServiceOpt = if (module == "prospects") Some(prospectTagService)
      else if (module == "campaigns") Some(campaignTagService)
      else None

      if (tagServiceOpt.isEmpty) {
        Res.BadRequestError("Invalid API Request")
      } else {

        val tagService = tagServiceOpt.get

        tagService.getAllTags(
          teamId = t.get.team_id
        ) match {

          case Failure(e) =>
            Res.ServerError(err = e)

          case Success(tags) =>

            // for new teams, send two sample tags as example
            val response: List[TagType] = if (tags.isEmpty) {

              tagService.getSampleTagsForNewUsers

            } else {
              tags
                .sortBy(_.getTagName)
                .toList
            }

            Res.Success("Tags found", Json.obj(
              "tags" -> response
            ))

        }
      }

    }
  }

  // new api: 2nd Nov 2021
  def findTagsByCampaignId(
                            campaign_id: Long,
                            tid: Option[Long]
                          ) = (
    permissionUtils.checkPermission(
      permission = PermType.VIEW_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(campaignId = campaign_id)
    ) { (request: CampaignRequest[AnyContent]) =>

    val t = request.actingTeamAccount
    val Res = request.Response

    campaignTagService.findTagsByObjectId(
      teamId = t.team_id,
      objectId = request.campaign.id
    ) match {

      case Failure(e) =>
        Res.ServerError(err = e)

      case Success(tags) =>

        Res.Success("Tags found", Json.obj(
          "tags" -> tags
        ))

    }
  }

  // legacy api
  def getAllTags(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.JUST_LOGGEDIN,
    tidOpt = tid
  ) { (request: PermissionRequest[AnyContent]) =>

    val t = request.actingTeamAccount
    val Logger = request.Logger
    val Res = request.Response

    if (t.isEmpty) {

      Res.BadRequestError("Invalid team")

    } else {
      prospectTagDAOLegacy.getTags(teamId = t.get.team_id) match {

        case Failure(e) =>
          Logger.fatal(s"Tag.getTags", err = e)
          Res.ServerError(s"There was an error.", e = Some(e))

        case Success(tags) =>

          // for new teams, send two sample tags as example
          val response: List[ProspectTag] = if (tags.isEmpty) {
            prospectTagService.getSampleTagsForNewUsers
          } else {
            tags
              .sortBy(_.tag)
          }

          Res.Success("Tags found", Json.obj(
            "tags" -> response
          ))

      }
    }
  }


  // use case : TagController.addORRemoveTagsForZapier(v: String, module: String) POST     /api/:v/:module/tags/by_email
  // use case : TagController.addORRemoveTagsForZapier(v: String, module: String) DELETE   /api/:v/:module/tags/by_email
  def addORRemoveTagsForZapier(v: String, module: String) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = None,
    apiAccess = true,
    webAccess = false
  ).async(parse.json) { request =>


    val l = request.loggedinAccount
    val taOpt = request.actingTeamAccount
    given Logger: SRLogger = request.Logger
    val Res = request.Response

    Logger.info(msg = s"[Zapier API called] org_id :: ${request.loggedinAccount.org.id} module_$module")

    // if DELETE HTTP request, remove tags
    // else Add tags
    val addOrRemove: AddOrRemoveTagAction.Value = if (request.method.toLowerCase.trim == "delete") {
      AddOrRemoveTagAction.REMOVE
    } else {
      AddOrRemoveTagAction.ADD
    }

    if (taOpt.isEmpty) {

      Future.successful(Res.ForbiddenError("You are not authorized"))

    } else {

      request.body.validate[AddOrRemoveTagsAPIRequestForZapier] match {
        case JsError(err) =>
          Logger.fatal(s"jserror:: $err")

          Future.successful(Res.JsValidationError(errors = err, requestBody = Some(request.body)))

        case JsSuccess(data, _) =>

          val tags = data.tags.trim.split(",").toIndexedSeq

          if (tags.nonEmpty) {

            // validate tag is alphanumeric etc. only when adding tags
            val invalidTag = if (addOrRemove == AddOrRemoveTagAction.ADD) tags.find(tag => !TagService.validateTagName(tag)) else None

            if (invalidTag.isDefined) {
              Future.successful(
                Res.BadRequestError(s"Invalid tag: ${invalidTag.get} :: only alphanumeric names with space/hyphens allowed")
              )

            } else {

              val ta = taOpt.get

              // _ at end denotes a curried function ::
              // REF: https://dzone.com/articles/currying-functions-in-scala-1
              val getPermittedAccountIds = PermissionMethods
                .getPermittedAccountIdsForAccountAndPermission(
                  loggedinAccount = l,
                  actingTeamId = ta.team_id,
                  actingAccountId = ta.user_id,
                  version = v,
                  Logger = Logger
                )

              module match {

                case "prospects" =>

                  if (data.prospect_emails.isEmpty) {

                    Future.successful(Res.BadRequestError("prospect_emails is empty"))

                  } else {

                    val permittedAccountIds = getPermittedAccountIds(PermType.EDIT_PROSPECTS)

                    prospectServiceV2.findByEmailAndAccountId(
                      emails = data.prospect_emails,
                      accountIds = permittedAccountIds,
                      teamId = ta.team_id,
                      Logger = Logger
                    ) match {

                      case Failure(e) =>
                        Future.successful(
                          Res.ServerError("Please try again", e = Some(e))
                        )

                      case Success(prospects) =>

                        val prospect_ids = prospects.map(p => p.id)

                        if (prospects.isEmpty) {
                          Future.successful(
                            Res.BadRequestError(
                              "You do not have permission to edit all/some of the selected prospects"
                            )
                          )
                        } else {

                          val tagsAndUuids: Seq[TagAndUuid] = tags.map(t => {
                            val camapaign_tag_uuid = srUuidUtils.generateTagsUuid()
                            TagAndUuid(tag = t, uuid = CampaignTagUuid(camapaign_tag_uuid))
                          })

                          tagService.updateTagsForProspects(
                            action = addOrRemove,
                            prospectIdsWithPermission = prospect_ids,
                            tags = tagsAndUuids,
                            teamId = ta.team_id,
                            accountId = ta.user_id
                          ) match {

                            case Failure(e) =>

                              Future.successful(Res.ServerError("Please try again", e = Some(e)))

                            case Success(_) =>

                              Future.successful(Res.Success("Success!", Json.obj("saved" -> true)))

                          }

                        }
                    }
                  }


                case _ =>
                  Future.successful(Res.NotFoundError("API not found"))
              }
            }
          } else {

            Future.successful(Res.BadRequestError("Tags is empty"))

          }
      }
    }
  }

}