package api.tags.models

import play.api.libs.json.{Js<PERSON><PERSON><PERSON>, <PERSON><PERSON>, OWrites, Reads, Writes}


sealed trait TagType {
  def getTagName: String
}

case class ProspectTag(
  tag_id: Long,
  tag: String,
  tag_uuid: ProspectTagUuid
) extends TagType {
  override def getTagName: String = tag
}
object ProspectTag {
  given writes: OWrites[ProspectTag] = Json.writes[ProspectTag]
  given reads: Reads[ProspectTag] = Json.reads[ProspectTag]

}

case class CampaignTag(
  tag_id: Long,
  tag: String,
  tag_uuid: CampaignTagUuid
) extends TagType {
  override def getTagName: String = tag
}
object CampaignTag {
  given writes: OWrites[CampaignTag] = Json.writes[CampaignTag]
  given reads: Reads[CampaignTag] = Json.reads[CampaignTag]
}


object TagType {
  given writes: Writes[TagType] = new Writes[TagType] {
    def writes(tag: TagType): JsValue = {

      tag match {
        case t: ProspectTag => Json.toJson(t)
        case t: CampaignTag => Json.toJson(t)
      }

    }
  }
}
