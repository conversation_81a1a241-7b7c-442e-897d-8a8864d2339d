package api.tags.models

import play.api.libs.json.{<PERSON><PERSON>, Reads}


/////////////// API case classes //////

case class AddOrRemoveProspectTagsAPIRequest(
  prospect_ids: Seq[Long] = Seq(),
  tags: Seq[String]
)
object AddOrRemoveProspectTagsAPIRequest {
  given reads: Reads[AddOrRemoveProspectTagsAPIRequest] = Json.reads[AddOrRemoveProspectTagsAPIRequest]
}


case class AddOrRemoveCampaignsTagsAPIRequest(
  campaign_ids: Seq[Long],
  tags: Seq[String]
)
object AddOrRemoveCampaignsTagsAPIRequest {
  given reads: Reads[AddOrRemoveCampaignsTagsAPIRequest] = Json.reads[AddOrRemoveCampaignsTagsAPIRequest]
}



case class AddOrRemoveTagsAPIRequestForZapier(
  prospect_emails: Seq[String] = Seq(),
  tags: String
)
object AddOrRemoveTagsAPIRequestForZapier {
  given reads: Reads[AddOrRemoveTagsAPIRequestForZapier] = Json.reads[AddOrRemoveTagsAPIRequestForZapier]
}