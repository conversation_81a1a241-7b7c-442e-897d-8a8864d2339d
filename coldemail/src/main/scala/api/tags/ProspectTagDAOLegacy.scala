package api.tags

import api.accounts.TeamId
import api.accounts.models.AccountId
import api.prospects.dao.ProspectIdAndPotentialDuplicateProspectId
import api.prospects.models.ProspectId
import api.tags.models.{ProspectTag, ProspectTagData, ProspectTagUuid, ProspectsTagId, TagAndUuid}
import play.api.Logging
import scalikejdbc.jodatime.JodaWrappedResultSet.fromWrappedResultSetToJodaWrappedResultSet
import scalikejdbc.{AutoSession, DB, WrappedResultSet, _}
import utils.Helpers
import utils.dbutils.SQLUtils

import scala.util.{Failure, Success, Try}
import utils.helpers.LogHelpers




case class AddTagsToObject(
  objectId: Long,
  tags: Seq[TagAndUuid]
)

case class InternalAddTagToObject(
  objectId: Long,
  tagId: Long
)

object ProspectTagDAOLegacy {
  def fromDb(rs: WrappedResultSet) = {

    ProspectTag(
      tag_id = rs.long("id"),
      tag = rs.string("tag"),
      tag_uuid = ProspectTagUuid(rs.string("uuid"))
    )
  }
}

class ProspectTagDAOLegacy extends Logging {

  implicit val session: AutoSession.type = AutoSession

  def getTags(
    teamId: Long,
    tags: Seq[String] = Seq() // optional
  ): Try[List[ProspectTag]] = Try {

    DB readOnly { implicit session =>

      val tagWhereClause = if (tags.nonEmpty) {

        sqls" AND lower(tags.tag) IN ${SQLUtils.generateSQLValuesClause({tags.map(_.toLowerCase.trim)})} "

      } else sqls" "

      sql"""
          SELECT id, tag, uuid from tags
          WHERE team_id = $teamId
          $tagWhereClause
          ORDER BY id DESC
        """
        .map(ProspectTagDAOLegacy.fromDb)
        .list
        .apply()

    }
  }

  /*
  def getTagById(
                  teamId: Long,
                  tagId: Long
                ): Option[Tag] = DB readOnly { implicit session =>

    sql"""
          SELECT id, tag from tags
          WHERE team_id = $teamId
          AND id = $tagId
        """
      .map(fromDb)
      .single
      .apply()

  }
  */

  private def _insertNewTags(
    tags: Seq[TagAndUuid],
    teamId: Long,
    accountId: Long
  ): Try[Seq[ProspectTag]] = Try {
    DB localTx { implicit session =>

      if (tags.isEmpty) Seq()
      else {

        var parameters = List[Any]()

        val valuePlaceholder = tags.map(tagNameAndUuid => {

          parameters = parameters ::: List(
            tagNameAndUuid.tag.trim,
            accountId,
            teamId,

            tagNameAndUuid.uuid.toString
          )

          s"""
            (
              ?,
              ?,
              ?,

              ?
            )

          """
        }).mkString(", ")

        SQL(s"""
          INSERT INTO tags
          (
            tag,
            account_id,
            team_id,

            uuid
          )
          VALUES $valuePlaceholder
          ON CONFLICT DO NOTHING
          RETURNING *;
        """)
          .bind(parameters*)
          .map(ProspectTagDAOLegacy.fromDb)
          .list
          .apply()


      }
    }

  }

  def findOrCreateTags(
    tags: Seq[TagAndUuid],
    teamId: Long,
    accountId: Long
  ): Try[List[ProspectTag]] = Try {

    // tags need to be checked in a case-insensitive manner
    val existingTags = getTags(
      teamId = teamId,
      tags = tags.map(_.tag)
    ).get

    val existingTagNames = existingTags.map(_.tag.toLowerCase.trim).distinct
    val nonExistingTags = tags.filter(t => !existingTagNames.contains(t.tag.toLowerCase.trim)).distinct

    val allTags = if (nonExistingTags.isEmpty) {
      existingTags
    } else {
      _insertNewTags(
        tags = nonExistingTags,
        teamId = teamId,
        accountId = accountId
      ) match {

        case Failure(e) =>

          logger.error(s"FATAL findOrCreateTags: $tags :: tid_$teamId :: aid_$accountId :: ${LogHelpers.getStackTraceAsString(e)}")

          throw new Exception("Error while creating tags. Please try again")

        case Success(newTags) =>
          existingTags ++ newTags


      }
    }

    allTags

  }

  def addTagsToObjects(
    data: Seq[AddTagsToObject],
    teamId: Long,
    accountId: Long
  ): Try[List[Long]] = Try {

    if (data.isEmpty) List() else {

      val tagObjs = data.filter(_.tags.nonEmpty)

      val foundTags = findOrCreateTags(
        tags = tagObjs.flatMap(_.tags).distinct,
        teamId = teamId,
        accountId = accountId
      ).get


      tagObjs
        .grouped(500)
        .toList
        .flatMap(tagObjGroup => {
          DB autoCommit { implicit session =>

            var parameters = List[Any]()

            val valuePlaceholder = tagObjGroup
              .flatMap(tagObj => {

                tagObj.tags.map(tagObjTag => {
                  InternalAddTagToObject(

                    // tags need be case-insensitive
                    tagId = foundTags.find(foundTag => foundTag.tag.toLowerCase.trim == tagObjTag.tag.toLowerCase.trim).get.tag_id,
                    objectId = tagObj.objectId
                  )
                })

              })
              .map(t => {

                parameters = parameters ::: List(
                  t.tagId,
                  t.objectId,
                  accountId
                )

                s"""
            (
              ?,
              ?,
              ?
            )

          """
              }).mkString(", ")

            SQL(
              s"""
          INSERT INTO tags_prospects
          (
            tag_id,
            prospect_id,
            account_id
          )
          VALUES $valuePlaceholder
          ON CONFLICT DO NOTHING
          RETURNING prospect_id;
        """)
              .bind(parameters*)
              .map(rs => rs.long("prospect_id"))
              .list              .apply()

          }
        })
    }
  }

  def removeTagsFromObjects(
    objectIds: Seq[Long],
    tags: Seq[String],
    teamId: Long,
    accountId: Long
  ): Try[Int] = Try {


    if (objectIds.isEmpty || tags.isEmpty) 0 else {

      val existingTags = getTags(
        teamId = teamId,
        tags = tags
      ).get

      if (existingTags.isEmpty) {
        logger.info(s"FATAL Tag.removeTagsFromObjects $objectIds : $tags : $teamId : $accountId : empty existingTags")
        0
      } else {

        objectIds
          .grouped(1000)
          .toList
          .map(oidGroup => {

            DB.autoCommit { implicit session =>

              sql"""
          DELETE FROM tags_prospects
          WHERE prospect_id IN ${SQLUtils.generateSQLValuesClause(oidGroup)}
            AND tag_id IN ${SQLUtils.generateSQLValuesClause(existingTags.map(_.tag_id).distinct)}
          ;
          """
                .update
                .apply()

            }
          })
          .sum
      }

    }
  }

  def getDuplicateProspectTags(
                                duplicateProspects: List[ProspectIdAndPotentialDuplicateProspectId]
                              )(implicit session: DBSession): Try[List[ProspectTagData]] = Try {

    sql"""
          SELECT tag_id, prospect_id, account_id, created_at
          FROM tags_prospects
          WHERE prospect_id in (${duplicateProspects.map(_.prospectId.id)})
          ;
        """
      .map(rs => {
        val prospectId = rs.long("prospect_id")
        ProspectTagData(
          tag_id = ProspectsTagId(rs.long("tag_id")),
          prospect_id = ProspectId(prospectId),
          account_id = AccountId(rs.long("account_id")),
          created_at = rs.jodaDateTime("created_at"),
          potentialDuplicateProspectId = duplicateProspects.find(_.prospectId.id == prospectId).get.potentialDuplicateProspectId
        )
      })
      .list
      .apply()
  }

  def insertTagsForMasterProspect(
                                   prospectTagData: List[ProspectTagData],
                                   masterProspectId: ProspectId
                                 )(implicit session: DBSession): Try[List[Long]] = Try {
    if(prospectTagData.isEmpty) {
      List()
    } else {
      var valueParameters = List[Any]()

      val valuePlaceholder: SQLSyntax = prospectTagData.map(p => {

          valueParameters = valueParameters ::: List(
            p.tag_id.id,
            masterProspectId.id,

            p.account_id.id,
            p.created_at,

            p.potentialDuplicateProspectId.id
          )

          sqls"""
           (
              ?,
              ?,

              ?,
              ?,

              ?
            )
          """

        })
        .reduce((vp1, vp2) => sqls"$vp1, $vp2")

      sql"""
         INSERT INTO tags_prospects
         (
          tag_id,
          prospect_id,

          account_id,
          created_at,

          potential_duplicate_prospect_id
         )

         VALUES $valuePlaceholder
         ON CONFLICT DO NOTHING
         RETURNING prospect_id;
       """
        .bind(valueParameters*)
        .map(_.long("prospect_id"))
        .list
        .apply()
    }
  }
}
