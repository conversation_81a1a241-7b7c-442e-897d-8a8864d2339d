package api.google_recaptcha.service

import api.accounts.models.OrgId
import api.{AppConfig, CacheServiceJedis}
import api.google_recaptcha.model.RecaptchaActionType
import utils.Helpers

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

sealed trait CheckIfWeShowCaptchaError

object CheckIfWeShowCaptchaError {
  case class RedisError(err: Throwable) extends CheckIfWeShowCaptchaError

  case class CouldnotCreateRedisKeyError(response: String) extends CheckIfWeShowCaptchaError
}

sealed trait ReCaptchaError

object ReCaptchaError {
  case class ShowCaptchaError(err: CheckIfWeShowCaptchaError) extends ReCaptchaError

  case object NoGResponseSentError extends ReCaptchaError

  case class VerifyCaptchaError(err: VerifyRecaptchaInputError) extends ReCaptchaError

  case class TestingEmailShouldNotHaveGResponse(errStr: String) extends ReCaptchaError
}

case class RecaptchaData(
                          requestIp: String,
                          actionType: RecaptchaActionType,
                          response: Option[String]
                        )

case class RecaptchaResponse(
                              showCaptcha: Boolean,
                              passedCaptcha: Boolean
                            )

class SRGoogleRecaptchaServices(
                               cacheServiceJedis: CacheServiceJedis,
                               googleRecaptchaApiService: GoogleRecaptchaApiService,
                               implicit val ec: ExecutionContext
                             ) {

  private def createRedisKey(
                              requestIp: String,
                              actionType: RecaptchaActionType
                            ): String = {
    s"sr_recaptcha___${requestIp}___$actionType"
  }


  private def checkAttemptCount(
                            requestIp: String,
                            actionType: RecaptchaActionType
                          ): Either[CheckIfWeShowCaptchaError, Int] = {
    val key = createRedisKey(
      requestIp = requestIp,
      actionType = actionType
    )

    val valueInRedis = cacheServiceJedis.get[Int](key = key)

    valueInRedis match {
      case Failure(err) =>

        Left(CheckIfWeShowCaptchaError.RedisError(err))

      case Success(None) =>

        cacheServiceJedis.set[Int](
          key = key,
          value = 1,
          expireInSeconds = AppConfig.googleRecaptchaTimeLimitInSeconds
        ) match {
          case Failure(err) =>

            Left(CheckIfWeShowCaptchaError.RedisError(err))

          case Success(response) =>
            if (response != "OK") {

              Left(CheckIfWeShowCaptchaError.CouldnotCreateRedisKeyError(response))

            } else {

              Right(1)

            }
        }

      case Success(Some(_)) =>
        cacheServiceJedis.incr(
          key = key
        ) match {
          case Failure(err) =>

            Left(CheckIfWeShowCaptchaError.RedisError(err))

          case Success(i) =>

            Right(i.toInt)

        }

    }
  }

  def verifyCaptcha(
                   requestIp: String,
                   response: Option[String],
                   userEmail: Option[String] = None
                   ): Future[Either[ReCaptchaError, RecaptchaResponse]] = {
    response match {
      case None =>
        //response will be none if google don't return g_response after captcha verification

        userEmail match {
          case None => Future.successful(Left(ReCaptchaError.NoGResponseSentError))

          case Some(email) =>
            if(Helpers.checkIfCypressTestingEmailAccount(email)){
              Future.successful(Right(
                RecaptchaResponse(
                  showCaptcha = false,
                  passedCaptcha = true
                )
              ))
            } else {
              Future.successful(Left(ReCaptchaError.NoGResponseSentError))
            }

        }


      case Some(res) =>

            if(userEmail.isDefined && Helpers.checkIfCypressTestingEmailAccount(userEmail.get)){
              Future.successful(Left(ReCaptchaError.TestingEmailShouldNotHaveGResponse("This case should not happen")))
            } else {
              googleRecaptchaApiService.verifyRecaptchaInput(
                requestIp = requestIp,
                response = res
              ) map {
                case Left(a) =>
                  Left(ReCaptchaError.VerifyCaptchaError(a))
                case Right(data) =>

                  Right(
                    RecaptchaResponse(
                      showCaptcha = !data.success,
                      passedCaptcha = data.success
                    )
                  )

              }
        }

    }
  }


  def checkIfWeShowCaptchaOrVerifyIt(
                                      data: RecaptchaData
                                    ): Future[Either[ReCaptchaError, RecaptchaResponse]] = {

    checkAttemptCount(
      requestIp = data.requestIp,
      actionType = data.actionType
    ) match {
      case Left(a) =>

        Future.successful(Left(ReCaptchaError.ShowCaptchaError(a)))

      case Right(i) =>
        if (i < AppConfig.googleRecaptchaAttempts) {
          Future.successful(Right(
            RecaptchaResponse(
              showCaptcha = false,
              passedCaptcha = true
            )
          ))

        } else {

          verifyCaptcha(
            requestIp = data.requestIp,
            response = data.response
          )

         }
    }
  }

}
