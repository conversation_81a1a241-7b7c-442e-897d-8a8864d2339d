package api.google_recaptcha.service

import api.AppConfig
import play.api.libs.ws.WSClient
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}
import play.api.libs.ws.WSBodyWritables.writeableOf_urlEncodedForm

case class VerifyRecaptchaInputResponse(
                                success: Boolean,
                                challenge_ts: String,
                                hostname: String,
                                error_codes: Option[Seq[String]]
                                )

sealed trait VerifyRecaptchaInputError

object VerifyRecaptchaInputError {
  case object WrongDataSentBackError extends VerifyRecaptchaInputError
}

class GoogleRecaptchaApiService (
                                  implicit val ws: WSClient,
                                  ec: ExecutionContext
                                ){
  private val googleRecaptchaVerificationLink = "https://www.google.com/recaptcha/api/siteverify"

  def verifyRecaptchaInput(
                             requestIp: String,
                             response: String
                           ): Future[Either[VerifyRecaptchaInputError, VerifyRecaptchaInputResponse]] = {

    val body: Map[String, Seq[String]] = Map(

      "secret" -> Seq(AppConfig.googleRecaptchaSecret),
      "response" -> Seq(response),
      "remoteip" -> Seq(requestIp)

    )

    ws.url(googleRecaptchaVerificationLink)
      .post(body)
      .map(r => r.json)
      .map(json => {

        val success_opt = (json \\ "success").headOption.map(_.as[Boolean])
        val challenge_ts_opt = (json \\ "challenge_ts").headOption.map(_.as[String])
        val hostname_opt = (json \\ "hostname").headOption.map(_.as[String])
        val error_codes_opt = (json \\ "error-codes").headOption.map(_.as[Seq[String]])

        if( success_opt.isEmpty ||
            challenge_ts_opt.isEmpty ||
            hostname_opt.isEmpty ) {

          Left(VerifyRecaptchaInputError.WrongDataSentBackError)

        }else {
          Right(
            VerifyRecaptchaInputResponse(
              success = success_opt.get,
              challenge_ts = challenge_ts_opt.get,
              hostname = hostname_opt.get,
              error_codes = error_codes_opt
            )
          )
        }
      }
      )

  }

}
