package api.scylla.dao

//import api.scylla.ScyllaRunSafely
//import com.datastax.driver.core.Row
//import eventframework.models.{SrEvent, SrEventType}
import io.smartreach.esp.api.emails.IEmailAddress
import io.smartreach.esp.utils.email.EmailReplyBounceType
import org.joda.time.DateTime
import play.api.libs.json.{Json, OFormat}
//import utils.SRLogger
//import utils.scylladb.cql.CqlImplicits.{CqlStrings, execute}
import play.api.libs.json.JodaWrites._
import play.api.libs.json.JodaReads._

//import scala.concurrent.{ExecutionContext, Future}
//import scala.util.Try

case class BounceData(
                       bounced_at: DateTime,
                       bounce_type: EmailReplyBounceType,
                       bounce_reason: String,
                       is_soft_bounced: Boolean
                     )

object BounceData {
  given formats: OFormat[BounceData] = Json.format[BounceData]
}

case class EmailMessageObjectV2(
                                 from_user: <PERSON><PERSON><PERSON>,

                                from: IEmailAddress,
                                reply_to: Option[IEmailAddress],
                                to: Seq[IEmailAddress],

                                cc_emails: Option[Seq[IEmailAddress]],
                                bcc_emails: Option[Seq[IEmailAddress]],

                                subject: String,

                                sent_at: DateTime,
                                bounce_data: Option[BounceData]
                              )

object EmailMessageObjectV2 {
  given formats: OFormat[EmailMessageObjectV2] = Json.format[EmailMessageObjectV2]

}
//
//case class EventInDB(
//                      event_id: String,
//                      event_type: SrEventType,
//                      org_id: Long,
//                      campaign_id: Option[Long],
//                      event_at: DateTime,
//                      team_id: Option[Long],
//                      email_setting_id: Option[Long],
//                      event_data: SrEvent,
//                      created_at: DateTime
//                    )
//
//class CacheEventScyllaDAO(
//                           scyllaRunSafely: ScyllaRunSafely
//                         ) {
//
//
//  def createEvent(
//                   org_id: Long,
//                   team_id: Option[Long],
//                   campaign_id: Option[Long],
//                   email_setting_id: Option[Long],
//                   data: SrEvent.EmailMessageBounced
//                  )(implicit ec: ExecutionContext, Logger: SRLogger): Future[Int] = {
//
//    scyllaRunSafely.withSessionExecute{ implicit session =>
//      val query =
//        cql"""
//            INSERT INTO sr_events
//            (
//             event_id,
//             event_type,
//             org_id,
//
//             campaign_id,
//             event_at,
//             team_id,
//
//             email_setting_id,
//             event_data,
//             created_at
//            )
//           VALUES (
//           ?,
//           ?,
//           ?,
//
//           ?,
//           ?,
//           ?,
//
//           ?,
//           ?,
//           ?
//           )
//            """
//
//      execute(
//        query,
//        data.event_id,
//        data.event_type.toString,
//        org_id,
//
//        campaign_id.orNull,
//        data.event_data.object_data.bounce_data.get.bounced_at.toDate,
//        team_id.orNull,
//
//        email_setting_id.orNull,
//        Json.toJson(data).toString,
//        DateTime.now().toDate
//      )
//        .map { data =>
//          data.all().size()
//        }
//        .recover {
//          case e =>
//            Logger.fatal(s"Scylla Action: createEvent Error while adding event in cache error", err = e)
//            0
//        }
//    }
//
//  }
//
//
//  private def fromCache(row: Row): Try[EventInDB] = Try{
//
//    val event_data = Json.parse(row.getString("event_data")).validate[SrEvent].get
//
//    EventInDB(
//      event_id = row.getString("event_id"),
//      event_type = SrEventType.fromKey(row.getString("event_type")).get,
//      org_id = row.getLong("org_id"),
//      campaign_id = Try{
//        row.getLong("campaign_id")
//      }.toOption,
//      event_at = new DateTime(row.getTimestamp("event_at")),
//      team_id = Try{
//        row.getLong("team_id")
//      }.toOption,
//      email_setting_id = Try{
//        row.getLong("email_setting_id")
//      }.toOption,
//      event_data = event_data,
//      created_at = new DateTime(row.getTimestamp("created_at"))
//    )
//  }
//
//  def getBouncedRepliesForCampaign(
//                                    campaign_id: Long,
//                                    org_id: Long
//                                  )(implicit ec: ExecutionContext, Logger: SRLogger): Future[List[EventInDB]] = {
//
//    scyllaRunSafely.withSessionExecute{ implicit session =>
//      //event_at to have a better consistency with email_scheduled table
//      // ideally we should check against the sent_at time
//      //
//      //however, since this is a bounced event, we dont have direct access to sent_at here
//      //
//      //since most bounces will happen immediately when the email is sent, we will ignore the difference between sent_at and bounced_at, and assume they are happening in the same time
//      //
//      //thats why we are using event_at here
//
//      //
//      val query =
//        cql"""
//        SELECT * from sr_events
//        WHERE org_id = ?
//        AND campaign_id = ?
//        AND event_type = ?
//        AND event_at > ?;
//        """
//
//      execute(query, org_id, campaign_id, SrEventType.EmailMessageBounced.toString, DateTime.now().minusDays(1).toDate).map { data =>
//
//
//        val listOfRow = data.all()
//        var resultList: List[EventInDB] = List()
//        if (!listOfRow.isEmpty)  {
//          listOfRow.forEach{row =>
//            val tryOfEventInDB = fromCache(row)
//
//            resultList = resultList ++ List(tryOfEventInDB.get)
//          }
//        }
//        resultList
//      }
//        .recover {
//          case e =>
//            Logger.fatal(s"Scylla Action: getBouncedRepliesForCampaign Error while getting bounced replies from cache error", err = e)
//            throw e
//        }
//    }
//  }
//
//}
//
