// 22-Jan-2024 SCYLLA_COMMENTED_OUT

//package api.scylla
//
//import com.datastax.driver.core.{ResultSet, Session}
//import utils.SRLogger
//import utils.scylladb.cql.CqlImplicits.{CqlStrings, execute}
//
//import scala.concurrent.{ExecutionContext, Future}
//
//class ScyllaEvolution (
//                        scyllaRunSafely: ScyllaRunSafely,
//                        ec: ExecutionContext
//                      ) {
//
//  private implicit val executionContext: ExecutionContext = ec
//
//  private def runEvolutions()(using Logger: SRLogger): Future[List[ResultSet]] = {
//
//    val resultOfEvolutionInListOfFuture = evolutions.map{query =>
//
//      scyllaRunSafely.withSessionExecute { implicit session: Session =>
//        execute(cql"$query")
//      }
//    }
//
//    val resultOfMigrationInListOfFuture = migrations.map { query =>
//
//      scyllaRunSafely.withSessionExecute { implicit session: Session =>
//        execute(cql"$query")
//      }
//    }
//
//    for {
//      resultOfEvolution <- Future.sequence(resultOfEvolutionInListOfFuture)
//      resultOfMigration <- Future.sequence(resultOfMigrationInListOfFuture)
//    } yield {
//      resultOfEvolution ++ resultOfMigration
//    }
//  }
//
//  private val evolutions: List[String] = List(
//
//    //Creating table task_cache
//    //1
////    """CREATE TABLE IF NOT EXISTS task_cache (
////         task_id text PRIMARY KEY,
////         created_at timestamp,
////         task_json text
////      );""",
//    //Creating table bounce_rate_data
//    //2
////    """
////         CREATE TABLE IF NOT EXISTS sr_events (
////            event_id text,
////            event_type text,
////            org_id BIGINT,
////            campaign_id BIGINT,
////            email_setting_id BIGINT,
////            team_id BIGINT,
////            event_at timestamp,
////            created_at timestamp,
////            event_data text,
////             PRIMARY KEY(event_id, event_type, org_id, event_at, created_at, event_data)
////         );
////       """,
//    //dropping table sr_events
//    //3
////    """DROP TABLE IF EXISTS sr_events;"""
////    ,
//    //Recreating table bounce_rate_data and adding campaign_id as a primary key
//    //https://stackoverflow.com/questions/27438113/cassandra-only-eq-and-in-relation-are-supported-on-the-partition-key-unless-yo
//    //https://www.baeldung.com/cassandra-keys
//    //4
////    """
////         CREATE TABLE IF NOT EXISTS sr_events (
////            event_id text,
////            event_type text,
////            org_id BIGINT,
////            campaign_id BIGINT,
////            email_setting_id BIGINT,
////            team_id BIGINT,
////            event_at timestamp,
////            created_at timestamp,
////            event_data text,
////             PRIMARY KEY((org_id, campaign_id, event_type), event_at, event_id,  created_at, event_data)
////         );
////       """
//
//  )
//
//
//
//  private val migrations: List[String] = List(
//    //Keep all commented out
//
//   /*
//   // 1
//   """
//    TRUNCATE TABLE task_cache;
//      """,
//
//   // 2
//    """
//       ALTER TABLE task_cache WITH default_time_to_live = 300;
//      """
//    */
//
//  )
//
//
//}
