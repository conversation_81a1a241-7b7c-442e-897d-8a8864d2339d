package api.whatsapp.models

import play.api.libs.json.{<PERSON><PERSON>, Writes}

case class WhatsappAccountSettings(
                                     uuid: String,
                                     first_name: String,
                                     last_name: String,
                                     whatsapp_number: String,
                                     owner_id: Long,
                                     team_id: Long,
                                     owner_first_name: String,
                                     owner_last_name: String,
                                     whatsapp_message_limit_per_day: Int,
                                   )

object WhatsappAccountSettings {
  implicit val writes: Writes[WhatsappAccountSettings] = Json.writes[WhatsappAccountSettings]
}