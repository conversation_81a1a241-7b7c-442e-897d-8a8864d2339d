package api.lead_finder.service

import api.accounts.service.{OTPJedisDAOService, OTPService}
import api.lead_finder.DAO.GDPROTPDBDAOService
import api.lead_finder.models.GDPRRecord

import scala.util.Try

class GDPROTPService (
                       val otpDBDAOService: GDPROTPDBDAOService,
                       val otpJedisDAOService: OTPJedisDAOService
                     ) extends OTPService {
  def verify(
              email: String
            ): Try[Option[GDPRRecord]] = otpDBDAOService.verify(email = email)

  def selectByEmail(
                     email: String
                   ): Try[Option[GDPRRecord]] = otpDBDAOService.selectByEmail(email = email)
}
