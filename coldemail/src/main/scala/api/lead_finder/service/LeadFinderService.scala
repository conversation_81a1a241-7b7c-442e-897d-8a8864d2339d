package api.lead_finder.service

import api.AppConfig
import api.accounts.dao.OrganizationDAO
import api.accounts.{Account, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.accounts.service.{ConsumedLeadFinderCredits, OrganizationService}
import api.campaigns.models.IgnoreProspectsInOtherCampaigns
import api.campaigns.services.CampaignId
import api.lead_finder.DAO.{AddLeadToProspectListData, LeadFinderBillingLogsDetails, LeadFinderDAO, LeadFinderIntermediate}
import api.lead_finder.controller.{AddLeadsIntoProspectListForm, LeadFinderRequestBody}
import api.lead_finder.models.{ContactType, LeadCompanySize, LeadCompanyType, LeadHiddenInfo, LeadId, LeadIndustry, LeadInfoResponse, LeadJobTitle, LeadMetadata, LeadUuid, LeadValidationBatchReqId}
import api.lead_finder.service
import api.prospects.dao.DuplicateProspectResult
import api.prospects.{CreateOrUpdateProspectsResult, ProspectCreateFormData, ProspectForValidation, ProspectService, ProspectSource}
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.{EmailForValidation, SrProspectColumns, UpdateProspectType}
import com.google.common.hash.Hashing
import io.smartreach.esp.api.emails.IEmailAddress
import org.joda.time.DateTime
import play.api.libs.json.{JsObject, Json, OFormat}
import utils.{Helpers, SRLogger}
import utils.emailvalidation.EmailValidationService
import utils.emailvalidation.models.{EmailValidationInitiator, EmailValidationPriority, EmailsForValidationWithInitiator, IdsOrEmailsForValidation}
import utils.helpers.LogHelpers
import utils.linkedin.LinkedinHelperFunctions
import utils.testapp.csv_upload.CsvUploadCol
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import java.nio.charset.StandardCharsets
import javax.mail.Address
import scala.util.{Failure, Success, Try}



sealed trait BillingLogsError

object BillingLogsError{
  case class ServerError(err: Throwable) extends BillingLogsError
  case class BadRequestError(err: String) extends BillingLogsError
}

case class  LeadFinderBillingLogsResponse(
                                          message: String,
                                          data: JsObject
                                        )

sealed trait LeadFinderPageNav

object LeadFinderPageNav {
  case class NextNav(
                      id: Long
                    ) extends LeadFinderPageNav

  case class PrevNav(
                      id: Long
                    ) extends LeadFinderPageNav

}

case class InsertLeadsBillingLogs(
                                   id: Long,
                                   first_name: Option[String],
                                   last_name: Option[String],
                                   contactType: ContactType,
                                   contactTypeValue: String,
                                   listName: Option[String],
                                   credits_used: Long,
                                   invalid_email: Boolean,
                                   email_checked: Boolean,
                                   email_checked_at: Option[DateTime],
                                 )

object LeadFinderService {

  def getFieldMapperForInsertingLeadsInLeadfinder(insertedBy : ProspectSource.Value): Option[Map[String, String]] = {
    insertedBy match {
      case  ProspectSource.PROSPECTDADDY => Some(Map(
        "first_name" -> "person_first_name",
        "last_name" -> "person_last_name",
        "company" -> "company_name",
        "job_title" -> "person_job_title",
        "linkedin_profile_url" -> "person_linkedin_url",
        "city" ->  "person_city",
        "state" ->  "person_state",
        "country"-> "person_country",
        "email" -> "person_business_email"
      ))

      case ProspectSource.SMARTREACH_PROSPECTS => Some(Map(
        "first_name" -> "person_first_name",
        "last_name" -> "person_last_name",
        "email" -> "person_business_email",
        "job_title" -> "person_job_title",
        "company" -> "company_name",
        "linkedin_url" -> "person_linkedin_url",
        "phone" -> "person_phone",
        "city" -> "person_city",
        "state" ->  "person_state",
        "country"-> "person_country"
      ))

      case _ =>None
    }

  }

  def calculateCentsRequiredForAddingLeads(insertData: List[InsertLeadsBillingLogs]): Int = {
    insertData.count(_.contactType == ContactType.Email) * AppConfig.LeadFinderCharges.email +
      insertData.count(_.contactType == ContactType.Linkedin) * AppConfig.LeadFinderCharges.linkedin_url +
      insertData.count(_.contactType == ContactType.Phone) * AppConfig.LeadFinderCharges.phone_number
  }

  def extractDomain(email: String)(using Logger: SRLogger): Option[String] = {

    val iEmailCheck = for {
      iEmail: Seq[IEmailAddress] <- IEmailAddress.parse(emailsStr = email)

      _: Array[Address] <- Try {
        IEmailAddress.toJavaAddressArray(iEmail)
      }
    } yield {
      true
    }

    iEmailCheck match {
      case Success(value) =>
        if(value){
          Some(email.split("@").last)
        } else {
          None
        }
      case Failure(exception) =>
        Logger.warn(s"Failed the IEmailAddress validation: ${LogHelpers.getStackTraceAsString(exception)}")
        None
    }
  }

  case class LeadUuidWithBasicData(
                                lead_uuid: LeadUuid,
                                job_title: Option[String]
                              )

  object LeadUuidWithBasicData {
    given format: OFormat[LeadUuidWithBasicData] = Json.format[LeadUuidWithBasicData]
  }



  private def getCsvUploadCol(
                               mappingFromClient: Map[String, String],
                               csvRow: Map[String, String]
                             ): CsvUploadCol =
  {

    var addressOpt: Option[String] = None
    var address1Opt: Option[String] = None
    var address2Opt: Option[String] = None
    var addressLine1Opt: Option[String] = None
    var addressLine2Opt: Option[String] = None
    var addressLine3Opt: Option[String] = None
    var anzsic2006CodeOpt: Option[String] = None
    var anzsic2006DescriptionOpt: Option[String] = None
    var appUsingOpt: Option[String] = None
    var assetsUsdOpt: Option[String] = None
    var birthDateOpt: Option[String] = None
    var birthYearOpt: Option[String] = None
    var businessDescriptionOpt: Option[String] = None
    var cityOpt: Option[String] = None
    var companyOpt: Option[String] = None
    var companyAddressOpt: Option[String] = None
    var companyCityOpt: Option[String] = None
    var companyCountryOpt: Option[String] = None
    var companyDivisionNameOpt: Option[String] = None
    var companyDomainNameOpt: Option[String] = None
    var companyFoundedOpt: Option[String] = None
    var companyIndustryOpt: Option[String] = None
    var companyLinkedinIdOpt: Option[String] = None
    var companyLinkedinUrlOpt: Option[String] = None
    var companyMetaDescriptionOpt: Option[String] = None
    var companyMetaEmailsOpt: Option[String] = None
    var companyMetaKeywordsOpt: Option[String] = None
    var companyMetaPhonesOpt: Option[String] = None
    var companyMetaTitleOpt: Option[String] = None
    var companyNameOpt: Option[String] = None
    var companyPhoneNumberOpt: Option[String] = None
    var companySizeOpt: Option[String] = None
    var companyStateOpt: Option[String] = None
    var companyStreetAddressOpt: Option[String] = None
    var companyTypeOpt: Option[String] = None
    var companyZipPostalCodeOpt: Option[String] = None
    var contactOpt: Option[String] = None
    var contactLevelOpt: Option[String] = None
    var contactNameOpt: Option[String] = None
    var contactTitleOpt: Option[String] = None
    var countriesOpt: Option[String] = None
    var countryOpt: Option[String] = None
    var countryRegionOpt: Option[String] = None
    var countyOpt: Option[String] = None
    var dnbHooversIndustryOpt: Option[String] = None
    var directMarketingStatusOpt: Option[String] = None
    var directPhoneOpt: Option[String] = None
    var directPhoneNumberOpt: Option[String] = None
    var emailOpt: Option[String] = None
    var emailAddressOpt: Option[String] = None
    var emailDomainOpt: Option[String] = None
    var emailFormatOpt: Option[String] = None
    var emailsOpt: Option[String] = None
    var employeeSizeOpt: Option[String] = None
    var employeesOpt: Option[String] = None
    var employeesAllSitesOpt: Option[String] = None
    var employeesSingleSiteOpt: Option[String] = None
    var employeesRangeOpt: Option[String] = None
    var encryptedEmailAddressOpt: Option[String] = None
    var entityTypeOpt: Option[String] = None
    var faxOpt: Option[String] = None
    var firstNameOpt: Option[String] = None
    var fullNameOpt: Option[String] = None
    var genderOpt: Option[String] = None
    var highestLevelJobFunctionOpt: Option[String] = None
    var industryOpt: Option[String] = None
    var industryHierarchicalCategoryOpt: Option[String] = None
    var industryLabelOpt: Option[String] = None
    var industryTypeOpt: Option[String] = None
    var isicRev4CodeOpt: Option[String] = None
    var isicRev4DescriptionOpt: Option[String] = None
    var jobFunctionOpt: Option[String] = None
    var jobTitleOpt: Option[String] = None
    var jobTitleHierarchyLevelOpt: Option[String] = None
    var lastNameOpt: Option[String] = None
    var liabilitiesUsdOpt: Option[String] = None
    var linkedinLinksOpt: Option[String] = None
    var linkedinUrlOpt: Option[String] = None
    var listNameOpt: Option[String] = None
    var localityOpt: Option[String] = None
    var locationOpt: Option[String] = None
    var managementLevelOpt: Option[String] = None
    var middleNameOpt: Option[String] = None
    var naicsOpt: Option[String] = None
    var naics1Opt: Option[String] = None
    var naics2Opt: Option[String] = None
    var naics2012CodeOpt: Option[String] = None
    var naics2012DescriptionOpt: Option[String] = None
    var naceRev2CodeOpt: Option[String] = None
    var naceRev2DescriptionOpt: Option[String] = None
    var ownershipTypeOpt: Option[String] = None
    var parentCompanyOpt: Option[String] = None
    var parentCountryRegionOpt: Option[String] = None
    var personBusinessEmailOpt: Option[String] = None
    var personCityOpt: Option[String] = None
    var personCompanyNameOpt: Option[String] = None
    var personFirstNameOpt: Option[String] = None
    var personHeadlineOpt: Option[String] = None
    var personJobTitleOpt: Option[String] = None
    var personLastNameOpt: Option[String] = None
    var personLinkedinIdOpt: Option[String] = None
    var personLinkedinUrlOpt: Option[String] = None
    var personLocationOpt: Option[String] = None
    var personPersonalEmailOpt: Option[String] = None
    var personPhoneOpt: Option[String] = None
    var personProUrlOpt: Option[String] = None
    var personStateOpt: Option[String] = None
    var personZipOpt: Option[String] = None
    var phoneOpt: Option[String] = None
    var phoneNumberOpt: Option[String] = None
    var postalCodeOpt: Option[String] = None
    var preTaxProfitUsdOpt: Option[String] = None
    var queryOpt: Option[String] = None
    var queryNameOpt: Option[String] = None
    var revenueOpt: Option[String] = None
    var revenueIn000sOpt: Option[String] = None
    var revenueInUOpt: Option[String] = None
    var revenueUsdOpt: Option[String] = None
    var revenueRangeOpt: Option[String] = None
    var salutationOpt: Option[String] = None
    var salesOpt: Option[String] = None
    var secondaryIndustryHierarchicalCategoryOpt: Option[String] = None
    var secondaryIndustryLabelOpt: Option[String] = None
    var sicOpt: Option[String] = None
    var sic1Opt: Option[String] = None
    var sic2Opt: Option[String] = None
    var sicCodeOpt: Option[String] = None
    var sourceCountOpt: Option[String] = None
    var stateOpt: Option[String] = None
    var stateOrProvinceOpt: Option[String] = None
    var suffixOpt: Option[String] = None
    var tickerOpt: Option[String] = None
    var titleOpt: Option[String] = None
    var titlecodeOpt: Option[String] = None
    var totalEmployeesOpt: Option[String] = None
    var tradestyleOpt: Option[String] = None
    var ukSic2007CodeOpt: Option[String] = None
    var ukSic2007DescriptionOpt: Option[String] = None
    var ultimateParentCompanyOpt: Option[String] = None
    var ultimateParentCountryRegionOpt: Option[String] = None
    var urlOpt: Option[String] = None
    var usSic1987CodeOpt: Option[String] = None
    var usSic1987DescriptionOpt: Option[String] = None
    var websiteOpt: Option[String] = None
    var zipOpt: Option[String] = None
    var zoomCompanyIdOpt: Option[String] = None
    var zoomIndividualIdOpt: Option[String] = None
    var zoominfoIndustryOpt: Option[String] = None
    var prospectDataConcat: String = ""


    mappingFromClient.foreach(header => {

      header._2 match {

        case "address" =>
          addressOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "address_1" =>
          address1Opt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "address_2" =>
          address2Opt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "address_line_1" =>
          addressLine1Opt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "address_line_2" =>
          addressLine2Opt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "address_line_3" =>
          addressLine3Opt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "anzsic_2006_code" =>
          anzsic2006CodeOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "anzsic_2006_description" =>
          anzsic2006DescriptionOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "app_using" =>
          appUsingOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "assets__usd_" =>
          assetsUsdOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "birth_date" =>
          birthDateOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "birth_year" =>
          birthYearOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "business_description" =>
          businessDescriptionOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "city" =>
          cityOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company" =>
          companyOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_address" =>
          companyAddressOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_city" =>
          companyCityOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_country" =>
          companyCountryOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_division_name" =>
          companyDivisionNameOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_domain_name" =>
          companyDomainNameOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_founded" =>
          companyFoundedOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_industry" =>
          companyIndustryOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_linkedin_id" =>
          companyLinkedinIdOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_linkedin_url" =>
          companyLinkedinUrlOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_meta_description" =>
          companyMetaDescriptionOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_meta_emails" =>
          companyMetaEmailsOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_meta_keywords" =>
          companyMetaKeywordsOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_meta_phones" =>
          companyMetaPhonesOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_meta_title" =>
          companyMetaTitleOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_name" =>
          companyNameOpt =if(csvRow.get(header._1).contains(""))
            None
          else {
            prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
            csvRow.get(header._1)
          }
        case "company_phone_number" =>
          companyPhoneNumberOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_size" =>
          companySizeOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_state" =>
          companyStateOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_street_address" =>
          companyStreetAddressOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_type" =>
          companyTypeOpt =if(csvRow.get(header._1).contains(""))
            None
          else {
            prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
            csvRow.get(header._1)
          }
        case "company_zip_postal_code" =>
          companyZipPostalCodeOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "contact" =>
          contactOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "contact_level" =>
          contactLevelOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "contact_name" =>
          contactNameOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "contact_title" =>
          contactTitleOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "countries" =>
          countriesOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "country" =>
          countryOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "country_region" =>
          countryRegionOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "county" =>
          countyOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "dnb_hoovers_industry" =>
          dnbHooversIndustryOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "direct_marketing_status" =>
          directMarketingStatusOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "direct_phone" =>
          directPhoneOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "direct_phone_number" =>
          directPhoneNumberOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "email" =>
          emailOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "email_address" =>
          emailAddressOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "email_domain" =>
          emailDomainOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "email_format" =>
          emailFormatOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "emails" =>
          emailsOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "employee_size" =>
          employeeSizeOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "employees" =>
          employeesOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "employees__all_sites_" =>
          employeesAllSitesOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "employees__single_site_" =>
          employeesSingleSiteOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "employees_range" =>
          employeesRangeOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "encrypted_email_address" =>
          encryptedEmailAddressOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "entity_type" =>
          entityTypeOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "fax" =>
          faxOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "first_name" =>
          firstNameOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "full_name" =>
          fullNameOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "gender" =>
          genderOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "highest_level_job_function" =>
          highestLevelJobFunctionOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "industry" =>
          industryOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "industry_hierarchical_category" =>
          industryHierarchicalCategoryOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "industry_label" =>
          industryLabelOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "industry_type" =>
          industryTypeOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "isic_rev_4_code" =>
          isicRev4CodeOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "isic_rev_4_description" =>
          isicRev4DescriptionOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "job_function" =>
          jobFunctionOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "job_title" =>
          jobTitleOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "job_title_hierarchy_level" =>
          jobTitleHierarchyLevelOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "last_name" =>
          lastNameOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "liabilities__usd_" =>
          liabilitiesUsdOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "linkedin_links" =>
          linkedinLinksOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "linkedin_url" =>
          linkedinUrlOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "list_name" =>
          listNameOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "locality" =>
          localityOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "location" =>
          locationOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "management_level" =>
          managementLevelOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "middle_name" =>
          middleNameOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "naics" =>
          naicsOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "naics1" =>
          naics1Opt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "naics2" =>
          naics2Opt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "naics_2012_code" =>
          naics2012CodeOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "naics_2012_description" =>
          naics2012DescriptionOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "nace_rev_2_code" =>
          naceRev2CodeOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "nace_rev_2_description" =>
          naceRev2DescriptionOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "ownership_type" =>
          ownershipTypeOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "parent_company" =>
          parentCompanyOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "parent_country_region" =>
          parentCountryRegionOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_business_email" =>
          personBusinessEmailOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_city" =>
          personCityOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_company_name" =>
          personCompanyNameOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_first_name" =>
          personFirstNameOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_headline" =>
          personHeadlineOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_job_title" =>
          personJobTitleOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_last_name" =>
          personLastNameOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_linkedin_id" =>
          personLinkedinIdOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_linkedin_url" =>
          personLinkedinUrlOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              LinkedinHelperFunctions.normalizeLinkedInURL(csvRow.get(header._1))
            }
        case "person_location" =>
          personLocationOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_personal_email" =>
          personPersonalEmailOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_phone" =>
          personPhoneOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_pro_url" =>
          personProUrlOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_state" =>
          personStateOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_zip" =>
          personZipOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "phone" =>
          phoneOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "phone_number" =>
          phoneNumberOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "postal_code" =>
          postalCodeOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "pre_tax_profit__usd_" =>
          preTaxProfitUsdOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "query" =>
          queryOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "query_name" =>
          queryNameOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "revenue" =>
          revenueOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "revenue__in_000s_" =>
          revenueIn000sOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "revenue__in_u_" =>
          revenueInUOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "revenue__usd_" =>
          revenueUsdOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "revenue_range" =>
          revenueRangeOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "salutation" =>
          salutationOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "sales" =>
          salesOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "secondary_industry_hierarchical_category" =>
          secondaryIndustryHierarchicalCategoryOpt =if(csvRow.get(header._1).contains(""))
            None
          else {
            prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
            csvRow.get(header._1)
          }
        case "secondary_industry_label" =>
          secondaryIndustryLabelOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "sic" =>
          sicOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "sic1" =>
          sic1Opt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "sic2" =>
          sic2Opt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "sic_code" =>
          sicCodeOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "source_count" =>
          sourceCountOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "state" =>
          stateOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "state_or_province" =>
          stateOrProvinceOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "suffix" =>
          suffixOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "ticker" =>
          tickerOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "title" =>
          titleOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "titlecode" =>
          titlecodeOpt =if(csvRow.get(header._1).contains(""))
            None
          else {
            prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
            csvRow.get(header._1)
          }
        case "total_employees" =>
          totalEmployeesOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "tradestyle" =>
          tradestyleOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "uk_sic_2007_code" =>
          ukSic2007CodeOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "uk_sic_2007_description" =>
          ukSic2007DescriptionOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "ultimate_parent_company" =>
          ultimateParentCompanyOpt =if(csvRow.get(header._1).contains(""))
            None
          else {
            prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
            csvRow.get(header._1)
          }
        case "ultimate_parent_country_region" =>
          ultimateParentCountryRegionOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "url" =>
          urlOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "us_sic_1987_code" =>
          usSic1987CodeOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "us_sic_1987_description" =>
          usSic1987DescriptionOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "website" =>
          websiteOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "zip" =>
          zipOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "zoom_company_id" =>
          zoomCompanyIdOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "zoom_individual_id" =>
          zoomIndividualIdOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "zoominfo_industry" =>
          zoominfoIndustryOpt =
            if(csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case _ =>
          println(s"11111 ${header._2}")
          None

      }


    })
    val prospectDataHash = Hashing.sha256().hashString(prospectDataConcat, StandardCharsets.UTF_8).toString

    CsvUploadCol(
      address = addressOpt,
      address_1 = address1Opt,
      address_2 = address2Opt,
      address_line_1 = addressLine1Opt,
      address_line_2 = addressLine2Opt,
      address_line_3 = addressLine3Opt,
      anzsic_2006_code = anzsic2006CodeOpt,
      anzsic_2006_description = anzsic2006DescriptionOpt,
      app_using = appUsingOpt,
      assets__usd = assetsUsdOpt,
      birth_date = birthDateOpt,
      birth_year = birthYearOpt,
      business_description = businessDescriptionOpt,
      city = cityOpt,
      company = companyOpt,
      company_address = companyAddressOpt,
      company_city = companyCityOpt,
      company_country = companyCountryOpt,
      company_division_name = companyDivisionNameOpt,
      company_domain_name = companyDomainNameOpt,
      company_founded = companyFoundedOpt,
      company_industry = companyIndustryOpt,
      company_linkedin_id = companyLinkedinIdOpt,
      company_linkedin_url = companyLinkedinUrlOpt,
      company_meta_description = companyMetaDescriptionOpt,
      company_meta_emails = companyMetaEmailsOpt,
      company_meta_keywords = companyMetaKeywordsOpt,
      company_meta_phones = companyMetaPhonesOpt,
      company_meta_title = companyMetaTitleOpt,
      company_name = companyNameOpt,
      company_phone_number = companyPhoneNumberOpt,
      company_size = companySizeOpt,
      company_state = companyStateOpt,
      company_street_address = companyStreetAddressOpt,
      company_type = companyTypeOpt,
      company_zip_postal_code = companyZipPostalCodeOpt,
      contact = contactOpt,
      contact_level = contactLevelOpt,
      contact_name = contactNameOpt,
      contact_title = contactTitleOpt,
      countries = countriesOpt,
      country = countryOpt,
      country_region = countryRegionOpt,
      county = countyOpt,
      dnb_hoovers_industry = dnbHooversIndustryOpt,
      direct_marketing_status = directMarketingStatusOpt,
      direct_phone = directPhoneOpt,
      direct_phone_number = directPhoneNumberOpt,
      email = emailOpt,
      email_address = emailAddressOpt,
      email_domain = emailDomainOpt,
      email_format = emailFormatOpt,
      emails = emailsOpt,
      employee_size = employeeSizeOpt,
      employees = employeesOpt,
      employees__all_sites = employeesAllSitesOpt,
      employees__single_site = employeesSingleSiteOpt,
      employees_range = employeesRangeOpt,
      encrypted_email_address = encryptedEmailAddressOpt,
      entity_type = entityTypeOpt,
      fax = faxOpt,
      first_name = firstNameOpt,
      full_name = fullNameOpt,
      gender = genderOpt,
      highest_level_job_function = highestLevelJobFunctionOpt,
      industry = industryOpt,
      industry_hierarchical_category = industryHierarchicalCategoryOpt,
      industry_label = industryLabelOpt,
      industry_type = industryTypeOpt,
      isic_rev_4_code = isicRev4CodeOpt,
      isic_rev_4_description = isicRev4DescriptionOpt,
      job_function = jobFunctionOpt,
      job_title = jobTitleOpt,
      job_title_hierarchy_level = jobTitleHierarchyLevelOpt,
      last_name = lastNameOpt,
      liabilities__usd = liabilitiesUsdOpt,
      linkedin_links = linkedinLinksOpt,
      linkedin_url = linkedinUrlOpt,
      list_name = listNameOpt,
      locality = localityOpt,
      location = locationOpt,
      management_level = managementLevelOpt,
      middle_name = middleNameOpt,
      naics = naicsOpt,
      naics1 = naics1Opt,
      naics2 = naics2Opt,
      naics_2012_code = naics2012CodeOpt,
      naics_2012_description = naics2012DescriptionOpt,
      nace_rev_2_code = naceRev2CodeOpt,
      nace_rev_2_description = naceRev2DescriptionOpt,
      ownership_type = ownershipTypeOpt,
      parent_company = parentCompanyOpt,
      parent_country_region = parentCountryRegionOpt,
      person_business_email = personBusinessEmailOpt,
      person_city = personCityOpt,
      person_company_name = personCompanyNameOpt,
      person_first_name = personFirstNameOpt,
      person_headline = personHeadlineOpt,
      person_job_title = personJobTitleOpt,
      person_last_name = personLastNameOpt,
      person_linkedin_id = personLinkedinIdOpt,
      person_linkedin_url = personLinkedinUrlOpt,
      person_location = personLocationOpt,
      person_personal_email = personPersonalEmailOpt,
      person_phone = personPhoneOpt,
      person_pro_url = personProUrlOpt,
      person_state = personStateOpt,
      person_zip = personZipOpt,
      phone = phoneOpt,
      phone_number = phoneNumberOpt,
      postal_code = postalCodeOpt,
      pre_tax_profit__usd = preTaxProfitUsdOpt,
      query = queryOpt,
      query_name = queryNameOpt,
      revenue = revenueOpt,
      revenue__in_000s = revenueIn000sOpt,
      revenue__in_u = revenueInUOpt,
      revenue__usd = revenueUsdOpt,
      revenue_range = revenueRangeOpt,
      salutation = salutationOpt,
      sales = salesOpt,
      secondary_industry_hierarchical_category = secondaryIndustryHierarchicalCategoryOpt,
      secondary_industry_label = secondaryIndustryLabelOpt,
      sic = sicOpt,
      sic1 = sic1Opt,
      sic2 = sic2Opt,
      sic_code = sicCodeOpt,
      source_count = sourceCountOpt,
      state = stateOpt,
      state_or_province = stateOrProvinceOpt,
      suffix = suffixOpt,
      ticker = tickerOpt,
      title = titleOpt,
      titlecode = titlecodeOpt,
      total_employees = totalEmployeesOpt,
      tradestyle = tradestyleOpt,
      uk_sic_2007_code = ukSic2007CodeOpt,
      uk_sic_2007_description = ukSic2007DescriptionOpt,
      ultimate_parent_company = ultimateParentCompanyOpt,
      ultimate_parent_country_region = ultimateParentCountryRegionOpt,
      url = urlOpt,
      us_sic_1987_code = usSic1987CodeOpt,
      us_sic_1987_description = usSic1987DescriptionOpt,
      website = websiteOpt,
      zip = zipOpt,
      zoom_company_id = zoomCompanyIdOpt,
      zoom_individual_id = zoomIndividualIdOpt,
      zoominfo_industry = zoominfoIndustryOpt,
      prospect_data_hash = prospectDataHash
    )




  }
}

sealed trait AddLeadsIntoProspectListsError

object AddLeadsIntoProspectListsError {
  case class SQLException(err: Throwable) extends AddLeadsIntoProspectListsError

  case object CreditLimitReachedError extends AddLeadsIntoProspectListsError
}


class LeadFinderService(
                         prospectDAOService: ProspectDAOService,
                         leadFinderDAO: LeadFinderDAO,
                         organizationService: OrganizationService,
                         prospectService: ProspectService,
                         emailValidationService: EmailValidationService,
                         srRollingUpdateCoreService: SrRollingUpdateCoreService
                       ) {

  val perPageLimit = 200
  val maxTotalDisplay = 2000


  def chooseNavType(prev:Option[LeadUuid],next:Option[LeadUuid]):Either[Exception,Option[LeadFinderPageNav]] = {
    if (prev.isDefined && next.isDefined){
      Left(throw new Exception("have to send only one of the prev or next to the payload"))
    } else if (prev.isDefined){
      leadFinderDAO.getLeadIdFromUuid(uuid = prev.get) match{
        case Failure(err)=>
          Left(throw new Exception("Failed to get id from uuid"))
        case Success(id)=>
          Right(Some(LeadFinderPageNav.PrevNav(id=id)))
      }
    } else if (next.isDefined) {
      leadFinderDAO.getLeadIdFromUuid(uuid = next.get) match {
        case Failure(err) =>
          Left(throw new Exception("Failed to get id from uuid"))
        case Success(id) =>
          Right(Some(LeadFinderPageNav.NextNav(id = id)))
      }
    } else {
      Right(None)
    }
  }

  def getRemainingLeadFinderCredits(orgId: OrgId): Try[Long] = {

    organizationService.getRemainingLeadFinderCredits(
      orgId = orgId
    )

  }


  def filterByCategory(
                        leadFinderQuery: LeadFinderRequestBody,
                        orgId: OrgId,
                        accountId: AccountId,
                        teamId: TeamId,
                        forProspectDaddy: Boolean
                      )
                      (using Logger: SRLogger): Either[Exception,LeadInfoResponse] = {

    val leadInfo = chooseNavType(next = leadFinderQuery.next, prev = leadFinderQuery.prev) match {

      case Left(err) =>
        Failure(err)

      case Right(navType) =>
        for {
          filterLogId: Long <- leadFinderDAO.insertLeadFinderFilterLog(
            teamId = teamId,
            accountId = accountId,
            leadFinderRequestBody = leadFinderQuery,
          )

          jobTitleToCheck: List[String] <- if (leadFinderQuery.job_title.isEmpty) Success(List())
          else leadFinderDAO.getAlternativeCategories(leadFinderQuery.job_title, "lead_job_title_metadata")

          companySizeToCheck: List[String] <- if (leadFinderQuery.company_size.isEmpty) Success(List())
          else leadFinderDAO.getAlternativeCategories(leadFinderQuery.company_size, "lead_company_size_metadata")


          companyTypesToCheck: List[String] <- if (leadFinderQuery.company_type.isEmpty) Success(List())
          else leadFinderDAO.getAlternativeCategories(leadFinderQuery.company_type,"lead_company_type_metadata")


          industryTypesToCheck: List[String] <- if (leadFinderQuery.industry.isEmpty) Success(List())
          else leadFinderDAO.getAlternativeCategories(leadFinderQuery.industry, "lead_industry_metadata")

          data: List[LeadFinderIntermediate] <- leadFinderDAO.getNextLeadDetails(
            companyTypes = companyTypesToCheck.map(cmp => LeadCompanyType(cmp)),
            companySizes = companySizeToCheck.map(cms => LeadCompanySize(cms)),
            jobTitles = jobTitleToCheck.map(cms => LeadJobTitle(cms)),
            industry = industryTypesToCheck.map(ind => LeadIndustry(ind)),
            citys = leadFinderQuery.city,
            firstName = leadFinderQuery.first_name,
            lastName = leadFinderQuery.last_name,
            email = leadFinderQuery.email,
            phone = leadFinderQuery.phone,
            companyName = leadFinderQuery.company_name,
            perPageLimit = perPageLimit,
            maxTotalDisplay = maxTotalDisplay,
            navType = navType,
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            forProspectDaddy = forProspectDaddy

          )

          updateLeadFinderLogs <- leadFinderDAO.updateLeadFinderFilterLog(
            id = filterLogId,
            leadsCount = if (data.isEmpty) 0 else data.head.total_records
          )
        } yield {
          LeadInfoResponse(
            total_records = if (data.isEmpty) 0 else data.head.total_records,
            leads = data.map(
              leadInfo => LeadHiddenInfo(
                uuid = leadInfo.leadInfo.uuid,
                name = s"${leadInfo.leadInfo.first_name} ${if(leadInfo.leadInfo.last_name == null || leadInfo.leadInfo.last_name.isEmpty) "" else leadInfo.leadInfo.last_name.charAt(0).toUpper}",
                job_title = leadInfo.leadInfo.job_title,
                company_name = leadInfo.leadInfo.company_name,
                has_phone_number = leadInfo.leadInfo.phone_number.isDefined,
                has_email = leadInfo.leadInfo.email.isDefined,
                has_linkedin = leadInfo.leadInfo.linkedin.isDefined
              )
            ),
            next = if (data.isEmpty || data.length < perPageLimit) None else Some(data.last.leadInfo.uuid),
            prev = if ((leadFinderQuery.next.isEmpty && leadFinderQuery.prev.isEmpty) || data.isEmpty) {
              None
            } else {
              Some(data.head.leadInfo.uuid)
            }
          )
        }
    }

    leadInfo match {
      case Failure(err)=>
        Left(new Exception(err))
      case Success(value)=>
        Right(value)

    }

  }


  def getCompanyTypeCategory():Either[SaveSQLError,List[LeadMetadata]] = {
    leadFinderDAO.getCompanyTypeCategories() match {
      case Success(leadDataList) =>
        Right(leadDataList)
      case Failure(err)=>
        Left(SaveSQLError.SQlExceptionError(err))
    }
  }

  def getIndustryCategories(): Either[SaveSQLError, List[LeadMetadata]] = {
    leadFinderDAO.getIndustryCategories() match {
      case Success(leadDataList) =>
        Right(leadDataList)
      case Failure(err) =>
        Left(SaveSQLError.SQlExceptionError(err))
    }
  }


  def getCompanySizeCategories(): Either[SaveSQLError, List[LeadMetadata]] = {
    leadFinderDAO.getCompanySizeCategories() match {
      case Success(leadDataList) =>
        Right(leadDataList)
      case Failure(err) =>
        Left(SaveSQLError.SQlExceptionError(err))
    }
  }

  def getJobTitleCategories(): Either[SaveSQLError, List[LeadMetadata]] = {
    leadFinderDAO.getJobTitleCategories() match {
      case Success(leadDataList) =>
        Right(leadDataList)
      case Failure(err) =>
        Left(SaveSQLError.SQlExceptionError(err))
    }
  }


  def getLeadsByUuid(
                      leadUuids: Seq[LeadUuid]
                    ): Try[List[AddLeadToProspectListData]] = Try {
    leadUuids
      .grouped(perPageLimit)
      .toList
      .flatMap(leadGroupedUuids => {
        leadFinderDAO.getLeadsByUuid(
          leadUuids = leadGroupedUuids
        ).get
      })
  }

  def getAlreadyAddedLeadWithContactType(
                                          leadUuids: Seq[LeadUuid],
                                          orgId: OrgId
                                        ): Try[Map[Long,List[String]]] =  Try {
    leadUuids
      .grouped(perPageLimit)
      .toList
      .flatMap(leadGroupedUuids => {
        leadFinderDAO.getAlreadyAddedLeadWithContactType(
          leadUuids = leadGroupedUuids,
          orgId = orgId
        ).get
      }).toMap
  }


  def addLeadsIntoProspectList(
                                addLeads: AddLeadsIntoProspectListForm,
                                orgId: OrgId,
                                accountId: AccountId,
                                teamId: TeamId,
                                permittedAccountIdsForEditingProspects: Seq[Long],
                                doerAccount: Account
                              )(using logger: SRLogger): Either[AddLeadsIntoProspectListsError, List[Long]] = {

    getLeadsByUuid(
      leadUuids = addLeads.lead_finder_prospect_ids
    ) match {
      case Failure(err) =>
        Left(AddLeadsIntoProspectListsError.SQLException(err))
      case Success(data) =>
        getAlreadyAddedLeadWithContactType(
          leadUuids = addLeads.lead_finder_prospect_ids,
          orgId = orgId
        ) match {
          case Failure(exception) =>
            logger.error(msg = "Error occurred while getAlreadyAddedLeadWithContactType",err = exception)
            Left(AddLeadsIntoProspectListsError.SQLException(exception))
          case Success(alreadyAddedLeads) =>
            val business_domain_zi = data.filter(p => p.dt_src.contains("zi") && p.email.isDefined).map(p => LeadFinderService.extractDomain(p.email.get)).distinct
            leadFinderDAO.getVerifiedBusinessDomains(
              business_domain = business_domain_zi.flatten
            ) match {
              case Failure(exception) =>
                Left(AddLeadsIntoProspectListsError.SQLException(exception))
              case Success(verified_domains_zi) =>
                val filteredData = data.filter(p =>
                  if (p.dt_src.contains("zi")) {
                    if (p.email.isDefined && LeadFinderService.extractDomain(p.email.get).isDefined && verified_domains_zi.contains(LeadFinderService.extractDomain(p.email.get).get)) {
                      true
                    } else {
                      false
                    }
                  } else {
                    true
                  }
                )

                var insertData = List[InsertLeadsBillingLogs]()
                filteredData.map { p =>
                  if (p.email.isDefined) {
                    insertData = insertData :+ InsertLeadsBillingLogs(
                      id = p.id,
                      contactType = ContactType.Email,
                      contactTypeValue = p.email.get,
                      listName = Some(addLeads.list_name),
                      credits_used = AppConfig.LeadFinderCharges.email,
                      first_name = p.first_name,
                      last_name = p.last_name,
                      invalid_email = p.invalid_email,
                      email_checked = p.email_checked,
                      email_checked_at = p.email_checked_at,
                    )
                  }
                  if (p.phone_number.isDefined && addLeads.include_phone) {
                    insertData = insertData :+ InsertLeadsBillingLogs(
                      id = p.id,
                      contactType = ContactType.Phone,
                      contactTypeValue = p.phone_number.get,
                      listName = Some(addLeads.list_name),
                      credits_used = AppConfig.LeadFinderCharges.phone_number,
                      first_name = p.first_name,
                      last_name = p.last_name,
                      invalid_email = p.invalid_email,
                      email_checked = p.email_checked,
                      email_checked_at = p.email_checked_at,
                    )
                  }
                  if (false) {
                    insertData = insertData :+ InsertLeadsBillingLogs(
                      id = p.id,
                      contactType = ContactType.Linkedin,
                      contactTypeValue = p.linkedin_url.get,
                      listName = Some(addLeads.list_name),
                      credits_used = AppConfig.LeadFinderCharges.linkedin_url,
                      first_name = p.first_name,
                      last_name = p.last_name,
                      invalid_email = p.invalid_email,
                      email_checked = p.email_checked,
                      email_checked_at = p.email_checked_at,
                    )
                  }
                  insertData
                }

                val filteredInsertData = insertData.filter(ild => !alreadyAddedLeads.contains(ild.id) || alreadyAddedLeads(ild.id).contains(ild.contactTypeValue))

                val listIdOpt: Option[Long] = prospectDAOService.findOrCreateList(
                  listName = Some(addLeads.list_name),
                  permittedAccountIds = permittedAccountIdsForEditingProspects,
                  listOwnerAccountId = accountId.id,
                  teamId = teamId.id
                )

                val prospectToAdd = filteredData.map { p =>
                  ProspectCreateFormData(
                    email = if (p.email.isDefined) Some(p.email.get) else None,
                    first_name = p.first_name,
                    last_name = p.last_name,
                    custom_fields = Json.obj(),
                    owner_id = Some(accountId.id),
                    list = Some(addLeads.list_name),
                    company = p.company,
                    city = p.city,
                    country = p.country,
                    timezone = None,
                    created_at = None,
                    state = p.state,
                    job_title = p.job_title,
                    phone = if (p.phone_number.isDefined && addLeads.include_phone) Some(p.phone_number.get) else None,
                    phone_2 = None,
                    phone_3 = None,
                    linkedin_url = if (p.linkedin_url.isDefined) Some(p.linkedin_url.get) else None, // Inserting linkedin_url without charging
                  )
                }


                val creditsRequired = LeadFinderService.calculateCentsRequiredForAddingLeads(
                  insertData = filteredInsertData
                )

                val addLeadsTry = for {
                  leadFinderCredits: Int <- organizationService.getRemainingLeadFinderCredits(
                      orgId = orgId
                    )
                    .map(_.toInt)

                  hasCredits: Boolean <- if (leadFinderCredits >= creditsRequired) {
                    Success(true)
                  }
                  else {
                    Failure(new Exception("You don't have enough credits to add leads."))
                  }


                  duplicate_prospects_data: Seq[DuplicateProspectResult] <-
                    prospectService.createOrUpdateProspects(
                        ownerAccountId = accountId.id,
                        teamId = teamId.id,
                        listName = Some(addLeads.list_name),
                        prospects = prospectToAdd,
                        updateProspectType = UpdateProspectType.None,
                        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
                        doerAccount = doerAccount,
                        prospectSource = Some(ProspectSource.SMARTREACH_LEADFINDER),
                        prospectAccountId = None,
                        campaign_id = addLeads.campaign_id.map(campId => campId.id),
                        prospect_tags = None,
                        ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
                        deduplicationColumns = Some(Seq(SrProspectColumns.Email, SrProspectColumns.Phone, SrProspectColumns.LinkedinUrl, SrProspectColumns.CompanyFirstnameLastname)), //all for leadfinder
                        auditRequestLogId = None,
                        SRLogger = logger
                      )
                      .map(_.duplicate_prospects_data)



                  toBeConsumedLeadFinderCredits: ConsumedLeadFinderCredits <-
                    organizationService.getConsumedLeadFinderCredits(
                      orgId = orgId,
                      creditsToBeConsumed = creditsRequired
                    )


                  // TODO: Make it Non Optional once we enable email validation for lead finder for everyone.
                  leadValidationBatchRequestId: Option[LeadValidationBatchReqId] <- if (
                    !AppConfig.RollingUpdates.enableEmailValidationForLeadFinder(orgId = orgId)
                  ) {
                    Success(None)

                  } else {

                    leadFinderDAO.createLeadValidationBatchRequest(
                      teamId = teamId,
                      campaignIdOpt = addLeads.campaign_id,
                      toBeConsumedLeadFinderCredits = toBeConsumedLeadFinderCredits
                    ).flatMap {

                      case None =>

                        val errMsg = "Lead validation batch request id not found"

                        logger.shouldNeverHappen(
                          msg = s"$errMsg - teamId: $teamId :: creditsRequired: $creditsRequired"
                        )

                        Failure(new Exception(errMsg))

                      case Some(batchReqId) =>

                        Success(Some(batchReqId))

                    }

                  }

                  /*
              * Suppose there are 2 Users and they do a search and then once gets a phone call and the other person
              * adds the prospects and as the prospect that are inserted are not reflected on the screen of other user
              * and when the 2nd person continues to add prospect and there are some common prospect in both the list
              * then this condition will help us in filter out that prospects for normal condition we only showing
              * the prospects that are not added in the prospect list
              *
              * Example:-
              *   1st user added the prospect with id  :- 1,2,3,4,5,6
              *   2st user added the prospect with id  :- 1,2,3,4,7,8
              *   common :- 1,2,4
              * so while inserting the data into prospect list we get the fields of 1,2,3,4 as duplicated in
              * duplicate_prospects_data which is Seq[DeDuplicateColumnAndValue] which have two fields
              * deDuplicationColumnValue(contains value of the column duplicate),
              * deDuplicationColumnTypes (contains type(ex:EMAIL,LINKEDIN,PHONE) of the column duplicate),
              * we make the list(duplicate_values_not_inserted) of all the deDuplicationColumnValue
              * and then filter out filteredInsertData where we check if the contactTypeValue inside filteredInsertData not
              * present in duplicate_values_not_inserted
              * */

                  duplicate_values_not_inserted = duplicate_prospects_data.flatMap(dp => dp.deDuplicateColumnAndValue.map(p => p.deDuplicationColumnValue))

                  removedDuplicateFromInsertData = filteredInsertData.filter(p => !duplicate_values_not_inserted.contains(p.contactTypeValue))

                  leadsForValidation: List[EmailForValidation.LeadEmailValidation] = removedDuplicateFromInsertData.flatMap { lf =>

                    lf.contactType match {

                      case ContactType.Linkedin
                           | ContactType.Phone =>

                        None

                      case ContactType.Email =>

                        Some(
                          EmailForValidation.LeadEmailValidation(
                            id = LeadId(id = lf.id),
                            email = lf.contactTypeValue,
                            email_checked = lf.email_checked,
                            email_sent_for_validation = false,
                            email_sent_for_validation_at = None
                          )
                        )

                    }

                  }

                  addedLeads: List[Long] <- leadFinderDAO.addLeadIntoBillingLogs(
                    insertData = removedDuplicateFromInsertData,
                    orgId = orgId,
                    accountId = accountId,
                    teamId = teamId,
                    listId = listIdOpt,
                    leadValidationBatchRequestId = leadValidationBatchRequestId
                  )

                  validEmailsAfterJavaCheck: List[String] <- if (
                    !AppConfig.RollingUpdates.enableEmailValidationForLeadFinder(orgId = orgId)
                  ) {

                    Success(List())

                  } else {

                    emailValidationService.validateLeadEmailsBasedOnJavaEmailAddress(
                      emails = leadsForValidation.map(_.email),
                      teamId = teamId,
                    )

                  }

                  _: Int <- {

                    leadValidationBatchRequestId match {

                      case None =>

                        Success(0)

                      case Some(batchReqId) =>

                        val leadsForValidationAfterJavaCheck: List[EmailForValidation.LeadEmailValidation] =
                          leadsForValidation.filter { l =>
                            validEmailsAfterJavaCheck.contains(l.email)
                          }

                        // TODO: Later on we need to decided this based on some criteria
                        val (highPriorityLeads, lowPriorityLeads) = leadsForValidationAfterJavaCheck.splitAt(10)

                        val seqTryOfProspectSentForValidationCount: Seq[Try[Int]] = Seq(
                          (EmailValidationPriority.High, highPriorityLeads),
                          (EmailValidationPriority.Low, lowPriorityLeads),
                        )
                          .map { case (validationPriority, lfv) =>

                            emailValidationService.sendProspectsForValidation(
                              priority = validationPriority,
                              accountId = accountId.id,
                              orgId = orgId.id,
                              logger = logger,
                              teamId = teamId,
                              isAgency = false, // TODO: Fixme,
                              idsOrEmailsForValidation = IdsOrEmailsForValidation.EntitiesForValidation(
                                emailsForValidations = EmailsForValidationWithInitiator.LeadEmailsForValidation(
                                  entitiesForValidation = lfv,
                                  leadValidationBatchReqId = batchReqId
                                )
                              ),
                            )

                          }

                        Helpers.seqTryToTrySeq(seqTryOfProspectSentForValidationCount).map(_.sum)

                    }

                  }

                  subtractCredits: Int <- organizationService.subtractLeadFinderCredits(
                    orgId = orgId,
                    toBeConsumedLeadFinderCredits = toBeConsumedLeadFinderCredits
                  )

                } yield {
                  addedLeads
                }

                addLeadsTry match {
                  case Failure(e) =>

                    logger.error(
                      msg = s"Failed to add leads into prospect list orgId: $orgId :: teamId: $teamId :: accountId: $accountId :: permittedAccountIdsForEditingProspects: $permittedAccountIdsForEditingProspects :: doerAccountId: ${doerAccount.internal_id}",
                      err = e
                    )

                    if (e.getMessage == "You don't have enough credits to add leads.") {
                      Left(AddLeadsIntoProspectListsError.CreditLimitReachedError)
                    }
                    else {
                      Left(AddLeadsIntoProspectListsError.SQLException(e))
                    }

                  case Success(addedLeads) =>
                    Right(addedLeads)
                }
            }
        }
    }
  }

  def getFinderBillingLogsDetails(
                                    teamId: TeamId,
                                    permittedAccountIds: Seq[AccountId]
                                  ): Try[List[LeadFinderBillingLogsDetails]] = {
    leadFinderDAO.getBillingLogs(
      teamId = teamId,
      permittedAccountIds = permittedAccountIds
    )
  }


  def insertSingleLeadInsideLeadFinder(
                                        mappingFromClient: Map[String, String],
                                        csvRow:Map[String, String],
                                        accountId : Long,
                                        teamId: Long,
                                        ta_id: Long,
                                        fileName: String,
                                        dataSource: Option[String]
                                      )(using Logger:SRLogger): Try[Option[LeadFinderService.LeadUuidWithBasicData]] = {
    val csvCol = LeadFinderService.getCsvUploadCol(
      mappingFromClient = mappingFromClient,
      csvRow = csvRow
    )


    if(csvCol.person_business_email.isDefined) {
      leadFinderDAO.insertSingleLead(
        data = csvCol,
        accountId = accountId,
        teamId = teamId,
        ta_id = ta_id,
        fileName = fileName,
        dataSource = dataSource
      ).map(Some(_))
    } else {
      Success(None)
    }
  }
  def isDomainPresentInVerifiedDomain(domain: String) = {
    leadFinderDAO.isDomainPresentInVerifiedDomain(domain = domain )
  }
}


sealed trait SaveSQLError

object SaveSQLError {

  case class  SQlExceptionError(err : Throwable) extends SaveSQLError

}