package api.lead_finder.service.citys

object LeadCitys3 {


  val citys = Map(
    "Renhuai, China" -> "renhuai",
    "Mira-Bhayandar, India" -> "mira-bhayandar",
    "Kemerovo, Russia" -> "kemerovo",
    "Duisburg, Germany" -> "duisburg",
    "Rasht, Iran" -> "rasht",
    "San Pedro Sula, Honduras" -> "san pedro sula",
    "Bucaramanga, Colombia" -> "bucaramanga",
    "Bhavnagar, India" -> "bhavnagar",
    "Conghua, China" -> "conghua",
    "Changwon, South Korea" -> "changwon",
    "Mianzhu, China" -> "mianzhu",
    "Pohang, South Korea" -> "pohang",
    "Tucson (AZ), United States" -> "tucson",
    "Taicang, China" -> "taicang",
    "Matsuyama, Japan" -> "matsuyama",
    "Hannover, Germany" -> "hannover",
    "Guntur, India" -> "guntur",
    "Lin'an, China" -> "lin'an",
    "Higashiosaka, Japan" -> "higashiosaka",
    "Wanning, China" -> "wanning",
    "Mar del Plata, Argentina" -> "mar del plata",
    "Ryazan, Russia" -> "ryazan",
    "Quilmes, Argentina" -> "quilmes",
    "Tomsk, Russia" -> "tomsk",
    "Linhe, China" -> "linhe",
    "Chibi, China" -> "chibi",
    "Jos, Nigeria" -> "jos",
    "Penza, Russia" -> "penza",
    "Laohekou, China" -> "laohekou",
    "Wenchang, China" -> "wenchang",
    "Juiz de Fora, Brazil" -> "juiz de fora",
    "Zhijiang, China" -> "zhijiang",
    "Newcastle, Australia" -> "newcastle",
    "Pinghu, China" -> "pinghu",
    "Zhaoqing, China" -> "zhaoqing",
    "Naberezhnye Tchelny, Russia" -> "naberezhnye tchelny",
    "Qingyuan, China" -> "qingyuan",
    "Tula, Russia" -> "tula",
    "Belgaum, India" -> "belgaum",
    "Pondicherry, India" -> "pondicherry",
    "Aracaju, Brazil" -> "aracaju",
    "Pasig, Philippines" -> "pasig",
    "Mykolayiv (Nikolaevskaya oblast), Ukraine" -> "mykolayiv",
    "Ibagué, Colombia" -> "ibagué",
    "Hanzhong, China" -> "hanzhong",
    "Tlaxcala, Mexico" -> "tlaxcala",
    "Chuxiong, China" -> "chuxiong",
    "Denpasar, Indonesia" -> "denpasar",
    "Lipetsk, Russia" -> "lipetsk",
    "Dengta, China" -> "dengta",
    "Tuxtla Gutiérrez, Mexico" -> "tuxtla gutiérrez",
    "Songkhla, Thailand" -> "songkhla",
    "Mariupol, Ukraine" -> "mariupol",
    "Danjiangkou, China" -> "danjiangkou",
    "Chiclayo, Peru" -> "chiclayo",
    "COPENHAGEN, Denmark" -> "copenhagen",
    "Penglai, China" -> "penglai",
    "Oaxaca de Juárez, Mexico" -> "oaxaca de juárez",
    "Aba, Nigeria" -> "aba",
    "Astrakhan, Russia" -> "astrakhan",
    "MACAO, China : Macao SAR" -> "macao",
    "Eskisehir, Turkey" -> "eskisehir",
    "Shishi, China" -> "shishi",
    "Kannur, India" -> "kannur",
    "Ananindeua, Brazil" -> "ananindeua",
    "Bokaro Steel City, India" -> "bokaro steel city",
    "SAN SALVADOR, El Salvador" -> "san salvador",
    "Kolhapur, India" -> "kolhapur",
    "Xinzhou, China" -> "xinzhou",
    "Joinville, Brazil" -> "joinville",
    "Dangyang, China" -> "dangyang",
    "Londrina, Brazil" -> "londrina",
    "Hebi, China" -> "hebi",
    "Ganzhou, China" -> "ganzhou",
    "Hamadan, Iran" -> "hamadan",
    "Yucheng, China" -> "yucheng",
    "Albuquerque (NM), United States" -> "albuquerque",
    "Agadir, Morocco" -> "agadir",
    "Chuzhou, China" -> "chuzhou",
    "Genève, Switzerland" -> "genève",
    "Durgapur, India" -> "durgapur",
    "Siping, China" -> "siping",
    "Surakarta, Indonesia" -> "surakarta",
    "Gomel, Belarus" -> "gomel",
    "Ajmer, India" -> "ajmer",
    "Leipzig, Germany" -> "leipzig",
    "Kirov, Russia" -> "kirov",
    "Belford Roxo, Brazil" -> "belford roxo",
    "Nanping, China" -> "nanping",
    "Luhansk, Ukraine" -> "luhansk",
    "Al-Sharjah, United Arab Emirates" -> "al-sharjah",
    "Shuangyashan, China" -> "shuangyashan",
    "Qitaihe, China" -> "qitaihe",
    "Zhangye, China" -> "zhangye",
    "Nürnberg, Germany" -> "nürnberg",
    "Bâle, Switzerland" -> "bâle",
    "Oujda, Morocco" -> "oujda",
    "Erbil, Iraq" -> "erbil",
    "Valenzuela, Philippines" -> "valenzuela",
    "Hongjiang, China" -> "hongjiang",
    "Baicheng, China" -> "baicheng",
    "Raurkela, India" -> "raurkela",
    "PANAMA CITY, Panama" -> "panama city",
    "Barcelona-Puerto La Cruz, Venezuela" -> "barcelona-puerto la cruz",
    "Huanghua, China" -> "huanghua",
    "Pontianak, Indonesia" -> "pontianak",
    "Himeji, Japan" -> "himeji",
    "Sanya, China" -> "sanya",
    "Orizaba, Mexico" -> "orizaba",
    "Dunhua, China" -> "dunhua",
    "Kawaguchi, Japan" -> "kawaguchi",
    "Durango (Victoria de Durango), Mexico" -> "durango",
    "Jian'ou, China" -> "jian'ou",
    "Tombouctou, Mali" -> "tombouctou",
    "Niterói, Brazil" -> "niterói",
    "Dresden, Germany" -> "dresden",
    "Arak, Iran" -> "arak",
    "Soledad, Colombia" -> "soledad",
    "Chaoyang (Liaoning), China" -> "chaoyang",
    "Shahe, China" -> "shahe",
    "Jiaohe, China" -> "jiaohe",
    "Long Beach (CA), United States" -> "long beach",
    "Jiangshan, China" -> "jiangshan",
    "Jian (Jiangxi), China" -> "jian",
    "Anda, China" -> "anda",
    "Jiande, China" -> "jiande",
    "Ulhasnagar, India" -> "ulhasnagar",
    "Las Piñas, Philippines" -> "las piñas",
    "Matsudo, Japan" -> "matsudo",
    "Port Said, Egypt" -> "port said",
    "Yuanping, China" -> "yuanping",
    "Gaoping, China" -> "gaoping",
    "Fenghua, China" -> "fenghua",
    "Makati, Philippines" -> "makati",
    "Qingzhen, China" -> "qingzhen",
    "Siliguri, India" -> "siliguri",
    "Kurashiki, Japan" -> "kurashiki",
    "Ipoh, Malaysia" -> "ipoh",
    "Lanus, Argentina" -> "lanus",
    "Taguig, Philippines" -> "taguig",
    "Nan'gong, China" -> "nan'gong",
    "Al-Kamishli, Syria" -> "al-kamishli",
    "Sao Joao de Meriti, Brazil" -> "sao joao de meriti",
    "Ichikawa, Japan" -> "ichikawa",
    "London, Canada" -> "london",
    "Nishinomiya, Japan" -> "nishinomiya",
    "Constantine (Qacentina), Algeria" -> "constantine",
    "Enping, China" -> "enping",
    "Weihui, China" -> "weihui",
    "Adhamiyah, Iraq" -> "adhamiyah",
    "Kitchener, Canada" -> "kitchener",
    "Duyun, China" -> "duyun",
    "Jhansi, India" -> "jhansi",
    "Amagasaki, Japan" -> "amagasaki",
    "Oita, Japan" -> "oita",
    "Liaoyuan, China" -> "liaoyuan",
    "Cagayan de Oro, Philippines" -> "cagayan de oro",
    "Antwerpen (Anvers), Belgium" -> "antwerpen",
    "Brighton/Worthing/Littlehampton, United Kingdom" -> "brighton/worthing/littlehampton",
    "Fresno (CA), United States" -> "fresno",
    "Baiyin, China" -> "baiyin",
    "Tonghua, China" -> "tonghua",
    "Maracay, Venezuela" -> "maracay",
    "Sargodha, Pakistan" -> "sargodha",
    "Utsunomiya, Japan" -> "utsunomiya",
    "Gdansk, Poland" -> "gdansk",
    "Sanhe, China" -> "sanhe",
    "Sacramento (CA), United States" -> "sacramento",
    "Pingliang, China" -> "pingliang",
    "New Orleans (LA), United States" -> "new orleans",
    "Kanazawa, Japan" -> "kanazawa",
    "Zhangjiajie, China" -> "zhangjiajie",
    "Cheboksary, Russia" -> "cheboksary",
    "Gejiu, China" -> "gejiu",
    "Aparecida de Goiania, Brazil" -> "aparecida de goiania",
    "Saharanpur, India" -> "saharanpur",
    "Cleveland (OH), United States" -> "cleveland",
    "Mixco, Guatemala" -> "mixco",
    "BANGUI, Central African Republic" -> "bangui",
    "Yulin (Shaanxi), China" -> "yulin",
    "LOME, Togo" -> "lome",
    "Qionghai, China" -> "qionghai",
    "Paranaque, Philippines" -> "paranaque",
    "Edinburgh, United Kingdom" -> "edinburgh",
    "Linxiang, China" -> "linxiang",
    "Puyang, China" -> "puyang",
    "Sangli-Miraj-Kupwad, India" -> "sangli-miraj-kupwad",
    "Qinyang, China" -> "qinyang",
    "Pereira, Colombia" -> "pereira",
    "Karaganda, Kazakhstan" -> "karaganda",
    "Kansas City (MO), United States" -> "kansas city",
    "Lyon, France" -> "lyon",
    "Jingdezhen, China" -> "jingdezhen",
    "Jambi, Indonesia" -> "jambi",
    "Huadian, China" -> "huadian",
    "Putian, China" -> "putian",
    "Jinghong, China" -> "jinghong",
    "Cangzhou, China" -> "cangzhou",
    "Mesa (AZ), United States" -> "mesa",
    "Nagasaki, Japan" -> "nagasaki",
    "Beian, China" -> "beian",
    "Portsmouth, United Kingdom" -> "portsmouth",
    "Bhatpara, India" -> "bhatpara",
    "Johore Bharu, Malaysia" -> "johore bharu",
    "Tabuk, Saudi Arabia" -> "tabuk",
    "Leicester, United Kingdom" -> "leicester",
    "Taonan, China" -> "taonan",
    "Sanshui, China" -> "sanshui",
    "Xinle, China" -> "xinle",
    "Virginia Beach (VA), United States" -> "virginia beach",
    "Mishan, China" -> "mishan",
    "Ning'an, China" -> "ning'an",
    "Chengde, China" -> "chengde",
    "Bryansk, Russia" -> "bryansk",
    "Hailin, China" -> "hailin",
    "Gulbarga, India" -> "gulbarga",
    "Bobo Dioulasso, Burkina Faso" -> "bobo dioulasso",
    "Masan, South Korea" -> "masan",
    "Tieling, China" -> "tieling",
    "Kaili, China" -> "kaili",
    "Ogbomosho, Nigeria" -> "ogbomosho",
    "Shifang, China" -> "shifang",
    "Yanji, China" -> "yanji",
    "Yazd, Iran" -> "yazd",
    "Firozabad, India" -> "firozabad",
    "Tirunelveli, India" -> "tirunelveli",
    "Zarqa, Jordan" -> "zarqa",
    "Matamoros, Mexico" -> "matamoros",
    "Ujjain, India" -> "ujjain",
    "Nanded, India" -> "nanded",
    "Daan, China" -> "daan",
    "Campos dos Goytacazes, Brazil" -> "campos dos goytacazes",
    "Balikpapan, Indonesia" -> "balikpapan",
    "Bacolod, Philippines" -> "bacolod",
    "Wuhai, China" -> "wuhai",
    "SAN JUAN, Puerto Rico" -> "san juan",
    "Yokosuka, Japan" -> "yokosuka",
    "Santiago de Cuba, Cuba" -> "santiago de cuba",
    "BRATISLAVA, Slovakia" -> "bratislava",
    "Jinhua, China" -> "jinhua",
    "Matola, Mozambique" -> "matola",
    "Lechang, China" -> "lechang",
    "Coatzacoalcos, Mexico" -> "coatzacoalcos",
    "Emeishan, China" -> "emeishan",
    "Kaliningrad (Kaliningradskaya oblast), Russia" -> "kaliningrad",
    "Hengshui, China" -> "hengshui",
    "Fangchenggang, China" -> "fangchenggang",
    "Santa Marta, Colombia" -> "santa marta",
    "Sialkote, Pakistan" -> "sialkote",
    "Yongji, China" -> "yongji",
    "Toyama, Japan" -> "toyama",
    "DOHA, Qatar" -> "doha",
    "Fujin, China" -> "fujin",
    "Kirkuk, Iraq" -> "kirkuk",
    "LIBREVILLE, Gabon" -> "libreville",
    "Fukuyama, Japan" -> "fukuyama",
    "Santos, Brazil" -> "santos",
    "Cheonan, South Korea" -> "cheonan",
    "Suez, Egypt" -> "suez",
    "Murcia, Spain" -> "murcia",
    "Utrecht, Netherlands" -> "utrecht",
    "Makyivka, Ukraine" -> "makyivka",
    "Sao José do Rio Prêto, Brazil" -> "sao josé do rio prêto",
    "Omaha (NE), United States" -> "omaha",
    "Xiaoyi, China" -> "xiaoyi",
    "Mauá, Brazil" -> "mauá",
    "Soacha, Colombia" -> "soacha",
    "Malatya, Turkey" -> "malatya",
    "Manado, Indonesia" -> "manado",
    "Toyota, Japan" -> "toyota",
    "Caxias do Sul, Brazil" -> "caxias do sul",
    "Magnitogorsk, Russia" -> "magnitogorsk",
    "General Santos, Philippines" -> "general santos",
    "Sanliurfa, Turkey" -> "sanliurfa",
    "Ivanovo, Russia" -> "ivanovo",
    "Szczecin, Poland" -> "szczecin",
    "Sihui, China" -> "sihui",
    "Gaza, Palestine, Occupied Territory" -> "gaza",
    "Shanwei, China" -> "shanwei",
    "Lianzhou, China" -> "lianzhou",
    "Malegaon, India" -> "malegaon",
    "Zhalantun, China" -> "zhalantun",
    "Yuxi, China" -> "yuxi",
    "Bahawalpur, Pakistan" -> "bahawalpur",
    "Rajahmundry, India" -> "rajahmundry",
    "Enugu, Nigeria" -> "enugu",
    "Betim, Brazil" -> "betim",
    "ASHGABAT, Turkmenistan" -> "ashgabat",
    "General San Martín, Argentina" -> "general san martín",
    "Florianópolis, Brazil" -> "florianópolis",
    "Santa Fé, Argentina" -> "santa fé",
    "Basra, Iraq" -> "basra",
    "Huangshan, China" -> "huangshan",
    "Yakeshi, China" -> "yakeshi",
    "Heshan (Guangdong), China" -> "heshan",
    "Kursk, Russia" -> "kursk",
    "Tver, Russia" -> "tver",
    "Machida, Japan" -> "machida",
    "Pietermaritzburg, South Africa" -> "pietermaritzburg",
    "Vila Velha, Brazil" -> "vila velha",
    "Nellore, India" -> "nellore",
    "Shuangliao, China" -> "shuangliao",
    "Tongchuan, China" -> "tongchuan",
    "Hirakata, Japan" -> "hirakata",
    "Yan'an, China" -> "yan'an",
    "Ningde, China" -> "ningde",
    "Sochi, Russia" -> "sochi",
    "Huambo, Angola" -> "huambo",
    "Akola, India" -> "akola",
    "Gifu, Japan" -> "gifu",
    "Ruichang, China" -> "ruichang",
    "Loudi, China" -> "loudi",
    "Adan, Yemen" -> "adan",
    "Luquan, China" -> "luquan",
    "Beira, Mozambique" -> "beira",
    "St. Catharines, Canada" -> "st. catharines",
    "TALLINN, Estonia" -> "tallinn",
    "Erzurum, Turkey" -> "erzurum",
    "Fujisawa, Japan" -> "fujisawa",
    "Diadema, Brazil" -> "diadema",
    "Oakland (CA), United States" -> "oakland",
    "El-Mahalla El-Kubra, Egypt" -> "el-mahalla el-kubra",
    "Gao, Mali" -> "gao",
    "Serra, Brazil" -> "serra",
    "Gaya, India" -> "gaya",
    "TIRANA, Albania" -> "tirana",
    "Bochum, Germany" -> "bochum",
    "Yogyakarta, Indonesia" -> "yogyakarta",
    "South Dum Dum, India" -> "south dum dum",
    "Namangan, Uzbekistan" -> "namangan",
    "Marikina, Philippines" -> "marikina",
    "Erode, India" -> "erode",
    "Ardabil, Iran" -> "ardabil",
    "Villa Nueva, Guatemala" -> "villa nueva",
    "Villavicencio, Colombia" -> "villavicencio",
    "Toulouse, France" -> "toulouse",
    "Carapicuíba, Brazil" -> "carapicuíba",
    "Udaipur, India" -> "udaipur",
    "Maheshtala, India" -> "maheshtala",
    "Rajshahi, Bangladesh" -> "rajshahi",
    "Hami, China" -> "hami",
    "Pasto, Colombia" -> "pasto",
    "Vellore, India" -> "vellore",
    "Olinda, Brazil" -> "olinda",
    "Changji, China" -> "changji",
    "Fenyang, China" -> "fenyang",
    "Hancheng, China" -> "hancheng",
    "Toyonaka, Japan" -> "toyonaka",
    "Yidu, China" -> "yidu",
    "Thessaloniki, Greece" -> "thessaloniki",
    "Montería, Colombia" -> "montería",
    "Dazhou, China" -> "dazhou",
    "Bournemouth, United Kingdom" -> "bournemouth",
    "Keelung, China" -> "keelung",
    "DJIBOUTI, Djibouti" -> "djibouti",
    "Da Nang, Viet Nam" -> "da nang",
    "Tulsa (OK), United States" -> "tulsa",
    "Halifax, Canada" -> "halifax",
    "Kuerle, China" -> "kuerle",
    "Ningguo, China" -> "ningguo",
    "Manizales, Colombia" -> "manizales",
    "Wuzhou, China" -> "wuzhou",
    "Porto Velho, Brazil" -> "porto velho",
    "Kashiwa, Japan" -> "kashiwa",
    "Bello, Colombia" -> "bello",
    "Kollam, India" -> "kollam",
    "Campina Grande, Brazil" -> "campina grande",
    "Vereeniging, South Africa" -> "vereeniging",
    "Chiang Mai, Thailand" -> "chiang mai",
    "Muntinlupa, Philippines" -> "muntinlupa",
    "An'guo, China" -> "an'guo",
    "ABUJA, Nigeria" -> "abuja",
    "Nizhny Tagil, Russia" -> "nizhny tagil",
    "Villahermosa, Mexico" -> "villahermosa",
    "Nagano, Japan" -> "nagano",
    "Buraydah, Saudi Arabia" -> "buraydah",
    "Samsun, Turkey" -> "samsun",
    "Honolulu (HI), United States" -> "honolulu",
    "Las Palmas de Gran Canaria, Spain" -> "las palmas de gran canaria",
    "PalmasdeGranCanaria, Spain" -> "palmasdegrancanaria",
    "Sevastopol, Ukraine" -> "sevastopol",
    "WELLINGTON, New Zealand" -> "wellington",
    "Wakayama, Japan" -> "wakayama",
    "Ulan-Ude, Russia" -> "ulan-ude",
    "Palma de Mallorca, Spain" -> "palma de mallorca",
    "Christchurch, New Zealand" -> "christchurch",
    "Jizhou, China" -> "jizhou",
    "Huanggang, China" -> "huanggang",
    "Xuchang, China" -> "xuchang",
    "Bologna, Italy" -> "bologna",
    "Jiexiu, China" -> "jiexiu",
    "Tanta, Egypt" -> "tanta",
    "Nanxiong, China" -> "nanxiong",
    "Khamis Mushayt, Saudi Arabia" -> "khamis mushayt",
    "Toyohashi, Japan" -> "toyohashi",
    "Moji das Cruzes, Brazil" -> "moji das cruzes",
    "Ichinomiya, Japan" -> "ichinomiya",
    "Ndola, Zambia" -> "ndola",
    "Salta, Argentina" -> "salta",
    "Nara, Japan" -> "nara",
    "Oyo, Nigeria" -> "oyo",
    "Colorado Springs (CO), United States" -> "colorado springs",
    "Reading/Wokingham, United Kingdom" -> "reading/wokingham",
    "Mansûra, Egypt" -> "mansûra",
    "Wuppertal, Germany" -> "wuppertal",
    "Kakinada, India" -> "kakinada",
    "Jalgaon, India" -> "jalgaon",
    "Hejin, China" -> "hejin",
    "Mogilev, Belarus" -> "mogilev",
    "Klang, Malaysia" -> "klang",
    "Macapá, Brazil" -> "macapá",
    "Brno, Czech Republic" -> "brno",
    "Piracicaba, Brazil" -> "piracicaba",
    "Firenze, Italy" -> "firenze",
    "Iloilo, Philippines" -> "iloilo",
    "Benoni, South Africa" -> "benoni",
    "Udon Thani, Thailand" -> "udon thani",
    "Teesside, United Kingdom" -> "teesside",
    "Bydgoszcz, Poland" -> "bydgoszcz",
    "Valledupar, Colombia" -> "valledupar",
    "Sulamaniya, Iraq" -> "sulamaniya",
    "Davangere, India" -> "davangere",
    "Chaozhou, China" -> "chaozhou",
    "Warri, Nigeria" -> "warri",
    "Arlington (TX), United States" -> "arlington",
    "Tongling, China" -> "tongling",
    "Kitwe, Zambia" -> "kitwe",
    "The Potteries, United Kingdom" -> "the potteries",
    "Bouake, Côte d'Ivoire" -> "bouake",
    "Piura, Peru" -> "piura",
    "BEIRUT, Lebanon" -> "beirut",
    "Samarkand, Uzbekistan" -> "samarkand",
    "Cariacica, Brazil" -> "cariacica",
    "Misurata, Libya" -> "misurata",
    "Kaunas, Lithuania" -> "kaunas",
    "Stavropol, Russia" -> "stavropol",
    "Hsinchu, China" -> "hsinchu",
    "Vinnytsya, Ukraine" -> "vinnytsya",
    "Dongfang, China" -> "dongfang",
    "ASMARA, Eritrea" -> "asmara",
    "Simpheropol, Ukraine" -> "simpheropol",
    "Yi'ning, China" -> "yi'ning",
    "Bauru, Brazil" -> "bauru",
    "Iquitos, Peru" -> "iquitos",
    "Nuevo Laredo, Mexico" -> "nuevo laredo",
    "Wuzhong, China" -> "wuzhong",
    "Uijeongbu (Eujeongbu), South Korea" -> "uijeongbu",
    "Arkhangelsk, Russia" -> "arkhangelsk",
    "Asahikawa, Japan" -> "asahikawa",
    "Pasay, Philippines" -> "pasay",
    "Kahramanmaras, Turkey" -> "kahramanmaras",
    "Wichita (KS), United States" -> "wichita",
    "Okazaki, Japan" -> "okazaki",
    "Tieli, China" -> "tieli",
    "Iwaki, Japan" -> "iwaki",
    "Meizhou, China" -> "meizhou",
    "Lublin, Poland" -> "lublin",
    "Bilbao, Spain" -> "bilbao",
    "Panipat, India" -> "panipat",
    "Suita, Japan" -> "suita",
    "Kherson, Ukraine" -> "kherson",
    "Itaquaquecetuba, Brazil" -> "itaquaquecetuba",
    "Abeokuta, Nigeria" -> "abeokuta",
    "San Juan, Argentina" -> "san juan",
    "Annaba, Algeria" -> "annaba",
    "Huaying, China" -> "huaying",
    "Vitebsk, Belarus" -> "vitebsk",
    "Takatsuki, Japan" -> "takatsuki",
    "Zixing, China" -> "zixing",
    "Petaling Jaya, Malaysia" -> "petaling jaya",
    "Bloemfontein, South Africa" -> "bloemfontein",
    "Onitsha, Nigeria" -> "onitsha",
    "Bhagalpur, India" -> "bhagalpur",
    "Fife, United Kingdom" -> "fife",
    "Al-Rayyan, Qatar" -> "al-rayyan",
    "Montes Claros, Brazil" -> "montes claros",
    "Jundiaí, Brazil" -> "jundiaí",
    "Panihati, India" -> "panihati",
    "Lishui, China" -> "lishui",
    "Ahmednagar, India" -> "ahmednagar",
    "Manukau, New Zealand" -> "manukau",
    "Irapuato, Mexico" -> "irapuato",
    "Huaihua, China" -> "huaihua",
    "Pelotas, Brazil" -> "pelotas",
    "Jiuquan, China" -> "jiuquan",
    "Zanjan, Iran" -> "zanjan",
    "Belgorod, Russia" -> "belgorod",
    "Kathlehong, South Africa" -> "kathlehong",
    "SAN JOSE, Costa Rica" -> "san jose",
    "Kempton Park, South Africa" -> "kempton park",
    "Oshawa, Canada" -> "oshawa",
    "Avellaneda, Argentina" -> "avellaneda",
    "BERNE, Switzerland" -> "berne",
    "Assyût, Egypt" -> "assyût",
    "Nice, France" -> "nice",
    "Plovdiv, Bulgaria" -> "plovdiv",
    "Kaluga, Russia" -> "kaluga",
    "Antofagasta, Chile" -> "antofagasta",
    "Mataram, Indonesia" -> "mataram",
    "Gumi (Kumi), South Korea" -> "gumi",
    "Raleigh (NC), United States" -> "raleigh",
    "Dhule, India" -> "dhule",
    "Kashi (Xinjiang), China" -> "kashi",
    "Baise, China" -> "baise",
    "Santa Ana (CA), United States" -> "santa ana",
    "Vladimir, Russia" -> "vladimir",
    "Jinju (Chinju), South Korea" -> "jinju",
    "Umlazi, South Africa" -> "umlazi",
    "Lengshuijiang, China" -> "lengshuijiang",
    "Malabon, Philippines" -> "malabon",
    "Koriyama, Japan" -> "koriyama",
    "Wudalianchi, China" -> "wudalianchi",
    "Andizhan, Uzbekistan" -> "andizhan",
    "Zhumadian, China" -> "zhumadian",
    "Takamatsu, Japan" -> "takamatsu",
    "Luxi (Yunnan), China" -> "luxi",
    "Sanming, China" -> "sanming",
    "Coventry/Bedworth, United Kingdom" -> "coventry/bedworth",
    "Rajpur Sonarpur, India" -> "rajpur sonarpur",
    "Carrefour, Haiti" -> "carrefour",
    "Tokorozawa, Japan" -> "tokorozawa",
    "Taraz, Kazakhstan" -> "taraz",
    "Khouribga, Morocco" -> "khouribga",
    "Buenaventura, Colombia" -> "buenaventura",
    "Sukkur, Pakistan" -> "sukkur",
    "Surat Thani, Thailand" -> "surat thani",
    "Baishan, China" -> "baishan",
    "Yong'an, China" -> "yong'an",
    "Yaan, China" -> "yaan",
    "Kaesong, North Korea" -> "kaesong",
    "Victoria, Canada" -> "victoria",
    "Gwangmyeong (Kwangmyong), South Korea" -> "gwangmyeong",
    "Kawagoe, Japan" -> "kawagoe",
    "Ciudad del Este, Paraguay" -> "ciudad del este",
    "Kochi, Japan" -> "kochi",
    "Canoas, Brazil" -> "canoas",
    "Akita, Japan" -> "akita",
    "Basilan, Philippines" -> "basilan",
    "Windsor, Canada" -> "windsor",
    "Gimhae (Kimhae), South Korea" -> "gimhae",
    "Anaheim (CA), United States" -> "anaheim",
    "San Miguelito, Panama" -> "san miguelito",
    "Muzaffarnagar, India" -> "muzaffarnagar",
    "Vladikavkaz (Osetinskaya ASSR), Russia" -> "vladikavkaz",
    "Bilaspur, India" -> "bilaspur",
    "Thrissur, India" -> "thrissur",
    "Sokoto, Nigeria" -> "sokoto",
    "Sao Vicente, Brazil" -> "sao vicente",
    "Chimbote, Peru" -> "chimbote",
    "Kurgan, Russia" -> "kurgan",
    "Franca, Brazil" -> "franca",
    "Al-Ayn, United Arab Emirates" -> "al-ayn",
    "Sanandaj, Iran" -> "sanandaj",
    "Cardiff, United Kingdom" -> "cardiff",
    "Shangrao, China" -> "shangrao",
    "Khon Kaen, Thailand" -> "khon kaen",
    "Cuzco, Peru" -> "cuzco",
    "Sinuiji, North Korea" -> "sinuiji",
    "Bari, Italy" -> "bari",
    "CANBERRA, Australia" -> "canberra",
    "Orel, Russia" -> "orel",
    "Maringá, Brazil" -> "maringá",
    "Van, Turkey" -> "van",
    "Zhoukou, China" -> "zhoukou",
    "Iksan (Iri), South Korea" -> "iksan",
    "Saransk, Russia" -> "saransk",
    "Patiala, India" -> "patiala",
    "Shahjahanpur, India" -> "shahjahanpur",
    "Ribeirao das Neves, Brazil" -> "ribeirao das neves",
    "Córdoba, Spain" -> "córdoba",
    "Alicante, Spain" -> "alicante",
    "Eindhoven, Netherlands" -> "eindhoven",
    "Pachuca (de Soto), Mexico" -> "pachuca",
    "Bielefeld, Germany" -> "bielefeld",
    "North Lanarkshire, United Kingdom" -> "north lanarkshire",
    "Celaya, Mexico" -> "celaya",
    "Kurnool, India" -> "kurnool",
    "Grodno, Belarus" -> "grodno",
    "Valladolid, Spain" -> "valladolid",
    "Birkenhead, United Kingdom" -> "birkenhead",
    "Neiva, Colombia" -> "neiva",
    "Murmansk, Russia" -> "murmansk",
    "Mathura, India" -> "mathura",
    "Anápolis, Brazil" -> "anápolis",
    "Volzhsky, Russia" -> "volzhsky",
    "Maebashi, Japan" -> "maebashi",
    "Hechi, China" -> "hechi",
    "Jianyang (Fujian), China" -> "jianyang",
    "Smolensk, Russia" -> "smolensk",
    "Xifeng, China" -> "xifeng",
    "Ta'izz, Yemen" -> "ta'izz",
    "Vitória, Brazil" -> "vitória",
    "Bellary, India" -> "bellary",
    "Pittsburgh (PA), United States" -> "pittsburgh",
    "Iasi, Romania" -> "iasi",
    "Koshigaya, Japan" -> "koshigaya",
    "Korba, India" -> "korba",
    "Bandar-e-Abbas, Iran" -> "bandar-e-abbas",
    "Kamarhati, India" -> "kamarhati",
    "Shizuishan, China" -> "shizuishan",
    "Khayelitsa, South Africa" -> "khayelitsa",
    "NoviSad, Serbia" -> "novisad",
    "Rio Branco, Brazil" -> "rio branco",
    "Meixian, China" -> "meixian",
    "Caucaia, Brazil" -> "caucaia",
    "Khoramabad, Iran" -> "khoramabad",
    "Wugang (Henan), China" -> "wugang",
    "Okene, Nigeria" -> "okene",
    "Pathum Thani, Thailand" -> "pathum thani",
    "Naha, Japan" -> "naha",
    "Varna, Bulgaria" -> "varna",
    "Hulin, China" -> "hulin",
    "Aomori, Japan" -> "aomori",
    "Calabar, Nigeria" -> "calabar",
    "Ostrava, Czech Republic" -> "ostrava",
    "Petrópolis, Brazil" -> "petrópolis",
    "Miyazaki, Japan" -> "miyazaki",
    "Muling, China" -> "muling",
    "Lausanne, Switzerland" -> "lausanne",
    "Sagar, India" -> "sagar",
    "Foz do Iguaçu, Brazil" -> "foz do iguaçu",
    "Najaf, Iraq" -> "najaf",
    "Cincinnati (OH), United States" -> "cincinnati",
    "Tongren, China" -> "tongren",
    "Port Sudan, Sudan" -> "port sudan",
    "Cherepovets, Russia" -> "cherepovets",
    "Mannheim, Germany" -> "mannheim",
    "Qazvin, Iran" -> "qazvin",
    "Huancayo, Peru" -> "huancayo",
    "Camagüey, Cuba" -> "camagüey",
    "Yamunanagar, India" -> "yamunanagar",
    "Kurume, Japan" -> "kurume",
    "Chita, Russia" -> "chita",
    "Cluj-Napoca, Romania" -> "cluj-napoca",
    "Constanta, Romania" -> "constanta",
    "Muzaffarpur, India" -> "muzaffarpur",
    "Guarujá, Brazil" -> "guarujá",
    "Ponta Grossa, Brazil" -> "ponta grossa",
    "Pavlodar, Kazakhstan" -> "pavlodar",
    "Southampton, United Kingdom" -> "southampton",
    "Jincheng, China" -> "jincheng",
    "Luohe, China" -> "luohe",
    "Cuenca, Ecuador" -> "cuenca",
    "Yokkaichi, Japan" -> "yokkaichi",
    "Ganja, Azerbaijan" -> "ganja",
    "Timisoara, Romania" -> "timisoara",
    "Tepic, Mexico" -> "tepic",
    "Nampula, Mozambique" -> "nampula",
    "Yeosu, South Korea" -> "yeosu",
    "Poltava, Ukraine" -> "poltava",
    "Catania, Italy" -> "catania",
    "Tirupati, India" -> "tirupati",
    "Ambattur, India" -> "ambattur",
    "Brest, Belarus" -> "brest",
    "South Lanarkshire, United Kingdom" -> "south lanarkshire",
    "Otsu, Japan" -> "otsu",
    "Yangzhong, China" -> "yangzhong",
    "Barcelona, Venezuela" -> "barcelona",
    "Kingston-upon-Hull, United Kingdom" -> "kingston-upon-hull",
    "Toledo (OH), United States" -> "toledo",
    "Gaoming, China" -> "gaoming",
    "Craiova, Romania" -> "craiova",
    "Wonsan, North Korea" -> "wonsan",
    "Latur, India" -> "latur",
    "Paulista, Brazil" -> "paulista",
    "San Isidro, Argentina" -> "san isidro",
    "Blumenou, Brazil" -> "blumenou",
    "Horlivka, Ukraine" -> "horlivka",
    "Al-Hudaydah (Hodeidah), Yemen" -> "al-hudaydah",
    "Dexing, China" -> "dexing",
    "Chandrapur, India" -> "chandrapur",
    "Aurora (CO), United States" -> "aurora",
    "Kerbala, Iraq" -> "kerbala",
    "Galati, Romania" -> "galati",
    "Naltchik, Russia" -> "naltchik",
    "Kharagpur, India" -> "kharagpur",
    "Kasugai, Japan" -> "kasugai",
    "Bakersfield (CA), United States" -> "bakersfield",
    "Denizli, Turkey" -> "denizli",
    "Bialystok, Poland" -> "bialystok",
    "Anning, China" -> "anning",
    "NEW DELHI, India" -> "new delhi",
    "Chernihiv, Ukraine" -> "chernihiv",
    "Rohtak, India" -> "rohtak",
    "Vologda, Russia" -> "vologda",
    "Jishou, China" -> "jishou",
    "Århus, Denmark" -> "århus",
    "Hakodate, Japan" -> "hakodate",
    "Noida, India" -> "noida",
    "Jhang, Pakistan" -> "jhang",
    "Vigo, Spain" -> "vigo",
    "Garoua, Cameroon" -> "garoua",
    "Fuquan, China" -> "fuquan",
    "Resistencia, Argentina" -> "resistencia",
    "Viña del Mar, Chile" -> "viña del mar",
    "Kaiyuan (Yunnan), China" -> "kaiyuan",
    "Sumgayit, Azerbaijan" -> "sumgayit",
    "Cherkasy, Ukraine" -> "cherkasy",
    "Akashi, Japan" -> "akashi",
    "Fukushima, Japan" -> "fukushima",
    "Shimonoseki, Japan" -> "shimonoseki",
    "Kukatpalle, India" -> "kukatpalle",
    "Soyapango, El Salvador" -> "soyapango",
    "Surgut, Russia" -> "surgut",
    "Riverside (CA), United States" -> "riverside",
    "Kulti, India" -> "kulti",
    "Vitória da Conquista, Brazil" -> "vitória da conquista",
    "Brahmapur, India" -> "brahmapur",
    "Vicente López, Argentina" -> "vicente lópez",
    "Sanmenxia, China" -> "sanmenxia",
    "Ust-Kamenogorsk, Kazakhstan" -> "ust-kamenogorsk",
    "Shaowu, China" -> "shaowu",
    "Al-Hufuf, Saudi Arabia" -> "al-hufuf",
    "Kindia, Guinea" -> "kindia",
    "Morioka, Japan" -> "morioka",
    "Nizamabad, India" -> "nizamabad",
    "Stockton (CA), United States" -> "stockton",
    "Palmira, Colombia" -> "palmira",
    "Quzhou, China" -> "quzhou",
    "Barddhaman, India" -> "barddhaman",
    "Ichalakaranji, India" -> "ichalakaranji",
    "Kuitun, China" -> "kuitun",
    "Uberaba, Brazil" -> "uberaba",
    "Al-Mubarraz, Saudi Arabia" -> "al-mubarraz",
    "Iligan, Philippines" -> "iligan",
    "Sumy, Ukraine" -> "sumy",
    "Cascavel, Brazil" -> "cascavel",
    "Delmas, Haiti" -> "delmas",
    "Sakarya, Turkey" -> "sakarya",
    "Corpus Christi (TX), United States" -> "corpus christi",
    "Caruaru, Brazil" -> "caruaru",
    "Tambov, Russia" -> "tambov",
    "Tongi, Bangladesh" -> "tongi",
    "Gebze, Turkey" -> "gebze",
    "Nzérékoré, Guinea" -> "nzérékoré",
    "Alappuzha, India" -> "alappuzha",
    "Armenia, Colombia" -> "armenia",
    "Ciudad Victoria, Mexico" -> "ciudad victoria",
    "GABORONE, Botswana" -> "gaborone",
    "Buonmathuot, Viet Nam" -> "buonmathuot",
    "Gelsenkirchen, Germany" -> "gelsenkirchen",
    "Semipalatinsk, Kazakhstan" -> "semipalatinsk",
    "Elazig, Turkey" -> "elazig",
    "Rampur, India" -> "rampur",
    "Novorossiysk, Russia" -> "novorossiysk",
    "Brasov, Romania" -> "brasov",
    "Newark (NJ), United States" -> "newark",
    "Susano, Brazil" -> "susano",
    "Sheikhu Pura, Pakistan" -> "sheikhu pura",
    "Ichihara, Japan" -> "ichihara",
    "Bhilwara, India" -> "bhilwara",
    "Zuwarah, Libya" -> "zuwarah",
    "GEORGETOWN, Guyana" -> "georgetown",
    "Jeju (Cheju), South Korea" -> "jeju",
    "Buffalo (NY), United States" -> "buffalo",
    "Limeira, Brazil" -> "limeira",
    "Roodepoort, South Africa" -> "roodepoort",
    "Ciudad Bolívar, Venezuela" -> "ciudad bolívar",
    "Mandaluyong, Philippines" -> "mandaluyong",
    "Abadan, Iran" -> "abadan",
    "Kuching, Malaysia" -> "kuching",
    "Batman, Turkey" -> "batman",
    "Karlsruhe, Germany" -> "karlsruhe",
    "Zhytomyr, Ukraine" -> "zhytomyr",
    "Belfast, United Kingdom" -> "belfast",
    "Santarém, Brazil" -> "santarém",
    "San Bernardo, Chile" -> "san bernardo",
    "Gyeongju (Kyongju), South Korea" -> "gyeongju",
    "Yoshkar-ola, Russia" -> "yoshkar-ola",
    "St. Paul (MN), United States" -> "st. paul",
    "Valparaíso, Chile" -> "valparaíso",
    "Anchorage (AK), United States" -> "anchorage",
    "Huozhou, China" -> "huozhou",
    "Wollongong, Australia" -> "wollongong",
    "Holguín, Cuba" -> "holguín",
    "Gijón, Spain" -> "gijón",
    "Shimoga, India" -> "shimoga",
    "Kostroma, Russia" -> "kostroma",
    "Islam Shahr (Qasemabad), Iran" -> "islam shahr",
    "Yao, Japan" -> "yao",
    "Cirebon, Indonesia" -> "cirebon",
    "Phyongsong, North Korea" -> "phyongsong",
    "Gunsan (Kunsan), South Korea" -> "gunsan",
    "Jining (Inner Mongolia), China" -> "jining",
    "Mérida, Venezuela" -> "mérida",
    "Komsomolsk-na-Amure, Russia" -> "komsomolsk-na-amure",
    "San Cristóbal, Venezuela" -> "san cristóbal",
    "Pekalongan, Indonesia" -> "pekalongan",
    "Rajarhat Gopalpur, India" -> "rajarhat gopalpur",
    "Taubaté, Brazil" -> "taubaté",
    "Gravatai, Brazil" -> "gravatai",
    "Swansea, United Kingdom" -> "swansea",
    "Nantes, France" -> "nantes",
    "Larkana, Pakistan" -> "larkana",
    "Kelamayi, China" -> "kelamayi",
    "Santa Maria, Brazil" -> "santa maria",
    "Sukabumi, Indonesia" -> "sukabumi",
    "Banda Aceh, Indonesia" -> "banda aceh",
    "Nakhon Si Thammarat, Thailand" -> "nakhon si thammarat",
    "Southend, United Kingdom" -> "southend",
    "Venezia, Italy" -> "venezia",
    "Wulanhaote, China" -> "wulanhaote",
    "Irbid, Jordan" -> "irbid",
    "Hilla, Iraq" -> "hilla",
    "Wiesbaden, Germany" -> "wiesbaden",
    "Wonju, South Korea" -> "wonju",
    "Lexington-Fayette (KY), United States" -> "lexington-fayette",
    "Ibaraki, Japan" -> "ibaraki",
    "Shillong, India" -> "shillong",
    "Tokushima, Japan" -> "tokushima",
    "Angeles, Philippines" -> "angeles",
    "Monclova, Mexico" -> "monclova",
    "Zagazig, Egypt" -> "zagazig",
    "Butuan, Philippines" -> "butuan",
    "Kakogawa, Japan" -> "kakogawa",
    "Ha'il, Saudi Arabia" -> "ha'il",
    "Darbhanga, India" -> "darbhanga",
    "Haifa, Israel" -> "haifa",
    "San Pedro de Macoris, Dominican Republic" -> "san pedro de macoris",
    "Taganrog, Russia" -> "taganrog",
    "Nasariya, Iraq" -> "nasariya",
    "Suncheon, South Korea" -> "suncheon",
    "Sterlitamak, Russia" -> "sterlitamak",
    "Alwar, India" -> "alwar",
    "Petrozavodsk, Russia" -> "petrozavodsk",
    "Cumaná, Venezuela" -> "cumaná",
    "Barueri, Brazil" -> "barueri",
    "Zhangping, China" -> "zhangping",
    "Münster (Westf.), Germany" -> "münster",
    "Chiayi, China" -> "chiayi",
    "Fengzhen, China" -> "fengzhen",
    "Gunpo (Kunpo), South Korea" -> "gunpo",
    "Mönchengladbach, Germany" -> "mönchengladbach",
    "Strasbourg, France" -> "strasbourg",
    "Dasarahalli, India" -> "dasarahalli",
    "Santiago del Estero, Argentina" -> "santiago del estero",
    "Yakutsk, Russia" -> "yakutsk",
    "Chemnitz, Germany" -> "chemnitz",
    "Boksburg, South Africa" -> "boksburg",
    "Hisar, India" -> "hisar",
    "Mito, Japan" -> "mito",
    "Tarlac, Philippines" -> "tarlac",
    "Hailaer, China" -> "hailaer",
    "Maturín, Venezuela" -> "maturín",
    "L.B. Nagar, India" -> "l.b. nagar",
    "Viamao, Brazil" -> "viamao",
    "Yunfu, China" -> "yunfu",
    "Bally, India" -> "bally",
    "Santa Ana, El Salvador" -> "santa ana",
    "Longjing, China" -> "longjing",
    "Kankan, Guinea" -> "kankan",
    "Sao José dos Pinhais, Brazil" -> "sao josé dos pinhais",
    "Blackpool, United Kingdom" -> "blackpool",
    "Cuddapah, India" -> "cuddapah",
    "Faiyûm, Egypt" -> "faiyûm",
    "Bahía Blanca, Argentina" -> "bahía blanca",
    "Verona, Italy" -> "verona",
    "Petrolina, Brazil" -> "petrolina",
    "Dzerzhinsk (Novgorodskaya oblast), Russia" -> "dzerzhinsk",
    "Ratchaburi, Thailand" -> "ratchaburi",
    "Mandaue, Philippines" -> "mandaue",
    "Popayán, Colombia" -> "popayán",
    "Governador Valadares, Brazil" -> "governador valadares",
    "Katsina, Nigeria" -> "katsina",
    "San Miguel, El Salvador" -> "san miguel",
    "Parbhani, India" -> "parbhani",
    "Hiratsuka, Japan" -> "hiratsuka",
    "Gajuwaka, India" -> "gajuwaka",
    "Nôvo Hamburgo, Brazil" -> "nôvo hamburgo",
    "Volta Redonda, Brazil" -> "volta redonda",
    "Corrientes, Argentina" -> "corrientes",
    "Aktobe, Kazakhstan" -> "aktobe",
    "Sivas, Turkey" -> "sivas",
    "Floridablanca, Colombia" -> "floridablanca",
    "Thiès, Senegal" -> "thiès",
    "Yamagata, Japan" -> "yamagata",
    "Dnieprodzerzhynsk, Ukraine" -> "dnieprodzerzhynsk",
    "Ismailia, Egypt" -> "ismailia",
    "Augsburg, Germany" -> "augsburg",
    "Várzea Grande, Brazil" -> "várzea grande",
    "Bengkulu, Indonesia" -> "bengkulu",
    "Halle, Germany" -> "halle",
    "NIS, Serbia" -> "nis",
    "PORT MORESBY, Papua New Guinea" -> "port moresby",
    "Sariwon, North Korea" -> "sariwon",
    "Ubon Ratchathani, Thailand" -> "ubon ratchathani",
    "Bratsk, Russia" -> "bratsk",
    "Bijapur, India" -> "bijapur",
    "Kirovohrad, Ukraine" -> "kirovohrad",
    "Khmelnytskiy (Hmilnyk), Ukraine" -> "khmelnytskiy",
    "Sari, Iran" -> "sari",
    "Dongsheng, China" -> "dongsheng",
    "Chuncheon, South Korea" -> "chuncheon",
    "Gdynia, Poland" -> "gdynia",
    "Baguio, Philippines" -> "baguio",
    "Fukui, Japan" -> "fukui",
    "Junagadh, India" -> "junagadh",
    "Kediri, Indonesia" -> "kediri",
    "Bamenda, Cameroon" -> "bamenda",
    "Gujrat, Pakistan" -> "gujrat",
    "Chishui, China" -> "chishui",
    "Tulufan, China" -> "tulufan",
    "Orsk, Russia" -> "orsk",
    "Kure, Japan" -> "kure",
    "Oshogbo, Nigeria" -> "oshogbo",
    "Baranagar, India" -> "baranagar",
    "Mokpo, South Korea" -> "mokpo",
    "Longquan, China" -> "longquan",
    "Plano (TX), United States" -> "plano",
    "Osh, Kyrgyzstan" -> "osh",
    "Lingwu, China" -> "lingwu",
    "Leiden, Netherlands" -> "leiden",
    "Boa Vista, Brazil" -> "boa vista",
    "Labé, Guinea" -> "labé",
    "Alajuela, Costa Rica" -> "alajuela",
    "St. Petersburg (FL), United States" -> "st. petersburg",
    "Phra Nakhon Si Ayutthaya, Thailand" -> "phra nakhon si ayutthaya",
    "Pucallpa, Peru" -> "pucallpa",
    "Sfax, Tunisia" -> "sfax",
    "Qingtongxia, China" -> "qingtongxia",
    "Tumkur, India" -> "tumkur",
    "Russiefa, Jordan" -> "russiefa",
    "Hospitalet de Llobregat, Spain" -> "hospitalet de llobregat",
    "LJUBLJANA, Slovenia" -> "ljubljana",
    "Sasebo, Japan" -> "sasebo",
    "Batangas, Philippines" -> "batangas",
    "Temuco, Chile" -> "temuco",
    "SUCRE, Bolivia" -> "sucre",
    "Najran (Aba as-Suud), Saudi Arabia" -> "najran",
    "Shakhty, Russia" -> "shakhty",
    "Batna, Algeria" -> "batna",
    "Braunschweig, Germany" -> "braunschweig",
    "Saraburi, Thailand" -> "saraburi",
    "Czestochowa, Poland" -> "czestochowa",
    "Imphal, India" -> "imphal",
    "Mardan, Pakistan" -> "mardan",
    "Embu, Brazil" -> "embu",
    "Messina, Italy" -> "messina",
    "Fuchu, Japan" -> "fuchu",
    "Dordrecht, Netherlands" -> "dordrecht",
    "Praia Grande, Brazil" -> "praia grande",
    "Kasur, Pakistan" -> "kasur",
    "Takasaki, Japan" -> "takasaki",
    "Angarsk, Russia" -> "angarsk",
    "Kashan, Iran" -> "kashan",
    "Hachinohe, Japan" -> "hachinohe",
    "Suqian, China" -> "suqian",
    "Syktivkar, Russia" -> "syktivkar",
    "Graz, Austria" -> "graz",
    "Majnoon, Iraq" -> "majnoon",
    "Rivne, Ukraine" -> "rivne",
    "Borujerd, Iran" -> "borujerd",
    "Aachen, Germany" -> "aachen",
    "Plymouth, United Kingdom" -> "plymouth",
    "Bergen, Norway" -> "bergen",
    "Anantapur, India" -> "anantapur",
    "Aldershot, United Kingdom" -> "aldershot",
    "Coruña(A), Spain" -> "coruña",
    "La Coruña, Spain" -> "la coruña",
    "Jinshi, China" -> "jinshi",
    "PARAMARIBO, Suriname" -> "paramaribo",
    "Thoothukkudi (Tuticorin), India" -> "thoothukkudi",
    "Farrukhabad-cum-Fategarh, India" -> "farrukhabad-cum-fategarh",
    "Huayin, China" -> "huayin",
    "Tegal, Indonesia" -> "tegal",
    "Neyagawa, Japan" -> "neyagawa",
    "Krefeld, Germany" -> "krefeld",
    "Dezful, Iran" -> "dezful",
    "Sincelejo, Colombia" -> "sincelejo",
    "Nizhenvartovsk, Russia" -> "nizhenvartovsk",
    "Narayanganj, Bangladesh" -> "narayanganj",
    "Chiang Rai, Thailand" -> "chiang rai",
    "Rangpur, Bangladesh" -> "rangpur",
    "Gorgan, Iran" -> "gorgan",
    "Juàzeiro do Norte, Brazil" -> "juàzeiro do norte",
    "Jian (Jilin), China" -> "jian",
    "Tiefa, China" -> "tiefa",
    "Jersey City (NJ), United States" -> "jersey city",
    "Glendale (AZ), United States" -> "glendale",
    "Lincoln (NE), United States" -> "lincoln",
    "Habra, India" -> "habra",
    "Akure, Nigeria" -> "akure",
    "Itagüi, Colombia" -> "itagüi",
    "Kasukabe, Japan" -> "kasukabe",
    "Chernivtsy, Ukraine" -> "chernivtsy",
    "Granada, Spain" -> "granada",
    "Sumaré, Brazil" -> "sumaré",
    "Tembisa, South Africa" -> "tembisa",
    "Bukhara, Uzbekistan" -> "bukhara",
    "Dire Dawa, Ethiopia" -> "dire dawa",
    "Magé, Brazil" -> "magé",
    "Derby, United Kingdom" -> "derby",
    "Ramagundam, India" -> "ramagundam",
    "Fuji, Japan" -> "fuji",
    "Qods, Iran" -> "qods",
    "Ipatinga, Brazil" -> "ipatinga",
    "Nagaoka, Japan" -> "nagaoka",
    "Luton/Dunstable, United Kingdom" -> "luton/dunstable",
    "Soka, Japan" -> "soka",
    "Lishi, China" -> "lishi",
    "Karradah Sharqiyah, Iraq" -> "karradah sharqiyah",
    "Jalna, India" -> "jalna",
    "Saskatoon, Canada" -> "saskatoon",
    "BUJUMBURA, Burundi" -> "bujumbura")

}
