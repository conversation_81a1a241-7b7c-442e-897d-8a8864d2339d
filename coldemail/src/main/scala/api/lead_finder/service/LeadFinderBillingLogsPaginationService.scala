package api.lead_finder.service

import api.{ApiVersion, AppConfig}
import api.accounts.TeamId
import api.accounts.models.AccountId
import api.emails.services.EmailSettingResponse
import api.lead_finder.DAO.{LeadFinderBillingLogsDetails, LeadFinderDAO}
import api.lead_finder.controller.LeadFinderBillingLogsAPIParams
import api.prospects.{ExactIdToCompareTime, InferredQueryTimeline, NavigationLinks}
import io.lemonlabs.uri.Url
import play.api.libs.json.{JsError, JsResult, JsSuccess, JsValue, Json, Reads}
import utils.jodatimeutils.JodaTimeUtils
import utils.{Helpers, SRLogger, StringUtilsV2}
import utils.pagination.{GetDataErrorTrait, GetSameTimeLeadFinderBillingLogs, PaginationServiceTrait}

import scala.util.{Failure, Success, Try}

sealed trait LeadFinderBillingLogsPaginationError extends GetDataErrorTrait

case class LeadFinderBillingLogsResult(
                                        billingLogs: Seq[LeadFinderBillingLogsDetails],
                                        links: NavigationLinks
                                      )

case object LeadFinderBillingLogsResult {
  implicit val reads: Reads[LeadFinderBillingLogsResult] = new Reads[LeadFinderBillingLogsResult] {
    override def reads(json: JsValue): JsResult[LeadFinderBillingLogsResult] = Try {
      LeadFinderBillingLogsResult(
        billingLogs = (json \ "email_settings").as[Seq[LeadFinderBillingLogsDetails]],
        links = (json \ "links").as[NavigationLinks]
      )
    } match {
      case Failure(e) => JsError(e.getMessage)
      case Success(billingLogsResult) => JsSuccess(billingLogsResult)
    }
  }
}
object LeadFinderBillingLogsPaginationError {
  case class DBFailure(err: Throwable) extends LeadFinderBillingLogsPaginationError
  case class PaginationError(msg: String) extends LeadFinderBillingLogsPaginationError
}


class LeadFinderBillingLogsPaginationService(
                                              leadFinderDAO: LeadFinderDAO
                                            ) extends PaginationServiceTrait[LeadFinderBillingLogsDetails] {

  override type GetSameTimeDataFromDbParamsType = GetSameTimeLeadFinderBillingLogs

  override def getSameTimeDataFromDb(
                                      data: GetSameTimeLeadFinderBillingLogs,
                                      exactIdToCompareTime: ExactIdToCompareTime
                                    )(using logger: SRLogger): Either[LeadFinderBillingLogsPaginationError, List[LeadFinderBillingLogsDetails]] = {
    leadFinderDAO.getBillingLogs(
      permittedAccountIds = data.permittedAccountIds,
      teamId = data.teamId,
      limit = data.limit,
      queryTimeline = Some(InferredQueryTimeline.Exact(exactIdToCompareTime))
    ) match {
      case Failure(exception) => Left(LeadFinderBillingLogsPaginationError.DBFailure(exception))
      case Success(exactlyAtProspects) => Right(exactlyAtProspects.toList)
    }
  }

  def validateEmailSettingQueryParams(
                                       parsedParams: Map[String, Vector[String]]
                                     ): Try[LeadFinderBillingLogsAPIParams] = {
    for {
      (is_first: Boolean, range: InferredQueryTimeline.Range) <- Helpers.getTimeLineRange(parsedParams = parsedParams)

    } yield {
      LeadFinderBillingLogsAPIParams(
        range,
        is_first
      )
    }
  }

  def validateAndExtractBillingLogsResult(
                                           teamId: TeamId,
                                           permittedAccountIds: Seq[AccountId],

                                           filters: LeadFinderBillingLogsAPIParams,
                                           params: String
                                          )(using Logger: SRLogger): Either[BillingLogsError, LeadFinderBillingLogsResponse] = {
    findBillingLogsPage(
      teamId = teamId,
      permittedAccountIds = permittedAccountIds,
      filters = filters,
    ) match {
      case Left(err) =>
        err match {
          case LeadFinderBillingLogsPaginationError.DBFailure(e: Throwable) =>
            Logger.error("This should not happen. DB failure in emailSetting listing api", err = e)
            Left(BillingLogsError.ServerError(e))

          case LeadFinderBillingLogsPaginationError.PaginationError(msg: String) =>
            Logger.error(msg)
            Left(BillingLogsError.ServerError(new Throwable(msg)))
        }

      case Right(leadFinderBillingLogsResult: LeadFinderBillingLogsResult) =>
        val uri = Url.parse(params)
        val page_data = leadFinderBillingLogsResult.links

        val nav_links = Helpers.getNavigationLinksForListingApi(uri = uri, page_data = page_data)

        Right(
          LeadFinderBillingLogsResponse(
            message = "Billing Logs found",
            data = Json.obj(
              "billing_logs" -> Json.toJson(leadFinderBillingLogsResult.billingLogs),
              "links" -> nav_links
            )
          )
        )
    }

  }

  def findBillingLogsPage(
                            teamId: TeamId,
                            permittedAccountIds: Seq[AccountId],
                            filters: LeadFinderBillingLogsAPIParams,
                          )(using Logger: SRLogger): Either[LeadFinderBillingLogsPaginationError, LeadFinderBillingLogsResult] = {


    val limit = AppConfig.leadFinderBillingLogsPerPage

    leadFinderDAO.getBillingLogs(
      teamId = teamId,
      permittedAccountIds = permittedAccountIds,
      limit = limit + 1,
      queryTimeline = Some(filters.range)
    ) match {
      case Failure(exception) => Left(LeadFinderBillingLogsPaginationError.DBFailure(exception))
      case Success(leadFinderBillingLogsDetails: List[LeadFinderBillingLogsDetails]) =>

        getFinalPaginationResponse(
          isFirst = Some(filters.is_first),
          timeline = filters.range,
          sortedData = leadFinderBillingLogsDetails.toList,
          limit = limit,
          dataToGetSameTimeRecords = GetSameTimeLeadFinderBillingLogs(
            permittedAccountIds = permittedAccountIds,
            teamId = teamId,
            limit = limit,
            Logger = Logger
          )
        ) match {
          case Left(err) => Left(LeadFinderBillingLogsPaginationError.PaginationError(msg = "Error in LeadFinder pagination"))

          case Right(paginationResObject) =>
            Right(
              LeadFinderBillingLogsResult(
                billingLogs = paginationResObject.data.toSeq,
                links = paginationResObject.links
              )
            )
        }
    }
  }
}
