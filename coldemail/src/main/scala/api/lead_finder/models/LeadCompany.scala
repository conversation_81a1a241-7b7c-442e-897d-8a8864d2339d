package api.lead_finder.models

import play.api.libs.json._


case class LeadCompany (
                         company_type: Option[LeadCompanyType],
                         company_name: Option[String],
                         company_location: Option[String],
                         industry: LeadIndustry
                       )

object LeadCompany {
  implicit val reads: Reads[LeadCompany] = Json.reads[LeadCompany]
  implicit val writes: OWrites[LeadCompany] = Json.writes[LeadCompany]
}


case class LeadIndustry(industry: String) extends AnyVal {

  override def toString: String = industry

}

object LeadIndustry{

  implicit val reads: Reads[LeadIndustry] = new Reads[LeadIndustry] {
    override def reads(ev: JsValue): JsResult[LeadIndustry] = {
      ev match {
        case JsString(industry) => JsSuccess(LeadIndustry(industry = industry))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[LeadIndustry] = new Writes[LeadIndustry] {
    override def writes(industry: LeadIndustry): JsValue = JsString(industry.industry)
  }
}

case class LeadJobTitle(job_title: String) extends AnyVal {
  override def toString: String = job_title
}

object LeadJobTitle{

  implicit val reads: Reads[LeadJobTitle] = new Reads[LeadJobTitle] {
    override def reads(ev: JsValue): JsResult[LeadJobTitle] = {
      ev match {
        case JsString(job_title) => JsSuccess(LeadJobTitle(job_title = job_title))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[LeadJobTitle] = new Writes[LeadJobTitle] {
    override def writes(job_title: LeadJobTitle): JsValue = JsString(job_title.job_title)
  }
}


case class LeadCompanySize(company_size: String) extends AnyVal {

  override def toString: String = company_size

}

object LeadCompanySize{

  implicit val reads: Reads[LeadCompanySize] = new Reads[LeadCompanySize] {
    override def reads(ev: JsValue): JsResult[LeadCompanySize] = {
      ev match {
        case JsString(company_size) => JsSuccess(LeadCompanySize(company_size = company_size))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[LeadCompanySize] = new Writes[LeadCompanySize] {
    override def writes(company_size: LeadCompanySize): JsValue = JsString(company_size.company_size)
  }
}


case class LeadCityName(city_name: String) extends AnyVal {

  override def toString: String = city_name

}

object LeadCityName{

  implicit val reads: Reads[LeadCityName] = new Reads[LeadCityName] {
    override def reads(ev: JsValue): JsResult[LeadCityName] = {
      ev match {
        case JsString(city_name) => JsSuccess(LeadCityName(city_name = city_name))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[LeadCityName] = new Writes[LeadCityName] {
    override def writes(city_name: LeadCityName): JsValue = JsString(city_name.city_name)
  }
}

case class LeadCompanyType(company_type: String) extends AnyVal {

  override def toString: String = company_type

}

object LeadCompanyType{

  implicit val reads: Reads[LeadCompanyType] = new Reads[LeadCompanyType] {
    override def reads(ev: JsValue): JsResult[LeadCompanyType] = {
      ev match {
        case JsString(company_type) => JsSuccess(LeadCompanyType(company_type = company_type))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[LeadCompanyType] = new Writes[LeadCompanyType] {
    override def writes(company_type: LeadCompanyType): JsValue = JsString(company_type.company_type)
  }
}


case class LeadFirstName(first_name: String) extends AnyVal {

  override def toString: String = first_name

}

object LeadFirstName{

  implicit val reads: Reads[LeadFirstName] = new Reads[LeadFirstName] {
    override def reads(ev: JsValue): JsResult[LeadFirstName] = {
      ev match {
        case JsString(first_name) => JsSuccess(LeadFirstName(first_name = first_name))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[LeadFirstName] = new Writes[LeadFirstName] {
    override def writes(first_name: LeadFirstName): JsValue = JsString(first_name.first_name)
  }
}


case class LeadLastName(last_name: String) extends AnyVal {

  override def toString: String = last_name

}

object LeadLastName{

  implicit val reads: Reads[LeadLastName] = new Reads[LeadLastName] {
    override def reads(ev: JsValue): JsResult[LeadLastName] = {
      ev match {
        case JsString(last_name) => JsSuccess(LeadLastName(last_name = last_name))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[LeadLastName] = new Writes[LeadLastName] {
    override def writes(last_name: LeadLastName): JsValue = JsString(last_name.last_name)
  }
}
