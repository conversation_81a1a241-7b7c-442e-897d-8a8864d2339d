package api.lead_finder.models

import play.api.libs.json.{<PERSON>s<PERSON><PERSON>r, Js<PERSON>umber, JsResult, JsSuccess, JsValue, Reads, Writes}

case class LeadId(id: Long) extends AnyVal {
  override def toString: String = id.toString
}

object LeadId {
  implicit val writes: Writes[LeadId] = new Writes[LeadId] {
    override def writes(o: LeadId): JsValue = JsNumber(o.id)
  }

  implicit val reads: Reads[LeadId] = new Reads[LeadId] {
    override def reads(json: JsValue): JsResult[LeadId] = {
      json match {
        case JsNumber(id) => JsSuccess(LeadId(id = id.toLong))
        case randomValue => JsError(s"expected Integer, got some random value - $randomValue")
      }
    }
  }
}
