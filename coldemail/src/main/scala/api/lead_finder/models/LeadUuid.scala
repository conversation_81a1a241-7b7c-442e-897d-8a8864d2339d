package api.lead_finder.models

import play.api.libs.json._

case class LeadUuid(lead_uuid: String) extends AnyVal {

  override def toString: String = lead_uuid

}

object LeadUuid{

  implicit val reads: Reads[LeadUuid] = new Reads[LeadUuid] {
    override def reads(ev: JsValue): JsResult[LeadUuid] = {
      ev match {
        case JsString(phone_number_uuid) => JsSuccess(LeadUuid(lead_uuid = phone_number_uuid))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[LeadUuid] = new Writes[LeadUuid] {
    override def writes(uuid: LeadUuid): JsValue = JsString(uuid.lead_uuid)
  }
}