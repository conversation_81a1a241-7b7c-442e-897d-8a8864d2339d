package api.lead_finder.models

import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON>ult, JsNumber, JsSuccess, JsValue, Reads, Writes}

case class LeadValidationBatchReqId(id: Long) extends AnyVal {

  override def toString: String = id.toString

}

object LeadValidationBatchReqId {

  implicit val reads: Reads[LeadValidationBatchReqId] = new Reads[LeadValidationBatchReqId] {

    override def reads(ev: JsValue): JsResult[LeadValidationBatchReqId] = {

      ev match {

        case JsNumber(id) => JsSuccess(LeadValidationBatchReqId(id = id.toLong))

        case randomValue => JsError(s"expected number, got some random value - $randomValue")

      }

    }

  }

  implicit val writes: Writes[LeadValidationBatchReqId] = new Writes[LeadValidationBatchReqId] {

    override def writes(o: LeadValidationBatchReqId): JsValue = JsNumber(o.id)

  }

}
