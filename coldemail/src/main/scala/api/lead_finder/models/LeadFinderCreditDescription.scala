package api.lead_finder.models

import play.api.libs.json.{ JsVal<PERSON>, <PERSON><PERSON>, Writes}

sealed trait LeadFinderCreditDescription {
  def description: String

  // Abstract method to update the description
  def updateDescription(newDescription: String): LeadFinderCreditDescription
}

object LeadFinderCreditDescription {

  implicit val writes: Writes[LeadFinderCreditDescription] = new Writes[LeadFinderCreditDescription] {
    override def writes(o: LeadFinderCreditDescription): JsValue = {
      o match {
        case data: MagicColumnCreditDescription => Json.toJson(data)
        case data: AiContentGenerationCreditDescription => Json.toJson(data)
      }
    }
  }

  case class MagicColumnCreditDescription(
                                           prospectId: Long,
                                           columnId: Long,
                                           description: String
                                         ) extends LeadFinderCreditDescription {
    // Implement the updateDescription method
    override def updateDescription(newDescription: String): MagicColumnCreditDescription =
      copy(description = newDescription)
  }

  object MagicColumnCreditDescription {
    implicit val MagicColumnCreditDescriptionWrites: Writes[MagicColumnCreditDescription] =
      Json.writes[MagicColumnCreditDescription]
  }

  case class AiContentGenerationCreditDescription(
                                                   prospectId: Long,
                                                   campaignId: Long,
                                                   stepId: Long,
                                                   description: String
                                                 ) extends LeadFinderCreditDescription {
    override def updateDescription(newDescription: String): AiContentGenerationCreditDescription =
      copy(description = newDescription)
  }

  object AiContentGenerationCreditDescription {
    implicit val AiContentGenerationCreditDescriptionWrites: Writes[AiContentGenerationCreditDescription] =
      Json.writes[AiContentGenerationCreditDescription]
  }
}





sealed trait SmartreachCreditType {
  def toString: String
}

object SmartreachCreditType {
  case object MagicColumn extends SmartreachCreditType {
    override def toString: String = "MagicColumn"
  }

  case object Leadfinder extends SmartreachCreditType {
    override def toString: String = "LeadFinder"
  }

  case object AiContentGeneration extends SmartreachCreditType {
    override def toString: String = "AiContentGeneration"
  }

  //case object ProspectDaddyCredit extends SmartreachCreditType {
  //  override def toString: String = "ProspectDaddy"
  //}
}

