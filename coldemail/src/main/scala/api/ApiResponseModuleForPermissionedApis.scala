package api

import api.accounts.PermissionRequest
import api.sr_audit_logs.services.RequestLogService
import play.api.mvc.Result
import utils.SRLogger

import scala.concurrent.ExecutionContext

class ApiResponseModuleForPermissionedApis(
  requestLogService: RequestLogService,
  override protected val Logger: SRLogger,
  implicit val ec: ExecutionContext
) extends APIResponseModule[PermissionRequest[?]] {

  private var permissionRequest: Option[PermissionRequest[?]] = None

  final override protected def shouldLogRequestToDb: Boolean = permissionRequest.isDefined


  final override def enableLogRequestToDb(request: PermissionRequest[?]): APIResponseModule[PermissionRequest[?]] = {
    permissionRequest = Some(request)

    this
  }

  final override protected def logRequestToDb(httpResponse: Result): Boolean = {

    permissionRequest match {

      case None =>

        Logger.fatal(s"CRITICAL: permissionRequest: None :: httpResponse: ${httpResponse}")
        false

      case Some(permissionReq) =>

        Logger.info(s"logRequestToDb: ${httpResponse.header.status} :: request: ${permissionRequest.get.path}")

        requestLogService.createRequestLog(
          request = permissionReq,
          httpResponse = httpResponse
        ) match {
          case Left(err) =>

            Logger.fatal(s"CRITICAL: createRequestLog_error: message: ${err.toString} :: ${httpResponse.header.status} :: request: ${permissionReq.path}")
            false

          case Right(_) =>

            Logger.info(s"createRequestLog success: ${permissionReq.auditRequestLogId} ")
            true
        }
    }
  }


}
