package api.billing.v2.services

import api.AppConfig
import api.accounts.{AccountService, TeamId}
import api.accounts.dao.OrganizationBillingDAO
import api.accounts.models.FindCampaignIdsBy
import api.call.service.CallService
import api.campaigns.services.CampaignService
import io.smartreach.esp.api.emails.EmailSettingId
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.helpers.LogHelpers

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class OrgDeactivateService(
                            campaignService: CampaignService,
                            accountService: AccountService,
                            organizationBillingDAO: OrganizationBillingDAO,
                            callService: CallService
                          ) {


  def hourlyCronService()(using logger: SRLogger, ec: ExecutionContext, wc: WSClient) = {


    for {


      // Unit
      reset_billing_monthly_cycle <- Try(organizationBillingDAO.resetBillingMonthlyCycle(updateSpecificOrg = None) match {

        case Failure(e) =>
          logger.error(s"[HourlyCronService] [FATAL] error Account.resetBillingMonthlyCycle ${LogHelpers.getStackTraceAsString(e)}")

        case Success(orgs) =>
          logger.info(s"[HourlyCronService] success update Account.resetBillingMonthlyCycle totalUpdated: ${orgs.map(_.id)}")

      })

      // Unit
      deactivate_trial_expired_orgs <- Try(deactivateTrialExpiredOrgs)

      release_phone_numbers_for_deactivated_orgs <- callService.releasePhoneNumberOnOrgDeactivation()


    }yield{

      true

    }

  }

  // Return Type: Unit


  // Return Type: Unit
  def deactivateTrialExpiredOrgs(using logger : SRLogger)= {

    accountService.deactivateTrialExpiredOrgs() match {

      case Failure(e) =>
        logger.error(s"[HourlyCronService] [FATAL] error Account.deactivateTrialExpiredOrgs ${LogHelpers.getStackTraceAsString(e)}")

      case Success(orgs) =>

        logger.info(s"[HourlyCronService] success update Account.deactivateTrialExpiredOrgs WILL PAUSE CAMPAIGNS NEXT totalUpdated: ${orgs.map(_.id)}")

        for (o <- orgs) {
          campaignService.stopAllCampaigns(
            by = FindCampaignIdsBy.Org(orgId = o.id)
          )(
            logger = logger
          ) match {
            case Failure(e) =>
              logger.error(s"[HourlyCronService] [FATAL] error Account.deactivateTrialExpiredOrgs stopAllCampaigns (org: $o) ${LogHelpers.getStackTraceAsString(e)}")

            case Success(orgs) =>
              logger.info(s"[HourlyCronService] success update Account.deactivateTrialExpiredOrgs stopAllCampaigns (org: $o)")

          }
        }

        logger.info(s"[HourlyCronService] success update Account.deactivateTrialExpiredOrgs DONE totalUpdated: ${orgs.map(_.id)}")

    }
  }


  // Retrun Type: Any





}
