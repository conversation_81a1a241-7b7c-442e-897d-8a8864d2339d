package api.billing

import io.sr.billing_common.models.PlanID
import org.joda.time.DateTime
import play.api.libs.json.{Js<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Writes}
import scalikejdbc.WrappedResultSet
import scalikejdbc.jodatime.JodaWrappedResultSet._
import utils.payment.models.{PGSubscriptionCurrency, PGSubscriptionInterval}


case class SRPlan(
  id: Int,
  sr_plan_name: String,
  sr_plan_id: PlanID,
  is_business: Boolean,
  plan_interval: PGSubscriptionInterval.Value,
  base_licences: Int,

  stripe_base_plan_id: String,
  stripe_additional_plan_id: Option[String],
  fastspring_base_plan_id: Option[String],
  fastspring_additional_plan_id: Option[String],
  currency: PGSubscriptionCurrency.Value,

  // price in cents / paisa
  base_price: Int,
  additional_licence_price: Int,

  active: Boolean,
  created_at: DateTime
)

object SRPlan {

  // given format = Json.format[SRPlan]

  given writes: Writes[SRPlan] = new Writes[SRPlan] {
    def writes(i: SRPlan): JsValue = {

      Json.obj(
        "id" -> i.id,
        "sr_plan_name" -> i.sr_plan_name,
        "sr_plan_id" -> i.sr_plan_id,
        "is_business" -> i.is_business,
        "plan_interval" -> i.plan_interval,
        "base_licences" -> i.base_licences,
        "currency" -> i.currency,
        "base_price" -> i.base_price,
        "additional_licence_price" -> i.additional_licence_price,
        "active" -> i.active
      )
    }
  }

  def fromDb(rs: WrappedResultSet): SRPlan = {
    SRPlan(
      id = rs.int("id"),
      sr_plan_name = rs.string("sr_plan_name"),
      sr_plan_id = PlanID.withName(rs.string("sr_plan_id")).get,
      is_business = rs.boolean("is_business"),
      plan_interval = PGSubscriptionInterval.withName(rs.string("plan_interval")),
      base_licences = rs.int("base_licences"),

      stripe_base_plan_id = rs.string("stripe_base_plan_id"),
      stripe_additional_plan_id = rs.stringOpt("stripe_additional_plan_id"),
      fastspring_base_plan_id = rs.stringOpt("fastspring_base_plan_id"),
      fastspring_additional_plan_id = rs.stringOpt("fastspring_additional_plan_id"),
      currency = PGSubscriptionCurrency.withName(rs.string("currency")),

      // price in cents / paisa
      base_price = rs.int("base_price"),
      additional_licence_price = rs.int("additional_licence_price"),

      active = rs.boolean("active"),
      created_at = rs.jodaDateTime("created_at")
    )
  }

  /*
  def filterPlans(
    plans: Seq[SRPlan]
  )
    (currency: PGSubscriptionCurrency.Value)
    (interval: PGSubscriptionInterval.Value)
    (isBusiness: Boolean): Seq[SRPlan] = {

    plans
      .filter(p => {
        p.currency == currency &&
          p.plan_interval == interval &&
          p.is_business == isBusiness
      })
  }

  def filterMonthlyPlans(plans: Seq[SRPlan]) = {
    plans.filter(p => p.plan_interval == FastSpringPlanCycle.MONTHLY)
  }

  def filterAnnualPlans(plans: Seq[SRPlan]) = {
    plans.filter(p => p.plan_interval == FastSpringPlanCycle.ANNUAL)
  }
  */

}
