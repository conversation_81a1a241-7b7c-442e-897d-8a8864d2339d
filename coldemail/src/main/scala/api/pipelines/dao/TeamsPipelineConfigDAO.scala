package api.pipelines.dao

import api.accounts.TeamId
import api.pipelines.models.ViewStatusType
import org.joda.time.DateTime
import play.api.libs.json.{Json, Reads, Writes}
import play.api.libs.json.JodaReads._
import play.api.libs.json.JodaWrites._
import scalikejdbc.{DB, DBSession, scalikejdbcSQLInterpolationImplicitDef}
import scalikejdbc.jodatime.JodaWrappedResultSet._

import scala.util.Try

case class TeamPipelineConfig(
  team_id: TeamId,
  view_status_type: ViewStatusType,
  updated_at: DateTime,
  created_at: DateTime
)

object TeamPipelineConfig {

  implicit val writes: Writes[TeamPipelineConfig] = Json.writes[TeamPipelineConfig]

}

case class UpdateTeamPipelineConfigData(
  view_status_type: ViewStatusType,
)

object UpdateTeamPipelineConfigData {

  implicit val reads: Reads[UpdateTeamPipelineConfigData] = Json.reads[UpdateTeamPipelineConfigData]

}

class TeamsPipelineConfigDAO {

  def getTeamsPipelineConfig(teamId: TeamId): Try[Option[TeamPipelineConfig]] = Try {

    DB.readOnly { implicit session =>

      sql"""
          SELECT
            *
          FROM
            teams_pipeline_config
          WHERE
            team_id = ${teamId.id}
         """
        .map { rs =>

          TeamPipelineConfig(
            team_id = TeamId(id = rs.long("team_id")),
            view_status_type = ViewStatusType.fromKey(key = rs.string("view_status_type")).get,
            created_at = rs.jodaDateTime("created_at"),
            updated_at = rs.jodaDateTime("updated_at")
          )

        }
        .single
        .apply()

    }

  }

  def createTeamsPipelineConfig(
    teamId: TeamId,
  )(
    implicit session: DBSession
  ): Try[Option[TeamPipelineConfig]] = Try {

    sql"""
        INSERT INTO teams_pipeline_config
          (
            team_id,
            view_status_type,
            updated_at
          )
        VALUES
          (
            ${teamId.id},
            ${ViewStatusType.ActiveOnly.toString},
            now()
          )
        RETURNING *;
       """
      .map { rs =>

        TeamPipelineConfig(
          team_id = TeamId(id = rs.long("team_id")),
          view_status_type = ViewStatusType.fromKey(key = rs.string("view_status_type")).get,
          created_at = rs.jodaDateTime("created_at"),
          updated_at = rs.jodaDateTime("updated_at")
        )

      }
      .single
      .apply()

  }

  def createTeamsPipelineConfigWithAutoTx(
    teamId: TeamId,
  ): Try[Option[TeamPipelineConfig]] = {

    DB.autoCommit { implicit session =>

      createTeamsPipelineConfig(teamId = teamId)

    }

  }

  def updateTeamsPipelineConfig(
    teamId: TeamId,
    updateTeamPipelineConfigData: UpdateTeamPipelineConfigData
  ): Try[Option[TeamPipelineConfig]] = Try {

    DB.autoCommit { implicit session =>

      sql"""
          UPDATE teams_pipeline_config
          SET
            view_status_type = ${updateTeamPipelineConfigData.view_status_type.toString},
            updated_at = now()
          WHERE
            team_id = ${teamId.id}
          RETURNING *;
         """
        .map { rs =>

          TeamPipelineConfig(
            team_id = TeamId(id = rs.long("team_id")),
            view_status_type = ViewStatusType.fromKey(key = rs.string("view_status_type")).get,
            created_at = rs.jodaDateTime("created_at"),
            updated_at = rs.jodaDateTime("updated_at")
          )

        }
        .single
        .apply()

    }

  }

}
