package api.pipelines.dao

import api.accounts.TeamId
import api.pipelines.models.{OpportunityStatus, OpportunityStatusPosRank, OpportunityStatusUUID, PipelineInternalId, PipelineUUID, StatusType}
import org.joda.time.DateTime
import play.api.libs.json.{<PERSON>son, OWrites, Reads}
import scalikejdbc.{DB, DBSession, SQLSyntax, scalikejdbcSQLInterpolationImplicitDef}

import scala.util.Try

case class OpportunityStatusDeleteData(
  to_opportunity_status_id: Option[OpportunityStatusUUID]
)

object OpportunityStatusDeleteData {
  implicit val reads: Reads[OpportunityStatusDeleteData] = Json.reads[OpportunityStatusDeleteData]
}

case class OpportunityStatusCreateData(
  opportunity_status_name: String,
  opportunity_status_pos_rank: OpportunityStatusPosRank,
  opportunity_status_type: StatusType,
  opportunity_status_color: String,
  // pipeline_internal_id: PipelineInternalId
)

object OpportunityStatusCreateData {
  implicit val reads: Reads[OpportunityStatusCreateData] = Json.reads[OpportunityStatusCreateData]
}

case class OpportunityStatusUpdateData(
  opportunity_status_name: String,
  // opportunity_status_pos_rank: OpportunityStatusPosRank,
  // opportunity_status_type: StatusType,
  opportunity_status_color: String,
  // pipeline_internal_id: PipelineInternalId
)

object OpportunityStatusUpdateData {
  implicit val reads: Reads[OpportunityStatusUpdateData] = Json.reads[OpportunityStatusUpdateData]
}

case class ReorderOpportunityStatusData(
  to_be_next_status_rank: OpportunityStatusPosRank,
  to_be_prev_status_rank: OpportunityStatusPosRank
)

object ReorderOpportunityStatusData {
  implicit val reads: Reads[ReorderOpportunityStatusData] = Json.reads[ReorderOpportunityStatusData]
}

// TODO: Maybe we should just return a Map
case class AnnualizedOpportunityStatusValue(
  opportunity_status_id: OpportunityStatusUUID,
  annualized_value: Int
)

object AnnualizedOpportunityStatusValue {
  implicit val writes: OWrites[AnnualizedOpportunityStatusValue] = Json.writes[AnnualizedOpportunityStatusValue]
}

class OpportunityStatusDAO {

  def getStatuses(
    teamId: TeamId,
    pipelineUUID: PipelineUUID
  ): Try[List[OpportunityStatus]] = Try {

    DB readOnly { implicit session =>

      sql"""
          SELECT
            s.*,
            p.uuid AS pipeline_uuid,
            count(o.id) AS opportunity_count
          FROM
            opportunity_statuses AS s
            JOIN pipelines AS p ON (s.pipeline_id = p.id AND s.team_id = p.team_id)
            LEFT JOIN opportunities AS o ON (o.status_id = s.id AND s.team_id = o.team_id)
          WHERE
            s.team_id = ${teamId.id}
            AND p.uuid = ${pipelineUUID.uuid}
          GROUP BY
            s.id,
            p.uuid
          ORDER BY
            s.status_pos_rank
         """
        .map { rs =>
          OpportunityStatus.fromDB(rs = rs)
        }
        .list
        .apply()
    }
  }

  def getOpportunityStatus(
    teamId: TeamId,
    opportunityStatusUUID: OpportunityStatusUUID
  )(
    implicit session: DBSession
  ): Try[Option[OpportunityStatus]] = Try {

    sql"""
        SELECT
          s.*,
          p.uuid AS pipeline_uuid,
          count(o.id) AS opportunity_count
        FROM
          opportunity_statuses AS s
          JOIN pipelines AS p ON (s.pipeline_id = p.id AND s.team_id = p.team_id)
          LEFT JOIN opportunities AS o ON (o.status_id = s.id AND s.team_id = o.team_id)
        WHERE
          s.team_id = ${teamId.id}
          AND s.uuid = ${opportunityStatusUUID.uuid}
        GROUP BY
          s.id,
          p.uuid
        LIMIT 1
       """
      .map { rs =>
        OpportunityStatus.fromDB(rs = rs)
      }
      .single
      .apply()
  }

  def getOpportunityStatusWithAutoTx(
    teamId: TeamId,
    opportunityStatusUUID: OpportunityStatusUUID
  ): Try[Option[OpportunityStatus]] = {

    DB readOnly { implicit session =>

      getOpportunityStatus(
        teamId = teamId,
        opportunityStatusUUID = opportunityStatusUUID
      )

    }
  }

  def getOpportunityStatusCountByStatusType(
    teamId: TeamId,
    pipelineUUID: PipelineUUID,
    statusType: StatusType
  ): Try[Option[Int]] = Try {

    DB readOnly { implicit session =>

      sql"""
          SELECT
	          count(s.id) AS status_type_count
          FROM
            opportunity_statuses AS s
            JOIN pipelines AS p ON (s.pipeline_id = p.id AND s.team_id = p.team_id)
          WHERE
            s.team_id = ${teamId.id}
            AND s.status_type = ${statusType.toString}
            AND p.uuid = ${pipelineUUID.uuid}
         """
        .map { rs =>

          rs.int("status_type_count")

        }
        .single
        .apply()
    }
  }


  def createStatus(
    teamId: TeamId,
    pipelineInternalId: PipelineInternalId,
    statusUUID: OpportunityStatusUUID,
    statusData: OpportunityStatusCreateData
  ): Try[Option[OpportunityStatusUUID]] = Try {

    DB autoCommit { implicit session =>

      sql"""
          INSERT INTO opportunity_statuses
            (
              uuid,
              status_name,
              pipeline_id,
              status_pos_rank,
              status_type,
              status_color,
              annualized_value,
              team_id,
              updated_at
            )
          VALUES
            (
              ${statusUUID.uuid},
              ${statusData.opportunity_status_name.toLowerCase},
              ${pipelineInternalId.id},
              ${statusData.opportunity_status_pos_rank.rank},
              ${statusData.opportunity_status_type.toString},
              ${statusData.opportunity_status_color},
              0,
              ${teamId.id},
              now()
            )
          RETURNING uuid;
         """
        .map { rs =>
          OpportunityStatusUUID(uuid = rs.string("uuid"))
        }
        .single
        .apply()
    }
  }

  def createOpportunityStatuses(
    teamId: TeamId,
    pipelineInternalId: PipelineInternalId,
    statusUUIDToData: Map[OpportunityStatusUUID, OpportunityStatusCreateData]
  )(
    implicit session: DBSession
  ): Try[Seq[OpportunityStatusUUID]] = Try {

    if (statusUUIDToData.isEmpty) {
      Seq()
    } else {

      var parameters = List[Any]()

      val valuePlaceholder: SQLSyntax = statusUUIDToData.map {

        case (statusUUID: OpportunityStatusUUID, data: OpportunityStatusCreateData) =>

          parameters = parameters ::: List(
            statusUUID.uuid,
            data.opportunity_status_name,
            pipelineInternalId.id,
            data.opportunity_status_pos_rank.rank,
            data.opportunity_status_type.toString,
            data.opportunity_status_color,
            0,
            teamId.id,
            DateTime.now()
          )

          sqls"""
              (
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?
              )
            """

      }
        .reduce((vp1, vp2) => sqls" $vp1, $vp2 ")

      sql"""
          INSERT INTO opportunity_statuses
            (
              uuid,
              status_name,
              pipeline_id,
              status_pos_rank,
              status_type,
              status_color,
              annualized_value,
              team_id,
              updated_at
            )
          VALUES
            $valuePlaceholder
          RETURNING uuid;
      """
        .bind(parameters*)
        .map { rs =>
          OpportunityStatusUUID(uuid = rs.string("uuid"))
        }
        .list
        .apply()

    }
  }


  def updateStatus(
    teamId: TeamId,
    pipelineInternalId: PipelineInternalId,
    statusUUID: OpportunityStatusUUID,
    statusData: OpportunityStatusUpdateData
  ): Try[Option[OpportunityStatusUUID]] = Try {

    DB autoCommit { implicit session =>

      sql"""
          UPDATE
            opportunity_statuses
          SET
            status_name = ${statusData.opportunity_status_name.toLowerCase},
            status_color = ${statusData.opportunity_status_color},
            updated_at = now()
          WHERE
            team_id = ${teamId.id}
            AND pipeline_id = ${pipelineInternalId.id}
            AND uuid = ${statusUUID.uuid}
          RETURNING uuid;
         """
        .map { rs =>
          OpportunityStatusUUID(uuid = rs.string("uuid"))
        }
        .single
        .apply()
    }
  }

  def updateAnnualizedValue(
    teamId: TeamId,
    statusUUID: OpportunityStatusUUID,
    addValueInUSDCents: Long,
    deductValueInUSDCents: Long,
  )(
    implicit session: DBSession
  ): Try[Option[OpportunityStatusUUID]] = Try {

    sql"""
        UPDATE
          opportunity_statuses
        SET
          annualized_value = (annualized_value - $deductValueInUSDCents) + $addValueInUSDCents,
          updated_at = now()
        WHERE
          team_id = ${teamId.id}
          AND uuid = ${statusUUID.uuid}
        RETURNING uuid;
       """
      .map { rs =>
        OpportunityStatusUUID(uuid = rs.string("uuid"))
      }
      .single
      .apply()
  }


  def deleteStatus(
    teamId: TeamId,
    pipelineUUID: PipelineUUID, // TODO: This is not used.
    statusUUID: OpportunityStatusUUID
  )(
    implicit session: DBSession
  ): Try[Int] = Try {

    sql"""
        DELETE FROM opportunity_statuses
        WHERE
          team_id = ${teamId.id}
          AND uuid = ${statusUUID.uuid};
       """
      .update
      .apply()
  }

  def reorderStatus(
    teamId: TeamId,
    pipelineUUID: PipelineUUID, // TODO: This is not used.
    statusUUID: OpportunityStatusUUID,
    reorderStatusData: ReorderOpportunityStatusData
  ): Try[Option[OpportunityStatusUUID]] = Try {

    val new_rank: Double = (
      reorderStatusData.to_be_next_status_rank.rank + reorderStatusData.to_be_prev_status_rank.rank
      ) / 2

    DB autoCommit { implicit session =>

      sql"""
            UPDATE
              opportunity_statuses
            SET
              status_pos_rank = $new_rank,
              updated_at = now()
            WHERE
              team_id = ${teamId.id}
              AND uuid = ${statusUUID.uuid}
            RETURNING uuid;
         """
        .map { rs =>
          OpportunityStatusUUID(uuid = rs.string("uuid"))
        }
        .single
        .apply()
    }
  }

}
