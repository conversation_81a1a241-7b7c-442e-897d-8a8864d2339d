package api.pipelines.utils

import api.ApiResponseModuleForPermissionedApis
import api.accounts.{Account, PermissionRequest, TeamId, TeamMember}
import api.pipelines.models.{Opportunity, OpportunityUUID, Pipeline, PipelineUUID}
import api.pipelines.services.{OpportunityService, PipelineService}
import api.prospects.dao_service.ProspectDAOService
import play.api.mvc.{ActionRefiner, Result, WrappedRequest}
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

case class OpportunityRequest[A](
  opportunity: Opportunity,
  actingTeamAccount: TeamMember,
  loggedinAccount: Account,
  isApiCall: Boolean,
  permittedAccountIds: Seq[Long],
  Logger: SRLogger,
  Response: ApiResponseModuleForPermissionedApis,
  request: PermissionRequest[A]
) extends WrappedRequest[A](request)


case class PipelineRequest[A](
  pipelineOpt: Option[Pipeline],
  actingTeamAccount: Team<PERSON>ember,
  loggedinAccount: Account,
  isApiCall: <PERSON><PERSON><PERSON>,
  permittedAccountIds: Seq[Long],
  Logger: SRLogger,
  Response: ApiResponseModuleForPermissionedApis,
  request: PermissionRequest[A]
) extends WrappedRequest[A](request)


class OpportunityUtils(
  opportunityService: OpportunityService,
  prospectDAOService: ProspectDAOService,
  pipelineService: PipelineService
) {

  private final def checkHasOpportunity[A](
    Res: ApiResponseModuleForPermissionedApis,
    request: PermissionRequest[A],
    opportunityIdOpt: Option[OpportunityUUID],
    teamMember: TeamMember
  ): Either[Result, OpportunityRequest[A]] = {

    val logger: SRLogger = request.Logger

    val teamId = TeamId(id = teamMember.team_id)

    opportunityIdOpt match {

      case None =>

        logger.error(msg = s"Opportunity id not provided. teamId: $teamId")

        Left(Res.NotFoundError(message = "opportunity id is required."))

      case Some(opportunityId) =>

        opportunityService.getOpportunity(teamId = teamId, opportunityUUID = opportunityId) match {

          case Failure(exception) =>

            val errMsg = s"Failed to fetch opportunity"

            logger.error(msg = s"$errMsg. teamId: $teamId :: opportunityId: $opportunityId", err = exception)

            Left {

              Res.ServerError(
                message = errMsg,
                e = None
              )

            }

          case Success(None) =>

            val errMsg = s"Failed to fetch opportunity - None"

            logger.error(msg = s"$errMsg. teamId: $teamId :: opportunityId: $opportunityId")

            Left {

              Res.NotFoundError(
                message = errMsg,
              )

            }

          case Success(Some(opportunity)) =>

            prospectDAOService.findFromMaster(
              teamId = teamId.id,
              prospectId = opportunity.prospect_id.id,
              Logger = logger
            ) match {
              case Failure(exception) =>

                val errMsg = s"Failed to fetch opportunity prospect"

                logger.error(
                  msg = s"$errMsg. teamId: $teamId :: opportunityId: $opportunityId :: prospectId: ${opportunity.prospect_id}",
                  err = exception
                )

                Left {

                  Res.ServerError(
                    message = errMsg,
                    e = None
                  )

                }

              case Success(None) =>

                val errMsg = s"Failed to fetch opportunity prospect - None"

                logger.error(
                  msg = s"$errMsg. teamId: $teamId :: opportunityId: $opportunityId :: prospectId: ${opportunity.prospect_id}"
                )

                Left {

                  Res.NotFoundError(
                    message = errMsg
                  )

                }

              case Success(Some(prospect)) =>

                if (!request.permittedAccountIds.contains(prospect.owner_id)) {

                  Left(Res.ForbiddenError(message = "You do not have the permission to access this opportunity"))

                } else {

                  Right {

                    OpportunityRequest(
                      opportunity = opportunity,
                      actingTeamAccount = teamMember,
                      loggedinAccount = request.loggedinAccount,
                      isApiCall = request.isApiCall,
                      permittedAccountIds = request.permittedAccountIds,
                      Logger = request.Logger,
                      Response = request.Response,
                      request = request
                    )

                  }

                }

            }

        }

    }

  }

  final def hasOpportunity(
    opportunityIdOpt: Option[OpportunityUUID]
  )(
    implicit ec: ExecutionContext
  ): ActionRefiner[PermissionRequest, OpportunityRequest] = {

    new ActionRefiner[PermissionRequest, OpportunityRequest] {

      override protected def executionContext: ExecutionContext = ec

      override def refine[A](request: PermissionRequest[A]): Future[Either[Result, OpportunityRequest[A]]] = {

        val Res = request.Response

        Future {

          request.actingTeamAccount match {

            case None =>

              Left(Res.NotFoundError(message = "Team Not Found"))

            case Some(teamMember: TeamMember) =>

              checkHasOpportunity(
                Res = Res,
                request = request,
                opportunityIdOpt = opportunityIdOpt,
                teamMember = teamMember
              )

          }

        }

      }

    }

  }


  private final def checkHasPipeline[A](
    Res: ApiResponseModuleForPermissionedApis,
    request: PermissionRequest[A],
    pipelineIdOpt: Option[PipelineUUID],
    teamMember: TeamMember,
    isPipelineRequired: Boolean
  ): Either[Result, PipelineRequest[A]] = {

    val logger: SRLogger = request.Logger

    val teamId = TeamId(id = teamMember.team_id)

    pipelineIdOpt match {

      case None =>

        if (isPipelineRequired) {

          logger.error(msg = s"Pipeline id not provided. teamId: $teamId")

          Left(Res.NotFoundError(message = "Pipeline id is required."))

        } else {

          Right {

            PipelineRequest(
              pipelineOpt = None,
              actingTeamAccount = teamMember,
              loggedinAccount = request.loggedinAccount,
              isApiCall = request.isApiCall,
              permittedAccountIds = request.permittedAccountIds,
              Logger = request.Logger,
              Response = request.Response,
              request = request
            )

          }

        }

      case Some(pipelineId) =>

        pipelineService.getPipeline(teamId = teamId, pipelineUUID = pipelineId) match {

          case Failure(exception) =>

            val errMsg = s"Failed to fetch pipeline"

            logger.error(msg = s"$errMsg. teamId: $teamId :: pipelineId: $pipelineId", err = exception)

            Left {

              Res.ServerError(
                message = errMsg,
                e = None
              )

            }

          case Success(None) =>

            // TODO: The teamId provided is used in the where clause, so if the pipeline id provided
            //        does not belong to the team it will return None.
            //        But it can also return None if the pipelineId provide does not exist.
            //        So what Response should we return `NotFoundError` or `ForbiddenError`

            val errMsg = s"Provided pipeline id does not exist in this team"

            logger.error(msg = s"$errMsg. teamId: $teamId :: pipelineId: $pipelineId")

            Left {

              Res.BadRequestError(
                message = errMsg,
              )

            }

          case Success(Some(pipeline)) =>

            // Note: This check is fruitless as we are using the same teamId to fetch the pipeline;
            //        teamId is used in where clause.
            if (pipeline.team_id != teamId) {

              Left(Res.ForbiddenError(message = "You do not have the permission to access this pipeline."))

            } else {

              Right {

                PipelineRequest(
                  pipelineOpt = Some(pipeline),
                  actingTeamAccount = teamMember,
                  loggedinAccount = request.loggedinAccount,
                  isApiCall = request.isApiCall,
                  permittedAccountIds = request.permittedAccountIds,
                  Logger = request.Logger,
                  Response = request.Response,
                  request = request
                )

              }

            }

        }

    }

  }

  final def hasPipeline(
    pipelineIdOpt: Option[PipelineUUID],
    isPipelineRequired: Boolean = true
  )(
    implicit ec: ExecutionContext
  ): ActionRefiner[PermissionRequest, PipelineRequest] = {

    new ActionRefiner[PermissionRequest, PipelineRequest] {

      override protected def executionContext: ExecutionContext = ec

      override def refine[A](request: PermissionRequest[A]): Future[Either[Result, PipelineRequest[A]]] = {

        val Res = request.Response

        Future {

          request.actingTeamAccount match {

            case None =>

              Left(Res.NotFoundError(message = "Team Not Found"))

            case Some(teamMember: TeamMember) =>

              checkHasPipeline(
                Res = Res,
                request = request,
                pipelineIdOpt = pipelineIdOpt,
                teamMember = teamMember,
                isPipelineRequired = isPipelineRequired
              )

          }

        }

      }

    }

  }

}
