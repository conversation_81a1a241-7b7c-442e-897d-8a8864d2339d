package api.pipelines.controllers

import api.CONSTANTS
import api.accounts.{PermType, PermissionUtils, TeamId}
import api.pipelines.dao.{OpportunityStatusCreateData, OpportunityStatusDeleteData, OpportunityStatusUpdateData, ReorderOpportunityStatusData}
import api.pipelines.models.{OpportunityStatusUUID, PipelineUUID}
import api.pipelines.services.OpportunityStatusService
import api.pipelines.utils.OpportunityUtils
import play.api.libs.json.{JsError, JsSuccess, JsValue, Json}
import play.api.libs.ws.WSClient
import play.api.mvc.{Action, AnyContent, BaseController, ControllerComponents}
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class OpportunityStatusController(
  opportunityStatusService: OpportunityStatusService,
  permissionUtils: PermissionUtils,
  opportunityUtils: OpportunityUtils,
  protected val controllerComponents: ControllerComponents,
  implicit val ws: WSClient,
) extends BaseController {

  private implicit val ec: ExecutionContext = controllerComponents.executionContext

  def getStatuses(
    v: String,
    pipelineUUID: String,
    tid: Option[Long]
  ): Action[AnyContent] = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_PROSPECTS,
    tidOpt = tid
  ) { request =>

    /**
      * Permissions:
      *
      * Query is always restricted by teamId and pipelineUUID
      *
      * */

    val Res = request.Response

    val actingTeamAccountOpt = request.actingTeamAccount

    given logger: SRLogger = request.Logger

    actingTeamAccountOpt match {

      case None =>

        val errMsg = s"actingTeamAccount not found. tid: $tid"

        logger.error(msg = errMsg)

        Res.NotFoundError(message = errMsg)

      case Some(actingTeamAccount) =>

        opportunityStatusService.getStatuses(
          teamId = TeamId(id = actingTeamAccount.team_id),
          pipelineUUID = PipelineUUID(uuid = pipelineUUID),
          logger = logger
        ) match {

          case Failure(exception) =>

            val errMsg = s"Failed to fetch statuses. tid: ${actingTeamAccount.team_id} :: pipelineUUID: $pipelineUUID"

            logger.error(msg = errMsg, err = exception)

            Res.ServerError(
              message = errMsg,
              e = Some(exception)
            )

          case Success(statuses) =>

            Res.Success(
              message = "Success",
              data = Json.toJson(statuses)
            )

        }
    }
  }

  def createStatus(
    v: String,
    pipelineUUID: String,
    tid: Option[Long]
  ): Action[JsValue] = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PIPELINE_DETAILS,
    tidOpt = tid
  ) andThen opportunityUtils.hasPipeline(
    pipelineIdOpt = Some(PipelineUUID(uuid = pipelineUUID))
  )).async(parse.json) { request =>

    /**
      * Permissions:
      *
      * checking if the provided pipelineUUID belongs to the team. - hasPipeline
      *
      * */

    val Res = request.Response

    val actingTeamAccount = request.actingTeamAccount

    given logger: SRLogger = request.Logger

    Future {

      request.body.validate[OpportunityStatusCreateData] match {

        case JsError(errors) =>

          Res.JsValidationError(errors = errors)

        case JsSuccess(statusData: OpportunityStatusCreateData, _) =>

          opportunityStatusService.createStatus(
            teamId = TeamId(id = actingTeamAccount.team_id),
            pipelineUUID = PipelineUUID(uuid = pipelineUUID),
            statusData = statusData,
            logger = logger
          ) match {

            case Failure(exception) =>

              val errMsg = s"Failed to create status."

              logger.error(msg = s"$errMsg tid: ${actingTeamAccount.team_id} :: pipelineUUID: $pipelineUUID :: statusData: $statusData", err = exception)

              Res.ServerError(
                message = errMsg,
                e = Some(exception)
              )

            case Success(None) =>

              val errMsg = s"Not found status."

              logger.error(
                msg = s"$errMsg  tid: ${actingTeamAccount.team_id} :: pipelineUUID: $pipelineUUID :: statusData: $statusData"
              )

              Res.NotFoundError(errMsg)

            case Success(Some(status)) =>

              Res.Success(
                message = "Success",
                data = Json.toJson(status)
              )
          }
      }
    }
  }

  def updateStatus(
    v: String,
    pipelineUUID: String,
    statusUUID: String,
    tid: Option[Long]
  ): Action[JsValue] = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PIPELINE_DETAILS,
    tidOpt = tid
  ).async(parse.json) { request =>

    /**
      * Permissions:
      *
      * Provided pipelineUUID and statusUUID, both are used in where clause of the query with teamId.
      *
      * */

    val Res = request.Response

    val actingTeamAccountOpt = request.actingTeamAccount

    given logger: SRLogger = request.Logger

    Future {

      actingTeamAccountOpt match {

        case None =>

          val errMsg = s"actingTeamAccount not found. tid: $tid"

          logger.error(msg = errMsg)

          Res.NotFoundError(message = errMsg)

        case Some(actingTeamAccount) =>

          request.body.validate[OpportunityStatusUpdateData] match {

            case JsError(errors) =>

              Res.JsValidationError(errors = errors)

            case JsSuccess(statusData: OpportunityStatusUpdateData, _) =>

              opportunityStatusService.updateStatus(
                teamId = TeamId(id = actingTeamAccount.team_id),
                pipelineUUID = PipelineUUID(uuid = pipelineUUID),
                statusUUID = OpportunityStatusUUID(uuid = statusUUID),
                statusData = statusData,
                logger = logger
              ) match {

                case Failure(exception) =>

                  val errMsg = s"Failed to update status."

                  logger.error(
                    msg = s"$errMsg  statusUUID: $statusUUID :: pipelineUUID: $pipelineUUID :: tid: ${actingTeamAccount.team_id} :: statusData: $statusData",
                    err = exception
                  )

                  Res.ServerError(
                    message = errMsg,
                    e = Some(exception)
                  )

                case Success(None) =>

                  val errMsg = s"Not found status for update. statusUUID: $statusUUID :: pipelineUUID: $pipelineUUID :: tid: ${actingTeamAccount.team_id} :: statusData: $statusData"

                  logger.error(msg = errMsg)

                  Res.NotFoundError(errMsg)

                case Success(Some(status)) =>

                  Res.Success(
                    message = "Success",
                    data = Json.toJson(status)
                  )
              }
          }
      }
    }
  }

  def reorderStatus(
    v: String,
    pipelineUUID: String,
    statusUUID: String,
    tid: Option[Long]
  ): Action[JsValue] = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PIPELINE_DETAILS,
    tidOpt = tid
  ).async(parse.json) { request =>

    /**
      * Permissions:
      *
      * statusUUID and teamId are used in the where clause.
      *
      * pipelineUUID is not used.
      *
      * */

    val Res = request.Response

    val actingTeamAccountOpt = request.actingTeamAccount

    given logger: SRLogger = request.Logger

    Future {

      actingTeamAccountOpt match {

        case None =>

          val errMsg = s"actingTeamAccount not found. tid: $tid"

          logger.error(msg = errMsg)

          Res.NotFoundError(message = errMsg)

        case Some(actingTeamAccount) =>

          request.body.validate[ReorderOpportunityStatusData] match {

            case JsError(errors) =>

              Res.JsValidationError(errors = errors)

            case JsSuccess(reorderStatusData: ReorderOpportunityStatusData, _) =>

              opportunityStatusService.reorderStatus(
                teamId = TeamId(id = actingTeamAccount.team_id),
                pipelineUUID = PipelineUUID(uuid = pipelineUUID),
                statusUUID = OpportunityStatusUUID(uuid = statusUUID),
                reorderStatusData = reorderStatusData,
                logger = logger
              ) match {

                case Failure(exception) =>

                  val errMsg = s"Failed to reorder status."

                  logger.error(
                    msg = s"$errMsg statusUUID: $statusUUID :: pipelineUUID: $pipelineUUID :: tid: ${actingTeamAccount.team_id} :: reorderStatusData: $reorderStatusData",
                    err = exception
                  )

                  Res.ServerError(
                    message = errMsg,
                    e = Some(exception)
                  )

                case Success(None) =>

                  val errMsg = s"Not found status for reorder. statusUUID: $statusUUID :: pipelineUUID: $pipelineUUID :: tid: ${actingTeamAccount.team_id} :: reorderStatusData: $reorderStatusData"

                  logger.error(msg = errMsg)

                  Res.NotFoundError(errMsg)

                case Success(Some(status)) =>

                  Res.Success(
                    message = "Success",
                    data = Json.toJson(status)
                  )
              }
          }
      }
    }
  }

  def deleteOpportunityStatus(
    v: String,
    pipelineUUID: String,
    statusUUID: String,
    tid: Option[Long]
  ): Action[JsValue] = permissionUtils.checkPermission(
    version = v,
    permission = PermType.DELETE_PIPELINE_DETAILS,
    tidOpt = tid,
  ).async(parse.json) { request =>

    /**
      * Permissions:
      *
      * statusUUID and teamId are used in the where clause.
      *
      * pipelineUUID is not used.
      *
      * */

    val Res = request.Response

    val actingTeamAccountOpt = request.actingTeamAccount

    given logger: SRLogger = request.Logger

    Future {

      actingTeamAccountOpt match {

        case None =>

          val errMsg = s"actingTeamAccount not found - deleteOpportunityStatus. tid: $tid"

          logger.error(msg = errMsg)

          Res.NotFoundError(message = errMsg)

        case Some(actingTeamAccount) =>

          request.body.validate[OpportunityStatusDeleteData] match {

            case JsError(errors) =>

              Res.JsValidationError(errors = errors)

            case JsSuccess(opportunityStatusDeleteData: OpportunityStatusDeleteData, _) =>

              opportunityStatusService.deleteOpportunityStatus(
                teamId = TeamId(id = actingTeamAccount.team_id),
                pipelineUUID = PipelineUUID(uuid = pipelineUUID),
                statusUUID = OpportunityStatusUUID(uuid = statusUUID),
                opportunityStatusDeleteData = opportunityStatusDeleteData,
                logger = logger
              ) match {

                case Failure(exception) =>

                  val errMsg = s"Failed to delete opportunity status. statusUUID: $statusUUID :: pipelineUUID: $pipelineUUID :: tid: ${actingTeamAccount.team_id} :: opportunityStatusDeleteData: $opportunityStatusDeleteData"

                  logger.error(msg = errMsg, err = exception)

                  Res.ServerError(
                    message = CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER,
                    e = Some(exception)
                  )

                case Success(_) =>

                  Res.Success(
                    message = "Success",
                    data = Json.obj()
                  )
              }
          }
      }
    }
  }

}
