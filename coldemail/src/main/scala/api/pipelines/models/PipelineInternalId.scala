package api.pipelines.models

import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>ult, JsNumber, JsSuccess, JsValue, Reads, Writes}


case class PipelineInternalId(id: Long) extends AnyVal {

  override def toString: String = id.toString
}

object PipelineInternalId {

  implicit val reads: Reads[PipelineInternalId] = new Reads[PipelineInternalId] {
    override def reads(ev: JsValue): JsResult[PipelineInternalId] = {
      ev match {
        case JsNumber(id) => JsSuccess(PipelineInternalId(id = id.toLong))
        case randomValue => JsError(s"expected number, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[PipelineInternalId] = new Writes[PipelineInternalId] {
    override def writes(o: PipelineInternalId): JsValue = JsNumber(o.id)
  }
}
