package api.pipelines.services

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.AccountId
import api.pipelines.dao.{PipelineDAO, PipelineData}
import api.pipelines.models.{Pipeline, PipelineUUID, StatusType}
import utils.SRLogger
import utils.dbutils.DBUtils
import utils.uuid.SrUuidUtils

import scala.util.{Failure, Success, Try}

class PipelineService(
  pipelineDAO: PipelineDAO,
  srUuidUtils: SrUuidUtils,
  dbUtils: DBUtils,
  opportunitiesDefaultSetupService: OpportunitiesDefaultSetupService
) {

  def getOpportunityStatusCountByStatusTypeInPipeline(
    teamId: TeamId,
    pipelineUUID: PipelineUUID
  ): Try[Map[StatusType, Int]] = {

    pipelineDAO.getOpportunityStatusCountByStatusTypeInPipeline(
      teamId = teamId,
      pipelineUUID = pipelineUUID
    )

  }

  def getPipelines(teamId: TeamId): Try[List[Pipeline]] = {

    pipelineDAO.getPipelines(teamId = teamId)

  }

  def getPipeline(teamId: TeamId, pipelineUUID: PipelineUUID): Try[Option[Pipeline]] = {

    pipelineDAO.getPipeline(teamId = teamId, pipelineUUID = pipelineUUID)

  }

  def createPipeline(
    teamId: TeamId,
    ownerId: AccountId,
    pipelineData: PipelineData,
    logger: SRLogger
  ): Try[Option[Pipeline]] = {

    val pipelineUUID: PipelineUUID = PipelineUUID(uuid = srUuidUtils.generatePipelineUuid())

    pipelineDAO.getPipelineCount(teamId = teamId) match {

      case Failure(exception) =>

        logger.error(
          msg = s"Failed to fetch pipeline count - createPipeline. teamId: $teamId :: pipelineUUID: $pipelineUUID",
          err = exception
        )

        Failure(exception)

      case Success(pipelineCount: Int) =>

        if (pipelineCount >= AppConfig.Opportunities.maxPipelineLimitPerTeam) {

          val errMsg = "Max pipeline limit reached, cannot create more pipelines"

          logger.error(
            msg = s"$errMsg. teamId: $teamId :: pipelineUUID: $pipelineUUID :: pipelineCount: $pipelineCount"
          )

          Failure(new Exception(errMsg))

        } else {

          PipelineService.validatePipelineData(pipelineData = pipelineData) match {

            case Some(errMsg) =>

              logger.error(
                s"Failed to create pipeline - $errMsg. teamId: $teamId :: pipelineUUID: $pipelineUUID :: pipelineData: $pipelineData"
              )

              Failure(new Exception(s"Failed to create pipeline - $errMsg"))

            case None =>

              val dbAndSession = dbUtils.startLocalTx()

              val session = dbAndSession.session

              val tryOfPipelineOpt: Try[Option[Pipeline]] =
                opportunitiesDefaultSetupService.createPipelineWithDefaultStatuses(
                  teamId = teamId,
                  ownerId = ownerId,
                  pipelineUUID = pipelineUUID,
                  pipelineData = pipelineData,
                  logger = logger
                )(session = session)

              // pipelineDAO.createPipelineWithAutoTx(
              //   teamId = teamId,
              //   ownerId = ownerId,
              //   pipelineData = pipelineData,
              //   pipelineUUID = pipelineUUID
              // )

              dbUtils.commitAndCloseSession(db = dbAndSession.db)

              tryOfPipelineOpt
          }
        }
    }
  }

  def updatePipeline(
    teamId: TeamId,
    pipelineUUID: PipelineUUID,
    pipelineData: PipelineData,
    logger: SRLogger
  ): Try[Option[Pipeline]] = {

    PipelineService.validatePipelineData(pipelineData = pipelineData) match {
      case Some(errMsg) =>

        logger.error(
          s"Failed to update pipeline - $errMsg. teamId: $teamId :: pipelineUUID: $pipelineUUID :: pipelineData: $pipelineData"
        )

        Failure(new Exception(s"Failed to update pipeline - $errMsg"))

      case None =>

        pipelineDAO.updatePipeline(
          teamId = teamId,
          pipelineUUID = pipelineUUID,
          pipelineData = pipelineData
        )
    }

  }

  def deletePipeline(
    teamId: TeamId,
    pipelineUUID: PipelineUUID,
    logger: SRLogger
  ): Try[Int] = {

    pipelineDAO.getPipelineCount(teamId = teamId) match {

      case Failure(exception) =>

        logger.error(
          msg = s"Failed to fetch pipeline count - deletePipeline. teamId: $teamId :: pipelineUUID: $pipelineUUID",
          err = exception
        )

        Failure(exception)

      case Success(pipelineCount: Int) =>

        if (pipelineCount <= AppConfig.Opportunities.minPipelineLimitPerTeam) {

          val errMsg = "Min pipeline limit reached, cannot delete more pipelines"

          logger.error(
            msg = s"$errMsg. teamId: $teamId :: pipelineUUID: $pipelineUUID :: pipelineCount: $pipelineCount"
          )

          Failure(new Exception(errMsg))

        } else {

          pipelineDAO.deletePipeline(
            teamId = teamId,
            pipelineUUID = pipelineUUID
          )

        }
    }
  }

}

object PipelineService {

  val EmptyPipelineNameErrorMsg = "Pipeline name cannot be empty."

  val MaxPipelineNameLimitErrorMsg =
    s"Pipeline name cannot exceed the maximum limit of ${AppConfig.Opportunities.maxCharLimitPipelineName} characters."


  def validatePipelineData(pipelineData: PipelineData): Option[String] = {

    if (
      pipelineData.pipeline_name.trim.isEmpty
    ) {
      Some(
        EmptyPipelineNameErrorMsg
      )
    }
    else if (pipelineData.pipeline_name.length > AppConfig.Opportunities.maxCharLimitPipelineName) {
      Some(
        MaxPipelineNameLimitErrorMsg
      )
    }
    else {
      None
    }
  }

}
