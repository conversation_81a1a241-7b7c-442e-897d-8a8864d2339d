package api.pipelines.services

import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import utils.dbutils.{DBUtils, DbAndSession}
import api.pipelines.dao.{OpportunityDAO, OpportunityData, ReorderOpportunityData}
import api.pipelines.models.{Opportunity, OpportunityPosRank, OpportunityStatusInternalId, OpportunityStatusUUID, OpportunityUUID, PipelineInternalId}
import scalikejdbc.DBSession
import utils.SRLogger
import utils.uuid.SrUuidUtils

import scala.util.{Failure, Success, Try}


class OpportunityDAOService(
  annualizedValueService: AnnualizedValueService,
  opportunityDAO: OpportunityDAO,
  dbUtils: DBUtils,
  srUuidUtils: SrUuidUtils,
) {

  def createOpportunity(
    teamId: TeamId,
    opportunityData: OpportunityData,
    opportunityStatusInternalId: OpportunityStatusInternalId,
    pipelineInternalId: PipelineInternalId,
    ownerAccountInternalId: AccountId,
    newHighestOpportunityRankInStatus: OpportunityPosRank,
    orgId: OrgId,
    logger: SRLogger
  ): Try[Option[Opportunity]] = {

    val opportunityUUID: OpportunityUUID = OpportunityUUID(uuid = srUuidUtils.generateOpportunityUuid())

    val dbAndSession: DbAndSession = dbUtils.startLocalTx()

    implicit val session: DBSession = dbAndSession.session

    val currAnnualizedValueData = AnnualizedValueData(
      opportunityStatusUUID = opportunityData.opportunity_status_id,
      opportunityValue = 0,
      opportunityType = opportunityData.opportunity_type,
      opportunityCurrency = opportunityData.currency
    )

    val newAnnualizedValueData = currAnnualizedValueData.copy(
      opportunityValue = opportunityData.value
    )

    val tryOfCreatedOpportunityUUIDOpt: Try[Option[OpportunityUUID]] = for {

      _: Option[OpportunityStatusUUID] <- annualizedValueService.updateAnnualizedValue(
        teamId = teamId,
        currAnnualizedValueData = currAnnualizedValueData,
        newAnnualizedValueData = newAnnualizedValueData,
        orgId = orgId,
        logger = logger
      )

      opportunityUUIDOpt: Option[OpportunityUUID] <- {

        opportunityDAO.createOpportunity(
          teamId = teamId,
          opportunityUUID = opportunityUUID,
          opportunityData = opportunityData,
          opportunityStatusInternalId = opportunityStatusInternalId,
          pipelineInternalId = pipelineInternalId,
          ownerAccountInternalId = ownerAccountInternalId,
          newHighestOpportunityRankInStatus = newHighestOpportunityRankInStatus
        ) match {
          case Failure(exception) =>

            logger.error(
              s"createOpportunity Failed. teamId: $teamId :: opportunityUUID: $opportunityUUID :: opportunityStatusInternalId: $opportunityStatusInternalId :: pipelineInternalId: $pipelineInternalId :: opportunityData: $opportunityData",
              err = exception
            )

            Failure(exception)

          case Success(None) =>

            logger.error(
              s"createOpportunity Failed - NONE. teamId: $teamId :: opportunityUUID: $opportunityUUID :: opportunityStatusInternalId: $opportunityStatusInternalId :: pipelineInternalId: $pipelineInternalId :: opportunityData: $opportunityData",
            )

            Success(None)

          case Success(Some(opportunityUUID: OpportunityUUID)) =>

            Success(Some(opportunityUUID))

        }
      }
    } yield {

      opportunityUUIDOpt

    }

    dbUtils.commitAndCloseSession(db = dbAndSession.db)

    // Note: fetch the opportunity to be returned after the session has been closed, or else we will get
    // the old opportunity/None as the get opportunity query is not part of the session.

    tryOfCreatedOpportunityUUIDOpt.flatMap {

      case None =>

        Success(None)

      case Some(opportunityUUID: OpportunityUUID) =>

        opportunityDAO.getOpportunity(
          teamId = teamId,
          opportunityUUID = opportunityUUID
        )

    }

  }

  def updateOpportunity(
    teamId: TeamId,
    opportunityUUID: OpportunityUUID,
    opportunityData: OpportunityData,
    opportunityStatusInternalId: OpportunityStatusInternalId,
    pipelineInternalId: PipelineInternalId,
    ownerAccountInternalId: AccountId,
    oldOpportunity: Opportunity,
    opportunityPosRank: OpportunityPosRank,
    orgId: OrgId,
    logger: SRLogger
  ): Try[Option[Opportunity]] = {

    val dbAndSession: DbAndSession = dbUtils.startLocalTx()

    implicit val session: DBSession = dbAndSession.session

    val tryOfUpdatedOpportunityUUIDOpt: Try[Option[OpportunityUUID]] = for {

      _: Option[OpportunityStatusUUID] <- {

        // Only call if currency or value or status or type has changed.

        if (
          OpportunityDAOService.shouldUpdateAnnualizedValueOnOpportunityUpdate(
            oldOpportunity = oldOpportunity,
            opportunityData = opportunityData
          )
        ) {

          annualizedValueService.updateAnnualizedValue(
            teamId = teamId,
            currAnnualizedValueData = AnnualizedValueData(
              opportunityStatusUUID = oldOpportunity.opportunity_status_id,
              opportunityValue = oldOpportunity.value,
              opportunityType = oldOpportunity.opportunity_type,
              opportunityCurrency = oldOpportunity.currency
            ),
            newAnnualizedValueData = AnnualizedValueData(
              opportunityStatusUUID = opportunityData.opportunity_status_id,
              opportunityValue = opportunityData.value,
              opportunityType = opportunityData.opportunity_type,
              opportunityCurrency = opportunityData.currency
            ),
            orgId = orgId,
            logger = logger
          )

        } else {

          Success(None)

        }
      }

      opportunityUUIDOpt: Option[OpportunityUUID] <- {

        opportunityDAO.updateOpportunity(
          teamId = teamId,
          opportunityUUID: OpportunityUUID,
          opportunityData = opportunityData,
          opportunityStatusInternalId = opportunityStatusInternalId,
          pipelineInternalId = pipelineInternalId,
          ownerAccountInternalId = ownerAccountInternalId,
          opportunityPosRank = opportunityPosRank
        ) match {
          case Failure(exception) =>

            logger.error(
              s"updateOpportunity Failed. teamId: $teamId :: opportunityUUID: $opportunityUUID :: opportunityStatusInternalId: $opportunityStatusInternalId :: pipelineInternalId: $pipelineInternalId :: opportunityData: $opportunityData",
              err = exception
            )

            Failure(exception)

          case Success(None) =>

            logger.error(
              s"updateOpportunity Failed - NONE. teamId: $teamId :: opportunityUUID: $opportunityUUID :: opportunityStatusInternalId: $opportunityStatusInternalId :: pipelineInternalId: $pipelineInternalId :: opportunityData: $opportunityData",
            )

            Success(None)

          case Success(Some(opportunityUUID: OpportunityUUID)) =>

            Success(Some(opportunityUUID))

        }
      }
    } yield {

      opportunityUUIDOpt

    }

    dbUtils.commitAndCloseSession(db = dbAndSession.db)

    // Note: fetch the opportunity to be returned after the session has been closed, or else we will get
    // the old opportunity/None as the get opportunity query is not part of the session.

    tryOfUpdatedOpportunityUUIDOpt.flatMap {

      case None =>

        Success(None)

      case Some(opportunityUUID: OpportunityUUID) =>

        opportunityDAO.getOpportunity(
          teamId = teamId,
          opportunityUUID = opportunityUUID
        )

    }
  }

  def reorderOpportunity(
    teamId: TeamId,
    opportunityUUID: OpportunityUUID,
    destOpportunityStatusInternalId: OpportunityStatusInternalId,
    reorderOpportunityData: ReorderOpportunityData,
    oldOpportunity: Opportunity,
    orgId: OrgId,
    logger: SRLogger
  ): Try[Option[Opportunity]] = {

    val dbAndSession: DbAndSession = dbUtils.startLocalTx()

    implicit val session: DBSession = dbAndSession.session

    val currAnnualizedValueData = AnnualizedValueData(
      opportunityStatusUUID = oldOpportunity.opportunity_status_id,
      opportunityValue = oldOpportunity.value,
      opportunityType = oldOpportunity.opportunity_type,
      opportunityCurrency = oldOpportunity.currency
    )

    val newAnnualizedValueData = currAnnualizedValueData.copy(
      opportunityStatusUUID = reorderOpportunityData.dest_status_uuid
    )

    val tryOfReorderedOpportunityUUIDOpt: Try[Option[OpportunityUUID]] = for {

      _: Option[OpportunityStatusUUID] <- {

        if (
          OpportunityDAOService.shouldUpdateAnnualizedValueOnOpportunityReorder(
            oldOpportunity = oldOpportunity,
            reorderOpportunityData = reorderOpportunityData
          )
        ) {

          // Only call if status has changed.

          annualizedValueService.updateAnnualizedValue(
            teamId = teamId,
            currAnnualizedValueData = currAnnualizedValueData,
            newAnnualizedValueData = newAnnualizedValueData,
            orgId = orgId,
            logger = logger
          )
        } else {
          Success(None)
        }
      }

      opportunityUUIDOpt: Option[OpportunityUUID] <- opportunityDAO.reorderOpportunity(
        teamId = teamId,
        opportunityUUID = opportunityUUID,
        reorderOpportunityData = reorderOpportunityData,
        destOpportunityStatusInternalId = destOpportunityStatusInternalId
      ) match {
        case Failure(exception) =>

          logger.error(
            s"reorderOpportunity Failed. teamId: $teamId :: opportunityUUID: $opportunityUUID :: destOpportunityStatusInternalId: $destOpportunityStatusInternalId :: reorderOpportunityData: $reorderOpportunityData",
            err = exception
          )

          Failure(exception)

        case Success(None) =>

          logger.error(
            s"reorderOpportunity Failed - NONE. teamId: $teamId :: opportunityUUID: $opportunityUUID :: destOpportunityStatusInternalId: $destOpportunityStatusInternalId :: reorderOpportunityData: $reorderOpportunityData",
          )

          Success(None)

        case Success(Some(opportunityUUID: OpportunityUUID)) =>

          Success(Some(opportunityUUID))

      }

    } yield {

      opportunityUUIDOpt

    }

    dbUtils.commitAndCloseSession(db = dbAndSession.db)

    // Note: fetch the opportunity to be returned after the session has been closed, or else we will get
    // the old opportunity/None as the get opportunity query is not part of the session.

    tryOfReorderedOpportunityUUIDOpt.flatMap {

      case None =>

        Success(None)

      case Some(opportunityUUID: OpportunityUUID) =>

        opportunityDAO.getOpportunity(
          teamId = teamId,
          opportunityUUID = opportunityUUID
        )

    }
  }


  def deleteOpportunity(
    teamId: TeamId,
    opportunityUUID: OpportunityUUID,
    oldOpportunity: Opportunity,
    orgId: OrgId,
    logger: SRLogger
  ): Try[Int] = {

    val dbAndSession: DbAndSession = dbUtils.startLocalTx()

    implicit val session: DBSession = dbAndSession.session

    val currAnnualizedValueData = AnnualizedValueData(
      opportunityStatusUUID = oldOpportunity.opportunity_status_id,
      opportunityValue = oldOpportunity.value,
      opportunityType = oldOpportunity.opportunity_type,
      opportunityCurrency = oldOpportunity.currency
    )

    val newAnnualizedValueData = currAnnualizedValueData.copy(
      opportunityValue = 0
    )

    val tryOfDeletedOpportunityOpt = for {

      _: Option[OpportunityStatusUUID] <- annualizedValueService.updateAnnualizedValue(
        teamId = teamId,
        currAnnualizedValueData = currAnnualizedValueData,
        newAnnualizedValueData = newAnnualizedValueData,
        orgId = orgId,
        logger = logger
      )

      deleteCount: Int <- opportunityDAO.deleteOpportunity(
        teamId = teamId,
        opportunityUUID = opportunityUUID
      )

    } yield {

      deleteCount

    }

    dbUtils.commitAndCloseSession(db = dbAndSession.db)

    tryOfDeletedOpportunityOpt

  }

  def transferOpportunities(
    teamId: TeamId,
    fromOpportunityStatusInternalId: OpportunityStatusInternalId,
    toOpportunityStatusInternalId: OpportunityStatusInternalId
  )(
    implicit session: DBSession
  ): Try[Int] = {

    opportunityDAO.getHighestRankOpportunity(
      teamId = teamId,
      opportunityStatusInternalId = toOpportunityStatusInternalId
    ).flatMap { highestRank =>

      opportunityDAO.transferOpportunities(
        teamId = teamId,
        fromOpportunityStatusInternalId = fromOpportunityStatusInternalId,
        toOpportunityStatusInternalId = toOpportunityStatusInternalId,
        highestRankInToBeTransferredStatus = highestRank
      )

    }
  }

}

object OpportunityDAOService {

  def shouldUpdateAnnualizedValueOnOpportunityUpdate(
    oldOpportunity: Opportunity,
    opportunityData: OpportunityData
  ): Boolean = {

    if (
      oldOpportunity.currency != opportunityData.currency ||
        oldOpportunity.value != opportunityData.value ||
        oldOpportunity.opportunity_status_id != opportunityData.opportunity_status_id ||
        oldOpportunity.opportunity_type != opportunityData.opportunity_type
    ) {
      true
    } else {
      false
    }

  }

  def shouldUpdateAnnualizedValueOnOpportunityReorder(
    oldOpportunity: Opportunity,
    reorderOpportunityData: ReorderOpportunityData
  ): Boolean = {

    if (oldOpportunity.opportunity_status_id != reorderOpportunityData.dest_status_uuid) {
      true
    } else {
      false
    }

  }

}
