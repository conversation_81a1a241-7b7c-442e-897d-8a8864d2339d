package api.integrations.services

import api.integrations.dao.WorkflowAutomationReportingDAO
import api.triggers.{CRMCountForLastSixHoursOverall, CRMCountForLastSixHoursTeamWise, TriggerServiceV2}
import org.joda.time.{DateTime, Duration}
import play.api.libs.ws.WSClient
import utils.SRLogger
import play.api.libs.json.JodaWrites._
import play.api.libs.json.JodaReads._
import utils.email_notification.service.EmailNotificationService

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success, Try}

class WorkflowAutomationReportingService(
                                          triggerServiceV2: TriggerServiceV2,
                                          emailNotificationService: EmailNotificationService,
                                          workflowAutomationReportingDAO: WorkflowAutomationReportingDAO
                                        ) {

  private val jedisKey = "crm_report_last_sent"

  private def checkIfWeSendReport()(using Logger: SRLogger): Boolean = {

    workflowAutomationReportingDAO.get(
      jedisKey
    ) match {
      case Some(value) =>
        val lastSentAt = DateTime.parse(value.toString)
        val timeSince = new Duration(lastSentAt, DateTime.now()).toStandardHours.getHours
        Logger.debug(s"Email last sent at $value  which is ${timeSince} hours ago")
        lastSentAt.plusHours(6).isBefore(DateTime.now())
      case None => true
    }
  }

  def checkIfWeSendReportAndSendIt()(using logger: SRLogger, ws: WSClient, ec: ExecutionContext): Try[Boolean] = {
    logger.debug("Enter checkIfWeSendReportAndSendIt")
    if (checkIfWeSendReport()) {

      val result = for {
        overallReport: List[CRMCountForLastSixHoursOverall] <- triggerServiceV2.gettingCRMSuccessCountForLastSixHoursOverall()

        teamWiseReport: List[CRMCountForLastSixHoursTeamWise] <- triggerServiceV2.gettingCRMSuccessCountForLastSixHoursTeamWise()
      } yield {
        val subject = if(teamWiseReport.exists(data => data.fail > 0 && data.total > 0  && (data.fail / data.total > 0.8))) {
          "CRM Reports for the last 6 hours (80% failure rate for some teams, please check)"
        } else {
          "CRM Reports for the last 6 hours"
        }

        val body = WorkflowAutomationReportingService.createBody(overallReport = overallReport, teamWiseReport = teamWiseReport)

        emailNotificationService.sendMailFromAdmin(
          toEmail = "<EMAIL>",
          toName = Some("Smartreach Team"),
          additionalBccEmails = Seq(
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
          ),
          subject = subject,
          body = body


        ) map { _ =>

          workflowAutomationReportingDAO.set(
            value = DateTime.now().getMillis,
            idData = jedisKey
          )
          logger.debug("Exit: True checkIfWeSendReportAndSendIt")
          true
        }
      }

      result.flatten
    } else {
      logger.debug("Exit: False checkIfWeSendReportAndSendIt :: Email Not sent")
      Success(false)
    }
  }

}

object WorkflowAutomationReportingService {

  def createBody(overallReport: List[CRMCountForLastSixHoursOverall], teamWiseReport: List[CRMCountForLastSixHoursTeamWise]): String = {
    val overallTableList:List[String] = overallReport.map { row =>
      s"""
          <tr>
            <td>${row.crm_type}</td>
            <td>${row.success}</td>
            <td>${row.fail}</td>
            <td>${row.ignored_attempt}</td>
            <td>${row.yet_to_attempt}</td>
            <td>${row.started_attempt}</td>
            <td>${row.total}</td>
          </tr>
       """
    }

    val overallTable = if(overallTableList.isEmpty) {
      "No CRM calls"
    } else {
      overallTableList.reduce(_ + _)
    }


    val teamWiseTableList: List[String] = teamWiseReport.map { row =>
      s"""
          <tr>
            <td>${row.email}</td>
            <td>${row.org_name}</td>
            <td>${row.company}</td>
            <td>${row.team_name}</td>
            <td>${row.crm_type}</td>
            <td>${row.success}</td>
            <td>${row.fail}</td>
            <td>${row.total}</td>
          </tr>
       """
    }

    val teamWiseTable = if (teamWiseTableList.isEmpty) {
      "No CRM calls"
    } else {
      teamWiseTableList.reduce(_ + _)
    }

    s"""
          Hey Team,
            This is the CRM Related report for the last 6 hours (From ${DateTime.now().minusHours(6)} to ${DateTime.now()}) -

            Overall Reports:
            <table>
              <tr>
                <th>CRM Type</th>
                <th>Success</th>
                <th>Failed</th>
                <th>Ignored Attempts</th>
                <th>Yet to Attempt</th>
                <th>Started Attempt</th>
                <th>Total</th>
              </tr>
              $overallTable
            </table>


            teamWise Reports:
            <table>
              <tr>
                <th>Email</th>
                <th>Company</th>
                <th>Org Name</th>
                <th>Team Name</th>
                <th>CRM Type</th>
                <th>Success</th>
                <th>Failed</th>
                <th>Total</th>
              </tr>
              $teamWiseTable
            </table>
       """
  }
}
