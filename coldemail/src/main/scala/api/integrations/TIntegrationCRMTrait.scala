package api.integrations


import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.ActorMaterializer
import api.accounts.{AccountDAO, TeamId}
import api.prospects.{ProspectCreateFormData, ProspectService, ProspectSource}
import api.AppConfig
import api.columns.ProspectColumnDef
import api.integrations.services.CrmLeadStatusUpdate
import api.sr_audit_logs.models.ProspectObjectWithOldProspectDeduplicationColumn
import api.triggers.*
import api.triggers.dao_service.TriggerDAOService
import eventframework.ProspectObject
import org.joda.time.DateTime
import play.api.Logging
import play.api.libs.json.*
import play.api.libs.json.JodaReads.*
import play.api.libs.json.JodaWrites.*
import play.api.libs.ws.{WSClient, WSRequest, WSResponse}
import utils.input_validations.LinkedInUrlValidation
import utils.linkedin.LinkedinHelperFunctions
import utils.{FutureUtils, SRLogger}
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

case class CRMIntegrations(
                            hubspot: Boolean,
                            pipedrive: Boolean,
                            salesforce: Boolean,
                            zoho: Boolean,
                            zoho_recruit: Boolean,
                            calendly: Boolean
                          )

object CRMIntegrations {

  implicit val writes: OWrites[CRMIntegrations] = Json.writes[CRMIntegrations]

}

case class TPInvalidGrantException(message: String = "", cause: Throwable = None.orNull)
  extends Exception(message, cause)



//case class IntegrationTPAccessTokenResponse(
//                                    access_token: String,
//                                    refresh_token: Option[String],
//                                    expires_in: Option[Int],
//                                    expires_at: Option[DateTime],
//                                    token_type: Option[String],
//                                    api_domain: Option[String] //this is only for zoho case bcz zoho has multi DC API's URLS with  (.eu, .au., .in, .com)
//                                  )
//
//object IntegrationTPAccessTokenResponse {
//  given formats = Json.format[IntegrationTPAccessTokenResponse]
//}

sealed trait IntegrationTPAccessTokenResponse

object IntegrationTPAccessTokenResponse {

  case class FullTokenData(
                            access_token: String,
                            refresh_token: Option[String],
                            expires_in: Option[Int],
                            expires_at: Option[DateTime],
                            token_type: Option[String],
                            api_domain: Option[String], //this is only for zoho case bcz zoho has multi DC API's URLS with  (.eu, .au., .in, .com)
                            is_sandbox: Option[Boolean]
                          ) extends IntegrationTPAccessTokenResponse

  object FullTokenData {
    given formats: OFormat[FullTokenData] = Json.format[FullTokenData]
  }


  case class RefreshTokenData(
                               refresh_token: Option[String],
                               expires_in: Option[Int],
                               expires_at: Option[DateTime],
                               token_type: Option[String],
                               api_domain: Option[String], //this is only for zoho case bcz zoho has multi DC API's URLS with  (.eu, .au., .in, .com)
                               is_sandbox: Option[Boolean]
                             ) extends IntegrationTPAccessTokenResponse

  object RefreshTokenData {
    given formats: OFormat[RefreshTokenData] = Json.format[RefreshTokenData]
  }


  implicit def writes: Writes[IntegrationTPAccessTokenResponse] = new Writes[IntegrationTPAccessTokenResponse] {
    def writes(ev: IntegrationTPAccessTokenResponse): JsValue = {

      ev match {
        case data: IntegrationTPAccessTokenResponse.FullTokenData => Json.toJson(data)

        case data: IntegrationTPAccessTokenResponse.RefreshTokenData => Json.toJson(data)


      }

    }
  }
}

case class IntegrationContactResponse(
                                       id: String,
                                       email: Option[String],
                                       status: Option[String],
                                       phone: Option[String] = None,
                                       linkedin_url: Option[String] = None,
                                       owner_id: Option[String] = None
                                     )


case class SampleContactData(
                              columns: Seq[IntegrationTPColumns],
                              res: JsValue
                            )

object IntegrationContactResponse {
  implicit val reads: Reads[IntegrationContactResponse] = Json.reads[IntegrationContactResponse]
}


/*this is for field_options will there for only dropdown columns (picklist) */
case class IntegrationTPDropdownColumnOptions(
                                               label: String,
                                               text_id: String
                                             )

object IntegrationTPDropdownColumnOptions {
  given format: OFormat[IntegrationTPDropdownColumnOptions] = Json.format[IntegrationTPDropdownColumnOptions]
}

case class IntegrationTPColumns(
                                 col_id: String,
                                 label: String,
                                 field_type: String,
                                 description: Option[String],
                                 /*field_options will there for only dropdown columns (picklist) */
                                 field_options: Option[Seq[IntegrationTPDropdownColumnOptions]] = None
                               )

object IntegrationTPColumns {
  given format: OFormat[IntegrationTPColumns] = Json.format[IntegrationTPColumns]
}

case class IntegrationTPFilters(
                                 id: String,
                                 name: String,
                                 display_name: String
                               )

object IntegrationTPFilters {
  given format: OFormat[IntegrationTPFilters] = Json.format[IntegrationTPFilters]
}


case class IntegrationTPUsersList(
                                   id: String,
                                   email: String
                                 )

object IntegrationTPUsersList {
  given format: OFormat[IntegrationTPUsersList] = Json.format[IntegrationTPUsersList]
}

case class IntegrationTPUser(
                              id: String,
                              email: String,
                              company_id: Option[String],
                              owner_id: Option[String]
                            )

object IntegrationTPUser {
  given format: OFormat[IntegrationTPUser] = Json.format[IntegrationTPUser]
}

case class BatchContactResponse(
                                 email: Option[String],
                                 phone: Option[String],
                                 linkedinUrl: Option[String],
                                 error: Option[BatchContactResponseTypes],
                                 contactId: Option[String] = None
                               )

sealed abstract class CommonCRMAPIErrors {
  def message: String
}

object CommonCRMAPIErrors {
  private val KEY_InternalServerError = "InternalServerError"
  private val KEY_NotFoundError = "NotFoundError"
  private val KEY_TooManyRequestsError = "TooManyRequestsError"
  private val KEY_UnAuthorizedError = "UnAuthorizedError"
  private val KEY_UnknownError = "UnknownError"
  private val KEY_UnknownErrorWithResponseBody = "UnknownErrorWithResponseBody"
  private val KEY_ConvertedLead = "ConvertedLead"
  private val KEY_ContactCreationDelayInCRM = "ContactCreationDelayInCRM"

  case class InternalServerError(msg: String) extends CommonCRMAPIErrors {
    override def message: String = s"$KEY_InternalServerError." + msg
  }

  case class NotFoundError(msg: String) extends CommonCRMAPIErrors {
    override def message: String = s"$KEY_NotFoundError." + msg
  }

  case class TooManyRequestsError(msg: String, nextAttemptAt: Option[DateTime] = None) extends CommonCRMAPIErrors {
    override def message: String = s"$KEY_TooManyRequestsError." + msg
  }


  case class ConvertedLead(msg: String) extends CommonCRMAPIErrors {
    override def message: String = s"$KEY_ConvertedLead." + msg
  }


  case class UnAuthorizedError(msg: String) extends CommonCRMAPIErrors {
    override def message: String = s"$KEY_UnAuthorizedError." + msg
  }

  case class UnknownError(msg: String) extends CommonCRMAPIErrors {
    override def message: String = s"$KEY_UnknownError." + msg
  }

  case class UnknownErrorWithResponseBody(msg: String, responseBody: String, responseCode: Int) extends CommonCRMAPIErrors {
    override def message: String = s"$KEY_UnknownErrorWithResponseBody." + msg + ":: responseBody: " + responseBody + ":: responseCode: " + responseCode
  }

  case class ContactCreationDelayInCRM(msg: String) extends CommonCRMAPIErrors {
    override def message: String = s"$KEY_ContactCreationDelayInCRM." + msg

  }

}


sealed abstract class OauthErrorTypes {
  def message: String
}

object OauthErrorTypes {
  private val KEY_InvalidClientError = "InvalidClientError"
  private val KEY_ForbiddenError = "ForbiddenError"
  private val KEY_InvalidRequestError = "InvalidRequestError"
  private val KEY_UnauthorizedClientError = "UnauthorizedClientError"
  private val KEY_AccessDeniedError = "AccessDeniedError"
  private val KEY_InvalidScopeError = "InvalidScopeError"
  private val KEY_InvalidGrantError = "InvalidGrantError"
  private val KEY_InvalidTokenError = "InvalidTokenError"
  private val KEY_NotFoundError = "NotFoundError"
  private val KEY_UnknownError = "UnknownError"
  private val KEY_MalformedOauthResponseError = "MalformedOauthResponseError"

  case class InvalidClientError(msg: String) extends OauthErrorTypes {
    override def message: String = s"$KEY_InvalidClientError." + msg
  }

  case class ForbiddenError(msg: String) extends OauthErrorTypes {
    override def message: String = s"$KEY_ForbiddenError." + msg
  }

  case class InvalidRequestError(msg: String) extends OauthErrorTypes {
    override def message: String = s"$KEY_InvalidRequestError." + msg
  }

  case class UnauthorizedClientError(msg: String) extends OauthErrorTypes {
    override def message: String = s"$KEY_UnauthorizedClientError." + msg
  }

  case class AccessDeniedError(msg: String) extends OauthErrorTypes {
    override def message: String = s"$KEY_AccessDeniedError." + msg
  }

  case class InvalidScopeError(msg: String) extends OauthErrorTypes {
    override def message: String = s"$KEY_InvalidScopeError." + msg
  }

  case class InvalidGrantError(msg: String) extends OauthErrorTypes {
    override def message: String = s"$KEY_InvalidGrantError." + msg
  }

  case class InvalidTokenError(msg: String) extends OauthErrorTypes {
    override def message: String = s"$KEY_InvalidTokenError." + msg
  }

  case class NotFoundError(msg: String) extends OauthErrorTypes {
    override def message: String = s"$KEY_NotFoundError." + msg
  }

  case class UnknownError(msg: String) extends OauthErrorTypes {
    override def message: String = s"$KEY_UnknownError." + msg
  }

  case class MalformedOauthResponseError(msg: String) extends OauthErrorTypes {
    override def message: String = s"$KEY_MalformedOauthResponseError." + msg
  }
}

sealed abstract class UpdateLeadStatusError {
  def message: String
}

object UpdateLeadStatusError {

  case class CommonCRMAPIError(err: CommonCRMAPIErrors) extends UpdateLeadStatusError {
    override def message = "CommonCRMAPIError." + err.message
  }

}

sealed abstract class CreateOrUpdateBatchContactsError {
  def message: String
}

object CreateOrUpdateBatchContactsError {

  case class CommonCRMAPIError(err: CommonCRMAPIErrors) extends CreateOrUpdateBatchContactsError {
    override def message = err.message
  }


  case class HubspotSearchFailureError(message:String , failureMessages: Seq[BatchContactResponseTypes]) extends CreateOrUpdateBatchContactsError
  case class InvalidModuleError(message: String) extends CreateOrUpdateBatchContactsError

  case class HubspotFailureBatchError(message: String, failureRecords: Seq[BatchContactResponse]) extends CreateOrUpdateBatchContactsError

  case class SalesForceFailureBatchError(message: String, failureRecords: Seq[BatchContactResponse]) extends CreateOrUpdateBatchContactsError

  case class CRMFindOneByEmailAndModuleError(err: FindOneByEmailAndModuleError) extends CreateOrUpdateBatchContactsError {
    override def message = err.message
  }
}

sealed abstract class SearchBatchContactsError {
  def message: String
}

object SearchBatchContactsError {

  case class CommonCRMAPIError(err: CommonCRMAPIErrors) extends SearchBatchContactsError {
    override def message = err.message
  }

  case class InvalidModuleError(message: String) extends SearchBatchContactsError

  case class HubspotFailureBatchError(message: String, failureRecords: Seq[BatchContactResponse]) extends SearchBatchContactsError

  case class HubspotSearchFailureError(message: String,failureMessages: Seq[BatchContactResponseTypes]) extends SearchBatchContactsError

  case class SalesForceBatchError(message: String, failureRecords: Seq[BatchContactResponse]) extends SearchBatchContactsError

  case class CRMFindOneByEmailAndModuleError(err: FindOneByEmailAndModuleError) extends SearchBatchContactsError {
    override def message = err.message
  }
}


sealed abstract class UpdateActivityError {
  def message: String
}

object UpdateActivityError {

  case class FetchTokensFromDBAndRefreshAccessTokenInServiceError(err: FetchTokensFromDBAndRefreshAccessTokenError) extends UpdateActivityError {
    override def message = err.toString
  }

  case class CommonCRMAPIError(err: CommonCRMAPIErrors) extends UpdateActivityError {
    override def message = err.message
  }

  case class UnableToLockRow(message: String) extends UpdateActivityError

}

sealed abstract class CreateOrUpdateSingleContactsError {
  def message: String
}

object CreateOrUpdateSingleContactsError {

  private val KEY_CommonCRMAPIError = "CommonCRMAPIError"
  private val KEY_MalformedResponseStructureError = "MalformedResponseStructureError"
  private val KEY_UnableToFindPersonIdError = "UnableToFindPersonIdError"
  private val KEY_InvalidModuleError = "InvalidModuleError"
  private val KEY_CRMFindOneByEmailAndModuleError = "CRMFindOneByEmailAndModuleError"
  private val KEY_ContactCreationDelayInCRM = "ContactCreationDelayInCRM"

  case class SQLException(message: String) extends CreateOrUpdateSingleContactsError

  case class CommonCRMAPIError(err: CommonCRMAPIErrors) extends CreateOrUpdateSingleContactsError {
    override def message = s"$KEY_CommonCRMAPIError." + err.message
  }

  case class MalformedResponseStructureError(msg: String) extends CreateOrUpdateSingleContactsError {
    override def message = s"$KEY_MalformedResponseStructureError." + msg
  }

  case class UnableToFindPersonIdError(msg: String) extends CreateOrUpdateSingleContactsError {
    override def message = s"$KEY_UnableToFindPersonIdError." + msg

  }


  case class ContactCreationDelayInCRM(msg: String) extends CreateOrUpdateSingleContactsError {
    override def message = s"$KEY_ContactCreationDelayInCRM." + msg

  }

  case class InvalidModuleError(msg: String) extends CreateOrUpdateSingleContactsError {
    override def message = s"$KEY_InvalidModuleError." + msg

  }

  case class CRMFindOneByEmailAndModuleError(err: FindOneByEmailAndModuleError) extends CreateOrUpdateSingleContactsError {
    override def message = s"$KEY_CRMFindOneByEmailAndModuleError." + err.message
  }


}

sealed abstract class FindOneByEmailAndModuleError {
  def message: String
}

object FindOneByEmailAndModuleError {

  val KEY_CommonCRMAPIError = "CommonCRMAPIError"
  val KEY_MalformedResponseStructureError = "MalformedResponseStructureError"
  val KEY_InvalidModuleError = "InvalidModuleError"

  case class CommonCRMAPIError(err: CommonCRMAPIErrors) extends FindOneByEmailAndModuleError {
    override def message = s"$KEY_CommonCRMAPIError." + err.message
  }

  case class MalformedResponseStructureError(msg: String) extends FindOneByEmailAndModuleError {
    override def message = s"$KEY_MalformedResponseStructureError." + msg

  }

  case class InvalidModuleError(msg: String) extends FindOneByEmailAndModuleError {
    override def message = s"$KEY_InvalidModuleError." + msg

  }

}


sealed abstract class FindBatchError {
  def message: String
}

object FindBatchError {

  val KEY_CommonCRMAPIError = "CommonCRMAPIError"
  val KEY_MalformedResponseStructureError = "MalformedResponseStructureError"
  val KEY_InvalidModuleError = "InvalidModuleError"

  case class CommonCRMAPIError(err: CommonCRMAPIErrors) extends FindBatchError {
    override def message = s"$KEY_CommonCRMAPIError." + err.message
  }

  case class MalformedResponseStructureError(msg: String) extends FindBatchError {
    override def message = s"$KEY_MalformedResponseStructureError." + msg

  }

  case class InvalidModuleError(msg: String) extends FindBatchError {
    override def message = s"$KEY_InvalidModuleError." + msg

  }

}


sealed abstract class FetchRecentContactsError {
  def message: String
}

object FetchRecentContactsError {

  case class CommonCRMAPIError(err: CommonCRMAPIErrors) extends FetchRecentContactsError {
    override def message = err.message
  }

}

sealed abstract class UninstallError {
  def message: String
}

object UninstallError {

  case class OAuthError(err: OauthErrorTypes) extends UninstallError {
    override def message = err.message
  }

}


sealed abstract class GetCRMFiltersError {
  def message: String
}

object GetCRMFiltersError {

  case class CommonCRMAPIError(err: CommonCRMAPIErrors) extends GetCRMFiltersError {
    override def message = err.message
  }

  case class UpperBoundHitError(message: String) extends GetCRMFiltersError
}

sealed abstract class BatchContactResponseTypes {
  def message: String
}

object BatchContactResponseTypes {

  case class InvalidDataError(message: String, errorObj: String) extends BatchContactResponseTypes

  case class InvalidEmailError(message: String, errorObj: String) extends BatchContactResponseTypes

  case class InvalidPropertyError(message: String, errorObj: String) extends BatchContactResponseTypes

  case class UnknownError(message: String, errorObj: String) extends BatchContactResponseTypes


  case class InternalServerError(message: String) extends BatchContactResponseTypes

  case class NotFoundError(message: String) extends BatchContactResponseTypes

  case class UnAuthorizedError(message: String) extends BatchContactResponseTypes

  case class TooManyRequestsError(message: String, nextAttemptAt: Option[DateTime] = None) extends BatchContactResponseTypes

  case class UnknownErrorWithResponseBody(message: String, errorObj: String) extends BatchContactResponseTypes

}


sealed abstract class GetCRMUsersError {
  def message: String
}

object GetCRMUsersError {

  private val KEY_MalformedUsersResponseError = "MalformedUsersResponseError"
  private val KEY_CommonCRMAPIError = "CommonCRMAPIError"

  case class MalformedUsersResponseError(msg: String) extends GetCRMUsersError {
    override def message: String = s"$KEY_MalformedUsersResponseError." + msg
  }

  case class CommonCRMAPIError(err: CommonCRMAPIErrors) extends GetCRMUsersError {
    override def message = s"$KEY_CommonCRMAPIError." + err.message
  }


}

sealed abstract class GetLeadStatusColumnsError {
  def message: String
}

object GetLeadStatusColumnsError {

  case class InvalidModuleError(message: String) extends GetLeadStatusColumnsError

  case class MalformedResponseStructureError(message: String) extends GetLeadStatusColumnsError

  case class CommonCRMAPIError(err: CommonCRMAPIErrors) extends GetLeadStatusColumnsError {
    override def message = err.message
  }

  case class InvalidGrantException(message: String) extends GetLeadStatusColumnsError

  case class LeadStatusColumnsFetchError(message: String) extends GetLeadStatusColumnsError

}

sealed abstract class GetAccessTokenError {
  def message: String
}

object GetAccessTokenError {

  case class MalformedAccessTokenResponseError(message: String) extends GetAccessTokenError

  case class OAuthError(err: OauthErrorTypes) extends GetAccessTokenError {
    override def message = err.message
  }

}

sealed abstract class RefreshAccessTokenError {
  def message: String
}

object RefreshAccessTokenError {

  private val KEY_MalformedRefreshAccessTokenResponseError = "MalformedRefreshAccessTokenResponseError"
  private val KEY_OAuthError = "OAuthError"

  case class MalformedRefreshAccessTokenResponseError(msg: String) extends RefreshAccessTokenError {
    override def message: String = s"$KEY_MalformedRefreshAccessTokenResponseError." + msg
  }

  case class OAuthError(err: OauthErrorTypes) extends RefreshAccessTokenError {
    override def message = s"$KEY_OAuthError." + err.message
  }
}


sealed abstract class FetchTokensFromDBAndRefreshAccessTokenError {
  def message: String
}

object FetchTokensFromDBAndRefreshAccessTokenError {
  private val KEY_SQLException = "SQLException"
  private val KEY_TokensNotFoundInDBError = "TokensNotFoundInDBError"
  private val KEY_InvalidRefreshTokenError = "InvalidRefreshTokenError"
  private val KEY_RefreshAccessTokenAPIError = "RefreshAccessTokenAPIError"

  case class SQLException(error: Throwable) extends FetchTokensFromDBAndRefreshAccessTokenError {
    override def message = s"$KEY_SQLException." + Option(error.getMessage).getOrElse("No Message")
  }

  case class TokensNotFoundInDBError(msg: String) extends FetchTokensFromDBAndRefreshAccessTokenError {
    override def message: String = s"$KEY_TokensNotFoundInDBError." + msg
  }

  case class InvalidRefreshTokenError(msg: String) extends FetchTokensFromDBAndRefreshAccessTokenError {
    override def message: String = s"$KEY_InvalidRefreshTokenError." + msg
  }

  case class RefreshAccessTokenAPIError(err: RefreshAccessTokenError) extends FetchTokensFromDBAndRefreshAccessTokenError {
    override def message = s"$KEY_RefreshAccessTokenAPIError." + err.message
  }

}

trait TIntegrationCRMTrait extends Logging {

  type CrmData <: CrmSpecificData // abstract type member https://docs.scala-lang.org/tour/abstract-type-members.html

  val prospectSource: ProspectSource.Value

  val name: IntegrationType

  def batchCreateOrUpdateLimit: Int

  val triggerDAO: Trigger
  val triggerServiceV2: TriggerServiceV2
  val triggerDAOService: TriggerDAOService

  private final val maxRetriesOnServerError: Int = 2

  protected final def isSuccessResponse(response: WSResponse): Boolean = response.status >= 200 && response.status < 300

  protected final def isServerErrorResponse(response: WSResponse): Boolean = response.status >= 500 && response.status < 600

  protected final def isUnableToLockRowResponse(response: WSResponse): Boolean = { //FIXME: NOT USED

    val errorCode = (response.json \\ "errorCode").headOption.flatMap(_.asOpt[String])

    val isUnableToLockRowResponse = if (response.status == 400 && errorCode.isDefined && errorCode.get == "UNABLE_TO_LOCK_ROW") true else false

    isUnableToLockRowResponse

  }

  /*
  final def shouldRetry(res: WSResponse, retryAttempt: Int): Boolean = {
    isServerErrorResponse(res) && retryAttempt < maxRetriesOnServerError
  }
  */

  protected final def retryOnServerError(

                                          f: => Future[WSResponse],
                                          delay: FiniteDuration = 3.second

                                        )(implicit ec: ExecutionContext, system: ActorSystem,
                                          Logger: SRLogger
                                        ): Future[WSResponse] = {

    def f1: Future[WSResponse] = {
      Logger.info("retryOnServerError f1: request called")

      f.map(response => {

        if (isServerErrorResponse(response)) {
          Logger.error(s"retryOnServerError f1: ERROR response called: ${response.status} res body: ${response.body}")

          throw new Exception(s"$name server error")
        } else {
          Logger.info(s"retryOnServerError f1: request success: status: ${response.status}")

          response
        }
      })
    }

    FutureUtils.retry(
      f = f1,
      delay = delay,
      retries = maxRetriesOnServerError
    )(ec = ec, s = system.scheduler, Logger = Logger)

  }


  def authorizationUrl(state: String, is_sandbox: Boolean)(implicit ws: WSClient, ec: ExecutionContext): String


  def refreshAccessToken(refreshTokenData: IntegrationTPAccessTokenResponse.RefreshTokenData)
                        (implicit ws: WSClient,
                         ec: ExecutionContext,
                         Logger: SRLogger
                        ): Future[Either[RefreshAccessTokenError, IntegrationTPAccessTokenResponse.FullTokenData]]

  def getAccessToken(code: String, location: Option[String], is_sandbox: Boolean)
                    (implicit ws: WSClient,
                     ec: ExecutionContext,
                     Logger: SRLogger
                    ): Future[Either[GetAccessTokenError, IntegrationTPAccessTokenResponse.FullTokenData]]

  def me(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData)(implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger): Future[IntegrationTPUser]

  def getTPColumns(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData, module_type: Option[String])(implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger): Future[Seq[IntegrationTPColumns]]

  def getLeadStatusColumns(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData, moduleType: IntegrationModuleType)
                          (implicit ws: WSClient,
                           ec: ExecutionContext,
                           Logger: SRLogger
                          ): Future[Either[GetLeadStatusColumnsError, Seq[IntegrationTPColumns]]]


  def getTPFilters(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData, module_type: Option[String])
                  (implicit ws: WSClient,
                   ec: ExecutionContext,
                   Logger: SRLogger
                  ): Future[Either[GetCRMFiltersError, Seq[IntegrationTPFilters]]]


  def getTPUsers(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData)
                (implicit ws: WSClient,
                 ec: ExecutionContext,
                 Logger: SRLogger
                ): Future[Either[GetCRMUsersError, Seq[IntegrationTPUsersList]]]

  def fetchTokensFromDB(teamId: Long, integration_type: IntegrationType)(using Logger: SRLogger): Try[Option[IntegrationTPAccessTokenResponse]] = {
    triggerDAOService.findTokensByIntegration(teamId = teamId, integration_type = integration_type)
  }

  private def refreshTokenAndUpdateInDatastore(

                                                data: IntegrationTPAccessTokenResponse.RefreshTokenData,

                                                teamId: Long,

                                                integration_type: IntegrationType,

                                              )(
                                                implicit Logger: SRLogger,
                                                ec: ExecutionContext,
                                                ws: WSClient
                                              ): Future[Either[FetchTokensFromDBAndRefreshAccessTokenError, IntegrationTPAccessTokenResponse.FullTokenData]] = {

    refreshAccessToken(refreshTokenData = data)(Logger = Logger.appendLogRequestId("refreshAccessToken"), ws = ws, ec = ec)
      .map {

        case Left(err) =>
          Left(FetchTokensFromDBAndRefreshAccessTokenError.RefreshAccessTokenAPIError(err = err))

        case Right(accessTokenResponse) =>

          triggerDAOService.updateTPAccessToken(
            teamId = teamId,
            accessTokenData = accessTokenResponse,
            integration_type = integration_type
          ) match {

            case Failure(e) =>

              Logger.error(s"FATAL $integration_type refreshAccessToken updateTPAccessToken error ::: $e")
              Left(FetchTokensFromDBAndRefreshAccessTokenError.SQLException(error = e))

            case Success(None) =>

              Logger.error(s"$integration_type Could not find integration while trying to update access token::  tid_$teamId")
              Left(FetchTokensFromDBAndRefreshAccessTokenError.TokensNotFoundInDBError(msg = "Could not find integration while trying to update access token"))

            case Success(Some(updatedAccessTokenData)) =>
              Right(updatedAccessTokenData)


          }

      }

  }

  def fetchTokensFromDBAndRefreshAccessToken(
                                              teamId: Long,
                                              integration_type: IntegrationType
                                            )(
                                              implicit Logger: SRLogger,
                                              ec: ExecutionContext,
                                              ws: WSClient
                                            ): Future[Either[FetchTokensFromDBAndRefreshAccessTokenError, IntegrationTPAccessTokenResponse.FullTokenData]] = {

    fetchTokensFromDB(teamId = teamId, integration_type = integration_type) match {
      case Failure(e) =>

        Logger.fatal(msg = "TIntegrationCRMTrait.fetchTokensFromDBAndRefreshAccessToken", err = e)

        Future.successful(
          Left(FetchTokensFromDBAndRefreshAccessTokenError.SQLException(error = e))
        )

      case Success(None) =>

        Logger.fatal(s"TIntegrationCRMTrait.fetchTokensFromDBAndRefreshAccessToken: Tokens not found.")

        Future.successful(
          Left(
            FetchTokensFromDBAndRefreshAccessTokenError.TokensNotFoundInDBError(
              msg = "SmartReach does not have access to your CRM account. Please reconnect your CRM in SmartReach, or contact support"
            )
          )
        )
      case Success(Some(accessTokenData)) =>

        accessTokenData match {
          case data: IntegrationTPAccessTokenResponse.FullTokenData =>

            val tokenInDataStoreHasExpired: Boolean = {
              data.expires_at.isDefined &&
                DateTime.now().isAfter(data.expires_at.get.minusMinutes(1))
            }

            if (tokenInDataStoreHasExpired) {


              Logger.debug("[calling refreshTokenAndUpdateInDatastore] tokenInDataStoreHasExpired going to refresh access token")

              refreshTokenAndUpdateInDatastore(

                data = IntegrationTPAccessTokenResponse.RefreshTokenData(

                  refresh_token = data.refresh_token,
                  expires_in = data.expires_in,
                  expires_at = data.expires_at,
                  token_type = data.token_type,
                  api_domain = data.api_domain,
                  is_sandbox = data.is_sandbox
                ),

                integration_type = integration_type,

                teamId = teamId

              )


            } else {
              Logger.debug(s"got accessToken from the db")
              Future.successful(
                Right(data)
              )

            }


          case data: IntegrationTPAccessTokenResponse.RefreshTokenData =>
            if (data.refresh_token.isEmpty) {

              Logger.fatal(s"TIntegrationCRMTrait.fetchTokensFromDBAndRefreshAccessToken: Tokens not found.")
              Future.successful(
                Left(FetchTokensFromDBAndRefreshAccessTokenError.TokensNotFoundInDBError(msg = "Could not find integration while trying to adding error"))
              )

            } else {

              Logger.debug("[calling refreshTokenAndUpdateInDatastore] got RefreshTokenData, going to refresh access token")


              refreshTokenAndUpdateInDatastore(
                data = data,
                integration_type = integration_type,
                teamId = teamId
              )

            }
        }
    }
  }

  def createOrUpdateBatchContacts(
                                   moduleType: IntegrationModuleType,
                                   accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                   contacts: Seq[ProspectObjectWithOldProspectDeduplicationColumn],
                                   fieldMapping: UpdateFieldsMappingForm,
                                   userMapping: UpdateUserMappingForm,
                                   accountId: Long,
                                   teamId: Long,
//                                   emailNotCompulsoryEnabled: Boolean
                                 )(implicit ws: WSClient,
                                   ec: ExecutionContext,
                                   system: ActorSystem,
                                   Logger: SRLogger
                                 ): Future[Seq[Either[CreateOrUpdateBatchContactsError, BatchContactResponse]]]

  def createOrUpdateSingleContact(
                                   accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                   moduleType: IntegrationModuleType,
                                   prospect: ProspectObject,
                                   fieldMapping: UpdateFieldsMappingForm,
                                   userMapping: UpdateUserMappingForm,
                                   accountId: Long,
                                   teamId: Long,
                                   email: String,
//                                   emailNotCompulsoryEnabled: Boolean
                                 )(implicit ws: WSClient,
                                   ec: ExecutionContext,
                                   system: ActorSystem,
                                   Logger: SRLogger): Future[Either[CreateOrUpdateSingleContactsError, String]]

  /*
  def createOrUpdateBatchLeads(accessTokenData: IntegrationTPAccessTokenResponse,
                               leads: Seq[Prospect],
                               fieldMapping: UpdateFieldsMappingForm,
                               accountId: Long,
                               teamId: Long
  )(implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger): Future[Option[Int]]
  */

  //  def getContactByEmail(accessTokenData: IntegrationTPAccessTokenResponse, email: String)(implicit ws: WSClient, ec: ExecutionContext): Future[Option[IntegrationContactResponse]]

  def findOneByEmailAndModule(
                               accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                               email: String,
                               module: IntegrationModuleType,
                               statusColumn: Option[String]
                             )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem,
                               Logger: SRLogger): Future[Either[FindOneByEmailAndModuleError, Option[IntegrationContactResponse]]]


  def findAllByProspectAndModule(
                                  accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                  prospect: ProspectObject,
                                  module: IntegrationModuleType,
                                  statusColumn: Option[String]
                                )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem,
                                  Logger: SRLogger): Future[Either[FindBatchError, Seq[IntegrationContactResponse]]]


  //  def buildCreateContactObj(
  //    moduleType: IntegrationModuleType,
  //    prospects: Seq[ProspectObject],
  //    fieldMapping: UpdateFieldsMappingForm,
  //    userMapping: UpdateUserMappingForm,
  //    accountId: Long,
  //    teamId: Long
  //  )(using Logger: SRLogger): JsValue

  /*
  def buildCreateLeadsObj(prospects: Seq[Prospect], fieldMapping: UpdateFieldsMappingForm, accountId: Long, teamId: Long
  )(using Logger: SRLogger): JsValue
  */

  def Uninstall(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData, teamId: Long)
               (implicit ws: WSClient,
                ec: ExecutionContext,
                Logger: SRLogger
               ): Future[Either[UninstallError, Int]]


  protected def updateActivityInCRM(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                    person_id: String,
                                    activitySubject: String,
                                    activityType: TPActivityType.Value,
                                    detailedActivitySubject: String,
                                    activityAt: DateTime,
                                    emailSubject: Option[String],
                                    emailSenderAddress: Option[String],
                                    emailSenderFirstName: Option[String],
                                    emailSenderLastName: Option[String],
                                    emailBody: Option[String],
                                    emailTextBody: Option[String],
                                    module: IntegrationModuleType,
                                    owner_id: Option[String]
                                   )(implicit ws: WSClient,
                                     ec: ExecutionContext,
                                     Logger: SRLogger
                                   ): Future[Either[UpdateActivityError, String]]


  def updateLeadStatus(
                        accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                        moduleType: IntegrationModuleType,
                        person: CrmLeadStatusUpdate
                      )(implicit ws: WSClient,
                        ec: ExecutionContext,
                        Logger: SRLogger
                      ): Future[Either[UpdateLeadStatusError, String]]

  /*
  * removing retry logic sitting inside this method (updateActivity)
  * We decided to move retry logic into middleware, That will come after ERROR handling for all CRM endpoints
  * */
  def updateActivity(
                      person_id: String,
                      activitySubject: String,
                      activityType: TPActivityType.Value,
                      detailedActivitySubject: String,
                      activityAt: DateTime,
                      emailSubject: Option[String],
                      emailSenderAddress: Option[String],
                      emailSenderFirstName: Option[String],
                      emailSenderLastName: Option[String],
                      emailBody: Option[String],
                      emailTextBody: Option[String],
                      module: IntegrationModuleType,
                      teamId: Long,
                      owner_id: Option[String]
                    )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem,
                      Logger: SRLogger
                    ): Future[Either[UpdateActivityError, String]] = {
    fetchTokensFromDBAndRefreshAccessToken(
      teamId = teamId,
      integration_type = name
    ).flatMap {
      case Left(e) => Future.successful(Left(UpdateActivityError.FetchTokensFromDBAndRefreshAccessTokenInServiceError(e)))

      case Right(accessTokenData) =>

        updateActivityInCRM(
          accessTokenData = accessTokenData,
          activitySubject = activitySubject,
          activityType = activityType,
          detailedActivitySubject = detailedActivitySubject,
          activityAt = activityAt,
          emailSubject = emailSubject,
          emailSenderAddress = emailSenderAddress,
          emailSenderFirstName = emailSenderFirstName,
          emailSenderLastName = emailSenderLastName,
          emailBody = emailBody,
          emailTextBody = emailTextBody,

          person_id = person_id,
          module = module,
          owner_id = owner_id
        )(Logger = Logger.appendLogRequestId("updateActivityInCRM"), ws = ws, ec = ec)
    }
  }

  protected def hasMoreContactsToSync(dataResponse: WSResponse, moduleType: IntegrationModuleType): Boolean

  protected def findLeastaddedAtToSync(data: Seq[JsValue])(using Logger: SRLogger): Long

  protected def findLatestaddedAtToSync(data: Seq[JsValue])(using Logger: SRLogger): Long


  protected def fetchRecentContactsFromCRM(
                                            accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                            page: Long,
                                            moduleType: IntegrationModuleType,

                                            forSampleData: Boolean,
                                            crmSpecificData: CrmData

                                          )(implicit ws: WSClient,
                                            ec: ExecutionContext,
                                            system: ActorSystem,
                                            Logger: SRLogger
                                          ): Future[Either[FetchRecentContactsError, WSResponse]]

  /*
  protected def fetchRecentLeadsFromCRM(
                            accessTokenData: IntegrationTPAccessTokenResponse,
                            page: Long,
                            trigger: TriggerInDB,

                            hubspotVidOffset: Option[Long] = None,
                            hubspotTimeOffset: Option[Long] = None

                          )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem,
    Logger: SRLogger
  ): Future[WSResponse]

  protected def getFilteredLeadsFromTPResponse(responseJson: JsValue, fieldMapping: UpdateFieldsMappingForm, accessToken: String)(implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger): Future[Seq[JsValue]]
  */

  // extract the contacts from response if /contacts api
  protected def getFilteredContactsFromTPResponse(
                                                   responseJson: JsValue,
                                                   fieldMapping: UpdateFieldsMappingForm,
                                                   accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                                   moduleType: IntegrationModuleType
                                                 )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem, Logger: SRLogger): Future[Seq[JsValue]]


  protected def getFilteredContactsFromTPResponseForFrontEnd(filteredContact: JsValue)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem, Logger: SRLogger): Future[JsValue]

  protected def getValueFromTPContactObj(contact: JsValue, destField: Option[TriggerFields])(using Logger: SRLogger): Option[String]

  protected def getTpOwnerIdValueFromTPContactObj(contact: JsValue)(using Logger: SRLogger): Option[String]


  // returning the corresponding destination field, for a given source field
  protected def _getDestinationFieldName(sr_field: String, fieldMaping: UpdateFieldsMappingForm): Option[TriggerFields] = {

    fieldMaping.fields.get
      .filter(f => f.tp_field.trim.nonEmpty && f.sr_field.trim.nonEmpty)
      .find(f => {
        f.sr_field == sr_field
      })


  }


  def getRecentContacts(
                         handleProspectData: (HandleCRMContactDataArguments) => Try[Boolean],
                         crm_type: IntegrationType,
                         module_type: IntegrationModuleType,
                         accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                         team_id: Long,
                         owner_id: Long,
                         last_sync_at: Option[DateTime],
                         updatedLastSyncedAtTimeAfterFirstFetch: Option[DateTime],
                         srCustomFieldNames: Seq[String],
                         page: Long,

                         crmSpecificData: CrmData,
                         srRollingUpdateCoreService: SrRollingUpdateCoreService
                       )
                       (
                         implicit ws: WSClient,
                         ec: ExecutionContext,
                         system: ActorSystem,
                         Logger: SRLogger
                       ): Future[Option[DateTime]] = {

    fetchRecentContactsFromCRM(
      accessTokenData = accessTokenData,
      page = page,
      moduleType = module_type,

      forSampleData = false,
      crmSpecificData = crmSpecificData
    )(Logger = Logger.appendLogRequestId("fetchRecentContactsFromCRM"), ws = ws, ec = ec, system = system)


      .flatMap {

        case Left(_) =>

          val errMsg = s"Error from CRM while syncing prospects. (TraceId: ${Logger.logRequestId})"
          Future.failed(new Exception(errMsg))

        case Right(response) =>

          if (!AppConfig.isProd) {
            Logger.info(s"page: $page ::  getRecentContacts INFO: ${response.body}")
          }

          triggerDAO.findFieldMapping(teamId = team_id,
            integration_type = crm_type,
            module_type = module_type
          ) match {

            case Failure(e) =>

              Logger.fatal(s"getRecentContacts findFieldMapping :: error: ${response.body}", err = e)

              val errMsg = s"Error while syncing prospects. (TraceId: FM_${Logger.logRequestId})"
              Future.failed(new Exception(errMsg))


            case Success(None) =>

              Logger.fatal(s"getRecentContacts error NONE findFieldMapping :: None: ${response.body}")

              val errMsg = s"Error while syncing prospects. (TraceId: FMN_${Logger.logRequestId})"
              Future.failed(new Exception(errMsg))

            case Success(Some(fieldMapping)) =>

              findUpdatedUserMapping(
                accessTokenData = accessTokenData,
                teamId = team_id,
                userId = owner_id,
                integration_type = crm_type
              )
                .flatMap({

                  case Left(err) =>
                    /*LD_STS TODO_EitherT handle */
                    Logger.fatal(s"getRecentContacts findUpdatedUserMapping :: error: ${err.toString}")
                    val errMsg = s"Error while syncing prospects. (TraceId: FM_${Logger.logRequestId})"
                    Future.failed(new Exception(errMsg))

                  case Right(userMapping) =>

                    getFilteredContactsFromTPResponse(responseJson = response.json,
                      fieldMapping = fieldMapping,
                      accessTokenData = accessTokenData,
                      moduleType = module_type
                    )(Logger = Logger.appendLogRequestId("getFilteredContactsFromTPResponse"), ws = ws, ec = ec, system = system)
                      .flatMap(filteredContacts => {

                        if (filteredContacts.nonEmpty) {

                          val prospectsToBeCreated = filteredContacts.map(c => {

                              def getFieldValue(f: String): Option[String] = {
                                getValueFromTPContactObj(
                                  contact = c,
                                  destField = _getDestinationFieldName(
                                    sr_field = f,
                                    fieldMaping = fieldMapping
                                  )
                                )
                              }

                              var customFieldsBaseObj = Json.obj()

                              srCustomFieldNames.foreach(f => {

                                val customFieldVal = getFieldValue(f = f)

                                if (customFieldVal.nonEmpty) {

                                  customFieldsBaseObj = customFieldsBaseObj ++ Json.obj(
                                    f -> customFieldVal.get
                                  )
                                }


                              })


                              val mappedSROwnerId = if (userMapping.users.nonEmpty) {
                                val contactOwnerId = getTpOwnerIdValueFromTPContactObj(contact = c)
                                //NOTE taking first one if multiple is mapped
                                //TODO take admin insted of head
                                val mappedSrUser = if (contactOwnerId.isDefined) userMapping.users.get.find(user => user.tp_user_id == contactOwnerId.get) else None
                                if (mappedSrUser.isDefined) Some(mappedSrUser.get.sr_user_id) else None
                              } else None

                              val email = getFieldValue(f = "email")
//
//                              val emailNotCompulsoryEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//                                feature = SrRollingUpdateFeature.EmailNotCompulsory,
//                                teamId = TeamId(team_id)
//                              )

//                              if (emailNotCompulsoryEnabled || (!emailNotCompulsoryEnabled && email.isDefined && email.get.trim.nonEmpty)) {
                                if(team_id == 22382L){
                                  val liUrl = getFieldValue(f = "linkedin_url")
                                  logger.debug(s"linkedin_url here for tid = ${team_id} is ${liUrl}")
                                }

                                val phone = getFieldValue(f = "phone")
                                val validatedPhone = if(ProspectService.checkIfPhoneIsValid(phone)) phone else None

                                val linkedInUrl = getFieldValue(f = "linkedin_url")
                                val validatedLinkedinUrl =  if(ProspectService.checkIfLinkedInUrlIsValid(linkedInUrl)) linkedInUrl else None

                                Some(
                                  ProspectCreateFormData(
                                    email = if (email.isDefined && email.get.trim.nonEmpty) email else None,

                                    owner_id = mappedSROwnerId,

                                    first_name = getFieldValue(f = "first_name"),
                                    last_name = getFieldValue(f = "last_name"),
                                    custom_fields = customFieldsBaseObj,

                                    list = getFieldValue(f = "list"),
                                    company = getFieldValue(f = "company"),
                                    city = getFieldValue(f = "city"),
                                    country = getFieldValue(f = "country"),
                                    timezone = getFieldValue(f = "timezone"),

                                    state = getFieldValue(f = "state"),
                                    phone = validatedPhone,
                                    phone_2 = None,
                                    phone_3 = None,
                                    job_title = getFieldValue(f = "job_title"),
                                    linkedin_url = validatedLinkedinUrl
                                  )
                                )
//                              } else None
                            })
                            .filter(_.isDefined)
                            .map(_.get)

                          handleProspectData(HandleCRMContactDataArguments(prospectsToBeCreated = prospectsToBeCreated)) match {

                            case Failure(e) =>

                              Logger.fatal(s"Error while handleProspectData :: error: ${response.body}", err = e)

                              val errMsg = s"Error while handleProspectData. (TraceId: FM_${Logger.logRequestId})"
                              Future.failed(new Exception(errMsg))

                            case Success(_) =>

                              val leastAddedAt = findLeastaddedAtToSync(data = filteredContacts)

                              val latestAddedAt = findLatestaddedAtToSync(data = filteredContacts)

                              /**
                               * this updatedLastSyncAt only used in first pagination and passed as updatedLastSyncedAtTimeAfterFirstFetch
                               * though out all iterations to ration latestAddedAt
                               *
                               * */
                              val updatedLastSyncAt = new DateTime(latestAddedAt)

                              if (
                                hasMoreContactsToSync(response, moduleType = module_type) &&
                                  (last_sync_at.isEmpty || (last_sync_at.isDefined && leastAddedAt > last_sync_at.get.getMillis))
                              ) {

                                val newCrmSpecificData = getCRMSpecificData(
                                  response = Some(response),
                                  filteredContacts = filteredContacts,
                                  tp_filter_id = crmSpecificData.tp_filter_id
                                )

                                getRecentContacts(
                                  handleProspectData = handleProspectData,
                                  crm_type = crm_type,
                                  module_type = module_type,
                                  accessTokenData = accessTokenData,
                                  team_id = team_id,
                                  owner_id = owner_id,
                                  last_sync_at = last_sync_at,
                                  updatedLastSyncedAtTimeAfterFirstFetch = if (updatedLastSyncedAtTimeAfterFirstFetch.isDefined) updatedLastSyncedAtTimeAfterFirstFetch else Some(updatedLastSyncAt),
                                  srCustomFieldNames = srCustomFieldNames,
                                  page = page + 1,

                                  crmSpecificData = newCrmSpecificData,
                                  srRollingUpdateCoreService = srRollingUpdateCoreService
                                )

                              } else {


                                /**
                                 * It will come here
                                 * 1. when contacts/leads reached the last pagination
                                 * Some times first level only comes here bcz requested count is not there
                                 * */

                                val syncedAtTime = if (page > 0 && updatedLastSyncedAtTimeAfterFirstFetch.isDefined)
                                  updatedLastSyncedAtTimeAfterFirstFetch
                                else Some(updatedLastSyncAt)

                                Future.successful(syncedAtTime)

                              }


                          }


                        } else {


                          /**
                           * It will come here
                           * 1. when contacts/leads reached the last pagination with empty data in last pagination in that case we need to update lastSyncedAt with latest contact/lead
                           * 2. when contacts/leads empty in first pagination only in that case we don't need to update lastSyncedAt assuming it's a new workflow with empty data in CRM
                           * */

                          Logger.info(s"getRecentContacts getFilteredContactsFromTPResponse No filtered contacts found :: empty: ${response.body}")

                          val syncedAtTime = if (page > 0 && updatedLastSyncedAtTimeAfterFirstFetch.isDefined)
                            updatedLastSyncedAtTimeAfterFirstFetch
                          else last_sync_at

                          Future.successful(syncedAtTime)

                        }

                      })
                })
          }


      }

  }

  def findUpdatedUserMapping(
                              accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                              teamId: Long,
                              userId: Long,
                              integration_type: IntegrationType
                            )(implicit ws: WSClient,
                              ec: ExecutionContext,
                              Logger: SRLogger): Future[Either[FindUpdatedUserMappingError, UpdateUserMappingForm]] = {


    getTPUsers(
      accessTokenData = accessTokenData
    )(Logger = Logger.appendLogRequestId("getTPUsers"), ws = ws, ec = ec).map {
      case Left(err) => Left(FindUpdatedUserMappingError.CRMGetTpUsersError(err = err))
      case Right(tpUsers) =>


        triggerServiceV2.findUpdatedUserMapping(
          teamId = teamId,
          integration_type = integration_type,
          tpUsers = tpUsers
        ) match {

          case Failure(exception) =>

            Left(
              FindUpdatedUserMappingError.SQLException(err = exception)
            )

          case Success(updatedUserMapping) =>
            
            Right(updatedUserMapping)

        }

    }

  }

  final def getSampleContactData(
                                  integration_type: IntegrationType,
                                  module_type: Option[String],
                                  teamId: Long)
                                (implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem, Logger: SRLogger): Future[SampleContactData] = {

    val dummyTrigger = TriggerInDB(
      id = 0,
      owner_id = 0,
      owner_name = "demo",
      team_id = 0,
      campaign_id = None,
      label = "demo",
      event = None,
      event_app_type = None,
      tp_filter_id = None,
      conditions = None,
      actions = None,
      created_at = DateTime.now(),
      active = false,
      shared_with_team = false,
      error = None,
      error_at = None,
      error_retries_count = 0,
      last_ran_at = None,
      in_queue_for_sync = false,
      pushed_to_queue_for_sync_at = None
    )

    Future.fromTry(IntegrationModuleType.withName(module_type.getOrElse("contacts")))
      .flatMap { moduleType => {


        val crmSpecificData = getCRMSpecificData(
          tp_filter_id = dummyTrigger.tp_filter_id
        )
        fetchTokensFromDBAndRefreshAccessToken(
          teamId = teamId,
          integration_type = integration_type
        ) flatMap {
          case Left(e) => Future.failed(new Throwable(s"$e"))

          case Right(accessTokenData) =>

            val futureResult = fetchRecentContactsFromCRM(
              accessTokenData = accessTokenData,
              page = 0,
              moduleType = moduleType,
              forSampleData = true,
              crmSpecificData = crmSpecificData
            )(Logger = Logger.appendLogRequestId("fetchRecentContactsFromCRM"), ws = ws, ec = ec, system = system)
              .flatMap {

                case Left(e) =>

                  Logger.fatal(s"getSampleContactData error: ${e.message} ")
                  Future.successful(Json.obj())

                case Right(response) =>

                  triggerDAO.findFieldMapping(teamId = teamId, integration_type = integration_type, module_type = moduleType) match {

                    case Failure(e) =>
                      Logger.fatal(s"getSampleContactData getTPFieldsMapping: : ${teamId} :: integration_type: $integration_type :: module_type: $module_type", err = e)
                      Future.successful(Json.obj())

                    case Success(None) =>

                      Logger.fatal(s"getSampleContactData getTPFieldsMapping None : ${teamId} :: integration_type: $integration_type :: module_type: $module_type")
                      Future.successful(Json.obj())

                    case Success(Some(mapping)) =>

                      getFilteredContactsFromTPResponse(responseJson = response.json,
                        fieldMapping = mapping,
                        accessTokenData = accessTokenData,
                        moduleType = moduleType
                      )(Logger = Logger.appendLogRequestId("getFilteredContactsFromTPResponse"), ws = ws, ec = ec, system = system)
                        .flatMap(filteredContacts => {
                          if (filteredContacts.nonEmpty) {

                            getFilteredContactsFromTPResponseForFrontEnd(filteredContact = filteredContacts.head)

                          } else {

                            Logger.fatal(s"getSampleContactData getFilteredContactsFromTPResponse empty: ${response.body}")
                            Future.successful(Json.obj())

                          }
                        })
                  }

              }


            val futureColumns = getTPColumns(
              accessTokenData = accessTokenData,
              module_type = module_type
            )(Logger = Logger.appendLogRequestId("getTPColumns"), ws = ws, ec = ec)

            for {
              res <- futureResult
              columns <- futureColumns
            } yield {
              SampleContactData(
                columns = columns,
                res = res
              )
            }
        }
      }
      }
  }


  /*
  def getRecentLeads(fetchedContacts: List[ProspectCreateFormData],
                     filteredRawContacts: Seq[JsValue],
                        accessTokenData: IntegrationTPAccessTokenResponse,
                        trigger: TriggerInDB,
                        action: TriggerAction,
                        srCustomFieldNames: Seq[String],
                        page: Long,

                        hubspotVidOffset: Option[Long],
                        hubspotTimeOffset: Option[Long]

                    )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem,
    Logger: SRLogger): Future[(Option[TriggerInDB], List[ProspectCreateFormData])] = {

    val moduleType = IntegrationModuleType.LEADS

    fetchRecentContactsFromCRM(
      accessTokenData = accessTokenData,
      page = page,
      trigger = trigger,
      moduleType = moduleType,

      hubspotVidOffset = hubspotVidOffset,
      hubspotTimeOffset = hubspotTimeOffset
    )
      .flatMap(response => {

        if (response.status != 200) {

          Logger.fatal(s"getRecentContacts error: ${response.body} :: trigger_id: wf_${trigger.id}")

          Future.successful((None, List()))

        } else {

          Logger.info(s"page: $page ::  getRecentContacts INFO: ${response.body} :: trigger_id: wf_${trigger.id}")

          Trigger.findFieldMapping(teamId = trigger.team_id,
            integration_type = trigger.event_app_type.get,
            module_type = moduleType) match {

            case Failure(e) =>

              Logger.fatal(s"getRecentContacts findFieldMapping :: trigger_id: wf_${trigger.id} error: ${response.body}", err = e)


              Future.successful((None, List()))

            case Success(None) =>

              Logger.fatal(s"getRecentContacts error NONE findFieldMapping :: trigger_id: wf_${trigger.id} None: ${response.body}")

              Future.successful((None, List()))

            case Success(Some(fieldMapping)) =>

              getFilteredContactsFromTPResponse(responseJson = response.json,
                fieldMapping = fieldMapping,
                accessToken = accessTokenData.access_token,
                moduleType = moduleType
              )
                .flatMap(filteredContacts => {

                  if (filteredContacts.nonEmpty) {

                    val prospectsToBeCreated = filteredContacts.map(c => {

                      def getFieldValue(f: String): Option[String] = {
                        getValueFromTPContactObj(
                          contact = c,
                          destField = _getDestinationFieldName(
                            sr_field = f,
                            fieldMaping = fieldMapping
                          )
                        )
                      }

                      val customFieldsBaseObj = Json.obj()

                      srCustomFieldNames.foreach(f => {

                        val customFieldVal = getFieldValue(f = f)

                        if (customFieldVal.nonEmpty) {

                          customFieldsBaseObj ++ Json.obj(
                            f -> customFieldVal.get
                          )
                        }


                      })

                      val email = getFieldValue(f = "email")

                      if (email.isDefined && email.get.trim.nonEmpty) {

                        Some(
                          ProspectCreateFormData(
                            email = email.get,

                            first_name = getFieldValue(f = "first_name"),
                            last_name = getFieldValue(f = "last_name"),
                            custom_fields = customFieldsBaseObj,

                            list = getFieldValue(f = "list"),
                            company = getFieldValue(f = "company"),
                            city = getFieldValue(f = "city"),
                            country = getFieldValue(f = "country"),
                            timezone = getFieldValue(f = "timezone"),

                            state = getFieldValue(f = "state"),
                            phone = getFieldValue(f = "phone"),
                            job_title = getFieldValue(f = "job_title")
                          )
                        )
                      } else None
                    })
                      .filter(_.isDefined)
                      .map(_.get)

                    val allContctsTillNow = fetchedContacts ++ prospectsToBeCreated

                    val allFilteredContactsTillNow = filteredRawContacts ++ filteredContacts //this is using for retain latest addedAt

                    val hubspot_vid_offset = (response.json \ "vid-offset").asOpt[Long]
                    val hubspot_time_offset = (response.json \ "time-offset").asOpt[Long]

                    val last_sync_at = (action.last_sync_at)
                    val leastAddedAt = findLeastaddedAtToSync(data = filteredContacts)

                    if (hasMoreContactsToSync(response) &&
                      (last_sync_at.isEmpty || (last_sync_at.isDefined && leastAddedAt > last_sync_at.get.getMillis))) {

                      getRecentLeads(
                        fetchedContacts = allContctsTillNow,
                        filteredRawContacts = allFilteredContactsTillNow,
                        accessTokenData = accessTokenData,
                        trigger = trigger,
                        action = action,
                        srCustomFieldNames = srCustomFieldNames,
                        page = page + 1,

                        hubspotVidOffset = hubspot_vid_offset,
                        hubspotTimeOffset = hubspot_time_offset
                      )

                    } else {

                      val latestAddedAt = findLatestaddedAtToSync(data = allFilteredContactsTillNow)

                      val updatedAction = action.copy(
                        last_sync_at = Some(new DateTime(latestAddedAt)) // Some(DateTime.now()) //this should be least contact added insted now()
                      )

                      val updatedTrigger = trigger.copy(
                        actions = Some(Seq(updatedAction))
                      )

//                      Trigger.updateLastSyncAt(id = trigger.id, data = updatedTrigger)
                      Future.successful(Some(updatedTrigger), allContctsTillNow)

                    }

                  } else {

                    Logger.fatal(s"getRecentLeads :: trigger_id: wf_${trigger.id} getFilteredLeadsFromTPResponse empty: ${response.body}")

                    Future.successful((None, List()))

                  }
                })
          }


        }


      })

  }
  */


  // default here is defined for Zoho CRM
  protected def __getFieldTypeFromTpColumnFieldType(tp_column_field_type: String): String = {

    tp_column_field_type match {

      case "text" | "varchar" | "website" | "email" | "textarea" | "picklist" =>

        "text"

      case "integer" | "number" | "int" | "bigint" | "double" | "phone" | "long" | "phonenumber" =>

        "number"

      case "date" =>

        "date"
    }
  }


  protected def findMappedUserByProspectOwnerIdForCRMContactObj(owner_id: Long, userMapping: UpdateUserMappingForm) = {

    if (userMapping.users.nonEmpty) {
      val mappedUsers = userMapping.users.get
      val mappedOwner = mappedUsers.find(user => user.sr_user_id == owner_id)
      mappedOwner
    } else {
      None
    }
  }

  protected def isValidTpFilterId(tp_filter_id: Option[String]): Option[String] = {
    if (tp_filter_id.isDefined &&
      tp_filter_id.get.nonEmpty &&
      tp_filter_id.get != "-1" //"-1" check is need bcz in front-end when user selected --all-- it will be -1 so it means it is not specific filter workflow
    )
      tp_filter_id
    else
      None
  }


  /*
  * Ref Docs:
  * https://help.salesforce.com/s/articleView?id=sf.remoteaccess_oauth_flow_errors.htm&type=5
  * https://legacydocs.hubspot.com/docs/methods/oauth2/initiate-oauth-integration
  * https://pipedrive.readme.io/docs/marketplace-oauth-authorization
  * https://www.zoho.com/crm/developer/docs/api/v2/auth-request.html
  * https://www.zoho.com/recruit/developer-guide/apiv2/auth-request.html
*/
  def parseOAuthStatusCodes(response: WSResponse): OauthErrorTypes = {

    val statusCode = response.status

    statusCode match {

      case 404 =>
        OauthErrorTypes.NotFoundError(s"Not found ${Try(response.body).getOrElse("")}")
      case 403 =>
        OauthErrorTypes.ForbiddenError(s"Forbidden error ${Try(response.body).getOrElse("")}")
      case 401 =>
        OauthErrorTypes.InvalidClientError(s"Invalid client ${Try(response.body).getOrElse("")}")
      case 400 =>
        Try {
          (response.json \ "error").as[String]
        } match {

          case Failure(_) =>
            logger.error(s"CRITICAL: Malformed Oauth response received ${response.body}")

            OauthErrorTypes.MalformedOauthResponseError(s"Malformed OAuth Response ${Try(response.body).getOrElse("")}")

          case Success(error) =>

            error match {
              case "invalid_request" =>
                OauthErrorTypes.InvalidRequestError(s"Invalid request ${Try(response.body).getOrElse("")}")
              case "unauthorized_client" =>
                OauthErrorTypes.UnauthorizedClientError(s"Unauthorized client ${Try(response.body).getOrElse("")}")
              case "access_denied" =>
                OauthErrorTypes.AccessDeniedError(s"Access denied ${Try(response.body).getOrElse("")}")
              case "invalid_scope" =>
                OauthErrorTypes.InvalidScopeError(s"Invalid scope ${Try(response.body).getOrElse("")}")
              case "invalid_grant" =>
                OauthErrorTypes.InvalidGrantError(s"Invalid grant ${Try(response.body).getOrElse("")}")
              case "invalid_token" =>
                OauthErrorTypes.InvalidTokenError(s"Invalid token ${Try(response.body).getOrElse("")}")
              case _ =>
                OauthErrorTypes.UnknownError(Try {
                  response.body
                }.getOrElse(s"Unknown Error ${Try(response.body).getOrElse("")}"))
            }
        }

      case _ =>
        OauthErrorTypes.UnknownError(Try {
          response.body
        }.getOrElse("Unknown Error"))
    }
  }

  def getCRMSpecificData(
                          response: Option[WSResponse] = None,
                          filteredContacts: Seq[JsValue] = Seq(),
                          tp_filter_id: Option[String]
                        ): CrmData

}

object TIntegrationCRMTrait {
  def mapToOldAndNewProspectObjects(contacts: Seq[ProspectObjectWithOldProspectDeduplicationColumn]): Seq[OldAndNewProspectObject] = {

    contacts.map(c => {
      var modifiedProspect = c
      if (c.oldProspectDeduplicationColumn.isDefined) {
        if (c.oldProspectDeduplicationColumn.get.email.isDefined) {
          modifiedProspect = modifiedProspect.copy(prospectObject = modifiedProspect.prospectObject.copy(email = c.oldProspectDeduplicationColumn.get.email))
        }
        if (c.oldProspectDeduplicationColumn.get.phone.isDefined) {
          modifiedProspect = modifiedProspect.copy(prospectObject = modifiedProspect.prospectObject.copy(phone = c.oldProspectDeduplicationColumn.get.phone))
        }
        OldAndNewProspectObject(newProspectObject = NewProspectObject(c.prospectObject), oldProspectObject = OldProspectObject(modifiedProspect.prospectObject))
      } else {
        OldAndNewProspectObject(newProspectObject = NewProspectObject(c.prospectObject), oldProspectObject = OldProspectObject(c.prospectObject))
      }
    })
  }
}
