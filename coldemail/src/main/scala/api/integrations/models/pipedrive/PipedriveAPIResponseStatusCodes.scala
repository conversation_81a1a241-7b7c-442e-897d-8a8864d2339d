package api.integrations.models.pipedrive

import api.integrations.CommonCRMAPIErrors
import org.joda.time.DateTime
import play.api.libs.ws.WSResponse
import utils.SRLogger

import scala.util.{Failure, Success, Try}

sealed abstract class PipedriveAPIResponseStatusCodes{
  def message: String
}

/*Doc Ref: https://pipedrive.readme.io/docs/core-api-concepts-http-status-codes*/
object PipedriveAPIResponseStatusCodes {

  case class CommonCRMAPIError(error: CommonCRMAPIErrors) extends PipedriveAPIResponseStatusCodes {
    override def message: String = error.message
  }

  //501
  case class NotImplementedError(message: String) extends PipedriveAPIResponseStatusCodes

  //422
  case class UnProcessableEntityError(message: String) extends PipedriveAPIResponseStatusCodes

  //415
  case class UnsupportedMediaTypeError(message: String) extends PipedriveAPIResponseStatusCodes

  //410
  case class GoneError(message: String) extends PipedriveAPIResponseStatusCodes

  //405
  case class MethodNotAllowed(message: String) extends PipedriveAPIResponseStatusCodes

  //403
  case class Forbidden(message: String) extends PipedriveAPIResponseStatusCodes

  //402
  case class PaymentRequiredError(message: String) extends PipedriveAPIResponseStatusCodes

  //400
  case class BadRequestError(message: String) extends PipedriveAPIResponseStatusCodes

  case class UnknownError(message: String, responseBody: String, responseCode: Int) extends PipedriveAPIResponseStatusCodes

  def parseStatusCodes(response: WSResponse)(using Logger: SRLogger):PipedriveAPIResponseStatusCodes  = {



    val statusCode = response.status

    statusCode match {

      case 503 =>
        PipedriveAPIResponseStatusCodes.CommonCRMAPIError(
          error = CommonCRMAPIErrors.InternalServerError(msg = "Service Unavailable Scheduled maintenance")
        )


      case 500 => PipedriveAPIResponseStatusCodes.CommonCRMAPIError(
        error = CommonCRMAPIErrors.InternalServerError(msg = "Generic server error")
      )

      /*Pipedrive Rate Limit Doc: https://pipedrive.readme.io/docs/core-api-concepts-rate-limiting */
      case 429 =>

        val nextAttemptAt = response.header("X-RateLimit-Reset").map(limitString => {
          val seconds = Try {
            limitString.toInt
          }
          DateTime.now().plusSeconds(seconds.getOrElse(15))
        })
        PipedriveAPIResponseStatusCodes.CommonCRMAPIError(
          error = CommonCRMAPIErrors.TooManyRequestsError(msg = "Rate limit has been exceeded", nextAttemptAt = nextAttemptAt)
        )

      case 404 =>
        PipedriveAPIResponseStatusCodes.CommonCRMAPIError(
          error = CommonCRMAPIErrors.NotFoundError(msg = "Resource unavailable")
        )

      case 401 =>
        PipedriveAPIResponseStatusCodes.CommonCRMAPIError(
          error = CommonCRMAPIErrors.UnAuthorizedError(msg = "Invalid API token")
        )

      case 501 => PipedriveAPIResponseStatusCodes.NotImplementedError(message = "Non-existent functionality")
      case 422 => PipedriveAPIResponseStatusCodes.UnProcessableEntityError(message = "Webhooks limit reached")
      case 415 => PipedriveAPIResponseStatusCodes.UnsupportedMediaTypeError(message = "Feature is not enabled")
      case 410 => PipedriveAPIResponseStatusCodes.GoneError(message = "Old resource permanently unavailable")
      case 405 => PipedriveAPIResponseStatusCodes.MethodNotAllowed(message = "Incorrect request method")
      case 403 => PipedriveAPIResponseStatusCodes.Forbidden(message = "Request not allowed")
      case 402 => PipedriveAPIResponseStatusCodes.PaymentRequiredError(message = "Company account is not open (possible reason: trial expired, payment details not entered)")
      case 400 => PipedriveAPIResponseStatusCodes.BadRequestError(message = "Request not understood")
      case _ => Try {

        /*Ref https://pipedrive.readme.io/docs/core-api-concepts-responses*/
        (response.json \ "error").asOpt[String].getOrElse("Unknown error")

      } match {

        case Failure(_) =>
          PipedriveAPIResponseStatusCodes.UnknownError(message = "Unknown error [could not parse response]", responseBody = response.body, responseCode = response.status)

        case Success(msg) =>
          PipedriveAPIResponseStatusCodes.UnknownError(message = msg, responseBody = response.body, responseCode = response.status)
      }

    }
  }

}
