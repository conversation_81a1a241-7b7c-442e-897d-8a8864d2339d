package api.integrations.crmapis

import api.integrations.{BatchContactResponse, CommonCRMAPIErrors, CreateOrUpdateBatchContactsError}
import api.integrations.models.pipedrive.PipedriveAPIResponseStatusCodes
import eventframework.ProspectObject

import play.api.libs.json.JsValue
import play.api.libs.ws.{WSClient, WSResponse}
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}
import play.api.libs.ws.DefaultBodyWritables._
import play.api.libs.ws.WSBodyWritables._

class PipedriveApi {

  def updatePipedriveContactsByUpdateUrl(
                                                  updateUrl:String,
                                                  updatedContact: JsValue,
                                                  accessToken:String,
                                                  prospectObject : ProspectObject,

                                                  isSuccessResponse:  WSResponse => Boolean,
                                                )(implicit ws: WSClient,
                                                  ec: ExecutionContext,
                                                  Logger:SRLogger
                                                ): Future[Either[CreateOrUpdateBatchContactsError.CommonCRMAPIError, BatchContactResponse]] ={

    ws.url(updateUrl)
      .addHttpHeaders(
        "Authorization" -> s"Bearer $accessToken",
        "Content-Type" -> "application/json"
      )
      .put(updatedContact)
      .map(response => {

        if (!isSuccessResponse(response)) {

          Logger.fatal(s"PipedriveOAuth createOrUpdateBatch update error: ${response} :: ${response.body}")

          PipedriveAPIResponseStatusCodes.parseStatusCodes(response = response) match {

            case e: PipedriveAPIResponseStatusCodes.CommonCRMAPIError =>
              Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(err = e.error))

            case e: PipedriveAPIResponseStatusCodes.UnknownError =>
              Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                msg = e.message,
                responseBody = e.responseBody,
                responseCode = e.responseCode
              )))

            case e =>
              Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = e.message)))
          }

        } else {

          // no need to log the success case
          //                      Logger.info(s"PipedriveOAuth createOrUpdateBatch update response :: $response")

          Right(BatchContactResponse(
            email = prospectObject.email,
            phone = prospectObject.phone,
            linkedinUrl = prospectObject.linkedin_url,
            error = None
          ))


        }


      })

  }

}
