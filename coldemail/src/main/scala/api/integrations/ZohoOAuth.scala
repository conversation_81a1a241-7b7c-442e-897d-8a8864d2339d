package api.integrations

import org.apache.pekko.actor.ActorSystem
import api.prospects.ProspectSource
import api.triggers._
import api.AppConfig
import api.accounts.TeamId
import api.columns.ProspectColumnDefUtils
import api.integrations.ZohoOAuth.{ContactsToCreateMap, ContactsToCreateOrUpdateMaps, combineResponseOfCreateAndUpdateContactsResult}
import api.integrations.crmapis.ZohoApi
import api.integrations.services.CrmLeadStatusUpdate
import api.prospects.models.ProspectId
import api.triggers.IntegrationModuleType.CONTACTS
import api.sr_audit_logs.models.ProspectObjectWithOldProspectDeduplicationColumn
import api.triggers.dao_service.TriggerDAOService
import eventframework.ProspectObject
import org.joda.time.DateTime
import play.api.libs.json.{Json, _}
import play.api.libs.ws.{WSClient, WSResponse}
import utils.mq.webhook.model.CreateInCRMJedisDAO
import utils.{Help<PERSON>, ParseUtils, SRLogger}
import api.integrations.models.zoho.ZohoCommonAPIResponseStatusCodes
import utils.input_validations.PhoneNumberValidation

import java.net.URLEncoder
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import play.api.libs.ws.WSBodyWritables.writeableOf_JsValue
import play.api.libs.ws.WSBodyWritables.writeableOf_urlEncodedForm

case class ZohoPicklistValuesResponse(
                                       display_value: String,
                                       actual_value: String
                                     )

object ZohoPicklistValuesResponse {
  implicit val reads: Reads[ZohoPicklistValuesResponse] = Json.reads[ZohoPicklistValuesResponse]
}

case class ZohoColumnsResponse(
                                id: String,
                                api_name: String,
                                display_label: String, //TODO not using now remove after if not breaking anything
                                field_label: String,
                                data_type: String,
                                read_only: Boolean,
                                pick_list_values: Option[Seq[ZohoPicklistValuesResponse]]
                              )

object ZohoColumnsResponse {
  implicit val reads: Reads[ZohoColumnsResponse] = Json.reads[ZohoColumnsResponse]
}

case class ZohoFiltersResponse(
                                id: String,
                                name: String,
                                display_value: String
                              )

object ZohoFiltersResponse {
  implicit val reads: Reads[ZohoFiltersResponse] = Json.reads[ZohoFiltersResponse]
}

case class ZohoUserResponse(
                             id: String,
                             email: String
                           )

object ZohoUserResponse {
  implicit val reads: Reads[ZohoUserResponse] = Json.reads[ZohoUserResponse]
}


class ZohoOAuth(
                 val triggerDAO: Trigger,
                 val triggerServiceV2: TriggerServiceV2,
                 val triggerDAOService: TriggerDAOService,
                 zohoApi: ZohoApi,
                 createInCRMJedisDAO: CreateInCRMJedisDAO
               ) extends ZohoCommonOAuth with TIntegrationCRMTrait {

  override type CrmData = CrmSpecificData.ZohoSpecificData

  val prospectSource: ProspectSource.Value = ProspectSource.ZOHO

  val name: IntegrationType = IntegrationType.ZOHO

  private val defaultCountForObjectsFetch = 200

  private val successStatusCodes = Seq(200, 201, 202);

  private val oAuthSettings = AppConfig.zohoOAuthSettings;
  override val batchCreateOrUpdateLimit: Int = 100
  val making_api_call_to_crm = "making_api_call_to_crm_zoho"

  def getAccessToken(code: String, location: Option[String], is_sandbox: Boolean)
                    (implicit ws: WSClient,
                     ec: ExecutionContext,
                     Logger: SRLogger
                    ): Future[Either[GetAccessTokenError, IntegrationTPAccessTokenResponse.FullTokenData]] = {
    getAccessToken(code = code, location = location, oAuthSettings = oAuthSettings, integration_type = IntegrationType.ZOHO)
  }

  def authorizationUrl(state: String, is_sandbox: Boolean = false)(implicit ws: WSClient, ec: ExecutionContext): String = {
    authorizationUrl(state = state, oAuthSettings = oAuthSettings)
  }

  def refreshAccessToken(refreshTokenData: IntegrationTPAccessTokenResponse.RefreshTokenData)
                        (implicit ws: WSClient,
                         ec: ExecutionContext,
                         Logger: SRLogger
                        ): Future[Either[RefreshAccessTokenError, IntegrationTPAccessTokenResponse.FullTokenData]] = {
    refreshAccessToken(accessTokenData = refreshTokenData, oAuthSettings = oAuthSettings, integration_type = IntegrationType.ZOHO)
  }

  // map SmartReach module type to Zoho CRM module name
  private def getCRMModuleType(moduleType: IntegrationModuleType): Future[String] = {
    moduleType match {

      case IntegrationModuleType.CONTACTS => Future.successful("Contacts")

      case IntegrationModuleType.LEADS => Future.successful("Leads")

      case IntegrationModuleType.CANDIDATES => Future.failed(new Exception(s"Unsupported module: $moduleType"))

    }
  }

  def getTPColumns(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData, module_type: Option[String])(implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger) = {

    Future.fromTry(IntegrationModuleType.withName(module_type.getOrElse("contacts")))
      .flatMap { moduleType => {

        getCRMModuleType(moduleType = moduleType)
          .flatMap(filedModule => {

            val s = AppConfig.zohoOAuthSettings
            val accessToken = accessTokenData.access_token
            val api_domain = accessTokenData.api_domain.get
            val url = s"${api_domain}/crm/v2/settings/fields"

            Logger.debug(making_api_call_to_crm)
            ws.url(url)
              .addHttpHeaders(
                "Authorization" -> s"Zoho-oauthtoken $accessToken"
              )
              .withQueryStringParameters(
                "module" -> filedModule
              )
              .get()
              .map(response => {

                if (!successStatusCodes.contains(response.status)) {

                  Logger.error(s"ZohoOAuth FATAL getZohoColumns error: $response ::: ${response.body} ")

                  val error = (response.json \ "message").asOpt[String]

                  throw new Exception(s"${error.getOrElse("Error while fetching fields")}")

                } else {

                  val fields = (response.json \ "fields")

                  val validateData = fields.validate[Seq[ZohoColumnsResponse]]

                  validateData match {

                    case JsError(e) =>

                      Logger.error(s"ZohoOAuth FATAL getZohoColumns validation error: $response ::: ${response.body} ::: $e")
                      throw new Exception(s"ZohoOAuth getTPColumns Js validation error: ${response.body} :: $e")

                    case JsSuccess(columnsData, _) =>

                      Logger.debug(s" ${name} getTPColumns pass! status: ${response.status}")

                      columnsData
                        .filter(c =>
                          !c.read_only &&
                            s.allowdedFieldTypes.contains(c.data_type)
                            // 30-Oct-2020 : Layout value comes in object format, and was crashing the frontend page while rendering sample data
                            && c.api_name.toLowerCase != "layout"
                        )
                        .sortBy(c => {

                          if (c.api_name.toLowerCase.trim == "email") {
                            -100
                          } else {
                            ParseUtils.parseInt(c.id).getOrElse(0)
                          }

                        })
                        .map(c => {
                          IntegrationTPColumns(
                            col_id = c.api_name,
                            label = c.field_label,
                            description = None,
                            field_type = __getFieldTypeFromTpColumnFieldType(tp_column_field_type = c.data_type)
                          )

                        })

                  }

                }


              })

          })
      }
      }
  }


  /*Ref Doc: https://www.zoho.com/crm/developer/docs/api/v2/custom-view-meta.html*/
  def getTPFilters(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData, module_type: Option[String])
                  (implicit ws: WSClient,
                   ec: ExecutionContext,
                   Logger: SRLogger
                  ): Future[Either[GetCRMFiltersError, Seq[IntegrationTPFilters]]] = {

    Future.fromTry(IntegrationModuleType.withName(module_type.get))
      .flatMap { moduleType => {

        getCRMModuleType(moduleType = moduleType)
          .flatMap(filedModule => {

            val s = AppConfig.zohoOAuthSettings
            val accessToken = accessTokenData.access_token
            val api_domain = accessTokenData.api_domain.get
            val url = s"${api_domain}/crm/v2/settings/custom_views"

            Logger.debug(making_api_call_to_crm)
            ws.url(url)
              .addHttpHeaders(
                "Authorization" -> s"Zoho-oauthtoken $accessToken"
              )
              .withQueryStringParameters(
                "module" -> filedModule
              )
              .get()
              .map(response => {

                if (!isSuccessResponse(response)) {

                  Logger.error(s"ZohoOAuth FATAL getZohoFilters error: $response ::: ${response.body} ")

                  ZohoCommonAPIResponseStatusCodes.parseStatusCodes(response = response) match {

                    case e: ZohoCommonAPIResponseStatusCodes.CommonCRMAPIError =>
                      Left(GetCRMFiltersError.CommonCRMAPIError(err = e.error))

                    case e =>
                      Left(GetCRMFiltersError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                        msg = e.message,
                        responseBody = response.body,
                        responseCode = response.status
                      )))
                  }

                } else {

                  val filters = (response.json \ "custom_views")

                  val validateData = filters.validate[Seq[ZohoFiltersResponse]]

                  validateData match {

                    case JsError(e) =>

                      Logger.error(s"ZohoOAuth FATAL getZohoFilters validation error: $response ::: ${response.body} ::: $e")
                      throw new Exception(s"ZohoOAuth getZohoFilters Js validation error: ${response.body} :: $e")

                    case JsSuccess(filtersData, _) =>

                      Logger.debug(s" ${name} getTPFilters pass! status: ${response.status}")

                      val tpFilters = filtersData
                        .map(f => {

                          IntegrationTPFilters(
                            id = f.id,
                            name = f.name,
                            display_name = f.display_value
                          )

                        })

                      Right(tpFilters)

                  }

                }


              })

          })
      }
      }
  }


  def searchContactByEmail(
                            apiDomain: String,
                            accessToken: String,
                            email: String,
                            zohoModule: String
                          )(implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger): Future[Option[String]] = {

    // Encode the email to ensure special characters are handled
    val encodedEmail = URLEncoder.encode(email, "UTF-8")
    val url = s"$apiDomain/crm/v2/$zohoModule/search?email=$encodedEmail"


    ws.url(url)
      .addHttpHeaders(
        "Authorization" -> s"Zoho-oauthtoken $accessToken"
      )
      .get()
      .map { response =>
        if (response.status == 204) {
          // Returning None case because api return 204 if a contact is not present in zoho crm
          None
        } else if (isSuccessResponse(response)) {
          ((response.json \ "data").as[Seq[JsObject]].head \ "id").asOpt[String]
        } else {
          Logger.warn(s"Failed to search contact by email: $email")
          None
        }
      }
  }


  override def createOrUpdateBatchContacts(
                                            moduleType: IntegrationModuleType,
                                            accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                            contacts: Seq[ProspectObjectWithOldProspectDeduplicationColumn],
                                            fieldMapping: UpdateFieldsMappingForm,
                                            userMapping: UpdateUserMappingForm,
                                            accountId: Long,
                                            teamId: Long,
                                          )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem, Logger: SRLogger): Future[Seq[Either[CreateOrUpdateBatchContactsError, BatchContactResponse]]]= {

    val transFormedContacts = TIntegrationCRMTrait.mapToOldAndNewProspectObjects(contacts = contacts)
    val apiDomain = accessTokenData.api_domain.get
    val accessToken = accessTokenData.access_token

    getCRMModuleType(moduleType = moduleType)
      .flatMap(zohoModule => {
        findAllByEmailsAndPhones(
          accessTokenData = accessTokenData,
          emails = transFormedContacts.map(_.oldProspectObject.prospectObject.email).filter(_.isDefined).map(_.get),
          phones = transFormedContacts.map(_.oldProspectObject.prospectObject.phone).filter(_.isDefined).map(_.get),
          module = moduleType,
          apiDomain = accessTokenData.api_domain.get,
          zohoModule = zohoModule
        ).flatMap {
          case Right(foundProspects: Seq[IntegrationContactResponse]) => {

            val processedContacts: Seq[Either[CreateOrUpdateBatchContactsError.CommonCRMAPIError, (ProspectObject, Option[String])]] = transFormedContacts.map(contact => {
              val foundContact = foundProspects.find(c =>
                (c.email.isDefined && c.email == contact.oldProspectObject.prospectObject.email) ||
                  (c.phone.isDefined && c.phone == contact.oldProspectObject.prospectObject.phone)
              )

              val contactWithId = (contact.newProspectObject.prospectObject, foundContact.map(_.id))

              if (foundContact.isDefined) {
                // will get Updated
                Right(contactWithId)
              } else {
                // Lock will be present only for creation
                val gotLock = createInCRMJedisDAO.acquireLockForProspect(
                  prospectId = ProspectId(contact.newProspectObject.prospectObject.id),
                  teamId = TeamId(teamId),
                  crmType = IntegrationType.ZOHO,
                  module = moduleType
                )


                if (gotLock) {
                  Right(contactWithId)
                } else {
                  Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(
                    CommonCRMAPIErrors.ContactCreationDelayInCRM(msg = "Contact is Locked will try after sometime.")
                  ))
                }
              }
            })

            // Filter out only the Right values for API call
            val validContacts = processedContacts.collect { case Right(contact) => contact }


            if (validContacts.isEmpty) {
              // If there are no valid contacts, return original processed results
              Future.successful(processedContacts.map {
                case Left(error) => Left(error) // Preserve original Left results (including lock failures)
                case Right(_) => {
                  Logger.shouldNeverHappen(s"Processed Contacts giving a right value even though validContacts is Empty. tid_${teamId}")
                  Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(
                  CommonCRMAPIErrors.UnknownError("This will never happen because Right validContacts are actually all the right Values of process contacts")))
                }})
            } else {
              val contactsObj = buildCreateContactObj(
                moduleType = moduleType,
                prospects = validContacts,
                fieldMapping = fieldMapping,
                userMapping = userMapping,
                accountId = accountId,
                teamId = teamId
              )

              val url = s"$apiDomain/crm/v3/$zohoModule/upsert"

              Logger.debug("Making API call to CRM")
              Logger.debug(s"CONTACTS OBJ going in create or update zoho ${contactsObj}")

             val result: Future[Seq[Either[CreateOrUpdateBatchContactsError, BatchContactResponse]]] =  ws.url(url)
                .addHttpHeaders(
                  "Authorization" -> s"Zoho-oauthtoken $accessToken",
                  "Content-Type" -> "application/json"
                )
                .post(contactsObj)
                .map { response =>
                  if (!isSuccessResponse(response)) {
                    Logger.fatal(s"ZohoOAuth createOrUpdateBatch error: ${response} :: ${response.body}")

                    val error = ZohoCommonAPIResponseStatusCodes.parseStatusCodes(response = response) match {
                      case e: ZohoCommonAPIResponseStatusCodes.CommonCRMAPIError =>
                        CreateOrUpdateBatchContactsError.CommonCRMAPIError(err = e.error)
                      case e: ZohoCommonAPIResponseStatusCodes.UnknownError =>
                        CreateOrUpdateBatchContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                          msg = e.message,
                          responseBody = e.responseBody,
                          responseCode = e.responseCode
                        ))
                      case e =>
                        CreateOrUpdateBatchContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = e.message))
                    }

                    // the lock errors are kept preserved as we  use the contact creation delay error in the above layers
                    processedContacts.map {
                      case Left(existingError) => Left(existingError) // Keep original errors (including locks)
                      case Right(_) => Left(error)
                    }
                  } else {

                    Logger.info(s"ZohoOAuth createOrUpdateBatch response :: ${response.json}")
                    val batchResults = (response.json \ "data").as[Seq[JsValue]]
                    val resultQueue = scala.collection.mutable.Queue[JsValue](batchResults*)
                    
                    processedContacts.map {
                      case Left(error) => Left(error) // Keep original errors (including locks)
                      case Right((contact, _)) => {
                        val result = resultQueue.dequeue()
                        val error = __parseBatchContactSuccessResponse(errorObj = result)
                        Right(BatchContactResponse(
                          email = contact.email,
                          phone = contact.phone,
                          linkedinUrl = contact.linkedin_url,
                          error = error
                        ))
                      }
                    }
                  }
                }.recover { case e =>{
                  contacts.foreach(contact => {
                    val hasLock = createInCRMJedisDAO.getLock( // since the api call failed releasing the lock so that we can try without waiting
                      prospectId = ProspectId(contact.prospectObject.id),
                      teamId = TeamId(teamId),
                      crmType = IntegrationType.ZOHO,
                      module = moduleType
                    )
                    if (hasLock) {
                      createInCRMJedisDAO.deleteLock( // since the api call failed releasing the lock so that we can try without waiting
                        prospectId = ProspectId(contact.prospectObject.id),
                        teamId = TeamId(teamId),
                        crmType = IntegrationType.ZOHO,
                        module = moduleType
                      )
                    }

                  })
                  throw e
                }
                }

              result
            }
          }

          case Left(findBatchError: FindBatchError) => {
            Future.successful(transFormedContacts.map(_ => findBatchError match {
              case FindBatchError.CommonCRMAPIError(err) =>
                Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(err))
              case FindBatchError.MalformedResponseStructureError(msg) =>
                Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(CommonCRMAPIErrors.UnknownError(msg)))
              case FindBatchError.InvalidModuleError(msg) =>
                Left(CreateOrUpdateBatchContactsError.InvalidModuleError(msg))
            }))
          }
        }
      })
  }


  private def updateMultipleMatchingContactWithASingleProspect(moduleType: IntegrationModuleType,
                                  accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                  contact: ProspectObject,
                                  listOfContactIds: Seq[String],
                                  fieldMapping: UpdateFieldsMappingForm,
                                  userMapping: UpdateUserMappingForm,
                                  accountId: Long,
                                  teamId: Long,
                                  zohoModule: String
                                 )(implicit ws: WSClient,
                                   ec: ExecutionContext,
                                   Logger: SRLogger
                                 ): Future[Either[CreateOrUpdateBatchContactsError, Seq[BatchContactResponse]]] = {


    val contactData = buildCreateContactObj(
      moduleType = moduleType,
      prospects = Seq((contact, None)),
      fieldMapping = fieldMapping,
      userMapping = userMapping,
      accountId = accountId,
      teamId = teamId
    )

    val updateRequests = listOfContactIds.map { contactId =>

      val api_domain = accessTokenData.api_domain.get

      val url = s"${api_domain}/crm/v2/$zohoModule/$contactId"

      zohoApi
        .updateByContactId(
          url = url,
          accessTokenData = accessTokenData,
          contactData = contactData,
          isSuccessResponse = isSuccessResponse,
          contact = contact,
          contactId = contactId
        )
    }

    // Combine the results from all PUT requests
    Future.sequence(updateRequests).map { results =>
      val errors = results.collect { case Left(error) => error }
      if (errors.nonEmpty) {
        Left(errors.head) // Return the first error found
      } else {
        Right(results.collect { case Right(success) => success })
      }
    }
  }

  def findAllByEmailsAndPhones(
                                accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                emails: Seq[String],
                                phones: Seq[String],
                                module: IntegrationModuleType,
                                apiDomain: String,
                                zohoModule: String
                              )(implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger): Future[Either[FindBatchError, Seq[IntegrationContactResponse]]] = {
    val emailCriteria = if (emails.nonEmpty) s"Email:in:${emails.mkString(",")}" else ""
    val phoneCriteria = if (phones.nonEmpty) s"Phone:in:${phones.mkString(",")}" else ""

    val emailSearchFuture: Future[Either[FindBatchError, Seq[IntegrationContactResponse]]] = if (emailCriteria.trim.nonEmpty) {
      zohoApi.findAllByCriteria(
        accessTokenData = accessTokenData,
        criteria = emailCriteria,
        module = module,
        api_domain = apiDomain,
        isSuccessResponse = isSuccessResponse,
        zohoModule = zohoModule
      )
    } else {
      Future.successful(Right(Seq()))
    }

    val phoneSearchFuture: Future[Either[FindBatchError, Seq[IntegrationContactResponse]]] = if (phoneCriteria.trim.nonEmpty) {
      zohoApi.findAllByCriteria(
        accessTokenData = accessTokenData,
        criteria = phoneCriteria,
        module = module,
        api_domain = apiDomain,
        isSuccessResponse = isSuccessResponse,
        zohoModule = zohoModule
      )
    } else {
      Future.successful(Right(Seq()))
    }


    for {
      emailSearch <- emailSearchFuture
      phoneSearch <- phoneSearchFuture
    } yield {
      (emailSearch, phoneSearch) match {
        case (Right(emailResults), Right(phoneResults)) =>
          Right((emailResults ++ phoneResults).distinct)

        case (Left(emailError), Left(phoneError)) =>
          Logger.shouldNeverHappen(s"Both email and phone searches failed: $emailError && $phoneError")
          Left(emailError)

        case (Left(emailError), _) =>
          Logger.shouldNeverHappen(s"Email search failed: $emailError")
          Left(emailError)

        case (_, Left(phoneError)) =>
          Logger.shouldNeverHappen(s"Phone search failed: $phoneError")
          Left(phoneError)
      }
    }

  }





  /*
  def createOrUpdateBatchLeads(accessTokenData: IntegrationTPAccessTokenResponse,
                               leads: Seq[Prospect],
                               fieldMapping: UpdateFieldsMappingForm,
                               accountId: Long,
                               teamId: Long
  )(implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger) = {

    val leadsObj = buildCreateLeadsObj(prospects = leads,
      fieldMapping = fieldMapping,
      accountId = accountId,
      teamId = teamId
    )

    val api_domain = accessTokenData.api_domain.get
    val url = s"${api_domain}/crm/v2/Leads/upsert"
    val accessToken = accessTokenData.access_token

    ws.url(url)
      .withHeaders(
        "Authorization" -> s"Zoho-oauthtoken $accessToken",
        "Content-Type" -> "application/json"
      )
      .post(leadsObj)
      .map(response => {

        if (!successStatusCodes.contains(response.status)) {

          Logger.fatal(s"ZohoOAuth createOrUpdateBatchLeads error: ${response} :: ${response.body}")
          None

        } else {

          Logger.info(s"ZohoOAuth createOrUpdateBatchLeads response :: $response")

          Some(1)

        }


      })

  }
  */

  /*Ref Doc: https://www.zoho.com/crm/developer/docs/api/v2/search-records.html*/
  def findOneByEmailAndModule(
                               accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                               email: String,
                               module: IntegrationModuleType,
                               statusColumn: Option[String]
                             )(
                               implicit ws: WSClient,
                               ec: ExecutionContext,
                               system: ActorSystem,
                               Logger: SRLogger
                             ): Future[Either[FindOneByEmailAndModuleError, Option[IntegrationContactResponse]]] = {

    val api_domain = accessTokenData.api_domain.get

    getCRMModuleType(moduleType = module)
      .flatMap(zohoModule => {

        val url = s"${api_domain}/crm/v2/$zohoModule/search"

        val accessToken = accessTokenData.access_token

        Logger.debug(making_api_call_to_crm)
        ws.url(url)
          .addHttpHeaders(
            "Authorization" -> s"Bearer $accessToken",
            "Content-Type" -> "application/json"
          )
          .withQueryStringParameters(
            "email" -> email
          )
          .get()
          .map(response => {

            /**
             * Date: 21st Jan 2022
             * Zoho returning 204 with null body when contact not found, Basically it is 404 case
             * So adding this check after @Kiana-lee Klette complained Zoho CRM not working: https://app.intercom.com/a/apps/xmya8oga/inbox/inbox/mentions/conversations/84281100121056
             * Created a ticket in Zoho no proper response from them, they time passing with send code send response and all: https://help.zoho.com/portal/en/ticket/4011066483439
             * Found this after testing manually from .eu zone and .com zone
             */
            if (response.status == 204) {

              Logger.info(s"ZohoOAuth findOneByEmailAndModule not found: ${response.body} :: status: ${response.status} :: email: $email url :: ${url}")

              Right(None)

            } else if (!isSuccessResponse(response)) {

              Logger.fatal(s"$name findOneByEmailAndModule error: ${response.body} :: status: ${response.status} :: email: $email url :: $url")

              ZohoCommonAPIResponseStatusCodes.parseStatusCodes(response = response) match {

                case e: ZohoCommonAPIResponseStatusCodes.CommonCRMAPIError =>
                  Left(FindOneByEmailAndModuleError.CommonCRMAPIError(err = e.error))

                case e =>
                  Left(FindOneByEmailAndModuleError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                    msg = e.message,
                    responseBody = response.body,
                    responseCode = response.status
                  )))

              }

            } else {

              Logger.debug(s" ${name} findOneByEmailAndModule pass! status: ${response.status}")

              val contact = (response.json \ "data").asOpt[Seq[JsValue]]

              if (contact.isDefined && contact.get.nonEmpty) {
                val id = (contact.get.head \ "id").as[String]
                val status = (contact.get.head \ "Lead_Status").asOpt[String]
                val email = (contact.get.head \ "Email").asOpt[String]
                Logger.debug(s"${name} findOneByEmailAndModule :: email : $email :: status :: $status")

                Right(
                  Some(
                    IntegrationContactResponse(
                      id = id,
                      email = email,
                      status = status,
                      phone = None
                    )
                  )
                )

              } else {

                Logger.info(s"$name findOneByEmailAndModule not found: ${response.body} :: status: ${response.status} :: email: $email url :: $url")

                Right(None)
              }
            }

          })
      })

  }

  def findAllByProspectAndModule(
                                  accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                  prospect: ProspectObject,
                                  module: IntegrationModuleType,
                                  statusColumn: Option[String]
                                )(implicit ws: WSClient,
                                  ec: ExecutionContext,
                                  system: ActorSystem,
                                  Logger: SRLogger): Future[Either[FindBatchError, Seq[IntegrationContactResponse]]] = {

    val api_domain = accessTokenData.api_domain.get

    getCRMModuleType(moduleType = module)
      .flatMap(zohoModule => {

        /*Ref Link: https://www.zoho.com/developer/docs/vertical-solutions/api/v6/search-records.html */
        val url = s"${api_domain}/crm/v2/$zohoModule/search"

        val accessToken = accessTokenData.access_token

        val queryParams: Seq[(String, String)] = ZohoOAuth.extractQueryParams(prospect = prospect)

        Logger.info(s"findAllByProspectAndModule Query Params which are going for prosepect search  ${queryParams.toString()}")

        // Convert the sequence to tuple parameters and pass them

        if (queryParams.isEmpty) {

          // Nothing valid
          Future.successful(Right(Seq()))
        } else {

          val request = ws.url(url)
            .addHttpHeaders(
              "Authorization" -> s"Bearer $accessToken",
              "Content-Type" -> "application/json"
            )
          Future.sequence(
            queryParams.map { case (key, value) =>

              Logger.info(making_api_call_to_crm)
              request
                .withQueryStringParameters((key, value))
                .get()
                .map(response => {

                  /**
                   * Date: 21st Jan 2022
                   * Zoho returning 204 with null body when contact not found, Basically it is 404 case
                   * So adding this check after @Kiana-lee Klette complained Zoho CRM not working: https://app.intercom.com/a/apps/xmya8oga/inbox/inbox/mentions/conversations/84281100121056
                   * Created a ticket in Zoho no proper response from them, they time passing with send code send response and all: https://help.zoho.com/portal/en/ticket/4011066483439
                   * Found this after testing manually from .eu zone and .com zone
                   */
                  if (response.status == 204) {

                    Logger.info(s"ZohoOAuth findAllByProspectAndModule not found: ${response.body} :: status: ${response.status} :: email: ${prospect.email} url :: ${url}")

                    Right(Seq())

                  } else if (!isSuccessResponse(response)) {

                    Logger.fatal(s"$name findAllByProspectAndModule error: ${response.body} :: status: ${response.status} :: email: ${prospect.email} url :: $url")

                    ZohoCommonAPIResponseStatusCodes.parseStatusCodes(response = response) match {


                      case e: ZohoCommonAPIResponseStatusCodes.CommonCRMAPIError =>
                        Left(FindBatchError.CommonCRMAPIError(err = e.error))

                      case e =>
                        Left(FindBatchError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                          msg = e.message,
                          responseBody = response.body,
                          responseCode = response.status
                        )))


                    }

                  } else {

                    val contacts = (response.json \ "data").as[Seq[JsValue]]


                    Logger.debug(s"${name} findAllByProspectAndModule :: Number of contacts found  ${contacts.size} for contact with email :: ${prospect.email} and phone ${prospect.phone}")

                    if (contacts.nonEmpty) {


                      val distinctContacts = contacts.map(contact => {
                          val id = (contact \ "id").as[String]
                          val status = (contact \ "Lead_Status").asOpt[String]
                          val email = (contact \ "Email").asOpt[String]
                          val phone = (contact \ "Phone").asOpt[String]
                          val linkedinUrl = (contact \ "linkedin_url").asOpt[String]
                          // Logger.debug(s"${name} findAllByProspectAndModule :: email : $email :: status :: $status phone :: ${phone}")
                          IntegrationContactResponse(
                            id = id,
                            email = email,
                            status = status,
                            phone = phone,
                            linkedin_url = linkedinUrl
                          )
                        })
                        .distinct

                      val contactWhichDoNotMatchEitherEmailOrPhone = distinctContacts.filterNot(resp =>{
                        resp.email == prospect.email || resp.phone == prospect.phone
                      })

                      val msgLog = contactWhichDoNotMatchEitherEmailOrPhone.map(c =>{
                        "email::" + c.email + "and phone: " + c.phone + "contactId ::" + c.id
                      })
                      Logger.warn(s"Zoho sent contacts which do not match either email or phone contacts are ${msgLog}")
                      val contactWhichMatchEitherEmailOrPhone = distinctContacts.filter(resp =>{
                        resp.email == prospect.email || resp.phone == prospect.phone
                      })

                      Right(contactWhichMatchEitherEmailOrPhone)


                    } else {

                      Logger.info(s"$name findAllByProspectAndModule not found: ${response.body} :: status: ${response.status} :: email: ${prospect.email} url :: $url")

                      Right(Seq())
                    }
                  }

                })
            })
        }.map { results =>
          // Separate errors from successes
          val (errors, successes) = results.partition(_.isLeft)

          // Collect all the errors
          val allErrors = errors.collect { case Left(error) => error }

          // Collect all the successes
          val combinedSuccesses = successes.collect { case Right(responses) => responses }.flatten.distinct

          // If there are any errors, return them. Otherwise, return all the successes.
          if (allErrors.nonEmpty) Left(
            FindBatchError.CommonCRMAPIError(
              CommonCRMAPIErrors.InternalServerError(
                allErrors.map(e => e.message).mkString("::")
              )
            )) else Right(combinedSuccesses)
        }
      })

  }

  protected def updateActivityInCRM(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                    person_id: String,
                                    activitySubject: String,
                                    activityType: TPActivityType.Value,
                                    detailedActivitySubject: String,
                                    activityAt: DateTime,
                                    emailSubject: Option[String],
                                    emailSenderAddress: Option[String],
                                    emailSenderFirstName: Option[String],
                                    emailSenderLastName: Option[String],
                                    emailBody: Option[String],
                                    emailTextBody: Option[String],
                                    module: IntegrationModuleType,
                                    owner_id: Option[String]
                                   )(implicit ws: WSClient,
                                     ec: ExecutionContext,
                                     Logger: SRLogger
                                   ): Future[Either[UpdateActivityError, String]] = {

    val api_domain = accessTokenData.api_domain.get
    //    val url = s"${api_domain}/crm/v2/Contacts/${person_id}/Notes"
    // val url = if(module == IntegrationModuleType.CONTACTS) s"${api_domain}/crm/v2/Contacts/${person_id}/Notes" else s"${api_domain}/crm/v2/Leads/${person_id}/Notes"

    getCRMModuleType(moduleType = module)
      .flatMap(zohoModule => {
        val url = s"${api_domain}/crm/v2/$zohoModule/${person_id}/Notes"


        val accessToken = accessTokenData.access_token

        val noteContent = emailTextBody.getOrElse(emailSubject.getOrElse(""))
        val activity = Json.obj("data" -> Json.arr(
          Json.obj(
            "Note_Title" -> activitySubject,
            "Note_Content" -> noteContent
          )
        ))

        Logger.debug(making_api_call_to_crm)
        ws.url(url)
          .addHttpHeaders(
            "Authorization" -> s"Bearer $accessToken",
            "Content-Type" -> "application/json"
          )
          .post(activity)
          .map(response => {

            if (!isSuccessResponse(response)) {

              Logger.fatal(s"ZohoOAuth updateActivityInCRM error: ${response} :: ${response.body}")

              ZohoCommonAPIResponseStatusCodes.parseStatusCodes(response = response) match {

                case e: ZohoCommonAPIResponseStatusCodes.CommonCRMAPIError =>
                  Left(UpdateActivityError.CommonCRMAPIError(err = e.error))

                case e: ZohoCommonAPIResponseStatusCodes.UnknownError =>
                  Left(UpdateActivityError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                    msg = e.message,
                    responseBody = e.responseBody,
                    responseCode = e.responseCode
                  )))

                case e =>
                  Left(UpdateActivityError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = e.message)))

              }

            } else {

              Logger.info(s"ZohoOAuth updateActivityInCRM response :: $response")

              Right(person_id)

            }

          })

      })

  }

  protected def hasMoreContactsToSync(dataResponse: WSResponse, moduleType: IntegrationModuleType) = {
    (dataResponse.json \ "info" \ "more_records").get.as[Boolean]
  }

  protected def findLeastaddedAtToSync(data: Seq[JsValue])(using Logger: SRLogger): Long = {

    val contactWithModifiedTimeOpt = data.findLast(contact => {
      val Modified_Time = (contact \ "Modified_Time").asOpt[String]
      Modified_Time.isDefined
    })

    contactWithModifiedTimeOpt match {
      case Some(contact) =>
        val Modified_Time = (contact \ "Modified_Time").asOpt[String]
        new DateTime(Modified_Time.get).getMillis
      case None =>
        Logger.warn(s"findLeastaddedAtToSync Modified_Time is empty for the entire list")

        val contactWithCreatedTimeOpt = data.findLast(contact => {
          val Created_Time = (contact \ "Created_Time").asOpt[String]
          Created_Time.isDefined
        })
        contactWithCreatedTimeOpt.map(time => new DateTime(time).getMillis).getOrElse(new DateTime().minusHours(3).getMillis)
    }
  }

  protected def findLatestaddedAtToSync(data: Seq[JsValue])(using Logger: SRLogger): Long = {


    val contactWithModifiedTimeOpt = data.find(contact => {
      val Modified_Time = (contact \ "Modified_Time").asOpt[String]
      Modified_Time.isDefined
    })

    contactWithModifiedTimeOpt match {
      case Some(contact) =>
        val Modified_Time = (contact \ "Modified_Time").asOpt[String]
        new DateTime(Modified_Time.get).getMillis
      case None =>
        Logger.warn(s"findLatestaddedAtToSync Modified_Time is empty for the entire list")

        val contactWithCreatedTimeOpt = data.find(contact => {
          val Created_Time = (contact \ "Created_Time").asOpt[String]
          Created_Time.isDefined
        })
        contactWithCreatedTimeOpt.map(time => new DateTime(time).getMillis).getOrElse(new DateTime().minusHours(3).getMillis)
    }

  }


  protected def fetchRecentContactsFromCRM(
                                            accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                            page: Long,
                                            moduleType: IntegrationModuleType,

                                            forSampleData: Boolean,
                                            crmSpecificData: CrmData

                                          )(implicit ws: WSClient,
                                            ec: ExecutionContext,
                                            system: ActorSystem,
                                            Logger: SRLogger
                                          ): Future[Either[FetchRecentContactsError, WSResponse]] = {

    val api_domain = accessTokenData.api_domain.get


    getCRMModuleType(moduleType = moduleType)
      .flatMap(zohoModule => {

        val url = s"${api_domain}/crm/v2/$zohoModule"

        val accessToken = accessTokenData.access_token

        val defaultCount = if (forSampleData) 1 else defaultCountForObjectsFetch;

        val validatedTpFilterId = isValidTpFilterId(tp_filter_id = crmSpecificData.tp_filter_id)

        def wsRequest = if (validatedTpFilterId.isDefined) {

          Logger.debug(making_api_call_to_crm)
          ws.url(url)
            .addHttpHeaders(
              "Authorization" -> s"Bearer $accessToken",
              "Content-Type" -> "application/json"
            )
            .withQueryStringParameters(
              "sort_order" -> "desc",
              "sort_by" -> "Modified_Time",
              "page" -> s"${page}",
              "per_page" -> s"${defaultCount}",
              "cvid" -> s"${validatedTpFilterId.get}"
            )

        } else {

          Logger.debug(making_api_call_to_crm)
          ws.url(url)
            .addHttpHeaders(
              "Authorization" -> s"Bearer $accessToken",
              "Content-Type" -> "application/json"
            )
            .withQueryStringParameters(
              "sort_order" -> "desc",
              "sort_by" -> "Modified_Time",
              "page" -> s"${page}",
              "per_page" -> s"${defaultCount}"
            )

        }

        wsRequest
          .get()
          .map(response => {

            if (!isSuccessResponse(response)) {

              Logger.fatal(s"ZohoOAuth fetchRecentContactsFromCRM error: ${response} :: ${response.body}")

              ZohoCommonAPIResponseStatusCodes.parseStatusCodes(response = response) match {

                case e: ZohoCommonAPIResponseStatusCodes.CommonCRMAPIError =>
                  Left(FetchRecentContactsError.CommonCRMAPIError(err = e.error))

                case e: ZohoCommonAPIResponseStatusCodes.UnknownError =>
                  Left(FetchRecentContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                    msg = e.message,
                    responseBody = e.responseBody,
                    responseCode = e.responseCode
                  )))

                case e =>
                  Left(FetchRecentContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = e.message)))

              }

            } else {

              Logger.info(s"ZohoOAuth fetchRecentContactsFromCRM response :: $response  body: ")
              (response.json \ "data").asOpt[Seq[JsValue]].foreach(_.foreach(js => logger.debug(s" fetchRecentContactsFromCRM response body :: $js")))

              Right(response)

            }

          })

      })

  }

  protected def getFilteredContactsFromTPResponse(
                                                   responseJson: JsValue,
                                                   fieldMapping: UpdateFieldsMappingForm,
                                                   accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                                   moduleType: IntegrationModuleType
                                                 )
                                                 (implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem, Logger: SRLogger): Future[Seq[JsValue]] = {

    moduleType match {

      case IntegrationModuleType.CONTACTS =>

        Future.successful((responseJson \ "data").get.as[Seq[JsValue]])

      case IntegrationModuleType.LEADS =>

        Future.successful((responseJson \ "data").get.as[Seq[JsValue]])

      case IntegrationModuleType.CANDIDATES =>

        Future.failed(new Exception(s"FATAL $name getFilteredContactsFromTPResponse unsupported module: $moduleType"))

    }

  }

  protected def getFilteredContactsFromTPResponseForFrontEnd(filteredContact: JsValue)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem, Logger: SRLogger): Future[JsValue] = {
    Future.successful(filteredContact)
  }

  /*
  protected def fetchRecentLeadsFromCRM(
                            accessTokenData: IntegrationTPAccessTokenResponse,
                            page: Long,
                            trigger: TriggerInDB,

                            hubspotVidOffset: Option[Long] = None,
                            hubspotTimeOffset: Option[Long] = None


                          )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem, Logger: SRLogger): Future[WSResponse] = {

    val api_domain = accessTokenData.api_domain.get
    val url = s"${api_domain}/crm/v2/Leads"
    val accessToken = accessTokenData.access_token


    if(trigger.tp_filter_id.isDefined && trigger.tp_filter_id.nonEmpty && trigger.tp_filter_id.get != "-1") {

      ws.url(url)
        .withHeaders(
          "Authorization" -> s"Bearer $accessToken",
          "Content-Type" -> "application/json"
        )
        .withQueryString(
          "sort_order" -> "desc",
          "sort_by" -> "Created_Time",
          "page" -> s"${page}",
          "cvid" -> s"${trigger.tp_filter_id.get}"
        )
        .get

    } else {

      ws.url(url)
        .withHeaders(
          "Authorization" -> s"Bearer $accessToken",
          "Content-Type" -> "application/json"
        )
        .withQueryString(
          "sort_order" -> "desc",
          "sort_by" -> "Created_Time",
          "page" -> s"${page}"
        )
        .get

    }

  }


  protected def getFilteredLeadsFromTPResponse(responseJson: JsValue, fieldMapping: UpdateFieldsMappingForm, accessToken: String)
                                       (implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger): Future[Seq[JsValue]] = {
    Future.successful((responseJson \ "data").get.as[Seq[JsValue]])
  }
  */


  protected def getValueFromTPContactObj(contact: JsValue, destField: Option[TriggerFields])(using Logger: SRLogger): Option[String] = {

    if (destField.isEmpty) None else {

      val destFieldValue = (contact \ destField.get.tp_field).asOpt[String]

      if (
        (destFieldValue.isEmpty || destFieldValue.get.trim.isEmpty) &&
          destField.get.tp_field_default_value.nonEmpty &&
          destField.get.tp_field_default_value.get.trim.nonEmpty)
        destField.get.tp_field_default_value
      else
        destFieldValue

    }

  }


  protected def getTpOwnerIdValueFromTPContactObj(contact: JsValue)
                                                 (using Logger: SRLogger): Option[String] = {
    (contact \ "Owner" \ "id").asOpt[String]

  }

  private def buildCreateContactObj(
                                     moduleType: IntegrationModuleType,
                                     prospects: Seq[(ProspectObject, Option[String])],
                                     fieldMapping: UpdateFieldsMappingForm,
                                     userMapping: UpdateUserMappingForm,
                                     accountId: Long,
                                     teamId: Long
                                   )(using Logger: SRLogger): JsValue = {

    moduleType match {
      case IntegrationModuleType.CONTACTS | IntegrationModuleType.LEADS =>

        val contactsObj = prospects.map { p =>
          // Handle mandatory Last_Name field
          val updatedProspect = if (p._1.last_name.forall(_.trim.isEmpty)) {
            Logger.warn(s"Empty last_name for prospect: id: ${p._1.id}, email: ${p._1.email}, first_name: ${p._1.first_name}, last_name: ${p._1.last_name}")
            p._1.copy(last_name = Some("N/A"))
          } else p._1

          // Initialize contact object, optionally include ID for updating existing records
          var contactObj = p._2 match {
            case Some(id) => Json.obj("id" -> id) // Include "id" if it exists
            case None => Json.obj()
          }

          // Populate contact object based on field mappings
          fieldMapping.fields.get
            .filter(f => f.sr_field.trim.nonEmpty && f.tp_field.trim.nonEmpty)
            .foreach { f =>
              contactObj = contactObj ++ Json.obj(
                f.tp_field -> ProspectColumnDefUtils._getProspectFieldValue(p = updatedProspect, field = f)
              )
            }

          // Map Owner if available
          findMappedUserByProspectOwnerIdForCRMContactObj(owner_id = updatedProspect.owner_id, userMapping = userMapping).foreach { owner =>
            contactObj = contactObj ++ Json.obj("Owner" -> owner.tp_user_id)
          }

          contactObj
        }

        Json.obj("data" -> contactsObj,"duplicate_check_fields" ->Json.arr("Email","Phone"))

      case IntegrationModuleType.CANDIDATES =>
        Logger.fatal(s"buildCreateContactObj not supported for module: $moduleType")
        Json.obj()
    }
  }


  /*
  def buildCreateLeadsObj(prospects: Seq[Prospect], fieldMapping: UpdateFieldsMappingForm, accountId: Long, teamId: Long
  )(
    implicit Logger: SRLogger
  ): JsValue = {

    val conatactsObj = prospects.map(p => {
      var conatactObj = Json.obj()
      fieldMapping.fields.get
        .filter(f => f.sr_field.trim.nonEmpty && f.tp_field.trim.nonEmpty)
        .foreach(f => {
          conatactObj = conatactObj ++ Json.obj(
            f.tp_field -> _getProspectFieldValue(p = p, field = f)
          )
        })

      conatactObj
    })

    Json.obj("data" -> conatactsObj)

  }
  */


  def Uninstall(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData, teamId: Long)
               (implicit ws: WSClient,
                ec: ExecutionContext,
                Logger: SRLogger
               ): Future[Either[UninstallError, Int]] = {

    val api_domain = accessTokenData.api_domain.get
    val revokeUrl = s"${api_domain}/oauth/v2/token/revoke?token=${accessTokenData.refresh_token.get}"
    val accessToken = accessTokenData.access_token

    val body: Map[String, Seq[String]] = Map(
      "token" -> Seq(accessTokenData.refresh_token.get)
    )

    Logger.debug(making_api_call_to_crm)
    ws.url(revokeUrl)
      .addHttpHeaders(
        "Authorization" -> s"Zoho-oauthtoken $accessToken",
        "Content-Type" -> "application/json"
      )
      .post(body)
      .map(response => {

        if (!isSuccessResponse(response)) {

          Logger.fatal(s"ZohoOAuth Uninstall error teamId: $teamId :: accessTokenData: $accessTokenData")

          Left(UninstallError.OAuthError(err = parseOAuthStatusCodes(response = response)))

        } else {

          Logger.info(s"ZohoOAuth Uninstall success teamId: $teamId :: accessTokenData: $accessTokenData")

          Right(1)

        }

      })

  }


  /*Doc: https://www.zoho.com/crm/developer/docs/api/v2/get-users.html */
  def getTPUsers(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData)
                (implicit ws: WSClient,
                 ec: ExecutionContext,
                 Logger: SRLogger
                ): Future[Either[GetCRMUsersError, Seq[IntegrationTPUsersList]]] = {

    val accessToken = accessTokenData.access_token
    val api_domain = accessTokenData.api_domain.get
    val url = s"${api_domain}/crm/v2/users?type=ActiveUsers"

    Logger.debug(making_api_call_to_crm)
    ws.url(url)
      .addHttpHeaders(
        "Authorization" -> s"Zoho-oauthtoken $accessToken",
        "Content-Type" -> "application/json"
      )
      .get()
      .map(response => {

        if (!isSuccessResponse(response)) {

          Logger.error(s"ZohoOAuth FATAL getAllUsers error: $response ::: ${response.body} ")

          ZohoCommonAPIResponseStatusCodes.parseStatusCodes(response = response) match {

            case e: ZohoCommonAPIResponseStatusCodes.CommonCRMAPIError =>
              Left(GetCRMUsersError.CommonCRMAPIError(err = e.error))

            case e: ZohoCommonAPIResponseStatusCodes.UnknownError =>
              Left(GetCRMUsersError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = e.message)))

            case e =>
              Left(GetCRMUsersError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                msg = e.message,
                responseBody = response.body,
                responseCode = response.status
              )))

          }

        } else {


          val users = (response.json \ "users")

          val validateData = users.validate[Seq[ZohoUserResponse]]

          validateData match {

            case JsError(e) =>

              Logger.error(s"ZohoOAuth getAllUsers validation error: $response ::: ${response.body} ::: $e")
              Left(GetCRMUsersError.MalformedUsersResponseError("Malformed users response received from Zoho"))

            case JsSuccess(usersData, _) =>

              val tpUsers = usersData
                .map(user => {

                  IntegrationTPUsersList(
                    id = user.id,
                    email = user.email
                  )

                })

              Right(tpUsers)
          }

        }
      })
  }


  def me(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData)
        (implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger) = {

    val accessToken = accessTokenData.access_token
    val api_domain = accessTokenData.api_domain.get
    val url = s"${api_domain}/crm/v2/users?type=CurrentUser"

    Logger.debug(making_api_call_to_crm)
    ws.url(url)
      .addHttpHeaders(
        "Authorization" -> s"Zoho-oauthtoken $accessToken",
        "Content-Type" -> "application/json"
      )
      .get()
      .map(response => {

        if (!successStatusCodes.contains(response.status)) {

          Logger.error(s"ZohoOAuth FATAL CurrentUser error: $response ::: ${response.body} ")
          throw new Exception(s"ZohoOAuth CurrentUser error: $accessToken")

        } else {

          Logger.info(s"ZohoOAuth CurrentUser ${response.body}")

          val users = (response.json \ "users")

          val validateData = users.validate[Seq[ZohoUserResponse]]

          validateData match {

            case JsError(e) =>

              Logger.error(s"ZohoOAuth CurrentUser validation error: $response ::: ${response.body} ::: $e")
              throw new Exception(s"ZohoOAuth CurrentUser Js validation error: ${response.body} :: $e")

            case JsSuccess(usersData, _) =>

              usersData
                .map(user => {

                  IntegrationTPUser(
                    id = user.id,
                    email = user.email,
                    company_id = None,
                    owner_id = None
                  )

                }).head
          }

        }
      })
  }

  /*Doc: https://www.zoho.com/crm/developer/docs/api/v2/upsert-records.html */
  def createOrUpdateSingleContact(
                                   accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                   moduleType: IntegrationModuleType,
                                   prospect: ProspectObject,
                                   fieldMapping: UpdateFieldsMappingForm,
                                   userMapping: UpdateUserMappingForm,
                                   accountId: Long,
                                   teamId: Long,
                                   email: String,
//                                   emailNotCompulsoryEnabled: Boolean
                                 )(implicit ws: WSClient, ec: ExecutionContext,
                                   system: ActorSystem,
                                   Logger: SRLogger
                                 ): Future[Either[CreateOrUpdateSingleContactsError, String]] = {

    getCRMModuleType(moduleType = moduleType)
      .flatMap(zohoModule => {

        val contactsObj = buildCreateContactObj(
          moduleType = moduleType,
          prospects = Seq((prospect, None)),
          fieldMapping = fieldMapping,
          userMapping = userMapping,
          accountId = accountId,
          teamId = teamId
        )

        val api_domain = accessTokenData.api_domain.get

        val url = s"${api_domain}/crm/v2/$zohoModule/upsert"

        val accessToken = accessTokenData.access_token

        Logger.debug(making_api_call_to_crm)
        ws.url(url)
          .addHttpHeaders(
            "Authorization" -> s"Zoho-oauthtoken $accessToken",
            "Content-Type" -> "application/json"
          )
          .post(contactsObj)
          .map(response => {

            if (!isSuccessResponse(response)) {

              Logger.fatal(s"ZohoOAuth createOrUpdateSingleContact error: ${response} :: ${response.body} :: ${contactsObj} :: url :: ${url}")

              ZohoCommonAPIResponseStatusCodes.parseStatusCodes(response = response) match {

                case e: ZohoCommonAPIResponseStatusCodes.CommonCRMAPIError =>
                  Left(CreateOrUpdateSingleContactsError.CommonCRMAPIError(err = e.error))

                case e: ZohoCommonAPIResponseStatusCodes.UnknownError =>
                  Left(CreateOrUpdateSingleContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                    msg = e.message,
                    responseBody = e.responseBody,
                    responseCode = e.responseCode
                  )))

                case e =>
                  Left(CreateOrUpdateSingleContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = e.message)))

              }

            } else {

              val data = (response.json \ "data").asOpt[Seq[JsObject]]

              if (data.isDefined && data.get.nonEmpty) {

                val id = (data.get.head \ "details" \ "id").asOpt[String]

                if (id.isDefined) {

                  Logger.info(s"ZohoOAuth createOrUpdateSingleContact response id: ${id} :: ${response.body} :: ${contactsObj} :: url :: ${url}")

                  Right(id.get)

                } else {

                  Logger.error(s"ZohoOAuth createOrUpdateSingleContact response id not found:: ${response.body} :: ${contactsObj} :: url :: ${url}")

                  Left(CreateOrUpdateSingleContactsError.UnableToFindPersonIdError(msg = "Malformed response received from Zoho, Unable to find person id from the response"))

                }

              } else {

                Logger.error(s"ZohoOAuth createOrUpdateSingleContact response data is empty:: ${response.body} :: ${contactsObj} :: url :: ${url}")

                Left(CreateOrUpdateSingleContactsError.MalformedResponseStructureError(msg = "Malformed response received from Zoho"))

              }

            }

          })
      })

  }


  def getLeadStatusColumns(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData, moduleType: IntegrationModuleType)
                          (implicit ws: WSClient,
                           ec: ExecutionContext,
                           Logger: SRLogger
                          ): Future[Either[GetLeadStatusColumnsError, Seq[IntegrationTPColumns]]] = {

    getCRMModuleType(moduleType = moduleType)
      .flatMap(filedModule => {

        val s = AppConfig.zohoOAuthSettings
        val accessToken = accessTokenData.access_token
        val api_domain = accessTokenData.api_domain.get
        val url = s"${api_domain}/crm/v2/settings/fields"

        Logger.debug(making_api_call_to_crm)
        ws.url(url)
          .addHttpHeaders(
            "Authorization" -> s"Zoho-oauthtoken $accessToken"
          )
          .withQueryStringParameters(
            "module" -> filedModule
          )
          .get()
          .map(response => {

            if (!isSuccessResponse(response)) {

              Logger.error(s"ZohoOAuth FATAL getZohoColumns error: $response ::: ${response.body} ")

              ZohoCommonAPIResponseStatusCodes.parseStatusCodes(response = response) match {

                case e: ZohoCommonAPIResponseStatusCodes.CommonCRMAPIError =>
                  Left(GetLeadStatusColumnsError.CommonCRMAPIError(err = e.error))

                case e =>
                  Left(GetLeadStatusColumnsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                    msg = e.message,
                    responseBody = response.body,
                    responseCode = response.status
                  )))

              }

            } else {

              val fields = (response.json \ "fields")

              val validateData = fields.validate[Seq[ZohoColumnsResponse]]

              validateData match {

                case JsError(e) =>

                  Logger.error(s"ZohoOAuth FATAL getZohoColumns validation error: $response ::: ${response.body} ::: $e")

                  Left(GetLeadStatusColumnsError.MalformedResponseStructureError(message = "Malformed response received from Zoho"))

                case JsSuccess(columnsData, _) =>

                  __filterPickListLeadStatusColumns(moduleType = moduleType, columnsData = columnsData) match {

                    case Failure(e) =>
                      Left(GetLeadStatusColumnsError.InvalidModuleError(message = e.getMessage))

                    case Success(columnsData) =>

                      val tpColumns = columnsData
                        .map(c => {
                          IntegrationTPColumns(
                            col_id = c.api_name,
                            label = c.field_label,
                            description = None,
                            field_type = __getFieldTypeFromTpColumnFieldType(tp_column_field_type = c.data_type),
                            field_options = if (c.pick_list_values.isDefined) Some(c.pick_list_values.get.map(o => IntegrationTPDropdownColumnOptions(label = o.display_value, text_id = o.actual_value))) else None
                          )

                        })

                      Right(tpColumns)

                  }

              }

            }


          })

      })

  }

  /*API DOC Ref: https://www.zoho.com/crm/developer/docs/api/v2/update-records.html*/
  def updateLeadStatus(
                        accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                        moduleType: IntegrationModuleType,
                        person: CrmLeadStatusUpdate,
                      )(implicit ws: WSClient,
                        ec: ExecutionContext,
                        Logger: SRLogger
                      ): Future[Either[UpdateLeadStatusError, String]] = {

    val person_id = person.person_id
    val statusColumn = person.statusColumn
    val statusValue = person.statusValue

    val api_domain = accessTokenData.api_domain.get

    val url = s"${api_domain}/crm/v2/$moduleType/$person_id"

    val accessToken = accessTokenData.access_token

    val statusObject = Json.obj("data" -> Json.arr(
      Json.obj(
        statusColumn.column_name -> statusValue
      )
    ))

    Logger.debug(making_api_call_to_crm)
    ws.url(url)
      .addHttpHeaders(
        "Authorization" -> s"Zoho-oauthtoken $accessToken",
        "Content-Type" -> "application/json"
      )
      .put(statusObject)
      .map(response => {

        if (!isSuccessResponse(response)) {

          Logger.fatal(s"ZohoOAuth updateLeadStatus error: ${response} :: ${response.body}")

          ZohoCommonAPIResponseStatusCodes.parseStatusCodes(response = response) match {

            case e: ZohoCommonAPIResponseStatusCodes.CommonCRMAPIError =>
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = e.error))

            case e: ZohoCommonAPIResponseStatusCodes.UnknownError =>
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                msg = e.message,
                responseBody = e.responseBody,
                responseCode = e.responseCode
              )))

            case e =>
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = e.message)))

          }

        } else {

          Logger.info(s"ZohoOAuth updateLeadStatus response :: $response")

          Right(person_id)

        }

      })

  }

  def __filterPickListLeadStatusColumns(
                                         moduleType: IntegrationModuleType,
                                         columnsData: Seq[ZohoColumnsResponse]
                                       ): Try[Seq[ZohoColumnsResponse]] = Try {

    moduleType match {

      case IntegrationModuleType.LEADS =>

        /**
         * Filtering only Lead status column for module Leads
         */
        columnsData
          .filter(c => c.api_name == "Lead_Status")

      case IntegrationModuleType.CONTACTS =>

        /**
         * Filtering all picklist columns for module Contacts,
         * bcz there is no standard Status column exists for Contacts column
         * giving all picklist columns to choose user which column user wants to map for status update
         */
        columnsData
          .filter(c => c.data_type == "picklist")

      case IntegrationModuleType.CANDIDATES =>

        throw new Exception(s"Invalid module: $moduleType")
    }

  }


  def getCRMSpecificData(
                          response: Option[WSResponse] = None,
                          filteredContacts: Seq[JsValue] = Seq(),
                          tp_filter_id: Option[String]
                        ): CrmData = {
    CrmSpecificData.ZohoSpecificData(
      tp_filter_id = tp_filter_id
    )
  }
}


object ZohoOAuth {

  private case class ContactsToCreateMap(
                                          cmap: Map[Int, Seq[IntegrationContactResponse]],
                                        ) extends AnyVal

  private case class ContactsToUpdateMap(
                                          umap: Map[Int, Seq[IntegrationContactResponse]]
                                        ) extends AnyVal

  def extractQueryParams(prospect: ProspectObject): Seq[(String, String)] = {
    Seq(
      prospect.email.map("email" -> _),
      prospect.phone.filter(PhoneNumberValidation.isValidPhoneNumber).map("phone" -> _)
    ).flatten
  }


  private case class ContactsToCreateOrUpdateMaps(
                                                   contactsToCreateMap: Map[Int, Seq[IntegrationContactResponse]],
                                                   contactsToUpdateMap: Map[Int, Seq[IntegrationContactResponse]]
                                                 )


  private def combineResponseOfCreateAndUpdateContactsResult(
                                                              createContactsFuture: Future[Either[CreateOrUpdateBatchContactsError, Seq[BatchContactResponse]]],
                                                              updateContactsFuture: Future[Either[CreateOrUpdateBatchContactsError, Seq[BatchContactResponse]]]
                                                            )(implicit executionContext: ExecutionContext) = {

    for {
      createResult: Either[CreateOrUpdateBatchContactsError, Seq[BatchContactResponse]] <- createContactsFuture
      updateResult: Either[CreateOrUpdateBatchContactsError, Seq[BatchContactResponse]] <- updateContactsFuture
    } yield {
      createResult match {
        case Left(error) => Left(error)
        case Right(createdContacts) =>
          updateResult match {
            case Left(updateError) => Left(updateError)
            case Right(updatedContacts) =>
              Right(createdContacts ++ updatedContacts)
          }
      }
    }

  }


}
