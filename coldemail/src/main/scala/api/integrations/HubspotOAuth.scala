package api.integrations

import org.apache.pekko.actor.ActorSystem
import api.prospects.ProspectSource
import api.triggers._
import api.AppConfig
import api.accounts.TeamId
import api.columns.ProspectColumnDefUtils
import api.integrations.crmapis.HubspotApi
import api.integrations.models.hubspot.HubspotAPIResponseStatusCodes
import api.integrations.services.CrmLeadStatusUpdate
import api.internal_support.service.UpdateOrgMetadata
import api.prospects.models.ProspectId
import api.sr_audit_logs.models.ProspectObjectWithOldProspectDeduplicationColumn
import api.triggers.dao_service.TriggerDAOService
import eventframework.ProspectObject
import org.joda.time.{DateTime, Duration}
import play.api.libs.json._
import play.api.libs.ws.{WSClient, WSResponse}
import utils.mq.webhook.model.CreateInCRMJedisDAO
import utils.{Helpers, ParseUtils, SRLogger}

import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import play.api.libs.ws.WSBodyWritables.writeableOf_JsValue
import play.api.libs.ws.WSBodyWritables.writeableOf_urlEncodedForm

case class HubspotPicklistValuesResponse(
                                          label: String,
                                          value: String
                                        )

object HubspotPicklistValuesResponse {
  implicit val reads: Reads[HubspotPicklistValuesResponse] = Json.reads[HubspotPicklistValuesResponse]
}

case class HSColumnsResponse(
                              name: String,
                              label: String,
                              fieldType: String,
                              formField: Boolean,
                              calculated: Boolean,
                              readOnlyValue: Boolean,
                              displayOrder: Int,
                              description: String,
                              options: Option[Seq[HubspotPicklistValuesResponse]]
                            )

object HSColumnsResponse {
  implicit val reads: Reads[HSColumnsResponse] = Json.reads[HSColumnsResponse]
}

case class HSFiltersResponse(
                              listId: Long,
                              name: String
                            )

object HSFiltersResponse {
  implicit val reads: Reads[HSFiltersResponse] = Json.reads[HSFiltersResponse]
}

case class HSUserResponse(
                           id: String,
                           email: Option[String]
                         )

object HSUserResponse {
  implicit val reads: Reads[HSUserResponse] = Json.reads[HSUserResponse]
}

case class HubspotAccountResponse(
                                   hub_id: Long,
                                   user_id: Long,
                                   user: String,
                                   app_id: Long,
                                   owner_id: Option[Long]
                                 )

object HubspotAccountResponse {
  implicit val reads: Reads[HubspotAccountResponse] = Json.reads[HubspotAccountResponse]
}


case class HubspotOwnerResponseOld(
                                 ownerId: Long,
                                 email: String
                               )

object HubspotOwnerResponseOld {
  implicit val reads: Reads[HubspotOwnerResponseOld] = Json.reads[HubspotOwnerResponseOld]
}

case class HubspotOwnerResponseNew(
                                 id: String,
                                 email: String
                               )

object HubspotOwnerResponseNew {
  implicit val reads: Reads[HubspotOwnerResponseNew] = Json.reads[HubspotOwnerResponseNew]
}

class HubSpotOAuth(
                    val triggerDAO: Trigger,
                    val triggerServiceV2: TriggerServiceV2,
                    val triggerDAOService: TriggerDAOService,
                    hubspotApi: HubspotApi,
                    createInCRMJedisDAO: CreateInCRMJedisDAO
                  ) extends TIntegrationCRMTrait {


  private case class ProspectObjectWithContactId(
                                                  prospectObject: ProspectObject,
                                                  contactId: Option[String]
                                                  /*
                                                    Note: The above contactId Represents the contactId from hubspot , so it will be defined only when the
                                                    contact already exists in hubspot.

                                                    for the non enc flow email itself can be considered as contactId, so i defaulted this value to None,
                                                    for non enc flow and for the other flow(enc) if the contact is not found
                                                   */
                                                )

  override type CrmData = CrmSpecificData.HubspotSpecificData

  override val batchCreateOrUpdateLimit: Int = 100

  val making_api_call_to_crm = "making_api_call_to_crm_hubspot"


  val prospectSource: ProspectSource.Value = ProspectSource.HUBSPOT

  val name: IntegrationType = IntegrationType.HUBSPOT

  // In Hubspot API, we are getting the error: "You have reached your ten_secondly_rolling limit.",
  // thats why putting additional delay between retries
  val retryOnAPIErrorDelay: FiniteDuration = 10.seconds


  def authorizationUrl(state: String, is_sandbox: Boolean = false)(implicit ws: WSClient, ec: ExecutionContext): String = {
    val s = AppConfig.hubspotOAuthSettings

    val requestUrl = ws.url(s.authorizationURL)
      .addQueryStringParameters(
        "client_id" -> s.clientID,
        "redirect_uri" -> s.redirectURL,
        "scope" -> s.scope,
        "state" -> state
      )
      .uri
      .toString

    requestUrl
  }

  def refreshAccessToken(refreshTokenData: IntegrationTPAccessTokenResponse.RefreshTokenData)
                        (implicit ws: WSClient,
                         ec: ExecutionContext,
                         Logger: SRLogger
                        ): Future[Either[RefreshAccessTokenError, IntegrationTPAccessTokenResponse.FullTokenData]] = {


    val s = AppConfig.hubspotOAuthSettings

    val refreshToken = refreshTokenData.refresh_token.get

    val body: Map[String, Seq[String]] = Map(
      "client_id" -> Seq(s.clientID),
      "client_secret" -> Seq(s.clientSecret),
      "refresh_token" -> Seq(refreshToken),
      "grant_type" -> Seq("refresh_token")
    )

    Logger.debug(making_api_call_to_crm)
    ws.url(s.accessTokenURL)
      .post(body)
      .map(response => {

        if (!isSuccessResponse(response)) {

          Logger.error(s"FATAL HubSpotOAuth refreshAccessToken error: $response ::: ${response.body} :: accessTokenData: $refreshTokenData")

          Left(RefreshAccessTokenError.OAuthError(err = parseOAuthStatusCodes(response = response)))


        } else {
          //              Logger.info(s"HubSpotOAuth AccessTokenResponse ${response.json}")
          val validateData = response.json.validate[IntegrationTPAccessTokenResponse.FullTokenData]
          validateData match {
            case JsError(e) => {
              Logger.error(s"FATAL HubSpotOAuth AccessTokenResponse validation error:: ${response.body} :: $e")
              Left(RefreshAccessTokenError.MalformedRefreshAccessTokenResponseError("Malformed Refresh AccessToken response received from HubSpot"))
            }
            case JsSuccess(accessTokenData, _) => {

              Right(accessTokenData.copy(refresh_token = Some(refreshToken)))

            }
          }
        }
      })
  }

  def getAccessToken(code: String, location: Option[String], is_sandbox: Boolean)
                    (implicit ws: WSClient,
                     ec: ExecutionContext,
                     Logger: SRLogger
                    ): Future[Either[GetAccessTokenError, IntegrationTPAccessTokenResponse.FullTokenData]] = {

    val s = AppConfig.hubspotOAuthSettings

    val body: Map[String, Seq[String]] = Map(

      "code" -> Seq(code),
      "client_id" -> Seq(s.clientID),
      "client_secret" -> Seq(s.clientSecret),
      "redirect_uri" -> Seq(s.redirectURL),
      "grant_type" -> Seq("authorization_code"),
      "scope" -> Seq(s.scope)

    )

    Logger.debug(making_api_call_to_crm)
    ws.url(s.accessTokenURL)
      .post(body)
      .map(response => {

        if (!isSuccessResponse(response)) {

          Logger.error(s"FATAL HubSpotOAuth getAccessToken error: $response ::: ${response.body}")

          Left(GetAccessTokenError.OAuthError(err = parseOAuthStatusCodes(response = response)))

        } else {

          val validateData = response.json.validate[IntegrationTPAccessTokenResponse.FullTokenData]

          validateData match {

            case JsError(e) =>

              Logger.error(s"FATAL HubSpotOAuth getAccessToken validation error: $response ::: ${response.body} ::: $e")

              Left(GetAccessTokenError.MalformedAccessTokenResponseError("Malformed AccessToken response received from HubSpot"))

            case JsSuccess(accessTokenData, _) =>

              Right(accessTokenData)

          }

        }


      })
  }

  private def deleteLock(
                          contacts: Seq[JsValue],
                          teamId: TeamId,
                          module: IntegrationModuleType,
                          prospectId: ProspectId
                        )(using Logger: SRLogger) = {
    contacts.foreach(c => {

      createInCRMJedisDAO.deleteLock( // since the api call failed releasing the lock so that we can try without waiting
        prospectId = prospectId,
        teamId = teamId,
        crmType = IntegrationType.HUBSPOT,
        module = module
      )

    })
  }


  /*
  private def getCRMModuleType(moduleType: IntegrationModuleType): Future[String] = {
    moduleType match {

      case IntegrationModuleType.CONTACTS => Future.successful("Contacts")

      case IntegrationModuleType.LEADS => Future.failed(new Exception(s"Unsupported module: $moduleType"))

      case IntegrationModuleType.CANDIDATES => Future.failed(new Exception(s"Unsupported module: $moduleType"))

    }
  }
  */

  /*Ref Doc: https://legacydocs.hubspot.com/docs/methods/contacts/batch_create_or_update
  * Hubspot Note: The batch size should not exceed 1000 contacts per request.
  * any errors with a single contact in your batch will prevent the entire batch from processing
  */

  private def createOrUpdate(
                              moduleType: IntegrationModuleType,
                              accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                              contacts: Seq[JsValue],
//                              emailNotCompulsoryEnabled: Boolean,
                              isNew: Boolean,
                              teamId: TeamId
                            )(using Logger: SRLogger, ws: WSClient, ec: ExecutionContext): Future[Seq[Either[CreateOrUpdateBatchContactsError, BatchContactResponse]]] = {

    val accessToken = accessTokenData.access_token

    if (!isNew) {
      // Update Contact

      Future.sequence(contacts.map { contact =>

        hubspotApi.updateContactInHubspot(
          contact = contact,
          accessToken = accessToken,
          isSuccessResponse = isSuccessResponse
        )

      })


    } else {

      // create Contact

      Future.sequence(contacts.map { contact =>

        val prospectId = (contact \ "prospectId").as[Long]
        val made_create_call_in_last_hour = createInCRMJedisDAO.getLock(
          prospectId = ProspectId(prospectId),
          crmType = IntegrationType.HUBSPOT,
          teamId = teamId,
          module = moduleType
        )
        // Note: This lock will exists for one hour even for the success case
        if (!made_create_call_in_last_hour) {
          val isLockAcquired = createInCRMJedisDAO.acquireLockForProspect(
            prospectId = ProspectId(prospectId),
            teamId = teamId,
            crmType = IntegrationType.HUBSPOT,
            module = moduleType
          )
          if (isLockAcquired) {
            hubspotApi.createContactInHubspot(
              contact = contact,
              accessToken = accessToken,
              isSuccessResponse = isSuccessResponse
            ).map {
              case Left(value) => {
                // delete lock
                deleteLock(
                  contacts = Seq(contact),
                  teamId = teamId,
                  module = moduleType,
                  prospectId = ProspectId(prospectId)
                )
                Left(value)
              }
              case Right(value) => Right(value)

            }.recover { e =>
              deleteLock(
                contacts = Seq(contact),
                teamId = teamId,
                module = moduleType,
                prospectId = ProspectId(prospectId)
              )
              throw e
            }

          } else {
            Logger.info(s"Could not acquire Lock for the given contact prospectId: ${prospectId} teamId: ${teamId}")
            Future.successful(Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(CommonCRMAPIErrors.ContactCreationDelayInCRM(s"Could not acquire Lock for the given prospect pid:${prospectId}"))))
          }
        } else {
          Logger.info(s"Lock is already acquired by the contact prospectId ${prospectId} teamId ${teamId}")
          Future.successful(Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(CommonCRMAPIErrors.ContactCreationDelayInCRM(s"Lock is already acquired by prospect pid:${prospectId}"))))
        }
      })


    }

  }

  def createOrUpdateBatchContacts(
                                   moduleType: IntegrationModuleType,
                                   accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                   contacts: Seq[ProspectObjectWithOldProspectDeduplicationColumn],
                                   fieldMapping: UpdateFieldsMappingForm,
                                   userMapping: UpdateUserMappingForm,
                                   accountId: Long,
                                   teamId: Long,
//                                   emailNotCompulsoryEnabled: Boolean
                                 )(implicit ws: WSClient,
                                   ec: ExecutionContext,
                                   system: ActorSystem,
                                   Logger: SRLogger
                                 ): Future[Seq[Either[CreateOrUpdateBatchContactsError, BatchContactResponse]]] = {

    moduleType match {

      case IntegrationModuleType.CONTACTS =>

        val accessToken = accessTokenData.access_token

        // MUST BE "def", NOT "val"

        // what i am gonna do is simple

        /*
          if i have oldDeduplcation column value for the prospect then i will replace it with using.copy
          if it is not defined same prospect will go
         */

        batchSearchAndReturnJsValue(
          moduleType = moduleType,
          accessTokenData = accessTokenData,
          contacts = TIntegrationCRMTrait.mapToOldAndNewProspectObjects(contacts = contacts),
          fieldMapping = fieldMapping,
          userMapping = userMapping,
//          emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
        ) flatMap {

          case Left(SearchBatchContactsError.CommonCRMAPIError(err)) =>
            Future.successful(contacts.map(c => {
              Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(err))

            }))

          case Left(SearchBatchContactsError.HubspotSearchFailureError(message,failureMessages)) =>
            Future.successful(contacts.map(c => {
              Left(CreateOrUpdateBatchContactsError.HubspotSearchFailureError(message = message,failureMessages = failureMessages))
            }))


          case Left(SearchBatchContactsError.HubspotFailureBatchError(message, failureRecords)) =>
            Future.successful(contacts.map(c => {
              Left(CreateOrUpdateBatchContactsError.HubspotFailureBatchError(message, failureRecords))
            }))

          case Left(SearchBatchContactsError.SalesForceBatchError(message, failureRecords)) => {
            Logger.shouldNeverHappen("Should Never Happen, SalesforceBatch Error In Hubspot flow")

            Future.successful(contacts.map(c => {
              Left(CreateOrUpdateBatchContactsError.SalesForceFailureBatchError(message, failureRecords))
            }))
          }

          case Left(SearchBatchContactsError.InvalidModuleError(message)) =>
            Future.successful(contacts.map(c => {
              Left(CreateOrUpdateBatchContactsError.InvalidModuleError(message))
            }))


          case Left(SearchBatchContactsError.CRMFindOneByEmailAndModuleError(err)) =>
            Future.successful(contacts.map(c => {
              Left(CreateOrUpdateBatchContactsError.CRMFindOneByEmailAndModuleError(err))
            }))


          case Right(contactsObj) =>


              // NOTE: ENC case is enabled
              val finalContactsObj = (contactsObj.head \ "inputs").as[Seq[JsValue]]


              val contactsToUpdate = finalContactsObj.filter(c => {

                // This contactId below  is hubspot ContactId which we found when we searched for contacts using the
                // batchSearchForContacts Function 
                val contactId = (c \ "contactId").asOpt[String]
                contactId.isDefined
              })

              val contactsToCreate = finalContactsObj.filter(c => {
                val contactId = (c \ "contactId").asOpt[String]
                contactId.isEmpty
              })

              for {

                result1: Seq[Either[CreateOrUpdateBatchContactsError, BatchContactResponse]] <- if (contactsToUpdate.nonEmpty) {
                  createOrUpdate(
                    moduleType = moduleType,
                    accessTokenData = accessTokenData,
                    contacts = contactsToUpdate,
//                    emailNotCompulsoryEnabled = emailNotCompulsoryEnabled,
                    isNew = false,
                    teamId = TeamId(teamId)
                  )
                } else {
                  Future.successful(Seq())
                }

                result2: Seq[Either[CreateOrUpdateBatchContactsError, BatchContactResponse]] <- if (contactsToCreate.nonEmpty) {
                  createOrUpdate(
                    moduleType = moduleType,
                    accessTokenData = accessTokenData,
                    contacts = contactsToCreate,
//                    emailNotCompulsoryEnabled = emailNotCompulsoryEnabled,
                    isNew = true,
                    teamId = TeamId(teamId)
                  )
                } else {
                  Future.successful(Seq())
                }

              } yield {
                result1 ++ result2
              }


//            }


        }


      case IntegrationModuleType.LEADS |
           IntegrationModuleType.CANDIDATES =>

        Logger.fatal(s"$name createOrUpdateBatchContacts error: module: $moduleType not supported")

        Future.successful(contacts.map(c =>
          Left(CreateOrUpdateBatchContactsError.InvalidModuleError(message = s"Invalid module $moduleType"))
        ))
    }
  }


  /*Ref Doc: https://legacydocs.hubspot.com/docs/methods/contacts/create_or_update*/
  def createOrUpdateSingleContact(
                                   accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                   moduleType: IntegrationModuleType,
                                   prospect: ProspectObject,
                                   fieldMapping: UpdateFieldsMappingForm,
                                   userMapping: UpdateUserMappingForm,
                                   accountId: Long,
                                   teamId: Long,
                                   email: String,
//                                   emailNotCompulsoryEnabled: Boolean
                                 )(implicit ws: WSClient, ec: ExecutionContext,
                                   system: ActorSystem,
                                   Logger: SRLogger
                                 ): Future[Either[CreateOrUpdateSingleContactsError, String]] = {
    batchSearchAndReturnJsValue(
      moduleType = moduleType,
      accessTokenData = accessTokenData,
      contacts = Seq(OldAndNewProspectObject(OldProspectObject(prospectObject = prospect), newProspectObject = NewProspectObject(prospectObject = prospect))),
      fieldMapping = fieldMapping,
      userMapping = userMapping,
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
    ) flatMap {

      case Left(err) =>
        Logger.fatal(s"batchSearchAndReturnJsValue Error for createOrUpdateSingleContact err:: ${err.message}")
        err match {
          case SearchBatchContactsError.CommonCRMAPIError(err) =>
            Future.successful(Left(CreateOrUpdateSingleContactsError.CommonCRMAPIError(err)))

          case SearchBatchContactsError.HubspotSearchFailureError(message,_) =>
            Future.successful(Left(CreateOrUpdateSingleContactsError.CommonCRMAPIError(CommonCRMAPIErrors.UnknownError(msg = message))))

          case SearchBatchContactsError.HubspotFailureBatchError(message, failureRecords) =>
            Future.successful(Left(CreateOrUpdateSingleContactsError.MalformedResponseStructureError(message)))

          case SearchBatchContactsError.SalesForceBatchError(message, failureRecords) => {
            Logger.shouldNeverHappen("Should Never Happen, SalesforceBatch Error In Hubspot flow")
            Future.successful(Left(CreateOrUpdateSingleContactsError.MalformedResponseStructureError(message)))
          }


          case SearchBatchContactsError.InvalidModuleError(message) =>
            Future.successful(Left(CreateOrUpdateSingleContactsError.InvalidModuleError(message)))

          case SearchBatchContactsError.CRMFindOneByEmailAndModuleError(err) =>
            Future.successful(Left(CreateOrUpdateSingleContactsError.CRMFindOneByEmailAndModuleError(err)))
        }

      case Right(contactsObj) =>

        Logger.debug(s"batchSearchAndReturnJsValue Success for createOrUpdateSingleContact")

        val contactUrl = "https://api.hubapi.com/crm/v3/objects/contacts"
        val accessToken = accessTokenData.access_token

        val contactItems = (contactsObj.head \ "inputs" ).as[Seq[JsValue]] // there is supposed to be 1 item only here

        if(contactItems.nonEmpty){
          val propertiesJsonObj = contactItems.head

        // must be "def", not "val"
        def wsReq = {
          Logger.debug(making_api_call_to_crm)
          ws.url(contactUrl)
            .addHttpHeaders(
              "Authorization" -> s"Bearer $accessToken",
              "Content-Type" -> "application/json"
            )
            .post(propertiesJsonObj)
        }

        retryOnServerError(
          wsReq,
          delay = retryOnAPIErrorDelay
        )
          .map(response => {

            if (!isSuccessResponse(response)) {

              Logger.fatal(s"HubSpotOAuth createOrUpdateSingleContact error: ${response.body}")

              HubspotAPIResponseStatusCodes.parseStatusCodes(response = response) match {

                case e: HubspotAPIResponseStatusCodes.CommonCRMAPIError =>
                  Left(CreateOrUpdateSingleContactsError.CommonCRMAPIError(err = e.error))

                case e: HubspotAPIResponseStatusCodes.UnknownError =>
                  Left(CreateOrUpdateSingleContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                    msg = e.message,
                    responseBody = e.responseBody,
                    responseCode = e.responseCode
                  )))

                case e =>
                  Left(CreateOrUpdateSingleContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = e.message)))
              }

            } else {

              println(s"HubSpotOAuth createOrUpdateSingleContact response :: ${response.body}")

              // v3 API returns "id" instead of "vid"
              val id = (response.json \ "id").asOpt[String]

              if (id.isDefined) {

                Right(id.get)

              } else {

                Logger.error(s"HubSpotOAuth createOrUpdateSingleContact response vid not found:: ${response.body}")

                Left(CreateOrUpdateSingleContactsError.UnableToFindPersonIdError(msg = "Malformed response received from Hubspot, Unable to find vid from the response"))

              }

            }


          })
       } else {
          Logger.shouldNeverHappen(s"Atleast one contact should always be present here prospectEmail :: ${email} tid ${teamId}")
          Future.successful(Left(CreateOrUpdateSingleContactsError.UnableToFindPersonIdError(msg = "No Contact found ")))
        }
    }
  }

  /*
  //NOT USING, NOT enabled for Hubspot
  def createOrUpdateBatchLeads(accessTokenData: IntegrationTPAccessTokenResponse,
                               leads: Seq[Prospect],
                               fieldMapping: UpdateFieldsMappingForm,
                               accountId: Long,
    teamId: Long
  )(implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger) = {

    Logger.fatal(s"HubSpotOAuth createOrUpdateBatchLeads not supported")
    Future.failed(new Exception("Hubspot leads not supported"))


  }
  */


  /*Ref Doc: https://legacydocs.hubspot.com/docs/methods/contacts/get_contact_by_email*/
  def findOneByEmailAndModule(
                               accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                               email: String,
                               module: IntegrationModuleType,
                               statusColumn: Option[String]
                             )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem,
                               Logger: SRLogger
                             ): Future[Either[FindOneByEmailAndModuleError, Option[IntegrationContactResponse]]] = {

    //TODO: In Hubspot we're supporting CONTACTS only when we support LEADS this condition will be removed
    module match {

      case IntegrationModuleType.CONTACTS =>

        val contactUrl = s"https://api.hubapi.com/contacts/v1/contact/email/${email}/profile?property=hs_lead_status&property=email&property=hubspot_owner_id"
        val accessToken = accessTokenData.access_token

        Logger.debug(making_api_call_to_crm)
        ws.url(contactUrl)
          .addHttpHeaders(
            "Authorization" -> s"Bearer $accessToken",
            "Content-Type" -> "application/json"
          )
          .get()
          .map(response => {

            if (response.status == 200) {

              val contact = response.json

              val email = (contact \ "properties" \ "email" \ "value").asOpt[String]
              val status = (contact \ "properties" \ "hs_lead_status" \ "value").asOpt[String]
              val owner_id = (contact \ "properties" \ "hubspot_owner_id" \ "value").asOpt[String]
              // commenting out large frequently running log
              Logger.info(s"HubSpotOAuth findOneByEmailAndModule response.status :: ${response.status} ::email : $email status :: $status")

              val id = (contact \ "vid").as[Long]

              Right(
                Some(
                  IntegrationContactResponse(
                    id = id.toString,
                    email = email,
                    status = status,
                    owner_id = owner_id,
                    phone = None
                  )
                )
              )

            } else if (response.status == 404) {

              Logger.info(s"HubSpotOAuth findOneByEmailAndModule not found: ${response.body} :: status: ${response.status} :: email: $email url :: ${contactUrl}")

              Right(None)

            } else {

              Logger.error(s"HubSpotOAuth findOneByEmailAndModule error: ${response.body} :: status: ${response.status} :: email: $email url :: ${contactUrl}")

              HubspotAPIResponseStatusCodes.parseStatusCodes(response = response) match {

                case e: HubspotAPIResponseStatusCodes.CommonCRMAPIError =>
                  Left(FindOneByEmailAndModuleError.CommonCRMAPIError(err = e.error))

                case e =>
                  Left(FindOneByEmailAndModuleError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                    msg = e.message,
                    responseBody = response.body,
                    responseCode = response.status
                  )))

              }

            }


          })

      case IntegrationModuleType.LEADS |
           IntegrationModuleType.CANDIDATES =>

        Logger.fatal(s"$name findOneByEmailAndModule error: module: $module not supported")

        Future.successful(
          Left(FindOneByEmailAndModuleError.InvalidModuleError(msg = s"Invalid Module: $module"))
        )

    }

  }


  def findAllByProspectAndModule(
                                  accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                  prospect: ProspectObject,
                                  module: IntegrationModuleType,
                                  statusColumn: Option[String]
                                )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem,
                                  Logger: SRLogger
                                ): Future[Either[FindBatchError, Seq[IntegrationContactResponse]]] = {

    //TODO: In Hubspot we're supporting CONTACTS only when we support LEADS this condition will be removed
    module match {

      case IntegrationModuleType.CONTACTS =>


        val contactUrl = s"https://api.hubapi.com/crm/v3/objects/contacts/search"
        val accessToken = accessTokenData.access_token

        val filterGroups = HubSpotOAuth.getFilterGroupsByContacts(contacts = Seq(prospect))

        ws.url(contactUrl)
          .addHttpHeaders(
            "Authorization" -> s"Bearer $accessToken",
            "Content-Type" -> "application/json"
          )
          .post(Json.obj(
            "filterGroups" -> filterGroups,
            "properties" -> Json.arr("id", "firstname", "lastname", "email", "phone", "linkedin_url", "hs_lead_status", "hubspot_owner_id")
          ))
          .map(response => {

            if (response.status == 200) {


              val contacts = (response.json \ "results").as[Seq[JsValue]]


              val result = contacts.map(contact => {
                val email = (contact \ "properties" \ "email").asOpt[String]
                val status = (contact \ "properties" \ "hs_lead_status").asOpt[String]
                val phone = (contact \ "properties" \ "phone").asOpt[String]

                val owner_id = (contact \ "properties" \ "hubspot_owner_id").asOpt[String]
                // commenting out large frequently running log
                Logger.info(s"$name findAllByProspectAndModule response.status :: ${response.status} ::email : $email :: phone :: ${phone} status :: $status")

                val id = (contact \ "id").as[String]

                IntegrationContactResponse(
                  id = id,
                  email = email,
                  status = status,
                  owner_id = owner_id,
                  phone = phone
                )

              })

              Right(result)


            } else if (response.status == 404) {

              Logger.info(s"$name findAllByProspectAndModule not found: ${response.body} :: status: ${response.status} :: prospectId:${prospect.id} :: email: ${prospect.email} :: phone: ${prospect.phone} :: url :: ${contactUrl}")

              /*
                Note:
                 If No Prospect is found we just create the prospect, thats why returning Right
               */
              Right(Seq())

            } else {

              Logger.error(s"$name findAllByProspectAndModule not found: ${response.body} :: status: ${response.status} :: prospectId:${prospect.id} :: email: ${prospect.email} :: phone: ${prospect.phone} :: url :: ${contactUrl}")

              HubspotAPIResponseStatusCodes.parseStatusCodes(response = response) match {

                case e: HubspotAPIResponseStatusCodes.CommonCRMAPIError =>
                  Left(FindBatchError.CommonCRMAPIError(err = e.error))

                case e =>
                  Left(FindBatchError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                    msg = e.message,
                    responseBody = response.body,
                    responseCode = response.status
                  )))

              }

            }
          })


      case IntegrationModuleType.LEADS |
           IntegrationModuleType.CANDIDATES =>

        Logger.fatal(s"$name findAllByProspectAndModule error: module: $module not supported")

        Future.successful(
          Left(FindBatchError.InvalidModuleError(msg = s"Invalid Module: $module"))
        )

    }

  }

  //Ref: https://legacydocs.hubspot.com/docs/methods/engagements/create_engagement
  protected def updateActivityInCRM(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                    person_id: String,
                                    activitySubject: String,
                                    activityType: TPActivityType.Value,
                                    detailedActivitySubject: String,
                                    activityAt: DateTime,
                                    emailSubject: Option[String],
                                    emailSenderAddress: Option[String],
                                    emailSenderFirstName: Option[String],
                                    emailSenderLastName: Option[String],
                                    emailBody: Option[String],
                                    emailTextBody: Option[String],
                                    module: IntegrationModuleType,
                                    owner_id: Option[String]
                                   )(implicit ws: WSClient,
                                     ec: ExecutionContext,
                                     Logger: SRLogger
                                   ): Future[Either[UpdateActivityError, String]] = {

    val activityUrl = "https://api.hubapi.com/engagements/v1/engagements"
    val accessToken = accessTokenData.access_token


    val activity_type = if (activityType == TPActivityType.EMAIL) "EMAIL" else "NOTE"
    val activity = if (activityType == TPActivityType.EMAIL) {

      if (emailSenderAddress.isEmpty || emailSenderFirstName.isEmpty || emailSenderLastName.isEmpty || emailSubject.isEmpty || emailBody.isEmpty || emailTextBody.isEmpty) {
        Logger.fatal(s"HubSpotOAuth updateActivity empty email data: emailSenderAddress: $emailSenderAddress :: emailSenderFirstName: $emailSenderFirstName :: emailSenderLastName $emailSenderLastName :: emailSubject: $emailSubject :: emailBody: $emailBody :: emailTextBody $emailTextBody")
      }

      val emlSenderAddress = emailSenderAddress.getOrElse("")
      val emlSenderFirstName = emailSenderFirstName.getOrElse("")
      val emlSenderLastName = emailSenderLastName.getOrElse("")
      val emlSubject = s"$activitySubject ${emailSubject.getOrElse("")}"
      val emlBody = emailBody.getOrElse("")
      val emlTextBody = emailTextBody.getOrElse("")

      owner_id match {
        case Some(value) =>
          Json.obj(
            "engagement" -> Json.obj(
              "active" -> true,
              "ownerId" -> value.toLong,
              "type" -> activity_type,
              "timestamp" -> activityAt.getMillis
            ),
            "associations" -> Json.obj(
              "contactIds" -> Json.arr(person_id),
              "companyIds" -> Json.arr(),
              "dealIds" -> Json.arr(),
              "ownerIds" -> Json.arr()
            ),

            "metadata" -> Json.obj(
              "from" -> Json.obj(
                "email" -> emlSenderAddress,
                "firstName" -> emlSenderFirstName,
                "lastName" -> emlSenderLastName
              ),
              "to" -> Json.arr(),
              "cc" -> Json.arr(),
              "bcc" -> Json.arr(),
              "subject" -> emlSubject,
              "html" -> emlBody,
              "text" -> emlTextBody
            )
          )

        case None =>
          Json.obj(
            "engagement" -> Json.obj(
              "active" -> true,
              "type" -> activity_type,
              "timestamp" -> activityAt.getMillis
            ),
            "associations" -> Json.obj(
              "contactIds" -> Json.arr(person_id),
              "companyIds" -> Json.arr(),
              "dealIds" -> Json.arr(),
              "ownerIds" -> Json.arr()
            ),

            "metadata" -> Json.obj(
              "from" -> Json.obj(
                "email" -> emlSenderAddress,
                "firstName" -> emlSenderFirstName,
                "lastName" -> emlSenderLastName
              ),
              "to" -> Json.arr(),
              "cc" -> Json.arr(),
              "bcc" -> Json.arr(),
              "subject" -> emlSubject,
              "html" -> emlBody,
              "text" -> emlTextBody
            )
          )

      }


    } else {

      Json.obj(
        "engagement" -> Json.obj(
          "active" -> true,
          //          "ownerId" -> 1, //NOTE: ownerId is option field what we need here is contactIds and body
          "type" -> activity_type,
          "timestamp" -> activityAt.getMillis
        ),
        "associations" -> Json.obj(
          "contactIds" -> Json.arr(person_id),
          "companyIds" -> Json.arr(),
          "dealIds" -> Json.arr(),
          "ownerIds" -> Json.arr()
        ),
        "metadata" -> Json.obj(
          "body" -> activitySubject
        )
      )

    }

    Logger.debug(making_api_call_to_crm)
    ws.url(activityUrl)
      .addHttpHeaders(
        "Authorization" -> s"Bearer $accessToken",
        "Content-Type" -> "application/json"
      )
      .post(activity)
      .map(response => {

        if (!isSuccessResponse(response)) {

          Logger.fatal(s"HubSpotOAuth updateActivityInCRM error: ${response.body}")

          HubspotAPIResponseStatusCodes.parseStatusCodes(response = response) match {

            case e: HubspotAPIResponseStatusCodes.CommonCRMAPIError =>
              Left(UpdateActivityError.CommonCRMAPIError(err = e.error))

            case e: HubspotAPIResponseStatusCodes.UnknownError =>
              Left(UpdateActivityError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                msg = e.message,
                responseBody = e.responseBody,
                responseCode = e.responseCode
              )))

            case e =>
              Left(UpdateActivityError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = e.message)))
          }

        } else {

          Logger.info(s"HubSpotOAuth updateActivityInCRM response :: $response")

          Right(person_id)

        }


      })

  }


  protected def hasMoreContactsToSync(dataResponse: WSResponse, moduleType: IntegrationModuleType) = {
    (dataResponse.json \ "has-more").get.as[Boolean]
  }

  //TODO this would changed to modifiedAt
  protected def findLeastaddedAtToSync(data: Seq[JsValue])(using Logger: SRLogger) = {

    val contact = data
      .reverse
      .head

    //    (contact \ "addedAt").get.as[Long]
    ParseUtils.parseLong((contact \ "properties" \ "lastmodifieddate" \ "value").asOpt[String].getOrElse("")).get

  }

  //TODO this would changed to modifiedAt
  protected def findLatestaddedAtToSync(data: Seq[JsValue])(using Logger: SRLogger) = {

    val contact = data.head

    //    (contact \ "addedAt").get.as[Long]
    ParseUtils.parseLong((contact \ "properties" \ "lastmodifieddate" \ "value").asOpt[String].getOrElse("")).get

  }

  protected def fetchRecentContactsFromCRM(
                                            accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                            page: Long,
                                            moduleType: IntegrationModuleType,

                                            forSampleData: Boolean,
                                            crmSpecificData: CrmData


                                          )(implicit ws: WSClient,
                                            ec: ExecutionContext,
                                            system: ActorSystem,
                                            Logger: SRLogger
                                          ): Future[Either[FetchRecentContactsError, WSResponse]] = {

    moduleType match {

      case IntegrationModuleType.CONTACTS =>

        val validatedTpFilterId = isValidTpFilterId(tp_filter_id = crmSpecificData.tp_filter_id)
        val contactUrl = if (forSampleData)
          s"https://api.hubapi.com/contacts/v1/lists/all/contacts/recent"
        else if (validatedTpFilterId.isDefined)
          //TODO should be v3 contact search by filter id and sort by lastmodifieddate currently v3 not have by list_id filter
          //ref: https://developers.hubspot.com/docs/api/crm/search
          s"https://api.hubapi.com/contacts/v1/lists/${validatedTpFilterId.get}/contacts/recent"
        else
          //          s"https://api.hubapi.com/contacts/v1/lists/all/contacts/recent"
          "https://api.hubapi.com/contacts/v1/search/query"
        //          "https://api.hubapi.com/contacts/v1/lists/recently_updated/contacts/recent"

        val accessToken = accessTokenData.access_token

        val defaultCount = if (forSampleData) 1 else 100; //Ref link https://developers.hubspot.com/docs/methods/contacts/get_recently_created_contacts

        // must be "def", not "val"
        def wsRequest = if (crmSpecificData.hubspotVidOffset.isDefined && crmSpecificData.hubspotTimeOffset.isDefined) {
          Logger.debug(making_api_call_to_crm)
          ws.url(contactUrl)
            .addHttpHeaders(
              "Authorization" -> s"Bearer $accessToken",
              "Content-Type" -> "application/json"
            )
            .addQueryStringParameters(
              "vidOffset" -> crmSpecificData.hubspotVidOffset.get.toString,
              "timeOffset" -> crmSpecificData.hubspotTimeOffset.get.toString,
              "count" -> s"$defaultCount"
            )
            .get()
        } else if (forSampleData) {

          Logger.debug(making_api_call_to_crm)
          ws.url(contactUrl)
            .addHttpHeaders(
              "Authorization" -> s"Bearer $accessToken",
              "Content-Type" -> "application/json"
            )
            .addQueryStringParameters(
              "count" -> s"$defaultCount"
            )
            .get()

        } else {

          Logger.info(s"\n\n\n page $page contactUrl $contactUrl defaultCount $defaultCount offset ${defaultCount * page} \n\n\n\n\n\n")
          Logger.debug(making_api_call_to_crm)
          ws.url(contactUrl)
            .addHttpHeaders(
              "Authorization" -> s"Bearer $accessToken",
              "Content-Type" -> "application/json"
            )
            .addQueryStringParameters(
              "count" -> s"$defaultCount",
              "sort" -> "lastmodifieddate",
              "offset" -> s"${defaultCount * page}"
            )
            .get()
        }


        wsRequest
          .map(response => {

            if (!isSuccessResponse(response)) {

              Logger.fatal(s"HubSpotOAuth fetchRecentContactsFromCRM error: ${response.body}")

              HubspotAPIResponseStatusCodes.parseStatusCodes(response = response) match {

                case e: HubspotAPIResponseStatusCodes.CommonCRMAPIError =>
                  Left(FetchRecentContactsError.CommonCRMAPIError(err = e.error))

                case e: HubspotAPIResponseStatusCodes.UnknownError =>
                  Left(FetchRecentContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                    msg = e.message,
                    responseBody = e.responseBody,
                    responseCode = e.responseCode
                  )))

                case e =>
                  Left(FetchRecentContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = e.message)))
              }

            } else {

              Logger.info(s"HubSpotOAuth fetchRecentContactsFromCRM response :: $response")

              Right(response)

            }

          })

      case IntegrationModuleType.LEADS |
           IntegrationModuleType.CANDIDATES =>

        Future.failed(new Exception(s"fetchRecentContactsFromCRM not supported for $name $moduleType"))

    }
  }

  protected def getFilteredContactsFromTPResponse(
                                                   responseJson: JsValue,
                                                   fieldMapping: UpdateFieldsMappingForm,
                                                   accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                                   moduleType: IntegrationModuleType
                                                 )
                                                 (implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem,
                                                  Logger: SRLogger
                                                 ): Future[Seq[JsValue]] = {

    moduleType match {

      case IntegrationModuleType.CONTACTS =>

        val contacts = (responseJson \ "contacts").get.as[Seq[JsValue]]

        if (contacts.nonEmpty) {

          var vids = s"vid=${(contacts.head \ "vid").as[Long]}"
          contacts.drop(1).foreach(c => {
            val vid = (c \ "vid").as[Long]
            vids = s"$vids&vid=${vid}"
          })
          val url = s"https://api.hubapi.com/contacts/v1/contact/vids/batch/?${vids}"

          // must be "def", not "val"
          def wsReq = {
            Logger.debug(making_api_call_to_crm)
            ws.url(url)
              .addHttpHeaders(
                "Authorization" -> s"Bearer ${accessTokenData.access_token}",
                "Content-Type" -> "application/json"
              )
              .get()
          }

          retryOnServerError(
            wsReq,
            delay = retryOnAPIErrorDelay
          )
            .map(response => {


              if (!isSuccessResponse(response)) {

                Logger.fatal(s"HubspotOAuth.getFilteredContactsFromTPResponse: batchUrl: $url :: batchResponse: ${response.body} :: resstatus: ${response} : ${response.status}")
                val error = (response.json \ "message").asOpt[String]

                throw new Exception(s"${error.getOrElse("Error while fetching Hubspot data")}")


              } else {


                val filterdContacts = (response.json).as[JsObject].values.toSeq
                  .sortBy(c => {
                    ParseUtils.parseLong((c \ "properties" \ "lastmodifieddate" \ "value").asOpt[String].getOrElse(""))
                  })
                  .toList
                  .reverse


                filterdContacts
              }
            })

        } else {

          Logger.fatal(s"HubSpotOAuth getFilteredContactsFromTPResponse empty error :: responseJson: $responseJson")

          Future.failed(new Exception(s"FATAL HubSpotOAuth getFilteredContactsFromTPResponse empty error :: responseJson: ${responseJson}"))

        }

      case IntegrationModuleType.LEADS |
           IntegrationModuleType.CANDIDATES =>

        Future.failed(new Exception(s"FATAL $name getFilteredContactsFromTPResponse unsupported module: $moduleType"))

    }
  }

  /*
  //NOT USING NOT enabled in hubspot
  protected def fetchRecentLeadsFromCRM(
                            accessTokenData: IntegrationTPAccessTokenResponse,
                            page: Long,
                            trigger: TriggerInDB,

                            hubspotVidOffset: Option[Long] = None,
                            hubspotTimeOffset: Option[Long] = None


                          )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem,
    Logger: SRLogger
  ): Future[WSResponse] = {

    Logger.fatal(s"HubspotOauth.makeRecentLeadsWS not supported: trigger: $trigger")
    Future.failed(new Exception(s"fetchRecentLeadsFromCRM not supported for $name"))

  }


  //NOT USING NOT enabled in hubspot
  def getFilteredLeadsFromTPResponse(responseJson: JsValue, fieldMapping: UpdateFieldsMappingForm, accessToken: String)(implicit ws: WSClient, ec: ExecutionContext,
    Logger: SRLogger
  ): Future[Seq[JsValue]] = {

    Logger.fatal(s"HubSpotOAuth getFilteredLeadsFromTPResponse not supported")
    Future.failed(new Exception("Hubspot leads not supported"))
  }
  */

  protected def getValueFromTPContactObj(contact: JsValue, destField: Option[TriggerFields])(using Logger: SRLogger): Option[String] = Try {
    if (destField.isEmpty) None else {

      val destFieldValue = destField.get.tp_field match {

        case "email" =>

          (contact \ "identity-profiles" \ 0 \ "identities").get.as[Seq[JsValue]].find(ident => {

            (ident \ "type").as[String].toLowerCase.trim == destField.get.tp_field

          }).map(ident => (ident \ "value").as[String])

        case _ =>

          (contact \ "properties" \ destField.get.tp_field \ "value").asOpt[String]

      }

      if (
        (destFieldValue.isEmpty || destFieldValue.get.trim.isEmpty) &&
          destField.get.tp_field_default_value.nonEmpty &&
          destField.get.tp_field_default_value.get.trim.nonEmpty)
        destField.get.tp_field_default_value
      else
        destFieldValue
    }
  } match {
    case Failure(e) =>

      Logger.fatal(s"HubspotOAuth.getValueFromTPContactObj: contact: $contact :: destField: $destField", err = e)
      throw e

    case Success(res) =>

      res
  }

  protected def getTpOwnerIdValueFromTPContactObj(contact: JsValue)
                                                 (using Logger: SRLogger): Option[String] = {
    (contact \ "portal-id").asOpt[String]
  }


  protected def getFilteredContactsFromTPResponseForFrontEnd(filteredContact: JsValue)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem, Logger: SRLogger): Future[JsValue] = {

    val properties = (filteredContact \ "properties").as[JsValue]

    Try {
      properties.asInstanceOf[JsObject].keys
    } match {

      case Failure(e) =>
        Logger.fatal(s"HubspotOAuth.getFilteredContactsFromTPResponseForFrontEnd: filteredContact: $filteredContact", err = e)
        Future.successful(Json.obj())

      case Success(keys) =>
        var updatedContactObj = Json.obj()
        keys.foreach(key => {
          val value = (properties \ key \ "value").as[String]
          updatedContactObj = updatedContactObj ++ Json.obj(key -> value)
        })

        Future.successful(updatedContactObj)

    }
  }

  def getTPColumns(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData, module_type: Option[String])(implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger) = {

    val s = AppConfig.hubspotOAuthSettings
    val contactUrl = "https://api.hubapi.com/properties/v1/contacts/properties"
    val accessToken = accessTokenData.access_token
    Logger.debug(making_api_call_to_crm)
    ws.url(contactUrl)
      .addHttpHeaders(
        "Authorization" -> s"Bearer $accessToken",
        "Content-Type" -> "application/json"
      )
      .get()
      .map(response => {

        if (!isSuccessResponse(response)) {

          Logger.error(s"HubSpotOAuth getHubSpotColumns error: $response ::: ${response.body}")

          val error = (response.json \ "message").asOpt[String]

          throw new Exception(s"${error.getOrElse("Error while fetching fields")}")

        } else {

          val validateData = response.json.validate[Seq[HSColumnsResponse]]

          validateData match {

            case JsError(e) =>

              Logger.error(s"HubSpotOAuth getHubSpotColumns validation error: $response ::: ${response.body} ::: $e")
              throw new Exception(s"HubSpotOAuth getHubSpotColumns Js validation error: ${response.body} :: $e")

            case JsSuccess(columnsData, _) =>

              columnsData
                .filter(c =>
                  !c.calculated &&
                    !c.readOnlyValue &&
                    c.formField &&
                    s.allowdedFieldTypes.contains(c.fieldType)
                )
                .sortBy(c => {

                  if (c.name == "email") {
                    -100
                  } else if (c.displayOrder >= 0) {
                    c.displayOrder
                  } else {
                    10000
                  }

                })
                .map(c => {

                  IntegrationTPColumns(
                    col_id = c.name,
                    label = c.label,
                    description = Some(c.description),
                    field_type = __getFieldTypeFromTpColumnFieldType(tp_column_field_type = c.fieldType)
                  )

                })
          }

        }


      })

  }


  def getTPFilters(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData, module_type: Option[String])
                  (implicit ws: WSClient,
                   ec: ExecutionContext,
                   Logger: SRLogger
                  ): Future[Either[GetCRMFiltersError, Seq[IntegrationTPFilters]]] = {

    val s = AppConfig.hubspotOAuthSettings

    val defaultCount = 250
    val contactUrl = "https://api.hubapi.com/contacts/v1/lists"
    val accessToken = accessTokenData.access_token

    /*
    * Adding recursive API request here because HubSpot return max 250 in one API
    * REF: https://developers.hubspot.com/docs/methods/lists/get_lists
    * */
    def fetchFilters(offset: Int, page: Int, fountTpFilters: Seq[IntegrationTPFilters]): Future[Either[GetCRMFiltersError, Seq[IntegrationTPFilters]]] = {

      /*
      * Adding upper bound limit for 20 iterations (20*250)
      * 18th Nov 2022 - adding the page because offset not comming in order like 0->250->500 checking upper bound limit on page (iterations)
      * */
      if (page > 20) {

        Logger.error(s"[HubSpotOAuth] getHubSpotFilters [FATAL] error: called 20 times in loop: fountTpFilters: ${fountTpFilters.length} : offset: $offset")
        Future.successful(Left(GetCRMFiltersError.UpperBoundHitError(message = "Hubspot filters limit reached")))

      } else {
        Logger.debug(making_api_call_to_crm)
        ws.url(contactUrl)
          .addHttpHeaders(
            "Authorization" -> s"Bearer $accessToken",
            "Content-Type" -> "application/json"
          )
          .addQueryStringParameters(
            "count" -> s"$defaultCount",
            "offset" -> s"$offset"
          )
          .get()
          .flatMap(response => {

            if (!isSuccessResponse(response)) {

              Logger.error(s"HubSpotOAuth getHubSpotFilters error: $response ::: ${response.body}")

              HubspotAPIResponseStatusCodes.parseStatusCodes(response = response) match {

                case e: HubspotAPIResponseStatusCodes.CommonCRMAPIError =>
                  Future.successful(Left(GetCRMFiltersError.CommonCRMAPIError(err = e.error)))

                case e =>
                  Future.successful(
                    Left(GetCRMFiltersError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                      msg = e.message,
                      responseBody = response.body,
                      responseCode = response.status
                    )))
                  )
              }

            } else {


              //Logger.info(s"HubSpotOAuth getHubSpotFilters ${response.body}")
              val filters = (response.json \ "lists")
              val offset = (response.json \ "offset").asOpt[Int]
              val hasMore = (response.json \ "has-more").asOpt[Boolean]

              val validateData = filters.validate[Seq[HSFiltersResponse]]

              validateData match {

                case JsError(e) =>

                  Logger.error(s"HubSpotOAuth getHubSpotFilters validation error: $response ::: ${response.body} ::: $e")
                  throw new Exception(s"HubSpotOAuth getHubSpotFilters Js validation error: ${response.body} :: $e")

                case JsSuccess(filtersData, _) =>

                  val tpFilters = fountTpFilters ++ filtersData
                    .map(f => {

                      IntegrationTPFilters(
                        id = f.listId.toString,
                        name = f.name,
                        display_name = f.name
                      )

                    })

                  if (hasMore.isDefined && hasMore.get && offset.isDefined) {
                    fetchFilters(offset = offset.get, page = page + 1, fountTpFilters = tpFilters)
                  } else {
                    Future.successful(Right(tpFilters))
                  }


              }

            }


          })
      }
    }

    fetchFilters(offset = 0, page = 0, fountTpFilters = Seq())

  }


  private def buildCreateContactObj(
                                     moduleType: IntegrationModuleType,
                                     prospectObjectWithContactId: ProspectObjectWithContactId,
                                     fieldMapping: UpdateFieldsMappingForm,
                                     userMapping: UpdateUserMappingForm,
                                     isNew: Boolean
                                   )(using Logger: SRLogger): JsValue = {

    moduleType match {
      case IntegrationModuleType.CONTACTS =>

        val conatactObj = {

          var properties = fieldMapping.fields.get
            .filter(f => f.sr_field.trim.nonEmpty && f.tp_field.trim.nonEmpty)
            .map(f => {
              Json.obj(
                "property" -> f.tp_field,
                "value" -> ProspectColumnDefUtils._getProspectFieldValue(p = prospectObjectWithContactId.prospectObject, field = f)
              )
            })

          /*
          FIXED NOTE: commented out this because of confusion regarding ownership
          currently campaign owner is becoming the contact owner in hubspot, even though the prospect owner in SR might be someone else

          THIS IS FIXED
          */
          //      if (ownerId.isDefined) {
          //        properties = properties ++ Seq(Json.obj(
          //          "property" -> "hubspot_owner_id",
          //          "value" -> ownerId.get
          //        ))
          //      }

          val owner = findMappedUserByProspectOwnerIdForCRMContactObj(owner_id = prospectObjectWithContactId.prospectObject.owner_id, userMapping = userMapping)
          if (owner.isDefined && isNew) {
            properties = properties ++ Seq(Json.obj(
              "property" -> "hubspot_owner_id",
              "value" -> owner.get.tp_user_id
            ))
          }


          if (prospectObjectWithContactId.contactId.isDefined) {
            Json.obj(
              "vid" -> prospectObjectWithContactId.contactId.get,
              "phone" -> prospectObjectWithContactId.prospectObject.phone,
              "linkedin_url" -> prospectObjectWithContactId.prospectObject.linkedin_url,
              "properties" -> properties

            )
          } else {
            Json.obj(
              "email" -> prospectObjectWithContactId.prospectObject.email,
              "phone" -> prospectObjectWithContactId.prospectObject.phone,
              "linkedin_url" -> prospectObjectWithContactId.prospectObject.linkedin_url,
              "properties" -> properties

            )

          }

        }

        Json.toJson(conatactObj)

      case IntegrationModuleType.LEADS |
           IntegrationModuleType.CANDIDATES =>

        Logger.fatal(s"$name buildCreateContactObj not supported for module: $moduleType ")

        Json.obj()
    }

  }


  private def buildCreateContactObjNew(
                                        moduleType: IntegrationModuleType,
                                        prospect: ProspectObject,
                                        fieldMapping: UpdateFieldsMappingForm,
                                        userMapping: UpdateUserMappingForm,
                                        isNew: Boolean,
                                        contactId: Option[String]
                                      )(using Logger: SRLogger): JsValue = {

    moduleType match {
      case IntegrationModuleType.CONTACTS =>

        val conatactObj = {

          var properties = fieldMapping.fields.get
            .filter(f => f.sr_field.trim.nonEmpty && f.tp_field.trim.nonEmpty)
            .foldLeft(Json.obj()) { (acc, f) =>
              acc ++ Json.obj(
                f.tp_field -> ProspectColumnDefUtils._getProspectFieldValue(p = prospect, field = f)
              )
            }

          /*
          FIXED NOTE: commented out this because of confusion regarding ownership
          currently campaign owner is becoming the contact owner in hubspot, even though the prospect owner in SR might be someone else

          THIS IS FIXED
          */
          //      if (ownerId.isDefined) {
          //        properties = properties ++ Seq(Json.obj(
          //          "property" -> "hubspot_owner_id",
          //          "value" -> ownerId.get
          //        ))
          //      }

          val owner = findMappedUserByProspectOwnerIdForCRMContactObj(owner_id = prospect.owner_id, userMapping = userMapping)
          if (owner.isDefined && isNew) {
            properties = properties ++ Json.obj(
              "hubspot_owner_id" -> owner.get.tp_user_id
            )
          }


          if (contactId.isDefined) {
            Json.obj(
              "properties" -> properties,
              "contactId" -> contactId.get,
              "prospectId" -> prospect.id
            )
          } else {
            Json.obj(
              "properties" -> properties,
              "prospectId" -> prospect.id

            )
          }
        }

        Json.toJson(conatactObj)

      case IntegrationModuleType.LEADS |
           IntegrationModuleType.CANDIDATES =>

        Logger.fatal(s"$name buildCreateContactObj not supported for module: $moduleType")

        Json.obj()
    }

  }

  /*
  //NOT USING, NOT enabled for Hubspot
  def buildCreateLeadsObj(prospects: Seq[Prospect], fieldMapping: UpdateFieldsMappingForm, accountId: Long, teamId: Long
  )(using Logger: SRLogger): JsValue = {

    Logger.error(s"FATAL HubSpotOAuth buildCreateLeadsObj not supported: aid_$accountId tid_$teamId")
    Json.obj()

  }
  */

  /*Ref Doc: https://legacydocs.hubspot.com/docs/methods/oauth2/delete-refresh-token*/
  def Uninstall(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData, teamId: Long)
               (implicit ws: WSClient,
                ec: ExecutionContext,
                Logger: SRLogger
               ): Future[Either[UninstallError, Int]] = {

    val accessToken = accessTokenData.access_token
    val revokeUrl = s"https://api.hubapi.com/oauth/v1/refresh-tokens/${accessTokenData.refresh_token}"
    Logger.debug(making_api_call_to_crm)
    ws.url(revokeUrl)
      .addHttpHeaders(
        "Authorization" -> s"Bearer $accessToken",
        "Content-Type" -> "application/json"
      )
      .delete()
      .map(response => {


        if (!isSuccessResponse(response)) {

          Logger.fatal(s"HubSpotOAuth Uninstall error teamId: $teamId :: accessTokenData: $accessTokenData")

          Left(UninstallError.OAuthError(err = parseOAuthStatusCodes(response = response)))

        } else {

          Logger.info(s"HubSpotOAuth Uninstall success teamId: $teamId :: accessTokenData: $accessTokenData")

          Right(1)

        }

      })

  }


  def me(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData)
        (implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger): Future[IntegrationTPUser] = {

    val access_token = accessTokenData.access_token
    val accountUrl = s"https://api.hubapi.com/oauth/v1/access-tokens/$access_token"

    Logger.debug(making_api_call_to_crm)
    ws.url(accountUrl)
      .addHttpHeaders(
        "Authorization" -> s"Bearer ${access_token}",
        "Content-Type" -> "application/json"
      )
      .get()
      .flatMap(response => {

        if (!isSuccessResponse(response)) {

          Logger.error(s"HubSpotOAuth /me error: ${response.body} :: status: ${response.status} :: url :: ${accountUrl}")

          throw new Exception(s"HubSpotOAuth /me error: ${response.body}")

        } else {

          val account = response.json

          val validateData = account.validate[HubspotAccountResponse]

          validateData match {

            case JsError(e) =>

              Logger.error(s"HubSpotOAuth /me validation error: $response ::: ${response.body} ::: $e")

              throw new Exception(s"HubSpotOAuth /me  validation error: ${response.body}")

            case JsSuccess(accountResponse, _) =>

              var account = accountResponse
              //NOTE doing getOwnerByEmail bcz owner_id is not getting in /me
              getOwnerById(id = accountResponse.user_id.toString, accessTokenData = accessTokenData
              )(Logger = Logger.appendLogRequestId("getOwnerByEmail"), ws = ws, ec = ec).map(owner => {

                IntegrationTPUser(
                  id = account.user_id.toString,
                  email = account.user,
                  company_id = Some(account.hub_id.toString),
                  owner_id = Some(owner.id)
                )

              })
          }
        }

      })

  }

    def getOwnerByEmail(email: String, accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData)
                             (implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger): Future[HubspotOwnerResponseOld] = {

    val access_token = accessTokenData.access_token
    val accountUrl = s"https://api.hubapi.com/owners/v2/owners/?email=$email"

    Logger.debug(making_api_call_to_crm)
    ws.url(accountUrl)
      .addHttpHeaders(
        "Authorization" -> s"Bearer ${access_token}",
        "Content-Type" -> "application/json"
      )
      .get()
      .map(response => {

        if (!isSuccessResponse(response)) {

          Logger.error(s"HubSpotOAuth getOwnerByEmail error: ${response.body} :: status: ${response.status} :: url :: ${accountUrl}")

          throw new Exception(s"HubSpotOAuth getOwnerByEmail error: ${response.body}")

        } else {

          val owner = response.json.head

          val validateData = owner.validate[HubspotOwnerResponseOld]

          validateData match {

            case JsError(e) =>

              Logger.error(s"HubSpotOAuth getOwnerByEmail validation error: $response ::: ${response.body} ::: $e")

              throw new Exception(s"HubSpotOAuth getOwnerByEmail  validation error: ${response.body}")

            case JsSuccess(ownerResponse, _) =>

              ownerResponse

          }


        }

      })

  }


   def getOwnerById(id: String, accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData)
                          (implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger): Future[HubspotOwnerResponseNew] = {

    val access_token = accessTokenData.access_token
    /*
      Refer this : 
      https://developers.hubspot.com/docs/reference/api/crm/owners
     */
    val accountUrl = s"https://api.hubapi.com/crm/v3/owners/${id}?idProperty=userId"

    Logger.debug(making_api_call_to_crm)
    ws.url(accountUrl)
      .addHttpHeaders(
        "Authorization" -> s"Bearer ${access_token}",
        "Content-Type" -> "application/json"
      )
      .get()
      .map(response => {

        if (!isSuccessResponse(response)) {

          Logger.error(s"HubSpotOAuth getOwnerByEmail error: ${response.body} :: status: ${response.status} :: url :: ${accountUrl}")

          throw new Exception(s"HubSpotOAuth getOwnerByEmail error: ${response.body}")

        } else {

          val owner = response.json
          val validateData = owner.validate[HubspotOwnerResponseNew]

          validateData match {

            case JsError(e) =>

              Logger.error(s"HubSpotOAuth getOwnerByEmail validation error: $response ::: ${response.body} ::: $e")

              throw new Exception(s"HubSpotOAuth getOwnerByEmail  validation error: ${response.body}")

            case JsSuccess(ownerResponse, _) =>

              ownerResponse

          }


        }

      })

  }


  def getTPUsers(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData)
                (implicit ws: WSClient,
                 ec: ExecutionContext,
                 Logger: SRLogger
                ): Future[Either[GetCRMUsersError, Seq[IntegrationTPUsersList]]] = {

    val s = AppConfig.hubspotOAuthSettings
    /*
    Refer this :
    https://developers.hubspot.com/docs/reference/api/crm/owners
     */
    val contactUrl = "https://api.hubapi.com/crm/v3/owners"
    val accessToken = accessTokenData.access_token

    Logger.debug(making_api_call_to_crm)
    ws.url(contactUrl)
      .addHttpHeaders(
        "Authorization" -> s"Bearer $accessToken",
        "Content-Type" -> "application/json"
      )
      .get()
      .map(response => {

        if (!isSuccessResponse(response)) {

          Logger.error(s"HubSpotOAuth getAllUsers error: $response ::: ${response.body}")

          HubspotAPIResponseStatusCodes.parseStatusCodes(response = response) match {

            case e: HubspotAPIResponseStatusCodes.CommonCRMAPIError =>
              Left(GetCRMUsersError.CommonCRMAPIError(err = e.error))

            case e: HubspotAPIResponseStatusCodes.UnknownError =>
              Left(GetCRMUsersError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = e.message)))

            case e =>
              Left(GetCRMUsersError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                msg = e.message,
                responseBody = response.body,
                responseCode = response.status
              )))

          }

        } else {

          // this was a big log
          // Logger.info(s"HubSpotOAuth getAllUsers ${response.body}")

          val users = response.json \ "results"

          val validateData = users.validate[Seq[HSUserResponse]]

          validateData match {

            case JsError(e) =>

              Logger.error(s"HubSpotOAuth getAllUsers validation error: $response ::: ${response.body} ::: $e")
              Left(GetCRMUsersError.MalformedUsersResponseError("Malformed users response received from HubSpot"))

            case JsSuccess(usersData, _) =>

              val tpUsers = usersData
                .filter(u => u.email.isDefined && u.email.get.nonEmpty)
                .map(user => {

                  IntegrationTPUsersList(
                    id = user.id,
                    email = user.email.get
                  )

                })

              Right(tpUsers)
          }
        }
      })
  }


  /*API DOC Ref: https://legacydocs.hubspot.com/docs/methods/contacts/update_contact*/
  def updateLeadStatus(
                        accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                        moduleType: IntegrationModuleType,
                        person: CrmLeadStatusUpdate,
                      )(implicit ws: WSClient,
                        ec: ExecutionContext,
                        Logger: SRLogger
                      ): Future[Either[UpdateLeadStatusError, String]] = {

    val person_id = person.person_id
    val statusColumn = person.statusColumn
    val statusValue = person.statusValue

    val contactUrl = s"https://api.hubapi.com/$moduleType/v1/contact/vid/$person_id/profile"
    val accessToken = accessTokenData.access_token


    val statusJson: JsObject = Json.obj(
      "property" -> statusColumn.column_name,
      "value" -> statusValue
    )
    val statusObject: JsObject = person.owner_id match {
      case Some(value) =>

        Json.obj("properties" -> Json.arr(
          statusJson,
          Json.obj(
            "property" -> "hubspot_owner_id",
            "value" -> value
          )
        ))
      case None =>

        Json.obj("properties" -> Json.arr(
          statusJson
        ))
    }


    Logger.debug(making_api_call_to_crm)
    ws.url(contactUrl)
      .addHttpHeaders(
        "Authorization" -> s"Bearer $accessToken",
        "Content-Type" -> "application/json"
      )
      .post(statusObject)
      .map(response => {

        if (!isSuccessResponse(response)) {

          Logger.fatal(s"HubSpotOAuth updateLeadStatus error: ${response.body}")

          HubspotAPIResponseStatusCodes.parseStatusCodes(response = response) match {

            case e: HubspotAPIResponseStatusCodes.CommonCRMAPIError =>
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = e.error))

            case e: HubspotAPIResponseStatusCodes.UnknownError =>
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                msg = e.message,
                responseBody = e.responseBody,
                responseCode = e.responseCode
              )))

            case e =>
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = e.message)))
          }

        } else {

          Logger.info(s"HubSpotOAuth updateLeadStatus response :: $response")

          Right(person_id)

        }

      })

  }

  def getLeadStatusColumns(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData, moduleType: IntegrationModuleType)
                          (implicit ws: WSClient,
                           ec: ExecutionContext,
                           Logger: SRLogger
                          ): Future[Either[GetLeadStatusColumnsError, Seq[IntegrationTPColumns]]] = {

    val s = AppConfig.hubspotOAuthSettings
    val contactUrl = s"https://api.hubapi.com/properties/v1/$moduleType/properties"
    val accessToken = accessTokenData.access_token

    Logger.debug(making_api_call_to_crm)
    ws.url(contactUrl)
      .addHttpHeaders(
        "Authorization" -> s"Bearer $accessToken",
        "Content-Type" -> "application/json"
      )
      .get()
      .map(response => {

        if (!isSuccessResponse(response)) {

          Logger.error(s"HubSpotOAuth getLeadStatusColumns error: $response ::: ${response.body}")

          HubspotAPIResponseStatusCodes.parseStatusCodes(response = response) match {

            case e: HubspotAPIResponseStatusCodes.CommonCRMAPIError =>
              Left(GetLeadStatusColumnsError.CommonCRMAPIError(err = e.error))

            case e =>
              Left(GetLeadStatusColumnsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                msg = e.message,
                responseBody = response.body,
                responseCode = response.status
              )))

          }

        } else {

          val validateData = response.json.validate[Seq[HSColumnsResponse]]

          validateData match {

            case JsError(e) =>

              Logger.error(s"HubSpotOAuth getLeadStatusColumns validation error: $response ::: ${response.body} ::: $e")

              Left(GetLeadStatusColumnsError.MalformedResponseStructureError(message = "Malformed response received from HubSpot"))

            case JsSuccess(columnsData, _) =>

              /**
               * Filtering only Lead status column with id hs_lead_status
               * In HubSpot also Lead status is a standard column
               */


              __filterPickListLeadStatusColumns(moduleType = moduleType, columnsData = columnsData) match {

                case Failure(e) =>
                  Left(GetLeadStatusColumnsError.InvalidModuleError(message = e.getMessage))

                case Success(columnsData) =>

                  val tpColumns = columnsData
                    .map(c => {
                      IntegrationTPColumns(
                        col_id = c.name,
                        label = c.label,
                        description = None,
                        field_type = c.fieldType,
                        field_options = Some(c.options.get.map(o => IntegrationTPDropdownColumnOptions(label = o.label, text_id = o.value)))
                      )

                    })

                  Right(tpColumns)

              }


          }

        }
      })

  }


  private def __filterPickListLeadStatusColumns(
                                                 moduleType: IntegrationModuleType,
                                                 columnsData: Seq[HSColumnsResponse]
                                               ): Try[Seq[HSColumnsResponse]] = Try {

    moduleType match {

      case IntegrationModuleType.CONTACTS =>

        /**
         * Filtering only Lead status column with id hs_lead_status
         * In HubSpot also Lead status is a standard column
         */

        columnsData
          .filter(c => c.name == "hs_lead_status" && c.options.isDefined && c.options.get.nonEmpty)

      case IntegrationModuleType.LEADS |
           IntegrationModuleType.CANDIDATES =>

        throw new Exception(s"Invalid module: $moduleType")

    }

  }

  /*"propertyValidationResult": { "isValid": false, "message": "Email address mukkojusatish+hstest23gmail.com is invalid", "error": "INVALID_EMAIL", "name": "email" }*/


  def getCRMSpecificData(
                          response: Option[WSResponse] = None,
                          filteredContacts: Seq[JsValue] = Seq(),
                          tp_filter_id: Option[String]
                        ): CrmData = {

    val hubspot_vid_offset = response.flatMap(a => (a.json \ "vid-offset").asOpt[Long])
    val hubspot_time_offset = response.flatMap(a => (a.json \ "time-offset").asOpt[Long])
    CrmSpecificData.HubspotSpecificData(
      hubspotVidOffset = hubspot_vid_offset,
      hubspotTimeOffset = hubspot_time_offset,
      tp_filter_id = isValidTpFilterId(tp_filter_id = tp_filter_id)
    )
  }


  def batchSearchAndReturnJsValue(
                                   moduleType: IntegrationModuleType,
                                   accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                   contacts: Seq[OldAndNewProspectObject],
                                   fieldMapping: UpdateFieldsMappingForm,
                                   userMapping: UpdateUserMappingForm,
//                                   emailNotCompulsoryEnabled: Boolean
                                 )(implicit ws: WSClient,
                                   ec: ExecutionContext,
                                   system: ActorSystem,
                                   Logger: SRLogger
                                 ): Future[Either[SearchBatchContactsError, Seq[JsValue]]] = {


//    println(s"emailNotCompulsoryEnabled ${emailNotCompulsoryEnabled}")
    println(s"contacts here ${contacts}")
    val result =  {
      hubspotApi.batchSearchForProspects(
        moduleType = moduleType,
        accessTokenData = accessTokenData,
        contacts = contacts.map(_.oldProspectObject.prospectObject),
        isSuccessResponse = isSuccessResponse,
        retryOnServerError = retryOnServerError,
        retryOnAPIErrorDelay = retryOnAPIErrorDelay
      )(Logger = Logger.appendLogRequestId("batchSearchForProspects"), ws = ws, ec = ec, system = system) map {
        case Left(value) => Left(value)
        case Right(value) => {
          val prospectFieldToContactIdMap = value.zipWithIndex.flatMap { case (batchContact, index) =>
            val resultingKeyValuePairs = Seq(
              batchContact.email.flatMap(email => batchContact.contactId.map(contactId => email -> contactId)),
              batchContact.phone.flatMap(phone => batchContact.contactId.map(contactId => phone -> contactId)),
              batchContact.linkedinUrl.flatMap(url => batchContact.contactId.map(contactId => url -> contactId))
            ).flatten // Remove None values

            resultingKeyValuePairs
          }.toMap


          val searchEmails = value.map(_.email).filter(_.isDefined).map(_.get.trim.toLowerCase)
          val searchPhones = value.map(_.phone).filter(_.isDefined).map(_.get.trim)
          val searchLinkedinUrl = value.map(_.linkedinUrl).filter(_.isDefined).map(_.get.trim)

          //Contacts which arrive in this path comes from ProspectDAOService.find() which is having inner join prospects_emails
          //in the old path email cannot be none but in the new path(email optional) the email field may be none for some prospects
          //TODO: For new path we may need to pass contacts fetched by linkedin_url/phone/company-firstname-lastname) so that we can isolate the cases
          //NOTE: We may need to add org_id to check old/new paths


          //TODO: EMAIL_OPTIONAL check the path and test code (contacts coming fromDb so old path will always have email)
          val newContacts: Seq[ProspectObjectWithContactId] = contacts.filterNot(contact =>
            (contact.oldProspectObject.prospectObject.email.isDefined && searchEmails.contains(contact.oldProspectObject.prospectObject.email.get.trim.toLowerCase))
              ||
              (contact.oldProspectObject.prospectObject.phone.isDefined && searchPhones.contains(contact.oldProspectObject.prospectObject.phone.get.trim))
              ||
              (contact.oldProspectObject.prospectObject.linkedin_url.isDefined && searchLinkedinUrl.contains(contact.oldProspectObject.prospectObject.linkedin_url.get.trim))

          ).distinct.map(c => ProspectObjectWithContactId(
            prospectObject = c.oldProspectObject.prospectObject,
            contactId = None
          ))


          //TODO: EMAIL_OPTIONAL check the path and test code (contacts coming fromDb so old path will always have email)
          val existingContacts: Seq[ProspectObjectWithContactId] = contacts.filter(contact =>

            // FIXME This code is same as of new contacts , extract it out 
            (contact.oldProspectObject.prospectObject.email.isDefined && searchEmails.contains(contact.oldProspectObject.prospectObject.email.get.trim.toLowerCase))
              ||
              (contact.oldProspectObject.prospectObject.phone.isDefined && searchPhones.contains(contact.oldProspectObject.prospectObject.phone.get.trim))
              ||
              (contact.oldProspectObject.prospectObject.linkedin_url.isDefined && searchLinkedinUrl.contains(contact.oldProspectObject.prospectObject.linkedin_url.get.trim))
          ).distinct.map(c => {

            val contactId = HubSpotOAuth.getContactId(prospect = c.oldProspectObject.prospectObject, prospectFieldToContactIdMap = prospectFieldToContactIdMap)
            ProspectObjectWithContactId(
              prospectObject = c.oldProspectObject.prospectObject,
              contactId = contactId
            )
          })



          // FIXME : the length should match with the original contacts  - check the distinct thing also 

          Right((newContacts, existingContacts))


        }
      }


    }

    result map {
      case Left(value) => {
        Left(value)
      }
      case Right(value) =>


        val prospectMap: Map[Long, ProspectObject] =
          contacts.map(oldAndNewProspectObject =>
            oldAndNewProspectObject.oldProspectObject.prospectObject.id -> oldAndNewProspectObject.newProspectObject.prospectObject
          ).toMap


//        if (emailNotCompulsoryEnabled) {


        val newContactsObj: Seq[JsValue] = value._1.map(c => {
          buildCreateContactObjNew(
            moduleType = moduleType,
            prospect = prospectMap(c.prospectObject.id), // it will be defined,
            fieldMapping = fieldMapping,
            userMapping = userMapping,
            isNew = true,
            contactId = None
          )
        })

        val existingContactsObj: Seq[JsValue] = value._2.map(c => {
          buildCreateContactObjNew(
            moduleType = moduleType,
            prospect = prospectMap(c.prospectObject.id),
            fieldMapping = fieldMapping,
            userMapping = userMapping,
            isNew = false,
            contactId = c.contactId
          )
        })

        Right(Seq(Json.obj(
          "inputs" -> (newContactsObj ++ existingContactsObj)
        )))


//        } else {
//
//          val newContactsObj = value._1.map(c => {
//            buildCreateContactObj(
//              moduleType = moduleType,
//              prospectObjectWithContactId = c,
//              fieldMapping = fieldMapping,
//              userMapping = userMapping,
//              isNew = true
//            )
//          })
//
//          val existingContactsObj = value._2.map(c => {
//            buildCreateContactObj(
//              moduleType = moduleType,
//              prospectObjectWithContactId = c,
//              fieldMapping = fieldMapping,
//              userMapping = userMapping,
//              isNew = false
//            )
//          })
//
//          Right(newContactsObj ++ existingContactsObj)
//
//
//        }

    }

  }

}


object HubSpotOAuth {


  def getContactId(
                    prospect: ProspectObject,
                    prospectFieldToContactIdMap: Map[String, String]
                  ): Option[String] = {

    if (prospect.email.isDefined && prospectFieldToContactIdMap.contains(prospect.email.get)) {
      prospectFieldToContactIdMap.get(prospect.email.get)
    } else if (prospect.phone.isDefined && prospectFieldToContactIdMap.contains(prospect.phone.get)) {
      prospectFieldToContactIdMap.get(prospect.phone.get)
    } else if (prospect.linkedin_url.isDefined && prospectFieldToContactIdMap.contains(prospect.linkedin_url.get)) {
      prospectFieldToContactIdMap.get(prospect.linkedin_url.get)
    } else {
      None
    }

  }

  def getFilterGroupsByContacts(contacts: Seq[ProspectObject]): JsArray = {
    val emailContacts = contacts.filter(_.email.isDefined).map(_.email.get)
    val phoneContacts = contacts.filter(_.phone.isDefined).map(_.phone.get)
    if (emailContacts.nonEmpty && phoneContacts.nonEmpty) {
      Json.arr(
        Json.obj(
          "filters" -> Json.arr(
            Json.obj(
              "propertyName" -> "email",
              "operator" -> "IN",
              "values" -> contacts.map(_.email).filter(_.isDefined).map(_.get)
            )
          )
        ),
        Json.obj(
          "filters" -> Json.arr(
            Json.obj(
              "propertyName" -> "phone",
              "operator" -> "IN",
              "values" -> contacts.map(_.phone).filter(_.isDefined).map(_.get)
            )
          )
        )

      )

    } else if (emailContacts.isEmpty) {
      Json.arr(
        Json.obj(
          "filters" -> Json.arr(
            Json.obj(
              "propertyName" -> "phone",
              "operator" -> "IN",
              "values" -> contacts.map(_.phone).filter(_.isDefined).map(_.get)
            )
          )
        )
      )

    } else {

      Json.arr(
        Json.obj(
          "filters" -> Json.arr(
            Json.obj(
              "propertyName" -> "email",
              "operator" -> "IN",
              "values" -> contacts.map(_.email).filter(_.isDefined).map(_.get)
            )
          )
        )
      )

    }
  }

}
