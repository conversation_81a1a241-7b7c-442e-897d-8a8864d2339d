package api.columns


import org.apache.commons.lang3.StringEscapeUtils
import scalikejdbc._
import utils.StringUtils

import scala.util.Try


object ProspectAccountsColumnDef extends ColumnDefTrait {


  val defaultColumns = List(
    ColumnDef(
      display_name = "Name",
      name = "name",
      tag_name = Some("account.name"),
      field_type = FieldTypeEnum.TEXT,
      show_in_datagrid = true,
      sortable = true,

      filterable = true,
      allowed_filter_operators = textFieldOperators

    ),

    ColumnDef(
      display_name = "Custom ID",
      name = "custom_id",
      tag_name = Some("account.custom_id"),
      field_type = FieldTypeEnum.TEXT,
      show_in_datagrid = true,
      sortable = true,

      filterable = true,
      allowed_filter_operators = textFieldOperators

    ),

    ColumnDef(
      display_name = "Owner",
      name = "owner_name",
      field_type = FieldTypeEnum.TEXT,
      show_in_datagrid = true,
      show_only_in_all_view = true
    ),

    ColumnDef(
      display_name = "Description",
      name = "description",
      field_type = FieldTypeEnum.TEXT,
      show_in_datagrid = true,
      sortable = true,

      filterable = true,
      allowed_filter_operators = textFieldOperators

    ),

    ColumnDef(
      display_name = "Industry",
      name = "industry",
      tag_name = Some("account.industry"),
      field_type = FieldTypeEnum.TEXT,
      show_in_datagrid = true,
      sortable = true,

      filterable = true,
      allowed_filter_operators = textFieldOperators

    ),

    ColumnDef(
      display_name = "Website",
      name = "website",
      field_type = FieldTypeEnum.TEXT,
      show_in_datagrid = true,
      sortable = true,

      filterable = true,
      allowed_filter_operators = textFieldOperators

    ),

    ColumnDef(
      display_name = "LinkedIn URL",
      name = "linkedin_url",
      field_type = FieldTypeEnum.TEXT,
      show_in_datagrid = true,
      sortable = true,

      filterable = true,
      allowed_filter_operators = textFieldOperators

    ),

    ColumnDef(
      display_name = "Total prospects",
      name = "total_prospects",
      show_in_datagrid = true,
      field_type = FieldTypeEnum.NUMBER,
      sortable = false,

      filterable = false,
      allowed_filter_operators = numberFieldOperators
    )

  )

  val allowedInternalMergeTags: List[ColumnDef] = defaultColumns.filter(col => col.tag_name.isDefined && !col.show_in_datagrid)

  // do not show these fields on the form in ui
  val ignoreFieldsOnUpdateProspectAccount = Seq(
    "id",
    "owner_id",
    "owner_name",
    "total_prospects"
  )

  // do not show these fields on the form in ui
  val ignoreFieldsOnAddOrUploadProspectAccount: Seq[String] = ignoreFieldsOnUpdateProspectAccount ++ Seq(
    ""
  )

  def findCustomColumns(teamId: Long) = {
    __findCustomColumns(teamId = teamId, columnDefType = ColumnDefTypeEnum.ACCOUNT)
  }

  def allColumns(teamId: Long): List[ColumnDef] = {

    val customColumns = findCustomColumns(teamId = teamId)

    val allColumns = defaultColumns ++ customColumns

    allColumns
  }

  def customColumns(allColumns: Seq[ColumnDef]): Seq[ColumnDef] = {

    allColumns
      .filter(_.is_custom)
  }

  def editableColumns(allColumns: Seq[ColumnDef]): Seq[ColumnDef] = {

    allColumns
      .filter(_.show_in_datagrid)

  }


  def getAvailableTagNames(teamId: Long): List[String] = {

    val prospectColumnTagNames = allColumns(teamId = teamId)
      .filter(_.tag_name.isDefined)
      .map(_.name)

    val internalMergeTagNames = allowedInternalMergeTags.map(_.name)

    val allTagNames = (prospectColumnTagNames ++ internalMergeTagNames).distinct

    allTagNames
  }

/* Commented out on 12-3-22 these tags are not access-able by the template so commenting out till it is provided
  def getKVMapForProspectAccountTemplate(
    prospectAccount: Option[ProspectAccount]
  ): util.HashMap[java.lang.String, Object] = {

    val pmap = new util.HashMap[java.lang.String, Object]

    if (prospectAccount.isEmpty) {

      pmap.put("account.name", null)
      pmap.put("account.custom_id", null)
      pmap.put("account.industry", null)

    } else {

      val pa = prospectAccount.get

      pmap.put("account.name", pa.name)
      pmap.put("account.custom_id", pa.custom_id)
      pmap.put("account.industry", pa.industry.flatMap(s => if (s.trim == "") None else Some(s)).orNull)

      pa.custom_fields.as[JsObject].value.foreach(a => {

        val key = a._1

        val value = if (a._2 == JsNull) null else {

          val unescapedValue = StringEscapeUtils
            .unescapeJson(a._2.toString())
            .stripPrefix("\"")
            .stripSuffix("\"")

            .trim
          if (unescapedValue.isEmpty) null else unescapedValue

        }


        pmap.put(s"account.$key", value)

      })
    }
    pmap
  }
  */

  def create(
    accountId: Long,
    teamId: Long,
    taId: Long,
    data: CustomColumnDefCreateForm
  ): Try[Option[ColumnDef]] = Try {

    DB autoCommit { implicit session =>

      val parsedColumnName = StringUtils.genColumnName(data.name)
      val columnType = ColumnDefTypeEnum.ACCOUNT.toString

        // for custom fields, name and display_name are same
        sql"""
          INSERT INTO column_defs
          (account_id, team_id, ta_id, display_name, name, field_type, is_custom, type)
          VALUES ($accountId, $teamId, $taId, $parsedColumnName, $parsedColumnName, ${data.field_type.toString}, true, $columnType)
          RETURNING *;
        """
          .map(ColumnDefHelper.customColumnfromDb)
          .single
          .apply()

    }

  }

  // List[(ColumnDef, Original name)]
  // original name, required to map while  uploading
  def createMultiple(accountId: Long, teamId: Long, taId: Long, data: CustomColumnDefCreateMultipleForm): Try[List[(ColumnDef, String)]] = Try {
    DB localTx { implicit session =>
      data.columns.map { c => (create(accountId, teamId, taId = taId, c).get.get, c.name) }
    }
  }

  def checkIfColumnBelongsToTeam(id: Long, name: String, teamId: Long): Boolean =  {

    DB.readOnly { implicit session =>

      sql"""SELECT
           EXISTS(SELECT * FROM column_defs WHERE id = $id AND name = $name and team_id = $teamId)
        """
        .map(rs => rs.boolean("exists"))
        .headOption
        .apply()
        .getOrElse(false)

    }
  }

  def getProspectAccountsCount(team_id: Long): Try[Int] = Try {
    DB.readOnly { implicit session =>

      sql"""
       SELECT count(*) FROM (SELECT * FROM prospect_accounts pa WHERE team_id = $team_id LIMIT 10000) as res;
      """
        .map(rs => rs.int("count"))
        .single
        .apply()
        .get

    }
  }

  def deleteCustomColumn(columnId: Long, name: String, permittedAccountIds: Seq[Long], teamId: Long): Try[Int] = Try {


   DB.localTx { implicit session =>

        // Regarding json ?? operator: https://github.com/jdbi/jdbi/issues/174#issuecomment-*********
        sql"""
          UPDATE prospect_accounts p
          SET custom_fields = custom_fields - $name
          WHERE p.team_id = $teamId
           AND p.owner_id IN ($permittedAccountIds)
           AND p.custom_fields ?? $name
          ;
        """
          .update
          .apply()

        sql"DELETE FROM column_defs cd WHERE cd.id = $columnId AND cd.name = $name AND account_id IN ($permittedAccountIds) AND  cd.team_id = $teamId;".update.apply()


    }

  }

}
