package api.columns

object EmailThreadColumnDef extends ColumnDefTrait {

  val defaultColumns: List[ColumnDef] = List(

    ColumnDef(
      display_name = "Email inbox",
      name = "email_inbox",
      field_type = FieldTypeEnum.NUMBER,
      dropdown_type = Some(ColDefDropdownType.EMAIL_INBOX),


      filterable = true,
      allowed_filter_operators = Seq(
        equalColumnOperator
      )
    ),

    ColumnDef(
      display_name = "Campaign",
      name = "campaign",
      field_type = FieldTypeEnum.NUMBER,
      dropdown_type = Some(ColDefDropdownType.CAMPAIGN),


      filterable = true,
      allowed_filter_operators = Seq(
        equalColumnOperator
      )
    ),

    ColumnDef(
      display_name = "From email",
      name = "from_email",
      field_type = FieldTypeEnum.TEXT,
      show_in_datagrid = true,
      sortable = true,
      allowed_filter_operators = textFieldOperators,
      filterable = true
    ),

    ColumnDef(
      display_name = "Prospect first name",
      name = "first_name",
      field_type = FieldTypeEnum.TEXT,
      show_in_datagrid = true,
      sortable = true,
      allowed_filter_operators = textFieldOperators,
      filterable = true
    ),

    ColumnDef(
      display_name = "Prospect last name",
      name = "last_name",
      field_type = FieldTypeEnum.TEXT,
      show_in_datagrid = true,
      sortable = true,
      allowed_filter_operators = textFieldOperators,
      filterable = true
    ),

    ColumnDef(
      display_name = "Prospect email",
      name = "email",
      field_type = FieldTypeEnum.TEXT,
      show_in_datagrid = true,
      sortable = true,
      allowed_filter_operators = textFieldOperators,
      filterable = true
    ),

    ColumnDef(
      display_name = "Prospect Category",
      name = "prospect_category",

      field_type = FieldTypeEnum.TEXT,
      show_in_datagrid = true,

      filterable = true,
      dropdown_type = Some(ColDefDropdownType.PROSPECT_CATEGORY),
      allowed_filter_operators = Seq(
        equalColumnOperator
      )
    ),

    ColumnDef(
      display_name = "Tab",
      name = "inbox_tab",

      field_type = FieldTypeEnum.NUMBER,
      show_in_datagrid = true,

      filterable = true,
      dropdown_type = Some(ColDefDropdownType.INBOX_TAB),
      allowed_filter_operators = Seq(
        equalColumnOperator
      )
    ),

    ColumnDef(
      display_name = "Sent at",
      name = "sent_at",

      show_in_datagrid = true,
      field_type = FieldTypeEnum.DATE,
      sortable = true,

      filterable = true,
      allowed_filter_operators = dateFieldOperators
    )

  )

  def allColumns(teamId: Long): List[ColumnDef] = defaultColumns

}
