package api.gpt.ai_hyperpersonalized

import api.accounts.models.OrgId
import api.campaigns.models.CampaignStepData
import api.campaigns.{CampaignDAO, CampaignStepWithChildren, PreviousFollowUp}
import api.campaigns.services.{CampaignId, CampaignStepService}
import api.gpt.services.GptApiService
import api.prospects.models.StepId
import api.sr_ai.apis.SrAiApi
import api.llm.dao.LlmResponseData
import eventframework.{ProspectFieldsResult, ProspectObject}
import io.smartreach.esp.api.emails.EmailSettingId
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import play.api.libs.json.{<PERSON><PERSON>, OFormat}
import play.api.libs.ws.WSClient
import sr_scheduler.models.{CampaignAIGenerationContext, ChannelType}
import utils.mq.channel_scheduler.channels.{ContentGenerationFailure}
import utils.{PusherService, SRLogger}

import scala.concurrent.{ExecutionContext, Future}
import api.accounts.service.OrganizationService
import api.leadfinder_credits.*
import api.leadfinder_credits.SmartReachCreditServiceError
import api.accounts.models.AccountId
import api.lead_finder.models.{LeadFinderCreditDescription, SmartreachCreditType}
import api.AppConfig
import api.accounts.TeamId



case class StepContext(
                        stepNumber: Int,
                        totalSteps: Int,
                        channel: ChannelType, // email, linkedin_message, linkedin_connection, whatsapp, call_script
                        callToAction: String,
                        step_context: String,
                      )

object StepContext {
  implicit val format: OFormat[StepContext] = Json.format[StepContext]
}

case class AiContentGenerationOutput(subject: String, body: String, llmResponseData: LlmResponseData) extends CreditOperationOutput

class AIHyperPersonalizedGenerator(
                                    gptApiService: GptApiService,
                                    campaignStepService: CampaignStepService,
                                    campaignDAO: CampaignDAO,
                                    pusherService: PusherService,
                                    srAiApi: SrAiApi,
                                    organizationService: OrganizationService
) extends SmartReachCreditService[
  SmartreachCreditOperationData.AiContentGenerationOperationProps,
  AiContentGenerationOutput
] {

  // --- Implementation of SmartReachCreditService ---
  override def _organizationService: OrganizationService = organizationService

  override def operationForCreditsConsumption(
    operationData: SmartreachCreditOperationData.AiContentGenerationOperationProps
  )(
    implicit ws: WSClient,
    ec: ExecutionContext,
    Logger: SRLogger
  ): Future[Either[Exception, AiContentGenerationOutput]] = {
    // This is the core logic moved from generateContentForTask
    implicit val system: ActorSystem = operationData.system // Assuming system is added to AiContentGenerationOperationProps
    implicit val materializer: Materializer = operationData.materializer // Assuming materializer is added to AiContentGenerationOperationProps

    Logger.info(s"Executing AI content generation operation for step: ${operationData.step_id.id}, prospect: ${operationData.prospect_object.id}")

    (for {
      llmResponseData: LlmResponseData <- srAiApi.generateHyperPersonalizedCommunicationV2(
        prospectData = operationData.prospect_details_to_feed_gpt,
        campaignContext = operationData.campaign_ai_generation_context,
        stepContext = operationData.stepContext,
        previousCommunications = operationData.previous_communications.toList,
        orgId = operationData.orgId
      )

      // Extract subject and body from LlmResponseData output
      subject = (llmResponseData.output \ "final_output" \ "subject").asOpt[String]
        .orElse((llmResponseData.output \ "subject").asOpt[String])
        .getOrElse("Generated Subject")

      body = (llmResponseData.output \ "final_output" \ "body").asOpt[String]
        .orElse((llmResponseData.output \ "body").asOpt[String])
        .getOrElse("Generated Body")
        
      // Validation logic could be added here if needed, returning Left(exception) on failure
    } yield {
      Right(AiContentGenerationOutput(
        subject = subject,
        body = body,
        llmResponseData = llmResponseData
      ))
    }).recover { case e =>
      // Log the error originating from the AI generation itself
      Logger.error(s"Core AI generation failed within operationForCreditsConsumption. Step: ${operationData.step_id.id}, Prospect: ${operationData.prospect_object.id}", e)
      Left(new Exception(s"AI content generation failed: ${e.getMessage}", e)) // Wrap in standard exception
    }
  }
  // --- End of SmartReachCreditService implementation ---

//  def generateHyperPersonalizedCommunicationV2(
//                                                prospectData: ProspectFieldsResult,
//                                                campaignContext: CampaignAIGenerationContext,
//                                                stepContext: StepContext,
//                                                previousCommunications: List[PreviousFollowUp],
//                                                orgId: OrgId
//                                            )(implicit
//                                              ws: WSClient,
//                                              materializer: Materializer,
//                                              system: ActorSystem,
//                                              ec: ExecutionContext,
//                                              logger: SRLogger
//                                            ): Future[String] = {
//
//
//      //1️⃣ If the **prospect's industry** does not match the **industry or product** in "campaign context details," return: **`INCORRECT_AUDIENCE`**
//
//      val systemprompt2 = """
//        |You are a highly skilled sales development representative with 7+ years of experience crafting **hyper-personalized outreach campaigns**.
//        |
//        |### 🔴 CRITICAL RULES (Failing these will cause incorrect outputs!)
//        |
//        |2️⃣ **Each step must introduce new insights and benefits**—do not repeat previous information.
//        |3️⃣ **Never reuse examples, metrics, or pain points** across steps.
//        |
//        |---
//        |
//        |### 📌 **Content Progression & Structure**
//        |#### **Step 1: Opening Message**
//        |- **Hook**: Reference the prospect's company, achievements, or trends.
//        |- **Pain Point or Opportunity**: Show how your solution connects to their business.
//        |- **Natural Transition** to the offer (without mentioning the word "offer").
//        |- **Clear CTA** related to the step context.
//        |
//        |#### **Follow-up Steps (Step 2 Onward)**
//        |- **No generic follow-ups** (e.g., "Just following up" or "Hope you're well").
//        |- Introduce **new angles** (e.g., different pain points, role-specific challenges).
//        |- Keep the **conversation natural**, referencing previous context in a fresh way.
//        |- **No repeated examples, numbers, or case studies.**
//        |
//        |---
//        |
//        |### 🎯 **Content Guidelines**
//        |- **Personalization must evolve**: Each step should highlight a **new** relevant detail.
//        |- **Keep it concise**:
//        |  - Emails: **≤ 150 words**
//        |  - LinkedIn messages: **≤ 100 words**
//        |  - WhatsApp messages: **≤ 100 words**
//        |  - LinkedIn connection requests: **≤ 300 characters**
//        |- Use **short, impactful sentences** (5th-grade reading level).
//        |- Avoid **AI-sounding words** like: "supercharge, innovative, transform, cutting-edge, landscape."
//        |
//        |---
//        |
//        |### 🏆 **Best Practices for Persuasion**
//        |- **Use different social proof** (each step should have a new form of credibility).
//        |- **CTA should feel organic**—modify it slightly to improve flow.
//        |- **Tone should match the channel** (e.g., LinkedIn = conversational, Email = professional but direct).
//        |
//        |---
//        |
//        |### 🔧 **How to Use Merge Tags**
//        |Use:
//        |- `{sender_first_name}` → sender's first name.
//        |**Do NOT use placeholders like `[Your Name]` or `[Company Name]`**—always use merge tags.
//        |""".stripMargin
//
//    val user_content: String = {
//      // Calculate all string values first
//      val prospectDataString: String = AIHyperPersonalizedGenerator.convertToKeyValueStrings(prospectData)
//
//      // Campaign Context strings
//      val targetPersonaStr = campaignContext.prospects_designation
////      val valuePropositionStr = campaignContext.
//      val offerDetailsStr = campaignContext.campaign_offer
//      val teamTypeStr = campaignContext.team_type
//      val industryContextStr = campaignContext.industry
//      val motiveStr = campaignContext.motive
//      val solutionStr = campaignContext.solution
//      val reachingOutStr = campaignContext.reason_of_reaching_out
//
//      // Step Context strings
//      val stepDetailsStr = stepContext.step_context
//      val currentStepStr = s"${stepContext.stepNumber} of ${stepContext.totalSteps}"
//      val channelStr = stepContext.channel.toString
//      val callToActionStr = stepContext.callToAction
//
//      // Designation mismatch note
//      ""
//    }
//
//    val user_content2: String = {
//      val prospectDataString: String = AIHyperPersonalizedGenerator.convertToKeyValueStrings(prospectData)
//
//      s"""
//         |📌 **Personalized Outreach for ${stepContext.channel} – Step ${stepContext.stepNumber} of ${stepContext.totalSteps}**
//         |
//         |🔹 **Prospect Details**
//         |$prospectDataString
//         |
//         |🔹 **Campaign Context**
//         |- Campaign Context in Json ${Json.toJson(campaignContext)}
//         |- Target Persona: ${campaignContext.prospects_designation}
//         |- Solution: ${campaignContext.solution}
//         |- Why We're Reaching Out: ${campaignContext.reason_of_reaching_out}
//         |
//         |🔹 **Step Context**
//         |- Step Details: ${stepContext.step_context}
//         |- Call to Action: ${stepContext.callToAction}
//         |
//         |---
//         |
//         |✉️ **First, generate a subject line/opening message** that:
//         |1. **References a prospect-specific detail** (no generic openings).
//         |2. **Is ≤ 6 words or 60 characters.**
//         |3. Uses **lowercase except the first letter**.
//         |4. Can repeat across emails **only if** they belong to the same channel.
//         |
//         |Return **only the subject line**—no additional text.
//         |""".stripMargin
//    }
//
////    println(s"\n\nuser_content: ${user_content}\n\n :: previousCommunications :: ${previousCommunications}")
////    println(s"initial chat : -2 ")
//    val initialChat = List(
//      Map(
//        "role" -> "system",
//        "content" ->systemprompt2
//         
//      ),
//      Map(
//        "role" -> "user",
//        "content" -> user_content2
//      )
//    )
//    /*
//    subject liness for emails will not be more than 6 words and every word should be in lower case or sentenced case never titled case
//     */
//
//    val designationNoteStr = if (prospectData.standardFields.jobTitle.isEmpty){
//      "Note: Prospect's jobtitle is not found."
//    } else if(prospectData.standardFields.jobTitle.get.trim() != campaignContext.prospects_designation.trim()) {
//      "Note: Prospect's designation differs from target persona. Include a connection request to the target persona."
//    } else ""
//
//    // Previous Communications string
////  if respone is there we are pausing campaign thats why we are not tolerating this.
////    | Response: ${comm.completed_reason.getOrElse("No response")}
////    """.stripMargin
//
//    val previousCommsStr = previousCommunications.map { comm =>
//      s"""Channel: ${comm.channel_follow_up_data.step_type.channelStepType.toString}
//         |Sent At: ${comm.step_id}
//         |Content: ${Json.stringify(Json.toJson(comm.channel_follow_up_data))}""".stripMargin
//    }.mkString("\n")
//
//    // Calculate the complete previous communications section
//    val previousCommunicationsSection = if (previousCommsStr.nonEmpty) {
//      s"""Previous Communications:
//         |$previousCommsStr""".stripMargin
//    } else ""
//
//    /*
//
//    STRICT RULES YOU MUST PAY ATTENTION HERE OR I WILL DIE:
//* if the prospect industry is not aligned with the industry or product provided in "campaign context details", then provide an output stating `INCORRECT_AUDIENCE`.
//     */
//    val main_content_prompt = s"""
//                                 |
//                                 |
//                                 |
//                                 |Now, create the main content for this ${stepContext.channel.toString} using the provided context.
//                                 |This is step ${stepContext.stepNumber} of ${stepContext.totalSteps}.
//                                 |
//                                 |${designationNoteStr}
//                                 |
//                                 |${previousCommunicationsSection}
//                                 |
//                                 |Based on these previous communications:
//                                 |  * Build upon the conversation without repeating information
//                                 |  * Reference previous context in new ways
//                                 |  * Introduce new benefits not mentioned before
//                                 |  * Create natural flow from previous messages
//                                 |
//                                 |The content should:
//                                 |1. Build Progressive Value:
//                                 |   - Each step must introduce completely new benefits or angles
//                                 |   - You can check previous communication on where to improve.
//                                 |   - Never repeat specific numbers or metrics from previous steps
//                                 |   - Use different features/benefits in each step
//                                 |   - If a benefit was mentioned before, use a different one
//                                 |
//                                 |2. Maintain Conversation Flow:
//                                 |   - Reference previous communication context without repeating it
//                                 |   - Build upon previous discussions with new insights
//                                 |   - Use different aspects of prospect's context in each step
//                                 |   - Create natural transitions between steps
//                                 |
//                                 |3. Personalization Rules:
//                                 |   - Use a different aspect of prospect data in each step
//                                 |   - Connect each step's offering to a unique part of prospect's business
//                                 |   - Vary the tone and structure based on previous interactions
//                                 |   - Create unique hooks for each step
//                                 |
//                                 |4. Offer Presentation:
//                                 |   - Present each benefit in a new way
//                                 |   - Vary the placement of key offerings (beginning, middle, or end)
//                                 |   - Connect offers to different aspects of prospect's business
//                                 |   - Create unique urgency angles
//                                 |
//                                 |5. Content Variation:
//                                 |   - Use different email structures for each step
//                                 |   - Vary paragraph lengths and formats
//                                 |   - Create unique openings and closings
//                                 |   - Use different types of social proof
//                                 |
//                                 |
//                                 |5. Follow channel-specific length guidelines
//                                 |6. The specified call to action : ${stepContext.callToAction}
//                                 |7. Use your knowledge of industry-specific challenges and role-based pain points to create relevant problem-solution approaches. The pain points should be specific to the prospect's industry and role [JobTitle :: ${prospectData.standardFields.jobTitle.getOrElse("Not Available")}].
//                                 |8. Ensure that the 'step context' and "CTA" is given more weightage when you providing an output. Content should be written based on 'step context' using 'prospect-level details' and 'campaign level context' to make the content dynamic. If the 'step context' is not clear the use the 'campaign level context' for context.
//                                 |9. Campaign offer should not be directly told it should be said in a way that makes it look like we are helping the prospects and it should not mention the word `offer`. It should be present in seperate line, it should be more subtle and in flow with the rest of the email.
//                                 |10. The 'CTA' should always follow 'step context'
//                                 |11. You should generate the body of email in a flow that the CTA can be improvised, its not necessary to write exact lines as provided but you can modify the words little bit.
//                                 |12. If there is any offer it should not just come in between abruptly without any context and don't mention the word offer you phrase it in a best personalized way for the prospect.
//                                 |13. Understand the CTA ( call to action ) if it matches with the step context or some other context place it at that point it not necessary to always keep it at end, there can be multiple CTA in the email.
//                                 |14. Don't repeat similar ice-breakers in the followups.
//                                 |15. For followups ( step -2 onwards ) do not use sentences like Following up on our last conversations, or hope you are well or similar stuffs, instead put a hook based on the vertical, function, industry or the prospect. Here, we don't want to make the prospect feel that they missed our previous message.
//                                 |16. For followups ( step -2 onwards ) do not use similar ice-breakers as used in the step-1 of the same channel.
//                                 |17. For followups ( step -2 onwards ) do not provide an email subject line
//                                 |18. For followups ( step -2 onwards ) if the communication channel (email, linkedin, whatsapp, call , etc, ) is the same as previous channel then ensure that, step -2 onwards does should not contain ice breaker sentences.
//                                 |19. For followups ( step -2 onwards ) do not repeat success stories, metrics, or examples that were used in previous steps. Each step should introduce new benefits or angles.
//                                 |20. When mentioning savings/benefits in follow-up steps, use different examples or metrics than those used in previous steps.
//                                 |21. Each step should focus on a unique aspect of the prospect's business/role that wasn't highlighted in previous steps.
//                                 |22. If step_context contains specific numbers or metrics (like 35% savings), use only those numbers instead of creating new ones.
//                                 |23. For step-1, use the most impactful prospect data point for personalization. Save other data points for subsequent steps.
//                                 |
//                                 |Here are few examples of cold emails whose writing style and formatting you should emulate
//                                 |Example -1 ( step -1 ):
//                                 |"Subject: cut techcorp's energy costs
//                                 |
//                                 |Hi John,
//                                 |
//                                 |Congrats on the recent funding round—big moves for innovation at TechCorp Solutions!
//                                 |
//                                 |But with growth comes rising costs, and energy bills can be a silent budget killer. Here's something that might interest you:
//                                 |
//                                 |Wockhart slashed their electricity costs by 35% by passing generated energy back to the grid and using an auto-switch to optimize saved energy. The result? Lower costs and uninterrupted power for critical operations. What if you could achieve similar results—with no upfront costs?
//                                 |
//                                 |We're offering free installation and first-year maintenance of a solar panel system, complete with 24/7 emergency support.
//                                 |
//                                 |Would you be open to a quick chat to explore how this could work for TechCorp? Looking forward to your thoughts,
//                                 |
//                                 |{sender_first_name}"
//                                 |
//                                 |Example -2 ( step -2 ):
//                                 |"Subject: cut memorial's energy costs
//                                 |
//                                 |Hi Sarah,
//                                 |
//                                 |Congrats on the recent funding—exciting times for Memorial Healthcare!
//                                 |
//                                 |With growth comes rising costs, and energy bills can be a silent budget drain. Here's something that might interest you:
//                                 |
//                                 |At Wockhart, a similar healthcare provider, we helped reduce electricity costs by 35%. They achieved this by passing generated energy back to the grid and using an auto-switch to optimize saved energy. The result? Lower costs and uninterrupted power for critical medical equipment.
//                                 |What if you could achieve similar results—30-40% savings—with no upfront costs
//                                 |
//                                 |Our approach includes free installation and first-year maintenance of a solar panel system, backed by 24/7 emergency support.
//                                 |
//                                 |Would you be open to a quick chat to explore how this could work for Memorial Healthcare?
//                                 |
//                                 |Looking forward to your thoughts,
//                                 |
//                                 |{sender_first_name}"
//                                 |
//                                 |Example -3 : ( step -1 )
//                                 |"Subject: cut memorial's energy costs
//                                 |
//                                 |Hi Sarah,
//                                 |
//                                 |Congrats on the recent funding—exciting times for Memorial Healthcare!
//                                 |
//                                 |With growth comes rising costs, and energy bills can be a silent budget drain. Here's something that might interest you:
//                                 |
//                                 |At Wockhart, a similar healthcare provider, we helped reduce electricity costs by 35%. They achieved this by passing generated energy back to the grid and using an auto-switch to optimize saved energy. The result? Lower costs and uninterrupted power for critical medical equipment.
//                                 |What if you could achieve similar results—30-40% savings—with no upfront costs?
//                                 |
//                                 |Our approach includes free installation and first-year maintenance of a solar panel system, backed by 24/7 emergency support.
//                                 |
//                                 |Would you be open to a quick chat to explore how this could work for Memorial Healthcare? Looking forward to your thoughts,
//                                 |
//                                 |{sender_first_name}"
//                                 |
//                                 |Format appropriately for the channel type.
//                                 |Output only the content, no subject/opening:
//                                 |""".stripMargin
//
//
////    println(s"\n\n main_content_prompt : ${main_content_prompt}\n\n")
//
//    for {
//      opening <- gptApiService.generateCompletionsForChat(
//        chat = initialChat,
//        userId = orgId
//      )
//
//      content <- gptApiService.generateCompletionsForChat(
//        chat = initialChat ++ List(
//          Map("role" -> "assistant", "content" -> opening),
//          Map(
//            "role" -> "user",
//            "content" -> main_content_prompt
//          )
//        ),
//        userId = orgId
//      )
//    } yield {
////      println(s"\n\nprospectData :: ${prospectData.firstName} ${prospectData.lastName} ::  Opening : ${opening} ::  content: ${content}\n\n")
//      stepContext.channel match {
//        case ChannelType.EmailChannel =>
//          s"""
//             |Subject: $opening
//             |
//             |$content
//             |""".stripMargin
//        case _ => content
//      }
//    }
//  }


  /**
    * Generates AI content for a single task.
    */
  def generateContentForTask(
                              step_id: StepId,
                              step_data: CampaignStepData,
                              prospect_object: ProspectObject,
                              campaign_ai_generation_context: Option[CampaignAIGenerationContext],
                              campaign_id: CampaignId,
                              sender_email_settings_id: Option[EmailSettingId],
                              orgId: OrgId,
                              accountId: AccountId,
                              teamId: TeamId,
                              previous_communications: Seq[PreviousFollowUp],
                              orderedSteps: Seq[CampaignStepWithChildren],
                            )(implicit ec: ExecutionContext,
                              ws: WSClient,
                              materializer: Materializer,
                              system: ActorSystem,
                              Logger: SRLogger): Future[AiContentGenerationOutput] = {

    Logger.info(s"Sending mock data back currently :: step_id :: ${step_id.id} :: step_data :: ${step_data.step_type}  :: campaign_id :: ${campaign_id} :: orgId : ${orgId.id}  ")

    val selected_columns_to_use: List[String] = step_data match {
      case magic: CampaignStepData.AutoEmailMagicContentStep => magic.step_context.columns_to_use
      case magic: CampaignStepData.ManualEmailMagicContentStep => magic.step_context.columns_to_use
      case _ => List()
    }

    val prospect_details_to_feed_gpt: ProspectFieldsResult = ProspectObject.extractAllFields(
      prospect = prospect_object,
      selectedCustomColumns = selected_columns_to_use
    )

    // --- Execute with Credit Consumption ---
    campaign_ai_generation_context match {
      case None =>
        Logger.shouldNeverHappen(s"ai_generation_context not found for task. OrgId: ${orgId.id}, Campaign: ${campaign_id.id}, Prospect: ${prospect_object.id}, Sender: ${sender_email_settings_id.map(_.emailSettingId)}")
        Future.failed(ContentGenerationFailure("AI Generation Context not found for campaign."))

      case Some(ai_generation_context) =>
        // Calculate step index and context
        val step_index_opt = orderedSteps.zipWithIndex.find(_._1.id == step_id.id).map(_._2)

        step_index_opt match {
          case None =>
             Logger.shouldNeverHappen(s"Could not find step index for step ${step_id.id} in orderedSteps for campaign ${campaign_id.id}")
             Future.failed(ContentGenerationFailure(s"Internal error: Could not determine step order for step ${step_id.id}"))

          case Some(step_index) =>
             val stepContext = StepContext(
               stepNumber = step_index + 1,
               totalSteps = ai_generation_context.max_number_of_steps,
               channel = ChannelType.EmailChannel, // Assuming Email, adjust if needed
               callToAction = step_data match {
                 case magic: CampaignStepData.AutoEmailMagicContentStep => magic.step_context.call_to_action
                 case magic: CampaignStepData.ManualEmailMagicContentStep => magic.step_context.call_to_action
                 case _ => ""
               },
               step_context = step_data match {
                 case magic: CampaignStepData.AutoEmailMagicContentStep => magic.step_context.step_details
                 case magic: CampaignStepData.ManualEmailMagicContentStep => magic.step_context.step_details
                 case _ => ""
               }
             )

             // Prepare operation data
             val operationData = SmartreachCreditOperationData.AiContentGenerationOperationProps(
               step_id = step_id,
               step_data = step_data,
               prospect_object = prospect_object,
               campaign_ai_generation_context = ai_generation_context,
               campaign_id = campaign_id,
               sender_email_settings_id = sender_email_settings_id,
               orgId = orgId,
               previous_communications = previous_communications,
               orderedSteps = orderedSteps,
               prospect_details_to_feed_gpt = prospect_details_to_feed_gpt,
               stepContext = stepContext,
               system = system,
               materializer = materializer
             )

             // Define credit details (Assuming these exist - replace placeholders)
             val creditDescription = LeadFinderCreditDescription.AiContentGenerationCreditDescription( // Placeholder
               prospectId = prospect_object.id,
               campaignId = campaign_id.id,
               stepId = step_id.id,
               description = s"AI content generation for Campaign ${campaign_id.id}, Step ${step_id.id}, Prospect " + prospect_object.first_name.getOrElse("")
             )
             val creditType = SmartreachCreditType.AiContentGeneration // Placeholder
             val creditsToConsume = AppConfig.LeadFinderCharges.singleAiContentGeneration // Placeholder, ensure this exists in AppConfig


             Logger.info(s"Attempting AI content generation with credit consumption for Step: ${step_id.id}, Prospect: ${prospect_object.id}. Credits to consume: $creditsToConsume")

             // Call the credit service execution method
             executeOperationByConsumingCredits(
               orgId = orgId,
               accountId = accountId, // Use passed accountId
               teamId = teamId, // Use passed teamId
               leadFinderCreditDescription = creditDescription,
               creditUsageType = creditType,
               creditsToBeConsumed = creditsToConsume,
               operationData = operationData
             ).flatMap { // Use flatMap to handle the Future[Either]
               case Right(output) =>
                 Logger.doNotTruncate(s"Successfully generated AI content and consumed credits for Step: ${step_id.id}, Prospect: ${prospect_object.id}")
                 Future.successful(output)
               case Left(error) =>
                 Logger.error(s"Credit consumption or AI operation failed for Step: ${step_id.id}, Prospect: ${prospect_object.id}. Error: $error")
                 // Map specific credit errors to ContentGenerationFailure or a new exception type
                 val failureReason = error match {
                   case SmartReachCreditServiceError.InSufficientCreditsError(msg, _) => s"Insufficient credits: $msg"
                   case SmartReachCreditServiceError.SQLFailureError(msg, _) => s"Database error during credit check/consumption: $msg"
                   case SmartReachCreditServiceError.FailedOperationError(msg, _) => s"AI generation failed: $msg" // Error from operationForCreditsConsumption
                   case SmartReachCreditServiceError.ErrorOnRevertingCreditsBackForFailedOperation(msg, _) => s"Critical error: AI generation failed AND credit revert failed: $msg"
                   // Add other specific SmartReachCreditServiceError cases if needed
                 }
                 Future.failed(ContentGenerationFailure(failureReason))
             }
        }
    }
  }
}





