package api.gpt

import org.apache.pekko.stream.Materializer
import api.{AppConfig, SRAPIResponse}
import api.accounts.models.{AccountId, OrgId}
import api.accounts.{PermType, PermissionUtils, TeamId}
import api.campaigns.services.CampaignId
import api.emails.dao_service.EmailThreadDAOService
import api.emails.{EmailThreadDAO, EmailUtils}
import api.linkedin_message_threads.LinkedinMessageThreadsDAO
import api.team_inbox.InboxUtils
import api.team_inbox.service.TeamInboxService
import play.api.libs.json.{JsError, JsSuccess, JsValue, Json}
import play.api.libs.ws.WSClient
import play.api.mvc.{Action, BaseController, ControllerComponents}
import sr_scheduler.models.ChannelType
import utils.SRLogger
import utils.uuid.services.SrUuidService

import scala.util.{Failure, Success, Try}
import scala.concurrent.{ExecutionContext, Future}

class GPTController(
                     gptService: GPTService,
                     gptInboxService: GPTInboxService,
                     permissionUtils: PermissionUtils,
                     protected val srUuidService: SrUuidService,
                     protected val emailThreadDAOService: EmailThreadDAOService,
                     protected val linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO,
                     protected val teamInboxService: TeamInboxService,
                     protected val controllerComponents: ControllerComponents,
                     implicit val materializer: Materializer,
                     implicit val ws: WSClient

                   ) extends BaseController with EmailUtils with InboxUtils {

  private implicit val ec: ExecutionContext = controllerComponents.executionContext

  def generateEmailBody(v: String, tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    permission = PermType.EDIT_CAMPAIGNS,
    tidOpt = tid
  ).async(parse.json) { request =>

    given logger: SRLogger = request.Logger
    val res = request.Response

    request.actingTeamAccount match {

      case None =>

        logger.error("generateEmailBody - actingTeamAccount None")

        Future.successful(res.BadRequestError("Invalid team"))

      case Some(teamAccount) =>

        request.body.validate[FieldsToGenerateEmailBody] match {

          case JsError(error) =>

            Future.successful(res.JsValidationError(error))

          case JsSuccess(fieldsToGenerateEmailBody, _) =>

            val teamId = TeamId(id = teamAccount.team_id)

            gptService.generateEmailBody(
              fields = fieldsToGenerateEmailBody,
              orgId = OrgId(request.loggedinAccount.org.id),
              teamId = teamId,
              accountId = AccountId(request.loggedinAccount.internal_id)
            ).map { response =>

              res.Success("Generated Email Body", Json.obj("email_body" -> response))

            }.recover { case e =>

              logger.error(s"Generate Email Body Failed. teamId: $teamId :: fields: $fieldsToGenerateEmailBody", e)

              res.BadRequestError(e.getMessage)

            }
        }

    }

  }

  def regenerateEmailBody(v: String, tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    permission = PermType.EDIT_CAMPAIGNS,
    tidOpt = tid
  ).async(parse.json) { request =>

    given logger: SRLogger = request.Logger
    val res = request.Response

    request.actingTeamAccount match {

      case None =>

        logger.error(s"regenerateEmailBody - actingTeamAccount None - accountId: ${request.loggedinAccount.internal_id}")

        Future.successful(res.BadRequestError("Invalid team"))

      case Some(teamAccount) =>

        val teamId = TeamId(id = teamAccount.team_id)

        request.body.validate[EmailRegenerationInput] match {
          case JsError(error) =>
            Future.successful(res.JsValidationError(error))

          case JsSuccess(regenerateEmailBodyRequest, _) =>

            gptService.regenerateEmailBody(
              regenerateEmailBodyRequest = regenerateEmailBodyRequest,
              teamId = teamId,
              orgId = OrgId(request.loggedinAccount.org.id),
              accountId = AccountId(request.loggedinAccount.internal_id)
            ).map { response =>

              res.Success("Regenerated Email Body", Json.obj("email_body" -> response))

            }.recover { case e =>

              logger.error("Regenerate Email Body Failed", e)

              res.BadRequestError(e.getMessage)

            }

        }

    }

  }


  def generateSubject(v: String, tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    permission = PermType.EDIT_CAMPAIGNS,
    tidOpt = tid
  ).async(parse.json) { request =>

    given logger: SRLogger = request.Logger
    val res = request.Response

    request.actingTeamAccount match {

      case None =>

        logger.error(s"generateSubject - actingTeamAccount None - accountId: ${request.loggedinAccount.internal_id}")

        Future.successful(res.BadRequestError("Invalid team"))

      case Some(teamAccount) =>

        val teamId = TeamId(id = teamAccount.team_id)

        request.body.validate[FieldsToGenerateEmailSubject] match {

          case JsError(error) =>
            Future.successful(res.JsValidationError(error))

          case JsSuccess(generateEmailSubjectRequest, _) =>

            gptService.generateSubjectForEmailBody(
              generateEmailSubjectRequest = generateEmailSubjectRequest,
              orgId = OrgId(request.loggedinAccount.org.id),
              teamId = teamId,
              accountId = AccountId(request.loggedinAccount.internal_id)
            ).map { response =>

              res.Success("Generated Email Subject", Json.obj("email_subject" -> response))

            }.recover { case e =>

              logger.error("Generate Email Subject Failed", e)

              res.BadRequestError(e.getMessage)

            }
        }
    }

  }

  def regenerateSubject(v: String, tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    permission = PermType.EDIT_CAMPAIGNS,
    tidOpt = tid
  ).async(parse.json) { request =>

    given logger: SRLogger = request.Logger
    val res = request.Response

    request.actingTeamAccount match {

      case None =>

        logger.error(s"regenerateSubject - actingTeamAccount None - accountId: ${request.loggedinAccount.internal_id}")

        Future.successful(res.BadRequestError("Invalid team"))

      case Some(teamAccount) =>

        val teamId = TeamId(id = teamAccount.team_id)

        request.body.validate[RegenerateEmailSubjectDetails] match {

          case JsError(error) =>

            Future.successful(res.JsValidationError(error))

          case JsSuccess(regenerateEmailSubjectRequest, _) =>

            gptService.regenerateEmailSubject(
              regenerateEmailSubjectRequest = regenerateEmailSubjectRequest,
              orgId = OrgId(request.loggedinAccount.org.id),
              teamId = teamId,
              accountId = AccountId(request.loggedinAccount.internal_id)
            ).map { response =>

              res.Success("Regenerated Email Subject", Json.obj("email_subject" -> response))

            }.recover { case e =>

              logger.error("Regenerate Email Subject Failed", e)

              res.BadRequestError(e.getMessage)

            }
        }
    }

  }

  def rephraseText(v: String, tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    permission = PermType.EDIT_CAMPAIGNS,
    tidOpt = tid
  ).async(parse.json) { request =>

    given logger: SRLogger = request.Logger
    val res = request.Response

    request.actingTeamAccount match {

      case None =>

        logger.error(s"rephraseText - actingTeamAccount None - accountId: ${request.loggedinAccount.internal_id}")

        Future.successful(res.BadRequestError("Invalid team"))

      case Some(teamAccount) =>

        val teamId = TeamId(id = teamAccount.team_id)

        Try((request.body \ "text").as[String]) match {
          case Failure(e) =>

            Future.failed(e)

          case Success(text) =>

            gptService.rephraseText(
              text = text,
              teamId = teamId,
              orgId = OrgId(request.loggedinAccount.org.id),
              accountId = AccountId(request.loggedinAccount.internal_id)
            ).map { response =>

              res.Success("Rephrased Text", Json.obj("rephrased_text" -> response))

            }.recover { case e =>

              logger.error("Rephrase Text Failed", e)

              res.BadRequestError(e.getMessage)

            }

        }

    }

  }

  def shortenText(v: String, tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    permission = PermType.EDIT_CAMPAIGNS,
    tidOpt = tid
  ).async(parse.json) { request =>

    given logger: SRLogger = request.Logger
    val res = request.Response

    request.actingTeamAccount match {

      case None =>

        logger.error(s"shortenText - actingTeamAccount None - accountId: ${request.loggedinAccount.internal_id}")

        Future.successful(res.BadRequestError("Invalid team"))

      case Some(teamAccount) =>

        val teamId = TeamId(id = teamAccount.team_id)

        Try((request.body \ "text").as[String]) match {
          case Failure(e) =>
            Future.failed(e)

          case Success(text) =>

            gptService.shortenText(
              text = text,
              orgId = OrgId(request.loggedinAccount.org.id),
              teamId = teamId,
              accountId = AccountId(request.loggedinAccount.internal_id)
            ).map { response =>

              res.Success("Shortened Text", Json.obj("shortened_text" -> response))

            }.recover { case e =>

              logger.error("Shorten Text Failed", e)

              res.BadRequestError(e.getMessage)

            }
        }
    }

  }

  def generateReplyForConversation(v: String, id: Long, tid: Option[Long]) =
    (
      permissionUtils.checkPermission(
        version = v,
        permission = PermType.SEND_MANUAL_EMAIL,
        tidOpt = tid

      ) andThen hasEmailThreadV3(
        emailThreadUuid = None, 
        emailThreadId = Some(id)
      )

      )
      .async { request =>

        given logger: SRLogger = request.Logger
        val res = request.Response


          gptInboxService.generateReplyForConversation(
            emailThreadId = request.thread.id,
            thread = request.thread,
            teamId = TeamId(tid.get),
            orgId = OrgId(request.loggedinAccount.org.id),
            accountId = AccountId(request.loggedinAccount.internal_id)
          )
            .map(response => {
              res.Success("Generated follow-up for previous email", Json.obj("reply_email_body" -> response))
            })
            .recover {
              case e =>
                logger.error("Generate follow-up email Failed", e)
                res.BadRequestError(e.getMessage)
            }

      }

  def generateReplyForConversationNewInbox(v: String, uuid: String, tid: Option[Long], tiid: String) =
    (
      permissionUtils.checkPermission(
        version = v,
        permission = PermType.SEND_MANUAL_EMAIL,
        tidOpt = tid

      ) andThen hasTeamInboxAccess(
        teamInboxIdFromReq = tiid,
        emailThreadUuid = uuid
      )

      )
      .async { request =>

        given logger: SRLogger = request.Logger
        val res = request.Response


          gptInboxService.generateReplyForConversation(
            emailThreadId = request.thread.id,
            thread = request.thread,
            teamId = TeamId(tid.get),
            orgId = OrgId(request.loggedinAccount.org.id),
            accountId = AccountId(request.loggedinAccount.internal_id)
          )
            .map(response => {
              res.Success("Generated follow-up for previous email", Json.obj("reply_email_body" -> response))
            })
            .recover {
              case e =>
                logger.error("Generate follow-up email Failed", e)
                res.BadRequestError(e.getMessage)
            }

      }

}
