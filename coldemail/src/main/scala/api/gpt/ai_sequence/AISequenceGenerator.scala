package api.gpt.ai_sequence

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.{CampaignBasicInfo, CampaignDAO, CampaignStepVariant, CampaignStepVariantCreateOrUpdate}
import api.campaigns.models.{CampaignAISequenceStatus, CampaignStepData, CampaignType, CreateCampaignStepVariantError}
import api.campaigns.services.{CampaignId, CampaignStepService}
import api.gpt.CreateStepsRequest
import api.gpt.services.GptApiService
import api.sr_ai.models.{BaseStepVariant, CampaignGenerationResult, SrAiGeneratedCampaignStep}
import api.sr_ai.service.CampaignGenerationService
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import play.api.libs.json.Json
import play.api.libs.ws.WSClient
import utils.pusher.SrPushEventNames
import utils.{FutureUtils, PusherService, SRLogger}

import scala.util.{Failure, Success}
import scala.concurrent.{ExecutionContext, Future}

case class CreateCampaignSeqState(
  parentId: Long,
  headStepId: Option[Long],
)

case class CampaignStepCreationRequestData(
                                            parentId: Long,
                                            subject: String,
                                            stepNumber: Int,
                                            previousChat: List[Map[String, String]],
                                            isFirstStep: Boolean,
                                            headStepId: Option[Long],
                                            isVariant: Boolean
                                          )


case class CampaignStepOutputWithChat(
                                       campaignStepVariant: CampaignStepVariant,
                                       chat: List[Map[String, String]]
                                     )

case class ProcessStepData(
                            stepNumber: Int,
                            lastOutput: CampaignStepOutputWithChat,
                            numberOfSteps: Int,
                            includeCallStep: Boolean,
                            initialSubject: String,
                            firstStepId: Long
                          )

class AISequenceGenerator(
                           gptApiService: GptApiService,
                           campaignStepService: CampaignStepService,
                           campaignDAO: CampaignDAO,
                           campaignGenerationService: CampaignGenerationService,
                           pusherService: PusherService
                         ) {


  private def logCreateCampaignStepVariantError(
                                                 err: CreateCampaignStepVariantError,
                                                 parentId: Long,
                                                 headStepId: Option[Long],
                                                 stepNumber: Int,
                                               )(
                                                 using Logger: SRLogger,
                                               ): Unit = {

    Logger.error(
      msg = s"logCreateCampaignStepVariantError - stepNo: $stepNumber  :: parentId: $parentId :: headStepId: $headStepId  - $err",
    )

    err match {

      case CreateCampaignStepVariantError.CampaignStepNotFound =>

        Logger.error(
          msg = "createStepsInCampaign - CampaignStepNotFound"
        )

      case CreateCampaignStepVariantError.MaxVariantsExceeded(maxVariants) =>

        Logger.error(
          msg = s"createStepsInCampaign - MaxVariantsExceeded - $maxVariants"
        )

      case CreateCampaignStepVariantError.TemplateIsFromLibraryFieldMissing =>


        Logger.error(
          msg = s"createStepsInCampaign - TemplateIsFromLibraryFieldMissing"
        )

      case CreateCampaignStepVariantError.CampaignStepVariantValidationError(err) =>

        Logger.error(
          msg = s"createStepsInCampaign - CampaignStepVariantValidationError - $err"
        )

      case CreateCampaignStepVariantError.ValidateCampaignTemplateError(err) =>

        Logger.error(
          msg = "createStepsInCampaign - ValidateCampaignTemplateError",
          err = err,
        )

      case CreateCampaignStepVariantError.ErrorWhileCreateCampaignFirst(err) =>

        Logger.error(
          msg = s"createStepsInCampaign - ErrorWhileCreateCampaignFirst - $err",
        )

      case CreateCampaignStepVariantError.TemplateAccessCheckError(e) =>

        Logger.error(
          msg = "createStepsInCampaign - TemplateAccessCheckError",
          err = e,
        )

      case CreateCampaignStepVariantError.TemplateNotFoundError(errMsg) =>

        Logger.error(
          msg = s"createStepsInCampaign - TemplateNotFoundError - $errMsg",
        )

      case CreateCampaignStepVariantError.CreatedStepVariantNotFound =>

        Logger.error(
          msg = s"createStepsInCampaign - CreatedStepVariantNotFound",
        )

      case CreateCampaignStepVariantError.CreateStepVariantException(err) =>

        Logger.error(
          msg = s"createStepsInCampaign - CreateStepVariantException",
          err = err,
        )

      case CreateCampaignStepVariantError.FeatureUsageEventSaveError(err) =>

        Logger.error(
          msg = s"createStepsInCampaign - FeatureUsageEventSaveError",
          err = err,
        )

    }

  }

  private def createVariant(
    orgId: OrgId,
    teamId: TeamId,
    userId: AccountId,
    taId: Long,
    campaignId: CampaignId,
    campaignHeadStepId: Option[Long],
    variant: BaseStepVariant,
    isFirstStep: Boolean,
    isVariant: Boolean,
    parentId: Long,
    stepNumber: Int,
  )(
    implicit
    ws: WSClient,
    ec: ExecutionContext,
    materializer: Materializer,
    Logger: SRLogger,
  ): Future[CreateCampaignSeqState] = {

    val stepData = variant match {

      case esv: BaseStepVariant.EmailStepVariantContent =>

        CampaignStepData.AutoEmailStep(
          subject = esv.subject,
          body = esv.body
        )

      case csv: BaseStepVariant.CallStepVariantContent =>

        CampaignStepData.CallTaskData(
          body = csv.script
        )

    }

    val variantData = CampaignStepVariantCreateOrUpdate(
      parent_id = if (isVariant) 0 else parentId,
      step_data = stepData,
      step_delay = if (isFirstStep) 0 else AppConfig.stepDelayForAISequenceGenerator,
      notes = None,
      priority = None
    )

    campaignStepService.createVariant(
      orgId = orgId.id,
      data = variantData,
      teamId = teamId.id,
      userId = userId.id,
      taId = taId,
      stepId = if (isVariant) parentId else 0,
      campaignId = campaignId.id,
      campaignHeadStepId = campaignHeadStepId,
    ).flatMap {

      case Right(createdV) =>

        val newHeadStepId = if (isFirstStep) Some(createdV.step_id) else campaignHeadStepId

        val newParentId = createdV.step_id

        Future.successful(
          CreateCampaignSeqState(parentId = newParentId, headStepId = newHeadStepId)
        )

      case Left(error) =>

        logCreateCampaignStepVariantError(
          err = error,
          parentId = parentId,
          headStepId = campaignHeadStepId,
          stepNumber = stepNumber,
        )

        Future.failed(new Exception(s"Failed to create campaign step variant due to: $error :: stepData: $stepData"))

    }

  }

  def createCampaignSteps(
    campaignId: CampaignId,
    teamId: TeamId,
    orgId: OrgId,
    accountId: AccountId,
    taId: Long,
    campaignSequence: List[SrAiGeneratedCampaignStep]
  )(
    implicit
    ws: WSClient,
    materializer: Materializer,
    ec: ExecutionContext,
    Logger: SRLogger,
  ): Future[Option[Long]] = {

    val initialState = Future.successful(
      CreateCampaignSeqState(
        parentId = 0,
        headStepId = None,
      )
    )

    campaignSequence
      .sortBy(_.step_number)
      .foldLeft(initialState) { (stateFut, step) =>

        val isFirstStep = step.step_number == 1

        step.variants
          .sortBy(_.variant_number)
          .foldLeft(stateFut) {

            case (variantStateFut, variant) =>

              variantStateFut.flatMap { variantState =>

                val isVariant = variant.variant_number > 1

                createVariant(
                  orgId = orgId,
                  teamId = teamId,
                  userId = accountId,
                  taId = taId,
                  campaignId = campaignId,
                  campaignHeadStepId = if (isFirstStep) None else variantState.headStepId,
                  variant = variant,
                  isFirstStep = isFirstStep,
                  isVariant = isVariant,
                  parentId = variantState.parentId,
                  stepNumber = step.step_number,
                )

              }

          }

      }.map { res =>

        res.headStepId

      }

  }

  //FIXME SRAiAPI move the publish to SrAiApiAuditLog
  private def generateCampaignWithRetry(
    teamId: TeamId,
    shouldIncludeCallStep: Boolean,
    userInputs: CreateStepsRequest,
  )(
    implicit
    ws: WSClient,
    ec: ExecutionContext,
    Logger: SRLogger,
    system: ActorSystem,
  ): Future[CampaignGenerationResult] = {

    // Sometimes very rarely, the LLM outputs invalid JSON,
    // so retry if its happens.

    FutureUtils.retry(
      // deferred evaluation
      f = campaignGenerationService.generateCampaign(
        teamId = teamId,
        shouldIncludeCallStep = shouldIncludeCallStep,
        userInputs = userInputs,
      ),
      retries = AppConfig.maxRetryAIGenerationAttempts,
    )(
      ec = ec,
      s = system.scheduler,
      Logger = Logger,
    )

  }

  def createStepsInCampaign(
                             createStepsRequest: CreateStepsRequest,
                             orgId: OrgId,
                             accountId: AccountId,
                             campaignId: CampaignId,
                             teamId: TeamId,
                             taId: Long
                           )(implicit
                             ws: WSClient,
                             materializer: Materializer,
                             system: ActorSystem,
                             ec: ExecutionContext,
                             logger: SRLogger
                           ): Future[Boolean] = {


    campaignDAO.getCampaignBasicInfo(teamId.id, campaignId.id) match {
      case Failure(exception) =>
        logger.shouldNeverHappen(
          s"createStepsInCampaign :: getCampaignBasicInfo Error :: " +
            s"team_id ${teamId.id} campaignId ${campaignId.id} " +
            s"accountId ${accountId.id} taId $taId orgID ${orgId.id}",
          Some(exception)
        )
        Future.failed(new Exception("This error should not have occurred"))

      case Success(None) =>
        logger.shouldNeverHappen(
          s"createStepsInCampaign :: getCampaignBasicInfo Error None found :: " +
            s"team_id ${teamId.id} campaignId ${campaignId.id} " +
            s"accountId ${accountId.id} taId $taId orgID ${orgId.id}"
        )
        Future.failed(new Exception("This error should not have occurred"))

      case Success(Some(campaign: CampaignBasicInfo)) =>
        val includeCallStep = campaign.settings.campaign_type != CampaignType.Email

        val generateCampaignSeqFut = for {

          campaignGenerationResult: CampaignGenerationResult <- generateCampaignWithRetry(
            teamId = teamId,
            shouldIncludeCallStep = includeCallStep,
            userInputs = createStepsRequest,
          )

          _: Option[Long] <- createCampaignSteps(
            campaignId = campaignId,
            teamId = teamId,
            orgId = orgId,
            accountId = accountId,
            taId = taId,
            campaignSequence = campaignGenerationResult.campaign_sequence,
          )


          _: String <- Future.fromTry(
            pusherService.sendMessageUsingPusher(
              channel_name = PusherService.getPusherChannelNameForCampaignAISequenceGeneration(
                teamId = teamId,
                orgId = orgId
              ),
              event_name = SrPushEventNames.campaignAISequenceKey,
              message = Json.obj(
                "campaign_name" -> campaign.name,
                "campaign_id" -> campaignId.id
              ),
              logger = logger
            )
          )

        } yield {
          true
        }

        generateCampaignSeqFut.flatMap { res =>

          Future.fromTry {
            campaignDAO.changeCampaignAISequenceStatus(
              campaignId = campaignId,
              teamId = teamId,
              status = CampaignAISequenceStatus.Finished
            )
          }.map { _ =>

            res

          }

        }.recoverWith { case e =>

          // Mark the process as finished, even if it failed.
          // This is because if any campaign sequence generation remains in a pending state,
          // the user will be unable to generate a new one.
          Future.fromTry {
            campaignDAO.changeCampaignAISequenceStatus(
              campaignId = campaignId,
              teamId = teamId,
              status = CampaignAISequenceStatus.Finished
            )
          }.flatMap { _ =>

            Future.failed(e)

          }

        }

    }
  }


}
