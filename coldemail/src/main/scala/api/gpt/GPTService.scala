package api.gpt

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.columns.ColumnDefsProspectsDetails
import api.gpt.services.GptApiService
import api.llm.models.LlmFlow.MagicColumnFlow
import api.spammonitor.model.UnderReviewReason
import api.sr_ai.service.ContentGenerationService
import app_services.sr_rate_limiter.SrRateLimiter
import eventframework.{MessageObject, ProspectObject}
import io.smartreach.esp.utils.email.EmailHelperCommon
import play.api.libs.ws.WSClient
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class GPTService(
                  srRateLimiter: SrRateLimiter,
                  gptApiService: GptApiService,
                  contentGenerationService: ContentGenerationService,
                ) {

  val gptApiHitLimit = AppConfig.gptApiHitLimit
  val gptApiRateLimitIntervalInSeconds = AppConfig.gptApiRateLimitIntervalInSeconds

  /*
  private def generatePromptMessageFromFields(fields: FieldsToGenerateEmailBody): Try[String] = {

    if (fields.max_number_of_words.getOrElse(200) > 500) {
      Failure(new Exception("Maximum number of words in email body cannot be more than 500."))
    }
    else {
      val promptMessage = "Write an email body using following details:"

      val mergeTagsToUse = if (fields.merge_tags_to_use.nonEmpty) {
        s"\nMerge Tags to use: ${fields.merge_tags_to_use.map(text => text + " ")}"
      }
      else {
        ""
      }

      Success(promptMessage +
        fields.recipient.map(text => s"\nRecipient's position: $text").getOrElse("") +
        fields.department.map(text => s"\nRecipient's Department: $text").getOrElse("") +
        fields.industry.map(text => s"\nRecipient's Industry: $text").getOrElse("") +
        fields.email_type.map(text => s"\nEmail Type: $text").getOrElse("") +
        fields.writing_style.map(text => s"\nEmail Writing Style: $text").getOrElse("") +
        fields.tone_of_voice.map(text => s"\nTone of voice: $text").getOrElse("") +
        mergeTagsToUse +
        s"\nMaximum Word Length: ${fields.max_number_of_words.getOrElse(200)}" +
        fields.additional_comment.map(text => s"\n$text").getOrElse("") +
        "\nStrictly do not include subject or any thing other than email body in the content."
      )
    }

  }
  */

  def generateEmailBody(
                         fields: FieldsToGenerateEmailBody,
                         teamId: TeamId,
                         orgId: OrgId,
                         accountId: AccountId
                       )(implicit ws: WSClient,
                         ec: ExecutionContext,
                         logger: SRLogger): Future[String] = {

    val apiHitLimitCheckKey = GPTService.getCacheKeyForAccountHittingGPTApi(
      accountId = accountId,
      orgId = orgId,
    )

    srRateLimiter.rateLimitApiCall(
      key = apiHitLimitCheckKey,
      threshold = gptApiHitLimit,
      expireInSeconds = gptApiRateLimitIntervalInSeconds
    ) match {
      case Failure(e) =>
        logger.error(s"Error while doing GPT Api rate limit check for accountId_${accountId.id}", e)
        Future.failed(e)

      case Success(_) =>

        // copied from generatePromptMessageFromFields
        if (fields.max_number_of_words.getOrElse(200) > 500) {

          Future.failed(new Exception("Maximum number of words in email body cannot be more than 500."))

        } else {

          contentGenerationService.generateEmailBody(
            fieldsToGenerateEmailBody = fields,
            teamId = teamId,
          ).map(_.generated_email_body)

        }

    }

  }


  def getMagicColumnPrompt(
                            magicColumnPrompt: String,
                            first_name: Option[String],
                            last_name: Option[String],
                            company: Option[String],
                            job_title: Option[String]
                          ): List[Map[String, String]] = {


    val firstNameSub = if (first_name.isDefined) s"\"Prospect's first name\": \"${first_name}\"," else ""
    val lastNameSub = if (last_name.isDefined) s"\"Prospect's last name\": \"${last_name}\"," else ""

    val jobTitleSub = if (job_title.isDefined) s"\"Prospect's job title\": \"${job_title}\"," else ""
    val companySub = if (company.isDefined) s"\"Prospect's company\": \"${company}\"" else ""

    val prospectJson =
      s"""
         |{
         |  ${firstNameSub}
         |  ${lastNameSub}
         |  ${jobTitleSub}
         |  ${companySub}
         |}
         |""".stripMargin


    val message =
      s"""
         |
         |- User's Instructions: ${magicColumnPrompt.trim}
         |
         |- Prospect Details (JSON): ${prospectJson.trim}
         |
         |Ensure the message is:
         |1) Maintain a professional yet approachable tone.
         |2) Do not include any merge tags, or any dynamic placeholders.
         |3) The result should be fully written out, with no dynamic elements.
         |
         |""".stripMargin

    val chat = List(
      Map(
        "role" -> "system",
        "content" ->
          """
            |You are an AI assistant skilled in professional message writing, contact categorization, and context-based reasoning.
            |
            |For message body prompts:
            |1. Write a concise, professional, and well-formatted message body with a line break between sentences.
            |2. Limit the word count to 150–200 words.
            |3. Recipients don’t like long messages, so the outreach should be to the point. Each message sequence to have at least 150 to 200 words (not more). The messages should be well formatted, with a line break between sentences. Personalize each message to the recipient’s specific context or pain points. Avoid using words that trigger spam filters and minimize the use of 'I' or 'we.' The outreach should focus on the recipient. Include a clear and compelling call to action. The idea is for the recipient to respond, or take action.
            |4. Personalize the message to the recipient's context or pain points without using placeholders or dynamic tags (e.g., {{first_name}}, {{company}}).
            |5. Focus on the recipient, with minimal use of 'I' or 'we.'
            |6. Include a clear and compelling call to action.
            |7. Avoid words that trigger spam filters.
            |8. Do not include any subject, salutation or message signature only the content of the message body.
            |9. The message should be fully written out, with no dynamic elements.
            |
            |For categorization or similar tasks:
            |1. Keep the response concise, within 50 words.
            |2. Avoid using placeholders or dynamic tags.
            |3. Be directly relevant to the user's query and avoid including unnecessary details.
            |
            |Strictly adhere to the word limits for each task and ensure responses are professional and relevant.
            |""".stripMargin
      ),
      Map(
        "role" -> "user",
        "content" -> message
      )
    )

    chat
  }


  def generateEmailBodyForMagicColumn(
                                       prospect: ProspectObject,
                                       magicColumnPrompt: String,
                                       orgId: OrgId,
                                       columnDefsProspectsRecord: ColumnDefsProspectsDetails
                                     )(
                                       implicit ws: WSClient,
                                       ec: ExecutionContext,
                                       logger: SRLogger
                                     ): Future[String] = {


    gptApiService.generateCompletionsForChat(
      chat = getMagicColumnPrompt(
        magicColumnPrompt = magicColumnPrompt,
        first_name = prospect.first_name,
        last_name = prospect.last_name,
        company = prospect.company,
        job_title = prospect.job_title
      ),
      userId = orgId,
      columnDefsProspectsRecord = Some(columnDefsProspectsRecord),
      flow = MagicColumnFlow
    )

  }

  def regenerateEmailBody(
                           regenerateEmailBodyRequest: EmailRegenerationInput,
                           orgId: OrgId,
                           teamId: TeamId,
                           accountId: AccountId
                         )(implicit ws: WSClient,
                           ec: ExecutionContext,
                           logger: SRLogger): Future[String] = {

    val apiHitLimitCheckKey = GPTService.getCacheKeyForAccountHittingGPTApi(
      accountId = accountId,
      orgId = orgId
    )

    srRateLimiter.rateLimitApiCall(
      key = apiHitLimitCheckKey,
      threshold = gptApiHitLimit,
      expireInSeconds = gptApiRateLimitIntervalInSeconds
    ) match {
      case Failure(e) =>
        logger.error(s"Error while doing GPT Api rate limit check for accountId_${accountId.id}", e)
        Future.failed(e)

      case Success(_) =>

        if (regenerateEmailBodyRequest.previous_email_body.trim.isEmpty) {

          Future.failed(new Exception("Previous Email Body Cannot be empty."))

        } else {

          contentGenerationService.regenerateEmailBody(
            regenerateEmailBodyRequest = regenerateEmailBodyRequest,
            teamId = teamId,
          ).map(_.regenerated_email_body)

        }

    }
  
  }

  def generateSubjectForEmailBody(
                                   generateEmailSubjectRequest: FieldsToGenerateEmailSubject,
                                   orgId: OrgId,
                                   teamId: TeamId,
                                   accountId: AccountId
                                 )
                                 (implicit ws: WSClient,
                                  ec: ExecutionContext,
                                  logger: SRLogger): Future[String] = {

    val apiHitLimitCheckKey = GPTService.getCacheKeyForAccountHittingGPTApi(
      accountId = accountId,
      orgId = orgId
    )

    srRateLimiter.rateLimitApiCall(
      key = apiHitLimitCheckKey,
      threshold = gptApiHitLimit,
      expireInSeconds = gptApiRateLimitIntervalInSeconds
    ) match {
      case Failure(e) =>
        logger.error(s"Error while doing GPT Api rate limit check for accountId_${accountId.id}", e)
        Future.failed(e)

      case Success(_) =>

        if (generateEmailSubjectRequest.email_body.trim.isEmpty) {

          Future.failed(new Exception("Email Body Cannot be empty."))

        } else if (generateEmailSubjectRequest.max_chars < 20 ||

          generateEmailSubjectRequest.max_chars > 80) {

          Future.failed(new Exception("Maximum number of Characters in Subject line should be between 20 and 80."))

        } else {

          contentGenerationService.generateEmailSubject(
            fieldsToGenerateEmailSubject = generateEmailSubjectRequest,
            teamId = teamId,
          ).map(_.generated_email_subject)

        }

    }
  }

  def regenerateEmailSubject(
                              regenerateEmailSubjectRequest: RegenerateEmailSubjectDetails,
                              orgId: OrgId,
                              teamId: TeamId,
                              accountId: AccountId
                            )
                            (implicit ws: WSClient,
                             ec: ExecutionContext,
                             logger: SRLogger): Future[String] = {

    val apiHitLimitCheckKey = GPTService.getCacheKeyForAccountHittingGPTApi(
      accountId = accountId,
      orgId = orgId
    )

    srRateLimiter.rateLimitApiCall(
      key = apiHitLimitCheckKey,
      threshold = gptApiHitLimit,
      expireInSeconds = gptApiRateLimitIntervalInSeconds
    ) match {
      case Failure(e) =>
        logger.error(s"Error while doing GPT Api rate limit check for accountId_${accountId.id}", e)
        Future.failed(e)

      case Success(_) =>

        if (regenerateEmailSubjectRequest.email_body.trim.isEmpty) {

          Future.failed(new Exception("Email Body Cannot be empty."))

        } else if (regenerateEmailSubjectRequest.previous_subjects.isEmpty) {

          Future.failed(new Exception("Previous Subjects Cannot be empty."))

        } else {

          contentGenerationService.regenerateEmailSubject(
            regenerateEmailSubjectRequest = regenerateEmailSubjectRequest,
            teamId = teamId,
          ).map(_.regenerated_subject)

        }
        
    }
  }

  /*
  def categorizeEmail(
                       email_body: String,
                       subject: String
                     )
                     (implicit ws: WSClient,
                      ec: ExecutionContext,
                      logger: SRLogger): Future[List[UnderReviewReason]] = {

    val emailBodyPlainText = EmailHelperCommon.getTextBodyFromHtmlBody(email_body).replace("\n", "")

    val promptMessageForSpamCheck =
      s"""Categorize the email as spam or non-spam:
         |Subject: $subject
         |Body: $emailBodyPlainText
         |""".stripMargin

    val chat = List(
      Map("role" -> "system", "content" ->
        """
You are an expert email categorization assistant. Your task is to analyze the content of an email, including its subject and body, and determine whether it is "spam" or "not spam." Consider factors such as tone, language, suspicious keywords, links, requests for personal information, or offers that might indicate spam. Provide a clear categorization along with a brief explanation of your reasoning. Format your response in JSON as shown below          |
          |{
          |  "subject": "<email subject>",
          |  "email": "<email body>",
          |  "analysis": {
          |    "spamLikelihood": "<low/medium/high>",
          |    "reason": "<reason for the categorization>"
          |  },
          |  "category": "<spam/not spam>"
          |}
          |
          |
          |Example:
          |
          |Subject: "Claim Your Prize Now!"
          |Email Body: "Congratulations! You've won $1,000,000. Click here to claim your prize!"
          |
          |Example output:
          |{
          |  "subject": "Claim Your Prize Now!",
          |  "email": "Congratulations! You've won $1,000,000. Click here to claim your prize!",
          |  "analysis": {
          |    "spamLikelihood": "high",
          |    "reason": "The email contains a too-good-to-be-true offer, suspicious language, and a call-to-action link, which are strong indicators of spam."
          |  },
          |  "category": "spam"
          |}
          |""".stripMargin),
      Map("role" -> "user", "content" -> promptMessageForSpamCheck)
    )

    gptApiService.generateCompletionsForChat(
        chat = chat,
        userId = OrgId(0) // Action is not performed by any user so monitoring in GPT not required
      )
      .flatMap(res => {
        var flags: List[UnderReviewReason] = List()

        if (res.contains("\"spamLikelihood\": \"high\"")) {
          flags = flags.appended(UnderReviewReason.Spam)
        }

        gptApiService.categorizeTextModeration(
            text = emailBodyPlainText
          )
          .map(categories => {
            flags.appendedAll(categories)
          })

      })
  }
   */

  def rephraseText(
                    text: String,
                    orgId: OrgId,
                    teamId: TeamId,
                    accountId: AccountId
                  )(implicit ws: WSClient,
                    ec: ExecutionContext,
                    logger: SRLogger): Future[String] = {
    val apiHitLimitCheckKey = GPTService.getCacheKeyForAccountHittingGPTApi(
      accountId = accountId,
      orgId = orgId
    )

    srRateLimiter.rateLimitApiCall(
      key = apiHitLimitCheckKey,
      threshold = gptApiHitLimit,
      expireInSeconds = gptApiRateLimitIntervalInSeconds
    ) match {
      case Failure(e) =>
        logger.error(s"Error while doing GPT Api rate limit check for accountId_${accountId.id}", e)
        Future.failed(e)

      case Success(_) =>
        if (text.length < 20) {
          Future.failed(new Exception("Selected Text must be atleast 20 characters long."))
        }
        else {

          contentGenerationService.rephraseText(
            teamId = teamId,
            textToRephrase = text,
          ).map(_.rephrased_text)

        }
    }

  }

  def shortenText(
                   text: String,
                   teamId: TeamId,
                   orgId: OrgId,
                   accountId: AccountId
                 )(implicit ws: WSClient,
                   ec: ExecutionContext,
                   logger: SRLogger): Future[String] = {
    val apiHitLimitCheckKey = GPTService.getCacheKeyForAccountHittingGPTApi(
      accountId = accountId,
      orgId = orgId
    )

    srRateLimiter.rateLimitApiCall(
      key = apiHitLimitCheckKey,
      threshold = gptApiHitLimit,
      expireInSeconds = gptApiRateLimitIntervalInSeconds
    ) match {
      case Failure(e) =>
        logger.error(s"Error while doing GPT Api rate limit check for accountId_${accountId.id}", e)
        Future.failed(e)

      case Success(_) =>
        if (text.length < 50) {
          Future.failed(new Exception("Selected Text must be atleast 50 characters long."))
        }
        else {

          contentGenerationService.shortenText(
            teamId = teamId,
            textToShorten = text,
          ).map(_.shortened_text)

        }
    }
  }

}

object GPTService {

  private val envKeyPrefix = AppConfig.redisKeyPrefix

  /*
  def generatePromptMessageForSubject(generateEmailSubjectRequest: FieldsToGenerateEmailSubject): Try[String] = {

    if (generateEmailSubjectRequest.email_body.trim.isEmpty) {

      Failure(new Exception("Email Body Cannot be empty."))

    }
    else if (generateEmailSubjectRequest.max_chars < 20 ||
      generateEmailSubjectRequest.max_chars > 80) {

      Failure(new Exception("Maximum number of Characters in Subject line should be between 20 and 80."))

    }
    else {
      val promptMessage = "Generate Subject for this email Body." +
        generateEmailSubjectRequest.subject_line_type.map(text => s"It should be $text.").getOrElse("") +
        generateEmailSubjectRequest.avoid_words.map(text => s"It should not have following words: $text.").getOrElse("") +
        generateEmailSubjectRequest.capitalization.map(text => s"Capitalization should be $text.").getOrElse("") +
        s"It should not exceed more than ${generateEmailSubjectRequest.max_chars} characters." +
        s"Do not use \" in subject."

      if (generateEmailSubjectRequest.use_emoticons) {
        Success(promptMessage + "Use one Emoticon in Subject.")
      }
      else {
        Success(promptMessage)
      }
    }
  }
  */

  final def getCacheKeyForAccountHittingGPTApi(
                                                  accountId: AccountId,
                                                  orgId: OrgId
                                                ): String = {

    s"$envKeyPrefix:orgId_${orgId.id}_accountId_${accountId.id}"

  }




}
