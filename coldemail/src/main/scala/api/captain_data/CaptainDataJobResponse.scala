package api.captain_data

import api.accounts.TeamId
import api.accounts.models.AccountId
import api.linkedin.models.LinkedinSettingId
import org.joda.time.DateTime
import play.api.libs.json.*
import utils.dateTime.SrDateTimeUtils

case class CaptainDataMessageResponse(
                            position: Int,
                            created_at: Long,
                            linkedin_profile_handle: String,
                            first_name: String,
                            last_name: String,
                            linkedin_profile_id: String,
                            sales_navigator_profile_id: String,
                            job_title: String,
                            content: String
                          )

case class LinkedinThreadLastMessage(
                            delivered_at: DateTime,
                            first_name: String,
                            last_name: String,
                            linkedin_profile_id: String,
                            sales_navigator_profile_id: String,
                            job_title: String,
                            content: String,
                            message_id: String
                          )

object CaptainDataMessageResponse {
  given format: OFormat[CaptainDataMessageResponse] = Json.format[CaptainDataMessageResponse]
}

case class LinkedinThreadParticipant(
                                      linkedin_profile_id: String,
                                      sales_navigator_profile_id: String,
                                      linkedin_profile_url: String,
                                      first_name: String,
                                      last_name: String,
                                      job_title: String,
                                      linkedin_people_post_search_url: String
                                    )

case class LinkedinThreadMeta(
                               cd_account_uid: String,
                               linkedin_setting_id: LinkedinSettingId,
                               account_id: AccountId,
                               team_id: TeamId
                              )

case class LinkedinMessageMeta(
                               cd_account_uid: String,
                               linkedin_setting_id: LinkedinSettingId,
                               account_id: AccountId,
                               team_id: TeamId,
                               linkedin_thread_id: Long
                              )

enum CaptainDataJobResponse {
  case ViewLinkedinProfileResponse(
                                    first_name: String,
                                    last_name: String,
                                    full_name: String,
                                    industry: String,
                                    headline: String,
                                    summary: String,
                                    profile_country: String,
                                    profile_language: String,
                                    location: String,
                                    profile_image_url: String,
                                    job_title: String,
                                    company_name: String,
                                    company_employees_range: String,
                                    company_industry: String
                                  )
  case SentMessageResponse(
                            linkedin_profile_url: String,
                            linkedin_profile_handle: String,
                            full_name: String,
                            message: String,
                            sales_navigator_profile_id: String,
                            linkedin_profile_id: String,
                            linkedin_thread_id: String,
                            linkedin_thread_url: String,
                            is_sent: Boolean,
                            linkedin_people_post_search_url: String,
                            reply: Option[String],
                            delivery_at: Long,
                            linkedin_url: String,
                            profile_url: String,
                            profile_id: String,
                            handle: String,
                            id: String,
                            thread_id: String,
                            thread_url: String,
                            extracted_at: DateTime,
                            start_url: String
                          )

  case RetrieveMessageResponse(
                                first_name: String,
                                last_name: String,
                                full_name: String,
                                linkedin_profile_handle: String,
                                linkedin_profile_url: String,
                                linkedin_profile_id: String,
                                messages: List[CaptainDataMessageResponse],
                                linkedin_thread_id: String,
                                profile_id: String,
                                profile_url: String,
                                handle: String,
                                thread_id: String,
                                extracted_at: DateTime,
                                start_url: String,
                                meta: LinkedinMessageMeta
                              )

  case LinkedInThreadsResponse(
                             linkedin_profile_id: String,
                             linkedin_thread_id: String,
                             linkedin_thread_url: String,
                             linkedin_profile_handle: Option[String],
                             sales_navigator_profile_id: String,
                             linkedin_profile_url: String,
                             first_name: String,
                             last_name: String,
                             read: Boolean,
                             total_received_messages: Option[Int],
                             job_title: String,
//                             created_at: DateTime,
                             linkedin_people_post_search_url: String,
//                             last_activity_at: DateTime,
                             unread_count: Int,
                             sponsored: Boolean,
                             participants: List[LinkedinThreadParticipant],
                             last_message: LinkedinThreadLastMessage,
                             profile_url: String,
                             title: String,
                             profile_id: String,
                             handle: Option[String],
                             thread_id: String,
                             id: String,
                             total_messages: Option[Int],
                             extracted_at: DateTime,
                             meta: LinkedinThreadMeta
                           )
}

object CaptainDataJobResponse {

  // Custom format for SentMessageResponse to handle DateTime
  implicit val sentMessageResponseFormat: OFormat[CaptainDataJobResponse.SentMessageResponse] = {
    implicit val dateTimeFormat: Format[DateTime] = SrDateTimeUtils.DateTimeFormat_YYYYMMDDHHMMSS
    Json.format[CaptainDataJobResponse.SentMessageResponse]
  }

  // Simple format for ViewLinkedinProfileResponse as it doesn't need DateTime handling
  implicit val viewLinkedinProfileResponseFormat: OFormat[CaptainDataJobResponse.ViewLinkedinProfileResponse] =
    Json.format[CaptainDataJobResponse.ViewLinkedinProfileResponse]

  implicit val retrieveMessageResponseFormat: OFormat[CaptainDataJobResponse.RetrieveMessageResponse] = {
    implicit val dateTimeFormat: Format[DateTime] = SrDateTimeUtils.DateTimeFormat_YYYYMMDDHHMMSS
    Json.format[CaptainDataJobResponse.RetrieveMessageResponse]
  }

  implicit val participantFormat: OFormat[LinkedinThreadParticipant] = Json.format[LinkedinThreadParticipant]

  implicit val linkedinThreadResultFormat: OFormat[CaptainDataJobResponse.LinkedInThreadsResponse] = {
    implicit val dateTimeFormat: Format[DateTime] = SrDateTimeUtils.DateTimeFormat_YYYYMMDDHHMMSS
    Json.format[CaptainDataJobResponse.LinkedInThreadsResponse]
  }

  implicit val linkedinThreadMetaFormat: OFormat[LinkedinThreadMeta] = Json.format[LinkedinThreadMeta]

  implicit val LinkedinThreadLastMessageFormat: OFormat[LinkedinThreadLastMessage] = {
    implicit val dateTimeFormat: Format[DateTime] = SrDateTimeUtils.DateTimeFormat_ISO_WITH_MICROS
    Json.format[LinkedinThreadLastMessage]
  }

  implicit val linkedinMessageMetaFormat: OFormat[LinkedinMessageMeta] = Json.format[LinkedinMessageMeta]
}