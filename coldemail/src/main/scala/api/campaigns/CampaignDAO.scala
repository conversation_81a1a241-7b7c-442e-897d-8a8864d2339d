package api.campaigns

import api.AppConfig
import api.accounts.email.models.EmailServiceProvider
import api.accounts.{Account, OrganizationRole, TeamId}
import io.sr.billing_common.models.PlanType
import api.campaigns.models.{AccountData, CallSettingSenderDetails, CampaignAISequenceStatus, CampaignAnalysisFor, CampaignEmailSettingsId, CampaignInboxSettingData, CampaignName, CampaignPauseReason, CampaignStepType, CampaignToCheckForSendingLimitNewFlow, CampaignType, CampaignTypeData, ChannelSettingSenderDetails, ChannelStepType, CurrentStepStatusForScheduler, DripCampaignForm, InactiveCampaignCheckType, LinkedinSettingSenderDetails, SearchParams, SenderDataForSendingLimitNewFlow, SmsSettingSenderDetails, VoicemailDeleteData, VoicemailId, VoicemailUuid, WhatsappSettingSenderDetails}
import api.accounts.models.{FindCampaignIdsBy, OrgId}
import api.campaigns.CampaignDAO.getUpdateOrInsertQuery
import api.campaigns.models.ChannelStepType.{CallStepType, GeneralTaskStepType, IndependentStepType, LinkedinStepType, SmsStepType, WhatsappStepType}
import api.campaigns.services.{CampaignId, ChannelSettingData}
import api.integrations.SRTriggerSource
import api.prospects.SearchQuerySelectType
import api.prospects.dao_service.ProspectDAOService
import api.reports.AllCampaignStats
import api.search.{CampaignQuery, SearchQuery}
import api.spammonitor.model.EmailSendingEntityTypeData
import api.tags.models.CampaignTag
import api.team_inbox.service.{ReplySentimentUuid, TeamInboxService}
import api.triggers.TriggerInDB
import app_services.db_query_counter.SrDBQueryCounterService
import eventframework.ConversationObject.ConversationCampaignAndProspectDB
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.{DateTime, DateTimeZone}
import org.postgresql.util.PGobject
import api.accounts.models.AccountId
import api.call.models.{UpdateVoicemailDetailsRequest, VoiceDropData, VoiceDropDetails}
import api.campaigns.dao.CampaignEmailSettingsDAO
import api.campaigns.models.CampaignTypeData.DripCampaignData
import api.emails.models.EmailReplyType
import api.linkedin.models.{LinkedInServiceProvider, LinkedinAccountStatus, LinkedinSessionCookieAndUserAgent, LinkedinSettingUuid, UserAgent}
import api.prospects.models.{ProspectCategory, ProspectId}
import api.scheduler_report.model.{DueTaskPendingData, ScheduledOptedOutProspectData}
import api.search.CampaignQuery.campaignEmailSettingsSelect
import api.tasks.models.TaskStatusType
import api.team_inbox.model.ReplySentimentSubCategory

import scala.concurrent.{ExecutionContext, Future}
import play.api.libs.json.*
import play.api.libs.json.JodaWrites.*
import play.api.libs.json.JodaReads.*
import scalikejdbc.*
import scalikejdbc.jodatime.JodaWrappedResultSet.*
import sr_scheduler.CampaignStatus
import sr_scheduler.models.SendingMode
import sr_scheduler.models.CampaignForScheduling.{CampaignEmailSettingForScheduler, IndependentStepScheduling}
import sr_scheduler.models.{CalendarTeam, CampaignAIGenerationContext, CampaignDataToAddNextToBeScheduledAt, CampaignDataToAddNextToBeScheduledAtForEmailChannel, CampaignDataToAddNextToBeScheduledAtForMultiChannel, CampaignEmailPriority, CampaignForScheduling, CampaignWarmupSetting, ChannelData, ChannelType, SelectedCalendarData}
import utils.cronjobs.InactiveCampaignData
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus
import utils.customersupport.services.InternalAdoptionReport
import utils.{AppConfigUtil, Helpers, SRAppConfig, SRLogger, SrLoggerTrait}
import utils.dbutils.{DBUtils, SQLUtils}
import utils.email.EmailHelper
import utils.intercom.dao.InternalSRUsageData
import utils.mq.MqCampaignSendReportMsg
import utils.mq.channel_scheduler.ChannelSchedulerService
import utils.uuid.SrUuidUtils
import utils.security.EncryptionHelpers

import scala.util.{Failure, Success, Try}
import scala.concurrent.{ExecutionContext, blocking}


case class LinkedinAccountUuidAndStatus(
                                         uuid: LinkedinSettingUuid,
                                         status: LinkedinAccountStatus
                                       )

case class CampaignCreateForm(
                               name: Option[String],
                               timezone: Option[String],
                               campaign_owner_id: Option[Long],
                               campaign_type: CampaignType
                             )

case class MaxEmailsPerDay(
                          max_emails_per_day: Long
                          ) extends AnyVal {
  override def toString: String = max_emails_per_day.toString
}

object MaxEmailsPerDay{

  implicit val reads: Reads[MaxEmailsPerDay] = new Reads[MaxEmailsPerDay] {
    override def reads(ev: JsValue): JsResult[MaxEmailsPerDay] = {
      ev match {
        case JsNumber(id) => JsSuccess(MaxEmailsPerDay(max_emails_per_day = id.toLong))
        case randomValue => JsError(s"expected number, got some random value - $randomValue")
      }
    }
  }

}

case class IsUpdate(update: Boolean) extends AnyVal {
  override def toString: String = update.toString
}
// Todo: We can make ChannelSettingUuid
case class ChannelSettingUuid(uuid: String) extends AnyVal {
  override def toString: String = uuid
}

object ChannelSettingUuid{

  implicit val reads: Reads[ChannelSettingUuid] = new Reads[ChannelSettingUuid] {
    override def reads(ev: JsValue): JsResult[ChannelSettingUuid] = {
      ev match {
        case JsString(uuid) => JsSuccess(ChannelSettingUuid(uuid = uuid))
        case randomValue => JsError(s"expected String, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[ChannelSettingUuid] = new Writes[ChannelSettingUuid] {
    override def writes(o: ChannelSettingUuid): JsValue = JsString(o.toString)
  }
}

object CampaignCreateForm {
  implicit val reads: Reads[CampaignCreateForm] = Json.reads[CampaignCreateForm]
}


case class CampaignUpdateNameForm(
  name: String
)

object CampaignUpdateNameForm {
  given format: OFormat[CampaignUpdateNameForm] = Json.format[CampaignUpdateNameForm]
}


//object CampaignStatus extends Enumeration {
//  type CampaignStatus = Value
//  val NOT_STARTED = Value("not_started")
//  val SCHEDULED = Value("scheduled")
//  val RUNNING = Value("running")
//  val STOPPED = Value("stopped")
//  val ARCHIVED = Value("archived")
//}


case class CampaignSendStartReport(
                                    campaign_id: Long,
                                    campaign_name: String,
                                    team_id: Long,
                                    team_name: String,
                                    analyzed_at: DateTime,
                                    total_sent_prospects: Int,
                                    total_unsent_prospects: Int,
                                    total_unsent_prospects_in_dnc: Int,
                                    total_unsent_prospects_in_same_tz: Int,
                                    total_unsent_prospects_with_invalid_emails: Int,
                                    total_unsent_prospects_with_missing_merge_tags: Int,
                                    total_unsent_prospects_with_previous_task_not_done: Int,
                                    total_unsent_prospects_that_are_likely_valid: Int,
                                    campaign_analysis_result: CampaignPauseReason
                                  )

object CampaignSendStartReport{
  implicit val writes: OWrites[CampaignSendStartReport] = Json.writes[CampaignSendStartReport]
}

case class Campaign(
                     id: Long,
                     uuid: Option[String],
                     account_id: Long,
                     team_id: Long,
                     shared_with_team: Boolean,
                     name: String,
                     status: CampaignStatus,

                     head_step_id: Option[Long],

                     settings: CampaignSettings,

                     last_scheduled_at: Option[DateTime],

                     created_at: DateTime
)

case class CampaignIdAndTeamId(
                              campaign_id: Long,
                              team_id: Long
                              )

case class CampaignSettings(

                             // settings
                             campaign_email_settings: List[CampaignEmailSettings],
                             campaign_linkedin_settings: List[LinkedinSettingSenderDetails],
                             campaign_call_settings: List[CallSettingSenderDetails],
                             campaign_whatsapp_settings: List[WhatsappSettingSenderDetails],
                             campaign_sms_settings: List[SmsSettingSenderDetails],
                             timezone: String,
                             daily_from_time: Int, // time since beginning of day in seconds
                             daily_till_time: Int, // time since beginning of day in seconds
                             sending_holiday_calendar_id: Option[Long],

                             // Sunday is the first day
                             days_preference: List[Boolean],

                             mark_completed_after_days: Int,
                             max_emails_per_day: Int,
                             open_tracking_enabled: Boolean,
                             click_tracking_enabled: Boolean,
                             enable_email_validation: Boolean,
                             ab_testing_enabled: Boolean,

                             ai_sequence_status: Option[CampaignAISequenceStatus],

                             // warm up
                             warmup_started_at: Option[DateTime],
                             warmup_length_in_days: Option[Int],
                             warmup_starting_email_count: Option[Int],
                             show_soft_start_setting: Boolean,

                             // schedule start
                             schedule_start_at: Option[DateTime],
                             schedule_start_at_tz: Option[String],

                             // send plain test email
                             send_plain_text_email: Option[Boolean],

                             campaign_type: CampaignType,


                             email_priority: CampaignEmailPriority.Value,
                             append_followups: Boolean,
                             opt_out_msg: String,
                             opt_out_is_text: Boolean,
                             add_prospect_to_dnc_on_opt_out: Boolean,
                             triggers: Seq[TriggerInDB],
                             selected_calendar_data: Option[SelectedCalendarData],
                             sending_mode: Option[SendingMode] // Made optional


)

case class CampaignBasicDetails(
 id: Long,
 account_id: Long,
 team_id: Long,
 name: String,
 head_step_id: Option[Long]
)

case class CampaignForValidation(
  campaign_id:Long,
  account_id: Long,
  team_id: Long,
  org_id:Long,
  isAgency: Boolean
)

case class StuckCampaign(
                        campaignId: CampaignId,
                        teamId: TeamId,
                        timezone: String,
                        days_preference: List[Boolean]
                        )

object  CampaignForValidation{
  def fromDb(rs:WrappedResultSet) = {
    CampaignForValidation(
      campaign_id = rs.long("id"),
      account_id = rs.long("account_id"),
      team_id = rs.long("team_id"),
      org_id = rs.long("org_id"),
      isAgency = rs.boolean("is_agency")
    )
  }
}

object CampaignSettings {
  import utils.sr_json_utils.CampaignEmailPriorityUtil.formatCampaignEmailPriorityUtil

  //  implicit val reads : Reads[CampaignSettings] = Json.reads[CampaignSettings]
  implicit val reads: Reads[CampaignSettings] = new Reads[CampaignSettings] {
    override def reads(json: JsValue): JsResult[CampaignSettings] = Try {
      CampaignSettings(
        campaign_email_settings = (json \ "campaign_email_settings").as[List[CampaignEmailSettings]],
        timezone = (json \ "timezone").as[String],
        daily_from_time = (json \ "daily_from_time").as[Int], // time since beginning of day in seconds
        daily_till_time = (json \ "daily_till_time").as[Int], // time since beginning of day in seconds
        sending_holiday_calendar_id = (json \ "sending_holiday_calendar_id").asOpt[Long],

        // Sunday is the first day
        days_preference = (json \ "days_preference").as[List[Boolean]],

        mark_completed_after_days = (json \ "mark_completed_after_days").as[Int],
        max_emails_per_day = (json \ "max_emails_per_day").as[Int],
//        max_emails_per_day_from_email_account = (json \ "max_emails_per_day_from_email_account").asOpt[Int],
        open_tracking_enabled = (json \ "open_tracking_enabled").as[Boolean],
        click_tracking_enabled = (json \ "click_tracking_enabled").as[Boolean],
        enable_email_validation = (json \ "enable_email_validation").as[Boolean],
        ab_testing_enabled = (json \ "ab_testing_enabled").as[Boolean],

        ai_sequence_status = (json \ "ai_sequence_status").asOpt[String].map(CampaignAISequenceStatus.fromString(_).get),

        // warm up
        warmup_started_at = (json \ "warmup_started_at").asOpt[DateTime],
        warmup_length_in_days = (json \ "warmup_length_in_days").asOpt[Int],
        warmup_starting_email_count = (json \ "warmup_starting_email_count").asOpt[Int],

        // schedule start
        schedule_start_at = (json \ "schedule_start_at").asOpt[DateTime],
        schedule_start_at_tz = (json \ "schedule_start_at_tz").asOpt[String],


        email_priority = (json \ "email_priority").as[CampaignEmailPriority.Value],
        append_followups = (json \ "append_followups").as[Boolean],
        opt_out_msg = (json \ "opt_out_msg").as[String],
        opt_out_is_text = (json \ "opt_out_is_text").as[Boolean],
        add_prospect_to_dnc_on_opt_out = (json \ "add_prospect_to_dnc_on_opt_out").as[Boolean],
        triggers = (json \ "triggers").as[Seq[TriggerInDB]],

        send_plain_text_email = (json \ "send_plain_text_email").asOpt[Boolean],
        campaign_type = (json \ "campaign_type").as[CampaignType],

//        from_email = (json \ "from_email").asOpt[String],
//        to_email = (json \ "to_email").asOpt[String],
//        signature = (json \ "signature").asOpt[String],
//        calendar_settings_data = (json \ "calendar_settings_data").asOpt[CalendarSettingsData]
        campaign_linkedin_settings = (json \ "campaign_linkedin_settings").as[List[LinkedinSettingSenderDetails]],
        campaign_call_settings = (json \ "campaign_call_settings").as[List[CallSettingSenderDetails]],
        campaign_whatsapp_settings = (json \ "campaign_whatsapp_settings").as[List[WhatsappSettingSenderDetails]] ,
        campaign_sms_settings = (json \ "campaign_sms_settings").as[List[SmsSettingSenderDetails]],
        show_soft_start_setting = (json \ "show_soft_start_setting").as[Boolean],
        selected_calendar_data = (json \ "selected_calendar_data").asOpt[SelectedCalendarData],
        sending_mode = (json \ "sending_mode").asOpt[SendingMode] // Changed from .getOrElse(SendingMode.AutoPilot)
      )
    } match {
      case Failure(e) => JsError(e.getMessage)
      case Success(campaignSetting) => JsSuccess(campaignSetting)
    }
  }

  // create campaign json object
  implicit val writes: Writes[CampaignSettings] = new Writes[CampaignSettings] {
    def writes(c: CampaignSettings) = Json.obj(
      "campaign_linkedin_settings" -> c.campaign_linkedin_settings,
      "campaign_whatsapp_settings" -> c.campaign_whatsapp_settings,
      "campaign_call_settings" -> c.campaign_call_settings,
      "campaign_sms_settings" -> c.campaign_sms_settings,
      "campaign_email_settings" -> c.campaign_email_settings,
      "timezone" -> c.timezone,
      "daily_from_time" -> c.daily_from_time, // time since beginning of day in seconds
      "daily_till_time" -> c.daily_till_time, // time since beginning of day in seconds
      "sending_holiday_calendar_id" -> c.sending_holiday_calendar_id,

      "ai_sequence_status" -> c.ai_sequence_status,

      // Sunday is the first day
      "days_preference" -> c.days_preference,

      "mark_completed_after_days" -> c.mark_completed_after_days,
      "max_emails_per_day" -> c.max_emails_per_day,
      "open_tracking_enabled" -> c.open_tracking_enabled,
      "click_tracking_enabled" -> c.click_tracking_enabled,
      "enable_email_validation" -> c.enable_email_validation,
      "ab_testing_enabled" -> c.ab_testing_enabled,

      // warm up
      "warmup_started_at" -> c.warmup_started_at,
      "warmup_length_in_days" -> c.warmup_length_in_days,
      "warmup_starting_email_count" -> c.warmup_starting_email_count,
      "show_soft_start_setting" -> c.show_soft_start_setting,

      // schedule start
      "schedule_start_at" -> c.schedule_start_at,
      "schedule_start_at_tz" -> c.schedule_start_at_tz,

      "send_plain_text_email" -> c.send_plain_text_email,
      "campaign_type" -> c.campaign_type,


      "email_priority" -> c.email_priority.toString,
      "append_followups" -> c.append_followups,
      "opt_out_msg" -> c.opt_out_msg,
      "opt_out_is_text" -> c.opt_out_is_text,
      "add_prospect_to_dnc_on_opt_out" -> c.add_prospect_to_dnc_on_opt_out,
      "triggers" -> c.triggers,
      "selected_calendar_data" -> c.selected_calendar_data,
      "sending_mode" -> c.sending_mode // Default Option Writes handles None/Some

    )
  }
}

///
case class CampaignBasicInfo(
                              id: Long,
                              uuid: Option[String],
                              team_id: Long,
                              name: String,
                              owner_name: String,
                              owner_email: String,
                              owner_id: Long,
                              status: CampaignStatus,
                              tags: Seq[CampaignTag],
                              head_step_id: Option[Long],
                              created_at: DateTime,
                              shared_with_team: Boolean,
                              warmup_is_on: Boolean,
                              error: Option[String],
                              spam_test_exists: Boolean,
                              ai_generation_context: Option[CampaignAIGenerationContext],
                              settings: CampaignSettings,
                              campaign_has_email_step: Boolean,
                              is_archived: Boolean
                            )

object CampaignBasicInfo{

  implicit val writes: Writes[CampaignBasicInfo] = new Writes[CampaignBasicInfo] {
    def writes(c: CampaignBasicInfo) = Json.obj(
      "id" -> c.id,
      "uuid" -> c.uuid,
      "shared_with_team" -> c.shared_with_team,
      "warmup_is_on" -> c.warmup_is_on,
      "team_id" -> c.team_id,
      "name" -> c.name,
      "owner_name" -> c.owner_name,
      "owner_email" -> c.owner_email,
      "owner_id" -> c.owner_id,

      "status" -> c.status.toString,

      "tags" -> c.tags,

      "head_step_id" -> c.head_step_id,
      "created_at" -> c.created_at,
      "error" -> c.error,
      "spam_test_exists"-> c.spam_test_exists,
      "ai_generation_context" -> c.ai_generation_context,
      "campaign_has_email_step" -> c.campaign_has_email_step,
      "settings"-> c.settings,
      "is_archived" -> c.is_archived

    )
  }

  implicit val reads: Reads[CampaignBasicInfo] =Json.reads[CampaignBasicInfo]

}

case class StartCampaignWarmup(
  warmup_length_in_days: Int,
  warmup_starting_email_count: Int
)

object StartCampaignWarmup {
  implicit val reads: Reads[StartCampaignWarmup] = Json.reads[StartCampaignWarmup]
}

case class CampaignWithStatsAndEmail(
                                      id: Long,
                                      uuid: Option[String],
                                      is_archived: Boolean,
                                      team_id: Long,
                                      shared_with_team: Boolean,
                                      name: String,
                                      owner_name: String,
                                      owner_email: String,
                                      owner_id: Long,
                                      status: CampaignStatus,
                                      tags: Seq[CampaignTag],
                                      spam_test_exists: Boolean,
                                      warmup_is_on: Boolean,

                                      stats: AllCampaignStats,

                                      head_step_id: Option[Long],

                                      settings: CampaignSettings,

                                      created_at: DateTime,
                                    
                                      ai_generation_context: Option[CampaignAIGenerationContext],

                                      error: Option[String]

                                    )

object CampaignWithStatsAndEmail {
  // implicit val writes = Json.writes[CampaignWithStatsAndEmail]

  import utils.sr_json_utils.CampaignStatusJsonUtil.readsCampaignStatus

//  implicit val reads = new Reads[CampaignWithStatsAndEmail] {
//    override def reads(json: JsValue): JsResult[CampaignWithStatsAndEmail] = Try {
//      CampaignWithStatsAndEmail(
//        id = (json \ "id").as[Int],
//        uuid =  (json \ "uuid").asOpt[String],
//        team_id = (json \ "team_id").as[Long],
//        shared_with_team = (json \ "shared_with_team").as[Boolean],
//        name = (json \ "name").as[String],
//        owner_name = (json \ "owner_name").as[String],
//        owner_email = (json \ "owner_email").as[String],
//        owner_id = (json \ "owner_id").as[Long],
//        status = (json \ "status").as[CampaignStatus],
//        tags = (json \ "tags").as[Seq[CampaignTag]],
//        spam_test_exists = (json \ "spam_test_exists").as[Boolean],
//        warmup_is_on = (json \ "warmup_is_on").as[Boolean],
//
//        stats = (json \ "stats").as[AllCampaignStats],
//
//        head_step_id = (json \ "head_step_id").asOpt[Long],
//
//        settings = (json \ "settings").as[CampaignSettings],
//
//        created_at = (json \ "created_at").as[DateTime],
//
//        error = (json \ "error").asOpt[String],
//
//        is_archived = (json\ "is_archived").as[Boolean]
//      )
//    } match {
//      case Failure(e) => JsError(e.getMessage)
//      case Success(campaignWithStatsAndEmail) => JsSuccess(campaignWithStatsAndEmail)
//    }
//
//  }


  // create campaign json object
  implicit val writes: Writes[CampaignWithStatsAndEmail] = new Writes[CampaignWithStatsAndEmail] {
    def writes(c: CampaignWithStatsAndEmail) =
      Json.obj(
      "id" -> c.id,
      "uuid" -> c.uuid,
      "team_id" -> c.team_id,
      "name" -> c.name,
      "owner_name" -> c.owner_name,
      "owner_email" -> c.owner_email,
      "owner_id" -> c.owner_id,

      "status" -> c.status.toString,

      "tags" -> c.tags,

      "spam_test_exists" -> c.spam_test_exists,
      "warmup_is_on" -> c.warmup_is_on,

      "head_step_id" -> c.head_step_id,
      "ai_generation_context" -> c.ai_generation_context,
      "created_at" -> c.created_at,
      "error" -> c.error,
      "stats" -> c.stats,
      "settings" -> c.settings,
      "is_archived" -> c.is_archived

    )
  }

  def apiStructure(c: CampaignWithStatsAndEmail, version: String) = {

    val jsonObjectWithIdentifier = if(version == "v3"){
      Json.obj(
        "uuid" -> c.uuid
      )
    } else {
      Json.obj(
        "id" -> c.id,
      )
    }


    jsonObjectWithIdentifier ++ Json.obj(
      "name" -> c.name,
      "owner_id" -> c.owner_id,
      // "owner_name" -> c.owner_name,
      // "owner_email" -> c.owner_email,
      "status" -> c.status.toString,
      "created_at" -> c.created_at,
      // "error" -> c.error,
      "stats" -> AllCampaignStats.apiStructure(s = c.stats)
    )
  }

}


object CampaignForSchedulingFromDB {

  def getSoftStartSettings(rs: WrappedResultSet): Option[CampaignWarmupSetting] = {

    val warmup_started_at = rs.jodaDateTimeOpt("warmup_started_at")

    warmup_started_at.map(warmupStartedAt => {

      val warmup_length_in_days = rs.int("warmup_length_in_days")
      val warmup_starting_email_count = rs.int("warmup_starting_email_count")

      CampaignWarmupSetting(
        warmup_started_at = warmupStartedAt,
        warmup_length_in_days = warmup_length_in_days,
        warmup_starting_email_count = warmup_starting_email_count
      )


    })

  }

  def fromDBGeneralTask(rs: WrappedResultSet): CampaignForScheduling.CampaignForSchedulingGeneral = {

    CampaignForScheduling.CampaignForSchedulingGeneral(
      campaign_id = rs.int("id"),
      campaign_name = rs.string("name"),
      campaign_owner_id = rs.long("account_id"),
      team_id = rs.long("team_id"),

      campaign_type_data = campaignTypeDataFromDB(rs),

      // Sunday is the first day
      // FIXME MULTICHANNEL: days_preference value should be returned from a function
      days_preference = rs.array("days_preference").getArray.asInstanceOf[Array[java.lang.Boolean]].toList.asInstanceOf[List[Boolean]],
      timezone = rs.string("timezone"),
      daily_from_time = rs.int("daily_from_time"),
      daily_till_time = rs.int("daily_till_time"),
      sending_holiday_calendar_id = rs.longOpt("sending_holiday_calendar_id"),
      softstart_setting = getSoftStartSettings(rs),
      email_priority = CampaignEmailPriority.withName(rs.string("email_priority")),
      mark_completed_after_days = rs.int("mark_completed_after_days"),
      prospects_remaining_to_be_scheduled_exists = rs.booleanOpt("prospects_remaining_to_be_scheduled_exists"),
      selected_calendar_data = CampaignDAO.fromDbSelectedCalendarData(rs),
      campaign_channel_setting_uuid = rs.string("campaign_channel_setting_uuid")
    )
  }

  def fromDbLinkedinTask(rs: WrappedResultSet): CampaignForScheduling.CampaignForSchedulingLinkedin = {
    CampaignForScheduling.CampaignForSchedulingLinkedin(
      campaign_id = rs.int("id"),
      campaign_name = rs.string("name"),
      campaign_owner_id = rs.long("account_id"),
      team_id = rs.long("team_id"),

      campaign_type_data = campaignTypeDataFromDB(rs),

      // Sunday is the first day
      // FIXME MULTICHANNEL: days_preference value should be returned from a function
      days_preference = rs.array("days_preference").getArray.asInstanceOf[Array[java.lang.Boolean]].toList.asInstanceOf[List[Boolean]],
      timezone = rs.string("timezone"),
      daily_from_time = rs.int("daily_from_time"),
      daily_till_time = rs.int("daily_till_time"),
      sending_holiday_calendar_id = rs.longOpt("sending_holiday_calendar_id"),
      softstart_setting = getSoftStartSettings(rs),
      email_priority = CampaignEmailPriority.withName(rs.string("email_priority")),
      mark_completed_after_days = rs.int("mark_completed_after_days"),
      prospects_remaining_to_be_scheduled_exists = rs.booleanOpt("prospects_remaining_to_be_scheduled_exists"),
      selected_calendar_data = CampaignDAO.fromDbSelectedCalendarData(rs),
      campaign_channel_setting_uuid = rs.string("campaign_channel_setting_uuid"),
      captain_data_user_id = rs.stringOpt("captain_data_user_id"),
      captain_data_account_id = rs.stringOpt("captain_data_account_id"),
      service_provider = rs.stringOpt("service_provider").flatMap(key => LinkedInServiceProvider.fromKey(key).get)

    )
  }

  def fromDBWhatsappTask(rs: WrappedResultSet): CampaignForScheduling.CampaignForSchedulingWhatsApp = {
    CampaignForScheduling.CampaignForSchedulingWhatsApp(
      campaign_id = rs.int("id"),
      campaign_name = rs.string("name"),
      campaign_owner_id = rs.long("account_id"),
      team_id = rs.long("team_id"),

      campaign_type_data = campaignTypeDataFromDB(rs),

      // Sunday is the first day
      // FIXME MULTICHANNEL: days_preference value should be returned from a function
      days_preference = rs.array("days_preference").getArray.asInstanceOf[Array[java.lang.Boolean]].toList.asInstanceOf[List[Boolean]],
      timezone = rs.string("timezone"),
      daily_from_time = rs.int("daily_from_time"),
      daily_till_time = rs.int("daily_till_time"),
      sending_holiday_calendar_id = rs.longOpt("sending_holiday_calendar_id"),
      softstart_setting = getSoftStartSettings(rs),
      email_priority = CampaignEmailPriority.withName(rs.string("email_priority")),
      mark_completed_after_days = rs.int("mark_completed_after_days"),
      prospects_remaining_to_be_scheduled_exists = rs.booleanOpt("prospects_remaining_to_be_scheduled_exists"),
      selected_calendar_data = CampaignDAO.fromDbSelectedCalendarData(rs),
      campaign_channel_setting_uuid = rs.string("campaign_channel_setting_uuid")
    )
  }


  def fromDBSmsTask(rs: WrappedResultSet): CampaignForScheduling.CampaignForSchedulingSms = {
    CampaignForScheduling.CampaignForSchedulingSms(
      campaign_id = rs.long("id"),
      campaign_name = rs.string("name"),
      campaign_owner_id = rs.long("account_id"),
      team_id = rs.long("team_id"),

      campaign_type_data = campaignTypeDataFromDB(rs),

      // Sunday is the first day
      // FIXME MULTICHANNEL: days_preference value should be returned from a function
      days_preference = rs.array("days_preference").getArray.asInstanceOf[Array[java.lang.Boolean]].toList.asInstanceOf[List[Boolean]],
      timezone = rs.string("timezone"),
      daily_from_time = rs.int("daily_from_time"),
      daily_till_time = rs.int("daily_till_time"),
      sending_holiday_calendar_id = rs.longOpt("sending_holiday_calendar_id"),
      softstart_setting = getSoftStartSettings(rs),
      email_priority = CampaignEmailPriority.withName(rs.string("email_priority")),
      mark_completed_after_days = rs.int("mark_completed_after_days"),
      prospects_remaining_to_be_scheduled_exists = rs.booleanOpt("prospects_remaining_to_be_scheduled_exists"),
      selected_calendar_data = CampaignDAO.fromDbSelectedCalendarData(rs),
      campaign_channel_setting_uuid = rs.string("campaign_channel_setting_uuid")
    )
  }

  def fromDBCallTask(rs: WrappedResultSet): CampaignForScheduling.CampaignForSchedulingCall = {
    CampaignForScheduling.CampaignForSchedulingCall(
      campaign_id = rs.long("id"),
      campaign_name = rs.string("name"),
      campaign_owner_id = rs.long("account_id"),
      team_id = rs.long("team_id"),

      campaign_type_data = campaignTypeDataFromDB(rs),

      // Sunday is the first day
      // FIXME MULTICHANNEL: days_preference value should be returned from a function
      days_preference = rs.array("days_preference").getArray.asInstanceOf[Array[java.lang.Boolean]].toList.asInstanceOf[List[Boolean]],
      timezone = rs.string("timezone"),
      daily_from_time = rs.int("daily_from_time"),
      daily_till_time = rs.int("daily_till_time"),
      sending_holiday_calendar_id = rs.longOpt("sending_holiday_calendar_id"),
      softstart_setting = getSoftStartSettings(rs),
      email_priority = CampaignEmailPriority.withName(rs.string("email_priority")),
      mark_completed_after_days = rs.int("mark_completed_after_days"),
      prospects_remaining_to_be_scheduled_exists = rs.booleanOpt("prospects_remaining_to_be_scheduled_exists"),
      selected_calendar_data = CampaignDAO.fromDbSelectedCalendarData(rs),
      campaign_channel_setting_uuid = rs.string("campaign_channel_setting_uuid")
    )
  }

  def fromDBIndependentStepScheduling(rs: WrappedResultSet): CampaignForScheduling.IndependentStepScheduling = {
    CampaignForScheduling.IndependentStepScheduling(
      campaign_id = rs.long("id"),
      campaign_name = rs.string("name"),
      campaign_owner_id = rs.long("account_id"),
      team_id = rs.long("team_id"),

      campaign_type_data = campaignTypeDataFromDB(rs),

      // Sunday is the first day
      // FIXME MULTICHANNEL: days_preference value should be returned from a function
      timezone = rs.string("timezone"),
      mark_completed_after_days = rs.int("mark_completed_after_days"),
    )
  }

  def fromDBMultichannel(rs: WrappedResultSet, channelStepType: ChannelStepType): CampaignForScheduling = {
  // Todo Multichannel : make this match exhaustive
    channelStepType match {
      case _: GeneralTaskStepType =>
        fromDBGeneralTask(rs)

      case _: WhatsappStepType =>
        fromDBWhatsappTask(rs)

      case _: LinkedinStepType =>
        fromDbLinkedinTask(rs)

      case _: SmsStepType =>
        fromDBSmsTask(rs)

      case _: CallStepType =>
        fromDBCallTask(rs)
    }

  }

  def campaignTypeDataFromDB(rs: WrappedResultSet): CampaignTypeData = {
    rs.stringOpt("campaign_type").map(CampaignType.fromString(_).get).getOrElse(CampaignType.MultiChannel) match {

      case CampaignType.Email =>
        CampaignTypeData.EmailChannelData(
          head_step_id = rs.long("head_step_id")
        )

      case CampaignType.MultiChannel =>
        CampaignTypeData.MultiChannelCampaignData(
          head_step_id = rs.long("head_step_id")
        )

      case CampaignType.Drip =>
        CampaignTypeData.DripCampaignData(
          edges = Json.parse(rs.string("drip_campaign_edges")).as[List[JsValue]],
          nodes = Json.parse(rs.string("drip_campaign_nodes")).as[List[JsValue]],
          head_node_id = rs.string("head_node_id_for_drip")
        )
        
      case CampaignType.MagicContent =>
        CampaignTypeData.MultiChannelCampaignData( // Default to MultiChannel data structure for now
          head_step_id = rs.long("head_step_id")
        )
    }
  }

  def fromDb(rs: WrappedResultSet, Logger: SrLoggerTrait): CampaignForScheduling.CampaignForSchedulingEmail = {

    val enableEmailValidationOrgLevel = rs.boolean("enable_email_validation_org_level")
    val enableEmailValidationCampaignLevel = rs.booleanOpt("enable_email_validation").getOrElse(true)

    // disable validation if its disabled on either org or campaign level
    val enableEmailValidation = enableEmailValidationOrgLevel && enableEmailValidationCampaignLevel

    val campaignAIGenerationContext: Option[CampaignAIGenerationContext] = rs.stringOpt("ai_generation_context").map(Json.parse(_).as[CampaignAIGenerationContext])

    CampaignForScheduling.CampaignForSchedulingEmail(
      campaign_id = rs.int("id"),
      campaign_owner_id = rs.long("account_id"),
      team_id = rs.long("team_id"),
      org_id = rs.long("org_id"),
      campaign_name = rs.string("name"),
      status = CampaignStatus.fromKey(rs.string("status")).get,


      campaign_type_data = campaignTypeDataFromDB(rs),

      ai_generation_context = campaignAIGenerationContext,

      // settings
      sending_holiday_calendar_id = rs.longOpt("sending_holiday_calendar_id"),
      campaign_email_setting = CampaignEmailSettingForScheduler(
        sender_email_settings_id = rs.int("sender_email_setting_id"),
        receiver_email_settings_id = rs.int("receiver_email_setting_id"),
        campaign_email_settings_id = CampaignEmailSettingsId(rs.long("campaign_email_settings_id")),
        emailServiceProvider = EmailServiceProvider.fromKey(rs.string("service_provider")).get
      ),

      append_followups = rs.boolean("append_followups"),
      open_tracking_enabled = rs.boolean("open_tracking_enabled"),
      click_tracking_enabled = rs.boolean("click_tracking_enabled"),
      opt_out_msg = rs.stringOpt("opt_out_msg").getOrElse(""),
      opt_out_is_text = rs.boolean("opt_out_is_text"),

      timezone = rs.string("timezone"),
      daily_from_time = rs.int("daily_from_time"),
      daily_till_time = rs.int("daily_till_time"),
      days_preference = rs.array("days_preference").getArray.asInstanceOf[Array[java.lang.Boolean]].toList.asInstanceOf[List[Boolean]],

      email_priority = CampaignEmailPriority.withName(rs.string("email_priority")),

      max_emails_per_prospect_per_day = rs.int("max_emails_per_prospect_per_day"),
      max_emails_per_prospect_per_week = rs.int("max_emails_per_prospect_per_week"),

      max_emails_per_prospect_account_per_day = rs.int("max_emails_per_prospect_account_per_day"),
      max_emails_per_prospect_account_per_week = rs.int("max_emails_per_prospect_account_per_week"),

      campaign_max_emails_per_day = rs.int("max_emails_per_day"),

      softstart_setting = getSoftStartSettings(rs),

      mark_completed_after_days = rs.int("mark_completed_after_days"),

      latest_email_scheduled_at = rs.jodaDateTimeOpt("latest_email_scheduled_at"),


      from_email = rs.string("from_email"),
      from_name = rs.string("from_name"),

      reply_to_email = rs.string("reply_to_email"),
      reply_to_name = rs.string("reply_to_name"),

      min_delay_seconds = rs.int("min_delay_seconds"),
      max_delay_seconds = rs.int("max_delay_seconds"),

      enable_email_validation = enableEmailValidation,

      sending_mode = rs.stringOpt("sending_mode").map(SendingMode.fromString(_).get),
      
      rep_mail_server_id = rs.int("rep_mail_server_id"),
      via_gmail_smtp = rs.booleanOpt("via_gmail_smtp"),
      prospects_remaining_to_be_scheduled_exists = rs.booleanOpt("prospects_remaining_to_be_scheduled_exists"),
      count_of_sender_emails = rs.int("count_of_sender_emails"),
      selected_calendar_data = CampaignDAO.fromDbSelectedCalendarData(rs)

    )
  }

  def campaignChannelSetupFromDB(
                                rs: WrappedResultSet
                                ): ChannelSettingData = {

    val channelType = ChannelType.fromKey(rs.string("channel_type")).get
    val uuid = rs.string("channel_settings_uuid")
    val team_id = rs.long("team_id")
    val campaign_id = rs.long("campaign_id")

    ChannelSettingData(
      channel_type = channelType,
      channel_setting_uuid = ChannelSettingUuid(
        uuid = uuid
      ),
      team_id = TeamId(
        id = team_id
      ),
      campaign_id = CampaignId(
        id = campaign_id
      )
    )

  }

  def fromDbGetChannelSenderDetailsForSetting(
                                             rs: WrappedResultSet,
                                             channel_type: ChannelType
                                             ): ChannelSettingSenderDetails = {


    val (email, phone_number, first_name, last_name):(Option[String], Option[String], String, String) = channel_type match {
      case ChannelType.WhatsappChannel =>
        (None, rs.stringOpt("whatsapp_number"), rs.string("first_name"), rs.string("last_name"))

      case ChannelType.LinkedinChannel =>
        (rs.stringOpt("email"), None, rs.string("first_name"), rs.string("last_name"))

      case ChannelType.SmsChannel =>
        (None, rs.stringOpt("phone_number"), rs.string("first_name"), rs.string("last_name"))

      case ChannelType.CallChannel =>
        (None,  rs.stringOpt("phone_number"), rs.string("first_name"), rs.string("last_name"))

      case ChannelType.GeneralChannel =>
        (None, None, "", "") // These cases will never happen 

      case ChannelType.EmailChannel | ChannelType.IndependentChannel =>
        (None, None, "", "") // These cases will never happen

    }


    ChannelSettingSenderDetails(
      channel_type = channel_type,
      channel_setting_uuid = ChannelSettingUuid(
        uuid = rs.string("uuid")
      ),
      team_id = TeamId(
        id = rs.long("team_id")
      ),
      owner_account_id = AccountData.AccountId(
        account_id = rs.long("owner_account_id")
      ),
      email = email,
      phone_number = phone_number,
      sender_name = first_name ++ " " ++ last_name,
      first_name = first_name,
      last_name = last_name
    )



  }
}



case class CampaignJustIdNameV2(
  campaign_id: Long,
  campaign_name: String
)

// fixme: rename this case class
case class CampaignJustIdName(
  id: Long,
  account_id: Long,
  name: String,
  owner_name: String,
  has_edit_campaign_permission: Boolean,
  status: CampaignStatus,
  created_at: DateTime,
  uuid: String
)

object CampaignJustIdName {
  // implicit val writes = Json.writes[CampaignJustIdName]

  implicit val writes: Writes[CampaignJustIdName] = new Writes[CampaignJustIdName] {
    def writes(i: CampaignJustIdName) = {

      Json.obj(
        "id" -> i.id,
        "name" -> i.name,
        "owner_name" -> i.owner_name,
        "has_edit_campaign_permission" -> i.has_edit_campaign_permission,
        "created_at" -> i.created_at,
        "uuid" -> i.uuid
      )
    }
  }
}

object CampaignDAO {

  def fromDbSelectedCalendarData(rs: WrappedResultSet): Option[SelectedCalendarData] = {
    val calendar_is_individual = rs.booleanOpt("calendar_is_individual")
    if (calendar_is_individual.isDefined) {
      Some(SelectedCalendarData(
        calendar_selected_username_slug = rs.stringOpt("calendar_selected_username_slug"),
        calendar_selected_user_id = rs.intOpt("calendar_selected_user_id"),
        calendar_event_type_id = rs.intOpt("calendar_event_type_id"),
        calendar_event_type_slug = rs.stringOpt("calendar_event_type_slug"),
        calendar_smartreach_account_id = rs.longOpt("calendar_smartreach_account_id"),
        calendar_team_id = rs.intOpt("calendar_team_id"),
        calendar_team_slug = rs.stringOpt("calendar_team_slug"),
        calendar_is_individual = calendar_is_individual
      ))
    } else {
      None
    }

  }

  def findCampaignsForSchedulingTasksQuery(channelStepType: ChannelStepType, channelSettingUuid: String) = {

    // Todo Multichannel: make this match exhaustive
    val table_name = channelStepType match {

      case _: WhatsappStepType =>
        sqls"""whatsapp_settings"""

      case _: LinkedinStepType =>
        sqls"""linkedin_settings"""

      case _: GeneralTaskStepType =>
        sqls"""general_channel_settings"""

      case _: SmsStepType =>
        sqls"""sms_settings"""

      case _: CallStepType =>
        sqls"""call_settings"""

    }

    val table_specific_select = channelStepType match {

      case _: WhatsappStepType =>
        sqls""""""

      case _: LinkedinStepType =>
        sqls"""
              captain_data_user_id, 
              captain_data_account_id,
              service_provider,
            """

      case _: GeneralTaskStepType =>
        sqls""""""

      case _: SmsStepType =>
        sqls""""""

      case _: CallStepType =>
        sqls""""""

    }

    val running: String = CampaignStatus.RUNNING.toString

    val query =
      sql"""
  SELECT

  c.*,
  $table_specific_select
  cs.max_tasks_per_prospect_per_day,

  cs.max_tasks_per_prospect_per_week,

  cs.latest_task_scheduled_at,
  csm.prospects_remaining_to_be_scheduled_exists,
  ccs.channel_settings_uuid as campaign_channel_setting_uuid,

  o.id as org_id


  FROM campaigns c
  INNER JOIN campaign_channel_settings ccs ON (ccs.campaign_id = c.id AND ccs.team_id = c.team_id)
  INNER JOIN $table_name cs ON (cs.uuid = ccs.channel_settings_uuid AND cs.team_id = ccs.team_id)
  INNER JOIN teams t ON t.id = c.team_id
  INNER JOIN accounts a ON a.id = c.account_id
  INNER JOIN organizations o ON o.id = a.org_id
  LEFT JOIN campaign_scheduling_metadata csm ON (c.id = csm.campaign_id AND c.team_id = csm.team_id)

  WHERE cs.uuid = $channelSettingUuid

    AND c.status = $running
    AND c.head_step_id IS NOT NULL
    -- AND (gs.paused_till IS NULL OR gs.paused_till < now())

    AND (o.paused_till IS NULL OR o.paused_till < now())
    AND (
        (
          csm.prospects_remaining_to_be_scheduled_exists OR
          csm.prospects_remaining_to_be_scheduled_exists IS NULL
        )
        OR o.is_agency

    )

    AND (
      ccs.next_to_be_scheduled_at IS NULL OR
       (
          ccs.next_to_be_scheduled_at < now()
          -- NOTE Multichannel: Skipping For general / linkedin / whatsapp task because there is no reply tracking
          -- AND
          -- res.last_read_for_replies > now() - interval '15 minutes'
        )
    )

    -- helps prioritize older campaigns slightly, requested by E2E in May 2020
    ORDER BY c.id;
"""

//    println(s"\n\nfindCampaignsForSchedulingTasksQuery: $query")

    query

  }

  def findCampaignsForSchedulingIndependentStep(campaign_id: CampaignId, team_id: TeamId) = {

    val running: String = CampaignStatus.RUNNING.toString

    val query =
      sql"""
  SELECT

  c.*,

  o.id as org_id

  FROM campaigns c
 -- INNER JOIN campaign_channel_settings ccs ON (ccs.campaign_id = c.id AND ccs.team_id = c.team_id)
  INNER JOIN teams t ON t.id = c.team_id
  INNER JOIN accounts a ON a.id = c.account_id
  INNER JOIN organizations o ON o.id = a.org_id
  --LEFT JOIN campaign_scheduling_metadata csm ON (c.id = csm.campaign_id AND c.team_id = csm.team_id)

  WHERE
    c.id = ${campaign_id.id}
    AND c.team_id = ${team_id.id}
    AND c.status = $running
    AND c.head_step_id IS NOT NULL
    -- AND (gs.paused_till IS NULL OR gs.paused_till < now())

    AND (o.paused_till IS NULL OR o.paused_till < now())


    -- helps prioritize older campaigns slightly, requested by E2E in May 2020
    ORDER BY c.id;
"""

    //    println(s"\n\nfindCampaignsForSchedulingTasksQuery: $query")

    query

  }

  def getUpdateOrInsertQuery(
                              team_id: TeamId,
                              campaign_id: CampaignId,
                              channelType: ChannelType,
                              channel_settings_uuid: ChannelSettingUuid,
                              isUpdate: IsUpdate
                            ): SQL[Nothing, NoExtractor] = {

    val uuid = sqls"""${channel_settings_uuid.uuid}"""
    if (isUpdate.update) {

      sql"""
        UPDATE campaign_channel_settings
          SET
            channel_settings_uuid = ${uuid}
          WHERE
              team_id = ${team_id.id}
            AND
              campaign_id = ${campaign_id.id}
            AND
              channel_type = ${channelType.toString}
          RETURNING campaign_id, team_id;
      """
    } else {
      sql"""
        INSERT INTO campaign_channel_settings
          (
             team_id,
             campaign_id,
             channel_type,
             channel_settings_uuid
          )

          VALUES
          (
              ${team_id.id},
              ${campaign_id.id},
              ${channelType.toString},
              ${channel_settings_uuid.uuid}
          )

          RETURNING campaign_id, team_id;
      """
    }
  }



  def getCampaignLevelError(
                             campaign_email_settings: List[CampaignEmailSettings]
                           ): Option[String] = {
    val errors: Set[String] = campaign_email_settings.flatMap(ces => ces.error.map(e => ces.sender_email +" : " + e)).toSet

    val error: Option[String] = if(errors.isEmpty) None
    else Some(errors.mkString(" :: "))

    error
  }

}


class CampaignDAO(
  srDBQueryCounterService: SrDBQueryCounterService,
  srUuidUtils: SrUuidUtils
) {


//  val DONOTCONTACT_CATEGORYID = Inbox.donotContactCatId // from Inbox.prospect_categories

  // Note: dont uncomment the below Json.writes line, the Campaign case class is for internal
  // purpose only. The CampaignWithStatsAndEmail case class is to be sent
  // as json whenever needed.
  // implicit val writes = Json.writes[Campaign]

  implicit val session: AutoSession.type = AutoSession
  private def fromDbCampaignSettings(rs: WrappedResultSet, status: CampaignStatus): CampaignSettings = {

    val triggers = Json.parse(rs.any("triggers").asInstanceOf[PGobject].getValue).validate[Seq[TriggerInDB]].get

    val scheduleCampaignStartAt = rs.jodaDateTimeOpt("schedule_start_at")

    val warmup_started_at = rs.jodaDateTimeOpt("warmup_started_at")

    val warmupStartsAt: Option[DateTime] = status match {
      case CampaignStatus.SCHEDULED => warmup_started_at match {
        case Some(value) => scheduleCampaignStartAt
        case None => None
      }
      case CampaignStatus.RUNNING => warmup_started_at
      case CampaignStatus.NOT_STARTED |
           CampaignStatus.ON_HOLD |
           CampaignStatus.STOPPED |
           CampaignStatus.ARCHIVED |
           CampaignStatus.UNDER_REVIEW |
           CampaignStatus.SUSPENDED => None
    }

    val campaign_linkedin_settings = rs
      .anyOpt("campaign_linkedin_settings")
      .map(r => Json
        .parse(r.asInstanceOf[PGobject].getValue)
        .validate[List[LinkedinSettingSenderDetails]].get
      )
      .getOrElse(List())

    val campaign_whatsapp_settings = rs
      .anyOpt("campaign_whatsapp_settings")
      .map(r => Json
        .parse(r.asInstanceOf[PGobject].getValue)
        .validate[List[WhatsappSettingSenderDetails]].get
      )
      .getOrElse(List())

    val campaign_sms_settings = rs
      .anyOpt("campaign_sms_settings")
      .map(r => Json
        .parse(r.asInstanceOf[PGobject].getValue)
        .validate[List[SmsSettingSenderDetails]].get
      )
      .getOrElse(List())

    val campaign_call_settings = rs
      .anyOpt("campaign_call_settings")
      .map(r => Json
        .parse(r.asInstanceOf[PGobject].getValue)
        .validate[List[CallSettingSenderDetails]].get
      )
      .getOrElse(List())

    val campaign_email_settings = rs
      .anyOpt("campaign_email_settings")
      .map(r => Json
        .parse(r.asInstanceOf[PGobject].getValue)
        .validate[List[CampaignEmailSettings]].get)
      .getOrElse(List())


    CampaignSettings(
      // settings
      campaign_linkedin_settings = campaign_linkedin_settings,
      campaign_whatsapp_settings = campaign_whatsapp_settings,
      campaign_call_settings = campaign_call_settings,
      campaign_sms_settings = campaign_sms_settings,
      campaign_email_settings = campaign_email_settings,
      timezone = rs.string("timezone"),
      daily_from_time = rs.int("daily_from_time"),
      daily_till_time = rs.int("daily_till_time"),
      sending_holiday_calendar_id = rs.longOpt("sending_holiday_calendar_id"),
      days_preference = rs.array("days_preference").getArray.asInstanceOf[Array[java.lang.Boolean]].toList.asInstanceOf[List[Boolean]],

      ai_sequence_status = rs.stringOpt("ai_sequence_status").map(CampaignAISequenceStatus.fromString(_).get),

      mark_completed_after_days = rs.int("mark_completed_after_days"),
      max_emails_per_day = rs.int("max_emails_per_day"),
      open_tracking_enabled = rs.boolean("open_tracking_enabled"),
      click_tracking_enabled = rs.boolean("click_tracking_enabled"),
      enable_email_validation = rs.booleanOpt("enable_email_validation").getOrElse(true),
      ab_testing_enabled = rs.boolean("ab_testing_enabled"),

      warmup_started_at = warmupStartsAt,
      warmup_length_in_days = if (warmupStartsAt.isEmpty) None else rs.intOpt("warmup_length_in_days"),
      warmup_starting_email_count = if (warmupStartsAt.isEmpty) None else rs.intOpt("warmup_starting_email_count"),
      show_soft_start_setting = if (warmupStartsAt.isEmpty) rs.boolean("show_soft_start_setting") else true,

      // schedule start
      schedule_start_at = scheduleCampaignStartAt,
      schedule_start_at_tz = rs.stringOpt("schedule_start_at_tz"),
      send_plain_text_email = rs.booleanOpt("send_plain_text_email"),
      campaign_type = CampaignType.fromString(rs.string("campaign_type")).get,

      email_priority = CampaignEmailPriority.withName(rs.string("email_priority")),
      append_followups = rs.boolean("append_followups"),
      opt_out_msg = rs.string("opt_out_msg"),
      opt_out_is_text = rs.boolean("opt_out_is_text"),
      add_prospect_to_dnc_on_opt_out = rs.boolean("add_prospect_to_dnc_on_opt_out"),
      triggers = triggers,
      selected_calendar_data = CampaignDAO.fromDbSelectedCalendarData(rs),
      sending_mode = rs.stringOpt("sending_mode").map(SendingMode.fromString(_).get)
    )
  }

  private def fromDbWithCampaignObject(rs: WrappedResultSet): CampaignBasicInfo = {
    val status = CampaignStatus.fromKey(rs.string("status")).get

    val isNotRunning = CampaignStatus.isNotRunning(status)

    val ctagsRaw = rs.anyOpt("ctags")
    val tags = if (ctagsRaw.isEmpty) Seq() else {
      Json.parse(ctagsRaw.get.asInstanceOf[PGobject].getValue)
        .validate[Seq[CampaignTag]]
        .getOrElse(Seq())
    }

    val settings = fromDbCampaignSettings(rs, status = status)

    val ai_generation_campaign_context: Option[CampaignAIGenerationContext] = rs.stringOpt("ai_generation_context").map(d => Json.parse(d).as[CampaignAIGenerationContext])

    CampaignBasicInfo(
      id = rs.long("id"),
      uuid = rs.stringOpt("uuid"),
      team_id = rs.long("team_id"),
      shared_with_team = rs.boolean("shared_with_team"),
      name = rs.string("name"),
      owner_name = rs.string("owner_name"),
      owner_email = rs.string("owner_email"),
      owner_id = rs.long("owner_account_id"),
      status = status,
      tags = tags,
      spam_test_exists = rs.boolean("spam_test_exists"),
      warmup_is_on = if (isNotRunning) false else rs.boolean("warmup_is_on"),
      ai_generation_context = ai_generation_campaign_context,

      error = CampaignDAO.getCampaignLevelError(campaign_email_settings = settings.campaign_email_settings),

      head_step_id = rs.longOpt("head_step_id"),
      campaign_has_email_step = rs.boolean("campaign_has_email_step"),
      created_at = rs.jodaDateTime("created_at"),

      settings = settings,
      is_archived = rs.boolean("is_archived")


    )
  }

  def fromDbCampaignBasicInfo(rs: WrappedResultSet):CampaignBasicInfo ={
    val status = CampaignStatus.fromKey(rs.string("status")).get

    val ctagsRaw = rs.anyOpt("ctags")
    val tags = if (ctagsRaw.isEmpty) Seq() else {
      Json.parse(ctagsRaw.get.asInstanceOf[PGobject].getValue)
        .validate[Seq[CampaignTag]]
        .getOrElse(Seq())
    }
    val isNotRunning = CampaignStatus.isNotRunning(status)

    val ai_generation_campaign_context: Option[CampaignAIGenerationContext] = rs.stringOpt("ai_generation_context").map(d => Json.parse(d).as[CampaignAIGenerationContext])


    val settings = fromDbCampaignSettings(rs, status = status)
    CampaignBasicInfo(
      id = rs.long("id"),
      uuid = rs.stringOpt("uuid"),
      team_id = rs.long("team_id"),
      name = rs.string("name"),
      owner_name = rs.string("owner_name"),
      owner_email = rs.string("owner_email"),
      owner_id = rs.long("owner_account_id"),
      status = status,
      tags = tags,
      head_step_id = rs.longOpt("head_step_id"),
      campaign_has_email_step = rs.boolean("campaign_has_email_step"),
      ai_generation_context = ai_generation_campaign_context,
      created_at = rs.jodaDateTime("created_at"),
      warmup_is_on = if (isNotRunning) false else rs.boolean("warmup_is_on"),
      shared_with_team = rs.boolean("shared_with_team"),
      error = CampaignDAO.getCampaignLevelError(campaign_email_settings = settings.campaign_email_settings),
      spam_test_exists = rs.boolean("spam_test_exists"),
      settings = settings,
      is_archived = rs.boolean("is_archived")
    )
  }


  private def fromDbCampaign(rs: WrappedResultSet): Campaign = {
    val status = CampaignStatus.fromKey(rs.string("status")).get

    Campaign(
    id = rs.long("id"),
    uuid = rs.stringOpt("uuid"),
    account_id = rs.long("account_id"),
    team_id = rs.long("team_id"),
    shared_with_team = rs.boolean("shared_with_team"),
    name = rs.string("name"),
    status = status,

    head_step_id = rs.longOpt("head_step_id"),
    created_at = rs.jodaDateTime("created_at"),
    last_scheduled_at = rs.jodaDateTimeOpt("last_scheduled_at"),

    settings = fromDbCampaignSettings(rs, status = status)

    )
  }

  private def fromDbCampaignBasicDetails(rs: WrappedResultSet): CampaignBasicDetails = CampaignBasicDetails(
    id = rs.long("id"),
    account_id = rs.long("account_id"),
    team_id = rs.long("team_id"),
    name = rs.string("name"),
    head_step_id = rs.longOpt("head_step_id")
  )

  def query(
    accountIds: Seq[Long],
    teamId: Long,
    orgId: Long,
    loggedinAccountId: Long,
    data: SearchQuery,
    account: Account,
    Logger: SRLogger
  ): Try[Seq[CampaignBasicInfo]] = Try {

    val query = CampaignQuery
      .getQuerySQL(
        permittedAccountIds = accountIds,
        teamId = teamId,
        orgId = orgId,
        data = data,
        account = account,
        fetchType = SearchQuerySelectType.FULL_OBJECT,
        Logger = Logger
      )
      .get
      ._3

    val clist = DB readOnly { implicit session =>

      query
        .map(fromDbCampaignBasicInfo)
        .list
        .apply()

    }

//    val ownedCampaigns = clist.filter(c => c.owner_id == loggedinAccountId)
//    val unownedCampaigns = clist.filter(c => c.owner_id != loggedinAccountId)

    // groupedByStatusForOwned
//    val o = ownedCampaigns.sortBy(_.id).reverse.groupBy(_.status)

    // groupedByStatusForUnowned
//    val u = unownedCampaigns.sortBy(_.id).reverse.groupBy(_.status)

//    o.getOrElse(CampaignStatus.RUNNING, Seq[CampaignBasicInfo]()) ++
//      u.getOrElse(CampaignStatus.RUNNING, Seq[CampaignBasicInfo]()) ++
//      o.getOrElse(CampaignStatus.SCHEDULED, Seq[CampaignBasicInfo]()) ++
//      u.getOrElse(CampaignStatus.SCHEDULED, Seq[CampaignBasicInfo]()) ++
//      o.getOrElse(CampaignStatus.UNDER_REVIEW, Seq[CampaignBasicInfo]()) ++
//      u.getOrElse(CampaignStatus.UNDER_REVIEW, Seq[CampaignBasicInfo]()) ++
//      o.getOrElse(CampaignStatus.SUSPENDED, Seq[CampaignBasicInfo]()) ++
//      u.getOrElse(CampaignStatus.SUSPENDED, Seq[CampaignBasicInfo]()) ++
//      o.getOrElse(CampaignStatus.NOT_STARTED, Seq[CampaignBasicInfo]()) ++
//      u.getOrElse(CampaignStatus.NOT_STARTED, Seq[CampaignBasicInfo]()) ++
//      o.getOrElse(CampaignStatus.STOPPED, Seq[CampaignBasicInfo]()) ++
//      u.getOrElse(CampaignStatus.STOPPED, Seq[CampaignBasicInfo]()) ++
//      o.getOrElse(CampaignStatus.ARCHIVED, Seq[CampaignBasicInfo]()) ++
//      u.getOrElse(CampaignStatus.ARCHIVED, Seq[CampaignBasicInfo]())
    clist
  }



  // SENDER_ROTATION
  // called from -
  // CampaignService.getCampaignBasicInfo
  // CampaignService.find
  def getCampaignBasicInfo(
                     teamId: Long,
                     cId: Long
                   ): Try[Option[CampaignBasicInfo]] = Try{
    val query = CampaignQuery
      .getModifiedQuerySQL(
        cId = cId,
        teamId = teamId
      )
      .get

    val campaign = DB readOnly { implicit session =>
      query
        .map(fromDbCampaignBasicInfo)
        .single
        .apply()
      }

    campaign

  }
  def getCampaignBasicInfoWithPermission(
                     teamId: Long,
                     cId: Long,
                     permittedAccountIds: Seq[Long]
                   ): Try[Option[CampaignBasicInfo]] = Try{
    val query = CampaignQuery
      .getModifiedQuerySQLWithPermission(
        cId = cId,
        teamId = teamId,
        permittedAccountIds = permittedAccountIds
      )
      .get

    val campaign = DB readOnly { implicit session =>
      query
        .map(fromDbCampaignBasicInfo)
        .single
        .apply()
      }

    campaign

  }

  def getCampaignBasicInfoV3(
                              accountIds: Seq[Long],
                              teamId: Long,
                              orgId: Long,
                              loggedinAccountId: Long,
                              search_params: SearchParams,
                              Logger: SRLogger
                            ): Try[List[CampaignBasicInfo]] = Try {
    val query = CampaignQuery
      .getCampaignsV3QuerySQL(
        permittedAccountIds = accountIds,
        teamId = teamId,
        orgId = orgId,
        search_params = search_params,
        Logger = Logger
      )
      .get

    val clist = DB readOnly { implicit session =>

      query
        .map(fromDbCampaignBasicInfo)
        .list
        .apply()

    }


    clist

  }

  def updateReportsId(
                       latestCampaignSendStartReportsId: Long,
                       campaignId: CampaignId,
                       teamId: TeamId
                     ): Try[Int] = Try {

    DB.autoCommit {implicit session =>
      sql"""
           UPDATE campaigns
           SET
             latest_campaign_send_start_reports_id = $latestCampaignSendStartReportsId,
             in_queue_for_analyzing_start_report = false
           WHERE
             id = ${campaignId.id}
             AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }

  }

  def updateCampaignContext(
                           data: CampaignAIGenerationContext,
                           campaign_id: CampaignId,
                           team_id: TeamId
                           ): Try[Int] = Try{

    DB.autoCommit{ implicit session =>
      val json_string = Json.stringify(Json.toJson(data))
      sql"""

           UPDATE campaigns
             SET  ai_generation_context = $json_string::jsonb
           where id = ${campaign_id.id}
           and team_id = ${team_id.id}

         """
        .update
        .apply()
    }
  }

  def hasAlreadyTriggeredAISequenceGenerationForCampaign(
                                                          accountId: AccountId,
                                                          teamId: TeamId
                                                        ): Try[Option[String]] = Try {

    DB readOnly {implicit session =>
      sql"""
           SELECT name FROM campaigns
            WHERE account_id = ${accountId.id}
            AND team_id = ${teamId.id}
            AND ai_sequence_status = ${CampaignAISequenceStatus.Triggered.toString}

            LIMIT 1
           ;
           """
        .map(rs => rs.string("name"))
        .single
        .apply()
    }

  }

  def changeCampaignAISequenceStatus(
                                      campaignId: CampaignId,
                                      teamId: TeamId,
                                      status: CampaignAISequenceStatus
                                    ): Try[String] = Try {

    val updateTimestamp: SQLSyntax = status match {
      case CampaignAISequenceStatus.Triggered =>
        sqls" ai_sequence_generation_triggered_at = now(), "

      case CampaignAISequenceStatus.Finished =>
        sqls" ai_sequence_generation_finished_at = now(), "
    }

    DB autoCommit {implicit session =>
      sql"""
           UPDATE campaigns
           SET
              $updateTimestamp
              ai_sequence_status = ${status.toString}
           WHERE id = ${campaignId.id}
           AND team_id = ${teamId.id}
           RETURNING name
           ;
           """
        .map(rs => rs.string("name"))
        .single
        .apply()
        .get
    }

  }

  def doneInAnalyzingStartReportQueue(
                                       campaignId: CampaignId,
                                       teamId: TeamId
                                     ): Try[Int] = Try {

    DB.autoCommit {implicit session =>
      sql"""
           UPDATE campaigns
           SET
             in_queue_for_analyzing_start_report = false
           WHERE
             id = ${campaignId.id}
             AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }

  }

  def getEmailSendingCampaignData(
                                   campaignId: CampaignId,
                                   teamId: TeamId
                                 ): Try[EmailSendingEntityTypeData.CampaignData] = Try{
    DB readOnly {implicit session =>
      sql"""
           SELECT
              c.team_id,
              t.org_id
           FROM campaigns c
           INNER JOIN teams t ON t.id = c.team_id
           WHERE c.id = ${campaignId.id} AND c.team_id = ${teamId.id}
           ;
           """
        .map(rs => {
          EmailSendingEntityTypeData.CampaignData(
            campaignId = campaignId.id,
            orgId = OrgId(rs.long("org_id")),
            teamId = rs.long("team_id")
          )
        })
        .single
        .apply()
        .get
    }
  }
  
  def updateValuesForPushingToAnalysisQueue(
                                           campaignIds: List[Long]
                                           ): Try[List[CampaignIdAndTeamId]] = Try {
    if(campaignIds.isEmpty) {
      List()
    } else {
      DB.autoCommit { implicit session =>
        sql"""
           UPDATE campaigns
           SET
             pushed_to_queue_for_analysing_start_report_at = now(),
             in_queue_for_analyzing_start_report = TRUE
           WHERE
             id IN ($campaignIds)

           RETURNING id, team_id
           ;
           """
          .map(rs => CampaignIdAndTeamId(
            campaign_id = rs.long("id"),
            team_id = rs.long("team_id")
          ))
          .list
          .apply()
      }
    }
  }

  def isEmailSentInLast30Mins(): Try[Boolean] = Try {
    DB readOnly {implicit session =>
      sql"""
           SELECT EXISTS(
             SELECT c.id FROM campaigns c
             WHERE
               c.status = ${CampaignStatus.RUNNING.toString} AND
               c.last_email_sent_at > now() - interval '30 minutes'
           )
           ;
           """
        .map(rs => rs.boolean("exists"))
        .single
        .apply()
        .getOrElse(false)
    }
  }

  def fetchStuckCampaigns(is_new_flow: Boolean): Try[List[StuckCampaign]] = Try {

    val campaignSendReportAnalysisInterval = if(is_new_flow) {
      SQLSyntax.createUnsafely(AppConfig.campaignSendReportAnalysisIntervalInMinutesNew.toString)
    }else SQLSyntax.createUnsafely(AppConfig.campaignSendReportAnalysisIntervalInMinutes.toString)


    val stuckInQueueInterval = if(is_new_flow){
      SQLSyntax.createUnsafely((AppConfig.campaignSendReportAnalysisIntervalInMinutesNew * 2).toString)
    } else SQLSyntax.createUnsafely((AppConfig.campaignSendReportAnalysisIntervalInMinutes * 2).toString)

    val daily_from_time_sql: SQLSyntax = sqls"(((current_timestamp AT TIME ZONE c.timezone )::date + c.daily_from_time * interval '1' second)::timestamptz AT TIME ZONE 'UTC' AT TIME ZONE c.timezone)"
    val daily_till_time_sql: SQLSyntax = sqls"(((current_timestamp AT TIME ZONE c.timezone )::date + c.daily_till_time * interval '1' second)::timestamptz AT TIME ZONE 'UTC' AT TIME ZONE c.timezone)"
    val org_id_for_new_report = AppConfig.RollingUpdates.org_id_for_new_report

    val check_for_orgs_in_new_flow: SQLSyntax = if(org_id_for_new_report.nonEmpty && is_new_flow) {
      sqls"and o.id in ($org_id_for_new_report)"
    } else sqls"AND (o.metadata ->> 'show_campaign_send_start_report'):: boolean"

    val check_last_ran_for_new_flow: SQLSyntax = if(org_id_for_new_report.nonEmpty && is_new_flow) {
      sqls"""
            AND NOT EXISTS (
            SELECT * from campaign_sending_volume_logs csvl
            where
              csvl.campaign_id = c.id
              and csvl.team_id = c.team_id
              and csvl.analysis_for = ${CampaignAnalysisFor.SendingVolume.toString}
              and
              (
                csvl.added_at > now() - interval '30 minutes'
                OR
                (
                csvl.pushed_for_checking_at_minutes_since_campaign_start_time = 120
                and
                csvl.added_at > $daily_from_time_sql
                )
              )
              LIMIT 1
            )
            AND now() - interval '50 minutes' >= $daily_from_time_sql
            AND now() - interval '150 minutes' <= $daily_from_time_sql
          """
    } else
      sqls"""
          AND now() BETWEEN $daily_from_time_sql
          AND $daily_till_time_sql
          """
    DB.readOnly { implicit session =>
      sql"""
          SELECT c.id, c.team_id, c.timezone, c.days_preference FROM campaigns c
          INNER JOIN teams t ON c.team_id = t.id
          INNER JOIN organizations o ON o.id = t.org_id
          WHERE (
             c.last_email_sent_at IS NULL
             OR
             c.last_email_sent_at < now() - interval '15 minutes'
             )

          AND EXISTS ( SELECT * FROM campaign_steps WHERE campaign_id = c.id AND step_type = ${CampaignStepType.AutoEmailStep.toKey} )

          $check_last_ran_for_new_flow

          AND c.status = ${CampaignStatus.RUNNING.toString}

          AND o.plan_type != ${PlanType.INACTIVE.toString}

          $check_for_orgs_in_new_flow

          AND (o.payment_due_campaign_pause_at IS NULL OR o.payment_due_campaign_pause_at > now())

          AND (
            (
              c.in_queue_for_analyzing_start_report = FALSE
                AND (
                  c.pushed_to_queue_for_analysing_start_report_at IS NULL
                  OR
                  c.pushed_to_queue_for_analysing_start_report_at < now() - interval '$campaignSendReportAnalysisInterval minutes'
                )
            )
            OR
            c.pushed_to_queue_for_analysing_start_report_at < now() - interval '$stuckInQueueInterval minutes'
         )
         ;
         """
        .map(rs => StuckCampaign(
          campaignId = CampaignId(id = rs.long("id")),
          teamId = TeamId(id = rs.long("team_id")),
          timezone = rs.string("timezone"),
          // FIXME MULTICHANNEL: days_preference value should be returned from a function
          days_preference = rs.array("days_preference").getArray.asInstanceOf[Array[java.lang.Boolean]].toList.asInstanceOf[List[Boolean]],
        ))
        .list
        .apply()
    }
  }

  def checkIfLastSentEmailAtIsNull(campaignId: CampaignId, teamId: TeamId): Try[Boolean] = Try {

    DB readOnly { implicit session => {
      sql"""
           SELECT EXISTS(
                 SELECT 1 FROM campaigns
                 WHERE id = ${campaignId.id} AND
                 team_id = ${teamId.id} AND
                 last_email_sent_at IS NOT NULL
               );
         """.map(rs => {
        !rs.boolean("exists")
      })
        .single
        .apply()
        .get
    }
    }
  }

/*
11 July 2024 :
    Not added the blacklist check here for the domain as this function doesn't have any functionality related
    to scheduling or pausing the sneding from the email
 */
  def fetchRunningCampaignsDataForLowSending(
                                              mqCampaignSendReportMsg: MqCampaignSendReportMsg
                                            ): Try[CampaignToCheckForSendingLimitNewFlow] =  Try {

    DB readOnly{ implicit session =>
      sql"""
         SELECT
           c.id as campaign_id,
           c.team_id,
           c.warmup_started_at,
           c.warmup_length_in_days,
           c.warmup_starting_email_count,
           c.max_emails_per_day,
           c.daily_from_time,
           c.daily_till_time,
           c.days_preference,
           c.timezone,

           (select count(*) from emails_scheduled es1
           where es1.team_id = c.team_id
           and es1.campaign_id = c.id
           and scheduled_from_campaign
           and scheduled_at > (((current_timestamp AT TIME ZONE c.timezone )::date + c.daily_from_time * interval '1' second)::timestamptz AT TIME ZONE 'UTC' AT TIME ZONE c.timezone)
           and sent
           ) as total_emails_sent_till_now,

           (select count(*) from emails_scheduled es1
           where es1.team_id = c.team_id
           and es1.campaign_id = c.id
           and scheduled_from_campaign
           and scheduled_at > (((current_timestamp AT TIME ZONE c.timezone )::date + c.daily_from_time * interval '1' second)::timestamptz AT TIME ZONE 'UTC' AT TIME ZONE c.timezone)
           ) as total_scheduled_for_today_till_now,

           (
           SELECT (
           json_agg(
           json_build_object(
             'quota_per_day', sender.quota_per_day,
             'number_of_campaigns', (SELECT count(*)
                                      from campaign_email_settings ces2
                                      inner join campaigns c2 on c2.team_id = ces2.team_id and c2.id = ces2.campaign_id
                                      where ces2.team_id = ces.team_id
                                      and ces.sender_email_setting_id = ces2.sender_email_setting_id
                                      and c2.status = ${CampaignStatus.RUNNING.toString}),
             'min_delay_seconds', sender.min_delay_seconds,
             'max_delay_seconds', sender.max_delay_seconds
           ))) as senders
           from campaign_email_settings ces
           inner JOIN email_settings sender ON sender.id = ces.sender_email_setting_id
           AND ces.team_id = sender.team_id
           WHERE
            ces.campaign_id = c.id
            and sender.status = ${EmailSettingStatus.Active.toString}
            AND ces.team_id = c.team_id
            AND (sender.paused_till IS NULL OR sender.paused_till < now())
            AND NOT sender.is_under_review
           ) as senders


         FROM campaigns c
         where c.id = ${mqCampaignSendReportMsg.campaignId.id}
         and c.team_id = ${mqCampaignSendReportMsg.teamId.id}

         """
        .map(rs =>
          CampaignToCheckForSendingLimitNewFlow(
            campaign_id = CampaignId(rs.long("campaign_id")),
            team_id = TeamId(rs.long("team_id")),
            warmupSettingOpt = CampaignForSchedulingFromDB.getSoftStartSettings(rs),
            max_emails_per_day = rs.int("max_emails_per_day"),
            daily_from_time = rs.int("daily_from_time"),
            daily_till_time = rs.int("daily_till_time"),
            days_preference =  rs.array("days_preference").getArray.asInstanceOf[Array[java.lang.Boolean]].toList.asInstanceOf[List[Boolean]],
            timezone  = rs.string("timezone"),
            total_emails_sent_till_now = rs.int("total_emails_sent_till_now"),
            total_scheduled_for_today_till_now = rs.int("total_scheduled_for_today_till_now"),
            senders = rs
              .anyOpt("senders")
              .map(r => Json
                .parse(r.asInstanceOf[PGobject].getValue)
                .validate[List[SenderDataForSendingLimitNewFlow]].get)
              .getOrElse(List())
          )
        )
        .single
        .apply()
        .get
    }

  }

  // TODO: Change this to Future
  def updateLastEmailSentAt(
                             campaignId: CampaignId,
                             teamId: TeamId
                           ): Try[Int] = Try {
    DB.autoCommit {implicit session =>
      sql"""
           UPDATE campaigns
           SET last_email_sent_at = now()
           WHERE id = ${campaignId.id}
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }
  }


  def getCampaignStatsById(
             cId:Long,
             teamId: Long,
             doNotContactCategoryId: Long,
             listOfPositiveUuid: List[ReplySentimentUuid],
             Logger: SRLogger,
             campaignHasEmailStep: Boolean
           ): Try[AllCampaignStats] = {

    for {

      query <- Try {
        CampaignQuery
          .getCampaignStatsQuerySQL(
            cId = cId,
            teamId = teamId,
            doNotContactCategoryId = doNotContactCategoryId,
            listOfPositiveUuid = listOfPositiveUuid,
            Logger = Logger,
            campaignHasEmailStep = campaignHasEmailStep
          )
      }

      campaignOpt: Option[AllCampaignStats] <- Try {
        DB readOnly { implicit session =>

          query
            .map(rs => AllCampaignStats.fromDb(rs = rs, campaignHasEmailStep = campaignHasEmailStep))
            .single
            .apply()

        }
      }

      campaign: AllCampaignStats <- campaignOpt match {
        case None => Failure(new Exception(s"No campaign found for campaign id: ${cId}"))
        case Some(campaign) => Success(campaign)
      }
    } yield {
      campaign
    }



  }


  def findCampaignObject(
    teamId: Long,
    showCampaignTags: Boolean,
    whereClause: SQLSyntax,
    listOfPositiveUuid: List[ReplySentimentUuid]
  ): SQL[?, ?] = {


    val campaignTagsSubQuery: SQLSyntax = CampaignQuery.genCampaignTagsSubquery(
      teamId = teamId,
      showCampaignTags = showCampaignTags
    )

    val rsPositiveCountSql: SQLSyntax = if (listOfPositiveUuid.nonEmpty) {

      sqls""" COUNT(cp.*) FILTER(WHERE cp.reply_sentiment_uuid in ${SQLUtils.generateSQLValuesClause(listOfPositiveUuid.map(_.uuid))}) """

    } else {

      sqls""" 0 """

    }

    val show_soft_start_settings: SQLSyntax = CampaignQuery.showSoftStartQuery

    val campaign_email_setting_select: SQLSyntax = CampaignQuery.campaignEmailSettingsSelect()

    val campaign_linkedin_setting_select: SQLSyntax = CampaignQuery.campaignLinkedinSettingSelect()
    val campaign_whatsapp_setting_select: SQLSyntax = CampaignQuery.campaignWhatsappSettingSelect()
    val campaign_sms_setting_select: SQLSyntax = CampaignQuery.campaignSmsSettingSelect()
    val campaign_call_setting_select: SQLSyntax = CampaignQuery.campaignCallSettingSelect()

    sql"""
      SELECT
        a.email AS owner_email,
        a.id AS owner_account_id,
        CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,

        COALESCE(json_agg(trig) FILTER (WHERE trig.id IS NOT NULL), '[]') AS triggers,
        c.id,
        c.account_id,
        c.name,
        c.status,
        c.is_archived,
        c.timezone,
        c.daily_from_time,
        c.daily_till_time,
        c.days_preference,
        c.ai_sequence_status,
        c.ai_generation_context,
        c.created_at,
        c.head_step_id,
        c.last_scheduled_at,
        c.email_priority,
        c.opt_out_msg,
        c.opt_out_is_text,
        c.team_id,
        c.shared_with_team,
        c.ta_id,
        c.max_emails_per_day,
        c.mark_completed_after_days,
        c.open_tracking_enabled,
        c.warmup_started_at,
        c.warmup_starting_email_count,
        c.warmup_length_in_days,
        c.click_tracking_enabled,
        c.latest_email_scheduled_at,
        c.append_followups,
        c.status_changed_at,
        c.will_delete,
        c.will_delete_at,
        c.enable_email_validation,
        c.ab_testing_enabled,
        c.add_prospect_to_dnc_on_opt_out,
        c.schedule_start_at,
        c.schedule_start_at_tz,
        c.last_analysed_at,
        c.pushed_to_queue_for_analysis_at,
        c.sending_holiday_calendar_id,
        -- c.next_to_be_scheduled_at,
        c.last_email_sent_at,
        c.latest_campaign_send_start_reports_id,
        c.pushed_to_queue_for_analysing_start_report_at,
        c.in_queue_for_analyzing_start_report,
        c.last_pushed_for_preemailvalidation_at,
        c.uuid,
        c.calendar_event_type_id,
        c.calendar_event_type_slug,
        c.calendar_is_individual,
        c.calendar_selected_user_id,
        c.calendar_selected_username_slug,
        c.send_plain_text_email,
        c.calendar_smartreach_account_id,
        c.calendar_team_id,
        c.campaign_type,
        c.calendar_team_slug,
        c.sending_mode,
        $campaign_email_setting_select,
        $campaign_linkedin_setting_select,
        $campaign_call_setting_select,
        $campaign_whatsapp_setting_select,
        $campaign_sms_setting_select,
        $campaignTagsSubQuery,

        $show_soft_start_settings,

        has_spam_test.exists AS spam_test_exists,
        (SELECT EXISTS(
          SELECT cs.id FROM campaign_steps cs 
          WHERE cs.campaign_id = c.id 
          AND (
            cs.step_type = ${CampaignStepType.AutoEmailStep.toKey}
            OR
            cs.step_type = ${CampaignStepType.ManualEmailStep.toKey}
          )
        )) AS campaign_has_email_step,

        (case when c.warmup_started_at is null or c.warmup_length_in_days is null THEN false
          else c.warmup_started_at + c.warmup_length_in_days * interval '1 day' > now() end) as warmup_is_on


      FROM campaigns c
        INNER JOIN accounts a ON a.id = c.account_id
        LEFT JOIN campaigns_prospects cp ON cp.campaign_id = c.id and cp.team_id = c.team_id

        LEFT JOIN triggers trig ON (trig.campaign_id = c.id AND trig.source = ${SRTriggerSource.CAMPAIGN_SETTINGS.toString})

        LEFT JOIN LATERAL (
         SELECT EXISTS(SELECT id FROM spam_tests spamtest WHERE spamtest.campaign_id = c.id)
        ) AS has_spam_test ON true

        LEFT JOIN LATERAL (
         SELECT COUNT(*) AS count FROM campaign_steps csteps WHERE csteps.campaign_id = c.id
        ) AS total_steps_count ON true

      WHERE $whereClause

      GROUP BY c.id, a.id, total_steps_count.count, has_spam_test.exists
      ;
    """
  }


  // SENDER_ROTATION
  // called from -
  // CampaignService.findAll
  // CampaignController.findAll
  def findAll(
    permittedAccountIds: Seq[Long],
    doNotContactCategoryId: Long,
    loggedinAccountId: Long,
    showCampaignTags: Boolean,
    teamId: Long,
    includeArchived: Boolean,
    listOfPositiveUuid: List[ReplySentimentUuid]

//    searchQuery: Option[ProspectsQuery]

  ): Seq[CampaignBasicInfo] = {

    // 24th August 2021: no need to have localTx anymore because we are also removing the sql" SET LOCAL work_mem = '128MB' clause below
    val clist = DB readOnly { implicit session =>

      // Use a higher work_mem in postgres for this query
      // sql" SET LOCAL work_mem = '128MB'; "


      val archivedCheck = if (!includeArchived) sqls" AND c.status != ${CampaignStatus.ARCHIVED.toString}"  else sqls""
      // val DO_NOT_CONTACT_CATEGORYID = Prospect.getProspectCategoryId(team_id = teamId.get, text_id = ProspectCategory.DO_NOT_CONTACT, account = account)

      // handle sorting (order by) in scala, to speed up query


      findCampaignObject(
        teamId = teamId,
        showCampaignTags = showCampaignTags,
        listOfPositiveUuid = listOfPositiveUuid,
        whereClause = sqls"""
        c.team_id = $teamId
          AND c.account_id IN ($permittedAccountIds)
          $archivedCheck
        """
      )
        .map(fromDbWithCampaignObject)
        .list
        .apply()

    }


    val ownedCampaigns = clist.filter(c => c.owner_id == loggedinAccountId)
    val unownedCampaigns = clist.filter(c => c.owner_id != loggedinAccountId)

    // groupedByStatusForOwned
    val o = ownedCampaigns.sortBy(_.id).reverse.groupBy(_.status)

    // groupedByStatusForUnowned
    val u = unownedCampaigns.sortBy(_.id).reverse.groupBy(_.status)

    o.getOrElse(CampaignStatus.ARCHIVED, Seq[CampaignBasicInfo]()) ++
      o.getOrElse(CampaignStatus.SUSPENDED, Seq[CampaignBasicInfo]()) ++
    o.getOrElse(CampaignStatus.UNDER_REVIEW, Seq[CampaignBasicInfo]()) ++
      o.getOrElse(CampaignStatus.SCHEDULED, Seq[CampaignBasicInfo]()) ++
      o.getOrElse(CampaignStatus.RUNNING, Seq[CampaignBasicInfo]()) ++
      o.getOrElse(CampaignStatus.ON_HOLD, Seq[CampaignBasicInfo]()) ++
      o.getOrElse(CampaignStatus.NOT_STARTED, Seq[CampaignBasicInfo]()) ++
      o.getOrElse(CampaignStatus.STOPPED, Seq[CampaignBasicInfo]()) ++
      u.getOrElse(CampaignStatus.UNDER_REVIEW, Seq[CampaignBasicInfo]()) ++
      u.getOrElse(CampaignStatus.SUSPENDED, Seq[CampaignBasicInfo]()) ++
    u.getOrElse(CampaignStatus.ARCHIVED, Seq[CampaignBasicInfo]()) ++
      u.getOrElse(CampaignStatus.SCHEDULED, Seq[CampaignBasicInfo]()) ++
      u.getOrElse(CampaignStatus.RUNNING, Seq[CampaignBasicInfo]()) ++
      u.getOrElse(CampaignStatus.ON_HOLD, Seq[CampaignBasicInfo]()) ++
      u.getOrElse(CampaignStatus.NOT_STARTED, Seq[CampaignBasicInfo]()) ++
      u.getOrElse(CampaignStatus.STOPPED, Seq[CampaignBasicInfo]())



  }


  def checkIfAccountHasArchivedCampaign(permittedAccountIds: Seq[Long], teamId: Long): Boolean = {
    DB.readOnly { implicit session =>
      sql"""
      select exists (select * from campaigns where account_id IN ($permittedAccountIds) and team_id = $teamId and status = ${CampaignStatus.ARCHIVED.toString})
      """
        .map(rs => rs.boolean("exists"))
        .headOption
        .apply()
        .get


    }
  }

  def checkIfUserHasPermissionForCampaigns(
    campaignIds: Seq[Long],
    permittedAccountIds: Seq[Long],
    teamId: Long
  ): Try[Seq[Long]] = Try {

    if (campaignIds.isEmpty) Seq() else {

      val idsWithPermission = DB.readOnly { implicit session =>

        sql"""
         SELECT id as id
         FROM campaigns
         WHERE id IN ${SQLUtils.generateSQLValuesClause(campaignIds.distinct)}
         AND team_id = $teamId
         AND account_id IN ($permittedAccountIds)
        """
          .map(_.long("id"))
          .list
          .apply()

      }

      idsWithPermission
    }
  }

  def findAllBasic(
    permittedAccountIds_VIEW_CAMPAIGNS: Seq[Long],
    permittedAccountIds_EDIT_CAMPAIGNS: Seq[Long],
    loggedinAccountId: Long,
    is_campaign_inbox: Option[Boolean],
    teamId: Long
  ): Seq[CampaignJustIdName] = {

    val is_campaign_inbox_clause = if(is_campaign_inbox.isDefined && is_campaign_inbox.get){

      sqls"""
            INNER JOIN campaign_email_settings ces ON ( ces.campaign_id = c.id and ces.team_id = c.team_id )
          """

    } else {

      sqls""""""
    }

    val clist = DB readOnly { implicit session =>

      sql"""
    SELECT distinct c.id, c.account_id, c.name, c.status, c.created_at, c.uuid,

    CONCAT(a.first_name, ' ', a.last_name) AS owner_name

    FROM campaigns c
    INNER JOIN accounts a ON a.id = c.account_id
    ${is_campaign_inbox_clause}
    WHERE c.account_id IN ($permittedAccountIds_VIEW_CAMPAIGNS)
      AND c.team_id = $teamId
    ;

    """
        .map(rs => {

          val ownerAccountId = rs.long("account_id")

          CampaignJustIdName(
            id = rs.long("id"),
            account_id = ownerAccountId,
            name = rs.string("name"),
            owner_name = rs.string("owner_name"),
            has_edit_campaign_permission = permittedAccountIds_EDIT_CAMPAIGNS.contains(ownerAccountId),
            status = CampaignStatus.fromKey(rs.string("status")).get,
            created_at = rs.jodaDateTime("created_at"),
            uuid = rs.string("uuid")
          )
        })
        .list
        .apply()
    }


    val ownedCampaigns = clist.filter(c => c.account_id == loggedinAccountId)
    val unownedCampaigns = clist.filter(c => c.account_id != loggedinAccountId)

    // groupedByStatusForOwned
    val o = ownedCampaigns.sortBy(_.id).reverse.groupBy(_.status)

    // groupedByStatusForUnowned
    val u = unownedCampaigns.sortBy(_.id).reverse.groupBy(_.status)

    o.getOrElse(CampaignStatus.ARCHIVED, Seq[CampaignJustIdName]()) ++
      o.getOrElse(CampaignStatus.UNDER_REVIEW, Seq[CampaignJustIdName]()) ++
      o.getOrElse(CampaignStatus.SCHEDULED, Seq[CampaignJustIdName]()) ++
      o.getOrElse(CampaignStatus.RUNNING, Seq[CampaignJustIdName]()) ++
      o.getOrElse(CampaignStatus.ON_HOLD, Seq[CampaignJustIdName]()) ++
      o.getOrElse(CampaignStatus.NOT_STARTED, Seq[CampaignJustIdName]()) ++
      o.getOrElse(CampaignStatus.SUSPENDED, Seq[CampaignJustIdName]()) ++
      o.getOrElse(CampaignStatus.STOPPED, Seq[CampaignJustIdName]()) ++
    u.getOrElse(CampaignStatus.ARCHIVED, Seq[CampaignJustIdName]()) ++
      u.getOrElse(CampaignStatus.UNDER_REVIEW, Seq[CampaignJustIdName]()) ++
      u.getOrElse(CampaignStatus.SCHEDULED, Seq[CampaignJustIdName]()) ++
      u.getOrElse(CampaignStatus.RUNNING, Seq[CampaignJustIdName]()) ++
      u.getOrElse(CampaignStatus.ON_HOLD, Seq[CampaignJustIdName]()) ++
      u.getOrElse(CampaignStatus.NOT_STARTED, Seq[CampaignJustIdName]()) ++
      u.getOrElse(CampaignStatus.SUSPENDED, Seq[CampaignJustIdName]()) ++
      u.getOrElse(CampaignStatus.STOPPED, Seq[CampaignJustIdName]())

  }

  def findAllActiveSenderEmailSettingsPartOfThisCampaign(emailSettingIds: Seq[EmailSettingId], team_id: TeamId, campaignId: CampaignId): Seq[EmailSettingId] = {
    if(emailSettingIds.nonEmpty) {
      DB.readOnly { implicit session =>
        sql"""
      select  ces.sender_email_setting_id
      from campaign_email_settings ces
      join campaigns c on ces.campaign_id = c.id and c.team_id = ces.team_id
      join email_settings es on es.id = ces.sender_email_setting_id and es.team_id = ces.team_id
      where c.status in (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString})
      and ces.sender_email_setting_id in (${emailSettingIds.map(_.emailSettingId)})
      and ces.team_id = ${team_id.id}
      and es.status = ${EmailSettingStatus.Active.toString}
      and ces.campaign_id != ${campaignId.id}
      """
          .map(rs => EmailSettingId(rs.long("sender_email_setting_id")))
          .list
          .apply()
          .distinct


      }
    } else Seq()
  }


  def findActiveCampaignIds(
    by: FindCampaignIdsBy
  ): Try[List[CampaignIdAndTeamId]] = Try {

    by match {

      case FindCampaignIdsBy.Account(accountId) =>

        DB.readOnly { implicit session =>
          sql"""
          select c.id, c.team_id
          from campaigns c
          where
          c.status IN (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString}, ${CampaignStatus.SCHEDULED.toString})

          and c.account_id = $accountId
          """
            .map(rs => CampaignIdAndTeamId(
              campaign_id = rs.long("id"),
              team_id = rs.long("team_id")
            )
            )
            .list
            .apply()


        }

      case FindCampaignIdsBy.Team(teamId) =>

        DB.readOnly { implicit session =>
          sql"""
          select c.id, c.team_id
          from campaigns c
          where
          c.status IN (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString}, ${CampaignStatus.SCHEDULED.toString})

          and c.team_id = $teamId
          """
            .map(rs => CampaignIdAndTeamId(
              campaign_id = rs.long("id"),
              team_id = rs.long("team_id")
            ))
            .list
            .apply()
        }

      case FindCampaignIdsBy.Org(orgId) =>

        DB.readOnly { implicit session =>
          sql"""
          select c.id, c.team_id
          from campaigns c
          join accounts a on a.id = c.account_id
          where c.status IN (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString}, ${CampaignStatus.SCHEDULED.toString})
          and a.org_id = $orgId
          """
            .map(rs => CampaignIdAndTeamId(
              campaign_id = rs.long("id"),
              team_id = rs.long("team_id")
            ))
            .list
            .apply()
        }

    }

  }

//  def findBasicForInternalWork(id: Long): Option[Option[String]] = blocking {
//    DB readOnly { implicit session =>
//
//
//      sql"""
//      SELECT
//        ces.email_setting_id
//
//      FROM campaign_email_settings ces
//      WHERE c.campaign_id = $id
//
//    """
//        .map(rs => rs.string("email_setting_id"))
//        .headOption
//        .apply()
//
//    }
//  }

  // SENDER_ROTATION
  // called from -
  // CampaignService.getCampaignPauseReasonAndCounts
  // CampaignService.updateStatus
  // CampaignService.findCampaignForCampaignUtilsOnly
  // SpamMonitorService.checkIfCampaignAllowedToSendEmail
  def findCampaignForCampaignUtilsOnly(id: Long, teamId: TeamId): Option[Campaign] = {

    blocking {
    DB readOnly { implicit session =>



      CampaignQuery.getQueryFindingCampaignForCampaignUtilsOnly(
        campaign_id = CampaignId(id = id),
        team_id  = teamId
      )
        .map(fromDbCampaign)
        .headOption
        .apply()

    }
  }
  }

  def getCampaignSendStartReport(
                                  teamId: TeamId
                                ): Try[List[CampaignSendStartReport]] = Try{

    DB.readOnly { implicit session =>
      sql"""
           SELECT
             c.id,
             c.name as campaign_name,
             t.id as team_id,
             t.name as team_name,
             cssr.analyzed_at,
             cssr.total_sent_prospects,
             cssr.total_unsent_prospects,
             cssr.total_unsent_prospects_in_dnc,
             cssr.total_unsent_prospects_in_same_tz,
             cssr.total_unsent_prospects_with_invalid_emails,
             cssr.total_unsent_prospects_with_missing_merge_tags,
             cssr.total_unsent_prospects_with_previous_task_not_done,
             cssr.total_unsent_prospects_that_are_likely_valid,
             cssr.campaign_analysis_result
           FROM campaigns c
           INNER JOIN campaign_send_start_reports cssr ON (c.latest_campaign_send_start_reports_id = cssr.id)
           INNER JOIN teams t ON (t.id = c.team_id)
           WHERE c.team_id = ${teamId.id}
           AND c.latest_campaign_send_start_reports_id IS NOT NULL
           AND c.status IN (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString})
           ;
           """
        .map(rs => {
          CampaignSendStartReport(
            campaign_id = rs.long("id"),
            campaign_name = rs.string("campaign_name"),
            team_id = rs.long("team_id"),
            team_name = rs.string("team_name"),
            analyzed_at = rs.jodaDateTime("analyzed_at"),
            total_sent_prospects = rs.int("total_sent_prospects"),
            total_unsent_prospects = rs.int("total_unsent_prospects"),
            total_unsent_prospects_in_dnc = rs.int("total_unsent_prospects_in_dnc"),
            total_unsent_prospects_in_same_tz = rs.int("total_unsent_prospects_in_same_tz"),
            total_unsent_prospects_with_invalid_emails = rs.int("total_unsent_prospects_with_invalid_emails"),
            total_unsent_prospects_with_missing_merge_tags = rs.int("total_unsent_prospects_with_missing_merge_tags"),
            total_unsent_prospects_with_previous_task_not_done = rs.int("total_unsent_prospects_with_previous_task_not_done"),
            total_unsent_prospects_that_are_likely_valid = rs.int("total_unsent_prospects_that_are_likely_valid"),
            campaign_analysis_result = CampaignPauseReason.fromKey(rs.string("campaign_analysis_result")).get
          )
        })
        .list
        .apply()
    }

  }


//  def findBasicDetails(id: Long): Option[CampaignBasicDetails] = DB readOnly { implicit session =>
//
//    sql"""
//      SELECT
//        c.id,
//        c.name,
//        c.account_id,
//        c.team_id,
//        c.head_step_id
//      FROM campaigns c
//      WHERE id = $id
//      ;
//    """
//      .map(fromDbCampaignBasicDetails)
//      .headOption
//      .apply()
//
//  }

  def findBasicDetails(
                        id: Long,
                        teamId: TeamId
                      ): Try[Option[CampaignBasicDetails]] = Try {
    DB readOnly { implicit session =>

      sql"""
        SELECT
          c.id,
          c.name,
          c.account_id,
          c.team_id,
          c.head_step_id
        FROM campaigns c
        WHERE id = $id AND c.team_id = ${teamId.id}
        ;
      """
        .map(fromDbCampaignBasicDetails)
        .headOption
        .apply()

    }
  }
  def findBasicDetails(
    id: Long,
    permittedAccountIds: Seq[Long],
    teamId: Long,
  ): Try[Option[CampaignBasicDetails]] = Try {
    DB readOnly { implicit session =>

      sql"""
        SELECT
          c.id,
          c.name,
          c.account_id,
          c.team_id,
          c.head_step_id
        FROM campaigns c
        WHERE id = $id
        AND
        c.account_id IN ($permittedAccountIds)
        AND
        c.team_id = $teamId
        ;
      """
        .map(fromDbCampaignBasicDetails)
        .headOption
        .apply()

    }
  }


  def findBasicDetailsBatch(
    ids: Seq[Long],
    teamId: Long
  ): List[CampaignJustIdNameV2] = DB readOnly { implicit session =>

    sql"""
      SELECT
        c.id,
        c.name
      FROM campaigns c
      WHERE id IN ${SQLUtils.generateSQLValuesClause(ids)}
      AND team_id = $teamId
      ;
    """
      .map(rs => {
        CampaignJustIdNameV2(
          campaign_id = rs.long("id"),
          campaign_name = rs.string("name")
        )
      })
      .list
      .apply()

  }

  def countProspects(campaignId: Long, team_id: TeamId): Try[Option[Long]] = Try {
    DB readOnly { implicit session =>
      sql"""
        SELECT
          COUNT(*) AS total_prospects
        FROM campaigns_prospects cp
        WHERE cp.campaign_id = $campaignId
        AND cp.team_id = ${team_id.id}
        AND cp.active
      """
        .map(rs => rs.long("total_prospects"))
        .single
        .apply()
    }

  }


  // called from -
  // CampaignService.create
  def create(accountId: Long, teamId: Long, taId: Long,
    name:String,
    campaignSettings: Option[CampaignSettings],
    timezone:String,
    campaign_type: CampaignType,
    campaignUuid: String
     )(implicit session: DBSession): Try[Option[Long]] = Try {

      val defaultStatus = CampaignStatus.NOT_STARTED.toString

      //      val defaultOptoutMsg = "PS: If you don't want to hear from me, just let me know"
      val defaultOptoutMsg = ""

      //      val initialEmailSettingsId = null

      val defaultFromTime = 32400

      val defaultTillTime = 64800

      val defaultEmailPriority = CampaignEmailPriority.EQUAL.toString

      // Sunday is the first day
      val defaultDaysPreference = List(false, true, true, true, true, true, false)

      val defaultDaysPreferenceStr = s"{${defaultDaysPreference.mkString(",")}}"
      val daysPrefString = if (campaignSettings.isDefined) s"{${campaignSettings.get.days_preference.mkString(",")}}" else s"{${defaultDaysPreference.mkString(",")}}"


      val cid = sql"""
          INSERT INTO campaigns
          (
            account_id,
            team_id,
            ta_id,

            status,
            name,

            timezone,
            daily_from_time,
            daily_till_time,
            days_preference,

            email_priority,
            opt_out_msg,
            opt_out_is_text,
            campaign_type,

            mark_completed_after_days,
            max_emails_per_day,
            open_tracking_enabled,
            click_tracking_enabled,
            append_followups,
            enable_email_validation,
            uuid
          )
          VALUES (
            $accountId,
            $teamId,
            $taId,

            $defaultStatus,
            ${name},

            ${timezone},
            ${if (campaignSettings.isDefined) campaignSettings.get.daily_from_time else defaultFromTime},
            ${if (campaignSettings.isDefined) campaignSettings.get.daily_till_time else defaultTillTime},
            $daysPrefString::boolean[7],

            ${if (campaignSettings.isDefined) campaignSettings.get.email_priority.toString else defaultEmailPriority},
            ${if (campaignSettings.isDefined) campaignSettings.get.opt_out_msg else defaultOptoutMsg},
            ${if (campaignSettings.isDefined) campaignSettings.get.opt_out_is_text else true},
            ${campaign_type.toString},


            ${if (campaignSettings.isDefined) campaignSettings.get.mark_completed_after_days else 1},
            ${if (campaignSettings.isDefined) campaignSettings.get.max_emails_per_day else 30},
            ${if (campaignSettings.isDefined) campaignSettings.get.open_tracking_enabled else false},
            ${if (campaignSettings.isDefined) campaignSettings.get.click_tracking_enabled else false},
            ${if (campaignSettings.isDefined) campaignSettings.get.append_followups else true},
            ${if (campaignSettings.isDefined) campaignSettings.get.enable_email_validation else true},
            ${campaignUuid}


          )
          RETURNING id;
      """
        .map(rs => rs.long("id"))
        .single
        .apply()
      cid
  }


  def updateName(
                  id: Long,
                  data: CampaignUpdateNameForm,
                  team_id: TeamId
                ): Try[Option[CampaignIdAndTeamId]] = Try {
    DB autoCommit { implicit session =>

      sql"""
          UPDATE campaigns
          SET
            name = ${data.name}
          WHERE id = $id AND team_id = ${team_id.id}
          RETURNING id, team_id;
      """
        .map(rs => CampaignIdAndTeamId(
          campaign_id = rs.long("id"),
          team_id = rs.long("team_id")
        )
        )
        .single
        .apply()

    }



  }


  def getVoicemails(
                     campaignId: CampaignId,
                     teamId: TeamId
                   ): Try[List[VoiceDropData]] = Try {
    DB readOnly { implicit session =>
      sql"""
             SELECT
              id,
              name,
              url,
              description,
              campaign_id,
              account_id
             FROM
             voicemails
                WHERE
                  team_id = ${teamId.id}
                AND
                  is_active
                AND
                  campaign_id = ${campaignId.id};
          """
        .map(rs => {
          VoiceDropData(
            details = VoiceDropDetails(
              id = VoicemailId(id = rs.long("id")),
              url = rs.string("url")
            ),
            description = rs.stringOpt("description").getOrElse(""),
            name = rs.string("name"),
            account_id = AccountId(id = rs.long("account_id")),
            campaign_id = CampaignId(id = rs.long("campaign_id"))
          )
        })
        .list
        .apply()
        
    }
  }


  def updateVoicemailDetails(
                              campaignId: CampaignId,
                              data: UpdateVoicemailDetailsRequest,
                              teamId: TeamId
                   ): Try[VoicemailId] = Try {
    DB autoCommit { implicit session =>
      sql"""
               UPDATE
               voicemails
               SET name = ${data.name},
                  description = ${data.description}
                  WHERE
                    team_id = ${teamId.id}
                  AND
                    is_active
                  AND 
                    id = ${data.voicemailId.id}
                  AND
                    campaign_id = ${campaignId.id}
                    
                  RETURNING id;
            """
        .map(rs => VoicemailId(id = rs.long("id")))
        .single
        .apply()
        .get

    }
  }

  //VoicemailDeleteData

  def getVoicemailDeleteData(
                     voicemailId: VoicemailId,
                     campaignId: CampaignId,
                     teamId: TeamId
                   ): Try[VoicemailDeleteData] = Try {
    DB readOnly { implicit session =>
      sql"""
              SELECT
               id,
               name,
               url,
               uuid
              FROM
              voicemails
                 WHERE
                   team_id = ${teamId.id}
                 AND 
                   campaign_id = ${campaignId.id}
                 AND
                   id = ${voicemailId.id};
           """
        .map(rs => {
          VoicemailDeleteData(
            id = VoicemailId(id = rs.long("id")),
            name = rs.string("name"),
            url = rs.string("url"),
            uuid = VoicemailUuid(uuid = rs.string("uuid"))
          )
        })
        .single
        .apply()
        .get

    }
  }

  def updateVoicemailActiveStatus(
                              voicemailId: VoicemailId,
                              is_active: Boolean,
                              campaignId: CampaignId,
                              teamId: TeamId
                            ): Try[Int] = Try {
    DB autoCommit { implicit session =>
      sql"""
                UPDATE
                voicemails
                SET
                  is_active = ${is_active},
                  updated_at = ${DateTime.now()}
                   WHERE
                     team_id = ${teamId.id}
                   AND 
                     campaign_id = ${campaignId.id}
                   AND
                     id = ${voicemailId.id};
             """
        .update
        .apply()


    }
  }
  
  def insertVoicemailData(
                         url: String,
                         uuid: String,
                         campaignId: CampaignId,
                         filename: String,
                         fileType: String,
                         description: String,
                         teamId: TeamId,
                         ownerId: AccountId
                         ): Try[String] = Try {
    DB autoCommit { implicit session =>
      sql"""
                 INSERT INTO voicemails 
                 (
                   uuid,
                   team_id,
                   name,
                   type,
                   url,
                   created_at,
                   campaign_id,
                   description,
                   account_id
                )
      
                VALUES
                (
                    ${uuid},
                    ${teamId.id},
                    ${filename},
                    ${fileType},
                    ${url},
                    ${DateTime.now()},
                    ${campaignId.id},
                    ${description},
                    ${ownerId.id}
                    
                )
                RETURNING url;
               """
        .map(rs => rs.string("url"))
        .single
        .apply()
        .get
    }
  }
                         

  def findCampaignsForSchedulingTasks(channelStepType: ChannelStepType, channelSettingUuid: String, Logger: SrLoggerTrait): Try[Seq[CampaignForScheduling]] = blocking {

    Try {
      DB readOnly { implicit session =>

        CampaignDAO.findCampaignsForSchedulingTasksQuery(channelStepType, channelSettingUuid)
          .map(rs => CampaignForSchedulingFromDB.fromDBMultichannel(rs, channelStepType))
          .list
          .apply()
      }
    }

  }

  def findCampaignsForSchedulingIndependentSteps(campaign_id: CampaignId, team_id: TeamId, Logger: SrLoggerTrait): Try[Option[CampaignForScheduling.IndependentStepScheduling]] = blocking {

    Try {
      DB readOnly { implicit session =>


        CampaignDAO.findCampaignsForSchedulingIndependentStep(
          campaign_id = campaign_id,
          team_id = team_id )
          .map(rs => CampaignForSchedulingFromDB.fromDBIndependentStepScheduling(rs))
          .single
          .apply()
      }
    }

  }


  def findCampaignsForSchedulingEA(emailSettingId: Long,
                                   Logger: SrLoggerTrait,
                                   team_id: TeamId
                                  ): Try[Seq[CampaignForScheduling.CampaignForSchedulingEmail]] = blocking {



    /*
      srDBQueryCounterService.logDBQueryCallFrequency(
        dbQueryName =  "findCampaignsForSchedulingEA",
        logger = Logger
      )
    */


        val running: String = CampaignStatus.RUNNING.toString

        val query =
          sqls"""
      SELECT

      c.id,
      c.account_id,
      c.name,
      c.status,
      c.is_archived,
      c.timezone,
      c.campaign_type,
      c.ai_generation_context,
      c.drip_campaign_nodes,
      c.drip_campaign_edges,
      c.daily_from_time,
      c.daily_till_time,
      c.days_preference,
      c.created_at,
      c.head_step_id,
      c.head_node_id_for_drip,
      c.last_scheduled_at,
      c.email_priority,
      c.opt_out_msg,
      c.opt_out_is_text,
      c.team_id,
      c.shared_with_team,
      c.ta_id,
      c.max_emails_per_day,
      c.mark_completed_after_days,
      c.open_tracking_enabled,
      c.warmup_started_at,
      c.warmup_starting_email_count,
      c.warmup_length_in_days,
      c.click_tracking_enabled,
      c.latest_email_scheduled_at,
      c.append_followups,
      c.status_changed_at,
      c.will_delete,
      c.will_delete_at,
      c.enable_email_validation,
      c.ab_testing_enabled,
      c.add_prospect_to_dnc_on_opt_out,
      c.schedule_start_at,
      c.schedule_start_at_tz,
      c.last_analysed_at,
      c.pushed_to_queue_for_analysis_at,
      c.sending_holiday_calendar_id,
      ces.next_to_be_scheduled_at,
      c.last_email_sent_at,
      c.latest_campaign_send_start_reports_id,
      c.pushed_to_queue_for_analysing_start_report_at,
      c.in_queue_for_analyzing_start_report,
      c.last_pushed_for_preemailvalidation_at,
      c.uuid,
      c.sending_mode,
      c.calendar_event_type_id,
      c.calendar_event_type_slug,
      c.calendar_is_individual,
      c.calendar_selected_user_id,
      c.calendar_selected_username_slug,
      c.calendar_smartreach_account_id,
      c.calendar_team_id,
      c.calendar_team_slug,
      ces.id as campaign_email_settings_id,
      ces.sender_email_setting_id,
      ces.receiver_email_setting_id,
      ses.service_provider,
      (CASE WHEN ses.rep_mail_server_id IS NOT NULL THEN ses.rep_mail_server_id ELSE o.rep_mail_server_id END) AS rep_mail_server_id,

      (CASE WHEN o.force_via_gmail_smtp IS TRUE THEN TRUE
        WHEN ses.via_gmail_smtp IS TRUE THEN TRUE
        ELSE FALSE
      END) AS via_gmail_smtp,

      ses.min_delay_seconds,
      ses.max_delay_seconds,

      t.max_emails_per_prospect_per_day,
      t.max_emails_per_prospect_per_week,

      t.max_emails_per_prospect_account_per_day,
      t.max_emails_per_prospect_account_per_week,
      ses.latest_email_scheduled_at,
      ses.email as from_email,
      CONCAT(ses.first_name, ' ', ses.last_name) as from_name,

      res.email as reply_to_email,
      res.sender_name as reply_to_name,

      o.id as org_id,
      csm.prospects_remaining_to_be_scheduled_exists,

      o.enable_email_validation AS enable_email_validation_org_level,

      -- this is used for sender rotation round robin to get the number of emails that can send email
      -- so we are able to divide the prospects equally between the senders
      (select count(*)
      from campaign_email_settings ces1
      INNER JOIN email_settings ses1 ON ses1.id = ces1.sender_email_setting_id and ses1.team_id = ces1.team_id
      INNER JOIN email_settings res1 ON res1.id = ces1.receiver_email_setting_id and res1.team_id = ces1.team_id
      LEFT JOIN domain_health_checks sender_domain_check1 ON sender_domain_check1.domain = ses1.email_address_host
      where ces1.campaign_id = c.id
        AND c.team_id = ces1.team_id
        and ses1.status = ${EmailSettingStatus.Active.toString}
        and res1.status = ${EmailSettingStatus.Active.toString}

        AND (ses1.paused_till IS NULL OR ses1.paused_till < now())
        AND (res1.paused_till IS NULL OR res1.paused_till < now())
        AND COALESCE(sender_domain_check1.is_in_spam_blacklist, FALSE) = FALSE
      ) as count_of_sender_emails


      FROM campaigns c
      INNER JOIN campaign_email_settings ces ON ces.campaign_id = c.id and c.team_id = ces.team_id
      INNER JOIN email_settings ses ON ses.id = ces.sender_email_setting_id and ses.team_id = ces.team_id
      INNER JOIN email_settings res ON res.id = ces.receiver_email_setting_id and res.team_id = ces.team_id
      INNER JOIN teams t ON t.id = c.team_id
      INNER JOIN accounts a ON a.id = c.account_id
      INNER JOIN organizations o ON o.id = a.org_id
      LEFT JOIN campaign_scheduling_metadata csm ON (c.id = csm.campaign_id AND c.team_id = csm.team_id)
      LEFT JOIN domain_health_checks sender_domain_check ON sender_domain_check.domain = ses.email_address_host


      WHERE ces.sender_email_setting_id = $emailSettingId

        AND c.status = $running
        AND c.team_id = ${team_id.id}
        and ses.status = ${EmailSettingStatus.Active.toString}
        and res.status = ${EmailSettingStatus.Active.toString}
        AND c.head_step_id IS NOT NULL
     -- AND ces.receiver_email_setting_id IS NOT NULL
        AND (ses.paused_till IS NULL OR ses.paused_till < now())

        AND COALESCE(sender_domain_check.is_in_spam_blacklist, FALSE) = FALSE

        AND (o.paused_till IS NULL OR o.paused_till < now())
        AND (
            (
              csm.prospects_remaining_to_be_scheduled_exists OR
              csm.prospects_remaining_to_be_scheduled_exists IS NULL
            )
            OR o.is_agency

        )

        -- if res.last_read_for_replies is NULL, we dont want to schedule any campaign from
        -- the email account, until reply-tracking is done atleast once

        AND (
         ces.next_to_be_scheduled_at IS NULL OR
           (
              ces.next_to_be_scheduled_at < now()
              AND
              res.last_read_for_replies > now() - interval '15 minutes'
            )
        )

        -- helps prioritize older campaigns slightly, requested by E2E in May 2020
        ORDER BY c.id
      ;
    """



        DBUtils.readOnlyWithExplain(
          query = query,
          logger = Logger.appendLogRequestId("findCampaignForSchedulingEA db call")
        ) { implicit session =>

          sql"""$query"""
            .map(rs => CampaignForSchedulingFromDB.fromDb(rs, Logger))
            .list
            .apply()
        }

  }

  def saveDripCampaign(
                        dripCampaignForm: DripCampaignForm,
                        campaignId: CampaignId,
                        teamId: TeamId
                      ): Try[Int] = Try {

    val nodes = dripCampaignForm.nodes
    val edges = dripCampaignForm.edges
    val headNodeId = dripCampaignForm.headNodeId

    DB autoCommit { implicit session =>
      sql"""
           UPDATE campaigns
           SET
              campaign_type = ${CampaignType.Drip.toString},
              drip_campaign_nodes = ${nodes.toString}::jsonb,
              drip_campaign_edges = ${edges.toString}::jsonb,
              head_node_id_for_drip = $headNodeId
           WHERE
              id = ${campaignId.id}
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }

  }

  def getDripCampaignData(
                       campaignId: CampaignId,
                       teamId: TeamId
                     ): Try[Option[DripCampaignData]] = Try {
    DB readOnly { implicit session =>
      sql"""
           SELECT
             campaign_type,
             drip_campaign_nodes,
             drip_campaign_edges,
             head_node_id_for_drip
           FROM campaigns
           WHERE id = ${campaignId.id}
           AND team_id = ${teamId.id}
         """
        .map(rs => {

          val campaignType = rs.stringOpt("campaign_type").map(CampaignType.fromString(_).get).getOrElse(CampaignType.MultiChannel)

          if(campaignType == CampaignType.Drip) {
            Some(
              DripCampaignData(
                edges = Json.parse(rs.stringOpt("drip_campaign_edges").getOrElse("[]")).as[List[JsValue]],
                nodes = Json.parse(rs.stringOpt("drip_campaign_nodes").getOrElse("[]")).as[List[JsValue]],
                head_node_id = rs.stringOpt("head_node_id_for_drip").getOrElse("")
              )
            )
          } else None

        })
        .single
        .apply()
        .get
    }
  }

  def updateStatus( //DON'T CALL DIRECTLY, ONLY THROUGH THE SERVICE
    id: Long,
    newStatus: CampaignStatus,
    team_id: TeamId,
    scheduleStartAt: Option[DateTime] = None,
    scheduleStartAtTimeZone: Option[String] = None
  ): Try[Option[CampaignIdAndTeamId]] = Try {

    val scheduleStartAtCheckFail = newStatus match {
      case CampaignStatus.SCHEDULED =>  scheduleStartAt.isEmpty || scheduleStartAtTimeZone.isEmpty
      case CampaignStatus.NOT_STARTED |
           CampaignStatus.RUNNING |
           CampaignStatus.ON_HOLD |
           CampaignStatus.STOPPED |
           CampaignStatus.ARCHIVED |
           CampaignStatus.UNDER_REVIEW |
           CampaignStatus.SUSPENDED => false
    }
    if (scheduleStartAtCheckFail) {
      throw new Exception("schedule_start_at and schedule_start_at_tz are required")
    } else {

      DB autoCommit { implicit session =>

        sql"""
          UPDATE campaigns
          SET
            status = ${newStatus.toString},
            schedule_start_at = $scheduleStartAt,
            schedule_start_at_tz = $scheduleStartAtTimeZone,
            status_changed_at = now()
          WHERE id = $id AND team_id = ${team_id.id}
          RETURNING id, team_id;
      """
          .map(rs => CampaignIdAndTeamId(
            campaign_id = rs.long("id"),
            team_id = rs.long("team_id")
          )
          )
          .single
          .apply()

      }
    }
  }

  def findScheduledCampaignsToStart(): Try[List[CampaignIdAndTeamId]] = Try {
    DB.readOnly { implicit session =>
      sql"""
         select c.id, c.team_id
         from campaigns c
         where c.status = ${CampaignStatus.SCHEDULED.toString}
         and c.schedule_start_at is not null
         and c.schedule_start_at <= now()
       """
        .map(rs => CampaignIdAndTeamId(
          campaign_id = rs.long("id"),
          team_id = rs.long("team_id")
        )
        )
        .list
        .apply()
    }
  }

  def startWarmup(
                   id: Long,
                   data: StartCampaignWarmup,
                   team_id: TeamId
                 ): Try[Option[CampaignIdAndTeamId]] = Try {
    DB autoCommit { implicit session =>

      sql"""
          UPDATE campaigns
          SET
           warmup_started_at = now(),
           warmup_length_in_days = ${data.warmup_length_in_days},
           warmup_starting_email_count = ${data.warmup_starting_email_count}
          WHERE id = $id AND team_id = ${team_id.id}
          RETURNING id, team_id;
      """
        .map(rs => CampaignIdAndTeamId(
          campaign_id = rs.long("id"),
          team_id = rs.long("team_id")
        )
        )
        .single
        .apply()

    }
  }


  def stopWarmup(
                  id: Long,
                  team_id: TeamId
                ): Try[Option[CampaignIdAndTeamId]] = Try {
    DB autoCommit { implicit session =>

      sql"""
          UPDATE campaigns
          SET
           warmup_started_at = null,
           warmup_length_in_days = null,
           warmup_starting_email_count = null
          WHERE id = $id AND team_id = ${team_id.id}
          RETURNING id, team_id;
      """
        .map(rs => CampaignIdAndTeamId(
          campaign_id = rs.long("id"),
          team_id = rs.long("team_id")
        )
        )
        .single
        .apply()
    }
  }

  def getWarmupSettings(campaignId: Long, team_id: TeamId)
  : Try[Option[StartCampaignWarmup]] = Try {
    DB.readOnly { implicit session =>

      sql"""
          SELECT
            warmup_length_in_days,
            warmup_starting_email_count
          FROM campaigns
          WHERE
           id = $campaignId
           AND team_id = ${team_id.id}
           AND warmup_started_at IS NOT NULL
           AND warmup_starting_email_count IS NOT NULL
           AND warmup_length_in_days IS NOT NULL
      """
        .map(rs => {
          StartCampaignWarmup(
            warmup_length_in_days = rs.int("warmup_length_in_days"),
            warmup_starting_email_count = rs.int("warmup_starting_email_count")
          )
        })
        .single
        .apply()

    }

  }

  def updateScheduleSettings(id: Long,
                             data: UpdateCampaignScheduleSettings,
                             team_id: TeamId
                            ): Try[Option[CampaignIdAndTeamId]] = Try {
    DB autoCommit { implicit session =>


      val timezone = DateTimeZone.forID(data.timezone).toString

      val daysPreferenceStr = s"{${data.days_preference.mkString(",")}}"

      sql"""
          UPDATE campaigns
          SET
            timezone = $timezone,
            daily_from_time = ${data.daily_from_time},
            daily_till_time = ${data.daily_till_time},
            sending_holiday_calendar_id = ${data.sending_holiday_calendar_id},
            days_preference = $daysPreferenceStr::boolean[7]
          WHERE id = $id AND team_id = ${team_id.id}
          RETURNING id, team_id;
      """
        .map(rs => CampaignIdAndTeamId(
          campaign_id = rs.long("id"),
          team_id = rs.long("team_id")
        )
        )
        .single
        .apply()
    }

  }

  def updateOtherSettings(
                           id: Long,
                           data: CampaignOtherSettings,
                           team_id: TeamId
                         ): Try[Option[CampaignIdAndTeamId]] = Try {
    DB autoCommit { implicit session =>


      sql"""
          UPDATE campaigns
          SET
            email_priority = ${data.email_priority.toString},
            max_emails_per_day = ${data.max_emails_per_day},

            open_tracking_enabled = ${data.open_tracking_enabled},
            click_tracking_enabled = ${data.click_tracking_enabled},
            enable_email_validation = ${data.enable_email_validation.getOrElse(true)},
            add_prospect_to_dnc_on_opt_out = ${data.add_prospect_to_dnc_on_opt_out},
            send_plain_text_email = ${data.send_plain_text_email},
            sending_mode = ${data.sending_mode.map(_.toString)}
          WHERE id = $id AND team_id = ${team_id.id}
          RETURNING id, team_id;
      """
        .map(rs => CampaignIdAndTeamId(
          campaign_id = rs.long("id"),
          team_id = rs.long("team_id")
        )
        )
        .single
        .apply()
    }

  }

  // updateMaxEmailsPerDay

  def updateMaxEmailsPerDay(campaignId: CampaignId, team_id: TeamId, maxEmailsPerDay: MaxEmailsPerDay): Try[Option[CampaignIdAndTeamId]] = Try {
    DB autoCommit { implicit session =>
      sql"""
          UPDATE campaigns
          SET
            max_emails_per_day = ${maxEmailsPerDay.max_emails_per_day}
          WHERE id = ${campaignId.id}
            AND  team_id = ${team_id.id}
          RETURNING id, team_id;
      """
        .map(rs => CampaignIdAndTeamId(
          campaign_id = rs.long("id"),
          team_id = rs.long("team_id")
        )
        )
        .single
        .apply()
    }

  }

  // SENDER_ROTATION
  // called from -
  // CampaignService.updateStatus
  // CampaignService.updateEmailSettingsV2
  def updatePrevEmailUseStatus(email_ids: Seq[EmailSettingId], teamId: TeamId): Try[List[EmailSettingId]] = Try {
    if(email_ids.nonEmpty){
      DB autoCommit { implicit session =>


        sql"""
          UPDATE email_settings e
                SET used_in_campaign = ( SELECT EXISTS
                (
                SELECT ces.sender_email_setting_id
                  FROM campaign_email_settings ces
                  inner join campaigns c ON c.id = ces.campaign_id and c.team_id = ces.team_id
                  WHERE e.id = ces.sender_email_setting_id AND c.status = ${CampaignStatus.RUNNING.toString}
                  LIMIT 1 ))
                WHERE e.id in (${email_ids.map(_.emailSettingId)})
                 AND e.team_id = ${teamId.id}
               RETURNING id;
      """
          .map(rs => EmailSettingId(
            emailSettingId = rs.long("id")
          )
          )
          .list
          .apply()
      }
    } else List()
  }

  // SENDER_ROTATION
  // called from -
  // CampaignService.updateStatus
  // CampaignService.updateEmailSettingsV2
  def updateEmailUseStatus(email_ids: Seq[EmailSettingId], teamId: TeamId, status: Boolean): Try[List[EmailSettingId]] = Try{
    if(email_ids.nonEmpty){
      DB autoCommit { implicit session =>


        sql"""
          UPDATE email_settings e
          SET used_in_campaign = $status
          WHERE e.id in (${email_ids.map(_.emailSettingId)})
            AND e.team_id = ${teamId.id}
          RETURNING id;
      """
          .map(rs => EmailSettingId(
            emailSettingId = rs.long("id")
          )
          )
          .list
          .apply()
      }
    } else  List()

  }

  def updateOptOutSettings(
    id: Long,
    data: CampaignOptOutSettings,
    orgId: Long,
    team_id: TeamId
  ): Try[Option[CampaignIdAndTeamId]] = Try {


    if (!data.opt_out_is_text && EmailHelper.getUnsubscribedLinkV2(
      campaignId = Some(0),
      optOutMsg = data.opt_out_msg,
      optOutIsText = data.opt_out_is_text,
      optOutLinkUrl = Some("smartreach.io"),

      orgId = orgId
    ).isFailure) {

      throw new Exception("Missing link")
    } else {
      DB autoCommit { implicit session =>

        sql"""
          UPDATE campaigns
          SET
            opt_out_msg = ${data.opt_out_msg},
            opt_out_is_text = ${data.opt_out_is_text}
          WHERE id = $id AND team_id = ${team_id.id}
          RETURNING id, team_id;
      """
          .map(rs => CampaignIdAndTeamId(
            campaign_id = rs.long("id"),
            team_id = rs.long("team_id")
          )
          )
          .single
          .apply()

      }

    }


  }

  /*
  * When we were running the integration tests for drip-campaigns, we found that running the call scheduler prevented
  * the campaign to be picked up by the other channel schedulers.
  * For example linkedin tasks were not getting scheduled after the call scheduler ran.

  * When we investigated we found that call scheduler was setting nextToBeScheduledAt for all the channels
  * instead of updating only for call channel. This PR passed the channelData for the update wherever possible.
  * Earlier the email channel settings were getting updated now also all the email channel settings will get updated.
  * For the passed email settings id in channel data will be ignored.
   */
  def _updateNextToScheduledAt(
                                campaignIdsWithNextToBeScheduledAt: Seq[CampaignDataToAddNextToBeScheduledAt],
                                Logger: SRLogger,
                                team_id: TeamId,
                                is_scheduler_flow: Boolean
                              ): Try[Int] = {

    if (campaignIdsWithNextToBeScheduledAt.isEmpty) Success {
      0
    } else Try {

//      Logger.info(s"Campaign._updateNextToScheduledAt: $campaignIdsWithNextToBeScheduledAt")

      var valueParametersForMultiChannel = List[Any]()

      var valuePlaceholderForMultiChannelList: List[SQLSyntax] = List()

      var valueParametersForEmailChannel = List[Any]()

      var valuePlaceholderForEmailChannelList: List[SQLSyntax] = List()

      campaignIdsWithNextToBeScheduledAt.foreach {
        case data: CampaignDataToAddNextToBeScheduledAtForMultiChannel =>
          valueParametersForMultiChannel = valueParametersForMultiChannel ::: List(
            data.campaignId,
            data.channel_settings_uuid,
            data.channelType.toString,
            data.nextToBeScheduledAt
          )
          valuePlaceholderForMultiChannelList = valuePlaceholderForMultiChannelList ::: List(
            sqls"""
                  (
                    ?,
                    ?,
                    ?,
                    ?::timestamptz
                  )
                """
          )
        case data: CampaignDataToAddNextToBeScheduledAtForEmailChannel =>
          valueParametersForEmailChannel = valueParametersForEmailChannel ::: List(
            data.campaignId,
            data.sender_email_setting_id,
            data.nextToBeScheduledAt
          )
          valuePlaceholderForEmailChannelList = (valuePlaceholderForEmailChannelList ::: List(
            sqls"""
                  (
                    ?,
                    ?,
                    ?::timestamptz
                  )
                """
          ))

      }
      valueParametersForEmailChannel = valueParametersForEmailChannel ::: List(team_id.id)
      valueParametersForMultiChannel = valueParametersForMultiChannel ::: List(team_id.id)

      val add_last_schedule_done_at: SQLSyntax = if (is_scheduler_flow) {
        sqls"last_schedule_done_at = now(),"
      } else {
        sqls""
      }

      DB localTx { implicit session =>


        if (valueParametersForMultiChannel.length > 1) {
          val valuePlaceholderForMultiChannel = valuePlaceholderForMultiChannelList.reduce((vp1, vp2) => sqls"$vp1, $vp2")

          val query = sql"""

                UPDATE campaign_channel_settings SET
                 next_to_be_scheduled_at = temp.next_at

                FROM (VALUES $valuePlaceholderForMultiChannel)
                   AS temp(
                        cid,
                        uuid,
                        type,
                        next_at
                   )


                WHERE campaign_id = temp.cid
                AND channel_type = temp.type
                AND channel_settings_uuid = temp.uuid
                AND team_id = ?;

                """
          // println(query.statement)
          query
            .bind(valueParametersForMultiChannel*)
            .update
            .apply()
        } else 0

        if (valueParametersForEmailChannel.length > 1) {
          val valuePlaceholderForEmailChannel = valuePlaceholderForEmailChannelList.reduce((vp1, vp2) => sqls"$vp1, $vp2")

          sql"""

                UPDATE campaign_email_settings SET
                $add_last_schedule_done_at
               next_to_be_scheduled_at = temp.next_at

               FROM (VALUES $valuePlaceholderForEmailChannel)
                   AS temp(
                        cid,
                        s_e_setting_id,
                        next_at
                   )


               WHERE campaign_id = temp.cid
               AND sender_email_setting_id = temp.s_e_setting_id
               AND team_id = ?;
                """
            .bind(valueParametersForEmailChannel*)
            .update
            .apply()
        } else 0


      }
    }

  }

  def _updateLastScheduled(
    campaignIds: Seq[Long],
    Logger: SRLogger,
    team_id: TeamId
  ): Try[Int] = blocking {

    if (campaignIds.isEmpty) Success(0) else {

      Logger.info(s"Campaign._updateLastScheduled: $campaignIds ")

      /**
       * 4th March 2023: earlier we were doing a batch update here for all the campaignIds at once
       * This was causing repeated sharelock / deadlock errors for large teams that run 50+ campaigns from same email account
       *
       * To avoid the deadlocks, we are doing a .map instead and updating campaigns one-by-one
       */
      val res: Seq[Try[Int]] = campaignIds
        .map(campaignId => {


      DBUtils.autoCommit(Logger) { implicit session =>

        sql"""
          UPDATE campaigns SET
            last_scheduled_at = now()

          WHERE id = ${campaignId}
          AND team_id = ${team_id.id};
      """
          .map(rs => rs)
          .update
          .apply()

        }
        })

      Helpers
        .seqTryToTrySeq(res)
        .map(_.sum)

    }

  }


  def checkIfCampaignInWebhookFilter(campaignId: CampaignId,
                                     teamId: TeamId): Try[Boolean] = Try{
    sql"""
          SELECT EXISTS (
             SELECT
              id
             FROM
              webhooks
             WHERE
              team_id = ${teamId.id}
             AND
              array_position(campaign_ids, ${campaignId.id}) IS NOT NULL
           ) AS exists

       """
      .map(rs => rs.boolean("exists"))
      .single
      .apply()
      .get


  }
  def deleteCampaign(
                      campaignId: Long,
                      team_id: TeamId
                    )(implicit session: DBSession): Try[Int] = Try {
      sql"""
            DELETE
            FROM campaigns
            WHERE id = $campaignId AND team_id = ${team_id.id}
       """
        .update
        .apply()
  }


  def updateAppendFollowUps(id: Long, data: CampaignFollowUpSetting, team_id: TeamId): Try[Option[CampaignIdAndTeamId]] = Try {
    DB autoCommit { implicit session =>


      sql"""
          UPDATE campaigns
          SET
            append_followups = ${data.append_followups}
          WHERE id = $id AND team_id = ${team_id.id}
          RETURNING id, team_id;
      """
        .map(rs => CampaignIdAndTeamId(
          campaign_id = rs.long("id"),
          team_id = rs.long("team_id")
        )
        )
        .single
        .apply()
    }

  }


  def pauseAllCampaignsAndUpdateEmailSettingsForEmailAccountDelete(
                                                                    emailSettingId: Long,
                                                                    team_id: TeamId
                                                                  ): Try[Seq[Long]] = Try {
    DB localTx  { implicit session =>

      val campaign_ids = sql"""
           delete from campaign_email_settings
           where
           (sender_email_setting_id = $emailSettingId OR
           receiver_email_setting_id = $emailSettingId) AND
           team_id = ${team_id.id}
           returning campaign_id;
         """
        .map(rs => rs.long("campaign_id"))
        .list
        .apply()

      if(campaign_ids.nonEmpty) {
        sql"""
         UPDATE campaigns set
            status = ${CampaignStatus.STOPPED.toString},
            status_changed_at = now()
         WHERE campaigns.id in ($campaign_ids) AND
         team_id = ${team_id.id}
         and not exists (
            select *
            from campaign_email_settings ces
            where ces.campaign_id = campaigns.id
            and campaigns.team_id = ces.team_id
            )
         RETURNING campaigns.id;
              ;
        """
          .map(rs => rs.long("id"))
          .list
          .apply()
      } else {
        Seq()
      }
    }


  }


  def pauseAllCampaignsWithId(
                               campaignIds: Seq[CampaignId],
                               team_id: TeamId,
                               status_changed_by: String
                             )(implicit session: DBSession): Try[Seq[CampaignId]] = Try {
      if (campaignIds.isEmpty) {
        Seq()
      } else {
        sql"""
       UPDATE campaigns set
          status = ${CampaignStatus.STOPPED.toString},
          status_changed_at = now(),
          status_changed_by = $status_changed_by
       WHERE  id in (${campaignIds.map(_.id)})
          AND team_id = ${team_id.id}
          AND status != ${CampaignStatus.STOPPED.toString}
       RETURNING id;
      """
          .map(rs => CampaignId(id = rs.long("id")))
          .list
          .apply()
      }
  }

  def pauseAllCampaignsWithIdAndTeamId(
                                        campaignIds: Seq[CampaignId],
                                        team_id: TeamId,
                                        status_changed_by: String
                                      ): Try[Seq[CampaignId]] = {
      DB autoCommit { implicit session =>
        pauseAllCampaignsWithId(
          campaignIds = campaignIds,
          team_id = team_id,
          status_changed_by = status_changed_by
        )
      }

  }
  def update_last_checked_for_completed_campaign_cron_at(
                                                          campaignIds: List[CampaignId]
                                                        ): Try[List[CampaignId]] = Try{
    if(campaignIds.isEmpty) {
      List()
    } else {
      DB autoCommit { implicit session =>
        sql"""
              UPDATE campaigns set
              last_checked_for_completed_campaign_cron_at = now()
              WHERE  id in (${campaignIds.map(_.id)})
              RETURNING id;
          """
          .map(rs => CampaignId(id = rs.long("id")))
          .list
          .apply()
      }
    }

  }


  def getCampaignsToCheckIfItIsInactive(): Try[List[CampaignIdAndTeamId]] =  Try {
    val stop_inactive_campaigns_after_days = SQLSyntax.createUnsafely(SRAppConfig.StopInactiveCampaign.stop_inactive_campaigns_after_days.toString)

    sql"""
             SELECT
          c.id AS cid,
          c.team_id
        FROM
          campaigns c
          JOIN accounts a ON a.id = c.account_id
          JOIN organizations o ON o.id = a.org_id
        WHERE
        c.status in (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString})
        AND o.plan_type = ${PlanType.PAID.toString}
        AND c.status_changed_at < now() - interval '$stop_inactive_campaigns_after_days days'
          AND (
          last_checked_for_completed_campaign_cron_at IS NULL OR
          last_checked_for_completed_campaign_cron_at < now() - interval '24 hours'
          )

        LIMIT 100;
           """
      .map(rs => CampaignIdAndTeamId(
        campaign_id = (rs.long("cid")),
        team_id = (rs.long("team_id"))
      ))
      .list
      .apply()

  }
  def getInactiveCampaignsForStopping(
                                       inactiveCampaignCheckType: InactiveCampaignCheckType,
                                       campaignIdAndTeamId: CampaignIdAndTeamId,
                                       stepTypesInCampaign: Set[CampaignStepType]
                                     ): Try[Option[InactiveCampaignData]] = Try{
    val query = CampaignQuery.getQueryForInactiveCampaignsForStopping(
      inactiveCampaignCheckType = inactiveCampaignCheckType,
      campaignIdAndTeamId = campaignIdAndTeamId,
      stepTypesInCampaign = stepTypesInCampaign
    )
    DB readOnly { implicit session =>
      sql"""$query"""
        .map(rs => InactiveCampaignData(
          campaign_id = CampaignId(rs.long("cid")),
          team_id = TeamId(rs.long("team_id")),
          org_name = rs.string("org_name"),
          campaign_owner_email = rs.string("campaign_owner_email"),
          team_name = rs.string("team_name"),
          campaign_name = rs.string("campaign_name"))
        )
        .single
        .apply()
    }
  }

  def deleteChannelSettingsIdFromCampaign(
                                           channelSettingsUuid: ChannelSettingUuid,
                                           team_id: TeamId
                                         )(implicit session: DBSession): Try[Seq[CampaignId]] = Try {
      sql"""
       DELETE FROM
          campaign_channel_settings
       WHERE
          channel_settings_uuid = ${channelSettingsUuid.uuid}
        AND
          team_id = ${team_id.id}
       RETURNING campaign_id
      """
        .map(rs => CampaignId(
          id = rs.long("campaign_id")
        ))
        .list
        .apply()
  }

  def getCampaignAndProspectByConversationId(team_id: Long, campaign_id: Long,
          conversation_id: Long,
          Logger: SRLogger
  ): Try[Option[ConversationCampaignAndProspectDB]] = Try {

    Logger.info(s"Enter CampaignDAO: getCampaignByConversationId ${conversation_id} tid: ${team_id} cmpgn_id: ${campaign_id}")

    DB.readOnly { implicit  session =>
      val qry = sql"""
         select
         campaigns.name AS campaign_name,
          campaigns.timezone AS campaign_tz,
          es.campaign_id AS campaign_id,
          (
            campaigns.status IN (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString})
            AND campaigns_prospects.active
            --AND (
            --  campaigns_prospects.out_of_office = true
            --  OR
            --  campaigns_prospects.auto_reply = true
            --)
            AND campaigns_prospects.completed = true
            AND campaigns_prospects.bounced = false
            AND campaigns_prospects.invalid_email = false
            AND campaigns_prospects.opted_out = false

          ) AS allow_rescheduling_option,
          campaigns_prospects.completed as prospect_is_completed,
          campaigns_prospects.will_resume_at as will_resume_at,
          campaigns_prospects.will_resume_at_tz as will_resume_at_tz,

          p.id as prospect_id,
          p.uuid as prospect_uuid,
          campaigns.uuid as campaign_uuid,
          es.reply_type as reply_type

        from campaigns

        join emails_scheduled es on (es.campaign_id = campaigns.id and es.email_thread_id = ${conversation_id})
        join campaigns_prospects on (
            campaigns_prospects.campaign_id = es.campaign_id
            AND campaigns_prospects.prospect_id = es.prospect_id
            AND campaigns_prospects.active
            AND campaigns_prospects.team_id = es.team_id
          )

        join prospects p on p.id = es.prospect_id AND p.team_id = $team_id

        where
        campaigns.id = ${campaign_id} and campaigns.team_id = ${team_id}
        limit 1
      """

      val res   = qry.map(rs => {
//        val prospectCategory = rs.string("prospect_category")
//        val campaignProspectAllowReschedulingOption = rs.boolean("allow_rescheduling_option")
//
//        val showReschedulingOption = campaignProspectAllowReschedulingOption && Helpers.checkProspectCategoryAutoReplyAndOoo(prospectCategory)

        ConversationCampaignAndProspectDB(
          prospect_id = rs.long("prospect_id"),
          campaign_id =  rs.long("campaign_id") ,
          campaign_name = rs.string("campaign_name"),
          campaign_tz = rs.string("campaign_tz"),
          //not all threads have reply_type set so by default not_categorized
          reply_type = EmailReplyType.withName(rs.stringOpt("reply_type").getOrElse(EmailReplyType.NOT_CATEGORIZED.toString)),
          //show_rescheduling_option = showReschedulingOption,
          campaignProspectAllowReschedulingOption = rs.boolean("allow_rescheduling_option"),
          prospect_is_completed = rs.boolean("prospect_is_completed"),
          will_resume_at = rs.jodaDateTimeOpt("will_resume_at"),
          will_resume_at_tz = rs.stringOpt("will_resume_at_tz"),
          campaign_uuid = rs.string("campaign_uuid"),
          prospect_uuid = rs.string("prospect_uuid")
        )
      }).single.apply()

      res

    }

  }

  def updateInternalSRUsageData(internalAdoptionReport: InternalAdoptionReport, orgId: OrgId) = Try {
    DB.autoCommit { implicit session =>
      sql"""
            UPDATE organizations
            SET
              internal_sr_adoption_score = ${internalAdoptionReport.internalSRAdoptionScore},
              internal_sr_usage_data = to_jsonb(${
                Json.toJson(internalAdoptionReport.internalSRUsageDataWithScore).toString
              }::jsonb),
              internal_sr_usage_data_updated_at = now()
            WHERE
              id = ${orgId.id};
          """
        .update
        .apply()
    }
  }

  def getInternalSRUsageDataByOrgID(orgId: Long): Try[Option[InternalSRUsageData]] = Try {
    DB.readOnly { implicit session =>
      sql"""
          SELECT
                      o.id,

                      (select exists
                          (
                            select csv.step_id, count(*)
                            from campaign_steps_variants csv
                            join campaigns c on csv.campaign_id = c.id
                            join teams t
                            on t.id = c.team_id
                            where
                            c.status != ${CampaignStatus.NOT_STARTED.toString}
                            and t.org_id = o.id
                            group by csv.step_id,t.id
                            having (
                              count(*) > 1
                            )
                          )
                      ) as ab_testing_enabled,

                      (select exists
                          (
                            select * from campaigns c join teams t
                            on t.id = c.team_id
                            where t.org_id = o.id
                            and c.sending_holiday_calendar_id is not null
                            and c.status != ${CampaignStatus.NOT_STARTED.toString}

                          )
                      ) as sending_holiday_calendar_enabled,

                      (select exists
                          (
                            select * from campaigns c join teams t
                            on t.id = c.team_id
                            where t.org_id = o.id
                            and c.warmup_started_at is not null
                            and c.status != ${CampaignStatus.NOT_STARTED.toString}

                          )
                      ) as warmup_used,

                      (select exists
                          (
                            select * from campaigns c join spam_tests sp on sp.campaign_id = c.id
                            join teams t on t.id = c.team_id  where t.org_id = o.id
                            and c.status != ${CampaignStatus.NOT_STARTED.toString}
                          )
                      ) as ran_spam_test,

                      (select exists
                          (
                             select * from teams t join webhooks w on t.id = w.team_id
                             where t.org_id = o.id
                             and w.active
                             and w.source ='zapier'
                          )
                      ) as integration_via_zapier,

                      (select exists
                                (
                                   select * from teams t  join column_defs cd on t.id = cd.team_id
                                   where t.org_id = o.id
                                   and cd.is_custom

                                )
                      ) as using_custom_column,

                      (select exists
                               (
                                   SELECT DISTINCT ON (cs.crm_type) cs.crm_type FROM workflow_crm_settings cs
                                   JOIN teams t ON cs.team_id = t.id
                                   JOIN workflow_crm_module_settings wcms ON t.id = wcms.team_id AND wcms.active
                                   WHERE t.org_id = o.id

                               )
                       ) as using_workflow_automation,

                       (select exists
                               (
                                   SELECT * FROM campaign_steps_variants cv
                                   JOIN campaigns c ON c.id = cv.campaign_id
                                   JOIN teams t ON c.team_id = t.id
                                   WHERE t.org_id = o.id
                                   AND (cv.subject LIKE '%{{%}}%' OR cv.body LIKE '%{{%}}%')
                                   AND c.status != ${CampaignStatus.NOT_STARTED.toString}
                               )
                             ) as using_merge_tags,

                      (select exists
                             (
                                SELECT
                                  *
                                FROM
                                  teams AS t
                                  JOIN webhooks AS wh ON t.id = wh.team_id
                                WHERE
                                  wh.active = TRUE
                                  AND t.org_id = o.id
                             )
                      ) as using_webhooks,

                      (select exists
                             (
                                SELECT
                                  *
                                FROM
                                  teams AS t
                                WHERE
                                  (
                                    t.uplead_api_key_enc IS NOT NULL
                                    OR t.dropcontact_api_key_enc IS NOT NULL
                                    OR t.anymailfinder_api_key_enc IS NOT NULL
                                    OR t.clearbit_api_key_enc IS NOT NULL
                                    OR t.hunter_api_key_enc IS NOT NULL
                                    OR t.aeroleads_api_key_enc IS NOT NULL
                                  )
                                  AND t.org_id = o.id
                             )
                      ) as using_email_finder,

                      (select exists
                             (
                                SELECT
                                  *
                                FROM
                                  accounts AS acc
                                WHERE
                                  acc.sr_pd_api_key_enc IS NOT NULL
                                  AND acc.active = TRUE
                                  AND acc.org_id = o.id
                             )
                      ) as using_prospect_daddy,

                     (
                        SELECT
                          count(*)
                        FROM
                          campaigns c
                          JOIN teams t ON t.id = c.team_id
                        WHERE
                          t.org_id = o.id
                          AND c.status IN (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString})
                     )
                     as running_campaign_count,

                     (
                        SELECT
                          count(*)
                        FROM
                          campaigns c
                          JOIN teams t ON t.id = c.team_id
                        WHERE
                          t.org_id = o.id
                          AND c.warmup_started_at IS NOT NULL
                          AND c.status IN (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString})
                     )
                     as running_campaign_using_warmup_count,

                     (select exists
                             (
                                SELECT
                                  *
                                FROM
                                  accounts
                                WHERE
                                  org_role != ${OrganizationRole.OWNER.toString}
                                  AND org_id = o.id
                             )
                     ) as invited_team_members,

                     (select exists
                             (
                                SELECT
                                  *
                                FROM
                                  campaigns c
                                  JOIN teams t ON t.id = c.team_id
                                WHERE
                                  t.org_id = o.id
                                  AND EXISTS(SELECT * FROM campaign_steps cs WHERE cs.step_type != ${CampaignStepType.AutoEmailStep.toString} AND cs.campaign_id = c.id)
                                  AND c.status IN (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString})
                             )
                     ) as using_multichannel,

                     (select exists
                             (
                                SELECT
                                  *
                                FROM
                                  team_inbox
                                WHERE
                                  org_id = o.id
                             )
                     ) as using_shared_inbox,

                     (select exists
                             (
                                SELECT
                                  *
                                FROM
                                  email_threads et
                                  JOIN teams t ON t.id = et.team_id
                                  JOIN reply_sentiments_for_teams rst ON rst.uuid = et.reply_sentiment_uuid
                                WHERE
                                  t.org_id = o.id
                                  AND et.reply_sentiment_uuid IS NOT NULL
                                  AND rst.reply_sentiment_name NOT IN ${
                                      SQLUtils.generateSQLValuesClause(
                                        Seq(
                                          ReplySentimentSubCategory.EmailFollowUpAutoReply.key,
                                          ReplySentimentSubCategory.EmailNegativeDoNotContact.key,
                                          ReplySentimentSubCategory.EmailNegativeUnsubscribe.key,
                                          ReplySentimentSubCategory.EmailFollowUpOutOfOffice.key
                                        )
                                      )
                                  }
                             )
                     ) as tagging_reply_sentiments,

                     (select exists
                             (
                                SELECT
                                  *
                                FROM
                                  prospects p
                                  JOIN teams t ON t.id = p.team_id
                                WHERE
                                  t.org_id = o.id
                                  AND p.prospect_category_updated_manually = TRUE
                             )
                     ) as using_prospect_categorization,

                     (select exists
                             (
                                SELECT
                                  *
                                FROM
                                  campaign_steps_variants AS csvr
                                  JOIN campaigns AS c ON csvr.campaign_id = c.id
                                  JOIN teams t ON t.id = c.team_id
                                WHERE
                                  (
                                    subject LIKE '%{{endspin}}%'
                                    OR body LIKE '%{{endspin}}%'
                                  )
                                  AND t.org_id = o.id
                             )
                     ) as using_spintax,

                     (select exists
                             (
                                SELECT
                                    *
                                FROM
                                  accounts
                                WHERE
                                  fp_ref_id IS NOT NULL
                                  AND org_role IN (${OrganizationRole.OWNER.toString}, ${OrganizationRole.AGENCY_ADMIN.toString})
                                  AND org_id = o.id
                             )
                     ) as signed_up_for_referral

                      FROM organizations o
                      WHERE
                      o.id = ${orgId}
                      LIMIT 1
                 ;
           """

        .map(rs => InternalSRUsageData.fromDb(rs))
        .single
        .apply()
    }
  }


  // FIXME: store last_pushed_for_preemailvalidation_at and use that to add some redundancy
  //   currently if the cron runs with a slight delay we can miss some campaigns
  def getCampaignsForPreEmailValidation: Try[List[CampaignForValidation]] = Try {

    val startTime: SQLSyntax = sqls" ((current_timestamp AT TIME ZONE c.timezone )::date + c.daily_from_time * interval '1' second)::timestamptz AT TIME ZONE 'UTC' AT TIME ZONE c.timezone "

    DB.readOnly { implicit session =>
      sql"""

         SELECT
         $startTime as starttime,
         c.id, c.team_id, c.name, c.account_id,o.is_agency,o.id as org_id FROM  campaigns c
         INNER JOIN teams t on t.id = c.team_id
         INNER JOIN organizations o on o.id = t.org_id

         WHERE
            
            -- campaign should be starting between 60 and 300 minutes from now
            $startTime
             BETWEEN now() + interval '60 minutes' AND now() + interval '300 minutes'

            AND

            (
              c.last_pushed_for_preemailvalidation_at is NULL
              OR
              c.last_pushed_for_preemailvalidation_at < now() - interval '2 hours'
              )
            AND o.plan_type != ${PlanType.INACTIVE.toString}

            -- if payment due for a while: no need to schedule
            AND (o.payment_due_campaign_pause_at IS NULL OR o.payment_due_campaign_pause_at > now())


            AND c.status = ${CampaignStatus.RUNNING.toString}

            -- campaigns starting earlier should be pushed to queue first
            ORDER BY starttime ASC
            ;
      """
        .map(rs => {
          CampaignForValidation.fromDb(rs)
        })
        .list
        .apply()
    }
  }

  def updateLastPushedForPreEmailValidationAt(campaignId: Long, teamId:Long): Try[Int] = Try{
    DB.autoCommit { implicit  session =>
      sql"""
        UPDATE campaigns
        SET last_pushed_for_preemailvalidation_at = now()
        WHERE
        id = ${campaignId}
        AND
        team_id = ${teamId};
      """
        .update
        .apply()


    }
  }


  def getSessionCookieAndUserAgentForLinkedinSetting(
                                          campaignId: CampaignId,
                                          teamId: TeamId
                                        ): Try[Option[LinkedinSessionCookieAndUserAgent]] = Try {
    DB readOnly {implicit session =>
      sql"""
           SELECT ls.session_cookie, ls.user_agent
           FROM campaign_channel_settings ccs
           INNER JOIN linkedin_settings ls ON ls.uuid = ccs.channel_settings_uuid
           WHERE ccs.campaign_id = ${campaignId.id}
           AND ccs.team_id = ${teamId.id}
           AND ccs.channel_type = ${ChannelType.LinkedinChannel.toString}
           ;
           """
        .map(rs => {
          for {
            cookie: String <- rs.stringOpt("session_cookie").map(EncryptionHelpers.decryptLinkedinSessionCookie)
            userAgent: String <- rs.stringOpt("user_agent")
          } yield {
            LinkedinSessionCookieAndUserAgent(
              cookie = cookie,
              userAgent = UserAgent(userAgent)
            )
          }
        })
        .single
        .apply()
        .flatten
    }
  }

  def getLinkedinAccountUuidAndStatusForCampaign(
                                                  campaignId: CampaignId,
                                                  teamId: TeamId
                                                ): Try[Option[LinkedinAccountUuidAndStatus]] = Try {
    DB readOnly { implicit session =>
      sql"""
           SELECT
             ls.uuid,
             ls.status
           FROM campaign_channel_settings ccs
           INNER JOIN linkedin_settings ls ON ls.uuid = ccs.channel_settings_uuid
           WHERE ccs.campaign_id = ${campaignId.id}
           AND ccs.team_id = ${teamId.id}
           AND ccs.channel_type = ${ChannelType.LinkedinChannel.toString}
           ;
           """
        .map(rs => {
          LinkedinAccountUuidAndStatus(
            uuid = LinkedinSettingUuid(rs.string("uuid")),
            status = LinkedinAccountStatus.fromString(rs.string("status")).get
          )
        })
        .single
        .apply()
    }
  }


  def updateOrInsertChannelSettingInCampaign(
                                       channel_settings_uuid: ChannelSettingUuid,
                                       campaign_id: CampaignId,
                                       team_id: TeamId,
                                       channelType: ChannelType,
                                       isUpdate:IsUpdate
                                     )(implicit ec:ExecutionContext, logger: SRLogger): Future[Option[CampaignIdAndTeamId]] = Future{

    val query = getUpdateOrInsertQuery(
      team_id = team_id,
      campaign_id = campaign_id,
      channelType = channelType,
      channel_settings_uuid = channel_settings_uuid,
      isUpdate = isUpdate
    )

    DB autoCommit { implicit session =>

      query
        .map(rs => CampaignIdAndTeamId(
          campaign_id = rs.long("campaign_id"),
          team_id = rs.long("team_id")
        )
        )
        .single
        .apply()

    }
  }




  def isCampaignChannelSettingsExists(
                                    team_id: TeamId,
                                    campaign_id: CampaignId,
                                    channel_type: ChannelType,
                                    )(implicit ec: ExecutionContext, logger: SRLogger): Future[IsUpdate] = Future{

    DB.readOnly{ implicit session =>
      sql"""
           SELECT EXISTS (
             SELECT *
              FROM campaign_channel_settings
              WHERE
                team_id = ${team_id.id}
               AND
                campaign_id = ${campaign_id.id}
               AND
                channel_type = ${channel_type.toString}
           )
         """
        .map(rs => IsUpdate(update = rs.boolean("exists")))
        .single
        .apply()
        .get
    }
  }

  def getChannelSettingSenderDetails(
                                    channel_type: ChannelType,
                                    team_id: TeamId,
                                    campaign_id: CampaignId,
                                    channel_setting_uuid: ChannelSettingUuid
                                    )(implicit ec: ExecutionContext, logger: SRLogger): Future[Option[ChannelSettingSenderDetails]] = Future {

    val table_name = channel_type match {
      case ChannelType.LinkedinChannel =>
        sqls"""linkedin_settings"""
      case ChannelType.WhatsappChannel =>
        sqls"""whatsapp_settings"""

      case ChannelType.SmsChannel =>
        sqls"""sms_settings"""

      case ChannelType.CallChannel =>
        sqls"""call_settings"""

      case ChannelType.EmailChannel =>
        sqls"""""" // This will never happen IMPOSSIBLE CASE

      case ChannelType.GeneralChannel | ChannelType.IndependentChannel =>
        sqls"""""" // This will never happen IMPOSSIBLE CASE
    }

    if(table_name.isEmpty){
      None
    }else{
      DB.readOnly { implicit session =>
        sql"""
             SELECT *
               FROM ${table_name}
               WHERE
                  uuid = ${channel_setting_uuid.uuid}
               AND
                  team_id = ${team_id.id}
        """
          .map(res => CampaignForSchedulingFromDB.fromDbGetChannelSenderDetailsForSetting(rs = res, channel_type = channel_type))
          .single
          .apply()
      }
    }

  }

  def isGeneralChannelSettingPresentForCampaign(campaignId: CampaignId, teamId: TeamId): Try[Boolean] = Try {

    DB readOnly {implicit session =>
      sql"""
           SELECT EXISTS (
              SELECT id
              FROM campaign_channel_settings
              WHERE campaign_id = ${campaignId.id}
              AND team_id = ${teamId.id}
              AND channel_type = ${ChannelType.GeneralChannel.toString()}
           )
           ;
           """
        .map(rs => rs.boolean("exists"))
        .single
        .apply()
        .get
    }

  }

  def getCampaignChannelSetupData(
                                 team_id: TeamId,
                                 campaign_id: CampaignId
                                 )(using ec: ExecutionContext, logger: SRLogger): Future[List[ChannelSettingData]] =  Future {

    DB.readOnly{ implicit session =>
      sql"""
           SELECT *
             FROM campaign_channel_settings
             WHERE
                team_id = ${team_id.id}
             AND
                campaign_id = ${campaign_id.id}
      """
        .map(res => CampaignForSchedulingFromDB.campaignChannelSetupFromDB(res))
        .list
        .apply()

    }

  }

  def getCampaignIdForUuid(
                            team_id: TeamId,
                            campaign_uuid: String
                          ): Try[Option[Long]] = Try {
    DB.readOnly { implicit session =>
      sql"""
           SELECT id
             FROM campaigns
             WHERE
                team_id = ${team_id.id}
             AND
                uuid = ${campaign_uuid}
      """
        .map(_.long("id"))
        .single
        .apply()

    }
  }


  def getCampaignIdWhichHaveInConsistentData: Try[List[CampaignIdAndTeamId]] = Try {
    DB.readOnly { implicit session =>
      sql"""
      (
      SELECT cp.prospect_id, cp.campaign_id,cp.team_id
      FROM campaigns_prospects cp
      inner join campaigns c on cp.campaign_id = c.id and cp.team_id = c.team_id and c.status = ${CampaignStatus.RUNNING.toString}
      where  cp.team_id is not null and cp.campaign_id is not null and   cp.current_step_id is null and c.created_at > now() - interval '120 days'
            and exists  (
                select es.id
                from emails_scheduled es
                where es.campaign_id = cp.campaign_id
                and es.prospect_id = cp.prospect_id
                AND es.scheduled_from_campaign
                AND es.sent
                AND es.team_id = cp.team_id
        limit 1
            )
      LIMIT 1
      )

      UNION ALL

      (
      SELECT cp.prospect_id, cp.campaign_id, cp.team_id
      FROM campaigns_prospects cp
      inner join campaigns c on cp.campaign_id = c.id and cp.team_id = c.team_id and c.status = ${CampaignStatus.RUNNING.toString}
      where cp.team_id  is not null and cp.campaign_id is not null and   cp.current_step_id is null and c.created_at > now() - interval' 120 days'
            and exists  (
                select t.task_id
                from tasks t
                where t.campaign_id = cp.campaign_id
                and t.prospect_id = cp.prospect_id
                AND t.team_id = cp.team_id
        limit 1
            )
      LIMIT 1
      )

          ;
         """
        .map(rs => {
          CampaignIdAndTeamId(
            campaign_id = rs.long("campaign_id"),
            team_id = rs.long("team_id")
          )
        })
        .list
        .apply()

    }

  }


  def getDueTasksForStoppedOrArchivedCampaign: Try[Option[DueTaskPendingData]] = Try {
    DB.readOnly { implicit session =>
      sql"""
       SELECT
           t.campaign_id,
           t.team_id,
           STRING_AGG(t.task_id::text, ',  ') AS task_ids_list
       FROM
           tasks t
           JOIN campaigns c ON c.id = t.campaign_id
       WHERE
           c.status in (${CampaignStatus.STOPPED.toString}, ${CampaignStatus.ARCHIVED.toString})
           and t.status = ${TaskStatusType.Due.toKey}
       GROUP BY t.campaign_id, t.team_id
       LIMIT 1
       ;

        """
        .map(rs => {
          DueTaskPendingData(
            taskIds = rs.stringOpt("task_ids_list").getOrElse(""),
            campaignIdAndTeamId = CampaignIdAndTeamId(
              campaign_id = rs.long("campaign_id"),
              team_id = rs.long("team_id")
            )
          )
        }
        )
        .single
        .apply()


    }

  }

  def getCampaignsWhoseProspectHaveOptedOutButEmailsArePresent: Try[Option[ScheduledOptedOutProspectData]] = Try {
    DB.readOnly { implicit session =>
      sql"""

          SELECT
              cp.campaign_id,
              cp.team_id,
              ARRAY_AGG(cp.prospect_id) AS prospect_ids_list
            FROM campaigns_prospects cp
            INNER JOIN teams t ON cp.team_id = t.id
            INNER JOIN campaigns c ON c.id = cp.campaign_id AND c.status = 'running'
            INNER JOIN emails_scheduled  es ON
            cp.campaign_id = es.campaign_id
            AND cp.prospect_id = es.prospect_id
            AND cp.team_id = es.team_id
            WHERE cp.opted_out = true
            AND t.active = true
            AND es.sent = false
            group by cp.campaign_id,cp.team_id
            limit 1
          ;
         """
        .map( rs =>{

          val prospectIdsArray =
            rs.array("prospect_ids_list")
              .getArray().asInstanceOf[Array[Object]]
              .map(_.asInstanceOf[java.lang.Integer].longValue())
              .map(ProspectId(_)).toSeq

          ScheduledOptedOutProspectData(
            prospect_ids_list = prospectIdsArray,
            campaignIdAndTeamId = CampaignIdAndTeamId(
              campaign_id = rs.long("campaign_id"),
              team_id = rs.long("team_id")
            )
          )

        })
        .single
        .apply()
    }

  }


  def getCampaignsWhoseProspectHaveOptedOutButDueTasksArePresent: Try[Option[ScheduledOptedOutProspectData]] = Try {
    DB.readOnly { implicit session =>
      sql"""

        SELECT
         cp.campaign_id,
         cp.team_id,
         ARRAY_AGG(cp.prospect_id) AS prospect_ids_list
         FROM campaigns_prospects cp
         INNER JOIN teams t ON cp.team_id = t.id
         INNER JOIN campaigns c ON c.id = cp.campaign_id and c.status = ${CampaignStatus.RUNNING.toString}
         INNER JOIN tasks  ts on cp.campaign_id = ts.campaign_id and cp.prospect_id = ts.prospect_id and cp.team_id = ts.team_id
         WHERE cp.opted_out = true
         AND t.active = true
         AND ts.status = ${TaskStatusType.Due.toKey}
         group by cp.campaign_id,cp.team_id
         limit 1
         ;
         """
        .map(rs => {
          val prospectIdsArray =
            rs.array("prospect_ids_list")
              .getArray().asInstanceOf[Array[Object]]
              .map(_.asInstanceOf[java.lang.Integer].longValue())
              .map(ProspectId(_)).toSeq

          ScheduledOptedOutProspectData(
            prospect_ids_list = prospectIdsArray,
            campaignIdAndTeamId = CampaignIdAndTeamId(
              campaign_id = rs.long("campaign_id"),
              team_id = rs.long("team_id")
            )
          )
        })
        .single
        .apply()
    }

  }


  def getCampaignWhoseProspectStatusIsCompletedOrPausedButDueTaskArePresents: Try[Option[ScheduledOptedOutProspectData]] = Try {
    DB.readOnly { implicit session =>
      sql"""

        SELECT
         cp.campaign_id,
         cp.team_id,
         ARRAY_AGG(cp.prospect_id) AS prospect_ids_list
         FROM campaigns_prospects cp
         INNER JOIN teams t ON cp.team_id = t.id
         INNER JOIN campaigns c ON c.id = cp.campaign_id and c.status = ${CampaignStatus.RUNNING.toString}
         INNER JOIN tasks  ts on cp.campaign_id = ts.campaign_id and cp.prospect_id = ts.prospect_id and cp.team_id = ts.team_id
         WHERE ( cp.completed OR cp.paused )
         AND t.active = true
         AND ts.status = ${TaskStatusType.Due.toKey}
         group by cp.campaign_id,cp.team_id
         limit 1
         ;
         """
        .map(rs => {
          val prospectIdsArray =
            rs.array("prospect_ids_list")
              .getArray().asInstanceOf[Array[Object]]
              .map(_.asInstanceOf[java.lang.Integer].longValue())
              .map(ProspectId(_)).toSeq

          ScheduledOptedOutProspectData(
            prospect_ids_list = prospectIdsArray,
            campaignIdAndTeamId = CampaignIdAndTeamId(
              campaign_id = rs.long("campaign_id"),
              team_id = rs.long("team_id")
            )
          )
        })
        .single
        .apply()
    }

  }


  def getCampaignWhoseProspectStatusIsCompletedOrPausedButEmailsArePresent: Try[Option[ScheduledOptedOutProspectData]] = Try {
    DB.readOnly { implicit session =>
      sql"""

          SELECT
              cp.campaign_id,
              cp.team_id,
              ARRAY_AGG(cp.prospect_id) AS prospect_ids_list
            FROM campaigns_prospects cp
            INNER JOIN teams t ON cp.team_id = t.id
            INNER JOIN campaigns c ON c.id = cp.campaign_id AND c.status = 'running'
            INNER JOIN emails_scheduled  es ON
            cp.campaign_id = es.campaign_id
            AND cp.prospect_id = es.prospect_id
            AND cp.team_id = es.team_id
            WHERE (cp.completed or cp.paused)
            AND t.active = true
            AND es.sent = false
            group by cp.campaign_id,cp.team_id
            limit 1
          ;
         """
        .map(rs => {

          val prospectIdsArray =
            rs.array("prospect_ids_list")
              .getArray().asInstanceOf[Array[Object]]
              .map(_.asInstanceOf[java.lang.Integer].longValue())
              .map(ProspectId(_)).toSeq

          ScheduledOptedOutProspectData(
            prospect_ids_list = prospectIdsArray,
            campaignIdAndTeamId = CampaignIdAndTeamId(
              campaign_id = rs.long("campaign_id"),
              team_id = rs.long("team_id")
            )
          )

        })
        .single
        .apply()
    }

  }
/*
   27 march 2024 :
   NOTE:: This query was too slow( took more then 8 mins and didn't complete)
   so not using it  now for integrity check
  */

//  def getCampaignIdWhichHaveInConsistentDataV2: Try[Option[CampaignIdAndTeamId]] = Try {
//    DB.readOnly { implicit session =>
//      sql"""
//            WITH scheduled_emails_and_tasks AS (
//        (SELECT
//                es.prospect_id,
//                es.step_id,
//                es.scheduled_at,
//                es.campaign_id
//        FROM
//                emails_scheduled es
//                JOIN (
//                        SELECT
//                                prospect_id,
//                                max(step_id) AS max_step_id
//                FROM
//                        emails_scheduled
//
//                GROUP BY
//                        prospect_id) AS max_dates ON es.prospect_id = max_dates.prospect_id
//                JOIN campaigns c on c.id = es.campaign_id and c.status = ${CampaignStatus.RUNNING.toString} and c.created_at > now() - interval '180 days'
//
//                AND es.step_id = max_dates.max_step_id
//        WHERE
//                step_id IS NOT NULL
//
//                LIMIT 1
//          )
//        UNION ALL
//
//        (SELECT
//                t.prospect_id,
//                t.step_id,
//                t.created_at,
//                t.campaign_id
//        FROM
//                tasks t
//                JOIN (
//                        SELECT
//                                prospect_id,
//                                max(step_id) AS max_step_id
//                        FROM
//                                tasks t
//
//                        GROUP BY
//                                prospect_id) AS max_dates ON t.prospect_id = max_dates.prospect_id
//                        AND t.step_id = max_dates.max_step_id
//                 JOIN campaigns c on c.id = t.campaign_id and c.status = ${CampaignStatus.RUNNING.toString} and c.created_at > now() - interval '180 days'
//
//                WHERE
//                        step_id IS NOT NULL
//                Limit 1
//                )
//      ),
//      latest_step_scheduled AS (
//        SELECT
//                t.prospect_id,
//                t.step_id,
//                t.scheduled_at,
//      t.campaign_id
//        FROM
//                scheduled_emails_and_tasks t
//                JOIN (
//                        SELECT
//                                prospect_id,
//                                max(step_id) AS max_step_id
//                FROM
//                        scheduled_emails_and_tasks
//                GROUP BY
//                        prospect_id) AS max_dates ON t.prospect_id = max_dates.prospect_id
//                AND t.step_id = max_dates.max_step_id)
//      -- Now you can continue your main query using the latest_step_scheduled CTE
//      --SELECT * FROM latest_step_scheduled
//      SELECT
//         cp.prospect_id,
//         cp.campaign_id,
//         cp.team_id
//      FROM
//        campaigns_prospects cp
//        JOIN latest_step_scheduled ls ON (cp.prospect_id = ls.prospect_id and cp.campaign_id = ls.campaign_id)
//      WHERE
//      ((cp.current_step_id IS NULL AND ls.step_id IS NOT NULL)
//        OR (cp.current_step_id != ls.step_id))
//        LIMIT 1
//
//          ;
//         """
//        .map(rs => {
//          CampaignIdAndTeamId(
//            campaign_id = rs.long("campaign_id"),
//            team_id = rs.long("team_id")
//          )
//        })
//        .single
//        .apply()
//
//    }
//
//  }

  /*



   */

  def findSettingsForCampaignInbox(
                    permittedAccountIds_EDIT_CAMPAIGNS: Seq[Long],
                    campaign_id: Option[CampaignId],
                    teamId: TeamId
                  ): Try[Seq[CampaignInboxSettingData]] = Try {

    val campaign_id_clause = if(campaign_id.isDefined){

      sqls"""
             AND c.id = ${campaign_id.get.id}

          """

    } else {

      sqls""""""

    }

//    val email_setting_ids_filter: SQLSyntax = if (email_setting_ids.nonEmpty) {
//
//      sqls"""
//
//          and (
//            ces.receiver_email_setting_id in (${email_setting_ids.map(_.emailSettingId)})
//            OR
//            ces.sender_email_setting_id in (${email_setting_ids.map(_.emailSettingId)})
//           )
//
//         """
//
//    } else {
//
//      sqls""""""
//    }

    if(permittedAccountIds_EDIT_CAMPAIGNS.nonEmpty){

      DB readOnly { implicit session =>

        sql"""
        SELECT
          c.id,
          c.account_id,
          c.status,
          c.created_at,
          ces.sender_email_setting_id,
          ces.receiver_email_setting_id

        FROM campaigns c
        INNER JOIN campaign_email_settings ces ON ( ces.campaign_id = c.id and ces.team_id = c.team_id ${campaign_id_clause})
        WHERE c.account_id IN ($permittedAccountIds_EDIT_CAMPAIGNS)
        AND c.team_id = ${teamId.id}
        ${campaign_id_clause}

      """
          .map(rs => {

            CampaignInboxSettingData(
              campaign_id = CampaignId(id = rs.long("id")),
              campaign_status = CampaignStatus.fromKey(rs.string("status")).get,
              receiver_email_setting_id = EmailSettingId(emailSettingId = rs.long("receiver_email_setting_id")),
              sender_email_setting_id = EmailSettingId(emailSettingId = rs.long("sender_email_setting_id")),
              account_id = AccountId(id = rs.long("account_id")),
              created_at = rs.jodaDateTime("created_at")
            )

          })
          .list
          .apply()
      }

    } else {
      List()
    }

  }

  /*
    Note:
    This function extract the maximum Integer For all the campaign name matching this format:
    "${ownerFirstName}'s Campaign - ${Campaign_Number} %"

    eg:
    Shubham's Campaign - 1
    Shubham's Campaign - 2

    The Out put will be: 2


    Shubham's Campaign - 2
    Shubham's Campaign - 5

    The Output will be: 5

     Shubham's Campaign - 2 Duplicate
     Shubham's Campaign - 4

    The Output will be : 4

   */
  def getNumberOfCampaignWhichMatchesNewCampaign(
                                                  ownerFirstName: String,
                                                  teamId: TeamId
                                                ): Try[Long] = Try {
    val campName = s"${ownerFirstName}'s Campaign"

    DB readOnly { implicit session =>
      val maxCampaignNumber =
        sql"""
        SELECT COALESCE(MAX(
                 CAST(substring(name FROM ${"^" + campName + " - " +"([0-9]+)"}) AS INTEGER)
         ), 0) AS max_campaign_number
         FROM campaigns
         WHERE team_id = ${teamId.id}
         AND name LIKE ${campName + "%"}
      """
          .map(rs => {
            rs.long("max_campaign_number")
          })
          .single // Expect a single row result
          .apply() // Execute the query and get the result

      maxCampaignNumber.getOrElse(0L)  // Return the next campaign number
    }
  }

}
