package api.campaigns

import play.api.libs.json.{<PERSON><PERSON>, O<PERSON><PERSON>}
import scalikejdbc._

case class CampaignStepRelation(
  campaign_id: Long,
  parent_id: Long,
  child_id: Long,
  conditions: Option[String]
)


object CampaignStepRelation {

  implicit val writes: OWrites[CampaignStepRelation] = Json.writes[CampaignStepRelation]

  implicit val session: AutoSession.type = AutoSession

  def fromDb(rs: WrappedResultSet): CampaignStepRelation = CampaignStepRelation(
    campaign_id = rs.int("campaign_id"),
    parent_id = rs.int("parent_id"),
    child_id = rs.int("child_id"),
    conditions = rs.stringOpt("conditions")
  )


  def findAll(campaignId: Int) = DB readOnly { implicit session =>

    sql"SELECT * FROM campaign_steps_relationships WHERE campaign_id = ${campaignId}"
      .map(CampaignStepRelation.fromDb)
      .list
      .apply()

  }

}
