package api.campaigns


import api.{ApiVersion, AppConfig}
import api.accounts.ReplyHandling
import api.accounts.ReplyHandling.ReplyHandling
import api.campaigns.models.{CampaignEmailSettingsId, CampaignName, CampaignProspectCompletedReason, CampaignProspectRevertData, CampaignStepRelatedDataForProspect, CampaignStepType, CampaignType, ChannelStepType, CurrentStepId, CurrentStepStatusForScheduler, CurrentStepStatusForSchedulerData, IgnoreProspectsInOtherCampaigns, NewProspectStatus, SenderRotationStats, StepLabel, WillResumeAtUpdatedBy}
import api.emails.{MessageSentAtByProspect, UpdateEmailStatusOnReplyV3}
import api.prospects.*
import api.prospects.models.{NewProspectAssociateWithCampaignData, PotentialDuplicateProspectStatus, ProspectAccountsId, ProspectCategory, ProspectCategoryId, ProspectDataForChannelScheduling, ProspectId, StepId}
import api.prospects.dao.{ProspectAddEventDAO, ProspectIdAndPotentialDuplicateProspectId}
import api.prospects.dao_service.{ProspectDAOService, ProspectDAOServiceV2, ProspectEmailsDAOService}
import api.search.ProspectQuery
import api.tasks.models.{RevertData, Task, TaskCreatedVia, TaskStatusType}
import api.team_inbox.model.{ReplySentimentTypeData, ReplySentimentUpdatedBy}
import api.team_inbox.service.ReplySentimentUuid
import api.team_inbox.service.UpdateReplySentimentFormForDAO.CampaignProspectThread
import app_services.db_query_counter.SrDBQueryCounterService
import org.joda.time.{DateTime, DateTimeZone}
import scalikejdbc.jodatime.JodaWrappedResultSet.*
import org.postgresql.util.PGobject
import play.api.Logging
import play.api.libs.json.{JsError, JsResult, JsValue, Json, OWrites, Reads, Writes}
import api.campaigns.services.CampaignId
import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.call.models.{CallStatus, ParticipantCallStatus}
import api.emails.models.DeletionReason
import io.smartreach.esp.api.emails.EmailSettingId
import api.phantombuster.LinkedinMessageThreadId
import api.sr_audit_logs.models.EventType
import api.tasks.models.RevertData.TaskRevertData
import scalikejdbc.*
import sr_scheduler.CampaignStatus
import sr_scheduler.models.CampaignForScheduling.CampaignEmailSettingForScheduler
import sr_scheduler.models.{ChannelType, ScheduledProspectsCountForCampaign, ScheduledProspectsCountForCampaignEmail}
import utils.dbutils.{DBUtils, SQLUtils}
import utils.mq.channel_scheduler.{LastSentStepData, SchedulerMapStepIdAndDelay, TableTypeForScheduler}
import utils.{Helpers, SRLogger}
import play.api.libs.json.JodaReads.*
import play.api.libs.json.JodaWrites.*
import utils.Helpers.sanitizeOptionString
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus
import utils.email.EmailReplyStatus
import utils.email.models.DeleteEmailsScheduledType
import api.emails.models.EmailReplyType
import utils.timezones.TimezoneUtils
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.util.{Failure, Success, Try}
import scala.concurrent.blocking

// create

sealed trait DataForCampaignAssignProspects

object DataForCampaignAssignProspects {

  //moved from Prospect.scala exactly as is
  //This form also accepts assign Prospect to campaign v3 data - CampaignController.assignProspectsV3
  case class AssignProspectToCampaignDataV3(
                                             prospect_ids: List[ProspectUuid],
                                             ignore_prospects_in_other_campaigns: Option[String] = Some(IgnoreProspectsInOtherCampaigns.DoNotIgnore.toString)
                                           ) extends DataForCampaignAssignProspects

  object AssignProspectToCampaignDataV3 {
    implicit val reads: Reads[AssignProspectToCampaignDataV3] = Json.reads[AssignProspectToCampaignDataV3]
    implicit val writes: OWrites[AssignProspectToCampaignDataV3] = Json.writes[AssignProspectToCampaignDataV3]
  }

  //This is the same case class as the existing one. Only the case class is brought under a sealed trait.
  case class CampaignProspectFormIds(
                                      prospect_ids: List[Long],
                                      campaign_ids: Option[Seq[Long]] = None, // setting default to ensure old APIs dont break, remove this default later
                                      ignore_prospects_in_other_campaigns: Option[String] = Some(IgnoreProspectsInOtherCampaigns.DoNotIgnore.toString),
                                      force_assign: Option[Boolean] = Some(true) // REMOVE THIS LATER, MIGHT BREAK OLD ZAPIER ETC INTEGRATIONS NOW
                                    ) extends DataForCampaignAssignProspects

  object CampaignProspectFormIds {
    implicit val reads: Reads[CampaignProspectFormIds] = Json.reads[CampaignProspectFormIds]
    implicit val writes: OWrites[CampaignProspectFormIds] = Json.writes[CampaignProspectFormIds]
  }

  case class CampaignProspectFormUuids(
                                        prospect_ids: List[ProspectUuid],
                                      )

  object CampaignProspectFormUuids {
    implicit val reads: Reads[CampaignProspectFormUuids] = Json.reads[CampaignProspectFormUuids]
    implicit val writes: OWrites[CampaignProspectFormUuids] = Json.writes[CampaignProspectFormUuids]
  }

  implicit def reads: Reads[DataForCampaignAssignProspects] = new Reads[DataForCampaignAssignProspects] {

    override def reads(json: JsValue): JsResult[DataForCampaignAssignProspects] = {

      json.validate[CampaignProspectFormIds]

    }
  }

  def validate(json: JsValue, version: String): JsResult[DataForCampaignAssignProspects] = {
    val apiVersionTry = ApiVersion.withName(version)
    apiVersionTry match {

      case Failure(exception) => JsError(exception.getMessage)

      case Success(apiVersion) => apiVersion match {
        case ApiVersion.V1 => json.validate[CampaignProspectFormIds]
        case ApiVersion.V2 => json.validate[CampaignProspectFormIds]
        case ApiVersion.V3 => json.validate[AssignProspectToCampaignDataV3]
      }
    }

  }

  implicit def writes: Writes[DataForCampaignAssignProspects] = new Writes[DataForCampaignAssignProspects] {
    override def writes(c: DataForCampaignAssignProspects) = {
      c match {
        case data: AssignProspectToCampaignDataV3 => Json.toJson(data)
        case data: CampaignProspectFormIds => Json.toJson(data)
      }
    }
  }
}

case class CampaignProspectUnassignRequest(
                                            prospect_ids: List[Long],
                                            campaign_ids: Option[Seq[Long]] = None // setting default to ensure old APIs dont break, remove this default later
                                          )

object CampaignProspectUnassignRequest {
  implicit val reads: Reads[CampaignProspectUnassignRequest] = Json.reads[CampaignProspectUnassignRequest]
}

case class ProspectListForPreview(
                                   prospect_email: Option[String],
                                   prospect_id: Long,
                                   prospect_name: Option[String],
                                   prospect_linkedin: Option[String],
                                   prospect_phone: Option[String],
                                   prospect_company: Option[String],
                                   channel_types: List[ChannelType]
                                 )

object ProspectListForPreview {
  implicit val writes: OWrites[ProspectListForPreview] = Json.writes[ProspectListForPreview]
}

case class EmailsScheduledCount(
                                 scheduled_in_last_24hours: Int,
                                 scheduled_in_last_week: Int
                               )

case class CampaignProspectUpdateScheduleStatus(
                                                 current_step_status_for_scheduler_data: CurrentStepStatusForSchedulerData,
                                                 current_step_type: CampaignStepType,
                                                 current_step_task_id: String,
                                                 step_id: Long,
                                                 campaign_id: Long,
                                                 prospect_id: Long,
                                                 email_message_id: Option[Long],
                                                 current_campaign_email_settings_id: Option[CampaignEmailSettingsId] //this can be null in the db
                                               )

case class UpdateNextScheduleAtData(
                                     campaignId: CampaignId,
                                     prospectId: ProspectId,
                                     teamId: TeamId,
                                     nexToBeCheckedAt: DateTime
                                   )

case class CompletedCampaignProspect(
                                      prospect_id: Long,
                                      campaign_id: Option[Long],
                                      completed_because_email_message_id: Option[Long],
                                      completed_because_secondary_prospect_email_id: Option[Long] = None
                                    )

// assign prospects by email

case class CampaignProspectsUpdateAssignByEmail(
                                                 prospect_emails: List[String],
                                                 ignore_prospects_in_other_campaigns: Option[String] = Some(IgnoreProspectsInOtherCampaigns.DoNotIgnore.toString),
                                                 force_assign: Option[Boolean] = Some(true) // REMOVE THIS LATER, MIGHT BREAK OLD ZAPIER ETC INTEGRATIONS NOW
                                               )

object CampaignProspectsUpdateAssignByEmail {
  implicit val reads: Reads[CampaignProspectsUpdateAssignByEmail] = Json.reads[CampaignProspectsUpdateAssignByEmail]
}

case class CPAssignResult(

                           prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign: List[Long],

                           prospectIdsIgnoredBecauseInOtherCampaigns: List[Long],

                           newlyAssignedProspectIds: List[Long]

                         )

case class CampaignAssignProspectIdData(
                                         prospectIdsAlreadyAssignedToThisCampaign: List[Long],
                                         prospectIdsIgnoredBecauseInOtherCampaigns: List[Long],
                                         prospectsToBeAssignedToNewCampaign: List[OwnedProspectsForAssigning]
                                       )


// MULTICAMPAIGN DONE
case class AssignedCampaignForProspect(
                                        active: Boolean,
                                        campaign_id: Long,
                                        from_email: Option[String],
                                        owner_name: String,
                                        prospect_name: String,
                                        prospect_email: String,
                                        prospect_id: Long,
                                        prospect_category_custom: String,
                                        owner_id: Long,
                                        is_owner: Boolean,
                                        completed: Boolean,
                                        campaign_name: String,
                                        open_tracking_enabled: Boolean,
                                        click_tracking_enabled: Boolean,
                                        campaign_status: CampaignStatus,
                                        campaign_prospect_status: JsValue
                                      )

object AssignedCampaignForProspect {

  import utils.sr_json_utils.CampaignStatusJsonUtil.writesCampaignStatus

  implicit val writes: OWrites[AssignedCampaignForProspect] = Json.writes[AssignedCampaignForProspect]
}


case class CPMarkAsCompleted(

                              /**
                               * 15-feb-2024:
                               * There are multiple issues with how we are using ReplyHandling setting while marking prospects as completed:
                               *
                               *  1. we are pausing all prospects from the same account in a campaign if any one bounces,
                               *     and  reply handing is pause_all_prospect_account_campaigns
                               *
                               * 2. we are also pausing all prospects from the same account in a
                               * campaign if any one gets a out_of_office reply, and  reply handing is pause_all_prospect_account_campaigns
                               * -------------------
                               * -------------------
                               * As a result, from the reply tracker flow (via which majority of these issues originate), we
                               * will pass the email_reply_status, and if apply logic like, if the status is Bounced / Out of office / Auto reply,
                               * we will not mark other prospects in the account as completed, even if that
                               * (PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY) ReplyHandling setting is selected.
                               */
                              email_reply_status: Option[EmailReplyStatus],

                              prospect_id: Long,
                              campaign_id: Option[Long],
                              prospect_account_id: Option[Long],
                              completed_because_email_message_id: Option[Long],
                              completed_because_secondary_prospect_email_id: Option[Long] = None
                            )

case class CPCompleted(
                        campaignId: Long,
                        prospectId: Long,
                        completed: Boolean
                      )


case class SelectedCPForUpdate(
                              campaign_id: CampaignId,
                              campaign_type: CampaignType,
                              team_id: TeamId,
                              prospect_id: ProspectId
                              )


case class CPMarkAsReplied(
                            campaign_id: Option[Long],
                            prospect_id: Option[Long],
                            prospect_account_id: Option[Long]
                          )

case class CPRepliedEvent(
                           campaignId: Long,
                           prospectId: Long,
                           replied_at: DateTime,
                           campaignName: Option[String]
                         )

case class CPCompletedEvent(
                             campaignId: Long,
                             prospectId: Long,
                             completed_at: DateTime,
                             campaignName: Option[String]
                           )

case class CPUnPausedEvent(
                            campaignId: Long,
                            prospectId: Long,
                            unpaused_at: DateTime,
                            campaignName: Option[String]
                          )


// Campaign-Prospect combo
case class CPTuple(
                    prospect_id: Long,
                    campaign_id: Long
                  )

case class CPStatusUpdateEvent(
                                prospect_id: Long,
                                campaign_id: Long,
                                event_at: DateTime
                              )

case class UnsentProspectsCounts(
                                  total_unsent_prospects: Int,
                                  total_unsent_prospects_with_invalid_emails: Int,
                                  total_unsent_prospects_with_missing_merge_tags: Int,
                                  total_unsent_prospects_in_dnc: Int,
                                  total_unsent_prospects_with_previous_task_not_done: Int,
                                  total_unsent_prospects_that_are_likely_valid: Int,
                                  total_unsent_prospects_in_same_tz: Int
                                )

object CampaignProspectDAO {

  def fetchProspectsV3MultichannelQuery(
                                         channelType: ChannelType,
                                         allowedProspectTimezones: List[String],
                                         prospectIdGreaterThan: Option[Long] = None, // for first page of results
                                         campaignId: Long,
                                         teamId: TeamId,
                                         limit: Int,
                                         enable_magic_column: Boolean,
                                         doNotContactCategoryId: ProspectCategoryId,
                                         useModifiedQueryForDripCampaign: Boolean,
                                         channelRelevantStepIdAndDelay: Vector[SchedulerMapStepIdAndDelay],
                                         newProspectsInCampaign: Boolean,
                                         firstStepIsMagicContent: Boolean,
                                         sendOnlyToProspectsWhoWereSentInCurrentCycle: Option[DateTime],
                                         campaign_email_setting: Option[CampaignEmailSettingForScheduler],
                                         orgId: OrgId,
//                                         emailNotCompulsoryEnabled: Boolean
                                       ): SQLSyntax = {


    val (sendOnlyToProspectsWhoWereSentInCurrentCycleCheck, prospectsMetadataLeftJoin): (SQLSyntax, SQLSyntax) = if (sendOnlyToProspectsWhoWereSentInCurrentCycle.isDefined) {

      (
        sqls"""
                    AND (
                      CASE WHEN pm.last_touched_at IS NOT NULL
                      THEN pm.last_touched_at >= ${sendOnlyToProspectsWhoWereSentInCurrentCycle.get}
                      ELSE (
                        prospects.last_contacted_at IS NOT NULL
                        AND
                        prospects.last_contacted_at >= ${sendOnlyToProspectsWhoWereSentInCurrentCycle.get}
                      )
                      END
                    )

                    """,

        sqls"""LEFT JOIN prospects_metadata pm ON pm.prospect_id = prospects.id AND pm.team_id = prospects.team_id"""
      )

    } else (sqls" ", sqls"")

    /**
     * 3 Dec 2024
     *
     * For magic columns, if a magic column is used as a merge tag in the subject or body,
     * but the generation of that magic column is still pending at the time of scheduling the prospect,
     * we want to skip scheduling the prospect for now and try to schedule it after some time.
     *
     * So, when such case occurs we populate the next_check_for_scheduling_at field int he DB.
     * This is done in the `checkPendingMagicColumn` fn.
     *
     * So, if magic columns are enabled for an organisation,
     * this below condition should be part of the fetch prospects for the scheduling query.
     */

    val fetchConditionForDrip: SQLSyntax = if (useModifiedQueryForDripCampaign || enable_magic_column) {
      sqls" AND (cp.next_check_for_scheduling_at IS NULL OR cp.next_check_for_scheduling_at < now()) "
    }
    else {
      sqls""
    }

    /*
       1 May 2025:

        When checking for new prospects in campaigns.
        We were only checking for AND cp.current_step_id IS NULL,
        but now since we have magic content steps, where we won't update the curr_step_id but update the
        current_step_status_for_scheduler to
            - ai_content_queued
            - pending_approval
            - approved

       These three different type of status will come and curr_step_id will be empty.

       so we don't want to pickup the same prospect again and push it to mq as new prospects.
       hence added check that either cp.current_step_status_for_scheduler IS NULL or
       cp.current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Approved.toKey}

       because when its approved we will schedule the actual step


     */

    val stepDelayChecksSQL: SQLSyntax = if (newProspectsInCampaign) {
      if(!firstStepIsMagicContent) {
        sqls" AND cp.current_step_id IS NULL "
      } else {
        sqls""" AND (
                cp.current_step_id IS NULL
                AND (
                cp.current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Approved.toKey} 
                OR cp.current_step_status_for_scheduler IS NULL
                )
                )
              """
      }


    } else {

      val tz = sqls" TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) "

      // we are picking campaign prospects that are part of this given sending email, or if it is not part of any sending email because the sending email was deleted
      //so if we dont have a sending email, we will still pick the prospect and sent to it
      val campaign_email_setting_id_check = if (campaign_email_setting.isDefined && channelType == ChannelType.EmailChannel) {
        sqls"""AND (
                 cp.current_campaign_email_settings_id = ${campaign_email_setting.get.campaign_email_settings_id.id}
                 OR cp.current_campaign_email_settings_id is NULL
                 )

              """
      } else sqls""

      val currentStepIdNotNullCheck = sqls" AND cp.current_step_id IS NOT NULL "

      /*
    if someone is running a 24-hpur long schedule-window (sending emails throughout the day),
    and sending follow-ups on consecutive days,
    maintain atleast a few hours gap between two emails to the prospect
    i.e. if prospect receives first email at 11.30pm first night, they should not receive the second email at 12:30am immediately
    (next day, but only 1 hour gap)
    this happened with sparity (May 2020)

    10 hours delay min between two emails to prospect from same campaign
    */
      val minDelayEnforceForConsecutiveEmailsSQLCheck = if (useModifiedQueryForDripCampaign) {
        sqls" (cp.latest_task_done_or_skipped_at IS NULL OR cp.latest_task_done_or_skipped_at < now() - interval '5 minutes') "
      } else sqls" (cp.latest_task_done_or_skipped_at IS NULL OR cp.latest_task_done_or_skipped_at < now() - interval '10 hours') "

      var delayCheckBuilder = sqls" "

      if (useModifiedQueryForDripCampaign) {
        delayCheckBuilder =
          sqls"""
            cp.current_step_status_for_scheduler IS NULL
            OR
            cp.current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Done.toKey}
            OR
            cp.current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Skipped.toKey}
              OR
            cp.current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Approved.toKey}
            """
      }
      else {
        channelRelevantStepIdAndDelay.reverse.zipWithIndex.foreach { case (data, index) => {

          val currentStepId = data.currentStepId
          val delayTillNextStep = data.delayTillNextStep

          val delayInterval = SQLSyntax.createUnsafely(delayTillNextStep.toString)


          /*
        NOTE:
        interval '$delayInterval' second

        syntax is wrong

        it should be:
        interval 'delayInterval second'

        it only works because: $delayInterval' second : will always take it as seconds, even if we write: $delayInterval' days
         */
          val stepCheck = if (!useModifiedQueryForDripCampaign) {
            sqls""" (
                      cp.current_step_id = $currentStepId
                      )
                      AND (
                        (
                          cp.current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Done.toKey}
                          AND
                          date_trunc('day', cp.latest_task_done_or_skipped_at AT $tz) <  ((now() AT $tz) - interval '$delayInterval' second)
                        )
                        OR
                        (
                          cp.current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Skipped.toKey}
                          AND
                          date_trunc('day', cp.latest_task_done_or_skipped_at AT $tz) <  ((now() AT $tz) - interval '$delayInterval' second)
                        )
                          OR
                          (
                            cp.current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Approved.toKey}
                          )
                        OR
                        cp.current_step_status_for_scheduler IS null
                       )
                      """
          } else {
            sqls""" (
                      cp.current_step_id = $currentStepId
                      )
                      AND (
                          cp.current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Done.toKey}
                        OR
                          cp.current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Skipped.toKey}
                        OR
                          cp.current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Approved.toKey}
                        OR
                        cp.current_step_status_for_scheduler IS null
                       )
                      """
          }

          val totalFollowupSteps = channelRelevantStepIdAndDelay.size
          val isNotLastStepInArray = index < (totalFollowupSteps - 1)
          val operatorClause = if (isNotLastStepInArray) sqls" OR " else sqls""

          delayCheckBuilder = sqls" $delayCheckBuilder $stepCheck $operatorClause "

        }
        }
      }

      sqls" $currentStepIdNotNullCheck AND ($delayCheckBuilder) AND $minDelayEnforceForConsecutiveEmailsSQLCheck $campaign_email_setting_id_check"


    }


    /*
  25-Oct-2023: it was always picking invalid_emails and that was preventing new prospects
  from being picked up, so the campaigns affected were not moving after sometime
  NOTE_INVALID_EMAIL_PROTECTION
   */
    val ignoreInvalidEmailSqls: SQLSyntax = if (channelType == ChannelType.EmailChannel) {

      sqls"AND NOT pe.invalid_email"

    } else {

      sqls""

    }


    // do not need tags in CampaignProspect.fetchProspects result
    // PROSPECTS_EMAILS_TODO_READ_CLEANED
    val prospectSelectSQLClause: SQLSyntax = ProspectQuery._getProspectJustSelectSQLSyntaxV2(
      getProspectTags = false
    )

    val prospectIdWhereClause: SQLSyntax = if (prospectIdGreaterThan.isDefined) sqls" AND prospects.id > ${prospectIdGreaterThan.get} " else sqls""
    val esp_matching_enabled = campaign_email_setting.isDefined && channelType == ChannelType.EmailChannel


    /*
     * for followups - we want to chose the earliest sent follow up first
     * so that the follow ups keep moving along.
     * Then to more recent follow ups
     *
     * The problem that was happening was
     * 2nd follow up was going after days sometimes 28 days after
     * the first email
     *
     * we use the newProspectsInCampaign flag to drive the logic
     * and use last_contacted_at to pick the earliest prospect.
     */
    val campaign_email_setting_order_by = if (esp_matching_enabled) {
      if (newProspectsInCampaign) {
        sqls"""CASE
               WHEN dpd.mx_inbox_provider = ${campaign_email_setting.get.emailServiceProvider.srMxCheckESPType.toString} THEN 0
               ELSE 1
               END,
              """
      } else {
        sqls""" last_contacted_at ASC, CASE
               WHEN dpd.mx_inbox_provider = ${campaign_email_setting.get.emailServiceProvider.srMxCheckESPType.toString} THEN 0
               ELSE 1
               END,
              """
      }
    } else {
      if (newProspectsInCampaign) {
        sqls""
      } else {
        sqls" last_contacted_at ASC, "
      }
    }

    val join_type: SQLSyntax = channelType match {
      case ChannelType.EmailChannel => sqls"INNER"
      case ChannelType.LinkedinChannel |
           ChannelType.WhatsappChannel |
           ChannelType.SmsChannel |
           ChannelType.CallChannel |
           ChannelType.IndependentChannel |
           ChannelType.GeneralChannel => sqls"LEFT"
    }
    val dns_join = if (esp_matching_enabled) {
      sqls"""
          INNER JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
          LEFT JOIN domain_public_dns dpd ON dpd.domain = pe.email_domain
          """
    } else {
      sqls" $join_type JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary) "

    }

    val checkIfDuplicateProspect: SQLSyntax = sqls"""
            AND NOT EXISTS (SELECT
              pdp.id
            FROM
              potential_duplicate_prospect_ids pdp
              INNER JOIN potential_duplicate_prospects_log pdpl ON pdp.potential_duplicate_prospects_log_id = pdpl.id
                AND pdp.team_id = pdpl.team_id
            WHERE
              pdpl.status IN (
                ${PotentialDuplicateProspectStatus.ToBeChecked.toString},
                ${PotentialDuplicateProspectStatus.QueuedToMerge.toString}
                )
              AND pdp.prospect_id = prospects.id
              AND pdp.team_id = ${teamId.id}
              )
          """


    /*

    23-jan-2025:

      we had a case where drip campaign bounced was not moving forward

      we identified that bounced prospects are being marked as complete

      we pulled historic data from the DB - and also analysed the code . hard bounced prospects are being marked as invalid and completed. softbounce prospects are not being marked as completed and not being marked as invalid

      Based on the findings - we will partition the old conditions for prospects in bounced scenario

      a. softbounced path remains untouched
      b. if campaign is drip - and hardbounced prospect - we continue to mark the prospect as invalid -
       but we will not mark the prospect as complete - this will allow us to continue the campaign on other channels
      c. if the campaign is not drip and hardbounced prospect - we will continue with exact older behaviour -
      mark the prospect as complete and email as invalid.

      Given the above the only change is in hardbounced path - in case drip campaign we are not marking the prospect
      as completed allowed them to proceed in the flow for channels other than email and multichannel.

      You can search for this reference tag for the invalid email condition in the SQL file NOTE_INVALID_EMAIL_PROTECTION

      Here are the following test cases

      1. Test Name : "Simple auto-email campaign with bounced reply" : it will mark the prospect as completed upon bounced email
      2. Test Name : "Simple auto-email campaign with actual reply"  : it will also marth the prospect as completed upon receiving email reply
      3. Test Name : "FOLLOWUP TEST - should schedule linkedin step if email bounced" : It will not mark the prospect
      as completed for drip campaign and linkedin step will be created

     */

    val campaigns_prospects_related_checks = if(useModifiedQueryForDripCampaign){

      /*
        24-Jan-2025: In drip campaign bounced is not used at all, so channels other than email can proceed.
        
       */
      sqls"""
          """

    } else{

      sqls"""
            cp.bounced = false AND
          """

    }

    val query =
      sqls"""

          $prospectSelectSQLClause,

          pe.email_checked,
          pe.email_sent_for_validation,
          pe.email_sent_for_validation_at,

          cp.current_step_id,
          cp.last_scheduled,

          cp.latest_task_done_or_skipped_at,
          cp.current_step_status_for_scheduler,

          pa.id as pa_id,
          pa.uuid as pa_uuid,
          pa.owner_id as pa_owner_id,
          pa.team_id as pa_team_id,
          pa.name as pa_name,
          pa.custom_id as pa_custom_id,
          pa.description as pa_description,
          pa.source as pa_source,
          pa.website as pa_website,
          pa.industry as pa_industry,
          pa.linkedin_url as pa_linkedin_url,
          pa.custom_fields as pa_custom_fields,
          pa.created_at as pa_created_at,
          pa.updated_at as pa_updated_at,
          cp.current_step_type,
          CONCAT(a.first_name, ' ', a.last_name) as pa_owner_name


         FROM campaigns_prospects cp
          INNER JOIN prospects ON (prospects.id = cp.prospect_id AND prospects.team_id = cp.team_id)
          INNER JOIN accounts a ON a.id = prospects.account_id
          INNER JOIN prospect_categories_custom pcat on ( pcat.id = prospects.prospect_category_id_custom AND pcat.team_id = prospects.team_id
              AND pcat.id != ${doNotContactCategoryId.id} )
          INNER JOIN campaigns ca ON (ca.id = cp.campaign_id AND ca.team_id = cp.team_id)
          $dns_join
          LEFT JOIN prospect_accounts pa ON (pa.id = prospects.prospect_account_id AND pa.team_id = prospects.team_id)
          LEFT JOIN prospect_lists ON (prospect_lists.id = prospects.list_id AND prospect_lists.team_id = prospects.team_id)
          $prospectsMetadataLeftJoin

          ${ProspectQuery.getMagicColsLeftLateralJoin}

           LEFT JOIN LATERAL (
                SELECT completed_at
                FROM call_conference_logs
                WHERE primary_prospect_id = cp.prospect_id
                  AND team_id = cp.team_id
                ORDER BY completed_at DESC
                LIMIT 1
            ) AS ccl ON TRUE

         WHERE cp.campaign_id = $campaignId
           AND cp.team_id = ${teamId.id}

          $prospectIdWhereClause


          AND cp.active = true
          AND cp.to_check = false
          $ignoreInvalidEmailSqls

          AND (
           cp.unpaused_by_admin = true

           OR (


            -- email-specific checks to stop campaign
           ${campaigns_prospects_related_checks}
            cp.replied = false
            AND cp.opted_out = false
            AND cp.out_of_office = false
            AND cp.auto_reply = false
            AND cp.completed = false
            AND cp.paused = false
            AND cp.will_resume_at is null

            -- TODO: linkedin-specific checks to stop campaign - ideally any such case should result in cp.completed to be true

          )
        )


          AND (

            CASE
             WHEN prospects.timezone is not null and prospects.timezone != '' THEN prospects.timezone IN ($allowedProspectTimezones)
             ELSE ca.timezone IN ($allowedProspectTimezones)
            END
          )

          AND prospects.prospect_category_id_custom != ${doNotContactCategoryId.id}

          $fetchConditionForDrip

          $sendOnlyToProspectsWhoWereSentInCurrentCycleCheck


          $stepDelayChecksSQL

          $checkIfDuplicateProspect

          ORDER BY $campaign_email_setting_order_by prospects.id ASC
          LIMIT $limit


      """

    query

  }

  //SENDER_ROTATION
  // called from -
  // EmailChannelScheduler.getScheduledProspectsCountForCampaign
  def getScheduledProspectCountForCampaignQuery(
                                                 emailSettingIds: Seq[EmailSettingId],
                                                 teamId: Long,
                                                 campaignIdAndTimezone: Seq[(Long, String)],
                                                 countOnlySentProspects: Boolean
                                               ) = {


    /*
    We have 2 cases
       1. when only sent - then scheduled counts will be 0 . so we optimise that away and replace with 0
       2 when sent and scheduled counts are both needed - we want to split the query using
         UNION ALL - so that each query hits our index in the DB

         "sr_emails_scheduled_inboxid_cid_schdat" btree (inbox_email_setting_id, campaign_id, scheduled_at) WHERE scheduled_from_campaign
         "sr_emails_scheduled_inboxid_cid_sentat" btree (inbox_email_setting_id, campaign_id, sent_at) WHERE scheduled_from_campaign

          Each arm of the union all will hit one index - so we make it easy for the optimiser to find the indexes

          In the sent part the scheduled counts will be non-zero. Right now i have passed as 0 for those
          counts in the sent arm. Need to check this with Prateek.

          In the not sent part the sent counts will be 0.

  neil=# select 7 as id, 1 as f1 , 2 as f2, 0 as f3, 0 as f4
  neil-# union all
  neil-# select 7 as id, 0 as f1 , 0 as f2, 3 as f3, 5 as f4
  neil-# ;
  id | f1 | f2 | f3 | f4
  ----+----+----+----+----
    7 |  1 |  2 |  0 |  0
    7 |  0 |  0 |  3 |  5
  (2 rows)


  neil=# select id, sum(f1), sum(f2), sum(f3), sum(f3)
  neil-# from (
  neil(#    select 7 as id, 1 as f1 , 2 as f2, 0 as f3, 0 as f4
  neil(#    union all
  neil(#    select 7 as id, 0 as f1 , 0 as f2, 3 as f3, 5 as f4
  neil(# ) part1
  neil-# group by id;
   id | sum | sum | sum | sum
  ----+-----+-----+-----+-----
    7 |   1 |   2 |   3 |   3
  (1 row)
   */

    val campaignIdAndTzSentWhereClause: SQLSyntax = campaignIdAndTimezone
      .map { case (cid, campaignTimezone) =>

        // start of day in campaign timezone
        val startOfDay = TimezoneUtils.getStartOfDayWithTimezone(timezone = campaignTimezone)

        sqls""" (
                    es.campaign_id = $cid
                    AND
                    es.sent_at >= $startOfDay
                  )
              """
      }
      .reduce((c1, c2) => sqls" $c1 OR $c2 ")

    val sentCondition: SQLSyntax =
      sqls"""
          ( es.sent_at >= now() - interval '24 hours' )
          """


    if (countOnlySentProspects) {

      sql"""
            select
            es.campaign_id,
            count(*) FILTER (WHERE es.is_opening_step ) as newCount,
            count(*) FILTER (WHERE NOT es.is_opening_step ) AS followupCount,
            0 as newCountNotSent,
            0 as followupCountNotSent

            from emails_scheduled es


            where es.inbox_email_setting_id in (${emailSettingIds.map(_.emailSettingId)})

            AND es.scheduled_from_campaign

            AND ($campaignIdAndTzSentWhereClause)

            -- this clause is only to optimize the search space
            AND $sentCondition

            AND team_id = $teamId
            AND es.sent

            GROUP BY es.campaign_id
            ;
            """
    }
    else {


      val campaignIdAndTzScheduledWhereClause: SQLSyntax = campaignIdAndTimezone
        .map { case (cid, campaignTimezone) =>

          // start of day in campaign timezone
          val startOfDay = TimezoneUtils.getStartOfDayWithTimezone(timezone = campaignTimezone)

          sqls""" (
                      es.campaign_id = $cid
                      AND (
                        es.scheduled_at >= $startOfDay
                      )
                    )
                """

        }
        .reduce((c1, c2) => sqls" $c1 OR $c2 ")


      val scheduledCondition: SQLSyntax =
        sqls"""
              ( es.scheduled_at >= now() - interval '24 hours' )
              """

      sql"""
          select
          part1.campaign_id,
          sum(newCount) as newCount,
          sum(followupCount) as followupCount,
          sum(newCountNotSent) as newCountNotSent,
          sum(followupCountNotSent) as followupCountNotSent

          from (
            select
            es.campaign_id,
            count(*) FILTER (WHERE es.is_opening_step ) as newCount,
            count(*) FILTER (WHERE NOT es.is_opening_step ) AS followupCount,
            0 as newCountNotSent,
            0 as followupCountNotSent

            from emails_scheduled es


            where es.inbox_email_setting_id in (${emailSettingIds.map(_.emailSettingId)})

            AND es.scheduled_from_campaign

            AND ($campaignIdAndTzSentWhereClause)

            -- this clause is only to optimize the search space
            AND $sentCondition

            AND team_id = $teamId
            AND es.sent

            GROUP BY es.campaign_id

          UNION ALL


            select
            es.campaign_id,
            0 as newCount,
            0 AS followupCount,
            count(*) FILTER (WHERE es.is_opening_step  ) as newCountNotSent,
            count(*) FILTER (WHERE NOT es.is_opening_step  ) as followupCountNotSent

            from emails_scheduled es


            where es.inbox_email_setting_id in (${emailSettingIds.map(_.emailSettingId)})

            AND es.scheduled_from_campaign

            AND ($campaignIdAndTzScheduledWhereClause)

            -- this clause is only to optimize the search space
            AND $scheduledCondition

            AND team_id = $teamId
            AND NOT es.sent

            GROUP BY es.campaign_id
          ) part1
            group by part1.campaign_id
            ;
            """


    }

  }

  //SENDER_ROTATION
  // called from -
  // EmailChannelScheduler.getSentOrScheduledProspectsCountForChannel
  // getSentOrScheduledProspectsCountForEmail
  def getSentOrScheduledProspectCountQuery(
                                            emailSettingIds: Seq[EmailSettingId],
                                            accountTimezone: String,
                                            countOnlySentEmails: Boolean,
                                            teamId: TeamId
                                          ) = {

    // start of day in account timezone
    val startOfDay = TimezoneUtils.getStartOfDayWithTimezone(timezone = accountTimezone)

    val sent_sql =
      sqls"""

            AND es.sent

            AND es.sent_at >= $startOfDay
            """

    /*
    val sentOrScheduledCondition = if (countOnlySentEmails) {

      sqls"""
            -- this clause is only to optimize the search space
            AND es.sent_at >= now() - interval '24 hours'
            AND es.sent_at AT TIME ZONE $accountTimezone >= date_trunc('day', now() AT TIME ZONE $accountTimezone)
            """

    }
    else {

      sqls"""
            -- this clause is only to optimize the search space
            AND (
               es.scheduled_at >= now() - interval '24 hours'
               OR
               es.sent_at >= now() - interval '24 hours'
            )
            AND (
              es.scheduled_at AT TIME ZONE $accountTimezone >= date_trunc('day', now() AT TIME ZONE $accountTimezone)
              OR
              es.sent_at AT TIME ZONE $accountTimezone >= date_trunc('day', now() AT TIME ZONE $accountTimezone)
            )
            """

    }

    sql"""
        select
        count(*) as count, es.sender_email_settings_id as eset_id

        from emails_scheduled es
        where es.sender_email_settings_id in (${emailSettingIds.map(_.emailSettingId)})

        AND es.scheduled_from_campaign

        $sentOrScheduledCondition
        group by es.sender_email_settings_id
        ;
      """

    */

    if (countOnlySentEmails) {
      sql"""
          select
          count(*) as count, es.inbox_email_setting_id as eset_id

          from emails_scheduled es
          where es.inbox_email_setting_id in (${emailSettingIds.map(_.emailSettingId)})
          AND es.team_id = ${teamId.id}
          AND es.scheduled_from_campaign

          $sent_sql
          group by es.inbox_email_setting_id
          ;
        """

    } else {

      /*
      val  scheduled_sql = sqls"""
            -- this clause is only to optimize the search space
            AND es.scheduled_at >= now() - interval '24 hours'

            AND not es.sent

            AND es.scheduled_at AT TIME ZONE $accountTimezone >= date_trunc('day', now() AT TIME ZONE $accountTimezone)
            """

      sql"""
        select part1.eset_id, sum(count) as count from
          (
          select
          count(*) as count, es.sender_email_settings_id as eset_id

          from emails_scheduled es
          where es.sender_email_settings_id in (${emailSettingIds.map(_.emailSettingId)})

          AND es.scheduled_from_campaign

          $sent_sql
          group by es.sender_email_settings_id

        UNION ALL

          select
          count(*) as count, es.sender_email_settings_id as eset_id

          from emails_scheduled es
          where es.sender_email_settings_id in (${emailSettingIds.map(_.emailSettingId)})

          AND es.scheduled_from_campaign

          $scheduled_sql
          group by es.sender_email_settings_id
          ) part1 group by part1.eset_id
          ;
        """
      */

      sql"""

                select
                count(*) as count, es.inbox_email_setting_id as eset_id

                from emails_scheduled es
                where es.inbox_email_setting_id in (${emailSettingIds.map(_.emailSettingId)})
                AND es.team_id = ${teamId.id}
                AND es.scheduled_from_campaign


                 -- AND not es.sent -- we are counting both sent + scheduled-and-not-sent here, so need for this check

                 AND es.scheduled_at >= $startOfDay
                group by es.inbox_email_setting_id
         """

    }

  }

  def getAddReplySentimentQuery(
                                 reply_sentiment_uuid: ReplySentimentUuid,
                                 whereClause: SQLSyntax,
                                 teamId: TeamId,
                                 reply_sentiment_updated_by: ReplySentimentUpdatedBy
                               ): SQL[Nothing, NoExtractor] = {
    sql"""
     UPDATE campaigns_prospects cp
       SET
         reply_sentiment_uuid = ${reply_sentiment_uuid.uuid},
           reply_sentiment_updated_by = ${reply_sentiment_updated_by.key}
          ${whereClause} AND cp.team_id = ${teamId.id}
       RETURNING cp.prospect_id;
    """
  }

}


class CampaignProspectDAO(
                           prospectDAOService: ProspectDAOService,
                           prospectQuery: ProspectQuery,
                           srDBQueryCounterService: SrDBQueryCounterService,
                           prospectAddEventDAO: ProspectAddEventDAO,
                           prospectEmailsDAOService: ProspectEmailsDAOService,
                           prospectDAOServiceV2: ProspectDAOServiceV2,
                           srRollingUpdateCoreService: SrRollingUpdateCoreService
                         ) extends Logging {


  implicit val session: AutoSession.type = AutoSession


  def updateCampaignEmailSettingId(
                                    campaignId: CampaignId,
                                    prospectId: ProspectId,
                                    teamId: TeamId,
                                    currentCampaignEmailSettingsId: Long
                                  ): Try[Int] = Try {

    DB autoCommit { implicit session =>

      sql"""
              UPDATE campaigns_prospects SET
              current_campaign_email_settings_id = ${currentCampaignEmailSettingsId}
              WHERE
                team_id = ${teamId.id}
                AND campaign_id = ${campaignId.id}
                AND prospect_id = ${prospectId.id}
               
          """
        .update
        .apply()

    }
  }
  // INTERNAL USE: no Try
  def _hasBeenSent(campaignId: Long, prospect_id: Long): Try[Int] = Try {

    sql"""
          UPDATE campaigns_prospects SET
          sent = TRUE,
          sent_at = COALESCE(sent_at, now()),
          last_contacted_at = now()
          WHERE campaign_id = $campaignId
            AND prospect_id = $prospect_id
            -- AND sent = FALSE
      """
      .update
      .apply()

  }


  // INTERNAL USE: no Try wrapper here
  def _hasOpened(campaignId: Long, prospect_id: Long, opened_at: DateTime): Int = DB autoCommit { implicit session =>

    sql"""
          UPDATE campaigns_prospects SET
          opened = TRUE,
          opened_at = COALESCE(opened_at, $opened_at),
          last_opened_at = $opened_at,
          next_check_for_scheduling_at = now(),
          total_opens = total_opens + 1
          WHERE campaign_id = $campaignId
            AND prospect_id = $prospect_id
            AND NOT bounced
            AND NOT email_soft_bounced
            -- AND opened = FALSE
      """
      .update
      .apply()

  }

  def _hasClicked(campaignId: Long, prospect_id: Long, clicked_at: DateTime): Int = DB.autoCommit { implicit session =>

    // todo: if last_opened_at > 30 mins ago, update it again now
    sql"""
          UPDATE campaigns_prospects SET
          opened = TRUE,
          opened_at = COALESCE(opened_at, $clicked_at),
          last_opened_at = $clicked_at,
          total_opens = (CASE WHEN total_opens = 0 THEN 1 ELSE total_opens END),
          clicked = TRUE,
          clicked_at = COALESCE(clicked_at, $clicked_at),
          last_clicked_at = $clicked_at,
          next_check_for_scheduling_at = now(),
          total_clicks = total_clicks + 1
          WHERE campaign_id = $campaignId
            AND prospect_id = $prospect_id
            AND NOT bounced
            AND NOT email_soft_bounced
            -- AND clicked = FALSE
      """
      .update
      .apply()

  }


  // INTERNAL USE: No Try wrapper here
  def _hasOptedOut(campaignId: CampaignId, prospect_id: ProspectId): Int = {
    DB autoCommit { implicit session =>

      sql"""
          UPDATE campaigns_prospects SET
          opted_out = TRUE,
          opted_out_at = now(),
          unpaused_by_admin = false
          WHERE campaign_id = ${campaignId.id}
            AND prospect_id = ${prospect_id.id}
            AND opted_out = FALSE
      """
        .update.apply()

    }
  }

  def getApproxCountOfProspectsThatCanGetFirstEmailToday(
                                                          campaignId: CampaignId,
                                                          teamId: TeamId,
                                                          least_amount_of_prospects_needed_to_meet_limit: Int,
                                                          start_of_day: DateTime
                                                        ): Try[Int] = Try {
    DB readOnly { implicit session =>
      sql"""
           select count(*) from (Select prospect_id as count from campaigns_prospects cp
           inner join campaigns c on c.id = cp.campaign_id and c.team_id = cp.team_id
           where cp.campaign_id = ${campaignId.id}
           and cp.team_id = ${teamId.id}
           and (
            cp.current_campaign_email_settings_id is null OR
            (
            cp.current_campaign_email_settings_id is not null and
            not cp.sent
            ) OR
            (
            cp.current_step_id = c.head_step_id
            AND cp.last_scheduled > $start_of_day -- need to get the start time of the campaign for the day
            )
           )
           and cp.active
           and not cp.invalid_email
           LIMIT $least_amount_of_prospects_needed_to_meet_limit
          )as prospect_id ;
         """
        .map(rs => rs.int("count"))
        .single
        .apply()
        .getOrElse(0)
    }
  }

  /**
   * We need an approx count for prospects that can get followup today
   * I have copied the conditions we have for fetchProspectsV3Multichannel
   * we are excluding the first step by using cp.sent and the last step by not cp.completed
   * NOTE: this is not taking in account the email delay in days, so we can improve the query.
   */
  def getApproxCountOfProspectsThatCanGetFollowupToday(
                                                        campaignId: CampaignId,
                                                        teamId: TeamId,
                                                        least_amount_of_prospects_needed_to_meet_limit: Int
                                                      ): Try[Int] = Try {
    DB readOnly { implicit session =>
      sql"""
           select count(*) from (Select prospect_id as count from campaigns_prospects cp
           inner join campaigns c on c.id = cp.campaign_id and c.team_id = cp.team_id
           inner join prospects p on p.id = cp.prospect_id and p.team_id = cp.team_id
           where cp.campaign_id = ${campaignId.id}
           and cp.team_id = ${teamId.id}
            AND (
              cp.current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Done.toKey}
                OR
              cp.current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Skipped.toKey}
             )
             and (cp.last_scheduled < now() - interval '10 hours')
           and cp.active
           and cp.sent
           and not cp.completed
           and not cp.invalid_email
           LIMIT $least_amount_of_prospects_needed_to_meet_limit
          )as prospect_id ;
         """
        .map(rs => rs.int("count"))
        .single
        .apply()
        .getOrElse(0)
    }
  }

  def getUnsentProspectsCounts(
                                campaignId: CampaignId,
                                teamId: TeamId,
                                campaignTimezone: String,
                                DONOTCONTACT_CATEGORY_ID: ProspectCategoryId

                              ): Option[UnsentProspectsCounts] = {

    DB.readOnly { implicit session =>
      sql"""
           SELECT

             COUNT(*) as total_unsent_prospects,

             COUNT(*) FILTER (WHERE cp.invalid_email) as total_unsent_prospects_with_invalid_emails,

             COUNT(*) FILTER (WHERE cp.to_check) as total_unsent_prospects_with_missing_merge_tags,

             COUNT(*) FILTER (WHERE p.prospect_category_id_custom = ${DONOTCONTACT_CATEGORY_ID.id}) as total_unsent_prospects_in_dnc,

             COUNT(*) FILTER (WHERE cp.current_step_id IS NOT NULL AND cp.current_step_status_for_scheduler != ${CurrentStepStatusForScheduler.Done.toKey}) as total_unsent_prospects_with_previous_task_not_done,

             COUNT(*) FILTER (WHERE (cp.current_step_status_for_scheduler IS NULL
                OR cp.current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Done.toKey}
                OR cp.current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Skipped.toKey}
             ) AND NOT
             (cp.invalid_email OR cp.to_check OR (p.prospect_category_id_custom = ${DONOTCONTACT_CATEGORY_ID.id}))) as total_unsent_prospects_that_are_likely_valid,

             COUNT(*) FILTER (WHERE p.timezone IS NULL OR p.timezone = $campaignTimezone) as total_unsent_prospects_in_same_tz

           FROM campaigns_prospects cp
           INNER JOIN prospects p ON ( p.id = cp.prospect_id AND p.team_id = cp.team_id )
           WHERE cp.campaign_id = ${campaignId.id}
             AND cp.team_id = ${teamId.id}
             AND cp.active
             AND NOT cp.sent
             AND NOT cp.completed
           ;
           """
        .map(rs => UnsentProspectsCounts(
          total_unsent_prospects = rs.int("total_unsent_prospects"),
          total_unsent_prospects_with_invalid_emails = rs.int("total_unsent_prospects_with_invalid_emails"),
          total_unsent_prospects_with_missing_merge_tags = rs.int("total_unsent_prospects_with_missing_merge_tags"),
          total_unsent_prospects_in_dnc = rs.int("total_unsent_prospects_in_dnc"),
          total_unsent_prospects_with_previous_task_not_done = rs.int("total_unsent_prospects_with_previous_task_not_done"),
          total_unsent_prospects_that_are_likely_valid = rs.int("total_unsent_prospects_that_are_likely_valid"),
          total_unsent_prospects_in_same_tz = rs.int("total_unsent_prospects_in_same_tz")
        ))
        .single
        .apply()
    }

  }

  def findAndMarkSpecificProspectsAsCompleted(
    lastStepIds: List[Long],
    teamId: TeamId,
    markCompletedAfterDays: Int,
    campaignId: CampaignId,
    prospectIds: List[Long],
  )(using logger: SRLogger): Try[Seq[CPCompleted]] = Try {

    val daysInterval = SQLSyntax.createUnsafely(markCompletedAfterDays.toString)

    if (lastStepIds.isEmpty) {

      logger.shouldNeverHappen(
        s"findAndMarkCompletedProspects - lastStepIds is empty. teamId: $teamId :: prospectIds: $prospectIds"
      )

      Seq()

    } else if (prospectIds.isEmpty) {

      logger.shouldNeverHappen(
        s"findAndMarkCompletedProspects - prospectIds is empty. teamId: $teamId :: lastStepIds: $lastStepIds"
      )

      Seq()

    } else {

      // When we handle the head step condition we might have to remove this check `last_scheduled`

      DB autoCommit { implicit session =>

        val query =
          sqls"""
        UPDATE campaigns_prospects SET
          paused = true,
          paused_at = now(),
          completed = TRUE,
          completed_at = now(),
          will_resume_at = null,
          unpaused_by_admin = false,
          completed_reason = ${CampaignProspectCompletedReason.FindAndMarkCompletedProspects.toString}
        where active
        and campaign_id = ${campaignId.id}
        and current_step_id IN ($lastStepIds)
        and not completed
        and team_id = ${teamId.id}
        and prospect_id in ${SQLUtils.generateSQLValuesClause(arr = prospectIds)}
        and current_step_status_for_scheduler in (${CurrentStepStatusForScheduler.Done.toString}, ${CurrentStepStatusForScheduler.Skipped.toString})
        and last_scheduled < now() - interval '$daysInterval days'
        returning prospect_id, campaign_id, completed;
          """
        //Logger.info(s"\nfindAndMarkCompletedProspects: $currentStepId: $markCompletedAfterDays: $query")

        sql"""$query"""
          .map { rs =>
            CPCompleted(
              prospectId = rs.long("prospect_id"),
              campaignId = rs.long("campaign_id"),
              completed = rs.boolean("completed")
            )
          }
          .list
          .apply()

      }

    }

  }

  // completed [WEBHOOK] are called in ScheduleEmailCronServiceV2
  def findAndMarkCompletedProspects(
                                     lastStepIds: List[Long],
                                     teamId: TeamId,
                                     markCompletedAfterDays: Int
                                   )(using logger: SRLogger): Try[Seq[CPCompleted]] = blocking {

    val daysInterval = SQLSyntax.createUnsafely(markCompletedAfterDays.toString)

    if (lastStepIds.isEmpty) {
      logger.shouldNeverHappen("Last Step Ids is empty")
      Success(Seq())
    }
    else {
      Try {
        DB autoCommit { implicit session =>
          val query =
            sqls"""

      UPDATE campaigns_prospects SET
        paused = true,
        paused_at = now(),
        completed = TRUE,
        completed_at = now(),
        will_resume_at = null,
        unpaused_by_admin = false,
        completed_reason = ${CampaignProspectCompletedReason.FindAndMarkCompletedProspects.toString}
      where active
      and current_step_id IN ($lastStepIds)
      and not completed
      and team_id = ${teamId.id}
      and current_step_status_for_scheduler in (${CurrentStepStatusForScheduler.Done.toString}, ${CurrentStepStatusForScheduler.Skipped.toString})
      and last_scheduled < now() - interval '$daysInterval days'
      returning prospect_id, campaign_id, completed;
        """
          //Logger.info(s"\nfindAndMarkCompletedProspects: $currentStepId: $markCompletedAfterDays: $query")

          sql"""$query"""
            .map(rs => CPCompleted(
              prospectId = rs.long("prospect_id"),
              campaignId = rs.long("campaign_id"),
              completed = rs.boolean("completed")
            ))
            .list
            .apply()
        }
      }
    }
  }

  /*
    Drip Campaign Note:
      This is Only called via drip campaign flow in ChannelSchedulerTrait getProspectsByStepType.
      There we have this check
      case data: CampaignTypeData.DripCampaignData =>
      so we are good here

   */
  def updateNextCheckForSchedulingAt(
                                      data: List[UpdateNextScheduleAtData]
                                    ): Try[Int] = Try {

    if (data.isEmpty) {
      0
    } else {
      DB autoCommit { implicit session =>

        var valueParameters = List[Any]()

        val valuePlaceholder: SQLSyntax = data.map(p => {

            valueParameters = valueParameters ::: List(
              p.prospectId.id,
              p.teamId.id,

              p.campaignId.id,
              p.nexToBeCheckedAt
            )

            sqls"""
           (
              ?,
              ?,

              ?,
              ?::timestamptz
            )
          """

          })
          .reduce((vp1, vp2) => sqls"$vp1, $vp2")

        sql"""
       UPDATE campaigns_prospects cp
        SET
          next_check_for_scheduling_at = temp.next_check_for_scheduling_at

          FROM (
              VALUES $valuePlaceholder
            )
            AS temp(
              prospect_id,
              team_id,

              campaign_id,
              next_check_for_scheduling_at
            )
      WHERE
         cp.prospect_id = temp.prospect_id
         AND
         cp.team_id = temp.team_id
         AND
         cp.campaign_id = temp.campaign_id
        ;
       """
          .bind(valueParameters *)
          .update
          .apply()
      }
    }

  }

  def updateLatestTaskDoneOrSkippedAtForMigration(teamId: TeamId): Try[Int] = Try {

    DB autoCommit { implicit session =>
      sql"""
           UPDATE campaigns_prospects
            SET latest_task_done_or_skipped_at = last_scheduled
            WHERE
            (
              current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Done.toKey}
              OR
              current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Skipped.toKey}
            )
            AND last_scheduled IS NOT NULL
            AND team_id = ${teamId.id}
            AND latest_task_done_or_skipped_at IS NULL
            ;
           """
        .update
        .apply()
    }

  }

  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  // FIXME: sending holiday join not here in active_campaigns
  def findCampaignsForProspect(
                                loggedinAccountId: Long,
                                prospectIds: Seq[Long],
                                onlyActive: Boolean,
//                                emailNotCompulsoryEnabled: Boolean,
                                teamId: TeamId
                              )(using Logger: SRLogger)
  : Try[Seq[AssignedCampaignForProspect]] = Try {


    if (prospectIds.isEmpty) Seq()
    else {
      DB readOnly { implicit session =>

        val onlyActiveCheck = if (onlyActive) sqls" AND cp.active " else sqls" "

        val query =

          sql"""
    SELECT
      ses.email as from_email,
      cp.active,
      cp.campaign_id,
      cp.completed,
      c.name as campaign_name,
      CONCAT(a.first_name, ' ', a.last_name) as owner_name,
      CONCAT(p.first_name, ' ', p.last_name) as prospect_name,
      pe.email AS prospect_email,
      p.id AS prospect_id,
      c.account_id,
      c.open_tracking_enabled,
      c.click_tracking_enabled,
      c.status as campaign_status,
      pc.text_id as prospect_category_custom,

      json_build_object(

        'to_check', cp.to_check,
        'to_check_fields', cp.to_check_fields,
        'sent', cp.sent,
        'opened', cp.opened,
        'replied', cp.replied,
        'opted_out', cp.opted_out,
        'bounced', cp.bounced,
        'completed', cp.completed,
        'clicked', cp.clicked,
        'invalid_email', cp.invalid_email,
        'auto_reply', cp.auto_reply,
        'out_of_office', cp.out_of_office,
        'campaign_name', c.name,
        'campaign_id', c.id,

        'step', campaign_steps.label

      ) as campaign_prospect_status

    FROM campaigns_prospects cp
    JOIN campaigns c on c.id = cp.campaign_id AND c.team_id = cp.team_id
    JOIN accounts a on a.id = c.account_id
    INNER JOIN prospects p on ( p.id = cp.prospect_id AND p.team_id = cp.team_id )
    LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
    INNER JOIN prospect_categories_custom pc ON ( pc.id = p.prospect_category_id_custom AND pc.team_id = p.team_id )
    LEFT JOIN campaign_email_settings ces on ces.campaign_id = cp.campaign_id and ces.team_id = cp.team_id and cp.current_campaign_email_settings_id = ces.id
    LEFT JOIN email_settings ses on ses.id = ces.sender_email_setting_id AND ses.team_id = ces.team_id AND ses.status = ${EmailSettingStatus.Active.toString}
    LEFT JOIN campaign_steps ON campaign_steps.id = cp.current_step_id

    WHERE cp.prospect_id IN ${SQLUtils.generateSQLValuesClause(prospectIds)}
      AND cp.team_id = ${teamId.id}
      $onlyActiveCheck
    -- LIMIT 1
    ;
    """

        query
          .map(rs => {

            val ownerAccountId = rs.long("account_id")

            AssignedCampaignForProspect(
              active = rs.boolean("active"),
              campaign_id = rs.long("campaign_id"),
              owner_name = rs.string("owner_name"),
              prospect_name = rs.string("prospect_name"),
              prospect_email = rs.string("prospect_email"),
              prospect_id = rs.long("prospect_id"),
              prospect_category_custom = rs.string("prospect_category_custom"),
              owner_id = ownerAccountId,
              is_owner = ownerAccountId == loggedinAccountId,
              from_email = rs.stringOpt("from_email"),
              completed = rs.boolean("completed"),
              campaign_name = rs.string("campaign_name"),
              open_tracking_enabled = rs.boolean("open_tracking_enabled"),
              click_tracking_enabled = rs.boolean("click_tracking_enabled"),
              campaign_status = CampaignStatus.fromKey(rs.string("campaign_status")).get,
              campaign_prospect_status = Json.parse(rs.any("campaign_prospect_status").asInstanceOf[PGobject].getValue)


            )
          })
          .list
          .apply()

      }
    }
  }

  def unassignProspectAfterMergeDuplicates(
                                            teamId: TeamId,
                                            prospects: List[ProspectIdAndPotentialDuplicateProspectId],
                                          ): Try[Int] = Try {
    var valueParameters = List[Any]()

    val valuePlaceholder: SQLSyntax = prospects.map(p => {

        valueParameters = valueParameters ::: List(
          p.prospectId.id,
          p.potentialDuplicateProspectId.id,

          teamId.id
        )

        sqls"""
           (
              ?,
              ?,

              ?
            )
          """

      })
      .reduce((vp1, vp2) => sqls"$vp1, $vp2")

    DB autoCommit { implicit session =>
      sql"""
           UPDATE campaigns_prospects cp
            SET
              active = false,
              potential_duplicate_prospect_id = temp.potential_duplicate_prospect_id

              FROM (
                  VALUES $valuePlaceholder
                )
                AS temp(
                  prospect_id,
                  potential_duplicate_prospect_id,

                  team_id
                )
          WHERE
            cp.team_id = temp.team_id
            AND cp.prospect_id = temp.prospect_id
            ;
           """
        .bind(valueParameters *)
        .update
        .apply()
    }
  }

  def unassign(
                permittedOwnerIds: Seq[Long],
                doerAccountId: Long,
                teamId: Long,
                doerAccountName: String,
                campaignId: Long,
                prospectIds: Seq[Long]): Try[Int] = Try {

    // prospects must belong to accounts
    val accountOwnedProspects: Seq[Long] = {
      prospectIds
        .grouped(500)
        .flatMap(groupedPids => {


          DB.readOnly { implicit session =>

            sql"""
          SELECT id FROM prospects WHERE id IN ${SQLUtils.generateSQLValuesClause(groupedPids)}
            AND account_id IN ($permittedOwnerIds)
            AND team_id = $teamId
        """
              .map(rs => rs.long("id"))
              .list
              .apply()

          }
        })
        .toSeq
    }

    if (prospectIds.length != accountOwnedProspects.length) {

      throw new Exception("Given prospects do not exist in your account")

    } else {

      accountOwnedProspects
        .grouped(500)
        .map(groupedAccountOwnedPids => {

          DB localTx { implicit session =>

            val unassignedProspects =
              sql"""
          UPDATE campaigns_prospects cp SET
            active = FALSE
          FROM campaigns c
          WHERE c.id = cp.campaign_id
          AND cp.campaign_id = $campaignId AND cp.prospect_id IN ${SQLUtils.generateSQLValuesClause(groupedAccountOwnedPids)}
          AND cp.active
          AND c.team_id = $teamId
          returning cp.prospect_id, cp.campaign_id, c.name
        """
                .map(rs => (rs.long("prospect_id"), rs.long("campaign_id"), rs.string("name")))
                .list
                .apply()


            // add unassigned events
            val events = unassignedProspects.map { case (prospectId, cid, cname) => CreateProspectEventDB(

              event_type = EventType.EVENT_V3_PROSPECT_REMOVED,
              doer_account_id = Some(doerAccountId),
              doer_account_name = Some(doerAccountName),

              assigned_to_account_id = None,
              assigned_to_account_name = None,

              old_category = None,
              new_category = None,

              prospect_id = prospectId,
              email_thread_id = None,

              campaign_id = Some(cid),
              campaign_name = Some(cname),

              step_id = None,
              step_name = None,

              clicked_url = None,

              account_id = doerAccountId,
              team_id = teamId,
              email_scheduled_id = None,

              created_at = DateTime.now(),

              task_type = None,
              channel_type = None,
              task_uuid = None,
              call_conference_uuid = None,

              duplicates_merged_at = None,
              total_merged_prospects = None,
              potential_duplicate_prospect_id = None

            )
            }

            prospectAddEventDAO.addEvents(events = events).get

            unassignedProspects.size

          }

        })
        .sum


    }

  }


  // fixme now
  def unassignProspectsFromMainPage(
                                     permittedOwnerIds: Seq[Long],
                                     doerAccountId: Long,
                                     teamId: Long,
                                     doerAccountName: String,
                                     prospectIds: List[Long],
                                     campaignIds: Seq[Long]
                                   ): Try[Int] = Try {

    if (prospectIds.isEmpty) 0 else DB localTx { implicit session =>


      // prospects must belong to accounts
      val accountOwnedProspects =
        sql"""
          SELECT id FROM prospects WHERE id IN ${SQLUtils.generateSQLValuesClause(prospectIds)}
           AND account_id IN ($permittedOwnerIds)
           AND team_id = $teamId
        """
          .map(rs => rs.long("id"))
          .list
          .apply()


      if (prospectIds.length != accountOwnedProspects.length) {
        throw new Exception("Invalid prospect ids")

      } else {

        val campaignIdsCheck = if (campaignIds.isEmpty) sqls"" else sqls" AND cp.campaign_id IN ${SQLUtils.generateSQLValuesClause(campaignIds.distinct)} "

        // MULTICAMPAIGN DONE permittedAccountIds: I think unassigning from main page should unassign from all, so may be this is valid
        val unassignedProspects =
          sql"""
          UPDATE campaigns_prospects cp
          SET active = FALSE
          FROM campaigns c
          WHERE c.id = cp.campaign_id
          AND cp.prospect_id IN ${SQLUtils.generateSQLValuesClause(accountOwnedProspects)}
          AND cp.active
          AND c.team_id = $teamId
          $campaignIdsCheck
          returning cp.prospect_id, cp.campaign_id, c.name
        """
            .map(rs => (rs.long("prospect_id"), rs.long("campaign_id"), rs.string("name")))
            .list
            .apply()


        // add unassigned events
        val events = unassignedProspects.map { case (prospectId, cid, cname) => CreateProspectEventDB(

          event_type = EventType.EVENT_V3_PROSPECT_REMOVED,
          doer_account_id = Some(doerAccountId),
          doer_account_name = Some(doerAccountName),

          assigned_to_account_id = None,
          assigned_to_account_name = None,

          old_category = None,
          new_category = None,

          prospect_id = prospectId,
          email_thread_id = None,

          campaign_id = Some(cid),
          campaign_name = Some(cname),

          step_id = None,
          step_name = None,

          clicked_url = None,

          account_id = doerAccountId,
          team_id = teamId,
          email_scheduled_id = None,

          created_at = DateTime.now(),

          task_type = None,
          channel_type = None,
          task_uuid = None,
          call_conference_uuid = None,

          duplicates_merged_at = None,
          total_merged_prospects = None,
          potential_duplicate_prospect_id = None
        )
        }

        prospectAddEventDAO.addEvents(events = events).get

        unassignedProspects.size


      }


    }

  }

  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  def prospectsForPreviewV2(
                             campaignId: CampaignId,
                             searchQuery: Option[String],
                             limit: Int,
                             offset: Int,
                             teamId: TeamId,
                             orgId: OrgId,
                             camapign_email_setting_id: Option[CampaignEmailSettingsId],
                             channel_types: List[ChannelType],
//                             emailNotCompulsoryEnabled: Boolean
                           )(using Logger: SRLogger): Try[Seq[ProspectListForPreview]] = Try {



    DB readOnly { implicit session =>


      val query = {
        val searchCheck = if (searchQuery.isDefined) {
          val param = s"%${searchQuery.get.toString}%"
          sqls" AND pe.email ILIKE $param " //TODO: EMAIL_OPTIONAL: add checks on all deduplication columns

        } else sqls""

        val ces_check: SQLSyntax = if (camapign_email_setting_id.isDefined) {
          sqls"""AND
               ( cp.current_campaign_email_settings_id = ${camapign_email_setting_id.get.id}
               OR cp.current_campaign_email_settings_id IS NULL
               )
                """
        } else sqls""

        sql"""
      SELECT
        p.id AS prospect_id,
        pe.email AS prospect_email,
        CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
        p.linkedin_url AS prospect_linkedin,
        p.phone AS prospect_phone,
        p.company AS prospect_company

      FROM prospects p

      INNER JOIN campaigns_prospects cp ON (p.id = cp.prospect_id and p.team_id = cp.team_id)
      LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
      WHERE cp.campaign_id = ${campaignId.id}

        AND cp.active
        AND p.team_id = ${teamId.id}
        $searchCheck
        $ces_check

      ORDER BY p.id DESC
      LIMIT $limit OFFSET $offset;

    """
      }

      query
        .map(rs => {
          val name = rs.stringOpt("prospect_name")

          ProspectListForPreview(
            prospect_email = rs.stringOpt("prospect_email"), //it can be none if left join prospects_emails has non-email prospects
            prospect_id = rs.long("prospect_id"),
            prospect_name = if (name.isDefined && name.get.trim.nonEmpty) name else None,
            prospect_linkedin = rs.stringOpt("prospect_linkedin"),
            prospect_phone = sanitizeOptionString(rs.stringOpt("prospect_phone")),
            prospect_company = rs.stringOpt("prospect_company"),
            channel_types = channel_types
          )
        })
        .list
        .apply()

    }
  }


  /**
   * Added on 18th August 2022 for multichannel scheduler refactor:
   *
   * MULTICHANNEL_REFACTOR:
   *
   * checks and limits handled in this query:
   * 1. ignore do-not-contact prospects - GENERIC
   * 2. sendOnlyToProspectsWhoWereSentInCurrentCycleCheck - GENERIC
   *
   *
   * 3.a. campaign-prospect checks: replied, optedout, oof, auto-replied, etc. -: EMAIL-SPECIFIC
   * 3.b. campaign-prospect checks: completed etc. - GENERIC
   *
   * 4. ELIMINATED: sending-holiday-calendar check -- could not be eliminated with TZ Aggregation across prospects - GENERIC
   * 5. ELIMINATED: days_preference -- can be eliminated with TZ Aggregation across prospects
   * 6. ELIMINATED: schedule-time-window checks keeping in account prospect/campaign timezones -- can be eliminated with TZ Aggregation across prospects
   *
   * 7. ELIMINATED: email_validation related checks - EMAIL-SPECIFIC
   *
   *
   * 8. follow-up step delay checks - GENERIC
   *
   * 9. ELIMINATED: max emails per prospect / day and /week checks - EMAIL-SPECIFIC
   *
   * 10. fetchLimit query check (this includes maxToBeScheduled / warmupLimit etc) - EMAIL-SPECIFIC
   */

  // TODO - move logic to service layer
  def fetchProspectsV3Multichannel(
                                    channelType: ChannelType,
                                    allowedProspectTimezones: List[String],
                                    prospectIdGreaterThan: Option[Long] = None, // for first page of results
                                    campaignId: Long,
                                    teamId: TeamId,
                                    limit: Int,
                                    channelRelevantStepIdAndDelay: Vector[SchedulerMapStepIdAndDelay],
                                    newProspectsInCampaign: Boolean,
                                    firstStepIsMagicContent: Boolean,
                                    enable_magic_column: Boolean,
                                    useModifiedQueryForDripCampaign: Boolean,
                                    sendOnlyToProspectsWhoWereSentInCurrentCycle: Option[DateTime],
                                    campaign_email_setting: Option[CampaignEmailSettingForScheduler],
                                    orgId: OrgId,
//                                    emailNotCompulsoryEnabled: Boolean
                                  log_query: Boolean = false
                                  )(using Logger: SRLogger): Try[List[ProspectDataForChannelScheduling]] = {

    // mapStepIdAndDelay ignores the last step cause there is no email to be sent after that
    val totalFollowupSteps = channelRelevantStepIdAndDelay.size

    if (allowedProspectTimezones.isEmpty) {

      Logger.info("allowedProspectTimezones.isEmpty")
      Success(List())

    } else if (!useModifiedQueryForDripCampaign && !newProspectsInCampaign && (totalFollowupSteps == 0)) {
      /*
         Drip Campaign Note:
         useModifiedQueryForDripCampaign == true implies we are in the drip campaign flow
         otherwise we are in the sequential campaign flow

       */
      //if its a Drip campaign we can still schedule even if we have no totalFollowupSteps and !newProspectsInCampaign

      Success(List[ProspectDataForChannelScheduling]())

    } else if (limit <= 0) {

      Success(List[ProspectDataForChannelScheduling]())

    } else {

      /*
         Drip Campaign Note:
          For Drip we will reach here even if there are no total follow ups and !newProspectsInCampaingn
       */
      Logger.debug(s"fetchProspectsV3Multichannel: newProspectsInCampaign: $newProspectsInCampaign :: prospectIdGreaterThan: $prospectIdGreaterThan :: allowedProspectTimezones: $allowedProspectTimezones ")

      prospectDAOService.getProspectCategoryId(
        teamId = teamId,
        text_id = ProspectCategory.DO_NOT_CONTACT,
        account = None
      ) match {

        case Failure(err) =>
          Logger.fatal(msg = s"[fetchProspectsV3Multichannel prospectDAOService.getProspectCategoryId] teamId::$teamId text_id::${ProspectCategory.DO_NOT_CONTACT}", err = err)
          Failure(err)

        case Success(doNotContactCategoryId: ProspectCategoryId) =>

          Try {
            DB readOnly { implicit session =>

              /** NOTES:
               * this dayOfWeek starts from Sunday: 0 till Saturday: 6
               * postgres array indices start at 1, so we need to query by days_preference[$dayOfWeek + 1]
               */


              /*
            * prospect timezone filteringimplemented like this:
            *     `  AND (current_date + $dayFromTimeInSeconds * interval '1' second)::timestamptz AT TIME ZONE 'UTC' AT TIME ZONE p.timezone <= now() ` etc
            * REF: timezone filtering: http://stackoverflow.com/a/********
            * */

              /**
               * TIMEZONE test sql
               * select
               * *
               * (current_date + interval '1000 seconds') AT TIME ZONE 'UTC' as utc,
               * *
               * current_date as without,
               * *
               * (current_date AT TIME ZONE 'Asia/Kolkata' + interval '1000 seconds') AT TIME ZONE 'UTC' as kol,
               * *
               * (current_date + interval '1000 seconds') AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata' as kol,
               * *
               * (current_date AT TIME ZONE 'Asia/Kolkata' + interval '1 seconds') AT TIME ZONE 'Asia/Kolkata' AT TIME ZONE 'UTC' as kol,
               * *
               * current_date AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata' as utckol,
               * *
               * current_date AT TIME ZONE 'Asia/Kolkata' AT TIME ZONE 'UTC' as kolutc
               * *
               * ;
               */

              val query = CampaignProspectDAO.fetchProspectsV3MultichannelQuery(
                channelType = channelType,
                allowedProspectTimezones = allowedProspectTimezones,
                prospectIdGreaterThan = prospectIdGreaterThan, // for first page of results
                campaignId = campaignId,
                teamId = teamId,
                limit = limit,
                doNotContactCategoryId = doNotContactCategoryId,
                useModifiedQueryForDripCampaign = useModifiedQueryForDripCampaign,
                enable_magic_column = enable_magic_column,
                channelRelevantStepIdAndDelay = channelRelevantStepIdAndDelay,
                newProspectsInCampaign = newProspectsInCampaign,
                firstStepIsMagicContent = firstStepIsMagicContent,
                sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,
                campaign_email_setting = campaign_email_setting,
                orgId = orgId,
//                emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
              )

              //teamId == TeamId(6412) ||
              if (log_query) {
                Logger.doNotTruncate(s"\n\n\nquery: ${query.value} :: ${query.parameters} :: ${query.toString}\n\n\n")
              }

              sql"""
                 $query
                 """
                .map(rs => ProspectDataForChannelScheduling.fromDBMultichannel(rs = rs, channelType = channelType))
                .list
                .apply()
            }
          }
      }

    }
  }


  def fetchProspectsForPreEmailValidation(
                                           minimumPreEmailValidatedProspectsCount: Int,
                                           campaignId: CampaignId,
                                           teamId: TeamId,
                                           doNotContactCategoryId: Long
                                         ): Try[List[ProspectForValidation]] = Try {

    DB readOnly { implicit session =>

      // Future Improvement: add the allowedProsepectTimezones check
      sql"""
          SELECT
            prospects.id,
            prospects.team_id,
            prospects.first_name,
            prospects.last_name,
            pe.email,
            pe.email_checked,
            pe.email_sent_for_validation,
            pe.email_sent_for_validation_at,
            pe.account_id,
            pe.team_id

          FROM campaigns_prospects cp
          INNER JOIN prospects ON ( prospects.id = cp.prospect_id AND cp.team_id = prospects.team_id )
          INNER JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)

          WHERE
            cp.campaign_id  = ${campaignId.id}
            AND cp.team_id = ${teamId.id}

            AND cp.active = true

            AND cp.sent = false

            AND cp.to_check = false

            AND (
              cp.unpaused_by_admin = true

              OR (
                cp.replied = false
                AND cp.opted_out = false
                AND cp.out_of_office = false
                AND cp.auto_reply = false
                AND cp.bounced = false
                AND cp.completed = false
                AND cp.paused = false
                AND cp.will_resume_at is null
              )
            )


            AND pe.email_checked = false

            AND pe.email_sent_for_validation = false

            AND pe.email_sent_for_validation_at is null

            AND prospects.prospect_category_id_custom != ${doNotContactCategoryId}


          ORDER BY prospects.id ASC

          LIMIT $minimumPreEmailValidatedProspectsCount
          ;

        """
        .map(rs =>

          ProspectForValidation(
            id = rs.long("id"),
            email = rs.string("email"),
            email_checked = rs.boolean("email_checked"),
            email_sent_for_validation = rs.boolean("email_sent_for_validation"),
            email_sent_for_validation_at = rs.jodaDateTimeOpt("email_sent_for_validation_at")
          )
        )
        .list
        .apply()
    }
  }

  /*

    24-Jan-2025:
      called from channelSchedulerTrait -
        - function getChannelTasksToBeScheduled(), line 800, with condition campaign_type = DRIP,
        - function createProspectIdWithNextStepIdMap, line 2822,
            - This is caled from
                  * 1. line : 3093 -> ChannelSchedulerTrait under drip Campaign type at 3067
                  * 2. line : 3128 -> ChannelSchedulerTrait under drip Campaign type at 3067
                  * 3. line : 3197 -> ChannelSchedulerTrait under drip Campaign type at 3067
      IndependentStepSchedulerService - line 87
            - This is only for drip
   */

  def getPreviouslySentStepsForProspect(
                                         prospectId: ProspectId,
                                         teamId: TeamId,
                                         campaignId: CampaignId,
                                       ): Try[List[LastSentStepData]] = Try {
    DB readOnly { implicit session =>
      sql"""
             with sentdata as (
             SELECT
                es.id::text as sent_id,
                ${ChannelType.EmailChannel.toString} as channel_type,
                es.sent_at,
                es.bounced,
                es.replied,
                es.replied_at,
                es.clicked,
                es.opened,
                es.opened_at,
                es.reply_type,
                null as failure_reason,
                null as call_status,
                rst.reply_sentiment_name as reply_sentiment,
                null as log_created_at,
                ${TableTypeForScheduler.EmailScheduled.key} as table_type,
                ${TaskStatusType.Done.toString} as status


              FROM emails_scheduled es
              LEFT JOIN email_threads et ON et.id = es.email_thread_id and es.team_id = et.team_id
              LEFT JOIN reply_sentiments_for_teams rst ON rst.uuid = et.reply_sentiment_uuid and et.team_id = rst.team_id
              WHERE es.team_id = ${teamId.id}
              AND es.prospect_id = ${prospectId.id}
              AND es.campaign_id = ${campaignId.id}
              -- AND es.scheduled_from_campaign
              AND es.sent

              UNION ALL

              SELECT
                t.task_id as sent_id,
                t.channel_type,
                (case when t.skipped_at is null then t.done_at else t.skipped_at end) as sent_at,
                null as bounced,
                null as replied,
                null as replied_at,
                null as clicked,
                null as opened,
                null as opened_at,
                null as reply_type,
                t.failure_reason,
                cpl.status as call_status,
                rst.reply_sentiment_name as reply_sentiment,
                ccl.log_created_at as log_created_at,
                ${TableTypeForScheduler.Tasks.key} as table_type,
                t.status
              FROM tasks t
              LEFT JOIN call_conference_logs ccl on t.team_id = ccl.team_id and ccl.task_uuid = t.task_id
              LEFT JOIN reply_sentiments_for_teams rst ON rst.uuid = ccl.reply_sentiment_uuid and ccl.team_id = rst.team_id
              Left Join call_participants_logs cpl ON cpl.call_conference_uuid = ccl.conference_uuid and ccl.team_id = cpl.team_id and cpl.participant_label = ${AppConfig.Calling.customer_label}
              WHERE t.team_id = ${teamId.id}
              AND t.prospect_id = ${prospectId.id}
              AND t.campaign_id = ${campaignId.id}
              AND t.created_via =  ${TaskCreatedVia.Scheduler.toKey}
              AND t.status in (${TaskStatusType.Done.toKey}, ${TaskStatusType.Skipped.toKey})
              order by sent_at DESC, log_created_at DESC
              )


              select
                sent_id,
                channel_type,
                sent_at::timestamptz,
                bounced,
                replied,
                replied_at,
                clicked,
                opened,
                opened_at,
                reply_type,
                failure_reason,
                call_status,
                reply_sentiment,
                table_type,
                status
                from sentdata
              order by sentdata.sent_at desc;
              """.map(rs => {
          LastSentStepData(
            sent_id = rs.string("sent_id"),
            channel_type = ChannelType.fromKey(rs.string("channel_type")).get,
            sent_at = rs.jodaDateTime("sent_at"),
            bounced = rs.booleanOpt("bounced"),
            replied = rs.booleanOpt("replied"),
            replied_at = rs.jodaDateTimeOpt("replied_at"),
            clicked = rs.booleanOpt("clicked"),
            opened = rs.booleanOpt("opened"),
            opened_at = rs.jodaDateTimeOpt("opened_at"),
            reply_type = rs.stringOpt("reply_type").map(d => EmailReplyType.withName(d)),
            failure_reason = rs.stringOpt("failure_reason"),
            call_status = rs.stringOpt("call_status").map(a => ParticipantCallStatus.fromString(a).get),
            reply_sentiment = rs.stringOpt("reply_sentiment"),
            table_type = TableTypeForScheduler.fromKey(rs.string("table_type")).get,
            task_status = TaskStatusType.fromString(rs.string("status")).get
          )
        })
        .list
        .apply()
    }
  }

  def checkProspectCountAtleastMinimum(
                                        campaignId: CampaignId,
                                        minCount: Long,
                                        doNotContactCategoryId: Long,
                                        teamId: TeamId
                                      ): Try[Option[Boolean]] = Try {

    DB readOnly { implicit session =>

      val limit = minCount
      // Future improvement: add the allowedProsepectTimezones check

      sql"""
      Select COUNT(*) >= ${limit} as is_min_validated  From (
            SELECT
               cp.prospect_id
            FROM campaigns_prospects cp
                INNER JOIN prospects ON (prospects.id = cp.prospect_id AND prospects.team_id = cp.team_id)
                INNER JOIN prospects_emails pe ON ( pe.prospect_id = prospects.id AND pe.team_id = prospects.team_id AND pe.is_primary)
            WHERE
                 cp.campaign_id = ${campaignId.id}
                 AND cp.team_id = ${teamId.id}
                 AND cp.active
                 AND (

                  -- either it has been validated, or its sent for validation

                  pe.email_checked = true
                  OR
                  pe.email_sent_for_validation = true
                )

                 AND not pe.invalid_email

                 -- 20-feb-2023: in the new validation flow (we are not validating emails that were validated recently,
                 --       email_sent_for_validation will be false always. so the below checks need to be removed)
                 -- AND pe.email_sent_for_validation = true
                 -- AND pe.email_sent_for_validation_at is not null


                 AND cp.sent = false
                 AND prospects.prospect_category_id_custom != ${doNotContactCategoryId}
                 AND cp.completed = false
                 AND cp.current_step_id is null

                 -- ignore prospects with missing merge-tags while counting validated prospects
                 AND cp.to_check = false

            LIMIT ${limit}

       ) as min_validated_count ;
         """
        .map(rs => rs.boolean("is_min_validated"))
        .single
        .apply()
    }
  }

  def getProspectsInCampaignFromGivenProspects(
                                                prospectIds: Seq[Long],
                                                campaignId: CampaignId,
                                                teamId: TeamId,
                                              )(using Logger: SRLogger): Try[Seq[Long]] = Try {
    DB readOnly { implicit session =>
      sql"""
          SELECT
            prospect_id
          FROM
            campaigns_prospects
          WHERE
            campaign_id = ${campaignId.id}
          AND team_id = ${teamId.id}
          AND
            prospect_id IN ${SQLUtils.generateSQLValuesClause(prospectIds)}
         """
        .map(rs => rs.long("prospect_id"))
        .list
        .apply()
        .toSeq
    }
  }

  def filterByProspectValidationStatus(

                                        prospectIds: Set[Long],
                                        team_id: TeamId,

                                      )(using Logger: SRLogger): Try[Set[Long]] = Try {
    DB readOnly { implicit session =>

      if (prospectIds.isEmpty) {

        Set[Long]()

      } else {

        val query =
          sql"""

          SELECT

            prospects.id as prospect_id


          FROM prospects
            INNER JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = ${team_id.id}) AND pe.is_primary)



          WHERE

            prospects.id IN ($prospectIds)

            AND prospects.team_id = ${team_id.id}

            AND pe.email_format_valid = true

            AND (
              (
                pe.email_checked = true
                AND pe.invalid_email = false
              )

              OR

              pe.email_checked = false

            )


        """

        query
          .map(_.long("prospect_id"))
          .list
          .apply()
          .toSet


      }


    }

  }

  def updateCampaignProspectStatusToDone(
                                          campaignId: CampaignId,
                                          prospectId: ProspectId,
                                          taskId: String
                                        ): Try[Int] = Try {

    DB autoCommit { implicit session =>
      sql"""
           UPDATE campaigns_prospects
           SET
              current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Done.toKey},
              sent = true,
              sent_at = COALESCE(sent_at, now()),
              latest_task_done_or_skipped_at = now()
           WHERE
              campaign_id = ${campaignId.id}
           AND prospect_id = ${prospectId.id}
           AND current_step_task_id = $taskId
           ;
           """
        .update
        .apply()

    }

  }

  def updateCampaignProspectStatusToDoneForIndependentStepScheduler(
                                                                     team_id: TeamId,
                                                                     campaignId: CampaignId,
                                                                     prospectIds: Seq[ProspectId],
                                                                     step_details_that_is_completed: CampaignStepWithChildren,
                                                                   ): Try[Int] = Try {

    DB autoCommit { implicit session =>
      sql"""
           UPDATE campaigns_prospects
           SET
              current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Done.toKey},
              sent = true,
              sent_at = COALESCE(sent_at, now()),
              -- latest_task_done_or_skipped_at = now()
              current_step_id = ${step_details_that_is_completed.id},
              current_step_type = ${step_details_that_is_completed.step_type.toKey},
              current_step_channel_type = ${step_details_that_is_completed.step_type.channelType.toString}
           WHERE
           team_id = ${team_id.id}
           and  campaign_id = ${campaignId.id}
           AND prospect_id in (${prospectIds.map(_.id)})
           """
        .update
        .apply()

    }

  }

  def filterProspectsBySentCountBasedOnProspectLimit(

                                                      prospectIds: Set[Long],
                                                      campaignTimezone: String,
                                                      team_id: TeamId,
                                                      maxEmailsPerProspectPerDay: Int,
                                                      maxEmailsPerProspectPerWeek: Int

                                                    )(using Logger: SRLogger): Try[Set[Long]] = Try {
    DB readOnly { implicit session =>

      if (prospectIds.isEmpty) {

        Set[Long]()

      } else {

        val tz = sqls" TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE $campaignTimezone END) "

        val query =
          sql"""

          SELECT

            prospects.id as prospect_id


          FROM prospects
          LEFT JOIN emails_scheduled es ON (
            es.prospect_id = prospects.id
            and
            es.scheduled_from_campaign
            and
            es.team_id = ${team_id.id}
            and
            (
              es.sent_at > now() - interval '8 days'
              or
              es.scheduled_at > now() - interval '8 days'
            )

          )



          WHERE

            prospects.id IN ($prospectIds)

            AND prospects.team_id = ${team_id.id}

          GROUP BY prospects.id

          HAVING (

            -- daily max email sent per prospect
            -- check how many emails have been sent or scheduled since start of day from any campaign
            (COUNT(es.id) FILTER (WHERE es.scheduled_from_campaign AND (es.sent = false OR ((es.scheduled_at AT $tz) > date_trunc('day', (now() AT $tz)))))) < $maxEmailsPerProspectPerDay


            AND


            -- weekly max email sent per prospect
            -- check how many emails have been sent or scheduled since start of week from any campaign
            (COUNT(es.id) FILTER (WHERE es.scheduled_from_campaign AND (es.sent = false OR ((es.scheduled_at AT $tz) > date_trunc('week', (now() AT $tz)))))) < $maxEmailsPerProspectPerWeek

         )


        """

        query
          .map(_.long("prospect_id"))
          .list
          .apply()
          .toSet


      }


    }

  }

  def mapProspectIdsWithProspectAccountIds(
                                            prospectIds: Set[ProspectId],
                                            teamId: TeamId
                                          ): Try[Map[ProspectId, Option[ProspectAccountsId]]] = Try {

    if (prospectIds.isEmpty) {
      Map()
    }
    else {
      DB readOnly { implicit session =>
        sql"""
             SELECT p.id, p.prospect_account_id
             FROM prospects p
             WHERE p.id IN (${prospectIds.map(_.id)})
             AND p.team_id = ${teamId.id}
             """
          .map(rs => {
            (ProspectId(rs.long("id")), rs.longOpt("prospect_account_id").map(ProspectAccountsId(_)))
          })
          .list
          .apply()
          .toMap
      }
    }

  }

  def getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount(
                                                                  prospectAccountIds: List[ProspectAccountsId],
                                                                  campaignTimezone: String,
                                                                  teamId: TeamId
                                                                ): Try[Map[ProspectAccountsId, EmailsScheduledCount]] = Try {

    if (prospectAccountIds.isEmpty) {
      Map()
    }
    else {
      DB readOnly { implicit session =>
        sql"""
             SELECT
                es.prospect_account_id as prospect_account_id,
                COUNT(*) FILTER (WHERE (es.scheduled_at AT TIME ZONE $campaignTimezone) > date_trunc('day', (now() AT TIME ZONE $campaignTimezone))) as emails_scheduled_in_last_24_hours,
                COUNT(*) FILTER (WHERE (es.scheduled_at AT TIME ZONE $campaignTimezone) > date_trunc('week', (now() AT TIME ZONE $campaignTimezone))) as emails_scheduled_in_last_week
             FROM
                emails_scheduled es
             WHERE
                es.prospect_account_id IN (${prospectAccountIds.map(_.id)})
                AND es.scheduled_from_campaign
             AND
                es.team_id = ${teamId.id}
             AND
                es.scheduled_at > now() - interval '9 days'

             GROUP BY es.prospect_account_id
             ;
             """
          .map(rs => {
            (ProspectAccountsId(rs.long("prospect_account_id")),
              EmailsScheduledCount(
                scheduled_in_last_24hours = rs.int("emails_scheduled_in_last_24_hours"),
                scheduled_in_last_week = rs.int("emails_scheduled_in_last_week")
              )
            )
          })
          .list
          .apply()
          .toMap
      }
    }

  }

  def filterProspectWhoHaveHoliday(

                                    prospectIds: List[Long],
                                    campaignTimezone: String,
                                    campaignSendingHolidayCalendarId: Option[Long],
                                    scheduleFromTime: DateTime,
                                    team_id: Long,

                                  )(using Logger: SRLogger): Try[Set[Long]] = Try {
    DB readOnly { implicit session =>

      if (prospectIds.isEmpty) {

        Set[Long]()

      } else {

        val tz = sqls" TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE $campaignTimezone END) "


        val query =
          sql"""

          SELECT

            prospects.id as prospect_id


          FROM prospects

          LEFT JOIN sending_holiday_calendars shc on (

            shc.id = (
              CASE WHEN
                prospects.sending_holiday_calendar_id is not null
              THEN
                prospects.sending_holiday_calendar_id
              ELSE
                $campaignSendingHolidayCalendarId
              END
            )

          )
          LEFT JOIN sending_holiday_calendar_dates shcd ON (

            shcd.sending_holiday_calendar_id = shc.id

            and check_if_holiday(

                -- holiday start
                shcd.start_date_utc,

                -- holiday end
                shcd.end_date_utc,

                -- current date with start of day time in relevant timezone
                date_trunc('day', $scheduleFromTime AT $tz)

            )

          )

          WHERE

            prospects.id IN ($prospectIds)

            AND prospects.team_id = $team_id

            -- should be a holiday
            AND shcd.id IS NOT NULL

        """

        query
          .map(_.long("prospect_id"))
          .list
          .apply()
          .toSet


      }


    }

  }


  // used in new scheduler
  def getDistinctProspectTimezones(
                                    teamId: Long,
                                    campaignId: Long
                                  ): Try[List[String]] = Try {

    sql"""


      SELECT
        DISTINCT(p.timezone) AS timezone

      FROM
        campaigns_prospects cp
        INNER JOIN prospects p ON (p.id = cp.prospect_id AND p.team_id = cp.team_id AND p.timezone is not null)

      WHERE
        cp.campaign_id = $campaignId
        and cp.team_id = $teamId
        AND NOT cp.completed
        ;
    """
      .map(rs => rs.string("timezone"))
      .list
      .apply()

  }


  // FIXME: SQL INJECTION
  def _updateScheduledStatus(
    scheduledCampaignProspectData: Seq[CampaignProspectUpdateScheduleStatus]
  )(using logger: SRLogger): Try[Seq[Long]] = Try {
    DB localTx { implicit session =>
      if (scheduledCampaignProspectData.isEmpty) Seq()
      else {
        var updateParams = List[Any]()
        val updatePlaceholder: SQLSyntax = scheduledCampaignProspectData.map { cpData =>
          // Determine if current_step_id should be updated based on partitioning logic
          val updateStepId = !(
            cpData.current_step_status_for_scheduler_data.status_type == CurrentStepStatusForScheduler.PendingApproval ||
            cpData.current_step_status_for_scheduler_data.status_type == CurrentStepStatusForScheduler.AiContentQueued ||
            cpData.current_step_status_for_scheduler_data.status_type == CurrentStepStatusForScheduler.Approved
          )

          var valueList = List(
            cpData.step_id,
            cpData.campaign_id,
            cpData.prospect_id,
            cpData.current_step_type.toKey,
            cpData.current_step_type.channelType.toString,
            cpData.current_step_task_id,
            cpData.email_message_id,
            cpData.current_campaign_email_settings_id.map(_.id),
            updateStepId // New boolean parameter
          )
          cpData.current_step_status_for_scheduler_data match {
            case data: CurrentStepStatusForSchedulerData.Due =>
              valueList = valueList ++ List(data.status_type.toKey, data.due_at.toString, None)
            case data: CurrentStepStatusForSchedulerData.Done =>
              valueList = valueList ++ List(data.status_type.toKey, None, data.done_at.toString)
            case data: CurrentStepStatusForSchedulerData.Skipped =>
              valueList = valueList ++ List(data.status_type.toKey, None, data.skipped_at.toString)
            case data: CurrentStepStatusForSchedulerData.PendingApproval =>
              valueList = valueList ++ List(data.status_type.toKey, data.pending_approval_at.toString, None)
            case data: CurrentStepStatusForSchedulerData.Approved =>
              valueList = valueList ++ List(data.status_type.toKey, None, None)
            case data: CurrentStepStatusForSchedulerData.AiContentQueued =>
              valueList = valueList ++ List(data.status_type.toKey, data.ai_content_queued_at.toString, None)

          }
          if(cpData.current_step_status_for_scheduler_data.status_type == CurrentStepStatusForScheduler.PendingApproval ||
            cpData.current_step_status_for_scheduler_data.status_type == CurrentStepStatusForScheduler.AiContentQueued ||
            cpData.current_step_status_for_scheduler_data.status_type == CurrentStepStatusForScheduler.Approved) {

            logger.debug(s"[_updateScheduledStatus] Adding update params for ${cpData.step_id} with status ${cpData.current_step_status_for_scheduler_data.status_type} and update_step_id :${updateStepId}")

          }

          updateParams = updateParams ::: valueList
          sqls"(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
        }.reduce((vp1, vp2) => sqls"$vp1, $vp2")

        val combinedSQL =
          sql"""
            UPDATE campaigns_prospects cp
              SET
                current_step_id = CASE WHEN temp.update_step_id THEN temp.step_id ELSE cp.current_step_id END,
                last_scheduled = COALESCE(temp.scheduled_at::timestamptz, cp.last_scheduled),
                current_step_status_for_scheduler = temp.current_step_status_for_scheduler,
                latest_task_done_or_skipped_at = COALESCE(temp.latest_task_done_or_skipped_at::timestamptz, cp.latest_task_done_or_skipped_at),
                current_step_type = temp.current_step_type,
                current_step_channel_type = temp.current_step_channel_type,
                current_step_task_id = COALESCE(temp.current_step_task_id, cp.current_step_task_id),
                current_email_scheduled_id_for_internal_use = COALESCE(temp.current_email_scheduled_id_for_internal_use::BIGINT, cp.current_email_scheduled_id_for_internal_use),
                current_campaign_email_settings_id = COALESCE(temp.current_campaign_email_settings_id::BIGINT, cp.current_campaign_email_settings_id)
              FROM (
                VALUES $updatePlaceholder
              )
              AS temp(
                step_id,
                campaign_id,
                prospect_id,
                current_step_type,
                current_step_channel_type,
                current_step_task_id,
                current_email_scheduled_id_for_internal_use,
                current_campaign_email_settings_id,
                update_step_id,
                current_step_status_for_scheduler,
                scheduled_at,
                latest_task_done_or_skipped_at
              )
              WHERE cp.campaign_id = temp.campaign_id
              AND cp.prospect_id = temp.prospect_id
              RETURNING cp.prospect_id;
          """

        logger.debug(s"[_updateScheduledStatus] Executing combinedSQL for ${scheduledCampaignProspectData.size} prospects.")
        combinedSQL
          .bind(updateParams *)
          .map(rs => rs.long("prospect_id"))
          .list
          .apply()
      }
    }
  }
  /*

  ---
  1. select current_step_id of prospect and its status -> null,  due / done [ does it matters ] ? yes,
  2. if status is done / null don't revert it. -> done
  3. if status is due ->
  4. current_step_id can be one of following null, or some(id)
  5. if its null ->
  6. don't revert it leave it.
  7. if its some(id), it can either be head_step_id or some_other_id.
  8. check step_type of current_step_id:
  9. step_type can be auto-email, manual-email, auto-linkedin-task, other-manual-tasks( general , linkedin , whatsapp, call )

  10. What I need to be careful here is manual tasks aren't deleted, and if we revert prospect back to them we don't want this.
  11. we need to move prospects back to previous step that what revert does, basically we want current step to get scheduled again.

  12. if its head_step_id and step_type is manual tasks or auto-linkedin tasks dont revert
  12.1. if its non-head-step_id and step_type is manual tasks or auto-linkedin tasks don't revert.
  13. it its head_step_id and step_type is auto-email tasks then use current revert.
  13.1 if its non-head-step_id and step_type is auto-email tasks then revert to previous step_id
  13.2. for this. I need to know previous_step_type and based on the previous step get its data filled.
  13.3
  13.4. anyways when revert is done set the current_step_scheduler_status as done.

  -----

  first fetch campaign_prospects data for prospect.
  then check if its status is done / null return success(0) that means revert wasn't performed
  if due and current_step_id == null: ? should never happen ( add fatal log ), or step status = done:
  if due and current_step_id == some(step_id):
      fetch campaign_steps data like step_id and step_type:
      if current_step_type == auto-email then only go for revert else success(0)

 */


  def campaignProspectMultichannelRevert(
                                          campaign_id: CampaignId,
                                          prospectId: ProspectId,
                                          current_step_id: StepId,
                                          revert_data: RevertData,
                                          reverted_by: String,
                                          team_id: TeamId,
                                          previous_task_step_type: CampaignStepType,
                                          previous_task_channel_type: ChannelType,
                                          deletion_reason: DeletionReason
                                        )(using Logger: SRLogger, session: DBSession): Try[Int] = Try {

    Logger.info(s"campaignProspectMultichannelRevert :: going to revert :: campaign_id: ${campaign_id.id} :: prospect_id: ${prospectId.id} :: team_id: ${team_id.id} :: current_step_id : ${current_step_id.id} ")

    val rows: SQLSyntax = revert_data match {

      case task_data: RevertData.TaskRevertData =>

        val latestTaskUpdate = task_data.status_type match {
          case TaskStatusType.Skipped =>
            if (task_data.skipped_at.isEmpty) {
              Logger.shouldNeverHappen(
                s"""
                             [campaignProspectMultichannelRevert] :: task_status is skipped and skipped_at is empty ::
                             cid_${campaign_id.id} :: pid_${prospectId.id} :: tid:${team_id.id} :: step_id:${current_step_id.id}
                             """)
            }
            sqls"""
                  current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Skipped.toKey},
                  latest_task_done_or_skipped_at = ${task_data.skipped_at.getOrElse(DateTime.now())},
                  """

          case TaskStatusType.Done =>
            if (task_data.done_at.isEmpty) {
              Logger.shouldNeverHappen(
                s"""
                             [campaignProspectMultichannelRevert] :: task_status is done and done_at is empty ::
                             cid_${campaign_id.id} :: pid_${prospectId.id} :: tid:${team_id.id} :: step_id:${current_step_id.id}
                             """)
            }
            sqls"""
                  current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Done.toKey},
                  latest_task_done_or_skipped_at = ${task_data.done_at.getOrElse(DateTime.now())},
                  """

          case TaskStatusType.Due |
               TaskStatusType.Archive |
               TaskStatusType.Failed |
               TaskStatusType.Snoozed |
               TaskStatusType.PushedToLinkedinExecutionQueue |
            /*
              25-april-2025:
                - We are setting prospect status to previous values,
                - now we are deleting the tasks which are due/ done/ approved/ pending_approval ( since this step is reverted ).
                - so, we will only be setting the current_status to previous value that is done or skipped or null
                - hence, not setting anything for approved / pending_approval status
             */
               TaskStatusType.PendingApproval |
               TaskStatusType.Approved |
               TaskStatusType.QueuedToMq=>

            Logger.shouldNeverHappen(
              s"""
                             [campaignProspectMultichannelRevert] :: task_status is ${task_data.status_type.toKey} ::
                             cid_${campaign_id.id} :: pid_${prospectId.id} :: tid:${team_id.id} :: step_id:${current_step_id.id}
                             """)

            sqls""
        }

        sqls"""
                 current_step_task_id = ${task_data.task_id},
                 last_scheduled = ${task_data.task_due_at},
                 $latestTaskUpdate
              """

      case email_data: RevertData.EmailScheduledRevertData =>

        val sent_at: SQLSyntax = email_data.sent_at match {

          case None =>
            sqls""""""

          case Some(time) =>

            sqls"""
                  sent = true,
                  sent_at = ${time},
                  latest_task_done_or_skipped_at = $time,
                  """

        }

        sqls"""
              current_step_task_id = ${email_data.emails_scheduled_id},
              current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Done.toKey},
              last_scheduled = ${email_data.last_scheduled_at},
              ${sent_at}

            """

    }


    sql"""
         UPDATE campaigns_prospects cp
            SET
            ${rows}
            current_step_id = ${current_step_id.id},
            reverted_by = ${reverted_by},
            revert_log_trace_id = ${Logger.logTraceId},
            last_reverted_at = now(),
            current_step_channel_type = ${previous_task_channel_type.toString},
            revert_reason = ${deletion_reason.toString},
            current_step_type = ${previous_task_step_type.toKey}

         WHERE
         cp.team_id = ${team_id.id}
         AND cp.campaign_id = ${campaign_id.id}
         AND cp.prospect_id  = ${prospectId.id}

       """
      .update
      .apply()

  }

  def revertCampaignProspectAfterHeadStepDeletion(
                                                   team_id: TeamId,
                                                   campaign_id: CampaignId,
                                                   prospect_id: ProspectId,
                                                   reverted_by: String,
                                                   revert_reason: DeletionReason
                                                 )(implicit session: DBSession, logger: SRLogger): Try[Int] = Try {


    sql"""
         UPDATE campaigns_prospects
                SET
                  last_scheduled = null,
                  latest_task_done_or_skipped_at = null,
                  current_step_id = null,
                  current_step_status_for_scheduler = null,
                  reverted_by = ${reverted_by.toString},
                  revert_log_trace_id = ${logger.logTraceId},
                  last_reverted_at = now(),
                  current_step_channel_type = null,
                  revert_reason = ${revert_reason.toString},
                  current_step_type = null
             WHERE
              team_id = ${team_id.id}
             AND  campaign_id = ${campaign_id.id}
             AND  prospect_id = ${prospect_id.id}

       """
      .update.apply()

  }

  // INTERNAL USE: there is no Try wrapper cause this is used inside other transactions wrapped in try
  // TODO: consider creating a queue for this

  def _revertCurrentStepIdOnDeactivatingCampaign(
                                                  campaignId: Long,
                                                  prospectsIdsToRevert: Seq[Long],
                                                  reverted_by: String,
                                                  deletion_reason: DeletionReason
                                                )(using Logger: SRLogger, session: DBSession): Int = {

    Logger.info(s"CampaignProspect _revertCurrentStepIdOnDeactivatingCampaign prospectsIdsToRevert (campaign $campaignId) :: $prospectsIdsToRevert")


    if (prospectsIdsToRevert.isEmpty) {

      0

    } else {

      //  --SELECT csr.parent_id FROM campaign_steps_relationships csr WHERE csr.child_id = cp.current_step_id
      sql"""
          UPDATE campaigns_prospects cp SET
            last_scheduled = (
              SELECT es.scheduled_at FROM emails_scheduled es
              where
                es.campaign_id = cp.campaign_id
                and es.prospect_id = cp.prospect_id
                AND es.scheduled_from_campaign
                --AND es.sent
                order by es.scheduled_at desc
                limit 1
              ),

            current_step_id = (
                select est.step_id
                from emails_scheduled est
                where
                  est.prospect_id = cp.prospect_id
                  and est.campaign_id = cp.campaign_id
                  and est.scheduled_from_campaign
                  --and est.sent
                order by est.scheduled_at desc
                limit 1
              ),
              current_email_scheduled_id_for_internal_use = (
              select est.id
              from emails_scheduled est
              where
                est.prospect_id = cp.prospect_id
                and est.campaign_id = cp.campaign_id
                and est.scheduled_from_campaign
                --and est.sent
              order by est.scheduled_at desc
              limit 1
            ),

              current_step_task_id = (
                  select est.id
                  from emails_scheduled est
                  where
                    est.prospect_id = cp.prospect_id
                    and est.campaign_id = cp.campaign_id
                    and est.scheduled_from_campaign
                  order by est.scheduled_at desc
                  limit 1
              ),

              reverted_by = ${reverted_by},

              revert_reason = ${deletion_reason.toString},

              revert_log_trace_id = ${Logger.logTraceId},

              last_reverted_at = now(),

              current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Done.toKey}

          WHERE cp.campaign_id = $campaignId AND cp.prospect_id IN ${SQLUtils.generateSQLValuesClause(prospectsIdsToRevert)};
        """
        .update
        .apply()
    }
  }

  //SENDER_ROTATION
  // called from -
  // EmailChannelScheduler.getSentOrScheduledProspectsCountForChannel
  def getSentOrScheduledProspectsCountForEmail(
                                                emailSettingIds: Seq[EmailSettingId],
                                                accountTimezone: String,
                                                countOnlySentEmails: Boolean = false,
                                                logger: SRLogger,
                                                teamId: TeamId
                                              ): Try[Map[EmailSettingId, Int]] = blocking {
    Try {

      val srLogger = logger.appendLogRequestId(s"[getSentOrScheduledProspectsCountForEmail]")

      /*
      srDBQueryCounterService.logDBQueryCallFrequency(
        dbQueryName =  "getSentOrScheduledProspectsCountForEmail",
        logger = srLogger
      )
      */

      if (emailSettingIds.nonEmpty) {
        DB readOnly { implicit session =>

          /* 9th JULY 2020: NOTE: DO NOT REMOVE THIS COMMENT

          THIS CHECK IS NO LONGER USED, SO COMMENTED OUT FOR NOW. WE MAY NEED THIS IN FUTURE THOUGH.

          val twentyFourHourLimitEnforceSQL: SQLSyntax = if ((emailAccountDonotEnfore24HourLimitTill.isEmpty || emailAccountDonotEnfore24HourLimitTill.get.isBeforeNow) && emailAccountQuotaPerDay > 250) {

            sqls"""

                OR es.scheduled_at >= now() - interval '24 hours'
                OR es.sent_at >= now() - interval '24 hours'

              """

          } else sqls""

          */



          val q = CampaignProspectDAO.getSentOrScheduledProspectCountQuery(
            emailSettingIds = emailSettingIds,
            accountTimezone = accountTimezone,
            countOnlySentEmails = countOnlySentEmails,
            teamId = teamId
          )


          val res = q
            .map(rs => (EmailSettingId(rs.long("eset_id")) -> rs.int("count")))
            .list
            .apply()
            .toMap

          res
        }
      } else Map()
    }
  }

  //SENDER_ROTATION
  // called from -
  // EmailChannelScheduler.getScheduledProspectsCountForCampaign
  def getScheduledProspectsCountForCampaign(
                                             Logger: SRLogger,
                                             emailSettingIds: Seq[EmailSettingId],
                                             teamId: Long,
                                             campaignIdAndTimezone: Seq[(Long, String)],
                                             countOnlySentProspects: Boolean = false
                                           ): Try[Seq[ScheduledProspectsCountForCampaignEmail]] = if (campaignIdAndTimezone.isEmpty) {

    Logger.fatal(s"getScheduledProspectsCountForCampaign campaignIdAndTimezone.isEmpty: eset_$emailSettingIds")

    Success(Seq())

  } else if (emailSettingIds.isEmpty) {
    Logger.debug(s"getScheduledProspectsCountForCampaign emailSettingIds.isEmpty: eset_$emailSettingIds")

    Success(Seq())
  } else blocking {
    Try {

      campaignIdAndTimezone
        .grouped(5)
        .flatMap(cgroup => {

          /*
          srDBQueryCounterService.logDBQueryCallFrequency(
            dbQueryName =  "getScheduledProspectsCountForCampaign",
            logger = Logger
          )
          */


          Helpers.logTimeTaken(
            Logger = Logger.appendLogRequestId(s"getScheduledProspectsCountForCampaignSQL eset_$emailSettingIds"),
            thresholdInMillis = 250
          ) {

            // 24th August 2021: no need to have localTx anymore because we just removed the sql" SET LOCAL work_mem = '256MB' clause
            DB readOnly { implicit session =>

              // 24th August 2021: removing the LOCAL work_mem set condition temporarily
              // sql" SET LOCAL work_mem = '256MB'; "

              // scheduled either in last 24 hours (for gmail) or today (to limit by day, even though 24 hour limit might be met)
              /*
            SQL(s"""
              select
                count(*) FILTER (WHERE es.step_id = c.head_step_id) as newCount,
            count(*) FILTER (WHERE es.step_id != c.head_step_id) AS followupCount,
            count(*) FILTER (WHERE es.step_id = c.head_step_id AND not es.sent) as newCountNotSent,
            count(*) FILTER (WHERE es.step_id != c.head_step_id AND not es.sent) as followupCountNotSent

            from emails_scheduled es
            inner join campaigns c on c.id = es.campaign_id
            where c.sender_email_settings_id = $emailSettingId

            ${if (campaignId.isDefined) s" AND c.id = ${campaignId.get} " else "" }

            -- AND es.scheduled_at >= date_trunc('day', now())
            AND (
              (CASE WHEN (es.donot_enforce_24_hour_limit_till IS NOT NULL AND es.donot_enforce_24_hour_limit = true) THEN false ELSE (es.scheduled_at >= now() - interval '24 hours') END)
              OR es.scheduled_at >= date_trunc('day', now() AT TIME ZONE c.timezone)
              OR (CASE WHEN (es.donot_enforce_24_hour_limit IS NOT NULL AND es.donot_enforce_24_hour_limit = true) THEN false ELSE ( es.sent_at >= now() - interval '24 hours') END)
              OR es.sent_at >= date_trunc('day', now() AT TIME ZONE c.timezone)
            )
      """)*/

              /* NOTE (DO NOT REMOVE THIS COMMENT): Removed the check:

              ${if ((emailAccountDonotEnfore24HourLimitTill.isEmpty || emailAccountDonotEnfore24HourLimitTill.get.isBeforeNow) && emailAccountQuotaPerDay > 250) s" OR es.scheduled_at >= now() - interval '24 hours' OR es.sent_at >= now() - interval '24 hours' " else " "}

              because a new user's emails were not sending just because they had a max daily limit of 500 on their email account,
              even tho they only sent 75 emails the first day
              */
              val query = CampaignProspectDAO.getScheduledProspectCountForCampaignQuery(
                emailSettingIds = emailSettingIds,
                teamId = teamId,
                campaignIdAndTimezone = cgroup,
                countOnlySentProspects = countOnlySentProspects
              )

              val res = query
                .map(rs => {

                  ScheduledProspectsCountForCampaignEmail(
                    campaignId = rs.long("campaign_id"),
                    newCount = rs.int("newCount"),
                    followupCount = rs.int("followupCount"),
                    newCountNotSent = rs.int("newCountNotSent"),
                    followupCountNotSent = rs.int("followupCountNotSent")
                  )
                })
                .list
                .apply()

              if (res.isEmpty) {

                val campaignIds: Seq[Long] = campaignIdAndTimezone.map(_._1)

                Logger.debug(s"getScheduledProspectsCountForCampaign: nores: campaignIds: $campaignIds :: tid_$teamId :: esetIds_${emailSettingIds.map(_.emailSettingId)} :: countOnlySentProspects: $countOnlySentProspects")

              }

              res
            }
          }
        })
        .toSeq
    }
  }


  def getProspectIdsBelongingToGivenProspectAccounts(

                                                      markAllCompletedFromProspectAccountIds: Seq[Long],

                                                      teamId: Long,

                                                    ): Try[List[Long]] = Try {

    DB.readOnly { implicit session =>
      sql"""
                   select id from prospects p
                   where
                    p.prospect_account_id IN ${SQLUtils.generateSQLValuesClause(markAllCompletedFromProspectAccountIds)}
                    AND p.team_id = $teamId
                 """
        .map(_.long("id"))
        .list
        .apply()
    }

  }

  def _selectCampaignsProspectsDetails(
                                        pauseSpecificCampaignsOnlyOnReply: Boolean,
                                        relevantEmails: Seq[CompletedCampaignProspect],
                                        teamId: Long,
                                        Logger: SRLogger
                                      ): Try[List[SelectedCPForUpdate]] = {

    if (relevantEmails.isEmpty) Success(List()) else {
      Try {
        relevantEmails
          .grouped(100)
          .flatMap(groupedRelevantEmails => {
            var valueParameters = List[Any]()

            val valuePlaceholder: String = groupedRelevantEmails
              .map(e => {
                valueParameters = valueParameters ::: List(
                  e.prospect_id,
                  e.campaign_id.getOrElse(0)
                )

                "(?, ?)"
              })
              .mkString(", ")

            val sub_query = if (pauseSpecificCampaignsOnlyOnReply) s" AND cp.campaign_id = temp.campaign_id " else s" "

            val query =
              s"""
              SELECT
                cp.campaign_id,
                cp.prospect_id,
                cp.team_id,
                c.campaign_type
              FROM campaigns_prospects cp
              INNER JOIN campaigns c ON c.id = cp.campaign_id and c.team_id = cp.team_id
              JOIN (VALUES $valuePlaceholder)
              AS temp(
                prospect_id,
                campaign_id
              ) ON cp.prospect_id = temp.prospect_id
              WHERE
                cp.prospect_id = temp.prospect_id
                AND (cp.paused = false OR cp.completed = false)
                AND c.id = cp.campaign_id
                AND c.team_id = cp.team_id
                ${sub_query}

              """

            DB.readOnly { implicit session =>
              SQL(query)
                .bind(valueParameters *)
                .map(rs => SelectedCPForUpdate(
                  campaign_id = CampaignId(id = rs.long("campaign_id")),
                  prospect_id = ProspectId(rs.long("prospect_id")),
                  team_id = TeamId(rs.long("team_id")),
                  campaign_type = CampaignType.fromString(rs.string("campaign_type")).get
                ))
                .list
                .apply()
            }
          })
          .toList
      }
    }
  }

  // INTERNAL USE
  // This gets called from ReplyTracking code - when a reply is automatically tracked from the prospect,
  // here we are considering different REPLY_HANDLING cases, unlike the sql used for just "Mark as completed" (pauseStatusChangedByAdmin)

  def _markAsCompleted(

                        pauseSpecificCampaignsOnlyOnReply: Boolean,
                        relevantEmails: Seq[CompletedCampaignProspect],
                        replyHandling: ReplyHandling,
                        teamId: Long,
                        completed_reason: CampaignProspectCompletedReason,
                        Logger: SRLogger

                      ): Try[List[CPCompleted]] = Try {

    // DB localTx { implicit session =>



    Logger.info(s"step 7 :: relevantEmails: $relevantEmails")

    if (relevantEmails.isEmpty) List() else {

      relevantEmails
        .grouped(100)
        .flatMap(groupedRelevantEmails => {


          var valueParameters = List[Any]()

          val valuePlaceholder: String = groupedRelevantEmails
            .map(e => {

              valueParameters = valueParameters ::: List(
                e.prospect_id,
                e.campaign_id.getOrElse(0),

                replyHandling.toString,
                e.completed_because_email_message_id.getOrElse(null),

                e.completed_because_secondary_prospect_email_id.getOrElse(null),
                completed_reason.toString
              )

              s"""
              (
                ?,
                ?,
                ?,
                ?,
                ?,
                ?
              )

            """

            })
            .mkString(", ")

          // temp.completed_because_email_message_id::bigint : otherwise throws when messageid is null
          val query =
            s"""
          UPDATE campaigns_prospects cp

          SET
            paused = true,
            paused_at = COALESCE(paused_at, now()),
            completed = TRUE,
            completed_at = COALESCE(completed_at, now()),

            completed_because_reply_handling = temp.reply_handling,
            completed_because_email_message_id = temp.completed_because_email_message_id::bigint,
            completed_because_secondary_prospect_email_id = temp.completed_because_secondary_prospect_email_id::bigint,

            will_resume_at = null,
            unpaused_by_admin = false,
            completed_reason = temp.completed_reason

          FROM (VALUES $valuePlaceholder)
          AS temp(
            prospect_id,
            campaign_id,

            reply_handling,
            completed_because_email_message_id,

            completed_because_secondary_prospect_email_id,

            completed_reason

          )
          WHERE
              cp.prospect_id = temp.prospect_id
              AND (cp.paused = false OR cp.completed = false)

            ${if (pauseSpecificCampaignsOnlyOnReply) s" AND cp.campaign_id = temp.campaign_id " else s" "}

          RETURNING cp.prospect_id, cp.campaign_id, cp.completed
          ;
      """

          //        Logger.info(s"step 8 :: groupedRelevantEmails: $groupedRelevantEmails. :: query: $query :: valueParameters: $valueParameters :: valuePlaceholder: $valuePlaceholder")

          DB.autoCommit { implicit session =>
            SQL(query)
              .bind(valueParameters *)
              .map(rs => CPCompleted(
                prospectId = rs.long("prospect_id"),
                campaignId = rs.long("campaign_id"),
                completed = rs.boolean("completed")
              ))
              .list
              .apply()
          }

        })
        .toList

    }
  }

  def setCampaignToResumeForProspectsAtLaterDate(

                                                  campaignProspects: Seq[CPTuple],
                                                  willResumeAt: DateTime,
                                                  willResumeAtTimezone: String,
                                                  willResumeAtUpdatedBy: WillResumeAtUpdatedBy,
                                                  logger: SRLogger

                                                ): Try[List[Long]] = {

    if (campaignProspects.isEmpty) Success(List())
    else {


      var valueParameters = List[Any]()

      val valuePlaceholder: SQLSyntax = campaignProspects
        .map(e => {

          valueParameters = valueParameters ::: List(

            e.prospect_id,
            e.campaign_id,

            willResumeAtUpdatedBy.toString,
            willResumeAt,
            willResumeAtTimezone
          )

          sqls"""
              (
                ?,
                ?,

                ?,
                ?,
                ?
              )
            """

        })
        .reduce((vp1, vp2) => sqls"$vp1, $vp2")

      DBUtils.autoCommit(
        logger = logger.appendLogRequestId("setCampaignToResumeForProspectsAtLaterDate")
      ) { implicit session =>


        sql"""
          UPDATE campaigns_prospects cp
          SET
            paused = false,
            completed = false,
            completed_at = null,
            completed_reason = null,

            will_resume_at_updated_by = temp.will_resume_at_updated_by,
            will_resume_at = temp.will_resume_at::timestamptz,
            will_resume_at_tz = temp.will_resume_at_tz
          FROM (VALUES $valuePlaceholder)
          AS temp(
               prospect_id,
               campaign_id,

               will_resume_at_updated_by,
               will_resume_at,
               will_resume_at_tz
          )
          WHERE cp.campaign_id = temp.campaign_id
            AND cp.prospect_id = temp.prospect_id
            AND cp.active = true
          RETURNING cp.prospect_id
        """
          .bind(valueParameters *)
          .map(rs => rs.long("prospect_id"))
          .list
          .apply()
      }

    }

  }


  // prospects that need the campaign resumed in the next 10 minutes
  def findProspectsWhichWereSetToResumeLater(

                                              logger: SRLogger

                                            ): Try[List[CPTuple]] = {

    DBUtils.readOnly(
      logger = logger.appendLogRequestId("findProspectsWhichWereSetToResumeLater")
    ) { implicit session =>

      sql"""
          SELECT campaign_id, prospect_id
          FROM campaigns_prospects cp
          WHERE
            will_resume_at is not null
            and will_resume_at < now() + interval '10 minutes'
            AND cp.active = true
            AND NOT cp.completed;
        """
        .map(rs => {
          CPTuple(
            prospect_id = rs.long("prospect_id"),
            campaign_id = rs.long("campaign_id")
          )
        })
        .list
        .apply()
    }

  }

  def restartCampaignForProspectsSetToResumeLater(

                                                   campaignProspects: Seq[CPTuple],
                                                   logger: SRLogger

                                                 ): Try[List[Long]] = {

    if (campaignProspects.isEmpty) Success(List())
    else {


      var valueParameters = List[Any]()

      val valuePlaceholder: SQLSyntax = campaignProspects
        .map(e => {

          valueParameters = valueParameters ::: List(

            e.prospect_id,
            e.campaign_id
          )

          sqls"""
              (
                ?,
                ?
              )
            """

        })
        .reduce((vp1, vp2) => sqls"$vp1, $vp2")

      DBUtils.autoCommit(
        logger = logger.appendLogRequestId("resumeCampaignForProspectAtLaterDate")
      ) { implicit session =>

        sql"""
         UPDATE campaigns_prospects cp
          SET

             unpaused_by_admin = true,
             unpaused_by_admin_at = now(),
             will_resume_at = null,
             auto_reply = false,
             out_of_office = false,
             unpaused_reason = CONCAT ('resumed by FiveMinutelyCronService at ', TO_CHAR(CURRENT_TIMESTAMP, 'YYYY/MM/DD HH24:MM:SS'))


          FROM (VALUES $valuePlaceholder)
          AS temp(
               prospect_id,
               campaign_id
          )
          WHERE cp.campaign_id = temp.campaign_id
            AND cp.prospect_id = temp.prospect_id

          RETURNING cp.campaign_id, cp.prospect_id
        """
          .bind(valueParameters *)
          .map(rs => rs.long("prospect_id"))
          .list
          .apply()
      }

    }

  }


  // INTERNAL USE
  // REF "null::timestamptz": https://stackoverflow.com/questions/12426363/casting-null-type-when-updating-multiple-rows
  // newinbox
  def _updateCampaignProspectStatusOnReplyV3(
                                              emailsToBeUpdated: Seq[UpdateEmailStatusOnReplyV3],
                                              SRLogger: SRLogger
                                            ): Try[Seq[CPCompleted]] = Try {
    DB localTx { implicit session =>

      val Logger = SRLogger.appendLogRequestId(
        appendLogReqId = s"cp._updateCampaignProspectStatusOnReplyV3"
      )
      Logger.info(s"Step 1: updateEmailStatusOnReplyData: $emailsToBeUpdated")

      if (emailsToBeUpdated.isEmpty) List()
      else {

        var valueParameters = List[Any]()

        val valuePlaceholder: String = emailsToBeUpdated
          .map(e => {

            val s = e.emailStatus

            val isReplied = s.isReplied
            val isBounced = s.bouncedData.isDefined
            val isAutoReply = s.isAutoReply
            val isOOFReply = s.isOutOfOfficeReply

            valueParameters = valueParameters ::: List(

              isReplied,
              if (isReplied) e.sentAt else null,

              isBounced,
              if (isBounced) e.sentAt else null,

              isAutoReply,
              if (isAutoReply) e.sentAt else null,

              isOOFReply,
              if (isOOFReply) e.sentAt else null,

              e.prospectId,
              e.campaignId
            )

            s"""
              (
                ?,
                ?,

                ?,
                ?,

                ?,
                ?,

                ?,
                ?,

                ?,
                ?
              )

            """

          })
          .mkString(", ")
        Logger.info(s"Step 3")

        // if any status already true, do not revert it
        SQL(
          s"""
          UPDATE campaigns_prospects cp
          SET

           replied = cp.replied or temp.replied,
           replied_at = COALESCE(cp.replied_at, temp.replied_at::timestamptz),
           next_check_for_scheduling_at = now(),

           bounced = cp.bounced or temp.bounced,
           bounced_at = COALESCE(cp.bounced_at, temp.bounced_at::timestamptz),

           auto_reply = cp.auto_reply or temp.autoreply,
           auto_reply_at = COALESCE(cp.auto_reply_at, temp.autoreply_at::timestamptz),

           out_of_office = cp.out_of_office or temp.oofreply,
           out_of_office_at = COALESCE(cp.out_of_office_at, temp.oofreply_at::timestamptz)

          FROM (VALUES $valuePlaceholder)
          AS temp(
            replied,
            replied_at,

            bounced,
            bounced_at,

            autoreply,
            autoreply_at,

            oofreply,
            oofreply_at,

            prospect_id,
            campaign_id
          )
          WHERE cp.campaign_id = temp.campaign_id
            AND cp.prospect_id = temp.prospect_id

          RETURNING cp.campaign_id, cp.prospect_id, cp.completed
        """)
          .bind(valueParameters *)
          .map(rs => CPCompleted(
            prospectId = rs.long("prospect_id"),
            campaignId = rs.long("campaign_id"),
            completed = rs.boolean("completed")
          ))
          .list
          .apply()
      }
    }
  }

  def pauseUnpauseCampaignProspect(
                                  campaignIds: Seq[Long],
                                  accountOwnedProspects: Seq[Long],
                                  newPauseStatus: Boolean,
                                  Logger: SRLogger
                                  ): Try[Seq[CPCompleted]] = Try {
    val campaignIdsCheck = if (campaignIds.isEmpty) sqls"" else sqls" AND cp.campaign_id IN ${SQLUtils.generateSQLValuesClause(campaignIds.distinct)} "

    if (!newPauseStatus) {
      // unpausing

      DB autoCommit { implicit session =>

        sql"""
          UPDATE campaigns_prospects cp
          SET
            unpaused_by_admin = true,
            unpaused_by_admin_at = now(),
            paused = false,
            paused_at = null,
            completed = false,
            completed_reason = null,
            completed_at = null,
            will_resume_at = null,
            will_resume_at_tz = null
          WHERE cp.prospect_id IN ${SQLUtils.generateSQLValuesClause(accountOwnedProspects)}
           AND (cp.completed OR cp.paused OR (cp.will_resume_at IS NOT NULL))
           AND cp.active
           $campaignIdsCheck
          RETURNING prospect_id, campaign_id, completed
        """
          .map(rs => CPCompleted(
            prospectId = rs.long("prospect_id"),
            campaignId = rs.long("campaign_id"),
            completed = rs.boolean("completed")
          ))
          .list
          .apply()

      }

    } else {
      // pausing

      DB autoCommit { implicit session =>

        // allow pausing even if cp.active = false (below), but do not allow unpausing if cp.active = false (above)
        sql"""
          UPDATE campaigns_prospects cp
          SET
            will_resume_at = null,
            unpaused_by_admin = false,
            unpaused_by_admin_at = null,
            paused = true,
            paused_at = now(),
            completed = true,
            completed_at = now(),
            completed_reason = ${CampaignProspectCompletedReason.PauseStatusChangedByAdmin.toString}
          WHERE cp.prospect_id IN ${SQLUtils.generateSQLValuesClause(accountOwnedProspects)}
            AND (cp.unpaused_by_admin = true OR cp.completed = false OR cp.paused = false)
            --AND cp.active
            $campaignIdsCheck
          RETURNING prospect_id, campaign_id, completed
        """
          .map(rs => CPCompleted(
            prospectId = rs.long("prospect_id"),
            campaignId = rs.long("campaign_id"),
            completed = rs.boolean("completed")
          ))
          .list
          .apply()

      }
    }
  }

  // NOT NEEDED: update replied status if its not already marked as replied
  def changeProspectStatusByAdmin(adminAccountId: Long,
                                  prospectsToStatusUpdated: Seq[CPStatusUpdateEvent],
                                  newStatus: NewProspectStatus
                                 ): Try[Seq[CPTuple]] = Try {
    DB autoCommit { implicit session =>

      if (prospectsToStatusUpdated.isEmpty) List()
      else {

        var isReplied = false

        val updateColumns = newStatus match {

          case NewProspectStatus.REPLIED =>

            isReplied = true

            sqls"""
                  replied = cp.replied or temp.replied,
                  next_check_for_scheduling_at = now(),
                  replied_at = COALESCE(cp.replied_at, temp.replied_at::timestamptz),
                  replied_marked_by_adminid = temp.replied_marked_by_adminid
              """

          case NewProspectStatus.PAUSE
               | NewProspectStatus.UNPAUSE
               | NewProspectStatus.RESUME_LATER =>

            throw new Exception(s"Invalid ProspectStatus: $newStatus")

        }

        var valueParameters = List[Any]()

        val valuePlaceholder: SQLSyntax = prospectsToStatusUpdated
          .map(e => {

            valueParameters = valueParameters ::: List(

              isReplied,
              if (isReplied) e.event_at else null,
              adminAccountId,

              e.prospect_id,
              e.campaign_id
            )

            sqls"""
              (
                ?,
                ?,
                ?,

                ?,
                ?
              )

            """

          })
          .reduce((vp1, vp2) => sqls"$vp1, $vp2")

        // if any status already true, do not revert it
        sql"""
          UPDATE campaigns_prospects cp
          SET

             $updateColumns

          FROM (VALUES $valuePlaceholder)
          AS temp(
               replied,
               replied_at,
               replied_marked_by_adminid,

               prospect_id,
               campaign_id
          )
          WHERE cp.campaign_id = temp.campaign_id
            AND cp.prospect_id = temp.prospect_id

          RETURNING cp.campaign_id, cp.prospect_id
        """
          .bind(valueParameters *)
          .map(rs => {

            CPTuple(
              prospect_id = rs.long("prospect_id"),
              campaign_id = rs.long("campaign_id")
            )

          })
          .list
          .apply()
      }

    }
  }


  // FIXME: SQL INJECTION (field name)
  def addMissingFieldsErrorMessage(campaignId: Long, prospectId: Long, fields: Seq[String]): Try[Int] = blocking {
    Try {
      DB.autoCommit { implicit session =>

        val toCheckFields = s"'{${fields.mkString(",")}}'"

        val query =
          s"""
          UPDATE campaigns_prospects
          SET
            to_check = TRUE,
            to_check_at = now(),
            to_check_fields = $toCheckFields::text[]

          WHERE campaign_id = $campaignId AND prospect_id = $prospectId
          ;
      """

        //      Logger.info(s"CampaignProspect.addMissingErrorMessage: $toCheckFields :: $query")

        SQL(query)
          .update
          .apply()

      }

    }
  }


  // on updating campaign step, update to_check status to false for relevant prospects
  def _updateProspectCategory(
                               prospectIds: Seq[Long],
                               prospectCategoryIdCustom: ProspectCategoryId,
                               teamId: TeamId
                             )(implicit session: DBSession): Int = {

    sql"""
          UPDATE campaigns_prospects
          SET prospect_category_id_custom = ${prospectCategoryIdCustom.id}
          WHERE prospect_id IN ${SQLUtils.generateSQLValuesClause(prospectIds)}
            AND team_id = ${teamId.id}
          ;
      """
      .update
      .apply()

  }

  def _revertCompletedStatusOnNewCampaignStepAddition(campaignId: Long): Int = {
    DB localTx { implicit session =>

      sql"""
          UPDATE campaigns_prospects
          SET
            paused = false,
            paused_at = null,
            completed = false,
            completed_at = null,
            completed_reason = null

          WHERE campaign_id = $campaignId
          AND active = true
          AND completed = true

          AND bounced = false
          AND replied = false
          AND opted_out = false
          AND out_of_office = false
          AND auto_reply = false
        """
        .update
        .apply()
    }
  }

  def getprospectIdsAlreadyAssignedToACampaign(
                                                prospectIds: List[Long],
                                                campaignId: Long,
                                                team_id: TeamId
                                              ): List[Long] = {

    prospectIds
      .grouped(500)
      .flatMap(groupedPids => {

        DB readOnly { implicit session =>
          sql"""
              SELECT cp.prospect_id
              FROM campaigns_prospects cp
              WHERE cp.prospect_id IN ${SQLUtils.generateSQLValuesClause(groupedPids)}
              AND cp.campaign_id = $campaignId
              AND cp.team_id = ${team_id.id}
              AND cp.active
            """
            .map(rs => rs.long("prospect_id"))
            .list
            .apply()
            .distinct
        }
      })
      .toList

  }

  def getCampaignsIdWhereProspectActiveInTheCampaignForDrip(
                                                             prospectId: Long,
                                                             campaignIds: Seq[Long],
                                                             team_id: TeamId
                                                           ): Seq[Long] = {

    DB readOnly { implicit session =>
      sql"""
              SELECT cp.campaign_id
              FROM campaigns_prospects cp
              WHERE cp.prospect_id = $prospectId
              AND cp.campaign_id IN ${SQLUtils.generateSQLValuesClause(campaignIds)}
              AND cp.team_id = ${team_id.id}
              AND cp.active
            """
        .map(rs => rs.long("campaign_id"))
        .list
        .apply()
        .distinct
    }

  }

  def pauseOtherDripCampaignsForProspect(
                                          team_id: Long,
                                          prospectId: Long,
                                          campaignIds: Seq[Long]
                                        ): Int = {

    DB autoCommit { implicit session =>
      sql"""
          UPDATE campaigns_prospects cp SET
            active = FALSE
          FROM campaigns c
          WHERE cp.campaign_id IN ${SQLUtils.generateSQLValuesClause(campaignIds)}
            AND cp.prospect_id = $prospectId
            AND cp.active
            AND c.team_id = $team_id
      """
        .update.apply()
    }

  }

  //TODO: optimise the query to reduce the search space to within the org/team
  def getProspectsActiveInOtherCampaigns(
                                          prospectIds: List[Long],
                                          campaignId: Long,
                                          teamId: TeamId
                                        ): List[Long] = {

    prospectIds
      .grouped(500)
      .flatMap(groupedPids => {


        DB readOnly { implicit session =>
          sql"""
              SELECT prospect_id
              FROM campaigns_prospects cp

              WHERE prospect_id IN ${SQLUtils.generateSQLValuesClause(groupedPids)}

              AND cp.active = true
              AND (
               cp.unpaused_by_admin = true

               OR (

                cp.replied = false
                AND cp.opted_out = false
                AND cp.out_of_office = false
                AND cp.auto_reply = false
                AND cp.bounced = false
                AND cp.completed = false
                AND cp.paused = false
                AND cp.will_resume_at is null
               )
              )
              -- this is a redundant check, we are adding it for clarity
              AND cp.campaign_id != $campaignId
              AND cp.team_id = ${teamId.id}
            ;
            """
            .map(rs => rs.long("prospect_id"))
            .list
            .apply()
        }
      })
      .toList

  }


  def getProspectsAddedInOtherCampaigns(
                                         prospectIds: List[Long],
                                         campaignId: Long,
                                         teamId: TeamId
                                       ): List[Long] = {
    prospectIds
      .grouped(500)
      .flatMap(groupedPids => {

        DB readOnly { implicit session =>
          sql"""
              SELECT prospect_id
              FROM campaigns_prospects cp

              WHERE prospect_id IN ${SQLUtils.generateSQLValuesClause(groupedPids)}
              AND cp.active
              -- this is a redundant check, we are adding it for clarity
              AND cp.campaign_id != $campaignId
              AND cp.team_id = ${teamId.id}
            ;
            """
            .map(rs => rs.long("prospect_id"))
            .list
            .apply()
        }
      })
      .toList
  }

  def getOldStepIdForCampaign(
                               prospectIds: Seq[ProspectId],
                               campaignId: CampaignId,
                               teamId: TeamId
                             ): List[(StepId, ProspectId, DateTime)] = {
    if (prospectIds.isEmpty) {
      List()
    } else {
      DB readOnly { implicit session =>
        sql"""
             select
                es.step_id,
                es.prospect_id,
                es.sent_at
             from emails_scheduled es
             where es.prospect_id IN ${SQLUtils.generateSQLValuesClause(prospectIds.map(_.id).distinct)}
                   and es.campaign_id =  ${campaignId.id}
                   and es.scheduled_from_campaign
                   and es.sent
                   and es.step_id is not null
                   and es.team_id = ${teamId.id};
           """
          .map(rs => (StepId(rs.long("step_id")), ProspectId(rs.long("prospect_id")), rs.jodaDateTime("sent_at")))
          .list
          .apply()
          .groupBy(_._2.id)
          .map { data =>

            data._2.maxBy(_._3)

          }.toList
      }
    }
  }

  def assignProspectsToCampaign(
                                 pgroup: List[OwnedProspectsForAssigning],
                                 campaignId: Long,
                                 teamId: TeamId
                               ): List[Long] = {

    var cpParameters = List[Any]()

    val cpPlaceholder: String = pgroup
      .map(p => {


        // 1-Nov-2020: earlier it was setting now() as default value for invalid_email_at and bounced_at, even if not bounced, it was affecting the activity timeline
        val invalidEmailAt = if (p.invalid_email.isDefined && p.invalid_email.get) p.email_bounced_at.getOrElse(org.joda.time.DateTime.now()) else null
        val emailBouncedAt = if (p.email_bounced.isDefined && p.email_bounced.get) p.email_bounced_at.getOrElse(org.joda.time.DateTime.now()) else null

        cpParameters = cpParameters ::: List(

          campaignId,
          p.prospect_id,
          p.invalid_email.getOrElse(false), //for non-email prospect it is none and in db default value is false
          invalidEmailAt,
          p.email_bounced.getOrElse(false), //for non-email prospect it is none and in db default value is false
          emailBouncedAt,
          p.prospect_category_id_custom,
          true,
          p.synced_previously_sent_step_for_deleted_prospect_step_id,

          teamId.id
        )

        s"""
                  (
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,

                    ?
                  )
                  """
      })
      .mkString(", ")

    // on conflict update active
    val assignedProspectIds: List[Long] = DB.autoCommit { implicit session =>

      SQL(
        s"""
                  INSERT INTO campaigns_prospects
                  (
                    campaign_id,
                    prospect_id,
                    invalid_email,
                    invalid_email_at,
                    bounced,
                    bounced_at,
                    prospect_category_id_custom,
                    active,
                    current_step_id,

                    team_id
                  )

                  VALUES $cpPlaceholder

                  ON CONFLICT(campaign_id, prospect_id) DO UPDATE SET active = TRUE

                  RETURNING prospect_id
                """)
        .bind(cpParameters *)
        .map(rs => {
          rs.long("prospect_id")
        })
        .list
        .apply()


    }

    assignedProspectIds


  }

  def addReplySentiment(
                         prospectThread: CampaignProspectThread,
                         reply_sentiment_uuid: ReplySentimentUuid,
                         teamId: TeamId,
                         reply_sentiment_updated_by: ReplySentimentUpdatedBy
                       )(implicit session: DBSession): Try[List[Long]] = Try {
    if (prospectThread.prospect_id.isEmpty) {
      List()
    } else {
      val whereClause =
        sqls"""
          WHERE cp.campaign_id = ${prospectThread.campaign_id}
                    AND cp.prospect_id  IN ${SQLUtils.generateSQLValuesClause(prospectThread.prospect_id)}
          """.stripMargin

      CampaignProspectDAO.getAddReplySentimentQuery(
          reply_sentiment_uuid = reply_sentiment_uuid,
          whereClause = whereClause,
          teamId = teamId,
          reply_sentiment_updated_by = reply_sentiment_updated_by
        )
        .map(rs => rs.long("prospect_id"))
        .list
        .apply()
    }
  }

  def addReplySentimentViaTask(
                                campaignId: CampaignId,
                                reply_sentiment_uuid: ReplySentimentUuid,
                                prospect_id: ProspectId,
                                teamId: TeamId,
                                reply_sentiment_updated_by: ReplySentimentUpdatedBy
                              )(implicit session: DBSession): Try[Option[Long]] = Try {
    val whereClause =
      sqls"""
        WHERE cp.campaign_id = ${campaignId.id}
              AND cp.prospect_id = ${prospect_id.id}
        """

    CampaignProspectDAO.getAddReplySentimentQuery(
        reply_sentiment_uuid = reply_sentiment_uuid,
        whereClause = whereClause,
        teamId = teamId,
        reply_sentiment_updated_by = reply_sentiment_updated_by
      ).map(rs => rs.long("prospect_id"))
      .single
      .apply()
  }

  def getCampaignsWhoseReplyWillBeTrackedInThisMessageThread(
                                                              messageThreadId: LinkedinMessageThreadId,
                                                              teamId: TeamId
                                                            ): Try[List[CampaignId]] = Try {

    val excludedStepTypes: List[String] = List(CampaignStepType.AutoLinkedinViewProfile.toKey, CampaignStepType.AutoLinkedinConnectionRequest.toKey)

    DB readOnly { implicit session =>
      sql"""
           SELECT cp.campaign_id FROM campaigns_prospects cp
           INNER JOIN linkedin_message_threads lmt ON lmt.prospect_id = cp.prospect_id AND lmt.team_id = cp.team_id
           INNER JOIN campaign_channel_settings ccs ON ccs.campaign_id = cp.campaign_id AND ccs.team_id = cp.team_id
           INNER JOIN linkedin_settings ls ON ls.id = lmt.linkedin_setting_id AND ls.team_id = lmt.team_id
           INNER JOIN campaign_steps cs ON cs.id = cp.current_step_id AND cs.campaign_id = cp.campaign_id
           WHERE lmt.id = ${messageThreadId.id}
           AND cp.team_id = ${teamId.id}
           AND ls.uuid = ccs.channel_settings_uuid
           AND cs.step_type NOT IN ${SQLUtils.generateSQLValuesClause(excludedStepTypes)}
           ;
           """
        .map(rs => CampaignId(rs.long("campaign_id")))
        .list
        .apply()
    }

  }

  def markCampaignCompletedForProspectDrip(
                                            prospect_id: ProspectId,
                                            team_id: TeamId,
                                            campaign_id: CampaignId,
                                          ): Try[Option[Long]] = Try {

    DB.autoCommit(implicit session =>

      sql"""
          UPDATE campaigns_prospects
            SET
              paused = true,
              paused_at = now(),
              completed = true,
              completed_at = now(),
              completed_reason = ${CampaignProspectCompletedReason.MoveToAnotherCampaignDripActivity.toString}

          WHERE campaign_id = ${campaign_id.id}
          AND team_id = ${team_id.id}
          AND prospect_id = ${prospect_id.id}

          RETURNING campaign_id
       """
        .map(res => res.long("campaign_id"))
        .single
        .apply()
    )

  }

  def markCampaignAsRepliedAndCompleted(
                                         campaign_ids: Seq[Long],
                                         messageSentAtByProspect: MessageSentAtByProspect,
                                         completed_because_secondary_prospect_email_id: Option[Long]
                                       )(implicit session: DBSession): Try[List[Long]] = Try {

    val messageIdUpdate = messageSentAtByProspect match {
      case data: MessageSentAtByProspect.EmailSentAtByProspect =>
        sqls"""
              completed_because_email_message_id = ${data.email_scheduled_id},
              completed_because_secondary_prospect_email_id = $completed_because_secondary_prospect_email_id
              """

      case data: MessageSentAtByProspect.LinkedinMessageSentAtByProspect =>
        sqls"""
              completed_because_linkedin_message_id = ${data.linkedin_message_id.id.toString}
              """
    }

    if (campaign_ids.isEmpty) {
      List()
    } else {

      sql"""
          UPDATE campaigns_prospects cp

            SET
              paused = true,
              paused_at = now(),

              replied = true,
              replied_at = ${messageSentAtByProspect.sent_at},
              next_check_for_scheduling_at = now(),

              completed = true,
              completed_at = now(),
              completed_reason = ${CampaignProspectCompletedReason.MarkCampaignAsRepliedAndCompleted.toString},

              $messageIdUpdate

          WHERE cp.campaign_id in ${SQLUtils.generateSQLValuesClause(campaign_ids)}
          AND cp.prospect_id = ${messageSentAtByProspect.prospect_id.id}
          AND NOT cp.replied

          RETURNING cp.campaign_id;

       """
        .map(_.long("campaign_id"))
        .list
        .apply()
    }
  }

  def checkIfCampaignProspectUpdated(
                                      current_step_task_id: String,
                                      current_step_id: StepId,
                                      campaignId: CampaignId,
                                      prospectId: ProspectId,
                                      teamId: TeamId
                                    )(implicit session: DBSession): Try[Option[CurrentStepDetailsRegardingDuplicate]] = Try {

    sql"""
         SELECT
            current_step_status_for_scheduler,
            current_step_task_id

         FROM campaigns_prospects
         WHERE
            campaign_id = ${campaignId.id}
         AND prospect_id = ${prospectId.id}
         AND current_step_id = ${current_step_id.id}
         AND current_step_task_id = ${current_step_task_id}
         AND team_id = ${teamId.id}

       """
      .map(rs => CurrentStepDetailsRegardingDuplicate(
        currentStepStatusForScheduler = CurrentStepStatusForScheduler.fromString(rs.string("current_step_status_for_scheduler")).get,
        current_step_task_id = rs.string("current_step_task_id")
      ))
      .single
      .apply()

  }

  def getCampaignProspectDataForRevert(
                                        campaignId: CampaignId,
                                        prospectId: ProspectId,
                                        teamId: TeamId
                                      )(implicit session: DBSession): Try[CampaignProspectRevertData] = Try {

    sql"""
         SELECT
            current_step_id,
            current_step_status_for_scheduler

         FROM campaigns_prospects
         WHERE
            campaign_id = ${campaignId.id}
           AND prospect_id = ${prospectId.id}
           AND team_id = ${teamId.id}
       """
      .map(rs => CampaignProspectRevertData(
        current_step_id = rs.longOpt("current_step_id").flatMap(id => Some(StepId(id = id))),
        current_step_status_for_scheduler = rs.stringOpt("current_step_status_for_scheduler").flatMap(status => Some(CurrentStepStatusForScheduler.fromString(status).get)),
        prospect_id = prospectId,
        campaignId = campaignId,
        team_id = teamId
      ))
      .single
      .apply()
      .getOrElse(
        CampaignProspectRevertData(
          current_step_id = None,
          current_step_status_for_scheduler = None,
          prospect_id = prospectId,
          campaignId = campaignId,
          team_id = teamId
        )
      )

  }

  def findCampaignStepDataForProspect(
                                       current_step_id: CurrentStepId,
                                       team_id: TeamId
                                     ): Try[Option[CampaignStepRelatedDataForProspect]] = Try {

    DB readOnly { implicit session =>

      sql"""

        SELECT
            c.id as campaign_id,
            c.name as campaign_name,
            sp.label as step_label

         FROM campaign_steps sp
         INNER JOIN campaigns c ON ( sp.campaign_id = c.id AND c.team_id = ${team_id.id})

         WHERE
             sp.id = ${current_step_id.step_id}
             AND
             c.team_id = ${team_id.id}

         LIMIT 1

         """
        .map(rs => {

          CampaignStepRelatedDataForProspect(
            campaign_name = CampaignName(name = rs.string("campaign_name")),
            campaign_id = CampaignId(id = rs.long("campaign_id")),
            current_step_id = current_step_id,
            step_label = StepLabel(label = rs.string("step_label"))
          )

        })
        .single
        .apply()

    }
  }

  def updateInternalNotesForCampaignProspect(
                                              team_id: TeamId,
                                              campaign_id: CampaignId,
                                              prospect_id: ProspectId,
                                              internal_notes: String

                                            )(using Logger: SRLogger): Try[Option[String]] = Try {

    DB.autoCommit(implicit session => {

      sql"""

           UPDATE campaigns_prospects
              SET
                  internal_notes = ${internal_notes}
              WHERE
                  team_id = ${team_id.id}
                  AND campaign_id = ${campaign_id.id}
                  AND prospect_id = ${prospect_id.id}

           RETURNING internal_notes

         """
        .map(rs => rs.string("internal_notes"))
        .single
        .apply()

    })

  }

  def hasBeenAddedToACampaign(
                               prospect_id: ProspectId,
                               team_id: TeamId
                             ): Try[Boolean] = Try {

    DB.readOnly(implicit session => {

      sql"""
           SELECT EXISTS (

             SELECT campaign_id
             FROM campaigns_prospects
             WHERE prospect_id = ${prospect_id.id} AND
             team_id = ${team_id.id}
             LIMIT 1

           )

         """
        .map(rs => rs.boolean("exists"))
        .single
        .apply()
        .getOrElse(false)

    })

  }

  /**
   * What it does: Fetches the count of prospects in a campaign that are either pending approval or have AI content queued.
   * Why it is like this: This count is needed to determine if there are prospects waiting for user action (approval) or system action (AI content generation).
   * @param campaignId The ID of the campaign.
   * @param teamId The ID of the team.
   * @param Logger The logger instance.
   * @return Try[Int] The count of prospects.
   */
  def getPendingApprovalOrQueuedCountForCampaign(
                                           campaignId: CampaignId,
                                           campaignStepType: CampaignStepType,
                                           teamId: TeamId
                                         )(using Logger: SRLogger): Try[Int] = Try {
    DB readOnly { implicit session =>
      sql"""
        SELECT COUNT(*) as count
        FROM campaigns_prospects cp
        WHERE cp.campaign_id = ${campaignId.id}
        AND cp.team_id = ${teamId.id}
        AND cp.active = true
        AND cp.completed = false
        AND cp.current_step_status_for_scheduler IN (${CurrentStepStatusForScheduler.PendingApproval.toKey}, ${CurrentStepStatusForScheduler.AiContentQueued.toKey})
        AND cp.current_step_type = ${campaignStepType.toKey}
      """
        .map(rs => rs.int("count"))
        .single
        .apply()
        .getOrElse(0)
    }
  }
}

case class CurrentStepDetailsRegardingDuplicate(
                                                 currentStepStatusForScheduler: CurrentStepStatusForScheduler,
                                                 current_step_task_id: String
                                               )
