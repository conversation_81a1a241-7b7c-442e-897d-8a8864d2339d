package api.campaigns.dao

import api.accounts.TeamId
import api.campaigns.models.{CampaignAnalysisFor, CampaignPauseReason, LowCampaignSendingVolumeReason}
import api.campaigns.services.CampaignId
import org.joda.time.DateTime
import scalikejdbc.jodatime.JodaWrappedResultSet.fromWrappedResultSetToJodaWrappedResultSet
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import utils.cronjobs.sender_volume_alert.model.{CampaignSendingVolumeLogs, CampaignSendingVolumeLogsForReport}

import scala.util.Try

class CampaignSendingVolumeLogsDAO {


  def addCampaignSendingVolumeLog(
    campaignSendingVolumeLogs: CampaignSendingVolumeLogs,
    campaignAnalysisFor: CampaignAnalysisFor,
  ): Try[Int] = Try {
    DB autoCommit {implicit session =>
      sql"""
          INSERT INTO campaign_sending_volume_logs
          (
            campaign_id,
            team_id,

            date_in_campaign_timezone,
            campaign_timezone,
            consecutive_delay_in_seconds,

            warmup_is_on,
            warmup_started_at,
            warmup_starting_email_count,

            analysis_for,

            campaign_start_time,
            pushed_for_checking_at_minutes_since_campaign_start_time,
            actual_minutes_since_campaign_start_time_during_computing,

            current_sent_count,
            expected_sent_count_till_now,
            current_sent_percent,

            total_scheduled_for_today_till_now,
            possible_issue_if_any
        )
          VALUES (
            ${campaignSendingVolumeLogs.campaign_id.id},
            ${campaignSendingVolumeLogs.team_id.id},

            ${campaignSendingVolumeLogs.date_in_campaign_timezone},
            ${campaignSendingVolumeLogs.campaign_timezone},
            ${campaignSendingVolumeLogs.consecutive_delay_in_seconds},

            ${campaignSendingVolumeLogs.warmup_is_on},
            ${campaignSendingVolumeLogs.warmup_started_at},
            ${campaignSendingVolumeLogs.warmup_starting_email_count},

            ${campaignAnalysisFor.toString},

            ${campaignSendingVolumeLogs.campaign_start_time},
            ${campaignSendingVolumeLogs.pushed_for_checking_at_minutes_since_campaign_start_time},
            ${campaignSendingVolumeLogs.actual_minutes_since_campaign_start_time_during_computing},

            ${campaignSendingVolumeLogs.current_sent_count},
            ${campaignSendingVolumeLogs.expected_sent_count_till_now},
            ${campaignSendingVolumeLogs.current_sent_percent},

            ${campaignSendingVolumeLogs.total_scheduled_for_today_till_now},
            ${campaignSendingVolumeLogs.possible_issue_if_any.map(_.toKey())}
          )
          ON CONFLICT DO NOTHING
         """
        .update
        .apply()
    }
  }


  def getCampaignReportForStuckCampaignsForLast15Mins: Try[List[CampaignSendingVolumeLogsForReport]] = Try{
    DB readOnly{ implicit session =>
      sql"""
         SELECT
             a.email as login_email,
             t.name as team_name,
             c.name as campaign_name,
             csvl.team_id,
             csvl.campaign_id,
             csvl.campaign_start_time,
             csvl.current_sent_count,
             csvl.expected_sent_count_till_now,
             csvl.total_scheduled_for_today_till_now,
             csvl.possible_issue_if_any
         FROM campaign_sending_volume_logs csvl
         INNER JOIN campaigns c on c.id = csvl.campaign_id and c.team_id = csvl.team_id
         INNER JOIN teams t on t.id = c.team_id
         INNER JOIN accounts a on a.id = c.account_id and t.org_id = a.org_id
         WHERE possible_issue_if_any is not null
         and csvl.analysis_for = ${CampaignAnalysisFor.SendingVolume.toString}
         and added_at > now() - interval '20 minutes'
         """
        .map{rs =>
          CampaignSendingVolumeLogsForReport(
            campaign_name = rs.string("campaign_name"),
            team_name = rs.string("team_name"),
            login_email = rs.string("login_email"),
            campaign_start_time = rs.jodaDateTime("campaign_start_time"),
            current_sent_count = rs.int("current_sent_count"),
            expected_sent_count_till_now = rs.int("expected_sent_count_till_now"),
            total_scheduled_for_today_till_now = rs.int("total_scheduled_for_today_till_now"),
            possible_issue_if_any = rs.stringOpt("possible_issue_if_any").map(v => LowCampaignSendingVolumeReason.fromKey(v).get),
            campaign_id = CampaignId(rs.long("campaign_id")),
            team_id = TeamId(rs.long("team_id"))
          )
        }
        .list
        .apply()
    }
  }

}
