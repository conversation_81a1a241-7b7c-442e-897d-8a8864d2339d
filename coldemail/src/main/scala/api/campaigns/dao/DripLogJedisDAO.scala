package api.campaigns.dao

import api.campaigns.services.CampaignId
import api.{AppConfig, CacheServiceJedis}
import utils.SRLogger

import scala.util.{Failure, Success, Try}

class DripLogJedisDAO  (
                         cacheServiceJedis: CacheServiceJedis
                       ) {

  val key = s"${AppConfig.redisKeyPrefix}_drip_log_list"
  def getCampaignsToLogDripFor()(using Logger: SRLogger): List[CampaignId] = {
    cacheServiceJedis.get[List[Long]](
      key = key
    ) match {
      case Success(None) => List()
      case Success(Some(list)) => list.map(cid => CampaignId(id = cid))
      case Failure(exception) =>
        Logger.shouldNeverHappen(s"failed getCampaignsToLogDripFor", Some(exception))
        List()
    }

  }

}
