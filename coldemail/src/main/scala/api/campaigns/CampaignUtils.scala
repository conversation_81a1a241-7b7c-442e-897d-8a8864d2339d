package api.campaigns

import api.APIErrorResponse.ErrorResponseV3
import api.{ApiResponseModuleForPermissionedApis, ApiVersion, ErrorType}
import api.accounts._
import api.campaigns.models.GetCampaignIdFromSrIdentifierError
import api.campaigns.services.{CampaignId, CampaignService, CampaignUuid}
import play.api.mvc.{ActionRefiner, Request, Result, WrappedRequest}
import utils.SRLogger
import utils.uuid.services.SrUuidService
import utils.uuid.{SrId, SrIdentifier, SrUuid}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}


case class CampaignRequest[A](
  campaign: Campaign,
  actingTeamAccount: TeamMember,
  loggedinAccount: Account,
  isApiCall: Boolean,
  permittedAccountIds: Seq[Long],
  Logger: SRLogger,
  Response: ApiResponseModuleForPermissionedApis,
  request: PermissionRequest[A]
) extends WrappedRequest[A](request)

case class OptionalCampaignRequest[A](
   campaign: Option[Campaign],
   actingTeamAccount: Option[TeamMember],
   loggedinAccount: Account,
   isApiCall: Boolean,
   permittedAccountIds: Seq[Long],
   Logger: SRLogger,
   Response: ApiResponseModuleForPermissionedApis,
   request: PermissionRequest[A]
 ) extends WrappedRequest[A](request)


case class CampaignStepRequest[A](
  campaign: Campaign,
  step: CampaignStep,
  actingTeamAccount: TeamMember,
  loggedinAccount: Account,
  isApiCall: Boolean,
  permittedAccountIds: Seq[Long],
  Logger: SRLogger,
  Response: ApiResponseModuleForPermissionedApis,
  request: Request[A]
) extends WrappedRequest[A](request)


case class CampaignStepVariantRequest[A](
  campaign: Campaign,
  step: CampaignStep,
  variant: CampaignStepVariant,
  actingTeamAccount: TeamMember,
  loggedinAccount: Account,
  isApiCall: Boolean,
  permittedAccountIds: Seq[Long],
  Logger: SRLogger,
  Response: ApiResponseModuleForPermissionedApis,
  request: Request[A]
) extends WrappedRequest[A](request)


trait CampaignUtils {
  protected val campaignService: CampaignService
  protected val campaignStepDAO: CampaignStepDAO
  protected val campaignStepVariantDAO: CampaignStepVariantDAO
  protected val srUuidService: SrUuidService

  private final def checkHasCampaign[A](
                                         Res: ApiResponseModuleForPermissionedApis,
                                         request: PermissionRequest[A],
                                         id: Long,
                                       )(implicit apiVersion: ApiVersion = ApiVersion.V2): Either[Result, CampaignRequest[A]] = {
    if (request.actingTeamAccount.isEmpty) {
      Left(Res.ForbiddenError("Please select a team first"))
    }
    else {

      val teamId = TeamId(request.actingTeamAccount.get.team_id)
      campaignService.findCampaignForCampaignUtilsOnly(id = id, teamId = teamId) match {

        case None => Left(Res.NotFoundError("Campaign not found"))

        case Some(campaign) =>

          if (!request.permittedAccountIds.contains(campaign.account_id)) {
            Left(Res.ForbiddenError("You do not have the permission to access this campaign"))
          } else if (request.actingTeamAccount.get.team_id != campaign.team_id) {
            Left(Res.ForbiddenError("Campaign does not belong to the current team. Please switch view to the respective team."))
          } else {
            Right(
              CampaignRequest(
                campaign = campaign,
                actingTeamAccount = request.actingTeamAccount.get,
                loggedinAccount = request.loggedinAccount,
                isApiCall = request.isApiCall,
                permittedAccountIds = request.permittedAccountIds,
                Logger = request.Logger,
                Response = request.Response,
                request
              )
            )
          }

      }
    }


  }

  final def hasCampaignWithUuid(v: ApiVersion,campaignUuid: CampaignUuid)(
    implicit ec: ExecutionContext
  ) = new ActionRefiner[PermissionRequest, CampaignRequest] {

    override protected def executionContext: ExecutionContext = ec

    override def refine[A](request: PermissionRequest[A]): Future[Either[Result, CampaignRequest[A]]] = {

      Future {

        val Logger = request.Logger
        val Res = request.Response
        given logger: SRLogger= request.Logger
        implicit val version = v

        if (request.actingTeamAccount.isEmpty) {
          Left(Res.ForbiddenError("Please select a team first"))
        } else {
          srUuidService.getCampaignIdFromUuid(
            campaignUuid = List(campaignUuid),
            teamId = TeamId(request.actingTeamAccount.get.team_id)
          ) match {
            case Failure(exception) =>
              Left(Res.ServerError("There was an error. Please contact support", e = Some (exception) ) )
            case Success(campaignUuidToIdMap) =>
              val campaignUuidToIdMapHeadOpt: Option[(CampaignUuid, Option[CampaignId])] = campaignUuidToIdMap.headOption

              val campaignIdOpt: Option[CampaignId] = campaignUuidToIdMapHeadOpt.flatMap(_._2)

              campaignIdOpt match {
                case None =>
                  Left(
                    Res.BadRequestError(
                      message = "campaign_id does no exist in the team",
                      errorResponse = List(
                        ErrorResponseV3(
                          message = "campaign_id does no exist in the team",
                          error_type = ErrorType.BAD_REQUEST
                        )
                      ),
                      version = v
                    )
                  )

                case Some(campaignId: CampaignId) =>
                  checkHasCampaign(
                    Res = Res,
                    request = request,
                    id = campaignId.id
                  )
              }
          }
        }
      }

    }
  }

  final def hasCampaign(campaignId: Long)(
                       implicit ec: ExecutionContext
  ) = new ActionRefiner[PermissionRequest, CampaignRequest] {

    override protected def executionContext: ExecutionContext = ec

    override def refine[A](request: PermissionRequest[A]): Future[Either[Result, CampaignRequest[A]]] = {

      Future {

        val Logger = request.Logger
        val Res = request.Response

        if (request.actingTeamAccount.isEmpty) {
          Left(Res.ForbiddenError("Please select a team first"))
        } else {
          checkHasCampaign(
            Res = Res,
            request = request,
            id = campaignId
          )
        }
      }

    }
  }

  //This is for public api to convert uuid to corresponding campaign_id
  final def hasCampaignV2(
                           id: SrIdentifier,
                           v: ApiVersion = ApiVersion.V2
                         )(
    implicit ec: ExecutionContext
  ) = new ActionRefiner[PermissionRequest, CampaignRequest] {

    override protected def executionContext: ExecutionContext = ec

    override def refine[A](request: PermissionRequest[A]): Future[Either[Result, CampaignRequest[A]]] = {

      Future {

        val Logger = request.Logger
        val Res = request.Response
        val teamId = TeamId(request.actingTeamAccount.get.team_id)

        implicit val version: ApiVersion = v

        if (request.actingTeamAccount.isEmpty) {
          Left(Res.ForbiddenError("Please select a team first"))
        } else {

          campaignService.getCampaignIdFromSrIdentifier(
            teamId = teamId,
            id = id
          ) match {
            case Left(GetCampaignIdFromSrIdentifierError.DbFailure(e)) =>
              Left(Res.ServerError(s"Error while getting campaign's id.", e = Some(e)))

            case Left(GetCampaignIdFromSrIdentifierError.CampaignIdNotFound(err_msg)) =>
              Left(Res.NotFoundError(err_msg))

            case Right(campaign_id) =>
              checkHasCampaign(
                Res = Res,
                request = request,
                id = campaign_id
              )
          }

        }


      }


    }
  }


  final def hasStep(stepId: Long)(
                   implicit ec: ExecutionContext
  ) = new ActionRefiner[CampaignRequest, CampaignStepRequest] {

    override protected def executionContext: ExecutionContext = ec

    override def refine[A](request: CampaignRequest[A]): Future[Either[Result, CampaignStepRequest[A]]] = {

      Future {
        val Logger = request.Logger
        val Res = request.Response

        campaignStepDAO.find(stepId = stepId, campaignId = request.campaign.id) match {

          case None => Left(Res.NotFoundError("Step not found"))

          case Some(step) =>

            if (!request.permittedAccountIds.contains(request.campaign.account_id)) {
              Left(Res.ForbiddenError("You do not have the permission to access this campaign"))
            } else if (request.actingTeamAccount.team_id != request.campaign.team_id) {
              Left(Res.ForbiddenError("Step does not belong to the current team. Please switch view to the respective team."))
            } else {
              Right(
                CampaignStepRequest(
                  campaign = request.campaign,
                  step = step,
                  actingTeamAccount = request.actingTeamAccount,
                  loggedinAccount = request.loggedinAccount,
                  isApiCall = request.isApiCall,
                  permittedAccountIds = request.permittedAccountIds,
                  Logger = request.Logger,
                  Response = request.Response,
                  request
                )
              )
            }


        }

      }


    }
  }

  final def hasVariant(variantId: Long)(
                      implicit ec: ExecutionContext
  ) = new ActionRefiner[CampaignStepRequest, CampaignStepVariantRequest] {

    override protected def executionContext: ExecutionContext = ec

    override def refine[A](request: CampaignStepRequest[A]): Future[Either[Result, CampaignStepVariantRequest[A]]] = {

      Future {
        val Logger = request.Logger
        val Res = request.Response

        campaignStepVariantDAO.find(variantId = variantId, stepId = request.step.id) match {

          case None => Left(Res.NotFoundError("Variant not found"))

          case Some(variant) =>

            if (
              !request.permittedAccountIds.contains(request.campaign.account_id) ||
                request.campaign.id != request.step.campaign_id ||
                variant.campaign_id != request.campaign.id) {

              Left(Res.ForbiddenError("You do not have the permission to access this campaign email"))

            } else if (request.actingTeamAccount.team_id != request.campaign.team_id) {

              Left(Res.ForbiddenError("Variant does not belong to the current team. Please switch view to the respective team."))

            } else {
              Right(
                CampaignStepVariantRequest(
                  campaign = request.campaign,
                  step = request.step,
                  variant = variant,
                  actingTeamAccount = request.actingTeamAccount,
                  loggedinAccount = request.loggedinAccount,
                  isApiCall = request.isApiCall,
                  permittedAccountIds = request.permittedAccountIds,
                  Logger = request.Logger,
                  Response = request.Response,
                  request
                )
              )            }


        }

      }


    }
  }
}
