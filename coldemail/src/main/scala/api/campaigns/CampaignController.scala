package api.campaigns

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import api.APIErrorResponse.{ErrorResponseProspectsAssignApi, ErrorResponseProspectsUnAssignApi, ErrorResponseV3}
import api.CONSTANTS.API_MSGS
import api.accounts.*
import api.emails.*
import api.prospects.*
import api.*
import api.accounts.EmailScheduledIdOrTaskId.EmailScheduledId
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.services.{AssignProspectsError, AssignProspectsErrorV3, AssignProspectsResponse, AssignProspectsResponseV3, CampaignAISequenceService, CampaignApiError, CampaignApiService, CampaignCacheService, CampaignId, CampaignPreviewService, CampaignProspectAssign, CampaignProspectService, CampaignService, CampaignStartService, CampaignStepService, CampaignTemplateService, CampaignTestStepService, CampaignUpdateSettingsService, CampaignUuid, CampaignValidationError, ChannelSettingData, ChannelSettingService, ChannelSettings, GetCampaignIdFromUuidError, GetChannelSettingsError, GetOrgEmailSendingStatusError, GetProspectIdFromUuidError, ProspectAssignErrorType, StartCampaignError, StartStopCampaignAPIRequest, StartStopCampaignError, UnAssignProspectsErrorV3, UpdateCampaignScheduleSettingsError, ValidateCampaignStepVariantError}
import api.campaigns.services.{AssignProspectsError, AssignProspectsErrorV3, AssignProspectsResponse, AssignProspectsResponseV3, CampaignAISequenceService, CampaignApiError, CampaignApiService, CampaignCacheService, CampaignId, CampaignPreviewService, CampaignProspectAssign, CampaignProspectService, CampaignService, CampaignStartService, CampaignStepService, CampaignTemplateService, CampaignTestStepService, CampaignUpdateSettingsService, CampaignUuid, CampaignValidationError, ChannelSettingData, ChannelSettingService, ChannelSettings, GetCampaignIdFromUuidError, GetChannelSettingsError, GetOrgEmailSendingStatusError, GetProspectIdFromUuidError, ProspectAssignErrorType, StartCampaignError, StartStopCampaignAPIRequest, StartStopCampaignError, UnAssignProspectsErrorV3, UpdateCampaignScheduleSettingsError, ValidateCampaignStepVariantError}
import api.sr_audit_logs.services.RequestLogService
import api.accounts.service.ResetUserCacheUtil
import api.campaigns.DataForCampaignAssignProspects.CampaignProspectFormIds
import api.campaigns.models.{AccountData, CampaignAddEmailChannelError, CampaignCreationError, CampaignEmailSettingsId, CampaignSetNextToBeScheduledAtData, CampaignStepId, ChannelSettingsPublicApiResponse, ChannelSetupError, CreateCampaignFirstStepError, CreateCampaignStepVariantError, DeleteCampaignTasksError, DripCampaignForm, DuplicateCampaignError, GetTestStepsError, NavigationLinksResponse, PreviewGetProspectError, PreviewGetStepsForProspectError, StepAndVariantId, UpdateMaxEmailsPerDayError, UpdateVariantError, ValidateCampaignTemplateError, VoicemailId, VoicemailSaveData}
import api.sr_audit_logs.services.RequestLogService
import api.accounts.service.ResetUserCacheUtil
import api.call.models.{DoerAccountName, UpdateVoicemailDetailsRequest, VoiceDropData}
import api.campaigns.DataForCampaignAssignProspects.{CampaignProspectFormIds, CampaignProspectFormUuids}
import api.campaigns.models.CampaignTypeData.DripCampaignData
import api.campaigns.services.UnAssignProspectsErrorV3.UnAssignProspectError
import api.emails.models.EmailServiceProviderSendEmail.getEmailServiceProviderSendEmail
import api.emails.models.{DeletionReason, EmailTrackingApiRequestData}
import api.emails.services.SelectAndPublishForDeletionService
import api.gpt.CreateStepsRequest
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.ProspectId
import api.prospects.service.{ProspectObjectForApi, ProspectServiceV2, ProspectsListingPaginationService}
import api.rep_tracking_hosts.service.RepTrackingHostService
import api.reports.AllCampaignStats
import api.reports.models.AllCampaignStatsWithTaskCount
import api.templates.TemplateUtils
import api.triggers.Trigger
import app_services.sr_rate_limiter.SrRateLimiter
import eventframework.ProspectObject
import play.api.libs.json.*
import play.api.libs.ws.WSClient
import play.api.mvc.{Action, AnyContent, BaseController, ControllerComponents, Request, Result}
import utils.{Helpers, ParseUtils, SRLogger, StringUtils, StringUtilsV2}
import utils.aws.S3
import utils.email.*
import utils.mq.channel_scheduler.MqEmailChannelScheduler
import api.spamtest.{CreateSpamTestError, GetDomainSpamTestError, GetSpamTestServiceError, SpamTestDAO, SpamTestService}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Random, Success, Try}
import org.joda.time.{DateTime, DateTimeZone}
import sr_scheduler.CampaignStatus
import utils.mq.email.MQEmailMessage
import utils.phishing.PhishingCheckService
import utils.GCP.CloudStorage
import utils.srinternalcampaigns.NewUserDripCampaignsDAO
import utils.helpers.LogHelpers
import api.sr_audit_logs.models.{EventType, ProspectIdWithOldProspectDeduplicationColumn}
import io.lemonlabs.uri.Url
import io.smartreach.esp.api.emails.EmailSettingId
import io.sr.billing_common.models.PlanType
import play.api.libs.Files.TemporaryFile
import utils.input_validations.SanitizeInputStringUtils
import utils.uuid.{SrId, SrIdentifier}
import play.api.libs.json.JodaWrites.*
import sr_scheduler.models.ChannelData.EmailChannelData
import sr_scheduler.models.{CampaignAIGenerationContext, ChannelData, ChannelType}
import utils.Helpers.{getNextLinkForCampaignsApi, getNextLinkForConvThread}
import utils.email.models.{DeleteEmailsScheduledType, SendScheduleEmailType}
import utils.mq.do_not_contact.{MQDoNotContactConsumer, MQDoNotContactMessage}
import utils.uuid.services.SrUuidService

import java.io.File
import scala.concurrent.impl.Promise
import play.api.mvc.*
import play.api.libs.Files.TemporaryFile
import play.api.mvc.MultipartFormData.FilePart

import javax.inject.*
import api.prospects.models.StepId
import utils.mq.channel_scheduler.channels.ContentGenerationFailure

case class ReorderCampaignStepData(
                                    step_id_to_be_reordered: CampaignStepId,
                                    new_parent_step_id: CampaignStepId,
                                  )

object ReorderCampaignStepData {

  implicit val reads: Reads[ReorderCampaignStepData] = Json.reads[ReorderCampaignStepData]

}

class CampaignController(
                          //  resetUserCacheUtil: ResetUserCacheUtil,
                          apiService: ApiService,
                          campaignStepService: CampaignStepService,
                          prospectService: ProspectService,
                          campaignPreviewService: CampaignPreviewService,
                          channelSettingService: ChannelSettingService,
                          protected val controllerComponents: ControllerComponents,
                          //  templateUtils: TemplateUtils,
                          spamTestService: SpamTestService,
                          campaignUpdateSettingsService: CampaignUpdateSettingsService,
                          campaignAISequenceService: CampaignAISequenceService,
                          //  campaignTemplateService: CampaignTemplateService,
                          protected val campaignService: CampaignService, // required in CampaignUtils
                          campaignTestStepService: CampaignTestStepService,
                          campaignStartService: CampaignStartService,
                          campaignProspectService: CampaignProspectService,
                          campaignProspectAssign: CampaignProspectAssign,
                          //  campaignProspectDAO: CampaignProspectDAO,
                          triggerDAO: Trigger,
                          emailSenderService: EmailSenderService,
                          //  mqEmailSchedulerV2: MqEmailChannelScheduler,
                          prospectsListingPaginationService: ProspectsListingPaginationService,
                          prospectDAOService: ProspectDAOService,
                          emailScheduledDAO: EmailScheduledDAO,
                          emailSettingDAO: EmailSettingDAO,
                          phishingCheckService: PhishingCheckService,
                          override protected val campaignStepVariantDAO: CampaignStepVariantDAO,
                          protected val requestLogService: RequestLogService,
                          protected val srRateLimiter: SrRateLimiter,
                          //        prospectAccountDAO: ProspectAccountDAO1,
                          newUserDripCampaignsDAO: NewUserDripCampaignsDAO,
                          implicit val wsClient: WSClient,
                          implicit val materializer: Materializer,
                          implicit val system: ActorSystem,
                          accountService: AccountService,
                          permissionUtils: PermissionUtils,
                          //  authUtils: AuthUtils,
                          prospectServiceV2: ProspectServiceV2,
                          override protected val campaignStepDAO: CampaignStepDAO,
                          campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO,
                          //  spamTestDAO: SpamTestDAO,
                          campaignCacheService: CampaignCacheService,
                          campaignApiService: CampaignApiService,
                          repTrackingHostService: RepTrackingHostService,

                          selectAndPublishForDeletionService: SelectAndPublishForDeletionService,
                          override protected val srUuidService: SrUuidService
                        ) extends BaseController
  with CampaignUtils with SanitizeInputStringUtils {

  private implicit val ec: ExecutionContext = controllerComponents.executionContext

  // NOTE: [ZAPIER] used in zapier app
  // [PUBLIC API]: Mentioned in [API DOCS]
  def findAll(
               v: String,
               basic: Option[Boolean],
               archived: Option[Boolean],
               aid: Option[Long],
               tid: Option[Long],
               is_campaign_inbox: Option[Boolean]
             ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_CAMPAIGNS,
    tidOpt = tid,
    apiAccess = true
  ).async { (request: PermissionRequest[AnyContent]) =>


    val permittedAccountIdsForVIEW_CAMPAIGNS = request.permittedAccountIds

    val ta = request.actingTeamAccount.get
    val teamId = ta.team_id
    val l = request.loggedinAccount
    given Logger: SRLogger = request.Logger
    val Res = request.Response

    val successMsg = "Campaigns found"

    if (basic.getOrElse(false)) {


      val permittedAccountIdsForEDIT_CAMPAIGNS = PermissionMethods.getPermittedAccountIdsForAccountAndPermission(
        loggedinAccount = l,
        actingTeamId = ta.team_id,
        actingAccountId = ta.user_id,
        version = v,
        Logger = Logger
      )(permission = PermType.EDIT_CAMPAIGNS)

      Future.successful(Res.Success(successMsg, Json.obj(
        "campaigns" -> campaignService.findAllBasic(
          permittedAccountIds_VIEW_CAMPAIGNS = permittedAccountIdsForVIEW_CAMPAIGNS,
          permittedAccountIds_EDIT_CAMPAIGNS = permittedAccountIdsForEDIT_CAMPAIGNS,
          loggedinAccountId = request.loggedinAccount.internal_id,
          is_campaign_inbox = is_campaign_inbox,
          teamId = teamId
        )
      )))

    } else {

      val campaignsFut = Future.fromTry {
        campaignService.findAll(
          showCampaignTags = AppConfig.RollingUpdates.showCampaignTags(l.org.id),
          permittedAccountIds = permittedAccountIdsForVIEW_CAMPAIGNS,
          loggedinAccountId = l.internal_id,
          teamId = teamId,
          account = Some(l),

          // filters
          includeArchived = archived.getOrElse(false)
        )
      }

      val hasArchivedCampaignFut: Future[Boolean] = Future {

        // not returning has_archived_campaign in isApiCall anyways, so ignore this sql for api calls
        if (request.isApiCall) {
          false
        } else {
          campaignService.checkIfAccountHasArchivedCampaign(permittedAccountIds = request.permittedAccountIds, teamId = teamId)
        }

      }

      for {
        campaigns <- campaignsFut
        hasArchivedCampaign <- hasArchivedCampaignFut
      } yield {
        if (request.isApiCall) {

          Res.Success(successMsg, Json.obj(
            "campaigns" -> campaigns.map(c => CampaignWithStatsAndEmail.apiStructure(c = c, version = v))
          ))

        } else {

          Res.Success(successMsg, Json.obj(
            "campaigns" -> campaigns,
            "has_archived_campaign" -> hasArchivedCampaign

          ))
        }

      }
    }

  }


  //need to check if this needs to be handled for v2 APIs
  def findAllV3(
                 basic: Option[Boolean],
                 archived: Option[Boolean],
                 aid: Option[Long],
                 tid: Option[Long],
                 name: Option[String],
                 sender_email_address: Option[String],
                 receiver_email_address: Option[String],
                 status: Option[String],
                 newer_than: Option[Long],
                 older_than: Option[Long]
               ) = permissionUtils.checkPermission(
    version = ApiVersion.V3.toString,
    permission = PermType.VIEW_CAMPAIGNS,
    tidOpt = tid,
    apiAccess = true
  ) { (request: PermissionRequest[AnyContent]) =>

    implicit val v: ApiVersion = ApiVersion.V3

    val permittedAccountIdsForVIEW_CAMPAIGNS = request.permittedAccountIds

    val ta = request.actingTeamAccount.get
    val teamId = ta.team_id
    val l = request.loggedinAccount
    given Logger: SRLogger = request.Logger
    val Res = request.Response

    val successMsg = "Campaigns found"

    val query = request.uri
    val parsedQueryParams: Map[String, Vector[String]] = StringUtilsV2.getParams(query)
    val orgId = request.loggedinAccount.org.id
    val queryParams = CampaignService.validateParams(
      parsedParams = parsedQueryParams
    )

    queryParams match {

      case Failure(exception) =>
        Logger.error("[Validation Error] Invalid parameters passed ::", exception)
        Res.BadRequestError(exception.getMessage)

      case Success(search_params) =>
        campaignService.findCampaignsV3(
          accountIds = permittedAccountIdsForVIEW_CAMPAIGNS,
          teamId = teamId,
          orgId = orgId,
          loggedinAccountId = l.internal_id,
          search_params = search_params,
          account = l,
          Logger = Logger
        ) match {

          case Failure(exception) =>
            Logger.error("[findAllCampaignsV3] Error while fetching data", exception)
            Res.ServerError("There was a server error. Please contact support", Some(exception))

          case Success(result) =>

            val uri = Url.parse(request.uri)
            val page_data = result.links

            val nav_links = NavigationLinksResponse(
              next = getNextLinkForCampaignsApi(uri = uri, page_data = page_data, search_params = search_params),
            )

            apiService.getCampaignListingApiResponse(
              v = v,
              campaigns = result.data,
              teamId = TeamId(id = teamId),
              orgId = OrgId(id = orgId),
              isPublicApi = request.isApiCall
            ) match {

              case Left(error) => error match {
                case CampaignApiError.ErrorWhileFetchingDataForTeamId(e) =>
                  Res.ServerError("Error while fetching data", Some(e))
              }

              case Right(response) =>



                Res.Success(
                  message = successMsg,
                  data = Json.obj("campaigns" -> response,
                    "links" -> nav_links),
                  apiVersion = v.toString
                )
            }


        }
    }

  }

  // [PUBLIC API]: Mentioned in [API DOCS]
  def find(
            v: ApiVersion,
            id: SrIdentifier,
            aid: Option[Long],
            tid: Option[Long]
          ) = (
    permissionUtils.checkPermissionV2(
      id = Some(id),
      version = v,
      permission = PermType.VIEW_CAMPAIGNS,
      tidOpt = tid,
      apiAccess = true
    )
      andThen hasCampaignV2(id, v)
    ) { (request: CampaignRequest[AnyContent]) =>

    val teamId = request.actingTeamAccount.team_id
    val orgId = OrgId(request.loggedinAccount.org.id)
    given Logger: SRLogger = request.Logger
    val Res = request.Response

    //campaignService.find is required here as it is mapped with CampaignStatsWithEmail.apiStructure
    val campaignOpt = campaignService.find(id = request.campaign.id, teamId = teamId, account = Some(request.loggedinAccount))

    implicit val version: ApiVersion = v
    campaignOpt match {

      case None =>
        Res.ServerError("Error while fetching data", None)

      case Some(campaign) =>
        apiService.getCampaignApiResponse(
          v = v.toString,
          campaign = campaign,
          orgId = orgId,
          teamId = TeamId(id = teamId),
          isPublicApi = request.isApiCall) match {

          case Left(error) => error match {
            case CampaignApiError.ErrorWhileFetchingDataForTeamId(_) =>
              Res.ServerError("Error while fetching data", None)
          }

          case Right(response) =>
            Res.Success(
              message = s"'${request.campaign.name}' campaign found",
              data = Json.toJson(response),
              apiVersion = v.toString
            )
        }
    }

  }

  def getProspectsInCampaign(
                              id: utils.uuid.SrIdentifier,
                              tid: Option[Long],
                              older_than: Option[Long],
                              newer_than: Option[Long]
                            ) = (
    permissionUtils.checkPermissionV2(
      id = Some(id),
      version = ApiVersion.V3,
      permission = PermType.VIEW_PROSPECTS,
      tidOpt = tid,
      apiAccess = true
    )
      andThen hasCampaignV2(id, ApiVersion.V3)
    ) { (request: CampaignRequest[AnyContent]) =>

    val ta = request.actingTeamAccount
    val teamId = TeamId(ta.team_id)
    given Logger: SRLogger = request.Logger
    val Res = request.Response
    val campaign_id = request.campaign.id
    val campaignUuid = CampaignUuid(request.campaign.uuid.get)


    val query = request.uri
    val parsedQueryParams: Map[String, Vector[String]] = StringUtilsV2.getParams(query)
    val queryParams = ProspectService.validateQueryParams(
      parsedParams = parsedQueryParams,
      campaign_id = Some(campaign_id)
    )

    queryParams match {
      case Failure(exception) => Res.BadRequestError(
        message = exception.getMessage
      )
      case Success(filters) =>
        prospectsListingPaginationService.findProspectsListingPageForApi(
          teamId = teamId,
          filters = filters,
          findActiveCampaignsIfInternalRequest = true

        ) match {
          case Failure(exception) =>
            Logger.error("[getProspectsInCampaign] error while fetching data", err = exception)
            Res.ServerError("Error while fetching data", None)
          case Success(prospects) =>

            val uri = Url.parse(request.uri)
            val page_data = prospects.links

            val nav_links = NavigationLinksResponse(
              next = Helpers.getNextLinkForListingApi(uri = uri, page_data = page_data, search_params = filters),
            )

            apiService.campaignProspectListingApiResponse(
              prospects = prospects.data,
              teamId = teamId
            ) match {

              case Failure(exception) =>
                Res.ServerError(exception)

              case Success(campaignProspectData) =>
                Res.Success(message = "Prospects found",
                  data = Json.obj(
                    "campaign_prospects" -> campaignProspectData,
                    "links" -> nav_links
                  ),
                  apiVersion = ApiVersion.V3.toString
                )
            }


        }

    }


  }

  /**
   * * Examples of passing and failing test cases because of sanitizeInputStrings refiner
   *
   * 1. Passing
   * curl 'http://localhost:3001/api/v2/campaigns?tid=47' \
   * -H 'Accept: application/json' \
   * -H 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8' \
   * -H 'Connection: keep-alive' \
   * -H 'Content-Type: application/json' \
   * -H 'Cookie: xxxxxx' \
   * -H 'Origin: http://localhost:3001' \
   * -H 'Referer: http://localhost:3001/dashboard/campaigns?tid=47' \
   * -H 'Sec-Fetch-Dest: empty' \
   * -H 'Sec-Fetch-Mode: cors' \
   * -H 'Sec-Fetch-Site: same-origin' \
   * -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
   * -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"' \
   * -H 'sec-ch-ua-mobile: ?0' \
   * -H 'sec-ch-ua-platform: "macOS"' \
   * --data-raw '{"name":"C-11 Apr","timezone":"Asia/Kolkata","campaign_owner_id":49,"is_multichannel":false}'
   * {"status":"success","message":"Congrats! A new campaign has been added. Please add steps, prospects & start the campaign!","data":{"campaign":{"id":344,"team_id":47,"name":"C-11 Apr","owner_name":"Prachi Mane","owner_email":"<EMAIL>","owner_id":49,"status":"not_started","tags":[],"spam_test_exists":false,"warmup_is_on":false,"head_step_id":null,"created_at":"2023-04-11T11:14:30.798+05:30","error":null,"stats":{"total_sent":0,"total_opened":0,"total_clicked":0,"total_replied":0,"total_steps":0,"current_prospects":0,"current_opted_out":0,"current_completed":0,"current_bounced":0,"current_to_check":0,"current_failed_email_validation":0,"current_in_progress":0,"current_unsent_prospects":0,"current_do_not_contact":0,"reply_sentiment_stats":{"positive":0}},"settings":{"sender_email_settings_id":null,"receiver_email_settings_id":null,"timezone":"Asia/Kolkata","daily_from_time":32400,"daily_till_time":64800,"sending_holiday_calendar_id":null,"days_preference":[false,true,true,true,true,true,false],"consecutive_email_delay":60,"mark_completed_after_days":1,"max_emails_per_day":50,"max_emails_per_day_from_email_account":null,"open_tracking_enabled":true,"click_tracking_enabled":false,"enable_email_validation":true,"ab_testing_enabled":false,"warmup_started_at":null,"warmup_length_in_days":null,"warmup_starting_email_count":null,"schedule_start_at":null,"schedule_start_at_tz":null,"email_priority":"equal","append_followups":true,"opt_out_msg":"","opt_out_is_text":true,"add_prospect_to_dnc_on_opt_out":true,"triggers":[],"from_email":null,"to_email":null,"signature":null},"is_multichannel":false}}}%
   *
   * 2. Failing
   * curl 'http://localhost:3001/api/v2/campaigns?tid=47' \
   * -H 'Accept: application/json' \
   * -H 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8' \
   * -H 'Connection: keep-alive' \
   * -H 'Content-Type: application/json' \
   * -H 'Cookie: xxxxxxxx' \
   * -H 'Origin: http://localhost:3001' \
   * -H 'Referer: http://localhost:3001/dashboard/campaigns?tid=47' \
   * -H 'Sec-Fetch-Dest: empty' \
   * -H 'Sec-Fetch-Mode: cors' \
   * -H 'Sec-Fetch-Site: same-origin' \
   * -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
   * -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"' \
   * -H 'sec-ch-ua-mobile: ?0' \
   * -H 'sec-ch-ua-platform: "macOS"' \
   * --data-raw '{"name":"<script>alert(1)</script>","timezone":"Asia/Kolkata","campaign_owner_id":49,"is_multichannel":false}'
   * {"status":"error","message":"Invalid input <script>alert(1)</script> for field name","data":{"error_type":"forbidden"}}%
   */

  def create(
              v: String,
              aid: Option[Long],
              tid: Option[Long]
            ): Action[JsValue] = permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid,
      localOrStagingApiTestAccess = true
    )
    .andThen(sanitizeInputStringsWithPermissionReq())
    .async(parse.json) { (request: PermissionRequest[JsValue]) => {

      val ta = request.actingTeamAccount
      given Logger: SRLogger = request.Logger
      val Res = request.Response

      if (ta.isEmpty) {

        Future.successful(Res.BadRequestError("Invalid team"))

      } else {
        val validateData = request.body.validate[CampaignCreateForm]

        validateData match {

          case JsError(e) =>
            Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

          case JsSuccess(data, _) =>

            val result = campaignService.createCampaign(
              orgId = request.loggedinAccount.org.id,
              accountId = data.campaign_owner_id.getOrElse(ta.get.user_id),
              teamId = ta.get.team_id,
              taId = ta.get.ta_id,
              data = data,
              permittedAccountIdsForEditCampaigns = request.permittedAccountIds,
              ownerFirstName = request.loggedinAccount.first_name.get // Not possible to have no first_name
            )
            result map {
              case Left(CampaignCreationError.CampaignNameNotUniqueError(e)) =>
                Res.BadRequestError("Another campaign with the given name exists. Please try creating the campaign with a different name.")

              case Left(CampaignCreationError.SQLException(e)) =>
                Res.ServerError(s"Error while creating campaign.", e = Some(e))

              case Left(CampaignCreationError.CampaignNotCreated) =>
                Res.ServerError("Error while creating campaign. Could you try again ?", e = None)

              case Left(CampaignCreationError.FeatureUsageEventSaveError(e)) =>
                Res.ServerError(err = e)

              case Left(CampaignCreationError.InCorrectCampaignOwnerId) =>
                Logger.error(s"Invalid campaign owner provided owner_id: ${data.campaign_owner_id}")
                Res.BadRequestError(s"Please send valid campaign owner")

              case Right(row) =>
                Res.Success("Congrats! A new campaign has been added. Please add steps, prospects & start the campaign!", Json.obj("campaign" -> row))
            }
        }
      }
    }
    }


  def updateCampaignContext(v: String, tid: Option[Long], campaignId: Long): Action[JsValue] = permissionUtils.checkPermission(
    permission = PermType.EDIT_CAMPAIGNS,
    tidOpt = tid
  ).async(parse.json) { (request: PermissionRequest[JsValue]) =>

    given logger: SRLogger = request.Logger
    val res = request.Response
    val loggedInAccount = request.loggedinAccount
    val actingTeamAccount = request.actingTeamAccount

    request.body.validate[CampaignAIGenerationContext] match {
      case JsError(error) =>
        Future.successful(res.JsValidationError(error))

      case JsSuccess(updateCampaignContextRequest, _) =>
        campaignService.updateCampaignContext(
          data = updateCampaignContextRequest,
          campaign_id = CampaignId(campaignId),
          team_id = TeamId(actingTeamAccount.get.team_id)
        ) match {
          case Failure(e) =>
            Future.successful(res.ServerError("Error while updating campaign context", Some(e)))
          case Success(updatedRows) =>
            if (updatedRows > 0) {
              Future.successful(res.Success("Campaign context updated successfully"))
            } else {
              Future.successful(res.NotFoundError("Campaign not found or no changes made"))
            }
        }
    }
  }


  def createStepsUsingAI(v: String, tid: Option[Long], campaignId: Long): Action[JsValue] = permissionUtils.checkPermission(
    permission = PermType.EDIT_CAMPAIGNS,
    tidOpt = tid
  ).async(parse.json) { (request: PermissionRequest[JsValue]) =>

    given logger: SRLogger = request.Logger
    val res = request.Response
    val loggedInAccount = request.loggedinAccount
    val actingTeamAccount = request.actingTeamAccount

    request.body.validate[CreateStepsRequest] match {
      case JsError(error) =>
        Future.successful(res.JsValidationError(error))

      case JsSuccess(createEmailStepsRequest, _) =>
        campaignAISequenceService.triggerCampaignAISequenceGeneration(
          createStepsRequest = createEmailStepsRequest,
          orgId = OrgId(loggedInAccount.org.id),
          campaignId = CampaignId(campaignId),
          teamId = TeamId(actingTeamAccount.get.team_id),
          taId = actingTeamAccount.get.ta_id,
          accountId = AccountId(loggedInAccount.internal_id)
        ) match {
          case Failure(e) =>
            if (e.getMessage.contains("You have already triggered AI Sequence Generation for campaign")) {
              Future.successful(res.BadRequestError(e.getMessage))
            }
            else {
              Future.successful(res.ServerError("Error while generating campaign steps", Some(e)))
            }

          case Success(bool) =>
            Future.successful(res.Success("Published campaign for generating steps"))
        }
    }

  }

  def changeArchiveStatus(
                           v: String,
                           id: Long,
                           aid: Option[Long],
                           tid: Option[Long]
                         ) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.CHANGE_CAMPAIGN_STATUS,
      tidOpt = tid
    )
      andThen hasCampaign(id)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>

    Future {

      val archive: Option[Boolean] = (request.body \ "archive").asOpt[Boolean]
      given Logger: SRLogger = request.Logger
      val Res = request.Response

      val t = request.actingTeamAccount
      val teamId = TeamId(id = request.actingTeamAccount.team_id)

      if (archive.isEmpty) {
        Res.BadRequestError("Please send 'archive' key in request body")
      } else {

        val updated: Try[CampaignWithStatsAndEmail] = if (archive.get) {

          campaignService.archiveCampaign(
            campaignId = id,
            teamId = teamId
          )

        } else {

          campaignService.stopCampaign(
            campaignId = id,
            teamId = teamId
          )
        }

        updated match {

          case Failure(e) =>
            Res.ServerError("Problem while archiving/stopping the campaign. Could you try again or contact support ? ", e = Some(e))

          case Success(row) =>

            Res.Success(s"Campaign successfully ${if (archive.get) "archived" else "unarchived"}", Json.obj("campaign" -> row))

        }
      }

    }
  }

  /**
   * * Examples of passing and failing test cases because of sanitizeInputStrings refiner
   *
   * 1. Passing
   * curl 'http://localhost:3001/api/v2/campaigns/342?tid=47' \
   * -X 'PUT' \
   * -H 'Accept: application/json' \
   * -H 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8' \
   * -H 'Connection: keep-alive' \
   * -H 'Content-Type: application/json' \
   * -H 'Cookie: xxxxxx' \
   * -H 'Origin: http://localhost:3001' \
   * -H 'Referer: http://localhost:3001/dashboard/campaigns/342/content?tid=47' \
   * -H 'Sec-Fetch-Dest: empty' \
   * -H 'Sec-Fetch-Mode: cors' \
   * -H 'Sec-Fetch-Site: same-origin' \
   * -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
   * -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"' \
   * -H 'sec-ch-ua-mobile: ?0' \
   * -H 'sec-ch-ua-platform: "macOS"' \
   * --data-raw '{"name":"Campaign 11 April"}'
   * {"status":"success","message":"Success! Campaign name changed from 'Campaign 11 April' to 'Campaign 11 April'","data":{"campaign":{"id":342,"team_id":47,"name":"Campaign 11 April","owner_name":"Prachi Mane","owner_email":"<EMAIL>","owner_id":49,"status":"not_started","tags":[],"spam_test_exists":false,"warmup_is_on":false,"head_step_id":null,"created_at":"2023-04-11T11:13:42.428+05:30","error":null,"stats":{"total_sent":0,"total_opened":0,"total_clicked":0,"total_replied":0,"total_steps":0,"current_prospects":0,"current_opted_out":0,"current_completed":0,"current_bounced":0,"current_to_check":0,"current_failed_email_validation":0,"current_in_progress":0,"current_unsent_prospects":0,"current_do_not_contact":0,"reply_sentiment_stats":{"positive":0}},"settings":{"sender_email_settings_id":null,"receiver_email_settings_id":null,"timezone":"Asia/Kolkata","daily_from_time":32400,"daily_till_time":64800,"sending_holiday_calendar_id":null,"days_preference":[false,true,true,true,true,true,false],"consecutive_email_delay":60,"mark_completed_after_days":1,"max_emails_per_day":50,"max_emails_per_day_from_email_account":null,"open_tracking_enabled":true,"click_tracking_enabled":false,"enable_email_validation":true,"ab_testing_enabled":false,"warmup_started_at":null,"warmup_length_in_days":null,"warmup_starting_email_count":null,"schedule_start_at":null,"schedule_start_at_tz":null,"email_priority":"equal","append_followups":true,"opt_out_msg":"","opt_out_is_text":true,"add_prospect_to_dnc_on_opt_out":true,"triggers":[],"from_email":null,"to_email":null,"signature":null},"is_multichannel":false}}}
   *
   * 2. Failing
   * curl 'http://localhost:3001/api/v2/campaigns/342?tid=47' \
   * -X 'PUT' \
   * -H 'Accept: application/json' \
   * -H 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8' \
   * -H 'Connection: keep-alive' \
   * -H 'Content-Type: application/json' \
   * -H 'Cookie: _ga=GA1.1.**********.**********; _uetvid=7135fda08fd411ec8a10e7c7025acf5e; _clck=12rij2s|1|f5v|0; _hp2_id.**********=%7B%22userId%22%3A%225419574963826303%22%2C%22pageviewId%22%3A%226444485230673675%22%2C%22sessionId%22%3A%228565663195733061%22%2C%22identity%22%3A%22prachi%40smartreach.io%22%2C%22trackerVersion%22%3A%224.0%22%2C%22identityField%22%3Anull%2C%22isIdentified%22%3A1%2C%22oldIdentity%22%3Anull%7D; _gcl_au=1.1.58288668.1679139271; PLAY_SESSION=eyJhbGciOiJIUzI1NiJ9.***********************************************************************************.Ow2bZDi79_gyR20M8yK6ULZFZZnAQUD9TYnc8ABfySI; _gid=GA1.1.1134763566.1681099440; ln_or=eyIzNjQzMTk2IjoiZCJ9; _ga_EH8EYSN2VW=GS1.1.1681191704.96.1.1681192511.60.0.0' \
   * -H 'Origin: http://localhost:3001' \
   * -H 'Referer: http://localhost:3001/dashboard/campaigns/342/content?tid=47' \
   * -H 'Sec-Fetch-Dest: empty' \
   * -H 'Sec-Fetch-Mode: cors' \
   * -H 'Sec-Fetch-Site: same-origin' \
   * -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
   * -H 'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"' \
   * -H 'sec-ch-ua-mobile: ?0' \
   * -H 'sec-ch-ua-platform: "macOS"' \
   * --data-raw '{"name":"<script>alert(1)</script>CampaignName"}'
   * {"status":"error","message":"Invalid input <script>alert(1)</script>CampaignName for field name","data":{"error_type":"forbidden"}}%
   */

  def updateName(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid
    )
      andThen sanitizeInputStringsWithPermissionReq()
      andThen hasCampaign(id)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>

    Future {
      given Logger: SRLogger = request.Logger
      val Res = request.Response
      request.body.validate[CampaignUpdateNameForm] match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>

          if (data.name.trim.isEmpty) {
            Res.BadRequestError(
              "Campaign name cannot be empty. Please provide a valid name.",
              logAsFatal = false
            )
          } else {


            campaignService.updateName(id = id,
              data = data,
              team_id = TeamId(request.actingTeamAccount.team_id)
            ) match {

              case Failure(e) =>

                e.getMessage match {

                  case msg if msg.contains("unique_campaign_name") => Res.BadRequestError("You have another campaign with the same name. Could you try with a different name ?")

                  case msg => Res.ServerError("Problem while updating the campaign. Could you try again ? " + msg, e = Some(e))
                }

              case Success(None) => Res.NotFoundError("Campaign not found. Could you reload and try again ?")

              case Success(Some(row)) => Res.Success(s"Success! Campaign name changed from '${request.campaign.name}' to '${row.name}'", Json.obj("campaign" -> row))

            }

          }
      }
    }

  }


  def updateSettings(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(id)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>

    Future {

      given Logger: SRLogger = request.Logger
      val Res = request.Response

      request.body.validate[UpdateCampaignScheduleSettings] match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>
          val team_id = TeamId(request.actingTeamAccount.team_id)
          Logger.info(s"CampaignController.updateSettings called for " + s"aid_${request.actingTeamAccount.user_id} : tid_${team_id.id} " + s": cid_$id : $data")


          val campaignDataBeforeUpdate = request.campaign

          campaignUpdateSettingsService.updateScheduleSettings(
            campaignDataBeforeUpdate = campaignDataBeforeUpdate,
            orgId = request.loggedinAccount.org.id,
            campaignId = id,
            settings = data,
            team_id = team_id
          ) match {

            case Left(UpdateCampaignScheduleSettingsError.SQLError(err)) =>

              Res.ServerError(err)

            case Left(UpdateCampaignScheduleSettingsError.CampaignNotFoundAfterUpdatingImpossibleError) =>

              Logger.fatal("UpdateCampaignScheduleSettingsError.CampaignNotFoundAfterUpdatingImpossibleError")
              Res.ServerError(
                message = "Error while updating campaign. Could you reload and try again?",
                e = None
              )

            case Left(UpdateCampaignScheduleSettingsError.CalendarNotFoundError) =>

              Res.NotFoundError(
                message = "Calendar not found"
              )

            case Right(updatedCampaign) =>

              Res.Success("Success! Campaign schedule settings have been updated", Json.obj("campaign" -> updatedCampaign))

          }

      }
    }
  }


  def updateAppendFollowUps(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(id)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>

    Future {

      given Logger: SRLogger = request.Logger
      val Res = request.Response


      val validateData = request.body.validate[CampaignFollowUpSetting]

      validateData match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>
          val team_id = TeamId(request.actingTeamAccount.team_id)


          campaignService.updateAppendFollowUps(id = id, data = data, team_id = team_id) match {

            case Failure(e) => Res.ServerError("Error while updating follow up campaign settings: " + e.getMessage, e = Some(e))

            case Success(None) => Res.NotFoundError("Campaign not found. Could you reload and try again ?")

            case Success(Some(row)) =>

              // 8-Apr-2022: ignoring scheduling the campaign immediately on this change

              Res.Success("Success! Append follow up setting has been updated.", Json.obj("campaign" -> row))

          }


      }
    }
  }

  def generateHyperPersonalizedPreview(
                                        v: String,
                                        campaignId: Long,
                                        prospectId: Long,
                                        stepId: Long,
                                        aid: Option[Long],
                                        tid: Option[Long]
                                      ) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.VIEW_CAMPAIGNS, // Or EDIT_CAMPAIGNS if more appropriate
      tidOpt = tid
    ) andThen hasCampaign(campaignId)
    ).async { (request: CampaignRequest[AnyContent]) =>

    implicit val Logger: SRLogger = request.Logger
    val Res = request.Response

    // Call the service method to generate and save the preview
    campaignPreviewService.generateHyperPersonalizedPreviewForProspect(
      prospect_id = ProspectId(prospectId),
      account_id = AccountId(request.actingTeamAccount.user_id),
      campaign = request.campaign,
      team_id = TeamId(id = request.actingTeamAccount.team_id),
      org_id = OrgId(request.loggedinAccount.org.id),
      step_id = StepId(stepId)
    ).map { case (subject, body) =>
      // Success response with subject and body
      Res.Success("Preview generated and saved", Json.obj("subject" -> subject, "body" -> body))
    }.recover {

      case e: ContentGenerationFailure =>
        Logger.error(s"generateHyperPersonalizedPreview :: team_id: ${tid} :: step_id: ${stepId} :: campaignId: ${campaignId} :: prospect_id: ${prospectId} error ${e.message} ")
        Res.BadRequestError(e.message)
      // Handle specific exceptions
      case e: Exception if e.getMessage.contains("Missing data") =>
        Res.NotFoundError("Prospect not found in campaign")
      case e: Exception if e.getMessage.contains("Step or variant not found") =>
        Res.BadRequestError("Step or variant not found")
      case e: Exception if e.getMessage.contains("Campaign has no email settings") =>
        Res.BadRequestError("Campaign has no email settings")
      case e: Exception =>
        Res.ServerError(API_MSGS.ERROR_INTERNAL_SERVER, e = Some(e))
    }
  }

  // SENDER_ROTATION
  // setting email_priority, max_emails_per_day, mark_completed_after_days, open_tracking_enabled, click_tracking_enabled, add_prospect_to_dnc_on_opt_out
  // using senderEmailSettingIds for only disabling email validation no Org is allowed to do so as of 29-Sept-2023
  def updateOtherSettings(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(id)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>

    Future {
      given Logger: SRLogger = request.Logger
      val Res = request.Response

      request.body.validate[CampaignOtherSettings] match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>
          Logger.info(s"CampaignController.updateOtherSettings called for aid_${request.actingTeamAccount.user_id} : tid_${request.actingTeamAccount.team_id} : cid_$id : $data")

          val enableEmailValidationInput = data.enable_email_validation.getOrElse(true)

          val invalidAttempt: Option[String] = if (!enableEmailValidationInput) {

            val senderEmailSettingIds = request.campaign.settings.campaign_email_settings.map(_.sender_email_setting_id)

            if (senderEmailSettingIds.isEmpty) {

              Some("Select email account to send emails from, before disabling email validation")

            } else {

              val senderEmailSetting = emailSettingDAO.find(senderEmailSettingIds = senderEmailSettingIds, teamId = TeamId(request.actingTeamAccount.team_id))

              if (senderEmailSetting.size != senderEmailSettingIds.size) {

                Some("Sending email account not found, please contact support")

              } else if (
                senderEmailSetting.map(_.service_provider).exists(sp => !EmailSetting.API_BASED_EMAILSERVICEPROVIDERS.contains(sp))

              ) {
                Some("Disabling email validation is not allowed if you are sending emails from SMTP-based email accounts.")

              } else {
                None
              }
            }
          } else if (data.send_plain_text_email && (data.click_tracking_enabled || data.open_tracking_enabled)) {
            Some("open tracking and click tracking cant be on for plain text email")
          } else {
            None
          }

          if (invalidAttempt.isDefined) {

            Logger.error(s"CampaignController.updateOtherSettings: error while disabling email validation: account ${request.loggedinAccount.internal_id} :: campaign $id :: $data :: $invalidAttempt")

            Res.BadRequestError(invalidAttempt.get)
          } else {

            val teamId = TeamId(id = request.actingTeamAccount.team_id)
            campaignService.updateOtherSettings(
              id = id,
              data = data,
              team_id = teamId
            ) match {

              case Failure(e) => Res.ServerError("Error while updating campaign settings. Please try again or contact support.", e = Some(e))

              case Success(None) => Res.NotFoundError("Campaign not found. Could you reload and try again ?")

              case Success(Some(row)) =>

                val team_id = TeamId(id = request.actingTeamAccount.team_id)
                // schedule immediately when campaign's limit etc. settings are updated
                campaignService.setNextToBeScheduledAt(

                  flowData = CampaignSetNextToBeScheduledAtData.UpdateCampaignSettingFlow(
                    campaignSettings = request.campaign.settings,
                    campaignId = CampaignId(request.campaign.id),
                    teamId = TeamId(request.campaign.team_id)
                  ),

                  Logger = Logger,
                  team_id = team_id
                ) match {

                  case Failure(e) =>
                    Res.ServerError("Error while updating campaign settings [2]. Please try again or contact support.", e = Some(e))

                  case Success(_) =>
                    Res.Success("Success! Campaign settings have been updated.", Json.obj("campaign" -> row))

                }


            }
          }


      }
    }
  }

  def updateMaxEmailsPerDay(v: String, id: Long, tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(id)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>

    Future {
      given Logger: SRLogger = request.Logger
      val Res = request.Response

      val validateData = request.body.validate[MaxEmailsPerDay]

      validateData match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>

          campaignService.updateMaxEmailsPerDay(
            campaignId = CampaignId(
              id = id
            ),
            team_id = TeamId(
              id = tid.get
            ),
            max_emails_per_day = data
          ) match {

            case Failure(e) =>
              Logger.fatal(s"[CampaignController] updateMaxEmailsPerDay: error while updating max_emails_per_day, c_id: ${id} tid: ${tid} max_emails_per_day: ${data} ")
              Res.ServerError(s"There was a problem updating your unsubscribe message. Could you try again ? " + e.getMessage, e = Some(e))

            case Success(row) =>

              row match {
                case Left(UpdateMaxEmailsPerDayError.NoneReturnedWhileUpdate) =>
                  Res.NotFoundError("Please try again, DB returned None while update ?")

                case Left(UpdateMaxEmailsPerDayError.ErrorWhileUpdation(err)) =>
                  Res.ServerError(err)

                case Left(UpdateMaxEmailsPerDayError.ValidationFailed(err)) =>
                  Res.BadRequestError(err.getMessage)

                case Right(None) => Res.NotFoundError("Campaign not found. Could you reload and try again ?")
                case Right(Some(row)) =>
                  Res.Success("Campaign max emails to be sent per day updated", Json.obj("campaign" -> row))

              }


          }
      }
    }
  }


  def updateOptOutMsg(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(id)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>

    Future {
      given Logger: SRLogger = request.Logger
      val Res = request.Response

      val validateData = request.body.validate[CampaignOptOutSettings]

      validateData match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>

          val team_id = TeamId(id = request.actingTeamAccount.team_id)
          campaignService.updateOptOutSettings(
            id = id,
            data = data,
            orgId = request.loggedinAccount.org.id,
            team_id = team_id
          ) match {

            case Failure(e) => Res.ServerError(s"There was a problem updating your unsubscribe message. Could you try again ? " + e.getMessage, e = Some(e))

            case Success(None) => Res.NotFoundError("Campaign not found. Could you reload and try again ?")

            case Success(Some(row)) =>
              // 8-Apr-2022: ignoring scheduling immediately on opt-out msg change
              Res.Success("Campaign unsubscribe message updated", Json.obj("campaign" -> row))

          }
      }
    }
  }


  def updateEmailSettingsV2(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(id)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>

    Future {
      given Logger: SRLogger = request.Logger
      val Res = request.Response

      val accountId = request.actingTeamAccount.user_id
      val teamId = request.actingTeamAccount.team_id

      val validateData = request.body.validate[List[CampaignEmailSettingsV2]]

      validateData match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>

          val permittedAccountIdsForEmailSetting = PermissionMethods.getPermittedAccountIdsForAccountAndPermission(
            loggedinAccount = request.loggedinAccount,
            actingAccountId = accountId,
            actingTeamId = teamId,
            version = v,
            Logger = Logger
          )(PermType.EDIT_CHANNELS)

          val senderEmailSetting = data.map(_.sender_email_settings_id).flatMap(sid => emailSettingDAO.find(sid, accountIds = permittedAccountIdsForEmailSetting, teamId = teamId))
          val receiverEmailSetting = data.map(_.receiver_email_settings_id).flatMap(sid => emailSettingDAO.find(sid, accountIds = permittedAccountIdsForEmailSetting, teamId = teamId))

          val emailValidationDisabledForCampaign = !request.campaign.settings.enable_email_validation


          if (data.isEmpty) {

            Res.BadRequestError(s"Please send atleast one of sender_email_settings_id or receiver_email_settings_id")

          } else if (senderEmailSetting.isEmpty || senderEmailSetting.length != data.length) {

            Res.BadRequestError("Provided sender_email_settings_id is either not found, or you do not have permission to use it")

          } else if (data.nonEmpty &&
            senderEmailSetting.exists(es => EmailSetting.API_BASED_EMAILSERVICEPROVIDERS.contains(es.service_provider)) &&
            emailValidationDisabledForCampaign
          ) {

            Res.BadRequestError("Using SMTP-based email accounts is not allowed in campaigns where you have disabled email validation. Please enable email validation first in Campaign Settings.")


          } else if (receiverEmailSetting.length != data.length) {

            Res.BadRequestError("Provided receiver_email_settings_id is either not found, or you do not have permission to use it")

          } else {
              Logger.info(s"CampaignController.updateEmailSettingsV2 called for aid_${request.actingTeamAccount.user_id} : tid_${request.actingTeamAccount.team_id} cid_$id : $data")

              campaignService.updateEmailSettingsV2(
                id = CampaignId(id = id), 
                teamId = TeamId(id = teamId),
                data = data, 
                campaign_status = request.campaign.status,
                planID = request.loggedinAccount.org.plan.plan_id,
                orgId = OrgId(request.loggedinAccount.org.id)
              ) match {

                  case Failure(e) => Res.ServerError("Problem while updating email accounts for your campaign. Could you try again ? " + e.getMessage, e = Some(e))

                  case Success(row) =>

                      row match {
                          case Left(CampaignAddEmailChannelError.MaxEmailSettingLimitError) =>
                              Res.BadRequestError("Maximum limit to add sender emails exceeded")

                          case Left(CampaignAddEmailChannelError.DbFailure(e)) =>
                              Res.ServerError("Error occured while updating emails", e=Some(e))
                              
                          case Right(None) => Res.NotFoundError("Campaign could not be found. Could you reload and try again ?")

                          case Right(Some(row)) =>

                              // schedule immediately when campaign's emails etc. settings are updated
                              campaignService.setNextToBeScheduledAt(

                                  flowData = CampaignSetNextToBeScheduledAtData.UpdateCampaignSettingFlow(
                                      campaignSettings = request.campaign.settings,
                                      campaignId = CampaignId(request.campaign.id),
                                      teamId = TeamId(request.campaign.team_id)
                                  ),

                                  Logger = Logger,
                                  team_id = TeamId(id = teamId)
                              ) match {

                                  case Failure(e) =>
                                      Res.ServerError("Error while updating campaign settings [2]. Please try again or contact support.", e = Some(e))

                                  case Success(_) =>
                                      Res.Success("Congrats! Email accounts have been updated for your campaign.", Json.obj("campaign" -> row))

                              }


                      }

              }
          }

      }
    }

  }


  def startWarmup(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(id)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>

    Future {
      given Logger: SRLogger = request.Logger
      val Res = request.Response

      request.body.validate[StartCampaignWarmup] match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>
          val team_id: TeamId = TeamId(request.actingTeamAccount.team_id)
          campaignService.startWarmup(id = id, data = data, team_id = team_id)
          match {

            case Failure(e) => Res.ServerError("Error while starting warmup: " + e.getMessage, e = Some(e))

            case Success(None) => Res.NotFoundError("Campaign not found. Could you reload and try again?")

            case Success(Some(row)) => Res.Success("Success! Campaign soft-start has been activated", Json.obj("campaign" -> row))

          }

      }
    }
  }

  def stopWarmup(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(id)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>

    Future {
      given Logger: SRLogger = request.Logger
      val Res = request.Response
      val team_id = TeamId(id = request.actingTeamAccount.team_id)

      campaignService.stopWarmup(id = id, team_id = team_id) match {

        case Failure(e) => Res.ServerError("Error while stoping warmup: " + e.getMessage, e = Some(e))

        case Success(None) => Res.NotFoundError("Campaign not found. Could you reload and try again?")

        case Success(Some(row)) => Res.Success("Success! Campaign soft-start has been disabled. Campaign will send emails at the full sending limit now.", Json.obj("campaign" -> row))

      }
    }
  }

  def findStepsByCampaign(v: String, campaignId: Int, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.VIEW_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(campaignId)
    ).async { (request: CampaignRequest[AnyContent]) =>

    Future {
      given Logger: SRLogger = request.Logger
      val Res = request.Response

      val campaign = request.campaign

      campaignStepService
        .findStepsByCampaign(
          campaign = campaign
        ) match {

        case None =>

          Res.Success("Campaign does not have any steps", Json.obj("campaign_steps" -> JsArray()))

        case Some(orderedSteps) =>

          Res.Success("Find here the steps associated with your campaign", Json.obj("campaign_steps" -> orderedSteps))
      }


    }

  }


  def previewGetProspects(v: String, campaignId: Long, page: Int, q: Option[String], aid: Option[Long], tid: Option[Long], cesid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.VIEW_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(campaignId)
    ).async { (request: CampaignRequest[AnyContent]) =>

    given Logger: SRLogger = request.Logger
    val Res = request.Response

    val campaign = request.campaign
    val org = request.loggedinAccount.org

    val permittedAccountIdsForVIEW_PROSPECT = PermissionMethods.getPermittedAccountIdsForAccountAndPermission(
      loggedinAccount = request.loggedinAccount,
      actingTeamId = request.actingTeamAccount.team_id,
      actingAccountId = request.actingTeamAccount.user_id,
      version = v,
      Logger = Logger
    )(permission = PermType.VIEW_PROSPECTS)

    if (campaign.head_step_id.isEmpty) {

      Future.successful(Res.BadRequestError("Campaign does not have any steps yet. You need to add step in the campaign before you can preview."))

    } else if (!permittedAccountIdsForVIEW_PROSPECT.contains(request.actingTeamAccount.user_id)) {

      Future.successful(Res.BadRequestError("You don't have permission to view prospect"))

    } else {


      val rowsPerPage = 25
      val limit = rowsPerPage
      val offset = (page - 1) * rowsPerPage

      campaignService.getPreviewProspects(
        campaign = request.campaign,
        searchQuery = q,
        limit = limit,
        offset = offset,
        teamId = TeamId(id = request.actingTeamAccount.team_id),
        orgId = OrgId(org.id),
        enable_ab_testing = org.settings.enable_ab_testing,
        camapign_email_setting_id = cesid.map(id => CampaignEmailSettingsId(id))
      ).map {

        case Left(PreviewGetProspectError.InternalServerError(e)) =>
          Res.ServerError(API_MSGS.ERROR_INTERNAL_SERVER, e = Some(e))

        case Left(PreviewGetProspectError.ValidationError(err_message)) =>
          Res.BadRequestError(err_message)

        case Right(prospectListForPreview) =>
          Res.Success("Checkout the email previews", Json.obj("page" -> page, "prospects" -> prospectListForPreview))
      }


    }
  }

  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  // GET     /api/:v/campaigns/:campaignId/previews/prospects/:prospectId
  def previewGetStepsForProspect(v: String, campaignId: Long, prospectId: Long, aid: Option[Long], tid: Option[Long], cesid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.VIEW_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(campaignId)
    ).async { (request: CampaignRequest[AnyContent]) =>

    given Logger: SRLogger = request.Logger
    val Res = request.Response
    val data = request.body.asJson.map(value => value.as[List[StepAndVariantId]]).getOrElse(List())
    val allTrackingDomainsUsed = repTrackingHostService.getRepTrackingHosts().get.map(_.host_url)

    val permittedAccountIdsForVIEW_TASKS = PermissionMethods.getPermittedAccountIdsForAccountAndPermission(
      loggedinAccount = request.loggedinAccount,
      actingTeamId = request.actingTeamAccount.team_id,
      actingAccountId = request.actingTeamAccount.user_id,
      version = v,
      Logger = Logger
    )(permission = PermType.VIEW_TASKS)

    campaignPreviewService.previewGetStepsForProspect(
      campaign = request.campaign,
      prospectId = prospectId,
      teamId = request.actingTeamAccount.team_id,
      enable_ab_testing = request.loggedinAccount.org.settings.enable_ab_testing,
      allTrackingDomainsUsed = allTrackingDomainsUsed,
      org = request.loggedinAccount.org,
      stepAndVariantIds = data,
      permittedAccountIdsForViewingTasks = permittedAccountIdsForVIEW_TASKS,
      selected_campaign_email_setting_id = cesid.map(id => CampaignEmailSettingsId(id))
    ) map {

      case Left(PreviewGetStepsForProspectError.NoStepInCampaign) =>

        Res.BadRequestError("Campaign does not have any step")

      case Left(PreviewGetStepsForProspectError.ProspectNotFoundInCampaign) =>

        Res.NotFoundError("Prospect not found in campaign")

      case Left(PreviewGetStepsForProspectError.ValidationError(errMessage)) =>

        Res.BadRequestError(errMessage)

      case Left(PreviewGetStepsForProspectError.ErrorWhileGettingProspectObjects(e)) =>

        Res.ServerError(API_MSGS.ERROR_INTERNAL_SERVER, e = Some(e))

      case Left(PreviewGetStepsForProspectError.InternalServerError(e)) =>
        Res.ServerError(API_MSGS.ERROR_INTERNAL_SERVER, e = Some(e))


      case Right(res) =>


        Res.Success("Checkout the email previews", Json.toJson(res))


    }
  }

  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  // POST     /api/:v/campaigns/:campaignId/previews/prospects/:prospectId/steps/:stepId
  def previewUpdateEmailForProspect(v: String, campaignId: Long, prospectId: Long, stepId: Long, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(campaignId)
      andThen hasStep(stepId = stepId)
    ).async(parse.json) { (request: CampaignStepRequest[JsValue]) =>

    Future {

      given Logger: SRLogger = request.Logger
      val Res = request.Response
      val editedBody = (request.body \ "edited_body").asOpt[String]
      val editedSubject = (request.body \ "edited_subject").asOpt[String]
      val teamId = TeamId(id = request.actingTeamAccount.team_id)

      if (editedBody.isEmpty || editedSubject.isEmpty) {
        Res.BadRequestError("Body / subject cannot be empty")
      } else {

        prospectDAOService.find(

          byCampaignId = Some(request.campaign.id),
          byProspectIds = Seq(prospectId),
          teamId = request.actingTeamAccount.team_id,
          limit = 1,
          Logger = Logger
        ) match {
          case Failure(exception) =>
            Logger.shouldNeverHappen(s"failed prospectDAOService.find", Some(exception))
            Res.ServerError("Sorry, there was an internal error. Please try again later", Some(exception))

          case Success(Seq()) =>
            Res.NotFoundError("Prospect not found in campaign")

          case Success(_) =>

            Try {

              /* 26 March 2024

                  Found one bug :

                  1. when we are scheduling a manual email step from a campaign. and once tasks are created
                  2. we go and edit the preview for one prospect.
                  3. It deletes the unsent emails_scheduled not tasks.
                  4. now when scheduler picks the prospect it tries to create task and error occurs for prevention of duplicate task.

                  to fix this we need to delete unDone tasks when preview is edited for prospect.

                  // delete manual emails_scheduled from this flow. so no is_manual_task = false required.

               */

              // on add/edit preview, remove unsent emails from emails_scheduled
              selectAndPublishForDeletionService.selectAndPublishForDeletion(
                deletion_reason = DeletionReason.PreviewUpdatedForEmail,
                deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByProspectId(
                  prospectIds = Seq(prospectId),
                  campaignId = campaignId,
                  prospectAccountIds = None,
                  replyHandling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
                  teamId = teamId)
              )
            } match {

              case Failure(e) =>
                Res.ServerError("There was an error. Please try again, or contact support", e = Some(e))

              case Success(_) =>
                campaignEditedPreviewEmailDAO.create(
                  campaignId = campaignId,
                  prospectId = prospectId,
                  stepId = stepId,
                  editedByAccountId = request.actingTeamAccount.user_id,
                  editedBody = editedBody.get,
                  editedSubject = editedSubject.get

                ) match {

                  case Failure(e) =>
                    Res.ServerError("There was an error. Please try again, or contact support", e = Some(e))

                  case Success(_) =>
                    Res.Success("Preview updated", Json.obj())

                }
            }
        }

      }
    }
  }


  def createVariant(v: String, campaignId: Long, stepId: Long, aid: Option[Long], tid: Option[Long]) = (

    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(campaignId)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>

    given Logger: SRLogger = request.Logger
    val Res = request.Response

    val ta = request.actingTeamAccount
    val c = request.campaign

    /*
        var stepIdLocal: Long = stepId
        var createVariantBadRequestError: Option[String] = None
        var createVariantServerError: Option[(String, Throwable)] = None
        var createVariantInvalidParamError: Option[(String, Throwable)] = None
        var createVariantNotFoundError: Option[String] = None
        var createVariantJsValidationError: Option[Seq[(JsPath, Seq[ValidationError])]] = None

        if (stepIdLocal == 0) {

          request.body.validate[CampaignStepCreate] match {

            case JsError(e) =>
              createVariantJsValidationError = Some(e)

            case JsSuccess(data, _) =>


              if (data.template_id.isDefined && data.template_is_from_library.isEmpty) {

                createVariantBadRequestError = Some("Please send if template_is_from_library")

              } else {

                campaignTemplateService.validateCampaignTemplate(
                  stepId = Some(stepId),
                  head_step_id = request.campaign.head_step_id,
                  teamId = ta.team_id,
                  emailBody = data.body,
                  emailSubject = data.subject
                ) match {
                  case Left(ValidateCampaignTemplateError.BodyCantHavePreviousSubject) =>

                    createVariantBadRequestError = Some("Body cant have previous_subject")

                  case Left(ValidateCampaignTemplateError.SubjectCantHaveSignature) =>

                    createVariantBadRequestError = Some("Subject cant have signature")

                  case Left(ValidateCampaignTemplateError.PreviousSubInFirstEmail) =>

                    createVariantBadRequestError = Some("previous_subject cant be used for first email")

                  case Left(ValidateCampaignTemplateError.ErrorWhileValidatingSubject(e)) =>
        var createVariantJsValidationError: Option[Seq[(JsPath, Seq[JsonValidationError])]] = None
    */

    request.body.validate[CampaignStepVariantCreateOrUpdate] match {

      case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

      case JsSuccess(data, _) =>

        campaignStepService
          .createVariant(
            orgId = request.loggedinAccount.org.id,
            data = data,
            teamId = ta.team_id,
            userId = ta.user_id,
            taId = ta.ta_id,
            stepId = stepId,
            campaignId = campaignId,
            campaignHeadStepId = c.head_step_id
          )
          .map {
            case Left(err) =>

              err match {

                case CreateCampaignStepVariantError.CampaignStepVariantValidationError(er) =>

                  er match {

                    case ValidateCampaignStepVariantError.CallToActionCantBeEmpty =>
                      Res.BadRequestError("Magic content call to action cannot be empty.")

                    case ValidateCampaignStepVariantError.StepDetailsCantBeEmpty =>
                      Res.BadRequestError("Magic content step details cannot be empty.")

                    case ValidateCampaignStepVariantError.LinkedinInmailBodyCantBeEmpty =>
                      Res.BadRequestError("Linkedin Inmail Body Cannot be Empty.")

                    case ValidateCampaignStepVariantError.GeneralTaskNotesCantBeEmpty =>
                      Res.BadRequestError("General Task cannot have empty notes.")

                    case ValidateCampaignStepVariantError.EmailBodyCantBeEmpty =>
                      Res.BadRequestError("Email Body Cannot be Empty.")

                    case ValidateCampaignStepVariantError.EmailSubjectCantBeEmpty =>
                      Res.BadRequestError("Email Subject Cannot be Empty.")

                    case ValidateCampaignStepVariantError.PhoneMessageBodyCantBeEmpty =>
                      Res.BadRequestError("Phone Message Body Cannot be Empty.")

                    case ValidateCampaignStepVariantError.CallTaskBodyCantBeEmpty =>
                      Res.BadRequestError("Call Task Body Cannot be Empty.")

                    case ValidateCampaignStepVariantError.LinkedinMessageBodyCantBeEmpty =>
                      Res.BadRequestError("Linkedin Message Body cannot be Empty.")

                    case ValidateCampaignStepVariantError.WhatsappMessageBodyCantBeEmpty =>
                      Res.BadRequestError("Whatsapp Message Body cannot be empty.")

                    case ValidateCampaignStepVariantError.LinkedinInmailSubjectTooLong =>
                      Res.BadRequestError(s"Linkedin in-mail subject shouldn't be more than ${AppConfig.MultiChannel.linkedin_in_mail_subject_character_limit} characters ")

                    case ValidateCampaignStepVariantError.LinkedinInmailBodyTooLong =>
                      Res.BadRequestError(s"Linkedin in-mail message body shouldn't be more than ${AppConfig.MultiChannel.linkedin_in_mail_message_body_character_limit} characters ")

                    case ValidateCampaignStepVariantError.LinkedinMessageBodyTooLong =>
                      Res.BadRequestError(s"Linkedin in-mail message body shouldn't be more than ${AppConfig.MultiChannel.linkedin_message_body_character_limit} characters ")

                    case ValidateCampaignStepVariantError.WhatsappMessageBodyTooLong =>
                      Res.BadRequestError(s"Whatsapp  message body shouldn't be more than ${AppConfig.MultiChannel.whatsapp_message_body_character_limit} characters ")

                    case ValidateCampaignStepVariantError.LinkedinConnectionRequestAddNoteTooLong =>
                      Res.BadRequestError(s"Linkedin connection request add note shouldn't be more than ${AppConfig.MultiChannel.linkedin_connection_request_add_note_character_limit} characters")
                  }

                case CreateCampaignStepVariantError.ErrorWhileCreateCampaignFirst(er) =>

                  er match {

                    case CreateCampaignFirstStepError.CampaignStepCreateValidationError(errors) =>

                      Res.JsValidationError(errors)

                    case CreateCampaignFirstStepError.TemplateIsFromLibraryFieldMissing =>


                      Res.BadRequestError("Please send if template_is_from_library")


                    case data: CreateCampaignFirstStepError.ValidateCampaignTemplateError =>

                      Logger.fatal(s"campaignStepData: ${data.campaignStepData}", err = data.e)

                      Res.BadRequestError(data.e.getMessage)


                    case CreateCampaignFirstStepError.TemplateAccessCheckError(e) =>

                      Res.ServerError("Error while checking Team has access to template", Some(e))

                    case CreateCampaignFirstStepError.TemplateNotFoundError(errMsg) =>

                      Res.NotFoundError(errMsg)


                    case CreateCampaignFirstStepError.CreateStepSQLException(e) =>

                      Res.ServerError(s"There was problem creating the step. Could you try again ? ${e.getMessage}", Some(e))

                    case CreateCampaignFirstStepError.CreatedStepNotFound =>

                      Res.ServerError(
                        s"There was problem creating the step. Could you try again ?",
                        e = Some(new Exception("No campaign step found after creating"))
                      )


                    case data: CreateCampaignFirstStepError.ParentStepNotFound =>


                      Res.InvalidParamError(
                        s"Step with parent_id: ${data.parent_step_id}, belonging to campaign, not found",
                        "parent_id"
                      )

                  }
                case CreateCampaignStepVariantError.CampaignStepNotFound =>

                  Res.BadRequestError("Invalid Step")

                case CreateCampaignStepVariantError.MaxVariantsExceeded(maxVariants: Int) =>

                  Res.BadRequestError(s"SmartReach allows upto $maxVariants variants for each step.")


                case CreateCampaignStepVariantError.TemplateIsFromLibraryFieldMissing =>

                  Res.BadRequestError("Please send if template_is_from_library")


                case CreateCampaignStepVariantError.ValidateCampaignTemplateError(err: Throwable) =>

                  Res.BadRequestError(err.getMessage)

                case CreateCampaignStepVariantError.TemplateAccessCheckError(e: Throwable) =>

                  Res.ServerError("Please try again or contact support", e = Some(e))

                case CreateCampaignStepVariantError.TemplateNotFoundError(errMsg: String) =>

                  Res.NotFoundError(errMsg)

                case CreateCampaignStepVariantError.CreatedStepVariantNotFound =>

                  Res.ServerError("Error while creating campaign step variant", e = None)

                case CreateCampaignStepVariantError.FeatureUsageEventSaveError(e) =>
                  Res.ServerError(err = e)

                case CreateCampaignStepVariantError.CreateStepVariantException(e: Throwable) =>

                  Res.ServerError(s"Error while creating campaign step variant: ${e.getMessage}", e = Some(e))

              }

            case Right(campaignStepVariant) =>
              Res.Success("Campaign step variant saved", Json.obj(
                "step_id" -> campaignStepVariant.step_id,
                "variant_id" -> campaignStepVariant.id,
              ))

          }
          .recover { case e =>
            Res.ServerError(err = e)
          }

    }
  }


  def updateVariant(v: String, campaignId: Int, stepId: Long, variantId: Int, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(campaignId)
      andThen hasStep(stepId = stepId)
      andThen hasVariant(variantId = variantId)
    ).async(parse.json) { (request: CampaignStepVariantRequest[JsValue]) =>


    given Logger: SRLogger = request.Logger
    val Res = request.Response
    val t = request.actingTeamAccount

    val validatedata = request.body.validate[CampaignStepVariantCreateOrUpdate]

    validatedata match {
      case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

      case JsSuccess(data, _) =>
        campaignStepService.updateVariant(
          orgId = request.loggedinAccount.org.id,
          data = data,
          t = t,
          variantId = variantId,
          stepId = stepId,
          campaignHeadStepId = request.campaign.head_step_id,
          campaignId = campaignId,
        ).map {
          case Left(err) => err match {

            case UpdateVariantError.CampaignStepVariantValidationError(er) =>

              er match {

                case ValidateCampaignStepVariantError.CallToActionCantBeEmpty =>
                  Res.BadRequestError("Magic content call to action cannot be empty.")

                case ValidateCampaignStepVariantError.StepDetailsCantBeEmpty =>
                  Res.BadRequestError("Magic content step details cannot be empty.")

                case ValidateCampaignStepVariantError.LinkedinInmailBodyCantBeEmpty =>
                  Res.BadRequestError("Linkedin Inmail Body Cannot be Empty.")

                case ValidateCampaignStepVariantError.GeneralTaskNotesCantBeEmpty =>
                  Res.BadRequestError("General Task cannot have empty notes.")

                case ValidateCampaignStepVariantError.EmailBodyCantBeEmpty =>
                  Res.BadRequestError("Email Body Cannot be Empty.")

                case ValidateCampaignStepVariantError.EmailSubjectCantBeEmpty =>
                  Res.BadRequestError("Email Subject Cannot be Empty.")

                case ValidateCampaignStepVariantError.PhoneMessageBodyCantBeEmpty =>
                  Res.BadRequestError("Phone Message Body Cannot be Empty.")

                case ValidateCampaignStepVariantError.CallTaskBodyCantBeEmpty =>
                  Res.BadRequestError("Call Task Body Cannot be Empty.")

                case ValidateCampaignStepVariantError.LinkedinMessageBodyCantBeEmpty =>
                  Res.BadRequestError("Linkedin Message Body cannot be Empty.")

                case ValidateCampaignStepVariantError.WhatsappMessageBodyCantBeEmpty =>
                  Res.BadRequestError("Whatsapp Message Body cannot be empty.")

                case ValidateCampaignStepVariantError.LinkedinInmailSubjectTooLong =>
                  Res.BadRequestError(s"Linkedin in-mail subject shouldn't be more than ${AppConfig.MultiChannel.linkedin_in_mail_subject_character_limit} characters ")

                case ValidateCampaignStepVariantError.LinkedinInmailBodyTooLong =>
                  Res.BadRequestError(s"Linkedin in-mail message body shouldn't be more than ${AppConfig.MultiChannel.linkedin_in_mail_message_body_character_limit} characters ")

                case ValidateCampaignStepVariantError.LinkedinMessageBodyTooLong =>
                  Res.BadRequestError(s"Linkedin in-mail message body shouldn't be more than ${AppConfig.MultiChannel.linkedin_message_body_character_limit} characters ")

                case ValidateCampaignStepVariantError.WhatsappMessageBodyTooLong =>
                  Res.BadRequestError(s"Whatsapp  message body shouldn't be more than ${AppConfig.MultiChannel.whatsapp_message_body_character_limit} characters ")

                case ValidateCampaignStepVariantError.LinkedinConnectionRequestAddNoteTooLong =>
                  Res.BadRequestError(s"Linkedin connection request add note shouldn't be more than ${AppConfig.MultiChannel.linkedin_connection_request_add_note_character_limit} characters")

              }

            case UpdateVariantError.TemplateIsFromLibraryError =>
              Res.BadRequestError("Please send if template_is_from_library")

            case UpdateVariantError.ValidateTemplateError(e) =>
              Res.BadRequestError(e.getMessage)

            case UpdateVariantError.ErrorWhileCheckTeamHasAccessError(e) =>
              Res.ServerError("Error while checking Team has access to template", e = e)

            case UpdateVariantError.TemplateNotFoundWhileUpdateError(errMsg) =>
              Res.NotFoundError(errMsg)

            case UpdateVariantError.StepVariantNotFoundError =>
              Res.NotFoundError("Step Variant not found. Could check and try again ?")

            case UpdateVariantError.UpdateVariantServerError(e) =>
              Res.ServerError(s"Error while updating step variant. Could you try again ? ${e.getMessage}", e = Some(e))

            case UpdateVariantError.CampaignStepNotFound =>
              Res.NotFoundError("Campaign Step not found. Could you check and try again ?")

            case UpdateVariantError.CannotUpdateStepType =>
              Res.BadRequestError("Cannot update Step Type.")

          }
          case Right(updatedCampaignStepVariant) =>

            Res.Success(s"Step Variant details have been updated", Json.obj(
              "step_id" -> updatedCampaignStepVariant.step_id,
              "variant_id" -> updatedCampaignStepVariant.id,
            ))

        }
    }

  }

  def updateVariantStatus(
                           v: String,
                           campaignId: Long,
                           stepId: Long,
                           variantId: Long,
                           aid: Option[Long],
                           tid: Option[Long]
                         ) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(campaignId)
      andThen hasStep(stepId = stepId)
      andThen hasVariant(variantId = variantId)
    )(parse.json) { (request: CampaignStepVariantRequest[JsValue]) =>

    val Logger = request.Logger
    val Res = request.Response
    val newActiveStatus = (request.body \ "active").asOpt[Boolean]
    val variants = campaignStepVariantDAO.findByStepId(stepId = stepId)
    val activeVariantsInStep = variants.filter(_.active)

    if (newActiveStatus.isEmpty) {

      Res.BadRequestError("Send 'active' status")

    } else if (request.variant.active == newActiveStatus.get) {

      Res.BadRequestError(s"Variant active status is already: ${newActiveStatus.get}")

    } else if (newActiveStatus.get == false && activeVariantsInStep.length <= 1) {

      Logger.warn(s"aid: $aid wants to disable head variant, current active variants count in step is:  ${activeVariantsInStep.size}")

      Res.BadRequestError(s"A campaign step must have at least one active variant to run. You can not disable the only active variant in this campaign step.")

    } else {

      campaignStepVariantDAO.updateVariantStatus(
        active = newActiveStatus.get,
        campaignId = campaignId,
        stepId = stepId,
        variantId = variantId
      ) match {
        case Failure(e) =>
          Res.ServerError("There was an error. Please try again, or contact support.", e = Some(e))

        case Success(None) =>
          Res.BadRequestError("Variant not found. Please try again.")

        case Success(Some(updatedStatus)) =>
          Res.Success(s"Variant sending ${if (updatedStatus) "resumed" else "paused"}", Json.obj())
      }
    }
  }

  def reorderCampaignSteps(
                            v: String,
                            campaignId: Long,
                            tid: Option[Long]
                          ): Action[JsValue] = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CAMPAIGNS,
    tidOpt = tid
  )
    andThen hasCampaign(campaignId)
    )(parse.json) { (request: CampaignRequest[JsValue]) =>

    given logger: SRLogger = request.Logger

    val Res = request.Response

    request.body.validate[ReorderCampaignStepData] match {

      case JsError(errors) =>

        logger.error(msg = s"Invalid ReorderCampaignStepData. errors: $errors")

        Res.JsValidationError(errors = errors)

      case JsSuccess(reorderCampaignStepData, _) =>

        val campaignId = CampaignId(id = request.campaign.id)

        val teamId = TeamId(id = request.actingTeamAccount.team_id)

        val stepIdToBeReordered = reorderCampaignStepData.step_id_to_be_reordered

        val newParentStepId = reorderCampaignStepData.new_parent_step_id

        campaignStepService.reorderCampaignSteps(
          campaignId = campaignId,
          teamId = teamId,
          stepIdToBeReordered = stepIdToBeReordered,
          newParentStepId = newParentStepId,
        ) match {

          case Failure(exception) =>

            logger.error(
              msg = s"Failed to reorder campaign steps. campaignId: $campaignId :: teamId: $teamId :: stepIdToBeReordered: $stepIdToBeReordered :: newParentStepId: $newParentStepId",
              err = exception
            )

            Res.ServerError(message = "Failed to reorder campaign steps.", e = None)

          case Success(Left(errMsg)) =>

            logger.error(
              msg = s"Failed to reorder campaign steps - $errMsg. campaignId: $campaignId :: teamId: $teamId :: stepIdToBeReordered: $stepIdToBeReordered :: newParentStepId: $newParentStepId",
            )

            Res.BadRequestError(message = errMsg)

          case Success(Right(updateCount)) =>

            Res.Success(
              message = "Campaign steps have been reordered. Please verify the delay that you have set between each step.",
              data = Json.toJson(updateCount)
            )

        }

    }

  }

  // SENDER_ROTATION
  // to delete variants of a step in a campaign
  // calling campaignStepVariantDAO.deleteV2
  def deleteVariant(v: String, campaignId: Int, stepId: Int, variantId: Int, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(campaignId)
      andThen hasStep(stepId = stepId)
      andThen hasVariant(variantId = variantId)
    ).async(parse.json) { (request: CampaignStepVariantRequest[JsValue]) =>

    Future {

      given Logger: SRLogger = request.Logger
      val Res = request.Response
      val campaign = request.campaign
      val teamId = TeamId(id = request.actingTeamAccount.team_id)
      campaignService.deleteVariant(
        stepId = stepId,
        campaign = campaign,
        loggedinAccount = request.loggedinAccount,
        campaignId = campaignId,
        step = request.step,
        variantId = variantId,
        teamId = teamId
      ) match {
        case Failure(e) =>

          e.getMessage match {

            case CONSTANTS.API_MSGS.SINGLE_STEP_DELETION_IS_NOT_ALLOWED =>

              Res.BadRequestError(CONSTANTS.API_MSGS.SINGLE_STEP_DELETION_IS_NOT_ALLOWED)

            case CONSTANTS.API_MSGS.ONE_ACTIVE_VARIANT_MUST_BE_PRESENT_WITH_STEP =>

              Res.BadRequestError(CONSTANTS.API_MSGS.ONE_ACTIVE_VARIANT_MUST_BE_PRESENT_WITH_STEP)

            case _ =>

              Res.ServerError(s"Oops! There was a problem while deleting this step variant. Could you try again ?", e = Some(e))

          }

        case Success(None) =>
          Res.NotFoundError("Variant not found. Could you check once and try again ?")

        case Success(Some(value)) =>
          Res.Success(s"Step variant has been deleted", Json.obj())

      }
    }
  }


  def unlinkTemplateFromVariant(v: String, campaignId: Int, stepId: Int, variantId: Int, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(campaignId)
      andThen hasStep(stepId = stepId)
      andThen hasVariant(variantId = variantId)
    ).async(parse.json) { (request: CampaignStepVariantRequest[JsValue]) =>

    Future {

      val Logger = request.Logger
      val Res = request.Response
      // we are updating by checking variantID, stepId and campaignId, and if have already checked 'hasCampaign'
      // so account should be owning the step

      campaignStepVariantDAO.unlinkTemplateFromVariant(
        id = variantId,
        stepId = stepId,
        campaignId = campaignId
      ) match {

        case Failure(e) => Res.ServerError(s"Error while unlinking template from step. Could you try again ?", e = Some(e))

        case Success(None) => Res.NotFoundError("Step Variant not found. Could check and try again ?")

        case Success(Some(row)) => Res.Success(s"Template has been unlinked", Json.obj())

      }

    }

  }


  // Creating a copy of a given Campaign down to settings and email setting ids
  def duplicateCampaign(v: String, campaignId: Int, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(campaignId)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>

    val ta = request.actingTeamAccount
    val c = request.campaign

    given Logger: SRLogger = request.Logger
    val Res = request.Response


    campaignService.duplicateCampaign(
      ta = ta,
      loggedinAccount = request.loggedinAccount,
      campaign = c,
      version = v,
      permittedAccountIds = request.permittedAccountIds
    ).map {
      case Left(DuplicateCampaignError.NoStepsFoundError) =>
        Res.ServerError("Campaign does not have any steps", e = None)

      case Left(DuplicateCampaignError.CreateCampaignError(err)) =>
        err match {
          case CampaignCreationError.CampaignNameNotUniqueError(e) =>
            Res.BadRequestError("Another campaign with the given name exists. Please try creating the campaign with a different name.")

          case CampaignCreationError.SQLException(e) =>
            Res.ServerError(s"Error while creating campaign.", e = Some(e))

          case CampaignCreationError.CampaignNotCreated =>
            Res.ServerError("Error while creating campaign. Could you try again ?", e = None)

          case CampaignCreationError.FeatureUsageEventSaveError(e) =>
            Res.ServerError(err = e)

          case CampaignCreationError.InCorrectCampaignOwnerId =>
            Res.BadRequestError("Incorrect campaign owner id passed. Please try again")
        }

      case Left(DuplicateCampaignError.DbFailure(e)) =>
        Res.ServerError(s"Error while duplicating campaign. Please try again. (${e.getMessage})", e = Some(e))

      case Left(DuplicateCampaignError.CampaignWithStatsAndEmailNotFound) =>
        Res.ServerError(s"Error while duplicating campaign. Please try again.", e = None)

      case Right(updateCampaignOptout) =>
        Res.Success("Congrats! A new campaign has been added. Please check steps & settings, add prospects & start the campaign!", Json.obj("campaign" -> updateCampaignOptout))

    }.recover(e =>
      Res.ServerError(s"Error while duplicating campaign. Please try again. (${e.getMessage})", e = Some(e))

    )
  }

  // use case : testStep POST    /api/:v/campaigns/:id/steps/send_test
  def testStep(v: String, campaignId: Int, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(campaignId)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>

    given Logger: SRLogger = request.Logger
    val Res = request.Response

    if (request.loggedinAccount.org.plan.plan_type == PlanType.INACTIVE) {

      val msg = "You do not have an active subscription. To send test emails, please upgrade your plan under Settings -> Billing"

      Future.successful(
        Res.BadRequestError(msg)
      )

    } else {

      request.body.validate[CampaignStepTest] match {

        case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

        case JsSuccess(data, _) =>

          val ta = request.actingTeamAccount
          val allTrackingDomainsUsed = repTrackingHostService.getRepTrackingHosts().get.map(_.host_url)
          val acc: Account = request.loggedinAccount

          campaignTestStepService.testStep(
            stepId = data.id,
            body = data.body,
            subject = data.subject,
            campaign = request.campaign,
            toEmail = data.to_email,
            campaignId = CampaignId(campaignId.toLong),
            stepType = data.step_type,
            allTrackingDomainsUsed = allTrackingDomainsUsed,
            campaign_email_settings_id = data.campaign_email_settings_id,
            team_id = TeamId(ta.team_id),
            owner_id = AccountId(ta.user_id),
            ta_id = ta.ta_id,
            owner_name = Helpers.getAccountName(a = acc),
            owner_email = AccountEmail(acc.email),
            calendar_account_data = acc.calendar_account_data,
            emailSendingFlow = None,
            org_id = OrgId(acc.org.id)
          ) map {
            case Left(GetTestStepsError.ErrorWhileValidateCampaignTemplate(err)) =>
              err match {
                case ValidateCampaignTemplateError.BodyCantHavePreviousSubject(err) =>

                  Res.BadRequestError(err.getMessage)

                case ValidateCampaignTemplateError.SubjectCantHaveSignature(err) =>
                  Res.BadRequestError(err.getMessage)

                case ValidateCampaignTemplateError.PreviousSubInFirstEmail(err) =>

                  Res.BadRequestError(err.getMessage)

                case ValidateCampaignTemplateError.ErrorWhileValidatingBody(e) =>

                  Res.BadRequestError(e.getMessage)

                case ValidateCampaignTemplateError.ErrorWhileValidatingSubject(e) =>

                  Res.BadRequestError(e.getMessage)

                case ValidateCampaignTemplateError.ErrorWhileValidatingNotes(e) =>

                  Res.BadRequestError(e.getMessage)

                case ValidateCampaignTemplateError.NotesCantHavePreviousSubject(err) =>

                  Res.BadRequestError(err.getMessage)
              }
            case Left(GetTestStepsError.InvalidEmailTemplate) =>

              Res.BadRequestError("Invalid Email template")

            case Left(GetTestStepsError.ReceiveReplyToEmailNotGiven) =>

              Res.BadRequestError("Please integrate the From and Receive-reply-to email accounts for the campaign before sending test")

            case Left(GetTestStepsError.ErrorWhileGettingEmailBody(e)) =>

              Res.ServerError(API_MSGS.INVALID_SYNTAX_IN_EMAIL, e = Some(e))

            case Left(GetTestStepsError.ErrorWhileInsertingMultipleForCampaign(e)) =>

              Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + " :" + e.getMessage, e = Some(e))

            case Left(GetTestStepsError.EmailNotSent(e)) =>
              if (e.isEmpty) {
                Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + ": your email account is not connected properly. Please reconnect, or contact support", e = None)
              } else {
                Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + " :" + e.get.getMessage, e = e)
              }

            case Left(GetTestStepsError.ErrorWhileGettingProspectCategoryId(err)) =>
              Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + " :" + err.getMessage, e = Some(err))

            case Right(_) =>
              Res.Success(s"Sent test email to: ${data.to_email}", Json.obj())
          }
      }
    }
  }

  // use case : CampaignController.processTestStep
  def processTestStep(v: String) = Action.async {
    request =>

      val logRequestIdFromReq = if (request.body.asJson.isDefined) (request.body.asJson.get \ "log_request_id").asOpt[String] else None
      val logRequestId = logRequestIdFromReq.getOrElse(StringUtils.genLogTraceId)

      val email_scheduled_id = if (request.body.asJson.isDefined) (request.body.asJson.get \ "email_scheduled_id").asOpt[String].flatMap(ParseUtils.parseLong) else None
      val team_id = if (request.body.asJson.isDefined) (request.body.asJson.get \ "team_id").asOpt[Long] else None

      val service_provider = if (request.body.asJson.isDefined) (request.body.asJson.get \ "service_provider").asOpt[EmailServiceProvider] else None

      val rep_mail_server_reverse_dns = if (request.body.asJson.isDefined) (request.body.asJson.get \ "rep_mail_server_reverse_dns").asOpt[String].getOrElse("") else ""
      val logTraceId = s"${StringUtils.genLogTraceId}_th_${Thread.currentThread().getName}"
      given Logger: SRLogger = new SRLogger(
        logRequestId = s"CampaignController.processTestStep: email_scheduled_id_$email_scheduled_id rep_mail_server_reverse_dns: $rep_mail_server_reverse_dns : logRequestId: $logRequestId ",
        customLogTraceId = Some(logTraceId)
      )

      val Res = new SRAPIResponse(Logger = Logger)

      Logger.debug(s"CampaignController.processTestStep data for test_step - request body --- ${request.body.asJson}")

      if (logRequestIdFromReq.isEmpty) {
        Logger.fatal(s"logRequestIdFromReq is empty: ${request.host}  ${request.path} ${request.rawQueryString} body: ${request.body} ")
      }

      Logger.info("start")
      if (email_scheduled_id.isEmpty || team_id.isEmpty || service_provider.isEmpty) {

        Logger.fatal(s"Invalid EmailScheduled Id email_scheduled_id: ${email_scheduled_id} or team_id $team_id or service_provider $service_provider")

        Future.successful(
          Res.BadRequestError(s"Sorry, there was an error while sending your test email. Please try again or contact support")
        )

      }
      else {

        emailSenderService.sendScheduleEmail(
            msg = MQEmailMessage(
              emailScheduledId = email_scheduled_id.get,
              logRequestId = Some(logRequestId),
              emailServiceProviderSendEmail = getEmailServiceProviderSendEmail(
                emailServiceProvider = service_provider.get
              ),
              team_id = TeamId(team_id.get)
            ),
            rep_smtp_reverse_dns_host = rep_mail_server_reverse_dns,
            sendEmailType = SendScheduleEmailType.TestEmail
          )
          .map(res => {
            Logger.success(s"EmailService.processSendEmailRequest: sent test email to: res: $res")

            Res.Success(s"Sent test email to: ${res}", Json.obj())
          })
          .recover { case e =>

            Logger.fatal(s"EmailService.processSendEmailRequest error", err = e)

            Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + ": " + e.getMessage, e = Some(e))
          }
      }

  }

  def assignProspectsV3(
                         id: SrIdentifier,
                         aid: Option[Long],
                         tid: Option[Long]
                       ): Action[JsValue] = (
    permissionUtils.checkPermission(
      version = "v3",
      permission = PermType.EDIT_PROSPECTS,
      tidOpt = tid,
      apiAccess = true
    )
      andThen hasCampaignV2(id, ApiVersion.V3)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>

    given Logger: SRLogger = request.Logger
    implicit val version: ApiVersion = ApiVersion.V3
    val Res = request.Response
    val validateData = request.body.validate[DataForCampaignAssignProspects.AssignProspectToCampaignDataV3]
    val campaign_id = request.campaign.id
    val oldCol = (request.body \ "ignore_prospects_active_in_other_campaigns").asOpt[Boolean]
    val isApiCall = request.isApiCall

    if (oldCol.isDefined) {
      Logger.debug(s"ignore_prospects_active_in_other_campaigns Sent from Frontend - ${oldCol.get}")
    }

    validateData match {

      case JsError(e) => Future.successful(
        Res.JsValidationError(errors = e,
          requestBody = Some(request.body)))

      case JsSuccess(data, _) =>

        val doer = request.loggedinAccount
        val ta = request.actingTeamAccount
        val teamId = TeamId(id = ta.team_id)

        Logger.info(s"data:: ${data}")

        val res = campaignApiService.assignProspectToCampaignV3(
          data = data,
          teamId = TeamId(id = ta.team_id),
          doer = doer,
          account_id = AccountId(id = ta.user_id),
          permittedAccountIds = request.permittedAccountIds,
          campaignId = CampaignId(id = campaign_id),
          campaignName = request.campaign.name,
          campaignSettings = request.campaign.settings
        )
        Future(

          res match {

            case Left(error) => error match {
              case AssignProspectsErrorV3.AssignProspectError(err) => err match {
                //precautionary errors

                case AssignProspectsError.ProspectAlreadyAssigned(error) =>
                  Res.BadRequestError(error)

                case AssignProspectsError.ProspectNotFound(error) =>
                  Res.BadRequestError(error)

                case AssignProspectsError.CampaignNotFound(error) =>
                  Res.BadRequestError(error)

                case AssignProspectsError.ErrorWhileAssigningProspect(error) =>
                  Res.BadRequestError(error)

                case AssignProspectsError.InvalidValueForIgnoreProspectsInOtherCampaigns(error) =>
                  Res.BadRequestError(error)
              }

              case AssignProspectsErrorV3.GetIgnoreProspectsInOtherCampaignsError(err) =>
                Logger.error(s"Error while getting the ignoreProspectsInOtherCampaigns, data sent from frontend - ${data.ignore_prospects_in_other_campaigns}", err = err)
                Res.BadRequestError(message = "Failed to Assign Prospect, Please contact Support",
                  version = ApiVersion.V3,
                  errorResponse = List(ErrorResponseProspectsAssignApi(
                    error_type = ProspectAssignErrorType.BadRequest,
                    message = "Error while assigning prospects. Please contact support.",
                    data = None
                  )))

              case AssignProspectsErrorV3.AssignProspectsValidationErrors(err) =>
                Res.BadRequestError(
                  message = "Error while validating data",
                  version = ApiVersion.V3,
                  errorResponse = err
                )

              case AssignProspectsErrorV3.FilterOwnedProspectError(err) =>
                Logger.error("[assignProspectsV3] Error while getting owned prospect data", err = err)
                Res.ServerError("There was an error. Please contact support", e = None)


              case AssignProspectsErrorV3.GetProspectIdFromUuidErrors(err) =>
                err match {
                  case GetProspectIdFromUuidError.GetProspectIdError(err) =>
                    Logger.error("[assignProspectsV3] Error while parsing uuids", err = err)
                    Res.ServerError("There was an error. Please contact support", e = None)
                }

              case AssignProspectsErrorV3.GetProspectUuidFromId(err) =>
                Logger.error("[assignProspectsV3] Error while parsing ids to get uuid", err = err)
                Res.ServerError("There was an error. Please contact support", e = None)

            }

            case Right(r: AssignProspectsResponseV3) =>

              apiService.prospectAssignApiResponse(
                data = r.totalAssignedProspectIds,
                campaign_id = CampaignId(id = campaign_id),
                teamId = teamId,
                campaign_uuid = CampaignUuid(uuid = request.campaign.uuid.get),
                org_id = OrgId(doer.org.id)
              ) match {

                case Failure(exception) =>
                  Logger.error("[assignProspectsV3] Error while fetching data.", err = exception)
                  Res.ServerError("Error while fetching data. Please contact support", e = None)

                case Success(prospect_data) =>
                  Res.Success(
                    apiVersion = version.toString,
                    message = "prospects assigned to campaign",
                    data = Json.obj(
                      "total_assigned" -> prospect_data.length,
                      "prospect_data" -> prospect_data,
                      "campaign_id" -> request.campaign.uuid)
                  )
              }
          })


    }

  }


  // FIXME [PUBLIC API]: Mentioned in [API DOCS]
  def assignProspects(v: String, campaignId: Int, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_PROSPECTS,
      tidOpt = tid,
      apiAccess = true
    )
      andThen hasCampaign(campaignId)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>


    Future {
      val Logger = request.Logger
      val Res = request.Response
      val validateData = request.body.validate[DataForCampaignAssignProspects.CampaignProspectFormIds]
      val oldCol = (request.body \ "ignore_prospects_active_in_other_campaigns").asOpt[Boolean]

      if (oldCol.isDefined) {
        Logger.debug(s"ignore_prospects_active_in_other_campaigns Sent from Frontend - ${oldCol.get}")
      }

      validateData match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>

          val doer = request.loggedinAccount
          val ta = request.actingTeamAccount


          // FORCEASSIGNISSUE
          val res = campaignApiService.assignProspectToCampaign(
            data = data,
            teamId = TeamId(id = ta.team_id),
            doer = doer,
            accountId = AccountId(id = ta.user_id),
            permittedAccountIds = request.permittedAccountIds,
            campaignId = CampaignId(id = campaignId),
            campaignName = request.campaign.name,
            campaignSettings = request.campaign.settings,
            Logger = Logger
          )

          res match {
            case Left(e: AssignProspectsError) =>
              e match {
                case AssignProspectsError.ProspectAlreadyAssigned(error) =>
                  Res.BadRequestError(error)
                case AssignProspectsError.ProspectNotFound(error) =>
                  Res.BadRequestError(error)
                case AssignProspectsError.CampaignNotFound(error) =>
                  Res.BadRequestError(error)
                case AssignProspectsError.ErrorWhileAssigningProspect(error) =>
                  Res.BadRequestError(error)
                case AssignProspectsError.InvalidValueForIgnoreProspectsInOtherCampaigns(error) =>
                  Res.BadRequestError(error)
              }
            case Right(r: AssignProspectsResponse) =>
              Logger.debug(s"CampaignController assignProspects cid_${r.campaignId} tid_${ta.team_id} total prospects added - ${r.assignedProspectIdsLength} pid- ${r.assignedProspectIds}")
              Res.Success(
                r.responseMsg,
                Json.obj("total_assigned" -> r.assignedProspectIdsLength, "prospect_ids" -> r.assignedProspectIds, "campaign_id" -> r.campaignId)
              )
          }

      }
    }

  }

  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  // NOTE: [ZAPIER] used only in zapier app
  // allow assigning one at a time
  // use case : assignProspectsByEmail          POST    /api/:v/campaigns/:id/prospects/by_email
  def assignProspectsByEmail(v: String, campaignId: Int, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_PROSPECTS,
      tidOpt = tid,
      apiAccess = true,
      webAccess = false
    )
      andThen hasCampaign(campaignId)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>


    Future {

      val Logger = request.Logger
      val Res = request.Response
      val oldCol = (request.body \ "ignore_prospects_active_in_other_campaigns").asOpt[Boolean]

      if (oldCol.isDefined) {
        Logger.debug(s"ignore_prospects_active_in_other_campaigns Sent from Frontend - ${oldCol.get}")
      }
      request.body.validate[CampaignProspectsUpdateAssignByEmail] match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>

          val accountId = request.actingTeamAccount.user_id
          val teamId = request.actingTeamAccount.team_id

          val logMsg = s"CampaignController assignProspectsByEmail from zapier (likely): ($accountId, $teamId) :: $campaignId :: $data"
          Logger.info(logMsg)

          if (data.prospect_emails.isEmpty) {
            Res.BadRequestError("Please provide prospect_emails")
          } else if (data.prospect_emails.length > 1 && !newUserDripCampaignsDAO.dripCampaignIds_V2.contains(campaignId)) {

            // escaping this check for drip campaigns step 5 & step 6

            // this is a temporary restriction for zapier to keep the zaps simple
            Res.BadRequestError("You can assign a single prospect at a time")

          } else {

            val prospectEmails = data.prospect_emails.distinct

            prospectServiceV2.findByEmailAndAccountId(
              emails = data.prospect_emails.distinct,
              accountIds = request.permittedAccountIds,
              teamId = teamId,
              Logger = Logger
            ) match {

              case Failure(e) => Res.ServerError(s"There was an error. Please try again or contact support", e = Some(e))

              case Success(prospectIds) => {

                if (prospectIds.length != prospectEmails.length) {

                  Res.BadRequestError("Given prospect emails do not exist in your account")

                } else {

                  // val forceAssign = data.force_assign.getOrElse(false)


                  val doer = request.loggedinAccount
                  val ta = request.actingTeamAccount

                  CampaignService.getIgnoreProspectsInOtherCampaigns(
                    ignore_prospects_in_other_campaigns = data.ignore_prospects_in_other_campaigns,
                    force_assign = data.force_assign
                  ) match {
                    case Failure(err) =>
                      Logger.error(s"Error while getting the ignoreProspectsInOtherCampaigns, data sent from frontend - ${data.ignore_prospects_in_other_campaigns}")
                      Res.BadRequestError("Failed to Assign Prospect, Please contact Support")

                    case Success(ignoreActiveProspects) =>


                      // FORCEASSIGNISSUE
                      campaignProspectService.assignProspectsToCampaign(
                        orgId = doer.org.id,
                        accountId = ta.user_id,
                        teamId = ta.team_id,
                        doerAccountId = doer.internal_id,
                        doerAccountName = Helpers.getAccountName(doer),
                        permittedAccountIds = request.permittedAccountIds,
                        campaignId = campaignId,
                        campaignName = request.campaign.name,
                        campaignSettings = request.campaign.settings,
                        prospectIds = prospectIds.map(_.id).toList,
                        ignoreProspectsInOtherCampaigns = ignoreActiveProspects,
                        Logger = Logger

                        /*
                      moveToNewCampaign = forceAssign,
                      allow_assigning_prospects_to_multiple_campaigns = doer.teams
                        .find(t => t.team_id == ta.team_id)
                        .get
                        .allow_assigning_prospects_to_multiple_campaigns

                      */
                      ) match {

                        case Failure(e) =>

                          Logger.error(s"FATAL : $logMsg :: e: ${LogHelpers.getStackTraceAsString(e)}")

                          e.getMessage match {

                            case msg if msg.contains("campaigns_prospects_pkey") => Res.BadRequestError("Prospect is already assigned to the given campaign")

                            case msg if msg.contains("campaigns_prospects_campaign_id_fkey") => Res.BadRequestError("Campaign with given id not found")

                            case msg if msg.contains("campaigns_prospects_prospect_id_fkey") => Res.BadRequestError("Prospect with given id not found")

                            case msg => Res.BadRequestError("Error while assigning prospects: " + msg)

                          }

                        case Success(assignResult) =>
                          val assignedProspectIds = assignResult.newlyAssignedProspectIds

                          Logger.info(s"$logMsg :: assignedProspectIds: $assignedProspectIds")

                          if (assignedProspectIds.isEmpty) {

                            if (assignResult.prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign.nonEmpty) {

                              Res.Success(s"Prospect(s) are already assigned to the campaign", Json.obj())

                            } else if (assignResult.prospectIdsIgnoredBecauseInOtherCampaigns.nonEmpty) {

                              Res.Success(s"Prospect(s) could not be assigned to campaign because they are active in a different campaign", Json.obj())

                            } else {

                              Res.ServerError(s"Prospect(s) could not be assigned to campaign", e = None)

                            }

                            /*
                          if (!forceAssign) {

                            // happens in zapier integration, where only on prospect can be assigned at a time
                            Res.BadRequestError(s"Prospect already assigned to a campaign")

                          } else {

                            ServerError(s"Prospects could not be assigned to campaign", e = None)

                          }
                          */

                          } else {
                            Logger.debug(s"CampaignController assignProspectsByEmail cid_${campaignId} tid_${ta.team_id} total prospects added - ${assignedProspectIds.length} pid- ${assignedProspectIds}")

                            Res.Success(
                              s"Prospect assigned successfully",

                              Json.obj(
                                "total_assigned" -> assignedProspectIds.length,
                                "prospect_ids" -> assignedProspectIds,
                                "campaign_id" -> campaignId
                              )
                            )

                          }


                      }
                  }
                }
              }
            }

          }
      }

    }

  }


  def unassignProspects(v: ApiVersion, cid: String, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v.textId,
      permission = PermType.EDIT_PROSPECTS,
      tidOpt = tid,
      apiAccess = true
    )
      andThen hasCampaignWithUuid(v, CampaignUuid(cid))
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>

    Future {

      val Logger = request.Logger
      val Res = request.Response
      val ta = request.actingTeamAccount
      val user_id = AccountId(ta.user_id)
      val teamId = TeamId(ta.team_id)
      given logger: SRLogger = request.Logger
      implicit val apiVersion: ApiVersion = v
      val doerAccountName = DoerAccountName(Helpers.getAccountName(ta))
      val permittedAccountIds = request.permittedAccountIds.map(a => AccountId(a));
      val campaign = request.campaign

      request.body.validate[CampaignProspectFormUuids] match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>
          campaignApiService.unAssignProspectFromCampaign(
            data = data,
            teamId = teamId,
            orgId = OrgId(request.loggedinAccount.org.id),
            permittedAccountIds = permittedAccountIds,
            campaign = campaign,
            user_id = user_id,
            doerAccountName = doerAccountName
          ) match {
            case Left(error) =>
              error match {
                case UnAssignProspectsErrorV3.UnAssignProspectError(err) =>
                  Res.BadRequestError(
                    message = err,
                    errorResponse = List(ErrorResponseV3(
                      error_type = ErrorType.BAD_REQUEST,
                      message = err
                    )),
                    version = v
                  )
                case UnAssignProspectsErrorV3.UnAssignProspectsValidationErrors(err) =>
                  Res.BadRequestError(
                    message = "Error while validating data",
                    version = v,
                    errorResponse = err
                  )
                case UnAssignProspectsErrorV3.FilterCampaignOwnedProspectError(err) =>
                  Logger.error("[unAssignProspectsV3] Error while getting campaign owned prospect data", err = err)
                  Res.ServerError("There was an error while unassigning prospects from campaign. Please contact support", e = None)

                case UnAssignProspectsErrorV3.FilterOwnedProspectError(err) =>
                  Logger.error("[unAssignProspectsV3] Error while getting owned prospect data", err = err)
                  Res.ServerError("There was an error while unassigning prospects from campaign. Please contact support", e = None)

                case UnAssignProspectsErrorV3.GetProspectIdFromUuidErrors(err) =>
                  err match {
                    case GetProspectIdFromUuidError.GetProspectIdError(err) =>
                      Logger.error("[unAssignProspectsV3] Error while parsing uuids", err = err)
                      Res.ServerError("There was an error while unassigning prospects from campaign. Please contact support", e = None)
                  }

                case UnAssignProspectsErrorV3.GetProspectUuidFromId(err) =>
                  Logger.error("[unAssignProspectsV3] Error while parsing ids to get uuid", err = err)
                  Res.ServerError("There was an error while unassigning prospects from campaign. Please contact support", e = None)
              }

            case Right(response) =>
              Res.SuccessNoContent()
          }
      }
    }
  }


  // NOTE: [ZAPIER] used in zapier app
  // allow unassigning one prospect at a time
  // use case : unassignProspectsFromCurrentCampaignByEmail       POST    /api/:v/prospects/unassign/by_email
  def unassignProspectsFromCurrentCampaignByEmail(v: String, aid: Option[Long], tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = tid,
    apiAccess = true
  ).async(parse.json) { (request: PermissionRequest[JsValue]) =>

    Future {
      val ta = request.actingTeamAccount.get

      val Logger = request.Logger
      val Res = request.Response
      request.body.validate[CampaignProspectsUpdateAssignByEmail] match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>

          val accountId = ta.user_id
          val teamId = ta.team_id

          val logMsg = s"CampaignController unassignProspectsFromCurrentCampaignByEmail from zapier (likely): ($accountId, $teamId) :: $data"
          Logger.info(logMsg)

          if (data.prospect_emails.isEmpty) {
            Res.BadRequestError("Please provide prospect_emails")
          } else if (data.prospect_emails.length > 1) {

            // this is a temporary restriction for zapier to keep the zaps simple
            Res.BadRequestError("You can unassign a single prospect at a time")

          } else {

            val prospectEmails = data.prospect_emails.distinct

            prospectServiceV2.findByEmailAndAccountId(
              emails = prospectEmails,
              accountIds = request.permittedAccountIds,
              teamId = teamId,
              Logger = Logger
            ) match {

              case Failure(e) => Res.ServerError(s"There was an error.", e = Some(e))

              case Success(prospectIds) => {

                if (prospectIds.length != prospectEmails.length) {

                  Res.BadRequestError("Given prospect emails do not exist in your account")

                } else {

                  campaignProspectService.unassignProspectsFromMainPage(
                    permittedOwnerIds = request.permittedAccountIds,
                    doerAccountId = accountId,
                    teamId = teamId,
                    doerAccountName = Helpers.getAccountName(a = ta),
                    prospectIds = prospectIds.map(_.id).toList,
                    campaignIds = Seq(),
                    Logger = Logger
                  ) match {

                    case Failure(e) =>
                      Logger.error(s"FATAL $logMsg :: error: ${LogHelpers.getStackTraceAsString(e)}")

                      Res.ServerError("Error while unassigning prospects", e = Some(e))

                    case Success(total) =>

                      Logger.info(s"$logMsg :: total: $total")

                      if (total == 0) {

                        Res.BadRequestError("Prospect is not assigned to any campaign")

                      } else if (total == 1) {

                        Res.Success(s"Prospect has been unassigned from campaign", Json.obj("unassigned" -> true))

                      } else {

                        Res.Success(s"$total prospects have been unassigned from campaigns", Json.obj())

                      }


                  }
                }
              }
            }
          }
      }
    }

  }

  def unassignProspectsFromMainPage(v: String, aid: Option[Long], tid: Option[Long]): Action[JsValue] = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_PROSPECTS,
    tidOpt = tid
  ).async(parse.json) { (request: PermissionRequest[JsValue]) =>


    Future {

      val Logger = request.Logger
      val Res = request.Response

      if (request.actingTeamAccount.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {
        val ta = request.actingTeamAccount.get

        request.body.validate[CampaignProspectUnassignRequest] match {

          case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

          case JsSuccess(data, _) =>

            if (data.campaign_ids.isEmpty || data.campaign_ids.get.isEmpty) {
              Logger.error(s"FATAL: campaign unassign main page request, no campaign id found: data: $data :: teamAccount: ${request.actingTeamAccount}")
            }

            campaignProspectService.unassignProspectsFromMainPage(
              permittedOwnerIds = request.permittedAccountIds,
              doerAccountId = ta.user_id,
              teamId = ta.team_id,
              doerAccountName = Helpers.getAccountName(ta),
              prospectIds = data.prospect_ids,
              campaignIds = data.campaign_ids.getOrElse(Seq()),
              Logger = Logger
            ) match {

              case Failure(e) => Res.BadRequestError("Error while unassigning prospects: " + e.getMessage)

              case Success(total) => Res.Success(s"$total prospects have been unassigned from campaigns", Json.obj("total_unassigned" -> total))

            }
        }
      }
    }

  }

  def startStopCampaign(
                         v: ApiVersion,
                         campaignId: SrIdentifier,
                         aid: Option[Long],
                         tid: Option[Long]
                       ) = (
    permissionUtils.checkPermissionV2(
      id = Some(campaignId),
      version = v,
      permission = PermType.CHANGE_CAMPAIGN_STATUS,
      tidOpt = tid,
      apiAccess = true
    )
      andThen hasCampaignV2(campaignId, v)
    ).async(parse.json) { request =>

    given Logger: SRLogger = request.Logger
    val Res = request.Response
    val campaign = request.campaign
    val ta = request.actingTeamAccount
    val org = request.loggedinAccount.org
    val team = request.loggedinAccount.teams.find(t => t.team_id == ta.team_id)
    val userId = AccountId(id = request.actingTeamAccount.user_id)
    val teamId = TeamId(id = request.actingTeamAccount.team_id)
    implicit val apiVersion: ApiVersion = v

    val validatedData = request.body.validate[StartStopCampaignAPIRequest]

    validatedData match {
      case JsError(e) =>
        Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

      case JsSuccess(data, _) =>

        campaignStartService.startStopCampaign(
          status = data.status,
          schedule_start_at = data.schedule_start_at,
          time_zone = data.time_zone,
          campaign = campaign,
          org = org,
          team = team,
          userId = userId,
          teamId = teamId,
          Logger = Logger
        ).map {
          case Left(StartStopCampaignError.InvalidParamsError(errors)) =>
            Res.BadRequestErrorV3(errors)

          case Left(StartStopCampaignError.CampaignStartError(err)) =>
            err match {

              case StartCampaignError.ValidationError(errMessage) =>
                Res.BadRequestErrorV3(List(ErrorResponseV3(error_type = ErrorType.BAD_REQUEST, message = errMessage)))

              case StartCampaignError.ErrorWhileCampaignValidation(err) =>

                err match {

                  case CampaignValidationError.NoCampaignStepError =>
                    Res.BadRequestErrorV3(List(ErrorResponseV3(error_type = ErrorType.BAD_REQUEST, message = "Please add at least one campaign step")))

                  case CampaignValidationError.UrlShortenerError(urlShorteners) =>
                    Res.ServerError(s"URL shorteners are not allowed ($urlShorteners). Could you try again without URL shorteners or contact support ? ", e = None)

                  case CampaignValidationError.NoProspectAddedError =>
                    Res.BadRequestErrorV3(List(ErrorResponseV3(error_type = ErrorType.BAD_REQUEST, message = "Please add at least one campaign step")))

                  case CampaignValidationError.DailyFromTimeGreaterThanDailyTillTimeError =>
                    Res.BadRequestErrorV3(List(ErrorResponseV3(error_type = ErrorType.BAD_REQUEST, message = "Please add at least one campaign step")))

                  case CampaignValidationError.CheckIfHasPhishingPlansError(e) =>
                    Res.ServerError(e.getMessage, e = Some(e))

                }

              case StartCampaignError.SQLException(e) =>
                Res.ServerError(e.getMessage, e = Some(e))

              case StartCampaignError.CountProspectReturnedEmptyError =>
                Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, e = None)

              case StartCampaignError.InValidSenderEmailError =>
                Res.BadRequestErrorV3(List(ErrorResponseV3(error_type = ErrorType.BAD_REQUEST, message = "Please select a valid email address to send emails from")))

              case StartCampaignError.InValidReceiveEmailError =>
                Res.BadRequestErrorV3(List(ErrorResponseV3(error_type = ErrorType.BAD_REQUEST, message = "Please select a valid email address to receive emails at")))

              case StartCampaignError.ProblemWhileStartingCampaignError(e) =>
                Res.ServerError("Problem while starting the campaign. Could you try again or contact support ? ", e = Some(e))

              case StartCampaignError.CannotStartCampaign =>
                Res.NotFoundError("Campaign not found. Could you check and try again ?")

              case StartCampaignError.FeatureUsageEventSaveError(e) =>
                Res.ServerError(err = e)

              case StartCampaignError.CrossedEmailSendLimit(msg) =>

                Res.BadRequestErrorV3(List(ErrorResponseV3(error_type = ErrorType.BAD_REQUEST, message = msg.getOrElse("You've exceeded the max sending email accounts"))))

              case StartCampaignError.InvalidEmailSelected =>
                Res.BadRequestErrorV3(List(ErrorResponseV3(error_type = ErrorType.BAD_REQUEST, message = "Please select a valid email address to send emails from")))

              case StartCampaignError.OwnerWasDeactivatedByAdmin(ownerName, email) =>

                Res.BadRequestErrorV3(
                  List(ErrorResponseV3(error_type = ErrorType.BAD_REQUEST, message =
                    s"""
                          Campaign owner's ($ownerName <${email}>) account was deactivated by your SmartReach account admin. Please change the campaign's owner to an active team member (under Campaign Settings -> Email & Owner setup), and then start the campaign
                        """)))

              case StartCampaignError.ScheduleStartAtTzRequired =>

                Res.BadRequestErrorV3(List(ErrorResponseV3(error_type = ErrorType.BAD_REQUEST, message = "schedule_start_at_tz is required")))

              case StartCampaignError.UpgradeToPaidPlan =>

                Res.PaymentRequiredError("Please upgrade to a paid plan to continue sending campaigns")

              case StartCampaignError.TeamNotActive(team_name) =>

                Res.BadRequestErrorV3(List(ErrorResponseV3(error_type = ErrorType.BAD_REQUEST, message = s"${team_name} team was paused / deactivated by your SmartReach account admin. Please contact your SmartReach admin to activate this team, and then you would be able start the campaign")))

              case StartCampaignError.TeamNotFound =>

                Res.BadRequestErrorV3(List(ErrorResponseV3(error_type = ErrorType.BAD_REQUEST, message = s"Team name wasn't found")))

              case StartCampaignError.ErrorWhileGettingEmailSendingStatus(err) =>
                err match {
                  case GetOrgEmailSendingStatusError.NoCampaignFound =>
                    Res.NotFoundError("No Campaign found")

                  case GetOrgEmailSendingStatusError.NoEmailSettingId =>
                    Res.NotFoundError("No Email Settings")

                  case GetOrgEmailSendingStatusError.ErrorWhileGettingOrganizationStatus(err) =>

                    Res.ServerError(err)

                  case GetOrgEmailSendingStatusError.ErrorWhileGettingSendingEmailStatus(err) =>

                    Res.ServerError(err)

                  case GetOrgEmailSendingStatusError.ErrorWhileGettingCampaignStatus(err) =>

                    Res.ServerError(err)
                }

              case StartCampaignError.EmailSendingStatusUnderReview =>
                Res.BadRequestErrorV3(List(ErrorResponseV3(error_type = ErrorType.BAD_REQUEST, message = "Your sending is under review. It can take upto 1 business day for our team to review, and then you will be able to start the campaign.")))

              case StartCampaignError.EmailSendingStatusBlocked =>
                Res.UnauthorizedError("Your Organization is blocked from sending campaigns")

              case StartCampaignError.FutureFailed(err) =>

                Logger.error(s"[CampaignController]: Future failed startCampaign error", err)
                Res.ServerError(err)
            }

          case Left(StartStopCampaignError.StopCampaignError(err)) =>
            Res.BadRequestErrorV3(List(ErrorResponseV3(error_type = ErrorType.BAD_REQUEST, message = err)))

          case Right(campaign) =>
            apiService.getCampaignApiResponse(
              v = v.toString,
              campaign = campaign,
              orgId = OrgId(id = org.id),
              teamId = teamId,
              isPublicApi = request.isApiCall
            ) match {

              case Left(error) => error match {
                case CampaignApiError.ErrorWhileFetchingDataForTeamId(_) =>
                  Res.ServerError("Error while fetching data", None)
              }

              case Right(response) =>

                val successMsg = if (campaign.status == CampaignStatus.SCHEDULED) {
                  s"Congrats! ${campaign.name} has been scheduled."
                } else if (campaign.status == CampaignStatus.RUNNING) {
                  s"Congrats! ${campaign.name} has been started. Emails will start getting scheduled within half an hour."
                } else {
                  s"${campaign.name} has been paused"
                }

                Res.Success(
                  message = successMsg,
                  data = Json.toJson(response),
                  apiVersion = v.toString
                )
            }
        }
    }
  }

  def deleteCampaign(v: String, campaignId: Int, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.DELETE_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaign(campaignId)
    ).async { (request: CampaignRequest[AnyContent]) =>

    Future {

      given Logger: SRLogger = request.Logger
      val Res = request.Response
      val t = request.actingTeamAccount

      Logger.info(s"DELETE deleteCampaign called by ${request.loggedinAccount.internal_id} :: $campaignId")

      val findAndDeleteTriggers: Try[Int] = Try {
        val triggers = triggerDAO.findCampaignTriggersByCampaignId(campaignId = request.campaign.id).get

        if (triggers.nonEmpty) {

          val ids = triggers.map(t => t.id)

          triggerDAO.delete(
            ids = ids,
            permittedAccountIds = request.permittedAccountIds,
            teamId = t.team_id
          ).get

        } else {

          0

        }

      }

      findAndDeleteTriggers match {

        case Failure(e) =>

          Res.ServerError(err = e)

        case Success(_) =>

          campaignService.deleteCampaignAndAssociatedTasks(campaignId = CampaignId(id = campaignId), teamId = TeamId(id = t.team_id)) match {

            case Left(DeleteCampaignTasksError.CampaignAssociatedToWebhook) =>
              Res.BadRequestError("Cannot delete campaign associated to webhook. Please remove the campaign from the webhook settings and try again.")

            case Left(DeleteCampaignTasksError.ErrorWhileFetchingWebhooksForCampaign(err)) =>
              Res.ServerError(s"Error while fetching webhooks for campaign: $err", e = Some(err))

            case Left(DeleteCampaignTasksError.DeleteCampaignsSqlError(e)) =>
              Res.ServerError(s"Error while deleting campaign: $e", e = Some(e))

            case Left(DeleteCampaignTasksError.DeleteAssociatedTaskSqlError(e)) =>
              Res.ServerError(s"Error while deleting Tasks ${e}", e = Some(e))

            case Left(DeleteCampaignTasksError.NoCampaignFoundToDelete) =>
              Res.NotFoundError(s"Campaign not found. Could you please check and retry ?")

            case Left(DeleteCampaignTasksError.ErrorWhileSelectAndPublish(e)) =>
              Res.ServerError(s"Error while publishing emails and tasks for deletion ${e}", e = Some(e))

            case Right(publishedMsgsForDeletionCount) =>

              if (publishedMsgsForDeletionCount > 0) {
                Res.Success("Deleted Campaign and published tasks and emails for deletion", data = Json.obj())
              }
              else {
                Res.Success("Deleted Campaign Successfully", data = Json.obj())
              }
          }


      }

    }

  }

  // use case : createSpamTest. POST    /api/:v/campaigns/:id/spam_tests
  // SENDER_ROTATION
  // creating spam test for a given campaign
  def createSpamTest(v: String, id: Long, aid: Option[Long], tid: Option[Long], test_type: String, cesid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid,
      localOrStagingApiTestAccess = true
    )
      andThen hasCampaign(id)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>


    Future {

      val Logger = request.Logger
      val Res = request.Response
      val maxSpamTests = Helpers.maxSpamTests(
        totalSendingEmailAccounts = request.loggedinAccount.org.counts.total_sending_email_accounts,
        additionalSpamTests = request.loggedinAccount.org.counts.additional_spam_tests
      )
      val campaign = request.campaign
      val enableDomainHealthPage = request.loggedinAccount.org.org_metadata.enable_domain_health_page

      val isDomainPageEnabled = if (enableDomainHealthPage.isDefined) {
        enableDomainHealthPage.get
      } else {
        false
      }

      val sender_email_settings_id = cesid.flatMap(id =>
        campaign.settings.campaign_email_settings.find(_.id.id == id).map(_.sender_email_setting_id.emailSettingId.toInt)
      )


      sender_email_settings_id match {
        case None =>
          Res.NotFoundError(s"Not found the sender email")

        case Some(value) =>
          val result = spamTestService.createSpamTest(
            planID = request.loggedinAccount.org.plan.plan_id,
            test_type = test_type,
            maxSpamTests = maxSpamTests,
            org_Id = request.loggedinAccount.org.id,
            sender_email_settings_id = sender_email_settings_id,
            head_step_id = campaign.head_step_id,
            campaign_id = campaign.id,
            team_id = request.actingTeamAccount.team_id,
            logger = request.Logger,
            isAfterDomainChanges = isDomainPageEnabled
          )
          result match {

            case Left(CreateSpamTestError.PlanIdInActive) =>
              Res.PaymentRequiredError("Please upgrade to a paid plan to continue")

            case Left(CreateSpamTestError.TestTypeNotFound) =>
              Res.BadRequestError("test_type must be either deliverability or auth")

            case Left(CreateSpamTestError.MaxSpamTestLimitReached) =>
              Res.BadRequestError(s"You have reached the limit ($maxSpamTests) of Spam Tests for this month. Please contact support if you need more Spam Tests.")

            case Left(CreateSpamTestError.SpamTestRunningError) =>
              Res.BadRequestError("A spam test is already running for this campaign. You can run a spam test per campaign once in 15 minutes.")

            case Left(CreateSpamTestError.EmptySenderEmailAccount) =>
              Res.BadRequestError("You must assign a sender email account to this campaign before you can start a spam test")

            case Left(CreateSpamTestError.EmptySteps) =>
              Res.BadRequestError("Add steps to campaign before starting a spam test")

            case Left(CreateSpamTestError.NoProspectAdded) =>
              Res.BadRequestError("Add a few new prospects and then try. We will NOT send any emails to them in the spam testing process")

            case Left(CreateSpamTestError.SQLException(err)) =>

              Res.ServerError(err.getMessage(), e = Some(err))

            case Right(dataRow) =>
              Res.Success("A spam test has been started. Check back here after a few hours to see the results.",
                Json.obj("spam_tests" -> Seq(dataRow)))

          }
      }


    }

  }


  def genPresignedUrlForUploadingStepImagesIntoS3(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CAMPAIGNS,
    tidOpt = tid
  ) { (request: PermissionRequest[AnyContent]) =>

    val Logger = request.Logger
    val Res = request.Response
    val filename = if (request.body.asJson.isDefined) (request.body.asJson.get \ "filename").asOpt[String] else None

    if (filename.isEmpty) {

      Res.BadRequestError(s"Send valid filename")

    } else {

      S3.genPresignedUrl(filename = filename.get) match {

        case Failure(e) =>
          Res.ServerError(s"Error while generating presigned url: $e", e = Some(e))

        case Success(url) =>
          Res.Success("Presigned url", Json.obj("presigned_url" -> url))

      }


    }


  }

  def genPresignedUrlForUploadingStepImages(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CAMPAIGNS,
    tidOpt = tid
  ) { (request: PermissionRequest[AnyContent]) =>

    val Logger = request.Logger
    val Res = request.Response
    val filename = if (request.body.asJson.isDefined) (request.body.asJson.get \ "filename").asOpt[String] else None

    if (filename.isEmpty) {

      Res.BadRequestError(s"Send valid filename")

    } else {

      CloudStorage.genSignedUrlForEmailImage(filename = filename.get) match {

        case Failure(e) =>
          Res.ServerError(s"Error while generating presigned url: $e", e = Some(e))

        case Success(url) =>
          Res.Success("Presigned url", Json.obj("presigned_url" -> url))

      }


    }


  }


  def getVoicemails(
                    v: ApiVersion,
                    campaign_id: Long,
                    tid: Option[Long]
                  ) = (
    permissionUtils.checkPermissionV2(
      id = Some(SrId(campaign_id.toInt)),
      permission = PermType.VIEW_CAMPAIGNS,
      tidOpt = tid,
    )
      andThen hasCampaignV2(id = SrId(campaign_id.toInt))
    ).async { request =>
    given Logger: SRLogger = request.Logger
    val Res = request.Response

    val loggedInAccount = request.loggedinAccount
    val ta = request.actingTeamAccount
    val campaignId = CampaignId(campaign_id)
    val teamId = TeamId(ta.team_id)
    Future.successful {

      campaignService.getVoicemails(
        campaignId = campaignId,
        teamId = teamId,
      ) match{

        case Failure(exception) =>
          Logger.error("Error while fetching voicemails", err = exception)
          Res.ServerError("Error while fetching voicemails", e = Some(exception))

        case Success(data: List[VoiceDropData]) =>
          Res.Success("Voicemails found", Json.obj("voicemails" -> data))

      }
    }

  }

  def updateVoicemailName(
                           v: String,
                           campaignId: Long,
                           voicemailId: Long,
                           tid: Option[Long]
                         ) = (permissionUtils.checkPermissionV2(
    id = Some(SrId(campaignId.toInt)),
    permission = PermType.EDIT_CAMPAIGNS,
    tidOpt = tid,
    //    apiAccess = false
  ) andThen hasCampaignV2(id = SrId(campaignId.toInt))
    ).async(parse.json) { request =>

    val ta = request.actingTeamAccount
    given Logger: SRLogger = request.Logger
    val Res = request.Response

    //    logger.info(s"\n\nRequest Body: ${request.body}\n\n")
    Future.successful {
    val validateData = request.body.validate[UpdateVoicemailDetailsRequest]

      validateData match {

        case JsError(e) =>
          Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>


          campaignService.updateVoicemailDetails(
            data = data,
            teamId = TeamId(id = ta.team_id),
            campaignId = CampaignId(id = campaignId)
          )match{

              case Failure(exception) =>
                Logger.error(s"[updateVoicemailName] Error while updating voicemail name account_id :: ${request.loggedinAccount.internal_id} team_id:: ${ta.team_id} newName:: ${data.name} voicemailId: ${data.voicemailId.id} ", err = exception)
                Res.ServerError("Error while updating voicemail name", e = Some(exception))

              case Success(res: VoicemailId) =>
                Res.Success("Voicemail updated", Json.obj("id" -> res.toString))

            }
      }
    }
  }


  def uploadVoicemails(v: ApiVersion, campaignId: SrIdentifier, tid: Option[Long]) =
    (permissionUtils.checkPermissionV2(
      id = Some(campaignId),
      version = v,
      permission = PermType.CHANGE_CAMPAIGN_STATUS,
      tidOpt = tid,
      apiAccess = false
    )
      andThen hasCampaignV2(campaignId, v)
  ).async(parse.multipartFormData)  { request =>

    given Logger: SRLogger = request.Logger
    val Res = request.Response
    val tid = request.actingTeamAccount.team_id
    val owner_id = request.loggedinAccount.internal_id

      request.body.file("file") match {
        case Some(filePart: FilePart[TemporaryFile]) =>
          val filename = request.body.dataParts.get("filename").flatMap(_.headOption).getOrElse("")
          val fileType= request.body.dataParts.get ("file_type").flatMap (_.headOption).getOrElse("")
          val description= request.body.dataParts.get ("description").flatMap(_.headOption).getOrElse("")

      val campaignIdFromForm: Long = request.body.dataParts.get("campaign_id")
              .flatMap(_.headOption) // Get the first value if it exists
              .flatMap { idStr =>
              Try(idStr.toLong).toOption // Safely parse the string to Long
              }
              .getOrElse(0L)
          // Create a VoicemailSaveData instance
          val voicemailData = VoicemailSaveData(
            file = filePart.ref, // TemporaryFile
            filename = filename,
            fileType = fileType,
            campaign_id = CampaignId(campaignIdFromForm) // Assuming CampaignId has a constructor
          )

          if(filename.length() > AppConfig.Voicemail.maxFileNameLength){
            
            Future.successful(Res.BadRequestError(s"Filename is too long. Maximum allowed length is ${AppConfig.Voicemail.maxFileNameLength} characters."))
            
          }else if(description.length() > AppConfig.Voicemail.maxFileDescriptionLength){
            
            Future.successful(Res.BadRequestError(s"File description is too long. Maximum allowed length is ${AppConfig.Voicemail.maxFileDescriptionLength} characters."))
            
          } else{

            // Call the service to upload the voicemail and save it in the database
            campaignService.uploadVoiceMailAndSaveInDb(
                voicemail = voicemailData.file,
                filename = voicemailData.filename,
                campaignId = voicemailData.campaign_id,
                fileType = voicemailData.fileType,
                description = description,
                ownerId = AccountId(id = owner_id),
                teamId = TeamId(id = tid)
              )
              .map { res =>
                Res.Success("Upload successful", Json.obj("url" -> res))
              }
              .recover { case e =>
                Logger.fatal(s"uploadVoicemails error", err = e)
                Res.ServerError("Error while uploading voicemail: " + e.getMessage, e = Some(e))
              }
          }

        case None =>
          Future.successful(Res.BadRequestError("No file uploaded."))
      }


  }


  def deleteVoicemail(v: String, campaignId: Long, voicemailId: Long, tid: Option[Long]) = 
    (permissionUtils.checkPermissionV2(
      id = Some(SrId(campaignId.toInt)),
      permission = PermType.EDIT_CAMPAIGNS,
      tidOpt = tid
    )
      andThen hasCampaignV2(SrId(campaignId.toInt))
      ).async(parse.json) { request =>


      given Logger: SRLogger = request.Logger
      val Res = request.Response
      val campaign = request.campaign
      val teamId = TeamId(id = request.actingTeamAccount.team_id)
      campaignService.deleteVoicemail(
          campaignId = CampaignId(id = campaignId),
          voicemailId = VoicemailId(id = voicemailId),
          teamId = teamId
        ).map { res =>
          Logger.info(s"[deleteVoicemail] Voicemail deleted by :: account_id:: ${request.loggedinAccount.internal_id}. team_id:: ${teamId.id} :: voicemail_id:: ${voicemailId}")
          Res.Success("Delete successful", Json.obj("id" -> res))
        }
        .recover { case e =>
          Logger.fatal(s"Error while deleting voicemail:: ${voicemailId} for team :: ${teamId.id}", err = e)
          Res.ServerError("Error while deleting voicemail: " + e.getMessage, e = Some(e))
        }
    }
    



  def findSpamTests(v: String, id: Long, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.VIEW_CAMPAIGNS,
      tidOpt = tid
    ) andThen hasCampaign(id)).async { (request: CampaignRequest[AnyContent]) =>

    val Logger = request.Logger
    val Res = request.Response
    val maxSpamTests = Helpers.maxSpamTests(
      totalSendingEmailAccounts = request.loggedinAccount.org.counts.total_sending_email_accounts,
      additionalSpamTests = request.loggedinAccount.org.counts.additional_spam_tests
    )
    val result = spamTestService.getSpamTestReport(id, maxSpamTests, request.loggedinAccount.org.id)

    result.map {
      case Left(GetSpamTestServiceError.FetchException(e)) => Res.ServerError(s"Some Error occur while fetching spamTest Report:$e", e = Some(e))
      case Right(res) =>
        Res.Success("Spam tests found", Json.toJson(res))
    }

  }


  def findInitialSpamTests(v: ApiVersion, cid: Option[Long], aid: Option[Long], tid: Option[Long]) =
    permissionUtils.checkPermission(
      permission = PermType.VIEW_CAMPAIGNS,
      tidOpt = tid,
      localOrStagingApiTestAccess = true
    ).async { (request: PermissionRequest[AnyContent]) =>

      val Logger = request.Logger
      val res = request.Response
      val accountId = request.loggedinAccount.internal_id
      val maxSpamTests = Helpers.maxSpamTests(
        totalSendingEmailAccounts = request.loggedinAccount.org.counts.total_sending_email_accounts,
        additionalSpamTests = request.loggedinAccount.org.counts.additional_spam_tests
      )

      val campaignId = cid.map(campaignId =>
        CampaignId(campaignId)
      )

      val result = spamTestService.getDomainLatestCheckRecord(
        campaignId = campaignId,
        orgId = OrgId(request.loggedinAccount.org.id),
        teamId = TeamId(tid.get),
        maxSpamTests = maxSpamTests,
        logger = Logger
      )

      result match {
        case Left(GetDomainSpamTestError.DomainFetchException(ex)) =>
          Logger.fatal(s"CampaignController findInitialSpamTests error while fetching spamtest for cid_${cid} : tid_${tid} : aid_${accountId}")
          Future.successful(res.ServerError(s"Error while fetching Spam Test, ${CONSTANTS.API_MSGS.DEFAULT_ERROR_MESSAGE}", e = Some(ex)))

        case Left(GetDomainSpamTestError.NoSenderEmails) =>
          Future.successful(res.BadRequestError("You must assign a sender email account to this campaign before you can start a spam test"))

        case Left(GetDomainSpamTestError.ErrorWhileFetchingInboxPlacementData(e)) =>
          Future.successful(res.ServerError(s"Error while fetching inbox placement report, ${CONSTANTS.API_MSGS.DEFAULT_ERROR_MESSAGE}", e = Some(e)))

        case Right(domainSpamTestResponse) =>

          Future.successful(res.Success("Spam test found for sender emails of campaign", Json.toJson(domainSpamTestResponse)))
      }


    }


  private def __unsubscribeViaPOSTHtml(
                                        trackUnsubscribePostUrl: String
                                      ): Result = {

    // xhr.setRequestHeader('Content-Type', 'application/json');
    val unsubscribeHtmlPage =
      s"""<!doctype html><html lang="en">
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
              <title>Unsubscribing ...</title>

              <style>

                body {
                  text-align: center;
                }

                #unsubmsg {
                  margin-top: 5rem;
                  font-size: 20px;
                }


                #unsubbtn {
                  background-color: rgb(33, 133, 208);
                  color: white;
                  cursor: pointer;
                  padding: 14px 28px;
                  border: none;
                  font-size: 18px;

                }

                #unsubbtn:hover {
                   background-color: rgba(33, 133, 208, 0.8);
                }

              </style>

              <script>

                function showText(txt) {

                  document.getElementById("unsubmsg").innerText = txt;

                  document.title = txt;

                }

                function showBtnText(txt) {

                  document.getElementById("unsubbtn").innerText = txt;

                }

                function onUnsubscribeSuccess() {

                    showText("You have been unsubscribed.");

                    document.getElementById("unsubbtn").style.display = "none";
                }

                function unsubscribeConfirm() {

                  showBtnText("Unsubscribing ...")


                  var xhr = new XMLHttpRequest();

                  xhr.open("POST", "$trackUnsubscribePostUrl", true);
                  xhr.send();
                  xhr.onload = function() {

                    onUnsubscribeSuccess()

                  }


                  return false;


                }

              </script>

            </head>

            <body>
              <p id="unsubmsg">Do you want to unsubscribe from further emails ?</p>

              <br/>

              <form name="unsubform" onsubmit="return unsubscribeConfirm()" >

                <button id="unsubbtn" type="submit" name="Submit">Unsubscribe</button>

              </form>

            </body>
          </html>"""

    val response = Ok(unsubscribeHtmlPage).as(HTML)

    response


  }

  def getUnsubscribeTV4(id: String) = Action { (request: Request[AnyContent]) =>

    val logRequestId = StringUtils.genLogTraceId
    val Logger = new SRLogger(logRequestId = s"$logRequestId getUnsubscribeTV4 id :: $id ")

    val requestIP = Helpers.getClientIPFromGCPLoadbalancer(request = request)
    val reqUserAgent = request.headers.get("User-Agent").getOrElse("")
    val reqHeaders = request.headers

    Logger.info(s"unsubinit host: ${request.host} :: uri: ${request.uri} :: Client-IP: $requestIP :: userAgent: $reqUserAgent :: allheaders: ${reqHeaders.toSimpleMap}")

    val requestPath = request.path
    val requestHost = request.host
    val requestProtocol = if (request.host.contains("localhost")) "http" else "https"

    /**
     * POST    /tv4/:code/optout?reqid=logRequestId
     *
     * this helps us trace unsubscribe requests across both the GET and POST steps
     */
    val postRequestUrl = s"$requestProtocol://$requestHost$requestPath?reqid=$logRequestId"

    Logger.info(s"requestPath: $requestPath :: postRequestUrl: $postRequestUrl")

    val htmlResponse = __unsubscribeViaPOSTHtml(
      trackUnsubscribePostUrl = postRequestUrl
    )

    EmailTrackingApiRequestData.saveEmailTrackingApiRequestHeader(
      traceReqId = logRequestId,
      reqHost = requestHost,
      reqURI = request.uri,
      postRequestUrl = Some(postRequestUrl),

      eventType = EventType.PROSPECT_OPTED_OUT,
      ip = requestIP.getOrElse(""),
      userAgent = reqUserAgent,
      requestHeaders = reqHeaders

    ) match {
      case Failure(e) =>

        Logger.fatal("saveEmailTrackingRequestHeader (IGNORING BUT CHECK): ", err = e)
        htmlResponse

      case Success(savedTrackingRequestId) =>

        Logger.info(s"savedTrackingRequestId: $savedTrackingRequestId")

        htmlResponse

    }


  }

  //PROSPECTS_EMAILS_TODO_READ_CLEANED
  // use case:  /tv4/:code/optout  -> postUnsubscribeTV4
  def postUnsubscribeTV4(code: String, reqid: Option[String]) = Action.async { request =>
    val logRequestId = reqid.getOrElse(StringUtils.genLogTraceId)
    val Logger = new SRLogger(logRequestId = s"$logRequestId  postUnsubscribeTV4 ")
    Logger.info(s"unsubinit post :: $code :: host: ${request.host} :: uri: ${request.uri} :: Client-IP: ${Helpers.getClientIPFromGCPLoadbalancer(request = request)}")

    campaignService.handleUnsubscribe(
      code = code,
      reqid = reqid
    )(using ec, Logger)
  }

  def updatePhisTankUrls = Action.async { request =>
    val logRequestId = s"${StringUtils.genLogTraceId} CampaignController.updatePhisTankUrls: "
    given Logger: SRLogger = new SRLogger(logRequestId = logRequestId)
    val Res = new SRAPIResponse(Logger = Logger)
    phishingCheckService._updatePhishTank()
      .map(value => {

        Logger.info(s"${value}")
        Res.Success("Updated phish tank urls", Json.obj("ids" -> value))

      })
      .recover { case e =>

        Logger.error(s"ERROR ${LogHelpers.getStackTraceAsString(e)}")
        Res.ServerError(s"${CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER}: ${e.getMessage}", e = Some(e))

      }

  }

  // SENDER_ROTATION
  // getting EmailSettingStatus for a given campaign including the sending status for the campaign, Org, and all the sending emails
  def getOrgEmailSendingStatus(
                                v: String,
                                campaign_id: Int,
                                aid: Option[Long],
                                tid: Option[Long]
                              ) = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_CAMPAIGNS,
    tidOpt = tid,
    apiAccess = false
  ) andThen
    hasCampaign(campaign_id)
    ).async { (request: CampaignRequest[AnyContent]) =>
    val org_id = request.loggedinAccount.org.id
    given Logger: SRLogger = request.Logger
    val Res = request.Response

    campaignStartService.getOrgEmailSendingStatus(
      org_id = OrgId(org_id),
      campaign = request.campaign
    ) match {
      case Left(GetOrgEmailSendingStatusError.NoCampaignFound) =>
        Future.successful(Res.NotFoundError("No Campaign found"))

      case Left(GetOrgEmailSendingStatusError.NoEmailSettingId) =>
        Future.successful(Res.NotFoundError("No Email Settings"))

      case Left(GetOrgEmailSendingStatusError.ErrorWhileGettingOrganizationStatus(err)) =>

        Future.successful(Res.ServerError(err))

      case Left(GetOrgEmailSendingStatusError.ErrorWhileGettingSendingEmailStatus(err)) =>

        Future.successful(Res.ServerError(err))

      case Left(GetOrgEmailSendingStatusError.ErrorWhileGettingCampaignStatus(err)) =>

        Future.successful(Res.ServerError(err))

      case Right(emailSendingStatus) =>
        Future.successful(Res.Success("OK",
          if (emailSendingStatus.sending_email_status.nonEmpty) {
            Json.obj(
              "sending_email_status" -> emailSendingStatus.sending_email_status,
              "organization_status" -> emailSendingStatus.organization_status,
              "campaign_status" -> emailSendingStatus.campaign_status
            )
          } else {
            Json.obj(
              "organization_status" -> emailSendingStatus.organization_status,
              "campaign_status" -> emailSendingStatus.campaign_status
            )
          }
        )
        )

    }
  }

  def getCampaignStats(v: ApiVersion, tid: Option[Long], cid: String) = (permissionUtils.checkPermission(
    permission = PermType.VIEW_CAMPAIGNS,
    tidOpt = tid,
    apiAccess = true
  )
    andThen hasCampaignWithUuid(v, CampaignUuid(cid))).async { (request: CampaignRequest[AnyContent]) =>
    given Logger: SRLogger = request.Logger
    val Res = request.Response
    implicit val apiVersion: ApiVersion = v

    val loggedInAccount = request.loggedinAccount // logged in account
    val ta = request.actingTeamAccount // team account
    val teamId = TeamId(ta.team_id)
    val isApiCall = request.isApiCall
    val paIds = request.permittedAccountIds
    campaignService.getCampaignIdsFromUuid(
      data = List(CampaignUuid(cid)),
      teamId = teamId
    ) match {
      case Left(err) =>
        err match {
          case GetCampaignIdFromUuidError.GetCampaignIdError(err) =>
            Logger.fatal(s"Error while fetching CampaignId from Campaign_uuid tid_${teamId} campaign_uuid : $cid ")
            Future.successful(Res.ServerError(message = "Error while getCampaignStats, Please contact support", e = Some(err)))
        }
      case Right(validAndInvalidCampaignUuidIdList) =>
        val validCampaignIdsOpt = validAndInvalidCampaignUuidIdList.valid_campaigns_ids.headOption
        val inValidCampaignUuids = validAndInvalidCampaignUuidIdList.invalid_uuids

        validCampaignIdsOpt match {
          case None =>
            Future.successful(Res.ServerError(message = "Error while getCampaignStats, Please contact support", e = None))
          case Some(validCampaignId) =>
            if (inValidCampaignUuids.nonEmpty) {
              Future.successful(Res.BadRequestError(
                message = "Invalid CampaignId Passed",
                errorResponse = List(
                  ErrorResponseV3(
                    message = "Invalid CampaignId Passed",
                    error_type = ErrorType.BAD_REQUEST
                  )
                ),
                version = v
              ))
            } else {
              val campaignIdOpt = validCampaignId._2
              campaignIdOpt match {
                case None =>
                  Future.successful(Res.ServerError(message = "Error while getCampaignStats, Please contact support", e = None))
                case Some(campaignId) =>
                  //                  campaignService.getCampaignStatsById(
                  //                    cId = campaignId.id,
                  ////                    orgId = OrgId(request.loggedinAccount.org.id),
                  //                    campaignStatus = request.campaign.status,
                  //                    campaignCreatedAt = request.campaign.created_at,
                  //                    teamId = ta.team_id,
                  //                    campaign_has_email_step = campaignService.checkIfCampaignHasEmailStep(
                  //                      campaignId = campaignId,
                  //                      teamId = teamId,
                  //                    ),
                  //                    acc = AccountData.AccountObj(account = loggedInAccount),
                  //                    Logger = Logger,
                  //
                  //                  )(campaignCacheService = campaignCacheService)

                  /*
                  29 August 2024 :
                  Added a new service layer function to fetch the campaign stats along with the task count
                   */
                  campaignService.getCampaignStatsWithTaskCount(
                    teamId = TeamId(ta.team_id),
                    orgId = OrgId(request.loggedinAccount.org.id),
                    campaignId = campaignId,
                    campaignStatus = request.campaign.status,
                    campaignCreatedAt = request.campaign.created_at,
                    acc = loggedInAccount,
                    permittedIds = paIds
                  )(campaignCacheService = campaignCacheService, ec = ec, logger = Logger).flatMap { stats =>

                    if (isApiCall) {
                      Future.successful(Res.Success(
                        message = "Campaigns Stats found",
                        data = Json.toJson(AllCampaignStatsWithTaskCount.apiStructureV3(stats)),
                        apiVersion = v.textId
                      ))
                    } else {
                      Future.successful(Res.Success(
                        message = "Campaigns Stats found",
                        data = Json.toJson(stats),
                        apiVersion = v.textId
                      ))
                    }

                  }.recoverWith { case e =>
                    Logger.fatal(s"[Fatal] campaignController.getCampaignStats Error: ${LogHelpers.getStackTraceAsString(e)}")
                    Future.successful(Res.ServerError(message = "Error while getCampaignStats, Please contact support", e = Some(e)))
                  }
              }
            }
        }
    }

  }


  def updateAccountSetting(
                            v: String,
                            campaign_id: Int,
                            tid: Option[Long]
                          ) = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CAMPAIGNS,
    tidOpt = tid,
    //    apiAccess = false
  ) andThen
    hasCampaign(campaign_id)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>

    val ta = request.actingTeamAccount
    given Logger: SRLogger = request.Logger
    val Res = request.Response

    //    logger.info(s"\n\nRequest Body: ${request.body}\n\n")

    val validateData = request.body.validate[ChannelSettingData]
    validateData match {

      case JsError(e) =>
        Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

      case JsSuccess(data, _) =>

        //        logger.debug(s"\n\nparsed Data :  $data\n\n")

        campaignService.updateAccountSetting(
            channel_setting_data = data,
            team_id = ta.team_id,
            campaign_id = campaign_id
          ).map(res => res match {

            case Left(ChannelSetupError.InvalidDataProvided) =>
              Res.BadRequestError("Please provide valid teamId or CampaignId")
            case Left(ChannelSetupError.NoUuidPresentError) =>
              Res.BadRequestError("Please send a valid id")
            case Left(ChannelSetupError.ErrorWhileUpdatingInDb) =>
              Res.BadRequestError("Error while updating, Please contact support")
            case Right(campaignChannelSettingData) =>
              Res.Success("Account setup successfull", Json.obj("channel_setup_data" -> campaignChannelSettingData))
          })
          .recover {
            err => {
              Logger.fatal(s"[Fatal] campaignController.updateAccountSettings This should have never happened data: ${data} Error: ${LogHelpers.getStackTraceAsString(err)}")
              Res.ServerError(err)
            }
          }


    }

    //    }
  }

  def getChannelSettings(
                          campaign_id: SrIdentifier,
                          tid: Option[Long]
                        ) = (
    permissionUtils.checkPermissionV2(
      id = Some(campaign_id),
      permission = PermType.VIEW_CAMPAIGNS,
      tidOpt = tid,
      version = ApiVersion.V3,
      apiAccess = true
    )
      andThen hasCampaignV2(id = campaign_id, v = ApiVersion.V3)
    ).async { request =>
    given Logger: SRLogger = request.Logger
    implicit val apiVersion: ApiVersion = ApiVersion.V3
    val Res = request.Response

    val loggedInAccount = request.loggedinAccount // logged in account
    val ta = request.actingTeamAccount // team account
    val campaignId = CampaignId(request.campaign.id)
    val teamId = TeamId(ta.team_id)
    val orgId = OrgId(loggedInAccount.org.id)

    channelSettingService.getChannelSettingsV3( // use cache also
        campaignId = campaignId,
        teamId = teamId,
      ).map {

        case Left(GetChannelSettingsError.DbFailure(err)) => Res.ServerError(message = "Server error, please contact support", e = Some(err))

        case Right(data: ChannelSettings) =>

          apiService.getChannelSettingsApiResponse(channelSettings = data, teamId = teamId, orgId = orgId) match {

            case Failure(err) =>

              Res.ServerError(message = "Server error, please contact support", e = Some(err))

            case Success(response: ChannelSettingsPublicApiResponse) =>

              Res.SuccessV3(data = Json.toJson(response))

          }

      }
      .recover(err => {
        Logger.fatal(s"[CampaignController.getChannelSettings channelSettingService.getChannelSettingsV3]  campaignId::$campaignId teamId::$teamId")
        Res.ServerError(message = "Server error, please contact support", e = Some(err))
      })

  }

  def saveDripCampaign(
                        v: ApiVersion,
                        campaign_id: Long,
                        tid: Option[Long],
                        aid: Option[Long]
                      ): Action[JsValue] = (permissionUtils.checkPermission(
    version = v.toString,
    permission = PermType.EDIT_CAMPAIGNS,
    tidOpt = tid
  ) andThen
    hasCampaign(campaign_id)
    ).async(parse.json) { (request: CampaignRequest[JsValue]) =>
    Future {

      val teamId = TeamId(request.actingTeamAccount.team_id)
      val campaignId = CampaignId(request.campaign.id)
      val Res = request.Response
      given Logger: SRLogger = request.Logger

      request.body.validate[DripCampaignForm] match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(dripCampaignForm: DripCampaignForm, _) =>

          val res: Try[Int] = campaignService.saveDripCampaign(
            dripCampaignForm = dripCampaignForm,
            campaignId = campaignId,
            teamId = teamId
          )

          res match {

            case Failure(exception) =>
              Logger.fatal(s"CampaignController.saveDripCampaign aid_$aid tid_$tid cid_$campaign_id", exception)
              Res.ServerError("Server error, please contact support.", e = None)

            case Success(_) => Res.Success("Campaign saved successfully")

          }

      }

    }
  }

  def getDripCampaignData(
                           v: ApiVersion,
                           campaign_id: Long,
                           tid: Option[Long],
                           aid: Option[Long]
                         ) = (permissionUtils.checkPermission(
    version = v.toString,
    permission = PermType.VIEW_CAMPAIGNS,
    tidOpt = tid
  ) andThen
    hasCampaign(campaign_id)
    ).async { (request: CampaignRequest[AnyContent]) =>
    Future {

      val teamId = TeamId(request.actingTeamAccount.team_id)
      val campaignId = CampaignId(request.campaign.id)
      val Res = request.Response
      given Logger: SRLogger = request.Logger

      val res: Try[Option[DripCampaignData]] = campaignService.getDripCampaignData(
        campaignId = campaignId,
        teamId = teamId
      )

      res match {

        case Failure(exception) =>
          Logger.fatal(s"CampaignController.saveDripCampaign aid_$aid tid_$tid cid_$campaign_id", exception)
          Res.ServerError("Server error, please contact support.", e = None)

        case Success(None) =>
          Res.Success("Drip campaign data not found, please save the drip campaign first.")

        case Success(Some(data)) =>
          Res.Success("Campaign data found", Json.toJson(data))

      }

    }
  }

}
