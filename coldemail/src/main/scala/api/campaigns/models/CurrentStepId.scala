package api.campaigns.models

import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, Js<PERSON><PERSON>ber, JsResult, JsSuccess, JsValue, Reads, Writes}

case class CurrentStepId(step_id: Long) extends AnyVal {
  override def toString: String = step_id.toString
}

object CurrentStepId{
  implicit val reads: Reads[CurrentStepId] = new Reads[CurrentStepId] {
    override def reads(ev: JsValue): JsResult[CurrentStepId] = {
      ev match {
        case JsNumber(id) => JsSuccess(CurrentStepId(step_id = id.toLong))
        case randomValue => JsError(s"expected number, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[CurrentStepId] = new Writes[CurrentStepId] {
    override def writes(o: CurrentStepId): JsValue = JsNumber(o.step_id)
  }
}