package api.campaigns.models

import play.api.libs.json.{JsString, JsValue, Writes}

import scala.util.Try

object CampaignPauseReason {

  private val campaignLimitReached = "campaign_limit_reached"
  private val previousStepIsNotDone = "previous_step_is_not_done"
  private val allProspectsCompleted = "all_prospects_completed"
  private val prospectsHaveInvalidEmails = "prospects_have_invalid_emails"
  private val prospectsHaveMissingMergeTags = "prospects_have_missing_merge_tags"
  private val emailAccountSendLimitReached = "email_account_send_limit_reached"
  private val noProspectLeftInCurrentTimezone = "no_prospect_left_in_current_timezone"
  private val restProspectsAreInDNC = "rest_prospects_are_in_dnc"
  private val serviceProviderError = "service_provider_error"
  private val unknownReason = "unknown_reason"
  private val serverErrorOccurredWhileAnalyzing = "server_error_occurred_while_analyzing"

  case object CampaignLimitReached extends CampaignPauseReason {
    override def toKey(): String = campaignLimitReached
  }

  case object PreviousStepIsNotDone extends CampaignPauseReason {
    override def toKey(): String = previousStepIsNotDone
  }

  case object AllProspectsCompleted extends CampaignPauseReason {
    override def toKey(): String = allProspectsCompleted
  }

  case object ProspectsHaveInvalidEmails extends CampaignPauseReason {
    override def toKey(): String = prospectsHaveInvalidEmails
  }

  case object ProspectsHaveMissingMergeTags extends CampaignPauseReason {
    override def toKey(): String = prospectsHaveMissingMergeTags
  }

  case object EmailAccountSendLimitReached extends CampaignPauseReason {
    override def toKey(): String = emailAccountSendLimitReached
  }

  case object NoProspectLeftInCurrentTimezone extends CampaignPauseReason {
    override def toKey(): String = noProspectLeftInCurrentTimezone
  }

  case object RestProspectsAreInDNC extends CampaignPauseReason {
    override def toKey(): String = restProspectsAreInDNC
  }

  case object ServiceProviderError extends CampaignPauseReason {
    override def toKey(): String = serviceProviderError
  }

  case object UnknownReason extends CampaignPauseReason {
    override def toKey(): String = unknownReason
  }

  case object ServerErrorOccurredWhileAnalyzing extends CampaignPauseReason {
    override def toKey(): String = serverErrorOccurredWhileAnalyzing
  }

  def fromKey(key: String): Try[CampaignPauseReason] = Try {
    key match {
      case `campaignLimitReached` => CampaignLimitReached
      case `previousStepIsNotDone` => PreviousStepIsNotDone
      case `allProspectsCompleted` => AllProspectsCompleted
      case `prospectsHaveInvalidEmails` => ProspectsHaveInvalidEmails
      case `prospectsHaveMissingMergeTags` => ProspectsHaveMissingMergeTags
      case `emailAccountSendLimitReached` => EmailAccountSendLimitReached
      case `noProspectLeftInCurrentTimezone` => NoProspectLeftInCurrentTimezone
      case `restProspectsAreInDNC` => RestProspectsAreInDNC
      case `serviceProviderError` => ServiceProviderError
      case `unknownReason` => UnknownReason
      case `serverErrorOccurredWhileAnalyzing` => ServerErrorOccurredWhileAnalyzing
    }
  }

  implicit def writes: Writes[CampaignPauseReason] = new Writes[CampaignPauseReason] {
    override def writes(o: CampaignPauseReason): JsValue = {
      JsString(o.toKey())
    }
  }
}

sealed trait CampaignPauseReason {
  def toKey(): String
}