package api.campaigns.models

import play.api.libs.json.{JsString, JsValue, Writes}

import scala.util.Try

sealed trait LowCampaignSendingVolumeReason {
  def toKey(): String
}

object LowCampaignSendingVolumeReason {

  private val campaignLimitReached = "campaign_limit_reached"
  private val noProspectLeft = "no_prospects_left"
  private val unknownReason = "unknown_reason"

  case object CampaignLimitReached extends LowCampaignSendingVolumeReason {
    override def toKey(): String = campaignLimitReached
  }

  case object UnknownReason extends LowCampaignSendingVolumeReason {
    override def toKey(): String = unknownReason
  }

  case object NoProspectsLeft extends LowCampaignSendingVolumeReason {
    override def toKey(): String = noProspectLeft
  }

  case class OldReasons(reason: String) extends LowCampaignSendingVolumeReason {
    override def toKey(): String = reason
  }

  def fromKey(key: String): Try[LowCampaignSendingVolumeReason] = Try{
    key match {
      case `campaignLimitReached` => CampaignLimitReached
      case `unknownReason` => UnknownReason
      case `noProspectLeft` => NoProspectsLeft
      case other => OldReasons(other)
    }
  }

  implicit def writes: Writes[LowCampaignSendingVolumeReason] = new Writes[LowCampaignSendingVolumeReason] {
    override def writes(o: LowCampaignSendingVolumeReason): JsValue = {
      JsString(o.toKey())
    }
  }
}
