package api.campaigns.models

import api.campaigns.services.ValidateCampaignStepVariantError

sealed trait UpdateVariantError

object UpdateVariantError {

  case class ErrorWhileCheckTeamHasAccessError(
    e: Option[Throwable]
  ) extends UpdateVariantError

  case object TemplateIsFromLibraryError extends UpdateVariantError

  case object CampaignStepNotFound extends UpdateVariantError

  case object CannotUpdateStepType extends UpdateVariantError

  case class CampaignStepVariantValidationError(err: ValidateCampaignStepVariantError) extends UpdateVariantError

  case class ValidateTemplateError(
    e: Throwable
  ) extends UpdateVariantError

  case class TemplateNotFoundWhileUpdateError(
    errMsg: String
  ) extends UpdateVariantError

  case object StepVariantNotFoundError extends UpdateVariantError

  case class UpdateVariantServerError(
    e: Throwable
  ) extends UpdateVariantError

}