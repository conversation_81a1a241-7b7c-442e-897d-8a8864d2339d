package api.campaigns.models

import api.accounts.TeamId
import api.campaigns.ChannelSettingUuid
import play.api.libs.json.{Json, OWrites, Reads}

case class WhatsappSettingSenderDetails(
  channel_setting_uuid: ChannelSettingUuid,
  team_id: TeamId,
  phone_number: String,
  first_name: String,
  last_name: String,
)

object WhatsappSettingSenderDetails {

  implicit val reads: Reads[WhatsappSettingSenderDetails] = Json.reads[WhatsappSettingSenderDetails]
  implicit val writes: OWrites[WhatsappSettingSenderDetails] = Json.writes[WhatsappSettingSenderDetails]

}