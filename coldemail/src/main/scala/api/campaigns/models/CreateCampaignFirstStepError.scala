package api.campaigns.models

import play.api.libs.json.{Js<PERSON>ath, JsonValidationError}

sealed trait CreateCampaignFirstStepError

object CreateCampaignFirstStepError {

  case class CampaignStepCreateValidationError(
    errors: Seq[(J<PERSON><PERSON><PERSON>, Seq[JsonValidationError])]
  ) extends CreateCampaignFirstStepError

  case object TemplateIsFromLibraryFieldMissing extends CreateCampaignFirstStepError

  case class ValidateCampaignTemplateError(
    e: Throwable,
    campaignStepData: CampaignStepData
  ) extends CreateCampaignFirstStepError

  case class TemplateAccessCheckError(e: Throwable) extends CreateCampaignFirstStepError

  case class TemplateNotFoundError(errMsg: String) extends CreateCampaignFirstStepError

  case class CreateStepSQLException(e: Throwable) extends CreateCampaignFirstStepError

  case object CreatedStepNotFound extends CreateCampaignFirstStepError

  case class ParentStepNotFound(
    parent_step_id: Long
  ) extends CreateCampaignFirstStepError

}