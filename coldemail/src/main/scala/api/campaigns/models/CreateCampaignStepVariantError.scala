package api.campaigns.models

import api.campaigns.services.ValidateCampaignStepVariantError

sealed trait CreateCampaignStepVariantError

object CreateCampaignStepVariantError {

  case object CampaignStepNotFound extends CreateCampaignStepVariantError

  case class MaxVariantsExceeded(maxVariants: Int) extends CreateCampaignStepVariantError

  case object TemplateIsFromLibraryFieldMissing extends CreateCampaignStepVariantError

  case class CampaignStepVariantValidationError(err: ValidateCampaignStepVariantError) extends CreateCampaignStepVariantError

  case class ValidateCampaignTemplateError(
    err: Throwable
  ) extends CreateCampaignStepVariantError

  case class ErrorWhileCreateCampaignFirst(
    err: CreateCampaignFirstStepError
  ) extends CreateCampaignStepVariantError


  case class TemplateAccessCheckError(e: Throwable) extends CreateCampaignStepVariantError

  case class TemplateNotFoundError(errMsg: String) extends CreateCampaignStepVariantError

  case object CreatedStepVariantNotFound extends CreateCampaignStepVariantError

  case class CreateStepVariantException(err: Throwable) extends CreateCampaignStepVariantError

  case class FeatureUsageEventSaveError(err: Throwable) extends CreateCampaignStepVariantError


}