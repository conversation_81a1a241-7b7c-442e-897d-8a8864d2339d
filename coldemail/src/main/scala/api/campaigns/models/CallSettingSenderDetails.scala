package api.campaigns.models

import api.accounts.TeamId
import api.campaigns.ChannelSettingUuid
import play.api.libs.json.{Json, OWrites, Reads}

case class CallSettingSenderDetails(
  channel_setting_uuid: ChannelSettingUuid,
  team_id: TeamId,
  phone_number: Option[String],
  first_name: String,
  last_name: String,
)

object CallSettingSenderDetails {

  implicit val reads: Reads[CallSettingSenderDetails] = Json.reads[CallSettingSenderDetails]
  implicit val writes: OWrites[CallSettingSenderDetails] = Json.writes[CallSettingSenderDetails]

}