package api.campaigns.models

import play.api.libs.json.{<PERSON><PERSON>, Writes}

case class CampaignPauseReasonAndCounts(
  campaignPauseReason: CampaignPauseReason,
  totalSentProspects: Int = -1,
  totalUnsentProspects: Int = -1,
  totalUnsentProspectsInDNC: Int = -1,
  totalUnsentProspectsInSameTz: Int = -1,
  total_unsent_prospects_with_invalid_emails: Int = -1,
  total_unsent_prospects_with_previous_task_not_done: Int = -1,
  total_unsent_prospects_that_are_likely_valid: Int = -1,
  total_unsent_prospects_with_missing_merge_tags: Int = -1,
)

object CampaignPauseReasonAndCounts {
  implicit val writes: Writes[CampaignPauseReasonAndCounts] = Json.writes[CampaignPauseReasonAndCounts]
}