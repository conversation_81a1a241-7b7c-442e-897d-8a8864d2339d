package api.campaigns.models

sealed trait GetTestStepsError

object GetTestStepsError {
  case class ErrorWhileValidateCampaignTemplate(err: ValidateCampaignTemplateError) extends GetTestStepsError

  case object InvalidEmailTemplate extends GetTestStepsError

  case object ReceiveReplyToEmailNotGiven extends GetTestStepsError

  case class ErrorWhileGettingEmailBody(err: Throwable) extends GetTestStepsError

  case class ErrorWhileInsertingMultipleForCampaign(err: Throwable) extends GetTestStepsError

  case class EmailNotSent(err: Option[Throwable]) extends GetTestStepsError

  case class ErrorWhileGettingProspectCategoryId(err: Throwable) extends GetTestStepsError
}