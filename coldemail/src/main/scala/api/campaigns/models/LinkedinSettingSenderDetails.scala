package api.campaigns.models

import api.accounts.TeamId
import api.campaigns.ChannelSettingUuid
import play.api.libs.json.{Json, OWrites, Reads}

case class LinkedinSettingSenderDetails(
  channel_setting_uuid: ChannelSettingUuid,
  team_id: TeamId,
  email: String,
  first_name: String,
  last_name: String,
  linkedin_profile_url: Option[String],
  automation_enabled: Boolean
)

object LinkedinSettingSenderDetails {

  implicit val reads: Reads[LinkedinSettingSenderDetails] = Json.reads[LinkedinSettingSenderDetails]
  implicit val writes: OWrites[LinkedinSettingSenderDetails] = Json.writes[LinkedinSettingSenderDetails]

}