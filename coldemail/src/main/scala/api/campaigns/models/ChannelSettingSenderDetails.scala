package api.campaigns.models

import api.accounts.TeamId
import api.campaigns.ChannelSettingUuid
import sr_scheduler.models.ChannelType

case class ChannelSettingSenderDetails(
  channel_type: ChannelType,
  channel_setting_uuid: ChannelSettingUuid,
  team_id: TeamId,
  owner_account_id: AccountData.AccountId,
  email: Option[String],
  phone_number: Option[String],
  sender_name: String,
  first_name: String,
  last_name: String,
)
