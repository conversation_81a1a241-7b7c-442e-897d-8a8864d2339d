package api.campaigns.models

import api.campaigns.CampaignEditedPreviewStep
import play.api.libs.json.{<PERSON>son, Writes}

case class StepsForPreview( // campaignSteps for preview
  prospect_id: Long,
  prospect_email: Option[String],
  prospect_name: String,
  prospect_linkedin: Option[String],
  prospect_phone: Option[String],
  prospect_company: Option[String],
  steps: Vector[CampaignEditedPreviewStep]
)

object StepsForPreview {
  implicit val writes: Writes[StepsForPreview] = Json.writes[StepsForPreview]
}