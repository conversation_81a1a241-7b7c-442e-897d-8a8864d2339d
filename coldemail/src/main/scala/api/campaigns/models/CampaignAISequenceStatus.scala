package api.campaigns.models

import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Writes}

import scala.util.Try

sealed trait CampaignAISequenceStatus

object CampaignAISequenceStatus {
  private val triggered = "triggered"
  private val finished = "finished"

  case object Triggered extends CampaignAISequenceStatus {
    override def toString: String = triggered
  }

  case object Finished extends CampaignAISequenceStatus {
    override def toString: String = finished
  }

  def fromString(key: String): Try[CampaignAISequenceStatus] = Try{
    key match {
      case `triggered` => Triggered
      case `finished` => Finished
    }
  }

  implicit val writes: Writes[CampaignAISequenceStatus] = new Writes[CampaignAISequenceStatus] {
    override def writes(o: CampaignAISequenceStatus): JsValue = {
      Json.toJson(o.toString)
    }
  }
}
