package api.campaigns.models

import api.campaigns.services.CampaignId
import play.api.libs.json._

case class StepLabel(label: String) extends AnyVal {
  override def toString: String = label
}

object StepLabel{
  implicit val reads: Reads[StepLabel] = new Reads[StepLabel] {
    override def reads(ev: JsValue): JsResult[StepLabel] = {
      ev match {
        case JsString(name) => JsSuccess(StepLabel(label = name))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[StepLabel] = new Writes[StepLabel] {
    override def writes(o: StepLabel): JsValue = JsString(o.label)
  }
}



case class CampaignStepRelatedDataForProspect(
                                               campaign_name: CampaignName,
                                               campaign_id: CampaignId,
                                               current_step_id: CurrentStepId,
                                               step_label: StepLabel
                                             )
