package api.campaigns.models

import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, OFormat, Writes}
import utils.sr_json_utils.CampaignStepTypeJsonUtil.jsonFormatCampaignStepType

sealed trait PreviewData {
  def step_type: CampaignStepType

  def toString: String
}

object PreviewData {

  private val autoEmailPreview = "auto_email_preview"
  private val autoEmailMagicPreview = "auto_email_magic_preview"
  private val manualEmailPreview = "manual_email_preview"
  private val manualEmailMagicPreview = "manual_email_magic_preview"
  private val linkedinViewProfilePreview = "linkedin_view_profile_preview"
  private val linkedinConnectionRequestPreview = "linkedin_connection_request_preview"
  private val linkedinInMailPreview = "linkedin_inmail_preview"
  private val linkedinMessagePreview = "linkedin_message_preview"
  private val autoLinkedinViewProfilePreview = "auto_linkedin_view_profile_preview"
  private val autoLinkedinConnectionRequestPreview = "auto_linkedin_connection_request_preview"
  private val autoLinkedinInMailPreview = "auto_linkedin_inmail_preview"
  private val autoLinkedinMessagePreview = "auto_linkedin_message_preview"
  private val whatsappPreview = "whatsapp_preview"
  private val smsPreview = "sms_preview"
  private val generalPreview = "general_preview"
  private val callPreview = "call_preview"
  private val moveToAnotherCampaignPreview = "move_to_another_campaign_preview"

  case class AutoEmailPreview(
                           step_type: CampaignStepType = CampaignStepType.AutoEmailStep,
                           subject: String,
                           body: String,
                           editable_body: Option[String],
                           body_preview: String,
                           edited: Boolean,
                           email_thread_id: Option[Long]
                         ) extends PreviewData {
    override def toString: String = autoEmailPreview
  }

  object AutoEmailPreview {
    given format: OFormat[AutoEmailPreview] = Json.format[AutoEmailPreview]
  }

  case class AutoEmailMagicPreview(
                               step_type: CampaignStepType = CampaignStepType.AutoEmailMagicContent,
                               generated_subject: Option[String],
                               generated_body: Option[String],
                               editable_body: Option[String],
                               generated_body_preview: Option[String],
                               edited: Boolean,
                               email_thread_id: Option[Long]
                             ) extends PreviewData {
    override def toString: String = autoEmailMagicPreview
  }

  object AutoEmailMagicPreview {
    given format: OFormat[AutoEmailMagicPreview] = Json.format[AutoEmailMagicPreview]
  }



  case class ManualEmailPreview(
                           step_type: CampaignStepType = CampaignStepType.ManualEmailStep,
                           subject: String,
                           body: String,
                           editable_body: Option[String],
                           body_preview: String,
                           edited: Boolean,
                           email_thread_id: Option[Long]
                         ) extends PreviewData {
    override def toString: String = manualEmailPreview
  }

  object ManualEmailPreview {
    given format: OFormat[ManualEmailPreview] = Json.format[ManualEmailPreview]
  }

  case class ManualEmailMagicPreview(
                                 step_type: CampaignStepType = CampaignStepType.ManualEmailMagicContent,
                                 generated_subject: Option[String],
                                 generated_body: Option[String],
                                 editable_body: Option[String],
                                 generated_body_preview: Option[String],
                                 edited: Boolean,
                                 email_thread_id: Option[Long]
                               ) extends PreviewData {
    override def toString: String = manualEmailMagicPreview
  }

  object ManualEmailMagicPreview {
    given format: OFormat[ManualEmailMagicPreview] = Json.format[ManualEmailMagicPreview]
  }

  case class LinkedinViewProfilePreview(
                                         step_type: CampaignStepType = CampaignStepType.LinkedinViewProfile
                                       ) extends PreviewData {

    override def toString: String = linkedinViewProfilePreview

  }

  object LinkedinViewProfilePreview {
    given format: OFormat[LinkedinViewProfilePreview] = Json.format[LinkedinViewProfilePreview]
  }

  case class LinkedinConnectionRequestPreview(
                                               step_type: CampaignStepType = CampaignStepType.LinkedinConnectionRequest,
                                               body: Option[String],
                                               body_preview: Option[String]
                                             ) extends PreviewData {
    override def toString: String = linkedinConnectionRequestPreview
  }

  object LinkedinConnectionRequestPreview {
    given format: OFormat[LinkedinConnectionRequestPreview] = Json.format[LinkedinConnectionRequestPreview]
  }

  case class LinkedinInMailPreview(
                                    step_type: CampaignStepType = CampaignStepType.LinkedinInmail,
                                    subject: Option[String],
                                    body: String,
                                    body_preview: String
                                  ) extends PreviewData {
    override def toString: String = linkedinInMailPreview
  }

  object LinkedinInMailPreview {
    given format: OFormat[LinkedinInMailPreview] = Json.format[LinkedinInMailPreview]
  }

  case class LinkedinMessagePreview(
                                     step_type: CampaignStepType = CampaignStepType.LinkedinMessage,
                                     body: String,
                                     body_preview: String
                                   ) extends PreviewData {
    override def toString: String = linkedinMessagePreview
  }

  object LinkedinMessagePreview {
    given format: OFormat[LinkedinMessagePreview] = Json.format[LinkedinMessagePreview]
  }

  case class AutoLinkedinViewProfilePreview(
                                         step_type: CampaignStepType = CampaignStepType.AutoLinkedinViewProfile
                                       ) extends PreviewData {

    override def toString: String = autoLinkedinViewProfilePreview

  }

  object AutoLinkedinViewProfilePreview {
    given format: OFormat[AutoLinkedinViewProfilePreview] = Json.format[AutoLinkedinViewProfilePreview]
  }

  case class AutoLinkedinConnectionRequestPreview(
                                               step_type: CampaignStepType = CampaignStepType.AutoLinkedinConnectionRequest,
                                               body: Option[String],
                                               body_preview: Option[String]
                                             ) extends PreviewData {
    override def toString: String = autoLinkedinConnectionRequestPreview
  }

  object AutoLinkedinConnectionRequestPreview {
    given format: OFormat[AutoLinkedinConnectionRequestPreview] = Json.format[AutoLinkedinConnectionRequestPreview]
  }

  case class AutoLinkedinInMailPreview(
                                    step_type: CampaignStepType = CampaignStepType.AutoLinkedinInmail,
                                    subject: String,
                                    body: String,
                                    body_preview: String
                                  ) extends PreviewData {
    override def toString: String = autoLinkedinInMailPreview
  }

  object AutoLinkedinInMailPreview {
    given format: OFormat[AutoLinkedinInMailPreview] = Json.format[AutoLinkedinInMailPreview]
  }

  case class AutoLinkedinMessagePreview(
                                     step_type: CampaignStepType = CampaignStepType.AutoLinkedinMessage,
                                     body: String,
                                     body_preview: String
                                   ) extends PreviewData {
    override def toString: String = autoLinkedinMessagePreview
  }

  object AutoLinkedinMessagePreview {
    given format: OFormat[AutoLinkedinMessagePreview] = Json.format[AutoLinkedinMessagePreview]
  }

  case class WhatsappPreview(
                              step_type: CampaignStepType = CampaignStepType.WhatsappMessage,
                              body: String,
                              body_preview: String
                            ) extends PreviewData {
    override def toString: String = whatsappPreview
  }

  object WhatsappPreview {
    given format: OFormat[WhatsappPreview] = Json.format[WhatsappPreview]
  }

  case class SmsPreview(
                         step_type: CampaignStepType = CampaignStepType.SmsMessage,
                         body: String,
                         body_preview: String
                       ) extends PreviewData {
    override def toString: String = smsPreview
  }

  object SmsPreview {
    given format: OFormat[SmsPreview] = Json.format[SmsPreview]
  }

  case class GeneralPreview(
                             step_type: CampaignStepType = CampaignStepType.GeneralTask,
                             body: String,
                             body_preview: String
                           ) extends PreviewData {
    override def toString: String = generalPreview
  }

  object GeneralPreview {
    given format: OFormat[GeneralPreview] = Json.format[GeneralPreview]
  }

  case class CallPreview(
                          body: String,
                          body_preview: String,
                          step_type: CampaignStepType = CampaignStepType.CallStep
                        ) extends PreviewData {
    override def toString: String = callPreview
  }

  object CallPreview {
    given format: OFormat[CallPreview] = Json.format[CallPreview]
  }


  case class MoveToAnotherCampaignPreview(
                             step_type: CampaignStepType = CampaignStepType.MoveToAnotherCampaignStep,
                           ) extends PreviewData {
    override def toString: String = moveToAnotherCampaignPreview
  }

  object MoveToAnotherCampaignPreview {
    given format: OFormat[MoveToAnotherCampaignPreview] = Json.format[MoveToAnotherCampaignPreview]
  }


  implicit def writes : Writes[PreviewData] = new Writes[PreviewData] {
    def writes(ev: PreviewData): JsValue = {
      ev match {
        case data: AutoEmailPreview => Json.toJson(data)
        case data: ManualEmailPreview => Json.toJson(data)
        case data: LinkedinViewProfilePreview => Json.toJson(data)
        case data: LinkedinMessagePreview => Json.toJson(data)
        case data: LinkedinInMailPreview => Json.toJson(data)
        case data: LinkedinConnectionRequestPreview => Json.toJson(data)
        case data: AutoLinkedinViewProfilePreview => Json.toJson(data)
        case data: AutoLinkedinMessagePreview => Json.toJson(data)
        case data: AutoLinkedinInMailPreview => Json.toJson(data)
        case data: AutoLinkedinConnectionRequestPreview => Json.toJson(data)
        case data: WhatsappPreview => Json.toJson(data)
        case data: CallPreview => Json.toJson(data)
        case data: SmsPreview => Json.toJson(data)
        case data: GeneralPreview => Json.toJson(data)
        case data: MoveToAnotherCampaignPreview => Json.toJson(data)
        case data: AutoEmailMagicPreview => Json.toJson(data)
        case data: ManualEmailMagicPreview => Json.toJson(data)
      }
    }
  }

}


