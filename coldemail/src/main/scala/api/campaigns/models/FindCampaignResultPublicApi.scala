package api.campaigns.models

import api.campaigns.services.CampaignSettingsDataForV3
import api.reports.AllCampaignStats
import api.tags.models.CampaignTag
import org.joda.time.DateTime
import play.api.libs.json.{Js<PERSON><PERSON>r, JsResult, JsSuccess, JsValue, <PERSON>son, OWrites, Reads}
import sr_scheduler.CampaignStatus
import play.api.libs.json.JodaWrites._
import play.api.libs.json.JodaReads._

import scala.util.{Failure, Success, Try}

case class FindCampaignsListingApiV3Response(
                                            campaigns: List[FindCampaignResultPublicApiV3],
                                            links: NavigationLinksResponse
                                            )


case class FindCampaignResultPublicApi(
                                        id: Int,
                                        name: String,
                                        owner_id: Long,
                                        status: CampaignStatus,
                                        stats: AllCampaignStatsResultPublicApi,
                                        created_at: DateTime,
                                      )


case class FindCampaignResultPublicApiV3(
                                          uuid: Option[String],
                                          name: String,
                                          created_at: DateTime,
                                          status: CampaignStatus,
                                          team_uuid: String,
                                          settings: CampaignSettingsDataForV3
                                      )

case class AllCampaignStatsResultPublicApi(
                                            total_sent: Int,
                                            total_opened: Int,
                                            total_clicked: Int,
                                            total_replied: Int
                                          )
case class CampaignPublicApiErrorV3(
                                     error_type: String,
                                     message: String,
                                     data: Option[String]
                                   )
object CampaignPublicApiErrorV3{
  implicit val reads: Reads[CampaignPublicApiErrorV3] = Json.reads[CampaignPublicApiErrorV3]
  implicit val writes: OWrites[CampaignPublicApiErrorV3] = Json.writes[CampaignPublicApiErrorV3]
}

object AllCampaignStatsResultPublicApi{
  implicit val reads: Reads[AllCampaignStatsResultPublicApi] = Json.reads[AllCampaignStatsResultPublicApi]
}

object FindCampaignResultPublicApi {

  import utils.sr_json_utils.CampaignStatusJsonUtil.readsCampaignStatus

  implicit val reads: Reads[FindCampaignResultPublicApi] = new Reads[FindCampaignResultPublicApi] {
    override def reads(json: JsValue): JsResult[FindCampaignResultPublicApi] = Try {
      FindCampaignResultPublicApi(
        id = (json \ "id").as[Int],
        name = (json \ "name").as[String],
        owner_id = (json \ "owner_id").as[Long],
        status = (json \ "status").as[CampaignStatus],
        stats = (json \ "stats").as[AllCampaignStatsResultPublicApi],
        created_at = (json \ "created_at").as[DateTime]
      )
    } match {
      case Failure(e) => JsError(e.getMessage)
      case Success(campaignResultPublicApi) => JsSuccess(campaignResultPublicApi)
    }
  }
}

object FindCampaignResultPublicApiV3 {

  import utils.sr_json_utils.CampaignStatusJsonUtil.readsCampaignStatus

  implicit val reads: Reads[FindCampaignResultPublicApiV3] = new Reads[FindCampaignResultPublicApiV3] {
    override def reads(json: JsValue): JsResult[FindCampaignResultPublicApiV3] = Try {
      FindCampaignResultPublicApiV3(
        uuid = (json \ "id").asOpt[String],
        name = (json \ "name").as[String],
        created_at = (json \ "created_at").as[DateTime],
        status = (json \ "status").as[CampaignStatus],
        team_uuid = (json \ "team_id").as[String],
        settings = (json \ "settings").as[CampaignSettingsDataForV3]
      )
    } match {
      case Failure(e) => JsError(e.getMessage)
      case Success(campaignResultPublicApiV3) => JsSuccess(campaignResultPublicApiV3)
    }
  }
}

object FindCampaignsListingApiV3Response{
  implicit val reads: Reads[FindCampaignsListingApiV3Response] = Json.reads[FindCampaignsListingApiV3Response]
}
