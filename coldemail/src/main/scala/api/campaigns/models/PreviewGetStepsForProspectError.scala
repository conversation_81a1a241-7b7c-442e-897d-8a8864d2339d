package api.campaigns.models

sealed trait PreviewGetStepsForProspectError

object PreviewGetStepsForProspectError {

  case object NoStepInCampaign extends PreviewGetStepsForProspectError

  case object ProspectNotFoundInCampaign extends PreviewGetStepsForProspectError

  case class InternalServerError(err: Throwable) extends PreviewGetStepsForProspectError

  case class ErrorWhileGettingProspectObjects(err: Throwable) extends PreviewGetStepsForProspectError

  case class ValidationError(err_message: String) extends PreviewGetStepsForProspectError

}