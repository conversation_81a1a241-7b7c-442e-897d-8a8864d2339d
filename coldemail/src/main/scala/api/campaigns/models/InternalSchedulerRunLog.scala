package api.campaigns.models

import api.accounts.TeamId
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import sr_scheduler.models.ChannelType
import utils.mq.channel_scheduler.channels.model.SchedulerSteps

case class InternalSchedulerRunLog(
                                    team_id: TeamId,
                                    internal_scheduler_run_log_data : InternalSchedulerRunLogData,
                                    started_at: DateTime,
                                    completed_at: DateTime,
                                    saved_tasks_count: Int,
                                    reached_scheduler_step: SchedulerSteps,
                                    log_trace_id: String
                                  )


case class InternalSchedulerRunLogReporting(
                                             team_id: TeamId,
                                             email_setting_id: EmailSettingId,
                                             reached_scheduler_step: SchedulerSteps,
                                             total_runs: Int,
                                             no_of_waste_runs: Int
                                           )
