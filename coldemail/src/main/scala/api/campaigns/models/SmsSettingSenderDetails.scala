package api.campaigns.models

import api.accounts.TeamId
import api.campaigns.ChannelSettingUuid
import play.api.libs.json.{Json, OWrites, Reads}

case class SmsSettingSenderDetails(
  channel_setting_uuid: ChannelSettingUuid,
  team_id: TeamId,
  phone_number: String,
  first_name: String,
  last_name: String,
)

object SmsSettingSenderDetails {

  implicit val reads: Reads[SmsSettingSenderDetails] = Json.reads[SmsSettingSenderDetails]
  implicit val writes: OWrites[SmsSettingSenderDetails] = Json.writes[SmsSettingSenderDetails]

}