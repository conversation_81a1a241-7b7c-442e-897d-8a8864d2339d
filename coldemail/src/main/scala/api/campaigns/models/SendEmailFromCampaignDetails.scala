package api.campaigns.models

import play.api.libs.json.{Json, OFormat}

case class StepDetails(
                        step_id: Long,
                        step_name: String,
                        step_type: CampaignStepType
                      )

object StepDetails {
  import utils.sr_json_utils.CampaignStepTypeJsonUtil.jsonFormatCampaignStepType
  given format: OFormat[StepDetails] = Json.format[StepDetails]
}

case class SendEmailFromCampaignDetails(
                                         campaign_id: Long,
                                         campaign_name: String,
                                         // stepDetails can be none when email is being sent manually from Inbox.
                                         // Example: sendNewEmailManually in InboxV3Service
                                         stepDetails: Option[StepDetails]
                                       )

object SendEmailFromCampaignDetails {
  given format: OFormat[SendEmailFromCampaignDetails] = Json.format[SendEmailFromCampaignDetails]
}



case class SenderRotationStats(
                                prospects_not_sent_any_emails: Int,
                                prospects_to_get_follow_up: Int
                              )

object SenderRotationStats {
  given format: OFormat[SenderRotationStats] = Json.format[SenderRotationStats]
}