package api.campaigns.models

import api.accounts.models.AccountId
import api.campaigns.services.CampaignId
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import sr_scheduler.CampaignStatus

case class CampaignInboxSettingData(
                                   campaign_id: CampaignId,
                                   campaign_status: CampaignStatus,
                                   receiver_email_setting_id: EmailSettingId,
                                   sender_email_setting_id: EmailSettingId,
                                   account_id: AccountId,
                                   created_at: DateTime
                                   )
