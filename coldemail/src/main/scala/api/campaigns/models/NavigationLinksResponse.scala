package api.campaigns.models

import play.api.libs.json.{Json, Reads, Writes}

case class NavigationLinksResponse(
  next: Option[String],
)

object NavigationLinksResponse {
  implicit val writes: Writes[NavigationLinksResponse] = new Writes[NavigationLinksResponse] {
    def writes(data: NavigationLinksResponse) = {

      Json.obj(
        "next" -> data.next,
      )
    }
  }
  implicit val reads: Reads[NavigationLinksResponse] = Json.reads[NavigationLinksResponse]
}