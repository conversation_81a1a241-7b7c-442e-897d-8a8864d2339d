package api.campaigns.models

sealed trait DeleteCampaignTasksError

object DeleteCampaignTasksError {

  case class ErrorWhileFetchingWebhooksForCampaign(e: Throwable) extends DeleteCampaignTasksError

  case object CampaignAssociatedToWebhook extends DeleteCampaignTasksError


  case class ErrorWhileSelectAndPublish(e: Throwable) extends DeleteCampaignTasksError

  case class DeleteCampaignsSqlError(e: Throwable) extends DeleteCampaignTasksError

  case object NoCampaignFoundToDelete extends DeleteCampaignTasksError

  case class DeleteAssociatedTaskSqlError(e: Throwable) extends DeleteCampaignTasksError

}