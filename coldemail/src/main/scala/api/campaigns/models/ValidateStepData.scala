package api.campaigns.models

import api.campaigns.services.CampaignId

sealed trait ValidateStepData {
  val campaignStepData: CampaignStepData
}

object ValidateStepData {

  case class ValidateStepVariantData(
    campaignStepData: CampaignStepData,
    campaignId: CampaignId,
    stepId: Option[Long],
    head_step_id: Option[Long],
  ) extends ValidateStepData


  case class ValidateCampaignTemplateData(
    campaignStepData: CampaignStepData,
  ) extends ValidateStepData

}