package api.campaigns.models

sealed trait ValidateCampaignTemplateError

object ValidateCampaignTemplateError {
  case class ErrorWhileValidatingBody(err: Throwable) extends ValidateCampaignTemplateError

  case class ErrorWhileValidatingSubject(err: Throwable) extends ValidateCampaignTemplateError

  case class BodyCantHavePreviousSubject(err: Throwable) extends ValidateCampaignTemplateError

  case class SubjectCantHaveSignature(err: Throwable) extends ValidateCampaignTemplateError

  case class PreviousSubInFirstEmail(err: Throwable) extends ValidateCampaignTemplateError

  case class ErrorWhileValidatingNotes(err: Throwable) extends ValidateCampaignTemplateError

  case class NotesCantHavePreviousSubject(err: Throwable) extends ValidateCampaignTemplateError
}