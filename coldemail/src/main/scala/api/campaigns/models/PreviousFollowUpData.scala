package api.campaigns.models

import api.campaigns.{PreviousFollowUp, models}
import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, <PERSON>sSuc<PERSON>, <PERSON>sV<PERSON>ue, <PERSON>son, OFormat, Reads, Writes}
import utils.sr_json_utils.CampaignStepTypeJsonUtil.jsonFormatCampaignStepType

import scala.util.{Failure, Success, Try}
sealed trait PreviousFollowUpData {
  def toString: String

  def step_type: CampaignStepType
}


object PreviousFollowUpData {

  private val autoEmailFollowUp = "auto_email_follow_up"
  private val manualEmailFollowUp = "manual_email_follow_up"
  private val linkedinViewProfileFollowUp = "linkedin_view_profile_follow_up"
  private val linkedinConnectionRequestFollowUp = "linkedin_connection_request_follow_up"
  private val linkedinInMailFollowUp = "linkedin_inmail_follow_up"
  private val linkedinMessageFollowUp = "linkedin_message_follow_up"
  private val autoLinkedinViewProfileFollowUp = "auto_linkedin_view_profile_follow_up"
  private val autoLinkedinConnectionRequestFollowUp = "auto_linkedin_connection_request_follow_up"
  private val autoLinkedinInMailFollowUp = "auto_linkedin_inmail_follow_up"
  private val autoLinkedinMessageFollowUp = "auto_linkedin_message_follow_up"
  private val whatsappFollowUp = "whatsapp_follow_up"
  private val smsFollowUp = "sms_follow_up"
  private val generalFollowUp = "general_follow_up"
  private val callFollowUp = "call_follow_up"
  private val autoEmailMagicFollowup = "auto_email_magic_follow_up"
  private val manualEmailMagicFollowup = "manual_email_magic_follow_up"


  case class AutoEmailFollowUp(
                               step_type: CampaignStepType = CampaignStepType.AutoEmailStep,
                               email_thread_id: Option[Long],
                               from_name: String,
                               subject: String,
                               body: String,
                               base_body: String,
                               from_email: String,
                               is_edited_preview_email: Boolean
                             ) extends PreviousFollowUpData {
    override def toString: String = autoEmailFollowUp
  }

  object AutoEmailFollowUp {
    given format: OFormat[AutoEmailFollowUp] = Json.format[AutoEmailFollowUp]
  }

  case class AutoEmailMagicFollowUp(
                                     step_type: CampaignStepType = CampaignStepType.AutoEmailStep,
                                     email_thread_id: Option[Long],
                                     from_name: String,
                                     generated_subject: Option[String],
                                     generated_body: Option[String],
                                     generated_base_body: Option[String],
                                     from_email: String,
                                     is_edited_preview_email: Boolean

                                   ) extends PreviousFollowUpData {
    override def toString: String = autoEmailMagicFollowup
  }

  object AutoEmailMagicFollowUp {
    given format: OFormat[AutoEmailMagicFollowUp] = Json.format[AutoEmailMagicFollowUp]
  }


  case class ManualEmailFollowUp(
                                 step_type: CampaignStepType = CampaignStepType.ManualEmailStep,
                                 email_thread_id: Option[Long],
                                 from_name: String,
                                 subject: String,
                                 body: String,
                                 base_body: String,
                                 from_email: String,
                                 is_edited_preview_email: Boolean
                               ) extends PreviousFollowUpData {
    override def toString: String = manualEmailFollowUp
  }

  object ManualEmailFollowUp {
    given format: OFormat[ManualEmailFollowUp] = Json.format[ManualEmailFollowUp]
  }

  case class ManualEmailMagicFollowUp(
                                  step_type: CampaignStepType = CampaignStepType.ManualEmailStep,
                                  email_thread_id: Option[Long],
                                  from_name: String,
                                  generated_subject: Option[String],
                                  generated_body: Option[String],
                                  generated_base_body: Option[String],
                                  from_email: String,
                                  is_edited_preview_email: Boolean
                                ) extends PreviousFollowUpData {
    override def toString: String = manualEmailMagicFollowup
  }

  object ManualEmailMagicFollowUp {
    given format: OFormat[ManualEmailMagicFollowUp] = Json.format[ManualEmailMagicFollowUp]
  }

  case class LinkedinViewProfileFollowUp(
                                         step_type: CampaignStepType = CampaignStepType.LinkedinViewProfile,
                                         from_name: String,
                                         from_email: String
                                       ) extends PreviousFollowUpData {

    override def toString: String = linkedinViewProfileFollowUp

  }

  object LinkedinViewProfileFollowUp {
    given format: OFormat[LinkedinViewProfileFollowUp] = Json.format[LinkedinViewProfileFollowUp]
  }

  case class LinkedinConnectionRequestFollowUp(
                                               step_type: CampaignStepType = CampaignStepType.LinkedinConnectionRequest,
                                               body: Option[String],
                                               base_body: Option[String],
                                               from_name: String,
                                               from_email: String
                                             ) extends PreviousFollowUpData {
    override def toString: String = linkedinConnectionRequestFollowUp
  }

  object LinkedinConnectionRequestFollowUp {
    given format: OFormat[LinkedinConnectionRequestFollowUp] = Json.format[LinkedinConnectionRequestFollowUp]
  }

  case class LinkedinInMailFollowUp(
                                    step_type: CampaignStepType = CampaignStepType.LinkedinInmail,
                                    subject: Option[String],
                                    body: String,
                                    base_body: String,
                                    from_name: String,
                                    from_email: String
                                  ) extends PreviousFollowUpData {
    override def toString: String = linkedinInMailFollowUp
  }

  object LinkedinInMailFollowUp {
    given format: OFormat[LinkedinInMailFollowUp] = Json.format[LinkedinInMailFollowUp]
  }

  case class LinkedinMessageFollowUp(
                                     step_type: CampaignStepType = CampaignStepType.LinkedinMessage,
                                     body: String,
                                     base_body: String,
                                     from_name: String,
                                     from_email: String
                                   ) extends PreviousFollowUpData {
    override def toString: String = linkedinMessageFollowUp
  }

  object LinkedinMessageFollowUp {
    given format: OFormat[LinkedinMessageFollowUp] = Json.format[LinkedinMessageFollowUp]
  }

  case class AutoLinkedinViewProfileFollowUp(
                                          step_type: CampaignStepType = CampaignStepType.AutoLinkedinViewProfile,
                                          from_name: String,
                                          from_email: String
                                        ) extends PreviousFollowUpData {

    override def toString: String = autoLinkedinViewProfileFollowUp

  }

  object AutoLinkedinViewProfileFollowUp {
    given format: OFormat[AutoLinkedinViewProfileFollowUp] = Json.format[AutoLinkedinViewProfileFollowUp]
  }

  case class AutoLinkedinConnectionRequestFollowUp(
                                                step_type: CampaignStepType = CampaignStepType.AutoLinkedinConnectionRequest,
                                                body: Option[String],
                                                base_body: Option[String],
                                                from_name: String,
                                                from_email: String
                                              ) extends PreviousFollowUpData {
    override def toString: String = autoLinkedinConnectionRequestFollowUp
  }

  object AutoLinkedinConnectionRequestFollowUp {
    given format: OFormat[AutoLinkedinConnectionRequestFollowUp] = Json.format[AutoLinkedinConnectionRequestFollowUp]
  }

  case class AutoLinkedinInMailFollowUp(
                                     step_type: CampaignStepType = CampaignStepType.AutoLinkedinInmail,
                                     subject: String,
                                     body: String,
                                     base_body: String,
                                     from_name: String,
                                     from_email: String
                                   ) extends PreviousFollowUpData {
    override def toString: String = autoLinkedinInMailFollowUp
  }

  object AutoLinkedinInMailFollowUp {
    given format: OFormat[AutoLinkedinInMailFollowUp] = Json.format[AutoLinkedinInMailFollowUp]
  }

  case class AutoLinkedinMessageFollowUp(
                                      step_type: CampaignStepType = CampaignStepType.AutoLinkedinMessage,
                                      body: String,
                                      base_body: String,
                                      from_name: String,
                                      from_email: String // FIXME: Change this to from_profile_url
                                    ) extends PreviousFollowUpData {
    override def toString: String = autoLinkedinMessageFollowUp
  }

  object AutoLinkedinMessageFollowUp {
    given format: OFormat[AutoLinkedinMessageFollowUp] = Json.format[AutoLinkedinMessageFollowUp]
  }

  case class WhatsappFollowUp(
                              step_type: CampaignStepType = CampaignStepType.WhatsappMessage,
                              body: String,
                              base_body: String,
                              from_name: String,
                              whatsapp_number: String
                            ) extends PreviousFollowUpData {
    override def toString: String = whatsappFollowUp
  }

  object WhatsappFollowUp {
    given format: OFormat[WhatsappFollowUp] = Json.format[WhatsappFollowUp]
  }

  case class SmsFollowUp(
                         step_type: CampaignStepType = CampaignStepType.SmsMessage,
                         body: String,
                         base_body: String,
                         from_name: String,
                         phone_number: String
                       ) extends PreviousFollowUpData {
    override def toString: String = smsFollowUp
  }

  object SmsFollowUp {
    given format: OFormat[SmsFollowUp] = Json.format[SmsFollowUp]
  }

  case class GeneralFollowUp(
                             step_type: CampaignStepType = CampaignStepType.GeneralTask,
                             body: String,
                             base_body: String
                           ) extends PreviousFollowUpData {
    override def toString: String = generalFollowUp
  }

  object GeneralFollowUp {
    given format: OFormat[GeneralFollowUp] = Json.format[GeneralFollowUp]
  }

  case class CallFollowUp(
                          body: String,
                          base_body: String,
                          from_name: String,
                          phone_number: String,
                          step_type: CampaignStepType = CampaignStepType.CallStep
                        ) extends PreviousFollowUpData {
    override def toString: String = callFollowUp
  }

  object CallFollowUp {
    given format: OFormat[CallFollowUp] = Json.format[CallFollowUp]
  }

  case class MoveToAnotherCampaignFollowUp(
                              step_type: CampaignStepType = CampaignStepType.MoveToAnotherCampaignStep,
                            ) extends PreviousFollowUpData {
    override def toString: String = generalFollowUp
  }

  object MoveToAnotherCampaignFollowUp {
    given format: OFormat[MoveToAnotherCampaignFollowUp] = Json.format[MoveToAnotherCampaignFollowUp]
  }

  implicit def writes : Writes[PreviousFollowUpData] = new Writes[PreviousFollowUpData] {
    def writes(ev: PreviousFollowUpData): JsValue = {
      ev match {
        case data: AutoEmailFollowUp => Json.toJson(data)
        case data: ManualEmailFollowUp => Json.toJson(data)
        case data: LinkedinViewProfileFollowUp => Json.toJson(data)
        case data: LinkedinMessageFollowUp => Json.toJson(data)
        case data: LinkedinInMailFollowUp => Json.toJson(data)
        case data: LinkedinConnectionRequestFollowUp => Json.toJson(data)
        case data: AutoLinkedinConnectionRequestFollowUp => Json.toJson(data)
        case data: AutoLinkedinMessageFollowUp => Json.toJson(data)
        case data: AutoLinkedinInMailFollowUp => Json.toJson(data)
        case data: AutoLinkedinViewProfileFollowUp => Json.toJson(data)
        case data: WhatsappFollowUp => Json.toJson(data)
        case data: CallFollowUp => Json.toJson(data)
        case data: SmsFollowUp => Json.toJson(data)
        case data: GeneralFollowUp => Json.toJson(data)
        case data: MoveToAnotherCampaignFollowUp => Json.toJson(data)
        case data: AutoEmailMagicFollowUp => Json.toJson(data)
        case data: ManualEmailMagicFollowUp => Json.toJson(data)
      }
    }
  }

  implicit def reads: Reads[PreviousFollowUpData] = (ev: JsValue) => Try {

    CampaignStepType.fromKey((ev \ "step_type").as[String]) match {

      case Failure(e) =>
        throw e

      case Success(stepType) =>

        stepType match {

          case CampaignStepType.AutoEmailStep =>

            JsSuccess(
              AutoEmailFollowUp(
                email_thread_id = (ev \ "email_thread_id").asOpt[Long],
                from_name = (ev \ "from_name").as[String],
                subject = (ev \ "subject").as[String],
                body = (ev \ "body").as[String],
                base_body = (ev \ "base_body").as[String],
                from_email = (ev \ "from_email").as[String],
                is_edited_preview_email = (ev \ "is_edited_preview_email").as[Boolean]
              )
            )

          case CampaignStepType.ManualEmailStep =>
            JsSuccess(
              ManualEmailFollowUp(
                email_thread_id = (ev \ "email_thread_id").asOpt[Long],
                from_name = (ev \ "from_name").as[String],
                subject = (ev \ "subject").as[String],
                body = (ev \ "body").as[String],
                base_body = (ev \ "base_body").as[String],
                from_email = (ev \ "from_email").as[String],
                is_edited_preview_email = (ev \ "is_edited_preview_email").as[Boolean]
              )
            )


          case CampaignStepType.LinkedinViewProfile =>

            JsSuccess(
              LinkedinViewProfileFollowUp(
                from_name = (ev \ "from_name").as[String],
                from_email = (ev \ "from_email").as[String],
              )
            )

          case CampaignStepType.LinkedinConnectionRequest =>

            JsSuccess(
              LinkedinConnectionRequestFollowUp(
                body = (ev \ "body").asOpt[String],
                base_body = (ev \ "base_body").asOpt[String],
                from_email = (ev \ "from_email").as[String],
                from_name = (ev \ "from_name").as[String],
              )
            )

          case CampaignStepType.LinkedinInmail =>

            JsSuccess(
              LinkedinInMailFollowUp(
                subject = (ev \ "subject").asOpt[String],
                body = (ev \ "body").as[String],
                base_body = (ev \ "base_body").as[String],
                from_email = (ev \ "from_email").as[String],
                from_name = (ev \ "from_name").as[String],
              )
            )

          case CampaignStepType.LinkedinMessage =>

            JsSuccess(
              LinkedinMessageFollowUp(
                body = (ev \ "body").as[String],
                base_body = (ev \ "base_body").as[String],
                from_email = (ev \ "from_email").as[String],
                from_name = (ev \ "from_name").as[String],
              )
            )

          case CampaignStepType.AutoLinkedinViewProfile =>

            JsSuccess(
              AutoLinkedinViewProfileFollowUp(
                from_name = (ev \ "from_name").as[String],
                from_email = (ev \ "from_email").as[String],
              )
            )

          case CampaignStepType.AutoLinkedinConnectionRequest =>

            JsSuccess(
              AutoLinkedinConnectionRequestFollowUp(
                body = (ev \ "body").asOpt[String],
                base_body = (ev \ "base_body").asOpt[String],
                from_email = (ev \ "from_email").as[String],
                from_name = (ev \ "from_name").as[String],
              )
            )

          case CampaignStepType.AutoLinkedinInmail =>

            JsSuccess(
              AutoLinkedinInMailFollowUp(
                subject = (ev \ "subject").as[String],
                body = (ev \ "body").as[String],
                base_body = (ev \ "base_body").as[String],
                from_email = (ev \ "from_email").as[String],
                from_name = (ev \ "from_name").as[String],
              )
            )

          case CampaignStepType.AutoLinkedinMessage =>

            JsSuccess(
              AutoLinkedinMessageFollowUp(
                body = (ev \ "body").as[String],
                base_body = (ev \ "base_body").as[String],
                from_email = (ev \ "from_email").as[String],
                from_name = (ev \ "from_name").as[String],
              )
            )

          case CampaignStepType.WhatsappMessage =>
            JsSuccess(
              WhatsappFollowUp(
                body = (ev \ "body").as[String],
                base_body = (ev \ "base_body").as[String],
                from_name = (ev \ "from_name").as[String],
                whatsapp_number = (ev \ "whatsapp_number").as[String]
              )
            )

          case CampaignStepType.SmsMessage =>
            JsSuccess(
              SmsFollowUp(
                body = (ev \ "body").as[String],
                base_body = (ev \ "base_body").as[String],
                from_name = (ev \ "from_name").as[String],
                phone_number = (ev \ "phone_number").as[String]
              )
            )

          case CampaignStepType.CallStep =>
            JsSuccess(
              CallFollowUp(
                body = (ev \ "body").as[String],
                base_body = (ev \ "base_body").as[String],
                from_name = (ev \ "from_name").as[String],
                phone_number = (ev \ "phone_number").as[String]
              )
            )

          case CampaignStepType.GeneralTask =>

            JsSuccess(
              GeneralFollowUp(
                body = (ev \ "body").as[String],
                base_body = (ev \ "base_body").as[String],
              )
            )

          case CampaignStepType.MoveToAnotherCampaignStep => // Fixme-drip-activity-type
            JsSuccess(
              MoveToAnotherCampaignFollowUp()
            )

          case CampaignStepType.ManualEmailMagicContent =>

            JsSuccess(
              ManualEmailMagicFollowUp(
                email_thread_id = (ev \ "email_thread_id").asOpt[Long],
                from_name = (ev \ "from_name").as[String],
                generated_subject = (ev \ "generated_subject").asOpt[String],
                generated_body = (ev \ "generated_body").asOpt[String],
                generated_base_body = (ev \ "generated_base_body").asOpt[String],
                from_email = (ev \ "from_email").as[String],
                is_edited_preview_email = (ev \ "is_edited_preview_email").as[Boolean]
              )
            )

          case CampaignStepType.AutoEmailMagicContent =>

            JsSuccess(
              AutoEmailMagicFollowUp(
                email_thread_id = (ev \ "email_thread_id").asOpt[Long],
                from_name = (ev \ "from_name").as[String],
                generated_subject = (ev \ "generated_subject").asOpt[String],
                generated_body = (ev \ "generated_body").asOpt[String],
                generated_base_body = (ev \ "generated_base_body").asOpt[String],
                from_email = (ev \ "from_email").as[String],
                is_edited_preview_email = (ev \ "is_edited_preview_email").as[Boolean]
              )
            )
        }
    }

  } match {
    case Failure(e) => JsError(e.toString)
    case Success(v) => v
  }


}