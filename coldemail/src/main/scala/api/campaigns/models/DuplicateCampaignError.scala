package api.campaigns.models

sealed trait DuplicateCampaignError

object DuplicateCampaignError {
  case object NoStepsFoundError extends DuplicateCampaignError

  case class CreateCampaignError(err: CampaignCreationError) extends DuplicateCampaignError

  case class DbFailure(e: Throwable) extends DuplicateCampaignError

  case object CampaignWithStatsAndEmailNotFound extends DuplicateCampaignError

}