package api.campaigns.models

sealed trait CampaignCreationError

object CampaignCreationError {

  case class CampaignNameNotUniqueError(err: Throwable) extends CampaignCreationError

  case class SQLException(err: Throwable) extends CampaignCreationError

  case object CampaignNotCreated extends CampaignCreationError

  case class FeatureUsageEventSaveError(err: Throwable) extends CampaignCreationError

  case object InCorrectCampaignOwnerId extends CampaignCreationError

}