package api.campaigns

import api.campaigns.services.CampaignId
import api.accounts.TeamId
import api.campaigns.models.CampaignPauseReason
import org.joda.time.DateTime
import scalikejdbc._

import scala.util.Try

case class CampaignAnalysisResults(
                                    campaign_id: CampaignId,
                                    team_id: TeamId,
                                    campaign_analysis_result: CampaignPauseReason,
                                    total_sent_prospects: Int,
                                    total_unsent_prospects: Int,
                                    total_unsent_prospects_in_dnc: Int,
                                    total_unsent_prospects_in_same_tz: Int,
                                    total_unsent_prospects_with_invalid_emails: Int,
                                    total_unsent_prospects_with_previous_task_not_done: Int,
                                    total_unsent_prospects_that_are_likely_valid: Int,
                                    total_unsent_prospects_with_missing_merge_tags: Int
                                  )

class CampaignSendReportsDAO {

  def saveCampaignAnalysisResults(
                                   campaignAnalysisResults: CampaignAnalysisResults
                                 ): Try[Long] = Try {

    DB.autoCommit {implicit session =>
      sql"""
            INSERT INTO campaign_send_start_reports(
              campaign_id,
              team_id,
              campaign_analysis_result,
              total_sent_prospects,
              total_unsent_prospects,
              total_unsent_prospects_in_dnc,
              total_unsent_prospects_in_same_tz,
              total_unsent_prospects_with_invalid_emails,
              total_unsent_prospects_with_previous_task_not_done,
              total_unsent_prospects_that_are_likely_valid,
              total_unsent_prospects_with_missing_merge_tags
            ) VALUES (
              ${campaignAnalysisResults.campaign_id.id},
              ${campaignAnalysisResults.team_id.id},
              ${campaignAnalysisResults.campaign_analysis_result.toKey()},
              ${campaignAnalysisResults.total_sent_prospects},
              ${campaignAnalysisResults.total_unsent_prospects},
              ${campaignAnalysisResults.total_unsent_prospects_in_dnc},
              ${campaignAnalysisResults.total_unsent_prospects_in_same_tz},
              ${campaignAnalysisResults.total_unsent_prospects_with_invalid_emails},
              ${campaignAnalysisResults.total_unsent_prospects_with_previous_task_not_done},
              ${campaignAnalysisResults.total_unsent_prospects_that_are_likely_valid},
              ${campaignAnalysisResults.total_unsent_prospects_with_missing_merge_tags}
            )
            RETURNING id;
           """
        .map(rs => rs.long("id"))
        .single
        .apply()
        .get
    }

  }

}