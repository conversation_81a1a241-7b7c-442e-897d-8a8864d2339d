package api.campaigns

import api.accounts.TeamId
import api.campaigns.models.{CampaignStepId, CampaignType}
import api.campaigns.services.{CampaignId, CampaignStepService}
import api.prospects.models.StepId
import scalikejdbc.{DB, DBSession}
import sr_scheduler.CampaignStatus
import utils.SRLogger
import utils.dbutils.DBUtils

import scala.util.{Failure, Success, Try}

class CampaignStepDAOService(
  campaignStepDAO: CampaignStepDAO,
  dbUtils: DBUtils,
  campaignStepVariantDAO: CampaignStepVariantDAO,
) {


  /*
    10 April 2024: when step is deleted, its parent step will always be present [ except for headstep ] then we move
    campaigns_prospects to parent step.
   */
  /*
  def revertCampaignProspectOnStepDeletion(

                                            parentRelation: Option[CampaignStepRelation],
                                            campaign_id: CampaignId,
                                            team_id: TeamId,
                                            step_id: StepId

                                          )(implicit session: DBSession, logger: SRLogger): Try[Int] = {

    for {

      previous_step_basic_details: Option[CampaignStep] <- Success(

        parentRelation match {

          case None =>

            None

          case Some(data) =>

            campaignStepDAO.findById(
              stepId = data.parent_id.toLong,
              campaignId = campaign_id.id
            )


        }


      )

      update_campaign_prospect: Int <- {

        previous_step_basic_details match {

          case None =>

            // head_step_id got deleted
            campaignStepDAO.revertCampaignProspectAtHeadStep(
              team_id = team_id,
              campaign_id = campaign_id,
              step_id = step_id
            )

          case Some(basic_details) =>

            // not the headStepId
            campaignStepDAO.revertCampaignProspectNotatHeadStep(
              team_id = team_id,
              campaign_id = campaign_id,
              step_id = step_id,
              basic_details = basic_details
            )

        }

      }

    } yield {

      update_campaign_prospect

    }


  }*/

  // called from -
  // CampaignController.deleteVariant
  def delete(step: CampaignStep, campaign: Campaign, teamId: TeamId)(using Logger: SRLogger): Try[Int] = {

    DB localTx { implicit session =>

      Try {
        val stepId = step.id

        val isHeadStep = stepId == campaign.head_step_id.getOrElse(-1)

        val parentRelation = campaignStepDAO.findParentRelation(step_id = StepId(id = step.id), campaign_id = CampaignId(id = campaign.id))
        val childRelation = campaignStepDAO.getChildParentRelation(step_id = StepId(id = step.id), campaign_id = CampaignId(id = campaign.id))

        (parentRelation.isDefined, childRelation.isDefined) match {


          case (false, false) =>
            // no parentRelation, no childRelation, this case should only be possible when the campaign_type is drip and campaign is not started
            if(campaign.settings.campaign_type == CampaignType.Drip && campaign.status == CampaignStatus.NOT_STARTED) {

              campaignStepDAO.setHeadStepIdNull(
                campaignId = CampaignId(campaign.id),
                teamId = teamId
              )

              campaignStepDAO.deleteFromCampaignSteps(
                stepId = StepId(id = stepId),
                campaign_id = CampaignId(id = campaign.id)
              )
            } else {
              throw new Exception(s"step has no parentRelation and no childRelation; stepId: $stepId")
            }

          case (true, false) =>

//            revertCampaignProspectOnStepDeletion(
//              parentRelation = parentRelation,
//              campaign_id = CampaignId(id = campaign.id),
//              team_id = teamId,
//              step_id = StepId(id =stepId)
//            )

            // this is the last step of the campaign
            campaignStepDAO.deleteFromCampaignStepsRelationChild(
              child_id = StepId(id = stepId),
              campaign_id = CampaignId(id  = campaign.id)
            )


            // if this the current step for any campaign, update their current_step to the previous step ie parent step
            campaignStepDAO.updateCampaignProspect(
              parentRelation = parentRelation.get,
              step_id = StepId(id = stepId),
              campaign_id = CampaignId(id  = campaign.id),
              team_id = teamId
            )

            // delete the step now
            campaignStepDAO.deleteFromCampaignSteps(
              stepId = StepId(id = stepId),
              campaign_id = CampaignId(id  = campaign.id)
            )


          case (false, true) =>
            // this should be the head step currently

            if (!isHeadStep) {
              throw new Exception(s"step has no parent and has child, but is not even head step: $stepId")
            }

            // before this is deleted lets make its child as the head_step
            campaignStepDAO.campaignHeadStepId(
              child_relation = childRelation.get,
              campaign_id = CampaignId(id = campaign.id),
              team_id = teamId
            )

//            revertCampaignProspectOnStepDeletion(
//              parentRelation = parentRelation,
//              campaign_id = CampaignId(id = campaign.id),
//              team_id = teamId,
//              step_id = StepId(id = stepId)
//            )

            // now this is no longer the head step, lets delete this
            campaignStepDAO.deleteFromCampaignStepsRelationParent(
              parent_id = StepId(id = stepId),
              campaign_id = CampaignId(id  = campaign.id)
            )

            // if this is the current step for any prospect, update their current step to null, since this is the first step
            campaignStepDAO.updateCampaignProspectToNull(
              step_id = StepId(id = stepId),
              campaign_id = CampaignId(id  = campaign.id),
              team_id = teamId
            )


            // delete the step now
            campaignStepDAO.deleteFromCampaignSteps(
              stepId = StepId(id = stepId),
              campaign_id = CampaignId(id  = campaign.id)
            )


          case (true, true) =>
            // this is a mid-step, there are steps above and below this

//            revertCampaignProspectOnStepDeletion(
//              parentRelation = parentRelation,
//              campaign_id = CampaignId(id = campaign.id),
//              team_id = teamId,
//              step_id = StepId(id = stepId)
//            )

            // delete the parent relation, i.e. where this is the child
            campaignStepDAO.deleteFromCampaignStepsRelationChild(
              child_id = StepId(id = stepId),
              campaign_id = CampaignId(id  = campaign.id)
            )

            // delete the child relation, i.e. where this is the parent
            campaignStepDAO.deleteFromCampaignStepsRelationParent(
              parent_id = StepId(id = stepId),
              campaign_id = CampaignId(id  = campaign.id)
            )

            // create a relation between its parent and child
            campaignStepDAO.addCampaignStepRelation(
              parent_relation = parentRelation.get,
              child_relation = childRelation.get,
              campaign_id = CampaignId(id = campaign.id)
            )

            // if this the current step for any campaign, update their current_step to the previous step, ie parent step
            campaignStepDAO.updateCampaignProspect(
              parentRelation = parentRelation.get,
              step_id = StepId(id = stepId),
              campaign_id = CampaignId(id = campaign.id),
              team_id = teamId
            )

            // delete the step now
            campaignStepDAO.deleteFromCampaignSteps(
              stepId = StepId(id = stepId),
              campaign_id = CampaignId(id = campaign.id)
            )
        }


      }

    }
  }

  def swapStepSubjects(
    campaignId: CampaignId,
    campaignStepId1: CampaignStepId,
    campaignStepId2: CampaignStepId,
    s2variants: Seq[CampaignStepVariant],
    s1variants: Seq[CampaignStepVariant],
  )(
    implicit session: DBSession,
    logger: SRLogger
  ): Try[Int] = {

    val s1VariantSubOptList: Seq[Option[String]] =
      CampaignStepService.getStepVariantSubjectsWhichSupportPrevSubMergeTag(
        stepVariantsData = s1variants.map(_.step_data)
      )

    val s2VariantSubOptList: Seq[Option[String]] =
      CampaignStepService.getStepVariantSubjectsWhichSupportPrevSubMergeTag(
        stepVariantsData = s2variants.map(_.step_data)
      )

    val s1VariantSubjects: Seq[String] = s1VariantSubOptList.flatten

    val s2VariantSubjects: Seq[String] = s2VariantSubOptList.flatten

    if (s1VariantSubjects.length != 1 || s2VariantSubjects.length != 1) {

      logger.error(
        msg = s"Failed to swap step subject - len variants not equal to 1. campaignId: $campaignId :: campaignStepId1: $campaignStepId1 :: s1variants: $s1variants :: campaignStepId2: $campaignStepId2 :: $s2variants"
      )

      Failure(new Exception("Cannot swap subjects of steps which have more than one variants."))

    } else {

      for {

        // assign step2 subject to step1 subject
        _: Int <- campaignStepVariantDAO.updateCampaignStepVariantSubject(
          newSubject = s1VariantSubjects.head,
          campaignId = campaignId,
          campaignStepId = campaignStepId2,
          variantId = s2variants.head.id,
        )

        // assign step1 subject to step2 subject
        count: Int <- campaignStepVariantDAO.updateCampaignStepVariantSubject(
          newSubject = s2VariantSubjects.head,
          campaignId = campaignId,
          campaignStepId = campaignStepId1,
          variantId = s1variants.head.id,
        )

      } yield {

        count

      }

    }

  }


  def reorderCampaignStepsAndUpdateLabels(
    campaignId: CampaignId,
    teamId: TeamId,
    newCampaignHeadStepId: CampaignStepId,
    prevCampaignHeadStepId: CampaignStepId,
    newHeadStepVariants: Seq[CampaignStepVariant],
    prevHeadStepVariants: Seq[CampaignStepVariant],
    headStepVariantsHavePrevSubMergeTag: Boolean,
    newCampaignStepsOrderTail: List[CampaignStepId]
  )(
    implicit logger: SRLogger
  ): Try[Int] = {

    val dbAndSession = dbUtils.startLocalTx()

    implicit val session: DBSession = dbAndSession.session

    val updateCount = if (headStepVariantsHavePrevSubMergeTag) {

      swapStepSubjects(
        campaignId = campaignId,
        campaignStepId1 = prevCampaignHeadStepId,
        s1variants = prevHeadStepVariants,
        campaignStepId2 = newCampaignHeadStepId,
        s2variants = newHeadStepVariants
      )

    } else {

      Success(0)

    }

    val tryOfInsertCount = updateCount.flatMap { _ =>

      campaignStepDAO.deleteAllCampaignStepsRelationships(
        campaignId = campaignId,
        teamId = teamId,
      ) match {

        case Failure(exception) =>

          logger.shouldNeverHappen(
            msg = s"Failed to delete campaign steps. campaignId: $campaignId :: teamId: $teamId",
            err = Some(exception)
          )

          Failure(exception)

        case Success(_) =>

          campaignStepDAO.updateHeadStep(
            campaignId = campaignId,
            teamId = teamId,
            newHeadStepId = newCampaignHeadStepId,
          ) match {

            case Failure(exception) =>

              logger.shouldNeverHappen(
                msg = s"Failed to update campaign head step. campaignId: $campaignId :: teamId: $teamId :: newHeadStepId: $newCampaignHeadStepId",
                err = Some(exception)
              )

              Failure(exception)

            case Success(None) =>

              val errMsg = "Failed to update head step id"

              logger.shouldNeverHappen(
                msg = s"$errMsg - returned None. campaignId: $campaignId :: teamId: $teamId :: newHeadStepId: $newCampaignHeadStepId"
              )

              Failure(new Exception(errMsg))

            case Success(Some(newHeadStepId)) =>

              val orderedCampaignStepRelations = CampaignStepDAOService.getOrderedCampaignStepRelations(
                campaignId = campaignId,
                headStepId = newHeadStepId,
                campaignStepsOrderTail = newCampaignStepsOrderTail,
              )

              campaignStepDAO.insertCampaignStepRelations(
                campaignId = campaignId,
                campaignStepRelations = orderedCampaignStepRelations
              ) match {

                case Failure(exception) =>

                  logger.shouldNeverHappen(
                    msg = s"Failed to insert campaign steps. campaignId: $campaignId :: campaignStepRelations: $orderedCampaignStepRelations",
                    err = Some(exception)
                  )

                  Failure(exception)

                case Success(stepInsertCount) =>

                  Success(stepInsertCount)

              }

          }

      }

    }

    dbUtils.commitAndCloseSession(db = dbAndSession.db)

    /**
      * 18-May-2024
      *
      * Not putting it in a txn,
      * because in `CampaignService.delete` there is a comment,
      * which say it causes isolation issues.
      *
      * Not sure about it though.
      */
    tryOfInsertCount.flatMap { insertCount =>

      campaignStepDAO._updateStepLabels(
        campaignId = campaignId.id,
        teamId = teamId,
      ).map { _ =>

        insertCount

      }

    }

  }

}


object CampaignStepDAOService {

  def getOrderedCampaignStepRelations(
    campaignId: CampaignId,
    campaignStepsOrderTail: List[CampaignStepId],
    headStepId: CampaignStepId,
  ): List[CampaignStepRelation] = {

    var parentStepId: CampaignStepId = headStepId

    campaignStepsOrderTail.map { s =>

      val csr = CampaignStepRelation(
        parent_id = parentStepId.id,
        child_id = s.id,
        campaign_id = campaignId.id,
        conditions = None
      )

      parentStepId = s

      csr

    }

  }

}
