package api.campaigns.services

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.{AccountId, OrgId}
import api.accounts.{Account, AccountEmail, AccountUuid, TeamId, TeamMember}
import api.calendar_app.models.CalendarAccountData
import api.campaigns.{Campaign, CampaignStepDAO, CampaignStepVariantDAO, CampaignStepWithChildren, PreviousFollowUp}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepData, CampaignStepType, PreviewData, PreviousFollowUpData}
import api.columns.InternalMergeTagValuesForProspect
import api.emails.dao_service.EmailScheduledDAOService
import api.emails.models.EmailServiceProviderSendEmail.getEmailServiceProviderSendEmail
import api.emails.{EmailScheduledDAO, EmailSettingDAO}
import api.emails.models.EmailSendingFlow
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.{ProspectCategory, ProspectCategoryId, ProspectId}
import api.prospects.{ProspectAccountDAO1, ProspectUuid}
import eventframework.{ProspectObject, ProspectObjectInternal}
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import play.api.libs.json.{JsValue, Json}
import play.api.libs.ws.WSClient
import sr_scheduler.models
import utils.{Helpers, SRLogger}
import utils.email.{EmailHelper, EmailOptionsForGetBodies, EmailSenderService, EmailServiceCompanion}
import utils.emailvalidation.EmailValidationService
import utils.mq.email.MQEmailMessage
import utils.templating.TemplateService
import sr_scheduler.models.ChannelType
import utils.dbutils.DBUtils
import utils.email.models.SendScheduleEmailType
import utils.helpers.LogHelpers

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import play.api.libs.ws.WSBodyWritables.writeableOf_JsValue
import utils.uuid.SrUuidUtils

class CampaignTestStepService(
  prospectDAOService: ProspectDAOService,
  campaignTemplateService: CampaignTemplateService,
  prospectAccountDAO: ProspectAccountDAO1,
//  campaignStepDAO: CampaignStepDAO,
  campaignStepVariantDAO: CampaignStepVariantDAO,
  templateService: TemplateService,
  emailSettingDAO: EmailSettingDAO,
  emailSenderService: EmailSenderService,
  emailServiceCompanion: EmailServiceCompanion,
  emailScheduledDAOService: EmailScheduledDAOService,
  campaignDAOService: CampaignDAOService,
  srUuidUtils: SrUuidUtils
) {


  // SENDER_ROTATION
  // Called from -
  // CampaignController.testStep
  def testStep(
                stepId: Option[Long],
                body: String,
                subject: String,
                campaign: Campaign,
                toEmail: String,
                campaignId: CampaignId,
                stepType: CampaignStepType,
                allTrackingDomainsUsed: Seq[String],
                campaign_email_settings_id: CampaignEmailSettingsId,
                team_id: TeamId,
                owner_id: AccountId,
                ta_id: Long,
                owner_name: String,
                owner_email: AccountEmail,
                calendar_account_data: Option[CalendarAccountData],
                emailSendingFlow: Option[EmailSendingFlow],
                org_id: OrgId
  )(
    implicit ec: ExecutionContext,
    Logger: SRLogger,
    wsClient: WSClient,
    system: ActorSystem
  ): Future[Either[GetTestStepsError,  Unit]] = {
    
    given srLogger: SRLogger = Logger // fixme given

    val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
      // allow testing for email only
      campaignStepData = CampaignStepData.AutoEmailStep(
        body = body,
        subject = subject
      ),
      campaignId = campaignId,
      stepId = stepId,
      head_step_id = campaign.head_step_id,
    )

    campaignTemplateService.validateCampaignTemplate(
      teamId = team_id.id,
      notes = None, // there are no notes present
      validateStepData = validateStepVariantData,
    ) match {

      case Left(a) =>

        Future(Left(GetTestStepsError.ErrorWhileValidateCampaignTemplate(a)))

      case Right(false) =>

        Future(Left(GetTestStepsError.InvalidEmailTemplate))


      case Right(true) =>

        val selected_campaign_email_settings = campaign.settings.campaign_email_settings.find(p => p.id.id == campaign_email_settings_id.id)


        val emailSettingOpt = selected_campaign_email_settings
          .flatMap { ces =>
            emailSettingDAO.find(ces.sender_email_setting_id.emailSettingId)
          }
        val receiveEmailSettingOpt = selected_campaign_email_settings.flatMap { ces =>
          emailSettingDAO.find(ces.receiver_email_setting_id.emailSettingId)
        }


        if (emailSettingOpt.isEmpty || receiveEmailSettingOpt.isEmpty) {

          Future(Left(GetTestStepsError.ReceiveReplyToEmailNotGiven))

        } else {


          val findProspect = prospectDAOService.findOneByCampaignId(
            Logger = Logger,
            campaignId = campaign.id,
            teamId = team_id.id
          )

          val testProspectTry: Try[ProspectObject] = if (findProspect.nonEmpty) Success(findProspect.get) else {

            val (emailUser, emailDomain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(toEmail)

            prospectDAOService.getProspectCategoryId(
              teamId = team_id,
              ProspectCategory.DO_NOT_CONTACT,
              account = None  //passing none because InboxPlacementCheck mq will be using this testStep function
            ) match {

              case Failure(err) =>
                Logger.fatal(msg = s"[testStep prospectDAOService.getProspectCategoryId] teamId::${team_id.id} text_id::${ProspectCategory.DO_NOT_CONTACT}", err = err)
                Failure(err)

              case Success(doNotContactCategoryId: ProspectCategoryId) =>
                val dummyProspect = ProspectObject(
                  id = 123,
                  owner_id = owner_id.id,
                  team_id = team_id.id,
                  first_name = Some("John"),
                  last_name = Some("Doe"),
                  email = Some(toEmail), //TODO: EMAIL_OPTIONAL check the path and test code
                  custom_fields = Json.obj(),

                  last_contacted_at = None,
                  last_contacted_at_phone = None,

                  created_at = DateTime.now().minusDays(10),

                  list = None,
                  company = None,
                  city = None,
                  country = None,
                  timezone = None,

                  prospect_category = "NOT CATEGORIZED",


                  state = None,
                  job_title = None,
                  phone = None,
                  phone_2 = None,
                  phone_3 = None,
                  linkedin_url = None,
                  latest_reply_sentiment_uuid = None,

                  internal = ProspectObjectInternal(

                    email_domain = Some(emailDomain),

                    list_id = None,

                    last_contacted_at = None,

                    prospect_category_id_custom = doNotContactCategoryId.id,
                    prospect_category_label_color = "#333",


                    invalid_email = Some(false),
                    prospect_account_id = None,
                    prospect_account_uuid = None,
                    prospect_account = None,

                    owner_email = owner_email.email,
                    owner_name = owner_name,

                    last_replied_at = None,
                    last_opened_at = None,
                      last_call_made_at = None,

                    prospect_source = None,
                    total_opens = 0,
                    total_clicks = 0,
                    active_campaigns = None,
                    current_campaign_id = None,

                    magic_columns = List(),

                    tags = None,
                    flags = Json.obj(),
                    latest_reply_sentiment = None

                  ),
                  current_step_type = None,
                  latest_task_done_at = None,
                  prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
                  owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
                  updated_at = DateTime.now()
                )

                Success(dummyProspect)

            }
          }

          testProspectTry match {

            case Failure(err) => Future(Left(GetTestStepsError.ErrorWhileGettingProspectCategoryId(err)))

            case Success(testProspect: ProspectObject) =>
              val prospectAccount = if (testProspect.internal.prospect_account_id.isDefined) {
                prospectAccountDAO.find(id = testProspect.id, teamId = testProspect.team_id)
              } else None


              var dummyPrevEmails: Seq[PreviousFollowUp] = Seq()

              val customTrackingDomain = if (emailSettingOpt.isDefined) emailSettingOpt.get.custom_tracking_domain else None
              val defaultUnsubscribeDomain = emailSettingOpt.map(_.default_unsubscribe_domain).getOrElse(AppConfig.defaultTrackingHost)

              val fromEmail = if (emailSettingOpt.isDefined) emailSettingOpt.get.email else AppConfig.adminEmail
              val fromName = if (emailSettingOpt.isDefined) Helpers.getSenderName(emailSettingOpt.get) else AppConfig.adminName


              val optOutLinkUrl = Some(EmailHelper._makeUnsubscribeLinkV4(
                campaignId = campaignId.id,
                prospectId = 0,
                stepId = 0,
                emailScheduledId = 0,
                customDomain = customTrackingDomain,
                defaultUnsubscribeDomain = defaultUnsubscribeDomain
              ))
              //stepId=0 refer to the first step here,
              //CampaignTestStepService to send test email. and it will be only send when atleast one step is defined.
              // thats why the use of stepId.getOrElse


              val calendarLink = if(campaign.settings.selected_calendar_data.isDefined){EmailHelper._makeCalendarLinkUrl(
                prospectId = ProspectId(id = testProspect.id),
                teamId = TeamId(id = testProspect.team_id),
                sender_name = fromName,
                campaignId = campaignId,
                stepId = stepId.getOrElse(0),
                calendarAccountData = calendar_account_data,
                selectedCalendarData = campaign.settings.selected_calendar_data
              ).toOption
              }else{
                None
              }

              var internalMergeTags = InternalMergeTagValuesForProspect(
                unsubscribe_link = optOutLinkUrl,
                sender_name = fromName,
                sender_first_name = emailSettingOpt.map(_.first_name).getOrElse(""),
                sender_last_name = emailSettingOpt.map(_.last_name).getOrElse(""),
                previous_subject = None,
                signature = emailSettingOpt.map(_.signature),
                sender_phone_number = None,
                calendar_link = calendarLink
              )


              //TODO: UNITTEST
              val previousSteps: Seq[CampaignStepWithChildren] = if (campaign.head_step_id.isDefined && campaign.settings.append_followups) {
                val steps = campaignDAOService.findOrderedSteps(
                  campaignId = campaignId.id,
                  teamId = team_id,
                )
                val previousStepsForUnsavedSteps = CampaignStepDAO.getOrderedSteps(
                  steps = steps,
                  headStepId = campaign.head_step_id.get
                )


                if (stepId.isDefined) {
                  previousStepsForUnsavedSteps.takeWhile(step => step.id != stepId.get)
                } else {
                  previousStepsForUnsavedSteps
                }
              } else {
                Seq()
              }


              for (prevStep <- previousSteps) {

                val subjectAndBody = CampaignStepData.getSubjectAndBodyFromStepData(
                  stepData = prevStep.variants.head.step_data
                )

                // FIXME: AiHyperPersonalizationService generate the content here and send one test step and deduct credit

                val (subject, body) = (subjectAndBody.subject, subjectAndBody.body)

                templateService.render(
                  template = subject,
                  prospect = testProspect,
                  internalMergeTags = internalMergeTags,
                  channel = ChannelType.EmailChannel // channel as Email as this is email specific
                ).map { renderedSubject =>
                  if (renderedSubject != "{{previous_subject}}") {
                    internalMergeTags = internalMergeTags.copy(
                      previous_subject = Some(renderedSubject)
                    )
                  }
                  templateService.render(
                    template = body,
                    prospect = testProspect,
                    internalMergeTags = internalMergeTags,
                    channel = ChannelType.EmailChannel // channel as Email as this is email specific
                  ).map { renderedBody => {


                    val previous_follow_up_data: PreviousFollowUpData = prevStep.variants.head.step_data match {

                      case data: CampaignStepData.AutoEmailStep =>

                        PreviousFollowUpData.AutoEmailFollowUp(
                          email_thread_id = None, // doesn't matter while testing
                          from_name = fromName,
                          subject = renderedSubject,
                          body = renderedBody,
                          base_body = renderedBody,
                          from_email = fromEmail,
                          is_edited_preview_email = false
                        )

                      case data: CampaignStepData.ManualEmailStep =>

                        PreviousFollowUpData.ManualEmailFollowUp(
                          email_thread_id = None, // doesn't matter while testing
                          from_name = fromName,
                          subject = renderedSubject,
                          body = renderedBody,
                          base_body = renderedBody,
                          from_email = fromEmail,
                          is_edited_preview_email = false
                        )

                      case data: CampaignStepData.AutoEmailMagicContentStep =>

                        val generated_subject = if(renderedSubject.trim.nonEmpty) Some(renderedSubject) else None
                        val generated_body = if(renderedBody.trim.nonEmpty) Some(renderedBody) else None
                        val generated_base_body = if(renderedBody.trim.nonEmpty) Some(renderedBody) else None

                        PreviousFollowUpData.AutoEmailMagicFollowUp(
                          email_thread_id = None, // doesn't matter while testing
                          from_name = fromName,
                          generated_subject = generated_subject,
                          generated_body = generated_body,
                          generated_base_body = generated_base_body,
                          from_email = fromEmail,
                          is_edited_preview_email = false
                        )

                      case data: CampaignStepData.ManualEmailMagicContentStep =>

                        val generated_subject = if(renderedSubject.trim.nonEmpty) Some(renderedSubject) else None
                        val generated_body = if(renderedBody.trim.nonEmpty) Some(renderedBody) else None
                        val generated_base_body = if(renderedBody.trim.nonEmpty) Some(renderedBody) else None

                        PreviousFollowUpData.ManualEmailMagicFollowUp(
                          email_thread_id = None, // doesn't matter while testing
                          from_name = fromName,
                          generated_subject = generated_subject,
                          generated_body = generated_body,
                          generated_base_body = generated_base_body,
                          from_email = fromEmail,
                          is_edited_preview_email = false
                        )

                      case _: CampaignStepData.LinkedinViewProfile
                           | _: CampaignStepData.LinkedinInmailData
                           | _: CampaignStepData.LinkedinMessageData
                           | _: CampaignStepData.LinkedinConnectionRequestData
                           | _: CampaignStepData.AutoLinkedinInmail
                           | _: CampaignStepData.AutoLinkedinMessage
                           | _: CampaignStepData.AutoLinkedinViewProfile
                           | _: CampaignStepData.AutoLinkedinConnectionRequest
                           | _: CampaignStepData.SmsMessageData
                           | _: CampaignStepData.WhatsappMessageData
                           | _: CampaignStepData.CallTaskData
                           | _: CampaignStepData.MoveToAnotherCampaignStepData
                           | _: CampaignStepData.GeneralTaskData
                      =>

                        throw new Exception("Impossible case: Step type can only be email types")

                    }

                    dummyPrevEmails = dummyPrevEmails ++ Seq(PreviousFollowUp(
                      channel_follow_up_data = previous_follow_up_data,
                      timezone = campaign.settings.timezone,
                      sent_at = prevStep.created_at,
                      step_id = Some(prevStep.id),
                      completed_reason = None,
                    ))
                  }
                  }
                }
              }


              val trackOpens = campaign.settings.open_tracking_enabled
              val trackClicks = campaign.settings.click_tracking_enabled

              emailServiceCompanion.getBodies(
                //                  emailSendDetail,
                editedPreviewEmail = None,
                org_id = org_id.id,
                calendarAccountData = calendar_account_data,
                head_step_id = campaign.head_step_id,
                email_scheduled_id = 0,
                campaign_id = Some(campaignId.id),
                step_id = stepId,
                prospect = testProspect,
                emailOptions = EmailOptionsForGetBodies(

                  isCampaignSendTestEmail = true,

                  for_editable_preview = false,
                  editedPreviewEmailAlreadyChecked = true, //28-10-24: Customers wanted to test original email and not the edited
                  custom_tracking_domain = customTrackingDomain,
                  default_tracking_domain = emailSettingOpt.map(_.default_tracking_domain).getOrElse(AppConfig.defaultTrackingHost),
                  default_unsubscribe_domain = emailSettingOpt.map(_.default_unsubscribe_domain).getOrElse(AppConfig.defaultTrackingHost),
                  signature = emailSettingOpt.map(_.signature),

                  opt_out_msg = campaign.settings.opt_out_msg,
                  opt_out_is_text = campaign.settings.opt_out_is_text,
                  append_followups = campaign.settings.append_followups,
                  bodyTemplate = body,
                  subjectTemplate = subject,

                  email_sender_name = emailSettingOpt.map(_.sender_name).getOrElse(""),
                  sender_first_name = emailSettingOpt.map(_.first_name).getOrElse(""),
                  sender_last_name = emailSettingOpt.map(_.last_name).getOrElse(""),
                  manualEmail = false,

                  trackOpens = trackOpens,
                  trackClicks = trackClicks,

                  previousEmails = dummyPrevEmails,
                  allTrackingDomainsUsed = allTrackingDomainsUsed,
                ),
                selectedCalendarData = campaign.settings.selected_calendar_data
              ) match {

                case Failure(e) =>

                  Future(Left(GetTestStepsError.ErrorWhileGettingEmailBody(e)))

                case Success(bodies) =>

                  // save scheduled email
                  val campaign_email_settings_id = campaign.settings.campaign_email_settings.headOption.get.id
                  var email = models.EmailScheduledNew(

                    campaign_id = Some(campaign.id),
                    step_id = None,
                    is_opening_step = false, // doesnt matter for test emails
                    prospect_id = None, // test email
                    prospect_account_id = None, // test email
                    added_at = DateTime.now,
                    scheduled_at = DateTime.now,
                    sender_email_settings_id = emailSettingOpt.get.id.get.emailSettingId, // FIXME VALUECLASS
                    template_id = None,
                    variant_id = None,
                    rep_mail_server_id = emailSettingOpt.get.rep_mail_server_id,

                    via_gmail_smtp = emailSettingOpt.map(es => {


                      if (es.service_provider == EmailServiceProvider.GMAIL_ALIAS) false // there is a problem sending via SMTP for alias accounts
                      // else es.via_gmail_smtp.getOrElse(false)
                      else {

                        es.via_gmail_smtp.getOrElse(false)

                        // es.service_provider == EmailServiceProvider.GMAIL_API
                      }

                    }),

                    team_id = campaign.team_id,
                    account_id = campaign.account_id,
                    campaign_name = Some(campaign.name),
                    step_name = None,
                    step_type = stepType,
                    receiver_email_settings_id = receiveEmailSettingOpt.get.id.get.emailSettingId, // FIXME VALUECLASS

                    to_email = toEmail,
                    to_name = None, // we're not taking the receiver name during testing,

                    from_email = emailSettingOpt.get.email,
                    from_name = Helpers.getSenderName(emailSettingOpt.get),

                    reply_to_email = if (emailSettingOpt.get.id.get == receiveEmailSettingOpt.get.id.get) None else Some(receiveEmailSettingOpt.get.email),
                    reply_to_name = if (emailSettingOpt.get.id.get == receiveEmailSettingOpt.get.id.get) None else Some(receiveEmailSettingOpt.get.sender_name),

                    rep_tracking_host_id = emailSettingOpt.map(_.rep_tracking_host_id).getOrElse(1).toLong,

                    scheduled_from_campaign = false,
                    scheduled_manually = true,

                    body = Some(bodies.htmlBody),
                    base_body = Some(bodies.baseBody),
                    text_body = Some(bodies.textBody),
                    // subject = Some(s"[TEST] ${bodies.subject}"),
                    subject = Some(bodies.subject),

                    email_thread_id = None,

                    has_open_tracking = trackOpens,
                    has_click_tracking = trackClicks,
                    has_unsubscribe_link = bodies.has_unsubscribe_link,
                    list_unsubscribe_header = None,
                    gmail_fbl = None,

                    pushed_to_rabbitmq = true, // pushing below via MQEmail.publish
                    campaign_email_settings_id = campaign_email_settings_id,
                    uuid = srUuidUtils.generateEmailsScheduledUuid()

                  )


                  email = email.copy(
                    list_unsubscribe_header = if (!bodies.has_unsubscribe_link ||
                      AppConfig.disableListUnsubscribeHeaderForOrgIds.contains(org_id)) None else Some(EmailHelper.getListUnsubscribeHeader(
                      prospectId = 0,
                      stepId = 0,
                      campaignId = campaignId.id,
                      emailScheduledId = 0,
                      replyToEmail = fromEmail
                    )) /*,
                        gmail_fbl = Some(EmailHelper.getGmailFBLId(
                          campaignId = campaignId,
                          accountId = ta.account_id,
                          prospectId = 0
                        ))*/
                  )


                  emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
                    emailsToBeScheduled = Vector(email),
                    campaign_email_setting_id = campaign_email_settings_id,
                    emailSendingFlow = emailSendingFlow,
                    Logger = Logger
                  ) match {

                    case Failure(e) =>

                      Future(Left(GetTestStepsError.ErrorWhileInsertingMultipleForCampaign(e)))

                    case Success(savedEmailScheduledSeq) =>

                      val rep_mail_server_reverse_dns = emailSettingOpt.get.rep_mail_server_reverse_dns.getOrElse("")


                      val data: JsValue = Json.obj(
                        "email_scheduled_id" -> savedEmailScheduledSeq.head.email_scheduled_id.toString,
                        "rep_mail_server_reverse_dns" -> rep_mail_server_reverse_dns,
                        "log_request_id" -> Logger.logRequestId,
                        "service_provider" -> emailSettingOpt.get.service_provider.toString,
                        "team_id" -> campaign.team_id
                      )
                      Logger.debug(s"CampaignController.testStep data for test_step - $data")

                      if (AppConfig.isProd) {

                        val hostProcessTestStepHost = emailSettingOpt.get.rep_mail_server_host + "/api/v1/campaigns/process_send_test"

                        wsClient.url(hostProcessTestStepHost)
                          .post(data)
                          .map(res => {

                            if (res.status == 200) {

                              Right({})

                            } else {

                              Logger.error(s"FATAL:: process_send_test testStep:: hostProcessTestStepHost: $hostProcessTestStepHost, ${res.body} :: savedEmailScheduledSeq: ${savedEmailScheduledSeq} :: hostProcessTestStepHost: ${hostProcessTestStepHost} :: emailSettingsId: ${emailSettingOpt.get.id.map(_.emailSettingId)} :: campaignId: ${email.campaign_id}")

                              Left(GetTestStepsError.EmailNotSent(None))

                            }

                          })
                          .recover { case e =>

                            Logger.error(s"FATAL:: process_send_test REQUEST FAILED: hostProcessTestStepHost: $hostProcessTestStepHost, testStep2:: savedEmailScheduledSeq: ${savedEmailScheduledSeq} :: hostProcessTestStepHost: ${hostProcessTestStepHost} :: emailSettingsId: ${emailSettingOpt.get.id.map(_.emailSettingId)} :: campaignId: ${email.campaign_id} :: ${LogHelpers.getStackTraceAsString(e)}")

                            Left(GetTestStepsError.EmailNotSent(None))


                          }

                      } else {

                        emailSenderService.sendScheduleEmail(
                            msg = MQEmailMessage(
                              emailScheduledId = savedEmailScheduledSeq.head.email_scheduled_id,
                              emailServiceProviderSendEmail = getEmailServiceProviderSendEmail(
                                emailSettingOpt.get.service_provider
                              ),
                              team_id = savedEmailScheduledSeq.head.team_id
                            ),
                            rep_smtp_reverse_dns_host = "",
                          sendEmailType = SendScheduleEmailType.TestEmail
                        )(ec, wsClient, Logger, system = system)
                          .map(res => {
                            Logger.error("FATAL sending test from api server only")
                            Right({})
                          })
                          .recover { case e =>
                            Left(GetTestStepsError.EmailNotSent(Some(e)))
                          }
                      }


                    /*
                    MQEmail.publish(message = MQEmailMessage(emailScheduledId = savedEmailScheduledSeq.head.email_scheduled_id), sendingIP = emailSettingOpt.get.rep_mail_server_public_ip) match {

                      case Failure(e) =>

                        Future.successful(ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + " :" + e.getMessage, e = Some(e)))

                      case Success(_) =>

                        Future.successful(Res.Success(s"Sending test email to: ${email.to_email}", Json.obj()))
                    }
                    */
                  }
              }
          }
        }
    }
  }

}
