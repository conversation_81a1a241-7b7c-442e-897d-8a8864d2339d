package api.campaigns.services

import api.accounts.TeamId
import api.campaigns.ChannelSettingUuid
import play.api.libs.json.{Json, Reads, Writes}
import sr_scheduler.models.ChannelType

case class ChannelSettingData(
  channel_type: ChannelType,
  channel_setting_uuid: ChannelSettingUuid,
  team_id: TeamId,
  campaign_id: CampaignId
)

object ChannelSettingData {

  import utils.sr_json_utils.ChannelTypeJsonUtil.jsonFormatChannelType

  implicit val reads: Reads[ChannelSettingData] = Json.reads[ChannelSettingData]

  implicit val writes: Writes[ChannelSettingData] = new Writes[ChannelSettingData] {
    def writes(c: ChannelSettingData) = Json.obj(
      "team_id" -> c.team_id,
      "campaign_id" -> c.campaign_id,
      "channel_type" -> c.channel_type,
      "channel_setting_uuid" -> c.channel_setting_uuid,
    )
  }

}