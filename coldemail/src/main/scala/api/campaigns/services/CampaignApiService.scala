package api.campaigns.services

import api.APIErrorResponse.{ErrorResponseProspectsAssignApi, ErrorResponseProspectsUnAssignApi}
import api.ApiVersion
import api.accounts.{Account, AccountService, AccountUuid, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.DataForCampaignAssignProspects.{AssignProspectToCampaignDataV3, CampaignProspectFormIds}
import api.campaigns.models.{CallAccountSettingsApiResponse, CampaignEmailSettingsResponse, ChannelSettingsPublicApi, ChannelSettingsPublicApiResponse, GeneralSettingApiResponse, IgnoreProspectsInOtherCampaigns, LinkedinAccountSettingsApiResponse, SmsSettingsApiResponse, WhatsappAccountSettingsApiResponse}
import api.campaigns.{CampaignBasicInfo, CampaignSettings, CampaignWithStatsAndEmail}
import api.campaigns.services.CampaignResource.{CampaignBasicInfoInternal, CampaignDataForApiV2, CampaignDataForApiV2WithoutStats, CampaignDataForApiV3, CampaignWithStatsAndEmailInternal}
import api.call.models.{CallAccountSettings, DoerAccountName}
import api.campaigns.DataForCampaignAssignProspects.{AssignProspectToCampaignDataV3, CampaignProspectFormIds, CampaignProspectFormUuids}
import api.campaigns.{Campaign, CampaignBasicInfo, CampaignSettings, CampaignWithStatsAndEmail}
import api.campaigns.services.CampaignResource.{CampaignBasicInfoInternal, CampaignDataForApiV2, CampaignDataForApiV2WithoutStats, CampaignDataForApiV3, CampaignWithStatsAndEmailInternal}
import api.emails.models.EmailSettingUuid
import api.emails.services.{EmailSettingResponseV3, EmailSettingService}
import api.general.GeneralSetting
import api.linkedin.models.LinkedinAccountSettings
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.ProspectId
import api.prospects.service.ProspectServiceV2
import api.prospects.{OwnedProspectsForAssigning, ProspectService, ProspectUuid}
import api.reports.AllCampaignStats
import api.sms.models.SmsSettings
import api.tags.models.CampaignTag
import api.team.TeamUuid
import api.team.service.TeamService
import api.whatsapp.models.WhatsappAccountSettings
import org.joda.time.DateTime
import play.api.libs.json.{JsError, JsResult, JsSuccess, JsValue, Json, Reads, Writes}
import sr_scheduler.CampaignStatus
import sr_scheduler.models.{CampaignAIGenerationContext, CampaignEmailPriority}
import play.api.libs.json.JodaReads.*
import play.api.libs.json.JodaWrites.*
import utils.{Helpers, SRLogger}

import scala.util.{Failure, Success, Try}




case class IgnoreProspectsValidAndAccountOwnedProspects(
                                                         ignoreProspectsInOtherCampaigns: IgnoreProspectsInOtherCampaigns,
                                                         valid_prospect_ids: List[Long],
                                                         accountOwnedProspects:List[OwnedProspectsForAssigning]
                                                       )
case class UnAssignProspectsValidationResponseV3(
                                                  valid_prospect_ids: List[ProspectId],
                                                  campaign_id: CampaignId
                                                )
sealed trait CampaignApiError

object CampaignApiError{
  case class ErrorWhileFetchingDataForTeamId(err: Throwable) extends CampaignApiError

}


case class CampaignSettingsDataForV3(
                                      timezone: String,
                                      daily_from_time: Int, // time since beginning of day in seconds
                                      daily_till_time: Int, // time since beginning of day in seconds

                                      // Sunday is the first day
                                      days_preference: List[Boolean],

                                      // delay between two consecutive in seconds
                                    //NOTE: fields were suppressed : 22/02/2024
//                                      consecutive_email_delay: Int,

//                                      max_emails_per_day: Int,
//                                      max_emails_per_day_from_email_account: Option[Int],
//                                      open_tracking_enabled: Boolean,
//                                      click_tracking_enabled: Boolean,
//                                      ab_testing_enabled: Boolean,

                                      // warm up
//                                      warmup_started_at: Option[DateTime],
//                                      warmup_length_in_days: Option[Int],
//                                      warmup_starting_email_count: Option[Int],

                                      // schedule start
//                                      schedule_start_at: Option[DateTime],
//                                      schedule_start_at_tz: Option[String],

//                                      email_priority: CampaignEmailPriority.Value,
//                                      opt_out_msg: String,
//                                      opt_out_is_text: Boolean,
//                                      add_prospect_to_dnc_on_opt_out: Boolean,
//                                      from_email: Option[String],
//                                      to_email: Option[String],
//                                      signature: Option[String]

                                    )

case object CampaignSettingsDataForV3 {
  implicit val writes: Writes[CampaignSettingsDataForV3] = new Writes[CampaignSettingsDataForV3] {
    def writes(data: CampaignSettingsDataForV3) = {
      Json.obj(
        "timezone" -> data.timezone,
        "daily_from_time" -> data.daily_from_time, // time since beginning of day in seconds
        "daily_till_time" -> data.daily_till_time, // time since beginning of day in seconds

        // Sunday is the first day
        "days_preference" -> data.days_preference,

        // delay between two consecutive in seconds
//        "consecutive_email_delay" -> data.consecutive_email_delay,
//
//        "max_emails_per_day" -> data.max_emails_per_day,
//        "max_emails_per_day_from_email_account" -> data.max_emails_per_day_from_email_account,
//        "open_tracking_enabled" -> data.open_tracking_enabled,
//        "click_tracking_enabled" -> data.click_tracking_enabled,
//        "ab_testing_enabled" -> data.ab_testing_enabled,
//
//        // warm up
//        "warmup_started_at" -> data.warmup_started_at,
//        "warmup_length_in_days" -> data.warmup_length_in_days,
//        "warmup_starting_email_count" -> data.warmup_starting_email_count,
//
//        // schedule start
//        "schedule_start_at" -> data.schedule_start_at,
//        "schedule_start_at_tz" -> data.schedule_start_at_tz,
//
//
//        "email_priority" -> data.email_priority.toString,
//        "opt_out_msg" -> data.opt_out_msg,
//        "opt_out_is_text" -> data.opt_out_is_text,
//        "add_prospect_to_dnc_on_opt_out" -> data.add_prospect_to_dnc_on_opt_out,
//        "from_email" -> data.from_email,
//        "to_email" -> data.to_email,
//        "signature" -> data.signature
      )
    }
  }

  implicit val reads: Reads[CampaignSettingsDataForV3] = new Reads[CampaignSettingsDataForV3]{
    override def reads(json: JsValue): JsResult[CampaignSettingsDataForV3] = Try {

//      val email_priority = (json \ "email_priority").as[String]

      CampaignSettingsDataForV3(
        timezone = (json \ "timezone").as[String],
        daily_from_time = (json \ "daily_from_time").as[Int],
        daily_till_time = (json \ "daily_till_time").as[Int],
        days_preference = (json \ "days_preference").as[List[Boolean]],
//        consecutive_email_delay = (json \ "consecutive_email_delay").as[Int],
//        max_emails_per_day = (json \ "max_emails_per_day").as[Int],
//        max_emails_per_day_from_email_account = (json \ "max_emails_per_day_from_email_account").asOpt[Int],
//        open_tracking_enabled = (json \ "open_tracking_enabled").as[Boolean],
//        click_tracking_enabled = (json \ "click_tracking_enabled").as[Boolean],
//        ab_testing_enabled = (json \ "ab_testing_enabled").as[Boolean],
//        warmup_started_at = (json \ "warmup_started_at").asOpt[DateTime],
//        warmup_length_in_days = (json \ "warmup_length_in_days").asOpt[Int],
//        warmup_starting_email_count = (json \ "warmup_starting_email_count").asOpt[Int],
//        schedule_start_at = (json \ "schedule_start_at").asOpt[DateTime],
//        schedule_start_at_tz = (json \ "schedule_start_at_tz").asOpt[String],
//        email_priority = CampaignEmailPriority.withName(email_priority),
//        opt_out_msg = (json \ "opt_out_msg").as[String],
//        opt_out_is_text = (json \ "opt_out_is_text").as[Boolean],
//        add_prospect_to_dnc_on_opt_out = (json \ "add_prospect_to_dnc_on_opt_out").as[Boolean],
//        from_email = (json \ "from_email").asOpt[String],
//        to_email = (json \ "to_email").asOpt[String],
//        signature = (json \ "signature").asOpt[String],
      )
    } match {
      case Failure(e) => JsError(e.getMessage)
      case Success(campaignSettingsDataForV3) => JsSuccess(campaignSettingsDataForV3)
    }
  }

}
sealed trait CampaignResource
object CampaignResource{

  case class CampaignDataForApiV3(
                                   uuid: Option[String],
                                   name: String,
                                   created_at: DateTime,
                                   status: CampaignStatus,
                                   tags: Seq[CampaignTag],
                                   owner_name: String,
                                   owner_email: String,
                                   team_uuid: String,
                                   settings: CampaignSettingsDataForV3
                                 ) extends CampaignResource

  object CampaignDataForApiV3 {

    implicit val writes: Writes[CampaignDataForApiV3] = new Writes[CampaignDataForApiV3] {

      override def writes(c: CampaignDataForApiV3) = {
        Json.obj(
          "object" -> "campaign",
          "id" -> c.uuid,
          "name" -> c.name,
          "created_at" -> c.created_at,
          "status" -> c.status.toString,
          "team_id" -> c.team_uuid,
          "settings" -> c.settings

        )
      }
    }

  }

  /*
      ref to docs : https://smartreach.io/api_docs#campaigns for v1 and v2

      response :
      {
    "status": "success",
    "message": "'Naruto' campaign found",
    "data": {
      "campaign": {
        "id": 7,
        "name": "Naruto",
        "owner_id": 2,
        "status": "not_started",
        "created_at": "2022-12-29T09:37:50.294+05:30",
        "stats": {
          "total_sent": 0,
          "total_opened": 0,
          "total_clicked": 0,
          "total_replied": 0
        }
      }
    }
  }
   */
  case class CampaignDataForApiV2(
                                 id: Long,
                                 name: String,
                                 owner_id: Long,
                                 status: CampaignStatus,
                                 created_at: DateTime,
                                 stats: AllCampaignStats
                               ) extends CampaignResource

  object CampaignDataForApiV2 {
    implicit val writes: Writes[CampaignDataForApiV2] = new Writes[CampaignDataForApiV2] {
      override def writes(c: CampaignDataForApiV2) = {
        Json.obj(
          "id" -> c.id,
          "name" -> c.name,
          "owner_id" -> c.owner_id,
          "status" -> c.status.toString,
          "created_at" -> c.created_at,
          "stats" -> AllCampaignStats.apiStructure(s = c.stats)
        )

      }
    }
  }

  case class CampaignDataForApiV2WithoutStats(
                                   id: Long,
                                   name: String,
                                   owner_id: Long,
                                   status: CampaignStatus,
                                   created_at: DateTime,
                                 ) extends CampaignResource

  object CampaignDataForApiV2WithoutStats {
    implicit val writes: Writes[CampaignDataForApiV2WithoutStats] = new Writes[CampaignDataForApiV2WithoutStats] {
      override def writes(c: CampaignDataForApiV2WithoutStats) = {
        Json.obj(
          "id" -> c.id,
          "name" -> c.name,
          "owner_id" -> c.owner_id,
          "status" -> c.status.toString,
          "created_at" -> c.created_at,
        )

      }
    }
  }

  //need to check if this needs to be handled for v2 APIs
  case class CampaignBasicInfoInternal(
                                id: Long,
                                uuid: Option[String],
                                team_id: Long,
                                name: String,
                                owner_name: String,
                                owner_email: String,
                                owner_id: Long,
                                status: CampaignStatus,
                                tags: Seq[CampaignTag],
                                head_step_id: Option[Long],
                                created_at: DateTime,
                                shared_with_team: Boolean,
                                warmup_is_on: Boolean,
                                error: Option[String],
                                spam_test_exists: Boolean,
                                settings: CampaignSettings,
                              ) extends CampaignResource

  object CampaignBasicInfoInternal {

    implicit val writes: Writes[CampaignBasicInfoInternal] = new Writes[CampaignBasicInfoInternal] {
      def writes(c: CampaignBasicInfoInternal) = Json.obj(
        "id" -> c.id,
        "uuid" -> c.uuid,
        "shared_with_team" -> c.shared_with_team,
        "warmup_is_on" -> c.warmup_is_on,
        "team_id" -> c.team_id,
        "name" -> c.name,
        "owner_name" -> c.owner_name,
        "owner_email" -> c.owner_email,
        "owner_id" -> c.owner_id,

        "status" -> c.status.toString,

        "tags" -> c.tags,

        "head_step_id" -> c.head_step_id,
        "created_at" -> c.created_at,
        "error" -> c.error,
        "spam_test_exists" -> c.spam_test_exists,
        "settings" -> c.settings

      )
    }

  }

  case class CampaignWithStatsAndEmailInternal(
                                        id: Long,
                                        uuid: Option[String],
                                        team_id: Long,
                                        shared_with_team: Boolean,
                                        name: String,
                                        owner_name: String,
                                        owner_email: String,
                                        owner_id: Long,
                                        status: CampaignStatus,
                                        tags: Seq[CampaignTag],
                                        spam_test_exists: Boolean,
                                        warmup_is_on: Boolean,

                                        stats: AllCampaignStats,

                                        head_step_id: Option[Long],
                                        ai_generation_context: Option[CampaignAIGenerationContext],

                                        settings: CampaignSettings,

                                        created_at: DateTime,

                                        error: Option[String]

                                      ) extends CampaignResource

  object CampaignWithStatsAndEmailInternal {

    implicit val writes: Writes[CampaignWithStatsAndEmailInternal] = new Writes[CampaignWithStatsAndEmailInternal] {
      def writes(c: CampaignWithStatsAndEmailInternal) =
        Json.obj(
          "id" -> c.id,
          "uuid" -> c.uuid,
          "team_id" -> c.team_id,
          "name" -> c.name,
          "owner_name" -> c.owner_name,
          "owner_email" -> c.owner_email,
          "owner_id" -> c.owner_id,

          "status" -> c.status.toString,

          "tags" -> c.tags,

          "spam_test_exists" -> c.spam_test_exists,
          "warmup_is_on" -> c.warmup_is_on,

          "head_step_id" -> c.head_step_id,
          "ai_generation_context" -> c.ai_generation_context,
          "created_at" -> c.created_at,
          "error" -> c.error,
          "stats" -> c.stats,
          "settings" -> c.settings
        )
    }

  }


  implicit def writes: Writes[CampaignResource] = new Writes[CampaignResource] {
    override def writes(c: CampaignResource) = {
      c match {
        case data: CampaignDataForApiV3 => Json.toJson(data)
        case data: CampaignDataForApiV2 => Json.obj("campaign" -> data)
        case data: CampaignWithStatsAndEmailInternal => Json.obj("campaign" -> data)
        case data: CampaignDataForApiV2WithoutStats => Json.obj("campaign" -> data)
        case data: CampaignBasicInfoInternal => Json.obj("campaign" -> data)
      }
    }
  }


}





class CampaignApiService(
                          teamService: TeamService,
                          prospectService: ProspectService,
                          campaignProspectAssign: CampaignProspectAssign,
                          prospectServiceV2: ProspectServiceV2,
                          accountService: AccountService,
                          prospectDAOService: ProspectDAOService,
                          campaignProspectService: CampaignProspectService,
                          emailSettingService: EmailSettingService
                        ) {

  def getCampaignApiResponse(
    v: String,
    campaign: CampaignWithStatsAndEmail,
    orgId: OrgId,
    teamId: TeamId
  )(using logger: SRLogger): Either[CampaignApiError, CampaignResource] = {
    if (v == ApiVersion.V3.toString) {


      getApiStructureForV3(
        data = campaign,
        orgId = orgId,
        teamId = teamId
      ) match {

        case Success(data: CampaignDataForApiV3) => Right(data)

        case Failure(exception) =>
          logger.error("Error while fetching uuid for team id", err = exception)
          Left(CampaignApiError.ErrorWhileFetchingDataForTeamId(err = exception))
      }
    }


    else {

      Right(getApiStructure(c = campaign, version = v))
    }

  }

  def getApiStructureForV3(
                            data: CampaignWithStatsAndEmail,
                            orgId: OrgId,
                            teamId: TeamId
                          )(using logger: SRLogger): Try[CampaignDataForApiV3] = {
    teamService.getTeamUuidFromTeamId(
      team_id = teamId,
      orgId = orgId,
    )
      .map(team_uuid => {
        val settings = data.settings
        CampaignDataForApiV3(
          uuid = data.uuid,
          name = data.name,
          created_at = data.created_at,
          status = data.status,
          tags = data.tags,
          owner_name = data.owner_name,
          owner_email = data.owner_email,
          team_uuid = team_uuid.uuid,
          settings = CampaignSettingsDataForV3(
            timezone = settings.timezone,
            daily_from_time = settings.daily_from_time, // time since beginning of day in seconds
            daily_till_time = settings.daily_till_time, // time since beginning of day in seconds

            // Sunday is the first day
            days_preference = settings.days_preference,

            // delay between two consecutive in seconds

//            consecutive_email_delay = settings.consecutive_email_delay,
//
//            max_emails_per_day = settings.max_emails_per_day,
//            max_emails_per_day_from_email_account = settings.campaign_email_settings.headOption.map(_.max_emails_per_day_from_email_account), //FIXME SENDER_ROTATION
//            open_tracking_enabled = settings.open_tracking_enabled,
//            click_tracking_enabled = settings.click_tracking_enabled,
//            ab_testing_enabled = settings.ab_testing_enabled,
//
//            // warm up
//            warmup_started_at = settings.warmup_started_at,
//            warmup_length_in_days = settings.warmup_length_in_days,
//            warmup_starting_email_count = settings.warmup_starting_email_count,
//
//            // schedule start
//            schedule_start_at = settings.schedule_start_at,
//            schedule_start_at_tz = settings.schedule_start_at_tz,
//
//            email_priority = settings.email_priority,
//            opt_out_msg = settings.opt_out_msg,
//            opt_out_is_text = settings.opt_out_is_text,
//            add_prospect_to_dnc_on_opt_out = settings.add_prospect_to_dnc_on_opt_out,
//            from_email = settings.campaign_email_settings.headOption.map(_.sender_email), //FIXME SENDER_ROTATION
//            to_email = settings.campaign_email_settings.headOption.map(_.receiver_email),
//            signature = settings.campaign_email_settings.headOption.flatMap(_.signature)
          )
        )
      })

  }

  def getApiStructure(c: CampaignWithStatsAndEmail, version: String): CampaignDataForApiV2 = {

    CampaignDataForApiV2(
      id = c.id,
      name = c.name,
      owner_id = c.owner_id,
      status = c.status,
      created_at = c.created_at,
      stats = c.stats
    )
  }

  def getApiStructureWithoutStats(c: CampaignBasicInfo, version: ApiVersion): CampaignDataForApiV2WithoutStats = {

    CampaignDataForApiV2WithoutStats(
      id = c.id,
      name = c.name,
      owner_id = c.owner_id,
      status = c.status,
      created_at = c.created_at,
    )
  }

  def getCampaignWithStatsAndEmailInternal(c: CampaignWithStatsAndEmail): CampaignWithStatsAndEmailInternal = {

    CampaignWithStatsAndEmailInternal(
      id = c.id,
      uuid = c.uuid,
      team_id = c.team_id,
      shared_with_team = c.shared_with_team,
      name = c.name,
      owner_name = c.owner_name,
      owner_email = c.owner_email,
      owner_id = c.owner_id,
      status = c.status,
      tags = c.tags,
      spam_test_exists = c.spam_test_exists,
      warmup_is_on = c.warmup_is_on,
      stats = c.stats,
      head_step_id = c.head_step_id,
      ai_generation_context = c.ai_generation_context,
      settings = c.settings,
      created_at = c.created_at,
      error = c.error
    )
  }

  def getCampaignBasicInfoInternal(campaigns: Seq[CampaignBasicInfo]): Seq[CampaignBasicInfoInternal] = {
    campaigns.map(c => {
      CampaignBasicInfoInternal(
        id = c.id,
        uuid = c.uuid,
        team_id = c.team_id,
        shared_with_team = c.shared_with_team,
        name = c.name,
        owner_name = c.owner_name,
        owner_email = c.owner_email,
        owner_id = c.owner_id,
        status = c.status,
        tags = c.tags,
        spam_test_exists = c.spam_test_exists,
        warmup_is_on = c.warmup_is_on,
        head_step_id = c.head_step_id,
        settings = c.settings,
        created_at = c.created_at,
        error = c.error
      )
    })
  }

  def getListingApiStructureForV3(
                            data: Seq[CampaignBasicInfo],
                            orgId: OrgId,
                            teamId: TeamId
                          )(using Logger: SRLogger): Try[Seq[CampaignDataForApiV3]] = {
    teamService.getTeamUuidFromTeamId(
      team_id = teamId,
      orgId = orgId,
    )
      .map(team_uuid => {
        data.map(campaign_info => {
          val settings = campaign_info.settings
          CampaignDataForApiV3(
            uuid = campaign_info.uuid,
            name = campaign_info.name,
            created_at = campaign_info.created_at,
            status = campaign_info.status,
            tags = campaign_info.tags,
            owner_name = campaign_info.owner_name,
            owner_email = campaign_info.owner_email,
            team_uuid = team_uuid.uuid,
            settings = CampaignSettingsDataForV3(
              timezone = settings.timezone,
              daily_from_time = settings.daily_from_time, // time since beginning of day in seconds
              daily_till_time = settings.daily_till_time, // time since beginning of day in seconds

              // Sunday is the first day
              days_preference = settings.days_preference,

              // delay between two consecutive in seconds

//              consecutive_email_delay = settings.consecutive_email_delay,

//              max_emails_per_day = settings.max_emails_per_day,
//              max_emails_per_day_from_email_account = settings.campaign_email_settings.headOption.map(_.max_emails_per_day_from_email_account), //FIXME SENDER_ROTATION
//              open_tracking_enabled = settings.open_tracking_enabled,
//              click_tracking_enabled = settings.click_tracking_enabled,
//              ab_testing_enabled = settings.ab_testing_enabled,
//
//              // warm up
//              warmup_started_at = settings.warmup_started_at,
//              warmup_length_in_days = settings.warmup_length_in_days,
//              warmup_starting_email_count = settings.warmup_starting_email_count,
//
//              // schedule start
//              schedule_start_at = settings.schedule_start_at,
//              schedule_start_at_tz = settings.schedule_start_at_tz,
//
//              email_priority = settings.email_priority,
//              opt_out_msg = settings.opt_out_msg,
//              opt_out_is_text = settings.opt_out_is_text,
//              add_prospect_to_dnc_on_opt_out = settings.add_prospect_to_dnc_on_opt_out,
//              from_email = settings.campaign_email_settings.headOption.map(_.sender_email), //FIXME SENDER_ROTATION
//              to_email = settings.campaign_email_settings.headOption.map(_.receiver_email),
//              signature = settings.campaign_email_settings.headOption.flatMap(_.signature)
            )
          )
        })

      })

  }

  def getListingApiStructure(
                              campaigns: Seq[CampaignBasicInfo],
                              version: ApiVersion
                            ): Seq[CampaignDataForApiV2WithoutStats] = {
    campaigns.map(campaign => {
      getApiStructureWithoutStats(
        c = campaign,
        version = version
      )

    })
  }

  def getCampaignListingApiResponse(
    v: ApiVersion,
    campaign: Seq[CampaignBasicInfo],
    orgId: OrgId,
    teamId: TeamId
  )(using logger: SRLogger): Either[CampaignApiError, Seq[CampaignResource]] = {
    if (v == ApiVersion.V3) {


      getListingApiStructureForV3(
        data = campaign,
        orgId = orgId,
        teamId = teamId
      ) match {

        case Success(data) => Right(data)

        case Failure(exception) =>
          logger.error("Error while fetching uuid for team id", err = exception)
          Left(CampaignApiError.ErrorWhileFetchingDataForTeamId(err = exception))
      }
    }


    else {

      Right(getListingApiStructure(campaigns = campaign, version = v))
    }

  }

  def getValidProspectIdsForAssignProspect(
                                            data: AssignProspectToCampaignDataV3,
                                            teamId: TeamId,
                                            doer: Account,
                                            account_id: AccountId,
                                            permittedAccountIds: Seq[Long],
                                            campaignId: CampaignId,
                                            campaignName: String,

                                          )(using Logger: SRLogger): Either[AssignProspectsErrorV3, IgnoreProspectsValidAndAccountOwnedProspects] = {
    var error_list: List[ErrorResponseProspectsAssignApi] = List()

    error_list = error_list ++ CampaignService.validateProspectAssignData(
      isApiCall = true,
      data = data
    )
    if (error_list.isEmpty) {

      CampaignService.getIgnoreProspectsInOtherCampaigns(
        ignore_prospects_in_other_campaigns = data.ignore_prospects_in_other_campaigns,
        force_assign = None
      ) match {
        case Failure(err) =>
          Left(AssignProspectsErrorV3.GetIgnoreProspectsInOtherCampaignsError(err = err))
        //        Future.successful(Res.BadRequestError("Failed to Assign Prospect, Please contact Support"))
        case Success(ignoreProspectsInOtherCampaigns) =>

          prospectService.getProspectIdsFromUuid(
            data = data.prospect_ids,
            teamId = teamId
          ) match {

            case Left(error) =>
              Left(AssignProspectsErrorV3.GetProspectIdFromUuidErrors(err = error))

            case Right(validIds_and_invalidUuids) =>

              val invalid_uuids = validIds_and_invalidUuids.invalid_uuids
              val prospect_ids = validIds_and_invalidUuids.valid_prospect_ids.map(_.id)

              if (invalid_uuids.nonEmpty) {
                val err = ErrorResponseProspectsAssignApi(
                  message = "Invalid prospect ids passed",
                  data = Some(invalid_uuids.mkString(",")),
                  error_type = ProspectAssignErrorType.BadRequest
                )

                error_list = error_list.appended(err)
              }


              //            Future.successful(Res.ServerError("There was an error. Please contact support", e = Some(exception)))
              //            val prospectIds = prospectIds.map(i => i._2.id).toList


              prospectServiceV2.filterOwnedProspects(
                prospectIds = prospect_ids,
                permittedAccountIds = permittedAccountIds,
                teamId = teamId.id,
                orgId = OrgId(doer.org.id),
                SRLogger = Logger
              ) match {

                case Failure(exception) => Left(AssignProspectsErrorV3.FilterOwnedProspectError(err = exception))

                case Success(accountOwnedProspects: List[OwnedProspectsForAssigning]) =>
                  if (prospect_ids.length != accountOwnedProspects.length) {

                    val msg = "Given prospects do not exist in your account"

                    // REF: https://stackoverflow.com/a/********
                    val accountOwnedProspectsSet = accountOwnedProspects.map(_.prospect_id).toSet
                    val prospectIdsThatAreNotOwned: List[ProspectId] = prospect_ids.filterNot(accountOwnedProspectsSet).map(ProspectId(_))

                    prospectService.getProspectUuidFromId(
                      data = prospectIdsThatAreNotOwned,
                      teamId = teamId
                    ) match {

                      case Failure(exception) =>
                        Logger.fatal(s"Campaign.assign Error while getting prospect uuid from id", err = exception)
                        Left(AssignProspectsErrorV3.GetProspectUuidFromId(err = exception))

                      case Success(notOwnedProspectUuids) =>
                        Logger.fatal(s"Campaign.assign doNotExistError: $msg :: prospectIds.length: ${prospect_ids.length} :: ${accountOwnedProspects.length} :: prospectIdsThatAreNotOwnedCount: ${prospectIdsThatAreNotOwned.length} :: prospectIdsCount : ${prospect_ids.length} :: accountOwnedProspectIdsCount: ${accountOwnedProspects.map(_.prospect_id).length}")

                        val err = ErrorResponseProspectsAssignApi(
                          message = "Given prospects do not exist in your account",
                          data = Some(notOwnedProspectUuids.mkString(",")),
                          error_type = ProspectAssignErrorType.Forbidden
                        )

                        error_list = error_list.appended(err)
                        Left(AssignProspectsErrorV3.AssignProspectsValidationErrors(err = error_list))

                    }


                  } else {
                    if (error_list.isEmpty) {
                      Right(IgnoreProspectsValidAndAccountOwnedProspects(
                        ignoreProspectsInOtherCampaigns = ignoreProspectsInOtherCampaigns,
                        valid_prospect_ids = prospect_ids,
                        accountOwnedProspects = accountOwnedProspects
                      ))
                    }
                    else {
                      Left(AssignProspectsErrorV3.AssignProspectsValidationErrors(err = error_list))
                    }

                  }
              }
          }
      }
    } else {
      Left(AssignProspectsErrorV3.AssignProspectsValidationErrors(err = error_list))
    }

  }

  def assignProspectToCampaignV3(
                                  data: AssignProspectToCampaignDataV3,
                                  teamId: TeamId,
                                  doer: Account,
                                  account_id: AccountId,
                                  permittedAccountIds: Seq[Long],
                                  campaignId: CampaignId,
                                  campaignName: String,
                                  campaignSettings: CampaignSettings
                                )(using Logger: SRLogger): Either[AssignProspectsErrorV3, AssignProspectsResponseV3] = {


    getValidProspectIdsForAssignProspect(
      data = data,
      teamId = teamId,
      doer = doer,
      account_id = account_id,
      permittedAccountIds = permittedAccountIds,
      campaignId = campaignId,
      campaignName = campaignName
    ) match {

      case Left(error) => Left(error)

      case Right(details: IgnoreProspectsValidAndAccountOwnedProspects) =>
        campaignProspectAssign.assignProspectsV3(
          doer = doer,
          accountId = account_id.id,
          teamId = teamId.id,
          ignoreProspectsInOtherCampaigns = details.ignoreProspectsInOtherCampaigns,
          permittedAccountIds = permittedAccountIds,
          campaignId = campaignId.id.toInt,
          campaignName = campaignName,
          campaignSettings = campaignSettings,
          prospectIds = details.valid_prospect_ids,
          accountOwnedProspects = details.accountOwnedProspects,
          Logger = Logger
        ) match {

          case Left(err) => Left(AssignProspectsErrorV3.AssignProspectError(err = err))

          case Right(result) => Right(result)
        }
    }



  }


  def assignProspectToCampaign(
                                data: CampaignProspectFormIds,
                                teamId: TeamId,
                                doer: Account,
                                accountId: AccountId,
                                permittedAccountIds: Seq[Long],
                                campaignId: CampaignId,
                                campaignName: String,
                                campaignSettings: CampaignSettings,
                                Logger: SRLogger
                              ): Either[AssignProspectsError, AssignProspectsResponse] = {

    CampaignService.getIgnoreProspectsInOtherCampaigns(
      ignore_prospects_in_other_campaigns = data.ignore_prospects_in_other_campaigns,
      force_assign = data.force_assign
    ) match {
      case Failure(err) =>
        Logger.error(s"Error while getting the ignoreProspectsInOtherCampaigns, data sent from frontend - ${data.ignore_prospects_in_other_campaigns}", err = err)
        Left(AssignProspectsError.InvalidValueForIgnoreProspectsInOtherCampaigns("Failed to Assign Prospect, Please contact Support"))
      case Success(ignoreProspectsInOtherCampaigns) =>

        campaignProspectAssign.assignProspects(
          doer = doer,
          accountId = accountId.id,
          teamId = teamId.id,
          ignoreProspectsInOtherCampaigns = ignoreProspectsInOtherCampaigns,
          permittedAccountIds = permittedAccountIds,
          campaignId = campaignId.id.toInt,
          campaignName = campaignName,
          prospectIds = data.prospect_ids,
          campaignSettings = campaignSettings,
          Logger = Logger
        )

    }
  }

  def _generalSettingResp(
                           generalSettings: List[GeneralSetting],
                           orgId: OrgId,
                           teamUuid: TeamUuid
                         )(using Logger: SRLogger): Try[List[GeneralSettingApiResponse]] = {

    val gs = generalSettings.map {
      generalSetting =>
        val owner_id: AccountId = generalSetting.owner_account_id
        accountService.getAccountUuidFromId(accountId = owner_id, orgId = orgId) match {
          case Failure(err) => Failure(err)
          case Success(ownerUuid) => Success(
            GeneralSettingApiResponse(
              generalSettingUuid = generalSetting.uuid,
              teamUuid = teamUuid,
              owner_account_uuid = ownerUuid,
              quota_per_day = generalSetting.quota_per_day
            ))
        }
    }

    Helpers.listTryToTryList(gs)

  }

  def _callSettingResp(
                        callSettings: List[CallAccountSettings],
                        orgId: OrgId,
                        teamUuid: TeamUuid
                      )(using Logger: SRLogger): Try[List[CallAccountSettingsApiResponse]] = {

    val cs: List[Try[CallAccountSettingsApiResponse]] = callSettings.map {
      callSetting =>

        val owner_id: AccountId = callSetting.owner_account_id
        accountService.getAccountUuidFromId(accountId = owner_id, orgId = orgId) match {

          case Failure(err) => Failure(err)

          case Success(ownerUuid) => Success(
            CallAccountSettingsApiResponse(
              callSettingUuid = callSetting.uuid,
              teamUuid = teamUuid,
              phone_number = callSetting.phone_number,
              owner_account_uuid = ownerUuid,
              first_name = callSetting.first_name,
              last_name = callSetting.last_name,
              call_limit_per_day = callSetting.call_limit_per_day
            )
          )

        }
    }

    Helpers.listTryToTryList(cs)

  }

  def _linkedinSettingResp(
                            linkedinSettings: List[LinkedinAccountSettings],
                            orgId: OrgId,
                            teamUuid: TeamUuid
                          )(using Logger: SRLogger): Try[List[LinkedinAccountSettingsApiResponse]] = {

    val ls = linkedinSettings.map {
      linkedinSetting =>

        val owner_id: AccountId = AccountId(linkedinSetting.owner_id)
        accountService.getAccountUuidFromId(accountId = owner_id, orgId = orgId) match {

          case Failure(err) => Failure(err)

          case Success(ownerUuid) => Success(
            LinkedinAccountSettingsApiResponse(
              linkedinSettingUuid = linkedinSetting.uuid,
              teamUuid = teamUuid,
              email = linkedinSetting.email,
              owner_account_uuid = ownerUuid,
              first_name = linkedinSetting.first_name,
              last_name = linkedinSetting.last_name,
              view_profile_limit_per_day = linkedinSetting.view_profile_limit_per_day,
              inmail_limit_per_day = linkedinSetting.inmail_limit_per_day,
              message_limit_per_day = linkedinSetting.message_limit_per_day,
              connection_request_limit_per_day = linkedinSetting.connection_request_limit_per_day,
              profile_url = Some(linkedinSetting.profile_url)
            )
          )

        }
    }

    Helpers.listTryToTryList(ls)

  }

  def _smsSettingResp(
                       smsSettings: List[SmsSettings],
                       orgId: OrgId,
                       teamUuid: TeamUuid
                     )(using Logger: SRLogger): Try[List[SmsSettingsApiResponse]] = {

    val ss: List[Try[SmsSettingsApiResponse]] = smsSettings.map {

      smsSetting =>
        val owner_id: AccountId = AccountId(smsSetting.owner_id)
        accountService.getAccountUuidFromId(accountId = owner_id, orgId = orgId) match {

          case Failure(err) => Failure(err)

          case Success(ownerUuid) => Success(
            SmsSettingsApiResponse(
              smsSettingUuid = smsSetting.uuid,
              teamUuid = teamUuid,
              owner_account_uuid = ownerUuid,
              phone_number = smsSetting.phone_number,
              first_name = smsSetting.first_name,
              last_name = smsSetting.last_name,
              sms_limit_per_day = smsSetting.sms_limit_per_day
            )
          )
        }
    }

    Helpers.listTryToTryList(ss)

  }

  def _whatsappSettingResp(
                            whatsappSettings: List[WhatsappAccountSettings],
                            orgId: OrgId,
                            teamUuid: TeamUuid
                          )(using Logger: SRLogger): Try[List[WhatsappAccountSettingsApiResponse]] = {

    val ws = whatsappSettings.map {
      whatsappSetting =>
        val owner_id: AccountId = AccountId(whatsappSetting.owner_id)
        accountService.getAccountUuidFromId(accountId = owner_id, orgId = orgId) match {

          case Failure(err) => Failure(err)

          case Success(ownerUuid) => Success(
            WhatsappAccountSettingsApiResponse(
              whatsappSettingUuid = whatsappSetting.uuid,
              teamUuid = teamUuid,
              owner_account_uuid = ownerUuid,
              first_name = whatsappSetting.first_name,
              last_name = whatsappSetting.last_name,
              whatsapp_number = whatsappSetting.whatsapp_number,
              whatsapp_message_limit_per_day = whatsappSetting.whatsapp_message_limit_per_day
            )
          )
        }
    }

    Helpers.listTryToTryList(ws)

  }

  def _campaignEmailSettingsResp(
                                  campaignEmailSettings: List[CampaignEmailSettingsPublicApi],
                                  teamUuid: TeamUuid
                                )(using Logger: SRLogger): Success[List[CampaignEmailSettingsResponse]] = {

    Success(campaignEmailSettings.map {
      campaignEmailSettings =>
        val sender_email_setting = campaignEmailSettings.sender_email_setting
        val sender_email_setting_resp = EmailSettingResponseV3(
          id = sender_email_setting.uuid.get,
          owner_id = sender_email_setting.owner_uuid,
          service_provider = sender_email_setting.service_provider,
          first_name = sender_email_setting.first_name,
          last_name = sender_email_setting.last_name,
          cc_emails = sender_email_setting.cc_emails,
          bcc_emails = sender_email_setting.bcc_emails,
          created_at = sender_email_setting.created_at,
          min_delay_seconds = sender_email_setting.min_delay_seconds,
          max_delay_seconds = sender_email_setting.max_delay_seconds,
          email = sender_email_setting.email
        )

        val receiver_email_setting = campaignEmailSettings.receiver_email_setting
        val receiver_email_setting_resp = EmailSettingResponseV3(
          id = receiver_email_setting.uuid.get,
          owner_id = receiver_email_setting.owner_uuid,
          service_provider = receiver_email_setting.service_provider,
          first_name = receiver_email_setting.first_name,
          last_name = receiver_email_setting.last_name,
          cc_emails = receiver_email_setting.cc_emails,
          bcc_emails = receiver_email_setting.bcc_emails,
          created_at = receiver_email_setting.created_at,
          min_delay_seconds = receiver_email_setting.min_delay_seconds,
          max_delay_seconds = receiver_email_setting.max_delay_seconds,
          email = receiver_email_setting.email
        )

        CampaignEmailSettingsResponse(
          team_uuid = teamUuid,
          sender_email_setting = sender_email_setting_resp,
          receiver_email_setting = receiver_email_setting_resp
        )
    })

  }

  def getChannelSettingsApiResponse(
                                     channelSettings: ChannelSettings,
                                     teamId: TeamId,
                                     orgId: OrgId
                                   )(using Logger: SRLogger): Try[ChannelSettingsPublicApiResponse]  = {

    teamService.getTeamUuidFromTeamId(team_id = teamId, orgId = orgId) match {

      case Failure(err) => Failure(err)

      case Success(teamUuid: TeamUuid) =>

        for {

          generalSettingResp: List[GeneralSettingApiResponse] <- {

            _generalSettingResp(
              generalSettings = channelSettings.generalSettings,
              orgId = orgId,
              teamUuid = teamUuid
            )

          }

          callSettingResp: List[CallAccountSettingsApiResponse] <- {

            _callSettingResp(
              callSettings = channelSettings.callSettings,
              orgId = orgId,
              teamUuid = teamUuid
            )

          }

          linkedinSettingResp: List[LinkedinAccountSettingsApiResponse] <- {

            _linkedinSettingResp(
              linkedinSettings = channelSettings.linkedinSettings,
              orgId = orgId,
              teamUuid = teamUuid
            )

          }

          smsSettingResp: List[SmsSettingsApiResponse] <- {

            _smsSettingResp(
              smsSettings = channelSettings.smsSettings,
              orgId = orgId,
              teamUuid = teamUuid
            )

          }

          whatsappSettingResp: List[WhatsappAccountSettingsApiResponse] <- {

            _whatsappSettingResp(
              whatsappSettings = channelSettings.whatsappSettings,
              orgId = orgId,
              teamUuid = teamUuid
            )

          }

          campaignEmailSettingsResp: List[CampaignEmailSettingsResponse] <- {

            _campaignEmailSettingsResp(
              campaignEmailSettings = channelSettings.campaignEmailSettings,
              teamUuid = teamUuid
            )

          }


        } yield {

          val channelSettingsPublicApi = ChannelSettingsPublicApi(
            generalSettings = generalSettingResp,
            campaignEmailSettings = campaignEmailSettingsResp,
            callSettings = callSettingResp,
            linkedinSettings = linkedinSettingResp,
            smsSettings = smsSettingResp,
            whatsappSettings = whatsappSettingResp
          )

          ChannelSettingsPublicApiResponse(channel_settings = channelSettingsPublicApi)

        }

    }
  }

  def getValidProspectIdsForUnAssignProspect(
                                              data: CampaignProspectFormUuids,
                                              campaign: Campaign,
                                              teamId: TeamId,
                                              orgId: OrgId,
                                              permittedAccountIds: Seq[AccountId],
                                            )(using Logger: SRLogger): Either[UnAssignProspectsErrorV3, UnAssignProspectsValidationResponseV3] = {
    var error_list: List[ErrorResponseProspectsUnAssignApi] = List()

    error_list = error_list ++ CampaignService.validateProspectUnAssignData(
      isApiCall = true,
      data = data
    )
    prospectService.getProspectIdsFromUuid(
      data = data.prospect_ids,
      teamId = teamId
    ) match {

      case Left(error) =>
        Left(UnAssignProspectsErrorV3.GetProspectIdFromUuidErrors(err = error))

      case Right(validIds_and_invalidUuids) =>

        val invalid_uuids = validIds_and_invalidUuids.invalid_uuids
        val prospect_ids = validIds_and_invalidUuids.valid_prospect_ids.map(_.id)

        if (invalid_uuids.nonEmpty) {
          val err = ErrorResponseProspectsUnAssignApi(
            message = "Invalid prospect ids passed",
            data = Some(invalid_uuids.mkString(",")),
            error_type = ProspectUnAssignErrorType.BAD_REQUEST
          )

          error_list = error_list.appended(err)
        }

        prospectServiceV2.filterOwnedProspects(
          prospectIds = prospect_ids,
          permittedAccountIds = permittedAccountIds.map(p=>p.id),
          teamId = teamId.id,
          orgId = orgId,
          SRLogger = Logger
        ) match {

          case Failure(exception) => Left(UnAssignProspectsErrorV3.FilterOwnedProspectError(err = exception))

          case Success(accountOwnedProspects: List[OwnedProspectsForAssigning]) =>
            if (prospect_ids.length != accountOwnedProspects.length) {

              val msg = "Given prospects do not exist in your account"

              // REF: https://stackoverflow.com/a/********
              val accountOwnedProspectsSet = accountOwnedProspects.map(_.prospect_id).toSet
              val prospectIdsThatAreNotOwned: List[ProspectId] = prospect_ids.filterNot(accountOwnedProspectsSet).map(ProspectId(_))

              prospectService.getProspectUuidFromId(
                data = prospectIdsThatAreNotOwned,
                teamId = teamId
              ) match {

                case Failure(exception) =>
                  Logger.fatal(s"Campaign.unassign Error while getting prospect uuid from id", err = exception)
                  Left(UnAssignProspectsErrorV3.GetProspectUuidFromId(err = exception))

                case Success(notOwnedProspectUuids) =>
                  Logger.fatal(s"Campaign.unassign doNotExistError: $msg :: prospectIds.length: ${prospect_ids.length} :: ${accountOwnedProspects.length} :: prospectIdsThatAreNotOwnedCount: ${prospectIdsThatAreNotOwned.length} :: prospectIdsCount : ${prospect_ids.length} :: accountOwnedProspectIdsCount: ${accountOwnedProspects.map(_.prospect_id).length}")

                  val err = ErrorResponseProspectsUnAssignApi(
                    message = "Given prospects do not exist in your account",
                    data = Some(notOwnedProspectUuids.mkString(",")),
                    error_type = ProspectUnAssignErrorType.FORBIDDEN
                  )

                  error_list = error_list.appended(err)
                  Left(UnAssignProspectsErrorV3.UnAssignProspectsValidationErrors(err = error_list))

              }


            } else {
              prospectDAOService.find(
                byCampaignId = Option(campaign.id),
                teamId = teamId.id,
                Logger = Logger
              ) match {
                case Failure(exception) => Left(UnAssignProspectsErrorV3.FilterCampaignOwnedProspectError(err = exception))
                case Success(campaignOwnedProspectObject) =>
                  val campaignOwnedProspects = campaignOwnedProspectObject.map(cpo => cpo.id).toList
                  if(!(prospect_ids.toSet subsetOf campaignOwnedProspects.toSet)){

                    val msg = "Given prospects do not exist in your campaign"

                    val prospectNotInCampaign = prospect_ids.filter(p => !campaignOwnedProspects.contains(p))

                    prospectService.getProspectUuidFromId(
                      data = prospectNotInCampaign.map(p => ProspectId(p)),
                      teamId = teamId
                    ) match {

                      case Failure(exception) =>
                        Logger.fatal(s"Campaign.unassign Error while getting prospect uuid from id", err = exception)
                        Left(UnAssignProspectsErrorV3.GetProspectUuidFromId(err = exception))

                      case Success(notOwnedProspectUuids) =>
                        Logger.fatal(s"Campaign.unassign doNotExistError: $msg :: prospectIds.length: ${prospect_ids.length} :: ${campaignOwnedProspects.length} :: prospectIdsThatAreNotOwnedCount: ${prospectNotInCampaign.length} :: prospectIdsCount : ${prospect_ids.length} :: accountOwnedProspectIdsCount: ${accountOwnedProspects.map(_.prospect_id).length}")

                        val err = ErrorResponseProspectsUnAssignApi(
                          message = "Given prospects do not exist in the given campaign",
                          data = Some(notOwnedProspectUuids.mkString(",")),
                          error_type = ProspectUnAssignErrorType.FORBIDDEN
                        )

                        error_list = error_list.appended(err)
                        Left(UnAssignProspectsErrorV3.UnAssignProspectsValidationErrors(err = error_list))

                    }

                  } else{
                    if (error_list.isEmpty) {
                      Right(UnAssignProspectsValidationResponseV3(
                        valid_prospect_ids = prospect_ids.map(p => ProspectId(p)),
                        campaign_id = CampaignId(campaign.id)
                      ))
                    }
                    else {
                      Left(UnAssignProspectsErrorV3.UnAssignProspectsValidationErrors(err = error_list))
                    }
                  }
              }
            }
        }
    }
  }

  def unAssignProspectFromCampaign(
                                  data: CampaignProspectFormUuids,
                                  teamId: TeamId,
                                  orgId: OrgId,
                                  permittedAccountIds: Seq[AccountId],
                                  campaign: Campaign,
                                  user_id: AccountId,
                                  doerAccountName: DoerAccountName
                                )(using Logger: SRLogger): Either[UnAssignProspectsErrorV3, UnAssignProspectsResponse] = {
    getValidProspectIdsForUnAssignProspect(
      data = data,
      teamId = teamId,
      orgId = orgId,
      permittedAccountIds = permittedAccountIds,
      campaign = campaign
    ) match {

      case Left(error) =>
        Left(error)

      case Right(validationResponse) =>
        campaignProspectService.unassign(
          permittedOwnerIds = permittedAccountIds.map(a => a.id),
          doerAccountId = user_id.id,
          teamId = teamId.id,
          doerAccountName = doerAccountName.name,
          campaignId = validationResponse.campaign_id.id,
          prospectIds = validationResponse.valid_prospect_ids.map(p => p.id),
          Logger = Logger
        ) match {

          case Failure(e) =>
            Left(UnAssignProspectsErrorV3.UnAssignProspectError(
              err = "Error while unassigning prospects: " + e.getMessage,
            ))
          case Success(_) =>
            Right(
              UnAssignProspectsResponse("Success")
            )
        }
    }

  }


}
