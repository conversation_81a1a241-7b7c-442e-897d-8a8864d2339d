package api.campaigns.services

import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.{Campaign, CampaignCreateForm, CampaignDAO, CampaignEmailSettings, CampaignEmailSettingsV2, CampaignIdAndTeamId, CampaignSettings, CampaignStepDAO, CampaignStepVariantDAO, CampaignStepWithChildren}
import api.campaigns.dao.{CampaignEmailSettingsDAO, CampaignSchedulingMetadataDAO}
import api.campaigns.models.{CampaignAISequenceStatus, CampaignAddEmailChannelError, CampaignInboxSettingData, CampaignType}
import io.smartreach.esp.api.emails.EmailSettingId
import io.sr.billing_common.models.PlanID
import utils.SRLogger
import utils.dbutils.DBUtils
import utils.featureflags.services.SrFeatureFlags

import scala.util.{Failure, Success, Try}

class CampaignDAOService(
                          campaignDAO: CampaignDAO,
                          campaignEmailSettingsDAO: CampaignEmailSettingsDAO,
                          campaignSchedulingMetadataDAO: CampaignSchedulingMetadataDAO,
                          dbUtils: DBUtils,
                          campaignStepVariantDAO: CampaignStepVariantDAO
                        ) {

  /*
  when we get a update campaign_email_settings  we are sending a list of sender emails and one receiver email and we need to updated the table accordingly
  steps to take -
  1. update the campaigns table to keep the old_receiver_email_setting_id and old_sender_email_setting_id up to date
  2. figure out if the the current set of senders in the db includes some sender email that is not present in the data that we are getting from the frontend
     if so, delete those rows
  3. figure out if the receiver is different in the db from the data that we got from the frontend, if so update the receiver for all the campaign_email_settings
  4. find all the sender_email_setting_id that are present in the data that we got from the frontend, and not in db, and add the same to the db
   */
    def updateEmailSettingsV2(
                               id: CampaignId,
                               data: List[CampaignEmailSettingsV2],
                               team_id: TeamId,
                               planID: PlanID,
                               orgId: OrgId
                             ): Try[Either[CampaignAddEmailChannelError, Option[CampaignIdAndTeamId]]] = {

        val maxEmailLimit: Int = SrFeatureFlags.getEmailLimitForCampaign(planID = planID, orgId = orgId)

        (for {
            currentCampaignEmailSettings: List[CampaignEmailSettings] <- campaignEmailSettingsDAO
              .getCampaignSenderEmailsWithSession(id, team_id)

            newSenders = data.map(_.sender_email_settings_id)
            eidsToDelete = currentCampaignEmailSettings
              .filterNot(ces => newSenders.contains(ces.sender_email_setting_id.emailSettingId))
              .map(_.sender_email_setting_id)

            senderToAdd = data.filterNot { newSetting =>
                currentCampaignEmailSettings.exists(_.sender_email_setting_id.emailSettingId == newSetting.sender_email_settings_id)
            }
            proposedTotal = currentCampaignEmailSettings.size - eidsToDelete.size + senderToAdd.size
            result <- if (proposedTotal > maxEmailLimit) {
                Success(Left(CampaignAddEmailChannelError.MaxEmailSettingLimitError))
            } else {
                (for {
                    _:Int <- campaignEmailSettingsDAO.delete(id, team_id, eidsToDelete)


                    _:Int <- if (currentCampaignEmailSettings.nonEmpty) {
                        val toUpdate = data.filter { newSetting =>
                            currentCampaignEmailSettings
                              .find(_.sender_email_setting_id.emailSettingId == newSetting.sender_email_settings_id)
                              .exists(_.receiver_email_setting_id.emailSettingId != newSetting.receiver_email_settings_id)
                        }
                        campaignEmailSettingsDAO.updateReceiver(id, team_id, toUpdate)
                    } else Success(0)
                    _:Int <- campaignEmailSettingsDAO.addingCampaignEmailSettingWithSession(id, senderToAdd, team_id)

                } yield Right(Some(CampaignIdAndTeamId(id.id, team_id.id))))
                  .recover { case e =>
                      Left(CampaignAddEmailChannelError.DbFailure(e))
                  }
            }
        } yield result).recover {
            case e: Throwable =>
                Left(CampaignAddEmailChannelError.DbFailure(e))
        }
    }


    def changeCampaignAISequenceStatus(
                                      campaignId: CampaignId,
                                      teamId: TeamId,
                                      status: CampaignAISequenceStatus
                                    ): Try[String] = {

    campaignDAO.changeCampaignAISequenceStatus(
      campaignId = campaignId,
      teamId = teamId,
      status = status
    )

  }

  def hasAlreadyTriggeredAISequenceGenerationForCampaign(
                                                          accountId: AccountId,
                                                          teamId: TeamId
                                                        ): Try[Option[String]] = {

    campaignDAO.hasAlreadyTriggeredAISequenceGenerationForCampaign(
      accountId = accountId,
      teamId = teamId
    )

  }

  def getCampaignSenderEmailsWithSession(
                                          campaignId: CampaignId,
                                          teamId: TeamId
                                        ): Try[List[CampaignEmailSettings]] = {
    campaignEmailSettingsDAO.getCampaignSenderEmailsWithSession(
      campaignId = campaignId,
      teamId = teamId
    )
  }


  def create(
              accountId: Long,
              teamId: Long,
              taId: Long,
              name: String,
              campaignSettings: Option[CampaignSettings],
              timezone: String,
              campaign_type: CampaignType,
              campaignUuid: String
            ): Try[Option[Long]] = {
    val dbAndSession = dbUtils.startLocalTx()


    val newCampaignEmailSettings = campaignSettings.map{cs =>
      cs.campaign_email_settings.map{ces =>
        CampaignEmailSettingsV2(
          sender_email_settings_id = ces.sender_email_setting_id.emailSettingId,
          receiver_email_settings_id = ces.receiver_email_setting_id.emailSettingId
        )
      }
    }.getOrElse(List())
    val db = dbAndSession.db
    val session = dbAndSession.session
    val campaignId = for {
      campaignId: Option[Long] <- campaignDAO.create(
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        name = name,
        campaignSettings = campaignSettings,
        timezone = timezone,
        campaign_type = campaign_type,
        campaignUuid = campaignUuid
      )(session)
      _: Int <- campaignEmailSettingsDAO.addingCampaignEmailSetting(
        id = CampaignId(campaignId.get),
        team_id = TeamId(teamId),
        data = newCampaignEmailSettings
      )(session)
      _: Option[CampaignId] <- campaignSchedulingMetadataDAO.addCampaignSchedulingMetadata(
        campaign_id = CampaignId(campaignId.get),
        team_id = TeamId(teamId)
      )(session)
    } yield campaignId

    dbUtils.commitAndCloseSession(db = db)

    campaignId
  }

  def findOrderedSteps(
    campaignId: Long,
    teamId: TeamId
  ): Seq[CampaignStepWithChildren] = {

    val headStepIdOpt = campaignDAO.findCampaignForCampaignUtilsOnly(
      id = campaignId,
      teamId = teamId,
    )
      .flatMap(_.head_step_id)

    headStepIdOpt match {

      case None =>

        Seq()

      case Some(headStepId) =>

        val steps = campaignStepVariantDAO.findByCampaignId(
          campaignIds = Seq(campaignId),
          teamId = teamId
        )

        CampaignStepDAO.getOrderedSteps(
          steps = steps,
          headStepId = headStepId
        )

    }

  }

  def findSettingsForCampaignInbox(
                                    permittedAccountIds_EDIT_CAMPAIGNS: Seq[Long],
                                    campaign_id: Option[CampaignId],
                                    teamId: TeamId
                                  ): Try[Seq[CampaignInboxSettingData]] = {

  campaignDAO.findSettingsForCampaignInbox(
      permittedAccountIds_EDIT_CAMPAIGNS = permittedAccountIds_EDIT_CAMPAIGNS,
      campaign_id = campaign_id,
      teamId = teamId
    )

  }

  def getNameForNewCampaign(
                             ownerFirstName: String,
                             teamId: TeamId,
                           ): Try[String] ={
    campaignDAO.getNumberOfCampaignWhichMatchesNewCampaign(
      ownerFirstName = ownerFirstName,
      teamId = teamId
    ) match {
      case Failure(exception) => Failure(exception)
      case Success(campaignNumber) =>
        Success(s"${ownerFirstName}'s Campaign - ${campaignNumber+1}")
    }
  }

  def findCampaignForCampaignUtilsOnly(id: Long, teamId: TeamId): Option[Campaign] = {
    campaignDAO.findCampaignForCampaignUtilsOnly(
      id = id,
      teamId = teamId
    )
  }

}
