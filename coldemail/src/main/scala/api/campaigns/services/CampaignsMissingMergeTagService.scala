package api.campaigns.services

import api.accounts.TeamId

import scala.util.{Failure, Success, Try}
import api.columns.{ColumnDef, FieldTypeEnum, MagicColumnResponse, ProspectColGenStatus, ProspectColumnDef}
import api.campaigns.{CampaignProspectDAO, UpdateNextScheduleAtData}
import api.prospects.models.ProspectId
import eventframework.ProspectObject
import sr_scheduler.models.ChannelType
import utils.SRLogger
import utils.mq.channel_scheduler.channels.ChannelSchedulerTrait
import utils.mq.channel_scheduler.channels.model.NextCheckForSchedulingIntervalType

class CampaignsMissingMergeTagService(
                                     campaignProspectDAO: CampaignProspectDAO,
//                                     mergeTagService: MergeTagService,
                                     campaignCacheService: CampaignCacheService,
                                       prospectColumnDef: ProspectColumnDef,

  ) {

  def addMissingOrInvalidFieldsAndResetCache(
                                     campaignId: CampaignId,
                                     prospectId: ProspectId,
                                     teamId: TeamId,
                                     absentTagsFounds: Seq[String]
                                     )(using logger: SRLogger): Try[Int] = {

    for {
      missingMergeError: Int <- campaignProspectDAO.addMissingFieldsErrorMessage(
        campaignId = campaignId.id,
        prospectId = prospectId.id,
        fields = absentTagsFounds
      )

      resetCache: Unit <- Try{campaignCacheService.resetCampaignStats(
        campaignId = campaignId.id,
        teamId = teamId.id
      )}

    } yield {
      missingMergeError
    }

  }

  private def getAllUsedMagicColumns(
    teamId: TeamId,
    subject: String,
    body: String,
    channelType: ChannelType,
  )(
    implicit Logger: SRLogger
  ): Try[List[ColumnDef]] = {

    val tryOfAllMagicColumns = Try {

      // TODO: This DAO call is not wrapped in Try
      prospectColumnDef.allColumns(
        teamId = teamId.id,
        channel = Some(channelType),
      )
        .filter(_.field_type == FieldTypeEnum.MAGIC)

    }

    tryOfAllMagicColumns match {

      case Failure(exception) =>

        Logger.shouldNeverHappen(
          msg = s"Failed to fetch prospectColumnDef.allColumns. teamId: $teamId",
          err = Some(exception),
        )

        Failure(exception)

      case Success(allMagicColumns) =>

        // val x = """\{\{\s*p\s*\}\}""".r

        val magicColsUsedInSubject = allMagicColumns.filter { mc =>

          CampaignTemplateService.containsMergeTag(
            s = subject,
            mergeTagName = mc.name,
          )

        }

        val magicColsUsedInBody = allMagicColumns.filter { mc =>

          CampaignTemplateService.containsMergeTag(
            s = body,
            mergeTagName = mc.name,
          )

        }

        // All magic columns, used in either subject or body.
        val allUsedMagicColumns = magicColsUsedInSubject ++ magicColsUsedInBody

        Success(allUsedMagicColumns.distinct)

    }

  }

  def checkMagicColumnWithErrors(
    teamId: TeamId,
    subject: String,
    body: String,
    prospect: ProspectObject,
    channelType: ChannelType,
  )(
    implicit Logger: SRLogger
  ): Try[List[MagicColumnResponse]] = {

    getAllUsedMagicColumns(
      teamId = teamId,
      subject = subject,
      body = body,
      channelType = channelType,
    ).map { allUsedMagicColumns =>

      val failedCols = prospect.internal.magic_columns.filter { mc =>

        val isGenFailed = mc.status match {

          case ProspectColGenStatus.Pending
               | ProspectColGenStatus.Queued
               | ProspectColGenStatus.Completed =>

            false

          case ProspectColGenStatus.Failed =>

            true

        }

        /**
          * 3 Dec 2024
          *
          * We check if the magic column generation has failed,
          * but we do not raise an error if the failed magic column is not used in subject or body.
          *
          * Missing merge tag error will only be raised if the magic column generation has failed
          * and that failed magic column is used as a merge tag in subject or body.
          */

        isGenFailed && allUsedMagicColumns.map(_.name).contains(mc.column_name)

      }

      failedCols

    }

  }

  def checkPendingMagicColumn(
    teamId: TeamId,
    subject: String,
    body: String,
    prospect: ProspectObject,
    campaignId: CampaignId,
    channelType: ChannelType,
  )(
    implicit Logger: SRLogger
  ): Try[List[MagicColumnResponse]] = {

    getAllUsedMagicColumns(
      teamId = teamId,
      subject = subject,
      body = body,
      channelType = channelType,
    ).flatMap { allUsedMagicColumns =>

      val pendingCols = prospect.internal.magic_columns.filter { mc =>

        val isGenPending = mc.status match {

          case ProspectColGenStatus.Pending
               | ProspectColGenStatus.Queued =>

            true

          // Failed will handled separately, we will show it to user as error.
          case ProspectColGenStatus.Failed
               | ProspectColGenStatus.Completed =>

            false

        }

        /**
          * 3 Dec 2024
          *
          * We check if the magic column generation is still pending,
          * but we do not skip scheduling the prospect if the pending magic column
          * is not used in subject or body.
          */

        isGenPending && allUsedMagicColumns.map(_.name).contains(mc.column_name)

      }


      if (pendingCols.isEmpty) {

        // The magic column which are used are not pending

        Success(pendingCols)

      } else {

        campaignProspectDAO.updateNextCheckForSchedulingAt(
          data = List(
            UpdateNextScheduleAtData(
              campaignId = campaignId,
              prospectId = ProspectId(id = prospect.id),
              teamId = teamId,
              // TODO: should I use this fn or match the cron interval ??
              nexToBeCheckedAt = ChannelSchedulerTrait.nextCheckForSchedulingIntervalForCampaignProspect(
                nextCheckForSchedulingIntervalType = NextCheckForSchedulingIntervalType.PendingMagicColumns,
              ),
            )
          )
        ) match {

          case Failure(exception) =>

            Logger.shouldNeverHappen(
              msg = s"Failed to set next to be scheduled at for prospect with pending magic cols. teamId: $teamId :: campaignId: $campaignId :: prospectId: ${prospect.id}",
              err = Some(exception),
            )

            Failure(exception)

          case Success(_) =>

            Success(pendingCols)

        }

      }

    }

  }


}
