package api.campaigns.services

import api.AppConfig
import api.accounts.{Account, ReplyHandling, TeamId}
import api.accounts.dao.{CalendlyAccessTokens, CalendlySettings, TeamsDAO}
import api.accounts.dao_service.AccountDAOService
import api.accounts.models.{AccountId, OrgId}
import api.blacklist.{Blacklist, BlacklistCreateDomainsForm, BlacklistCreateEmailsForm, BlacklistCreateForm, BlacklistService, DomainsWithExcludedEmails}
import api.blacklist.models.BlacklistCreateOrDeleteApiLevel
import api.campaigns.{CPCompleted, CPMarkAsCompleted}
import api.campaigns.models.CampaignProspectCompletedReason
import api.integrations.{CalendlyAccessTokenResponse, CalendlyOAuth, CreateWebhookCalendlyError, GetAccessTokenCalendlyError}
import api.prospects.{BatchActionErrors, ProspectBatchActionService, ProspectBatchActionUpdateCategory}
import api.prospects.dao.ProspectDAO
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.ProspectCategory
import eventframework.ProspectObject
import play.api.libs.json.{JsObject, Json}
import play.api.libs.ws.WSClient
import utils.{Helpers, SRLogger, StringUtils}
import utils.emailvalidation.EmailValidationService
import utils.security.SignatureValidationService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

sealed trait OAuthFetchTokenAndCreateWebhookCalendlyError

object OAuthFetchTokenAndCreateWebhookCalendlyError {
    case class BadRequestError(message: String) extends OAuthFetchTokenAndCreateWebhookCalendlyError

    case class ServerError(message: String, error: Option[Throwable]) extends OAuthFetchTokenAndCreateWebhookCalendlyError
}

// New error types for disconnection flow
sealed trait CalendlyDisconnectionError

object CalendlyDisconnectionError {
    case class TokenRetrievalError(message: String) extends CalendlyDisconnectionError
    case class WebhookDeletionError(message: String) extends CalendlyDisconnectionError
    case class DatabaseError(message: String, error: Option[Throwable]) extends CalendlyDisconnectionError
    case class IntegrationNotFoundError(message: String) extends CalendlyDisconnectionError
    case class OAuthRevocationError(message: String) extends CalendlyDisconnectionError
}

sealed trait WebhookSignatureValidationResult
object WebhookSignatureValidationResult {
    case class InvalidSignature(message: String) extends WebhookSignatureValidationResult
    case class InvalidBody(message: String) extends WebhookSignatureValidationResult
    case class InvalidPayload(message: String) extends WebhookSignatureValidationResult
    case class UnsupportedEvent(message: String) extends WebhookSignatureValidationResult
    case class Success(prospectEmail: String) extends WebhookSignatureValidationResult
}

class CalendlyWebhookService(
                            teamDAO: TeamsDAO,
                            calendlyOAuth: CalendlyOAuth,
                            prospectDAOService: ProspectDAOService,
                            campaignProspectService2: CampaignProspectService2,
                            blacklistService: BlacklistService,
                            accountDAOService: AccountDAOService,
                            prospectBatchActionService: ProspectBatchActionService
                            ) {

    private def isTokenExpiredOrExpiringSoon(tokens: CalendlyAccessTokens): Boolean = {
        val result: Boolean = tokens.expiresAt match {
            case None => 
                // If we don't have expiry info, assume it might be expired and refresh
                true
            case Some(expiryTime: org.joda.time.DateTime) =>
                val now: org.joda.time.DateTime = org.joda.time.DateTime.now()
                val bufferTime: org.joda.time.DateTime = expiryTime.minusMinutes(5) // 5-minute buffer
                now.isAfter(bufferTime)
        }
        result
    }

    def validateWebhookSignatureAndProcess(
        signatureHeader: Option[String],
        rawBody: Option[String]
    )(using logger: SRLogger): WebhookSignatureValidationResult = {

        rawBody match {
            case None =>
                logger.error("Missing or invalid request body")
                WebhookSignatureValidationResult.InvalidBody("Missing or invalid request body")

            case Some(body) =>
                // Validate webhook signature
                val isValidSignature = SignatureValidationService.validateWebhookSignature(
                    signingKey = AppConfig.calendlyOAuthSettings.webhookSigningKey,
                    signatureHeader = signatureHeader,
                    rawBody = body,
                    maxAgeSeconds = 300
                )

                if (!isValidSignature) {
                    logger.error("Invalid or missing Calendly webhook signature")
                    WebhookSignatureValidationResult.InvalidSignature("Invalid webhook signature")
                } else {
                    // Process the payload
                    Try(Json.parse(body)) match {
                        case Success(json) =>
                            json.validate[JsObject].fold(
                                errors => {
                                    logger.error(s"Invalid JSON payload: $errors")
                                    WebhookSignatureValidationResult.InvalidPayload(s"Invalid JSON: $errors")
                                },
                                validPayload => {
                                    logger.debug(s"Parsed payload: $validPayload")
                                    val event = (validPayload \ "event").asOpt[String]
                                    val payload = (validPayload \ "payload").asOpt[JsObject]

                                    if (event.contains("invitee.created") && payload.isDefined) {
                                        val inviteeName = (payload.get \ "name").asOpt[String]
                                        val inviteeEmail = (payload.get \ "email").asOpt[String].getOrElse("")
                                        val eventTime = (payload.get \ "scheduled_at").asOpt[String]

                                        logger.info(s"Calendly invitee created: name=$inviteeName, email=$inviteeEmail, time=$eventTime")
                                        WebhookSignatureValidationResult.Success(inviteeEmail)
                                    } else {
                                        logger.info(s"Ignoring non-invitee.created event: $event")
                                        WebhookSignatureValidationResult.UnsupportedEvent(s"Ignoring non-invitee.created event: $event")
                                    }
                                }
                            )
                        case Failure(e) =>
                            logger.error(s"Failed to parse JSON body: $e")
                            WebhookSignatureValidationResult.InvalidPayload("Invalid JSON body")
                    }
                }
        }
    }

    def fetchTokenAndCreateWebhookSuscription(
                                             code: String,
                                             teamId: TeamId,
                                             accountId: AccountId,
                                             orgId: OrgId
                                             )(
                                             using logger: SRLogger,
                                             ec: ExecutionContext,
                                             ws: WSClient): Future[Either[OAuthFetchTokenAndCreateWebhookCalendlyError, Int]] = {

      for {
        // Step 1: Get access token
        tokenResponse: Either[GetAccessTokenCalendlyError, CalendlyAccessTokenResponse.FullTokenData] <- calendlyOAuth.getAccessToken(code = code)
        tokenResult: Either[OAuthFetchTokenAndCreateWebhookCalendlyError.ServerError, CalendlyAccessTokenResponse.FullTokenData] <- tokenResponse match {
          case Left(error) =>
              Future.successful(Left(OAuthFetchTokenAndCreateWebhookCalendlyError.ServerError(
                  message = s"Failed to fetch the tokens",
                  error = None
              )))
          case Right(token) =>
              Future.successful(Right(token))
        }
        if tokenResult.isRight

        // Step 2: Update OAuth tokens for Calendly
        _ <- Future.fromTry(teamDAO.updateOAuthTokensForCalendly(
          teamId = teamId,
          tokenData = tokenResult.toOption.get
        )).recoverWith { case exception =>
          logger.error(s"updateOAuthTokensForCalendly : error while updating tokens", err=exception)
          Future.successful(Left(OAuthFetchTokenAndCreateWebhookCalendlyError.ServerError(
            message = "SQL error occurred while saving the token to db",
            error = Some(exception)
          )))
        }

        // Step 3: Create Calendly credentials
        calendlyCredentials: CalendlyAccessTokens = CalendlyAccessTokens(
          accessToken = tokenResult.toOption.get.access_token,
          refreshToken = tokenResult.toOption.get.refresh_token,
          userUrl = tokenResult.toOption.get.user_uri,
          organizationUrl = tokenResult.toOption.get.organization_uri,
            expiresAt = tokenResult.toOption.get.access_token_expires_at
        )

        // Step 4: Create webhook subscription
        webhookResponse: Either[CreateWebhookCalendlyError, String] <- calendlyOAuth.createWebhookSubscription(
          teamId = teamId,
          accountId = accountId,
          orgId = orgId,
          token = calendlyCredentials
        )
        
        webhookResult: Either[OAuthFetchTokenAndCreateWebhookCalendlyError.ServerError, String] <- webhookResponse match {
          case Left(error) => 
            Future.successful(Left(OAuthFetchTokenAndCreateWebhookCalendlyError.ServerError(
              message = s"Failed to create the webhook",
              error = None
            )))
          case Right(webhookId) =>
            Future.successful(Right(webhookId))
        }
        if webhookResult.isRight

        // Step 5: Update Calendly webhook ID
        updated: Either[OAuthFetchTokenAndCreateWebhookCalendlyError.ServerError, Int] <- Future.fromTry(teamDAO.updateCalendlyWebhookId(teamId, webhookResult.toOption.get))
          .map(result => Right(result))
          .recover { case exception =>
            logger.error(s"updateCalendlyWebhookId : error while updating webhookId", err=exception)
            Left(OAuthFetchTokenAndCreateWebhookCalendlyError.ServerError(
              message = "SQL error occurred while saving the webhook ID to db",
              error = Some(exception)
            ))
          }
      } yield updated
    }

    def handleWebhookEvent(
                            teamId: TeamId,
                            accountId: AccountId,
                            orgId: OrgId,
                            prospectEmail: String
                          )(implicit logger: SRLogger, ec: ExecutionContext): Future[(List[CPCompleted], Seq[Blacklist])] = {
        for {
            calendlyOptions: CalendlySettings <- Future.fromTry(teamDAO.getCalendlySettings(teamId = teamId))

            prospectDetails: Seq[ProspectObject] <- Future.fromTry(prospectDAOService.findProspects(
                byProspectEmails = Seq(prospectEmail),
                teamId = teamId.id,
                org_id = orgId
            ))

            account: Option[Account] <- Future.fromTry(accountDAOService.findAccountFromDbIgnoringCache(id = accountId))


            completedResult: List[CPCompleted] <- if (prospectDetails.nonEmpty &&
              (calendlyOptions.mark_prospect_campaign_completed ||
                calendlyOptions.mark_prospect_domain_completed)) {
                markProspectsAsCompleted(
                    calendlyOptions = calendlyOptions,
                    prospectDetails = prospectDetails,
                    teamId = teamId,
                    account = account
                )
            } else {
                logger.error(s"No prospect was found for email : $prospectEmail")
                Future.successful(List.empty)
            }

            // Add to blacklist only if prospectDetails is not empty, prospectEmail is non-empty, and relevant calendly options are set
            blacklistResult: Seq[Blacklist] <- if (prospectDetails.nonEmpty &&
              prospectEmail.nonEmpty &&
              (calendlyOptions.mark_prospect_to_dnc ||
                calendlyOptions.mark_prospect_domain_to_dnc)) {
                addProspectsToBlacklist(
                    calendlyOptions = calendlyOptions,
                    prospectEmail = prospectEmail,
                    teamId = teamId,
                    orgId = orgId,
                    accountId = accountId,
                    account = account
                )
            } else {
                logger.error(s"No prospect was found for email : $prospectEmail")
                Future.successful(Seq.empty)
            }

        } yield {
            (completedResult, blacklistResult)
        }
    }

     private def markProspectsAsCompleted(
                                          calendlyOptions: CalendlySettings,
                                          prospectDetails: Seq[ProspectObject],
                                          teamId: TeamId,
                                          account: Option[Account]
                                        )(implicit logger: SRLogger, ec: ExecutionContext): Future[List[CPCompleted]] = {
        val logRequestId = StringUtils.genLogTraceId + "_markProspectsAsCompleted"
        given Logger: SRLogger = new SRLogger(logRequestId = logRequestId)
        val cpData = prospectDetails.map(prospect =>
            CPMarkAsCompleted(
                email_reply_status = None,
                completed_because_email_message_id = None,
                prospect_id = prospect.id,
                campaign_id = None, // Let the service handle which campaigns to mark as completed based on replyHandling
                prospect_account_id = prospect.internal.prospect_account_id
            )
        )

        val replyHandling: ReplyHandling.Value = if (calendlyOptions.mark_prospect_domain_completed) {
            ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY
        } else {
            ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY
        }

        val completedResults: Try[List[CPCompleted]] = campaignProspectService2._markAsCompleted(
            cpData = cpData,
            replyHandling = replyHandling,
            teamId = teamId.id,
            SRLogger = logger,
            isHardBounceFlow = false,
            completed_reason = CampaignProspectCompletedReason.MarkAsCompletedFromCalendlyWebhook
        )

        completedResults match {
            case Failure(exception) =>
                Future.failed(exception)

            case Success(markedCompletedProspects) =>
                Logger.info(s"Prospect marked complete ${markedCompletedProspects}")
                if(markedCompletedProspects.nonEmpty) {

                    prospectDAOService.getProspectCategoryId(
                        teamId = teamId,
                        text_id = ProspectCategory.MEETING_BOOKED,
                        account = account
                    ) match {
                        case Failure(exception) =>
                            Future.failed(exception)

                        case Success(meetingBookedCategoryId) =>


                            prospectBatchActionService.updateCategory(
                                data = ProspectBatchActionUpdateCategory(category_id = meetingBookedCategoryId.id),
                                teamId = teamId.id,
                                loginAccount = account.get,
                                prospectIds = markedCompletedProspects.map(_.prospectId),
                                Logger = logger,
                                permittedAccountIds = Seq(account.get.internal_id),
                                auditRequestLogId = logRequestId
                            ) match {
                                case Left(BatchActionErrors.BadRequestError(e)) =>
                                    Logger.error(s"prospectBatchActionService.updateCategory bad request  error occured : $e")

                                case Left(BatchActionErrors.ServerError(e)) =>
                                    Logger.error(s"prospectBatchActionService.updateCategory server error occured", err = e)

                                case Right(categoryUpdated) =>
                                    Logger.info(s"prospect category updated ${categoryUpdated}")
                            }


                    }
                }
                Future.successful(markedCompletedProspects)

        }

    }

    private def addProspectsToBlacklist(
                                         calendlyOptions: CalendlySettings,
                                         prospectEmail: String,
                                         teamId: TeamId,
                                         orgId: OrgId,
                                         accountId: AccountId,
                                         account: Option[Account]
                                       )(implicit logger: SRLogger, ec: ExecutionContext): Future[Seq[Blacklist]] = {
        //        if (calendlyOptions.mark_prospect_to_dnc || calendlyOptions.mark_prospect_domain_to_dnc && prospectEmail.nonEmpty) {
        val blacklistData = if (calendlyOptions.mark_prospect_domain_to_dnc) {
            val domain = DomainsWithExcludedEmails(
                domain = EmailValidationService.getLowercasedNameAndDomainFromEmail(email = prospectEmail)._2,
                excluded_emails = Seq()
            )
            BlacklistCreateDomainsForm(
                domains = Seq(domain)
            )
        }
        else {
            BlacklistCreateEmailsForm(
                emails = Seq(prospectEmail)
            )
        }

        if (account.isEmpty) {
            return Future.successful(Seq.empty)
        }

        val level: BlacklistCreateOrDeleteApiLevel.Team = BlacklistCreateOrDeleteApiLevel.Team(
            team_id = teamId,
            org_id = orgId,
            ta_id = account.get.teams.head.access_members.head.ta_id
        )

        val accountOwnerName: String = Helpers.getAccountName(a = account.get)

        Future.fromTry(
            blacklistService.createOrUpdateBlacklistService(
                accountId = accountId.id,
                addedByName = accountOwnerName,
                level = level,
                data = blacklistData,
                is_req_via_dnc_form = true,
                opted_out_from_campaign_id = None,
                opted_out_from_campaign_name = None,
                account = account.get,
                auditRequestLogId = None,
                isApiCall = false,
                team_id = Some(teamId),
                Logger = logger
            ) match {
                case Left(error) => Failure(new RuntimeException(s"Failed to blacklist email: $error"))
                case Right(blacklists) => Success(blacklists)
            }
        )
        //        } else {
        //            Future.successful(Seq.empty)
        //        }
    }

    /**
     * Disconnects the Calendly integration for a team.
     *
     * What it does:
     * - Retrieves current tokens and webhook ID from database
     * - Attempts to refresh access token if expired (to ensure API calls work)
     * - If tokens are valid, deletes webhook subscription from Calendly (MUST succeed)
     * - If webhook deletion succeeds, revokes OAuth access token from Calendly (MUST succeed)
     * - If token revocation succeeds, clears all Calendly integration data from database
     * - Fails fast if any step fails (proper error propagation)
     *
     * Why it is like this:
     * - Ensures complete and proper disconnection - no partial states
     * - Sequential execution prevents orphaned resources
     * - Webhook deletion before token revocation (webhook deletion needs valid token)
     * - Database cleanup only after successful API cleanup
     * - Follows OAuth 2.0 best practices for proper integration disconnection
     * - Provides accurate feedback to users about disconnection status
     */
    def disconnectCalendlyIntegration(
                                     teamId: TeamId
                                     )(using logger: SRLogger,
                                       ec: ExecutionContext,
                                       ws: WSClient): Future[Either[CalendlyDisconnectionError, Boolean]] = {

        val disconnectionFlow = for {
            // Step 1: Check if integration exists and get tokens
            tokensOpt: Option[CalendlyAccessTokens] <- Future.fromTry(teamDAO.getCalendlyOAuthTokens(teamId = teamId))
              .recover { case exception =>
                  logger.error(s"Failed to retrieve Calendly tokens for team ${teamId.id}", err = exception)
                  None
              }

            // Step 2: If no integration exists, return error
            _ <- if (tokensOpt.isEmpty) {
                Future.successful(Left(CalendlyDisconnectionError.IntegrationNotFoundError(message = s"No Calendly integration found for team ${teamId.id}")))
            } else {
                Future.successful(Right(()))
            }

            // Step 3: Refresh token if expired (to ensure subsequent API calls work)
            validTokensResult: Either[String, CalendlyAccessTokens] <- if (isTokenExpiredOrExpiringSoon(tokens = tokensOpt.get)) {
                logger.info(s"Calendly token is expired or expiring soon for team ${teamId.id}, refreshing before disconnection")
                calendlyOAuth.refreshAccessToken(refreshToken = tokensOpt.get.refreshToken)
                  .flatMap {
                      case Right(newTokenData) =>
                          // Update database with new tokens
                          Future.fromTry(teamDAO.updateOAuthTokensForCalendly(teamId = teamId, tokenData = newTokenData))
                            .map { _ =>
                                logger.info(s"Successfully refreshed Calendly tokens for disconnection of team ${teamId.id}")
                                Right(CalendlyAccessTokens(
                                    accessToken = newTokenData.access_token,
                                    refreshToken = newTokenData.refresh_token,
                                    userUrl = newTokenData.user_uri,
                                    organizationUrl = newTokenData.organization_uri,
                                    expiresAt = newTokenData.access_token_expires_at
                                ))
                            }
                            .recover { case exception =>
                                logger.error(s"Failed to update refreshed tokens for team ${teamId.id}", err = exception)
                                Left("Failed to update refreshed tokens")
                            }

                      case Left(error) =>
                          logger.error(s"Failed to refresh Calendly tokens for team ${teamId.id}: $error")
                          Future.successful(Left(s"Token refresh failed: $error"))
                  }
                  .recover { case exception =>
                      logger.error(s"Exception during token refresh for team ${teamId.id}", err = exception)
                      Left(s"Token refresh exception: ${exception.getMessage}")
                  }
            } else {
                logger.info(s"Calendly token is still valid for team ${teamId.id}, proceeding with full disconnection")
                Future.successful(Right(tokensOpt.get))
            }

        } yield validTokensResult

        // Continue with the flow based on token validation result
        disconnectionFlow.flatMap {
            case Left(tokenError) =>
                logger.error(s"Cannot proceed with Calendly disconnection for team ${teamId.id} due to token issues: $tokenError")
                Future.successful(Left(CalendlyDisconnectionError.TokenRetrievalError(message = s"Failed to obtain valid tokens: $tokenError")))

            case Right(validTokens) =>
                // Continue with the disconnection flow using validTokens
                for {
                    // Step 4: Get webhook ID (required for proper cleanup)
                    webhookIdOpt: Option[String] <- Future.fromTry(teamDAO.getCalendlyWebhookId(teamId = teamId))
                      .recover { case exception =>
                          logger.warn(s"Failed to retrieve webhook ID for team ${teamId.id}", err = exception)
                          None
                      }

                    // Step 5: Delete webhook subscription (MUST succeed if webhook exists)
                    webhookDeletionResult: Either[CalendlyDisconnectionError, Boolean] <- webhookIdOpt match {
                        case Some(webhookId: String) =>
                            logger.info(s"Deleting Calendly webhook for team ${teamId.id}")
                            calendlyOAuth.deleteWebhookSubscription(webhookId = webhookId, accessToken = validTokens.accessToken)
                              .map {
                                  case Right(_) =>
                                      logger.info(s"Successfully deleted Calendly webhook for team ${teamId.id}")
                                      Right(true)
                                  case Left(error) =>
                                      logger.error(s"Failed to delete Calendly webhook for team ${teamId.id}: $error")
                                      Left(CalendlyDisconnectionError.WebhookDeletionError(message = s"Webhook deletion failed: $error"))
                              }
                              .recover { case exception =>
                                  logger.error(s"Exception during webhook deletion for team ${teamId.id}", err = exception)
                                  Left(CalendlyDisconnectionError.WebhookDeletionError(message = s"Webhook deletion exception: ${exception.getMessage}"))
                              }
                        case None =>
                            logger.info(s"No webhook ID found for team ${teamId.id}, skipping webhook deletion")
                            Future.successful(Right(true))
                    }

                    // Step 6: Revoke OAuth access token (MUST succeed) - only if webhook deletion succeeded
                    oauthRevocationResult: Either[CalendlyDisconnectionError, Boolean] <- webhookDeletionResult match {
                        case Right(_) =>
                            logger.info(s"Revoking Calendly OAuth token for team ${teamId.id}")
                            calendlyOAuth.revokeAccessToken(accessToken = validTokens.accessToken)
                              .map {
                                  case Right(_) =>
                                      logger.info(s"Successfully revoked Calendly OAuth token for team ${teamId.id}")
                                      Right(true)
                                  case Left(error) =>
                                      logger.error(s"Failed to revoke Calendly OAuth token for team ${teamId.id}: $error")
                                      Left(CalendlyDisconnectionError.OAuthRevocationError(message = s"Token revocation failed: $error"))
                              }
                              .recover { case exception =>
                                  logger.error(s"Exception during OAuth token revocation for team ${teamId.id}", err = exception)
                                  Left(CalendlyDisconnectionError.OAuthRevocationError(message = s"Token revocation exception: ${exception.getMessage}"))
                              }
                        case Left(webhookError) =>
                            logger.error(s"Skipping token revocation due to webhook deletion failure for team ${teamId.id}")
                            Future.successful(Left(webhookError))
                    }

                    // Step 7: Clear integration data from database (final step - only after successful API cleanup)
                    clearResult: Either[CalendlyDisconnectionError, Boolean] <- oauthRevocationResult match {
                        case Right(_) =>
                            Future.fromTry(teamDAO.clearCalendlyIntegration(teamId = teamId))
                              .map { (rowsUpdated: Int) =>
                                  if (rowsUpdated > 0) {
                                      logger.info(s"Successfully cleared Calendly integration data for team ${teamId.id}")
                                      Right(true)
                                  } else {
                                      logger.warn(s"No rows updated when clearing Calendly integration for team ${teamId.id}")
                                      Right(true) // Still consider it successful if no data to clear
                                  }
                              }
                              .recover { case exception =>
                                  logger.error(s"Failed to clear Calendly integration data for team ${teamId.id}", err = exception)
                                  Left(CalendlyDisconnectionError.DatabaseError(
                                      message = "Failed to clear integration data from database",
                                      error = Some(exception)
                                  ))
                              }
                        case Left(revocationError) =>
                            logger.error(s"Skipping database cleanup due to token revocation failure for team ${teamId.id}")
                            Future.successful(Left(revocationError))
                    }

                } yield clearResult
        }
    }

    def refreshCalendlyTokenIfNeeded(
                                    teamId: TeamId
                                    )(using logger: SRLogger,
                                      ec: ExecutionContext,
                                      ws: WSClient): Future[Either[CalendlyDisconnectionError, Option[CalendlyAccessTokens]]] = {

        for {
            // Step 1: Get current tokens
            tokensOpt: Option[CalendlyAccessTokens] <- Future.fromTry(teamDAO.getCalendlyOAuthTokens(teamId = teamId))
              .recover { case exception =>
                  logger.error(s"Failed to retrieve Calendly tokens for team ${teamId.id}", err = exception)
                  None
              }

            // Step 2: Check if refresh is needed (token expires within 10 minutes)
            refreshResult: Either[CalendlyDisconnectionError, Option[CalendlyAccessTokens]] <- tokensOpt match {
                case None =>
                    Future.successful(Left(CalendlyDisconnectionError.IntegrationNotFoundError(message = s"No Calendly integration found for team ${teamId.id}")))

                case Some(tokens: CalendlyAccessTokens) =>
                    // For now, we'll refresh proactively since we don't have expiry checking logic
                    // In a production system, you'd check the expiry timestamp here
                    calendlyOAuth.refreshAccessToken(refreshToken = tokens.refreshToken)
                      .flatMap {
                          case Right(newTokenData) =>
                              // Update database with new tokens
                              Future.fromTry(teamDAO.updateOAuthTokensForCalendly(teamId = teamId, tokenData = newTokenData))
                                .map { _ =>
                                    logger.info(s"Successfully refreshed Calendly tokens for team ${teamId.id}")
                                    Right(Some(CalendlyAccessTokens(
                                        accessToken = newTokenData.access_token,
                                        refreshToken = newTokenData.refresh_token,
                                        userUrl = newTokenData.user_uri,
                                        organizationUrl = newTokenData.organization_uri,
                                        expiresAt = newTokenData.access_token_expires_at
                                    )))
                                }
                                .recover { case exception =>
                                    logger.error(s"Failed to update refreshed tokens for team ${teamId.id}", err = exception)
                                    Left(CalendlyDisconnectionError.DatabaseError(
                                        message = "Failed to update refreshed tokens",
                                        error = Some(exception)
                                    ))
                                }

                          case Left(error) =>
                              logger.error(s"Failed to refresh Calendly tokens for team ${teamId.id}: $error")
                              Future.successful(Left(CalendlyDisconnectionError.TokenRetrievalError(message = s"Token refresh failed: $error")))
                      }
            }

        } yield refreshResult
    }
}
