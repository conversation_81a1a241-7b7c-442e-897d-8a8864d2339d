package api.campaigns.services

import org.apache.pekko.stream.Materializer
import api.accounts.{TeamId, TeamMember}
import api.campaigns.{Campaign, CampaignStepDAO, CampaignStepDAOService, CampaignStepVariant, CampaignStepVariantCreateOrUpdate, CampaignStepVariantDAO, CampaignStepVariantForScheduling, CampaignStepWithChildren}
import api.campaigns.models.{CampaignStepData, CampaignStepId, CreateCampaignFirstStepError, CreateCampaignStepVariantError, UpdateVariantError, ValidateCampaignTemplateError, ValidateStepData}
import api.campaigns.services.CampaignStepService.doesStepVariantsContainPrevSubMergeTag
import api.campaigns.{Campaign, CampaignStepDAO, CampaignStepDAOService, CampaignStepVariant, CampaignStepVariantCreateOrUpdate, CampaignStepVariantDAO, CampaignStepWithChildren}
import api.templates.TemplateUtils
import org.joda.time.DateTime
import play.api.libs.json.{Js<PERSON><PERSON>, JsonValidationError}
import play.api.libs.ws.WSClient
import sr_scheduler.CampaignStatus
import utils.SRLogger
import utils.sr_product_usage_data.models.SrUserFeatureUsageEvent
import utils.sr_product_usage_data.services.SrUserFeatureUsageEventService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}














case class CampaignCreateFirstStepSuccess(
  step_id: Long
)

class CampaignStepService(
  campaignTemplateService: CampaignTemplateService,
  campaignStepDAO: CampaignStepDAO,
  campaignStepVariantDAO: CampaignStepVariantDAO,
  templateUtils: TemplateUtils,
  srUserFeatureUsageEventService: SrUserFeatureUsageEventService,
  campaignDAOService: CampaignDAOService,
  campaignStepDAOService: CampaignStepDAOService,
  campaignService: CampaignService,
) {

  def createFirstStep(
    data: CampaignStepVariantCreateOrUpdate,
    campaignId: Long,
    stepId: Long,
    campaignHeadStepId: Option[Long],
    teamId: Long,
    userId: Long,
    taId: Long
  )(
    implicit Logger: SRLogger
  ): Either[CreateCampaignFirstStepError, CampaignCreateFirstStepSuccess] = {

    if (data.template_id.isDefined && data.template_is_from_library.isEmpty) {

      Left(CreateCampaignFirstStepError.TemplateIsFromLibraryFieldMissing)

      // createVariantBadRequestError = Some("Please send if template_is_from_library")

    } else {

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = data.step_data,
        campaignId = CampaignId(id = campaignId),
        stepId = Some(stepId),
        head_step_id = campaignHeadStepId
      )

      campaignTemplateService.validateCampaignTemplate(
        validateStepData = validateStepVariantData,
        teamId = teamId,
        notes = data.notes
      ) match {

        case Left(ValidateCampaignTemplateError.BodyCantHavePreviousSubject(err)) =>

          Logger.fatal(s"campaignStepData: ${data.step_data}", err)

          Left(CreateCampaignFirstStepError.ValidateCampaignTemplateError(err, data.step_data))

        case Left(ValidateCampaignTemplateError.SubjectCantHaveSignature(err)) =>

          Logger.fatal(s"campaignStepData: ${data.step_data}", err)

          Left(CreateCampaignFirstStepError.ValidateCampaignTemplateError(err,
            campaignStepData = data.step_data
          ))

        case Left(ValidateCampaignTemplateError.PreviousSubInFirstEmail(err)) =>

          Logger.fatal(s"campaignStepData: ${data.step_data}", err)

          Left(CreateCampaignFirstStepError.ValidateCampaignTemplateError(err,
            campaignStepData = data.step_data
          ))

        case Left(ValidateCampaignTemplateError.ErrorWhileValidatingSubject(e)) =>

          Logger.fatal(s"campaignStepData: ${data.step_data}", err = e)

          Left(CreateCampaignFirstStepError.ValidateCampaignTemplateError(e = e,
            campaignStepData = data.step_data
          ))

        case Left(ValidateCampaignTemplateError.ErrorWhileValidatingBody(e)) =>

          Logger.fatal(s"campaignStepData: ${data.step_data}", err = e)

          Left(CreateCampaignFirstStepError.ValidateCampaignTemplateError(e = e,
            campaignStepData = data.step_data
          ))

        case Left(ValidateCampaignTemplateError.ErrorWhileValidatingNotes(e)) =>

          Logger.fatal(s"campaignStepData: ${data.step_data}", err = e)

          Left(CreateCampaignFirstStepError.ValidateCampaignTemplateError(e = e,
            campaignStepData = data.step_data
          ))

        case Left(ValidateCampaignTemplateError.NotesCantHavePreviousSubject(e)) =>

          Logger.fatal(s"campaignStepData: ${data.step_data}", err = e)

          Left(CreateCampaignFirstStepError.ValidateCampaignTemplateError(e = e,
            campaignStepData = data.step_data
          ))

        case Right(_) =>

          val tcheck = templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep(
            templateId = data.template_id,
            templateIsFromLibrary = data.template_is_from_library,
            ownerAccountId = userId,
            ownerTeamId = teamId,
            ownerTaId = taId,
            Logger = Logger
          )

          if (tcheck.templateCheckError.isDefined) {

            Left(CreateCampaignFirstStepError.TemplateAccessCheckError(e = tcheck.templateCheckError.get))

            // createVariantServerError = Some("Error while checking Team has access to template", tcheck.templateCheckError.get)

          } else if (tcheck.templateNotFoundError.isDefined) {

            Left(CreateCampaignFirstStepError.TemplateNotFoundError(errMsg = tcheck.templateNotFoundError.get))

            // createVariantNotFoundError = Some(tcheck.templateNotFoundError.get)

          } else {

            data.parent_id match {

              case 0 =>

                campaignStepDAO.create(
                  campaignId = campaignId,
                  headStepId = campaignHeadStepId,
                  data = data.copy(
                    template_id = tcheck.templateIdToBeSaved
                  ),
                  teamId = TeamId(teamId)
                ) match {

                  case Failure(e) =>

                    Left(CreateCampaignFirstStepError.CreateStepSQLException(e = e))
                  // createVariantServerError = Some(s"There was problem creating the step. Could you try again ? ${e.getMessage}", e)

                  case Success(None) =>

                    Left(CreateCampaignFirstStepError.CreatedStepNotFound)
                  // createVariantServerError = Some(s"There was problem creating the step. Could you try again ?", new Exception("No campaign step found after creating"))

                  case Success(Some(row)) =>

                    Right(
                      CampaignCreateFirstStepSuccess(
                        step_id = row.id
                      )
                    )
                }


              case _ =>

                // check if campaign step with id equals to parent id exists

                campaignStepDAO.find(
                  stepId = data.parent_id,
                  campaignId = campaignId
                ) match {

                  case None =>

                    Left(CreateCampaignFirstStepError.ParentStepNotFound(
                      parent_step_id = data.parent_id
                    ))
                  //createVariantInvalidParamError = Some(s"Step with parent_id: ${data.parent_id}, belonging to campaign, not found", new Exception("parent_id"))

                  case Some(_) =>

                    campaignStepDAO.create(
                      campaignId = campaignId,
                      headStepId = campaignHeadStepId,
                      data = data.copy(template_id = tcheck.templateIdToBeSaved),
                      teamId = TeamId(teamId)
                    ) match {

                      case Failure(e) =>
                        Left(CreateCampaignFirstStepError.CreateStepSQLException(
                          e = e
                        ))
                      //createVariantServerError = Some(s"Error while creating campaign step: ${e.getMessage}", e)

                      case Success(None) =>
                        Left(CreateCampaignFirstStepError.CreatedStepNotFound)
                      //createVariantServerError = Some(s"Error while creating campaign step", new Exception)

                      case Success(Some(row)) =>
                        Right(
                          CampaignCreateFirstStepSuccess(
                            step_id = row.id
                          )
                        )

                    }
                }
            }
          }
      }
    }


  }

  def reorderCampaignSteps(
    campaignId: CampaignId,
    teamId: TeamId,
    stepIdToBeReordered: CampaignStepId,
    newParentStepId: CampaignStepId,
  )(
    implicit logger: SRLogger
  ): Try[Either[String, List[CampaignStepWithChildren]]] = {

    // To make sure that we have the latest status of the campaign.
    campaignService.findCampaignForCampaignUtilsOnly(
      id = campaignId.id,
      teamId = teamId
    ) match {

      case None =>

        val errMsg = "Campaign not found"

        logger.shouldNeverHappen(
          msg = s"$errMsg. campaignId: ${campaignId.id} :: teamId: ${teamId.id}"
        )

        Failure(new Exception(errMsg))

      case Some(campaign) =>

        if (campaign.status != CampaignStatus.NOT_STARTED) {

          val errMsg = "Cannot reorder campaign steps once the campaign has been started"

          logger.error(
            msg = s"$errMsg. campaignId: ${campaign.id} :: campaignStatus: ${campaign.status.toString} :: teamId: $teamId"
          )

          Success(Left(errMsg))

        } else {

          findStepsByCampaign(campaign = campaign) match {

            case None =>

              val errMsg = "No campaign steps found for reorder"

              logger.error(
                msg = s"$errMsg. campaignId: ${campaign.id} :: campaignHeadStepId: ${campaign.head_step_id} :: campaignStatus: ${campaign.status.toString} :: teamId: $teamId"
              )

              Success(Left(errMsg))

            case Some(steps) =>

              val currCampaignStepsOrder = steps.map(s => CampaignStepId(id = s.id))

              if (currCampaignStepsOrder.length <= 1) {

                val errMsg = "Not enough campaign steps for reorder"

                logger.error(
                  msg = s"$errMsg. campaignId: ${campaign.id} :: campaignHeadStepId: ${campaign.head_step_id} :: campaignStatus: ${campaign.status.toString} :: teamId: $teamId"
                )

                Success(Left(errMsg))

              } else if (
                !(currCampaignStepsOrder.contains(newParentStepId) || newParentStepId.id == 0) ||
                  !currCampaignStepsOrder.contains(stepIdToBeReordered)
              ) {

                val errMsg = "Provided campaign step ids do not belong to the campaign"

                logger.error(
                  msg = s"$errMsg. campaignId: ${campaign.id} :: campaignHeadStepId: ${campaign.head_step_id} :: stepIdToBeReordered: $stepIdToBeReordered :: newParentStepId: $newParentStepId :: teamId: $teamId"
                )

                Success(Left(errMsg))

              } else {

                val newOrder: List[CampaignStepId] = CampaignStepService.reorderCampaignSteps(
                  currCampaignStepsOrder = currCampaignStepsOrder,
                  stepIdToBeReordered = stepIdToBeReordered,
                  newParentStepId = newParentStepId,
                )

                newOrder.headOption match {

                  case None =>

                    val errMsg = "Campaign head step not found after reorder"

                    logger.shouldNeverHappen(
                      msg = s"$errMsg. campaignId: ${campaign.id} :: campaignHeadStepId: ${campaign.head_step_id} :: stepIdToBeReordered: $stepIdToBeReordered :: newParentStepId: $newParentStepId :: currOrder: $currCampaignStepsOrder :: newOrder: $newOrder :: teamId: $teamId"
                    )

                    Failure(new Exception(errMsg))

                  case Some(newCampaignHeadStepId) =>

                    val stepIdToStep = steps.groupBy(_.id)

                    val newSteps = newOrder.flatMap { sid => stepIdToStep.get(sid.id) }.flatten

                    lazy val (prevSubMergeTagsAreValid, i) = CampaignStepService.validateStepsWithPrevSubMergeTag(
                      steps = newSteps
                    )

                    val newHeadStepVariants: Seq[CampaignStepVariant] = campaignStepVariantDAO.findByStepId(
                      stepId = newCampaignHeadStepId.id
                    )

                    // check if new head step has the {{previous_subject}} merge tag.
                    val doesNewHeadStepVariantsHavePrevSubMergeTag: Boolean = doesStepVariantsContainPrevSubMergeTag(
                      stepVariantsData = newHeadStepVariants.map(_.step_data)
                    )

                    val newOrderSet: Set[CampaignStepId] = newSteps.map(s => CampaignStepId(id = s.id)).toSet

                    val currOrderSet: Set[CampaignStepId] = currCampaignStepsOrder.toSet

                    val oldHeadStep: CampaignStepWithChildren = steps.head

                    // check if old head step supports the {{previous_subject}} merge tag.
                    val doesOldHeadStepSupportsPrevSubMergeTag: Boolean =
                      CampaignTemplateService.stepTypeSupportsPrevSubjectMergeTag(
                        campaignStepType = oldHeadStep.step_type
                      )

                    val oldHeadStepVariants: Seq[CampaignStepVariant] = campaignStepVariantDAO.findByStepId(
                      stepId = oldHeadStep.id
                    )

                    if (!newOrderSet.equals(currOrderSet)) {

                      val errMsg = "Reordered step ids do not match with original step ids"

                      logger.shouldNeverHappen(
                        msg = s"$errMsg. campaignId: ${campaign.id} :: campaignHeadStepId: ${campaign.head_step_id} :: stepIdToBeReordered: $stepIdToBeReordered :: newParentStepId: $newParentStepId :: currOrder: $currCampaignStepsOrder :: newOrder: $newOrder :: teamId: $teamId"
                      )

                      Failure(new Exception(errMsg))

                    } else if (
                      (!doesNewHeadStepVariantsHavePrevSubMergeTag || !doesOldHeadStepSupportsPrevSubMergeTag) &&
                        !prevSubMergeTagsAreValid
                    ) {

                      /**
                        * We want to fix the invalid {{previous_step}} merge tags,
                        * but only if the problem is in the new head step,
                        * or else from this block we will return an error to the user
                        * asking them to manually fix the steps.
                        *
                        * So, if steps are invalid but the new head step has the
                        * {{previous_subject}} merge tag and the old head step supports
                        * the previous subject merge tag,
                        * then we can auto-fix the invalid steps by swapping
                        * the subjects of the new and old head steps,
                        * and then we will skip this block and auto-fix the steps.
                        */

                      val errMsg = s"Invalid order step at ${i + 1} does not have a previous concrete subject"

                      val invalidStep: Option[CampaignStepWithChildren] = newSteps.lift(i)

                      val invalidStepData = invalidStep.map(_.variants.map(_.step_data))

                      logger.error(
                        msg = s"$errMsg. campaignId: ${campaign.id} :: stepIdToBeReordered: $stepIdToBeReordered :: newParentStepId: $newParentStepId :: currOrder: $currCampaignStepsOrder :: newOrder: $newOrder :: invalidStepData: $invalidStepData :: teamId: $teamId"
                      )

                      Success(Left(errMsg))

                    } else {

                      if (
                        doesNewHeadStepVariantsHavePrevSubMergeTag &&
                          (newHeadStepVariants.length > 1 || oldHeadStepVariants.length > 1)
                      ) {

                        val errMsg = "Cannot reorder a step with multiple variant which has previous subject merge tag"

                        logger.error(
                          msg = s"$errMsg. campaignId: ${campaign.id} :: campaignHeadStepId: ${campaign.head_step_id} :: stepIdToBeReordered: $stepIdToBeReordered :: newParentStepId: $newParentStepId :: currOrder: $currCampaignStepsOrder :: newOrder: $newOrder :: teamId: $teamId"
                        )

                        Success(Left(errMsg))

                      } else {

                        val campaignId = CampaignId(id = campaign.id)

                        campaignStepDAOService.reorderCampaignStepsAndUpdateLabels(
                          campaignId = campaignId,
                          teamId = teamId,
                          newCampaignHeadStepId = newCampaignHeadStepId,
                          newCampaignStepsOrderTail = newOrder.tail,
                          newHeadStepVariants = newHeadStepVariants,
                          prevHeadStepVariants = oldHeadStepVariants,
                          headStepVariantsHavePrevSubMergeTag = doesNewHeadStepVariantsHavePrevSubMergeTag,
                          prevCampaignHeadStepId = CampaignStepId(id = oldHeadStep.id)
                        )
                          .flatMap { _ =>

                            campaignService.findCampaignForCampaignUtilsOnly(
                              id = campaignId.id,
                              teamId = teamId
                            ) match {

                              case None =>

                                val errMsg = "Campaign not found after reorder"

                                logger.shouldNeverHappen(
                                  msg = s"$errMsg. campaignId: ${campaignId.id} :: teamId: ${teamId.id}"
                                )

                                Failure(new Exception(errMsg))

                              case Some(campaign) =>

                                findStepsByCampaign(campaign = campaign) match {

                                  case None =>

                                    val errMsg = "No campaign steps found after reorder"

                                    logger.shouldNeverHappen(
                                      msg = s"$errMsg. campaignId: ${campaign.id} :: campaignHeadStepId: ${campaign.head_step_id} :: campaignStatus: ${campaign.status.toString} :: teamId: $teamId"
                                    )

                                    Failure(new Exception(errMsg))

                                  case Some(updatedSteps) =>

                                    Success(Right(updatedSteps))

                                }

                            }

                          }

                      }

                    }

                }

              }

          }

        }

    }

  }


  def findStepsByCampaign(
    campaign: Campaign
  ): Option[List[CampaignStepWithChildren]] = {

    val steps = campaignDAOService
      .findOrderedSteps(
        campaignId = campaign.id,
        teamId = TeamId(id = campaign.team_id)
      )

    if (steps.isEmpty) {

      None

    } else {

      Some(steps.toList)

    }

  }

  def createVariant(
                     orgId: Long,
                     data: CampaignStepVariantCreateOrUpdate,
                     teamId: Long,
                     userId: Long,
                     taId: Long,
                     stepId: Long,
                     campaignId: Long,
                     campaignHeadStepId: Option[Long]
                   )(
                     implicit Logger: SRLogger,
                     ec: ExecutionContext,
                     materializer: Materializer,
                     ws: WSClient,
                   ): Future[Either[CreateCampaignStepVariantError, CampaignStepVariant]] = {

    CampaignStepVariantValidationService.validateCampaignStepVariant(variant = data) match {
      case Left(error) =>
        Future.successful(
          Left(CreateCampaignStepVariantError.CampaignStepVariantValidationError(error))
        )

      case Right(data) =>
        val stepCheckId = if (stepId != 0) {

          Right(
            CampaignCreateFirstStepSuccess(
              step_id = stepId
            )
          )

        } else {

          createFirstStep(
            data = data,
            campaignId = campaignId,
            campaignHeadStepId = campaignHeadStepId,
            stepId = stepId,
            teamId = teamId,
            userId = userId,
            taId = taId
          ) match {
            case Left(err) => Left(err)
            case Right(result) => Right(result)
          }
        }

        stepCheckId match {

          case Left(err) =>

            Future.successful(Left(CreateCampaignStepVariantError.ErrorWhileCreateCampaignFirst(err)))

          case Right(value) =>

            campaignStepDAO.find(stepId = value.step_id, campaignId = campaignId) match {

              case None =>

                Future.successful(
                  Left(CreateCampaignStepVariantError.CampaignStepNotFound)
                )

              case Some(_) =>

                val variants = campaignStepVariantDAO.findByStepId(stepId = value.step_id)

                val maxVariants = data.step_data match {
                  case _: CampaignStepData.AutoEmailStep => 5
                  case _: CampaignStepData.ManualEmailStep => 5
                  case _ => 1
                }
                if (variants.size >= maxVariants) {
                  Future.successful(
                    Left(
                      CreateCampaignStepVariantError.MaxVariantsExceeded(
                        maxVariants = maxVariants
                      )
                    )
                  )

                } else {

                  if (data.template_id.isDefined && data.template_is_from_library.isEmpty) {

                    Future.successful(

                      Left(
                        CreateCampaignStepVariantError.TemplateIsFromLibraryFieldMissing
                      )
                    )

                  } else {

                    val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
                      campaignStepData = data.step_data,
                      campaignId = CampaignId(id = campaignId),
                      stepId = Some(stepId),
                      head_step_id = campaignHeadStepId,
                    )

                    campaignTemplateService.validateCampaignTemplate(
                      teamId = teamId,
                      validateStepData = validateStepVariantData,
                      notes = data.notes
                    ) match {

                      case Left(ValidateCampaignTemplateError.BodyCantHavePreviousSubject(err)) =>

                        Future.successful(
                          Left(CreateCampaignStepVariantError.ValidateCampaignTemplateError(err))
                        )

                      case Left(ValidateCampaignTemplateError.SubjectCantHaveSignature(err)) =>

                        Future.successful(
                          Left(CreateCampaignStepVariantError.ValidateCampaignTemplateError(err))
                        )

                      case Left(ValidateCampaignTemplateError.PreviousSubInFirstEmail(err)) =>

                        Future.successful(
                          Left(CreateCampaignStepVariantError.ValidateCampaignTemplateError(err))
                        )

                      case Left(ValidateCampaignTemplateError.ErrorWhileValidatingSubject(e)) =>

                        Future.successful(
                          Left(CreateCampaignStepVariantError.ValidateCampaignTemplateError(err = e))
                        )

                      case Left(ValidateCampaignTemplateError.ErrorWhileValidatingBody(e)) =>

                        Future.successful(
                          Left(CreateCampaignStepVariantError.ValidateCampaignTemplateError(err = e))
                        )

                      case Left(ValidateCampaignTemplateError.NotesCantHavePreviousSubject(err)) =>

                        Future.successful(
                          Left(CreateCampaignStepVariantError.ValidateCampaignTemplateError(err))
                        )

                      case Left(ValidateCampaignTemplateError.ErrorWhileValidatingNotes(err)) =>

                        Future.successful(
                          Left(CreateCampaignStepVariantError.ValidateCampaignTemplateError(err))
                        )

                      case Right(_) =>

                        val tcheck = templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep(
                          templateId = data.template_id,
                          templateIsFromLibrary = data.template_is_from_library,
                          ownerAccountId = userId,
                          ownerTeamId = teamId,
                          ownerTaId = taId,
                          Logger = Logger
                        )


                        if (tcheck.templateCheckError.isDefined) {

                          Future.successful(
                            Left(CreateCampaignStepVariantError.TemplateAccessCheckError(e = tcheck.templateCheckError.get))
                          )


                        } else if (tcheck.templateNotFoundError.isDefined) {

                          Future.successful(
                            Left(CreateCampaignStepVariantError.TemplateNotFoundError(errMsg = tcheck.templateNotFoundError.get))
                          )


                        } else {

                          campaignStepVariantDAO.create(
                            orgId = orgId,
                            stepId = value.step_id,
                            campaignId = campaignId,
                            data = data.copy(
                              template_id = tcheck.templateIdToBeSaved
                            ))
                            .map(rowOpt => {

                              if (rowOpt.isEmpty) {

                                Left(
                                  CreateCampaignStepVariantError.CreatedStepVariantNotFound
                                )

                              } else {

                                srUserFeatureUsageEventService.addFeatureUsageEvent(
                                  orgId = orgId,
                                  event = SrUserFeatureUsageEvent.AddedFirstEmailContentInCampaign(
                                    added_first_email_content_in_campaign_at = DateTime.now()
                                  )
                                ) match {
                                  case Failure(e) =>
                                    Left(CreateCampaignStepVariantError.FeatureUsageEventSaveError(e))

                                  case Success(value) =>
                                    Right(
                                      rowOpt.get
                                    )
                                }
                              }

                            })
                            .recover { case e => {
                              Left(
                                CreateCampaignStepVariantError.CreateStepVariantException(err = e)
                              )
                            }
                            }

                        }

                    }

                  }


                }
            }

        }
    }


  }

  def updateVariant(
                     orgId: Long,
                     data: CampaignStepVariantCreateOrUpdate,
                     t: TeamMember,
                     variantId: Long,
                     stepId: Long,
                     campaignId: Long,
                     campaignHeadStepId: Option[Long],
                   )(using Logger: SRLogger,
                     ec: ExecutionContext,
                     materializer: Materializer,
                     ws: WSClient
                   ): Future[Either[UpdateVariantError, CampaignStepVariant]] = {

    CampaignStepVariantValidationService.validateCampaignStepVariant(variant = data) match {
      case Left(error) =>
        Future.successful(
          Left(UpdateVariantError.CampaignStepVariantValidationError(error))
        )

      case Right(data) =>
        if (data.template_id.isDefined && data.template_is_from_library.isEmpty) {
          //Future.successful(Res.BadRequestError("Please send if template_is_from_library"))

          Future.successful(Left(UpdateVariantError.TemplateIsFromLibraryError))
        } else {

          val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
            campaignStepData = data.step_data,
            campaignId = CampaignId(id = campaignId),
            stepId = Some(stepId),
            head_step_id = campaignHeadStepId,
          )

          campaignTemplateService.validateCampaignTemplate(
            teamId = t.team_id,
            validateStepData = validateStepVariantData,
            notes = data.notes
          ) match {

            case Left(ValidateCampaignTemplateError.BodyCantHavePreviousSubject(err)) =>

              Future.successful(
                Left(UpdateVariantError.ValidateTemplateError(err))
              )

            case Left(ValidateCampaignTemplateError.SubjectCantHaveSignature(err)) =>

              Future.successful(
                Left(UpdateVariantError.ValidateTemplateError(err))
              )

            case Left(ValidateCampaignTemplateError.PreviousSubInFirstEmail(err)) =>

              Future.successful(
                Left(UpdateVariantError.ValidateTemplateError(err))
              )

            case Left(ValidateCampaignTemplateError.ErrorWhileValidatingSubject(e)) =>

              Future.successful(
                Left(UpdateVariantError.ValidateTemplateError(e = e))
              )

            case Left(ValidateCampaignTemplateError.ErrorWhileValidatingBody(e)) =>

              Future.successful(
                Left(UpdateVariantError.ValidateTemplateError(e = e))
              )

            case Left(ValidateCampaignTemplateError.NotesCantHavePreviousSubject(e)) =>

              Future.successful(
                Left(UpdateVariantError.ValidateTemplateError(e = e))
              )

            case Left(ValidateCampaignTemplateError.ErrorWhileValidatingNotes(e)) =>

              Future.successful(
                Left(UpdateVariantError.ValidateTemplateError(e = e))
              )

            case Right(_) =>

              val tcheck = templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep(
                templateId = data.template_id,
                templateIsFromLibrary = data.template_is_from_library,
                ownerAccountId = t.user_id,
                ownerTeamId = t.team_id,
                ownerTaId = t.ta_id,
                Logger = Logger
              )

              if (tcheck.templateCheckError.isDefined) {

                //Future.successful(Res.ServerError("Error while checking Team has access to template", e = tcheck.templateCheckError))

                Future.successful(Left(UpdateVariantError.ErrorWhileCheckTeamHasAccessError(
                  e = tcheck.templateCheckError
                )))

              } else if (tcheck.templateNotFoundError.isDefined) {

                //Future.successful(Res.NotFoundError(tcheck.templateNotFoundError.get))

                Future.successful(Left(UpdateVariantError.TemplateNotFoundWhileUpdateError(
                  errMsg = tcheck.templateNotFoundError.get
                )))

              } else {

                // we are updating by checking variantID, stepId and campaignId, and if have already checked 'hasCampaign'
                // so account should be owning the step

                campaignStepDAO.find(
                  stepId = stepId,
                  campaignId = campaignId
                ) match {

                  case None => Future.successful(Left(UpdateVariantError.CampaignStepNotFound))

                  case Some(campaignStep) =>

                    if (campaignStep.step_type != data.step_data.step_type) {

                      Future.successful(Left(UpdateVariantError.CannotUpdateStepType))

                    } else {

                      campaignStepVariantDAO.update(
                        orgId = orgId,
                        id = variantId,
                        stepId = stepId,
                        campaignId = campaignId,
                        data = data.copy(template_id = tcheck.templateIdToBeSaved),
                        teamId = TeamId(t.team_id)
                        ).map(rowOpt => {

                          if (rowOpt.isEmpty) {
                            //Res.NotFoundError("Step Variant not found. Could check and try again ?")

                            Left(UpdateVariantError.StepVariantNotFoundError)
                          } else {

                            Right(rowOpt.get)
                            //Res.Success(s"Step Variant details have been updated", Json.obj("campaign_step_variant" -> rowOpt.get))
                          }

                        }).recover { case e => {
                        // Res.ServerError(s"Error while updating step variant. Could you try again ? ${e.getMessage}", e = Some(e))

                        Left(UpdateVariantError.UpdateVariantServerError(
                          e = e
                        ))
                      }
                      }

                    }

                }
              }
          }

        }

    }
    }
}

object CampaignStepService {

  def validateStepsWithPrevSubMergeTag(
    steps: List[CampaignStepWithChildren]
  ): (Boolean, Int) = {

    // find the first occurrence of step variant with {{prev_sub}} merge tag

    val pos = steps.indexWhere { s =>

      doesStepVariantsContainPrevSubMergeTag(
        stepVariantsData = s.variants.map(_.step_data)
      )

    }

    // then check if it has a any previous step to rely on.

    if (pos == -1) {

      // no steps found with {{prev_sub}} merge tag

      (true, pos)

    } else {

      val (prevSteps, _) = steps.splitAt(pos)

      val isValid = prevSteps.reverse.exists { ps =>

        CampaignTemplateService.stepTypeSupportsPrevSubjectMergeTag(
          campaignStepType = ps.step_type
        )

      }

      (isValid, pos)

    }

  }


  def reorderCampaignSteps(
    currCampaignStepsOrder: List[CampaignStepId],
    stepIdToBeReordered: CampaignStepId,
    newParentStepId: CampaignStepId,
  ): List[CampaignStepId] = {

    val filteredSteps = currCampaignStepsOrder.filter(_ != stepIdToBeReordered)

    if (newParentStepId.id == 0) {

      stepIdToBeReordered +: filteredSteps

    } else {

      val splitPos = filteredSteps.indexOf(newParentStepId) + 1

      val (start, end) = filteredSteps.splitAt(splitPos)

      start ++ List(stepIdToBeReordered) ++ end

    }

  }

  /**
    * doesStepVariantsContainPrevSubMergeTag will return true,
    * only if the step supports and has the {{previous_subject}} merge tag.
    *
    * As of now only AutoEmailStep and ManualEmailStep support
    * the {{previous_subject}} merge tag.
    */
  def doesStepVariantsContainPrevSubMergeTag(
    stepVariantsData: Seq[CampaignStepData]
  ): Boolean = {

    /**
      * 04-06-2024:
      *
      * Considering in-active variants also,
      * because we don't validate the variants when re-activated.
      */

    val stepVariantSubjects: Seq[String] =
      getStepVariantSubjectsWhichSupportPrevSubMergeTag(stepVariantsData = stepVariantsData).flatten

    stepVariantSubjects.exists(CampaignTemplateService.containsPrevSubjectMergeTag)

  }

  def getStepVariantSubjectsWhichSupportPrevSubMergeTag(
    stepVariantsData: Seq[CampaignStepData]
  ): Seq[Option[String]] = {

    stepVariantsData.map { svd =>

      getStepVariantSubjectIfSupportsPrevSubMergeTag(
        stepVariantData = svd
      )

    }

  }


  private def getStepVariantSubjectIfSupportsPrevSubMergeTag(
    stepVariantData: CampaignStepData
  ): Option[String] = {

    stepVariantData match {

      case _: CampaignStepData.LinkedinViewProfile => None
      case _: CampaignStepData.LinkedinConnectionRequestData => None
      case _: CampaignStepData.LinkedinMessageData => None
      case _: CampaignStepData.AutoLinkedinConnectionRequest => None
      case _: CampaignStepData.AutoLinkedinMessage => None
      case _: CampaignStepData.AutoLinkedinViewProfile => None
      case _: CampaignStepData.GeneralTaskData => None
      case _: CampaignStepData.WhatsappMessageData => None
      case _: CampaignStepData.SmsMessageData => None
      case _: CampaignStepData.CallTaskData => None
      case _: CampaignStepData.LinkedinInmailData => None
      case _: CampaignStepData.AutoLinkedinInmail => None
      case _: CampaignStepData.MoveToAnotherCampaignStepData => None


      case data: CampaignStepData.AutoEmailMagicContentStep =>

        // FIXME: AiHyperPersonalizationService Verify this

        None

      case data: CampaignStepData.ManualEmailMagicContentStep =>

        // FIXME: AiHyperPersonalizationService Verify this

        None

      case aes: CampaignStepData.AutoEmailStep =>

        Some(aes.subject)

      case mes: CampaignStepData.ManualEmailStep =>

        Some(mes.subject)

    }

  }

}
