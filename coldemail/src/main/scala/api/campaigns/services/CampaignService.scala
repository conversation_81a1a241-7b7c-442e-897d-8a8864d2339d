package api.campaigns.services

import org.apache.pekko.stream.Materializer
import api.APIErrorResponse.{ErrorResponseProspectsAssignApi, ErrorResponseProspectsUnAssignApi}
import api.{AppConfig, CONSTANTS}
import api.accounts.{Account, AccountService, OrganizationWithCurrentData, PermType, PermissionMethods, TeamId, TeamMember}
import api.campaigns.{CPTuple, Campaign, CampaignAnalysisResults, CampaignBasicDetails, CampaignBasicInfo, CampaignCreateForm, CampaignDAO, CampaignEditedPreviewEmail, CampaignEditedPreviewEmailDAO, CampaignEditedPreviewStep, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignEmailSettingsV2, CampaignFollowUpSetting, CampaignForValidation, CampaignIdAndTeamId, CampaignJustIdName, CampaignJustIdNameV2, CampaignOptOutSettings, CampaignOtherSettings, CampaignProspectDAO, CampaignSendReportsDAO, CampaignSettings, CampaignStep, CampaignStepDAO, CampaignStepDAOService, CampaignStepVariantCreateOrUpdate, CampaignStepVariantDAO, CampaignStepVariantForScheduling, CampaignStepWithChildren, CampaignUpdateNameForm, CampaignWithStatsAndEmail, ChannelSettingUuid, IsUpdate, MaxEmailsPerDay, PreviousFollowUp, ProspectListForPreview, StartCampaignWarmup, StuckCampaign, UnsentProspectsCounts, UpdateCampaignScheduleSettings, models}
import api.accounts.{Account, AccountService, OrganizationWithCurrentData}
import io.sr.billing_common.models.{PlanID, PlanType}
import api.columns.InternalMergeTagValuesForProspect
import api.emails.{EmailScheduledDAO, EmailSetting, EmailSettingDAO}
import api.prospects.{InferredQueryTimeline, ProspectUpdateCategoryTemp}
import utils.{AppConfigUtil, Helpers, ParseUtils, SRLogger, SrLoggerTrait, StringUtils}
import play.api.libs.json.{JsObject, JsResult, JsString, JsValue, Json, OFormat, OWrites, Reads, Writes}
import play.api.mvc.{Result, Results}
import Results.Ok
import api.accounts.EmailScheduledIdOrTaskId.EmailScheduledId
import utils.email.{EmailHelper, EmailOptionsForGetBodies, EmailsScheduledDeleteService}
import utils.templating.TemplateService

import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Random, Success, Try}
import api.accounts.models.{FindCampaignIdsBy, OrgId}
import api.accounts.service.ResetUserCacheUtil
import api.campaigns.models.{CampaignAISequenceStatus, CampaignAnalysisFor, CampaignEmailSettingsId, CampaignInboxSettingData, CampaignSetNextToBeScheduledAtData, CampaignSetNextToBeScheduledAtDataForSettingChanges, CampaignStepData, CampaignStepType, CampaignToCheckForSendingLimitNewFlow, CampaignType, CampaignTypeData, ChannelStepType, DripCampaignForm, IgnoreProspectsInOtherCampaigns, InactiveCampaignCheckType, InternalSchedulerRunLog, InternalSchedulerRunLogReporting, LowCampaignSendingVolumeReason, PreviewData, PreviousFollowUpData, SenderDataForSendingLimitNewFlow, SenderRotationStats, SubjectAndBody, VoicemailId, VoicemailUuid, WillResumeAtUpdatedBy}
import sr_scheduler.models.{CampaignAIGenerationContext, CampaignDataToAddNextToBeScheduledAt, CampaignEmailPriority, CampaignForScheduling, CampaignWarmupSetting, ChannelData, ChannelType, ScheduledProspectsCountForCampaignEmail}
import api.emails.services.{EmailScheduledService, EmailSettingJedisService, SelectAndPublishForDeletionService}
import api.prospects.dao_service.{ProspectDAOService, ProspectDAOServiceV2}
import api.prospects.models.{ProspectCategory, ProspectCategoryId, ProspectCategoryUpdateFlow, ProspectId}
import api.reports.AllCampaignStats
import api.search.SearchQuery
import utils.mq.channel_scheduler.{ChannelSchedulerService, MqIndependentStepSchedulerMsg, SchedulerMapStepIdAndDelay}
import api.spammonitor.dao.{EmailSendingStatusDAO, UpdateEmailSendingStatusForm}
import api.spammonitor.model.{EmailSendingEntityTypeData, SendEmailStatusData}
import api.tags.models.CampaignTag
import api.tasks.services.TaskService
import api.team_inbox.dao_service.ReplySentimentDAOService
import api.team_inbox.model.ReplySentimentType
import api.team_inbox.service.{ReplySentimentService, ReplySentimentUuid}
import api.triggers.{Trigger, TriggerInDB}
import eventframework.{ConversationObject, ProspectObject}
import eventframework.ConversationObject.ConversationCampaignAndProspect
import io.smartreach.esp.api.emails.EmailSettingId
import io.smartreach.esp.utils.email.EmailHelperCommon
import org.joda.time.{DateTime, DateTimeZone, Duration, Seconds}
import sr_scheduler.CampaignStatus
import utils.helpers.LogHelpers
import utils.sr_product_usage_data.models.SrUserFeatureUsageEvent
import utils.sr_product_usage_data.services.SrUserFeatureUsageEventService
import utils.timezones.TimezoneUtils
import scalikejdbc.*
import sr_scheduler.models.ChannelType.getChannelName
import utils.dbutils.DBUtils
import utils.uuid.{SrId, SrIdentifier, SrUuid, SrUuidUtils}
import api.accounts.models.AccountId
import api.call.models.{UpdateVoicemailDetailsRequest, VoiceDropData}
import api.campaigns.DataForCampaignAssignProspects.{AssignProspectToCampaignDataV3, CampaignProspectFormUuids}
import api.campaigns.dao.{CampaignEmailSettingsDAO, CampaignSchedulingMetadataDAO, CampaignSendingVolumeLogsDAO, InternalSchedulerRunLogDAO}
import api.campaigns.models.CampaignType.Drip
import api.campaigns.models.CampaignTypeData.DripCampaignData
import api.campaigns.services.CampaignAddEmailChannelError.DbFailure
import api.emails.models.DeletionReason
import api.gpt.{CreateStepsRequest, GPTService}
import api.linkedin.models.LinkedinSessionCookieAndUserAgent
import api.prospects.InferredQueryTimeline.Range
import api.reports.models.AllCampaignStatsWithTaskCount
import api.scheduler_report.model.SchedulerIntegrityReportLog
import api.scheduler_report.{ReportData, ReportType, SchedulerIntegrityService}
import api.tasks.models.TaskCount
import play.api.libs.ws.WSClient
import scalikejdbc.interpolation.SQLSyntax
import utils.GCP.CloudStorage
import utils.cache_utils.service.DbFallbackToCacheUtil
import utils.cronjobs.InactiveCampaignData
import utils.cronjobs.sender_volume_alert.model.{CampaignSendingVolumeLogs, CampaignSendingVolumeLogsForReport}
import utils.email.models.DeleteEmailsScheduledType
import utils.email_notification.service.EmailNotificationService
import utils.emailvalidation.EmailValidationService
import utils.jodatimeutils.JodaTimeUtils
import utils.mq.channel_scheduler.channels.NextStepFinderForDrip.removeBulkFromNode
import utils.mq.{CampaignAISequenceGeneratorMsg, MQCampaignAISequenceGenerator, MqCampaignSendReportMsg}
import utils.mq.channel_scheduler.channels.{ChannelSchedulerTrait, NextStepFinderForDrip, NodeData}
import utils.mq.channel_scheduler.channels.model.CampaignRejectedForSchedulingReason
import utils.mq.delete_and_revert.MQDeletionAndRevertDataV2
import utils.mq.do_not_contact.MQDoNotContactMessage
import utils.mq.webhook.mq_activity_trigger.{MQActivityTriggerMsgForm, MQActivityTriggerPublisher}
import utils.uuid.services.SrUuidService
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService
import api.sr_audit_logs.models.{EventType, ProspectIdWithOldProspectDeduplicationColumn}
import api.emails.models.EmailTrackingApiRequestData
import utils.email.{DecryptedUnsubLinkV2, EmailHelper}

import java.io.File
import scala.concurrent.duration.DurationInt
import play.api.libs.Files.TemporaryFile
import play.api.mvc.MultipartFormData

case class ChannelSettingSenderDetails(
                                        channel_type: ChannelType,
                                        channel_setting_uuid: ChannelSettingUuid,
                                        team_id: TeamId,
                                        owner_account_id: AccountData.AccountId,
                                        email: Option[String],
                                        phone_number: Option[String],
                                        sender_name: String,
                                        first_name: String,
                                        last_name: String,
                                      )

case class LinkedinSettingSenderDetails(
                                         channel_setting_uuid: ChannelSettingUuid,
                                         team_id: TeamId,
                                         email: String,
                                         first_name: String,
                                         last_name: String,
                                         linkedin_profile_url: Option[String],
                                         automation_enabled: Boolean
                                       )

object LinkedinSettingSenderDetails {

  implicit val reads: Reads[LinkedinSettingSenderDetails] = Json.reads[LinkedinSettingSenderDetails]
  implicit val writes: OWrites[LinkedinSettingSenderDetails] = Json.writes[LinkedinSettingSenderDetails]

}

case class WhatsappSettingSenderDetails(
                                         channel_setting_uuid: ChannelSettingUuid,
                                         team_id: TeamId,
                                         phone_number: String,
                                         first_name: String,
                                         last_name: String,
                                       )

object WhatsappSettingSenderDetails {

  implicit val reads: Reads[WhatsappSettingSenderDetails] = Json.reads[WhatsappSettingSenderDetails]
  implicit val writes: OWrites[WhatsappSettingSenderDetails] = Json.writes[WhatsappSettingSenderDetails]

}

case class CallSettingSenderDetails(
                                     channel_setting_uuid: ChannelSettingUuid,
                                     team_id: TeamId,
                                     phone_number: Option[String],
                                     first_name: String,
                                     last_name: String,
                                   )

object CallSettingSenderDetails {

  implicit val reads: Reads[CallSettingSenderDetails] = Json.reads[CallSettingSenderDetails]
  implicit val writes: OWrites[CallSettingSenderDetails] = Json.writes[CallSettingSenderDetails]

}

case class SmsSettingSenderDetails(
                                    channel_setting_uuid: ChannelSettingUuid,
                                    team_id: TeamId,
                                    phone_number: String,
                                    first_name: String,
                                    last_name: String,
                                  )

object SmsSettingSenderDetails {

  implicit val reads: Reads[SmsSettingSenderDetails] = Json.reads[SmsSettingSenderDetails]
  implicit val writes: OWrites[SmsSettingSenderDetails] = Json.writes[SmsSettingSenderDetails]

}

case class ChannelSettingData(
                               channel_type: ChannelType,
                               channel_setting_uuid: ChannelSettingUuid,
                               team_id: TeamId,
                               campaign_id: CampaignId
                             )

case class ValidAndInvalidCampaignUuidIdList(
                                              invalid_uuids: List[CampaignUuid],
                                              valid_campaigns_ids: Map[CampaignUuid, Option[CampaignId]]
                                            )

object ChannelSettingData {

  import utils.sr_json_utils.ChannelTypeJsonUtil.jsonFormatChannelType

  implicit val reads: Reads[ChannelSettingData] = Json.reads[ChannelSettingData]

  implicit val writes: Writes[ChannelSettingData] = new Writes[ChannelSettingData] {
    def writes(c: ChannelSettingData) = Json.obj(
      "team_id" -> c.team_id,
      "campaign_id" -> c.campaign_id,
      "channel_type" -> c.channel_type,
      "channel_setting_uuid" -> c.channel_setting_uuid,
    )
  }

}

case class NavigationLinksResponse(
                                    next: Option[String],
                                  )

object NavigationLinksResponse {
  implicit val writes: Writes[NavigationLinksResponse] = new Writes[NavigationLinksResponse] {
    def writes(data: NavigationLinksResponse) = {

      Json.obj(
        "next" -> data.next,
      )
    }
  }
  implicit val reads: Reads[NavigationLinksResponse] = Json.reads[NavigationLinksResponse]
}


case class VoicemailSaveData(
                              file: TemporaryFile, // Use TemporaryFile to handle file uploads
                              filename: String,
                              fileType: String,
                              campaign_id: CampaignId
                            )

case class VoicemailDeleteData(

                                name: String,
                                uuid: VoicemailUuid,
                                url: String,
                                id: VoicemailId
                              )



sealed trait UpdateMaxEmailsPerDayError

object UpdateMaxEmailsPerDayError {
  case class ValidationFailed(err: Throwable) extends UpdateMaxEmailsPerDayError

  case class ErrorWhileUpdation(err: Throwable) extends UpdateMaxEmailsPerDayError

  case object NoneReturnedWhileUpdate extends UpdateMaxEmailsPerDayError
}

sealed trait PreviewGetStepsForProspectError

object PreviewGetStepsForProspectError {

  case object NoStepInCampaign extends PreviewGetStepsForProspectError

  case object ProspectNotFoundInCampaign extends PreviewGetStepsForProspectError

  case class InternalServerError(err: Throwable) extends PreviewGetStepsForProspectError

  case class ErrorWhileGettingProspectObjects(err: Throwable) extends PreviewGetStepsForProspectError

  case class ValidationError(err_message: String) extends PreviewGetStepsForProspectError

}

sealed trait PreviewGetProspectError

object PreviewGetProspectError {

  case class InternalServerError(err: Throwable) extends PreviewGetProspectError

  case class ValidationError(err_message: String) extends PreviewGetProspectError
}

sealed trait GetTestStepsError

object GetTestStepsError {
  case class ErrorWhileValidateCampaignTemplate(err: ValidateCampaignTemplateError) extends GetTestStepsError

  case object InvalidEmailTemplate extends GetTestStepsError

  case object ReceiveReplyToEmailNotGiven extends GetTestStepsError

  case class ErrorWhileGettingEmailBody(err: Throwable) extends GetTestStepsError

  case class ErrorWhileInsertingMultipleForCampaign(err: Throwable) extends GetTestStepsError

  case class EmailNotSent(err: Option[Throwable]) extends GetTestStepsError

  case class ErrorWhileGettingProspectCategoryId(err: Throwable) extends GetTestStepsError
}

sealed trait ChannelSetupError

object ChannelSetupError {

  case object NoUuidPresentError extends ChannelSetupError

  case object ErrorWhileUpdatingInDb extends ChannelSetupError

  case object InvalidDataProvided extends ChannelSetupError

}

sealed trait CampaignAddEmailChannelError

object CampaignAddEmailChannelError {

    case object MaxEmailSettingLimitError extends CampaignAddEmailChannelError

    case class DbFailure(e:Throwable) extends CampaignAddEmailChannelError
}

sealed trait GetCampaignIdFromSrIdentifierError

object GetCampaignIdFromSrIdentifierError {
  case class DbFailure(e: Throwable) extends GetCampaignIdFromSrIdentifierError

  case class CampaignIdNotFound(err_msg: String) extends GetCampaignIdFromSrIdentifierError
}

sealed trait DuplicateCampaignError

object DuplicateCampaignError {
  case object NoStepsFoundError extends DuplicateCampaignError

  case class CreateCampaignError(err: CampaignCreationError) extends DuplicateCampaignError

  case class DbFailure(e: Throwable) extends DuplicateCampaignError

  case object CampaignWithStatsAndEmailNotFound extends DuplicateCampaignError

}

/*
 AccountData sealed trait was created in order to avoid confusion between,
 accountId and account which were required in CampaignService.getCampaignStatsById
*/
sealed trait AccountData

object AccountData {

  case class AccountObj(account: Account) extends AccountData

  case class AccountId(account_id: Long) extends AccountData
}

sealed trait CampaignPauseReason {
  def toKey(): String
}

object CampaignPauseReason {

  private val campaignLimitReached = "campaign_limit_reached"
  private val previousStepIsNotDone = "previous_step_is_not_done"
  private val allProspectsCompleted = "all_prospects_completed"
  private val prospectsHaveInvalidEmails = "prospects_have_invalid_emails"
  private val prospectsHaveMissingMergeTags = "prospects_have_missing_merge_tags"
  private val emailAccountSendLimitReached = "email_account_send_limit_reached"
  private val noProspectLeftInCurrentTimezone = "no_prospect_left_in_current_timezone"
  private val restProspectsAreInDNC = "rest_prospects_are_in_dnc"
  private val serviceProviderError = "service_provider_error"
  private val unknownReason = "unknown_reason"
  private val serverErrorOccurredWhileAnalyzing = "server_error_occurred_while_analyzing"

  case object CampaignLimitReached extends CampaignPauseReason {
    override def toKey(): String = campaignLimitReached
  }

  case object PreviousStepIsNotDone extends CampaignPauseReason {
    override def toKey(): String = previousStepIsNotDone
  }

  case object AllProspectsCompleted extends CampaignPauseReason {
    override def toKey(): String = allProspectsCompleted
  }

  case object ProspectsHaveInvalidEmails extends CampaignPauseReason {
    override def toKey(): String = prospectsHaveInvalidEmails
  }

  case object ProspectsHaveMissingMergeTags extends CampaignPauseReason {
    override def toKey(): String = prospectsHaveMissingMergeTags
  }

  case object EmailAccountSendLimitReached extends CampaignPauseReason {
    override def toKey(): String = emailAccountSendLimitReached
  }

  case object NoProspectLeftInCurrentTimezone extends CampaignPauseReason {
    override def toKey(): String = noProspectLeftInCurrentTimezone
  }

  case object RestProspectsAreInDNC extends CampaignPauseReason {
    override def toKey(): String = restProspectsAreInDNC
  }

  case object ServiceProviderError extends CampaignPauseReason {
    override def toKey(): String = serviceProviderError
  }

  case object UnknownReason extends CampaignPauseReason {
    override def toKey(): String = unknownReason
  }

  case object ServerErrorOccurredWhileAnalyzing extends CampaignPauseReason {
    override def toKey(): String = serverErrorOccurredWhileAnalyzing
  }

  def fromKey(key: String): Try[CampaignPauseReason] = Try {
    key match {
      case `campaignLimitReached` => CampaignLimitReached
      case `previousStepIsNotDone` => PreviousStepIsNotDone
      case `allProspectsCompleted` => AllProspectsCompleted
      case `prospectsHaveInvalidEmails` => ProspectsHaveInvalidEmails
      case `prospectsHaveMissingMergeTags` => ProspectsHaveMissingMergeTags
      case `emailAccountSendLimitReached` => EmailAccountSendLimitReached
      case `noProspectLeftInCurrentTimezone` => NoProspectLeftInCurrentTimezone
      case `restProspectsAreInDNC` => RestProspectsAreInDNC
      case `serviceProviderError` => ServiceProviderError
      case `unknownReason` => UnknownReason
      case `serverErrorOccurredWhileAnalyzing` => ServerErrorOccurredWhileAnalyzing
    }
  }

  implicit def writes: Writes[CampaignPauseReason] = new Writes[CampaignPauseReason] {
    override def writes(o: CampaignPauseReason): JsValue = {
      JsString(o.toKey())
    }
  }
}

case class SearchParams(
                         name: Option[String],
                         sender_email_setting: Option[String],
                         receiver_email_setting: Option[String],
                         status: Option[CampaignStatus],
                         range: InferredQueryTimeline.Range,
                         is_first: Boolean
                       )

case class NavigationTime(
                           next: Option[Long],
                         )

case class CampaignLisitingApiResult(
                                      data: Seq[CampaignBasicInfo],
                                      links: NavigationTime
                                    )

case class NextLinkData(
                         response_data: List[CampaignBasicInfo],
                         next: Option[Long]
                       )

case class CampaignPauseReasonAndCounts(
                                         campaignPauseReason: CampaignPauseReason,
                                         totalSentProspects: Int = -1,
                                         totalUnsentProspects: Int = -1,
                                         totalUnsentProspectsInDNC: Int = -1,
                                         totalUnsentProspectsInSameTz: Int = -1,
                                         total_unsent_prospects_with_invalid_emails: Int = -1,
                                         total_unsent_prospects_with_previous_task_not_done: Int = -1,
                                         total_unsent_prospects_that_are_likely_valid: Int = -1,
                                         total_unsent_prospects_with_missing_merge_tags: Int = -1,
                                       )

object CampaignPauseReasonAndCounts {
  implicit val writes: Writes[CampaignPauseReasonAndCounts] = Json.writes[CampaignPauseReasonAndCounts]
}

case class StepsForPreview( // campaignSteps for preview
                            prospect_id: Long,
                            prospect_email: Option[String],
                            prospect_name: String,
                            prospect_linkedin: Option[String],
                            prospect_phone: Option[String],
                            prospect_company: Option[String],
                            steps: Vector[CampaignEditedPreviewStep]
                          )

object StepsForPreview {
  implicit val writes: Writes[StepsForPreview] = Json.writes[StepsForPreview]
}

//case class CampaignChannelSettingData(
//                                   linkedin_uuid: Option[String],
//                                   whatsapp_uuid: Option[String]
//                                 )

//object CampaignChannelSettingData {
//  given format = Json.format[CampaignChannelSettingData]
//}

case class StepAndVariantId(
                             stepId: Long,
                             variantId: Long
                           )

object StepAndVariantId {
  implicit val reads: Reads[StepAndVariantId] = Json.reads[StepAndVariantId]
}

sealed trait CampaignCreationError

object CampaignCreationError {

  case class CampaignNameNotUniqueError(err: Throwable) extends CampaignCreationError

  case class SQLException(err: Throwable) extends CampaignCreationError

  case object CampaignNotCreated extends CampaignCreationError

  case class FeatureUsageEventSaveError(err: Throwable) extends CampaignCreationError

  case object InCorrectCampaignOwnerId extends CampaignCreationError

}

case class SenderDetails(
                          sender_number: Option[String],
                          sender_name: String,
                          sender_first_name: String,
                          sender_last_name: String,
                        )

sealed trait DeleteCampaignTasksError

object DeleteCampaignTasksError {

  case class ErrorWhileFetchingWebhooksForCampaign(e: Throwable) extends DeleteCampaignTasksError

  case object CampaignAssociatedToWebhook extends DeleteCampaignTasksError


  case class ErrorWhileSelectAndPublish(e: Throwable) extends DeleteCampaignTasksError

  case class DeleteCampaignsSqlError(e: Throwable) extends DeleteCampaignTasksError

  case object NoCampaignFoundToDelete extends DeleteCampaignTasksError

  case class DeleteAssociatedTaskSqlError(e: Throwable) extends DeleteCampaignTasksError

}


class CampaignService(
                       srRollingUpdateCoreService: SrRollingUpdateCoreService,
                       resetUserCacheUtil: ResetUserCacheUtil,
                       triggerDAO: Trigger,
                       campaignProspectDAO: CampaignProspectDAO,
                       prospectDAOService: ProspectDAOService,
                       prospectDAOServiceV2: ProspectDAOServiceV2,
                       campaignStepVariantDAO: CampaignStepVariantDAO,
                       emailScheduledDAO: EmailScheduledDAO,
                       emailSettingDAO: EmailSettingDAO,
                       channelSettingService: ChannelSettingService,
                       campaignSendReportsDAO: CampaignSendReportsDAO,
                       emailSettingJedisService: EmailSettingJedisService,
                       campaignDAO: CampaignDAO,
                       campaignSendingVolumeLogsDAO: CampaignSendingVolumeLogsDAO,
                       internalSchedulerRunLogDAO: InternalSchedulerRunLogDAO,
                       campaignDAOService: CampaignDAOService,
                       srUserFeatureUsageEventService: SrUserFeatureUsageEventService,
                       campaignCacheService: CampaignCacheService,
                       emailSendingStatusDAO: EmailSendingStatusDAO,
                       accountService: AccountService,
                       replySentimentDAOService: ReplySentimentDAOService,
                       taskService: TaskService,
                       dbUtils: DBUtils,
                       srUuidUtils: SrUuidUtils,
                       campaignStepDAO: CampaignStepDAO,
                       cloudStorage: CloudStorage,
                       campaignSchedulingMetadataDAO: CampaignSchedulingMetadataDAO,
                       srUuidService: SrUuidService,
                       schedulerIntegrityService: SchedulerIntegrityService,
                       emailNotificationService: EmailNotificationService,
                       selectAndPublishForDeletionService: SelectAndPublishForDeletionService,
                       campaignStepDaoService: CampaignStepDAOService,
                       emailsScheduledDeleteService: EmailsScheduledDeleteService,
                       emailScheduledService: EmailScheduledService,
                       prospectUpdateCategoryTemp: ProspectUpdateCategoryTemp,
                       mqActivityTriggerPublisher: MQActivityTriggerPublisher
                       //  prospectService: ProspectService,
                       //  campaignProspectAssign: CampaignProspectAssign,
                       //  srUuidService: SrUuidService

                     ) {


  private def reportInconsistentCampaignDataForWrongStepId(reportId: Long, reportType: ReportType)(
    using logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext
  ) = {
    campaignDAO.getCampaignIdWhichHaveInConsistentData match {
      case Failure(exception) =>
        logger.fatal("[DataIntegrityCheckCron] Db failure", err = exception)

        Failure(exception)
      case Success(campaignsFound) =>
        if (campaignsFound.isEmpty) {
          schedulerIntegrityService.updateDataIntegrityCheckQueueStatus(
            reportData = None,
            reportId = reportId
          ) match {
            case Failure(exception) =>
              logger.fatal("CampaignService reportInconsistentCampaignDataForWrongStepId  Failed to update IntegrityCheck Queue status", err = exception)
              Failure(exception)
            case Success(value) =>
              logger.info(s"CampaignService reportInconsistentCampaignDataForWrongStepId  No Inconsistent data for ${reportType.toString}")
              Success(false)
          }
        } else {

          for {
            campaignList: List[CampaignBasicDetails] <-
              Try {
                campaignsFound.map(campaign => {
                  campaignDAO.findBasicDetails(id = campaign.campaign_id, teamId = TeamId(id = campaign.team_id)) match {
                    case Failure(exception) => {
                      logger.fatal("CampaignService reportInconsistentCampaignData :: DB Failure", err = exception)
                      None
                    }
                    case Success(campaignFound) =>
                      campaignFound
                  }
                }).filter(_.isDefined).map(_.get)
              }



            _: Int <- schedulerIntegrityService.updateDataIntegrityCheckQueueStatus(
              reportData = Some(ReportData.WrongStepIdData(
                message = "Missing Current Step Id for a Campaign which has already Sent Email",
                report_type = ReportType.WRONG_STEP_ID,
                campaign_id = campaignList.map(basicDetails => CampaignId(id = basicDetails.id)),
                team_id = campaignList.map(basicDetails => TeamId(id = basicDetails.team_id))
              )),
              reportId = reportId
            )

            _ <- emailNotificationService.sendMailFromAdmin(
              toEmail = "<EMAIL>",
              toName = Some("SmartReach Support"),

              ccEmail = Some("<EMAIL>, <EMAIL>, <EMAIL>"),

              subject = s"Campaigns found with Inconsistent data ",
              body = campaignList.map(campaign => {
                s"""
                   <br/>CampaignName: ${campaign.name}
                   <br/>Camapign Id: ${campaign.id}
                   <br/>Campaign Team Id : ${campaign.team_id}

                    """.stripMargin
              }).mkString("<br/> <br/>")

            )(wsClient = ws, ec = ec, logger = logger)

          } yield (true)

        }
    }
  }

  private def reportStoppedOrArchivedCampaignWithDueTasks(reportId: Long, reportType: ReportType)(
    using logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext
  ) = {
    campaignDAO.getDueTasksForStoppedOrArchivedCampaign match {
      case Failure(exception) =>
        logger.fatal("[DataIntegrityCheckCron] Db failure", err = exception)
        Failure(exception)
      case Success(dueTaskPendingData) =>

        if (dueTaskPendingData.isEmpty) {
          logger.info("CampaignService reportStoppedOrArchivedCampaignWithDueTasks :: No Stopped/archived campaign found with due tasks")
          schedulerIntegrityService.updateDataIntegrityCheckQueueStatus(
            reportData = None,
            reportId = reportId
          ) match {
            case Failure(exception) =>
              logger.fatal("CampaignService reportStoppedOrArchivedCampaignWithDueTasks  Failed to update IntegrityCheck Queue status", err = exception)
              Failure(exception)
            case Success(value) =>
              logger.info(s"CampaignService reportStoppedOrArchivedCampaignWithDueTasks  No Inconsistent data for ${reportType.toString}")
              Success(false)
          }

        } else {
          for {

            _ <- schedulerIntegrityService.updateDataIntegrityCheckQueueStatus(
              reportData = Some(ReportData.CampaignStoppedOrArchivedButDueTaskPendingData(
                message = "Campaign is Stopped Or Archived but due tasks still pending",
                report_type = ReportType.CAMPAIGN_STOPPED_OR_ARCHIVED_BUT_DUE_TASK_PENDING,
                campaign_id = CampaignId(id = dueTaskPendingData.get.campaignIdAndTeamId.campaign_id),
                team_id = TeamId(id = dueTaskPendingData.get.campaignIdAndTeamId.team_id),
                tasks_pending = dueTaskPendingData.get.taskIds
              )),
              reportId = reportId
            )

            _ <- emailNotificationService.sendMailFromAdmin(
              toEmail = "<EMAIL>",
              toName = Some("SmartReach Support"),

              ccEmail = Some("<EMAIL>, <EMAIL>, <EMAIL>"),

              subject = s"Campaigns found with Inconsistent data : Due Tasks Found for stopped/archived campaign ",
              body = {
                s"""
                       <br/>Camapign Id: ${dueTaskPendingData.get.campaignIdAndTeamId.campaign_id}
                       <br/>Campaign Team Id :  ${dueTaskPendingData.get.campaignIdAndTeamId.team_id}
                       <br/>Due Tasks: ${dueTaskPendingData.get.taskIds}

                        """.stripMargin
              }

            )

          } yield (true)

        }
    }


  }

  private def reportOptedOutButEmailExistsCampaigns(reportId: Long, reportType: ReportType)(
    using logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext
  ) = {
    campaignDAO.getCampaignsWhoseProspectHaveOptedOutButEmailsArePresent match {
      case Failure(exception) =>
        logger.fatal("[DataIntegrityCheckCron] Db failure", err = exception)

        Failure(exception)
      case Success(scheduledOptedOutProspectData) =>
        if (scheduledOptedOutProspectData.isEmpty) {
          schedulerIntegrityService.updateDataIntegrityCheckQueueStatus(
            reportData = None,
            reportId = reportId
          ) match {
            case Failure(exception) =>
              logger.fatal("CampaignService reportOptedOutButEmailExistsCampaigns  Failed to update IntegrityCheck Queue status", err = exception)
              Failure(exception)
            case Success(value) =>
              logger.info(s"CampaignService reportOptedOutButEmailExistsCampaigns  No Inconsistent data for ${reportType.toString}")
              Success(false)
          }
        } else {

          for {


            _: Int <- schedulerIntegrityService.updateDataIntegrityCheckQueueStatus(
              reportData = Some(ReportData.ProspectOptedOutButScheduledEmailPendingData(
                message = "Prospect Has Opted Out from campaign but still  unsent email Scheduled Present",
                report_type = ReportType.PROSPECT_OPTED_OUT_BUT_SCHEDULE_EMAIL_PENDONG,
                campaign_id = CampaignId(id = scheduledOptedOutProspectData.get.campaignIdAndTeamId.campaign_id),
                team_id = TeamId(id = scheduledOptedOutProspectData.get.campaignIdAndTeamId.team_id),
                prospect_id_list = scheduledOptedOutProspectData.get.prospect_ids_list
              )),
              reportId = reportId
            )

            _ <- emailNotificationService.sendMailFromAdmin(
              toEmail = "<EMAIL>",
              toName = Some("SmartReach Support"),

              ccEmail = Some("<EMAIL>, <EMAIL>, <EMAIL>"),

              subject = s"Campaigns found with Inconsistent data - Prospect Opted Out But Unsent Email Scheduled still present ",
              body =
                s"""
                   <br/>Camapign Id: ${scheduledOptedOutProspectData.get.campaignIdAndTeamId.campaign_id}
                   <br/>Campaign Team Id : ${scheduledOptedOutProspectData.get.campaignIdAndTeamId.team_id}
                   <br/>Prospect Causing this issues: ${scheduledOptedOutProspectData.get.prospect_ids_list.toString()}

                    """.stripMargin


            )

          } yield (true)

        }
    }

  }

  private def reportOptedOutButDueTaskExistsCampaigns(reportId: Long, reportType: ReportType)(
    using logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext
  ) = {
    campaignDAO.getCampaignsWhoseProspectHaveOptedOutButDueTasksArePresent match {
      case Failure(exception) =>
        logger.fatal("[DataIntegrityCheckCron] Db failure", err = exception)

        Failure(exception)
      case Success(scheduledOptedOutProspectData) =>
        if (scheduledOptedOutProspectData.isEmpty) {
          schedulerIntegrityService.updateDataIntegrityCheckQueueStatus(
            reportData = None,
            reportId = reportId
          ) match {
            case Failure(exception) =>
              logger.fatal("CampaignService reportOptedOutButDueTaskExistsCampaigns  Failed to update IntegrityCheck Queue status", err = exception)
              Failure(exception)
            case Success(value) =>
              logger.info(s"CampaignService reportOptedOutButDueTaskExistsCampaigns  No Inconsistent data for ${reportType.toString}")
              Success(false)
          }
        } else {

          for {

            _: Int <- schedulerIntegrityService.updateDataIntegrityCheckQueueStatus(
              reportData = Some(ReportData.ProspectOptedOutButDueTaskPendingData(
                message = "Prospect Has Opted Out from campaign but still  due task present",
                report_type = ReportType.CAMPAIGN_STOPPED_OR_ARCHIVED_BUT_DUE_TASK_PENDING,
                campaign_id = CampaignId(id = scheduledOptedOutProspectData.get.campaignIdAndTeamId.campaign_id),
                team_id = TeamId(id = scheduledOptedOutProspectData.get.campaignIdAndTeamId.team_id),
                prospect_id_list = scheduledOptedOutProspectData.get.prospect_ids_list
              )),
              reportId = reportId
            )

            _ <- emailNotificationService.sendMailFromAdmin(
              toEmail = "<EMAIL>",
              toName = Some("SmartReach Support"),

              ccEmail = Some("<EMAIL>, <EMAIL>, <EMAIL>"),

              subject = s"Campaigns found with Inconsistent data - Prospect Opted Out But Due tasks are still present ",
              body =
                s"""
                    <br/>Camapign Id: ${scheduledOptedOutProspectData.get.campaignIdAndTeamId.campaign_id}
                    <br/>Campaign Team Id : ${scheduledOptedOutProspectData.get.campaignIdAndTeamId.team_id}
                    <br/>Prospect Causing this issues: ${scheduledOptedOutProspectData.get.prospect_ids_list.toString()}

                    """.stripMargin


            )

          } yield (true)

        }
    }

  }

  private def reportCampaignsWhoseProspectStatusIsCompletedOrPausedButDueTasksArePresent(reportId: Long, reportType: ReportType)(
    using logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext
  ) = {
    campaignDAO.getCampaignWhoseProspectStatusIsCompletedOrPausedButDueTaskArePresents match {
      case Failure(exception) =>
        logger.fatal("[DataIntegrityCheckCron] Db failure", err = exception)

        Failure(exception)
      case Success(scheduledDueProspectData) =>
        if (scheduledDueProspectData.isEmpty) {
          schedulerIntegrityService.updateDataIntegrityCheckQueueStatus(
            reportData = None,
            reportId = reportId
          ) match {
            case Failure(exception) =>
              logger.fatal("CampaignService reportCampaignsWhoseProspectStatusIsCompletedOrPausedButDueTasksArePresent  Failed to update IntegrityCheck Queue status", err = exception)
              Failure(exception)
            case Success(value) =>
              logger.info(s"CampaignService reportCampaignsWhoseProspectStatusIsCompletedOrPausedButDueTasksArePresent  No Inconsistent data for ${reportType.toString}")
              Success(false)
          }
        } else {

          for {

            _: Int <- schedulerIntegrityService.updateDataIntegrityCheckQueueStatus(
              reportData = Some(ReportData.CampaignProspectStatusCompletedOrPausedButDueTaskPresentData(
                message = "Campaign Prospect Status is Completed/Paused but still  due task present",
                report_type = ReportType.CAMPAIGN_PROSPECT_STATUS_COMPLETED_OR_PAUSED_BUT_DUE_TASK_PRESENT,
                campaign_id = CampaignId(id = scheduledDueProspectData.get.campaignIdAndTeamId.campaign_id),
                team_id = TeamId(id = scheduledDueProspectData.get.campaignIdAndTeamId.team_id),
                prospect_id_list = scheduledDueProspectData.get.prospect_ids_list
              )),
              reportId = reportId
            )

            _ <- emailNotificationService.sendMailFromAdmin(
              toEmail = "<EMAIL>",
              toName = Some("SmartReach Support"),

              ccEmail = Some("<EMAIL>, <EMAIL>, <EMAIL>"),

              subject = s"Campaigns found with Inconsistent data - Prospect Status Completed or Paused But Due tasks are still present ",
              body =
                s"""
                    <br/>Camapign Id: ${scheduledDueProspectData.get.campaignIdAndTeamId.campaign_id}
                    <br/>Campaign Team Id : ${scheduledDueProspectData.get.campaignIdAndTeamId.team_id}
                    <br/>Prospect Causing this issues: ${scheduledDueProspectData.get.prospect_ids_list.toString()}

                    """.stripMargin


            )

          } yield (true)

        }
    }

  }


  private def reportCampaignWhoseProspectStatusIsCompletedOrPausedButEmailsArePresent(reportId: Long, reportType: ReportType)(
    using logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext
  ) = {
    campaignDAO.getCampaignWhoseProspectStatusIsCompletedOrPausedButEmailsArePresent match {
      case Failure(exception) =>
        logger.fatal("[DataIntegrityCheckCron] Db failure", err = exception)

        Failure(exception)
      case Success(scheduledDueProspectData) =>
        if (scheduledDueProspectData.isEmpty) {
          schedulerIntegrityService.updateDataIntegrityCheckQueueStatus(
            reportData = None,
            reportId = reportId
          ) match {
            case Failure(exception) =>
              logger.fatal("CampaignService reportCampaignWhoseProspectStatusIsCompletedOrPausedButEmailsArePresent  Failed to update IntegrityCheck Queue status", err = exception)
              Failure(exception)
            case Success(value) =>
              logger.info(s"CampaignService reportCampaignWhoseProspectStatusIsCompletedOrPausedButEmailsArePresent  No Inconsistent data for ${reportType.toString}")
              Success(false)
          }
        } else {

          for {

            _: Int <- schedulerIntegrityService.updateDataIntegrityCheckQueueStatus(
              reportData = Some(ReportData.CampaignProspectStatusCompletedOrPausedButScheduledEmailPresentData(
                message = "Campaign Prospect Status is Completed/Paused but still  unsent email Scheduled  present",
                report_type = ReportType.CAMPAIGN_PROSPECT_STATUS_COMPLETED_OR_PAUSED_BUT_SCHEDULED_EMAIL_PRESENT,
                campaign_id = CampaignId(id = scheduledDueProspectData.get.campaignIdAndTeamId.campaign_id),
                team_id = TeamId(id = scheduledDueProspectData.get.campaignIdAndTeamId.team_id),
                prospect_id_list = scheduledDueProspectData.get.prospect_ids_list
              )),
              reportId = reportId
            )

            _ <- emailNotificationService.sendMailFromAdmin(
              toEmail = "<EMAIL>",
              toName = Some("SmartReach Support"),

              ccEmail = Some("<EMAIL>, <EMAIL>, <EMAIL>"),

              subject = s"Campaigns found with Inconsistent data - Prospect Status Completed or Paused But Unsent Email Scheduled still present ",
              body =
                s"""
                    <br/>Camapign Id: ${scheduledDueProspectData.get.campaignIdAndTeamId.campaign_id}
                    <br/>Campaign Team Id : ${scheduledDueProspectData.get.campaignIdAndTeamId.team_id}
                    <br/>Prospect Causing this issues: ${scheduledDueProspectData.get.prospect_ids_list.toString()}

                    """.stripMargin


            )

          } yield (true)

        }
    }

  }

  private def deleteTaskAndEmailDirectly(
                                          deletion_reason: DeletionReason,
                                          campaign_id: CampaignId,
                                          team_id: TeamId,
                                          sender_email_settings_ids: Seq[EmailSettingId],
                                          extra_log_message: Option[String]
                                        )(using Logger: SRLogger): Try[Seq[Int]] = {

    for {

      select_task_ids_emails_to_be_deleted: Seq[MQDeletionAndRevertDataV2] <- {

        val res = selectAndPublishForDeletionService.selectTaskAndEmailToBeDeletedForDeleteVariantFlow(
          deletion_reason = deletion_reason,
          deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByCampaignId(
            campaignId = campaign_id.id,
            teamId = team_id,
            senderEmailSettingIds = sender_email_settings_ids
          )
        )

        res

      }

      delete_unsent_task_and_revert: Seq[Int] <- Try {

        select_task_ids_emails_to_be_deleted.map(msg => {

          Logger.info(s"deleteTaskAndEmailDirectly msg : ${msg}, team_id : ${team_id} and campaignId : ${campaign_id} ${extra_log_message}")

          val res2 = emailsScheduledDeleteService.processMessageForDeletionAndRevert(
            msg = msg
          ) match {

            case Failure(err) =>

              Logger.fatal(s"deleteTaskAndEmailDirectly Error ${msg} , teamID: ${team_id}, campaign_id: ${campaign_id} ${extra_log_message}", err)
              0

            case Success(data) =>

              Logger.info(s" delete and revert successful in deleteVariant ${msg} , teamID: ${team_id}, campaign_id: ${campaign_id} ${extra_log_message}")
              1


          }

          res2

        })
      }

    } yield {

      delete_unsent_task_and_revert

    }

  }

  def updateCampaignContext(
                             data: CampaignAIGenerationContext,
                             campaign_id: CampaignId,
                             team_id: TeamId
                           ): Try[Int] = {
    campaignDAO.updateCampaignContext(
      data = data,
      campaign_id = campaign_id,
      team_id = team_id
    )
  }

  def reportInconsistentCampaignData(reportType: ReportType, reportId: Long)(using logger: SRLogger, ws: WSClient, ec: ExecutionContext): Boolean = {

    val reportedSuccessfully = reportType match {
      case ReportType.WRONG_STEP_ID => reportInconsistentCampaignDataForWrongStepId(reportId = reportId, reportType = reportType) match {
        case Failure(exception) =>
          logger.fatal("CampaignService reportInconsistentCampaignData for type: WrongStepId error Occurred", err = exception)
          false
        case Success(value) => value
      }

      case ReportType.CAMPAIGN_STOPPED_OR_ARCHIVED_BUT_DUE_TASK_PENDING => reportStoppedOrArchivedCampaignWithDueTasks(reportId = reportId, reportType = reportType) match {
        case Failure(exception) =>
          logger.fatal("CampaignService reportInconsistentCampaignData for type: CAMPAIGN_STOPPED_OR_ARCHIVED_BUT_DUE_TASK_PENDING error Occurred", err = exception)
          false

        case Success(value) => value
      }

      case ReportType.PROSPECT_OPTED_OUT_BUT_SCHEDULE_EMAIL_PENDONG =>
        reportOptedOutButEmailExistsCampaigns(reportId = reportId, reportType = reportType) match {
          case Failure(exception) =>
            logger.fatal("CampaignService reportInconsistentCampaignData for type: PROSPECT_OPTED_OUT_BUT_SCHEDULE_EMAIL_PENDONG error Occurred", err = exception)
            false
          case Success(value) => value
        }

      case ReportType.PROSPECT_OPTED_OUT_BUT_DUE_TASK_PENDING =>
        reportOptedOutButDueTaskExistsCampaigns(reportId = reportId, reportType = reportType) match {
          case Failure(exception) =>
            logger.fatal("CampaignService reportInconsistentCampaignData for type: PROSPECT_OPTED_OUT_BUT_DUE_TASK_PENDING error Occurred", err = exception)
            false
          case Success(value) => value
        }

      case ReportType.CAMPAIGN_PROSPECT_STATUS_COMPLETED_OR_PAUSED_BUT_SCHEDULED_EMAIL_PRESENT =>
        reportCampaignWhoseProspectStatusIsCompletedOrPausedButEmailsArePresent(reportId = reportId, reportType = reportType) match {
          case Failure(exception) =>
            logger.fatal("CampaignService reportInconsistentCampaignData for type: CAMPAIGN_PROSPECT_STATUS_COMPLETED_OR_PAUSED_BUT_SCHEDULED_EMAIL_PRESENT error Occurred", err = exception)
            false
          case Success(value) => value
        }

      case ReportType.CAMPAIGN_PROSPECT_STATUS_COMPLETED_OR_PAUSED_BUT_DUE_TASK_PRESENT =>
        reportCampaignsWhoseProspectStatusIsCompletedOrPausedButDueTasksArePresent(reportId = reportId, reportType = reportType) match {
          case Failure(exception) =>
            logger.fatal("CampaignService reportInconsistentCampaignData for type: CAMPAIGN_PROSPECT_STATUS_COMPLETED_OR_PAUSED_BUT_DUE_TASK_PRESENT error Occurred", err = exception)
            false
          case Success(value) => value
        }

      case ReportType.EMAIL_VALIDATION_APIS =>
        logger.shouldNeverHappen("CampaignService reportInconsistentCampaignData for type: EMAIL_VALIDATION_APIS is an illegal type for this method")
        false

      case ReportType.INBOX_PLACEMENT_CHECK_LOGS =>
        logger.shouldNeverHappen("CampaignService reportInconsistentCampaignData for type: INBOX_PLACEMENT_CHECK_LOGS is an illegal type for this method")
        false

      case ReportType.DOMAIN_BLACKLIST_REPORT =>
        logger.shouldNeverHappen("CampaignService reportInconsistentCampaignData for type: DOMAIN_BLACKLIST_REPORT is an illegal type for this method")
        false

      case ReportType.NEW_SIGNUP_REPORT =>
        logger.shouldNeverHappen("CampaignService reportInconsistentCampaignData for type: NEW_SIGNUP_REPORT is an illegal type for this method")
        false
    }


    reportedSuccessfully
  }

  private def delete(
                      step: CampaignStep,
                      campaign: Campaign,
                      teamId: TeamId
                    )(using Logger: SRLogger): Try[Seq[Long]] = {

    campaignStepDaoService.delete(
      step = step,
      campaign = campaign,
      teamId = teamId
    ).flatMap { _ =>

      /*
      23-april-2024:

        not sure why this redundant operation is present here. but, for now I feel it won't cause any issue

       */

      selectAndPublishForDeletionService.selectAndPublishForDeletion(
        deletion_reason = DeletionReason.DeletedVariantByDelete,
        deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByCampaignId(
          campaignId = campaign.id,
          teamId = teamId,
          senderEmailSettingIds = campaign.settings.campaign_email_settings.map(_.sender_email_setting_id)
        )
      ) match {

        case Failure(e) =>
          Logger.error(s"FATAL Campaign step delete deleteUnsentByCampaignId: ${step.id} :: ${campaign.id} :: ${LogHelpers.getStackTraceAsString(e)}")

        case Success(_) =>
        // do nothing

      }

      // update the step labels; this is outside the above tx, because of transaction isolation issues
      campaignStepDAO._updateStepLabels(campaignId = campaign.id, teamId = TeamId(campaign.team_id))

    }
  }

  private def deleteV2(
                        variantId: Long,
                        stepId: Long,
                        campaignId: Long,
                        teamId: TeamId,
                        sender_email_settings_ids: Seq[EmailSettingId]
                      )(using Logger: SRLogger): Try[Seq[Long]] = {

    /*

      23-apr-2024:
      we are doing all the deletion in one go immediately after step deletion since
      doing half stuff in mq and doing half immediately was causing deadlock scenario

     */

    for {

      delete_unsent_task_and_revert: Seq[Int] <- deleteTaskAndEmailDirectly(
        deletion_reason = DeletionReason.DeletedVariantByDeleteV2,
        campaign_id = CampaignId(id = campaignId),
        team_id = teamId,
        sender_email_settings_ids = sender_email_settings_ids,
        extra_log_message = Some(s"and variant ${variantId}")
      )

      delete_variant_related_info: Seq[Long] <- campaignStepVariantDAO.deleteV2(
        variantId = variantId,
        stepId = stepId,
        campaignId = campaignId
      )


    } yield {

      Logger.info(s"deleteVariant flow total prospects reverted ${delete_unsent_task_and_revert.sum} team_id: ${teamId} campaignId ${campaignId} variantId : ${variantId} ")

      delete_variant_related_info

    }
  }
  
  def getVoicemails(
                   campaignId: CampaignId,
                   teamId: TeamId
                 )(using Logger: SRLogger): Try[List[VoiceDropData]] = {
    campaignDAO.getVoicemails(
      campaignId = campaignId, 
      teamId = teamId
    )
  }

  def updateVoicemailDetails(
                              campaignId: CampaignId,
                              data: UpdateVoicemailDetailsRequest,
                              teamId: TeamId
                         )(using Logger: SRLogger): Try[VoicemailId] = {
    campaignDAO.updateVoicemailDetails(
      campaignId = campaignId,
      data = data,
      teamId = teamId
    )
  }


  def uploadVoiceMailAndSaveInDb(
                                  campaignId: CampaignId,
                                  filename: String,
                                  teamId: TeamId,
                                  ownerId: AccountId,
                                  fileType: String,
                                  description: String,
                                  voicemail: File
                                )(implicit ec: ExecutionContext, Logger: SRLogger): Future[String] = {
    val uuid: String = srUuidUtils.generateVoicemailUuid()
    val new_file_name: String = uuid + "_" + filename
    for {
      url: String <- cloudStorage.createUploadAndGenerateURLForAudio(
        file = voicemail,
        fileType = fileType,
        filename = new_file_name
      )
      
      _: String <- Future.fromTry(campaignDAO.insertVoicemailData(
        url = url,
        uuid = uuid,
        campaignId = campaignId,
        description = description,
        fileType = fileType,
        filename = filename,
        teamId = teamId,
        ownerId = ownerId
      ))


    } yield{
      url
    }

  }


  def deleteVoicemail(
                     voicemailId: VoicemailId,
                     campaignId: CampaignId,
                     teamId: TeamId
                     )(implicit ec: ExecutionContext, Logger: SRLogger): Future[VoicemailId] = {

    for {

      data: VoicemailDeleteData <- Future.fromTry(campaignDAO.getVoicemailDeleteData(
        teamId = teamId,
        campaignId = campaignId,
        voicemailId = voicemailId
      ))

      name: String <- Future.successful(
        CampaignService.extractFilenameFromUrlVoicemail(url = data.url)
      )

      _: Boolean <- cloudStorage.deleteForVoicemail(
        filename = name
      )

      res: Int <- Future.fromTry(campaignDAO.updateVoicemailActiveStatus(

        teamId = teamId,
        is_active = false,
        campaignId = campaignId,
        voicemailId = voicemailId
      ))


    } yield {
      voicemailId
    }

  }



  // on autoreply / OOF reply, users may want to manually reschedule the campaign to start later
  def setProspectsToResumeLater(

                                 willResumeAt: DateTime,
                                 willResumeAtTimezone: String,
                                 campaignProspects: Seq[CPTuple],
                                 permittedAccountIdsForProspects: Seq[Long],
                                 willResumeAtUpdatedBy: WillResumeAtUpdatedBy,
                                 teamId: Long,
                                 logger: SRLogger

                               ): Try[List[Long]] = {


    /**
     * mark CampaignProspect as "not completed", and set will_resume_at timestamp
     */

    for {

      pidsWithPermission: Set[Long] <- prospectDAOServiceV2
        .checkIfUserHasPermissionForProspects(
          prospectsIds = campaignProspects.map(_.prospect_id).distinct,
          permittedAccountIds = permittedAccountIdsForProspects,
          teamId = teamId
        )
        .map(_.toSet)

      updatedStatusForProspectIds: List[Long] <- campaignProspectDAO
        .setCampaignToResumeForProspectsAtLaterDate(
          campaignProspects = campaignProspects
            .filter(cp => pidsWithPermission.contains(cp.prospect_id)),
          willResumeAt = willResumeAt,
          willResumeAtTimezone = willResumeAtTimezone,
          willResumeAtUpdatedBy = willResumeAtUpdatedBy,
          logger = logger
        )

    } yield {
      updatedStatusForProspectIds
    }

  }


  def fetchStuckCampaigns(is_new_flow: Boolean): Try[List[StuckCampaign]] = campaignDAO.fetchStuckCampaigns(is_new_flow = is_new_flow)


  def fetchRunningCampaignsDataForLowSending(
                                              mqCampaignSendReportMsg: MqCampaignSendReportMsg
                                            ): Try[CampaignToCheckForSendingLimitNewFlow] = campaignDAO.fetchRunningCampaignsDataForLowSending(mqCampaignSendReportMsg = mqCampaignSendReportMsg)

  def updateValuesForPushingToAnalysisQueue(
                                             campaignIds: List[Long]
                                           ): Try[List[CampaignIdAndTeamId]] =
    campaignDAO.updateValuesForPushingToAnalysisQueue(
      campaignIds = campaignIds
    )

  def restartCampaignForProspectsSetToResumeLater(
                                                   logger: SRLogger
                                                 ): Try[List[CPTuple]] = {

    for {
      campaignProspects: List[CPTuple] <- campaignProspectDAO.findProspectsWhichWereSetToResumeLater(
        logger = logger
      )

      _: List[Long] <- campaignProspectDAO.restartCampaignForProspectsSetToResumeLater(
        campaignProspects = campaignProspects,
        logger = logger
      )
    } yield {
      campaignProspects
    }

  }

  def findCampaignForSchedulingTasks(channelStepType: ChannelStepType, channelSettingUuid: String, Logger: SrLoggerTrait): Try[Seq[CampaignForScheduling]] = {

    campaignDAO.findCampaignsForSchedulingTasks(channelStepType = channelStepType, channelSettingUuid = channelSettingUuid, Logger = Logger)

  }

  def checkCampaignSchedulingMetadata(
                                       campaignIdAndTeamId: CampaignIdAndTeamId
                                     )(using Logger: SRLogger): Try[Int] = {
    for {
      stepTypesInCampaign: Set[CampaignStepType] <- Try {

        campaignDAOService.findOrderedSteps(
            campaignId = campaignIdAndTeamId.campaign_id,
            teamId = TeamId(id = campaignIdAndTeamId.team_id)
          )
          .map(_.step_type)
          .toSet

      }

      prospects_remaining_to_be_scheduled_exists: Boolean <- getInactiveCampaignsForStopping(
        inactiveCampaignCheckType = InactiveCampaignCheckType.OnHoldCheck,
        campaignIdAndTeamId = campaignIdAndTeamId,
        stepTypesInCampaign = stepTypesInCampaign
      ).map(_.isEmpty) //if we find a prospect that can be scheduled for a given campaign, we filter it out in the query, so if this is empty, we need to mark it as running
      updated: Int <- campaignSchedulingMetadataDAO.updateCampaignSchedulingMetadata(
        campaignId = CampaignId(campaignIdAndTeamId.campaign_id),
        teamId = TeamId(campaignIdAndTeamId.team_id),
        prospects_remaining_to_be_scheduled_exists = prospects_remaining_to_be_scheduled_exists // when this is false, we will not pick a campaign for scheduling and it will be put on hold
      )
      new_campaign_status = if (prospects_remaining_to_be_scheduled_exists) {
        CampaignStatus.RUNNING
      } else {
        CampaignStatus.ON_HOLD
      }
      update_campaign_status <- updateStatus(
        id = campaignIdAndTeamId.campaign_id,
        teamId = TeamId(campaignIdAndTeamId.team_id),
        newStatus = new_campaign_status
      )
    } yield {
      updated
    }
  }

  def sendReportForInternalSchedulerRunLogForLast24Hours()(implicit wsClient: WSClient, ec: ExecutionContext, logger: SRLogger): Try[Unit] = {


    for {
      schedulerRunLog: List[InternalSchedulerRunLogReporting] <- getInternalSchedulerRunLogForLast24Hours

      integrityReportData: List[SchedulerIntegrityReportLog] <- schedulerIntegrityService.getReportDataForLast24Hours
      //_: Unit
      _ <- emailNotificationService.sendReportForInternalSchedulerRunLog(
        schedulerRunLog = schedulerRunLog,
        integrityReportData = integrityReportData
      )
    } yield {

    }

  }

  // SENDER_ROTATION
  // called from -
  // CampaignService.analyzeCampaignSendReports
  // InternalSupportController.diagnoseCampaign
  def getCampaignPauseReasonAndCounts(
                                       campaignId: CampaignId,
                                       teamId: TeamId
                                     )(using logger: SRLogger): CampaignPauseReasonAndCounts = {
    campaignDAO
      .findCampaignForCampaignUtilsOnly(id = campaignId.id, teamId = teamId) match {

      case None =>
        logger.error(s"Cannot Find Campaign: $campaignId in DB.")
        Success {
          CampaignPauseReasonAndCounts(
            campaignPauseReason = CampaignPauseReason.ServerErrorOccurredWhileAnalyzing
          )
        }

      case Some(campaign) =>

        if (campaign.settings.campaign_email_settings.isEmpty) {
          logger.error(s"No sender_email_settings_id found for campaign_${campaignId.id}")
          Success {
            CampaignPauseReasonAndCounts(
              campaignPauseReason = CampaignPauseReason.ServerErrorOccurredWhileAnalyzing
            )
          }
        }
        else {
          val emailSettingIds = campaign.settings.campaign_email_settings.map(_.sender_email_setting_id)

          val emailAccountSettings = emailSettingDAO.find(
            emailSettingIds,
            teamId
          )
          if (emailAccountSettings.isEmpty) {

            logger.error(s"Cannot Find Sender Email Account for this Campaign: $campaignId")
            Success {
              CampaignPauseReasonAndCounts(
                campaignPauseReason = CampaignPauseReason.ServerErrorOccurredWhileAnalyzing
              )
            }

          } else {

            if (emailAccountSettings.map(_.error).exists(_.isDefined)) {
              Success {
                CampaignPauseReasonAndCounts(
                  campaignPauseReason = CampaignPauseReason.ServiceProviderError
                )
              }
            }
            else {
              // Step 3 - did we hit the campaign sending limit ?
              campaignProspectDAO.getScheduledProspectsCountForCampaign(
                  Logger = logger,
                  emailSettingIds = emailSettingIds,
                  teamId = teamId.id,
                  campaignIdAndTimezone = Seq((campaignId.id, campaign.settings.timezone)),
                  countOnlySentProspects = true
                )
                .flatMap(scheduledProspectsCountForCampaignEmailSeq => {

                  val scheduledProspectsCountForCampaignEmail = scheduledProspectsCountForCampaignEmailSeq.headOption
                    .getOrElse(
                      ScheduledProspectsCountForCampaignEmail(
                        campaignId = campaignId.id,
                        newCount = 0,
                        followupCount = 0,
                        newCountNotSent = 0,
                        followupCountNotSent = 0
                      )
                    )

                  val emailsSentFromCampaign = scheduledProspectsCountForCampaignEmail.followupCount +
                    scheduledProspectsCountForCampaignEmail.newCount

                  if (emailsSentFromCampaign >= campaign.settings.max_emails_per_day) {
                    // Yes we hit the campaign sending limit.
                    Success {
                      CampaignPauseReasonAndCounts(
                        campaignPauseReason = CampaignPauseReason.CampaignLimitReached,
                        totalSentProspects = emailsSentFromCampaign
                      )
                    }
                  }

                  else {
                    // Step 4 - check the total counts sent for this email setting today
                    campaignProspectDAO.getSentOrScheduledProspectsCountForEmail(
                        emailSettingIds = emailSettingIds,
                        accountTimezone = campaign.settings.timezone,
                        countOnlySentEmails = true,
                        logger = logger,
                        teamId = teamId
                      )
                      .map(emailsSentFromAccountMap => {

                        // Step 5 - did we hit the email setting sending limit ?
                        if (emailsSentFromAccountMap.values.sum >= emailAccountSettings.map(_.quota_per_day).sum) {

                          /*
                          Yes we hit the email setting limit.
                          Step 4.1 - room for improvement
                          1 check the count of the campaign vs the count of the email setting
                          if the campaign count < than the email_setting count
                          check for other campaigns integrated into this email setting
                          check their counts and report . This is how i usually investigate
                          many times other campaigns are integrated and their starting time
                          is ahead of the starved campaigns - alerting ourselves/ customers on this
                          deflects the case early without need for developer investigation
                          todo - see if i can move the campaign limit checks before the email limit checks
                               it will search on a potentially smaller space
                               we will go for email setting check only if campaign sending limit is not violated
                               we will be able to tell if the customer is confused because campaign limit not reached
                               and can investigate email setting limit - maybe other campaigns are integrated
                               and they are consuming the limit before this one.
                          */
                          CampaignPauseReasonAndCounts(
                            campaignPauseReason = CampaignPauseReason.EmailAccountSendLimitReached
                          )
                        }

                        else {
                          prospectDAOService.getProspectCategoryId(
                            teamId = teamId,
                            text_id = ProspectCategory.DO_NOT_CONTACT,
                            account = None
                          ) match {

                            case Failure(err) =>
                              logger.error(s"Cannot Find prospectCategoryId teamId::$teamId text_id::${ProspectCategory.DO_NOT_CONTACT}", err = err)
                              // need to check with Aditya for this - checked and agreed on this
                              CampaignPauseReasonAndCounts(
                                campaignPauseReason = CampaignPauseReason.ServerErrorOccurredWhileAnalyzing
                              )

                            case Success(doNotContactCategoryId: ProspectCategoryId) =>

                              val unsentProspectsCounts = campaignProspectDAO
                                .getUnsentProspectsCounts(
                                  campaignId = campaignId,
                                  teamId = teamId,
                                  campaignTimezone = campaign.settings.timezone,
                                  DONOTCONTACT_CATEGORY_ID = doNotContactCategoryId
                                )
                                .getOrElse(
                                  UnsentProspectsCounts(
                                    total_unsent_prospects = 0,
                                    total_unsent_prospects_with_invalid_emails = 0,
                                    total_unsent_prospects_with_missing_merge_tags = 0,
                                    total_unsent_prospects_in_dnc = 0,
                                    total_unsent_prospects_with_previous_task_not_done = 0,
                                    total_unsent_prospects_that_are_likely_valid = 0,
                                    total_unsent_prospects_in_same_tz = 0
                                  )
                                )

                              val campaignPauseReasonAndCounts = CampaignPauseReasonAndCounts(
                                campaignPauseReason = CampaignPauseReason.AllProspectsCompleted,
                                totalSentProspects = emailsSentFromCampaign,
                                totalUnsentProspects = unsentProspectsCounts.total_unsent_prospects,
                                totalUnsentProspectsInDNC = unsentProspectsCounts.total_unsent_prospects_in_dnc,
                                totalUnsentProspectsInSameTz = unsentProspectsCounts.total_unsent_prospects_in_same_tz,
                                total_unsent_prospects_with_invalid_emails = unsentProspectsCounts.total_unsent_prospects_with_invalid_emails,
                                total_unsent_prospects_with_missing_merge_tags = unsentProspectsCounts.total_unsent_prospects_with_missing_merge_tags,
                              )

                              val pauseReason: CampaignPauseReasonAndCounts = if (unsentProspectsCounts.total_unsent_prospects == 0) {
                                campaignPauseReasonAndCounts
                              }
                              else if (unsentProspectsCounts.total_unsent_prospects_with_invalid_emails > 0) {
                                campaignPauseReasonAndCounts.copy(
                                  campaignPauseReason = CampaignPauseReason.ProspectsHaveInvalidEmails
                                )
                              }
                              else if (unsentProspectsCounts.total_unsent_prospects_with_missing_merge_tags > 0) {
                                campaignPauseReasonAndCounts.copy(
                                  campaignPauseReason = CampaignPauseReason.ProspectsHaveMissingMergeTags
                                )
                              }
                              else if (unsentProspectsCounts.total_unsent_prospects == unsentProspectsCounts.total_unsent_prospects_in_dnc) {
                                campaignPauseReasonAndCounts.copy(
                                  campaignPauseReason = CampaignPauseReason.RestProspectsAreInDNC
                                )
                              }
                              else if (unsentProspectsCounts.total_unsent_prospects_in_same_tz == 0) {
                                campaignPauseReasonAndCounts.copy(
                                  campaignPauseReason = CampaignPauseReason.NoProspectLeftInCurrentTimezone
                                )
                              }
                              else {
                                campaignPauseReasonAndCounts.copy(
                                  campaignPauseReason = CampaignPauseReason.UnknownReason
                                )
                              }

                              pauseReason

                          }
                        }
                      })
                  }
                })
            }
          }
        }
    }
  } match {

    case Failure(exception) =>
      logger.error(s"Error occurred while analyzing campaign: $campaignId", err = exception)
      CampaignPauseReasonAndCounts(
        campaignPauseReason = CampaignPauseReason.ServerErrorOccurredWhileAnalyzing
      )

    case Success(campaignPauseReasonAndCounts: CampaignPauseReasonAndCounts) =>
      campaignPauseReasonAndCounts
  }


  def analyzeCampaignSendReports(campaignId: CampaignId, teamId: TeamId)(using logger: SRLogger): Try[Int] = {

    val campaignPauseReasonAndCounts: CampaignPauseReasonAndCounts = getCampaignPauseReasonAndCounts(
      campaignId = campaignId,
      teamId = teamId
    )

    campaignSendReportsDAO
      .saveCampaignAnalysisResults(
        campaignAnalysisResults = CampaignAnalysisResults(
          campaign_id = campaignId,
          team_id = teamId,
          campaign_analysis_result = campaignPauseReasonAndCounts.campaignPauseReason,
          total_sent_prospects = campaignPauseReasonAndCounts.totalSentProspects,
          total_unsent_prospects = campaignPauseReasonAndCounts.totalUnsentProspects,
          total_unsent_prospects_in_dnc = campaignPauseReasonAndCounts.totalUnsentProspectsInDNC,
          total_unsent_prospects_in_same_tz = campaignPauseReasonAndCounts.totalUnsentProspectsInSameTz,
          total_unsent_prospects_with_invalid_emails = campaignPauseReasonAndCounts.total_unsent_prospects_with_invalid_emails,
          total_unsent_prospects_with_previous_task_not_done = campaignPauseReasonAndCounts.total_unsent_prospects_with_previous_task_not_done,
          total_unsent_prospects_that_are_likely_valid = campaignPauseReasonAndCounts.total_unsent_prospects_that_are_likely_valid,
          total_unsent_prospects_with_missing_merge_tags = campaignPauseReasonAndCounts.total_unsent_prospects_with_missing_merge_tags
        )
      ) match {
      case Failure(e) =>
        if (e.getMessage.contains("campaign_send_start_reports_campaign_id_fkey")) {
          logger.info(s"Campaign: ${campaignId.id} does not exist or has been deleted.")
          Success(1) // returning Success because we don't want to re-analyze this campaign.
        }
        else {
          Failure(e)
        }

      case Success(savedReportsId) =>

        campaignDAO.updateReportsId(
          latestCampaignSendStartReportsId = savedReportsId,
          campaignId = campaignId,
          teamId = teamId
        )
    }
  }

  def getSenderRotationStats(
                              campaignId: CampaignId,
                              teamId: TeamId,
                              least_amount_of_prospects_needed_to_meet_limit: Int,
                              start_time_for_campaign_for_the_day: DateTime
                            )(using Logger: SRLogger): Try[SenderRotationStats] = {

    DbFallbackToCacheUtil.findFromCacheFallbackToDB(
      getFromCache = campaignCacheService.getSenderRotationStatsById(
        campaignId = campaignId,
        teamId = teamId
      ),
      getFromDb = getSenderRotationStatsFromDB(
        campaignId = campaignId,
        teamId = teamId,
        least_amount_of_prospects_needed_to_meet_limit = least_amount_of_prospects_needed_to_meet_limit,
        start_time_for_campaign_for_the_day = start_time_for_campaign_for_the_day),
      setToCache = campaignCacheService.setSenderRotationStatsById(
        campaignId = campaignId,
        teamId = teamId)
    ).map(_.getOrElse(SenderRotationStats(prospects_not_sent_any_emails = 0, prospects_to_get_follow_up = 0)))

  }


  def getSenderRotationStatsFromDB(
                                    campaignId: CampaignId,
                                    teamId: TeamId,
                                    least_amount_of_prospects_needed_to_meet_limit: Int,
                                    start_time_for_campaign_for_the_day: DateTime
                                  )(using Logger: SRLogger): Try[Option[SenderRotationStats]] = {

    for {
      first_step: Int <- campaignProspectDAO.getApproxCountOfProspectsThatCanGetFirstEmailToday(
        campaignId = campaignId,
        teamId = teamId,
        least_amount_of_prospects_needed_to_meet_limit = least_amount_of_prospects_needed_to_meet_limit,
        start_of_day = start_time_for_campaign_for_the_day)
      follow_up: Int <- campaignProspectDAO.getApproxCountOfProspectsThatCanGetFollowupToday(
        campaignId = campaignId,
        teamId = teamId,
        least_amount_of_prospects_needed_to_meet_limit = least_amount_of_prospects_needed_to_meet_limit)
    } yield Some(SenderRotationStats(
      prospects_not_sent_any_emails = first_step,
      prospects_to_get_follow_up = follow_up
    ))

  }
  //  def getCampaignSteps(
  //                      campaign_id: CampaignId
  //                      ): Seq[CampaignStepWithChildren] = {
  //
  //    campaignDAOService.findByCampaignId(
  //      campaignId = campaign_id.id
  //    )
  //
  //  }


  def analyzeCampaignSendReportsNewFlow(
                                         campaignToCheckForSendingLimitNewFlow: CampaignToCheckForSendingLimitNewFlow,
                                       )(using logger: SRLogger): Try[Int] = {

    getSenderRotationStats(
      campaignId = campaignToCheckForSendingLimitNewFlow.campaign_id,
      teamId = campaignToCheckForSendingLimitNewFlow.team_id,
      least_amount_of_prospects_needed_to_meet_limit = campaignToCheckForSendingLimitNewFlow.max_emails_per_day,
      start_time_for_campaign_for_the_day = TimezoneUtils
        .getStartOfDayWithTimezone(timezone = campaignToCheckForSendingLimitNewFlow.timezone)
        .plusSeconds(campaignToCheckForSendingLimitNewFlow.daily_from_time)
    ) match {
      case Failure(exception) =>
        logger.fatal(s"Failed to getSenderRotationStats", exception)
        Failure(exception)
      case Success(senderRotationStats) =>

        val log = CampaignService.checkIfCampaignsAreStuckNewFlow(
          campaign_to_check = campaignToCheckForSendingLimitNewFlow,
          senderRotationStats = senderRotationStats
        )

        log match {
          case Some(campaignSendingVolumeLogs) =>
            if (campaignSendingVolumeLogs.possible_issue_if_any.isDefined) {
              logger.fatal(s"Campaign is not meeting the sending limit - cid_${campaignSendingVolumeLogs.campaign_id.id} tid_${campaignSendingVolumeLogs.team_id.id} reason - ${campaignSendingVolumeLogs.possible_issue_if_any}")
            }
            campaignSendingVolumeLogsDAO.addCampaignSendingVolumeLog(
              campaignSendingVolumeLogs = campaignSendingVolumeLogs,
              campaignAnalysisFor = CampaignAnalysisFor.SendingVolume,
            ) flatMap { value =>
              if (value == 0) {
                logger.fatal(s"FAILED TO ADD addCampaignSendingVolumeLog - cid_${campaignSendingVolumeLogs.campaign_id.id} tid_${campaignSendingVolumeLogs.team_id.id} reason - ${campaignSendingVolumeLogs.possible_issue_if_any}")
              }
              campaignDAO.doneInAnalyzingStartReportQueue(
                campaignId = campaignSendingVolumeLogs.campaign_id,
                teamId = campaignSendingVolumeLogs.team_id
              )
            }

          case None =>
            logger.debug(s"Campaign has not reached the 60 min run mark cid_${campaignToCheckForSendingLimitNewFlow.campaign_id.id} tid_${campaignToCheckForSendingLimitNewFlow.team_id.id}")
            Success(0)
        }

    }
  }

  def getCampaignReportForStuckCampaignsForLast15Mins: Try[List[CampaignSendingVolumeLogsForReport]] = {
    campaignSendingVolumeLogsDAO.getCampaignReportForStuckCampaignsForLast15Mins
  }


  /*
    Note:
    9 July 2024
      We check here if Last Email is sent for the campaign , if last email is not sent => Null, we don't update
      the next_email_scheduled and keep it as Null Only.

      Why?
       We pick campaigns on the basis of next_email_scheduled_at and last_read_for_replies , but if
       next_email_scheduled_at =NULL  implies this is a fresh campaign , so we don't want to track replies,
       and just put Campaign for scheduling.


       You can check this query CampaignDAO.findCampaignsForSchedulingEA,

       where we fetch campaigns if next_email_scheduled_at is Null,

       otherwise if not null we check for reply tracking also.


       That's why we don't update next_email_scheduled_at if there is not email sent yet for campaign  which correctly
       implies that it is a new campaign.




   */
  private def trackReplyIfEmailSentPreviously(
                                               data: CampaignSetNextToBeScheduledAtDataForSettingChanges,
                                               teamId: TeamId,
                                             )(using Logger: SRLogger): Seq[(CampaignSetNextToBeScheduledAtDataForSettingChanges, DateTime)] = {

      checkIfLastSentEmailAtIsNull(campaignId = data.campaignId, teamId = teamId) match {
        case Failure(exception) => {
          Logger.shouldNeverHappen(s"checkIfLastSentEmailAtIsNull failed: flow is : ${data.toKey} : cid_${data.campaignId.id} tid_${teamId.id}", Some(exception))
          Seq()
        }
        case Success(isEmailNotSentYet) => {
          if (isEmailNotSentYet) {
            /*
            * For this case we are not Doing reply tracking as No email Was ever sent
            * */
            Logger.info(s"flow is : ${data.toKey} :: Not Updating Next Email Scheduled at for cid_${data.campaignId.id} tid_${teamId.id} as No Email IsSent Yet ")
            val updatedData = data match {

              case value: CampaignSetNextToBeScheduledAtData.UpdateCampaignSettingFlow => value.copy(campaignSettings = value.campaignSettings.copy(campaign_email_settings = List()))
              case value: CampaignSetNextToBeScheduledAtData.StartCampaignFlow => value.copy(campaignSettings = value.campaignSettings.copy(campaign_email_settings = List()))
              case value: CampaignSetNextToBeScheduledAtData.AddedProspectToCampaignFlow => value.copy(campaignSettings = value.campaignSettings.copy(campaign_email_settings = List()))
            }
            Seq((updatedData, DateTime.now()))
          } else {
            Seq((data, DateTime.now()))

          }
        }
      }
  }

  def setNextToBeScheduledAt(
                              flowData: CampaignSetNextToBeScheduledAtData,
                              Logger: SRLogger,
                              team_id: TeamId,
                            ): Try[Int] = {

    // Logger.debug("Entry setNextToBeScheduledAt ")

    given srLogger: SRLogger = Logger // fixme given

    val campaignIdsWithNextToBeScheduledAt: Seq[CampaignDataToAddNextToBeScheduledAt] = flowData match {

      case data: CampaignSetNextToBeScheduledAtData.SchedulerSuccessFlow =>

        /* 15-Apr-2024
        NOTE: we have added the `last_schedule_done_at` check in the scheduler queries which enforces a min 5-min interval
        between consecutive schedule-attempts for the same campaign, making the below limits (3-min / 5-min) irrelevant

        if (savedTasksFromCurrentSchedulerRunIfSchedulerFlow > 0) {

          AppConfig.SchedulerConfig.CAMPAIGN_NEXT_SCHEDULED_AT_DELAY_IN_MINUTES_IF_SOME_TASKS_FOUND_IN_CURRENT_RUN_OLD_19FEB2024

        } else {

          AppConfig.SchedulerConfig.CAMPAIGN_NEXT_SCHEDULED_AT_DELAY_IN_MINUTES_IF_NO_TASK_FOUND_IN_CURRENT_RUN_OLD_19FEB2024

        }
        */

        data.campaignDataWithAllTheTimezone.map { campaignDataWithAllTheTimezone =>
          val (campaignForScheduling, timezones) = campaignDataWithAllTheTimezone

          // we check if we have timezones for the campaign and we scheduled 0 tasks in this run
          // if so we check which timezone is upcoming and set the next_start_time to the start of that timezone run
          // if we dont have any upcoming timezone, we put the start of the earliest timezone for the next day
          val nextToBeScheduledAt: DateTime = if(timezones.nonEmpty && data.total_scheduled == 0) {
            val all_campaign_start_time = timezones.map(timezone => {
              DateTime.now()
                .withZone(DateTimeZone.forID(timezone))
                .withTimeAtStartOfDay()
                .plusSeconds(campaignForScheduling.daily_from_time)
            })

            val earliest_start_time = all_campaign_start_time.min
            val next_start_time = all_campaign_start_time.filter(a => a.isAfter(DateTime.now())).minOption

            if(next_start_time.isDefined){
              next_start_time.get
            } else {
              earliest_start_time.plusDays(1)
            }

          } else {
            DateTime.now().plusMinutes(AppConfig.SchedulerConfig.CAMPAIGN_NEXT_SCHEDULED_AT_MIN_DELAY_IN_MINUTES_COULD_NOT_LOCK)

          }

          CampaignSetNextToBeScheduledAtData.getCampaignDataToAddNextToBeScheduledAtForScheduler(
            campaignForScheduling = campaignForScheduling,
            nextToBeScheduledAt =  nextToBeScheduledAt
          )
        }.toList


      case data: CampaignSetNextToBeScheduledAtData.SchedulerRejectedFlow =>

        /* 15-Apr-2024
        NOTE: we have added the `last_schedule_done_at` check in the scheduler queries which enforces a min 5-min interval
        between consecutive schedule-attempts for the same campaign, making the below limits (3-min / 5-min) irrelevant

        if (savedTasksFromCurrentSchedulerRunIfSchedulerFlow > 0) {

          AppConfig.SchedulerConfig.CAMPAIGN_NEXT_SCHEDULED_AT_DELAY_IN_MINUTES_IF_SOME_TASKS_FOUND_IN_CURRENT_RUN_OLD_19FEB2024

        } else {

          AppConfig.SchedulerConfig.CAMPAIGN_NEXT_SCHEDULED_AT_DELAY_IN_MINUTES_IF_NO_TASK_FOUND_IN_CURRENT_RUN_OLD_19FEB2024

        }
        */
        data.rejectedForSchedulingReason match {
          case CampaignRejectedForSchedulingReason.CurrentTimeNotInCampaignTimezone =>
            data.campaignDataWithAllTheTimezone.map { campaignAndTimezone =>
              val (campaignForScheduling, timezones) = campaignAndTimezone


              val nextToBeScheduledAt: DateTime = CampaignService.calculateNextToBeScheduled(
                timezones = timezones,
                current_time = DateTime.now(),
                daily_from_time = campaignForScheduling.daily_from_time,
                daily_till_time = campaignForScheduling.daily_till_time
              )

              CampaignSetNextToBeScheduledAtData.getCampaignDataToAddNextToBeScheduledAtForScheduler(
                campaignForScheduling = campaignForScheduling,
                nextToBeScheduledAt = nextToBeScheduledAt
              )
            }.toList


          case CampaignRejectedForSchedulingReason.CouldNotAcquireCampaignLock =>
            data.campaignsForScheduling.map { campaignForScheduling =>
              CampaignSetNextToBeScheduledAtData.getCampaignDataToAddNextToBeScheduledAtForScheduler(
                campaignForScheduling = campaignForScheduling,
                nextToBeScheduledAt = DateTime.now().plusMinutes(AppConfig.SchedulerConfig.CAMPAIGN_NEXT_SCHEDULED_AT_MIN_DELAY_IN_MINUTES_COULD_NOT_LOCK)

              )
            }.toList


          case CampaignRejectedForSchedulingReason.CampaignHasHitDailyLimit =>
            data.campaignsForScheduling.toSeq.map { camp =>
              val startOfNextSchedulingDay: DateTime = Helpers.startOfTheDay(camp.timezone)
                .plusDays(1) //if we have hit the limit we can schedule the email the following day rather than trying today
                .minusMinutes(30) // the scheduling happens 30 mins before the time starts, so just to avoid delay in scheduling we are doing this

              Logger.debug(s"daily limit reached for campaign id -${camp.campaign_id} tid_${camp.team_id} nextSchedulingAt - $startOfNextSchedulingDay")
              CampaignSetNextToBeScheduledAtData.getCampaignDataToAddNextToBeScheduledAtForScheduler(
                campaignForScheduling = camp,
                nextToBeScheduledAt = startOfNextSchedulingDay

              )
            }.toList


        }


      case data: CampaignSetNextToBeScheduledAtData.UpdateCampaignSettingFlow =>

        // immediately, in case of campaign settings changes
        trackReplyIfEmailSentPreviously(
          data = data,
          teamId = team_id,
        ).flatMap { data =>
          CampaignSetNextToBeScheduledAtData.getCampaignDataToAddNextToBeScheduledAtForCampaign(
            campaignSettings = data._1.campaignSettings,
            nextToBeScheduledAt = data._2,
            campaignId = data._1.campaignId

          )
        }


      case data: CampaignSetNextToBeScheduledAtData.StartCampaignFlow =>

        // immediately, in case of campaign-start
        /*
          If Last Sent By Campaign IS Not Null Other wise return Empty SEQUENCE
         */
        trackReplyIfEmailSentPreviously(
          data = data,
          teamId = team_id,
        ).flatMap { data =>
          CampaignSetNextToBeScheduledAtData.getCampaignDataToAddNextToBeScheduledAtForCampaign(
            campaignSettings = data._1.campaignSettings,
            nextToBeScheduledAt = data._2,
            campaignId = data._1.campaignId

          )
        }


      case data: CampaignSetNextToBeScheduledAtData.AddedProspectToCampaignFlow =>

        // immediately, in case of new prospect addition
        trackReplyIfEmailSentPreviously(
          data = data,
          teamId = team_id,
        ).flatMap { data =>
          CampaignSetNextToBeScheduledAtData.getCampaignDataToAddNextToBeScheduledAtForCampaign(
            campaignSettings = data._1.campaignSettings,
            nextToBeScheduledAt = data._2,
            campaignId = data._1.campaignId

          )
        }


    }


    val debug_result = campaignDAO._updateNextToScheduledAt(
      campaignIdsWithNextToBeScheduledAt = campaignIdsWithNextToBeScheduledAt,
      Logger = Logger,
      team_id = team_id,
      is_scheduler_flow = flowData.fromSchedulerFlow
    )


    // Logger.debug(s"Exit setNextToBeScheduledAt: $debug_result")

    debug_result
  }

  // SENDER_ROTATION
  // called from -
  // archiveCampaign
  // CampaignController.changeArchiveStatus
  private def checkIfLastSentEmailAtIsNull(campaignId: CampaignId, teamId: TeamId): Try[Boolean] =
    campaignDAO.checkIfLastSentEmailAtIsNull(campaignId = campaignId, teamId = teamId)

  private def onStopOrArchiveCampaign(
                                       campaign: CampaignWithStatsAndEmail,
                                       teamId: TeamId
                                     )(using logger: SRLogger): Try[Boolean] = {

    val campaignId = campaign.id
    val teamId = TeamId(campaign.team_id)

    for {

      _: Option[CampaignWithStatsAndEmail] <- stopWarmup(id = campaignId, team_id = teamId)

      _: Int <- selectAndPublishForDeletionService.selectAndPublishForDeletion(
        deletion_reason = DeletionReason.OnStopOrArchiveCampaign,
        deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByCampaignId(
          campaignId = campaignId,
          senderEmailSettingIds = campaign.settings.campaign_email_settings.map(_.sender_email_setting_id),
          teamId = teamId,
        )
      )

      _: Int <- triggerDAO.deleteCampaignTriggers(
        campaignId = campaignId,
        teamId = campaign.team_id
      )


    } yield {
      true
    }
  }


  // SENDER_ROTATION
  // called from -
  // CampaignController.changeArchiveStatus
  def archiveCampaign(
                       campaignId: Long,
                       teamId: TeamId
                     )(using Logger: SRLogger): Try[CampaignWithStatsAndEmail] = {

    for {


      campaign: CampaignWithStatsAndEmail <- updateStatus(
        id = campaignId,
        newStatus = CampaignStatus.ARCHIVED,
        teamId = teamId
      )
        .flatMap {
          case None => Failure(new Exception("Campaign not found. Could you check and try again ?"))
          case Some(c) =>

            // "Current sending email accounts" is taken from cache
            resetUserCacheUtil._resetTeamCache(aid = c.owner_id)

            Success(c)
        }

      _: Boolean <- onStopOrArchiveCampaign(
        campaign = campaign,
        teamId = teamId
      )

    } yield {
      campaign
    }
  }


  def stopCampaign(
                    campaignId: Long,
                    teamId: TeamId
                  )(using Logger: SRLogger): Try[CampaignWithStatsAndEmail] = {

    for {

      campaign: CampaignWithStatsAndEmail <- updateStatus(
        id = campaignId,
        newStatus = CampaignStatus.STOPPED,
        teamId = teamId
      )
        .flatMap {
          case None => Failure(new Exception("Campaign not found. Could you check and try again ?"))
          case Some(c) =>

            // "Current sending email accounts" is taken from cache
            resetUserCacheUtil._resetTeamCache(aid = c.owner_id)

            Success(c)
        }

      _: Boolean <- onStopOrArchiveCampaign(
        campaign = campaign,
        teamId = TeamId(id = campaign.team_id)
      )

    } yield {
      campaign
    }
  }

  def stopAllCampaigns(by: FindCampaignIdsBy)(
    logger: SRLogger
  ): Try[Boolean] = Try {

    given srLogger: SRLogger = logger // fixme given

    campaignDAO.findActiveCampaignIds(by = by) match {
      case Failure(e1) =>

        logger.error(s"FATAL stopAllCampaigns ($by) Error while deactivating campaigns :: ${LogHelpers.getStackTraceAsString(e1)}")

        throw e1

      case Success(cidsAndTeamIds) =>

        for (cidAndTeamId <- cidsAndTeamIds) {

          stopCampaign(campaignId = cidAndTeamId.campaign_id, teamId = TeamId(cidAndTeamId.team_id))
          match {

            case Failure(e) =>
              logger.error(s"FATAL stopAllCampaigns ($by) FATAL $cidAndTeamId " + s": ${LogHelpers.getStackTraceAsString(e)}")

              throw e

            case Success(campaign) =>
              logger.info(s"stopAllCampaigns ($by) $cidAndTeamId " +
                s": ${campaign.name} has been paused")
          }

        }

        true

    }
  }

  def getSessionCookieAndUserAgentForLinkedinSetting(campaignId: CampaignId, teamId: TeamId): Try[LinkedinSessionCookieAndUserAgent] = {

    campaignDAO.getSessionCookieAndUserAgentForLinkedinSetting(
        campaignId = campaignId,
        teamId = teamId
      )
      .flatMap {
        case None => Failure(new Exception(s"Linkedin Setting for campaign_$campaignId does not have a session_cookie"))
        case Some(value) => Success(value)
      }

  }


  def createCampaign(
                      orgId: Long,
                      accountId: Long,
                      teamId: Long,
                      taId: Long,
                      data: CampaignCreateForm,
                      campaignSettings: Option[CampaignSettings] = None,
                      permittedAccountIdsForEditCampaigns: Seq[Long],
                      ownerFirstName: String
                    )(implicit ec: ExecutionContext, Logger: SRLogger): Future[Either[CampaignCreationError, CampaignWithStatsAndEmail]] = Future {

    if (permittedAccountIdsForEditCampaigns.contains(accountId)) {
      create(
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        data = data,
        campaignSettings = campaignSettings,
        ownerFirstName = ownerFirstName

      ) match {
        case Failure(e) =>
          if (e.getMessage != null && e.getMessage.contains("unique_campaign_name")) {
            Left(CampaignCreationError.CampaignNameNotUniqueError(e)) //campaignNameNotunique
          } else {
            Left(CampaignCreationError.SQLException(e)) //dberror
          }
        case Success(None) =>
          Left(CampaignCreationError.CampaignNotCreated)

        case Success(Some(data)) =>
          Logger.debug(s"created Campaign with campaign id - ${data.id}")

          val entityType = EmailSendingEntityTypeData.CampaignData(
            orgId = OrgId(orgId), campaignId = data.id, teamId = teamId
          )
          val updateEmailSendingStatusForm = UpdateEmailSendingStatusForm(
            entityType = entityType,
            orgId = OrgId(orgId),
            sendEmailStatus = SendEmailStatusData.AllowedData()
          )
          val addingSendStatus = emailSendingStatusDAO.addingEmailSendingStatusTry(
            updateEmailSendingStatusForm = updateEmailSendingStatusForm
          ) match {
            case Failure(err) =>
              Logger.fatal(s"Error while adding to Email send status for campaign id - ${data.id}", err = err)
            case Success(_) =>
              Logger.debug(s"added to Email send status for campaign id - ${data.id}")

              {}
          }

          srUserFeatureUsageEventService.addFeatureUsageEvent(
            orgId = orgId,
            event = SrUserFeatureUsageEvent.CreatedFirstCampaign(
              created_first_campaign_at = DateTime.now()
            )
          ) match {
            case Failure(e) =>

              Left(CampaignCreationError.FeatureUsageEventSaveError(e))

            case Success(value) =>
              Right(data)
          }


      }
    } else {
      Left(CampaignCreationError.InCorrectCampaignOwnerId)
    }
  }

  def getInactiveCampaignsForStopping(
                                       inactiveCampaignCheckType: InactiveCampaignCheckType,
                                       campaignIdAndTeamId: CampaignIdAndTeamId,
                                       stepTypesInCampaign: Set[CampaignStepType]
                                     ): Try[Option[InactiveCampaignData]] = {
    campaignDAO.getInactiveCampaignsForStopping(
      inactiveCampaignCheckType = inactiveCampaignCheckType,
      campaignIdAndTeamId = campaignIdAndTeamId,
      stepTypesInCampaign = stepTypesInCampaign
    )
  }

  // SENDER_ROTATION
  // called from -
  // CampaignService.archiveCampaign
  // CampaignService.stopCampaign
  // CampaignStartService.startCampaignAfterInitialValidation
  // SpamMonitorService.updateCampaignAndEmailSendStatus
  // FiveMinutelyCronService
  def updateStatus(
                    id: Long,
                    newStatus: CampaignStatus,
                    scheduleStartAt: Option[DateTime] = None,
                    scheduleStartAtTimeZone: Option[String] = None,
                    isSupport: Boolean = false,
                    teamId: TeamId
                  )(using Logger: SRLogger): Try[Option[CampaignWithStatsAndEmail]] = Try {

    campaignDAO.findCampaignForCampaignUtilsOnly(id = id, teamId = teamId) match {
      case None => Failure(new Throwable("No Campaign Found For the Id"))
      case Some(campaign) =>
        campaign.status match {
          case CampaignStatus.SUSPENDED | CampaignStatus.UNDER_REVIEW =>
            if (!isSupport) {
              Failure(new Throwable(s"ACTION NOT ALLOWED, tried to ${newStatus.toString} a campaign that is ${campaign.status} without access to do so."))
            } else {
              Logger.debug(s"changing to ${newStatus.toString} a campaign that is ${campaign.status}. campaign_id - $id")
              campaignDAO.updateStatus(
                id = id,
                newStatus = newStatus,
                scheduleStartAt = scheduleStartAt,
                team_id = TeamId(campaign.team_id),
                scheduleStartAtTimeZone = scheduleStartAtTimeZone).map { updatedId =>
                updatedId.flatMap { cm =>
                  find(
                    id = cm.campaign_id,
                    teamId = cm.team_id
                  )
                }
              }
            }
          case CampaignStatus.STOPPED | CampaignStatus.ARCHIVED | CampaignStatus.RUNNING | CampaignStatus.SCHEDULED | CampaignStatus.NOT_STARTED | CampaignStatus.ON_HOLD =>
            if (newStatus == campaign.status) {
              Success(None)
            } else if (
              (newStatus == CampaignStatus.UNDER_REVIEW || newStatus == CampaignStatus.SUSPENDED) &&
                !isSupport
            ) {
              Failure(new Throwable(s"ACTION NOT ALLOWED, tried to ${newStatus.toString} a campaign that is ${campaign.status} without access to do so."))
            } else {
              Logger.debug(s"changing to ${newStatus.toString} a campaign that is ${campaign.status}. campaign_id - $id")
              campaignDAO.updateStatus(
                id = id,
                newStatus = newStatus,
                team_id = TeamId(campaign.team_id),
                scheduleStartAt = scheduleStartAt,
                scheduleStartAtTimeZone = scheduleStartAtTimeZone
              ).map {
                case None => None

                case Some(cm) =>
                  if (campaign.settings.campaign_email_settings.isEmpty) {
                    find(
                      id = cm.campaign_id,
                      teamId = cm.team_id
                    )
                  } else {
                    if (newStatus == CampaignStatus.RUNNING) {
                      //USE_CASE_scheduleForUpdateWhenCampaignIsUpdated
                      campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated(
                        campaignId = CampaignId(cm.campaign_id),
                        teamId = teamId
                      ) match {
                        case Success(value) => //DO NOTHING
                        case Failure(exception) =>
                          Logger.fatal(s"Failed scheduleForUpdateWhenCampaignIsUpdated", exception)
                      }
                      campaignDAO.updateEmailUseStatus(email_ids = campaign.settings.campaign_email_settings.map(ces => ces.sender_email_setting_id), teamId = TeamId(id = cm.team_id), status = true)
                    }
                    else {
                      campaignDAO.updatePrevEmailUseStatus(email_ids = campaign.settings.campaign_email_settings.map(ces => ces.sender_email_setting_id), teamId = TeamId(id = cm.team_id))
                    }

                    find(
                      id = cm.campaign_id,
                      teamId = cm.team_id
                    )
                  }
              }
            }
        }
    }
  }.flatten

  def find(
            id: Long,
            teamId: Long,
            account: Option[Account] = None
          )(using logger: SRLogger): Option[CampaignWithStatsAndEmail] = {

    val campaignDetails = for {

      campaignBasicInfo: Option[CampaignBasicInfo] <- getCampaignBasicInfo(teamId = teamId, campaginId = id)

      allCampaignStats: Option[AllCampaignStats] <- Try {
        campaignBasicInfo.map(c => {
          getCampaignStatsById(
            cId = id,
            campaignStatus = c.status,
            campaignCreatedAt = c.created_at,
            teamId = teamId,
            campaign_has_email_step = campaignBasicInfo.exists(_.campaign_has_email_step),
            acc = if (account.isDefined) AccountData.AccountObj(account.get) else AccountData.AccountId(campaignBasicInfo.get.owner_id), Logger = logger
          )(campaignCacheService = campaignCacheService)
            .get
        })
      }

      campaigns: Option[CampaignWithStatsAndEmail] = campaignBasicInfo.map(
        c => CampaignWithStatsAndEmail(
          id = c.id,
          uuid = c.uuid,
          team_id = c.team_id,
          shared_with_team = c.shared_with_team,
          name = c.name,
          owner_name = c.owner_name,
          owner_email = c.owner_email,
          owner_id = c.owner_id,
          status = c.status,
          tags = c.tags,
          spam_test_exists = c.spam_test_exists,
          warmup_is_on = c.warmup_is_on,
          stats = allCampaignStats.get, // If campaignBasicInfo is not None, this cannot be None
          head_step_id = c.head_step_id,
          ai_generation_context = c.ai_generation_context,
          settings = c.settings,
          created_at = c.created_at,
          error = c.error,
          is_archived = c.is_archived
        )
      )

    } yield campaigns

    campaignDetails match {
      case Failure(err) =>
        logger.fatal(s"An error occurred while fetching campaign details for campaignId:$id ::: teamId: $teamId", err)
        None
      case Success(campaign) =>
        campaign
    }


  }

  def findWithPermission(
                          id: Long,
                          permittedAccountIds: Seq[Long],
                          teamId: Long,
                          account: Option[Account] = None
                        )(using logger: SRLogger): Option[CampaignWithStatsAndEmail] = {

    val campaignDetails = for {

      campaignBasicInfo: Option[CampaignBasicInfo] <- campaignDAO.getCampaignBasicInfoWithPermission(teamId = teamId, cId = id, permittedAccountIds = permittedAccountIds)

      allCampaignStats: Option[AllCampaignStats] <- Try {
        campaignBasicInfo.map(c => {
          getCampaignStatsById(
            cId = id,
            campaignStatus = c.status,
            campaignCreatedAt = c.created_at,
            teamId = teamId,
            campaign_has_email_step = campaignBasicInfo.exists(_.campaign_has_email_step),
            acc = if (account.isDefined) AccountData.AccountObj(account.get) else AccountData.AccountId(campaignBasicInfo.get.owner_id), Logger = logger
          )(campaignCacheService = campaignCacheService)
            .get
        })
      }

      campaigns: Option[CampaignWithStatsAndEmail] = campaignBasicInfo.map(
        c => CampaignWithStatsAndEmail(
          id = c.id,
          uuid = c.uuid,
          team_id = c.team_id,
          shared_with_team = c.shared_with_team,
          name = c.name,
          owner_name = c.owner_name,
          owner_email = c.owner_email,
          owner_id = c.owner_id,
          status = c.status,
          tags = c.tags,
          spam_test_exists = c.spam_test_exists,
          warmup_is_on = c.warmup_is_on,
          stats = allCampaignStats.get, // If campaignBasicInfo is not None, this cannot be None
          head_step_id = c.head_step_id,
          ai_generation_context = c.ai_generation_context,
          settings = c.settings,
          created_at = c.created_at,
          error = c.error,
          is_archived = c.is_archived
        )
      )

    } yield campaigns

    campaignDetails match {
      case Failure(err) =>
        logger.fatal(s"An error occurred while fetching campaign details for campaignId:$id ::: teamId: $teamId", err)
        None
      case Success(campaign) =>
        campaign
    }


  }

  def getCampaignBasicInfo(
                            teamId: Long,
                            campaginId: Long
                          ): Try[Option[CampaignBasicInfo]] = {

    campaignDAO.getCampaignBasicInfo(
      teamId = teamId,
      cId = campaginId
    )

  }

  def getCampaignsForIndependentStepSchedulerMQ(): Try[List[MqIndependentStepSchedulerMsg]] = Try {

    val interval_time = SQLSyntax.createUnsafely(AppConfig.IndependentStepScheduler.independent_step_scheduler_interval_seconds.toString)
    val in_queue_stuck_time = SQLSyntax.createUnsafely(AppConfig.IndependentStepScheduler.independent_step_scheduler_in_stuck_interval.toString)

    DB.readOnly { implicit session =>
      sql"""
        SELECT
          c.id as campaign_id,
          t.id as team_id,
          o.id as org_id
        FROM
          campaigns c
          INNER JOIN teams t ON c.team_id = t.id
          INNER JOIN organizations o ON o.id = t.org_id
        WHERE
          o.active
          AND o.plan_type != ${PlanType.INACTIVE.toString}
          AND c.campaign_type = ${CampaignType.Drip.toString}
          AND c.status = ${CampaignStatus.RUNNING.toString}
          AND c.head_step_id IS NOT NULL
          AND EXISTS (
              SELECT 1
              FROM campaign_steps cs
              WHERE c.id = cs.campaign_id
                AND cs.step_type = ${CampaignStepType.moveToAnotherCampaign}
          )
          AND (
            ( c.in_queue_for_independent_step_scheduler = false
             AND
              (
                c.last_pushed_to_queue_for_independent_step_scheduler IS NULL
               OR
                c.last_pushed_to_queue_for_independent_step_scheduler  < NOW() - INTERVAL '$interval_time SECONDS'
              )
            )
            OR
              (
                c.in_queue_for_independent_step_scheduler = true
                 AND
                c.last_pushed_to_queue_for_independent_step_scheduler  < NOW() - INTERVAL '$in_queue_stuck_time HOUR'
              )
          )
        ORDER BY
          c.last_pushed_to_queue_for_independent_step_scheduler ASC NULLS FIRST
        LIMIT 100
      """
      .map(rs => MqIndependentStepSchedulerMsg(
        campaignId = CampaignId(id = rs.long("campaign_id")),
        teamId = TeamId(id = rs.long("team_id")),
        org_id = OrgId(id = rs.long("org_id"))
      ))
      .list
      .apply()
    }
  }


  def mqIndependentStepSchedulerUpdatePushedToMqToTrue(mqIndependentMsg: MqIndependentStepSchedulerMsg): Try[Long] = Try {

    DB.autoCommit { implicit session =>

      sql"""
           UPDATE campaigns
            SET
              in_queue_for_independent_step_scheduler = true,
              last_pushed_to_queue_for_independent_step_scheduler = now()
            WHERE
              id = ${mqIndependentMsg.campaignId.id}
              AND team_id = ${mqIndependentMsg.teamId.id}
         """
        .update
        .apply()
    }


  }

  def mqIndependentStepSchedulerUpdatePushedToMqToFalse(
                                                         campaign_id: CampaignId,
                                                         team_id: TeamId
                                                       )(using logger: SRLogger): Try[Long] = Try {

    logger.info(s"mqIndependentStepSchedulerUpdatePushedToMqToFalse :: campaign_id : ${campaign_id.id} :: team_id : ${team_id.id}")


    DB.autoCommit { implicit session =>

      sql"""
           UPDATE campaigns
            SET
              in_queue_for_independent_step_scheduler = false
            WHERE
              id = ${campaign_id.id}
              AND team_id = ${team_id.id}
         """
        .update
        .apply()
    }


  }

  private def create(
                      accountId: Long,
                      teamId: Long,
                      taId: Long,
                      data: CampaignCreateForm,
                      campaignSettings: Option[CampaignSettings],
                      ownerFirstName: String
                    )(using Logger: SRLogger): Try[Option[CampaignWithStatsAndEmail]] = {

    val defaultTimezone = DateTimeZone.forID("America/Los_Angeles").toString
    val timezone = if (
      campaignSettings.isDefined &&
        campaignSettings.get.timezone.nonEmpty &&
        TimezoneUtils.isValidTimezone(tz = campaignSettings.get.timezone.trim)
    ) {
      campaignSettings.get.timezone.trim
    } else if (
      data.timezone.isDefined &&
        data.timezone.get.nonEmpty &&
        TimezoneUtils.isValidTimezone(tz = data.timezone.get.trim)
    ) {
      data.timezone.get.trim
    } else {
      defaultTimezone.trim
    }

    val camp_uuid = srUuidUtils.generateCampaignUuid()


    val newCampaignId = for {

      campaignName: String <- campaignDAOService.getNameForNewCampaign(
        ownerFirstName = ownerFirstName,
        teamId = TeamId(id = teamId)
      )
      name: String <- Try {
        /* Note:
            If the Name Comes From Duplicate Campaign flow use that , Other wise use the default name we are generating
            above.
        * */
        if (data.name.isDefined && data.name.get.contains("Duplicate")) {
          data.name.get
        } else {
          campaignName
        }
      }
      newId <- campaignDAOService.create(
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        name = name,
        campaignSettings = campaignSettings,
        timezone = timezone,
        campaign_type = data.campaign_type,
        campaignUuid = camp_uuid,
      )

    } yield {
      newId
    }

    newCampaignId match {
      case Failure(exception) => {
        Logger.shouldNeverHappen(s"Campaign Creation Failed For aid_${accountId} tid_${teamId} ", err = Some(exception))
        Failure(exception)
      }
      case Success(newId) =>
        Success(newId.flatMap(id => find(
          id = id,
          teamId = teamId
        )))

    }
  }

  def updateName(
                  id: Long,
                  data: CampaignUpdateNameForm,
                  team_id: TeamId
                )(using Logger: SRLogger): Try[Option[CampaignWithStatsAndEmail]] = {
    campaignDAO.updateName(
      id = id,
      data = data,
      team_id = team_id
    ).map { updatedId =>
      updatedId.flatMap(cm => find(
        id = cm.campaign_id,
        teamId = cm.team_id
      )
      )
    }
  }

  def startWarmup(
                   id: Long,
                   data: StartCampaignWarmup,
                   team_id: TeamId
                 )(using logger: SRLogger): Try[Option[CampaignWithStatsAndEmail]] = {

    campaignDAO.startWarmup(
      id = id,
      data = data,
      team_id = team_id
    ).map { updatedId =>
      updatedId.flatMap { cm =>
        find(
          id = cm.campaign_id,
          teamId = cm.team_id
        )
      }
    }
  }

  def stopWarmup(
                  id: Long,
                  team_id: TeamId
                )(using Logger: SRLogger): Try[Option[CampaignWithStatsAndEmail]] = {
    campaignDAO.stopWarmup(
      id = id,
      team_id = team_id
    ).map { updatedId =>
      updatedId.flatMap { cm =>
        find(
          id = cm.campaign_id,
          teamId = cm.team_id
        )
      }
    }
  }

  def updateScheduleSettings(
                              id: Long,
                              data: UpdateCampaignScheduleSettings,
                              team_id: TeamId
                            )(using logger: SRLogger): Try[Option[CampaignWithStatsAndEmail]] = {
    campaignDAO.updateScheduleSettings(
      id = id,
      data = data,
      team_id = team_id
    ).map { updatedId =>
      updatedId.flatMap { cm =>
        //USE_CASE_scheduleForUpdateWhenCampaignIsUpdated
        campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated(
          campaignId = CampaignId(cm.campaign_id),
          teamId = team_id
        ) match {
          case Success(value) => //DO NOTHING
          case Failure(exception) =>
            logger.fatal(s"Failed scheduleForUpdateWhenCampaignIsUpdated", exception)
        }
        find(
          id = cm.campaign_id,
          teamId = cm.team_id
        )
      }
    }
  }

  def deleteVariant(
                     stepId: Long,
                     campaign: Campaign,
                     loggedinAccount: Account,
                     campaignId: Long,
                     step: CampaignStep,
                     variantId: Int,
                     teamId: TeamId
                   )(using Logger: SRLogger): Try[Option[Long]] = {

    val variants = campaignStepVariantDAO.findByStepId(stepId = stepId)
    val steps = campaignDAOService.findOrderedSteps(
      campaignId = campaign.id,
      teamId = teamId,
    )
    val activeVariantsInStep = variants.filter(_.active)

    val thisIsTheOnlyActiveVariantAmongstMultipleVariantsInStep = variants.size > 1 &&
      activeVariantsInStep.size == 1 &&
      activeVariantsInStep.head.id == variantId

    Logger.info(s"DELETE deleteVariant called by ${loggedinAccount.internal_id} :: $campaignId :: $stepId :: $variantId")

    if (variants.size <= 1 && steps.size <= 1 && !(campaign.settings.campaign_type == CampaignType.Drip && campaign.status == CampaignStatus.NOT_STARTED)) {

      Logger.warn(s"aid: ${loggedinAccount.internal_id} wants to delete head step, current steps count:  ${steps.size}")

      Failure(new Throwable(CONSTANTS.API_MSGS.SINGLE_STEP_DELETION_IS_NOT_ALLOWED))

    } else if (thisIsTheOnlyActiveVariantAmongstMultipleVariantsInStep) {

      Failure(new Throwable(CONSTANTS.API_MSGS.ONE_ACTIVE_VARIANT_MUST_BE_PRESENT_WITH_STEP))

    } else {

      campaignStepVariantDAO.find(variantId = variantId, stepId = stepId) match {

        case None => Success(None)

        case Some(variant) =>

          deleteV2(
            variantId = variantId,
            stepId = stepId,
            campaignId = campaign.id,
            teamId = teamId,
            sender_email_settings_ids = campaign.settings.campaign_email_settings.map(setting => setting.sender_email_setting_id)
          ) match {

            case Failure(e) =>
              Logger.fatal(s"Oops! There was a problem while deleting this step variant. Could you try again ?", e)
              Failure(new Throwable(s"Oops! There was a problem while deleting this step variant. Could you try again ?"))

            case Success(returned_step_ids) =>

              Logger.info(s"deleteVariant :: List of step whose variant got deleted ${returned_step_ids}")

              if (variants.size < 2) {
                delete(
                  step = step,
                  campaign = campaign,
                  teamId = teamId
                ) match {

                  case Failure(e) =>

                    Logger.fatal(s"[FATAL] CampaignController.deleteVanriant.CampaignStep.delete  stepId: $stepId, campaignId: ${campaign.id}", e)

                    Failure(new Throwable(s"Oops! There was a problem while deleting this step variant. Could you try again ?"))

                  case Success(label_change_step_ids) =>

                    Logger.debug(s"deleteVariant :: Step ids whose labels have changed : ${label_change_step_ids}")
                    Success(Some(variant.id))
                }

              } else {

                Success(Some(variant.id))
              }

          }
      }
    }
  }

  def updateOtherSettings(
                           id: Long,
                           data: CampaignOtherSettings,
                           team_id: TeamId
                         )(using logger: SRLogger): Try[Option[CampaignWithStatsAndEmail]] = {
    campaignDAO.updateOtherSettings(
      id = id,
      data = data,
      team_id = team_id
    ).map { updatedId =>
      updatedId.flatMap { cm =>
        find(
          id = cm.campaign_id,
          teamId = cm.team_id
        )
      }
    }
  }

  def updateMaxEmailsPerDay(
                             campaignId: CampaignId,
                             max_emails_per_day: MaxEmailsPerDay,
                             team_id: TeamId
                           )(using logger: SRLogger): Try[Either[UpdateMaxEmailsPerDayError, Option[CampaignWithStatsAndEmail]]] = Try {

    val validateMaxEmails = CampaignService.validateMaxEmails(
      max_emails_per_day = max_emails_per_day
    )

    validateMaxEmails match {
      case Failure(err) =>

        Left(UpdateMaxEmailsPerDayError.ValidationFailed(err))

      case Success(maxEmailsPerDay) =>

        campaignDAO.updateMaxEmailsPerDay(
          campaignId = campaignId,
          team_id = team_id,
          maxEmailsPerDay = maxEmailsPerDay
        ) match {
          case Failure(exception) =>
            logger.fatal(s"[CampaignService] updateMaxEmailsPerDay: error while update in db c_id: ${campaignId}, t_id: ${team_id} error message : ${exception.getMessage}")
            Left(UpdateMaxEmailsPerDayError.ErrorWhileUpdation(exception))

          case Success(None) =>
            Left(UpdateMaxEmailsPerDayError.NoneReturnedWhileUpdate)

          case Success(Some(cm)) =>
            Right(find(
              id = cm.campaign_id,
              teamId = cm.team_id
            ))
        }
    }
  }

  // SENDER_ROTATION
  // called from -
  // CampaignController.updateEmailSettingsV2
  def updateEmailSettingsV2(
    id: CampaignId,
    teamId: TeamId,
    data: List[CampaignEmailSettingsV2],
    campaign_status: CampaignStatus,
    planID: PlanID,
    orgId: OrgId
)(using logger: SRLogger): Try[Either[CampaignAddEmailChannelError, Option[CampaignWithStatsAndEmail]]] = {

    data.foreach { cs =>
        val cacheIdData = EmailSettingId(cs.receiver_email_settings_id)
        emailSettingJedisService.delete(cacheIdData)
    }

    (for {
        previousEmailDetails: List[CampaignEmailSettings] <- if (campaign_status == CampaignStatus.RUNNING) {
            campaignDAOService.getCampaignSenderEmailsWithSession(id, teamId)
        } else Success(List())


        daoResult: Either[CampaignAddEmailChannelError, Option[CampaignIdAndTeamId]] <- campaignDAOService.updateEmailSettingsV2(
          id = id,
          data = data,
          team_id = teamId,
          planID = planID,
          orgId = orgId
        ) match {
            case Success(Left(error)) => Success(Left(error))
            case Success(Right(opt)) => Success(Right(opt))
            case Failure(e) => Success(Left(CampaignAddEmailChannelError.DbFailure(e)))
        }


        updatedId <- daoResult match {
            case Left(error) => Success(Left(error))
            case Right(optCampaignIdAndTeam) => Try {
                optCampaignIdAndTeam.flatMap { cm =>
                    val res: Try[Seq[EmailSettingId]] = for {
                        id1 <- campaignDAO.updatePrevEmailUseStatus(
                            email_ids = previousEmailDetails.map(_.sender_email_setting_id),
                            teamId = teamId
                        )
                        id2 <- campaignDAO.updateEmailUseStatus(
                            email_ids = previousEmailDetails.map(_.sender_email_setting_id),
                            teamId = teamId,
                            status = true
                        )
                    } yield id2

                    res match {
                        case Failure(e) =>
                            logger.error(s"Error while changing email used_in_campaign status", err = e)
                            None
                        case Success(_) =>
                            find(cm.campaign_id, cm.team_id).orElse(None)
                    }
                }
            }.map(opt => Right(opt))
              .recover { case e => Left(CampaignAddEmailChannelError.DbFailure(e)) }
        }

    } yield updatedId).recover {
        case e: Throwable =>
            logger.error("Unexpected error in service layer", e)
            Left(CampaignAddEmailChannelError.DbFailure(e))
    }
}
    /**
   * Date: Feb-17-2023
   * Todo: create a different case class for recieving the data from frontend instead of channelSettingData
   * then pass team_id and campaign_id from controller to here as value_classes.
   */

  def updateAccountSetting(
                            channel_setting_data: ChannelSettingData,
                            team_id: Long,
                            campaign_id: Long
                          )(implicit ec: ExecutionContext,
                            logger: SRLogger
                          ): Future[Either[ChannelSetupError, List[ChannelSettingData]]] = {

    if (channel_setting_data.team_id.id != team_id || channel_setting_data.campaign_id.id != campaign_id) {
      logger.info(s"[CampaignService] updateAccountSetting: invalid team_id ${channel_setting_data.team_id.id} or campaign_id ${channel_setting_data.campaign_id.id} provided and logged in as ${team_id} and ${campaign_id}")
      Future.successful(Left(ChannelSetupError.InvalidDataProvided)) // Todo Please check for the case if owner tries to change id for other teams,
    }
    else if (channel_setting_data.channel_setting_uuid.uuid.isEmpty) {
      logger.info(s"[CampaignService] updateAccountSettign: empty uuid is sent data: ${channel_setting_data} team_id: ${team_id} campaign_id: ${campaign_id}")
      Future.successful(Left(ChannelSetupError.NoUuidPresentError))
    }
    else {

      for {

        isCampaignChannelSettingsExists: IsUpdate <- campaignDAO.isCampaignChannelSettingsExists(
          team_id = channel_setting_data.team_id,
          campaign_id = channel_setting_data.campaign_id,
          channel_type = channel_setting_data.channel_type
        )

        campaignData: Option[CampaignIdAndTeamId] <- {

          campaignDAO.updateOrInsertChannelSettingInCampaign(
            team_id = channel_setting_data.team_id,
            campaign_id = channel_setting_data.campaign_id,
            channelType = channel_setting_data.channel_type,
            channel_settings_uuid = channel_setting_data.channel_setting_uuid,
            isUpdate = isCampaignChannelSettingsExists
          )
        }

        //UNIT
        _ <- Future.fromTry {
          campaignData.map { em =>
            //USE_CASE_scheduleForUpdateWhenCampaignIsUpdated
            campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated(
              campaignId = CampaignId(em.campaign_id),
              teamId = TeamId(em.team_id)
            ) match {
              case Success(value) => Success(value) //DO NOTHING
              case Failure(exception) =>
                logger.fatal(s"Failed scheduleForUpdateWhenCampaignIsUpdated", exception)
                Failure(exception)
            }
          }.getOrElse(Success({}))

        }

        campaignDataV2: Either[ChannelSetupError, List[ChannelSettingData]] <- campaignData match {
          case None =>
            logger.error(s"[CampaignService] updateAccountSetting: Db returned None while updating the account setting data provided was ${channel_setting_data}")
            Future.successful(Left(ChannelSetupError.ErrorWhileUpdatingInDb))

          case Some(campaignIdAndTeamId) =>
            channelSettingService.getChannelAccountSetting(
              campaign_id = campaignIdAndTeamId.campaign_id,
              team_id = campaignIdAndTeamId.team_id
            ).map(rs => Right(rs))
        }


      } yield {

        campaignDataV2

      }
    }
  }


  def updateOptOutSettings(
                            id: Long,
                            data: CampaignOptOutSettings,
                            orgId: Long,
                            team_id: TeamId
                          )(using Logger: SRLogger): Try[Option[CampaignWithStatsAndEmail]] = {

    campaignDAO.updateOptOutSettings(
      id = id,
      data = data,
      orgId = orgId,
      team_id = team_id
    ).map { updatedId =>
      updatedId.flatMap(cm => find(
        id = cm.campaign_id,
        teamId = cm.team_id
      )
      )
    }
  }

  def updateAppendFollowUps(id: Long, data: CampaignFollowUpSetting, team_id: TeamId)(using Logger: SRLogger): Try[Option[CampaignWithStatsAndEmail]] = {
    campaignDAO.updateAppendFollowUps(
      id = id,
      data = data,
      team_id = team_id
    ).map { updatedId =>
      updatedId.flatMap(cm => find(
        id = cm.campaign_id,
        teamId = cm.team_id
      )
      )
    }
  }

  //Commenting the below find function since its not used anywhere
  //  def find(
  //            id: Long,
  //            permittedAccountIds: Seq[Long],
  //            teamId: Long,
  //            account: Option[Account]
  //          ): Option[CampaignWithStatsAndEmail] = {
  //    campaignDAO.find(
  //      id = id,
  //      permittedAccountIds = permittedAccountIds,
  //      teamId = teamId,
  //      account = account
  //    ).map{ campaign =>
  //      CampaignService.getErrorForCampaign(
  //        campaign = campaign
  //      )
  //    }
  //  }

  def findBasicDetailsWithPermission(
                                      id: Long,
                                      permittedAccountIds: Seq[Long],
                                      teamId: Long,
                                    ): Try[Option[CampaignBasicDetails]] = {

    campaignDAO
      .findBasicDetails(
        id = id,
        permittedAccountIds = permittedAccountIds,
        teamId = teamId,
      )
  }

  def countProspects(campaignId: Long, team_id: TeamId): Try[Option[Long]] = {
    campaignDAO.countProspects(
      campaignId = campaignId,
      team_id = team_id
    )
  }

  def findAllActiveSenderEmailSettingsPartOfThisCampaign(emailSettingIds: Seq[EmailSettingId], team_id: TeamId, campaignId: CampaignId): Seq[EmailSettingId] = {
    campaignDAO.findAllActiveSenderEmailSettingsPartOfThisCampaign(
      emailSettingIds = emailSettingIds,
      team_id = team_id,
      campaignId = campaignId
    )
  }

  def getCampaignEmailSettingsForDuplicateCampaign(
                                                    campaignId: Long,
                                                    settings: CampaignSettings,
                                                    permittedAccountIds: Seq[Long],
                                                    team_id: Long
                                                  ): Try[List[CampaignEmailSettings]] = {

    campaignDAOService.getCampaignSenderEmailsWithSession(
      campaignId = CampaignId(campaignId),
      teamId = TeamId(team_id)
    ).map { campaign_email_settings =>
      val senderEmailSettingsIds: List[EmailSettingId] = settings
        .campaign_email_settings
        .map(_.sender_email_setting_id)

      val receiverEmailSettingIds: List[EmailSettingId] = settings
        .campaign_email_settings
        .map(_.receiver_email_setting_id)

      val emailSettingIds: List[EmailSettingId] = (senderEmailSettingsIds ++ receiverEmailSettingIds).distinct

      val emailSettingsWithPermissions: Seq[EmailSetting] = emailSettingDAO.find(
        ids = emailSettingIds,
        permittedAccountIds = permittedAccountIds,
        teamId = team_id
      )
      val allowed_email_settings_ids: Seq[Long] = emailSettingsWithPermissions
        .flatMap(_.id)
        .map(_.emailSettingId)

      val allowed_campaign_email_settings: List[CampaignEmailSettings] = campaign_email_settings
        .filter(ces =>
          allowed_email_settings_ids
            .contains(ces.sender_email_setting_id.emailSettingId) &&
            allowed_email_settings_ids
              .contains(ces.receiver_email_setting_id.emailSettingId)
        )


      allowed_campaign_email_settings.map { ses =>
        CampaignEmailSettings(
          campaign_id = CampaignId(0L),
          sender_email_setting_id = ses.sender_email_setting_id,
          receiver_email_setting_id = ses.receiver_email_setting_id,
          sender_email = ses.sender_email,
          receiver_email = ses.receiver_email,
          team_id = TeamId(team_id),
          uuid = CampaignEmailSettingsUuid("temp_uuid"),
          id = ses.id,
          max_emails_per_day_from_email_account = ses.max_emails_per_day_from_email_account,
          signature = ses.signature,
          error = ses.error,
          from_name = ses.from_name
        )
      }
    }
  }

  def findCampaignForCampaignUtilsOnly(id: Long, teamId: TeamId): Option[Campaign] = {
    campaignDAO.findCampaignForCampaignUtilsOnly(
      id = id,
      teamId = teamId
    )
  }

  //  def findBasicDetails(id: Long): Option[CampaignBasicDetails] = {
  //    campaignDAO.findBasicDetails(
  //      id = id
  //    )
  //  }

  def findBasicDetails(id: Long, teamId: TeamId): Try[Option[CampaignBasicDetails]] = {
    campaignDAO.findBasicDetails(
      id = id,
      teamId = teamId
    )
  }

  def findScheduledCampaignsToStart(): Try[List[CampaignIdAndTeamId]] = {
    campaignDAO.findScheduledCampaignsToStart()
  }

  def getWarmupSettings(campaignId: Long, team_id: TeamId)
  : Try[Option[StartCampaignWarmup]]
  = {
    campaignDAO.getWarmupSettings(campaignId = campaignId, team_id = team_id)
  }

  def findBasicDetailsBatch(
                             ids: Seq[Long],
                             teamId: Long
                           ): List[CampaignJustIdNameV2] = {
    campaignDAO.findBasicDetailsBatch(
      ids = ids,
      teamId = teamId
    )
  }

  def getCampaignAndProspectByConversationId(team_id: Long,
                                             campaign_id: Long,
                                             conversation_id: Long,
                                             Logger: SRLogger
                                            ): Try[Option[ConversationCampaignAndProspect]] = {
    val campaign_prospects = campaignDAO.getCampaignAndProspectByConversationId(
      team_id = team_id,
      campaign_id = campaign_id,
      conversation_id = conversation_id,
      Logger = Logger
    )
    campaign_prospects
      .map(convCampaignProspectOpt => {

        convCampaignProspectOpt
          .map(convCampaignProspect => {
            //If data is successfully fetched from DB, applied logic to get values for required information.
            val showReschedulingOption: Boolean = convCampaignProspect.campaignProspectAllowReschedulingOption && Helpers.checkProspectCategoryAutoReplyAndOoo(convCampaignProspect.reply_type)

            /*
            To check if prospect is active or not --->
            * If prospect is completed --> paused (inactive)
            * If prospect is not completed -->
              -> there is no rescheduling time set --> active
              -> rescheduling time is set --> inactive till rescheduled time
          */
            val prospectStatus = if (convCampaignProspect.prospect_is_completed) false
            else {
              convCampaignProspect.will_resume_at match {
                case None => true
                case Some(_) => false
              }
            }

            val result = ConversationObject.ConversationCampaignAndProspect(
              prospect_id = convCampaignProspect.prospect_id,
              campaign_id = convCampaignProspect.campaign_id,
              campaign_name = convCampaignProspect.campaign_name,
              campaign_tz = convCampaignProspect.campaign_tz,
              show_rescheduling_option = showReschedulingOption,
              prospect_is_active = prospectStatus,
              will_resume_at = convCampaignProspect.will_resume_at,
              will_resume_at_tz = convCampaignProspect.will_resume_at_tz,
              prospect_uuid = convCampaignProspect.prospect_uuid,
              campaign_uuid = convCampaignProspect.campaign_uuid
            )
            result
          })
      })


  }


  def duplicateCampaign(
                         ta: TeamMember,
                         loggedinAccount: Account,
                         campaign: Campaign,
                         permittedAccountIds: Seq[Long],
                         version: String
                       )(implicit ec: ExecutionContext, materializer: Materializer, ws: WSClient, Logger: SRLogger): Future[Either[DuplicateCampaignError, CampaignWithStatsAndEmail]] = {
    val campaignIds = findAllBasic(
      permittedAccountIds_VIEW_CAMPAIGNS = Seq(ta.user_id), // This is wrong, ignoring because we only need the editable "campaignIds" here
      permittedAccountIds_EDIT_CAMPAIGNS = Seq(ta.user_id),
      loggedinAccountId = loggedinAccount.internal_id,
      is_campaign_inbox = None,
      teamId = ta.team_id
    ).map(_.id)
    val currentMaxCampaignId = if (campaignIds.nonEmpty) campaignIds.max else 0


    val old_steps = campaignDAOService.findOrderedSteps(
      campaignId = campaign.id,
      teamId = TeamId(id = campaign.team_id)
    )

    campaign.head_step_id match {

      case None => Future(Left(DuplicateCampaignError.NoStepsFoundError))

      case Some(headStepId) =>

        val permittedAccountIdsForEmailSetting = PermissionMethods.getPermittedAccountIdsForAccountAndPermission(
          loggedinAccount = loggedinAccount,
          actingAccountId = ta.user_id,
          actingTeamId = ta.team_id,
          version = version: String,
          Logger = Logger
        )(PermType.EDIT_CHANNELS)


//        getCampaignEmailSettingsForDuplicateCampaign(
//          campaignId = campaign.id,
//          settings = campaign.settings,
//          permittedAccountIds = permittedAccountIdsForEmailSetting,
//          team_id = campaign.team_id
//        ) match {
//          case Failure(exception) =>
//            Future.successful(Left(DuplicateCampaignError.DbFailure(exception)))
//
//          case Success(campaign_email_settings) =>

            createCampaign(
              orgId = loggedinAccount.teams.head.org_id,
              accountId = ta.user_id,
              teamId = ta.team_id,
              taId = ta.ta_id,
              data = CampaignCreateForm(name = Some(s"${campaign.name} Duplicate $currentMaxCampaignId${Random.nextInt(100)}"),
                timezone = Some(campaign.settings.timezone),
                campaign_owner_id = Some(campaign.account_id),
                campaign_type = campaign.settings.campaign_type
              ),
              campaignSettings = Some(campaign.settings.copy(
                campaign_email_settings = List(),
                campaign_linkedin_settings = List(),
                campaign_call_settings = List(),
                campaign_whatsapp_settings = List(),
                campaign_sms_settings = List()
              )),
              permittedAccountIdsForEditCampaigns = permittedAccountIds,
              ownerFirstName = loggedinAccount.first_name.get
            ) flatMap {

              case Left(e) =>
                Future.successful(Left(DuplicateCampaignError.CreateCampaignError(e)))

              case Right(newCampaign) =>

                val oldCampaignOrderedSteps = CampaignStepDAO.getOrderedSteps(old_steps, headStepId)

                var lastCreatedStepId: Long = 0

                var oldStepsForDripCheck: List[Long] = List()
                var newStepsForDripCheck: List[Long] = List()
                val newlyCreatedOrderedSteps = Try {
                  oldCampaignOrderedSteps.map { step =>

                    val stepId = step.id
                    val variants = step.variants

                    val parentId = lastCreatedStepId
                    oldStepsForDripCheck = oldStepsForDripCheck ++ List(stepId)
                    campaignStepDAO.create(
                      campaignId = newCampaign.id,
                      headStepId = newCampaign.head_step_id,
                      data = CampaignStepVariantCreateOrUpdate(
                        parent_id = parentId,
                        step_data = variants.head.step_data,
                        step_delay = step.delay,
                        notes = variants.head.notes,
                        priority = variants.head.priority,
                      ),
                      teamId = TeamId(newCampaign.team_id)
                    ) match {

                      case Failure(e) =>
                        Logger.fatal(s"Error while creating step: $stepId :: $parentId :: label: ${step.label}", err = e)
                        throw e

                      case Success(None) =>
                        Logger.fatal(s"Error while creating step:: None : step: $stepId :: $parentId")
                        throw new Exception(s"no step created")

                      case Success(Some(newStep)) =>
                        newStepsForDripCheck = newStepsForDripCheck ++ List(newStep.id)

                        // NOTE: variants must be created in the same order as that of previous campaign's step
                        // and CampaignStepVariant.create is a Future, so we are using Helpers.seqFutures
                        Helpers.seqFutures(variants) { variant =>

                            campaignStepVariantDAO.create(
                              orgId = loggedinAccount.org.id,
                              stepId = newStep.id,
                              campaignId = newStep.campaign_id,
                              data = CampaignStepVariantCreateOrUpdate(
                                parent_id = parentId,
                                step_data = variant.step_data,
                                step_delay = variant.step_delay.toInt,
                                notes = variant.notes,
                                priority = variant.priority,
                                template_id = variant.template_id
                              ))

                          }.map(variantsAdded => {

                            if (variantsAdded.isEmpty) {
                              Logger.fatal(s"Error while adding variants:: None : step: $stepId :: ${campaign.id}")
                              throw new Exception(s"no variant created")
                            } else {
                              Logger.info(s"added variants:: ${variantsAdded.filter(_.isDefined).map(_.get.id)} :for step: $stepId :: ${campaign.id}")

                            }

                          })
                          .recover { case e => {
                            Logger.fatal(s"Error while adding variants: step: $stepId :: ${campaign.id}", err = e)
                            throw e
                          }
                          }

                        lastCreatedStepId = newStep.id
                        newStep
                    }

                  }
                }

                newlyCreatedOrderedSteps match {

                  case Failure(e) =>
                    Future.successful(Left(DuplicateCampaignError.DbFailure(e)))


                  case Success(newOrderedSteps) =>

                    // for drip we need to add edges nodes and head_step_id so it works,
                    // so we need to take all that data from old campaign and replace all the step_ids with the corresponding
                    // ids from the new campaign
                    val addingDrip = for {
                      oldCampaignDripData <- Future.fromTry {
                        val oldDripData = if (campaign.settings.campaign_type != Drip) {
                          Success(None)
                        } else {
                          campaignDAO.getDripCampaignData(
                            campaignId = CampaignId(campaign.id),
                            teamId = TeamId(campaign.team_id)
                          )
                        }

                        oldDripData
                      }
                      adding_drip_data <- {
                        if (oldCampaignDripData.isEmpty) {
                          Future.successful(0)
                        } else {


                          val res = NextStepFinderForDrip.getDuplicateCampaign(
                            old_head_node_id = oldCampaignDripData.get.head_node_id,
                            old_edges = oldCampaignDripData.get.edges,
                            old_nodes = oldCampaignDripData.get.nodes,
                            ordered_step_ids_for_old_campaign = oldStepsForDripCheck,
                            ordered_step_ids_for_new_campaign = newStepsForDripCheck
                          )

                          res.flatMap { newDripData =>
                            Future.fromTry {
                              campaignDAO.saveDripCampaign(
                                dripCampaignForm = DripCampaignForm(
                                  nodes = Json.toJson(newDripData.nodes),
                                  edges = Json.toJson(newDripData.edges),
                                  headNodeId = newDripData.head_node_id
                                ),
                                campaignId = CampaignId(newCampaign.id),
                                teamId = TeamId(newCampaign.team_id)
                              )
                            }
                          }


                        }
                      }

                    } yield {
                      adding_drip_data
                    }

                    addingDrip.map { value =>
                      updateScheduleSettings(
                        id = newCampaign.id,
                        data = UpdateCampaignScheduleSettings(

                          timezone = campaign.settings.timezone,
                          daily_from_time = campaign.settings.daily_from_time,
                          daily_till_time = campaign.settings.daily_till_time,
                          sending_holiday_calendar_id = campaign.settings.sending_holiday_calendar_id,
                          days_preference = campaign.settings.days_preference
                        ),
                        team_id = TeamId(campaign.team_id)
                      ) match {

                        case Failure(e) =>
                          Left(DuplicateCampaignError.DbFailure(e))

                        case Success(None) =>
                          Left(DuplicateCampaignError.CampaignWithStatsAndEmailNotFound)

                        case Success(Some(_)) =>


                          updateOtherSettings(id = newCampaign.id,
                            data = CampaignOtherSettings(
                              email_priority = campaign.settings.email_priority,
                              max_emails_per_day = campaign.settings.max_emails_per_day,
                              // mark_completed_after_days = c.settings.mark_completed_after_days,
                              open_tracking_enabled = campaign.settings.open_tracking_enabled,
                              click_tracking_enabled = campaign.settings.click_tracking_enabled,
                              enable_email_validation = Some(campaign.settings.enable_email_validation),
                              add_prospect_to_dnc_on_opt_out = campaign.settings.add_prospect_to_dnc_on_opt_out,
                              send_plain_text_email = campaign.settings.send_plain_text_email.getOrElse(false),
                              sending_mode = campaign.settings.sending_mode
                            ),
                            team_id = TeamId(id = newCampaign.team_id)) match {

                            case Failure(e) =>
                              Left(DuplicateCampaignError.DbFailure(e))

                            case Success(None) =>
                              Left(DuplicateCampaignError.CampaignWithStatsAndEmailNotFound)

                            case Success(Some(_)) =>

                              updateOptOutSettings(
                                id = newCampaign.id,
                                data = CampaignOptOutSettings(opt_out_msg = campaign.settings.opt_out_msg, opt_out_is_text = campaign.settings.opt_out_is_text),
                                orgId = loggedinAccount.org.id,
                                team_id = TeamId(id = newCampaign.team_id)
                              ) match {

                                case Failure(e) =>
                                  Left(DuplicateCampaignError.DbFailure(e))

                                case Success(None) =>
                                  Left(DuplicateCampaignError.CampaignWithStatsAndEmailNotFound)

                                case Success(Some(updateCampaignOptout)) =>
                                  Right(updateCampaignOptout)

                              }


                          }


                      }
                    }


                }

            }
//        }


    }
  }

  def findAllBasic(
                    permittedAccountIds_VIEW_CAMPAIGNS: Seq[Long],
                    permittedAccountIds_EDIT_CAMPAIGNS: Seq[Long],
                    loggedinAccountId: Long,
                    is_campaign_inbox: Option[Boolean],
                    teamId: Long
                  ): Seq[CampaignJustIdName] = {
    campaignDAO.findAllBasic(
      permittedAccountIds_VIEW_CAMPAIGNS = permittedAccountIds_VIEW_CAMPAIGNS,
      permittedAccountIds_EDIT_CAMPAIGNS = permittedAccountIds_EDIT_CAMPAIGNS,
      loggedinAccountId = loggedinAccountId,
      is_campaign_inbox = is_campaign_inbox,
      teamId = teamId
    )
  }


  def findAll(
               permittedAccountIds: Seq[Long],
               loggedinAccountId: Long,
               showCampaignTags: Boolean,
               teamId: Long,
               includeArchived: Boolean,
               account: Option[Account]

               //    searchQuery: Option[ProspectsQuery]

             )(
               implicit logger: SRLogger
             ): Try[Seq[CampaignWithStatsAndEmail]] = {

    // extracting doNotContactCategoryId before so that it doesn't get called multiple times
    prospectDAOService.getProspectCategoryId(
      teamId = TeamId(id = teamId),
      text_id = ProspectCategory.DO_NOT_CONTACT,
      account = account
    ) match {

      case Failure(err) =>
        logger.fatal(msg = s"[findAll prospectDAOService.getProspectCategoryId] teamId::$teamId text_id::${ProspectCategory.DO_NOT_CONTACT}", err = err)
        Failure(err)

      case Success(doNotContactCategoryId: ProspectCategoryId) =>

        replySentimentDAOService.getUuidsForTeamIdAndReplySentimentType(
          team_id = teamId,
          replySentimentType = ReplySentimentType.Positive
        ).flatMap { listOfPositiveUuid =>

          Try {

            campaignDAO.findAll(
              doNotContactCategoryId = doNotContactCategoryId.id,
              permittedAccountIds = permittedAccountIds,
              loggedinAccountId = loggedinAccountId,
              showCampaignTags = showCampaignTags,
              teamId = teamId,
              includeArchived = includeArchived,
              listOfPositiveUuid = listOfPositiveUuid
            ).map { campaign =>

              getCampaignStatsById(
                cId = campaign.id,
                campaignStatus = campaign.status,
                campaignCreatedAt = campaign.created_at,
                teamId = teamId,
                acc = AccountData.AccountObj(account.get),
                campaign_has_email_step = campaign.campaign_has_email_step,
                Logger = logger
              )(campaignCacheService)
                .map(stats => {
                  CampaignService.getErrorForCampaign(
                    campaign = CampaignWithStatsAndEmail(
                      id = campaign.id,
                      uuid = campaign.uuid,
                      is_archived = campaign.is_archived,
                      team_id = campaign.team_id,
                      shared_with_team = campaign.shared_with_team,
                      name = campaign.name,
                      owner_name = campaign.owner_name,
                      owner_email = campaign.owner_email,
                      owner_id = campaign.owner_id,
                      status = campaign.status,
                      tags = campaign.tags,
                      spam_test_exists = campaign.spam_test_exists,
                      warmup_is_on = campaign.warmup_is_on,

                      stats = stats,

                      head_step_id = campaign.head_step_id,

                      ai_generation_context = campaign.ai_generation_context,

                      settings = campaign.settings,

                      created_at = campaign.created_at,

                      error = campaign.error
                    )
                  )
                })
                .get

            }
          }
        }

    }
  }

  def checkIfAccountHasArchivedCampaign(permittedAccountIds: Seq[Long], teamId: Long): Boolean = {
    campaignDAO.checkIfAccountHasArchivedCampaign(
      permittedAccountIds = permittedAccountIds,
      teamId = teamId
    )

  }

  def query(
             accountIds: Seq[Long],
             teamId: Long,
             orgId: Long,
             loggedinAccountId: Long,
             data: SearchQuery,
             account: Account,
             Logger: SRLogger
           ): Try[(Seq[CampaignBasicInfo], Boolean)] = {
    val rowsPerPage = AppConfig.AllCampaignsPage.rowsPerPage
    val allCampaigns = campaignDAO.query(
      accountIds = accountIds,
      teamId = teamId,
      orgId = orgId,
      loggedinAccountId = loggedinAccountId,
      data = data,
      account = account,
      Logger = Logger
    ).map(_.map { campaign =>
      CampaignService.getErrorForCampaign(
        campaign = campaign
      )
    })

    allCampaigns.map { campaigns =>
      val hasMore = campaigns.length > rowsPerPage
      if (hasMore) {
        (campaigns.dropRight(1), hasMore)
      } else {
        (campaigns, hasMore)
      }
    }
  }

  def getNextLinkForPagination(
                                campaigns: List[CampaignBasicInfo],
                                limit: Int,
                                search_params: SearchParams
                              ): NextLinkData = {

    val has_more = campaigns.length > limit
    val timeline = search_params.range

    timeline match {
      case Range.Before(_) =>

        val res_campaigns = if (has_more) {
          campaigns.slice(0, limit)
        } else {
          campaigns
        }
        val next = if (has_more) {
          Some(res_campaigns.last.created_at.toInstant.getMillis)
        }
        else {
          None
        }

        NextLinkData(
          response_data = res_campaigns,
          next = next
        )

      case Range.After(_) =>

        val res_campaigns = if (has_more) {

          campaigns.slice(1, limit + 1)

        } else {
          campaigns
        }
        val next = if (has_more) {
          Some(res_campaigns.head.created_at.toInstant.getMillis + 1)
        }
        else {
          None
        }
        NextLinkData(
          response_data = res_campaigns,
          next = next
        )
    }

  }

  def findCampaignsV3(
                       accountIds: Seq[Long],
                       teamId: Long,
                       orgId: Long,
                       loggedinAccountId: Long,
                       search_params: SearchParams,
                       account: Account,
                       Logger: SRLogger
                     ): Try[CampaignLisitingApiResult] = {


    campaignDAO.getCampaignBasicInfoV3(
      accountIds = accountIds,
      teamId = teamId,
      orgId = orgId,
      loggedinAccountId = loggedinAccountId,
      search_params = search_params,
      Logger = Logger
    ) match {

      case Failure(exception) =>
        Logger.error("findCampaignsV3 server error::", err = exception)
        Failure(exception = exception)

      case Success(campaigns) =>
        if (campaigns.isEmpty) {

          Success(CampaignLisitingApiResult(
            data = campaigns,
            links = NavigationTime(
              next = None
            )
          ))

        } else {

          val sortedCampaigns = campaigns.sortBy(_.created_at)(JodaTimeUtils.dateTimeOrdering)
            .reverse

          val limit = AppConfig.AllCampaignsPage.rowsPerPage

          val nextLinkData: NextLinkData = getNextLinkForPagination(
            campaigns = sortedCampaigns,
            limit = limit,
            search_params = search_params
          )

          val all_campaigns = nextLinkData.response_data.map(campaign =>
            CampaignService.getErrorForCampaign(
              campaign = campaign
            ))

          Success(CampaignLisitingApiResult(
            data = all_campaigns,
            links = NavigationTime(
              next = nextLinkData.next
            )
          ))
        }
    }


  }

  def getCampaignStatsFromDb(
                              cId: Long,
                              acc: AccountData,
                              teamId: Long,
                              listOfPositiveUuid: List[ReplySentimentUuid],
                              campaign_has_email_step: Boolean,
                              Logger: SRLogger
                            ): Try[AllCampaignStats] = {
    for {

      acc: Account <- acc match {
        case AccountData.AccountObj(account) => Try {
          account
        }
        case AccountData.AccountId(account_id) => accountService.find(id = account_id)(
          logger = Logger
        )
      }

      doNotContactCategoryId: Long <- Try {
        Helpers.getCategoryByTextId(
          team_id = teamId,
          textId = ProspectCategory.DO_NOT_CONTACT,
          account = acc,
          Logger = Logger
        ).get.id
      }

      campaignStats: AllCampaignStats <- campaignDAO.getCampaignStatsById(
        cId = cId,
        teamId = teamId,
        doNotContactCategoryId = doNotContactCategoryId,
        listOfPositiveUuid = listOfPositiveUuid,
        Logger = Logger,
        campaignHasEmailStep = campaign_has_email_step
      )
    } yield campaignStats
  }

  def getCampaignStatsFromCacheOrDB(
                                     cId: Long,
                                     teamId: Long,
                                     campaignStatus: CampaignStatus,
                                     campaignCreatedAt: DateTime,
                                     acc: AccountData
                                   )(using Logger: SRLogger): Try[AllCampaignStats] = {

    campaignCacheService.getCampaignStatsById(campaignId = cId, teamId = teamId) match {

      case Some(campaignStats) =>
        Try(campaignStats)

      case None =>
        replySentimentDAOService.getUuidsForTeamIdAndReplySentimentType(
          team_id = teamId,
          replySentimentType = ReplySentimentType.Positive
        ) flatMap { listOfPositiveUuid =>

          getCampaignStatsFromDb(
            cId = cId,
            acc = acc,
            teamId = teamId,
            campaign_has_email_step = checkIfCampaignHasEmailStep(
              campaignId = CampaignId(cId),
              teamId = TeamId(id = teamId),
            ),
            listOfPositiveUuid = listOfPositiveUuid,
            Logger = Logger
          ) match {

            case Failure(err) => Failure(err)

            case Success(campaignStats) =>
              campaignCacheService.setCampaignStats(
                stats = campaignStats,
                campaignId = cId,
                campaignStatus = campaignStatus,
                campaignCreatedAt = campaignCreatedAt,
                teamId = teamId
              )
              Success(campaignStats)

          }
        }


    }

  }

  def getCampaignStatsById(
                            cId: Long,
                            campaignStatus: CampaignStatus,
                            campaignCreatedAt: DateTime,
                            teamId: Long,
                            acc: AccountData,
                            campaign_has_email_step: Boolean,
                            Logger: SRLogger
                          )(campaignCacheService: CampaignCacheService): Try[AllCampaignStats] = {
    given srLogger: SRLogger = Logger // fixme given

    acc match {

      case AccountData.AccountObj(account) =>
        if (account.org.plan.plan_type == PlanType.TRIAL) {
          replySentimentDAOService.getUuidsForTeamIdAndReplySentimentType(
            team_id = teamId,
            replySentimentType = ReplySentimentType.Positive
          )(
            logger = Logger
          ) flatMap { listOfPositiveUuid =>
            getCampaignStatsFromDb(
              cId = cId,
              acc = acc,
              teamId = teamId,
              listOfPositiveUuid = listOfPositiveUuid,
              campaign_has_email_step = campaign_has_email_step,
              Logger = Logger
            )
          }

        } else {

          getCampaignStatsFromCacheOrDB(
            cId = cId,
            campaignStatus = campaignStatus,
            campaignCreatedAt = campaignCreatedAt,
            teamId = teamId,
            acc = acc
          )

        }

      case AccountData.AccountId(acc_id) =>
        getCampaignStatsFromCacheOrDB(
          cId = cId,
          campaignStatus = campaignStatus,
          campaignCreatedAt = campaignCreatedAt,
          teamId = teamId,
          acc = acc
        )

    }
  }

  def checkIfCampaignHasEmailStep(
                                   campaignId: CampaignId,
                                   teamId: TeamId,
                                 ): Boolean = {

    val campaignsStepWithChildren: Seq[CampaignStepWithChildren] = campaignDAOService.findOrderedSteps(
      campaignId = campaignId.id,
      teamId = teamId
    )

    val campaignHasEmailStep: Boolean = campaignsStepWithChildren.exists(
      _.step_type.channelType == ChannelType.EmailChannel
    )

    campaignHasEmailStep

  }


  def pauseAllCampaignsAndUpdateEmailSettingsForEmailAccountDelete(
                                                                    emailSettingId: Long,
                                                                    team_id: TeamId)
                                                                  (using logger: SRLogger): Try[Seq[Long]] = {

    val cacheIdData = EmailSettingId(emailSettingId)

    emailSettingJedisService.delete(cacheIdData)

    campaignDAO.pauseAllCampaignsAndUpdateEmailSettingsForEmailAccountDelete(
      emailSettingId = emailSettingId,
      team_id = team_id)
  }

  def pauseAllCampaignsAndDeleteSettingIdFromCampaigns(
                                                        channelSettingUuid: ChannelSettingUuid,
                                                        team_id: TeamId,
                                                        status_changed_by: String
                                                      ): Try[Seq[CampaignId]] = {
    val dbAndSession = dbUtils.startLocalTx()

    val db = dbAndSession.db
    implicit val session = dbAndSession.session

    val deleteSettingId = for {

      deleteSettingIdFromCampaign: Seq[CampaignId] <- campaignDAO.deleteChannelSettingsIdFromCampaign(
        channelSettingsUuid = channelSettingUuid,
        team_id = team_id
      )

      pauseAllCampaigns: Seq[CampaignId] <- campaignDAO.pauseAllCampaignsWithId(
        campaignIds = deleteSettingIdFromCampaign,
        team_id = team_id,
        status_changed_by = status_changed_by
      )

    } yield {
      pauseAllCampaigns
    }

    dbUtils.commitAndCloseSession(db = db)

    deleteSettingId
  }

  def checkIfUserHasPermissionForCampaigns(
                                            campaignIds: Seq[Long],
                                            permittedAccountIds: Seq[Long],
                                            teamId: Long
                                          ): Try[Seq[Long]] =
    campaignDAO.checkIfUserHasPermissionForCampaigns(
      campaignIds = campaignIds,
      permittedAccountIds = permittedAccountIds,
      teamId = teamId
    )

  def findCampaignsForSchedulingEA(emailSettingId: Long,
                                   Logger: SrLoggerTrait,
                                   team_id: TeamId)
  : Try[Seq[CampaignForScheduling.CampaignForSchedulingEmail]] = {
    campaignDAO.findCampaignsForSchedulingEA(
      emailSettingId = emailSettingId,
      Logger = Logger,
      team_id = team_id
    )
  }


  def _updateLastScheduled(
                            campaignIds: Seq[Long],
                            Logger: SRLogger,
                            team_id: TeamId
                          ): Try[Int] = {
    val debug_updateLastScheduled = campaignDAO._updateLastScheduled(
      campaignIds = campaignIds,
      Logger = Logger,
      team_id = team_id
    )

    debug_updateLastScheduled

  }

  def deleteCampaign(
                      campaignId: Long,
                      team_id: TeamId
                    )(implicit session: DBSession): Try[Int] = {
    campaignDAO.deleteCampaign(
      campaignId = campaignId,
      team_id = team_id
    )
  }

  def addInternalSchedulerRunLog(internalSchedulerRunLog: InternalSchedulerRunLog): Try[Int] = internalSchedulerRunLogDAO.addInternalSchedulerRunLog(internalSchedulerRunLog = internalSchedulerRunLog)


  def getInternalSchedulerRunLogForLast24Hours: Try[List[InternalSchedulerRunLogReporting]] = internalSchedulerRunLogDAO.getInternalSchedulerRunLogForLast24Hours

  def checkIfCampaignInWebhooksFilter(campaignId: CampaignId,
                                      teamId: TeamId)(using Logger: SRLogger): Either[DeleteCampaignTasksError, true] = {

    campaignDAO.checkIfCampaignInWebhookFilter(
      campaignId = campaignId,
      teamId = teamId) match {

      case Failure(exception) =>
        Left(DeleteCampaignTasksError.ErrorWhileFetchingWebhooksForCampaign(e = exception))

      case Success(value) => if(value){
        Left(DeleteCampaignTasksError.CampaignAssociatedToWebhook)
      } else {
        Right(true)
      }
    }
  }


  def deleteCampaignAndAssociatedTasks(campaignId: CampaignId, teamId: TeamId)(using Logger: SRLogger): Either[DeleteCampaignTasksError, Int] = {



    /*
      23-sep-2024:
        1. we need to make this code perfect, currently tasks are getting deleted,
        2. then, we are going to delete campaign.
        3. if the we deploy in middle of execution this can break.
        4. so we will handle it later smoothly.

     */

    checkIfCampaignInWebhooksFilter(
      campaignId = campaignId,
      teamId = teamId
    ) match {

      case Left(error) =>
        Left(error)

      case Right(_) =>
        val dbAndSession = dbUtils.startLocalTx()

        val db = dbAndSession.db
        implicit val session = dbAndSession.session

        val res: Either[DeleteCampaignTasksError, Int] = deleteTaskAndEmailDirectly(
          deletion_reason = DeletionReason.DeletedCampaign,
          campaign_id = campaignId,
          team_id = teamId,
          sender_email_settings_ids = Seq(),
          extra_log_message = Some(s"deleteCampaignAndAssociatedTasks ${campaignId.id}")
        ) match {

          case Failure(e) =>

            Logger.error(s"FATAL deleteCampaignAndAssociatedTasks ::  deleteTaskAndEmailDirectly:: campaign_id :: ${campaignId.id} :: ${LogHelpers.getStackTraceAsString(e)}")
            Left(DeleteCampaignTasksError.ErrorWhileSelectAndPublish(e))


          case Success(deletedCount) =>

            deleteCampaign(campaignId = campaignId.id, team_id = teamId) match {

              case Failure(err) =>
                Logger.error(s"FATAL deleteCampaignAndAssociatedTasks ::  deleteCampaign:: campaign_id :: ${campaignId.id} :: ${LogHelpers.getStackTraceAsString(err)}")

                Left(DeleteCampaignTasksError.DeleteCampaignsSqlError(err))


              case Success(deleted_campaign) =>

                if (deleted_campaign == 1) {

                  Right(deletedCount.sum)

                } else {
                  Left(DeleteCampaignTasksError.NoCampaignFoundToDelete)
                }

            }
        }


        dbUtils.commitAndCloseSession(db = db)

        res


    }


  }


  def getCampaignsForPreEmailValidation()(using logger: SRLogger): Try[List[CampaignForValidation]] = {
    campaignDAO.getCampaignsForPreEmailValidation match {
      case Failure(err) =>
        logger.fatal("An error occurred while fetching list of campaign for pre validations", err = err)
        Failure(err)

      case Success(value) => Success(value)
    }
  }

  def updateLastPushedForPreEmailValidationAt(campaignId: Long, teamId: Long)(using logger: SRLogger): Try[Int] = {
    campaignDAO.updateLastPushedForPreEmailValidationAt(campaignId = campaignId, teamId = teamId) match {
      case Failure(err) =>
        logger.fatal("An error occurred while updating last_pushed_for_preemailvalidation_at", err = err)
        Failure(err)

      case Success(value) => Success(value)

    }
  }

  /**
   * Handles the unsubscribe process for a prospect from a campaign
   *
   * @param code The encoded unsubscribe code from the email link
   * @param reqid Optional request ID for tracking
   * @param mqDoNotContactPublish Function to publish to the DNC queue
   * @return Future with Result
   */
  def handleUnsubscribe(
                         code: String,
                         reqid: Option[String]
                       )(using ec: ExecutionContext, Logger: SRLogger): Future[Result] = {
    val logRequestId = reqid.getOrElse(StringUtils.genLogTraceId)
    val Logger = new SRLogger(logRequestId = s"$logRequestId  handleUnsubscribe ")
    Logger.info(s"unsubscribe processing :: $code")

    val successResponse = Ok("Success")

    EmailHelper.decodeBase32UnsubscribedLinkV4(code) match {
      case Failure(e) =>
        Logger.info(s"FATAL CampaignService.handleUnsubscribe opt out: campaign :: ${LogHelpers.getStackTraceAsString(e)}")
        Future.successful(successResponse)

      case Success(data) =>
        if (data.emailScheduledId == 0) {
          // emailScheduledId is 0 in the prospect previews page
          // disable unsubscribe link in preview emails
          Logger.info(s"CampaignService.handleUnsubscribe IGNORING opt out: emailScheduledId: ${data.emailScheduledId} :: $code")
          Future.successful(successResponse)
        } else {
          val traceRequestDBId: Option[Long] = reqid.flatMap(reqIdStr => {
            EmailTrackingApiRequestData.getEmailTrackingApiRequestIdFromTraceId(
              traceReqId = reqIdStr
            ).toOption.flatten
          })

          emailScheduledService.hasOptedOutV2(
            Logger = Logger,
            emailScheduledId = EmailScheduledId(data.emailScheduledId),
            traceReqId = traceRequestDBId
          ) match {
            case Failure(e) =>
              Logger.error(s"[FATAL] CampaignService.handleUnsubscribe ${LogHelpers.getStackTraceAsString(e)}")
              Future.successful(successResponse)

            case Success((count, prospectDetails)) =>
              Logger.info(s"CampaignService.handleUnsubscribe Unsubscribed successfully: emailScheduledId: ${data.emailScheduledId} :: $code")

              if (prospectDetails.isEmpty) {
                Future.successful(successResponse)
              } else {
                val d = prospectDetails.get

                val changeCategoryToDnc = if (!d.add_prospect_to_dnc_on_opt_out) Try {} else Try {

                  val doNotContactCatId: ProspectCategoryId = prospectDAOService.getProspectCategoryId(
                    teamId = TeamId(id = d.team_id),
                    text_id = ProspectCategory.DO_NOT_CONTACT,
                    account = None
                  ).get

                  //change prospect category to dnc
                  prospectUpdateCategoryTemp.updateCategoryAndCreateEvent(
                    prospectIds = Seq(d.prospect_id),
                    doerAccountId = d.account_id,
                    teamId = d.team_id,
                    accountName = d.account_name,
                    prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AdminUpdate(
                      old_prospect_category_id = None,
                      new_prospect_category_id = doNotContactCatId
                    ),
                    account = None,
                    event_created_at = DateTime.now(),
                    logger = Logger,
                    auditRequestLogId = Some(Logger.logRequestId)
                  ).get
                }

                changeCategoryToDnc match {
                  case Failure(e) =>
                    Logger.error(s"[FATAL] CampaignService.handleUnsubscribe Blacklist.create $d :: ${LogHelpers.getStackTraceAsString(e)}")
                    Future.successful(successResponse)

                  case Success(_) =>
                    Logger.info(s"CampaignService.handleUnsubscribe success Blacklist.create $d")

                    val msg = MQActivityTriggerMsgForm(
                      accountId = d.account_id,
                      teamId = d.team_id,
                      campaignId = d.campaign_id,
                      prospectIds = Seq(ProspectIdWithOldProspectDeduplicationColumn(prospectId = ProspectId(d.prospect_id), None)),
                      clickedUrl = None,
                      campaignName = d.campaign_name,
                      emailScheduledIds = None,
                      event = EventType.PROSPECT_OPTED_OUT.toString,
                      threadId = None,
                      replySentimentUuid = None
                    )

                    mqActivityTriggerPublisher.publishEvents(message = msg)
                    Future.successful(successResponse)
                }
              }
          }
        }
    }
  }


  def getPreviewProspects(
                           campaign: Campaign,
                           searchQuery: Option[String],
                           limit: Int,
                           offset: Int,
                           teamId: TeamId,
                           orgId: OrgId,
                           enable_ab_testing: Boolean,
                           camapign_email_setting_id: Option[CampaignEmailSettingsId]
                         )(implicit ec: ExecutionContext
                           , Logger: SRLogger
                         ): Future[Either[PreviewGetProspectError, Seq[ProspectListForPreview]]] = {

    for {

      campaignSteps: Seq[CampaignStepWithChildren] <- Future {
        campaignDAOService.findOrderedSteps(
          campaignId = campaign.id,
          teamId = teamId
        )
      }

      channelSettingData: List[ChannelSettingData] <- channelSettingService.getChannelAccountSetting(
        team_id = teamId.id,
        campaign_id = campaign.id
      )

      validations: Either[PreviewGetProspectError, Seq[Boolean]] <- CampaignService.validatePreviewForProspect(
        c = campaign,
        campaignSteps = campaignSteps,
        channelSettingData = channelSettingData
      ) match {
        case Failure(exception) =>

          Future.successful(Left(PreviewGetProspectError.ValidationError(err_message = exception.getMessage)))

        case Success(seq_bool) =>

          Future.successful(Right(seq_bool))

      }


    } yield {

      campaignProspectDAO.prospectsForPreviewV2(
        campaignId = CampaignId(id = campaign.id),
        searchQuery = searchQuery,
        limit = limit,
        offset = offset,
        teamId = teamId,
        orgId = orgId,
        camapign_email_setting_id = camapign_email_setting_id,
        channel_types = channelSettingData.map(_.channel_type),
//        emailNotCompulsoryEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(teamId = teamId, feature = SrRollingUpdateFeature.EmailNotCompulsory
//        )
      ) match {
        case Failure(e) =>
          Left(PreviewGetProspectError.InternalServerError(e))

        case Success(prospectForPreview) =>
          Right(prospectForPreview)

      }

    }

  }

  def getCampaignIdFromSrIdentifier(
                                     teamId: TeamId,
                                     id: SrIdentifier
                                   ): Either[GetCampaignIdFromSrIdentifierError, Long] = {

    id match {
      case SrId(id) => Right(id)
      case SrUuid(uuid) => campaignDAO.getCampaignIdForUuid(
        team_id = teamId,
        campaign_uuid = uuid
      ) match {
        case Failure(exception) =>
          Left(GetCampaignIdFromSrIdentifierError.DbFailure(exception))

        case Success(id) =>
          id match {
            case None => Left(GetCampaignIdFromSrIdentifierError.CampaignIdNotFound("Campaign id not found"))
            case Some(id) => Right(id)
          }
      }
    }
  }

  def getCampaignIdsFromUuid(
                              data: List[CampaignUuid],
                              teamId: TeamId
                            )(using logger: SRLogger): Either[GetCampaignIdFromUuidError, ValidAndInvalidCampaignUuidIdList] = {
    srUuidService.getCampaignIdFromUuid(
      campaignUuid = data,
      teamId = teamId
    ) match {
      case Failure(exception) => Left(GetCampaignIdFromUuidError.GetCampaignIdError(err = exception))
      case Success(uuidIdMap) =>
        val invalid_uuids = uuidIdMap.filter(p => p._2.isEmpty).keys.toList
        val valid_ids: Map[CampaignUuid, Option[CampaignId]] = uuidIdMap.filter(p => p._2.isDefined)

        Right(ValidAndInvalidCampaignUuidIdList(
          invalid_uuids = invalid_uuids,
          valid_campaigns_ids = valid_ids
        ))
    }
  }

  def findCampaignSettingForCampaignInbox(
                                           permittedAccountIds_EDIT_CAMPAIGNS: Seq[Long],
                                           teamId: TeamId,
                                           campaign_id: Option[CampaignId]
                                         ): Try[Seq[CampaignInboxSettingData]] = {

    campaignDAOService.findSettingsForCampaignInbox(
      permittedAccountIds_EDIT_CAMPAIGNS = permittedAccountIds_EDIT_CAMPAIGNS,
      teamId = teamId,
      campaign_id = campaign_id,
    )

  }

  // TODO: complete the implementation of this function
  def validateDripCampaign(
                            dripCampaignForm: DripCampaignForm,
                            campaignId: CampaignId,
                            teamId: TeamId
                          ): Either[String, true] = {
    Right(true)
  }

  def saveDripCampaign(
                        dripCampaignForm: DripCampaignForm,
                        campaignId: CampaignId,
                        teamId: TeamId
                      ): Try[Int] = {

    validateDripCampaign(
      dripCampaignForm = dripCampaignForm,
      campaignId = campaignId,
      teamId = teamId
    ) match {
      case Left(error) => Failure(new Exception(error))
      case Right(_) =>
        campaignDAO.saveDripCampaign(
          dripCampaignForm = dripCampaignForm.copy(
            nodes = Json.toJson(removeBulkFromNode(dripCampaignForm.nodes.as[List[JsValue]]))
          ),
          campaignId = campaignId,
          teamId = teamId
        )
    }

  }

  def getDripCampaignData(
                           campaignId: CampaignId,
                           teamId: TeamId
                         ): Try[Option[DripCampaignData]] = {

    campaignDAO.getDripCampaignData(
      campaignId = campaignId,
      teamId = teamId
    )

  }

  def getCampaignStatsWithTaskCount(
                                     teamId: TeamId,
                                     orgId: OrgId,
                                     campaignId: CampaignId,
                                     campaignStatus: CampaignStatus,
                                     campaignCreatedAt: DateTime,
                                     acc: Account,
                                     permittedIds: Seq[Long]
                                   )(implicit ec: ExecutionContext,
                                     logger: SRLogger,
                                     campaignCacheService: CampaignCacheService): Future[AllCampaignStatsWithTaskCount] = {

    for {
      campaignStatsData: AllCampaignStats <- Future.fromTry(getCampaignStatsById(
        cId = campaignId.id,
        campaignStatus = campaignStatus,
        campaignCreatedAt = campaignCreatedAt,
        teamId = teamId.id,
        campaign_has_email_step = checkIfCampaignHasEmailStep(
          campaignId = campaignId,
          teamId = teamId,
        ),
        acc = AccountData.AccountObj(account = acc),
        Logger = logger,
      )(campaignCacheService = campaignCacheService))

      taskCount: TaskCount <- taskService.getTaskFilterCount(
        team_id = teamId.id,
        orgId = orgId,
        assignee_id = None,
        reply_sentiment_uuid_opt = None, // No reply sentiment filter when called from campaigns page
        task_priority = List(),
        campaign_id = Some(campaignId.id),
        duration_from = None,
        duration_to = None,
        timeZone = acc.timezone.getOrElse("UTC"),
        doNotFetchAutomatedDueTasks = true,
        permittedAccountIds = permittedIds
      )
    } yield {
      AllCampaignStatsWithTaskCount.fromAllCampaignStats(
        stats = campaignStatsData,
        taskCount = taskCount
      )
    }
  }
}

object CampaignService {

  def calculateNextToBeScheduled(
                                  timezones: Set[String],
                                  current_time: DateTime,
                                  daily_from_time: Int,
                                  daily_till_time: Int
                                ): DateTime = {
    if (timezones.nonEmpty) {
      val campaign_start_time: DateTime = timezones.map(timezone => {
        current_time
          .withZone(DateTimeZone.forID(timezone))
          .withTimeAtStartOfDay()
          .plusSeconds(daily_from_time)
      }).min

      val campaign_end_time: DateTime = timezones.map(timezone => {
        current_time
          .withZone(DateTimeZone.forID(timezone))
          .withTimeAtStartOfDay()
          .plusSeconds(daily_till_time)
      }).max

      if (current_time.isBefore(campaign_start_time)) {
        campaign_start_time
      } else if (current_time.isAfter(campaign_end_time)) {
        campaign_start_time.plusDays(1)
      } else {
        current_time.plusMinutes(AppConfig.SchedulerConfig.CAMPAIGN_NEXT_SCHEDULED_AT_MIN_DELAY_IN_MINUTES)
      }
    } else {
      current_time.plusMinutes(AppConfig.SchedulerConfig.CAMPAIGN_NEXT_SCHEDULED_AT_MIN_DELAY_IN_MINUTES)

    }
  }

  def getCampaignPauseReason(
                              maxEmailsThatCanGoToday: Int,
                              senderRotationStats: SenderRotationStats
                            ): LowCampaignSendingVolumeReason = {
    if (maxEmailsThatCanGoToday == 0) {
      LowCampaignSendingVolumeReason.CampaignLimitReached
    } else if (maxEmailsThatCanGoToday > senderRotationStats.prospects_not_sent_any_emails + senderRotationStats.prospects_to_get_follow_up) {
      LowCampaignSendingVolumeReason.NoProspectsLeft
    } else {
      LowCampaignSendingVolumeReason.UnknownReason
    }
  }

  def getMaxEmailThatCanGoForCampaignForOneSender(
                                                   sender: SenderDataForSendingLimitNewFlow,
                                                   max_emails_per_day_per_sender_for_campaign: Int,
                                                   warmupSettingOpt: Option[CampaignWarmupSetting],
                                                   totalCampaignDurationInSeconds: Int
                                                 ): Int = {

    val average_delay = (sender.max_delay_seconds + sender.min_delay_seconds) / 2
    val total_emails_based_on_delay = totalCampaignDurationInSeconds / average_delay

    val channelOrCampaignMinDailyQuota = List(total_emails_based_on_delay, max_emails_per_day_per_sender_for_campaign, sender.quota_per_day).min


    val warmup_is_on = (
      warmupSettingOpt.isDefined
        && warmupSettingOpt
        .get
        .warmup_started_at
        .plusDays(warmupSettingOpt.get.warmup_length_in_days)
        .isAfter(DateTime.now()))

    if (warmup_is_on) {
      val warmupQuota = ChannelSchedulerTrait.getWarmupQuota(
        warmupSetting = warmupSettingOpt.get,
        channelOrCampaignMinDailyQuota = channelOrCampaignMinDailyQuota
      )
      List(warmupQuota, channelOrCampaignMinDailyQuota).min
    } else {
      channelOrCampaignMinDailyQuota
    }

  }

  def getMaxEmailThatCanGoForCampaign(
                                       senders: List[SenderDataForSendingLimitNewFlow],
                                       warmupSettingOpt: Option[CampaignWarmupSetting],
                                       max_emails_per_day_per_sender_for_campaign: Int,
                                       totalCampaignDurationInSeconds: Int
                                     ): Int = {


    senders.map(sender =>
      getMaxEmailThatCanGoForCampaignForOneSender(
        sender = sender,
        max_emails_per_day_per_sender_for_campaign = max_emails_per_day_per_sender_for_campaign,
        totalCampaignDurationInSeconds = totalCampaignDurationInSeconds,
        warmupSettingOpt = warmupSettingOpt)
    ).sum
  }

  def checkIfCampaignsAreStuckNewFlow(
                                       campaign_to_check: CampaignToCheckForSendingLimitNewFlow,
                                       senderRotationStats: SenderRotationStats
                                     ): Option[CampaignSendingVolumeLogs] = {

    val startOfCampaign = TimezoneUtils
      .getStartOfDayWithTimezone(timezone = campaign_to_check.timezone)
      .plusSeconds(campaign_to_check.daily_from_time)
    val timeSinceStartOfCampaign = new Duration(startOfCampaign, DateTime.now()).getStandardMinutes

    val pushed_for_checking_at_minutes_since_campaign_start_time_opt = timeSinceStartOfCampaign match {
      case x if x < 60 => None
      case x if x < 90 => Some(60)
      case x if x < 120 => Some(90)
      case x if x >= 120 => Some(120)
    }

    pushed_for_checking_at_minutes_since_campaign_start_time_opt match {
      case None => None
      case Some(pushed_for_checking_at_minutes_since_campaign_start_time) =>
        val endOfCampaign = TimezoneUtils
          .getStartOfDayWithTimezone(timezone = campaign_to_check.timezone)
          .plusSeconds(campaign_to_check.daily_till_time)

        val totalCampaignDurationInSeconds = Seconds.secondsBetween(startOfCampaign, endOfCampaign).getSeconds

        val maxEmailsThatCanGoToday = getMaxEmailThatCanGoForCampaign(
          senders = campaign_to_check.senders,
          warmupSettingOpt = campaign_to_check.warmupSettingOpt,
          max_emails_per_day_per_sender_for_campaign = campaign_to_check.max_emails_per_day,
          totalCampaignDurationInSeconds = totalCampaignDurationInSeconds
        )

        val current_sent_percent: Float = if (maxEmailsThatCanGoToday < 1) {
          100.toFloat
        } else (campaign_to_check.total_emails_sent_till_now.toFloat * 100) / maxEmailsThatCanGoToday.toFloat

        val possible_issue_if_any: Option[LowCampaignSendingVolumeReason] = if (current_sent_percent < 80) {
          Some(getCampaignPauseReason(maxEmailsThatCanGoToday = maxEmailsThatCanGoToday, senderRotationStats = senderRotationStats))
        } else None

        val warmup_is_on = campaign_to_check.warmupSettingOpt.isDefined && campaign_to_check.warmupSettingOpt.get.warmup_started_at.isAfter(DateTime.now())
        val actual_minutes_since_campaign_start_time_during_computing = new Duration(startOfCampaign, DateTime.now()).getStandardMinutes.toInt
        Some(CampaignSendingVolumeLogs(
          campaign_id = campaign_to_check.campaign_id,
          team_id = campaign_to_check.team_id,
          date_in_campaign_timezone = startOfCampaign.toDate.toString,
          campaign_timezone = campaign_to_check.timezone,
          consecutive_delay_in_seconds = maxEmailsThatCanGoToday / totalCampaignDurationInSeconds,
          warmup_is_on = warmup_is_on,
          warmup_started_at = campaign_to_check.warmupSettingOpt.map(_.warmup_started_at),
          warmup_starting_email_count = campaign_to_check.warmupSettingOpt.map(_.warmup_starting_email_count),
          campaign_start_time = startOfCampaign,
          pushed_for_checking_at_minutes_since_campaign_start_time = pushed_for_checking_at_minutes_since_campaign_start_time,
          actual_minutes_since_campaign_start_time_during_computing = actual_minutes_since_campaign_start_time_during_computing,
          current_sent_count = campaign_to_check.total_emails_sent_till_now,
          expected_sent_count_till_now = maxEmailsThatCanGoToday,
          current_sent_percent = current_sent_percent,
          total_scheduled_for_today_till_now = campaign_to_check.total_scheduled_for_today_till_now,
          possible_issue_if_any = possible_issue_if_any,
          added_at = DateTime.now()
        ))

    }


  }

  def getCampaignNameFromParams(parsedParams: Map[String, Vector[String]]): Option[String] = {
    parsedParams.get("name")
    match {
      case Some(name) => Some(name.head)
      case None => None
    }
  }

  def validateEmailAddressFromParams(email_address: Option[Vector[String]]): Try[Option[String]] = {
    email_address match {
      case Some(email) =>
        if (EmailValidationService.validateEmailFormat(email = email.head))
          Success(Some(email.head))
        else
          Failure(new Exception("Invalid email format"))

      case None => Success(None)
    }
  }

  def getCampaignStatusFromParams(parsedParams: Map[String, Vector[String]]): Try[Option[CampaignStatus]] = {
    parsedParams.get("status") match {
      case Some(status) =>
        CampaignStatus.fromKey(status.head).map(s => Some(s))
      case None =>
        Success(None)
    }
  }

  def extractFilenameFromUrlVoicemail(url: String): String = {
    // Split the URL by "/" and get the last part which contains the filename
    url.split("/").last
  }

  def validateParams(
                      parsedParams: Map[String, Vector[String]],
                    )(using logger: SRLogger): Try[SearchParams] = {
    for {
      name: Option[String] <- Try {
        getCampaignNameFromParams(parsedParams)
      }

      sender_email_address: Option[String] <- validateEmailAddressFromParams(email_address = parsedParams.get("sender_email_address"))

      receiver_email_address: Option[String] <- validateEmailAddressFromParams(email_address = parsedParams.get("receiver_email_address"))

      status: Option[CampaignStatus] <- getCampaignStatusFromParams(parsedParams)

      (is_first: Boolean, range: InferredQueryTimeline.Range) <- Helpers.getTimeLineRange(parsedParams = parsedParams)

      searchParams <- Success(SearchParams(
        name = name,
        sender_email_setting = sender_email_address,
        receiver_email_setting = receiver_email_address,
        status = status,
        range = range,
        is_first = is_first
      ))

    } yield {
      searchParams

    }
  }

  def validateProspectAssignData(
                                  isApiCall: Boolean,
                                  data: AssignProspectToCampaignDataV3
                                ): List[ErrorResponseProspectsAssignApi] = {
    var error_list: List[ErrorResponseProspectsAssignApi] = List()
    if (isApiCall &&
      data.prospect_ids.isEmpty
    ) {
      val err = ErrorResponseProspectsAssignApi(
        message = "Please send a list of prospect ids. Empty request found.",
        error_type = ProspectAssignErrorType.EmptyList,
        data = None
      )
      error_list = error_list.appended(err)

    }
    if (isApiCall &&
      data.prospect_ids.size > 100
    ) {

      val err = ErrorResponseProspectsAssignApi(
        message = "Limit exceeded. Cannot assign more than 100 prospects.",
        error_type = ProspectAssignErrorType.LimitExceeded,
        data = None
      )
      error_list = error_list.appended(err)

      //      Failure(new Exception("You can send upto 100 ids in prospect_ids at a time"))

    }

    error_list
  }

  def validateProspectUnAssignData(
                                    isApiCall: Boolean,
                                    data: CampaignProspectFormUuids
                                  ): List[ErrorResponseProspectsUnAssignApi] = {
    var error_list: List[ErrorResponseProspectsUnAssignApi] = List()
    if (isApiCall &&
      data.prospect_ids.isEmpty
    ) {
      val err = ErrorResponseProspectsUnAssignApi(
        message = "Please send a list of prospect ids. Empty request found.",
        error_type = ProspectUnAssignErrorType.EMPTY_LIST,
        data = None
      )
      error_list = error_list.appended(err)

    }
    if (isApiCall &&
      data.prospect_ids.size > AppConfig.assignProspectLimit
    ) {

      val err = ErrorResponseProspectsUnAssignApi(
        message = "Limit exceeded. Cannot assign more than 100 prospects.",
        error_type = ProspectUnAssignErrorType.LIMIT_EXCEEDED,
        data = None
      )
      error_list = error_list.appended(err)

    }
    error_list
  }

  def getPreviewData(
                      previous_follow_up: PreviousFollowUpData,
                      editable_body: Option[String],
                      allTrackingDomainsUsed: Seq[String]
                    ): PreviewData = {

    previous_follow_up match {

      case data: PreviousFollowUpData.AutoEmailFollowUp =>

        PreviewData.AutoEmailPreview(
          subject = data.subject,
          body = EmailHelper.disableTrackingLinks(
            body = data.body,
            allTrackingDomains = allTrackingDomainsUsed,
            isTextBody = false
          ),
          editable_body = editable_body,
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = data.base_body).take(200),
          edited = data.is_edited_preview_email,
          email_thread_id = data.email_thread_id
        )

      case data: PreviousFollowUpData.AutoEmailMagicFollowUp =>
        // FIXME: AiHyperPersonalizationService - verify this changes

        val body_ = EmailHelper.disableTrackingLinks(
          body =data.generated_body.getOrElse(""),
          allTrackingDomains = allTrackingDomainsUsed,
          isTextBody = false
        )
        val body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = data.generated_base_body.getOrElse("")).take(200)
        val generated_body = if(body_.trim.nonEmpty) Some(body_) else None

        val generated_body_preview = if(body_preview.trim.nonEmpty) Some(body_preview) else None

        PreviewData.AutoEmailMagicPreview(
          generated_subject = data.generated_subject,
          generated_body = generated_body,
          editable_body = editable_body,
          generated_body_preview = generated_body_preview,
          edited = data.is_edited_preview_email,
          email_thread_id = data.email_thread_id
        )


      case data: PreviousFollowUpData.ManualEmailMagicFollowUp =>

        // FIXME: AiHyperPersonalizationService - verify this changes

        val body_ = EmailHelper.disableTrackingLinks(
          body =data.generated_body.getOrElse(""),
          allTrackingDomains = allTrackingDomainsUsed,
          isTextBody = false
        )
        val body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = data.generated_base_body.getOrElse("")).take(200)
        val generated_body = if(body_.trim.nonEmpty) Some(body_) else None

        val generated_body_preview = if(body_preview.trim.nonEmpty) Some(body_preview) else None

        PreviewData.ManualEmailMagicPreview(
          generated_subject = data.generated_subject,
          generated_body = generated_body,
          editable_body = editable_body,
          generated_body_preview =generated_body_preview,
          edited = data.is_edited_preview_email,
          email_thread_id = data.email_thread_id
        )


      case data: PreviousFollowUpData.ManualEmailFollowUp =>

        PreviewData.ManualEmailPreview(
          subject = data.subject,
          body = EmailHelper.disableTrackingLinks(
            body = data.body,
            allTrackingDomains = allTrackingDomainsUsed,
            isTextBody = false
          ),
          editable_body = editable_body,
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = data.base_body).take(200),
          edited = data.is_edited_preview_email,
          email_thread_id = data.email_thread_id
        )


      case data: PreviousFollowUpData.LinkedinViewProfileFollowUp =>

        PreviewData.LinkedinViewProfilePreview()


      case data: PreviousFollowUpData.LinkedinConnectionRequestFollowUp =>

        PreviewData.LinkedinConnectionRequestPreview(
          body = data.body.map(b => EmailHelper.disableTrackingLinks(
            body = b,
            allTrackingDomains = allTrackingDomainsUsed,
            isTextBody = false
          )),
          body_preview = data.base_body.map(bb => EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = bb).take(200))
        )


      case data: PreviousFollowUpData.LinkedinInMailFollowUp =>

        PreviewData.LinkedinInMailPreview(
          subject = data.subject,
          body = EmailHelper.disableTrackingLinks(
            body = data.body,
            allTrackingDomains = allTrackingDomainsUsed,
            isTextBody = false
          ),
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = data.base_body).take(200)
        )


      case data: PreviousFollowUpData.LinkedinMessageFollowUp =>

        PreviewData.LinkedinMessagePreview(
          body = EmailHelper.disableTrackingLinks(
            body = data.body,
            allTrackingDomains = allTrackingDomainsUsed,
            isTextBody = false
          ),
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = data.base_body).take(200)
        )

      case data: PreviousFollowUpData.AutoLinkedinViewProfileFollowUp =>

        PreviewData.AutoLinkedinViewProfilePreview()


      case data: PreviousFollowUpData.AutoLinkedinConnectionRequestFollowUp =>

        PreviewData.AutoLinkedinConnectionRequestPreview(
          body = data.body.map(b => EmailHelper.disableTrackingLinks(
            body = b,
            allTrackingDomains = allTrackingDomainsUsed,
            isTextBody = false
          )),
          body_preview = data.base_body.map(bb => EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = bb).take(200))
        )


      case data: PreviousFollowUpData.AutoLinkedinInMailFollowUp =>

        PreviewData.AutoLinkedinInMailPreview(
          subject = data.subject,
          body = EmailHelper.disableTrackingLinks(
            body = data.body,
            allTrackingDomains = allTrackingDomainsUsed,
            isTextBody = false
          ),
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = data.base_body).take(200)
        )


      case data: PreviousFollowUpData.AutoLinkedinMessageFollowUp =>

        PreviewData.AutoLinkedinMessagePreview(
          body = EmailHelper.disableTrackingLinks(
            body = data.body,
            allTrackingDomains = allTrackingDomainsUsed,
            isTextBody = false
          ),
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = data.base_body).take(200)
        )


      case data: PreviousFollowUpData.SmsFollowUp =>

        PreviewData.SmsPreview(
          body = EmailHelper.disableTrackingLinks(
            body = data.body,
            allTrackingDomains = allTrackingDomainsUsed,
            isTextBody = false
          ),
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = data.base_body).take(200)
        )


      case data: PreviousFollowUpData.WhatsappFollowUp =>

        PreviewData.WhatsappPreview(
          body = EmailHelper.disableTrackingLinks(
            body = data.body,
            allTrackingDomains = allTrackingDomainsUsed,
            isTextBody = false
          ),
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = data.base_body).take(200)
        )


      case data: PreviousFollowUpData.CallFollowUp =>

        PreviewData.CallPreview(
          body = EmailHelper.disableTrackingLinks(
            body = data.body,
            allTrackingDomains = allTrackingDomainsUsed,
            isTextBody = false
          ),
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = data.base_body).take(200)
        )


      case data: PreviousFollowUpData.GeneralFollowUp =>

        PreviewData.GeneralPreview(
          body = EmailHelper.disableTrackingLinks(
            body = data.body,
            allTrackingDomains = allTrackingDomainsUsed,
            isTextBody = false
          ),
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = data.base_body).take(200)
        )

      case data: PreviousFollowUpData.MoveToAnotherCampaignFollowUp =>

        PreviewData.MoveToAnotherCampaignPreview()
    }
  }

  def validateMaxEmails(max_emails_per_day: MaxEmailsPerDay): Try[MaxEmailsPerDay] = Try {
    if (max_emails_per_day.max_emails_per_day > 500) {
      throw new Exception("Max emails per day can't be greater than 500")
    } else if (max_emails_per_day.max_emails_per_day < 1) {
      throw new Exception("Max emails per day can't be lower than 1")
    } else {
      max_emails_per_day
    }
  }

  def getIgnoreProspectsInOtherCampaigns(
                                          ignore_prospects_in_other_campaigns: Option[String],
                                          force_assign: Option[Boolean]
                                        ): Try[IgnoreProspectsInOtherCampaigns] = Try {
    ignore_prospects_in_other_campaigns match {
      case None =>
        if (force_assign.isDefined && !force_assign.get) {
          // to prevent breaking current integrations
          //16-May-2022 after adding a new field that is ignore_prospects_added_in_other_campaigns we are retaining the same logic as before for forced assign
          IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns
        } else {
          IgnoreProspectsInOtherCampaigns.DoNotIgnore
        }
      case Some(value) =>
        IgnoreProspectsInOtherCampaigns.fromKey(value).get

    }
  }

  def getErrorForCampaign(
                           campaign: CampaignWithStatsAndEmail
                         ): CampaignWithStatsAndEmail = {
    campaign.status match {
      case CampaignStatus.UNDER_REVIEW =>
        val error = "Your campaign is under review. Visit the campaign to learn more about its status"
        campaign.copy(
          error = campaign.error match {
            case None => Some(error)
            case Some(err) => Some(err + ", " + error)

          }
        )

      case CampaignStatus.SUSPENDED =>
        val error = "This campaign has been suspended for violating the Usage & Anti-spam Policy."
        campaign.copy(
          error = campaign.error match {
            case None => Some(error)
            case Some(err) => Some(err + ", " + error)
          }
        )
      case CampaignStatus.ON_HOLD =>
        val error = "This Campaign has no prospects to send to, update settings or add more prospects to resume sending"
        campaign.copy(
          error = campaign.error match {
            case None => Some(error)
            case Some(err) => Some(err + ", " + error)
          }
        )

      case CampaignStatus.RUNNING | CampaignStatus.STOPPED | CampaignStatus.NOT_STARTED | CampaignStatus.SCHEDULED | CampaignStatus.ARCHIVED =>
        campaign
    }
  }

  def getErrorForCampaign(
                           campaign: CampaignBasicInfo
                         ): CampaignBasicInfo = {
    campaign.status match {
      case CampaignStatus.UNDER_REVIEW =>
        val error = "Your campaign is under review. Visit the campaign to learn more about its status"
        campaign.copy(
          error = campaign.error match {
            case None => Some(error)
            case Some(err) => Some(err + ", " + error)

          }
        )

      case CampaignStatus.SUSPENDED =>
        val error = "This campaign has been suspended for violating the Usage & Anti-spam Policy."
        campaign.copy(
          error = campaign.error match {
            case None => Some(error)
            case Some(err) => Some(err + ", " + error)
          }
        )
      case CampaignStatus.ON_HOLD =>
        val error = "This Campaign has no prospects to send to, update settings or add more prospects to resume sending"
        campaign.copy(
          error = campaign.error match {
            case None => Some(error)
            case Some(err) => Some(err + ", " + error)
          }
        )

      case CampaignStatus.RUNNING | CampaignStatus.STOPPED | CampaignStatus.NOT_STARTED | CampaignStatus.SCHEDULED | CampaignStatus.ARCHIVED =>
        campaign
    }
  }

  def checkIfChannelSettingExists(
                                   channelSettingSenderDetails: List[ChannelSettingSenderDetails], // Already filtered by channel type
                                   channel_type: ChannelType
                                 ): Try[Option[ChannelSettingSenderDetails]] = Try {

    channel_type match {
      case ChannelType.EmailChannel =>
        throw new Exception(CampaignService.impossibleEmailChannelCantComeHereError)

      case ChannelType.LinkedinChannel =>
        if (channelSettingSenderDetails.isEmpty) {
          throw new Exception(CampaignService.linkedinChannelSettingsNotFoundError)
        } else {
          Some(channelSettingSenderDetails.head) // we are having only one channel setting currently
        }

      case ChannelType.WhatsappChannel =>

        if (channelSettingSenderDetails.isEmpty) {
          throw new Exception(CampaignService.whatsappChannelSettingsNotFoundError)
        } else {
          Some(channelSettingSenderDetails.head) // we are having only one channel setting currently
        }

      case ChannelType.SmsChannel =>

        if (channelSettingSenderDetails.isEmpty) {
          throw new Exception(CampaignService.smsChannelSettingsNotFoundError)
        } else {
          Some(channelSettingSenderDetails.head) // we are having only one channel setting currently
        }

      case ChannelType.CallChannel =>

        if (channelSettingSenderDetails.isEmpty) {
          throw new Exception(CampaignService.callChannelSettingsNotFoundError)
        } else {
          Some(channelSettingSenderDetails.head) // we are having only one channel setting currently
        }

      case ChannelType.GeneralChannel | ChannelType.IndependentChannel =>

        None // we are having only one channel setting currently

    }

  }

  def addNewLineBreaks(
                        body: String
                      ): String = {
    body.replaceAll("(\r\n|\n)", "<br />")
  }

  val currentVariantCantBeEmptyError = "Current variant can't be empty"
  val channelSettingSenderDataCantBeNoneError = "Channel setting sender data can't be none"
  val senderOrReceiverEmailIdNotPresentError = "Sender/Receiver email id isn't present"
  val impossibleStepTypeCanOnlyBeEmailTypesError = "Impossible case: step type can only be email types"
  val impossibleEmailChannelCantComeHereError = "Impossible case: email channel can't come here"
  val callChannelSettingsNotFoundError = "Call channel task present but setting details not found"
  val linkedinChannelSettingsNotFoundError = "Linkedin channel task present but setting details not found"
  val whatsappChannelSettingsNotFoundError = "Whatsapp channel task present but setting details not found"
  val smsChannelSettingsNotFoundError = "Sms channel task present but setting details not found"

  def sendCorrectError(
                        e: Throwable
                      ): PreviewGetStepsForProspectError = {

    e.getMessage match {
      case `currentVariantCantBeEmptyError` =>
        PreviewGetStepsForProspectError.ValidationError(e.getMessage)

      case `channelSettingSenderDataCantBeNoneError` =>
        PreviewGetStepsForProspectError.ValidationError(e.getMessage)

      case `senderOrReceiverEmailIdNotPresentError` =>
        PreviewGetStepsForProspectError.ValidationError(e.getMessage)

      case `impossibleStepTypeCanOnlyBeEmailTypesError` =>
        PreviewGetStepsForProspectError.ValidationError(e.getMessage)

      case `impossibleEmailChannelCantComeHereError` =>
        PreviewGetStepsForProspectError.ValidationError(e.getMessage)

      case `callChannelSettingsNotFoundError` =>
        PreviewGetStepsForProspectError.ValidationError(e.getMessage)

      case `linkedinChannelSettingsNotFoundError` =>
        PreviewGetStepsForProspectError.ValidationError(e.getMessage)

      case `whatsappChannelSettingsNotFoundError` =>
        PreviewGetStepsForProspectError.ValidationError(e.getMessage)

      case `smsChannelSettingsNotFoundError` =>
        PreviewGetStepsForProspectError.ValidationError(e.getMessage)

      case _ =>
        PreviewGetStepsForProspectError.InternalServerError(e)
    }

  }

  def validatePreviewForProspect(
                                  c: Campaign,
                                  campaignSteps: Seq[CampaignStepWithChildren],
                                  channelSettingData: Seq[ChannelSettingData],
                                ): Try[Seq[Boolean]] = Try {

    campaignSteps.map { step => {
      step.step_type.channelType match {
        case ChannelType.EmailChannel =>

          if (c.settings.campaign_email_settings.isEmpty) {
            throw new Exception(CampaignService.senderOrReceiverEmailIdNotPresentError)
          } else {
            true
          }


        case ChannelType.LinkedinChannel =>


          val settingIdCount = channelSettingData.count(setting => setting.channel_type == ChannelType.LinkedinChannel)

          if (settingIdCount <= 0) {
            throw new Exception(CampaignService.linkedinChannelSettingsNotFoundError)
          } else {
            true
          }


        case ChannelType.WhatsappChannel =>

          val settingIdCount = channelSettingData.count(setting => setting.channel_type == ChannelType.WhatsappChannel)
          if (settingIdCount <= 0) {
            throw new Exception(CampaignService.whatsappChannelSettingsNotFoundError)
          } else {
            true
          }


        case ChannelType.SmsChannel =>

          val settingIdCount = channelSettingData.count(setting => setting.channel_type == ChannelType.SmsChannel)
          if (settingIdCount <= 0) {
            throw new Exception(CampaignService.smsChannelSettingsNotFoundError)
          } else
            true


        case ChannelType.CallChannel =>

          val settingIdCount = channelSettingData.count(setting => setting.channel_type == ChannelType.CallChannel)
          if (settingIdCount <= 0) {
            throw new Exception(CampaignService.callChannelSettingsNotFoundError)
          } else
            true


        case ChannelType.GeneralChannel | ChannelType.IndependentChannel =>

          true

      }
    }
    }


  }

}
