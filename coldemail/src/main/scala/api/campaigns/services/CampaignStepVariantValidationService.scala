package api.campaigns.services

import api.AppConfig
import api.campaigns.CampaignStepVariantCreateOrUpdate
import api.campaigns.models.CampaignStepData

sealed trait ValidateCampaignStepVariantError

object ValidateCampaignStepVariantError {

  case object EmailBodyCantBeEmpty  extends ValidateCampaignStepVariantError
  case object CallToActionCantBeEmpty  extends ValidateCampaignStepVariantError
  case object StepDetailsCantBeEmpty  extends ValidateCampaignStepVariantError

  case object EmailSubjectCantBeEmpty extends ValidateCampaignStepVariantError
  
  case object LinkedinMessageBodyCantBeEmpty extends ValidateCampaignStepVariantError
  case object LinkedinMessageBodyTooLong extends ValidateCampaignStepVariantError
  case object LinkedinConnectionRequestAddNoteTooLong extends ValidateCampaignStepVariantError

  case object LinkedinInmailBodyCantBeEmpty extends ValidateCampaignStepVariantError
  case object LinkedinInmailSubjectTooLong extends ValidateCampaignStepVariantError
  case object LinkedinInmailBodyTooLong extends ValidateCampaignStepVariantError

  case object WhatsappMessageBodyCantBeEmpty extends ValidateCampaignStepVariantError
  case object WhatsappMessageBodyTooLong extends ValidateCampaignStepVariantError

  case object PhoneMessageBodyCantBeEmpty extends ValidateCampaignStepVariantError

  case object CallTaskBodyCantBeEmpty extends  ValidateCampaignStepVariantError

  case object GeneralTaskNotesCantBeEmpty extends ValidateCampaignStepVariantError

}

object CampaignStepVariantValidationService {

  def validateCampaignStepVariant(
                                   variant: CampaignStepVariantCreateOrUpdate
                                 ): Either[ValidateCampaignStepVariantError, CampaignStepVariantCreateOrUpdate] = {

    variant.step_data match {

      case autoEmailStep: CampaignStepData.AutoEmailStep =>
        if (autoEmailStep.body.trim == "") {
          Left(ValidateCampaignStepVariantError.EmailBodyCantBeEmpty)
        } else if (autoEmailStep.subject.trim == "") {
          Left(ValidateCampaignStepVariantError.EmailSubjectCantBeEmpty)
        } else Right(variant)

      case data: CampaignStepData.AutoEmailMagicContentStep =>

        if (data.step_context.step_details.trim == "") {
          Left(ValidateCampaignStepVariantError.StepDetailsCantBeEmpty)
        } else if (data.step_context.call_to_action.trim == "") {
          Left(ValidateCampaignStepVariantError.CallToActionCantBeEmpty)
        } else Right(variant)

      case data: CampaignStepData.ManualEmailMagicContentStep =>

        if (data.step_context.step_details.trim == "") {
          Left(ValidateCampaignStepVariantError.StepDetailsCantBeEmpty)
        } else if (data.step_context.call_to_action.trim == "") {
          Left(ValidateCampaignStepVariantError.CallToActionCantBeEmpty)
        } else Right(variant)

      case manualEmailStep: CampaignStepData.ManualEmailStep =>
        if (manualEmailStep.body.trim == "") {
          Left(ValidateCampaignStepVariantError.EmailBodyCantBeEmpty)
        } else if (manualEmailStep.subject.trim == "") {
          Left(ValidateCampaignStepVariantError.EmailSubjectCantBeEmpty)
        } else Right(variant)

      case linkedinMessageData: CampaignStepData.LinkedinMessageData =>
        /*
                 * 17-september-2024: we are removing limits for manual linkedin steps
        if (linkedinMessageData.body.trim == "") {
          Left(ValidateCampaignStepVariantError.LinkedinMessageBodyCantBeEmpty)
        }
        else if (linkedinMessageData.body.length > AppConfig.MultiChannel.linkedin_message_body_character_limit){
          Left(ValidateCampaignStepVariantError.LinkedinMessageBodyTooLong)
        }
          else

         */
          Right(variant)

      case linkedinInmailData: CampaignStepData.LinkedinInmailData =>
        /*
         * 17-september-2024: we are removing limits for manual linkedin steps
            if (linkedinInmailData.body.trim == "") {
              Left(ValidateCampaignStepVariantError.LinkedinInmailBodyCantBeEmpty)
            }

            else if (linkedinInmailData.body.length >  AppConfig.MultiChannel.linkedin_in_mail_message_body_character_limit) {
              Left(ValidateCampaignStepVariantError.LinkedinInmailBodyTooLong)
            } else if (linkedinInmailData.subject.isDefined &&  linkedinInmailData.subject.get.length > AppConfig.MultiChannel.linkedin_in_mail_subject_character_limit) {
              Left(ValidateCampaignStepVariantError.LinkedinInmailSubjectTooLong) }
         */
         Right(variant)

      case linkedinConnectionRequestData: CampaignStepData.LinkedinConnectionRequestData =>
        /*
        17-september-2024: we are removing limits for manual linkedin steps
        if (linkedinConnectionRequestData.body.getOrElse("").length > AppConfig.MultiChannel.linkedin_connection_request_add_note_character_limit) {
           Left(ValidateCampaignStepVariantError.LinkedinConnectionRequestAddNoteTooLong)
         } else {
         */
          Right(variant)
//        }

      case _: CampaignStepData.LinkedinViewProfile =>
        Right(variant)

      case linkedinMessageData: CampaignStepData.AutoLinkedinMessage =>
        if (linkedinMessageData.body.trim == "") {
          Left(ValidateCampaignStepVariantError.LinkedinMessageBodyCantBeEmpty)
        } else if (linkedinMessageData.body.length > AppConfig.MultiChannel.linkedin_message_body_character_limit) {
          Left(ValidateCampaignStepVariantError.LinkedinMessageBodyTooLong)
        }
        else Right(variant)

      case linkedinInmailData: CampaignStepData.AutoLinkedinInmail =>
        if (linkedinInmailData.body.trim == "") {
          Left(ValidateCampaignStepVariantError.LinkedinInmailBodyCantBeEmpty)
        } else if (linkedinInmailData.body.length > AppConfig.MultiChannel.linkedin_in_mail_message_body_character_limit) {
          Left(ValidateCampaignStepVariantError.LinkedinInmailBodyTooLong)
        } else if (linkedinInmailData.subject.length > AppConfig.MultiChannel.linkedin_in_mail_subject_character_limit) {
          Left(ValidateCampaignStepVariantError.LinkedinInmailSubjectTooLong)
        } else Right(variant)

      case linkedinConnectionRequestData: CampaignStepData.AutoLinkedinConnectionRequest =>
        if (linkedinConnectionRequestData.body.isDefined && linkedinConnectionRequestData.body.get.length > AppConfig.MultiChannel.linkedin_connection_request_add_note_character_limit) {
          Left(ValidateCampaignStepVariantError.LinkedinConnectionRequestAddNoteTooLong)
        } else {
          Right(variant)
        }

      case _: CampaignStepData.AutoLinkedinViewProfile =>
        Right(variant)

      case whatsappMessageData: CampaignStepData.WhatsappMessageData =>
        if (whatsappMessageData.body.trim == "") {
          Left(ValidateCampaignStepVariantError.WhatsappMessageBodyCantBeEmpty)
        } else if (whatsappMessageData.body.length > AppConfig.MultiChannel.whatsapp_message_body_character_limit) {
          Left(ValidateCampaignStepVariantError.WhatsappMessageBodyTooLong)
        } else Right(variant)

      case smsMessageData: CampaignStepData.SmsMessageData =>
        if (smsMessageData.body.trim == "") {
          Left(ValidateCampaignStepVariantError.PhoneMessageBodyCantBeEmpty)
        } else Right(variant)

      case callTaskData: CampaignStepData.CallTaskData =>
        if (callTaskData.body.trim == "") {
          Left(ValidateCampaignStepVariantError.CallTaskBodyCantBeEmpty)
        } else Right(variant)

      case _: CampaignStepData.GeneralTaskData =>
        if (variant.notes.getOrElse("").trim == "") {
          Left(ValidateCampaignStepVariantError.GeneralTaskNotesCantBeEmpty)
        } else Right(variant)

      case _: CampaignStepData.MoveToAnotherCampaignStepData =>

        Right(variant)

    }

  }
}
