package api.campaigns.services

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.models.CampaignAISequenceStatus
import api.gpt.CreateStepsRequest
import utils.mq.{CampaignAISequenceGeneratorMsg, MQCampaignAISequenceGenerator}

import scala.util.{Failure, Success, Try}

class CampaignAISequenceService(
                                 campaignDAOService: CampaignDAOService,
                                 mqCampaignAISequenceGenerator: MQCampaignAISequenceGenerator
                               ) {

  def triggerCampaignAISequenceGeneration(
                                           campaignId: CampaignId,
                                           orgId: OrgId,
                                           taId: Long,
                                           accountId: AccountId,
                                           teamId: TeamId,
                                           createStepsRequest: CreateStepsRequest
                                         ): Try[Boolean] = {

    if (createStepsRequest.number_of_steps <= 0 || createStepsRequest.number_of_steps > AppConfig.maxAllowedStepsForAISequenceGenerator) {
      return Failure(new Exception(s"Number of steps should be between 1 and ${AppConfig.maxAllowedStepsForAISequenceGenerator}"))
    }

    for {
      _: String <- campaignDAOService.hasAlreadyTriggeredAISequenceGenerationForCampaign(
        accountId = accountId,
        teamId = teamId
      )
        .flatMap {
          case Some(campaignName) =>
            Failure(new Exception(s"You have already triggered AI Sequence Generation for campaign: $campaignName"))

          case None =>
            Success("")
        }

      _: String <- campaignDAOService.changeCampaignAISequenceStatus(
        campaignId = campaignId,
        teamId = teamId,
        status = CampaignAISequenceStatus.Triggered
      )

      // Unit
      _ <- mqCampaignAISequenceGenerator.publish(
        msg = CampaignAISequenceGeneratorMsg(
          campaignId = campaignId,
          teamId = teamId,
          orgId = orgId,
          accountId = accountId,
          taId = taId,
          createStepsRequest = createStepsRequest
        )
      )
    } yield {
      true
    }

  }

}
