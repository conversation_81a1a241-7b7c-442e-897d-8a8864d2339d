package api.campaigns.services

import api.AppConfig
import api.accounts.models.OrgId
import api.accounts.{Account, AccountService, OrganizationWithCurrentData, TeamId}
import api.calendar_app.{CalendarAppService, GetSelectedCalendarDataFromDbError}
import api.calendar_app.models.CalendarAccountData
import api.campaigns.models.CampaignStepType.{AutoEmailMagicContent, ManualEmailMagicContent, WhatsappMessage}
import api.campaigns.{Campaign, CampaignBasicInfo, CampaignEditedPreviewEmail, CampaignEditedPreviewEmailDAO, CampaignEditedPreviewStep, CampaignEmailSettings, CampaignStepDAO, CampaignStepVariantDAO, CampaignStepVariantForScheduling, CampaignStepWithChildren, PreviousFollowUp}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepData, CampaignStepType, ChannelSettingSenderDetails, PreviewData, PreviewGetStepsForProspectError, PreviousFollowUpData, SenderDetails, StepAndVariantId, StepsForPreview, SubjectAndBody}
import api.accounts.models.AccountId
import api.campaigns.services.CampaignService.{getPreviewData, sendCorrectError}
import api.columns.InternalMergeTagValuesForProspect
import api.prospects.models.{ProspectId, StepId}
import api.emails.dao_service.EmailScheduledDAOService
import api.emails.{EmailScheduledDAO, EmailSetting, EmailSettingDAO, EmailsScheduledUuid}
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.ProspectId
import api.prospects.{ProspectAccount, ProspectAccountDAO1}
import api.tasks.services.TaskService
import eventframework.{ProspectFieldsResult, ProspectObject}
import io.smartreach.esp.utils.email.EmailHelperCommon
import sr_scheduler.models.{CampaignAIGenerationContext, ChannelType, SelectedCalendarData}
import utils.{Helpers, SRLogger}
import utils.email.{EmailHelper, EmailOptionsForGetBodies, EmailServiceCompanion, UnSubscribeLinkHelper}
import utils.srlogging.sr.Logger

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import utils.templating.TemplateService
import io.smartreach.esp.api.emails.EmailSettingId
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import org.jsoup.Jsoup
import api.gpt.ai_hyperpersonalized.{AIHyperPersonalizedGenerator, StepContext}
import play.api.libs.ws.WSClient

case class ProspectPreviewData(
                                prospect_objects: Seq[ProspectObject],
                                campaign_basic_info_opt: Option[CampaignBasicInfo],
                                previous_sent_steps: Seq[PreviousFollowUp],
                                ordered_steps: Seq[CampaignStepWithChildren]
                              )

class CampaignPreviewService(
  campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO,
  prospectDAOService: ProspectDAOService,
  emailScheduledDAOService: EmailScheduledDAOService,
  campaignStepVariantDAO: CampaignStepVariantDAO,
  prospectAccountDAO: ProspectAccountDAO1,
  channelSettingService: ChannelSettingService,
  templateService: TemplateService,
  emailSettingDAO: EmailSettingDAO,
  emailServiceCompanion: EmailServiceCompanion,
  taskService: TaskService,
  accountService: AccountService,
  campaignDAOService: CampaignDAOService,
  calendarAppService: CalendarAppService,
  campaignService: CampaignService,
  aiHyperPersonalizedGenerator: AIHyperPersonalizedGenerator
) {

  def previewEmailChannel(
    orderedCampaignSteps: Seq[CampaignStepWithChildren],
    step: CampaignStepWithChildren,
    alreadySentSteps: Seq[PreviousFollowUp],
    editedPreviewSteps: Seq[CampaignEditedPreviewEmail],
    previousSubject: Option[String],
    campaign: Campaign,
    prospect: ProspectObject,
    org: OrganizationWithCurrentData,
    es: EmailSetting,
    stepAndVariantIds: List[StepAndVariantId],
    prospectAccount: Option[ProspectAccount],
    alreadyPreviewEditedThisStep: Option[CampaignEditedPreviewEmail],
    allTrackingDomainsUsed: Seq[String],
    prospectSteps: Vector[CampaignEditedPreviewStep],
    stepIndex: Int,
    calendarAccountData: Option[CalendarAccountData],
    selectedCalendarData: Option[SelectedCalendarData]
  )(using logger: SRLogger): CampaignEditedPreviewStep = {

    var dummyPrevEmails: Seq[PreviousFollowUp] = Seq() // previous follow up logic this is. if not email
    val previousSteps: Seq[CampaignStepWithChildren] = orderedCampaignSteps
      .filter(step => step.step_type.channelType == ChannelType.EmailChannel) // filtering by only email steps
      .takeWhile(email_step => email_step.id != step.id) // Getting all the previous emails not needed for other steps.

    var subject = ""
    for (prevStep <- previousSteps) {
      val alreadySentPrevStep = alreadySentSteps.find(s => s.step_id.get == prevStep.id)
      val alreadyPreviewEditedPrevStep = editedPreviewSteps.find(s => s.stepId == prevStep.id)

      val body = if (alreadySentPrevStep.isDefined) {

        alreadySentPrevStep.get.channel_follow_up_data match {

          case data: PreviousFollowUpData.AutoEmailFollowUp =>

            subject = data.subject

            data.base_body // for previous step append, take the base body, not the full html body

          case data: PreviousFollowUpData.ManualEmailFollowUp =>

            subject = data.subject

            data.base_body // for previous step append, take the base body, not the full html body

          case data: PreviousFollowUpData.AutoEmailMagicFollowUp =>

            subject = data.generated_subject.get

            data.generated_base_body.get


          case data: PreviousFollowUpData.ManualEmailMagicFollowUp =>

            subject = data.generated_subject.get

            data.generated_base_body.get


          case _: PreviousFollowUpData.LinkedinInMailFollowUp
               | _: PreviousFollowUpData.LinkedinMessageFollowUp
               | _: PreviousFollowUpData.LinkedinConnectionRequestFollowUp
               | _: PreviousFollowUpData.LinkedinViewProfileFollowUp
               | _: PreviousFollowUpData.AutoLinkedinInMailFollowUp
               | _: PreviousFollowUpData.AutoLinkedinMessageFollowUp
               | _: PreviousFollowUpData.AutoLinkedinConnectionRequestFollowUp
               | _: PreviousFollowUpData.AutoLinkedinViewProfileFollowUp
               | _: PreviousFollowUpData.WhatsappFollowUp
               | _: PreviousFollowUpData.SmsFollowUp
               | _: PreviousFollowUpData.CallFollowUp
               | _: PreviousFollowUpData.MoveToAnotherCampaignFollowUp
               | _: PreviousFollowUpData.GeneralFollowUp
          =>

            throw new Exception("Impossible case: Step type can only be email types")

        }

      } else if (alreadyPreviewEditedPrevStep.isDefined) {

        subject = alreadyPreviewEditedPrevStep.get.editedSubject

        alreadyPreviewEditedPrevStep.get.editedBody

      } else {

        // General - Take it out
        val optOutLinkUrl = Some(UnSubscribeLinkHelper.constructUnsubUrl(
          emailsScheduledUuid = EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
          teamId = es.team_id,
          customDomain = es.custom_tracking_domain,
          defaultUnsubscribeDomain = es.default_unsubscribe_domain
        ))

        val calendarLinkUrl =  if(selectedCalendarData.isDefined){
          EmailHelper._makeCalendarLinkUrl(
            prospectId = ProspectId(id = prospect.id),
            teamId = TeamId(id = prospect.team_id),
            sender_name = es.sender_name,
            campaignId = CampaignId(id = campaign.id),
            stepId = step.id,
            calendarAccountData = calendarAccountData,
            selectedCalendarData = selectedCalendarData
          ) match {
            case Failure(exception) => {
              logger.warn(s"CampaignPreviewService previewEmailChannel :: Error Occurred while making calendar Link , sending None ${exception.printStackTrace()}")
              None
            }
            case Success(value) => Some(value)
          }
        }else{
          None
        }

        // General - Take it out
        val internalMergeTags = InternalMergeTagValuesForProspect(
          unsubscribe_link = optOutLinkUrl,
          sender_name = es.sender_name,
          sender_first_name = es.first_name,
          sender_last_name = es.last_name,
          previous_subject = previousSubject,
          signature = Some(es.signature),
          sender_phone_number = None,
          calendar_link = calendarLinkUrl

        )
        val prevStepAndVariantID = stepAndVariantIds.find(stepAndVariantId => stepAndVariantId.stepId == prevStep.id)
        val prevVariantId = prevStepAndVariantID.map(_.variantId)
        val prevVariant = prevVariantId.flatMap(id => prevStep.variants.find(va => va.id == id))


        val subjectAndBody = CampaignStepData.getSubjectAndBodyFromStepData(
          stepData = prevVariant.getOrElse(prevStep.variants.filter(_.active).head).step_data
        )

        val (prevSubject, prevBody) = (subjectAndBody.subject, subjectAndBody.body)

        subject = templateService.render(
          template = previousSubject.getOrElse(""),
          prospect = prospect,
          internalMergeTags = internalMergeTags,
          channel = ChannelType.EmailChannel
        ).get

        templateService.render(
          template = prevBody,
          prospect = prospect,
          internalMergeTags = internalMergeTags,
          channel = ChannelType.EmailChannel
        ).get

      }

      val body_disable = EmailHelper.disableTrackingLinks(
        body = body,
        allTrackingDomains = allTrackingDomainsUsed,
        isTextBody = false
      )

      val channel_follow_up_data = step.step_type match {

        case CampaignStepType.AutoEmailStep =>

          PreviousFollowUpData.AutoEmailFollowUp(
            email_thread_id = None,
            from_name = Helpers.getSenderName(es),
            subject = subject,
            body = body_disable,
            base_body = body,
            from_email = es.email,
            is_edited_preview_email = false
          )

        case CampaignStepType.ManualEmailStep =>

          PreviousFollowUpData.ManualEmailFollowUp(
            email_thread_id = None,
            from_name = Helpers.getSenderName(es),
            subject = subject,
            body = body_disable,
            base_body = body,
            from_email = es.email,
            is_edited_preview_email = false
          )

        case ManualEmailMagicContent  =>

          val generated_subject: Option[String] = if(subject.trim.nonEmpty) Some(subject) else None
          val generated_body: Option[String] = if(body_disable.trim.nonEmpty) Some(body_disable) else None
          val generated_base_body : Option[String] = if(body.trim.nonEmpty) Some(body) else None


          PreviousFollowUpData.ManualEmailMagicFollowUp(
            email_thread_id = None,
            from_name = Helpers.getSenderName(es),
            generated_subject =  generated_subject ,
            generated_body = generated_body,
            generated_base_body = generated_base_body,
            from_email = es.email,
            is_edited_preview_email = false
          )


        case AutoEmailMagicContent =>

          val generated_subject: Option[String] = if(subject.trim.nonEmpty) Some(subject) else None
          val generated_body: Option[String] = if(body_disable.trim.nonEmpty) Some(body_disable) else None
          val generated_base_body : Option[String] = if(body.trim.nonEmpty) Some(body) else None

          PreviousFollowUpData.AutoEmailMagicFollowUp(
            email_thread_id = None,
            from_name = Helpers.getSenderName(es),
            generated_subject =  generated_subject ,
            generated_body = generated_body,
            generated_base_body = generated_base_body,
            from_email = es.email,
            is_edited_preview_email = false
          )

        case CampaignStepType.LinkedinViewProfile
             | CampaignStepType.LinkedinConnectionRequest
             | CampaignStepType.LinkedinInmail
             | CampaignStepType.LinkedinMessage
             | CampaignStepType.AutoLinkedinInmail
             | CampaignStepType.AutoLinkedinMessage
             | CampaignStepType.AutoLinkedinConnectionRequest
             | CampaignStepType.AutoLinkedinViewProfile
             | CampaignStepType.WhatsappMessage
             | CampaignStepType.SmsMessage
             | CampaignStepType.CallStep
             | CampaignStepType.MoveToAnotherCampaignStep
             | CampaignStepType.GeneralTask
        =>

          throw new Exception("Impossible case: Step type can only be email types")

      }

      dummyPrevEmails = dummyPrevEmails ++ Seq(
        PreviousFollowUp(
          channel_follow_up_data = channel_follow_up_data,
          timezone = campaign.settings.timezone,
          sent_at = prevStep.created_at,
          step_id = Some(prevStep.id),
          completed_reason = None // doesnt matter while appending-followups
        )
      )
    }

    val alreadyPreviewEditedThisStep = editedPreviewSteps.find(s => s.stepId == step.id) // look into this

    // It contains some genral logic in else section
    val (stepSubjectTemplate, stepBodyTemplate) = if (alreadyPreviewEditedThisStep.isDefined) {
      (alreadyPreviewEditedThisStep.get.editedSubject, alreadyPreviewEditedThisStep.get.editedBody)
    } else {
      // this logic is needed - general  FYI: Other Than Email steps have only one variant
      val currentStepAndVariantID: Option[StepAndVariantId] = stepAndVariantIds.find(stepAndVariantId => stepAndVariantId.stepId == step.id)
      val currentVariantId: Option[Long] = currentStepAndVariantID.map(_.variantId)
      val currentVariant: Option[CampaignStepVariantForScheduling] = currentVariantId.flatMap(id => step.variants.find(va => va.id == id))

      if (currentVariant.isEmpty) {
        throw new Exception("Current variant can't be empty") // Check error message
      }

      val data: SubjectAndBody = CampaignStepData.getSubjectAndBodyFromStepData(
        stepData = currentVariant.get.step_data
      )

      (data.subject, data.body)
    }

    // email only
    // This might useful for other steps as well -
    val emailServiceBody = emailServiceCompanion.getBodies(
      editedPreviewEmail = alreadyPreviewEditedThisStep.find(s => s.stepId == step.id),
      org_id = org.id,
      calendarAccountData = calendarAccountData,
      head_step_id = campaign.head_step_id,
      emailsScheduledUuid = EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
      campaign_id = Some(campaign.id),
      step_id = Some(step.id),
      prospect = prospect,
      emailOptions = EmailOptionsForGetBodies(
        for_editable_preview = false,
        editedPreviewEmailAlreadyChecked = true,
        custom_tracking_domain = es.custom_tracking_domain,
        default_tracking_domain = es.default_tracking_domain,
        default_unsubscribe_domain = es.default_unsubscribe_domain,
        signature = Some(es.signature),
        opt_out_msg = campaign.settings.opt_out_msg,
        opt_out_is_text = campaign.settings.opt_out_is_text,
        append_followups = campaign.settings.append_followups,
        bodyTemplate = stepBodyTemplate,
        subjectTemplate = stepSubjectTemplate,
        trackClicks = campaign.settings.click_tracking_enabled,
        email_sender_name = es.sender_name,
        sender_first_name = es.first_name,
        sender_last_name = es.last_name,
        manualEmail = false,
        trackOpens = false,
        previousEmails = dummyPrevEmails,
        allTrackingDomainsUsed = allTrackingDomainsUsed,
      ),
      selectedCalendarData = selectedCalendarData
    ).get


    // Todo: Try using emailServicebody to create emailServiceBodyForEDITABLEPreview
    // message without followups, this will be sent to the Editor
    // we wont allow editing with the followups
    // This might useful for other steps as well -
    val emailServiceBodyForEDITABLEPreview = emailServiceCompanion.getBodies(
      editedPreviewEmail = alreadyPreviewEditedThisStep.find(s => s.stepId == step.id),
      org_id = org.id,
      calendarAccountData = calendarAccountData,
      head_step_id = campaign.head_step_id,
      emailsScheduledUuid = EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
      campaign_id = Some(campaign.id),
      step_id = Some(step.id),
      prospect = prospect,
      emailOptions = EmailOptionsForGetBodies(
        for_editable_preview = true,
        editedPreviewEmailAlreadyChecked = true,
        custom_tracking_domain = es.custom_tracking_domain,
        default_tracking_domain = es.default_tracking_domain,
        default_unsubscribe_domain = es.default_unsubscribe_domain,
        signature = Some(es.signature),
        opt_out_msg = campaign.settings.opt_out_msg,
        opt_out_is_text = campaign.settings.opt_out_is_text,
        append_followups = campaign.settings.append_followups,
        bodyTemplate = stepBodyTemplate,
        subjectTemplate = stepSubjectTemplate,

        trackClicks = campaign.settings.click_tracking_enabled,
        email_sender_name = es.sender_name,
        sender_first_name = es.first_name,
        sender_last_name = es.last_name,
        manualEmail = false,
        trackOpens = false,
        previousEmails = dummyPrevEmails,
        allTrackingDomainsUsed = allTrackingDomainsUsed
      ),
      selectedCalendarData = selectedCalendarData
    ).get


    val preview_data: PreviewData = step.step_type match {

      case CampaignStepType.AutoEmailStep =>

        val text_body = emailServiceBody.textBody

        val body = if (campaign.settings.send_plain_text_email.isDefined && campaign.settings.send_plain_text_email.get) {
          CampaignPreviewService.makeHTML(text_body)
        } else emailServiceBody.htmlBody

        PreviewData.AutoEmailPreview(
          subject = emailServiceBody.subject,
          body = EmailHelper.disableTrackingLinks(
            body = body,
            allTrackingDomains = allTrackingDomainsUsed,
            isTextBody = false
          ),
          editable_body = Some(emailServiceBodyForEDITABLEPreview.htmlBody),
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = emailServiceBody.baseBody).take(200),
          edited = alreadyPreviewEditedThisStep.isDefined,
          email_thread_id = None
        )

      case CampaignStepType.ManualEmailStep =>

        val text_body = emailServiceBody.textBody

        val body = if (campaign.settings.send_plain_text_email.isDefined && campaign.settings.send_plain_text_email.get) {
          CampaignPreviewService.makeHTML(text_body)
        } else emailServiceBody.htmlBody

        PreviewData.ManualEmailPreview(
          subject = emailServiceBody.subject,
          body = EmailHelper.disableTrackingLinks(
            body =body,
            allTrackingDomains = allTrackingDomainsUsed,
            isTextBody = false
          ),
          editable_body = Some(emailServiceBodyForEDITABLEPreview.htmlBody),
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = emailServiceBody.baseBody).take(200),
          edited = alreadyPreviewEditedThisStep.isDefined,
          email_thread_id = None
        )


      case CampaignStepType.AutoEmailMagicContent =>

        val textBodyEmpty = emailServiceBody.textBody.trim.isEmpty

        val body = if (!textBodyEmpty && campaign.settings.send_plain_text_email.contains(true)) {
          CampaignPreviewService.makeHTML(emailServiceBody.textBody)
        } else emailServiceBody.htmlBody

        val generated_subject = if (emailServiceBody.subject.trim.nonEmpty) Some(emailServiceBody.subject) else None

        val body_ = if (!textBodyEmpty) {
          EmailHelper.disableTrackingLinks(
            body = body,
            allTrackingDomains = allTrackingDomainsUsed,
            isTextBody = false
          )
        } else ""

        val body_preview = if (!textBodyEmpty) {
          EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = emailServiceBody.baseBody).take(200)
        } else ""

        val generated_body = if (body_.trim.nonEmpty) Some(body_) else None
        val generated_body_preview = if (body_preview.trim.nonEmpty) Some(body_preview) else None
        val editable_body = if (!emailServiceBodyForEDITABLEPreview.textBody.trim.nonEmpty) Some(emailServiceBodyForEDITABLEPreview.htmlBody) else None

        PreviewData.AutoEmailMagicPreview(
          generated_subject = generated_subject,
          generated_body = generated_body,
          editable_body = editable_body,
          generated_body_preview = generated_body_preview,
          edited = alreadyPreviewEditedThisStep.isDefined,
          email_thread_id = None
        )

      case CampaignStepType.ManualEmailMagicContent =>

        val textBodyEmpty = emailServiceBody.textBody.trim.isEmpty

        val body = if (!textBodyEmpty && campaign.settings.send_plain_text_email.contains(true)) {
          CampaignPreviewService.makeHTML(emailServiceBody.textBody)
        } else emailServiceBody.htmlBody

        val generated_subject = if (emailServiceBody.subject.trim.nonEmpty) Some(emailServiceBody.subject) else None

        val body_ = if (!textBodyEmpty) {
          EmailHelper.disableTrackingLinks(
            body = body,
            allTrackingDomains = allTrackingDomainsUsed,
            isTextBody = false
          )
        } else ""

        val body_preview = if (!textBodyEmpty) {
          EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = emailServiceBody.baseBody).take(200)
        } else ""

        val generated_body = if (body_.trim.nonEmpty) Some(body_) else None
        val generated_body_preview = if (body_preview.trim.nonEmpty) Some(body_preview) else None
        val editable_body = if (!emailServiceBodyForEDITABLEPreview.textBody.trim.nonEmpty) Some(emailServiceBodyForEDITABLEPreview.htmlBody) else None

        PreviewData.ManualEmailMagicPreview(
          generated_subject = generated_subject,
          generated_body = generated_body,
          editable_body = editable_body,
          generated_body_preview = generated_body_preview,
          edited = alreadyPreviewEditedThisStep.isDefined,
          email_thread_id = None
        )


      case CampaignStepType.LinkedinViewProfile
           | CampaignStepType.LinkedinConnectionRequest
           | CampaignStepType.LinkedinInmail
           | CampaignStepType.LinkedinMessage
           | CampaignStepType.AutoLinkedinInmail
           | CampaignStepType.AutoLinkedinMessage
           | CampaignStepType.AutoLinkedinConnectionRequest
           | CampaignStepType.AutoLinkedinViewProfile
           | CampaignStepType.WhatsappMessage
           | CampaignStepType.SmsMessage
           | CampaignStepType.CallStep
           | CampaignStepType.MoveToAnotherCampaignStep
           | CampaignStepType.GeneralTask
      =>

        throw new Exception("Impossible case: Step type can only be email types")

    }

    // Added to Prospect Steps for Frontend
    CampaignEditedPreviewStep(
      step_id = step.id,
      step_label = step.label.getOrElse(""),
      preview_data = preview_data,
      step_type = step.step_type,
      sent = false,
      sent_at = None,
      completed_reason = if (prospect.internal.invalid_email.getOrElse(false) && stepIndex == 0) Some("invalid_email") else None,
    )

  }


  // SENDER_ROTATION
  // Called from -
  // campaignController.previewGetStepsForProspect
  def previewGetStepsForProspect(
    campaign: Campaign,
    prospectId: Long,
    teamId: Long,
    enable_ab_testing: Boolean,
    allTrackingDomainsUsed: Seq[String],
    org: OrganizationWithCurrentData,
    stepAndVariantIds: List[StepAndVariantId],
    permittedAccountIdsForViewingTasks: Seq[Long],
    selected_campaign_email_setting_id: Option[CampaignEmailSettingsId]
  )(implicit ec: ExecutionContext, logger: SRLogger): Future[Either[PreviewGetStepsForProspectError, StepsForPreview]] = {

    //  step dependent : This needs to be only if step type is email
    //    if (campaign.settings.sender_email_settings_id.isEmpty || campaign.settings.receiver_email_settings_id.isEmpty) {
    //
    //      Future(Left(PreviewGetStepsForProspectError.SenderReceiverEmailAccountNotGiven))
    //
    //    } else
    //
    //
    if (campaign.head_step_id.isEmpty) { // Correct

      Future(Left(PreviewGetStepsForProspectError.NoStepInCampaign))

    } else {

      prospectDAOService.find( // Correct  This contains all linkedin / whatsapp detals
        Logger = logger,
        byCampaignId = Some(campaign.id),
        byProspectIds = Seq(prospectId),
        teamId = teamId,
        limit = 1

      ) match {
        case Failure(err) =>

          Future(Left(PreviewGetStepsForProspectError.ErrorWhileGettingProspectObjects(err))) // Correct

        case Success(prospectObjects) =>

          if (prospectObjects.isEmpty) {
            Future(Left(PreviewGetStepsForProspectError.ProspectNotFoundInCampaign)) // Correct
          } else {

            val prospect = prospectObjects.head

            // linkedin url is present is prospectAccount but not whatsapp number, why ?
            val prospectAccount = prospectAccountDAO.find(id = prospect.id, teamId = prospect.team_id)

            // correct - All Step got here - Step type is also present :-^
            val campaignStepsF: Future[Seq[CampaignStepWithChildren]] = Future {
              campaignDAOService.findOrderedSteps(
                campaignId = campaign.id,
                teamId = TeamId(id = teamId)
              )
            }

            /*
              General view is alright, but Response is Email specific
              As in 1st iteration we are not adding followups of previous sent message to step types other than Email
              This will be moved to email steps only

              # Move to Email
            */
            val alreadySentStepsF: Future[Seq[PreviousFollowUp]] = Future {
              emailScheduledDAOService.getPreviousSentSteps(
                campaignId = campaign.id,
                prospectId = prospectId,
                teamId = TeamId(id = teamId)
              ).get.filter(_.step_id.isDefined)//filtering out sent steps that have no step_id since this is for preview tab and we cant render if step is deleted
            }


            // Only Email Flow
            val editedPreviewStepsF: Future[Seq[CampaignEditedPreviewEmail]] = Future {
              campaignEditedPreviewEmailDAO.find(
                campaignId = Some(campaign.id),
                prospectId = prospectId,
                stepId = None
              ).get
            }

            val futureResult: Future[Either[PreviewGetStepsForProspectError, StepsForPreview]] = for {
              campaignSteps: Seq[CampaignStepWithChildren] <- campaignStepsF
              alreadySentSteps: Seq[PreviousFollowUp] <- alreadySentStepsF //  Email only, correct it if step type is this then go to this dao
              editedPreviewSteps: Seq[CampaignEditedPreviewEmail] <- editedPreviewStepsF // Email Only

              //              es: EmailSetting <- esF  // Email Only, correct it if step type is this then go to this dao
              channel_setting_sender_details: List[ChannelSettingSenderDetails] <-
                channelSettingService.getChannelSettingSenderData(
                  team_id = TeamId(
                    id = teamId
                  ),
                  campaign_id = CampaignId(
                    id = campaign.id
                  )
                )

              es: Option[EmailSetting] <- if (campaignSteps.exists(s => s.step_type.channelType == ChannelType.EmailChannel)) {
                if(campaign.settings.campaign_email_settings.isEmpty) {
                  Future.failed(new Exception(CampaignService.senderOrReceiverEmailIdNotPresentError))
                } else  {
                  val selected_sender_email_setting_id: Option[CampaignEmailSettings] = selected_campaign_email_setting_id
                    .flatMap(cesid => campaign.settings.campaign_email_settings.find(_.id.id == cesid.id))

                  selected_sender_email_setting_id match {
                    case Some(value) =>
                      Future.successful(emailSettingDAO.find(id = value.sender_email_setting_id, teamId = TeamId(teamId)))
                    case None =>
                      Future.failed(new Exception(CampaignService.senderOrReceiverEmailIdNotPresentError))

                  }

                }
              } else {
                Future.successful(None)
              }

              // Getting all the sent tasks for other than email types
              alreadySentTasks: Seq[PreviousFollowUp] <- {
                // There are some FixMe Inside
                taskService.getCompletedTasksForProspectInCampaign(
                  campaign_id = campaign.id,
                  prospect_id = prospectId,
                  orgId = OrgId(id = org.id),
                  team_id = teamId,
                  permittedAccountIds = permittedAccountIdsForViewingTasks,
                  timeZone = prospect.timezone.getOrElse(campaign.settings.timezone),
                  channel_setting_details = channel_setting_sender_details,
                  es = es
                ).map(_.filter(_.step_id.isDefined)) //filtering out sent steps that have no step_id since this is for preview tab and we cant render if step is deleted
              }

              accountObj:Account  <-  Future.fromTry(accountService.find(id = campaign.account_id ))


              selectedCalendarData:Option[SelectedCalendarData] <- Future{
                calendarAppService.getSelectedCalendarDataFromDB(
                  teamId = TeamId(id = teamId),
                  orgId = OrgId(id = org.id),
                  campaignId = Some(CampaignId(id = campaign.id))
                ) match {
                  case Left(GetSelectedCalendarDataFromDbError.FromCampaignsDbFailure(err)) => {
                    logger.fatal(" CampaignPreviewService previewGetStepsForProspect ::  Failed to fetch Selected calendar from campaigns",err)
                    None
                  }
                  case Left(GetSelectedCalendarDataFromDbError.FromTeamsDbFailure(err)) => {
                    logger.fatal("CampaignPreviewService previewGetStepsForProspect :: Failed to fetch Selected calendar from teams",err)
                    None
                  }
                  case Left(GetSelectedCalendarDataFromDbError.CalendarSettingsForTeamsNotConfigured(teamId)) =>{
                    logger.warn(s" CampaignPreviewService previewGetStepsForProspect :: Calendar Settings not configured for team : ${teamId.id}")
                    None
                  }
                  case Right(value) => Some(value)
                }
              }


            } yield {
              Try {


                var prospectSteps = Vector[CampaignEditedPreviewStep]()
                val orderedCampaignSteps = CampaignStepDAO.getOrderedSteps(steps = campaignSteps, headStepId = campaign.head_step_id.get)
                orderedCampaignSteps.zipWithIndex.foreach { case (step, stepIndex) =>


                  // concatenated already Sent tasks.
                  val alreadySentThisStep = alreadySentSteps.concat(alreadySentTasks).find(s => s.step_id.get == step.id)
                  val alreadyPreviewEditedThisStep = editedPreviewSteps.find(s => s.stepId == step.id) // look into this

                  if (alreadySentThisStep.isDefined) { // fine this one if other step is sent or not, just send this.
                    val em = alreadySentThisStep.get

                    val preview_data: PreviewData = getPreviewData(
                      previous_follow_up = alreadySentThisStep.get.channel_follow_up_data,
                      editable_body = None, // NOTE: sending empty because it doesnt matter in sent cases like this one
                      allTrackingDomainsUsed = allTrackingDomainsUsed
                    )

                    prospectSteps = prospectSteps :+ CampaignEditedPreviewStep(
                      step_id = step.id,
                      step_label = step.label.getOrElse(""),
                      preview_data = preview_data,
                      //                      subject = ,

                      // for the step, take the full html body, not just the base body
                      //                      body = em.body,
                      //                      body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = em.baseBody).take(200),
                      step_type = step.step_type,

                      //                      editable_body = "", // NOTE: sending empty because it doesnt matter in sent cases like this one
                      sent = true,
                      sent_at = Some(em.sent_at),
                      //                      edited = em.is_edited_preview_email,
                      completed_reason = em.completed_reason,
                      //                      email_thread_id = em.email_thread_id
                    )
                  } else {

                    step.step_type.channelType match {
                      case ChannelType.EmailChannel => {

                        if (campaign.settings.campaign_email_settings.isEmpty) {
                          throw new Exception(CampaignService.senderOrReceiverEmailIdNotPresentError)
                        }

                        //                        val es = emailSettingDAO.find(campaign.settings.sender_email_settings_id.get).get


                        val prospectStep = previewEmailChannel(
                          orderedCampaignSteps = orderedCampaignSteps,
                          step = step,
                          prospect = prospect,
                          alreadySentSteps = alreadySentSteps,
                          editedPreviewSteps = editedPreviewSteps,
                          previousSubject = CampaignPreviewService.getPreviousSubject(prospectSteps),
                          campaign = campaign,
                          es = es.headOption.get, //FIXME SENDER_ROTATION this doesn't need to be changed check with @prateek and @neil // This should never fail as we have sender_email_setting_id for email step.
                          org = org,
                          stepAndVariantIds = stepAndVariantIds,
                          prospectAccount = prospectAccount,
                          allTrackingDomainsUsed = allTrackingDomainsUsed,
                          prospectSteps = prospectSteps,
                          alreadyPreviewEditedThisStep = alreadyPreviewEditedThisStep,
                          stepIndex = stepIndex,
                          calendarAccountData = accountObj.calendar_account_data,
                          selectedCalendarData = selectedCalendarData
                        )

                        prospectSteps = prospectSteps :+ prospectStep
                      }

                      case ChannelType.LinkedinChannel
                           | ChannelType.WhatsappChannel
                           | ChannelType.SmsChannel
                           | ChannelType.CallChannel
                           | ChannelType.GeneralChannel =>

                        val channel_settings = channel_setting_sender_details.filter(st => st.channel_type == step.step_type.channelType)

                        CampaignService.checkIfChannelSettingExists(
                          channelSettingSenderDetails = channel_settings,
                          channel_type = step.step_type.channelType
                        ) match {

                          case Failure(e) =>
                            throw e

                          case Success(data) =>
                            val prospectStep = previewChannelOtherThanEmail(
                              stepIndex = stepIndex,
                              step = step,
                              prospect = prospect,
                              campaign = campaign,
                              stepAndVariantIds = stepAndVariantIds,
                              prospectAccount = prospectAccount,
                              allTrackingDomains = allTrackingDomainsUsed,
                              channelSettingSenderDetails = data,
                              calendarAccountData = accountObj.calendar_account_data,
                              selectedCalendarData = selectedCalendarData
                            )

                            prospectSteps = prospectSteps :+ prospectStep

                        }

                      case ChannelType.IndependentChannel =>

                        val prospectStep = previewChannelOtherThanEmail(
                          stepIndex = stepIndex,
                          step = step,
                          prospect = prospect,
                          campaign = campaign,
                          stepAndVariantIds = stepAndVariantIds,
                          prospectAccount = prospectAccount,
                          allTrackingDomains = allTrackingDomainsUsed,
                          channelSettingSenderDetails = None,
                          calendarAccountData = accountObj.calendar_account_data,
                          selectedCalendarData = selectedCalendarData
                        )

                        prospectSteps = prospectSteps :+ prospectStep

                    }


                  }
                }
                val name = prospect.first_name.getOrElse("") + " " + prospect.last_name.getOrElse("")
                StepsForPreview(
                  prospect_id = prospect.id,
                  prospect_email = prospect.email, //TODO: EMAIL_OPTIONAL check the path and test code (for old path(email compulsory) we will always get the email)
                  prospect_name = name,
                  prospect_linkedin = prospect.linkedin_url,
                  prospect_phone = prospect.phone,
                  prospect_company = prospect.company,
                  steps = prospectSteps
                  //                    It is handled in getPreviewSteps
                  //                    .map(step => {
                  //                      step.copy(
                  //                        body = EmailHelper.disableTrackingLinks(
                  //                          body = body,
                  //                          allTrackingDomains = allTrackingDomainsUsed,
                  //                          isTextBody = false
                  //                        )
                  //                      )
                  ////                      step.copy(
                  ////                        body = EmailHelper.disableTrackingLinks(
                  ////                          body = body,
                  ////                          allTrackingDomains = allTrackingDomainsUsed,
                  ////                          isTextBody = false
                  ////                        )
                  ////                      )
                  //                    })
                )
              } match {
                case Failure(e) =>
                  Left(sendCorrectError(e = e))

                case Success(res) =>
                  Right(res)
              }
            }

            futureResult.recover(
              err => Left(sendCorrectError(e = err))
            )
          }
      }
    }
  }


  def previewChannelOtherThanEmail(
    step: CampaignStepWithChildren,
    campaign: Campaign,
    prospect: ProspectObject,
    stepAndVariantIds: List[StepAndVariantId],
    prospectAccount: Option[ProspectAccount],
    stepIndex: Int,
    allTrackingDomains: Seq[String],
    channelSettingSenderDetails: Option[ChannelSettingSenderDetails],
    calendarAccountData: Option[CalendarAccountData],
    selectedCalendarData: Option[SelectedCalendarData]

  )(using logger: SRLogger)
  : CampaignEditedPreviewStep = {

    // this logic is needed - general  FYI: Other Than Email steps have only one variant
    val currentStepAndVariantID: Option[StepAndVariantId] = stepAndVariantIds.find(stepAndVariantId => stepAndVariantId.stepId == step.id)
    val currentVariantId: Option[Long] = currentStepAndVariantID.map(_.variantId)
    val currentVariant: Option[CampaignStepVariantForScheduling] = currentVariantId.flatMap(id => step.variants.find(va => va.id == id))

    if (currentVariant.isEmpty) {
      throw new Exception(CampaignService.currentVariantCantBeEmptyError) // Check error message
    }
    val sender_data: SenderDetails = step.step_type.channelType match {

      case ChannelType.EmailChannel =>

        throw new Exception(CampaignService.impossibleEmailChannelCantComeHereError)

      case ChannelType.GeneralChannel =>

        SenderDetails(
          sender_name = "",
          sender_first_name = "",
          sender_last_name = "",
          sender_number = None
        )

      case ChannelType.LinkedinChannel
           | ChannelType.WhatsappChannel
           | ChannelType.CallChannel
           | ChannelType.SmsChannel =>


        channelSettingSenderDetails match {
          case None =>

            throw new Exception(CampaignService.channelSettingSenderDataCantBeNoneError)

          case Some(data) =>
            SenderDetails(
              sender_name = data.sender_name,
              sender_first_name = data.first_name,
              sender_last_name = data.last_name,
              sender_number = data.phone_number)
        }

      case ChannelType.IndependentChannel =>

            SenderDetails(
              sender_name = "",
              sender_first_name = "",
              sender_last_name = "",
              sender_number = None
            )
    }

    val calendarLink = if(selectedCalendarData.isDefined) {
      EmailHelper._makeCalendarLinkUrl(
        prospectId = ProspectId(id = prospect.id),
        teamId = TeamId(id = prospect.team_id),
        sender_name = sender_data.sender_name,
        campaignId = CampaignId(id = campaign.id),
        stepId = step.id,
        calendarAccountData = calendarAccountData,
        selectedCalendarData = selectedCalendarData
      ) match {
        case Failure(exception) => {
          logger.warn(s"CampaignPreviewService previewChannelOtherThanEmail :Error Occurred while making calendar Link , sending None ${exception.printStackTrace()}")
          None
        }
        case Success(value) => Some(value)
      }
    }else{
      None
    }

    val internalMergeTags = InternalMergeTagValuesForProspect(
      unsubscribe_link = None,
      sender_name = sender_data.sender_name,
      sender_first_name = sender_data.sender_first_name,
      sender_last_name = sender_data.sender_last_name,
      previous_subject = None,

      signature = None, //This is for preview other than Email, since the signature is part of the campaign email setting now, we dont have a signature here
      sender_phone_number = sender_data.sender_number,
      calendar_link = calendarLink
    )

    val subjectAndBody = CampaignStepData.getSubjectAndBodyFromStepData(
      stepData = currentVariant.get.step_data
    )

    val body_line_breaks = CampaignService.addNewLineBreaks(
      body = subjectAndBody.body
    )

    val (subject, filtered_body) = (subjectAndBody.subject, EmailHelper.disableTrackingLinks(
      body = body_line_breaks,
      allTrackingDomains = allTrackingDomains,
      isTextBody = false
    ))


    val preview_data = currentVariant.get.step_data match {

      case _: CampaignStepData.LinkedinViewProfile =>


        PreviewData.LinkedinViewProfilePreview()


      case _: CampaignStepData.LinkedinConnectionRequestData =>


        val renderedBody: String = templateService.render(
          template = filtered_body,
          prospect = prospect,
          internalMergeTags = internalMergeTags,
          channel = ChannelType.LinkedinChannel
        ).get


        PreviewData.LinkedinConnectionRequestPreview(
          body = Some(renderedBody),
          body_preview = Some(EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = renderedBody).take(200))
        )

      case _: CampaignStepData.LinkedinInmailData =>

        val renderedSubject: String = templateService.render(
          template = subject,
          prospect = prospect,
          internalMergeTags = internalMergeTags,
          channel = step.step_type.channelType
        ).get

        val renderedBody: String = templateService.render(
          template = filtered_body,
          prospect = prospect,
          internalMergeTags = internalMergeTags,
          channel = step.step_type.channelType
        ).get

        PreviewData.LinkedinInMailPreview(
          subject = Some(renderedSubject),
          body = renderedBody,
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = renderedBody).take(200)
        )

      case _: CampaignStepData.LinkedinMessageData =>

        val renderedBody: String = templateService.render(
          template = filtered_body,
          prospect = prospect,
          internalMergeTags = internalMergeTags,
          channel = step.step_type.channelType
        ).get


        PreviewData.LinkedinMessagePreview(
          body = renderedBody,
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = renderedBody).take(200)
        )

      case _: CampaignStepData.AutoLinkedinViewProfile =>


        PreviewData.AutoLinkedinViewProfilePreview()


      case _: CampaignStepData.AutoLinkedinConnectionRequest =>


        val renderedBody: String = templateService.render(
          template = filtered_body,
          prospect = prospect,
          internalMergeTags = internalMergeTags,
          channel = ChannelType.LinkedinChannel
        ).get


        PreviewData.AutoLinkedinConnectionRequestPreview(
          body = Some(renderedBody),
          body_preview = Some(EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = renderedBody).take(200))
        )

      case _: CampaignStepData.AutoLinkedinInmail =>

        val renderedSubject: String = templateService.render(
          template = subject,
          prospect = prospect,
          internalMergeTags = internalMergeTags,
          channel = step.step_type.channelType
        ).get

        val renderedBody: String = templateService.render(
          template = filtered_body,
          prospect = prospect,
          internalMergeTags = internalMergeTags,
          channel = step.step_type.channelType
        ).get

        PreviewData.AutoLinkedinInMailPreview(
          subject = renderedSubject,
          body = renderedBody,
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = renderedBody).take(200)
        )

      case _: CampaignStepData.AutoLinkedinMessage =>

        val renderedBody: String = templateService.render(
          template = filtered_body,
          prospect = prospect,
          internalMergeTags = internalMergeTags,
          channel = step.step_type.channelType
        ).get


        PreviewData.AutoLinkedinMessagePreview(
          body = renderedBody,
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = renderedBody).take(200)
        )


      case _: CampaignStepData.WhatsappMessageData =>

        val renderedBody = templateService.render(
          template = filtered_body,
          prospect = prospect,
          internalMergeTags = internalMergeTags,
          channel = step.step_type.channelType
        ).get


        PreviewData.WhatsappPreview(
          body = renderedBody,
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = renderedBody).take(200)
        )

      case _: CampaignStepData.SmsMessageData =>

        val renderedBody = templateService.render(
          template = filtered_body,
          prospect = prospect,
          internalMergeTags = internalMergeTags,
          channel = step.step_type.channelType
        ).get


        PreviewData.SmsPreview(
          body = renderedBody,
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = renderedBody).take(200)
        )

      case _: CampaignStepData.CallTaskData =>

        val renderedBody = templateService.render(
          template = filtered_body,
          prospect = prospect,
          internalMergeTags = internalMergeTags,
          channel = step.step_type.channelType
        ).get


        PreviewData.CallPreview(
          body = renderedBody,
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = renderedBody).take(200)
        )

      case _: CampaignStepData.GeneralTaskData =>

        val body_line_break = CampaignService.addNewLineBreaks(body = currentVariant.get.notes.getOrElse(""))

        val renderedBody = templateService.render(
          template = body_line_break,
          prospect = prospect,
          internalMergeTags = internalMergeTags,
          channel = step.step_type.channelType
        )


        PreviewData.GeneralPreview(
          body = renderedBody.get,
          body_preview = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = body_line_break).take(200)
        )

      case _: CampaignStepData.MoveToAnotherCampaignStepData =>

//        val body_line_break = CampaignService.addNewLineBreaks(body = currentVariant.get.notes.getOrElse(""))
//
//        val renderedBody = templateService.render(
//          template = body_line_break,
//          prospect = prospect,
//          internalMergeTags = internalMergeTags,
//          channel = step.step_type.channelType
//        )

        PreviewData.MoveToAnotherCampaignPreview()

      case _: CampaignStepData.AutoEmailStep | _: CampaignStepData.ManualEmailStep |
           _: CampaignStepData.AutoEmailMagicContentStep | _: CampaignStepData.ManualEmailMagicContentStep =>

        throw new Exception(CampaignService.impossibleEmailChannelCantComeHereError)




    }



    //                        val htmlBody = ParseUtils.getHtml(renderedBody.getOrElse(""))
    //                        val htmlSubject = ParseUtils.getHtml(renderedSubject.getOrElse(""))

    // Added to Prospect Steps for Frontend
    CampaignEditedPreviewStep(
      step_id = step.id,
      step_label = step.label.getOrElse(""),
      step_type = step.step_type,
      preview_data = preview_data,
      sent = false, //Todo fixme
      sent_at = None,
      completed_reason = if (prospect.internal.invalid_email.getOrElse(false) && stepIndex == 0) Some("invalid_email") else None,
    )
  }



  def generateHyperPersonalizedPreviewForProspect(
                                                   prospect_id: ProspectId,
                                                   account_id: AccountId,
                                                   campaign: Campaign,
                                                   team_id: TeamId,
                                                   org_id: OrgId,
                                                   step_id: StepId
                                                 )(implicit ec: ExecutionContext,
                                                   ws: WSClient,
                                                   materializer: Materializer,
                                                   system: ActorSystem,
                                                   Logger: SRLogger
                                                 ): Future[(String, String)] = {
    // Step 1: Collect all necessary data asynchronously
    val dataFuture: Future[ProspectPreviewData] = for {
      prospect_objects: Seq[ProspectObject] <- Future.fromTry(prospectDAOService.find(
        Logger = Logger,
        byCampaignId = Some(campaign.id),
        byProspectIds = Seq(prospect_id.id),
        teamId = team_id.id,
        limit = 1
      ))
      get_campaign_basic_info_opt: Option[CampaignBasicInfo] <- Future.fromTry(campaignService.getCampaignBasicInfo(
        teamId = team_id.id,
        campaginId = campaign.id
      ))
      previous_sent_steps: Seq[PreviousFollowUp] <- Future.fromTry(emailScheduledDAOService.getPreviousSentSteps(
        campaignId = campaign.id,
        prospectId = prospect_id.id,
        teamId = team_id
      ))
      ordered_steps: Seq[CampaignStepWithChildren] <- Future.successful(campaignDAOService.findOrderedSteps(
        campaignId = campaign.id,
        teamId = team_id
      ))
    } yield (ProspectPreviewData(
      prospect_objects = prospect_objects,
      campaign_basic_info_opt = get_campaign_basic_info_opt,
      previous_sent_steps = previous_sent_steps,
      ordered_steps = ordered_steps))

    // Step 2: Process the data and handle edge cases
    dataFuture.flatMap {
      case ProspectPreviewData(prospect_objects, Some(get_campaign_basic_info), previous_sent_steps, ordered_steps) if prospect_objects.nonEmpty =>
        val prospect_object = prospect_objects.head // Safe because we checked nonEmpty

        // Use nested for-comprehension to handle Option values safely
        val maybeContent: Option[Future[(String, String)]] = for {
          step: CampaignStepWithChildren <- ordered_steps.find(s => s.id == step_id.id)
          variant: CampaignStepVariantForScheduling <- step.variants.headOption
        } yield {
          // Check if campaign email settings exist
//          if (campaign.settings.campaign_email_settings.isEmpty) {
//            Logger.error(s"generateHyperPersonalizedPreviewForProspect :: CampaignId: ${campaign.id} team_id: ${team_id.id}:: step_id: ${step_id.id} :: prospect_id: ${prospect_id.id} :: Campaign has no email settings")
//            Future.failed(new Exception("Campaign has no email settings"))
//          } else {
//            val sender_email_settings_id = campaign.settings.campaign_email_settings.head.sender_email_setting_id

            /*
               step_id: StepId,
                step_data: CampaignStepData,
                prospect_object: ProspectObject,
                campaign_ai_generation_context: Option[CampaignAIGenerationContext],
                campaign_id: CampaignId,
                sender_email_settings_id: EmailSettingId,
                orgId: OrgId,
                accountId: AccountId,
                teamId: TeamId,
                previous_communications: Seq[PreviousFollowUp],
                orderedSteps: Seq[CampaignStepWithChildren],

             */
              val generatedContentFuture =  aiHyperPersonalizedGenerator.generateContentForTask(
                step_id = step_id,
                step_data = variant.step_data,
                prospect_object = prospect_object,
                campaign_ai_generation_context = get_campaign_basic_info.ai_generation_context,
                campaign_id = CampaignId(id = campaign.id),
                sender_email_settings_id = None,
                orgId = OrgId(id = org_id.id),
                accountId = account_id,
                teamId = team_id,
                previous_communications = previous_sent_steps,
                orderedSteps = ordered_steps
              )
            generatedContentFuture.map { case (data) =>
              (data.subject, EmailHelper.convertNewlinesToBrTags(data.body))
            }
        }

        // Handle the case where step or variant is missing
        maybeContent.getOrElse {
          Logger.error(s"generateHyperPersonalizedPreviewForProspect :: Step or variant not found :: CampaignId: ${campaign.id}, ProspectId: ${prospect_id.id}, StepId: ${step_id.id}")
          Future.failed(new Exception("Step or variant not found"))
        }

      case _ =>
        Logger.error(s"generateHyperPersonalizedPreviewForProspect :: CampaignId: ${campaign.id} team_id: ${team_id.id}:: step_id: ${step_id.id} :: prospect_id: ${prospect_id.id} :: Missing data: prospect or campaign basic info")
        Future.failed(new Exception("Missing data: prospect or campaign basic info"))
    }
  }



}

object CampaignPreviewService {


  def makeHTML(text: String): String = {

    /**
      * 13 Sep 2024
      *
      * When converting plain text to HTML,
      * Jsoup does not preserve the newline ("\n").
      * So replacing all the "\n" in the body with HTML `<br />` tags,
      * before parsing with Jsoup.
      */
    val doc = Jsoup.parse(text.replaceAll("\n", "<br />"))
    doc.html()

  }

  def getPreviousSubject(prospectSteps: Vector[CampaignEditedPreviewStep]): Option[String] = {
    var prevSub: Option[String] = None

    prospectSteps.foreach(ps => {
      ps.preview_data match {
        case autoEmailData: PreviewData.AutoEmailPreview =>
          prevSub = Some(autoEmailData.subject)

        case manualEmailData: PreviewData.ManualEmailPreview =>
          prevSub = Some(manualEmailData.subject)

        case autoEmailMagicPreview: PreviewData.AutoEmailMagicPreview =>
          prevSub = autoEmailMagicPreview.generated_subject

        case manualEmailMagicPreview: PreviewData.ManualEmailMagicPreview =>

          prevSub = manualEmailMagicPreview.generated_subject



        case viewProfileData: PreviewData.LinkedinViewProfilePreview =>
        case connectionRequestData: PreviewData.LinkedinConnectionRequestPreview =>
        case inmailData: PreviewData.LinkedinInMailPreview =>
        case linkedinMessageData: PreviewData.LinkedinMessagePreview =>
        case autoViewProfileData: PreviewData.AutoLinkedinViewProfilePreview =>
        case autoConnectionRequestData: PreviewData.AutoLinkedinConnectionRequestPreview =>
        case autoInMailData: PreviewData.AutoLinkedinInMailPreview =>
        case autoLinkedinMessageData: PreviewData.AutoLinkedinMessagePreview =>
        case whatsappData: PreviewData.WhatsappPreview =>
        case smsData: PreviewData.SmsPreview =>
        case generalData: PreviewData.GeneralPreview =>
        case callData: PreviewData.CallPreview =>
        case moveToAnotherCampaignData: PreviewData.MoveToAnotherCampaignPreview =>

      }
    })

    prevSub
  }
}
