package api.campaigns.services

import api.CONSTANTS.API_MSGS
import api.accounts.TeamId
import api.campaigns.{CampaignStepDAO, CampaignStepVariantDAO, CampaignStepWithChildren}
import api.campaigns.models.{CampaignStepData, CampaignStepId, CampaignStepType}
import api.columns.ProspectColumnDef
import sr_scheduler.models.ChannelType
import utils.templating.TemplateService

import scala.util.{Failure, Success}

sealed trait ValidateCampaignTemplateError

object ValidateCampaignTemplateError {
  case class ErrorWhileValidatingBody(err: Throwable) extends ValidateCampaignTemplateError

  case class ErrorWhileValidatingSubject(err: Throwable) extends ValidateCampaignTemplateError

  case class BodyCantHavePreviousSubject(err: Throwable) extends ValidateCampaignTemplateError

  case class SubjectCantHaveSignature(err: Throwable) extends ValidateCampaignTemplateError

  case class PreviousSubInFirstEmail(err: Throwable) extends ValidateCampaignTemplateError

  case class ErrorWhileValidatingNotes(err: Throwable) extends ValidateCampaignTemplateError

  case class NotesCantHavePreviousSubject(err: Throwable) extends ValidateCampaignTemplateError
}

sealed trait ValidateStepData {
  val campaignStepData: CampaignStepData
}

object ValidateStepData {

  case class ValidateStepVariantData(
                                      campaignStepData: CampaignStepData,
                                      campaignId: CampaignId,
                                      stepId: Option[Long],
                                      head_step_id: Option[Long],
                                    ) extends ValidateStepData


  case class ValidateCampaignTemplateData(
                                           campaignStepData: CampaignStepData,
                                         ) extends ValidateStepData

}

class CampaignTemplateService(
                               templateService: TemplateService,
                               prospectColumnDef: ProspectColumnDef,
                               campaignDAOService: CampaignDAOService,
                             ) {

  // will throw an exception if template syntax / merge-tags are invalid
  def validateCampaignTemplate(
                                teamId: Long,
                                validateStepData: ValidateStepData,
                                notes: Option[String]
                              ): Either[ValidateCampaignTemplateError, Boolean] = {

    val subjectAndBody = CampaignStepData.getSubjectAndBodyFromStepData(
      stepData = validateStepData.campaignStepData
    )

    val (subject, body) = (subjectAndBody.subject, subjectAndBody.body)


    val channel: ChannelType = validateStepData.campaignStepData.step_type.channelType
    val availableTagNames = prospectColumnDef.getAvailableTagNames(
      teamId = teamId, channel = Some(channel)
    )

    validateBody(
      body = body,
      availableTagNames = availableTagNames,
      channel = channel // since there will always be a channelType present here, so passing it directly.
    ) match {
      case Left(a) => Left(a)

      case Right(isBodyValid) =>

        validateSubject(
          teamId = TeamId(id = teamId),
          validateStepData = validateStepData,
          subject = subject,
          availableTagNames = availableTagNames,
          channel = channel
        ) match {
          case Left(a) => Left(a)

          case Right(isSubjectValid) =>
            notes match {
              case Some(note) =>
                validateNotes(
                  notes = note,
                  availableTagNames = availableTagNames,
                  channel = channel
                ) match {
                  case Left(a) => Left(a)
                  case Right(isNoteValid) => Right(isBodyValid && isSubjectValid && isNoteValid)
                }
              case None => Right(isBodyValid && isSubjectValid)
            }
        }

    }
  }

  private def validateBody(
                            body: String,
                            availableTagNames: Seq[String],
                            channel: ChannelType
                          ): Either[ValidateCampaignTemplateError, Boolean] = {

    templateService.isValid(
      template = body,
      availableTagNames = availableTagNames,
      channel = channel
    ) match {
      case Failure(err) =>
        if (err.getMessage == API_MSGS.INVALID_SYNTAX_IN_EMAIL)
          Left(ValidateCampaignTemplateError.ErrorWhileValidatingBody(err))
        else
          Left(ValidateCampaignTemplateError.ErrorWhileValidatingBody(new Exception(err.getMessage + " ,in " + ChannelType.getChannelName(channel) + " body.")))

      case Success(isValidBody) =>
        if (CampaignTemplateService.containsPrevSubjectMergeTag(s = body)) {
          Left(ValidateCampaignTemplateError.BodyCantHavePreviousSubject(err = new Exception("Body Cant have previous_subject")))
        } else {
          Right(isValidBody)
        }


    }

  }

  private def validateSubject(
                               validateStepData: ValidateStepData,
                               teamId: TeamId,
                               subject: String,
                               availableTagNames: Seq[String],
                               channel: ChannelType
                             ): Either[ValidateCampaignTemplateError, Boolean] = {

    templateService.isValid(
      template = subject,
      availableTagNames = availableTagNames,
      channel = channel
    ) match {

      case Failure(err) =>
        if (err.getMessage == API_MSGS.INVALID_SYNTAX_IN_EMAIL)
          Left(ValidateCampaignTemplateError.ErrorWhileValidatingSubject(err))
        else
          Left(ValidateCampaignTemplateError.ErrorWhileValidatingSubject(new Exception(err.getMessage + " ,in " + ChannelType.getChannelName(channel) + " subject.")))

      case Success(isValidSubject) =>

        if (subject.contains("{{signature}}")) {

          Left(ValidateCampaignTemplateError.SubjectCantHaveSignature(err = new Exception("Subject can't have Signature Merge tag")))

        } else {

          validateStepData match {

            case _: ValidateStepData.ValidateCampaignTemplateData =>

              Right(isValidSubject)

            case vsd: ValidateStepData.ValidateStepVariantData =>

              lazy val steps = campaignDAOService.findOrderedSteps(
                campaignId = vsd.campaignId.id,
                teamId = teamId
              )

              if (
                CampaignTemplateService.containsPrevSubjectMergeTag(s = subject)
                  && (
                  vsd.head_step_id.isEmpty ||
                    vsd.head_step_id == vsd.stepId ||
                    (vsd.stepId.isDefined && !CampaignTemplateService.prevStepsSupportPrevSubMergeTag(steps = steps.toList, stepId = CampaignStepId(id = vsd.stepId.get)))
                  )
              ) {

                Left(ValidateCampaignTemplateError.PreviousSubInFirstEmail(err = new Exception("previous_subject cant be used for first email")))

              } else {

                Right(isValidSubject)

              }

          }

        }

    }

  }

  private def validateNotes(
                             notes: String,
                             availableTagNames: Seq[String],
                             channel: ChannelType
                           ): Either[ValidateCampaignTemplateError, Boolean] = {

    templateService.isValid(
      template = notes,
      availableTagNames = availableTagNames,
      channel = channel
    ) match {
      case Failure(err) =>
        if (err.getMessage == API_MSGS.INVALID_SYNTAX_IN_EMAIL)
          Left(ValidateCampaignTemplateError.ErrorWhileValidatingNotes(err))
        else
          Left(ValidateCampaignTemplateError.ErrorWhileValidatingNotes(new Exception(err.getMessage + " ,in " + ChannelType.getChannelName(channel) + " notes.")))

      case Success(isValidNote) =>
        if (CampaignTemplateService.containsPrevSubjectMergeTag(s = notes)) {
          Left(ValidateCampaignTemplateError.NotesCantHavePreviousSubject(err = new Exception("Notes Cant have previous_subject")))
        } else {
          Right(isValidNote)
        }
    }

  }
}

object CampaignTemplateService {

  def prevStepsSupportPrevSubMergeTag(
                                       steps: List[CampaignStepWithChildren],
                                       stepId: CampaignStepId
                                     ): Boolean = {

    if (steps.map(_.id).contains(stepId.id)) {

      // step already exists

      val pos = steps.map(_.id).indexOf(stepId.id)

      val (prevSteps, _) = steps.splitAt(pos)

      prevSteps.nonEmpty &&
        CampaignTemplateService.somePreviousStepSupportsPrevSubMergeTag(
          prevStepTypes = prevSteps.map(_.step_type)
        )

    } else {

      // adding new step

      steps.nonEmpty &&
        CampaignTemplateService.somePreviousStepSupportsPrevSubMergeTag(
          prevStepTypes = steps.map(_.step_type)
        )

    }

  }

  private def somePreviousStepSupportsPrevSubMergeTag(
                                                       prevStepTypes: List[CampaignStepType]
                                                     ): Boolean = {

    prevStepTypes.reverse.exists { stepType =>

      CampaignTemplateService.stepTypeSupportsPrevSubjectMergeTag(
        campaignStepType = stepType
      )

    }

  }

  /**
   * stepTypeSupportsPrevSubjectMergeTag will return true,
   * if the step supports the {{previous_subject}} merge tag,
   * even if it does not have it.
   *
   * As of now only AutoEmailStep and ManualEmailStep support
   * the {{previous_subject}} merge tag.
   */
  def stepTypeSupportsPrevSubjectMergeTag(
                                           campaignStepType: CampaignStepType
                                         ): Boolean = {

    /**
     * 03-06-2024:
     *
     * Note:
     * For Linkedin In-mail subject is optional,
     * be careful if we ever support {{previous_subject}} merge tag for linkedin in-mail.
     */

    campaignStepType match {

      case CampaignStepType.AutoEmailStep
           | CampaignStepType.ManualEmailStep
           | CampaignStepType.ManualEmailMagicContent
           | CampaignStepType.AutoEmailMagicContent =>

        true

      case CampaignStepType.LinkedinConnectionRequest
           | CampaignStepType.LinkedinMessage
           | CampaignStepType.AutoLinkedinConnectionRequest
           | CampaignStepType.GeneralTask
           | CampaignStepType.WhatsappMessage
           | CampaignStepType.AutoLinkedinMessage
           | CampaignStepType.SmsMessage
           | CampaignStepType.CallStep
           | CampaignStepType.AutoLinkedinViewProfile
           | CampaignStepType.AutoLinkedinInmail
           | CampaignStepType.LinkedinInmail
           | CampaignStepType.MoveToAnotherCampaignStep
           | CampaignStepType.LinkedinViewProfile =>

        false

    }

  }

  def containsPrevSubjectMergeTag(s: String): Boolean = {

    CampaignTemplateService.containsMergeTag(
      s = s,
      mergeTagName = "previous_subject",
    )

  }

  def containsMergeTag(s: String, mergeTagName: String): Boolean = {

    val sList: Array[String] = s.split(" ").filter(_ != "")

    sList.contains(s"{{$mergeTagName}}") ||
      sList.containsSlice(Array("{{", mergeTagName, "}}")) ||
      sList.containsSlice(Array(s"{{$mergeTagName", "}}")) ||
      sList.containsSlice(Array("{{", s"$mergeTagName}}"))

  }

}
