package api.campaigns.services

import api.APIErrorResponse.{ErrorResponseProspectsAssignApi, ErrorResponseProspectsUnAssignApi, ErrorResponseUpdateProspectStatusApi}
import api.accounts.models.{AccountId, OrgId}
import api.AppConfig
import api.accounts.{Account, AccountService, EmailScheduledIdOrTaskId, PermType, PermissionMethods, PermissionUtils, TeamId}
import api.campaigns.dao.{CampaignSchedulingMetadataDAO, DripLogJedisDAO}
import api.campaigns.{CPAssignResult, Campaign, CampaignAssignProspectIdData, CampaignBasicInfo, CampaignForValidation, CampaignIdAndTeamId, CampaignProspectDAO, CampaignSettings, UpdateNextScheduleAtData}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignProspectInternalNote, CampaignSetNextToBeScheduledAtData, CampaignStepType, IgnoreProspectsInOtherCampaigns, SenderRotationStats}
import api.emails.RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet
import api.emails.{CampaignProspectStepScheduleLogData, CampaignProspectStepScheduleLogDataForIgnore, CampaignProspectStepScheduleLogsDAO, MessageSentAtByProspect, RejectionReasonForCampaignProspectStepSchedule}
import api.emails.daos.DomainPublicDNSDAO
import api.emails.models.CampaignProspectStepScheduleEventType
import api.linkedin.LinkedinConnectionsDAO
import api.phantombuster.LinkedinMessageThreadId
import api.prospects.dao.ProspectAddEventDAO
import api.prospects.service.ProspectServiceV2
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.{NewProspectAssociateWithCampaignData, ProspectCategory, ProspectCategoryId, ProspectDataForChannelScheduling, ProspectId, StepId}
import api.prospects.{CreateProspectEventDB, OwnedProspectsForAssigning, ProspectForValidation, ProspectUpdateCategoryTemp, ProspectUuid}
import api.sr_audit_logs.models.EventType
import api.tasks.pgDao.TaskIdAndTeamId
import eventframework.ProspectObject
import org.joda.time.DateTime
import play.api.libs.json.{JsError, JsNumber, JsResult, JsString, JsSuccess, JsValue, Json, Reads, Writes}
import scalikejdbc.DBSession
import sr_scheduler.models.CampaignForScheduling.CampaignEmailSettingForScheduler
import sr_scheduler.models.{CampaignForScheduling, ChannelType, EmailScheduledNew}
import utils.cache_utils.service.DbFallbackToCacheUtil
import utils.dbutils.DBUtils
import utils.email.EmailHelper
import utils.emailvalidation.EmailValidationService
import utils.mq.channel_scheduler.SchedulerMapStepIdAndDelay
import utils.mq.channel_scheduler.channels.NextStepFinderForDrip
import utils.mq.email.MQDomainServiceProviderDNSService
import utils.{Helpers, SRLogger}

import scala.util.{Failure, Success, Try}
import utils.sr_product_usage_data.models.SrUserFeatureUsageEvent
import utils.sr_product_usage_data.services.SrUserFeatureUsageEventService
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

sealed trait AssignProspectsError

object AssignProspectsError {
  case class ProspectAlreadyAssigned(error: String) extends AssignProspectsError

  case class CampaignNotFound(error: String) extends AssignProspectsError

  case class ProspectNotFound(error: String) extends AssignProspectsError

  case class ErrorWhileAssigningProspect(error: String) extends AssignProspectsError

  case class InvalidValueForIgnoreProspectsInOtherCampaigns(err: String) extends AssignProspectsError
}


sealed trait UpdateProspectStatusErrorV3

object UpdateProspectStatusErrorV3 {
  case class UpdateProspectsStatusError(err: String) extends UpdateProspectStatusErrorV3

  case class GetProspectIdFromUuidErrors(err: GetProspectIdFromUuidError) extends UpdateProspectStatusErrorV3

  case class FilterOwnedProspectError(err: Throwable) extends UpdateProspectStatusErrorV3

  case class FilterCampaignOwnedProspectError(err: Throwable) extends UpdateProspectStatusErrorV3

  case class GetProspectUuidFromId(err: Throwable) extends UpdateProspectStatusErrorV3

  case class GetCampaignIdFromUuidErrors(err: GetCampaignIdFromUuidError) extends UpdateProspectStatusErrorV3

  case class UpdateProspectsStatusValidationError(err: List[ErrorResponseUpdateProspectStatusApi]) extends UpdateProspectStatusErrorV3

  case class ServerError(err: Throwable) extends UpdateProspectStatusErrorV3
}

sealed trait GetProspectIdFromUuidError

object GetProspectIdFromUuidError {

  case class GetProspectIdError(err: Throwable) extends GetProspectIdFromUuidError


}

sealed trait GetCampaignIdFromUuidError

object GetCampaignIdFromUuidError {

  case class GetCampaignIdError(err: Throwable) extends GetCampaignIdFromUuidError


}

sealed trait AssignProspectsErrorV3

object AssignProspectsErrorV3 {
  case class AssignProspectError(err: AssignProspectsError) extends AssignProspectsErrorV3

  case class GetIgnoreProspectsInOtherCampaignsError(err: Throwable) extends AssignProspectsErrorV3

  case class GetProspectIdFromUuidErrors(err: GetProspectIdFromUuidError) extends AssignProspectsErrorV3

  case class FilterOwnedProspectError(err: Throwable) extends AssignProspectsErrorV3

  case class AssignProspectsValidationErrors(err: List[ErrorResponseProspectsAssignApi]) extends AssignProspectsErrorV3

  case class GetProspectUuidFromId(err: Throwable) extends AssignProspectsErrorV3
}

sealed trait UnAssignProspectsErrorV3

object UnAssignProspectsErrorV3 {
  case class UnAssignProspectError(err: String) extends UnAssignProspectsErrorV3

  case class GetProspectIdFromUuidErrors(err: GetProspectIdFromUuidError) extends UnAssignProspectsErrorV3

  case class FilterOwnedProspectError(err: Throwable) extends UnAssignProspectsErrorV3

  case class FilterCampaignOwnedProspectError(err: Throwable) extends UnAssignProspectsErrorV3

  case class UnAssignProspectsValidationErrors(err: List[ErrorResponseProspectsUnAssignApi]) extends UnAssignProspectsErrorV3

  case class GetProspectUuidFromId(err: Throwable) extends UnAssignProspectsErrorV3
}

case class AssignProspectsResponse(
                                    responseMsg: String,
                                    assignedProspectIdsLength: Int,
                                    assignedProspectIds: List[Long],
                                    campaignId: Long
                                  )

case class UnAssignProspectsResponse(
                                      responseMsg: String,
                                    )

case class AssignProspectsResponseV3(
                                      assignedProspectIdsLength: Int,
                                      totalAssignedProspectIds: List[Long],
                                      campaignId: Long
                                    )

case class CampaignId(id: Long) extends AnyVal {
  override def toString: String = id.toString
}

object CampaignId {
  implicit val reads: Reads[CampaignId] = new Reads[CampaignId] {
    override def reads(ev: JsValue): JsResult[CampaignId] = {
      ev match {
        case JsNumber(id) => JsSuccess(CampaignId(id = id.toLong))
        case randomValue => JsError(s"expected number, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[CampaignId] = new Writes[CampaignId] {
    override def writes(o: CampaignId): JsValue = JsNumber(o.id)
  }
}

case class CampaignUuid(uuid: String) extends AnyVal {
  override def toString: String = uuid
}

object CampaignUuid {
  implicit val reads: Reads[CampaignUuid] = new Reads[CampaignUuid] {
    override def reads(ev: JsValue): JsResult[CampaignUuid] = {
      ev match {
        case JsString(uuid) => JsSuccess(CampaignUuid(uuid = uuid))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[CampaignUuid] = new Writes[CampaignUuid] {
    override def writes(o: CampaignUuid): JsValue = JsString(o.uuid)
  }
}


class CampaignProspectService(
                               //                               prospectDAO: Prospect,
                               campaignService: CampaignService,
                               prospectAddEventDAO: ProspectAddEventDAO,
                               prospectServiceV2: ProspectServiceV2,
                               campaignProspectDAO: CampaignProspectDAO,
                               linkedinConnectionsDAO: LinkedinConnectionsDAO,
                               campaignProspectTimzonesJedisService: CampaignProspectTimezonesJedisService,
                               srUserFeatureUsageEventService: SrUserFeatureUsageEventService,
                               campaignCacheService: CampaignCacheService,
                               dbUtils: DBUtils,
                               prospectDAOService: ProspectDAOService,
                               prospectUpdateCategoryTemp: ProspectUpdateCategoryTemp,
                               campaignSchedulingMetadataDAO: CampaignSchedulingMetadataDAO,
                               domainPublicDNSDAO: DomainPublicDNSDAO,
                               mqDomainServiceProviderDNSService: MQDomainServiceProviderDNSService,
                               campaignProspectStepScheduleLogsDAO: CampaignProspectStepScheduleLogsDAO,
                               accountService: AccountService,
                               dripLogJedisDAO: DripLogJedisDAO
                             ) {

  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  def validateProspectOwnership(
                                 orgId: Long,
                                 permittedAccountIds: Seq[Long],
                                 doerAccountId: Long,
                                 doerAccountName: String,

                                 accountId: Long,
                                 teamId: Long,
                                 campaignId: Long,
                                 campaignName: String,
                                 prospectIds: List[Long],
                                 ignoreProspectsInOtherCampaigns: IgnoreProspectsInOtherCampaigns,
                                 Logger: SRLogger

                               ): Try[List[OwnedProspectsForAssigning]] = {

    /**
     * 1. check if user has ownership of the prospects: if not throw error
     */

    prospectServiceV2.filterOwnedProspects(
      prospectIds = prospectIds,
      permittedAccountIds = permittedAccountIds,
      teamId = teamId,
      orgId = OrgId(orgId),
      SRLogger = Logger
    ) match {

      case Failure(exception) => Failure(exception = exception)

      case Success(accountOwnedProspects) =>
        if (prospectIds.length != accountOwnedProspects.length) {

          val msg = "Given prospects do not exist in your account"

          // REF: https://stackoverflow.com/a/********
          val accountOwnedProspectsSet = accountOwnedProspects.map(_.prospect_id).toSet
          val prospectIdsThatAreNotOwned = prospectIds.filterNot(accountOwnedProspectsSet)

          Logger.fatal(s"Campaign.assign doNotExistError: $msg :: prospectIds.length: ${prospectIds.length} :: ${accountOwnedProspects.length} :: prospectIdsThatAreNotOwnedCount: ${prospectIdsThatAreNotOwned.length} :: prospectIdsCount : ${prospectIds.length} :: accountOwnedProspectIdsCount: ${accountOwnedProspects.map(_.prospect_id).length}")

          Failure(new Exception(msg))
        }
        else {
          Success(accountOwnedProspects)
        }
    }


  }

  def assignProspectsToCampaign(
                                 orgId: Long,
                                 permittedAccountIds: Seq[Long],
                                 doerAccountId: Long,
                                 doerAccountName: String,

                                 accountId: Long,
                                 teamId: Long,
                                 campaignId: Long,
                                 campaignName: String,
                                 campaignSettings: CampaignSettings,
                                 prospectIds: List[Long],
                                 ignoreProspectsInOtherCampaigns: IgnoreProspectsInOtherCampaigns,
                                 Logger: SRLogger

                               ): Try[CPAssignResult] = Try {

    if (prospectIds.isEmpty) {
      if (teamId == 11202) {
        Logger.debug(s"assignProspectsToCampaign prospects is empty")
      }
      Success(
        CPAssignResult(
          prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign = List(),

          prospectIdsIgnoredBecauseInOtherCampaigns = List(),

          newlyAssignedProspectIds = List()
        )
      )

    } else {
      if (teamId == 11202) {
        Logger.debug(s"assignProspectsToCampaign prospects - $prospectIds")
      }
      validateProspectOwnership(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        permittedAccountIds = permittedAccountIds,
        campaignId = campaignId,
        campaignName = campaignName,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = ignoreProspectsInOtherCampaigns,
        Logger = Logger

      ) match {

        case Failure(exception) => Failure(exception = exception)

        case Success(accountOwnedProspects) =>
          if (teamId == 11202) {
            Logger.debug(s"assignProspectsToCampaign accountOwnedProspects - $accountOwnedProspects")
          }
          assign(
            orgId = orgId,
            accountId = accountId,
            teamId = teamId,
            doerAccountId = doerAccountId,
            doerAccountName = doerAccountName,
            permittedAccountIds = permittedAccountIds,
            campaignId = campaignId,
            campaignName = campaignName,
            campaignSettings = campaignSettings,
            prospectIds = prospectIds,
            accountOwnedProspects = accountOwnedProspects,
            ignoreProspectsInOtherCampaigns = ignoreProspectsInOtherCampaigns,
            Logger = Logger,
          ).map { assignRes =>

            prospectUpdateCategoryTemp.autoUpdateProspectCategory(
              teamId = TeamId(id = teamId),
              doerAccountId = AccountId(id = doerAccountId),
              prospectIds = assignRes.newlyAssignedProspectIds.map(pid => ProspectId(id = pid)),
              newProspectCategory = ProspectCategory.OPEN,
            )(logger = Logger)

            assignRes

          }

      }
    }
  }.flatten

  def getLinkedInProfileConnectedUuid(
                                  campaignId: CampaignId,
                                  linkedinProfileUrl: Option[String],
                                  teamId: TeamId
                                  )(using Logger: SRLogger): Try[Option[String]] = {
    linkedinProfileUrl match {

      case None => Success(None)

      case Some(profile_url) => 
        linkedinConnectionsDAO.getLinkedinConnectionUuid(
        campaignId = campaignId,
        linkedinProfileUrl = profile_url,
        teamId = teamId
      ) match {
          case Failure(exception) => 
            Logger.shouldNeverHappen(s"Error while fetching connection status team_id:: ${teamId.id} campaign_id: ${campaignId.id} linkedin url :: ${profile_url}", err= Some(exception))
            Failure(exception)
          
          case Success(res) => 
            if(res.isDefined){
              Logger.info(s"Connection found for profile url:: ${profile_url} in campaign:: ${campaignId.id} team_id:: ${teamId.id}. with entry :: ${res.get}")
            }else{
              Logger.info(s"Connection not found for profile url:: ${profile_url} in campaign:: ${campaignId.id} team_id:: ${teamId.id}")
            }
            Success(res)
        }
        
    }
  }

  private def assign(
                      orgId: Long,
                      permittedAccountIds: Seq[Long],
                      doerAccountId: Long,
                      doerAccountName: String,

                      accountId: Long,
                      teamId: Long,
                      campaignId: Long,
                      campaignName: String,
                      campaignSettings: CampaignSettings,
                      prospectIds: List[Long],
                      ignoreProspectsInOtherCampaigns: IgnoreProspectsInOtherCampaigns,
                      accountOwnedProspects: List[OwnedProspectsForAssigning],
                      Logger: SRLogger
                      // moveToNewCampaign: Boolean,
                      // allow_assigning_prospects_to_multiple_campaigns: Boolean

                    ): Try[CPAssignResult] = Try {

    given srLogger: SRLogger = Logger // fixme given

    // should have been transaction but I could not figure out how to check / modify the isolation level

    /**
     * Pseudo Code:
     *
     * 1. check if user has ownership of the prospects: if not owned, throw error
     *
     * 2. check if prospects are already assigned to the campaign, if yes ignore such prospects
     *
     * 3. check if we have to ignore prospects added to or active in any other campaign
     */

    /**
     *
     * 2. check if prospects are already assigned to the campaign, if yes ignore such prospects
     *
     */
    val prospectIdsAlreadyAssignedToThisCampaign = campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign(
      prospectIds = accountOwnedProspects.map(_.prospect_id),
      campaignId = campaignId,
      team_id = TeamId(teamId)
    )

    if (teamId == 11202) {
      Logger.debug(s"assign prospectIdsAlreadyAssignedToThisCampaign - $prospectIdsAlreadyAssignedToThisCampaign")
    }

    val ownedProspectsNotAlreadyAssignedToGivenCampaign = accountOwnedProspects
      .filterNot(pr => {
        prospectIdsAlreadyAssignedToThisCampaign.contains(pr.prospect_id)
      })

    if (teamId == 11202) {
      Logger.debug(s"assign ownedProspectsNotAlreadyAssignedToGivenCampaign - $ownedProspectsNotAlreadyAssignedToGivenCampaign")
    }

    val prospectIdsToIgnoreWhileAssigning: List[Long] = if (ownedProspectsNotAlreadyAssignedToGivenCampaign.isEmpty) List()
    else
      ignoreProspectsInOtherCampaigns match {

        case IgnoreProspectsInOtherCampaigns.DoNotIgnore => List()
        case IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns =>
          campaignProspectDAO.getProspectsActiveInOtherCampaigns(
            prospectIds = ownedProspectsNotAlreadyAssignedToGivenCampaign.map(_.prospect_id),
            campaignId = campaignId,
            teamId = TeamId(teamId)
          )
        case IgnoreProspectsInOtherCampaigns.IgnoreProspectsAddedInOtherCampaigns =>
          campaignProspectDAO.getProspectsAddedInOtherCampaigns(
            prospectIds = ownedProspectsNotAlreadyAssignedToGivenCampaign.map(_.prospect_id),
            campaignId = campaignId,
            teamId = TeamId(teamId)
          )
      }

    if (teamId == 11202) {
      Logger.debug(s"assign ownedProspectsNotAlreadyAssignedToGivenCampaign - $ownedProspectsNotAlreadyAssignedToGivenCampaign")
    }

    var prospectsToBeAssignedToNewCampaign = ownedProspectsNotAlreadyAssignedToGivenCampaign
      .filterNot(p => prospectIdsToIgnoreWhileAssigning.contains(p.prospect_id))

    if (teamId == 11202) {
      Logger.debug(s"assign prospectsToBeAssignedToNewCampaign - $prospectsToBeAssignedToNewCampaign")
    }
    val stepIdsAndProspectIds = campaignProspectDAO.getOldStepIdForCampaign(prospectIds =
      prospectsToBeAssignedToNewCampaign.map(p => ProspectId(p.prospect_id)),
      campaignId = CampaignId(campaignId),
      teamId = TeamId(teamId))
    if (teamId == 11202) {
      Logger.debug(s"assign stepIdsAndProspectIds - $stepIdsAndProspectIds")
    }
    prospectsToBeAssignedToNewCampaign = prospectsToBeAssignedToNewCampaign.map { p =>

      p.copy(synced_previously_sent_step_for_deleted_prospect_step_id = stepIdsAndProspectIds.find(stepAndProspect => stepAndProspect._2.id == p.prospect_id).map(_._1.id))
    }
    if (teamId == 11202) {
      Logger.debug(s"assign prospectsToBeAssignedToNewCampaign - $prospectsToBeAssignedToNewCampaign")
    }

    //      Logger.info(s"DB prospects assign 2: ${accountId} : $campaignId : ${prospectsToBeAssignedToNewCampaign.size}")

    val allNewlyAssignedProspectIds: List[Long] = if (prospectsToBeAssignedToNewCampaign.isEmpty) {

      List[Long]()

    } else {
      prospectsToBeAssignedToNewCampaign
        .grouped(500)
        .toList
        .flatMap(pgroup => {

          val assignedProspectIds = campaignProspectDAO.assignProspectsToCampaign(
            pgroup = pgroup,
            campaignId = campaignId,
            teamId = TeamId(teamId)
          )
          if (assignedProspectIds.nonEmpty) {
            //USE_CASE_scheduleForUpdateWhenCampaignIsUpdated
            campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated(
              campaignId = CampaignId(campaignId),
              teamId = TeamId(teamId)
            ) match {
              case Success(value) => //DO NOTHING
              case Failure(exception) => Logger.fatal(s"Failed scheduleForUpdateWhenCampaignIsUpdated", exception)
            }
          }

          // add assigned events
          val events = assignedProspectIds.map(id => CreateProspectEventDB(

            event_type = EventType.EVENT_V3_PROSPECT_ADDED,
            doer_account_id = Some(doerAccountId),
            doer_account_name = Some(doerAccountName),

            assigned_to_account_id = None,
            assigned_to_account_name = None,

            old_category = None,
            new_category = None,

            prospect_id = id,
            email_thread_id = None,

            campaign_id = Some(campaignId),
            campaign_name = Some(campaignName),

            step_id = None,
            step_name = None,

            clicked_url = None,

            account_id = accountId,
            team_id = teamId,
            email_scheduled_id = None,

            created_at = org.joda.time.DateTime.now(),

            task_type = None,
            channel_type = None,
            task_uuid = None,
            call_conference_uuid = None,

            duplicates_merged_at = None,
            total_merged_prospects = None,
            potential_duplicate_prospect_id = None

          ))

          prospectAddEventDAO.addEvents(events = events).get

          assignedProspectIds

        })


    }

    val featureUsageFunnelResult: Try[Int] = if (allNewlyAssignedProspectIds.isEmpty) {
      Success(0)
    } else {
      srUserFeatureUsageEventService.addFeatureUsageEvent(
        orgId = orgId,
        event = SrUserFeatureUsageEvent.AddedFirstProspectInCampaign(
          added_first_prospect_in_campaign_at = DateTime.now()
        )
      )
    }

    featureUsageFunnelResult match {
      case Failure(e) =>
        Failure(e)
      case Success(value) =>


        if (prospectsToBeAssignedToNewCampaign.length != allNewlyAssignedProspectIds.length) {
          Logger.fatal(s"prospectsToBeAssignedToNewCampaign.length != allNewlyAssignedProspectIds.length:: allNewlyAssignedProspectIds: $allNewlyAssignedProspectIds :: prospectsToBeAssignedToNewCampaign: $prospectsToBeAssignedToNewCampaign")
        }

        // if new prospects are added to the campaign, set it to be scheduled immediately
        val updatedNextToBeScheduledAt = if (allNewlyAssignedProspectIds.isEmpty) Success(0) else {
          campaignCacheService.resetSenderRotationById(
            campaignId = CampaignId(campaignId),
            teamId = TeamId(teamId)
          )
          // schedule immediately when campaign's limit etc. settings are updated
          campaignService.setNextToBeScheduledAt(

            flowData = CampaignSetNextToBeScheduledAtData.AddedProspectToCampaignFlow(

              campaignSettings = campaignSettings,
              campaignId = CampaignId(campaignId),
              teamId = TeamId(teamId)
            ),

            Logger = Logger,
            team_id = TeamId(id = teamId)
          )

        }

        if (allNewlyAssignedProspectIds.nonEmpty) {
          campaignCacheService.resetCampaignStats(campaignId = campaignId, teamId = teamId)
        }

        updatedNextToBeScheduledAt
          .map(_ => {

            CPAssignResult(
              prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign = prospectIdsAlreadyAssignedToThisCampaign,

              prospectIdsIgnoredBecauseInOtherCampaigns = prospectIdsToIgnoreWhileAssigning,

              newlyAssignedProspectIds = allNewlyAssignedProspectIds
            )
          })
    }
  }.flatten

  def getProspectIdsToIgnoreWhileAssigning(
                                            ignoreProspectsInOtherCampaigns: IgnoreProspectsInOtherCampaigns,
                                            ownedProspectsNotAlreadyAssignedToGivenCampaign: List[OwnedProspectsForAssigning],
                                            campaignId: Long,
                                            teamId: TeamId
                                          ): Try[List[Long]] = {
    if (ownedProspectsNotAlreadyAssignedToGivenCampaign.isEmpty) {
      //we are returning the list of prospect ids to ignore while assigning
      //if ownedProspectsNotAlreadyAssignedToGivenCampaign comes out to be empty, we are returning an empty list.
      Success(List())
    } else {

      ignoreProspectsInOtherCampaigns match {

        case IgnoreProspectsInOtherCampaigns.DoNotIgnore => Success(List())
        case IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns =>
          Try {
            campaignProspectDAO.getProspectsActiveInOtherCampaigns(
              prospectIds = ownedProspectsNotAlreadyAssignedToGivenCampaign.map(_.prospect_id),
              campaignId = campaignId,
              teamId = teamId
            )
          }
        case IgnoreProspectsInOtherCampaigns.IgnoreProspectsAddedInOtherCampaigns =>
          Try {
            campaignProspectDAO.getProspectsAddedInOtherCampaigns(
              prospectIds = ownedProspectsNotAlreadyAssignedToGivenCampaign.map(_.prospect_id),
              campaignId = campaignId,
              teamId = teamId
            )
          }
      }
    }

  }


  def getProspectIdDataForAssigning(
                                     orgId: Long,
                                     permittedAccountIds: Seq[Long],
                                     doerAccountId: Long,
                                     doerAccountName: String,

                                     accountId: Long,
                                     teamId: Long,
                                     campaignId: Long,
                                     campaignName: String,
                                     prospectIds: List[Long],
                                     ignoreProspectsInOtherCampaigns: IgnoreProspectsInOtherCampaigns,
                                     accountOwnedProspects: List[OwnedProspectsForAssigning],
                                   ): Try[CampaignAssignProspectIdData] = {

    for {

      prospectIdsAlreadyAssignedToThisCampaign: List[Long] <- Try {
        campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign(
          prospectIds = accountOwnedProspects.map(_.prospect_id),
          campaignId = campaignId,
          team_id = TeamId(teamId)
        )
      }

      ownedProspectsNotAlreadyAssignedToGivenCampaign: List[OwnedProspectsForAssigning] <- Try {
        accountOwnedProspects
          .filterNot(pr => {
            prospectIdsAlreadyAssignedToThisCampaign.contains(pr.prospect_id)
          })
      }

      prospectIdsToIgnoreWhileAssigning: List[Long] <- getProspectIdsToIgnoreWhileAssigning(
        ignoreProspectsInOtherCampaigns = ignoreProspectsInOtherCampaigns,
        ownedProspectsNotAlreadyAssignedToGivenCampaign = ownedProspectsNotAlreadyAssignedToGivenCampaign,
        campaignId = campaignId,
        teamId = TeamId(teamId)
      )

      prospectsToBeAssignedToNewCampaign: List[OwnedProspectsForAssigning] <- Try {
        ownedProspectsNotAlreadyAssignedToGivenCampaign
          .filterNot(p => prospectIdsToIgnoreWhileAssigning.contains(p.prospect_id))
      }

      /*
      This is used to check if the prospect was a part of the campaign before and if so,
       add the correct step id for the campaign prospect
      * */

      stepIdsAndProspectIds: List[(StepId, ProspectId, DateTime)] <- Try {
        campaignProspectDAO.getOldStepIdForCampaign(
          prospectIds = prospectsToBeAssignedToNewCampaign.map(p => ProspectId(p.prospect_id)),
          campaignId = CampaignId(campaignId),
          teamId = TeamId(id = teamId))
      }

      prospectsToBeAssignedToNewCampaign: List[OwnedProspectsForAssigning] <- Try {
        prospectsToBeAssignedToNewCampaign.map { p =>
          p.copy(
            synced_previously_sent_step_for_deleted_prospect_step_id = stepIdsAndProspectIds.find(stepAndProspect =>
                stepAndProspect._2.id == p.prospect_id)
              .map(_._1.id))
        }
      }


    } yield {

      CampaignAssignProspectIdData(
        prospectIdsIgnoredBecauseInOtherCampaigns = prospectIdsToIgnoreWhileAssigning,
        prospectsToBeAssignedToNewCampaign = prospectsToBeAssignedToNewCampaign,
        prospectIdsAlreadyAssignedToThisCampaign = prospectIdsAlreadyAssignedToThisCampaign)

    }
  }

  def postAssignActions(
                         allNewlyAssignedProspectIds: List[Long],
                         orgId: Long,
                         prospectsToBeAssignedToNewCampaign: List[OwnedProspectsForAssigning],
                         campaignId: Long,
                         campaignSettings: CampaignSettings,
                         teamId: Long,
                         Logger: SRLogger
                       ): Try[Int] = {

    given srLogger: SRLogger = Logger // fixme given

    val featureUsageFunnelResult: Try[Int] = if (allNewlyAssignedProspectIds.isEmpty) {
      Success(0)
    } else {
      srUserFeatureUsageEventService.addFeatureUsageEvent(
        orgId = orgId,
        event = SrUserFeatureUsageEvent.AddedFirstProspectInCampaign(
          added_first_prospect_in_campaign_at = DateTime.now()
        )
      )
    }

    featureUsageFunnelResult match {
      case Failure(e) =>
        Failure(e)

      case Success(_) =>

        if (prospectsToBeAssignedToNewCampaign.length != allNewlyAssignedProspectIds.length) {
          Logger.fatal(s"prospectsToBeAssignedToNewCampaign.length != allNewlyAssignedProspectIds.length:: allNewlyAssignedProspectIds: $allNewlyAssignedProspectIds :: prospectsToBeAssignedToNewCampaign: $prospectsToBeAssignedToNewCampaign")
        }

        // if new prospects are added to the campaign, set it to be scheduled immediately
        val updatedNextToBeScheduledAt = if (allNewlyAssignedProspectIds.isEmpty) {
          Success(0)
        } else {

          // schedule immediately when campaign's limit etc. settings are updated
          campaignService.setNextToBeScheduledAt(

            flowData = CampaignSetNextToBeScheduledAtData.AddedProspectToCampaignFlow(

              campaignSettings = campaignSettings,
              campaignId = CampaignId(campaignId),
              teamId = TeamId(teamId)
            ),

            team_id = TeamId(id = teamId),
            Logger = Logger,
          )

        }

        if (allNewlyAssignedProspectIds.nonEmpty) {
          campaignCacheService.resetCampaignStats(campaignId = campaignId, teamId = teamId)
        }

        updatedNextToBeScheduledAt
    }
  }

  def assignV3(
                orgId: Long,
                permittedAccountIds: Seq[Long],
                doerAccountId: Long,
                doerAccountName: String,

                accountId: Long,
                teamId: Long,
                campaignId: Long,
                campaignName: String,
                campaignSettings: CampaignSettings,
                prospectIds: List[Long],
                ignoreProspectsInOtherCampaigns: IgnoreProspectsInOtherCampaigns,
                accountOwnedProspects: List[OwnedProspectsForAssigning],
                Logger: SRLogger

              ): Try[CPAssignResult] = Try {
    if (prospectIds.isEmpty) {

      Success(
        CPAssignResult(
          prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign = List(),

          prospectIdsIgnoredBecauseInOtherCampaigns = List(),

          newlyAssignedProspectIds = List()
        )
      )

    } else {

      getProspectIdDataForAssigning(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        permittedAccountIds = permittedAccountIds,
        campaignId = campaignId,
        campaignName = campaignName,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = ignoreProspectsInOtherCampaigns,
        accountOwnedProspects = accountOwnedProspects
      ).flatMap(prospectIdData => {

        val prospectsToBeAssignedToNewCampaign: List[OwnedProspectsForAssigning] = prospectIdData.prospectsToBeAssignedToNewCampaign


        val allNewlyAssignedProspectIds: List[Long] = if (prospectsToBeAssignedToNewCampaign.isEmpty) {

          List[Long]()

        } else {
          prospectsToBeAssignedToNewCampaign
            .grouped(500)
            .toList
            .flatMap(pgroup => {

              val assignedProspectIds = campaignProspectDAO.assignProspectsToCampaign(
                pgroup = pgroup,
                campaignId = campaignId,
                teamId = TeamId(teamId)
              )
              if (assignedProspectIds.nonEmpty) {
                //USE_CASE_scheduleForUpdateWhenCampaignIsUpdated
                campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated(
                  campaignId = CampaignId(campaignId),
                  teamId = TeamId(teamId)
                ) match {
                  case Success(value) => //DO NOTHING
                  case Failure(exception) => Logger.fatal(s"Failed scheduleForUpdateWhenCampaignIsUpdated", exception)
                }
              }

              // add assigned events
              val events = assignedProspectIds.map(id => CreateProspectEventDB(

                event_type = EventType.EVENT_V3_PROSPECT_ADDED,
                doer_account_id = Some(doerAccountId),
                doer_account_name = Some(doerAccountName),

                assigned_to_account_id = None,
                assigned_to_account_name = None,

                old_category = None,
                new_category = None,

                prospect_id = id,
                email_thread_id = None,

                campaign_id = Some(campaignId),
                campaign_name = Some(campaignName),

                step_id = None,
                step_name = None,

                clicked_url = None,

                account_id = accountId,
                team_id = teamId,
                email_scheduled_id = None,

                created_at = org.joda.time.DateTime.now(),

                task_type = None,
                channel_type = None,
                task_uuid = None,
                call_conference_uuid = None,

                duplicates_merged_at = None,
                total_merged_prospects = None,
                potential_duplicate_prospect_id = None

              ))

              prospectAddEventDAO.addEvents(events = events).get

              assignedProspectIds

            })


        }

        val updatedNextToBeScheduledAt: Try[Int] = postAssignActions(
          allNewlyAssignedProspectIds = allNewlyAssignedProspectIds,
          orgId = orgId,
          prospectsToBeAssignedToNewCampaign = prospectsToBeAssignedToNewCampaign,
          campaignId = campaignId,
          campaignSettings = campaignSettings,
          teamId = teamId,
          Logger = Logger
        )


        updatedNextToBeScheduledAt
          .map(_ => {

            prospectUpdateCategoryTemp.autoUpdateProspectCategory(
              teamId = TeamId(id = teamId),
              doerAccountId = AccountId(id = doerAccountId),
              prospectIds = allNewlyAssignedProspectIds.map(pid => ProspectId(id = pid)),
              newProspectCategory = ProspectCategory.OPEN,
            )(logger = Logger)

            CPAssignResult(
              prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign = prospectIdData.prospectIdsAlreadyAssignedToThisCampaign,

              prospectIdsIgnoredBecauseInOtherCampaigns = prospectIdData.prospectIdsIgnoredBecauseInOtherCampaigns,

              newlyAssignedProspectIds = allNewlyAssignedProspectIds
            )
          })
      })
    }
  }.flatten


  def unassign(
                permittedOwnerIds: Seq[Long],
                doerAccountId: Long,
                teamId: Long,
                doerAccountName: String,
                campaignId: Long,
                prospectIds: Seq[Long],
                Logger: SRLogger
              ): Try[Int] = {

    given srLogger: SRLogger = Logger // fixme given

    campaignProspectDAO.unassign(
      permittedOwnerIds = permittedOwnerIds,
      doerAccountId = doerAccountId,
      teamId = teamId,
      doerAccountName = doerAccountName,
      campaignId = campaignId,
      prospectIds = prospectIds
    ) match {
      case Failure(err) => Failure(err)

      case Success(totalUnassigned) =>
        //USE_CASE_scheduleForUpdateWhenCampaignIsUpdated
        campaignCacheService.resetSenderRotationById(
          campaignId = CampaignId(campaignId),
          teamId = TeamId(teamId)
        )
        campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated(
          campaignId = CampaignId(campaignId),
          teamId = TeamId(teamId)
        ) match {
          case Success(value) => //DO NOTHING
          case Failure(exception) => Logger.fatal(s"Failed scheduleForUpdateWhenCampaignIsUpdated", exception)
        }

        if (totalUnassigned > 0) {
          campaignCacheService.resetCampaignStats(campaignId = campaignId, teamId = teamId)
        }

        Success(totalUnassigned)
    }
  }

  def markCampaignsAsRepliedAndCompleted(
                                          campaign_ids: Seq[Long],
                                          messageSentAtByProspect: MessageSentAtByProspect,
                                          completed_because_secondary_prospect_email_id: Option[Long]
                                        ): Try[List[Long]] = {

    val dbAndSession = dbUtils.startLocalTx()
    val db = dbAndSession.db
    implicit val session: DBSession = dbAndSession.session

    val res = campaignProspectDAO.markCampaignAsRepliedAndCompleted(
      campaign_ids = campaign_ids,
      messageSentAtByProspect = messageSentAtByProspect,
      completed_because_secondary_prospect_email_id = completed_because_secondary_prospect_email_id
    )

    dbUtils.commitAndCloseSession(db)

    res

  }

  def getCampaignsWhoseReplyWillBeTrackedInThisMessageThread(
                                                              messageThreadId: LinkedinMessageThreadId,
                                                              teamId: TeamId
                                                            ): Try[List[CampaignId]] = {

    campaignProspectDAO.getCampaignsWhoseReplyWillBeTrackedInThisMessageThread(
      messageThreadId = messageThreadId,
      teamId = teamId
    )

  }

  def getAllDistinctTimezones(
                               campaignId: Long,
                               teamId: Long,
                               campaignTimezone: String
                             )(using logger: SRLogger): Try[Set[String]] = {

    val cacheIdData = CampaignId(id = campaignId)

    campaignProspectTimzonesJedisService.get(
      idData = cacheIdData
    ) match {
      case None =>

        logger.info(s"getAllDistinctTimezones DB_HIT : cid_${campaignId} tid_${teamId}")

        campaignProspectDAO.getDistinctProspectTimezones(
            teamId = teamId,
            campaignId = campaignId
          )
          .map(timezones => {

            val allTimezones = if (timezones.contains(campaignTimezone)) {
              timezones
            } else {
              timezones ++ List(campaignTimezone)
            }

            campaignProspectTimzonesJedisService.set(
              value = allTimezones,
              idData = cacheIdData
            )

            allTimezones.toSet
          })

      case Some(allTimezones) =>
        if (allTimezones.contains(campaignTimezone)) {

          Success(allTimezones.toSet)

        } else {


          // sometimes the campaignTimezone is not present in the cache, so we add it
          // and then update the cache. This can happen if the user has updated the campaign timezone just now.

          val tzsWithCampaignTz = allTimezones ++ List(campaignTimezone)

          campaignProspectTimzonesJedisService.set(
            value = tzsWithCampaignTz,
            idData = cacheIdData
          )

          Success(tzsWithCampaignTz.toSet)

        }
    }

  }

  def unassignProspectsFromMainPage(

                                     permittedOwnerIds: Seq[Long],
                                     doerAccountId: Long,
                                     teamId: Long,
                                     doerAccountName: String,
                                     prospectIds: List[Long],
                                     campaignIds: Seq[Long],
                                     Logger: SRLogger

                                   ) = {

    given srLogger: SRLogger = Logger // fixme given
    campaignProspectDAO.unassignProspectsFromMainPage(
      permittedOwnerIds = permittedOwnerIds,
      doerAccountId = doerAccountId,
      teamId = teamId,
      doerAccountName = doerAccountName,
      prospectIds = prospectIds,
      campaignIds = campaignIds
    ) match {

      case Failure(err) => Failure(err)

      case Success(unassigned) =>
        if (unassigned > 0) {
          campaignIds.foreach(cId =>
            campaignCacheService.resetCampaignStats(campaignId = cId, teamId = teamId)
          )
        }
        Success(unassigned)
    }
  }

  def updateCampaignProspectStatusToDone(
                                          campaignId: CampaignId,
                                          prospectId: ProspectId,
                                          ownerAccountIdOpt: Option[AccountId],
                                          taskId: String,
                                          teamId: TeamId,
                                        )(using logger: SRLogger): Try[Int] = {

    campaignProspectDAO.updateCampaignProspectStatusToDone(
      campaignId = campaignId,
      prospectId = prospectId,
      taskId = taskId
    ).map { res =>

      ownerAccountIdOpt match {

        case None =>

          logger.error(
            msg = s"linkedinAccountOwnerIdOpt None - teamId: $teamId :: prospectId: $prospectId :: campaignId: $campaignId"
          )

          Success(0)

        case Some(ownerAccountId) =>

          prospectUpdateCategoryTemp.autoUpdateProspectCategory(
            teamId = teamId,
            doerAccountId = ownerAccountId,
            prospectIds = Seq(prospectId),
            newProspectCategory = ProspectCategory.CONTACTED,
          )

      }

      res

    }

  }


  def fetchProspectsForPreEmailValidation(
                                           campaignId: CampaignId,
                                           teamId: TeamId,
                                           minimumPreEmailValidatedProspectsCount: Int
                                         )(using logger: SRLogger): Try[List[ProspectForValidation]] = {

    prospectDAOService.getProspectCategoryId(
      teamId = teamId,
      text_id = ProspectCategory.DO_NOT_CONTACT,
      account = None
    ) match {
      case Failure(err) =>
        logger.fatal(msg = s"[fetchProspectsForPreEmailValidation prospectDAOService.getProspectCategoryId] teamId::$teamId text_id::${ProspectCategory.DO_NOT_CONTACT}", err = err)
        Failure(err)
      case Success(doNotContactCategoryId: ProspectCategoryId) =>

        campaignProspectDAO
          .fetchProspectsForPreEmailValidation(
            minimumPreEmailValidatedProspectsCount = minimumPreEmailValidatedProspectsCount,
            campaignId = campaignId,
            teamId = teamId,
            doNotContactCategoryId = doNotContactCategoryId.id
          ) match {
          case Failure(err) =>
            logger.fatal(s"An error Occurred while fetching list Of prospects for validation " +
              s"for campaign_id:${campaignId} team_id:${teamId}", err = err)

            Failure(err)

          case Success(value) => Success(value)
        }
    }


  }

  def checkMinimumValidatedProspectCountAtleastMinimum(
                                                        campaignId: CampaignId,
                                                        teamId: TeamId,
                                                        minCount: Long
                                                      )(using logger: SRLogger): Try[Boolean] = {

    prospectDAOService.getProspectCategoryId(
      teamId = teamId,
      text_id = ProspectCategory.DO_NOT_CONTACT,
      account = None
    ) match {

      case Failure(err) => Failure(err)

      case Success(doNotContactCategoryId: ProspectCategoryId) =>

        campaignProspectDAO
          .checkProspectCountAtleastMinimum(
            campaignId = campaignId,
            minCount = minCount,
            doNotContactCategoryId = doNotContactCategoryId.id,
            teamId = teamId
          ) match {
          case Failure(err) =>

            logger.fatal(s"An error Occurred while checking validated prospects count  " +
              s"for campaign_id:${campaignId} team_id:${teamId}", err = err)
            Failure(err)

          case Success(value) => Success(value.getOrElse(false))
        }
    }
  }


  def getSenderRotationStats(
                              campaignId: CampaignId,
                              teamId: TeamId,
                              least_amount_of_prospects_needed_to_meet_limit: Int,
                              start_time_for_campaign_for_the_day: DateTime
                            )(using Logger: SRLogger): Try[SenderRotationStats] = {
    campaignService.getSenderRotationStats(
      campaignId = campaignId,
      teamId = teamId,
      least_amount_of_prospects_needed_to_meet_limit = least_amount_of_prospects_needed_to_meet_limit,
      start_time_for_campaign_for_the_day = start_time_for_campaign_for_the_day
    )
  }


  def fetchProspectsV3MultichannelWithEmailOptionalCheck(
                                                          channelType: ChannelType,
                                                          allowedProspectTimezones: List[String],
                                                          prospectIdGreaterThan: Option[Long] = None, // for first page of results
                                                          campaignId: Long,
                                                          teamId: TeamId,
                                                          limit: Int,
                                                          channelRelevantStepIdAndDelay: Vector[SchedulerMapStepIdAndDelay],
                                                          newProspectsInCampaign: Boolean,
                                                          firstStepIsMagicContent: Boolean,
                                                          sendOnlyToProspectsWhoWereSentInCurrentCycle: Option[DateTime],
                                                          useModifiedQueryForDripCampaign: Boolean,
                                                          campaign_email_setting: Option[CampaignEmailSettingForScheduler],
                                                          org_id: OrgId,
//                                                          emailNotCompulsoryEnabled: Boolean,
                                                          head_step_id: String

                                                        )(using Logger: SRLogger): Try[List[ProspectDataForChannelScheduling]] = {

    val enable_magic_column = accountService.findOwnerAccountByOrgId(orgId = org_id.id)
      .toOption
      .flatMap(_.org.org_metadata.enable_magic_column)
      .getOrElse(false)

    if (!useModifiedQueryForDripCampaign) {

      campaignProspectDAO.fetchProspectsV3Multichannel(
        channelType = channelType,
        prospectIdGreaterThan = prospectIdGreaterThan,
        allowedProspectTimezones = allowedProspectTimezones,
        campaignId = campaignId,
        teamId = teamId,
        channelRelevantStepIdAndDelay = channelRelevantStepIdAndDelay,

        enable_magic_column = enable_magic_column,

        limit = limit,
        newProspectsInCampaign = newProspectsInCampaign,
        firstStepIsMagicContent = firstStepIsMagicContent,

        sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,
        campaign_email_setting = campaign_email_setting,
        orgId = org_id,
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled,
        useModifiedQueryForDripCampaign = useModifiedQueryForDripCampaign,
        log_query = getCampaignsToLogDripFor().map(_.id).contains(campaignId)

      )
    } else {
      fetchProspectsV3MultichannelNewForDrip(
        channelType = channelType,
        allowedProspectTimezones = allowedProspectTimezones,
        prospectIdGreaterThan = prospectIdGreaterThan,
        campaignId = campaignId,
        teamId = teamId,
        limit = limit,
        channelRelevantStepIdAndDelay = channelRelevantStepIdAndDelay,
        newProspectsInCampaign = newProspectsInCampaign,
        firstStepIsMagicContent = firstStepIsMagicContent,
        useModifiedQueryForDripCampaign = useModifiedQueryForDripCampaign,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,
        campaign_email_setting = campaign_email_setting,
        orgId = org_id,
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled,
        head_step_id = head_step_id

      )
    }
      .map { prospectDataForChannelScheduling =>


        val prospectObjects: List[ProspectObject] = channelType match {
          case ChannelType.EmailChannel =>
            val prospectsTry = checkListOfProspectAndFilterOutProspectsThatDontHaveDomain(
              prospectsForScheduling = prospectDataForChannelScheduling
            )
              .map { validAfterCheckingForESPData =>

                  validAfterCheckingForESPData.map(_.prospect)
              }

            prospectsTry match {
              case Success(value) => value
              case Failure(exception) =>
                Logger.fatal(s"checkListOfProspectAndFilterOutProspectsThatDontHaveDomain failed", exception)
                prospectDataForChannelScheduling.map(_.prospect)
            }

          case ChannelType.LinkedinChannel |
               ChannelType.WhatsappChannel |
               ChannelType.SmsChannel |
               ChannelType.CallChannel |
               ChannelType.IndependentChannel |
               ChannelType.GeneralChannel => prospectDataForChannelScheduling.map(_.prospect)
        }

        val validProspectObjectIds: List[Long] = prospectObjects.map(_.id)
        prospectDataForChannelScheduling.filter(pfs => validProspectObjectIds.contains(pfs.prospect.id))
      }
  }

  def fetchProspectsV3MultichannelNewForDrip(
                                              channelType: ChannelType,
                                              allowedProspectTimezones: List[String],
                                              prospectIdGreaterThan: Option[Long] = None, // for first page of results
                                              campaignId: Long,
                                              teamId: TeamId,
                                              limit: Int,
                                              channelRelevantStepIdAndDelay: Vector[SchedulerMapStepIdAndDelay],
                                              newProspectsInCampaign: Boolean,
                                              firstStepIsMagicContent: Boolean,
                                              useModifiedQueryForDripCampaign: Boolean,
                                              sendOnlyToProspectsWhoWereSentInCurrentCycle: Option[DateTime],
                                              campaign_email_setting: Option[CampaignEmailSettingForScheduler],
                                              orgId: OrgId,
//                                              emailNotCompulsoryEnabled: Boolean,
                                              head_step_id: String
                                            )(using Logger: SRLogger): Try[List[ProspectDataForChannelScheduling]] = {

    val totalFollowupSteps = channelRelevantStepIdAndDelay.size

    val enable_magic_column = accountService.findOwnerAccountByOrgId(orgId = orgId.id)
      .toOption
      .flatMap(_.org.org_metadata.enable_magic_column)
      .getOrElse(false)

    campaignProspectDAO.fetchProspectsV3Multichannel(
      channelType = channelType,
      allowedProspectTimezones = allowedProspectTimezones,
      prospectIdGreaterThan = prospectIdGreaterThan,
      campaignId = campaignId,
      teamId = teamId,
      limit = limit,
      channelRelevantStepIdAndDelay = channelRelevantStepIdAndDelay,
      newProspectsInCampaign = newProspectsInCampaign,
      firstStepIsMagicContent = firstStepIsMagicContent,
      useModifiedQueryForDripCampaign = useModifiedQueryForDripCampaign,
      enable_magic_column = enable_magic_column,
      sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,
      campaign_email_setting = campaign_email_setting,
      orgId = orgId,
//      emailNotCompulsoryEnabled = emailNotCompulsoryEnabled
      log_query = getCampaignsToLogDripFor().map(_.id).contains(campaignId)

    ) match {
      case Failure(exception) => Failure(exception)
      case Success(prospects) =>
        if (allowedProspectTimezones.isEmpty) {
          Success(List())
        } else if (!useModifiedQueryForDripCampaign && !newProspectsInCampaign && (totalFollowupSteps == 0)) {
          Logger.shouldNeverHappen("followup steps not found still checking for followup prospects")
          Success(List())

        } else if (limit <= 0) {
          Logger.shouldNeverHappen("Limit is less than zero")

          Success(List())

        } else {

          //Not ignoring now so that we can pass it up to condition check
          //          val ignoredProspects: List[(ProspectDataForChannelScheduling, DateTime)] = prospects.flatMap { prospect =>
          //            val delayTillNextStep: Option[Int] = prospect
          //              .current_step_id
          //              .flatMap(id =>
          //                channelRelevantStepIdAndDelay
          //                  .find(_.currentStepId == id)
          //                  .map(_.delayTillNextStep)
          //              )
          //
          //            val last_contacted_at = prospect.prospect.last_contacted_at
          //            if(prospect.prospect.last_contacted_at.isDefined &&
          //              delayTillNextStep.isDefined) {
          //              val send_allowed_after = last_contacted_at
          //                .get
          //                .plusMinutes(delayTillNextStep.get)
          //
          //              if(send_allowed_after.isAfter(DateTime.now())) { //not allowed to send right now so ignore
          //                List((
          //                  prospect,
          //                  last_contacted_at.get.plusSeconds(delayTillNextStep.get) //scheduler after time
          //                )
          //                )
          //
          //              } else List()
          //            } else {
          //              List()
          //            }
          //          }
          //
          //          addIgnoreLogAndUpdateNextCheckForSchedulingAt(
          //            prospects = ignoredProspects.map{p =>
          //              (p._1, p._2, StepDelayNotMet)
          //            },
          //            campaignId = CampaignId(campaignId),
          //            head_step_id = head_step_id,
          //            teamId = teamId
          //          )
          Success(prospects
            //            .filterNot(p => ignoredProspects.map(_._1.prospect.id).contains(p.prospect.id))
          )
        }
    }
  }

  def addIgnoreLogAndUpdateNextCheckForSchedulingAt(
                                                     prospects: List[(ProspectDataForChannelScheduling, DateTime, RejectionReasonForCampaignProspectStepSchedule)],
                                                     campaignId: CampaignId,
                                                     head_step_id: String,
                                                     teamId: TeamId,
                                                   )(using Logger: SRLogger): Unit = {
    val result = if (prospects.nonEmpty) {
      for {
        addingInsertLog: List[Long] <- campaignProspectStepScheduleLogsDAO.insertForIgnore(
          data = prospects.map(data => {
            val (p, _, rejection_reason) = data
            CampaignProspectStepScheduleLogDataForIgnore(
              team_id = TeamId(p.prospect.team_id),
              c_id = campaignId,
              p_id = ProspectId(p.prospect.id),
              step_id = p.current_step_id.map(_.toString).getOrElse(head_step_id),
              step_type = None,
              request_log_id = Logger.logRequestId,
              rejection_reason = rejection_reason
            )
          }
          )
        )
        update: List[Int] <- Helpers.listTryToTryList {
          prospects.map { data =>
            val (p, nextScheduleTime, _) = data
            campaignProspectDAO.updateNextCheckForSchedulingAt(
              data = List(
                UpdateNextScheduleAtData(
                  campaignId = campaignId,
                  prospectId = ProspectId(p.prospect.id),
                  teamId = teamId,
                  nexToBeCheckedAt = nextScheduleTime
                )
              )
            )
          }
        }
      } yield addingInsertLog

    } else Success(List())
    result match {
      case Failure(exception) => Logger.fatal(s"Failed:: campaignProspectStepScheduleLogsDAO.insertForIgnore", exception)
      case Success(value) => //Do nothing
    }

  }

  def checkListOfProspectAndFilterOutProspectsThatDontHaveDomain(
                                                                  prospectsForScheduling: List[ProspectDataForChannelScheduling]
                                                                )(using Logger: SRLogger): Try[List[ProspectDataForChannelScheduling]] = {

    val prospectsWithEmails: List[ProspectDataForChannelScheduling] = prospectsForScheduling
      .filter(_.prospect.email.isDefined)

    val domains: Set[String] = prospectsWithEmails
      .map { p =>
        val (_, domain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(email = p.prospect.email.get)
        domain
      }.toSet

    domainPublicDNSDAO.getBatch(domains = domains) map { domainsWithESP =>
      val validDomains: List[String] = domainsWithESP.map(_.domain)

      val prospectsWithoutDomainESPRecord: List[ProspectDataForChannelScheduling] = prospectsWithEmails.filter { p =>
        val (_, domain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(email = p.prospect.email.get)
        !validDomains.contains(domain)
      }


      val validProspects: List[ProspectDataForChannelScheduling] = prospectsForScheduling.filterNot(prospectsWithoutDomainESPRecord.contains)


      val domainsForPushingToQueue: List[String] = prospectsWithoutDomainESPRecord.map { p =>
        val (_, domain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(email = p.prospect.email.get)
        domain
      }

      domainsForPushingToQueue.map(mqDomainServiceProviderDNSService.publish)

      validProspects
    }
  }

  def updateInternalNotesForCampaignProspect(
                                              data: Seq[CampaignProspectInternalNote]
                                            )(using Logger: SRLogger): Int = {

    if (data.isEmpty) {

      0

    } else {

      val saved_notes = data.map(uE => {

        campaignProspectDAO.updateInternalNotesForCampaignProspect(
          team_id = uE.team_id,
          campaign_id = uE.campaign_id,
          prospect_id = uE.prospect_id,
          internal_notes = uE.internal_note
        ) match {

          case Failure(err) =>

            Logger.fatal(s"updateInternalNotesForCampaignProspect :: Error while updating internal notes in campaigns prospects :: campaign_id : ${uE.campaign_id}, prospect_id: ${uE.prospect_id} team_id: ${uE.team_id}", err)
            0

          case Success(None) =>

            Logger.shouldNeverHappen(s"updateInternalNotesForCampaignProspect :: None received after updating internal notes in campaigns prospects :: campaign_id : ${uE.campaign_id}, prospect_id: ${uE.prospect_id} team_id: ${uE.team_id} ")
            0


          case Success(Some(note)) =>

            Logger.info(s"updateInternalNotesForCampaignProspect :: note : ${note} Internal notes added successfully!! team_id ${uE.team_id} campaign_id: ${uE.campaign_id} prospect_id: ${uE.prospect_id} ")
            1

        }

      })

      Logger.info(s"updateInternalNotesForCampaignProspect :: total internal_notes saved ${saved_notes.sum} campaign_id: ${data.head.campaign_id} prospect_id: ${data.head.prospect_id} ")

      saved_notes.sum

    }
  }


  def moveToAnotherCampaign(
                             prospect_id: ProspectId,
                             orgId: OrgId,
                             doerAccountId: Long,
                             doerAccountName: String,
                             accountId: Long,
                             team_id: TeamId,
                             current_campaign_id: CampaignId,
                             move_to_campaign_id: CampaignId,
                             move_to_campaign_name: String,
                             ignoreProspectsInOtherCampaigns: IgnoreProspectsInOtherCampaigns
                           )(using Logger: SRLogger): Try[CPAssignResult] = {

    for {

      account: Account <- accountService.find(
        id = doerAccountId
      )

      permittedAccountIds: Seq[Long] <- Try {
        PermissionMethods.getPermittedAccountIdsForAccountAndPermission(
          loggedinAccount = account,
          actingTeamId = team_id.id,
          actingAccountId = doerAccountId,
          version = "v2",
          Logger = Logger
        )(permission = PermType.EDIT_PROSPECTS)
      }

      /* Mark  */
      marK_prospect_completed: Option[Long] <- campaignProspectDAO.markCampaignCompletedForProspectDrip(
        prospect_id = prospect_id,
        campaign_id = current_campaign_id,
        team_id = team_id
      )

      /* ProspectCategory Team */
      fetch_campaign_settings: CampaignBasicInfo <- campaignService.getCampaignBasicInfo(
        campaginId = move_to_campaign_id.id,
        teamId = team_id.id
      ) match {
        case Failure(exception) =>

          Logger.error(s"moveToAnotherCampaign : Error  campaign_id : ${move_to_campaign_id.id} team_id : ${team_id.id}", exception)
          Failure(exception)

        case Success(None) =>

          Logger.error(s"moveToAnotherCampaign :: Campaign Basic info not found. campaign_id : ${move_to_campaign_id.id} team_id : ${team_id.id}")
          Failure(new Exception("moveToAnotherCampaign :: None found for fetch campaign settings"))

        case Success(Some(campaign_basic_info)) =>

          Success(campaign_basic_info)

      }

      /* Mark the prospect completed in current campaign */
      assign_prospect_to_another_campaign: CPAssignResult <- assignProspectsToCampaign(
        orgId = orgId.id,
        permittedAccountIds = permittedAccountIds, // Fix This
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = team_id.id,
        campaignId = move_to_campaign_id.id,
        campaignName = move_to_campaign_name,
        campaignSettings = fetch_campaign_settings.settings,
        prospectIds = List(prospect_id.id), // Fix This
        ignoreProspectsInOtherCampaigns = ignoreProspectsInOtherCampaigns,
        Logger = Logger
      )


    } yield {

      assign_prospect_to_another_campaign

    }

  }

  def getCampaignsToLogDripFor()(using Logger: SRLogger): List[CampaignId] = {
    dripLogJedisDAO.getCampaignsToLogDripFor()
  }

  def getPendingApprovalCountForCampaign(
                                          campaignId: CampaignId,
                                          teamId: TeamId,
                                          campaignStepType: CampaignStepType
                                        )(using Logger: SRLogger): Try[Int] = {

    campaignProspectDAO.getPendingApprovalOrQueuedCountForCampaign(
      campaignId = campaignId,
      campaignStepType = campaignStepType,
      teamId = teamId
    )

  }
}

/**
 * Date: 12/04/2022
 * Creating separate class for assignProspects to Separate the assign DAO and the assignProspects service
 */
class CampaignProspectAssign(
                              campaignProspectService: CampaignProspectService,
                              campaignCacheService: CampaignCacheService
                            ) {
  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  def assignProspects(
                       doer: Account,
                       accountId: Long,
                       teamId: Long,
                       ignoreProspectsInOtherCampaigns: IgnoreProspectsInOtherCampaigns,
                       permittedAccountIds: Seq[Long],
                       campaignId: Long,
                       campaignName: String,
                       campaignSettings: CampaignSettings,
                       prospectIds: List[Long],
                       Logger: SRLogger
                     ): Either[AssignProspectsError, AssignProspectsResponse] = {

    given srLogger: SRLogger = Logger // fixme given

    // FORCEASSIGNISSUE
    val tryAssign = campaignProspectService.assignProspectsToCampaign(
      orgId = doer.org.id,
      accountId = accountId,
      teamId = teamId,
      doerAccountId = doer.internal_id,
      doerAccountName = Helpers.getAccountName(doer),
      permittedAccountIds = permittedAccountIds,
      campaignId = campaignId,
      campaignName = campaignName,
      prospectIds = prospectIds,
      campaignSettings = campaignSettings,
      ignoreProspectsInOtherCampaigns = ignoreProspectsInOtherCampaigns,
      Logger = Logger

      /*
      moveToNewCampaign = data.force_assign.getOrElse(false),
      allow_assigning_prospects_to_multiple_campaigns = doer.teams
        .find(t => t.team_id == ta.team_id)
        .get
        .allow_assigning_prospects_to_multiple_campaigns
      */
    )

    tryAssign match {

      case Failure(e) =>

        e.getMessage match {

          case msg if msg.contains("campaigns_prospects_pkey") => Left(AssignProspectsError.ProspectAlreadyAssigned("Prospect is already assigned to the given campaign"))

          case msg if msg.contains( "campaigns_prospects_campaign_id_fkey") => Left(AssignProspectsError.CampaignNotFound("Campaign with given id not found"))

          case msg if msg.contains("campaigns_prospects_prospect_id_fkey") => Left(AssignProspectsError.ProspectNotFound("Prospect with given id not found"))

          case msg => Left(AssignProspectsError.ErrorWhileAssigningProspect(s"Error while assigning prospects: $msg"))
          //Res.BadRequestError("Error while assigning prospects: " + msg)
        }

      case Success(assignResult) =>
        val assignedProspectIds = assignResult.newlyAssignedProspectIds
        val prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign = assignResult.prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign
        val prospectIdsIgnoredBecauseInOtherCampaigns = assignResult.prospectIdsIgnoredBecauseInOtherCampaigns

        val responseMsg = if (prospectIds.isEmpty) {

          "Please select a few prospects to assign"

        } else if (assignedProspectIds.isEmpty) {

          ignoreProspectsInOtherCampaigns match {
            case IgnoreProspectsInOtherCampaigns.IgnoreProspectsAddedInOtherCampaigns =>
              "No prospect assigned. Selected prospects are either already assigned to the campaign, or are currently added in other campaigns"

            case IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns =>
              "No prospect assigned. Selected prospects are either already assigned to the campaign, or are currently active in other campaigns"

            case IgnoreProspectsInOtherCampaigns.DoNotIgnore =>
              "No prospect assigned. Selected prospects are already assigned to the campaign"

          }

        } else {

          campaignCacheService.resetCampaignStats(campaignId = campaignId, teamId = teamId)

          ignoreProspectsInOtherCampaigns match {
            case IgnoreProspectsInOtherCampaigns.IgnoreProspectsAddedInOtherCampaigns =>
              s"${assignedProspectIds.size} prospects assigned to campaign '${campaignName}', ${prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign.length} were already in this Campaign. ${prospectIdsIgnoredBecauseInOtherCampaigns.length} were added in other campaigns."

            case IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns =>
              s"${assignedProspectIds.size} prospects assigned to campaign '${campaignName}', ${prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign.length} were already in this Campaign. ${prospectIdsIgnoredBecauseInOtherCampaigns.length} were active in other campaigns."

            case IgnoreProspectsInOtherCampaigns.DoNotIgnore =>
              s"${assignedProspectIds.size} prospects assigned to campaign '${campaignName}', ${prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign.length} were already in this Campaign."

          }

        }

        // NOTE: since we are running the scheduler every 15 minutes, no need to use the MQCampaign queue
        //              MQCampaign.publish(MQCampaignMessage(campaignId))
        Right(AssignProspectsResponse(responseMsg, assignedProspectIds.length, assignedProspectIds, campaignId))

    }

  }

  def assignProspectsV3(
                         doer: Account,
                         accountId: Long,
                         teamId: Long,
                         ignoreProspectsInOtherCampaigns: IgnoreProspectsInOtherCampaigns,
                         permittedAccountIds: Seq[Long],
                         campaignId: Int,
                         campaignName: String,
                         campaignSettings: CampaignSettings,
                         prospectIds: List[Long],
                         accountOwnedProspects: List[OwnedProspectsForAssigning],
                         Logger: SRLogger
                       ): Either[AssignProspectsError, AssignProspectsResponseV3] = {

    // FORCEASSIGNISSUE
    val tryAssign = campaignProspectService.assignV3(
      orgId = doer.org.id,
      accountId = accountId,
      teamId = teamId,
      doerAccountId = doer.internal_id,
      doerAccountName = Helpers.getAccountName(doer),
      permittedAccountIds = permittedAccountIds,
      campaignId = campaignId,
      campaignName = campaignName,
      campaignSettings = campaignSettings,
      prospectIds = prospectIds,
      ignoreProspectsInOtherCampaigns = ignoreProspectsInOtherCampaigns,
      accountOwnedProspects = accountOwnedProspects,
      Logger = Logger
    )

    tryAssign match {

      case Failure(e) =>

        e.getMessage match {

          case msg if msg.contains("campaigns_prospects_pkey") => Left(AssignProspectsError.ProspectAlreadyAssigned("Prospect is already assigned to the given campaign"))

          case msg if msg.contains("campaigns_prospects_campaign_id_fkey") => Left(AssignProspectsError.CampaignNotFound("Campaign with given id not found"))

          case msg if msg.contains("campaigns_prospects_prospect_id_fkey") => Left(AssignProspectsError.ProspectNotFound("Prospect with given id not found"))

          case msg => Left(AssignProspectsError.ErrorWhileAssigningProspect(s"Error while assigning prospects: $msg"))
          //Res.BadRequestError("Error while assigning prospects: " + msg)
        }

      case Success(assignResult) =>
        val assignedProspectIds = assignResult.newlyAssignedProspectIds
        val prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign = assignResult.prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign
        val prospectIdsIgnoredBecauseInOtherCampaigns = assignResult.prospectIdsIgnoredBecauseInOtherCampaigns

        Logger.info(s"prospect ids ignored because in other campaigns :: ${prospectIdsIgnoredBecauseInOtherCampaigns} for campaign id:: ${campaignId} teamId:: ${teamId}")

        // NOTE: since we are running the scheduler every 15 minutes, no need to use the MQCampaign queue
        //              MQCampaign.publish(MQCampaignMessage(campaignId))
        Right(AssignProspectsResponseV3(
          assignedProspectIdsLength = assignedProspectIds.length,
          totalAssignedProspectIds = assignedProspectIds ++ prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign,
          campaignId = campaignId))

    }

  }

}
