package api.campaigns.services

import api.accounts.{ReplyHandling, TeamId}
import api.accounts.models.AccountId
import api.campaigns.{CPCompleted, CampaignProspectDAO}
import api.emails.models.DeletionReason
import api.emails.services.SelectAndPublishForDeletionService
import api.prospects.dao_service.{ProspectDAOServiceV2, ProspectEmailsDAOService}
import utils.SRLogger
import utils.email.models.DeleteEmailsScheduledType

import scala.util.{Failure, Success, Try}

class CampaignProspectDAOService(
                                  campaignProspectDAO: CampaignProspectDAO,
                                  prospectDAOServiceV2: ProspectDAOServiceV2,
                                  selectAndPublishForDeletionService: SelectAndPublishForDeletionService,
                                  prospectEmailsDAOService: ProspectEmailsDAOService
                                ) {

  // MULTICAMPAIGN DONE
  // TEAMEDITION
  // This gets called from "Mark as completed" action in frontend
  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  // use case : ProspectController.markAsCompletedZapier POST    /api/:v/prospects/change_pause_status
  // Use case : _handleEventAction SRTriggerActionType.COMPLETE_CAMPAIGN_FOR_PROSPECT HandleActivityTriggerEventService.__markProspectAsCompleted
  def pauseStatusChangedByAdmin(
                                 permittedAccountIds: Seq[Long],
                                 statusChangedByAccountId: AccountId,
                                 teamId: Long,
                                 prospectEmails: Seq[String], // if prospectEmails is there, check by that and ignore prospectIds
                                 prospectIds: Seq[Long],
                                 newPauseStatus: Boolean,
                                 campaignIds: Seq[Long], // optional campaign_ids for which to pause prospects
                                 Logger: SRLogger
                               ): Try[Seq[CPCompleted]] = {

    given logger: SRLogger = Logger
    val distinctCampaignIds = campaignIds.distinct

    if (prospectEmails.isEmpty && prospectIds.isEmpty) {
      Success(Seq())
    }
    else if (prospectEmails.nonEmpty && prospectIds.nonEmpty) {
      Failure(new Exception("send either prospectIds or prospectEmails"))
    }
    else Try {

      Logger.debug("pauseStatusChangedByAdmin accountId: " + statusChangedByAccountId.id + " :: teamId: " + teamId + " newPauseStatus: " + newPauseStatus + " campaignIds: " + distinctCampaignIds + " prospectEmails: " + prospectEmails.mkString(",") + " prospectIds: " + prospectIds.mkString(","))

      // prospects must belong to account
      val accountOwnedProspects = if (prospectEmails.nonEmpty) {

        prospectEmailsDAOService.checkIfUserHasPermissionForProspectsByEmail(
            prospectEmails = prospectEmails,
            permittedAccountIds = permittedAccountIds,
            teamId = teamId,
            Logger = Logger
          )
          .get

      } else {

        prospectDAOServiceV2.checkIfUserHasPermissionForProspects(
            prospectsIds = prospectIds,
            permittedAccountIds = permittedAccountIds,
            teamId = teamId
          )
          .get

      }

      if (prospectEmails.nonEmpty && prospectEmails.length != accountOwnedProspects.length) {

        throw new Exception("Invalid prospect emails")

      } else if (prospectIds.nonEmpty && prospectIds.length != accountOwnedProspects.length) {

        throw new Exception("Invalid prospect ids")

      } else {

        campaignProspectDAO.pauseUnpauseCampaignProspect(
          campaignIds = distinctCampaignIds,
          accountOwnedProspects = accountOwnedProspects,
          newPauseStatus = newPauseStatus,
          Logger = Logger
        ) match {
          case Success(updated) =>
            if(newPauseStatus && distinctCampaignIds.length == 1) {
              selectAndPublishForDeletionService.selectAndPublishForDeletion(
                deletion_reason = DeletionReason.PublishCompletedProspects,
                deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByProspectId(
                  prospectIds = prospectIds,
                  campaignId = distinctCampaignIds.head,
                  prospectAccountIds = None,
                  replyHandling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
                  teamId = TeamId(teamId)
                )
              )
            } else if(distinctCampaignIds.length > 1) {
              Logger.shouldNeverHappen("pauseStatusChangedByAdmin :: More than one campaign is not supported")
            }

            updated
          case Failure(exception) => throw exception
        }

      }
    }
  }
}
