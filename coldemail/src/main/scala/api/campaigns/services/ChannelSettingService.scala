package api.campaigns.services

import api.accounts.TeamId
import api.call.DAO.CallDAO
import api.call.models.{CallAccountSettings, PhoneNumberUuid}
import api.campaigns.{CampaignDAO, CampaignEmailSettings}
import api.campaigns.dao.CampaignEmailSettingsDAO
import api.campaigns.models.{CampaignEmailSettingsId, ChannelSettingSenderDetails}
import api.emails.{EmailSetting, EmailSettingDAO}
import api.general.{GeneralSetting, GeneralSettingDAO, GeneralSettingUuid}
import api.linkedin.LinkedinSettingDAO
import api.linkedin.models.{LinkedinAccountSettings, LinkedinSettingUuid}
import api.sms.{SmsSettingDAO, SmsSettingUuid}
import api.sms.models.SmsSettings
import api.whatsapp.{WhatsappSettingDAO, WhatsappSettingUuid}
import api.whatsapp.models.WhatsappAccountSettings
import io.smartreach.esp.api.emails.EmailSettingId
import play.api.libs.json.{Js<PERSON><PERSON>, <PERSON><PERSON>, Writes}
import sr_scheduler.models.ChannelType
import utils.{Help<PERSON>, SRLogger}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

case class CampaignEmailSettingsPublicApi(
                                           id: CampaignEmailSettingsId,
                                           teamId: TeamId,
                                           sender_email_setting: EmailSetting,
                                           receiver_email_setting: EmailSetting
                                         )


case class ChannelSettings(
                            generalSettings: List[GeneralSetting],
                            campaignEmailSettings: List[CampaignEmailSettingsPublicApi],
                            callSettings: List[CallAccountSettings],
                            linkedinSettings: List[LinkedinAccountSettings],
                            smsSettings: List[SmsSettings],
                            whatsappSettings: List[WhatsappAccountSettings]
                          )
sealed trait GetChannelSettingsError

object GetChannelSettingsError {

  case class DbFailure(err: Throwable) extends GetChannelSettingsError

}

class ChannelSettingService(
                             campaignDAO: CampaignDAO,
                             campaignEmailSettingsDAO: CampaignEmailSettingsDAO,
                             linkedinSettingDAO: LinkedinSettingDAO,
                             callDAO: CallDAO,
                             smsSettingDAO: SmsSettingDAO,
                             whatsappSettingDAO: WhatsappSettingDAO,
                             generalSettingDAO: GeneralSettingDAO,
                             emailSettingDAO: EmailSettingDAO
                           ) {


  def getChannelSettingSenderData(
                                   team_id: TeamId,
                                   campaign_id: CampaignId
                                 )(implicit ec: ExecutionContext, logger: SRLogger): Future[List[ChannelSettingSenderDetails]] = {

    for {
      channel_setting_data: List[ChannelSettingData] <- getChannelAccountSetting(
        team_id = team_id.id,
        campaign_id = campaign_id.id
      )

      setting_sender_data: List[Option[ChannelSettingSenderDetails]] <- Future.sequence(channel_setting_data.map(setting_data => {
        setting_data.channel_type match {
          case ChannelType.EmailChannel =>
            logger.fatal(s"Getting Email channel for channel_setting_data request $setting_data")
            Future(None)

          case ChannelType.GeneralChannel =>
            Future(None) // general channel is added to the campaign channel setting, but it will not have sender details
          case ChannelType.LinkedinChannel |
               ChannelType.WhatsappChannel |
               ChannelType.SmsChannel|
               ChannelType.IndependentChannel |
               ChannelType.CallChannel =>
            campaignDAO.getChannelSettingSenderDetails(
              channel_type = setting_data.channel_type,
              team_id = setting_data.team_id,
              campaign_id = setting_data.campaign_id,
              channel_setting_uuid = setting_data.channel_setting_uuid
            ) map {
              case None =>
                logger.fatal(s"IMPOSSIBLE CASE: setting data given but no setting sender details - $setting_data")
                None
              case Some(value) =>
                Some(value)
            }
        }
      }))
    } yield {
      setting_sender_data.flatten
    }
  }

  def _generalSetting(
                       generalSettingUuid: List[GeneralSettingUuid],
                       teamId: TeamId
                     )(using logger: SRLogger): Try[List[GeneralSetting]] = {
    val gs = generalSettingUuid.map {
      generalSettingUuid =>
        generalSettingDAO.getGeneralSettingByUuid(generalSettingUuid = generalSettingUuid, teamId = teamId) match {

          case Failure(err) =>
            logger.fatal(msg = s"[ChannelSettingService.getChannelSettingsV3 generalSettingDAO.getGeneralSettingByUuid] uuid::$generalSettingUuid team_id::$teamId")
            Failure(err)

          case Success(None) =>
            logger.fatal(msg = s"[ChannelSettingService.getChannelSettingsV3] generalSettingDAO.getGeneralSettingByUuid returned None for uuid::$generalSettingUuid team_id::$teamId")
            Failure(throw new Throwable("invalid generalSettingUuid"))

          case Success(Some(data)) => Success(data)

        }
    }

    Helpers.listTryToTryList(gs)
  }

  def _campaignEmailSettings(
                              campaignId: CampaignId,
                              teamId: TeamId
                            )(using logger: SRLogger): Try[List[CampaignEmailSettingsPublicApi]] = {

    campaignEmailSettingsDAO.getCampaignSenderEmailsWithSession(campaignId = campaignId, teamId = teamId) match {
      case Failure(err) =>
        logger.fatal(s"[ChannelSettingService.getChannelSettingsV3 campaignEmailSettingsDAO.getCampaignSenderEmailsWithSession] campaign_id::$campaignId team_id::$teamId")
        Failure(err)

      case Success(ces: List[CampaignEmailSettings]) =>

        val emailSettings: List[Try[CampaignEmailSettingsPublicApi]] = ces.map(campaignEmailSetting => {

          val sender_email_setting_id: EmailSettingId = campaignEmailSetting.sender_email_setting_id
          val sender_email_setting: Option[EmailSetting] = emailSettingDAO.find(id = sender_email_setting_id, teamId = teamId)

          val receiver_email_setting_id: EmailSettingId = campaignEmailSetting.receiver_email_setting_id
          val receiver_email_setting: Option[EmailSetting] = emailSettingDAO.find(id = receiver_email_setting_id, teamId = teamId)

          if (sender_email_setting.isEmpty && receiver_email_setting.isEmpty) {
            logger.fatal(s"[ChannelSettingService.getChannelSettingsV3] None returned for sender_email_setting::${sender_email_setting_id} and receiver_email_setting_id::$receiver_email_setting_id")
            Failure(throw new Throwable("invalid sender_email_setting_id & receiver_email_setting_id"))
          } else if (sender_email_setting.isEmpty) {
            logger.fatal(s"[ChannelSettingService.getChannelSettingsV3] None returned for sender_email_setting::${sender_email_setting_id}")
            Failure(throw new Throwable("invalid sender_email_setting_id"))
          } else if (receiver_email_setting.isEmpty) {
            logger.fatal(s"[ChannelSettingService.getChannelSettingsV3] None returned for receiver_email_setting::${receiver_email_setting_id}")
            Failure(throw new Throwable("invalid receiver_email_setting_id"))
          } else {

            Success(
              CampaignEmailSettingsPublicApi(
                id = campaignEmailSetting.id,
                teamId = teamId,
                sender_email_setting = sender_email_setting.get,
                receiver_email_setting = receiver_email_setting.get
              )
            )

          }
        })

        Helpers.listTryToTryList(emailSettings)
    }

  }

  def _callSetting(
                    callSettingUuids: List[PhoneNumberUuid],
                    teamId: TeamId
                  )(using logger: SRLogger): Try[List[CallAccountSettings]] = {

    val ces: List[Try[CallAccountSettings]] = callSettingUuids.map {
      callSettingUuid =>
        callDAO.getCallSettingByUuid(uuid = callSettingUuid, teamId = teamId) match {
          case Failure(err) =>
            logger.fatal(msg = s"[ChannelSettingService.getChannelSettingsV3 callDAO.getCallSettingByUuid] uuid::$callSettingUuid team_id::$teamId", err = err)
            Failure(err)
          case Success(None) =>
            logger.fatal(s"[ChannelSettingService.getChannelSettingsV3] None returned for callDAO.getCallSettingByUuid uuid::$callSettingUuid team_id::$teamId")
            Failure(throw new Exception("invalid callSettingUuid"))

          case Success(Some(callAccountSettings: CallAccountSettings)) => Success(callAccountSettings)
        }
    }

    Helpers.listTryToTryList(ces)

  }

  def _linkedinSetting(
                        linkedinSettingUuids: List[LinkedinSettingUuid],
                        teamId: TeamId
                      )(using logger: SRLogger): Try[List[LinkedinAccountSettings]] = {

    val ls: List[Try[LinkedinAccountSettings]] = linkedinSettingUuids.map {
      linkedinSettingUuid =>
        linkedinSettingDAO.findLinkedinAccountByIdOrUuid(id = None, uuid = Some(linkedinSettingUuid.uuid), teamId = teamId) match {
          case Failure(err) =>
            logger.fatal(msg = s"[ChannelSettingService.getChannelSettingsV3 linkedinSettingDAO.findLinkedinAccountByIdOrUuid] uuid::$linkedinSettingUuid team_id::$teamId", err = err)
            Failure(err)
          case Success(None) =>
            logger.fatal(s"[ChannelSettingService.getChannelSettingsV3] None returned for linkedinSettingDAO.findLinkedinAccountByIdOrUuid uuid::$linkedinSettingUuid team_id::$teamId")
            Failure(throw new Throwable("invalid linkedinSettingUuid"))

          case Success(Some(linkedinAccountSettings: LinkedinAccountSettings)) => Success(linkedinAccountSettings)
        }
    }

    Helpers.listTryToTryList(ls)

  }

  def _smsSetting(
                   smsSettingUuids: List[SmsSettingUuid],
                   teamId: TeamId
                 )(using logger: SRLogger): Try[List[SmsSettings]] = {

    val ss: List[Try[SmsSettings]] = smsSettingUuids.map(
      smsSettingUuid =>
        smsSettingDAO.findSmsSettingsByUuid(uuid = smsSettingUuid.uuid, teamId = teamId.id) match {

          case Failure(err) =>
            logger.fatal(msg = s"[ChannelSettingService.getChannelSettingsV3 smsSettingDAO.findSmsSettingsByUuid] uuid::$smsSettingUuid team_id::$teamId", err = err)
            Failure(err)

          case Success(None) =>
            logger.fatal(s"[ChannelSettingService.getChannelSettingsV3] None returned for smsSettingDAO.findSmsSettingsByUuid uuid::$smsSettingUuid team_id::$teamId")
            Failure(throw new Throwable("invalid smsSettingUuid"))

          case Success(Some(data: SmsSettings)) => Success(data)

        }
    )

    Helpers.listTryToTryList(ss)

  }

  def _whatsappSetting(
                        whatsappSettingUuids: List[WhatsappSettingUuid],
                        teamId: TeamId
                      )(using logger: SRLogger): Try[List[WhatsappAccountSettings]] = {

    val ws = whatsappSettingUuids.map {
      whatsappSettingUuid =>
        whatsappSettingDAO.findWhatsappAccountByUuid(uuid = whatsappSettingUuid.uuid, teamId = teamId.id) match {

          case Failure(err) =>
            logger.fatal(msg = s"[ChannelSettingService.getChannelSettingsV3 whatsappSettingDAO.findWhatsappAccountByUuid] uuid::$whatsappSettingUuid team_id::$teamId", err = err)
            Failure(err)

          case Success(None) =>
            logger.fatal(s"[ChannelSettingService.getChannelSettingsV3] None returned for whatsappSettingDAO.findWhatsappAccountByUuid uuid::$whatsappSettingUuid team_id::$teamId")
            Failure(throw new Throwable("invalid whatsappSettingUuid"))

          case Success(Some(data: WhatsappAccountSettings)) => Success(data)

        }
    }

    Helpers.listTryToTryList(ws)

  }

  def getChannelSettingsV3(
                            teamId: TeamId,
                            campaignId: CampaignId
                          )(implicit ec: ExecutionContext, logger: SRLogger): Future[Either[GetChannelSettingsError, ChannelSettings]] = {

    getChannelAccountSetting(
      team_id = teamId.id,
      campaign_id = campaignId.id
    ).map(channelSettingDataList => {

        // the length of channelSettingDataList can be max - 30(emails - 25 and 5 other settings), so iterating for each setting separately will not affect the runtime much
        val generalSettingUuid: List[GeneralSettingUuid] = channelSettingDataList.filter(cs => cs.channel_type == ChannelType.GeneralChannel).map(_.channel_setting_uuid.uuid).map(uuid => GeneralSettingUuid(uuid))
        val callSettingUuids: List[PhoneNumberUuid] = channelSettingDataList.filter(cs => cs.channel_type == ChannelType.CallChannel).map(_.channel_setting_uuid.uuid).map(uuid => PhoneNumberUuid(uuid))
        val linkedinSettingUuids: List[LinkedinSettingUuid] = channelSettingDataList.filter(cs => cs.channel_type == ChannelType.LinkedinChannel).map(_.channel_setting_uuid.uuid).map(uuid => LinkedinSettingUuid(uuid))
        val smsSettingUuids: List[SmsSettingUuid] = channelSettingDataList.filter(cs => cs.channel_type == ChannelType.SmsChannel).map(_.channel_setting_uuid.uuid).map(uuid => SmsSettingUuid(uuid))
        val whatsappSettingUuids: List[WhatsappSettingUuid] = channelSettingDataList.filter(cs => cs.channel_type == ChannelType.WhatsappChannel).map(_.channel_setting_uuid.uuid).map(uuid => WhatsappSettingUuid(uuid))

        val res: Try[ChannelSettings] = for {

          generalSetting: List[GeneralSetting] <- {

            _generalSetting(
              generalSettingUuid = generalSettingUuid,
              teamId = teamId
            )

          }

          campaignEmailSettings: List[CampaignEmailSettingsPublicApi] <- {

            _campaignEmailSettings(
              campaignId = campaignId,
              teamId = teamId
            )

          }

          callSetting: List[CallAccountSettings] <- {

            _callSetting(
              callSettingUuids = callSettingUuids,
              teamId = teamId
            )

          }

          linkedinSetting: List[LinkedinAccountSettings] <- {

            _linkedinSetting(
              linkedinSettingUuids = linkedinSettingUuids,
              teamId = teamId
            )

          }

          smsSetting: List[SmsSettings] <- {

            _smsSetting(
              smsSettingUuids = smsSettingUuids,
              teamId = teamId
            )

          }

          whatsappSetting: List[WhatsappAccountSettings] <- {

            _whatsappSetting(
              whatsappSettingUuids = whatsappSettingUuids,
              teamId = teamId
            )

          }

        } yield {

          ChannelSettings(
            generalSettings = generalSetting,
            campaignEmailSettings = campaignEmailSettings,
            callSettings = callSetting,
            linkedinSettings = linkedinSetting,
            smsSettings = smsSetting,
            whatsappSettings = whatsappSetting
          )

        }

        res match {

          case Failure(err) => Left(GetChannelSettingsError.DbFailure(err))

          case Success(channelSettings: ChannelSettings) => Right(channelSettings)

        }

      })
      .recover(err => {
        logger.fatal(msg = s"[ChannelSettingService.getChannelAccountSetting] error::$err")
        Left(GetChannelSettingsError.DbFailure(err))
      })
  }

  def getChannelAccountSetting(
                                team_id: Long,
                                campaign_id: Long
                              )(implicit ec: ExecutionContext, logger: SRLogger): Future[List[ChannelSettingData]] = {

    val teamId = TeamId(
      id = team_id
    )

    val campaignId = CampaignId(
      id = campaign_id
    )
    campaignDAO.getCampaignChannelSetupData(
      team_id = teamId,
      campaign_id = campaignId
    )
  }
}
