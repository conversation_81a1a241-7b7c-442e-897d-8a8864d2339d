package api.campaigns.services

import api.accounts.TeamId
import api.campaigns.dao.{CampaignSendingVolumeLogsDAO, CompletedTaskWithNonCompletedProspectData, DripCampaignHealthCheckDAO, DripCampaignMonitorData, DripCampaignMonitorDataWithStartTime}
import api.campaigns.models.{CampaignAnalysisFor, CampaignToCheckForSendingLimitNewFlow}
import org.joda.time.DateTime
import utils.SRLogger
import utils.cronjobs.sender_volume_alert.model.CampaignSendingVolumeLogs
import utils.mq.MqCampaignSendReportMsg
import utils.timezones.TimezoneUtils

import scala.util.{Failure, Success, Try}

class DripCampaignHealthCheckService(
  dripCampaignHealthCheckDAO: DripCampaignHealthCheckDAO,
  campaignSendingVolumeLogsDAO: CampaignSendingVolumeLogsDAO,
  campaignService: CampaignService,
) {

  def fetchDripCampaignForMonitoring(
    implicit Logger: SRLogger
  ): Try[List[DripCampaignMonitorData]] = {

    for {

      campaignsFroDripMonitor: List[DripCampaignMonitorDataWithStartTime] <-
        dripCampaignHealthCheckDAO.fetchDripCampaignForMonitoring

      campaignsFroDripMonitorFiltered: List[DripCampaignMonitorDataWithStartTime] =
        campaignsFroDripMonitor.filter { dcm =>

          dcm.todayCount match {

            case 0 =>

              dcm.dailyFromTime.plusMinutes(15).isBefore(DateTime.now())

            case 1 =>

              dcm.dailyFromTime.plusMinutes(45).isBefore(DateTime.now())

            case 2 =>

              dcm.dailyFromTime.plusMinutes(120).isBefore(DateTime.now())

            case _ =>

              false

          }

        }

      campaignsUpdatePushedToQueue: List[DripCampaignMonitorData] <- {

        Logger.info(
          msg = s"fetchDripCampaignForMonitoring. campaignsFroDripMonitor: $campaignsFroDripMonitor :: campaignsFroDripMonitorFiltered: $campaignsFroDripMonitorFiltered :: campaignsFroDripMonitorSize: ${campaignsFroDripMonitor.size} :: campaignsFroDripMonitorFilteredSize: ${campaignsFroDripMonitorFiltered.size}"
        )

        dripCampaignHealthCheckDAO.setPushedToQueueForDripMonitorCheck(

          dripCampaignMonitorDataList = campaignsFroDripMonitorFiltered.map { cd =>

            DripCampaignMonitorData(
              campaignId = cd.campaignId,
              teamId = cd.teamId,
            )

          }

        )
      }
    } yield {

      campaignsUpdatePushedToQueue

    }

  }

  def processDripCampaignHealthCheck(
    dripCampaignId: CampaignId,
    teamId: TeamId,
  )(
    implicit Logger: SRLogger,
  ): Try[Int] = {

    val tryOfDoneTaskWithNonCompletedProspectsList = for {

      doneTaskWithNonCompletedProspectsList: List[CompletedTaskWithNonCompletedProspectData] <- dripCampaignHealthCheckDAO.fetchCompletedTaskWithNonCompletedProspects(
        campaignId = dripCampaignId,
        teamId = teamId,
      )

      campaign_to_check: CampaignToCheckForSendingLimitNewFlow <- campaignService.fetchRunningCampaignsDataForLowSending(
        mqCampaignSendReportMsg = MqCampaignSendReportMsg(
          teamId = teamId,
          campaignId = dripCampaignId,
        )
      )

      startOfCampaign = TimezoneUtils
        .getStartOfDayWithTimezone(timezone = campaign_to_check.timezone)
        .plusSeconds(campaign_to_check.daily_from_time)

      timeSinceStartOfCampaign: Int =
        new org.joda.time.Duration(startOfCampaign, DateTime.now())
          .getStandardMinutes
          .toInt

      campaignSendingVolumeLog: CampaignSendingVolumeLogs = CampaignSendingVolumeLogs(
        campaign_id = campaign_to_check.campaign_id,
        team_id = campaign_to_check.team_id,
        date_in_campaign_timezone = startOfCampaign.toDate.toString,
        campaign_timezone = campaign_to_check.timezone,
        consecutive_delay_in_seconds = 0,
        warmup_is_on = campaign_to_check.warmupSettingOpt.isDefined,
        warmup_started_at = campaign_to_check.warmupSettingOpt.map(_.warmup_started_at),
        warmup_starting_email_count = campaign_to_check.warmupSettingOpt.map(_.warmup_starting_email_count),
        campaign_start_time = startOfCampaign,
        pushed_for_checking_at_minutes_since_campaign_start_time = timeSinceStartOfCampaign,
        actual_minutes_since_campaign_start_time_during_computing = timeSinceStartOfCampaign,
        current_sent_count = campaign_to_check.total_emails_sent_till_now,
        expected_sent_count_till_now = 0,
        current_sent_percent = 0,
        total_scheduled_for_today_till_now = campaign_to_check.total_scheduled_for_today_till_now,
        possible_issue_if_any = None,
        added_at = DateTime.now()
      )

      _: Int <- campaignSendingVolumeLogsDAO.addCampaignSendingVolumeLog(
        campaignAnalysisFor = CampaignAnalysisFor.DripMonitor,
        campaignSendingVolumeLogs = campaignSendingVolumeLog,
      )

      _: DripCampaignMonitorData <- dripCampaignHealthCheckDAO.setDripMonitorCheckComplete(
        dripCampaignMonitorData = DripCampaignMonitorData(
          campaignId = dripCampaignId,
          teamId = teamId,
        ),
      ).flatMap {

        case None =>

          Logger.shouldNeverHappen(
            msg = s"setDripMonitorCheckComplete failed - None - campaignId: ${dripCampaignId.id} :: teamId: ${teamId.id}",
            err = None,
          )

          Failure(new Exception("setDripMonitorCheckComplete failed - None"))

        case Some(dc) =>

          Success(dc)

      }

    } yield {

      doneTaskWithNonCompletedProspectsList

    }

    tryOfDoneTaskWithNonCompletedProspectsList match {

      case Failure(exception) =>

        Logger.shouldNeverHappen(
          msg = s"fetchCompletedTaskWithNonCompletedProspects failed - campaignId: ${dripCampaignId.id} :: teamId: ${teamId.id}",
          err = Some(exception),
        )

        Failure(exception)

      case Success(doneTaskWithNonCompletedProspectsList) =>

        if (doneTaskWithNonCompletedProspectsList.nonEmpty) {

          Logger.error(
            msg = s"Found completed tasks with non completed prospects - teamId: ${teamId.id} :: campaignId: ${dripCampaignId.id} :: doneTaskWithNonCompletedProspectsList: $doneTaskWithNonCompletedProspectsList",
          )

        }

        Success(
          doneTaskWithNonCompletedProspectsList.size
        )

    }

  }

}
