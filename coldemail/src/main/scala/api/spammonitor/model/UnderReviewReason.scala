package api.spammonitor.model

import utils.SRLogger
import utils.enum_sr_utils.SREnumJsonUtils

import scala.util.{Failure, Success, Try}

sealed trait UnderReviewStage

object UnderReviewStage {

  private val while_signing_up = "while_signing_up"
  private val while_operating = "while_operating"

  case object WhileSigningUp extends UnderReviewStage {
    override def toString: String = while_signing_up
  }

    case object WhileOperating extends UnderReviewStage {
      override def toString: String = while_operating
    }

}
sealed trait UnderReviewReason {
  def underReviewStage: UnderReviewStage
  def underReviewStatus: UnderReviewStatus
}


object UnderReviewReason extends SREnumJsonUtils[UnderReviewReason]{
  override protected val enumName: String = "UnderReviewReasons"

  private val login_email_domain_age = "login_email_domain_age"
  private val sending_email_domain_age = "sending_email_domain_age"
  private val login_email_contains_number = "login_email_contains_number"
  private val sending_email_contains_number = "sending_email_contains_number"
  private val could_not_check_login_email_domain_age = "could_not_check_login_email_domain_age"
  private val could_not_check_sending_email_domain_age = "could_not_check_sending_email_domain_age"
  private val primary_email_account_age = "primary_email_account_age"
  private val high_spam_complaint = "high_spam_complaint"
  private val low_open_rate = "low_open_rate"
  private val high_bounce_rate = "high_bounce_rate"
  private val hate = "hate"
  private val threatening = "threatening"
  private val self_harm = "self_harm"
  private val sexual = "sexual"
  private val sexual_minors = "sexual_minors"
  private val violence = "violence"
  private val violence_graphic = "violence_graphic"
  private val spam = "spam"


//  case object LoginEmailDomainAge extends UnderReviewReason{
//    override def toString: String = login_email_domain_age
//
//    override def underReviewStage: UnderReviewStage = UnderReviewStage.WhileSigningUp
//
//    override def underReviewStatus: UnderReviewStatus = EmailSendStatus.WarningForOneDay
//  }
//
//  case object SendingEmailDomainAge extends UnderReviewReason{
//    override def toString: String = sending_email_domain_age
//
//    override def underReviewStage: UnderReviewStage = UnderReviewStage.WhileSigningUp
//    override def underReviewStatus: UnderReviewStatus = EmailSendStatus.WarningForOneDay
//
//  }

  case object LoginEmailContainsNumber extends UnderReviewReason {
    override def toString: String = login_email_contains_number

    override def underReviewStage: UnderReviewStage = UnderReviewStage.WhileSigningUp
    override def underReviewStatus: UnderReviewStatus = EmailSendStatus.WarningForOneDay


  }

  case object SendingEmailContainsNumber extends UnderReviewReason {
    override def toString: String = sending_email_contains_number

    override def underReviewStage: UnderReviewStage = UnderReviewStage.WhileSigningUp
    override def underReviewStatus: UnderReviewStatus = EmailSendStatus.WarningForOneDay

  }

//  case object CouldNotCheckLoginEmailDomainAge extends UnderReviewReason {
//    override def toString: String = could_not_check_login_email_domain_age
//
//    override def underReviewStage: UnderReviewStage = UnderReviewStage.WhileSigningUp
//    override def underReviewStatus: UnderReviewStatus = EmailSendStatus.WarningForOneDay
//
//  }
//
//  case object CouldNotCheckSendingEmailDomainAge extends UnderReviewReason {
//    override def toString: String = could_not_check_sending_email_domain_age
//
//    override def underReviewStage: UnderReviewStage = UnderReviewStage.WhileSigningUp
//    override def underReviewStatus: UnderReviewStatus = EmailSendStatus.WarningForOneDay
//
//  }
//
//  case object PrimaryEmailAccountAge extends UnderReviewReason {
//    override def toString: String = primary_email_account_age
//
//    override def underReviewStage: UnderReviewStage = UnderReviewStage.WhileSigningUp
//    override def underReviewStatus: UnderReviewStatus = EmailSendStatus.WarningForOneDay
//  }

  case object HighSpamComplaint extends UnderReviewReason {
    override def toString: String = high_spam_complaint

    override def underReviewStage: UnderReviewStage = UnderReviewStage.WhileOperating
    override def underReviewStatus: UnderReviewStatus = EmailSendStatus.UnderReview


  }

  case object LowOpenRate extends UnderReviewReason {
    override def toString: String = low_open_rate

    override def underReviewStage: UnderReviewStage = UnderReviewStage.WhileOperating
    override def underReviewStatus: UnderReviewStatus = EmailSendStatus.WarningForOneDay

  }

  case object HighBounceRate extends UnderReviewReason {
    override def toString: String = high_bounce_rate

    override def underReviewStage: UnderReviewStage = UnderReviewStage.WhileOperating
    override def underReviewStatus: UnderReviewStatus = EmailSendStatus.UnderReview

  }

  case object Hate extends UnderReviewReason {
    override def toString: String = hate

    override def underReviewStage: UnderReviewStage = UnderReviewStage.WhileOperating
    override def underReviewStatus: UnderReviewStatus = EmailSendStatus.WarningForOneDay

  }

  case object Threatening extends UnderReviewReason {
    override def toString: String = threatening

    override def underReviewStage: UnderReviewStage = UnderReviewStage.WhileOperating
    override def underReviewStatus: UnderReviewStatus = EmailSendStatus.WarningForOneDay

  }

  case object Self_Harm extends UnderReviewReason {
    override def toString: String = self_harm

    override def underReviewStage: UnderReviewStage = UnderReviewStage.WhileOperating
    override def underReviewStatus: UnderReviewStatus = EmailSendStatus.WarningForOneDay

  }

  case object Sexual extends UnderReviewReason {
    override def toString: String = sexual

    override def underReviewStage: UnderReviewStage = UnderReviewStage.WhileOperating
    override def underReviewStatus: UnderReviewStatus = EmailSendStatus.WarningForOneDay

  }

  case object Sexual_Minors extends UnderReviewReason {
    override def toString: String = sexual_minors

    override def underReviewStage: UnderReviewStage = UnderReviewStage.WhileOperating
    override def underReviewStatus: UnderReviewStatus = EmailSendStatus.WarningForOneDay

  }

  case object Violence extends UnderReviewReason {
    override def toString: String = violence

    override def underReviewStage: UnderReviewStage = UnderReviewStage.WhileOperating
    override def underReviewStatus: UnderReviewStatus = EmailSendStatus.WarningForOneDay

  }

  case object Violence_Graphic extends UnderReviewReason {
    override def toString: String = violence_graphic

    override def underReviewStage: UnderReviewStage = UnderReviewStage.WhileOperating
    override def underReviewStatus: UnderReviewStatus = EmailSendStatus.WarningForOneDay

  }

  case object Spam extends UnderReviewReason {
    override def toString: String = spam

    override def underReviewStage: UnderReviewStage = UnderReviewStage.WhileOperating
    override def underReviewStatus: UnderReviewStatus = EmailSendStatus.WarningForOneDay

  }

  override def fromKey(key: String): Try[UnderReviewReason] = Try{
    key match {
//      case `login_email_domain_age` => LoginEmailDomainAge
//      case `sending_email_domain_age` => SendingEmailDomainAge
      case `login_email_contains_number` => LoginEmailContainsNumber
      case `sending_email_contains_number` => SendingEmailContainsNumber
//      case `could_not_check_login_email_domain_age` => CouldNotCheckLoginEmailDomainAge
//      case `could_not_check_sending_email_domain_age` => CouldNotCheckSendingEmailDomainAge
//      case `primary_email_account_age` => PrimaryEmailAccountAge
      case `high_spam_complaint` => HighSpamComplaint
      case `low_open_rate` => LowOpenRate
      case `high_bounce_rate` => HighBounceRate
      case `hate` => Hate
      case `threatening` => Threatening
      case `self_harm` => Self_Harm
      case `sexual` => Sexual
      case `sexual_minors` => Sexual_Minors
      case `violence` => Violence
      case `violence_graphic` => Violence_Graphic
      case `spam` => Spam
    }
  }

  override def toKey(value: UnderReviewReason): String = value.toString


  def fromKeySequence(keys: Seq[String])(using Logger: SRLogger): Try[Seq[UnderReviewReason]] = Try{

    var seq: Seq[UnderReviewReason] = Seq()
    keys.foreach{ key =>

      fromKey(key) match {
        case Success(value) =>
          seq = seq ++ Seq(value)
        case Failure(err) =>
          Logger.fatal(s"Failed to get UnderReviewReason ENUM for db key $key", err = err)
      }

    }

    seq

  }
}
