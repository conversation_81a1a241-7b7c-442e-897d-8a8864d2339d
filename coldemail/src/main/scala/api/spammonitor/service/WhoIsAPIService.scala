package api.spammonitor.service

import api.AppConfig
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat
import play.api.libs.json._
import play.api.libs.ws.{WSClient, WSResponse}
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}
import scala.runtime.Nothing$
import scala.util.{Failure, Success, Try}

case class ApiLayerResponse (
                              domain_name : String,
                              creation_date: Option[DateTime],
                              expiration_date: Option[DateTime],
                              jsonData: JsValue
                            )


trait WhoIsApiCallError

object WhoIsApiCallError {
  case class WrongResponseFromSRServer(response : String) extends WhoIsApiCallError
  case class InvalidDataSentBackErrorWhoIs(err: Throwable) extends WhoIsApiCallError
}
trait WhoIsAPIService{
  def getDomainDetails(domain:String)(using Logger: SRLogger, ec: ExecutionContext, ws: WSClient):Future[Either[WhoIsApiCallError, ApiLayerResponse]]
}
