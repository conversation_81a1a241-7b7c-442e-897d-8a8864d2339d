package api.spammonitor.service


import api.campaigns.{Campaign, CampaignDAO, CampaignStepDAO, CampaignStepVariantDAO}
import api.accounts.{AccountDAO, OrganizationForSupportApp, TeamId}
import io.sr.billing_common.models.PlanType
import api.accounts.dao.OrganizationDAO
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.models.{CampaignStepData, CampaignStepType}
import sr_scheduler.CampaignStatus
import api.campaigns.services.{CampaignId, CampaignService, CampaignStepService}
import api.emails.EmailScheduledDAO
import api.free_email_domain.service.FreeEmailDomainListService
import api.prospects.models.StepId
import api.spammonitor.model.{EmailSendStatus, UnderReviewStatus}
import api.sr_ai.service.ContentAnalysisService
import org.joda.time.format.DateTimeFormat
import utils.{CommandExecutor, Helpers}
//import api.scylla.dao.CacheEventScyllaDAO
import api.spammonitor.dao.{DomainInfoWhoisDAO, EmailSendingStatus, UpdateEmailSendingStatusForm}
import api.spammonitor.model.{EmailSendingEntityTypeData, SendEmailStatusData, UnderReviewReason}
import org.joda.time.{DateTime, Duration}
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.email_notification.service.EmailNotificationService
import utils.emailvalidation.EmailValidationService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

sealed trait CheckIfEmailIsAllowedToSignUpError

object CheckIfEmailIsAllowedToSignUpError {
  case class FailedToCheckIfDomainFree(err: Throwable) extends CheckIfEmailIsAllowedToSignUpError

  case object DomainIsFree extends CheckIfEmailIsAllowedToSignUpError


}

case class UnderReviewReasonsAndStepLabel (
                                           underReviewReasons: List[UnderReviewReason],
                                           stepLabel: String
                                           )

class SpamMonitorService(
                          freeEmailDomainListService: FreeEmailDomainListService,
                          domainDataService: DomainDataService,
                          domainInfoWhoisDAO: DomainInfoWhoisDAO,
                          organizationDAO: OrganizationDAO,
//                          cacheEventScyllazDAO: CacheEventScyllaDAO,
                          campaignDAO: CampaignDAO,
                          campaignStepDAO: CampaignStepDAO,
                          campaignStepVariantDAO: CampaignStepVariantDAO,
                          campaignService: CampaignService,
                          campaignStepService: CampaignStepService,
//                          gptService: GPTService,
                          emailSendingStatusService: EmailSendingStatusService,
                          emailScheduledDAO: EmailScheduledDAO,
                          accountDAO: AccountDAO,
                          contentAnalysisService: ContentAnalysisService,
                          emailNotificationService: EmailNotificationService
                        ) {


  private def checkIfFreeDomain(
                                 email: String
                               )(using Logger: SRLogger): Try[Boolean] = Try {
    val (_, domain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(email = email)

    freeEmailDomainListService.isFreeEmailDomain(domain = domain) match {
      case Failure(err) =>
        throw err
      case Success(true) =>
        !freeEmailDomainListService.isWhitelistedForSignupAndSendingEmail(email = email).get
      case Success(false) =>
        false
    }

  }


//  def checkDomainAgeInDays(
//                            domain: String
//                          )(using Logger: SRLogger, ec: ExecutionContext, ws: WSClient): Future[Long] = {
//    domainDataService.getDomainAge(
//      domain = domain
//    ) map {
//      case Left(some) =>
//        some match {
//          case  WhoIsApiCallError.InvalidDataSentBackErrorWhoIs(err) =>
//            Logger.fatal(s"Error while getting age from Whois API InvalidDataSentBackErrorWhoIs --- error - $err")
//          case WhoIsApiCallError.WrongResponseFromSRServer(data) =>
//            Logger.fatal(s"Error while getting age from Whois API WrongResponseFromSRServer --- data sent back - $data")
//        }
//
//        throw new Throwable(s"Error while getting age from Whois API --- error - $some")
//      case Right(apiLayerResponse) =>
//            apiLayerResponse.creation_date match {
//              case Some(value) =>
//                domainInfoWhoisDAO.createWhoisCheckLog(data = apiLayerResponse) match {
//                  case Failure(err) =>
//                    throw err
//                  case Success(_) =>
//                    val today = DateTime.now()
//                    val differenceBetween = new Duration(value, today).getStandardDays
//                    differenceBetween
//                }
//              case None => 0L
//            }
//    }
//  }


  private def checkIfNumberInEmail(
                                    email: String
                                  )(using Logger: SRLogger): Boolean = {
    val (name, _) = EmailValidationService.getLowercasedNameAndDomainFromEmail(email = email)

    name.exists(_.isDigit)

  }


  def checkIfEmailIsAllowedToSignUp(
                                     email: String
                                   )(using Logger: SRLogger, ec: ExecutionContext, ws: WSClient): Future[Either[CheckIfEmailIsAllowedToSignUpError, SendEmailStatusData]] = {

    checkIfFreeDomain(email = email) match {
      case Failure(err) =>

        Future(Left(CheckIfEmailIsAllowedToSignUpError.FailedToCheckIfDomainFree(err)))

      case Success(true) =>

        Future(Left(CheckIfEmailIsAllowedToSignUpError.DomainIsFree))

      case Success(false) =>
        var underReviewReason: List[UnderReviewReason] = List()
        val futureOfGettingUnderReviewReasons = {
          if (checkIfNumberInEmail(email = email)) {
            underReviewReason = underReviewReason ++ Seq(UnderReviewReason.LoginEmailContainsNumber)

          }
//          val (_, domain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(email = email)
//          checkDomainAgeInDays(domain = domain).map { daysSinceDomainCreated =>
//
//            if (daysSinceDomainCreated < AppConfig.age_in_days_for_spam_monitor) {
//              underReviewReason = underReviewReason ++ Seq(UnderReviewReason.LoginEmailDomainAge)
//
//            }
//            underReviewReason
//          }.recover { case e =>
//            Logger.error(s"Error while checkDomainAgeInDays", e)
//            underReviewReason = underReviewReason ++ Seq(UnderReviewReason.CouldNotCheckLoginEmailDomainAge)
//            underReviewReason
//
//          }
          Future.successful(underReviewReason)

        }


        futureOfGettingUnderReviewReasons map { result =>
          if (underReviewReason.isEmpty) {
            Right(SendEmailStatusData.AllowedData())
          } else {
            if(result.map(_.underReviewStatus).contains(EmailSendStatus.UnderReview)) {
              Right(SendEmailStatusData.UnderReviewData(
                underReviewReasons = result,
                stepLabels = Seq(),
              )) // No Campaign Steps Involved
            } else {
              Right(SendEmailStatusData.WarningData(
                underReviewReasons = result,
                stepLabels = Seq(),
                created_at = DateTime.now() // this is dummy, in use we will use the one we get from db
              )) // No Campaign Steps Involved
            }
          }
        }
    }
  }

  def checkIfEmailIsAllowedToSend(
                                   email: String,
                                   orgId: Long
                                 )(using Logger: SRLogger, ec: ExecutionContext, ws: WSClient): Future[SendEmailStatusData] = {
    checkIfOrgIsPaidWithMoreThan5EmailSettings(
      orgId = orgId
    ) match {
      case Failure(err) =>
        Future.failed(err)
      case Success(checkIfOrgIsAllowedToPass) =>
        if (checkIfOrgIsAllowedToPass) {
          val sendEmailStatusData = SendEmailStatusData.AllowedData()
          Future.successful(sendEmailStatusData)
        } else {

          var underReviewReason: List[UnderReviewReason] = List()
          val futureOfGettingUnderReviewReasons = {
            if (checkIfNumberInEmail(email = email)) {
              underReviewReason = underReviewReason ++ Seq(UnderReviewReason.SendingEmailContainsNumber)
            }
//            val (_, domain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(email = email)
//            checkDomainAgeInDays(domain = domain).map { daysSinceDomainCreated =>
//
//              if (daysSinceDomainCreated < AppConfig.age_in_days_for_spam_monitor) {
//                underReviewReason = underReviewReason ++ Seq(UnderReviewReason.SendingEmailDomainAge)
//              }
//              underReviewReason
//            }.recover { case e =>
//              Logger.error(s"Error while checkDomainAgeInDays", e)
//              underReviewReason = underReviewReason ++ Seq(UnderReviewReason.CouldNotCheckSendingEmailDomainAge)
//
//              underReviewReason
//
//            }
            Future.successful(underReviewReason)

          }


          futureOfGettingUnderReviewReasons map { result =>
            if (underReviewReason.isEmpty) {
              SendEmailStatusData.AllowedData()
            } else {
              if (result.map(_.underReviewStatus).contains(EmailSendStatus.UnderReview)) {
                SendEmailStatusData.UnderReviewData(
                  underReviewReasons = result,
                  stepLabels = Seq(),
                ) // No Campaign Steps Involved
              } else SendEmailStatusData.WarningData(
                underReviewReasons = result,
                stepLabels = Seq(),
                created_at = DateTime.now() // this is dummy, in use we will use the one we get from db
              ) // No Campaign Steps Involved

            }
          }
        }
    }
  }

  def checkIfOrgIsPaidWithMoreThan5EmailSettings(
                                                orgId:Long
                                                ): Try[Boolean] = {

    organizationDAO.getOrgForSupportByOrgId(
      orgId = orgId
    ) map {
      case None => false
      case Some(org) =>
        if(org.plan_type != PlanType.PAID.toString) {
          false
        }else{
          org.total_sending_email_accounts > 5
        }
    }
  }

  private def checkHighBounceRate(campaign: Campaign, Logger: SRLogger): Try[Boolean] = {
    emailScheduledDAO.getSentEmailCountForACampaignForLast24Hours(
      campaign_id = campaign.id,
      team_id = campaign.team_id
    )
      .flatMap(listOfEmailsSent => {

        val totalNumberOfEmailsSent = listOfEmailsSent.length
        val totalNumberOfBouncedReplies = listOfEmailsSent.count(hasBounced => hasBounced) //bounced_replies_in_scylla.length

        if (totalNumberOfBouncedReplies > totalNumberOfEmailsSent) {

          Logger.fatal(s"Total number of bounced replies($totalNumberOfBouncedReplies) more than total email($totalNumberOfEmailsSent) send in the last 24 hours, for campaignId - ${campaign.id}")
          Failure(new Throwable(s"Total number of bounced replies($totalNumberOfBouncedReplies) more than total email($totalNumberOfEmailsSent) send in the last 24 hours, for campaignId - ${campaign.id}"))

        }
        else if (totalNumberOfEmailsSent == 0 || totalNumberOfBouncedReplies < SpamMonitorService.minimumBounceReplyNumberAllowed) {

          Logger.debug(s"Total number of bounced replies($totalNumberOfBouncedReplies) out of total  email($totalNumberOfEmailsSent) send in the last 24 hours, for campaignId - ${campaign.id} IT IS ALLOWED")
          Success(false)

        }
        else if ((totalNumberOfBouncedReplies * 100) / totalNumberOfEmailsSent < SpamMonitorService.bounceRateThreshold) {

          Logger.debug(s"Total number of bounced replies($totalNumberOfBouncedReplies) out of total  email($totalNumberOfEmailsSent) send in the last 24 hours, for campaignId - ${campaign.id} IT IS ALLOWED")
          Success(false)

        }
        else {

          Logger.debug(s"Total number of bounced replies($totalNumberOfBouncedReplies) out of total email($totalNumberOfEmailsSent) send in the last 24 hours, for campaignId - ${campaign.id} IT IS UNDER REVIEW")
          Success(true)

        }
      })
  }

  def updateCampaignAndEmailSendStatus(
                                        campaignData: EmailSendingEntityTypeData.CampaignData,
                                        reasonsToPutCampaignUnderReview: List[UnderReviewReason],
                                        stepLabels: Seq[String],
                                        oldSendEmailStatus: Option[SendEmailStatusData],
                                        org: OrganizationForSupportApp
                                      )(
                                        implicit Logger: SRLogger,
                                        ws: WSClient,
                                        ec: ExecutionContext
                                      ): Future[Long] = {

    val sendEmailStatus = if (reasonsToPutCampaignUnderReview.map(_.underReviewStatus).contains(EmailSendStatus.UnderReview)) {
      SendEmailStatusData.UnderReviewData(
        underReviewReasons = reasonsToPutCampaignUnderReview,
        stepLabels = stepLabels
      )
    } else  SendEmailStatusData.WarningData(
      underReviewReasons = reasonsToPutCampaignUnderReview,
      stepLabels = stepLabels,
      created_at = DateTime.now() // this is dummy, in use we will use the one we get from db
    )
    val updateEmailSendingStatusForm = UpdateEmailSendingStatusForm(
      entityType = campaignData,
      orgId = campaignData.orgId,
      sendEmailStatus = sendEmailStatus
    )

    emailSendingStatusService.updateEmailSendingStatus(
      updateEmailSendingStatusForm = updateEmailSendingStatusForm,
      oldSendEmailStatus = oldSendEmailStatus,
      owner_email = org.owner_account_email
    ) match {
      case Left(err) =>
        Future.failed(new Throwable(s"Error while Adding email send status in DB - $err"))
      case Right(_) =>
        //if the email sending status is under review for
        // the campaign only then we want to put the campaign status under review
        if (sendEmailStatus.emailSendingStatus == EmailSendStatus.UnderReview) {
          campaignService.updateStatus(
            id = campaignData.campaignId,
            newStatus = CampaignStatus.UNDER_REVIEW,
            isSupport = true,
            teamId = TeamId(campaignData.teamId)
          ) match {
            case Failure(err) => Future.failed(err)
            case Success(None) =>
              Logger.fatal(s"CAMPAIGN NOT FOUND AFTER UPDATING id - ${campaignData.campaignId}")
              Future.failed(new Throwable(s"CAMPAIGN NOT FOUND AFTER UPDATING"))
            case Success(Some(campaignWithStatus)) =>

              if (reasonsToPutCampaignUnderReview.contains(UnderReviewReason.HighBounceRate)) {
                sendEmailToOwner(
                  orgId = campaignData.orgId.id,
                  owner_email = campaignWithStatus.owner_email,
                  owner_name = Some(campaignWithStatus.owner_name),
                  underReviewReason = UnderReviewReason.HighBounceRate
                ) match {
                  case Failure(err) =>
                    Logger.fatal(s"Failed to send email to ${campaignWithStatus.owner_email} for Spam monitor", err = err)
                  case Success(_) =>
                    Logger.debug(s"Email sent to ${campaignWithStatus.owner_email} for Spam monitor")

                }
              }

              Future.successful(campaignWithStatus.id)
          }
        } else Future.successful(campaignData.campaignId)


    }

  }

  def checkEmailContentInsideCampaignStep(
                                           stepId: StepId,
                                           campaignId: CampaignId,
                                           teamId: TeamId,
                                         )(
                                           implicit Logger: SRLogger,
                                           ws: WSClient,
                                           ec: ExecutionContext
                                         ): Future[UnderReviewReasonsAndStepLabel] = {

    campaignStepVariantDAO.findStepWithVariants(
      stepId = stepId,
      campaignId = campaignId
    ) match {
      case Failure(e) =>
        Future.failed(e)

      case Success(campaignStepWithChildren) =>
        Logger.info(s"Variants found: ${campaignStepWithChildren.variants}")
        Future.sequence {
          campaignStepWithChildren.variants.map(variant => {
            Logger.info("GPT content check start")
            variant.step_data match {
              case autoEmailStep: CampaignStepData.AutoEmailStep =>

                // gptService.categorizeEmail(
                //   email_body = autoEmailStep.body,
                //   subject = autoEmailStep.subject
                // )

                contentAnalysisService.categorizeEmail(
                  teamId = teamId,
                  emailBody = autoEmailStep.body,
                  subject = autoEmailStep.subject
                )

              case manualEmailStep: CampaignStepData.ManualEmailStep =>
                // gptService.categorizeEmail(
                //   email_body = manualEmailStep.body,
                //   subject = manualEmailStep.subject
                // )

                contentAnalysisService.categorizeEmail(
                  teamId = teamId,
                  emailBody = manualEmailStep.body,
                  subject = manualEmailStep.subject
                )

              case _ =>
                // This case would never occur
                Logger.fatal(s"variant_${variant.id} has different step_type than step_${stepId.id}")
                Future.successful(List())
            }
          })
        }
          .map(reasonsToBeFlattened => {
            UnderReviewReasonsAndStepLabel(
              underReviewReasons = reasonsToBeFlattened.flatten.distinct.toList,
              stepLabel = campaignStepWithChildren.label.get
            )
          })

    }

  }

  def checkContentAndUpdateStatus(
                                 stepId: StepId,
                                 campaignId: CampaignId,
                                 teamId: TeamId
                                 )(
                                   implicit Logger: SRLogger,
                                   ws: WSClient,
                                   ec: ExecutionContext
                                 ): Future[Long] = {

    checkEmailContentInsideCampaignStep(
      stepId = stepId,
      campaignId = campaignId,
      teamId = teamId,
    )
      .flatMap(underReviewReasonsAndStepLabel => {
        if (underReviewReasonsAndStepLabel.underReviewReasons.nonEmpty) {
          Logger.info(s"$stepId will be flagged as ${underReviewReasonsAndStepLabel.underReviewReasons}")
          campaignDAO.getEmailSendingCampaignData(
            campaignId = campaignId,
            teamId = teamId
          ) match {

            case Failure(e) => Future.failed(e)

            case Success(campaignData) =>
              organizationDAO.getOrgForSupportByOrgId(
                orgId = campaignData.orgId.id
              ) match {
                case Failure(exception) => Future.failed(exception)
                case Success(None) => Future.failed(new Throwable("No Org found"))
                case Success(Some(org)) =>
                  updateCampaignAndEmailSendStatus(
                    campaignData = campaignData,
                    reasonsToPutCampaignUnderReview = underReviewReasonsAndStepLabel.underReviewReasons,
                    stepLabels = Seq(underReviewReasonsAndStepLabel.stepLabel),
                    oldSendEmailStatus = None,
                    org = org
                  )
                    .flatMap(res => {
                      campaignStepDAO.updateContentCheckStatus(
                        stepId = stepId,
                        campaignId = campaignId
                      ) match {
                        case Failure(e) =>
                          Logger.error(s"Error while updating content check status for ${stepId}", e)
                          Future.failed(e)

                        case Success(value) =>
                          Future.successful(res)
                      }
                    })
              }
          }

        }
        else {
          Logger.info(s"$stepId has no red flags.")
          campaignStepDAO.updateContentCheckStatus(
            stepId = stepId,
            campaignId = campaignId
          ) match {
            case Failure(e) => Future.failed(e)
            case Success(_) => Future.successful(1)
          }
        }
      })

  }

  def checkIfCampaignAllowedToSendEmail(
                                         campaignData: EmailSendingEntityTypeData.CampaignData,
                                         oldEmailSendingStatus: SendEmailStatusData
                                       )(using Logger: SRLogger, ec: ExecutionContext, ws: WSClient): Future[Long] = {

    //    cacheEventScyllaDAO.getBouncedRepliesForCampaign(
    //      campaign_id = campaignData.campaignId,
    //      org_id = campaignData.orgId
    //    ).flatMap{ bounced_replies_in_scylla =>

    organizationDAO.getOrgForSupportByOrgId(
      orgId = campaignData.orgId.id
    ) match {
      case Failure(exception) => Future.failed(exception)
      case Success(None) => Future.failed(new Throwable("No Org found"))
      case Success(Some(org)) =>
        campaignDAO.findCampaignForCampaignUtilsOnly(
          id = campaignData.campaignId,
          teamId = TeamId(campaignData.teamId)
        ) match {
          case None =>
            Logger.fatal(s"No campaign found for campaignId - ${campaignData.campaignId}")
            Future.failed(new Throwable(s"No campaign found for campaignId - ${campaignData.campaignId}"))
          case Some(campaign) =>
            Future.fromTry {
                checkHighBounceRate(campaign, Logger)
              }
              .flatMap(
                hasHighBounceRate => {

                  val reasonsToPutCampaignUnderReview: List[UnderReviewReason] = if (hasHighBounceRate) {
                    List(UnderReviewReason.HighBounceRate)
                  }
                  else {
                    List()
                  }

                  if (reasonsToPutCampaignUnderReview.isEmpty) {

                    val updateEmailSendingStatusForm = UpdateEmailSendingStatusForm(
                      entityType = campaignData,
                      orgId = campaignData.orgId,
                      sendEmailStatus = oldEmailSendingStatus
                    )

                    emailSendingStatusService.updateEmailSendingStatus(
                      updateEmailSendingStatusForm = updateEmailSendingStatusForm,
                      oldSendEmailStatus = Some(oldEmailSendingStatus),
                      owner_email = org.owner_account_email
                    ) match {
                      case Left(err) =>
                        Future.failed(new Throwable(s"Error while Adding email send status in DB - $err"))
                      case Right(_) => Future.successful(campaignData.campaignId)
                    }

                  }
                  else {

                    updateCampaignAndEmailSendStatus(
                      campaignData = campaignData,
                      reasonsToPutCampaignUnderReview = reasonsToPutCampaignUnderReview,
                      stepLabels = Seq(),
                      oldSendEmailStatus = Some(oldEmailSendingStatus),
                      org = org
                    )

                  }
                })
        }
    }

  }

  def sendEmailToOwner(
                      orgId: Long,
                      owner_email: String,
                      owner_name: Option[String],
                      underReviewReason: UnderReviewReason
                      )(using Logger: SRLogger, ec: ExecutionContext, ws: WSClient): Try[Unit] = {
    organizationDAO.getOrg(
      orgId = orgId
    ).flatMap {
      case None =>
        Failure(new Throwable("Org not found after updating"))

      case Some(org) =>

        for {
          lastSentAtOption <- accountDAO.getWhenTheLastSpamReviewEmailWasSent(
            accountId = AccountId(id = org.owner_account_id.get) // FIXME VALUECLASS
          )

          emailSent <- {
            if (checkIfWeSendEmail(
              lastSentAtOption = lastSentAtOption
            )) {
              emailNotificationService.sendMailFromAdmin(
                toEmail = owner_email,
                toName = owner_name,
                subject = "URGENT: Your campaign(s) are under review for Spam Check",
                body = s"${owner_name.getOrElse("Hey There")}, We regret to inform you that your campaign was sent under review for $underReviewReason"
              )
            } else {
              Success({})
            }
          }
        } yield {
          emailSent
        }

    }
  }

  def checkIfWeSendEmail(
                          lastSentAtOption: Option[DateTime]
                        ): Boolean = {

    lastSentAtOption match {
      case None => true

      case Some(lastSentAt) =>
        val today = DateTime.now ()
        val durationInHours = new Duration (lastSentAt, today).getStandardHours
        durationInHours > 24
    }
  }


  def getLast24HoursForCampaignsForCron()(using Logger: SRLogger): Try[List[EmailSendingStatus]] = {

    emailSendingStatusService.getCampaignForReviewFromLast24Hours()
  }


  def warningToUnderReviewUpdate()(implicit wsClient: WSClient, ec: ExecutionContext, Logger: SRLogger): Try[Seq[Int]] = {
    emailSendingStatusService.getEmailSendingStatusAll(
      emailSendStatuses = Seq(api.spammonitor.model.EmailSendStatus.WarningForOneDay),
      getBefore = Some(DateTime.now().minusHours(24))
    ) match {
      case Failure(exception) => Logger.fatal(s"Failed to getEmailSendingStatusAll", exception)
        Failure(exception)
      case Success(value) =>
        val result = value.map { warning =>
          warning.send_email_status match {
            case _: SendEmailStatusData.UnderReviewData =>
              Logger.shouldNeverHappen(s"UnderReviewData given when we asked for warning - $warning")
              Failure(new Throwable(s"UnderReviewData given when we asked for warning - $warning"))

            case _: SendEmailStatusData.ManualReviewData =>
              Logger.shouldNeverHappen(s"ManualReviewData data given when we asked for warning - $warning")
              Failure(new Throwable(s"ManualReviewData given when we asked for warning - $warning"))

            case _: SendEmailStatusData.AllowedData =>
              Logger.shouldNeverHappen(s"AllowedData data given when we asked for warning - $warning")
              Failure(new Throwable(s"AllowedData given when we asked for warning - $warning"))

            case _: SendEmailStatusData.BlockedData =>
              Logger.shouldNeverHappen(s"BlockedData data given when we asked for warning - $warning")
              Failure(new Throwable(s"BlockedData given when we asked for warning - $warning"))

            case data: SendEmailStatusData.WarningData =>
              if (warning.created_at.isBefore(DateTime.now().minusHours(24))) {
                organizationDAO.getOrgForSupportByOrgId(
                  orgId = warning.entity_type.getOrgId().id
                ) match {
                  case Failure(exception) => Failure(exception)
                  case Success(None) => Failure(new Throwable("No Org found"))
                  case Success(Some(org)) =>
                    emailSendingStatusService.updateEmailSendingStatus(
                      updateEmailSendingStatusForm = UpdateEmailSendingStatusForm(
                        entityType = warning.entity_type,
                        orgId = warning.entity_type.getOrgId(),
                        sendEmailStatus = SendEmailStatusData.UnderReviewData(
                          underReviewReasons = data.underReviewReasons, stepLabels = data.stepLabels
                        )
                      ),
                      oldSendEmailStatus = Some(data),
                      owner_email = org.owner_account_email
                    ) match {
                      case Right(_) =>
                        Success(1)
                      case Left(value) =>
                        value match {
                          case UpdateEmailSendingStatusError.ErrorWhileAddingInDB(err) =>
                            Logger.fatal(s"failed updateEmailSendingStatus ErrorWhileAddingInDB", err)
                            Failure(err)
                        }
                    }
                }
              } else Success(0)
          }
        }

        Helpers.seqTryToTrySeq(result)
    }
  }

}

object SpamMonitorService {

  val bounceRateThreshold = 15

  val minimumBounceReplyNumberAllowed = 5
}
