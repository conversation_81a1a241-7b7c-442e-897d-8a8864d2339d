package api.spammonitor.service

import api.AppConfig
import api.spammonitor.service.{ApiLayerResponse, WhoIsAPIService, WhoIsApiCallError}
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat
import play.api.libs.json.{JsVal<PERSON>, <PERSON>son}
import play.api.libs.ws.WSClient
import utils.{CommandExecutor, SRLogger}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class ApiLayerAPIService extends WhoIsAPIService{
  override def getDomainDetails(domain: String)(using Logger: SRLogger, ec: ExecutionContext, ws: WSClient): Future[Either[WhoIsApiCallError, ApiLayerResponse]]={
    Logger.info(s"Getting domain : ${domain} from APILayer Service")
    ws.url(s"https://api.apilayer.com/whois/query?domain=$domain")
      .addHttpHeaders("apikey" -> AppConfig.ThirdPartyApiKeys.api_key_for_api_layer)
      .get().map {
      response =>

        if (response.status == 200) {
          Try {
            val data=Json.parse(response.body)

            ApiLayerResponse(
              domain_name = (data \ "result" \ "domain_name").as[String],
              creation_date = (data \ "result" \ "creation_date")
                .asOpt[String]
                .map(date => ApiLayerAPIService.matchDateTimeWithSecFallbackToMills(date).get),
              expiration_date = (data \ "result" \ "expiration_date")
                  .asOpt[String]
                .map(date => ApiLayerAPIService.matchDateTimeWithSecFallbackToMills(date).get),
              jsonData = (data \ "result").as[JsValue]
            )
          } match {
            case Failure(err) =>
              Logger.fatal(s"Error while getting age from Whois API InvalidDataSentBackErrorWhoIs body --- ${response.body} --- json --- ${response.json} --- error - $err")
              Left(WhoIsApiCallError.InvalidDataSentBackErrorWhoIs(err))
            case Success(value) =>
              Right(value)
          }

        } else {
          Left(WhoIsApiCallError.WrongResponseFromSRServer(response.body))
        }
    }
  }



}

object ApiLayerAPIService {
  def matchDateTimeWithSecFallbackToMills(
                                           date: String
                                         )(using Logger: SRLogger): Try[DateTime] = Try{
    val datetimeFormat = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss")
    val datetimeFormatMillis = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSSSSS")
    Try {
       DateTime.parse(date, datetimeFormat)
    } match {
      case Success(value) => value
      case Failure(exception) =>
        Logger.error(s"Failed to match date with no mills", exception)
        DateTime.parse(date, datetimeFormatMillis)
    }

  }
}

