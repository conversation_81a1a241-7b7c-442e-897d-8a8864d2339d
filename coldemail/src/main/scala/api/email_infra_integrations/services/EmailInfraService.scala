package api.email_infra_integrations.services

import api.AppConfig
import api.accounts.email.models.{EmailProvidedBy, EmailServiceProvider}
import api.accounts.{Account, AccountService, GetOAuthUrlError, SocialAuthService , GetOAuthUrlResponse, RepGoogleApiKey, TeamId, TeamMember}
import api.accounts.models.{AccountId, OrgId}
import api.billing.v2.services.SubscriptionService
import api.email_infra_integrations.dao.*
import api.email_infra_integrations.maildoso.*
import api.email_infra_integrations.maildoso.service.MailDosoService
import api.email_infra_integrations.models.*
import api.email_infra_integrations.models.UpdateZapMailMailboxRequest
import api.email_infra_integrations.models.PlatformType.MAILDOSO
import api.email_infra_integrations.zapmail.*
import api.emails.models.EmailSettingUuid
import api.emails.services.{CreateEmailError, EmailAccountService, EmailSettingService}
import api.emails.{EmailSetting, EmailSettingDAO, EmailSettingForm, PurchasedEmailInfo}
import api.prospects.dao_service.ProspectDAOService
import api.rep_mail_servers.models.RepMailServer
import api.rep_mail_servers.services.SrMailServerService
import io.smartreach.esp.api.emailApi.models.ESPMailgunRegion
import play.api.libs.json.JodaWrites.*
import play.api.libs.json.JodaReads.*
import io.smartreach.esp.api.emails.EmailSettingId
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.pattern.after
import play.api.libs.json.{JsValue, Json, OFormat}
import org.joda.time.DateTime
import utils.security.EncryptionHelpers

import scala.concurrent.{ExecutionContext, Future}
import play.api.libs.ws.WSClient
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus
import utils.{AddonLimitReachedException, Helpers, PlanLimitService, RequiredEmailAndDomains, SRLogger}
import utils.emailvalidation.EmailValidationService
import utils.helpers.LogHelpers
import utils.mq.emailInfra.MqZapmailEmailAccountCreationPublisher
import utils.mq.purchased_domains_and_emails_deleter.{DomainUuidWithPlatformType, MqPurchasedDomainsDeleter, MqPurchasedDomainsDeleterMsg, MqPurchasedEmailsDeleter, MqPurchasedEmailsDeleterMsg}
import utils.testapp.Test_TaskPgDAO.srUuidUtils

import scala.concurrent.duration
import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.util.{Failure, Success, Try}

case class PurchasedEmailsAndSaveData(
                                     create_email_account_response: CreateEmailAccountResponse,
                                     saveEmailSettingData: Seq[EmailSetting],
                                     teamId: TeamId,
                                     updatedCount: Int
                                     )

case class ZapMailMailboxDetailsForQueue(
  mailboxDetails: ZapMailMailboxRequestDetails,
  domain: String,
  accountId: AccountId,
  teamId: TeamId,
  orderId: ZapMailOrderID
)

sealed trait SaveDomainError

case class AddonLimitReachedForDomainPurchase(msg: String) extends SaveDomainError

sealed trait GetAddonsCountError

object GetAddonsCountError {

  case class GetPlanLimitServiceError(e: Throwable) extends GetAddonsCountError

  case object ParamsMissing extends GetAddonsCountError

}


class EmailInfraService(
                         mailDosoService: MailDosoService,
                         emailInfraDAO: EmailInfraDAO,
                         emailSettingDAO: EmailSettingDAO,
                         planLimitService: PlanLimitService,
                         emailSettingService: EmailSettingService,
                         emailAccountService: EmailAccountService,
                         accountService: AccountService,
                         socialAuthService: SocialAuthService,
                         mqPurchasedDomainsDeleter: MqPurchasedDomainsDeleter,
                         mqPurchasedEmailsDeleter: MqPurchasedEmailsDeleter,
                         mqZapmailEmailAccountCreationPublisher:MqZapmailEmailAccountCreationPublisher,
                         zapMailApi: ZapMailAPI
                       ) {


  def validateDomains(domains: List[String]): List[String] = {
    domains.filter(d => EmailValidationService.validateDomain(d))
  }

  def verifyEmails(
                    emails: List[String],
                    account_id: AccountId,
                    teamId: TeamId
                  ): Try[List[String]] = {
    Success(emails)
  }

  // use case 1 : user searches for domains
  def search_for_domains(
                          domains: List[String],
                        )(implicit ec: ExecutionContext, ws: WSClient, Logger: SRLogger): Future[DomainSearchResponse] = {
    // step 1 : create user if doesn't exist
    // step 2.a : validate the domain list
    // step 2.b : search for domain list

    if (domains.length > 100) Future.failed(new Exception("Maximum 100 domains can be searched at a time"))
    else {
      for {

        /*
        Date: 18-Jan-2025
        Wwe are removing the validation of domains as maildoso allows keyword search for domains.
        Hence the tld will be added by maildoso side in the search result.
         */

//        validated_domains <- Future(validateDomains(domains))

        search_domains: List[DomainResult] <- mailDosoService.searchDomains(domains, add_variations = Some(AppConfig.Maildoso.add_variations))

      } yield {
//        val filtered_out_domains: List[String] = domains.filter(d => !validated_domains.contains(d))
//        val search_result_for_filtered_out_domains = filtered_out_domains.map(d => DomainResult(
//          name = d, status = Some("invalid domains"), available = Some("no"), error = None
//        ))
//        val search_domains_result = search_domains
//
//        val all_domains_result = search_domains_result ++ search_result_for_filtered_out_domains

        DomainSearchResponse(
          domains = search_domains
        )
      }

    }


  }

  // use case 2 : user wants to buy domains
  def buy_maildoso_domains(
                            domains: List[String],
                            redirect_to: List[String],
                            account_id: AccountId,
                            teamId: TeamId
                          )(implicit ec: ExecutionContext, ws: WSClient): Future[CreateDomainResponse] = {
    // step 1 : call for search_domains api
    // step 2 : call buy domain api
    // step 3 : save response to disk with worker id

    // NOTE: ask maildoso => if some domains are not available will it succeed with the purchase of other domains?

    for {

      user_id: Long <- Future.fromTry(mailDosoService.getOrCreateMaildosoUser(
        account_id = account_id,
        teamId = teamId
      ))

      validated_domains: List[String] <- Future(validateDomains(domains))

      create_domains: CreateDomainResponse <- mailDosoService.createDomains(
        user_id = user_id,
        domains = validated_domains,
        redirect_to = redirect_to
      )

      save_maildoso_task_id <- Future.fromTry(mailDosoService.saveMaildosoTaskId(
        account_id = account_id,
        teamId = teamId,
        task_id = create_domains.task_id
      ))

    } yield {
      create_domains
    }


  }

  // use case 3 : user wants to buy emails
  def buy_maildoso_emails(
                           emails: List[String],
                           accountId: AccountId,
                           first_name: String,
                           last_name: String,
                           teamId: TeamId
                         )(implicit ec: ExecutionContext, ws: WSClient, Logger: SRLogger): Future[CreateEmailAccountResponse] = {
    // step 1 : verify that the user owns the domain
    // step 2 : complete the purchase of the email via the api
    // save the worker id to disk

    for {

      verify_emails: List[String] <- Future.fromTry(verifyEmails(emails, accountId, teamId))

      create_email_account_request: List[CreateEmailAccountRequest] = verify_emails.map(em =>
        CreateEmailAccountRequest(
          email_account = em,
          first_name = first_name,
          last_name = last_name,
          password = None,
          is_active = true,
          forwarding_account_id = None,
          skip_sequence = None
        ))

      create_emails: CreateEmailAccountResponse <- mailDosoService.createEmailAccount(create_email_account_request)

      save_maildoso_task_id <- Future.fromTry(mailDosoService.saveMaildosoTaskId(
        account_id = accountId,
        teamId = teamId,
        task_id = create_emails.task_id
      ))

    } yield {
      create_emails
    }

  }

  // use case 4 : list the domains purchased for the user
  // use case 5 : list the emails purchased for the user
  // use case 6 : to give price of the purchase to the user

  def getAddonsCountForDomainsAndEmailsPurchase(
                                                 toBePurchasedEmailQty: Option[Int],
                                                 toBePurchasedDomainQty: Option[Int],
                                                 platformType: Option[PlatformType],
                                                 teamId: TeamId,
                                               )(using Logger: SRLogger): Either[GetAddonsCountError, RequiredEmailAndDomains] = {

    if(toBePurchasedDomainQty.isEmpty || toBePurchasedEmailQty.isEmpty || platformType.isEmpty) {
      Left(GetAddonsCountError.ParamsMissing)
    } else {
      planLimitService.getAdditionalRequiredEmailsAndDomainForPurchase(
        toBePurchasedEmailQty = toBePurchasedEmailQty.get,
        toBePurchasedDomainQty = toBePurchasedDomainQty.get,
        platformType = platformType.get,
        teamId = teamId
      ) match {
        case Failure(err) => Left(GetAddonsCountError.GetPlanLimitServiceError(err))

        case Success(addonsCount: RequiredEmailAndDomains) => Right(addonsCount)
      }
    }
  }

  def getZapMailMicrosoftOAuthUrl(
                 account: Account,
                 teamId: TeamId
                 )(using logger: SRLogger, ec: ExecutionContext, ws: WSClient): Try[String] = {

    socialAuthService.getOAuthUrl(
      email_type = None,
      email_setting_id = None,
      emailSettingOpt = None,
      service_provider = "outlook",
      emailAddress = None,
      campaign_id = None,
      teamMember = Helpers.getTeamMemberFromAccount(account, teamId),
      email_address = None,
      campaign_basic_setup = Some(false),
      is_sandbox = Some(false),
      is_inbox = Some(false),
      goto_quickstart = Some(false),
      org = account.org,
      teamId = teamId,
      accountId = AccountId(account.internal_id),
      allowCRMIntegration = false,
      isZapmailFlow = true
    ) match {
      case Left(error) =>
        Failure(new Exception(s"Failed to get OAuth URL: ${error}"))
      case Right(response) => Success(response.redirect_to)
    }

  }

  def getZapMailGoogleOAuthConfig(
                 account: Account,
                 teamId: TeamId
                 )(using logger: SRLogger, ec: ExecutionContext, ws: WSClient): Future[ZapMailGoogleOAuthConfig] = {

    for {
      // Get Google API keys for the organization
      googleApiKey: RepGoogleApiKey <- Future.fromTry(emailSettingDAO.getGoogleAuthKeysForOrg(orgId = account.org.id))
        .recoverWith { case e =>
          logger.error(s"Failed to get Google API keys for org ${account.org.id}: ${e.getMessage}")
          Future.failed(new Exception(s"Failed to get Google API keys: ${e.getMessage}"))
        }

      // Get OAuth URL from social auth service and convert Either to Future
      oauthResponse: GetOAuthUrlResponse <- Future.successful(
        socialAuthService.getOAuthUrl(
          email_type = None,
          email_setting_id = None,
          emailSettingOpt = None,
          service_provider = "google",
          emailAddress = None,
          campaign_id = None,
          teamMember = Helpers.getTeamMemberFromAccount(account, teamId),
          email_address = None,
          campaign_basic_setup = Some(false),
          is_sandbox = Some(false),
          is_inbox = Some(false),
          goto_quickstart = Some(false),
          org = account.org,
          teamId = teamId,
          accountId = AccountId(account.internal_id),
          allowCRMIntegration = false,
          isZapmailFlow = true
        )
      ).flatMap {
        case Left(error) =>
          logger.error(s"Failed to get OAuth URL: ${error}")
          Future.failed(new Exception(s"Failed to get OAuth URL: ${error}"))
        case Right(response) =>
          Future.successful(response)
      }

    } yield {
      logger.info(s"getZapMailGoogleOAuthConfig: ${oauthResponse.redirect_to}")
      ZapMailGoogleOAuthConfig(
        googleOauthRoute = oauthResponse.redirect_to,
        clientId = googleApiKey.cl_id,
        appName = "SmartReach.io"
      )
    }
  }

  def purchaseZapMailDomains(
                              account: Account,
                              teamId: TeamId,
                              redirect_domain_url: String,
                              workspaceType: ZapMailWorkspaceType,
                              mailboxes: Map[String, List[ZapMailMailboxRequestDetails]] // Domain Name -> List of Mailbox Details
                            )(implicit ec: ExecutionContext, ws: WSClient, logger: SRLogger): Future[ZapMailOrderID] = {

    val accountId = AccountId(account.internal_id)
    val isOauthFlow = account.org.org_metadata.enable_oauth_for_zapmail.getOrElse(false)

    val uniqueDomains: List[String] = mailboxes.keys.toList
    val uniqueEmails: List[String] = mailboxes.flatMap {
      case (domain, mailboxDetailsList) =>
        mailboxDetailsList.map(mailboxDetails => s"${mailboxDetails.username.trim().toLowerCase}@$domain")
    }.toList

    logger.info(
      s"Purchase ZapMail Domains Service [purchaseZapMailDomains] : mailboxes :: ${mailboxes}"
    )

    for {

      _: Unit <- Future{
        if(uniqueDomains.exists(domain => domain.contains("gmail"))) {
          logger.error(
            s"Purchase ZapMail Domains Service [purchaseZapMailDomains] : One of the domains contains *gmail* which is not allowed. uniqueDomains :: ${uniqueDomains}"
          )
          throw new Exception("One of the domains contains *gmail* which is not allowed")
        }
      }

      hasAddonLimit: Boolean <- Future.fromTry(planLimitService.checkEmailsAndDomainsAddonLimitReached(
        toBePurchasedEmailQty = uniqueEmails.size,
        toBePurchasedDomainQty = uniqueDomains.size,
        platformType = PlatformType.ZAPMAIL,
        teamId = teamId
      ))

      _: Boolean <- if (hasAddonLimit) {
        Future.failed(new Exception("Addon limit reached"))
      } else {
        Future.successful(true)
      }


      googleOAuthConfig: Option[ZapMailGoogleOAuthConfig] <- if (isOauthFlow && workspaceType == ZapMailWorkspaceType.Google) {
        getZapMailGoogleOAuthConfig(
          account = account,
          teamId = teamId
        ).map(Some(_))
      } else {
        Future.successful(None)
      }

      microsoftOauthRoute: Option[String] <- if (workspaceType == ZapMailWorkspaceType.Microsoft) {
        Future.fromTry(getZapMailMicrosoftOAuthUrl(
          account = account,
          teamId = teamId
        ).map(Some(_)))
      } else {
        Future.successful(None)
      }

      orderId: ZapMailOrderID <- zapMailApi.createOrder(
        userId = ZapMailUserUID(
          uid = AppConfig.ZapMail.userId
        ),
        domains = uniqueDomains.map(domain => ZapMailDomainRegistration(
          domain = domain,
          years = AppConfig.ZapMail.domainPurchaseYears,
          forwardTo = None
        )
        ),
        mailboxes = mailboxes,
        billingCycle = Some(AppConfig.ZapMail.mailBoxPurchaseTerm),
        serviceProvider = Some(workspaceType),
        googleOauthConfig = googleOAuthConfig,
        microsoftOauthRoute = microsoftOauthRoute
      )

      orderDetails: List[ZapMailOrderItem] <- zapMailApi.getOrderDetails(
        orderId = orderId
      )

      _ <- Future {
        logger.info(
          s"Details of the order with order id : ${orderId.toString} is ${orderDetails}"
        )
      }

      // This is where , we do actual purchase
      orderStatus: Boolean <- zapMailApi.processOrder(
        orderId = orderId
      )

      _ <- Future {
        logger.info(
          s"Order with order id : ${orderId.toString} is processed"
        )


        emailInfraDAO.insertEmailInfraPurchaseLog(
          accountId = accountId,
          teamId = teamId,
          purchaseId = orderId.toString,
          provider = EmailProvidedBy.ZapMail,
          purchaseInfo = Json.toJson(orderDetails),
          purchaseStatus = orderStatus
        )
      }

      saveOrderUuids: List[String] <- Future.fromTry(emailInfraDAO.saveNewDomainsFromZapMail(
        accountId = accountId,
        teamId = teamId,
        redirect_domain_url = redirect_domain_url,
        workspace_type = EmailInfraWorkspaceType.fromZapMailWorkspaceType(workspaceType),
        purchasedDomains = mailboxes.map {
          case (domain, mailboxDetailsList) => ZapMailPurchaseDomain(
            domainName = domain,
            purchasedEmails = mailboxDetailsList.map(
              mailbox => PurchaseEmail.ZapMailWithoutPassword(
                email_account = s"${mailbox.username.toLowerCase.trim()}@$domain",
                first_name = mailbox.firstName,
                last_name = mailbox.lastName
              )
            ),
            orderId = orderId
          )
        }.toList // Convert Iterable to List here
      ))

      _ <- Future {
          if (!isOauthFlow && workspaceType == ZapMailWorkspaceType.Google) {
            mailboxes.map {
              case (domain, mailboxDetailsList) =>
                mailboxDetailsList.map { mailboxDetails =>
                  mqZapmailEmailAccountCreationPublisher.publishMessage(
                    msg = ZapMailMailboxDetailsForQueue(mailboxDetails = mailboxDetails,
                      domain = domain,
                      accountId = accountId,
                      teamId = teamId,
                      orderId = orderId
                    ))
                }
            }
          }
      }

    } yield {
      orderId
    }
  }


  def createZapMailMailbox(
                            data: ZapMailMailboxDetailsForQueue
                          )
                          (implicit ec: ExecutionContext, ws: WSClient, logger: SRLogger): Future[Boolean] = {

    val accountOpt = accountService.find(id = data.accountId.id)

    accountOpt match {

      case Failure(exception) => {
        logger.shouldNeverHappen(s"Account not found for account id ${data.accountId.id} [createZapMailMailbox] while creating email: ${data.mailboxDetails.username}@${data.domain}")
        Future.failed(new Exception(s"Account not found account id ${data.accountId.id}",exception))
      }

      case Success(account:Account) =>
        val teamMember = Helpers.getTeamMemberFromAccount(
          account = account,
          team_id = data.teamId
        )

        val isOauthFlow = account.org.org_metadata.enable_oauth_for_zapmail.getOrElse(false)

        if (!isOauthFlow) {
          val normalizedEmailAccount = s"${data.mailboxDetails.username.trim.toLowerCase}@${data.domain}"
          emailAccountService.createEmail(
            data = EmailSettingForm(
              email = normalizedEmailAccount,
              service_provider = EmailServiceProvider.GMAIL_ASP,
              smtp_username = Some(normalizedEmailAccount),
              smtp_password = Some(""),
              smtp_host = Some(EmailAccountService.GMAIL_SMTP_HOST),
              smtp_port = Some(EmailAccountService.GMAIL_SMTP_PORT),
              imap_username = Some(normalizedEmailAccount),
              imap_password = Some(""),
              imap_host = Some(EmailAccountService.GMAIL_IMAP_HOST),
              imap_port = Some(EmailAccountService.GMAIL_IMAP_PORT),
              email_domain = Some(data.domain),
              api_key = None,
              mailgun_region = None,
              quota_per_day = 100,
              min_delay_seconds = EmailServiceProvider.GMAIL_ASP.min_delay_seconds,
              max_delay_seconds = EmailServiceProvider.GMAIL_ASP.max_delay_seconds,
              can_send = true,
              can_receive = true,
              cc_emails = None,
              bcc_emails = None,
              provided_by = EmailProvidedBy.ZapMail,
              first_name = data.mailboxDetails.firstName,
              last_name = data.mailboxDetails.lastName,
              status = EmailSettingStatus.InActive,
              platform_email_id = Some(data.orderId.toString),
              email_tag = None
            ),
            account = account,
            ta = teamMember
          ) match {
            case Right(_) =>
              logger.info(s"Successfully created email setting [handleZapMailOnMailBoxCreatedWebhook] for email: ${normalizedEmailAccount}")
              Future.successful(true)
            case Left(error) =>
              logger.criticalSmsAlert(s"Error occurred [handleZapMailOnMailBoxCreatedWebhook] while creating email setting for email: ${normalizedEmailAccount}, on Handling Webhook for ZapMail: mailbox.success Error: ${error}")
              //                      throw new Exception(s"Email creation failed: ${error}")
              Future.successful(false)
          }
        } else {
          logger.info(s"Skipping email setting creation for email: ${data.mailboxDetails.username.trim.toLowerCase}@${data.domain} as it is an oauth flow")
          Future.successful(true)
        }
    }
  }


  def purchaseAdditionalZapMailMailboxs(
                                         account: Account,
                                         teamId: TeamId,
                                         domain: String,
                                         workspaceType: ZapMailWorkspaceType,
                                         mailboxes: List[ZapMailMailboxRequestDetails] // Domain Name -> List of Mailbox Details
                            )(implicit ec: ExecutionContext, ws: WSClient, logger: SRLogger): Future[ZapMailOrderID] = {

    logger.info(
      s"Purchase ZapMail Domains Service [purchaseZapMailDomains] : mailboxes :: ${mailboxes}"
    )

    val accountId = AccountId(account.internal_id)
    val isOauthFlow = account.org.org_metadata.enable_oauth_for_zapmail.getOrElse(false)

    for {
      hasAddonLimit: Boolean <- Future.fromTry(planLimitService.checkEmailsAndDomainsAddonLimitReached(
        toBePurchasedEmailQty = mailboxes.size,
        toBePurchasedDomainQty = 0,
        platformType = PlatformType.ZAPMAIL,
        teamId = teamId
      ))

      _: Boolean <- if (hasAddonLimit) {
        Future.failed(new Exception("Addon limit reached"))
      } else {
        Future.successful(true)
      }

      subscriptionId: ZapMailSubscriptionID <- Future.fromTry(emailInfraDAO.getSubscriptionIdForZapMailDomain(
        domain = domain
      ))

      googleOAuthConfig: Option[ZapMailGoogleOAuthConfig] <- if (isOauthFlow && workspaceType == ZapMailWorkspaceType.Google) {
        getZapMailGoogleOAuthConfig(
          account = account,
          teamId = teamId
        ).map(Some(_))
      } else {
        Future.successful(None)
      }


      microsoftOauthRoute: Option[String] <- if (workspaceType == ZapMailWorkspaceType.Microsoft) {
        Future.fromTry(getZapMailMicrosoftOAuthUrl(
          account = account,
          teamId = teamId
        ).map(Some(_)))
      } else {
        Future.successful(None)
      }

      orderId: ZapMailOrderID <- zapMailApi.createAdditionalMailboxOrder(
        subscriptionId = subscriptionId,
        userId = ZapMailUserUID(
          uid = AppConfig.ZapMail.userId
        ),
        mailboxes = mailboxes,
        domain = domain,
        googleOauthConfig = googleOAuthConfig,
        microsoftOauthRoute = microsoftOauthRoute
      )

      orderDetails: List[ZapMailOrderItem] <- zapMailApi.getOrderDetails(
        orderId = orderId
      )

      _ <- Future {
        // TODO: save this in audit logs
        logger.info(
          s"Details of the order with order id : ${orderId.toString} is ${orderDetails}"
        )
      }

      // This is where , we do actual purchase
      orderStatus: Boolean <- zapMailApi.processOrder(
        orderId = orderId
      )

      savedUuid: String <- Future.fromTry(
        emailInfraDAO.addAdditionalPurchasedEmailsForZapMail(
          teamId = teamId,
          accountId = accountId,
          domain = domain,
          emails = mailboxes.map(
            mailboxDetails =>
              PurchaseEmail.ZapMailWithoutPassword(
                email_account = s"${mailboxDetails.username.trim.toLowerCase}@$domain",
                first_name = mailboxDetails.firstName,
                last_name = mailboxDetails.lastName
              )
          )
        )
      )

      _ <- Future {
        if (!isOauthFlow) {
          Try {
            mailboxes.map { mailboxDetails =>
              mqZapmailEmailAccountCreationPublisher.publishMessage(
                msg = ZapMailMailboxDetailsForQueue(mailboxDetails = mailboxDetails,
                  domain = domain,
                  accountId = accountId,
                  teamId = teamId,
                  orderId = orderId
                ))
            }
          }
        }
      }

    } yield {
      orderId
    }
  }

  /**
   * Helper method to extract TLD from a domain name if present
   * @param domainName The domain name that might contain a TLD
   * @return A tuple of (baseDomain, Option[TLD])
   */
  def extractTldFromDomainName(domainName: String): (String, Option[ZapMailTLD]) = {
    val lastDotIndex = domainName.lastIndexOf('.')
    if (lastDotIndex > 0 && lastDotIndex < domainName.length - 1) {
      val potentialTld = domainName.substring(lastDotIndex + 1)
      val baseDomain = domainName.substring(0, lastDotIndex)
      (baseDomain, ZapMailTLD.fromString(potentialTld))
    } else {
      (domainName, None)
    }
  }

  def searchDomainsAndCheckWorkspace(request: ZapMailDomainSearchRequest)(implicit ec: ExecutionContext, ws: WSClient, logger: SRLogger): Future[List[ZapMailAvailableDomain]] = {

    // Check if the domain name contains a TLD
    val (baseDomain, extractedTldOpt) = extractTldFromDomainName(request.domainName)

    // Create a new request with the correct format
    val updatedRequest = extractedTldOpt match {
      case Some(tld) =>
        // If we found a valid TLD in the domain name, use the base domain and add the TLD to the list
        logger.info(s"Found TLD ${tld.asString} in domain name ${request.domainName}, using base domain $baseDomain")
        request.copy(
          domainName = baseDomain,
          tlds = if (request.tlds.contains(tld)) request.tlds else tld :: request.tlds
        )
      case None =>
        // If no valid TLD was found, use the original request
        request.copy(
          domainName = baseDomain
        )
    }

    // Step 1: Search for available domains
    zapMailApi.searchDomains(updatedRequest).flatMap { availableDomains =>
      // Step 2: For each available domain, check if a workspace exists
      val domainChecks: List[Future[Option[ZapMailAvailableDomain]]] = availableDomains.map { domain =>
        if (domain.domainName.contains("gmail")) {
          Future.successful(None)
        } else {
          // Safely convert domain prices to Double with error handling
          val shouldExcludeDueToPricing = try {
            val domainPrice = domain.domainPrice.toDouble
            val renewPrice = domain.renewPrice.toDouble

            domainPrice > AppConfig.ZapMail.minDomainPriceToDisplay ||
              renewPrice > AppConfig.ZapMail.minDomainRenewPriceToDisplay
          } catch {
            case e: NumberFormatException =>
              logger.criticalSmsAlert(s"Failed to convert price to Double for domain ${domain.domainName}. Domain price: ${domain.domainPrice}, Renew price: ${domain.renewPrice}, status: ${domain.status}")
              true
          }

          if (shouldExcludeDueToPricing) {
            Future.successful(None)
          } else {
            zapMailApi.canCreateWorkspace(domain.domainName).map { workspaceExists =>
              // Step 3: Only include the domain if workspaceExists is true
              if (workspaceExists) Some(domain) else None
            }
          }
        }
      }

      // Step 4: Combine all the futures into a single future of a list
      Future.sequence(domainChecks).map { domainsWithWorkspaceStatus =>
        // Step 5: Flatten the list to remove None values and keep only domains with workspaceExists = true
        domainsWithWorkspaceStatus.flatten
      }
    }
  }

  def zapMailFormatMailboxCreatedWebhook(requestBody: JsValue)(using logger: SRLogger): Either[Exception, MailboxCreatedWebhook] = {
    ZapMailAPI.formatMailboxCreatedWebhook(requestBody = requestBody)
  }

  def zapMailFormatMailboxOrderSuccessWebhook(requestBody: JsValue)(using logger: SRLogger): Either[Exception, MailboxOrderSuccessWebhook] = {
    ZapMailAPI.formatMailboxOrderSuccessWebhook(requestBody = requestBody)
  }

  def zapMailFormatMailboxOrderFailedWebhook(requestBody: JsValue)(using logger: SRLogger): Either[Exception, MailboxOrderFailedWebhook] = {
    ZapMailAPI.formatMailboxOrderFailedWebhook(requestBody = requestBody)
  }

  def zapMailFormatDomainOrderSuccessWebhook(requestBody: JsValue)(using logger: SRLogger): Either[Exception, DomainOrderSuccessWebhook] = {
    ZapMailAPI.formatDomainOrderSuccessWebhook(requestBody = requestBody)
  }

  def zapMailFormatDomainOrderFailedWebhook(requestBody: JsValue)(using logger: SRLogger): Either[Exception, DomainOrderFailedWebhook] = {
    ZapMailAPI.formatDomainOrderFailedWebhook(requestBody = requestBody)
  }

  def handleZapMailMailboxCreatedWebhook(webhookData: MailboxCreatedWebhookData)(
    implicit logger: SRLogger,
    ec: ExecutionContext,
    ws: WSClient
  ): Try[Boolean] = {

    // had made this change to hide the password
    logger.info(s"[ZapMail] EmailInfraService.handleZapMailMailboxCreatedWebhook webhookData: ${webhookData.mailboxDetails.copy(appPassword = Some(""))}")

    val domain = webhookData.domain
    val subscriptionId = ZapMailSubscriptionID(uid=webhookData.subscriptionId)
    val mailboxDetails = webhookData.mailboxDetails

    val normalizedEmailAccount = mailboxDetails.email.toLowerCase()

    logger.info(s"[ZapMail] EmailInfraService.handleZapMailMailboxCreatedWebhook normalizedEmailAccount: ${normalizedEmailAccount}")

    val result = for {

      existingPurchasedEmails: List[PurchaseEmail] <- emailInfraDAO.getPurchasedEmailsFromDomain(
        domain = domain
      )

      _ <- {
        val matchingEmails = PurchaseEmail
          .filterWithoutPasswordList(existingPurchasedEmails)
          .filter(  _.email_account.toLowerCase() == mailboxDetails.email.toLowerCase())
        if (matchingEmails.length != 1) {
          Failure(new Exception(s"Expected exactly one matching email, found ${matchingEmails.size} for email: ${mailboxDetails.email} in purchased Emails"))
        } else {
          Success(true)
        }
      }

      updatedPurchasedEmails: List[PurchaseEmail] = existingPurchasedEmails.map {
        case item: PurchaseEmail.ZapMailWithoutPassword =>
          if (item.email_account.toLowerCase.trim == mailboxDetails.email.toLowerCase.trim)
            MailboxCreatedWebhookData.convertToZapMailPurchaseEmail(
              data = webhookData.copy(
                mailboxDetails = mailboxDetails.copy(
                  appPassword = mailboxDetails.appPassword.map(EncryptionHelpers.encryptEmailSettingCredential)
                )
              )
            )
          else item
        case item : PurchaseEmail.ZapMailWithPassword =>
          item
        case item : PurchaseEmail.Maildoso =>
          item
      }

      (accountId: AccountId, teamId: TeamId) <- emailInfraDAO.updateZapMailDomainToActive(
        finalPurchaseEmails = updatedPurchasedEmails,
        domain = domain,
        subscriptionId = subscriptionId
      )

      account: Account <- accountService.find(id = accountId.id)

      // Create email account
      emailCreationStatus:Int <- if (account.org.org_metadata.enable_oauth_for_zapmail.getOrElse(false) || webhookData.workspaceType==ZapMailWorkspaceType.Microsoft) {
        /*
          * We are not performing the updation of email account from inactive to active as it is the oauth flow
            for which we haven't created the inactive email before [which we will do in app password flow]
          * The email would be directly created from the oauth url we sent to them
          * Althrough we will store the details of email, password, subscription Id and other info inside the purchased_emails column of purchased_domains table
        */
        Success(1)
      } else {
        emailSettingDAO.updateZapMailEmailSettingStatus(
          worker_task_id = webhookData.orderId,
          teamId = teamId,
          status = EmailSettingStatus.Active,
          email = normalizedEmailAccount,
          new_password = mailboxDetails.appPassword.get, // This won't be None as its ASP flow
          subscriptionId = subscriptionId
        )
      }
    } yield true

    result.recover {
      case exception =>
        logger.criticalSmsAlert(s"Unexpected error [handleZapMailOnMailBoxCreatedWebhook] processing webhook for ZapMail: mailbox.success for email: ${normalizedEmailAccount}: ${exception.getMessage}")
        throw exception
    }
  }


  def handleZapMailMailboxOrderSuccessWebhook(webhookData: MailboxOrderSuccessWebhookData)(using logger: SRLogger): Either[Exception, Int] = {
    emailInfraDAO.handleZapMailMailboxSuccessWebhook(
      subscriptionId = ZapMailSubscriptionID(webhookData.subscriptionId),
      orderId = webhookData.orderId
    ) match {
      case Failure(exception) =>
        logger.criticalSmsAlert(
          s"SQL error in emailInfraDAO.handleZapMailMailboxOrderSuccessWebhook : ${exception.getMessage}"
        )
        Left(exception.asInstanceOf[Exception])
      case Success(value) => Right(value)
    }
  }

  def handleZapMailMailboxOrderFailedWebhook(webhookData: MailboxOrderFailedWebhookData)(using logger: SRLogger): Either[Exception, Int] = {

    logger.criticalSmsAlert(
      s"""ZapMail Mailbox order  failed after successful purchase of domains [This need to check with ZapMail]  [Currently marking domain purchase as failure] : webhookData: ${webhookData} """
    )

    emailInfraDAO.handleZapMailMailboxFailureWebhook(
      orderId = webhookData.orderId
    ) match {
      case Failure(exception) =>
        logger.criticalSmsAlert(
          s"SQL error in emailInfraDAO.handleZapMailMailboxFailureWebhook: ${exception.getMessage}"
        )
        Left(exception.asInstanceOf[Exception])
      case Success(value) => Right(value)
    }
  }

  def handleZapMailDomainOrderSuccessWebhook(webhookData: DomainOrderSuccessWebhookData)
                                            (
                                              implicit logger: SRLogger,
                                              ec: ExecutionContext,
                                              ws: WSClient
                                            ): Future[Boolean] = {

    logger.info(s"handleZapMailDomainOrderSuccessWebhook webhookData: ${webhookData}")

    for {

      _: Int <- Future.fromTry(emailInfraDAO.handleZapMailDomainSuccessWebhook(
        webhookData = webhookData
      ))

      domain_redirect_url: String <- Future.fromTry(emailInfraDAO.fetchDomainRedirectUrlForDomain(
        domainName = webhookData.domainName,
        orderId = webhookData.orderId
      ))

      forwardStatus: Boolean <- zapMailApi.addDomainForwarding(
        domains = List(webhookData.domainName),
        forwardTo = domain_redirect_url,
        maskForwarding = false
      )

    } yield {
      forwardStatus
    }

  }

  def handleZapMailDomainOrderFailedWebhook(webhookData: DomainOrderFailedWebhookData)(using logger: SRLogger): Either[Exception, Int] = {
    emailInfraDAO.handleZapMailDomainFailureWebhook(
      orderId = webhookData.orderId
    ) match {
      case Failure(exception) =>
        logger.criticalSmsAlert(
          s"SQL error in emailInfraDAO.handleZapMailDomainOrderFailedWebhook: ${exception.getMessage}"
        )
        Left(exception.asInstanceOf[Exception])
      case Success(value) => Right(value)
    }
  }

  def saveDomainDetails(
                         purchasedDomains: List[PurchasedDomainForm],
                         teamId: TeamId,
                         accountId: AccountId
                       )(implicit ec: ExecutionContext, ws: WSClient, logger: SRLogger): Future[Either[SaveDomainError, SaveDomainsResponseWithPaymentLink]] = {

    val domains_to_purchase = purchasedDomains.length
    val emails_to_purchase = purchasedDomains.flatMap(_.purchasedEmails).length

    planLimitService.checkEmailsAndDomainsAddonLimitReached(
      toBePurchasedEmailQty = emails_to_purchase,
      toBePurchasedDomainQty = domains_to_purchase,
      platformType = PlatformType.MAILDOSO, // TODO: check
      teamId = teamId
    ) match {
      case Success(_) =>

        val purchaseDomainsWithCorrectPasswords: List[PurchasedDomainForm] = purchasedDomains.map(purchasedDomain => {
          purchasedDomain.copy(purchasedEmails = purchasedDomain.purchasedEmails.map(email => {
            email.copy(email_account = email.email_account.toLowerCase().trim, password = Helpers.generateEmailPassword())
          }))
        })

        val domainsToBePurchased: List[String] = purchaseDomainsWithCorrectPasswords.map(_.domainName)

        val redirect_to: List[String] = purchaseDomainsWithCorrectPasswords
          .find(_.domainRedirectTo.isDefined)
          .map(_.domainRedirectTo.get)
          .toList

        val createDomainRequest: CreateDomainRequest = CreateDomainRequest(
          user_id = AppConfig.Maildoso.maildoso_user_id,
          domains = domainsToBePurchased,
          redirect_to = redirect_to
        )

        val res: Future[Either[AddonLimitReachedForDomainPurchase, SaveDomainsResponseWithPaymentLink]] = for {

          purchasedDomainsResponse: PurchaseDomainResponse <- mailDosoService.purchaseDomains(createDomainRequest)

          saveToDb: List[PurchasedDomain] = purchasedDomainsResponse.results
            .filter(_.result.contains(PurchaseDomainResponseResultType.SUCCESS))
            .map(pD => {

              val domainEmails: List[PurchaseEmail.Maildoso] =
                purchaseDomainsWithCorrectPasswords
                  .find(d => d.domainName == pD.name)
                  .map(_.purchasedEmails)
                  .getOrElse(List())

              PurchasedDomain(
                domainName = pD.name,
                purchasedEmails = domainEmails,
                domainRedirectTo = redirect_to.headOption,
                platformDomainId = pD.id.map(_.toString),
                platformDomainPurchasedTaskId = purchasedDomainsResponse.task_id
              )
            })

          savedDomains <- emailInfraDAO.savePurchaseDomains(
            purchasedDomains = saveToDb,
            teamId = teamId,
            accountId = accountId,
            workspaceType = EmailInfraWorkspaceType.SMTP
          ) match {
            case Success(domains) =>
              logger.info(s"[Maildoso] saveDomainDetails savedDomains: ${domains} team_id: ${teamId} :: account_id: ${accountId}")
              Future.successful(Right(SaveDomainsResponseWithPaymentLink(
                domains_saved_for_purchase = domains,
                payment_link = "https://www.2cents.com/make-payment"
              )))

            case Failure(e) =>
              logger.fatal(s"[Maildoso] team_id: ${teamId} :: account_id: ${accountId} saveDomainDetails failed", e)
              Future.failed(e)
          }

        } yield {
          savedDomains
        }

        res

      case Failure(e: AddonLimitReachedException) =>  Future.successful(Left(AddonLimitReachedForDomainPurchase(msg = e.getMessage)))

      case Failure(e) =>
        logger.fatal(s"[Maildoso] team_id: ${teamId} :: account_id: ${accountId} planLimitService.checkEmailsAndDomainsAddonLimitReached failed", e)
        Future.failed(e)

    }
  }


  def fetchTaskIdsAndCheckStatus()(implicit ec: ExecutionContext, ws: WSClient, system: ActorSystem, Logger: SRLogger): Future[List[Int]] = {

    // println("Calling fetchWorkerTaskIdsToCheckStatus()")

    def delayedCall[T](delay: FiniteDuration)(block: => Future[T]): Future[T] = {
      after(delay, system.scheduler)(block)
    }

    for {
      task_ids_and_type: List[PurchaseDomainsAndEmailsTaskIdAndType] <- Future.fromTry(fetchWorkerTaskIdsToCheckStatus())

      check_task_status: Seq[Int] <- {
        // println(s"Received task ids and type as $task_ids_and_type")

        task_ids_and_type.foldLeft(Future.successful(Seq.empty[Int])) { (acc, task_and_type) =>
          acc.flatMap { results =>
            delayedCall(1.second) {
              // println(s"Checking task with id ${task_and_type._1} and type ${task_and_type._2}")
              checkTaskIdStatus(
                worker_task_id = task_and_type.taskId,
                worker_task_type = task_and_type.purchaseType,
                teamId = task_and_type.teamId
              ).map(results :+ _)
            }
          }
        }
      }
    } yield check_task_status.toList
  }


  def checkTaskIdStatus(
                         worker_task_id: String,
                         worker_task_type: PurchaseType,
                         teamId: TeamId
                       )(implicit ec: ExecutionContext, ws: WSClient, Logger: SRLogger) = {

    for {

      check_state_of_task: TaskStateResponse <- mailDosoService.getTaskState(
        task_id = worker_task_id
      )

      update_the_task_status_in_db: Int <- check_state_of_task.task_state match {

        case MaildosoWorkerTaskState.SUCCESS =>

          Future.fromTry {

            worker_task_type match {

              case PurchaseType.DOMAIN =>

                val update_domain_task_status = emailInfraDAO.updatePurchasedDomainsTaskStatusInDbToActive(
                  worker_task_id,
                  team_id = teamId
                )

                update_domain_task_status

              case PurchaseType.EMAIL =>

                val update_email_task_status = emailSettingDAO.updateEmailSettingStatus(
                  worker_task_id = worker_task_id,
                  teamId = teamId,
                  status = EmailSettingStatus.Active
                )

                update_email_task_status

            }
          }

        case MaildosoWorkerTaskState.OTHER(task_state) =>
          Logger.info(s"EmailInfraService.checkTaskIdStatus task not successful task_state: $task_state :: worker_task_id: $worker_task_id :: worker_task_type: $worker_task_type :: team_id: ${teamId.id}")
          Future.successful(0)
      }


    } yield {

      update_the_task_status_in_db

    }
  }

  def fetchWorkerTaskIdsToCheckStatus(): Try[List[PurchaseDomainsAndEmailsTaskIdAndType]] = {
    emailInfraDAO.fetchWorkerTaskIdsToCheckStatus()
  }

  // Currently Not in use
  def fetchAndPurchaseDomains()(implicit ec: ExecutionContext, ws: WSClient, Logger: SRLogger): Future[Try[List[PurchaseDomainResponse]]] = {
    Future {
      fetchDomainsToPurchase()
    }.flatMap {
      case Success(domainsToSetup) =>
        val groupedDomains: Map[TeamId, List[PurchasedDomainToSetup]] = domainsToSetup.groupBy(_.teamId)
        val purchaseFutures: List[Future[PurchaseDomainResponse]] = groupedDomains.map { case (teamId, domains) =>
          val createDomainRequest = CreateDomainRequest(
            domains = domains.map(_.domainName),
            user_id = AppConfig.Maildoso.maildoso_user_id,
            redirect_to = List(domains.map(_.domainRedirectTo).head) // only single can be passed
          )
          mailDosoService.purchaseDomains(createDomainRequest).flatMap { purchaseDomainResponse =>
            val purchasedDomains: List[(String, PurchasedDomainToSetup)] = domains.map(domain => (domain.purchasedDomainUuid, domain))
            Future.fromTry(emailInfraDAO.savePurchaseDomainsTaskId(
              purchasedDomainsUuid = purchasedDomains.map(d => PurchasedDomainsUuid(d._2.purchasedDomainUuid)),
              purchasedDomains = purchasedDomains.map(_._2.domainName),
              task_id = purchaseDomainResponse.task_id,
              teamId = teamId,
              accountId = domains.head.accountId
            ).map(_ => {
              Logger.info(s"[Maildoso] savePurchaseDomainsTaskId saved task id: ${purchaseDomainResponse.task_id}")
              purchaseDomainResponse
            }))
          }
        }.toList
        Future.sequence(purchaseFutures).map(Success(_))

      case Failure(exception) =>
        Logger.fatal(s"[Maildoso] fetchAndPurchaseDomains failed", exception)
        Future.successful(Failure(exception))
    }
  }

  def getPurchasedDomainsAndEmails(account_id: AccountId, team_id: TeamId): Try[List[PurchasedDomain]] = {
    emailInfraDAO.getPurchasedDomainsAndEmails(account_id, team_id)
  }

  def fetchDomainsToPurchase(): Try[List[PurchasedDomainToSetup]] = {
    emailInfraDAO.fetchDomainsToSetup()
  }

  def fetchAndPurchaseEmails()(implicit ec: ExecutionContext, ws: WSClient, logger: SRLogger): Future[Seq[PurchasedEmailsAndSaveData]] = {

      fetchEmailsToPurchase() match {
        case Success(emailsToSetup) =>

          purchaseAndSaveEmailAccounts(
            emailsToSetup = emailsToSetup,
            isPurchasingInitialEmails = true
          )

        case Failure(exception) =>

          logger.fatal(s"[Maildoso] EmailInfraService.fetchAndPurchaseEmails exception:, error:  ${LogHelpers.getStackTraceAsString(exception)}", exception)

          Future.failed(exception)
    }
  }

  def testAppEmailInsert(
                          emailResponse: EmailAccountResult,
                          domain_name: String,
                          team_id: TeamId,
                          account_id : AccountId
                        )(implicit ec: ExecutionContext, ws: WSClient, logger: SRLogger): Try[Option[EmailSetting]] = {
      Try {
        val account = accountService.find(id = account_id.id).get
        val teamMember = Helpers.getTeamMemberFromAccount(
          account = account,
          team_id = team_id
        )

        emailAccountService.createEmail(
          data = EmailSettingForm(
            email = emailResponse.email_account,
            service_provider = EmailServiceProvider.OTHER,

            smtp_username = Some(emailResponse.email_account),
            smtp_password = Some(emailResponse.password),
            smtp_host = Some(AppConfig.Maildoso.SMTP_HOST),
            smtp_port = Some(AppConfig.Maildoso.SMTP_PORT),

            imap_username = Some(emailResponse.email_account),
            imap_password = Some(emailResponse.password),
            imap_host = Some(emailResponse.imap.imap_host),
            imap_port = Some(emailResponse.imap.port),

            email_domain = Some(domain_name),
            api_key = None,
            mailgun_region = None,

            quota_per_day = 100,
            min_delay_seconds = EmailServiceProvider.OTHER.min_delay_seconds,
            max_delay_seconds = EmailServiceProvider.OTHER.max_delay_seconds,

            can_send = true,
            can_receive = true,

            cc_emails = None,
            bcc_emails = None,
            provided_by = EmailProvidedBy.Maildoso,

            first_name = emailResponse.first_name.getOrElse("Unknown"),
            last_name = emailResponse.last_name.getOrElse("Unknown"),
            status = EmailSettingStatus.InActive,

            platform_email_id = Some(emailResponse.id.toString),
            email_tag = None
          ),
          account = account,
          ta = teamMember
          // ... [existing EmailSettingForm configuration remains the same]
        ) match {
          case Right(value) => Some(value)
          case Left(value) =>
            value match {
              case CreateEmailError.FreeDomainUseError =>
                logger.error(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${team_id}  :: value: ${value}")
                None // Return Success with None instead of Failure

              // Handle other error cases similarly
              case CreateEmailError.DuplicateEmailError =>

                logger.error(s"Error occurred while creating email setting for email: ${emailResponse.email_account},  teamId: ${team_id}  :: value: ${value}")
                //                                Failure(new Exception(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id}, value: ${value}"))
                None

              case CreateEmailError.MultipleIntegrationAttemptError =>

                logger.error(s"Error occurred while creating email setting for email: ${emailResponse.email_account},  teamId: ${team_id}  :: value: ${value}")
                //                                Failure(new Exception(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id}, value: ${value}"))
                None
              case CreateEmailError.EmailNotAddedError =>

                logger.error(s"Error occurred while creating email setting for email: ${emailResponse.email_account},  teamId: ${team_id}  :: value: ${value}")
                //                                Failure(new Exception(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id}, value: ${value}"))
                None

              case CreateEmailError.SQLException(msg, err) =>

                logger.error(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${team_id}  :: value: ${value} :: msg: ${msg} :: error: ${LogHelpers.getStackTraceAsString(err)}")
                //                                Failure(new Exception(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id}, value: ${value}"))
                None
              case CreateEmailError.FeatureUsageServiceError(err) =>

                logger.error(s"Error occurred while creating email setting for email: ${emailResponse.email_account},  teamId: ${team_id}  :: value: ${value} :: error: ${LogHelpers.getStackTraceAsString(err)}")
                //                                Failure(new Exception(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id}, value: ${value}"))
                None

              case CreateEmailError.EmailValidationError(err) =>

                logger.error(s"Error occurred while creating email setting for email: ${emailResponse.email_account},  teamId: ${team_id}  :: value: ${value} :: error: ${LogHelpers.getStackTraceAsString(err)}")
                //                                Failure(new Exception(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id}, value: ${value}"))
                None

            }
        }
      }
  }

  def purchaseAndSaveEmailAccounts(
                                    emailsToSetup: List[PurchaseEmailsToSetup],
                                    isPurchasingInitialEmails: Boolean
                                  )(implicit ec: ExecutionContext, ws: WSClient, logger: SRLogger): Future[Seq[PurchasedEmailsAndSaveData]] = {
    logger.info(s"[Maildoso] EmailInfraService.purchaseAndSaveEmailAccounts emailsToSetup: ${emailsToSetup}")
    val groupedEmails = emailsToSetup.map(
      em => em.copy(
        purchasedEmails = em.purchasedEmails.map(
          pe => pe.copy(email_account = pe.email_account.toLowerCase())
        )
      )).groupBy(_.teamId)

    val purchaseFutures: Seq[Future[PurchasedEmailsAndSaveData]] = groupedEmails.toList.map { case (groupedByTeamId, emails) =>

      logger.info(s"[Maildoso] EmailInfraService.purchaseAndSaveEmailAccounts emails: ${emails}")

      val createEmailAccountRequest = emails.flatMap(_.purchasedEmails.map(em => CreateEmailAccountRequest(
        email_account = em.email_account,
        first_name = em.first_name,
        last_name = em.last_name,
        password = Some(em.password),
        is_active = true,
        forwarding_account_id = None,
        skip_sequence = None
      )))

      mailDosoService.createEmailAccount(createEmailAccountRequest)
        .recover { case exception =>
          logger.error(s"Failed to create email account for teamId: ${groupedByTeamId.id}, Error: ${LogHelpers.getStackTraceAsString(exception)}")
          CreateEmailAccountResponse(results = List(), task_id = None) // Return empty response instead of failing
        }
        .flatMap { createEmailAccountResponse =>

          if(isPurchasingInitialEmails) {

            val purchasedDomainsUuids: List[PurchasedDomainsUuid] = emails.map(_.purchasedDomainUuid).map(a => PurchasedDomainsUuid(a))

            emailInfraDAO.savePurchaseEmailsTaskId(
              purchasedDomainsUuid = purchasedDomainsUuids,
              teamId = groupedByTeamId
            )

          }

          logger.info(s"[EmailInfraService.purchaseAndSaveEmailAccounts] account_id: ${emailsToSetup.headOption.map(_.accountId.id)} :: team_id: ${emailsToSetup.headOption.map(_.teamId.id)} :: createEmailAccountResponse: ${createEmailAccountResponse}")

          // Process each email response and collect successful results
          val processedResults = createEmailAccountResponse.results.map { emailResponse =>
            try {
              emails.find(_.purchasedEmails.exists(_.email_account.toLowerCase() == emailResponse.email_account.toLowerCase())) match {
                case Some(email) =>
                  try {
                    val account = accountService.find(id = email.accountId.id).get

                    val teamMember = Helpers.getTeamMemberFromAccount(
                      account = account,
                      team_id = groupedByTeamId
                    )

                    emailAccountService.createEmail(
                      data = EmailSettingForm(
                        email = emailResponse.email_account,
                        service_provider = EmailServiceProvider.OTHER,

                        smtp_username = Some(emailResponse.email_account),
                        smtp_password = Some(emailResponse.password),
                        smtp_host = Some(AppConfig.Maildoso.SMTP_HOST),
                        smtp_port = Some(AppConfig.Maildoso.SMTP_PORT),

                        imap_username = Some(emailResponse.email_account),
                        imap_password = Some(emailResponse.password),
                        imap_host = Some(emailResponse.imap.imap_host),
                        imap_port = Some(emailResponse.imap.port),

                        email_domain = Some(email.domainName),
                        api_key = None,
                        mailgun_region = None,

                        quota_per_day = 100,
                        min_delay_seconds = EmailServiceProvider.OTHER.min_delay_seconds,
                        max_delay_seconds = EmailServiceProvider.OTHER.max_delay_seconds,

                        can_send = true,
                        can_receive = true,

                        cc_emails = None,
                        bcc_emails = None,
                        provided_by = EmailProvidedBy.Maildoso,

                        first_name = emailResponse.first_name.getOrElse("Unknown"),
                        last_name = emailResponse.last_name.getOrElse("Unknown"),
                        status = EmailSettingStatus.InActive,

                        platform_email_id = Some(emailResponse.id.toString),
                        email_tag = None
                      ),
                      account = account,
                      ta = teamMember
                      // ... [existing EmailSettingForm configuration remains the same]
                    ) match {
                      case Right(value) => Success(Some(value))
                      case Left(value) =>
                        value match {
                          case CreateEmailError.FreeDomainUseError =>
                            logger.error(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id} :: value: ${value}")
                            Success(None) // Return Success with None instead of Failure

                          // Handle other error cases similarly
                          case CreateEmailError.DuplicateEmailError =>

                            logger.error(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id} :: value: ${value}")
                            //                                Failure(new Exception(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id}, value: ${value}"))
                            Success(None)

                          case CreateEmailError.MultipleIntegrationAttemptError =>

                            logger.error(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id} :: value: ${value}")
                            //                                Failure(new Exception(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id}, value: ${value}"))
                            Success(None)
                          case CreateEmailError.EmailNotAddedError =>

                            logger.error(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id} :: value: ${value}")
                            //                                Failure(new Exception(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id}, value: ${value}"))
                            Success(None)

                          case CreateEmailError.SQLException(msg, err) =>

                            logger.error(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id} :: value: ${value} :: msg: ${msg} :: error: ${LogHelpers.getStackTraceAsString(err)}")
                            //                                Failure(new Exception(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id}, value: ${value}"))
                            Success(None)
                          case CreateEmailError.FeatureUsageServiceError(err) =>

                            logger.error(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id} :: value: ${value} :: error: ${LogHelpers.getStackTraceAsString(err)}")
                            //                                Failure(new Exception(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id}, value: ${value}"))
                            Success(None)

                          case CreateEmailError.EmailValidationError(err) =>

                            logger.error(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id} :: value: ${value} :: error: ${LogHelpers.getStackTraceAsString(err)}")
                            //                                Failure(new Exception(s"Error occurred while creating email setting for email: ${emailResponse.email_account}, teamId: ${groupedByTeamId.id}, value: ${value}"))
                            Success(None)

                        }
                    }
                  } catch {
                    case e: Exception =>
                      logger.error(s"Unexpected error processing email ${emailResponse.email_account}: ${e.getMessage}")
                      Success(None)
                  }

                case None =>
                  logger.error(s"Email response ${emailResponse.email_account} does not match any purchased email. :: teamId: ${groupedByTeamId.id}")
                  Success(None)
              }
            } catch {
              case e: Exception =>
                logger.error(s"Unexpected error processing email response: ${e.getMessage}")
                Success(None)
            }
          }

          // Combine all results and proceed with successful ones
          val combinedResults = Helpers.seqTryToTrySeq(processedResults).map(_.flatten)


          combinedResults match {
            case Success(emailsSettingSaveData) =>

              logger.doNotTruncate(s"purchaseAndSaveEmailAccounts :: combinedResults Success  ${emailsSettingSaveData} :: team_id : ${groupedByTeamId.id} task_id : ${createEmailAccountResponse.task_id} ")

              if (createEmailAccountResponse.task_id.nonEmpty) {
                Future.fromTry(emailSettingDAO.updatePurchasedEmailSettingsTaskId(
                  emails = emails.flatMap(_.purchasedEmails.map(_.email_account)),
                  platformTaskId = createEmailAccountResponse.task_id.get,
                  teamId = groupedByTeamId,
                ).map(update_count => {

                  if(update_count == emails.flatMap(_.purchasedEmails.map(_.email_account)).size) {

                    logger.info(s"purchaseAndSaveEmailAccounts :: Update count matched :: update_count : ${update_count} emails : ${emails.flatMap(_.purchasedEmails.map(_.email_account))} :: size : ${emails.flatMap(_.purchasedEmails.map(_.email_account)).size} :: teamId: ${groupedByTeamId.id}")

                  }else{

                    logger.shouldNeverHappen(s"purchaseAndSaveEmailAccounts :: Update count does not match :: update_count :: ${update_count} emails: ${emails.flatMap(_.purchasedEmails.map(_.email_account))} :: size : ${emails.flatMap(_.purchasedEmails.map(_.email_account)).size} :: teamId: ${groupedByTeamId.id}")

                  }

                  PurchasedEmailsAndSaveData(
                    create_email_account_response =  createEmailAccountResponse,
                    saveEmailSettingData = emailsSettingSaveData,
                    teamId = groupedByTeamId,
                    updatedCount = update_count
                  )

                }))
              } else {
                logger.fatal(s"task id is empty createEmailAccountResponse: ${createEmailAccountResponse}, teamId: ${groupedByTeamId.id}")
                Future.successful(PurchasedEmailsAndSaveData(
                  create_email_account_response =  createEmailAccountResponse,
                  saveEmailSettingData = emailsSettingSaveData,
                  teamId = groupedByTeamId,
                  updatedCount = 0
                )) // Continue instead of failing
              }
            case Failure(exception) =>
              logger.shouldNeverHappen(s"Error processing batch: ${exception.getMessage}, groupedByTeamId: ${groupedByTeamId.id}, Error: ${LogHelpers.getStackTraceAsString(exception)}")
              Future.successful(PurchasedEmailsAndSaveData(
                create_email_account_response =  createEmailAccountResponse,
                saveEmailSettingData = List(),
                teamId = groupedByTeamId,
                updatedCount = 0
              )) // Continue instead of failing
          }
        }


    }

    Future.sequence(purchaseFutures)
  }

  def fetchEmailsToPurchase(): Try[List[PurchaseEmailsToSetup]] = {
    emailInfraDAO.fetchEmailsToPurchase()
  }

  def deleteScheduledForDeletionDomains()(using Logger: SRLogger): Future[List[String]] = {

    emailInfraDAO.fetchDomainsForDeletion() match {

      case Failure(exception) =>

        Logger.fatal(s"[Maildoso] fetchDomainsForDeletion failed", exception)

        Future.failed(exception)

      case Success(value) =>

        Logger.info(s"deleteScheduledForDeletionDomains fetchDomainsForDeletion success: found domains for deletion: ${value}")

        val res: Seq[Try[Seq[String]]] = value.groupBy(_.team_id.id).map({ case (teamId, platformDomainIds) => {

          mqPurchasedDomainsDeleter.publish(
            msg = MqPurchasedDomainsDeleterMsg(
              domainUuidsWithPlatformType = platformDomainIds.map(
               domain =>  DomainUuidWithPlatformType(domainUuid = domain.purchasedDomainUuid, platformType = domain.platformType)
              ),
              teamId = TeamId(id = teamId)
            )
          ) match {
            case Failure(exception) =>

              Logger.shouldNeverHappen(s"Failed to publish domain deletion message for teamId: ${teamId} :: purchasedDomainUuids: ${platformDomainIds.map(_.purchasedDomainUuid).mkString(",")}", Some(exception))

            case Success(value) =>

              Logger.info(s"Successfully published domain deletion message for teamId: ${teamId} :: purchasedDomainUuids: ${platformDomainIds.map(_.purchasedDomainUuid).mkString(",")}")


          }

          val account_id_groups = platformDomainIds.groupBy(_.account_id.id).map({ case (accountId, accountBasedPlatformid) => {

            emailInfraDAO.updatePurchasedDomainStatusByPurchaseDomainUuids(
              purchasedDomainUuids = accountBasedPlatformid.map(_.purchasedDomainUuid),
              status = PurchaseDomainsAndEmailsStatus.PUSHED_TO_QUEUE_FOR_DELETION,
              teamId = TeamId(id = teamId),
              accountId = Some(AccountId(id = accountId)),
              scheduled_for_deletion_at = None
            ) match {
              case Failure(exception) =>

                Logger.shouldNeverHappen(s"Failed to update domain deletion status for teamId: ${teamId} :: purchasedDomainUuids: ${accountBasedPlatformid.map(_.purchasedDomainUuid).mkString(",")}", Some(exception))
                Success(List())

              case Success(value) =>

                Logger.info(s"Successfully updated domain deletion status for teamId: ${teamId} :: purchasedDomainUuids: ${accountBasedPlatformid.map(_.purchasedDomainUuid).mkString(",")}")

                Success(value)


            }

          }
          })

          Helpers.seqTryToTrySeq(account_id_groups.toSeq).map(_.flatten)

        }
        }).toList

        Future.fromTry(Helpers.seqTryToTrySeq(res).map(_.flatten).map(_.toList))

    }

  }

  def deleteScheduledForDeletionEmails()(using Logger: SRLogger): Future[Seq[Int]] = {

    emailSettingDAO.fetchEmailsForDeletion() match {

      case Failure(exception) =>

        Logger.shouldNeverHappen(s"[Maildoso] fetchDomainsForDeletion failed", Some(exception))

        Future.failed(exception)

      case Success(value) =>

        Logger.info(s"deleteScheduledForDeletionDomains fetchDomainsForDeletion success: found domains for deletion: ${value}")

        val res = value.map(d => {

          mqPurchasedEmailsDeleter.publish(
            msg = MqPurchasedEmailsDeleterMsg(
              emailSettingId = EmailSettingId(emailSettingId = d.email_setting_id),
              domainPlatformType = d.domain_provider,
              teamId = d.team_id
            )
          ) match {
            case Failure(exception) =>

              Logger.shouldNeverHappen(s"deleteScheduledForDeletionEmails mqPurchasedEmailsDeleter.publish failed team_id: ${d.team_id} em_setting_id: ${d.email_setting_id} ", Some(exception))

              Success(0)


            case Success(value) =>

              Logger.info(s"deleteScheduledForDeletionEmails mqPurchasedEmailsDeleter.publish success team_id: ${d.team_id} em_setting_id: ${d.email_setting_id} ")

              emailSettingDAO.updatePlatformEmailStatusForSettingPushedToMq(
                emailSettingId = EmailSettingId(emailSettingId = d.email_setting_id),
                teamId = d.team_id,
                platform_email_status = PurchaseDomainsAndEmailsStatus.PUSHED_TO_QUEUE_FOR_DELETION
              ) match {

                case Failure(exception) =>

                  Logger.shouldNeverHappen(s"deleteScheduledForDeletionEmails updatePlatformEmailStatusForSettingPushedToMq failed team_id: ${d.team_id} em_setting_id: ${d.email_setting_id} ", Some(exception))

                  Success(0)

                case Success(value) =>

                  Logger.info(s"deleteScheduledForDeletionEmails updatePlatformEmailStatusForSettingPushedToMq success team_id: ${d.team_id} em_setting_id: ${d.email_setting_id} ")

                  Success(value)

              }

          }



        })

        Future.fromTry(Helpers.seqTryToTrySeq(res))

    }

  }

  def scheduleOrgDomainsAndEmailsForDeletionOnCancellation(
                                    org_id: OrgId,
                                    scheduled_for_cancellation_at: DateTime
                                    )(using Logger: SRLogger, ec: ExecutionContext, ws: WSClient): Future[DeletedDomainsAndEmails] = {
    for {
      domains_and_emails: List[OrgPurchasedDomainAndEmailForCancellation] <- Future.fromTry(emailInfraDAO.fetchDomainsAndEmailsForOrg(orgId = org_id))

      deleted_emails: List[Option[EmailSettingId]] <- Future.sequence(
        domains_and_emails
          .filter(_.emailSettingId.nonEmpty)
          .distinctBy(_.emailSettingId.get.emailSettingId)
          .map(d => (d.emailSettingId.get, d.team_id))
          .map { case (emailSetting, teamId) =>
            scheduleEmailsForDeletion(
              emailSettingId = emailSetting,
              teamId = teamId,
              schedule_for_deletion_at = scheduled_for_cancellation_at.minusMinutes(AppConfig.Maildoso.DELETE_DOMAIN_EMAILS_BEFORE_CANCELLATION_MINUTES)
            ).map {
              case Right(value) =>
                Logger.info(s"[EmailInfraService.scheduleOrgDomainsAndEmailsForDeletionOnCancellation] org_id: ${org_id} Successfully deleted email team_id: ${teamId.id} email_setting_id: ${emailSetting}")
                Some(emailSetting)
              case Left(value) =>
                value match {
                  case DeletePurchasedEmailsError.DBFailure(e) =>
                    Logger.shouldNeverHappen(s"[EmailInfraService.scheduleOrgDomainsAndEmailsForDeletionOnCancellation] org_id: ${org_id} Error occurred while deleting team_id: ${teamId.id} email: $emailSetting :: error: ${LogHelpers.getStackTraceAsString(e)}", Some(e))
                    None
                  case DeletePurchasedEmailsError.PublishToMqFailure(e) =>
                    Logger.shouldNeverHappen(s"[EmailInfraService.scheduleOrgDomainsAndEmailsForDeletionOnCancellation] org_id: ${org_id} Error occurred while deleting team_id: ${teamId.id} email: $emailSetting :: error: ${LogHelpers.getStackTraceAsString(e)}", Some(e))
                    None
                  case DeletePurchasedEmailsError.InvalidEmailSettingId =>
                    Logger.shouldNeverHappen(s"[EmailInfraService.scheduleOrgDomainsAndEmailsForDeletionOnCancellation] org_id: ${org_id} Invalid email setting id, team_id: ${teamId.id} setting id: $emailSetting")
                    None
                  case DeletePurchasedEmailsError.NextBillingDateNotFound =>
                    Logger.shouldNeverHappen(s"[EmailInfraService.scheduleOrgDomainsAndEmailsForDeletionOnCancellation] org_id: ${org_id} Nex billing date not found, team_id: ${teamId.id} setting id: $emailSetting")
                    None
                }
            }
          }
      )

      res: List[Option[OrgPurchasedDomainAndEmailForCancellation]] <- Future.sequence(
        domains_and_emails
          .distinctBy(_.purchasedDomainUuid)
          .map { data =>
            Future {
              scheduleDomainsForDeletion(
                purchasedDomainUuids = List(data.purchasedDomainUuid),
                platformType = data.platformType,
                teamId = data.team_id,
                accountId = data.accountId,
                scheduled_for_deletion_at = Some(scheduled_for_cancellation_at.minusMinutes(AppConfig.Maildoso.DELETE_DOMAIN_EMAILS_BEFORE_CANCELLATION_MINUTES)),
                is_cancellation_flow = true
              )
            }.map {
              case Right(value) =>
                Logger.info(s"[EmailInfraService.scheduleOrgDomainsForDeletionOnCancellation] org_id: ${org_id} Successfully deleted domain: ${data.purchasedDomainUuid}")
                Some(data)
              case Left(value) =>
                value match {
                  case DeletePurchasedDomainsError.DBFailure(e) =>
                    Logger.shouldNeverHappen(s"[EmailInfraService.scheduleOrgDomainsForDeletionOnCancellation] org_id: ${org_id} Error occurred while deleting domain: ${data.purchasedDomainUuid} :: error: ${LogHelpers.getStackTraceAsString(e)}", Some(e))
                    None
                  case DeletePurchasedDomainsError.PublishToMqFailure(e) =>
                    Logger.shouldNeverHappen(s"[EmailInfraService.scheduleOrgDomainsForDeletionOnCancellation] org_id: ${org_id} Error occurred while publishing domain: ${data.purchasedDomainUuid} :: error: ${LogHelpers.getStackTraceAsString(e)}", Some(e))
                    None
                  case DeletePurchasedDomainsError.InvalidDomainUuids =>
                    Logger.shouldNeverHappen(s"[EmailInfraService.scheduleOrgDomainsForDeletionOnCancellation] org_id: ${org_id} Invalid domain uuids: ${data.purchasedDomainUuid}")
                    None
                  case DeletePurchasedDomainsError.EmptyDomainUuids =>
                    Logger.shouldNeverHappen(s"[EmailInfraService.scheduleOrgDomainsForDeletionOnCancellation] org_id: ${org_id} Empty domain uuids")
                    None
                  case DeletePurchasedDomainsError.EmailSettingsActive =>
                    Logger.shouldNeverHappen(s"[EmailInfraService.scheduleOrgDomainsForDeletionOnCancellation] org_id: ${org_id} Email settings are active for domain: ${data.purchasedDomainUuid}")
                    None
                  case DeletePurchasedDomainsError.NextBillingDateNotFound =>
                    Logger.shouldNeverHappen(s"[EmailInfraService.scheduleOrgDomainsForDeletionOnCancellation] org_id: ${org_id} Next billing date not found. domain: ${data.purchasedDomainUuid}")
                    None
                }
            }
          }
      )
    } yield {
      DeletedDomainsAndEmails(
        org_id = org_id,
        platform_domain_uuids = res.filter(_.nonEmpty).map(_.get.purchasedDomainUuid),
        platform_email_ids = deleted_emails.filter(_.nonEmpty).map(_.get.emailSettingId)
      )
    }
  }

  def scheduleDomainForDeletionByUser(
                          purchasedDomainUuids: List[String],
                          platformType: PlatformType,
                          teamId: TeamId,
                          accountId: AccountId
                        )(using Logger: SRLogger): Either[DeletePurchasedDomainsError, true] = {

    val scheduled_for_deletion_at = accountService.find(accountId.id)

    scheduled_for_deletion_at match {

      case Success(value) =>

          if(value.org.plan.next_billing_date.isEmpty){

              Logger.shouldNeverHappen(s"scheduleDomainForDeletionByUser :: next_billing_date not found for team_id: ${teamId} and org_id: ${value.org.id} purchasedDomainUuids : ${purchasedDomainUuids}")
              Left(DeletePurchasedDomainsError.NextBillingDateNotFound)

          }
          else {

              scheduleDomainsForDeletion(
                  purchasedDomainUuids = purchasedDomainUuids,
                  teamId = teamId,
                  accountId = accountId,
                  platformType = platformType,
                  scheduled_for_deletion_at = Some(value.org.plan.current_cycle_started_at.plusMonths(1).minusMinutes(AppConfig.Maildoso.DELETE_DOMAIN_EMAILS_BEFORE_CANCELLATION_MINUTES))
              )
          }

      case Failure(exception) =>

        Logger.error(s"Failed to get scheduled_for_deletion_at - deleteSingleDomain.teamId: $teamId accountId: $accountId purchasedDomainUuids: ${purchasedDomainUuids}", exception)

        Left(DeletePurchasedDomainsError.DBFailure(exception))

    }


  }

  private def validateZapmailDomainDeletionByReturningEmailSettingStatus(
                             purchasedDomainUuids: List[String],
                             teamId: TeamId
                           )(using Logger: SRLogger): Either[DeletePurchasedDomainsError, Boolean] = {

    for {
      zapmailOrderIds: List[String] <- emailInfraDAO.getZapmailOrderIdsByUuids(
        purchasedDomainUuids = purchasedDomainUuids,
        teamId = teamId
      ).toEither.left.map(a => {
        Logger.error(s"[validateZapmailDomainDeletionByReturningEmailSettingStatus] Failed to get zapmailOrderIds from purchasedDomainUuids: ${purchasedDomainUuids} :: team_id: ${teamId.id}")
        DeletePurchasedDomainsError.DBFailure(a)})

      _ <- if (zapmailOrderIds.size != purchasedDomainUuids.size) {
        Logger.error(s"[validateZapmailDomainDeletionByReturningEmailSettingStatus] zapmailOrderIds size ${zapmailOrderIds.size} is not equal to purchasedDomainUuids.size ${purchasedDomainUuids.size} :: domain uuids: ${purchasedDomainUuids} :: team_id: ${teamId.id}")
        Left(DeletePurchasedDomainsError.InvalidDomainUuids)
      } else {
        Right({})
      }

      emailSettingIds: List[EmailSettingId] <- emailSettingService.getEmailSettingIdsFromZapmailOrderId(
        zapmailOrderIds = zapmailOrderIds,
        teamId = teamId
      ).toEither.left.map(a => {
        Logger.error(s"[validateMaildosoDomainDeletionByReturningEmailSettingStatus] Failed to get emailSettingIds from zapmailOrderIds: ${zapmailOrderIds} :: team_id: ${teamId.id}")
        DeletePurchasedDomainsError.DBFailure(a)})

    } yield {
      emailSettingIds.nonEmpty
    }

  }

  private def validateMailDosoDomainDeletionByReturningEmailSettingStatus(
                   purchasedDomainUuids: List[String],
                   teamId: TeamId
                 )(using Logger: SRLogger): Either[DeletePurchasedDomainsError, Boolean] = {

    for {
      platformDomainIds: List[String] <- emailInfraDAO.getPlatformDomainIdsByUuids(
        purchasedDomainUuids = purchasedDomainUuids,
        teamId = teamId
      ).toEither.left.map(a => {
        Logger.error(s"[validateMaildosoDomainDeletionByReturningEmailSettingStatus] Failed to get platformDomainIds from purchasedDomainUuids: ${purchasedDomainUuids} :: team_id: ${teamId.id}")
        DeletePurchasedDomainsError.DBFailure(a)})

      _ <- if (platformDomainIds.size != purchasedDomainUuids.size) {
        Logger.error(s"[validateMaildosoDomainDeletionByReturningEmailSettingStatus] platformDomainIds size ${platformDomainIds.size} is not equal to purchasedDomainUuids.size ${purchasedDomainUuids.size} :: domain uuids: ${purchasedDomainUuids} :: team_id: ${teamId.id}")
        Left(DeletePurchasedDomainsError.InvalidDomainUuids)
      } else {
        Right({})
      }

      emailSettingIds: List[EmailSettingId] <- emailSettingService.getEmailSettingIdsFromPlatformDomainIds(
        platformDomainIds = platformDomainIds,
        teamId = teamId
      ).toEither.left.map(a => {
        Logger.error(s"[validateMaildosoDomainDeletionByReturningEmailSettingStatus] Failed to get emailSettingIds from platformDomainIds: ${platformDomainIds} :: team_id: ${teamId.id}")
        DeletePurchasedDomainsError.DBFailure(a)
      })

    } yield {
      emailSettingIds.nonEmpty
    }

  }

  def scheduleDomainsForDeletion(
                     purchasedDomainUuids: List[String],
                     teamId: TeamId,
                     accountId: AccountId,
                     platformType: PlatformType,
                     scheduled_for_deletion_at: Option[DateTime],
                     is_cancellation_flow: Boolean = false
                   )(using Logger: SRLogger): Either[DeletePurchasedDomainsError, true] = {

    if(purchasedDomainUuids.isEmpty) {
      Left(DeletePurchasedDomainsError.EmptyDomainUuids)
    } else {

      for {
        doValidateAndGetHasEmailsExists <-
          if (platformType == PlatformType.ZAPMAIL) {
            validateZapmailDomainDeletionByReturningEmailSettingStatus(
              purchasedDomainUuids = purchasedDomainUuids,
              teamId = teamId
            )
          } else {
            validateMailDosoDomainDeletionByReturningEmailSettingStatus(
              purchasedDomainUuids = purchasedDomainUuids,
              teamId = teamId
            )
          }


        // we are doing this because some if email settings are already present, we are not going to delete them
        // instead we will ask user to delete email settings first and then delete the domains
        _:Unit <- if (!is_cancellation_flow && doValidateAndGetHasEmailsExists) {
          Logger.error(s"[scheduleDomainsForDeletion] Email settings are active for domain: ${purchasedDomainUuids} :: team_id: ${teamId.id}")
          Left(DeletePurchasedDomainsError.EmailSettingsActive)
        } else {
          Right({})
        }

        _:List[String] <- emailInfraDAO.updatePurchasedDomainStatusByPurchaseDomainUuids(
          purchasedDomainUuids = purchasedDomainUuids,
          status = PurchaseDomainsAndEmailsStatus.SCHEDULED_FOR_DELETION,
          teamId = teamId,
          accountId = Some(accountId),
          scheduled_for_deletion_at = scheduled_for_deletion_at
        ).toEither.left.map(e =>{
          DeletePurchasedDomainsError.DBFailure(e)
      })

      } yield {
        true
      }

    }

  }

  def updateZapMailMailboxDetails(
                                   request: UpdateZapMailMailboxRequest
                                 )(implicit ec: ExecutionContext, ws: WSClient, logger: SRLogger): Future[Either[Exception, Boolean]] = {

    logger.info(s"[ZapMail] Updating mailbox details for email: ${request.email}")

    val (userName, domain): (String, String) = request.getUserNameAndDomain

    val result: Future[Boolean] = for {
      // Step 1: Get the mailbox ID from the email
      mailboxIdOpt: Option[ZapMailMailboxID] <- Future.fromTry(emailInfraDAO.getMailboxIdFromEmail(
        email = request.email
      ))

      // Step 2: Validate that we found a mailbox ID
      mailboxId: ZapMailMailboxID <- mailboxIdOpt match {
        case Some(id) => Future.successful(id)
        case None => Future.failed(new Exception(s"No mailbox found for email ${request.email}"))
      }

      // Step 3: Get existing purchased emails for the domain
      existingPurchasedEmails: List[PurchaseEmail] <- Future.fromTry(emailInfraDAO.getPurchasedEmailsFromDomain(
        domain = domain
      ))

      // Step 4: Update the purchased emails with new details
      updatedPurchasedEmails: List[PurchaseEmail] = existingPurchasedEmails.map {
        case item: PurchaseEmail.ZapMailWithPassword =>
          if (item.email_account.toLowerCase == request.email.toLowerCase)
            item.copy(
              first_name = request.firstName,
              last_name = request.lastName,
              profilePictureUrl = request.profilePicture
            )
          else item
        case item: PurchaseEmail.ZapMailWithoutPassword =>
          item
        case item: PurchaseEmail.Maildoso =>
          item
      }

      // Step 5: Update the database with the modified purchased emails
      subscriptionId: ZapMailSubscriptionID <- Future.fromTry(emailInfraDAO.getSubscriptionIdForZapMailDomain(
        domain = domain
      ))

      _: (AccountId, TeamId) <- Future.fromTry(emailInfraDAO.updateZapMailDomainToActive(
        finalPurchaseEmails = updatedPurchasedEmails,
        domain = domain,
        subscriptionId = subscriptionId
      ))

      // Step 6: Call the ZapMail API to update the mailbox details
      updateResult: Boolean <- zapMailApi.updateMailboxDetails(
        userId = ZapMailUserUID(uid = AppConfig.ZapMail.userId),
        mailboxId = mailboxId,
        firstName = request.firstName,
        lastName = request.lastName,
        userName = userName,
        profilePicture = request.profilePicture
      )
    } yield updateResult

    result.map(Right(_)).recover {
      case e: Exception =>
        logger.error(s"[ZapMail] Failed to update mailbox details for email ${request.email}: ${e.getMessage}", e)
        Left(e)
    }
  }

  def deleteSingleEmails(
                         emailSettingId: EmailSettingId,
                         teamId: TeamId,
                         accountId: AccountId
                       )(using Logger: SRLogger,
                         ec: ExecutionContext, WSClient: WSClient): Future[Either[DeletePurchasedEmailsError, Int]] = {

    val tryAccount: Try[Account] = accountService.find(accountId.id)

    tryAccount match {

      case Success(account) =>

        if(account.org.plan.next_billing_date.isEmpty){

          Logger.shouldNeverHappen(s"deleteSingleEmails :: next_billing_date not found for team_id: ${teamId} and org_id: ${account.org.id} emailSettingId : ${emailSettingId}")
          Future.successful(Left(DeletePurchasedEmailsError.NextBillingDateNotFound))

        } else {

          scheduleEmailsForDeletion(
            emailSettingId = emailSettingId,
            teamId = teamId,
            schedule_for_deletion_at = account.org.plan.next_billing_date.get.minusMinutes(AppConfig.Maildoso.DELETE_DOMAIN_EMAILS_BEFORE_CANCELLATION_MINUTES)
          )
        }


      case Failure(exception) =>

        Logger.error(s"Failed to get scheduled_for_deletion_at - deleteSingleDomain.teamId: $teamId accountId: $accountId emailSettingId: ${emailSettingId}", exception)

        Future.successful(Left(DeletePurchasedEmailsError.DBFailure(exception)))

    }


  }

  def scheduleEmailsForDeletion(
                                 emailSettingId: EmailSettingId,
                                 teamId: TeamId,
                                 schedule_for_deletion_at: DateTime
                               )(using Logger: SRLogger,
                                 ec: ExecutionContext,
                                 WSClient: WSClient): Future[Either[DeletePurchasedEmailsError, Int]] = {

    val deletionResult = for {
      // Step 1: Check if email setting exists
      exists:Unit <- Future.fromTry(emailSettingDAO.checkEmailSettingExists(
        emailSettingId = emailSettingId,
        teamId = teamId
      )).recover {
        case ex => throw new Exception("DB failure during existence check", ex)
      }.flatMap { exists =>
        if (!exists)
          Future.failed(new Exception("Invalid email setting ID"))
        else
          Future.successful(())
      }

      // Step 2: Get email info (We will use this is zapmail deletion flow)
      emailInfo: PurchasedEmailInfo <- Future.fromTry(emailSettingDAO.getDomainPurchasedInfoFromEmailSettingId(
        emailSettingId = emailSettingId,
        teamId = teamId
      )).recover {
        case ex => throw new Exception("DB failure during platform type check", ex)
      }

      _:Boolean <- if (emailInfo.platformType == PlatformType.ZAPMAIL) {
        deleteZapMailMailbox(
          emailInfo = emailInfo
        )
      } else {
        Future.successful(true)
      }

      // Step 3: Schedule deletion
      result:Int <- Future.fromTry(emailSettingDAO.scheduleEmailForDeletion(
        emailSettingId = emailSettingId,
        teamId = teamId,
        schedule_for_deletion_at = schedule_for_deletion_at
      )).recover {
        case ex => throw new Exception("DB failure during scheduling", ex)
      }
    } yield result

    deletionResult.map { rowsUpdated =>
      Logger.info(
        s"Successfully scheduled email for deletion - emailSettingId: $emailSettingId teamId: $teamId schedule_for_deletion_at: $schedule_for_deletion_at"
      )
      Right(rowsUpdated)
    }.recover {
      case ex if ex.getMessage == "Invalid email setting ID" =>
        Left(DeletePurchasedEmailsError.InvalidEmailSettingId)
      case ex =>
        Logger.error(
          s"Failed to schedule email for deletion - emailSettingId: $emailSettingId teamId: $teamId schedule_for_deletion_at: $schedule_for_deletion_at",
          ex
        )
        Left(DeletePurchasedEmailsError.DBFailure(ex.getCause match {
          case null => ex
          case cause => cause
        }))
    }
  }

  def deleteZapMailMailbox(
                            emailInfo: PurchasedEmailInfo
                          )(using Logger: SRLogger,
                            ec: ExecutionContext, WSClient: WSClient): Future[Boolean] = {
    for {

      existingPurchasedEmails: List[PurchaseEmail] <- Future.fromTry(emailInfraDAO.getPurchasedEmailsFromDomain(
        domain = emailInfo.domain
      ))

      zapMailPurchasedMailbox: PurchaseEmail.ZapMailWithPassword <- Future.fromTry {
        val matchingEmail = PurchaseEmail
          .filterZapMailWithPasswordList(existingPurchasedEmails)
          .filter(_.email_account == emailInfo.email)
        if (matchingEmail.length != 1) {
          Failure(new Exception(s"Expected exactly one matching email, found ${matchingEmail.size} for email: ${emailInfo.email} in purchased Emails"))
        } else {
          Success(matchingEmail.head)
        }
      }

      _: Unit <- Future{
        if (zapMailPurchasedMailbox.expiresAt.minusDays(1).isBeforeNow && zapMailPurchasedMailbox.expiresAt.isAfterNow) {
          throw new Exception(s"Delete Action is not allowed as the email is on locked period. please try after 24 hrs")
        }
      }

      _ <- zapMailApi.removeMailboxes(
        userId = ZapMailUserUID(
          uid = AppConfig.ZapMail.userId
        ),
        mailboxId = ZapMailMailboxID(zapMailPurchasedMailbox.mailboxId)
      )



    } yield {
      true
    }
  }

  case class EmailDetails(
                           emailAddress: String,
                           senderFirstName: String,
                           senderLastName: String,
                           emailStatus: PurchaseDomainsAndEmailsStatus,
                           emailSettingId: Option[EmailSettingId],
                           emailSettingUuid: Option[EmailSettingUuid],
                           emailCreatedAt: Option[DateTime],
                           profilePictureUrl: Option[String]
                         )

  case class PurchasedDomainAndEmailForFrontend(
                                                 purchasedDomainUuid: String,
                                                 domainName: String,
                                                 domainCreatedAt: DateTime,
                                                 domainStatus: PurchaseDomainsAndEmailsStatus,
                                                 emails: List[EmailDetails],
                                                 platformType: EmailProvidedBy,
                                                 workspaceType: EmailInfraWorkspaceType
                                               )

  object EmailDetails {
    given format: OFormat[EmailDetails] = Json.format[EmailDetails]
  }

  object PurchasedDomainAndEmailForFrontend {
    given format: OFormat[PurchasedDomainAndEmailForFrontend] = Json.format[PurchasedDomainAndEmailForFrontend]
  }


  def fetchPurchasedDomainsAndEmails(
                                       teamId: TeamId
                                     ): Try[List[PurchasedDomainAndEmailForFrontend]] = {
    for {
      domainsAndEmails: List[PurchasedDomainAndEmailForFrontendFromDB] <-
        emailInfraDAO.fetchDomainsAndEmails(teamId = teamId)

      emailsFromEmailSettings: List[EmailSpecificDetails] <-
        if (domainsAndEmails.isEmpty)
          Success(List())
        else
          emailInfraDAO.fetchEmailsFromEmailSettings(
          platformDomains = domainsAndEmails.map(_.domainName).toList,
          teamId = teamId
        )
    } yield {
      domainsAndEmails.groupBy(_.domainName).map {
        case (domainName, emails) =>
        PurchasedDomainAndEmailForFrontend(
          purchasedDomainUuid = emails.head.purchasedDomainUuid,
          domainName = domainName,
          domainStatus = emails.head.domainStatus,
          domainCreatedAt = emails.head.domainCreatedAt,
          emails = emails.map(email => {
            val emailSetting: Option[EmailSpecificDetails] = emailsFromEmailSettings.find(_.email.trim.toLowerCase() == email.emailAddress.trim.toLowerCase())
            
            EmailDetails(
              emailAddress = email.emailAddress,
              senderFirstName = email.senderFirstName,
              senderLastName = email.senderLastName,
              emailStatus = emailSetting.map(_.emailStatus).getOrElse(PurchaseDomainsAndEmailsStatus.SETUP_INITIATED),
              emailSettingId = emailSetting.map(_.emailSettingId),
              emailSettingUuid = emailSetting.map(_.emailSettingUuid),
              emailCreatedAt = emailSetting.map(_.emailCreatedAt),
              profilePictureUrl = email.profilePictureUrl
            )
          }),
          platformType = emails.head.platform_type,
          workspaceType = emails.head.workspaceType
        )
      }.toList
    }
  }

  def validateEmailsForAdditionalPurchase(
                                           purchaseAdditionalEmailsForm: PurchaseAdditionalEmailsForm,
                                           purchasedDomainUuid: String,
                                           teamId: TeamId
                                         )(using Logger: SRLogger): Either[PurchaseAdditionalEmailsError, PurchasedDomains] = {

    for {

      _ <- planLimitService.checkEmailsAndDomainsAddonLimitReached(
        toBePurchasedEmailQty = purchaseAdditionalEmailsForm.purchasedEmails.length,
        toBePurchasedDomainQty = 0,
        platformType = PlatformType.MAILDOSO, // TODO: check this
        teamId = teamId
      ) match {
        case Success(_) =>
          Logger.info(s"[EmailInfraService.validateEmailsForAdditionalPurchase] purchasedDomainUuid: $purchasedDomainUuid :: team_id: ${teamId.id} Addon limit check passed.")
          Right(true)

        case Failure(_: AddonLimitReachedException) =>
          Logger.error(s"[EmailInfraService.validateEmailsForAdditionalPurchase] purchasedDomainUuid: $purchasedDomainUuid :: team_id: ${teamId.id} Addon limit reached, not enough credits.")
          Left(PurchaseAdditionalEmailsError.DoesNotHaveEnoughCredits)

        case Failure(e) =>
          Logger.fatal(s"[EmailInfraService.validateEmailsForAdditionalPurchase] purchasedDomainUuid: $purchasedDomainUuid :: team_id: ${teamId.id} Addon limit check failed with error: ${e.getMessage}", e)
          Left(PurchaseAdditionalEmailsError.DBFailure(e))
      }

      purchasedDomain <- emailInfraDAO.getPurchasedDomainsForMaildosoByUuid(
        purchasedDomainUuid = purchasedDomainUuid,
        teamId = teamId
      ) match {

        case Failure(e) =>
          Logger.fatal(s"[EmailInfraService.validateEmailsForAdditionalPurchase] purchasedDomainUuid: $purchasedDomainUuid :: team_id: ${teamId.id} Failed to retrieve purchased domain with error: ${e.getMessage}", e)
          Left(PurchaseAdditionalEmailsError.DBFailure(e))

        case Success(None) =>
          Logger.error(s"[EmailInfraService.validateEmailsForAdditionalPurchase] purchasedDomainUuid: $purchasedDomainUuid :: team_id: ${teamId.id} Purchased domain not found.")
          Left(PurchaseAdditionalEmailsError.PurchaseDomainsNotFound)

        case Success(Some(purchasedDomain)) =>
          Logger.info(s"[EmailInfraService.validateEmailsForAdditionalPurchase] purchasedDomainUuid: $purchasedDomainUuid :: team_id: ${teamId.id} Purchased domain retrieved successfully.")
          Right(purchasedDomain)

      }

      invalidEmailExists: Boolean = purchaseAdditionalEmailsForm.purchasedEmails
        .exists(email => !EmailValidationService.validateEmailFormat(
          email = email.email_account
        ))

      _ <- if (invalidEmailExists) {
        Logger.error(s"[EmailInfraService.validateEmailsForAdditionalPurchase] purchasedDomainUuid: $purchasedDomainUuid :: team_id: ${teamId.id} Invalid email format found.")
        Left(PurchaseAdditionalEmailsError.InvalidEmails)
      } else {
        Logger.info(s"[EmailInfraService.validateEmailsForAdditionalPurchase] purchasedDomainUuid: $purchasedDomainUuid :: team_id: ${teamId.id} All emails have valid format.")
        Right(true)
      }

      emailDoesNotBelongToDomain: Boolean = purchaseAdditionalEmailsForm.purchasedEmails
        .exists(email => Helpers.getDomainFromEmail(email.email_account).get != purchasedDomain.domain_name)

      _ <- if (emailDoesNotBelongToDomain) {
        Logger.error(s"[EmailInfraService.validateEmailsForAdditionalPurchase] purchasedDomainUuid: $purchasedDomainUuid :: team_id: ${teamId.id} Email does not belong to the domain.")
        Left(PurchaseAdditionalEmailsError.EmailDoesNotBelongToDomain)
      } else {
        Logger.info(s"[EmailInfraService.validateEmailsForAdditionalPurchase] purchasedDomainUuid: $purchasedDomainUuid :: team_id: ${teamId.id} All emails belong to the domain.")
        Right(true)
      }

      _ <- if (purchasedDomain.domain_status != PurchaseDomainsAndEmailsStatus.ACTIVE) {
        Logger.error(s"[EmailInfraService.validateEmailsForAdditionalPurchase] purchasedDomainUuid: $purchasedDomainUuid :: team_id: ${teamId.id} Domain is not active.")
        Left(PurchaseAdditionalEmailsError.DomainIsNotActive)
      } else {
        Logger.info(s"[EmailInfraService.validateEmailsForAdditionalPurchase] purchasedDomainUuid: $purchasedDomainUuid :: team_id: ${teamId.id} Domain is active.")
        Right(true)
      }

      emailsAlreadyPurchased: Boolean = purchaseAdditionalEmailsForm.purchasedEmails
        .exists(email => purchasedDomain.purchased_emails.contains(email.email_account))

      _ <- if (emailsAlreadyPurchased) {
        Logger.error(s"[EmailInfraService.validateEmailsForAdditionalPurchase] purchasedDomainUuid: $purchasedDomainUuid :: team_id: ${teamId.id} Some emails have already been purchased.")
        Left(PurchaseAdditionalEmailsError.EmailAlreadyPurchased)
      } else {
        Logger.info(s"[EmailInfraService.validateEmailsForAdditionalPurchase] purchasedDomainUuid: $purchasedDomainUuid :: team_id: ${teamId.id} No emails have been previously purchased.")
        Right(true)
      }

      _ <- emailSettingDAO.checkIfEmailExists(
        emails = purchaseAdditionalEmailsForm.purchasedEmails.map(_.email_account),
        teamId = teamId
      ) match {

        case Failure(e) =>
          Logger.fatal(s"[EmailInfraService.validateEmailsForAdditionalPurchase] purchasedDomainUuid: $purchasedDomainUuid :: team_id: ${teamId.id} Checking email existence failed with error: ${e.getMessage}", e)
          Left(PurchaseAdditionalEmailsError.DBFailure(e))

        case Success(true) =>
          Logger.error(s"[EmailInfraService.validateEmailsForAdditionalPurchase] purchasedDomainUuid: $purchasedDomainUuid :: team_id: ${teamId.id} Emails already exist in the system.")
          Left(PurchaseAdditionalEmailsError.EmailAlreadyPurchased)

        case Success(false) =>
          Logger.info(s"[EmailInfraService.validateEmailsForAdditionalPurchase] purchasedDomainUuid: $purchasedDomainUuid :: team_id: ${teamId.id} Emails do not exist in the system.")
          Right(true)

      }

    } yield {
      purchasedDomain
    }

  }

  def purchaseAdditionalEmails(
                                purchaseAdditionalEmailsForm: PurchaseAdditionalEmailsForm,
                                purchasedDomainUuid: String,
                                teamId: TeamId,
                                accountId: AccountId
                              )(implicit ec: ExecutionContext, ws: WSClient, logger: SRLogger): Future[Either[PurchaseAdditionalEmailsError, Seq[PurchasedEmailsAndSaveData]]] = {

    val emails_to_purchase: PurchaseAdditionalEmailsForm = purchaseAdditionalEmailsForm.copy(purchasedEmails = purchaseAdditionalEmailsForm.purchasedEmails.map
        (d => d.copy(email_account = d.email_account.toLowerCase().trim))
      )

    val validations: Either[PurchaseAdditionalEmailsError, PurchasedDomains] = validateEmailsForAdditionalPurchase(
      purchaseAdditionalEmailsForm = emails_to_purchase,
      purchasedDomainUuid = purchasedDomainUuid,
      teamId = teamId
    )

    validations match {
      case Left(err) => Future.successful(Left(err))

      case Right(purchasedDomain) =>

        val purchasedEmails: List[PurchaseEmail.Maildoso] = emails_to_purchase.purchasedEmails.map(email => {
          email.copy(email_account = email.email_account.toLowerCase().trim, password = Helpers.generateEmailPassword())
        })

        val emailToSetup = PurchaseEmailsToSetup(
          purchasedDomainUuid = purchasedDomainUuid,
          platformType = purchasedDomain.platform_type,
          domainName = purchasedDomain.domain_name,
          teamId = teamId,
          accountId = accountId,
          purchasedEmails = purchasedEmails
        )

        for {

          purchaseAndSaveEmailsAccounts: Seq[PurchasedEmailsAndSaveData] <- purchaseAndSaveEmailAccounts(
            emailsToSetup = List(emailToSetup),
            isPurchasingInitialEmails = false
          )

          _ <- Future.fromTry(emailInfraDAO.addAdditionalPurchasedEmails(
            teamId = teamId,
            purchasedDomainUuid = purchasedDomainUuid,
            emails = emails_to_purchase.purchasedEmails
          ))

        } yield {
          Right(purchaseAndSaveEmailsAccounts)
        }

    }

  }

}



