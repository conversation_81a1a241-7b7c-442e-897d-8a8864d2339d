package api.email_infra_integrations.models

import play.api.libs.json.{Json, OFormat}
import utils.SRLogger
import utils.emailvalidation.EmailValidationService

case class UpdateZapMailMailboxRequest(
  email: String,
  firstName: String,
  lastName: String,
  profilePicture: Option[String] = None
) {
  def getUserNameAndDomain(implicit logger: SRLogger): (String, String) = {
    EmailValidationService.getLowercasedNameAndDomainFromEmail(email)
  }
}

object UpdateZapMailMailboxRequest {
  implicit val format: OFormat[UpdateZapMailMailboxRequest] = Json.format[UpdateZapMailMailboxRequest]
}