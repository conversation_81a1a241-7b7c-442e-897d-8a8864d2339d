package api.email_infra_integrations.models

import play.api.libs.json.{Format, JsError, JsResult, JsString, JsSuccess, JsValue}

import scala.util.{Failure, Success, Try}

sealed trait PurchaseType

object PurchaseType {
  private val domain = "domain"
  private val email = "email"

  case object DOMAIN extends PurchaseType {
    override def toString: String = domain
  }

  case object EMAIL extends PurchaseType {
    override def toString: String = email
  }

  def toKey(purchaseType: PurchaseType): String = purchaseType.toString

  def fromString(str: String): Try[PurchaseType] = Try {
    str match {
      case `domain` => DOMAIN
      case `email` => EMAIL
    }
  }

  given format: Format[PurchaseType] = new Format[PurchaseType] {
    override def writes(o: PurchaseType): JsValue = {
      JsString(o.toString)
    }

    override def reads(json: JsValue): JsResult[PurchaseType] = {
      fromString(json.as[String]) match {
        case Failure(err) => JsError(err.getMessage)
        case Success(t) => JsSuccess(t)
      }
    }
  }
}