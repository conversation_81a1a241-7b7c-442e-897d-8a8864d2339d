package api.email_infra_integrations.models

import play.api.libs.json.{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>sS<PERSON>, <PERSON>sSuc<PERSON>, JsValue}

import scala.util.{Failure, Success, Try}

sealed trait PurchaseDomainsAndEmailsStatus

object PurchaseDomainsAndEmailsStatus {
  private val setupPending = "setup_pending"
  private val setupInitiated = "setup_initiated"
  private val active = "active"

  // this status is when the domain is scheduled for deletion at a later time
  private val scheduledForDeletion = "scheduled_for_deletion"

  // this status is when the domain is pushed to the queue for deletion and is being deleted
  private val pushedToQueueForDeletion = "pushed_to_queue_for_deletion"
  private val deleted = "deleted"
  private val failed = "failed"

  private val waitingForZapmailDomainSuccessWebhook = "waiting_for_zapmail_order_success_webhook"
  private val waitingForZapmailMailboxSuccessWebhook = "waiting_for_zapmail_mailbox_success_webhook"

  case object SETUP_PENDING extends PurchaseDomainsAndEmailsStatus {
    override def toString: String = setupPending
  }

  case object WAITING_FOR_ZAPMAIL_DOMAIN_SUCCESS_WEBHOOK extends PurchaseDomainsAndEmailsStatus {
    override def toString: String = waitingForZapmailDomainSuccessWebhook
  }

  case object WAITING_FOR_ZAPMAIL_MAILBOX_SUCCESS_WEBHOOK extends PurchaseDomainsAndEmailsStatus {
    override def toString: String = waitingForZapmailMailboxSuccessWebhook
  }

  case object FAILED extends PurchaseDomainsAndEmailsStatus {
    override def toString: String = failed
  }

  case object SETUP_INITIATED extends PurchaseDomainsAndEmailsStatus {
    override def toString: String = setupInitiated
  }

  case object ACTIVE extends PurchaseDomainsAndEmailsStatus {
    override def toString: String = active
  }

  case object SCHEDULED_FOR_DELETION extends PurchaseDomainsAndEmailsStatus {
    override def toString: String = scheduledForDeletion
  }

  case object PUSHED_TO_QUEUE_FOR_DELETION extends PurchaseDomainsAndEmailsStatus {
    override def toString: String = pushedToQueueForDeletion
  }

  case object DELETED extends PurchaseDomainsAndEmailsStatus {
    override def toString: String = deleted
  }

  def toKey(status: PurchaseDomainsAndEmailsStatus): String = status.toString

  def fromString(str: String): Try[PurchaseDomainsAndEmailsStatus] = Try {
    str match {
      case `setupPending` => SETUP_PENDING
      case `setupInitiated` => SETUP_INITIATED
      case `active` => ACTIVE
      case `scheduledForDeletion` => SCHEDULED_FOR_DELETION
      case `deleted` => DELETED
      case `pushedToQueueForDeletion` => PUSHED_TO_QUEUE_FOR_DELETION
      case `waitingForZapmailDomainSuccessWebhook` => WAITING_FOR_ZAPMAIL_DOMAIN_SUCCESS_WEBHOOK
      case `waitingForZapmailMailboxSuccessWebhook` => WAITING_FOR_ZAPMAIL_MAILBOX_SUCCESS_WEBHOOK
      case `failed` => FAILED
    }
  }

  given format: Format[PurchaseDomainsAndEmailsStatus] = new Format[PurchaseDomainsAndEmailsStatus] {
    override def writes(o: PurchaseDomainsAndEmailsStatus): JsValue = {
      JsString(o.toString)
    }

    override def reads(json: JsValue): JsResult[PurchaseDomainsAndEmailsStatus] = {
      fromString(json.as[String]) match {
        case Failure(err) => JsError(err.getMessage)
        case Success(t) => JsSuccess(t)
      }
    }
  }
}