package api

import api.emails.models.EmailTrackingApiRequestData
import api.sr_audit_logs.models.EventType
import api.sr_audit_logs.services.RequestLogService
import org.joda.time.DateTime
import play.api.Logging
import play.api.libs.json.{JsO<PERSON>, Json}
import play.api.mvc.{AnyContent, BaseController, ControllerComponents, Request, Result}
import utils.{Help<PERSON>, SRLogger, StringUtils}
import utils.email.{ClickTrackingUrlHelper, Email<PERSON>elper, OpenTrackingUrlHelper}
import utils.mq.trackingapp.{MQClickTracker, MQClickTrackerMessage}
import utils.helpers.LogHelpers
import utils.mq.trackingapp.OpenTracker.{MQOpenTrackerMessage, MQOpenTrackerPublisher}

import scala.util.{Failure, Success, Try}


class IndexController(
                       protected val controllerComponents: ControllerComponents,
                       mqOpenTrackerPublisher: MQOpenTrackerPublisher,
                       mqClickTracker: MQClickTracker,
                       requestLogService: RequestLogService
                     ) extends BaseController with Logging {

  /*
  * version is a build-version which is creating in build.sbt `version :=`
  * this version is shown in main route `/`
  * */
  lazy val version: String = getClass.getPackage.getImplementationVersion

  /*def getImage() = Try {

    val singlePixelImage = new BufferedImage(1, 1, BufferedImage.TYPE_4BYTE_ABGR)
    val transparent = new Color(0, 0, 0, 0)
    singlePixelImage.setRGB(0, 0, transparent.getRGB)

    val file = new File("hello.gif")
    ImageIO.write(singlePixelImage, "gif", file)


    file

  }*/

  def getStaticImage() = Try {

    val singlePixelGif = Array(71, 73, 70, 56, 57, 97, 1, 0, 1, 0, -16, 0, 0, 0, 0, 0, 0, 0, 0, 33, -7, 4, 1, 0, 0, 0, 0, 44, 0, 0, 0, 0, 1, 0, 1, 0, 64, 8, 4, 0, 1, 4, 4, 0, 59).map(_.toByte)

    singlePixelGif

  }

  def sendGif() = {

    getStaticImage() match {

      case Failure(e) =>
        logger.error(s"[IndexController.getImage] FATAL error track trackEmailOpen :: ${LogHelpers.getStackTraceAsString(e)}")
        Ok("")
          .withHeaders(
            CACHE_CONTROL -> "no-cache, no-store, must-revalidate",
            PRAGMA -> "no-cache",
            EXPIRES -> "0"
          )

      case Success(image) =>
        Ok(image)
          .withHeaders(
            CONTENT_TYPE -> "image/gif",
            CACHE_CONTROL -> "no-cache, no-store, must-revalidate",
            PRAGMA -> "no-cache",
            EXPIRES -> "0"
          )
    }
  }

  /*val sendImage = getImage() match {

    case Failure(e) =>
      Logger.error(s"[IndexController.getImage] FATAL error track trackEmailOpen :: ${Helpers.getStackTraceAsString(e)}")
      Ok("")
        .withHeaders(
          CACHE_CONTROL -> "no-cache, no-store, must-revalidate",
          PRAGMA -> "no-cache",
          EXPIRES -> "0"
        )

    case Success(image) =>
      Ok.sendFile(image.getAbsoluteFile)
        .withHeaders(
          CACHE_CONTROL -> "no-cache, no-store, must-revalidate",
          PRAGMA -> "no-cache",
          EXPIRES -> "0"
        )

  }*/

  // when a user opens a prospect's email inside SmartReach inbox, those also
  // get tracked, as if the prospect opened them
  // Therefore, when we show something in the inbox, we replace the original
  // open tracking url, with this dummy url, so that mails opened by admin from
  // his/her SmartReach account are not tracked.
  def donottrackEmailOpenDummyForInbox(id: String) = Action {
    sendGif()
  }

  def trackEmailOpenNewV2(code: String) = Action { (request: Request[AnyContent]) =>

    val logRequestId = StringUtils.genLogTraceId
    val Logger = new SRLogger(logRequestId = s"$logRequestId trackEmailOpenNewV2 code :: $code ")

    val requestIP = Helpers.getClientIPFromGCPLoadbalancer(request = request)
    val reqUserAgent = request.headers.get("User-Agent").getOrElse("")
    val reqHeaders = request.headers
    val requestHost = request.host

    Logger.info(s"ot2init host: ${request.host} :: uri: ${request.uri} :: Client-IP: $requestIP :: userAgent: $reqUserAgent :: allheaders: ${reqHeaders.toSimpleMap}")


    OpenTrackingUrlHelper.deconstructOpenTrackingUrl(code) match {
      case Success(result) =>

        if(result.emailScheduledId.isEmpty && result.emailsScheduledUuidAndTeamId.isEmpty) {

          Logger.info(s"trackEmailOpenNewV2 [IGNORETRACK] invalid link: id and uuid both empty. code : ${code}")

          sendGif()

        } else if(result.emailScheduledId.nonEmpty && result.emailsScheduledUuidAndTeamId.nonEmpty) {

          Logger.info(s"trackEmailOpenNewV2 [IGNORETRACK] invalid link: id ${result.emailScheduledId} and uuid ${result.emailsScheduledUuidAndTeamId} both present")

          sendGif()
        } else if(result.emailsScheduledUuidAndTeamId.nonEmpty && result.emailsScheduledUuidAndTeamId.get._1.uuid == AppConfig.dummy_email_tracking_uuid){
          Logger.info(s"trackEmailOpenNewV2 [IGNORETRACK] dummy link: uuid ${result.emailsScheduledUuidAndTeamId} : teamId ${result.emailsScheduledUuidAndTeamId.get._2}")

          sendGif()
        } else {
          val savedTrackingReqDBId: Option[Long] = EmailTrackingApiRequestData.saveEmailTrackingApiRequestHeader(
            traceReqId = logRequestId,
            reqHost = requestHost,
            reqURI = request.uri,
            postRequestUrl = None,

            eventType = EventType.EMAIL_OPENED,
            ip = requestIP.getOrElse(""),
            userAgent = reqUserAgent,
            requestHeaders = reqHeaders

          ) match {
            case Failure(e) =>

              Logger.fatal("saveEmailTrackingRequestHeader (IGNORING BUT CHECK): ", err = e)
              None

            case Success(savedTrackingRequestId) =>

              Logger.info(s"savedTrackingRequestId: $savedTrackingRequestId")

              savedTrackingRequestId

          }

          mqOpenTrackerPublisher.publish(MQOpenTrackerMessage(
            id = result.emailScheduledId.map(_.toString),
            emailsScheduledUuid = result.emailsScheduledUuidAndTeamId.map(_._1),
            teamId = result.emailsScheduledUuidAndTeamId.map(_._2),
            opened_at = DateTime.now().getMillis.toString,
            ip = Helpers.getClientIPFromGCPLoadbalancer(request = request).getOrElse(""),
            trace_req_db_id = savedTrackingReqDBId,
            audit_log_request_id = None /*Some(audit_log_request_id)*/
          )) match {

            case Failure(e) =>

              Logger.error(s"Error trackEmailOpenNewV2 ($result):: ($code) " + LogHelpers.getStackTraceAsString(e))

              sendGif()

            case Success(_) =>

              sendGif()
          }
        }

        /*

        11 Feb 2023 - commented out, creating lot of noise in the audit logs

        requestLogService.createRequestLogForAnonymousRequests(request = request) match {

          case Left(err) =>
            Logger.error(s"Error createRequestLogInDb trackEmailOpenNewV2 code:: ($code) :: error ${err.toString}")
            sendGif()

          case Right(audit_log_request_id) =>
*/


      /*}*/

      case Failure(e: java.lang.NumberFormatException) =>
        Logger.error(s"IndexController trackEmailOpenNewV2 skipping error $code  :: ${LogHelpers.getStackTraceAsString(e)}")
        sendGif()

      case Failure(e: javax.crypto.BadPaddingException) =>
        Logger.error(s"IndexController trackEmailOpenNewV2 skipping error $code :: ${request.headers} :: ${LogHelpers.getStackTraceAsString(e)}")
        sendGif()

      case Failure(e: javax.crypto.IllegalBlockSizeException) =>
        Logger.error(s"IndexController trackEmailOpenNewV2 skipping error $code :: ${request.headers} :: ${LogHelpers.getStackTraceAsString(e)}")
        sendGif()

      case Failure(error) =>
        Logger.error(s"IndexController trackEmailOpenNewV2 skipping error $code  :: ${LogHelpers.getStackTraceAsString(error)}")
        sendGif()

    }
  }


  private def __clickTrackResponse(
                                    // teamId: Option[Long],
                                    decodedUrl: String
                                  ): Result = {

    // if the link does not have a http scheme, prepend "http://" else the redirect fails
    val redirectTo = if (!decodedUrl.toLowerCase.matches("^\\w+://.*")) "http://" + decodedUrl else decodedUrl

    val redirectToHtmlPage =
      s"""<!doctype html><html lang="en">
            <head>
              <meta charset="utf-8">
              <title>$redirectTo</title>

              <script>
                window.onload = function() { setTimeout(() => { window.location.href = "$redirectTo"; }, 150);  }
              </script>

            </head>

            <body>
              <p>Going to $redirectTo ...</p>
            </body>
          </html>"""

    val redirectToHtmlResponse = Ok(redirectToHtmlPage).as(HTML)

    redirectToHtmlResponse

  }

  def trackClickBase32GET(id: String) = Action { (request: Request[AnyContent]) =>

    val logRequestId = StringUtils.genLogTraceId
    val Logger = new SRLogger(logRequestId = s"$logRequestId trackClickBase32GET id :: $id ")

    val requestIP = Helpers.getClientIPFromGCPLoadbalancer(request = request)
    val reqUserAgent = request.headers.get("User-Agent").getOrElse("")
    val reqHeaders = request.headers

    Logger.info(s"clickinit host: ${request.host} :: uri: ${request.uri} :: Client-IP: $requestIP :: userAgent: $reqUserAgent :: allheaders: ${reqHeaders.toSimpleMap}")

    val requestPath = request.path
    val requestHost = request.host
    val requestProtocol = if (request.host.contains("localhost")) "http" else "https"

    /**
     * POST /ct3/:id/click?reqid=logRequestId
     *
     * this helps us trace click requests across both the GET and POST steps
     */
    val postRequestUrl = s"$requestProtocol://$requestHost$requestPath?reqid=$logRequestId"

    Logger.info(s"requestPath: $requestPath :: postRequestUrl: $postRequestUrl")

    ClickTrackingUrlHelper.deconstructClickTrackingUrl(code = id) match {

      case Failure(e) =>

        val errMsg = s"${LogHelpers.getStackTraceAsString(e)}"

        if (request.uri.contains("R0VZVEdNUl") || request.uri.contains("R0VZVEdOUl")) {
          Logger.error(s"NOFT $errMsg")
        } else {
          Logger.error(s"FATAL $errMsg")
        }

        Ok("Invalid link")

      case Success(result) =>
        val htmlResponse = trackClickBase32POSTHtml(
          trackClickPostUrl = postRequestUrl,
          redirectToUrl = result.clickTrackingUrl
        )

        EmailTrackingApiRequestData.saveEmailTrackingApiRequestHeader(
          traceReqId = logRequestId,
          reqHost = requestHost,
          reqURI = request.uri,
          postRequestUrl = Some(postRequestUrl),

          eventType = EventType.EMAIL_LINK_CLICKED,
          ip = requestIP.getOrElse(""),
          userAgent = reqUserAgent,
          requestHeaders = reqHeaders

        ) match {
          case Failure(e) =>

            Logger.fatal("saveEmailTrackingRequestHeader (IGNORING BUT CHECK): ", err = e)
            htmlResponse

          case Success(savedTrackingRequestId) =>

            Logger.info(s"savedTrackingRequestId: $savedTrackingRequestId")

            htmlResponse


        }


    }

  }

  def trackClickBase32POSTHtml(
                                redirectToUrl: String,
                                trackClickPostUrl: String
                              ): Result = {

    val trackClickBase32HtmlPage =
      s"""<!doctype html><html lang="en">
            <head>
              <meta charset="utf-8">
              <title>Redirecting ...</title>

              <script>
                window.onload = function() { setTimeout(() => {

                  var xhr = new XMLHttpRequest();

                  xhr.open("POST", "$trackClickPostUrl", true);
                  xhr.onreadystatechange = handler;
                  xhr.responseType = 'json';
                  xhr.setRequestHeader('Accept', 'application/json');
                  xhr.send();

                  function handler() {
                    if (this.readyState === this.DONE) {
                      if (this.status === 200) {
                        window.location.href = "$redirectToUrl";
                      } else {
                        window.location.href = "$redirectToUrl";
                      }
                    }
                  };

                }, 150); }
              </script>

            </head>

            <body>
            <p>Going to URL ${redirectToUrl}...</p>
            </body>
          </html>"""

    val response = Ok(trackClickBase32HtmlPage).as(HTML)

    response

  }

  def trackClickBase32Post(id: String, reqid: Option[String]) = Action { (request: Request[AnyContent]) =>

    val logRequestId = reqid.getOrElse(StringUtils.genLogTraceId)

    val Logger = new SRLogger(logRequestId = s" $logRequestId trackClickBase32Post ")
    val Res = new SRAPIResponse(Logger = Logger)

    Logger.info(s"clickinit post id :: $id :: reqid: $reqid :: host: ${request.host} :: uri: ${request.uri} :: userAgent: ${request.headers.get("User-Agent")} :: Client-IP: ${Helpers.getClientIPFromGCPLoadbalancer(request = request)}")

    ClickTrackingUrlHelper.deconstructClickTrackingUrl(code = id) match {

      case Failure(e) =>

        val errMsg = s"${LogHelpers.getStackTraceAsString(e)}"

        if (request.uri.contains("R0VZVEdNUl") || request.uri.contains("R0VZVEdOUl")) {
          Logger.error(s"NOFT $errMsg")
        } else {
          Logger.error(s"FATAL $errMsg")
        }

        Res.BadRequestError("Invalid link")

      case Success(result) =>
        if(result.emailScheduledId.isEmpty && result.emailsScheduledUuidAndTeamId.isEmpty) {
          
          Logger.info(s"trackClickBase32Post [IGNORETRACK] invalid link: id and uuid both empty: ${result.clickTrackingUrl}")
          Ok("Redirect failed")
          
        } else if(result.emailScheduledId.nonEmpty && result.emailsScheduledUuidAndTeamId.nonEmpty) {
          
          Logger.info(s"trackClickBase32Post [IGNORETRACK] invalid link: id ${result.emailScheduledId} and uuid ${result.emailsScheduledUuidAndTeamId} both present: ${result.clickTrackingUrl}")
          Ok("Redirect failed")
          
        } else {
          val emailScheduledId = result.emailScheduledId
          mqClickTracker.publish(
            MQClickTrackerMessage(
              email_id = emailScheduledId.map(_.toString),
              email_scheduled_uuid = result.emailsScheduledUuidAndTeamId.map(_._1),
              team_id = result.emailsScheduledUuidAndTeamId.map(_._2),
              clicked_url = result.clickTrackingUrl,
              clicked_at = DateTime.now().getMillis.toString,
              trace_reqid = reqid
            )
          ) match {

            case Failure(e) =>
              Logger.fatal(s"MQClickTracker redirecting $emailScheduledId :: ${result.clickTrackingUrl}", err = e)

              Res.ServerError("Invalid link", e = Some(e))

            case Success(_) =>

              Res.Success("Click tracked successfully!", Json.obj())

          }
        }
    }
  }

  def donottrackEmailClickDummyForInboxBase32(id: String) = Action {
    ClickTrackingUrlHelper.deconstructClickTrackingUrl(code = id) match {

      case Failure(e) => Ok("Redirect failed")

      case Success(result) =>
        __clickTrackResponse(
          decodedUrl = result.clickTrackingUrl
        )
      // Redirect(decodedUrl)
    }
  }

  // return index.html for any non-matching route
  // def index(name: String) = controllers.Assets.at(path = "/public", file = "index.html")


  def returnIndexHtml(path: String) = Action { (request: Request[AnyContent]) =>
    val logRequestId = s"${StringUtils.genLogTraceId} IndexController.returnIndexHtml: "

    val Logger = new SRLogger(logRequestId)

    if (
      path.trim.nonEmpty && (

        // ignore identifiable bots completely
        !path.contains("favicon.ico")
          && !path.contains(".php")
          && !path.contains("phpMyAdmin")
          && !path.contains("_asterisk")
          && !path.endsWith(".json")
          && !path.endsWith(".png")
          && !path.endsWith(".env")
          && !path.endsWith(".xml")
          && !path.endsWith(".aspx")
          && !path.contains(".git")
          && !path.endsWith(".action")
          && !path.endsWith(".ini")
          && !path.endsWith(".asp")
          && !path.endsWith(".jsp")
        )

    ) {
      val requestIP = Helpers.getClientIPFromGCPLoadbalancer(request = request)

      logger.warn(s"IndexController.returnIndexHtml: $path :: ${request.method} ${request.uri} :: ip: $requestIP :: body: ${request.body}")
    }

    val redirectToHtmlPage =
      s"""<!DOCTYPE html>
        <html lang="en">

          <head>
            <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
                <meta http-equiv="x-ua-compatible" content="ie=edge">

                </head>

                <body>
                  <div>
                    Hello world!
                    <p style="position: absolute; bottom: -9px; right: 7px">${version}</p>
                  </div>
                </body>

              </html>"""

    //Ok(redirectToHtmlPage).as(HTML)


    //    val requestIP = Helpers.getClientIPFromGCPLoadbalancer(request = request)
    //
    //    logger.warn(s"IndexController.returnIndexHtml: $path :: ${request.method} ${request.uri} :: ip: $requestIP :: body: ${request.body}")

    new SRAPIResponse(Logger = Logger).Success(
      message = "Success",
      data = Json.obj(
        "version" -> s"${version}",
        "message" -> "Hello world!"
      )
    )
  }


  def returnRobotsTxt() = Action { (request: Request[AnyContent]) =>

    val returnRobotsTxtContent =
      s"""User-agent: *\nDisallow: *""".stripMargin

    Ok(returnRobotsTxtContent).as(TEXT)

  }

  def return404NotFound(path: String) = Action { (request: Request[AnyContent]) =>

    val Logger = new SRLogger(logRequestId = s"Not found")
    val Res = new SRAPIResponse(Logger = Logger)


    // do not log standard bot noise
    val logError: Boolean = IndexController.shouldLogApiNotFoundError(
      path = path
    )

    if (logError) {
      Logger.warn(s"API_NOT_FOUND $path")
    }

    // 23-feb-2024: checked in logs: everything logged here was noise,
    // we are anything thats not clearly noise above as warning anyways.
    // Therefore we are avoiding logging this error inside the APIResponseModule
    Res.NotFoundError(s"API not found", ignoreErrorLog = true)

  }
}

object IndexController {

  // ignore logging random noise
  private val standardApiBotNoisePathsOnWhichToIgnoreLogger: Set[String] = Set(
    "favicon.ico",
    "wp-login.php",
    "wp-content/",
    ".env"
  )

  private val endsWithStandardBotPatterns = Seq(
    ".php",
    ".js",
    ".xml",
    "logon.html",
    "dns-query",
    "query",
    ".DS_Store",
    "api.json",
    ".cgi",
  )

  def shouldLogApiNotFoundError(path: String): Boolean = {

    val p = path.trim.toLowerCase

    // do not log standard bot noise
    val exactMatchBot: Boolean = IndexController
      .standardApiBotNoisePathsOnWhichToIgnoreLogger
      .contains(p)


    val endsWithBotSpeak: Boolean = endsWithStandardBotPatterns
      .exists(pattern => p.endsWith(pattern))


    !exactMatchBot &&
      !endsWithBotSpeak

  }
}
