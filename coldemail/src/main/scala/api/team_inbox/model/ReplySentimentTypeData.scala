package api.team_inbox.model

import api.emails.EmailReplySavedV3
import api.prospects.models.{ProspectCategory, ProspectCategoryId, ProspectCategoryNew, ProspectId}
import api.team_inbox.service.{ReplySentimentForTeam, UpdateReplySentimentFormForDAO}
import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>sS<PERSON>, JsSuc<PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Writes}
import sr_scheduler.models.ChannelType
import utils.SRLogger

import scala.util.{Failure, Success, Try}

sealed trait ReplySentimentType {
  def toString: String

  def display_name: String

}

object ReplySentimentType {

  private val POSITIVE = "positive"
  private val NEGATIVE = "negative"
  private val FOLLOW_UP_NEEDED = "follow_up_needed"

  private val REFERRAL = "referral"
  private val OBJECTION = "objection"
  private val DO_NOT_CONTACT = "do_not_contact"
  private val OTHER = "other"
  private val NONE = "none"
  private val UNCATEGORIZED = "uncategorized"

  case object Positive extends ReplySentimentType {
    override def toString: String = POSITIVE
    override def display_name: String = "Positive"
  }

  case object Negative extends ReplySentimentType {
    override def toString: String = NEGATIVE

    override def display_name: String = "Negative"
  }

  case object FollowUpNeeded extends ReplySentimentType {
    override def toString: String = FOLLOW_UP_NEEDED

    override def display_name: String = "Follow Up Needed"
  }

  case object Referral extends ReplySentimentType {
    override def toString: String = REFERRAL
    override def display_name: String = "Referral"
  }

  case object Objection extends ReplySentimentType {
    override def toString: String = OBJECTION
    override def display_name: String = "Objection"
  }

  case object DoNotContact extends ReplySentimentType {
    override def toString: String = DO_NOT_CONTACT
    override def display_name: String = "Do Not Contact"
  }

  case object Other extends ReplySentimentType {
    override def toString: String = OTHER
    override def display_name: String = "Other"
  }



  case object Uncategorized extends ReplySentimentType {
    override def toString: String = UNCATEGORIZED

    override def display_name: String = "Uncategorized"
  }



  def getReplySentimentTypeFromCode(rs_id: String): Try[Option[ReplySentimentType]] = Try{
    rs_id match {
      case `POSITIVE` => Some(ReplySentimentType.Positive)
      case `NEGATIVE` => Some(ReplySentimentType.Negative)
      case `FOLLOW_UP_NEEDED` => Some(ReplySentimentType.FollowUpNeeded)

      case `REFERRAL` => Some(ReplySentimentType.Referral)
      case `OBJECTION` => Some(ReplySentimentType.Objection)
      case `DO_NOT_CONTACT` => Some(ReplySentimentType.DoNotContact)
      case `OTHER` => Some(ReplySentimentType.Other)

      case `NONE` => None

    }
  }

  def fromKey(key: String): Try[ReplySentimentType] = Try{

    key match {
      case `POSITIVE` => Positive
      case `FOLLOW_UP_NEEDED` => FollowUpNeeded
      case `NEGATIVE` => Negative

      case `REFERRAL` => Referral
      case `OBJECTION` => Objection
      case `DO_NOT_CONTACT` => DoNotContact
      case `OTHER` => Other
      case `UNCATEGORIZED` => Uncategorized

    }
  }

  def getAllReplySentimentTypes(): List[ReplySentimentType] = {
    List(
      ReplySentimentType.Positive,
      ReplySentimentType.Negative,
      ReplySentimentType.FollowUpNeeded,

      ReplySentimentType.Referral,
      ReplySentimentType.Objection,
      ReplySentimentType.DoNotContact,
      ReplySentimentType.Uncategorized,

      ReplySentimentType.Other

    )
  }


  implicit val writes: Writes[ReplySentimentType] = new Writes[ReplySentimentType]{
    def writes(o: ReplySentimentType): JsValue = {
      JsString(o.toString)
    }
  }

  implicit val reads: Reads[ReplySentimentType] = new Reads[ReplySentimentType] {
    override def reads(json: JsValue) = {

      json match {
        case JsString(value) =>
          fromKey(key = value) match {

            case Failure(exception) => JsError(exception.toString)

            case Success(data) => JsSuccess(data)
          }

        case _ =>

          JsError(s"""Expected a ReplySentimentType string, got something else """)
      }

    }
  }

}


sealed trait ReplySentimentChannelType;

object ReplySentimentChannelType {

  private val allChannelSentiments = "all"
  private val emailChannelType = ChannelType.EmailChannel.toString
  private val linkedinChannelType = ChannelType.LinkedinChannel.toString
  private val smsChannelType = ChannelType.SmsChannel.toString
  private val whatsappChannelType = ChannelType.WhatsappChannel.toString
  private val generalChannelType = ChannelType.GeneralChannel.toString
  private val callChannelType = ChannelType.CallChannel.toString

  case object AllChannelSentiments extends ReplySentimentChannelType {
    override def toString: String = allChannelSentiments

  }



  case object CallChannelType extends ReplySentimentChannelType {

    override def toString: String = callChannelType
  }

  case object EmailChannelType extends ReplySentimentChannelType {

    override def toString: String = emailChannelType
  }

  case object LinkedinChannelType extends ReplySentimentChannelType {
    override def toString: String = linkedinChannelType
  }

  case object SmsChannelType extends ReplySentimentChannelType {
    override def toString: String = smsChannelType
  }

  case object WhatsappChannelType extends ReplySentimentChannelType {
    override def toString: String = whatsappChannelType
  }

  case object GeneralChannelType extends ReplySentimentChannelType {
    override def toString: String = generalChannelType
  }


  def fromKey(key: String): Try[ReplySentimentChannelType] = Try{

    key match {


      case `callChannelType` => CallChannelType

      case `allChannelSentiments` => AllChannelSentiments

      case `emailChannelType` => EmailChannelType

      case `linkedinChannelType` => LinkedinChannelType

      case `smsChannelType` => SmsChannelType

      case `whatsappChannelType` => WhatsappChannelType

      case `generalChannelType` => GeneralChannelType


    }

  }



}

sealed trait ReplySentimentTypeData {
  override def toString: String = getReplySentimentType.toString

  def getReplySentimentSubCategory: ReplySentimentSubCategory

  def getSubcategoryName: String = getReplySentimentSubCategory.toString

  def getReplySentimentChannelType: ReplySentimentChannelType

  def getDisplayNameForType: String

  def getReplySentimentType: ReplySentimentType


}


object ReplySentimentTypeData {

  val PositiveDisplayName = "Positive"
  val NegativeDisplayName = "Negative"
  val FollowUpNeededDisplayName = "Follow Up Needed"
  
  val ReferralDisplayName = "Referral"
  val ObjectionDisplayName = "Objection"
  val DNCDisplayName = "Do Not Contact"
  val OtherDisplayName = "Other"




  case class PositiveData(
                           replySentimentSubCategory: ReplySentimentSubCategory,
                           replySentimentChannelType: ReplySentimentChannelType  = ReplySentimentChannelType.EmailChannelType

                         ) extends ReplySentimentTypeData {
    override def getReplySentimentSubCategory: ReplySentimentSubCategory = replySentimentSubCategory

    override def toString: String = getReplySentimentType.toString

    override def getReplySentimentChannelType: ReplySentimentChannelType = replySentimentChannelType

    override def getDisplayNameForType: String = PositiveDisplayName

    override def getReplySentimentType: ReplySentimentType = ReplySentimentType.Positive


  }

  object PositiveData {

    implicit val writes: Writes[PositiveData] = new Writes[PositiveData] {
      def writes(a: PositiveData) = {
        Json.obj(
          "reply_sentiment_type" -> a.getDisplayNameForType,
          "reply_sentiment_name" -> a.getSubcategoryName,
          "reply_sentiment_channel_type" -> a.getReplySentimentChannelType.toString
        )
      }
    }
  }

  case class NegativeData(
                           replySentimentSubCategory: ReplySentimentSubCategory,
                           replySentimentChannelType: ReplySentimentChannelType = ReplySentimentChannelType.EmailChannelType,

                         ) extends ReplySentimentTypeData {
    override def getReplySentimentSubCategory: ReplySentimentSubCategory = replySentimentSubCategory

    override def toString: String = getReplySentimentType.toString

    override def getReplySentimentChannelType: ReplySentimentChannelType = replySentimentChannelType

    override def getDisplayNameForType: String = NegativeDisplayName

    override def getReplySentimentType: ReplySentimentType = ReplySentimentType.Negative


  }

  object NegativeData {

    implicit val writes: Writes[NegativeData] = new Writes[NegativeData] {
      def writes(a: NegativeData) = {
        Json.obj(
          "reply_sentiment_type" -> a.getDisplayNameForType,
          "reply_sentiment_name" -> a.getSubcategoryName,
          "reply_sentiment_channel_type" -> a.getReplySentimentChannelType.toString,
        )
      }
    }
  }

  case class FollowUpNeededData(
                            replySentimentSubCategory: ReplySentimentSubCategory,
                            replySentimentChannelType: ReplySentimentChannelType  = ReplySentimentChannelType.EmailChannelType,

                         ) extends ReplySentimentTypeData {
    override def getReplySentimentSubCategory: ReplySentimentSubCategory = replySentimentSubCategory

    override def toString: String = getReplySentimentType.toString

    override def getReplySentimentChannelType: ReplySentimentChannelType = replySentimentChannelType

    override def getDisplayNameForType: String = FollowUpNeededDisplayName

    override def getReplySentimentType: ReplySentimentType = ReplySentimentType.FollowUpNeeded


  }

  object FollowUpNeededData {

    implicit val writes: Writes[FollowUpNeededData] = new Writes[FollowUpNeededData] {
      def writes(a: FollowUpNeededData) = {
        Json.obj(
          "reply_sentiment_type" -> a.getDisplayNameForType,
          "reply_sentiment_name" -> a.getSubcategoryName,
          "reply_sentiment_channel_type" -> a.getReplySentimentChannelType.toString
        )
      }
    }
  }
  
  

  case class ReferralData(

                           replySentimentSubCategory: ReplySentimentSubCategory,
                           replySentimentChannelType: ReplySentimentChannelType = ReplySentimentChannelType.EmailChannelType 

                         ) extends ReplySentimentTypeData {
    override def getReplySentimentSubCategory: ReplySentimentSubCategory = replySentimentSubCategory

    override def toString: String = getReplySentimentType.toString

    override def getReplySentimentChannelType: ReplySentimentChannelType = replySentimentChannelType

    override def getDisplayNameForType: String = ReferralDisplayName

    override def getReplySentimentType: ReplySentimentType = ReplySentimentType.Referral



  }

  object ReferralData {

    implicit val writes: Writes[ReferralData] = new Writes[ReferralData] {
      def writes(a: ReferralData) = {
        Json.obj(
          "reply_sentiment_type" -> a.getDisplayNameForType,
          "reply_sentiment_name" -> a.getSubcategoryName,
          "reply_sentiment_channel_type" -> a.getReplySentimentChannelType.toString
        )
      }
    }
  }

  case class ObjectionData(

                            replySentimentSubCategory: ReplySentimentSubCategory,
                            replySentimentChannelType: ReplySentimentChannelType  = ReplySentimentChannelType.EmailChannelType

                          ) extends ReplySentimentTypeData {
    override def getReplySentimentSubCategory: ReplySentimentSubCategory = replySentimentSubCategory

    override def toString: String = getReplySentimentType.toString

    override def getReplySentimentChannelType: ReplySentimentChannelType = replySentimentChannelType

    override def getDisplayNameForType: String = ObjectionDisplayName

    override def getReplySentimentType: ReplySentimentType = ReplySentimentType.Objection



  }

  object ObjectionData {

    implicit val writes: Writes[ObjectionData] = new Writes[ObjectionData] {
      def writes(a: ObjectionData) = {
        Json.obj(
          "reply_sentiment_type" -> a.getDisplayNameForType,
          "reply_sentiment_name" -> a.getSubcategoryName,
          "reply_sentiment_channel_type" -> a.getReplySentimentChannelType.toString,
        )
      }
    }
  }

  case class DoNotContactData(

                               replySentimentSubCategory: ReplySentimentSubCategory,
                               replySentimentChannelType: ReplySentimentChannelType  = ReplySentimentChannelType.EmailChannelType,

                             ) extends ReplySentimentTypeData {
    override def getReplySentimentSubCategory: ReplySentimentSubCategory = replySentimentSubCategory

    override def toString: String = getReplySentimentType.toString

    override def getReplySentimentChannelType: ReplySentimentChannelType = replySentimentChannelType

    override def getDisplayNameForType: String = DNCDisplayName

    override def getReplySentimentType: ReplySentimentType = ReplySentimentType.DoNotContact



  }

  object DoNotContactData {

    implicit val writes: Writes[DoNotContactData] = new Writes[DoNotContactData] {
      def writes(a: DoNotContactData) = {
        Json.obj(
          "reply_sentiment_type" -> a.getDisplayNameForType,
          "reply_sentiment_name" -> a.getSubcategoryName,
          "reply_sentiment_channel_type" -> a.getReplySentimentChannelType.toString,
        )
      }
    }
  }


  case class OtherData(

                        replySentimentSubCategory: ReplySentimentSubCategory,
                        replySentimentChannelType: ReplySentimentChannelType  = ReplySentimentChannelType.EmailChannelType,

                      ) extends ReplySentimentTypeData {
    override def getReplySentimentSubCategory: ReplySentimentSubCategory = replySentimentSubCategory

    override def toString: String = getReplySentimentType.toString

    override def getReplySentimentChannelType: ReplySentimentChannelType = replySentimentChannelType

    override def getDisplayNameForType: String = OtherDisplayName

    override def getReplySentimentType: ReplySentimentType = ReplySentimentType.Other



  }

  object OtherData {

    implicit val writes: Writes[OtherData] = new Writes[OtherData] {
      def writes(a: OtherData) = {
        Json.obj(
          "reply_sentiment_type" -> a.getDisplayNameForType,
          "reply_sentiment_name" -> a.getSubcategoryName,
          "reply_sentiment_channel_type" -> a.getReplySentimentChannelType.toString,
        )
      }
    }
  }


  def toKey(replySentimentType: ReplySentimentTypeData): String = replySentimentType.toString


  def fromKey(
               replySentimentTypeString: String,
               replySentimentName: String,
               replySentimentChannelType: String
             )(using Logger:SRLogger): Try[ReplySentimentTypeData] = {
    for {
      channelType: ReplySentimentChannelType <- ReplySentimentChannelType.fromKey(replySentimentChannelType)
      subCategory: ReplySentimentSubCategory <- ReplySentimentSubCategory.fromKey(replySentimentName.trim,channelType)
      mainType: ReplySentimentType <- ReplySentimentType.fromKey(replySentimentTypeString)
    } yield {
      mainType match {
        case ReplySentimentType.Positive => PositiveData(
          replySentimentSubCategory = subCategory,
          replySentimentChannelType = channelType
        )
        case ReplySentimentType.Negative => NegativeData(
          replySentimentSubCategory = subCategory,
          replySentimentChannelType = channelType
        )
        case ReplySentimentType.FollowUpNeeded => FollowUpNeededData(
          replySentimentSubCategory = subCategory,
          replySentimentChannelType = channelType
        )
        case ReplySentimentType.Referral => ReferralData(
          replySentimentSubCategory = subCategory,
          replySentimentChannelType = channelType
        )
        case ReplySentimentType.Objection => ObjectionData(
          replySentimentSubCategory = subCategory,
          replySentimentChannelType = channelType
        )
        case ReplySentimentType.DoNotContact => DoNotContactData(
          replySentimentSubCategory = subCategory,
          replySentimentChannelType = channelType
        )
        case ReplySentimentType.Other => OtherData(
          replySentimentSubCategory = subCategory,
          replySentimentChannelType = channelType
        )

        case ReplySentimentType.Uncategorized =>
          // Uncategorized was only added for Prospect Replies Report. This text is not saved in DB.	          throw new IllegalArgumentException("Uncategorized ReplySentimentType is not supported in ReplySentimentTypeData")
          // The empty reply_sentiment_uuid is treated as Uncategorized in Reports and sent to Frontend.	      }
          // Therefore, we cannot get a string with value "uncategorized" in fromKey.
          throw new Exception("Uncategorized Reply Sentiment is not expected in fromKey.")
      }
    }
  }


  implicit val writes: Writes[ReplySentimentTypeData] = new Writes[ReplySentimentTypeData] {
    def writes(attempt: ReplySentimentTypeData): JsValue = {

      attempt match {
        case t: ReplySentimentTypeData.PositiveData => Json.toJson(t)
        case t: ReplySentimentTypeData.NegativeData => Json.toJson(t)
        case t: ReplySentimentTypeData.FollowUpNeededData => Json.toJson(t)
        case t: ReplySentimentTypeData.ReferralData => Json.toJson(t)
        case t: ReplySentimentTypeData.ObjectionData => Json.toJson(t)
        case t: ReplySentimentTypeData.DoNotContactData => Json.toJson(t)
        case t: ReplySentimentTypeData.OtherData => Json.toJson(t)
      }

    }
  }

  implicit val reads: Reads[ReplySentimentTypeData] = new Reads[ReplySentimentTypeData] {
    override def reads(json: JsValue): JsResult[ReplySentimentTypeData] = {

      for {
        reply_sentiment_type: String <- {
          val reply_sentiment_type_Reads: Reads[String] = (JsPath \ "reply_sentiment_type").read[String]
          json.validate[String](reply_sentiment_type_Reads)
        }

        reply_sentiment_name: String <- {
          val reply_sentiment_name_Reads: Reads[String] = (JsPath \ "reply_sentiment_name").read[String]
          json.validate[String](reply_sentiment_name_Reads)
        }

        reply_sentiment_channel_type: String <- {
          val reply_sentiment_channel_type_Reads: Reads[String] = (JsPath \ "reply_sentiment_channel_type").read[String]
          json.validate[String](reply_sentiment_channel_type_Reads)
        }

       

      } yield {
        given Logger: SRLogger = new SRLogger("[ReplySentimentTypeData Reads]")
        fromKey(
          replySentimentTypeString = getReplySentimentTypeFromDisplayName(reply_sentiment_type).get,
          replySentimentName = reply_sentiment_name,
          replySentimentChannelType = reply_sentiment_channel_type,
        ).get
      }
    }

  }


  private def getReplySentimentTypeFromDisplayName(
                                                    displayName: String
                                                  ): Try[String] = Try {
    displayName match {
      case `PositiveDisplayName` => ReplySentimentType.Positive.toString
      case `NegativeDisplayName` => ReplySentimentType.Negative.toString
      case `FollowUpNeededDisplayName` => ReplySentimentType.FollowUpNeeded.toString
      case `ReferralDisplayName` => ReplySentimentType.Referral.toString
      case `ObjectionDisplayName` => ReplySentimentType.Objection.toString
      case `DNCDisplayName` => ReplySentimentType.DoNotContact.toString
      case `OtherDisplayName` => ReplySentimentType.Other.toString
    }
  }

  def getNewProspectCategoryToMoveToBasedOnReplySentimentTypeData(
                                                                   replySentimentTypeData: ReplySentimentTypeData
                                                                 )(using Logger: SRLogger): Option[ProspectCategory.Value] = {

    val mainType: ReplySentimentType = replySentimentTypeData.getReplySentimentType
    val subCategoryEnum: ReplySentimentSubCategory = replySentimentTypeData.getReplySentimentSubCategory
    val channel: ReplySentimentChannelType = replySentimentTypeData.getReplySentimentChannelType

    mainType match {
      // --- New, Clearly Defined Main Types ---

      case ReplySentimentType.Positive =>
        subCategoryEnum match {
          case ReplySentimentSubCategory.EmailPositiveWantsDemo |
               ReplySentimentSubCategory.EmailPositiveWantsMeeting |
               ReplySentimentSubCategory.CallPositiveConnectedInterestedDemoSet |
               ReplySentimentSubCategory.CallPositiveConnectedInterestedMeetingSet |
               ReplySentimentSubCategory.LinkedInPositiveRepliedInterestedWantsDemoMeeting |
               ReplySentimentSubCategory.GeneralPositiveOutcomeInterestedDemoMeetingScheduled => Some(ProspectCategory.MEETING_BOOKED)
          case ReplySentimentSubCategory.EmailPositiveNeedsMoreInfo |
               ReplySentimentSubCategory.EmailPositiveSpeakToSales |
               ReplySentimentSubCategory.EmailPositiveOtherPositive |
               ReplySentimentSubCategory.CallPositiveConnectedInterestedSendInfo |
               ReplySentimentSubCategory.CallPositiveConnectedSpeakToSalesQualified |
               ReplySentimentSubCategory.LinkedInPositiveRepliedInterestedNeedsMoreInfo |
               ReplySentimentSubCategory.LinkedInPositiveRepliedInterestedOtherPositive |
               ReplySentimentSubCategory.SmsPositiveRepliedInterestedYesWantsInfoCallMe |
               ReplySentimentSubCategory.WhatsAppPositiveRepliedInterestedYesWantsInfoCallMe |
               ReplySentimentSubCategory.GeneralPositiveOutcomeInterestedNeedsFollowUpInfo |
               ReplySentimentSubCategory.CallFollowUpConnectedBusyCallBackRequested  // for old orgs Call back requested is in positive
          => Some(ProspectCategory.INTERESTED)
          case _ =>
            Logger.shouldNeverHappen(s"Inconsistent Data: ReplySentimentType is Positive, but subcategory is ${subCategoryEnum}.")
            None
        }

      case ReplySentimentType.Negative =>
        subCategoryEnum match {
          case ReplySentimentSubCategory.EmailNegativeDoNotContact |
               ReplySentimentSubCategory.EmailNegativeUnsubscribe |
               ReplySentimentSubCategory.CallNegativeConnectedDoNotCallRequest |
               ReplySentimentSubCategory.LinkedInNegativeRepliedDoNotContactRequest |
               ReplySentimentSubCategory.SmsNegativeRepliedStopUnsubscribeDNCRequest |
               ReplySentimentSubCategory.WhatsAppNegativeRepliedStopUnsubscribeDNCRequest |
               ReplySentimentSubCategory.GeneralNegativeOutcomeDoNotContactIndicated => Some(ProspectCategory.DO_NOT_CONTACT)
          case ReplySentimentSubCategory.CallNegativeConnectedWrongNumber |
               ReplySentimentSubCategory.SmsNegativeRepliedWrongNumber |
               ReplySentimentSubCategory.WhatsAppNegativeRepliedWrongNumber |
               ReplySentimentSubCategory.GeneralNegativeOutcomeBadContactInfoConfirmed |
               ReplySentimentSubCategory.CallNegativeCallFailedErrorTechnicalError => Some(ProspectCategory.DELIVERY_FAILED)
          case ReplySentimentSubCategory.EmailNegativeNoNeedBadFit |
               ReplySentimentSubCategory.EmailNegativeUsingCompetitor |
               ReplySentimentSubCategory.EmailNegativeNoBudget |
               ReplySentimentSubCategory.EmailNegativeNotRightPerson |
               ReplySentimentSubCategory.EmailNegativeOtherReason |
               ReplySentimentSubCategory.EmailNegativeSpeakToSupportNotSales |
               ReplySentimentSubCategory.CallNegativeConnectedNotInterestedNoNeedBadFit |
               ReplySentimentSubCategory.CallNegativeConnectedNotInterestedUsingCompetitor |
               ReplySentimentSubCategory.CallNegativeConnectedNotInterestedNoBudget |
               ReplySentimentSubCategory.CallNegativeConnectedNotInterestedNotDecisionMaker |
               ReplySentimentSubCategory.CallNegativeConnectedNotInterestedGeneralObjection |
               ReplySentimentSubCategory.CallNegativeConnectedHungUpRefusedToTalk |
               ReplySentimentSubCategory.CallNegativeGatekeeperAccessDenied |
               ReplySentimentSubCategory.LinkedInNegativeRepliedNotInterestedBadFitNoNeed |
               ReplySentimentSubCategory.LinkedInNegativeRepliedNotInterestedUsingCompetitorOther |
               ReplySentimentSubCategory.LinkedInNegativeRepliedNotRightPerson |
               ReplySentimentSubCategory.SmsNegativeRepliedNotInterestedNoThanks |
               ReplySentimentSubCategory.SmsNegativeRepliedNotRightPerson |
               ReplySentimentSubCategory.WhatsAppNegativeRepliedNotInterestedNoThanks |
               ReplySentimentSubCategory.WhatsAppNegativeRepliedNotRightPerson |
               ReplySentimentSubCategory.GeneralNegativeOutcomeNotInterested |

               ReplySentimentSubCategory.GeneralNegativeOutcomeDisqualified => Some(ProspectCategory.NOT_INTERESTED)
          
          case ReplySentimentSubCategory.CallNegativeConnectedNotInterestedTimingIssues => Some(ProspectCategory.NOT_NOW)
          
          case _ =>
            Logger.shouldNeverHappen(s"Inconsistent Data: ReplySentimentType is Negative, but subcategory is ${subCategoryEnum}.")
            None
        }

      case ReplySentimentType.FollowUpNeeded =>
        subCategoryEnum match {
          case ReplySentimentSubCategory.EmailFollowUpOutOfOffice => Some(ProspectCategory.OUT_OF_OFFICE)
          case ReplySentimentSubCategory.EmailFollowUpAutoReply => Some(ProspectCategory.AUTO_REPLY)
          case ReplySentimentSubCategory.CallFollowUpGatekeeperInformationPassed => Some(ProspectCategory.CONTACTED)
          case ReplySentimentSubCategory.CallFollowUpVoicemailLeft |
                ReplySentimentSubCategory.CallFollowUpNoAnswerNoMessage => Some(ProspectCategory.UNRESPONSIVE)
          case ReplySentimentSubCategory.EmailFollowUpNotRightTime |
               ReplySentimentSubCategory.LinkedInFollowUpRepliedNotRightTime => Some(ProspectCategory.NOT_NOW)
          case ReplySentimentSubCategory.EmailFollowUpReferredSomeone |
               ReplySentimentSubCategory.EmailFollowUpMiscellaneousNeedsClarification |
               ReplySentimentSubCategory.EmailFollowUpForeignLanguage |
               ReplySentimentSubCategory.CallFollowUpConnectedBusyCallBackRequested |
               ReplySentimentSubCategory.CallFollowUpConnectedReferredToSomeoneElse |
               ReplySentimentSubCategory.LinkedInFollowUpRepliedReferredSomeone |
               ReplySentimentSubCategory.LinkedInFollowUpRepliedMiscellaneousNeedsClarification |
               ReplySentimentSubCategory.SmsFollowUpRepliedCallBackLaterSpecificTime |
               ReplySentimentSubCategory.SmsFollowUpRepliedReferredSomeone |
               ReplySentimentSubCategory.SmsFollowUpRepliedMiscellaneousUnclear |
               ReplySentimentSubCategory.WhatsAppFollowUpRepliedCallBackLaterSpecificTime |
               ReplySentimentSubCategory.WhatsAppFollowUpRepliedReferredSomeone |
               ReplySentimentSubCategory.WhatsAppFollowUpRepliedMiscellaneousUnclear |
               ReplySentimentSubCategory.GeneralFollowUpOutcomeFollowUpScheduledAgreed |
               ReplySentimentSubCategory.GeneralFollowUpOutcomeReferralMade => Some(ProspectCategory.REPLIED)
          

          case ReplySentimentSubCategory.GeneralFollowUpOutcomeMoreResearchNeeded => None

          // Fallback for any other FollowUpNeeded, Other
          // Generally, these require some form of engagement or review.

          
          case _ =>
            Logger.shouldNeverHappen(s"Inconsistent Data: ReplySentimentType is FollowUpNeeded, but subcategory is ${subCategoryEnum}.")
            None
        }


      case ReplySentimentType.DoNotContact =>
        // The DNC main type is a terminal signal. Any subcategory under it should result in DNC.
        subCategoryEnum match {
          case ReplySentimentSubCategory.EmailNegativeDoNotContact |
               ReplySentimentSubCategory.EmailNegativeUnsubscribe =>
            Some(ProspectCategory.DO_NOT_CONTACT)
          case _ =>
            Logger.warn(s"Inconsistent Data: ReplySentimentType is DoNotContact, but subcategory is ${subCategoryEnum}. Honoring DNC request.")
            Some(ProspectCategory.DO_NOT_CONTACT)
        }

      case ReplySentimentType.Referral =>
        subCategoryEnum match {
          case ReplySentimentSubCategory.EmailFollowUpReferredSomeone |
               ReplySentimentSubCategory.CallFollowUpConnectedReferredToSomeoneElse => Some(ProspectCategory.REPLIED)
          case _ =>
            Logger.shouldNeverHappen(s"Inconsistent Data: ReplySentimentType is Referral, but subcategory is ${subCategoryEnum}.")
            None
        }

      case ReplySentimentType.Objection =>
        subCategoryEnum match {
          case ReplySentimentSubCategory.CallNegativeConnectedWrongNumber => Some(ProspectCategory.DELIVERY_FAILED)
          case ReplySentimentSubCategory.EmailFollowUpNotRightTime |
               ReplySentimentSubCategory.CallNegativeConnectedNotInterestedTimingIssues => Some(ProspectCategory.NOT_NOW)
          case ReplySentimentSubCategory.EmailNegativeNoNeedBadFit |
               ReplySentimentSubCategory.EmailNegativeUsingCompetitor |
               ReplySentimentSubCategory.EmailNegativeNoBudget |
               ReplySentimentSubCategory.EmailNegativeNotRightPerson |
               ReplySentimentSubCategory.EmailNegativeOtherReason |
               ReplySentimentSubCategory.CallNegativeConnectedHungUpRefusedToTalk => Some(ProspectCategory.NOT_INTERESTED)
          case _ =>
            Logger.shouldNeverHappen(s"Inconsistent Data: ReplySentimentType is Objection, but subcategory is ${subCategoryEnum}.")
            None
        }

      case ReplySentimentType.Other =>
        subCategoryEnum match {
          case ReplySentimentSubCategory.EmailFollowUpOutOfOffice => Some(ProspectCategory.OUT_OF_OFFICE)
          case ReplySentimentSubCategory.EmailFollowUpAutoReply => Some(ProspectCategory.AUTO_REPLY)
          case ReplySentimentSubCategory.EmailNegativeSpeakToSupportNotSales => Some(ProspectCategory.NOT_INTERESTED)
          case ReplySentimentSubCategory.CallFollowUpVoicemailLeft => Some(ProspectCategory.UNRESPONSIVE)
          case ReplySentimentSubCategory.CallNegativeCallFailedErrorTechnicalError => Some(ProspectCategory.DELIVERY_FAILED)
          case ReplySentimentSubCategory.EmailFollowUpForeignLanguage |
               ReplySentimentSubCategory.EmailFollowUpMiscellaneousNeedsClarification => Some(ProspectCategory.REPLIED)
          case ReplySentimentSubCategory.CallFollowUpConnectedBusyCallBackRequested => Some(ProspectCategory.REPLIED)
          case ReplySentimentSubCategory.CallFollowUpNoAnswerNoMessage => Some(ProspectCategory.UNRESPONSIVE)
          case _ =>
            Logger.shouldNeverHappen(s"Inconsistent Data: ReplySentimentType is Other, but subcategory is ${subCategoryEnum}.")
            None
        }

      // This case is now only reachable if a new, unhandled ReplySentimentType is added to the enum without updating this function.
      case _ =>
        Logger.shouldNeverHappen(s"Exhaustive match failed: Unhandled main ReplySentimentType: ${mainType}. This function needs to be updated.")
        None
    }
  }

   def findOutOfOfficeSentiment(sentiments: List[ReplySentimentForTeam]): Option[ReplySentimentForTeam] = {


     sentiments.find { sentiment =>
      val displayName = sentiment.reply_sentiment.getReplySentimentType.display_name
      val subcategory = sentiment.reply_sentiment.getSubcategoryName
      val isEmailChannelType = sentiment.reply_sentiment.getReplySentimentChannelType == ReplySentimentChannelType.EmailChannelType

      val isOutOfOfficeSubcategory = subcategory == ReplySentimentSubCategory.EmailFollowUpOutOfOffice.key

       isEmailChannelType && isOutOfOfficeSubcategory

    }
  }

   def findUnsubscribeSentiment(sentiments: List[ReplySentimentForTeam]): Option[ReplySentimentForTeam] = {



     sentiments.find { sentiment =>
      val displayName = sentiment.reply_sentiment.getReplySentimentType.display_name
      val subcategory = sentiment.reply_sentiment.getSubcategoryName
      val isEmailChannelType = sentiment.reply_sentiment.getReplySentimentChannelType == ReplySentimentChannelType.EmailChannelType

      val isUnsubscribedSubcategory = subcategory == ReplySentimentSubCategory.EmailNegativeUnsubscribe.key
      isEmailChannelType && isUnsubscribedSubcategory // We only have Unsubscribed SubCategory Match have removed the DNC check for this
    }
  }

   def findAutoReplySentiment(sentiments: List[ReplySentimentForTeam]): Option[ReplySentimentForTeam] = {



     sentiments.find { sentiment =>
      val displayName = sentiment.reply_sentiment.getReplySentimentType.display_name
      val subcategory = sentiment.reply_sentiment.getSubcategoryName
       val isEmailChannelType = sentiment.reply_sentiment.getReplySentimentChannelType == ReplySentimentChannelType.EmailChannelType
       val isAutoReplySubcategory = subcategory == ReplySentimentSubCategory.EmailFollowUpAutoReply.key

       isEmailChannelType && isAutoReplySubcategory
    }
  }

  



}
