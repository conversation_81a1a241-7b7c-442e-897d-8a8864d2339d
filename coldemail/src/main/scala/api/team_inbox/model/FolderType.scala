package api.team_inbox.model


import api.emails.models.InboxType
import play.api.libs.json._

import scala.util.{Failure, Success, Try}

//This enum is for db folder_type column in email_threads table (it is not having sent folder)
sealed abstract class FolderType {
  val textId: String

  override def toString: String = textId
}


object FolderType {

  private val KEY_PROSPECTS = "prospects"
  private val KEY_NON_PROSPECTS = "non-prospects"
  private val KEY_DONE = "done"
  private val KEY_SNOOZED = "snoozed"
  private val KEY_IRRELEVANT = "irrelevant"

  case object PROSPECTS extends FolderType {
    override val textId: String = KEY_PROSPECTS
  }

  case object NON_PROSPECTS extends FolderType {
    override val textId: String = KEY_NON_PROSPECTS
  }

  case object DONE extends FolderType {
    override val textId: String = KEY_DONE
  }

  case object SNOOZED extends FolderType {
    override val textId: String = KEY_SNOOZED
  }

  case object IRRELEVANT extends FolderType {
    override val textId: String = KEY_IRRELEVANT
  }


  def withName(name: String): Try[FolderType] = Try {

    name.toLowerCase.trim match {

      case KEY_PROSPECTS => PROSPECTS

      case KEY_NON_PROSPECTS => NON_PROSPECTS

      case KEY_DONE => DONE

      case KEY_SNOOZED => SNOOZED

      case KEY_IRRELEVANT => IRRELEVANT

      case _ => throw new Exception(s"Invalid Folder type: $name")
    }

  }

  implicit def writes: Writes[FolderType] = new Writes[FolderType] {
    def writes(rs: FolderType): JsValue = {
      JsString(rs.textId)
    }
  }

  implicit def reads: Reads[FolderType] = new Reads[FolderType] {

    def reads(json: JsValue): JsResult[FolderType] = json match {

      case JsString(s) =>

        withName(name = s) match {

          case Failure(exception) =>
            JsError(s"Enumeration expected of type: FolderType, but it does not appear to contain the value: '$s'")

          case Success(value) =>
            JsSuccess(value)
        }

      case _ =>
        JsError("String value expected")

    }
  }


}



