package api.team_inbox.model

import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>sR<PERSON>ult, JsString, JsSuccess, JsV<PERSON>ue, <PERSON>s, Writes}

import scala.util.{Failure, Success, Try}

enum ReplySentimentUpdatedBy(val key: String) {
  case User extends ReplySentimentUpdatedBy("user")
  case SmartreachAi extends ReplySentimentUpdatedBy("smartreach_ai")
  override def toString: String = key
}

object ReplySentimentUpdatedBy {

  def fromKey(actionStatus: String): Try[ReplySentimentUpdatedBy] = Try {
    actionStatus match {
      case ReplySentimentUpdatedBy.User.key => ReplySentimentUpdatedBy.User
      case ReplySentimentUpdatedBy.SmartreachAi.key => ReplySentimentUpdatedBy.SmartreachAi
      case _ => throw new IllegalArgumentException(s"Invalid updater: $actionStatus")
    }
  }

  def toKey(value: ReplySentimentUpdatedBy): String = value.toString

  implicit val reads: Reads[ReplySentimentUpdatedBy] = new Reads[ReplySentimentUpdatedBy] {
    override def reads(json: JsValue): JsResult[ReplySentimentUpdatedBy] = {
      fromKey(json.as[String]) match {
        case Success(status) => JsSuccess(status)
        case Failure(d) => JsError(s"Invalid ReplySentimentUpdatedBy value: ${d.getMessage}")
      }
    }
  }

  implicit val writes: Writes[ReplySentimentUpdatedBy] = new Writes[ReplySentimentUpdatedBy] {
    override def writes(status: ReplySentimentUpdatedBy): JsValue = JsString(toKey(status))
  }
}

