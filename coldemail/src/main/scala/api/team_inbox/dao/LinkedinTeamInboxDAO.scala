package api.team_inbox.dao
import api.team_inbox.model.{TeamInbox, TeamInboxDetails, TeamInboxes}
import scalikejdbc._
import sr_scheduler.models.ChannelType

import scala.util.Try

class LinkedinTeamInboxDAO extends TeamInboxDAOTrait {

  override def getTeamInboxIdForThread(thread_id: Long, teamId: Long): Try[Option[Long]] = Try {

    DB readOnly { implicit session =>
      sql"""
           select ti.id from team_inbox ti
           inner join linkedin_message_threads lmt on ti.linkedin_setting_id = lmt.linkedin_setting_id

           where lmt.id = $thread_id and lmt.team_id = $teamId;
         """
        .map(rs => rs.long("id"))
        .single
        .apply()
    }

  }

  override def getAllTeamInboxesInTeam(team_id: Long, org_id: Long): Try[List[TeamInboxes]] = Try {

    DB readOnly { implicit session =>
      sql"""
           SELECT
              ti.id,
              ti.name,
              ls.email,
              CONCAT(a.first_name, ' ', a.last_name) as owner_name,
              ${ChannelType.LinkedinChannel.toString} as channel_type

           FROM team_inbox ti
           INNER JOIN linkedin_settings ls on ls.id = ti.linkedin_setting_id
           INNER JOIN accounts a on a.id = ls.owner_account_id

           WHERE ti.org_id = $org_id and ls.team_id = $team_id;
         """
        .map(TeamInboxes.fromDb)
        .list
        .apply()
    }

  }

  override def getTeamInboxDetails(team_inbox_id: Long, org_id: Long): Try[Option[TeamInboxDetails]] = Try {
    DB readOnly { implicit session =>
      sql"""
           SELECT ti.id, ti.name, ls.email, ${ChannelType.LinkedinChannel.toString} as channel_type from team_inbox ti
           inner join linkedin_settings ls on ls.id = ti.linkedin_setting_id
           where ti.id = $team_inbox_id and ti.org_id = $org_id;
         """
        .map(TeamInboxDetails.fromDb)
        .single
        .apply()
    }
  }

  override def getTeamIdForTeamInbox(team_inbox_id: Long, team_id: Long, org_id: Long): Try[Long] = Try {
    DB readOnly { implicit session =>
      sql"""
            select ls.team_id from linkedin_settings ls
            join team_inbox t on t.linkedin_setting_id = ls.id
            where t.id = $team_inbox_id and t.org_id = $org_id and ls.team_id = $team_id
         """
        .map(_.long("team_id"))
        .single
        .apply()
        .get
    }
  }

  override def getOwnerOfTeamInbox(team_inbox_id: Long, org_id: Long): Try[Long] = Try {
    DB readOnly { implicit session =>
      sql"""
            select owner_account_id from linkedin_settings ls
            join team_inbox ti on ls.id = ti.linkedin_setting_id
            where ti.id = $team_inbox_id and ti.org_id = $org_id;
         """
        .map(_.long("owner_account_id"))
        .single
        .apply()
        .get
    }
  }

  override def findTeamInboxesForAccount(account_id: Long, teamId: Long): Try[List[TeamInbox]] = Try {
    DB readOnly { implicit session =>
      sql"""
            SELECT * FROM
            (
             (
             SELECT

             ti.*, ls.email

             FROM team_inbox ti

             INNER JOIN linkedin_settings ls on ls.id = ti.linkedin_setting_id
             WHERE ls.owner_account_id = $account_id and ls.team_id = $teamId
             )
              union
             (
             SELECT

             ti.*, ls.email

             FROM team_inbox ti

             INNER JOIN linkedin_settings ls on ls.id = ti.linkedin_setting_id and ls.team_id = $teamId
             INNER JOIN team_inbox_permissions tp on ti.id = tp.team_inbox_id
             INNER JOIN teams_accounts ta on ta.user_role_id = tp.user_role_id

             WHERE ta.account_id = $account_id and ta.team_id = $teamId
             )
            ) as t;
         """
        .map(TeamInbox.fromDb)
        .list
        .apply()
    }
  }

}
