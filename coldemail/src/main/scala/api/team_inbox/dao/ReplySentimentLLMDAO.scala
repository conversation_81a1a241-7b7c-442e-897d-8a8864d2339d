package api.team_inbox.dao

import api.AppConfig
import api.accounts.TeamId
import api.columns.ProspectColGenStatus
import api.columns.models.ColumnDefsProspectsId
import api.emails.EmailReplySavedV3
import api.llm.dao.{LlmAuditLogInsertData, LlmAuditLogDAO}
import api.llm.models.LlmFlow
import api.prospects.models.ProspectId
import api.sr_ai.models.{AiApiOptions, AiModel, SrAiApiFailedException, TokenUsage}
import api.team_inbox.model.{ReplySentimentChannelType, ReplySentimentType, ReplySentimentTypeData}
import play.api.libs.json.{JsError, JsLookupResult, JsObject, JsSuccess, JsValue, <PERSON>son, OWrites, Reads, Writes}
import play.api.libs.ws.WSClient
import utils.{CommandExecutor, SRLogger}
import sr_scheduler.models.ChannelType
import eventframework.MessageObject
import eventframework.MessageObject.EmailMessageObject
import play.api.libs.ws.writeableOf_JsValue

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

import api.team_inbox.service.ReplySentimentForTeam


/**
 * Response from AI sentiment analysis
 */
case class AIReplyClassificationResult(
                                        sentiment_type: String, 
                                        sentiment: String, 
                                        confidence: Double,
                                        token_usage: TokenUsage,
                                      )
object AIReplyClassificationResult {
  implicit val reads: Reads[AIReplyClassificationResult] = Json.reads[AIReplyClassificationResult]
}

/**
 * Request payload for sentiment analysis API
 */
case class SentimentAnalysisRequest(
  conversationMessages: Seq[ConversationMessage],
  options: AiApiOptions,
  replySentimentsForTeam: List[String]
)

/**
 * Conversation message for sentiment analysis
 */
case class ConversationMessage(
  sender: String,
  content: String
)

object SentimentAnalysisRequest {
  implicit val writes: Writes[SentimentAnalysisRequest] = Json.writes[SentimentAnalysisRequest]
}

object ConversationMessage {
  implicit val writes: Writes[ConversationMessage] = Json.writes[ConversationMessage]
}

class  ReplySentimentLLMDAO(
                            llmAuditLogDAO: LlmAuditLogDAO
                          ) {
  /**
   * Analyzes a sequence of email messages and returns the sentiment of the latest reply
   *
   * @param messages The email messages to analyze (in chronological order)
   * @param aiConfig Configuration for the AI provider
   * @param ec       Execution context for async operations
   * @param logger   Logger for logging operations
   * @return Future containing the ReplySentimentTypeData or None if no sentiment is detected
   */
  def analyzeEmailConversation(
                                messages: Seq[EmailMessageObject],
                                aiConfig: AiApiOptions,
                                teamId: TeamId,
                                primaryProspectId: Long,
                                replySentimentsForTeam : List[String]
                              )(implicit ec: ExecutionContext, wsClient: WSClient, logger: SRLogger): Future[Option[ReplySentimentTypeData]] = {

    if (messages.isEmpty) {
      logger.info("[ReplySentimentLLMDAO.analyzeEmailConversation] No messages provided for analysis")
      return Future.successful(None)
    }

    // Convert messages to conversation format
    val conversationMessages = messages.map { message =>
      ConversationMessage(
        sender = if (message.from_user) "User" else "Prospect",
        content = message.body
      )
    }

    // Create the request payload
    val request = Json.toJson(SentimentAnalysisRequest(
      conversationMessages = conversationMessages,
      options = aiConfig,
      replySentimentsForTeam = replySentimentsForTeam
    ))

    // Get the API URL from configuration
    val apiUrl = s"${AppConfig.srAiApiBaseUrl}/api/sentiment"

    logger.info(s"[ReplySentimentLLMDAO.analyzeEmailConversation] Calling sentiment analysis API at: $apiUrl with ${messages.size} messages")

    // Call the API
    wsClient.url(apiUrl)
      .withHttpHeaders(
        "X-TRACE-ID" -> logger.logTraceId
      )
      .post(request)
      .flatMap { response =>
        if (response.status == 200) {
          // Parse the response
          Try(response.json.as[AIReplyClassificationResult]) match {
            case Success(classificationResult) =>
              logger.info(s"[ReplySentimentLLMDAO.analyzeEmailConversation] Parsed classification result: $classificationResult")

              // Convert to ReplySentimentTypeData
              if (classificationResult.sentiment_type == "none") {
                Future.successful(None)
              } else {
                // Extract the sentiment name (everything after the colon in the sentiment string)
                val sentimentName = classificationResult.sentiment.split(": ").lastOption.getOrElse(
                  throw new IllegalArgumentException(s"Invalid sentiment format: ${classificationResult.sentiment}")
                )

                // Convert to ReplySentimentTypeData using fromKey
                ReplySentimentTypeData.fromKey(
                  replySentimentTypeString = classificationResult.sentiment_type,
                  replySentimentName = sentimentName,
                  replySentimentChannelType = ReplySentimentChannelType.EmailChannelType.toString
                ) match {
                  case Success(sentimentData) => Future.successful(Some(sentimentData))
                  case Failure(e) =>
                    logger.shouldNeverHappen(s"[ReplySentimentLLMDAO.analyzeEmailConversation] Failed to convert classification result: ${e.getMessage}")
                    Future.failed(e)
                }
              }
            case Failure(e) =>
              logger.shouldNeverHappen(s"[ReplySentimentLLMDAO.analyzeEmailConversation] Failed to parse classification result: ${e.getMessage}")
              Future.failed(e)
          }
        } else {
          logger.shouldNeverHappen(s"[ReplySentimentLLMDAO.analyzeEmailConversation] API call failed with status ${response.status}: ${response.body}")
          Future.failed(
            SrAiApiFailedException(
              message = s"API call failed with status ${response.status}: ${response.body}",
              statusCode = response.status,
            )
          )
        }
      }
      .recoverWith {
        case e =>
          logger.shouldNeverHappen(s"[ReplySentimentLLMDAO.analyzeEmailConversation] Error calling sentiment analysis API: ${e.getMessage}")
          Future.failed(e)
      }
  }

  /**
   * Logs the AI sentiment analysis to the LLM audit log
   *
   * @param messages             The email messages that were analyzed
   * @param classificationResult The result of the AI sentiment analysis
   */
  private def logToAuditLog(
                             messages: Seq[EmailMessageObject],
                             classificationResult: AIReplyClassificationResult,
                             teamId: TeamId,
                             model: AiModel,
                             primaryProspectId: Long
                           )(implicit ec: ExecutionContext, logger: SRLogger): Unit = {
    // For MessageObject, we don't have direct access to team_id and prospect_id
    // This would need to be provided separately or extracted from the messages
    
    // Determine the status based on the sentiment type
    val status = classificationResult.sentiment_type match {
      case "none" => ProspectColGenStatus.Failed
      case _ => ProspectColGenStatus.Completed
    }

    // Create the audit log entry
    // Note: This is a simplified version. In a real implementation, you would need to
    // extract team_id and prospect_id from the messages or pass them as parameters
    val auditLog = LlmAuditLogInsertData(
      team_id = teamId,
      prospect_id = ProspectId(primaryProspectId),
      llm_tool = model,
      status = status,
      flow = LlmFlow.ReplySentiment,
      column_def_id = None,
      consumed_token_count_prompt_input = classificationResult.token_usage.prompt_tokens,
      consumed_token_count_generation_output = classificationResult.token_usage.completion_tokens,
      request_log_id = logger.logRequestId,
      queue_message = Json.obj(),
    )

    // Log asynchronously (don't block or wait for the result)
    llmAuditLogDAO.insertLlmAuditLog(List(auditLog)) match {
      case Success(_) =>
//        logger.info(s"[ReplySentimentLLMDAO.logToAuditLog] Successfully logged LLM usage for sentiment analysis")
      case Failure(e) =>
        logger.shouldNeverHappen(s"[ReplySentimentLLMDAO.logToAuditLog] Failed to log LLM usage", Some(e))
    }
  }
}