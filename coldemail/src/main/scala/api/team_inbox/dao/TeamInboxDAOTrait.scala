package api.team_inbox.dao

import api.team_inbox.model.{TeamInbox, TeamInboxDetails, TeamInboxes}
import scalikejdbc.{DB, DBSession, scalikejdbcSQLInterpolationImplicitDef}
import utils.dbutils.SQLUtils
import api.accounts.TeamId
import sr_scheduler.models.ChannelType

import scala.util.Try

case class SettingIdsAndChannelType(esets: Seq[Long], lsets: Seq[Long] ,channelType: Option[ChannelType], are_all_esets_accessible: Boolean = false)

trait TeamInboxDAOTrait {

  def getTeamInboxIdForThread(
                               thread_id: Long,
                               teamId: Long,
                             ): Try[Option[Long]]

  def getAllTeamInboxesInTeam(team_id: Long, org_id: Long): Try[List[TeamInboxes]]

  def getTeamInboxDetails(team_inbox_id: Long, org_id: Long): Try[Option[TeamInboxDetails]]

  def getPermittedUserRolesForInbox(team_inbox_id: Long, org_id: Long): Try[List[UserRoles]] = Try {
    DB readOnly { implicit session =>
      sql"""
            SELECT ur.id, ur.role_name from user_roles ur
            inner join team_inbox_permissions tp on ur.id = tp.user_role_id

            where tp.team_inbox_id = $team_inbox_id and tp.org_id = $org_id;
         """
        .map(UserRoles.fromDb)
        .list
        .apply()
    }
  }

  def getTeamIdForTeamInbox(team_inbox_id: Long, team_id: Long, org_id: Long): Try[Long]

  def getOwnerOfTeamInbox(team_inbox_id: Long, org_id: Long): Try[Long]

  def checkTeamInboxAccess(account_id: Long, team_inbox_id: Long, team_id: Long): Try[Option[Long]] = Try {
    DB readOnly { implicit session =>
      sql"""
            select tp.user_role_id from team_inbox_permissions tp
            inner join teams_accounts ta
            on ta.user_role_id = tp.user_role_id

            where tp.team_inbox_id = $team_inbox_id
            and ta.account_id = $account_id
            and ta.team_id = $team_id;
         """
        .map(_.long("user_role_id"))
        .single
        .apply()
    }
  }

  def updateTeamInboxName(
                           team_inbox_id: Long,
                           name: Option[String],
                           org_id: Long
                         ): Try[Option[Long]] = Try {

    name match {
      case None => Some(0)
      case Some(name) =>
        DB autoCommit { implicit session =>
          sql"""
           UPDATE team_inbox

           SET
           name = $name

           WHERE id = $team_inbox_id and org_id = $org_id
           returning id;
         """
            .map(rs => rs.long("id"))
            .single
            .apply()
        }
    }
  }

  def getUsersOfTeamInbox(
                           team_inbox_id: Long,
                           org_id: Long
                         ): Try[List[Long]] = Try {
    DB readOnly { implicit session =>

      sql"""
           SELECT user_role_id from team_inbox_permissions
           WHERE team_inbox_id = $team_inbox_id and org_id = $org_id;
         """
        .map(rs => rs.long("user_role_id"))
        .list
        .apply()

    }
  }

  def deleteTeamInboxPermission(
                                 team_inbox_id: Long,
                                 user_role_ids: Seq[Long],
                                 org_id: Long
                               ): Try[Seq[Long]] = Try {
    if (user_role_ids.isEmpty) {
      Seq()
    } else {
      DB autoCommit { implicit session =>
        sql"""
           DELETE from team_inbox_permissions

           WHERE team_inbox_id = $team_inbox_id and org_id = $org_id
           and user_role_id in ${SQLUtils.generateSQLValuesClause(user_role_ids)}
           returning id;
         """
          .map(rs => rs.long("id"))
          .list
          .apply()
      }
    }
  }

  def insertTeamInboxPermissions(
                                  team_inbox_id: Long,
                                  user_role_ids: List[Long],
                                  org_id: Long
                                )(implicit session: DBSession): Try[List[Long]] = Try {
    user_role_ids.map(r => {
      sql"""
           INSERT INTO team_inbox_permissions
           (
           team_inbox_id,
           user_role_id,
           org_id
           )
           VALUES
           (
           $team_inbox_id,
           $r,
           $org_id
           )
           returning id;
         """
        .map(rs => rs.long("id"))
        .single
        .apply()
        .get
    })
  }

  def insertTeamInboxPermissionsAC(
                                    team_inbox_id: Long,
                                    user_role_ids: List[Long],
                                    org_id: Long
                                  ): Try[List[Long]] = {
    DB autoCommit { implicit session =>
      insertTeamInboxPermissions(
        team_inbox_id = team_inbox_id,
        user_role_ids = user_role_ids,
        org_id = org_id
      )
    }
  }

  def findTeamInboxesForAccount(account_id: Long, teamId: Long): Try[List[TeamInbox]]

  def getSettingIdsAndChannelType(team_inbox_id: Long, teamId: TeamId): Try[Option[SettingIdsAndChannelType]] = Try{

    DB readOnly {implicit session =>
      sql"""
           SELECT email_setting_id, linkedin_setting_id
           FROM team_inbox
           WHERE id = $team_inbox_id
           AND team_id = ${teamId.id}
           ;
           """
        .map(rs => {
          val emailSettingIdOpt = rs.longOpt("email_setting_id")
          val linkedinSettingIdOpt = rs.longOpt("linkedin_setting_id")

          if (emailSettingIdOpt.isDefined) {
            Some(SettingIdsAndChannelType(
              esets = Seq(emailSettingIdOpt.get),
              lsets = Seq(),
              channelType = Some(ChannelType.EmailChannel)
            ))
          }
          else if (linkedinSettingIdOpt.isDefined) {
            Some(SettingIdsAndChannelType(
              esets = Seq(),
              lsets = Seq(linkedinSettingIdOpt.get),
              channelType = Some(ChannelType.LinkedinChannel)
            ))
          }
          else {
            None
          }
        })
        .single
        .apply()
        .flatten
    }

  }

}
