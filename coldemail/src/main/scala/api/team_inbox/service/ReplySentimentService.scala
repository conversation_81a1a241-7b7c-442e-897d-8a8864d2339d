package api.team_inbox.service

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.AccountId
import api.call.models.{CallSID, ConferenceUuid}
import api.campaigns.CampaignProspectDAO
import api.campaigns.services.CampaignId
import api.emails.EmailReplySavedV3
import api.emails.dao_service.EmailThreadDAOService
import api.emails.{EmailThreadDAO, team_inbox_conversations}
import api.linkedin_message_threads.LinkedinMessageThreadsDAO
import api.team_inbox.model.{ReplySentimentChannelType, ReplySentimentType, ReplySentimentTypeData, ReplySentimentUpdatedBy}
import play.api.libs.json.*
import utils.{Helpers, SRLogger}
import utils.dbutils.DBUtils
import api.prospects.dao.ProspectDAO
import api.prospects.models.{ProspectCategory, ProspectCategoryNew, ProspectId}
import api.sr_ai.models.AiApiOptions
import api.sr_audit_logs.models.EventType
import api.tasks.models.TaskType
import api.tasks.services.TaskUuid
import api.team_inbox.dao_service.ReplySentimentDAOService
import eventframework.MessageObject
import eventframework.MessageObject.EmailMessageObject
import play.api.libs.ws.WSClient
import sr_scheduler.models.ChannelType
import utils.mq.task.{MqForSaveReplySentimentPublisher, SaveReplySentimentData}
import utils.mq.webhook.mq_activity_trigger.{MQActivityTriggerMsgForm, MQActivityTriggerPublisher}
import api.accounts.models.OrgId
import utils.mq.prospect_category.{MqAutoUpdateProspectCategoryMsg, MqAutoUpdateProspectCategoryPublisher}

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success, Try}
import scala.concurrent.Future

case class ReplySentimentUuid(uuid: String) extends AnyVal {
  override def toString: String = uuid
}

object ReplySentimentUuid {
  implicit val reads: Reads[ReplySentimentUuid] = new Reads[ReplySentimentUuid] {
    override def reads(json: JsValue): JsResult[ReplySentimentUuid] = {
      json match {
        case JsString(uuid) => JsSuccess(ReplySentimentUuid(uuid = uuid))
        case randomValue => JsError(s"expected String, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[ReplySentimentUuid] = new Writes[ReplySentimentUuid] {
    override def writes(o: ReplySentimentUuid): JsValue = JsString(o.uuid)
  }
}

case class ReplySentimentForTeam(
                                  uuid: ReplySentimentUuid,
                                  reply_sentiment: ReplySentimentTypeData
                                )

object ReplySentimentForTeam {
  given format: OFormat[ReplySentimentForTeam] = Json.format[ReplySentimentForTeam]
}


case class ReplySentimentForTeamReport(
                                        uuid: ReplySentimentUuid,
                                        reply_sentiment: ReplySentimentTypeData,
                                        count: Int
                                      )

object ReplySentimentForTeamReport {
  given format: OFormat[ReplySentimentForTeamReport] = Json.format[ReplySentimentForTeamReport]
}

case class ReplySentimentForTeamReportGrouped(
                                               reply_sentiment_type: ReplySentimentType,
                                               reply_sentiment_for_team_report: List[ReplySentimentForTeamReport],
                                               count: Int
                                             )

object ReplySentimentForTeamReportGrouped {
  given format: OFormat[ReplySentimentForTeamReportGrouped] = Json.format[ReplySentimentForTeamReportGrouped]
}

case class UpdateReplySentimentForm(
                                     team_inbox_conversations: List[team_inbox_conversations],
                                     reply_sentiment_uuid: ReplySentimentUuid
                                   )


sealed trait UpdateReplySentimentFormForDAO {
  def getNoProspectThreadList: List[UpdateReplySentimentFormForDAO.NoProspectThread]

  def getCampaignProspectThreadList: List[UpdateReplySentimentFormForDAO.CampaignProspectThread]


  def getProspectThreadList: List[UpdateReplySentimentFormForDAO.ProspectThread]

  val email_thread_id: Long
}

object UpdateReplySentimentFormForDAO {

  case class NoProspectThread(
                               email_thread_id: Long
                             ) extends UpdateReplySentimentFormForDAO {
    override def getNoProspectThreadList: List[NoProspectThread] = {
      List(NoProspectThread(
        email_thread_id = email_thread_id
      ))
    }

    override def getCampaignProspectThreadList: List[UpdateReplySentimentFormForDAO.CampaignProspectThread] = List()

    override def getProspectThreadList: List[UpdateReplySentimentFormForDAO.ProspectThread] = List()
  }

  case class CampaignProspectThread(
                                     campaign_id: Long,
                                     prospect_id: List[Long],
                                     email_thread_id: Long
                                   ) extends UpdateReplySentimentFormForDAO {
    override def getNoProspectThreadList: List[NoProspectThread] = {
      List(NoProspectThread(
        email_thread_id = email_thread_id
      ))
    }

    override def getCampaignProspectThreadList: List[UpdateReplySentimentFormForDAO.CampaignProspectThread] = List(
      CampaignProspectThread(
        campaign_id = campaign_id,
        prospect_id = prospect_id,
        email_thread_id = email_thread_id
      )
    )


    override def getProspectThreadList: List[UpdateReplySentimentFormForDAO.ProspectThread] = List(
      ProspectThread(
        prospect_id = prospect_id,
        email_thread_id = email_thread_id
      )
    )


  }


  case class ProspectThread(
                             prospect_id: Seq[Long],
                             email_thread_id: Long
                           ) extends UpdateReplySentimentFormForDAO {
    override def getNoProspectThreadList: List[NoProspectThread] = {
      List(NoProspectThread(
        email_thread_id = email_thread_id
      ))
    }

    override def getCampaignProspectThreadList: List[UpdateReplySentimentFormForDAO.CampaignProspectThread] = List()


    override def getProspectThreadList: List[UpdateReplySentimentFormForDAO.ProspectThread] = List(
      ProspectThread(
        prospect_id = prospect_id,
        email_thread_id = email_thread_id,
      )
    )


  }


  def getUpdateReplySentiment(
                               campaign_id: Option[Long],
                               prospect_id: List[Long],
                               email_thread_id: Long,
                             ): UpdateReplySentimentFormForDAO = {

    if (prospect_id.nonEmpty) {
      if (campaign_id.isDefined) {
        UpdateReplySentimentFormForDAO.CampaignProspectThread(
          campaign_id = campaign_id.get,
          prospect_id = prospect_id,
          email_thread_id = email_thread_id,
        )
      } else {
        UpdateReplySentimentFormForDAO.ProspectThread(
          prospect_id = prospect_id,
          email_thread_id = email_thread_id,
        )
      }

    } else {
      UpdateReplySentimentFormForDAO.NoProspectThread(
        email_thread_id = email_thread_id,
      )
    }

  }
}

object UpdateReplySentimentForm {
  implicit val reads: Reads[UpdateReplySentimentForm] = Json.reads[UpdateReplySentimentForm]
}

class ReplySentimentService(
                             emailThreadDAOService: EmailThreadDAOService,
                             linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO,
                             campaignProspectDAO: CampaignProspectDAO,
                             dbUtils: DBUtils,
                             replySentimentDAOService: ReplySentimentDAOService,
                             prospectDAO: ProspectDAO,
                             mqActivityTriggerPublisher: MQActivityTriggerPublisher,
                             mqForSaveReplySentimentPublisher: MqForSaveReplySentimentPublisher,
                             mqAutoUpdateProspectCategoryPublisher: MqAutoUpdateProspectCategoryPublisher
                           ) {

  // should send the id of the updated or added row
  def addOrUpdateReplySentiment(
                                 updateReplySentimentFormForDAO: List[UpdateReplySentimentFormForDAO],
                                 channelType: ChannelType,
                                 team_id: Long,
                                 reply_sentiment_uuid: ReplySentimentUuid,
                                 accountId: Long,
                                 reply_sentiment_updated_by: ReplySentimentUpdatedBy
                               )(using Logger: SRLogger): Try[true] = {
    val dbAndSession = dbUtils.startLocalTx()

    val db = dbAndSession.db
    val session = dbAndSession.session
    val prospectThreads: List[UpdateReplySentimentFormForDAO.ProspectThread] = updateReplySentimentFormForDAO.flatMap(_.getProspectThreadList)

    val result: Try[true] = for {
      addingToEmailThread: List[Long] <- {
        val noProspectThreads: List[UpdateReplySentimentFormForDAO.NoProspectThread] = updateReplySentimentFormForDAO.flatMap(_.getNoProspectThreadList)
        if (noProspectThreads.isEmpty) {
          Success(List())
        } else {
          // TODO: Use a common function for this
          val messageThreadsDAOTry = channelType match {
            case ChannelType.EmailChannel =>
              Success(emailThreadDAOService)

            case ChannelType.LinkedinChannel =>
              Success(linkedinMessageThreadsDAO)

            case _ =>
              Failure(new Exception(s"Reply Sentiments not supported for ${channelType.toString}"))
          }

          messageThreadsDAOTry.flatMap(messageThreadsDAO => {
            messageThreadsDAO.addReplySentimentToEmailThread(
              threadIds = noProspectThreads.map(_.email_thread_id),
              team_id = team_id,
              reply_sentiment_uuid = reply_sentiment_uuid,
              reply_sentiment_updated_by = reply_sentiment_updated_by
            )(session)
          })
        }
      }
      addingToCampaignProspect: Seq[Long] <- {
        val campaignProspectThreads: List[UpdateReplySentimentFormForDAO.CampaignProspectThread] = updateReplySentimentFormForDAO.flatMap(_.getCampaignProspectThreadList)
        if (campaignProspectThreads.isEmpty) {
          Success(List())
        } else {
          val listOfTry: List[Try[List[Long]]] = campaignProspectThreads.map(prospectThread => {
            campaignProspectDAO.addReplySentiment(
              prospectThread = prospectThread,
              reply_sentiment_uuid = reply_sentiment_uuid,
              teamId = TeamId(id = team_id),
              reply_sentiment_updated_by = reply_sentiment_updated_by
            )(session)
          })
          Helpers.seqTryToTrySeq(listOfTry).map(_.flatten)

        }
      }
      addingToProspect: List[Long] <- {
        if (prospectThreads.isEmpty) {
          Success(List())
        } else {
          prospectDAO.addingLatestReplySentimentForProspect(
            team_id = team_id,
            prospectIds = prospectThreads.flatMap(_.prospect_id),
            reply_sentiment_uuid = reply_sentiment_uuid
          )(session)
        }
      }
      addingToQueue: Unit <- Try {
        updateReplySentimentFormForDAO.foreach { form =>
          mqActivityTriggerPublisher.publishEvents(message = MQActivityTriggerMsgForm(
            accountId = accountId,
            teamId = team_id,
            event = EventType.REPLY_SENTIMENT_UPDATED.toString,
            campaignId = None,
            prospectIds = Seq(),
            clickedUrl = None,
            campaignName = None,
            emailScheduledIds = None,
            threadId = Some(form.email_thread_id),
            replySentimentUuid = Some(reply_sentiment_uuid),
          ))
        }
      }

    } yield {
      true
    }
    dbUtils.commitAndCloseSession(db = db)

    result match {
      case Failure(exception) => Failure(exception)

      case Success(_) =>
        mqAutoUpdateProspectCategoryPublisher.publish(
          msg = MqAutoUpdateProspectCategoryMsg(teamId = TeamId(team_id),
            prospectIds = prospectThreads.flatMap(_.prospect_id).map(ProspectId(_)),
            doerAccountId = AccountId(accountId),
            replySentimentUuid = Some(reply_sentiment_uuid),
            newProspectCategory = None
          )).map(_ => true)

    }
  }

        def publishReplySentimentToMq(
                                       campaignId: Option[CampaignId],
                                       accountId: AccountId,
                                       teamId: TeamId,
                                       taskUuid: TaskUuid,
                                       replySentimentUuid: ReplySentimentUuid,
                                       prospect_id: ProspectId,
                                       call_sp_sid: Option[CallSID],
                                       conference_uuid: Option[ConferenceUuid],
                                       permitted_account_ids : Seq[Long],
                                       task_type: TaskType
                                     )(implicit ec: ExecutionContext, logger: SRLogger): Try[true] = {

            val saveReplySentimentData: SaveReplySentimentData = SaveReplySentimentData(
                campaignId = campaignId,
                accountId = accountId,
                teamId = teamId,
                taskUuid = taskUuid,
                replySentimentUuid = replySentimentUuid,
                permitted_account_ids = permitted_account_ids,
                prospect_id = prospect_id,
                call_sp_sid = call_sp_sid,
                conference_uuid = conference_uuid,
                task_type = task_type
            )

            mqForSaveReplySentimentPublisher.publish(msg = saveReplySentimentData) match {

                case Failure(exception) =>
                    Failure(exception)

                case Success(_) =>
                    Success(true)

            }
        }


  def getReplySentimentsForTeam(
                                 team_id: Long,
                                 reply_sentiment_channel_type: ReplySentimentChannelType
                               )(
                                 implicit logger: SRLogger
                               ): Try[List[ReplySentimentForTeam]] = {

    replySentimentDAOService.getReplySentimentsForATeam(
      team_id = team_id,
      reply_sentiment_channel_type = reply_sentiment_channel_type
    )

  }

}

object ReplySentimentService {

  

  def parseReplySentimentChannelType(
                                      params: Map[String, Vector[String]],
                                      teamId : TeamId,
                                      orgId: OrgId 

                                    ): Try[ReplySentimentChannelType] = {

    params.get("reply_sentiment_channel_type") match {

      case None =>

        Try {
          ReplySentimentChannelType.EmailChannelType
        }


      case Some(value) =>

        ReplySentimentChannelType.fromKey(value.head)

    }

  }

  def filterSentimentsByChannel(replySentimentsForTeam : List[ReplySentimentForTeam] ,channelType: ReplySentimentChannelType): List[String] =
    replySentimentsForTeam
      .filter(_.reply_sentiment.getReplySentimentChannelType == channelType)
      .map(rs => s"${rs.reply_sentiment.getDisplayNameForType}: ${rs.reply_sentiment.getSubcategoryName}")

}
