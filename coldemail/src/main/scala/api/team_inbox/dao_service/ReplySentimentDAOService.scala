package api.team_inbox.dao_service

import api.accounts.TeamId
import api.prospects.models.{ProspectCategory, ProspectCategoryId, ProspectCategoryNew, ProspectId}
import api.team_inbox.dao.{ReplySentimentDAO, ReplySentimentForTeamIdAndChannelType, ReplySentimentJedisDAO}
import api.team_inbox.model.{ReplySentimentChannelType, ReplySentimentType, ReplySentimentTypeData}
import api.team_inbox.service.{ReplySentimentForTeam, ReplySentimentUuid}
import scalikejdbc.DBSession
import utils.SRLogger
import api.accounts.dao_service.OrganizationDAOService
import api.accounts.models.AccountId
import api.prospects.ProspectUpdateCategoryTemp
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.util.{Failure, Success, Try}

class ReplySentimentDAOService(
                               replySentimentDAO: ReplySentimentDAO,
                               replySentimentJedisDAO: ReplySentimentJedisDAO,
                               organizationDAOService: OrganizationDAOService,
                               srRollingUpdateCoreService: SrRollingUpdateCoreService
                              ) {

  def getReplySentimentsForATeam(
                                  team_id: Long,
                                  reply_sentiment_channel_type: ReplySentimentChannelType
                                )(
    implicit logger: SRLogger
  ): Try[List[ReplySentimentForTeam]] = {

    replySentimentJedisDAO.get(
      idData = ReplySentimentForTeamIdAndChannelType(
        team_id = TeamId(id = team_id),
        reply_sentiment_channel_type = reply_sentiment_channel_type
      )
    ) match {
      case None => {
          replySentimentDAO.getReplySentimentsForATeam(
            team_id = team_id,
            reply_sentiment_channel_type = reply_sentiment_channel_type,
            newReplySentimentsEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
              teamId = TeamId(team_id),
              feature = SrRollingUpdateFeature.NewReplySentiments
            )
          ).map { list =>
            
            val sortedList = list.sortWith((a,b) => {
              val priorityA = ReplySentimentDAOService.getSortPriority(a.reply_sentiment)
              val priorityB = ReplySentimentDAOService.getSortPriority(b.reply_sentiment)
              priorityA < priorityB
            })
              
            

            replySentimentJedisDAO.set(
              value = sortedList,
              idData = ReplySentimentForTeamIdAndChannelType(
                team_id = TeamId(id = team_id),
                reply_sentiment_channel_type = reply_sentiment_channel_type
              )
            )
            list
          }

        
      }
      case Some(list) =>
        Success(list)
    }
  }

  def getUuidsForTeamIdAndReplySentimentType(
                                              team_id: Long,
                                              replySentimentType: ReplySentimentType
                                            )(
                                              implicit logger: SRLogger
                                            ): Try[List[ReplySentimentUuid]] = {
    getReplySentimentsForATeam(team_id = team_id, reply_sentiment_channel_type = ReplySentimentChannelType.AllChannelSentiments) map { list =>

      val listOfFilteredUuid: List[ReplySentimentUuid] = ReplySentimentDAOService.getUuidsForAReplySentiment(
        listOfReplySentiment = list,
        replySentimentType = replySentimentType
      )

      listOfFilteredUuid
    }
  }

  def addReplySentimentsForATeam(
                                  team_id: Long,
                                  replySentimentType: ReplySentimentTypeData,
                                  uuid: String
                                )(implicit session: DBSession): Try[Option[Long]] = {
    replySentimentDAO.addReplySentimentsForATeam(
      team_id = team_id,
      replySentimentType = replySentimentType,
      uuid = uuid
    )

  }

  def updateProspectCategoryMappedToReplySentiment(
    teamId: TeamId,
    replySentimentUuid: ReplySentimentUuid,
    prospectCategoryIdOpt: Option[ProspectCategoryId],
  ): Try[Option[ReplySentimentUuid]] = {

    replySentimentDAO.updateProspectCategoryMappedToReplySentiment(
      teamId = teamId,
      replySentimentUuid = replySentimentUuid,
      prospectCategoryIdOpt = prospectCategoryIdOpt
    )

  }

}

object ReplySentimentDAOService {

  def getUuidsForAReplySentiment(
                                  listOfReplySentiment: List[ReplySentimentForTeam],
                                  replySentimentType: ReplySentimentType
                                ): List[ReplySentimentUuid] = {

        listOfReplySentiment
          .filter(p => p.reply_sentiment.getReplySentimentType == replySentimentType)
          .map(_.uuid)
  }

  def getSortPriority(replySentimentTypeData: ReplySentimentTypeData): Int = {
    replySentimentTypeData match {
      case data: ReplySentimentTypeData.PositiveData => 1
      case data: ReplySentimentTypeData.NegativeData => 2
      case data: ReplySentimentTypeData.FollowUpNeededData => 3
      case data: ReplySentimentTypeData.ReferralData => 4
      case data: ReplySentimentTypeData.ObjectionData => 5
      case data: ReplySentimentTypeData.DoNotContactData => 6
      case data: ReplySentimentTypeData.OtherData => 7
    }
  }
}