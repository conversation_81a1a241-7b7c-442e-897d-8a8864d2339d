package api.csv_uploads.models
import play.api.libs.json.{Js<PERSON><PERSON><PERSON>, JsString, JsSuccess, JsValue, Reads, Writes}

import scala.util.{Failure, Success, Try}

sealed trait CsvUploadType {

    def toString: String

}

object CsvUploadType {

    private val prospect = "prospect"
    private val dnc = "dnc"
    private val dnc_agency = "dnc_agency"
    private val bulk_email = "bulk_email"

    case object PROSPECTUPLOAD extends CsvUploadType {

        override def toString: String = prospect
    }

    case object DNCUPLOAD extends CsvUploadType {

        override def toString: String = dnc
    }

    case object DNCAGENCYUPLOAD extends CsvUploadType {

        override def toString: String = dnc_agency
    }

    case object BULKEMAILUPLOAD extends CsvUploadType {
        override def toString: String = bulk_email
    }

    def fromString(key: String): Try[CsvUploadType] = Try {
        key match {

            case `prospect` => PROSPECTUPLOAD

            case `dnc` => DNCUPLOAD

            case `dnc_agency` => DNCAGENCYUPLOAD

            case `bulk_email` => BULKEMAILUPLOAD

        }
    }

    implicit def reads: Reads[CsvUploadType] = {
        case JsString(value) =>
            fromString(key = value) match {

                case Failure(exception) => JsError(exception.toString)

                case Success(data) => JsSuccess(value = data)
            }

        case _ => JsError("Invalid JSON type for CsvUploadType. Expected a string.")

    }

    implicit def writes: Writes[CsvUploadType] = new Writes[CsvUploadType] {
        def writes(rs: CsvUploadType): JsValue = {
            JsString(rs.toString)
        }
    }
}
