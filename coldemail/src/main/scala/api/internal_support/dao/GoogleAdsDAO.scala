package api.internal_support.dao

import io.sr.billing_common.models.PlanID
import api.internal_support.model.GoogleAdsReport
import api.reports.models.{EnrichedCompanyInfoForReports, UtmData}
import org.joda.time.DateTime
import io.smartreach.companies_api.api.enrich_company_info.models.EnrichedCompanyInfo
import io.sr.billing_common.models.SrPlanName
import org.joda.time.format.DateTimeFormat
import org.postgresql.util.PGobject
import play.api.libs.json.Json
import scalikejdbc.{DB, WrappedResultSet, scalikejdbcSQLInterpolationImplicitDef, using}
import scalikejdbc.jodatime.JodaWrappedResultSet._

import java.util.Locale
import scala.util.Try


class GoogleAdsDAO {

  def getGoogleAdsReport(date: DateTime): Try[List[GoogleAdsReport]] = Try{
    DB readOnly{implicit session =>
      sql"""
           select

          org.created_at as signed_up_at,

          org.id as internal_org_id,


          org.plan_name,

          (
          case
          when org.plan_type ilike '%inactive%' and org.cancelled_at is not null THEN 'cancelled'
          else org.plan_type
          end
          ) as plan_type ,






          (case when org.plan_type = 'inactive' then 0
          else org.total_sending_email_accounts
          end
          )


          as total_seats,


          org.utm_source,
          org.utm_campaign,
          org.utm_content,
          org.utm_medium,
          org.utm_term,
          org.utm_device,
          org.utm_agid,
          org.utm_match_type,
          org.utm_placement,
          org.utm_network,

          org.is_agency,

          org.billing_address_country,


          org.onboarding_data ->> 'teamSize' as onboarding_teamSize,
          org.onboarding_data ->> 'usecase' as onboarding_usecase,
          org.onboarding_data ->> 'crm' as onboarding_crm,

          org.enriched_company_info

          from organizations org

          where
          org.utm_source is not null

          and
           org.utm_source not in ('prospectdaddy')
          and
           org.created_at > $date

          --and org.plan_type != 'inactive'
          --and org.cancellation_reason is not null

          group by org.id

          order by signed_up_at desc
         """
        .map(GoogleAdsDAO.GoogleAdsReportMapping)
        .list
        .apply()
        .map(_.get)
    }
  }

}

object GoogleAdsDAO {
  def GoogleAdsReportMapping(rs: WrappedResultSet):Try[GoogleAdsReport] = Try{
    val date =  rs.jodaDateTime("signed_up_at")

    val planName: String = rs.string("plan_name")

    val totalSeats = rs.intOpt("total_seats")

    val isAgency: Boolean = rs.boolean("is_agency")

    val isBusinessPlan: Option[Boolean] = totalSeats
      .map(seats => {

        PlanID.considerAsBusinessPlanForReporting(

          totalSeats = seats,

          planName = SrPlanName(name = planName)

        )
      })

    val planId: String = PlanID
      ._getPlanId(planname = planName)
      .toString

    val planInterval: Option[String] = PlanID.getPlanInterval(
      planName = SrPlanName(name = planName)
    )

    val cohort: String = if (isAgency) "agency"
    else if (isBusinessPlan.getOrElse(false)) "business"
    else "individual"


    val utmData = UtmData(
      utm_source = rs.stringOpt("utm_source"),
      utm_medium = rs.stringOpt("utm_medium"),
      utm_campaign = rs.stringOpt("utm_campaign"),
      utm_term = rs.stringOpt("utm_term"),
      utm_content = rs.stringOpt("utm_content"),
      utm_device = rs.stringOpt("utm_device"),
      utm_agid = rs.stringOpt("utm_agid"),
      utm_match_type = rs.stringOpt("utm_match_type"),
      utm_placement = rs.stringOpt("utm_placement"),
      utm_network = rs.stringOpt("utm_network")
    )

    val enrichedCompanyInfoOpt: Option[EnrichedCompanyInfo] = rs.anyOpt("enriched_company_info")
      .flatMap { ec =>
        Json.parse(
          ec.asInstanceOf[PGobject].getValue
        ).validateOpt[EnrichedCompanyInfo].get
      }

    val enrichedCompanyInfoForReportsOpt: Option[EnrichedCompanyInfoForReports] = enrichedCompanyInfoOpt.map { ec =>
      EnrichedCompanyInfoForReports.parseFromEnrichedCompanyInfo(enrichedCompanyInfo = ec)
    }

    GoogleAdsReport(

      signed_up_at_mm_dd_yyyy = date.toString(DateTimeFormat.forPattern("MM/dd/yyyy HH:mm:ss")).substring(0, 10),

      signed_up_at_month_year =date.monthOfYear().getAsText(Locale.ENGLISH) + "-" + date.getYear.toString,

      internal_org_id = rs.long("internal_org_id"),

      plan_name = planName,

      is_business_plan = isBusinessPlan,

      plan_type = rs.string("plan_type"),

      plan_interval = planInterval,

      cohort = cohort,

      plan = Option(planId),

      total_seats = totalSeats,

      utmData = utmData,

      is_agency = isAgency,

      billing_address_country = rs.string("billing_address_country"),

      onboarding_teamSize = rs.stringOpt("onboarding_teamSize"),

      onboarding_usecase = rs.stringOpt("onboarding_usecase"),

      onboarding_crm = rs.stringOpt("onboarding_crm"),

      approx_monthly_spend = 0, // it is being overwritten in the service layer

      enrichedCompanyInfoForReportsOpt = enrichedCompanyInfoForReportsOpt
    )
  }
}
