package api.internal_support.service

import api_layer_models.CurrencyType
import io.sr.billing_common.models.PlanID
import api.accounts.{AccountDAO, AuthUtils, OrganizationForSupportApp, OrganizationRole, RolePermissionDataDAOV2, RolePermissionDataV2, TeamAccountRole}
import api.accounts.dao.{AccountDAOV2, OrganizationDAO}
import api.accounts.models.{AccountV2, OrgId, SignupType}
import api.accounts.service.NewAuthFlowService
import api.billing.PaymentGateway.PaymentGateway
import api.internal_support.dao.GoogleAdsDAO
import api.internal_support.model.GoogleAdsReport
import api.spammonitor.dao.{EmailSendingStatusWithCurrentStage, UpdateEmailSendingStatusForm}
import api.spammonitor.model.{EmailSendStatus, EmailSendingEntityType, SendEmailStatusData}
import api.spammonitor.service.{EmailSendingStatusService, GetUnderReviewOrgError, UpdateEmailSendingStatusError}
import api.prospects.dao.CsvModel
import api.prospects.service.CsvQueueService
import org.joda.time.DateTime
import play.api.libs.json.{Json, OWrites, Reads}
import play.api.libs.json.JodaWrites._
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.affiliate.{AffiliateEvent, SaleApproved}
import utils.emailvalidation.EmailValidationService.validateEmailFormat

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}


case class EnableAgencyPlanDetails(
                                    owner_email: String,
                                    agencyPlanType: PlanID,
                                    minSeats: Int
                                  )

object EnableAgencyPlanDetails {
  implicit val reads: Reads[EnableAgencyPlanDetails] = Json.reads[EnableAgencyPlanDetails]
}

sealed trait CheckRoleAndOrgError

object CheckRoleAndOrgError {
  case object WrongEmailFormatError extends CheckRoleAndOrgError

  case object AccountNotFromOrgError extends CheckRoleAndOrgError

  case object WrongOrgRole extends CheckRoleAndOrgError

  case object NoAccountFoundError extends CheckRoleAndOrgError

  case class SQlErrorWhileFindingAccount(err: Throwable) extends CheckRoleAndOrgError
}

sealed trait TransferOwnershipError

object TransferOwnershipError {
  case class ErrorWhileGettingAccount(err: CheckRoleAndOrgError) extends TransferOwnershipError

  case object AccountsNotFromSameOrg extends TransferOwnershipError

  case class SQLErrorWhileChangingOwner(err: Throwable) extends TransferOwnershipError
}

sealed trait DeleteAccountError

object DeleteAccountError {
  case object WrongEmailFormatError extends DeleteAccountError

  case object NoAccountFound extends DeleteAccountError

  case object AccountOfOwnerOfOrgError extends DeleteAccountError

  case class SQlErrorWhileGettingAccount(err: Throwable) extends DeleteAccountError

  case class SQLErrorWhileSchedulingDeleteAccount(err: Throwable) extends DeleteAccountError
}

sealed trait DeleteOrgError

object DeleteOrgError {
  case object WrongEmailFormatError extends DeleteOrgError

  case class SQlErrorWhileGettingOrg(err: Throwable) extends DeleteOrgError

  case object NoOrgFound extends DeleteOrgError

  case class SQLErrorWhileSchedulingDeleteOrg(err: Throwable) extends DeleteOrgError
}

sealed trait GetGoogleAdsError

object GetGoogleAdsError {
  case class SQLErrorWhileGettingList(err: Throwable) extends GetGoogleAdsError
}

sealed trait TrialExtendError

object TrialExtendError {

  case object DaysGreaterThan31ValidationError extends TrialExtendError

  case object InvalidEmailError extends TrialExtendError

  case object OrganizationNotFoundError extends TrialExtendError

  case object AlreadyPaidOrganizationError extends TrialExtendError

  case object OrgNotFoundAfterSavedImpossibleError extends TrialExtendError

  case class SQLError(err: Throwable) extends TrialExtendError
}

sealed trait BlockOrganizationError

object BlockOrganizationError {
  case object InvalidEmailError extends BlockOrganizationError

  case object OrganizationNotFoundError extends BlockOrganizationError

  case class SQLError(err: Throwable) extends BlockOrganizationError

  case object OrgNotFountAfterUpdateError extends BlockOrganizationError
}

sealed trait CsvUploadError

object CsvUploadError {
  case class SQLError(err: Throwable) extends CsvUploadError
}

sealed trait UpdateSignupTypeError

object UpdateSignupTypeError {
  case class SQLErrorWhileUpdating(err: Throwable) extends UpdateSignupTypeError

  case class SQLErrorWhileGettingAccount(err: Throwable) extends UpdateSignupTypeError

  case object NoAccountFoundAfterUpdate extends UpdateSignupTypeError
}

case class ApproveAffiliateCommissionData(
                                           orgId: OrgId,
                                           pg: PaymentGateway,
                                           pg_customer_id: String,
                                           billing_event_id: Int,
                                           currency: CurrencyType,
                                           amount_received: Int,
                                           amount_refunded: Option[Int]
                                         )

object ApproveAffiliateCommissionData {
  implicit val reads: Reads[ApproveAffiliateCommissionData] = Json.reads[ApproveAffiliateCommissionData]
}

sealed trait FunnelStageError

object FunnelStageError {
  case class SQLError(err: Throwable) extends FunnelStageError

  case class ErrorWhileParsingParam(err: Throwable) extends FunnelStageError

}

case class UpdateOrgMetadata(
                              email: String
                            )

object UpdateOrgMetadata {
  implicit val reads: Reads[UpdateOrgMetadata] = Json.reads[UpdateOrgMetadata]

}

case class UpdateSignupType(
                             email: String,
                             signupType: SignupType
                           )

object UpdateSignupType {
  implicit val reads: Reads[UpdateSignupType] = Json.reads[UpdateSignupType]

}


case class UpdateAccountTrialEmail(
                                    email: String,
                                    extend_by_days: Int
                                  )

object UpdateAccountTrialEmail {
  implicit val reads: Reads[UpdateAccountTrialEmail] = Json.reads[UpdateAccountTrialEmail]
}

case class TransferOwnerData(
                              oldEmail: String,
                              newEmail: String,
                              orgName: String
                            )

object TransferOwnerData {
  implicit val reads: Reads[TransferOwnerData] = Json.reads[TransferOwnerData]
}

case class DeleteActionData(
                             accountEmail: String,
                             processImmediately: Boolean
                           )

object DeleteActionData {
  implicit val reads: Reads[DeleteActionData] = Json.reads[DeleteActionData]
}

case class MetadataEmail(
                          accountEmail: String
                        )

object MetadataEmail {
  implicit val reads: Reads[MetadataEmail] = Json.reads[MetadataEmail]

}

case class DeleteActionReturnValue(
                                    scheduled_for_deletion_at: DateTime
                                  )

case object DeleteActionReturnValue {
  implicit val writes: OWrites[DeleteActionReturnValue] = Json.writes[DeleteActionReturnValue]
}

sealed trait EmailUpdateError

object EmailUpdateError {
  case object InvalidEmailError extends EmailUpdateError

  case object AccountNotFoundError extends EmailUpdateError

  case class SQLError(err: Throwable) extends EmailUpdateError

  case object AccountNotFountAfterUpdateError extends EmailUpdateError

  case object NewEmailAlreadyHasAnAccount extends EmailUpdateError
}

case class UpdateEmail(
                        oldEmail: String,
                        newEmail: String
                      )

object UpdateEmail {
  implicit val reads: Reads[UpdateEmail] = Json.reads[UpdateEmail]
}

class InternalSupportServices(
                               accountDAOV2: AccountDAOV2,
                               rolePermissionDataDAOV2: RolePermissionDataDAOV2,
                               organizationDAO: OrganizationDAO,
                               googleAdsDAO: GoogleAdsDAO,
                               emailSendingStatusService: EmailSendingStatusService,
                               csvQueueService: CsvQueueService,
                               newAuthFlowService: NewAuthFlowService
                             ) {

  private def checkRoleAndOrg(
                               accountEmail: String,
                               shouldBeOwner: Boolean,
                               orgName: String): Either[CheckRoleAndOrgError, AccountV2] = {

    if (!validateEmailFormat(accountEmail)) {
      Left(CheckRoleAndOrgError.WrongEmailFormatError)
    } else {
      val accountTry = accountDAOV2.getAccountByEmail(email = accountEmail)
      accountTry match {
        case Failure(err) =>
          Left(CheckRoleAndOrgError.SQlErrorWhileFindingAccount(err))

        case Success(None) =>
          Left(CheckRoleAndOrgError.NoAccountFoundError)

        case Success(Some(account)) =>
          if (orgName.trim.toLowerCase != account.company.trim.toLowerCase) {
            Left(CheckRoleAndOrgError.AccountNotFromOrgError)
          }
          else if (shouldBeOwner && !AuthUtils.__isOrgOwner(account)) {
            Left(CheckRoleAndOrgError.WrongOrgRole)
          }
          else if (!shouldBeOwner && AuthUtils.__isOrgOwner(account)) {
            Left(CheckRoleAndOrgError.WrongOrgRole)
          }
          else {
            Right(account)
          }
      }
    }
  }

  def transferOwnership(
                         data: TransferOwnerData
                       ): Either[TransferOwnershipError, Long] = {

    val oldOwnerAccountTry = checkRoleAndOrg(
      accountEmail = data.oldEmail,
      shouldBeOwner = true,
      orgName = data.orgName
    )

    oldOwnerAccountTry match {
      case Left(err) =>
        Left(TransferOwnershipError.ErrorWhileGettingAccount(err))
      case Right(oldOwnerAccount) =>
        val newOwnerAccountTry = checkRoleAndOrg(
          accountEmail = data.newEmail,
          shouldBeOwner = false,
          orgName = data.orgName
        )
        newOwnerAccountTry match {
          case Left(err) =>
            Left(TransferOwnershipError.ErrorWhileGettingAccount(err))

          case Right(newOwnerAccount) =>
            if (oldOwnerAccount.org_id != newOwnerAccount.org_id) {
              Left(TransferOwnershipError.AccountsNotFromSameOrg)
            } else {
              val allTeamAccountRoles: Seq[RolePermissionDataV2] = rolePermissionDataDAOV2.findAllRoles(orgId = oldOwnerAccount.org_id).get
              val adminRoleId: Long = allTeamAccountRoles.find(r => r.role_name == TeamAccountRole.ADMIN).get.id
              val ownerRoleId: Long = allTeamAccountRoles.find(r => r.role_name == TeamAccountRole.OWNER).get.id

              accountDAOV2.updateOwner(
                oldOwnerAccount = oldOwnerAccount,
                newOwnerAccount = newOwnerAccount,
                adminRoleId = adminRoleId,
                ownerRoleId = ownerRoleId
              ) match {
                case Failure(err) =>
                  Left(TransferOwnershipError.SQLErrorWhileChangingOwner(err))
                case Success(i) =>
                  Right(i)
              }

            }

        }
    }
  }


  def deleteAccount(
                     email: String,
                     processImmediately: Boolean,
                     logger: SRLogger
                   ): Either[DeleteAccountError, DeleteActionReturnValue] = {

    logger.debug(s"deleteAccount - processImmediately: $processImmediately")

    if (!validateEmailFormat(email)) {
      Left(DeleteAccountError.WrongEmailFormatError)

    } else {
      val accountTry = accountDAOV2.getAccountByEmail(email = email)

      accountTry match {
        case Failure(err) =>
          Left(DeleteAccountError.SQlErrorWhileGettingAccount(err))

        case Success(None) =>
          Left(DeleteAccountError.NoAccountFound)

        case Success(Some(account)) =>

          if (AuthUtils.__isOrgOwner(account)) {
            Left(DeleteAccountError.AccountOfOwnerOfOrgError)
          } else {
            val scheduled_for_deletion_at = if (processImmediately) {
              DateTime.now()
            } else {
              getComingSundayDateTime
            }

            logger.debug(s"deleteAccount - scheduled_for_deletion_at: $scheduled_for_deletion_at")

            val updateTry = accountDAOV2.scheduleForDeletion(
              email = email,
              scheduled_for_deletion_at = scheduled_for_deletion_at)

            updateTry match {
              case Failure(err) =>
                logger.error("deleteAccount - updateTry FAILED", err = err)
                Left(DeleteAccountError.SQLErrorWhileSchedulingDeleteAccount(err))

              case Success(_) =>
                logger.debug("deleteAccount - updateTry SUCCESS")

                val returnValue = DeleteActionReturnValue(
                  scheduled_for_deletion_at = scheduled_for_deletion_at
                )
                Right(returnValue)
            }
          }
      }

    }
  }

  def deleteOrg(
                 email: String,
                 processImmediately: Boolean
               ): Either[DeleteOrgError, DeleteActionReturnValue] = {
    if (!validateEmailFormat(email)) {
      Left(DeleteOrgError.WrongEmailFormatError)
    } else {
      val organizationTry = organizationDAO.getOrgForSupportByEmail(orgEmail = email)

      organizationTry match {
        case Failure(err) =>
          Left(DeleteOrgError.SQlErrorWhileGettingOrg(err))

        case Success(None) =>
          Left(DeleteOrgError.NoOrgFound)

        case Success(Some(_)) =>
          val scheduled_for_deletion_at =
            if (processImmediately) {
              DateTime.now()
            } else {
              getComingSundayDateTime
            }

          val updateTry = organizationDAO.scheduleForDeletion(
            email = email,
            scheduled_for_deletion_at = scheduled_for_deletion_at
          )

          updateTry match {
            case Failure(err) =>
              Left(DeleteOrgError.SQLErrorWhileSchedulingDeleteOrg(err))

            case Success(i) =>
              val returnValue = DeleteActionReturnValue(
                scheduled_for_deletion_at = scheduled_for_deletion_at
              )
              Right(returnValue)
          }

      }
    }
  }

  def getComingSundayDateTime: DateTime = {
    val currentTime = DateTime.now()
    currentTime.plusDays(7 - currentTime.dayOfWeek().get())
  }

  def getGoogleAdsReport(days: Int): Either[GetGoogleAdsError, List[GoogleAdsReport]] = {

    val date = new DateTime().withTimeAtStartOfDay()

    val getReportsFromDate = date.minusDays(days)

    googleAdsDAO.getGoogleAdsReport(getReportsFromDate) match {
      case Failure(err) =>
        Left(GetGoogleAdsError.SQLErrorWhileGettingList(err))

      case Success(list) =>
        val logger = new SRLogger("[GADS]")
        Right(
          list.map { googleAdsReport =>
            val approx_monthly_spend = PlanID.getApproximateMonthlySpend(
              planId = PlanID._getPlanId(googleAdsReport.plan_name),
              isV2BusinessPlan = googleAdsReport.is_business_plan.getOrElse(false),
              seats = googleAdsReport.total_seats.getOrElse(0)
            )
            googleAdsReport.copy(approx_monthly_spend = approx_monthly_spend)
          }
        )

    }


  }

  final def updateEmail(
                         data: UpdateEmail
                       ): Either[EmailUpdateError, AccountV2] = {
    if (!validateEmailFormat(data.newEmail) || !validateEmailFormat(data.oldEmail)) {
      Left(EmailUpdateError.InvalidEmailError)
    } else {
      val newAccount = accountDAOV2.getAccountByEmail(data.newEmail)
      newAccount match {
        case Failure(ex) => Left(EmailUpdateError.SQLError(ex))
        case Success(Some(_)) => Left(EmailUpdateError.NewEmailAlreadyHasAnAccount)
        case Success(None) =>
          val updated = accountDAOV2.updateEmailAndChangeSignupTypeToPassword(data)
          updated match {
            case Success(i) => if (i != 1) {
              Left(EmailUpdateError.AccountNotFoundError)
            } else {
              val account = accountDAOV2.getAccountByEmail(data.newEmail)
              account match {
                case Success(Some(account)) => Right(account)
                case Success(None) => Left(EmailUpdateError.AccountNotFountAfterUpdateError)
                case Failure(e) => Left(EmailUpdateError.SQLError(e))
              }
            }
            case Failure(e) => Left(EmailUpdateError.SQLError(e))
          }
      }

    }

  }

  final def updateTrialByEmail(
                                data: UpdateAccountTrialEmail
                              ): Either[TrialExtendError, OrganizationForSupportApp] = {

    if (data.extend_by_days > 31) {

      Left(TrialExtendError.DaysGreaterThan31ValidationError)

    } else if (!validateEmailFormat(data.email)) {

      Left(TrialExtendError.InvalidEmailError)

    } else {

      val organization = organizationDAO.getOrgForSupportByEmail(orgEmail = data.email)

      organization match {

        case Success(Some(organization)) =>
          if (organization.plan_type == "inactive" || organization.plan_type == "trial") {
            val result = organizationDAO.extendFreeTrialEmail(
              data = data
            )
            result match {
              case Success(i) =>
                organizationDAO.getOrgForSupportByEmail(orgEmail = data.email) match {

                  case Failure(e) =>

                    Left(TrialExtendError.SQLError(err = e))

                  case Success(None) =>

                    Left(TrialExtendError.OrgNotFoundAfterSavedImpossibleError)


                  case Success(Some(updatedAccount)) =>

                    Right(updatedAccount)
                }

              case Failure(e) =>

                Left(TrialExtendError.SQLError(err = e))
            }
          } else {

            Left(TrialExtendError.AlreadyPaidOrganizationError)

            // Failure(new Throwable(ERROR_INVALID_REQUEST))
          }
        case Success(None) => Left(TrialExtendError.OrganizationNotFoundError)

        case Failure(e) => Left(TrialExtendError.SQLError(err = e))

      }

    }

  }

  def validateDataForgetUnderReviewEntity(
                                           validateDataEntityType: Option[Seq[String]],
                                           validateDataSendingStatus: Option[Seq[String]]
                                         ): Try[(Option[EmailSendingEntityType], Seq[EmailSendStatus])] = Try {
    val entityType = validateDataEntityType flatMap { entitySeq =>
      val entityStringOpt = entitySeq.headOption
      entityStringOpt flatMap { entityString =>
        if (entityString.trim.isEmpty) {
          None
        } else {
          Some(EmailSendingEntityType.fromKey(
            key = entityString
          ).get)
        }
      }
    }
    val sendingStatusSeq = validateDataSendingStatus.getOrElse(Seq())

    if (sendingStatusSeq.isEmpty) {
      throw new Throwable("Please send a valid Sending Status")
    } else {
      val sendingStatus: Seq[EmailSendStatus] = sendingStatusSeq.map(a => EmailSendStatus.fromKey(a).get)
      (entityType, sendingStatus)
    }


  }

  def getUnderReviewEntities(
                              emailSendingEntityType: Option[EmailSendingEntityType],
                              sendingStatuses: Seq[EmailSendStatus]
                            )(using Logger: SRLogger): Either[GetUnderReviewOrgError, List[EmailSendingStatusWithCurrentStage]] = {

    emailSendingStatusService.getUnderReview(
      emailSendingEntityTypeOpt = emailSendingEntityType,
      sendingStatuses = sendingStatuses
    )
  }

  def getCsvUploadData()(using Logger:SRLogger): Either[CsvUploadError, List[CsvModel]] = {
    csvQueueService.getCsvUploadData() match {
      case Success(data) =>
        Right(data)
      case Failure(e) =>
        Logger.shouldNeverHappen(s"Failed getCsvUploadData", Some(e))
        Left(CsvUploadError.SQLError(err = e))
    }
  }


  def updateEmailSendingStatus(
                                updateEmailSendingStatusForm: UpdateEmailSendingStatusForm,
                                oldSendEmailStatus: Option[SendEmailStatusData],
                                isSupport: Boolean = false
                              )(implicit wsClient: WSClient, ec: ExecutionContext, Logger: SRLogger): Either[UpdateEmailSendingStatusError, Unit] = {
    organizationDAO.getOrgForSupportByOrgId(
      orgId = updateEmailSendingStatusForm.orgId.id
    ) match {
      case Failure(exception) => Left(UpdateEmailSendingStatusError.ErrorWhileAddingInDB(exception))
      case Success(None) => Left(UpdateEmailSendingStatusError.ErrorWhileAddingInDB(new Throwable(s"No ORG found")))
      case Success(Some(org)) =>
        emailSendingStatusService.updateEmailSendingStatus(
          updateEmailSendingStatusForm = updateEmailSendingStatusForm,
          oldSendEmailStatus = oldSendEmailStatus,
          isSupport = isSupport,
          owner_email = org.owner_account_email
        )
    }

  }


  final def blockOrganization(updateOrgMetadata: UpdateOrgMetadata): Either[BlockOrganizationError, OrganizationForSupportApp] = {
    if (!validateEmailFormat(updateOrgMetadata.email)) {
      Left(BlockOrganizationError.InvalidEmailError)
    } else {
      val updated = organizationDAO.blockOrganization(email = updateOrgMetadata.email)
      updated match {
        case Failure(e) => Left(BlockOrganizationError.SQLError(e))
        case Success(i) => if (i == 1) {
          val organization = organizationDAO.getOrgForSupportByEmail(orgEmail = updateOrgMetadata.email)
          organization match {
            case Success(None) =>
              Left(BlockOrganizationError.OrgNotFountAfterUpdateError)
            case Failure(e) => Left(BlockOrganizationError.SQLError(e))
            case Success(Some(organization)) => Right(organization)
          }
        } else {
          Left(BlockOrganizationError.OrganizationNotFoundError)
        }
      }
    }

  }

  def updateEmailSignupType(
                             email: String,
                             signupType: SignupType
                           )(using Logger: SRLogger): Either[UpdateSignupTypeError, AccountV2] = {
    accountDAOV2.updateSignupType(
      email = email,
      signupType = signupType
    ) match {
      case Failure(err) =>
        Left(UpdateSignupTypeError.SQLErrorWhileUpdating(err))

      case Success(_) =>
        accountDAOV2.getAccountByEmail(email = email) match {

          case Failure(err) =>
            Left(UpdateSignupTypeError.SQLErrorWhileGettingAccount(err))

          case Success(None) =>
            Left(UpdateSignupTypeError.NoAccountFoundAfterUpdate)

          case Success(Some(account)) =>
            Right(account)
        }
    }
  }

  def enableAgencyPlan(
                        enableAgencyPlanDetails: EnableAgencyPlanDetails,
                        logger: SRLogger
                      ): Try[Option[OrganizationForSupportApp]] = {

    val agencyPlans: Try[(String, String)] = enableAgencyPlanDetails.agencyPlanType match {
      case PlanID.V3_AGENCY_BASIC =>
        Success("show_agency_email_plans", "show_agency_mc_plans")

      case PlanID.V3_AGENCY_MC =>
        Success("show_agency_mc_plans", "show_agency_email_plans")

      case _ =>
        Failure(new Exception("Invalid Agency PlanID"))
    }

    agencyPlans.flatMap { case (enablePlan: String, disablePlan: String) =>

      for {

        enableAgencyPlan: Int <- organizationDAO.updateAgencyMetadata(
          owner_email = enableAgencyPlanDetails.owner_email,
          minSeats = enableAgencyPlanDetails.minSeats,
          enablePlanType = enablePlan,
          disablePlanType = disablePlan
        )

        supportOrg: Option[OrganizationForSupportApp] <- if (enableAgencyPlan != 0) {
          organizationDAO.getOrgForSupportByEmail(orgEmail = enableAgencyPlanDetails.owner_email)
        } else {

          val errMsg = s"Failed to update agency metadata for org with owner email: ${enableAgencyPlanDetails.owner_email}"

          logger.error(errMsg)
          Failure(new Exception(errMsg))
        }

      } yield {

        supportOrg
      }
    }

  }

}
