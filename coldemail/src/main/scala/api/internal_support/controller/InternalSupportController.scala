package api.internal_support.controller

import api.accounts.{AccountService, OrganizationForSupportApp, TeamId}
import api.accounts.models.OrgId
import api.campaigns.models.CampaignPauseReason
import api.campaigns.services.{CampaignId, CampaignService}
import api.free_email_domain.service.{AddingDomainToFreeListError, AddingDomainToWhitelistError, DomainName, FreeEmailDomainListService}
import api.middleware.InternalSupportAuthService
import api.reports.models.{AudienceReportData, InternalAudienceReportType, InternalSRUsageResult}
import api.internal_support.service.{ApproveAffiliateCommissionData, BlockOrganizationError, CheckRoleAndOrgError, CsvUploadError, DeleteAccountError, DeleteActionData, DeleteOrgError, EmailUpdateError, EnableAgencyPlanDetails, FunnelStageError, GetGoogleAdsError, InternalSupportServices, MetadataEmail, TransferOwnerData, TransferOwnershipError, TrialExtendError, UpdateAccountTrialEmail, UpdateEmail, UpdateOrgMetadata, UpdateSignupType, UpdateSignupTypeError}
import api.rep_mail_servers.services.SrMailServerService
import api.spammonitor.service.{GetUnderReviewOrgError, UpdateEmailSendingStatusError}
import api.spammonitor.dao.UpdateEmailSendingStatusForm
import org.joda.time.DateTime
import org.joda.time.format.ISODateTimeFormat
import play.api.libs.json.JodaReads._
import play.api.libs.json.{JsError, JsSuccess, JsValue, Json, Reads}
import play.api.libs.ws.WSClient
import play.api.mvc.{Action, AnyContent, BaseController, ControllerComponents}
import utils.SRLogger
import utils.affiliate.{AffiliateTrackingService, DenyAffiliateCommissionData, ReferralCouponDetails, ReferralCouponStatus, SaleDenied, TrackAffiliateResponseStatus}
import utils.customersupport.dao.CustomerAcquisitionReport
import utils.customersupport.services.{AcquisitionFieldData, InternalCSDReportService, UpdateAdoptionReportData}
import utils.sr_product_usage_data.services.SrUserFeatureUsageEventService

import scala.concurrent.impl.Promise
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

case class UpdateReferralCouponDetails(
                                        org_id: OrgId,
                                        approve_coupon: Boolean
                                      )

object UpdateReferralCouponDetails {
  implicit val reads: Reads[UpdateReferralCouponDetails] = Json.reads[UpdateReferralCouponDetails]
}

case class OrgIdsForPromoterDetails(
  org_ids: List[OrgId]
)

object OrgIdsForPromoterDetails {
  implicit val reads: Reads[OrgIdsForPromoterDetails] = Json.reads[OrgIdsForPromoterDetails]
}

class InternalSupportController(
                                 internalSupportAuthService: InternalSupportAuthService,
                                 internalSupportServices: InternalSupportServices,
                                 freeEmailDomainListService: FreeEmailDomainListService,
                                 srUserFeatureUsageEventService: SrUserFeatureUsageEventService,
                                 affiliateTrackingService: AffiliateTrackingService,
                                 accountService: AccountService,
                                 campaignService: CampaignService,
                                 internalCSDReportService: InternalCSDReportService,
                                 srMailServerService: SrMailServerService,
                                 protected val controllerComponents: ControllerComponents,
                                 implicit val ws: WSClient
                               ) extends BaseController {

  private implicit val ec: ExecutionContext = controllerComponents.executionContext


  def updateOrgOwner(): Action[JsValue] = internalSupportAuthService.isSupportCallAuthorized.async(parse.json) { implicit request =>

    val validateData = request.body.validate[TransferOwnerData]
    val Logger = request.logger
    val Res = request.response
    validateData match {
      case JsError(errors) =>
        Future.successful(Res.JsValidationError(errors))

      case JsSuccess(metadata, _) =>
        internalSupportServices.transferOwnership(metadata) match {
          case Left(TransferOwnershipError.AccountsNotFromSameOrg) =>
            Future.successful(Res.BadRequestError("Both accounts are not from the same org"))

          case Left(TransferOwnershipError.ErrorWhileGettingAccount(err)) =>
            err match {
              case CheckRoleAndOrgError.WrongOrgRole =>
                Future.successful(Res.BadRequestError("The org role of accounts are not correct"))

              case CheckRoleAndOrgError.AccountNotFromOrgError =>
                Future.successful(Res.BadRequestError("One of the account is not from the org"))

              case CheckRoleAndOrgError.NoAccountFoundError =>
                Future.successful(Res.NotFoundError("Account not found error"))

              case CheckRoleAndOrgError.SQlErrorWhileFindingAccount(err) =>
                Future.successful(Res.ServerError("Error while finding account", Some(err)))

              case CheckRoleAndOrgError.WrongEmailFormatError =>
                Future.successful(Res.BadRequestError("wrong email format"))
            }
          case Left(TransferOwnershipError.SQLErrorWhileChangingOwner(err)) =>
            Future.successful(Res.ServerError("error while updating owner", Some(err)))

          case Right(_) =>
            Future.successful(Res.Success("Successfully changed the owner", Json.obj()))

        }
    }

  }

  def deleteAccount(): Action[JsValue] = internalSupportAuthService.isSupportCallAuthorized.async(parse.json) { implicit request =>
    val validateData = request.body.validate[DeleteActionData]
    val logger = request.logger
    val Res = request.response
    validateData match {
      case JsError(err) =>

        logger.error(s"Failed to validated Request body ${request.body}")

        Future.successful(Res.JsValidationError(err))

      case JsSuccess(metadata, _) =>

        logger.debug(s"Successfully validated Request body ${metadata}")


        internalSupportServices.deleteAccount(
          email = metadata.accountEmail,
          processImmediately = metadata.processImmediately,
          logger = logger
        ) match {
          case Left(DeleteAccountError.NoAccountFound) =>
            Future.successful(Res.NotFoundError("No Account Found"))

          case Left(DeleteAccountError.AccountOfOwnerOfOrgError) =>
            logger.debug(s"Account is of Owner cant delete")

            Future.successful(Res.BadRequestError("Account is of Owner cant delete"))


          case Left(DeleteAccountError.SQlErrorWhileGettingAccount(err)) =>
            logger.error(s"error while getting account", err = err)

            Future.successful(Res.ServerError("error while getting account", Some(err)))


          case Left(DeleteAccountError.SQLErrorWhileSchedulingDeleteAccount(err)) =>
            logger.error(s"error while creating delete schedule", err = err)

            Future.successful(Res.ServerError("error while creating delete schedule", Some(err)))


          case Left(DeleteAccountError.WrongEmailFormatError) =>
            logger.debug(s"Wrong Email Format")

            Future.successful(Res.BadRequestError("Wrong Email Format"))


          case Right(returnValue) =>
            logger.debug(s"Successfully created delete Schedule the account will be deleted next Sunday $returnValue")

            Future.successful(Res.Success("Successfully created delete Schedule the account will be deleted next Sunday", Json.toJson(returnValue)))

        }

    }
  }

  def deleteOrganization(): Action[JsValue] = internalSupportAuthService.isSupportCallAuthorized.async(parse.json) { implicit request =>
    val validateData = request.body.validate[DeleteActionData]
    val Logger = request.logger
    val Res = request.response

    validateData match {
      case JsError(err) =>
        Future.successful(Res.JsValidationError(err))

      case JsSuccess(metadata, _) =>
        internalSupportServices.deleteOrg(
          email = metadata.accountEmail,
          processImmediately = metadata.processImmediately
        ) match {
          case Left(DeleteOrgError.WrongEmailFormatError) =>
            Future.successful(Res.BadRequestError("Wrong Email Format"))

          case Left(DeleteOrgError.NoOrgFound) =>
            Future.successful(Res.NotFoundError("No Org Found"))

          case Left(DeleteOrgError.SQlErrorWhileGettingOrg(err)) =>
            Future.successful(Res.ServerError("error while getting Org", Some(err)))

          case Left(DeleteOrgError.SQLErrorWhileSchedulingDeleteOrg(err)) =>
            Future.successful(Res.ServerError("error while creating delete schedule", Some(err)))

          case Right(value) =>
            Future.successful(Res.Success("Successfully created delete Schedule the Org will be deleted next Sunday", Json.toJson(value)))

        }

    }
  }


  def getGoogleAds: Action[AnyContent] = internalSupportAuthService.isSupportCallAuthorized.async { implicit request =>
    val Logger = request.logger
    val Res = request.response
    val validateData = request.queryString.get("days")

    validateData match {
      case None =>
        Future.successful(Res.BadRequestError("Please Send number of days"))
      case Some(daysSeq) =>
        if (daysSeq.length != 1) {
          Future.successful(Res.BadRequestError("Send only one number of days"))
        } else {
          val days = daysSeq.head.toInt

          internalSupportServices.getGoogleAdsReport(
            days = days
          ) match {
            case Left(GetGoogleAdsError.SQLErrorWhileGettingList(err)) =>
              Future.successful(Res.ServerError(err))
            case Right(list) =>
              Future.successful(Res.Success("Found Ads data", Json.toJson(list)))
          }
        }
    }


  }

  def addToFreeDomain(): Action[JsValue] = internalSupportAuthService.isSupportCallAuthorized.async(parse.json) { implicit request =>
    val validateData = request.body.validate[DomainName]
    val Logger = request.logger
    val Res = request.response

    validateData match {
      case JsError(err) =>
        Future.successful(Res.JsValidationError(err))

      case JsSuccess(metadata, _) =>
        freeEmailDomainListService.addToFreeDomain(
          domain = metadata.domain
        )(Logger) match {
          case Left(AddingDomainToFreeListError.InvalidDomainError) =>
            Future.successful(Res.BadRequestError("Invalid Domain Format"))

          case Left(AddingDomainToFreeListError.SQLErrorWhileAddingToFreeList(err)) =>
            val errorMessage = Option(err.getMessage).getOrElse("")
            if (errorMessage.contains("free_email_service_domains_domain_name_key")) {
              Future.successful(Res.ServerError("Free domain already in the list", Some(err)))
            } else {
              Future.successful(Res.ServerError("Error While adding to the free list.", Some(err)))
            }

          case Left(AddingDomainToFreeListError.CouldNotAddToTheList) =>
            Logger.fatal("The domain was not added, Impossible Error.")
            Future.successful(Res.ForbiddenError("The domain was not added, Impossible Error."))


          case Left(AddingDomainToFreeListError.SQLErrorWhileFindingInTheList(err)) =>
            Future.successful(Res.ServerError("Error While Finding in the list", Some(err)))

          case Left(AddingDomainToFreeListError.AlreadyInTheList) =>
            Future.successful(Res.BadRequestError("Domain is Already in the list"))

          case Right(i) =>
            Future.successful(Res.Success(s"Domain was added in the free list id $i", Json.obj()))
        }
    }
  }

  def addToWhiteList(): Action[JsValue] = internalSupportAuthService.isSupportCallAuthorized.async(parse.json) { implicit request =>
    val validateData = request.body.validate[MetadataEmail]
    given Logger: SRLogger= request.logger
    val Res = request.response
    validateData match {
      case JsError(err) =>
        Future.successful(Res.JsValidationError(err))

      case JsSuccess(metadata, _) =>
        freeEmailDomainListService.addToWhiteList(email = metadata.accountEmail) match {

          case Left(AddingDomainToWhitelistError.InvalidEmailError) =>
            Future.successful(Res.BadRequestError("Wrong Email Format"))

          case Left(AddingDomainToWhitelistError.SQLErrorWhileAddingToWhiteList(err)) =>
            Future.successful(Res.ServerError("Error While adding to the whitelist.", Some(err)))

          case Left(AddingDomainToWhitelistError.SQLErrorWhileFindingInTheList(err)) =>
            Future.successful(Res.ServerError("Error While Finding in the list", Some(err)))

          case Left(AddingDomainToWhitelistError.AlreadyInTheList) =>
            Future.successful(Res.BadRequestError("email is Already in the list"))

          case Left(AddingDomainToWhitelistError.CouldNotAddToTheList) =>
            Logger.fatal("The domain was not added, Impossible Error.")
            Future.successful(Res.ForbiddenError("The domain was not added, Impossible Error."))

          case Right(i) =>
            Future.successful(Res.Success(s"Email was added in the free list id $i", Json.obj()))
        }
    }
  }

  def updateEmailOfAccount(): Action[JsValue] = internalSupportAuthService.isSupportCallAuthorized.async(parse.json) { implicit request =>
    val validateData = request.body.validate[UpdateEmail]
    val Logger = request.logger
    val Res = request.response
    validateData match {
      case JsError(err) =>
        Future.successful(Res.JsValidationError(err))

      case JsSuccess(metadata, _) =>
        internalSupportServices.updateEmail(data = metadata) match {
          case Left(EmailUpdateError.NewEmailAlreadyHasAnAccount) => Future.successful(
            Res.BadRequestError("New email already has an account, try transferring ownership.")
          )
          case Left(EmailUpdateError.SQLError(err)) =>

            Future.successful(
              Res.ServerError("Server error while updating email", Some(err))
            )
          case Left(EmailUpdateError.AccountNotFoundError) =>
            Future.successful(
              Res.NotFoundError(
                message = "Account with given email not found"
              )
            )
          case Left(EmailUpdateError.InvalidEmailError) =>
            Future.successful(
              Res.BadRequestError(
                message = "Invalid email format"
              )
            )
          case Left(EmailUpdateError.AccountNotFountAfterUpdateError) =>

            Logger.fatal(s"EmailUpdateError.AccountNotFountAfterUpdateError: request body: ${request.body}")

            Future.successful(
              Res.NotFoundError(
                "Account not found, after saving"
              )
            )
          case Right(updatedAccount) =>
            val updatedAccountJSON = Json.toJson(updatedAccount)
            Future.successful(Res.Success("Updated Successfully", updatedAccountJSON))
        }
    }
  }

  def updateOrganizationTrialEmail(): Action[JsValue] = internalSupportAuthService.isSupportCallAuthorized.async(parse.json) { implicit request =>
    val validateData = request.body.validate[UpdateAccountTrialEmail]
    val Logger = request.logger
    val Res = request.response

    validateData match {
      case JsSuccess(value, _) =>

        val account = internalSupportServices.updateTrialByEmail(value)
        account match {

          case Left(TrialExtendError.SQLError(err)) =>

            Future.successful(
              Res.ServerError("Server error while extending trial", Some(err))
            )

          case Left(TrialExtendError.OrganizationNotFoundError) =>

            Future.successful(
              Res.NotFoundError(
                message = "Organization not found"
              )
            )

          case Left(TrialExtendError.OrgNotFoundAfterSavedImpossibleError) =>

            Logger.fatal(s"TrialExtendError.OrgNotFoundAfterSavedImpossibleError: request body: ${request.body}")

            Future.successful(
              Res.NotFoundError(
                "Organization not found, after saving"
              )
            )

          case Left(TrialExtendError.InvalidEmailError) =>

            Future.successful(
              Res.BadRequestError(
                message = "Invalid email format"
              )
            )

          case Left(TrialExtendError.DaysGreaterThan31ValidationError) =>

            Future.successful(
              Res.BadRequestError(
                message = "Cannot extend trial by more than 31 days"
              )
            )

          case Left(TrialExtendError.AlreadyPaidOrganizationError) =>

            Future.successful(
              Res.BadRequestError(
                message = "Cannot extend trial for an organization that has already paid"
              )
            )

          case Right(updatedOrg) =>
            val organizationJson = Json.toJson(updatedOrg)
            Future.successful(Res.Success("Updated Successfully", organizationJson))

        }

      case JsError(e) =>

        Future.successful(Res.JsValidationError(e))
    }

  }


  def getUnderReviewEntity: Action[AnyContent] = internalSupportAuthService.isSupportCallAuthorized.async { implicit request =>
    given Logger: SRLogger = request.logger
    val Res = request.response
    val validateDataEntityType = request.queryString.get("entity_type")
    val validateDataSendingStatus = request.queryString.get("sending_status")

    internalSupportServices.validateDataForgetUnderReviewEntity(
      validateDataEntityType = validateDataEntityType,
      validateDataSendingStatus = validateDataSendingStatus
    ) match {
      case Failure(err) =>
        Logger.error("Wrong data send from support app", err)
        Future.successful(Res.BadRequestError("Send Correct Sending Status and Entity type"))
      case Success((entityType, sendingStatuses)) =>

        internalSupportServices.getUnderReviewEntities(
          emailSendingEntityType = entityType,
          sendingStatuses = sendingStatuses
        ) match {
          case Left(GetUnderReviewOrgError.ErrorWhileGettingCurrentStatus(err)) =>
            Future.successful(Res.ServerError("Error while getting current status from db", Some(err)))

          case Left(GetUnderReviewOrgError.ErrorWhileGettingFromDB(err)) =>
            Future.successful(Res.ServerError("Error while getting data from db", Some(err)))
          case Right(list) =>
            Future.successful(Res.Success("Found Entities", Json.toJson(list)))

        }
    }
  }

  def getCsvUploadData(): Action[AnyContent] = internalSupportAuthService.isSupportCallAuthorized.async { implicit request =>

    given Logger: SRLogger = request.logger
    val Res = request.response
    val csvData = internalSupportServices.getCsvUploadData()
    csvData match {
      case Left(CsvUploadError.SQLError(err)) =>
        Future.successful(Res.ServerError("Server error while fetching Csv Queue Data", Some(err)))

      case Right(csvUploadData) =>
        Future.successful(Res.Success("fetched Successfully",
          data = Json.obj(
            "csv_list" -> csvUploadData
          )))
    }
  }


  def updateEmailSendingStatus(): Action[JsValue] = internalSupportAuthService.isSupportCallAuthorized.async(parse.json) { implicit request =>
    val validateData = request.body.validate[UpdateEmailSendingStatusForm]
    given Logger: SRLogger= request.logger
    val Res = request.response

    validateData match {
      case JsError(err) =>
        Future.successful(Res.JsValidationError(err))

      case JsSuccess(updateEmailSendingStatusForm, _) =>
        internalSupportServices.updateEmailSendingStatus(
          updateEmailSendingStatusForm = updateEmailSendingStatusForm,
          oldSendEmailStatus = None,
          isSupport = true
        ) match {
          case Left(UpdateEmailSendingStatusError.ErrorWhileAddingInDB(err)) =>
            Future.successful(Res.ServerError("Server error while updating email send status", Some(err)))

          case Right(_) =>
            Future.successful(Res.Success("Status Updated", Json.obj()))

        }
    }
  }

  /**
   * Path to Referral Related UML Diagram:
   * seq-diagrams/sr-referral-program/6-update-affiliate-commission-status.plantuml
   */
  def declineAffiliateCommission(): Action[JsValue] = internalSupportAuthService.isSupportCallAuthorized.async(parse.json) { implicit request =>

    val validateData = request.body.validate[DenyAffiliateCommissionData]
    val Res = request.response
    given logger: SRLogger = request.logger

    validateData match {
      case JsError(err) =>
        Future.successful(Res.JsValidationError(err))

      case JsSuccess(affiliateEventData: DenyAffiliateCommissionData, _) =>

        affiliateTrackingService.denyAffiliateCommission(
            affiliateEventData = affiliateEventData
          )
          .map {
            case TrackAffiliateResponseStatus.ResSuccess =>
              logger.info(s"Successfully denied affiliate commission - $affiliateEventData")
              Res.Success(
                "Successfully denied affiliate commission",
                Json.obj("denied" -> true)
              )

            case TrackAffiliateResponseStatus.ResFailure =>
              logger.fatal(s"FAILED to deny affiliate commission - eventData: $affiliateEventData")
              Res.BadRequestError("Failed to deny affiliate commission")
          }
          .recover {
            case e =>
              logger.error("Failed to deny affiliate commission", err = e)
              Res.ServerError(e.getMessage, e = Some(e))
          }
    }
  }


  def getFunnelStageData: Action[AnyContent] = internalSupportAuthService.isSupportCallAuthorized.async { implicit request =>

    val Logger = request.logger
    val Res = request.response
    val validateData = request.queryString.get("days").map(daysSeq => Try {
      daysSeq.head.toInt
    })

    val funnelStage = validateData match {
      case None =>
        srUserFeatureUsageEventService.getFunnelStageForTrialUsers(days = None)
      case Some(Success(days)) =>
        srUserFeatureUsageEventService.getFunnelStageForTrialUsers(days = Some(days))
      case Some(Failure(err)) =>
        Left(throw new Exception("Error while parsing the params", err))
    }
    funnelStage match {
      case Left(FunnelStageError.ErrorWhileParsingParam(err)) =>
        Future.successful(Res.ServerError("Server error parsing params", Some(err)))

      case Left(FunnelStageError.SQLError(err)) =>
        Future.successful(Res.ServerError("Server error while fetching Funnel Data For TrialUser", Some(err)))

      case Right(funnelStageData) =>
        Future.successful(Res.Success("fetched Successfully",
          data = Json.obj(
            "funnel_data" -> funnelStageData
          )))
    }
  }

  def getAcquisitionReport: Action[JsValue] = internalSupportAuthService.isSupportCallAuthorized.async(parse.json) { implicit request =>
    val logger = request.logger
    val Res = request.response

    Future.successful {

      Try {
        (
          (request.body \ "start_date").as[DateTime],
          (request.body \ "end_date").as[DateTime]
        )
      } match {
        case Failure(exception) =>
          logger.error("Failed to parse startDate and endDate", err = exception)
          Res.BadRequestError("Failed to parse data")

        case Success((startDate: DateTime, endDate: DateTime)) =>

          internalCSDReportService.getAcquisitionReport(
            startDate = startDate,
            endDate = endDate
          ) match {
            case Failure(e) =>
              logger.error("Failed to Get Acquisition Report", err = e)
              Res.ServerError("Cannot Get Acquisition Report", Some(e))

            case Success(customerAcquisitionReportList: List[CustomerAcquisitionReport]) =>
              logger.info(s"Successfully fetch Acquisition Report $customerAcquisitionReportList")

              Res.Success(
                "Successfully fetched Acquisition Report.",
                data = Json.obj("acquisition_report" -> customerAcquisitionReportList)
              )
          }
      }
    }
  }

  def updateAcquisitionReport: Action[JsValue] = internalSupportAuthService.isSupportCallAuthorized.async(parse.json) { implicit request =>
    val logger = request.logger
    val Res = request.response

    Future.successful {

      request.body.validate[AcquisitionFieldData] match {
        case JsError(e) =>
          logger.error("Failed to parse AcquisitionFieldData")
          Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(acquisitionFieldData: AcquisitionFieldData, _) =>

          internalCSDReportService.updateAcquisitionReport(
            acquisitionFieldData = acquisitionFieldData
          ) match {
            case Failure(e) =>
              logger.error("Failed to update Acquisition Report", err = e)
              Res.ServerError("Cannot update Acquisition Report", Some(e))

            case Success(updateCount: Int) =>
              logger.info(s"Successfully updated Acquisition Report $updateCount")

              Res.Success(
                "Successfully updated Acquisition Report.",
                data = Json.obj("update_count" -> updateCount)
              )
          }
      }
    }
  }

  def updateAdoptionReport: Action[JsValue] = internalSupportAuthService.isSupportCallAuthorized.async(parse.json) { implicit request =>
    val logger = request.logger
    val Res = request.response

    Future.successful {

      request.body.validate[UpdateAdoptionReportData] match {
        case JsError(e) =>
          logger.error(s"Failed to parse UpdateAdoptionReportData $e")
          Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(updateAdoptionReportData: UpdateAdoptionReportData, _) =>

          internalCSDReportService.updateAdoptionReport(
            updateAdoptionReportData = updateAdoptionReportData
          ) match {
            case Failure(e) =>
              logger.error("Failed to update Adoption Report", err = e)
              Res.ServerError("Cannot update Adoption Report", Some(e))

            case Success(0) =>
              logger.error("Failed to update Adoption Report, updateCount: 0")
              Res.BadRequestError("Cannot update Adoption Report, Not Found")

            case Success(updateCount: Int) =>
              logger.info(s"Successfully updated Adoption Report $updateCount")

              Res.Success(
                "Successfully updated Adoption Report.",
                data = Json.obj("update_count" -> updateCount)
              )
          }
      }
    }
  }

  def getAdoptionStageData: Action[AnyContent] = internalSupportAuthService.isSupportCallAuthorized.async { implicit request =>
    val logger = request.logger
    val Res = request.response

    Future.successful {
      srUserFeatureUsageEventService.getAdoptionStageReport(logger = logger) match {
        case Failure(e) =>
          Res.ServerError("Cannot get Adoption Stage Data", Some(e))

        case Success(internalSRUsageResults: List[InternalSRUsageResult]) =>
          Res.Success(
            "Successfully fetched Adoption Report.",
            data = Json.obj(
              "adoption_data" -> internalSRUsageResults
            )
          )
      }
    }
  }

  def getAudienceReportData: Action[AnyContent] = internalSupportAuthService.isSupportCallAuthorized.async { implicit request =>
    val logger = request.logger
    val Res = request.response


    val report_type_try = InternalAudienceReportType.withName(
      request.getQueryString("type").getOrElse(InternalAudienceReportType.SubscribedActive.toString)
    )

    Future.successful {

      report_type_try match {

        case Failure(exception) =>
          logger.error("Parsing InternalAudienceReportType Failed", err = exception)
          Res.ServerError("Invalid Report Type", None)

        case Success(report_type) =>

          srUserFeatureUsageEventService.getAudienceReportData(report_type = report_type, logger = logger) match {
            case Failure(e) =>
              Res.ServerError("Cannot get Audience Report Data", Some(e))

            case Success(audienceReportData: List[AudienceReportData]) =>
              Res.Success(
                "Successfully fetched Audience Report.",
                data = Json.obj(
                  "audience_data" -> audienceReportData
                )
              )
          }
      }
    }
  }

  def getAffiliateTrackingData: Action[AnyContent] = internalSupportAuthService.isSupportCallAuthorized.async { implicit request =>

    val logger = request.logger
    val Res = request.response

    Future.successful {
      affiliateTrackingService.getAffiliateTrackingData(logger = logger)
      match {
        case Failure(e) =>
          Res.ServerError("Cannot get Affiliate Customers from DB", Some(e))

        case Success(orgIds) =>
          Res.Success("Got Affiliate Customer Org Ids from DB", Json.obj(
            "org_ids" -> orgIds
          ))
      }
    }

  }

  /**
   * Path to Referral Related UML Diagram:
   * seq-diagrams/sr-referral-program/3-get-pending-coupon-approvals.plantuml
   */
  def getPendingCouponApprovals: Action[AnyContent] = internalSupportAuthService.isSupportCallAuthorized.async { implicit request =>

    val logger = request.logger
    val Res = request.response

    val fromDate: DateTime = request.getQueryString("from")
      .map(DateTime.parse(_, ISODateTimeFormat.dateTimeParser()))
      .getOrElse(DateTime.now().minusMonths(1))

    Future.successful {

      affiliateTrackingService.getPendingCouponApprovals(fromDate = fromDate) match {
        case Failure(e) =>
          logger.error(s"Failed to fetch pending coupon approvals from date $fromDate", err = e)
          Res.ServerError("Cannot get Pending Coupons Approvals", Some(e))

        case Success(pendingCouponApprovals: List[ReferralCouponDetails]) =>
          logger.info(s"Successfully fetch pending coupon approvals from date $fromDate - $pendingCouponApprovals")
          Res.Success(
            "Successfully fetched Pending Coupon Approvals",
            Json.obj("pending_coupon_approvals" -> pendingCouponApprovals)
          )
      }
    }
  }


  def getPromoterDetails =
    internalSupportAuthService.isSupportCallAuthorized.async(parse.json) { implicit request =>

      given Logger: SRLogger = request.logger
      val Res = request.response

      request.body.validate[OrgIdsForPromoterDetails] match {

        case JsError(e) =>

          Logger.error(s"Failed to parse OrgIdsForPromoterDetails - e: $e")

          Future.successful(
            Res.JsValidationError(errors = e, requestBody = Some(request.body))
          )

        case JsSuccess(orgIdsForPromoterDetails: OrgIdsForPromoterDetails, _) =>

          affiliateTrackingService.getPromoterDetailsByOrgId(
            orgIds = orgIdsForPromoterDetails.org_ids,
          ) match {

            case Failure(exception) =>

              Logger.error("Failed to get promoter details", err = exception)

              Future.successful(
                Res.ServerError("Cannot get promoter details", Some(exception))
              )
            case Success(value) =>

              Logger.info("Successfully got promoter details")

              Future.successful(
                Res.Success("Successfully got Promoter Details", Json.toJson(value))
              )

          }

      }

    }


  /**
   * Path to Referral Related UML Diagram:
   * seq-diagrams/sr-referral-program/4-update-coupon-status.plantuml
   */
  def updateReferralCouponStatus: Action[JsValue] = internalSupportAuthService.isSupportCallAuthorized.async(parse.json) { implicit request =>

    val logger = request.logger
    val Res = request.response

    Future.successful {

      request.body.validate[UpdateReferralCouponDetails] match {
        case JsError(e) =>
          logger.error("Failed to parse UpdateReferralCouponDetails")
          Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(updateReferralCouponDetails: UpdateReferralCouponDetails, _) =>

          val couponStatus = if (updateReferralCouponDetails.approve_coupon) {
            ReferralCouponStatus.Approved
          } else {
            ReferralCouponStatus.Denied
          }

          affiliateTrackingService.updateReferralCouponStatus(
            orgId = updateReferralCouponDetails.org_id,
            updateCouponStatus = couponStatus,
            logger = logger
          ) match {
            case Failure(exception) =>
              logger.error(s"Failed to update referral coupon status for org_id: ${updateReferralCouponDetails.org_id.id}", err = exception)
              Res.ServerError(s"Failed to update referral coupon status for org_id: ${updateReferralCouponDetails.org_id.id}", Some(exception))

            case Success(None) =>
              logger.fatal(s"Failed to update referral coupon status for org_id: ${updateReferralCouponDetails.org_id.id} - 0")
              Res.BadRequestError(s"Failed to update referral coupon status for org_id: ${updateReferralCouponDetails.org_id.id}")

            case Success(Some(orgId: OrgId)) =>
              logger.info(s"Successfully updated referral coupon status to ${couponStatus.toString} for orgId: ${updateReferralCouponDetails.org_id}")
              Res.Success(
                s"Successfully updated referral coupon status to $couponStatus",
                Json.obj("update_coupon_status" -> "success")
              )
          }
      }
    }
  }

  def blockOrganization: Action[JsValue] = internalSupportAuthService.isSupportCallAuthorized.async(parse.json) { implicit request =>
    val validateData = request.body.validate[UpdateOrgMetadata]
    val Res = request.response
    val logger = request.logger
    validateData match {
      case JsSuccess(updateOrgMetadata, _) =>
        val updated = internalSupportServices.blockOrganization(updateOrgMetadata = updateOrgMetadata)
        updated match {
          case Left(BlockOrganizationError.InvalidEmailError) =>
            Future.successful(
              Res.BadRequestError(
                message = "Invalid email format"
              )
            )
          case Left(BlockOrganizationError.SQLError(err)) =>
            Future.successful(
              Res.ServerError("Server error blocking org", Some(err))
            )
          case Left(BlockOrganizationError.OrganizationNotFoundError) =>
            Future.successful(
              Res.NotFoundError(
                message = "Email not found or not of the owner"
              )
            )
          case Left(BlockOrganizationError.OrgNotFountAfterUpdateError) =>
            logger.fatal(s"MetadataUpdateError.AccountNotFountAfterUpdateError: request body: ${request.body}")

            Future.successful(
              Res.NotFoundError(
                "Account not found, after saving"
              )
            )
          case Right(updatedOrg) =>
            val updatedOrgJSON = Json.toJson(updatedOrg)
            Future.successful(Res.Success("Updated Successfully", updatedOrgJSON))

        }
      case JsError(e) =>
        Future.successful(Res.JsValidationError(e))
    }

  }

  /**
   * Path to Referral Related UML Diagram:
   * seq-diagrams/sr-referral-program/6-update-affiliate-commission-status.plantuml
   */
  def approveAffiliateCommission(): Action[JsValue] = internalSupportAuthService.isSupportCallAuthorized.async(parse.json) { implicit request =>
    val res = request.response
    given logger: SRLogger = request.logger

    request.body.validate[ApproveAffiliateCommissionData] match {
      case JsError(e) =>
        Future.successful(res.JsValidationError(e))

      case JsSuccess(data: ApproveAffiliateCommissionData, _) =>
        affiliateTrackingService.approveAffiliateCommission(
            data = data
          ).map {
            case TrackAffiliateResponseStatus.ResSuccess =>
              logger.info(s"Successfully approved affiliate commission $data")
              res.Success(
                "Successfully approved affiliate commission",
                Json.obj("approved" -> true)
              )

            case TrackAffiliateResponseStatus.ResFailure =>
              logger.fatal(s"FAILED to approve affiliate commission - eventData: $data")
              res.BadRequestError("Failed to approve affiliate commission")
          }
          .recover {
            case e =>
              logger.error(s"Error encountered while approving affiliate commission for org_id: ${data.orgId} :: pg_customer_id: ${data.pg_customer_id} :: pg_event_id: ${data.billing_event_id}", err = e)
              res.ServerError(e.getMessage, Some(e))
          }
    }
  }

  def enableAgencyPlan(): Action[JsValue] = internalSupportAuthService.isSupportCallAuthorized.async(parse.json) { implicit request =>
    val res = request.response
    val logger: SRLogger = request.logger

    request.body.validate[EnableAgencyPlanDetails] match {
      case JsError(e) =>
        Future.successful(res.JsValidationError(e))

      case JsSuccess(enableAgencyPlanDetails: EnableAgencyPlanDetails, _) =>

        Future.successful {

          internalSupportServices.enableAgencyPlan(
            enableAgencyPlanDetails = enableAgencyPlanDetails,
            logger = logger
          ) match {
            case Failure(exception) =>
              logger.error(s"Failed to enable agency plan for org: $enableAgencyPlanDetails", err = exception)
              res.ServerError(exception.getMessage, Some(exception))

            case Success(None) =>
              val errMsg = s"Failed to fetch org with owner email: ${enableAgencyPlanDetails.owner_email}"
              logger.error(errMsg)
              res.NotFoundError(errMsg)

            case Success(Some(orgForSupportApp: OrganizationForSupportApp)) =>
              logger.info(s"Success enabled agency plan for org: $enableAgencyPlanDetails")
              res.Success(
                s"Success enabled agency plan for org: $enableAgencyPlanDetails",
                data = Json.toJson(orgForSupportApp)
              )
          }
        }
    }
  }


  def updateSignupType(): Action[JsValue] = internalSupportAuthService.isSupportCallAuthorized.async(parse.json) { implicit request =>
    val validateData = request.body.validate[UpdateSignupType]
    given Logger: SRLogger= request.logger
    val Res = request.response
    validateData match {
      case JsError(e) =>
        Future.successful(Res.JsValidationError(e))
      case JsSuccess(data, _) =>
        internalSupportServices.updateEmailSignupType(
          email = data.email,
          signupType = data.signupType
        ) match {
          case Left(UpdateSignupTypeError.NoAccountFoundAfterUpdate) =>
            Logger.fatal("IMPOSSIBLE ERROR: NO ACCOUNT FOUND AFTER UPDATING SIGNUP TYPE")
            Future.successful(Res.NotFoundError("NO ACCOUNT FOUND AFTER UPDATING SIGNUP TYPE"))

          case Left(UpdateSignupTypeError.SQLErrorWhileGettingAccount(err)) =>
            Future.successful(Res.ServerError("Error While Getting Account", Some(err)))

          case Left(UpdateSignupTypeError.SQLErrorWhileUpdating(err)) =>
            Future.successful(Res.ServerError("Error While Updating Email Signup Type", Some(err)))

          case Right(value) =>
            Future.successful(Res.Success("Updated Successfully", Json.toJson(value)))
        }

    }
  }

  def getCompanyData(orgId: Long): Action[AnyContent] = internalSupportAuthService.isSupportCallAuthorized.async { implicit request =>
    given Logger: SRLogger= request.logger

    val Res = request.response

    accountService.getCompanyDataByOrgId(orgId = orgId) match {
      case Failure(err) => Future.successful(Res.ServerError("Error occurred while fetching Company details", Some(err)))

      case Success(company) => Future.successful(Res.Success("Found Company details", Json.obj("company" -> company)))
    }

  }

  def getReportForMailServerForInternalReport(): Action[AnyContent] = internalSupportAuthService.isSupportCallAuthorized.async { implicit request =>
    given Logger: SRLogger= request.logger

    val Res = request.response

    srMailServerService.getReportForMailServerForInternalReport() match {
      case Failure(err) => Future.successful(Res.ServerError("Error occurred while fetching Rep mail server report", Some(err)))

      case Success(data) => Future.successful(Res.Success("Found Rep mail server report", Json.toJson(data)))
    }

  }


  // This call will be made to this api from the CSD support app.

  def diagnoseCampaign(cid: Long, tid: Long): Action[AnyContent] = internalSupportAuthService.isSupportCallAuthorized.async { implicit request =>
    given logger: SRLogger = request.logger

    /*
    this is an internal api ,
    we dont want our Support team to think too much
    so we take in a campaign id - and determine the org and team_id

    then we run all the manual actions that we would have run from the sql terminal

    NxD notes todo: reuse the work done in CampaignService.getCampaignPauseReasonAndCounts
    Possible problems :
    1. daily limit reached
       a. email setting has reached the daily sending limit.
          the email setting in this campaign is shared across multiple campaigns
          so we need to pull sending stats from those campaigns too to get the no of emails sent
          and find out if we have hit the integrated email sending limits
       b. campaign  has reached the daily sending limit
    2.1 weekly prospect limit reached and all prospects are from the same account domain
    2.2 daily  prospect limit reached and all prospects are from the same account domain
    3. campaign sending start and end time, and campaign was started after the sending window.
    5. the campaign is paused because of some reason - paused by our support team for example
    6. there were errors with the password for this email setting id - (find out where
         those errors are stored)
    */

    val Res = request.response

    val campaignPauseReasonAndCounts = campaignService.getCampaignPauseReasonAndCounts(
      campaignId = CampaignId(cid),
      teamId = TeamId(tid)
    )

    Future.successful {
      if (campaignPauseReasonAndCounts.campaignPauseReason == CampaignPauseReason.ServerErrorOccurredWhileAnalyzing) {
        Res.ServerError("Could not analyze campaign due to some server error", None)
      }
      else {
        Res.Success("Diagnose Successful", Json.obj(
          "campaign_pause_reason_and_counts" -> campaignPauseReasonAndCounts
        ))
      }
    }
  }

}
