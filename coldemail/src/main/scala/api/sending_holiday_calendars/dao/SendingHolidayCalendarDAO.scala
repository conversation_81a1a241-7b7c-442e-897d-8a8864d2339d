package api.sending_holiday_calendars.dao

import api.sending_holiday_calendars.models.SendingHolidayCalendar
import scalikejdbc.{AutoSession, DB, WrappedResultSet, scalikejdbcSQLInterpolationImplicitDef}

import scala.util.Try



class SendingHolidayCalendarDAO
 extends SendingHolidayCalendarDAOTrait {

  private implicit val session: AutoSession = AutoSession

  private def fromDb(
    rs: WrappedResultSet
  ): Try[SendingHolidayCalendar] = Try {

    SendingHolidayCalendar(
      calendar_id = rs.long("id"),
      calendar_name = rs.string("calendar_name")

    )
  }

  final def createCalendar(
    calendarName: String,
    orgId: Long,
    accountId: Long,
    accountName: String
  ): Try[Option[SendingHolidayCalendar]] = Try {

    DB.autoCommit { implicit session =>

      sql"""
          INSERT INTO sending_holiday_calendars
          (
            created_by,
            created_by_name,
            org_id,
            calendar_name

          )
          VALUES (
            $accountId,
            $accountName,
            $orgId,
            ${calendarName.trim}


          )

          ON CONFLICT DO NOTHING

          RETURNING *;
        """
        .map(rs => fromDb(rs).get)
        .single
        .apply()

    }

  }

  final def getAllCalendars(
    orgId: Long
  ): Try[Seq[SendingHolidayCalendar]] = Try {
    DB readOnly { implicit session =>

      sql"""
      SELECT *
      FROM sending_holiday_calendars
      WHERE org_id = $orgId
      """
        .map(fromDb(_).get)
        .list
        .apply()

    }
  }

  final def findCalendarByOrgId(
    orgId: Long,
    calendarId: Long
  ): Try[Option[SendingHolidayCalendar]] = Try {
    DB readOnly { implicit session =>

      sql"""
      SELECT *
      FROM sending_holiday_calendars
      WHERE org_id = $orgId
      AND id = $calendarId
      """
        .map(fromDb(_).get)
        .single
        .apply()

    }
  }


  final def deleteCalendar(
    calendarId: Long,
    orgId: Long
  ): Try[Long] = Try{
    DB autoCommit { implicit session =>

      sql"""
           DELETE FROM sending_holiday_calendars
           WHERE
           id = $calendarId
           AND org_id = $orgId
           RETURNING id
         """
        .map(_.long("id"))
        .single
        .apply()
        .get

    }
  }
}
