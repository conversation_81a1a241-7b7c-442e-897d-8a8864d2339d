package api.sending_holiday_calendars.dao

import api.sending_holiday_calendars.models.SendingHolidayCalendar

import scala.util.Try

trait SendingHolidayCalendarDAOTrait {

  def findCalendarByOrgId(
    orgId: Long,
    calendarId: Long
  ): Try[Option[SendingHolidayCalendar]]

  def createCalendar(
    calendarName: String,
    orgId: Long,
    accountId: Long,
    accountName: String
  ): Try[Option[SendingHolidayCalendar]]

  def getAllCalendars(
    orgId: Long
  ): Try[Seq[SendingHolidayCalendar]]

  def deleteCalendar(
    calendarId: Long,
    orgId: Long
  ): Try[Long]
}
