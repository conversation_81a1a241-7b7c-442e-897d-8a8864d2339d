package api.sending_holiday_calendars.dao

import api.sending_holiday_calendars.models.{SendingHolidayCalendarDate, SendingHolidayForAddingToCalendar}

import scala.util.Try

trait SendingHolidayCalendarDateDAOTrait {

  def getHolidaysInCalendar(
    calendarId: Long,
    orgId: Long
  ): Try[Seq[SendingHolidayCalendarDate]]

  def addHolidaysToCalendar(
    calendarId: Long,
    accountId: Long,
    accountName: String,
    holidays: Seq[SendingHolidayForAddingToCalendar],
    orgId: Long
  ): Try[Seq[SendingHolidayCalendarDate]]

  def removeHolidayFromCalendar(
    calendarId: Long,
    orgId: Long,
    holidayId: Long
  ): Try[Option[Long]]


}
