package api.sr_common_auth.dao

import api.accounts.models.{AccountId, OrgId}
import api.sr_common_auth.models.SRRefreshTokenAndAccessTokenData
import scalikejdbc.DB
import scalikejdbc.interpolation.SQLSyntax
import scalikejdbc.scalikejdbcSQLInterpolationImplicitDef
import scalikejdbc.jodatime.JodaWrappedResultSet.fromWrappedResultSetToJodaWrappedResultSet

import scala.util.{Failure, Success, Try}

class AuthDAO {
  def getAccessTokenDataFromDB(  //
                                accountId: AccountId,
                                orgId: Option[OrgId] /*
                                  org id will be none in the case when play session is present but redis is not
                                  So in order to issue logout from hydra ,we are allowing logout with just accountId /
                                */
                              ): Try[Option[SRRefreshTokenAndAccessTokenData]] = Try {

    val orgIdCheckClause: SQLSyntax = if (orgId.nonEmpty) {
      sqls" and org_id = ${orgId.get.id} "
    } else {
      sqls" "
    }

    DB readOnly { implicit session =>
      sql"""
           SELECT
            sr_refresh_token,
            sr_access_token,
            sr_access_token_expires_at,
            sr_scope
           FROM accounts
           WHERE
           id = ${accountId.id}
           ${orgIdCheckClause}
           ;
           """
        .map(rs => {
          val accessTokenOpt = rs.stringOpt("sr_access_token")
          if(accessTokenOpt.isDefined){
            SRRefreshTokenAndAccessTokenData(
              access_token = accessTokenOpt.get,
              refresh_token = rs.string("sr_refresh_token"),
              expires_at = rs.jodaDateTime("sr_access_token_expires_at"),
              scope = rs.string("sr_scope")
            )
          } else {
            throw new Exception("Access Token Related Fields Not Found From Db")
          }
        }
        )
        .single
        .apply()


    }
  }
  def updateAccessTokenDataInDB(accountId:AccountId,orgId: OrgId, refreshTokenAndAccessTokenData: SRRefreshTokenAndAccessTokenData):Try[Int] = Try{

    DB autoCommit { implicit session =>

      sql"""
           UPDATE accounts
           SET
            sr_refresh_token = ${refreshTokenAndAccessTokenData.refresh_token},
            sr_access_token = ${refreshTokenAndAccessTokenData.access_token},
            sr_access_token_expires_at = ${refreshTokenAndAccessTokenData.expires_at},
            sr_scope = ${refreshTokenAndAccessTokenData.scope}
           where
           id = ${accountId.id}
           and org_id = ${orgId.id}
           ;
         """
        .update
        .apply()



    }
  }
}
