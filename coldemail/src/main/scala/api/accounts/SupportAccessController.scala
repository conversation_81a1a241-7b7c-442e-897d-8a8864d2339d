package api.accounts

import api.ApiVersion
import api.accounts.service.ResetUserCacheUtil
import play.api.libs.json.{JsError, JsSuccess, JsValue, Json}
import play.api.libs.ws.WSClient
import play.api.mvc.{Action, AnyContent, BaseController, ControllerComponents}
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class SupportAccessController (
                                protected val controllerComponents: ControllerComponents,
                                authUtils: AuthUtils,
                                accountService: AccountService,
                                resetUserCacheUtil: ResetUserCacheUtil,
                                implicit val wsClient: WSClient
                              ) extends BaseController {

  private implicit val ec: ExecutionContext = controllerComponents.executionContext

  def toggleAccessToInboxToSupportUser(): Action[JsValue] = authUtils.isLoggedIn().async(parse.json) { (request: AccountRequest[JsValue]) =>
    Future {

      given Logger: SRLogger= request.Logger
      val Res = request.Response
      val validateData = request.body.validate[GrantAccessForm]

      val accountId = request.account.internal_id

      validateData match {
        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>
          accountService.switchAccessToInboxToSupportUser(
            accountId = accountId,
            give_access = data.give_access,
            account_email = data.account_email,
            org_name = data.org_name
          ) match {

            case Failure(err) =>
              Logger.fatal(s"Error While Granting Access To support user for accountId - $accountId")
              Res.ServerError("Unable to change the data, Please contact support", Some(err))

            case Success(account_id) =>
              resetUserCacheUtil._resetTeamCache(aid = accountId)
              accountService.find(id = account_id) match {
                case Failure(err) =>
                  Logger.fatal(s"Error While Getting Account  for accountId - $accountId")
                  Res.ServerError("Unable to change the data, Please contact support", Some(err))
                case Success(account) =>
                  Res.Success("Access Granted!", Json.toJson(account))

              }

          }
      }
    }
  }

  def getHistoryForAccessToInboxToSupportUser = authUtils.isLoggedIn().async { (request: AccountRequest[AnyContent]) =>

    Future {

      val Logger = request.Logger
      val Res = request.Response
      val accountId = request.account.internal_id

      accountService.getHistoryForAccountId(
        accountId = accountId
      ) match {
        case Failure(err) =>
          Logger.fatal(s"Error While getting Access To support user for accountId - $accountId")
          Res.ServerError("Unable to get data, Please contact support", Some(err))
        case Success(value) =>
          Res.Success("history found", Json.toJson(value))

      }
    }
  }

}
