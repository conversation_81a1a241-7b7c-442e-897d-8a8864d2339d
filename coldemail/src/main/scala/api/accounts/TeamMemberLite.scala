package api.accounts

import eventframework.SrResourceTypes
import org.joda.time.DateTime
import play.api.libs.json.{JsError, JsObject, JsResult, JsSuccess, JsValue, Json, OFormat, Reads}
import play.api.libs.json.JodaWrites._
import play.api.libs.json.JodaReads._

import scala.util.{Failure, Success, Try}

// used for all_members api
case class TeamMemberLite(

  user_id: Long,
  first_name: Option[String],
  last_name: Option[String],
  email: String,
  active: Boolean,
  timezone: Option[String],
  twofa_enabled: Boolean,
  created_at: DateTime,
  user_uuid: AccountUuid,
  team_role: TeamAccountR<PERSON>,

)

object TeamMemberLite {

  // given reads: Reads[TeamMemberLite] = Json.reads[TeamMemberLite]

  given format: OFormat[TeamMemberLite] = Json.format[TeamMemberLite]

  def apiStructure(i: TeamMemberLite): JsObject = {

    Json.obj(

      "object" -> SrResourceTypes.USER.toKey,

      "id" -> i.user_id,
      "first_name" -> i.first_name,
      "last_name" -> i.last_name,
      "email" -> i.email,
      "active" -> i.active

    )

  }

}
