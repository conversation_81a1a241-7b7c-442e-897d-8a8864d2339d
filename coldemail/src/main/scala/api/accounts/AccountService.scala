package api.accounts

import api.accounts.dao.{AffiliateDetails, OrganizationDAO, OrganizationJedisCacheDao, SrFeatureCredits, SupportAccessToUserAccountDAO, TeamsDAO}
import api.accounts.models.{AccessEntityType, AccessStatusType, AccountId, Company, OrgId, OrgUuid, Persona, SignupType, Subscription_data, SupportAccessToUserAccount}
import api.accounts.service.{OrganizationService, ResetUserCacheUtil, UserRedisKeyService}
import io.sr.billing_common.models.{AddonLicenceType, OneTimePlanType, PlanID, PlanType}
import api.team.service.TeamService
import api.accounts.sr_api_key_type.models.SRApiKeyType
import api.call.models.TwilioSubAccountStatus
import api.internal_support.service.AccessTokenService
import api.reports.models.UtmData
import api.spammonitor.dao.EmailSendingStatusDAO
import api.spammonitor.model.{EmailSendingEntityTypeData, SendEmailStatusData}
import api.team_inbox.dao_service.ReplySentimentDAOService
import api.team_inbox.model.ReplySentimentType
import api.team_inbox.service.{ReplySentimentService, ReplySentimentUuid, TeamInboxService}
import api.{AppConfig, CacheServiceJedis}
import org.joda.time.DateTime
import play.api.libs.json.{Json, Reads}
import scalikejdbc.{DBSession, SQLSyntax, scalikejdbcSQLInterpolationImplicitDef, sqls}
import utils.dbutils.DBUtils
import utils.timezones.TimezoneUtils
import utils.{AddonLimitReachedException, AppConfigUtil, Helpers, PlanLimitService, SRLogger}
import utils.helpers.LogHelpers
import api.calendar_app.CalendarUserId
import api.accounts.TeamId
import api.accounts.dao_service.AccountDAOService
import api.accounts.dao_service.OrganizationDAOService
import api.campaigns.services.CampaignId
import api.pipelines.models.Pipeline
import api.pipelines.services.{OpportunitiesDefaultSetupData, OpportunitiesDefaultSetupService}
import api.general.{GeneralSettingDAO, GeneralSettingService}
import api.rep_mail_servers.models.RepMailServer
import api.rep_mail_servers.services.SrMailServerService
import api.teamStats.models.TeamInfo
import api.teams_metadata.TeamsMetadataService
import io.smartreach.companies_api.api.enrich_company_info.dao.EnrichedCompanyData
import utils.cache.models.SrResetCacheInterval
import utils.cronjobs.SubordinatesByTeamIdForReportGeneration
import utils.featureflags.services.SrFeatureFlags
import utils.phonenumber.PhoneNumberValidator
import utils.sr_resource.services.{SrResourceDaoService, SrResourceWithoutTenantDaoService}
import utils.uuid.SrUuidUtils
import utils.uuid.dao.SrUuidDbTable
import utils.uuid.services.SrUuidService
import api.accounts.service.AccountCreateService
import api.teamStats.services.AgencyDashboardCacheService

import scala.concurrent.Future
import scala.util.{Failure, Success, Try}

sealed trait UpdateProfileDuringOnboardingError

object UpdateProfileDuringOnboardingError {

  case object NameIsEmpty extends UpdateProfileDuringOnboardingError

  case class SQLErrorWhileUpdatingData(err: Throwable) extends UpdateProfileDuringOnboardingError

  case object IdNotFoundAfterUpdateImpossibleError extends UpdateProfileDuringOnboardingError

  case object OrgNameSentForNonOwnerError extends UpdateProfileDuringOnboardingError

  case object OrgNameNotSentForOwnerError extends UpdateProfileDuringOnboardingError

  case object InvalidPhoneNumberError extends UpdateProfileDuringOnboardingError
}

sealed trait ActivateOrDeactivateTeamError 

object ActivateOrDeactivateTeamError {
  case class AddonLimitReachedError(message: String) extends ActivateOrDeactivateTeamError
  
  case class ResetOrgDataCacheError(err: Throwable) extends ActivateOrDeactivateTeamError
  
  case class SQLException(err: Throwable) extends ActivateOrDeactivateTeamError
}

sealed trait AgencySwitchError

object AgencySwitchError {

    case object WrongPlanError extends AgencySwitchError

    case object NonOwnerError extends AgencySwitchError

    case object MoreThanOneTeamError extends AgencySwitchError

    case class SQLException(err: Throwable) extends AgencySwitchError

    case object DualDisabledError extends AgencySwitchError

    case object DualEnabledError extends AgencySwitchError
}

case class OrganizationAndAffiliateDetails(
  organizationWithCurrentData: OrganizationWithCurrentData,
  affiliateDetailsOpt: Option[AffiliateDetails],
  totalActiveOrgMemberCount: Int,
)

case class GrantAccessForm(
                            give_access: Boolean,
                            account_email: String,
                            org_name: String
                          )
object GrantAccessForm {
  given reads: Reads[GrantAccessForm] = Json.reads[GrantAccessForm]
}
class AccountService(
                      accountDAO: AccountDAO,
                      srResourceWithoutTenantDaoService: SrResourceWithoutTenantDaoService,
                      cacheServiceJedis: CacheServiceJedis,
                      accessTokenService: AccessTokenService,
                      userRedisKeyService: UserRedisKeyService,
                      teamDAO: TeamsDAO,
//                      teamService: TeamService,
                      organizationDAO: OrganizationDAO,
                      dbUtils: DBUtils,
                      teamsMetadataService: TeamsMetadataService,
                      generalSettingService: GeneralSettingService,
                      emailSendingStatusDAO: EmailSendingStatusDAO,
                      supportAccessToUserAccountDAO: SupportAccessToUserAccountDAO,
                      replySentimentDAOService: ReplySentimentDAOService,
                      srUuidUtils: SrUuidUtils,
                      opportunitiesDefaultSetupService: OpportunitiesDefaultSetupService,
                      srUuidService: SrUuidService,
                      organizationDAOService: OrganizationDAOService,
                      organizationJedisCacheDao: OrganizationJedisCacheDao,
                      srMailServerService: SrMailServerService,
                      accountCreateService: AccountCreateService,
                      accountDAOService: AccountDAOService,
                      agencyDashboardCacheService: AgencyDashboardCacheService
                    ) {


  def isMultiChannelCampaign(
    teamId: TeamId,
    campaignId: CampaignId,
  )(using Logger: SRLogger): Try[Boolean] = {

    accountDAO.isMultiChannelCampaign(
      teamId = teamId,
      campaignId = campaignId,
    ) match {

      case Failure(exception) =>

        Logger.shouldNeverHappen(
          msg = s"Failed to check isMultiChannelCampaign. teamId: $teamId :: campaignId: $campaignId",
          err = Some(exception),
        )

        Failure(exception)

      case Success(isMultiChanCamp) =>

        Success(isMultiChanCamp)

    }

  }

  def findGAuthKeyFor2FA(
                          id: AccountId
                        ): Option[String] = accountDAO.findGAuthKeyFor2FA(id = id)

  def update2FAEnabled(
                        isSMSFlow: Boolean,
                        accountId: AccountId,
                        countryCode: Option[String],
                        mobileCountryCode:  Option[String],
                        mobileNumber:  Option[Long],
                        gauthkey: Option[String]
                      ): Try[Option[AccountId]] = accountDAO.update2FAEnabled(
    isSMSFlow = isSMSFlow,
    accountId = accountId,
    countryCode = countryCode,
    mobileCountryCode = mobileCountryCode,
    mobileNumber = mobileNumber,
    gauthkey = gauthkey
  )

  def updateUserManagementSettings(
                                    orgId: OrgId,
                                    enforce_2fa: Boolean
                                  ): Try[Int] = accountDAO.updateUserManagementSettings(orgId = orgId, enforce_2fa = enforce_2fa)

  def updateTeamConfig(
                        accountId: AccountId,
                        teamId: TeamId,
                        data: UpdateTeamConfig
                      ): Try[Option[TeamId]] = accountDAO.updateTeamConfig(accountId = accountId, teamId = teamId, data = data)

  def updateAccountEmailNotificationSettings(
                                              accountId: AccountId,
                                              data: UpdateAccountEmailNotificationSettings
                                            ): Try[Option[AccountId]] =  accountDAO.updateAccountEmailNotificationSettings(accountId = accountId, data = data)

  def updateProfile(
                     accountId: AccountId,
                     data: UpdateAccountProfile
                   ): Try[Option[AccountId]] = accountDAO.updateProfile(accountId = accountId, data = data)

  def getInvitesByOrgId(
                         orgId: OrgId
                       ): Seq[InviteMember] = accountDAO.getInvitesByOrgId(orgId = orgId)

  def getTeamMatesByOrgId(
                           orgId: OrgId,
                           userListingApiWhereClauseAndOrderBy: SQLSyntax = sqls""
                         ): Seq[ResetTeamCache] = accountDAO.getTeamMatesByOrgId(orgId = orgId, userListingApiWhereClauseAndOrderBy = userListingApiWhereClauseAndOrderBy)

  def getInvitesByTeamId(
                          teamId: TeamId
                        ): Seq[InviteMember] = accountDAO.getInvitesByTeamId(teamId = teamId)

  def getInviteByCode(
                       inviteCode: String
                     ): Try[List[InviteMemberWithOrgAndTeamNames]] =  accountDAO.getInviteByCode(inviteCode = inviteCode)

  def getInviteById(
                     inviteId: Long
                   ): Option[InviteMember] = accountDAO.getInviteById(inviteId = inviteId)

  def deleteInviteByCode(
                          inviteCode: String,
                          org_id: OrgId
                        ): Try[Option[String]] = accountDAO.deleteInviteByCode(inviteCode = inviteCode, org_id = org_id)


  def findTeamById(
                    id: TeamId
                  ): Option[Team] = accountDAO.findTeamById(id = id)

  def updateTeamName(
                      teamId: TeamId,
                      newName: String
                    ): Try[Option[TeamId]] = accountDAO.updateTeamName(teamId = teamId, newName = newName)

  def activateOrDeactivateTeam(
                                teamId: TeamId,
                                orgId: OrgId,
                                active: Boolean,
                                planLimitService: PlanLimitService
                              )(using logger: SRLogger): Either[ActivateOrDeactivateTeamError, Int] = {

    val tryOfUpdateCount: Try[Int] = for {

      _: OrgId <- if (active) {
        planLimitService.checkLimitAccordingToAddonLicenseTypeUsingOrgId(
          orgId = orgId,
          addonLicenceType = AddonLicenceType.ClientTeams,
          isAddingNewTeam = false,
        )
      } else {
        Success(orgId)
      }

      updateCount: Int <- accountDAO.activateOrDeactivateTeam(teamId = teamId, orgId = orgId, active = active)

    } yield {

      updateCount

    }

    tryOfUpdateCount match {
      case Failure(AddonLimitReachedException(message, _)) =>
        Left(ActivateOrDeactivateTeamError.AddonLimitReachedError(message))

      case Failure(e) =>
        Left(ActivateOrDeactivateTeamError.SQLException(e))

      case Success(value) =>
        organizationDAOService.resetOrgDataFromCache(
          orgId = orgId,
          resetCacheInterval = SrResetCacheInterval.Immediately
        ) match {
          case Failure(e) =>
            Left(ActivateOrDeactivateTeamError.ResetOrgDataCacheError(e))

          case Success(bool) =>
              agencyDashboardCacheService.clearTeamSummaryOnDeactivation(teamId = teamId.id)
            Right(value)
        }
    }
  }
  def activateOrDeactivateAccount(
                                   accountId: AccountId,
                                   orgId: OrgId,
                                   active: Boolean
                                 ): Try[Int] = accountDAO.activateOrDeactivateAccount(accountId = accountId, orgId = orgId, active = active)

  def getAccountEmailNotificationSettings(accountId: AccountId): Try[Option[String]] = accountDAO.getAccountEmailNotificationSettings(accountId = accountId)

  def updateOrgMetadata(
                         orgId: OrgId,
                         metadata: OrgMetadataForm
                       ): Try[Int] = accountDAO.updateOrgMetadata(orgId = orgId, metadata = metadata)

  def enableAgencyViewDuringOnboarding(orgId: OrgId,
                                       enableAgency: Boolean,
                                       account: Account)(
    using logger: SRLogger
  ): Either[AgencySwitchError,Int] = {

      val isAgency: Boolean = account.org.is_agency
      val accountId: Long = account.internal_id
      val ownerAccountId: Long = account.org.owner_account_id
      val planId = account.org.plan.plan_id


    if(accountId != ownerAccountId){
          Left(AgencySwitchError.NonOwnerError)
      } else if (!account.org.settings.agency_option_allow_changing){
          Left(AgencySwitchError.WrongPlanError)
      } else {
          enableAgency match {
              case true =>
                  if (isAgency) {
                      Left(AgencySwitchError.DualEnabledError)
                  }
                  else {
                      val updateCount: Try[Int] = accountDAO.enableAgencyViewDuringOnboarding(orgId = orgId, enableAgency = enableAgency, planId = planId)

                      updateCount match {
                          case Success(value) =>

                              removeAccountFromRedis(accountId = accountId)

                              Right(value)

                          case Failure(exception) =>

                            Left(AgencySwitchError.SQLException(exception))
                      }
                  }

              case false =>

                // they have to be a agency now becoming a non-agency,
                // so the count of active teams should be exactly one.

                  if (!isAgency) {
                      Left(AgencySwitchError.DualDisabledError)
                  }
                  else if (account.teams.count(_.active) != 1) {
                      Left(AgencySwitchError.MoreThanOneTeamError)
                  } else {
                      val updateCount: Try[Int] = accountDAO.enableAgencyViewDuringOnboarding(orgId = orgId, enableAgency = enableAgency, planId = planId)

                      updateCount match {
                          case Success(value) =>

                            removeAccountFromRedis(accountId = accountId)

                            Right(value)

                          case Failure(exception) =>
                            Left(AgencySwitchError.SQLException(exception))
                      }
                  }

          }
      }
  }

  def updateAccountMetadata(
                             accountId: AccountId,
                             metadata: AccountMetadata
                           ): Try[Int] = accountDAO.updateAccountMetadata(accountId = accountId, metadata = metadata)

  def signout(accountId: Long, Logger: SRLogger): Option[Long] = {
    removeAccountFromRedis(accountId = accountId)(
      logger = Logger
    )
    userRedisKeyService.removeRedisKey(accountId = accountId)(Logger)
  }

  final def removeAccountFromRedis(
                                    accountId: Long
                                  )(
    implicit logger: SRLogger
  ): Try[true] = accountDAOService.removeAccountFromRedis(accountId = accountId)


  // Note: This method should be private
  def findAccountFromDbIgnoringCache(
    id: AccountId
  )(
    implicit logger: SRLogger
  ): Try[Option[Account]] = accountDAOService.findAccountFromDbIgnoringCache(
    id = id
  )


  def find(
    id: Long
  )(
    implicit logger: SRLogger
  ): Try[Account] = accountDAOService.find(id = id)

  def getOwnerAccountIdByOrgId(
    orgId: Long
  ): Try[Option[Long]] = {

    accountDAO
      .getOwnerAccountIdByOrgId(orgId = OrgId(id = orgId)).map(_.map(_.id)) // FIXME VALUECLASS

  }

  def findOwnerAccountByOrgId(
    orgId: Long
  )(
    implicit logger: SRLogger
  ): Try[Account] = {

    getOwnerAccountIdByOrgId(orgId = orgId)
      .flatMap {
        case None => Failure(new Exception("org not found"))

        case Some(ownerAccountId) =>
          find(id = ownerAccountId)

      }

  }

  def getRemainingCredits(orgId: OrgId, one_time_plan_type: String)(using logger: SRLogger): Try[SrFeatureCredits] = {

    OneTimePlanType.fromString(one_time_plan_type)
      .flatMap {

        case OneTimePlanType.SR_NATIVE_CALLING =>

          getOrgCreditsDetails(
            orgId = orgId
          ).flatMap {

            case None =>

              /**
                * 13-Aug-2024
                *
                * For the organizations, who have not purchased any numbers,
                * for those organizations `getOrgCreditsDetails` will return None.
                */

              logger.error(s"credit details not found for orgId: ${orgId.id}")

              Success(
                SrFeatureCredits(
                  purchased_credits = 0,
                  default_credits_from_base_plan = None
                )
              )

            case Some(credit_details) =>

              calculateRemainingCredits(
                creditsObject = credit_details
              ).map { purchasedCredits =>

                SrFeatureCredits(
                  purchased_credits = purchasedCredits.toLong,
                  default_credits_from_base_plan = None
                )

              }

          }

        case OneTimePlanType.SR_LEAD_FINDER =>

          organizationDAO.fetchRemainingLeadFinderCredits(
            orgId = orgId
          )

      }

  }

  def getOrgCreditsDetails(orgId: OrgId): Try[Option[CreditsObject]] = {
    accountDAO.getOrgCreditDetails(orgId = orgId)

  }

  def calculateRemainingCredits(creditsObject: CreditsObject): Try[BigInt] = Try {

    /*
    ACTIVE -> when the account is active, we can directly fetch the remaining credits that are getting updated from the cron in the database
    SUSPENDED -> cron is updating the remaining balance for the organizations which are active, hence for suspended sub-account we rely on the data coming from trigger, which tells us what is the current usage. So we are using twl_trigger_current_value to get most recent usage from org.
     */
    creditsObject.twl_sub_account_status match {
      case TwilioSubAccountStatus.ACTIVE => creditsObject.call_credits_remaining
      case TwilioSubAccountStatus.SUSPENDED => creditsObject.call_credits - creditsObject.twl_trigger_current_value.getOrElse(0)
    }
  }

  def findOwnerOrganizationAndAffiliateDetails(
    orgId: OrgId
  )(
    implicit logger: SRLogger
  ): Try[OrganizationAndAffiliateDetails] = {

    for {

      organization: Option[OrganizationWithCurrentData] <- organizationDAOService.getOrgWithCurrentData(orgId = orgId)

      affiliateDetailsOpt: Option[AffiliateDetails] <- organizationDAO.getAffiliateDetailsByOrgId(orgId = orgId)

      totalActiveOrgMemberCount: Int <- getTotalActiveOrgMemberCount(orgId = orgId)

    } yield {

      OrganizationAndAffiliateDetails(
        organizationWithCurrentData = organization.get,
        affiliateDetailsOpt = affiliateDetailsOpt,
        totalActiveOrgMemberCount = totalActiveOrgMemberCount,
      )

    }
  }

  def getTotalActiveOrgMemberCount(
    orgId: OrgId
  )(
    implicit logger: SRLogger
  ): Try[Int] = {

    findOwnerAccountByOrgId(orgId = orgId.id) match {

      case Failure(exception) =>

        logger.shouldNeverHappen(
          msg = s"Failed to find owner accountId by orgId. orgId: $orgId",
          err = Some(exception),
        )

        Failure(exception)

      case Success(ownerAccount) =>

        Success {

          ownerAccount.teams
            .flatMap(t => t.all_members)
            .filter(m => m.active)
            .map(m => m.user_id)
            .distinct
            .length

        }

    }

  }

  def getAllApiKeysToResetCache(accountId: Long): Seq[ResetApiCache] = {

    accountDAOService.getAllApiKeysToResetCache(
      accountId = accountId // FIXME VALUECLASS
    )

  }

  def findByApiKey(
    apiKey: String,
    keyType: SRApiKeyType
  )(
    implicit logger: SRLogger
  ): Try[Option[Account]] = {

    accountDAO.findByApiKey(
      apiKey = apiKey,
      keyType = keyType
    ) match {
      case Failure(exception) => Failure(exception)

      case Success(None) => Success(None)

      case Success(Some(accId)) =>

        find(id = accId.id)
          .map(a => Some(a))
    }

  }

  def updateProfileDuringOnboarding(
                                     account: Account,
                                     data: UpdateAccountProfileDuringOnboarding
                                   ): Either[UpdateProfileDuringOnboardingError, Long] = {

    if (data.first_name.trim.isEmpty || data.last_name.trim.isEmpty) {

      Left(UpdateProfileDuringOnboardingError.NameIsEmpty)

    }
        /*
        5-Dec-2024 : Removed the clause to mandate the phone number while onboarding
         */
        
//    else if (
//      data.onboarding_phone_number.isEmpty ||
//        data.onboarding_phone_number.get.trim.isEmpty
////        || !PhoneNumberValidator.isValidPhoneNumber(data.onboarding_phone_number.get.trim)
//    ) {
//
//      Left(UpdateProfileDuringOnboardingError.InvalidPhoneNumberError)
//
//    }
    else {
      val is_valid_phonenumber: Boolean = PhoneNumberValidator.isValidPhoneNumber(data.onboarding_phone_number.get.trim)

      account.org_role match {
        case Some(OrganizationRole.OWNER) =>
          if (data.org_name.isEmpty || data.org_name.get.trim.isEmpty) {
            Left(UpdateProfileDuringOnboardingError.OrgNameNotSentForOwnerError)
          } else {

            val isOrgOnboardingDone = account.org.org_metadata.is_onboarding_done.getOrElse(false)

            val updatedAccountId = for {
              /**
               * If Org Onboarding is not done :
               *    - Here Account onboarding flag is marked as done only if Org onboarding not done.
               * else
               *    - we are skipping this Account onboarding flag and updating in invited user onboarding last step.
               */
              _ : Int <- if (!isOrgOnboardingDone){
                      accountDAO.updateAccountMetadata(
                      accountId = AccountId(id = account.internal_id),
                      metadata = AccountMetadata(
                        is_profile_onboarding_done = Some(true)
                      )
                    )} else{
                      Try{0}
                    }

              id:Option[Long] <- accountDAO.updateProfileDuringOnboardingForOwner(
                accountId = AccountId(id = account.internal_id), // FIXME VALUECLASS
                first_name = data.first_name,
                last_name = data.last_name,
                newOrgName = data.org_name.get,
                onboarding_phone_number = data.onboarding_phone_number.get,
                is_phone_number_valid = is_valid_phonenumber,
                org_id = OrgId(id = account.org.id) // FIXME VALUECLASS
              ).map(_.map(_.id)) // FIXME VALUECLASS

            } yield {
              id
            }


            updatedAccountId match {
              case Failure(err) =>
                Left(UpdateProfileDuringOnboardingError.SQLErrorWhileUpdatingData(err))

              case Success(None) =>
                Left(UpdateProfileDuringOnboardingError.IdNotFoundAfterUpdateImpossibleError)


              case Success(Some(id)) =>

                Right(id)

            }
          }

        case Some(OrganizationRole.AGENCY_ADMIN) | None =>
          if (data.org_name.isDefined) {
            Left(UpdateProfileDuringOnboardingError.OrgNameSentForNonOwnerError)

          } else {
            val isOrgOnboardingDone = account.org.org_metadata.is_onboarding_done.getOrElse(false)
            val updatedAccountId = for {
              /**
               * If Org Onboarding is not done :
               *    - Here Account onboarding flag is marked as done only if Org onboarding not done.
               *      else
               *    - we are skipping this Account onboarding flag and updating in invited user onboarding last step.
               */
              _ : Int <- if (!isOrgOnboardingDone){
                accountDAO.updateAccountMetadata(
                accountId = AccountId(id = account.internal_id), // FIXME VALUECLASS
                metadata = AccountMetadata(
                  is_profile_onboarding_done = Some(true)
                )
              )} else{
                Try{0}
              }

              id: Option[Long] <- accountDAO.updateProfileDuringOnboardingForMember(
                accountId = AccountId(id = account.internal_id), // FIXME VALUECLASS
                first_name = data.first_name, 
                last_name = data.last_name,
                onboarding_phone_number = data.onboarding_phone_number.get,
                is_phone_number_valid = is_valid_phonenumber
              ).map(_.map(_.id)) // FIXME VALUECLASS

            } yield {
              id
            }


            updatedAccountId match {
              case Failure(err) =>
                Left(UpdateProfileDuringOnboardingError.SQLErrorWhileUpdatingData(err))

              case Success(None) =>
                Left(UpdateProfileDuringOnboardingError.IdNotFoundAfterUpdateImpossibleError)

              case Success(Some(id)) =>
                Right(id)


            }
          }
      }
    }
  }

  def getSessionAccountIdOptForMaster(
                                       sessionAccountIdVal: String,
                                       parseInt: String => Option[Int]
                                     )(Logger: SRLogger): Option[Int] = {
    accessTokenService.getSessionAccountIdOptForMaster(
      sessionAccountIdVal = sessionAccountIdVal,
      parseInt = parseInt)(Logger)
  }

  def checkIfUserHasRedisLoggedInKey(
                                      sessionAccountIdVal: String
                                    )(Logger: SRLogger): Option[Int] = {

    userRedisKeyService.checkIfUserIsLoggedIn(
      sessionAccountIdVal = sessionAccountIdVal.toLong
    )(Logger)

  }



  def create(
    logger: SRLogger,
    data: AccountCreateForm,
    orgKeyName: String,
    utmData: UtmData,
    signup_type: SignupType,
    sendEmailStatus: SendEmailStatusData,
    admitadCreatedAt: Option[DateTime],
    firstPromoterCreatedAt: Option[DateTime]
  )(
    using rolePermissionDataDAOV2: RolePermissionDataDAOV2
  ): Try[Option[Account]] = {

    given Logger: SRLogger = logger

    accountCreateService.createAndReturnAccountId(
      data = data,
      orgKeyName = orgKeyName,
      utmData = utmData,
      signup_type = signup_type,
      sendEmailStatus = sendEmailStatus,
      admitadCreatedAt = admitadCreatedAt,
      firstPromoterCreatedAt = firstPromoterCreatedAt
    ).flatMap { accountId =>


        find(
        id = accountId.id
      )(
        logger = logger
      ).map { account =>

        Some(account)

      }}

  }

  private def grantAccessToInboxToSupportUser(
                                               accountId: Long,
                                               account_email: String,
                                               org_name: String
                                             ): Try[Long] = {

    val dbAndSession = dbUtils.startLocalTx()
    val db = dbAndSession.db
    implicit val session: DBSession = dbAndSession.session

    val result = for {
      _: Option[AccountId] <- accountDAO.switchSupportAccess(
        accountId = AccountId(id = accountId), // FIXME VALUECLASS
        status = true
      )
      _: Option[Long] <- supportAccessToUserAccountDAO.addStatus(
        account_id = accountId,
        access_entity_type = AccessEntityType.TeamInbox,
        account_email = account_email,
        org_name = org_name
      )
    } yield {
      accountId
    }
    dbUtils.commitAndCloseSession(db = db)

    result

  }

  private def revokeAccessToInboxToSupportUser(
                                                accountId: Long
                                              ): Try[Long] = {
    val dbAndSession = dbUtils.startLocalTx()
    val db = dbAndSession.db
    implicit val session: DBSession = dbAndSession.session

    val result = for {
      _: Option[AccountId] <- accountDAO.switchSupportAccess(
        accountId = AccountId(id = accountId), // FIXME VALUECLASS
        status = false
      )
      _: Long <- supportAccessToUserAccountDAO.denyAccess(
        account_id = accountId,
        access_entity_type = AccessEntityType.TeamInbox
      )
    } yield {
      accountId
    }
    dbUtils.commitAndCloseSession(db = db)
    result
  }

  def switchAccessToInboxToSupportUser(
                                        accountId: Long,
                                        give_access: Boolean,
                                        account_email: String,
                                        org_name: String
                                      ): Try[Long] = Try {

    supportAccessToUserAccountDAO.getLatest(
      account_id = accountId,
      access_entity_type = AccessEntityType.TeamInbox
    ) flatMap {
      case None =>
        if (give_access) {
          grantAccessToInboxToSupportUser(
            accountId = accountId,
            account_email = account_email,
            org_name = org_name
          )
        } else {
          Failure(new Throwable("Wrong type of access given from frontend"))
        }


      case Some(supportAccessToUserAccount) =>


        (supportAccessToUserAccount.access_status.getAccessStatusType, give_access) match {
          case (AccessStatusType.Allowed, false)=>

              revokeAccessToInboxToSupportUser(
                accountId = accountId
              )
          case (AccessStatusType.Allowed, true) =>
            Failure(new Throwable("Wrong type of access given from frontend"))

          case (AccessStatusType.Revoked | AccessStatusType.Expired, true) =>
              grantAccessToInboxToSupportUser(
                accountId = accountId,
                account_email = account_email,
                org_name = org_name
              )
          case (AccessStatusType.Revoked | AccessStatusType.Expired, false) =>
            Failure(new Throwable("Wrong type of access given from frontend"))

        }
    }
  }.flatten


  def getHistoryForAccountId(
                            accountId: Long
                            ): Try[List[SupportAccessToUserAccount]] = {
    supportAccessToUserAccountDAO.getHistory(
      account_id = accountId
    )
  }

  def getCompanyDataByOrgId(orgId:Long)(
    implicit logger: SRLogger
  ) :Try[Company] = {

    findOwnerAccountByOrgId(orgId = orgId) match {
      case Failure(err)  => Failure(new Exception("An Error Occurred while fetching account data "))

      case Success(acc) =>

        val totalseats =  if(acc.org.plan.plan_type == PlanType.INACTIVE ) 0 else acc.org.counts.total_sending_email_accounts

        val mrr = PlanID.getApproximateMonthlySpend(
          planId = acc.org.plan.plan_id,
          isV2BusinessPlan = acc.org.plan.is_v2_business_plan,
          seats = totalseats
        )

        val persona = Persona(
          account_type = acc.account_type.toString,
          country = acc.profile.country_code.getOrElse("")
        )

        val subscription_data= Subscription_data(
          total_seats = totalseats,
          mrr = mrr
        )
        Success(
          Company(
          org_id = orgId,
          name = acc.org.name,
          persona = persona ,
          subscription_data = subscription_data
        )
        )
    }

  }

  def getAgencyTeamLevelStats(
                               orgId: Long,
                               aid: Long,
                               tids: Seq[Long],
                               from: Option[DateTime],
                               till: Option[DateTime]
                             )(
    implicit logger: SRLogger
  ): Try[Seq[ReportTeamStats]] = {
    for {
      positive_reply_sentiments: List[ReplySentimentUuid] <- Try{
        tids.flatMap{tid =>
          replySentimentDAOService.getUuidsForTeamIdAndReplySentimentType(
            team_id = tid,
            replySentimentType = ReplySentimentType.Positive
          ).get
        }.toList
      }

      agencyLevelStats: Seq[ReportTeamStats] <- accountDAO.getAgencyTeamLevelStats(
        orgId = OrgId(id = orgId), // FIXME VALUECLASS
        aid = AccountId(id = aid), // FIXME VALUECLASS
        tids = tids.map(t => TeamId(id = t)), // FIXME VALUECLASS
        from = from,
        till = till,
        positive_reply_sentiments = positive_reply_sentiments
      )

    } yield {
      agencyLevelStats
    }
  }
  
  def checkIfOrgHasMCCampaign( orgId: Long): Try[Boolean] = {
    
    accountDAO.findIfAnyCampaignInOrgHasMCCampaign(orgId = orgId)
  }

  def checkIfOrgHasDripCampaign(orgId: OrgId): Try[Boolean] = {

    accountDAO.findIfAnyCampaignInOrgHasDripCampaign(orgId = orgId)
  }

  def checkEmailAddressIfAlreadyRegisteredInCalendar(email:String, orgId: OrgId)(using logger: SRLogger): Try[Boolean] = {

    accountDAO.checkEmailAddressIfAlreadyRegisteredInCalendar(email = email ,orgId = orgId )

  }

  def updateCalendarUserData(
                              orgId: OrgId,
                              accountId: AccountId,
                              calendarUserId: CalendarUserId,
                              calendarUsernameSlug: String
                            ):Try[Int] = {

    accountDAO.updateCalendarUserData(orgId = orgId, accountId = accountId, calendarUserId = calendarUserId,calendarUsernameSlug = calendarUsernameSlug)

  }





  def getTotalCallingNumbersOfOrg(orgId: OrgId): Try[Int] = {
    accountDAO.getTotalCallingAccountsOfOrg(orgId = orgId)

  }


  def getAccountHashedPasswd(
    accountId: AccountId
  ): Try[Option[String]] = {

    accountDAO.getAccountHashedPasswd(accountId = accountId)

  }

  def updatePasswordWhenUserIsLoggedIn(accountId: AccountId, password: String): Try[Option[AccountId]] = {
    accountDAO.updatePasswordWhenUserIsLoggedIn(accountId= accountId, password=password)
  }





  def getEmailsOfTeamMembers(teamId: TeamId): Try[List[String]] = {
    accountDAO.getEmailsOfTeamMembers(teamId = teamId)
  }

  def getEmailOfOrgOwner(org_id: OrgId): Try[String] = {
    accountDAO.getEmailOfOrgOwner(org_id = org_id)
  }

  def findNewOwnerTeamMember(teamId: TeamId, accountId: AccountId): Try[Option[TeamMemberBasic]] = {
    accountDAO.findNewOwnerTeamMember(teamId = teamId, accountId = accountId)
  }

  def getTeamMatesByTeamId(teamId: TeamId): Seq[ResetTeamCache] = {
    accountDAO.getTeamMatesByTeamId(teamId = teamId)
  }

  def getEmailSummaryAccountIdsforReport(email_notification_summary: String): Try[Seq[AccountId]] = {
    accountDAO.getEmailSummaryAccountIdsforReport(
      email_notification_summary = email_notification_summary
    )
  }

  def getAllTeamAdminReportCampaignStats(
    team_member_aids: Seq[AccountId],
    orgId: OrgId
  ): Try[Seq[ReportTeamMemberStats]] = {
    accountDAO.getAllTeamAdminReportCampaignStats(
      team_member_aids = team_member_aids,
      orgId = orgId
    )
  }

  def getTopCampaignStatsForReportV2(
    subordinates: Seq[SubordinatesByTeamIdForReportGeneration]
  ): Try[Seq[ReportCampaignStats]] = {
    accountDAO.getTopCampaignStatsForReportV2(
      subordinates = subordinates
    )
  }


  def getTopListStatsForReportV2(
    subordinates: Seq[SubordinatesByTeamIdForReportGeneration]
  ): Try[Seq[ReportListStats]] = {
    accountDAO.getTopListStatsForReportV2(
      subordinates = subordinates
    )
  }

  def getTopTemplateStatsForReport(
    subordinates: Seq[SubordinatesByTeamIdForReportGeneration]
  ): Try[Seq[ReportTemplateStats]] = {
    accountDAO.getTopTemplateStatsForReport(
      subordinates = subordinates
    )
  }

  def updateLastEmailSummarySentAt(aid: AccountId): Try[Option[AccountId]] = {
    accountDAO.updateLastEmailSummarySentAt(
      aid = aid
    )
  }

  def deactivateTrialExpiredOrgs(): Try[Seq[Organization]] = {
    accountDAO.deactivateTrialExpiredOrgs()
  }

  def updateOrgWithEnrichedCompanyData(
    orgId: OrgId,
    enrichedCompanyData: EnrichedCompanyData
  ): Try[Option[OrgId]] = {
    accountDAO.updateOrgWithEnrichedCompanyData(
      orgId = orgId,
      enrichedCompanyData = enrichedCompanyData
    )
  }

  // 2-feb-2024: we were facing issues during resent otp flow,
  // mostly a cache issue
  // this fn is to be used only in signup/ reset password etc flows
  def findByEmailIgnoreCache(email: String)(
    implicit logger: SRLogger
  ): Try[Option[Account]] = {

    accountDAO.findByEmail(email = email) match {

      case Failure(exception) => Failure(exception)

      case Success(None) => Success(None)

      case Success(Some(accId)) =>

        findAccountFromDbIgnoringCache(id = accId)

    }
  }

  def updatePasswordAndResetVerificationCode(accountId: AccountId, password: String, code: String)(
    implicit logger: SRLogger
  ): Try[Option[AccountId]] = {

    for {
      res: Option[AccountId] <- accountDAO.updatePasswordAndResetVerificationCode(
        accountId = accountId,
        password = password,
        code = code
      )

      _: true <- removeAccountFromRedis(
        accountId = accountId.id
      )
    } yield {
      res
    }


  }

  def updateAccountEmailStatusIsVerified(accountId: AccountId, code: String)(
    implicit logger: SRLogger
  ): Try[Option[AccountId]] = {


    for {
      res: Option[AccountId] <- accountDAO.updateAccountEmailStatusIsVerified(
        accountId = accountId,
        code = code
      )

      _: true <- removeAccountFromRedis(
        accountId = accountId.id
      )
    } yield {
      res
    }


  }

  def getInviteByEmail(email: String): Option[InviteMemberBasic] = {
    accountDAO.getInviteByEmail(
      email = email
    )
  }

  def checkOrgTrackingSubdomainKey(checkKey: String): Int = {
    accountDAO.checkOrgTrackingSubdomainKey(
      checkKey = checkKey
    )
  }

  def getSignupTypeForAccount(email: String): Try[Option[SignupType]] = {
    accountDAO.getSignupTypeForAccount(email = email)
  }


  def getTeamSummary(
    orgId: OrgId,
    tid: TeamId,
    aid: AccountId,
    from: Option[DateTime],
    till: Option[DateTime],
    positive_reply_sentiments: List[ReplySentimentUuid]
  )(using Logger: SRLogger): Try[ReportTeamStats] = {
    accountDAO.getTeamSummary(
      orgId = orgId,
      tid = tid,
      aid = aid,
      from = from,
      till = till,
      positive_reply_sentiments = positive_reply_sentiments
    )
  }

  def getTeamNamesAndIdsBySendDate(
    orgId: OrgId,
    tids: Seq[TeamId]
  ): Try[List[TeamInfo]] = {
    accountDAO.getTeamNamesAndIdsBySendDate(
      orgId = orgId,
      tids = tids
    )
  }

  def getUserRoleIdForRoleInOrg(org_id: OrgId): Try[List[RoleAndUserRoleId]] = {
    accountDAO.getUserRoleIdForRoleInOrg(org_id = org_id)
  }

  def addEmailVerificationCode(accountId: AccountId, emailVerificationCode: String): Try[Option[AccountId]] = {

    val Logger = new SRLogger(s"addEmailVerificationCode: aid_$accountId")

    for {
      res: Option[AccountId] <- accountDAO.addEmailVerificationCode(
        accountId = accountId,
        emailVerificationCode = emailVerificationCode
      )

      _: true <- removeAccountFromRedis(accountId = accountId.id)(
        logger = Logger
      )

    } yield {
      res
    }

  }

  def getAccountUuidFromId(accountId: AccountId, orgId: OrgId)(using Logger: SRLogger): Try[AccountUuid] = {
    srUuidService.getAccountUuidFromId(accountId, orgId)
  }

}

object AccountService {
  private val passwordRegEx = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[\\S]{8,50}$"

  def checkPassword(password: String): Boolean = {

    password.matches(passwordRegEx)
  }
}