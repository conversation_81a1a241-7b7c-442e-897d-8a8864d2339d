package api.accounts

import api.emails.{EmailSetting, EmailSettingCreate<PERSON>iaOAuth, EmailSettingDAO}
import api._
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.{AccountId, OrgId}
import api.accounts.service.ResetUserCacheUtil
import api.emails.services.{CreateEmailViaOauthError, EmailAccountService}
import api.free_email_domain.service.FreeEmailDomainListService
import api.integrations._
import api.integrations.services.TIntegrationCRMService
import api.triggers.{CheckLimitAndAddOrUpdateCRMError, IntegrationType, IntegrationTypeService, WorkflowCrmSettingsService}
import api.triggers.dao.WorkflowCrmSettingsDAO
import io.sr.billing_common.models.AddonLicenceType
import org.joda.time.DateTime
import play.api.libs.json.Json
import play.api.libs.ws.WSClient
import play.api.mvc.{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roller, ControllerComponents}
import utils.{Help<PERSON>, ParseUtils, PlanLimitService, SRAppConfig, SRLogger}
import utils.email.GmailApiReplyTrackingService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

case class OAuthState(
                       campaignId: Option[Long],
                       emailType: Option[String],
                       aid: Long,
                       tid: Long,
                       serviceProvider: String,
                       googleApiKeyId: Option[Int],
                       campaign_basic_setup: Option[Boolean],
                       is_sandbox: Boolean,
                       is_inbox: Boolean,
                       goto_quickstart: Boolean
                     )


class SocialAuthController(
                            protected val controllerComponents: ControllerComponents,
                            salesforceOAuth: SalesforceOAuth,
                            zohoOAuth: ZohoOAuth,
                            zohoRecruitOAuth: ZohoRecruitOAuth,
                            pipedriveOAuth: PipedriveOAuth,
                            hubspotOAuth: HubSpotOAuth,
                            googleOAuth: GoogleOAuth,
                            microsoftOAuth: MicrosoftOAuth,
                            emailSettingDAO: EmailSettingDAO,
                            workflowCrmSettingsService: WorkflowCrmSettingsService,
                            workflowCrmSettingsDAO: WorkflowCrmSettingsDAO,
                            accountService: AccountService,
                            //                            authUtils: AuthUtils,
                            emailAccountService: EmailAccountService,
                            resetUserCacheUtil: ResetUserCacheUtil,
                            permissionUtils: PermissionUtils,
                            freeEmailDomainListService: FreeEmailDomainListService,
                            tIntegrationCRMService: TIntegrationCRMService,
                            socialAuthService: SocialAuthService,
                            implicit val ws: WSClient
                          ) extends BaseController {

  implicit val ec: ExecutionContext = controllerComponents.executionContext


  //  val EMAIL_AUTH_TYPES = List("both", "sender", "receiver")

  def getOAuthUrl(
                   v: String,
                   aid: Option[Long],
                   tid: Long,
                   service_provider: String,
                   campaign_id: Option[Long],
                   email_type: Option[String],
                   email_setting_id: Option[Long],
                   email_address: Option[String],
                   confirm_install: Option[Boolean] = Some(false),
                   campaign_basic_setup: Option[Boolean] = Some(false),
                   is_sandbox: Option[Boolean] = Some(false),
                   is_inbox: Option[Boolean] = Some(false),
                   goto_quickstart: Option[Boolean] =Some(false)
                 ) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CHANNELS,
    tidOpt = Some(tid)
  ) { (request: PermissionRequest[AnyContent]) =>

    given Logger: SRLogger= request.Logger
    val Res = request.Response

    if (request.actingTeamAccount.isEmpty) {
      Res.BadRequestError("Please select team first")
    }
    /*TODO: this permission check should happen in checkPermission permission itself
    * need to add new permission for CRM integrations
    * */
    else if(
      (service_provider == "hubspot"
        || service_provider == "pipedrive"
        || service_provider == "zoho"
        || service_provider == "zoho_recruit"
        || service_provider == "salesforce") &&
        request.actingTeamAccount.get.team_role != TeamAccountRole.ADMIN &&
        request.actingTeamAccount.get.team_role != TeamAccountRole.OWNER &&
        request.actingTeamAccount.get.team_role != TeamAccountRole.AGENCY_ADMIN
    ) {

      Res.ForbiddenError("Please ask the team admin to connect this tool. Once the team admin connects it, you may then create workflow automations. Contact support if there are any questions.")

    } else {

      val ta = request.actingTeamAccount.get

      val accountId = if(aid.isDefined) aid.get else ta.user_id
      val teamId = ta.team_id

      val emailSettingOpt = email_setting_id.flatMap(eid => emailSettingDAO.find(id = eid, accountIds = request.permittedAccountIds, teamId = teamId))
      val allowCRMIntegration = request.loggedinAccount.org.settings.allow_native_crm_integration

      val emailAddress = if (emailSettingOpt.isDefined) emailSettingOpt.map(_.email) else email_address
      val org = request.loggedinAccount.org

      Logger.info(s"ATTEMPT getOAuthUrl $emailAddress: loggedinaccount: lid_${request.loggedinAccount.internal_id} :: actingaccount: (aid_${ta.user_id} : tid_${ta.team_id} : acting team email: ${ta.email}")

      socialAuthService.getOAuthUrl(
        email_type = email_type,
        email_setting_id = email_setting_id,
        emailSettingOpt = emailSettingOpt,
        service_provider = service_provider,
        emailAddress = emailAddress,
        campaign_id = campaign_id,
        teamMember = ta,
        email_address = emailAddress,
        org = org,
        teamId = TeamId(teamId),
        accountId = AccountId(accountId),
        allowCRMIntegration = allowCRMIntegration,
        isZapmailFlow = false,
        confirm_install = confirm_install,
        campaign_basic_setup = campaign_basic_setup,
        is_sandbox = is_sandbox,
        is_inbox = is_inbox,
        goto_quickstart = goto_quickstart
      ) match {
        case Left(getOAuthUrlError: GetOAuthUrlError) =>
          getOAuthUrlError match {
            case GetOAuthUrlError.ServerError(err) =>

              Res.ServerError("There was an error, please try again or contact support", e = Some(err))

            case GetOAuthUrlError.EmailSettingNotFound =>

              Res.NotFoundError("email setting not found, please contact support")

            case GetOAuthUrlError.FreeDomainUseError =>

              Res.BadRequestError(
                message = "Please provide a work email . Personal email addresses are not allowed",
                error_code = Some(ErrorCode.FREE_EMAIL_DOMAIN_NOT_SUPPORTED),
                logAsFatal = false
              )

            case GetOAuthUrlError.EmailEitherSendOrReceiveError =>

              Res.BadRequestError("email_type must be either 'send' or 'receive'")

            case GetOAuthUrlError.MultipleCrmIntegrationError =>

              Res.BadRequestError("Can't integrate more than one CRM")

            case GetOAuthUrlError.GSuiteDomainNotInstalled =>

              Res.BadRequestError(
                message = "Your G Suite domain is not installed. Please follow the instructions to install",
                logAsFatal = false,
                error_code = Some(ErrorCode.GSUITE_DOMAIN_NOT_INSTALLED)
              )

            case GetOAuthUrlError.InvalidServiceProvider =>
              Res.BadRequestError("Invalid Service Provider")

            case GetOAuthUrlError.UpgradeSubscription =>
              Res.BadRequestError(
                "Please upgrade your subscription to use this integration.",
                error_code = Some(ErrorCode.UPGRADE_PLAN_INTEGRATION)
              )
          }
        case Right(GetOAuthUrlResponse(message, redirect_to)) =>
          Res.Success(
            message = message,
            data = Json.obj("redirect_to" -> redirect_to)
          )
      }
    }
  }


  def oauthFetchTokens(v: String, aid: Option[Long], tid: Long) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CHANNELS,
    tidOpt = Some(tid)
  ).async { (request: PermissionRequest[AnyContent]) =>

    given Logger: SRLogger= request.Logger
    val Res = request.Response

    if (request.actingTeamAccount.isEmpty) {
      Future.successful(Res.BadRequestError("Please select team first"))
    } else {

      val ta = request.actingTeamAccount.get

      val accountId = if (aid.isDefined) aid.get else ta.user_id
      // check if authorization code is there in url
      val codeOpt = request.getQueryString("code")

      // campaignId should come back is state
      val state = request.getQueryString("state")

      if (state.isEmpty) {

        Logger.fatal(s"OAuthFetchTokens error: state param missing, $ta")

        Future.successful(Res.BadRequestError("We need the state param from service provider. That is missing. Sorry about this. Please contact us"))
      } else {

        val s = socialAuthService._deconstructGAuthState(
          state = state.get,
          isZapmailFlow = false
        )

        val serviceProvider = s.serviceProvider
        val googleApiKeyId = s.googleApiKeyId
        val emailType = s.emailType
        val campaignId = s.campaignId
        val is_sandbox = s.is_sandbox


        if (s.aid != accountId) {

          Future.successful(Res.BadRequestError("Pass valid aid"))

        } else {

          if (ta.team_id != s.tid) {

            Future.successful(Res.ForbiddenError("You do not have access to this team"))

          } else {

            val location = request.getQueryString("location") //location parameter will exists only in zoho case

            socialAuthService.oauthFetchTokens(
              codeOpt = codeOpt,
              serviceProvider = serviceProvider,
              accountId = AccountId(accountId),
              teamId = TeamId(s.tid),
              state = state,
              location = location,
              teamMember = ta,
              is_sandbox = is_sandbox,
              googleApiKeyId = googleApiKeyId,
              permittedAccountIds = request.permittedAccountIds,
              orgId = OrgId(request.loggedinAccount.org.id),
              campaignId = campaignId,
              emailType = emailType,
              isZapmailOauthFlow = false
            ).map {
              case Left(oAuthFetchTokensError: OAuthFetchTokensError) =>
                oAuthFetchTokensError match {
                  case OAuthFetchTokensError.BadRequestError(err) =>

                    Logger.error(
                      msg = s"OAuthFetchTokensError.BadRequestError - $err - teamId: ${s.tid} :: serviceProvider: $serviceProvider",
                    )

                    Res.BadRequestError(message = err)

                  case OAuthFetchTokensError.ServerError(message,error) =>

                    val errMsg = s"OAuthFetchTokensError.ServerError - $message - teamId: ${s.tid} :: serviceProvider: $serviceProvider"

                    error match {

                      case None =>

                        Logger.error(msg = errMsg)

                      case Some(err) =>

                        Logger.error(msg = errMsg, err = err)

                    }

                    Res.ServerError(message=message,e = error)
                }

              case Right(oAuthFetchTokensResponse: OAuthFetchTokensResponse) =>
                Res.Success(message = oAuthFetchTokensResponse.message,data = oAuthFetchTokensResponse.data)
            }.recover { case e =>

              Logger.fatal(s"${serviceProvider}AuthFetchTokens error: emailSettingsForSaving, $ta", err = e)

              Res.BadRequestError(e.getMessage)
            }
          }

        }

      }
    }
  }

  // TODO: Move logic to service and do validations on existing zapmail mailboxs
  def addZapmailEmailsViaOauth(v: String) = Action.async { request =>
    implicit val Logger: SRLogger = SRLogger("addZapmailEmailsViaOauth")

    // Create a simple response helper
    val Res = new SRAPIResponse(Logger = Logger)

    // Extract code and state from query parameters
    val codeOpt = request.getQueryString("code")
    val stateOpt = request.getQueryString("state")

    if (stateOpt.isEmpty) {
      Logger.error(s"addZapmailEmailsViaOauth error: state param missing")
      Future.successful(Res.BadRequestError("State parameter is missing"))
    } else {
      val state = stateOpt.get
      val s = socialAuthService._deconstructGAuthState(
        state = state,
        isZapmailFlow = true
      )

      // Extract parameters from state
      val aid = s.aid
      val tid = s.tid
      val serviceProvider = s.serviceProvider
      val googleApiKeyId = s.googleApiKeyId
      val emailType = s.emailType
      val campaignId = s.campaignId
      val is_sandbox = s.is_sandbox
      val location = request.getQueryString("location")

      // Use for-comprehension to chain the operations
      val result = for {
        // Find account
        account: Account <- Future.fromTry(accountService.find(id = aid))

        // Get team member
        teamMember: TeamMember = Helpers.getTeamMemberFromAccount(
          account = account,
          team_id = TeamId(tid)
        )

        // Get org ID
        orgId: OrgId = OrgId(account.org.id)

        // Call OAuth service
        oauthResult: Either[OAuthFetchTokensError, OAuthFetchTokensResponse] <- socialAuthService.oauthFetchTokens(
          codeOpt = codeOpt,
          serviceProvider = serviceProvider,
          accountId = AccountId(aid),
          teamId = TeamId(tid),
          state = Some(state),
          location = location,
          teamMember = teamMember,
          is_sandbox = is_sandbox,
          googleApiKeyId = googleApiKeyId,
          permittedAccountIds = Seq(aid), // TODO : check if this is correct
          orgId = orgId,
          campaignId = campaignId,
          emailType = emailType,
          isZapmailOauthFlow = true
        )
      } yield oauthResult

      // Handle the result
      result.map {
        case Left(oAuthFetchTokensError: OAuthFetchTokensError) =>
          oAuthFetchTokensError match {
            case OAuthFetchTokensError.BadRequestError(err) =>
              Logger.error(s"OAuthFetchTokensError.BadRequestError - $err - teamId: $tid :: serviceProvider: $serviceProvider")
              Res.BadRequestError(message = err)

            case OAuthFetchTokensError.ServerError(message, error) =>
              val errMsg = s"OAuthFetchTokensError.ServerError - $message - teamId: $tid :: serviceProvider: $serviceProvider"
              error match {
                case None => Logger.error(msg = errMsg)
                case Some(err) => Logger.error(msg = errMsg, err = err)
              }
              Res.ServerError(message = message, e = error)
          }

        case Right(oAuthFetchTokensResponse: OAuthFetchTokensResponse) =>
          Res.Success(message = oAuthFetchTokensResponse.message, data = oAuthFetchTokensResponse.data)
      }.recover {
        case e: NoSuchElementException =>
          Logger.error(s"Entity not found: ${e.getMessage}", err = e)
          Res.NotFoundError(e.getMessage)

        case e: Exception =>
          Logger.error(s"${serviceProvider}AuthFetchTokens error", err = e)
          Res.ServerError("Unexpected error during OAuth flow", e = Some(e))
      }
    }
  }
}
