package api.accounts


import api_layer_models.CurrencyType
import api.accounts.models.{AccountId, AccountProfileInfo, OrgId, OrgUuid, SignupType}
import io.sr.billing_common.models.{PlanID, PlanType}
import api.accounts.sr_api_key_type.models.SRApiKeyType
import api.{AppConfig, accounts}
import api.accounts.service.UserFromAccountApiResponse
import api.call.models.TwilioSubAccountStatus
import api.calendar_app.CalendarUserId
import api.calendar_app.models.CalendarAccountData
import api.campaigns.models.CampaignStepType.AutoEmailStep
import api.campaigns.models.CampaignType
import api.campaigns.services.CampaignId
import api.prospects.models.{ProspectCategory, ProspectCategoryDisplyNameColor, ProspectCategoryNew, ProspectId}
import api.prospects.{Inbox, InferredQueryTimeline}
import api.reports.ReplySentimentStats
import api.teamStats.models.TeamInfo
import api.team_inbox.model.{FolderType, ReplySentimentType}
import api.team_inbox.service.{ReplySentimentForTeam, ReplySentimentUuid}
import eventframework.PaginationSortedData
import io.smartreach.companies_api.api.enrich_company_info.dao.EnrichedCompanyData
import io.smartreach.companies_api.api.enrich_company_info.models.EnrichedCompanyInfo
import io.sr.billing_common.services.PlanLimitService
import org.joda.time.DateTime
import org.postgresql.util.PGobject
import play.api.libs.json.*
import play.api.libs.json.JodaWrites.*
import play.api.libs.json.JodaReads.*
import scalikejdbc.*
import scalikejdbc.jodatime.JodaWrappedResultSet.*
import sr_scheduler.CampaignStatus
import utils.affiliate.{FirstPromoterReferrerAccountDetails, ReferralCouponStatus}
import utils.cronjobs.SubordinatesByTeamIdForReportGeneration
import utils.{Helpers, SRLogger}
import utils.dbutils.SQLUtils
import utils.enum_sr_utils.EnumUtils
import utils.security.{EncryptionHelpers, EncryptionService}
import utils.uuid.SrUuidUtils

import scala.util.{Failure, Success, Try}

case class ReportTeamStats(
                            rank: Int = 0, // set in EmailNotificationCronService


                            active: Boolean,
                            error: Option[String],
                            org_id: OrgId,
                            team_id: TeamId,
                            running_campaigns: Int, // all runnning campaigns
                            total_members: Int,


                            team: String,
                            inbox_unread_count: String,
                            total_sent: Int,
                            total_opened: Int,
                            total_bounced: Int,
                            total_clicked: Int,
                            total_opted_out: Int,
                            total_replied: Int,

                            percentage_total_opened: Int,
                            percentage_total_bounced: Int,
                            percentage_total_clicked: Int,
                            percentage_total_opted_out: Int,
                            percentage_total_replied: Int,
                            reply_sentiment_stats: ReplySentimentStats
                          )

case class AccountIdAndOrgRole(
                           accountId: AccountId,
                           orgRole: OrganizationRole
                           )

object ReportTeamStats {
  given format: OFormat[ReportTeamStats] = Json.format[ReportTeamStats]

  def fromDb(rs: WrappedResultSet): ReportTeamStats = {


    val sent = rs.int("total_sent")
    val bounced = rs.int("total_bounced")
    val opened = rs.int("total_opened")
    val clicked = rs.int("total_clicked")
    val opted_out = rs.int("total_opted_out")
    val replied = rs.int("total_replied")
    val positive = rs.int("total_positive_reply")

    val inboxUnreadCountInt = rs.int("inbox_unread_count")

    val inboxUnreadCount = if (inboxUnreadCountInt >= 50) "50+" else inboxUnreadCountInt.toString

    ReportTeamStats(
      team = rs.string("team"),

      team_id = TeamId(id = rs.long("team_id")),
      org_id = OrgId(id = rs.long("org_id")),
      active = rs.boolean("active"),
      inbox_unread_count = inboxUnreadCount,

      error = rs.stringOpt("error")
        .map(err => s"We are facing problems running campaigns in this team: $err"),

      running_campaigns = rs.int("running_campaigns"),
      total_members = rs.int("total_members"),

      total_sent = sent,
      total_bounced = bounced,
      total_opened = opened,
      total_clicked = clicked,
      total_opted_out = opted_out,
      total_replied =replied,

      percentage_total_opened = Helpers.getPercent(opened, sent),
      percentage_total_bounced = Helpers.getPercent(bounced, sent),
      percentage_total_clicked = Helpers.getPercent(clicked, sent),
      percentage_total_opted_out = Helpers.getPercent(opted_out, sent),
      percentage_total_replied = Helpers.getPercent(replied, sent),
      reply_sentiment_stats = ReplySentimentStats(
        positive = positive
      )
    )
  }
}


case class ResetApiCache(
  user_id: Long,
  user_api_key: Option[String],
  sr_pd_api_key: Option[String],
  ta_api_keys: Option[Seq[String]]
)

case class ResetTeamCache(
  teams: Seq[TeamsBelongingToAccount],
  user_id: AccountId,
  user_uuid: AccountUuid,
  email: String,
  first_name: String,
  last_name: String,
  active: Boolean,
  org_uuid: OrgUuid,
  org_role: Option[OrganizationRole],
  created_at: DateTime,
  timezone: Option[String],
  twofa_enabled: Boolean,
  scheduled_for_deletion_at: Option[DateTime]
) extends PaginationSortedData {
  override def getSortBy: DateTime = created_at

  override def getExactlyAtIdOrUuid = user_uuid.toString
}

case class AccountIdAndTeamId(accountId: AccountId, teamId: TeamId)

case class AccountBillingData(
                               orgId: OrgId,
                               currentBillingCycleStartedAt: DateTime
                             )

object ResetTeamCache {

  // ignore api_key while returning account as JSON
  given writes: Writes[ResetTeamCache] = new Writes[ResetTeamCache] {
    def writes(account: ResetTeamCache): JsObject = {

      Json.obj(
        "user_id" -> account.user_id,
        "uuid" -> account.user_uuid,
        "org_uuid" -> account.org_uuid,
        "org_role" -> account.org_role,
        "created_at" -> account.created_at,
        "timezone" -> account.timezone,
        "twofa_enabled " -> account.twofa_enabled,
        "teams" -> account.teams,
        "email" -> account.email,
        "first_name" -> account.first_name,
        "last_name" -> account.last_name,
        "active" -> account.active,
        "scheduled_for_deletion_at" -> account.scheduled_for_deletion_at
      )
    }
  }


  def fromDb(rs: WrappedResultSet): ResetTeamCache = {
    val teams = Json.parse(rs.any("teams").asInstanceOf[PGobject].getValue).validate[Seq[TeamsBelongingToAccount]].get
      .map{ a =>
        a.copy(
          api_key = a.api_key.map(EncryptionHelpers.decryptTeamsAccountCredential)
        )

    }


    ResetTeamCache(
      teams = teams,
      user_id = AccountId(rs.long("user_id")),
      email = rs.string("email"),
      first_name = rs.string("first_name"),
      last_name = rs.string("last_name"),
      active = rs.boolean("active"),
      scheduled_for_deletion_at = rs.jodaDateTimeOpt("scheduled_for_deletion_at"),
      user_uuid = AccountUuid(rs.string("uuid")),
      org_uuid = OrgUuid(rs.string("org_uuid")),
      org_role = OrganizationRole.withName(rs.string("org_role")),
      created_at = rs.jodaDateTime("created_at"),
      timezone = rs.stringOpt("timezone"),
      twofa_enabled = rs.boolean("twofa_enabled")
    )
  }

}

///

case class ResetBillingMonthlyCycleForOrg(
  orgId: OrgId,
  currentCycleStartedAt: DateTime
)

///

object AccountSummaryEmailNotification extends Enumeration {
  type AccountSummaryEmailNotification = Value
//  val DAILY = Value("daily")
  val WEEKLY: accounts.AccountSummaryEmailNotification.Value = Value("weekly")
  val NEVER: accounts.AccountSummaryEmailNotification.Value = Value("never")

  given format: Format[accounts.AccountSummaryEmailNotification.Value] = EnumUtils.enumFormat(AccountSummaryEmailNotification)

}

///




///


/*
object AccountErrorCodeType extends Enumeration {
  type AccountErrorCodeType = Value
  val PROSPECT_LIMIT_EXCEEDED = Value("prospect_limit_exceeded")

  given format = EnumUtils.enumFormat(AccountErrorCodeType)

}
*/






///



case class UpdateTeamConfig(
  max_emails_per_prospect_per_day: Int,
  max_emails_per_prospect_per_week: Int,
  max_emails_per_prospect_account_per_day: Option[Int] = None,
  max_emails_per_prospect_account_per_week: Option[Int] = None,
  // allow_assigning_prospects_to_multiple_campaigns: Boolean, FORCEASSIGNISSUE
  reply_handling: ReplyHandling.Value

)

object UpdateTeamConfig {
  given reads: Reads[UpdateTeamConfig] = Json.reads[UpdateTeamConfig]
}


///


case class UpdateAccountEmailNotificationSettings(
  email_notification_summary: AccountSummaryEmailNotification.Value,
  weekly_report_emails: Option[String]
)

object UpdateAccountEmailNotificationSettings {
  implicit val reads: Reads[UpdateAccountEmailNotificationSettings] = Json.reads[UpdateAccountEmailNotificationSettings]
}

///

case class UpdateAccountProfileDuringOnboarding(
                                 first_name: String,
                                 last_name: String,
                                 org_name: Option[String],
                                 onboarding_phone_number: Option[String]
                               )

object UpdateAccountProfileDuringOnboarding {
  implicit val reads: Reads[UpdateAccountProfileDuringOnboarding] = Json.reads[UpdateAccountProfileDuringOnboarding]
}


case class UpdateAccountProfile(
  first_name: String,
  last_name: String,
  timezone: Option[String]
)

object UpdateAccountProfile {
  implicit val reads: Reads[UpdateAccountProfile] = Json.reads[UpdateAccountProfile]
}

case class ApiCheckForm(
  api_key: String,
  key_type: Option[String] = None
)

object ApiCheckForm {
  implicit val reads: Reads[ApiCheckForm] = Json.reads[ApiCheckForm]
}


case class SignupViaPasswordForm(
  email: String,
  password: String,
  invite_code: Option[String],
  timezone: Option[String],
  country_code: String,
  g_response: Option[String], //response from google recaptcha
  login_challenge:Option[String]
)
object SignupViaPasswordForm {
  implicit val reads: Reads[SignupViaPasswordForm] = Json.reads[SignupViaPasswordForm]
}





case class OrgMetadataForm(
  inbox_v3: Option[Boolean]
)
object OrgMetadataForm {
  given format: OFormat[OrgMetadataForm] = Json.format[OrgMetadataForm]

}

case class ResetPasswordForm(
  email: String,
  password: String,
  code: String,
  g_response: Option[String] //response from google recaptcha
)

object ResetPasswordForm {
  implicit val reads: Reads[ResetPasswordForm] = Json.reads[ResetPasswordForm]
}

case class ChangePasswordForm(
  old_password: String,
  new_password: String,
  g_response: Option[String] //response from google recaptcha
)

object ChangePasswordForm {
  implicit val reads: Reads[ChangePasswordForm] = Json.reads[ChangePasswordForm]
}

case class VerifyEmailForm(
    otp: String,
    email: String,
    g_response: Option[String], //response from google recaptcha
    login_challenge: Option[String]
  )

object VerifyEmailForm {
  implicit val reads: Reads[VerifyEmailForm] = Json.reads[VerifyEmailForm]
}

case class ResendVerifyEmailForm(
      email: String,
      g_response: Option[String] //response from google recaptcha
    )

object ResendVerifyEmailForm {
  implicit val reads: Reads[ResendVerifyEmailForm] = Json.reads[ResendVerifyEmailForm]
}

case class ReportListStats(
  rank: Int = 0, // will be set in EmailNotificationCronService

    name: String,
    owner: String,
    team: String,
    total_prospects: Int,
    total_sent: Int,
    total_opened: Int,
    total_bounced: Int,
    total_clicked: Int,
    total_opted_out: Int,
    total_replied: Int,

    percentage_total_opened: Int,
    percentage_total_bounced: Int,
    percentage_total_clicked: Int,
    percentage_total_opted_out: Int,
    percentage_total_replied: Int
)

object ReportListStats {
  implicit val writes: OWrites[ReportListStats] = Json.writes[ReportListStats]


  def fromDb(rs: WrappedResultSet): ReportListStats = {

    val sent = rs.int("total_sent")
    val bounced = rs.int("total_bounced")
    val opened = rs.int("total_opened")
    val clicked = rs.int("total_clicked")
    val opted_out = rs.int("total_opted_out")
    val replied = rs.int("total_replied")

    ReportListStats(
      name = rs.string("name"),
      owner = rs.string("owner"),
      team = rs.string("team"),
      total_prospects = rs.int("total_prospects"),
      total_sent = sent,
      total_bounced = bounced,
      total_opened = opened,
      total_clicked = clicked,
      total_opted_out = opted_out,
      total_replied = replied,

      percentage_total_opened = Math.ceil(opened.toFloat * 100 / sent).toInt,
      percentage_total_bounced = Math.ceil(bounced.toFloat * 100 / sent).toInt,
      percentage_total_clicked = Math.ceil(clicked.toFloat * 100 / sent).toInt,
      percentage_total_opted_out = Math.ceil(opted_out.toFloat * 100 / sent).toInt,
      percentage_total_replied = Math.ceil(replied.toFloat * 100 / sent).toInt
    )
  }
}









case class ReportTemplateStats(

    rank: Int = 0, // will be used in Email report Leaderboard, set in EmailNotificationCronService

    template_id: Long,
    name: String,
    owner_name: String,
    team: String,

    prospects_contacted: Int,
    prospects_opened: Int,
    bounced: Int,
    clicked: Int,
    opted_out: Int,
    replied: Int,
    positive: Int,

    percentage_prospects_opened: Int,
    percentage_bounced: Int,
    percentage_clicked: Int,
    percentage_opted_out: Int,
    percentage_replied: Int,
    percentage_positive: Int
  )

object ReportTemplateStats {
  implicit val writes: OWrites[ReportTemplateStats] = Json.writes[ReportTemplateStats]

  def fromDb(rs: WrappedResultSet): ReportTemplateStats = {

    val templateId = rs.long("template_id")
    val contacted = rs.int("prospects_contacted")
    val opened = rs.int("prospects_opened")
    val bounced = rs.int("bounced")
    val clicked = rs.int("clicked")
    val opted_out = rs.int("opted_out")
    val replied = rs.int("replied")
    val positive = rs.int("positive")  

    ReportTemplateStats (
      template_id = templateId,
    name = rs.string("name"),
      owner_name = rs.string("owner_name"),
      team = rs.string("team"),
      prospects_contacted = contacted,
      prospects_opened = opened,
      bounced = bounced,
      clicked = clicked,
      opted_out = opted_out,
      replied = replied,
      positive = positive,  

    percentage_prospects_opened = Math.ceil(opened.toFloat * 100 / contacted).toInt,
    percentage_bounced = Math.ceil(bounced.toFloat * 100 / contacted).toInt,
    percentage_clicked = Math.ceil(clicked.toFloat * 100 / contacted).toInt,
    percentage_opted_out = Math.ceil(opted_out.toFloat * 100 / contacted).toInt,
    percentage_replied = Math.ceil(replied.toFloat * 100 / contacted).toInt,
        percentage_positive =  Math.ceil(positive.toFloat * 100 / contacted).toInt
  )
  }
}










case class ReportCampaignStats(
  rank: Int = 0, // will be set later in EmailNotificationCronService


  name: String,
  team: String,
  owner: String,
  campaign_id: CampaignId,
  total_sent: Int,
  total_opened: Int,
  total_bounced: Int,
  total_clicked: Int,
  total_opted_out: Int,
  total_replied: Int,
  total_interested: Int,
  total_positive_replies: Int,

  percentage_total_opened: Int,
  percentage_total_bounced: Int,
  percentage_total_clicked: Int,
  percentage_total_opted_out: Int,
  percentage_total_replied: Int
)

object ReportCampaignStats {
  implicit val reads: Reads[ReportCampaignStats] = Json.reads[ReportCampaignStats]

  def fromDb(rs: WrappedResultSet): ReportCampaignStats =  {

    val sent = rs.int("total_sent")
    val bounced = rs.int("total_bounced")
    val opened = rs.int("total_opened")
    val clicked = rs.int("total_clicked")
    val opted_out = rs.int("total_opted_out")
    val replied = rs.int("total_replied")
    val interested = rs.int("total_interested")
    val positive_replies = rs.int("total_positive_replies")

    ReportCampaignStats (
    name = rs.string("name"),
    team = rs.string("team"),
    owner = rs.string("owner"),
    campaign_id = CampaignId(id = rs.long("campaign_id")),
    total_sent = sent,
    total_bounced = bounced,
    total_opened = opened,
    total_clicked = clicked,
    total_opted_out = opted_out,
    total_replied = replied,
    total_interested = interested,
    total_positive_replies = positive_replies,
    percentage_total_opened = Math.ceil(opened.toFloat * 100 / sent).toInt,
    percentage_total_bounced = Math.ceil(bounced.toFloat * 100 / sent).toInt,
    percentage_total_clicked = Math.ceil(clicked.toFloat * 100 / sent).toInt,
    percentage_total_opted_out = Math.ceil(opted_out.toFloat * 100 / sent).toInt,
    percentage_total_replied = Math.ceil(replied.toFloat * 100 / sent).toInt
  )
  }
}


case class ReportCampaignStatsClvrp(
                                rank: Int = 0, // will be set later in EmailNotificationCronService

                                name: String,
                                team: String,
                                owner: String,
                                campaign_id: CampaignId,
                                total_sent: Int,
                                total_replied: Int,
                                total_interested: Int,

                                percentage_total_replied: Int
                              )

object ReportCampaignStatsClvrp {
  implicit val reads: Reads[ReportCampaignStatsClvrp] = Json.reads[ReportCampaignStatsClvrp]
}


case class ReportTeamMemberStats(
  name: String,
  // team: String,
  rank: Int, // email leaderboard rank
  running_campaigns: Int,
  total_sent: Int,
  total_opened: Int,
  total_bounced: Int,
  total_clicked: Int,
  total_opted_out: Int,
  total_replied: Int,

  percentage_total_opened: Int,
  percentage_total_bounced: Int,
  percentage_total_clicked: Int,
  percentage_total_opted_out: Int,
  percentage_total_replied: Int
)

object ReportTeamMemberStats {
  implicit val writes: OWrites[ReportTeamMemberStats] = Json.writes[ReportTeamMemberStats]

  def fromDb(rs: WrappedResultSet): ReportTeamMemberStats = {

    val sent = rs.int("total_sent")
    val bounced = rs.int("total_bounced")
    val opened = rs.int("total_opened")
    val clicked = rs.int("total_clicked")
    val opted_out = rs.int("total_opted_out")
    val replied = rs.int("total_replied")

    ReportTeamMemberStats(
      rank = 0, // will be updated later, in EmailNotificationCronService, while creating report

      name = rs.string("name"),
      // team = rs.string("team"),
      running_campaigns = rs.int("running_campaigns"),
      total_sent = sent,
      total_bounced = bounced,
      total_opened = opened,
      total_clicked = clicked,
      total_opted_out = opted_out,
      total_replied = replied,

      percentage_total_opened = Math.ceil(opened.toFloat * 100 / sent).toInt,
      percentage_total_bounced = Math.ceil(bounced.toFloat * 100 / sent).toInt,
      percentage_total_clicked = Math.ceil(clicked.toFloat * 100 / sent).toInt,
      percentage_total_opted_out = Math.ceil(opted_out.toFloat * 100 / sent).toInt,
      percentage_total_replied = Math.ceil(replied.toFloat * 100 / sent).toInt

    )
  }
}


case class DBConnections(
    pid: Option[Long],
    state: Option[String],
    rank: Option[Int],
    application_name: Option[String],
    datname: Option[String],
    query: Option[String],
    running_since: Option[DateTime]
)

object DBConnections {
  implicit val reads: Reads[DBConnections] = Json.reads[DBConnections]

  def fromDb(rs: WrappedResultSet): DBConnections = DBConnections(
    pid = rs.longOpt("pid"),
    state = rs.stringOpt("state"),
    rank = rs.intOpt("rank"),
    application_name = rs.stringOpt("application_name"),
    datname = rs.stringOpt("datname"),
    query = rs.stringOpt("query"),
    running_since = rs.jodaDateTimeOpt("running_since")
  )
}



// case class and companion object for ForgotPasswordForm
case class ForgotPasswordResendOTPForm(
  email: String,
  g_response: Option[String] //response from google recaptcha
)

object ForgotPasswordResendOTPForm {
  implicit val reads: Reads[ForgotPasswordResendOTPForm] = Json.reads[ForgotPasswordResendOTPForm]
}


////////// START: Email Reputation Related /////////

case class RepTrackingHosts(
  id: Int,
  host_url: String,
  subdomain_based: Boolean,
  active: Boolean
)
object RepTrackingHosts {
  given format: OFormat[RepTrackingHosts] = Json.format[RepTrackingHosts]
}

case class RepGoogleApiKey(
  id: Int,
  cl_id: String,
  cl_sec: String
)

////////// END: Email Reputation Related /////////






case class RoleAndUserRoleId(
                              user_role_id: Long,
                              role: String
                            )

object RoleAndUserRoleId {
  implicit val writes: OWrites[RoleAndUserRoleId] = Json.writes[RoleAndUserRoleId]

  def fromDb(rs: WrappedResultSet): RoleAndUserRoleId = RoleAndUserRoleId(
    user_role_id = rs.long("id"),
    role = rs.string("role_name"),
  )
}

class AccountDAO(
                srUuidUtils: SrUuidUtils
                ) {


  implicit val session: AutoSession.type = AutoSession


  private def fromDb(rs: WrappedResultSet)(using Logger: SRLogger): AccountWithoutOrgInternal = {

    //    val org = OrganizationWithCurrentData.fromDb(
    //      rs = rs
    //    )

    val accountId = AccountId(id = rs.int("id"))

    val accountMetadata = {

      val dbData = Json.parse(rs.any("account_metadata").asInstanceOf[PGobject].getValue).validate[AccountMetadata].get

      dbData.copy(

        is_profile_onboarding_done = if (AppConfig.isProd && accountId.id < 7280) Some(true) // going live on 9-feb-22
        else dbData.is_profile_onboarding_done

      )

    }

    // val orgRole = if (rs.stringOpt("org_role").isDefined) Some(OrganizationRole.withName(rs.string("org_role"))) else None
    val orgRole: Option[OrganizationRole] = rs.stringOpt("org_role").flatMap(OrganizationRole.withName)

    // 23 Jan 2023 - This field was unused even before separating org from this query.
    //    val allowCRMIntegration = org.settings.allow_native_crm_integration

    // Sort teams by active status and alphabetically, for the Switch View in frontend
    val _teamsUnsorted = Json.parse(
        rs
          .any("teams")
          .asInstanceOf[PGobject]
          .getValue)
      .as[Seq[JsValue]]
      .map{data =>
        data.validate[TeamAccount] match {
          case JsSuccess(value, path) => value
          case JsError(errors) =>
            Logger.fatal(s"JsError ---- Failed to parse error --- $errors data ----- $data")
            throw new Throwable(s"failed to parse errors $errors")
        }

      }
      .map(ta => {

        /*val roleData: Option[RolePermissionData] = if (orgRole == OrganizationRole.ADMIN) {

          Some(RolePermissionData.ORG_ADMIN)

        } else {

          val member = ta.access_members
            .find(mem => mem.account_id == accountId)

          if (member.isDefined) {

            if (member.get.team_role == TeamAccountRole.ADMIN) {
              Some(RolePermissionData.TEAM_ADMIN)

            } else {

              Some(RolePermissionData.TEAM_MEMBER)

            }

          } else {

            Logger.error(s"FATAL Account.fromDb _teamsUnsorted roleData member.isEmpty: ta: $ta")

            None
          }
        }

        ta.copy(role = roleData)*/

        val pc = ta.prospect_categories_custom
        val editableCategories = pc.filter(_.is_custom).sortBy(_.name.toLowerCase.trim)

        val defaultCategories = pc.filterNot(_.is_custom).sortBy(_.name.trim.toLowerCase)

        val sortedCategories = editableCategories ++ defaultCategories


        val role = RolePermissionDataV2.toRolePermissionApi(data = ta.role_from_db.get)


        ta.copy(
          //add decription here
          access_members =  ta.access_members.map{a =>
            a.copy(
              api_key = a.api_key.map(EncryptionHelpers.decryptTeamsAccountCredential),
              zapier_key = a.zapier_key.map(EncryptionHelpers.decryptTeamsAccountCredential)
            )
          },

          role = Some(role),

          prospect_categories_custom = sortedCategories


        )

      })

    val __activeTeamsSortedAlphabetically = _teamsUnsorted.filter(_.active).sortBy(_.team_name.toLowerCase.trim)

    val _activelyUsedActiveTeams = __activeTeamsSortedAlphabetically.filter(_.is_actively_used)
    val _inactivelyUsedActiveTeams = __activeTeamsSortedAlphabetically.filterNot(_.is_actively_used)

    val _inactiveTeamsSortedAlphabetically = _teamsUnsorted.filterNot(_.active).sortBy(_.team_name.toLowerCase.trim)

    val teams = _activelyUsedActiveTeams ++ _inactivelyUsedActiveTeams ++ _inactiveTeamsSortedAlphabetically



    /*
    val org = Json.parse(rs.any("org").asInstanceOf[PGobject].getValue).validate[OrganizationWithCurrentData] match {

      case JsError(e) => throw new Exception(s"Invalid organization data: $e")

      case JsSuccess(data, _) => data


    }
    */

    val orgId = OrgId(id = rs.long("org_id"))

    // Start - Dependent on organizations table.

    val is_agency = rs.boolean("org_obj_is_agency")

    val planName = rs.string("org_obj_plan_name")

    val planID = PlanID._getPlanId(planname = planName)

    val is2FAAllowedForOrg: Boolean = Helpers.is2FAAllowedForOrg(planID = planID, orgId = orgId.id)

    val accountType = if (is_agency) AccountType.AGENCY
    else if (teams.nonEmpty && teams.head.total_members > 1) AccountType.TEAM
    else AccountType.INDIVIDUAL

    // End - Dependent on organizations table.

    val isActive = rs.boolean("active")

    val calendarUserIdOpt = rs.intOpt("calendar_user_id").map(id => CalendarUserId(id = id))
    val calendarUserNameOpt = rs.stringOpt("calendar_username_slug")

    val calendarAccountData = if (calendarUserIdOpt.isDefined && calendarUserNameOpt.isDefined) {
      Some(CalendarAccountData(
        calendar_user_id = calendarUserIdOpt.get,
        calendar_username_slug = calendarUserNameOpt.get
      ))
    } else None
    AccountWithoutOrgInternal(
      internal_id = accountId,
      id = AccountUuid(rs.string("uuid")),
      email = rs.string("email"),
      email_verification_code = rs.stringOpt("email_verification_code"),
      email_verification_code_created_at = rs.jodaDateTimeOpt("email_verification_code_created_at"),
      created_at = rs.jodaDateTime("created_at"),

      first_name = rs.stringOpt("first_name")
        .flatMap(a => if (a.trim.isEmpty) None else Some(a)), // return as None if empty string in db

      last_name = rs.stringOpt("last_name")
        .flatMap(a => if (a.trim.isEmpty) None else Some(a)), // return as None if empty string in db

      company = rs.stringOpt("company"),
      timezone = rs.stringOpt("timezone"),

      profile = AccountProfileInfo(
        first_name = rs.string("first_name"),
        last_name = rs.string("last_name"),
        company = rs.stringOpt("company"),
        timezone = rs.stringOpt("timezone"),
        country_code = rs.stringOpt("country_code"),
        mobile_country_code = rs.stringOpt("mobile_country_code"),
        mobile_number = rs.longOpt("mobile_number"),

        // if user downgrades from ultimate to lower plan, ignore twofa_enabled
        twofa_enabled = is2FAAllowedForOrg && rs.boolean("twofa_enabled"),
        has_gauthenticator = rs.boolean("has_gauthenticator"),
        weekly_report_emails = rs.stringOpt("weekly_report_emails"),
        onboarding_phone_number = rs.stringOpt("onboarding_phone_number"),
        scheduled_for_deletion_at = rs.jodaDateTimeOpt("scheduled_for_deletion_at")
      ),

      teams = teams,
      account_type = accountType,
      //      current_prospect_sent_count = rs.int("current_prospect_sent_count"),

      org_id = orgId,

      org_role = orgRole,

      active = isActive,

      email_notification_summary = rs.string("email_notification_summary"),

      account_metadata = accountMetadata,

      email_verified = rs.boolean("email_verified"),
      signupType = SignupType.fromKey(rs.string("signup_type")).toOption,
      account_access = AccountAccess(
        inbox_access = rs.boolean("access_to_inbox_given")
        ),
      calendar_account_data = calendarAccountData
    )
  }


  def checkEmailAddressIfAlreadyRegisteredInCalendar(
                                                      email: String,
                                                      orgId: OrgId
                                                    )(using logger: SRLogger): Try[Boolean] = Try {


    DB readOnly { implicit session =>
      sql"""
           SELECT calendar_user_id from accounts where email =${email} and  org_id = ${orgId.id} ;
         """
        .map(rs => {
          rs.intOpt("calendar_user_id").isDefined
        }
        )
        .single
        .apply()
        .get
    }
  }

  def findAccountsThatDoNotHaveGeneralChannelSettings(): Try[List[AccountIdAndTeamId]] = Try {
    DB readOnly { implicit session =>
      sql"""
           SELECT ta.account_id, ta.team_id
           FROM teams_accounts ta
           INNER JOIN teams t ON t.id = ta.team_id
           INNER JOIN organizations o ON o.id = t.org_id
           WHERE NOT EXISTS (
              SELECT gcs.id FROM general_channel_settings gcs
              WHERE gcs.owner_account_id = ta.account_id AND gcs.team_id = ta.team_id
           )
           AND o.plan_type != ${PlanType.INACTIVE.toString}
           ;
           """
        .map(rs => {
          AccountIdAndTeamId(
            accountId = AccountId(rs.long("account_id")),
            teamId = TeamId(rs.long("team_id"))
          )
        })
        .list
        .apply()
    }
  }

  def _getTeamsAggregateSqlQuery(): SQLSyntax = {
    sqls"""
          json_agg(json_build_object(

          'access_members', (SELECT CASE WHEN ta.role = ${TeamAccountRole.MEMBER.toString} THEN

              (
                SELECT json_agg(json_build_object(
                  'team_role', user_roles.role_name,
                  'team_id', t.id,
                  'team_name', t.name,
              'first_name', sacc.first_name,
              'last_name', sacc.last_name,
              'email', sacc.email,
              'user_id', sacc.id,
              'ta_id', stacc.id,
              'api_key', stacc.api_key_enc,
              'zapier_key', stacc.zapier_key_enc
              ))
              FROM teams_accounts stacc
              INNER JOIN accounts sacc ON sacc.id = stacc.account_id
              INNER JOIN user_roles on user_roles.id = stacc.user_role_id
              WHERE stacc.team_id = t.id AND stacc.account_id = acc.id
              )


            ELSE

              (
                SELECT json_agg(json_build_object(
                  'team_role', user_roles.role_name,
                  'team_id', t.id,
                  'team_name', t.name,
              'first_name', sacc.first_name,
              'last_name', sacc.last_name,
              'email', sacc.email,
              'user_id', sacc.id,
              'ta_id', stacc.id,
              'api_key', stacc.api_key_enc,
              'zapier_key', stacc.zapier_key_enc
              ))
              FROM teams_accounts stacc
              INNER JOIN accounts sacc ON sacc.id = stacc.account_id
              INNER JOIN user_roles on user_roles.id = stacc.user_role_id
              WHERE stacc.team_id = t.id
              )

            END),


        'all_members', (
          SELECT json_agg(json_build_object(
                      'team_role', user_roles.role_name,
                      'first_name', sacc.first_name,
                      'last_name', sacc.last_name,
                      'email', sacc.email,
                      'user_id', sacc.id,
                      'api_key', stacc.api_key_enc,
                      'active', sacc.active,
                      'user_uuid', sacc.uuid,
                      'created_at', sacc.created_at,
                      'timezone', sacc.timezone,
                      'twofa_enabled', sacc.twofa_enabled
                  ))
            FROM teams_accounts stacc
            INNER JOIN accounts sacc ON sacc.id = stacc.account_id
            INNER JOIN user_roles on user_roles.id = stacc.user_role_id
            WHERE stacc.team_id = t.id
          ),


        'total_members', (SELECT COUNT(totalta.*) FROM teams_accounts totalta WHERE totalta.team_id = t.id),

        'is_actively_used', (SELECT EXISTS(
            SELECT * FROM
            campaigns tcc
            WHERE tcc.team_id = t.id AND tcc.account_id = acc.id
            AND tcc.status IN (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.SCHEDULED.toString}, ${CampaignStatus.ON_HOLD.toString})
        )),

        'role_from_db', (SELECT row_to_json(urole) FROM user_roles urole WHERE urole.id = ta.user_role_id limit 1),
        'team_name', t.name,
        'team_id', t.id,
        'team_uuid', t.uuid,
        'org_id', t.org_id,
        'active', t.active,
        'created_at', t.created_at,
        'max_emails_per_prospect_per_day', t.max_emails_per_prospect_per_day,
        'max_emails_per_prospect_per_week', t.max_emails_per_prospect_per_week,
        'max_emails_per_prospect_account_per_day', t.max_emails_per_prospect_account_per_day,
        'max_emails_per_prospect_account_per_week', t.max_emails_per_prospect_account_per_week,
        'selected_calendar_data',(
            json_build_object(
              'calendar_event_type_id',t.calendar_event_type_id,
              'calendar_event_type_slug',t.calendar_event_type_slug,
              'calendar_is_individual',t.calendar_is_individual,
              'calendar_selected_user_id',t.calendar_selected_user_id,
              'calendar_selected_username_slug',t.calendar_selected_username_slug,
              'calendar_team_id',t.calendar_team_id,
              'calendar_team_slug',t.calendar_team_slug,
              'calendar_smartreach_account_id',t.calendar_smartreach_account_id
              )
         ),

        'reply_handling', t.reply_handling,

        'prospect_categories_custom',  (
           SELECT
              json_agg(
                json_build_object(
                   'id', pc.id,
                   'name', pc.name,
                   'text_id', pc.text_id,
                   'label_color', pc.label_color,
                   'is_custom', pc.is_custom,
                   'team_id', pc.team_id,
                   'rank', pc.prospect_category_rank
                )
                ORDER BY pc.prospect_category_rank
              )
           FROM prospect_categories_custom pc
           WHERE pc.team_id = t.id
        )

      )) AS teams
        """
  }

  def findAccountSql(whereCondition: SQLSyntax):SQL[?, ?] = {

    /*

    'current_accounts_in_use', (
          SELECT COUNT (caiu_a.id)
            FROM accounts caiu_a
            INNER JOIN teams_accounts caiu_ta ON caiu_ta.account_id = caiu_a.id
             INNER JOIN campaigns caiu_c ON caiu_c.ta_id = caiu_ta.id
             WHERE caiu_a.org_id = org.id AND caiu_c.status = 'running'
        ),
     */

  val teamsAggSql: SQLSyntax = _getTeamsAggregateSqlQuery()

  sql"""
  SELECT

  $teamsAggSql,

  org.plan_name as org_obj_plan_name,
  org.is_agency as org_obj_is_agency,

  acc.id,
  acc.uuid,
  acc.email,
  acc.email_verification_code,
  acc.email_verification_code_created_at,
  acc.created_at,
  acc.first_name,
  acc.last_name,
  acc.company,
  acc.timezone,
  acc.org_role,
  acc.org_id,
  acc.active,
  acc.email_notification_summary,
  acc.email_verified,
  acc.mobile_country_code,
  acc.country_code,
  acc.signup_type,
  acc.mobile_number,
  acc.twofa_enabled,
  acc.gauthkey_enc IS NOT NULL as has_gauthenticator,
  acc.metadata as account_metadata,
  acc.weekly_report_emails,
  acc.access_to_inbox_given,
  acc.calendar_user_id,
  acc.calendar_username_slug,
  acc.scheduled_for_deletion_at as scheduled_for_deletion_at,
  acc.onboarding_phone_number

  FROM accounts acc
  INNER JOIN teams_accounts ta ON ta.account_id = acc.id
  INNER JOIN teams t ON t.id = ta.team_id
  INNER JOIN organizations org ON org.id = acc.org_id
  --INNER JOIN accounts org_accounts ON org_accounts.org_id = org.id


  WHERE ${AccountDAO.conditionToGetAllExistingTeams()}
  $whereCondition

    GROUP BY acc.id, org.id
    LIMIT 1;
      """
  }

  def findTry(id: AccountId)(using Logger: SRLogger): Try[Option[AccountWithoutOrgInternal]] = Try {

    DB readOnly { implicit session =>

      findAccountSql(sqls" AND acc.id = ${id.id} ")
        .map(fromDb)
        .single
        .apply()

    }
  }

  // TODO - move the logic to AccountDAOService and have test cases for query generation
  def getTeamsListing(
                       accountId: AccountId,
                       queryTimeline: InferredQueryTimeline,
                       active: Option[Boolean]
                     ): Try[Seq[TeamAccount]] = Try {
    DB readOnly { implicit session =>

      val isActive = active.getOrElse(true)
      val whereCondition = sqls"AND acc.id = ${accountId.id} AND t.active = $isActive"
      val clausePagination = queryTimeline match {

        case InferredQueryTimeline.Range.Before(dateTime) =>
          sqls" AND t.created_at AT TIME ZONE 'UTC' < ${dateTime} AT TIME ZONE 'UTC'"

        case InferredQueryTimeline.Range.After(dateTime) =>
          sqls" AND t.created_at AT TIME ZONE 'UTC' > ${dateTime} AT TIME ZONE 'UTC'"

        case InferredQueryTimeline.Exact(exactTimeThreadOrTask) =>
          sqls" AND t.created_at AT TIME ZONE 'UTC' = (select created_at from teams where id = ${exactTimeThreadOrTask.id.toLong})"

      }

      _getTeamsListing(whereCondition, clausePagination)
        .map(AccountDAO.fromDbTeamsListing)
        .single
        .apply()
        .get

    }
  }

  def _getTeamsListing(whereCondition: SQLSyntax, clausePagination: SQLSyntax):SQL[?, ?] = {

    val teamsAggregateSqlQuery: SQLSyntax = _getTeamsAggregateSqlQuery()

    sql"""
      SELECT

      $teamsAggregateSqlQuery

      FROM accounts acc
      INNER JOIN teams_accounts ta ON ta.account_id = acc.id
      INNER JOIN teams t ON t.id = ta.team_id
      INNER JOIN organizations org ON org.id = acc.org_id

      WHERE ${AccountDAO.conditionToGetAllExistingTeams()}
      $whereCondition
      $clausePagination;
       """
  }



  def findByApiKey(apiKey: String, keyType: SRApiKeyType): Try[Option[AccountId]] = Try { DB readOnly { implicit session =>

    val accountIdQuery:SQL[?, ?] = keyType match {

      case SRApiKeyType.SRTeamUserLevelKey =>

        sql"""
              SELECT acc.id
              FROM accounts acc
              INNER JOIN teams_accounts ta ON ta.account_id = acc.id
              WHERE ta.api_key_enc = ${EncryptionHelpers.encryptTeamsAccountsCredential(apiKey)}
              LIMIT 1
              """

      case SRApiKeyType.SRZapierKey =>

        sql"""
                SELECT acc.id
                FROM accounts acc
                INNER JOIN teams_accounts ta ON ta.account_id = acc.id
                WHERE ta.zapier_key_enc = ${EncryptionHelpers.encryptTeamsAccountsCredential(apiKey)}
                LIMIT 1
                """

      case SRApiKeyType.SRUserLevelKey =>

        sql" SELECT acc.id FROM accounts acc WHERE acc.user_api_key_enc = ${EncryptionHelpers.encryptAccountsCredential(apiKey)} LIMIT 1"

      case SRApiKeyType.SRProspectDaddyKey =>

        sql" SELECT acc.id FROM accounts acc WHERE acc.sr_pd_api_key_enc = ${EncryptionHelpers.encryptAccountsCredential(apiKey)} LIMIT 1"


      case SRApiKeyType.SRWarmupBoxKey =>

        sql"""
            SELECT acc.id
            FROM accounts acc
            INNER JOIN organizations org ON org.id = acc.org_id
            WHERE org.warmupbox_key_enc = ${EncryptionHelpers.encryptOrganizationsCredential(apiKey)}
            AND acc.org_role = ${OrganizationRole.OWNER.toString}
            LIMIT 1

            """

    }


    accountIdQuery
      .map(res => AccountId(res.long("id")))
      .headOption
      .apply()
  }
  }


  /*
   *
   * Use case: prospect upload via upload csv - save csv in google after getting presigned url - Step 2.
   */

  def findNewOwnerTeamMember(teamId: TeamId, accountId: AccountId): Try[Option[TeamMemberBasic]] = Try {
    DB readOnly { implicit session =>

      sql"""
        select
          ta.team_id,
          ta.id as ta_id,
          acc.id as user_id,
          acc.first_name,
          acc.last_name
        from teams_accounts ta
        join accounts acc on acc.id = ta.account_id
        where ta.team_id = ${teamId.id}
        and ta.account_id = ${accountId.id}
        ;

        """
        .map(rs => TeamMemberBasic(
          team_id = rs.long("team_id"),
          user_id = rs.long("user_id"),
          ta_id = rs.long("ta_id"),
          first_name = rs.stringOpt("first_name").getOrElse(""),
          last_name = rs.stringOpt("last_name").getOrElse("")
        ))
        .single
        .apply()

    }
  }

  def findIfAnyCampaignInOrgHasDripCampaign(orgId: OrgId): Try[Boolean] = Try {
    DB readOnly { implicit session =>

      sql"""
        SELECT EXISTS (
          SELECT * FROM
          campaigns c
          JOIN teams t ON c.team_id = t.id
          WHERE
          t.org_id = ${orgId.id}
          AND c.status in (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString})
          AND c.campaign_type = ${CampaignType.Drip.toString}
          ) AS using_drip_campaign;

        """
        .map(rs => rs.boolean("using_drip_campaign"))
        .single
        .apply()
        .get
    }
  }

  def findIfAnyCampaignInOrgHasMCCampaign(orgId: Long): Try[Boolean] = Try {
    DB readOnly { implicit session =>

      sql"""
        SELECT EXISTS (
          SELECT * FROM 
          campaign_steps cs 
          JOIN campaigns c ON c.id = cs.campaign_id
          JOIN teams t ON c.team_id = t.id
          WHERE
          t.org_id = $orgId
          AND c.status in (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString})
          AND cs.step_type != ${AutoEmailStep.toKey}
          ) AS using_mc_campaign;

        """
        .map(rs => rs.boolean("using_mc_campaign"))
        .single
        .apply()
        .get
    }
  }

  def isMultiChannelCampaign(
    teamId: TeamId,
    campaignId: CampaignId,
  ): Try[Boolean] = Try {

    // cannot say status == RUNNING etc because campaign will be paused when they try to resume

    DB readOnly { implicit session =>

      sql"""
        SELECT EXISTS (
          SELECT * FROM
          campaign_steps cs
          JOIN campaigns c ON c.id = cs.campaign_id
          JOIN teams t ON c.team_id = t.id
          WHERE
          t.id = ${teamId.id}
          AND c.id = ${campaignId.id}
          AND cs.step_type != ${AutoEmailStep.toKey}
          ) AS using_mc_campaign;

        """
        .map(rs => rs.boolean("using_mc_campaign"))
        .single
        .apply()
        .get
    }
  }


  def getTotalCallingAccountsOfOrg(orgId: OrgId): Try[Int] = Try {
    DB readOnly {
      implicit session =>
        sql"""
           SELECT COUNT(*)
           AS calling_accounts_number
           FROM call_settings cs
           LEFT JOIN teams t
           ON cs.team_id = t.id
           WHERE org_id = ${orgId.id};
         """
          .map(rs => rs.int("calling_accounts_number"))
          .single
          .apply()
          .get
    }
  }

  def findTeamById(id: TeamId): Option[Team] = DB readOnly { implicit session =>

    sql"""
    select * from teams where id = ${id.id};

    """
      .map(Team.fromDb)
      .headOption
      .apply()

  }

  def findByEmail(email: String): Try[Option[AccountId]] = Try{
    DB readOnly { implicit session =>

    val param = email.toLowerCase.trim

    sql"SELECT acc.id FROM accounts acc WHERE lower(acc.email) = $param LIMIT 1"
      .map(rs => AccountId(rs.long("id")))
      .headOption
      .apply()

  }
  }

  // check if anything with the same tracking key exists
  def checkOrgTrackingSubdomainKey(checkKey: String): Int = DB readOnly { implicit session =>

    val regex = s"$checkKey%"

    sql"Select count(*) from organizations org where org.tracking_subdomain_key ilike $regex;"
      .map(rs => rs.int("count"))
      .single
      .apply()
      .head

  }


  def _createTeam(name: String, orgId: OrgId)(implicit session: DBSession): Option[TeamId] = {

      val defaultSendingLimitPerProspectPerWeek = 3
      val defaultSendingLimitPerProspectPerDay = 1
      val defaultReplyHandlingSetting = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY.toString
      val teamUuid: String = srUuidUtils.generateTeamUuid()

      sql"""
          INSERT INTO teams
          (name, org_id, max_emails_per_prospect_per_day, max_emails_per_prospect_per_week, reply_handling, uuid)
          VALUES (${name.trim}, ${orgId.id}, $defaultSendingLimitPerProspectPerDay, $defaultSendingLimitPerProspectPerWeek, $defaultReplyHandlingSetting, $teamUuid)
          RETURNING id;
      """
        .map(rs => TeamId(id = rs.long("id")))
        .single
        .apply()

  }

  def fetchAllAgencyAdminsAndOwnerOfOrg(orgId: OrgId): Try[List[AccountIdAndOrgRole]] = Try {
    DB readOnly { implicit session =>
      sql"""
           SELECT id, org_role from accounts
           WHERE org_id = ${orgId.id}
           AND org_role IN (${OrganizationRole.OWNER.toString}, ${OrganizationRole.AGENCY_ADMIN.toString})
           ;
           """
        .map(rs => 
          AccountIdAndOrgRole(
            accountId = AccountId(rs.long("id")),
            orgRole = OrganizationRole.withName(rs.string("org_role")).get // This would not come as null because of IN condition
          )
        )
        .list
        .apply()
    }
  }



  def createInvitedAccount(
                            data: AccountCreateForm,
                            hashedPassword: Option[String],
                            invite: InviteMemberWithOrgAndTeamNames,
                            timezone: Option[String],
                            country: Option[String],
                            email_verified: Boolean,
                            weeklyReportEmails: String,
                            signup_type: SignupType,
                            account_uuid: String
                          )(implicit session: DBSession): Try[AccountId] = Try {

    val role: Option[OrganizationRole] = if (invite.user_role_name == TeamAccountRole.AGENCY_ADMIN) {
      Some(OrganizationRole.AGENCY_ADMIN)
    }
    else None

        sql"""
                  INSERT INTO accounts
                  (
                    email,
                    password,

                    first_name,
                    last_name,

                    country_code,
                    mobile_country_code,
                    mobile_number,

                    company,
                    org_id,
                    org_role,

                    timezone,
                    country,
                    email_verified,
                    weekly_report_emails,
                    signup_type,

                    uuid
                  )
                  VALUES (
                    ${data.email.trim.toLowerCase},
                    $hashedPassword,

                    ${data.first_name},
                    ${data.last_name},

                    ${invite.country_code},
                    ${invite.mobile_country_code},
                    ${invite.mobile_number},

                    ${data.company},
                    ${invite.org_id},

                    ${role.map(_.toString)},
                    ${timezone.orNull},
                    $country,
                    $email_verified,
                    $weeklyReportEmails,
                    ${signup_type.toString},

                    $account_uuid
                 )
                  RETURNING id;
                """
                .map(rs => AccountId(id = rs.long("id")))
                .single
                .apply()
                .get

  }

  def createNewAccount(
                     data: AccountCreateForm,
                     hashedPassword: Option[String],
                     timezone: Option[String],
                     country: Option[String],
                     weeklyReportEmails: String,
                     signup_type: SignupType,
                     orgId: OrgId,
                     account_uuid: String
                      )(implicit session: DBSession): Try[AccountId] = Try {

          sql"""
            INSERT INTO accounts
              (
              email,
              password,
              first_name,
              last_name,
              company,
              org_id,
              org_role,
              timezone,
              country_code,
              country,
              weekly_report_emails,
              signup_type,

              uuid
              )
            VALUES
            (
            ${data.email.trim.toLowerCase},
            $hashedPassword,
             ${data.first_name},
             ${data.last_name},
             ${data.company},
             ${orgId.id},
             ${OrganizationRole.OWNER.toString},
             ${timezone.orNull},
             ${data.country_code},
             $country,
             $weeklyReportEmails,
             ${signup_type.toString},

             $account_uuid
             )

              RETURNING id;
          """
          .map(rs => AccountId(id = rs.long("id")))
          .single
          .apply()
            .get
  }


  def getAccountHashedPasswd(
    accountId: AccountId
  ): Try[Option[String]] = Try {

    DB.readOnly { implicit session =>
      sql"select password from accounts where id = ${accountId.id}"
        .map(_.string("password"))
        .single
        .apply()
    }
  }

  // should have been transaction but I could not figure out how to check / modify the isolation level
  // so the teams_accounts query is throwing an error saying account_id does not exist
  //
  // This method is not being used.
//  def addTeam(teamName: String, accountId: AccountId, orgId: OrgId, accountName: String)(
//    rolePermissionDataDAOV2: RolePermissionDataDAOV2
//  ): Try[Boolean] = Try {
//    DB localTx { implicit session =>
//
//      val teamId = _createTeam(
//        name = teamName,
//        orgId = orgId
//      ).get
//
//      val roles = rolePermissionDataDAOV2.findAllRoles(orgId = orgId.id).get // FIXME VALUECLASS
//
//      val categories = insertDefaultCategories(
//        teamId = teamId,
//        accountId = accountId,
//        accountName = accountName
//      )
//
//      val ownerRoleId = roles.find(r => r.role_name == TeamAccountRole.OWNER).get.id
//
//      val tidAcc = _createTeamAccount(
//        teamId = teamId,
//        accountId = accountId,
//        userRoleId = ownerRoleId,
//        role = TeamAccountRole.ADMIN
//      )
//
//      true
//
//      /*val a = for {
//
//      } yield tidAcc
//
//      a match {
//        case Failure(e) => throw e
//        case Success(_) => Account.find(accountId).get
//      }*/
//    }
//  }

  def getOwnerAccountIdByOrgId(
    orgId: OrgId
  ): Try[Option[AccountId]] = Try {

    DB.readOnly { implicit session =>

      sql"""
           select
            owner_account_id
           from organizations
           where
            id = ${orgId.id}"""
        .map(o => AccountId(id = o.long("owner_account_id")))
        .single
        .apply()

    }
  }

  def getOrgCreditDetails(orgId: OrgId): Try[Option[CreditsObject]] = Try {
    DB.readOnly {
      implicit session =>
        sql"""
             SELECT
             call_credits_remaining,
             twl_trigger_current_value,
             credit_unit,
             call_credits,
             twl_sub_account_status
             FROM org_calling_credits
             WHERE org_id = ${orgId.id}
           """
          .map(res => {
            val sub_account_status = TwilioSubAccountStatus.fromString(res.string("twl_sub_account_status")).get
            CreditsObject(
              call_credits_remaining = res.bigInt("call_credits_remaining"),
              /*
              note:  this matching is done because a type mismatch error was occurring when using bigIntOpt
              required : Option[BigInt]
              fount : Option[java.math.BigInteger]
              */
              twl_trigger_current_value = res.bigIntOpt("twl_trigger_current_value") match {
                case Some(_) => Some(res.bigInt("twl_trigger_current_value"))
                case None => None
              },
              credit_unit = CurrencyType.fromKey(res.string("credit_unit")).get,
              call_credits = res.bigInt("call_credits"),
              twl_sub_account_status = sub_account_status
            )
          })
          .single
          .apply()
    }
  }

  def inviteTeamMember(
    inviterAccountId: AccountId,
    email: String,
    orgId: OrgId,
    teamId: TeamId,
    teamName: String,
    inviteCode: String,

    firstName: String,
    lastName: String,
    countryCode: Option[String],
    mobileCountryCode: Option[String],
    mobileNumber: Option[Long],
    userRoleId: Long

  ): Try[Option[InviteMember]] = Try {
    val invite = DB autoCommit { implicit session =>


      sql"""
          INSERT INTO team_agency_invites
          (inviter_account_id, org_id, team_id, email, invite_type, invite_code,

          first_name,
          last_name,
          country_code,
          mobile_country_code,
          mobile_number,
          user_role_id

          )
          VALUES (${inviterAccountId.id}, ${orgId.id}, ${teamId.id}, $email, 'team', $inviteCode,

          $firstName,
          $lastName,
          $countryCode,
          $mobileCountryCode,
          $mobileNumber,
          $userRoleId

          )
          RETURNING *;
      """
        .map(_.long("id"))
        .single
        .apply()

    }

    invite.flatMap(id => getInviteById(inviteId = id))

  }


  def update2FAEnabled(
    isSMSFlow: Boolean,
    accountId: AccountId,
    countryCode: Option[String],
    mobileCountryCode:  Option[String],
    mobileNumber:  Option[Long],
    gauthkey: Option[String]
  ): Try[Option[AccountId]] = Try {
    DB autoCommit { implicit session =>

      val q = if (isSMSFlow) {

        sql"""
          UPDATE accounts
          SET
            mobile_country_code = ${mobileCountryCode.get},
            country_code = ${countryCode.get},
            mobile_number = ${mobileNumber.get},
            twofa_enabled = true,
            twofa_enabled_at = now()

          WHERE id = ${accountId.id}
          RETURNING id;
      """
      } else {

        sql"""
          UPDATE accounts
          SET
            gauthkey_enc = ${gauthkey.map(EncryptionHelpers.encryptAccountsCredential)},
            twofa_enabled = true,
            twofa_enabled_at = now()

          WHERE id = ${accountId.id}
          RETURNING id;
        """
      }


      q.map(rs => AccountId(id = rs.long("id")))
        .single
        .apply()
    }

  }


  def findGAuthKeyFor2FA(id: AccountId): Option[String] = DB readOnly { implicit session =>

    sql"Select gauthkey_enc from accounts where id = ${id.id};"
      .map(rs => rs.stringOpt("gauthkey_enc").map(EncryptionHelpers.decryptAccountsCredential))
      .single
      .apply()
      .flatten

  }

  def updateUserManagementSettings(orgId: OrgId, enforce_2fa: Boolean): Try[Int] = Try {
    DB autoCommit { implicit session =>
      sql"""
          UPDATE organizations
          SET
            enforce_2fa = $enforce_2fa,
            enforce_2fa_at = now()
          WHERE id = ${orgId.id}
      """
        .update
        .apply()

    }

  }


  def getInviteById(inviteId: Long): Option[InviteMember] = DB readOnly { implicit session =>

    sql"""
          SELECT tai.*, t.name as team_name FROM team_agency_invites tai
          INNER JOIN teams t ON tai.team_id = t.id
          WHERE tai.id = $inviteId;
      """
      .map(InviteMember.fromDb)
      .single
      .apply()

  }

  def getInviteByCode(inviteCode: String): Try[List[InviteMemberWithOrgAndTeamNames]] = Try{
    DB readOnly { implicit session =>
    sql"""

    SELECT t.name AS team_name,
    CONCAT(a.first_name, ' ', a.last_name) AS inviter_name,
    a.org_id,
    o.name AS org_name,
    tai.id,
    tai.first_name,
    tai.last_name,
    tai.country_code,
    tai.mobile_country_code,
    tai.mobile_number,
    tai.invite_code,
    tai.email,
    tai.team_id,
    tai.user_role_id,
    user_roles.role_name as user_role_name
    FROM team_agency_invites tai
    INNER JOIN accounts a ON a.id = tai.inviter_account_id
    INNER JOIN user_roles ON user_roles.id = tai.user_role_id
    LEFT JOIN teams t ON t.id = tai.team_id
    LEFT JOIN organizations o ON o.id = tai.org_id
    WHERE tai.email = (SELECT email FROM team_agency_invites WHERE invite_code = $inviteCode LIMIT 1)


      """
      .map(InviteMemberWithOrgAndTeamNames.fromDb)
      .list
      .apply()

  }
}

  def deleteInviteByCode(inviteCode: String, org_id: OrgId): Try[Option[String]] = Try {
    DB autoCommit { implicit session =>

      sql"""
          DELETE FROM team_agency_invites WHERE invite_code = $inviteCode AND org_id = ${org_id.id}
          RETURNING email;
      """
        .map(rs => rs.string("email"))
        .single
        .apply()

    }
  }

  def getInvitesByTeamId(teamId: TeamId): Seq[InviteMember] = DB readOnly { implicit session =>

    sql"""
          SELECT tai.*, t.name as team_name FROM team_agency_invites tai
          INNER JOIN teams t ON tai.team_id = t.id
          WHERE tai.team_id = ${teamId.id};
      """
      .map(InviteMember.fromDb)
      .list
      .apply()
  }


  def getInvitesByOrgId(orgId: OrgId): Seq[InviteMember] = DB readOnly { implicit session =>

    sql"""
          SELECT tai.*, t.name as team_name FROM team_agency_invites tai
          INNER JOIN teams t ON tai.team_id = t.id
          WHERE tai.org_id = ${orgId.id};
      """
      .map(InviteMember.fromDb)
      .list
      .apply()
  }



  def getInviteByEmail(email: String): Option[InviteMemberBasic] = DB readOnly { implicit session =>

    sql"""
          SELECT
           tai.*, t.name as team_name,
           CONCAT(a.first_name, ' ', a.last_name) AS inviter_name
          FROM team_agency_invites tai
          INNER JOIN accounts a ON a.id = tai.inviter_account_id
          LEFT JOIN teams t ON t.id = tai.team_id
          WHERE tai.email = $email;
      """
      .map(InviteMemberBasic.fromDb)
      .list
      .apply()
      .headOption
  }



  def getUserLevelApiKey(accountId: AccountId): Try[Option[String]] = Try {
    DB readOnly { implicit session =>

      sql"""
          SELECT a.user_api_key_enc FROM accounts a
          WHERE a.id = ${accountId.id};
      """
        .map(_.stringOpt("user_api_key_enc").map(EncryptionHelpers.decryptAccountsCredential))
        .single
        .apply()
        .flatten
    }
  }

  def getProspectDaddyApiKey(accountId: AccountId): Try[Option[String]] = Try {
    DB readOnly { implicit session =>

      sql"""
          SELECT a.sr_pd_api_key_enc FROM accounts a
          WHERE a.id = ${accountId.id};
      """
        .map(_.stringOpt("sr_pd_api_key_enc").map(EncryptionHelpers.decryptAccountsCredential))
        .single
        .apply()
        .flatten
    }
  }

  def getSRWarmupBoxKey(accountId: AccountId): Try[Option[String]] = Try {
    DB readOnly { implicit session =>

      sql"""
          SELECT org.warmupbox_key_enc
          FROM accounts a
          JOIN organizations org ON org.id = a.org_id
          WHERE a.id = ${accountId.id};
      """
        .map(_.stringOpt("warmupbox_key_enc").map(EncryptionHelpers.decryptOrganizationsCredential))
        .single
        .apply()
        .flatten
    }
  }

  def updateApiKey(
    accountId: AccountId,
    orgId: OrgId,
    teamId: Option[TeamId],
    keyType: SRApiKeyType
  ): Try[Option[String]] = Try {
    DB autoCommit { implicit session =>

      val updatedApiKeyOpt = keyType match {

        case SRApiKeyType.SRTeamUserLevelKey =>

          val newApiKey = SRApiKeyType.genApiKey(keyType = keyType)

          sql"""
            UPDATE teams_accounts
            SET
              api_key_enc = ${EncryptionHelpers.encryptTeamsAccountsCredential(newApiKey)}
            WHERE account_id = ${accountId.id} AND team_id = ${teamId.get.id}
            RETURNING api_key_enc;
          """
            .map(rs => EncryptionHelpers.decryptTeamsAccountCredential(rs.string("api_key_enc")))
            .single
            .apply()

        case SRApiKeyType.SRZapierKey =>

          val newApiKey = SRApiKeyType.genApiKey(keyType = keyType)

          sql"""
            UPDATE teams_accounts
            SET
              zapier_key_enc = ${EncryptionHelpers.encryptTeamsAccountsCredential(newApiKey)}
            WHERE account_id = ${accountId.id} AND team_id = ${teamId.get.id}
            RETURNING zapier_key_enc;
          """
            .map(rs => EncryptionHelpers.decryptTeamsAccountCredential(rs.string("zapier_key_enc")))
            .single
            .apply()

        case SRApiKeyType.SRUserLevelKey =>

          val newApiKey = SRApiKeyType.genApiKey(keyType = keyType)
          //returning the normal key for user to note it down
          sql"""
            UPDATE accounts
            SET
              user_api_key_enc = ${EncryptionHelpers.encryptAccountsCredential(newApiKey)}
            WHERE id = ${accountId.id}
            RETURNING user_api_key_enc;
          """
            .map(rs => EncryptionHelpers.decryptAccountsCredential(rs.string("user_api_key_enc")))
            .single
            .apply()

        case SRApiKeyType.SRProspectDaddyKey =>

          val newApiKey = SRApiKeyType.genApiKey(keyType = keyType)

          sql"""
            UPDATE accounts
            SET
              sr_pd_api_key_enc = ${EncryptionHelpers.encryptAccountsCredential(newApiKey)}
            WHERE id = ${accountId.id}
            RETURNING sr_pd_api_key_enc;
          """
            .map(rs => EncryptionHelpers.decryptAccountsCredential(rs.string("sr_pd_api_key_enc")))
            .single
            .apply()

        case SRApiKeyType.SRWarmupBoxKey =>

          val newApiKey = SRApiKeyType.genApiKey(keyType = keyType)
//FIXME do we encrypt this?
          sql"""
            UPDATE organizations
            SET
              warmupbox_key_enc = ${EncryptionHelpers.encryptOrganizationsCredential(newApiKey)}
            WHERE id = ${orgId.id}
            RETURNING warmupbox_key_enc;
          """
            .map(rs => EncryptionHelpers.decryptOrganizationsCredential(rs.string("warmupbox_key_enc")))
            .single
            .apply()

      }

      updatedApiKeyOpt


    }

  }

  /**
   * 5-Sep-2024: if we try to reset cache for accounts only in inactive teams, then it throws a Account Not Found error
   * because the main AccountDAO.findAccountSql fn doesnt return accounts only in inactive teams
   */
  def getAllApiKeysToResetCache(accountId: AccountId): Seq[ResetApiCache] = DB readOnly { implicit session =>

    sql"""

      select
        allacc.id AS user_id,
        allacc.user_api_key_enc,
        allacc.sr_pd_api_key_enc,
        string_agg(ta.api_key_enc, ',') as taapikeys
      from accounts self
      join accounts allacc on allacc.org_id = self.org_id
      join teams_accounts ta on ta.account_id = allacc.id
      join teams t on t.id = ta.team_id
      where self.id = ${accountId.id}

        AND ${AccountDAO.conditionToGetAllExistingTeams()}

      group by allacc.id
      ;


       """
      .map(rs => {
        ResetApiCache(
          user_id = rs.long("user_id"),

          user_api_key = rs.stringOpt("user_api_key_enc").map(EncryptionHelpers.decryptAccountsCredential),

          sr_pd_api_key = rs.stringOpt("sr_pd_api_key_enc").map(EncryptionHelpers.decryptAccountsCredential),

          ta_api_keys = rs.stringOpt("taapikeys")
            .map(str => str.split(",").toIndexedSeq.map(_.trim)
            .map(EncryptionHelpers.decryptTeamsAccountCredential))
        )
      })
      .list
      .apply()
  }

  def updateAccountMetadata(accountId: AccountId, metadata: AccountMetadata): Try[Int] = Try {
    DB autoCommit { implicit session =>

      sql"""
          UPDATE accounts
          SET
            metadata = metadata || to_jsonb(${Json.toJson(metadata).toString()}::jsonb)
          WHERE id = ${accountId.id};
      """
        .update
        .apply()
    }

  }

  def updateOrgMetadata(orgId: OrgId, metadata: OrgMetadataForm): Try[Int] = Try {
    DB autoCommit { implicit session =>

      sql"""
          UPDATE organizations
          SET
            metadata = metadata || to_jsonb(${Json.toJson(metadata).toString()}::jsonb)
          WHERE id = ${orgId.id};
      """
        .update
        .apply()
    }
  }

  def updateAccountOnboardingData(accountId: AccountId, onboardingData: JsValue): Try[AccountId] = Try {
    DB autoCommit { implicit session =>

      sql"""
          UPDATE accounts
          SET
            onboarding_data = to_jsonb(${onboardingData.toString}::jsonb)
          WHERE id = ${accountId.id}
          RETURNING id;
              """
        .map(
          rs => AccountId(id = rs.long("id"))
        )
        .single
        .apply()
        .get
    }

  }


  def updateOrgOnboardingData(orgId: OrgId, onboardingData: JsValue): Try[OnboardingUTMParams] = Try {
    DB autoCommit { implicit session =>

      sql"""
          UPDATE organizations
          SET
            onboarding_data = to_jsonb(${onboardingData.toString}::jsonb)
          WHERE id = ${orgId.id}
          RETURNING
            utm_source,
            utm_medium,
            utm_campaign,
            utm_term,
            utm_content
          ;
      """
        .map(rs => {
          OnboardingUTMParams(
            utm_source = rs.stringOpt("utm_source"),
            utm_medium = rs.stringOpt("utm_medium"),
            utm_campaign = rs.stringOpt("utm_campaign"),
            utm_term = rs.stringOpt("utm_term"),
            utm_content = rs.stringOpt("utm_content")
          )
        })
        .single
        .apply()
        .get
    }

  }

  def updateOrgWithEnrichedCompanyData(
    orgId: OrgId,
    enrichedCompanyData: EnrichedCompanyData
  ): Try[Option[OrgId]] = Try {

    DB autoCommit { implicit session =>

      val enrichedCompanyInfoOpt: Option[EnrichedCompanyInfo] = enrichedCompanyData.enrichedCompanyInfoOpt

      val enrichedCompanyInfoJson: JsValue = Json.toJson(enrichedCompanyInfoOpt)

      val enrichedCompanyInfoRaw: JsValue = enrichedCompanyData.enrichedCompanyInfoRaw

      sql"""
          UPDATE
            organizations
          SET
          	enriched_company_info = to_jsonb(${enrichedCompanyInfoJson.toString}::jsonb),
            enriched_company_info_raw = to_jsonb(${enrichedCompanyInfoRaw.toString}::jsonb),
            enriched_company_info_updated_at = now()
          WHERE
            id = ${orgId.id}
          RETURNING
            id
        """
        .map(
          rs => OrgId(id = rs.long("id"))
        )
        .single
        .apply()
    }
  }


  def enableAgencyViewDuringOnboarding(
    orgId: OrgId,
    enableAgency: Boolean,
    planId: PlanID,
  ): Try[Int] = Try {

    // We don't want to update the limits if they are on a paid plan.
    val updateLimitsStatement = if (planId == PlanID.TRIAL) {

      val prospectsAndChannelSeats = PlanLimitService.getDefaultLimitsForTrials(isAgency = enableAgency)

      sqls"""
            plan_prospects_saved_max = ${prospectsAndChannelSeats.prospects_saved},

            plan_crm_integrations_max = ${prospectsAndChannelSeats.crm_integrations},

            plan_client_teams_max = ${prospectsAndChannelSeats.client_teams},

            plan_calling_seats_max = ${prospectsAndChannelSeats.calling_accounts},

            plan_li_automation_seats_max = ${prospectsAndChannelSeats.automated_linkedin_accounts},
            plan_li_manual_seats_max = ${prospectsAndChannelSeats.manual_linkedin_accounts},

            plan_prospects_contacted_max = ${prospectsAndChannelSeats.prospects_contacted},
          """

    } else {

      sqls""

    }


    DB autoCommit { implicit session =>

      sql"""
          UPDATE organizations
          SET
            $updateLimitsStatement
            is_agency = ${enableAgency}
          WHERE id = ${orgId.id}
          ;
      """
        .update
        .apply()
    }

  }

  def updateOrgRoleToAgencyAdmin(accountId: AccountId)
                                (implicit session: DBSession): Try[Int] = Try {

    sql"""
         UPDATE accounts
         SET org_role = ${OrganizationRole.AGENCY_ADMIN.toString}
         WHERE id = ${accountId.id}
         ;
         """
      .update
      .apply()

  }

  def updateUserRole(
                      accountId: AccountId,
                      teamId: TeamId,
                      userRoleId: Long,
                      newRoleName: String
  )(implicit session: DBSession): Try[Int] = Try {

      sql"""
          UPDATE teams_accounts
          SET
            role = $newRoleName,
            user_role_id = $userRoleId
          WHERE account_id = ${accountId.id} AND team_id = ${teamId.id}
      """
        .update
        .apply()

  }


  def updateTeamName(teamId: TeamId, newName: String): Try[Option[TeamId]] = Try {
    DB autoCommit { implicit session =>


      sql"""
          UPDATE teams SET
            name = $newName
          WHERE id = ${teamId.id}
          RETURNING id;
      """
        .map(rs => TeamId(id = rs.long("id")))
        .single
        .apply()
    }

  }

  def updateTeamConfig(accountId: AccountId, teamId: TeamId, data: UpdateTeamConfig): Try[Option[TeamId]] = Try {
    DB autoCommit { implicit session =>

      val maxEmailsPerProspectAccountPerDaySql: SQLSyntax = data.max_emails_per_prospect_account_per_day
        .map(dayLimit => sqls"  max_emails_per_prospect_account_per_day = $dayLimit, ")
        .getOrElse(sqls"")

      val maxEmailsPerProspectAccountPerWeekSql: SQLSyntax = data.max_emails_per_prospect_account_per_week
        .map(weekLimit => sqls"  max_emails_per_prospect_account_per_week = $weekLimit, ")
        .getOrElse(sqls"")


      sql"""
          UPDATE teams
          SET
            max_emails_per_prospect_per_day = ${data.max_emails_per_prospect_per_day},
            max_emails_per_prospect_per_week = ${data.max_emails_per_prospect_per_week},

            $maxEmailsPerProspectAccountPerDaySql

            $maxEmailsPerProspectAccountPerWeekSql

            reply_handling = ${data.reply_handling.toString}

          WHERE id = ${teamId.id}
          RETURNING id;
      """
        .map(rs => TeamId(id = rs.long("id")))
        .single
        .apply()
    }

  }

  def getAccountEmailNotificationSettings(accountId: AccountId): Try[Option[String]] = Try {
    DB readOnly { implicit session =>

      sql"""
          SELECT email_notification_summary
          FROM accounts
          WHERE id = ${accountId.id}
         ;
      """
        .map(rs => rs.string("email_notification_summary"))
        .single
        .apply()
    }

  }


  def updateAccountEmailNotificationSettings(accountId: AccountId, data: UpdateAccountEmailNotificationSettings): Try[Option[AccountId]] = Try {
    DB autoCommit { implicit session =>

      val emails = data.weekly_report_emails
        .map(em => {
          em
            .split(",")
            .map(_.trim.toLowerCase)
            .filterNot(_.isEmpty)
            .distinct
            .mkString(",")
        })

      sql"""
          UPDATE accounts
          SET
            email_notification_summary = ${data.email_notification_summary.toString},
            weekly_report_emails = $emails,
            last_email_notification_summary_state = email_notification_summary,
            last_email_notification_summary_updated_at = now()

          WHERE id = ${accountId.id}
          RETURNING id;
      """
        .map(rs => AccountId(id = rs.long("id")))
        .single
        .apply()
    }

  }


  def updatePasswordAndResetVerificationCode(accountId: AccountId, password: String, code: String): Try[Option[AccountId]] = Try {
    DB autoCommit { implicit session =>

      val hashedPassword = PasswordHasher.hash(password)

      sql"""
          UPDATE accounts
          SET
            password = $hashedPassword,
            email_verified = TRUE,
            email_verification_code = NULL,
            email_verification_code_created_at = NULL
          WHERE id = ${accountId.id}
            AND email_verification_code = $code
          RETURNING id;
      """
        .map(rs => AccountId(id = rs.long("id")))
        .single
        .apply()
    }


  }

  def updatePasswordWhenUserIsLoggedIn(accountId: AccountId, password: String): Try[Option[AccountId]] = Try {
    DB autoCommit { implicit session =>

      val hashedPassword = PasswordHasher.hash(password)

      sql"""
          UPDATE accounts
          SET
            password = $hashedPassword,
            email_verification_code = NULL,
            email_verification_code_created_at = NULL
          WHERE id = ${accountId.id}
          RETURNING id;
      """
        .map(rs => AccountId(id = rs.long("id")))
        .single
        .apply()
    }


  }








  def updateAccountEmailStatusIsVerified(accountId: AccountId, code: String): Try[Option[AccountId]] = Try {

    DB autoCommit { implicit session =>

      sql"""
          UPDATE accounts
          SET
            email_verified = TRUE,
            email_verification_code = NULL,
            email_verification_code_created_at = NULL
          WHERE id = ${accountId.id}
              AND email_verification_code = $code
          RETURNING id;
      """
        .map(rs => AccountId(id = rs.long("id")))
        .single
        .apply()
    }


  }



  /*

  def _incrementCurrentSentProspectCount(accountId: Long): Int = {
    DB autoCommit { implicit session =>


      sql"""
          UPDATE accounts
          SET
            current_prospect_sent_count = current_prospect_sent_count + 1
          WHERE id = $accountId
          ;
      """
        .update
        .apply()
    }


  }

  */
  


  def addEmailVerificationCode(accountId: AccountId, emailVerificationCode: String): Try[Option[AccountId]] = Try {
    DB autoCommit { implicit session =>


      sql"""
          UPDATE accounts
          SET
            email_verification_code = $emailVerificationCode,
            email_verification_code_created_at = now()
          WHERE id = ${accountId.id}
          RETURNING id;
      """
        .map(rs => AccountId(id = rs.long("id")))
        .single
        .apply()

    }

  }

  def getEmailVerificationCodeByAccountIDForResend(accountId: AccountId): Try[Option[String]] = Try {
    DB autoCommit {implicit session =>

      sql"""
        SELECT a.email_verification_code
        FROM accounts a
        WHERE a.id = ${accountId.id}
         AND a.email_verified = FALSE
         AND a.email_verification_code IS NOT NULL
       """
        .map(rs => rs.string("email_verification_code"))
        .single
        .apply()

    }
  }


  def updateProfile(accountId: AccountId, data: UpdateAccountProfile): Try[Option[AccountId]] = Try {
    DB autoCommit { implicit session =>


      sql"""
          UPDATE accounts
          SET
            first_name = ${data.first_name},
            last_name = ${data.last_name},
            timezone = ${data.timezone}
          WHERE id = ${accountId.id}
          RETURNING id;
      """
        .map(rs => AccountId(id = rs.long("id")))
        .single
        .apply()

    }

  }

  def updateProfileDuringOnboardingForMember(
                                              accountId: AccountId,
                                              first_name: String,
                                              last_name: String,
                                              onboarding_phone_number: String,
                                              is_phone_number_valid: Boolean
                                            ): Try[Option[AccountId]] = Try {
    DB autoCommit { implicit session =>


      sql"""
          UPDATE accounts
          SET
            first_name = $first_name,
            last_name = $last_name,
            onboarding_phone_number = $onboarding_phone_number,
            is_onboarding_phone_number_valid = $is_phone_number_valid
          WHERE id = ${accountId.id}
          RETURNING id;
      """
        .map(rs => AccountId(id = rs.long("id")))
        .single
        .apply()

    }

  }

  def updateProfileDuringOnboardingForOwner(
                                             accountId: AccountId,
                                             first_name: String,
                                             last_name: String,
                                             newOrgName: String,
                                             onboarding_phone_number: String,
                                             is_phone_number_valid: Boolean,
                                             org_id: OrgId
                                           ): Try[Option[OrgId]] = Try {
    DB localTx { implicit session =>


      sql"""
          UPDATE accounts
          SET
            first_name = $first_name,
            last_name = $last_name,
            company = $newOrgName,
            onboarding_phone_number = $onboarding_phone_number,
            is_onboarding_phone_number_valid = $is_phone_number_valid
          WHERE id = ${accountId.id}
          RETURNING id
      """
        .map(rs => AccountId(id = rs.long("id")))
        .single
        .apply()

      sql"""
          UPDATE accounts
          SET
            company = $newOrgName
          WHERE org_id = ${org_id.id}
          RETURNING id
      """
        .map(rs => AccountId(id = rs.long("id")))
        .list
        .apply()

      sql"""
           UPDATE organizations
           SET name = $newOrgName
           WHERE owner_account_id = ${accountId.id}
           RETURNING id
      """
        .map(rs => OrgId(id = rs.long("id")))
        .single
        .apply()

    }

  }






  def deactivateTrialExpiredOrgs(): Try[Seq[Organization]] = Try {

    DB autoCommit { implicit session =>

      sql"""
          UPDATE organizations
          SET
            plan_name = ${PlanID.INACTIVE.toString},
            plan_type = ${PlanType.INACTIVE.toString},
            error_reported_at = null,
            total_sending_email_accounts = 1,
            base_licence_count = 0,
            additional_licence_count = 0,
            paused_till = null,
            error_code = null,
            new_prospects_paused_till = null,
            warning_msg = null,
            warning_at = null,
            warning_code = null
          WHERE plan_type = ${PlanType.TRIAL.toString} and trial_ends_at <= now()
          RETURNING *;
        """
        .map(Organization.fromDb)
        .list
        .apply()
    }

  }

  def updateFirstPromoterReferralTrackSignup(
    orgId: OrgId,
    affiliateCookieValue: String,
    firstPromoterReferrerAccountDetails: FirstPromoterReferrerAccountDetails
  ): Try[Int] = Try {
    DB autoCommit { implicit session =>

      sql"""
          UPDATE
            organizations
          SET
            fp_referrer_track_id = $affiliateCookieValue,
            fp_referrer_account_id = ${firstPromoterReferrerAccountDetails.accountId.id},
            fp_referrer_email = ${firstPromoterReferrerAccountDetails.email},
            referral_coupon_status = ${ReferralCouponStatus.NotApproved.toString},
            affiliate_tracker = ${AffiliateTracker.FirstPromoter.toString}
          WHERE
            id = ${orgId.id}
            AND referral_coupon_status IS NULL;
      """
        .update
        .apply()
    }
  }


  // AFFILIATE RELATED:
  def updateFirstPromoterAffiliateTrackSignup(orgId: OrgId, fpLead: JsValue): Try[Int] = Try {
    DB autoCommit { implicit session =>

      sql"""
          UPDATE 
            organizations
          SET 
            firstpromoter_lead = to_jsonb(${fpLead.toString}::jsonb),
            affiliate_tracker = ${AffiliateTracker.FirstPromoter.toString}
          WHERE
            id = ${orgId.id};
      """
        .update
        .apply()

    }
  }

  // Store admitad_uid and admitad_gclid params, as we need them to make an api to to admitad later.
  def updateAdmitadTrackSignup(
    orgId: OrgId,
    admitad_uid: String,
    admitadGclidCookieValue: Option[String]
  ): Try[Int] = Try {
    DB autoCommit { implicit session =>

      sql"""
          UPDATE
            organizations
          SET
            admitad_lead = ${admitad_uid},
            admitad_gclid = ${admitadGclidCookieValue},
            affiliate_tracker = ${AffiliateTracker.Admitad.toString}
          WHERE
            id = ${orgId.id}
      """
        .update
        .apply()

    }
  }

  def updateCalendarUserData(
                        orgId:OrgId,
                        accountId: AccountId,
                        calendarUserId:CalendarUserId,
                        calendarUsernameSlug: String
                        ):Try[Int] = Try{
    DB autoCommit{ implicit  session =>
      sql"""
           UPDATE
            accounts
           SET
            calendar_user_id = ${calendarUserId.id},
            calendar_username_slug = ${calendarUsernameSlug}
           WHERE
            id = ${accountId.id}
            and org_id = ${orgId.id};
         """
        .update
        .apply()

    }
  }


  def activateOrDeactivateTeam(teamId: TeamId, orgId: OrgId, active: Boolean): Try[Int] = Try {
    DB autoCommit { implicit session =>
      val deactivatedAt: SQLSyntax = if (!active) sqls" , deactivated_at = ${DateTime.now()} " else sqls""


      sql"""
          UPDATE teams
          SET
            active = $active
            $deactivatedAt
            WHERE id = ${teamId.id}
            AND org_id = ${orgId.id}
          ;
      """
        .update
        .apply()

    }

  }

  def activateOrDeactivateAccount(accountId: AccountId, orgId: OrgId, active: Boolean): Try[Int] = Try {
    DB autoCommit { implicit session =>
      val deactivatedAt: SQLSyntax = if (!active) sqls" , deactivated_at = ${DateTime.now()} " else sqls""


      sql"""
          UPDATE accounts
          SET
            active = $active
            $deactivatedAt
            WHERE id = ${accountId.id}
            AND org_id = ${orgId.id}
          ;
      """
        .update
        .apply()

    }

  }

  def _getTeamMatesByTeamId(
                             teamId: TeamId,
                             queryTimeline: Option[InferredQueryTimeline] = None,
                             limit: Int = 100
                           ): Seq[ResetTeamCache] = {
    val userListingApiWhereClauseAndOrderBy: SQLSyntax = if (queryTimeline.isDefined) {
      queryTimeline.get match {
        case InferredQueryTimeline.Range.Before(dateTime) =>
          sqls" AND a.created_at AT TIME ZONE 'UTC' < ${dateTime} AT TIME ZONE 'UTC' ORDER BY a.created_at desc LIMIT $limit"

        case InferredQueryTimeline.Range.After(dateTime) =>
          sqls" AND a.created_at AT TIME ZONE 'UTC' > ${dateTime} AT TIME ZONE 'UTC' ORDER BY a.created_at asc LIMIT $limit"

        case InferredQueryTimeline.Exact(exactTimeThreadOrTask) =>
          sqls" AND a.created_at AT TIME ZONE 'UTC' = (select created_at from accounts where id = ${exactTimeThreadOrTask.id.toLong} and team_id = ${teamId}) AT TIME ZONE 'UTC'"

      }
    } else sqls" ORDER BY LOWER(a.email) ASC "

    getTeamMatesByTeamId(
      teamId = teamId,
      whereClause = userListingApiWhereClauseAndOrderBy
    )
  }

  // SECURITY FIXME: do not return api keys in invites api
  def getTeamMatesByTeamId(
                            teamId: TeamId,
                            whereClause: SQLSyntax = sqls""
                          ): Seq[ResetTeamCache] =
    DB readOnly { implicit session =>
      sql"""

      select a.id as user_id, subq.teams, a.first_name, a.last_name, a.email, a.active, a.scheduled_for_deletion_at, o.uuid as org_uuid, a.uuid, a.org_role, a.created_at, a.timezone, a.twofa_enabled
      from accounts a
      join teams_accounts ta on ta.account_id = a.id
      left join lateral (
        select
          json_agg(
          json_build_object(
            'team_name', t.name,
            'team_id', ta3.team_id,
            'role_id', ta3.user_role_id,
            'role_name', ur.role_name,
            'api_key', ta3.api_key_enc
            )
        ) as teams
        from teams_accounts ta3 join teams t on t.id = ta3.team_id
        join user_roles ur on ur.id = ta3.user_role_id
        where ta3.account_id = a.id and ta3.team_id = ta.team_id
      ) AS subq on true
      join organizations o on o.id = a.org_id
      where ta.team_id = ${teamId.id} ${whereClause};

       """
      .map(ResetTeamCache.fromDb)
      .list
      .apply()
  }

  def _getTeamMatesByOrgId(
                             orgId: OrgId,
                             queryTimeline: Option[InferredQueryTimeline] = None,
                             limit: Int = 100
                           ): Seq[ResetTeamCache] = {
    val userListingApiWhereClauseAndOrderBy: SQLSyntax = if (queryTimeline.isDefined) {
      queryTimeline.get match {
        case InferredQueryTimeline.Range.Before(dateTime) =>
          sqls" AND a.created_at AT TIME ZONE 'UTC' < ${dateTime} AT TIME ZONE 'UTC' ORDER BY a.created_at desc LIMIT $limit"

        case InferredQueryTimeline.Range.After(dateTime) =>
          sqls" AND a.created_at AT TIME ZONE 'UTC' > ${dateTime} AT TIME ZONE 'UTC' ORDER BY a.created_at asc LIMIT $limit"

        case InferredQueryTimeline.Exact(exactTimeThreadOrTask) =>
          sqls" AND a.created_at AT TIME ZONE 'UTC' = (select created_at from accounts where id = ${exactTimeThreadOrTask.id.toLong} and org_id = ${OrgId}) AT TIME ZONE 'UTC'"

      }
    } else sqls" ORDER BY LOWER(a.email) ASC "

    getTeamMatesByOrgId(
      orgId = orgId,
      userListingApiWhereClauseAndOrderBy = userListingApiWhereClauseAndOrderBy
    )
  }


  // SECURITY FIXME: do not return api keys in invites api
  def getTeamMatesByOrgId(
                           orgId: OrgId,
                           userListingApiWhereClauseAndOrderBy: SQLSyntax = sqls""
                         ): Seq[ResetTeamCache] =
    DB readOnly { implicit session =>

    //    sql"SELECT * FROM accounts WHERE email = $email LIMIT 1"

    sql"""

    select a.id as user_id, subq.teams, a.first_name, a.last_name, a.email, a.active, a.scheduled_for_deletion_at, o.uuid as org_uuid, a.uuid, a.org_role, a.created_at, a.timezone, a.twofa_enabled
    from accounts a
    left join lateral (
      select
        COALESCE(
        json_agg(
          json_build_object(
            'team_name', t.name,
            'team_id', ta.team_id,
            'role_id', ta.user_role_id,
            'role_name', ur.role_name,
            'api_key', ta.api_key_enc
          )
        ),
        '[]') as teams
      from teams_accounts ta join teams t on (t.id = ta.team_id AND ${AccountDAO.conditionToGetAllExistingTeams()})
      join user_roles ur on ur.id = ta.user_role_id
      where ta.account_id = a.id
    ) AS subq on true
    join organizations o on o.id = a.org_id
    where a.org_id = ${orgId.id} ${userListingApiWhereClauseAndOrderBy};

    """
      .map(ResetTeamCache.fromDb)
      .list
      .apply()

  }

  def getUserByAccountId(
                  accountId: AccountId,
                  orgId: OrgId
                 ): Try[UserFromAccountApiResponse] = Try {
    DB readOnly { implicit session =>
      sql"""
          select a.id as user_id, a.first_name, a.last_name, a.email, a.active, o.uuid as org_uuid, a.uuid, a.org_role, a.created_at, a.timezone, a.twofa_enabled
          from accounts a
          join organizations o on o.id = a.org_id
          where a.id = ${accountId.id}
          and a.org_id = ${orgId.id}
         """
        .map(UserFromAccountApiResponse.fromDb)
        .single
        .apply()
        .get
    }
  }

  def getTopListStatsForReportV2(
    subordinates: Seq[SubordinatesByTeamIdForReportGeneration]
  ): Try[Seq[ReportListStats]] = Try {

    val teamAccountJoinClause: SQLSyntax = subordinates
      .map { team =>

        val aidClause: SQLSyntax = if (team.hasAccessToAllTeamMemberAccounts) {
          sqls""
        } else {
          sqls" AND a.id IN (${team.accessAccountIds.map(_.id)})"
        }
        
        sqls" ( t.id = ${team.teamId.id} $aidClause ) "
      }
      .reduce((a, b) => sqls" $a OR $b ")

    DB readOnly {
      implicit session =>

        sql"""
             with latest_lists as (
             select pl1.id, pl1.name, pl1.total_prospects, pl1.team_id, pl1.account_id, t.name as team
             from prospect_lists pl1
             join teams t on t.id = pl1.team_id
             join accounts a on a.id = pl1.account_id
             where
               ( $teamAccountJoinClause )
               and pl1.total_prospects > 0
               and pl1.total_emails_sent > 0
               and pl1.last_contacted >= date_trunc('day', now() AT TIME ZONE 'UTC') - interval '1 week'
               order by pl1.created_at desc
               --limit 5
             )

            SELECT

             ll.id as list_id,
             ll.name as name,
             ll.total_prospects AS total_prospects,
             a.first_name AS owner,
             ll.team AS team,

             COUNT(DISTINCT p.id) AS total_sent,
             COUNT(DISTINCT e.prospect_id) FILTER (WHERE e.opened AND c.open_tracking_enabled) AS total_opened,
             COUNT(DISTINCT p.id) FILTER (WHERE e.clicked) AS total_clicked,
             COUNT(DISTINCT p.id) FILTER (WHERE e.replied) AS total_replied,
             COUNT(DISTINCT p.id) FILTER (WHERE e.bounced) AS total_bounced,
             COUNT(DISTINCT p.id) FILTER (WHERE e.opted_out) AS total_opted_out

             FROM prospects p
             INNER JOIN emails_scheduled e ON e.prospect_id = p.id and p.team_id = e.team_id
             LEFT JOIN campaigns c on c.id = e.campaign_id and c.team_id = e.team_id
             INNER JOIN latest_lists ll ON p.list_id = ll.id
             INNER JOIN accounts a ON a.id = ll.account_id
             WHERE
               p.list_id = ll.id
               AND p.last_contacted_at IS NOT NULL
               AND e.sent
               AND e.scheduled_from_campaign

               AND e.sent_at >= date_trunc('day', now() AT TIME ZONE (CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END)) - interval '1 week'
               AND e.sent_at < date_trunc('day', now() AT TIME ZONE (CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END)) - interval '1 second'

              GROUP BY ll.id, ll.name, ll.total_prospects, a.id, ll.team
          """
          .map(ReportListStats.fromDb)
          .list
          .apply()
          .sortBy(_.total_opened)
          .reverse
          .take(5)
    }
  }

  def getTopCampaignStatsForReportV2(
    subordinates: Seq[SubordinatesByTeamIdForReportGeneration]
  ): Try[Seq[ReportCampaignStats]] = Try {

    if (subordinates.isEmpty) {
      Seq()
    } else {


    val teamAccountJoinClause: SQLSyntax = subordinates
      .map { team =>

        val aidClause: SQLSyntax = if (team.hasAccessToAllTeamMemberAccounts) {
          sqls""
        } else {
          sqls" AND a.id IN (${team.accessAccountIds.map(_.id)})"
        }


        sqls" ( t.id = ${team.teamId.id} $aidClause ) "
      }
      .reduce((a, b) => sqls" $a OR $b ")


    DB readOnly {
      implicit session =>

        sql"""
           select
              t.name as team,
              c.id as campaign_id,
              c.name as name ,
              a.first_name as owner,
              COUNT(DISTINCT (CASE WHEN es.sent IS TRUE THEN es.prospect_id END)) AS total_sent,
             COUNT(DISTINCT es.prospect_id) FILTER (WHERE es.opened AND c.open_tracking_enabled) AS total_opened,
              COUNT(DISTINCT (CASE WHEN es.bounced IS TRUE THEN es.prospect_id END)) AS total_bounced,
              COUNT(DISTINCT (CASE WHEN es.clicked IS TRUE THEN es.prospect_id END)) AS total_clicked,
              COUNT(DISTINCT (CASE WHEN es.replied IS TRUE THEN es.prospect_id END)) AS total_replied,
              COUNT(DISTINCT (CASE WHEN es.opted_out IS TRUE THEN es.prospect_id END)) AS total_opted_out,
              count(DISTINCT (
                    CASE WHEN pcat.text_id = ${ProspectCategory.INTERESTED.toString} THEN
                            es.prospect_id
                          END)) AS total_interested,
              count(DISTINCT (
                  CASE WHEN rs.reply_sentiment_type = ${ReplySentimentType.Positive.toString} THEN
                    es.prospect_id
                  END)) AS total_positive_replies
           from campaigns c
           join accounts a on a.id = c.account_id
           join teams t on t.id = c.team_id
           left join emails_scheduled es on es.campaign_id = c.id and es.team_id = c.team_id

           INNER JOIN prospects p ON es.prospect_id = p.id and es.team_id = p.team_id
           INNER JOIN prospect_categories_custom pcat ON p.prospect_category_id_custom = pcat.id
           LEFT JOIN email_threads et ON et.latest_email_id = es.id and et.team_id = es.team_id
           LEFT JOIN reply_sentiments_for_teams rs ON et.reply_sentiment_uuid = rs.uuid and et.team_id = rs.team_id

           where (
            $teamAccountJoinClause
           )
           and es.sent
           and es.scheduled_from_campaign
           and c.status in (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString})

           and es.sent_at >= date_trunc('day', now() AT TIME ZONE (CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END)) - interval '1 week'
           and es.sent_at < date_trunc('day', now() AT TIME ZONE (CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END)) - interval '1 second'

           group by c.id, t.name, a.id
           -- order by c.status_changed_at desc
           -- limit 5;
          """
          .map(ReportCampaignStats.fromDb)
          .list
          .apply()
          .sortBy(_.total_opened)
          .reverse
          .take(5)
    }
    }
  }

  def getTopTemplateStatsForReport(
    subordinates: Seq[SubordinatesByTeamIdForReportGeneration]
  ): Try[Seq[ReportTemplateStats]] = Try {

    val teamAccountJoinClause: SQLSyntax = subordinates
      .map { team =>

        val aidClause: SQLSyntax = if (team.hasAccessToAllTeamMemberAccounts) {
          sqls""
        } else {
          sqls" AND a.id IN (${team.accessAccountIds.map(_.id)})"
        }
        
        sqls" ( team.id = ${team.teamId.id} $aidClause ) "
      }
      .reduce((a, b) => sqls" $a OR $b ")

    DB readOnly {
      implicit session =>

        sql"""
           select
              tm.id as template_id,
              tm.label as name,
              team.name as team,
              a.first_name as owner_name,
              COUNT(DISTINCT (CASE WHEN es.sent IS TRUE THEN es.prospect_id END)) AS prospects_contacted,
              COUNT(DISTINCT (CASE WHEN es.opened IS TRUE THEN es.prospect_id END)) AS prospects_opened,
              COUNT(DISTINCT (CASE WHEN es.bounced IS TRUE THEN es.prospect_id END)) AS bounced,
              COUNT(DISTINCT (CASE WHEN es.clicked IS TRUE THEN es.prospect_id END)) AS clicked,
              COUNT(DISTINCT (CASE WHEN es.replied IS TRUE THEN es.prospect_id END)) AS replied,
              COUNT(DISTINCT (CASE WHEN es.opted_out IS TRUE THEN es.prospect_id END)) AS opted_out,
              count(DISTINCT (
                  CASE WHEN rs.reply_sentiment_type = ${ReplySentimentType.Positive.toString} THEN
                    es.prospect_id
                  END)) AS positive
           from templates tm
           join accounts a on tm.account_id = a.id
           join teams team on team.id = tm.team_id
           left join emails_scheduled es on es.template_id = tm.id AND es.team_id = team.id
           
           LEFT JOIN email_threads et ON et.latest_email_id = es.id and et.team_id = es.team_id
           LEFT JOIN reply_sentiments_for_teams rs ON et.reply_sentiment_uuid = rs.uuid and et.team_id = rs.team_id

           where
           ( $teamAccountJoinClause )

           and es.sent
           and es.scheduled_from_campaign

           and es.sent_at >= date_trunc('day', now() AT TIME ZONE (CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END)) - interval '1 week'
           and es.sent_at < date_trunc('day', now() AT TIME ZONE (CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END)) - interval '1 second'

           group by tm.id, a.id, team.id
           -- order by c.status_changed_at desc
           -- limit 5;
          """
          .map(ReportTemplateStats.fromDb)
          .list
          .apply()
          .sortBy(_.prospects_opened)
          .reverse
          .take(5)

    }

  }

  def getAllTeamAdminReportCampaignStats(
    team_member_aids: Seq[AccountId],
    orgId: OrgId
  ): Try[Seq[ReportTeamMemberStats]] = Try {
    DB readOnly {
      implicit session =>

        sql"""
         select
             a.first_name as name,
             --t.name as team,

             (select count(rc.*) from campaigns rc where rc.status in (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString}) and rc.account_id = a.id) as running_campaigns,

             COUNT(DISTINCT (CASE WHEN es.sent IS TRUE THEN es.prospect_id END)) AS total_sent,
             COUNT(DISTINCT es.prospect_id) FILTER (WHERE es.opened AND c.open_tracking_enabled) AS total_opened,
             COUNT(DISTINCT (CASE WHEN es.bounced IS TRUE THEN es.prospect_id END)) AS total_bounced,
             COUNT(DISTINCT (CASE WHEN es.clicked IS TRUE THEN es.prospect_id END)) AS total_clicked,
             COUNT(DISTINCT (CASE WHEN es.replied IS TRUE THEN es.prospect_id END)) AS total_replied,
             COUNT(DISTINCT (CASE WHEN es.opted_out IS TRUE THEN es.prospect_id END)) AS total_opted_out
             from accounts a
             left join emails_scheduled es on es.account_id = a.id
             left join campaigns c on c.id = es.campaign_id and c.team_id = es.team_id
             --join teams t on t.id = es.team_id
             where a.id  IN (${team_member_aids.map(_.id)})
             and a.org_id = ${orgId.id}
             and es.sent
             and es.scheduled_from_campaign

             and es.sent_at >= date_trunc('day', now() AT TIME ZONE (CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END)) - interval '1 week'
             and es.sent_at < date_trunc('day', now() AT TIME ZONE (CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END)) - interval '1 second'

             group by a.id
             -- order by t.name
             ;
         """
          .map(ReportTeamMemberStats.fromDb)
          .list
          .apply()
    }
  }

  def getTeamNamesAndIdsBySendDate(
                            orgId: OrgId,
                            tids: Seq[TeamId]
                          ): Try[List[TeamInfo]] = Try {

    val teams = DB.readOnly{ implicit session =>
      sql"""
            SELECT
              t.name as team_name,
              t.id as team_id,
              t.org_id as org_id,
              t.active as active
            FROM teams t

            WHERE t.id IN (${tids.map(_.id)})
              AND t.org_id = ${orgId.id}
            GROUP BY t.id
            ;
         """
        .map(rs => TeamInfo(
          team_id = rs.long("team_id"),
          team_name = rs.string("team_name"),
          org_id = rs.long("org_id"),
          active = rs.boolean("active"))
        )
        .list
        .apply()
    }

    val activeTeamsSortedAlphabetically = teams.filter(_.active).sortBy(_.team_name.toLowerCase.trim)
    val inactiveTeamsSortedAlphabetically = teams.filterNot(_.active).sortBy(_.team_name.toLowerCase.trim)

    activeTeamsSortedAlphabetically ++ inactiveTeamsSortedAlphabetically

  }
/*
11 July 2024 :
Not added the blacklist check for domains here as the function don't have any purpose of scheduling and just has the purpose to show report
 */
  def getTeamSummary(
                    orgId: OrgId,
                    tid: TeamId,
                    aid: AccountId,
                    from: Option[DateTime],
                    till: Option[DateTime],
                    positive_reply_sentiments: List[ReplySentimentUuid]
                    )(using Logger: SRLogger): Try[ReportTeamStats] = Try {

    DB.readOnly { implicit session =>
      val checkInboxAfterDate = DateTime.now().minusDays(AppConfig.inboxUnreadCountShowForDays)

      val fromWhereClause = if (from.isDefined) {
        sqls" and es.sent_at >= ${from.get} "
      } else {
        sqls"""
            and es.sent_at >= date_trunc('day', (now() + interval '1 day') AT TIME ZONE (
              CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END
            )) - interval '1 week'
            """
      }

      val tillWhereClause = if (till.isDefined) {
        sqls" and es.sent_at < ${till.get} "
      } else {
        sqls"""
            and es.sent_at < date_trunc('day', (now() + interval '1 day') AT TIME ZONE (
              CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END
            )) - interval '1 second'
            """
      }


      // FIXME VALUECLASS
      val inboxUnreadCountSQL: SQLSyntax = if (AppConfig.showInboxUnreadCountForOrgIds.contains(orgId.id)) {
        //FIXME SENDER_ROTATION - https://github.com/heaplabs/smartreach_backend/pull/3464#discussion_r1332969069
        sqls"""
             SELECT COUNT(*) AS count FROM
              (
                  SELECT DISTINCT ON (et.id)
                    et.id
                  FROM email_threads et
                  INNER JOIN email_threads_prospects etp ON etp.email_thread_id = et.id
                  INNER JOIN prospects p ON p.id = etp.prospect_id
                  INNER JOIN prospect_categories_custom pcat ON pcat.id = p.prospect_category_id_custom
                  where
                    t.active
                    and et.latest_reply_at is not null
                    and et.folder_type != ${FolderType.DONE.textId}
                    and not et.sr_read
                    and et.latest_email_id is not null
                    AND et.team_id = ${tid.id}
                    and et.latest_reply_at > $checkInboxAfterDate
                    and pcat.text_id != ${ProspectCategory.DELIVERY_FAILED.toString}

                   LIMIT 50
               ) threads
            """
      } else {
        sqls" SELECT 0 as count "
      }

      val queryForPositiveReplySentiment = if (positive_reply_sentiments.isEmpty) {
        sqls"(SELECT 0) as total_positive_reply, "
      } else {
        sqls"""
              COUNT(DISTINCT (CASE WHEN et.reply_sentiment_uuid IN ${SQLUtils.generateSQLValuesClause(positive_reply_sentiments.map(_.uuid))} THEN es.prospect_id END)) AS total_positive_reply,
            """
      }

      val query =
        sql"""
          SELECT

            t.name as team,
            t.id as team_id,
            t.org_id as org_id,
            t.active as active,
            ($inboxUnreadCountSQL) as inbox_unread_count,

            (SELECT count(sc.*) from campaigns sc where sc.status in (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString}) and sc.team_id = t.id) as running_campaigns,

            (SELECT COUNT(totalta.*) FROM teams_accounts totalta WHERE totalta.team_id = t.id) AS total_members,

            (SELECT

              (CASE WHEN (send.paused_till is not null and send.paused_till > now() + interval '1 week') THEN send.error
                    WHEN (rec.paused_till is not null and rec.paused_till > now() + interval '1 week') THEN rec.error

                    WHEN (send.is_under_review is true OR send.is_under_review is true) THEN 'Your email account is under manual review. Please contact support.'

                    ELSE NULL END

              ) as error

              from campaigns c
              JOIN campaign_email_settings ces on ces.campaign_id = c.id and c.team_id = ces.team_id
              join email_settings send on send.id = ces.sender_email_setting_id
              join email_settings rec on rec.id = ces.receiver_email_setting_id

              where
              (
                (send.paused_till is not null and send.paused_till > now() + interval '1 week')
                OR
                (rec.paused_till is not null and rec.paused_till > now() + interval '1 week')
              )
              and c.status IN (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString}, ${CampaignStatus.SCHEDULED.toString})
              and c.account_id = ${aid.id}
              and c.team_id = ${tid.id}
              and t.active
              LIMIT 1

            ) AS error,


            COUNT(DISTINCT (CASE WHEN es.sent IS TRUE THEN es.prospect_id END)) AS total_sent,
            COUNT(DISTINCT es.prospect_id) FILTER (WHERE es.opened AND c.open_tracking_enabled) AS total_opened,
            COUNT(DISTINCT (CASE WHEN es.bounced IS TRUE THEN es.prospect_id END)) AS total_bounced,
            COUNT(DISTINCT (CASE WHEN es.clicked IS TRUE THEN es.prospect_id END)) AS total_clicked,
            COUNT(DISTINCT (CASE WHEN es.replied IS TRUE THEN es.prospect_id END)) AS total_replied,
            $queryForPositiveReplySentiment
            COUNT(DISTINCT (CASE WHEN es.opted_out IS TRUE THEN es.prospect_id END)) AS total_opted_out

          FROM teams t
          JOIN accounts a on a.id = ${aid.id}
          LEFT JOIN emails_scheduled es on (
            t.id = es.team_id
            and es.sent
            and es.scheduled_from_campaign
            $fromWhereClause
            $tillWhereClause
          )
          LEFT JOIN campaigns c on c.id = es.campaign_id and c.team_id = es.team_id

          LEFT JOIN email_threads et on et.id = es.email_thread_id and et.team_id = es.team_id

          WHERE t.id = ${tid.id}
            AND t.org_id = ${orgId.id}
          GROUP BY t.id
          ;
        """

      query
        .map(ReportTeamStats.fromDb)
        .single
        .apply()
        .get
    }

  }


    /*
11 July 2024 :
Not added the blacklist check for domains here as the function don't have any purpose of scheduling and just has the purpose to show report
 */
  def getAgencyTeamLevelStats(
    orgId: OrgId,
    tids: Seq[TeamId],
    aid: AccountId,
    from: Option[DateTime],
    till: Option[DateTime],
    positive_reply_sentiments: List[ReplySentimentUuid]
  ): Try[Seq[ReportTeamStats]] = Try {
    val teams = DB readOnly {
      implicit session =>

        /*sql"""
       select
         t.name as team,
         t.id as team_id,
         t.org_id as org_id,
         t.active as active,

         (select count(rc.*) from campaigns rc where rc.status = ${CampaignStatus.RUNNING.toString} and rc.team_id = t.id) as running_campaigns,
         (SELECT COUNT(totalta.*) FROM teams_accounts totalta WHERE totalta.team_id = t.id) AS total_members,

           COUNT(DISTINCT (CASE WHEN es.sent IS TRUE THEN es.prospect_id END)) AS total_sent,
           COUNT(DISTINCT (CASE WHEN es.opened IS TRUE THEN es.prospect_id END)) AS total_opened,
           COUNT(DISTINCT (CASE WHEN es.bounced IS TRUE THEN es.prospect_id END)) AS total_bounced,
           COUNT(DISTINCT (CASE WHEN es.clicked IS TRUE THEN es.prospect_id END)) AS total_clicked,
           COUNT(DISTINCT (CASE WHEN es.replied IS TRUE THEN es.prospect_id END)) AS total_replied,
           COUNT(DISTINCT (CASE WHEN es.opted_out IS TRUE THEN es.prospect_id END)) AS total_opted_out

           from teams t
           join accounts a on a.id = $aid
           left join emails_scheduled es on t.id = es.team_id

           where t.id IN ($tids)
           and es.sent
           and es.scheduled_from_campaign

           and es.sent_at >= date_trunc('day', now() AT TIME ZONE (CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END)) - interval '1 week'
           and es.sent_at < date_trunc('day', now() AT TIME ZONE (CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END)) - interval '1 second'
           group by t.id;
       """*/

        val checkInboxAfterDate = DateTime.now().minusDays(AppConfig.inboxUnreadCountShowForDays)

        val fromWhereClause = if (from.isDefined) {
          sqls" and es.sent_at >= ${from.get} "
        } else {
          sqls"""
            and es.sent_at >= date_trunc('day', (now() + interval '1 day') AT TIME ZONE (
              CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END
            )) - interval '1 week'
            """
        }

        val tillWhereClause = if (till.isDefined) {
          sqls" and es.sent_at < ${till.get} "
        } else {
          sqls"""
            and es.sent_at < date_trunc('day', (now() + interval '1 day') AT TIME ZONE (
              CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END
            )) - interval '1 second'
            """
        }

        // FIXME VALUECLASS
        val inboxUnreadCountSQL: SQLSyntax = if (AppConfig.showInboxUnreadCountForOrgIds.contains(orgId.id)) {
          sqls"""
             SELECT COUNT(*) AS count FROM
              (
                  SELECT DISTINCT ON (et.id)
                    et.id
                  FROM email_threads et
                  INNER JOIN email_threads_prospects etp ON etp.email_thread_id = et.id
                  INNER JOIN prospects p ON p.id = etp.prospect_id
                  INNER JOIN prospect_categories_custom pcat ON pcat.id = p.prospect_category_id_custom
                  where
                    t.active
                    and et.latest_reply_at is not null
                    and not et.archived
                    and not et.sr_read
                    and et.latest_email_id is not null
                    AND et.team_id = t.id
                    and et.latest_reply_at > $checkInboxAfterDate
                    and pcat.text_id != ${ProspectCategory.DELIVERY_FAILED.toString}

                   LIMIT 50
               ) threads
            """
        } else {
          sqls" SELECT 0 as count "
        }

        val queryForPositiveReplySentiment = if (positive_reply_sentiments.isEmpty) {
          sqls"(SELECT 0) as total_positive_reply, "
        } else {
          sqls"""
                COUNT(DISTINCT (CASE WHEN et.reply_sentiment_uuid IN ${SQLUtils.generateSQLValuesClause(positive_reply_sentiments.map(_.uuid))} THEN es.prospect_id END)) AS total_positive_reply,
              """
        }

        val joinEmailThread = if (positive_reply_sentiments.isEmpty) {
          sqls""
        } else sqls"LEFT JOIN email_threads et on et.id = es.email_thread_id and et.team_id = t.id"


        // for error, only show longer-term (1 week+) errors to admin. No need to show 20-min timeout errors
        sql"""
          SELECT

            t.name as team,
            t.id as team_id,
            t.org_id as org_id,
            t.active as active,
            ($inboxUnreadCountSQL) as inbox_unread_count,

            (SELECT count(sc.*) from campaigns sc where sc.status in (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString}) and sc.team_id = t.id) as running_campaigns,

            (SELECT COUNT(totalta.*) FROM teams_accounts totalta WHERE totalta.team_id = t.id) AS total_members,

            (SELECT

              (CASE WHEN (send.paused_till is not null and send.paused_till > now() + interval '1 week') THEN send.error
                    WHEN (rec.paused_till is not null and rec.paused_till > now() + interval '1 week') THEN rec.error

                    WHEN (send.is_under_review is true OR send.is_under_review is true) THEN 'Your email account is under manual review. Please contact support.'

                    ELSE NULL END

              ) as error

              from campaigns c
              JOIN campaign_email_settings ces on ces.campaign_id = c.id and c.team_id = ces.team_id
              join email_settings send on send.id = ces.sender_email_setting_id
              join email_settings rec on rec.id = ces.receiver_email_setting_id

              where
              (
                (send.paused_till is not null and send.paused_till > now() + interval '1 week')
                OR
                (rec.paused_till is not null and rec.paused_till > now() + interval '1 week')
              )
              and c.status IN (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString}, ${CampaignStatus.SCHEDULED.toString})
              and c.account_id = ${aid.id}
              and c.team_id = t.id
              and t.active
              LIMIT 1

            ) AS error,


            COUNT(DISTINCT (CASE WHEN es.sent IS TRUE THEN es.prospect_id END)) AS total_sent,
            COUNT(DISTINCT es.prospect_id) FILTER (WHERE es.opened AND c.open_tracking_enabled) AS total_opened,
            COUNT(DISTINCT (CASE WHEN es.bounced IS TRUE THEN es.prospect_id END)) AS total_bounced,
            COUNT(DISTINCT (CASE WHEN es.clicked IS TRUE THEN es.prospect_id END)) AS total_clicked,
            COUNT(DISTINCT (CASE WHEN es.replied IS TRUE THEN es.prospect_id END)) AS total_replied,
            $queryForPositiveReplySentiment
            COUNT(DISTINCT (CASE WHEN es.opted_out IS TRUE THEN es.prospect_id END)) AS total_opted_out

          FROM teams t
          JOIN accounts a on a.id = ${aid.id}
          LEFT JOIN emails_scheduled es on (
            t.id = es.team_id
            and es.sent
            and es.scheduled_from_campaign

          $fromWhereClause

          $tillWhereClause

          )
          LEFT JOIN campaigns c on c.id = es.campaign_id and es.team_id = c.team_id
          $joinEmailThread

          WHERE t.id IN (${tids.map(_.id)})
            AND t.org_id = ${orgId.id}
          GROUP BY t.id
          ;
        """
          .map(ReportTeamStats.fromDb)
          .list
          .apply()
    }


    val activeTeamsSortedAlphabetically = teams.filter(_.active).sortBy(_.team.toLowerCase.trim)
    val inactiveTeamsSortedAlphabetically = teams.filterNot(_.active).sortBy(_.team.toLowerCase.trim)

    activeTeamsSortedAlphabetically ++ inactiveTeamsSortedAlphabetically
  }

  // FIXME: CHECK WEEKLY EMAILS GOING ON TIME AND MONDAY
  /*def getEmailSummaryAccountIdsforReport(email_notification_summary: String): Try[Seq[EmailSummaryAccount]] = Try {
    DB readOnly {
      implicit session => {

        val sentAtParam: SQLSyntax = if (email_notification_summary == "daily") sqls"now() - interval '23 hours'" else sqls"now() - interval '6 days'"

        val weeklyDayQuery: SQLSyntax = if (email_notification_summary == "weekly") sqls" AND extract(dow from (current_timestamp at time zone (CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END))::timestamp) = 1" else sqls""

        sql"""
        select a.id as account_id,
          a.org_role,
          o.is_agency,
          (select exists(select * from teams_accounts tacheck where tacheck.account_id = a.id and tacheck.role = 'admin') as is_team_admin)
          from accounts a
          join organizations o on o.id = a.org_id
          WHERE a.email_notification_summary = $email_notification_summary
          AND (a.last_summary_email_sent_at IS NULL OR a.last_summary_email_sent_at <  $sentAtParam)
          AND CAST(current_timestamp at time zone (CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END) AS time) BETWEEN '08:30' AND '10:30'

          $weeklyDayQuery
         """
          .map(EmailSummaryAccount.fromDb)
          .list
          .apply()
      }
    }
  }*/

  def getEmailSummaryAccountIdsforReport(email_notification_summary: String): Try[Seq[AccountId]] = Try {
    DB readOnly {
      implicit session => {

        sql"""

          select distinct a.id as account_id

          from accounts a

          INNER JOIN teams_accounts ta ON ta.account_id = a.id
          INNER JOIN teams t ON t.id = ta.team_id
          JOIN organizations org on org.id = a.org_id

          WHERE a.email_notification_summary = 'weekly'
          AND (a.last_summary_email_sent_at IS NULL OR a.last_summary_email_sent_at <  now() - interval '6 days')
          AND CAST(current_timestamp at time zone (CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END) AS time) BETWEEN '08:30' AND '10:30'
          AND ((org.id != ${AppConfig.orgToSurpassSomeChecksInGetAccForReport} AND extract(dow from (current_timestamp at time zone (CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END))::timestamp) = 1
              ) OR (
            org.id = ${AppConfig.orgToSurpassSomeChecksInGetAccForReport} AND extract(dow from (current_timestamp at time zone (CASE WHEN a.timezone IS NOT NULL THEN a.timezone ELSE 'UTC' END))::timestamp) = 3
            )
            )
          AND (
              t.active
              OR (
                  t.active = FALSE
                  AND t.deleted_at IS NULL
                 )
              )
          AND a.active
          AND org.active
          AND org.plan_type != ${PlanType.INACTIVE.toString}
          ;

        """
          .map(rs => AccountId(id = rs.long("account_id")))
          .list
          .apply()
      }
    }
  }

  def updateLastEmailSummarySentAt(aid: AccountId): Try[Option[AccountId]] = Try {
    DB autoCommit { implicit session =>
      sql"""
          UPDATE accounts
          SET last_summary_email_sent_at = now()
          WHERE id = ${aid.id}
          RETURNING id;
      """
        .map(rs => AccountId(id = rs.long("id")))
        .single
        .apply()

    }

  }




  ////////// START: Email Reputation Related /////////




  def getRepTrackingHosts(): Try[Seq[RepTrackingHosts]] = Try {

    DB readOnly {
      implicit session =>
        sql"""select * from rep_tracking_hosts"""
          .map(rs => RepTrackingHosts(
            id = rs.int("id"),
            host_url = rs.string("host_url"),
            subdomain_based = rs.boolean("subdomain_based"),
            active = rs.boolean("active")
          ))
          .list
          .apply()
    }

  }

  ////////// END: Email Reputation Related /////////


  def terminateBacklogDBConnections(): Try[Seq[DBConnections]] = Try {

    DB autoCommit { implicit session =>

      sql"""
           WITH inactive_connections AS (
            SELECT
               pid,
               state,
               rank() over (partition by client_addr order by backend_start ASC) as rank,
               application_name, datname,query,
               (current_timestamp - state_change) as running_since
               FROM pg_stat_activity
               WHERE pid <> pg_backend_pid( )
                 AND application_name !~ '(?:psql)|(?:pgAdmin.+)'
                 AND query not like 'autovacuum:%'
                 AND usename = current_user
                 AND state = 'active'
                 AND current_timestamp - state_change > interval '120 minutes'
                 AND query not ilike 'SET application_name%'

                 AND query not ilike '%create index concurrently%'
             )
             SELECT pg_terminate_backend(pid), *
               FROM inactive_connections
               WHERE rank > 1 -- Leave one connection for each application connected to the database;
         """
        .map(DBConnections.fromDb)
        .list
        .apply()
    }

  }


  def switchSupportAccess(
                           accountId: AccountId,
                           status: Boolean
                         )(implicit session: DBSession): Try[Option[AccountId]] = Try {
      sql"""
            UPDATE accounts
            SET access_to_inbox_given = $status
            WHERE id = ${accountId.id}
            RETURNING id;
  """
        .map(rs => AccountId(id = rs.long("id")))
        .single
        .apply()
  }

  def __insertDefaultCategories(
                                 teamId: TeamId,
                                 orgId: OrgId,
                                 accountId: AccountId,
                                 addNewCategories: Boolean,
                                 accountName: String
                               )  = {

    var parameters = List[Any]()

    val valuePlaceholder = if(!addNewCategories) {
      val displayNameOfDefaultCategories: Map[ProspectCategory.Value, ProspectCategoryDisplyNameColor] = ProspectCategory.displayNamesOfDefaultCategories

      displayNameOfDefaultCategories.map(c => {
        val cate = ProspectCategory.getProspectCategoryDisplyNameAndId(cate = c._1)

        val isEditable = if (c._1 == ProspectCategory.INTERESTED
          || c._1 == ProspectCategory.NOT_INTERESTED
          || c._1 == ProspectCategory.NOT_NOW
          || c._1 == ProspectCategory.CONVERTED) {

          true

        } else {

          false

        }

        parameters = parameters ::: List(

          cate.displayName,
          c._1.toString,
          cate.color,
          accountId.id,
          accountName,
          teamId.id,
          cate.prospectCategoryRank.rank,
          isEditable
        )

        s"""
              (

                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?
              )

            """
      }).mkString(", ")

    } else {
      val displayNameOfDefaultCategories: Map[ProspectCategoryNew.Value, ProspectCategoryDisplyNameColor] = ProspectCategoryNew.displayNamesOfDefaultCategories

      displayNameOfDefaultCategories.map(c => {
        val cate = ProspectCategoryNew.getProspectCategoryDisplyNameAndId(cate = c._1)

        val isEditable = false

        parameters = parameters ::: List(

          cate.displayName,
          c._1.toString,
          cate.color,
          accountId.id,
          accountName,
          teamId.id,
          cate.prospectCategoryRank.rank,
          isEditable
        )

        s"""
              (

                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?
              )

            """
      }).mkString(", ")

    }

    SQL(s"""
          INSERT INTO prospect_categories_custom
          (
             name,
             text_id,
             label_color,
             account_id,
             account_name,
             team_id,
             prospect_category_rank,
             is_custom

          )
          VALUES $valuePlaceholder
          ON CONFLICT DO NOTHING
          RETURNING *;
      """)
      .bind(parameters*)

  }

  def insertDefaultCategories(
                               teamId: TeamId,
                               orgId: OrgId,
                               accountId: AccountId,
                               addNewCategories: Boolean,
                               accountName: String
                             )
                             (implicit session: DBSession): Try[Seq[ProspectCategoriesInDB]] = Try {

    __insertDefaultCategories(
      teamId = teamId,
      orgId = orgId,
      accountId = accountId,
      addNewCategories = addNewCategories,
      accountName = accountName
    )
      .map(rs => ProspectCategoriesInDB.fromDb(rs))
      .list
      .apply()

  }


  def getSignupTypeForAccount(email: String): Try[Option[SignupType]] = Try{
    DB readOnly{ implicit session =>
      sql"""
           SELECT signup_type
           FROM accounts
           WHERE lower(email) = ${email.toLowerCase().trim}
         """
        .map{rs => SignupType.fromKey(rs.string("signup_type"))}
        .single
        .apply()
        .map(_.get)
    }
  }

  def getUserRoleIdForRoleInOrg(org_id: OrgId): Try[List[RoleAndUserRoleId]] = Try {
    DB readOnly { implicit session =>
      sql"""
           select id, role_name from user_roles where org_id = ${org_id.id} order by id;
         """
        .map(RoleAndUserRoleId.fromDb)
        .list
        .apply()
    }
  }
  
  def getWhenTheLastSpamReviewEmailWasSent(
                                          accountId: AccountId
                                          ): Try[Option[DateTime]] = Try{

    DB readOnly { implicit session =>
      sql"""
           select spam_monitor_email_last_sent_at
            from accounts
             where id = ${accountId.id};
         """
        .map(rs => rs.jodaDateTimeOpt("spam_monitor_email_last_sent_at"))
        .single
        .apply()
        .flatten
    }
    
  }

  def getEmailsOfTeamMembers(teamId: TeamId): Try[List[String]] = Try{
    DB readOnly { implicit session =>

      sql"""
           SELECT acc.email from accounts acc
           INNER JOIN teams_accounts ta on ta.account_id = acc.id

           WHERE ta.team_id = ${teamId.id};
         """
        .map(_.string("email"))
        .list
        .apply()
    }
  }

  def getEmailOfOrgOwner(org_id: OrgId): Try[String] = Try{
    DB readOnly { implicit session =>

      sql"""
           SELECT a.email FROM accounts a
           INNER JOIN organizations o on o.owner_account_id=a.id

           WHERE o.id = ${org_id.id};
         """
        .map(_.string("email"))
        .single
        .apply() match {
        case None => throw new Exception("Owner email not found")
        case Some(email) => email
      }
    }
  }

  def getAccountNameById(accId: AccountId): Try[Option[String]] = Try {
    DB readOnly { implicit session =>
      sql"""
            SELECT CONCAT(a.first_name,' ',a.last_name) as account_name from accounts a 
            where a.id = ${accId.id};         
         """
        .map(rs => rs.string("account_name"))
        .single
        .apply()

    }
  }


}

object AccountDAO {

  def fromDbTeamsListing(rs: WrappedResultSet): Seq[TeamAccount] = {
    Json.parse(rs.any("teams").asInstanceOf[PGobject].getValue).validate[Seq[TeamAccount]].get
  }
  
  def conditionToGetAllExistingTeams(): SQLSyntax = {
    sqls"""
            (
              t.active 
              OR 
              (
                t.active = false 
                AND 
                t.deleted_at IS NULL
              )
            )
          """
  }
}


