package api.accounts

import api.accounts.dao.TeamsDAO
import api.accounts.models.{AccountId, OrgId}
import api.{AppConfig, CONSTANTS}
import api.accounts.email.models.{EmailProvidedBy, EmailServiceProvider}
import api.accounts.service.ResetUserCacheUtil
import api.campaigns.services.CalendlyWebhookService
import api.email_infra_integrations.models.PurchaseDomainsAndEmailsStatus
import api.emails.models.EmailSettingIntegrationLogsStage
import api.emails.services.{CreateEmailViaOauthError, EmailAccountService, EmailAccountTestService}
import api.emails.{EmailSetting, EmailSettingCreateViaOAuth, EmailSettingDAO}
import api.free_email_domain.service.FreeEmailDomainListService
import api.integrations.services.TIntegrationCRMService
import api.integrations.{Calend<PERSON><PERSON><PERSON>, GetAccessTokenError, GetCR<PERSON>sers<PERSON>rror, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IntegrationTPAccessTokenResponse, PipedriveOAuth, SalesforceOAuth, Zoh<PERSON>OAuth, ZohoRecruitOAuth}
import api.triggers.{CheckLimitAndAddOrUpdateCRMError, IntegrationType, WorkflowCrmSettingsService}
import api.triggers.dao.WorkflowCrmSettingsDAO
import com.fasterxml.jackson.annotation.JsonValue
import org.joda.time.DateTime
import play.api.libs.json.{JsValue, Json}
import play.api.libs.ws.WSClient
import utils.email.{GmailApiReplyTrackingService, GmailApiSendEmailService}
import utils.security.EncryptionHelpers
import utils.{Helpers, ParseUtils, SRAppConfig, SRLogger}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

sealed trait GetOAuthUrlError

object GetOAuthUrlError {
  case object EmailEitherSendOrReceiveError extends GetOAuthUrlError
  case object EmailSettingNotFound extends GetOAuthUrlError
  case object FreeDomainUseError extends GetOAuthUrlError
  case object GSuiteDomainNotInstalled extends GetOAuthUrlError
  case object MultipleCrmIntegrationError extends GetOAuthUrlError
  case object UpgradeSubscription extends GetOAuthUrlError
  case object InvalidServiceProvider extends GetOAuthUrlError
  case class ServerError(err: Throwable) extends  GetOAuthUrlError
}

case class GetOAuthUrlResponse(
                                message: String,
                                redirect_to: String
                              )

sealed trait OAuthFetchTokensError

object OAuthFetchTokensError {
  case class BadRequestError(message: String) extends OAuthFetchTokensError
  case class ServerError(message: String, error: Option[Throwable]) extends OAuthFetchTokensError
}

case class OAuthFetchTokensResponse(
                                     message: String,
                                     data: JsValue
                                   )
class SocialAuthService(
                         freeEmailDomainListService: FreeEmailDomainListService,
                         emailSettingDAO: EmailSettingDAO,
                         googleOAuth: GoogleOAuth,
                         microsoftOAuth: MicrosoftOAuth,
                         workflowCrmSettingsDAO: WorkflowCrmSettingsDAO,
                         hubspotOAuth: HubSpotOAuth,
                         zohoOAuth: ZohoOAuth,
                         zohoRecruitOAuth: ZohoRecruitOAuth,
                         pipedriveOAuth: PipedriveOAuth,
                         emailAccountTestService: EmailAccountTestService,
                         salesforceOAuth: SalesforceOAuth,
                         tIntegrationCRMService: TIntegrationCRMService,
                         workflowCrmSettingsService: WorkflowCrmSettingsService,
                         resetUserCacheUtil: ResetUserCacheUtil,
                         gmailApiSendEmailService: GmailApiSendEmailService,
                         accountService: AccountService,
                         emailAccountService: EmailAccountService,
                         calendlyOAuth: CalendlyOAuth,
                         teamDAO: TeamsDAO,
                         calendlyWebhookService: CalendlyWebhookService
                       ) {

  private val gauthStateDelimiter = "___"

  // TODO: decryption of state should come independent of isZapmailFlow flag
  def _deconstructGAuthState(state: String, isZapmailFlow: Boolean): OAuthState = {
    // Decrypt the state first
    val decryptedState = if (isZapmailFlow) {
      EncryptionHelpers.decryptEmailSettingCredential(state)
    } else state

    // REF: https://stackoverflow.com/a/********
    val splits = decryptedState.split(gauthStateDelimiter, -1).toVector

    val aid = ParseUtils.parseLong(splits(0)).get
    val tid = ParseUtils.parseLong(splits(1)).get
    val serviceProvider = splits(2)
    val cid = if (splits(3).isEmpty) None else ParseUtils.parseLong(splits(3))
    val emailType = if (splits(4).isEmpty) None else Some(splits(4))
    val googleApiKeyId = if (splits(5).isEmpty) None else ParseUtils.parseInt(splits(5))
    val campaign_basic_setup = if (splits(6).isEmpty) None else ParseUtils.parseBool(splits(6))
    val is_sandbox = if (splits(7).isEmpty) false else ParseUtils.parseBool(splits(7)).getOrElse(false)
    val is_inbox = if (splits(8).isEmpty) false else ParseUtils.parseBool(splits(8)).getOrElse(false)
    val goto_quickstart = if (splits(9).isEmpty) false else ParseUtils.parseBool(splits(9)).getOrElse(false)

    OAuthState(
      campaignId = cid,
      emailType = emailType,
      aid = aid,
      tid = tid,
      serviceProvider = serviceProvider,
      googleApiKeyId = googleApiKeyId,
      campaign_basic_setup = campaign_basic_setup,
      is_sandbox = is_sandbox,
      is_inbox = is_inbox,
      goto_quickstart = goto_quickstart
    )

  }

  // TODO: encryption of state should come independent of isZapmailFlow flag
  def _createAuthState(stateData: OAuthState, isZapmailFlow: Boolean): String = {

    val s = stateData

    // pass the campaignId and teamId to google url state
    val cid = s.campaignId.getOrElse("").toString

    val gap = gauthStateDelimiter

    val unencryptedState = s.aid.toString +
      gap + s.tid.toString +
      gap + s.serviceProvider +
      gap + cid +
      gap + s.emailType.getOrElse("") +
      gap + s.googleApiKeyId.getOrElse("").toString +
      gap + s.campaign_basic_setup.getOrElse("").toString +
      gap + s.is_sandbox +
      gap + s.is_inbox +
      gap + s.goto_quickstart
    // is_inbox flag is used to redirect back to inbox page once email is integrated
    /*
    1 July 2024 goto_quickstart flag to redirect to quickstart page after email is integrated successfully
     */

    // Encrypt the state before returning
    if (isZapmailFlow) {
      EncryptionHelpers.encryptEmailSettingCredential(unencryptedState)
    } else unencryptedState
  }

  def getOAuthUrl(
                   email_type: Option[String],
                   email_setting_id: Option[Long],
                   emailSettingOpt: Option[EmailSetting],
                   service_provider: String,
                   emailAddress: Option[String],
                   campaign_id: Option[Long],
                   teamMember: TeamMember,
                   email_address: Option[String],
                   confirm_install: Option[Boolean] = Some(false),
                   campaign_basic_setup: Option[Boolean] = Some(false),
                   is_sandbox: Option[Boolean] = Some(false),
                   is_inbox: Option[Boolean] = Some(false),
                   goto_quickstart: Option[Boolean] = Some(false),
                   org: OrganizationWithCurrentData,
                   teamId: TeamId,
                   accountId: AccountId,
                   allowCRMIntegration: Boolean,
                   isZapmailFlow: Boolean
                 )(using Logger:SRLogger,ws: WSClient,ec: ExecutionContext):Either[GetOAuthUrlError,GetOAuthUrlResponse] = {
    var error_occurred: Option[String] = None
    val result = if (email_type.isDefined && email_type.get != "send" && email_type.get != "receive") {

      error_occurred = Some("email_type must be either 'send' or 'receive'")
      Left(GetOAuthUrlError.EmailEitherSendOrReceiveError)
    } else if (email_setting_id.isDefined && emailSettingOpt.isEmpty) {

      error_occurred = Some("email setting not found, please contact support")
      Left(GetOAuthUrlError.EmailSettingNotFound)

    } else {

      //TODO: here checkIfFreeEmailService uses .get here because if service provider is outlook ,then in the current case email addreess is None,
      // need to fix this in a better way
      if (service_provider == "google" &&
        emailAddress.isDefined &&
        freeEmailDomainListService.checkIfFreeEmailService(email = emailAddress.get.trim.toLowerCase).get) {
        Logger.fatal(s"getOAuthUrl google $emailAddress not supported: account: (${teamMember.user_id} : ${teamMember.email}")

        error_occurred = Some("Please provide a work email . Personal email addresses are not allowed")
        Left(GetOAuthUrlError.FreeDomainUseError)

      } else if (
        service_provider == "google" &&
          email_address.isDefined &&
          (confirm_install.isEmpty || !confirm_install.get) &&
          !emailSettingDAO.isGsuiteDomainInstalled(email_address.get, teamId = teamId.id)
      ) {

        error_occurred = Some("Your G Suite domain is not installed. Please follow the instructions to install")
        Left(GetOAuthUrlError.GSuiteDomainNotInstalled)

      } else if (service_provider == "google") {


        val allowed_for_new_google_api_key = org.org_metadata.allowed_for_new_google_api_key.getOrElse(false)


        //: Try[(RepGoogleAPIKey, GoogleOauthSettings)]
        val googleApiKeyAndSettingsTry = if (allowed_for_new_google_api_key) { // always take new api key and new scopes

          val googleOauthSettingsForNewKey = if (isZapmailFlow)
            AppConfig.googleOAuthSettingsForNewKey.copy(
              redirectURL = AppConfig.zapmailRedirectUrl
            )
          else AppConfig.googleOAuthSettingsForNewKey

          emailSettingDAO.getGoogleAuthKeysForOrg(orgId = org.id)
            .map(apiKey => (apiKey, googleOauthSettingsForNewKey))

        } else if (emailSettingOpt.isDefined &&
          /*
            07-October-2024 :
            Added a one more check of service_provider (for reconnecting flow)
            Reason : When we integrate the email account using gmail_asp the rep_google_api_key_id in email_settings is set to 1 by default
            hence while reconnecting via aoauth this rep_google_api_key_id is not the desired one we want so hence we have to fetch it
            from the organizations table where it is 6 (which we want for oAuth) so due to this service_provider check
            now if the service_provider of the email_setting is GMAIL_ASP it will fetch the rep_google_api_key_id from the organizations
            else if it is GMAIL_API it will fetch from the email_settings.
             */
          !emailSettingOpt.map(_.service_provider).contains(EmailServiceProvider.GMAIL_ASP)) { // while reconnect take migration API key

          val googleOauthSettings = if (isZapmailFlow)
            AppConfig.googleOAuthSettings.copy(
              redirectURL = AppConfig.zapmailRedirectUrl
            )
          else AppConfig.googleOAuthSettings

          emailSettingDAO.getGoogleAuthKeysForEmailSetting(
              emailSettingId = emailSettingOpt.get.id.get.emailSettingId, // FIXME VALUECLASS
              forReconnect = true
            )
            .map(apiKey => (apiKey, googleOauthSettings))

        } else {

          /*
          07-October-2024 :
          If the user has integrated an email with gmail_asp and now he wants it to switch from gmail_asp to gmail_Oauth(gmail_api)
          then it will come to this else block so that it will fetch the google_client_id from the organization table instead of email_Settings table
           */

          val googleOauthSettings = if (isZapmailFlow)
            AppConfig.googleOAuthSettings.copy(
              redirectURL = AppConfig.zapmailRedirectUrl
            ) else AppConfig.googleOAuthSettings

          emailSettingDAO.getGoogleAuthKeysForOrg(orgId = org.id)
            .map(apiKey => (apiKey, googleOauthSettings))

        }


        googleApiKeyAndSettingsTry match {

          case Failure(e) =>

            Logger.fatal(s"google getOAuthUrl: aid ${accountId.id} :: tid ${teamId.id} :: sp $service_provider ")

            error_occurred = Some("There was an error, please try again or contact support" + e.getMessage)
            Left(GetOAuthUrlError.ServerError(e))

          case Success((googleApiKeyId, googleOauthSettings)) =>

            // pass the campaignId / teamId to google url state
            val state = _createAuthState(stateData = OAuthState(
              campaignId = campaign_id,
              emailType = email_type,
              aid = accountId.id,
              tid = teamId.id,
              serviceProvider = service_provider,
              googleApiKeyId = Some(googleApiKeyId.id),
              campaign_basic_setup = campaign_basic_setup,
              is_sandbox = is_sandbox.getOrElse(false),
              is_inbox = is_inbox.getOrElse(false),
              goto_quickstart = goto_quickstart.getOrElse(false)
            ),
              isZapmailFlow = isZapmailFlow
            )


            val authorizationUrl = googleOAuth.authorizationUrl(
              state = state,
              gcpClientId = googleApiKeyId.cl_id,
              emailAddress = emailAddress,
              forceApproval = true,
              goauthSettings = googleOauthSettings
            )

            Right(GetOAuthUrlResponse(
              message = "Redirect user to Google auth page",
              redirect_to = authorizationUrl
            ))

        }


      } else if (service_provider == "outlook") {


        // pass the campaignId / teamId to google url state
        val state = _createAuthState(stateData = OAuthState(
          campaignId = campaign_id,
          emailType = email_type,
          aid = accountId.id,
          tid = teamId.id,
          serviceProvider = service_provider,
          googleApiKeyId = None,
          campaign_basic_setup = campaign_basic_setup,
          is_sandbox = is_sandbox.getOrElse(false),
          is_inbox = is_inbox.getOrElse(false),
          goto_quickstart = goto_quickstart.getOrElse(false)
        ),
          isZapmailFlow = isZapmailFlow
        )

        // Use AppConfig.microsoftOAuthSettings, for SocialAuth
        val authorizationUrl = microsoftOAuth.authorizationUrl(
          state = state,
          s = if (isZapmailFlow) SRAppConfig.microsoftOAuthSettings.copy(
            redirectURL = AppConfig.zapmailRedirectUrl
          ) else SRAppConfig.microsoftOAuthSettings,
          emailAddress = emailAddress,
          promptLogin = true)

        Right(GetOAuthUrlResponse(
          message = "Redirect user to Microsoft auth page",
          redirect_to = authorizationUrl
        ))

      }else if (service_provider == "calendly"){
          val state = _createAuthState(stateData = OAuthState(
              campaignId = campaign_id,
              emailType = email_type,
              aid = accountId.id,
              tid = teamId.id,
              serviceProvider = service_provider,
              googleApiKeyId = None,
              campaign_basic_setup = None,
              is_sandbox = is_sandbox.getOrElse(false),
              is_inbox = is_inbox.getOrElse(false),
              goto_quickstart = goto_quickstart.getOrElse(false)
          ),
            isZapmailFlow = false
          )

          val authorizationUrl = calendlyOAuth.getAuthorizationUrl(
              state = state
              //              s = SRAppConfig.microsoftOAuthSettings,
              //              emailAddress = emailAddress,
              //              promptLogin = true
          )

          Right(GetOAuthUrlResponse(
              message = "Redirect user to Calendly auth page",
              redirect_to = authorizationUrl
          ))
      }
      else {
        workflowCrmSettingsDAO.getAllIntegrationsForTeam(
          teamId = teamId.id
        ) match {
          case Failure(err) => Left(GetOAuthUrlError.ServerError(err))

          case Success(integrations) =>
            //adding a check for more than 1 CRM
            //checking if the integrations for the given team is more than 1
            // and if so, checking if the list has the integration that we are getting from the frontend (reconnect case)
            // we let them pass if its reconnect
            if (integrations.nonEmpty && !integrations.contains(IntegrationType.withName(service_provider).get) && teamId.id != AppConfig.heaplabsTeamId) {
              Left(GetOAuthUrlError.MultipleCrmIntegrationError)
            } else {
              if (service_provider == "hubspot") {

                if (!allowCRMIntegration) {

                  Left(GetOAuthUrlError.UpgradeSubscription)

                } else {

                  // pass the campaignId / teamId to url state
                  val state = _createAuthState(stateData = OAuthState(
                    campaignId = campaign_id,
                    emailType = email_type,
                    aid = accountId.id,
                    tid = teamId.id,
                    serviceProvider = service_provider,
                    googleApiKeyId = None,
                    campaign_basic_setup = None,
                    is_sandbox = is_sandbox.getOrElse(false),
                    is_inbox = is_inbox.getOrElse(false),
                    goto_quickstart = goto_quickstart.getOrElse(false)
                  ), isZapmailFlow = false)

                  val authorizationUrl = hubspotOAuth.authorizationUrl(state)

                  Right(GetOAuthUrlResponse(
                    message = "Redirect user to HubSpot auth page",
                    redirect_to = authorizationUrl
                  ))

                }
              } else if (service_provider == "zoho") {


                if (!allowCRMIntegration) {

                  Left(GetOAuthUrlError.UpgradeSubscription)

                } else {

                  // pass the campaignId / teamId to url state
                  val state = _createAuthState(stateData = OAuthState(
                    campaignId = campaign_id,
                    emailType = email_type,
                    aid = accountId.id,
                    tid = teamId.id,
                    serviceProvider = service_provider,
                    googleApiKeyId = None,
                    campaign_basic_setup = None,
                    is_sandbox = is_sandbox.getOrElse(false),
                    is_inbox = is_inbox.getOrElse(false),
                    goto_quickstart = goto_quickstart.getOrElse(false)
                  ), isZapmailFlow = false)

                  val authorizationUrl = zohoOAuth.authorizationUrl(state)
                  Right(GetOAuthUrlResponse(
                    message = "Redirect user to Zoho auth page",
                    redirect_to = authorizationUrl
                  ))
                }
              } else if (service_provider == "zoho_recruit") {


                if (!allowCRMIntegration) {

                  Left(GetOAuthUrlError.UpgradeSubscription)

                } else {

                  // pass the campaignId / teamId to url state
                  val state = _createAuthState(stateData = OAuthState(
                    campaignId = campaign_id,
                    emailType = email_type,
                    aid = accountId.id,
                    tid = teamId.id,
                    serviceProvider = service_provider,
                    googleApiKeyId = None,
                    campaign_basic_setup = None,
                    is_sandbox = is_sandbox.getOrElse(false),
                    is_inbox = is_inbox.getOrElse(false),
                    goto_quickstart = goto_quickstart.getOrElse(false)
                  ), isZapmailFlow = false)

                  val authorizationUrl = zohoRecruitOAuth.authorizationUrl(state)

                  Right(GetOAuthUrlResponse(
                    message = "Redirect user to Zoho Recruit auth page",
                    redirect_to = authorizationUrl
                  ))
                }
              } else if (service_provider == "pipedrive") {


                if (!allowCRMIntegration) {

                  Left(GetOAuthUrlError.UpgradeSubscription)

                } else {
                  // pass the campaignId / teamId to url state
                  val state = _createAuthState(stateData = OAuthState(
                    campaignId = campaign_id,
                    emailType = email_type,
                    aid = accountId.id,
                    tid = teamId.id,
                    serviceProvider = service_provider,
                    googleApiKeyId = None,
                    campaign_basic_setup = None,
                    is_sandbox = is_sandbox.getOrElse(false),
                    is_inbox = is_inbox.getOrElse(false),
                    goto_quickstart = goto_quickstart.getOrElse(false)
                  ), isZapmailFlow = false)

                  val authorizationUrl = pipedriveOAuth.authorizationUrl(state)

                  Right(GetOAuthUrlResponse(
                    message = "Redirect user to Pipedrive auth page",
                    redirect_to = authorizationUrl
                  ))
                }
              } else if (service_provider == "salesforce") {


                // TODO: there will be a different check for Salesforce
                if (!allowCRMIntegration) {

                  Left(GetOAuthUrlError.UpgradeSubscription)

                } else {

                  // pass the campaignId / teamId to url state
                  val state = _createAuthState(stateData = OAuthState(
                    campaignId = campaign_id,
                    emailType = email_type,
                    aid = accountId.id,
                    tid = teamId.id,
                    serviceProvider = service_provider,
                    googleApiKeyId = None,
                    campaign_basic_setup = None,
                    is_sandbox = is_sandbox.getOrElse(false),
                    is_inbox = is_inbox.getOrElse(false),
                    goto_quickstart = goto_quickstart.getOrElse(false)
                  ), isZapmailFlow = false)

                  val authorizationUrl = salesforceOAuth.authorizationUrl(state, is_sandbox = is_sandbox.getOrElse(false))

                  Right(GetOAuthUrlResponse(
                    message = "Redirect user to Salesforce auth page",
                    redirect_to = authorizationUrl
                  ))
                }
              } else {
                error_occurred = Some("Invalid Service Provider")
                Left(GetOAuthUrlError.InvalidServiceProvider)
              }
            }
        }
      }
    }

    result match {
      case Left(err) =>
        if(error_occurred.isDefined){
          emailAccountTestService.insertEmailSettingIntegrationLogs(
            accountId = accountId,
            orgId = OrgId(org.id),
            teamId = TeamId(teamMember.team_id),
            email_address = emailAddress,
            service_provider = Some(if (service_provider == "google") EmailServiceProvider.GMAIL_API else if (service_provider == "outlook") EmailServiceProvider.OUTLOOK_API else EmailServiceProvider.OTHER),
            completed = false,
            error_occurred = error_occurred,
            email_integration_stage = EmailSettingIntegrationLogsStage.OAuthUrlGenerationError
          ) match {
            case Failure(exception) =>
              Logger.error(s"Error occurred while inserting the Email Setting Integration logs for service provider: ${service_provider}", err = exception)
            case Success(value) =>
              Logger.info("Email Integration logs inserted successfully")
          }
        }
        Left(err)
      case Right(value) =>
        emailAccountTestService.insertEmailSettingIntegrationLogs(
          accountId = accountId,
          orgId = OrgId(org.id),
          teamId = TeamId(teamMember.team_id),
          email_address = emailAddress,
          service_provider = Some(if (service_provider == "google") EmailServiceProvider.GMAIL_API else if (service_provider == "outlook") EmailServiceProvider.OUTLOOK_API else EmailServiceProvider.OTHER),
          completed = false,
          error_occurred = None,
          email_integration_stage = EmailSettingIntegrationLogsStage.OAuthUrlGenerationSuccess
        ) match {
          case Failure(exception) =>
            Logger.error(s"Error occurred while inserting the Email Setting Integration logs for service provider: ${service_provider}", err = exception)
          case Success(value) =>
            Logger.info("Email Integration logs inserted successfully")
            value
        }
        Right(value)
    }
  }

  def __googleAuthFetchTokens(
                               code: String,
                               failedLogErrorPartial: String,
                               failedAuthorizationErrorMsg: String,
                               actingTeamAccount: TeamMember,
                               googleApiKeyId: Int,
                               isZapmailOauthFlow: Boolean,
                               Logger: SRLogger

                             )(implicit ec: ExecutionContext,
                               ws: WSClient): Future[(EmailSettingCreateViaOAuth, Seq[EmailSettingCreateViaOAuth])] = {

    val FAILED_AUTH_EXCEPTION = new Exception(failedAuthorizationErrorMsg)



    // if code is there as a query param in the url, then this a successful redirect from google oauth flow

    googleOAuth.getAccessToken(
      code = code,
      googleAuthApiKeyId = googleApiKeyId,
      signupType = None,
      isZapmailOauthFlow = isZapmailOauthFlow
    ).flatMap { access_token =>

      val accessTokenData = access_token

      val data = for {
        email <- googleOAuth.getProfileEmail(accessTokenData.access_token,Logger)
        names <- googleOAuth.getProfileName(accessTokenData.access_token,Logger)
        aliases <- Future.fromTry(gmailApiSendEmailService.getGmailServiceAlias(accessTokenData.access_token))
      } yield {
        (email, names, aliases)
      }


      data.map { case (email, names, aliases) => {

        if (accessTokenData.refresh_token.isEmpty ||
          accessTokenData.expires_in.isEmpty ||
          accessTokenData.token_type.isEmpty) {


          Logger.fatal(s"2: $failedLogErrorPartial")

          throw new Exception("Invalid Google Authorization, we did not get the required info from the auth request")

        } else {


          val parentEmailSettingForSaving = EmailSettingCreateViaOAuth(

            email = email,

            service_provider = EmailServiceProvider.GMAIL_API,

            sender_name = names.profileName,

            oauth2_access_token = accessTokenData.access_token,
            oauth2_refresh_token = accessTokenData.refresh_token.get,
            oauth2_token_type = accessTokenData.token_type.get,
            oauth2_token_expires_in = accessTokenData.expires_in.get,
            oauth2_access_token_expires_at = DateTime.now().plusSeconds(accessTokenData.expires_in.get),

            rep_google_api_key_id = Some(googleApiKeyId),
            rep_mail_server_id = None,
            rep_tracking_host_id = None,
            gsuite_domain_install = Some(true), // after Gsuite market place listing change it to true
            domain_provider = if (isZapmailOauthFlow) Some(EmailProvidedBy.ZapMail) else None,
            domain = Helpers.getDomainFromEmail(email).getOrElse(""), // This won't be None as email is valid
            email_purchase_status = if (isZapmailOauthFlow) Some(PurchaseDomainsAndEmailsStatus.ACTIVE) else None,
            quota_per_day = 100,

            alias_parent_id = None,

            first_name = names.givenName,
            last_name = names.familyName,

          )


          val aliasEmailSettingsForSaving = aliases
            .filter(alias => (alias.getIsPrimary == null || alias.getIsPrimary == false) && (alias.getVerificationStatus == "accepted"))
            .map(alias => {

              //                              Logger.info(s"\n\n ${alias.getIsPrimary } :: ${alias.getDisplayName} :: ${alias.getReplyToAddress} :: ${alias.getSendAsEmail} :: ${alias.getVerificationStatus}")


              val emailSettingAliasForSaving = EmailSettingCreateViaOAuth(

                email = alias.getSendAsEmail,

                service_provider = EmailServiceProvider.GMAIL_ALIAS,

                sender_name = alias.getDisplayName,

                oauth2_access_token = accessTokenData.access_token,
                oauth2_refresh_token = accessTokenData.refresh_token.get,
                oauth2_token_type = accessTokenData.token_type.get,
                oauth2_token_expires_in = accessTokenData.expires_in.get,
                oauth2_access_token_expires_at = DateTime.now().plusSeconds(accessTokenData.expires_in.get),

                rep_google_api_key_id = Some(googleApiKeyId),
                rep_mail_server_id = None,
                rep_tracking_host_id = None,
                gsuite_domain_install = Some(false),
                domain_provider = if (isZapmailOauthFlow) Some(EmailProvidedBy.ZapMail) else None,
                email_purchase_status = if (isZapmailOauthFlow) Some(PurchaseDomainsAndEmailsStatus.ACTIVE) else None,
                domain = Helpers.getDomainFromEmail(email).getOrElse(""), // This won't be None as email is valid
                quota_per_day = 100,

                alias_parent_id = None, // TO BE ADDED AFTER PARENT EMAIL SETTING SAVED IN MAIN FUNCTION

                first_name = alias.getDisplayName,
                last_name = "",

              )

              emailSettingAliasForSaving


            })

          (parentEmailSettingForSaving, aliasEmailSettingsForSaving)

        }


      }

      }


    }.recover{e =>
      Logger.fatal(s"Failed googleOAuth.getAccessToken", e)
      throw e
    }
  }

  def __outlookAuthFetchTokens(
                                code: String,
                                failedLogErrorPartial: String,
                                failedAuthorizationErrorMsg: String,
                                actingTeamAccount: TeamMember,
                                isZapmailOauthFlow: Boolean,
                                Logger: SRLogger

                              )(implicit ec: ExecutionContext,
                                ws: WSClient): Future[(EmailSettingCreateViaOAuth, Seq[EmailSettingCreateViaOAuth])] = {

    val FAILED_AUTH_EXCEPTION = new Exception(failedAuthorizationErrorMsg)


    // if code is there as a query param in the url, then this a successful redirect from google oauth flow
    microsoftOAuth.getAccessToken(
      code,
      Logger = Logger,
      s = if (isZapmailOauthFlow) SRAppConfig.microsoftOAuthSettings.copy(
        redirectURL = AppConfig.zapmailRedirectUrl
      ) else SRAppConfig.microsoftOAuthSettings // Use AppConfig.microsoftOAuthSettings, SocialAuth
    ).flatMap { access_token =>

      if (access_token.isEmpty) {

        Logger.fatal(s"1: $failedLogErrorPartial")
        Future.failed(FAILED_AUTH_EXCEPTION)

      } else {

        val accessTokenData = access_token.get


        val userProfileDataFuture = microsoftOAuth.getProfile(
          accessTokenData.access_token,
          Logger = Logger
        )


        userProfileDataFuture.map { case userProfileData => {

          /**
           * 7th Nov 22
           * Adding this condition to test user issue
           * key `mail` is comming as null whereas `userPrincipalName` is not null with same data
           * So testing if works (emails sends and tracks) should enable for all
           * */
          val userProfile = if (actingTeamAccount.team_id == AppConfig.outlook_userPrincipalName_allow_team_id && userProfileData.mail.isEmpty) {

            Logger.info(s"__outlookAuthFetchTokens `mail` empty using userPrincipalName here for team:${AppConfig.outlook_userPrincipalName_allow_team_id}  :: userPrincipalName:${userProfileData.userPrincipalName}")

            userProfileData.copy(
              mail = Some(userProfileData.userPrincipalName)
            )

          } else userProfileData


          val failedGetProfileErrorMsg = "We could not find a valid outlook mailbox in your account. Please contact support if you have any questions."
          val FAILED_GET_PROFILE_EXCEPTION = new Exception(failedGetProfileErrorMsg)

          if (accessTokenData.refresh_token.isEmpty ||
            accessTokenData.expires_in.isEmpty ||
            accessTokenData.token_type.isEmpty) {


            Logger.fatal(s"2: $failedLogErrorPartial")

            throw FAILED_AUTH_EXCEPTION

          } else if (userProfile.mail.isEmpty) {

            Logger.fatal(s"3: $failedGetProfileErrorMsg :: Outlook UserDetails: ${userProfile} :: SmartReach Account Id: ${actingTeamAccount.user_id}")

            throw FAILED_GET_PROFILE_EXCEPTION

          }
          else {

            val email = userProfile.mail.get

            if (userProfile.givenName.isEmpty || userProfile.surname.isEmpty) {
              Logger.warn(s"Outlook userProfile incomplete name : ${userProfile.givenName} , ${userProfile.surname}")
            }


            val parentEmailSettingForSaving = EmailSettingCreateViaOAuth(

              email = email,

              service_provider = EmailServiceProvider.OUTLOOK_API,

              sender_name = userProfile.displayName,

              oauth2_access_token = accessTokenData.access_token,
              oauth2_refresh_token = accessTokenData.refresh_token.get,
              oauth2_token_type = accessTokenData.token_type.get,
              oauth2_token_expires_in = accessTokenData.expires_in.get,
              oauth2_access_token_expires_at = DateTime.now().plusSeconds(accessTokenData.expires_in.get),

              rep_google_api_key_id = None,
              rep_mail_server_id = None,
              rep_tracking_host_id = None,
              gsuite_domain_install = None,
              domain_provider = if (isZapmailOauthFlow) Some(EmailProvidedBy.ZapMail) else None,
              email_purchase_status = if (isZapmailOauthFlow) Some(PurchaseDomainsAndEmailsStatus.ACTIVE) else None,
              domain = Helpers.getDomainFromEmail(email).getOrElse(""), // This won't be None as email is valid

              quota_per_day = 100,

              alias_parent_id = None,

              first_name = userProfile.givenName.getOrElse(""),
              last_name = userProfile.surname.getOrElse(""),

            )

            val aliasEmailSettingForSaving = Seq() // outlook aliases not supported

            (parentEmailSettingForSaving, aliasEmailSettingForSaving)

          }


        }

        }

      }


    }
  }


  def __fetchTPOAuthTokens(service_provider: IntegrationType,
                           code: String,
                           location: Option[String],
                           failedLogErrorPartial: String,
                           failedAuthorizationErrorMsg: String,
                           actingTeamAccount: TeamMember,
                           is_sandbox: Boolean
                          )(
                            implicit Logger: SRLogger,
                            ec: ExecutionContext,
                            ws: WSClient
                          ): Future[IntegrationTPAccessTokenResponse.FullTokenData] = {

    val FAILED_AUTH_EXCEPTION = new Exception(failedAuthorizationErrorMsg)
    // if code is there as a query param in the url, then this a successful redirect from pipedrive oauth flow
    tIntegrationCRMService.getAccessToken(service_provider, code, location, is_sandbox).flatMap {

      case Left(GetAccessTokenError.MalformedAccessTokenResponseError(message)) =>
        Logger.fatal(s"1: $message")
        Future.failed(new Exception(message))

      case Left(GetAccessTokenError.OAuthError(err)) =>
        Logger.fatal(s"2: $failedLogErrorPartial :: ${err.message}")
        Future.failed(FAILED_AUTH_EXCEPTION)

      case Right(accessTokenData) =>

        /**
         * calling the getTPColumns api as a confirmation to ensure we can access data correctly
         */
        tIntegrationCRMService.getTPColumns(integrationType = service_provider, accessTokenData = accessTokenData, module_type = None) //getTPColumns DOING here just check oauthService API is enabled for his account or not (to avoid errors Like Zoho CRM plus expired, SalesForce API DISABLED )
          .flatMap { tp_columns =>
            Future {
              accessTokenData
            }

          }
    }
  }

  def oauthFetchTokens(
                        codeOpt: Option[String],
                        serviceProvider: String,
                        accountId: AccountId,
                        teamId: TeamId,
                        state: Option[String],
                        location: Option[String],
                        teamMember: TeamMember,
                        is_sandbox: Boolean,
                        googleApiKeyId: Option[Int],
                        permittedAccountIds: Seq[Long],
                        orgId: OrgId,
                        campaignId: Option[Long],
                        emailType: Option[String],
                        isZapmailOauthFlow: Boolean
                      )(implicit Logger: SRLogger,
                        ec: ExecutionContext,
                        ws: WSClient): Future[Either[OAuthFetchTokensError,OAuthFetchTokensResponse]] = {

    var error_occurred: Option[String] = None
    var saved_email_address: Option[String] = None
    val result = codeOpt match {

      case None =>

        Future.successful(Left(OAuthFetchTokensError.BadRequestError("Invalid authorization code")))

      case Some(code) =>

        val failedAuthorizationErrorMsg = s"Failed $serviceProvider Authorization. Please retry or contact support."
        val failedLogErrorPartial = s"${serviceProvider}AuthFetchTokens [FATAL]: Failed: ${accountId.id} :: ${teamId.id} :: $state :: $code"

        // check if crm integration service provider
        val crmServiceProviderTry = IntegrationType.withName(name = serviceProvider)

        // if(serviceProvider == "hubspot" || serviceProvider == "zoho" || serviceProvider == "zoho_recruit" || serviceProvider == "pipedrive" || serviceProvider == "salesforce") {

        if (crmServiceProviderTry.isSuccess) {

          val crmServiceProvider = crmServiceProviderTry.get

          crmServiceProvider match {

            case IntegrationType.HUBSPOT |
                 IntegrationType.ZOHO |
                 IntegrationType.ZOHO_RECRUIT |
                 IntegrationType.PIPEDRIVE |
                 IntegrationType.SALESFORCE =>

              __fetchTPOAuthTokens(
                service_provider = crmServiceProvider,
                code = code,
                location = location,
                failedLogErrorPartial = failedLogErrorPartial,
                failedAuthorizationErrorMsg = failedAuthorizationErrorMsg,
                actingTeamAccount = teamMember,
                is_sandbox = is_sandbox
              ).flatMap {
                data => {

                  tIntegrationCRMService.getTPUsers(
                      integrationType = crmServiceProvider,
                      accessTokenData = data
                    )
                    .flatMap {

                      case Left(GetCRMUsersError.MalformedUsersResponseError(message)) =>

                        Future.successful(Left(OAuthFetchTokensError.ServerError(message = message, error = None)))

                      case Left(GetCRMUsersError.CommonCRMAPIError(err)) =>

                        Future.successful(Left(OAuthFetchTokensError.BadRequestError(err.message)))

                      case Right(tpUsers) =>

                        crmServiceProvider match {

                          case IntegrationType.PIPEDRIVE |
                               IntegrationType.HUBSPOT |
                               IntegrationType.ZOHO |
                               IntegrationType.ZOHO_RECRUIT |
                               IntegrationType.SALESFORCE =>

                            tIntegrationCRMService.me(
                                integrationType = crmServiceProvider,
                                accessTokenData = data)
                              .map(acc => {

                                workflowCrmSettingsService.checkLimitAndAddOrUpdateCRM(
                                  teamId = TeamId(teamMember.team_id),
                                  accountId = AccountId(teamMember.user_id),
                                  tokens = data,
                                  tp_company_id = if (acc.company_id.nonEmpty) Some(acc.company_id.get.toString) else None,
                                  tp_user_id = if (acc.id.nonEmpty) Some(acc.id.toString) else None,
                                  tp_owner_id = acc.owner_id,
                                  service_provider = crmServiceProvider,
                                  tp_users = tpUsers,
                                  is_sandbox = is_sandbox
                                ) match {

                                  case Left(CheckLimitAndAddOrUpdateCRMError.CRMIntegrationLimitReached) =>

                                    val errMsg = "You don't have enough CRM Integration credits left"

                                    Logger.error(
                                      msg = s"$errMsg. teamId: ${teamMember.team_id} :: accountId: ${teamMember.user_id} :: service_provider: $crmServiceProvider"
                                    )

                                    Left(OAuthFetchTokensError.BadRequestError(errMsg))

                                  case Left(CheckLimitAndAddOrUpdateCRMError.SQLException(e)) =>

                                    Logger.error(
                                      msg = s"Failed to connect CRM. teamId: ${teamMember.team_id} :: accountId: ${teamMember.user_id} :: service_provider: $crmServiceProvider",
                                      err = e,
                                    )

                                    Left(OAuthFetchTokensError.ServerError(failedAuthorizationErrorMsg, Some(e)))

                                  case Right(resOpt) =>

                                    resOpt match {

                                      case None =>
                                        resetUserCacheUtil._resetTeamCache(aid = teamMember.user_id)

                                        Left(OAuthFetchTokensError.ServerError(failedAuthorizationErrorMsg, error = None))

                                      case Some(res) =>

                                        resetUserCacheUtil._resetTeamCache(aid = teamMember.user_id)

                                        Right(OAuthFetchTokensResponse(s"Your $serviceProvider account successfully integrated", Json.obj()))
                                    }

                                }

                              })


                          case IntegrationType.SMARTREACH =>

                            Logger.fatal(s"SocialAuthController.oauthFetchTokens: Invalid crmIntegrationService: $crmServiceProvider")

                            Future.successful(Left(OAuthFetchTokensError.BadRequestError("Invalid CRM Type")))
                        }
                    }

                }
              }.recover { case e =>

                Left(OAuthFetchTokensError.BadRequestError(e.getMessage))
              }

            case IntegrationType.SMARTREACH =>

              Logger.fatal(s"Invalid service type: $serviceProvider")

              Future.successful(Left(OAuthFetchTokensError.BadRequestError(s"Invalid service type. Please contact support.")))
          }

        }
        else if(serviceProvider == "calendly"){
            calendlyWebhookService.fetchTokenAndCreateWebhookSuscription(
                code = code,
                teamId = teamId,
                accountId = accountId,
                orgId = orgId
            ).map{
                case Left(error) =>
                    Left(OAuthFetchTokensError.ServerError(message = s"calendlyWebhookService.fetchTokenAndCreateWebhookSuscription error occured", error = None))

                case Right(value) =>
                    Right(OAuthFetchTokensResponse(message = "Success", data = Json.obj()))
            }

        }
        else {

          val emailSettingsForSaving = if (serviceProvider == "outlook") __outlookAuthFetchTokens(
            code = code,
            failedLogErrorPartial = failedLogErrorPartial,
            failedAuthorizationErrorMsg = failedAuthorizationErrorMsg,
            actingTeamAccount = teamMember,
            isZapmailOauthFlow = isZapmailOauthFlow,
            Logger = Logger
          ) else if (serviceProvider == "google") {

            if (googleApiKeyId.isEmpty) {
              Logger.fatal(s"oauthFetchTokens google: googleApiKeyId isEmpty: $state")
              error_occurred = Some(s"oauthFetchTokens google: googleApiKeyId isEmpty: $state")
              Future.failed(new Exception(s"Invalid google request. Please contact support."))

            } else {

              __googleAuthFetchTokens(
                code = code,
                failedLogErrorPartial = failedLogErrorPartial,
                failedAuthorizationErrorMsg = failedAuthorizationErrorMsg,
                actingTeamAccount = teamMember,
                googleApiKeyId = googleApiKeyId.get,
                isZapmailOauthFlow = isZapmailOauthFlow,
                Logger = Logger
              )
            }
          }
          else {
            error_occurred = Some(s"Invalid service provider: $serviceProvider")
            Future.failed(new Exception(s"Invalid service provider: $serviceProvider"))
          }


          emailSettingsForSaving
            .map { case (parentEmailSettingForSaving, aliasEmailSettingsForSaving) => {

              val email = parentEmailSettingForSaving.email

              val checkEmailInSameTeam = emailSettingDAO.findByEmailAndTeamId(email = email, teamId = teamMember.team_id)

              val doesEmailExistInSameTeam = checkEmailInSameTeam.isDefined

              val permittedAccountIdsForEditingAccounts = permittedAccountIds


              val invalidZapmailDomainCall : Boolean = if (isZapmailOauthFlow) {
                val isZapmailDomainExists = Helpers.getDomainFromEmail(email)
                  .exists(
                    domain =>
                      emailSettingDAO.doesZapMailDomainExist(domain, teamId).getOrElse(false)
                  )
                !isZapmailDomainExists
              } else false



              // if (doesEmailExistInSameTeam && (checkEmailInSameTeam.get.account_id != ta.account_id)) {

              // if email already connected in team with a different team member,
              // and user does not have permission to edit email of the other team member, then show error
              if (
                doesEmailExistInSameTeam &&

                  // not loggedin user's email    // FIXME VALUECLASS
                  (checkEmailInSameTeam.get.owner_id.id != teamMember.user_id) &&

                  // loggedin user does not have permission to edit team members' emails
                  !permittedAccountIdsForEditingAccounts.contains(checkEmailInSameTeam.get.owner_id.id) // FIXME VALUECLASS
              ) {

                val teamMember = accountService.find(id = checkEmailInSameTeam.get.owner_id.id).get // FIXME VALUECLASS
                val name = Helpers.getAccountName(teamMember)

                Logger.fatal(s"3 already in team $name $email: $failedLogErrorPartial")
                error_occurred = Some(s"$email is already integrated by $name. You do not have the permission to edit/update it. Please contact your admin or our support.")
                val errMsg = s"$email is already integrated by $name. You do not have the permission to edit/update it. Please contact your admin or our support."

                Left(OAuthFetchTokensError.BadRequestError(errMsg))

              } else if (invalidZapmailDomainCall){
                Logger.shouldNeverHappen(s"Invalid Zapmail Domain: $email: $failedLogErrorPartial , This should come only from the zapmail oauth flow.")
                Left(OAuthFetchTokensError.BadRequestError("Invalid Zapmail Domain, That been attempted to connect. Please contact support."))
              } else {

                emailAccountService.createEmailViaOauth(
                  orgId = orgId.id,
                  accountId = teamMember.user_id,
                  teamId = teamMember.team_id,
                  taId = teamMember.ta_id,
                  data = parentEmailSettingForSaving,
                  owner_email = teamMember.email
                ) match {

                  case Left(CreateEmailViaOauthError.FreeDomainUseError) =>
                    error_occurred = Some(CONSTANTS.API_MSGS.FREE_EMAIL_DOMAIN_ERROR)

                    Left(OAuthFetchTokensError.BadRequestError(CONSTANTS.API_MSGS.FREE_EMAIL_DOMAIN_ERROR))

                  case Left(CreateEmailViaOauthError.DuplicateEmailError) =>
                    Logger.fatal(s"4 already in account $email: $failedLogErrorPartial")
                    error_occurred = Some("Given email is already added to your account")

                    Left(OAuthFetchTokensError.BadRequestError("Given email is already added to your account"))

                  case Left(CreateEmailViaOauthError.SQLException(msg, e)) =>
                    Logger.fatal(s"5 createViaOauth exception $email: $e: $failedLogErrorPartial")
                    error_occurred = Some(s"5 createViaOauth exception $email: $e: $failedLogErrorPartial")

                    Left(OAuthFetchTokensError.ServerError(failedAuthorizationErrorMsg, error = Some(e)))

                  case Left(CreateEmailViaOauthError.EmailNotAddedError) =>
                    Logger.fatal(s"6 empty savedParentEmailSetting $email: $failedLogErrorPartial")
                    error_occurred = Some(s"6 empty savedParentEmailSetting $email: $failedLogErrorPartial")

                    Left(OAuthFetchTokensError.ServerError(failedAuthorizationErrorMsg, error = None))

                  case Left(CreateEmailViaOauthError.FeatureUsageServiceError(e)) =>
                    error_occurred = Some("Error while adding email: "+ e.getMessage)
                    Left(OAuthFetchTokensError.ServerError("Error while adding email: ", error = Some(e)))

                  case Right(savedParentEmailSetting) =>

                    var savedEmailSettings: Vector[EmailSetting] = Vector(savedParentEmailSetting)

                    aliasEmailSettingsForSaving.foreach(alias => {

                      val aliasWithParentSettingId = alias.copy(
                        alias_parent_id = Some(savedParentEmailSetting.id.get.emailSettingId) // FIXME VALUECLASS
                      )

                      emailAccountService.createEmailViaOauth(
                        orgId = orgId.id,
                        accountId = teamMember.user_id,
                        teamId = teamMember.team_id,
                        taId = teamMember.ta_id,
                        data = aliasWithParentSettingId,
                        owner_email = teamMember.email
                      ) match {
                        case Left(CreateEmailViaOauthError.FreeDomainUseError) =>
                          error_occurred = Some(CONSTANTS.API_MSGS.FREE_EMAIL_DOMAIN_ERROR)
                          Left(OAuthFetchTokensError.BadRequestError(CONSTANTS.API_MSGS.FREE_EMAIL_DOMAIN_ERROR))

                        case Left(CreateEmailViaOauthError.DuplicateEmailError) =>
                          Logger.fatal("7 alias Email already exists")
                          error_occurred = Some("Given email (alias) is already added to your account")
                          Left(OAuthFetchTokensError.BadRequestError("Given email (alias) is already added to your account"))

                        case Left(CreateEmailViaOauthError.SQLException(msg, e)) =>

                          //                                      Logger.error(s"n\n im in trouble: $e")

                          Logger.fatal(s"8 createViaOauth email: $email :: alias: $alias :: msg: $msg :: $failedLogErrorPartial", err = e)
                          error_occurred = Some(s"Error while adding email ${alias.email}: " + e.getMessage)
                          Left(OAuthFetchTokensError.ServerError(s"Error while adding email ${alias.email}: " + e.getMessage, error = Some(e)))

                        case Left(CreateEmailViaOauthError.EmailNotAddedError) =>
                          Logger.fatal("Cannot add Email")
                          error_occurred = Some("Email Not added.")
                          Left(OAuthFetchTokensError.ServerError("Email Not added.", error = Some(new Exception())))

                        case Left(CreateEmailViaOauthError.FeatureUsageServiceError(e)) =>
                          Logger.fatal("FeatureUsageServiceError")
                          error_occurred = Some("Error while adding email: "+e.getMessage)
                          Left(OAuthFetchTokensError.ServerError("Error while adding email: ", error = Some(e)))


                        case Right(savedAlias) =>

                          //                                      Logger.error(s"n\n alias success: $savedAlias")

                          savedEmailSettings = savedEmailSettings :+ savedAlias


                      }
                    })

                    saved_email_address = Some(savedParentEmailSetting.email)
                    Right(OAuthFetchTokensResponse("Email account(s) have been saved", Json.obj("emails" -> savedEmailSettings, "campaign_id" -> campaignId, "email_type" -> emailType)))

                }
              }
            }


            }


        }
          .recover { case e =>

            Logger.fatal(s"${serviceProvider}AuthFetchTokens error: emailSettingsForSaving, $teamMember", err = e)
            error_occurred = Some(s"${serviceProvider}AuthFetchTokens error: emailSettingsForSaving, $teamMember "+e.getMessage)

            Left(OAuthFetchTokensError.BadRequestError(e.getMessage))
          }

    }
    result.map{
      case Left(err) =>
        if (error_occurred.isDefined && (serviceProvider == "google" || serviceProvider == "outlook")) {
          emailAccountTestService.insertEmailSettingIntegrationLogs(
            accountId = accountId,
            orgId = orgId,
            teamId = TeamId(teamMember.team_id),
            email_address = saved_email_address,
            service_provider = Some(if (serviceProvider == "google") EmailServiceProvider.GMAIL_API else if (serviceProvider == "outlook") EmailServiceProvider.OUTLOOK_API else EmailServiceProvider.OTHER),
            completed = false,
            error_occurred = error_occurred,
            email_integration_stage = EmailSettingIntegrationLogsStage.OAuthUrlGenerationError
          ) match {
            case Failure(exception) =>
              Logger.error(s"Error occurred while inserting the Email Setting Integration logs for service provider: ${serviceProvider}", err = exception)
            case Success(value) =>
              Logger.info("Email Integration logs inserted successfully")
          }
        }
        Left(err)
      case Right(value) =>
        Right(value)
    }
  }



}
