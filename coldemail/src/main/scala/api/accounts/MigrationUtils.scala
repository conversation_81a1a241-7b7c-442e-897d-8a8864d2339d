package api.accounts

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import api.AppConfig
import io.sr.billing_common.models.PlanType
import api.accounts.dao.AccountDAOV2
import api.accounts.models.{AccountId, AccountV2, OrgId}
import api.call.models.{CallStatus, ParticipantCallStatus}
import api.campaigns.CampaignProspectDAO
import api.campaigns.services.CampaignId
import api.emails.{EmailAndProspectCatDetailsForMigration, EmailSettingDAO}
import api.emails.models.{EmailMessageContact, EmailMessageContactModel, EmailMessageContacts, EmailReplyType}
import api.prospects.dao.ProspectDAO
import api.prospects.models.{ProspectCategory, ProspectCategoryId, ProspectCategoryUpdateFlow, ProspectId}
import api.prospects.service.ProspectServiceV2
import api.prospects.{Inbox, ProspectIdEmail, ProspectUpdateCategoryTemp, ProspectUpdateCategoryTemp2}
import api.team_inbox.dao_service.ReplySentimentDAOService
import api.team_inbox.model.FolderType
import api.team_inbox.service.TeamInboxService
import com.twilio.rest.insights.v1.Conference.ConferenceStatus
import io.smartreach.esp.api.emails.IEmailAddress
import org.joda.time.DateTime
import org.postgresql.util.PGobject
import play.api.Logging
import play.api.libs.json.*
import play.api.libs.ws.ahc.AhcWSClient
import scalikejdbc.*
import scalikejdbc.interpolation.SQLSyntax
import scalikejdbc.jodatime.JodaWrappedResultSet.*
import utils.dbutils.{DBUtils, SQLUtils}
import utils.mq.delete_org_data.MqOrgDataDeleterMsg
import utils.{AppConfigUtil, Helpers, SRAppConfig, SRLogger}
import utils.helpers.LogHelpers
import utils.uuid.{IdTeamId, MigrationFunc, SrIdentifier, SrUuidUtils, TableNames}

import scala.annotation.tailrec
import scala.collection.immutable
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.concurrent.duration.*
import scala.util.control.Breaks.break
import scala.util.{Failure, Left, Success, Try}
import api.accounts.service.OrganizationService
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

private case class MigrationUtilsEmailMessagesInboxV3(
  emailMessageId: Long,
  to_emails: Seq[IEmailAddress],
  from_email: IEmailAddress,
  reply_to_email: Option[IEmailAddress],
  cc_emails: Seq[IEmailAddress],
  bcc_emails: Seq[IEmailAddress],
  team_id: Long
)

private case class OrgInfoForDeleteInactiveData(
                                                 owner_email: String,
                                                 current_cycle_started_at: DateTime,
                                                 plan_type: PlanType
                                               )

private case class ExistingDataEmailThread(
                                            id: Long,
                                            archived: Boolean,
                                            snoozed: Boolean,
                                            is_prospect: Boolean,
                                            bounced: Boolean
                                          )

private case class EmailThreadsUpdatedAt(
                                          id: Long,
                                          updated_at: DateTime
                                        )

class MigrationUtils(
  //                      prospectDAO : Prospect,
  rolePermissionDataDAOV2: RolePermissionDataDAOV2,
  accountDAOV2: AccountDAOV2,
  prospectServiceV2: ProspectServiceV2,
  emailMessageContactModel: EmailMessageContactModel,
  migrationService: MigrationService,
  campaignProspectDAO: CampaignProspectDAO,
  replySentimentDAOService: ReplySentimentDAOService,
  srUuidUtils: SrUuidUtils,
  dbUtils: DBUtils,
  migrationFunc: MigrationFunc,
  email_setting_dao: EmailSettingDAO,
  accountService: AccountService,
  prospectDAO: ProspectDAO,
  organizationService: OrganizationService,
  srRollingUpdateCoreService: SrRollingUpdateCoreService
) extends Logging {



  // MIGRATION SCRIPT FOR INBOXV3 EmailMessageContacts
  // Added on 6th May 2021

  def findEmailSettingsForMigratingEmailMsgContacts() = {
    DB.readOnly { implicit session =>

      sql"""
         select id, email, team_id from email_settings eset
          where
          exists (select * from emails_scheduled es where es.sender_email_settings_id = eset.id)
          ;

      """
        .map(rs => {
          (rs.long("id"), rs.string("email"), rs.long("team_id"))
        })
        .list
        .apply()
    }
  }

  private def findEmailMessagesForSavingContacts(
    inboxEmailSettingId: Long,
    teamId: Long,
    limitPerRound: Int,
  )(using Logger: SRLogger) = {
    val emailContactsRaw: Seq[MigrationUtilsEmailMessagesInboxV3] = DB.readOnly { implicit session =>

      sql"""
           select ems.es_id as email_message_id,
             ems.to_email,
             ems.from_email,
             ems.from_name,
             ems.reply_to_email,
             ems.reply_to_name,
             ems.cc_emails,
             ems.bcc_emails,
             ems.full_headers,
             ems.outlook_response_json

             from emails_scheduled es
             inner join email_message_data ems on ems.es_id = es.id and es.team_id = ems.team_id

             where es.sender_email_settings_id = $inboxEmailSettingId
              and es.team_id = $teamId
              and es.sent
              and ems.to_email is not null
              and ems.to_email != ''

             and not exists (select * from email_message_contacts emc where emc.email_message_id = es.id and emc.team_id = es.team_id)

             limit $limitPerRound

      """
        .map(rs => {

          //val scheduledFromCampaign = rs.boolean("scheduled_from_campaign")
          val emailMessageId = rs.long("email_message_id")

          val fullHeadersOpt: Option[JsValue] = rs.anyOpt("full_headers")
            .flatMap(hdrs => {
              val json = Json.parse(hdrs.asInstanceOf[PGobject].getValue)

              val empty = (json \ "From").asOpt[String].isEmpty

              if (empty) None else Some(json)
            })

          val outlookResOpt: Option[JsValue] = rs.anyOpt("outlook_response_json")
            .flatMap(hdrs => {
              val json = Json.parse(hdrs.asInstanceOf[PGobject].getValue)

              val empty = (json \ "toRecipients").asOpt[String].isEmpty

              if (empty) None else Some(json)
            })

          if (fullHeadersOpt.isDefined) {

            val fullHeaders = fullHeadersOpt.get

            val fromEmail: IEmailAddress = IEmailAddress.parse((fullHeaders \ "From").as[String]).get.head
            val toEmails: Seq[IEmailAddress] = IEmailAddress.parse((fullHeaders \ "To").as[String]).get
            val ccEmails: Seq[IEmailAddress] = (fullHeaders \ "Cc").asOpt[String]
              .map(cc => IEmailAddress.parse(cc).get)
              .getOrElse(Seq())

            val bccEmails: Seq[IEmailAddress] = (fullHeaders \ "Bcc").asOpt[String]
              .map(bcc => IEmailAddress.parse(bcc).get)
              .getOrElse(Seq())

            val replyEmail: Option[IEmailAddress] = (fullHeaders \ "Reply-to").asOpt[String]
              .map(em => IEmailAddress.parse(em).get.head)

            MigrationUtilsEmailMessagesInboxV3(
              emailMessageId = emailMessageId,
              from_email = fromEmail,
              to_emails = toEmails,
              cc_emails = ccEmails,
              bcc_emails = bccEmails,
              reply_to_email = replyEmail,
              team_id = teamId
            )


          } else if (outlookResOpt.isDefined) {

            val m = outlookResOpt.get


            val toRecipients: Seq[JsValue] = (m \ "toRecipients").as[List[JsValue]]
            val toAddresses: Seq[IEmailAddress] = toRecipients.map(to => {
              IEmailAddress(
                name = (to \ "emailAddress" \ "name").asOpt[String],
                email = (to \ "emailAddress" \ "address").asOpt[String].getOrElse("").trim.toLowerCase
              )
            })
              .filter(_.email.nonEmpty)

            val ccRecipients = (m \ "ccRecipients").as[List[JsValue]]
            val ccEmails: List[IEmailAddress] = ccRecipients
              .map(js => {
                val emailOpt = (js \ "emailAddress" \ "address").asOpt[String]
                val nameOpt = (js \ "emailAddress" \ "name").asOpt[String]

                (emailOpt, nameOpt)
              })
              .filter(r => r._1.isDefined && r._1.get.trim.nonEmpty)
              .map { case (emailOpt, nameOpt) =>

                IEmailAddress(
                  email = emailOpt.get,
                  name = nameOpt
                )
              }

            val bccRecipients = (m \ "bccRecipients").as[List[JsValue]]
            val bccEmails: List[IEmailAddress] = bccRecipients
              .map(js => {
                val emailOpt = (js \ "emailAddress" \ "address").asOpt[String]
                val nameOpt = (js \ "emailAddress" \ "name").asOpt[String]

                (emailOpt, nameOpt)
              })
              .filter(r => r._1.isDefined && r._1.get.trim.nonEmpty)
              .map { case (emailOpt, nameOpt) =>

                IEmailAddress(
                  email = emailOpt.get,
                  name = nameOpt
                )
              }


            val replyTo: Option[JsValue] = (m \ "replyTo").as[List[JsValue]].headOption
            val replyToAddress = if (replyTo.nonEmpty) (replyTo.get \ "emailAddress" \ "address").asOpt[String] else None
            val replyToName = if (replyTo.nonEmpty) (replyTo.get \ "emailAddress" \ "name").asOpt[String] else None
            val replytoEmail = replyToAddress.map(addr => {
              IEmailAddress(
                email = addr,
                name = replyToName
              )
            })

            val fromAddr = IEmailAddress(
              email = (m \ "from" \ "emailAddress" \ "address").asOpt[String].getOrElse(""), // NOTE: sometimes email field is empty in outlook api
              name = (m \ "from" \ "emailAddress" \ "name").asOpt[String]
            )

            MigrationUtilsEmailMessagesInboxV3(
              emailMessageId = emailMessageId,
              from_email = fromAddr,
              to_emails = toAddresses,
              cc_emails = ccEmails,
              bcc_emails = bccEmails,
              reply_to_email = replytoEmail,
              team_id = teamId
            )


          } else {

            val toEmails = IEmailAddress.parse(rs.string("to_email")).get
            val fromEmail = IEmailAddress(
              email = rs.string("from_email"),
              name = rs.stringOpt("from_name")
            )
            val replyTo = rs.stringOpt("reply_to_email").map(em => IEmailAddress(
              email = em,
              name = rs.stringOpt("reply_to_name")
            ))

            val ccEmails = rs.stringOpt("cc_emails")
              .map(cc => IEmailAddress.parse(emailsStr = cc).get)
              .getOrElse(Seq())

            val bccEmails = rs.stringOpt("bcc_emails")
              .map(bcc => IEmailAddress.parse(emailsStr = bcc).get)
              .getOrElse(Seq())

            MigrationUtilsEmailMessagesInboxV3(
              emailMessageId = emailMessageId,
              from_email = fromEmail,
              to_emails = toEmails,
              cc_emails = ccEmails,
              bcc_emails = bccEmails,
              reply_to_email = replyTo,
              team_id = teamId
            )
          }


        })
        .list
        .apply()

    }

    emailContactsRaw
  }

  private def findEmailMessagesAndSaveContacts(
    inboxEmailSettingId: Long,
    teamId: Long,
    inboxEmailAddress: String,
    SRLogger: SRLogger,
    page: Int = 1
  ): Int = {

    val MESSAGES_LIMIT_PER_ROUND = 1000

    given Logger: SRLogger= SRLogger.appendLogRequestId(appendLogReqId = s" :: findEmailMessagesAndSaveContacts page: $page :: ")

    val emailContactsRaw = findEmailMessagesForSavingContacts(
      inboxEmailSettingId = inboxEmailSettingId,
      teamId = teamId,
      limitPerRound = MESSAGES_LIMIT_PER_ROUND
    )

    Logger.info(s"found emailContactsRaw: ${emailContactsRaw.size}")
    val totalEmailContactsRawFound =       emailContactsRaw.size


    if (emailContactsRaw.isEmpty) {

      Logger.info("all email messages have been handled, done with this inbox")
      totalEmailContactsRawFound

    } else {

      val allEmails = emailContactsRaw.flatMap(c => {
        Seq(c.from_email) ++
          c.to_emails ++
          c.cc_emails ++
          c.bcc_emails ++
          c.reply_to_email.toSeq
      })
        .map(_.email.trim.toLowerCase)
        .distinct

      Logger.info(s"found unique email addresses to search: ${allEmails.size}")

      val prospectsFound = prospectServiceV2.findByIdOrEmail(
        orProspectIds = Seq(),
        orProspectEmails = allEmails,
        teamId = teamId,
        logger = Logger
      )
        .get
        .map(pr => ProspectIdEmail(
          id = pr.prospect_id,
          email = pr.email,
          owner_id = pr.owner_id
        ))

      Logger.info(s"total prospects found: ${prospectsFound.size}")


      val emailMessageContactsToSave: Seq[EmailMessageContact] = emailContactsRaw.flatMap(c => {

        val contacts: EmailMessageContacts = EmailMessageContact.getEmailMessageContacts(
          inboxEmailAddress = inboxEmailAddress,
          emailMessageId = c.emailMessageId,
          fromEmail = c.from_email,
          toEmails = c.to_emails,
          ccEmails = c.cc_emails,
          bccEmails = c.bcc_emails,
          replyToEmail = c.reply_to_email,
          teamId = teamId,
          foundProspects = prospectsFound
        )

        EmailMessageContact.getContactsArrayFromEmailMessageContacts(
          contacts = contacts
        )
      })

      Logger.info(s"total emailMessageContactsToSave: ${emailMessageContactsToSave.size}")


      emailMessageContactModel.insertEmailMessageContacts(
        contacts = emailMessageContactsToSave
      ) match {
        case Failure(e) =>
          Logger.fatal("insertEmailMessageContacts", err = e)
          throw e

        case Success(saved) =>
          Logger.info(s"insertEmailMessageContacts done: saved: ${saved.size}")

          findEmailMessagesAndSaveContacts(
            inboxEmailSettingId = inboxEmailSettingId,
            teamId = teamId,
            inboxEmailAddress = inboxEmailAddress,
            SRLogger = SRLogger,
            page = page + 1
          )
      }
    }
  }

  def migrateEmailMessageContacts(
    inboxEmailSettingId: Long,
    inboxEmailAddress: String,
    teamId: Long
  ): Try[AnyVal] = Try {

    val Logger = new SRLogger(
      logRequestId = s" eset_$inboxEmailSettingId tid_$teamId"
    )

    Logger.info(s"starting")


    val emailBelongsToTeam = DB.readOnly { implicit session =>
      sql" select exists(select * from email_settings where team_id = $teamId and id = $inboxEmailSettingId AND email = $inboxEmailAddress)"
        .map(_.boolean("exists"))
        .single
        .apply()
        .get
    }

    if (!emailBelongsToTeam) {
      throw new Exception(s"fatal: email doesnt belong to team.")
    } else {

      Logger.info("emailBelongsToTeam: true")

      findEmailMessagesAndSaveContacts(
        inboxEmailSettingId = inboxEmailSettingId,
        teamId = teamId,
        inboxEmailAddress = inboxEmailAddress,
        SRLogger = Logger
      )


    }

  }




  /*

  def tempMigrateOrganizationToTrackingKey(): Try[AnyVal] = Try {

    for (orgId <- 392 to 624) {


      Logger.info(s"finding org with id: $orgId")
      val org = DB.autoCommit { implicit session =>

        sql""" select id, name from organizations where id = $orgId
         AND tracking_subdomain_key is null

      """
          .map(rs => (rs.long("id"), rs.string("name")))
          .single
          .apply()

      }

      if (org.isEmpty) {
        Logger.error(s"did not find org with id: $orgId : skipping")

      } else {

        Logger.info(s"found org with id: $orgId : $org")


        var orgKeyName = Helpers.generateOrgTrackingSubdomainKey(orgName = org.get._2)

        val countOfSameTrackingKey = Account.checkOrgTrackingSubdomainKey(checkKey = orgKeyName)

        if (countOfSameTrackingKey > 0) {

          orgKeyName = orgKeyName + countOfSameTrackingKey

        }

        Logger.info(s"found key for org with id: $orgId : $orgKeyName : $countOfSameTrackingKey")


        val orgUpdated = DB.autoCommit { implicit session =>

          sql""" update organizations set tracking_subdomain_key = $orgKeyName where id = $orgId
         AND tracking_subdomain_key is null returning *
      """
            .map(rs => (rs.long("id"), rs.string("name"), rs.stringOpt("tracking_subdomain_key")))
            .single
            .apply()

        }

        Logger.info(s"updated org: $orgUpdated")

        orgUpdated.isDefined


      }
    }
  }
  */


  //////////////////
  //////////////////
  //////////////////
  //////////////////



  def migrateOlderAccountsIntoProspectCategoryCustom = {

    val newDefaultCats = Seq(
      ProspectCategory.MEETING_BOOKED,
    )

    val teams: List[(Long, Long, String)] = DB.readOnly { implicit session =>
      sql"""
           SELECT t.id AS team_id,
           org.owner_account_id AS account_id,
           CONCAT(a.first_name, ' ',  a.last_name) AS account_name
           FROM teams t
           INNER JOIN organizations org ON org.id = t.org_id
           INNER JOIN accounts a ON a.id = org.owner_account_id
           WHERE NOT EXISTS (
            select * from prospect_categories_custom
            where
              team_id = t.id and
              text_id IN ${SQLUtils.generateSQLValuesClause(arr = newDefaultCats.map(_.toString))}
           )
           ORDER BY t.id ASC
           ;
         """
        .map(rs => (rs.long("account_id"), rs.long("team_id"), rs.string("account_name")))
        .list
        .apply()
    }

    logger.info(s"\n\nfound teams: ${teams.length}\n\n")


    insertCategoriesForAllTeams(teams = teams, newDefaultCats = newDefaultCats)


    /*teams.foreach(team => {
      Logger.info(s"\n\nstart team: $team\n\n")

      insertCategories(team = team) match {

        case Failure(e) => Logger.error(s"FAILED")

        case Success(value) => Logger.info(s"Success ${value}")

      }

    })*/

  }

  def insertCategoriesForAllTeams(
    teams: Seq[(Long, Long, String)],
    newDefaultCats: Seq[ProspectCategory.Value]
  ) = Try {

    teams.grouped(300).map { tms =>

      logger.info(s"Running query")

      var parameters = List[Any]()

      val valuePlaceholder = tms.zipWithIndex.flatMap { case (team, i) =>

        ProspectCategory.displayNamesOfDefaultCategories
          .filter(c => newDefaultCats.contains(c._1))
          .map { c =>

            val cate = ProspectCategory.getProspectCategoryDisplyNameAndId(cate = c._1)

            val isEditable = if (
              c._1 == ProspectCategory.INTERESTED
                || c._1 == ProspectCategory.NOT_INTERESTED
                || c._1 == ProspectCategory.NOT_NOW
                || c._1 == ProspectCategory.CONVERTED
            ) {

              true

            } else {

              false

            }

            parameters = parameters ::: List(
              cate.displayName,
              c._1.toString,
              cate.color,
              team._1,
              team._3,
              team._2,
              cate.prospectCategoryRank.rank + team._1 + team._2 + i,
              isEditable
            )

            s"""
            (

              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?
            )

          """

          }

      }
        .mkString(", ")

      logger.info(s"DONE valuePlaceholder")

      val query =
        s"""
            INSERT INTO prospect_categories_custom
            (
               name,
               text_id,
               label_color,
               account_id,
               account_name,
               team_id,
               prospect_category_rank,
               is_custom

            )
            VALUES $valuePlaceholder
            ON CONFLICT DO NOTHING
            RETURNING *;
          """


      DB autoCommit { implicit session =>

        SQL(query)
          .bind(parameters*)
          .map(_.long("id"))
          .list
          .apply()

      }

    }

  }

  /*def migrateProspectCategoryIdCustomIntoProspects = {

    val teams: List[(Long, Seq[ProspectCategoriesInDB])] = DB.readOnly { implicit session =>
      sql"""
          SELECT t.id AS team_id, json_agg(pc) as cats

          FROM teams t
          join prospect_categories_custom pc on pc.team_id = t.id
          WHERE

          EXISTS (SELECT * FROM campaigns_prospects cp join campaigns c ON c.id = cp.campaign_id WHERE cp.prospect_category_id_custom IS NULL AND c.team_id = t.id)

          AND t.id IN (24)

          group by t.id
          ORDER BY t.id ASC
          ;
         """
        .map(rs => (
          rs.long("team_id"),
          Json.parse(rs.any("cats").asInstanceOf[PGobject].getValue).validate[Seq[ProspectCategoriesInDB]].get
        ))
        .list
        .apply()
    }

    Logger.info(s"\n\nfound teams: ${teams.length}\n\n")

    updatedProspectCategoryIdCustom(teams = teams) match {

      case Failure(e) => Logger.error(s"FAILED")

      case Success(value) => Logger.info(s"Success ${value}")

    }


  }


  def updatedProspectCategoryIdCustom(teams: List[(Long, Seq[ProspectCategoriesInDB])]): Try[Seq[Int]] = Try {

    teams.map(team => {

      Logger.info(s"""Started TEAM_ID ${team._1} ::

                UPDATE campaigns_prospects cp
                SET
                prospect_category_id_custom = (
                CASE
                WHEN prospect_category_id = 1 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(1).get.toString).get.id}

                WHEN prospect_category_id = 2 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(2).get.toString).get.id}

                WHEN prospect_category_id = 3 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(3).get.toString).get.id}

                WHEN prospect_category_id = 4 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(4).get.toString).get.id}

                WHEN prospect_category_id = 5 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(5).get.toString).get.id}

                WHEN prospect_category_id = 6 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(6).get.toString).get.id}

                WHEN prospect_category_id = 7 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(7).get.toString).get.id}

                WHEN prospect_category_id = 8 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(8).get.toString).get.id}

                WHEN prospect_category_id = 9 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(9).get.toString).get.id}

                WHEN prospect_category_id = 10 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(10).get.toString).get.id}
                END
                )


                FROM campaigns c

                WHERE

                c.id = cp.campaign_id

                AND c.team_id = ${team._1}

                AND prospect_category_id_custom IS NULL
        """)

      val res = DB autoCommit { implicit session =>

        sql"""
          UPDATE campaigns_prospects cp
          SET
            prospect_category_id_custom = (
              CASE
                 WHEN prospect_category_id = 1 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(1).get.toString).get.id}

                 WHEN prospect_category_id = 2 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(2).get.toString).get.id}

                 WHEN prospect_category_id = 3 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(3).get.toString).get.id}

                 WHEN prospect_category_id = 4 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(4).get.toString).get.id}

                 WHEN prospect_category_id = 5 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(5).get.toString).get.id}

                 WHEN prospect_category_id = 6 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(6).get.toString).get.id}

                 WHEN prospect_category_id = 7 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(7).get.toString).get.id}

                 WHEN prospect_category_id = 8 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(8).get.toString).get.id}

                 WHEN prospect_category_id = 9 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(9).get.toString).get.id}

                 WHEN prospect_category_id = 10 THEN ${team._2.find(cucat => cucat.text_id == Inbox.getProspectCategoryById(10).get.toString).get.id}
              END
            )


            FROM campaigns c

            WHERE

            c.id = cp.campaign_id

            AND c.team_id = ${team._1}

            AND cp.prospect_category_id_custom IS NULL
          ;
      """
          .update
          .apply()

      }

      Logger.info(s"\n\n\n ENDED TEAM_ID ${team._1} :: ${res} \n\n\n")

      res

    })


  }*/


  def updatedProspectCategoryIdCustom(i: Long): Try[Int] = Try {


    logger.info(s"\n\n\n STARTED ${i}  \n\n\n")

    val res = DB autoCommit { implicit session =>

      val from = i * 10000
      val to = (i+1) * 10000

      sql"""

             UPDATE prospects
             SET prospect_category_id_custom = 75
             WHERE prospect_category_id = 1
             AND team_id = 8
             AND id BETWEEN $from AND $to;
      """
        .update
        .apply()

    }

    logger.info(s"\n\n\n ENDED ${i}  ${res} \n\n\n")

    res



  }

  sealed trait DelData
  case class DelDataForTeam(
    teamId: Long,
    teamName: String,
    orgId: Long
  ) extends DelData

  case class DelDataForAccount(
    accountId: Long,
    email: String,
    orgId: Long
  ) extends DelData

  private def isValidDeleteRequest(
    data: DelData,
    Logger: SRLogger
  ): Try[Boolean] = {

    data match {

      case d: DelDataForAccount =>

        val tryOfAccountEmail = migrationService.getAccountEmail(
          accountId = AccountId(id = d.accountId),
          orgId = OrgId(id = d.orgId),
          logger = Logger
        )

        val tryOfIsValidRequest = tryOfAccountEmail.map(_ == d.email)

        tryOfIsValidRequest.map { isValidRequest =>

          if (!isValidRequest) {

            Logger.error("FATAL: invalid attempt FATAL email not matching")
          }
        }

        tryOfIsValidRequest

      case d: DelDataForTeam =>

        val tryOfTeamName = migrationService.getTeamName(
          teamId = TeamId(id = d.teamId),
          orgId = OrgId(id = d.orgId),
          logger = Logger
        )

        val tryOfIsValidRequest = tryOfTeamName.map(_ == d.teamName)

        tryOfIsValidRequest.map { isValidRequest =>

          if (!isValidRequest) {

            Logger.error("FATAL: invalid attempt FATAL team name not matching")
          }
        }

        tryOfIsValidRequest
    }
  }

  /**
    * should be used carefully
    */
  private def deletData(
    data: DelData,
    Logger: SRLogger,
    delete_prospect: Boolean = true
  ): Try[Unit] = Try {

    isValidDeleteRequest(data = data, Logger = Logger).map { isValidRequest =>

      if (!isValidRequest) {
        throw new Exception(s"Invalid del attempt: $data")
      } else {

        val isAccountDelete: Boolean =
        data match {

          case d: DelDataForAccount =>
            Logger.info(s"\n\nstarting deleting data for account: org_${d.orgId} :: aid_${d.accountId} :: email: ${d.email} ...\n\n")
            true

          case d: DelDataForTeam =>
            Logger.info(s"\n\nstarting deleting data for team: org_${d.orgId} :: tid_${d.teamId} :: teamName :: ${d.teamName} ...\n\n")
            false

        }

        // deleteDataFromTable("prospect_events", maxRowsToDeleteAtOnce = 1000)

        for {
          _: Int <- deleteOrUpdateDataFromTable(tableName = "emails_scheduled", maxRowsToManipulateAtOnce = 200, accountIdColName = Some("account_id"), withTaId = false, data = data, Logger = Logger)

          _: Int <- deleteOrUpdateDataFromTable(tableName = "team_agency_invites", accountIdColName = Some("inviter_account_id"), withTaId = false, data = data, Logger = Logger)

          _: Int <- deleteOrUpdateDataFromTable(tableName = "workflow_crm_settings", accountIdColName = Some("owner_id"), withTaId = false, data = data, Logger = Logger)
          _: Int <- deleteOrUpdateDataFromTable(tableName = "triggers", accountIdColName = Some("account_id"), withTaId = false, data = data, Logger = Logger)

          _: Int <- deleteOrUpdateDataFromTable(tableName = "campaigns", maxRowsToManipulateAtOnce = 25, accountIdColName = Some("account_id"), withTaId = true, data = data, Logger = Logger)

          //  We are deleting prospects -> added_by separately,
          //  because as account_id and added_by can be different.
          //  So, when we call delete prospects -> account_id then it will try to delete all the entries
          //  in the prospects, table where account_id = $deleteAccountID.
          //  But may possibly miss some entries where added_by = $deleteAccountId.
          _: Int <- deleteOrUpdateDataFromTable(tableName = "notes", maxRowsToManipulateAtOnce = 200, accountIdColName = Some("added_by"), withTaId = false, data = data, Logger = Logger)
          _: Int <- if (delete_prospect) {
            deleteOrUpdateDataFromTable(tableName = "prospects", maxRowsToManipulateAtOnce = 200, accountIdColName = Some("account_id"), withTaId = true, data = data, Logger = Logger)
          } else Success(0)

          _: Int <- if (delete_prospect) {
            deleteOrUpdateDataFromTable(tableName = "prospects", maxRowsToManipulateAtOnce = 200, accountIdColName = Some("added_by"), withTaId = false, data = data, Logger = Logger)
          } else Success(0)

          _: Int <- deleteOrUpdateDataFromTable(tableName = "email_threads", maxRowsToManipulateAtOnce = 200, accountIdColName = Some("account_id"), withTaId = false, data = data, Logger = Logger)

          _: Int <- if (delete_prospect) {
            deleteOrUpdateDataFromTable(tableName = "prospect_accounts", maxRowsToManipulateAtOnce = 200, accountIdColName = Some("owner_id"), withTaId = false, data = data, Logger = Logger)
          }  else Success(0)

          _: Int <- deleteOrUpdateDataFromTable(tableName = "templates", maxRowsToManipulateAtOnce = 25, accountIdColName = Some("account_id"), withTaId = true, data = data, Logger = Logger)

          _: Int <- deleteOrUpdateDataFromTable(tableName = "blacklist", maxRowsToManipulateAtOnce = 200, accountIdColName = Some("account_id"), withTaId = true, data = data, Logger = Logger)

          _: Int <- deleteOrUpdateDataFromTable(tableName = "column_defs", accountIdColName = Some("account_id"), withTaId = true, data = data, Logger = Logger)

          _: Int <- deleteOrUpdateDataFromTable(tableName = "lead_finder_billing_logs", accountIdColName = Some("account_id"), withTaId = false, data = data, Logger = Logger)

          _: Int <- if (delete_prospect) {
            deleteOrUpdateDataFromTable(tableName = "prospect_lists", accountIdColName = Some("account_id"), withTaId = false, data = data, Logger = Logger)
          } else Success(0)

          _: Int <- if (delete_prospect) {
            deleteOrUpdateDataFromTable(tableName = "prospect_saved_filters", accountIdColName = Some("account_id"), withTaId = false, data = data, Logger = Logger)
          } else Success(0)

          _: Int <- deleteOrUpdateDataFromTable(tableName = "tags", maxRowsToManipulateAtOnce = 10, accountIdColName = Some("account_id"), withTaId = false, data = data, Logger = Logger)

          _: Int <- deleteOrUpdateDataFromTable(tableName = "webhooks", accountIdColName = Some("account_id"), withTaId = true, data = data, Logger = Logger)

          _: Int <- if (delete_prospect) {
            deleteOrUpdateDataFromTable(tableName = "prospects_emails", accountIdColName = Some("account_id"), withTaId = false, data = data, Logger = Logger)
          } else Success(0)

          _: Int <- deleteOrUpdateDataFromTable(tableName = "webhook_events", idColName = "webhook_id", accountIdColName = Some("account_id"), withTaId = false, data = data, Logger = Logger)
          _: Int <- if (isAccountDelete) {
            deleteOrUpdateDataFromTable(tableName = "tags_prospects", idColName = "tag_id", accountIdColName = Some("account_id"), withTaId = false, data = data, Logger = Logger)
          } else Success(0)

          _: Int <- deleteOrUpdateDataFromTable(tableName = "email_settings", accountIdColName = Some("account_id"), withTaId = true, data = data, Logger = Logger)

          _: Int <- deleteOrUpdateDataFromTable(tableName = "opportunities", accountIdColName = Some("owner_id"), withTaId = false, data = data, Logger = Logger)

          _: Int <- deleteOrUpdateDataFromTable(tableName = "pipelines", accountIdColName = Some("owner_id"), withTaId = false, data = data, Logger = Logger)
          _: Int <- deleteOrUpdateDataFromTable(tableName = "lead_finder_filter_logs", accountIdColName = Some("account_id"), withTaId = false, data = data, Logger = Logger)
          _: Int <- deleteOrUpdateDataFromTable(tableName = "prospect_events", maxRowsToManipulateAtOnce = 10, accountIdColName = Some("doer_account_id"), withTaId = false, data = data, Logger = Logger)
          _: Int <- deleteOrUpdateDataFromTable(tableName = "prospect_events", maxRowsToManipulateAtOnce = 10, accountIdColName = Some("assigned_to_account_id"), withTaId = false, data = data, Logger = Logger)
          _: Int <- deleteOrUpdateDataFromTable(tableName = "prospect_events", maxRowsToManipulateAtOnce = 10, accountIdColName = Some("account_id"), withTaId = false, data = data, Logger = Logger)

          _: Int <- if(!isAccountDelete){
            deleteOrUpdateDataFromTable(tableName = "call_participants_logs", accountIdColName = None, withTaId = false, data = data, Logger = Logger, idColIsUuid = true, idColName = "call_conference_uuid")
          } else Success(0)
          _: Int <- if(!isAccountDelete){
            deleteOrUpdateDataFromTable(tableName = "call_conference_logs", accountIdColName = None, withTaId = false, data = data, Logger = Logger, idColIsUuid = true, idColName = "conference_uuid")
          } else Success(0)
        } yield {
          Logger.info("all data related to team account deleted")

        }
      }
    }
  }

  def deleteOrUpdateDataFromTable(
    idColName: String = "id",
    idColIsUuid: Boolean = false,
    tableName: String,
    maxRowsToManipulateAtOnce: Int = 200,
    accountIdColName: Option[String],
    withTaId: Boolean,
    data: DelData,
    Logger: SRLogger
  ): Try[Int] = {

    data match {
      case delDataForTeam: DelDataForTeam =>

        val orgId = OrgId(id = delDataForTeam.orgId)
        val teamId = TeamId(id = delDataForTeam.teamId)

        val deleteData = DeleteData(
          orgId = orgId,
          idColName = idColName,
          tableName = tableName
        )

        deleteOrUpdateAllDataStepByStep(
          sqlWhereClause = sqls" tabl.team_id = ${teamId.id} ",
          manipulationData = deleteData,
          accountIdColName = accountIdColName,
          maxRowsToManipulateAtOnce = maxRowsToManipulateAtOnce,
          Logger = Logger,
          idColIsUuid = idColIsUuid
        ).map {
          finalManipulatedCount =>

            Logger.info(s"Done deleting data from table: finalManipulatedCount $finalManipulatedCount")

            finalManipulatedCount
        }

      case delDataForAccount: DelDataForAccount =>

        if(accountIdColName.isDefined && accountIdColName.get.trim.nonEmpty) {
          val accountId = AccountId(id = delDataForAccount.accountId)
          val orgId = OrgId(id = delDataForAccount.orgId)

          if (
            withTaId
          ) {

            handleTableWithAccountIdAndTaId(
              idColName = idColName,
              idColIsUuid = idColIsUuid,
              tableName = tableName,
              maxRowsToManipulateAtOnce = maxRowsToManipulateAtOnce,
              accountIdColName = accountIdColName.get,
              accountId = accountId,
              orgId = orgId,
              Logger = Logger
            )
          } else {

            handleTableWithAccountId(
              idColName = idColName,
              idColIsUuid = idColIsUuid,
              tableName = tableName,
              accountId = accountId,
              orgId = orgId,
              maxRowsToManipulateAtOnce = maxRowsToManipulateAtOnce,
              accountIdColName = accountIdColName.get,
              Logger = Logger
            )
          }
        }else {
          Failure(new Throwable(s"No accountIdColName sent for delete account $data "))
        }
    }
  }

  private def handleTableWithAccountId(
    idColName: String,
    idColIsUuid: Boolean,
    tableName: String,
    accountId: AccountId,
    orgId: OrgId,
    maxRowsToManipulateAtOnce: Int,
    accountIdColName: String,
    Logger: SRLogger
  ): Try[Int] = {

    migrationService.getOwnerAccountIdByOrgId(
      orgId = orgId,
      logger = Logger
    ).flatMap { ownerAccountId =>

      val accountReferenceUpdateData = AccountReferenceUpdateData(
        delete_account_id = accountId,
        org_owner_id = ownerAccountId,
        orgId = orgId,
        idColName = idColName,
        tableName = tableName
      )

      deleteOrUpdateAllDataStepByStep(
        sqlWhereClause = sqls" tabl.${SQLSyntax.createUnsafely(accountIdColName)} = ${accountId.id} ",
        manipulationData = accountReferenceUpdateData,
        accountIdColName = Some(accountIdColName),
        maxRowsToManipulateAtOnce = maxRowsToManipulateAtOnce,
        Logger = Logger.appendLogRequestId(s"aid_${accountId.id}"),
        idColIsUuid = idColIsUuid
      ).map { finalManipulatedCount =>

        Logger.info(s"Done updating data from table $tableName, handleTableWithAccountId: finalManipulatedCount $finalManipulatedCount")

        finalManipulatedCount
      }
    }
  }

  private def handleTableWithAccountIdAndTaId(
    idColName: String,
    tableName: String,
    maxRowsToManipulateAtOnce: Int,
    accountIdColName: String,
    accountId: AccountId,
    orgId: OrgId,
    Logger: SRLogger,
    idColIsUuid: Boolean
  ): Try[Int] = {
    Logger.debug(s"Starting handleTableWithAccountIdAndTaId for tableName - $tableName")

    for {
      ownerInfoList: List[OwnerInfo] <- migrationService.getOwnerInfoList(
          accountId = accountId,
          orgId = orgId,
          logger = Logger
        )

      deleteAccountName: Option[String] <- migrationService.getDeleteAccountInfo(
        accountId = accountId
      )

      res: Seq[Int] <- {

        val updateRes: Seq[Try[Int]] = ownerInfoList.map { o =>

          Logger.debug(s"owner info ---- $o ::: deleteAccountName - $deleteAccountName")

          val accountAndTeamsAccountReferenceUpdateData: AccountAndTeamsAccountReferenceUpdateData = AccountAndTeamsAccountReferenceUpdateData(
            account_id_col_name = accountIdColName,
            delete_account_id = accountId,
            org_owner_id = o.accountId,
            teamId = o.teamId,
            owner_ta_id = o.taId,
            orgId = orgId,
            idColName = idColName,
            tableName = tableName,
            delete_account_name = deleteAccountName.getOrElse(s"accountId - ${accountId.id}")
          )

          deleteOrUpdateAllDataStepByStep(
            sqlWhereClause =
              sqls""" tabl.team_id = ${o.teamId.id}
                    AND (
                    tabl.${SQLSyntax.createUnsafely(accountIdColName)} = ${accountId.id}  OR
                     tabl.ta_id IN (select id from teams_accounts where account_id = ${accountId.id} and team_id = ${o.teamId.id}))""",
            manipulationData = accountAndTeamsAccountReferenceUpdateData,
            accountIdColName = Some(accountIdColName),
            maxRowsToManipulateAtOnce = maxRowsToManipulateAtOnce,
            Logger = Logger.appendLogRequestId(s"aid_${accountId.id} with ta_id "),
            idColIsUuid = idColIsUuid
          ) match {
            case Failure(exception) =>
              Logger.error(s"Failed to Update - tableName: $tableName, colName: ta_id, updateData: $accountAndTeamsAccountReferenceUpdateData ", err = exception)
              Failure(exception)

            case Success(updateCount) =>
              Logger.info(s"Done updating data from table: updateCount $updateCount")
              Success(updateCount)
          }
        }

        Helpers.seqTryToTrySeq(updateRes)
      }
    } yield {

      val totalUpdateCount = res.sum

      Logger.info(s"Total Update Count for tableName: $tableName handleTableWithAccountIdAndTaId: updateCount $totalUpdateCount")

      totalUpdateCount
    }
  }

  // if there are a lot of rows, the deletion process can take time or even stall.
  // so we will delete it step by step, e.g. 1000 prospects at a time.
  // this limit comes from the maxDeleteRowsAtOnce param from above
  @tailrec
  private def deleteOrUpdateAllDataStepByStep(
    totalManipulatedCount: Int = 0,
    idToDeleteFrom: Long = 0,
    sqlWhereClause: SQLSyntax,
    idColIsUuid: Boolean,
    manipulationData: CommonManipulationData,
    accountIdColName: Option[String],
    maxRowsToManipulateAtOnce: Int,
    Logger: SRLogger
  ): Try[Int] = {

    migrationService.getIdsForManipulation(
      idColName = manipulationData.idColName,
      tableName = manipulationData.tableName,
      maxRowsToManipulateAtOnce = maxRowsToManipulateAtOnce,
      sqlWhereClause = sqlWhereClause,
      accountIdColName = accountIdColName,
      orgId = manipulationData.orgId,
      logger = Logger,
      idColIsUuid = idColIsUuid,
      idToDeleteFrom = idToDeleteFrom
    ) match {
      case Failure(exception) =>
        Logger.error(s"Failed to getIdsForDeleting, tableName: ${manipulationData.tableName}, whereClause: $sqlWhereClause, maxRowsToManipulateAtOnce: $maxRowsToManipulateAtOnce", err = exception)
        Failure(exception)

      case Success(idsForManipulationInThisCycle) =>

        if (idsForManipulationInThisCycle.isEmpty) {

          if (totalManipulatedCount == 0) {
            Logger.info(s"no rows found for deleting from ${manipulationData.tableName} table")
          } else {
            Logger.info(s"no further rows for deleting/updating from ${manipulationData.tableName} :: totalManipulatedCount: $totalManipulatedCount")
          }

          Success(totalManipulatedCount)

        } else {

          val tryOfManipulatedThisTime: Try[Int] = manipulationData match {

            case deleteData: DeleteData =>
              Logger.info(s" deleting from ${manipulationData.tableName} ... found ${idsForManipulationInThisCycle.length} ${if (totalManipulatedCount > 0) "more " else ""}rows")

              migrationService.deleteFromTable(
                idColName = deleteData.idColName,
                tableName = deleteData.tableName,
                delSqlWhereClause = sqlWhereClause,
                idsForDeleting = idsForManipulationInThisCycle,
                logger = Logger
              )

            case accountReferenceUpdateData: AccountReferenceUpdateData =>
              Logger.info(s" updating from ${manipulationData.tableName} Only account_id id update... found ${idsForManipulationInThisCycle.length} ${if (totalManipulatedCount > 0) "more " else ""}rows")

              if(accountIdColName.isDefined && accountIdColName.get.trim.nonEmpty){
                migrationService.updateDataForTableWithOnlyAccountId(
                  updateAccountData = accountReferenceUpdateData,
                  idsForDeleting = idsForManipulationInThisCycle,
                  accountIdColName = accountIdColName.get,
                )
              } else  Failure(new Throwable(s"No accountIdColName sent for delete account $manipulationData "))


            case accountAndTeamsAccountReferenceUpdateData: AccountAndTeamsAccountReferenceUpdateData =>

              Logger.info(s" updating from ${manipulationData.tableName} account_id and ta_id id update... found ${idsForManipulationInThisCycle.length} ${if (totalManipulatedCount > 0) "more " else ""}rows")

              migrationService.updateDataForTableWithAccountIdAndTaId(
                updateAccountAndTeamsAccountData = accountAndTeamsAccountReferenceUpdateData,
                idsForDeleting = idsForManipulationInThisCycle,
              )

          }

          tryOfManipulatedThisTime match {
            case Failure(exception) =>
              Logger.error(s"Failed tryOfManipulatedThisTime, tableName: ${manipulationData.tableName}, whereClause: $sqlWhereClause, idsForDeleting: $idsForManipulationInThisCycle", err = exception)
              Failure(exception)

            case Success(manipulatedThisTime) =>

              val totalManipulatedTillNow = totalManipulatedCount + manipulatedThisTime

              if(idsForManipulationInThisCycle.distinct.length != totalManipulatedCount) {
                Logger.fatal(s"idsForManipulationInThisCycle(${idsForManipulationInThisCycle.distinct.length}) count not the same as totalManipulatedCount($totalManipulatedCount) manipulationData -- $manipulationData --- idsForManipulationInThisCycle --- $idsForManipulationInThisCycle")
              }
              Logger.info(s"Total manipulated tableName: ${manipulationData.tableName} till now: $totalManipulatedTillNow")

              if((idsForManipulationInThisCycle.length > manipulatedThisTime) && manipulatedThisTime == 0) {
                Logger.shouldNeverHappen(s"Failed to manipulate data ${manipulationData.toString} where clause - ${sqlWhereClause.value} where clause parameter - ${sqlWhereClause.parameters}")
                Failure(new Throwable("Failed to manipulate data for found ids"))
              } else {
                deleteOrUpdateAllDataStepByStep(
                  totalManipulatedCount = totalManipulatedTillNow,
                  sqlWhereClause = sqlWhereClause,
                  manipulationData = manipulationData,
                  accountIdColName = accountIdColName,
                  maxRowsToManipulateAtOnce = maxRowsToManipulateAtOnce,
                  Logger = Logger,
                  idColIsUuid = idColIsUuid,
                  idToDeleteFrom = SrIdentifier.getIdToDeleteFrom(ids = idsForManipulationInThisCycle, idColIsUuid = idColIsUuid)
                )
              }

          }
        }
    }
  }

  private def printEmptyLine = logger.info("=================")

  private def deleteOrg(
    orgId: Long,
    orgName: String,
    orgOwnerEmail: String,
    Logger: SRLogger
  ) = Try {

    def log(msg: String) = {
      printEmptyLine
      Logger.info(msg)
      printEmptyLine

    }

    val orgOwnerCheck: Option[String] = DB.readOnly { implicit session =>
      sql"select email from accounts acc join organizations org on acc.id = org.owner_account_id where org.id = $orgId and name = $orgName"
        .map(_.string("email"))
        .single
        .apply()
    }

    if (orgOwnerCheck.isEmpty) {
      throw new Exception("orgOwnerCheck is empty: org not found")
    } else if (orgOwnerCheck.get != orgOwnerEmail) {
      throw new Exception(s"orgOwnerEmail not matching: found email: $orgOwnerCheck")
    } else {

      val orgTeams: Seq[(String, Long)] = DB.readOnly { implicit session =>
        sql"select id, name from teams t where t.org_id = $orgId"
          .map(rs => (rs.string("name"), rs.long("id")))
          .list
          .apply()
      }


      log(s"teams found: $orgTeams")


      orgTeams.foreach { case (teamName, teamId) =>

        log(s"deleting all data in team: $teamName ($teamId)")

        // case DelDataForTeam: we will delete related data.
        deletData(
          data = DelDataForTeam(
            teamId = teamId,
            teamName = teamName,
            orgId = orgId
          ),
          Logger = Logger
        ).get


        DB.localTx { implicit session =>

          log(s"deleting teams_accounts in team: $teamName ($teamId)")

          sql"delete from teams_accounts ta where ta.team_id = $teamId"
            .update
            .apply()

          log(s"deleting team: $teamName ($teamId)")

          sql"delete from teams t where t.id = $teamId"
            .update
            .apply()

        }

      }


      DB.localTx { implicit session =>


        log("setting owner_account_id to null")

        sql"update organizations set owner_account_id = null where id = $orgId"
          .update
          .apply()

        log("deleting accounts in org")

        sql"delete from accounts a where a.org_id = $orgId"
          .update
          .apply()


        log("deleting org")


        sql"delete from organizations where id = $orgId"
          .update
          .apply()

      }

      log("deleted org")




    }


  }


  def getInactiveOldOrgsForDeletion(): Try[List[MqOrgDataDeleterMsg]] = Try {

    DB.readOnly { implicit session =>

      sql"""
           	select
              org.id as org_id,
              org.name as org_name,
              a.email as org_owner_email

              from organizations org
              join accounts a on a.id = org.owner_account_id
              where org.plan_type = 'inactive'

              and 
              (org.data_last_deleted_at IS NULL
              OR
              org.data_last_deleted_at < now() - interval '${SQLSyntax.createUnsafely(SRAppConfig.inactive_org_delete_after_months.toString)} months'
              )

              and org.current_cycle_started_at < now() - interval '${SQLSyntax.createUnsafely(SRAppConfig.inactive_org_delete_after_months.toString)} months'
              and org.id NOT IN (1, 19, 20, 21, 24, 32, 62, 70, 77, 388, 2154, 2444)
              order by org.id asc
              LIMIT 1000
              ;
         """
        .map(rs => (

          MqOrgDataDeleterMsg(
            orgId = rs.long("org_id"),
            orgOwnerEmail = rs.string("org_owner_email"),
            orgName = rs.string("org_name")
          )

        ))
        .list
        .apply()

    }
  }


  def checkIfOrgDataIsDeleted(
                               orgId: OrgId
                             ): Try[Boolean] = Try {

    DB.readOnly { implicit session =>

      sql"""
             select
              org.data_last_deleted_at

              from organizations org
              where org.plan_type = 'inactive'
              and 
              org.data_last_deleted_at > now() - interval '${SQLSyntax.createUnsafely(SRAppConfig.inactive_org_delete_after_months.toString)} months'
              and org.id = ${orgId.id}

              ;
         """
        .map(rs => rs.jodaDateTimeOpt("data_last_deleted_at"))
        .single
        .apply()
        .flatten
        .isDefined
        

    }
  }

  private def updateLastDeletedAtTimestampForOrg(
    orgId: Long
  ): Try[Int] = Try {

    DB.autoCommit { implicit session =>

      sql"update organizations set data_last_deleted_at = now() where id = $orgId"
        .update
        .apply()

    }
  }

  def deleteDataInOrganizationForInactiveOrgs(
    orgId: Long,
    orgName: String,
    orgOwnerEmail: String,
    logRequestId: String,
    Logger: SRLogger
  ): Try[Long] = Try {

    def log(msg: String): Unit = {
      // printEmptyLine

      Logger.info(s"$logRequestId $msg")

      // printEmptyLine

    }

    val orgOwnerCheck: Option[OrgInfoForDeleteInactiveData] = DB.readOnly { implicit session =>
      sql"""
          select
            acc.email,
            org.current_cycle_started_at,
            org.plan_type
          from accounts acc
          join organizations org on acc.id = org.owner_account_id
          where org.id = $orgId
            and org.name = $orgName
            and
              (org.data_last_deleted_at IS NULL
              OR
              org.data_last_deleted_at < now() - interval '${SQLSyntax.createUnsafely(SRAppConfig.inactive_org_delete_after_months.toString)} months'
              )
            and org.plan_type = ${PlanType.INACTIVE.toString}
          """
        .map(rs => {
          OrgInfoForDeleteInactiveData(
            owner_email = rs.string("email"),
            current_cycle_started_at = rs.jodaDateTime("current_cycle_started_at"),
            plan_type = PlanType.withName(rs.string("plan_type")).get
          )
        })
        .single
        .apply()
    }

    if (orgOwnerCheck.isEmpty) {

      throw new Exception("orgOwnerCheck is empty: org not found")

    } else if (orgOwnerCheck.get.owner_email != orgOwnerEmail) {

      throw new Exception(s"orgOwnerEmail not matching: found email: $orgOwnerCheck")

    } else if (orgOwnerCheck.get.plan_type != PlanType.INACTIVE) {

      throw new Exception(s"not inactive org: $orgOwnerCheck")

    } else if (
      orgOwnerCheck.get.current_cycle_started_at
        .isAfter(
          DateTime.now().minusMonths(SRAppConfig.inactive_org_delete_after_months)
        )
    ) {

      throw new Exception(s"active in last ${SRAppConfig.inactive_org_delete_after_months} months: $orgOwnerCheck")


    } else {

      val orgTeams: Seq[(String, Long)] = DB.readOnly { implicit session =>
        sql"select id, name from teams t where t.org_id = $orgId"
          .map(rs => (rs.string("name"), rs.long("id")))
          .list
          .apply()
      }


      log(s"teams found: $orgTeams")


      orgTeams.foreach { case (teamName, teamId) =>

        log(s"deleting all data in team: $teamName ($teamId)")

        // case DelDataForTeam: we will delete related data.
        deletData(
          data = DelDataForTeam(
            teamId = teamId,
            teamName = teamName,
            orgId = orgId
          ),
          Logger = Logger,
          delete_prospect = false
        ).get


      }


      log("deleted org")

      updateLastDeletedAtTimestampForOrg(
        orgId = orgId
      ).get


      orgId


    }


  }

  private def accountBelongsToOrg(
    orgId: Long,
    accountId: Long
  ): Boolean  = DB.readOnly { implicit session =>

    sql"select id from accounts where org_id = $orgId and id = $accountId"
      .map(_.long("id"))
      .single
      .apply()
      .isDefined

  }

  private def isOrgAgency(
    orgId: Long
  ): Boolean  = DB.readOnly { implicit session =>

    sql"select is_agency from organizations o where o.id = $orgId"
      .map(_.boolean("is_agency"))
      .single
      .apply()
      .get

  }

  private def checkIfOrgOwner(
    orgId: Long,
    ownerId: Long
  ): Boolean  = DB.readOnly { implicit session =>

    sql"select id from organizations o where o.id = $orgId AND o.owner_account_id = $ownerId"
      .map(_.long("id"))
      .single
      .apply()
      .isDefined

  }

  // use carefully
  private def transferOwnership(
    oldOwnerId: Long,
    newOwnerId: Long,
    orgId: Long
  ): Try[Boolean] = Try {

    if (isOrgAgency(orgId = orgId)) {

      throw new Exception("this script doesnt support agency transfer ownership")

    } else if (!accountBelongsToOrg(orgId = orgId, accountId = oldOwnerId)) {

      throw new Exception("oldOwnerId doesnt belong to org")

    } else if (!accountBelongsToOrg(orgId = orgId, accountId = newOwnerId)) {

      throw new Exception("newOwnerId doesnt belong to org")

    } else if (!checkIfOrgOwner(orgId = orgId, ownerId = oldOwnerId)) {

      throw new Exception("oldOwnerId is not owner of org")

    } else {

      val allTeamAccountRoles: Seq[RolePermissionDataV2] = rolePermissionDataDAOV2.findAllRoles(orgId = orgId).get

      DB.localTx { implicit session =>

        def updateTeamAccountRole(
          newRole: TeamAccountRole,
          accountId: Long
        ) = {

          val newRoleId: Long = allTeamAccountRoles.find(r => r.role_name == newRole).get.id

          sql"""
        update teams_accounts set

          role = ${newRole.toString},
          user_role_id = $newRoleId

        where account_id = $accountId
        """
            .update.apply()
        }


        def updateAccountOrgRole(
          orgRole: Option[OrganizationRole],
          accountId: Long
        ) = {

          sql"update accounts set org_role = ${orgRole.map(_.toString)} where id = $accountId AND org_id = $orgId"
            .update.apply()
        }

        updateAccountOrgRole(
          accountId = oldOwnerId,
          orgRole = None
        )

        updateAccountOrgRole(
          accountId = newOwnerId,
          orgRole = Some(OrganizationRole.OWNER)
        )

        updateTeamAccountRole(
          accountId = oldOwnerId,
          newRole = TeamAccountRole.ADMIN
        )

        updateTeamAccountRole(
          accountId = newOwnerId,
          newRole = TeamAccountRole.OWNER
        )


        sql"update organizations org set owner_account_id = $newOwnerId where org.id = $orgId"
          .update.apply()

      }


      checkIfOrgOwner(orgId = orgId, ownerId = newOwnerId)

    }



  }

  def deleteAccount(account: AccountV2, Logger: SRLogger): Either[DeleteAccountError, Boolean] = {
   if(!AuthUtils.__isOrgOwner(account)){

     // case DelDataForAccount: we will update related data.
     deletData(
       DelDataForAccount(
         accountId = account.id,
         email = account.email,
         orgId = account.org_id
       ),
       Logger = Logger
     ) match {
       case Failure(err) =>
         Left(DeleteAccountError.SQLErrorWhileDeletingAccountData(err))
       case Success(_) =>
         Right(true)
     }
   }else {
     Left(DeleteAccountError.AccountOfOwner)
   }
  }

  def deleteOrgForSupportApp (organization: Organization,
                              Logger: SRLogger
                             ):Either[DeleteOrgError, Boolean] = {

    val accountTry = accountDAOV2.getAdminAccountByOrgId(org_id = organization.id)

    accountTry match {
      case Failure(err) =>
        Left(DeleteOrgError.SQLErrorWhileFindingAccount(err))

      case Success(None) =>
        Left(DeleteOrgError.NoOwnerFoundError)

      case Success(Some(account)) =>
        checkIfOrgDataIsDeleted(orgId = OrgId(organization.id)) match {
          case Failure(exception) =>         
            Left(DeleteOrgError.SQLErrorWhileCheckingIfAlreadyDeleted(exception))

          case Success(true) =>
            Left(DeleteOrgError.OrgIsDeletedBefore)

          case Success(false) =>
            deleteOrg(
              orgId = organization.id,
              orgName = organization.name,
              orgOwnerEmail = account.email,
              Logger = Logger
            ) match {
              case Failure(err) =>
                Left(DeleteOrgError.SQLErrorWhileDeletingOrg(err))

              case Success(_) =>
                Right(true)
            }
        }


    }
  }

  private def getEmailThreadBooleanValues(
                                           teamId: Long,
                                           threads_start_date: DateTime,
                                           threads_end_date: DateTime
                                         )(implicit session: DBSession): Try[List[ExistingDataEmailThread]] = Try {
    sql"""
          select distinct et.id,
          et.archived,
          et.snoozed,
          (case when etp.prospect_id is null then false else true end) as is_prospect,
          es.bounced as bounced

          from email_threads et

          inner join emails_scheduled es on et.team_id = es.team_id and es.email_thread_id = et.id and es.id = et.latest_email_id
          left join email_threads_prospects etp on etp.email_thread_id = et.id
          where et.team_id = ${teamId}
          and et.created_at <= $threads_start_date
          and et.created_at > $threads_end_date
          ;
        """
      .map(rs => ExistingDataEmailThread(
        id = rs.long("id"),
        archived = rs.boolean("archived"),
        snoozed = rs.boolean("snoozed"),
        is_prospect = rs.boolean("is_prospect"),
        bounced = rs.boolean("bounced")
      ))
      .list
      .apply()
  }

  private def updateInboxFolderForMigration(
                                             threads: List[(Long, FolderType)],
                                             teamId: Long
                                           )(implicit session: DBSession): Try[List[Long]] = Try {

    var valueParameters = List[Any]()

    val valuePlaceholder: SQLSyntax = threads
      .map(t => {

        valueParameters = valueParameters ::: List(
          t._1,
          t._2.toString,

          teamId
        )

        sqls"""
           (
              ?,
              ?,
              ?
            )
          """

      })
      .reduce((vp1, vp2) => sqls"$vp1, $vp2")


    sql"""
          UPDATE email_threads et
          SET
            folder_type = temp.folder_type
            FROM (
                  VALUES $valuePlaceholder
                )
                AS temp(
                  id,
                  folder_type,

                  team_id
                )
          WHERE et.id = temp.id
          AND et.team_id = temp.team_id

          RETURNING et.id
          ;
      """
      .bind(valueParameters*)
      .map(_.long("id"))
      .list
      .apply()

  }

  def migrateInboxFolders(
                           team_ids_to_be_migrated: List[Long]
                         )(using logger: SRLogger) = {


    implicit val system: ActorSystem = ActorSystem()
    implicit val materializer: Materializer = Materializer(system)
    implicit val wSClient: AhcWSClient = AhcWSClient()
    implicit val ec = Helpers.genFixedThreadPoolEC(threads = 10)
        team_ids_to_be_migrated.map(team_id => {

          println(s"migrateInboxFolders::::Migration started for teamId: ${team_id}")

          var start_date: DateTime = DateTime.now()
          var end_date: DateTime = start_date.minusDays(15)
          val final_date: DateTime = DateTime.now().minusMonths(7)

          var updated_thread_ids: Seq[Long] = Seq()
          var total_updated_threads: Long = 0
          var result: Try[Seq[Long]] = Success(Seq())
          var futureRes: Future[Seq[Long]] = Future.successful(Seq())

          while (start_date.isAfter(final_date)) {

            result = Try {
              val updated_threads: Seq[Long] = DB localTx { implicit session =>
                //1. Get existing booleans from db (id, archived, snoozed, is_prospect)
                val email_thread_ids: List[ExistingDataEmailThread] = getEmailThreadBooleanValues(
                  teamId = team_id,
                  threads_start_date = start_date,
                  threads_end_date = end_date
                ) match {
                  case Failure(exception) => throw exception
                  case Success(res) =>
                    start_date = start_date.minusDays(15)
                    end_date = start_date.minusDays(15)
                    res
                }

                if (email_thread_ids.nonEmpty) {


                  println(s"migrateInboxFolders::::Number of threads to be updated for teamId_$team_id: ${email_thread_ids.length}")

                  //2. Create map (id -> folder_type)

                  // If archived then DONE else if snoozed SNOOZED else if is_prospect PROSPECT else NON_PROSPECT

                  val email_threads: List[(Long, FolderType)] = email_thread_ids.map(data => {
                    if (data.archived) {
                      (data.id, FolderType.DONE)
                    } else if (data.snoozed) {
                      (data.id, FolderType.SNOOZED)
                    } else if(data.bounced) {
                      (data.id, FolderType.IRRELEVANT)
                    } else if (data.is_prospect) {
                      (data.id, FolderType.PROSPECTS)
                    } else {
                      (data.id, FolderType.NON_PROSPECTS)
                    }
                  })

                  val all_updated_threads: Seq[Long] = email_threads
                    .grouped(1000)
                    .flatMap(threads => {

                      //3. Update folder_type in db
                      val updated_threads: List[Long] = updateInboxFolderForMigration(
                        threads = threads,
                        teamId = team_id
                      ) match {
                        case Failure(exception) => throw exception
                        case Success(res) =>
                          total_updated_threads = total_updated_threads + res.length
                          res
                      }
                      updated_threads
                    })
                    .toSeq

                  println(s"migrateInboxFolders::::Number of threads updated for teamId_${team_id} successfully in duration $start_date to $end_date : ${total_updated_threads}")

                  all_updated_threads
                } else {
                  println(s"migrateInboxFolders:::: 0 threads found for teamId_${team_id} in duration $start_date to $end_date")
                  Seq()
                }
              }

              updated_thread_ids = updated_thread_ids ++ updated_threads
              updated_thread_ids
            }

            futureRes = Future.fromTry(result)

            Await.result(
              futureRes,
              Duration.create(100, SECONDS)
            )

            futureRes.onComplete {
              case Failure(exception) =>
                println(s"migrateInboxFolders::::Error while migration of teamId_${team_id}:: ${exception.getMessage}")
              case Success(updatedThreads) =>
                println(s"migrateInboxFolders::::Migration done for ${total_updated_threads} number of threads for teamId_${team_id}")
            }
          }
        }
      )
    }

  def migrateUuidsForTable(
                            table_name: TableNames,
                            team_id: Long
                          )(using logger: SRLogger): Either[RecursiveMigrationMethodError, CountOfMigration] = {

    logger.info(s"migrateEmailsScheduledUuid::::Migration started for teamId: ${team_id}")

    def getTableIds(
                     ids_to_update_from: IdsToUpdate,
                     count_per_cycle: CountOfMigration,
                     table_name: TableNames
                   ): Try[List[IdsToUpdate]] = Try {
      DB readOnly { implicit session =>

        sql"""
          select tb.id
          from ${table_name.toSQL} tb
          where tb.team_id = ${team_id}
          and tb.id > ${ids_to_update_from.id}
          ORDER BY id ASC
          LIMIT ${count_per_cycle.count}
          ;
        """
          .map(rs => IdsToUpdate(rs.long("id")))
          .list
          .apply()
      }
    }

    def updateTableUuids(
                         ids: List[IdsToUpdate],
                         table_name: TableNames
                       ): Try[CountOfMigration] = Try {

      var valueParameters = List[Any]()

      val valuePlaceholder: SQLSyntax = ids
        .map(id => {

          val uuid = migrationFunc.getUuidFunction(table_name = table_name, data = IdTeamId(id = 0, teamId = Option(0)))

          valueParameters = valueParameters ::: List(
            id.id,
            uuid,

            team_id
          )

          sqls"""
           (
              ?,
              ?,
              ?
            )
          """

        })
        .reduce((vp1, vp2) => sqls"$vp1, $vp2")

      val count = DB autoCommit { implicit session =>
        sql"""
          UPDATE ${table_name.toSQL} tb
          SET
            uuid = temp.uuid
            FROM (
                  VALUES $valuePlaceholder
                )
                AS temp(
                  id,
                  uuid,

                  team_id
                )
          WHERE tb.id = temp.id
          AND tb.team_id = temp.team_id

          RETURNING tb.id
          ;
      """
          .bind(valueParameters*)
          .map(_.long("id"))
          .list
          .apply()
          .length
      }

      CountOfMigration(count)

    }

    MigrationRecursiveService.recursiveMigrationMethod(
      getIdsToMigrateMethod = getTableIds,
      updateTheIds = updateTableUuids,
      total_updated = CountOfMigration(0),
      ids_to_update_from = IdsToUpdate(0),
      count_per_cycle = CountOfMigration(1000),
      table_name = table_name
    )

  }


  private def selectEmailThreadsAndUpdatedAt(
                                              team_id: Long,
                                              threads_start_date: DateTime,
                                              threads_end_date: DateTime
                                            ): Try[List[EmailThreadsUpdatedAt]] = Try {
    DB.readOnly { implicit session =>

      sql"""
        select id, coalesce(latest_reply_at, created_at) as updated_at
        from email_threads

        where team_id = $team_id
        and updated_at is null
        and created_at <= $threads_start_date
        and created_at > $threads_end_date
        ;
      """
        .map(rs => EmailThreadsUpdatedAt(
          id = rs.long("id"),
          updated_at = rs.jodaDateTime("updated_at")
        ))
        .list
        .apply()
    }
  }

  private def updateUpdatedAtColumnInEmailThreads(
                                                   threads: List[EmailThreadsUpdatedAt],
                                                   team_id: Long
                                                 ): List[Long] = {

    var valueParameters = List[Any]()

    val valuePlaceholder: SQLSyntax = threads
      .map(t => {

        valueParameters = valueParameters ::: List(
          t.id,
          t.updated_at,

          team_id
        )

        sqls"""
          (
          ?,
          ?,
          ?
          )
          """

        })
        .reduce((vp1, vp2) => sqls"$vp1, $vp2")

    DB.autoCommit { implicit session =>

      sql"""
        UPDATE email_threads et
        SET
        updated_at = temp.updated_at::timestamptz
        FROM (
        VALUES $valuePlaceholder
        )
        AS temp(
        id,
        updated_at,

        team_id
        )
        WHERE et.id = temp.id
        AND et.team_id = temp.team_id

        RETURNING et.id
        ;
      """
        .bind(valueParameters*)
        .map(_.long("id"))
        .list
        .apply()
    }
  }

  def migrateUpdatedAtEmailThreads(
                                    team_ids_to_be_migrated: List[Long]
                                  ): Unit = {

    implicit val system: ActorSystem = ActorSystem()
    implicit val materializer: Materializer = Materializer(system)
    implicit val wSClient: AhcWSClient = AhcWSClient()
    implicit val ec: ExecutionContext = Helpers.genFixedThreadPoolEC(threads = 10)

    var iteration_num_for_tid = 0

    team_ids_to_be_migrated.foreach(tid => {
      val tid_res: Future[Try[Int]] = Future {
        iteration_num_for_tid = iteration_num_for_tid + 1

        var start_date: DateTime = DateTime.now()
        var end_date: DateTime = start_date.minusDays(1)
        val final_date: DateTime = DateTime.now().minusMonths(6)
        var iteration_num_for_while_loop: Int = 0
        var email_threads_updated_till_now: Int = 0

        @tailrec
        def selectAndUpdateThreads(
                                    total_email_threads_updated: Int = 0
                                  ): Try[Int] = {

          email_threads_updated_till_now = total_email_threads_updated

          iteration_num_for_while_loop = iteration_num_for_while_loop + 1

          if (start_date.isBefore(final_date)) {

            Success(total_email_threads_updated)
          } else {

            val selectAndUpdateRes: Try[Seq[Long]] = for {
              email_threads: List[EmailThreadsUpdatedAt] <- selectEmailThreadsAndUpdatedAt(
                team_id = tid,
                threads_start_date = start_date,
                threads_end_date = end_date
              )

              update_result <- Try {
                if (email_threads.nonEmpty) {
                  println(s"Email threads to be updated length for tid_${tid} : ${email_threads.length}")

                  val res: Seq[Long] = email_threads
                    .grouped(500)
                    .flatMap(threadGroup => {
                      val threadsUpdated: List[Long] = updateUpdatedAtColumnInEmailThreads(
                        threads = threadGroup,
                        team_id = tid
                      )

                      threadsUpdated
                    }).toSeq

                  res
                } else {
                  Seq()
                }
              }

            } yield {
              update_result
            }

            selectAndUpdateRes match {
              case Failure(exception) =>
                println(s"Error migration failed: ${exception.getMessage}")
                Failure(exception)
              case Success(total_updated) =>
                email_threads_updated_till_now = email_threads_updated_till_now + total_updated.length
                println(s"Iteration ${iteration_num_for_tid} for tid_${tid} completed ${iteration_num_for_while_loop} loops and updated threads = ${total_updated.length}")

                start_date = start_date.minusDays(1)
                end_date = start_date.minusDays(1)
                selectAndUpdateThreads(email_threads_updated_till_now)
            }

          }
        }

        val migrationRes = selectAndUpdateThreads()

        if(start_date.isAfter(final_date)){
          migrationRes
        } else {
          Success(email_threads_updated_till_now)
        }

      }

      Await.result(
        tid_res,
        scala.concurrent.duration.Duration.Inf
      )


      tid_res.map {
        case Failure(exception) =>
          println(s"Update failed for tid: ${exception.getMessage}")
        case Success(t) =>
          println(s"........Migration completed for tid_${tid} and total threads updated: ${t} ..........")
      }
    })
  }

  // 13 march 2024: Commenting out this since it is currently not getting used any where
//  def campaignWiseMigrationForCampaignProspectStepId(teamId: TeamId)(using Logger: SRLogger): Try[List[Unit]] = Try {
//
//    val campaignsForTeam: List[CampaignId] = DB readOnly { implicit session =>
//      sql"""
//              select id from campaigns
//              where team_id = ${teamId.id}
//            """
//        .map(rs => CampaignId(rs.long("id")))
//        .list.apply()
//
//    }
//
//    Logger.debug(s"Found Given Campaign for tid_${teamId.id} cids - ${campaignsForTeam.map(_.id)}")
//
  //    val dbAndSession = dbUtils.startLocalTx()
//    val db = dbAndSession.db
//    implicit val session: DBSession = dbAndSession.session
//    campaignsForTeam map { campaignId =>
//
//
//      val prospect_id_with_wrong_step_id = {
//
//        sql"""
//               select cp.prospect_id
//               from campaigns_prospects cp
//               where cp.campaign_id = ${campaignId.id}
//               and cp.current_step_id is null
//               and cp.team_id = ${teamId.id}
//               and exists  (
//               select *
//               from emails_scheduled es
//               where es.campaign_id = cp.campaign_id
//               and es.prospect_id = cp.prospect_id
//               AND es.scheduled_from_campaign
//               AND es.sent
//               AND es.team_id = cp.team_id
//               )
//               FOR UPDATE SKIP LOCKED ;
//
//             """
//          .map(rs => ProspectId(rs.long("prospect_id")))
//          .list
//          .apply()
//      }
//      // Take only running campaign or stopped
//      // stopped => stopped within 3 months
//
//
//      Logger.debug(s"Found Given prospect_id for tid_${teamId.id} and cid_${campaignId.id} total found - ${prospect_id_with_wrong_step_id.length} pids -> ${prospect_id_with_wrong_step_id.map(_.id)}")
//
//      var totalUpdated = 0
//      if (prospect_id_with_wrong_step_id.isEmpty) {
//
//      } else {
//        campaignProspectDAO._revertCurrentStepIdOnDeactivatingCampaign(campaignId = campaignId.id, prospectsIdsToRevert = prospect_id_with_wrong_step_id.map(_.id),
//          reverted_by = "campaignWiseMigrationForCampaignProspectStepId"
//        )
//
//        Logger.debug(s"Found Given prospect_id with correct step id for tid_${teamId.id} and cid_${campaignId.id}")
//
//
//      }
//
//      dbUtils.closeSession(db = db)
//      Logger.debug(s"Total prospect found for this cycle -- ${prospect_id_with_wrong_step_id.length} total updated --- $totalUpdated")
//
//    }
//  }




  def replySentimentAdditionForTeam(
                                     teamId: TeamId
                                   )(using Logger: SRLogger): Try[Seq[Option[Long]]]  =  {

    val dbAndSession = dbUtils.startLocalTx()
    val db = dbAndSession.db
    val session = dbAndSession.session

    val add_reply_sentiments: Try[Seq[Option[Long]]] = prospectDAO.getProspectCategories(
      teamId = teamId
    ) match {

      case Failure(err) =>

        Logger.shouldNeverHappen(s"replySentimentAdditionForTeam prospect_category :: teamId: ${teamId} ",Some(err))

        Failure(err)

      case Success(p_cats) =>

        val result: Try[Seq[Option[Long]]] = Helpers.seqTryToTrySeq {
          TeamInboxService.defaultReplySentimentTypes(
            prospect_categories = p_cats,
            newReplySentimentsEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
              teamId = teamId,
              feature = SrRollingUpdateFeature.NewReplySentiments
            )
          ).map { replySentimentType =>
            replySentimentDAOService.addReplySentimentsForATeam(
              team_id = teamId.id,
              replySentimentType = replySentimentType,
              uuid = srUuidUtils.generateReplySentimentUuid()
            )(session)
          }
        }

        result



    }

    dbUtils.commitAndCloseSession(db = db)

    add_reply_sentiments

  }

  def completeInProgressConferenceAndCalls()(using Logger: SRLogger):Try[(Int, Int)] = {

    for{

      conference_complete: Int <- Try{

        DB autoCommit  {implicit session =>

        sql"""
             UPDATE call_conference_logs
                SET status = ${CallStatus.COMPLETED.toString}
             WHERE status = ${CallStatus.ACTIVE.toString}
           """
          .update
          .apply()
      }
      }

      call_progress: Int <- Try {

        DB autoCommit { implicit session =>

          sql"""
                 UPDATE call_participants_logs
                    SET status = ${ParticipantCallStatus.COMPLETED.toString}
                 WHERE status = ${ParticipantCallStatus.IN_PROGRESS.toString}
                 """
            .update
            .apply()
        }
      }

    }yield{

      (conference_complete,call_progress)

    }

  }

  def dropEmailsScheduledNotPartOfAnyTeam()(using Logger: SRLogger): Either[RecursiveMigrationMethodError, CountOfMigration] = {

    //fixme: use table_name parameter in query
    def getIdsToDrop(
                      ids_to_update_from: IdsToUpdate,
                      count_per_cycle: CountOfMigration,
                      table_name: TableNames
                    ): Try[List[IdsToUpdate]] = Try{

      DB readOnly {implicit session =>
        sql"""
              select id
              from emails_scheduled
              where team_id is null
              and  id > ${ids_to_update_from.id}
              order by id ASC
              limit ${count_per_cycle.count}
           """
          .map(rs => IdsToUpdate(rs.long("id")))
          .list
          .apply()
      }

    }

    //fixme: use table_name parameter in query
    def delEmailScheduled(
                         ids: List[IdsToUpdate],
                         table_name: TableNames
                         ): Try[CountOfMigration] = Try{

      val count = DB autoCommit{implicit session =>
        sql"""
              delete from emails_scheduled where id in (${ids.map(_.id)}) and team_id is null
           """
          .update
          .apply()
      }

      CountOfMigration(count)

    }


    MigrationRecursiveService.recursiveMigrationMethod(
      getIdsToMigrateMethod = getIdsToDrop,
      updateTheIds = delEmailScheduled,
      total_updated = CountOfMigration(0),
      ids_to_update_from = IdsToUpdate(0),
      count_per_cycle = CountOfMigration(1000),
      table_name = TableNames.EmailsScheduled
    )
  }

  def moveLastContactedAtToLastEmailedAt(team_id: TeamId)(using Logger: SRLogger): Either[RecursiveMigrationMethodError, CountOfMigration] = {


    def getProspectIds(
                      ids_to_update_from: IdsToUpdate,
                      count_per_cycle: CountOfMigration,
                      table_name: TableNames
                    ): Try[List[IdsToUpdate]] = Try {

      DB readOnly { implicit session =>
        sql"""
              SELECT id
              FROM prospects
              WHERE team_id = ${team_id.id}
              AND  id > ${ids_to_update_from.id}
              AND last_emailed_at IS NULL
              AND last_contacted_at IS NOT NULL
              ORDER BY id ASC
              LIMIT ${count_per_cycle.count}
           """
          .map(rs => IdsToUpdate(rs.long("id")))
          .list
          .apply()
      }

    }

    def moveLastContactedAt(
                           ids: List[IdsToUpdate],
                           table_name: TableNames
                         ): Try[CountOfMigration] = Try {

      val count = DB autoCommit { implicit session =>
        sql"""
              UPDATE prospects
              SET
                last_emailed_at = last_contacted_at
              WHERE
                id in (${ids.map(_.id)})
                AND team_id = ${team_id.id}
           """
          .update
          .apply()
      }

      CountOfMigration(count)

    }

    MigrationRecursiveService.recursiveMigrationMethod(
      getIdsToMigrateMethod = getProspectIds,
      updateTheIds = moveLastContactedAt,
      total_updated = CountOfMigration(0),
      ids_to_update_from = IdsToUpdate(0),
      count_per_cycle = CountOfMigration(1000),
      table_name = TableNames.Prospects
    )
  }

  def selectIdAndCreatedAtForProspects(
                                        team_id: Long
                                      )(implicit session: DBSession): Try[List[(Long, DateTime)]] = Try {
    sql"""
           SELECT id, created_at
           FROM
           prospects
           WHERE updated_at is NULL
           and team_id = ${team_id}
           """
      .map(rs => (rs.long("id"), rs.jodaDateTime("created_at")))
      .list
      .apply()
  }

  def updateUpdatedAtForProspect(
                                  prospects: List[(Long, DateTime)],
                                  team_id: Long
                                )(implicit session: DBSession): Try[List[Long]] = Try {
    var valueParameters = List[Any]()

    val valuePlaceholder: SQLSyntax = prospects
      .map(t => {

        valueParameters = valueParameters ::: List(
          t._1,
          t._2,
          team_id
        )

        sqls"""
          (
          ?,
          ?,
          ?
          )
          """

      })
      .reduce((vp1, vp2) => sqls"$vp1, $vp2")

    sql"""
        UPDATE prospects p
        SET
        updated_at = temp.updated_at::timestamptz
        FROM (
        VALUES $valuePlaceholder
        )
        AS temp(
        id,
        updated_at,

        team_id
        )
        WHERE p.id = temp.id
        AND p.team_id = temp.team_id

        RETURNING p.id
        ;
      """
      .bind(valueParameters*)
      .map(_.long("id"))
      .list
      .apply()
  }

  def migrateProspectsUpdatedAt(team_id: Long)(using logger: SRLogger): Try[Int] = {

    var total_ids_updated: Int = 0

    @tailrec
    def migrationScript(team_id: Long): Try[Seq[Long]] = {
      val dbAndSession = dbUtils.startLocalTx()

      val db = dbAndSession.db
      implicit val session = dbAndSession.session

      logger.info(s"prospects updated_at migration script started for team_id ${team_id}")

      val res = for {
        idsToUpdate: List[(Long, DateTime)] <- selectIdAndCreatedAtForProspects(team_id = team_id)

        updatedIds: Seq[List[Long]] <- {
          val res = idsToUpdate
            .grouped(100)
            .map(idGroup => {
              val updatedIds = updateUpdatedAtForProspect(prospects = idGroup, team_id = team_id)
              updatedIds
            }).toSeq
          Helpers.seqTryToTrySeq(res)
        }
      } yield {

        updatedIds

      }
      dbUtils.commitAndCloseSession(db = db)
      res match {

        case Failure(exception) => Failure(exception = exception)

        case Success(seq) => {
          if (seq.isEmpty) {
            logger.info(s"Updated_at migration completed team_id :: ${team_id} total updated ids: ${seq.length}")
            Success(Seq())
          }
          else {
            logger.info(s"Updated_at migration running team_id :: ${team_id} total updated ids: ${seq.length}")
            total_ids_updated = total_ids_updated + seq.length
            migrationScript(team_id = team_id)
          }
        }
      }

    }

    val res = migrationScript(team_id = team_id)

    res match {

      case Failure(exception) => Failure(exception = exception)

      case Success(_) => Success(total_ids_updated)
    }

  }


  def migrationForEmailMessageData(
                                    team_id: TeamId
                                  )(using Logger: SRLogger): Either[RecursiveMigrationMethodError, CountOfMigration] = {

    def getIdsToMigrateMethod(getIdFrom: IdsToUpdate, limit: CountOfMigration, table_name: TableNames): Try[List[IdsToUpdate]] = Try {


      DB readOnly { implicit session =>
        sql"""
             Select es.id
             from emails_scheduled es
             where
             es.team_id = ${team_id.id}
             AND es.id > ${getIdFrom.id}
             AND NOT EXISTS (select es_id from email_message_data ems where es.id = ems.es_id AND ems.team_id = es.team_id)
             order by es.id Asc
             LIMIT ${limit.count}
           """
          .map(rs => IdsToUpdate(rs.long("id")))
          .list
          .apply()
      }

    }

    def updateTheIds(idsToUpdate: List[IdsToUpdate], table_name: TableNames): Try[CountOfMigration] = Try {

      val count = DB autoCommit { implicit session =>

        sql"""
             INSERT INTO email_message_data (
                     es_id,
                    to_email,
                    from_email,
                    reply_to_email,
                    subject,
                    body,
                    gmail_msg_id,
                    gmail_thread_id,
                    original_inbox_folder,
                    outlook_msg_id,
                    outlook_conversation_id,
                    outlook_response_json,
                    from_name,
                    base_body,
                    email_thread_id,
                    inbox_email_setting_id,
                    full_headers,
                    references_header,
                    message_id,
                    in_reply_to_header,
                    cc_emails,
                    bcc_emails,
                    team_id,
                    to_name,
                    text_body,
                    reply_to_name
             )
             SELECT
                   id,
                   to_email,
                   from_email,
                   reply_to_email,
                   subject,
                   body,
                   gmail_msg_id,
                   gmail_thread_id,
                   original_inbox_folder,
                   outlook_msg_id,
                   outlook_conversation_id,
                   outlook_response_json,
                   from_name,
                   base_body,
                   email_thread_id,
                   inbox_email_setting_id,
                   full_headers,
                   references_header,
                   message_id,
                   in_reply_to_header,
                   cc_emails,
                   bcc_emails,
                   team_id,
                   to_name,
                   text_body,
                   reply_to_name
             FROM emails_scheduled
             where id in (${idsToUpdate.map(_.id)})
             AND team_id = ${team_id.id}
           """
          .update
          .apply()
      }

      CountOfMigration(count)

    }

    MigrationRecursiveService.recursiveMigrationMethod(
      getIdsToMigrateMethod = getIdsToMigrateMethod,
      updateTheIds = updateTheIds,
      total_updated = CountOfMigration(0),
      ids_to_update_from = IdsToUpdate(0),
      count_per_cycle = CountOfMigration(1000),
      table_name = TableNames.EmailsScheduled
    )

  }



  def deleteFromEmailMessageDataBefore22Jan()(using Logger: SRLogger): Try[Boolean] = {

    val getIdsToDelete: List[Long] = DB readOnly {implicit session =>
      sql"""
           select emd.es_id
           from email_message_data emd
           inner join emails_scheduled es on es.id = emd.es_id and es.team_id = emd.team_id
            where scheduled_at < '2024-01-22 23:59:59'
            limit 1000;
         """
        .map(rs => rs.long("es_id"))
        .list
        .apply()
    }

    println(s"Deleting from email_message_data es_ids - $getIdsToDelete")

    def delete(ids: List[Long]): Try[Int] = Try{
      DB autoCommit {implicit session =>
        sql"""
               delete from email_message_data
               where es_id in (${ids})
             """
          .update
          .apply()
      }
    }

    if(getIdsToDelete.isEmpty) {
      Success(true)
    } else {
      delete(ids = getIdsToDelete) match {
        case Success(_) =>
          Logger.info(s"Deleted from email_message_data es_ids - $getIdsToDelete")
          deleteFromEmailMessageDataBefore22Jan()
        case Failure(exception) =>
          Failure(exception)
      }
    }
  }

  private case class EmailThreadProspectLatestReplyAt(
                                                       email_thread_id: Long,
                                                       prospect_id: Long,
                                                       latest_reply_at: DateTime
                                                     )

  //only for team_id 16266 (<EMAIL>)
  def migrateSentEmailsFromAdminToSentFolder(
                                              teamId: TeamId
                                            ): Unit = {

    val Logger = new SRLogger(
      logRequestId = s"EmailThreadProspectLatestReplyAt"
    )

    def getEmailThreadsData(): Try[List[EmailThreadProspectLatestReplyAt]] = Try {
      DB readOnly { implicit session =>
        sql"""
               SELECT
                et.id as email_thread_id,
                et.latest_reply_at,
                p.id as prospect_id
                FROM
                email_threads et
                INNER JOIN emails_scheduled es ON es.id = et.latest_email_id
                AND et.team_id = es.team_id
                INNER JOIN prospects p ON p.id = es.prospect_id
                AND p.team_id = es.team_id
                INNER JOIN email_settings eset on eset.id = es.sender_email_settings_id
                WHERE
                et.team_id = ${teamId.id}
                AND NOT es.scheduled_from_campaign
                AND eset.email = es.from_email
                AND et.latest_reply_at is not null;
             """
          .map(rs => EmailThreadProspectLatestReplyAt(
            rs.long("email_thread_id"),
            rs.long("prospect_id"),
            rs.jodaDateTime("latest_reply_at")
          ))
          .list
          .apply()
      }
    }

    def updateLatestSentByAdminAt(
                                   threads: List[EmailThreadProspectLatestReplyAt],
                                   team_id: Long
                                 ): Try[List[Long]] = Try {

      var valueParameters = List[Any]()

      val valuePlaceholder: SQLSyntax = threads
        .map(t => {

          valueParameters = valueParameters ::: List(
            t.email_thread_id,
            t.latest_reply_at,

            team_id
          )

          sqls"""
          (
          ?,
          ?,

          ?
          )
          """

        })
        .reduce((vp1, vp2) => sqls"$vp1, $vp2")

      DB.autoCommit { implicit session =>

        sql"""
        UPDATE email_threads et
        SET
        latest_sent_by_admin_at = temp.latest_reply_at::timestamptz,
        latest_reply_at = null
        FROM (
        VALUES $valuePlaceholder
        )
        AS temp(
        email_thread_id,
        latest_reply_at,

        team_id
        )
        WHERE et.id = temp.email_thread_id
        AND et.team_id = temp.team_id

        RETURNING et.id
        ;
      """
          .bind(valueParameters*)
          .map(_.long("id"))
          .list
          .apply()
      }
    }


    /**
     * 1. select all email_thread ids and latest_reply_at
     * for which the prospect_id is not having record in campaigns-prospects
     * and eset (sender_email_settings_id) is not present in campaign_email_settings
     */


    getEmailThreadsData() match {
      case Failure(exception) =>
        Logger.error(s"getEmailThreadsData failed: ${LogHelpers.getStackTraceAsString(exception)}")
      case Success(threads) =>
        Logger.success(s"getEmailThreadsData success: total threads found: ${threads.length}")

        /**
         * 2. update latest_sent_by_admin_at = latest_reply_at and latest_reply_at = null
         */

        threads
          .grouped(500)
          .toList
          .foreach(t => {
            updateLatestSentByAdminAt(
              threads = threads,
              team_id = teamId.id
            ) match {
              case Failure(exception) =>
                Logger.error(s"updateLatestSentByAdminAt failed: ${LogHelpers.getStackTraceAsString(exception)}")

              case Success(updated_ids) =>
                Logger.success(s"updateLatestSentByAdminAt success: total threads updated: ${updated_ids.length}")

            }
          })



    }

  }

//  def migrateProspectsToCorrectCategory(
//                                       only_log: Boolean
//                                      )(using logger: SRLogger): Try[Int] = {
//
//
//    /*
//
//    20-june-2024: approach:
//      1. find team_id,prospect_id, reply_type, sent_at, prospect_category_id_custom, inbox_email_setting_id,account_id
//
//                find all the prospect_ids that received replies between 12:30 pm to 10 pm , 19th june 2024.
//                which has a reply_type in email scheduled equal to 'auto_reply' / 'delivery_failed'
//
//      2. group by team_id
//
//      3. group by reply_type find prospect_category_custom_id for this
//
//      4. group by inbox_email_setting
//
//      5. find account from owner_id
//
//      6. call _updateCategoryDB ?
//
//
//     */
//
//    val res: Try[Int] = for {
//
//      emails_and_prospect_cat_details: Seq[EmailAndProspectCatDetailsForMigration] <- email_setting_dao.getEmailAndProspectCatDataForMigration(
//        from_time = DateTime.parse("2024-06-19T06:31:00+00:00"),
//        till_time = DateTime.parse("2024-06-19T16:00:00+00:00")
//      )
//
//      group_by_some_stuufs: Seq[Int] <- Try{emails_and_prospect_cat_details
//        .groupBy(_.team_id.id)
//        .map(teamIdAndProspectData => {
//
//          val team_prospect_category_custom = prospectDAO.getProspectCategories(teamId = TeamId(teamIdAndProspectData._1)).get
//
//          val total_updations_for_this_team: immutable.Iterable[Int] = teamIdAndProspectData._2
//            .groupBy(_.owner_id)
//            .map(prospectDetailGroupByOwner => {
//
//              val findAccount = accountService.find(id = prospectDetailGroupByOwner._1.id).get
//
//              val auto_reply = prospectDetailGroupByOwner._2.filter(_.reply_type == EmailReplyType.AUTO_REPLY.toString)
//              val delivery_failed = prospectDetailGroupByOwner._2.filter(_.reply_type == EmailReplyType.DELIVERY_FAILED.toString)
//
//              val auto_reply_count = if(auto_reply.nonEmpty){
//
//                val auto_reply_prospect_category_id = team_prospect_category_custom.filter(pcat => pcat.text_id == ProspectCategory.AUTO_REPLY.toString ).head
//
//                auto_reply.zipWithIndex.map {
//                  case (data, i) =>
//
//
//                    if (only_log) {
//
//                      logger.info(s"index ::  ${i} migrateProspectsToCorrectCategory :: GOING_TO_UPDATE :: TEAM_ID: ${teamIdAndProspectData._1} PROSPECT_ID : ${data.prospect_id.id} OLDER_TYPE_ID :: ${prospectDetailGroupByOwner._2.head.current_prospect_cat_id} MOVING TO ID : ${auto_reply_prospect_category_id.id} :: AUTO_REPLY ::  ")
//
//                      1
//
//                    } else {
//
//                      prospectCategoryUpdateTemp2._updateCategoryDB(
//                        prospectIds = Seq(data.prospect_id.id),
//                        doerAccountId = prospectDetailGroupByOwner._1.id,
//                        teamId = teamIdAndProspectData._1,
//                        accountName = "SmartReach",
//                        prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AutoUpdate(
//                          old_prospect_category_id = prospectDetailGroupByOwner._2.head.current_prospect_cat_id,
//                          new_prospect_category_id = ProspectCategoryId(id = auto_reply_prospect_category_id.id)
//                        ),
//                        account = Some(findAccount),
//                        logger = logger,
//                        event_created_at = data.sent_at,
//                        auditRequestLogId = Some(logger.logRequestId) // check this
//                      ) match {
//
//                        case Failure(err) =>
//
//                          logger.debug(s"migrateProspectsToCorrectCategory :: AUTO_REPLY ::  Error while migration :: all_data: ${data} ::  Error_Trace:  ${LogHelpers.getStackTraceAsString(err)}")
//                          0
//
//                        case Success(data) =>
//
//                          logger.info(s"migrateProspectsToCorrectCategory :: AUTO_REPLY :: successfully  migrated :: all_data: ${data} ")
//                          1
//
//                      }
//
//
//                    }
//
//
//                }.sum
//
//
//              } else 0
//
//              val delivery_failed_count = if(delivery_failed.nonEmpty){
//
//                val delivery_failed_prospect_category_id = team_prospect_category_custom.filter(pcat => pcat.text_id == ProspectCategory.DELIVERY_FAILED.toString ).head
//
//                delivery_failed.zipWithIndex.map {
//                  case (data,i) => {
//
//                    if (only_log) {
//
//                      logger.info(s" index ::  ${i} migrateProspectsToCorrectCategory :: GOING_TO_UPDATE :: TEAM_ID: ${teamIdAndProspectData._1} PROSPECT_ID : ${data.prospect_id.id} OLDER_TYPE_ID :: ${prospectDetailGroupByOwner._2.head.current_prospect_cat_id} MOVING TO ID : ${delivery_failed_prospect_category_id.id} :: DELIVERY_FAILED ::  ")
//
//                      1
//
//
//                    } else {
//
//
//                      prospectCategoryUpdateTemp2._updateCategoryDB(
//                        prospectIds = Seq(data.prospect_id.id),
//                        doerAccountId = prospectDetailGroupByOwner._1.id,
//                        teamId = teamIdAndProspectData._1,
//                        accountName = "SmartReach",
//                        prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AutoUpdate(
//                          old_prospect_category_id = prospectDetailGroupByOwner._2.head.current_prospect_cat_id,
//                          new_prospect_category_id = ProspectCategoryId(id = delivery_failed_prospect_category_id.id)
//                        ),
//                        account = Some(findAccount),
//                        logger = logger,
//                        event_created_at = data.sent_at
//                        , auditRequestLogId = Some(logger.logRequestId) // check this
//                      ) match {
//
//                        case Failure(err) =>
//
//                          logger.debug(s"migrateProspectsToCorrectCategory :: Delivery_failed Error while migration :: all_data: ${data} ::  Error_Trace:  ${LogHelpers.getStackTraceAsString(err)}")
//                          0
//
//                        case Success(data) =>
//
//                          logger.info(s"migrateProspectsToCorrectCategory :: Delivery_failed successfully  migrated :: all_data: ${data}")
//                          1
//
//                      }
//
//                    }
//
//                  }
//                }.sum
//
//              } else 0
//
//              logger.debug(s"migrateProspectsToCorrectCategory :: updated_count for owner_id : ${prospectDetailGroupByOwner._1} :: auto_reply_count :: ${auto_reply_count} :: delivery_failed_count :: ${delivery_failed_count}")
//              auto_reply_count + delivery_failed_count
//
//            })
//
//          logger.debug(s"migrateProspectsToCorrectCategory :: total_updations_for_this_team ${total_updations_for_this_team}")
//
//          total_updations_for_this_team.toSeq.sum
//
//        }).toSeq}
//
//
//
//    }yield{
//
//      logger.debug(s"migrateProspectsToCorrectCategory :: total updates happened via migration :: ${group_by_some_stuufs.sum}")
//      group_by_some_stuufs.sum
//
//    }
//
//    res
//
//
//  }

}

sealed trait DeleteAccountError

object DeleteAccountError{
  case object AccountOfOwner extends DeleteAccountError
  case class SQLErrorWhileDeletingAccountData(err: Throwable) extends  DeleteAccountError
}

sealed trait DeleteOrgError

object DeleteOrgError {
  case class SQLErrorWhileFindingAccount(err: Throwable) extends DeleteOrgError

  case class SQLErrorWhileCheckingIfAlreadyDeleted(err: Throwable) extends DeleteOrgError

  case object NoOwnerFoundError extends DeleteOrgError

  case object OrgIsDeletedBefore extends DeleteOrgError

  case class SQLErrorWhileDeletingOrg(err: Throwable) extends DeleteOrgError
}


