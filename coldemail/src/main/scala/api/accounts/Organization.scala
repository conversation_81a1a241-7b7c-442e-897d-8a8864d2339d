package api.accounts

import api.accounts.models.AccountId
import api.billing.PaymentGateway
import io.sr.billing_common.models.PlanID
import org.joda.time.DateTime
import io.sr.billing_common.models.PlanType
import org.postgresql.util.PGobject
import play.api.libs.json._
import play.api.libs.json.JodaReads._
import play.api.libs.json.JodaWrites._
import scalikejdbc.WrappedResultSet
import scalikejdbc.jodatime.JodaWrappedResultSet._





case class AccountErrorObjectApi(
  error_msg: String,
  error_code: String,
  error_at: Option[DateTime],
  upgrade_now_prompt: Boolean
)


object AccountErrorObjectApi {
  given formats: OFormat[AccountErrorObjectApi] = Json.format[AccountErrorObjectApi]
}


case class AccountWarningObjectApi(
  warning_msg: String,
  warning_code: AccountWarningCodeType,
  warning_at: DateTime,
  upgrade_now_prompt: Boolean,
  add_call_credit_button: Boolean = false,
  new_prospects_paused_till: Option[DateTime]
)

object AccountWarningObjectApi {
  given formats: OFormat[AccountWarningObjectApi] = Json.format[AccountWarningObjectApi]
}


sealed trait OrganizationRole {
  def toString: String
}

object OrganizationRole {
  private val owner = "owner"
  private val agencyAdmin = "agency_admin"

  case object OWNER extends OrganizationRole {
    override def toString: String = owner
  }

  case object AGENCY_ADMIN extends OrganizationRole {
    override def toString: String = agencyAdmin
  }

  def withName(key: String): Option[OrganizationRole] = {
    key match {
      case `owner` => Some(OWNER)
      case `agencyAdmin` => Some(AGENCY_ADMIN)
      case _ => None
    }
  }

  given format: Format[OrganizationRole] = new Format[OrganizationRole] {
    override def writes(o: OrganizationRole): JsValue = {
      JsString(o.toString)
    }

    override def reads(json: JsValue): JsResult[OrganizationRole] = {
      withName(json.as[String]) match {
        case None => JsError("Invalid org_role")
        case Some(value) => JsSuccess(value)
      }
    }
  }
}


case class Organization(

  id: Long,
  name: String,
  owner_account_id: Option[Long], // FIXME This won't be an Option
  plan_type: String,
  plan_name: String,
  trial_ends_at: DateTime,
  additional_spam_tests: Int,
  is_agency: Boolean,
  fp_referrer_account_id: Option[AccountId],
  affiliateTrackerOpt: Option[AffiliateTracker]
)

object Organization {



  /*
  DO NOT REMOVE THIS COMMENT

  //  # If you use Timestamp
  //  implicit val tsreads: Reads[Timestamp] = Reads.of[Long] map (new Timestamp(_))
  //  implicit val tswrites: Writes[Timestamp] = Writes { (ts: Timestamp) => JsString(ts.toString) }

  // REF: https://stackoverflow.com/a/********/1463434

  //  # If you use DateTime
  implicit val tsreads: Reads[DateTime] = Reads.of[String] map (new DateTime(_))
  implicit val tswrites: Writes[DateTime] = Writes { (dt: DateTime) =>
    JsString(dt.toString)
  }

  */


  def fromDb(rs: WrappedResultSet) = Organization(
    id = rs.long("id"),
    name = rs.string("name"),
    owner_account_id = rs.longOpt("owner_account_id"),
    trial_ends_at = rs.jodaDateTime("trial_ends_at"),
    plan_type = rs.string("plan_type"),
    plan_name = rs.string("plan_name"),
    additional_spam_tests = rs.int("additional_spam_tests"),
    is_agency = rs.boolean("is_agency"),
    fp_referrer_account_id = rs.longOpt("fp_referrer_account_id").map(AccountId(_)),
    affiliateTrackerOpt = rs.stringOpt("affiliate_tracker").map(AffiliateTracker.fromString(_).get)
  )
}

case class OrganizationForSupportApp(

                         id: Long,
                         name: String,
                         owner_account_id: Option[Long],
                         owner_account_email: String,
                         plan_type: String,
                         plan_name: String,
                         trial_ends_at: DateTime,
                         additional_spam_tests: Int,
                         metadata: OrgMetadata,
                         total_sending_email_accounts: Int
                       )

object OrganizationForSupportApp {

  given writes: OWrites[OrganizationForSupportApp] = Json.writes[OrganizationForSupportApp]


  /*
  DO NOT REMOVE THIS COMMENT

  //  # If you use Timestamp
  //  implicit val tsreads: Reads[Timestamp] = Reads.of[Long] map (new Timestamp(_))
  //  implicit val tswrites: Writes[Timestamp] = Writes { (ts: Timestamp) => JsString(ts.toString) }

  // REF: https://stackoverflow.com/a/********/1463434

  //  # If you use DateTime
  implicit val tsreads: Reads[DateTime] = Reads.of[String] map (new DateTime(_))
  implicit val tswrites: Writes[DateTime] = Writes { (dt: DateTime) =>
    JsString(dt.toString)
  }

  */


  def fromDb(rs: WrappedResultSet) = OrganizationForSupportApp(
    id = rs.long("id"),
    name = rs.string("name"),
    owner_account_id = rs.longOpt("owner_account_id"),
    trial_ends_at = rs.jodaDateTime("trial_ends_at"),
    plan_type = rs.string("plan_type"),
    plan_name = rs.string("plan_name"),
    additional_spam_tests = rs.int("additional_spam_tests"),
    metadata = Json.parse(rs.any("metadata").asInstanceOf[PGobject].getValue).validate[OrgMetadata].get,
    owner_account_email = rs.string("owner_account_email"),
    total_sending_email_accounts = rs.int("total_sending_email_accounts")
  )
}

case class OrgPlan(
  new_prospects_paused_till: Option[DateTime],
  is_v2_business_plan: Boolean,
  fs_account_id: Option[String],
  stripe_customer_id: Option[String],
  payment_gateway: Option[PaymentGateway.Value],
  current_cycle_started_at: DateTime,
  next_billing_date: Option[DateTime],

  payment_due_invoice_link: Option[String],
  payment_due_campaign_pause_at: Option[DateTime],

  plan_type: PlanType,
  plan_name: String,
  plan_id: PlanID
)
object OrgPlan {
  given format: OFormat[OrgPlan] = Json.format[OrgPlan]
}







