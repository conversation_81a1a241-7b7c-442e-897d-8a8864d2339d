package api.accounts.models

import utils.enum_sr_utils.SREnumJsonUtils

import scala.util.Try

sealed trait OTPType

object OTPType {

  private val forgot_password = "forgot_password"
  private val verify_email = "verify_email"
  private val gdpr_check = "gdpr_check"


  case class ForgotPassword(
                             accountId: Long
                           ) extends OTPType {
    override def toString: String = forgot_password

  }

  case class VerifyEmail(
                          accountId: Long
                        ) extends OTPType {
    override def toString: String = verify_email

  }
  case class GDPREmail(
                          email: String
                        ) extends OTPType {
    override def toString: String = gdpr_check

  }

  def fromKey(key: String, accountId: Option[Long], email: Option[String]): Try[OTPType] = Try{
    key match {
      case `forgot_password` => ForgotPassword(accountId.get)
      case `verify_email` => VerifyEmail(accountId.get)
      case `gdpr_check` => GDPREmail(email.get)
    }

  }

  def toKey(value: OTPType): String = {
    value.toString
  }
}