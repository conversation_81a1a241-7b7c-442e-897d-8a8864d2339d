package api.accounts.models

import play.api.libs.json.Json
import play.api.libs.json._

case class Company (
  org_id: Long,
  name: String,
  persona: Persona,
  subscription_data: Subscription_data
)
object  Company {
  given writes:OWrites[Company] = Json.writes[Company]

}

case class Persona(
  account_type:String,
  country:String
)
object Persona{
  implicit  val writes:OWrites[Persona] = Json.writes[Persona]
}


case class Subscription_data(
  total_seats:Int,
  mrr: Int
)
object Subscription_data{
  implicit  val  writes:OWrites[Subscription_data] = Json.writes[Subscription_data]
}