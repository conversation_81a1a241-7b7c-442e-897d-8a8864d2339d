package api.accounts.models

import scala.util.Try


trait AccountStatus
object AccountStatus {
  protected val enumName: String = "AccountStatus"

  val active = "active"

  val scheduled_for_deletion = "scheduled_for_deletion"

  case object Active extends AccountStatus{
    override def toString: String = active
  }

  case object ScheduledForDeletion extends AccountStatus{
    override def toString: String = scheduled_for_deletion
  }

  def fromKey(actionStatus: String): Try[AccountStatus] = Try{

    actionStatus match {
      case `active` => Active

      case `scheduled_for_deletion` => ScheduledForDeletion
    }
  }

  def toKey(
             value: AccountStatus
           ): String = value.toString
}
