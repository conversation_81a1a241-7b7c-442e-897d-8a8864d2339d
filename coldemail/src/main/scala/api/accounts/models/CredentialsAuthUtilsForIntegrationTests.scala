package api.accounts.models

import api.accounts.{AccountUuid, OrganizationRole}
import org.joda.time.DateTime
import play.api.libs.json.JodaReads._
import play.api.libs.json.JodaWrites._
import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>sR<PERSON>ult, JsSuccess, <PERSON>sV<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>}

import scala.util.{Failure, Success, Try}

case class UserFromAccountApiResponseTest(
                                         id: AccountUuid,
                                         first_name: String,
                                         last_name: String,
                                         org_id: OrgUuid,
                                         status: String,
                                         email: String,
                                         created_at: Long,
                                         timezone: String
                                       )
object UserFromAccountApiResponseTest{
  given reads: Reads[UserFromAccountApiResponseTest] = Json.reads[UserFromAccountApiResponseTest]
}
