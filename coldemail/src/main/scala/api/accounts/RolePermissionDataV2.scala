package api.accounts

import eventframework.SrResourceTypes
import play.api.libs.json._
import scalikejdbc._

import scala.util.{Failure, Success, Try}

// REF: https://github.com/bizzabo/play-json-extensions/issues/73#issuecomment-*********
import play.api.libs.json.{JsSuccess, Json, Reads}


///////////////////////////////////
///////////////////////////////////
///////////////////////////////////

sealed trait PermType {
  def toKey: String

  /*
    7-june-2024:

    for any permission if always viewable is true. PermLevelValidation will always be set to `All`
    and it can't be chang

   */

  def always_viewable: Boolean = false

  override def toString: String = toKey
}

object PermType {
  private val justLoggedin = "just_loggedin"
  private val zapierAccess = "zapier_access"
  private val manageBilling = "manage_billing"

  private val viewUserManagement = "view_user_management"
  private val editUserManagement = "edit_user_management"

  private val viewTeamConfig = "view_team_config"
  private val editTeamConfig = "edit_team_config"

  private val viewProspects = "view_prospects"
  private val editProspects = "edit_prospects"
  private val deleteProspects = "delete_prospects"

  private val viewCampaigns = "view_campaigns"
  private val editCampaigns = "edit_campaigns"
  private val deleteCampaigns = "delete_campaigns"
  private val changeCampaignStatus = "change_campaign_status"

  private val viewReports = "view_reports"
  private val editReports = "edit_reports"
  private val downloadReports = "download_reports"

//  private val viewInbox = "view_inbox"
//  private val editInbox = "edit_inbox"
  private val sendManualEmail = "send_manual_email"

  private val viewTemplates = "view_templates"
  private val editTemplates = "edit_templates"
  private val deleteTemplates = "delete_templates"

  private val viewBlacklist = "view_blacklist"
  private val editBlacklist = "edit_blacklist"

  private val viewWorkflows = "view_workflows"
  private val editWorkflows = "edit_workflows"

  private val viewWebhooks = "view_webhooks"
  private val editWebhooks = "edit_webhooks"

//  private val viewProspectAccounts = "view_prospect_accounts"
//  private val editProspectAccounts = "edit_prospect_accounts"

//  private val viewEmailAccounts = "view_email_accounts"
//  private val editEmailAccounts = "edit_email_accounts"
//  private val deleteEmailAccounts = "delete_email_accounts"
//
//  private val viewLinkedinAccounts = "view_linkedin_accounts"
//  private val editLinkedinAccounts = "edit_linkedin_accounts"
//  private val deleteLinkedinAccounts = "delete_linkedin_accounts"
//
//  private val viewWhatsappAccounts = "view_whatsapp_accounts"
//  private val editWhatsappAccounts = "edit_whatsapp_accounts"
//  private val deleteWhatsappAccounts = "delete_whatsapp_accounts"
//
//  private val viewSmsAccounts = "view_sms_accounts"
//  private val editSmsAccounts = "edit_sms_accounts"
//  private val deleteSmsAccounts = "delete_sms_accounts"

  private val viewChannels = "view_channels"
  private val editChannels = "edit_channels"
  private val deleteChannels = "delete_channels"

  private val viewTasks = "view_tasks"
  private val editTasks = "edit_tasks"
  private val deleteTasks = "delete_tasks"

  private val editPipelineDetails = "edit_pipeline_details"
  private val deletePipelineDetails = "delete_pipeline_details"

  private val viewOpportunities = "view_opportunities"
  private val editOpportunities = "edit_opportunities"
  private val deleteOpportunities = "delete_opportunities"

  case object JUST_LOGGEDIN extends PermType {
    override def toKey: String = justLoggedin
  }
  case object ZAPIER_ACCESS extends PermType {
    override def toKey: String = zapierAccess
  }
  case object MANAGE_BILLING extends PermType {
    override def toKey: String = manageBilling
  }

  case object VIEW_USER_MANAGEMENT extends PermType {
    override def toKey: String = viewUserManagement
    override def always_viewable: Boolean = true
  }
  case object EDIT_USER_MANAGEMENT extends PermType {
    override def toKey: String = editUserManagement
  }

  case object VIEW_TEAM_CONFIG extends PermType {
    override def toKey: String = viewTeamConfig

    override def always_viewable: Boolean = true
  }
  case object EDIT_TEAM_CONFIG extends PermType {
    override def toKey: String = editTeamConfig
  }

  case object VIEW_PROSPECTS extends PermType {
    override def toKey: String = viewProspects

    override def always_viewable: Boolean = true
  }
  case object EDIT_PROSPECTS extends PermType {
    override def toKey: String = editProspects
  }
  case object DELETE_PROSPECTS extends PermType {
    override def toKey: String = deleteProspects
  }

  case object VIEW_CAMPAIGNS extends PermType {
    override def toKey: String = viewCampaigns

    override def always_viewable: Boolean = true
  }
  case object EDIT_CAMPAIGNS extends PermType {
    override def toKey: String = editCampaigns
  }
  case object DELETE_CAMPAIGNS extends PermType {
    override def toKey: String = deleteCampaigns
  }
  case object CHANGE_CAMPAIGN_STATUS extends PermType {
    override def toKey: String = changeCampaignStatus
  }

  case object VIEW_REPORTS extends PermType {
    override def toKey: String = viewReports
    override def always_viewable: Boolean = true
  }
  case object EDIT_REPORTS extends PermType {
    override def toKey: String = editReports

    override def always_viewable: Boolean = true

  }
  case object DOWNLOAD_REPORTS extends PermType {
    override def toKey: String = downloadReports

    override def always_viewable: Boolean = true

  }

//  case object VIEW_INBOX extends PermType {
//    override def toKey: String = viewInbox
//
//    override def always_viewable: Boolean = false
//  }
//  case object EDIT_INBOX extends PermType {
//    override def toKey: String = editInbox
//  }
  case object SEND_MANUAL_EMAIL extends PermType {
    override def toKey: String = sendManualEmail
  }

  case object VIEW_TEMPLATES extends PermType {
    override def toKey: String = viewTemplates

    override def always_viewable: Boolean = true
  }
  case object EDIT_TEMPLATES extends PermType {
    override def toKey: String = editTemplates
  }
  case object DELETE_TEMPLATES extends PermType {
    override def toKey: String = deleteTemplates
  }

  case object VIEW_BLACKLIST extends PermType {
    override def toKey: String = viewBlacklist

    override def always_viewable: Boolean = true
  }
  case object EDIT_BLACKLIST extends PermType {
    override def toKey: String = editBlacklist
  }

  case object VIEW_WORKFLOWS extends PermType {
    override def toKey: String = viewWorkflows

    override def always_viewable: Boolean = true
  }
  case object EDIT_WORKFLOWS extends PermType {
    override def toKey: String = editWorkflows
  }

  case object VIEW_WEBHOOKS extends PermType {
    override def toKey: String = viewWebhooks

    override def always_viewable: Boolean = true
  }
  case object EDIT_WEBHOOKS extends PermType {
    override def toKey: String = editWebhooks
  }

//  case object VIEW_PROSPECT_ACCOUNTS extends PermType {
//    override def toKey: String = viewProspectAccounts
//
//    override def always_viewable: Boolean = true
//  }
//  case object EDIT_PROSPECT_ACCOUNTS extends PermType {
//    override def toKey: String = editProspectAccounts
//  }

//  case object VIEW_EMAIL_ACCOUNTS extends PermType {
//    override def toKey: String = viewEmailAccounts
//
//    override def always_viewable: Boolean = true
//  }
//  case object EDIT_EMAIL_ACCOUNTS extends PermType {
//    override def toKey: String = editEmailAccounts
//  }
//  case object DELETE_EMAIL_ACCOUNTS extends PermType {
//    override def toKey: String = deleteEmailAccounts
//  }
//
//  case object VIEW_LINKEDIN_ACCOUNTS extends PermType {
//    override def toKey: String = viewLinkedinAccounts
//
//    override def always_viewable: Boolean = true
//  }
//
//  case object EDIT_LINKEDIN_ACCOUNTS extends PermType {
//    override def toKey: String = editLinkedinAccounts
//  }
//
//  case object DELETE_LINKEDIN_ACCOUNTS extends PermType {
//    override def toKey: String = deleteLinkedinAccounts
//  }
//
//  case object VIEW_WHATSAPP_ACCOUNTS extends PermType {
//    override def toKey: String = viewWhatsappAccounts
//
//    override def always_viewable: Boolean = true
//  }
//
//  case object EDIT_WHATSAPP_ACCOUNTS extends PermType {
//    override def toKey: String = editWhatsappAccounts
//  }
//
//  case object DELETE_WHATSAPP_ACCOUNTS extends PermType {
//    override def toKey: String = deleteWhatsappAccounts
//  }

//  case object VIEW_SMS_ACCOUNTS extends PermType {
//    override def toKey: String = viewSmsAccounts
//
//    override def always_viewable: Boolean = true
//  }
//
//  case object EDIT_SMS_ACCOUNTS extends PermType {
//    override def toKey: String = editSmsAccounts
//  }
//
//  case object DELETE_SMS_ACCOUNTS extends PermType {
//    override def toKey: String = deleteSmsAccounts
//  }

    case object VIEW_CHANNELS extends PermType {
      override def toKey: String = viewChannels

      override def always_viewable: Boolean = true
    }

    case object EDIT_CHANNELS extends PermType {
      override def toKey: String = editChannels
    }

    case object DELETE_CHANNELS extends PermType {
      override def toKey: String = deleteChannels
    }

  case object VIEW_TASKS extends PermType {
    override def toKey: String = viewTasks

    override def always_viewable: Boolean = true
  }

  case object EDIT_TASKS extends PermType {
    override def toKey: String = editTasks
  }

  case object DELETE_TASKS extends PermType {
    override def toKey: String = deleteTasks
  }

  case object EDIT_PIPELINE_DETAILS extends PermType {
    override def toKey: String = editPipelineDetails
  }

  case object DELETE_PIPELINE_DETAILS extends PermType {
    override def toKey: String = deletePipelineDetails
  }

  case object VIEW_OPPORTUNITIES extends PermType {
    override def toKey: String = viewOpportunities

    override def always_viewable: Boolean = true
  }

  case object EDIT_OPPORTUNITIES extends PermType {
    override def toKey: String = editOpportunities
  }

  case object DELETE_OPPORTUNITIES extends PermType {
    override def toKey: String = deleteOpportunities
  }

  def fromKey(key: String): Try[PermType] = Try {
    key match {
      case `justLoggedin` => JUST_LOGGEDIN
      case `zapierAccess` => ZAPIER_ACCESS
      case `manageBilling` => MANAGE_BILLING

      case `viewUserManagement` => VIEW_USER_MANAGEMENT
      case `editUserManagement` => EDIT_USER_MANAGEMENT

      case `viewTeamConfig` => VIEW_TEAM_CONFIG
      case `editTeamConfig` => EDIT_TEAM_CONFIG

      case `viewProspects` => VIEW_PROSPECTS
      case `editProspects` => EDIT_PROSPECTS
      case `deleteProspects` => DELETE_PROSPECTS

      case `viewCampaigns` => VIEW_CAMPAIGNS
      case `editCampaigns` => EDIT_CAMPAIGNS
      case `deleteCampaigns` => DELETE_CAMPAIGNS
      case `changeCampaignStatus` => CHANGE_CAMPAIGN_STATUS

      case `viewReports` => VIEW_REPORTS
      case `editReports` => EDIT_REPORTS
      case `downloadReports` => DOWNLOAD_REPORTS

//      case `viewInbox` => VIEW_INBOX
//      case `editInbox` => EDIT_INBOX
      case `sendManualEmail` => SEND_MANUAL_EMAIL

      case `viewTemplates` => VIEW_TEMPLATES
      case `editTemplates` => EDIT_TEMPLATES
      case `deleteTemplates` => DELETE_TEMPLATES

      case `viewBlacklist` => VIEW_BLACKLIST
      case `editBlacklist` => EDIT_BLACKLIST

      case `viewWorkflows` => VIEW_WORKFLOWS
      case `editWorkflows` => EDIT_WORKFLOWS

      case `viewWebhooks` => VIEW_WEBHOOKS
      case `editWebhooks` => EDIT_WEBHOOKS

//      case `viewProspectAccounts` => VIEW_PROSPECT_ACCOUNTS
//      case `editProspectAccounts` => EDIT_PROSPECT_ACCOUNTS

//      case `viewEmailAccounts` => VIEW_EMAIL_ACCOUNTS
//      case `editEmailAccounts` => EDIT_EMAIL_ACCOUNTS
//      case `deleteEmailAccounts` => DELETE_EMAIL_ACCOUNTS
//
//      case `viewLinkedinAccounts` => VIEW_LINKEDIN_ACCOUNTS
//      case `editLinkedinAccounts` => EDIT_LINKEDIN_ACCOUNTS
//      case `deleteLinkedinAccounts` => DELETE_LINKEDIN_ACCOUNTS
//
//      case `viewWhatsappAccounts` => VIEW_WHATSAPP_ACCOUNTS
//      case `editWhatsappAccounts` => EDIT_WHATSAPP_ACCOUNTS
//      case `deleteWhatsappAccounts` => DELETE_WHATSAPP_ACCOUNTS
//
//      case `viewSmsAccounts` => VIEW_SMS_ACCOUNTS
//      case `editSmsAccounts` => EDIT_SMS_ACCOUNTS
//      case `deleteSmsAccounts` => DELETE_SMS_ACCOUNTS

      case `viewChannels` => VIEW_CHANNELS
      case `editChannels` => EDIT_CHANNELS
      case `deleteChannels` => DELETE_CHANNELS

      case `viewTasks` => VIEW_TASKS
      case `editTasks` => EDIT_TASKS
      case `deleteTasks` => DELETE_TASKS

      case `editPipelineDetails` => EDIT_PIPELINE_DETAILS
      case `deletePipelineDetails` => DELETE_PIPELINE_DETAILS

      case `viewOpportunities` => VIEW_OPPORTUNITIES
      case `editOpportunities` => EDIT_OPPORTUNITIES
      case `deleteOpportunities` => DELETE_OPPORTUNITIES
    }
  }

  implicit def format: Format[PermType] = new Format[PermType]  {
    override def writes(o: PermType): JsValue = JsString(o.toKey)

    override def reads(json: JsValue): JsResult[PermType] =
      fromKey(json.as[String]) match {
        case Failure(e) => JsError(e.toString)
        case Success(value: PermType) => JsSuccess(value)
      }
  }

}

sealed trait PermissionOwnershipV2 {
  def toKey: String
}

object PermissionOwnershipV2 {
  private val all = "all"
  private val owned = "owned"
  private val noAccess = "no_access"

  case object All extends PermissionOwnershipV2 {
    override def toKey: String = all
  }

  case object Owned extends PermissionOwnershipV2 {
    override def toKey: String = owned
  }

  case object NoAccess extends PermissionOwnershipV2 {
    override def toKey: String = noAccess
  }

  def fromString(key: String) = Try {
    key match {
      case `all` => All
      case `owned` => Owned
      case `noAccess` => NoAccess
    }
  }

  given reads: Reads[PermissionOwnershipV2] = new Reads[PermissionOwnershipV2] {
    override def reads(json: JsValue): JsResult[PermissionOwnershipV2] = {
      PermissionOwnershipV2.fromString(json.as[String]) match {

        case Failure(exception) => JsError(exception.toString)
        case Success(value) => JsSuccess(value)

      }
    }
  }

  implicit val writes: Writes[PermissionOwnershipV2] = new Writes[PermissionOwnershipV2] {
    override def writes(o: PermissionOwnershipV2): JsValue = {
      JsString(o.toKey)
    }
  }

}

sealed trait PermissionLevelForValidation {
  override def toString: String
}

object PermissionLevelForValidation {
  private val view = "view"
  private val edit = "edit"
  private val delete = "delete"
  private val ignoreValidation = "ignore_validation"

  case object View extends PermissionLevelForValidation {
    override def toString: String = view
  }

  case object Edit extends PermissionLevelForValidation {
    override def toString: String = edit
  }

  case object Delete extends PermissionLevelForValidation {
    override def toString: String = delete
  }

  case object IgnoreValidation extends PermissionLevelForValidation {
    override def toString: String = ignoreValidation
  }

  def fromKey(key: String): Try[PermissionLevelForValidation] = Try {
    key match {
      case `view` => View
      case `edit` => Edit
      case `delete` => Delete
      case `ignoreValidation` => IgnoreValidation
    }
  }

  implicit def format: Format[PermissionLevelForValidation] = new Format[PermissionLevelForValidation] {
    override def reads(json: JsValue): JsResult[PermissionLevelForValidation] = {
      fromKey(json.as[String]) match {
        case Failure(e) => JsError(e.toString)
        case Success(value) => JsSuccess(value)
      }
    }

    override def writes(o: PermissionLevelForValidation): JsValue = {
      JsString(o.toString)
    }
  }

}

case class RolePermV2 (
                        ownership: PermissionOwnershipV2,
                        entity: SrResourceTypes,
                        permissionLevel: PermissionLevelForValidation,
                        permissionType: PermType,
                        version: String = "v2"
                      )

object RolePermV2 {

  implicit val reads: Reads[RolePermV2] = Json.reads[RolePermV2]


  implicit def writes: Writes[RolePermV2] = new Writes[RolePermV2]{

    /*
      7-june-2024:
      `always_viewable` is additional, and not in the actual case class.

     */
    def writes(o: RolePermV2): JsValue = {

        Json.obj(
          "entity" -> o.entity,
          "version" -> o.version,
          "ownership" -> o.ownership,
          "permissionType" -> o.permissionType,
          "permissionLevel" -> o.permissionLevel,
          "always_viewable" -> o.permissionType.always_viewable
        )

    }

  }

}

// EDITROLEBACKEND
case class RolePermissionsInDBV2(

                                  id: Long,
                                  role_name: TeamAccountRole,

                                  manage_billing_v2: PermissionOwnershipV2,
                                  view_user_management_v2: PermissionOwnershipV2,
                                  edit_user_management_v2: PermissionOwnershipV2,

                                  view_team_config_v2: PermissionOwnershipV2,
                                  edit_team_config_v2: PermissionOwnershipV2,

                                  /*
                                  view_user_settings: Boolean,
                                  view_user_settings_ownership: PermissionOwnership.Value,

                                  edit_user_settings: Boolean,
                                  edit_user_settings_ownership: PermissionOwnership.Value,
                                  */


                                  view_prospects_v2: PermissionOwnershipV2,

                                  edit_prospects_v2: PermissionOwnershipV2,

                                  delete_prospects_v2: PermissionOwnershipV2,



                                  view_campaigns_v2: PermissionOwnershipV2,

                                  edit_campaigns_v2: PermissionOwnershipV2,

                                  delete_campaigns_v2: PermissionOwnershipV2,

                                  change_campaign_status_v2: PermissionOwnershipV2,



                                  view_reports_v2: PermissionOwnershipV2,

                                  edit_reports_v2: PermissionOwnershipV2,

                                  download_reports_v2: PermissionOwnershipV2,



//                                  view_inbox_v2: PermissionOwnershipV2,
//
//                                  edit_inbox_v2: PermissionOwnershipV2,

                                  send_manual_email_v2: PermissionOwnershipV2,



                                  view_templates_v2: PermissionOwnershipV2,

                                  edit_templates_v2: PermissionOwnershipV2,

                                  delete_templates_v2: PermissionOwnershipV2,



                                  view_blacklist_v2: PermissionOwnershipV2,

                                  edit_blacklist_v2: PermissionOwnershipV2,


                                  view_workflows_v2: PermissionOwnershipV2,

                                  edit_workflows_v2: PermissionOwnershipV2,

                                  view_webhooks_v2: PermissionOwnershipV2,

                                  edit_webhooks_v2: PermissionOwnershipV2,

//                                  view_prospect_accounts_v2: PermissionOwnershipV2,
//
//                                  edit_prospect_accounts_v2: PermissionOwnershipV2,


//                                  view_email_accounts_v2: PermissionOwnershipV2,
//                                  edit_email_accounts_v2: PermissionOwnershipV2,
//                                  delete_email_accounts_v2: PermissionOwnershipV2,
//
//                                  view_linkedin_accounts_v2: PermissionOwnershipV2,
//                                  edit_linkedin_accounts_v2: PermissionOwnershipV2,
//                                  delete_linkedin_accounts_v2: PermissionOwnershipV2,
//
//                                  view_whatsapp_accounts_v2: PermissionOwnershipV2,
//                                  edit_whatsapp_accounts_v2: PermissionOwnershipV2,
//                                  delete_whatsapp_accounts_v2: PermissionOwnershipV2,
//
//                                  view_sms_accounts_v2: PermissionOwnershipV2,
//                                  edit_sms_accounts_v2: PermissionOwnershipV2,
//                                  delete_sms_accounts_v2: PermissionOwnershipV2,

                                  view_channels_v2: PermissionOwnershipV2,
                                  edit_channels_v2: PermissionOwnershipV2,
                                  delete_channels_v2: PermissionOwnershipV2,
                                
                                  view_tasks_v2: PermissionOwnershipV2,
                                  edit_tasks_v2: PermissionOwnershipV2,
                                  delete_tasks_v2: PermissionOwnershipV2,

                                  edit_pipeline_details_v2: PermissionOwnershipV2,
                                  delete_pipeline_details_v2: PermissionOwnershipV2,

                                  view_opportunities_v2: PermissionOwnershipV2,
                                  edit_opportunities_v2: PermissionOwnershipV2,
                                  delete_opportunities_v2: PermissionOwnershipV2,

)
object RolePermissionsInDBV2 {
  // implicit lazy val format: OFormat[RolePermissionsInDBV2] = Json.format[RolePermissionsInDBV2]


  given format: Format[RolePermissionsInDBV2] = new Format[RolePermissionsInDBV2] {
    override def reads(json: JsValue): JsResult[RolePermissionsInDBV2] = Try {
      RolePermissionsInDBV2(
        id = (json \ "id").as[Long],
        role_name = (json \ "role_name").as[TeamAccountRole],
        manage_billing_v2 = (json \ "manage_billing_v2").as[PermissionOwnershipV2],
        view_user_management_v2 = (json \ "view_user_management_v2").as[PermissionOwnershipV2],
        edit_user_management_v2 = (json \ "edit_user_management_v2").as[PermissionOwnershipV2],
        view_team_config_v2 = (json \ "view_team_config_v2").as[PermissionOwnershipV2],
        edit_team_config_v2 = (json \ "edit_team_config_v2").as[PermissionOwnershipV2],
        view_prospects_v2 = (json \ "view_prospects_v2").as[PermissionOwnershipV2],
        edit_prospects_v2 = (json \ "edit_prospects_v2").as[PermissionOwnershipV2],
        delete_prospects_v2 = (json \ "delete_prospects_v2").as[PermissionOwnershipV2],
        view_campaigns_v2 = (json \ "view_campaigns_v2").as[PermissionOwnershipV2],
        edit_campaigns_v2 = (json \ "edit_campaigns_v2").as[PermissionOwnershipV2],
        delete_campaigns_v2 = (json \ "delete_campaigns_v2").as[PermissionOwnershipV2],
        change_campaign_status_v2 = (json \ "change_campaign_status_v2").as[PermissionOwnershipV2],
        view_reports_v2 = (json \ "view_reports_v2").as[PermissionOwnershipV2],
        edit_reports_v2 = (json \ "edit_reports_v2").as[PermissionOwnershipV2],
        download_reports_v2 = (json \ "download_reports_v2").as[PermissionOwnershipV2],
//        view_inbox_v2 = (json \ "view_inbox_v2").as[PermissionOwnershipV2],
//        edit_inbox_v2 = (json \ "edit_inbox_v2").as[PermissionOwnershipV2],
        send_manual_email_v2 = (json \ "send_manual_email_v2").as[PermissionOwnershipV2],
        view_templates_v2 = (json \ "view_templates_v2").as[PermissionOwnershipV2],
        edit_templates_v2 = (json \ "edit_templates_v2").as[PermissionOwnershipV2],
        delete_templates_v2 = (json \ "delete_templates_v2").as[PermissionOwnershipV2],
        view_blacklist_v2 = (json \ "view_blacklist_v2").as[PermissionOwnershipV2],
        edit_blacklist_v2 = (json \ "edit_blacklist_v2").as[PermissionOwnershipV2],
        view_workflows_v2 = (json \ "view_workflows_v2").as[PermissionOwnershipV2],
        edit_workflows_v2 = (json \ "edit_workflows_v2").as[PermissionOwnershipV2],
        view_webhooks_v2 = (json \ "view_webhooks_v2").as[PermissionOwnershipV2],
        edit_webhooks_v2 = (json \ "edit_webhooks_v2").as[PermissionOwnershipV2],
//        view_prospect_accounts_v2 = (json \ "view_prospect_accounts_v2").as[PermissionOwnershipV2],
//        edit_prospect_accounts_v2 = (json \ "edit_prospect_accounts_v2").as[PermissionOwnershipV2],
//        view_email_accounts_v2 = (json \ "view_email_accounts_v2").as[PermissionOwnershipV2],
//        edit_email_accounts_v2 = (json \ "edit_email_accounts_v2").as[PermissionOwnershipV2],
//        delete_email_accounts_v2 = (json \ "delete_email_accounts_v2").as[PermissionOwnershipV2],
//        view_linkedin_accounts_v2 = (json \ "view_linkedin_accounts_v2").as[PermissionOwnershipV2],
//        edit_linkedin_accounts_v2 = (json \ "edit_linkedin_accounts_v2").as[PermissionOwnershipV2],
//        delete_linkedin_accounts_v2 = (json \ "delete_linkedin_accounts_v2").as[PermissionOwnershipV2],
//        view_whatsapp_accounts_v2 = (json \ "view_whatsapp_accounts_v2").as[PermissionOwnershipV2],
//        edit_whatsapp_accounts_v2 = (json \ "edit_whatsapp_accounts_v2").as[PermissionOwnershipV2],
//        delete_whatsapp_accounts_v2 = (json \ "delete_whatsapp_accounts_v2").as[PermissionOwnershipV2],
//        view_sms_accounts_v2 = (json \ "view_sms_accounts_v2").as[PermissionOwnershipV2],
//        edit_sms_accounts_v2 = (json \ "edit_sms_accounts_v2").as[PermissionOwnershipV2],
//        delete_sms_accounts_v2 = (json \ "delete_sms_accounts_v2").as[PermissionOwnershipV2],
        view_channels_v2 = (json \ "view_channels_v2").as[PermissionOwnershipV2],
        edit_channels_v2 = (json \ "edit_channels_v2").as[PermissionOwnershipV2],
        delete_channels_v2 = (json \ "delete_channels_v2").as[PermissionOwnershipV2],
        view_tasks_v2 = (json \ "view_tasks_v2").as[PermissionOwnershipV2],
        edit_tasks_v2 = (json \ "edit_tasks_v2").as[PermissionOwnershipV2],
        delete_tasks_v2 = (json \ "delete_tasks_v2").as[PermissionOwnershipV2],
        edit_pipeline_details_v2 = (json \ "edit_pipeline_details_v2").as[PermissionOwnershipV2],
        delete_pipeline_details_v2 = (json \ "delete_pipeline_details_v2").as[PermissionOwnershipV2],
        view_opportunities_v2 = (json \ "view_opportunities_v2").as[PermissionOwnershipV2],
        edit_opportunities_v2 = (json \ "edit_opportunities_v2").as[PermissionOwnershipV2],
        delete_opportunities_v2 = (json \ "delete_opportunities_v2").as[PermissionOwnershipV2]
      )
    } match {
      case Failure(e) => JsError(e.toString)
      case Success(value) => JsSuccess(value)
    }

    override def writes(o: RolePermissionsInDBV2): JsValue = Json.obj(
      "id" -> o.id,
      "role_name" -> o.role_name,
      "manage_billing_v2" -> o.manage_billing_v2,
      "view_user_management_v2" -> o.view_user_management_v2,
      "edit_user_management_v2" -> o.edit_user_management_v2,
      "view_team_config_v2" -> o.view_team_config_v2,
      "edit_team_config_v2" -> o.edit_team_config_v2,
      "view_prospects_v2" -> o.view_prospects_v2,
      "edit_prospects_v2" -> o.edit_prospects_v2,
      "delete_prospects_v2" -> o.delete_prospects_v2,
      "view_campaigns_v2" -> o.view_campaigns_v2,
      "edit_campaigns_v2" -> o.edit_campaigns_v2,
      "delete_campaigns_v2" -> o.delete_campaigns_v2,
      "change_campaign_status_v2" -> o.change_campaign_status_v2,
      "view_reports_v2" -> o.view_reports_v2,
      "edit_reports_v2" -> o.edit_reports_v2,
      "download_reports_v2" -> o.download_reports_v2,
//      "view_inbox_v2" -> o.view_inbox_v2,
//      "edit_inbox_v2" -> o.edit_inbox_v2,
      "send_manual_email_v2" -> o.send_manual_email_v2,
      "view_templates_v2" -> o.view_templates_v2,
      "edit_templates_v2" -> o.edit_templates_v2,
      "delete_templates_v2" -> o.delete_templates_v2,
      "view_blacklist_v2" -> o.view_blacklist_v2,
      "edit_blacklist_v2" -> o.edit_blacklist_v2,
      "view_workflows_v2" -> o.view_workflows_v2,
      "edit_workflows_v2" -> o.edit_workflows_v2,
      "view_webhooks_v2" -> o.view_webhooks_v2,
      "edit_webhooks_v2" -> o.edit_webhooks_v2,
//      "view_prospect_accounts_v2" -> o.view_prospect_accounts_v2,
//      "edit_prospect_accounts_v2" -> o.edit_prospect_accounts_v2,
//      "view_email_accounts_v2" -> o.view_email_accounts_v2,
//      "edit_email_accounts_v2" -> o.edit_email_accounts_v2,
//      "delete_email_accounts_v2" -> o.delete_email_accounts_v2,
//      "view_linkedin_accounts_v2" -> o.view_linkedin_accounts_v2,
//      "edit_linkedin_accounts_v2" -> o.edit_linkedin_accounts_v2,
//      "delete_linkedin_accounts_v2" -> o.delete_linkedin_accounts_v2,
//      "view_whatsapp_accounts_v2" -> o.view_whatsapp_accounts_v2,
//      "edit_whatsapp_accounts_v2" -> o.edit_whatsapp_accounts_v2,
//      "delete_whatsapp_accounts_v2" -> o.delete_whatsapp_accounts_v2,
//      "view_sms_accounts_v2" -> o.view_sms_accounts_v2,
//      "edit_sms_accounts_v2" -> o.edit_sms_accounts_v2,
//      "delete_sms_accounts_v2" -> o.delete_sms_accounts_v2,
      "view_channels_v2" -> o.view_channels_v2,
      "edit_channels_v2" -> o.edit_channels_v2,
      "delete_channels_v2" -> o.delete_channels_v2,
      "view_tasks_v2" -> o.view_tasks_v2,
      "edit_tasks_v2" -> o.edit_tasks_v2,
      "delete_tasks_v2" -> o.delete_tasks_v2,
      "edit_pipeline_details_v2" -> o.edit_pipeline_details_v2,
      "delete_pipeline_details_v2" -> o.delete_pipeline_details_v2,
      "view_opportunities_v2" -> o.view_opportunities_v2,
      "edit_opportunities_v2" -> o.edit_opportunities_v2,
      "delete_opportunities_v2" -> o.delete_opportunities_v2
    )
  }


  // EDITROLEBACKEND
  def fromDb(rs: WrappedResultSet) = {

    RolePermissionsInDBV2(

      id = rs.long("id"),
      role_name = TeamAccountRole.withName(rs.string("role_name")).get,


      manage_billing_v2 = PermissionOwnershipV2.fromString(rs.string("manage_billing_v2")).get,
      view_user_management_v2 = PermissionOwnershipV2.fromString(rs.string("view_user_management_v2")).get,
      edit_user_management_v2 = PermissionOwnershipV2.fromString(rs.string("edit_user_management_v2")).get,

      view_team_config_v2 = PermissionOwnershipV2.fromString(rs.string("view_team_config_v2")).get,
      edit_team_config_v2 = PermissionOwnershipV2.fromString(rs.string("edit_team_config_v2")).get,

      // This was commented out in old flow.
      /*
      view_user_settings = rs.boolean("view_user_settings"),
      view_user_settings_ownership = PermissionOwnership.withName(rs.string("view_user_settings_ownership")),

      edit_user_settings = rs.boolean("edit_user_settings"),
      edit_user_settings_ownership = PermissionOwnership.withName(rs.string("edit_user_settings_ownership")),
      */

      view_prospects_v2 = PermissionOwnershipV2.fromString(rs.string("view_prospects_v2")).get,

      edit_prospects_v2 = PermissionOwnershipV2.fromString(rs.string("edit_prospects_v2")).get,

      delete_prospects_v2 = PermissionOwnershipV2.fromString(rs.string("delete_prospects_v2")).get,


      view_campaigns_v2 = PermissionOwnershipV2.fromString(rs.string("view_campaigns_v2")).get,

      edit_campaigns_v2 = PermissionOwnershipV2.fromString(rs.string("edit_campaigns_v2")).get,

      delete_campaigns_v2 = PermissionOwnershipV2.fromString(rs.string("delete_campaigns_v2")).get,

      change_campaign_status_v2 = PermissionOwnershipV2.fromString(rs.string("change_campaign_status_v2")).get,


      view_reports_v2 = PermissionOwnershipV2.fromString(rs.string("view_reports_v2")).get,

      edit_reports_v2 = PermissionOwnershipV2.fromString(rs.string("edit_reports_v2")).get,

      download_reports_v2 = PermissionOwnershipV2.fromString(rs.string("download_reports_v2")).get,


//      view_inbox_v2 = PermissionOwnershipV2.fromString(rs.string("view_inbox_v2")).get,
//
//      edit_inbox_v2 = PermissionOwnershipV2.fromString(rs.string("edit_inbox_v2")).get,

      send_manual_email_v2 = PermissionOwnershipV2.fromString(rs.string("send_manual_email_v2")).get,


      view_templates_v2 = PermissionOwnershipV2.fromString(rs.string("view_templates_v2")).get,

      edit_templates_v2 = PermissionOwnershipV2.fromString(rs.string("edit_templates_v2")).get,

      delete_templates_v2 = PermissionOwnershipV2.fromString(rs.string("delete_templates_v2")).get,



      // everyone has permission to blacklist
      view_blacklist_v2 = PermissionOwnershipV2.fromString(rs.string("view_blacklist_v2")).get,

      edit_blacklist_v2 = PermissionOwnershipV2.fromString(rs.string("edit_blacklist_v2")).get,

      view_workflows_v2 = PermissionOwnershipV2.fromString(rs.string("view_workflows_v2")).get,

      edit_workflows_v2 = PermissionOwnershipV2.fromString(rs.string("edit_workflows_v2")).get,

      view_webhooks_v2 = PermissionOwnershipV2.fromString(rs.string("view_webhooks_v2")).get,

      edit_webhooks_v2 = PermissionOwnershipV2.fromString(rs.string("edit_webhooks_v2")).get,

//      view_prospect_accounts_v2 = PermissionOwnershipV2.fromString(rs.string("view_prospect_accounts_v2")).get,
//
//      edit_prospect_accounts_v2 = PermissionOwnershipV2.fromString(rs.string("edit_prospect_accounts_v2")).get,


//      view_email_accounts_v2 = PermissionOwnershipV2.fromString(rs.string("view_email_accounts_v2")).get,
//      edit_email_accounts_v2 = PermissionOwnershipV2.fromString(rs.string("edit_email_accounts_v2")).get,
//      delete_email_accounts_v2 = PermissionOwnershipV2.fromString(rs.string("delete_email_accounts_v2")).get,
//
//      view_linkedin_accounts_v2 = PermissionOwnershipV2.fromString(rs.string("view_linkedin_accounts_v2")).get,
//      edit_linkedin_accounts_v2 = PermissionOwnershipV2.fromString(rs.string("edit_linkedin_accounts_v2")).get,
//      delete_linkedin_accounts_v2 = PermissionOwnershipV2.fromString(rs.string("delete_linkedin_accounts_v2")).get,
//
//      view_whatsapp_accounts_v2 = PermissionOwnershipV2.fromString(rs.string("view_whatsapp_accounts_v2")).get,
//      edit_whatsapp_accounts_v2 = PermissionOwnershipV2.fromString(rs.string("edit_whatsapp_accounts_v2")).get,
//      delete_whatsapp_accounts_v2 = PermissionOwnershipV2.fromString(rs.string("delete_whatsapp_accounts_v2")).get,
//
//      view_sms_accounts_v2 = PermissionOwnershipV2.fromString(rs.string("view_sms_accounts_v2")).get,
//      edit_sms_accounts_v2 = PermissionOwnershipV2.fromString(rs.string("edit_sms_accounts_v2")).get,
//      delete_sms_accounts_v2 = PermissionOwnershipV2.fromString(rs.string("delete_sms_accounts_v2")).get,

      view_channels_v2 = PermissionOwnershipV2.fromString(rs.string("view_channels_v2")).get,
      edit_channels_v2 = PermissionOwnershipV2.fromString(rs.string("edit_channels_v2")).get,
      delete_channels_v2 = PermissionOwnershipV2.fromString(rs.string("delete_channels_v2")).get,
      
      view_tasks_v2 = PermissionOwnershipV2.fromString(rs.string("view_tasks_v2")).get,
      edit_tasks_v2 = PermissionOwnershipV2.fromString(rs.string("edit_tasks_v2")).get,
      delete_tasks_v2 = PermissionOwnershipV2.fromString(rs.string("delete_tasks_v2")).get,

      edit_pipeline_details_v2 = PermissionOwnershipV2.fromString(rs.string("edit_pipeline_details_v2")).get,
      delete_pipeline_details_v2 = PermissionOwnershipV2.fromString(rs.string("delete_pipeline_details_v2")).get,

      view_opportunities_v2 = PermissionOwnershipV2.fromString(rs.string("view_opportunities_v2")).get,
      edit_opportunities_v2 = PermissionOwnershipV2.fromString(rs.string("edit_opportunities_v2")).get,
      delete_opportunities_v2 = PermissionOwnershipV2.fromString(rs.string("delete_opportunities_v2")).get,

    )
  }
}


// EDITROLEBACKEND
case class RolePermissionsV2(
                              just_loggedin: RolePermV2 = RolePermV2(
                                ownership = PermissionOwnershipV2.All,
                                entity = SrResourceTypes.OTHER,
                                permissionLevel = PermissionLevelForValidation.IgnoreValidation,
                                permissionType = PermType.JUST_LOGGEDIN
                              ),
                              zapier_access: RolePermV2 = RolePermV2(
                                ownership = PermissionOwnershipV2.All,
                                entity = SrResourceTypes.OTHER,
                                permissionLevel = PermissionLevelForValidation.IgnoreValidation,
                                permissionType = PermType.ZAPIER_ACCESS
                              ),

                              manage_billing: RolePermV2,
                              view_user_management: RolePermV2,
                              edit_user_management: RolePermV2,

                              view_team_config: RolePermV2,
                              edit_team_config: RolePermV2,

                              /*
                              view_user_settings: RolePermConfig,
                              edit_user_settings: RolePermConfig,
                              */

                              view_prospects: RolePermV2,
                              edit_prospects: RolePermV2,
                              delete_prospects: RolePermV2,

                              view_campaigns: RolePermV2,
                              edit_campaigns: RolePermV2,
                              delete_campaigns: RolePermV2,
                              change_campaign_status: RolePermV2,

                              view_reports: RolePermV2,
                              edit_reports: RolePermV2,
                              download_reports: RolePermV2,

//                              view_inbox: RolePermV2,
//                              edit_inbox: RolePermV2,
                              send_manual_email: RolePermV2,

                              view_templates: RolePermV2,
                              edit_templates: RolePermV2,
                              delete_templates: RolePermV2,

                              view_blacklist: RolePermV2,
                              edit_blacklist: RolePermV2,

                              view_workflows: RolePermV2,
                              edit_workflows: RolePermV2,

                              view_webhooks: RolePermV2,
                              edit_webhooks: RolePermV2,

//                              view_prospect_accounts: RolePermV2,
//                              edit_prospect_accounts: RolePermV2,

//                              view_email_accounts: RolePermV2,
//                              edit_email_accounts: RolePermV2,
//                              delete_email_accounts: RolePermV2,
//
//                              view_linkedin_accounts: RolePermV2,
//                              edit_linkedin_accounts: RolePermV2,
//                              delete_linkedin_accounts: RolePermV2,
//
//                              view_whatsapp_accounts: RolePermV2,
//                              edit_whatsapp_accounts: RolePermV2,
//                              delete_whatsapp_accounts: RolePermV2,
//
//                              view_sms_accounts: RolePermV2,
//                              edit_sms_accounts: RolePermV2,
//                              delete_sms_accounts: RolePermV2,

                              view_channels: RolePermV2,
                              edit_channels: RolePermV2,
                              delete_channels: RolePermV2,

                              view_tasks: RolePermV2,
                              edit_tasks: RolePermV2,
                              delete_tasks: RolePermV2,

                              edit_pipeline_details: RolePermV2,
                              delete_pipeline_details: RolePermV2,

                              view_opportunities: RolePermV2,
                              edit_opportunities: RolePermV2,
                              delete_opportunities: RolePermV2,
                              // Total 51 permissions
)
object RolePermissionsV2 {

  implicit val rolePermissionsReads: Reads[RolePermissionsV2] = new Reads[RolePermissionsV2] {
    override def reads(ev: JsValue): JsResult[RolePermissionsV2] = Try {
      RolePermissionsV2(
        just_loggedin = (ev \ "just_loggedin").as[RolePermV2],
        zapier_access = (ev \ "zapier_access").as[RolePermV2],
        manage_billing = (ev \ "manage_billing").as[RolePermV2],
        view_user_management = (ev \ "view_user_management").as[RolePermV2],
        edit_user_management = (ev \ "edit_user_management").as[RolePermV2],
        view_team_config = (ev \ "view_team_config").as[RolePermV2],
        edit_team_config = (ev \ "edit_team_config").as[RolePermV2],

        view_prospects = (ev \ "view_prospects").as[RolePermV2],
        edit_prospects =  (ev \ "edit_prospects").as[RolePermV2],

        delete_prospects = (ev \ "delete_prospects").as[RolePermV2],

        view_campaigns = (ev \ "view_campaigns").as[RolePermV2],

        edit_campaigns = (ev \ "edit_campaigns").as[RolePermV2],

        delete_campaigns = (ev \ "delete_campaigns").as[RolePermV2],

        change_campaign_status = (ev \ "change_campaign_status").as[RolePermV2],

        view_reports  = (ev \ "view_reports").as[RolePermV2],

        edit_reports = (ev \ "edit_reports").as[RolePermV2],

        download_reports = (ev \ "download_reports").as[RolePermV2],

//        view_inbox = (ev \ "view_inbox").as[RolePermV2],
//        edit_inbox = (ev \ "edit_inbox").as[RolePermV2],
        send_manual_email = (ev \ "send_manual_email").as[RolePermV2],

        view_templates = (ev \ "view_templates").as[RolePermV2],
        edit_templates = (ev \ "edit_templates").as[RolePermV2],
        delete_templates = (ev \ "delete_templates").as[RolePermV2],

        view_blacklist = (ev \ "view_blacklist").as[RolePermV2],
        edit_blacklist = (ev \ "edit_blacklist").as[RolePermV2],

        view_workflows = (ev \ "view_workflows").as[RolePermV2],
        edit_workflows = (ev \ "edit_workflows").as[RolePermV2],

        view_webhooks = (ev \ "view_webhooks").as[RolePermV2],
        edit_webhooks = (ev \ "edit_webhooks").as[RolePermV2],

//        view_prospect_accounts = (ev \ "view_prospect_accounts").as[RolePermV2],
//        edit_prospect_accounts = (ev \ "edit_prospect_accounts").as[RolePermV2],

//        view_email_accounts = (ev \ "view_email_accounts").as[RolePermV2],
//        edit_email_accounts = (ev \ "edit_email_accounts").as[RolePermV2],
//        delete_email_accounts = (ev \ "delete_email_accounts").as[RolePermV2],
//
//        view_linkedin_accounts = (ev \ "view_linkedin_accounts").as[RolePermV2],
//        edit_linkedin_accounts = (ev \ "edit_linkedin_accounts").as[RolePermV2],
//        delete_linkedin_accounts = (ev \ "delete_linkedin_accounts").as[RolePermV2],
//
//        view_whatsapp_accounts = (ev \ "view_whatsapp_accounts").as[RolePermV2],
//        edit_whatsapp_accounts = (ev \ "edit_whatsapp_accounts").as[RolePermV2],
//        delete_whatsapp_accounts = (ev \ "delete_whatsapp_accounts").as[RolePermV2],
//
//        view_sms_accounts = (ev \ "view_sms_accounts").as[RolePermV2],
//        edit_sms_accounts = (ev \ "edit_sms_accounts").as[RolePermV2],
//        delete_sms_accounts = (ev \ "delete_sms_accounts").as[RolePermV2],

        view_channels = (ev \ "view_channels").as[RolePermV2],
        edit_channels = (ev \ "edit_channels").as[RolePermV2],
        delete_channels = (ev \ "delete_channels").as[RolePermV2],

        view_tasks =  (ev \ "view_tasks").as[RolePermV2],
        edit_tasks = (ev \ "edit_tasks").as[RolePermV2],

        delete_tasks = (ev \ "delete_tasks").as[RolePermV2],

        edit_pipeline_details = (ev \ "edit_pipeline_details").as[RolePermV2],
        delete_pipeline_details = (ev \ "delete_pipeline_details").as[RolePermV2],
        view_opportunities = (ev \ "view_opportunities").as[RolePermV2],
        edit_opportunities = (ev \ "edit_opportunities").as[RolePermV2],
        delete_opportunities = (ev \ "delete_opportunities").as[RolePermV2]
      )
    } match {

      case Failure(er) => JsError(er.toString)
      case Success(data) => JsSuccess(data)

    }
  }

  implicit val writes: Writes[RolePermissionsV2] = new Writes[RolePermissionsV2] {
    override def writes(o: RolePermissionsV2): JsValue = {
      Json.obj(
        "just_loggedin" -> o.just_loggedin,
        "zapier_access" -> o.zapier_access,
        "manage_billing" -> o.manage_billing,
        "view_user_management" -> o.view_user_management,
        "edit_user_management" -> o.edit_user_management,
        "view_team_config" -> o.view_team_config,
        "edit_team_config" -> o.edit_team_config,
        "view_prospects" -> o.view_prospects,
        "edit_prospects" -> o.edit_prospects,
        "delete_prospects" -> o.delete_prospects,
        "view_campaigns" -> o.view_campaigns,
        "edit_campaigns" -> o.edit_campaigns,
        "delete_campaigns" -> o.delete_campaigns,
        "change_campaign_status" -> o.change_campaign_status,
        "view_reports" -> o.view_reports,
        "edit_reports" -> o.edit_reports,
        "download_reports" -> o.download_reports,
//        "view_inbox" -> o.view_inbox,
//        "edit_inbox" -> o.edit_inbox,
        "send_manual_email" -> o.send_manual_email,
        "view_templates" -> o.view_templates,
        "edit_templates" -> o.edit_templates,
        "delete_templates" -> o.delete_templates,
        "view_blacklist" -> o.view_blacklist,
        "edit_blacklist" -> o.edit_blacklist,
        "view_workflows" -> o.view_workflows,
        "edit_workflows" -> o.edit_workflows,
        "view_webhooks" -> o.view_webhooks,
        "edit_webhooks" -> o.edit_webhooks,
//        "view_prospect_accounts" -> o.view_prospect_accounts,
//        "edit_prospect_accounts" -> o.edit_prospect_accounts,
//        "view_email_accounts" -> o.view_email_accounts,
//        "edit_email_accounts" -> o.edit_email_accounts,
//        "delete_email_accounts" -> o.delete_email_accounts,
//        "view_linkedin_accounts" -> o.view_linkedin_accounts,
//        "edit_linkedin_accounts" -> o.edit_linkedin_accounts,
//        "delete_linkedin_accounts" -> o.delete_linkedin_accounts,
//        "view_whatsapp_accounts" -> o.view_whatsapp_accounts,
//        "edit_whatsapp_accounts" -> o.edit_whatsapp_accounts,
//        "delete_whatsapp_accounts" -> o.delete_whatsapp_accounts,
//        "view_sms_accounts" -> o.view_sms_accounts,
//        "edit_sms_accounts" -> o.edit_sms_accounts,
//        "delete_sms_accounts" -> o.delete_sms_accounts,
        "view_channels" -> o.view_channels,
        "edit_channels" -> o.edit_channels,
        "delete_channels" -> o.delete_channels,
        "view_tasks" -> o.view_tasks,
        "edit_tasks" ->o.edit_tasks,
        "delete_tasks" -> o.delete_tasks,
        "edit_pipeline_details" -> o.edit_pipeline_details,
        "delete_pipeline_details" -> o.delete_pipeline_details,
        "view_opportunities" -> o.view_opportunities,
        "edit_opportunities" -> o.edit_opportunities,
        "delete_opportunities" -> o.delete_opportunities
      )
    }
  }


  def getRolePermission(
                         permissionRequired: PermType,
                         userRole: RolePermissionDataV2
                       ): RolePermV2 = {

    val p = userRole.permissions

    // EDITROLEBACKEND
    val permissionFound = permissionRequired match {

      case PermType.JUST_LOGGEDIN => p.just_loggedin
      case PermType.ZAPIER_ACCESS => p.zapier_access

      case PermType.MANAGE_BILLING => p.manage_billing
      case PermType.VIEW_USER_MANAGEMENT => p.view_user_management
      case PermType.EDIT_USER_MANAGEMENT => p.edit_user_management


      case PermType.VIEW_TEAM_CONFIG => p.view_team_config
      case PermType.EDIT_TEAM_CONFIG => p.edit_team_config

      //      case PermType.VIEW_ROLES => p.view_user_management
      //      case PermType.EDIT_ROLES => p.edit_user_management

      /*
      case PermType.VIEW_USER_SETTINGS => p.view_user_settings
      case PermType.EDIT_USER_SETTINGS => p.edit_user_settings
      */

      case PermType.VIEW_PROSPECTS => p.view_prospects
      case PermType.EDIT_PROSPECTS => p.edit_prospects
      case PermType.DELETE_PROSPECTS => p.delete_prospects

      case PermType.VIEW_CAMPAIGNS => p.view_campaigns
      case PermType.EDIT_CAMPAIGNS => p.edit_campaigns
      case PermType.DELETE_CAMPAIGNS => p.delete_campaigns
      case PermType.CHANGE_CAMPAIGN_STATUS => p.change_campaign_status

      case PermType.VIEW_REPORTS => p.view_reports
      case PermType.EDIT_REPORTS => p.edit_reports
      case PermType.DOWNLOAD_REPORTS => p.download_reports

//      case PermType.VIEW_INBOX => p.view_inbox
//      case PermType.EDIT_INBOX => p.edit_inbox
      case PermType.SEND_MANUAL_EMAIL => p.send_manual_email

      case PermType.VIEW_TEMPLATES => p.view_templates
      case PermType.EDIT_TEMPLATES => p.edit_templates
      case PermType.DELETE_TEMPLATES => p.delete_templates

      case PermType.VIEW_BLACKLIST => p.view_blacklist
      case PermType.EDIT_BLACKLIST => p.edit_blacklist

      case PermType.VIEW_WORKFLOWS => p.view_workflows
      case PermType.EDIT_WORKFLOWS => p.edit_workflows

      case PermType.VIEW_WEBHOOKS => p.view_webhooks
      case PermType.EDIT_WEBHOOKS => p.edit_webhooks

//      case PermType.VIEW_PROSPECT_ACCOUNTS => p.view_prospect_accounts
//      case PermType.EDIT_PROSPECT_ACCOUNTS => p.edit_prospect_accounts

//      case PermType.VIEW_EMAIL_ACCOUNTS => p.view_email_accounts
//      case PermType.EDIT_EMAIL_ACCOUNTS => p.edit_email_accounts
//      case PermType.DELETE_EMAIL_ACCOUNTS => p.delete_email_accounts
//
//      case PermType.VIEW_LINKEDIN_ACCOUNTS => p.view_linkedin_accounts
//      case PermType.EDIT_LINKEDIN_ACCOUNTS => p.edit_linkedin_accounts
//      case PermType.DELETE_LINKEDIN_ACCOUNTS => p.delete_linkedin_accounts
//
//      case PermType.VIEW_WHATSAPP_ACCOUNTS => p.view_whatsapp_accounts
//      case PermType.EDIT_WHATSAPP_ACCOUNTS => p.edit_whatsapp_accounts
//      case PermType.DELETE_WHATSAPP_ACCOUNTS => p.delete_whatsapp_accounts
//
//      case PermType.VIEW_SMS_ACCOUNTS => p.view_sms_accounts
//      case PermType.EDIT_SMS_ACCOUNTS => p.edit_sms_accounts
//      case PermType.DELETE_SMS_ACCOUNTS => p.delete_sms_accounts

      case PermType.VIEW_CHANNELS => p.view_channels
      case PermType.EDIT_CHANNELS => p.edit_channels
      case PermType.DELETE_CHANNELS => p.delete_channels

      case PermType.VIEW_TASKS => p.view_tasks
      case PermType.EDIT_TASKS => p.edit_tasks
      case PermType.DELETE_TASKS => p.delete_tasks

      case PermType.EDIT_PIPELINE_DETAILS => p.edit_pipeline_details
      case PermType.DELETE_PIPELINE_DETAILS => p.delete_pipeline_details

      case PermType.VIEW_OPPORTUNITIES => p.view_opportunities
      case PermType.EDIT_OPPORTUNITIES => p.edit_opportunities
      case PermType.DELETE_OPPORTUNITIES => p.delete_opportunities
    }

    permissionFound
  }
}

case class RolePermissionDataV2(
  id: Long,
  role_name: TeamAccountRole,
  permissions: RolePermissionsV2
)

///////////////////////////////////
///////////////////////////////////
///////////////////////////////////
object RolePermissionDataV2{

  given format: OFormat[RolePermissionDataV2] = Json.format[RolePermissionDataV2]


  // private val notPermitted = RolePermConfig(permitted = false)

  def toRolePermissionApi(data: RolePermissionsInDBV2): RolePermissionDataV2 = {


    // EDITROLEBACKEND
    RolePermissionDataV2(
      id = data.id,
      role_name = data.role_name,

      permissions = RolePermissionsV2(


        manage_billing = RolePermV2(
          ownership = data.manage_billing_v2,
          entity = SrResourceTypes.OTHER,
          permissionLevel = PermissionLevelForValidation.IgnoreValidation,
          permissionType = PermType.MANAGE_BILLING
        ),
        view_user_management = RolePermV2(
          ownership = data.view_user_management_v2,
          entity = SrResourceTypes.USER,
          permissionLevel = PermissionLevelForValidation.View,
          permissionType = PermType.VIEW_USER_MANAGEMENT
        ),
        edit_user_management = RolePermV2(
          ownership = data.edit_user_management_v2,
          entity = SrResourceTypes.USER,
          permissionLevel = PermissionLevelForValidation.Edit,
          permissionType = PermType.EDIT_USER_MANAGEMENT
        ),


        view_team_config = RolePermV2(
          ownership = data.view_team_config_v2,
          entity = SrResourceTypes.TEAM,
          permissionLevel = PermissionLevelForValidation.View,
          permissionType = PermType.VIEW_TEAM_CONFIG
        ),
        edit_team_config = RolePermV2(
          ownership = data.edit_team_config_v2,
          entity = SrResourceTypes.TEAM,
          permissionLevel = PermissionLevelForValidation.Edit,
          permissionType = PermType.EDIT_TEAM_CONFIG
        ),


        /*
        view_user_settings = RolePermConfig(
          permitted = data.view_user_settings,
          ownership = data.view_user_settings_ownership
        ),
        edit_user_settings = RolePermConfig(
          permitted = data.edit_user_settings,
          ownership = data.edit_user_settings_ownership
        ),
        */



        view_prospects = RolePermV2(
          ownership = data.view_prospects_v2,
          entity = SrResourceTypes.PROSPECT,
          permissionLevel = PermissionLevelForValidation.View,
          permissionType = PermType.VIEW_PROSPECTS
        ),
        edit_prospects = RolePermV2(
          ownership = data.edit_prospects_v2,
          entity = SrResourceTypes.PROSPECT,
          permissionLevel = PermissionLevelForValidation.Edit,
          permissionType = PermType.EDIT_PROSPECTS
        ),
        delete_prospects = RolePermV2(
          ownership = data.delete_prospects_v2,
          entity = SrResourceTypes.PROSPECT,
          permissionLevel = PermissionLevelForValidation.Delete,
          permissionType = PermType.DELETE_PROSPECTS
        ),

        view_campaigns = RolePermV2(
          ownership = data.view_campaigns_v2,
          entity = SrResourceTypes.CAMPAIGN,
          permissionLevel = PermissionLevelForValidation.View,
          permissionType = PermType.VIEW_CAMPAIGNS
        ),
        edit_campaigns = RolePermV2(
          ownership = data.edit_campaigns_v2,
          entity = SrResourceTypes.CAMPAIGN,
          permissionLevel = PermissionLevelForValidation.Edit,
          permissionType = PermType.EDIT_CAMPAIGNS
        ),
        delete_campaigns = RolePermV2(
          ownership = data.delete_campaigns_v2,
          entity = SrResourceTypes.CAMPAIGN,
          permissionLevel = PermissionLevelForValidation.Delete,
          permissionType = PermType.DELETE_CAMPAIGNS
        ),
        change_campaign_status = RolePermV2(
          ownership = data.change_campaign_status_v2,
          entity = SrResourceTypes.CAMPAIGN,
          permissionLevel = PermissionLevelForValidation.Edit,
          permissionType = PermType.CHANGE_CAMPAIGN_STATUS
        ),

        view_reports = RolePermV2(
          ownership = data.view_reports_v2,
          entity = SrResourceTypes.REPORT,
          permissionLevel = PermissionLevelForValidation.View,
          permissionType = PermType.VIEW_REPORTS
        ),
        edit_reports = RolePermV2(
          ownership = data.edit_reports_v2,
          entity = SrResourceTypes.REPORT,
          permissionLevel = PermissionLevelForValidation.Edit,
          permissionType = PermType.EDIT_REPORTS
        ),
        download_reports = RolePermV2(
          ownership = data.download_reports_v2,
          entity = SrResourceTypes.REPORT,
          permissionLevel = PermissionLevelForValidation.Edit,
          permissionType = PermType.DOWNLOAD_REPORTS
        ),

//        view_inbox = RolePermV2(
//          ownership = data.view_inbox_v2,
//          entity = SrResourceTypes.INBOX,
//          permissionLevel = PermissionLevelForValidation.View,
//          permissionType = PermType.VIEW_INBOX
//        ),
//        edit_inbox = RolePermV2(
//          ownership = data.edit_inbox_v2,
//          entity = SrResourceTypes.INBOX,
//          permissionLevel = PermissionLevelForValidation.Edit,
//          permissionType = PermType.EDIT_INBOX
//        ),
        send_manual_email = RolePermV2(
          ownership = data.send_manual_email_v2,
          entity = SrResourceTypes.INBOX,
          permissionLevel = PermissionLevelForValidation.View,
          permissionType = PermType.SEND_MANUAL_EMAIL
        ),

        view_templates = RolePermV2(
          ownership = data.view_templates_v2,
          entity = SrResourceTypes.TEMPLATE,
          permissionLevel = PermissionLevelForValidation.View,
          permissionType = PermType.VIEW_TEMPLATES
        ),
        edit_templates = RolePermV2(
          ownership = data.edit_templates_v2,
          entity = SrResourceTypes.TEMPLATE,
          permissionLevel = PermissionLevelForValidation.Edit,
          permissionType = PermType.EDIT_TEMPLATES
        ),
        delete_templates = RolePermV2(
          ownership = data.delete_templates_v2,
          entity = SrResourceTypes.TEMPLATE,
          permissionLevel = PermissionLevelForValidation.Delete,
          permissionType = PermType.DELETE_TEMPLATES
        ),

        // everyone has permission to blacklist
        view_blacklist = RolePermV2(
          ownership = data.view_blacklist_v2,
          entity = SrResourceTypes.BLACKLIST,
          permissionLevel = PermissionLevelForValidation.View,
          permissionType = PermType.VIEW_BLACKLIST
        ),
        edit_blacklist = RolePermV2(
          ownership = data.edit_blacklist_v2,
          entity = SrResourceTypes.BLACKLIST,
          permissionLevel = PermissionLevelForValidation.Edit,
          permissionType = PermType.EDIT_BLACKLIST
        ),


        view_workflows = RolePermV2(
          ownership = data.view_workflows_v2,
          entity = SrResourceTypes.WORKFLOW,
          permissionLevel = PermissionLevelForValidation.View,
          permissionType = PermType.VIEW_WORKFLOWS
        ),
        edit_workflows = RolePermV2(
          ownership = data.edit_workflows_v2,
          entity = SrResourceTypes.WORKFLOW,
          permissionLevel = PermissionLevelForValidation.Edit,
          permissionType = PermType.EDIT_WORKFLOWS
        ),

        view_webhooks = RolePermV2(
          ownership = data.view_webhooks_v2,
          entity = SrResourceTypes.WEBHOOK,
          permissionLevel = PermissionLevelForValidation.View,
          permissionType = PermType.VIEW_WEBHOOKS
        ),
        edit_webhooks = RolePermV2(
          ownership = data.edit_webhooks_v2,
          entity = SrResourceTypes.WEBHOOK,
          permissionLevel = PermissionLevelForValidation.Edit,
          permissionType = PermType.EDIT_WEBHOOKS
        ),

//        view_prospect_accounts = RolePermV2(
//          ownership = data.view_prospect_accounts_v2,
//          entity = SrResourceTypes.PROSPECT_ACCOUNT,
//          permissionLevel = PermissionLevelForValidation.View,
//          permissionType = PermType.VIEW_PROSPECT_ACCOUNTS
//        ),
//        edit_prospect_accounts = RolePermV2(
//          ownership = data.edit_prospect_accounts_v2,
//          entity = SrResourceTypes.PROSPECT_ACCOUNT,
//          permissionLevel = PermissionLevelForValidation.Edit,
//          permissionType = PermType.EDIT_PROSPECT_ACCOUNTS
//        ),

//        view_email_accounts = RolePermV2(
//          ownership = data.view_email_accounts_v2,
//          entity = SrResourceTypes.EMAIL_ACCOUNT,
//          permissionLevel = PermissionLevelForValidation.View,
//          permissionType = PermType.VIEW_EMAIL_ACCOUNTS
//        ),
//        edit_email_accounts = RolePermV2(
//          ownership = data.edit_email_accounts_v2,
//          entity = SrResourceTypes.EMAIL_ACCOUNT,
//          permissionLevel = PermissionLevelForValidation.Edit,
//          permissionType = PermType.EDIT_EMAIL_ACCOUNTS
//        ),
//        delete_email_accounts = RolePermV2(
//          ownership = data.delete_email_accounts_v2,
//          entity = SrResourceTypes.EMAIL_ACCOUNT,
//          permissionLevel = PermissionLevelForValidation.Delete,
//          permissionType = PermType.DELETE_EMAIL_ACCOUNTS
//        ),

//        view_linkedin_accounts = RolePermV2(
//          ownership = data.view_linkedin_accounts_v2,
//          entity = SrResourceTypes.LINKEDIN_ACCOUNT,
//          permissionLevel = PermissionLevelForValidation.View,
//          permissionType = PermType.VIEW_LINKEDIN_ACCOUNTS
//        ),
//        edit_linkedin_accounts = RolePermV2(
//          ownership = data.edit_linkedin_accounts_v2,
//          entity = SrResourceTypes.LINKEDIN_ACCOUNT,
//          permissionLevel = PermissionLevelForValidation.Edit,
//          permissionType = PermType.EDIT_LINKEDIN_ACCOUNTS
//        ),
//        delete_linkedin_accounts = RolePermV2(
//          ownership = data.delete_linkedin_accounts_v2,
//          entity = SrResourceTypes.LINKEDIN_ACCOUNT,
//          permissionLevel = PermissionLevelForValidation.Delete,
//          permissionType = PermType.DELETE_LINKEDIN_ACCOUNTS
//        ),

//        view_whatsapp_accounts = RolePermV2(
//          ownership = data.view_whatsapp_accounts_v2,
//          entity = SrResourceTypes.WHATSAPP_ACCOUNT,
//          permissionLevel = PermissionLevelForValidation.View,
//          permissionType = PermType.VIEW_WHATSAPP_ACCOUNTS
//        ),
//        edit_whatsapp_accounts = RolePermV2(
//          ownership = data.edit_whatsapp_accounts_v2,
//          entity = SrResourceTypes.WHATSAPP_ACCOUNT,
//          permissionLevel = PermissionLevelForValidation.Edit,
//          permissionType = PermType.EDIT_WHATSAPP_ACCOUNTS
//        ),
//        delete_whatsapp_accounts = RolePermV2(
//          ownership = data.delete_whatsapp_accounts_v2,
//          entity = SrResourceTypes.WHATSAPP_ACCOUNT,
//          permissionLevel = PermissionLevelForValidation.Delete,
//          permissionType = PermType.DELETE_WHATSAPP_ACCOUNTS
//        ),

//        view_sms_accounts = RolePermV2(
//          ownership = data.view_sms_accounts_v2,
//          entity = SrResourceTypes.SMS_ACCOUNT,
//          permissionLevel = PermissionLevelForValidation.View,
//          permissionType = PermType.VIEW_SMS_ACCOUNTS
//        ),
//        edit_sms_accounts = RolePermV2(
//          ownership = data.edit_sms_accounts_v2,
//          entity = SrResourceTypes.SMS_ACCOUNT,
//          permissionLevel = PermissionLevelForValidation.Edit,
//          permissionType = PermType.EDIT_SMS_ACCOUNTS
//        ),
//        delete_sms_accounts = RolePermV2(
//          ownership = data.delete_sms_accounts_v2,
//          entity = SrResourceTypes.SMS_ACCOUNT,
//          permissionLevel = PermissionLevelForValidation.Delete,
//          permissionType = PermType.DELETE_SMS_ACCOUNTS
//        ),

          view_channels = RolePermV2(
            ownership = data.view_channels_v2,
            entity = SrResourceTypes.CHANNEL,
            permissionLevel = PermissionLevelForValidation.View,
            permissionType = PermType.VIEW_CHANNELS
          ),
          edit_channels = RolePermV2(
            ownership = data.edit_channels_v2,
            entity = SrResourceTypes.CHANNEL,
            permissionLevel = PermissionLevelForValidation.Edit,
            permissionType = PermType.EDIT_CHANNELS
          ),
          delete_channels = RolePermV2(
            ownership = data.delete_channels_v2,
            entity = SrResourceTypes.CHANNEL,
            permissionLevel = PermissionLevelForValidation.Delete,
            permissionType = PermType.DELETE_CHANNELS
          ),

        view_tasks = RolePermV2(
          ownership = data.view_tasks_v2,
          entity = SrResourceTypes.TASK,
          permissionLevel = PermissionLevelForValidation.View,
          permissionType = PermType.VIEW_TASKS
        ),
        edit_tasks = RolePermV2(
          ownership = data.edit_tasks_v2,
          entity = SrResourceTypes.TASK,
          permissionLevel = PermissionLevelForValidation.Edit,
          permissionType = PermType.EDIT_TASKS
        ),
        delete_tasks = RolePermV2(
          ownership = data.delete_tasks_v2,
          entity = SrResourceTypes.TASK,
          permissionLevel = PermissionLevelForValidation.Delete,
          permissionType = PermType.DELETE_TASKS
        ),

        edit_pipeline_details = RolePermV2(
          ownership = data.edit_pipeline_details_v2,
          entity = SrResourceTypes.PIPELINE,
          permissionLevel = PermissionLevelForValidation.Edit,
          permissionType = PermType.EDIT_PIPELINE_DETAILS
        ),

        delete_pipeline_details = RolePermV2(
          ownership = data.delete_pipeline_details_v2,
          entity = SrResourceTypes.PIPELINE,
          permissionLevel = PermissionLevelForValidation.Delete,
          permissionType = PermType.DELETE_PIPELINE_DETAILS
        ),

        view_opportunities = RolePermV2(
          ownership = data.view_opportunities_v2,
          entity = SrResourceTypes.OPPORTUNITY,
          permissionLevel = PermissionLevelForValidation.View,
          permissionType = PermType.VIEW_OPPORTUNITIES
        ),

        edit_opportunities = RolePermV2(
          ownership = data.edit_opportunities_v2,
          entity = SrResourceTypes.OPPORTUNITY,
          permissionLevel = PermissionLevelForValidation.Edit,
          permissionType = PermType.EDIT_OPPORTUNITIES
        ),

        delete_opportunities = RolePermV2(
          ownership = data.delete_opportunities_v2,
          entity = SrResourceTypes.OPPORTUNITY,
          permissionLevel = PermissionLevelForValidation.Delete,
          permissionType = PermType.DELETE_OPPORTUNITIES
        ),
      )
    )
  }
}

class RolePermissionDataDAOV2 {

//  implicit lazy val format = Jsonx.formatCaseClassUseDefaults[RolePermissionData]

  /*private val ALL = RolePermConfig(permitted = true, ownership = PermissionOwnership.ALL)
  private val OWNED = RolePermConfig(permitted = true, ownership = PermissionOwnership.OWNED)

  val defaultPermissions = RolePermissions(
    just_loggedin = permitted,
    zapier_access = permitted,
    manage_billing = notPermitted,
    user_management = notPermitted,
    edit_roles = notPermitted,

    view_user_settings = OWNED,
    edit_user_settings = OWNED,

    view_prospects = OWNED,
    edit_prospects = OWNED,
    delete_prospects = OWNED,

    view_campaigns = OWNED,
    edit_campaigns = OWNED,
    delete_campaigns = OWNED,
    change_campaign_status = OWNED,

    view_reports = OWNED,
    edit_reports = OWNED,
    download_reports = OWNED,

    view_inbox = OWNED,
    edit_inbox = OWNED,
    send_manual_email = OWNED,

    view_templates = OWNED,
    edit_templates = OWNED,
    delete_templates = OWNED,

    // everyone has permission to blacklist
    view_blacklist = ALL,
    edit_blacklist = ALL,

    view_email_accounts = OWNED,
    edit_email_accounts = OWNED,
    delete_email_accounts = OWNED


  )


  val ORG_ADMIN = RolePermissionData(

    id = 0,

    role_name = TeamAccountRole.OWNER,

    permissions = defaultPermissions.copy(
      manage_billing = permitted,
      user_management = permitted,
      edit_roles = permitted,

      view_user_settings = ALL,
      edit_user_settings = OWNED,

      view_prospects = ALL,
      edit_prospects = ALL,
      delete_prospects = ALL,

      view_campaigns = ALL,
      edit_campaigns = ALL,
      delete_campaigns = ALL,
      change_campaign_status = ALL,

      view_reports = ALL,
      edit_reports = ALL,
      download_reports = ALL,

      view_inbox = ALL,
      edit_inbox = ALL,
      send_manual_email = ALL,

      view_templates = ALL,
      edit_templates = ALL,
      delete_templates = ALL,

      view_blacklist = ALL,
      edit_blacklist = ALL,

      view_email_accounts = ALL,
      edit_email_accounts = ALL,
      delete_email_accounts = ALL

    )
  )

  val TEAM_ADMIN = RolePermissionData(

    id = 0,

    role_name = TeamAccountRole.ADMIN,

    permissions = defaultPermissions.copy(
      manage_billing = notPermitted,
      user_management = permitted,
      edit_roles = permitted,

      view_user_settings = ALL,
      edit_user_settings = OWNED,

      view_prospects = ALL,
      edit_prospects = ALL,
      delete_prospects = ALL,

      view_campaigns = ALL,
      edit_campaigns = ALL,
      delete_campaigns = ALL,
      change_campaign_status = ALL,

      view_reports = ALL,
      edit_reports = ALL,
      download_reports = ALL,

      view_inbox = ALL,
      edit_inbox = ALL,
      send_manual_email = ALL,

      view_templates = ALL,
      edit_templates = ALL,
      delete_templates = ALL,

      view_blacklist = ALL,
      edit_blacklist = ALL,

      view_email_accounts = ALL,
      edit_email_accounts = ALL,
      delete_email_accounts = ALL

    )
  )

  val TEAM_MEMBER = RolePermissionData(

    id = 0,

    role_name = TeamAccountRole.MEMBER,

    permissions = defaultPermissions.copy(
      view_prospects = ALL,
      edit_prospects = OWNED
    )
  )*/


  implicit val session: AutoSession.type = AutoSession

  // EDITROLEBACKEND
  def __insertDefaultRoles(orgId: Long, simpler_perms_flag : Boolean)  = {

    val ownerDefaultRoles = RolePermissionDataDAOV2.defaultRoles(
      role = TeamAccountRole.OWNER,
      simpler_perm_flag = simpler_perms_flag
    )
    val agencyAdminDefaultRoles = RolePermissionDataDAOV2.defaultRoles(
      role = TeamAccountRole.AGENCY_ADMIN,
      simpler_perm_flag = simpler_perms_flag
    )

    val adminDefaultRoles = RolePermissionDataDAOV2.defaultRoles(
      role = TeamAccountRole.ADMIN,
      simpler_perm_flag = simpler_perms_flag
    )

    val memberDefaultRoles = RolePermissionDataDAOV2.defaultRoles(
      role = TeamAccountRole.MEMBER,
      simpler_perm_flag = simpler_perms_flag
    )


      sql"""
          INSERT INTO user_roles
          (

            role_name,
            org_id,

            manage_billing_v2,

            view_user_management_v2,
            edit_user_management_v2,

            view_team_config_v2,
            edit_team_config_v2,

            view_roles,
            edit_roles,


            view_user_settings_v2,
            edit_user_settings_v2,

            view_prospects_v2,
            edit_prospects_v2,
            delete_prospects_v2,

            view_campaigns_v2,
            edit_campaigns_v2,
            delete_campaigns_v2,
            change_campaign_status_v2,

            view_reports_v2,
            edit_reports_v2,
            download_reports_v2,

            send_manual_email_v2,

            view_templates_v2,
            edit_templates_v2,
            delete_templates_v2,

            view_blacklist_v2,
            edit_blacklist_v2,

            view_workflows_v2,
            edit_workflows_v2,

            view_webhooks_v2,
            edit_webhooks_v2,

            view_channels_v2,
            edit_channels_v2,
            delete_channels_v2,

            view_tasks_v2,
            edit_tasks_v2,
            delete_tasks_v2,

            edit_pipeline_details_v2,
            delete_pipeline_details_v2,

            view_opportunities_v2,
            edit_opportunities_v2,
            delete_opportunities_v2

          )
          VALUES

          (
            ${TeamAccountRole.OWNER.toString},
            $orgId,

            ${ownerDefaultRoles.manage_billing_v2.toKey},

            ${ownerDefaultRoles.view_user_management_v2.toKey},
            ${ownerDefaultRoles.edit_user_management_v2.toKey},

            ${ownerDefaultRoles.view_team_config_v2.toKey},
            ${ownerDefaultRoles.edit_team_config_v2.toKey},

            -- view_roles
            true,

            -- edit_roles
            true,

            -- view_user_settings
            'all',

            -- edit_user_settings
            'all',

            ${ownerDefaultRoles.view_prospects_v2.toKey},
            ${ownerDefaultRoles.edit_prospects_v2.toKey},
            ${ownerDefaultRoles.delete_prospects_v2.toKey},

            ${ownerDefaultRoles.view_campaigns_v2.toKey},
            ${ownerDefaultRoles.edit_campaigns_v2.toKey},
            ${ownerDefaultRoles.delete_campaigns_v2.toKey},
            ${ownerDefaultRoles.change_campaign_status_v2.toKey},

            ${ownerDefaultRoles.view_reports_v2.toKey},
            ${ownerDefaultRoles.edit_reports_v2.toKey},
            ${ownerDefaultRoles.download_reports_v2.toKey},

            ${ownerDefaultRoles.send_manual_email_v2.toKey},

            ${ownerDefaultRoles.view_templates_v2.toKey},
            ${ownerDefaultRoles.edit_templates_v2.toKey},
            ${ownerDefaultRoles.delete_templates_v2.toKey},

            ${ownerDefaultRoles.view_blacklist_v2.toKey},
            ${ownerDefaultRoles.edit_blacklist_v2.toKey},

            ${ownerDefaultRoles.view_workflows_v2.toKey},
            ${ownerDefaultRoles.edit_workflows_v2.toKey},

            ${ownerDefaultRoles.view_webhooks_v2.toKey},
            ${ownerDefaultRoles.edit_webhooks_v2.toKey},

            ${ownerDefaultRoles.view_channels_v2.toKey},
            ${ownerDefaultRoles.edit_channels_v2.toKey},
            ${ownerDefaultRoles.delete_channels_v2.toKey},

            ${ownerDefaultRoles.view_tasks_v2.toKey},
            ${ownerDefaultRoles.edit_tasks_v2.toKey},
            ${ownerDefaultRoles.delete_tasks_v2.toKey},

            ${ownerDefaultRoles.edit_pipeline_details_v2.toKey},
            ${ownerDefaultRoles.delete_pipeline_details_v2.toKey},

            ${ownerDefaultRoles.view_opportunities_v2.toKey},
            ${ownerDefaultRoles.edit_opportunities_v2.toKey},
            ${ownerDefaultRoles.delete_opportunities_v2.toKey}

          ),

          (
            ${TeamAccountRole.AGENCY_ADMIN.toString},
            $orgId,

            ${agencyAdminDefaultRoles.manage_billing_v2.toKey},

            ${agencyAdminDefaultRoles.view_user_management_v2.toKey},
            ${agencyAdminDefaultRoles.edit_user_management_v2.toKey},

            ${agencyAdminDefaultRoles.view_team_config_v2.toKey},
            ${agencyAdminDefaultRoles.edit_team_config_v2.toKey},

            -- view_roles
            true,

            -- edit_roles
            true,

            -- view_user_settings
            'all',

            -- edit_user_settings
            'all',

            ${agencyAdminDefaultRoles.view_prospects_v2.toKey},
            ${agencyAdminDefaultRoles.edit_prospects_v2.toKey},
            ${agencyAdminDefaultRoles.delete_prospects_v2.toKey},

            ${agencyAdminDefaultRoles.view_campaigns_v2.toKey},
            ${agencyAdminDefaultRoles.edit_campaigns_v2.toKey},
            ${agencyAdminDefaultRoles.delete_campaigns_v2.toKey},
            ${agencyAdminDefaultRoles.change_campaign_status_v2.toKey},

            ${agencyAdminDefaultRoles.view_reports_v2.toKey},
            ${agencyAdminDefaultRoles.edit_reports_v2.toKey},
            ${agencyAdminDefaultRoles.download_reports_v2.toKey},

            ${agencyAdminDefaultRoles.send_manual_email_v2.toKey},

            ${agencyAdminDefaultRoles.view_templates_v2.toKey},
            ${agencyAdminDefaultRoles.edit_templates_v2.toKey},
            ${agencyAdminDefaultRoles.delete_templates_v2.toKey},

            ${agencyAdminDefaultRoles.view_blacklist_v2.toKey},
            ${agencyAdminDefaultRoles.edit_blacklist_v2.toKey},

            ${agencyAdminDefaultRoles.view_workflows_v2.toKey},
            ${agencyAdminDefaultRoles.edit_workflows_v2.toKey},

            ${agencyAdminDefaultRoles.view_webhooks_v2.toKey},
            ${agencyAdminDefaultRoles.edit_webhooks_v2.toKey},

            ${agencyAdminDefaultRoles.view_channels_v2.toKey},
            ${agencyAdminDefaultRoles.edit_channels_v2.toKey},
            ${agencyAdminDefaultRoles.delete_channels_v2.toKey},

            ${agencyAdminDefaultRoles.view_tasks_v2.toKey},
            ${agencyAdminDefaultRoles.edit_tasks_v2.toKey},
            ${agencyAdminDefaultRoles.delete_tasks_v2.toKey},

            ${agencyAdminDefaultRoles.edit_pipeline_details_v2.toKey},
            ${agencyAdminDefaultRoles.delete_pipeline_details_v2.toKey},

            ${agencyAdminDefaultRoles.view_opportunities_v2.toKey},
            ${agencyAdminDefaultRoles.edit_opportunities_v2.toKey},
            ${agencyAdminDefaultRoles.delete_opportunities_v2.toKey}

          ),

          (
            ${TeamAccountRole.ADMIN.toString},
            $orgId,

            ${adminDefaultRoles.manage_billing_v2.toKey},

            ${adminDefaultRoles.view_user_management_v2.toKey},
            ${adminDefaultRoles.edit_user_management_v2.toKey},

            ${adminDefaultRoles.view_team_config_v2.toKey},
            ${adminDefaultRoles.edit_team_config_v2.toKey},

            -- view_roles
            true,

            -- edit_roles
            true,

            -- view_user_settings
            'all',

            -- edit_user_settings
            'all',

            ${adminDefaultRoles.view_prospects_v2.toKey},
            ${adminDefaultRoles.edit_prospects_v2.toKey},
            ${adminDefaultRoles.delete_prospects_v2.toKey},

            ${adminDefaultRoles.view_campaigns_v2.toKey},
            ${adminDefaultRoles.edit_campaigns_v2.toKey},
            ${adminDefaultRoles.delete_campaigns_v2.toKey},
            ${adminDefaultRoles.change_campaign_status_v2.toKey},

            ${adminDefaultRoles.view_reports_v2.toKey},
            ${adminDefaultRoles.edit_reports_v2.toKey},
            ${adminDefaultRoles.download_reports_v2.toKey},

            ${adminDefaultRoles.send_manual_email_v2.toKey},

            ${adminDefaultRoles.view_templates_v2.toKey},
            ${adminDefaultRoles.edit_templates_v2.toKey},
            ${adminDefaultRoles.delete_templates_v2.toKey},

            ${adminDefaultRoles.view_blacklist_v2.toKey},
            ${adminDefaultRoles.edit_blacklist_v2.toKey},

            ${adminDefaultRoles.view_workflows_v2.toKey},
            ${adminDefaultRoles.edit_workflows_v2.toKey},

            ${adminDefaultRoles.view_webhooks_v2.toKey},
            ${adminDefaultRoles.edit_webhooks_v2.toKey},

            ${adminDefaultRoles.view_channels_v2.toKey},
            ${adminDefaultRoles.edit_channels_v2.toKey},
            ${adminDefaultRoles.delete_channels_v2.toKey},

            ${adminDefaultRoles.view_tasks_v2.toKey},
            ${adminDefaultRoles.edit_tasks_v2.toKey},
            ${adminDefaultRoles.delete_tasks_v2.toKey},

            ${adminDefaultRoles.edit_pipeline_details_v2.toKey},
            ${adminDefaultRoles.delete_pipeline_details_v2.toKey},

            ${adminDefaultRoles.view_opportunities_v2.toKey},
            ${adminDefaultRoles.edit_opportunities_v2.toKey},
            ${adminDefaultRoles.delete_opportunities_v2.toKey}

          ),

          (
            ${TeamAccountRole.MEMBER.toString},
            $orgId,

            ${memberDefaultRoles.manage_billing_v2.toKey},

            ${memberDefaultRoles.view_user_management_v2.toKey},
            ${memberDefaultRoles.edit_user_management_v2.toKey},

            ${memberDefaultRoles.view_team_config_v2.toKey},
            ${memberDefaultRoles.edit_team_config_v2.toKey},

            -- view_roles
            true,

            -- edit_roles
            false,


            -- view_user_settings
            'owned',

            -- edit_user_settings
            'owned',

            ${memberDefaultRoles.view_prospects_v2.toKey},
            ${memberDefaultRoles.edit_prospects_v2.toKey},
            ${memberDefaultRoles.delete_prospects_v2.toKey},

            ${memberDefaultRoles.view_campaigns_v2.toKey},
            ${memberDefaultRoles.edit_campaigns_v2.toKey},
            ${memberDefaultRoles.delete_campaigns_v2.toKey},
            ${memberDefaultRoles.change_campaign_status_v2.toKey},

            ${memberDefaultRoles.view_reports_v2.toKey},
            ${memberDefaultRoles.edit_reports_v2.toKey},
            ${memberDefaultRoles.download_reports_v2.toKey},

            ${memberDefaultRoles.send_manual_email_v2.toKey},

            ${memberDefaultRoles.view_templates_v2.toKey},
            ${memberDefaultRoles.edit_templates_v2.toKey},
            ${memberDefaultRoles.delete_templates_v2.toKey},

            ${memberDefaultRoles.view_blacklist_v2.toKey},
            ${memberDefaultRoles.edit_blacklist_v2.toKey},

            ${memberDefaultRoles.view_workflows_v2.toKey},
            ${memberDefaultRoles.edit_workflows_v2.toKey},

            ${memberDefaultRoles.view_webhooks_v2.toKey},
            ${memberDefaultRoles.edit_webhooks_v2.toKey},

            ${memberDefaultRoles.view_channels_v2.toKey},
            ${memberDefaultRoles.edit_channels_v2.toKey},
            ${memberDefaultRoles.delete_channels_v2.toKey},

            ${memberDefaultRoles.view_tasks_v2.toKey},
            ${memberDefaultRoles.edit_tasks_v2.toKey},
            ${memberDefaultRoles.delete_tasks_v2.toKey},

            ${memberDefaultRoles.edit_pipeline_details_v2.toKey},
            ${memberDefaultRoles.delete_pipeline_details_v2.toKey},

            ${memberDefaultRoles.view_opportunities_v2.toKey},
            ${memberDefaultRoles.edit_opportunities_v2.toKey},
            ${memberDefaultRoles.delete_opportunities_v2.toKey}

          )

          ON CONFLICT DO NOTHING
          RETURNING *;
        """

  }

  def insertDefaultRoles(orgId: Long, is_simpler_perms: Boolean)(implicit session:DBSession): Try[Seq[RolePermissionDataV2]] = Try{


      __insertDefaultRoles(orgId = orgId, simpler_perms_flag = is_simpler_perms)
        .map(rs => RolePermissionsInDBV2.fromDb(rs))
        .list
        .apply()
        .map(RolePermissionDataV2.toRolePermissionApi)

  }

  def findRoleById(userRoleId: Long, orgId: Long): Try[Option[RolePermissionDataV2]] = Try {
    DB readOnly { implicit session =>

      sql"""
      select * from user_roles where org_id = $orgId and id = $userRoleId;

      """
        .map(RolePermissionsInDBV2.fromDb)
        .headOption
        .apply()
        .map(RolePermissionDataV2.toRolePermissionApi)

    }
  }

  private def __findAllRoles(orgId: Long) = {
    DB readOnly { implicit session =>

      sql"""
      select * from user_roles where org_id = $orgId;

      """

    }
  }

  def findAllRoles(orgId: Long): Try[Seq[RolePermissionDataV2]] = Try {
    val roles = DB readOnly { implicit session =>

      __findAllRoles(orgId = orgId)
        .map(RolePermissionsInDBV2.fromDb)
        .list
        .apply()
        .map(RolePermissionDataV2.toRolePermissionApi)

    }


    // sort default roles
    // EDITROLEBACKEND
    val owner = roles.find(r => r.role_name == TeamAccountRole.OWNER).get
    val agencyAdmin = roles.find(r => r.role_name == TeamAccountRole.AGENCY_ADMIN).get
    val admin = roles.find(r => r.role_name == TeamAccountRole.ADMIN).get
    val member = roles.find(r => r.role_name == TeamAccountRole.MEMBER).get

    Seq(owner, agencyAdmin, admin, member)
  }

//  def migrateOlderAccounts = {
//
//    val teamIds: List[Long] = DB.readOnly { implicit session =>
//      sql"select t.id from teams t where not exists (select * from user_roles where team_id = t.id)"
//        .map(_.long("id"))
//        .list
//        .apply()
//    }
//
//    Logger.info(s"\n\nfound teams: ${teamIds.length}\n\n")
//
//    teamIds.foreach(tid => {
//
//      Logger.info(s"\n\nstart team: $tid\n\n")
//
//
//      val roles = insertDefaultRoles(teamId = tid)
//
//      Logger.info(s"\n\nstart team: $tid :: roles: $roles\n\n")
//
//
//      val ownerRoleId = roles.find(r => r.role_name == TeamAccountRole.OWNER).get.id
//      val memberRoleId = roles.find(r => r.role_name == TeamAccountRole.MEMBER).get.id
//
//      Logger.info(s"\n\nstart team: $tid :: roles: $roles\nownerRoleId: $ownerRoleId :: memberRoleId: $memberRoleId \n")
//
//      val updated = DB.autoCommit { implicit session =>
//        sql"""update teams_accounts ta
//           set
//            user_role_id = (case when ta.role = 'admin' then $ownerRoleId else $memberRoleId end)
//           where ta.team_id = $tid
//        """
//          .update
//          .apply()
//      }
//
//      Logger.info(s"\n\nend team: $tid :: updated: $updated\n\n")
//
//    })
//
//  }

  // EDITROLEBACKEND
  /*
  DO NOT ALLOW UPDATING:

  view_blacklist = ${data.view_blacklist.permitted},
  view_blacklist_ownership = ${data.view_blacklist.ownership.toString},

   */
  def updateRole(userRoleId: Long, orgId: Long, data: RolePermissionsV2): Try[Option[RolePermissionDataV2]] = Try {
    DB autoCommit { implicit session =>
      sql"""
          UPDATE user_roles SET

            manage_billing_v2 = ${data.manage_billing.ownership.toKey},
            view_user_management_v2 = ${data.view_user_management.ownership.toKey},
            edit_user_management_v2 = ${data.edit_user_management.ownership.toKey},

            view_team_config_v2 = ${data.view_team_config.ownership.toKey},
            edit_team_config_v2 = ${data.edit_team_config.ownership.toKey},


            view_prospects_v2 = ${data.view_prospects.ownership.toKey},

            edit_prospects_v2 = ${data.edit_prospects.ownership.toKey},

            delete_prospects_v2 = ${data.delete_prospects.ownership.toKey},

            view_campaigns_v2 = ${data.view_campaigns.ownership.toKey},

            edit_campaigns_v2 = ${data.edit_campaigns.ownership.toKey},

            delete_campaigns_v2 = ${data.delete_campaigns.ownership.toKey},

            change_campaign_status_v2 = ${data.change_campaign_status.ownership.toKey},

            view_reports_v2 = ${data.view_reports.ownership.toKey},

            edit_reports_v2 = ${data.edit_reports.ownership.toKey},

            download_reports_v2 = ${data.download_reports.ownership.toKey},

            send_manual_email_v2 = ${data.send_manual_email.ownership.toKey},

            view_templates_v2 = ${data.view_templates.ownership.toKey},

            edit_templates_v2 = ${data.edit_templates.ownership.toKey},

            delete_templates_v2 = ${data.delete_templates.ownership.toKey},

            edit_blacklist_v2 = ${data.edit_blacklist.ownership.toKey},

            view_workflows_v2 = ${data.view_workflows.ownership.toKey},

            edit_workflows_v2 = ${data.edit_workflows.ownership.toKey},

            view_webhooks_v2 = ${data.view_webhooks.ownership.toKey},

            edit_webhooks_v2 = ${data.edit_webhooks.ownership.toKey},

            view_channels_v2 = ${data.view_channels.ownership.toKey},
            edit_channels_v2 = ${data.edit_channels.ownership.toKey},
            delete_channels_v2 = ${data.delete_channels.ownership.toKey},

            view_tasks_v2 = ${data.view_tasks.ownership.toKey},
            edit_tasks_v2 = ${data.edit_tasks.ownership.toKey},
            delete_tasks_v2 = ${data.delete_tasks.ownership.toKey},

            edit_pipeline_details_v2 = ${data.edit_pipeline_details.ownership.toKey},
            delete_pipeline_details_v2 = ${data.delete_pipeline_details.ownership.toKey},

            view_opportunities_v2 = ${data.view_opportunities.ownership.toKey},
            edit_opportunities_v2 = ${data.edit_opportunities.ownership.toKey},
            delete_opportunities_v2 = ${data.delete_opportunities.ownership.toKey}

          WHERE id = $userRoleId
          AND org_id = $orgId
          RETURNING *

      """
        .map(RolePermissionsInDBV2.fromDb)
        .headOption
        .apply()
        .map(RolePermissionDataV2.toRolePermissionApi)

    }
  }
}

object RolePermissionDataDAOV2 {

  def defaultRoles(role: TeamAccountRole, simpler_perm_flag: Boolean) ={

   val data = Map(
    TeamAccountRole.OWNER.toString -> RolePermissionsInDBV2(
      id = -1, // this value is not used
      role_name = TeamAccountRole.OWNER,
      manage_billing_v2 = PermissionOwnershipV2.All,
      view_user_management_v2 = PermissionOwnershipV2.All,
      edit_user_management_v2 = PermissionOwnershipV2.All,

      view_team_config_v2 = PermissionOwnershipV2.All,
      edit_team_config_v2 = PermissionOwnershipV2.All,

      view_prospects_v2 = PermissionOwnershipV2.All,
      edit_prospects_v2 = PermissionOwnershipV2.All,
      delete_prospects_v2 = PermissionOwnershipV2.All,

      view_campaigns_v2 = PermissionOwnershipV2.All,
      edit_campaigns_v2 = PermissionOwnershipV2.All,
      delete_campaigns_v2 = PermissionOwnershipV2.All,
      change_campaign_status_v2 = PermissionOwnershipV2.All,

      view_reports_v2 = PermissionOwnershipV2.All,
      edit_reports_v2 = PermissionOwnershipV2.All,
      download_reports_v2 = PermissionOwnershipV2.All,

//      view_inbox_v2 = PermissionOwnershipV2.All,
//      edit_inbox_v2 = PermissionOwnershipV2.All,
      send_manual_email_v2 = PermissionOwnershipV2.All,

      view_templates_v2 = PermissionOwnershipV2.All,
      edit_templates_v2 = PermissionOwnershipV2.All,
      delete_templates_v2 = PermissionOwnershipV2.All,

      view_blacklist_v2 = PermissionOwnershipV2.All,
      edit_blacklist_v2 = PermissionOwnershipV2.All,

      view_workflows_v2 = PermissionOwnershipV2.All,
      edit_workflows_v2 = PermissionOwnershipV2.All,

      view_webhooks_v2 = PermissionOwnershipV2.All,
      edit_webhooks_v2 = PermissionOwnershipV2.All,

//      view_prospect_accounts_v2 = PermissionOwnershipV2.All,
//      edit_prospect_accounts_v2 = PermissionOwnershipV2.All,

//      view_email_accounts_v2 = PermissionOwnershipV2.All,
//      edit_email_accounts_v2 = PermissionOwnershipV2.All,
//      delete_email_accounts_v2 = PermissionOwnershipV2.All,
//
//      view_linkedin_accounts_v2 = PermissionOwnershipV2.All,
//      edit_linkedin_accounts_v2 = PermissionOwnershipV2.All,
//      delete_linkedin_accounts_v2 = PermissionOwnershipV2.All,
//
//      view_whatsapp_accounts_v2 = PermissionOwnershipV2.All,
//      edit_whatsapp_accounts_v2 = PermissionOwnershipV2.All,
//      delete_whatsapp_accounts_v2 = PermissionOwnershipV2.All,
//
//      view_sms_accounts_v2 = PermissionOwnershipV2.All,
//      edit_sms_accounts_v2 = PermissionOwnershipV2.All,
//      delete_sms_accounts_v2 = PermissionOwnershipV2.All,

      view_channels_v2 = PermissionOwnershipV2.All,
      edit_channels_v2 = PermissionOwnershipV2.All,
      delete_channels_v2 = PermissionOwnershipV2.All,

      view_tasks_v2 = PermissionOwnershipV2.All,
      edit_tasks_v2 = PermissionOwnershipV2.All,
      delete_tasks_v2 = PermissionOwnershipV2.All,

      edit_pipeline_details_v2 = PermissionOwnershipV2.All,
      delete_pipeline_details_v2 = PermissionOwnershipV2.All,

      view_opportunities_v2 = PermissionOwnershipV2.All,
      edit_opportunities_v2 = PermissionOwnershipV2.All,
      delete_opportunities_v2 = PermissionOwnershipV2.All
    ),

    // Agency Admin does not have billing access by default
    TeamAccountRole.AGENCY_ADMIN.toString -> RolePermissionsInDBV2(
      id = -1, // this value is not used
      role_name = TeamAccountRole.AGENCY_ADMIN,
      manage_billing_v2 = PermissionOwnershipV2.NoAccess,
      view_user_management_v2 = PermissionOwnershipV2.All,
      edit_user_management_v2 = PermissionOwnershipV2.All,

      view_team_config_v2 = PermissionOwnershipV2.All,
      edit_team_config_v2 = PermissionOwnershipV2.All,

      view_prospects_v2 = PermissionOwnershipV2.All,
      edit_prospects_v2 = PermissionOwnershipV2.All,
      delete_prospects_v2 = PermissionOwnershipV2.All,

      view_campaigns_v2 = PermissionOwnershipV2.All,
      edit_campaigns_v2 = PermissionOwnershipV2.All,
      delete_campaigns_v2 = PermissionOwnershipV2.All,
      change_campaign_status_v2 = PermissionOwnershipV2.All,

      view_reports_v2 = PermissionOwnershipV2.All,
      edit_reports_v2 = PermissionOwnershipV2.All,
      download_reports_v2 = PermissionOwnershipV2.All,

//      view_inbox_v2 = PermissionOwnershipV2.All,
//      edit_inbox_v2 = PermissionOwnershipV2.All,
      send_manual_email_v2 = PermissionOwnershipV2.All,

      view_templates_v2 = PermissionOwnershipV2.All,
      edit_templates_v2 = PermissionOwnershipV2.All,
      delete_templates_v2 = PermissionOwnershipV2.All,

      view_blacklist_v2 = PermissionOwnershipV2.All,
      edit_blacklist_v2 = PermissionOwnershipV2.All,

      view_workflows_v2 = PermissionOwnershipV2.All,
      edit_workflows_v2 = PermissionOwnershipV2.All,

      view_webhooks_v2 = PermissionOwnershipV2.All,
      edit_webhooks_v2 = PermissionOwnershipV2.All,

//      view_prospect_accounts_v2 = PermissionOwnershipV2.All,
//      edit_prospect_accounts_v2 = PermissionOwnershipV2.All,

//      view_email_accounts_v2 = PermissionOwnershipV2.All,
//      edit_email_accounts_v2 = PermissionOwnershipV2.All,
//      delete_email_accounts_v2 = PermissionOwnershipV2.All,
//
//      view_linkedin_accounts_v2 = PermissionOwnershipV2.All,
//      edit_linkedin_accounts_v2 = PermissionOwnershipV2.All,
//      delete_linkedin_accounts_v2 = PermissionOwnershipV2.All,
//
//      view_whatsapp_accounts_v2 = PermissionOwnershipV2.All,
//      edit_whatsapp_accounts_v2 = PermissionOwnershipV2.All,
//      delete_whatsapp_accounts_v2 = PermissionOwnershipV2.All,
//
//      view_sms_accounts_v2 = PermissionOwnershipV2.All,
//      edit_sms_accounts_v2 = PermissionOwnershipV2.All,
//      delete_sms_accounts_v2 = PermissionOwnershipV2.All,

      view_channels_v2 = PermissionOwnershipV2.All,
      edit_channels_v2 = PermissionOwnershipV2.All,
      delete_channels_v2 = PermissionOwnershipV2.All,
      
      view_tasks_v2 = PermissionOwnershipV2.All,
      edit_tasks_v2 = PermissionOwnershipV2.All,
      delete_tasks_v2 = PermissionOwnershipV2.All,

      edit_pipeline_details_v2 = PermissionOwnershipV2.All,
      delete_pipeline_details_v2 = PermissionOwnershipV2.All,

      view_opportunities_v2 = PermissionOwnershipV2.All,
      edit_opportunities_v2 = PermissionOwnershipV2.All,
      delete_opportunities_v2 = PermissionOwnershipV2.All
    ),

    // Admin does not have billing access by default
    TeamAccountRole.ADMIN.toString -> RolePermissionsInDBV2(
      id = -1, // this value is not used
      role_name = TeamAccountRole.ADMIN,
      manage_billing_v2 = PermissionOwnershipV2.NoAccess,
      view_user_management_v2 = PermissionOwnershipV2.All,
      edit_user_management_v2 = PermissionOwnershipV2.All,

      view_team_config_v2 = PermissionOwnershipV2.All,
      edit_team_config_v2 = PermissionOwnershipV2.All,

      view_prospects_v2 = PermissionOwnershipV2.All,
      edit_prospects_v2 = PermissionOwnershipV2.All,
      delete_prospects_v2 = PermissionOwnershipV2.All,

      view_campaigns_v2 = PermissionOwnershipV2.All,
      edit_campaigns_v2 = PermissionOwnershipV2.All,
      delete_campaigns_v2 = PermissionOwnershipV2.All,
      change_campaign_status_v2 = PermissionOwnershipV2.All,

      view_reports_v2 = PermissionOwnershipV2.All,
      edit_reports_v2 = PermissionOwnershipV2.All,
      download_reports_v2 = PermissionOwnershipV2.All,

//      view_inbox_v2 = PermissionOwnershipV2.All,
//      edit_inbox_v2 = PermissionOwnershipV2.All,
      send_manual_email_v2 = PermissionOwnershipV2.All,

      view_templates_v2 = PermissionOwnershipV2.All,
      edit_templates_v2 = PermissionOwnershipV2.All,
      delete_templates_v2 = PermissionOwnershipV2.All,

      view_blacklist_v2 = PermissionOwnershipV2.All,
      edit_blacklist_v2 = PermissionOwnershipV2.All,

      view_workflows_v2 = PermissionOwnershipV2.All,
      edit_workflows_v2 = PermissionOwnershipV2.All,

      view_webhooks_v2 = PermissionOwnershipV2.All,
      edit_webhooks_v2 = PermissionOwnershipV2.All,

//      view_prospect_accounts_v2 = PermissionOwnershipV2.All,
//      edit_prospect_accounts_v2 = PermissionOwnershipV2.All,

//      view_email_accounts_v2 = PermissionOwnershipV2.All,
//      edit_email_accounts_v2 = PermissionOwnershipV2.All,
//      delete_email_accounts_v2 = PermissionOwnershipV2.All,
//
//      view_linkedin_accounts_v2 = PermissionOwnershipV2.All,
//      edit_linkedin_accounts_v2 = PermissionOwnershipV2.All,
//      delete_linkedin_accounts_v2 = PermissionOwnershipV2.All,
//
//      view_whatsapp_accounts_v2 = PermissionOwnershipV2.All,
//      edit_whatsapp_accounts_v2 = PermissionOwnershipV2.All,
//      delete_whatsapp_accounts_v2 = PermissionOwnershipV2.All,
//
//      view_sms_accounts_v2 = PermissionOwnershipV2.All,
//      edit_sms_accounts_v2 = PermissionOwnershipV2.All,
//      delete_sms_accounts_v2 = PermissionOwnershipV2.All,

      view_channels_v2 = PermissionOwnershipV2.All,
      edit_channels_v2 = PermissionOwnershipV2.All,
      delete_channels_v2 = PermissionOwnershipV2.All,

      view_tasks_v2 = PermissionOwnershipV2.All,
      edit_tasks_v2 = PermissionOwnershipV2.All,
      delete_tasks_v2 = PermissionOwnershipV2.All,

      edit_pipeline_details_v2 = PermissionOwnershipV2.All,
      delete_pipeline_details_v2 = PermissionOwnershipV2.All,

      view_opportunities_v2 = PermissionOwnershipV2.All,
      edit_opportunities_v2 = PermissionOwnershipV2.All,
      delete_opportunities_v2 = PermissionOwnershipV2.All
    ),

    // Member can only view and perform operations on resources he / she is owner of.
    // They cannot edit team details or invite people.
    TeamAccountRole.MEMBER.toString -> RolePermissionsInDBV2(
      id = -1, // this value is not used
      role_name = TeamAccountRole.MEMBER,
      manage_billing_v2 = PermissionOwnershipV2.NoAccess,
      view_user_management_v2 = PermissionOwnershipV2.All,
      edit_user_management_v2 = PermissionOwnershipV2.NoAccess,

      view_team_config_v2 = PermissionOwnershipV2.All,
      edit_team_config_v2 = PermissionOwnershipV2.NoAccess,

      view_prospects_v2 = if(simpler_perm_flag) PermissionOwnershipV2.All else PermissionOwnershipV2.Owned,
      edit_prospects_v2 = PermissionOwnershipV2.Owned,
      delete_prospects_v2 = PermissionOwnershipV2.Owned,

      view_campaigns_v2 = if(simpler_perm_flag) PermissionOwnershipV2.All else PermissionOwnershipV2.Owned,
      edit_campaigns_v2 = PermissionOwnershipV2.Owned,
      delete_campaigns_v2 = PermissionOwnershipV2.Owned,
      change_campaign_status_v2 = PermissionOwnershipV2.Owned,

      view_reports_v2 =  PermissionOwnershipV2.All,
      edit_reports_v2 = PermissionOwnershipV2.All ,
      download_reports_v2 = PermissionOwnershipV2.All,

//      view_inbox_v2 = PermissionOwnershipV2.Owned,
//      edit_inbox_v2 = PermissionOwnershipV2.Owned,
      send_manual_email_v2 = PermissionOwnershipV2.Owned,

      view_templates_v2 = if(simpler_perm_flag) PermissionOwnershipV2.All else PermissionOwnershipV2.Owned,
      edit_templates_v2 = PermissionOwnershipV2.Owned,
      delete_templates_v2 = PermissionOwnershipV2.Owned,

      view_blacklist_v2 = PermissionOwnershipV2.All,
      edit_blacklist_v2 = PermissionOwnershipV2.All,

      view_workflows_v2 = if(simpler_perm_flag) PermissionOwnershipV2.All else PermissionOwnershipV2.Owned,
      edit_workflows_v2 = PermissionOwnershipV2.Owned,

      view_webhooks_v2 = if(simpler_perm_flag) PermissionOwnershipV2.All else PermissionOwnershipV2.NoAccess,
      edit_webhooks_v2 = PermissionOwnershipV2.NoAccess,

//      view_prospect_accounts_v2 = if(simpler_perm_flag) PermissionOwnershipV2.All else PermissionOwnershipV2.Owned,
//      edit_prospect_accounts_v2 = PermissionOwnershipV2.Owned,

//      view_email_accounts_v2 = if(simpler_perm_flag) PermissionOwnershipV2.All else PermissionOwnershipV2.Owned,
//      edit_email_accounts_v2 = PermissionOwnershipV2.Owned,
//      delete_email_accounts_v2 = PermissionOwnershipV2.Owned,
//
//      view_linkedin_accounts_v2 = if(simpler_perm_flag) PermissionOwnershipV2.All else PermissionOwnershipV2.Owned,
//      edit_linkedin_accounts_v2 = PermissionOwnershipV2.Owned,
//      delete_linkedin_accounts_v2 = PermissionOwnershipV2.Owned,
//
//      view_whatsapp_accounts_v2 = if(simpler_perm_flag) PermissionOwnershipV2.All else PermissionOwnershipV2.Owned,
//      edit_whatsapp_accounts_v2 = PermissionOwnershipV2.Owned,
//      delete_whatsapp_accounts_v2 = PermissionOwnershipV2.Owned,
//
//      view_sms_accounts_v2 = if(simpler_perm_flag) PermissionOwnershipV2.All else PermissionOwnershipV2.Owned,
//      edit_sms_accounts_v2 = PermissionOwnershipV2.Owned,
//      delete_sms_accounts_v2 = PermissionOwnershipV2.Owned,

      view_channels_v2 = if(simpler_perm_flag) PermissionOwnershipV2.All else PermissionOwnershipV2.Owned,
      edit_channels_v2 = PermissionOwnershipV2.Owned,
      delete_channels_v2 = PermissionOwnershipV2.Owned,

      view_tasks_v2 = if(simpler_perm_flag) PermissionOwnershipV2.All else PermissionOwnershipV2.Owned,
      edit_tasks_v2 = PermissionOwnershipV2.Owned,
      delete_tasks_v2 = PermissionOwnershipV2.Owned,

      edit_pipeline_details_v2 = if(simpler_perm_flag) PermissionOwnershipV2.All else PermissionOwnershipV2.Owned,
      delete_pipeline_details_v2 = PermissionOwnershipV2.NoAccess,

      view_opportunities_v2 = if(simpler_perm_flag) PermissionOwnershipV2.All else PermissionOwnershipV2.Owned,
      edit_opportunities_v2 = PermissionOwnershipV2.Owned,
      delete_opportunities_v2 = PermissionOwnershipV2.Owned

    )
  )

    data(role.toString)

  }

}