package api.accounts

import play.api.libs.json.{<PERSON><PERSON>, Reads, Writes}
import scalikejdbc.WrappedResultSet
import org.postgresql.util.PGobject


object TeamMetaDataDAO {
  given writes: Writes[TeamMetaData] = Json.writes[TeamMetaData]
  given reads: Reads[TeamMetaData] = Json.reads[TeamMetaData]

  def fromDb(rs: WrappedResultSet): TeamMetaData = {

    val _teamMetaData = Json.parse(rs.any("metadata").asInstanceOf[PGobject].getValue).validate[TeamMetaData].get
    val orgId = rs.long("org_id")

    val reply_sentiment_report = _teamMetaData.reply_sentiment_report.getOrElse(false)

    val teamMetaData = _teamMetaData.copy(
      reply_sentiment_report = Some(reply_sentiment_report)
    )

    teamMetaData
  }
}