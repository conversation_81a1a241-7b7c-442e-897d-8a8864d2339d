package api.accounts.sr_api_key_type.models

import scala.util.Random

sealed trait SRApiKeyType

object SRApiKeyType {

  case object SRUserLeve<PERSON><PERSON><PERSON> extends SRApiKeyType
  case object SRTeamUser<PERSON><PERSON><PERSON><PERSON><PERSON> extends SRApiKeyType
  case object SRProspectD<PERSON><PERSON><PERSON><PERSON> extends SRApiKeyType
  case object SR<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends SRApiKeyType
  case object SR<PERSON><PERSON><PERSON><PERSON><PERSON> extends SRApiKeyType

  val userLevelKeyPrefix: String = "uk_"
  val teamUserLevelKeyPrefix: String = "tuk_"
  val prospectDaddyKeyPrefix: String = "sr_pd_"
  val srWarmupBoxKeyPrefix: String = "sr_wrmpbx_"
  val zapierKeyPrefix: String = "zp_"


  def getKeyTypeFromQueryParam(key_type: String): SRApiKeyType = {

    key_type match {
      case "user" => SRApiKeyType.SRUserLevelKey
      case "team_user" => SRApiKeyType.SRTeamUserLevelKey
      case "prospectdaddy" => SRApiKeyType.SRProspectDaddyKey
      case "warmupbox" => SRApiKeyType.SRWarmupBoxKey
      case "zapier" => SRApiKeyType.SRZapierKey
    }

  }


  def getKeyTypeFromPrefix(key: String): SRApiKeyType = {

    if (key.startsWith(userLevelKeyPrefix)) SRUserLevelKey
    else if (key.startsWith(teamUserLevelKeyPrefix)) SRTeamUserLevelKey
    else if (key.startsWith(prospectDaddyKeyPrefix)) SRProspectDaddyKey
    else if (key.startsWith(srWarmupBoxKeyPrefix)) SRWarmupBoxKey
    else if(key.startsWith(zapierKeyPrefix)) SRZapierKey
    else {
      // older keys didnt have any prefix
      SRTeamUserLevelKey
    }

  }

  def genApiKey(keyType: SRApiKeyType): String = {
    val keySuffix = Random.alphanumeric.take(32).mkString("")

    keyType match {

      case SRUserLevelKey => s"${userLevelKeyPrefix}_$keySuffix"

      case SRTeamUserLevelKey =>
        // fixme - note, with date, logger.fatal for team api key generation
        s"${teamUserLevelKeyPrefix}_$keySuffix"

      case SRProspectDaddyKey => s"${prospectDaddyKeyPrefix}_$keySuffix"

      case SRWarmupBoxKey => s"${srWarmupBoxKeyPrefix}_$keySuffix"

      case SRZapierKey => s"${zapierKeyPrefix}_$keySuffix"
    }

  }


}
