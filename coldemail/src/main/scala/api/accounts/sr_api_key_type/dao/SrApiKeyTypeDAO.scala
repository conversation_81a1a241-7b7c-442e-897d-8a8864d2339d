package api.accounts.sr_api_key_type.dao

import api.CONSTANTS
import api.accounts.models.AccountId
import api.accounts.{Account, AccountDAO, PermissionMethods, TeamAccountRole, TeamId}
import api.accounts.sr_api_key_type.models.SRApiKeyType
import utils.SRLogger

import scala.util.{Failure, Success, Try}

class SrApiKeyTypeDAO(
  accountDAO: AccountDAO
) {

  /*
  23 Dec 21: moved from SRApiKeyType object to remove cyclic dependency
   */
  def getApiKey(
    keyType: SRApiKeyType,
    account: Account,
    teamId: TeamId
  )(
    implicit Logger: SRLogger
  ): Try[Option[String]] = {

    val role = PermissionMethods.getUserRole(
      loggedinAccount = account,
      teamId = teamId.id,
      ignoreFatalLogging = true,
      Logger = Logger
    )

    role match {
      case None =>
        Failure(new Exception("Role not found for team member"))

      case Some(roleAndPermission) =>

        if (roleAndPermission.role_name == TeamAccountRole.MEMBER) {

          Failure(new Exception("Member role not allowed to generate API key"))

        } else {

          keyType match {

            case SRApiKeyType.SRTeamUserLevelKey | SRApiKeyType.SRZapierKey =>

              account.teams.find(t => t.team_id == teamId.id) match {
                case None => Failure(new Exception("You are not authorised to do this. Please contact support if you have any questions."))
                case Some(team) =>

                  val teamAccount = team.access_members.find(m => m.user_id == account.internal_id).get

                  val key = if(keyType == SRApiKeyType.SRTeamUserLevelKey) teamAccount.api_key
                  else teamAccount.zapier_key

                  Success(
                    key
                  )
              }


            case SRApiKeyType.SRUserLevelKey =>

              // FIXME VALUECLASS
              accountDAO.getUserLevelApiKey(accountId = AccountId(id = account.internal_id)) match {
                case Failure(e) =>

                  Logger.fatal("getUserLevelApiKey", err = e)

                  Failure(new Exception(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER))

                case Success(None) =>

                  Success(None)

                case Success(Some(key)) =>

                  Success(
                    Some(key)
                  )
              }

            case SRApiKeyType.SRProspectDaddyKey =>

              // FIXME VALUECLASS
              accountDAO.getProspectDaddyApiKey(accountId = AccountId(id = account.internal_id)) match {
                case Failure(e) =>
                  Logger.fatal("getProspectDaddyApiKey", err = e)

                  Failure(new Exception(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER))

                case Success(None) =>

                  Success(None)

                case Success(Some(key)) =>

                  Success(Some(key))

              }


            case SRApiKeyType.SRWarmupBoxKey =>

              // FIXME VALUECLASS
              val accountId = AccountId(id = account.internal_id)

              val enable_internal_email_accounts_api_for_warmuphero: Boolean =
                account.org.org_metadata.enable_internal_email_accounts_api_for_warmuphero.getOrElse(false)

              val whApiKeyRes: Try[Option[Either[String, String]]] = if (
                !enable_internal_email_accounts_api_for_warmuphero
              ) {

                accountDAO.getSRWarmupBoxKey(accountId = accountId).map(_.map(Right(_)))

              } else {

                roleAndPermission.role_name match {

                  case TeamAccountRole.ADMIN |
                       TeamAccountRole.MEMBER =>

                    val errMsg = s"Please contact your SmartReach Owner or Agency Admin to check or update the WarmupHero API Key. Your role (${roleAndPermission.role_name}) does not have access to do this."

                    Logger.info(
                      msg = s"$errMsg. accountId: $accountId :: role: ${roleAndPermission.role_name} :: orgId: ${account.org.id}"
                    )

                    Success(Some(Left(errMsg)))

                  case TeamAccountRole.OWNER |
                       TeamAccountRole.AGENCY_ADMIN =>

                    accountDAO.getSRWarmupBoxKey(accountId = accountId).map(_.map(Right(_)))

                }

              }

              whApiKeyRes match {

                case Failure(e) =>

                  Logger.fatal("getSRWarmupBoxKey", err = e)

                  Failure(new Exception(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER))

                case Success(None) =>

                  Success(None)

                case Success(Some(Left(errMsg))) =>

                  Failure(new Exception(errMsg))

                case Success(Some(Right(key))) =>

                  Success(Some(key))
              }

          }
        }
    }
  }

}
