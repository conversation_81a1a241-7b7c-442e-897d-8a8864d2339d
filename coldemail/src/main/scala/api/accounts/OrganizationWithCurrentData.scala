package api.accounts

import api.AppConfig
import api.billing.PaymentGateway
import io.sr.billing_common.models.{PlanID, SrPlanName}
import org.joda.time.DateTime
import org.postgresql.util.PGobject
import play.api.libs.json.{Json, OFormat}
import play.api.libs.json.JodaReads._
import play.api.libs.json.JodaWrites._
import scalikejdbc.jodatime.JodaWrappedResultSet._
import scalikejdbc.WrappedResultSet
import utils.{Helpers, SRLogger}
import utils.featureflags.services.{OrgMetadataService, SrFeatureFlags}

case class OrganizationWithCurrentData(

  id: Long,
  name: String,

  owner_account_id: Long,

  counts: OrgCountData,
  settings: OrgSettings,
  plan: OrgPlan,

  is_agency: Boolean,
  trial_ends_at: DateTime,
  // next_billing_date: Option[DateTime],


  //  active: Boolean,
  //  deactivation_message: Option[String],
  //  deactivated_at: Option[DateTime],

  error: Option[String],
  error_code: Option[String],
  //  error_reported_at: Option[DateTime],
  paused_till: Option[DateTime],

  errors: Seq[AccountErrorObjectApi],
  warnings: Seq[AccountWarningObjectApi],

  via_referral: Boolean,

  org_metadata: OrgMetadata

)

object OrganizationWithCurrentData {


  //  # If you use Timestamp
  //  implicit val tsreads: Reads[Timestamp] = Reads.of[Long] map (new Timestamp(_))
  //  implicit val tswrites: Writes[Timestamp] = Writes { (ts: Timestamp) => JsString(ts.toString) }

  // REF: https://stackoverflow.com/a/********/1463434

  //  # If you use DateTime
  //  implicit val tsreads: Reads[DateTime] = Reads.of[String] map (new DateTime(_))
  //  implicit val tswrites: Writes[DateTime] = Writes { (dt: DateTime) =>
  //    JsString(dt.toString)
  //  }

  given format: OFormat[OrganizationWithCurrentData] = Json.format[OrganizationWithCurrentData]


  def fromDb(
    rs: WrappedResultSet,
  )(
    implicit Logger: SRLogger,
  ): OrganizationWithCurrentData = {

    val planName = rs.string("org_obj_plan_name")
    val planID = PlanID._getPlanId(planname = planName)
    val planType = PlanID._getPlanType(planID = planID)
    val totalSendingEmailAccounts = rs.int("org_obj_counts_total_sending_email_accounts")
    val orgId = rs.long("org_obj_id")
    val viaAffiliate = rs.booleanOpt("org_obj_via_affiliate").getOrElse(false)
    val isAgency = rs.boolean("org_obj_is_agency")
    val newpProspectsPausedTill = rs.jodaDateTimeOpt("org_obj_new_prospects_paused_till")

    // FIXME: remove this field altogether
    val is_v2_business_plan: Boolean = PlanID.isV2BusinessPlan(
      planName = SrPlanName(name = planName),
    )

    val orgPlan = OrgPlan(
      new_prospects_paused_till = newpProspectsPausedTill,
      current_cycle_started_at = rs.jodaDateTime("org_obj_current_cycle_started_at"),
      fs_account_id = rs.stringOpt("org_obj_fs_account_id"),
      
      stripe_customer_id = rs.stringOpt("org_obj_stripe_customer_id"),
      payment_gateway = rs.stringOpt("org_obj_payment_gateway")
        .map(pgString => PaymentGateway.withName(pgString)),

      next_billing_date = rs.jodaDateTimeOpt("org_obj_next_billing_date"),

      payment_due_invoice_link = rs.stringOpt("org_obj_payment_due_invoice_link"),
      payment_due_campaign_pause_at = rs.jodaDateTimeOpt("org_obj_payment_due_campaign_pause_at"),

      plan_type = planType,
      plan_name = planName,
      plan_id = planID,

      // FIXME: remove this field altogether
      is_v2_business_plan = is_v2_business_plan
    )

    val isOrgOnboardingDone = rs.boolean("org_obj_is_onboarding_done")
    val _orgMetadata = Json.parse(rs.any("org_obj_metadata").asInstanceOf[PGobject].getValue).validate[OrgMetadata].get

    val maxLinkedinAutomationSeats = rs.int("plan_li_automation_seats_max")

    val hasWhApiKey = rs.stringOpt("warmupbox_key_enc").exists(_.trim.nonEmpty)

    val orgMetadata = OrgMetadataService.enrichOrgMetadataFromDb(
      orgId = orgId,
      planID = planID,
      planType = planType,
      is_v2_business_plan = is_v2_business_plan,
      isAgency = isAgency,
      maxLinkedinAutomationSeats = maxLinkedinAutomationSeats,
      isOrgOnboardingDone = isOrgOnboardingDone,
      _orgMetadata = _orgMetadata,
      planName = SrPlanName(name = planName),
      hasWhApiKey = hasWhApiKey,
    )


    val warningMsg = rs.stringOpt("org_obj_warning_msg")
    val warningCode = rs.stringOpt("org_obj_warning_code").map(c => AccountWarningCodeType.fromString(c).get)
    var warningSequence: Seq[AccountWarningObjectApi] = Seq()

    val prospectLimitWarning: Option[AccountWarningObjectApi] = if (warningMsg.isDefined && warningCode.isDefined) {

      val showUpgradeNowPrompt: Boolean = warningCode.get match {
          case AccountWarningCodeType.ProspectLimit80Percent => true
          case AccountWarningCodeType.ProspectLimitExceeded => true
          case AccountWarningCodeType.LowCallingCredit => false
          case AccountWarningCodeType.CallingFeatureSuspended => false
          case AccountWarningCodeType.ProspectSavedWarning => true
        }




      val res = AccountWarningObjectApi(
        warning_msg = warningMsg.get,
        warning_code = warningCode.get,
        warning_at = rs.jodaDateTime("org_obj_warning_at"),
        upgrade_now_prompt = showUpgradeNowPrompt,
        new_prospects_paused_till = newpProspectsPausedTill
      )

      warningSequence = warningSequence ++ Seq(res)

      Some(res)

    } else None

    val callWarningMsg = rs.stringOpt("occ_warning_message")
    val callWarningCode = rs.stringOpt("occ_warning_code").map(c => AccountWarningCodeType.fromString(c).get)

    val orgCallCreditWarning: Option[AccountWarningObjectApi] = if(callWarningMsg.isDefined && callWarningCode.isDefined) {

      val showAddCreditButton: Boolean = (
        callWarningCode.get == AccountWarningCodeType.LowCallingCredit
          || callWarningCode.get == AccountWarningCodeType.CallingFeatureSuspended
        )

      val res2 = AccountWarningObjectApi(
        warning_msg = callWarningMsg.get,
        warning_code = callWarningCode.get,
        warning_at = rs.jodaDateTime("occ_warning_at"),
        upgrade_now_prompt = false,
        new_prospects_paused_till = None,
        add_call_credit_button = showAddCreditButton
      )

      warningSequence = warningSequence ++ Seq(res2)

      Some(res2)
    } else None


    val pausedTillRaw = rs.jodaDateTimeOpt("org_obj_paused_till")
    val orgIsPaused = pausedTillRaw.isDefined && pausedTillRaw.get.isAfterNow
    val pausedTill = if (orgIsPaused) pausedTillRaw else None


    val errorMsg = if (orgIsPaused) rs.stringOpt("org_obj_error") else None
    val errorCode = if (orgIsPaused) rs.stringOpt("org_obj_error_code") else None
    val errorReportedAt = if (orgIsPaused) rs.jodaDateTimeOpt("org_obj_error_reported_at") else None
    val accountErrors: Seq[AccountErrorObjectApi] = if (errorMsg.isDefined && errorCode.isDefined && orgIsPaused) {

      val showUpgradeNowPrompt: Boolean = false

      Seq(AccountErrorObjectApi(
        error_msg = errorMsg.get,
        error_code = errorCode.get,
        error_at = errorReportedAt,
        upgrade_now_prompt = showUpgradeNowPrompt
      ))

    } else Seq()

    val allow2FA = Helpers.is2FAAllowedForOrg(planID = planID, orgId = orgId)
    val show2FASetting = Helpers.show2FASettingInFrontend(planID = planID, orgId = orgId)
    
    val totalProspectsSaved = rs.intOpt("current_prospects_saved").getOrElse(0)

    // Number of active teams in the organization
    val current_active_client_teams = rs.int("current_active_client_teams")

    val current_client_teams: Int = if (
      !isAgency ||
        orgMetadata.ignore_inactive_teams_for_v4_plan_limit_check.getOrElse(false)
    ) {

      // If `ignore_inactive_teams_for_v4_plan_limit_check`
      // flag is enabled only consider active teams for plan limits.

      /**
        * 5 Mar 2025
        *
        * When a user enables agency plans during the trial and creates a few teams,
        * but later switches back to a non-agency plan, they are required to disable
        * the extra teams before switching back to the non-agency plan.
        *
        * However, when attempting to upgrade to a paid plan, they get an error message
        * saying, "Please delete the extra teams or select the given number of seats"
        * because we currently only exclude deleted teams from the plan limit check.
        *
        * Going forward, for non-agency plans,
        * inactive teams will also be excluded from the plan limit check.
        */

      // Non-agencies and agencies for whom the `ignore_inactive_teams_for_v4_plan_limit_check`
      // flag is active will come in this block.

      // But for non-agencies the active team count should always be 1.

      if (
        !isAgency &&
          current_active_client_teams != 1
      ) {
        Logger.shouldNeverHappen(
          msg = s"[OrganizationWithCurrentData] current_active_client_teams != 1 assertion failed. orgId: $orgId :: current_active_client_teams: $current_active_client_teams",
          err = None,
        )
      }

      current_active_client_teams

    } else {

      // If `ignore_inactive_teams_for_v4_plan_limit_check`
      // flag is no enabled then consider both active and inactive teams for plan limits.

      // Most agencies for whom the `ignore_inactive_teams_for_v4_plan_limit_check`
      // flag is not active will come in this block.

      // Number of active and inactive teams in the organization
      rs.int("current_active_and_inactive_client_teams")

    }

    OrganizationWithCurrentData(
      id = rs.long("org_obj_id"),
      name = rs.string("org_obj_name"),

      owner_account_id = rs.long("org_obj_owner_account_id"),

      plan = orgPlan,

      is_agency = isAgency,
      trial_ends_at = rs.jodaDateTime("org_obj_trial_ends_at"),


      // next_billing_date = rs.jodaDateTimeOpt("org_obj_next_billing_date"),

      counts = OrgCountData(

        base_licence_count = rs.int("org_obj_base_licence_count"),
        additional_licence_count = rs.int("org_obj_additional_licence_count"),

        current_sending_email_accounts = rs.int("org_obj_counts_current_sending_email_accounts"),

        total_sending_email_accounts = totalSendingEmailAccounts,


        additional_spam_tests = rs.int("org_obj_counts_additional_spam_tests"),

        max_prospect_limit_org = PlanID._maxAllowedProspectsPerMonthForOrg(
          planId = planID,
          totalSendingEmailAccounts = totalSendingEmailAccounts,
          maxProspectsContactedFromDB = rs.int("org_obj_counts_max_prospect_sent_count_org")
        ),
        current_prospect_sent_count_org = rs.int("org_obj_counts_current_prospect_sent_count_org"),

        max_li_manual_seats = rs.int("plan_li_manual_seats_max"),
        current_li_manual_seats = rs.int("plan_li_manual_seats_current"),
        
        max_li_automation_seats = maxLinkedinAutomationSeats,
        current_li_automation_seats = rs.int("plan_li_automation_seats_current"),

        max_purchased_domains = rs.int("plan_purchased_domains_max"),
        current_purchased_domains = rs.int("plan_purchased_domains_current"),

        max_purchased_email_accounts = rs.int("plan_purchased_email_accounts_max"),
        current_purchased_email_accounts = rs.int("plan_purchased_email_accounts_current"),

        max_purchased_zapmail_domains = rs.int("plan_purchased_zapmail_domains_max"),
        current_purchased_zapmail_domains = rs.int("plan_purchased_zapmail_domains_current"),

        max_purchased_zapmail_email_accounts = rs.int("plan_purchased_zapmail_email_accounts_max"),
        current_purchased_zapmail_email_accounts = rs.int("plan_purchased_zapmail_email_accounts_current"),

        max_calling_accounts = rs.int("plan_calling_seats_max"),
        current_calling_accounts = rs.int("current_calling_accounts"),

        max_crm_integrations = rs.int("plan_crm_integrations_max"),
        current_crm_integrations = rs.int("current_crm_integrations"),
        
        max_client_teams = rs.int("plan_client_teams_max"),
        current_client_teams = current_client_teams,
        current_active_client_teams = current_active_client_teams,

        has_active_call_campaigns = rs.boolean("has_active_call_campaigns"),
        
        max_prospects_saved = rs.int("plan_prospects_saved_max"),
        current_prospects_saved = totalProspectsSaved,
        
        current_active_team_inboxes = rs.int("current_active_team_inboxes"),

        max_phone_number_buying_limit_org = SrFeatureFlags.maxPhoneNumberToBuyLimitForOrg(
          calling_flag = orgMetadata.enable_native_calling,
          total_email_limit = totalSendingEmailAccounts)
      ),

      settings = OrgSettings(
        enable_ab_testing = SrFeatureFlags.isAbtestingAllowed(planID = planID, forceEnableABTestingForOrg = rs.boolean("org_enable_ab_testing"), orgId = orgId),
        bulk_sender = rs.boolean("org_obj_bulk_sender"),
        disable_force_send = rs.boolean("org_obj_disable_force_send"),
        allow_2fa = allow2FA,
        show_2fa_setting = show2FASetting,

        // ignore enforce_2fa if 2fa is disabled on user's plan (lets say when user downgrades from Ultimate plan)
        enforce_2fa = allow2FA && rs.boolean("org_enforce_2fa"),

        allow_native_crm_integration = PlanID.isCRMIntegrationEnabled(
          planID = planID
        ),
          agency_option_show = SrFeatureFlags.doShowEnableAgencyOption(planId = planID),
          agency_option_allow_changing = SrFeatureFlags.isAllowAgencyChange(planId = planID)
      ),

      error = errorMsg,
      error_code = errorCode,
      //      error_reported_at = errorReportedAt,
      paused_till = pausedTill,


      errors = accountErrors,
      warnings = warningSequence,

      via_referral = viaAffiliate,

      org_metadata = orgMetadata
      // created_at = rs.jodaDateTime("org_obj_created_at")
    )
  }


}