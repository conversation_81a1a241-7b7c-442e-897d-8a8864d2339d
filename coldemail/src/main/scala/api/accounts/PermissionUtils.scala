package api.accounts

import api.accounts.models.OrgId
import api.accounts.models.AccountId
import api.accounts.service.LogoutError
import api.accounts.sr_api_key_type.models.SRApiKeyType
import api.sr_audit_logs.services.RequestLogService
import api.sr_common_auth.AuthService
import api.team.TeamUuid
import api.team.service.TeamService
import api.{ApiResponseModuleForPermissionedApis, ApiVersion, AppConfig, CONSTANTS, SRAPIResponse}
import play.api.libs.json.Json
import play.api.mvc._
import play.api.routing.Router
import utils.{Helpers, ParseUtils, SRLogger}
import utils.enum_sr_utils.EnumUtils
import utils.logging.SRAccessType
import app_services.sr_rate_limiter.SrRateLimiter
import io.sr.billing_common.models.PlanType
import play.api.libs.ws.WSClient
import utils.security.EncryptionHelpers
import utils.uuid.{SrId, SrIdentifier, SrUuid, SrUuidUtils}

import scala.util.{Failure, Success, Try}

// REF: https://github.com/bizzabo/play-json-extensions/issues/73#issuecomment-*********


import scala.concurrent.{ExecutionContext, Future}

case class PermissionRequest[A](
  auditRequestLogId: String,
  loggedinAccount: Account,
  isSupportAppModeRequest: Boolean,
  actingTeamAccount: Option[TeamMember], // action is being taken on behalf of this team-account
  permittedAccountIds: Seq[Long], // permitted accounts for this specific route
  isApiCall: Boolean,
  Logger: SRLogger,
  Response: ApiResponseModuleForPermissionedApis,
  request: Request[A]

) extends WrappedRequest[A](request)

object PermissionMethods {
  
  final def getAllMembersInTeam(
    loggedinAccount: Account,
    teamId: Long

  ): Seq[TeamMemberLite] = {

    val allMembersInTeam = loggedinAccount.teams
      .find(t => t.team_id == teamId)
      .toSeq
      .flatMap(_.all_members) // ALL MEMBERS IN TEAM

    allMembersInTeam

  }

  final def getAllMemberAccountIdsInTeam(
    loggedinAccount: Account,
    teamId: Long

  ): Set[Long] = {

    val allMemberAccountIdsInTeam = getAllMembersInTeam(
      loggedinAccount = loggedinAccount,
      teamId = teamId
    )
      .map(_.user_id)
      .toSet

    allMemberAccountIdsInTeam

  }


  final def getPermittedAccountIds(

    loggedinAccount: Account,
    viewTid: Long,
    viewAid: Long,
    permissionOwnership: PermissionOwnershipV2,
    version: String,
    Logger: SRLogger

  ): Seq[Long] = {


    if (viewAid != 0 && loggedinAccount.internal_id != viewAid) {
      Logger.fatal(s"olduicheck :: getPermittedAccountIds :: loggedinAccount.id != viewAid :: ${loggedinAccount.internal_id} != ${viewAid}")
    }

    val allMembersInTeam = getAllMembersInTeam(
      loggedinAccount = loggedinAccount,
      teamId = viewTid
    )


    val permittedAccountIds = if (permissionOwnership == PermissionOwnershipV2.All &&
      // 30 May 2021: remove this check block
          // for newer ui with v2 apis
          ((version == ApiVersion.V2.toString || version == ApiVersion.V3.toString) &&
            (
              viewAid == 0 || // happening in getPermittedAccountIdsForAccountAndPermission which is called by search api
                viewAid == loggedinAccount.internal_id
              )
            )
    ) {

      allMembersInTeam
        .map(_.user_id)

    } else if (permissionOwnership == PermissionOwnershipV2.All && viewAid != 0 && viewTid != 0) {

      //adding specific orgIds, to know who else has this
      if(!(loggedinAccount.internal_id == viewAid &&  AppConfig.olduicheck_org_ids_to_eliminate_the_log.contains(loggedinAccount.org.id))) {
        Logger.fatal(s"olduicheck :: getPermittedAccountIds :: SWITCH VIEW ACCOUNT should not happen :: ${loggedinAccount.internal_id} != ${viewAid} :: orgid: ${loggedinAccount.org.id}")
      }

      allMembersInTeam
        .find(mem => mem.user_id == viewAid) // SWITCH VIEW ACCOUNT
        .toSeq
        .map(_.user_id)

    } else if (

      // EDIT_TEAM_CONFIG (delete prospect categories) like permissions
      permissionOwnership == PermissionOwnershipV2.NoAccess
    ) {

      allMembersInTeam
        .map(_.user_id)

    } else {

      // if (PermissionOwnership.OWNED_RECORDS || PermissionOwnership.NOT_APPLICABLE)

      allMembersInTeam
        .find(mem => mem.user_id == loggedinAccount.internal_id) // ONLY THEIR OWN ACCOUNT
        .toSeq
        .map(_.user_id)
    }


    if (permittedAccountIds.isEmpty) {

      Logger.fatal(s"getPermittedAccountIds empty: viewTid: $viewTid :: viewAid: $viewAid :: permissionOwnership: $permissionOwnership :: loggedinAccount: ${loggedinAccount.internal_id} :: ${loggedinAccount.email} :: ${loggedinAccount.org.name}")

    }


    permittedAccountIds


  }

  final def getUserRole(loggedinAccount: Account, teamId: Long, ignoreFatalLogging: Boolean,
    Logger: SRLogger
  ): Option[RolePermissionDataV2] = {

    /*if (loggedinAccount.org_role == OrganizationRole.ADMIN &&

      // either agency view (teamId = 0) OR team must belong to org
      (teamId == 0 || loggedinAccount.teams.exists(t => t.team_id == teamId))

    ) {
       Some(RolePermissionData.ORG_ADMIN)



      // org admin is the owner in all teams, take the first teams role
      loggedinAccount.teams.head.role


    }*/

    if (teamId == 0) {

      // if org admin / agency dashboard: org admin is the owner in all teams, take the first teams role
      // if any member, in frontend we take the first team by default
      // either way, first team role will be taken
      loggedinAccount.teams.head.role

    } else {

      val t = loggedinAccount.teams
        .find(t => t.team_id == teamId)

      if (t.isEmpty) {

        if (!ignoreFatalLogging) { // ignore in case of master dashboard
          Logger.fatal(s" getUserRole t.isEmpty: $teamId  ::::  $loggedinAccount")
        }

        None

      } else if (t.get.role.isEmpty) {

        Logger.fatal(s" getUserRole t.get.role.isEmpty: $teamId  ::::  $loggedinAccount")

        None

      } else {

        t.get.role
      }

    }

  }

  // scala function currying: permission
  final def getPermittedAccountIdsForAccountAndPermission(
    loggedinAccount: Account,

    // action is being taken on behalf of this team-account
    actingTeamId: Long,
    actingAccountId: Long,

    version: String,

    Logger: SRLogger

  )(
    permission: PermType
  ) = {

    if (loggedinAccount.internal_id != actingAccountId) {
      Logger.fatal(s"olduicheck :: getPermittedAccountIdsForAccountAndPermission :: loggedinAccount.id != actingAccountId :: ${loggedinAccount.internal_id} != ${actingAccountId}")

    }

    val userRole = getUserRole(loggedinAccount = loggedinAccount, teamId = actingTeamId, ignoreFatalLogging = false,
      Logger = Logger
    )

    if (userRole.isEmpty) {

      Logger.error(s"FATAL: getPermittedAccountIdsForAccountAndPermission empty userRole: loggedinAccount: $loggedinAccount :: actingTeamAccount: ($actingTeamId, $actingAccountId) :: permission: $permission")

      Seq(actingAccountId)

    } else {

      val rolePerm = RolePermissionsV2.getRolePermission(permissionRequired = permission, userRole = userRole.get)


      val p = getPermittedAccountIds(
        loggedinAccount = loggedinAccount,
        viewTid = actingTeamId,
        viewAid = 0, // 23-Mar-2023: it was always getting passed as 0 earlier
        permissionOwnership = rolePerm.ownership,
        version = version,
        Logger = Logger
      )

      p

    }

  }
}

/*
object PermissionOwnership extends Enumeration {
  type PermissionOwnership = Value

  val ALL = Value("all")
  // val OWNED_AND_REPORTS_RECORDS = Value("owned_and_reports_records") // no difference with all records
  val OWNED = Value("owned")
  val NA = Value("na") // NOT APPLICABLE

  given format = EnumUtils.enumFormat(PermissionOwnership)

}

case class RolePermConfig(
  permitted: Boolean,
  ownership: PermissionOwnership.Value = PermissionOwnership.NA,
  version: String = "v1"
)

object RolePermConfig {
  given format = Json.format[RolePermConfig]
}
*/


///////////////

class PermissionUtils(
  authUtils: AuthUtils,
  ec: ExecutionContext,
  srRateLimiter: SrRateLimiter,
  srUuidUtils: SrUuidUtils,
  requestLogService: RequestLogService,
  authService: AuthService,
  teamService: TeamService,
  implicit val ws:WSClient
) {


  final def getCurrentTeamMemberByApiKey(keyType: SRApiKeyType, apiKey: String, loggedinAccount: Account)(
    logger: SRLogger
  ): Option[TeamMember] = {

    val accessMembers = loggedinAccount.teams
      .flatMap(t => t.access_members)

    val teamMember: Option[TeamMember] = keyType match {

      case SRApiKeyType.SRTeamUserLevelKey =>
        accessMembers.find(m => m.api_key.isDefined && m.api_key.get == apiKey)

      case SRApiKeyType.SRZapierKey =>
        accessMembers.find(m => m.zapier_key.isDefined && m.zapier_key.get == apiKey)

      case SRApiKeyType.SRUserLevelKey | SRApiKeyType.SRProspectDaddyKey | SRApiKeyType.SRWarmupBoxKey =>
        None

    }

    if (teamMember.isDefined && teamMember.get.user_id != loggedinAccount.internal_id) {

      logger.warn(s"[olduicheck api] getCurrentTeamMemberByApiKey (teamMember.get.user_id != loggedinAccount.id): teamMemberUserId: ${teamMember.get.user_id} and loggedinAccountId: ${loggedinAccount.internal_id}")

    }

    if (teamMember.isEmpty) {

      logger.warn(s"[olduicheck api] getCurrentTeamMemberByApiKey (teamMember.isEmpty) loggedinAccountId: ${loggedinAccount.internal_id}")

    }

    teamMember

  }

  private final def _checkViewTidViewAidAccess(loggedInAccount: Account, tid: Long,
                                               Logger: SRLogger
                                              ): Option[TeamMember] = {


    val ta = loggedInAccount.teams
      .find(t => t.team_id == tid)

    val member = ta.toSeq.flatMap(_.access_members)
      .find(mem => mem.team_id == tid && mem.user_id == loggedInAccount.internal_id)

    member

  }

  private def _getTeamMember(
                              keyType: SRApiKeyType,
                              apiKey: String,
                              orgId: OrgId,
                              loggedinAccount: Account,
                              apiTeamId: Option[SrIdentifier]
                            )(implicit permLogger: SRLogger): Try[Option[TeamMember]] = {
    keyType match {

      case SRApiKeyType.SRTeamUserLevelKey | SRApiKeyType.SRZapierKey =>

        Success(
          getCurrentTeamMemberByApiKey(keyType = keyType, apiKey = apiKey, loggedinAccount = loggedinAccount)(
            logger = permLogger
          )
        )

      case SRApiKeyType.SRUserLevelKey |
           SRApiKeyType.SRProspectDaddyKey =>

        val teamIdTry: Try[TeamId] = apiTeamId match {

          case None => Failure(new Exception("team_id not present in query param for User level key"))

          case Some(srIdentifier: SrIdentifier) => teamService.getTeamIdFromSrIdentifier(
            srIdentifier = srIdentifier,
            orgId = orgId
          )
        }

        teamIdTry match {

          case Failure(err) => Failure(err)

          case Success(teamId) =>
            Success(
              loggedinAccount.teams
                .find(t => t.team_id == teamId.id)
                .flatMap(t => t.access_members
                  .find(m => m.user_id == loggedinAccount.internal_id)
                )
            )
        }


      case SRApiKeyType.SRWarmupBoxKey =>

        permLogger.shouldNeverHappen("SRWarmupBoxKey does not have access to any APIs")
        Success(None)

    }
  }

  /*
   * Date: 16-Apr-2024
   * Here we are checking if teamId(Long) is passed for v2 version,
   * and teamUuid is passed for v3 version of API in case of User level key.
   *
   * For other API keys - Team level, we are not passing the team_id in query param,
   * so 'teamIdCheckForVersion' will be true for those cases.
   *
   * The check for team_id passing in query param for User level key is present in
   * the first 'if' block.
   */
  private def _isValidApiCall(
                               apiKeyType: SRApiKeyType,
                               apiTeamId: Option[SrIdentifier],
                               version: String,
                               apiKey: String,
                               permissionError: PermissionError
                             )(implicit permLogger: SRLogger): Either[Result, true] = {

    val baseErrMsg: String = s"[PermissionUtils._isValidApiCall] tid_$apiTeamId :: apiKey_$apiKey :: version_$version :: keyType_$apiKeyType"

    val apiVersionTry: Try[ApiVersion] = ApiVersion.withName(version)

    apiVersionTry match {

      case Failure(_) =>
        permLogger.warn(msg = s"incorrect version passed $baseErrMsg")
        permissionError.VERSION_NOT_SUPPORTED

      case Success(apiVersion) =>

        (
          apiKeyType,
          apiTeamId,
          apiVersion
        ) match {

          case (
            SRApiKeyType.SRUserLevelKey,
            None,
            ApiVersion.V1 | ApiVersion.V2 | ApiVersion.V3
            ) =>

            permLogger.warn(s"API_TEAM_ID_REQUIRED $baseErrMsg")
            permissionError.API_TEAM_ID_REQUIRED

          case (
            SRApiKeyType.SRUserLevelKey,
            Some(SrId(_)),
            ApiVersion.V3
            ) =>

            permLogger.warn(msg = s"team_id(long) passed for User level key v3 version $baseErrMsg")
            permissionError.INVALID_TEAM_ID

          case (
            SRApiKeyType.SRUserLevelKey,
            Some(SrUuid(_)),
            ApiVersion.V2 | ApiVersion.V1
            ) =>

            permLogger.warn(msg = s"team_uuid(string) passed for User level key v2 version $baseErrMsg")
            permissionError.INVALID_TEAM_ID

          case (
            SRApiKeyType.SRUserLevelKey,
            Some(SrUuid(_)),
            ApiVersion.V3
            ) =>

            Right(true)

          case (
            SRApiKeyType.SRUserLevelKey,
            Some(SrId(_)),
            ApiVersion.V2 | ApiVersion.V1
            ) =>

            Right(true)

          case (
            SRApiKeyType.SRProspectDaddyKey,
            None,
            ApiVersion.V1 | ApiVersion.V2 | ApiVersion.V3
            ) =>

            permLogger.warn(msg = s"API_TEAM_ID_REQUIRED $baseErrMsg")
            permissionError.API_TEAM_ID_REQUIRED

          case (
            SRApiKeyType.SRProspectDaddyKey,
            Some(SrId(_)),
            ApiVersion.V3
            ) =>

            permLogger.warn(msg = s"team_id(long) passed for ProspectDaddy key v3 version $baseErrMsg")
            permissionError.INVALID_TEAM_ID

          case (
            SRApiKeyType.SRProspectDaddyKey,
            Some(SrUuid(_)),
            ApiVersion.V2 | ApiVersion.V1
            ) =>

            permLogger.warn(msg = s"team_uuid(string) passed for ProspectDaddy key v2 version $baseErrMsg")
            permissionError.INVALID_TEAM_ID

          case (
            SRApiKeyType.SRProspectDaddyKey,
            Some(SrUuid(_)),
            ApiVersion.V3
            ) =>

            Right(true)

          case (
            SRApiKeyType.SRProspectDaddyKey,
            Some(SrId(_)),
            ApiVersion.V2 | ApiVersion.V1
            ) =>

            Right(true)

          case (
            SRApiKeyType.SRWarmupBoxKey,
            None | Some(SrId(_)) | Some(SrUuid(_)),
            ApiVersion.V1 | ApiVersion.V2 | ApiVersion.V3
            ) =>

            permLogger.warn(msg = s"SRWarmupBoxKey doesn't have access to any APIs $baseErrMsg")
            permissionError.SRWARMUPBOXKEY_NOT_HAVE_ACCESS_TO_API

          case (
            SRApiKeyType.SRTeamUserLevelKey | SRApiKeyType.SRZapierKey,
            None | Some(SrId(_)) | Some(SrUuid(_)),
            ApiVersion.V1 | ApiVersion.V2 | ApiVersion.V3
            ) =>

            Right(true)


        }

    }

  }

  ////////////////////////

  private final class PermissionError(
    val Res: SRAPIResponse, // response already logs the logRequestId
    Logger: SRLogger,
    implicit val version: ApiVersion = ApiVersion.V2
  ) {

    given srLogger: SRLogger = Logger // fixme given


    def UNAUTHORIZED(accountId: Option[AccountId],orgId: Option[OrgId])(implicit executionContext: ExecutionContext): Future[Left[Result, Nothing]] = {
      authUtils.performLogoutAndDropSession(
        accountId = accountId,
        orgId = orgId,
        logString = "PermissionError UNAUTHORIZED"
      ).map {
        case Left(LogoutError.AccountIdNotFoundError) =>  Left(Res.UnauthorizedError("Please login").withNewSession)

        case Left(LogoutError.LogoutUnsuccessfulSendServerError) => Left(Res.ServerError("Please Login",None).withNewSession)

        case Right(value) => Left(Res.UnauthorizedError("Please login").withNewSession)
      }.recover { case e =>
        Left(Res.ServerError(e).withNewSession)
      }
    }



    def ACCOUNT_DEACTIVATED(accountId: Option[AccountId],orgId:Option[OrgId])(implicit executionContext: ExecutionContext): Future[Left[Result, Nothing]] = {

      authUtils.performLogoutAndDropSession(
        accountId = accountId,
        orgId = orgId,
        logString = "PermissionError ACCOUNT_DEACTIVATED"
      ).map {
        case Left(LogoutError.AccountIdNotFoundError) => Left(Res.UnauthorizedError("Please login").withNewSession)

        case Left(LogoutError.LogoutUnsuccessfulSendServerError) => Left(Res.ServerError("Please Login",None).withNewSession)

        case Right(value) => Left(Res.UnauthorizedError( CONSTANTS.API_MSGS.ACCOUNT_DEACTIVATED_ERROR).withNewSession)
      }.recover { case e =>
        Left(Res.ServerError(e).withNewSession)
      }

    }


    def SUBSCRIPTION_INACTIVE = Left(Res.PaymentRequiredError(
      CONSTANTS.API_MSGS.SUBSCRIPTION_INACTIVE_ERROR
    ))


    def NO_IP_ACCESS = Left(Res.UnauthorizedError(
      "Please contact support for access."
    ).withNewSession)

    def NO_API_ACCESS = Left(Res.ForbiddenError(
      s"You do not have API access for this. Contact support if you have any question."
    ))


    // BadRequestError to avoid infinite reloads in frontend
    def NO_WEB_ACCESS = Left(Res.BadRequestError(
      s"You do not have access to this. Please contact support."
    ))

    def NO_ACCESS_TO_INBOX = Left(Res.BadRequestError(
      s"You do not have access to Inbox. Please ask the owner to grant that."
    ))

    // BadRequestError to avoid infinite reloads in frontend
    def DEPRECATED_V1_APIS = Left(Res.BadRequestError(
      s"This api has been deprecated. Please contact support. [v1]"
    ))

    // RELOAD IN FRONTEND IF MEMBER NOT IN TEAM ForbiddenError
    def MEMBER_NOT_IN_TEAM = Left(Res.ForbiddenError(
      s"You are not authorized to do this. Please contact support. [100]"
    ))


    // lets say loggedinuser has only 'edit_campaign'-'owned' permission,
    // and tries to create campaign in a different team member's view
    def MEMBER_IN_TEAM_BUT_NOT_ALLOWED = Left(Res.BadRequestError(
      s"You do not have the permission to do this for the current team member."
    ))


    def ROLE_NOT_FOUND = Left(Res.ForbiddenError(
      s"Your role was not found. Please contact support."
    ))


    def ROLE_DOES_NOT_HAVE_PERMISSION(userRole: RolePermissionDataV2, permission: PermType) = {
      //        Future.successful(BadRequestError(s"You do not have permission to do this."))
      Left(Res.BadRequestError(
        s"Your role (${userRole.role_name}) does not have permission to do this ($permission). Please contact your account owner, or contact our support."
      ))
    }

    def BAD_TID_ERROR = Left(Res.BadRequestError(
      s"Please choose team (tid query params missing)"
    ))

    /*
    def BAD_API_KEY_ERROR = Left(Res.BadRequestError(
      s"Please provide valid api key or contact support."
    ))
    */

    def API_TEAM_ID_REQUIRED = Left(Res.BadRequestError(
      s"Please pass a 'team_id' query param with this API. You can refer our API docs to know more about how to get 'team_id'. " +
        s"Link: https://help.smartreach.io/reference/using-the-smartreach-api"
    ))

    // val ROUTE_PERMISSION_NOT_DEFINED = Future.successful(ServerError("There was a server error. Please contact support. [rpm]", e = None))

    def AID_NOT_SUPPORTED_IN_V2 = Left(Res.BadRequestError(
      s"Invalid param: aid"
    ))

    def VERSION_NOT_SUPPORTED = Left(Res.BadRequestError(
      s"Please pass the correct api version"
    ))

    def INVALID_TEAM_ID: Left[Result, Nothing] = Left(Res.ForbiddenError(
      message = s"Please pass a valid team_id."
    ))
    
    def INTERNAL_ERROR_WHILE_FINDING_MEMBER(err: Throwable) = Left(Res.ServerError(err = err))

    def SRWARMUPBOXKEY_NOT_HAVE_ACCESS_TO_API = Left(Res.BadRequestError(message = "SRWarmupBoxKey does not have access to any APIs"))

  }

  private final def checkingPermission[A](
                                           version: String = "v2",
                                           permission: PermType,
                                           tidOpt: Option[Long] = None,

                                           webAccess: Boolean = true,
                                           apiAccess: Boolean = false,
                                           allowInAgencyDashboard: Boolean = false,
                                           request: AccountRequest[A],
                                           PERMISSION_ERROR: PermissionError,
                                           logRequestId: String,
                                           sessionIsInSupportAppMode: Boolean
                                         )(
                                           implicit executionContext: ExecutionContext,
                                           permLogger: SRLogger
                                         ): Future[Either[Result, PermissionRequest[A]]] = {


    /*
    if input string is: "api.campaigns.CampaignController.assignProspects",
    it will output: "CampaignController.assignProspects"
     */
    val apiPathForRateLimiter: String = {

      val handler = request.attrs(Router.Attrs.HandlerDef)

      val apiMethodControllerPath = s"${handler.controller}.${handler.method}"

      apiMethodControllerPath
        .split("\\.")
        .takeRight(2)
        .mkString(".")
    }

    val inactiveSubscription: Boolean = request.account.org.plan.plan_type == PlanType.INACTIVE


    if (request.isApiCall) {
      if (inactiveSubscription) {

        permLogger.warn(s"SUBSCRIPTION_INACTIVE")

        Future.successful(PERMISSION_ERROR.SUBSCRIPTION_INACTIVE)

      } else if (!apiAccess) {
        permLogger.fatal(s"NO_API_ACCESS: api: $apiPathForRateLimiter ::  isDev: ${AppConfig.isDevDomain} isLocal: ${AppConfig.isLocalDevDomain} :: dash: ${AppConfig.dashboardDomain} :: isProdRaw: ${AppConfig.isProdRaw}")

        Future.successful(PERMISSION_ERROR.NO_API_ACCESS)
      } else {

        val apiKey = request.apiKey.get
        val keyType = SRApiKeyType.getKeyTypeFromPrefix(key = apiKey)


        // for user-level api keys, we expect a "team_id" query param for any api that uses "checkPermission"
        val apiTeamId: Option[SrIdentifier] = request.queryString.get("team_id")
          .flatMap(_.headOption)
          .map(teamIdStr => SrIdentifier.fromQueryParamString(teamIdStr))

        val isValidApiCall: Either[Result, true] = _isValidApiCall(
          apiKeyType = keyType,
          apiTeamId = apiTeamId,
          version = version,
          apiKey = apiKey,
          permissionError = PERMISSION_ERROR
        )

        isValidApiCall match {

          case Left(err) => Future.successful(Left(err))

          case Right(true) =>

            val loggedinAccount = request.account

            val memberTry: Try[Option[TeamMember]] = _getTeamMember(
              keyType = keyType,
              apiKey = apiKey,
              orgId = OrgId(request.account.org.id),
              loggedinAccount = loggedinAccount,
              apiTeamId = apiTeamId
            )

            memberTry match {

              case Failure(err) =>
                if(err.getMessage == CONSTANTS.APP_ERR_MSGS.UUID_NOT_FOUND_ERR_MSG) {
                  permLogger.warn(msg = s"[APICALL] Invalid_Team_Uuid: $apiKey :: ${loggedinAccount.internal_id}")
                  Future.successful(PERMISSION_ERROR.INVALID_TEAM_ID)
                } else {
                  permLogger.shouldNeverHappen(s"[APICALL] error while fetching team_id from team_uuid: $apiTeamId :: $apiKey :: ${loggedinAccount.internal_id}", Some(err))
                  Future.successful(
                    PERMISSION_ERROR.INTERNAL_ERROR_WHILE_FINDING_MEMBER(err)
                  )
                }

              case Success(member) =>

                permLogger.info(s"[APICALL] ${loggedinAccount.internal_id} :: $logRequestId")
                if (!loggedinAccount.active) {

                  permLogger.fatal(s"[APICALL] ACCOUNT_DEACTIVATED: $apiKey :: ${loggedinAccount.internal_id}")


                  PERMISSION_ERROR.ACCOUNT_DEACTIVATED(accountId = Some(AccountId(id = loggedinAccount.internal_id)), orgId = Some(OrgId(id = loggedinAccount.org.id)))

                } else if (member.isEmpty) {

                  permLogger.fatal(s"[APICALL] MEMBER_NOT_IN_TEAM could not find team: $apiKey :: ${loggedinAccount.internal_id}")

                  Future.successful(PERMISSION_ERROR.MEMBER_NOT_IN_TEAM)

                } else {

                  if (loggedinAccount.internal_id != member.get.user_id) {
                    permLogger.fatal(s"olduicheck :: isAPi case :: loggedinAccount.id != member.get.user_id :: ${loggedinAccount.internal_id} != ${member.get.user_id}")

                  }

                  val userRole: Option[RolePermissionDataV2] = PermissionMethods.getUserRole(loggedinAccount = loggedinAccount, teamId = member.get.team_id, ignoreFatalLogging = sessionIsInSupportAppMode,
                    Logger = permLogger)

                  if (userRole.isEmpty) {
                    permLogger.fatal(s"[APICALL] ROLE_NOT_FOUND api key: $apiKey :: ${loggedinAccount.internal_id}")

                    Future.successful(PERMISSION_ERROR.ROLE_NOT_FOUND)
                  } else {
                    val rolePerm = RolePermissionsV2.getRolePermission(permissionRequired = permission, userRole = userRole.get)

                    if (rolePerm.ownership == PermissionOwnershipV2.NoAccess) {
                      permLogger.fatal(s"[APICALL] ROLE_DOES_NOT_HAVE_PERMISSION api key: $apiKey :: ${loggedinAccount.internal_id}")

                      Future.successful(
                        PERMISSION_ERROR.ROLE_DOES_NOT_HAVE_PERMISSION(userRole = userRole.get, permission = permission)
                      )
                    } else {

                      val auditRequestLogId = srUuidUtils.generateRequestLogId(teamId = member.get.team_id, accountId = loggedinAccount.internal_id)

                      val permittedAccountIds = PermissionMethods.getPermittedAccountIds(
                        loggedinAccount = loggedinAccount,
                        viewTid = member.get.team_id,
                        viewAid = member.get.user_id,
                        permissionOwnership = rolePerm.ownership,
                        version = version,
                        Logger = permLogger
                      )

                      // should not happen in this case, but still checking
                      if (!permittedAccountIds.contains(member.get.user_id)) {

                        permLogger.fatal(s"[APICALL] MEMBER_IN_TEAM_BUT_NOT_ALLOWED api key: $apiKey :: ${loggedinAccount.internal_id}")

                        Future.successful(PERMISSION_ERROR.MEMBER_IN_TEAM_BUT_NOT_ALLOWED)
                      } else {

                        val clientIP = Helpers.getClientIPFromGCPLoadbalancer(request = request)

                        val ipAccess = authUtils.checkIfIPAccessAndLogForAudit(
                          loggedinAccount = loggedinAccount,
                          member = member,
                          accessType = SRAccessType.API,
                          clientIP = clientIP,
                          isSupportAppModeRequest = false,
                          request_method = request.method,
                          request_host = request.host,
                          request_url = request.path,
                          request_query = request.rawQueryString
                        )

                        if (!ipAccess) {
                          Future.successful(PERMISSION_ERROR.NO_IP_ACCESS)
                        } else {


                          val Logger = new SRLogger(
                            logRequestId = s"$logRequestId apicall lid_${loggedinAccount.internal_id} aid_${member.map(_.user_id.toString).getOrElse("null")} tid_${member.map(_.team_id.toString).getOrElse("null")} :: "
                          )


                          val rateLimitOK = srRateLimiter.rateLimitAPIAndLogOverallFrequency(
                            apiPath = apiPathForRateLimiter,
                            loggedinAccountId = loggedinAccount.internal_id,
                            orgId = loggedinAccount.org.id,
                            logger = Logger
                          )

                          Future.successful(Right(PermissionRequest(
                            auditRequestLogId = auditRequestLogId,
                            loggedinAccount = loggedinAccount,
                            isSupportAppModeRequest = false,
                            actingTeamAccount = member,

                            // loggedinAccount route specific view
                            permittedAccountIds = permittedAccountIds,

                            isApiCall = true,
                            Logger = Logger,
                            Response = new ApiResponseModuleForPermissionedApis(requestLogService = requestLogService, Logger = Logger, ec = executionContext),
                            request
                          )))
                        }
                      }
                    }
                  }
                }
            }
        }
      }
    } else {

      // check restrictions for master
      val apiAllowedInSupportMode = PermissionUtils.checkIfApiAllowedInSupportMode(
        requestMethod = request.method,
        requestPath = request.path
      )

      val isInboxRequest = PermissionUtils.checkIfInboxApiRequest(
        requestPath = request.path
      )

      val access_to_inbox_given = request.account.account_access.inbox_access

      val isV2OrV3 = version == "v2" || version == "v3"

      if (!isV2OrV3) {

        Future.successful(PERMISSION_ERROR.DEPRECATED_V1_APIS)

      } else if (sessionIsInSupportAppMode && !apiAllowedInSupportMode) {

        PERMISSION_ERROR.UNAUTHORIZED(accountId = Some(AccountId(id = request.account.internal_id)),orgId = Some(OrgId(id = request.account.org.id)))

      } else if (sessionIsInSupportAppMode && isInboxRequest && !access_to_inbox_given) {
        permLogger.fatal(s"[WEBCALL] NO_ACCESS_TO_INBOX")

        Future.successful(PERMISSION_ERROR.NO_ACCESS_TO_INBOX)

      } else if (!webAccess) {
        permLogger.shouldNeverHappen(s"[WEBCALL] NO_WEB_ACCESS")

        Future.successful(PERMISSION_ERROR.NO_WEB_ACCESS)
      } else {

        val loggedinAccount = request.account

        val accId = AccountId( id = loggedinAccount.internal_id)
        val orgId = OrgId(id = loggedinAccount.org.id)

        if (!loggedinAccount.active) {

          permLogger.fatal(s"ACCOUNT_DEACTIVATED: WEB API: ${loggedinAccount.internal_id}")


          PERMISSION_ERROR.ACCOUNT_DEACTIVATED(accountId = Some(accId),orgId = Some(orgId))

        } else if (loggedinAccount.org.settings.enforce_2fa && !loggedinAccount.profile.twofa_enabled) {

          permLogger.fatal(s"2FA ENFORCED BUT NOT ENABLED: ${loggedinAccount.internal_id}")


          PERMISSION_ERROR.UNAUTHORIZED(accountId = Some(accId),orgId =Some(orgId))
        }
        else {


          if (tidOpt.isEmpty) {

            if (
              permission == PermType.JUST_LOGGEDIN ||
                permission == PermType.MANAGE_BILLING
            ) {

              val userRole: Option[RolePermissionDataV2] = PermissionMethods.getUserRole(loggedinAccount = loggedinAccount, teamId = 0, ignoreFatalLogging = sessionIsInSupportAppMode,
                Logger = permLogger
              )
              if (userRole.isEmpty) {

                if (!sessionIsInSupportAppMode) {
                  permLogger.fatal(s"[WEBCALL] ROLE_NOT_FOUND :: ${loggedinAccount.internal_id}")
                }

                Future.successful(PERMISSION_ERROR.ROLE_NOT_FOUND)

              } else {
                val rolePerm = RolePermissionsV2.getRolePermission(permissionRequired = permission, userRole = userRole.get)
                if (rolePerm.ownership == PermissionOwnershipV2.NoAccess) {
                  permLogger.fatal(s"[WEBCALL] ROLE_DOES_NOT_HAVE_PERMISSION :: ${loggedinAccount.internal_id}")

                  Future.successful(
                    PERMISSION_ERROR.ROLE_DOES_NOT_HAVE_PERMISSION(userRole = userRole.get, permission = permission)
                  )

                } else {

                  val clientIP = Helpers.getClientIPFromGCPLoadbalancer(request = request)

                  val ipAccess = authUtils.checkIfIPAccessAndLogForAudit(
                    loggedinAccount = loggedinAccount,
                    member = None,
                    accessType = SRAccessType.WEB,
                    clientIP = clientIP,
                    isSupportAppModeRequest = sessionIsInSupportAppMode,
                    request_method = request.method,
                    request_host = request.host,
                    request_url = request.path,
                    request_query = request.rawQueryString
                  )

                  if (!ipAccess) {
                    Future.successful(PERMISSION_ERROR.NO_IP_ACCESS)
                  } else {

                    val Logger = new SRLogger(logRequestId = s"$logRequestId sess lid_${loggedinAccount.internal_id} aid_${loggedinAccount.internal_id} perm: $permission :: ")

                    val rateLimitOK = srRateLimiter.rateLimitAPIAndLogOverallFrequency(
                      apiPath = apiPathForRateLimiter,
                      loggedinAccountId = loggedinAccount.internal_id,
                      orgId = loggedinAccount.org.id,
                      logger = Logger
                    )

                    val auditRequestLogId = srUuidUtils.generateRequestLogId(teamId = 0, accountId = loggedinAccount.internal_id)

                    Future.successful(Right(
                      PermissionRequest(
                        auditRequestLogId = auditRequestLogId,
                        loggedinAccount = loggedinAccount,
                        isSupportAppModeRequest = sessionIsInSupportAppMode,
                        actingTeamAccount = None,
                        permittedAccountIds = Seq(), // no team selected
                        isApiCall = false,
                        Logger = Logger,
                        Response = new ApiResponseModuleForPermissionedApis(requestLogService = requestLogService, Logger = Logger, ec = executionContext),
                        request
                      )
                    ))
                  }
                }
              }

            } else {

              if (sessionIsInSupportAppMode) {
                permLogger.error(s"[WEBCALL] BAD_AID_TID_ERROR :: ${loggedinAccount.internal_id}")
              } else {
                permLogger.fatal(s"[WEBCALL] BAD_AID_TID_ERROR :: ${loggedinAccount.internal_id}")
              }

              // for web requests, tid is required
              Future.successful(PERMISSION_ERROR.BAD_TID_ERROR)
            }

          } else if (tidOpt.get < 0) {

            permLogger.fatal(s"[WEBCALL] BAD_AID_TID_ERROR :: ${loggedinAccount.internal_id}")

            Future.successful(PERMISSION_ERROR.BAD_TID_ERROR)

          } else {

            val loggedinAccountId = loggedinAccount.internal_id
            val viewTid = tidOpt.get

            // MUST GET ROLE OF LOGGEDIN ACCOUNT, NOT VIEW ACCOUNT
            val userRole: Option[RolePermissionDataV2] = PermissionMethods.getUserRole(loggedinAccount = loggedinAccount, teamId = viewTid, ignoreFatalLogging = sessionIsInSupportAppMode,
              Logger = permLogger
            )

            if (userRole.isEmpty) {

              if (!sessionIsInSupportAppMode) {
                permLogger.fatal(s"[WEBCALL] ROLE_NOT_FOUND :: $loggedinAccountId")
              }

              Future.successful(PERMISSION_ERROR.ROLE_NOT_FOUND)

            } else {
              val rolePerm = RolePermissionsV2.getRolePermission(permissionRequired = permission, userRole = userRole.get)

              // if not org_admin, they will be rejected in the below rolePerm.permitted step
              if (rolePerm.ownership == PermissionOwnershipV2.NoAccess) {
                permLogger.fatal(s"[WEBCALL] ROLE_DOES_NOT_HAVE_PERMISSION :: $loggedinAccountId")

                Future.successful(
                  PERMISSION_ERROR.ROLE_DOES_NOT_HAVE_PERMISSION(userRole = userRole.get, permission = permission)
                )

              } else if (viewTid == 0) {
                // agency dashboard

                if (allowInAgencyDashboard) {

                  val clientIP = Helpers.getClientIPFromGCPLoadbalancer(request = request)

                  val ipAccess = authUtils.checkIfIPAccessAndLogForAudit(
                    loggedinAccount = loggedinAccount,
                    member = None,
                    accessType = SRAccessType.WEB,
                    clientIP = clientIP,
                    isSupportAppModeRequest = sessionIsInSupportAppMode,
                    request_method = request.method,
                    request_host = request.host,
                    request_url = request.path,
                    request_query = request.rawQueryString
                  )

                  if (!ipAccess) {
                    Future.successful(PERMISSION_ERROR.NO_IP_ACCESS)
                  } else {

                    val Logger = new SRLogger(logRequestId = s"$logRequestId sess aid_$loggedinAccountId agency-dashboard :: ")

                    val rateLimitOK = srRateLimiter.rateLimitAPIAndLogOverallFrequency(
                      apiPath = apiPathForRateLimiter,
                      loggedinAccountId = loggedinAccountId,
                      orgId = loggedinAccount.org.id,
                      logger = Logger
                    )

                    val auditRequestLogId = srUuidUtils.generateRequestLogId(teamId = 0, accountId = loggedinAccountId)

                    Future.successful(Right(
                      PermissionRequest(
                        auditRequestLogId = auditRequestLogId,
                        loggedinAccount = loggedinAccount,
                        isSupportAppModeRequest = sessionIsInSupportAppMode,
                        actingTeamAccount = None,
                        permittedAccountIds = Seq(), // no team selected
                        isApiCall = false,
                        Logger = Logger,
                        Response = new ApiResponseModuleForPermissionedApis(requestLogService = requestLogService, Logger = Logger, ec = executionContext),
                        request
                      )
                    ))
                  }

                } else {
                  permLogger.fatal(s"[WEBCALL] BAD_AID_TID_ERROR :: $loggedinAccountId")

                  Future.successful(PERMISSION_ERROR.BAD_TID_ERROR)

                }
              } else {


                // check if loggedin account has access to team and account (viewTid, loggedinAccountId)
                val permittedAccounts = PermissionMethods.getPermittedAccountIds(
                  loggedinAccount = loggedinAccount,
                  viewTid = viewTid,
                  viewAid = loggedinAccountId,
                  permissionOwnership = rolePerm.ownership,
                  version = version,
                  Logger = permLogger
                )

                val member = _checkViewTidViewAidAccess(
                  loggedInAccount = loggedinAccount,
                  tid = viewTid,
                  Logger = permLogger
                )


                //val member = permittedAccounts.find(pa => pa == loggedinAccountId)

                if (member.isEmpty) {
                  permLogger.fatal(s"[WEBCALL] MEMBER_NOT_IN_TEAM :: $loggedinAccountId")

                  Future.successful(PERMISSION_ERROR.MEMBER_NOT_IN_TEAM)

                } else if (!permittedAccounts.contains(member.get.user_id)) {

                  permLogger.fatal(s"[WEBCALL] MEMBER_IN_TEAM_BUT_NOT_ALLOWED :: $loggedinAccountId")

                  Future.successful(PERMISSION_ERROR.MEMBER_IN_TEAM_BUT_NOT_ALLOWED)

                } else {

                  val clientIP = Helpers.getClientIPFromGCPLoadbalancer(request = request)

                  val ipAccess = authUtils.checkIfIPAccessAndLogForAudit(
                    loggedinAccount = loggedinAccount,
                    member = member,
                    accessType = SRAccessType.WEB,
                    clientIP = clientIP,
                    isSupportAppModeRequest = sessionIsInSupportAppMode,
                    request_method = request.method,
                    request_host = request.host,
                    request_url = request.path,
                    request_query = request.rawQueryString
                  )

                  if (!ipAccess) {
                    Future.successful(PERMISSION_ERROR.NO_IP_ACCESS)
                  } else {

                    val Logger = new SRLogger(
                      logRequestId = s"$logRequestId sess lid_$loggedinAccountId aid_${member.map(_.user_id.toString).getOrElse("null")} tid_${member.map(_.team_id.toString).getOrElse("null")} ::"
                    )

                    val rateLimitOK = srRateLimiter.rateLimitAPIAndLogOverallFrequency(
                      apiPath = apiPathForRateLimiter,
                      loggedinAccountId = loggedinAccountId,
                      orgId = loggedinAccount.org.id,
                      logger = Logger
                    )

                    val auditRequestLogId = srUuidUtils.generateRequestLogId(teamId = member.get.team_id, accountId = loggedinAccountId)

                    Future.successful(Right(
                      PermissionRequest(
                        auditRequestLogId = auditRequestLogId,
                        loggedinAccount = loggedinAccount,
                        isSupportAppModeRequest = sessionIsInSupportAppMode,
                        actingTeamAccount = member,

                        // loggedinAccount route specific view
                        permittedAccountIds = permittedAccounts,

                        isApiCall = false,
                        Logger = Logger,
                        Response = new ApiResponseModuleForPermissionedApis(requestLogService = requestLogService, Logger = Logger, ec = executionContext),
                        request
                      )
                    ))
                  }

                }
              }

            }

          }


        }
      }
    }
  }

  final def checkPermission(
                             version: String = "v2",
                             permission: PermType,
                             tidOpt: Option[Long] = None,

                             webAccess: Boolean = true,
                             apiAccess: Boolean = false,

                             /**
     * 29-Mar-2024: we need to write integration tests for some APIs which were not publicly open (i.e apiAccess was false)
     * but were getting used in the mobile apps.
     *
     * Therefore, we added this localOrStagingApiTestAccess flag, to open up those apis only for our integration tests.
     */
                             localOrStagingApiTestAccess: Boolean = false,

                             allowInAgencyDashboard: Boolean = false,
                             url_api_key_allowed: Boolean = false

  ) = authUtils.isLoggedIn(url_api_key_allowed = url_api_key_allowed) andThen new ActionRefiner[AccountRequest, PermissionRequest] {

    override def executionContext: ExecutionContext = ec

    override protected def refine[A](request: AccountRequest[A]): Future[Either[Result, PermissionRequest[A]]] = {


      val logRequestId = request.Logger.logRequestId
      val sessionIsInSupportAppMode = request.isSupportAppRequest
      val nonFatalErrorLogPrepend = s"$logRequestId [checkPermissions] version2: $version :: m: m_$sessionIsInSupportAppMode :: ($tidOpt, $permission, ${request.host}, Client-IP: ${Helpers.getClientIPFromGCPLoadbalancer(request = request)}) :: "
      val permLogger: SRLogger = new SRLogger(logRequestId = nonFatalErrorLogPrepend)
      val PERMISSION_ERROR = new PermissionError(Res = new SRAPIResponse(permLogger),Logger = permLogger)

      val enableApiAccess: Boolean = {

        if (apiAccess) {
          // public api access is enabled for this api
          true

        } else if (
          localOrStagingApiTestAccess &&
            (AppConfig.isDevDomain || AppConfig.isLocalDevDomain)
        ) {

          // this api is most likely being integration tested in the staging env
          true

        } else {

          // no api access
          false
        }
      }

      checkingPermission(
        version = version,
        permission = permission,
        tidOpt = tidOpt,

        webAccess = webAccess,
        apiAccess = enableApiAccess,
        allowInAgencyDashboard = allowInAgencyDashboard,
        request = request,

        logRequestId = logRequestId,
        sessionIsInSupportAppMode = sessionIsInSupportAppMode,
        PERMISSION_ERROR = PERMISSION_ERROR
      )(
        executionContext = executionContext,
        permLogger = permLogger
      )

    }
  }

  //This is to check for version v3 having uuid passed for public Apis
  final def checkPermissionV2(
                             id: Option[SrIdentifier],
                             version: ApiVersion = ApiVersion.V2,
                             permission: PermType,
                             tidOpt: Option[Long] = None,

                             webAccess: Boolean = true,
                             apiAccess: Boolean = false,
                             allowInAgencyDashboard: Boolean = false

                           ) = authUtils.isLoggedIn(apiVersion = version) andThen new ActionRefiner[AccountRequest, PermissionRequest] {

    override def executionContext: ExecutionContext = ec

    override protected def refine[A](request: AccountRequest[A]): Future[Either[Result, PermissionRequest[A]]] = {


      val logRequestId = request.Logger.logRequestId
      val sessionIsInSupportAppMode = request.isSupportAppRequest
      val nonFatalErrorLogPrepend = s"$logRequestId [checkPermissions] version2: $version :: m: m_$sessionIsInSupportAppMode :: ($tidOpt, $permission, ${request.host}, Client-IP: ${Helpers.getClientIPFromGCPLoadbalancer(request = request)}) :: "
      val permLogger: SRLogger = new SRLogger(logRequestId = nonFatalErrorLogPrepend)
      implicit val apiVersion: ApiVersion = version
      val Res = new SRAPIResponse(permLogger)
      val PERMISSION_ERROR = new PermissionError(Res,Logger = permLogger)

      //GET should have id SrIdentifier
      val reqType = request.request.method

      RequestHeaderMethod.fromKey(reqType) match {
        case Failure(_) =>
          Future.successful(Left(Res.BadRequestError("Invalid request header")))

        case Success(reqMethod) =>

          if(reqMethod == RequestHeaderMethod.POST && id.isDefined){
            Future.successful(Left(Res.BadRequestError("Query param should not be passed for POST request")))
          } else {

            val isCorrectVersion: Boolean = if (id.isDefined) {
              id.get match {
                case SrId(_) => version == ApiVersion.V1 || version == ApiVersion.V2
                case SrUuid(_) => version == ApiVersion.V3
              }
            } else {
              version match {
                case ApiVersion.V1 | ApiVersion.V2 | ApiVersion.V3 => true
//                case _ => false
              }
            }

            if (!isCorrectVersion) {
              Future.successful(PERMISSION_ERROR.VERSION_NOT_SUPPORTED)
            } else {
              checkingPermission(
                version = version.toString,
                permission = permission,
                tidOpt = tidOpt,

                webAccess = webAccess,
                apiAccess = apiAccess,
                allowInAgencyDashboard = allowInAgencyDashboard,
                request = request,

                logRequestId = logRequestId,
                sessionIsInSupportAppMode = sessionIsInSupportAppMode,
                PERMISSION_ERROR = PERMISSION_ERROR
              )(
                executionContext = executionContext,
                permLogger = permLogger
              )
            }
          }
      }

    }
  }

}


object PermissionUtils {

  // paths allowed in support mode (CSD)
  def checkIfApiAllowedInSupportMode(

    requestMethod: String,
    requestPath: String

  ): Boolean = {

    val isReadRequest = requestMethod == "GET"
    val isApiRequest = requestPath.startsWith("/api/v1") ||
      requestPath.startsWith("/api/v2")

    // all GET calls and prospect filter and send test email
    val defaultAllowedRequest = (isReadRequest &&
      !requestPath.contains("/api_key") // API keys are not viewable in read-only mode
      ) ||
      (requestMethod == "POST" &&
        isApiRequest && (

        requestPath.contains("/prospects/query") ||
          requestPath.contains("/search") ||
          requestPath.endsWith("/steps/send_test") ||
          requestPath.contains("/get_inbox_draft") ||
          requestPath.contains("/export") ||
          requestPath.contains("/stats/") ||
          requestPath.contains("/reports/") ||
          requestPath.contains("/spam_tests") || // 6-dec-2024: allow CSD to trigger spam tests
          requestPath.contains("/billing_session") ||
          requestPath.contains("/voice/format_number") ||
          requestPath.contains("/previews/prospects/")

        ) && !(

        // save filters api not allowed
        requestPath.contains("/stats/reports/filters")


        )) ||
      (requestMethod == "PUT" &&
        requestPath.contains("/save_drip") // FIXMEL added in 11 June'25 for a single user - remove it immediately
        )

    defaultAllowedRequest
  }

  def checkIfInboxApiRequest(

    requestPath: String

  ): Boolean = {

    val isInboxRequest = (
      requestPath.contains("/inbox") ||
        requestPath.contains("/team_inbox")
      ) && !requestPath.contains("/team_inbox/reply_sentiments")

    isInboxRequest
  }

}