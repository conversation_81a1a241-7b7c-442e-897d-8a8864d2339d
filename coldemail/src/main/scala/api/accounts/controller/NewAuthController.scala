package api.accounts.controller


import api.{AppConfig, CONSTANTS, CacheService, SRAPIResponse}
import api.accounts.models.SignupType
import api.google_recaptcha.model.RecaptchaActionType
import api.google_recaptcha.service.{CheckIfWeShowCaptchaError, ReCaptchaError, RecaptchaData, SRGoogleRecaptchaServices}
import api.accounts.service.{AccountWithRedirectUri, AccountWithRedirectionData, AuthenticateResponseType, AuthorizationUrlError, CheckIfAllowedAndTypeOfSignupError, CheckIfAllowedToLoginError, FetchOrMakeAccountError, NewAuthFlowService}
import api.reports.models.UtmData
import api.spammonitor.service.CheckIfEmailIsAllowedToSignUpError
import org.joda.time.DateTime
import org.joda.time.format.ISODateTimeFormat
import play.api.libs.json.{J<PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>}
import play.api.libs.ws.WSClient
import play.api.mvc.{Action, BaseController, ControllerComponents}
import utils.emailvalidation.EmailValidationService
import utils.logging.SRAccessType
import utils.{Helpers, SRLogger, StringUtils}
import utils.helpers.LogHelpers

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

case class OAuthValidateData(
                              state: String,
                              code: String,
                              timezone: Option[String],
                              country_code: String,
                              signupType: String
                            )

object OAuthValidateData {
  given reads: Reads[OAuthValidateData] = Json.reads[OAuthValidateData]

}

case class SignupCheckData(
                            accountEmail: String,
                            g_response: Option[String] //response from google when we check the captcha
                          )

object SignupCheckData {
  implicit val reads: Reads[SignupCheckData] = Json.reads[SignupCheckData]

}

case class NewOAuthSignupData(
                               accountEmail: String,
                               signupType: String,
                               invite_code: Option[String],
                               login_challenge: Option[String]
                             )

object NewOAuthSignupData {
  implicit val reads: Reads[NewOAuthSignupData] = Json.reads[NewOAuthSignupData]
}

class NewAuthController(
                         newAuthFlowService: NewAuthFlowService,
                         sRGoogleRecaptchaServices: SRGoogleRecaptchaServices,
                         cacheService: CacheService,
                         protected val controllerComponents: ControllerComponents,
                         implicit val ws: WSClient

                       ) extends BaseController {

  private implicit val ec: ExecutionContext = controllerComponents.executionContext


  def findHostType: Action[JsValue] = Action.async(parse.json) { request =>
    val validateData = request.body.validate[SignupCheckData]
    val logRequestId = s"${StringUtils.genLogTraceId} NewAuthController.authenticate: "
    given Logger: SRLogger = new SRLogger(logRequestId = logRequestId)
    val userIp = Helpers.getClientIPFromGCPLoadbalancer(request)
      .getOrElse("0.0.0.0")
    val Res = new SRAPIResponse(Logger = Logger)
    validateData match {
      case JsError(err) =>
        Logger.fatal(s"wrong data sent $err")
        Future.successful(Res.JsValidationError(err))

      case JsSuccess(value, _) =>
        val data = RecaptchaData(
          requestIp = userIp,
          actionType = RecaptchaActionType.Signup,
          response = value.g_response
        )

        sRGoogleRecaptchaServices.checkIfWeShowCaptchaOrVerifyIt(data).flatMap {
          case Left(ReCaptchaError.VerifyCaptchaError(err)) =>

            Logger.fatal(s"Error while verifying captcha ---- $err") //error not of throwable type
            Future.successful(Res.ServerError("error while verifying the captcha", None))

          case Left(ReCaptchaError.NoGResponseSentError) =>

            Future.successful(Res.Success("", Json.obj("showCaptcha" -> true, "passedCaptcha" -> false)))

          case Left(ReCaptchaError.ShowCaptchaError(err)) =>

            err match {

              case CheckIfWeShowCaptchaError.RedisError(err) =>

                Logger.fatal("Error from redis", err = err)
                Future.successful(Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, None))

              case CheckIfWeShowCaptchaError.CouldnotCreateRedisKeyError(err) =>

                Logger.fatal(s"Wrong reply from redis expected OK got  - $err") //error is string
                Future.successful(Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, None))
            }

          case Left(ReCaptchaError.TestingEmailShouldNotHaveGResponse(errStr)) =>
            Logger.fatal(s"IMPOSSIBLE ERROR We are not having g_response for testing email in this flow- $errStr")
            Future.successful(Res.BadRequestError("Sorry please try again"))

          case Right(recaptchaResponse) =>
            newAuthFlowService.checkIfAllowedToSignUpAndTypeOfSignup(value.accountEmail).flatMap {

              case Left(CheckIfAllowedAndTypeOfSignupError.AlreadySignedUp) =>

                //This error msg is to prevent Harvesting
                //            Res.BadRequestError("Please choose a different username.")
                Future.successful(Res.Success("found type of signup", Json.obj("signupType" -> SignupType.Password.toString, "showCaptcha" -> recaptchaResponse.showCaptcha, "passedCaptcha" -> recaptchaResponse.passedCaptcha)))

              case Left(CheckIfAllowedAndTypeOfSignupError.ErrorFindingAccount(err)) =>

                Logger.fatal("Error while finding Account", err = err)
                Future.successful(Res.ServerError("Please Try again later", Some(err)))
              case Left(CheckIfAllowedAndTypeOfSignupError.SQLErrorWhileCheckingIfFreeDomain(err)) =>

                Logger.fatal("Error while checking if the account is free", err = err)
                Future.successful(Res.ServerError("Please Try again later", Some(err)))

              case Left(CheckIfAllowedAndTypeOfSignupError.EmailIsFromFreeDomain) =>

                Future.successful(Res.BadRequestError("Please use your work email"))

              case Right(signupType) =>
                Logger.info(s"checkIfAllowedToSignUpAndTypeOfSignup success for ${value.accountEmail}")
                Future.successful(Res.Success("found type of signup", Json.obj("signupType" -> signupType.toString, "showCaptcha" -> recaptchaResponse.showCaptcha, "passedCaptcha" -> recaptchaResponse.passedCaptcha)))

            }
        }


    }
  }


  def getOAuthUrl: Action[JsValue] = Action.async(parse.json) { request =>

    val logRequestId = s"${StringUtils.genLogTraceId} NewAuthController.getOAuthUrl: "
    given Logger: SRLogger= new SRLogger(logRequestId = logRequestId)
    val Res = new SRAPIResponse(Logger = Logger)
    val validateData = request.body.validate[NewOAuthSignupData]

    validateData match {
      case JsError(err) =>
        Logger.fatal(s"wrong data sent $err")
        Future.successful(Res.JsValidationError(err))

      case JsSuccess(data, _) =>

        newAuthFlowService.getAuthorizationUrl(data = data).map {
          case Left(AuthorizationUrlError.InvalidEmail) => Res.BadRequestError("Please send a valid email")
          case Left(AuthorizationUrlError.WrongSignupType(err)) => {
            Logger.fatal(s"Wrong signup type from frontend ${data.signupType}", err = err)
            Res.BadRequestError("Please Try Again")
          }
          case Right(authUrl) => Res.Success("Redirect user to Auth Page auth page", Json.obj("redirect_to" -> authUrl))

        }.recover { case err => {
          Logger.fatal("Error While getting redirect Uri", err = err)
          Res.ServerError("Try Again Later", Some(err))
        }
        }
    }

  }

  /**
   * Path to Referral Related UML Diagram:
   * seq-diagrams/sr-referral-program/2-affiliate-signup-2-oauth.plantuml
   */
  def authenticateUserViaOauthFlow: Action[JsValue] = Action.async(parse.json) { request =>
    val logRequestId = s"${StringUtils.genLogTraceId} NewAuthController.authenticateUserViaOauthFlow: "
    given Logger: SRLogger= new SRLogger(logRequestId = logRequestId)
    val Res = new SRAPIResponse(Logger = Logger)
    val validateData = request.body.validate[OAuthValidateData]


    val utmSource = request.cookies.get("utm_source").map(_.value)
    val utmMedium = request.cookies.get("utm_medium").map(_.value)
    val utmCampaign = request.cookies.get("utm_campaign").map(_.value)
    val utmTerm = request.cookies.get("utm_term").map(_.value)
    val utmContent = request.cookies.get("utm_content").map(_.value)
    val utmDevice = request.cookies.get("utm_device").map(_.value)
    val utmAgid = request.cookies.get("utm_agid").map(_.value)
    val utmMatchType = request.cookies.get("utm_match_type").map(_.value)
    val utmPlacement = request.cookies.get("utm_placement").map(_.value)
    val utmNetwork = request.cookies.get("utm_network").map(_.value)

    val utmData = UtmData(
      utm_source = utmSource,
      utm_medium = utmMedium,
      utm_campaign = utmCampaign,
      utm_term = utmTerm,
      utm_content = utmContent,
      utm_device = utmDevice,
      utm_agid = utmAgid,
      utm_match_type = utmMatchType,
      utm_placement = utmPlacement,
      utm_network = utmNetwork
    )

    // We are creating and storing these cookies (admitad_created_at & fp_created_at),
    // So that when processing the affiliate commission, we can identify from where did we get the affiliate.
    val admitadCreateAt = request.cookies.get(AppConfig.admitadCreatedAtCookieName)
      .map { c =>
        DateTime.parse(c.value, ISODateTimeFormat.dateTimeParser())
      }

    val firstPromoterCreateAt =
      request.cookies.get(AppConfig.firstPromoterCreatedAtCookieName)
        .map { c =>
          DateTime.parse(c.value, ISODateTimeFormat.dateTimeParser())
        }

    validateData match {
      case JsError(errors) => {
        Logger.fatal(s"Alert LoginFlow: JsValidation Error Occurred for email  ${errors.toString()}")
        Future.successful(Res.JsValidationError(errors))
      }
      case JsSuccess(data, _) =>
        val signupTypeTry = SignupType.fromKeyOAuth(data.signupType)
        signupTypeTry match {
          case Failure(err) =>

            Logger.fatal(s"Alert LoginFlow: Wrong signup type from frontend ${data.signupType}", err = err)
            Future.successful(Res.BadRequestError("Please Try Again"))

          case Success(signupType) =>
            Logger.info(s"Signup type is ${signupType.toString}")

            val isLocalhost = request.domain.toLowerCase == "localhost"

            val clientIp = Helpers.getClientIPFromGCPLoadbalancer(request = request)


            newAuthFlowService.authenticateUserViaOauthFlow(
              data = data,
              utmData = utmData,
              signupType = signupType,
              admitadCreateAt = admitadCreateAt,
              firstPromoterCreateAt = firstPromoterCreateAt,
              cookies = request.cookies,
              isLocalhost = isLocalhost,
              clientIP = clientIp,
              request_method = request.method,
              request_host = request.host,
              request_url = request.path,
              request_query = request.rawQueryString
            ).map {
              case Left(FetchOrMakeAccountError.IpAccessIsFalse) => Res.UnauthorizedError(
                "Please contact support (<EMAIL>) for access. [2]"
              )
              case Left(FetchOrMakeAccountError.InvalidLoginChallenge(err)) => {
                Logger.fatal("Alert LoginFlow : FetchOrMakeAccountError. InvalidLoginChallenge ")
                Res.BadRequestError(err.getMessage)
              }
              case Left(FetchOrMakeAccountError.AccountNotActive) =>
                Logger.fatal("Alert LoginFlow : FetchOrMakeAccountError. AccountNotActive")
                Res.UnauthorizedError(
                  CONSTANTS.API_MSGS.ACCOUNT_DEACTIVATED_ERROR
                )

              case Left(FetchOrMakeAccountError.NoEmailFound) =>
                Logger.fatal("Alert LoginFlow : Google didn't send account. IMPOSSIBLE ERROR")
                Res.BadRequestError("Error while getting account from the host")

              case Left(FetchOrMakeAccountError.ErrorFindingAccount(err)) =>
                Logger.fatal("Alert LoginFlow : Error while finding account", err = err)
                Res.ServerError("Error while getting Account, Please contact support", Some(err))

              case Left(FetchOrMakeAccountError.SQLErrorWhileMakingAccount(err)) =>
                Logger.fatal("Alert LoginFlow : Error while Making the account", err = err)
                Res.ServerError("Error while getting Account, Please contact support", Some(err))

              case Left(FetchOrMakeAccountError.EmailNotValid(err)) =>
                err match {
                  case CheckIfEmailIsAllowedToSignUpError.DomainIsFree =>
                    Res.BadRequestError("Please use your work email.")

                  case CheckIfEmailIsAllowedToSignUpError.FailedToCheckIfDomainFree(err) =>
                    Logger.fatal(s"Couldn't check if domain in free or blocked list-- ${LogHelpers.getStackTraceAsString(err)}")
                    Res.ServerError("Sorry, there was an error while creating your account. Please try again or contact support.", None)

                }

              case Right(value) => value match {
                case data: AuthenticateResponseType.Enable2Fa => {
                  Res.Success("Enable 2FA!", Json.obj(
                    "code" -> data.code,
                    "aid" -> data.aid,

                    "verstate" -> data.verstate, // would be used to cross verify the verify_2fa api call
                    "default_country_code" -> data.default_country_code,
                    "disable_analytics" -> data.disable_analytics,
                    "redirect_uri" -> data.redirect_uri
                  ))
                }
                case data: AuthenticateResponseType.Verify2Fa => {
                  Res.Success("Verify 2FA!", Json.obj(
                    "code" -> data.code,
                    "aid" -> data.aid,

                    "verstate" -> data.verstate, // would be used to cross verify the verify_2fa api call
                    "two_fa_type" -> data.two_fa_type,
                    "disable_analytics" -> data.disable_analytics,
                    "redirect_uri" -> data.redirect_uri
                  ))
                }
                case data: AuthenticateResponseType.AccountFoundorCreated =>

                  val successRes = Json.obj(
                    "code" -> data.code,
                    "account" -> data.account,
                    "disable_analytics" -> data.disable_analytics,
                    "redirect_uri" -> data.redirect_uri,
                    "is_sign_up" -> data.is_sign_up /*
                     28 dec 2023 : FIXME IMMEDIATELY: adding this for temporary for not showing the Account Found Alert coming below in frontend
                    */

                  )

                  if (data.is_sign_up) {
                    Res.Success("Account Created Successfully!!, Please login", successRes)
                  } else {
                    Res.Success("Account Found !! ", successRes)

                  }

              }
            }.recover { case e => {
              Logger.fatal(s"An Error Occurred while  authentication ", err = e)
              Res.ServerError(message = "Please try again or contact support", Some(e))
            }}
        }


    }

  }

  def findLoginType: Action[JsValue] = Action.async(parse.json) { request =>
    val logRequestId = s"${StringUtils.genLogTraceId} NewAuthController.findLoginType: "
    given Logger: SRLogger= new SRLogger(logRequestId = logRequestId)
    val Res = new SRAPIResponse(Logger = Logger)
    val validateData = request.body.validate[SignupCheckData]
    val userIp = Helpers.getClientIPFromGCPLoadbalancer(request)
      .getOrElse("0.0.0.0")
    validateData match {
      case JsError(err) => Future.successful(Res.JsValidationError(err))
      case JsSuccess(value, _) =>
        val data = RecaptchaData(
          requestIp = userIp,
          actionType = RecaptchaActionType.Login,
          response = value.g_response
        )
        sRGoogleRecaptchaServices.checkIfWeShowCaptchaOrVerifyIt(data) map {
          case Left(ReCaptchaError.VerifyCaptchaError(err)) =>

            Logger.fatal(s"Error while verifying captcha ---- $err") //error not of throwable type
            Res.ServerError("error while verifying the captcha", None)

          case Left(ReCaptchaError.NoGResponseSentError) =>

            if (Helpers.checkIfCypressTestingEmailAccount(value.accountEmail)) {
              Res.Success("found type of signup", Json.obj("signupType" -> SignupType.Password.toString, "showCaptcha" -> false, "passedCaptcha" -> true))
            } else {
              Res.Success("", Json.obj("showCaptcha" -> true, "passedCaptcha" -> false))
            }


          case Left(ReCaptchaError.ShowCaptchaError(err)) =>
            err match {
              case CheckIfWeShowCaptchaError.RedisError(err) =>

                Logger.fatal("Error from redis", err = err)
                Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, None)

              case CheckIfWeShowCaptchaError.CouldnotCreateRedisKeyError(err) =>

                Logger.fatal(s"Wrong reply from redis expected OK got  - $err") //error is string
                Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, None)

            }

          case Left(ReCaptchaError.TestingEmailShouldNotHaveGResponse(errStr)) =>
            Logger.fatal(s"IMPOSSIBLE ERROR We are not having g_response for testing email in this flow- $errStr")
            Res.BadRequestError("Sorry please try again")

          case Right(recaptchaResponse) =>
            newAuthFlowService.checkSignupTypeForLogin(email = value.accountEmail) match {

              case Left(CheckIfAllowedToLoginError.SQLErrorWhileGettingSignupType(err)) =>

                Logger.fatal(s"SQl Error while getting signup type for login", err = err)
                Res.ServerError("Please contact the support team", Some(err))

              case Left(CheckIfAllowedToLoginError.NoAccountFound) =>

                //This error msg is to prevent Harvesting
                //            Res.BadRequestError("Username or password is incorrect")
                Res.Success("found type of signup", Json.obj("signupType" -> SignupType.Password.toString, "showCaptcha" -> recaptchaResponse.showCaptcha, "passedCaptcha" -> recaptchaResponse.passedCaptcha))

              case Right(signupType) =>


                Res.Success("found type of signup", Json.obj("signupType" -> signupType.toString, "showCaptcha" -> recaptchaResponse.showCaptcha, "passedCaptcha" -> recaptchaResponse.passedCaptcha))
            }
        }
    }

  }


  /**
   * The below controller function is used in the 2fa flow During Oauth  flow with microsoft/google to get the login challenge.
   * The login challenge is used to accept the login request which then redirects user to consent screen
   *
   * @return
   */

  def getLoginChallengeFromState = Action.async(parse.json) { request =>

    val logRequestId = s"${StringUtils.genLogTraceId} NewAuthController.getLoginChallengeFromState: "
    given Logger: SRLogger= new SRLogger(logRequestId = logRequestId)
    val Res = new SRAPIResponse(Logger = Logger)

    val stateOpt = (request.body \ "state").asOpt[String]

    if (stateOpt.isEmpty) {
      Future.successful(Res.BadRequestError("State Parameter not provided"))
    } else {
      val loginChallengeOpt = newAuthFlowService.decodeOauthState(state = stateOpt.get, logger = Logger).loginChallenge
      if (loginChallengeOpt.isDefined) {
        Future.successful(Res.Success("Found Login Challenge", Json.obj("login_challenge" -> loginChallengeOpt.get.challenge)))
      } else {
        Future.successful(Res.Success("Login Challenge Not Found", Json.obj()))
      }
    }

  }

}
