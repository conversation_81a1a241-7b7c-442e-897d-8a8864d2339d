package api.accounts.dao

import api.accounts.{OrganizationRole, TeamAccountRole, TeamId}
import api.accounts.models.{AccountId, AccountStatus, AccountV2, OrgId, SignupType}
import api.accounts.sr_api_key_type.models.SRApiKeyType
import api.internal_support.service.UpdateEmail
import org.joda.time.DateTime
import scalikejdbc.{DB, DBSession, scalikejdbcSQLInterpolationImplicitDef}

import scala.util.Try

case class AccountAndTeamData(
                             team_name: String,
                             team_id: TeamId,
                             email: String,
                             account_id: String,
                             first_name: String,
                             last_name: String
                           )
class AccountDAOV2(
                 organizationDAO: OrganizationDAO
                 ){
  def getAccountAndTeamData(
                           accountId: AccountId,
                           teamId: TeamId
                         ): Try[AccountAndTeamData] = Try{
    DB readOnly {implicit session =>
      sql"""
            select t.name as team_name,
                   t.id as team_id,
                   a.email as email,
                   a.id as account_id,
                   a.first_name as first_name,
                   a.last_name as last_name
            from teams_accounts ta
            inner join accounts a on a.id = ta.account_id
            inner join teams t on t.id = ta.team_id
            where ta.account_id = ${accountId.id}
            and ta.team_id = ${teamId.id}
       """
        .map(rs => AccountAndTeamData(
          team_name = rs.string("team_name"),
          team_id = TeamId(rs.long("team_id")),
          email = rs.string("email"),
          account_id = rs.string("account_id"),
          first_name = rs.string("first_name"),
          last_name = rs.string("last_name")
        ))
        .single
        .apply()
        .get

    }

  }

  def getAccountByEmail(
                         email: String
                       ): Try[Option[AccountV2]] = Try{
    DB readOnly{ implicit  session => {

      sql"""
         SELECT accounts.id,
                 accounts.email,
                 first_name,
                 last_name,
                 organizations.name as company,
                 org_id,
                 org_role,
                 accounts.active,
                 account_status ,
                 accounts.scheduled_for_deletion_at
         FROM accounts
         INNER JOIN organizations ON accounts.org_id = organizations.id
         WHERE lower(email) = ${email.toLowerCase().trim};
       """
        .map(AccountV2.getFromDB)
        .single
        .apply()}
    }
  }
  private def updateTeamAccountRole(
                                     account: AccountV2,
                                     orgRole: TeamAccountRole,
                                     newRoleId: Long
                           )(implicit session: DBSession): Int = {


    sql"""
            UPDATE
              teams_accounts
            SET
              role = ${orgRole.toString},
              user_role_id = $newRoleId
            WHERE
              account_id = ${account.id}
            """
            .update
            .apply()
  }

   private def updateAccountOrgRole(
                             orgRole: Option[OrganizationRole],
                             account: AccountV2
                           )(implicit session: DBSession): Int = {

     sql"""UPDATE accounts
           SET org_role = ${orgRole.map(_.toString)}
           WHERE
           id = ${account.id} AND
            org_id = ${account.org_id}"""
       .update
       .apply()
   }

  private def addingNewOwnerToAllTheTeams(
                                           orgId: OrgId,
                                           accountId: AccountId,
                                           userRoleId: Long,
                                           roleName: TeamAccountRole
                                         )(implicit session: DBSession): List[Long] = {

    val teams =
      sql"""
         select t.id
         from teams t
         where t.org_id = ${orgId.id}
         and NOT exists(
         select *
         from teams_accounts ta
         where
         ta.team_id = t.id
         and ta.account_id = ${accountId.id}
         )
       """
        .map(rs => TeamId(rs.long("id")))
        .list
        .apply()
    teams.map { teamId =>
      val extensionKey = SRApiKeyType.genApiKey(keyType = SRApiKeyType.SRTeamUserLevelKey)
      sql"""
          INSERT INTO teams_accounts
          (team_id, account_id, role, user_role_id, extension_key)
          VALUES (${teamId.id}, ${accountId.id}, ${roleName.toString}, $userRoleId, $extensionKey)
          RETURNING id;
      """
        .map(rs => rs.long("id"))
        .single
        .apply()
        .get
    }
  }

   def updateOwner(
                    oldOwnerAccount:AccountV2,
                    newOwnerAccount: AccountV2,
                    adminRoleId: Long,
                    ownerRoleId: Long
                  ): Try[Int] = Try{

     DB.localTx { implicit session =>

       updateAccountOrgRole(
         account = oldOwnerAccount,
         orgRole = None
       )
       addingNewOwnerToAllTheTeams(
         orgId = OrgId(newOwnerAccount.org_id),
         accountId = AccountId(newOwnerAccount.id),
         userRoleId = adminRoleId,
         roleName = TeamAccountRole.ADMIN
       ) //Adding the new owner as admin to all the teams in which the new account is not part of.

       updateAccountOrgRole(
         account = newOwnerAccount,
         orgRole = Some(OrganizationRole.OWNER)
       )

       updateTeamAccountRole(
         account = oldOwnerAccount,
         orgRole = TeamAccountRole.ADMIN,
         newRoleId = adminRoleId
       )

       updateTeamAccountRole(
         account = newOwnerAccount,
         orgRole = TeamAccountRole.OWNER,
         newRoleId = ownerRoleId
       )
       organizationDAO.updateOrgOwner(
         newOwnerId = newOwnerAccount.id,
         orgId = newOwnerAccount.org_id
       ).get
     }

   }

  def scheduleForDeletion (
                            email: String,
                            scheduled_for_deletion_at: DateTime
                          ): Try[Long] = Try{
    DB.localTx{implicit session =>

      sql"""
             UPDATE accounts
          SET
             account_status = ${AccountStatus.ScheduledForDeletion.toString},
             scheduled_for_deletion_at = $scheduled_for_deletion_at,
             active = false
          WHERE lower(email) = ${email.trim.toLowerCase}
          RETURNING id;
         """
        .map(rs => rs.long("id"))
        .single
        .apply()
        .get
    }
  }

  def getOneScheduledAccountForDeletion: Try[Option[AccountV2]] = Try {
    DB readOnly{implicit session =>

      sql"""
         SELECT id,
                 email,
                 first_name,
                 last_name,
                 company,
                 org_id,
                 org_role,
                 active,
                 account_status,
                 scheduled_for_deletion_at
         FROM accounts
         WHERE account_status =  ${AccountStatus.ScheduledForDeletion.toString} AND
         scheduled_for_deletion_at < now()
         LIMIT 1
       """
        .map( AccountV2.getFromDB)
        .single
        .apply()}

    }


  def deleteAccount(
                     accountId: Long,
                     orgId: Long
                   ): Try[Int] = Try{

    DB.localTx{implicit session =>
      sql"""DELETE FROM teams_accounts ta
           WHERE ta.account_id = $accountId"""
        .update
        .apply()
      
      sql"""
         DELETE FROM accounts
         WHERE id = $accountId AND
         org_id = $orgId AND
         account_status =  ${AccountStatus.ScheduledForDeletion.toString}
       """
        .update
        .apply()
    }

  }

  def getAdminAccountByOrgId(
                              org_id: Long
                            ): Try[Option[AccountV2]] = Try{
    DB readOnly{ implicit  session => {

      sql"""
         SELECT id,
                 email,
                 first_name,
                 last_name,
                 company,
                 org_id,
                 org_role,
                 active,
                 account_status ,
                 scheduled_for_deletion_at
         FROM accounts
         WHERE org_id = $org_id AND
         org_role = ${OrganizationRole.OWNER.toString}
       """
        .map(AccountV2.getFromDB)
        .single
        .apply()}
    }
  }

  def updateEmailAndChangeSignupTypeToPassword(updateEmail: UpdateEmail): Try[Int] = Try {
    DB.localTx{ implicit session => {
      sql"""
           UPDATE accounts
           SET email = ${updateEmail.newEmail.toLowerCase().trim},
           signup_type = ${SignupType.Password.toString}
           WHERE lower(email) = ${updateEmail.oldEmail.toLowerCase().trim};
         """
        .update
        .apply()
    }
    }
  }


  def updateSignupType(email: String, signupType: SignupType): Try[Int] = Try {
    DB.localTx{ implicit session => {
      sql"""
           UPDATE accounts
           SET
           signup_type = ${signupType.toString}
           WHERE lower(email) = ${email.toLowerCase().trim};
         """
        .update
        .apply()
    }
    }
  }
}
