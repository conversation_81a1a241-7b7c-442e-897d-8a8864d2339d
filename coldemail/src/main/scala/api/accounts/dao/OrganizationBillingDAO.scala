package api.accounts.dao

import api.AppConfig
import api.accounts.models.{AccountId, OrgId}
import api.accounts.service.ConsumedLeadFinderCredits
import api.accounts.{Organization, ResetBillingMonthlyCycleForOrg, TeamId}
import io.sr.billing_common.models.{BillingSubscriptionStatus, PlanID, PlanType, SrPlanLimits}
import api.billing.PaymentGateway
import api.emails.EmailSettingDAO
import api.lead_finder.models.{LeadFinderCreditDescription, SmartreachCreditType}
import org.joda.time.DateTime
import play.api.libs.json.Json
import scalikejdbc.{AutoSession, DB, SQLSyntax, scalikejdbcSQLInterpolationImplicitDef}
import utils.payment.api_services.PGPlanInterval
import utils.testapp.Test_TaskPgDAO.srUuidUtils

import scala.util.Try

case class OrgPlanDetail(
  orgId: Long,
  orgName: String,
  firstPromoterLeadExists: <PERSON><PERSON><PERSON>,
  fpReferrerTrackId: Option[String],
  stripe_customer_id: Option[String],
  fs_account_id: Option[String],
  planType: PlanType,
  planInterval: Option[PGPlanInterval.Value],
  admitad_uid: Option[String],
  admitad_gclid: Option[String]
)

/*
7 Jan 2021: these fns have been cut-pasted from the existing AccountDAO
to reduce the scope of AccountDAO and also remove the EmailSettingDAO dependency from that class
 */
class OrganizationBillingDAO(
  emailSettingDAO: EmailSettingDAO
) {

  private implicit val session: AutoSession.type = AutoSession




  // FIXME FREE PLAN
  def resetBillingMonthlyCycle(
    updateSpecificOrg: Option[ResetBillingMonthlyCycleForOrg]
  ): Try[Seq[Organization]] = Try {

    val resetBillingCycleIntervalInMonths = AppConfig.resetBillingCycleIntervalInMonths

    DB localTx { implicit session =>

      // var currentCycleStartedAt = DateTime.now()
      val whereClause: SQLSyntax = if (updateSpecificOrg.isDefined) {
        // case: webhook update subscription called, or new subscription added after trial

        val data = updateSpecificOrg.get
        // currentCycleStartedAt = data.currentCycleStartedAt

        sqls"""
          WHERE id = ${data.orgId.id}
        """

      } else {
        // case: force reset monthly cycle for free accounts, or if payment still due, called from the cron service

        sqls"""
          WHERE current_cycle_started_at <= now() - interval '${SQLSyntax.createUnsafely(resetBillingCycleIntervalInMonths.toString)} months'
          AND (plan_type = 'paid' OR plan_type = 'free')
        """

      }

      val updateCycleStartedAtSql = updateSpecificOrg match {

        case None =>

          // This case handles when the function is called by the hourly cron service
          // to reset the usage limits for organizations at the end of users monthly billing cycle.

          /**
            * 17 Apr 2025
            *
            * Previously, we were setting `current_cycle_started_at` to the current timestamp (`DateTime.now()`).
            * However, this approach caused the `current_cycle_started_at` value to drift forward over time,
            * leading to issues especially for annual users where some customers reported that their billing
            * cycle hadn’t been reset as expected.
            *
            * To address this, we now derive the new `current_cycle_started_at` by adding one month
            * to its existing value. This ensures that the billing cycle remains aligned with the original
            * payment date.
            *
            * We also considered setting `current_cycle_started_at` to the subscription start date from
            * payment gateway during the subscription update flow. However, we identified an edge case
            * with this approach.
            *
            * For annual users, if they update their subscription after the first month,
            * `current_cycle_started_at` would be reset to the original subscription start date.
            * As a result, the hourly reset cron would detect that it's been more than a month
            * since the last reset for this organization and incorrectly reset the consumed credits.
            */

          sqls"""
                current_cycle_started_at = current_cycle_started_at + interval '${SQLSyntax.createUnsafely(resetBillingCycleIntervalInMonths.toString)} months'
              """

        case Some(data) =>

          // This case only happens when a new subscription is created.

          sqls"""
                current_cycle_started_at = ${data.currentCycleStartedAt}
              """

      }

      /*
     --active = true,
     --deactivation_message = null,
     --deactivated_at = null,
     */
      sql"""
          UPDATE organizations
          SET
            plan_prospects_contacted_current = 0,
            plan_lead_finder_credits_curr = 0,
            error = null,
            error_reported_at = null,
            paused_till = null,
            error_code = null,
            new_prospects_paused_till = null,
            warning_msg = null,
            warning_at = null,
            warning_code = null,
            $updateCycleStartedAtSql

          $whereClause
          RETURNING *;
        """
        .map(Organization.fromDb)
        .list
        .apply()
        .map(org => {
          emailSettingDAO.resetSentCountAtEndOfMonthlyCycle(orgIds = Seq(org.id)).get

          org
        })
    }

  }

  def getAffiliateCustomers(): Try[List[Long]] = Try {
    DB.readOnly { implicit session =>
      sql"""
          SELECT id
          FROM organizations
          WHERE (firstpromoter_lead IS NOT NULL OR admitad_lead is NOT NULL)
          AND plan_type = 'paid';
         """
        .map(rs => {
          rs.long("id")
        })
        .list
        .apply()
    }
  }

  def getRemainingBasePlanLeadFinderCredits(
    orgId: OrgId
  ): Try[Long] = Try {

    DB readOnly { implicit session =>

      sql"""
          SELECT
            plan_lead_finder_credits_max,
            plan_lead_finder_credits_curr
          FROM
            organizations
          WHERE
            id = ${orgId.id};
         """
        .map { rs =>
          rs.long("plan_lead_finder_credits_max") -
            rs.long("plan_lead_finder_credits_curr")
        }
        .single
        .apply()
        .get

    }

  }

  def addLeadFinderCredits(orgId: OrgId, credits: Long): Try[Int] = Try {
    DB autoCommit {implicit session =>
      sql"""
           UPDATE organizations
           SET lead_finder_credits = lead_finder_credits + $credits
           WHERE id = ${orgId.id}
           ;
           """
        .update
        .apply()
    }
  }

  def addLeadFinderLog(
                        orgId: OrgId,
                        accountId: AccountId,
                        teamId: TeamId,
                        creditsUsed: Int,
                        leadFinderCreditDescription: LeadFinderCreditDescription,
                        creditUsageType: SmartreachCreditType
                      ): Try[Long] = Try {
    val uuid = srUuidUtils.generateLeadFinderBillingLogsUuid()

    DB.autoCommit { implicit session =>
      sql"""
            INSERT INTO lead_finder_billing_logs (
              uuid,
              org_id,
              team_id,
              account_id,
              added_at,
              credits_used,
              bill_description,
              usage_type
            ) VALUES (
              $uuid,
              ${orgId.id},
              ${teamId.id},
              ${accountId.id},
              ${DateTime.now()},
              $creditsUsed,
              to_json(${Json.toJson(leadFinderCreditDescription).toString()}::jsonb),
              ${creditUsageType.toString}
            )
            RETURNING id;
          """
        .map(rs => rs.long("id"))
        .single
        .apply()
        .get
    }

  }


  def subtractLeadFinderCredits(
    orgId: OrgId,
    consumedLeadFinderCredits: ConsumedLeadFinderCredits)
  : Try[Int] = Try {

    DB autoCommit { implicit session =>

      sql"""
          UPDATE
            organizations
          SET
            lead_finder_credits = GREATEST(0, lead_finder_credits - ${consumedLeadFinderCredits.consumed_from_purchased_credits}),
            plan_lead_finder_credits_curr = plan_lead_finder_credits_curr + ${consumedLeadFinderCredits.consumed_from_base_plan_credits}
          WHERE
            id = ${orgId.id};
         """
        .update
        .apply()

    }
  }


  def updateSubscription(
    pgCustomerId: String,
    pgSubscriptionId: Option[String],
    planLimits: SrPlanLimits,

    sr_plan_name: String,

    planType: PlanType,
    nextBillingDate: Option[DateTime],
    paymentGateway: PaymentGateway.Value,
    cancelledAt: Option[DateTime],
    defaultLeadFinderCredits: Int,
    maxManualLinkedinAccounts: Int,
  ): Try[Option[Organization]] = Try {

    val (setSubscriptionIdClause: SQLSyntax, whereClause: SQLSyntax) = paymentGateway match {

      case PaymentGateway.FASTSPRING =>
        (
          sqls" fs_subscription_id = $pgSubscriptionId, ",

          sqls" fs_account_id = $pgCustomerId "
        )

      case PaymentGateway.STRIPE =>
        (
          // we are not storing stripe subscription id as of now
          sqls"",

          sqls" stripe_customer_id = $pgCustomerId "
        )

    }


    DB autoCommit { implicit session =>

      sql"""
          UPDATE organizations
          SET
            plan_type = ${planType.toString},

            plan_name = ${sr_plan_name},
            base_licence_count = ${planLimits.base_licence_count},
            additional_licence_count = ${planLimits.additional_licence_count},

            $setSubscriptionIdClause

            total_sending_email_accounts = ${planLimits.total_sending_email_account},
            plan_prospects_contacted_max = ${planLimits.max_prospects_contacted},
            plan_li_manual_seats_max = $maxManualLinkedinAccounts,
            plan_li_automation_seats_max = ${planLimits.max_automated_linkedin_accounts},
            plan_client_teams_max = ${planLimits.max_client_teams},
            plan_crm_integrations_max = ${planLimits.max_crm_integrations},
            plan_calling_seats_max = ${planLimits.max_calling_seats},
            plan_prospects_saved_max = ${planLimits.max_prospects_saved},

            plan_lead_finder_credits_max = $defaultLeadFinderCredits,

            plan_purchased_domains_max = ${planLimits.max_purchased_domains},
            plan_purchased_email_accounts_max = ${planLimits.max_purchased_email_accounts},

            plan_purchased_zapmail_domains_max = ${planLimits.max_purchased_zapmail_domains},
            plan_purchased_zapmail_email_accounts_max = ${planLimits.max_purchased_zapmail_email_accounts},

            next_billing_date = $nextBillingDate,
            payment_gateway = ${if (cancelledAt.isDefined) None else Some(paymentGateway.toString)},

            error = null,
            error_reported_at = null,
            paused_till = null,
            error_code = null,
            new_prospects_paused_till = null,
            warning_msg = null,
            warning_at = null,
            warning_code = null,

            payment_due_invoice_link = null,
            payment_due_campaign_pause_at = null,
            payment_failed_at = null,

            cancelled_at = COALESCE(cancelled_at, $cancelledAt)

          WHERE $whereClause
          RETURNING *;
      """
        .map(Organization.fromDb)
        .single
        .apply()
    }
  }

  /*
  def addStripeSubscription(
    pgCustomerId: String,

    sr_plan_name: String,
    sr_pricing_plan_id: Option[Int],
    base_licence_count: Int,
    additional_licence_count: Int,

    totalSendingEmailAccounts: Int,
    nextBillingDate: DateTime,
    currentCycleStartedAt: DateTime

  ): Try[Option[Organization]] = Try {

    DB localTx { implicit session =>

      updateSubscription(
        pgCustomerId = pgCustomerId,
        pgSubscriptionId = None, // its anyways not saved for stripe

        sr_plan_name = sr_plan_name,
        sr_pricing_plan_id = sr_pricing_plan_id,
        base_licence_count = base_licence_count,
        additional_licence_count = additional_licence_count,

        planType = PlanType.PAID,
        nextBillingDate = Some(nextBillingDate),
        cancelledAt = None,
        totalSendingEmailAccounts = totalSendingEmailAccounts,
        paymentGateway = PaymentGateway.STRIPE
      )
        .map(orgOpt => {
          orgOpt.map(org => {

            resetBillingMonthlyCycle(
              updateSpecificOrg = Some(ResetBillingMonthlyCycleForOrg(
                orgId = org.id,
                currentCycleStartedAt = currentCycleStartedAt
              ))
            ).map(_.head).get
          })

        })
    }
  }.flatten
  */


  def addSubscription(
    paymentGateway: PaymentGateway.Value,
    pgCustomerId: String,
    pgSubscriptionId: String,
    sr_plan_name: String,
    planLimits: SrPlanLimits,

    currentCycleStartedAt: DateTime,
    nextBillingDate: DateTime,

    defaultLeadFinderCredits: Int,

    maxManualLinkedinAccounts: Int,

  ): Try[Option[Organization]] = Try {

    DB localTx { implicit session =>

      updateSubscription(
        pgCustomerId = pgCustomerId,
        pgSubscriptionId = Some(pgSubscriptionId),

        sr_plan_name = sr_plan_name,
        planLimits = planLimits,

        planType = PlanType.PAID,
        cancelledAt = None,
        nextBillingDate = Some(nextBillingDate),
        paymentGateway = paymentGateway,
        defaultLeadFinderCredits = defaultLeadFinderCredits,
        maxManualLinkedinAccounts = maxManualLinkedinAccounts,
      )
        .map(orgOpt => {

          orgOpt.map(org => {
            resetBillingMonthlyCycle(
              updateSpecificOrg = Some(ResetBillingMonthlyCycleForOrg(
                orgId = OrgId(id = org.id), // FIXME VALUECLASS
                currentCycleStartedAt = currentCycleStartedAt
              ))
            ).map(_.head).get
          })

        })

    }

  }.flatten


  def updateOrgFSAccountIdAndLookupId(orgId: Long, fsAccountId: String, fsLookupId: String): Try[Option[Organization]] = Try {
    DB autoCommit { implicit session =>

      sql"""
          UPDATE organizations
          SET
            fs_account_id = $fsAccountId,
            fs_lookup_id = $fsLookupId
          WHERE id = $orgId
          RETURNING *;
      """
        .map(Organization.fromDb)
        .single
        .apply()

    }

  }


  def updateOrgStripeCustomerId(orgId: Long, stripeCustomerId: String): Try[Option[Organization]] = Try {
    DB autoCommit { implicit session =>

      sql"""
          UPDATE organizations
          SET
            stripe_customer_id = $stripeCustomerId
          WHERE id = $orgId
          RETURNING *;
      """
        .map(Organization.fromDb)
        .single
        .apply()

    }

  }



  def setPaymentDueInvoiceLink(
    paymentGateway: PaymentGateway.Value,
    pgCustomerId: String,
    data: BillingSubscriptionStatus.PaymentDue,
    invoiceLink: String
  ): Try[Option[Organization]] = Try {

    val wherePGCustomerIdClause: SQLSyntax = paymentGateway match {

      case PaymentGateway.FASTSPRING => sqls" org.fs_account_id = $pgCustomerId "

      case PaymentGateway.STRIPE => sqls" org.stripe_customer_id = $pgCustomerId "

      case PaymentGateway.RAZORPAY |
           PaymentGateway.OTHER =>

        throw new Exception(s"pg not supported: $paymentGateway")
    }

    DB autoCommit { implicit session =>
      sql"""
          UPDATE organizations org
          SET
            payment_due_invoice_link = $invoiceLink,
            payment_failed_at = ${data.payment_due_since},
            payment_due_campaign_pause_at = ${data.payment_due_campaign_pause_at}
          WHERE $wherePGCustomerIdClause

            -- dont show payment link when subscription is already cancelled / inactive
            AND plan_type != ${PlanType.INACTIVE.toString}

            RETURNING *
          ;
      """
        .map(Organization.fromDb)
        .single
        .apply()

    }

  }

  /* 8 Sep 2023 - cleaning up old billing flow
  def updateCancellationFeedback(
    orgId: Long,
    cancellationReason: String
  ): Try[Option[Organization]] = Try {

    DB autoCommit { implicit session =>
      sql"""
          UPDATE organizations
          SET
            cancellation_reason = $cancellationReason,
            cancelled_at = COALESCE(cancelled_at, ${DateTime.now})
          WHERE id = $orgId
          RETURNING *;
      """
        .map(Organization.fromDb)
        .single
        .apply()

    }

  }
  */

  def getOrgDetailsForNewBillingFlow(
    orgId: Long

  ): Try[Option[OrgPlanDetail]] = Try {

    DB.readOnly { implicit session =>

      sql"""
        SELECT
          org.id AS orgId,
          org.name AS orgName,
          org.fs_account_id,
          org.stripe_customer_id,
          org.plan_name,
          (org.firstpromoter_lead IS NOT NULL) AS firstPromoterLeadExists,
          org.fp_referrer_track_id,
          org.admitad_lead,
          org.admitad_gclid
        FROM
          organizations org
        WHERE
          org.id = $orgId
       """
        .map(rs => {

          val planName = rs.string("plan_name")
          val planId = PlanID._getPlanId(planname = planName)
          val planType = PlanID._getPlanType(planID = planId)

          OrgPlanDetail(
            orgId = rs.long("orgId"),
            firstPromoterLeadExists = rs.boolean("firstPromoterLeadExists"),
            fpReferrerTrackId = rs.stringOpt("fp_referrer_track_id"),
            orgName = rs.string("orgName"),
            fs_account_id = rs.stringOpt("fs_account_id"),
            stripe_customer_id = rs.stringOpt("stripe_customer_id"),
            planType = planType,
            planInterval = PGPlanInterval.getPlanInterval(planNameInDB = planName),
            admitad_uid = rs.stringOpt("admitad_lead"),
            admitad_gclid = rs.stringOpt("admitad_gclid")
          )
        })
        .single
        .apply()

    }

  }

  def getOrgDetailByPgCustomerId(

    pgCustomerId: String,
    pg: PaymentGateway.Value

  ): Try[Option[OrgPlanDetail]] = Try {

    DB readOnly {implicit session =>

      val wherePGCustomerIdClause: SQLSyntax = pg match {

        case PaymentGateway.FASTSPRING => sqls" org.fs_account_id = $pgCustomerId "

        case PaymentGateway.STRIPE => sqls" org.stripe_customer_id = $pgCustomerId "

        case PaymentGateway.RAZORPAY |
             PaymentGateway.OTHER =>

          throw new Exception(s"pg not supported: $pg")
      }

      sql"""
        SELECT
          org.id AS orgId,
          org.name AS orgName,
          org.fs_account_id,
          org.stripe_customer_id,
          org.plan_name,
          (org.firstpromoter_lead IS NOT NULL) AS firstPromoterLeadExists,
          org.fp_referrer_track_id,
          org.admitad_lead,
          org.admitad_gclid
        FROM
          organizations org
        WHERE $wherePGCustomerIdClause

       """
        .map(rs => {

          val planName = rs.string("plan_name")

          val planId = PlanID._getPlanId(planname = planName)
          val planType = PlanID._getPlanType(planID = planId)

          OrgPlanDetail(
            orgId = rs.long("orgId"),
            firstPromoterLeadExists = rs.boolean("firstPromoterLeadExists"),
            fpReferrerTrackId = rs.stringOpt("fp_referrer_track_id"),
            orgName = rs.string("orgName"),
            fs_account_id = rs.stringOpt("fs_account_id"),
            stripe_customer_id = rs.stringOpt("stripe_customer_id"),
            planType = planType,
            planInterval = PGPlanInterval.getPlanInterval(planNameInDB = planName),
            admitad_uid = rs.stringOpt("admitad_lead"),
            admitad_gclid = rs.stringOpt("admitad_gclid")
          )
        })
        .single
        .apply()

    }
  }


}
