package api.accounts

import api_layer_models.CurrencyType
import api.call.models.TwilioSubAccountStatus
import play.api.libs.json.{Json, OFormat}

case class CreditsObject(
                          call_credits_remaining: BigInt,
                          twl_trigger_current_value: Option[BigInt], //If the account gets suspended, then only twl_trigger_current_value will have a value. Otherwise it will be null. This is based on the logic in the update credits cron.
                          credit_unit: CurrencyType,
                          call_credits: BigInt,
                          twl_sub_account_status: TwilioSubAccountStatus
                        )


object CreditsObject {
  given formats: OFormat[CreditsObject] = Json.format[CreditsObject]
}
