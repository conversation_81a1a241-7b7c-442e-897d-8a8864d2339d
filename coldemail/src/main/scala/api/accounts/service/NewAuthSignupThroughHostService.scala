package api.accounts.service

import utils.SRLogger

import scala.concurrent.Future
import scala.util.Try

trait NewAuthSignupThroughHostService {

  def authorizationUrl(
                        state: String,
                        emailAddress: String,
                        loginChallenge: Option[String] = None
                      )(using Logger: SRLogger): Future[String]

  def fetchProfileDataUsingAuthCode(
                                     code: String,
                                     emailAddress: Option[String]
                                   )(using Logger: SRLogger): Future[ProfileForSignup]
}
