package api.accounts.service

import api.accounts.Account

sealed trait AccountCreationResponse


object  AccountCreationResponse {

  case class InviteFlowAccountCreationResponse(
                                                code :String,
                                                account : Account,
                                                disable_analytics: <PERSON><PERSON><PERSON>,
                                                attemptNumber: Int,
                                                redirect_to: String
                                              ) extends AccountCreationResponse

  case class RegistrationFlowAccountCreationResponse(
                                                      disable_analytics: <PERSON>olean,
                                                      attemptNumber: Int
                                                     ) extends AccountCreationResponse
  case class ExistingAccountResponse(
                                       disable_analytics: <PERSON>olean,
                                       attemptNumber: Int
                                     ) extends AccountCreationResponse
}
