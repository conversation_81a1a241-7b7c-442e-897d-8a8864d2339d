package api.accounts.service
import api.AppConfig
import api.accounts.GoogleOAuth
import api.accounts.models.SignupType
import api.emails.EmailSettingDAO
import play.api.libs.ws.WSClient
import utils.{SRLogger, StringUtils}

import scala.concurrent.impl.Promise
import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

class NewAuthGoogleSignupService(
                                  googleOAuth: GoogleOAuth,
                                  emailSettingDAO: EmailSettingDAO,
                                  implicit val ws: WSClient,
                                  implicit val ec: ExecutionContext

                                ) extends NewAuthSignupThroughHostService{
  override def authorizationUrl(
                                 state: String,
                                 emailAddress: String,
                                 loginChallenge: Option[String] = None
                               )(using Logger: SRLogger): Future[String] = {
    val googleApiKeyTry = emailSettingDAO.getGoogleAuthKeysForSignup(emailAddress = emailAddress)
    Logger.info(s"State is ${state}")
      Future.fromTry(googleApiKeyTry.map { googleApiKeyId =>
        googleOAuth.authorizationUrl(
          state = if(loginChallenge.isDefined) {s"${loginChallenge.get}*${state}"} else state,
          emailAddress = Some(emailAddress),
          gcpClientId = googleApiKeyId.cl_id,
          goauthSettings = AppConfig.googleOAuthSettingsForSignupFlow,
          forceApproval = false
        )
      })
  }

  override def fetchProfileDataUsingAuthCode(
                                              code: String,
                                              emailAddress: Option[String]
                                            )(using Logger: SRLogger): Future[ProfileForSignup] = {
    //TODO_GOOGLE_NEW_API_KEY this check should go away when google app is approved and started migrating the users
    val googleAuthApiKeyId = if (emailAddress.isDefined && AppConfig.allowedEmailAddressForNewGoogleAPIKeys.contains(emailAddress.get))
      AppConfig.googleAuthApiKeyIdV2
    else
      AppConfig.googleAuthApiKeyIdForNewAuthFlow

    val result = for {
      accessToken <-
        googleOAuth.getAccessToken(
          code = code,
          googleAuthApiKeyId = googleAuthApiKeyId,
          signupType = Some(SignupType.Google),
          isZapmailOauthFlow = false
        )

      profile <-
        googleOAuth.getProfileName(
          accessToken = accessToken.access_token,
          Logger = Logger
        )

    } yield {

      ProfileForSignup(
        profileName = profile.profileName,
        givenName = profile.givenName,
        familyName = profile.familyName,
        email = profile.email
      )

    }

    result
  }
}
