package api.accounts.service

import api.accounts.{AccountBillingData, AccountService, AccountWarningCodeType, AccountWarningObjectApi, OrgPlan, OrganizationWithCurrentData, TeamId}
import api.accounts.dao.AccountOrgBillingRelatedInfoDAO
import api.accounts.dao_service.OrganizationDAOService
import api.accounts.models.{AccountId, OrgId}
import api.prospects.models.{ProspectId, ProspectTouchedType, ProspectsMetadataUpsert, TouchIncrementReason, TouchedIncrementReason}
import io.sr.billing_common.models.{AddonLicenceType, PlanID, SrBillingPlanName, SrPlanVersion}
import api.prospects.dao_service.ProspectDAOService
import eventframework.ProspectObject
import org.joda.time.DateTime
import play.api.libs.ws.WSClient
import sr_scheduler.models.ChannelType
import utils.dbutils.{DBUtils, DbAndSession}
import utils.{PlanLimitService, SRLogger}
import utils.email_notification.service.{EmailNotificationDelayNotPassedException, EmailNotificationService}
import utils.helpers.LogHelpers

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success, Try}

class AccountOrgBillingRelatedService (
                                        accountOrgBillingRelatedInfoDAO: AccountOrgBillingRelatedInfoDAO,
                                        organizationDAOService: OrganizationDAOService,
                                        prospectDAOService: ProspectDAOService,
                                        planLimitService: PlanLimitService,
                                        dbUtils: DBUtils,
) {

  def incrementProspectsContactedCount(
    orgId: OrgId,
    teamId: TeamId,
    prospectId: ProspectId,
    prospectEmailOpt: Option[String],
    prospectFirstNameOpt: Option[String],
    prospectLastNameOpt: Option[String],
    touchIncrementReason: TouchedIncrementReason,
    channelTypeOpt: Option[ChannelType],
  )(using logger: SRLogger): Try[Int] = {

    val dbAndSession: DbAndSession = dbUtils.startLocalTx()

    val updateCount = for {

      res: Int <- accountOrgBillingRelatedInfoDAO.incrementProspectsContactedCount(
        orgId = orgId,
      )(session = dbAndSession.session)

      _: Int <- accountOrgBillingRelatedInfoDAO.addBillingProspectsTouchedLog(
        orgId = orgId,
        teamId = teamId,
        prospectId = prospectId,
        prospectEmailOpt = prospectEmailOpt,
        prospectFirstNameOpt = prospectFirstNameOpt,
        prospectLastNameOpt = prospectLastNameOpt,
        touchIncrementReason = touchIncrementReason,
        channelTypeOpt = channelTypeOpt,
      )(session = dbAndSession.session)

    } yield {

      res

    }

    dbUtils.commitAndCloseSession(db = dbAndSession.db)

    updateCount

  }

  def accountBillingDateAndCurrentSentAccount(accountId: AccountId): Try[AccountBillingData] = {
    accountOrgBillingRelatedInfoDAO.accountBillingDateAndCurrentSentAccount(accountId = accountId)
  }


  private def updateProspectsContacted(
    prospectId: ProspectId,
    teamId: TeamId,
    channelType: ChannelType,
    updateLastContactedAt: Boolean,
    shouldIncProspectsTouchedCount: Boolean,
  ): Try[Seq[Long]] = {

    val currentTime = DateTime.now()

    accountOrgBillingRelatedInfoDAO.insertProspectsMetadataOrUpdateLastTouchedAt(
      prospectId = prospectId,
      teamId = teamId,
      data = ProspectsMetadataUpsert.ProspectTouchedData(
        currentTime = currentTime,
        updateContactedAt = updateLastContactedAt,
        shouldIncProspectsTouchedCount = shouldIncProspectsTouchedCount
      )
    )
      // TODO: Remove this flatMap after 2months i.e May 2024
      .flatMap { _ =>

        if (updateLastContactedAt) {

          prospectDAOService._updateProspectLastContactedAt(
            savedRepliesWithProspectIdOnly = Seq((prospectId.id, currentTime)),
            team_id = teamId,
            channel_type = channelType
          )

        } else {

          Success(Seq(1))

        }

      }

  }

  def checkAndUpdateProspectsContacted(
                                        prospectId: ProspectId,
                                        teamId: TeamId,
                                        channelType: ChannelType,
                                        prospectTouchedType: ProspectTouchedType,
                                        updateLastContactedAt: Boolean,
                                        logger: SRLogger,
                                      ): Try[Int] = {
    
    given srLogger: SRLogger = logger // fixme given

    val incReason: TouchedIncrementReason = prospectTouchedType match {

      case ProspectTouchedType.EmailValidationCheck(_) =>

        TouchIncrementReason.InvalidEmail

      case ProspectTouchedType.TaskDone =>

        TouchIncrementReason.TaskDone

      case ProspectTouchedType.ManualTaskScheduled =>

        TouchIncrementReason.ManualTaskScheduled

    }

    accountOrgBillingRelatedInfoDAO.getOrgIdAndLastTouchedAtForProspects(
      prospectId = prospectId,
      teamId = teamId
    )
      .flatMap(checkAndUpdateProspectsContactedDetails => {

        val lastTouchedAtOpt: Option[DateTime] = checkAndUpdateProspectsContactedDetails.lastTouchedAt

        val currBillingCycleStartedAt: DateTime = checkAndUpdateProspectsContactedDetails.currentBillingCycleStartedAt

        val isLastTouchedAtBeforeCurrBillingCycle =
          lastTouchedAtOpt.isEmpty ||
            lastTouchedAtOpt.get.isBefore(currBillingCycleStartedAt)

        prospectTouchedType match {

          case ProspectTouchedType.EmailValidationCheck(invalidEmail) =>

            val planVersion = SrPlanVersion.getSrPlanVersion(SrBillingPlanName(checkAndUpdateProspectsContactedDetails.planName.name))

            if (planVersion == SrPlanVersion.V2) {

              Success(1)

            } else {

              val shouldIncProspectsTouchedCount = invalidEmail && isLastTouchedAtBeforeCurrBillingCycle

              updateProspectsContacted(
                prospectId = prospectId,
                teamId = teamId,
                channelType = channelType,
                updateLastContactedAt = false,
                shouldIncProspectsTouchedCount = shouldIncProspectsTouchedCount,
              )
                .flatMap(_ => {
                  if (shouldIncProspectsTouchedCount) {

                    incrementProspectsContactedCount(
                      orgId = checkAndUpdateProspectsContactedDetails.orgId,
                      teamId = teamId,
                      prospectId = prospectId,
                      prospectFirstNameOpt = checkAndUpdateProspectsContactedDetails.prospectFirstNameOpt,
                      prospectLastNameOpt = checkAndUpdateProspectsContactedDetails.prospectLastNameOpt,
                      prospectEmailOpt = checkAndUpdateProspectsContactedDetails.prospectEmailOpt,
                      touchIncrementReason = incReason,
                      channelTypeOpt = Some(channelType),
                    )

                  } else {

                    Success(1)

                  }
                })
            }

          case ProspectTouchedType.TaskDone
               | ProspectTouchedType.ManualTaskScheduled =>

            val shouldIncProspectsTouchedCount = isLastTouchedAtBeforeCurrBillingCycle

            updateProspectsContacted(
              prospectId = prospectId,
              teamId = teamId,
              channelType = channelType,
              updateLastContactedAt = updateLastContactedAt,
              shouldIncProspectsTouchedCount = shouldIncProspectsTouchedCount,
            )
              .flatMap(_ => {
                if (shouldIncProspectsTouchedCount) {

                  incrementProspectsContactedCount(
                    orgId = checkAndUpdateProspectsContactedDetails.orgId,
                    teamId = teamId,
                    prospectId = prospectId,
                    prospectFirstNameOpt = checkAndUpdateProspectsContactedDetails.prospectFirstNameOpt,
                    prospectLastNameOpt = checkAndUpdateProspectsContactedDetails.prospectLastNameOpt,
                    prospectEmailOpt = checkAndUpdateProspectsContactedDetails.prospectEmailOpt,
                    touchIncrementReason = incReason,
                    channelTypeOpt = Some(channelType),
                  )

                } else {

                  Success(1)

                }
              })
        }

      })

  }
  
  def validateOrgLimitsAndAddWarning(
                                    orgId: OrgId,
                                    addonLicenceType: Option[AddonLicenceType]
                                  )
                                    (
                                    implicit ws: WSClient, 
                                   executionContext: ExecutionContext, 
                                   Logger: SRLogger
                                  ): Try[Int] = {
    
    for {
      org: OrganizationWithCurrentData <- organizationDAOService.getOrgWithCurrentData(orgId = orgId)
        .flatMap {
          case None =>
            Failure(new Exception(s"Organization with id: ${orgId.id} not found."))

          case Some(org) =>
            Success(org)
        }
      
      _: Boolean <- if(addonLicenceType.isEmpty){planLimitService.checkProspectCountLimitAndAddWarning(
        orgId = OrgId(org.id),
        orgPlan = org.plan,
        orgWarnings = org.warnings,
        maxProspectsFromOrg = org.counts.max_prospect_limit_org,
        currentProspectsCount = org.counts.current_prospect_sent_count_org
      )}else{
        planLimitService.checkAddOnLimitAndAddWarning(
          orgId = OrgId(org.id),
          addonLicenceType = addonLicenceType.get
        )

      }
      
      updateCount: Int <- organizationDAOService.updateProspectsContactedCheckTime(orgId = orgId)
      
    } yield {
      updateCount
    }
    
  }

}
