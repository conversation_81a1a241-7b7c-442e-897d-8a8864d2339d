package api.accounts.service


import api.{AppConfig, CONSTANTS, CacheService, SRAPIResponse}
import api.accounts.{Account, AccountCreateForm, AccountDAO, AccountService, AccountUuid, AuthUtils, InviteMemberBasic, PasswordHasher, RolePermissionDataDAOV2, RolePermissionDataV2, SignInForm, SignupViaPasswordForm, TeamAccountRole, TeamId}
import api.accounts.models.{AccountId, AuthenticatedRightEnum, OTPType, OrgId, SignupType}
import api.accounts.service.AccountCreationResponse.{InviteFlowAccountCreationResponse, RegistrationFlowAccountCreationResponse}
import api.accounts.sr_api_key_type.models.SRApiKeyType
import api.call.models.NavigationLinksPrevNextResponse
import api.common_auth.{CommonAuthService, HandleLoginError}
import api.config.AppConfigCommonAuth
import api.free_email_domain.service.FreeEmailDomainListService
import api.google_recaptcha.service.{CheckIfWeShowCaptchaError, ReCaptchaError, SRGoogleRecaptchaServices, VerifyRecaptchaInputError}
import api.reports.models.UtmData
import api.spammonitor.model.SendEmailStatusData
import api.spammonitor.service.{CheckIfEmailIsAllowedToSignUpError, SpamMonitorService}
import api.team.TeamUuid
import org.joda.time.DateTime
import org.joda.time.format.{DateTimeFormat, DateTimeFormatter}
import play.api.libs.json.{JsObject, JsValue}
import play.api.libs.ws.WSClient
import play.api.mvc.Request
import utils.email_notification.service.EmailNotificationService
import utils.{Helpers, SRLogger, StringUtils}
import utils.logging.SRAccessType
import utils.emailvalidation.EmailValidationService
import utils.uuid.services.SrUuidService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}



sealed trait AuthenticateError

object AuthenticateError {
  case class  VerifyCaptchaError(err: VerifyRecaptchaInputError)  extends AuthenticateError

  case object NoGResponseSentError extends  AuthenticateError

  case class ShowCaptchaError(err:CheckIfWeShowCaptchaError) extends  AuthenticateError

  case object LoginChallengeNotFound extends AuthenticateError

  case class ErrorWhileFindingAccount(err:Throwable) extends AuthenticateError

  case object NoAccountFound extends AuthenticateError

  case object NoAccountFoundWhileSendingVerificationEmail extends AuthenticateError


  case class FailedToGetAccountHashedPasswd(e: Throwable) extends AuthenticateError

  case object WrongOTPType extends AuthenticateError



  case object AlreadyVerifiedAccount extends AuthenticateError

  case class EmailNotificationServiceError(err: Throwable ) extends AuthenticateError

  case class RedisError(err: Throwable) extends  AuthenticateError

  case object OTPNotFoundInDB extends AuthenticateError

  case object NumberOfAttemptsExceeded  extends AuthenticateError

  case object  WrongReplyFromRedis extends AuthenticateError

  case class SQLErrorWhileAddingToDB(err: Throwable ) extends AuthenticateError

  case object AccountNotFoundAfterChanging extends AuthenticateError

  case class JedisError(err: Throwable,account:Account) extends  AuthenticateError

  case class WrongReplyFromRedis(str: String,account:Account) extends  AuthenticateError

  case object RecaptchaResponseDidNotPass  extends AuthenticateError

  case object AccountIdNotFoundError extends AuthenticateError

  case object LogoutUnsuccessfulSendServerError extends AuthenticateError


  case object NoAccountHashedPasswdFound extends AuthenticateError

  case object FailedToMatchPassword extends AuthenticateError

  case object LogoutSuccessfulSendUnAuthorizedError  extends AuthenticateError
  case class TestingEmailShouldNotHaveGResponse(errStr: String) extends AuthenticateError


}

sealed trait UsersListingError
object UsersListingError{
  case class ServerError(err: Throwable) extends UsersListingError
  case class BadRequestError(err: String) extends UsersListingError
}

case class  UsersListingResponse(
                                  nav_links: NavigationLinksPrevNextResponse,
                                  usersListingApiResponse: Seq[UserFromAccountApiResponse]
                                )
sealed trait UsersByIdError

object UsersByIdError{
  case class ServerError(err: Throwable) extends UsersByIdError
  case class BadRequestError(err: String) extends UsersByIdError
}

sealed trait LogoutError
object LogoutError {

  case object  LogoutUnsuccessfulSendServerError extends  LogoutError

  case object AccountIdNotFoundError extends  LogoutError

}

sealed trait ForgotPasswordSendOTPError

object ForgotPasswordSendOTPError {

  case class ReCaptchaValidationError(err: ReCaptchaError) extends ForgotPasswordSendOTPError

  case object ReCaptchaFailed extends ForgotPasswordSendOTPError

  case class ErrorFindingAccount(err: Throwable) extends ForgotPasswordSendOTPError

  case object NoAccountFound extends ForgotPasswordSendOTPError

  case class EmailNotificationServiceError(err: Throwable) extends ForgotPasswordSendOTPError

  case class ErrorWhileGettingOtp(err: GetOTPError) extends ForgotPasswordSendOTPError

  case object WrongSignupType extends ForgotPasswordSendOTPError

}

sealed trait UpdatePasswordWithOTPError

object UpdatePasswordWithOTPError {

  case class ReCaptchaValidationError(err: ReCaptchaError) extends UpdatePasswordWithOTPError

  case object ReCaptchaFailed extends UpdatePasswordWithOTPError

  case class ErrorFindingAccount(err: Throwable) extends UpdatePasswordWithOTPError

  case object NoAccountFound extends UpdatePasswordWithOTPError

  case class VerifyOTPFail(err: VerifyOTPError) extends UpdatePasswordWithOTPError

  case object OTPDidNotMatch extends UpdatePasswordWithOTPError

  case class SQLErrorWhileUpdatingPassword(err: Throwable) extends UpdatePasswordWithOTPError

  case object WrongSignupType extends UpdatePasswordWithOTPError

  case class ErrorWhileDeletingTheKey(err: Throwable) extends UpdatePasswordWithOTPError

}

sealed trait ResendVerificationEmailError

object ResendVerificationEmailError {

  case class ErrorFindingAccount(err: Throwable) extends ResendVerificationEmailError

  case object NoAccountFound extends ResendVerificationEmailError

  case object AlreadyVerified extends ResendVerificationEmailError

  case class ErrorWhileGettingOtp(err: GetOTPError) extends ResendVerificationEmailError

  case class EmailNotificationServiceError(err: Throwable) extends ResendVerificationEmailError

  case class ReCaptchaFailError(err: ReCaptchaError) extends ResendVerificationEmailError

  case object ReCaptchaFailed extends ResendVerificationEmailError
}

sealed trait ExistingAccountSignupEmailError

object ExistingAccountSignupEmailError {

  case class ErrorFindingAccount(err: Throwable) extends ExistingAccountSignupEmailError

  case object NoAccountFound extends ExistingAccountSignupEmailError

  case class EmailNotificationServiceError(err: Throwable) extends ExistingAccountSignupEmailError
}

sealed trait VerifyEmailError

object VerifyEmailError {

  case class ReCaptchaValidationError(err: ReCaptchaError) extends VerifyEmailError

  case object ReCaptchaFailed extends VerifyEmailError

  case class ErrorFindingAccount(err: Throwable) extends VerifyEmailError

  case object NoAccountFound extends VerifyEmailError

  case class VerifyOTPFail(err: VerifyOTPError) extends VerifyEmailError

  case object OTPDidNotMatch extends VerifyEmailError

  case class SQLErrorWhileVerifyingAccount(err: Throwable) extends VerifyEmailError

  case object NoneResponseAfterVerifyingEmail extends VerifyEmailError

  case class ErrorWhileDeletingTheKey(err: Throwable) extends VerifyEmailError

}

sealed trait ErrorWhileAuthenticating

object ErrorWhileAuthenticating {

  case class ErrorWhileFindingAccount(err: Throwable) extends ErrorWhileAuthenticating

  case object NoAccountFound extends ErrorWhileAuthenticating

  case class AccountNotActive(accountId:Long, orgId: Option[OrgId]) extends ErrorWhileAuthenticating

  case class IpAccessNotAllowed(accountId: Long,orgId: Option[OrgId]) extends ErrorWhileAuthenticating

  case class FailedToGetAccountHashedPasswd(err: Throwable) extends ErrorWhileAuthenticating

  case object NoAccountHashedPasswdFound extends ErrorWhileAuthenticating

  case object FailedToMatchPassword extends ErrorWhileAuthenticating

  case class ErrorResendVerificationEmail(err: ResendVerificationEmailError) extends ErrorWhileAuthenticating

  case class ErrorCreateUserLoginKeyInRedis(err: CreateUserLoginKeyInRedisError, account: Account) extends ErrorWhileAuthenticating
  case object  LoginChallengeNotFound extends ErrorWhileAuthenticating

}
sealed trait FailedAtSignup

object FailedAtSignup {
  case class ReCaptchaValidationError(err: ReCaptchaError) extends FailedAtSignup

  case object ReCaptchaFailed extends FailedAtSignup

  case class SpamCheckFailed(err: CheckIfEmailIsAllowedToSignUpError) extends FailedAtSignup

  case object EmptyDomain extends FailedAtSignup

  case class NoInviteCode(inviteMember: Option[InviteMemberBasic]) extends FailedAtSignup

  case object PasswordFailed extends FailedAtSignup

  case class ErrorFindingAccount(err: Throwable) extends FailedAtSignup

  case object AccountAlreadyThere extends FailedAtSignup

  case class FailedToCreateAccount(err: Throwable) extends FailedAtSignup

  case object NoAccountFoundAfterCreating extends FailedAtSignup


}

class CredentialsAuthService(
                              sRGoogleRecaptchaServices: SRGoogleRecaptchaServices,
                              resetUserCacheUtil: ResetUserCacheUtil,
                              emailNotificationService: EmailNotificationService,
                              accountOTPService: AccountOTPService,
                              authUtils: AuthUtils,
                              cacheService: CacheService,
                              //                              userRedisKeyService: UserRedisKeyService,
                              spamMonitorService: SpamMonitorService,
                              accountService: AccountService,
                              //                              freeEmailDomainListService: FreeEmailDomainListService,
                              commonAuthService: CommonAuthService,
                              implicit val wsClient: WSClient,
                              implicit val ec: ExecutionContext,
                              accountDAO: AccountDAO,
                              srUuidService: SrUuidService
                            ) {

  def ultimateAuthenticate(
                            data: SignInForm,
                            userIp: String,
                            request: Request[JsValue], //FIXME Only pass the required parameters not the whole request
                          )(using Logger: SRLogger): Future[Either[AuthenticateError,AuthenticatedRightEnum]] = {
    sRGoogleRecaptchaServices.verifyCaptcha(
      requestIp = userIp,
      response = data.g_response,
      userEmail = Some(data.email)
    ) flatMap  {

      case Left(ReCaptchaError.VerifyCaptchaError(err)) =>
        Future.successful(Left(AuthenticateError.VerifyCaptchaError(err)))

      case Left(ReCaptchaError.NoGResponseSentError) =>
        Future.successful(Left(AuthenticateError.NoGResponseSentError))

      case Left(ReCaptchaError.ShowCaptchaError(err)) =>
        Future.successful(Left(AuthenticateError.ShowCaptchaError(err)))

      case Left(ReCaptchaError.TestingEmailShouldNotHaveGResponse(errStr)) =>
        Future.successful(Left(AuthenticateError.TestingEmailShouldNotHaveGResponse(errStr)))

      case Right(recaptchaResponse) =>
        if (!recaptchaResponse.passedCaptcha) {
          Future.successful(Left(AuthenticateError.RecaptchaResponseDidNotPass))
        } else {
          val email = data.email.toLowerCase.trim

//          Logger.info(s"$logRequestId login attempt: $email : origin: ${request.headers.get("Origin")}")
          authenticate(data = data, request = request).flatMap {

            case Left(ErrorWhileAuthenticating.LoginChallengeNotFound) =>
              Future.successful(Left(AuthenticateError.LoginChallengeNotFound))

            case Left(ErrorWhileAuthenticating.ErrorWhileFindingAccount(err)) =>
              Future.successful(Left(AuthenticateError.ErrorWhileFindingAccount(err)))

            case Left(ErrorWhileAuthenticating.NoAccountFound) =>
              Future.successful(Left(AuthenticateError.NoAccountFound))

            case Left(ErrorWhileAuthenticating.AccountNotActive(accId, orgId)) =>
              authUtils.performLogoutAndDropSession(
                accountId = Some(AccountId(id = accId)),
                orgId = orgId,
                logString = "CredentialAuthService ultimateAuthenticate AccountNotActive"
              ).map {

                case Left(LogoutError.AccountIdNotFoundError) =>
                  Left(AuthenticateError.AccountIdNotFoundError)

                case Left(LogoutError.LogoutUnsuccessfulSendServerError)  =>
                  Left(AuthenticateError.LogoutUnsuccessfulSendServerError)

                case Right(value) =>
                  Left(AuthenticateError.LogoutSuccessfulSendUnAuthorizedError)

              }
            case Left(ErrorWhileAuthenticating.IpAccessNotAllowed(accId, orgId)) =>
               authUtils.performLogoutAndDropSession(
                accountId = Some(AccountId(id = accId)),
                orgId = orgId,
                logString = "CredentialAuthService ultimateAuthenticate IpAccessNotAllowed "
              ).map {

                case Left(LogoutError.AccountIdNotFoundError) =>
                  Left(AuthenticateError.AccountIdNotFoundError)

                case Left(LogoutError.LogoutUnsuccessfulSendServerError) =>
                  Left(AuthenticateError.LogoutUnsuccessfulSendServerError)

                case Right(value) =>
                  Left(AuthenticateError.LogoutSuccessfulSendUnAuthorizedError)

              }

            case Left(ErrorWhileAuthenticating.FailedToGetAccountHashedPasswd(e)) =>
              Future.successful(Left(AuthenticateError.FailedToGetAccountHashedPasswd(e)))

            case Left(ErrorWhileAuthenticating.NoAccountHashedPasswdFound) =>
              Future.successful(Left(AuthenticateError.NoAccountHashedPasswdFound))

            case Left(ErrorWhileAuthenticating.FailedToMatchPassword) =>
              Future.successful(Left(AuthenticateError.FailedToMatchPassword))

            case Left(ErrorWhileAuthenticating.ErrorResendVerificationEmail(err)) =>
              err
              match {
                case (ResendVerificationEmailError.ReCaptchaFailed) =>
                  Future.successful(Left(AuthenticateError.RecaptchaResponseDidNotPass))

                case (ResendVerificationEmailError.ReCaptchaFailError(a)) =>
                  a match {
                    case ReCaptchaError.VerifyCaptchaError(err) =>
                      Future.successful(Left(AuthenticateError.VerifyCaptchaError(err)))

                    case ReCaptchaError.NoGResponseSentError =>
                      Future.successful(Left(AuthenticateError.NoGResponseSentError))


                    case ReCaptchaError.ShowCaptchaError(err) =>
                      Future.successful(Left(AuthenticateError.ShowCaptchaError(err)))


                    case ReCaptchaError.TestingEmailShouldNotHaveGResponse(errStr) =>
                      Future.successful(Left(AuthenticateError.TestingEmailShouldNotHaveGResponse(errStr)))

                  }
                case (ResendVerificationEmailError.ErrorFindingAccount(err)) =>
                  Future.successful(Left(AuthenticateError.ErrorWhileFindingAccount(err)))

                case (ResendVerificationEmailError.NoAccountFound) =>
                  Future.successful(Left(AuthenticateError.NoAccountFoundWhileSendingVerificationEmail))

                case (ResendVerificationEmailError.AlreadyVerified) =>
                  Future.successful(Left(AuthenticateError.AlreadyVerifiedAccount))

                case (ResendVerificationEmailError.EmailNotificationServiceError(err)) =>
                  Future.successful(Left(AuthenticateError.EmailNotificationServiceError(err)))

                case (ResendVerificationEmailError.ErrorWhileGettingOtp(a)) => a match {

                  case GetOTPError.RedisError(err) =>
                    Future.successful(Left(AuthenticateError.RedisError(err)))

                  case GetOTPError.OTPNotFoundInDB =>
                    Future.successful(Left(AuthenticateError.OTPNotFoundInDB))

                  case GetOTPError.NumberOfAttemptsExceeded =>
                    Future.successful(Left(AuthenticateError.NumberOfAttemptsExceeded))

                  case GetOTPError.ErrorWhileCreatingOTP(b) =>

                    b match {
                      case CreateOTPAndAddToRedisAndDBError.ErrorAddingKeyToRedis(a) => a match {

                        case AddKeyToRedisError.RedisError(err) =>
                          Future.successful(Left(AuthenticateError.RedisError(err)))

                        case AddKeyToRedisError.WrongReplyFromRedis =>
                          Future.successful(Left(AuthenticateError.WrongReplyFromRedis))
                      }

                      case CreateOTPAndAddToRedisAndDBError.ErrorAddingCodeToDB(a) => a match {

                        case AddCodeToDBError.SQLErrorWhileAddingToDB(err) =>
                          Future.successful(Left(AuthenticateError.SQLErrorWhileAddingToDB(err)))

                        case AddCodeToDBError.EntityNotFoundAfterChanging =>
                          Future.successful(Left(AuthenticateError.AccountNotFoundAfterChanging))
                        case AddCodeToDBError.WrongOTPType =>
                          Future.successful(Left(AuthenticateError.WrongOTPType))
                        case AddCodeToDBError.AlreadyInGDPRList =>
                          Future.successful(Left(AuthenticateError.WrongOTPType))
                      }
                    }


                }
              }
            case Left(ErrorWhileAuthenticating.ErrorCreateUserLoginKeyInRedis(err, account)) =>
              err match {
                case (CreateUserLoginKeyInRedisError.JedisError(err)) =>
                  Future.successful(Left(AuthenticateError.JedisError(err,account)))

                case (CreateUserLoginKeyInRedisError.WrongReplyFromJedis(msg)) =>
                  Future.successful(Left(AuthenticateError.WrongReplyFromRedis(msg,account)))


              }

            case Right(authenticatedRightEnum) =>
              Future.successful(Right(authenticatedRightEnum))

          }
        }
    }
  }
  def authenticate(
                    data: SignInForm,
                    request: Request[JsValue]
                  )(using Logger: SRLogger): Future[Either[ErrorWhileAuthenticating, AuthenticatedRightEnum]] =  {

          if(data.login_challenge.isEmpty){

            Future.successful(Left(ErrorWhileAuthenticating.LoginChallengeNotFound))
          }else {

            val email = data.email.toLowerCase.trim


            accountService.findByEmailIgnoreCache(email = email) match {

              case Failure(err) =>

                Future.successful(Left(ErrorWhileAuthenticating.ErrorWhileFindingAccount(err)))

              case Success(None) =>

                Future.successful(Left(ErrorWhileAuthenticating.NoAccountFound))

              case Success(Some(account)) =>

              //Fix Me add unit test covering the following code
                commonAuthService.acceptLogin(
                  loginChallenge = data.login_challenge.get,
                  subject = account.internal_id.toString,
                  remember = AppConfigCommonAuth.remember,
                  remember_for_duration_in_seconds = AppConfigCommonAuth.remember_for_duration_in_seconds
                ).map { redirect_to =>

                  val clientIP = Helpers.getClientIPFromGCPLoadbalancer(request = request)

                  val ipAccess = authUtils.checkIfIPAccessAndLogForAudit(
                    loggedinAccount = account,
                    member = None,
                    accessType = SRAccessType.WEB,
                    clientIP = clientIP,
                    isSupportAppModeRequest = false, // 8-feb-2021: this wont matter, support login has a new token-based auth api
                    request_method = request.method,
                    request_host = request.host,
                    request_url = request.path,
                    request_query = request.rawQueryString
                  )

                  if (!account.active) {

                    Left(ErrorWhileAuthenticating.AccountNotActive(accountId = account.internal_id, orgId = Some(OrgId(id = account.org.id))))

                  } else if (account.profile.scheduled_for_deletion_at.isDefined) {
                    Left(ErrorWhileAuthenticating.AccountNotActive(accountId = account.internal_id, orgId = Some(OrgId(account.org.id))))
                  } else if (!ipAccess) {

                    Left(ErrorWhileAuthenticating.IpAccessNotAllowed(accountId = account.internal_id,orgId = Some(OrgId(account.org.id) )))

                  } else {

                    accountService.getAccountHashedPasswd(
                      accountId = AccountId(id = account.internal_id) // FIXME VALUECLASS

                    ) match {
                      case Failure(e) =>

                        Left(ErrorWhileAuthenticating.FailedToGetAccountHashedPasswd(e))

                      case Success(None) =>

                        Left(ErrorWhileAuthenticating.NoAccountHashedPasswdFound)

                      case Success(Some(hashedPass)) =>

                        PasswordHasher.matches(hashedPass, data.password) match {

                          case false =>

                            Left(ErrorWhileAuthenticating.FailedToMatchPassword)

                          case true =>

                            val p = account.profile

                            if (!account.email_verified) {

                              //case when account is not verified
                              resendVerificationEmail(
                                email = email
                              )
                              match {
                                case Left(err) =>

                                  Left(ErrorWhileAuthenticating.ErrorResendVerificationEmail(err))

                                case Right(count) =>

                                  Right(AuthenticatedRightEnum.ResendVerificationEmail(account = account, count = count, redirectTo = redirect_to))

                              }
                            } else if (

                              account.org.settings.enforce_2fa &&
                                (
                                  !p.twofa_enabled ||

                                    (p.twofa_enabled && !(

                                      (
                                        p.mobile_country_code.isDefined &&
                                          p.country_code.isDefined &&
                                          p.mobile_number.isDefined
                                        ) ||

                                        p.has_gauthenticator

                                      ))
                                  )

                            ) {
                              val cacheKey = s"otpstate:${account.internal_id}"
                              val generatedVerificationState = StringUtils.genRandomAlphaNumericString30Chars
                              cacheService.setStr(key = cacheKey, value = generatedVerificationState, expirySeconds = 1200)

                              Right(AuthenticatedRightEnum.Enable2FA(account = account, generatedVerificationState = generatedVerificationState,redirectTo = redirect_to))

                            } else if (p.twofa_enabled) {

                              val cacheKey = s"otpstate:${account.internal_id}"
                              val generatedVerificationState = StringUtils.genRandomAlphaNumericString30Chars
                              cacheService.setStr(key = cacheKey, value = generatedVerificationState, expirySeconds = 1200)

                              val twoFAType = if (p.has_gauthenticator) "gauth" else "sms"

                              Right(AuthenticatedRightEnum.Verify2FA(account = account, generatedVerificationState = generatedVerificationState, twoFAType = twoFAType, redirectTo = redirect_to))

                            } else {
                              Right(AuthenticatedRightEnum.Success(account, redirectTo = redirect_to))
                            }

                        }
                    }
                  }
                }
            }
          }
  }


  def forgotPasswordSendOTP(
                             userIp: String,
                             g_response: Option[String],
                             email: String
                           )(using Logger: SRLogger): Future[Either[ForgotPasswordSendOTPError, Int]] = {

    sRGoogleRecaptchaServices.verifyCaptcha(
      requestIp = userIp,
      response = g_response
    ) map {
      case Left(a) =>

        Left(ForgotPasswordSendOTPError.ReCaptchaValidationError(a))

      case Right(recaptchaResponse) =>
        if (!recaptchaResponse.passedCaptcha) {

          Left(ForgotPasswordSendOTPError.ReCaptchaFailed)

        } else {

          accountService.findByEmailIgnoreCache(email) match {

            case Failure(err) => Left(ForgotPasswordSendOTPError.ErrorFindingAccount(err))

            case Success(None) => Left(ForgotPasswordSendOTPError.NoAccountFound)

            case Success(Some(account)) =>

              if (!account.signupType.contains(SignupType.Password)) {

                Left(ForgotPasswordSendOTPError.WrongSignupType)

              } else {


                given localSrLogger: SRLogger = Logger.appendLogRequestId(s"aid_${account.internal_id}")

                accountOTPService.getOrCreateOTPAndCount(
                  verification_code_in_db = account.email_verification_code,
                  otpType = OTPType.ForgotPassword(account.internal_id)
                ) match {
                  case Left(a) =>

                    Left(ForgotPasswordSendOTPError.ErrorWhileGettingOtp(a))

                  case Right(otpAndCount) =>

                    val body = views.html.emails.forgotPassword(
                      otp = otpAndCount.otp,
                      first_name = account.first_name.getOrElse("there")
                    ).toString()

                    emailNotificationService.sendMailFromAdmin(
                      toEmail = account.email,
                      toName = Some(Helpers.getAccountName(account)),
                      subject = s"Reset your ${CONSTANTS.APP_NAME} password",
                      body = body) match {

                      case Failure(e) =>

                        Left(ForgotPasswordSendOTPError.EmailNotificationServiceError(e))

                      case Success(_) =>

                        Right(otpAndCount.count)

                    }

                }
              }
          }
        }
    }
  }


  def updatePasswordWithOTP(
                             userIp: String,
                             g_response: Option[String],
                             email: String,
                             otp: String,
                             password: String
                           )(Logger: SRLogger): Future[Either[UpdatePasswordWithOTPError, Long]] = {


    sRGoogleRecaptchaServices.verifyCaptcha(
      requestIp = userIp,
      response = g_response
    ) map {
      case Left(a) =>

        Left(UpdatePasswordWithOTPError.ReCaptchaValidationError(a))

      case Right(recaptchaResponse) =>
        if (!recaptchaResponse.passedCaptcha) {

          Left(UpdatePasswordWithOTPError.ReCaptchaFailed)

        } else {
          accountService.findByEmailIgnoreCache(email)(
            logger = Logger
          ) match {

            case Failure(err) =>

              Left(UpdatePasswordWithOTPError.ErrorFindingAccount(err))

            case Success(None) =>

              Left(UpdatePasswordWithOTPError.NoAccountFound)


            case Success(Some(account)) =>
              if (!account.signupType.contains(SignupType.Password)) {

                Left(UpdatePasswordWithOTPError.WrongSignupType)

              } else {

                accountOTPService.getOTPFromDBAndVerifyOTP(
                  verification_code_in_db = account.email_verification_code,
                  otpType = OTPType.ForgotPassword(account.internal_id),
                  otp = otp
                )(Logger.appendLogRequestId(s"aid_${account.internal_id}")) match {
                  case Left(a) =>

                    Left(UpdatePasswordWithOTPError.VerifyOTPFail(a))

                  case Right(passAndCode) =>

                    if (!passAndCode.pass) {

                      Left(UpdatePasswordWithOTPError.OTPDidNotMatch)

                    } else {
                      accountService.updatePasswordAndResetVerificationCode(
                        // FIXME VALUECLASS
                        accountId = AccountId(id = account.internal_id),
                        password = password,
                        code = passAndCode.code
                      )(Logger) match {

                        case Failure(e) =>

                          Left(UpdatePasswordWithOTPError.SQLErrorWhileUpdatingPassword(e))

                        case Success(None) =>

                          Left(UpdatePasswordWithOTPError.NoAccountFound)

                        case Success(Some(_)) =>

                          accountOTPService.deleteKeyInRedis(
                            otpType = OTPType.ForgotPassword(account.internal_id)
                          ) match {
                            case Failure(err) =>
                              Left(UpdatePasswordWithOTPError.ErrorWhileDeletingTheKey(err))

                            case Success(_) =>
                              Right(account.internal_id)

                          }


                      }
                    }
                }
              }
          }
        }
    }
  }


  def resendVerificationEmail(
                               email: String
                             )(using Logger: SRLogger): Either[ResendVerificationEmailError, Int] = {


    accountService.findByEmailIgnoreCache(email) match {

      case Failure(err) =>

        Left(ResendVerificationEmailError.ErrorFindingAccount(err))

      case Success(None) =>

        Left(ResendVerificationEmailError.NoAccountFound)

      case Success(Some(account)) =>

        if (account.email_verified) {

          // changed so that we dont show this error in the frontend when we get a resend OTP request
          Right(0)

        } else {

          given localSrLogger: SRLogger = Logger.appendLogRequestId(s"aid_${account.internal_id}")

          accountOTPService.getOrCreateOTPAndCount(
            verification_code_in_db = account.email_verification_code,
            otpType = OTPType.VerifyEmail(account.internal_id)
          ) match {
            case Left(a) =>
              Left(ResendVerificationEmailError.ErrorWhileGettingOtp(a))

            case Right(otpAndCount) =>

              val dtf: DateTimeFormatter = DateTimeFormat.forPattern("dd MMM YYYY")

              val today = DateTime.now()

              val toName = Some(account.first_name.getOrElse("there"))

              val body = views.html.emails.verifyEmail(
                name = toName.get,
                otp = otpAndCount.otp,
                appName = CONSTANTS.APP_NAME,
                trial_start_date = dtf.print(today),
                trial_end_date = dtf.print(account.org.trial_ends_at)
              ).toString()

              emailNotificationService.sendMailFromAdmin(
                toEmail = account.email,
                toName = toName,
                subject = s"Verify your ${CONSTANTS.APP_NAME} email",
                body = body) match {

                case Failure(err) =>

                  Left(ResendVerificationEmailError.EmailNotificationServiceError(err))

                case Success(_) =>

                  Right(otpAndCount.count)

              }
          }
        }
    }
  }

  def sendExistingAccountSignupEmail(
                                      email: String
                                    )(using Logger: SRLogger): Either[ExistingAccountSignupEmailError, Unit] = {


    accountService.findByEmailIgnoreCache(email) match {

      case Failure(err) =>

        Left(ExistingAccountSignupEmailError.ErrorFindingAccount(err))

      case Success(None) =>

        Left(ExistingAccountSignupEmailError.NoAccountFound)

      case Success(Some(account)) =>
//              val dtf: DateTimeFormatter = DateTimeFormat.forPattern("dd MMM YYYY")
//
//              val today = DateTime.now()

              val toName = Some(account.first_name.getOrElse("there"))

              val body =
                s"""
                   Hello ${toName.get},
                   There was an attempt to signup to ${CONSTANTS.APP_NAME} with your email. If done by you, please try to logging in instead.
                   If not done by you, no need to take any actions, the signup was unsuccessful.<br/>
                   Regards,<br/>
                   ${CONSTANTS.APP_NAME} Team
                """

              emailNotificationService.sendMailFromAdmin(
                toEmail = account.email,
                toName = toName,
                subject = s"URGENT: Attempt to signup to ${CONSTANTS.APP_NAME} with your existing email",
                body = body) match {

                case Failure(err) =>

                  Left(ExistingAccountSignupEmailError.EmailNotificationServiceError(err))

                case Success(_) =>

                  Right({})

              }
          }
  }


  def verifyEmail(
                   userIp: String,
                   g_response: Option[String],
                   email: String,
                   otp: String,
                   loginChallenge: Option[LoginChallenge]
                 )(using Logger: SRLogger): Future[Either[VerifyEmailError, AccountWithRedirectUri]] = {
      sRGoogleRecaptchaServices.verifyCaptcha(
        requestIp = userIp,
        response = g_response
      ) flatMap{
        case Left(a) =>

          Future.successful(Left(VerifyEmailError.ReCaptchaValidationError(a)))

        case Right(recaptchaResponse) =>

          if (!recaptchaResponse.passedCaptcha) {

           Future.successful(Left(VerifyEmailError.ReCaptchaFailed))

          } else {


          accountService.findByEmailIgnoreCache(email = email) match {

              case Failure(err) =>

                Future.successful(Left(VerifyEmailError.ErrorFindingAccount(err)))

              case Success(None) =>

                Future.successful(Left(VerifyEmailError.NoAccountFound))

              case Success(Some(account)) =>

                if (account.email_verified) {
                  // sending OTP didNotMatch when email is already verified, Signup flow Security fix
                  Future.successful(Left(VerifyEmailError.OTPDidNotMatch))

                } else {

                  accountOTPService.getOTPFromDBAndVerifyOTP(
                    verification_code_in_db = account.email_verification_code,
                    otpType = OTPType.VerifyEmail(account.internal_id),
                    otp = otp
                  )(Logger.appendLogRequestId(s"aid_${account.internal_id}")) match {
                    case Left(a) =>

                      Future.successful(Left(VerifyEmailError.VerifyOTPFail(a)))
                    case Right(passAndCode) =>

                      if (!passAndCode.pass) {

                        Future.successful(Left(VerifyEmailError.OTPDidNotMatch))

                      } else {
                        accountService.updateAccountEmailStatusIsVerified(
                          // FIXME VALUECLASS
                          accountId = AccountId(id = account.internal_id),
                          code = passAndCode.code
                        ) match {

                          case Failure(err) =>

                            Future.successful(Left(VerifyEmailError.SQLErrorWhileVerifyingAccount(err)))

                          case Success(None) =>
                            Future.successful(Left(VerifyEmailError.NoneResponseAfterVerifyingEmail))
                          case Success(Some(_)) =>
                            accountOTPService.deleteKeyInRedis(
                              otpType = OTPType.VerifyEmail(account.internal_id)
                            ) match {
                              case Failure(err) =>

                                Future.successful(Left(VerifyEmailError.ErrorWhileDeletingTheKey(err)))

                              case Success(_) =>

                                if(loginChallenge.isDefined) {
                                  commonAuthService.acceptLogin(
                                    loginChallenge =loginChallenge.map(_.challenge).get,
                                    subject = account.internal_id.toString,
                                    remember = true,
                                    remember_for_duration_in_seconds = AppConfigCommonAuth.remember_for_duration_in_seconds
                                  ).map(redirect_uri =>{
                                    Right(AccountWithRedirectUri(account = account, redirectUri = redirect_uri))
                                  })
                                } else {
                                  Future.successful(Right(AccountWithRedirectUri(account = account, redirectUri = s"${AppConfig.dashboardDomain}/login")))
                                }



                            }


                        }
                      }
                  }
                }
            }
          }
      }

  }


  def ultimateSignUp(
                      userIp: String,
                      newAccount: SignupViaPasswordForm,
                      utmData: UtmData,
                      rolePermissionDataDAOV2: RolePermissionDataDAOV2,
                      cookieString: String,
                      queryStr: String,
                      admitadCreateAt: Option[DateTime],
                      firstPromoterCreateAt: Option[DateTime],
                      isLocalhost: Boolean,
                      loginChallenge: LoginChallenge,
                      message:String
                    )(using Logger: SRLogger):Future[Either[SignUpError,AccountCreationResponse]] = {


    signup(
      userIp = userIp,
      newAccount = newAccount,
      utmData = utmData,
      rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
      cookieString = cookieString,
      queryStr = queryStr,
      admitadCreateAt = admitadCreateAt,
      firstPromoterCreateAt = firstPromoterCreateAt
    ).flatMap {
      case Left(FailedAtSignup.ReCaptchaValidationError(err)) => Future.successful(Left(SignUpError.ReCaptchaValidationError(err)))

      case Left(FailedAtSignup.ReCaptchaFailed) =>  Future.successful(Left(SignUpError.ReCaptchaFailed))

      case Left(FailedAtSignup.SpamCheckFailed(err)) =>  Future.successful(Left(SignUpError.SpamCheckFailed(err)))


      case Left(FailedAtSignup.EmptyDomain) =>  Future.successful(Left(SignUpError.EmptyDomain))


      case Left(FailedAtSignup.NoInviteCode(inviteMember)) =>  Future.successful(Left(SignUpError.NoInviteCode(inviteMember)))

      case Left(FailedAtSignup.PasswordFailed) =>  Future.successful(Left(SignUpError.PasswordFailed))


      case Left(FailedAtSignup.AccountAlreadyThere) =>
        //            Res.BadRequestError(s"You already have an account on SmartReach with the email: ${newAccount.email}. Please login or reset your password.", error_code = Some(ErrorCode.ACCOUNT_WITH_EMAIL_EXISTS))

        sendExistingAccountSignupEmail(
          email = newAccount.email
        ) match {
          case Left(ExistingAccountSignupEmailError.ErrorFindingAccount(err)) =>
            Future.successful(Left(SignUpError.ExistingAccountSignupEmailErrorInSignUp(ExistingAccountSignupEmailError.ErrorFindingAccount(err))))

          case Left(ExistingAccountSignupEmailError.NoAccountFound) =>
            Future.successful(Left(SignUpError.ExistingAccountSignupEmailErrorInSignUp(ExistingAccountSignupEmailError.NoAccountFound)))

          case Left(ExistingAccountSignupEmailError.EmailNotificationServiceError(err)) =>

            Future.successful(Left(SignUpError.ExistingAccountSignupEmailErrorInSignUp(ExistingAccountSignupEmailError.EmailNotificationServiceError(err))))



          case Right(_) =>
            Future.successful(Right(AccountCreationResponse.ExistingAccountResponse(
                disable_analytics = isLocalhost,
                attemptNumber = 0
              )))


        }

      case Left(FailedAtSignup.ErrorFindingAccount(err)) =>
        Future.successful(Left(SignUpError.ErrorFindingAccount(err)))

      case Left(FailedAtSignup.FailedToCreateAccount(e)) =>
        Future.successful(Left(SignUpError.FailedToCreateAccount(e)))


      case Left(FailedAtSignup.NoAccountFoundAfterCreating) =>
        Future.successful(Left(SignUpError.NoAccountFoundAfterCreating))
      case Right(savedAccount) =>
        Logger.info(s"signUp attempt ok :: ${message}")


        resetUserCacheUtil._resetTeamCache(savedAccount.internal_id)

        if (newAccount.invite_code.isDefined) {


          val succRes = commonAuthService.acceptLogin(
              loginChallenge = loginChallenge.challenge,
              subject = savedAccount.internal_id.toString,
              remember = AppConfigCommonAuth.remember,
              remember_for_duration_in_seconds = AppConfigCommonAuth.remember_for_duration_in_seconds
          ).map{redirect_to =>
            Right(InviteFlowAccountCreationResponse(
              code = "success",
              account = savedAccount,
              disable_analytics = isLocalhost,
              attemptNumber = 0,
              redirect_to = redirect_to
            ))
          }






          /*
          12th July 2021: NOT SENDING DRIP EMAILS TO INVITED USERS

          if (!isDevSetup) {

            NewUserDripCampaigns.addUserToWelcomeCampaign(savedAccount).map { case res =>

              Logger.info(s"New user added to welcome campaign AccountId: ${savedAccount.id}")

              succRes

            }.recover { case e =>

              Logger.error(s"[FATAL] New user adding to welcome campaign error: ${Helpers.getStackTraceAsString(e)}")

              succRes

            }

          } else {

            Logger.info(s"New user added to welcome campaign AccountId: ${savedAccount.id}")

            Future.successful(succRes)

          }
          */


          succRes


        } else {

          resendVerificationEmail(
            email = savedAccount.email
          ) match {
            case Left(err) =>
              Future.successful(Left(SignUpError.ResendVerificationEmailErrorInSignup(err)))

            case Right(count) =>
              Future.successful(Right(RegistrationFlowAccountCreationResponse(
                disable_analytics = isLocalhost,
                attemptNumber = count
              )))

          }

        }
    }
  }


  def signup(
              userIp: String,
              newAccount: SignupViaPasswordForm,
              utmData: UtmData,
              rolePermissionDataDAOV2: RolePermissionDataDAOV2,
              cookieString: String,
              queryStr: String,
              admitadCreateAt: Option[DateTime],
              firstPromoterCreateAt: Option[DateTime]
  )(using Logger: SRLogger): Future[Either[FailedAtSignup, Account]] = {

    for {
      recaptchaResponseEither <- sRGoogleRecaptchaServices.verifyCaptcha(
        requestIp = userIp,
        response = newAccount.g_response
      )

      spamCheckEither <- spamMonitorService.checkIfEmailIsAllowedToSignUp(email = newAccount.email.toLowerCase.trim)

    } yield {

      recaptchaResponseEither match {
        case Left(err) =>
          Left(FailedAtSignup.ReCaptchaValidationError(err))

        case Right(recaptchaResponse) =>

          if (!recaptchaResponse.passedCaptcha) {
            Left(FailedAtSignup.ReCaptchaFailed)

          } else {
            spamCheckEither match {
              case Left(err) =>
                Left(FailedAtSignup.SpamCheckFailed(err))

              case Right(sendEmailStatus) =>
                signupStep2(
                  newAccount = newAccount,
                  utmData = utmData,
                  rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
                  cookieString = cookieString,
                  queryStr = queryStr,
                  sendEmailStatus = sendEmailStatus,
                  admitadCreatedAt = admitadCreateAt,
                  firstPromoterCreatedAt = firstPromoterCreateAt
                )
            }
          }
      }
    }
  }


  def signupStep2(
                   newAccount: SignupViaPasswordForm,
                   utmData: UtmData,
                   rolePermissionDataDAOV2: RolePermissionDataDAOV2,
                   cookieString: String,
                   queryStr: String,
                   sendEmailStatus: SendEmailStatusData,
                   admitadCreatedAt: Option[DateTime],
                   firstPromoterCreatedAt: Option[DateTime]
                 )(using Logger: SRLogger): Either[FailedAtSignup, Account] = {

    val (_, domain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(email = newAccount.email)
    val inviteMember = accountService.getInviteByEmail(email = newAccount.email)


    if (domain.trim.isEmpty) {

      Left(FailedAtSignup.EmptyDomain)

    } else if (inviteMember.isDefined && newAccount.invite_code.isEmpty) {

      Left(FailedAtSignup.NoInviteCode(inviteMember))

    } else if (!AccountService.checkPassword(newAccount.password)) {

      Left(FailedAtSignup.PasswordFailed)

    } else {


      accountService.findByEmailIgnoreCache(newAccount.email) match {


        case Failure(err) =>

          Left(FailedAtSignup.ErrorFindingAccount(err))

        case Success(Some(_)) =>

          Left(FailedAtSignup.AccountAlreadyThere)

        case Success(None) =>

          signupStep4PreExistingEmailCheckSuccess(
            newAccount = newAccount,
            utmData = utmData,
            rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
            cookieString = cookieString,
            queryStr = queryStr,
            sendEmailStatus = sendEmailStatus,
            admitadCreatedAt = admitadCreatedAt,
            firstPromoterCreatedAt = firstPromoterCreatedAt
          )
      }
    }


  }

  def signupStep4PreExistingEmailCheckSuccess(
                                               newAccount: SignupViaPasswordForm,
                                               utmData: UtmData,
                                               rolePermissionDataDAOV2: RolePermissionDataDAOV2,
                                               cookieString: String,
                                               queryStr: String,
                                               sendEmailStatus: SendEmailStatusData,
                                               admitadCreatedAt: Option[DateTime],
                                               firstPromoterCreatedAt: Option[DateTime]
                                             )(using Logger: SRLogger): Either[FailedAtSignup, Account] = {

    val company = EmailValidationService.getLowercasedCompanyNameFromEmail(newAccount.email)(Logger = Logger)

    var orgKeyName = Helpers.generateOrgTrackingSubdomainKey(orgName = company)

    val countOfSameTrackingKey = accountService.checkOrgTrackingSubdomainKey(checkKey = orgKeyName)

    if (countOfSameTrackingKey > 0) {

      /*
       12th Apr 2022 appending Datetime Seconds
       Since we are allowing delete organization
       we hit duplicate key value violates unique constraint on tracking_subdomain_key
      */
      orgKeyName = orgKeyName + countOfSameTrackingKey + DateTime.now().getMillis / 1000

    }

    Logger.info(s"cookies found during signup: email: ${newAccount.email} company: ${newAccount.email} :: cookies: $cookieString :: query: $queryStr")


    given rolePermissionDataDAOV2Implicit :RolePermissionDataDAOV2 = rolePermissionDataDAOV2

    accountService.create(
      logger = Logger,
      data = AccountCreateForm(
        email = newAccount.email,
        password = Some(newAccount.password),
        first_name = "",
        last_name = "",
        company = company,
        invite_code = newAccount.invite_code,
        timezone = newAccount.timezone,
        country_code = newAccount.country_code,
        g_response = newAccount.g_response,
        rs_code_used = None // fixme: remove this column from db completely
      ),
      orgKeyName = orgKeyName,

      utmData = utmData,
      signup_type = SignupType.Password,
      sendEmailStatus = sendEmailStatus,
      admitadCreatedAt = admitadCreatedAt,
      firstPromoterCreatedAt = firstPromoterCreatedAt
    ) match {


      case Failure(e) =>

        Left(FailedAtSignup.FailedToCreateAccount(e))

      case Success(savedAccountOpt) =>

        savedAccountOpt match {

          case None =>
            Left(FailedAtSignup.NoAccountFoundAfterCreating)

          case Some(savedAccount) =>


            val dashboardDomain = AppConfig.dashboardDomain
            val isDevSetup = dashboardDomain.contains(".sreml.com")
            val isInvitedUser = newAccount.invite_code.isDefined

            if (!isDevSetup && isInvitedUser) {

              emailNotificationService.sendMailFromAdmin(
                toEmail = "<EMAIL>",
                toName = Some("Heaplabs Team"),

                subject = s"SR Invited User: ${newAccount.email} : ${savedAccountOpt.get.org.name}",
                body =
                  s"""
                           <br/>Name: ${Helpers.getAccountName(a = savedAccount)}
                           <br/>Email: ${savedAccount.email}
                           <br/>Company: ${savedAccount.org.name}
                           <br/>Current Plan: ${savedAccount.org.plan.plan_id}
                           <br/>Total sending email accounts: ${savedAccount.org.counts.total_sending_email_accounts}
                           <br/>Current sending email accounts: ${savedAccount.org.counts.current_sending_email_accounts}
                           <br/>Account Id (Org Id): ${savedAccount.internal_id} (${savedAccount.org.id})

                        """.stripMargin
              )

            }
            Right(savedAccount)
        }


    }

  }

  def handleLoginChallenge(loginChallenge: Option[String], subject: String)(
    implicit ws: WSClient,
    ec: ExecutionContext,
    Logger: SRLogger
  ): Future[Either[HandleLoginError, String]] = {

    if (loginChallenge.isEmpty) {
      Logger.fatal(s"Login challenge value ${loginChallenge.toString}")
      Future.successful(Left(HandleLoginError.LoginChallengeNotFound))
    } else {
      commonAuthService.acceptLogin(
        loginChallenge = loginChallenge.get,
        subject = subject,
        remember = AppConfigCommonAuth.remember,
        remember_for_duration_in_seconds = AppConfigCommonAuth.remember_for_duration_in_seconds
      )(ws = ws, ec = ec, Logger = Logger).map(res =>
        Right(res)
      ).recover { case e =>
        Left(HandleLoginError.AcceptLoginRequestFailed(e))
      }
    }

  }

  def getUserByUuidResponse(
                            accountUuid: AccountUuid,
                            orgId: OrgId
                           )(using logger: SRLogger): Either[UsersByIdError,UserFromAccountApiResponse] = {
    srUuidService.getAccountIdFromUuid(
      accountUuid = accountUuid,
      orgId = orgId
    ) match {
      case Failure(err) =>
        if (err.getMessage == CONSTANTS.APP_ERR_MSGS.UUID_NOT_FOUND_ERR_MSG) {
          Left(UsersByIdError.BadRequestError(s"User Id not found ${accountUuid}"))
        } else {
          Left(UsersByIdError.ServerError(err))
        }
      case Success(accountId: AccountId) =>
        accountDAO.getUserByAccountId(
          accountId = accountId,
          orgId = orgId
        ) match {
          case Failure(err) =>
            Left(UsersByIdError.ServerError(err))
          case Success(userFromAccountApiResponse) =>
            Right(userFromAccountApiResponse)
        }
    }
  }

  def getUserByUuid(
                     loggedInAccount: Account,
                     accountUuid: AccountUuid,
                     orgId: OrgId,
                     teamId: TeamId
                   )(using logger: SRLogger):Either[UsersByIdError,UserFromAccountApiResponse] = {
    if(AuthUtils._isRoleAgencyAdminOrOwner(loggedInAccount)){
      getUserByUuidResponse(
        accountUuid = accountUuid,
        orgId = orgId
      )
    } else {
      val teamMembers = accountDAO.getTeamMatesByTeamId(teamId = teamId).map(t=>t.user_uuid);
      if(teamMembers.contains(accountUuid)){
        getUserByUuidResponse(
          accountUuid = accountUuid,
          orgId = orgId
        )
      } else {
        Left(UsersByIdError.BadRequestError("No User Exist For the give id"))
      }
    }
  }

  def updateApiKey(
                    role: Option[RolePermissionDataV2],
                    enable_internal_email_accounts_api_for_warmuphero: Boolean,
                    accountId: AccountId,
                    orgId: OrgId,
                    teamId: Option[TeamId],
                    keyType: SRApiKeyType
                  )(using Logger: SRLogger): Try[Option[String]] = {

    val updateKeyRes: Try[Option[String]] = keyType match {

      case SRApiKeyType.SRUserLevelKey
           | SRApiKeyType.SRTeamUserLevelKey
           | SRApiKeyType.SRProspectDaddyKey
           | SRApiKeyType.SRZapierKey =>

        accountDAO.updateApiKey(
          accountId = accountId,
          orgId = orgId,
          teamId = teamId,
          keyType = keyType
        )

      case SRApiKeyType.SRWarmupBoxKey =>

        if (!enable_internal_email_accounts_api_for_warmuphero) {

          accountDAO.updateApiKey(
            accountId = accountId,
            orgId = orgId,
            teamId = teamId,
            keyType = keyType
          )

        } else {

          role match {

            case None =>

              Success(None)

            case Some(userRole) =>

              userRole.role_name match {

                case TeamAccountRole.ADMIN
                     | TeamAccountRole.MEMBER =>

                  val errMsg = s"Please contact your SmartReach Owner or Agency Admin to check or update the WarmupHero API Key. Your role (${userRole.role_name}) does not have access to do this."

                  Logger.info(
                    msg = s"$errMsg. accountId: $accountId :: role: ${userRole.role_name} :: orgId: ${orgId}"
                  )

                  Failure(new Exception(errMsg))

                case TeamAccountRole.OWNER
                     | TeamAccountRole.AGENCY_ADMIN =>

                  accountDAO.updateApiKey(
                    accountId = accountId,
                    orgId = orgId,
                    teamId = teamId,
                    keyType = keyType
                  )

              }

          }

        }

    }

    updateKeyRes.map(
      _.map(
        updatedKey => {
          resetUserCacheUtil._resetTeamCache(accountId.id)
          updatedKey
        }
      )
    )

  }

  def getRb2bIntegrationLink(
                            accountId: AccountId,
                            orgId: OrgId,
                            teamId: TeamId,
                            teamUuid: TeamUuid
                            )(using Logger: SRLogger): Try[String] = {
    accountDAO.getUserLevelApiKey(accountId = accountId) match {
      case Failure(e) =>

        Logger.fatal("getUserLevelApiKey", err = e)

        Failure(new Exception(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER))

      case Success(None) =>

        accountDAO.updateApiKey(
          accountId = accountId,
          orgId = orgId,
          teamId = Some(teamId),
          keyType = SRApiKeyType.SRUserLevelKey
        ) match {
          case Failure(exception) =>
            Failure(new Exception(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER))
          case Success(None) =>
            Failure(new Exception(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER))
          case Success(Some(newKey)) =>

            Success(s"${AppConfig.apiDomain}/api/v3/prospect_from_rb2b?api_key=$newKey&team_id=${teamUuid.uuid}&source=rb2b")

        }

      case Success(Some(key)) =>

        Success(
          s"${AppConfig.apiDomain}/api/v3/prospect_from_rb2b?api_key=$key&team_id=${teamUuid.uuid}&source=rb2b"
        )

    }
  }

}
