package api.accounts.service

import api.accounts.models.{OrgUuid, ProspectAccountStatus}
import api.accounts.{AccountUuid, OrganizationRole, ResetTeamCache}
import api.sr_audit_logs.models.ProspectEventDataType
import org.joda.time.DateTime
import play.api.libs.json.JodaReads._
import play.api.libs.json.JodaWrites._
import play.api.libs.json.{JsError, JsResult, JsSuccess, JsValue, <PERSON>son, Reads, Writes}
import scalikejdbc.WrappedResultSet
import scalikejdbc.jodatime.JodaWrappedResultSet.fromWrappedResultSetToJodaWrappedResultSet

import scala.util.{Failure, Success, Try}


case class UserFromAccountApiResponse(
  id : AccountUuid,
  first_name : String,
  last_name : String,
  org_id : OrgUuid,
  status: ProspectAccountStatus,
  email: String,
  created_at : Long,
  timezone : Option[String]
) extends ProspectEventDataType
case object UserFromAccountApiResponse {
  def fromDb(rs: WrappedResultSet): UserFromAccountApiResponse = {
    UserFromAccountApiResponse(
      id = AccountUuid(rs.string("uuid")),
      first_name = rs.string("first_name"),
      last_name = rs.string("last_name"),
      email = rs.string("email"),
      org_id = OrgUuid(rs.string("org_uuid")),
      status = ProspectAccountStatus.fromString(rs.boolean("active")),
      created_at = rs.jodaDateTime("created_at").getMillis,
      timezone = rs.stringOpt("timezone"),
    )
  }

  given writes: Writes[UserFromAccountApiResponse] = new Writes[UserFromAccountApiResponse] {
    def writes(data: UserFromAccountApiResponse) = {
      Json.obj(
        "object" -> "user",
        "id" -> data.id,
        "first_name" -> data.first_name,
        "last_name" -> data.last_name,
        "org_id" -> data.org_id,
        "status" -> data.status,
        "email" -> data.email,
        "created_at" -> data.created_at,
        "timezone" -> data.timezone

      )
    }
  }

  given reads: Reads[UserFromAccountApiResponse] = new Reads[UserFromAccountApiResponse] {
    override def reads(json: JsValue): JsResult[UserFromAccountApiResponse] = Try {
      UserFromAccountApiResponse(
        id = (json \ "id").as[AccountUuid],
        first_name = (json \ "first_name").as[String],
        last_name = (json \ "last_name").as[String],
        org_id = (json \ "org_id").as[OrgUuid],
        status = (json \ "active").as[ProspectAccountStatus],
        email = (json \ "email").as[String],
        created_at = (json \ "created_at").as[Long],
        timezone = (json \ "timezone").asOpt[String]
      )
    } match {
      case Failure(e) => JsError(e.getMessage)
      case Success(userFromAccountApiResponse) => JsSuccess(userFromAccountApiResponse)
    }
  }
}
object CredentialsAuthApiService {
  def constructResponseGetUsersFromAccount(
                                            users: Seq[ResetTeamCache]
                                          ): Seq[UserFromAccountApiResponse] = {
    users.map(
      user =>
        UserFromAccountApiResponse(
          id = user.user_uuid,
          first_name = user.first_name,
          last_name = user.last_name,
          org_id = user.org_uuid,
          status = ProspectAccountStatus.fromString(user.active),
          email = user.email,
          created_at = user.created_at.getMillis,
          timezone = user.timezone
        )
    )
  }
}
