package api.accounts.service
import api.AppConfig
import api.accounts.MicrosoftOAuth
import play.api.libs.ws.WSClient
import utils.{SRAppConfig, SRLogger}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

class NewAuthMicrosoftSignupService(
                                       microsoftOAuth: MicrosoftOAuth,
                                       implicit val ws: WSClient,
                                       implicit val ec: ExecutionContext
                                     ) extends NewAuthSignupThroughHostService{
  override def authorizationUrl(
                                 state: String,
                                 emailAddress: String,
                                 loginChallenge:Option[String]
                               )(using Logger: SRLogger): Future[String] = {
    Future(microsoftOAuth.authorizationUrl(
        state = if(loginChallenge.isDefined) s"${loginChallenge.get}*${state}}" else state,
        s = SRAppConfig.microsoftOAuthSettingsForNewAuth,
        emailAddress = Some(emailAddress),
        promptLogin = false,
      ))
  }

  override def fetchProfileDataUsingAuthCode(
                                              code: String,
                                              emailAddress: Option[String]
                                            )(using Logger: SRLogger): Future[ProfileForSignup] = {
    val result = for {
      accessToken <-
        microsoftOAuth.getAccessToken(
        code = code,
        Logger = Logger,
        s = SRAppConfig.microsoftOAuthSettingsForNewAuth // Use AppConfig.microsoftOAuthSettingsForNewAuth, for NewAuth
        )

      profile <-
        microsoftOAuth.getProfile(
          accessToken = accessToken.get.access_token,
          Logger = Logger
        )

    } yield {

      ProfileForSignup(
        profileName = profile.userPrincipalName,
        givenName = profile.givenName.getOrElse(profile.displayName),
        familyName = profile.surname.getOrElse(""),
        email = profile.mail
      )

    }
    result
  }
}
