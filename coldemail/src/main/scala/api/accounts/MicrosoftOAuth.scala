package api.accounts

import api.AppConfig
import api.emails.EmailSettingDAO
import io.smartreach.esp.api.emails.EmailSettingId
import io.smartreach.esp.api.microsoftOAuth.{EmailSettingUpdateAccessToken, MicrosoftOAuthApi}
import play.api.Logging

import scala.util.Try


class MicrosoftOAuth(
  emailSettingDAO: EmailSettingDAO,
) extends Logging with MicrosoftOAuthApi {

  override def updateAccessTokenAndRefreshToken(emailSettingId: EmailSettingId, data: EmailSettingUpdateAccessToken): Try[Option[EmailSettingId]] = {
    emailSettingDAO.updateAccessTokenAndRefreshToken(emailSettingId, data).map(_.flatMap(_.id))
  }
}
