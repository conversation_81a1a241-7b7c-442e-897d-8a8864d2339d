package api.accounts

import api.accounts
import play.api.libs.json.Format
import utils.enum_sr_utils.EnumUtils

object ReplyHandling extends Enumeration {
  type ReplyHandling = Value
  val PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY = Value("pause_all_campaigns")
  val PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY = Value("pause_specific_campaign")

  val PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY = Value("pause_all_prospect_account_campaigns")

  given format: Format[accounts.ReplyHandling.Value] = EnumUtils.enumFormat(ReplyHandling)
}
