package api.prospect_daddy.model

import api.campaigns.services.CampaignId
import api.prospects.ProspectCreateFormData
import play.api.libs.json.{Json, Reads, Writes}

case class DirectProspectForm(
                               prospects: Seq[ProspectCreateFormData],
                               campaign_id: Option[CampaignId] = None,
                               list_name: Option[String] = None,
                             )

object DirectProspectForm {
  implicit val reads: Reads[DirectProspectForm] = Json.reads[DirectProspectForm]
  implicit val writes: Writes[DirectProspectForm] = Json.writes[DirectProspectForm]
}

