package api.rep_mail_servers.dao

import api.accounts.models.OrgId
import api.rep_mail_servers.models.{OrgLevelReportForMailServers, RepMailServer, ServerLevelReportForMailServers}
import app_services.blacklist_monitoring.models.{BlacklistCheckResult, BlacklistCheckStatus}
import io.sr.billing_common.models.PlanType
import org.joda.time.DateTime
import org.postgresql.util.PGobject
import play.api.libs.json.Json
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import utils.SRLogger

import scala.util.{Success, Try}

class SrMailServerDao {


  def getAllServers(
    logger: SRLogger
  ): Try[Seq[RepMailServer]] = Try {

    val servers = DB readOnly {
      implicit session =>
        sql"""select * from rep_mail_servers WHERE active"""
          .map(rs => RepMailServer(
            id = rs.int("id"),
            public_ip = rs.string("public_ip").trim,
            reverse_dns = rs.string("reverse_dns").trim,
            sending_score = rs.int("sending_score"),
            sending_score_threshold = rs.int("sending_score_threshold")
          ))
          .list
          .apply()
    }

    servers

  }

  def getAllActiveNotDedicatedServers(
                                       logger: SRLogger
                                     ): Try[Seq[RepMailServer]] = Try {

    val servers = DB readOnly {
      implicit session =>
        sql"""
             select * from rep_mail_servers
             WHERE active
             AND NOT is_dedicated
             AND overall_blacklist_status = ${BlacklistCheckStatus.PASSED.toString}
             """
          .map(rs => RepMailServer(
            id = rs.int("id"),
            public_ip = rs.string("public_ip").trim,
            reverse_dns = rs.string("reverse_dns").trim,
            sending_score = rs.int("sending_score"),
            sending_score_threshold = rs.int("sending_score_threshold")
          ))
          .list
          .apply()
    }

    servers

  }

  def getServersForBlacklistCheck(
    lastCheckedForBlacklistBefore: DateTime
  ): Try[Seq[RepMailServer]] = Try {

    val servers = DB readOnly {
      implicit session =>
        sql"""select * from rep_mail_servers
             where
              (
                last_blacklist_check_at is null
                OR
                last_blacklist_check_at < $lastCheckedForBlacklistBefore
              )
              and active
           """
          .map(rs => RepMailServer(
            id = rs.int("id"),
            public_ip = rs.string("public_ip").trim,
            reverse_dns = rs.string("reverse_dns").trim,
            sending_score = rs.int("sending_score"),
            sending_score_threshold = rs.int("sending_score_threshold")
          ))
          .list
          .apply()
    }

    servers

  }

  def updateBlacklistCheckStatus(
    overallBlacklistCheckStatus: BlacklistCheckStatus,
    domainCheckResult: BlacklistCheckResult,
    ipCheckResult: BlacklistCheckResult,
    domain: String
  ): Try[Int] = Try {

    DB.autoCommit { implicit session =>

      sql"""
           update rep_mail_servers set

             last_blacklist_check_at = now(),

             overall_blacklist_status = ${overallBlacklistCheckStatus.toString},

             domain_blacklist_status = ${domainCheckResult.status.toString},
             domain_blacklist_failure_description = ${domainCheckResult.failureDescription},
             domain_blacklist_full_result = to_jsonb(${domainCheckResult.fullResult.toString()}::jsonb),

             ip_blacklist_status = ${ipCheckResult.status.toString},
             ip_blacklist_failure_description = ${ipCheckResult.failureDescription},
             ip_blacklist_full_result = to_jsonb(${ipCheckResult.fullResult.toString()}::jsonb)

           where

             reverse_dns = $domain
         """
        .update
        .apply()

    }
  }


  def getReportForMailServerForInternalReport(
                                               sr_rep_mail_server_id: Int,
                                               sr_rep_mail_server_name: String,
                                               from: DateTime,
                                               till: DateTime
                                             ): Try[Option[ServerLevelReportForMailServers]] = Try {
    val org_level = DB readOnly { implicit session =>
      sql"""

          SELECT
              org.id as org_id,
              org.name as org_name,
              COUNT(DISTINCT (CASE WHEN e.sent IS TRUE THEN e.prospect_id END)) AS prospect_total_sent,
              COUNT(DISTINCT (CASE WHEN e.sent and c.open_tracking_enabled IS TRUE THEN e.prospect_id END)) AS prospect_total_sent_with_open_tracking,
              COUNT(DISTINCT (CASE WHEN e.opened and c.open_tracking_enabled IS TRUE THEN e.prospect_id END)) AS prospect_total_open,
              COUNT(DISTINCT (CASE WHEN e.clicked IS TRUE THEN e.prospect_id END)) AS prospect_total_clicks,
              COUNT(DISTINCT (CASE WHEN e.replied IS TRUE THEN e.prospect_id END)) AS prospect_total_replies,
              COUNT(DISTINCT (CASE WHEN e.bounced IS TRUE THEN e.prospect_id END)) AS prospect_total_bounced,
              COUNT( (CASE WHEN e.sent IS TRUE THEN e.id END)) AS email_total_sent,
              COUNT( (CASE WHEN e.sent and c.open_tracking_enabled IS TRUE THEN e.id END)) AS email_total_sent_with_open_tracking,
              COUNT( (CASE WHEN e.opened and c.open_tracking_enabled IS TRUE THEN e.id END)) AS email_total_open,
              COUNT( (CASE WHEN e.clicked IS TRUE THEN e.id END)) AS email_total_clicks,
              COUNT( (CASE WHEN e.replied IS TRUE THEN e.id END)) AS email_total_replies,
              COUNT( (CASE WHEN e.bounced IS TRUE THEN e.id END)) AS email_total_bounced
          FROM emails_scheduled e
          LEFT JOIN campaigns c ON c.team_id = e.team_id and c.id = e.campaign_id
          INNER JOIN teams t ON t.id = e.team_id
          INNER JOIN organizations org ON org.id = t.org_id

          WHERE
          e.sent
          AND e.scheduled_from_campaign
          AND org.active
          AND org.plan_type = ${PlanType.PAID.toString}
          AND e.rep_mail_server_id = $sr_rep_mail_server_id

          AND e.sent_at > $from
          AND e.sent_at < $till
          GROUP BY org.id;

         """
        .map(rs => OrgLevelReportForMailServers(
          org_id = OrgId(rs.long("org_id")),
          org_name = rs.string("org_name"),
          prospect_total_sent = rs.int("prospect_total_sent"),
          prospect_total_sent_with_open_tracking = rs.int("prospect_total_sent_with_open_tracking"),
          prospect_total_open = rs.int("prospect_total_open"),
          prospect_total_clicks = rs.int("prospect_total_clicks"),
          prospect_total_replies = rs.int("prospect_total_replies"),
          prospect_total_bounced = rs.int("prospect_total_bounced"),
          email_total_sent = rs.int("email_total_sent"),
          email_total_sent_with_open_tracking = rs.int("email_total_sent_with_open_tracking"),
          email_total_open = rs.int("email_total_open"),
          email_total_clicks = rs.int("email_total_clicks"),
          email_total_replies = rs.int("email_total_replies"),
          email_total_bounced = rs.int("email_total_bounced"),
          from = from,
          till = till
        ))
        .list
        .apply()

    }

    Some(
      ServerLevelReportForMailServers(
        rep_mail_server_id = sr_rep_mail_server_id,
        rep_mail_server_name = sr_rep_mail_server_name,
        org_data = org_level,
        last_checked = DateTime.now(),
        prospect_total_sent = org_level.map(_.prospect_total_sent).sum,
        prospect_total_sent_with_open_tracking = org_level.map(_.prospect_total_sent_with_open_tracking).sum,
        prospect_total_open = org_level.map(_.prospect_total_open).sum,
        prospect_total_clicks = org_level.map(_.prospect_total_clicks).sum,
        prospect_total_replies = org_level.map(_.prospect_total_replies).sum,
        prospect_total_bounced = org_level.map(_.prospect_total_bounced).sum,
        email_total_sent = org_level.map(_.email_total_sent).sum,
        email_total_sent_with_open_tracking = org_level.map(_.email_total_sent_with_open_tracking).sum,
        email_total_open = org_level.map(_.email_total_open).sum,
        email_total_clicks = org_level.map(_.email_total_clicks).sum,
        email_total_replies = org_level.map(_.email_total_replies).sum,
        email_total_bounced = org_level.map(_.email_total_bounced).sum,
        from = from,
        till = till
      )
    )

  }

  def insertIntoSrServerReport(
                                data: ServerLevelReportForMailServers
                              ) = Try {
    DB autoCommit {implicit session =>
      sql"""
          INSERT INTO sr_server_report
          (
           rep_mail_servers_id,
           created_at,
           from_time,
           till_time,
           report
          )
           VALUES
           (
            ${data.rep_mail_server_id},
            ${data.last_checked},
            ${data.from},
            ${data.till},
            to_jsonb(${Json.toJson(data).toString}::jsonb)
           )
          RETURNING id;
      """
        .execute
        .apply()
    }
  }


  def getFromSrServerReportForSupport(
                                       rep_mail_server_id: Int
                                     ): Try[List[ServerLevelReportForMailServers]] = Try {
    DB readOnly { implicit session =>
      sql"""
          select report
          from
          sr_server_report
          where
          rep_mail_servers_id = $rep_mail_server_id
          ORDER BY till_time desc
          ;
      """
        .map(rs => Json.parse(rs.any("report").asInstanceOf[PGobject].getValue).validate[ServerLevelReportForMailServers].get)
        .list
        .apply()
    }
  }

  def getSrServerReportBasedOnTime(
                                   rep_mail_server_id: Int,
                                   report_from: DateTime,
                                   report_till: DateTime
                                 ): Try[List[ServerLevelReportForMailServers]] = Try {
    DB readOnly { implicit session =>
      sql"""
          select report
          from
          sr_server_report
          where
          rep_mail_servers_id = $rep_mail_server_id
          AND till_time > $report_from
          AND till_time < $report_till;
      """
        .map(rs => Json.parse(rs.any("report").asInstanceOf[PGobject].getValue).validate[ServerLevelReportForMailServers].get)
        .list
        .apply()
    }
  }

  def getForOrgIfDedicated(orgId: OrgId): Try[Option[RepMailServer]] = Try{
    DB readOnly {
      implicit session =>
        sql"""select rms.*
              from rep_mail_servers rms
              inner join organizations o on o.rep_mail_server_id = rms.id
             where
             rms.is_dedicated
             AND o.id = ${orgId.id}
              and rms.active;
           """
          .map(rs => RepMailServer(
            id = rs.int("id"),
            public_ip = rs.string("public_ip").trim,
            reverse_dns = rs.string("reverse_dns").trim,
            sending_score = rs.int("sending_score"),
            sending_score_threshold = rs.int("sending_score_threshold")
          ))
          .single
          .apply()
    }
  }

}
