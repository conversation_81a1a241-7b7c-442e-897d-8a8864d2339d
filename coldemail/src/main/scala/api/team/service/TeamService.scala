package api.team.service

import api.{AppConfig, CONSTANTS}

import scala.util.{Failure, Success, Try}
import api.accounts.{Account, TeamAccount, TeamAccountRole, TeamId, TeamMember, TeamMetaData}
import api.accounts.dao.TeamsDAO
import api.accounts.models.{AccountId, OrgId, TeamSRAIFlags}
import utils.{Help<PERSON>, SRLogger}
import api.calendar_app.CalendarTeamId
import api.calendar_app.models.CalendarTeamNameAndTeamId
import api.general.GeneralSettingService
import api.prospects.InferredQueryTimeline
import api.team.TeamUuid
import api.team.dao_service.TeamDAOService
import io.sr.billing_common.models.PlanID
import play.api.libs.json.JsValue
import play.api.libs.ws.WSClient
import scalikejdbc.DBSession
import utils.featureflags.services.SrFeatureFlags
import utils.pagination.GetDataErrorTrait
import utils.uuid.{SrId, SrIdentifier, SrUuid, SrUuidUtils}
import utils.uuid.services.SrUuidService
import utils_deploy.rolling_updates.models.{SrRollingUpdateFeature, TeamDataForRollingUpdate}

import scala.concurrent.{ExecutionContext, Future}

sealed trait GetTeamByIdError

object GetTeamByIdError {

  case class TeamNotFound(msg: String) extends GetTeamByIdError

  case class UnauthorizedError(msg: String) extends GetTeamByIdError

  case class DBFailure(err: Throwable) extends GetTeamByIdError

  case object ServerError extends GetTeamByIdError

}

sealed trait TeamsListingError extends GetDataErrorTrait

object TeamsListingError {

  case class InvalidParams(msg: String) extends TeamsListingError

  case class DBFailure(err: Throwable) extends TeamsListingError

  case class PaginationError(msg: String) extends TeamsListingError

}

class TeamService(
                   teamDAO: TeamsDAO,
                   teamDAOService: TeamDAOService,
//                   srUuidUtils: SrUuidUtils,
                   srUuidService: SrUuidService,
                   teamsListingPaginationService: TeamsListingPaginationService
                 ){

  def checkIfTeamIsDeleted(
                           teamId: TeamId
                         ): Try[Boolean] = {
    teamDAO.checkIfTeamIsDeleted(
      teamId = teamId
    )
  }


  def getTeamsOrderByOrgPlanType: Try[List[TeamId]] = {
    teamDAO.getTeamsOrderByOrgPlanType
  }

  def getAllActiveInactiveTeamsAcrossOrgForMigration: Try[List[TeamId]] = {
    teamDAO.getAllActiveAndInactiveTeamsAcrossOrgForMigration
  }

  def getTeamMetaData(teamId: Long)(using logger:SRLogger): Try[TeamMetaData] = {
    teamDAO.getTeamMetaData(teamId = teamId) match {

      case Failure(err) =>
        logger.fatal(s"An error occured while fetching details from Db for teamID:${teamId}",err = err)
        Failure(new Exception(s"An error occured while fetching details from Db for teamID:${teamId}"))

      case Success(teamMetaData) =>
        Success(teamMetaData)

    }
  }



//  private def checkIfEmailNotCompulsoryEnabled(
//                                      teamId: TeamId,
//
//                                      )(using logger: SRLogger) ={
//
//    getTeamDataForRollingUpdates(
//      teamId = teamId
//    ) match {
//      case Failure(exception) =>
//        logger.fatal(s"Error while finding team", err = exception)
//        // do not send early update
//        false
//
//      case Success(None) =>
//        logger.fatal(s"error teamNotFound")
//        // do not send early update
//        false
//
//      case Success(Some(team)) =>
//         TeamDataForRollingUpdate.shouldAllowEarlyRollingUpdate(
//          team = team,
//          feature = SrRollingUpdateFeature.EmailNotCompulsory,
//        )
//
//    }
//  }

  def createTeamsAccount(
                           teamId: TeamId,
                           accountId: AccountId,
                           userRoleId: Long,
                           roleName: TeamAccountRole,
                           extensionKey: String
                         )(implicit session: DBSession): Try[Long] = teamDAOService.createTeamsAccount(
    teamId = teamId,
    accountId = accountId,
    userRoleId = userRoleId,
    roleName = roleName,
    extensionKey = extensionKey
  )

  def getTeamsListing(
                       accountId: AccountId,
                       active: Option[Boolean],
                       parsedParams: Map[String, Seq[String]]
                     )(using Logger: SRLogger): Either[TeamsListingError, TeamsListingApiResult] = {

    TeamService.validateTeamsListingQueryParams(parsedParams = parsedParams) match {

      case Failure(e) => Left(TeamsListingError.InvalidParams(e.getMessage))

      case Success(filters: TeamsListingAPIParams) =>

        teamsListingPaginationService.findTeamsListingPage(
          accountId = accountId,
          filters = filters,
          active = active
        )

    }
  }

  def updateTeamMetadata(teamId: Long, metaData: TeamMetaData)(using logger : SRLogger):Try[Int] = {

    teamDAO.updateTeamMetaData(teamId = teamId, metaData = metaData) match {

      case Failure(err) =>
        logger.fatal(s"An error occurred while updating metadata in db for teamId : ${teamId}",err = err)
        Failure(new Exception(s"An error occurred while updating metadata in db for teamId: ${teamId}"))

      case Success(value) => Success(value)
    }
  }

  def updateCalendarTeamData(
                                 orgId :OrgId,
                                 teamId : TeamId,
                                 calendarTeamId : CalendarTeamId,
                                 calendarTeamName: String
                               ): Try[Int] = {

    teamDAO.updateCalendarTeamData(
      orgId = orgId,
      teamId = teamId,
      calendarTeamId = calendarTeamId,
      calendarTeamName = calendarTeamName
    )

  }

  def getTeamById(
                   teamUuid: TeamUuid,
                   account: Account,
                   orgId: OrgId
                 )(using Logger: SRLogger):Either[GetTeamByIdError, TeamAccount] = {

    getTeamIdFromUuid(
      teamUuid = teamUuid,
      orgId = orgId
    ) match {

      case Failure(err) =>

        if(err.getMessage.contains(CONSTANTS.APP_ERR_MSGS.UUID_NOT_FOUND_ERR_MSG)) {
          Left(GetTeamByIdError.TeamNotFound(msg = "Please pass a valid team_id"))
        } else {
          Logger.fatal(msg = s"[TeamService.getTeamById getTeamIdFromUuid] teamUuid::$teamUuid orgId::$orgId", err = err)
          Left(GetTeamByIdError.DBFailure(err))
        }

      case Success(teamId: TeamId) =>

        // find team from list of teams which user has access to
        account.teams.find(_.team_id == teamId.id) match {

          case None => Left(GetTeamByIdError.UnauthorizedError(msg = "You are not authorized to access this team data."))

          case Some(teamAccount: TeamAccount) =>

            // find user in the team
            teamAccount.access_members.find(_.user_id == account.internal_id) match {

              case None =>
                Logger.fatal(msg = s"User has access to TeamAccount but is not present in access_members list. accountUuid::${account.id} teamUuid::$teamUuid orgId::$orgId")
                Left(GetTeamByIdError.ServerError)

              case Some(_) =>

                Right(teamAccount)

            }

        }

    }

  }

  def getTeamUuidFromTeamId(
    team_id : TeamId,
    orgId: OrgId,
  )(using Logger: SRLogger): Try[TeamUuid] = {

    srUuidService.getTeamUuidFromId(
      teamId = team_id,
      orgId = orgId,
    )

  }

  def getTeamIdFromSrIdentifier(
                                 srIdentifier: SrIdentifier,
                                 orgId: OrgId
                               )(using Logger: SRLogger): Try[TeamId] = {
    srIdentifier match {

      case SrId(id) => Success(TeamId(id))

      case SrUuid(teamUuid) =>
        getTeamIdFromUuid(
          teamUuid = TeamUuid(teamUuid),
          orgId = orgId
        )
    }
  }

    def updateProspectTableColumnOrder(teamId: TeamId,
                                       isChangeInGlobalTable: Boolean,
                                       newColumnOrder: JsValue): Try[Int] = {
        teamDAO.updateProspectTableColumnOrder(
            teamId = teamId,
            isChangeInGlobalTable = isChangeInGlobalTable,
            newColumnOrder = newColumnOrder
        )
    }


    def getProspectTableColumnOrder(teamId: TeamId,
                                    isChangeInGlobalTable: Boolean
                                   ): Try[String] = {
        teamDAO.getProspectTableColumnOrder(
            teamId = teamId,
            isChangeInGlobalTable = isChangeInGlobalTable
        )
    }



    def getTeamIdFromUuid(
                         teamUuid: TeamUuid,
                         orgId: OrgId
                       )(using Logger: SRLogger): Try[TeamId] = {

    srUuidService.getTeamIdFromUuid(
      teamUuid = teamUuid,
      orgId = orgId
    )

  }

  def getCalendarDataFromTeams(
                                teamId: TeamId,
                                orgId: OrgId
                              ):Try[CalendarTeamNameAndTeamId] = {
    teamDAO.getCalendarDataFromTeams(teamId = teamId, orgId = orgId)
  }


  def checkIfWePushForSrAiApiCall(
                                   allow_using_sr_ai_api: Boolean,
                                   teamId: TeamId,
                                   plan_id: PlanID
                                 )(using Logger: SRLogger): Try[TeamSRAIFlags] = {
    if (allow_using_sr_ai_api) {
      teamDAOService.getSRAIFlagsForTeam(
        teamId = teamId
      )
    } else {

      Success(
        TeamSRAIFlags(
          allow_using_sr_ai_api_for_reply_sentiment = false,
          allow_using_sr_ai_api_for_ooo = false
        )
      )
    }
  }

  def updateSrAiFlagsForTeam(
    teamId: TeamId,
    flags: TeamSRAIFlags
  )(using Logger: SRLogger): Try[TeamSRAIFlags] = {

    teamDAOService.updateSRAIFlagsForTeam(
      teamId = teamId,
      flags = flags,
    )

  }

}

object TeamService {
  def validateTeamsListingQueryParams(
                                       parsedParams: Map[String, Seq[String]]
                                     ): Try[TeamsListingAPIParams] = {
    for {
      (is_first: Boolean, range: InferredQueryTimeline.Range) <- Helpers.getTimeLineRange(parsedParams = parsedParams)

    } yield {
      TeamsListingAPIParams(
        range,
        is_first
      )
    }
  }
}
