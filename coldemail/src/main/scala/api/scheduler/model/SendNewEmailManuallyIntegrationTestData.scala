package api.scheduler.model

import api.accounts.models.AccountProfileInfo
import api.accounts.{Account, AccountAccess, AccountMetadata, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData}
import api.emails.SendNewManualEmailV3
import io.smartreach.esp.api.emails.IEmailAddress
import io.sr.billing_common.models.{PlanID, PlanType}
import org.joda.time.DateTime

object SendNewEmailManuallyIntegrationTestData {


  case class InputData(
                        data: SendNewManualEmailV3,
                        org_id: Long,
                        teamId: Long,
                        loggedinInAccount: Account,
                        auditRequestLog: String,
                        version: String,
                        tiid: Option[String],
                        account_id: Long
                      )

  object InputData {

    private val data: SendNewManualEmailV3 = SendNewManualEmailV3(
      email_thread_id = None,

      // Extension flow : campaign step id will come from extension flow, while running manual email tasks
      campaign_step_id = None,

      sender_email_setting_id = SchedulerIntegrationTestUtils.schedulerIntegrationTestFor_staging_2.email_setting_id.emailSettingId,

      receiver_email_setting_id = Some(SchedulerIntegrationTestUtils.schedulerIntegrationTestFor_staging_2.email_setting_id.emailSettingId),

      // from: IEmailAddress,
      // reply_to: Option[IEmailAddress],

      to = Seq(IEmailAddress(
        name = None,
        email = "<EMAIL>"
      )),

      cc_emails = None,
      bcc_emails = None,

      body = "Hi this is body of email",
      subject = "Hi, this is subject of email",

      enable_open_tracking = Some(false),
      enable_click_tracking = Some(false),
      mark_as_done = None

    )

    private val org_id = SchedulerIntegrationTestUtils.schedulerIntegrationTestFor_staging_2.org_id.id

    private val accountId = SchedulerIntegrationTestUtils.schedulerIntegrationTestFor_staging_2.account_id.id

    private val teamId = SchedulerIntegrationTestUtils.schedulerIntegrationTestFor_staging_2.team_id.id


    private val auditRequestLogId = "log request Id hun main boom "

    private val version = "V3"

    private val tiid = None

    private val email = "<EMAIL>"

    private val accountMetadata: AccountMetadata = AccountMetadata(
      // account_ui_version = None,
      is_profile_onboarding_done = None
    )

    private val orgCountData_default: OrgCountData = OrgCountData(
      base_licence_count = 1,
      additional_licence_count = 1,
      current_sending_email_accounts = 1,
      total_sending_email_accounts = 1,
      additional_spam_tests = 1,
      max_li_manual_seats = 23,
      current_li_manual_seats = 2,
      max_prospect_limit_org = 1,
      max_li_automation_seats = 50,
      current_li_automation_seats = 25,
      current_prospect_sent_count_org = 1,
      max_phone_number_buying_limit_org = 0,
      max_calling_accounts = 100,
      current_calling_accounts = 100,
      max_crm_integrations = 100,
      current_crm_integrations = 10,
      max_client_teams = 10,
      current_client_teams = 13,
      current_active_client_teams = 10,
      max_prospects_saved = 14,
      current_prospects_saved = 12,

      max_purchased_domains = 2,
      current_purchased_domains = 1,

      max_purchased_email_accounts = 4,
      current_purchased_email_accounts = 1,

      max_purchased_zapmail_domains = 5,
      current_purchased_zapmail_domains = 4,

      max_purchased_zapmail_email_accounts = 4,
      current_purchased_zapmail_email_accounts = 2,

      current_active_team_inboxes = 1,

      has_active_call_campaigns = true
    )

    private val orgPlanFixture: OrgPlan = OrgPlan(
      new_prospects_paused_till = None,
      current_cycle_started_at = DateTime.now(),
      is_v2_business_plan = true,

      next_billing_date = Some(DateTime.now()),


      fs_account_id = None,
      stripe_customer_id = None,
      payment_gateway = None,
      plan_type = PlanType.PAID,
      plan_name = "pro",
      payment_due_invoice_link = None,
      payment_due_campaign_pause_at = None,
      plan_id = PlanID.PRO
    )

    private val minSeats = 10


    private val orgMetadata_default = OrgMetadata(
      allow_user_level_api_key = Some(false),
      ff_multichannel = Some(false),
      is_onboarding_done = Some(true),
      show_agency_admin_role = Some(false),
      show_agency_pricing = Some(false),
      enable_linkedin_automation = Some(false),
      enable_lead_finder = Some(false),
      show_linkedin_inbox = Some(false),
      enable_calendar = Some(false),
      enable_native_calling = Some(false),
      show_promo_option = Some(false),
      show_individual_plans = Some(true),
      show_business_plans = Some(false),
      show_business_pro_plan = Some(false),
      show_agency_email_plans = Some(false), show_agency_mc_plans = Some(false),
      show_enterprise_plans = Some(false),
      ff_emails_sent_report = Some(false),
      show_campaign_tags = Some(false),
      limit_on_prospect_accounts = Some(true),
      min_seats = Some(minSeats),
      show_campaign_send_start_report = Some(false),
      allowed_for_new_google_api_key = Some(false),
      allow_going_back_in_crm_status = Some(false),
      show_referral_program = Some(false),
      salesforce_sandbox_enabled = Some(false),
      show_sending_holiday_calendar = Some(false),
      enable_opportunities_pipeline = Some(false),
      increase_email_delay = Some(false)
    )



    private val orgSettings = OrgSettings(
      enable_ab_testing = true,
      disable_force_send = false,
      bulk_sender = false,
      allow_2fa = false,
      show_2fa_setting = false,
      enforce_2fa = false,
      allow_native_crm_integration = false,
        agency_option_allow_changing = false,
        agency_option_show = false
    )

    private val org = OrganizationWithCurrentData(
      id = org_id,
      name = "Shashank",
      owner_account_id = accountId,
      counts = orgCountData_default,
      settings = orgSettings,
      plan = orgPlanFixture,
      is_agency = true,
      trial_ends_at = DateTime.now().plusDays(10),
      error = None,
      error_code = None,
      paused_till = None,
      errors = Seq(),
      warnings = Seq(),
      via_referral = true,
      org_metadata = orgMetadata_default
    )

    private val profile: AccountProfileInfo = AccountProfileInfo(
      first_name = "Shashank",
      last_name = "Dwivedi",
      company = Some("ShashankD"),
      timezone = Some("IN"),
      country_code = Some("IN"),
      mobile_country_code = Some("+91"),
      mobile_number = Some(6386990563L),
      twofa_enabled = false,
      has_gauthenticator = false,
      weekly_report_emails = Some("<EMAIL>"),
      scheduled_for_deletion_at = None,
      onboarding_phone_number = Some("+916386990563L")
    )

    private val accountAdmin: Account = Account(
      id = AccountUuid("account_uuid"),
      internal_id = accountId,
      email = email,
      email_verification_code = None,
      email_verification_code_created_at = None,
      created_at = DateTime.now().minusDays(1000),
      first_name = Some("Shashank"),
      last_name = Some("Dwivedi"),
      company = Some("SD"),
      timezone = None,
      profile = profile,
      org_role = Some(OrganizationRole.OWNER),
      teams = Seq(),
      account_type = AccountType.AGENCY,
      org = org,
      active = true,
      email_notification_summary = "dSFA",
      account_metadata = accountMetadata,
      email_verified = true,
      signupType = None,
      account_access = AccountAccess(
        inbox_access = false
      ),
      calendar_account_data = None
    )

    private val loggedinAccount = accountAdmin


    val inputData: InputData = InputData(
      data = data,
      org_id = org_id,
      teamId = teamId,
      loggedinInAccount = loggedinAccount,
      auditRequestLog = auditRequestLogId,
      version = version,
      tiid = tiid,
      account_id = accountId
    )
  }


}
