package api.scheduler.model

import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.models.CampaignName
import io.smartreach.esp.api.emails.EmailSettingId

import scala.util.Random

case class CampaignEmailSettingData(
                                     receiver_email_setting_id: EmailSettingId,
                                     sender_email_setting_id: EmailSettingId
                                   )
case class CampaignLinkedinSettings(
                                     linkedin_account_setting_id: String
                                   )
case class CampaignCallSettings(
                                 phone_number: Option[String]
                               )

case class CampaignWhatsappSettings(
                                     phone_number: String
                                   )

case class CampaignSmsSettings(
                                     phone_number: String
                                   )
case class SchedulerInputForIntegrationTest(
                                             org_id: OrgId,
                                             account_id: AccountId,
                                             team_id: TeamId,
                                             ta_id: Long,
                                             campaign_name: CampaignName,
                                             prospect_categories_custom_not_categorized: Long,
                                             prospect_categories_custom_do_not_contact: Long,
                                             current_sending_email_accounts: Int,
                                             email_setting_id: EmailSettingId,
                                             schedule_from_time_sec: Int,
                                             schedule_till_time_sec: Int,
                                             prospect_email: String,
                                             timezone: String,
                                             enable_email_scheduler: <PERSON><PERSON>an,
                                             campaign_email_setting_data: CampaignEmailSettingData,
                                             campaign_linkedin_settings_data: CampaignLinkedinSettings,
                                             campaign_call_settings_data: CampaignCallSettings,
                                             campaign_whatsapp_settings_data: CampaignWhatsappSettings,
                                             campaign_sms_settings_data: CampaignSmsSettings
                                           )
object SchedulerIntegrationTestUtils {

  val schedulerIntegrationTestFor_staging_2: SchedulerInputForIntegrationTest = SchedulerInputForIntegrationTest(
    org_id = OrgId(20),
    account_id = AccountId(17),
    team_id = TeamId(15),
    ta_id = 17,
    timezone = "Asia/Kolkata",
    campaign_name = CampaignName(Random.alphanumeric.take(10).mkString("")),
    campaign_email_setting_data = CampaignEmailSettingData(
      receiver_email_setting_id = EmailSettingId(111),
      sender_email_setting_id = EmailSettingId(111)
    ),
    email_setting_id = EmailSettingId(111),
    prospect_categories_custom_not_categorized = 134,
    prospect_categories_custom_do_not_contact = 135,
    current_sending_email_accounts = 1,
    schedule_from_time_sec = 0,
    schedule_till_time_sec = 86399,
    prospect_email = Random.alphanumeric.take(10).mkString("") + "@gmail.com",
    campaign_call_settings_data = CampaignCallSettings(
      phone_number = None
    ),
    campaign_whatsapp_settings_data = CampaignWhatsappSettings(
      phone_number = ""
    ),
    campaign_linkedin_settings_data = CampaignLinkedinSettings(
      linkedin_account_setting_id = ""
    ),
    campaign_sms_settings_data = CampaignSmsSettings(
      phone_number = ""
    ),
    enable_email_scheduler = true
  )
}

