package api.tracking_host

import org.apache.pekko.actor.ActorSystem
import api.tracking_host.dao.CustomTrackingDomainDAO
import api.tracking_host.services.CustomTrackingDomainService
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}

import scala.concurrent.{ExecutionContext, Future}

case class CnameCheckMsg(
                        domainPointerName: String
                        )

class MqCnameUptimeCheck (
                     customTrackingDomainDAO: CustomTrackingDomainDAO,
                     customTrackingDomainService: CustomTrackingDomainService
                   ) extends SimpleMqServiceTrait[CnameCheckMsg]{

    override val queueBaseName: String = MQConfig.domainCnameUptimeBaseName
    override val prefetchCount: Int = MQConfig.domainCnameUptimeCheckPrefetchCount

    override def processMessage(msg: CnameCheckMsg)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = {

        val pointerName = msg.domainPointerName

        implicit lazy val logger: SRLogger = new SRLogger(logRequestId = s"MqCnameUptimeCheck :: processMessage :: $msg :: ")

         val res = for {
            isCnameUp : Boolean <- customTrackingDomainService.doCnameUptimeCheck(pointerName)
              .recover { e =>
                  logger.error(s"[MqCnameUptimeCheck] customTrackingDomainService.doCnameUptimeCheck failed for ${pointerName}")
                  false
              }

            _:Int <- Future.fromTry(customTrackingDomainDAO.updateUptimeStatus(
                domainPointerName = pointerName,
                isCnameUp = isCnameUp
            ))

            _ <- if(!isCnameUp) {
              Future.fromTry(customTrackingDomainService.sendCnameDownNotification(domainPointerName = pointerName) )
            } else {
            Future.successful({})
        }
        } yield {
            isCnameUp
        }

        res.map{_ =>
            logger.info(s"CNAME up check is done successfully for ${pointerName}")

        }.recover{ e =>
            logger.error(s"CNAME check failed for ${pointerName}")
        }
    }


    }
