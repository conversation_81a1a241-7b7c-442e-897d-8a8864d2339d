package api.tracking_host

import api.tracking_host.dao.{CustomTrackingDomainDAO, InactiveDomainForRemoval}
import org.apache.pekko.actor.ActorSystem
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.cloudflare.CloudflareService
import utils.email_notification.service.EmailNotificationService
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}

import scala.util.{Failure, Success}
import scala.concurrent.{ExecutionContext, Future}

case class MqInactiveCustomRecordDeleterMsg(
                                           message: InactiveDomainForRemoval
                                           )

class MqInactiveDomainCustomTrackingDeleter(
                                           cloudflareService: CloudflareService,
                                           customTrackingDomainDAO: CustomTrackingDomainDAO,
                                           emailNotificationService: EmailNotificationService
                                           ) extends SimpleMqServiceTrait[MqInactiveCustomRecordDeleterMsg]{

    override val queueBaseName: String = MQConfig.customTrackingDomainRecordDeleterBaseName
    override val prefetchCount: Int = MQConfig.customTrackingDomainRecordDeleterPrefetchCount

    override def processMessage(msg: MqInactiveCustomRecordDeleterMsg)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = {

        val domain = msg.message.domainHost

        implicit lazy val logger: SRLogger = new SRLogger(logRequestId = s"MqInactiveDomainCustomTrackingDeleter :: processMessage :: $msg :: ")

        cloudflareService.removeCustomHostName(
            zoneId = msg.message.cloudflareZoneId,
            identifier = msg.message.cloudflareIdentifier,
        ).map{
            case Left(e) =>
                logger.error(s"Failed to remove custom domain: ${msg.message.domainHost}  Team_id : ${msg.message.teamId.id} $e ")
            case Right(_) =>
                customTrackingDomainDAO.deleteCustomTrackingDomain(
                    domain = msg.message.domainHost,
                    cloudflareIdentifier = msg.message.cloudflareIdentifier
                ) match {
                    case Success(_) =>
                        logger.info(s"Custom tracking domain deleted successfully: $domain")

                        emailNotificationService.sendMailFromAdmin(
                            toEmail = "<EMAIL>",
                            toName = Some("SmartReach Support"),
                            ccEmail = Some("<EMAIL>"),
                            subject = s"Removed the custom tracking domain: $domain",
                            body =
                                s"""
                     <br/>Hi,
                     <br/> 
                     <br/> The custom tracking domain record for  $domain has been removed due to being associated with the inactive team.
                     <br/>domain : ${domain}
                     <br/>
                     <br/>TeamId  : ${msg.message.teamId}
                     <br/>
                     <br/>Regards,
                     <br/>SmartReach.io Team
                    """.stripMargin
                        )
                    case Failure(e) =>
                        logger.error(s"Failed to delete custom tracking domain: $e")
                }
//                logger.info(s"Custom domain removed successfully: $domain")
        }

    }

}
