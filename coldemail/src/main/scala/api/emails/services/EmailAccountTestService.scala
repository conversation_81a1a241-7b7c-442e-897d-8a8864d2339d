package api.emails.services

import org.apache.pekko.actor.ActorSystem
import api.{CONSTANTS, HelpfulErrorMessageTrait}
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.{AccountId, OrgId}
import api.accounts.{Account, PermissionRequest, TeamId}
import api.emails.models.{EmailSettingIntegrationLogsStage, HelpfulErrorMessageDuringEmailIntegration}
import api.emails.services.EmailAccountTestService.getHelpFulMessageForEmailIntegrationError
import api.emails.{EmailSetting, EmailSettingDAO, EmailSettingForm}
import api.free_email_domain.service.FreeEmailDomainListService
import api.prospects.dao.ProspectsEmailsDAO
import org.joda.time.DateTime
import play.api.libs.json.Json
import play.api.libs.ws.WSClient
import utils.{Help<PERSON>, SRLogger}
import utils.email.{EmailSendDetail, EmailSenderService, SmtpImapService}
import utils.logging.{SRInternalLog, SRLogStatus}
import io.smartreach.esp.api.emails.IEmailAddress
import io.smartreach.esp.utils.email.EmailHelperCommon

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

sealed trait EmailAccountTestSettingError

object EmailAccountTestSettingError {

  case object FreeDomainUseError extends EmailAccountTestSettingError

  case class ValidationError(err: Throwable) extends EmailAccountTestSettingError

  case class EmailExistsAsProspect(errMsg: String) extends EmailAccountTestSettingError

  case class TestError(errMessage: String, helpfulErrorData: Option[HelpfulErrorMessageTrait]) extends EmailAccountTestSettingError

}

sealed trait EmailAccountTestGmailASPError

object EmailAccountTestGmailASPError {

  case object ESPMustBeGmailAPI extends EmailAccountTestGmailASPError

  case class TestError(err: EmailAccountTestSettingError) extends EmailAccountTestGmailASPError

}

class EmailAccountTestService(
                               smtpImapService: SmtpImapService,
                               emailSenderService: EmailSenderService,
                               freeEmailDomainListService: FreeEmailDomainListService,
                               prospectsEmailsDAO: ProspectsEmailsDAO,
                               emailSettingDAO: EmailSettingDAO
                             ) {


  def insertEmailSettingIntegrationLogs(
                                         accountId: AccountId,
                                         orgId: OrgId,
                                         teamId: TeamId,
                                         email_address: Option[String],
                                         service_provider: Option[EmailServiceProvider],
                                         completed: Boolean,
                                         email_integration_stage: EmailSettingIntegrationLogsStage,
                                         error_occurred: Option[String]
                                       )(using logger: SRLogger): Try[Option[Long]] = {
    emailSettingDAO.insertEmailSettingIntegrationAttempt(
      orgId = orgId,
      teamId = teamId,
      accountId = accountId,
      completed = completed,
      email_address = email_address,
      serviceProvider = service_provider.map(p => p.toString),
      error_occurred = error_occurred,
      email_integration_stage = email_integration_stage
    )
  }

  def testSettings[A](
                       data: EmailSettingForm,
                       account: Account,
                       teamId: TeamId,

                       // This change is part a hotfix to handle gmail sending issues on 31-Mar-2022
                       // we will refactor out the "request" argument in a future PR
                       request: PermissionRequest[A]
                     )(
                       implicit ec: ExecutionContext,
                       Logger: SRLogger,
                       wSClient: WSClient,
                       system: ActorSystem
                     ): Future[Either[EmailAccountTestSettingError, Boolean]] = {
    var error_occurred: Option[String] = None
    val result = prospectsEmailsDAO.findByEmailV2(
      emails = Seq(data.email),
      teamId = request.actingTeamAccount.get.team_id,
      logger = Logger
    ) match {


      case Failure(e) =>
        error_occurred = Some(e.getMessage)
        Future.successful(
          Left(EmailAccountTestSettingError.TestError(e.getMessage, helpfulErrorData = None))
        )

      case Success(prospect) =>
        if (prospect.nonEmpty) {

          error_occurred = Some(s"The email set-up is not possible as ${data.email} is added as a Prospect email address to a campaign in this team. Probably used for campaign testing purposes. If you like to proceed then delete this prospect email address from Prospects and retry.")
          Future.successful(Left(EmailAccountTestSettingError.EmailExistsAsProspect(
            s"The email set-up is not possible as ${data.email} is added as a Prospect email address to a campaign in this team. Probably used for campaign testing purposes. If you like to proceed then delete this prospect email address from Prospects and retry.")
          ))
        } else {
          freeEmailDomainListService.checkIfFreeEmailService(email = data.email) match {

            case Failure(e) =>
              error_occurred = Some(e.getMessage)
              Future.successful(
                Left(EmailAccountTestSettingError.TestError(e.getMessage, helpfulErrorData = None))
              )

            case Success(isFreeEmailService) => {
              if (isFreeEmailService) {
                error_occurred = Some(CONSTANTS.API_MSGS.FREE_EMAIL_DOMAIN_ERROR)
                Future.successful(
                  Left(EmailAccountTestSettingError.FreeDomainUseError)
                )
              }
              else {
                val logData = SRInternalLog(
                  function = Logger.logRequestId,
                  data = Json.obj(
                    "service_provider" -> data.service_provider,
                    "email" -> data.email,
                    "imap_host" -> data.imap_host,
                    "imap_port" -> data.imap_port,
                    "imap_username" -> data.imap_username,
                    "smtp_host" -> data.smtp_host,
                    "smtp_port" -> data.smtp_port,
                    "smtp_username" -> data.smtp_username,
                    "can_send" -> data.can_send,
                    "can_receive" -> data.can_receive,
                    "email_domain" -> data.email_domain,
                    "mailgun_region" -> data.mailgun_region,
                    "quota_per_day" -> data.quota_per_day
                  )
                )


                SRInternalLog.logApi(request = request, logData = logData, status = SRLogStatus.START)

                //only validating based on basic and advanced details for sending configuration test email

                EmailSetting.validateBasicSettingInput(
                  data = data,
                  orgId = OrgId(id = request.loggedinAccount.org.id),
                  isNew = false
                ) match {

                  case Failure(e) =>
                    //Future.successful(Res.BadRequestError(e.getMessage))

                    error_occurred = Some(e.getMessage)
                    Future.successful(
                      Left(
                        EmailAccountTestSettingError.ValidationError(err = e)
                      )
                    )

                  case Success(_) =>
                    val emailAfterDate = DateTime.now().minusMinutes(5)

                    //          val imapEmailSetting = EmailSetting.getImapEmailSetting(data)
                    val emailToBeSent = EmailSetting.getTestEmail(
                      data = data,
                      accountEmail = account.email,
                      bulkSender = account.org.settings.bulk_sender,
                      accountId = account.internal_id
                    )

                    val fullName = Helpers.getSenderName(data)

                    val emailSendDetail: EmailSendDetail = EmailSendDetail(

                      sender_email_settings_id = 0,
                      receiving_email_settings_id = 0,
                      sender_message_id_suffix = "mail@hello",

                      id = 0,
                      org_id = account.org.id,
                      team_id = 0,
                      account_id = 0,
                      prospect_id = None,
                      sendEmailFromCampaignDetails = None,

                      subject = emailToBeSent.subject,
                      body = emailToBeSent.htmlBody,
                      text_body = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = emailToBeSent.htmlBody),
                      scheduled_from_campaign = false,

                      service_provider = data.service_provider,
                      via_gmail_smtp = None,

                      smtp_username = data.smtp_username.map(_.trim),
                      smtp_host = data.smtp_host.map(_.trim),
                      smtp_port = data.smtp_port,
                      smtp_password = data.smtp_password.map(_.trim),
                      oauth2_refresh_token = None,
                      oauth2_access_token = None,
                      oauth2_access_token_expires_at = None,

                      // for mailgun
                      email_domain = data.email_domain.map(_.trim),
                      api_key = data.api_key.map(_.trim),
                      mailgun_region = data.mailgun_region,

                      from_email = data.email.trim,
                      from_name = fullName,
                      reply_to_email = None,
                      reply_to_name = None,
                      to_emails = Seq(IEmailAddress(
                        email = account.email,
                        name = account.first_name
                      )),
                      cc_emails = Seq(), //data.cc_emails.map(_.trim),
                      bcc_emails = Seq(), //data.bcc_emails.map(_.trim),

                      in_reply_to_id = None,
                      in_reply_to_references_header = None,
                      in_reply_to_sent_at = None,
                      in_reply_to_subject = None,
                      in_reply_to_outlook_msg_id = None,

                      //              opt_out_msg = "",
                      //              opt_out_is_text = true,
                      //              append_followups = false,
                      //              open_tracking_enabled = false,
                      //              click_tracking_enabled = false,
                      //              signature = None,

                      sender_email_setting_paused_till = None,

                      custom_tracking_domain = None,
                      //              sr_tracking_host = AppConfig.defaultTrackingHost,
                      rep_tracking_host_id = 1,
                      // bulk_sender = account.org.settings.bulk_sender,
                      gmail_fbl = None,
                      list_unsubscribe_header = None,
                      send_plain_text_email = Some(false), // SEND_PLAIN_TEXT_EMAIL_FIXME : this is good we are checking sending email so we dont need to send plain text

                      email_thread_id = None,
                      gmail_thread_id = None
                    )

                    val testFut = (data.can_send, data.can_receive) match {

                      case (true, true) =>


                        for {
                          imapEmailSetting <- Future.fromTry(EmailSetting.getImapEmailSetting(data))
                          messages <- Future.fromTry(smtpImapService.fetchRecentMessagesForTestingImap(
                            emailAfterDate = emailAfterDate,
                            emailTillDate = None,
                            imapEmailSetting = imapEmailSetting,
                            Logger = Logger
                          ))
                          sendEmail <- emailSenderService.sendEmailToProspect(data = emailSendDetail, rep_smtp_reverse_dns_host = None)


                        } yield true

                      case (true, false) =>

                        for {
                          sendEmail <- emailSenderService.sendEmailToProspect(data = emailSendDetail, rep_smtp_reverse_dns_host = None)


                        } yield true

                      case (false, true) =>
                        for {
                          imapEmailSetting <- Future.fromTry(EmailSetting.getImapEmailSetting(data))
                          messages <- Future.fromTry(smtpImapService.fetchRecentMessagesForTestingImap(
                            emailAfterDate = emailAfterDate,
                            emailTillDate = None,
                            imapEmailSetting = imapEmailSetting,
                            Logger = Logger
                          ))
                        } yield true


                      case (false, false) =>
                        Future.failed(new Exception("test_type must be either send or receive"))

                    }
                    testFut.map(e => {

                        SRInternalLog.logApi(request = request, logData = logData, status = SRLogStatus.SUCCESS)

                        // Res.Success("Your email account settings have been successfully tested", Json.obj())
                        Right(
                          true
                        )

                      })
                      .recover { case e => {

                        SRInternalLog.logApi(request = request, logData = logData, status = SRLogStatus.ERROR, err = Some(e))

                        Logger.fatal("There was an error while testing your email settings: ", e)
                        // Res.BadRequestError("There was an error while testing your email settings: " + e.getMessage)
                        getHelpFulMessageForEmailIntegrationError(
                          err = e,
                          selected_service_provider = data.service_provider
                        ) match {
                          case None =>
                            error_occurred = Some("There was an error while testing your email settings: " + e.getMessage)

                            Left(
                              EmailAccountTestSettingError.TestError(
                                errMessage = "There was an error while testing your email settings: " + e.getMessage,
                                helpfulErrorData = None
                              )
                            )
                          case Some(helpfulError: HelpfulErrorMessageTrait) =>
                            error_occurred = Some(s"This is the error we received from your email host: ${helpfulError.error.getMessage}.")
                            Left(
                              EmailAccountTestSettingError.TestError(
                                errMessage = s"This is the error we received from your email host: ${helpfulError.error.getMessage}.",
                                helpfulErrorData = Some(helpfulError)
                              )
                            )
                        }

                      }
                      }


                }

              }

            }
          }

        }
    }

    result.map {
      case Left(err: EmailAccountTestSettingError) =>
        insertEmailSettingIntegrationLogs(
          accountId = AccountId(account.internal_id),
          orgId = OrgId(account.org.id),
          teamId = teamId,
          email_address = Some(data.email),
          service_provider = Some(data.service_provider),
          completed = false,
          error_occurred = error_occurred,
          email_integration_stage = EmailSettingIntegrationLogsStage.TestSettingError
        )
        Left(err)
      case Right(value: Boolean) =>
        insertEmailSettingIntegrationLogs(
          accountId = AccountId(account.internal_id),
          orgId = OrgId(account.org.id),
          teamId = teamId,
          email_address = Some(data.email),
          service_provider = Some(data.service_provider),
          completed = false,
          error_occurred = None,
          email_integration_stage = EmailSettingIntegrationLogsStage.TestSettingSuccess
        )
        Right(value)

    }.recover { case e =>
      insertEmailSettingIntegrationLogs(
        accountId = AccountId(account.internal_id),
        orgId = OrgId(account.org.id),
        teamId = teamId,
        email_address = Some(data.email),
        service_provider = Some(data.service_provider),
        completed = false,
        error_occurred = error_occurred,
        email_integration_stage = EmailSettingIntegrationLogsStage.TestSettingError
      )
      Left(EmailAccountTestSettingError.TestError(e.getMessage, helpfulErrorData = None))
    }

  }

  def testSettingForGmailASP[A](
                                 emailSetting: EmailSetting,
                                 appPassword: String,
                                 account: Account,
                                 teamId: TeamId,
                                 // This change is part a hotfix to handle gmail sending issues on 31-Mar-2022
                                 // we will refactor out the "request" argument in a future PR
                                 permissionRequest: PermissionRequest[A]
                               )(
                                 implicit ec: ExecutionContext,
                                 Logger: SRLogger,
                                 wSClient: WSClient,
                                 system: ActorSystem
                               ): Future[Either[EmailAccountTestGmailASPError, Boolean]] = {

    if (emailSetting.service_provider != EmailServiceProvider.GMAIL_API) {

      Future.successful(
        Left(EmailAccountTestGmailASPError.ESPMustBeGmailAPI)
      )

    } else {

      val email = emailSetting.email


      val formData = EmailSettingForm(
        email = email,

        service_provider = EmailServiceProvider.GMAIL_ASP,

        smtp_username = Some(email),
        smtp_password = Some(appPassword),
        smtp_host = Some(EmailAccountService.GMAIL_SMTP_HOST),
        smtp_port = Some(EmailAccountService.GMAIL_SMTP_PORT),

        imap_username = Some(email),
        imap_password = Some(appPassword),
        imap_host = Some(EmailAccountService.GMAIL_IMAP_HOST),
        imap_port = Some(EmailAccountService.GMAIL_IMAP_PORT),

        email_domain = emailSetting.email_domain,
        api_key = None,
        mailgun_region = None,
        quota_per_day = emailSetting.quota_per_day,
        min_delay_seconds = emailSetting.min_delay_seconds,
        max_delay_seconds = emailSetting.max_delay_seconds,
        can_send = true,
        can_receive = true,
        cc_emails = emailSetting.cc_emails,
        bcc_emails = emailSetting.bcc_emails,
        first_name = emailSetting.first_name,
        last_name = emailSetting.last_name,

        platform_email_id = None,
        email_tag = None

      )

      testSettings(
        data = formData,
        account = account,
        request = permissionRequest,
        teamId = teamId
      )
        .map(res => res.left.map(a => EmailAccountTestGmailASPError.TestError(a)))

    }
  }
}

object EmailAccountTestService {

  def getHelpFulMessageForEmailIntegrationError(
                                                 selected_service_provider: EmailServiceProvider,
                                                 err: Throwable
                                               )(using logger: SRLogger): Option[HelpfulErrorMessageTrait] = {
    HelpfulErrorMessageDuringEmailIntegration.fromString(
      error = err,
      selected_service_provider = selected_service_provider
    )
  }

}
