package api.emails.services

import api.{AppConfig, CacheServiceJedis}
import api.accounts.service.AddKeyToRedisError
import api.accounts.{EmailScheduledIdOrTaskId, ReplyHandling, TeamId}
import api.campaigns.CampaignProspectDAO
import api.campaigns.services.CampaignId
import api.emails.models.DeletionReason
import api.emails.{EmailDetailsForHasOptedOut, EmailScheduledDAO, EmailsScheduledUuid, OptedOutEmailDetails}
import api.prospects.models.ProspectId
import scalikejdbc.DB
import utils.cache_utils.CacheKeyGen
import utils.{Helpers, SRLogger}
import utils.cronjobs.ResultForSenderRunAlert
import utils.email.models.DeleteEmailsScheduledType
import utils.mq.services.MQDoNotNackException

import scala.util.{Failure, Success, Try}

sealed trait CheckCountForEmailScheduledButNotSentError
object CheckCountForEmailScheduledButNotSentError {
  case class DBFailure(err: Throwable) extends CheckCountForEmailScheduledButNotSentError
  case class LimitExceededError(err: String) extends CheckCountForEmailScheduledButNotSentError
  case class ErrorWhileFetchingKey(err: Throwable) extends CheckCountForEmailScheduledButNotSentError
  case class ErrorWhileSettingCountInRedis(err: Throwable) extends CheckCountForEmailScheduledButNotSentError
  case class CountNotFound(err: String) extends CheckCountForEmailScheduledButNotSentError
}

class EmailScheduledService(

                             emailScheduledDAO: EmailScheduledDAO,
                             campaignProspectDAO: CampaignProspectDAO,
                             cacheServiceJedis: CacheServiceJedis,
                             selectAndPublishForDeletionService: SelectAndPublishForDeletionService

                           ) {

  def getStuckEmailsForSendingInLast15Mins(

                                )(using Logger: SRLogger): Unit =
    emailScheduledDAO.getStuckEmailsForSendingInLast15Mins() match {
      case Success(list) =>
        list.foreach( data =>
          Logger.fatal(s"Sending is not happening for :: rep_mail_server_id: ${data.rep_mail_server_id} :: org_id: ${data.org_id.id} :: team_id: ${data.team_id.id} :: team_name: ${data.team_name} :: count: ${data.count}")

        )
      case Failure(exception) => //DO NOTHING
      Logger.fatal(s"Failed to get getStuckEmailsInLast15Mins", exception)
    }

  def hasOptedOutV2(
                     Logger: SRLogger,
//                     stepId: Long,
                     emailScheduledId: Option[EmailScheduledIdOrTaskId.EmailScheduledId],
                     emailsScheduledUuidAndTeamId: Option[(EmailsScheduledUuid, TeamId)],
                     traceReqId: Option[Long]
                   ): Try[(Int, Option[OptedOutEmailDetails])] = Try {
    
    if(emailsScheduledUuidAndTeamId.isEmpty && emailScheduledId.isEmpty) {
      
      throw new Exception("No emailScheduledId or emailsScheduledUuid provided")
      
    } else if(emailsScheduledUuidAndTeamId.nonEmpty && emailScheduledId.nonEmpty) {
      
      throw new Exception("Both emailScheduledId and emailsScheduledUuid provided")
      
    } else {
      
      given srLogger: SRLogger = Logger
    // fixme given
    DB localTx { implicit session =>

      val res: Option[EmailDetailsForHasOptedOut] = emailScheduledDAO.getEmailDetailsForHasOptedOutV2(
        emailScheduledId = emailScheduledId,
        emailsScheduledUuidAndTeamId = emailsScheduledUuidAndTeamId,
      )

      res match {
        case None =>
          Logger.error(s"No data found for the emails_scheduled $emailScheduledId")
          throw MQDoNotNackException(s"No data found for the emails_scheduled $emailScheduledId")

        case Some(value) =>
          val emailHasBounced: Boolean = value.bounced
          val teamId: Long = value.team_id.id

          if (emailHasBounced) {

            Logger.info(s"hasOptedOutV2 emailHasBounced (therefore ignoring optedout request): emailScheduledId: $emailScheduledId")

            (0, None)

          } else {

            val emailScheduledIdUpdated =
              emailScheduledDAO.updateEmailHasOptedOut(
                emailScheduledId = emailScheduledId,
                emailsScheduledUuid = emailsScheduledUuidAndTeamId.map(_._1),
                traceReqId = traceReqId,
                teamId = value.team_id
              )

            if (emailScheduledIdUpdated.isEmpty) {
              Logger.fatal(s"hasOptedOutV2 emailScheduledIdUpdated.isEmpty: emailScheduledId: $emailScheduledId")
            }

            if (value.campaign_id.isEmpty || value.prospect_id.isEmpty) {
              Logger.info(s"hasOptedOutV2 campaign_id ${value.campaign_id} :: prospect_id  ${value.prospect_id} : emailScheduledId: $emailScheduledId")
              (0, None)
            } else {

              val campaignId = value.campaign_id.get
              val prospectId = value.prospect_id.get

              val count = campaignProspectDAO._hasOptedOut(campaignId, prospectId)

              selectAndPublishForDeletionService.selectAndPublishForDeletion(
                deletion_reason = DeletionReason.HasOptedOutV2,
                deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByProspectId(
                  campaignId = campaignId.id,
                  prospectIds = Seq(prospectId.id),
                  prospectAccountIds = None,
                  replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY,
                  teamId = TeamId(id = teamId),
                )
              )

              // emailScheduled
              emailScheduledDAO.__getOptedOutProspectDetails(campaignId = campaignId, prospectId = prospectId, teamId = TeamId(teamId)) match {
                case Failure(err) =>
                  Logger.error(s"__getOptedOutProspectDetails failed for TeamID - $teamId", err)
                  throw err
                case Success(value) =>
                  (count, value)
              }
            }
          }
      }
      }
    }
  }

  def setEmailSendingStuckCountInRedis(
                                        count: Int,
                                        key: String
                                      ): Try[String] = {

    cacheServiceJedis.set[Int](
      key = key,
      value = count,
      expireInSeconds = AppConfig.ttlForEmailsStuckCountRedisKeyInSeconds
    )

  }

  def checkCountForEmailScheduledButNotSent(): Either[CheckCountForEmailScheduledButNotSentError, Int] = {

    val key: String = CacheKeyGen.getScheduledEmailsStuckCountKey
    val alertAtEmailSendingStuckCount = AppConfig.alertAtEmailSendingStuckCount

    emailScheduledDAO.getNotSentEmailScheduledIds() match {
      case Failure(exception) =>
        Left(CheckCountForEmailScheduledButNotSentError.DBFailure(exception))

      case Success(ids) =>
        cacheServiceJedis.get[Int](
          key = key
        ) match {
          case Failure(exception) =>
            Left(CheckCountForEmailScheduledButNotSentError.ErrorWhileFetchingKey(exception))

          case Success(count) =>
            if (count.isDefined && count.get > alertAtEmailSendingStuckCount) {

              //once it raises the alert instead of making the count 0 - we can decrease by 10.
              //if the problem persists - the count will get incremented again starting from 10
              //if there problem is solved in 20 minutes the ttl will expire and the key will disappear
              setEmailSendingStuckCountInRedis(
                count = alertAtEmailSendingStuckCount / 2,
                key = key
              ) match {
                case Success(_) =>
                  Left(CheckCountForEmailScheduledButNotSentError.LimitExceededError("Email sending stuck!"))

                case Failure(exception) =>
                  Left(CheckCountForEmailScheduledButNotSentError.ErrorWhileSettingCountInRedis(exception))
              }

            } else if (count.isDefined) {
              val total_count: Int = count.get + ids.length
              setEmailSendingStuckCountInRedis(
                count = total_count,
                key = key
              ) match {
                case Success(value) => Right(total_count)
                case Failure(exception) =>
                  Left(CheckCountForEmailScheduledButNotSentError.ErrorWhileSettingCountInRedis(exception))
              }
            } else {
              setEmailSendingStuckCountInRedis(
                count = ids.length,
                key = key
              ) match {
                case Success(_) =>
                  Right(ids.length)
                case Failure(exception) =>
                  Left(CheckCountForEmailScheduledButNotSentError.ErrorWhileSettingCountInRedis(exception))
              }
            }
        }

    }

  }


}
