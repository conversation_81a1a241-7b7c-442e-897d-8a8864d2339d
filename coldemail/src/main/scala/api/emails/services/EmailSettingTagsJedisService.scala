package api.emails.services

import api.CacheServiceJedis
import api.accounts.TeamId
import utils.cache_utils.{SimpleCacheServiceTrait, SrRedisKeyNamespace, SrRedisKeyRegister}


class EmailSettingTagsJedisService(
  protected val cacheServiceJedis: CacheServiceJedis
) extends SimpleCacheServiceTrait[TeamId, List[String]] {

  override protected val redisKeyNamespace: SrRedisKeyNamespace = SrRedisKeyRegister.emailSettingTagsKeyNamespace

  override def getCacheId(data: TeamId): String = s"team_${data.id}"

  // Cache for 7 days (604800 seconds) - reasonable for tags that don't change frequently
  override val defaultExpiresInSeconds: Int = 604800

} 