package api.emails

import api.emails.dao_service.EmailThreadDAOService
import api.emails.models.{EmailMessageContact, EmailMessageContactModel, EmailMessageContacts}
import api.prospects.{ProspectCheckForIsSentEmail, ProspectData, ProspectIdEmail}
import io.smartreach.esp.api.emails.IEmailAddress
import org.joda.time.DateTime
import utils.SRLogger
import utils.helpers.LogHelpers
import scala.util.{Failure, Success, Try}

class EmailCommonService(
                          emailMessageContactModel: EmailMessageContactModel,
                          emailThreadDAOService: EmailThreadDAOService,
                          emailSettingDAO: EmailSettingDAO
                        ) {


  /**
   * Saves email message contacts to the database.
   */
   def saveEmailContacts(
                                        dataForEmailMessageContacts: Seq[EmailCommonService.DataForEmailMessageContact],
                                        validProspects: Seq[ProspectIdEmail],
                                        inboxEmailSettingEmail: String,
                                        team_id: Long,
                                      )(using SRLogger: SRLogger): Unit = {
    val emailMessageContactsToSave: Seq[EmailMessageContact] = dataForEmailMessageContacts
      .flatMap(msg => {

        val contacts: EmailMessageContacts = EmailMessageContact.getEmailMessageContacts(
          inboxEmailAddress = inboxEmailSettingEmail,
          emailMessageId = msg.email_scheduled_id,

          fromEmail = msg.from_email,
          toEmails = msg.to_emails,
          ccEmails = msg.cc_emails,

          /*
            For reply tracker  we are not tracking bcc emails during reply tracking as of now. but for scheduler flow we
            consider bcc emails.
           */
          bccEmails =  msg.bcc_emails ,
          replyToEmail = msg.reply_to,
          foundProspects = validProspects,
          teamId = team_id
        )

        val allContactsInMessage: Seq[EmailMessageContact] = EmailMessageContact.getContactsArrayFromEmailMessageContacts(contacts = contacts)

        allContactsInMessage

      })

    emailMessageContactModel.insertEmailMessageContacts(
      contacts = emailMessageContactsToSave
    ) match {
      case Failure(e) =>
        throw new Exception(s"FATAL error while saving EmailMessageContact._insertEmailMessageContacts: ${LogHelpers.getStackTraceAsString(e)}")

      case Success(_) =>
      // do nothing
    }
  }


  /**
   * Associates email threads with prospects in the database.
   *
   * @param threadProspects Sequence of email thread and prospect associations
   * @param teamId          The team ID for context in error logs
   * @param logger          Implicit logger for error reporting
   * @return Success or Failure
   */
   def associateProspectsWithEmailThreads(
                                                  threadProspects: Seq[AssociateEmailThreadProspect],
                                                  teamId: Long
                                                )(using logger: SRLogger): Try[Unit] = {

    if (threadProspects.isEmpty) {
       Success(())
    }else {

      emailThreadDAOService._associateEmailThreadsAndProspectsV3(
        logger = logger,
        threadProspects = threadProspects
      ) match {
        case Failure(e) =>
          logger.fatal(s"Failed to associate email threads with prospects. TeamId: $teamId, data:${threadProspects}", err = e)
          Failure(e)

        case Success(result) =>
          Success(())
      }
    }
  }

  /**
   * Increments the current sent prospect count for an email if the prospect
   * hasn't been contacted in the current billing cycle
   */
  def incrementSentProspectCountIfNeeded(
                                          prospectInCampaign: Option[ProspectCheckForIsSentEmail],
                                          currentBillingCycleStartedAt: DateTime,
                                          emailSettingId: Long
                                        )(using Logger: SRLogger): Unit = {
    if (prospectInCampaign.isDefined &&
      (prospectInCampaign.get.last_contacted_at.isEmpty ||
        prospectInCampaign.get.last_contacted_at.get.isBefore(currentBillingCycleStartedAt))) {

      emailSettingDAO._incrementCurrentSentProspectCountForEmail(emailSettingId = emailSettingId) match {
        case Success(value) => // DO NOTHING
        case Failure(exception) =>
          Logger.fatal("Error while _incrementCurrentSentProspectCountForEmail", exception)
          throw exception
      }
    }
  }




}
object EmailCommonService {
  case class DataForEmailMessageContact(
                                       email_scheduled_id: Long,
                                       from_email: IEmailAddress,
                                       to_emails: Seq[IEmailAddress],
                                       cc_emails: Seq[IEmailAddress],
                                       bcc_emails: Seq[IEmailAddress],
                                       reply_to: Option[IEmailAddress],
                                       )
}
