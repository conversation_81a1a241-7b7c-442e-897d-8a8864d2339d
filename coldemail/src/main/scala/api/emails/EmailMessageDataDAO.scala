package api.emails

import api.accounts.TeamId
import io.smartreach.esp.api.emails.IEmailAddress
import scalikejdbc.{DBSession, SQL, scalikejdbcSQLInterpolationImplicitDef, DB}
import utils.SRLogger

import scala.util.Try

class EmailMessageDataDAO {


  def addingInEmailMessageDataTableForScheduler(
                                                 saved: Seq[EmailScheduledNewAfterSaving]
                                               )(implicit session: DBSession): Try[Seq[Long]] = Try {

    //    DB localTx { implicit session =>

    //      Logger.info(s"EmailScheduled._insertMultiple: ${emailsToBeScheduled.length}")

    if (saved.isEmpty) Seq()
    else {

      var parameters = List[Any]()


      val valuePlaceholder = saved.map(email => {


        parameters = parameters ::: List(
          email.email_scheduled_id,
          email.sender_email_settings_id,
          email.team_id.id,

          email.to_email,
          email.to_name,


          email.from_email,
          email.from_name,
          email.reply_to_email,
          email.reply_to_name,


          email.body,
          email.base_body,
          email.text_body,
          email.subject


        )

        s"""
        (
          ?,
          ?,
          ?,


          ?,
          ?,


          ?,
          ?,
          ?,
          ?,

          ?,
          ?,
          ?,
          ?

        )

      """
      }).mkString(", ")

      SQL(
        s"""
      INSERT INTO email_message_data
      (
         es_id,
         inbox_email_setting_id,
         team_id,

         to_email,
         to_name,


         from_email,
         from_name,
         reply_to_email,
         reply_to_name,


        -- only for manually scheduled emails

          body,
          base_body,
          text_body,
          subject

      )
      VALUES $valuePlaceholder
      ON CONFLICT DO NOTHING
      RETURNING *;
    """)
        .bind(parameters *)
        .map(rs => {
          rs.long("es_id")
        })
        .list
        .apply()

    }
    //    }

  }

  def addingInEmailMessageDataTableForTracker(
                                               toUpdateEmailThreadsInternal: Seq[EmailReplySavedV3]
                                             )(using Logger: SRLogger, session: DBSession): Try[Seq[Long]] = Try{
    if (toUpdateEmailThreadsInternal.isEmpty) Seq() else {
      var parameters = List[Any]()

      val valuePlaceholderForEmailMessageData = toUpdateEmailThreadsInternal.map(email => {

        val toEmail: String = IEmailAddress.stringify(emails = email.to_emails).getOrElse("") //deliberately sending "" if none for reply tracking

        // todo newinbox: remove this to_name column after full migration
        val toName = if (email.to_emails.length == 1) email.to_emails.head.name else None

        val ccEmails: Option[String] = IEmailAddress.stringify(emails = email.cc_emails)

        parameters = parameters ::: List(
          email.email_scheduled_id,
          email.from_email.email,
          toEmail,

          email.inbox_email_setting_id,
          email.references_header,
          email.message_id,
          email.in_reply_to_header,

          email.subject,
          email.email_body,
          email.base_body,

          email.text_body,
          email.full_headers.toString(),

          toName,
          email.from_email.name,
          email.reply_to.map(_.email).getOrElse(""),

          email.reply_to.map(_.name).getOrElse(""),
          email.email_thread_id,
          email.gmail_msg_id,

          email.gmail_thread_id,
          email.outlook_msg_id,
          email.outlook_conversation_id,

          email.outlook_response_json.map(_.toString()),
          ccEmails,
          email.team_id.id
        )

        s"""
          (
            ?,
            ?,
            ?,

            ?,
            ?,
            ?,
            ?,

            ?,
            ?,
            ?,

            ?,
            to_json(?::json),

            ?,
            ?,
            ?,

            ?,
            ?,
            ?,

            ?,
            ?,
            ?,

            to_json(?::json),
            ?,
            ?

          )

        """
      }).mkString(", ")

      SQL(
        s"""
          INSERT INTO email_message_data
          (
            es_id,
            from_email,
            to_email,

            inbox_email_setting_id,
            references_header,
            message_id,
            in_reply_to_header,

            subject,
            body,
            base_body,

            text_body,
            full_headers,

            to_name,
            from_name,
            reply_to_email,

            reply_to_name,
            email_thread_id,
            gmail_msg_id,

            gmail_thread_id,
            outlook_msg_id,
            outlook_conversation_id,

            outlook_response_json,
            cc_emails,
            team_id

          )
          VALUES $valuePlaceholderForEmailMessageData
          ON CONFLICT DO NOTHING
          RETURNING es_id;
        """)
        .bind(parameters*)
        .map(rs => {
          rs.long("es_id")
        })
        .list
        .apply()
    }
  }


  def markAsSent(emailSentId: Long,
                 data: EmailToBeSent,
                 toEmails: String,
                 toCcEmails: Option[String],
                 toBccEmails: Option[String],
                 emailThreadId: Long,
                 to_name: Option[String],
                 teamId: TeamId
                )(implicit session: DBSession): Try[Option[Long]] = Try {


      sql"""
          UPDATE email_message_data
          SET
            to_name = ${to_name.orNull},
            to_email = $toEmails,
            from_email = ${data.from_email},
            from_name = ${data.from_name},
            reply_to_email = ${data.reply_to_email},
            reply_to_name = ${data.reply_to_name},

            subject = ${data.subject},
            body = ${data.htmlBody},
            text_body = ${data.textBody},
            bcc_emails = $toBccEmails,
            cc_emails = $toCcEmails,
            message_id = ${data.message_id},
            references_header = ${data.references_header},
            inbox_email_setting_id = ${data.sender_email_settings_id},


            email_thread_id = $emailThreadId,
            gmail_msg_id = ${data.gmail_msg_id},
            gmail_thread_id = ${data.gmail_thread_id},

            outlook_msg_id = ${data.outlook_msg_id},
            outlook_conversation_id = ${data.outlook_conversation_id},
            outlook_response_json = ${data.outlook_response_json.map(_.toString).getOrElse("{}")}::jsonb


          WHERE es_id = $emailSentId
          AND team_id = ${teamId.id}
          RETURNING es_id;
      """
        .map(rs => rs.long("es_id"))
        .single
        .apply()

  }

  def updateTrackedOutlookMsgId(email_scheduled_id: Long, outlook_msg_id: String): Try[Int] = Try {

    DB.autoCommit { implicit session =>
      sql"""
          UPDATE email_message_data
          SET
            outlook_msg_id = $outlook_msg_id
          WHERE
            es_id = $email_scheduled_id
            AND outlook_msg_id IS NULL
            ;
          """
        .update
        .apply()

    }
  }


}
