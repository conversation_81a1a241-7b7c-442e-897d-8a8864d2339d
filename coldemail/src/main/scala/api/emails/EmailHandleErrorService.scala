package api.emails

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.AccountId
import api.emails.models.DeletionReason
import api.emails.services.SelectAndPublishForDeletionService
import io.smartreach.esp.api.EmailConstants.EMAIL_API_ERROR_KEYS
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import play.api.libs.ws.WSClient
import utils.{Helpers, SRLogger}
import utils.email.models.{DeleteEmailsScheduledType, SendScheduleEmailType}
import utils.email.SREmailErrors
import utils.email_notification.service.EmailNotificationService
import utils.helpers.LogHelpers
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success, Try}

class EmailHandleErrorService(
  emailSettingDAO: EmailSettingDAO,
  emailNotificationService: EmailNotificationService,
  emailScheduledDAO: EmailScheduledDAO,
  selectAndPublishForDeletionService: SelectAndPublishForDeletionService,
  srRollingUpdateCoreService: SrRollingUpdateCoreService
) {


  private def sendAuthenticationErrorNotification(
    emailSettingError: SREmailErrors.EmailSettingError,
    accountId: AccountId,
    teamId: TeamId,
    emailSettingId: EmailSettingId,
    emailSettingAddress: String,
    e: Throwable
  )(implicit wsClient: WSClient, ec: ExecutionContext, logger: SRLogger): Try[Unit] = {

    // send email notifications
    emailSettingError match {

      case SREmailErrors.GmailInvalidGrantError =>

        // send email notification
        emailNotificationService.sendGmailAuthErrorEmail(
          accountId = accountId,
          teamId = teamId,
          emailSettingId = emailSettingId,
          gmailAddress = emailSettingAddress
        )

      case SREmailErrors.MicrosoftInvalidGrantError =>

        // send email notification
        emailNotificationService.sendMicrosoftAuthErrorEmail(
          accountId = accountId,
          teamId = teamId,
          emailSettingId = emailSettingId,
          outlookAddress = emailSettingAddress
        )

      case SREmailErrors.AuthenticationFailedError =>

        // send email notification
        emailNotificationService.sendSmtpImapAuthErrorEmail(
          accountId = accountId,
          teamId = teamId,
          emailSettingId = emailSettingId,
          emailAddress = emailSettingAddress,
          errorResponse = e.getMessage
        )

      case SREmailErrors.GmailSendingLimitReachedError =>
        // this email is being sent from the handleEmailSettingError function itself
        Success(())

      case SREmailErrors.SmtpSendFailedWithSpamORRejectedWarning |
           SREmailErrors.GmailSMTPFailedError |
           SREmailErrors.UnknownSendError |
           SREmailErrors.GSuiteMailServiceNotEnabledError | // TODO send email on GSuiteMailServiceNotEnabledError
           SREmailErrors.ConnectionTimeoutError |
           SREmailErrors.EmailAPIInternalFailureError |
           SREmailErrors.ReplyTrackingMessagingExceptionError |
           SREmailErrors.OutlookServiceUnavailableError |
           SREmailErrors.OutlookAPIFatalError |
           SREmailErrors.UnknownReplyTrackingError |
           SREmailErrors.EmailAPIAccessTokenTemporaryError |
           SREmailErrors.OutlookBadOutBoundSendError =>

        // no email notifications need to be sent here
        Success(())

    }
  }

  private def _addErrorAndDeleteUnsentEmailsForEmailSetting(
    emailSettingId: EmailSettingId,
    teamId: TeamId,
    emailSettingError: SREmailErrors.EmailSettingError,
    Logger: SRLogger
  )(errorMsg: String): Try[Seq[EmailSetting]] = {

    given srLogger: SRLogger = Logger // fixme given

    Logger.error(s"_addErrorAndDeleteUnsentEmailsForEmailSetting :: $emailSettingError")

    emailSettingDAO.addError(
      emailSettingId = emailSettingId.emailSettingId,
      error = errorMsg,
      errorReportedAt = DateTime.now(),
      pausedTill = emailSettingError.pauseTill

    ).flatMap(updateSettings => {

      if (updateSettings.isEmpty) {

        Failure(new Exception(s"_addErrorAndDeleteUnsentEmailsForEmailSetting FATAL [EmailService] (emailSettingId: $emailSettingId) error EmailSetting not found while updating error ${Logger.logRequestId}"))

      } else {

        // drop the message from queue
        // Not deleting is_manual_tasks = true and also selecting first and then deleting
        // As manual tasks are run by users, and they can leave the task in due for long time
        // also when we were reverting the campaign_prospects after deleting emails_scheduled entry
        // we were not deleting previous manual_email_task therefore on scheduling duplicate tasks
        // were getting populated


          Logger.debug(s"_addErrorAndDeleteUnsentEmailsForEmailSetting DeleteUnSentByEmailSettingIdNotManual for team_id : ${teamId.id} ")

          selectAndPublishForDeletionService.selectAndPublishForDeletion(
            deletion_reason = DeletionReason.AddErrorAndDeleteUnsentEmailsForEmailSetting,
            deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnSentByEmailSettingIdNotManual(

              emailSettingData = Map(teamId -> Seq(emailSettingId)),
            )
          )
            .map(_ => updateSettings)



      }
    })

  }

  // this can throw exceptions
  def handleEmailSettingError(
    emailSettingId: EmailSettingId,
    sendEmailType: SendScheduleEmailType,
    e: Throwable,
    accountId: AccountId,
    teamId: TeamId,
    emailSettingAddress: String,
    Logger: SRLogger
  )(
    emailSettingError: SREmailErrors.EmailSettingError
  )(implicit wsClient: WSClient, ec: ExecutionContext): Try[Seq[EmailSetting]] = {

    given logger: SRLogger= Logger

    val errorStr: String = Option(e.getMessage).getOrElse("")

    if (errorStr.nonEmpty) {

      val msg = s"$emailSettingError handleEmailSettingError :: err: ${LogHelpers.getStackTraceAsString(e)}"

      if (
        errorStr.contains("Couldn't connect to host") ||
          errorStr.contains(EMAIL_API_ERROR_KEYS.OUTLOOK_SERVICE_UNAVAILABLE)
      ) {

        Logger.warn(msg)

      } else {

        Logger.error(msg)

      }
    }



    // partial function ends with _
    val addErrorAndDeleteUnsent = _addErrorAndDeleteUnsentEmailsForEmailSetting(
      // FIXME VALUECLASS
      emailSettingId = emailSettingId,
      teamId = teamId,
      emailSettingError = emailSettingError,
      Logger = Logger
    )

    emailSettingError match {

      case SREmailErrors.GmailInvalidGrantError |
           SREmailErrors.MicrosoftInvalidGrantError |
           SREmailErrors.AuthenticationFailedError =>

        emailSettingDAO.getEmailSettingAuthenticationFailedCount(
          // FIXME VALUECLASS
          emailSettingId = emailSettingId.emailSettingId,
          sendEmailType = sendEmailType
        ) match {

          case Failure(e) =>

            Logger.fatal(s"$emailSettingError getEmailSettingAuthenticationFailedCount failed", err = e)

            Failure(e)

          case Success(None) =>

            Logger.fatal(s"$emailSettingError getEmailSettingAuthenticationFailedCount Invalid email_settings_id")

            Failure(new Exception(s"${Logger.logRequestId} [EmailService] (emailSettingId: ${emailSettingId.emailSettingId}) $emailSettingError getEmailSettingAuthenticationFailedCount Invalid email_setting_id"))

          case Success(Some(auth_failed_count)) =>

            if (auth_failed_count > 2) {
              //pausing emailSetting when two consecutive AuthenticationFails happen

              Logger.error(s"$emailSettingError error auth failed", err = e)

              for {
                emailSettings: Seq[EmailSetting] <- addErrorAndDeleteUnsent(emailSettingError.defaultMsg)
                sendEmailNotification: Unit <- sendAuthenticationErrorNotification(
                  emailSettingError = emailSettingError,
                  accountId = accountId,
                  teamId = teamId,
                  emailSettingId = emailSettingId,
                  emailSettingAddress = emailSettingAddress,
                  e = e
                )
              } yield {
                emailSettings
              }

            } else {

              Logger.error(s"$emailSettingError error auth failed:: auth_failed_count: $auth_failed_count", err = e)

              Failure(new Exception(s"${Logger.logRequestId} [EmailService] $emailSettingError error auth failed (emailSettingId: ${emailSettingId.emailSettingId}) :: auth_failed_count: $auth_failed_count"))

            }

        }

      case SREmailErrors.GmailSMTPFailedError =>

        addErrorAndDeleteUnsent(s"Gmail send error, will retry after 10 minutes. Error: ${e.getMessage}")

      case SREmailErrors.SmtpSendFailedWithSpamORRejectedWarning =>

        addErrorAndDeleteUnsent(
          s"Your email domain / host is being blocked by the recipient(s) for sending spam (Please contact support if you have any questions): ${e.getMessage}"
        )

      case SREmailErrors.UnknownSendError =>

        if (e.getMessage != null && e.getMessage.toLowerCase.contains("not found")) {
          Logger.fatal(s"handleEmailSettingError SREmailErrors.UnknownSendError $emailSettingError FATAL NOT FOUND", err = e)
        } else {
          Logger.error(s"handleEmailSettingError SREmailErrors.UnknownSendError $emailSettingError error", err = e)
        }

        addErrorAndDeleteUnsent(
          s"Error while sending email, will retry after 10 minutes. Error: ${e.getMessage}"
        )

      case SREmailErrors.GSuiteMailServiceNotEnabledError |
           SREmailErrors.ConnectionTimeoutError |
           SREmailErrors.EmailAPIInternalFailureError |
           SREmailErrors.ReplyTrackingMessagingExceptionError |
           SREmailErrors.EmailAPIAccessTokenTemporaryError |
           SREmailErrors.OutlookServiceUnavailableError |
           SREmailErrors.OutlookAPIFatalError =>

        addErrorAndDeleteUnsent(emailSettingError.defaultMsg)

      case SREmailErrors.GmailSendingLimitReachedError =>

        addErrorAndDeleteUnsent(emailSettingError.defaultMsg)
          .flatMap(updateEmails => {

            // ignore aliases
            val mainGmailAddress = updateEmails.find(em => em.id.get == emailSettingId)

            if (mainGmailAddress.isEmpty) {
              Logger.fatal(s"EmailSetting.addError gmail limit: main gmail address not found: ${emailSettingId.emailSettingId} :: $updateEmails")

              Success(updateEmails) // FIXME

            } else {

              val gmailAddress = mainGmailAddress.get

              emailNotificationService.sendGmailSendingLimitErrorEmail(
                accountId = gmailAddress.owner_id.id, // FIXME VALUECLASS
                emailAddress = gmailAddress.email // must not used emailSettingAddress here: sender's gmailAddress could be different from reply-inbox address
              ).map(_ => updateEmails)

            }
          })

      case SREmailErrors.OutlookBadOutBoundSendError  =>

        for {
          emailSettings <- addErrorAndDeleteUnsent(emailSettingError.defaultMsg)
          _ <- emailNotificationService.sendOutlookBadOutDoundSenderErrorEmail(
            accountId = accountId,
            outlook_address = emailSettingAddress
          )
        } yield {
          emailSettings
        }

      case SREmailErrors.UnknownReplyTrackingError =>

        Logger.fatal(s"SREmailErrors.UnknownReplyTrackingError handleEmailSettingError", err = e)

        addErrorAndDeleteUnsent(emailSettingError.defaultMsg)

    }
  }

}
