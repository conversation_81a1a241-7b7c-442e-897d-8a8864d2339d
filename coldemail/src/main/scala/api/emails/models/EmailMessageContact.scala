package api.emails.models

import api.prospects.{ProspectBasicDetails, ProspectIdEmail}
import eventframework.ILinkedinProfile
import io.smartreach.esp.api.emails.IEmailAddress
import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>}
import sr_scheduler.models.ChannelType


sealed trait MessageConversationContactInboxV3 {
  def prospect_id: Option[Long]
  def campaign_id: Option[Long]
  def contact_type: MessageContactType
  def is_current_inbox: Boolean
  def prospect_basic_info: Option[ProspectBasicDetails]
}

object MessageConversationContactInboxV3 {
  case class EmailMessageConversationContactInboxV3(
                                                     prospect_uuid: Option[String],
                                                     prospect_id: Option[Long],
                                                     campaign_id: Option[Long],
                                                     email: IEmailAddress,
                                                     contact_type: MessageContactType,
                                                     is_current_inbox: Boolean,
                                                     channelType: ChannelType = ChannelType.EmailChannel,
                                                     prospect_basic_info: Option[ProspectBasicDetails.EmailProspectBasicDetails]
                                                   ) extends MessageConversationContactInboxV3

  object EmailMessageConversationContactInboxV3 {
    given writes: Writes[EmailMessageConversationContactInboxV3] = J<PERSON>.writes[EmailMessageConversationContactInboxV3]
  }

  case class LinkedinMessageConversationContactInboxV3(
                                                        prospect_uuid: Option[String],
                                                        prospect_id: Option[Long],
                                                        campaign_id: Option[Long],
                                                        profile_url: ILinkedinProfile,
                                                        contact_type: MessageContactType,
                                                        is_current_inbox: Boolean,
                                                        channelType: ChannelType = ChannelType.LinkedinChannel,
                                                        prospect_basic_info: Option[ProspectBasicDetails.LinkedinProspectBasicDetails]
                                                   ) extends MessageConversationContactInboxV3

  object LinkedinMessageConversationContactInboxV3 {
    given writes: Writes[LinkedinMessageConversationContactInboxV3] = Json.writes[LinkedinMessageConversationContactInboxV3]
  }

  given writes: Writes[MessageConversationContactInboxV3] = new Writes[MessageConversationContactInboxV3] {
    override def writes(o: MessageConversationContactInboxV3): JsValue = {
      o match {
        case data: EmailMessageConversationContactInboxV3 => Json.toJson(data)
        case data: LinkedinMessageConversationContactInboxV3 => Json.toJson(data)
      }
    }
  }
}


case class EmailMessageContacts(
  from: EmailMessageContact,
  to: Seq[EmailMessageContact],
  cc: Seq[EmailMessageContact],
  bcc: Seq[EmailMessageContact],
  replyTo: Option[EmailMessageContact]
)

case class EmailMessageContact(
                                email_message_id: Long,
                                prospect_id: Option[Long],
                                email: IEmailAddress,
                                contact_type: MessageContactType,
                                is_current_inbox: Boolean,
                                team_id: Long
)

object EmailMessageContact {


  private def genEmailMessageContact(
    emailMessageId: Long,
    inboxEmailAddress: String,
    teamId: Long,
    foundProspects: Seq[ProspectIdEmail]
  )(
    email: IEmailAddress,
    contactType: MessageContactType
  ): EmailMessageContact = {

    val inboxEmail = inboxEmailAddress.trim.toLowerCase

    EmailMessageContact(

      contact_type = contactType,
      team_id = teamId,
      email_message_id = emailMessageId,

      email = email,
      is_current_inbox = email.email.toLowerCase.trim == inboxEmail,

      prospect_id = foundProspects
        .find(p => {
          p.email.trim.toLowerCase == email.email.trim.toLowerCase
        })
        .map(_.id)

    )
  }

  def getEmailMessageContacts(
    inboxEmailAddress: String,
    emailMessageId: Long,

    fromEmail: IEmailAddress,
    toEmails: Seq[IEmailAddress],
    ccEmails: Seq[IEmailAddress],
    bccEmails: Seq[IEmailAddress],
    replyToEmail: Option[IEmailAddress],

    foundProspects: Seq[ProspectIdEmail],

    teamId: Long
  ): EmailMessageContacts = {


    val getEmailMsgContact = genEmailMessageContact(
      emailMessageId = emailMessageId,
      inboxEmailAddress = inboxEmailAddress,
      teamId = teamId,
      foundProspects = foundProspects
    )


    val fromContact = getEmailMsgContact(
      fromEmail,
      MessageContactType.From
    )

    val toContacts = toEmails
      .map(em => {

        getEmailMsgContact(
          em,
          MessageContactType.To
        )

      })

    val ccContacts = ccEmails
      .map(em => {

        getEmailMsgContact(
          em,
          MessageContactType.Cc
        )

      })

    val bccContacts = bccEmails
      .map(em => {

        getEmailMsgContact(
          em,
          MessageContactType.Bcc
        )

      })

    val replyToContact = replyToEmail
      .map(em => {

        getEmailMsgContact(
          em,
          MessageContactType.ReplyTo
        )

      })

    EmailMessageContacts(
      from = fromContact,
      to = toContacts,
      cc = ccContacts,
      bcc = bccContacts,
      replyTo = replyToContact
    )

  }

  def getContactsArrayFromEmailMessageContacts(
    contacts: EmailMessageContacts
  ): Seq[EmailMessageContact] = {

    Seq(contacts.from) ++
      contacts.to ++
      contacts.cc ++
      contacts.bcc ++
      contacts.replyTo.toSeq
  }



}