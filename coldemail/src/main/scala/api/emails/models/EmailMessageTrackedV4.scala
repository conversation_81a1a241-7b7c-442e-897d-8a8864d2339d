package api.emails.models

import api.accounts.TeamId
import api.accounts.models.AccountId
import api.campaigns.services.CampaignId
import api.prospects.models.{ProspectAccountsId, ProspectId, StepId}
import io.smartreach.esp.api.emails.EmailSettingId
import io.smartreach.esp.utils.email.models.EmailReply.{GmailReplyTrackedViaAPI, OutlookReplyTrackedViaAPI}
import io.smartreach.esp.utils.email.models.{CommonPropsEmailMessage, ImapParsedReply}
import utils.email.EmailReplyStatus
import utils.email.services.InternalTrackingNote

import scala.collection.mutable.ListBuffer

sealed trait EmailMessageTrackedV4

case class PropsTrackedReply(
        inbox_email_setting_id: EmailSettingId,

        campaign_id: Option[CampaignId],
        step_id: Option[StepId],

        prospect_id_in_campaign: Option[ProspectId], // prospect_id for whom campaign should be paused

        prospect_account_id_in_campaign: Option[ProspectAccountsId],

        campaign_name: Option[String],
        step_name: Option[String],

        sr_inbox_read: Boolean,
//        original_inbox_folder: Option[String],
        email_status: EmailReplyStatus,

        scheduled_manually: Boolean,

        email_thread_id: Option[Long],
        team_id: TeamId,
        account_id: AccountId,

        internal_tracking_note: InternalTrackingNote.Value,

        tempThreadId: Option[Int] // used temporarily for identifying email threads during reply tracking

)

object EmailMessageTrackedV4 {



  case class GmailMessageTrackedV4(
          gmailReplyTrackedViaAPI: GmailReplyTrackedViaAPI,

          // =====================================
          propsTrackedReply: PropsTrackedReply



  ) extends EmailMessageTrackedV4


  case class OutlookMessageTrackedV4(
          outlookReplyTrackedViaAPI: OutlookReplyTrackedViaAPI,

          propsTrackedReply: PropsTrackedReply
  ) extends EmailMessageTrackedV4

  // todo: nxd shorten the names - for example imapParsedReply to mesg
  case class ImapTrackedReplyV4(
          imapParsedReply: ImapParsedReply,

          propsTrackedReply: PropsTrackedReply
  ) extends EmailMessageTrackedV4

  def getPropsTrackedReply(emailMessageTrackedV4: EmailMessageTrackedV4): PropsTrackedReply = {
    emailMessageTrackedV4 match {
      case gmailMessageTrackedV4: GmailMessageTrackedV4 =>
        gmailMessageTrackedV4.propsTrackedReply
      case imapTrackedReplyV4: ImapTrackedReplyV4 =>
        imapTrackedReplyV4.propsTrackedReply
      case outlookMessageTrackedV4: OutlookMessageTrackedV4 =>
        outlookMessageTrackedV4.propsTrackedReply
    }
  }

  def getCommonPropsEmailMessage(emailMessageTrackedV4: EmailMessageTrackedV4): CommonPropsEmailMessage = {
    emailMessageTrackedV4 match {
      case gmailMessageTrackedV4: GmailMessageTrackedV4 =>
        gmailMessageTrackedV4.gmailReplyTrackedViaAPI.commonPropsEmailMessage

      case imapTrackedReplyV4: ImapTrackedReplyV4 =>
        imapTrackedReplyV4.imapParsedReply.commonPropsEmailMessage

      case outlookMessageTrackedV4: OutlookMessageTrackedV4 =>
        outlookMessageTrackedV4.outlookReplyTrackedViaAPI.commonPropsEmailMessage

    }
  }

  def getGmailThreadId(emailMessageTrackedV4: EmailMessageTrackedV4) : Option[String] = {
    emailMessageTrackedV4 match {

      case gmailMessageTrackedV4: GmailMessageTrackedV4 =>
        Some(gmailMessageTrackedV4.gmailReplyTrackedViaAPI.gmail_thread_id)
        
      case _: OutlookMessageTrackedV4 =>
        None

      case _: ImapTrackedReplyV4 =>
        None

    }
  }

  def getOutlookConversationId(emailMessageTrackedV4: EmailMessageTrackedV4) : Option[String] = {
    emailMessageTrackedV4 match {

      case outlookMessageTrackedV4: OutlookMessageTrackedV4 =>
        Some(outlookMessageTrackedV4.outlookReplyTrackedViaAPI.outlook_converstation_id)

      case _: GmailMessageTrackedV4 =>
        None

      case _: ImapTrackedReplyV4 =>
        None

    }
  }

  def getReplyToHeader(emailMessageTrackedV4: EmailMessageTrackedV4): Option[String] = {
    getCommonPropsEmailMessage(emailMessageTrackedV4).in_reply_to_header
  }

  def getReplyToHeaders(trackedEmails: ListBuffer[EmailMessageTrackedV4] ): ListBuffer[String] = {
    trackedEmails.map(atrackedEmail =>
      getReplyToHeader(atrackedEmail))
            .filter(aTrackedEmail => aTrackedEmail.isDefined).map(_.get)
  }


  def getGmailThreadIds(trackedEmails: ListBuffer[EmailMessageTrackedV4])
  : Seq[String] = {
    trackedEmails
            .map(aTrackedEmail => {
                getGmailThreadId(aTrackedEmail)
              })
            .filter(_.isDefined)
            .map(_.get)
            .toSeq
  }

  def getOutlookConversationIds(trackedEmails: ListBuffer[EmailMessageTrackedV4])
  : Seq[String] = {
    trackedEmails
            .map(aTrackedEmail =>
                    getOutlookConversationId(aTrackedEmail)
            )
              .filter(_.isDefined)
              .map(_.get)
              .toSeq
  }

  def updatePropsTrackedReply(baseEmail: EmailMessageTrackedV4,
          propsTrackedReplyNew: PropsTrackedReply): EmailMessageTrackedV4 = {
    val email = baseEmail match {
      case gmailMessageTrackedV4: GmailMessageTrackedV4 =>
        gmailMessageTrackedV4.copy(propsTrackedReply = propsTrackedReplyNew
        )
      case outlookMessageTrackedV4: OutlookMessageTrackedV4 =>
        outlookMessageTrackedV4.copy(propsTrackedReply = propsTrackedReplyNew)
      case imapTrackedReplyV4: ImapTrackedReplyV4 =>
        imapTrackedReplyV4.copy(propsTrackedReply = propsTrackedReplyNew)


    }
    email
  }

  def copyTempThreadIdToMessage(
          msg           : EmailMessageTrackedV4,
          tempThreadId  : Int): EmailMessageTrackedV4 = {
    msg match {
      case GmailMessageTrackedV4(gmailReplyTrackedViaAPI, propsTrackedReply) =>
        GmailMessageTrackedV4(
          gmailReplyTrackedViaAPI = gmailReplyTrackedViaAPI,
          propsTrackedReply = propsTrackedReply.copy(tempThreadId = Some(tempThreadId)))

      case OutlookMessageTrackedV4(outlookReplyTrackedViaAPI, propsTrackedReply) =>
        OutlookMessageTrackedV4(
          outlookReplyTrackedViaAPI = outlookReplyTrackedViaAPI,
          propsTrackedReply = propsTrackedReply.copy(tempThreadId = Some(tempThreadId)))

      case EmailMessageTrackedV4.ImapTrackedReplyV4(imapParsedReply, propsTrackedReply) =>
        ImapTrackedReplyV4(
          imapParsedReply = imapParsedReply,
          propsTrackedReply = propsTrackedReply.copy(tempThreadId = Some(tempThreadId))
        )

    }
  }

}
