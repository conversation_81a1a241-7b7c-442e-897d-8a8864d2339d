package api.emails.models

import scala.util.Try

sealed trait CampaignProspectStepScheduleEventType

object CampaignProspectStepScheduleEventType {


  private val SCHEDULED = "scheduled"
  private val SENT = "sent"
  private val DELETED = "deleted"
  private val IGNORED = "ignored"
  private val MARKEDCOMPLETED = "marked_completed"


  case object Scheduled extends CampaignProspectStepScheduleEventType {
    override def toString: String = SCHEDULED
  }
  case object Sent extends CampaignProspectStepScheduleEventType {
    override def toString: String = SENT
  }
  case object Deleted extends CampaignProspectStepScheduleEventType {
    override def toString: String = DELETED
  }

  //adding for the debugging purpose for drip campaigns
  case object Ignored extends CampaignProspectStepScheduleEventType {
    override def toString: String = IGNORED
  }
  
  case object MarkedCompleted extends CampaignProspectStepScheduleEventType {
    override def toString: String = MARKEDCOMPLETED
  }

  def toKey(emailScheduleEventType: CampaignProspectStepScheduleEventType): String = {
    emailScheduleEventType.toString
  }

  def fromKey(key: String): Try[CampaignProspectStepScheduleEventType] = Try{
    key match {
      case `SCHEDULED` => Scheduled
      case `SENT` => Sent
      case `DELETED` => Deleted
      case `IGNORED` => Ignored
      case `MARKEDCOMPLETED` => MarkedCompleted
    }
  }



}
