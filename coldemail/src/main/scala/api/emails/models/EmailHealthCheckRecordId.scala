package api.emails.models

import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, JsNumber, JsSuc<PERSON>, JsValue, Reads, Writes}


case class EmailHealthCheckRecordId(id: Long) extends AnyVal {

  override def toString: String = id.toString

}

object EmailHealthCheckRecordId {

  given reads: Reads[EmailHealthCheckRecordId] = new Reads[EmailHealthCheckRecordId] {

    override def reads(ev: JsValue): JsResult[EmailHealthCheckRecordId] = {

      ev match {

        case JsNumber(id) => JsSuccess(EmailHealthCheckRecordId(id = id.toLong))

        case randomValue => JsError(s"expected number, got some random value - $randomValue")

      }

    }

  }

  given writes: Writes[EmailHealthCheckRecordId] = new Writes[EmailHealthCheckRecordId] {

    override def writes(o: EmailHealthCheckRecordId): JsValue = JsNumber(o.id)

  }

}
