package api.emails.models

import api.accounts.TeamId
import utils.SRLogger

sealed trait MqDeleteAndRevertDataMsgV2 {
  def toKey() : String

  def team_id: TeamId

}

object MqDeleteAndRevertDataMsgV2 {

  val task_only_message = "task_only_message"
  val email_only_message = "email_only_message"
  val task_and_email_both_message = "task_and_email_both_message"


  case class TaskOnlyMessage(
                              task_id: String,
                              team_id: TeamId,
                            ) extends  MqDeleteAndRevertDataMsgV2 {

    override def toKey(): String = task_only_message

  }

  case class EmailOnlyMessage(
                              email_scheduled_id: Long,
                              team_id: TeamId,
                              sender_email_setting_id: Option[Long]
                            ) extends  MqDeleteAndRevertDataMsgV2 {

    override def toKey(): String = email_only_message

  }

  case class TaskAndEmailMessage(
                              task_id: String,
                              email_scheduled_id : Long,
                              team_id: TeamId,
                              sender_email_setting_id: Option[Long]
                            ) extends  MqDeleteAndRevertDataMsgV2 {

    override def toKey(): String = task_and_email_both_message

  }


  def convertFromDbDataToMqMsg(
                                delete_and_revert_data_from_db: Seq[DeleteAndRevertDataFromDB],
                              )(using logger: SRLogger): Seq[MqDeleteAndRevertDataMsgV2] = {


    delete_and_revert_data_from_db
      .map(data => {

        (data.task_data, data.emails_scheduled_data) match {

          case (None, None) =>

            logger.fatal(s"convertFromDbDataToMqMsg :: task_data and email_data both none :: team_id ${data.teamId} ")
            None

          case (None, Some(e_data)) =>

            Some(MqDeleteAndRevertDataMsgV2.EmailOnlyMessage(
              email_scheduled_id = e_data.es_id,
              team_id = data.teamId,
              sender_email_setting_id = e_data.es_inbox_email_setting_id
            ))


          case (Some(t_data), None) =>

            Some(MqDeleteAndRevertDataMsgV2.TaskOnlyMessage(
              task_id = t_data.task_id,
              team_id = data.teamId,
            ))

          case (Some(t_data), Some(e_data)) =>

            Some(MqDeleteAndRevertDataMsgV2.TaskAndEmailMessage(
              email_scheduled_id  = e_data.es_id,
              task_id = t_data.task_id,
              team_id = data.teamId,
              sender_email_setting_id = e_data.es_inbox_email_setting_id
            ))

        }

      })
      .filter(_.isDefined)
      .map(_.get)


  }

}
