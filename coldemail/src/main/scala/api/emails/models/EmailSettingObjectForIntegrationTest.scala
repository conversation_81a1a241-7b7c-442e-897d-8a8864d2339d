package api.emails.models

import play.api.libs.json.{JsError, JsResult, JsSuccess, JsValue, Reads}

import scala.util.{Failure, Success, Try}

case class  EmailSettingApiError(
                                      error_type: String,
                                      message: String
                                    )
case object EmailSettingApiError{
  given reads: Reads[EmailSettingApiError] = new Reads[EmailSettingApiError] {
    override def reads(json: JsValue): JsResult[EmailSettingApiError] = Try {
      EmailSettingApiError(
        error_type = (json \ "error_type").as[String],
        message = (json \ "message").as[String]
      )
    } match {
      case Failure(e) => JsError(e.getMessage)
      case Success(emailSettingApiError) => JsSuccess(emailSettingApiError)
    }
  }
}
