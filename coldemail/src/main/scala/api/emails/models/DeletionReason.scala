package api.emails.models

import utils.email.models.DeleteEmailsScheduledType

sealed trait DeletionReason {

  def toString: String

}


object DeletionReason {

  private val previewUpdatedForEmail = "preview_updated_for_email"
  private val deletedVariantByDelete = "delete_variant_by_delete"
  private val deletedCampaign = "deleted_campaign"
  private val deletedVariantByDeleteV2 = "delete_variant_by_delete_v2"
  private val onStopOrArchiveCampaign = "on_stop_or_archive_campaign"
  private val addErrorAndDeleteUnsentEmailsForEmailSetting = "add_error_and_delete_unsent_emails_for_email_setting"
  private val saveEmailsAndRepliesFromInboxV3 = "save_emails_and_replies_from_inbox_v3"
  private val publishCompletedProspects = "publish_completed_prospects"
  private val hasOptedOutV2 = "has_opted_out_v2"
  private val deleteStuckEmails = "delete_stuck_emails"
  private val markProspectAsInvalidEmailOnInvalidAddressesException = "mark_prospect_as_invalid_email_on_invalid_addresses_exception"
  private val processSendEmailRequest = "process_send_email_request"
  private val processSendEmailRequestFollowUpFromOutlookApiAndParentMessageIdIsNotValid = "process_send_email_request_follow_up_from_outlook_api_and_parent_message_id_is_not_valid"

  case object PreviewUpdatedForEmail extends DeletionReason {

    override def toString: String = previewUpdatedForEmail

  }

  case object DeletedVariantByDelete extends DeletionReason {

    override def toString: String = deletedVariantByDelete

  }

  case object DeletedVariantByDeleteV2 extends DeletionReason {

    override def toString: String = deletedVariantByDeleteV2

  }

  case object OnStopOrArchiveCampaign extends DeletionReason {

    override def toString: String = onStopOrArchiveCampaign

  }

  case object AddErrorAndDeleteUnsentEmailsForEmailSetting extends DeletionReason {

    override def toString: String = addErrorAndDeleteUnsentEmailsForEmailSetting

  }

  case object SaveEmailsAndRepliesFromInboxV3 extends DeletionReason {

    override def toString: String = saveEmailsAndRepliesFromInboxV3

  }

  case object PublishCompletedProspects extends DeletionReason {

    override def toString: String = publishCompletedProspects

  }

  case object HasOptedOutV2 extends DeletionReason {

    override def toString: String = hasOptedOutV2

  }

  case object DeleteStuckEmails extends DeletionReason {

    override def toString: String = deleteStuckEmails

  }

   case object MarkProspectAsInvalidEmailOnInvalidAddressesException extends DeletionReason {

     override def toString: String = markProspectAsInvalidEmailOnInvalidAddressesException

   }

  case object ProcessSendEmailRequest extends DeletionReason {

    override def toString: String = processSendEmailRequest

  }

  case object ProcessSendEmailRequestFollowUpFromOutlookApiAndParentMessageIdIsNotValid extends DeletionReason {

      override def toString: String = processSendEmailRequestFollowUpFromOutlookApiAndParentMessageIdIsNotValid

  }

  case object DeletedCampaign extends DeletionReason {

    override def toString: String = deletedCampaign

  }

  case class Other(reason: String) extends DeletionReason {

    override def toString: String = "other_".concat(reason)

  }



  def isTaskIdSelectForDeletionRequired(
                                 deletionType : DeleteEmailsScheduledType
                               ): Boolean = {

    deletionType match {

      case _ : DeleteEmailsScheduledType.DeleteUnsentByEmailScheduledId =>

        false

      case _: DeleteEmailsScheduledType.DeleteUnsentByProspectId =>

        true

      case _: DeleteEmailsScheduledType.DeleteUnsentByCampaignId =>

        true

      case _: DeleteEmailsScheduledType.DeleteAllUnSentByEmailSettingId =>

        false

      case _: DeleteEmailsScheduledType.DeleteUnSentByEmailSettingIdNotManual =>

        false


    }




  }






}
