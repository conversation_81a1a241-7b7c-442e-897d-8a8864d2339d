package api.emails.models

import api.accounts.email.models.{EmailProvidedBy, EmailServiceProvider}
import api.emails.EmailSetting
import org.joda.time.DateTime
import play.api.libs.json.JodaWrites.*
import play.api.libs.json.{<PERSON><PERSON>, Writes}

case class WarmupHeroEmailSettingResponse(
  id: String,
  owner_id: String,

  service_provider: EmailServiceProvider,
  can_auto_connect: Boolean,

  first_name: String,
  last_name: String,

  email: String,

  created_at: Option[DateTime],

  smtp_username: Option[String],
  smtp_host: Option[String],
  smtp_port: Option[Int],

  imap_username: Option[String],
  imap_host: Option[String],
  imap_port: Option[Int],
)

object WarmupHeroEmailSettingResponse {

  given writes: Writes[WarmupHeroEmailSettingResponse] = Json.writes[WarmupHeroEmailSettingResponse]


  def fromEmailSetting(
    es: EmailSetting
  ): WarmupHeroEmailSettingResponse = {

    val canAutoConnect = WarmupHeroEmailSettingResponse.canAutoConnectInWarmupHero(esp = es.service_provider)

    WarmupHeroEmailSettingResponse(
      id = es.uuid.get.uuid, // TODO: check
      owner_id = es.owner_uuid.toString,

      service_provider = es.service_provider,
      can_auto_connect = canAutoConnect,

      first_name = es.first_name,
      last_name = es.last_name,

      email = es.email,

      created_at = es.created_at,

      smtp_username = es.smtp_username,
      smtp_host = es.smtp_host,
      smtp_port = es.smtp_port,

      imap_username = es.imap_username,
      imap_host = es.imap_host,
      imap_port = es.imap_port,
    )

  }

  def canAutoConnectInWarmupHero(esp: EmailServiceProvider) = {

    esp match {

      case EmailServiceProvider.GMAIL_ASP
           | EmailServiceProvider.EXCHANGE
           | EmailServiceProvider.NAMECHEAP
           | EmailServiceProvider.OTHER
           | EmailServiceProvider.REDIFF_MAIL =>

        true

      case EmailServiceProvider.GMAIL_API
           | EmailServiceProvider.GMAIL_ALIAS
           | EmailServiceProvider.OUTLOOK_API
           | EmailServiceProvider.AWS_SES
           | EmailServiceProvider.SENDGRID
           | EmailServiceProvider.MAILGUN =>


        false

    }

  }

}

case class WarmupHeroEmailSettingWithPasswordResponse(
  id: String,
  owner_id: String,

  service_provider: EmailServiceProvider,
  can_auto_connect: Boolean,

  is_purchased_from_sr: Boolean,

  first_name: String,
  last_name: String,

  email: String,

  created_at: Option[DateTime],

  smtp_username: Option[String],
  smtp_password: Option[String],
  smtp_host: Option[String],
  smtp_port: Option[Int],

  imap_username: Option[String],
  imap_password: Option[String],
  imap_host: Option[String],
  imap_port: Option[Int],
)

object WarmupHeroEmailSettingWithPasswordResponse {

  given writes: Writes[WarmupHeroEmailSettingWithPasswordResponse] = Json.writes[WarmupHeroEmailSettingWithPasswordResponse]

  def fromEmailSetting(
    es: EmailSetting
  ): WarmupHeroEmailSettingWithPasswordResponse = {

    val canAutoConnect = WarmupHeroEmailSettingResponse.canAutoConnectInWarmupHero(esp = es.service_provider)

    // defaulting to false is domain_provider = None
    val isPurchasedFromSr = es.domain_provider.exists {

      case EmailProvidedBy.User =>

        false

      case EmailProvidedBy.Maildoso =>

        true

      case EmailProvidedBy.ZapMail =>

        true

    }

    WarmupHeroEmailSettingWithPasswordResponse(
      id = es.uuid.get.uuid, // TODO: check
      owner_id = es.owner_uuid.toString,

      service_provider = es.service_provider,
      can_auto_connect = canAutoConnect,

      is_purchased_from_sr = isPurchasedFromSr,

      first_name = es.first_name,
      last_name = es.last_name,

      email = es.email,

      created_at = es.created_at,

      smtp_username = es.smtp_username,
      smtp_password = es.smtp_password,
      smtp_host = es.smtp_host,
      smtp_port = es.smtp_port,

      imap_username = es.imap_username,
      imap_password = es.imap_password,
      imap_host = es.imap_host,
      imap_port = es.imap_port,
    )

  }

}
