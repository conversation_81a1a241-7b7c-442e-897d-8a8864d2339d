package api.emails.models

import play.api.libs.json.{Format, Js<PERSON>rror, JsResult, JsString, JsSuccess, JsValue}

sealed trait DomainProvider {
  def toString: String
}

object DomainProvider {
  private val user = "user"
  private val maildoso = "maildoso"

  case object User extends DomainProvider {
    override def toString: String = user
  }

  case object Maildoso extends DomainProvider {
    override def toString: String = maildoso
  }

  def withName(key: String): Option[DomainProvider] = {
    key match {
      case `user` => Some(User)
      case `maildoso` => Some(Maildoso)
      case _ => None
    }
  }

  given format: Format[DomainProvider] = new Format[DomainProvider] {
    override def writes(o: DomainProvider): JsValue = {
      JsString(o.toString)
    }

    override def reads(json: JsValue): JsResult[DomainProvider] = {
      withName(json.as[String]) match {
        case None => JsError(s"Invalid DomainProvider: $json")
        case Some(value) => JsSuccess(value)
      }
    }
  }
}
