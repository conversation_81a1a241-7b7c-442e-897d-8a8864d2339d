package api.emails.models

import play.api.libs.json._

import scala.util.{Failure, Success, Try}

sealed abstract class InboxType {
  val textId: String

  override def toString: String = textId
}

object InboxType {

  private val KEY_SINGLE = "single"
  private val KEY_CONSOLIDATED = "consolidated"
  private val KEY_ALL_CAMPAIGNS = "all_campaigns"

  case object SINGLE extends InboxType {
    override val textId: String = KEY_SINGLE
  }

  case object CONSOLIDATED extends InboxType {
    override val textId: String = KEY_CONSOLIDATED
  }

  case object AllCampaigns extends InboxType {
    override val textId: String = KEY_ALL_CAMPAIGNS
  }

  def withName(name: String): Try[InboxType] = Try {

    name.toLowerCase.trim match {

      case KEY_SINGLE => SINGLE

      case KEY_CONSOLIDATED => CONSOLIDATED

      case KEY_ALL_CAMPAIGNS => AllCampaigns

      case _ => throw new Exception(s"Invalid Inbox type: $name")
    }

  }

  implicit def writes: Writes[InboxType] = new Writes[InboxType] {
    def writes(rs: InboxType): JsValue = {
      JsString(rs.textId)
    }
  }

  implicit def reads: Reads[InboxType] = new Reads[InboxType] {

    def reads(json: JsValue): JsResult[InboxType] = json match {

      case JsString(s) =>

        withName(name = s) match {

          case Failure(exception) =>
            JsError(s"Enumeration expected of type: InboxId, but it does not appear to contain the value: '$s'")

          case Success(value) =>
            JsSuccess(value)
        }

      case _ =>
        JsError("String value expected")

    }
  }


}
