package api.emails.models

import api.accounts.email.models.EmailServiceProvider
import api.{HelpfulErrorMessageTrait, HelpfulSolutionData}
import utils.SRLogger

object HelpfulErrorMessageDuringEmailIntegration {

  private val google_via_form_auth_fail = "[AUTHENTICATIONFAILED] Invalid credentials (Failure)"

  private val google_via_form_auth_fail_alert = "[ALERT] Invalid credentials (Failure)"

  private val google_via_form_login_with_account_password = "[ALERT] Application-specific password required: https://support.google.com/accounts/answer/185833 (Failure)"

  private val google_via_form_login_with_imap_setting_disabled = "[ALERT] Your account is not enabled for IMAP use. Please visit your Gmail settings page and enable your account for IMAP access. (Failure)"

  private val google_via_form_sending_limit_reached = "550-5.4.5 Daily user sending limit exceeded. For more information on Gmail\n550-5.4.5 sending limits go to\n550 5.4.5  https://support.google.com/a/answer/166852 8926c6da1cb9f-48937012d55sm5200059173.2 - gsmtp"

  private val mail_gun_wrong_credentials = "[MailgunService] Forbidden. Please check your API Key, Mailgun Email Domain and Mailgun Region"

  private val mail_gun_free_account_error = "Free accounts are for test purposes only. Please upgrade or add the address to authorized recipients in Account Settings."

  private val send_grid_wrong_email_passed = "[SendGrid error]: The from address does not match a verified Sender Identity. Mail cannot be sent until this error is resolved. Visit https://sendgrid.com/docs/for-developers/sending-email/sender-identity/ to see the Sender Identity requirements"

  private val send_grid_wrong_api_key_passed = "[SendGrid error]: The provided authorization grant is invalid, expired, or revoked"

  private val rediff_free_account = """AUTHFAIL: Error \[1,\d+\].""".r

  private val godaddy_restricted_error = "535 User (\\S+@\\S+\\.\\S+) has been restricted.".r

  private val rediff_random_error = "421 sorry, Your email could not be sent at this time. Please contact the administrator. (#5.7.1)"

  case class GoogleAuthFailViaForm(
                                 error: Throwable
                               ) extends HelpfulErrorMessageTrait {

    override val toKey: String = google_via_form_auth_fail

    override val header: String = "Google Connection Failure"

    override val helpful_message: String = "Check if (Email Address) and (App Specific Password) are correct"

    override val solution_list: Seq[HelpfulSolutionData] =
      Seq(
        HelpfulSolutionData(solution = "Connect your g-suite account", url = "https://help.smartreach.io/docs/connect-g-suite-with-smtp")
      )

  }

  case class GoogleLoginWithAccountPasswordViaForm(
                                                    error: Throwable
                                                  ) extends HelpfulErrorMessageTrait {

    override val toKey: String = google_via_form_login_with_account_password

    override val header: String = "Google Connection Failure"

    override val helpful_message: String = "Use an App-password instead of the main password of your Google account"

    override val solution_list: Seq[HelpfulSolutionData] =
      Seq(
        HelpfulSolutionData(solution = "Connect your g-suite account", url = "https://help.smartreach.io/docs/connect-g-suite-with-smtp")
      )

  }

  case class GoogleLoginWithImapDisabled(
                                          error: Throwable
                                        ) extends HelpfulErrorMessageTrait {

    override val toKey: String = google_via_form_login_with_imap_setting_disabled

    override val header: String = "Google Connection Failure"

    override val helpful_message: String = "IMAP Setting for this email account is disabled it should be turned on to integrate the email account"

    override val solution_list: Seq[HelpfulSolutionData] =
      Seq(
        HelpfulSolutionData(solution = "Enable Imap for Gmail account", url = "https://support.google.com/a/answer/105694?hl=en"),
      )

  }

  case class GoogleSendingLimitReached(
                                        error: Throwable
                                      ) extends HelpfulErrorMessageTrait {

    override val toKey: String = google_via_form_sending_limit_reached

    override val header: String = "Google Connection Failure"

    override val helpful_message: String = "Google has put a temporary restriction on sending from your email account. Usually, the sending automatically resumes after 24 hours."

    override val solution_list: Seq[HelpfulSolutionData] =
      Seq(
        HelpfulSolutionData(solution = "To learn more about Gmail sending limits in Google Workspace", url = "https://support.google.com/a/answer/166852")
      )

  }

  case class MailGunWrongCredentials(
                                      error: Throwable
                                    ) extends HelpfulErrorMessageTrait {

    override val toKey: String = mail_gun_wrong_credentials

    override val header: String = "MailGun Connection Failure"

    override val helpful_message: String = "Check  if (Email Address), (Mailgun Sending API Key), (Mailgun Email Domain) and (Region) are correct"

    override val solution_list: Seq[HelpfulSolutionData] = Seq(
      HelpfulSolutionData(solution = "Connect your mailgun account", url = "https://help.smartreach.io/docs/connect-mailgun"),
      HelpfulSolutionData(solution = "Setup mailgun", url = "https://help.smartreach.io/docs/setup-mailgun")
    )

  }

  case class MailGunFreeAccountUsageError(
                                           error: Throwable
                                         ) extends HelpfulErrorMessageTrait {

    override val toKey: String = mail_gun_free_account_error

    override val header: String = "MailGun Connection Failure"

    override val helpful_message: String = "1) Check if you are using a free Mailgun Account. if yes, then either upgrade your Mailgun plan, or try adding the recipient from Mailgun dashboard (as a Free Account allows you to send mails to max 5 recipients).\n" +
      "\n2) Check if (Email Address), (Mailgun Sending API Key),(Mailgun Email Domain) and (Region) inputs are correct."

    override val solution_list: Seq[HelpfulSolutionData] = Seq(
      HelpfulSolutionData(solution = "Connect your mailgun account", url = "https://help.smartreach.io/docs/connect-mailgun"),
      HelpfulSolutionData(solution = "Setup mailgun", url = "https://help.smartreach.io/docs/setup-mailgun"),
      HelpfulSolutionData(solution = "mailgun article", url = "https://help.mailgun.com/hc/en-us/articles/*********-Authorized-Recipients")
    )

  }

  case class SendGridWrongEmailPassed(
                                      error: Throwable
                                    ) extends HelpfulErrorMessageTrait {

    override val toKey: String = send_grid_wrong_email_passed

    override val header: String = "Sendgrid Connection Failure"

    override val helpful_message: String = "Check if (Email Address) that you have provided is correct"

    override val solution_list: Seq[HelpfulSolutionData] =
      Seq(
        HelpfulSolutionData(solution = "Connect your sendgrid account", url = "https://help.smartreach.io/docs/connect-sendgrid"),
        HelpfulSolutionData(solution = "Setup new domain", url = "https://help.smartreach.io/docs/setup-new-domain"),
        HelpfulSolutionData(solution = "Find sendgrid api key", url = "https://help.smartreach.io/docs/find-sendgrid-api-key"),
        HelpfulSolutionData(solution = "Sender identity", url = "https://sendgrid.com/docs/for-developers/sending-email/sender-identity")
      )

  }

  case class SendGridWrongApiKeyPassed(
                                       error: Throwable
                                     ) extends HelpfulErrorMessageTrait {

    override val toKey: String = send_grid_wrong_api_key_passed

    override val header: String = "SenGrid Connection Failure"

    override val helpful_message: String = "Check if the SendGrid API Key that is passed is correct or try generating a new API Key in the SendGrid dashboard and use that"

    override val solution_list: Seq[HelpfulSolutionData] =
      Seq(
        HelpfulSolutionData(solution = "Connect your sendgrid account", url = "https://help.smartreach.io/docs/connect-sendgrid"),
        HelpfulSolutionData(solution = "Setup new domain", url = "https://help.smartreach.io/docs/setup-new-domain"),
        HelpfulSolutionData(solution = "Find sendgrid api key", url = "https://help.smartreach.io/docs/find-sendgrid-api-key")
      )

  }

  case class RediffFreeAccount(
                                error: Throwable
                              ) extends HelpfulErrorMessageTrait {

    override val toKey: String = error.getMessage

    override val header: String = "Rediff Connection Failure"

    override val helpful_message: String = "Please verify your password and host details, and also check if you are using a free Rediff account (as its restricted by Rediff to use the free account elsewhere) and instead try using a Rediffmail Pro account."

    override val solution_list: Seq[HelpfulSolutionData] = Seq()

  }

  case class RediffRandomError(
                                error: Throwable
                              ) extends HelpfulErrorMessageTrait {

    override val toKey: String = error.getMessage

    override val header: String = "Rediff Connection Failure"

    override val helpful_message: String = "This is an issue from Rediff.You can wait for 15 minutes for the error to resolve automatically,and try re-connecting the account, or reach out to Rediff support directly."

    override val solution_list: Seq[HelpfulSolutionData] = Seq()

  }

  case class GoDaddyRestrictedError(
                                     error: Throwable
                                   ) extends HelpfulErrorMessageTrait {
    override val toKey: String = error.getMessage

    override val header: String = "GoDaddy Connection Failure"

    override val helpful_message: String = "It looks like GoDaddy has restricted your account. You can wait for 15 minutes, and try re-connecting the account, or reach out to GoDaddy support directly."

    override val solution_list: Seq[HelpfulSolutionData] = Seq()
  }

  def fromString(error: Throwable, selected_service_provider: EmailServiceProvider)(using logger: SRLogger): Option[HelpfulErrorMessageTrait] = {

    val msg = error.getMessage

    (error.getMessage, selected_service_provider) match {

      case (`google_via_form_auth_fail`,EmailServiceProvider.GMAIL_ASP) =>
        Some(
          GoogleAuthFailViaForm(
            error = error
          )
        )

      case (`google_via_form_auth_fail_alert`, EmailServiceProvider.GMAIL_ASP) =>
        Some(
          GoogleAuthFailViaForm(
            error = error
          )
        )

      case (`google_via_form_auth_fail_alert`, EmailServiceProvider.OTHER) =>
        Some(
          GoogleAuthFailViaForm(
            error = error
          )
        )

      case (`google_via_form_auth_fail`, EmailServiceProvider.OTHER) =>
        Some(
          GoogleAuthFailViaForm(
            error = error
          )
        )

      case (`google_via_form_login_with_account_password`, EmailServiceProvider.GMAIL_ASP) =>
        Some(
          GoogleLoginWithAccountPasswordViaForm(
            error = error
          )
        )

      case (`google_via_form_login_with_account_password`, EmailServiceProvider.OTHER) =>
        Some(
          GoogleLoginWithAccountPasswordViaForm(
            error = error
          )
        )

      case (`google_via_form_login_with_imap_setting_disabled`, EmailServiceProvider.GMAIL_ASP) =>
        Some(
          GoogleLoginWithImapDisabled(
            error = error
          )
        )

      case (`google_via_form_login_with_imap_setting_disabled`, EmailServiceProvider.OTHER) =>
        Some(
          GoogleLoginWithImapDisabled(
            error = error
          )
        )

      case (`google_via_form_sending_limit_reached`, EmailServiceProvider.GMAIL_ASP) =>
        Some(
          GoogleSendingLimitReached(
            error = error
          )
        )

      case (`google_via_form_sending_limit_reached`, EmailServiceProvider.OTHER) =>
        Some(
          GoogleSendingLimitReached(
            error = error
          )
        )

      case (`mail_gun_wrong_credentials`,EmailServiceProvider.MAILGUN) => Some(
        MailGunWrongCredentials(
          error = error
        )
      )

      case (msg,EmailServiceProvider.MAILGUN) if msg.contains(`mail_gun_free_account_error`) =>
        Some(
          MailGunFreeAccountUsageError(
            error = error
          )
        )

      case (`send_grid_wrong_email_passed`,EmailServiceProvider.SENDGRID) =>
        Some(
          SendGridWrongEmailPassed(
            error = error
          )
        )

      case (`send_grid_wrong_api_key_passed`,EmailServiceProvider.SENDGRID) =>
        Some(
          SendGridWrongApiKeyPassed(
            error = error
          )
        )

      case (msg,EmailServiceProvider.OTHER) if godaddy_restricted_error.pattern.matcher(msg.trim).matches =>
        Some(
          GoDaddyRestrictedError(
            error
          )
        )

      case (`rediff_random_error`,EmailServiceProvider.REDIFF_MAIL) =>
        Some(
          RediffRandomError(
            error = error
          )
        )

      case (`rediff_random_error`, EmailServiceProvider.OTHER) =>
        Some(
          RediffRandomError(
            error = error
          )
        )

      case  (msg,EmailServiceProvider.REDIFF_MAIL) if rediff_free_account.pattern.matcher(msg.trim).matches() => Some(RediffFreeAccount(error))

      case  (msg,EmailServiceProvider.OTHER) if rediff_free_account.pattern.matcher(msg.trim).matches() => Some(RediffFreeAccount(error))

      case _ =>
        logger.warn(s"unable to find a match for providing an helpful message for the error: ${error.getMessage} , selected_service_provider: ${selected_service_provider}")
        None

    }
  }

}