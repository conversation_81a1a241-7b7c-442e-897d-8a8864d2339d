package api.emails.models

import play.api.libs.json._

import scala.util.{Failure, Success, Try}

//This enum is for API to getConversations from all folders for inbox
sealed abstract class FolderId {
  val textId: String

  override def toString: String = textId
}


object FolderId {

  private val KEY_PROSPECTS = "prospects"
  private val KEY_NON_PROSPECTS = "non-prospects"
  private val KEY_DONE = "done"
  private val KEY_SNOOZED = "snoozed"
  private val KEY_SENT = "sent"
  private val KEY_IRRELEVANT = "irrelevant"

  case object PROSPECTS extends FolderId {
    override val textId: String = KEY_PROSPECTS
  }

  case object NON_PROSPECTS extends FolderId {
    override val textId: String = KEY_NON_PROSPECTS
  }

  case object DONE extends FolderId {
    override val textId: String = KEY_DONE
  }

  case object SNOOZED extends FolderId {
    override val textId: String = KEY_SNOOZED
  }

  case object SENT extends FolderId {
    override val textId: String = KEY_SENT
  }

  case object IRRELEVANT extends FolderId {
    override val textId: String = KEY_IRRELEVANT
  }


  def withName(name: String): Try[FolderId] = Try {

    name.toLowerCase.trim match {
      case KEY_PROSPECTS => PROSPECTS
      case KEY_NON_PROSPECTS => NON_PROSPECTS
      case KEY_DONE => DONE
      case KEY_SNOOZED => SNOOZED

      case KEY_SENT => SENT

      case KEY_IRRELEVANT => IRRELEVANT

      case _ => throw new Exception(s"Invalid Inbox Id: $name")
    }
  }

  implicit def writes: Writes[FolderId] = new Writes[FolderId] {
    def writes(rs: FolderId): JsValue = {
      JsString(rs.textId)
    }
  }

  implicit def reads: Reads[FolderId] = new Reads[FolderId] {

    def reads(json: JsValue): JsResult[FolderId] = json match {

      case JsString(s) =>

        withName(name = s) match {

          case Failure(exception) =>
            JsError(s"Enumeration expected of type: FolderId, but it does not appear to contain the value: '$s'")

          case Success(value) =>
            JsSuccess(value)
        }

      case _ =>
        JsError("String value expected")

    }
  }


}



