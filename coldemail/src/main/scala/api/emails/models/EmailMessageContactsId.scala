package api.emails.models

import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, JsResult, JsSuccess, JsValue, Reads, Writes}

case class EmailMessageContactsId(id: Long) extends AnyVal {

  override def toString: String = id.toString

}

object EmailMessageContactsId {

  given reads: Reads[EmailMessageContactsId] = new Reads[EmailMessageContactsId] {
    override def reads(ev: JsValue): JsResult[EmailMessageContactsId] = {
      ev match {
        case JsNumber(id) => JsSuccess(EmailMessageContactsId(id = id.toLong))
        case randomValue => JsError(s"expected number, got some random value - $randomValue")
      }
    }
  }

  given writes: Writes[EmailMessageContactsId] = new Writes[EmailMessageContactsId] {
    override def writes(o: EmailMessageContactsId): JsValue = JsNumber(o.id)
  }
}
