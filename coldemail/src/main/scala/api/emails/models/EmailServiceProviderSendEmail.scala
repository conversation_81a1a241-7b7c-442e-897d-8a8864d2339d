package api.emails.models

import api.accounts.email.models.EmailServiceProvider
import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, <PERSON>sResult, JsString, JsSuccess, JsValue, Reads, Writes}

import scala.util.{Failure, Success, Try}

sealed trait EmailServiceProviderSendEmail {
  def toKey: String
  override def toString: String = toKey
}

object EmailServiceProviderSendEmail {

  private val outlook = "outlook"
  private val other = "other"

  case object OUTLOOK extends EmailServiceProviderSendEmail {
    override def toKey: String = outlook
  }

  case object OTHER extends EmailServiceProviderSendEmail {
    override def toKey: String = other
  }

  def fromKey(key: String): Try[EmailServiceProviderSendEmail] = Try {

    key match {
      case `outlook` => OUTLOOK
      case `other` => OTHER
    }

  }


  implicit def reads: Reads[EmailServiceProviderSendEmail] = new Reads[EmailServiceProviderSendEmail] {

    def reads(json: JsValue): JsR<PERSON>ult[EmailServiceProviderSendEmail] = json match {

      case JsString(s) =>

        fromKey(key = s) match {

          case Failure(exception) =>

            val response = s"Enumeration expected of type: EmailServiceProviderSendEmail, but it does not appear to contain the value: '$s'"

            JsError(response)

          case Success(value) =>

            JsSuccess(value)
        }

      case _ =>
        JsError("String value expected")

    }
  }

  implicit def writes: Writes[EmailServiceProviderSendEmail] = (ev: EmailServiceProviderSendEmail) => {

    JsString(ev.toKey)

  }

  def getEmailServiceProviderSendEmail(emailServiceProvider: EmailServiceProvider): EmailServiceProviderSendEmail = {
    emailServiceProvider match {
      case EmailServiceProvider.GMAIL_API |
           EmailServiceProvider.GMAIL_ALIAS |
           EmailServiceProvider.GMAIL_ASP |
           EmailServiceProvider.MAILGUN |
           EmailServiceProvider.SENDGRID |
           EmailServiceProvider.EXCHANGE |
           EmailServiceProvider.AWS_SES |
           EmailServiceProvider.NAMECHEAP |
           EmailServiceProvider.OTHER |
           EmailServiceProvider.REDIFF_MAIL => EmailServiceProviderSendEmail.OTHER

      case EmailServiceProvider.OUTLOOK_API => EmailServiceProviderSendEmail.OUTLOOK

    }
  }
}
