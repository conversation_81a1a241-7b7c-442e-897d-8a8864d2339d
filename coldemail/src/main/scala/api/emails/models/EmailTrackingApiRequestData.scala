package api.emails.models

import api.sr_audit_logs.models.EventType
import org.joda.time.DateTime
import play.api.libs.json.Json
import play.api.mvc.Headers
import scalikejdbc.{AutoSession, DB, scalikejdbcSQLInterpolationImplicitDef}
import scalikejdbc.jodatime.JodaWrappedResultSet._

import scala.util.Try

case class EmailTrackingApiRequestData(
  trackingRequestId: Long,
  ip: String,
  userAgent: String,
  createdAt: DateTime,
  eventType: EventType
)

object EmailTrackingApiRequestData {

  implicit val session: AutoSession.type = AutoSession

  def saveEmailTrackingApiRequestHeader(
    traceReqId: String,
    reqHost: String,
    reqURI: String,
    postRequestUrl: Option[String],
    eventType: EventType,
    ip: String,
    userAgent: String,
    requestHeaders: Headers
  ): Try[Option[Long]] = Try {
    DB autoCommit { implicit session =>
      sql"""
        INSERT INTO email_tracking_api_request_data
        (
           trace_req_id,
           req_host,
           req_uri,
           post_req_url,

           event_type,
           ip,
           user_agent,
           req_headers
        )
        VALUES (
          $traceReqId,
          $reqHost,
          $reqURI,
          $postRequestUrl,

          ${eventType.toString},
          $ip,
          $userAgent,
          to_json(${Json.toJson(requestHeaders.toSimpleMap).toString}::jsonb)

        )
        ON CONFLICT DO NOTHING
        RETURNING id;
      """
        .map(_.long("id"))
        .single
        .apply()

    }

  }


  def getEmailTrackingApiRequestIdFromTraceId(
    traceReqId: String
  ): Try[Option[Long]] = Try {

    DB readOnly {implicit session =>

      sql"""
        SELECT id from email_tracking_api_request_data
        WHERE trace_req_id = $traceReqId
        LIMIT 1
       """
        .map(_.long("id"))
        .single
        .apply()

    }
  }

  def getEmailTrackingApiRequestDataFromTraceId(
    traceReqId: String
  ): Try[Option[EmailTrackingApiRequestData]] = Try {

    DB readOnly {implicit session =>

      sql"""
        SELECT
          id,
          event_type,
          ip,
          user_agent,
          created_at

        from email_tracking_api_request_data
        WHERE trace_req_id = $traceReqId
        LIMIT 1
       """
        .map(rs => {

          EmailTrackingApiRequestData(

            trackingRequestId = rs.long("id"),
            ip = rs.stringOpt("ip").getOrElse(""),
            userAgent = rs.stringOpt("user_agent").getOrElse(""),
            eventType = EventType.fromKey(
              rs.string("event_type")
            ).get,
            createdAt = rs.jodaDateTime("created_at")

          )

        })
        .single
        .apply()

    }
  }

  def updateSuspectedBotId(
    trackingRequestId: Long,
    suspectedBotId: Long
  ): Try[Int] = Try {

    DB.autoCommit { implicit session =>
      sql"""
      UPDATE email_tracking_api_request_data
      SET
        ignored_as_suspected_bot_id = $suspectedBotId
      WHERE
        id = $trackingRequestId
        AND ignored_as_suspected_bot_id IS NULL
        ;
      """
        .update
        .apply()

    }
  }
}