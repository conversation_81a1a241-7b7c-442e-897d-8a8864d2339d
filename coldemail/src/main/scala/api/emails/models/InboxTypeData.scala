package api.emails.models

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.OrgId
import api.campaigns.services.CampaignId
import api.prospects.models.{ProspectAccountsId, ProspectId}
import io.smartreach.esp.api.emails.EmailSettingId
import io.sr.billing_common.models.PlanType
import utils.featureflags.services.{OrgMetadataService, SrFeatureFlags}
import utils.{<PERSON><PERSON>, SRLogger}

import scala.util.{Failure, Success, Try}

sealed trait InboxTypeData {

  def inbox_type: InboxType

}


object InboxTypeData {


  case class SINGLE_DATA (

                         team_inbox_id : Long
                         ) extends InboxTypeData {

    override  def inbox_type: InboxType = InboxType.SINGLE

  }

  case class CONSOLIDATED_DATA(
                                allow_for_consolidated_inbox: Boolean
                              ) extends InboxTypeData {

    override  def inbox_type: InboxType = InboxType.CONSOLIDATED

  }

  case class ALLCAMPAIGNS_DATA(
                                prospect_id: Option[ProspectId],
                                prospect_account_id: Option[ProspectAccountsId],
                                campaign_id: Option[CampaignId]
                              )  extends InboxTypeData {

    override def inbox_type: InboxType = InboxType.AllCampaigns
  }


  def getInboxTypeData(
                        show_campaign_inbox: Boolean,
                        team_inbox_id: String, // long or string
                        org_id: OrgId,
                        campaign_id: Option[CampaignId],
                        prospect_id: Option[ProspectId],
                        prospect_account_id: Option[ProspectAccountsId],
                        allow_for_consolidated_inbox: Boolean = false
                      )(using logger: SRLogger): Try[InboxTypeData] = {

    /*
      15-may-2024: below is not one of the best way to write the code. but to change it we need to make
      changes in frontend. currently have moved this check to topMost layer. We can take frontend changes
      as a separate thing. 

     */
   val inbox_type_data: Try[InboxTypeData] = Helpers.stringToLong(team_inbox_id) match {
      case Failure(_) =>

        if (team_inbox_id == InboxType.CONSOLIDATED.toString && allow_for_consolidated_inbox) {

          Success(InboxTypeData.CONSOLIDATED_DATA(
            allow_for_consolidated_inbox = allow_for_consolidated_inbox
          ))

        } else if (
          (team_inbox_id == InboxType.AllCampaigns.toString) && show_campaign_inbox
        ) {

          if(prospect_id.isDefined && prospect_account_id.isDefined) {
            logger.shouldNeverHappen("Cannot pass both prospect_id and prospect_account_id")
            Failure(new Throwable("Sorry there was an error. Please contact support"))
          } else {
            Success(InboxTypeData.ALLCAMPAIGNS_DATA(
              campaign_id = campaign_id,
              prospect_id = prospect_id,
              prospect_account_id = prospect_account_id
            ))
          }


        } else {
          Failure(new Throwable("Sorry there was an error. Please contact support"))
        }

      case Success(team_inbox_in_long) =>

        Success(InboxTypeData.SINGLE_DATA(
          team_inbox_id = team_inbox_in_long
        ))
    }

    inbox_type_data

  }


}