package api.emails.models

import play.api.libs.json.{Js<PERSON><PERSON>r, JsResult, JsString, JsSuccess, JsValue, Reads, Writes}

case class EmailSettingUuid(uuid: String){
  override def toString: String = uuid
}

object EmailSettingUuid{
  given reads: Reads[EmailSettingUuid] = new Reads[EmailSettingUuid] {
    override def reads(ev: JsValue): JsResult[EmailSettingUuid] = {
      ev match {
        case JsString(uuid) => JsSuccess(EmailSettingUuid(uuid = uuid))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }
  }

  given writes: Writes[EmailSettingUuid] = new Writes[EmailSettingUuid] {
    override def writes(o: EmailSettingUuid): JsValue = JsString(o.uuid)
  }
}
