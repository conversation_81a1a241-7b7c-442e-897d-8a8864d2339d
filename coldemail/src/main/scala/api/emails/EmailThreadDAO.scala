package api.emails

import api.AppConfig
import api.accounts.models.{AccountId, OrgId}
import api.accounts.{Account, TeamId}
import api.campaigns.services.CampaignId
import api.emails.models.{InboxType, InboxTypeData, ManualEmailThreadData}
import api.prospects.MailboxFolder.CampaignInboxFolder
import api.prospects.dao.ProspectIdAndPotentialDuplicateProspectId
import api.prospects.{MessageThreadDAOTrait, ValidatedConvReq}
import api.team_inbox.model.{FolderType, ReplySentimentType, ReplySentimentTypeData, ReplySentimentUpdatedBy}
import api.prospects.models.{ProspectAccountsId, ProspectId}
import api.team_inbox.model.{FolderType, ReplySentimentType, ReplySentimentTypeData}
import api.team_inbox.service.{ReplySentimentUuid, UpdateReplySentimentFormForDAO}
import eventframework.{ConversationCampaign, ConversationContact, ConversationObject, ConversationObjectInboxV3, ConversationProspect, MessageObject, SrResourceTypes}
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import play.api.libs.json.{JsError, JsResult, JsSuccess, JsValue, OWrites, Reads, Writes}
import scalikejdbc.interpolation.SQLSyntax
import scalikejdbc.{SQLSyntax, sqls}
import sr_scheduler.CampaignStatus
import sr_scheduler.models.ChannelType
import utils.dbutils.DBUtils
import utils.helpers.LogHelpers
//import api.prospects.ContactQueryTypeParent.{ContactQueryType, ContactQueryTypeIncidental}
import api.prospects.{ InferredQueryTimeline, MailboxFolder, SearchQuerySelectType}
import api.search.SearchQuery
import org.joda.time.DateTime
import org.postgresql.util.PGobject
import play.api.libs.json.Json
import scalikejdbc.{AutoSession, DB, WrappedResultSet, _}
import scalikejdbc.jodatime.JodaWrappedResultSet._
import utils.dbutils.SQLUtils
import utils.{Helpers, ParseUtils, SRLogger}
import utils.email.EmailHelper
import utils.email.services.InternalTrackingNote
import play.api.libs.json.Format
import play.api.libs.json.JodaWrites._


import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}


case class EmailSearchQueryResponse(
  page: Int,
  show_load_more: Boolean,
  campaign_id: Option[CampaignId],
  replies: Seq[ConversationObject]
)

object EmailSearchQueryResponse {
  given writes: OWrites[EmailSearchQueryResponse] = Json.writes[EmailSearchQueryResponse]
}



case class EmailSearchQueryResponseInboxV3(
  has_more: Boolean,
  conversations: Seq[ConversationObjectInboxV3.EmailConversationObjectInboxV3]
)

object EmailSearchQueryResponseInboxV3 {
  given writes: OWrites[EmailSearchQueryResponseInboxV3] = Json.writes[EmailSearchQueryResponseInboxV3]
}

case class EmailThreadUuid(uuid: String) extends AnyVal {
  override def toString: String = uuid
}


// this is also used in oldinbox apis, thats why "prospect_id"
// fixme inboxv3: replace this with NewEmailThreadV3
/*
case class NewEmailThread(
  temp_thread_id: Int,
  owner_id: AccountId,
  team_id: TeamId,
  campaign_id: Option[CampaignId],
  campaign_name: Option[String],
  prospect_id: Option[ProspectId],
  admin_email: String,
  email_settings_id: EmailSettingId,
  prospect_email: String,
  subject: String,
  latest_email_id: Option[Long],
  gmail_msg_id: Option[String],
  gmail_thread_id: Option[String],

  outlook_msg_id: Option[String],
  outlook_conversation_id: Option[String],

  internal_tracking_note: InternalTrackingNote.Value

)
*/
// newinbox
case class NewEmailThreadV3(
                             temp_thread_id: Int,
                             uuid: EmailThreadUuid,
                             owner_id: AccountId,
                             team_id: TeamId,
                             campaign_id: Option[CampaignId],
                             campaign_name: Option[String],
                             inbox_email: String,
                             inbox_email_settings_id: EmailSettingId,
                             subject: String,
                             latest_email_id: Option[Long],
                             gmail_msg_id: Option[String],
                             gmail_thread_id: Option[String],

                             outlook_msg_id: Option[String],
                             outlook_conversation_id: Option[String],

                             internal_tracking_note: InternalTrackingNote.Value,
                             // - computed from the no of prospect ids > 0
                             has_prospect: Boolean,

                             folder_type: FolderType
)

case class AssociateEmailThreadProspect(
  emailThreadId: Long,
  prospectId: Long,
  temp_thread_id: Option[Int]
)

case class SavedEmailThread(
  id: Long,
  temp_thread_id: Option[Long],
  folder_type: Option[String]
)

sealed trait ThreadForProspectInternal {
  def id: Long

  def channelType: ChannelType

  def team_id: Long

  def owner_id: Long

  def campaign_id: Option[Long]

  def campaign_name: Option[String]

  def inbox_owner_id: AccountId
}

object ThreadForProspectInternal {
  case class EmailThreadForProspectInternal(
                                             id: Long,
                                             channelType: ChannelType = ChannelType.EmailChannel,
                                             team_id: Long,
                                             owner_id: Long,
                                             campaign_id: Option[Long],
                                             campaign_name: Option[String],
                                             inbox_owner_id: AccountId,
                                             gmail_thread_id: Option[String]
                                           ) extends ThreadForProspectInternal

  case class LinkedinThreadForProspectInternal(
                                                id: Long,
                                                channelType: ChannelType = ChannelType.LinkedinChannel,
                                                team_id: Long,
                                                owner_id: Long,
                                                campaign_id: Option[Long],
                                                inbox_owner_id: AccountId,
                                                campaign_name: Option[String]
                                              ) extends ThreadForProspectInternal
}

case class team_inbox_conversations (
                         team_inbox_id: Option[Long],
                         conversation_ids: List[String]
                       )
object team_inbox_conversations {
  given reads: Reads[team_inbox_conversations] = Json.reads[team_inbox_conversations]
}

case class InboxThreadSnoozeFormData(
  thread_ids: List[Long],
  snoozed_till: Option[Long],
  is_snoozed: Boolean
)
object InboxThreadSnoozeFormData {
  given reads: Reads[InboxThreadSnoozeFormData] = Json.reads[InboxThreadSnoozeFormData]
}
case class InboxThreadSnoozeFormDataV2(
                                      team_inbox_conversations: List[team_inbox_conversations],
                                      snoozed_till: Option[Long],
                                      is_snoozed: Boolean
                                    )
object InboxThreadSnoozeFormDataV2 {
  given reads: Reads[InboxThreadSnoozeFormDataV2] = Json.reads[InboxThreadSnoozeFormDataV2]
}



case class InboxThreadArchiveFormData(
                                      thread_ids: Seq[Long],
                                      archive: Boolean
                                    )

object InboxThreadArchiveFormData {
  given reads: Reads[InboxThreadArchiveFormData] = Json.reads[InboxThreadArchiveFormData]
}
case class InboxThreadArchiveFormData_V2(
                                       team_inbox_conversations: List[team_inbox_conversations],
                                       archive: Boolean
                                     )

object InboxThreadArchiveFormData_V2 {
  given reads: Reads[InboxThreadArchiveFormData_V2] = Json.reads[InboxThreadArchiveFormData_V2]
}

case class ConversationsSearchResponse(
                                        conv_id: String,
                                        team_inbox_id: Option[Long],
                                        team_inbox_name: Option[String] = None, //default none for linkedin
                                        title: String,
                                        from: Option[IEmailAddress] = None, //default none for linkedin
                                        msg_preview: Option[String] = None, //default none for linkedin
                                        folder_type: Option[String] = None, //default none for linkedin
                                        sent_at : Option[DateTime] = None,
                                        is_read: Option[Boolean] = None,
                                        description: String //to_email in email inbox and to_profile_url in linkedin
                                      )
object ConversationsSearchResponse{
  given writes: Writes[ConversationsSearchResponse] = new Writes[ConversationsSearchResponse] {

    def writes(rs: ConversationsSearchResponse): JsValue = {

      Json.obj(
        "conv_id" -> rs.conv_id,
        "team_inbox_id" -> rs.team_inbox_id,
        "team_inbox_name" -> rs.team_inbox_name,
        "title" -> rs.title,
        "from" -> rs.from,
        "msg_preview" -> rs.msg_preview,
        "folder_type" -> rs.folder_type,
        "sent_at" -> rs.sent_at,
        "is_read" -> rs.is_read,
        "description" -> rs.description
      )
    }
  }}

case class SearchedEmailThreads(
                                 email: String,
                                 scheduled_id: Long,
                                 email_thread_id: Long
                               )

case class EmailThreadUpdateLatestEmailData(
  email_thread_id: Long,
  by_account: Boolean,
  latest_email_id: Long,
  sent_at: DateTime,
  latest_sent_by_admin_at: Option[DateTime],
  by_prospect: Boolean,
  folderType: FolderType
)

case class EmailThreadSearchResultIntermediate(
                                                     page: Int,
                                                     campaign_id: Long,
                                                     replies: ConversationObject
                                                   )

case class ThreadAndTeamId(
                            thread_id: Int,     //fixme: Convert it to Long
                            teamId: Long
                          )

sealed trait QueryTimeLine

object QueryTimeLine {
  case class Before(dateTime: DateTime) extends QueryTimeLine
  case class After(dateTime: DateTime) extends QueryTimeLine
  case class At(dateTime: DateTime) extends QueryTimeLine
}

object EmailThreadDAO {
  /*
    def getConversationQueryV2(teamId: Long,
                             permittedOwnerIds: Seq[Long],
                             conv_query_type: ContactQueryTypeParent
                            ): SQL[Nothing, NoExtractor] = {
    /*
      ${hasProspectsCondition}
      ${reply_at_condition}
      ${orderBy}
        limit ${page_size}

     */
    val (remaining_clauses : SQLSyntax, prospects_inner_join_query: SQLSyntax) = conv_query_type match {
      case pagination_data: ContactQueryTypeParent.ContactQueryType.ProspectWithProspectCategory =>

        val hasProspectsCondition : SQLSyntax = pagination_data.folderType match {
          case InboxFolder.Prospects =>
            sqls" AND et.has_prospect = true"
        }
        val (reply_at_condition: SQLSyntax, orderBy: SQLSyntax) = pagination_data.queryTimeLine match {
          case QueryTimeLine.Before(dateTime) =>
            (sqls" AND et.latest_reply_at < ${dateTime}", sqls" ORDER BY et.latest_reply_at desc")

          case QueryTimeLine.After(dateTime) =>
            (sqls" AND et.latest_reply_at > ${dateTime}", sqls" ORDER BY et.latest_reply_at asc")

          case _ => ""

        }
        val prospect_cat_condition = sqls" AND p.prospect_category_id_custom = ${pagination_data.prospectCategoryId} "
        val final_sql_clause = sqls""" ${hasProspectsCondition}
                 ${reply_at_condition}
                 ${prospect_cat_condition}
                 $orderBy
                 limit ${pagination_data.page_size}
                """
        val prospects_inner_join_query = sqls" inner join prospects p on et.prospect_id = p.id and et.team_id = p.team_id "
        (final_sql_clause, prospects_inner_join_query)


      case pagination_data:  ContactQueryTypeParent.ContactQueryTypeIncidental.ProspectWithProspectCategoryExactlyAt =>

        val hasProspectsCondition : SQLSyntax = pagination_data.folderType match {
          case InboxFolder.Prospects =>
            sqls" AND et.has_prospect = true"
        }
        val (reply_at_condition: SQLSyntax, orderBy: SQLSyntax) = pagination_data.queryTimeLine match {
          case QueryTimeLine.At(dateTime) =>
            (sqls" AND et.latest_reply_at = ${dateTime}", sqls" ")
        }

        val prospect_cat_condition = sqls" AND p.prospect_category_id_custom = ${pagination_data.prospectCategoryId} "
        val final_sql_clause = sqls""" ${hasProspectsCondition}
                 ${reply_at_condition}
                 ${prospect_cat_condition}
                 $orderBy
                 limit ${pagination_data.page_size}
                """
        val prospects_inner_join_query = sqls" inner join prospects p on et.prospect_id = p.id and et.team_id = p.team_id "
        (final_sql_clause, prospects_inner_join_query)


      case pagination_data: ContactQueryType.ProspectWithoutProspectCategory =>

        val hasProspectsCondition : SQLSyntax = pagination_data.folderType match {
          case InboxFolder.Prospects =>
            sqls" AND et.has_prospect = true"

        }
        val (reply_at_condition: SQLSyntax, orderBy: SQLSyntax) = pagination_data.queryTimeLine match {

          case QueryTimeLine.Before(dateTime) =>
            (sqls" AND et.latest_reply_at < ${dateTime}", sqls" ORDER BY et.latest_reply_at desc")

          case QueryTimeLine.After(dateTime) =>
            (sqls" AND et.latest_reply_at > ${dateTime}", sqls" ORDER BY et.latest_reply_at asc")

          case _ => ""

        }
        val final_sql_clause = sqls""" ${hasProspectsCondition}
                 ${reply_at_condition}
                 $orderBy
                 limit ${pagination_data.page_size}
                """
        (final_sql_clause, sqls"")


      case pagination_data: ContactQueryTypeIncidental.ProspectWithoutProspectCategoryExactlyAt =>

        val hasProspectsCondition : SQLSyntax = pagination_data.folderType match {
          case InboxFolder.Prospects =>
            sqls" AND et.has_prospect = true"
        }
        val (reply_at_condition: SQLSyntax, orderBy: SQLSyntax) = pagination_data.queryTimeLine match {
          case QueryTimeLine.At(dateTime) =>
            (sqls" AND et.latest_reply_at = ${dateTime}", sqls" ")
        }
        val final_sql_clause = sqls""" ${hasProspectsCondition}
                 ${reply_at_condition}
                 $orderBy
                 limit ${pagination_data.page_size}
                """
        (final_sql_clause, sqls"")

      case pagination_data: ContactQueryType.NonProspect =>

        val hasProspectsCondition : SQLSyntax = pagination_data.folderType match {
          case InboxFolder.NonProspects =>
            sqls" AND et.has_prospect = false"
        }
        val (reply_at_condition: SQLSyntax, orderBy: SQLSyntax) = pagination_data.queryTimeLine match {
          case QueryTimeLine.Before(dateTime) =>
            (sqls" AND et.latest_reply_at < ${dateTime}", sqls" ORDER BY et.latest_reply_at desc")

          case QueryTimeLine.After(dateTime) =>
            (sqls" AND et.latest_reply_at > ${dateTime}", sqls" ORDER BY et.latest_reply_at asc")

          case _ => ""

        }
        val final_sql_clause = sqls""" ${hasProspectsCondition}
                 ${reply_at_condition}
                 $orderBy
                 limit ${pagination_data.page_size}
                """
        (final_sql_clause, sqls"")


      case pagination_data: ContactQueryTypeIncidental.NonProspectExactlyAt =>

        val hasProspectsCondition : SQLSyntax = pagination_data.folderType match {
          case InboxFolder.NonProspects =>
            sqls" AND et.has_prospect = false"
        }
        val (reply_at_condition: SQLSyntax, orderBy: SQLSyntax) = pagination_data.queryTimeLine match {
          case QueryTimeLine.At(dateTime) =>
            (sqls" AND et.latest_reply_at = ${dateTime}", sqls" ")
        }
        val final_sql_clause = sqls""" ${hasProspectsCondition}
                 ${reply_at_condition}
                 $orderBy
                 limit ${pagination_data.page_size}
                """
        (final_sql_clause, sqls"")
      case single_conv_data: ContactQueryTypeParent.ContactQueryTypeSingle.ProspectQueryById =>
        val final_sql_clause = sqls" AND et.id = ${single_conv_data.conv_id} "

        (final_sql_clause, sqls"")

      case pagination_data: ContactQueryType.DoneFolder =>

        val doneCondition : SQLSyntax = pagination_data.folderType match {
          case MailboxFolder.DoneFolder =>
            sqls" AND et.archived = true"
        }
        val (reply_at_condition: SQLSyntax, orderBy: SQLSyntax) = pagination_data.queryTimeLine match {
          case QueryTimeLine.Before(dateTime) =>
            (sqls" AND et.latest_reply_at < ${dateTime}", sqls" ORDER BY et.latest_reply_at desc")

          case QueryTimeLine.After(dateTime) =>
            (sqls" AND et.latest_reply_at > ${dateTime}", sqls" ORDER BY et.latest_reply_at asc")

          case _ => ""

        }
        val final_sql_clause = sqls""" ${doneCondition}
                 ${reply_at_condition}
                 $orderBy
                 limit ${pagination_data.page_size}
                """
        (final_sql_clause, sqls"")

      case pagination_data: ContactQueryType.SnoozedFolder =>

        val snoozedCondition : SQLSyntax = pagination_data.folderType match {
          case MailboxFolder.SnoozedFolder =>
            sqls" AND et.snoozed = true"
        }
        val (reply_at_condition: SQLSyntax, orderBy: SQLSyntax) = pagination_data.queryTimeLine match {
          case QueryTimeLine.Before(dateTime) =>
            (sqls" AND et.latest_reply_at < ${dateTime}", sqls" ORDER BY et.latest_reply_at desc")

          case QueryTimeLine.After(dateTime) =>
            (sqls" AND et.latest_reply_at > ${dateTime}", sqls" ORDER BY et.latest_reply_at asc")

          case _ => ""

        }
        val final_sql_clause = sqls""" ${snoozedCondition}
                 ${reply_at_condition}
                 $orderBy
                 limit ${pagination_data.page_size}
                """
        (final_sql_clause, sqls"")


      case pagination_data: ContactQueryType.TeamInboxFolder =>

        val hasProspectsCondition : SQLSyntax = pagination_data.folderType match {
          case MailboxFolder.TeamInboxFolder(pagination_data.esetId) =>
            sqls" AND et.has_prospect = true"
        }
        val (reply_at_condition: SQLSyntax, orderBy: SQLSyntax) = pagination_data.queryTimeLine match {
          case QueryTimeLine.Before(dateTime) =>
            (sqls" AND et.latest_reply_at < ${dateTime}", sqls" ORDER BY et.latest_reply_at desc")

          case QueryTimeLine.After(dateTime) =>
            (sqls" AND et.latest_reply_at > ${dateTime}", sqls" ORDER BY et.latest_reply_at asc")

          case _ => ""

        }
        val email_settings_condition = sqls" AND et.email_settings_id = ${pagination_data.esetId}"

        val final_sql_clause = sqls""" ${hasProspectsCondition}
                 ${email_settings_condition}
                 ${reply_at_condition}
                 $orderBy
                 limit ${pagination_data.page_size}
                """

        (final_sql_clause, sqls"")

      case pagination_data: ContactQueryTypeParent.ContactQueryTypeIncidental.TeamInboxFolderExactlyAt =>

        val hasProspectsCondition : SQLSyntax = pagination_data.folderType match {
          case MailboxFolder.TeamInboxFolder(pagination_data.esetId) =>
            sqls" AND et.has_prospect = true"
        }
        val (reply_at_condition: SQLSyntax, orderBy: SQLSyntax) = pagination_data.queryTimeLine match {
          case QueryTimeLine.At(dateTime) =>
            (sqls" AND et.latest_reply_at = ${dateTime}", sqls" ")
        }

        val email_settings_condition = sqls" AND et.email_settings_id = ${pagination_data.esetId}"

        val final_sql_clause = sqls""" ${hasProspectsCondition}
                 ${email_settings_condition}
                 ${reply_at_condition}
                 $orderBy
                 limit ${pagination_data.page_size}
                """

        (final_sql_clause, sqls"")


      case pagination_data: ContactQueryTypeIncidental.DoneFolderExactlyAt =>

        val doneCondition : SQLSyntax = pagination_data.folderType match {
          case MailboxFolder.DoneFolder =>
            sqls" AND et.archived = true"
        }
        val (reply_at_condition: SQLSyntax, orderBy: SQLSyntax) = pagination_data.queryTimeLine match {
          case QueryTimeLine.At(dateTime) =>
            (sqls" AND et.latest_reply_at = ${dateTime}", sqls" ")
        }
        val final_sql_clause = sqls""" ${doneCondition}
                 ${reply_at_condition}
                 $orderBy
                 limit ${pagination_data.page_size}
                """
        (final_sql_clause, sqls"")

      case pagination_data: ContactQueryTypeIncidental.SnoozedFolderExactlyAt =>

        val snoozedCondition : SQLSyntax = pagination_data.folderType match {
          case MailboxFolder.SnoozedFolder =>
            sqls" AND et.snoozed = true"
        }
        val (reply_at_condition: SQLSyntax, orderBy: SQLSyntax) = pagination_data.queryTimeLine match {
          case QueryTimeLine.At(dateTime) =>
            (sqls" AND et.latest_reply_at = ${dateTime}", sqls" ")
        }
        val final_sql_clause = sqls""" ${snoozedCondition}
                 ${reply_at_condition}
                 $orderBy
                 limit ${pagination_data.page_size}
                """
        (final_sql_clause, sqls"")
    }

    val query: SQL[Nothing, NoExtractor]  =
      sql""" select
    latest_emails_with_prospects.et_id as id,
    latest_emails_with_prospects.latest_reply_at,
    latest_emails_with_prospects.archived as done,
    latest_emails_with_prospects.snoozed,
    latest_emails_with_prospects.snoozed_till,
    latest_emails_with_prospects.sr_read,
    (case when es.text_body is not null and trim(es.text_body) != '' then left(es.text_body, 200) else es.body end) as body,
    es.subject AS latest_msg_subject,
    es.id as latest_msg_id,
    es.sent_at as latest_msg_sent_at,
    es.from_email as latest_msg_from_email,
    es.reply_to_email as latest_msg_reply_to_email,
    es.to_email AS latest_msg_to_emails,
    es.cc_emails AS latest_msg_cc_emails,
    es.bcc_emails AS latest_msg_bcc_emails,
    (case when es.scheduled_from_campaign or es.scheduled_manually then true else false end) as latest_msg_by_user,
    CONCAT(owner.first_name, ' ',  owner.last_name) AS owner_name,
    owner.id as owner_account_id
    from emails_scheduled es
    inner join (
      select
        et.id as et_id,
        et.latest_reply_at,
        sr_read,
        et.archived,
        et.snoozed,
        et.snoozed_till,
        et.latest_email_id,
        et.account_id
      from
      email_threads et
      ${prospects_inner_join_query}
      where
        et.account_id IN ($permittedOwnerIds)
        AND et.team_id = $teamId
        AND et.latest_email_id is not null
        ${remaining_clauses}
    ) latest_emails_with_prospects
    on
    es.id = latest_emails_with_prospects.latest_email_id
    join accounts owner on owner.id = latest_emails_with_prospects.account_id


    """
    query
  }
  */

  def getCampaignById(
                       allCampaignIds: Seq[Long],
                       team_id: Long
                     ): SQLSyntax = {
    sqls"""
       select id, name from campaigns where id IN ${SQLUtils.generateSQLValuesClause(allCampaignIds)}
       and team_id = $team_id;
       """
  }

  def getInsertNewThreadQuery(
                               threadGroup: Seq[NewEmailThreadV3],
                               validCampaignById: Map[Long, (Long, String)],
                               adminReplyFromSRInbox: Boolean,
                               SRLogger: SRLogger
                             ): SQL[Nothing, NoExtractor] = {

    var parameters = List[Any]()


    val valuePlaceholder = threadGroup.map(th => {

      val (campaignId, campaignName): (Option[Long], Option[String]) = if (adminReplyFromSRInbox) {
        (th.campaign_id.map(_.id), th.campaign_name)
      } else if (th.campaign_id.isDefined && validCampaignById.contains(th.campaign_id.get.id)) {
        (th.campaign_id.map(_.id), th.campaign_name)
      } else (None, None)

      val folder_type: String = th.folder_type.toString


      parameters = parameters ::: List(
        SRLogger.logTraceId,
        th.temp_thread_id,
        th.uuid.uuid,
        th.owner_id.id,
        th.team_id.id,
        campaignId,
        campaignName,
        th.inbox_email_settings_id.emailSettingId,

        th.inbox_email,
        th.subject,
        th.gmail_msg_id,
        th.gmail_thread_id,

        th.outlook_msg_id,
        th.outlook_conversation_id,

        th.internal_tracking_note.toString,
        folder_type,

        th.has_prospect,
        DateTime.now()
      )

      s"""
      (
        ?,
        ?,
        ?,
        ?,
        ?,
        ?,
        ?,
        ?,

        ?,
        ?,
        ?,
        ?,

        ?,
        ?,

        ?,
        ?,

        ?,
        ?

      )

    """
    }).mkString(", ")


    SQL(
      s"""
      INSERT INTO email_threads
      (
        req_log_trace_id,
        temp_thread_id,
        uuid,
        account_id,
        team_id,
        campaign_id,
        campaign_name,
        email_settings_id,

        admin_email,
        subject,
        gmail_msg_id,
        gmail_thread_id,

        outlook_msg_id,
        outlook_conversation_id,

        internal_tracking_note,
        folder_type,

        has_prospect,
        updated_at

      )
      VALUES $valuePlaceholder
      RETURNING id, temp_thread_id, folder_type;
    """)
      .bind(parameters*)

  }

}

class EmailThreadDAO {

  implicit val session: AutoSession.type = AutoSession

  def getTimelineQueryCondition(
                                 timeline: InferredQueryTimeline,
                                 mailboxFolder: MailboxFolder,
                                 team_id: Long
                               ): (SQLSyntax, SQLSyntax) = {

    mailboxFolder match {

      case _: MailboxFolder.TeamInboxFolder.SentFolder | _: MailboxFolder.CampaignInboxFolder.SentFolder =>
        val not_null_clause = sqls" AND et.latest_sent_by_admin_at is not null "

        timeline match {
          case InferredQueryTimeline.Range.Before(dateTime) =>
            (sqls" AND et.updated_at < ${dateTime} $not_null_clause", sqls" ORDER BY et.updated_at desc")

          case InferredQueryTimeline.Range.After(dateTime) =>
            (sqls" AND et.updated_at > ${dateTime} $not_null_clause", sqls" ORDER BY et.updated_at asc")

          case InferredQueryTimeline.Exact(exctlyAtThread) =>
            val updated_at_of_exctlyAtThread = sqls" (select updated_at from email_threads where id = ${exctlyAtThread.id.toLong} and team_id = $team_id) "
            (sqls" AND et.updated_at = ${updated_at_of_exctlyAtThread} $not_null_clause", sqls" ")
        }

      case _: MailboxFolder.TeamInboxFolder | _: MailboxFolder.CampaignInboxFolder =>

        val not_null_clause = sqls" AND et.latest_reply_at is not null "

        timeline match {
          case InferredQueryTimeline.Range.Before(dateTime) =>
            (sqls" AND et.updated_at < ${dateTime} $not_null_clause", sqls" ORDER BY et.updated_at desc")

          case InferredQueryTimeline.Range.After(dateTime) =>
            (sqls" AND et.updated_at > ${dateTime} $not_null_clause", sqls" ORDER BY et.updated_at asc")

          case InferredQueryTimeline.Exact(exctlyAtThread) =>
            val updated_at_of_exctlyAtThread = sqls" (select updated_at from email_threads where id = ${exctlyAtThread.id.toLong} and team_id = $team_id) "
            (sqls" AND et.updated_at = ${updated_at_of_exctlyAtThread} $not_null_clause", sqls" ")
        }

    }

  }

  def matchIdConditionSQLS(conv_id: Long): SQLSyntax = {
    sqls" AND et.id = ${conv_id} "
  }

//  def getQueryForSearchInboxEmailThreadsOld(
//                                             teamId: Long,
//                                             searchKey: String,
//                                             settingsIds: Seq[Long],
//                                             date_within: Option[DateTime],
//                                             folder_type: Option[FolderType],
//                                             inbox_type: InboxType
//                                           ): SQLSyntax = {
//
//    val date_within_condition = date_within match {
//      case None => sqls""
//      case Some(date) => sqls" AND sent_at > $date"
//    }
//
//    val main_where_clause = sqls" emails_scheduled.team_id = $teamId AND sender_email_settings_id in ${SQLUtils.generateSQLValuesClause(settingsIds)} AND email_thread_id is not null "
//
//    val team_inbox_join = inbox_type match {
//      case InboxType.SINGLE | InboxType.CONSOLIDATED =>
//        sqls" inner join team_inbox on team_inbox.email_setting_id = emails_scheduled.sender_email_settings_id "
//      case InboxType.AllCampaigns => sqls""
//    }
//
//    val (select_inside, select_outside) = inbox_type match {
//      case InboxType.SINGLE | InboxType.CONSOLIDATED => (
//        sqls", team_inbox.id as team_inbox_id", sqls", t.team_inbox_id"
//      )
//      case InboxType.AllCampaigns => (sqls"", sqls"")
//    }
//
//    /*
//      24-may-2024:
//        1. since we are not allowing non-prospects folder and irrelevant folder to be viewed from campaigns_inbox
//        2. we will also not allow to search in those folders.
//     */
//    val folder_type_where_clause: SQLSyntax = inbox_type match {
//
//      case InboxType.SINGLE | InboxType.CONSOLIDATED =>
//
//        if (folder_type.isDefined) {
//          sqls" AND et.folder_type = ${folder_type.get.toString} "
//        } else {
//          sqls""
//        }
//
//
//      case InboxType.AllCampaigns =>
//
//        sqls"""
//                    AND et.folder_type IN (${FolderType.PROSPECTS.textId}, ${FolderType.DONE.textId}, ${FolderType.SNOOZED.textId})
//                  """
//
//    }
//
//    val searchStr = s"%${searchKey.trim.toLowerCase}%"
//
//    sqls"""
//          select distinct on (t.email_thread_id)
//
//          t.uuid, t.subject, t.to_email, t.sender_email_settings_id, t.sent_at ${select_outside} from (
//
//          (select email_thread_id, et.uuid, emails_scheduled.subject, emails_scheduled.to_email, sender_email_settings_id, sent_at ${select_inside}
//          from emails_scheduled
//          inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
//          $team_inbox_join
//
//          where $main_where_clause $folder_type_where_clause
//          AND lower(to_email) like $searchStr $date_within_condition limit 5)
//
//          union all
//
//          (select email_thread_id, et.uuid, emails_scheduled.subject, emails_scheduled.to_email, sender_email_settings_id, sent_at ${select_inside}
//          from emails_scheduled
//          inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
//          $team_inbox_join
//
//          where $main_where_clause $folder_type_where_clause
//          AND lower(from_email) like $searchStr $date_within_condition limit 5)
//
//          union all
//
//          (select email_thread_id, et.uuid, emails_scheduled.subject, emails_scheduled.to_email, sender_email_settings_id, sent_at ${select_inside}
//          from emails_scheduled
//          inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
//          $team_inbox_join
//
//          where $main_where_clause $folder_type_where_clause
//          AND lower(emails_scheduled.subject) like $searchStr $date_within_condition limit 5)
//
//          union all
//
//          (select email_thread_id, et.uuid, emails_scheduled.subject, emails_scheduled.to_email, sender_email_settings_id, sent_at ${select_inside}
//          from emails_scheduled
//          inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
//          $team_inbox_join
//
//          where $main_where_clause $folder_type_where_clause
//          AND lower(cc_emails) like $searchStr $date_within_condition limit 5)
//
//         ) as t
//         order by t.email_thread_id desc;
//        """
//
//  }


  private def generateEmailThreadQuery(
                                        teamId: Long,
                                        whereClause: SQLSyntax,
                                        inbox_type: InboxType,
                                        limit: Int = 5,
                                        sent_at: Option[Long],
                                        last_sent_id: Option[String],
                                        prospect_account_id: Option[ProspectAccountsId] = None,
                                        isProspectQuery: Boolean = false
                                      ): SQLSyntax = {

    val select_inside = inbox_type match {
      case InboxType.SINGLE | InboxType.CONSOLIDATED =>
        sqls", team_inbox.id as team_inbox_id, team_inbox.name as team_inbox_name"
      case InboxType.AllCampaigns => sqls""
    }

    val team_inbox_join = inbox_type match {
      case InboxType.SINGLE | InboxType.CONSOLIDATED =>
        sqls" inner join team_inbox on team_inbox.email_setting_id = es.inbox_email_setting_id "
      case InboxType.AllCampaigns => sqls""
    }

    val prospect_join = if (isProspectQuery) {
      val prospectsJoin = if(prospect_account_id.isDefined) {
        sqls" inner join prospects p on p.id = etp.prospect_id and p.team_id = es.team_id "
      } else sqls""
      sqls" inner join email_threads_prospects etp on etp.email_thread_id = et.id $prospectsJoin"
    } else {
      sqls""
    }

    val sr_read_alias = if (isProspectQuery) {
      sqls"et.sr_read as is_read"
    } else {
      sqls"et.sr_read"
    }

    val check_sent_at = sent_at match {
      case None => sqls""
      case Some(sent_at) => sqls" AND es.sent_at <= to_timestamp(${sent_at / 1000}) "
    }

    val check_last_sent_id = last_sent_id match {
      case None => sqls""
      case Some(last_sent_id) => sqls" AND et.uuid != $last_sent_id "
    }

    val query =
      sqls"""
      select
        et.id as email_thread_id,
        et.uuid,
        ems.subject,
        ems.to_email,
        ems.from_email,
        ems.body,
        es.inbox_email_setting_id,
        CASE
          WHEN et.latest_reply_at IS NULL AND et.latest_sent_by_admin_at IS NOT NULL THEN 'sent'
          ELSE et.folder_type
        END AS folder_type,
        $sr_read_alias,
        sent_at
        $select_inside
      from emails_scheduled es
      inner join email_message_data ems on ems.team_id = es.team_id and es.id = ems.es_id
      inner join email_threads et on et.latest_email_id = es.id and et.team_id = es.team_id
      $prospect_join
      $team_inbox_join
      where es.team_id = $teamId $whereClause $check_sent_at $check_last_sent_id
      ORDER BY sent_at DESC limit $limit
    """

    if (!isProspectQuery) {
      sqls"($query)"
    } else {
      sqls"$query;"
    }
  }

  /**
   * Gets a query for searching inbox email threads
   */
  def getQueryForSearchInboxEmailThreadsNew(
                                             teamId: Long,
                                             searchKey: String,
                                             settingsIds: Seq[Long],
                                             date_within: Option[DateTime],
                                             folder_type: Option[FolderType],
                                             are_all_esets_accessible: Boolean,
                                             sent_at: Option[Long],
                                             last_sent_id: Option[String],
                                             inbox_type: InboxType
                                           ): SQLSyntax = {

    val searchStr = s"%${searchKey.trim.toLowerCase}%"

    val select_outside = inbox_type match {
      case InboxType.SINGLE | InboxType.CONSOLIDATED =>
        sqls", t.team_inbox_id, t.team_inbox_name"
      case InboxType.AllCampaigns => sqls""
    }

    val eset_clause = if (are_all_esets_accessible) {
      sqls""
    } else {
      sqls" AND es.inbox_email_setting_id in ${SQLUtils.generateSQLValuesClause(settingsIds)}"
    }

    val folder_type_where_clause: SQLSyntax = inbox_type match {
      case InboxType.SINGLE | InboxType.CONSOLIDATED =>
        if (folder_type.isDefined) {
          sqls" AND et.folder_type = ${folder_type.get.toString} "
        } else {
          sqls""
        }
      case InboxType.AllCampaigns =>
        sqls"""
          AND et.folder_type IN (${FolderType.PROSPECTS.textId}, ${FolderType.DONE.textId}, ${FolderType.SNOOZED.textId})
        """
    }

    val date_condition = date_within match {
      case None => sqls""
      case Some(date) => sqls" AND sent_at > $date"
    }

    val baseWhereClause = sqls"$eset_clause $folder_type_where_clause"

    val toEmailWhereClause = sqls"$baseWhereClause AND lower(ems.to_email) like $searchStr $date_condition"
    val fromEmailWhereClause = sqls"$baseWhereClause AND lower(ems.from_email) like $searchStr $date_condition"
    val subjectWhereClause = sqls"$baseWhereClause AND lower(ems.subject) like $searchStr $date_condition"
    val ccEmailsWhereClause = sqls"$baseWhereClause AND lower(ems.cc_emails) like $searchStr $date_condition"

    val toEmailQuery = generateEmailThreadQuery(
      teamId = teamId,
      whereClause = toEmailWhereClause,
      sent_at = sent_at,
      last_sent_id = last_sent_id,
      inbox_type = inbox_type
    )

    val fromEmailQuery = generateEmailThreadQuery(
      teamId = teamId,
      whereClause = fromEmailWhereClause,
      sent_at = sent_at,
      last_sent_id = last_sent_id,
      inbox_type = inbox_type
    )

    val subjectQuery = generateEmailThreadQuery(
      teamId = teamId,
      whereClause = subjectWhereClause,
      sent_at = sent_at,
      last_sent_id = last_sent_id,
      inbox_type = inbox_type
    )

    val ccEmailsQuery = generateEmailThreadQuery(
      teamId = teamId,
      whereClause = ccEmailsWhereClause,
      sent_at = sent_at,
      last_sent_id = last_sent_id,
      inbox_type = inbox_type
    )

    sqls"""
        select distinct on (t.email_thread_id)
        t.uuid,
        t.subject,
        t.to_email,
        t.from_email,
        t.body,
        t.inbox_email_setting_id,
        t.folder_type,
        t.sr_read as is_read,
        t.sent_at
        $select_outside
        from (
          $toEmailQuery
          union all
          $fromEmailQuery
          union all
          $subjectQuery
          union all
          $ccEmailsQuery
        ) as t
        order by t.email_thread_id desc limit 20;
      """

  }

  def getQueryForSearchInboxEmailThreadsForProspect(
                                                     teamId: Long,
                                                     settingsIds: Seq[Long],
                                                     prospectId: Option[ProspectId],
                                                     prospect_account_id: Option[ProspectAccountsId],
                                                     are_all_esets_accessible: Boolean,
                                                     sent_at: Option[Long],
                                                     last_sent_id: Option[String],
                                                     inbox_type: InboxType
                                                   ): SQLSyntax = {

    val eset_clause = if (are_all_esets_accessible) {
      sqls""
    } else {
      sqls" AND es.inbox_email_setting_id in ${SQLUtils.generateSQLValuesClause(settingsIds)}"
    }

    val prospectWhereClause =  if(prospectId.isDefined) {
      sqls"$eset_clause AND etp.prospect_id = ${prospectId.get.id}"
    } else {
      sqls"$eset_clause AND p.prospect_account_id = ${prospect_account_id.get.id}"
    }


    generateEmailThreadQuery(
      teamId = teamId,
      whereClause = prospectWhereClause,
      inbox_type = inbox_type,
      limit = 20,
      sent_at = sent_at,
      last_sent_id = last_sent_id,
      prospect_account_id = prospect_account_id,
      isProspectQuery = true
    )
  }

  private def getEmailThreadsProspectsExistsQuery(
                                                   teamId: Long,
                                                   is_campaign_inbox: Boolean,
                                                 ): SQLSyntax = {
    if (is_campaign_inbox) {
      //for campaign inbox we want to show only prospect emails
      //if user is having team_inbox then they can move non-prospect emails to done/snoozed or send the email non-prospects
      //so we are checking if there is any email_thread_prospect
      sqls"""
            AND EXISTS (
              select
                etp.email_thread_id
              from
                email_threads_prospects etp
                inner join prospects p on ( p.id = etp.prospect_id AND p.team_id = ${teamId} )
              where
                etp.email_thread_id = et.id
                  AND p.team_id = ${teamId}
              LIMIT 1 )
          """
    } else {
      sqls""
    }
  }

  private def getProspectFolderQuery(
                                    teamId: Long,
                                    esetIds: Seq[Long],
                                     prospect_category: Option[Long],
                                     replySentimentType: Option[ReplySentimentType],
                                    folder_type: FolderType,
                                    is_campaign_inbox: Boolean,
                                    campaign_id: Option[CampaignId]
                                    ): SQLSyntax = {

    val campaign_id_clause = if (campaign_id.isDefined) {

      sqls"""
           AND et.campaign_id = ${campaign_id.get.id}
          """

    } else {

      sqls"""
          """
    }
    val inbox_eset_id_clause = if(esetIds.isEmpty) {
      
      sqls""""""
    } else {
      sqls"""AND es.inbox_email_setting_id in ${SQLUtils.generateSQLValuesClause(esetIds)}"""
    }

    prospect_category match {
      case Some(prospectCategoryId) =>
        val exists_in_etp_condition =
          sqls"""
              AND EXISTS (
              select
                etp.email_thread_id
              from
                email_threads_prospects etp
                inner join prospects p on ( p.id = etp.prospect_id AND p.team_id = ${teamId} )
              where
                etp.email_thread_id = et.id
                  AND p.prospect_category_id_custom = ${prospectCategoryId}
                  AND p.team_id = ${teamId}
              )
            """
        sqls" AND et.folder_type = ${folder_type.textId} $inbox_eset_id_clause ${exists_in_etp_condition} ${campaign_id_clause}"
      case None =>
        val emailThreadProspectExists = getEmailThreadsProspectsExistsQuery(
          teamId = teamId,
          is_campaign_inbox = is_campaign_inbox
        )
        sqls" AND et.folder_type = ${folder_type.textId} $inbox_eset_id_clause ${emailThreadProspectExists} ${campaign_id_clause}"
    }

  }

  private def getSnoozedFolder(
                                esetIds: Seq[Long],
                                replySentimentType: Option[ReplySentimentType],
                                is_campaign_inbox: Boolean,
                                teamId: Long,
                                campaign_id: Option[CampaignId]
                              ): SQLSyntax = {

    val campaign_id_clause = if(campaign_id.isDefined){

      sqls"""
           AND et.campaign_id = ${campaign_id.get.id}
          """

    } else {

      sqls"""

          """
    }

    val emailThreadProspectExists = getEmailThreadsProspectsExistsQuery(
      teamId = teamId,
      is_campaign_inbox = is_campaign_inbox
    )

    sqls"AND et.folder_type = ${FolderType.SNOOZED.textId} AND es.inbox_email_setting_id in ${SQLUtils.generateSQLValuesClause(esetIds)} $emailThreadProspectExists ${campaign_id_clause}"

  }

  private def getIrrelevantFolder(
                                   esetIds: Seq[Long],
                                   campaign_id: Option[CampaignId],
                                   teamId: Long,
                                   is_campaign_inbox: Boolean
                                 ) = {

    val campaign_id_clause = if (campaign_id.isDefined) {

      sqls"""
           AND et.campaign_id = ${campaign_id.get.id}
          """

    } else {

      sqls"""

          """
    }

    val emailThreadProspectExists = if(is_campaign_inbox) {
      //for campaign inbox we want to show bounced emails only for prospects
      sqls"""
            AND EXISTS (
              select
                etp.email_thread_id
              from
                email_threads_prospects etp
                inner join prospects p on ( p.id = etp.prospect_id AND p.team_id = ${teamId} )
              where
                etp.email_thread_id = et.id
                  AND p.team_id = ${teamId}
              LIMIT 1 )
          """
    } else {
      sqls""
    }

    sqls"AND et.folder_type = ${FolderType.IRRELEVANT.textId} AND es.inbox_email_setting_id in ${SQLUtils.generateSQLValuesClause(esetIds)} ${campaign_id_clause} ${emailThreadProspectExists}"

  }

  private def getSentFolder(
                                esetIds: Seq[Long],
                                replySentimentType: Option[ReplySentimentType],
                                is_campaign_inbox: Boolean,
                                teamId: Long,
                                campaign_id: Option[CampaignId]
                              ): SQLSyntax = {

    val campaign_id_clause = if (campaign_id.isDefined) {

      sqls"""
           AND et.campaign_id = ${campaign_id.get.id}
          """

    } else sqls""

    val emailThreadProspectExists = getEmailThreadsProspectsExistsQuery(
      teamId = teamId,
      is_campaign_inbox = is_campaign_inbox
    )

    sqls" AND es.inbox_email_setting_id in ${SQLUtils.generateSQLValuesClause(esetIds)} ${emailThreadProspectExists} ${campaign_id_clause}"

  }

  def getMailboxQuery(mailboxFolder: MailboxFolder, teamId: Long): SQLSyntax = {


    mailboxFolder match {

      case p: MailboxFolder.TeamInboxFolder.Prospects =>

        getProspectFolderQuery(
          teamId = teamId,
          esetIds = p.esetIds,
          prospect_category = p.prospect_category,
          replySentimentType = p.replySentimentType,
          folder_type = FolderType.PROSPECTS,
          is_campaign_inbox = false,
          campaign_id = None
        )

      case p: MailboxFolder.CampaignInboxFolder.Prospects =>

        getProspectFolderQuery(
          teamId = teamId,
          esetIds = p.esetIds,
          prospect_category = p.prospect_category,
          replySentimentType = p.replySentimentType,
          folder_type = FolderType.PROSPECTS,
          is_campaign_inbox = false,
          campaign_id = p.campaign_id
        )

      case np: MailboxFolder.TeamInboxFolder.NonProspects =>
        sqls" AND et.folder_type = ${FolderType.NON_PROSPECTS.textId} AND es.inbox_email_setting_id in ${SQLUtils.generateSQLValuesClause(np.esetIds)} "

      case p: MailboxFolder.TeamInboxFolder.DoneFolder =>

        getProspectFolderQuery(
          teamId = teamId,
          esetIds = p.esetIds,
          prospect_category = p.prospect_category,
          replySentimentType = p.replySentimentType,
          folder_type = FolderType.DONE,
          is_campaign_inbox = false,
          campaign_id = None
        )


      case p: MailboxFolder.CampaignInboxFolder.DoneFolder =>

        getProspectFolderQuery(
          teamId = teamId,
          esetIds = p.esetIds,
          prospect_category = p.prospect_category,
          replySentimentType = p.replySentimentType,
          folder_type = FolderType.DONE,
          is_campaign_inbox = false,
          campaign_id = p.campaign_id
        )

      //Currently category_id filter is only for prospects and done folder. When customer asks for other folders we can update it
      case p: MailboxFolder.TeamInboxFolder.SnoozedFolder =>

        getSnoozedFolder(
          esetIds = p.esetIds,
          replySentimentType = p.replySentimentType,
          is_campaign_inbox = false,
          teamId = teamId,
          campaign_id = None
        )

      case p: MailboxFolder.CampaignInboxFolder.SnoozedFolder =>

        getSnoozedFolder(
          esetIds = p.esetIds,
          replySentimentType = p.replySentimentType,
          is_campaign_inbox = false,
          teamId = teamId,
          campaign_id = p.campaign_id
        )

      case p: MailboxFolder.TeamInboxFolder.IrrelevantFolder =>

        getIrrelevantFolder(
          esetIds = p.esetIds,
          campaign_id = None,
          teamId = teamId,
          is_campaign_inbox = false
        )

      case p: MailboxFolder.CampaignInboxFolder.IrrelevantFolder =>

        getIrrelevantFolder(
          esetIds = p.esetIds,
          campaign_id = p.campaign_id,
          teamId = teamId,
          is_campaign_inbox = true
        )
      case p: MailboxFolder.TeamInboxFolder.SentFolder =>

        getSentFolder(
          esetIds = p.esetIds,
          replySentimentType = p.replySentimentType,
          is_campaign_inbox = false,
          teamId = teamId,
          campaign_id = None
        )

      case p: MailboxFolder.CampaignInboxFolder.SentFolder =>

        getSentFolder(
          esetIds = p.esetIds,
          replySentimentType = p.replySentimentType,
          is_campaign_inbox = true,
          teamId = teamId,
          campaign_id = p.campaign_id
        )

    }

  }

  def getConversationQueryV3(teamId: Long,
                                      validatedConvReq: ValidatedConvReq,
                                      replySentimentUuid: List[ReplySentimentUuid],
                             remaining_clauses: SQLSyntax
                            ): SQLSyntax = {
    val whereForReplySentiment: SQLSyntax = if (replySentimentUuid.isEmpty) {
      sqls""""""
    } else {
      sqls"""
            AND et.reply_sentiment_uuid in ${SQLUtils.generateSQLValuesClause(replySentimentUuid.map(_.uuid))}
          """
    }

    /**
     * inbox_based_rows: select fields based on inbox ( TeamInboxFolder/CampaignInboxFolder )
     *    for team_inbox we are returning team_inbox_id and team_inbox_name
     *    for campaign_inbox we are returning nothing
     * inbox_based_join_clause: join clause based on inbox ( TeamInboxFolder/CampaignInboxFolder )
     *    for team_inbox we are adding team_inbox join clause to fetch team_inbox_name to show in FE
     *    for campaign_inbox we are adding email_thread_prospects join clause if prospect redirection to campaign-inbox from FE
     * inbox_based_where_clause: where clause based on inbox ( TeamInboxFolder/CampaignInboxFolder )
     *    for team_inbox we don't have any where clause
     *    for campaign_inbox we are adding prospectId clause if prospect redirection to campaign-inbox from FE
     */
    val (inbox_based_rows, inbox_based_join_clause, inbox_based_where_clause) = validatedConvReq.mailboxFolderRequest match {

      case _ : MailboxFolder.TeamInboxFolder =>

        (
          sqls"""
            ,
            t.id as team_inbox_id,
            t.name as team_inbox_name
              """,

          sqls"""
                inner join team_inbox t on t.email_setting_id = et.email_settings_id and et.team_id = t.team_id
              """,
          sqls""
        )

      case data : MailboxFolder.CampaignInboxFolder =>

        //prospectId will be defined if user is redirected from prospect popout to the campaign inbox
        val prospectIdOrAccountIdClause = if(data.prospectId.isDefined) {
          sqls" AND etp.prospect_id = ${data.prospectId.get.id} "
        } else if(data.prospectAccountsId.isDefined) {
          //prospectAccountsId will be defined if user is redirected from prospect account page to the campaign inbox
          sqls" AND p.prospect_account_id = ${data.prospectAccountsId.get.id} "
        } else sqls""

        //if prospectId is defined then we need to join email_threads_prospects
        val etpJoin = if(data.prospectId.isDefined) {
          sqls" inner join email_threads_prospects etp on et.id = etp.email_thread_id "
        } else if(data.prospectAccountsId.isDefined) {
          sqls"""
                inner join email_threads_prospects etp on et.id = etp.email_thread_id
                inner join prospects p on p.id = etp.prospect_id and p.team_id = es.team_id
              """
        } else sqls""


        (
          sqls"""
              """,
          etpJoin,
          prospectIdOrAccountIdClause
        )

    }

    val query =
      sqls"""
       select
        et.id as id,
        et.uuid as uuid,
        et.updated_at,
        et.latest_reply_at,
        et.latest_sent_by_admin_at,
        et.folder_type,
        et.snoozed_till,
        et.sr_read,
        et.reply_sentiment_uuid,
        (
          case when ems.text_body is not null
          and trim(ems.text_body) != '' then left(ems.text_body, 200) else ems.body end
        ) as body,
        ems.subject AS latest_msg_subject,
        es.id as latest_msg_id,
        es.uuid as latest_msg_uuid,
        es.sent_at as latest_msg_sent_at,
        ems.from_email as latest_msg_from_email,
        ems.reply_to_email as latest_msg_reply_to_email,
        ems.to_email AS latest_msg_to_emails,
        ems.cc_emails AS latest_msg_cc_emails,
        ems.bcc_emails AS latest_msg_bcc_emails,
        (
          case when es.scheduled_from_campaign
          or es.scheduled_manually then true else false end
        ) as latest_msg_by_user,
        CONCAT(owner.first_name, ' ', owner.last_name) AS owner_name,
        owner.id as owner_account_id
        ${inbox_based_rows}
      from
        emails_scheduled es
        inner join email_message_data ems on ems.team_id = es.team_id and es.id = ems.es_id
        inner join email_threads et on es.id = et.latest_email_id and et.team_id = es.team_id
        ${inbox_based_join_clause}
        join accounts owner on owner.id = et.account_id
        where
            et.team_id = $teamId
            $whereForReplySentiment
            $inbox_based_where_clause
            ${remaining_clauses}
      """
    query

  }

  def findByIdInternalV3(
    id: Long,
    teamId: TeamId,
    inboxEmailSettingId: Option[Long] = None
  ): Option[ThreadForProspectInternal.EmailThreadForProspectInternal] = DB.readOnly { implicit session =>

    val inboxEmailSettingIdWhereClause: SQLSyntax = if (inboxEmailSettingId.isDefined) {
      sqls"  AND et.email_settings_id = ${inboxEmailSettingId.get} "
    } else {
      sqls" "
    }

    sql"""
       SELECT

        et.campaign_id,
        et.campaign_name,
        et.gmail_thread_id,

        et.id,
        et.team_id,
        eset.account_id as inbox_owner_id,
        et.account_id

       FROM email_threads et
       
       INNER JOIN email_settings eset on eset.id = et.email_settings_id and eset.team_id = et.team_id
       

       WHERE et.id = $id
       AND et.team_id = ${teamId.id}
       $inboxEmailSettingIdWhereClause
       LIMIT 1
     """
      .map(rs => {
        ThreadForProspectInternal.EmailThreadForProspectInternal(
          campaign_id = rs.longOpt("campaign_id"),
          campaign_name = rs.stringOpt("campaign_name"),

          gmail_thread_id = rs.stringOpt("gmail_thread_id"),

          id = rs.long("id"),
          team_id = rs.long("team_id"),
          inbox_owner_id = AccountId(rs.long("inbox_owner_id")),
          owner_id = rs.long("account_id")

        )
      })
      .headOption
      .apply()

  }

  def countSelectedThreadsCanBeAccessed(
                                                  thread_ids: List[Long],
                                                  setting_id: Long,
                                                  teamId: TeamId
                                       ): Try[Int] = Try {
    
    if (thread_ids.isEmpty) {
      0
    }
    else {
      DB readOnly { implicit session =>

        sql"""
             SELECT count(*) as count
                        FROM email_threads
                        WHERE id IN ($thread_ids)
                        AND email_settings_id = ${setting_id}
                        AND team_id = ${teamId.id}
  
           """
          .map(rs => rs.int("count"))
          .single
          .apply()
          .get
      }
    }
  }


  // when an admin replies manually to a thread, no need to recheck campaignId and prospectId (adminReplyFromSRInbox): those 2 additional queries slow down the api further
  // fixed inboxv3: replace this with _insertNewThreadsV3 wherever this is used.
  //  remove "prospect_id", "prospect_email" columns from email_threads table
/*
  def _insertNewThreads(threads: Seq[NewEmailThread], adminReplyFromSRInbox: Boolean): Try[Seq[SavedEmailThread]] = Try {
    DB localTx { implicit session =>


      if (threads.isEmpty) Seq()
      else {



        // sometimes prospects have been deleted by the admin / user. If there are any replies from deleted prospects,
        // we should ignore those, otherwise the batch insert will fail with a foreign key violation error
        // when an admin replies manually from SR inbox, no need to check these

        // FIXME VALUECLASS
        val allProspectIds: Seq[Long] = threads.filter { e => e.prospect_id.isDefined }.map { e => e.prospect_id.get.id }.distinct
        // FIXME VALUECLASS
        val allCampaignIds: Seq[Long] = threads.filter { e => e.campaign_id.isDefined }.map { e => e.campaign_id.get.id }.distinct

        val validProspectIds: Seq[Long] = if (!adminReplyFromSRInbox && allProspectIds.nonEmpty) {

          sql"""
               select id from prospects where id IN ${SQLUtils.generateSQLValuesClause(allProspectIds)}
             """
            .map(rs => rs.long("id"))
            .list
            .apply()
        } else {

          Seq()

        }


        val validCampaignById: Map[Long, (Long, String)] = if (!adminReplyFromSRInbox && allCampaignIds.nonEmpty) {

          sql"""select id, name from campaigns where id IN ${SQLUtils.generateSQLValuesClause(allCampaignIds)}"""
            .map(rs => (rs.long("id"), rs.string("name")))
            .list
            .apply()
            .map(r => (r._1 -> r))
            .toMap

        } else Map[Long, (Long, String)]()





        var parameters = List[Any]()


        val valuePlaceholder = threads.map(email => {

          // FIXME VALUECLASS
          val prospectId: Option[Long] = if (adminReplyFromSRInbox) {
            email.prospect_id.map(_.id)
          } else if (email.prospect_id.isDefined && validProspectIds.contains(email.prospect_id.get.id)) {
            email.prospect_id.map(_.id)
          } else None

          // FIXME VALUECLASS
          val campaignId: Option[Long] = if (adminReplyFromSRInbox) {
            email.campaign_id.map(_.id)
          } else if (email.campaign_id.isDefined && validCampaignById.contains(email.campaign_id.get.id)) {
            email.campaign_id.map(_.id)
          } else None

          val campaignName = if (adminReplyFromSRInbox) None else campaignId.map(cid => validCampaignById(campaignId.get)._2)

          parameters = parameters ::: List(
            email.temp_thread_id,
            email.owner_id.id,
            email.team_id.id,
            campaignId,
            campaignName,
            prospectId,
            email.email_settings_id.emailSettingId,

            email.admin_email,
            email.prospect_email,
            email.subject,
            email.gmail_msg_id,
            email.gmail_thread_id,

            email.outlook_msg_id,
            email.outlook_conversation_id,

            email.internal_tracking_note.toString,
            // prospect_id is recomputed here, so
            // `has_prospect` should be re-computed
            // on the same grounds
            validProspectIds.nonEmpty
          )


          s"""
            (
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,

              ?,
              ?,
              ?,
              ?,
              ?,

              ?,
              ?,

              ?,
              ?

            )

          """
        }).mkString(", ")

        // fixed inboxv3 remove et.prospect_id, et.prospect_email
        SQL(s"""
          INSERT INTO email_threads
          (
            temp_thread_id,
            account_id,
            team_id,
            campaign_id,
            campaign_name,
            prospect_id,
            email_settings_id,

            admin_email,
            prospect_email,
            subject,
            gmail_msg_id,
            gmail_thread_id,

            outlook_msg_id,
            outlook_conversation_id,

            internal_tracking_note,
            has_prospect
          )
          VALUES $valuePlaceholder
          --ON CONFLICT DO NOTHING
          RETURNING id, temp_thread_id;
        """)
          .bind(parameters*)
          .map(rs => {

            SavedEmailThread(
              id = rs.long("id"),
              temp_thread_id = rs.longOpt("temp_thread_id")
            )

          })
          .list
          .apply()


      }
    }

  }
*/

  // newinbox
  def _associateEmailThreadsAndProspectsV3(
    logger: SRLogger,
    threadProspects: Seq[AssociateEmailThreadProspect]
  ): Try[Seq[AssociateEmailThreadProspect]] = Try {
    DB localTx { implicit session =>


      if (threadProspects.isEmpty) Seq()
      else {

        // now save to email_threads_prospects table

        var threadProspectParameters = List[Any]()

        val tpValuePlaceholder = threadProspects
          .map(tp => {

            threadProspectParameters = threadProspectParameters ::: List(
              logger.logTraceId,
              tp.emailThreadId,
              tp.prospectId,
              tp.temp_thread_id.getOrElse(null)
            )


            s"""
            (
              ?,
              ?,
              ?,
              ?
            )

          """
          }).mkString(", ")

        SQL(
          s"""
          INSERT INTO email_threads_prospects
          (
            req_log_trace_id,
            email_thread_id,
            prospect_id,
            temp_thread_id
          )
          VALUES $tpValuePlaceholder
          ON CONFLICT DO NOTHING
          RETURNING email_thread_id, prospect_id, temp_thread_id;
        """)
          .bind(threadProspectParameters*)
          .map(rs => {

            AssociateEmailThreadProspect(
              emailThreadId = rs.long("email_thread_id"),
              prospectId = rs.long("prospect_id"),
              temp_thread_id = rs.intOpt("temp_thread_id")
            )

          })
          .list
          .apply()

      }
    }

  }


  // nxd - add team_id
  def _updateLatestEmailId(data: Seq[EmailThreadUpdateLatestEmailData]): Seq[Long] = {
    DB localTx { implicit session =>

      // run separate queries for by_account and by prospects
      // update latest_reply_at is emails not by_account and
      // mark those newly added / updated email_threads as unread

      val byACCOUNT = data.filter(_.by_account)
      val byOTHERS = data.filterNot(_.by_account)

      val byAccountIds: Seq[Long] = if (byACCOUNT.isEmpty) {
        Seq[Long]()
      } else {

        /*
        val sentValuePlaceholderACCOUNT = byACCOUNT.map(e => {
          Seq(
            e.latest_email_id,
            e.email_thread_id
          )
        })

        sql"""
        UPDATE email_threads SET
          latest_email_id = ?
        WHERE id = ?
        RETURNING id;
      """
          .batchAndReturnGeneratedKey("id", sentValuePlaceholderACCOUNT*)
          .apply()
        */


        val sentValuePlaceholderACCOUNT: String = byACCOUNT.map(e => {

          s"""(
            ${e.latest_email_id},
           '${e.sent_at}'::timestamptz,

            ${e.email_thread_id}
          )"""

        }).mkString(", ")

        SQL(
          s"""
            UPDATE email_threads et
            SET
              latest_email_id = temp.latest_email_id,
              latest_sent_by_admin_at = (
                                          CASE WHEN
                                            (
                                            et.latest_sent_by_admin_at is not null AND et.latest_sent_by_admin_at > temp.sent_at
                                            )
                                            THEN et.latest_sent_by_admin_at ELSE temp.sent_at
                                          END
                                        ),
              updated_at = now(),
              sr_read = true
            FROM (
              VALUES $sentValuePlaceholderACCOUNT
            )
            AS temp(
              latest_email_id,
              sent_at,

              email_thread_id
            )
            WHERE et.id = temp.email_thread_id
            RETURNING et.id
            ;
          """)
          .map(rs => rs.long("id"))
          .list
          .apply()
      }


      val otherIds: Seq[Long] = if (byOTHERS.isEmpty) {

        Seq[Long]()

      } else {


        /*
        val sentValuePlaceholderOTHERS = byOTHERS.map(e => {
          Seq(
            e.latest_email_id,
            e.sent_at,
            e.email_thread_id
          )
        })

        sql"""
        UPDATE email_threads SET
          latest_email_id = ?,
          latest_reply_at = ?,
          sr_read = false,
          archived = false
        WHERE id = ?
        RETURNING id;
      """
          .batchAndReturnGeneratedKey("id", sentValuePlaceholderOTHERS*)
          .apply()

        */


        val sentValuePlaceholderOTHERS: String = byOTHERS.map(e => {
          s"""(
            ${e.latest_email_id},
            '${e.sent_at}'::timestamptz,
            ${e.email_thread_id},

            '${e.folderType.textId}',

            ${e.by_prospect},

            '${FolderType.PROSPECTS.textId}',
            '${FolderType.IRRELEVANT.textId}'
          )"""
        }).mkString(", ")

        /**
         * If previous folder is prospects and the new thread don't have prospect then
         * we should not change it to non-prospect but we can change it to irrelevant if bounced
         */

        SQL(
          s"""
            UPDATE email_threads et SET
              latest_email_id = temp.latest_email_id,
              latest_reply_at = temp.sent_at,
              sr_read = false,
              updated_at = now(),
              archived = false,
              folder_type = CASE WHEN
                                  et.folder_type = temp.prospects_folder
                                  AND temp.folder_type != temp.irrelevant_folder
                                  THEN et.folder_type
                                  ELSE temp.folder_type
                                END
            FROM (
              VALUES $sentValuePlaceholderOTHERS
            )
            AS temp(
              latest_email_id,
              sent_at,
              email_thread_id,

              folder_type,

              by_prospect,

              prospects_folder,
              irrelevant_folder
            )
            WHERE et.id = temp.email_thread_id
            RETURNING et.id;
          """)
          .map(rs => rs.long("id"))
          .list
          .apply()

      }

      byAccountIds ++ otherIds
    }


  }


  // nxd: add team_id
  // only mark specific thread as read
  def _markAsReadInSRInboxV2(emailThreadId: Long): Int = {

    DB.localTx { implicit session =>

      sql"""
          UPDATE email_threads
          SET
            sr_read = true
          WHERE id = $emailThreadId;
      """
        .update
        .apply()

    }
  }

  //only for archive/prospects/non-prospects/irrelevant... for snooze folder use changeFolderToSnooze
  //We have automatic unsnooze cron which changes the folder after unsnooze. In this case we want to
  //bring the thread in notice by showing it on top based on updated_at value as we are
  //sorting the threads with updated_at column.
  def updateInboxFolderType(
                             threadIds: Seq[Long],
                             teamId: TeamId,
                             folder_type: FolderType,
                             is_automatic_unsnooze: Boolean = false,
                             updated_at_unsnooze_cron: Option[DateTime] = None
                           )(implicit session: DBSession): Try[Int] = Try {

    val set_snoozed_till =
      sqls"""
         snoozed_till = (CASE WHEN ${is_automatic_unsnooze} THEN snoozed_till ELSE null END)
          """

    val set_updated_at = if(updated_at_unsnooze_cron.isDefined) {
      sqls" updated_at = ${updated_at_unsnooze_cron.get} "
    } else {
      sqls" updated_at = now() "
    }

    if(threadIds.isEmpty) {

      0
    } else {
      sql"""
          UPDATE email_threads
          SET
            folder_type = ${folder_type.textId},
            $set_snoozed_till,
            $set_updated_at,
            sr_read = false
          WHERE id IN ${SQLUtils.generateSQLValuesClause(threadIds)}
          AND team_id = ${teamId.id}
          ;
      """
        .update
        .apply()
    }

  }


  def changeFolderToSnooze(
                            threadIds: List[Long],
                            snoozed_till: Option[DateTime],
                            teamId: TeamId
                          ): Try[Int] = Try {

    DB.autoCommit { implicit session =>

      sql"""
          UPDATE email_threads
          SET
          folder_type = ${FolderType.SNOOZED.textId},
          snoozed_till = $snoozed_till,
          updated_at = now()
          WHERE id IN ${SQLUtils.generateSQLValuesClause(threadIds)}
            AND team_id = ${teamId.id}
          ;
      """
        .update
        .apply()

    }
  }
  // used internally for reply tracking
  // fixed inboxv3 remove et.prospect_id
  def findGmailAndOutlookThreads(
    gmailThreadIds: Seq[String],
    outlookConversationIds: Seq[String],
    teamId: TeamId,
    inboxEmailSettingId: Long
  ): Try[Seq[EmailThreadFoundForCheckingReplies]] = Try {
    DB readOnly { implicit session =>

      if (gmailThreadIds.isEmpty && outlookConversationIds.isEmpty) {
        Seq()
      } else {


        val whereClause: SQLSyntax = if (gmailThreadIds.nonEmpty && outlookConversationIds.nonEmpty) {
          sqls"""

          (
          et.gmail_thread_id IN ${SQLUtils.generateSQLValuesClause(gmailThreadIds)}

          OR

          et.outlook_conversation_id IN ${SQLUtils.generateSQLValuesClause(outlookConversationIds)}
          )
          """
        } else if (gmailThreadIds.nonEmpty) {

          sqls""" et.gmail_thread_id IN ${SQLUtils.generateSQLValuesClause(gmailThreadIds)} """

        } else if (outlookConversationIds.nonEmpty) {
          sqls""" et.outlook_conversation_id IN ${SQLUtils.generateSQLValuesClause(outlookConversationIds)} """

        } else {

          throw new Exception("FATAL findGmailAndOutlookThreads: both gmailThreadIds and outlookConversationIds are empty")
        }

        //left joins are to track non-prospects threads also

        sql"""
          select
            et.id as email_thread_id,
            et.gmail_thread_id,
            et.outlook_conversation_id,
            etp.prospect_id,
            p.prospect_account_id,
            et.campaign_id

          from email_threads et
          left join email_threads_prospects etp on etp.email_thread_id = et.id
          left join prospects p on p.id = etp.prospect_id

          where
          $whereClause
          and et.email_settings_id = $inboxEmailSettingId
          and et.team_id = ${teamId.id}
          ;
        """
          .map(rs => EmailThreadFoundForCheckingReplies(
            email_thread_id = rs.long("email_thread_id"),
            gmail_thread_id = rs.stringOpt("gmail_thread_id"),
            outlook_conversation_id = rs.stringOpt("outlook_conversation_id"),

            primary_prospect_id = rs.longOpt("prospect_id"),
            primary_prospect_account_id = rs.longOpt("prospect_account_id"),
            campaign_id = rs.longOpt("campaign_id"),

            // NOTE: email_threads table does not store these
            message_id = None,
            step_id = None
          ))
          .list
          .apply()
      }
    }
  }

  private def parseEmailThreadResFromDb(
    rs: WrappedResultSet,
    page: Int,
    teamId: TeamId,
    Logger: SRLogger
  ): Try[EmailThreadSearchResultIntermediate] = Try {
    implicit  val logger = Logger

    EmailThreadSearchResultIntermediate(
      page = page,
      campaign_id = 0.toLong,
      replies = {

        /*
        val fullName = Helpers.getProspectNameForSendingEmail(firstName = rs.stringOpt("first_name"), lastName = rs.stringOpt("last_name"))


        val prospectCategory = rs.string("prospect_category")
        val prospectCategoryLabelColor = rs.string("prospect_category_label_color")
        val prospectCategoryId = rs.long("prospect_category_id")

        val prospectTags: Array[String] = rs.stringOpt("prospect_tags")
          .map(t => t.split(","))
          .getOrElse(Array())

         */

        val folderTypeOpt: Option[String] = rs.stringOpt("folder_type")
        val folder_type: Option[FolderType] = folderTypeOpt.map(f => FolderType.withName(f).get)

        val snoozed_till = rs.jodaDateTimeOpt("snoozed_till")
        val body = rs.stringOpt("body")
        val bodyPreview = body.map(b => EmailHelper.getTextPreviewSlugFromHtml(html = b).take(150))

        val prospects: Seq[ConversationProspect] = Json.parse(rs.any("prospects").asInstanceOf[PGobject].getValue).validate[Seq[ConversationProspect]].get

        /* NOTE: 5th Apr 2021: phasing out the email_threads.prospect_id column which used to be "primary_prospect_id"
        val primaryProspectId = rs.longOpt("primary_prospect_id")
        val primaryProspect: ConversationProspect = if (primaryProspectId.isEmpty) {

          prospects.head

        } else {

          prospects
            .find(p => p.prospect_id == primaryProspectId.get)
            .getOrElse(prospects.head)

        }
        */

        val primaryProspect: ConversationProspect = prospects.head

        val toEmails = IEmailAddress.parse(emailsStr = rs.string("latest_msg_to_emails")).get

        val replyTo = rs.stringOpt("latest_msg_reply_to_email")
          .flatMap(em => IEmailAddress.parse(emailsStr = em).get.headOption)

        val ccEmails = rs.stringOpt("latest_msg_cc_emails")
          .map(em => IEmailAddress.parse(emailsStr = em).get)

        val everyoneInvolved: Seq[IEmailAddress] = {

          var all = toEmails

          if (replyTo.isDefined) {
            all = all ++ Seq(replyTo.get)
          }

          if (ccEmails.isDefined && ccEmails.get.nonEmpty) {
            all = all ++ ccEmails.get
          }

          all
            .map(em => em.copy(email = em.email.trim.toLowerCase()))
            .groupBy(_.email)
            .map(_._2.head)
            .toSeq
        }

        /**
          * 15 March 2021
          * fixme inboxv3: this is in no way perfect. we need to be able to filter out
          * admin emails. we will ultimately get this data from email_message_prospects table as noted
          * in roadmap doc.
          */
        val contacts: Seq[ConversationContact] = everyoneInvolved
          .map(em => {

            val p = prospects
              .find(p => p.prospect_email.trim.toLowerCase == em.email)

            val name = if (em.name.isDefined) em.name else p.map(_.prospect_email)

            ConversationContact(
              name = name,
              email = em.email,
              prospect_id = p.map(_.prospect_id)
            )

          })

        val latestMsgFromEmailRaw = rs.string("latest_msg_from_email")

        // 28th July 2021
        // REF: for one user, the inbox was breaking because the from_email was "mailer-daemon@missing_domain"
        // and the email address parser was throwing error:
        // "Domain contains illegal character in string ``mailer-daemon@missing_domain''"
        // We are ignoring such errors just passing through the raw email in such a case now
        val latestMsgFromEmail = IEmailAddress.parse(emailsStr = latestMsgFromEmailRaw) match {
          case Failure(e) =>

            Logger.fatal(s"[IGNORING] error while parsing latest_msg_from_email: ${latestMsgFromEmailRaw}", err = e)
            IEmailAddress(email = latestMsgFromEmailRaw)

          case Success(emails) =>

            if (emails.isEmpty) IEmailAddress(email = latestMsgFromEmailRaw)
            else emails.head

        }

        val campaign_id = rs.longOpt("campaign_id")
        val campaign_uuid = rs.stringOpt("campaign_uuid")
        val prospectCategory = primaryProspect.prospect_category
        val campaignProspectAllowReschedulingOption = rs.booleanOpt("allow_rescheduling_option").getOrElse(false)

        // (auto reply or oof) and campaignId not empty and campaign not running for prospect
        val showReschedulingOption = campaignProspectAllowReschedulingOption &&
          campaign_id.isDefined &&
          (
            prospectCategory.toLowerCase.contains("auto reply") ||
              prospectCategory.toLowerCase.contains("out of office")
            )

        val campaign = ConversationCampaign(
          campaign_id = campaign_id,
          campaign_uuid = campaign_uuid,
          campaign_name = rs.stringOpt("campaign_name"),
          campaign_tz = rs.stringOpt("campaign_tz"),
          show_rescheduling_option = showReschedulingOption,
          will_resume_at = rs.jodaDateTimeOpt("will_resume_at"),
          will_resume_at_tz = rs.stringOpt("will_resume_at_tz")
        )

        ConversationObject(
          id = rs.long("id"),
          team_id = teamId,
          // FIXME VALUECLASS
          owner_id = AccountId(id = rs.long("owner_account_id")),
          owner_name = rs.string("owner_name"),

          contacts = contacts,

          prospects = prospects,

          primary_prospect = Some(primaryProspect),

          campaign = campaign,

          folder_type = folder_type,
          snoozed_till = snoozed_till,

          latest_message = MessageObject.EmailMessageObject(
            uuid = rs.stringOpt("latest_msg_uuid"),

            subject = rs.string("latest_msg_subject"),
            body = body.getOrElse(""),
            body_preview = bodyPreview.getOrElse(""),
            sent_at = rs.jodaDateTime("latest_msg_sent_at"),

            from_user = rs.boolean("latest_msg_by_user"),

            from = latestMsgFromEmail,
            to = toEmails,

            reply_to = replyTo,

            cc_emails = ccEmails,

            bcc_emails = rs.stringOpt("latest_msg_bcc_emails")
              .map(em => IEmailAddress.parse(emailsStr = em).get)

          ),


          latest_reply_at = rs.jodaDateTimeOpt("latest_reply_at"), // used to paginate
          is_read = rs.boolean("sr_read")
        )

      })
  }

  private def parseConversationObjectFromDb(
                                             rs: WrappedResultSet,
                                             teamId: TeamId,
                                             Logger: SRLogger
                                           ): Try[ConversationObject] = Try {
    given logger: SRLogger = Logger // fixme given

    /*
    val fullName = Helpers.getProspectNameForSendingEmail(firstName = rs.stringOpt("first_name"), lastName = rs.stringOpt("last_name"))


    val prospectCategory = rs.string("prospect_category")
    val prospectCategoryLabelColor = rs.string("prospect_category_label_color")
    val prospectCategoryId = rs.long("prospect_category_id")

    val prospectTags: Array[String] = rs.stringOpt("prospect_tags")
      .map(t => t.split(","))
      .getOrElse(Array())

     */

    val folderTypeOpt: Option[String] = rs.stringOpt("folder_type")
    val folder_type: Option[FolderType] = folderTypeOpt.map(f => FolderType.withName(f).get)

    val snoozed_till = rs.jodaDateTimeOpt("snoozed_till")
    val body = rs.stringOpt("body")
    val bodyPreview = body.map(b => EmailHelper.getTextPreviewSlugFromHtml(html = b).take(150))

    val prospects: Seq[ConversationProspect] = Try {
      rs.anyOpt("prospects").map(p => Json.parse(p.asInstanceOf[PGobject].getValue).validate[Seq[ConversationProspect]].get).getOrElse(Seq())
    } match {
      case Success(value) => value
      case Failure(exception) =>
        Logger.fatal(s"failed p.asInstanceOf[PGobject].getValue).validate[Seq[ConversationProspect]].get", exception)
        Seq()
    }
    /* NOTE: 5th Apr 2021: phasing out the email_threads.prospect_id column which used to be "primary_prospect_id"
    val primaryProspectId = rs.longOpt("primary_prospect_id")
    val primaryProspect: ConversationProspect = if (primaryProspectId.isEmpty) {

      prospects.head

    } else {

      prospects
        .find(p => p.prospect_id == primaryProspectId.get)
        .getOrElse(prospects.head)

    }
    */

    val primaryProspect: Option[ConversationProspect] = prospects.headOption

    val toEmails = IEmailAddress.parse(emailsStr = rs.string("latest_msg_to_emails")).get

    val replyTo = rs.stringOpt("latest_msg_reply_to_email")
      .flatMap(em => IEmailAddress.parse(emailsStr = em).get.headOption)

    val ccEmails = rs.stringOpt("latest_msg_cc_emails")
      .map(em => IEmailAddress.parse(emailsStr = em).get)

    val everyoneInvolved: Seq[IEmailAddress] = {

      var all = toEmails

      if (replyTo.isDefined) {
        all = all ++ Seq(replyTo.get)
      }

      if (ccEmails.isDefined && ccEmails.get.nonEmpty) {
        all = all ++ ccEmails.get
      }

      all
        .map(em => em.copy(email = em.email.trim.toLowerCase()))
        .groupBy(_.email)
        .map(_._2.head)
        .toSeq
    }

    /**
     * 15 March 2021
     * fixme inboxv3: this is in no way perfect. we need to be able to filter out
     * admin emails. we will ultimately get this data from email_message_prospects table as noted
     * in roadmap doc.
     */
    val contacts: Seq[ConversationContact] = everyoneInvolved
      .map(em => {

        val p = prospects
          .find(p => p.prospect_email.trim.toLowerCase == em.email)

        val name = if (em.name.isDefined) em.name else p.map(_.prospect_email)

        ConversationContact(
          name = name,
          email = em.email,
          prospect_id = p.map(_.prospect_id)
        )

      })

    val latestMsgFromEmailRaw = rs.string("latest_msg_from_email")

    // 28th July 2021
    // REF: for one user, the inbox was breaking because the from_email was "mailer-daemon@missing_domain"
    // and the email address parser was throwing error:
    // "Domain contains illegal character in string ``mailer-daemon@missing_domain''"
    // We are ignoring such errors just passing through the raw email in such a case now
    val latestMsgFromEmail = IEmailAddress.parse(emailsStr = latestMsgFromEmailRaw) match {
      case Failure(e) =>

        Logger.fatal(s"[IGNORING] error while parsing latest_msg_from_email: ${latestMsgFromEmailRaw}", err = e)
        IEmailAddress(email = latestMsgFromEmailRaw)

      case Success(emails) =>

        if (emails.isEmpty) IEmailAddress(email = latestMsgFromEmailRaw)
        else emails.head

    }

    val campaign_id = rs.longOpt("campaign_id")
    val campaign_uuid = rs.stringOpt("campaign_uuid")
    val prospectCategory = primaryProspect.map(_.prospect_category)
    val campaignProspectAllowReschedulingOption = rs.booleanOpt("allow_rescheduling_option").getOrElse(false)

    // (auto reply or oof) and campaignId not empty and campaign not running for prospect
    val showReschedulingOption = campaignProspectAllowReschedulingOption &&
      campaign_id.isDefined &&
      (
        prospectCategory.isDefined && (
        prospectCategory.get.toLowerCase.contains("auto reply") ||
          prospectCategory.get.toLowerCase.contains("out of office"))
        )

    val campaign = ConversationCampaign(
      campaign_id = campaign_id,
      campaign_uuid = campaign_uuid,
      campaign_name = rs.stringOpt("campaign_name"),
      campaign_tz = rs.stringOpt("campaign_tz"),
      show_rescheduling_option = showReschedulingOption,
      will_resume_at = rs.jodaDateTimeOpt("will_resume_at"),
      will_resume_at_tz = rs.stringOpt("will_resume_at_tz")
    )

    ConversationObject(
      id = rs.long("id"),
      team_id = teamId,
      // FIXME VALUECLASS
      owner_id = AccountId(id = rs.long("owner_account_id")),
      owner_name = rs.string("owner_name"),

      contacts = contacts,

      prospects = prospects,

      primary_prospect = primaryProspect,

      campaign = campaign,

      folder_type = folder_type,
      snoozed_till = snoozed_till,

      latest_message = MessageObject.EmailMessageObject(
        uuid = rs.stringOpt("latest_msg_uuid"),

        subject = rs.string("latest_msg_subject"),
        body = body.getOrElse(""),
        body_preview = bodyPreview.getOrElse(""),
        sent_at = rs.jodaDateTime("latest_msg_sent_at"),

        from_user = rs.boolean("latest_msg_by_user"),

        from = latestMsgFromEmail,
        to = toEmails,

        reply_to = replyTo,

        cc_emails = ccEmails,

        bcc_emails = rs.stringOpt("latest_msg_bcc_emails")
          .map(em => IEmailAddress.parse(emailsStr = em).get)

      ),


      latest_reply_at = rs.jodaDateTimeOpt("latest_reply_at"), // used to paginate
      is_read = rs.boolean("sr_read")
    )

  }




  def toConversationSummaryView(rs: WrappedResultSet, teamId: Long, mailboxFolderRequest : MailboxFolder)(using Logger:SRLogger): Try[ConversationObjectInboxV3.EmailConversationObjectInboxV3] = Try {

    val snoozed_till = rs.jodaDateTimeOpt("snoozed_till")
    val body = rs.stringOpt("body")
    val bodyPreview = body.map(b => EmailHelper.getTextPreviewSlugFromHtml(html = b).take(150))

    val folderTypeOpt: Option[String] = rs.stringOpt("folder_type")

    val folder_type: Option[FolderType] = folderTypeOpt.map(f => FolderType.withName(f).get)

    val fromEmail = rs.string("latest_msg_from_email")

    val (team_inbox_id, team_inbox_name) = mailboxFolderRequest match {

      case _: MailboxFolder.TeamInboxFolder =>

        (Some(rs.long("team_inbox_id")), Some(rs.string("team_inbox_name")))

      case _: MailboxFolder.CampaignInboxFolder =>

        (None, None)

    }

    ConversationObjectInboxV3.EmailConversationObjectInboxV3(
      uuid = rs.string("uuid"),
      team_id = TeamId(teamId),
      owner_id = AccountId(rs.long("owner_account_id")),
      owner_name = rs.string("owner_name"),

      folder_type = folder_type,
      snoozed_till = snoozed_till,

      latest_message = MessageObject.EmailMessageObject(
        uuid = rs.stringOpt("latest_msg_uuid"),

        subject = rs.string("latest_msg_subject"),
        body = body.getOrElse(""),
        body_preview = bodyPreview.getOrElse(""),
        sent_at = rs.jodaDateTime("latest_msg_sent_at"),

        from_user = rs.boolean("latest_msg_by_user"),

        from = IEmailAddress.parseWithVeryLooseCheck(email = fromEmail),

        to = IEmailAddress.parse(
          emailsStr = rs.string("latest_msg_to_emails"),
          isStrict = false
        ).get,

        reply_to = rs.stringOpt("latest_msg_reply_to_email")
                .flatMap(em => IEmailAddress.parse(emailsStr = em, isStrict = false).get.headOption),

        cc_emails = rs.stringOpt("latest_msg_cc_emails")
                .map(em => IEmailAddress.parse(emailsStr = em, isStrict = false).get),

        bcc_emails = rs.stringOpt("latest_msg_bcc_emails")
                .map(em => IEmailAddress.parse(emailsStr = em, isStrict = false).get)

      ),


      latest_reply_at = rs.jodaDateTimeOpt("latest_reply_at"), // used to paginate
      latest_sent_by_admin_at = rs.jodaDateTimeOpt("latest_sent_by_admin_at"), // used to paginate

      is_read = rs.boolean("sr_read"),

      team_inbox_id = team_inbox_id,
      team_inbox_name = team_inbox_name,
      reply_sentiment_uuid = rs.stringOpt("reply_sentiment_uuid").map(uuid => ReplySentimentUuid(uuid = uuid)),

      updated_at = rs.jodaDateTime("updated_at")
    )

    }










  /**
    * fixme InboxV3: need to support these folders: by_prospects, by_others, bounced, spam, trash, sent
    * Usage not found
    * 16-may-2024 : this function call  `toConversationSummaryView` internally which requires extra parameter
    * inbox_type, so adding a default type here
    */
  def getConversationsInboxV3(
    teamId: Long,
    permittedOwnerIds: Seq[Long],
    inboxId: String,
    folderId: String,
    olderThan: Option[DateTime],
    mailBoxFolderfromReq: MailboxFolder = MailboxFolder.TeamInboxFolder.Prospects( // Fixme When using this function
      esetIds = Seq(),
      prospect_category = None,
      replySentimentType = None
    )
  )(implicit ec: ExecutionContext,Logger: SRLogger): Future[EmailSearchQueryResponseInboxV3] = Future {

    val limitPerPage = 10

    /**
      * adding "1" at end just to see if pagination-next needs to be shown, last one will be dropped before returning results below
      */
    val limit = limitPerPage + 1

    val paginationWhereClause: SQLSyntax = if (olderThan.isEmpty) sqls""
    else {
      sqls" and es.sent_at < ${olderThan.get}"
    }

    val showOnlyProspectReplies = true
    val showOnlyRepliesClause = if (showOnlyProspectReplies) {

      sqls" and et.latest_reply_at is not null "

    } else {
      sqls" "
    }

    val inboxIdClause: SQLSyntax = if (inboxId.startsWith("eset_")) {

      val inboxEmailSettingId = ParseUtils.parseLong(inboxId.substring(5))

      if (inboxEmailSettingId.isEmpty) {
        throw new Exception(s"Invalid inbox: $inboxId")
      } else {

        // fixme: inbox_email_setting_id should be stored and checked at the email_threads table level
        sqls" and es.inbox_email_setting_id = ${inboxEmailSettingId.get} "

      }

    } else  if (inboxId == "done") {

      sqls" and et.archived "

    } else  if (inboxId == "snoozed") {

      sqls" and et.snoozed "

    } else {

      sqls""

    }



    val conversations = DB readOnly { implicit session =>

      sql"""
      select
        et.id,
        et.latest_reply_at,
        et.archived as done,
        et.snoozed,
        et.snoozed_till,
        et.reply_sentiment_uuid,
        (case when es.text_body is not null and trim(es.text_body) != '' then left(es.text_body, 200) else es.body end) as body,
        es.subject AS latest_msg_subject,
        es.id as latest_msg_id,
        es.sent_at as latest_msg_sent_at,
        es.from_email as latest_msg_from_email,
        es.reply_to_email as latest_msg_reply_to_email,
        es.to_email AS latest_msg_to_emails,
        es.cc_emails AS latest_msg_cc_emails,
        es.bcc_emails AS latest_msg_bcc_emails,
        (case when es.scheduled_from_campaign or es.scheduled_manually then true else false end) as latest_msg_by_user,


        et.sr_read,


        CONCAT(owner.first_name, ' ',  owner.last_name) AS owner_name,
        owner.id as owner_account_id

        from email_threads et
        join emails_scheduled es on es.id = et.latest_email_id

        join accounts owner on owner.id = et.account_id

        where

         et.account_id IN ($permittedOwnerIds)

         $showOnlyRepliesClause

         $inboxIdClause

         AND et.team_id = $teamId

         $paginationWhereClause

        ORDER BY et.latest_reply_at DESC

        LIMIT $limit

    """

        .map(rs => toConversationSummaryView(rs, teamId = teamId, mailboxFolderRequest  = mailBoxFolderfromReq).get)
        .list
        .apply()

    }

    val hasMoreToLoad = conversations.length > limitPerPage


    val finalConversationsToReturn: List[ConversationObjectInboxV3.EmailConversationObjectInboxV3] = if (hasMoreToLoad) {

      // we fetch 1 more than limitPerPage (10 + 1) to figure if theres another page, dropping the last one
      conversations.dropRight(1)

    } else {

      conversations

    }


    EmailSearchQueryResponseInboxV3(
      has_more = hasMoreToLoad,
      conversations = finalConversationsToReturn
    )
  }

  def getConversationsForSearch(
                                 query: SQLSyntax,
                                 inbox_type: InboxType
                               )(using logger: SRLogger): Try[List[ConversationsSearchResponse]] = {


    DBUtils.readOnlyWithExplain(
      query = query,
      logger = logger,
      always_explain_if_exceeds_threshold = true
    ) { implicit session =>
      sql""" $query """
        .map(rs => {
          val team_inbox_id: Option[Long] = inbox_type match {
            case InboxType.SINGLE | InboxType.CONSOLIDATED =>
              rs.longOpt("team_inbox_id")
            case InboxType.AllCampaigns => None
          }

          val team_inbox_name: Option[String] = inbox_type match {
            case InboxType.SINGLE | InboxType.CONSOLIDATED =>
              rs.stringOpt("team_inbox_name")
            case InboxType.AllCampaigns => None
          }

          val fromEmail = rs.stringOpt("from_email")

          val body = rs.stringOpt("body")
          val bodyPreview = body.map(b => EmailHelper.getTextPreviewSlugFromHtml(html = b).take(150))
          ConversationsSearchResponse(
            conv_id = rs.string("uuid"),
            team_inbox_id = team_inbox_id,
            team_inbox_name = team_inbox_name,
            title = rs.string("subject"),
            from = fromEmail.map(email => IEmailAddress.parseWithVeryLooseCheck(email = email)),
            msg_preview = bodyPreview,
            folder_type = rs.stringOpt("folder_type"),
            sent_at = rs.jodaDateTimeOpt("sent_at"),
            is_read = rs.booleanOpt("is_read"),
            description = rs.string("to_email")
          )
        })
        .list
        .apply()
    }
  }

  def addReplySentimentToEmailThread(
                                      threadIds: List[Long],
                                      team_id: Long,
                                      reply_sentiment_uuid: ReplySentimentUuid, 
                                      reply_sentiment_updated_by: ReplySentimentUpdatedBy
                                    )(implicit session: DBSession): Try[List[Long]] = Try {

    if (threadIds.isEmpty) {
      List()
    } else {

      sql"""
       UPDATE email_threads et
         SET
           reply_sentiment_uuid = ${reply_sentiment_uuid.uuid},
           reply_sentiment_updated_by = ${reply_sentiment_updated_by.key}
         WHERE et.team_id = $team_id
          AND et.id IN ${SQLUtils.generateSQLValuesClause(threadIds)}
         RETURNING et.id;
     """
        .map(rs => rs.long("id"))
        .list
        .apply()
    }

  }

  def searchEmailThreadsRelatedToEmailId(
                                        prospect_emails: Seq[String],
                                        team_id: Long
                                        )(implicit session: DBSession): Try[List[SearchedEmailThreads]] = Try {

    if (prospect_emails.isEmpty) {
      List()

    } else {

      sql"""
         SELECT DISTINCT emc.email_message_id as scheduled_id, emc.email, es.email_thread_id

         FROM email_message_contacts emc
         INNER JOIN emails_scheduled es on emc.email_message_id = es.id and emc.team_id = es.team_id

         where lower(emc.email) in ${SQLUtils.generateSQLValuesClause(prospect_emails.map(_.toLowerCase.trim))}
         and emc.team_id = $team_id;
       """
        .map(rs =>
          SearchedEmailThreads(
            email = rs.string("email"),
            scheduled_id = rs.long("scheduled_id"),
            email_thread_id = rs.long("email_thread_id")
          )
        )
        .list
        .apply()
    }
  }

  //To shift the conversation in prospects folder
  def updateEmailThreadHasProspect(
                                    email_thread_ids: List[Long],
                                    team_id: Long
                                  )(implicit session: DBSession): Try[List[Long]] = Try {

    if (email_thread_ids.isEmpty) {
      List()
    } else {

      sql"""
         UPDATE email_threads et

         SET has_prospect = true
         WHERE et.id in ${SQLUtils.generateSQLValuesClause(email_thread_ids)} AND et.team_id = $team_id

         RETURNING et.id;
      """
        .map(rs => rs.long("id"))
        .list
        .apply()
    }
  }

  //check if thread_id exists in email_threads_table i.e. prospect/s present in thread
  def getThreadIdsExistingInEmailThreadsProspects(
                                                   thread_ids: Seq[Long],
                                                   teamId: TeamId
                                                 )(implicit session: DBSession): Try[List[Long]] = Try {
      sql"""
          SELECT email_thread_id
            FROM email_threads_prospects etp
            INNER JOIN email_threads et on et.id = etp.email_thread_id

            WHERE etp.email_thread_id in ($thread_ids)

            and et.team_id = ${teamId.id};
         """
        .map(_.long("email_thread_id"))
        .list
        .apply()
  }

  // update folder_type to non-prospect only for threads in prospects folder
  def updateFolderToNonProspect(
                                prospect_ids: Seq[ProspectId],
                                team_id: TeamId
                               )(implicit session: DBSession): Try[Int] = Try {
    if (prospect_ids.nonEmpty) {
      val prospectIds: Seq[Long] = prospect_ids.map(_.id)

        sql"""
            UPDATE email_threads as et
              SET folder_type = ${FolderType.NON_PROSPECTS.textId},
              updated_at = now()

              FROM email_threads_prospects etp
              WHERE etp.email_thread_id = et.id
              and etp.prospect_id in ${SQLUtils.generateSQLValuesClause(prospectIds)}
              and folder_type = ${FolderType.PROSPECTS.textId}
              and et.team_id = ${team_id.id}
          ;
      """
          .update
          .apply()

    } else {
      0
    }
  }

  //getting called in associate prospect to historical threads
  def updateFolderToProspects(
                               threadIds: Seq[Long],
                               teamId: TeamId
                             ): Try[Int] = Try {

    if (threadIds.isEmpty) {

      0
    } else {

      DB autoCommit { implicit session =>
        sql"""
            UPDATE email_threads
            SET
              folder_type = ${FolderType.PROSPECTS.textId},
              updated_at = now()
            WHERE id IN ${SQLUtils.generateSQLValuesClause(threadIds)}
            AND folder_type = ${FolderType.NON_PROSPECTS.textId}
            AND team_id = ${teamId.id}
            ;
        """
          .update
          .apply()
      }
    }

  }

  def getEmailThreadsCrossedSnoozedTill()
                                       (implicit ec: ExecutionContext): Future[List[ThreadAndTeamId]] = Future {

    DB readOnly { implicit session =>

      sql"""
        select id, team_id
        from email_threads

        where snoozed_till >= now() - interval '1 hours'
        and snoozed_till < now()
        ;
       """
        .map(rs => ThreadAndTeamId(
          thread_id = rs.int("id"), teamId = rs.long("team_id")
        ))
        .list
        .apply()
    }
  }

  def getConversationObjectForThreadId(thread_id: Long, teamId: TeamId)(using Logger: SRLogger): Try[ConversationObject] = {
    val campaignProspectColumnSQL =
      sqls"""
        campaigns.name AS campaign_name,
        campaigns.uuid AS campaign_uuid,
        campaigns.timezone AS campaign_tz,
        es.campaign_id AS campaign_id,

        (

          campaigns.status in (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString})

          AND campaigns_prospects.active

          --AND (
          --  campaigns_prospects.out_of_office = true
          --  OR
          --  campaigns_prospects.auto_reply = true
          --)

          AND campaigns_prospects.completed = true

          AND campaigns_prospects.bounced = false
          AND campaigns_prospects.invalid_email = false
          AND campaigns_prospects.opted_out = false

        ) AS allow_rescheduling_option,

        campaigns_prospects.will_resume_at as will_resume_at,
        campaigns_prospects.will_resume_at_tz as will_resume_at_tz,
      """
    DB readOnly { implicit session =>
      val query =
        sql"""
          select
            et.id,
            et.latest_reply_at,
            et.folder_type,
            et.snoozed_till,

            --p.email AS prospect_email,
            --p.id as prospect_id,

            (case when ems.text_body is not null and trim(ems.text_body) != '' then left(ems.text_body, 200) else ems.body end) as body,
            ems.subject AS latest_msg_subject,
            es.id as latest_msg_id,
            es.sent_at as latest_msg_sent_at,
            ems.from_email as latest_msg_from_email,
            ems.reply_to_email as latest_msg_reply_to_email,
            ems.to_email AS latest_msg_to_emails,
            ems.cc_emails AS latest_msg_cc_emails,
            ems.bcc_emails AS latest_msg_bcc_emails,
            es.uuid as latest_msg_uuid,
            (case when es.scheduled_from_campaign or es.scheduled_manually then true else false end) as latest_msg_by_user,

            $campaignProspectColumnSQL

            et.sr_read,


            CONCAT(owner.first_name, ' ',  owner.last_name) AS owner_name,
            owner.id as owner_account_id,


            (
              select

            json_agg(
              json_build_object(

                'prospect_id', p2.id,
                'prospect_uuid', p2.uuid,
                'prospect_email', pe.email,

                'prospect_name', TRIM(CONCAT(p2.first_name, ' ', p2.last_name)),
                'prospect_owner_id', p2.account_id,

                'prospect_category', pcat.name,
                'prospect_category_label_color', pcat.label_color,
                'prospect_category_id',  pcat.id,
                'prospect_tags', COALESCE(ptags.tags, '{}')

              )
            ) as prospects

              from email_threads et2
              left join email_threads_prospects etp ON etp.email_thread_id = et2.id
              join prospects p2 on (
                (
                  (p2.id = etp.prospect_id AND p2.team_id = ${teamId.id})
                  OR
                  (p2.id = et2.prospect_id AND p2.team_id = ${teamId.id})
                )

             )

             inner join prospects_emails pe ON p2.id = pe.prospect_id AND p2.team_id = pe.team_id AND pe.is_primary

              join prospect_categories_custom pcat on (pcat.id = p2.prospect_category_id_custom AND pcat.team_id = ${teamId.id})

              left join lateral (
                select ARRAY_AGG(tags.tag) as tags
                from tags join tags_prospects ON tags_prospects.prospect_id = p2.id
                where (
                  tags.id = tags_prospects.tag_id
                  AND NOT tags.hidden
                )
              ) as ptags on true

              where et2.id = et.id
              and et2.team_id = ${teamId.id}

             ) AS prospects

            from email_threads et
            join emails_scheduled es on (es.id = et.latest_email_id AND es.team_id = ${teamId.id})
            join email_message_data ems on ems.es_id = es.id and es.team_id = ems.team_id

            --left join prospects p on p.id = es.prospect_id

            join accounts owner on owner.id = et.account_id

            left join campaigns ON (
                campaigns.id = es.campaign_id
                AND campaigns.team_id = ${teamId.id}
              )
              left join campaigns_prospects on (
                campaigns_prospects.campaign_id = es.campaign_id
                AND campaigns_prospects.prospect_id = es.prospect_id
                AND campaigns_prospects.active
                AND campaigns_prospects.team_id = es.team_id
              )


            where

             et.team_id = ${teamId.id}
             and et.id = $thread_id

            ORDER BY et.team_id, et.latest_reply_at DESC NULLS LAST
              --, es.sent_at DESC

        """
      query
        .map(rs => parseConversationObjectFromDb(rs = rs, teamId = teamId, Logger))
        .single
        .apply()
        .get
    }
  }

  def getEmailThreadUuidWhenSendingManualEmail(
                                                campaign_id: CampaignId,
                                                prospect_ids: Seq[Long],
                                                team_id: TeamId,
                                                email_settings: EmailSetting
                                              ): Try[List[ManualEmailThreadData]] = Try{

    val email_settings_id: SQLSyntax = email_settings.id match {

      case None =>
        sqls""""""

      case Some(id) =>

        sqls"""
              AND et.email_settings_id = ${id.emailSettingId}
            """
    }

    if(prospect_ids.isEmpty){

      List()

    } else {

      DB.readOnly(implicit session => {

        sql"""

        SELECT
          et.uuid,
          et.subject
        FROM
          email_threads et
        INNER JOIN email_threads_prospects etp on (et.id = etp.email_thread_id AND etp.prospect_id IN (${prospect_ids}))
        WHERE
          et.campaign_id = ${campaign_id.id}
          AND et.team_id = ${team_id.id}
          ${email_settings_id}

        ORDER BY et.created_at DESC
        ;
           """
          .map(rs => ManualEmailThreadData(
            uuid = EmailThreadUuid(uuid = rs.string("uuid")),
            subject = rs.string("subject")
          ))
          .list
          .apply()

      })

    }

  }

  def updateMasterProspectInEmailThreadsProspects(
                                                   duplicateProspects: List[ProspectIdAndPotentialDuplicateProspectId],
                                                   masterProspectId: ProspectId,
                                                   teamId: TeamId
                                                 ): Try[List[Long]] = Try {
    var valueParameters = List[Any]()

    val valuePlaceholder: SQLSyntax = duplicateProspects.map(p => {

        valueParameters = valueParameters ::: List(
          p.prospectId.id,
          p.potentialDuplicateProspectId.id,
          masterProspectId.id
        )

        sqls"""
           (
              ?,
              ?,
              ?
            )
          """

      })
      .reduce((vp1, vp2) => sqls"$vp1, $vp2")

    DB autoCommit { implicit session =>
      sql"""
        UPDATE email_threads_prospects etp
            SET
              prospect_id = temp.master_prospect_id,
              potential_duplicate_prospect_id = temp.potential_duplicate_prospect_id

              FROM (
                  VALUES $valuePlaceholder
                )
                AS temp(
                  prospect_id,
                  potential_duplicate_prospect_id,
                  master_prospect_id
                )
          WHERE
            etp.prospect_id = temp.prospect_id
          RETURNING etp.email_thread_id
            ;
           """
        .bind(valueParameters*)
        .map(_.long("email_thread_id"))
        .list
        .apply()
    }
  }

}