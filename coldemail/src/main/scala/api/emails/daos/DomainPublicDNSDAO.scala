package api.emails.daos

import api.AppConfig
import api.accounts.email.models.SrMxCheckESPType
import io.smartreach.sr_dns_utils.{DNSService, DomainDataForFreeEmail, DomainPublicDNSAddOrUpdateData, DomainPublicDNSData}
import org.postgresql.util.PGobject
import play.api.libs.json.Json
import scalikejdbc.{DB, SQL, SQLSyntax, WrappedResultSet, scalikejdbcSQLInterpolationImplicitDef}
import scalikejdbc.jodatime.JodaWrappedResultSet.*

import scala.util.{Success, Try}

class DomainPublicDNSDAO {


  def insertOrUpdate(
              domainPublicDNSAddOrUpdateData: DomainPublicDNSAddOrUpdateData
            ): Try[Option[String]] = {

    insertOrUpdateBatch(
      domainsData = Seq(domainPublicDNSAddOrUpdateData)
    )
      .map(_.headOption)

    /*
    DB autoCommit { implicit session =>
      sql"""
           Insert into domain_public_dns
           (
            domain,
            dns_service,
            raw_response,
            mx_inbox_provider,
            is_valid
           )
           VALUES (
           ${domainPublicDNSAddOrUpdateData.domain.toLowerCase.trim},
           ${domainPublicDNSAddOrUpdateData.dns_service.toString},
            to_jsonb(${domainPublicDNSAddOrUpdateData.raw_response.toString()}::jsonb),
            ${domainPublicDNSAddOrUpdateData.mx_inbox_provider.toString},
            ${domainPublicDNSAddOrUpdateData.is_valid}
           )
            ON CONFLICT(lower(domain)) DO UPDATE SET
            dns_service = ${domainPublicDNSAddOrUpdateData.dns_service.toString},
            raw_response = to_jsonb(${domainPublicDNSAddOrUpdateData.raw_response.toString()}::jsonb),
            mx_inbox_provider = ${domainPublicDNSAddOrUpdateData.mx_inbox_provider.toString},
            last_updated_at = now()
           RETURNING domain;
         """
        .map(rs => rs.string("domain"))
        .single
        .apply()
    }
    */
  }

  def insertOrUpdateBatch(
    domainsData: Seq[DomainPublicDNSAddOrUpdateData]
  ): Try[List[String]] = Try {

    // store lower(email) or lower(email_domain)

    if (domainsData.isEmpty)
      List()
    else {

      val batchParams: Seq[Seq[Any]] = domainsData.map { data =>
        Seq(
          data.domain.toLowerCase.trim, // this is very important, we need to store lower(domain) only
          data.dns_service.toString,
          data.raw_response.toString, // Will be cast to jsonb in SQL
          data.mx_inbox_provider.toString,
          data.is_valid
        )
      }


      batchParams
        .grouped(500)
        .toList
        .flatMap(edgroup => {

          DB autoCommit { implicit session =>

            val valueParameters = edgroup.flatten

            val valuePlaceholder = edgroup.map(r => {
              s"""(
                 ?,
                 ?,
                 to_jsonb(?::jsonb),

                 ?,
                 ?
               )"""
            }).mkString(", ")

            val query =
              s"""
                 INSERT INTO domain_public_dns
                 (
                    domain,
                    dns_service,
                    raw_response,
                    mx_inbox_provider,
                    is_valid
                 )
                 VALUES $valuePlaceholder
                  ON CONFLICT(domain) DO UPDATE SET
                    dns_service = EXCLUDED.dns_service,
                    raw_response = EXCLUDED.raw_response,
                    mx_inbox_provider = EXCLUDED.mx_inbox_provider,
                    last_updated_at = now()

                 RETURNING domain;
               """

            SQL(query)
              .bind(valueParameters *)
              .map(_.string("domain"))
              .list
              .apply()
          }

        })


    }
  }

  def update(
              domainPublicDNSAddOrUpdateData: DomainPublicDNSAddOrUpdateData
            ): Try[Int] = Try {
    DB autoCommit { implicit session =>

      sql"""
           update domain_public_dns
           set
           dns_service = ${domainPublicDNSAddOrUpdateData.dns_service.toString},
           raw_response = to_jsonb(${domainPublicDNSAddOrUpdateData.raw_response.toString()}::jsonb),
           mx_inbox_provider = ${domainPublicDNSAddOrUpdateData.mx_inbox_provider.toString},
           last_updated_at = now(),
           is_valid = ${domainPublicDNSAddOrUpdateData.is_valid}
           where domain = ${domainPublicDNSAddOrUpdateData.domain.toLowerCase.trim};
         """
        .update
        .apply()

    }
  }

  def get(
         domain: String
         ): Try[Option[DomainPublicDNSData]] = Try{
    DB readOnly{ implicit session =>
      sql"""
           Select
              domain,
              dns_service,
              raw_response,
              mx_inbox_provider,
              last_updated_at,
              first_checked_at,
              is_valid
              from domain_public_dns
              where domain = ${domain.toLowerCase.trim}
              and last_updated_at > now() - interval '${SQLSyntax.createUnsafely(AppConfig.PublicDNS.number_of_months_after_which_we_update_DNS.toString)} months'
         """
        .map{DomainPublicDNSDAO.fromDb}
        .single
        .apply()

    }
  }


  def updateDisposableAndPublicEmailService(
                                           data: DomainDataForFreeEmail
                                           ): Try[Int] = Try {
    DB autoCommit { implicit session =>

      sql"""
           update domain_public_dns
           set
           last_updated_at = now(),
           is_public_email_service = ${data.is_public_email_service},
           is_disposable = ${data.is_disposable},
           is_valid = (CASE WHEN is_valid = false THEN false ELSE ${data.is_valid} END)
           where domain = ${data.domain.toLowerCase.trim};
         """
        .update
        .apply()

    }
  }
  def getForFreeDomain(
                        domain: String
                      ): Try[Option[DomainDataForFreeEmail]] = Try{
    DB readOnly{ implicit session =>
      sql"""
           Select
              domain,
              is_valid,
              is_public_email_service,
              is_disposable
              from domain_public_dns
              where domain = ${domain.toLowerCase.trim}
         """
        .map{DomainPublicDNSDAO.fromDbForDomainDataForFreeEmail}
        .single
        .apply()

    }
  }


  def getBatch(
                domains: Set[String]
              ): Try[List[DomainPublicDNSData]] = Try{
    if(domains.isEmpty) {
      List()
    } else {
      DB readOnly{ implicit session =>
        sql"""
           Select
              domain,
              dns_service,
              raw_response,
              mx_inbox_provider,
              last_updated_at,
              first_checked_at,
              is_valid
              from domain_public_dns
              where domain IN (${domains.map(_.toLowerCase.trim)})
              and last_updated_at > now() - interval '${SQLSyntax.createUnsafely(AppConfig.PublicDNS.number_of_months_after_which_we_update_DNS.toString)} months'
         """
          .map{DomainPublicDNSDAO.fromDb}
          .list
          .apply()

      }
    }
  }

}

object DomainPublicDNSDAO {
  def  fromDb(rs: WrappedResultSet): DomainPublicDNSData = {

    DomainPublicDNSData(
      domain = rs.string("domain"),
      dns_service = DNSService.fromKey(rs.string("dns_service")).get,
      raw_response = Json.parse(rs.any("raw_response").asInstanceOf[PGobject].getValue),
      mx_inbox_provider = SrMxCheckESPType.checkGoogleDNSApiRes(rs.string("mx_inbox_provider")),
      last_updated_at = rs.jodaDateTime("last_updated_at"),
      first_checked_at = rs.jodaDateTime("first_checked_at"),
      is_valid = rs.boolean("is_valid")
    )

  }
  def  fromDbForDomainDataForFreeEmail(rs: WrappedResultSet): DomainDataForFreeEmail = {

    DomainDataForFreeEmail(
      domain = rs.string("domain"),
      is_valid = rs.boolean("is_valid"),
      is_public_email_service = rs.booleanOpt("is_public_email_service"),
      is_disposable = rs.booleanOpt("is_disposable")
    )

  }
}
