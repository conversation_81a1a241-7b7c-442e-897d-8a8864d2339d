package api.emails

import api.AppConfig
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.{AccountId, OrgId, TeamSRAIFlags}
import api.accounts.{Account, ReplyHandling, TeamId}
import api.campaigns.models.CampaignProspectCompletedReason
import api.campaigns.models.CampaignProspectCompletedReason.SaveEmailsAndRepliesFromInboxV3
import api.campaigns.services.{CampaignId, CampaignProspectService2}
import api.campaigns.{CPMarkAsCompleted, CampaignProspectDAO}
import api.emails.dao_service.{EmailReplyTrackingDAOService, EmailScheduledDAOService, EmailThreadDAOService}
import api.emails.models.{DeletionReason, EmailMessageContact, EmailMessageContactModel, EmailMessageContacts, EmailReplyType}
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.{ProspectCategoryId, ProspectCategoryUpdateFlow}
import api.campaigns.CPCompleted
import api.prospects.ProspectBasicForBatchUpdate
import api.sr_ai.models.{MqSrAiApiMessage, MqSrAiApiMessageWithLogId}
import api.sr_ai.mq.MqSrAiApiPublisher
import api.team_inbox.model.ReplySentimentUpdatedBy.SmartreachAi
import api.team_inbox.dao.EmailTeamInboxDAO
import api.team_inbox.model.ReplySentimentSubCategory
import api.team_inbox.model.ReplySentimentTypeData.FollowUpNeededDisplayName
import io.sr.billing_common.models.PlanID
//import api.scylla.dao.BounceData
import api.team_inbox.model.{FolderType, ReplySentimentChannelType}
import api.team_inbox.service.ReplySentimentUuid
import io.smartreach.esp.utils.email.services.GmailServiceCommon
import io.smartreach.esp.utils.email.{EmailReplyBounceType, EmailReplyStatusCommon}
import utils.helpers.LogHelpers
import utils.uuid.SrUuidUtils
import sr_scheduler.models.ChannelType
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService
//import api.prospects.service.ProspectServiceV2
import api.prospects.models.{ProspectCategory, ProspectId}
import api.prospects.{ProspectBounceResponseInfo, ProspectIdEmail, ProspectUpdateCategoryTemp}
import api.team_inbox.model.ReplySentimentTypeData
import api.team_inbox.service.{ReplySentimentForTeam, ReplySentimentService, TeamInboxService, UpdateReplySentimentFormForDAO}
import utils.email.EmailReplyStatus
import utils.{Helpers, SRLogger}
import api.llm.dao.{LlmAuditLogDAO, LlmAuditLogInsertData}
import api.llm.models.LlmFlow
import api.columns.ProspectColGenStatus
import play.api.libs.json.Json

import scala.collection.immutable.Seq
import scala.util.{Failure, Success, Try}
import api.team.service.TeamService

sealed trait SetReplySentimentForNewMessagesError

object SetReplySentimentForNewMessagesError {
  case class GetReplySentimentsForTeamError(err: Throwable) extends SetReplySentimentForNewMessagesError
  case class DbErrorWhileUpdate(err: Throwable) extends SetReplySentimentForNewMessagesError

}

class EmailReplyTrackingModelV2 (
                                  emailReplyTrackingModel : EmailReplyTrackingModel,
                                  prospectDAOService: ProspectDAOService,
                                  //                                  campaignProspectDAO: CampaignProspectDAO,
                                  srUuidUtils: SrUuidUtils,
                                  campaignProspectService2: CampaignProspectService2,
                                  prospectUpdateCategoryTemp: ProspectUpdateCategoryTemp,
                                  //                                  prospectServiceV2: ProspectServiceV2,
                                  emailMessageContactModel: EmailMessageContactModel,
                                  emailReplyTrackingDAOService: EmailReplyTrackingDAOService,
                                  replySentimentService: ReplySentimentService,
                                  emailThreadDAOService: EmailThreadDAOService,
                                  srRollingUpdateCoreService: SrRollingUpdateCoreService,
                                  teamInboxDAO: EmailTeamInboxDAO,
                                  emailScheduledDAOService: EmailScheduledDAOService,
                                  emailCommonService: EmailCommonService,
                                  teamService: TeamService,
                                  llmAuditLogDAO: LlmAuditLogDAO
                                ){

  // MARK: - What it does: Helper method to create audit log entry for AI API calls
  // MARK: - Why it is like this: Creates audit trail for AI API calls; publishing is handled by SrAiApiCron
  private def createPendingAuditLogForAiApi(
    message: MqSrAiApiMessage,
    flow: LlmFlow,
    primaryProspectId: ProspectId
  )(implicit logger: SRLogger): Try[Long] = {
    
    val auditLogData = LlmAuditLogInsertData(
      team_id = message.team_id,
      prospect_id = primaryProspectId,
      llm_tool = flow.model,
      status = ProspectColGenStatus.Pending,
      flow = flow,
      column_def_id = None,
      consumed_token_count_prompt_input = None,
      consumed_token_count_generation_output = None,
      request_log_id = srUuidUtils.generateMqRequestLogId(),
      queue_message = Json.toJson(message)
    )
    
    for {
      insertedIds: List[Long] <- llmAuditLogDAO.insertLlmAuditLog(List(auditLogData))
      logId = insertedIds.head
    } yield {
      logger.info(s"Created audit log entry with ID: $logId for flow: ${flow.toString}. Will be processed by SrAiApiCron.")
      logId
    }
  }

  private def getCountsMoreThan1_AETP(emailMessages: Seq[AssociateEmailThreadProspect]): Map[Int, Int] = {
    /*
    val temp_id_map = scala.collection.mutable.HashMap[Int, Int]()
    emailMessages.foreach(em => {
      em.temp_thread_id match {

        case  Some(t) =>

          if (temp_id_map.contains(t)) {
            val rhs = temp_id_map(t)
            temp_id_map(t) = rhs + 1
          } else {
            temp_id_map(t) = 1
          }
        case None =>
          if (temp_id_map.contains(-1)) {
            val rhs = temp_id_map(-1)
            temp_id_map(-1) = rhs + 1
          } else {
            temp_id_map(-1) = 1
          }

      }
    })
    val temp_id_map2 = scala.collection.mutable.HashMap[Int, Int]()
    temp_id_map.foreach {
      case (k, v) =>
        if (v > 1)  {
          temp_id_map2(k) = v
        }
    }
    temp_id_map2

     */
    val res = emailMessages
      .map( em => {
        em.temp_thread_id match {
          case Some(v) => (v, em)
          case None => ( -1, em)
        }
      })
      .groupBy(_._1)
      .map( v1 => {
        (v1._1, v1._2.length)
      })
      .filter(v2 => {
        v2._2 > 1
      })

    res

  }

  def getCountsMoreThan1_NETv3(emailMessages: Seq[NewEmailThreadV3]): Map[Int, Int] = {
    /*
    val temp_id_map = scala.collection.mutable.HashMap[Int, Int]()
    emailMessages.foreach(em => {
          val t= em.temp_thread_id
          if (temp_id_map.contains(t)) {
            val rhs = temp_id_map(t)
            temp_id_map(t) = rhs + 1
          } else {
            temp_id_map(t) = 1
          }
    })
    val temp_id_map2 = scala.collection.mutable.HashMap[Int, Int]()
    temp_id_map.foreach {
      case (k, v) =>
        if (v > 1)  {
          temp_id_map2(k) = v
        }
    }
    temp_id_map2
     */
    val res = emailMessages
      .groupBy(_.temp_thread_id)
      .map(v => {
        (v._1, v._2.length)
      })
      .filter(v2 => {
        v2._2 > 1
      })
    res
  }

  private def getCountsMoreThan1(emailMessages: Seq[EmailMessageTracked]): Map[Int, Int] = {
    /*
    val temp_id_map = scala.collection.mutable.HashMap[Int, Int]()
    emailMessages.foreach(em => {
      em.tempThreadId match {

        case  Some(t) =>

          if (temp_id_map.contains(t)) {
            val rhs = temp_id_map(t)
            temp_id_map(t) = rhs + 1
          } else {
            temp_id_map(t) = 1
          }
        case None =>
          if (temp_id_map.contains(-1)) {
            val rhs = temp_id_map(-1)
            temp_id_map(-1) = rhs + 1
          } else {
            temp_id_map(-1) = 1
          }

      }
    })
    val temp_id_map2 =  scala.collection.mutable.HashMap[Int, Int]()
    temp_id_map.foreach {
      case (k, v) =>
        if (v > 1)  {
          temp_id_map2(k) = v
        }
    }
    temp_id_map2
     */
    val res = emailMessages
      .map(em => {
        em.tempThreadId match {
          case Some(thrid) => (thrid, em)
          case None => (-1, em)
        }
      })
      .groupBy(v => v._1)
      .map( v => {
        (v._1, v._2.length)
      })
      .filter(v2 => {
        v2._2 > 1
      })
      res
  }


  private def getDistinctNewEmailThreadsToBeCreated(
                                                     emailMessagesTrackedWithProsepctInvolved: Seq[(EmailMessageTracked, List[Long])],
                                                     inboxEmailSetting: EmailSetting
                                                   ): Seq[NewEmailThreadV3] = {

    emailMessagesTrackedWithProsepctInvolved
      .filter(_._1.email_thread_id.isEmpty)
      .map { case (m, prospectsInvolved) => { // newEmailThreadsToBeCreatedAll
        val prospect_ids = prospectsInvolved
        val folder_type: FolderType = EmailReplyTrackingModelV2.getFolderTypeOfEmailThread(
          email_reply_status = Some(m.email_status),
          has_prospect = prospect_ids.nonEmpty,
          email_subject = m.subject
        )

        val emailThreadUuid = srUuidUtils.generateEmailThreadsUuid()

        NewEmailThreadV3(
          temp_thread_id = m.tempThreadId.get,
          uuid = EmailThreadUuid(emailThreadUuid),
          // FIXME VALUECLASS
          campaign_id = m.campaign_id.map(c => CampaignId(id = c)),
          campaign_name = m.campaign_name,
          inbox_email = inboxEmailSetting.email,
          inbox_email_settings_id = inboxEmailSetting.id.get,
          subject = m.subject,
          latest_email_id = None,
          gmail_msg_id = m.gmail_msg_id,
          gmail_thread_id = m.gmail_thread_id,
          outlook_msg_id = m.outlook_msg_id,
          outlook_conversation_id = m.outlook_conversation_id,
          internal_tracking_note = m.internal_tracking_note,
          owner_id = inboxEmailSetting.owner_id,
          team_id = inboxEmailSetting.team_id,
          has_prospect = prospect_ids.nonEmpty,
          folder_type = folder_type
        )
      }
      }
      // get only distinct temp thread ids
      // REF: https://stackoverflow.com/a/3912833
      .groupBy(_.temp_thread_id)
      .map(_._2.head)
      .toSeq
  }

  private def markProspectsCompletedHelper(
                                            toMarkAsCompletedCampaignsProspects:Seq[EmailReplySavedV3],
                                            replyHandling: ReplyHandling.Value,
                                            markProspectAsCompleted:Boolean,
                                            isHardBouncedFlow: Boolean,
                                            team_id: TeamId,
                                            account: Account,
                                            completed_reason: CampaignProspectCompletedReason
                                    )(using Logger: SRLogger): Seq[CPCompleted] = {


    val updateEmailStatusOnReplyData: Seq[UpdateEmailStatusOnReplyV3] = toMarkAsCompletedCampaignsProspects
      .filter(em => {

        // do not update status when emails from admins
        !em.by_account &&
          em.all_prospects_involved.nonEmpty &&
          em.campaign_id.isDefined
      })
      .flatMap(em => {

        em.all_prospects_involved.map(p => {
          UpdateEmailStatusOnReplyV3(
            prospectId = p.prospect_id,

            emailStatus = em.email_status,
            emailScheduledId = em.email_scheduled_id,
            emailThreadId = em.email_thread_id,
            byAccount = em.by_account,
            sentAt = em.sent_at,
            campaignId = em.campaign_id.get,
            stepId = em.step_id
          )
        })
      })

    emailReplyTrackingDAOService.updateEmailStatusOnReplyAndCampaignProspectStatusOnReply(
      updateEmailStatusOnReplyData = updateEmailStatusOnReplyData)

    val completedCampaignsProspectsForWebhook = if (markProspectAsCompleted) {
      campaignProspectService2._markAsCompleted(
        cpData = toMarkAsCompletedCampaignsProspects
          .flatMap(em => {

            // check that primary_prospect_id is always there in all_prospects_involved - Added check while getting EmailReplySavedV3 above

            em.all_prospects_involved.map(pr => {
              CPMarkAsCompleted(
                email_reply_status = Some(em.email_status),
                completed_because_email_message_id = Some(em.email_scheduled_id),
                prospect_id = pr.prospect_id,
                campaign_id = em.campaign_id,
                prospect_account_id = pr.prospect_account_id
              )
            })


          }),
        replyHandling = replyHandling,
        isHardBounceFlow = isHardBouncedFlow,
        teamId = team_id.id,
        SRLogger = Logger,
        completed_reason = CampaignProspectCompletedReason.SaveEmailsAndRepliesFromInboxV3
      ) match {
        case Failure(e) =>
          Logger.fatal(s"step 0.10 CampaignProspect._markAsCompleted", err = e)

          List()

        case Success(data) =>
          Logger.success(s"step 0.10: CampaignProspect._markAsCompleted: ${data}")

          data

      }


    } else {
      Logger.info(s"Not Marking Prospect as completed as this step is part of campaign data: ${toMarkAsCompletedCampaignsProspects}")
      List()
    }

    completedCampaignsProspectsForWebhook


  }






  private def fetchValidProspects(
                                   emailMessages:Seq[EmailMessageTracked],
                                   team_id:Long
                                 )(using Logger:SRLogger):List[ERIntermediateValidProspect] =  {

    // from, to, cc, replyTo email addresses involved in the email message
    val allEmailAddressesInvolved: Seq[String] = emailMessages
      .flatMap(em => {

        val replyToEmail: Seq[String] = if (em.reply_to.isDefined) {
          Seq(em.reply_to.get.email)
        } else {
          Seq()
        }

        em.to_emails.map(_.email) ++
          Seq(em.from.email) ++
          em.cc_emails.map(_.email) ++
          replyToEmail
      })
      .map(_.toLowerCase.trim)
      .distinct

    val allPrimaryProspectIds: Seq[Long] = emailMessages
      .map(_.prospect_id_in_campaign)
      .filter(_.isDefined)
      .map(_.get)

    /**
     * check prospects table, if some prospects not found - then create those, and check again
     */
    val foundValidProspects: List[ERIntermediateValidProspect] = emailReplyTrackingModel._findIntermediateProspectsForReplyTracking(
      teamId = team_id,
      byProspectEmails = allEmailAddressesInvolved,
      byProspectIds = allPrimaryProspectIds
    )
    match {
      case Failure(e) =>
        Logger.error(s"FATAL error while fetching emailReplyTrackingModel._findIntermediateProspectsForReplyTracking in saveEmailsAndRepliesFromInboxV3")
        throw new Exception(s"FATAL error while fetching emailReplyTrackingModel._findIntermediateProspectsForReplyTracking: ${LogHelpers.getStackTraceAsString(e)}")
      case Success(emails) =>
        emails.map(em => em.copy(email = em.email.trim.toLowerCase))
    }

    foundValidProspects

  }

  // PROSPECTS_EMAILS_TODO_UPDATE
  /*
  * Use case : In inbox when user replies to email thread manually
  */

  // do not filter out prospects with invalid prospect_ids, just reset the prospect_id values to null
  // for emails that were received a week or more ago
  // this function name needs to be correct as it is saving emails from replies, Inbox and extension [ outbound ]

  /**
    *
    * Note:
    * 4-May-2024
    *
    * Used in 3 Flows
    * 1) sendNewManualEmailV2
    *   a)This is currently only used in extension for sending manual email
    *   so adminReplyFromSrInbox must be true as well as markProspectAsCompleted must be true
    *
    * 2) sendNewManualEmailV3
    *   a) adminReplyFromSRInbox will have a value based on sealed trait  so if it comes
    *   via Extension flow without campaign or
    *   via inbox
    *   , it must be set to true and markProspectAsCompleted
    *    will also be true for above 2 cases
    *   for ExtensionFlow with Campaign  adminReplyFromSRInbox will be false and markProspectAsCompleted will
    *   also be false as there can be steps in campaign which are still not executed.
    *
    *
    * 3) Reply tracking
    *   a) adminReplyFromSRInbox should be false ,but we want to markProspect as completed as we received a reply
    *   so markProspectAsCompleted  must be true
    *
    * @param adminReplyFromSRInbox
    * @param markProspectAsCompleted
    */
  
  private def sendForDeleteScheduledEmails(
                                    emails: Seq[EmailReplySavedV3],
                                    teamId: TeamId,
                                    deletion_reason: DeletionReason,
                                    replyHandling: ReplyHandling.Value,
                                    markProspectAsCompleted: Boolean
                                  )(using SRLogger: SRLogger): Try[Int] = Try {

    val savedRepliesBasicForDeleteUnsent = emails.map(s => EmailReplySavedV3Basic(
      campaign_id = s.campaign_id,
      prospect_id_in_campaign = s.prospect_id_in_campaign,
      prospect_account_id_in_campaign = s.prospect_account_id_in_campaign
    ))

    if (markProspectAsCompleted) {
      emailReplyTrackingModel._deleteUnsentByProspectIdsOnRepliesV3(
        emailsReceived = savedRepliesBasicForDeleteUnsent,
        replyHandling = replyHandling,
        teamId = teamId,
        deletion_reason = deletion_reason
      )
    } else 0
  }

  /**
   * Validates email messages and logs relevant information.
   */
  private def validateAndLogEmailMessages(
                                           emailMessages: Seq[EmailMessageTracked],
                                           team_id: Long,
                                           replyHandling: ReplyHandling.Value
                                         )(using SRLogger: SRLogger): SRLogger = {
    val tempIdMap = getCountsMoreThan1(emailMessages)
    /*
          * I have verified this will print :
          * scala> val v1 = scala.collection.mutable.HashMap[Int,Int]()
           val v1: scala.collection.mutable.HashMap[Int,Int] = HashMap()
           scala> v1(3)= 5
           scala> v1(4)=7
           scala> println(s"${v1}")
           HashMap(3 -> 5, 4 -> 7)
          */
    SRLogger.info(s"saveEmailsAndRepliesFromInboxV3: INBOXDEBUG temp thread Ids having more than 1 email mapped: $tempIdMap")

    emailMessages.foreach { msg =>
      if (msg.tempThreadId.isEmpty) {
        SRLogger.fatal(s"saveEmailsAndRepliesFromInboxV3 emailMessages assertion that all new messages have a tempthreadid has failed msgId = message_id ${msg.message_id} , references_header: ${msg.references_header} , in_reply_to_header : ${msg.in_reply_to_header}")
      }
    }

    SRLogger.appendLogRequestId(
      appendLogReqId = s"tid_$team_id :: READEMAIL es.saveEmailsAndRepliesFromInboxV3: emailMessages: ${emailMessages.length} :: replyHandling: $replyHandling"
    )
  }

  /**
   * Fetches step and campaign data if not replying from SR inbox.
   */


  //FIXME use value classes for  return type 
  private def fetchStepAndCampaignData(emailMessages: Seq[EmailMessageTracked], adminReplyFromSRInbox: Boolean): (Map[Long, (Long, String)], Map[Long, (Long, String)]) = {
    val allStepIds = emailMessages.filter { e => e.step_id.isDefined }.map { e => e.step_id.get }.distinct
    val allCampaignIds = emailMessages.filter { e => e.campaign_id.isDefined }.map { e => e.campaign_id.get }.distinct

    val validStepsById: Map[Long, (Long, String)] = if (!adminReplyFromSRInbox && allStepIds.nonEmpty) {

      emailReplyTrackingModel.getIdAndLabelForSelectedStepIds(allStepIds = allStepIds).getOrElse(Map[Long, (Long, String)]())

    } else Map[Long, (Long, String)]()


    val validCampaignById: Map[Long, (Long, String)] = if (!adminReplyFromSRInbox && allCampaignIds.nonEmpty) {

      emailReplyTrackingModel.getIdAndNameForSelectedCampaignIds(allCampaignIds =   allCampaignIds).getOrElse(Map[Long, (Long, String)]())

    } else Map[Long, (Long, String)]()

    (validStepsById, validCampaignById)
  }

  /**
   * Maps email messages to prospects and checks for auto-replies.
   */
  private def mapAndCleanEmailMessages(
                                        emailMessages: Seq[EmailMessageTracked],
                                        validProspects: List[ERIntermediateValidProspect],
                                        adminReplyFromSRInbox: Boolean,
                                        validStepsById: Map[Long, (Long, String)],
                                        validCampaignById: Map[Long, (Long, String)],
                                        senderEmails: Seq[String]
                                      )(using SRLogger: SRLogger): Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])] = {
    val cleanedUpEmailMessagesBeforeAutoReplyCheck: Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])]
    = EmailReplyTrackingModelV2.mapEmailMessagesToProspects(
      validProspects = validProspects,
      emailMessages = emailMessages,
      adminReplyFromSRInbox = adminReplyFromSRInbox,
      validStepsById = validStepsById,
      validCampaignById = validCampaignById,
      senderEmails = senderEmails
    )


    val cleanedUpEmailMessages: Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])] = cleanedUpEmailMessagesBeforeAutoReplyCheck.map { message =>
      (checkforAutoReply(message_to_check_for_auto_reply = message._1).get, message._2)
    }
    cleanedUpEmailMessages.foreach(msg_tracked => {
      val msg = msg_tracked._1
      if (msg.tempThreadId.isEmpty) {
        SRLogger.fatal(s"saveEmailsAndRepliesFromInboxV3 cleanedUpEmailMessages assertion that all new messages have a tempthreadid has failed msgId = message_id ${msg.message_id} , references_header: ${msg.references_header} , in_reply_to_header : ${msg.in_reply_to_header}")
      }
    })
    cleanedUpEmailMessages
  }


  /**
   * Retrieves team inbox ID for the given email setting.
   */
  private def getTeamInboxId(inboxEmailSetting: EmailSetting)(using Logger:SRLogger): Option[Long] = {
    teamInboxDAO.getTeamInboxIdForEmailSetting(
      eset_id = inboxEmailSetting.id.get.emailSettingId,
      org_id = inboxEmailSetting.org_id.id
    ) match {
      case Failure(exception) => Logger.shouldNeverHappen(s"error while fetching team_inbox_id for inbox_email_setting_id ${inboxEmailSetting.id.get.emailSettingId}", Some(exception))
        None
      case Success(tiid) => tiid
    }
  }


  /**
   * Filters email messages based on relevance criteria.
   */
  private def filterRelevantEmailMessages(
                                           cleanedUpEmailMessages: Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])],
                                           teamInboxId: Option[Long]
                                         ): Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])] = {


    cleanedUpEmailMessages.filter { message =>
      val msg = message._1
      val prospects_involved = message._2
      (msg.prospect_id_in_campaign.isDefined ||
        msg.prospect_account_id_in_campaign.isDefined ||
        msg.campaign_id.isDefined ||
        msg.step_id.isDefined ||
        prospects_involved.nonEmpty ||
        teamInboxId.nonEmpty)
    }
  }

  /**
   * Converts saved email thread messages to thread-prospect associations.
   */
  private def createThreadProspectAssociations(
                                                savedEmailThreadMsgs: Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])]
                                              ): Seq[AssociateEmailThreadProspect] = {
    savedEmailThreadMsgs
      .filter(_._1.email_thread_id.isDefined)
      .flatMap(th => th._2.map(p => {
        AssociateEmailThreadProspect(
          prospectId = p.prospect_id,
          emailThreadId = th._1.email_thread_id.get,
          temp_thread_id = th._1.tempThreadId
        )
      }))
  }

  /**
   * Creates and assigns email threads to messages.
   */
  private def createAndAssignEmailThreads(
                                           cleanedUpEmailMessagesFiltered: Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])],
                                           inboxEmailSetting: EmailSetting,
                                           adminReplyFromSRInbox: Boolean,
                                           team_id: Long
                                         )(using Logger: SRLogger): Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])] = {
    // find messages with empty email thread ids
    // create email threads and assign email_thread ids

    // fixme inboxv3 only create email threads for saved replies instead of cleanedUpEmailMessages,
    //   this is causing issues for gmail sent emails

    val distinctNewEmailThreadsToBeCreated: Seq[NewEmailThreadV3] = getDistinctNewEmailThreadsToBeCreated(
      emailMessagesTrackedWithProsepctInvolved = cleanedUpEmailMessagesFiltered.map(item => (item._1, item._2.map(_.prospect_id))),
      inboxEmailSetting = inboxEmailSetting
    )
    // distinctNewEmailThreadsToBeCreated - it is not possible that tempThreadId is empty here as it's a non-option field
    val temp_id_map2 = getCountsMoreThan1_NETv3(emailMessages = distinctNewEmailThreadsToBeCreated)
    Logger.info(s"saveEmailsAndRepliesFromInboxV3 : distinctNewEmailThreadsToBeCreated INBOXDEBUG temp thread Ids having more than 1 email mapped: ${temp_id_map2}")


    val savedEmailThreadMsgs: Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])] = emailThreadDAOService.insertNewThreads(
      threads = distinctNewEmailThreadsToBeCreated,
      adminReplyFromSRInbox = adminReplyFromSRInbox,
      SRLogger = Logger,
      team_id = team_id
    ) match {

      case Failure(e) =>
        throw new Exception(s"FATAL error while saving email threads: ${LogHelpers.getStackTraceAsString(e)}")

      case Success(newThreads) =>

        // assign the newly saved email_thread_ids to the new messages
        EmailReplyTrackingModelV2.modifyEmailThreadData(
          data = cleanedUpEmailMessagesFiltered,
          newThreads = newThreads,
          inboxEmailSetting = inboxEmailSetting
        )

    }

    val irrelevantEmailThreads: Seq[Long] = cleanedUpEmailMessagesFiltered
      .filter(m => m._1.email_thread_id.isDefined && EmailReplyStatus.isHardOrSoftBounced(m._1.email_status))
      .map(_._1.email_thread_id.get)

    emailThreadDAOService.updateInboxFolderTypeTry(
      threadIds = irrelevantEmailThreads,
      teamId = TeamId(team_id),
      folder_type = FolderType.IRRELEVANT
    ) match {
      case Failure(e) =>
        Logger.error(s"FATAL error while updateInboxFolderType to irrelevant: ${LogHelpers.getStackTraceAsString(e)}")

      case Success(_) => //do nothing
    }

    val threadProspects = createThreadProspectAssociations(savedEmailThreadMsgs)
    val temp_id_map3 = getCountsMoreThan1_AETP(emailMessages = threadProspects)
    //val temp_id_map3 = emailMessages.groupBy(_.tempThreadId).fi
    Logger.info(s"saveEmailsAndRepliesFromInboxV3 : distinctNewEmailThreadsToBeCreated INBOXDEBUG temp thread Ids having more than 1 email mapped: ${temp_id_map3}")
    emailCommonService.associateProspectsWithEmailThreads(
      threadProspects = threadProspects,
      teamId = team_id
    ) match {

      case Failure(e) =>
        throw new Exception(s"FATAL error while saving _associateEmailThreadsAndProspectsV3: ${LogHelpers.getStackTraceAsString(e)}")

      case Success(newThreads) =>
      // do nothing
    }

    savedEmailThreadMsgs.foreach(msg_and_prospect => {
      val msg = msg_and_prospect._1
      if (msg.tempThreadId.isEmpty) {
        Logger.fatal(s"saveEmailsAndRepliesFromInboxV3 savedEmailThreadMsgs assertion that all new messages have a tempthreadid has failed msgId = message_id ${msg.message_id} , references_header: ${msg.references_header} , in_reply_to_header : ${msg.in_reply_to_header}")
      }
    })


    savedEmailThreadMsgs
  }


  def saveEmailsAndRepliesFromInboxV3(
                                       accountId: Long,
                                       team_id: Long,
                                       emailMessages: Seq[EmailMessageTracked],
                                       inboxEmailSetting: EmailSetting,

                                       replyHandling: ReplyHandling.Value,
                                       account: Account,

                                       senderEmails: Seq[String],

                                       /**
                                        * this is true when user is replying from inside SmartReach inbox
                                        *
                                        * In this case, we will auto find the prospect, because email.prospect_id is always
                                        * coming as None from sendNewManualEmailV2
                                        *
                                        * fixme InboxV3: we are not creating the prospect in this case, if its a new to-email address that is not there
                                        * in the prospects list as of now
                                        */
                                       adminReplyFromSRInbox: Boolean,
                                       // prospectWhileAdminReply: Option[ProspectObject], // while admin manually replying, we already have the prospect object

                                       auditRequestLogId: String,
                                       markProspectAsCompleted: Boolean


                                     )(using SRLogger: SRLogger): Try[DBEmailMessagesSavedResponse] = {
    Try {


      val nothingSavedResponse = DBEmailMessagesSavedResponse(
        savedMessages = Seq(),
        emailMessagesFromProspects = Seq(),
        prospectIdsWithHardBouncedEmail = Seq(),
        prospectIdsWithSoftBouncedEmail = Seq(),
        emailScheduledIdsWithHardBouncedEmail = Seq(),
        emailScheduledIdsWithSoftBouncedEmail = Seq(),
        emailScheduledIdsWithAutoReplyEmail = Seq(),
        emailScheduledIdsWithOutOfOfficeReplyEmail = Seq(),
        completedWebhookData = Seq(),
        gmailSendingLimitErrorEmailSettingId = None,
        hardBouncedReplies = Seq(),
        softBouncedReplies = Seq()

      )

      if (emailMessages.isEmpty) {

        nothingSavedResponse

      } else {
        val Logger = validateAndLogEmailMessages(emailMessages = emailMessages, team_id =  team_id, replyHandling = replyHandling)


        /**
         * sometimes prospects have been deleted by the admin / user. If there are any replies from deleted prospects,
         * we should reset those prospect_ids to null,
         * otherwise the batch insert will fail with a foreign key violation error
         *
         *
         * Also, keep in mind, when an admin replies manually to a thread, the prospect_id always comes as null here,
         * so we need to find the prospects by the to_emails and save the prospect_ids
         */
        val validProspects: List[ERIntermediateValidProspect] = fetchValidProspects(
          emailMessages = emailMessages, team_id = team_id
        )

        val (validStepsById, validCampaignById) = fetchStepAndCampaignData(emailMessages = emailMessages,adminReplyFromSRInbox = adminReplyFromSRInbox)



        val cleanedUpEmailMessages = mapAndCleanEmailMessages(
          emailMessages = emailMessages,
          validProspects = validProspects,
          adminReplyFromSRInbox = adminReplyFromSRInbox,
          validStepsById = validStepsById,
          validCampaignById = validCampaignById,
          senderEmails = senderEmails
        )

        //check whether team_inbox exist for inbox_email_setting_id
        val team_inbox_id: Option[Long] = getTeamInboxId(inboxEmailSetting = inboxEmailSetting)

        /**
         * 11-Apr-2025: filter cleanedUpEmailMessages where emailMessageTracked contains either ((prospect_id or prospect_account_id)
         * and (contains campaign_id or step_id)) or inbox email setting has team inbox entry in team
         */

        val cleanedUpEmailMessagesFiltered: Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])] =
          filterRelevantEmailMessages(cleanedUpEmailMessages = cleanedUpEmailMessages, teamInboxId = team_inbox_id)


        val newMsgsWithEmailThreadId: Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])] =
          createAndAssignEmailThreads(
            cleanedUpEmailMessagesFiltered = cleanedUpEmailMessagesFiltered,
            inboxEmailSetting = inboxEmailSetting,
            adminReplyFromSRInbox = adminReplyFromSRInbox,
            team_id = team_id
          )


        // note that savedEmailThreadMsgs is assigned to newMsgsWithEmailThreadId


        val savedMessages: Seq[EmailReplySavedV3] = insertTrackedRepliesV3(
          newMsgsWithEmailThreadId = newMsgsWithEmailThreadId,
          sentManuallyFromSRInbox = adminReplyFromSRInbox,
          inboxEmailSetting = inboxEmailSetting,
          allow_using_sr_ai_api = account.org.org_metadata.allow_using_sr_ai_api.getOrElse(false),
          plan_id = account.org.plan.plan_id,
          team_id = TeamId(team_id)
        ).get

        if (savedMessages.isEmpty) {
          nothingSavedResponse
        }
        else {


          Logger.info(s"savedReplyIds ($accountId): ${savedMessages.map(_.email_scheduled_id)}")

          Logger.info(s"step 0")


          // Save email message contacts

          
          emailCommonService.saveEmailContacts(
            dataForEmailMessageContacts = savedMessages.map(msg => {
              EmailCommonService.DataForEmailMessageContact(
                email_scheduled_id = msg.email_scheduled_id,
                from_email= msg.from_email,
                to_emails = msg.to_emails,
                cc_emails = msg.cc_emails,
                bcc_emails = Seq(),
                reply_to = msg.reply_to
              )
            }), 
            validProspects = validProspects.map(pr => ProspectIdEmail(
              id = pr.prospect_id,
              email = pr.email,
              owner_id = pr.account_id
            )),
            inboxEmailSettingEmail = inboxEmailSetting.email,
            team_id = team_id,
          )



          Logger.info(s"step 0.1")

          /////////////////////////////////////////////
          // --- HANDLE EMAILS FROM ADMIN: START --- //
          /////////////////////////////////////////////


          val emailMessagesFromAdminsAndSentManually = savedMessages.filter(e => e.by_account)

          Logger.info(s"step 0.2: emailMessagesFromAdminsAndSentManually: ${emailMessagesFromAdminsAndSentManually.length}")


          if (emailMessagesFromAdminsAndSentManually.nonEmpty) {



            // update last_contacted_at for to_email prospects (prospects who are being contacted)
            val savedRepliesWithProspectIdOnly = emailMessagesFromAdminsAndSentManually
              .flatMap(e => {

                // todo newinbox: check cced_prospects also and update their last_contacted_at too

                e.to_prospects.map(p => (p.prospect_id, e.sent_at))

              })

            prospectDAOService._updateProspectLastContactedAt(
              savedRepliesWithProspectIdOnly = savedRepliesWithProspectIdOnly,
              team_id = TeamId(id = team_id),
              channel_type = ChannelType.EmailChannel // as this is related to email steps only.
            ) match {
              case Failure(e) =>
                Logger.fatal(s"step 0.2.1 Prospect._updateProspectLastContactedAt", err = e)

              case Success(data) =>
                Logger.info(s"step 0.2.1 SUCCESS: Prospect._updateProspectLastContactedAt: $data")

            }

          }


          /////////////////////////////////////////////////
          // --- HANDLE EMAILS FROM PROSPECTS: START --- //
          /////////////////////////////////////////////////


          val emailMessagesFromProspects = savedMessages.filter(e => {
            (

              // fixme inboxv3: why no cc_email prospects / reply_to email prospect?
              e.campaign_associated_prospect.isDefined ||
                e.from_prospect.isDefined

              ) && !e.by_account
          })

          Logger.info(s"step 0.3: emailMessagesFromProspects: ${emailMessagesFromProspects.length} :: emailMessagesFromProspects: ${emailMessagesFromProspects.map(_.email_scheduled_id)}")



          val (hardBouncedReplies, softBouncedReplies) = {

            // Check if the organization treats soft bounces as hard bounces
            val canMarkSoftBounceAsHard = AppConfig.RollingUpdates.canMarkCampaignCompletedForSoftBounce(OrgId(account.org.id))

            // Step 1: Filter emails with bouncedData defined
            val bouncedEmails = emailMessagesFromProspects.filter(_.email_status.bouncedData.isDefined)

            // Step 2: Partition into hard and soft bounces
            bouncedEmails.partition { emfp =>
              val bouncedData = emfp.email_status.bouncedData.get // Safe to use .get because of the filter
              !bouncedData.is_soft_bounced || canMarkSoftBounceAsHard
            }

          }

          val badOutBoundReplies = emailMessagesFromProspects.filter(b =>
            b.email_status.bouncedData.isDefined &&
            b.email_status.bouncedData.get.bounce_reason == EmailReplyStatusCommon.BAD_OUTBOUND_SENDER &&
            b.email_status.bouncedData.get.bounce_type == EmailReplyBounceType.SpamComplaint  &&
            inboxEmailSetting.service_provider == EmailServiceProvider.OUTLOOK_API
          )

          if(badOutBoundReplies.nonEmpty) {
            Logger.fatal(s"BAD_OUTBOUND_SENDER error : badOutBoundReplies:: ${badOutBoundReplies.map(_.email_scheduled_id)}")
          }

          val autoAndOOFReplies = emailMessagesFromProspects.filter(em => em.email_status.isOutOfOfficeReply || em.email_status.isAutoReply)

          // actualRepliesFromProspects ignores bounced, auto, and oof replies
          val actualRepliesFromProspects = emailMessagesFromProspects.filter(_.email_status.isReplied)

          Logger.info(s"step 0.4: before actualReplies")

          // 1. handle legit replies

          // do not mark as opened if delivery failed, auto reply, forwarded etc

          // MULTICAMPAIGN DONE
          if (actualRepliesFromProspects.nonEmpty) {

            emailReplyTrackingDAOService.handleActualReply(
              actualRepliesFromProspects = actualRepliesFromProspects,
              team_id = team_id,
              Logger = Logger,
              doerAccountId = AccountId(id = accountId)
            )

          }


          Logger.info(s"step 0.5: before bouncedReplies")

          // 2. handle Hard bounced replies

          if (hardBouncedReplies.nonEmpty) {

            /*
              25-Jan-2025:
                - We are handling hard bounced prospects replies here itself so not adding them to
                  toMarkAsCompletedCampaignsProspects
             */

            emailReplyTrackingDAOService.handleHardBouncedReplies(
              hardBouncedReplies = hardBouncedReplies,
              team_id = team_id,
              Logger = Logger
            )




          }
          // 3. handle Soft bounced replies

          if (softBouncedReplies.nonEmpty) {
            /**
             * 8 Aug 2024: Only for few orgs soft bounced is treated as hard bounced
             */

            emailReplyTrackingDAOService.handleSoftBouncedReplies(
              softBouncedReplies = softBouncedReplies,
              Logger = Logger
            )
          }




          if(actualRepliesFromProspects.nonEmpty){

            // delete other scheduled emails for the same prospect
            sendForDeleteScheduledEmails(
              emails = actualRepliesFromProspects,
              teamId = TeamId(id = team_id),
              deletion_reason = DeletionReason.SaveEmailsAndRepliesFromInboxV3,
              replyHandling = replyHandling,
              markProspectAsCompleted = markProspectAsCompleted
            ) match {
              case Failure(e) =>
                Logger.fatal(s"step 0.01", err = e)

              case Success(data) =>
                Logger.success(s"step 0.01: $data")
            }
          }

          {
            val notActualReplies = hardBouncedReplies ++ softBouncedReplies ++ autoAndOOFReplies

            if (notActualReplies.nonEmpty) {
              //if hard bounced that means any email we send to this prospect will fail, but prospect_account still can have valid emails so will not delete them
              //so we cant use PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY
              // and since the prospect can be in multiple campaigns we need to remove all those too so we cant use PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY

              sendForDeleteScheduledEmails(
                emails = notActualReplies,
                teamId = TeamId(id = team_id),
                deletion_reason = DeletionReason.SaveEmailsAndRepliesFromInboxV3,

                replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY,
                markProspectAsCompleted = markProspectAsCompleted
              ) match {
                case Failure(e) =>
                  Logger.fatal(s"step 0.01", err = e)

                case Success(data) =>
                  Logger.success(s"step 0.01: $data")
              }
            }
          }




          /////////
          /////////
          // Mark as completed campaigns-prospects



          /*
            25-Jan-2024:

              We are handling all those prospects except the hard-bounced ones. the hardbounced prospects were never
              added to toMarkAsCompletedCampaignsProspects so, they remain un-touched.

           */

          val completedProspectsForWebhookMain: Seq[CPCompleted] =  {
            val toMarkAsCompletedCampaignsProspectsNonHardBounced: Seq[EmailReplySavedV3] = emailMessagesFromAdminsAndSentManually ++ actualRepliesFromProspects ++ autoAndOOFReplies
            val toMarkAsCompletedCampaignsProspectsHardBounced: Seq[EmailReplySavedV3] = hardBouncedReplies
            Logger.info(s"""step 0.6 READEMAIL es.saveEmailsAndRepliesFromInboxV3 222: savedMessages: ${savedMessages.map(_.email_scheduled_id)} :: emailMessagesFromProspects: ${emailMessagesFromProspects.map(_.email_scheduled_id)} :: nonBouncedAndNonAutoAndNonOOFRepliesFromProspects: ${actualRepliesFromProspects.map(_.email_scheduled_id)} :: """)


            val completedCampaignsProspectsForWebhookNonHardBounced: Seq[CPCompleted] = if(toMarkAsCompletedCampaignsProspectsNonHardBounced.nonEmpty){
              markProspectsCompletedHelper(
                toMarkAsCompletedCampaignsProspects = toMarkAsCompletedCampaignsProspectsNonHardBounced,
                replyHandling = replyHandling,
                isHardBouncedFlow = false,
                markProspectAsCompleted = markProspectAsCompleted,
                team_id = TeamId(id = team_id),
                account = account,
                completed_reason = CampaignProspectCompletedReason.SaveEmailsAndRepliesFromInboxV3
              )
            }else{
              Seq()
            }

            /*
              25-Jan-2025:

              This function will mark the non drip campaigns prospects completed and we don't add these to the
              toMarkAsCompletedCampaignsProspects since, we are handling it here itself.
             */

            val completedCampaignsProspectsForWebhookHardBounced: Seq[CPCompleted] = if(toMarkAsCompletedCampaignsProspectsHardBounced.nonEmpty){
              markProspectsCompletedHelper(
                toMarkAsCompletedCampaignsProspects = toMarkAsCompletedCampaignsProspectsHardBounced,
                replyHandling = replyHandling,
                markProspectAsCompleted = markProspectAsCompleted,
                /*
                  24-jan-2024:
                    isHardBouncedFlow is set to true here, as we are only passing hardbouncedreplies from here.
                 */
                isHardBouncedFlow = true,
                team_id = TeamId(id = team_id),
                account = account,
                completed_reason = SaveEmailsAndRepliesFromInboxV3
              )
            }else{
              Seq()
            }
            completedCampaignsProspectsForWebhookNonHardBounced ++ completedCampaignsProspectsForWebhookHardBounced

          }


          /////////
          /////////


          val isNewCategory: Boolean = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
            teamId = TeamId(id = team_id),
            feature = SrRollingUpdateFeature.NewReplySentiments
          )(Logger)


          /**
           * 4. update category type of prospect
           * only updating from emails with campaign_associated_prospect as only autoreply, bounced, oof category being updated
           */
          //fixme: https://app.clickup.com/t/85zrphvyf
          val updateSentimentAndCategoriesForReplies: Seq[EmailReplySavedV3] = emailMessagesFromProspects
            .filter(p => {
              p.campaign_associated_prospect.isDefined &&
                !p.by_account &&
                (p.reply_type != EmailReplyType.NOT_CATEGORIZED) &&
                !GmailServiceCommon.isSendingLimitReachedMessage(
                  fromEmail = p.from_email.email,
                  emailBody = p.email_body
                )
            })

          val repliesWhichCanBeCategorizedAutomatically: Seq[EmailReplySavedV3] = updateSentimentAndCategoriesForReplies
            .filter(p => EmailReplyType.repliesWhichCanBeCategorizedAutomatically.contains(p.reply_type))

          repliesWhichCanBeCategorizedAutomatically
            .groupBy(_.reply_type)
            .map((replyType: EmailReplyType.Value, emails: Seq[EmailReplySavedV3]) => {
            prospectUpdateCategoryTemp.autoUpdateProspectCategory(
              teamId = TeamId(team_id),
              prospectIds = emails.map(e => ProspectId(e.campaign_associated_prospect.get.prospect_id)),
              doerAccountId = AccountId(accountId),
              newProspectCategory = ProspectCategory.fromEmailReplyType(replyType)
            )
          })

          setReplySentimentForNewMessages(
            team_id = team_id,
            newSavedMessages = updateSentimentAndCategoriesForReplies.filterNot(EmailReplyType.repliesWhichCanBeCategorizedAutomatically.contains(_)),
            accountId = accountId,
            orgId = OrgId(account.org.id)
          ) match {
            case Left(SetReplySentimentForNewMessagesError.GetReplySentimentsForTeamError(err)) =>
              Logger.fatal(s"Error while getting reply sentiment for team", err)
            case Left(SetReplySentimentForNewMessagesError.DbErrorWhileUpdate(err)) =>
                  Logger.fatal(s"Error while adding reply sentiment", err)
            case Right(true) => //do nothing
          }


          DBEmailMessagesSavedResponse(
            savedMessages = savedMessages,

            emailMessagesFromProspects = emailMessagesFromProspects,

            prospectIdsWithHardBouncedEmail = hardBouncedReplies
              .filter(_.prospect_id_in_campaign.isDefined)
              .map(_.prospect_id_in_campaign.get)
              .distinct,

            prospectIdsWithSoftBouncedEmail = softBouncedReplies
              .filter(_.prospect_id_in_campaign.isDefined)
              .map(_.prospect_id_in_campaign.get)
              .distinct,

            emailScheduledIdsWithHardBouncedEmail = hardBouncedReplies.map(_.email_scheduled_id).distinct,
            emailScheduledIdsWithSoftBouncedEmail = softBouncedReplies.map(_.email_scheduled_id).distinct,

            emailScheduledIdsWithAutoReplyEmail = emailMessagesFromProspects
              .filter(_.email_status.isAutoReply)
              .map(_.email_scheduled_id)
              .distinct,

            emailScheduledIdsWithOutOfOfficeReplyEmail = emailMessagesFromProspects
              .filter(_.email_status.isOutOfOfficeReply)
              .map(_.email_scheduled_id)
              .distinct,

            completedWebhookData = completedProspectsForWebhookMain,

            gmailSendingLimitErrorEmailSettingId = emailReplyTrackingModel._getGmailSendingLimitReachedErrorSenderId(
              savedReplies = savedMessages,
              Logger = Logger
            ),
            hardBouncedReplies = hardBouncedReplies,
            softBouncedReplies = softBouncedReplies
          )


        }

      }

    }
  }

  //This method will apply reply sentiment that can be auto computed by us, like out of office, and auto reply
  private def setReplySentimentForNewMessages(
                                     team_id: Long,
                                     newSavedMessages: Seq[EmailReplySavedV3],
                                     accountId: Long,
                                     orgId:OrgId
                                     )(using Logger: SRLogger): Either[SetReplySentimentForNewMessagesError, true] = {

    replySentimentService.getReplySentimentsForTeam(team_id = team_id, reply_sentiment_channel_type = ReplySentimentChannelType.EmailChannelType) match {
      case Failure(err) => Left(SetReplySentimentForNewMessagesError.GetReplySentimentsForTeamError(err = err))

      case Success(listOfReplySentiment) =>

        val updateReplySentimentFormForDAOAndUuid: List[(UpdateReplySentimentFormForDAO, ReplySentimentUuid)] =
          newSavedMessages
            .map(thread => EmailReplyTrackingModelV2.getUpdateReplySentimentFormForNewThreads(
              newSavedMessage = thread,
              listOfReplySentiment = listOfReplySentiment,
              orgId = orgId
            ))
            .filter(_.isDefined)
            .map(_.get)
            .toList

        val listOfTry: List[Try[true]] = updateReplySentimentFormForDAOAndUuid.groupBy(_._2).map(data => {
          val (reply_sentiment_uuid, listOfUuidAndReplySentimentForm) = data
          val updateReplySentimentFormForDAO: List[UpdateReplySentimentFormForDAO] = listOfUuidAndReplySentimentForm.map(_._1)

          replySentimentService.addOrUpdateReplySentiment(
            updateReplySentimentFormForDAO = updateReplySentimentFormForDAO,
            channelType = ChannelType.EmailChannel,
            reply_sentiment_uuid = reply_sentiment_uuid,
            team_id = team_id,
            accountId = accountId,
            reply_sentiment_updated_by = SmartreachAi
          )
        }).toList

        val result = Helpers.seqTryToTrySeq(listOfTry)

        result match {
          case Failure(err) => Left(SetReplySentimentForNewMessagesError.DbErrorWhileUpdate(err))
          case Success(list) => Right(true)
        }
    }
  }

  private def checkforAutoReply(
                         message_to_check_for_auto_reply: EmailMessageTracked
                       )(using Logger: SRLogger): Try[EmailMessageTracked] = {
    if (message_to_check_for_auto_reply.campaign_id.isDefined && message_to_check_for_auto_reply.prospect_id_in_campaign.isDefined && message_to_check_for_auto_reply.email_status.isReplied) {
      emailScheduledDAOService.getPreviousSentSteps(
        campaignId = message_to_check_for_auto_reply.campaign_id.get,
        prospectId = message_to_check_for_auto_reply.prospect_id_in_campaign.get,
        teamId = TeamId(message_to_check_for_auto_reply.team_id)
      ).map { previous_steps =>
        if (previous_steps.isEmpty) {
          message_to_check_for_auto_reply
        } else {
          val lastSentStep = previous_steps.maxBy(_.sent_at)

          if (message_to_check_for_auto_reply.recorded_at.isBefore(lastSentStep.sent_at.plusSeconds(AppConfig.delay_to_consider_an_email_auto_reply))) {
            Logger.info(s"Taking as AutoReply for cid_${message_to_check_for_auto_reply.campaign_id.get} pid_${message_to_check_for_auto_reply.prospect_id_in_campaign.get} new_message_recorded_at: ${message_to_check_for_auto_reply.recorded_at} last_campaign_message_sent_at - ${lastSentStep.sent_at} message - ${message_to_check_for_auto_reply.body}")
            message_to_check_for_auto_reply.copy(
              email_status = message_to_check_for_auto_reply.email_status.copy(
                replyType = EmailReplyType.AUTO_REPLY,
                isAutoReply = true,
              )
            )
          } else message_to_check_for_auto_reply

        }
      }
    } else Success(message_to_check_for_auto_reply)

  }

  def insertTrackedRepliesV3(
                              newMsgsWithEmailThreadId: Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])],
                              sentManuallyFromSRInbox: Boolean,
                              inboxEmailSetting: EmailSetting,
                              allow_using_sr_ai_api: Boolean,
                              plan_id: PlanID,
                              team_id: TeamId
                            )(using Logger: SRLogger): Try[Seq[EmailReplySavedV3]] =
  if (newMsgsWithEmailThreadId.isEmpty) Success(Seq()) else
  Try {
    newMsgsWithEmailThreadId.foreach(msg_and_prospect => {
      val msg = msg_and_prospect._1
      if (msg.tempThreadId.isEmpty) {
        Logger.fatal(s"emailReplyTrackingModelV2.insertTrackedRepliesV3  assertion that all new messages have a tempthreadid has failed msgId = message_id ${msg.message_id} , references_header: ${msg.references_header} , in_reply_to_header : ${msg.in_reply_to_header}")
      }
    })

    val savedReplies = emailReplyTrackingModel._insertTrackedRepliesV3(
      newMsgsWithEmailThreadId = newMsgsWithEmailThreadId,
      sentManuallyFromSRInbox = sentManuallyFromSRInbox
    ).get

    val sendForSRAiApiCall: Try[TeamSRAIFlags] = teamService.checkIfWePushForSrAiApiCall(
      allow_using_sr_ai_api = allow_using_sr_ai_api,
      teamId = team_id,
      plan_id = plan_id
    )
    val publishForMQReplySentimentForTrackerData: Boolean = sendForSRAiApiCall
      .map(_.allow_using_sr_ai_api_for_reply_sentiment)
      .getOrElse(true) //if the query fails we still push to the queue and filter it out on the side of the queue
    // update latest_email_id in email threads after adding new emails and
    // mark newly added / updated email_threads as unread

    //if the query fails we still push to the queue and filter it out on the side of the queue
    val publishForMQOOOClassification = sendForSRAiApiCall
      .map(_.allow_using_sr_ai_api_for_ooo)
      .getOrElse(true)

    emailThreadDAOService._updateLatestEmailId(

      // WRONG: get only distinct email_thread_id
      // RIGHT (corrected later): get all the emails, because sometimes emails which are replies get missed being tracked as replies
      // REF: https://stackoverflow.com/a/3912833
      data = savedReplies
        .map(em => {

          if(!em.by_account && em.prospect_id_in_campaign.isDefined && em.campaign_id.isDefined) {

            if (publishForMQReplySentimentForTrackerData) {
              //FIXME SRAiAPI move the publish to SrAiApiAuditLog

              val replySentimentMessage = MqSrAiApiMessage.MQReplySentimentForTrackerData(
                team_id = em.team_id,
                email_thread_id = em.email_thread_id,
                account_id = inboxEmailSetting.owner_id,
                campaign_id = CampaignId(em.campaign_id.get),
                prospect_ids = em.all_prospects_involved.map(a => ProspectId(a.prospect_id)).toList,
                allow_using_sr_ai_api = allow_using_sr_ai_api,
                plan_id = plan_id
              )
              
              createPendingAuditLogForAiApi(
                message = replySentimentMessage,
                flow = LlmFlow.ReplySentiment,
                primaryProspectId = ProspectId(em.all_prospects_involved.head.prospect_id)
              ) match {
                case Success(logId) => 
                  Logger.info(s"Successfully created reply sentiment audit log with ID: $logId for email thread: ${em.email_thread_id}")
                case Failure(err) => 
                  Logger.shouldNeverHappen(s"Failed to create reply sentiment audit log for email thread: ${em.email_thread_id}", Some(err))
              }

            }

            if (publishForMQOOOClassification) {
              //FIXME SRAiAPI move the publish to SrAiApiAuditLog

              val oooMessage = MqSrAiApiMessage.MqOutOfOfficeCheckMessage(
                team_id = em.team_id,
                email_thread_id = em.email_thread_id,
                account_id = AccountId(id = inboxEmailSetting.owner_id.id),
                campaign_id = CampaignId(id = em.campaign_id.get),
                prospect_ids = em.all_prospects_involved.map(p => ProspectId(id = p.prospect_id)).toList,
                allow_using_sr_ai_api = allow_using_sr_ai_api,
                plan_id = plan_id,
              )
              
              createPendingAuditLogForAiApi(
                message = oooMessage,
                flow = LlmFlow.OutOfOfficeClassification,
                primaryProspectId = ProspectId(em.all_prospects_involved.head.prospect_id),
              ) match {
                case Success(logId) => 
                  Logger.info(s"Successfully created OOO classification audit log with ID: $logId for email thread: ${em.email_thread_id}")
                case Failure(err) => 
                  Logger.shouldNeverHappen(s"Failed to create OOO classification audit log for email thread: ${em.email_thread_id}", Some(err))
              }

            }

          }
          EmailThreadUpdateLatestEmailData(
            email_thread_id = em.email_thread_id,
            by_account = em.by_account,
            latest_email_id = em.email_scheduled_id,
            sent_at = em.sent_at,
            latest_sent_by_admin_at = if (inboxEmailSetting.email.trim.toLowerCase() == em.from_email.email.trim.toLowerCase()) Some(em.sent_at) else None,
            by_prospect = em.from_prospect.isDefined,
            folderType = EmailReplyTrackingModelV2.getFolderTypeOfEmailThread(
              email_reply_status = Some(em.email_status),
              has_prospect = em.all_prospects_involved.nonEmpty,
              email_subject = em.subject
            )
        )})
        .reverse // reverse chronologically
      // .groupBy(_.email_thread_id)
      // .map(_._2.head)
      // .toSeq

    )

    // val savedReplyIds = toUpdateEmailThreads.map(_.latest_email_id)
    savedReplies



  }

}

/*
   11-jun-2024:

   please check again if we need to update prospect status ( category ) while doing this also.

 */

object EmailReplyTrackingModelV2 {


  def mapEmailMessagesToProspects(
                            validProspects:List[ERIntermediateValidProspect],
                            emailMessages:Seq[EmailMessageTracked],
                            adminReplyFromSRInbox: Boolean,
                            validStepsById: Map[Long, (Long, String)], // FIXME: Convert to value class Later
                            validCampaignById: Map[Long, (Long, String)],//FIXME: Convert to value class Later
                            senderEmails: Seq[String]
                            )(using Logger:SRLogger): Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])] = {


    val validProspectsById: Map[Long, ERIntermediateValidProspect] = validProspects.map(r => (r.prospect_id -> r)).toMap

    val validProspectIds: Seq[Long] = validProspects.map(_.prospect_id)




    emailMessages.map(msg => {

      val prospectsInvolved: List[ERIntermediateValidProspect] = {

        val _prospectsInvolvedByToEmails = msg.to_emails
          .map(e => {
            validProspects.find(vp => vp.email.trim.toLowerCase == e.email.trim.toLowerCase)
          })
          .filter(_.isDefined)
          .map(_.get)

        val _prospectInvolvedByFromEmail = if (msg.from.email.isEmpty) Seq() else {
          validProspects
            .find(vp => vp.email.trim.toLowerCase == msg.from.email.trim.toLowerCase)
            .map(vp => Seq(vp))
            .getOrElse(Seq())
        }


        val _prospectInvolvedByPrimaryProspectId = if (msg.prospect_id_in_campaign.isEmpty) Seq() else {
          val vpFound = validProspects.find(vp => vp.prospect_id == msg.prospect_id_in_campaign.get)

          if (vpFound.isEmpty) {
            Logger.fatal(s"msg.prospect_id.nonEmpty but vpFound.isEmpty :: msg.prospect_id: ${msg.prospect_id_in_campaign} :: message_id: ${msg.message_id}")
            Seq()
          } else {
            Seq(vpFound.get)
          }
        }

        // fixme inboxv3: ccemails?? also replyto email
        (_prospectsInvolvedByToEmails ++ _prospectInvolvedByFromEmail ++ _prospectInvolvedByPrimaryProspectId)
          .groupBy(_.email.trim.toLowerCase)
          .map(_._2.head)
          .toList

      }

      /**
       * NOTE: we are also saving emails not from prospects
       * newinbox - Cannot remove this, its the prospect whose campaign status needs to be changed, even if they may not be directly involved in this email
       * */
      val prospectIdInCampaign: Option[Long] = if (msg.prospect_id_in_campaign.isDefined &&

        // prospect_id as well is being checked in intermediate sql "_findIntermediateProspectsForReplyTracking"
        validProspectIds.contains(msg.prospect_id_in_campaign.get)
      ) {
        msg.prospect_id_in_campaign
      } else {
        // fixme inboxv3: this entire else block should be removed

        val messageId: String = msg.message_id

        if (msg.prospect_id_in_campaign.isDefined) {
          Logger.info(s"inboxv3 check: msg.prospect_id_in_campaign.isDefined but validProspect not found: msg.prospect_id_in_campaign: ${msg.prospect_id_in_campaign} :: mid: $messageId")
        }

        // 30-Jan-2021: there is a possibility that msg.prospect_id_in_campaign isEmpty, but prospect exists and is found via email -
        // while manual testing, I found that many emails from "<EMAIL>" were not correctly associated with the prospectId (was null)
        // when the prospect existed - <NAME_EMAIL> is a admin email setting as well
        // fixme inboxv3: this case would go away when we remove the code block for creating prospects automatically
        val checkProspectByFromEmail = validProspects.find(pr => pr.email.trim.toLowerCase == msg.from.email.trim.toLowerCase)


        /**
         * 4-Mar-2021: while replying manually from inbox, we are sending email.prospect_id as None from
         * InboxController.sendNewManualEmailV2 api
         *
         */
        val checkProspectByToEmails = {

          val toEmails = msg
            .to_emails
            .map(_.email.trim.toLowerCase)
            .filterNot(em => {

              /**
               * 4-March-2021: if the sending admin is also present as a prospect, then also do not assign it the admin
               * fixme inboxV3: this must be removed / handled more subtly: this problem would go away
               *   with the inboxv3 layout, in email_messages_contacts table we can treat the same email as both a prospect and as admin
               * sometimes the admin would have added themshelves as a prospect, and then if they are replying
               * to a legitimate prospect, then there could be issues
               */
              senderEmails.map(_.trim.toLowerCase).contains(em)
            })

          validProspects.filter(pr => toEmails.contains(pr.email.trim.toLowerCase))
        }


        if (checkProspectByFromEmail.isDefined) {

          Logger.info(s"inboxv3 check: checkProspectByFromEmail is found: pid: ${checkProspectByFromEmail.map(_.prospect_id)} :: mid: $messageId")

          Some(checkProspectByFromEmail.get.prospect_id)

        } else if (checkProspectByToEmails.nonEmpty) {

          /**
           * fixme inboxv3: newinbox this will go with our InboxV3 upgrade
           */
          if (checkProspectByToEmails.length > 1) {
            Logger.info(s"inboxv3 check: checkProspectByToEmails has multiple to emails, we are taking just the first one by default: pids: ${checkProspectByToEmails.map(_.prospect_id)} :: mid: $messageId")
          }

          checkProspectByToEmails.headOption.map(_.prospect_id)

        } else {

          None

        }

      }

      // NOTE: prospect_account_id is populating it in the TEmailService (not always, like in message-id check), BUT we overwrite it here
      val prospectAccountIdInCampaign = prospectIdInCampaign.flatMap(pid => {
        validProspectsById(pid).prospect_account_id
      })


      // newinbox check
      if (msg.prospect_account_id_in_campaign.isDefined) {
        val paIdFromTEmailService = msg.prospect_account_id_in_campaign.get

        if (prospectAccountIdInCampaign.isEmpty || paIdFromTEmailService != prospectAccountIdInCampaign.get) {

          Logger.fatal(s"paIdFromTEmailService not matching with prospectAccountId ($paIdFromTEmailService != $prospectAccountIdInCampaign) :: prospectId: $prospectIdInCampaign :: msgId: ${msg.message_id}")

        }
      }

      // sometimes for testing or while manual email sent, we set the step_id = 0
      // set those as null, otherwise there will be an sql error

      // also, if user has deleted the step meanwhile, ignore the step_id
      // if we don't do this there will be a SQL error because of missing foreign key in
      // the step_id column
      val stepId: Option[Long] = if (adminReplyFromSRInbox) {
        msg.step_id
      } else if (msg.step_id.isDefined && validStepsById.contains(msg.step_id.get)) {
        if (msg.step_id.get == 0) None else msg.step_id
      } else None

      if (msg.step_id.isDefined && stepId.isEmpty) {
        Logger.fatal(s"INVALID STEPID: msg.step_id: ${msg.step_id.get} :: mid: ${msg.message_id} ")
      }

      val stepLabel: Option[String] = if (adminReplyFromSRInbox) {
        msg.step_name
      } else if (stepId.isDefined) {
        Some(validStepsById(stepId.get)._2)
      } else None

      // also, if user has deleted the campaign meanwhile, ignore the campaign_id
      val campaignId: Option[Long] = if (adminReplyFromSRInbox) {
        msg.campaign_id
      } else if (msg.campaign_id.isDefined && validCampaignById.contains(msg.campaign_id.get)) {
        if (msg.campaign_id.get == 0) None else msg.campaign_id
      } else None


      if (msg.campaign_id.isDefined && campaignId.isEmpty) {
        Logger.fatal(s"INVALID CAMPAIGNID: msg.campaign_id: ${msg.campaign_id.get} :: mid: ${msg.message_id} ")
      }

      val campaignName: Option[String] = if (adminReplyFromSRInbox) {
        msg.campaign_name
      } else if (campaignId.isDefined) {
        Some(validCampaignById(campaignId.get)._2)
      } else None


      val cleanedUpEmail = msg.copy(

        // REF: https://stackoverflow.com/questions/1347646/postgres-error-on-insert-error-invalid-byte-sequence-for-encoding-utf8-0x0

        subject = msg.subject.replaceAll("\u0000", ""),

        body = msg.body.replaceAll("\u0000", ""),

        base_body = msg.base_body.replaceAll("\u0000", ""),


        prospect_id_in_campaign = prospectIdInCampaign,
        prospect_account_id_in_campaign = prospectAccountIdInCampaign,
        campaign_id = campaignId,
        campaign_name = campaignName,
        step_id = stepId,
        step_name = stepLabel,
        // we know that this is copied over
        // tempThreadId = msg.tempThreadId

      )

      (cleanedUpEmail, prospectsInvolved)
    })
  }

  

  private def findMatchingSentiment(
                                     isOutOfOfficeReply: Boolean,
                                     isUnsubscribeRequest: Boolean,
                                     isAutoReply: Boolean,
                                     sentiments: List[ReplySentimentForTeam]
                                   ): Option[ReplySentimentForTeam] = {

    if (isOutOfOfficeReply) {
      ReplySentimentTypeData.findOutOfOfficeSentiment(sentiments = sentiments)
    } else if (isUnsubscribeRequest) {
      ReplySentimentTypeData.findUnsubscribeSentiment(sentiments = sentiments)
    } else if (isAutoReply) {
      ReplySentimentTypeData.findAutoReplySentiment(sentiments = sentiments)
    } else {
      None
    }
  }

  private def createUpdateForm(
                        newSavedMessage: EmailReplySavedV3,
                        isCampaignProspect: Boolean,
                        isProspect: Boolean,
                        prospectIds: List[ProspectId]
                      ): UpdateReplySentimentFormForDAO = {

    (isCampaignProspect, isProspect) match {
      case (false, false) =>
        UpdateReplySentimentFormForDAO.NoProspectThread(
          email_thread_id = newSavedMessage.email_thread_id
        )
      case (false, true) =>
        UpdateReplySentimentFormForDAO.ProspectThread(
          prospect_id = prospectIds.map(_.id),
          email_thread_id = newSavedMessage.email_thread_id
        )
      case (true, _) =>
        UpdateReplySentimentFormForDAO.CampaignProspectThread(
          campaign_id = newSavedMessage.campaign_id.get,
          prospect_id = prospectIds.map(_.id),
          email_thread_id = newSavedMessage.email_thread_id
        )
    }
  }


  def getUpdateReplySentimentFormForNewThreads(
                                                newSavedMessage: EmailReplySavedV3,
                                                listOfReplySentiment: List[ReplySentimentForTeam],
                                                orgId: OrgId
                                              )(using Logger:SRLogger): Option[(UpdateReplySentimentFormForDAO, ReplySentimentUuid)] = {

    val emailStatus = newSavedMessage.email_status
    val isOutOfOfficeReply = emailStatus.isOutOfOfficeReply
    val isUnsubscribeRequest = emailStatus.isUnsubscribeRequest
    val isAutoReply = emailStatus.isAutoReply

    // Determine thread type
    val isCampaignProspect = newSavedMessage.campaign_id.isDefined &&
      newSavedMessage.prospect_id_in_campaign.isDefined
    val isProspect = newSavedMessage.all_prospects_involved.nonEmpty
    val prospectIds = newSavedMessage.all_prospects_involved.map(p => ProspectId(p.prospect_id)).toList



    // Find matching sentiment based on email type
    val selectedSentiment = findMatchingSentiment(
      isOutOfOfficeReply = isOutOfOfficeReply,
      isUnsubscribeRequest = isUnsubscribeRequest ,
      isAutoReply = isAutoReply ,
      sentiments = listOfReplySentiment
    )
    if(isOutOfOfficeReply && selectedSentiment.isEmpty){
      Logger.shouldNeverHappen(s"isOutOfOfficeReply is true but selectedSentiment is empty orgId:: ${orgId.id} tid_${newSavedMessage.team_id.id}")

    }else if(isUnsubscribeRequest && selectedSentiment.isEmpty){
      Logger.shouldNeverHappen(s"isUnsubscribeRequest is true but selectedSentiment is empty orgId:: ${orgId.id} tid_${newSavedMessage.team_id.id}")

    }else if(isAutoReply && selectedSentiment.isEmpty){
      Logger.shouldNeverHappen(s"isUnsubscribeRequest is true but selectedSentiment is empty orgId:: ${orgId.id} tid_${newSavedMessage.team_id.id}")
    }


    selectedSentiment.map { sentiment =>
      val form = createUpdateForm(
        newSavedMessage = newSavedMessage,
        isCampaignProspect = isCampaignProspect,
        isProspect = isProspect,
        prospectIds =prospectIds
      )
      (form, sentiment.uuid)
    }


  }

  def getFolderTypeOfEmailThread(
                                  email_reply_status: Option[EmailReplyStatus],
                                  has_prospect: Boolean,
                                  email_subject: String
                                ): FolderType = {

    val is_hard_or_soft_bounced: Boolean = if(email_reply_status.isDefined){
      EmailReplyStatus.isHardOrSoftBounced(emailReplyStatus = email_reply_status.get)
    } else {
      false
    }

    val isWarmupEmail: Boolean = email_subject.contains("wrmpbx")

    if(is_hard_or_soft_bounced || isWarmupEmail) {
      FolderType.IRRELEVANT
    } else if(has_prospect) {
      FolderType.PROSPECTS
    } else {
      FolderType.NON_PROSPECTS
    }
  }

   private def modifyEmailThreadData(
                                     data: Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])],
                                     newThreads: Seq[SavedEmailThread],
                                     inboxEmailSetting: EmailSetting
                                   )(using Logger: SRLogger): Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])] = {
    data.map(em => {

      if (em._1.email_thread_id.isDefined) em else {

        val (email, prospectsInvolved) = em
        // nxd:  just to note that we are suspecting the bug around this section
        val emailThread = newThreads.find(t => t.temp_thread_id.get == email.tempThreadId.get)

        if (emailThread.isEmpty) {
          Logger.fatal(s"assigning new email thread id not found: ${inboxEmailSetting.id.map(_.emailSettingId)} :: ${inboxEmailSetting.email} :: ${em._1.email_thread_id} :: ${newThreads.map(_.id)}")

          throw new Exception(s"${Logger.logRequestId} new email thread not found")
        } else {

          val prev_temp_thread_id = email.tempThreadId
          val newEmail = email.copy(
            email_thread_id = Some(emailThread.get.id),
            tempThreadId = emailThread.flatMap(_.temp_thread_id).map(_.toInt)
          )
          val new_temp_thread_id_if_changed = newEmail.tempThreadId
          if (prev_temp_thread_id.isDefined && new_temp_thread_id_if_changed.isDefined &&
            prev_temp_thread_id.get != new_temp_thread_id_if_changed.get) {
            Logger.info(s"__assignEmailThreadIdsForExistingEmailThreadsV4 INBOXDEBUG prev_temp_thread_id :  ${prev_temp_thread_id}, new_temp_thread_id_if_changed = ${new_temp_thread_id_if_changed} ")
          }
          (
            newEmail,
            prospectsInvolved
          )
        }

      }

    })
  }

}
