package api.emails

import api.accounts.email.models.{EmailProvidedBy, EmailServiceProvider}
import api.accounts.models.{AccountId, OrgId}

import java.security.KeyPairGenerator
import java.security.interfaces.{RS<PERSON>ri<PERSON><PERSON><PERSON>, RSAPublic<PERSON><PERSON>}
import javax.mail.Address
import javax.mail.internet.InternetAddress
import api.{AppConfig, CONSTANTS, emails}
import api.accounts.{AccountUuid, AccountWarningCodeType, OrgMetadata, RepGoogleApiKey, ReplyHandling, TeamAccount, TeamId}
import io.sr.billing_common.models.PlanType
import api.blacklist.Blacklist
import api.campaigns.services.CampaignId
import api.email_infra_integrations.models.{DomainForDeletionData, EmailForDeletionData, PlatformType, PurchaseDomainsAndEmailsStatus}
import api.email_infra_integrations.zapmail.ZapMailSubscriptionID
import api.accounts.email.models.EmailProvidedBy.{Maildoso, User}
import api.emails.models.{DomainProvider, EmailReplyType, EmailSettingIntegrationLogsStage, EmailSettingUuid, WarmupHeroEmailSettingResponse}
import api.prospects.InferredQueryTimeline
import api.prospects.models.{ProspectCategory, ProspectCategoryId, ProspectId}
import api.team.TeamUuid
import api.team_inbox.model.InitialEmailSyncData
import app_services.blacklist_monitoring.models.{BlacklistCheckResult, BlacklistCheckStatus, BlacklistCheckType}
import eventframework.PaginationSortedData
import io.smartreach.esp.api.emailApi.models.{DKIMRecord, ESPMailgunRegion, ImapEmailSetting, SmtpEmailSetting}
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import io.smartreach.esp.api.microsoftOAuth.EmailSettingUpdateAccessToken
import io.smartreach.esp.utils.email.EmailHelperCommon
import org.joda.time.DateTime
import org.postgresql.util.PGobject
import play.api.Logging
import play.api.libs.json.{Format, JsError, JsNumber, JsResult, JsSuccess, JsValue, Json, OWrites, Reads, Writes}
import play.api.libs.json.JodaReads.*
import play.api.libs.json.JodaWrites.*
import scalikejdbc.*
import scalikejdbc.jodatime.JodaWrappedResultSet.*
import sr_scheduler.CampaignStatus
import sr_scheduler.models.{ChannelDataDaoTrait, EmailSettingCreateEmailSchedule}
import utils.cache_utils.model.CampaignUseStatusForEmailSetting
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus.{Active, InActive}
import utils.dbutils.{DBUtils, SQLUtils}
import utils.{Helpers, SRLogger, SrLoggerTrait, StringUtils}
import utils.email.EmailHelper
import utils.email.models.SendScheduleEmailType
import utils.emailvalidation.EmailValidationService
import utils.enum_sr_utils.EnumUtils
import utils.security.EncryptionHelpers
import utils.uuid.SrUuidUtils

import scala.util.{Failure, Success, Try}
import scala.concurrent.blocking
import play.api.libs.json.*
import play.api.libs.functional.syntax.*


case class PurchasedEmailInfo(
                             email: String,
                             domain: String,
                             platformType: PlatformType
                             )



case class CustomTrackingDomainForm(
  tracking_domain_host: String
)

case class EmailSendReceiveOauthStatus(
    can_send: Boolean,
    can_receive: Boolean,
    oauth2_enabled: Boolean
)

case class EmailSettingStatusData (
    email: String,
    status: EmailSettingStatus
                                     )


// create form

case class EmailSettingForm(

  email: String,

  service_provider: EmailServiceProvider,

  smtp_username: Option[String],
  smtp_password: Option[String],
  smtp_host: Option[String],
  smtp_port: Option[Int],

  imap_username: Option[String],
  imap_password: Option[String],
  imap_host: Option[String],
  imap_port: Option[Int],

  // for mailgun
  email_domain: Option[String],
  api_key: Option[String],
  mailgun_region: Option[ESPMailgunRegion.Value],

  quota_per_day: Int,
  min_delay_seconds: Int,
  max_delay_seconds: Int,


  can_send: Boolean,
  can_receive: Boolean,

  cc_emails: Option[String],
  bcc_emails: Option[String],

  provided_by: EmailProvidedBy = EmailProvidedBy.User,

  first_name: String,
  last_name: String,

  status: EmailSettingStatus = EmailSettingStatus.Active, // By default it is active in db

  /*
    - Platform Email ID
    * in case of Maildoso, it is the platform_email_id
    * in case of ZapMail, it is subscriptionId
  */
  platform_email_id: Option[String],

  email_tag: Option[String]
)

object EmailSettingForm {
  given format: Format[EmailSettingForm] = new Format[EmailSettingForm] {
    override def reads(json: JsValue): JsResult[EmailSettingForm] = Try {
      EmailSettingForm(
        email = (json \ "email").as[String],
        service_provider = (json \ "service_provider").as[EmailServiceProvider],

        smtp_username = (json \ "smtp_username").asOpt[String],
        smtp_password = (json \ "smtp_password").asOpt[String],
        smtp_host = (json \ "smtp_host").asOpt[String],
        smtp_port = (json \ "smtp_port").asOpt[Int],

        imap_username = (json \ "imap_username").asOpt[String],
        imap_password = (json \ "imap_password").asOpt[String],
        imap_host = (json \ "imap_host").asOpt[String],
        imap_port = (json \ "imap_port").asOpt[Int],

        email_domain = (json \ "email_domain").asOpt[String],
        api_key = (json \ "api_key").asOpt[String],
        mailgun_region = (json \ "mailgun_region").asOpt[ESPMailgunRegion.Value],

        quota_per_day = (json \ "quota_per_day").as[Int],
        min_delay_seconds = (json \ "min_delay_seconds").as[Int],
        max_delay_seconds = (json \ "max_delay_seconds").as[Int],

        can_send = (json \ "can_send").as[Boolean],
        can_receive = (json \ "can_receive").as[Boolean],

        cc_emails = (json \ "cc_emails").asOpt[String],
        bcc_emails = (json \ "bcc_emails").asOpt[String],

        provided_by = (json \ "provided_by").asOpt[EmailProvidedBy].getOrElse(User),

        first_name = (json \ "first_name").as[String],
        last_name = (json \ "last_name").as[String],

        // Reading it as optional, and marking it as Active if not found. Since, by default it is active.
        status = (json \ "status").asOpt[EmailSettingStatus].getOrElse(EmailSettingStatus.Active),

        platform_email_id = (json \ "platform_email_id").asOpt[String],
        
        // Email tag for categorizing and filtering emails
        email_tag = (json \ "email_tag").asOpt[String]
      )
    } match {
      case Failure(e) => JsError(e.toString)
      case Success(value) => JsSuccess(value)
    }

    override def writes(o: EmailSettingForm): JsValue = {
      Json.obj(
        "email" -> o.email,
        "service_provider" -> o.service_provider,

        "smtp_username" -> o.smtp_username,
        "smtp_password" -> o.smtp_password,
        "smtp_host" -> o.smtp_host,
        "smtp_port" -> o.smtp_port,

        "imap_username" -> o.imap_username,
        "imap_password" -> o.imap_password,
        "imap_host" -> o.imap_host,
        "imap_port" -> o.imap_port,

        "email_domain" -> o.email_domain,
        "api_key" -> o.api_key,
        "mailgun_region" -> o.mailgun_region,

        "quota_per_day" -> o.quota_per_day,
        "min_delay_seconds" -> o.min_delay_seconds,
        "max_delay_seconds" -> o.max_delay_seconds,

        "can_send" -> o.can_send,
        "can_receive" -> o.can_receive,

        "cc_emails" -> o.cc_emails,
        "bcc_emails" -> o.bcc_emails,

        "provided_by" -> o.provided_by,

        "first_name" -> o.first_name,
        "last_name" -> o.last_name,

        "status" -> o.status,

        "platform_email_id" -> o.platform_email_id,
        
        // Email tag for categorizing and filtering emails
        "email_tag" -> o.email_tag
      )
    }
  }
}


object CustomTrackingDomainForm {
  given reads: Reads[CustomTrackingDomainForm] = Json.reads[CustomTrackingDomainForm]
}

// UPDATE signature FORM

case class EmailSettingUpdateSignatureForm(
  signature: String
)

object EmailSettingUpdateSignatureForm {
  given reads: Reads[EmailSettingUpdateSignatureForm] = Json.reads[EmailSettingUpdateSignatureForm]
}


case class EmailSettingCreateViaOAuth(

  email: String,

  service_provider: EmailServiceProvider,

  sender_name: String,
  first_name: String,
  last_name: String,
  domain: String,

  oauth2_access_token: String,
  oauth2_refresh_token: String,
  oauth2_token_type: String,
  oauth2_token_expires_in: Int,
  oauth2_access_token_expires_at: DateTime,

  rep_google_api_key_id: Option[Int],
  rep_mail_server_id: Option[Int],
  rep_tracking_host_id: Option[Int],
  gsuite_domain_install: Option[Boolean],
  domain_provider: Option[EmailProvidedBy],

  quota_per_day: Int,
  email_purchase_status: Option[PurchaseDomainsAndEmailsStatus],

  alias_parent_id: Option[Long]

)

case class EmailSettingDailyLimitAndDelay(
  es_ids: Seq[EmailSettingId],
  quota_per_day: Option[Int],
  min_delay_seconds: Option[Int],
  max_delay_seconds: Option[Int]
)

object EmailSettingDailyLimitAndDelay {
  given reads: Reads[EmailSettingDailyLimitAndDelay] = {
    implicit val emailSettingIdListReads: Reads[Seq[EmailSettingId]] =
      Reads.seq[Long].map(_.map(EmailSettingId.apply))
    
    Json.reads[EmailSettingDailyLimitAndDelay]
  }
}


case class EmailSetting(
  id: Option[EmailSettingId],
  uuid: Option[EmailSettingUuid],
  org_id: OrgId,
  owner_id: AccountId,
  owner_uuid: AccountUuid,
  team_id: TeamId,
  team_uuid: TeamUuid,
  message_id_suffix: String,

  email: String,
  email_address_host: String,

  service_provider: EmailServiceProvider,
  domain_provider: Option[EmailProvidedBy],                     
  via_gmail_smtp: Option[Boolean],

  owner_name: String,
  sender_name: String,
  first_name: String,
  last_name: String,

  cc_emails: Option[String],
  bcc_emails: Option[String],

  smtp_username: Option[String],
  smtp_password: Option[String],
  smtp_host: Option[String],
  smtp_port: Option[Int],

  imap_username: Option[String],
  imap_password: Option[String],
  imap_host: Option[String],
  imap_port: Option[Int],

  oauth2_access_token: Option[String],
  oauth2_refresh_token: Option[String],
  oauth2_token_type: Option[String],
  oauth2_token_expires_in: Option[Int],
  oauth2_access_token_expires_at: Option[DateTime],

  // for mailgun
  email_domain: Option[String],
  api_key: Option[String],
  mailgun_region: Option[ESPMailgunRegion.Value],

  quota_per_day: Int,

  reply_handling: ReplyHandling.Value,
  last_read_for_replies: Option[DateTime],
  latest_email_scheduled_at: Option[DateTime],

  error: Option[String],
  error_reported_at: Option[DateTime],
  paused_till: Option[DateTime],

  signature: String,

  created_at: Option[DateTime],

  current_prospect_sent_count_email: Int,

  default_tracking_domain: String,
  default_unsubscribe_domain: String,
  rep_tracking_host_id: Int,
  tracking_domain_host: Option[String],
  custom_tracking_domain: Option[String],
  custom_tracking_cname_value: Option[String],
  custom_tracking_domain_is_verified: Option[Boolean],
  custom_tracking_domain_is_ssl_enabled: Option[Boolean],

  rep_mail_server_id: Int,
  rep_mail_server_public_ip: String,
  rep_mail_server_host: String,
  rep_mail_server_reverse_dns: Option[String],

  min_delay_seconds: Int,
  max_delay_seconds: Int,

  tag: Option[String],

  campaign_use_status_for_email_setting: CampaignUseStatusForEmailSetting,
  show_rms_ip_in_frontend: Boolean

) extends PaginationSortedData {
  override def getSortBy: DateTime = created_at.get

  override def getExactlyAtIdOrUuid = id.map(_.emailSettingId.toString).get // should never happen ??
}

object EmailSetting {

//  given reads = new Reads[EmailSetting] {
//    override def reads(json: JsValue): JsResult[EmailSetting] = Try {
//      EmailSetting(
//        id = (json \ "id").asOpt[Long].map(EmailSettingId(_)),
//        org_id = (json \ "org_id").as[OrgId],
//        owner_id = (json \ "owner_id").as[AccountId],
//        team_id = (json \ "team_id").as[TeamId],
//        message_id_suffix = (json \ "message_id_suffix").as[String],
//
//        email = (json \ "email").as[String],
//        email_address_host = (json \ "email_address_host").as[String],
//
//        service_provider = (json \ "service_provider").as[EmailServiceProvider],
//        via_gmail_smtp = (json \ "via_gmail_smtp").asOpt[Boolean],
//
//        owner_name = (json \ "owner_name").as[String],
//        sender_name = (json \ "sender_name").as[String],
//        first_name = (json \ "first_name").as[String],
//        last_name = (json \ "last_name").as[String],
//
//        cc_emails = (json \ "cc_emails").asOpt[String],
//        bcc_emails = (json \ "bcc_emails").asOpt[String],
//
//        smtp_username = (json \ "smtp_username").asOpt[String],
//        smtp_password = (json \ "smtp_password").asOpt[String],
//        smtp_host = (json \ "smtp_host").asOpt[String],
//        smtp_port = (json \ "smtp_port").asOpt[Int],
//
//        imap_username = (json \ "imap_username").asOpt[String],
//        imap_password = (json \ "imap_password").asOpt[String],
//        imap_host = (json \ "imap_host").asOpt[String],
//        imap_port = (json \ "imap_port").asOpt[Int],
//
//        oauth2_access_token = (json \ "oauth2_access_token").asOpt[String],
//        oauth2_refresh_token = (json \ "oauth2_refresh_token").asOpt[String],
//        oauth2_token_type = (json \ "oauth2_token_type").asOpt[String],
//        oauth2_token_expires_in = (json \ "oauth2_token_expires_in").asOpt[Int],
//        oauth2_access_token_expires_at = (json \ "oauth2_access_token_expires_at").asOpt[DateTime],
//
//        // for mailgun
//        email_domain = (json \ "email_domain").asOpt[String],
//        api_key = (json \ "api_key").asOpt[String],
//        mailgun_region = (json \ "mailgun_region").asOpt[ESPMailgunRegion.Value],
//
//        quota_per_day = (json \ "quota_per_day").as[Int],
//
//        reply_handling = (json \ "reply_handling").as[ReplyHandling.Value],
//        last_read_for_replies = (json \ "last_read_for_replies").asOpt[DateTime],
//        latest_email_scheduled_at = (json \ "latest_email_scheduled_at").asOpt[DateTime],
//
//        error = (json \ "error").asOpt[String],
//        error_reported_at = (json \ "error_reported_at").asOpt[DateTime],
//        paused_till = (json \ "paused_till").asOpt[DateTime],
//
//        signature = (json \ "signature").as[String],
//
//        created_at = (json \ "created_at").asOpt[DateTime],
//
//        current_prospect_sent_count_email = (json \ "current_prospect_sent_count_email").as[Int],
//
//        default_tracking_domain = (json \ "default_tracking_domain").as[String],
//        default_unsubscribe_domain = (json \ "default_unsubscribe_domain").as[String],
//        rep_tracking_host_id = (json \ "rep_tracking_host_id").as[Int],
//        tracking_domain_host = (json \ "tracking_domain_host").asOpt[String],
//        custom_tracking_domain = (json \ "custom_tracking_domain").asOpt[String],
//        custom_tracking_cname_value = (json \ "custom_tracking_cname_value").asOpt[String],
//        custom_tracking_domain_is_verified = (json \ "custom_tracking_domain_is_verified").asOpt[Boolean],
//        custom_tracking_domain_is_ssl_enabled = (json \ "custom_tracking_domain_is_ssl_enabled").asOpt[Boolean],
//
//        rep_mail_server_id = (json \ "rep_mail_server_id").as[Int],
//        rep_mail_server_public_ip = (json \ "rep_mail_server_public_ip").as[String],
//        rep_mail_server_host = (json \ "rep_mail_server_host").as[String],
//        rep_mail_server_reverse_dns = (json \ "rep_mail_server_reverse_dns").asOpt[String],
//
//        min_delay_seconds = (json \ "min_delay_seconds").as[Int],
//        max_delay_seconds = (json \ "max_delay_seconds").as[Int],
//
//        email_used_in_campaign = (json\ "email_used_in_campaign").as[Boolean]
//      )
//    } match {
//      case Failure(e) => JsError(e.getMessage)
//      case Success(emailSetting) => JsSuccess(emailSetting)
//    }
//
//  }

  val API_BASED_EMAILSERVICEPROVIDERS = Seq(
    EmailServiceProvider.GMAIL_ALIAS,
    EmailServiceProvider.GMAIL_API,
    EmailServiceProvider.OUTLOOK_API,
    EmailServiceProvider.SENDGRID,
    EmailServiceProvider.MAILGUN
  )


  def getEmailSendReceiveOauthStatus(e: EmailSetting): EmailSendReceiveOauthStatus = {
    val oauth2Enabled = e.oauth2_access_token.isDefined

    val canSend = oauth2Enabled ||
      // the following check covers OTHERS / EXCHANGE / AWS SES
      (e.smtp_password.isDefined && e.smtp_username.isDefined && e.smtp_host.isDefined && e.smtp_port.isDefined
        ) ||
      (e.service_provider == EmailServiceProvider.MAILGUN && e.email_domain.isDefined && e.api_key.isDefined && e.mailgun_region.isDefined) ||
      (e.service_provider == EmailServiceProvider.SENDGRID && e.api_key.isDefined)



    //    println(s"\n\ngetEmailSendReceiveOauthStatus : $canSend :: $e\n\n")

    val canReceive = (oauth2Enabled && e.service_provider != EmailServiceProvider.GMAIL_ALIAS) || (e.imap_password.isDefined && e.imap_username.isDefined && e.imap_host.isDefined && e.imap_port.isDefined
      )
    //step1: case class

    EmailSendReceiveOauthStatus(
      can_send = canSend,
      can_receive = canReceive,
      oauth2_enabled = oauth2Enabled)
  }


  def getImapEmailSetting(emailSetting: EmailSetting) = Try {
    ImapEmailSetting(
      imap_host = emailSetting.imap_host.get.trim,
      imap_port = emailSetting.imap_port.get,
      imap_username = emailSetting.imap_username.get.trim,
      imap_password = emailSetting.imap_password.get.trim
    )
  }

  def getImapEmailSetting(emailSetting: EmailSettingForm) = Try {
    ImapEmailSetting(
      imap_host = emailSetting.imap_host.get.trim,
      imap_port = emailSetting.imap_port.get,
      imap_username = emailSetting.imap_username.get.trim,
      imap_password = emailSetting.imap_password.get.trim
    )
  }

  def getTestEmail(
    data: EmailSettingForm,
    accountEmail: String,
    bulkSender: Boolean,
    accountId: Long
  ) = {
    val subject = s"Test Email From ${CONSTANTS.APP_NAME}"
    val emailBody = s"Congrats! Your email settings (for sending emails from ${data.email}) are properly configured. Thanks!"
    EmailToBeSent(
      sender_email_settings_id = 0,

      to_emails = Seq(IEmailAddress(
        email = accountEmail,
        name = None // dont need for smartreach integration test email
      )),
      from_email = data.email,
      reply_to_email = None,
      reply_to_name = None,
      cc_emails = Seq(),
      bcc_emails = Seq(),
      from_name = Helpers.getSenderName(data),
      subject = subject,
      textBody = EmailHelperCommon.getTextBodyFromHtmlBody(emailBody),
      htmlBody = emailBody,
      message_id = None,
      in_reply_to_id = None,
      in_reply_to_references_header = None,
      in_reply_to_sent_at = None,
      references_header = None,
      //      baseBody = "",


      email_thread_id = None,
      gmail_msg_id = None,
      gmail_thread_id = None,

      outlook_msg_id = None,
      outlook_conversation_id = None,
      outlook_response_json = None,

      gmail_fbl = None,
      list_unsubscribe_header = None,
      hasCustomTrackingDomain = false,
      rep_smtp_reverse_dns_host = None

    )
  }



  def _checkIfBCCorCCError(emailsString: String): Boolean = {

    var hasError = false

    val emails = emailsString.split(",").toList.map(_.trim)
    val isInvalid = emails.exists(em => !EmailValidationService.validateEmailFormat(email = em))

    if (isInvalid) {

      hasError = true

    } else {

      Try {
        InternetAddress.parse(emailsString).asInstanceOf[Array[Address]]
      } match {

        case Failure(e) =>
          hasError = true

        case Success(myEmailList) =>

          hasError = false

      }

    }

    hasError
  }

  def validateEmailSettingInput(
                                  data: EmailSettingForm,
                                  isNew: Boolean,
                                  orgId: OrgId,
                                  increase_email_delay: Boolean,
                                  max_email_sending_quota_per_day: Int
                                )= Try {
    val bccError: Boolean = if (data.bcc_emails.isDefined && data.bcc_emails.get.trim.nonEmpty) {

      _checkIfBCCorCCError(emailsString = data.bcc_emails.get)

    } else false


    val ccError: Boolean = if (data.cc_emails.isDefined && data.cc_emails.get.trim.nonEmpty) {

      _checkIfBCCorCCError(emailsString = data.cc_emails.get)

    } else false

    val min_delay: Int = if(increase_email_delay) {
      api.AppConfig.EmailDelay.New.min_delay
    } else api.AppConfig.EmailDelay.Old.min_delay



    if (data.quota_per_day>max_email_sending_quota_per_day){
      throw new Exception(s"Maximum emails per day should not exceed $max_email_sending_quota_per_day")
    }
    else if (data.min_delay_seconds < min_delay) {

      throw new Exception(s"Minimum delay between two emails can not be less than $min_delay seconds")

    } else if (data.max_delay_seconds > 1000) {

      throw new Exception(s"Maximum delay between two emails can not be more than 1000 seconds")

//    } else if (data.max_delay_seconds < data.min_delay_seconds) {
//
//      throw new Exception("Max delay between minimum delay and maximum delay should be 5 seconds") //removing this as repeating condition

    } else if (data.min_delay_seconds >= data.max_delay_seconds) {

      throw new Exception("Minimum delay cannot be more than maximum delay")

    } else if (data.max_delay_seconds - data.min_delay_seconds < 5) {

      throw new Exception("Minimum difference between min delay and max delay should be 5 seconds")

    } else if (bccError) {

      throw new Exception("Invalid BCC Emails")

    } else if (ccError) {

      throw new Exception("Invalid CC Emails")

    } else if (data.first_name.isEmpty && data.service_provider!=EmailServiceProvider.OUTLOOK_API) {

      throw new Exception("First name can't be empty")

    } else if (data.last_name.isEmpty && data.service_provider!=EmailServiceProvider.OUTLOOK_API) {

      throw new Exception("Last name can't be empty")

    } else true

  }

    def validateBulkEmailSettingsUpdate(
                                         quotaPerDayOpt: Option[Int],
                                         minDelaySecondsOpt: Option[Int],
                                         maxDelaySecondsOpt: Option[Int],
                                         max_email_sending_quota_per_day: Int,
                                         increase_email_delay: Boolean
                                       ): Try[Boolean] = {
        // Validate quota per day if provided
        if (quotaPerDayOpt.exists(_ > max_email_sending_quota_per_day)) {
            Failure(new Exception(s"Maximum emails per day should not exceed $max_email_sending_quota_per_day"))
        }
        // Validate min delay if provided
        else if (minDelaySecondsOpt.exists(min => {
            val minDelay = if (increase_email_delay) {
                api.AppConfig.EmailDelay.New.min_delay
            } else api.AppConfig.EmailDelay.Old.min_delay
            min < minDelay
        })) {
            val minDelay = if (increase_email_delay) {
                api.AppConfig.EmailDelay.New.min_delay
            } else api.AppConfig.EmailDelay.Old.min_delay
            Failure(new Exception(s"Minimum delay between two emails can not be less than $minDelay seconds"))
        }
        // Validate max delay if provided
        else if (maxDelaySecondsOpt.exists(_ > 1000)) {
            Failure(new Exception(s"Maximum delay between two emails can not be more than 1000 seconds"))
        }
        // Validate min and max delay relationship if both are provided
        else if (minDelaySecondsOpt.isDefined && maxDelaySecondsOpt.isDefined &&
          minDelaySecondsOpt.get >= maxDelaySecondsOpt.get) {
            Failure(new Exception("Minimum delay cannot be more than maximum delay"))
        }
        else if (minDelaySecondsOpt.isDefined && maxDelaySecondsOpt.isDefined &&
          maxDelaySecondsOpt.get - minDelaySecondsOpt.get < 5) {
            Failure(new Exception("Minimum difference between min delay and max delay should be 5 seconds"))
        }
        else {
            Success(true)
        }
    }

  def checkIsDomainAllowedIfEmailAndUsernameDoesNotMatch(
    smtp_username: Option[String],
    smtp_host: Option[String],
    imap_username: Option[String],
    imap_host: Option[String],
    canSend: Boolean,
    canReceive: Boolean,
  ): Boolean = {

    // host check
    val allowedEmailAndUsernameNonMatchingDomains_domain_in_host: Set[String] =
      AppConfig.allowedEmailAndUsernameNonMatchingDomains_domain_in_host

    val smtp_host_trimmed_lower: Option[String] = smtp_host.map(_.trim.toLowerCase)

    val smtpHostHasAllowedDomain: Boolean =
      smtp_host_trimmed_lower.exists { sh =>

        allowedEmailAndUsernameNonMatchingDomains_domain_in_host
          .exists(sh.contains)

      }

    val imap_host_trimmed_lower: Option[String] = imap_host.map(_.trim.toLowerCase)

    val imapHostHasAllowedDomain: Boolean =
      imap_host_trimmed_lower.exists { ih =>

        allowedEmailAndUsernameNonMatchingDomains_domain_in_host
          .exists(ih.contains)

      }


    // username check
    val allowedEmailAndUsernameNonMatchingDomains_domain_in_username: Set[String] =
      AppConfig.allowedEmailAndUsernameNonMatchingDomains_domain_in_username

    val smtp_username_trimmed_lower: Option[String] = smtp_username.map(_.trim.toLowerCase)

    val smtpUsernameHasAllowedDomain: Boolean =
      smtp_username_trimmed_lower.exists { su =>

        allowedEmailAndUsernameNonMatchingDomains_domain_in_username
          .exists(su.contains)

      }

    val imap_username_trimmed_lower: Option[String] = imap_username.map(_.trim.toLowerCase)

    val imapUsernameHasAllowedDomain: Boolean =
      imap_username_trimmed_lower.exists { iu =>

        allowedEmailAndUsernameNonMatchingDomains_domain_in_username
          .exists(iu.contains)

      }


    if (canSend && canReceive) {

      (smtpUsernameHasAllowedDomain && imapUsernameHasAllowedDomain) ||
        (smtpHostHasAllowedDomain && imapHostHasAllowedDomain)

    } else if (canSend && !canReceive) {

      smtpUsernameHasAllowedDomain || smtpHostHasAllowedDomain

    } else if (!canSend && canReceive) {

      imapUsernameHasAllowedDomain || imapHostHasAllowedDomain

    } else {

      false

    }

  }

  def validateBasicSettingInput(
                                 data: EmailSettingForm,
                                 isNew: Boolean,
                                 orgId: OrgId
                               )= Try {

    if (!data.can_send && !data.can_receive) {

      throw new Exception("Either can_send or can_receive should be true")
    }
    else {

      data.service_provider match {

        case EmailServiceProvider.GMAIL_API | EmailServiceProvider.GMAIL_ALIAS | EmailServiceProvider.OUTLOOK_API =>

          if (isNew) {
            throw new Exception("Gmail or outlook api can be integrated only via OAuth")
          }


        case EmailServiceProvider.EXCHANGE |
             EmailServiceProvider.GMAIL_ASP |
             EmailServiceProvider.NAMECHEAP |
             EmailServiceProvider.REDIFF_MAIL |
             EmailServiceProvider.OTHER =>

          val isDomainAllowedIfEmailAndUsernameDoesNotMatch: Boolean =
            checkIsDomainAllowedIfEmailAndUsernameDoesNotMatch(
              smtp_username = data.smtp_username,
              imap_username = data.imap_username,
              canSend = data.can_send,
              canReceive = data.can_receive,
              imap_host = data.imap_host,
              smtp_host = data.smtp_host,
            )

          if (data.can_receive &&

            (data.imap_host.isEmpty ||
              data.imap_port.isEmpty ||
              data.imap_username.isEmpty ||
              data.imap_password.isEmpty
              )) {

            throw new Exception("Please send all IMAP fields")

          } else if (data.can_send &&

            (data.smtp_host.isEmpty ||
              data.smtp_port.isEmpty ||
              data.smtp_username.isEmpty ||
              data.smtp_password.isEmpty
              )) {


            throw new Exception("Please send all SMTP fields")

          } else if (
            !AppConfig.ignoreSmtpImapSameEmailCheckForOrgIds.contains(orgId.id) &&
              data.can_send &&
              data.can_receive &&
              data.imap_username.nonEmpty &&
              data.smtp_username.nonEmpty &&
              (data.imap_username.get.trim.toLowerCase != data.smtp_username.get.trim.toLowerCase)
          ) {

            throw new Exception("IMAP Username and SMTP Username need to be the same while connecting the email-account. Please connect them separately if you need both the email-accounts.")

          } else if (
            !AppConfig.ignoreSmtpImapSameEmailCheckForOrgIds.contains(orgId.id) &&
              data.can_receive &&
              data.imap_username.nonEmpty &&
              (!isDomainAllowedIfEmailAndUsernameDoesNotMatch && data.imap_username.get.trim.toLowerCase != data.email.trim.toLowerCase)
          ) {

            throw new Exception("IMAP Username should be same as the Email Address. If you have any questions, please contact our support.")

          } else if (
            !AppConfig.ignoreSmtpImapSameEmailCheckForOrgIds.contains(orgId.id) &&
              data.can_send &&
              data.smtp_username.nonEmpty &&
              (!isDomainAllowedIfEmailAndUsernameDoesNotMatch && data.smtp_username.get.trim.toLowerCase != data.email.trim.toLowerCase)
          ) {

            throw new Exception("SMTP Username should be same as the Email Address. If you have any questions, please contact our support.")

          } else {

            true

          }

        case EmailServiceProvider.MAILGUN =>

          if (data.can_receive) {

            throw new Exception("Can't receive replies via Mailgun")

          } else if (!data.can_send) {

            throw new Exception("can_send should be true")

          } else if (data.api_key.isEmpty) {

            throw new Exception("Please send Mailgun api key")

          } else if (data.email_domain.isEmpty) {

            throw new Exception("Please send Mailgun domain")

          } else if (data.mailgun_region.isEmpty) {

            throw new Exception("Please send Mailgun region")

          } else {

            true

          }

        case EmailServiceProvider.AWS_SES =>

          if (data.can_receive) {

            throw new Exception("Can't receive replies via AWS SES")

          } else if (data.can_send &&

            (data.smtp_host.isEmpty ||
              data.smtp_port.isEmpty ||
              data.smtp_username.isEmpty ||
              data.smtp_password.isEmpty
              )) {

            throw new Exception("Please send all AWS SES SMTP fields")

          } else {

            true

          }


        case EmailServiceProvider.SENDGRID =>

          if (data.can_receive) {

            throw new Exception("Can't receive replies via SendGrid")

          } else if (data.can_send &&

            ( data.api_key.isEmpty)) {

            throw new Exception("Please send SendGrid api key")

          } else {

            true

          }
      }

    }
  }

  //  given reads = Json.reads[EmailSetting]


  // ignore oauth2 credentials while returning account as JSON

  given writes: Writes[EmailSetting] = new Writes[EmailSetting] {
    def writes(e: EmailSetting): JsValue = {
      /* We are passing the credentials as None as passing the passwords as plain text might be a security issue.
            Therefore - imap_password, smtp_password, and api_key are sent as none.
        */

      val emailSendReceiveOauthStatus = getEmailSendReceiveOauthStatus(e)
      val error = if (
        e.error.isDefined &&
          (e.paused_till.isEmpty || (e.paused_till.isDefined && e.paused_till.get.isAfterNow)) &&
          e.service_provider != EmailServiceProvider.GMAIL_ALIAS
      ) {

        e.error

      } else {
        None
      }

      val canAutoStartWarmup = WarmupHeroEmailSettingResponse.canAutoConnectInWarmupHero(esp = e.service_provider)

      val mailgunRegion = e.mailgun_region.map(_.toString)

      // Should we add Writes/Reads for this ??
      Json.obj(
        "id" -> e.id.map(_.emailSettingId), // FIXME VALUECLASS
        "uuid" -> e.uuid.map(_.uuid),
        "owner_id" -> e.owner_id.id, // FIXME VALUECLASS
        "team_id" -> e.team_id.id, // FIXME VALUECLASS

        "can_auto_start_warmup" -> canAutoStartWarmup,

        "email" -> e.email,
        "email_address_host" -> e.email_address_host,

        "service_provider" -> e.service_provider.toString,

        "owner_name" -> e.owner_name,
        "sender_name" -> e.sender_name, // TODO remove this
        "first_name" -> e.first_name,
        "last_name" -> e.last_name,

        "cc_emails" -> e.cc_emails,
        "bcc_emails" -> e.bcc_emails,

        "smtp_username" -> e.smtp_username,
        "smtp_password" -> None , //e.smtp_password,
        "smtp_host" -> e.smtp_host,
        "smtp_port" -> e.smtp_port,

        "imap_username" -> e.imap_username,
        "imap_password" -> None , //e.imap_password,
        "imap_host" -> e.imap_host,
        "imap_port" -> e.imap_port,

        "email_domain" -> e.email_domain,
        "api_key" -> None , //e.api_key,
        "mailgun_region" -> mailgunRegion,

        "quota_per_day" -> e.quota_per_day,
        "min_delay_seconds" -> e.min_delay_seconds,
        "max_delay_seconds" -> e.max_delay_seconds,

        "created_at" -> e.created_at,

        "can_send" -> emailSendReceiveOauthStatus.can_send,
        "can_receive" -> emailSendReceiveOauthStatus.can_receive,
        "oauth2_enabled" -> emailSendReceiveOauthStatus.oauth2_enabled,

        "error" -> error,

        "tracking_domain_host" -> e.tracking_domain_host,
        "dns_host" -> e.custom_tracking_domain,
        "custom_tracking_cname_value" -> e.custom_tracking_cname_value,
        "custom_tracking_domain_is_verified" -> e.custom_tracking_domain_is_verified,
        "custom_tracking_domain_is_ssl_enabled" -> e.custom_tracking_domain_is_ssl_enabled,

        "signature" -> e.signature,
          "tag" -> e.tag,
        "campaign_use_status_for_email_setting" -> e.campaign_use_status_for_email_setting.toString,
        "rms_ip" -> {
          if (e.show_rms_ip_in_frontend) Some(e.rep_mail_server_public_ip) else None
        }


      )
    }
  }
}


case class EmailSettingForScheduling(
  id: Int,
  account_id: Long,
  team_id: Long,
  org_id: Long,

  current_prospect_sent_count_org: Int,
  // current_prospect_sent_count_email: Int,
  is_agency: Boolean,
  plan_name: String,
  total_sending_email_accounts: Int,
  current_cycle_started_at: DateTime,
  org_warning_code: Option[AccountWarningCodeType],
  org_warning_at: Option[DateTime],

  sender_email: String


)

case class InboxV3Folder(
                          text_id: String,
                          email_setting_id: Option[Long],
                          inbox_name: String,
                          unread: Long,
                          team_inbox_id: Option[Long] = None,
                          email: Option[String] = None,
                          initial_sync_required: Boolean,
                          initial_sync_data: Option[InitialEmailSyncData]
                        )
object InboxV3Folder {
  given writes: OWrites[InboxV3Folder] = Json.writes[InboxV3Folder]

//  def fromDbForEmailSetting(rs: WrappedResultSet): InboxV3Folder = {
//
//    val emailSettingId = rs.long("email_setting_id")
//
//    InboxV3Folder(
//      text_id = s"${CONSTANTS.TEXT_ID_PREPEND.EMAIL_ACCOUNT}$emailSettingId",
//      email_setting_id = Some(emailSettingId),
//      inbox_name = rs.string("inbox_name"),
//      unread = rs.long("unread"),
//      team_inbox_id = rs.longOpt("team_inbox_id"),
//      email = rs.stringOpt("email")
//    )
//  }
}

case class BlackListedCustomerDomainDetails(
                                             emailDomain: BlacklistCheckType.Domain,
                                             logInEmail: String,
                                             orgId:OrgId,
                                             host: String,
                                             public_ip: BlacklistCheckType.IP,
                                             domain_blacklist_failure_description: String,
                                             domain_blacklist_full_result: String,
                                             domain_blacklist_status: BlacklistCheckStatus,
                                             ip_blacklist_failure_description: String,
                                             ip_blacklist_full_result: String,
                                             ip_blacklist_status: BlacklistCheckStatus,
                                             overall_blacklist_status: BlacklistCheckStatus,
                                             host_url: String,
                                           )

object  BlackListedCustomerDomainDetails{
  def fromDb(rs:WrappedResultSet): BlackListedCustomerDomainDetails = BlackListedCustomerDomainDetails(
    emailDomain = BlacklistCheckType.Domain(domain = rs.string("email_address_host")),
    logInEmail = rs.string("email"),
    orgId = OrgId(rs.long("org_id")),
    host = rs.string("host"),
    public_ip = BlacklistCheckType.IP(ip = rs.string("public_ip")),
    domain_blacklist_failure_description = rs.string("domain_blacklist_failure_description"),
    domain_blacklist_full_result = rs.string("domain_blacklist_full_result"),
    domain_blacklist_status = BlacklistCheckStatus.fromKey(rs.string("domain_blacklist_status")).get,
    ip_blacklist_failure_description = rs.string("ip_blacklist_failure_description"),
    ip_blacklist_full_result = rs.string("ip_blacklist_full_result"),
    ip_blacklist_status = BlacklistCheckStatus.fromKey(rs.string("ip_blacklist_status")).get,
    overall_blacklist_status = BlacklistCheckStatus.fromKey(rs.string("overall_blacklist_status")).get,
    host_url = rs.string("host_url")
  )
}


case class EmailAddressHost(emailAddressHost: String) extends  AnyVal

class EmailSettingDAO(
                     srUuidUtils: SrUuidUtils
) extends Logging
  with ChannelDataDaoTrait[EmailSettingCreateEmailSchedule] {


  implicit val session: AutoSession.type = AutoSession

  def __findEmailSettingsSQL(whereClause: SQLSyntax): Seq[EmailSetting] = blocking {

    // should be on master DB, because we may call this immediately after inserts and updates
    DB readOnly { implicit session =>

      sql""" SELECT

       t.org_id,
       t.reply_handling as reply_handling,
       (case when rth.subdomain_based then concat(org.tracking_subdomain_key, '.', rth.host_url) else rth.host_url end) as default_tracking_domain,
       (case when rth.subdomain_based then concat(org.tracking_subdomain_key, '.', rth.unsubscribe_host_url) else rth.unsubscribe_host_url end) as default_unsubscribe_domain,
       rth.id as rep_tracking_host_id,
       rms.id as rep_mail_server_id,
       rms.public_ip AS rep_mail_server_public_ip,
       rms.host AS rep_mail_server_host,
       rms.reverse_dns AS rep_mail_server_reverse_dns,

       (CASE WHEN org.force_via_gmail_smtp IS TRUE THEN TRUE
          WHEN es.via_gmail_smtp IS TRUE THEN TRUE
          ELSE FALSE
          END) AS via_gmail_smtp_overall,

        es.*,
        (SELECT STRING_AGG(c.status, ',') from campaigns c
               inner join campaign_email_settings ces on c.id = ces.campaign_id and ces.team_id = c.team_id
               where
                ces.sender_email_setting_id = es.id
             ) AS campaign_status_for_campaigns,
       ctd.domain_pointer_name as ctd_domain_pointer_name,
       ctd.domain_pointer_value as ctd_domain_pointer_value,
       ctd.is_verified as ctd_is_verified,
       ctd.is_ssl_enabled as ctd_is_ssl_enabled,
       a.uuid as account_uuid,
       t.uuid as team_uuid,

       org.metadata,

       dhc.is_in_spam_blacklist as is_in_spam_blacklist,

       CONCAT(a.first_name, ' ', a.last_name) as owner_name

        FROM email_settings es
        JOIN teams t ON t.id = es.team_id
        JOIN accounts a ON es.account_id = a.id
        JOIN organizations org ON org.id = a.org_id
        LEFT JOIN custom_tracking_domain_records ctd ON lower(ctd.domain_host) = lower(es.email_address_host)
        JOIN rep_tracking_hosts rth ON rth.id = (CASE WHEN es.rep_tracking_host_id IS NOT NULL THEN es.rep_tracking_host_id ELSE org.rep_tracking_host_id END)
        JOIN rep_mail_servers rms ON rms.id = (case when es.rep_mail_server_id IS NOT NULL THEN es.rep_mail_server_id ELSE org.rep_mail_server_id END)
        LEFT JOIN domain_health_checks dhc ON dhc.domain = es.email_address_host

        $whereClause

       """
        .map(rs => {

          val isUnderReview = rs.boolean("is_under_review")

          val isUnderSpamBlacklist = rs.booleanOpt("is_in_spam_blacklist").getOrElse(false)

          val error = if (isUnderReview) {
            Some("Your email account is under manual review. Please contact support.")
          } else if (isUnderSpamBlacklist) {
            Some("Your sending email domain is found in a global spam blacklist. Please check the status by going to Settings -> Team Settings -> Domain Health.")
          } else {
            rs.stringOpt("error")
          }

          val sender_name_db = rs.string("sender_name").trim
          val first_name_db = rs.string("first_name").trim
          val last_name_db = rs.string("last_name").trim

          val senderName = if (sender_name_db.nonEmpty) sender_name_db
          else if (first_name_db.nonEmpty && last_name_db.nonEmpty) first_name_db + " " + last_name_db
          else first_name_db

          val campaign_status_for_campaigns = rs.stringOpt("campaign_status_for_campaigns").map { list =>
            list.split(",")
              .toList
              .map(cs =>
                CampaignStatus.fromKey(cs)
                  .get
              )
          }.getOrElse(List())


          val campaign_use_status_for_email_setting = {
            if (campaign_status_for_campaigns.contains(CampaignStatus.RUNNING) || campaign_status_for_campaigns.contains(CampaignStatus.ON_HOLD))
              CampaignUseStatusForEmailSetting.HasRunningCampaign
            else if (campaign_status_for_campaigns.nonEmpty) CampaignUseStatusForEmailSetting.NoRunningCampaign
            else CampaignUseStatusForEmailSetting.IsNotAssignedToAnyCampaign
          }
          val show_rms_ip_in_frontend = Json.parse(rs.any("metadata").asInstanceOf[PGobject].getValue)
            .validate[OrgMetadata]
            .get
            .show_rms_ip_in_frontend
            .getOrElse(false) //DO NOT USE THE ORG METADATA for other flags, since we are not sending it to enrich method

          EmailSetting(
            id = rs.intOpt("id").map(e => EmailSettingId(emailSettingId = e)),
            uuid = rs.stringOpt("uuid").map(e => EmailSettingUuid(uuid = e)),
            org_id = OrgId(id = rs.long("org_id")),
            owner_id = AccountId(id = rs.int("account_id")),
            owner_uuid = AccountUuid(uuid = rs.string("account_uuid")),
            team_id = TeamId(id = rs.long("team_id")),
            team_uuid = TeamUuid(uuid = rs.string("team_uuid")),
            message_id_suffix = rs.string("message_id_suffix"),

            email = rs.string("email"),
            email_address_host = rs.string("email_address_host"),

            service_provider = EmailServiceProvider.fromKey(rs.string("service_provider")).get,
            domain_provider = rs.stringOpt("domain_provider").flatMap { str =>
              EmailProvidedBy.fromString(str).toOption
            },
            via_gmail_smtp = rs.booleanOpt("via_gmail_smtp_overall"),

            owner_name = rs.string("owner_name"),
            sender_name = senderName,
            first_name = first_name_db,
            last_name = last_name_db,

            cc_emails = rs.stringOpt("cc_emails"),
            bcc_emails = rs.stringOpt("bcc_emails"),

            smtp_username = rs.stringOpt("smtp_username"),
            smtp_password = rs.stringOpt("smtp_password_enc").map(EncryptionHelpers.decryptEmailSettingCredential),
            smtp_host = rs.stringOpt("smtp_host"),
            smtp_port = rs.intOpt("smtp_port"),

            imap_username = rs.stringOpt("imap_username"),
            imap_password = rs.stringOpt("imap_password_enc").map(EncryptionHelpers.decryptEmailSettingCredential),
            imap_host = rs.stringOpt("imap_host"),
            imap_port = rs.intOpt("imap_port"),

            oauth2_access_token = rs.stringOpt("oauth2_access_token_enc").map(EncryptionHelpers.decryptEmailSettingCredential),
            oauth2_refresh_token = rs.stringOpt("oauth2_refresh_token_enc").map(EncryptionHelpers.decryptEmailSettingCredential),
            oauth2_token_type = rs.stringOpt("oauth2_token_type"),
            oauth2_token_expires_in = rs.intOpt("oauth2_token_expires_in"),
            oauth2_access_token_expires_at = rs.jodaDateTimeOpt("oauth2_access_token_expires_at"),

            email_domain = rs.stringOpt("email_domain"),
            api_key = rs.stringOpt("api_key_enc").map(EncryptionHelpers.decryptEmailSettingCredential),
            mailgun_region = rs.stringOpt("mailgun_region").map(mr => ESPMailgunRegion.withName(mr)),

            quota_per_day = rs.int("quota_per_day"),
            reply_handling = ReplyHandling.withName(rs.string("reply_handling")),
            last_read_for_replies = rs.jodaDateTimeOpt("last_read_for_replies"),
            latest_email_scheduled_at = rs.jodaDateTimeOpt("latest_email_scheduled_at"),

            error = error,
            error_reported_at = rs.jodaDateTimeOpt("error_reported_at"),
            paused_till = rs.jodaDateTimeOpt("paused_till"),

            signature = rs.string("signature"),

            created_at = rs.jodaDateTimeOpt("created_at"),

            current_prospect_sent_count_email = rs.int("current_prospect_sent_count_email"),

            default_tracking_domain = rs.string("default_tracking_domain"),
            default_unsubscribe_domain = rs.string("default_unsubscribe_domain"),
            rep_tracking_host_id = rs.int("rep_tracking_host_id"),
            tracking_domain_host = rs.stringOpt("tracking_domain_host"),
            custom_tracking_domain = rs.stringOpt("ctd_domain_pointer_name"),
            custom_tracking_cname_value = rs.stringOpt("ctd_domain_pointer_value"),
            custom_tracking_domain_is_verified = rs.booleanOpt("ctd_is_verified"),
            custom_tracking_domain_is_ssl_enabled = rs.booleanOpt("ctd_is_ssl_enabled"),

            rep_mail_server_id = rs.int("rep_mail_server_id"),
            rep_mail_server_public_ip = rs.string("rep_mail_server_public_ip").trim,
            rep_mail_server_host = rs.string("rep_mail_server_host"),
            rep_mail_server_reverse_dns = rs.stringOpt("rep_mail_server_reverse_dns"),

            min_delay_seconds = rs.int("min_delay_seconds"),
            max_delay_seconds = rs.int("max_delay_seconds"),

            tag = rs.stringOpt("email_tag"),

            campaign_use_status_for_email_setting = campaign_use_status_for_email_setting,
            show_rms_ip_in_frontend = show_rms_ip_in_frontend
          )
        })
        .list
        .apply()
    }
  }


  def findAll(
               accountIds: Seq[Long],
               teamIds: Seq[Long],
               queryTimeline: Option[InferredQueryTimeline] = None,
               limit: Int = 100
             )(using logger: SRLogger): Try[Seq[EmailSetting]] = Try {

    val emailSettingApiWhereClauseAndOrderBy: SQLSyntax = if (queryTimeline.isDefined) {
      queryTimeline.get match {
        case InferredQueryTimeline.Range.Before(dateTime) =>
          sqls" AND date_trunc('milliseconds', es.created_at) AT TIME ZONE 'UTC' < ${dateTime} AT TIME ZONE 'UTC' ORDER BY es.created_at desc LIMIT $limit"

        case InferredQueryTimeline.Range.After(dateTime) =>
          sqls" AND date_trunc('milliseconds', es.created_at) AT TIME ZONE 'UTC' > ${dateTime} AT TIME ZONE 'UTC' ORDER BY es.created_at asc LIMIT $limit"

        case InferredQueryTimeline.Exact(exactTimeThreadOrTask) =>

          if (teamIds.isEmpty) {
            logger.shouldNeverHappen(
              msg = s"Not found teamIds - EmailSettingDAO.findAll. accountIds: $accountIds :: exactTimeThreadOrTask: $exactTimeThreadOrTask"
            )
          }

          sqls" AND date_trunc('milliseconds', es.created_at) AT TIME ZONE 'UTC' = (select date_trunc('milliseconds', created_at) from email_settings where id = ${exactTimeThreadOrTask.id.toLong} and team_id IN ${SQLUtils.generateSQLValuesClause(arr = teamIds)}) AT TIME ZONE 'UTC'"

      }
    } else sqls" ORDER BY LOWER(es.email) ASC "


    if (teamIds.nonEmpty) {

      __findEmailSettingsSQL(whereClause = sqls" WHERE es.status = ${EmailSettingStatus.Active.toString} AND es.account_id IN ($accountIds) and es.team_id IN ${SQLUtils.generateSQLValuesClause(arr = teamIds)} ${emailSettingApiWhereClauseAndOrderBy}")

    } else {

      __findEmailSettingsSQL(whereClause = sqls" WHERE es.status = ${EmailSettingStatus.Active.toString} AND es.account_id IN ($accountIds) ${emailSettingApiWhereClauseAndOrderBy}")

    }

  }


  // FIXME: there can be multiple email-settings in the same team (different service providers)
  def findByEmailAndTeamId(email: String, teamId: Long): Option[EmailSetting] = DB readOnly { implicit session =>

    __findEmailSettingsSQL(whereClause = sqls" where es.team_id = $teamId and LOWER(es.email) = LOWER($email) and  es.status = ${EmailSettingStatus.Active.toString} ")
      .headOption

  }

  def findAllByEmailAndTeamId(email: String, teamId: Long): Seq[EmailSetting] = DB readOnly { implicit session =>

    __findEmailSettingsSQL(whereClause = sqls" where es.team_id = $teamId and LOWER(es.email) = LOWER(${email.trim}) and  es.status = ${EmailSettingStatus.Active.toString}")

  }

  //for campaign inbox
  def ********************************(teamId: TeamId, permittedAccountIds: Seq[Long]): Seq[EmailSetting] = DB readOnly { implicit session =>

    __findEmailSettingsSQL(whereClause = sqls" where es.team_id = ${teamId.id} and es.account_id in ${SQLUtils.generateSQLValuesClause(permittedAccountIds)} ")
  }

  def dkimRecordFromDb(rs: WrappedResultSet): DKIMRecord = {
    DKIMRecord(
      domain = rs.string("domain"),
      selector = rs.string("dkim_selector"),
      record = rs.string("dkim_record"),
      public_key = rs.string("public_key"),
      private_key = EncryptionHelpers.decryptDkimRecordsCredential(rs.string("private_key_enc")),
      active = rs.boolean("active")
    )
  }


  // TODO: remove this, not used
  //  def findOneForCreatingSpamTests(): Option[EmailSetting] = {
  //    val esID = DB readOnly { implicit session =>
  //      sql"""
  //
  //      SELECT DISTINCT ON (es.id)
  //      es.id
  //      FROM email_settings es
  //      INNER JOIN campaign_email_settings ces on ces.campaign_id = es.id
  //      INNER JOIN campaigns c ON ces.campaign_id = c.id
  //      INNER JOIN campaigns_prospects cp ON (cp.campaign_id = c.id AND cp.active)
  //      LEFT JOIN spam_tests st ON st.email_settings_id = es.id
  //      WHERE c.status = 'running'
  //      AND c.head_step_id IS NOT NULL
  //        AND (st.created_at IS NULL OR st.created_at < now() - interval '2 weeks')
  //      AND es.created_at < now() - interval '3 hours'
  //      ORDER BY es.id, st.created_at DESC
  //      LIMIT 1;
  //
  //      """
  //        .map(_.long("id"))
  //        .single
  //        .apply()
  //
  //    }
  //
  //    esID.flatMap(id => find(id = id))
  //  }


  /*
  FIXME:
     AND (
      sender.latest_email_scheduled_at > now() - interval '30 days'
      OR (sender.latest_email_scheduled_at IS NULL AND sender.created_at > now() - interval '15 days')

      -- if an old campaign is recently restarted, it should check for replies
      OR c.status_changed_at > now() - interval '15 day'
    )

    prevents older campaigns from running

    instead pause older campaigns, so users come back and restart them
   */
  def fetchInboxesForReplyTrackingQueue(
                                         logger: SRLogger
                                       ): Try[Seq[(Long, EmailServiceProvider, Option[EmailProvidedBy])]] = blocking {

    DBUtils.autoCommit(
      logger = logger.appendLogRequestId("fetchInboxesForReplyTrackingQueue")
    ) { implicit session =>

      val pushedToQueueForReplyTrackingInterval = SQLSyntax.createUnsafely(AppConfig.SchedulerConfig.pushedToQueueForReplyTrackingIntervalInMinutes.toString)

      sql"""

    UPDATE email_settings all_rows

    SET
    in_queue_for_reply_tracking = TRUE,
    pushed_to_queue_for_reply_tracking_at = now()

    FROM (
      SELECT

        -- in gmail_alias, bounced emails are received in the primary inbox
        (CASE WHEN receiver.service_provider = 'gmail_alias' THEN receiver.alias_parent_id ELSE receiver.id END )  as id


    -- sender.message_id_suffix as sender_message_id_suffix

    FROM email_settings receiver
    INNER JOIN campaign_email_settings ces on (
    ces.receiver_email_setting_id = receiver.id OR
     ces.sender_email_setting_id = receiver.id)
     AND receiver.team_id = ces.team_id
    INNER JOIN campaigns c ON (
      ces.campaign_id = c.id
      and c.team_id = ces.team_id
      AND
      (
        receiver.id = ces.receiver_email_setting_id
      OR  (
        receiver.id = ces.sender_email_setting_id

        -- if sending email does not give us imap access, we can not check for bounced emails
        AND (

          receiver.service_provider = 'gmailapi'

          OR receiver.service_provider = 'gmail'

          OR (
            receiver.imap_host is not null
            AND trim(receiver.imap_host) != ''
            AND receiver.imap_password_enc is not null
            AND trim(receiver.imap_password_enc) != ''
            AND receiver.imap_port is not null
            AND receiver.imap_username is not null
            AND trim(receiver.imap_username) != ''

          )

        )

      )
      )
    )
    -- INNER JOIN email_settings sender ON ces.sender_email_setting_id = sender.id
    INNER JOIN accounts a ON a.id = receiver.account_id
    INNER JOIN organizations o ON o.id = a.org_id
    INNER JOIN teams t on t.id = receiver.team_id
    WHERE

    o.plan_type != ${PlanType.INACTIVE.toString}

    AND t.active

    AND a.active

    AND receiver.status = ${EmailSettingStatus.Active.toString}

    AND (receiver.in_queue_for_reply_tracking = FALSE OR receiver.pushed_to_queue_for_reply_tracking_at < now() - interval '$pushedToQueueForReplyTrackingInterval minutes')

  --  AND (
  --    sender.latest_email_scheduled_at > now() - interval '30 days'
  --    OR (sender.latest_email_scheduled_at IS NULL AND sender.created_at > now() - interval '15 days')

  --    -- if an old campaign is recently restarted, it should check for replies
  --    OR c.status_changed_at > now() - interval '15 day'
  --  )

    -- track replies for running campaigns, and campaigns that were paused in the last 7 days
    AND (c.status = ${CampaignStatus.RUNNING.toString} OR (c.status in (${CampaignStatus.STOPPED.toString}, ${CampaignStatus.ON_HOLD.toString}) AND c.status_changed_at > now() - interval '7 days'))
    AND (receiver.last_read_for_replies IS NULL OR receiver.last_read_for_replies < now() - interval '10 minutes')

    AND (receiver.paused_till IS NULL OR receiver.paused_till < now())

    AND NOT receiver.is_under_review

    AND (o.paused_till IS NULL OR o.paused_till < now())

    -- Update1: FOR UPDATE is not allowed with DISTINCT clause : ERROR
    -- Update2: dont comment the FOR UPDATE clause, its causing FATAL sharedlocks
    --     Ref for update2: https://stackoverflow.com/questions/********/postgresql-deadlock-from-simple-update-i-cant-get-the-cause

    FOR UPDATE SKIP LOCKED

    ) filtered_rows

    WHERE all_rows.id = filtered_rows.id

    RETURNING all_rows.id,  all_rows.service_provider, all_rows.domain_provider;
      """
        .map(rs => (
          rs.long("id"), 
          EmailServiceProvider.fromKey(rs.string("service_provider")).get, 
          rs.stringOpt("domain_provider").map(a => EmailProvidedBy.fromString(a).get)
        ))
        .list
        .apply()
        .distinct

    }
  }

  def fetchTeamInboxesForReplyTrackingQueue(
                                             logger: SRLogger
                                           ): Try[Seq[(Long, EmailServiceProvider, Option[EmailProvidedBy])]] = blocking {

    DBUtils.autoCommit(
      logger = logger.appendLogRequestId("fetchTeamInboxesForReplyTrackingQueue")
    ) { implicit session =>

      sql"""

    UPDATE email_settings all_rows

    SET
    in_queue_for_reply_tracking = TRUE,
    pushed_to_queue_for_reply_tracking_at = now()

    FROM (
      SELECT

        (CASE WHEN receiver.service_provider = 'gmail_alias' THEN receiver.alias_parent_id ELSE receiver.id END )  as id

    FROM email_settings receiver

    INNER JOIN accounts a ON a.id = receiver.account_id
    INNER JOIN organizations o ON o.id = a.org_id
    INNER JOIN teams t on t.id = receiver.team_id
    INNER JOIN team_inbox ti on receiver.id = ti.email_setting_id

    WHERE

    o.plan_type != ${PlanType.INACTIVE.toString}

    AND t.active

    AND a.active

    AND receiver.status = ${EmailSettingStatus.Active.toString}

    AND (receiver.in_queue_for_reply_tracking = FALSE OR receiver.pushed_to_queue_for_reply_tracking_at < now() - interval '10 minutes')

    AND (receiver.paused_till IS NULL OR receiver.paused_till < now())

    AND NOT receiver.is_under_review

    AND (o.paused_till IS NULL OR o.paused_till < now())

    AND (receiver.id = ti.email_setting_id AND o.id = ti.org_id)

    -- Update1: FOR UPDATE is not allowed with DISTINCT clause : ERROR
    -- Update2: dont comment the FOR UPDATE clause, its causing FATAL sharedlocks
    --     Ref for update2: https://stackoverflow.com/questions/********/postgresql-deadlock-from-simple-update-i-cant-get-the-cause

    FOR UPDATE SKIP LOCKED

    ) filtered_rows

    WHERE all_rows.id = filtered_rows.id

    RETURNING all_rows.id,  all_rows.service_provider, all_rows.domain_provider;
      """
        .map(rs => (
          rs.long("id"), 
          EmailServiceProvider.fromKey(rs.string("service_provider")).get,
          rs.stringOpt("domain_provider").map(a => EmailProvidedBy.fromString(a).get)
        ))
        .list
        .apply()
        .distinct

    }
  }


  def find(id: Long, email_status: EmailSettingStatus = EmailSettingStatus.Active): Option[EmailSetting] = {
    __findEmailSettingsSQL(whereClause = sqls" WHERE es.id = $id and  es.status = ${email_status.toString}")
      .headOption
  }

  def findEmailSettingStatus(id: EmailSettingId, teamId: TeamId): Try[EmailSettingStatusData] = Try {
    DB readOnly { implicit session =>
      sql"""
            SELECT es.email,es.status
            FROM email_settings es
            WHERE es.id = ${id.emailSettingId}
            AND es.team_id = ${teamId.id}
             """
        .map { rs =>
          EmailSettingStatusData(
            email = rs.string("email"),
            status = EmailSettingStatus.fromKey(rs.string("status")).get
          )

        }
        .single
        .apply()
        .get
    }
  }


  def find(id: EmailSettingId, teamId: TeamId): Option[EmailSetting] = {

    __findEmailSettingsSQL(whereClause = sqls" WHERE es.id = ${id.emailSettingId} AND es.team_id = ${teamId.id} and  es.status = ${EmailSettingStatus.Active.toString}")
      .headOption

  }

  def find(id: Long, accountIds: Seq[Long], teamId: Long): Option[EmailSetting] = {

    __findEmailSettingsSQL(whereClause = sqls" WHERE es.id = $id AND es.account_id IN ($accountIds) AND es.team_id = $teamId and  es.status = ${EmailSettingStatus.Active.toString}")
      .headOption

  }

  def find(senderEmailSettingIds: Seq[EmailSettingId], teamId: TeamId): Seq[EmailSetting] = {
    if (senderEmailSettingIds.isEmpty) {

      Seq()

    } else {

      __findEmailSettingsSQL(whereClause = sqls" WHERE es.id in (${senderEmailSettingIds.map(_.emailSettingId)}) AND es.team_id = ${teamId.id} and  es.status = ${EmailSettingStatus.Active.toString}")

    }
  }

  def find(ids: Seq[EmailSettingId], permittedAccountIds: Seq[Long], teamId: Long): Seq[EmailSetting] = {

    if (ids.isEmpty) Seq() else {

      __findEmailSettingsSQL(
        whereClause =
          sqls"""
                      WHERE es.id IN (${ids.map(_.emailSettingId)})
                      AND es.account_id IN ($permittedAccountIds)
                      AND es.team_id = $teamId
                      and  es.status = ${EmailSettingStatus.Active.toString}
                      """)
    }
  }

  def find(emailSettingUuids: Seq[EmailSettingUuid], teamId: TeamId): Try[Seq[EmailSetting]] = Try {

    if (emailSettingUuids.isEmpty) Seq()
    else {

      __findEmailSettingsSQL(
        whereClause =
          sqls"""
                WHERE es.uuid IN (${emailSettingUuids.map(_.uuid)})
                AND es.team_id = ${teamId.id}
                and  es.status = ${EmailSettingStatus.Active.toString}
              """
      )

    }

  }

  def find(
            uuid: EmailSettingUuid,
            orgId: OrgId,
          ): Try[Option[EmailSetting]] = Try {

    __findEmailSettingsSQL(
      whereClause =
        sqls"""
              WHERE es.uuid = ${uuid.uuid}
              AND es.status = ${EmailSettingStatus.Active.toString}
              AND org.id = ${orgId.id}
            """
    )
      .headOption

  }

  /* 10-mar-2025
  Fetch function to fetch the email settings for the sender worker cron monitoring

   */

  /*
  14 May 2025 :
   not including the emails with specific ids those ids are for emails which are been signed off
  es_ids: 26718,42169,43238,43226,34569
   */

  def findForSenderWorkerCheck(
          orgId: OrgId,
          accountId: AccountId,
          teamId: TeamId
          ):Try[Seq[EmailSetting]] = Try {

      __findEmailSettingsSQL(
          whereClause =
            sqls"""
                    WHERE es.status = ${EmailSettingStatus.Active.toString}
                    AND org.id = ${orgId.id}
                    AND es.account_id = ${accountId.id}
                    AND es.team_id = ${teamId.id}
                    AND es.id IN (
                        SELECT MIN(es2.id)
                        FROM email_settings es2
                        JOIN teams t2 ON t2.id = es2.team_id
                        JOIN organizations org2 ON org2.id = t2.org_id
                        WHERE org2.id = ${orgId.id}
                        AND es2.team_id = ${teamId.id}
                        AND es2.status = ${EmailSettingStatus.Active.toString}
                        AND es2.id NOT IN (26718,42169,43238,43226,34569)
                        GROUP BY (CASE WHEN es2.rep_mail_server_id IS NOT NULL THEN es2.rep_mail_server_id ELSE org2.rep_mail_server_id END)
                    )
                  """
      )

  }


  /**
   * fixme InboxV3: return only "active" or "receive" inboxes
   * fixme InboxV3: unread count
   * This function is used to show the list of inboxes in the InboxV3 ui
   *
   * @param permittedOwnerIds
   * @param teamId
   * @return
   */
  //  def findInboxesByOwnership(
  //    permittedOwnerIds: Seq[Long],
  //    teamId: Long
  //  ): Try[Seq[InboxV3Folder]] = Try {
  //
  //    DB readOnly { implicit session =>
  //
  //      sql"""
  //       select email_setting_id, inbox_name, count(email_thread_id) as unread
  //       from (
  //         SELECT
  //
  //        es.id AS email_setting_id,
  //        es.email AS inbox_name,
  //        et.id as email_thread_id
  //
  //        --'5' as unread
  //
  //        FROM email_settings es
  //        JOIN teams t ON t.id = es.team_id
  //        left JOIN email_threads et on et.email_settings_id = es.id
  //        WHERE
  //        es.team_id = $teamId
  //        AND es.account_id IN ($permittedOwnerIds)
  //        AND et.latest_reply_at is not null
  //
  //        --removing below condition to fetch all the email_setting's emails
  //        --and not et.sr_read
  //        and et.latest_email_id is not null
  //        and not et.snoozed
  //        and not et.archived
  //        AND et.team_id = $teamId
  //        AND et.prospect_id is not null
  //        -- 99 is the highest 2 digit number otherwise every read
  //        -- would require  us to show some UI change
  //        -- this is a psychological play.
  //        -- we initially thought we will show the count as max 10
  //        -- but 99 allows lot of room to update inbox count lazily
  //        -- without alerting the user. (10 -> 9 is a single digit from double
  //        -- so we would expect some ui change)
  //        -- after further discussion we have decided to keep this no as 20
  //        limit 20
  //       ) part1
  //       group by email_setting_id, inbox_name;
  //
  //       """
  //        .map(InboxV3Folder.fromDbForEmailSetting)
  //        .list
  //        .apply()
  //
  //    }
  //
  //  }


  // SENDER_ROTATION
  // called from -
  // CampaignController.duplicateCampaign
  def findByIds(ids: Seq[Long], permittedAccountIds: Seq[Long], teamId: Long): Try[Seq[EmailSetting]] = Try {

    if (ids.isEmpty) Seq() else {

      __findEmailSettingsSQL(
        whereClause =
          sqls"""
                      WHERE es.id IN ($ids)
                      AND es.account_id IN ($permittedAccountIds)
                      AND es.team_id = $teamId
                      and  es.status = ${EmailSettingStatus.Active.toString}
                      """)
    }

  }


  def findForScheduling(
                         channelId: Long,
                         srLogger: SrLoggerTrait
                       ): Option[EmailSettingCreateEmailSchedule] = blocking {

    val logger = srLogger.appendLogRequestId(s"[findForScheduling]")

    /*
    srDBQueryCounterService.logDBQueryCallFrequency(
      dbQueryName =  "findForScheduling",
      logger = logger
    )
    */
    DB readOnly { implicit session =>

      sql"""
      SELECT

      e.id,
      e.team_id,
      e.account_id,
      e.quota_per_day,
      e.donot_enforce_24_hour_limit_till,
      e.email,
      e.sender_name,
      e.first_name,
      e.last_name,

      (case when rth.subdomain_based then concat(o.tracking_subdomain_key, '.', rth.host_url) else rth.host_url end) as default_tracking_domain,
      (case when rth.subdomain_based then concat(o.tracking_subdomain_key, '.', rth.unsubscribe_host_url) else rth.unsubscribe_host_url end) as default_unsubscribe_domain,
      rth.id AS rep_tracking_host_id,
      (select ctd.domain_pointer_name
        from custom_tracking_domain_records ctd
        where lower(ctd.domain_host) = lower(e.email_address_host)
          and ctd.is_verified
          and ctd.is_ssl_enabled
      ) as ctd_domain_pointer_name,
      e.signature,


      e.min_delay_seconds,
      e.max_delay_seconds,

      e.latest_email_scheduled_at,
      a.timezone as account_timezone,
      o.bulk_sender,
      o.id as org_id



      FROM email_settings e
      JOIN accounts a ON a.id = e.account_id
      JOIN teams t ON t.id = e.team_id
      JOIN organizations o ON o.id = a.org_id
      JOIN rep_tracking_hosts rth ON rth.id = (CASE WHEN e.rep_tracking_host_id IS NOT NULL THEN e.rep_tracking_host_id ELSE o.rep_tracking_host_id END)

      WHERE o.plan_type != ${PlanType.INACTIVE.toString}
        AND t.active
        AND a.active
        AND e.status = ${EmailSettingStatus.Active.toString}
        AND (o.paused_till IS NULL OR o.paused_till < now())
        AND NOT e.is_under_review
        AND e.id = $channelId

        -- if payment due for a while: no need to schedule
        AND (o.payment_due_campaign_pause_at IS NULL OR o.payment_due_campaign_pause_at > now())

      LIMIT 1
      """
        .map(rs => {

          val sender_name_db = rs.string("sender_name").trim
          val first_name_db = rs.string("first_name").trim
          val last_name_db = rs.string("last_name").trim

          val senderName = if (sender_name_db.nonEmpty) sender_name_db
          else if (first_name_db.nonEmpty && last_name_db.nonEmpty) first_name_db + " " + last_name_db
          else first_name_db


          EmailSettingCreateEmailSchedule(
            id = rs.long("id"),
            team_id = rs.long("team_id"),
            org_id = OrgId(rs.long("org_id")),
            account_id = rs.long("account_id"),
            email = rs.string("email"),
            sender_name = senderName,
            first_name = first_name_db,
            last_name = last_name_db,

            quota_per_day = rs.int("quota_per_day"),
            donot_enforce_24_hour_limit_till = rs.jodaDateTimeOpt("donot_enforce_24_hour_limit_till"),
            min_delay_seconds = rs.int("min_delay_seconds"),
            max_delay_seconds = rs.int("max_delay_seconds"),
            latest_email_scheduled_at = rs.jodaDateTimeOpt("latest_email_scheduled_at"),

            default_tracking_domain = rs.string("default_tracking_domain"),
            default_unsubscribe_domain = rs.string("default_unsubscribe_domain"),
            rep_tracking_host_id = rs.long("rep_tracking_host_id"),
            custom_tracking_domain = rs.stringOpt("ctd_domain_pointer_name"),
            signature = rs.stringOpt("signature"),

            account_timezone = rs.stringOpt("account_timezone").getOrElse("UTC"),

            bulk_sender = rs.boolean("bulk_sender"),


          )
        })
        .headOption
        .apply()

    }
  }

  def getSenderEmailSettingIdsForReceiverId(
                                             receiverEmailSettingId: Long,
                                             teamId: Long): Try[Seq[Long]] = blocking {
    Try {

      val esIds = DB readOnly { implicit session =>

        //    sql"SELECT * FROM email_settings WHERE id = $id LIMIT 1"

        sql"""
      select distinct on (ses.id) id from (
          select
          ces.sender_email_setting_id as id
          from campaign_email_settings ces
          inner join email_settings sender on sender.id = ces.sender_email_setting_id and sender.team_id = ces.team_id
          where
    ( ces.team_id = $teamId and ces.receiver_email_setting_id = $receiverEmailSettingId and sender.status = ${EmailSettingStatus.Active.toString})


    UNION ALL


    select
          ces.sender_email_setting_id as id
          from campaign_email_settings ces
          inner join email_settings sender on sender.id = ces.sender_email_setting_id and sender.team_id = ces.team_id
          where

          -- include sending email when sending and receiving addresses are different
          (

            (ces.sender_email_setting_id != ces.receiver_email_setting_id)

              and

        ( ces.team_id = $teamId and ces.sender_email_setting_id = $receiverEmailSettingId and sender.status = ${EmailSettingStatus.Active.toString})
    )

   UNION ALL

         select  ses.id
         from campaign_email_settings ces
         join email_settings ses on ses.id = ces.sender_email_setting_id and ses.team_id = ces.team_id
         where

         -- include aliases (suppose I'm sending from gmail alias, and it bounces and reaches primary email, need to check alias' suffix as well, for tracking bounced emails sent from alias)
         (ses.team_id = $teamId and ses.alias_parent_id = $receiverEmailSettingId)
         and ses.status = ${EmailSettingStatus.Active.toString}

   UNION ALL

         select ses.id
         from email_settings ses
         join team_inbox ti on ses.id = ti.email_setting_id and ti.team_id = ses.team_id
         where

         ses.team_id = $teamId and ses.id = $receiverEmailSettingId
         and ses.status = ${EmailSettingStatus.Active.toString}

  ) ses


    ;

    """
          .map(_.long("id"))
          .list
          .apply()

      }

      esIds
    }
  }


  // 13680 calls are going here in 1 hour in peak traffic
  // need to cache the value by first moving into a service.
  //NOT USED
  //  def findSendersForReceiver(
  //    receiverEmailSettingId: Long,
  //    teamId: Long
  //  ):
  //  Try[Seq[EmailSetting]] = blocking {
  //    Try {
  //      val esIds = DB readOnly { implicit session =>
  //
  //        //    sql"SELECT * FROM email_settings WHERE id = $id LIMIT 1"
  //
  //        /*
  //
  //        sql"""
  //      select distinct on (ses.id) ses.* from campaigns c
  //      join email_settings ses on ses.id = c.sender_email_settings_id
  //      where c.receiver_email_settings_id = $receiverEmailSettingId;
  //      """
  //
  //        */
  //
  //
  //        sql"""
  //      select distinct on (ses.id) ses.id from campaigns c
  //      join campaign_email_settings ces on ces.campaign_id = c.id
  //      join email_settings ses on ses.id = ces.sender_email_setting_id
  //      where
  //      ( c.team_id = $teamId and ces.receiver_email_setting_id = $receiverEmailSettingId )
  //
  //
  //      -- include sending email when sending and receiving addresses are different
  //      OR (
  //
  //        (ces.sender_email_setting_id != ces.receiver_email_setting_id)
  //
  //          and
  //
  //          ( c.team_id = $teamId and ces.sender_email_setting_id = $receiverEmailSettingId )
  //      )
  //
  //      -- include aliases (suppose I'm sending from gmail alias, and it bounces and reaches primary email, need to check alias' suffix as well, for tracking bounced emails sent from alias)
  //      OR (ses.team_id = $teamId and ses.alias_parent_id = $receiverEmailSettingId)
  //
  //
  //      ;
  //
  //      """
  //          .map(_.long("id"))
  //          .list
  //          .apply()
  //
  //      }
  //
  //      if (esIds.isEmpty) Seq()
  //      else __findEmailSettingsSQL(whereClause = sqls" WHERE es.id IN ($esIds) ")
  //    }
  //  }

  def findSendersForReceiver(
                              esIds: Seq[Long]
                            ): Try[Seq[EmailSetting]] = blocking {
    Try {
      if (esIds.isEmpty) Seq()
      else __findEmailSettingsSQL(whereClause = sqls" WHERE es.id IN ($esIds) and  es.status = ${EmailSettingStatus.Active.toString}")
    }
  }


  def doesZapMailDomainExist(
                              domain: String,
                              teamId: TeamId
                            ): Try[Boolean] = Try {
    DB.readOnly { implicit session =>
      sql"""
        SELECT EXISTS(
          SELECT 1
          FROM purchased_domains
          WHERE domain_name = ${domain.toLowerCase.trim}
          AND team_id = ${teamId.id}
          AND platform_type = ${PlatformType.ZAPMAIL.toString}
        );
      """
        .map(rs => rs.boolean("exists"))
        .single
        .apply()
        .get
    }
  }
  
  def createViaForm(
                     accountId: Long,
                     teamId: Long,
                     taId: Long,
                     data: EmailSettingForm,
                     emailSettingUuid: EmailSettingUuid,
                     rep_mail_server_id: Int
                   )(
                     using Logger: SRLogger
                   ): Try[Option[EmailSetting]] = Try {
    val esId = DB localTx { implicit session =>

      val senderName = if (data.first_name.trim.nonEmpty && data.last_name.trim.nonEmpty) data.first_name.trim + " " + data.last_name.trim
      else data.first_name.trim

      val (_, emailAddressHost) = EmailValidationService.getLowercasedNameAndDomainFromEmail(email = data.email)

      sql"""
          INSERT INTO email_settings
          (
           account_id,
           team_id,
           ta_id,

           email,

           service_provider,

           sender_name,
           first_name,
           last_name,

           smtp_username,
           smtp_password_enc,
           smtp_host,
           smtp_port,

           imap_username,
           imap_password_enc,
           imap_host,
           imap_port,

           email_domain,
           api_key_enc,
           mailgun_region,

           quota_per_day,
           min_delay_seconds,
           max_delay_seconds,

           tracking_domain_host,
           dns_host,
           email_address_host,
           cc_emails,
           bcc_emails,
           used_in_campaign,
           domain_provider,
           uuid,
           platform_email_id,

           status,

           rep_mail_server_id,
           
           email_tag

          )
          VALUES (
            $accountId,
            $teamId,
            $taId,

            ${data.email.trim},

            ${data.service_provider.toString},

            $senderName,
            ${data.first_name.trim},
            ${data.last_name.trim},

            ${if (data.can_send) data.smtp_username.map(_.trim) else null},
            ${if (data.can_send) data.smtp_password.map(_.trim).map(EncryptionHelpers.encryptEmailSettingCredential) else null},
            ${if (data.can_send) data.smtp_host.map(_.trim) else null},
            ${if (data.can_send) data.smtp_port else null},

            ${if (data.can_receive) data.imap_username.map(_.trim) else null},
            ${if (data.can_receive) data.imap_password.map(_.trim).map(EncryptionHelpers.encryptEmailSettingCredential) else null},
            ${if (data.can_receive) data.imap_host.map(_.trim) else null},
            ${if (data.can_receive) data.imap_port else null},

            ${data.email_domain.map(_.trim)},
            ${data.api_key.map(_.trim).map(EncryptionHelpers.encryptEmailSettingCredential)},
            ${data.mailgun_region.map(_.toString.trim)},

            ${data.quota_per_day},
            ${data.min_delay_seconds},
            ${data.max_delay_seconds},

            null,
            null,
            $emailAddressHost,
            ${if (data.cc_emails.isDefined && data.cc_emails.get.trim.nonEmpty) data.cc_emails.get.trim else null},
            ${if (data.bcc_emails.isDefined && data.bcc_emails.get.trim.nonEmpty) data.bcc_emails.get.trim else null},
            false,
            ${data.provided_by.toString},
            ${emailSettingUuid.uuid},

            ${data.platform_email_id},
            ${data.status.toString},
            $rep_mail_server_id,
            
            ${data.email_tag.map(_.toLowerCase.trim)}

          )
          RETURNING id;
        """
        .map(_.long("id"))
        .single
        .apply()
        .flatMap(savedEmailId => {

          val msgIdSuffix = EmailHelper.genMessageIdSuffix(savedEmailId)

          sql"""update email_settings set message_id_suffix = $msgIdSuffix where id = ${savedEmailId} returning id"""
            .map(_.long("id")).single.apply()


        })

    }

    esId.flatMap(id => find(id = id, email_status = data.status))
  }


  def _incrementCurrentSentProspectCountForEmail(emailSettingId: Long): Try[Int] = Try {
    DB autoCommit { implicit session =>


      sql"""
          UPDATE email_settings
          SET
            current_prospect_sent_count_email = current_prospect_sent_count_email + 1
          WHERE id = $emailSettingId
          ;
      """
        .update
        .apply()
    }


  }

  def resetSentCountAtEndOfMonthlyCycle(orgIds: Seq[Long]): Try[Int] = Try {

    if (orgIds.isEmpty) 0
    else {

      DB localTx { implicit session =>

        sql"""
          UPDATE email_settings es
          SET
            error = null,
            error_reported_at = null,
            paused_till = null,
            current_prospect_sent_count_email =  0,
            auth_failed_count = 0
          FROM accounts a
          WHERE a.id = es.account_id
          --JOIN organizations o ON o.id = a.org_id
          AND a.org_id IN ($orgIds)
          ;
      """
          .update
          .apply()

      }

    }


  }

  def createViaOauth(
                      accountId: Long,
                      teamId: Long,
                      taId: Long,
                      data: EmailSettingCreateViaOAuth,
                      emailSettingUuid: EmailSettingUuid
                    )(
                      using Logger: SRLogger
                    ): Try[Option[EmailSetting]] = Try {

    Logger.info(s"EmailSetting.createViaOauth called: ($accountId, $teamId, $taId): data: $data")

    val esId = DB localTx { implicit session =>


      val senderName = if (data.first_name.trim.nonEmpty && data.last_name.trim.nonEmpty) data.first_name.trim + " " + data.last_name.trim
      else data.first_name.trim

      val (_, emailAddressHost) = EmailValidationService.getLowercasedNameAndDomainFromEmail(email = data.email)

      /*
        4-October-2024 :
        Have to add this update query over here to first update the service_provider for
        the existing email_setting with service_provider as "gmail_asp" else there will be a
        new entry in the email_settings table for this reconnect request
        For eg . If there is an existing email_setting where email is "<EMAIL>" with
        service_provider as "gmail_asp" and now i want it to reconnect it via OAuth for the existing email_setting
        then I have to match the service_provider, team_id and email so that the required OAuth fields will be updated
         for the matching email_setting >Then to reconnect from gmail_asp to gmail_api we first have
         to update the service_provider from gmail_asp to gmail_api then the conflict will occur and the respective
         Oauth_access_token and Oauth_refresh_token fields will be updated without creating a new email_setting for the same
         email with the same team_id
         */

      if (data.service_provider == EmailServiceProvider.GMAIL_API) {
        sql"""
                 UPDATE email_settings SET
                 service_provider = ${EmailServiceProvider.GMAIL_API.toString}
                 WHERE service_provider = ${EmailServiceProvider.GMAIL_ASP.toString}
                 AND team_id = ${teamId}
                 AND email = ${data.email.trim}
               """
          .update
          .apply()
      }

      // take the first api key FIXME, NOT NULL column this is
      sql"""
          INSERT INTO email_settings
          (
           account_id,
           team_id,
           ta_id,

           email,

           service_provider,

           sender_name,
           first_name,
           last_name,

           oauth2_access_token_enc,
           oauth2_refresh_token_enc,
           oauth2_token_type,
           oauth2_token_expires_in,
           oauth2_access_token_expires_at,

           rep_google_api_key_id,
           rep_mail_server_id,
           rep_tracking_host_id,
           gsuite_domain_install,
           email_domain,
           domain_provider,
           platform_purchased_email_status,

           quota_per_day,

           alias_parent_id,
           email_address_host,
           used_in_campaign,
           uuid,
           min_delay_seconds,
           max_delay_seconds
          )
          VALUES (
            $accountId,
            $teamId,
            $taId,

            ${data.email.trim},

            ${data.service_provider.toString},

            $senderName,
            ${data.first_name.trim},
            ${data.last_name.trim},

            ${EncryptionHelpers.encryptEmailSettingCredential(data.oauth2_access_token)},
            ${EncryptionHelpers.encryptEmailSettingCredential(data.oauth2_refresh_token)},
            ${data.oauth2_token_type},
            ${data.oauth2_token_expires_in},
            ${data.oauth2_access_token_expires_at},

            ${data.rep_google_api_key_id.getOrElse(1)},
            ${data.rep_mail_server_id},
            ${data.rep_tracking_host_id},
            ${data.gsuite_domain_install.getOrElse(false)},
            ${data.domain},
            ${data.domain_provider.map(_.toString).orNull},
            ${data.email_purchase_status.map(_.toString).orNull},
            ${data.quota_per_day},

            ${data.alias_parent_id.orNull},
            $emailAddressHost,
            false,
            ${emailSettingUuid.uuid},
            ${data.service_provider.min_delay_seconds},
            ${data.service_provider.max_delay_seconds}

          )
          ON CONFLICT (team_id, email, service_provider) DO UPDATE SET
            oauth2_access_token_enc = EXCLUDED.oauth2_access_token_enc,
            oauth2_refresh_token_enc = EXCLUDED.oauth2_refresh_token_enc,
            rep_google_api_key_id = EXCLUDED.rep_google_api_key_id,
            error = null,
            error_reported_at = null,
            paused_till = null,

            sender_name = EXCLUDED.sender_name,
            first_name = EXCLUDED.first_name,
            last_name = EXCLUDED.last_name,

            auth_failed_count = 0,
            gsuite_domain_install = EXCLUDED.gsuite_domain_install
          RETURNING id;
        """
        .map(_.long("id"))
        .single
        .apply()
        .flatMap(savedEmailId => {

          /*
          sql"""update email_settings set
               oauth2_access_token = ${savedEmail.oauth2_access_token},
               oauth2_refresh_token = ${savedEmail.oauth2_refresh_token},
               oauth2_token_expires_in = ${savedEmail.oauth2_access_token_expires_at},
               oauth2_access_token_expires_at = ${savedEmail.oauth2_access_token_expires_at}
               where team_id = $teamId and LOWER(email) = LOWER(${savedEmail.email}) and id != ${savedEmail.id}

             """
            .update
            .apply()

          */

          val msgIdSuffix = EmailHelper.genMessageIdSuffix(savedEmailId)

          sql"""update email_settings set message_id_suffix = $msgIdSuffix where id = $savedEmailId returning id"""
            .map(_.long("id")).single.apply()


        })

    }

    esId.flatMap(id => find(id = id))

  }

  def updateAccessTokenAndRefreshToken(emailSettingId: EmailSettingId, data: EmailSettingUpdateAccessToken): Try[Option[EmailSetting]] = blocking {
    Try {
      val esId = DB autoCommit { implicit session =>


        sql"""
          UPDATE email_settings
          SET
            oauth2_access_token_enc = ${EncryptionHelpers.encryptEmailSettingCredential(data.oauth2_access_token)},
            oauth2_access_token_expires_at = ${data.oauth2_access_token_expires_at},
            oauth2_token_expires_in = ${data.oauth2_token_expires_in},
            oauth2_refresh_token_enc = ${EncryptionHelpers.encryptEmailSettingCredential(data.oauth2_refresh_token)}

          WHERE id = ${emailSettingId.emailSettingId}
          RETURNING id;
        """
          .map(_.long("id"))
          .single
          .apply()

      }

      esId.flatMap(id => find(id = id))

    }
  }

  def updateBasicSettingsViaForm(emailSettingId: EmailSettingId, data: EmailSettingForm, teamId: TeamId): Try[Option[EmailSetting]] = Try {
    val esId = DB autoCommit { implicit session =>

      sql"""
          UPDATE email_settings
          SET

            smtp_username = ${data.smtp_username.map(_.trim)},
            smtp_password_enc = ${data.smtp_password.map(_.trim).map(EncryptionHelpers.encryptEmailSettingCredential)},
            smtp_host = ${data.smtp_host.map(_.trim)},
            smtp_port = ${data.smtp_port},

            imap_username = ${data.imap_username.map(_.trim)},
            imap_password_enc = ${data.imap_password.map(_.trim).map(EncryptionHelpers.encryptEmailSettingCredential)},
            imap_host = ${data.imap_host.map(_.trim)},
            imap_port = ${data.imap_port},

            email_domain = ${data.email_domain.map(_.trim)},
            api_key_enc = ${data.api_key.map(_.trim).map(EncryptionHelpers.encryptEmailSettingCredential)},
            mailgun_region = ${data.mailgun_region.map(_.toString.trim)},
            
            error = null,
            error_reported_at = null,
            paused_till = null,
            auth_failed_count = 0

          WHERE id = ${emailSettingId.emailSettingId}
          AND team_id = ${teamId.id}
          RETURNING id;
        """
        .map(_.long("id"))
        .single
        .apply()

    }

    esId.flatMap(id => find(id = id))

  }

  def updateEmailSettingViaForm(emailSettingId: EmailSettingId, data: EmailSettingForm, teamId: TeamId): Try[Option[EmailSetting]] = Try {
    val esId = DB autoCommit { implicit session =>


      //TODO: check this
      val senderName = if (data.first_name.trim.nonEmpty && data.last_name.trim.nonEmpty) data.first_name.trim + " " + data.last_name.trim
      else data.first_name.trim

      sql"""
          UPDATE email_settings
          SET
      
            sender_name = $senderName,
            first_name = ${data.first_name.trim},
            last_name = ${data.last_name.trim},

            quota_per_day = ${data.quota_per_day},
            min_delay_seconds = ${data.min_delay_seconds},
            max_delay_seconds = ${data.max_delay_seconds},

            cc_emails = ${if (data.cc_emails.isDefined && data.cc_emails.get.trim.nonEmpty) data.cc_emails.get.trim else null},

            bcc_emails = ${if (data.bcc_emails.isDefined && data.bcc_emails.get.trim.nonEmpty) data.bcc_emails.get.trim else null},
            error = null,
            error_reported_at = null,
            paused_till = null,
            auth_failed_count = 0

          WHERE id = ${emailSettingId.emailSettingId}
          AND team_id = ${teamId.id}
          RETURNING id;
        """
        .map(_.long("id"))
        .single
        .apply()

    }

    esId.flatMap(id => find(id = id))

  }

  def updateEmailSettingsQuotaAndDelays(data: EmailSettingDailyLimitAndDelay,teamId: TeamId): Try[List[EmailSetting]] = Try {
    // Build dynamic update clauses based on which fields are defined
    val updateClauses = Seq(
      data.quota_per_day.map(quota => sqls"quota_per_day = $quota"),
      data.min_delay_seconds.map(min => sqls"min_delay_seconds = $min"),
      data.max_delay_seconds.map(max => sqls"max_delay_seconds = $max")
    ).flatten

    // If no fields are defined, return 0 (no updates)
    if (updateClauses.isEmpty || data.es_ids.isEmpty) {
      List.empty
    } else {
      val combinedUpdateClause = SQLSyntax.join(updateClauses, sqls",")

      val emailSettingIds = data.es_ids.map(_.emailSettingId)
      
      val esIds = DB autoCommit { implicit session =>
        sql"""
          UPDATE email_settings
          SET $combinedUpdateClause
          WHERE id IN ${SQLUtils.generateSQLValuesClause(arr = emailSettingIds)}
          AND team_id = ${teamId.id}
          RETURNING id;
        """
          .map(_.long("id"))
          .list
          .apply()
      }
        esIds.flatMap(id => find(id = id))
    }
  }

  def updateNextCheckForSchedulerForEmailSetting(
                                                emailSettingId: EmailSettingId,
                                                teamId: TeamId
                                                ): Try[Int] = Try{
    val interval_time = SQLSyntax.createUnsafely(AppConfig.SchedulerConfig.CAMPAIGN_NEXT_SCHEDULED_AT_MIN_DELAY_IN_MINUTES.toString)

    DB autoCommit { implicit session =>
      sql"""
           update campaign_email_settings
           set next_to_be_scheduled_at =  now()

          where
               (
                  sender_email_setting_id = ${emailSettingId.emailSettingId}
                  OR
                  receiver_email_setting_id = ${emailSettingId.emailSettingId}
               )
               and next_to_be_scheduled_at is not null
               and next_to_be_scheduled_at > now() + interval '$interval_time minutes'
               AND team_id = ${teamId.id};
         """
        .update
        .apply()
    }
  }

  def moveGmailAccountFromAPIToAppPasswordFlow(
                                                emailSettingId: Int,
                                                teamId: Long,
                                                smtp: SmtpEmailSetting,
                                                imap: ImapEmailSetting
                                              ): Try[Int] = Try {

    DB autoCommit { implicit session =>

      sql"""
          UPDATE email_settings
          SET

            service_provider = ${EmailServiceProvider.GMAIL_ASP.toString},

            smtp_username = ${smtp.smtp_username},
            smtp_password_enc = ${EncryptionHelpers.encryptEmailSettingCredential(smtp.smtp_password)},
            smtp_host = ${smtp.smtp_host},
            smtp_port = ${smtp.smtp_port},

            imap_username = ${imap.imap_username},
            imap_password_enc = ${EncryptionHelpers.encryptEmailSettingCredential(imap.imap_password)},
            imap_host = ${imap.imap_host},
            imap_port = ${imap.imap_port},

            oauth2_access_token_enc = null,
            oauth2_access_token_expires_at = null,
            oauth2_token_expires_in = null,
            oauth2_refresh_token_enc = null,

            error = null,
            error_reported_at = null,
            paused_till = null,
            auth_failed_count = 0

          WHERE id = $emailSettingId
          AND team_id = $teamId
          ;
        """
        .update
        .apply()

    }

  }

  /*def updateCustomTrackingDomain(id: Int, trackingDomainHost: String, custom_tracking_cname_value: String, customTrackingDomain: String): Try[Option[EmailSetting]] = Try {
    val esId = DB autoCommit { implicit session =>
      sql"""
          UPDATE email_settings
          SET
            tracking_domain_host = ${trackingDomainHost.trim},
            dns_host = $customTrackingDomain,
            custom_tracking_cname_value = $custom_tracking_cname_value

          WHERE id = $id
          RETURNING id;
        """
        .map(_.long("id"))
        .single
        .apply()

    }

    esId.flatMap(id => find(id = id))

  }*/


  def updateLastReadForReplies(emailSetting: EmailSetting, lastReadForReplies: DateTime,
                               Logger: SRLogger
                              ): Try[Option[Long]] = blocking {
    Try {
      DB autoCommit { implicit session =>

        Logger.info(s"updateLastReadForReplies:: ${emailSetting.email}")
        sql"""
          UPDATE email_settings
          SET
            last_read_for_replies = $lastReadForReplies,
            in_queue_for_reply_tracking = FALSE,
            error = null,
            error_reported_at = null,
            paused_till = null,
            auth_failed_count = 0

          WHERE id = ${emailSetting.id.map(_.emailSettingId)}
          RETURNING id;
        """
          .map(_.long("id"))
          .single
          .apply()

      }


    }
  }


  def _updateLastScheduled(emailSettingId: Long, latestEmailScheduledAt: Option[DateTime],
                           Logger: SRLogger
                          ): Int = {
    blocking {
      DB autoCommit { implicit session =>
        Logger.info(s"_updateLastScheduled in_queue_for_scheduling: $emailSettingId :: $latestEmailScheduledAt")

        if (latestEmailScheduledAt.isEmpty) {

          sql"""
          UPDATE email_settings
          SET
            in_queue_for_scheduling = false

          WHERE id = $emailSettingId
        """
            .update
            .apply()
        } else {

          sql"""
          UPDATE email_settings
          SET
            in_queue_for_scheduling = false,
            latest_email_scheduled_at = ${latestEmailScheduledAt.get}

          WHERE id = $emailSettingId
        """
            .update
            .apply()

        }

      }
    }
  }


  def _updateLastSent(emailSettingId: Long, lastSentId: Long, lastSentSentAt: DateTime, lastSentScheduledAt: DateTime): Int = {
    blocking {
      DB autoCommit { implicit session =>
        logger.info(s"_updateLastSent: $emailSettingId :: $lastSentSentAt :: $lastSentScheduledAt")

        sql"""
        UPDATE email_settings
        SET
          last_sent_sent_at = $lastSentSentAt,
          last_sent_scheduled_at = $lastSentScheduledAt,
          auth_failed_count = 0

        WHERE id = $emailSettingId
      """
          .update
          .apply()


      }
    }
  }

  /*
  // INTERNAL USE: there is no Try wrapper cause this is used inside other transactions wrapped in try
  def _revertLatestEmailScheduledAtOnDeactivatingByEmailSettingId(emailSettingId: Long): Int = {

    Logger.info(s"EmailSetting _revertLatestEmailScheduledAtOnDeactivatingByEmailSettingId: email setting $emailSettingId")

    sql"""
        UPDATE email_settings eset SET
          latest_email_scheduled_at = COALESCE((SELECT es.scheduled_at FROM emails_scheduled es where es.sender_email_settings_id = eset.id order by es.scheduled_at desc limit 1), null)
        WHERE eset.id = $emailSettingId;
      """
      .update
      .apply()

  }
  */

  // INTERNAL USE: there is no Try wrapper cause this is used inside other transactions wrapped in try
  //SENDER_ROTATION
  // called from -
  // EmailScheduledDAO.deleteUnsentByEmailSettingId
  // EmailScheduledDAO.deleteUnsentByEmailScheduledIdAndRevert
  // EmailScheduledDAO._deleteUnsentByProspectIds
  def _revertLatestEmailScheduledAtOnDeactivatingByEmailSettingId(emailSettingIds: Seq[EmailSettingId], deletedAllEmailForEmailSettingId: Boolean = false)(implicit session: DBSession): Int = {

    logger.info(s"EmailSetting _revertLatestEmailScheduledAtOnDeactivatingByEmailSettingId: email setting $emailSettingIds :: $deletedAllEmailForEmailSettingId")

    if (emailSettingIds.isEmpty) {
      0
    }
    else if (deletedAllEmailForEmailSettingId) {

      sql"""
      UPDATE email_settings eset SET
        latest_email_scheduled_at = last_sent_sent_at
      WHERE eset.id in (${emailSettingIds.map(_.emailSettingId)});
    """
        .update
        .apply()

    } else {

      /*

      // NOTE: it must be from the master db itself and not srread1 because this is part of a local txn
      // and srread1 does not yet have the updated values

      // val latestEmailScheduledAt = DB readOnly { implicit session =>
      val latestEmailScheduledAt = {

        sql"""
      WITH

      es1 as (SELECT last_sent_sent_at as at from email_settings where id = $emailSettingId),

      es2 as (SELECT  COALESCE(
        (SELECT es.scheduled_at FROM emails_scheduled es where es.sender_email_settings_id = $emailSettingId and not sent order by es.scheduled_at desc limit 1)
      , null) as at
      )

      SELECT (CASE WHEN es1.at IS NULL THEN es2.at
        WHEN es2.at IS NULL THEN es1.at
        WHEN es1.at >= es2.at THEN es1.at
        ELSE es2.at
        END
      )
      FROM es1, es2;
    """
          .map(rs => rs.jodaDateTimeOpt("at"))
          .single
          .apply()
          .flatten
      }

      Logger.info(s"EmailSetting _revertLatestEmailScheduledAtOnDeactivatingByEmailSettingId: email setting $emailSettingId :: found latestEmailScheduledAt :: $latestEmailScheduledAt")

      sql"""
      UPDATE email_settings eset SET
        latest_email_scheduled_at = $latestEmailScheduledAt
      WHERE eset.id = $emailSettingId;
    """
        .update
        .apply()
      */

      sql"""
      UPDATE email_settings eset SET
        latest_email_scheduled_at = (
        WITH
          es1 as (SELECT last_sent_sent_at as at from email_settings where id = eset.id),
          es2 as (SELECT  COALESCE((SELECT es.scheduled_at FROM emails_scheduled es where es.inbox_email_setting_id = eset.id and not sent AND es.scheduled_from_campaign order by es.scheduled_at desc limit 1), null) as at)

        SELECT (CASE WHEN es1.at IS NULL THEN es2.at
          WHEN es2.at IS NULL THEN es1.at
          WHEN es1.at >= es2.at THEN es1.at
          ELSE es2.at
          END
        )
        FROM es1, es2

      )
      WHERE eset.id in (${emailSettingIds.map(_.emailSettingId)});
  """
        .update
        .apply()


    }
  }


  def addError(emailSettingId: Long, error: String, errorReportedAt: DateTime, pausedTill: DateTime): Try[Seq[EmailSetting]] = blocking {
    Try {
      val esId = DB localTx { implicit session =>

        // in case of gmail, error should be applied to parent as well as all aliases
        val parentId = sql"""select (CASE WHEN service_provider = 'gmail_alias' THEN alias_parent_id ELSE id END) AS id from email_settings where id = $emailSettingId""".map(_.long("id")).single.apply().get

        sql"""
          UPDATE email_settings
          SET
            error = $error,
            error_reported_at = $errorReportedAt,
            paused_till = $pausedTill

          WHERE id = $parentId OR alias_parent_id = $parentId
          RETURNING *;
        """
          .map(_.long("id"))
          .list
          .apply()

      }

      esId.flatMap(id => find(id = id))
    }
  }


  def sendingEmailAccountsForScheduling(senderEmailSettingIds: Seq[Int]): Try[Seq[Long]] = if (senderEmailSettingIds.isEmpty) {
    Success(Seq[Long]())
  } else {

    blocking {
      Try {


        DB autoCommit { implicit session =>
          sql"""
      UPDATE email_settings es_rows SET
        in_queue_for_scheduling = TRUE,
        pushed_to_queue_for_scheduling_at = now()
      where es_rows.id IN ($senderEmailSettingIds)
       and  es_rows.status = ${EmailSettingStatus.Active.toString}
       and (
          (
            es_rows.in_queue_for_scheduling = FALSE
            AND EXISTS (
              SELECT ces1.campaign_id from campaign_email_settings ces1
              INNER JOIN campaigns c1 ON (c1.id = ces1.campaign_id and c1.team_id = ces1.team_id AND c1.status = ${CampaignStatus.RUNNING.toString})
              where ces1.sender_email_setting_id = es_rows.id
              and ces1.team_id = es_rows.team_id
              and
                (
                  ces1.last_schedule_done_at IS NULL
                  OR
                  ces1.last_schedule_done_at < now() - interval '5 minutes'
                )
              and (
              ces1.next_to_be_scheduled_at IS NULL OR
              ces1.next_to_be_scheduled_at < now()
              )
              LIMIT 1

            )
          )
          OR
          es_rows.pushed_to_queue_for_scheduling_at < now() - interval '60 minutes'
       )
      returning id
      """
            .map(_.long("id"))
            .list
            .apply()

        }
      }
    }
  }

  def fetchEmailAccountForScheduling(): Try[Seq[EmailSettingForScheduling]] = Try {
    DB autoCommit { implicit session =>

      val donotScheduleIfAlreadyScheduledFor = SQLSyntax
        .createUnsafely(AppConfig.SchedulerConfig.doNotScheduleEmailAccountIfAlreadyScheduledEmailsInNextIntervalMinutes.toString)

      val query =
        sql"""

        UPDATE email_settings es_rows SET
          in_queue_for_scheduling = TRUE,
          pushed_to_queue_for_scheduling_at = now()

        FROM (

          SELECT DISTINCT ON (ses.id)
            ses.id, ses.account_id, ses.team_id,

          o.plan_prospects_contacted_current,
          -- ses.current_prospect_sent_count_email,
          ses.email AS sender_email,
          o.is_agency,
          o.id as org_id,
          o.plan_name,
          o.total_sending_email_accounts,

          o.enable_email_validation,
          o.current_cycle_started_at,
          o.warning_code as org_warning_code,
          o.warning_at as org_warning_at


          FROM email_settings ses
          INNER JOIN campaign_email_settings ces on ces.sender_email_setting_id = ses.id  and ses.team_id = ces.team_id
          INNER JOIN campaigns c ON (c.id = ces.campaign_id and c.team_id = ces.team_id AND c.status = ${CampaignStatus.RUNNING.toString})
          INNER JOIN email_settings res ON res.id = ces.receiver_email_setting_id and res.team_id = ces.team_id
          INNER JOIN teams_accounts ta ON ta.account_id = c.account_id AND ta.team_id = c.team_id
          INNER JOIN teams t ON t.id = ta.team_id
          INNER JOIN accounts a ON a.id = ta.account_id
          INNER JOIN organizations o ON o.id = a.org_id
          LEFT JOIN domain_health_checks sender_dhc ON sender_dhc.domain = ses.email_address_host

          WHERE o.plan_type != ${PlanType.INACTIVE.toString} AND t.active AND a.active

            AND ses.status = ${EmailSettingStatus.Active.toString}
            AND res.status = ${EmailSettingStatus.Active.toString}
            AND (
              ses.latest_email_scheduled_at is null
              or
              ses.latest_email_scheduled_at < now() + interval '$donotScheduleIfAlreadyScheduledFor minutes'

            )

            AND (ses.paused_till IS NULL OR ses.paused_till < now())
            AND (res.paused_till IS NULL OR res.paused_till < now())
            AND NOT ses.is_under_review
            AND NOT res.is_under_review
            AND (o.paused_till IS NULL OR o.paused_till < now())
            AND COALESCE(sender_dhc.is_in_spam_blacklist, FALSE) = FALSE

            -- if payment due for a while: no need to schedule
            AND (o.payment_due_campaign_pause_at IS NULL OR o.payment_due_campaign_pause_at > now())

            AND (
              (
                ses.in_queue_for_scheduling = FALSE
                  AND EXISTS (
                    SELECT ces1.campaign_id from campaign_email_settings ces1
                    INNER JOIN campaigns c1 ON (c1.id = ces1.campaign_id and c1.team_id = ces1.team_id AND c1.status = ${CampaignStatus.RUNNING.toString})
                    INNER JOIN teams t1 on c1.team_id = t1.id
                    where ces1.sender_email_setting_id = ses.id
                    and ces1.team_id = ses.team_id
                    and
                      (
                        ces1.last_schedule_done_at IS NULL
                        OR
                        ces1.last_schedule_done_at < now() - interval '5 minutes'
                      )
                    and (
                    ces1.next_to_be_scheduled_at IS NULL OR
                    ces1.next_to_be_scheduled_at < now()
                    )
                    LIMIT 1

                  )
              )
              OR
              ses.pushed_to_queue_for_scheduling_at < now() - interval '60 minutes'
            )

            -- only for testing
            -- AND a.id IN (42, 8, 45, 87)

            ORDER BY ses.id, ses.pushed_to_queue_for_scheduling_at ASC

      ) filtered_campaign_rows
      WHERE es_rows.id = filtered_campaign_rows.id
      RETURNING filtered_campaign_rows.*

      ;
    """


      query
        .map(rs => EmailSettingForScheduling(
          id = rs.int("id"),
          account_id = rs.long("account_id"),
          team_id = rs.long("team_id"),
          org_id = rs.long("org_id"),

          // current_prospect_sent_count_email = rs.int("current_prospect_sent_count_email"),
          current_prospect_sent_count_org = rs.int("plan_prospects_contacted_current"),
          is_agency = rs.boolean("is_agency"),
          plan_name = rs.string("plan_name"),
          total_sending_email_accounts = rs.int("total_sending_email_accounts"),
          current_cycle_started_at = rs.jodaDateTime("current_cycle_started_at"),

          org_warning_code = rs.stringOpt("org_warning_code").map(AccountWarningCodeType.fromString(_).get),
          org_warning_at = rs.jodaDateTimeOpt("org_warning_at"),

          sender_email = rs.string("sender_email")

        ))
        .list
        .apply()

    }
  }

  def updateSignature(emailSettingId: Long, signature: EmailSettingUpdateSignatureForm): Try[Option[EmailSetting]] = Try {
    val esId = DB autoCommit { implicit session =>

      sql"""
          UPDATE email_settings
          SET
            signature = ${signature.signature.trim}
          WHERE id = $emailSettingId
          RETURNING *;
        """
        .map(_.long("id"))
        .single
        .apply()

    }

    esId.flatMap(id => find(id = id))
  }


  //  def changeActiveStatus(emailSettingIds: Seq[Int], active: Boolean): Try[Int] = Try {
  //    if (emailSettingIds.isEmpty) 0
  //    else {
  //      DB autoCommit { implicit session =>
  //
  //        sql"""
  //          UPDATE email_settings
  //          SET
  //            active = $active
  //          WHERE id IN ($emailSettingIds);
  //        """
  //          .update
  //          .apply()
  //
  //      }
  //    }
  //  }

  def getActiveSenderEmailSettingIds(orgId: Long): Seq[Long] = {
    DB.readOnly { implicit session =>

      sql"""
      SELECT DISTINCT (ces.sender_email_setting_id)
      from campaign_email_settings ces
      join campaigns c on ces.campaign_id = c.id and ces.team_id = c.team_id
      inner join accounts a ON a.id = c.account_id
      inner join email_settings es on es.id = ces.sender_email_setting_id and es.team_id = ces.team_id
      join teams t on t.id = c.team_id
      where a.org_id = $orgId
      and es.status = ${EmailSettingStatus.Active.toString}
      and c.status IN (${CampaignStatus.RUNNING.toString}, ${CampaignStatus.ON_HOLD.toString}, ${CampaignStatus.SCHEDULED.toString})
      and t.active


      -- group by c.sender_email_settings_id

      ;
      """

        /*
        sql"""

        SELECT DISTINCT (c.sender_email_settings_id)
        from campaigns c
        inner join accounts a ON a.id = c.account_id
        where a.org_id = $orgId
        and c.status = 'running'

        """
          */
        .map(rs => rs.long("sender_email_setting_id"))
        .list
        .apply()
    }
  }

  def getGoogleAuthKeysForEmailSetting(emailSettingId: Long, forReconnect: Boolean): Try[RepGoogleApiKey] = Try {

    DB readOnly { implicit session =>

      val reconnectCheck: SQLSyntax = if (!forReconnect) sqls"" else sqls" WHEN es.rep_google_api_key_id_if_reconnect IS NOT NULL THEN es.rep_google_api_key_id_if_reconnect "

      sql""" SELECT gapi.*

        FROM email_settings es

        JOIN accounts a ON es.account_id = a.id
        JOIN organizations org ON org.id = a.org_id
        JOIN rep_google_api_keys gapi ON gapi.id = (
          CASE
           $reconnectCheck
           WHEN es.rep_google_api_key_id IS NOT NULL THEN es.rep_google_api_key_id
           ELSE org.rep_google_api_key_id
          END
        )

        WHERE es.id = $emailSettingId
        and es.status = ${EmailSettingStatus.Active.toString}

       """
        .map(rs => {

          RepGoogleApiKey(
            id = rs.int("id"),
            cl_id = rs.string("cl_id"),
            cl_sec = rs.string("cl_sec")
          )
        })
        .single
        .apply()
        .get
    }
  }

  def getGoogleAuthKeysForOrg(orgId: Long): Try[RepGoogleApiKey] = Try {

    DB readOnly { implicit session =>

      sql""" SELECT gapi.*

        FROM organizations org
        JOIN rep_google_api_keys gapi ON gapi.id = org.rep_google_api_key_id
        WHERE org.id = $orgId

       """
        .map(rs => {

          RepGoogleApiKey(
            id = rs.int("id"),
            cl_id = rs.string("cl_id"),
            cl_sec = rs.string("cl_sec")
          )
        })
        .single
        .apply()
        .get
    }
  }

  def getGoogleAuthKeysForSignup(emailAddress: String): Try[RepGoogleApiKey] = Try {

    DB readOnly { implicit session =>

      val googleAuthApiKeyIdForNewAuthFlow = if (AppConfig.allowedEmailAddressForNewGoogleAPIKeys.contains(emailAddress.trim.toLowerCase)) AppConfig.googleAuthApiKeyIdV2 else AppConfig.googleAuthApiKeyIdForNewAuthFlow

      sql"""
             SELECT *
             FROM rep_google_api_keys
             WHERE id = $googleAuthApiKeyIdForNewAuthFlow
       """
        .map(rs => {

          RepGoogleApiKey(
            id = rs.int("id"),
            cl_id = rs.string("cl_id"),
            cl_sec = rs.string("cl_sec")
          )
        })
        .single
        .apply()
        .get
    }
  }

  def getGoogleAuthKeysById(googleApiKeyId: Int): Try[RepGoogleApiKey] = Try {

    DB readOnly { implicit session =>

      sql""" SELECT gapi.* FROM rep_google_api_keys gapi WHERE gapi.id = $googleApiKeyId """
        .map(rs => {

          RepGoogleApiKey(
            id = rs.int("id"),
            cl_id = rs.string("cl_id"),
            cl_sec = rs.string("cl_sec")
          )
        })
        .single
        .apply()
        .get
    }
  }


  def deleteEmailSetting(
                          emailSettingId: EmailSettingId,
                          teamId: TeamId

                        ): Try[Int] = Try {

    DB.autoCommit { implicit session =>

      sql"""
            DELETE
            FROM email_settings
            where 
              id = ${emailSettingId.emailSettingId}
              AND
              team_id = ${teamId.id}

      """.update.apply()
    }
  }

  /*
  def transitionToCustomSuffix(): Unit = {
    DB localTx { implicit session =>

      println("start transition ...")
      println("fetch all teams ...")

      val emailSettingIds =
        sql""" select * from email_settings"""
          .map(rs => rs.long("id"))
          .list.apply()


      println(s"fetched ${emailSettingIds.size} email settings ...")


      emailSettingIds.foreach { id =>

        println(s"for email setting ($id) ...")

        val msgIdSuffix = EmailHelper.genMessageIdSuffix(id)

        println(s"for email setting ($id) ::: suffix: $msgIdSuffix ...")

        val updateES = sql"""update email_settings set message_id_suffix = $msgIdSuffix where id = $id returning *""".map(rs => rs.long("id")).single.apply()

        println(s"updated email settings $updateES")


      }


      println("done!!!")

    }
  }
  */

  def findActiveDKIMRecord(domain: String): Try[Option[DKIMRecord]] = Try {
    DB.readOnly { implicit session =>
      sql"""
      SELECT *
      FROM dkim_records dr
      WHERE dr.domain = ${domain.toLowerCase.trim}
      AND active = TRUE;
      """
        .map(dkimRecordFromDb)
        .single
        .apply()
    }
  }

  def findDKIMRecord(domain: String): Try[Option[DKIMRecord]] = Try {
    DB.readOnly { implicit session =>
      sql"""
      SELECT *
      FROM dkim_records dr
      WHERE dr.domain = ${domain.toLowerCase.trim};
      """
        .map(dkimRecordFromDb)
        .single
        .apply()
    }
  }


  def activateDKIMRecord(domain: String): Try[Option[DKIMRecord]] = Try {
    DB autoCommit { implicit session =>
      sql"""
          UPDATE dkim_records
          SET
            active = TRUE
          WHERE domain = ${domain.toLowerCase.trim}
          RETURNING *;
        """
        .map(dkimRecordFromDb)
        .single
        .apply()
    }
  }

  def deactivateDKIMRecord(domain: String, email_settings_id: Long, error: String): Try[Option[DKIMRecord]] = Try {
    DB autoCommit { implicit session =>
      sql"""
          UPDATE dkim_records
          SET
            active = FALSE,
            error = ${error},
            error_reported_at = now(),
            error_produced_email_settings_id = ${email_settings_id}
          WHERE domain = ${domain.toLowerCase.trim}
          RETURNING *;
        """
        .map(dkimRecordFromDb)
        .single
        .apply()
    }
  }


  def saveDKIMRecord(dkimRecord: DKIMRecord, account_id: Long, account_name: String, account_email: String): Try[Option[DKIMRecord]] = blocking {
    Try {
      DB autoCommit { implicit session =>

        val recordSqls = sqls"""${dkimRecord.record}"""

        sql"""
          INSERT INTO dkim_records
          (
           domain,
           dkim_selector,
           dkim_record,
           public_key,
           private_key_enc,
           account_id,
           account_name,
           account_email
          )
          VALUES
          (
            ${dkimRecord.domain.toLowerCase.trim},
            ${dkimRecord.selector},
            $recordSqls,
            ${dkimRecord.public_key},
            ${EncryptionHelpers.encryptDkimRecordsCredential(dkimRecord.private_key)},
            $account_id,
            $account_name,
            $account_email
          )
          RETURNING *;
        """
          .map(dkimRecordFromDb)
          .single
          .apply()

      }
    }
  }


  def generateDKIMRecord(domain: String): DKIMRecord = {
    val generator = KeyPairGenerator.getInstance("RSA")
    generator.initialize(1024)
    val keyPair = generator.generateKeyPair

    val publicKey = keyPair.getPublic.asInstanceOf[RSAPublicKey]
    val privateKey = keyPair.getPrivate.asInstanceOf[RSAPrivateKey]
    val public_key = jakarta.xml.bind.DatatypeConverter.printBase64Binary(publicKey.getEncoded)

    DKIMRecord(
      public_key = public_key,
      private_key = jakarta.xml.bind.DatatypeConverter.printBase64Binary(privateKey.getEncoded),
      domain = domain,
      record = "v=DKIM1; k=rsa; p=" + public_key,
      selector = "smartreach",
      active = false
    )

  }


  def getEmailSettingAuthenticationFailedCount(
                                                emailSettingId: Long,
                                                sendEmailType: SendScheduleEmailType
                                              ): Try[Option[Int]] = Try {

    sendEmailType match {

      case SendScheduleEmailType.TestEmail =>

        DB readOnly { implicit session =>
          sql"""
              SELECT auth_failed_count FROM email_settings
              WHERE id = $emailSettingId
          """
            .map(rs => rs.int("auth_failed_count"))
            .single
            .apply()
        }


      case SendScheduleEmailType.AutoEmail | SendScheduleEmailType.ManualExtension =>

        DB autoCommit { implicit session =>
          sql"""
              UPDATE email_settings
              SET
                auth_failed_count = auth_failed_count + 1
              WHERE id = $emailSettingId
              RETURNING auth_failed_count;
          """
            .map(rs => rs.int("auth_failed_count"))
            .single
            .apply()
        }

    }
  }

  def isGsuiteDomainInstalled(
                               email: String,
                               teamId: Long
                             )(
                               using Logger: SRLogger
                             ): Boolean = {

    val domain = s"%${EmailValidationService.getLowercasedNameAndDomainFromEmail(email)._2}"

    DB.readOnly { implicit session =>

      sql"""
      SELECT EXISTS(SELECT * FROM email_settings WHERE email ILIKE $domain AND gsuite_domain_install = TRUE AND team_id = $teamId AND status = ${EmailSettingStatus.Active.toString});
      """
        .map(rs => rs.boolean("exists"))
        .single
        .apply()
        .get
    }
  }

  // TODO: 23rd May: remove this service: doesnt work anymore after auth0 acquired it
  def getAllSendingDomainsForBlacklistCheck(): Try[List[String]] = Try {

    DB.readOnly { implicit session =>

      sql"""
        select
        split_part(eset.email, '@', 2) as domain

        from email_settings eset
        join campaign_email_settings ces on ces.sender_email_setting_id = eset.id OR ces.receiver_email_setting_id = eset.id and eset.team_id = ces.team_id
        join campaigns c on c.id = ces.campaign_id
        join accounts on accounts.id = eset.account_id
        join teams on teams.id = eset.team_id
        join organizations org on org.id = teams.org_id

        where c.status = 'running'
        and teams.active
        and accounts.active
        and org.active
        and eset.status = ${EmailSettingStatus.Active.toString}
        group by domain
        ;
      """
        .map(rs => rs.string("domain"))
        .list
        .apply()
    }
  }

  def getOwnerAccountIdOfEmailSetting(email_setting_id: Long, teamId: Long): Try[Long] = Try {
    DB readOnly { implicit session =>
      sql"""
           select account_id from email_settings
           where id= $email_setting_id and team_id = $teamId;
         """
        .map(_.long("account_id"))
        .single
        .apply()
        .get
    }
  }

  def getAllEmailSettingsInTeam(team_id: Long): Try[List[String]] = Try {
    DB readOnly { implicit session =>
      sql"""
           SELECT email FROM email_settings WHERE team_id = $team_id
           AND status = ${EmailSettingStatus.Active.toString};
         """
        .map(_.string("email"))
        .list
        .apply()
    }
  }

  def getEmailDomainNotBlackListChecked: Try[Option[EmailAddressHost]] = Try {

    DB readOnly { implicit session =>
      sql"""
      SELECT DISTINCT ON(e.email_address_host)  e.email_address_host
       FROM email_settings e
       join teams t on t.id = e.team_id
       join organizations o on o.id = t.org_id

       WHERE ( e.last_blacklist_checked_at IS NULL
       OR e.last_blacklist_checked_at < now() - interval '24 hours' )

       AND o.plan_type != ${PlanType.INACTIVE.toString}

       AND t.active

       AND EXISTS (
       SELECT * FROM campaigns c JOIN campaign_email_settings ces on ces.campaign_id = c.id and c.team_id = ces.team_id
       WHERE ces.sender_email_setting_id = e.id
       AND c.status = ${CampaignStatus.RUNNING.toString}
       )
       LIMIT 1
       ;
         """
        .map(em => EmailAddressHost(em.string("email_address_host")))
        .single
        .apply()

    }
  }

  def updateBlacklistChecked(emailDomain: EmailAddressHost, blacklistCheckStatus: BlacklistCheckStatus): Try[List[EmailSettingId]] = Try {
    DB autoCommit { implicit session =>
      sql"""
        UPDATE email_settings
        SET last_blacklist_checked_at = ${DateTime.now()},
        last_blacklist_checked_result = ${blacklistCheckStatus.toString}
        WHERE email_address_host = ${emailDomain.emailAddressHost}
        RETURNING id;
      """
        .map { esId => EmailSettingId(emailSettingId = esId.long("id")) }
        .list
        .apply()
    }


  }

  def getBlackListedDomainDetailsInLast24Hours(domain: EmailAddressHost): Try[Option[BlackListedCustomerDomainDetails]] = Try {

    DB readOnly { implicit session =>
      sql"""
      SELECT DISTINCT ON(es.email_address_host)
      es.email_address_host ,
      t.org_id, es.email,
      rs.host,rs.public_ip,
      rs.domain_blacklist_failure_description,
      rs.domain_blacklist_full_result,
      rs.domain_blacklist_status,
      rs.ip_blacklist_failure_description,
      rs.ip_blacklist_full_result,
      rs.ip_blacklist_status,
      rs.overall_blacklist_status,
      rh.host_url
     FROM email_settings es
     JOIN teams t ON t.id = es.team_id
     JOIN organizations org ON t.org_id = org.id
     JOIN rep_mail_servers rs ON org.rep_mail_server_id = rs.id
     JOIN rep_tracking_hosts rh ON org.rep_tracking_host_id = rh.id
     WHERE es.last_blacklist_checked_result = ${BlacklistCheckStatus.FAILED.toString}
     AND es.email_address_host = ${domain.emailAddressHost}
     AND last_blacklist_checked_at > now() - interval '24 hours';
     """
        .map(BlackListedCustomerDomainDetails.fromDb)
        .single
        .apply()
    }

  }


  def getCampaignIdsByEmailSettingsId(es_id: Int): Try[List[Long]] = Try {
    DB.readOnly { implicit session =>
      sql"""
            SELECT ces.campaign_id FROM campaign_email_settings ces WHERE ces.sender_email_setting_id = ${es_id};
         """
        .map(_.long("campaign_id"))
        .list
        .apply()
    }
  }


  def schedulerEmailForDelete(
                               emailSettingId: EmailSettingId,
                               teamId: TeamId
                             )(
                               using logger: SRLogger,
                             ): Try[Int] = Try {

    logger.debug(s"EmailSettingDAO.schedulerEmailForDelete updating")

    val res = DB autoCommit { implicit session =>
      sql"""
           update email_settings
           set
           email = CONCAT('srdelete+', '${SQLSyntax.createUnsafely(StringUtils.genRandomAlphaNumericString30Chars)}', email),
           status = ${EmailSettingStatus.ScheduledForDeletion.toString},
           scheduled_for_deletion_at = now()
           where
           id = ${emailSettingId.emailSettingId}
           and team_id = ${teamId.id}
           and status = ${EmailSettingStatus.Active.toString}

         """
        .update
        .apply()
    }

    logger.debug(s"EmailSettingDAO.schedulerEmailForDelete done: $res")

    res

  }

  def scheduleEmailForDeletion(
                                        emailSettingId: EmailSettingId,
                                        teamId: TeamId,
                                        schedule_for_deletion_at: DateTime
                                      )(
                                        using logger: SRLogger,
                                      ): Try[Int] = Try {

    logger.debug(s"EmailSettingDAO.schedulerEmailForDelete updating")

    val res = DB autoCommit { implicit session =>
      sql"""
             update email_settings
             set
             platform_purchased_email_status = ${PurchaseDomainsAndEmailsStatus.SCHEDULED_FOR_DELETION.toString},
             scheduled_for_deletion_at = $schedule_for_deletion_at
             where
             id = ${emailSettingId.emailSettingId}
             and team_id = ${teamId.id}
             and status = ${EmailSettingStatus.Active.toString}

           """
        .update
        .apply()
    }

    logger.debug(s"EmailSettingDAO.schedulerEmailForDelete done: $res")

    res

  }

  def fetchEmailsForDeletion(): Try[List[EmailForDeletionData]] = Try {

    DB.readOnly { implicit session =>

      sql"""
           SELECT
             team_id,
             account_id,
             id,
             platform_email_id,
             domain_provider
           FROM
             email_settings
           WHERE
           scheduled_for_deletion_at < now()
           and platform_purchased_email_status = ${PurchaseDomainsAndEmailsStatus.SCHEDULED_FOR_DELETION.toString}
         """
        .map(rs => EmailForDeletionData(
          team_id = TeamId(id = rs.long("team_id")),
          account_id = AccountId(id = rs.long("account_id")),
          email_setting_id = rs.long("id"),
          domain_provider = PlatformType.fromString(rs.string("domain_provider")).get,
        )
        )
        .list
        .apply()
    }
  }


  def getSenderEmails(
                       teamId: TeamId,
                       campaignId: Option[CampaignId]
                     ): Try[List[String]] = Try {

    val whereClause = campaignId match {
      case Some(cid) =>
        sqls"WHERE ces.campaign_id = ${cid.id} AND ces.team_id = ${teamId.id}"

      case None =>
        sqls"WHERE ces.team_id = ${teamId.id}"
    }
    DB readOnly { implicit session =>
      sql"""
                  SELECT es.email
                  FROM email_settings es
                  JOIN campaign_email_settings ces ON es.id = ces.sender_email_setting_id AND es.team_id = ces.team_id

                  ${whereClause}
               """
        .map(rs => rs.string("email"))
        .list
        .apply()

    }


  }


  def getDomainsForBlacklistCheck: Try[List[EmailAddressHost]] = Try {

    DB readOnly { implicit session =>
      sql"""
                 SELECT DISTINCT ON(es.email_address_host) es.email_address_host
                 FROM email_settings es
                 JOIN campaign_email_settings ces ON es.id = ces.sender_email_setting_id AND es.team_id = ces.team_id
                 JOIN campaigns c ON c.id = ces.campaign_id AND c.team_id = ces.team_id
                 LEFT JOIN domain_health_checks dhc ON dhc.domain = es.email_address_host
                 WHERE c.status = ${CampaignStatus.RUNNING.toString}
                 AND (dhc.last_blacklist_check_at is null OR dhc.last_blacklist_check_at < now() - INTERVAL '1 week')
                 AND (dhc.pushed_to_queue is null  OR dhc.pushed_to_queue = false OR (dhc.pushed_to_queue = true AND dhc.last_pushed_to_queue_at < now() - INTERVAL '6 hours'))
                 LIMIT 100
                 ;
               """
        .map(rs => EmailAddressHost(rs.string("email_address_host")))
        .list
        .apply()

    }

  }


//  def getEmailAndProspectCatDataForMigration(
//                                              from_time: DateTime, // '2024-06-19 07:00:00'
//                                              till_time: DateTime // '2024-06-19 16:00:00'
//
//                                            ): Try[List[EmailAndProspectCatDetailsForMigration]] = Try {
//
//    DB.readOnly(implicit session => {
//
//      sql"""
//
//      SELECT
//            es.team_id,
//            es.prospect_id,
//            es.reply_type,
//            es.sent_at,
//            p.prospect_category_id_custom,
//            pcc.text_id as current_prospect_text_id,
//            es.inbox_email_setting_id,
//            em.account_id as owner_id
//      FROM
//        emails_scheduled es
//        INNER JOIN prospects p ON (p.team_id = es.team_id
//            AND p.id = es.prospect_id)
//        INNER JOIN prospect_categories_custom pcc ON (pcc.id = p.prospect_category_id_custom
//              AND pcc.text_id = ${ProspectCategory.NOT_CATEGORIZED.toString})
//        INNER JOIN email_settings em ON (es.inbox_email_setting_id = em.id and em.team_id = es.team_id)
//        WHERE
//          es.prospect_id IS NOT NULL
//          AND es.sent_at BETWEEN ${from_time} AND ${till_time}
//          AND (es.reply_type = ${EmailReplyType.AUTO_REPLY.toString}
//            OR es.reply_type = ${EmailReplyType.DELIVERY_FAILED.toString})
//          AND es.scheduled_from_campaign
//         """
//        .map(rs => EmailAndProspectCatDetailsForMigration(
//          prospect_id = ProspectId(id = rs.long("prospect_id")),
//          team_id = TeamId(id = rs.long("team_id")),
//          owner_id = AccountId(id = rs.long("owner_id")),
//          reply_type = rs.string("reply_type"),
//          sent_at = rs.jodaDateTime("sent_at"),
//          current_prospect_cat_id = ProspectCategoryId(id = rs.long("prospect_category_id_custom")),
//          current_prospect_cat_text_id = rs.string("current_prospect_text_id"),
//          inbox_email_setting_id = EmailSettingId(emailSettingId = rs.long("inbox_email_setting_id"))
//        ))
//        .list
//        .apply()
//
//    })
//
//
//  }

  def insertEmailSettingIntegrationAttempt(
                                            orgId: OrgId,
                                            teamId: TeamId,
                                            accountId: AccountId,
                                            email_address: Option[String],
                                            serviceProvider: Option[String],
                                            completed: Boolean,
                                            email_integration_stage: EmailSettingIntegrationLogsStage,
                                            error_occurred: Option[String]
                                          )(using logger: SRLogger): Try[Option[Long]] = Try {

    val uuid = srUuidUtils.generateEmailSettingAttemptUuid()
    DB.autoCommit { implicit session =>


      sql"""
           INSERT INTO email_setting_integration_attempt_logs

           (
            uuid,
            org_id,
            team_id,
            account_id,

            email_address,
            service_provider,

            completed,
            error_occurred,
            email_integration_stage,
            log_trace_id
           )

           VALUES (
            $uuid,
            ${orgId.id},
            ${teamId.id},
            ${accountId.id},

            $email_address,
            $serviceProvider,

            $completed,
            $error_occurred,
            ${email_integration_stage.toString},
            ${logger.logTraceId}
           )
           RETURNING id

         """
        .map(rs => rs.long("id"))
        .single
        .apply()

    }
  }

  def getPlatformEmailIdFromEmailSettingId(
                                            emailSettingId: EmailSettingId,
                                            teamId: TeamId
                                          ): Try[Option[String]] = Try {
    DB.readOnly { implicit session =>

      sql"""
         SELECT
           platform_email_id
         FROM
           email_settings
         WHERE
           id = ${emailSettingId.emailSettingId}
           AND team_id = ${teamId.id}
      """
        .map(rs => rs.stringOpt("platform_email_id"))
        .single
        .apply()
        .get
    }
  }

  def getEmailSettingIdsFromZapmailOrderIds(
                                             zapmailOrderIds: List[String],
                                             teamId: TeamId
                                             ): Try[List[EmailSettingId]] = Try {

    DB readOnly { implicit session =>

      sql"""
         SELECT
           es.id as email_setting_id
         FROM
           email_settings es
         WHERE
           es.email_domain IN (
              SELECT pd.domain_name
              FROM purchased_domains pd
              WHERE pd.zapmail_order_id IN ($zapmailOrderIds)
              AND pd.platform_type = ${PlatformType.ZAPMAIL.toString}
              AND pd.team_id = ${teamId.id}
           )
           AND es.platform_purchased_email_status IN (
            ${PurchaseDomainsAndEmailsStatus.ACTIVE.toString},
            ${PurchaseDomainsAndEmailsStatus.SETUP_INITIATED.toString},
            ${PurchaseDomainsAndEmailsStatus.SETUP_PENDING.toString}
           )
           AND es.team_id = ${teamId.id}
      """
        .map(rs => EmailSettingId(emailSettingId = rs.long("email_setting_id")))
        .list
        .apply()
    }
  }

  def getEmailSettingIdsFromPlatformDomainIds(
                                               platformDomainIds: List[String],
                                               teamId: TeamId
                                             ): Try[List[EmailSettingId]] = Try {

    DB readOnly { implicit session =>

      sql"""
         SELECT
           es.id as email_setting_id
         FROM
           email_settings es
         WHERE
           es.email_domain IN (
              SELECT pd.domain_name
              FROM purchased_domains pd
              WHERE pd.platform_domain_id IN ($platformDomainIds)
              AND pd.platform_type = ${PlatformType.MAILDOSO.toString}
              AND pd.team_id = ${teamId.id}
           )
           AND es.platform_purchased_email_status IN (
            ${PurchaseDomainsAndEmailsStatus.ACTIVE.toString},
            ${PurchaseDomainsAndEmailsStatus.SETUP_INITIATED.toString},
            ${PurchaseDomainsAndEmailsStatus.SETUP_PENDING.toString}
           )
           AND es.team_id = ${teamId.id}
      """
        .map(rs => EmailSettingId(emailSettingId = rs.long("email_setting_id")))
        .list
        .apply()
    }
  }


  def getEmailSettingIdsFromPurchaseDomainUuids(
                                               purchaseDomainUuids: List[String],
                                               teamId: TeamId
                                             ): Try[List[EmailSettingId]] = Try {

    DB readOnly { implicit session =>

      sql"""
         SELECT
           es.id as email_setting_id
         FROM
           email_settings es
         WHERE
           es.email_domain IN (
              SELECT pd.domain_name
              FROM purchased_domains pd
              WHERE pd.purchased_domain_uuid IN ($purchaseDomainUuids)
              AND pd.team_id = ${teamId.id}
           )
           AND es.platform_purchased_email_status IN (
            ${PurchaseDomainsAndEmailsStatus.ACTIVE.toString},
            ${PurchaseDomainsAndEmailsStatus.SETUP_INITIATED.toString},
            ${PurchaseDomainsAndEmailsStatus.SETUP_PENDING.toString}
           )
           AND es.team_id = ${teamId.id}
      """
        .map(rs => EmailSettingId(emailSettingId = rs.long("email_setting_id")))
        .list
        .apply()
    }
  }



  def checkEmailSettingExists(
                               emailSettingId: EmailSettingId,
                               teamId: TeamId
                             ): Try[Boolean] = Try {

    DB readOnly { implicit session =>

      sql"""
         SELECT EXISTS (
           SELECT
             *
           FROM
             email_settings es
           WHERE
             es.id = ${emailSettingId.emailSettingId}
             AND es.status = ${EmailSettingStatus.Active.toString}
             AND es.team_id = ${teamId.id}
         );
      """
        .map(rs => rs.boolean("exists"))
        .single
        .apply()
        .get
    }
  }

  def getDomainPurchasedInfoFromEmailSettingId(
                                                      emailSettingId: EmailSettingId,
                                                      teamId: TeamId
                                                    ): Try[PurchasedEmailInfo] = Try {
    DB readOnly { implicit session =>
      sql"""
      SELECT email,email_domain,domain_provider
      FROM email_settings es
      WHERE
        es.id = ${emailSettingId.emailSettingId}
        AND es.status = ${EmailSettingStatus.Active.toString}
        AND es.team_id = ${teamId.id}
      """
        .map(rs =>

          PurchasedEmailInfo(
            email = rs.string("email"),
            domain = rs.string("email_domain"),
            platformType = PlatformType.fromString(rs.string("domain_provider")).get
          )
        )
        .single
        .apply()
        .get
    }
  }

  def updatePlatformEmailStatusForSettingPushedToMq(
                                                     emailSettingId: EmailSettingId,
                                                     teamId: TeamId,
                                                     platform_email_status: PurchaseDomainsAndEmailsStatus
                                                   ): Try[Int] = Try {

    DB.autoCommit { implicit session =>
      sql"""
           UPDATE email_settings
           SET platform_purchased_email_status = ${platform_email_status.toString}
           WHERE id = ${emailSettingId.emailSettingId}
           AND team_id = ${teamId.id};
        """
        .update
        .apply()
    }

  }


  def updateEmailSettingStatus(
                                worker_task_id: String,
                                teamId: TeamId,
                                status: EmailSettingStatus
                              ): Try[Int] = Try {
    DB.autoCommit { implicit session =>
      sql"""
         UPDATE email_settings
         SET status = ${status.toString},
          platform_purchased_email_status = ${PurchaseDomainsAndEmailsStatus.ACTIVE.toString}
         WHERE platform_purchased_email_task_id = ${worker_task_id}
         AND team_id = ${teamId.id};
      """
        .update
        .apply()
    }
  }

  def updateZapMailEmailSettingStatus(
                                       worker_task_id: String,
                                       teamId: TeamId,
                                       status: EmailSettingStatus,
                                       email: String,
                                       new_password: String,
                                       subscriptionId: ZapMailSubscriptionID
                                     ): Try[Int] = Try {
    DB.autoCommit { implicit session =>
      sql"""
         UPDATE email_settings
         SET status = ${status.toString},
          platform_purchased_email_status = ${PurchaseDomainsAndEmailsStatus.ACTIVE.toString},
          platform_purchased_email_task_id = ${subscriptionId.toString},
          smtp_password_enc = ${EncryptionHelpers.encryptEmailSettingCredential(new_password)},
          imap_password_enc = ${EncryptionHelpers.encryptEmailSettingCredential(new_password)}
         WHERE platform_email_id = ${worker_task_id}
         AND domain_provider = ${PlatformType.ZAPMAIL.toString}
         AND team_id = ${teamId.id}
         AND email = ${email};
      """
        .update
        .apply()
    }
  }

  def updateZapMailEmailSettingStatusForTestApp(
                                       status: EmailSettingStatus,
                                       teamId: TeamId,
                                       email: String,
                                       new_password: String,
                                       subscriptionId: ZapMailSubscriptionID
                                     ): Try[Int] = Try {
    DB.autoCommit { implicit session =>
      sql"""
           UPDATE email_settings
           SET status = ${status.toString},
            platform_purchased_email_status = ${PurchaseDomainsAndEmailsStatus.ACTIVE.toString},
            platform_purchased_email_task_id = ${subscriptionId.toString},
            smtp_password_enc = ${EncryptionHelpers.encryptEmailSettingCredential(new_password)},
            imap_password_enc = ${EncryptionHelpers.encryptEmailSettingCredential(new_password)}
           WHERE domain_provider = ${PlatformType.ZAPMAIL.toString}
           AND team_id = ${teamId.id}
           AND email = ${email};
        """
        .update
        .apply()
    }
  }

  def updatePurchasedEmailSettingsTaskId(
                                emails: List[String],
                                teamId: TeamId,
                                platformTaskId: String
                              ): Try[Int] = Try {
    DB.autoCommit { implicit session =>
      sql"""
         UPDATE email_settings
         SET platform_purchased_email_task_id = ${platformTaskId},
             platform_purchased_email_status = ${PurchaseDomainsAndEmailsStatus.SETUP_INITIATED.toString}
         WHERE email in (${emails})
         AND team_id = ${teamId.id}
      """
        .update
        .apply()
    }
  }

    def getDistinctTagsForTeam(teamId: TeamId): Try[List[String]] = Try {
        DB readOnly { implicit session =>
            sql"""
         SELECT DISTINCT email_tag
         FROM email_settings
         WHERE team_id = ${teamId.id}
           AND email_tag IS NOT NULL
           AND email_tag != ''
           AND status = ${EmailSettingStatus.Active.toString}
         ORDER BY email_tag ASC
       """
              .map(_.string("email_tag"))
              .list
              .apply()
        }
    }

  def checkIfEmailExists(
                          emails: List[String],
                          teamId: TeamId
                        ): Try[Boolean] = Try {

    DB readOnly { implicit session =>
      sql"""
       SELECT EXISTS (
         SELECT
           *
         FROM
           email_settings es
         WHERE
           es.email IN ($emails)
           AND es.team_id = ${teamId.id}
       );
    """
        .map(rs => rs.boolean("exists"))
        .single
        .apply()
        .get
    }

  }

    def updateEmailSettingsTags(data: EmailSettingBulkTagUpdate, teamId: TeamId): Try[List[EmailSetting]] = Try {
    if (data.es_ids.isEmpty) {
      List.empty
    } else {
      val emailSettingIds = data.es_ids.map(_.emailSettingId)
      
      val esIds = DB autoCommit { implicit session =>
        val tagValue = data.tag.orNull
        
        sql"""
          UPDATE email_settings
          SET email_tag = ${tagValue.toLowerCase}
          WHERE id IN ${SQLUtils.generateSQLValuesClause(arr = emailSettingIds)}
          AND team_id = ${teamId.id}
          RETURNING id;
        """
          .map(_.long("id"))
          .list
          .apply()
      }
      
      esIds.flatMap(id => find(id = id))
    }
  }

}

case class EmailAndProspectCatDetailsForMigration(
                                                 prospect_id: ProspectId,
                                                 team_id: TeamId,
                                                 owner_id: AccountId,
                                                 reply_type: String,
                                                 sent_at: DateTime,
                                                 current_prospect_cat_id: ProspectCategoryId,
                                                 current_prospect_cat_text_id: String,
                                                 inbox_email_setting_id: EmailSettingId)

case class EmailSettingBulkTagUpdate(
  es_ids: Seq[EmailSettingId],
  tag: Option[String]  // None means remove tag, Some("tag") means set tag
)

object EmailSettingBulkTagUpdate {
  given reads: Reads[EmailSettingBulkTagUpdate] = {
    implicit val emailSettingIdListReads: Reads[Seq[EmailSettingId]] =
      Reads.seq[Long].map(_.map(EmailSettingId.apply))
    
    Json.reads[EmailSettingBulkTagUpdate]
  }
}
