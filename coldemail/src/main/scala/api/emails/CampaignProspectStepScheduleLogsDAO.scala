package api.emails

import api.accounts.{EmailScheduledIdOrTaskId, ScheduleType, TeamId}
import api.campaigns.models.CampaignStepType
import api.campaigns.services.CampaignId
import api.emails.models.CampaignProspectStepScheduleEventType
import api.prospects.models.{ProspectId, StepId}
import org.joda.time.DateTime
import scalikejdbc.interpolation.SQLSyntax
import scalikejdbc.{DB, DBSession, scalikejdbcSQLInterpolationImplicitDef}
import utils.SRLogger
import utils.mq.channel_scheduler.channels.StepType

import scala.util.Try

case class CampaignProspectStepScheduleLogData(
                                                campaignProspectStepScheduleEventType: CampaignProspectStepScheduleEventType,
                                                emailScheduledIdOrTaskId: EmailScheduledIdOrTaskId,
                                                team_id: TeamId,
                                                c_id: CampaignId,
                                                p_id: ProspectId,
                                                step_id: String,
                                                step_type: Option[CampaignStepType]
                                              )

case class CampaignProspectStepScheduleLogDataForIgnore(
                                                         team_id: TeamId,
                                                         c_id: CampaignId,
                                                         p_id: ProspectId,
                                                         step_id: String,
                                                         step_type: Option[CampaignStepType],
                                                         request_log_id: String,
                                                         rejection_reason: RejectionReasonForCampaignProspectStepSchedule
                                                       )

sealed trait RejectionReasonForCampaignProspectStepSchedule {
  def next_check_for_scheduling_at: DateTime
}

object RejectionReasonForCampaignProspectStepSchedule {
  case class StepDelayNotMet(
                              next_check_for_scheduling_at: DateTime
                            ) extends RejectionReasonForCampaignProspectStepSchedule

  case class StepIdNotFound(
                             next_check_for_scheduling_at: DateTime
                           ) extends RejectionReasonForCampaignProspectStepSchedule

  case class NoStepMatched(
                             next_check_for_scheduling_at: DateTime
                          ) extends RejectionReasonForCampaignProspectStepSchedule

  case class LastStep(
                       next_check_for_scheduling_at: DateTime
                     ) extends RejectionReasonForCampaignProspectStepSchedule

  case class NoProspectFound(
                              next_check_for_scheduling_at: DateTime
                            ) extends RejectionReasonForCampaignProspectStepSchedule

  case class FailedToParseOutput(
    next_check_for_scheduling_at: DateTime,
  ) extends RejectionReasonForCampaignProspectStepSchedule

}

class CampaignProspectStepScheduleLogsDAO {

  def insert(
              data: Seq[CampaignProspectStepScheduleLogData]
            )(using Logger: SRLogger): Try[List[Long]] = {

    DB autoCommit { implicit session =>
      insertWithSession(data = data)
    }
  }


  def insertWithSession(
                         data: Seq[CampaignProspectStepScheduleLogData]
                       )(using Logger: SRLogger, session: DBSession): Try[List[Long]] = Try {
    if (data.isEmpty) {
      List()
    } else {
      data
        .groupBy(_.emailScheduledIdOrTaskId.scheduleType)
        .flatMap { emailData =>
          var parameters = List[Any]()
          val (scheduleType, emails) = emailData
          val idSql: SQLSyntax = scheduleType match {
            case ScheduleType.EmailSchedule => sqls"email_scheduled_id,"
            case ScheduleType.TaskSchedule => sqls"task_id,"
          }
          val valuePlaceholder = emails.map(email => {

            val emailScheduleOrTaskId = email.emailScheduledIdOrTaskId match {
              case EmailScheduledIdOrTaskId.TaskId(id) => id
              case EmailScheduledIdOrTaskId.EmailScheduledId(id) => id
            }
            parameters = parameters ::: List(
              Logger.logTraceId,
              email.campaignProspectStepScheduleEventType.toString,
              emailScheduleOrTaskId,
              email.team_id.id,

              email.c_id.id,
              email.p_id.id,
              email.step_id,
              email.step_type.map(_.toKey)
            )

            sqls"""
          (
            ?,
            ?,
            ?,
            ?,

            ?,
            ?,
            ?,
            ?
          )

        """

          }).reduce((vp1, vp2) => sqls"$vp1, $vp2")
          val query =
            sql"""
                  INSERT INTO campaign_prospect_step_schedule_logs
                  (
                     audit_request_log_id,
                     email_schedule_event_type,
                     $idSql
                     team_id,
                     campaign_id,
                     prospect_id,
                     step_id,
                     step_type
                  )
                 VALUES $valuePlaceholder

                  RETURNING id;
                """

          query
            .bind(parameters*)
            .map(rs => rs.long("id"))
            .list
            .apply()
        }.toList


    }
  }


  def insertForIgnore(
                       data: Seq[CampaignProspectStepScheduleLogDataForIgnore],
                       shouldMarkCompleted: Boolean,
                     )(using Logger: SRLogger): Try[List[Long]] = Try {
    if (data.isEmpty) {
      List()
    } else {

      DB autoCommit { implicit session =>

        var parameters = List[Any]()

        val valuePlaceholder = data.map(ignored => {

          val eventType = if (shouldMarkCompleted) {
            CampaignProspectStepScheduleEventType.MarkedCompleted
          } else {
            CampaignProspectStepScheduleEventType.Ignored
          }

          parameters = parameters ::: List(
            Logger.logTraceId,
            eventType.toString,
            ignored.rejection_reason.toString,
            ignored.team_id.id,

            ignored.c_id.id,
            ignored.p_id.id,
            ignored.step_id,

            ignored.step_type.map(_.toKey)
          )

          sqls"""
          (
            ?,
            ?,
            ?,
            ?,

            ?,
            ?,
            ?,

            ?
          )

        """

        }).reduce((vp1, vp2) => sqls"$vp1, $vp2")
        val query =
          sql"""
                  INSERT INTO campaign_prospect_step_schedule_logs
                  (
                     audit_request_log_id,
                     email_schedule_event_type,
                     rejected_reason,
                     team_id,

                     campaign_id,
                     prospect_id,
                     step_id,

                     step_type
                  )
                 VALUES $valuePlaceholder

                  RETURNING id;
                """

        query
          .bind(parameters*)
          .map(rs => rs.long("id"))
          .list
          .apply()
      }

    }
  }


}
