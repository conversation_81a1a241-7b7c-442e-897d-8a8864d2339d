package api.emails

import api.AppConfig
import api.accounts.EmailScheduledIdOrTaskId.{EmailScheduledId, TaskId}
import api.accounts.*
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.*
import api.campaigns.dao.InboxPlacementCheckTrackedEmails
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepType, NewProspectStatus, PreviousFollowUpData, SendEmailFromCampaignDetails, StepDetails}
import api.campaigns.services.CampaignId
import api.emails.models.{AssociateAndUpdateCampaignProspectData, DeleteAndRevertDataFromDB, DeleteAndRevertEmailScheduledFromDB, DeleteAndRevertTaskFromDB, EmailDataForCheckingBotClick, EmailMessageContactModel, EmailSendingFlow, MessageContactType, ProspectEmailMessage, StuckScheduledEmailForDeletion, UpdateEmailScheduledFromExtensionData, EmailReplyType}
import api.integrations.ZapierFindOneForEmail
import api.phantombuster.LinkedinMessageId
import api.prospects.*
import api.prospects.dao.{ProspectAddEventDAO, ProspectIdAndPotentialDuplicateProspectId}
import api.prospects.dao_service.ProspectEmailsDAOService
import api.prospects.models.{NewProspectAssociateWithCampaignData, ProspectAccountsId, ProspectCategory, ProspectId, StepId}
import api.prospects.service.ProspectServiceV2
import api.team_inbox.model.FolderType
import io.sr.billing_common.models.PlanType
import eventframework.MessageObject
import org.joda.time.DateTime
import org.postgresql.util.PGobject
import play.api.Logging
import play.api.libs.json.{JsValue, Json, Reads}
import scalikejdbc.interpolation.SQLSyntax
import scalikejdbc.{AutoSession, DB, SQLSyntax, WrappedResultSet, *}
import scalikejdbc.jodatime.JodaWrappedResultSet.*
import sr_scheduler.CampaignStatus
import sr_scheduler.models.EmailScheduledNew
import utils.{Helpers, SRLogger, StringUtils}
import utils.dbutils.{DBUtils, SQLUtils}
import utils.email.*
import utils.email.services.InternalTrackingNote
import utils.security.EncryptionHelpers
import api.sr_audit_logs.models.EventType
import eventframework.MessageObject.EmailMessageObject
import api.tasks.models.{RevertData, TaskCreatedVia, TaskStatus, TaskStatusType}
import io.smartreach.esp.api.emailApi.models.ESPMailgunRegion
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import io.smartreach.esp.utils.email.InternetMsgIDForFetchOutlookMsgId
import utils.cronjobs.ResultForSenderRunAlert
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus
import utils.email.models.DeleteEmailsScheduledType
import utils.mq.services.MQDoNotNackException
import utils.uuid.SrUuidUtils

import scala.util.{Failure, Success, Try}
import scala.concurrent.blocking

case class OptedOutEmailDetails(
                                 campaign_name: Option[String],
                                 campaign_id: Option[Long],

                                 add_prospect_to_dnc_on_opt_out: Boolean,
                                 account_name: String,
                                 account_id: Long,
                                 team_id: Long,
                                 ta_id: Long,
                                 prospect_id: Long,
                                 prospect_email: String
                               )



case class EmailScheduledNewStep2(
                                   email_scheduled_id: Long,
                                   subject: String,
                                   body: String,
                                   base_body: String,
                                   text_body: String,

                                   has_open_tracking: Boolean,
                                   has_click_tracking: Boolean,
                                   has_unsubscribe_link: Boolean,
                                   is_edited_preview_email: Boolean,

                                   list_unsubscribe_header: Option[String],
                                   gmail_fbl: Option[String]
                                 )

case class EmailScheduledNewAfterSaving(
                                         email_scheduled_id: Long,
                                         campaign_id: Option[Long],
                                         step_id: Option[Long],
                                         prospect_id: Option[Long], // can be None for test emails
                                         to_email: String,
                                         reply_to_email: Option[String],
                                         step_type: CampaignStepType,
                                         from_email: String,
                                         added_at: DateTime,
                                         scheduled_at: DateTime,
                                         sender_email_settings_id: Long,
                                         template_id: Option[Long],
                                         variant_id: Option[Long],
                                         rep_mail_server_id: Int,
                                         campaign_email_setting_id: CampaignEmailSettingsId,
                                         team_id: TeamId,
                                         to_name: Option[String],
                                         from_name: String,
                                         reply_to_name: Option[String],
                                         body: Option[String],
                                         base_body: Option[String],
                                         text_body: Option[String],
                                         subject: Option[String]

                                       )

// todo newinbox change from_email, reply_to_email, cc_email type to IEmailAddress
case class EmailMessageTracked(
                                inbox_email_setting_id: Long,
                                from: IEmailAddress,


                                // v3 specific start
                                to_emails: Seq[IEmailAddress], // only for newinbox api
                                // v3 specific end


                                subject: String,

                                body: String,
                                base_body: String,
                                text_body: String,

                                references_header: Option[String],

                                campaign_id: Option[Long],
                                step_id: Option[Long],

                                prospect_id_in_campaign: Option[Long], // prospect_id for whom campaign should be paused

                                prospect_account_id_in_campaign: Option[Long],

                                campaign_name: Option[String],
                                step_name: Option[String],

                                received_at: DateTime,
                                recorded_at: DateTime,
                                sr_inbox_read: Boolean,
                                original_inbox_folder: Option[String],
                                email_status: EmailReplyStatus,

                                message_id: String,
                                full_headers: JsValue,

                                scheduled_manually: Boolean,

                                reply_to: Option[IEmailAddress],

                                email_thread_id: Option[Long],
                                gmail_msg_id: Option[String],
                                gmail_thread_id: Option[String],

                                outlook_msg_id: Option[String],
                                outlook_conversation_id: Option[String],
                                outlook_response_json: Option[JsValue],

                                cc_emails: Seq[IEmailAddress],
                                in_reply_to_header: Option[String],
                                team_id: Long,
                                account_id: Long,


                                internal_tracking_note: InternalTrackingNote.Value,

                                tempThreadId: Option[Int] // used temporarily for identifying email threads during reply tracking

                              )


case class EmailThreadFoundForCheckingReplies(
                                               email_thread_id: Long,
                                               message_id: Option[String],
                                               gmail_thread_id: Option[String],
                                               outlook_conversation_id: Option[String],
                                               primary_prospect_id: Option[Long],
                                               primary_prospect_account_id: Option[Long],
                                               step_id: Option[Long],
                                               campaign_id: Option[Long]
                                             )

object EmailThreadFoundForCheckingReplies {

  def fromDb(rs: WrappedResultSet): EmailThreadFoundForCheckingReplies = {
    EmailThreadFoundForCheckingReplies(
      email_thread_id = rs.long("email_thread_id"),
      message_id = rs.stringOpt("message_id"),
      gmail_thread_id = rs.stringOpt("gmail_thread_id"),
      outlook_conversation_id = rs.stringOpt("outlook_conversation_id"),
      primary_prospect_id = rs.longOpt("prospect_id"),
      primary_prospect_account_id = rs.longOpt("prospect_account_id"),
      step_id = rs.longOpt("step_id"),
      campaign_id = rs.longOpt("campaign_id")
    )
  }

}


case class EmailScheduledIdsFoundForSendingViaMQ(
                                                  emailScheduledId: Long,
                                                  sendingServerIP: String,
                                                  service_provider: EmailServiceProvider,
                                                  teamId: TeamId
                                                )

case class SendNewManualEmailV2(

                                 email_message_id: Option[Long] = None,

                                 email_thread_id: Option[Long] = None, // TODO: uuid value class

                                 sender_email_setting_id: Long,
                                 receiver_email_setting_id: Option[Long],

                                 // from: IEmailAddress,
                                 // reply_to: Option[IEmailAddress],

                                 to: Seq[IEmailAddress],

                                 task_id: Option[String] = None,

                                 cc_emails: Option[Seq[IEmailAddress]] = None,
                                 bcc_emails: Option[Seq[IEmailAddress]] = None,

                                 body: String,
                                 subject: String,

                                 enable_open_tracking: Option[Boolean] = None,
                                 enable_click_tracking: Option[Boolean] = None,
                                 mark_as_done: Option[Boolean] = None
                               )
object SendNewManualEmailV2 {
  given reads: Reads[SendNewManualEmailV2] = Json.reads[SendNewManualEmailV2]
}


case class SendNewManualEmailV3(

                                 email_thread_id: Option[String] = None,

                                 // Extension flow : campaign step id will come from extension flow, while running manual email tasks
                                 campaign_step_id: Option[Long] = None,

                                 sender_email_setting_id: Long,
                                 receiver_email_setting_id: Option[Long],

                                 // from: IEmailAddress,
                                 // reply_to: Option[IEmailAddress],

                                 to: Seq[IEmailAddress],

                                 cc_emails: Option[Seq[IEmailAddress]] = None,
                                 bcc_emails: Option[Seq[IEmailAddress]] = None,

                                 body: String,
                                 subject: String,

                                 enable_open_tracking: Option[Boolean] = None,
                                 enable_click_tracking: Option[Boolean] = None,
                                 mark_as_done: Option[Boolean] = None
                               )
object SendNewManualEmailV3 {
  given reads: Reads[SendNewManualEmailV3] = Json.reads[SendNewManualEmailV3]
}

// PROSPECTCOLUMNCHANGETAG
case class EmailReceivedForWebhook(
                                    id: Long,

                                    campaign_name: Option[String],

                                    prospect_id: Long,
                                    email: String,
                                    first_name: Option[String],
                                    last_name: Option[String],
                                    prospect_list: Option[String],
                                    custom_fields: JsValue,
                                    company: Option[String],
                                    city: Option[String],
                                    country: Option[String],

                                    subject: String,
                                    body: String,
                                    body_text: String,
                                    email_thread_id: Option[Long],
                                    from_email: String,
                                    received_at: DateTime,
                                    step_name: Option[String],

                                    sent_at: Option[DateTime],
                                    opened_at: Option[DateTime],
                                    clicked_at: Option[DateTime],
                                    bounced_at: Option[DateTime],
                                    replied_at: Option[DateTime],
                                    auto_reply_at: Option[DateTime],
                                    out_of_office_reply_at: Option[DateTime]
                                  )


case class EmailReceivedForWebhookV2(
                                      id: Long,
                                      campaign_name: Option[String],
                                      campaign_id: Option[Long],
                                      prospect_id: Long,

                                      subject: String,
                                      body: String,
                                      body_text: String,
                                      email_thread_id: Option[Long],
                                      from_email: String,
                                      to_email: String,
                                      sent_at: DateTime,
                                      step_name: Option[String],
                                      opened_at: Option[DateTime],
                                      clicked_at: Option[DateTime],
                                      replied_at: Option[DateTime]
                                    )


case class ERIntermediateValidProspect(
                                        prospect_id: Long,
                                        email: String,
                                        email_domain: String,
                                        account_id: Long, //prospect owner id
                                        team_id: Long,
                                        ta_id: Long,
                                        prospect_account_id: Option[Long], //prospect_acccount_id => table -> prospect_accounts.id
                                        prospect_category_id_custom: Long,
                                        last_contacted_at:Option[DateTime]
                                      )


case class EmailScheduledForMQTriggerPublish(
                                              email_scheduled_id: Long,
                                              prospect_id: Long,
                                              prospect_owner_id: Long
                                            )

case class EmailScheduled(
                           id: Option[Long],

                           subject: Option[String],
                           // body: Option[String],
                           // body_preview: Option[String],
                           message_id: Option[String],

                           references_header: Option[String],

                           sendEmailFromCampaignDetails: Option[SendEmailFromCampaignDetails],
                           prospect_id: Option[Long],
                           sender_email_settings_id: Option[Long],
                           // receiving_email_settings_id: Option[Long],
                           email_thread_id: Option[Long],
                           outlook_msg_id: Option[String],

                           scheduled_at: DateTime,
                           // added_at: DateTime,

                           // sent: Boolean,
                           sent_at: Option[DateTime],

                           account_id: Option[Long],
                           team_id: Option[Long]
                         )


case class EmailScheduledForCheckingReplies(

                                             /*
                                             NOTE:
                                             Sep-2020: this list will only contain emails that were scheduled_from_campaign, so there can be only one to_email as of now.
                                              */

                                             prospect_email: String,
                                             to_email: String,

                                             campaign_id: Option[CampaignId],
                                             step_id: Option[StepId],
                                             prospect_id: ProspectId,
                                             prospect_account_id: Option[ProspectAccountsId],
                                             message_id: Option[String],
                                             references_header: Option[String],
                                             subject: Option[String],
                                             sent_at: Option[DateTime],

                                             email_thread_id: Option[Long],
                                             gmail_thread_id: Option[String],
                                             outlook_msg_id: Option[String],
                                             outlook_conversation_id: Option[String]


                                             //  first_name: Option[String],
                                             //  last_name: Option[String],
                                             //  full_name: Option[String],
                                             //  company: Option[String]

                                           )

case class EmailReceivedForUpdateLeadStatus(
                                             id: Long,
                                             prospect_id: Long
                                           )

sealed trait MessageSentAtByProspect {
  def sent_at: DateTime

  def prospect_id: ProspectId
}

object MessageSentAtByProspect {
  case class EmailSentAtByProspect(
                                    sent_at: DateTime,
                                    email_scheduled_id: Long,
                                    prospect_id: ProspectId,
                                  ) extends MessageSentAtByProspect

  case class LinkedinMessageSentAtByProspect(
                                              sent_at: DateTime,
                                              linkedin_message_id: LinkedinMessageId,
                                              prospect_id: ProspectId
                                            ) extends MessageSentAtByProspect
}

case class CampaignIdAndProspectId(
                                    campaignId: CampaignId,
                                    prospectId: ProspectId,
                                    team_id :TeamId
                                  )

case class OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId(
                                                                       campaignId: Option[CampaignId],
                                                                       prospectId: Option[ProspectId],
                                                                       emailSettingId: Option[EmailSettingId],
                                                                       taskId: Option[TaskId],
                                                                       step_id: Option[StepId],
                                                                       team_id: TeamId
                                                                     )

case class InboxPlacementCheckSentEmailScheduledDetails(
                                                         emailScheduledId: Long,
                                                         sent_at: DateTime,
                                                         message_id: String
                                                       )

case class EmailHeaderForInboxPlacementCheckAnalysis(
                                                     emailScheduledId: Long,
                                                     emailHeaders: JsValue
                                                   )

case class EmailDetailsForHasOptedOut(
                                       bounced: Boolean,
                                       campaign_id: Option[CampaignId],
                                       prospect_id: Option[ProspectId],
                                       team_id: TeamId
                                     )

class EmailScheduledDAO(
                         emailSettingDAO: EmailSettingDAO,
                         emailThreadDAO: EmailThreadDAO,
                         //  accountDAO: AccountDAO,
                         prospectAddEventDAO: ProspectAddEventDAO,
                         campaignProspectDAO: CampaignProspectDAO,
                         //  prospectDAO: Prospect,
                         prospectEmailsDAOService: ProspectEmailsDAOService,
                         srUuidUtils: SrUuidUtils
                         //  emailMessageContactModel: EmailMessageContactModel
                       ) extends Logging {


  implicit val session: AutoSession.type = AutoSession


  def fromDb(rs: WrappedResultSet): EmailScheduled = {

    val isManual = rs.boolean("is_manual_task")

    val campaignStepType = if (isManual) {
      CampaignStepType.ManualEmailStep
    } else {
      CampaignStepType.AutoEmailStep
    }

    val stepDetails = rs.longOpt("step_id").map(
      stepId => {
        StepDetails(
          step_id = stepId,
          step_name = rs.string("step_name"),
          step_type = campaignStepType
        )
      })

    val sendEmailFromCampaignDetails = rs.longOpt("campaign_id").map(
      campaign_id => SendEmailFromCampaignDetails(
        campaign_id = campaign_id,
        campaign_name = rs.string("campaign_name"),
        stepDetails = stepDetails
      )
    )

    EmailScheduled(

      id = rs.longOpt("id"),

      subject = rs.stringOpt("subject"),
      // body = rs.stringOpt("body"),
      // body_preview = bodyPreview,


      message_id = rs.stringOpt("message_id"),
      references_header = rs.stringOpt("references_header"),

      sendEmailFromCampaignDetails = sendEmailFromCampaignDetails,
      prospect_id = rs.longOpt("prospect_id"),
      sender_email_settings_id = rs.longOpt("sender_email_settings_id"),
      // receiving_email_settings_id = rs.longOpt("receiving_email_settings_id"),
      email_thread_id = rs.longOpt("email_thread_id"),
      outlook_msg_id = rs.stringOpt("outlook_msg_id"),

      scheduled_at = rs.jodaDateTime("scheduled_at"),
      // added_at = rs.jodaDateTime("added_at"),

      // sent = rs.boolean("sent"),
      sent_at = rs.jodaDateTimeOpt("sent_at"),

      account_id = rs.longOpt("account_id"),
      team_id = rs.longOpt("team_id")

    )
  }


  def fromDbForWebhook(rs: WrappedResultSet): EmailReceivedForWebhook = EmailReceivedForWebhook(
    id = rs.long("id"),
    prospect_id = rs.long("prospect_id"),
    subject = rs.string("subject"),
    body = rs.string("body"),
    body_text = rs.string("text_body"),
    email_thread_id = rs.longOpt("email_thread_id"),
    from_email = rs.string("from_email"),
    campaign_name = rs.stringOpt("campaign_name"),
    step_name = rs.stringOpt("step_name"),

    email = rs.string("email"),
    first_name = rs.stringOpt("first_name"),
    last_name = rs.stringOpt("last_name"),
    prospect_list = rs.stringOpt("prospect_list"),
    company = rs.stringOpt("company"),
    city = rs.stringOpt("city"),
    country = rs.stringOpt("country"),
    custom_fields = Json.parse(rs.any("custom_fields").asInstanceOf[PGobject].getValue),

    received_at = rs.jodaDateTime("received_at"),


    sent_at = rs.jodaDateTimeOpt("sent_at"),
    opened_at = rs.jodaDateTimeOpt("opened_at"),
    clicked_at = rs.jodaDateTimeOpt("clicked_at"),
    bounced_at = rs.jodaDateTimeOpt("bounced_at"),
    replied_at = rs.jodaDateTimeOpt("replied_at"),
    auto_reply_at = rs.jodaDateTimeOpt("auto_reply_at"),
    out_of_office_reply_at = rs.jodaDateTimeOpt("out_of_office_reply_at")

  )


  def fromDbForWebhookV2(rs: WrappedResultSet): EmailReceivedForWebhookV2 = EmailReceivedForWebhookV2(
    id = rs.long("id"),
    prospect_id = rs.long("prospect_id"),
    subject = rs.stringOpt("subject").getOrElse(""),
    body = rs.stringOpt("body").getOrElse(""),
    body_text = rs.stringOpt("text_body").getOrElse(""),
    email_thread_id = rs.longOpt("email_thread_id"),
    from_email = rs.string("from_email"),
    to_email = rs.string("to_email"),
    campaign_name = rs.stringOpt("campaign_name"),
    campaign_id = rs.longOpt("campaign_id"),
    step_name = rs.stringOpt("step_name"),

    sent_at = rs.jodaDateTime("sent_at"),
    opened_at = rs.jodaDateTimeOpt("opened_at"),
    clicked_at = rs.jodaDateTimeOpt("clicked_at"),
    replied_at = rs.jodaDateTimeOpt("replied_at")
  )

  def fromDbForUpdateLeadStatus(rs: WrappedResultSet): EmailReceivedForUpdateLeadStatus =
    EmailReceivedForUpdateLeadStatus(
      id = rs.long("id"),
      prospect_id = rs.long("prospect_id")
    )

  def getStuckEmailsForSendingInLast15Mins(): Try[List[ResultForSenderRunAlert]] = Try {
    DB.readOnly { implicit session =>

      sql"""
        SELECT
          rep_mail_server_id,
          t.id as team_id,
          t.name as team_name,
          t.org_id,
          count(*) as count
        FROM
          emails_scheduled es
          JOIN teams t ON t.id = es.team_id
        WHERE
          es.scheduled_from_campaign
          AND es.scheduled_at > now() - interval '30 minutes'
          AND rep_mail_server_id IS NOT NULL
          AND es.pushed_to_rabbitmq = TRUE
          AND es.pushed_to_rabbitmq_at IS NOT NULL
          AND es.pushed_to_rabbitmq_at < now() - interval '15 minutes'
          AND NOT es.sent
        GROUP BY
          rep_mail_server_id,
          t.id
        LIMIT 100;
       """
        .map(rs => ResultForSenderRunAlert(
          rep_mail_server_id = rs.int("rep_mail_server_id"),
          team_id = TeamId(rs.long("team_id")),
          team_name =  rs.string("team_name"),
          org_id = OrgId(rs.long("org_id")),
          count = rs.int("count")
        ))
        .list
        .apply()

    }

  }

  def updateEmailScheduledSubjectAndBody(
                                          email_message_id: Long,
                                          team_id: TeamId,
                                          subject: String,
                                          text_body: String,
                                          base_body: String,
                                          sender_email_settings_id: Long,
                                          body: String  //html_body
                                        ): Try[UpdateEmailScheduledFromExtensionData] = Try {

    DB.autoCommit { implicit session =>

      sql"""
           UPDATE emails_scheduled
              SET
                  inbox_email_setting_id = ${sender_email_settings_id},
                  sender_email_settings_id =${sender_email_settings_id}
              WHERE id = $email_message_id
              AND team_id = ${team_id.id}
           RETURNING inbox_email_setting_id, prospect_id, campaign_id, step_id
         """
        .map(rs => UpdateEmailScheduledFromExtensionData(
          inbox_email_setting_id = EmailSettingId(
            emailSettingId = rs.long("inbox_email_setting_id")
          ),
          prospect_id = ProspectId(id = rs.long("prospect_id")),
          campaign_id = CampaignId(id = rs.long("campaign_id")),
          step_id = StepId(id = rs.long("step_id"))
          )
        )
        .single
        .apply()
        .get

    }

  }

  def updateEmailMessageDataSubjectAndBody(
                                          email_message_id: Long,
                                          team_id: TeamId,
                                          subject: String,
                                          text_body: String,
                                          base_body: String,
                                          body: String //html_body
                                        ) = Try {

    DB.autoCommit { implicit session =>

      sql"""
           UPDATE email_message_data
              SET subject = $subject,
                  text_body = $text_body,
                  base_body = $base_body,
                  body = $body
              WHERE es_id = $email_message_id
              AND team_id = ${team_id.id};
         """
        .update
        .apply()

    }

  }


  def checkIfEmailsAreGoingFromCampaignsInLastFifteenMin(

                                                        ): Try[Boolean] = Try {


    DB.readOnly { implicit session =>

      sql"""
        select exists (
          select

            *

          from emails_scheduled es

          where

            es.sent

            and

            es.scheduled_from_campaign

            and

            es.sent_at > now() - interval '15 minutes'

        )
          ;

       """
        .map(_.boolean("exists"))
        .single
        .apply()
        .getOrElse(false)

    }

  }

  def deleteUnsentEmailByCampaignIdAndProspectId(
                                                  campaignId: CampaignId,
                                                  prospectId: ProspectId,
                                                  teamId: TeamId
                                                ): Try[Int] = Try {

    DB autoCommit {implicit session =>
      sql"""
           DELETE FROM emails_scheduled
           WHERE sent = false
           AND campaign_id = ${campaignId.id}
           AND prospect_id = ${prospectId.id}
           AND team_id = ${teamId.id}
           ;
           """
        .update
        .apply()
    }

  }


  def updatePushedToQueueForDeletion(emailScheduledId: Long, teamId: TeamId): Try[Int] = Try {
    DB autoCommit { implicit session =>
      sql"""
           UPDATE emails_scheduled
           SET
            pushed_to_queue_for_deletion = true,
            pushed_to_queue_for_deletion_at = now()
           WHERE
            id = ${emailScheduledId}
            AND team_id = ${teamId.id}
           ;
         """
        .update
        .apply()

    }
  }

    /*
    11 July 2024 :
    Not added the blacklist check in any of the function in the EmailScheduledDAO as the check is done in preScheduling flow
    and this DAO contains calls with post scheduling flow
     */
  def getScheduleDetailsForSendingNew(
                                       emailScheduledId: Long,
                                       teamId: TeamId
                                     )(using logger: SRLogger): Try[Option[EmailSendDetail]] = Try  {
    val emStep1 = DB readOnly { implicit session =>


      // This is used to get info just before email is sent. Do not return anything if given email is already sent.


      sql"""

    SELECT
    s.id,
    s.prospect_id,
    s.step_id,
    s.campaign_id,
    ems.subject,
    ems.body,
    ems.text_body,
    s.is_manual_task,
    s.scheduled_from_campaign,

    --steps.label AS step_name,
    s.step_name,
    ess.id AS sender_email_settings_id,
    ess.service_provider,
    ess.smtp_username,
    ess.smtp_host,
    ess.smtp_port,
    ess.smtp_password_enc,
    ess.oauth2_refresh_token_enc,
    ess.oauth2_access_token_enc,
    ess.oauth2_access_token_expires_at,
    ess.email_domain,
    ess.api_key_enc,
    ess.mailgun_region,
    ess.email AS from_email,
    ess.sender_name AS from_name,
    ess.cc_emails,
    ess.bcc_emails,
    ess.message_id_suffix,
    ess.paused_till AS sender_email_setting_paused_till,
    s.via_gmail_smtp,

    --esr.email AS reply_to_email,
    --esr.sender_name AS reply_to_name,
    ems.reply_to_email,
    ems.reply_to_name,

    --p.email AS to_email,
    ems.to_email,

    --(CASE WHEN p.first_name IS NOT NULL AND p.last_name IS NOT NULL AND p.first_name != '' AND p.last_name != '' THEN CONCAT(p.first_name, ' ', p.last_name) ELSE null END) AS to_name,
    ems.to_name,

    --(case when rth.subdomain_based then concat(org.tracking_subdomain_key, '.', rth.host_url) else rth.host_url end) as sr_tracking_host,

    --rth.id AS rep_tracking_host_id,
    s.rep_tracking_host_id,

    --c.team_id,
    --c.account_id,
    s.team_id,
    s.account_id,

    --c.opt_out_msg,
    --c.opt_out_is_text,
    --c.open_tracking_enabled,
    --c.append_followups,
    --c.click_tracking_enabled,

    --c.name AS campaign_name,
    --c.receiver_email_settings_id,
    s.campaign_name,
    s.receiving_email_settings_id,
    s.email_thread_id,

    --ess.signature,

    ess.dns_host,
    --org.bulk_sender,
    s.list_unsubscribe_header,
    s.gmail_fbl,
    accounts.org_id,
    c.send_plain_text_email

    FROM emails_scheduled s
    inner join email_message_data ems on s.id = ems.es_id and s.team_id = ems.team_id
    LEFT JOIN campaigns c ON ( c.id = s.campaign_id  AND c.team_id = s.team_id )
    --INNER JOIN campaign_steps_variants variants ON variants.id = s.variant_id
    --INNER JOIN campaign_steps steps ON steps.id = s.step_id
    --INNER JOIN campaigns c ON c.id = steps.campaign_id
    --INNER JOIN prospects p ON p.id = s.prospect_id
    INNER JOIN email_settings ess ON s.sender_email_settings_id = ess.id
    --INNER JOIN email_settings esr ON s.receiving_email_settings_id = esr.id
    INNER JOIN accounts ON accounts.id = ess.account_id
    INNER JOIN organizations org ON org.id = accounts.org_id
    --INNER JOIN rep_tracking_hosts rth ON rth.id = (CASE WHEN ess.rep_tracking_host_id IS NOT NULL THEN ess.rep_tracking_host_id ELSE org.rep_tracking_host_id END)
    WHERE s.id = $emailScheduledId
    AND s.sent IS FALSE
    AND s.team_id = ${teamId.id}
    AND s.pushed_to_queue_for_deletion is null
    and ess.status = ${EmailSettingStatus.Active.toString}
    AND (s.scheduled_from_campaign OR s.scheduled_manually)
    AND org.plan_type != ${PlanType.INACTIVE.toString}
    LIMIT 1

      """


        .map(rs => {

          val isManual = rs.boolean("is_manual_task")

          val campaignStepType = if (isManual) {
            CampaignStepType.ManualEmailStep
          } else {
            CampaignStepType.AutoEmailStep
          }

          val stepDetails = rs.longOpt("step_id").map(
            stepId => {
              StepDetails(
                step_id = stepId,
                step_name = rs.string("step_name"),
                step_type = campaignStepType
              )
            })

          val sendEmailFromCampaignDetails = rs.longOpt("campaign_id").map(
            campaign_id => {
              SendEmailFromCampaignDetails(
                campaign_id = campaign_id,
                campaign_name = rs.string("campaign_name"),
                stepDetails = stepDetails
              )
            }
          )

          EmailSendDetail(
            id = rs.long("id"),
            org_id = rs.long("org_id"),
            account_id = rs.long("account_id"),
            team_id = rs.long("team_id"),
            subject = rs.string("subject"),
            body = rs.string("body"),
            text_body = rs.string("text_body"),
            sendEmailFromCampaignDetails = sendEmailFromCampaignDetails,
            prospect_id = rs.longOpt("prospect_id"),
            scheduled_from_campaign = rs.boolean("scheduled_from_campaign"),

            sender_email_settings_id = rs.long("sender_email_settings_id"),
            sender_message_id_suffix = rs.string("message_id_suffix"),

            receiving_email_settings_id = rs.long("receiving_email_settings_id"),

            service_provider = EmailServiceProvider.fromKey(rs.string("service_provider")).get,
            via_gmail_smtp = rs.booleanOpt("via_gmail_smtp"),

            smtp_username = rs.stringOpt("smtp_username"),
            smtp_host = rs.stringOpt("smtp_host"),
            smtp_port = rs.intOpt("smtp_port"),
            smtp_password = rs.stringOpt("smtp_password_enc").map(EncryptionHelpers.decryptEmailSettingCredential),
            oauth2_refresh_token = rs.stringOpt("oauth2_refresh_token_enc").map(EncryptionHelpers.decryptEmailSettingCredential),
            oauth2_access_token = rs.stringOpt("oauth2_access_token_enc").map(EncryptionHelpers.decryptEmailSettingCredential),
            oauth2_access_token_expires_at = rs.jodaDateTimeOpt("oauth2_access_token_expires_at"),
            custom_tracking_domain = rs.stringOpt("dns_host"),
            //        sr_tracking_host = rs.string("sr_tracking_host"),
            rep_tracking_host_id = rs.int("rep_tracking_host_id"),
            // bulk_sender = rs.boolean("bulk_sender"),
            list_unsubscribe_header = rs.stringOpt("list_unsubscribe_header"),
            gmail_fbl = rs.stringOpt("gmail_fbl"),

            // for mailgun
            email_domain = rs.stringOpt("email_domain"),
            api_key = rs.stringOpt("api_key_enc").map(EncryptionHelpers.decryptEmailSettingCredential),
            mailgun_region = rs.stringOpt("mailgun_region").map(mr => ESPMailgunRegion.withName(mr)),

            from_email = rs.string("from_email"),
            from_name = rs.string("from_name"),
            reply_to_email = rs.stringOpt("reply_to_email"),
            reply_to_name = rs.stringOpt("reply_to_name"),


            to_emails = IEmailAddress.parse(emailsStr =  rs.string("to_email")).get,

            cc_emails = rs.stringOpt("cc_emails")
              .flatMap(cc =>
                IEmailAddress.parse(emailsStr = cc).toOption
              ).getOrElse(Seq()),

            bcc_emails = rs.stringOpt("bcc_emails")
              .flatMap(bcc =>
                IEmailAddress.parse(emailsStr = bcc).toOption
              ).getOrElse(Seq()),


            sender_email_setting_paused_till = rs.jodaDateTimeOpt("sender_email_setting_paused_till"),

            email_thread_id = rs.longOpt("email_thread_id"),
            send_plain_text_email = rs.booleanOpt("send_plain_text_email"),
            gmail_thread_id = None,


            // these will be assigned in the next step below this query
            in_reply_to_id = None,
            in_reply_to_references_header = None,
            in_reply_to_sent_at = None,
            in_reply_to_subject = None,
            in_reply_to_outlook_msg_id = None


          )
        })
        .single
        .apply()

    }


    // handle parent for emailThreadId case: manually sent emails
    val emStep2 = if (emStep1.isEmpty) None else DB readOnly { implicit session =>

      val threadId = emStep1.get.email_thread_id
      val senderEmailSettingId = emStep1.get.sender_email_settings_id

      val query = if (threadId.isDefined) {
        sql"""
    SELECT

    ems.message_id AS in_reply_to_id,
    ems.references_header AS in_reply_to_references_header,
    es.sent_at AS in_reply_to_sent_at,
    ems.email_thread_id AS in_reply_email_thread_id,
    ems.gmail_thread_id AS in_reply_gmail_thread_id,
    ems.subject AS in_reply_to_subject,
    ems.outlook_msg_id AS in_reply_to_outlook_msg_id

    FROM emails_scheduled es
    inner join email_message_data ems on es.id = ems.es_id and es.team_id = ems.team_id
    WHERE es.email_thread_id = ${threadId.get}
           AND es.sent

           -- if the user changes the from-email midway through the campaign, send in new thread
           AND es.sender_email_settings_id = $senderEmailSettingId

       ORDER BY es.sent_at DESC
       LIMIT 1

      """
      } else {

        // for new emails scheduled from campaign
        // if the user changes the from-email midway through the campaign, send in new thread: (AND s.sender_email_settings_id = es.sender_email_settings_id) join clause checks this

        sql"""
    SELECT

    ems.message_id AS in_reply_to_id,
    ems.references_header AS in_reply_to_references_header,
    es.sent_at AS in_reply_to_sent_at,
    ems.email_thread_id AS in_reply_email_thread_id,
    ems.gmail_thread_id AS in_reply_gmail_thread_id,
    ems.subject AS in_reply_to_subject,
    ems.outlook_msg_id AS in_reply_to_outlook_msg_id

    FROM emails_scheduled es
    inner join email_message_data ems on es.id = ems.es_id and es.team_id = ems.team_id
    INNER JOIN emails_scheduled s ON (s.campaign_id = es.campaign_id AND s.prospect_id = es.prospect_id AND s.sender_email_settings_id = es.sender_email_settings_id)
    INNER JOIN campaign_steps_relationships csr ON (csr.parent_id = es.step_id AND csr.child_id = s.step_id)
    WHERE s.id = $emailScheduledId


       AND es.scheduled_from_campaign
       AND s.scheduled_from_campaign

       LIMIT 1

      """
      }


      Helpers.logTimeTaken(
        Logger = new SRLogger("getScheduleDetailsForSendingSQL"),
        thresholdInMillis = 500
      ) {
        query
          .map(rs => emStep1.get.copy(

            in_reply_to_id = rs.stringOpt("in_reply_to_id"),
            in_reply_to_references_header = rs.stringOpt("in_reply_to_references_header"),
            in_reply_to_sent_at = rs.jodaDateTimeOpt("in_reply_to_sent_at"),
            in_reply_to_subject = rs.stringOpt("in_reply_to_subject"),
            in_reply_to_outlook_msg_id = rs.stringOpt("in_reply_to_outlook_msg_id"),

            email_thread_id = rs.longOpt("in_reply_email_thread_id"),
            gmail_thread_id = rs.stringOpt("in_reply_gmail_thread_id")
          ))
          .single
          .apply()
      }
    }
    if (emStep2.isDefined) emStep2 else emStep1
  }
//  def getScheduleDetailsForSendingOld(
//                                       emailScheduledId: Long,
//                                       teamId: TeamId
//                                     )(using logger: SRLogger): Try[Option[EmailSendDetail]] = Try {
//    val emStep1 = DB readOnly { implicit session =>
//
//
//      // This is used to get info just before email is sent. Do not return anything if given email is already sent.
//
//
//      sql"""
//
//    SELECT
//    s.id,
//    s.prospect_id,
//    s.step_id,
//    s.campaign_id,
//    s.subject,
//    s.body,
//    s.text_body,
//    s.is_manual_task,
//    s.scheduled_from_campaign,
//
//    --steps.label AS step_name,
//    s.step_name,
//    ess.id AS sender_email_settings_id,
//    ess.service_provider,
//    ess.smtp_username,
//    ess.smtp_host,
//    ess.smtp_port,
//    ess.smtp_password_enc,
//    ess.oauth2_refresh_token_enc,
//    ess.oauth2_access_token_enc,
//    ess.oauth2_access_token_expires_at,
//    ess.email_domain,
//    ess.api_key_enc,
//    ess.mailgun_region,
//    ess.email AS from_email,
//    ess.sender_name AS from_name,
//    ess.cc_emails,
//    ess.bcc_emails,
//    ess.message_id_suffix,
//    ess.paused_till AS sender_email_setting_paused_till,
//    s.via_gmail_smtp,
//
//    --esr.email AS reply_to_email,
//    --esr.sender_name AS reply_to_name,
//    s.reply_to_email,
//    s.reply_to_name,
//
//    --p.email AS to_email,
//    s.to_email,
//
//    --(CASE WHEN p.first_name IS NOT NULL AND p.last_name IS NOT NULL AND p.first_name != '' AND p.last_name != '' THEN CONCAT(p.first_name, ' ', p.last_name) ELSE null END) AS to_name,
//    s.to_name,
//
//    --(case when rth.subdomain_based then concat(org.tracking_subdomain_key, '.', rth.host_url) else rth.host_url end) as sr_tracking_host,
//
//    --rth.id AS rep_tracking_host_id,
//    s.rep_tracking_host_id,
//
//    --c.team_id,
//    --c.account_id,
//    s.team_id,
//    s.account_id,
//
//    --c.opt_out_msg,
//    --c.opt_out_is_text,
//    --c.open_tracking_enabled,
//    --c.append_followups,
//    --c.click_tracking_enabled,
//
//    --c.name AS campaign_name,
//    --c.receiver_email_settings_id,
//    s.campaign_name,
//    s.receiving_email_settings_id,
//    s.email_thread_id,
//
//    --ess.signature,
//
//    ess.dns_host,
//    --org.bulk_sender,
//    s.list_unsubscribe_header,
//    s.gmail_fbl,
//    accounts.org_id,
//    c.send_plain_text_email
//
//    FROM emails_scheduled s
//    --INNER JOIN campaign_steps_variants variants ON variants.id = s.variant_id
//    --INNER JOIN campaign_steps steps ON steps.id = s.step_id
//    LEFT JOIN campaigns c ON ( c.id = s.campaign_id  AND c.team_id = s.team_id )
//    --INNER JOIN prospects p ON p.id = s.prospect_id
//    INNER JOIN email_settings ess ON (s.sender_email_settings_id = ess.id AND s.team_id = ess.team_id )
//    --INNER JOIN email_settings esr ON s.receiving_email_settings_id = esr.id
//    INNER JOIN accounts ON accounts.id = ess.account_id
//    INNER JOIN organizations org ON org.id = accounts.org_id
//    --INNER JOIN rep_tracking_hosts rth ON rth.id = (CASE WHEN ess.rep_tracking_host_id IS NOT NULL THEN ess.rep_tracking_host_id ELSE org.rep_tracking_host_id END)
//    WHERE s.id = $emailScheduledId
//    AND s.sent IS FALSE
//    and s.team_id = ${teamId.id}
//    AND s.pushed_to_queue_for_deletion is null
//    and ess.status = ${EmailSettingStatus.Active.toString}
//    AND (s.scheduled_from_campaign OR s.scheduled_manually)
//    AND org.plan_type != ${PlanType.INACTIVE.toString}
//    LIMIT 1
//
//      """
//
//
//        .map(rs => {
//
//          val isManual = rs.boolean("is_manual_task")
//
//          val campaignStepType = if (isManual) {
//            CampaignStepType.ManualEmailStep
//          } else {
//            CampaignStepType.AutoEmailStep
//          }
//
//          val stepDetails = rs.longOpt("step_id").map(
//            stepId => {
//              StepDetails(
//                step_id = stepId,
//                step_name = rs.string("step_name"),
//                step_type = campaignStepType
//              )
//            })
//
//          val sendEmailFromCampaignDetails = rs.longOpt("campaign_id").map(
//            campaign_id => {
//              SendEmailFromCampaignDetails(
//                campaign_id = campaign_id,
//                campaign_name = rs.string("campaign_name"),
//                stepDetails = stepDetails
//              )
//            }
//          )
//
//          EmailSendDetail(
//            id = rs.long("id"),
//            org_id = rs.long("org_id"),
//            account_id = rs.long("account_id"),
//            team_id = rs.long("team_id"),
//            subject = rs.string("subject"),
//            body = rs.string("body"),
//            text_body = rs.string("text_body"),
//            sendEmailFromCampaignDetails = sendEmailFromCampaignDetails,
//            prospect_id = rs.longOpt("prospect_id"),
//            scheduled_from_campaign = rs.boolean("scheduled_from_campaign"),
//
//            sender_email_settings_id = rs.long("sender_email_settings_id"),
//            sender_message_id_suffix = rs.string("message_id_suffix"),
//
//            receiving_email_settings_id = rs.long("receiving_email_settings_id"),
//
//            service_provider = EmailServiceProvider.fromKey(rs.string("service_provider")).get,
//            via_gmail_smtp = rs.booleanOpt("via_gmail_smtp"),
//
//            smtp_username = rs.stringOpt("smtp_username"),
//            smtp_host = rs.stringOpt("smtp_host"),
//            smtp_port = rs.intOpt("smtp_port"),
//            smtp_password = rs.stringOpt("smtp_password_enc").map(EncryptionHelpers.decryptEmailSettingCredential),
//            oauth2_refresh_token = rs.stringOpt("oauth2_refresh_token_enc").map(EncryptionHelpers.decryptEmailSettingCredential),
//            oauth2_access_token = rs.stringOpt("oauth2_access_token_enc").map(EncryptionHelpers.decryptEmailSettingCredential),
//            oauth2_access_token_expires_at = rs.jodaDateTimeOpt("oauth2_access_token_expires_at"),
//            custom_tracking_domain = rs.stringOpt("dns_host"),
//            //        sr_tracking_host = rs.string("sr_tracking_host"),
//            rep_tracking_host_id = rs.int("rep_tracking_host_id"),
//            // bulk_sender = rs.boolean("bulk_sender"),
//            list_unsubscribe_header = rs.stringOpt("list_unsubscribe_header"),
//            gmail_fbl = rs.stringOpt("gmail_fbl"),
//
//            // for mailgun
//            email_domain = rs.stringOpt("email_domain"),
//            api_key = rs.stringOpt("api_key_enc").map(EncryptionHelpers.decryptEmailSettingCredential),
//            mailgun_region = rs.stringOpt("mailgun_region").map(mr => ESPMailgunRegion.withName(mr)),
//
//            from_email = rs.string("from_email"),
//            from_name = rs.string("from_name"),
//            reply_to_email = rs.stringOpt("reply_to_email"),
//            reply_to_name = rs.stringOpt("reply_to_name"),
//
//
//            to_emails = IEmailAddress.parse(emailsStr =  rs.string("to_email")).get,
//
//            cc_emails = rs.stringOpt("cc_emails")
//              .flatMap(cc =>
//                IEmailAddress.parse(emailsStr = cc).toOption
//              ).getOrElse(Seq()),
//
//            bcc_emails = rs.stringOpt("bcc_emails")
//              .flatMap(bcc =>
//                IEmailAddress.parse(emailsStr = bcc).toOption
//              ).getOrElse(Seq()),
//
//
//            sender_email_setting_paused_till = rs.jodaDateTimeOpt("sender_email_setting_paused_till"),
//
//            email_thread_id = rs.longOpt("email_thread_id"),
//            send_plain_text_email = rs.booleanOpt("send_plain_text_email"),
//            gmail_thread_id = None,
//
//
//            // these will be assigned in the next step below this query
//            in_reply_to_id = None,
//            in_reply_to_references_header = None,
//            in_reply_to_sent_at = None,
//            in_reply_to_subject = None,
//            in_reply_to_outlook_msg_id = None
//
//
//          )
//        })
//        .single
//        .apply()
//
//    }
//
//
//    // handle parent for emailThreadId case: manually sent emails
//    val emStep2 = if (emStep1.isEmpty) None else DB readOnly { implicit session =>
//
//      val threadId = emStep1.get.email_thread_id
//      val senderEmailSettingId = emStep1.get.sender_email_settings_id
//
//      val query = if (threadId.isDefined) {
//        sql"""
//    SELECT
//
//    es.message_id AS in_reply_to_id,
//    es.references_header AS in_reply_to_references_header,
//    es.sent_at AS in_reply_to_sent_at,
//    es.email_thread_id AS in_reply_email_thread_id,
//    es.gmail_thread_id AS in_reply_gmail_thread_id,
//    es.subject AS in_reply_to_subject,
//    es.outlook_msg_id AS in_reply_to_outlook_msg_id
//
//    FROM emails_scheduled es
//    WHERE es.email_thread_id = ${threadId.get}
//           AND es.sent
//
//           -- if the user changes the from-email midway through the campaign, send in new thread
//           AND es.sender_email_settings_id = $senderEmailSettingId
//
//       ORDER BY es.sent_at DESC
//       LIMIT 1
//
//      """
//      } else {
//
//        // for new emails scheduled from campaign
//        // if the user changes the from-email midway through the campaign, send in new thread: (AND s.sender_email_settings_id = es.sender_email_settings_id) join clause checks this
//
//        sql"""
//    SELECT
//
//    es.message_id AS in_reply_to_id,
//    es.references_header AS in_reply_to_references_header,
//    es.sent_at AS in_reply_to_sent_at,
//    es.email_thread_id AS in_reply_email_thread_id,
//    es.gmail_thread_id AS in_reply_gmail_thread_id,
//    es.subject AS in_reply_to_subject,
//    es.outlook_msg_id AS in_reply_to_outlook_msg_id
//
//    FROM emails_scheduled es
//    INNER JOIN emails_scheduled s ON (s.campaign_id = es.campaign_id AND s.prospect_id = es.prospect_id AND s.sender_email_settings_id = es.sender_email_settings_id)
//    INNER JOIN campaign_steps_relationships csr ON (csr.parent_id = es.step_id AND csr.child_id = s.step_id)
//    WHERE s.id = $emailScheduledId
//
//
//       AND es.scheduled_from_campaign
//       AND s.scheduled_from_campaign
//
//       LIMIT 1
//
//      """
//      }
//
//
//      Helpers.logTimeTaken(
//        Logger = new SRLogger("getScheduleDetailsForSendingSQL"),
//        thresholdInMillis = 500
//      ) {
//        query
//          .map(rs => emStep1.get.copy(
//
//            in_reply_to_id = rs.stringOpt("in_reply_to_id"),
//            in_reply_to_references_header = rs.stringOpt("in_reply_to_references_header"),
//            in_reply_to_sent_at = rs.jodaDateTimeOpt("in_reply_to_sent_at"),
//            in_reply_to_subject = rs.stringOpt("in_reply_to_subject"),
//            in_reply_to_outlook_msg_id = rs.stringOpt("in_reply_to_outlook_msg_id"),
//
//            email_thread_id = rs.longOpt("in_reply_email_thread_id"),
//            gmail_thread_id = rs.stringOpt("in_reply_gmail_thread_id")
//          ))
//          .single
//          .apply()
//      }
//    }
//    if (emStep2.isDefined) emStep2 else emStep1
//  }

  // mark the specific thread as read
  //todo add team_id
  def markAsReadInSRInboxV2(
                             emailThreadId: Long,
                             teamId: TeamId
                           ): Try[Int] = Try {

    DB.localTx { implicit session =>

      sql"""
          UPDATE emails_scheduled
          SET
            sr_inbox_read = true
          WHERE email_thread_id = $emailThreadId
          AND team_id = ${teamId.id}
          ;
      """
        .update
        .apply()

      // also mark it as read on the email_threads table
      emailThreadDAO._markAsReadInSRInboxV2(emailThreadId = emailThreadId)

    }
  }


  private def _insertMultipleForCampaign(
                                          emailsToBeScheduled: Vector[EmailScheduledNew],
                                          campaign_email_setting_id: CampaignEmailSettingsId,
                                          emailSendingFlow: Option[EmailSendingFlow],
                                          Logger: SRLogger
                                        )(implicit session: DBSession): Seq[EmailScheduledNewAfterSaving] = {

    Helpers.logTimeTaken(
      Logger = Logger.appendLogRequestId(s"_insertMultipleForCampaign: totalToBeInserted: ${emailsToBeScheduled.length}"),
      thresholdInMillis = 50
    ) {

      //    DB localTx { implicit session =>

      //      Logger.info(s"EmailScheduled._insertMultiple: ${emailsToBeScheduled.length}")

      if (emailsToBeScheduled.isEmpty) Seq()
      else {

        var parameters = List[Any]()

        val valuePlaceholder = emailsToBeScheduled.map(email => {

          parameters = parameters ::: List(

            // inbox_email_setting_id : email inbox in which this email is present, added in Jan 2021
            // we maintain a unique index for emails on (message_id, inbox_email_setting_id)
            // will be same as sender id because this is the inbox the email will be found in
            email.sender_email_settings_id,
            email.uuid,

            email.campaign_id,
            email.step_id,
            email.is_opening_step,
            email.prospect_id,
            email.prospect_account_id,
            email.scheduled_at,
            email.added_at,
            email.template_id,
            email.variant_id,

            email.sender_email_settings_id,
            email.team_id,
            email.account_id,
            email.rep_mail_server_id,
            email.via_gmail_smtp,
            (email.step_type == CampaignStepType.ManualEmailStep),

            email.scheduled_from_campaign,
            email.scheduled_manually,

            email.rep_tracking_host_id,
            email.campaign_name,
            email.step_name,
            email.receiver_email_settings_id,
            email.email_thread_id,
            email.pushed_to_rabbitmq,
            if (email.pushed_to_rabbitmq) DateTime.now() else null,


            email.list_unsubscribe_header.isDefined,
            email.gmail_fbl.isDefined,
            email.has_open_tracking,
            email.has_click_tracking,
            email.has_unsubscribe_link,

            emailSendingFlow.map(_.toString),

            email.list_unsubscribe_header,
            email.gmail_fbl

          )

          s"""
            (
              ?,
              ?::uuid,

              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,

              ?,
              ?,
              ?,
              ?,
              ?,
              ?,

              ?,
              ?,

              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,

              ?,
              ?,
              ?,
              ?,
              ?,

              ?,

              ?,
              ?

            )

          """
        }).mkString(", ")

        SQL(
          s"""
          INSERT INTO emails_scheduled
          (

             inbox_email_setting_id,
             uuid,

             campaign_id,
             step_id,
             is_opening_step,
             prospect_id,
             prospect_account_id,
             scheduled_at,
             added_at,
             template_id,
             variant_id,

             sender_email_settings_id,
             team_id,
             account_id,
             rep_mail_server_id,
             via_gmail_smtp,
             is_manual_task,

             scheduled_from_campaign,
             scheduled_manually,

             rep_tracking_host_id,
             campaign_name,
             step_name,
             receiving_email_settings_id,
             email_thread_id,
             pushed_to_rabbitmq,
             pushed_to_rabbitmq_at,


              has_list_unsubscribe_header,
              has_gmail_fbl_header,
              has_open_tracking,
              has_click_tracking,
              has_unsubscribe_link,

              sending_flow,

              list_unsubscribe_header,
              gmail_fbl


          )
          VALUES $valuePlaceholder
          ON CONFLICT DO NOTHING
          RETURNING *;
        """)
          .bind(parameters*)
          .map(rs => {
            val isManual = rs.boolean("is_manual_task")
            val uuid = rs.string("uuid")
            val selectedEmail: EmailScheduledNew = emailsToBeScheduled.find(a => a.uuid == uuid).get

            val campaignStepType = if (isManual) {
              CampaignStepType.ManualEmailStep
            } else {
              CampaignStepType.AutoEmailStep
            }
            EmailScheduledNewAfterSaving(
              email_scheduled_id = rs.long("id"),
              campaign_id = rs.longOpt("campaign_id"),
              step_id = rs.longOpt("step_id"),
              step_type = campaignStepType,
              prospect_id = rs.longOpt("prospect_id"),
              added_at = rs.jodaDateTime("added_at"),
              to_email = selectedEmail.to_email,
              reply_to_email = selectedEmail.reply_to_email,
              from_email = selectedEmail.from_email,
              scheduled_at = rs.jodaDateTime("scheduled_at"),
              sender_email_settings_id = rs.long("sender_email_settings_id"),
              template_id = rs.longOpt("template_id"),
              variant_id = rs.longOpt("variant_id"),
              rep_mail_server_id = rs.int("rep_mail_server_id"),
              campaign_email_setting_id = campaign_email_setting_id,
              team_id = TeamId(rs.long("team_id")),
              to_name =  selectedEmail.to_name,
              from_name = selectedEmail.from_name,
              reply_to_name = selectedEmail.reply_to_name,
              body =  selectedEmail.body,
              base_body =  selectedEmail.base_body,
              text_body =  selectedEmail.text_body,
              subject = selectedEmail.subject
            )
          })
          .list
          .apply()

      }
      //    }
    }

  }

  def _insertTrackedRepliesV3(
                               newMsgsWithEmailThreadId: Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])],
                               sentManuallyFromSRInbox: Boolean
                             )(using Logger: SRLogger, session: DBSession): Try[Seq[EmailReplySavedV3]] = Try {

    if (newMsgsWithEmailThreadId.isEmpty) Seq()

    else {

      val SCHEDULED_FROM_CAMPAIGN = false

      // mark all replies as sent = true
      // this will ensure these emails wont get picked up by the scheduler for sending
      // mark "sent" as true for everything via reply tracking
      val IS_SENT = true

      val replies: Seq[(EmailMessageTracked, String)] = newMsgsWithEmailThreadId.map(a => (a._1, srUuidUtils.generateEmailsScheduledUuid()))


      var parameters = List[Any]()

      val valuePlaceholder = replies.map(data => {
        val (email, uuid) = data

        val sent_at = email.received_at
        val added_at = email.recorded_at

        if (email.email_thread_id.isEmpty) {
          Logger.fatal(s"_insertTrackedReplies email.email_thread_id.isEmpty (ignoring): email: $email")
        }

        val toEmails: String = IEmailAddress.stringify(emails = email.to_emails).getOrElse("") //deliberately sending "" if none for reply tracking

        // todo newinbox: remove this to_name column after full migration
        val toName = if (email.to_emails.length == 1) email.to_emails.head.name else None

        val isSoftBounce: Boolean = email.email_status.bouncedData.isDefined && email.email_status.bouncedData.get.is_soft_bounced
        val softBounceAt: Option[DateTime] = if (isSoftBounce) email.email_status.bouncedData.map(_.bounced_at) else None
        val ccEmails: Option[String] = IEmailAddress.stringify(emails = email.cc_emails)

        val sending_flow: Option[String] = if(email.internal_tracking_note == InternalTrackingNote.INBOX_PLACEMENT_CHECK_TEST_EMAIL) {
          Some(EmailSendingFlow.InboxPlacementIn.toString)
        } else None

        parameters = parameters ::: List(
          Logger.logTraceId,
          email.inbox_email_setting_id,
          uuid,

          email.inbox_email_setting_id,
          email.inbox_email_setting_id,

          email.campaign_id,
          email.step_id,
          email.prospect_id_in_campaign,
          email.campaign_name,
          email.step_name,

          IS_SENT,
          sent_at, // scheduled_at is NOT NULL column
          sent_at,
          added_at,
          email.sr_inbox_read,
          email.email_status.replyType.toString,

          email.scheduled_manually,
          SCHEDULED_FROM_CAMPAIGN,

          email.email_thread_id,
          email.team_id,
          email.account_id,
          email.prospect_account_id_in_campaign,

          sentManuallyFromSRInbox,
          email.internal_tracking_note.toString,


          /* 4th October 2022:

          we are storing the bounced / bounced_at data here itself, because `_updateEmailStatusOnReplyV3` fn
          which actually updates the status - ignores non-campaign emails

          As a result, we were ending up with emails that were marked as
          reply_type=delivery_failed
          but bounced=false

          so, we are just updating the bounced status here as well
          */
          email.email_status.bouncedData.isDefined,
          email.email_status.bouncedData.map(_.bounced_at).orNull,

          isSoftBounce,
          softBounceAt.orNull,


          email.email_status.bouncedData.map(_.bounce_type.toString).orNull,
          email.email_status.bouncedData.map(_.bounce_reason).orNull,
          sending_flow,

          email.tempThreadId.getOrElse(null)
        )

        s"""
          (
            ?,
            ?,
            ?::uuid,

            ?,
            ?,

            ?,
            ?,
            ?,
            ?,
            ?,


            ?,
            ?,
            ?,
            ?,
            ?,
            ?,

            ?,
            ?,

            ?,
            ?,
            ?,
            ?,

            ?,
            ?,

            ?,
            ?,

            ?,
            ?,

            ?,
            ?,
            ?,

            ?

          )

        """
      }).mkString(", ")

      val toUpdateEmailThreadsInternal: Seq[EmailReplySavedV3] = SQL(
        s"""
          INSERT INTO emails_scheduled
          (
            req_log_trace_id,
            inbox_email_setting_id,
            uuid,

            receiving_email_settings_id,
            sender_email_settings_id,

            campaign_id,
            step_id,
            prospect_id,
            campaign_name,
            step_name,

            sent,
            scheduled_at,
            sent_at,
            added_at,
            sr_inbox_read,
            reply_type,

            scheduled_manually,
            scheduled_from_campaign,

            email_thread_id,
            team_id,
            account_id,
            prospect_account_id,

            sent_manually_from_sr_inbox,
            internal_tracking_note,

            bounced,
            bounced_at,

            email_soft_bounced,
            email_soft_bounced_at,

            email_bounce_type,
            email_bounce_reason,
            sending_flow,

            temp_thread_id

          )
          VALUES $valuePlaceholder
          ON CONFLICT DO NOTHING
          RETURNING
            scheduled_manually,
            scheduled_from_campaign,
            id,
            uuid,
            sent_at,
            email_thread_id,
            campaign_id,
            step_id,
            prospect_id,
            prospect_account_id,
            reply_type,
            inbox_email_setting_id,
            team_id
          ;
        """)
        .bind(parameters*)
        .map(rs => {

          val uuid = rs.string("uuid")
          val selectedEmail: EmailMessageTracked = replies.find(a => a._2 == uuid).get._1

          val message_id = selectedEmail.message_id
          val emailBody = selectedEmail.body


          val toEmails = selectedEmail.to_emails

          val prospectIdInCampaign = rs.longOpt("prospect_id")
          val emailScheduledId = rs.long("id")

          val msgOpt = newMsgsWithEmailThreadId.find(em => {
            em._1.message_id == message_id
          })

          if (msgOpt.isEmpty) {
            Logger.fatal(s"msgOpt.isEmpty: message_id: $message_id : fromEmailAddress: ${selectedEmail.from}")
          }


          val msg = msgOpt.get

          val prospectsInvolved = msg._2
          val emailStatus = msg._1.email_status

          // fixme inboxv3: why is mapping fromEmail to prospect needed to be done here?
          //  we can do it in the top-level itself in ERIntermediateValidProspect
          val fromProspect = prospectsInvolved.find(pr => {
            pr.email.trim.toLowerCase == selectedEmail.from.email.trim.toLowerCase
          })

          val campaignAssociatedProspect = if (prospectIdInCampaign.isEmpty) None else {
            prospectsInvolved.find(pr => {
              pr.prospect_id == prospectIdInCampaign.get
            })
          }

          /**
           * newinbox check - check that primary_prospect_id is always there in all_prospects_involved & campaign_associated_prospect
           *
           * while marking prospect as completed and updating email status below: we are assuming this is the case
           * */
          if (prospectIdInCampaign.nonEmpty
            && campaignAssociatedProspect.isEmpty
          ) {
            Logger.fatal(s"prospectIdInCampaign.nonEmpty && campaignAssociatedProspect.isEmpty case should not be possible: prospectIdInCampaign $prospectIdInCampaign :: mid: $message_id")
          }

          val toEmailAddresses = toEmails.map(_.email.trim.toLowerCase)
          val toProspects = prospectsInvolved.filter(pr => {
            toEmailAddresses.contains(pr.email.trim.toLowerCase)
          })


          if (prospectIdInCampaign.nonEmpty
            && fromProspect.isEmpty
            && toProspects.isEmpty

            /**
             * for DELIVERY_FAILED replies, we do not auto-create prospects
             */
            && msg._1.email_status.replyType != EmailReplyType.DELIVERY_FAILED
          ) {
            Logger.fatal(s"prospectIdInCampaign.nonEmpty && fromProspect.isEmpty && toProspects.isEmpty && !bounced case should not be possible: prospectIdInCampaign $prospectIdInCampaign :: mid: $message_id")
          }

          val ccEmails: Seq[IEmailAddress] = selectedEmail.cc_emails


          val replyTo = selectedEmail.reply_to

          EmailReplySavedV3(
            message_id = message_id,
            email_scheduled_id = emailScheduledId,
            email_thread_id = rs.long("email_thread_id"),
            sent_at = rs.jodaDateTime("sent_at"),

            by_account = rs.boolean("scheduled_manually") || rs.boolean("scheduled_from_campaign"),

            campaign_id = rs.longOpt("campaign_id"),
            step_id = rs.longOpt("step_id"),
            prospect_id_in_campaign = prospectIdInCampaign,
            prospect_account_id_in_campaign = rs.longOpt("prospect_account_id"),
            reply_type = EmailReplyType.withName(rs.string("reply_type")),

            from_email = selectedEmail.from,

            to_emails = toEmails,

            email_body = emailBody,

            cc_emails = ccEmails,

            reply_to = replyTo,

            inbox_email_setting_id = rs.long("inbox_email_setting_id"),

            all_prospects_involved = prospectsInvolved,
            from_prospect = fromProspect,
            to_prospects = toProspects,
            campaign_associated_prospect = campaignAssociatedProspect,

            email_status = emailStatus,
            subject = selectedEmail.subject,
            base_body = selectedEmail.base_body,
            text_body = selectedEmail.text_body,
            full_headers = selectedEmail.full_headers,

            gmail_msg_id = selectedEmail.gmail_msg_id ,

            gmail_thread_id = selectedEmail.gmail_thread_id,
            outlook_msg_id = selectedEmail.outlook_msg_id,
            outlook_conversation_id = selectedEmail.outlook_conversation_id,

            outlook_response_json = selectedEmail.outlook_response_json,
            team_id = TeamId(rs.long("team_id")),
            references_header = selectedEmail.references_header,
            in_reply_to_header = selectedEmail.in_reply_to_header

          )
        })
        .list.apply()

      toUpdateEmailThreadsInternal


    }

  }

  def saveEmailsToBeScheduledAndUpdateCampaignDataV2(
                                                      emailsToBeScheduled: Vector[EmailScheduledNew],
                                                      campaign_email_setting_id: CampaignEmailSettingsId,
                                                      emailSendingFlow: Option[EmailSendingFlow],
                                                      Logger: SRLogger
                                                    )(session: DBSession): Try[Seq[EmailScheduledNewAfterSaving]] = if (emailsToBeScheduled.isEmpty) {
    Success(Seq())
  } else {

    // Logger.info(s"saveEmailsToBeScheduledAndUpdateCampaignDataV2 got emails: ${emailsToBeScheduled.map(e => s"cid_${e.campaign_id} pid_${e.prospect_id} tid_${e.team_id} eset_${e.sender_email_settings_id} cstep_${e.step_id}")}")
    Logger.info(s"saveEmailsToBeScheduledAndUpdateCampaignDataV2 got emails len: ${emailsToBeScheduled.size}")

    val saved =
      blocking {
        Try {
          // DB localTx { implicit session =>

          //    Logger.info(s"EmailScheduled.saveEmailsToBeScheduledAndUpdateCampaignData: $emailsToBeScheduled")

          val insertedEmailScheduledNewListAfterSave = _insertMultipleForCampaign(
            emailsToBeScheduled = emailsToBeScheduled,
            campaign_email_setting_id = campaign_email_setting_id,
            emailSendingFlow = emailSendingFlow,
            Logger = Logger
          )(session)

          insertedEmailScheduledNewListAfterSave

          // }
        }
      }

    saved match {
      case Failure(exception) =>
        Logger.fatal(s"saveEmailsToBeScheduledAndUpdateCampaignDataV2 failed to save emails:", exception)

      case Success(saved_emails) =>

        Logger.info(s"saveEmailsToBeScheduledAndUpdateCampaignDataV2 saved emails: ${saved_emails.map(_.prospect_id)} :: emailScheduledId count: ${saved_emails.size}")

    }

    saved
  }

  def addBodyToEmailsToBeScheduled(
                                    emails: Seq[EmailScheduledNewStep2],
                                    teamId: TeamId
                                  )(implicit session: DBSession): Try[Seq[Long]] = Try {

    var emailParameters = List[Any]()

    val emailPlaceholder: String = emails
      .map(e => {

        emailParameters = emailParameters ::: List(

          e.email_scheduled_id,

          e.list_unsubscribe_header.isDefined,
          e.gmail_fbl.isDefined,
          e.has_open_tracking,
          e.has_click_tracking,
          e.has_unsubscribe_link,
          e.is_edited_preview_email,

          e.list_unsubscribe_header,
          e.gmail_fbl

        )

        s"""
          (
            ?,

            ?,
            ?,
            ?,
            ?,
            ?,
            ?,

            ?,
            ?
          )
        """
      })
      .mkString(", ")

    emailParameters = emailParameters ::: List(teamId.id)

    SQL(
      s"""
        UPDATE emails_scheduled es
        SET

          has_list_unsubscribe_header = temp.has_list_unsubscribe_header,
          has_gmail_fbl_header = temp.has_gmail_fbl_header,
          has_open_tracking = temp.has_open_tracking,
          has_click_tracking = temp.has_click_tracking,
          has_unsubscribe_link = temp.has_unsubscribe_link,
          is_edited_preview_email = temp.is_edited_preview_email,

          list_unsubscribe_header = temp.list_unsubscribe_header,
          gmail_fbl = temp.gmail_fbl

        FROM (
          VALUES $emailPlaceholder
        )
        AS temp(
          email_scheduled_id,

          has_list_unsubscribe_header,
          has_gmail_fbl_header,
          has_open_tracking,
          has_click_tracking,
          has_unsubscribe_link,
          is_edited_preview_email,

          list_unsubscribe_header,
          gmail_fbl

        )
        WHERE es.id = temp.email_scheduled_id
          AND es.team_id = ?
          AND es.scheduled_from_campaign
          AND NOT es.sent
        RETURNING es.id;
      """)
      .bind(emailParameters*)
      .map(rs => rs.long("id"))
      .list
      .apply()

  }


  // delete from emails_scheduled and revert current_step_id in campaigns_prospects
  // CampaignStepVariantDAO.deleteV2
  // CampaignStepDAO.delete
  // CampaignService.onStopOrArchiveCampaign
  //7 march 2024 : Commenting it out because we are first finding the select email scheduled id to delete depending on the below where clause
  // and then deleting by email scheduled id.

  //  def deleteUnsentByCampaignId(
  //                                campaignId: Long,
  //                                teamId: TeamId,
  //                                senderEmailSettingIds: Seq[EmailSettingId]
  //                              )(using Logger: SRLogger,session:DBSession): Try[List[Long]] = Try {
  //    Logger.debug(s"deleteUnsentByCampaignId senderEmailSettingId - $senderEmailSettingIds campaignId - $campaignId")
  //    val deletedProspectIds = {
  //
  //        sql"""
  //        DELETE FROM emails_scheduled
  //        WHERE campaign_id = $campaignId
  //          AND team_id = ${teamId.id}
  //          AND sent = false
  //          AND scheduled_from_campaign
  //        RETURNING prospect_id;
  //      """
  //          .map(rs => rs.longOpt("prospect_id"))
  //          .list
  //          .apply()
  //          .filter(_.isDefined)
  //          .map(_.get)
  //    }
  //
  //    deletedProspectIds
  //  }

  //7 march 2024 : Commenting it out because we are first finding the select email scheduled id to delete depending on the below where clause
  // and then deleting by email scheduled id.

  //  def deleteUnsentByEmailSettingIds(emailSettingData: Map[TeamId, Seq[EmailSettingId]])(using Logger: SRLogger,session: DBSession): Try[Seq[CampaignIdAndProspectId]] = {
  //    Logger.debug(s"EmailScheduledDAO deleteUnsentByEmailSettingIds emailSettingData: $emailSettingData")
  //    Try {
  //      emailSettingData.flatMap { case(teamId, emailSettingIds) =>
  //          sql"""
  //                    DELETE FROM emails_scheduled
  //                    WHERE inbox_email_setting_id in (${emailSettingIds.map(_.emailSettingId)})
  //                      AND team_id = ${teamId.id}
  //                      AND sent = false
  //                      AND scheduled_from_campaign
  //                    RETURNING campaign_id, prospect_id;
  //                  """
  //            .map(rs => (rs.longOpt("campaign_id"), rs.longOpt("prospect_id")))
  //            .list
  //            .apply()
  //            .filter(d => d._1.isDefined && d._2.isDefined)
  //            .map(res => CampaignIdAndProspectId(
  //              campaignId = CampaignId(res._1.get),
  //              prospectId = ProspectId(res._2.get),
  //              team_id = teamId
  //            )
  //            )
  //      }.toSeq
  //    }
  //  }

  def getTaskIdsAndEmailScheduledIdsForDeletion(
                                                 query: SQL[Nothing, NoExtractor],
                                                 teamId: TeamId,
                                                 task_ids_should_be_selected : Boolean
                                               )(using logger: SRLogger): Try[Seq[DeleteAndRevertDataFromDB]] = Try {


    query
      .map(rs => {

        if (task_ids_should_be_selected) {

          DeleteAndRevertDataFromDB(
            task_data = rs.stringOpt("t_task_id").map(t_id => {
              DeleteAndRevertTaskFromDB(
                task_id = t_id,
                task_step_id = rs.longOpt("t_step_id").map(StepId(_)),
                task_campaign_id = rs.longOpt("t_campaign_id").map(CampaignId(_)),
                task_prospect_id = rs.longOpt("t_prospect_id").map(ProspectId(_))
              )
            }),
            emails_scheduled_data = rs.longOpt("es_scheduled_id").map(es_id => {
              DeleteAndRevertEmailScheduledFromDB(
                es_id = es_id,
                es_step_id = rs.longOpt("es_step_id").map(StepId(_)),
                es_campaign_id = rs.longOpt("es_campaign_id").map(CampaignId(_)),
                es_inbox_email_setting_id = rs.longOpt("es_inbox_setting_id"),
                es_prospect_id = rs.longOpt("es_prospect_id").map(ProspectId(_))
              )
            }),
            teamId = teamId
          )
        } else {

          DeleteAndRevertDataFromDB(
            task_data = None,
            emails_scheduled_data = rs.longOpt("es_scheduled_id").map(es_id => {
              DeleteAndRevertEmailScheduledFromDB(
                es_id = es_id,
                es_step_id = rs.longOpt("es_step_id").map(StepId(_)),
                es_campaign_id = rs.longOpt("es_campaign_id").map(CampaignId(_)),
                es_inbox_email_setting_id = rs.longOpt("es_inbox_setting_id"),
                es_prospect_id = rs.longOpt("es_prospect_id").map(ProspectId(_))
              )
            }),
            teamId = teamId
          )


        }

      })
      .list
      .apply()
  }

  def selectEmailThatCouldHaveBeenDeleted(
                                           emailSettingData: Map[TeamId, Seq[EmailSettingId]]
                                         )(using Logger: SRLogger): Try[Seq[StuckScheduledEmailForDeletion]] = {

    Logger.debug(s"EmailScheduledDAO selectEmailScheduledToDeleteExceptManual emailSettingData: ${emailSettingData}")
    Try {
      emailSettingData.flatMap { case (teamId, emailSettingIds) =>
        if (emailSettingIds.isEmpty) {
          List()
        } else {
          DB readOnly { implicit session =>
            sql"""
                SELECT
                  es.id,
                  es.scheduled_manually,
                  es.inbox_email_setting_id
                FROM emails_scheduled es
                WHERE inbox_email_setting_id in (${emailSettingIds.map(_.emailSettingId)})
                  AND team_id = ${teamId.id}
                  AND sent = false
                  AND is_manual_task = false
                  AND scheduled_from_campaign
             """
              .map(rs => (StuckScheduledEmailForDeletion(
                emailScheduledId = rs.long("id"),
                scheduledManually = rs.boolean("scheduled_manually"),
                senderEmailSettingId = rs.longOpt("inbox_email_setting_id"),
                teamId = teamId
              )))
              .list
              .apply()
          }
        }
      }.toSeq
    }
  }

  def deleteUnsentByEmailScheduledId(
                                      emailScheduledId: Long,
                                      teamId: TeamId,
                                      senderEmailSettingId: Option[Long]
                                    )(using Logger: SRLogger,session:DBSession): Try[OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId] = Try {
    Logger.debug(s"deleteUnsentByEmailScheduledId emailScheduledId - $emailScheduledId senderEmailSettingId - $senderEmailSettingId")
    val campaignProspectIdRemoved: OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId =  {
      sql"""
          DELETE FROM emails_scheduled
          WHERE id = $emailScheduledId
            AND team_id = ${teamId.id}
            AND sent = false
            AND scheduled_from_campaign
          RETURNING campaign_id, prospect_id, team_id, step_id
        """
        .map(rs => OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId(
          campaignId = rs.longOpt("campaign_id").map(v => CampaignId(v)),
          prospectId = rs.longOpt("prospect_id").map(v => ProspectId(v)),
          step_id = rs.longOpt("step_id").map(v => StepId(v)),
          emailSettingId = senderEmailSettingId.map(id => EmailSettingId(emailSettingId = id)),
          team_id = teamId,
          taskId = None
        )
        )
        .single
        .apply()
        .getOrElse(OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId(
          campaignId = None,
          prospectId = None,
          emailSettingId = senderEmailSettingId.map(id => EmailSettingId(emailSettingId = id)),
          team_id = teamId,
          taskId = None,
          step_id = None
        ))
    }

    campaignProspectIdRemoved

  }


  // DONE: MULTICAMPAIGN FIX: deleting all the campaigns' emails for the prospect if reply_handling is pause_specific

  //7 march 2024 : Commenting it out because we are first finding the select email scheduled id to delete depending on the below where clause
  // and then deleting by email scheduled id.
  //  def _deleteUnsentByProspectIds(
  //    campaignId: Long,
  //    prospectIds: Seq[Long],
  //
  //    prospectAccountIds: Option[Seq[Long]],
  //    // if REPLY_HANDLING is pause-specific, this will be false
  //    // if this is called from unsubscribe request, this will be true
  ////    deleteForAllCampaigns: Boolean
  //    replyHandling: ReplyHandling.Value,
  //    teamId: TeamId
  //  )(using Logger: SRLogger,session: DBSession): Try[List[OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId]] = Try{
  //    Logger.debug(s"_deleteUnsentByProspectIds campaignId - $campaignId replyHandling - $replyHandling prospectIds - $prospectIds prospectAccountIds - $prospectAccountIds")
  //
  //      val idsRemoved = {
  //
  //        val whereClause = replyHandling match {
  //
  //          case ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY =>
  //
  //            sqls" AND campaign_id = $campaignId AND prospect_id IN ${SQLUtils.generateSQLValuesClause(prospectIds)} "
  //
  //          case ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY =>
  //            if (
  //              prospectAccountIds.isDefined &&
  //                prospectAccountIds.get.nonEmpty
  //            ) {
  //
  //              sqls" AND (prospect_account_id IN ${SQLUtils.generateSQLValuesClause(prospectAccountIds.get)} OR prospect_id IN ${SQLUtils.generateSQLValuesClause(prospectIds)} )"
  //
  //            } else {
  //
  //              sqls" AND prospect_id IN ${SQLUtils.generateSQLValuesClause(prospectIds)} "
  //
  //            }
  //
  //          case ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY =>
  //            sqls" AND prospect_id IN ${SQLUtils.generateSQLValuesClause(prospectIds)} "
  //
  //        }
  //
  //        sql"""
  //          DELETE FROM emails_scheduled
  //          WHERE sent = false
  //            AND scheduled_from_campaign
  //            AND team_id = ${teamId.id}
  //            $whereClause
  //
  //          RETURNING campaign_id, prospect_id, sender_email_settings_id
  //        """
  //          .map(rs => (
  //            OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId(
  //              campaignId = rs.longOpt("campaign_id").map(id => CampaignId(id = id )),
  //              prospectId = rs.longOpt("prospect_id").map(id => ProspectId(id = id)),
  //              emailSettingId = rs.longOpt("sender_email_settings_id").map(id => EmailSettingId(emailSettingId = id)),
  //              team_id = teamId
  //            ))
  //
  //          )
  //          .list
  //          .apply()
  //      }
  //        idsRemoved
  //  }


  // Purpose of this function is to avoid multiple workers pushing the same emailScheduledId to MQ
  // (Concurrency issue)
  private def updateEmailIdsForQueueingCampaignEmailsToRabbitMQ(
                                                                 emailScheduledToBePushed: Map[TeamId, Seq[Long]],
                                                                 logger: SRLogger
                                                               ): List[Long] = {

    if (emailScheduledToBePushed.isEmpty) {
      List()
    } else {


      Helpers.logTimeTaken(
        Logger = logger.appendLogRequestId("updateEmailIdsForQueueingCampaignEmailsToRabbitMQ")
      ) {

        emailScheduledToBePushed.flatMap {
          case (teamId, emailScheduledIdsToBePushed) =>
            DB.localTx { implicit session =>

              val emailScheduledIdsNotLocked: List[Long] =
                sql"""
                select es.id

                from emails_scheduled es

                where es.id in ${SQLUtils.generateSQLValuesClause(emailScheduledIdsToBePushed)}
                AND es.team_id = ${teamId.id}

                and (es.pushed_to_rabbitmq IS NULL OR es.pushed_to_rabbitmq IS FALSE)

                -- this is important
                FOR UPDATE SKIP LOCKED
                """
                  .map(_.long("id"))
                  .list
                  .apply()

              if (emailScheduledIdsNotLocked.isEmpty) {

                List()

              } else {

                sql"""
                  UPDATE emails_scheduled es

                  SET
                    pushed_to_rabbitmq = TRUE,
                    pushed_to_rabbitmq_at = now()

                  WHERE

                    es.id in ${SQLUtils.generateSQLValuesClause(emailScheduledIdsNotLocked)}
                    AND es.team_id = ${teamId.id}

                  RETURNING es.id
                ;
               """
                  .map(_.long("id"))
                  .list
                  .apply()

              }
            }
        }
      }
    }.toList
  }


  // for org_ids NOT in AppConfig.orgIdsForPushToRabbitMqV2Flow
  private def findEmailIdsForQueueingCampaignEmailsToRabbitMQ(
                                                               toBeSentTill: DateTime,
                                                               logger: SRLogger
                                                             ): Try[List[EmailScheduledIdsFoundForSendingViaMQ]] = {


    DBUtils.readOnly(
      logger = logger.appendLogRequestId("findEmailIdsForQueueingCampaignEmailsToRabbitMQ")
    ){ implicit session =>

      sql"""

    WITH eset as (

      SELECT DISTINCT ON (esettings.id)

        esettings.id,
        esettings.team_id,
        esettings.last_sent_sent_at,
        esettings.last_sent_scheduled_at,
        esettings.service_provider,
        rms.public_ip as sending_server_ip

      from email_settings esettings


      -- inner join (
      --   select distinct sender_email_settings_id from campaigns where sender_email_settings_id is not null and status = 'running'
      -- ) disteset on esettings.id = disteset.sender_email_settings_id

      INNER JOIN campaign_email_settings ces ON esettings.id = ces.sender_email_setting_id and esettings.team_id = ces.team_id
      INNER JOIN campaigns c ON (

        c.id = ces.campaign_id
        AND c.status = ${CampaignStatus.RUNNING.toString}
        and c.team_id = ces.team_id

      )

      -- check there is some email to be scheduled
      INNER JOIN emails_scheduled es ON (

        es.inbox_email_setting_id = esettings.id
        AND es.team_id = esettings.team_id
        AND es.scheduled_from_campaign
        AND NOT es.pushed_to_rabbitmq
        AND NOT es.sent
        AND es.scheduled_at < $toBeSentTill

        -- we are deleting stuck emails after 1 hour, even when in queue (HourlyCronService.deleteStuckEmails)
        -- this condition optimizes this query by reducing search space
        AND es.scheduled_at > now() - interval '6 hours'

      )

    join accounts acc on acc.id = esettings.account_id
    join organizations org on org.id = acc.org_id
    join rep_mail_servers rms ON rms.id = (case when esettings.rep_mail_server_id IS NOT NULL then esettings.rep_mail_server_id else org.rep_mail_server_id end)

    -- ignore email settings that are paused
    WHERE

      org.id NOT IN (${AppConfig.orgIdsForPushToRabbitMqV2Flow})
      AND esettings.status = ${EmailSettingStatus.Active.toString}
      AND

      (
        esettings.paused_till IS NULL
        OR esettings.paused_till < now()
      )

      -- Added on 7th April 2022 for scaling issue
      AND (
        esettings.last_sent_sent_at is null
        OR
        esettings.last_sent_sent_at < now() - interval '60 seconds'
      )

      -- check that there is no other message from email that is being sent now
      -- get the count of message in mq (for sending) for each email setting
      -- do not allow a email setting to have more than 1 message in mq
      GROUP BY esettings.id, rms.id
      HAVING (SELECT NOT EXISTS(

        SELECT esc.id FROM emails_scheduled esc
          WHERE
            esc.inbox_email_setting_id = esettings.id
            AND esc.team_id = esettings.team_id
            AND esc.pushed_to_rabbitmq
            AND not esc.sent
            AND esc.scheduled_from_campaign

          -- we are deleting stuck emails after 1 hour, even when in queue
          -- this condition optimizes this query by reducing search space
          AND esc.scheduled_at > now() - interval '6 hours'

      ))

      -- Added on 7th April 2022 for scaling issue, updated to 500 on 9th Sep 2024
      -- reduced to 150 on 10th Sep 2024 , as IPs went bad the next day
      -- further reduced to 100 on 11th Sep 2024 , as IPs went bad the next day
      limit 100

    )

      select tobepushed.id, eset.sending_server_ip, eset.team_id, eset.service_provider

        from eset

        -- get a max of 1 email to be pushed to queue
        inner join lateral (

          SELECT es.id, es.scheduled_at
            FROM emails_scheduled es
            inner join email_message_data ems on ems.team_id = es.team_id and es.id = ems.es_id
            INNER JOIN campaigns ca ON ca.id = es.campaign_id AND ca.team_id = es.team_id
            INNER JOIN prospects p ON p.id = es.prospect_id AND p.team_id = es.team_id
            INNER JOIN campaigns_prospects cp ON cp.campaign_id = ca.id AND cp.prospect_id = p.id AND cp.team_id = ca.team_id
            WHERE es.inbox_email_setting_id = eset.id
            AND es.team_id = eset.team_id
            AND es.pushed_to_rabbitmq = FALSE
            AND es.sent = FALSE
            AND es.pushed_to_queue_for_deletion is null
            AND es.scheduled_at < $toBeSentTill
            AND NOT es.is_manual_task

            -- this condition optimizes this query by reducing search space
            AND es.scheduled_at > now() - interval '6 hours'

            AND es.scheduled_from_campaign

            -- otherwise email goes before SchedulerEmailCronService Step2 where body and subject are added
            AND (ems.body IS NOT NULL AND ems.subject IS NOT NULL)

            AND ((current_timestamp AT TIME ZONE (CASE WHEN (p.timezone IS NOT NULL AND p.timezone != '') THEN p.timezone ELSE ca.timezone END))::date + ca.daily_from_time * interval '1' second)::timestamptz AT TIME ZONE 'UTC' AT TIME ZONE (CASE WHEN (p.timezone IS NOT NULL AND p.timezone != '') THEN p.timezone ELSE ca.timezone END) <= now()

            AND ((current_timestamp AT TIME ZONE (CASE WHEN (p.timezone IS NOT NULL AND p.timezone != '') THEN p.timezone ELSE ca.timezone END))::date + ca.daily_till_time * interval '1' second)::timestamptz AT TIME ZONE 'UTC' AT TIME ZONE (CASE WHEN (p.timezone IS NOT NULL AND p.timezone != '') THEN p.timezone ELSE ca.timezone END) > now()

            AND es.is_manual_task = false

            AND cp.active

            ORDER BY es.scheduled_at ASC

            LIMIT 1

        -- FOR UPDATE SKIP LOCKED

        ) tobepushed on true

        where

        -- last message should be sent atleast delay wrt to new messages seconds ago, max delay hardcapped to 120:
        (eset.last_sent_sent_at is null or extract(epoch from now() - eset.last_sent_sent_at) >= least(extract(epoch from (tobepushed.scheduled_at - eset.last_sent_scheduled_at)), 120))

    ;
    """

        .map(rs => {
          EmailScheduledIdsFoundForSendingViaMQ(
            emailScheduledId = rs.long("id"),
            sendingServerIP = rs.string("sending_server_ip"),
            service_provider = EmailServiceProvider.fromKey(rs.string("service_provider")).get,
            teamId =  TeamId(id = rs.long("team_id"))
          )
        })
        .list
        .apply()

    }
 

  }


  // for org_ids in AppConfig.orgIdsForPushToRabbitMqV2Flow
  private def findEmailSettingsForQueueingCampaignEmailsToRabbitMQV2(
                                                                      toBeSentTill: DateTime,
                                                                      logger: SRLogger
                                                                    ): Try[Map[TeamId, List[(EmailSettingId, EmailServiceProvider)]]] = {


    DBUtils.readOnly(
      logger = logger.appendLogRequestId("findEmailSettingsForQueueingCampaignEmailsToRabbitMQV2")
    ) { implicit session =>

      sql"""

      SELECT

      DISTINCT (esettings.id) as email_setting_id, esettings.team_id, esettings.service_provider

      from email_settings esettings


      -- inner join (
      --   select distinct sender_email_settings_id from campaigns where sender_email_settings_id is not null and status = 'running'
      -- ) disteset on esettings.id = disteset.sender_email_settings_id

      INNER JOIN campaign_email_settings ces ON esettings.id = ces.sender_email_setting_id and esettings.team_id = ces.team_id
      INNER JOIN campaigns c ON (

        c.id = ces.campaign_id
        AND c.status = ${CampaignStatus.RUNNING.toString}

      )

      -- check there is some email to be scheduled
      INNER JOIN emails_scheduled es ON (

        es.inbox_email_setting_id = esettings.id
        AND es.scheduled_from_campaign
        AND NOT es.pushed_to_rabbitmq
        AND NOT es.sent
        AND es.scheduled_at < $toBeSentTill

        -- we are deleting stuck emails after 1 hour, even when in queue (HourlyCronService.deleteStuckEmails)
        -- this condition optimizes this query by reducing search space
        AND es.scheduled_at > now() - interval '6 hours'

      )

    join accounts acc on acc.id = esettings.account_id

    -- ignore email settings that are paused
    WHERE

      acc.org_id IN (${AppConfig.orgIdsForPushToRabbitMqV2Flow})
      AND esettings.status = ${EmailSettingStatus.Active.toString}
      AND

      (
        esettings.paused_till IS NULL
        OR esettings.paused_till < now()
      )

      -- Added on 7th April 2022 for scaling issue
      AND (
        esettings.last_sent_sent_at is null
        OR
        esettings.last_sent_sent_at < now() - interval '60 seconds'
      )

      -- check that there is no other message from email that is being sent now
      -- get the count of message in mq (for sending) for each email setting
      -- do not allow a email setting to have more than 1 message in mq
      GROUP BY esettings.id
      HAVING (SELECT NOT EXISTS(

        SELECT esc.id FROM emails_scheduled esc
          WHERE
            esc.inbox_email_setting_id = esettings.id
            AND esc.pushed_to_rabbitmq
            AND not esc.sent
            AND esc.scheduled_from_campaign

          -- we are deleting stuck emails after 1 hour, even when in queue
          -- this condition optimizes this query by reducing search space
          AND esc.scheduled_at > now() - interval '6 hours'

      ))

      -- Added on 7th April 2022 for scaling issue
      limit 100

    ;
    """

        .map(rs => (
          TeamId(id = rs.long("team_id")),
          EmailSettingId(emailSettingId = rs.long("email_setting_id")),
          EmailServiceProvider.fromKey(rs.string("service_provider")).get
        ))
        .list
        .apply()
        .groupBy(_._1)
        .map { case (teamId, group) =>
          teamId -> group.map(eset => (eset._2, eset._3))
        }

    }


  }


  //TODO 8-May-2024: Add team_id for inner joins with campaign and prospects table
  // for org_ids in AppConfig.orgIdsForPushToRabbitMqV2Flow
  def findEmailIdsForQueueingCampaignEmailsToRabbitMQV2(
                                                         emailSettingData:  Map[TeamId, List[(EmailSettingId, EmailServiceProvider)]],
                                                         toBeSentTill: DateTime,
                                                         logger: SRLogger
                                                       ): Try[Seq[EmailScheduledIdsFoundForSendingViaMQ]] = {

    if (emailSettingData.isEmpty) Success(Seq()) else {
      Try {
        emailSettingData.flatMap {
          case (teamId, emailSettingIds) =>

            DBUtils.readOnly(
              logger = logger.appendLogRequestId("findEmailIdsForQueueingCampaignEmailsToRabbitMQV2")
            ) { implicit session =>

              sql"""

            select tobepushed.id, rms.public_ip as sending_server_ip, eset.service_provider

              from email_settings eset

              join accounts acc on acc.id = eset.account_id
              join organizations org on org.id = acc.org_id
              join rep_mail_servers rms ON rms.id = (case when eset.rep_mail_server_id IS NOT NULL then eset.rep_mail_server_id else org.rep_mail_server_id end)


              -- get a max of 1 email to be pushed to queue
              inner join lateral (

                SELECT es.id, es.scheduled_at
                  FROM emails_scheduled es
                  inner join email_message_data ems on ems.team_id = es.team_id and es.id = ems.es_id
                  INNER JOIN campaigns ca ON ca.id = es.campaign_id
                  INNER JOIN prospects p ON p.id = es.prospect_id

                  INNER JOIN campaigns_prospects cp ON cp.campaign_id = ca.id AND cp.prospect_id = p.id AND cp.team_id = ca.team_id



                  WHERE

                  es.inbox_email_setting_id = eset.id
                  AND es.team_id = eset.team_id

                  AND es.pushed_to_rabbitmq = FALSE
                  AND es.sent = FALSE
                  AND es.scheduled_at < $toBeSentTill
                  AND NOT es.is_manual_task

                  -- this condition optimizes this query by reducing search space
                  AND es.scheduled_at > now() - interval '6 hours'

                  AND es.scheduled_from_campaign

                  AND es.pushed_to_queue_for_deletion is null

                  -- otherwise email goes before SchedulerEmailCronService Step2 where body and subject are added
                  AND (ems.body IS NOT NULL AND ems.subject IS NOT NULL)

                  AND ((current_timestamp AT TIME ZONE (CASE WHEN (p.timezone IS NOT NULL AND p.timezone != '') THEN p.timezone ELSE ca.timezone END))::date + ca.daily_from_time * interval '1' second)::timestamptz AT TIME ZONE 'UTC' AT TIME ZONE (CASE WHEN (p.timezone IS NOT NULL AND p.timezone != '') THEN p.timezone ELSE ca.timezone END) <= now()

                  AND ((current_timestamp AT TIME ZONE (CASE WHEN (p.timezone IS NOT NULL AND p.timezone != '') THEN p.timezone ELSE ca.timezone END))::date + ca.daily_till_time * interval '1' second)::timestamptz AT TIME ZONE 'UTC' AT TIME ZONE (CASE WHEN (p.timezone IS NOT NULL AND p.timezone != '') THEN p.timezone ELSE ca.timezone END) > now()

                  AND cp.active

                  ORDER BY es.scheduled_at ASC

                  LIMIT 1

              -- FOR UPDATE SKIP LOCKED

              ) tobepushed on true

              where

              eset.id IN (${emailSettingIds.map(_._1.emailSettingId)})
              AND eset.team_id = ${teamId.id}
              AND eset.status = ${EmailSettingStatus.Active.toString}
              AND

              -- last message should be sent atleast delay wrt to new messages seconds ago, max delay hardcapped to 120:
              (eset.last_sent_sent_at is null or extract(epoch from now() - eset.last_sent_sent_at) >= least(extract(epoch from (tobepushed.scheduled_at - eset.last_sent_scheduled_at)), 120))

          ;
          """

                .map(rs => {
                  EmailScheduledIdsFoundForSendingViaMQ(
                    emailScheduledId = rs.long("id"),
                    sendingServerIP = rs.string("sending_server_ip"),
                    service_provider = EmailServiceProvider.fromKey(rs.string("service_provider")).get,
                    teamId = teamId
                  )
                })
                .list
                .apply()

            }.get
        }.toSeq
      }

    }


  }


  // returns (emailScheduledId, sending_server_ip)
  // Does NOT schedule manual emails
  // for org_ids other than those in AppConfig.orgIdsForPushToRabbitMqV2Flow
  def findAndUpdateEmailIdsForQueueingCampaignEmailsToRabbitMQ(

                                                                toBeSentTill: DateTime

                                                              ): Try[Seq[EmailScheduledIdsFoundForSendingViaMQ]] = {

    val logger = new SRLogger(s"findAndUpdateEmailIdsForQueueingCampaignEmailsToRabbitMQ ${StringUtils.genLogTraceId}")

    for {

      foundEmailsForScheduling <- findEmailIdsForQueueingCampaignEmailsToRabbitMQ(
        toBeSentTill = toBeSentTill,
        logger = logger
      )

      updatedEmailsForScheduling <-
        Try {

          // converting List[FindEmailIdsForQueueingCampaignEmailsToRabbitMQ] to Map[TeamId, List[Long]]
          val emailScheduledIdsToBePushed = foundEmailsForScheduling
            .groupBy(_.teamId)
            .map { case(teamId, group) =>
              teamId -> group.map(_.emailScheduledId)
            }

          updateEmailIdsForQueueingCampaignEmailsToRabbitMQ(
            emailScheduledToBePushed = emailScheduledIdsToBePushed,
            logger = logger
          )
            .toSet

        }


    } yield {

      foundEmailsForScheduling
        .filter(em => {
          updatedEmailsForScheduling.contains(em.emailScheduledId)
        })

    }

  }

  // returns (emailScheduledId, sending_server_ip)
  // Does NOT schedule manual emails
  // for org_ids in AppConfig.orgIdsForPushToRabbitMqV2Flow
  def findAndUpdateEmailIdsForQueueingCampaignEmailsToRabbitMQV2(

                                                                  toBeSentTill: DateTime

                                                                ): Try[Seq[EmailScheduledIdsFoundForSendingViaMQ]] = {

    val logger = new SRLogger(s"findAndUpdateEmailIdsForQueueingCampaignEmailsToRabbitMQ ${StringUtils.genLogTraceId}")

    for {

      emailSettingDataForScheduling: Map[TeamId, List[(EmailSettingId, EmailServiceProvider)]] <- findEmailSettingsForQueueingCampaignEmailsToRabbitMQV2(
        toBeSentTill = toBeSentTill,
        logger = logger
      )

      foundEmailsForScheduling <- findEmailIdsForQueueingCampaignEmailsToRabbitMQV2(
        emailSettingData = emailSettingDataForScheduling,
        toBeSentTill = toBeSentTill,
        logger = logger
      )

      updatedEmailsForScheduling <-
        Try {

          // converting List[FindEmailIdsForQueueingCampaignEmailsToRabbitMQ] to Map[TeamId, List[Long]]
          val emailScheduledIdsToBePushed = foundEmailsForScheduling
            .groupBy(_.teamId)
            .map { case (teamId, group) =>
              teamId -> group.map(_.emailScheduledId)
            }

          updateEmailIdsForQueueingCampaignEmailsToRabbitMQ(
            emailScheduledToBePushed = emailScheduledIdsToBePushed,
            logger = logger
          )
            .toSet

        }


    } yield {

      foundEmailsForScheduling
        .filter(em => {
          updatedEmailsForScheduling.contains(em.emailScheduledId)
        })

    }

  }


  // if there is an error while sending email from queue, drop it from queue
  // also, pause that email setting
  /*
  def dropFromQueue(emailScheduledId: Long): Try[Option[EmailScheduled]] = Try {
    DB autoCommit { implicit session =>

      sql"""
          UPDATE emails_scheduled
          SET
            pushed_to_rabbitmq = false,
            pushed_to_rabbitmq_at = null
          WHERE id = $emailScheduledId
          RETURNING *;
      """
        .map(EmailScheduled.fromDb)
        .single
        .apply()

    }
  }
  */


  def markAsSent(emailSentId: Long,
                 data: EmailToBeSent,
                 campaignName: Option[String],
                 stepName: Option[String],
                 repTrackingHostId: Int,
                 Logger: SRLogger,
                 toEmails: String,
                 toCcEmails: Option[String],
                 toBccEmails: Option[String],
                 emailThreadId: Long,
                 to_name: Option[String],
                 teamId: TeamId
                )(implicit session: DBSession): Try[Option[EmailScheduled]] = Try {

    // newinbox: store to_emails with name (name <email>, name2 <email2>) in json in emails_scheduled table - IEmailAddress.stringify is used for this
    sql"""
          UPDATE emails_scheduled e
          SET
           
            sender_email_settings_id = ${data.sender_email_settings_id},
            inbox_email_setting_id = ${data.sender_email_settings_id},
            sent_at = now(),
            sent = true,
            campaign_name = $campaignName,
            step_name = $stepName,

            rep_tracking_host_id = $repTrackingHostId,

            email_thread_id = $emailThreadId,
            
            temp_thread_id = -2,
            has_custom_tracking_domain = ${data.hasCustomTrackingDomain},
            req_log_trace_id = ${Logger.logTraceId}
          FROM email_message_data ems
          WHERE
           ems.team_id = e.team_id
           and e.id = ems.es_id
          AND e.id = $emailSentId
          AND e.team_id = ${teamId.id}
          RETURNING 
          e.is_manual_task, 
          e.step_id, 
          e.step_name, 
          e.campaign_id, 
          e.campaign_name, 
          e.id, 
          ems.subject, 
          ems.message_id,
          ems.references_header,
          e.prospect_id,
          e.sender_email_settings_id,
          e.email_thread_id,
          ems.outlook_msg_id,
          e.scheduled_at,
          e.sent_at,
          e.account_id,
          e.team_id
          ;
      """
      .map(fromDb)
      .single
      .apply()

  }

  def findEmailDataForCheckingBotClicks(
                                         emailSentId: Long
                                       ): Try[Option[EmailDataForCheckingBotClick]] = Try {

    DB readOnly { implicit session =>

      sql"""
       select

          t.org_id,
          es.id as esid,
          es.sent_at

        from emails_scheduled es
        join teams t on t.id = es.team_id

        where
         es.id = $emailSentId
          and es.sent
          and es.sent_at is not null
          limit 1
        ;
       """
        .map(rs => {

          EmailDataForCheckingBotClick(
            sentAt = rs.jodaDateTime("sent_at"),
            emailSentId = rs.long("esid"),
            orgId = rs.long("org_id")
          )

        })
        .single
        .apply()
    }
  }

  // todo: this should return a Try
  def isClicked(emailScheduledId: Long, clickedUrl: String, clicked_at: DateTime,
                traceReqId: Option[Long],
                Logger: SRLogger
               ): Try[Seq[EmailScheduled]] = Try {
    // DB localTx { implicit session =>

    // avoid multiple click tracks immediately when email first clicked
    // only track for emails that were sent via campaign or manually by the smartreach user
    val markedClicked = DB.autoCommit { implicit session =>
      sql"""
            UPDATE emails_scheduled e
            SET
                clicked = true,
                clicked_at = COALESCE(clicked_at, $clicked_at),
                opened = CASE
                             WHEN has_open_tracking IS true THEN true
                             ELSE opened
                         END,
                opened_at = CASE
                                WHEN has_open_tracking IS true THEN COALESCE(opened_at, $clicked_at)
                                ELSE opened_at
                            END,
                click_tracking_trace_req_id = COALESCE(click_tracking_trace_req_id, $traceReqId)
                FROM email_message_data ems
            WHERE

                ems.team_id = e.team_id
                and e.id = ems.es_id
                and e.id = $emailScheduledId
                AND (e.clicked_at IS NULL OR e.clicked_at < now() - interval '1 minute')
                AND NOT e.bounced
                AND NOT e.email_soft_bounced
            RETURNING 
            e.is_manual_task, 
              e.step_id, 
              e.step_name, 
              e.campaign_id, 
              e.campaign_name, 
              e.id, 
              ems.subject, 
              ems.message_id,
              ems.references_header,
              e.prospect_id,
              e.sender_email_settings_id,
              e.email_thread_id,
              ems.outlook_msg_id,
              e.scheduled_at,
              e.sent_at,
              e.account_id,
              e.team_id
            ;
          """
        .map(fromDb)
        .list
        .apply()
    }

    if (markedClicked.nonEmpty) {

      val firstMarkedClicked = markedClicked.head

      if (firstMarkedClicked.sendEmailFromCampaignDetails.isDefined && firstMarkedClicked.prospect_id.isDefined) {

        campaignProspectDAO._hasClicked(
          campaignId = firstMarkedClicked.sendEmailFromCampaignDetails.get.campaign_id,
          prospect_id = firstMarkedClicked.prospect_id.get,
          clicked_at = clicked_at
        )
      }

      if (firstMarkedClicked.prospect_id.isDefined) {

        prospectEmailsDAOService._hasClicked(prospect_id = firstMarkedClicked.prospect_id.get, clicked_at = clicked_at, Logger = Logger)
      }

      // save click events
      val events = markedClicked
        .filter(em => em.sendEmailFromCampaignDetails.isDefined && em.account_id.isDefined && em.team_id.isDefined && em.prospect_id.isDefined)
        .map(m =>

          CreateProspectEventDB(

            event_type = EventType.EMAIL_LINK_CLICKED,
            doer_account_id = None,
            doer_account_name = None,

            assigned_to_account_id = None,
            assigned_to_account_name = None,

            old_category = None,
            new_category = None,

            prospect_id = m.prospect_id.get,
            email_thread_id = m.email_thread_id,

            // Extracting following 4 values is same at some places. TODO: we can extract logic in a method.
            campaign_id = m.sendEmailFromCampaignDetails.map(_.campaign_id),
            campaign_name = m.sendEmailFromCampaignDetails.map(_.campaign_name),

            step_id = m.sendEmailFromCampaignDetails.flatMap(_.stepDetails.map(_.step_id)),
            step_name = m.sendEmailFromCampaignDetails.flatMap(_.stepDetails.map(_.step_name)),

            clicked_url = Some(clickedUrl),

            account_id = m.account_id.get,
            team_id = m.team_id.get,
            email_scheduled_id = m.id,

            // TODO prospect_events: check if we can handle this in better way
            created_at = clicked_at,

            task_type = None,
            channel_type = None,
            task_uuid = None,
            call_conference_uuid = None,

            duplicates_merged_at = None,
            total_merged_prospects = None,
            potential_duplicate_prospect_id = None

          )
        )

      prospectAddEventDAO.addEvents(events = events).get

    }

    markedClicked


    // }
  }


  // mark opened by email_thread_id as well
  // only return campaign_name, prospect_id
  // only track for emails that were sent via campaign or manually by the smartreach user
  def isOpened(emailSentId: Long, opened_at: DateTime, ip: String,

               // thread id is sent for reply-tracked emails
               email_thread_id: Option[Long],
               traceReqId: Option[Long],
               Logger: SRLogger

              ): Try[Seq[EmailScheduled]] = blocking {
    Try {

      val Logger = new SRLogger(s"EmailScheduled.isOpened ($traceReqId) ")

      val latestOpenedAt: Option[DateTime] = {

        Helpers.logTimeTaken(
          Logger = Logger.appendLogRequestId("ProspectEvent.latestOpenedAtSQL"),
          thresholdInMillis = 500
        ) {

          DB readOnly { implicit session =>

            sql"""
          select ev.created_at as event_at
          from prospect_events ev
          where ev.email_scheduled_id = $emailSentId
          and ev.event_type = 'opened'

          order by ev.created_at desc
          limit 1
          ;
         """
              .map(rs => rs.jodaDateTimeOpt("event_at"))
              .single
              .apply()
              .flatten
          }
        }
      }

      // if latest opened at in last minute, ignore
      if (latestOpenedAt.isDefined && latestOpenedAt.get.isAfter(DateTime.now.minusMinutes(1))) {

        Logger.info(s"MQOpenTracker EmailScheduled.isOpened $emailSentId :: $opened_at :: latestOpenedAt in past minutes, ignoring :: latestOpenedAt: $latestOpenedAt: request_ip: $ip")

        Seq()

      } else {
        val markedOpened: List[EmailScheduled] =
          Helpers.logTimeTaken(
            Logger = Logger.appendLogRequestId("EmailScheduled.markOpened"),
            thresholdInMillis = 500
          ) {

            DB autoCommit { implicit session =>

              val emailThreadIdORCheck: SQLSyntax = if (email_thread_id.isEmpty) sqls" " else sqls" OR e.email_thread_id = ${email_thread_id.get} "

              // avoid multiple open tracks immediately when email first opened

              sql"""
              UPDATE emails_scheduled e SET opened = true, opened_at = COALESCE(opened_at, $opened_at),
                open_tracking_trace_req_id = COALESCE (open_tracking_trace_req_id, $traceReqId)

              FROM email_message_data ems
              WHERE
                 ems.team_id = e.team_id
                 and e.id = ems.es_id
              AND (e.opened_at IS NULL OR e.opened_at < now() - interval '1 minute')

                AND NOT e.bounced
                AND NOT e.email_soft_bounced

                AND (
                  e.id = $emailSentId
                  $emailThreadIdORCheck
                )

              RETURNING 
              e.is_manual_task, 
              e.step_id, 
              e.step_name, 
              e.campaign_id, 
              e.campaign_name, 
              e.id, 
              ems.subject, 
              ems.message_id,
              ems.references_header,
              e.prospect_id,
              e.sender_email_settings_id,
              e.email_thread_id,
              ems.outlook_msg_id,
              e.scheduled_at,
              e.sent_at,
              e.account_id,
              e.team_id
              ;
            """
                .map(fromDb)
                .list
                .apply()
            }
          }


        if (markedOpened.nonEmpty) {

          val firstMarkedOpened = markedOpened.head

          if (firstMarkedOpened.sendEmailFromCampaignDetails.isDefined && firstMarkedOpened.prospect_id.isDefined) {

            Helpers.logTimeTaken(
              Logger = Logger.appendLogRequestId("CampaignProspect._hasOpened"),
              thresholdInMillis = 500
            ) {

              campaignProspectDAO._hasOpened(
                firstMarkedOpened.sendEmailFromCampaignDetails.get.campaign_id,
                prospect_id = firstMarkedOpened.prospect_id.get,
                opened_at = opened_at
              )

            }
          }

          if (firstMarkedOpened.prospect_id.isDefined) {
            Helpers.logTimeTaken(
              Logger = Logger.appendLogRequestId("Prospect._hasOpened"),
              thresholdInMillis = 500
            ) {

              prospectEmailsDAOService._hasOpened(
                prospect_id = firstMarkedOpened.prospect_id.get,
                opened_at = opened_at,
                Logger = Logger
              )

            }
          }

          // save open events
          val events = markedOpened
            // account_id if account deleted (from team)
            .filter(em => em.prospect_id.isDefined && em.account_id.isDefined && em.team_id.isDefined)
            .groupBy(_.id).map(_._2.head) // one open event per email
            .map(m => CreateProspectEventDB(

              event_type = EventType.EMAIL_OPENED,
              doer_account_id = None,
              doer_account_name = None,

              assigned_to_account_id = None,
              assigned_to_account_name = None,

              old_category = None,
              new_category = None,

              prospect_id = m.prospect_id.get,
              email_thread_id = m.email_thread_id,

              // Extracting following 4 values is same at some places. TODO: we can extract logic in a method.
              campaign_id = m.sendEmailFromCampaignDetails.map(_.campaign_id),
              campaign_name = m.sendEmailFromCampaignDetails.map(_.campaign_name),

              step_id = m.sendEmailFromCampaignDetails.flatMap(_.stepDetails.map(_.step_id)),
              step_name = m.sendEmailFromCampaignDetails.flatMap(_.stepDetails.map(_.step_name)),

              clicked_url = None,

              account_id = m.account_id.get,
              team_id = m.team_id.get,
              email_scheduled_id = m.id,
              // TODO prospect_events: check if we can handle this in better way

              created_at = opened_at,

              task_type = None,
              channel_type = None,
              task_uuid = None,
              call_conference_uuid = None,

              duplicates_merged_at = None,
              total_merged_prospects = None,
              potential_duplicate_prospect_id = None

            )).toSeq

          Logger.info(s"MQOpenTracker EmailScheduled.isOpened $emailSentId :: $opened_at :: total events: ${markedOpened.map(_.id)} :: filtered: ${events.map(_.email_scheduled_id)}")

          Helpers.logTimeTaken(
            Logger = Logger.appendLogRequestId("ProspectEvent.addEvents"),
            thresholdInMillis = 500
          ) {

            prospectAddEventDAO.addEvents(events = events).get

          }

        }

        markedOpened


      }

      /*
      DB localTx { implicit session =>

        val markedOpened =
          sql"""

      WITH sent AS (
        select * from emails_scheduled where id = $emailSentId LIMIT 1
      )
      UPDATE emails_scheduled e SET opened = true, opened_at = now()
      WHERE NOT e.opened AND e.campaign_id = (SELECT campaign_id FROM sent) AND e.prospect_id = (SELECT prospect_id FROM sent)
      RETURNING *;
"""
            /*
          sql"""
              UPDATE emails_scheduled
              SET
                opened = true,
                opened_at = now()
              WHERE id = $emailSentId
                AND opened = false
              RETURNING *;
          """
            */
            .map(EmailScheduled.fromDb)
            .list
            .apply()


        if (markedOpened.nonEmpty) {

          val firstMarkedOpened = markedOpened.head

          if (firstMarkedOpened.campaign_id.isDefined) {

            CampaignProspect._hasOpened(firstMarkedOpened.campaign_id.get, prospect_id = firstMarkedOpened.prospect_id)
          }

          // save open events
          val events = markedOpened.map(m => CreateProspectEventDB(

            event_type = EventType.OPENED,
            doer_account_id = None,
            doer_account_name = None,

            assigned_to_account_id = None,
            assigned_to_account_name = None,

            old_category = None,
            new_category = None,

            prospect_id = m.prospect_id,

            campaign_id = m.campaign_id,
            campaign_name = Some(m.campaign_name),

            step_id = m.step_id,
            step_name = m.step_name,

            clicked_url = None

          ))

          ProspectEvent.addEvents(events = events)

        }

        markedOpened


      }

      */
    }
  }

  def updateEmailHasOptedOut(
                              emailScheduledId: EmailScheduledIdOrTaskId.EmailScheduledId,
                              traceReqId: Option[Long],
                              teamId: TeamId
                            )(implicit session: DBSession): Option[Long] =  {

    sql"""
      UPDATE emails_scheduled
      SET
        opted_out = true,
        opted_out_at = COALESCE(opted_out_at, now()),
        unsubscribe_tracking_trace_req_id = COALESCE(unsubscribe_tracking_trace_req_id, $traceReqId)
      WHERE id = ${emailScheduledId.id}
      AND team_id = ${teamId.id}
      RETURNING id;
    """
      .map(_.long("id"))
      .single
      .apply()

  }

  def getEmailDetailsForHasOptedOutV2(
                                       emailScheduledId: EmailScheduledIdOrTaskId.EmailScheduledId
                                     )(implicit session: DBSession): Option[EmailDetailsForHasOptedOut] = {

    sql"""
         select
         es.bounced,
         es.campaign_id,
         es.prospect_id,
          es.team_id
          from emails_scheduled es
          where es.id = ${emailScheduledId.id}
       """
      .map(rs => EmailDetailsForHasOptedOut(
        bounced = rs.booleanOpt("bounced").getOrElse(false),
        campaign_id = rs.longOpt("campaign_id").map(CampaignId(_)),
        prospect_id = rs.longOpt("prospect_id").map(ProspectId(_)),
        team_id = TeamId(id = rs.long("team_id"))
      )
      )
      .single
      .apply()

  }


  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  //use case:  /tv4/:code/optout  -> postUnsubscribeTV4
  def __getOptedOutProspectDetails(campaignId: CampaignId, prospectId: ProspectId, teamId: TeamId): Try[Option[OptedOutEmailDetails]] = {

    val query =
      sqls"""
        with ca as (
          select c.name as campaign_name, c.add_prospect_to_dnc_on_opt_out, c.id as campaign_id from campaigns c where c.id = ${campaignId.id}
        )

        select

        (select campaign_name from ca) as campaign_name,
        (select campaign_id from ca) as campaign_id,
        (select add_prospect_to_dnc_on_opt_out from ca) as add_prospect_to_dnc_on_opt_out,

        a.first_name,
        a.last_name,


        p.team_id,
        p.account_id,
        p.ta_id,
        pe.email

        from prospects p
        INNER JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
        join accounts a on a.id = p.account_id
        where p.id = ${prospectId.id}
        AND p.team_id = ${teamId.id}
        ;
      """

    val logger = new SRLogger(logRequestId = "EmailScheduedDAO.__getOptedOutProspectDetails")
    //DB readOnly { implicit session =>
    DBUtils.readOnlyWithExplain(
      query = query,
      logger = logger
    ) { implicit session =>
      sql""" $query """
        .map(rs => OptedOutEmailDetails(

          campaign_name = rs.stringOpt("campaign_name"),
          campaign_id = rs.longOpt("campaign_id"),
          add_prospect_to_dnc_on_opt_out = rs.booleanOpt("add_prospect_to_dnc_on_opt_out").getOrElse(false),

          account_name = rs.string("first_name") + " " + rs.string("last_name"),
          prospect_email = rs.string("email"),

          team_id = rs.long("team_id"),
          account_id = rs.long("account_id"),
          ta_id = rs.long("ta_id"),
          prospect_id = prospectId.id
        ))
        .single
        .apply()
    }
  }



//  def getSentEmailsForCheckingRepliesOld(senderEmailSettingsIds: Seq[Int], teamId: Long,logger: SRLogger): Seq[EmailScheduledForCheckingReplies] = blocking {
//    // DB
//    DB readOnly { implicit session =>
//
//      /*
//      val query = s"""
//
//      select p.email AS to_email, es.prospect_id, es.campaign_id, es.step_id, es.sent_at, es.message_id,
//      es.references_header, p.first_name, p.last_name, p.company
//    from emails_scheduled es
//    inner join prospects p on p.id = es.prospect_id
//    where (es.reply_to_email = '$replyToEmail' or es.sender_email_settings_id IN (${senderEmailSettingsIds.mkString(", ")}) )
//    and es.sent and es.sent_at > now() - interval '1 week'
//    order by es.sent_at desc;
//
//    """
//      */
//
//      // NOT SURE WHERE "es.reply_to_email = $replyToEmail" condition required: where (es.reply_to_email = $replyToEmail or es.sender_email_settings_id IN ($senderEmailSettingsIds) )
//
//      /*
//      val query =
//        sql"""
//
//      select es.to_email AS to_email, es.prospect_id, es.campaign_id, es.step_id, es.sent_at, es.message_id,
//      es.references_header, es.email_thread_id, es.gmail_thread_id, es.subject, es.outlook_msg_id,
//      es.outlook_conversation_id
//
//    from emails_scheduled es
//
//    --where (es.reply_to_email = $replyToEmail or es.sender_email_settings_id IN ($senderEmailSettingsIds) )
//
//    where es.sender_email_settings_id IN ($senderEmailSettingsIds)
//    and es.sent and es.sent_at > now() - interval '2 week'
//    and es.prospect_id is not null
//
//    and es.team_id = $teamId
//
//    and es.scheduled_from_campaign
//    order by es.sent_at desc;
//
//    """
//      */
//
//      if (senderEmailSettingsIds.isEmpty) Seq()
//      else {
//
//
//        val query = sql"""
//
//              select pe.email AS prospect_email, es.to_email as to_email, es.prospect_id, es.prospect_account_id, es.campaign_id, es.step_id, es.sent_at, es.message_id,
//              es.references_header, es.email_thread_id, es.gmail_thread_id, es.subject, es.outlook_msg_id,
//              es.outlook_conversation_id
//
//              from emails_scheduled es
//              inner join prospects_emails pe on (pe.prospect_id = es.prospect_id and pe.team_id = $teamId)
//
//              where es.inbox_email_setting_id IN ${SQLUtils.generateSQLValuesClause(senderEmailSettingsIds)}
//              and es.sent and es.sent_at > now() - interval '2 week'
//              and es.prospect_id is not null
//
//              and es.team_id = $teamId
//
//              and es.scheduled_from_campaign
//              ;
//
//            """
//
//
//        //Logger.info(s"query: $query")
//        val emails =
//          query
//            .map(rs => {
//
//              EmailScheduledForCheckingReplies(
//                prospect_id = ProspectId(id = rs.long("prospect_id")),
//                step_id = rs.longOpt("step_id").map(s => StepId(id = s)),
//                campaign_id = rs.longOpt("campaign_id").map(c => CampaignId(id = c)),
//
//                prospect_email = rs.string("prospect_email"),
//
//                // fixed for now: newinbox take this result from the new to_emails json column
//                // to_email = rs.string("to_email").toLowerCase(),
//                // Feb 2020: this list will only contain emails that were scheduled_from_campaign, so there can be only one to_email as of now.
//                to_email = IEmailAddress.parse(emailsStr = rs.string("to_email"))(logger = logger )
//                  .get
//                  .headOption
//                  .map(_.email.trim.toLowerCase)
//                  .getOrElse(""),
//
//                message_id = rs.stringOpt("message_id"),
//                references_header = rs.stringOpt("references_header"),
//                sent_at = rs.jodaDateTimeOpt("sent_at"),
//                subject = rs.stringOpt("subject"),
//
//                // for email_threads
//                email_thread_id = rs.longOpt("email_thread_id"),
//                gmail_thread_id = rs.stringOpt("gmail_thread_id"),
//                outlook_msg_id = rs.stringOpt("outlook_msg_id"),
//                outlook_conversation_id = rs.stringOpt("outlook_conversation_id"),
//                prospect_account_id = rs.longOpt("prospect_account_id").map(p => ProspectAccountsId(id = p))
//
//              )
//            })
//            .list
//            .apply()
//            .sortBy(_.sent_at)
//            .reverse
//
//        //    Logger.info(s"OK EmailScheduled.getSentEmailsForCheckingReplies for $replyToEmail :: ${emails.length}")
//
//        emails
//      }
//
//    }
//  }

  def getSentEmailsForCheckingRepliesNew(senderEmailSettingsIds: Seq[Int], teamId: Long,logger: SRLogger): Seq[EmailScheduledForCheckingReplies] = blocking {
    // DB
    DB readOnly { implicit session =>

      /*
      val query = s"""

      select p.email AS to_email, es.prospect_id, es.campaign_id, es.step_id, es.sent_at, es.message_id,
      es.references_header, p.first_name, p.last_name, p.company
    from emails_scheduled es
    inner join prospects p on p.id = es.prospect_id
    where (es.reply_to_email = '$replyToEmail' or es.sender_email_settings_id IN (${senderEmailSettingsIds.mkString(", ")}) )
    and es.sent and es.sent_at > now() - interval '1 week'
    order by es.sent_at desc;

    """
      */

      // NOT SURE WHERE "es.reply_to_email = $replyToEmail" condition required: where (es.reply_to_email = $replyToEmail or es.sender_email_settings_id IN ($senderEmailSettingsIds) )

      /*
      val query =
        sql"""

      select es.to_email AS to_email, es.prospect_id, es.campaign_id, es.step_id, es.sent_at, es.message_id,
      es.references_header, es.email_thread_id, es.gmail_thread_id, es.subject, es.outlook_msg_id,
      es.outlook_conversation_id

    from emails_scheduled es

    --where (es.reply_to_email = $replyToEmail or es.sender_email_settings_id IN ($senderEmailSettingsIds) )

    where es.sender_email_settings_id IN ($senderEmailSettingsIds)
    and es.sent and es.sent_at > now() - interval '2 week'
    and es.prospect_id is not null

    and es.team_id = $teamId

    and es.scheduled_from_campaign
    order by es.sent_at desc;

    """
      */

      if (senderEmailSettingsIds.isEmpty) Seq()
      else {


        val query =
          sql"""

              select pe.email AS prospect_email, ems.to_email as to_email, es.prospect_id, es.prospect_account_id, es.campaign_id, es.step_id, es.sent_at, ems.message_id,
              ems.references_header, ems.email_thread_id, ems.gmail_thread_id, ems.subject, ems.outlook_msg_id,
              ems.outlook_conversation_id

              from emails_scheduled es
              inner join email_message_data ems on es.id = ems.es_id and es.team_id = ems.team_id
              inner join prospects_emails pe on (pe.prospect_id = es.prospect_id and pe.team_id = $teamId)

              where es.inbox_email_setting_id IN ${SQLUtils.generateSQLValuesClause(senderEmailSettingsIds)}
              and es.sent and es.sent_at > now() - interval '2 week'
              and es.prospect_id is not null

              and es.team_id = $teamId

              and es.scheduled_from_campaign
              ;

            """

        //Logger.info(s"query: $query")
        val emails =
          query
            .map(rs => {

              EmailScheduledForCheckingReplies(
                prospect_id = ProspectId(id = rs.long("prospect_id")),
                step_id = rs.longOpt("step_id").map(s => StepId(id = s)),
                campaign_id = rs.longOpt("campaign_id").map(c => CampaignId(id = c)),

                prospect_email = rs.string("prospect_email"),

                // fixed for now: newinbox take this result from the new to_emails json column
                // to_email = rs.string("to_email").toLowerCase(),
                // Feb 2020: this list will only contain emails that were scheduled_from_campaign, so there can be only one to_email as of now.
                to_email = IEmailAddress.parse(emailsStr = rs.string("to_email"))(logger = logger )
                  .get
                  .headOption
                  .map(_.email.trim.toLowerCase)
                  .getOrElse(""),

                message_id = rs.stringOpt("message_id"),
                references_header = rs.stringOpt("references_header"),
                sent_at = rs.jodaDateTimeOpt("sent_at"),
                subject = rs.stringOpt("subject"),

                // for email_threads
                email_thread_id = rs.longOpt("email_thread_id"),
                gmail_thread_id = rs.stringOpt("gmail_thread_id"),
                outlook_msg_id = rs.stringOpt("outlook_msg_id"),
                outlook_conversation_id = rs.stringOpt("outlook_conversation_id"),
                prospect_account_id = rs.longOpt("prospect_account_id").map(p => ProspectAccountsId(id = p))

              )
            })
            .list
            .apply()
            .sortBy(_.sent_at)
            .reverse

        //    Logger.info(s"OK EmailScheduled.getSentEmailsForCheckingReplies for $replyToEmail :: ${emails.length}")

        emails
      }

    }
  }


//  def getPreviousSentStepsOld(
//                               campaignId: Long,
//                               prospectId: Long,
//                               teamId: TeamId,
//                               get_skipped: Boolean
//                             ): Try[Seq[PreviousFollowUp]] = {
//
//    //DB readOnly { implicit session =>
//    val logger = new SRLogger(logRequestId = "EmailScheduledDAO.getPreviousSentSteps")
//    val selectForGetSkipped = if(get_skipped) {
//      sqls"""
//        (case when t.skipped_at is null then es.sent_at else t.skipped_at end) as sent_at
//         """
//    } else {
//      sqls"""
//           es.sent_at
//         """
//    }
//
//    val joinForGetSkipped = if (get_skipped) {
//      sqls"""
//      left join tasks t on es.team_id = t.team_id and es.prospect_id = t.prospect_id and es.campaign_id = t.campaign_id and es.step_id = t.step_id
//         """
//    } else {
//      sqls""""""
//    }
//    val whereForGetSkipped = if (get_skipped) {
//      sqls"""
//      OR t.status = ${TaskStatusType.Skipped.toString} 
//         """
//    } else {
//      sqls""""""
//    }
//    val qry = sqls"""
//        select es.from_name, es.base_body,
//        $selectForGetSkipped,
//        es.from_email,
//        es.body,
//        es.subject,
//        es.step_id,
//        es.email_thread_id,
//
//        (CASE
//          WHEN es.replied THEN 'replied'
//          WHEN es.auto_reply THEN 'auto_reply'
//          WHEN es.opted_out THEN 'unsubscribed'
//          WHEN es.out_of_office_reply THEN 'out_of_office'
//          WHEN es.bounced THEN 'bounced'
//          WHEN pe.invalid_email THEN 'invalid_email'
//          ELSE null
//
//        END) as completed_reason,
//        es.is_edited_preview_email,
//        (CASE WHEN (p.timezone IS NOT NULL AND p.timezone != '') THEN p.timezone ELSE c.timezone END) AS timezone
//      from emails_scheduled es
//      $joinForGetSkipped
//      join prospects p on p.id = es.prospect_id
//      INNER JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
//      join campaigns c on c.id = es.campaign_id
//      where
//      es.campaign_id = $campaignId
//      and es.prospect_id = $prospectId
//      and es.team_id = ${teamId.id}
//      and (
//      es.sent $whereForGetSkipped
//      )
//      and (es.base_body IS NOT NULL OR es.base_body != '')
//      and es.scheduled_from_campaign;
//      """
//
//    DBUtils.readOnlyWithExplain(
//      query = qry,
//      logger = logger
//    ) { implicit session =>
//      sql""" $qry """
//        .map(rs => {
//          val fromEmail = rs.string("from_email")
//          val followup_data = PreviousFollowUpData.AutoEmailFollowUp(
//            email_thread_id = rs.longOpt("email_thread_id"),
//            from_name = rs.string("from_name"),
//            subject = rs.stringOpt("subject").getOrElse(fromEmail),
//            body = rs.string("body"),
//            base_body = rs.string("base_body"),
//            from_email = fromEmail,
//            is_edited_preview_email = rs.booleanOpt("is_edited_preview_email").getOrElse(false)
//          )
//          PreviousFollowUp(
//            channel_follow_up_data = followup_data,
//            sent_at = rs.jodaDateTime("sent_at"),
//            timezone = rs.string("timezone"),
//            step_id = rs.longOpt("step_id"),
//            completed_reason = rs.stringOpt("completed_reason"),
//          )
//        })
//        .list
//        .apply()
//    }
//  }

  def getPreviousSentStepsNew(
                               campaignId: Long,
                               prospectId: Long,
                               teamId: TeamId,
                               get_skipped: Boolean
                             ): Try[Seq[PreviousFollowUp]] = {

    //DB readOnly { implicit session =>
    val logger = new SRLogger(logRequestId = "EmailScheduledDAO.getPreviousSentSteps")
    val selectForGetSkipped = if (get_skipped) {
      sqls"""
        (case
             when t.status = ${TaskStatusType.Done.toString} then t.done_at
             when t.status = ${TaskStatusType.Skipped.toString} then t.skipped_at
             else es.sent_at end
         ) as sent_at
         """
    } else {
      sqls"""
           es.sent_at
         """
    }
    val joinForGetSkipped = if (get_skipped) {
      sqls"""
      left join tasks t on es.team_id = t.team_id and es.prospect_id = t.prospect_id and es.campaign_id = t.campaign_id and es.step_id = t.step_id
         """
    } else {
      sqls""""""
    }
    val whereForGetSkipped = if (get_skipped) {
      sqls"""
      OR (
      t.status = ${TaskStatusType.Skipped.toString}
      OR (
      t.status = ${TaskStatusType.Done.toString} AND NOT es.sent
      )
      )
         """
    } else {
      sqls""""""
    }
    val qry = sqls"""
        select ems.from_name, ems.base_body,
        $selectForGetSkipped,
        ems.from_email,
        ems.body,
        ems.subject,
        es.step_id,
        ems.email_thread_id,

        (CASE
          WHEN es.replied THEN 'replied'
          WHEN es.auto_reply THEN 'auto_reply'
          WHEN es.opted_out THEN 'unsubscribed'
          WHEN es.out_of_office_reply THEN 'out_of_office'
          WHEN es.bounced THEN 'bounced'
          WHEN pe.invalid_email THEN 'invalid_email'
          ELSE null

        END) as completed_reason,
        es.is_edited_preview_email,
        (CASE WHEN (p.timezone IS NOT NULL AND p.timezone != '') THEN p.timezone ELSE c.timezone END) AS timezone
      from emails_scheduled es
      inner join email_message_data ems on es.id = ems.es_id and es.team_id = ems.team_id
      $joinForGetSkipped
      join prospects p on p.id = es.prospect_id
      INNER JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
      join campaigns c on c.id = es.campaign_id
      where
      es.campaign_id = $campaignId
      and es.prospect_id = $prospectId
      and es.team_id = ${teamId.id}
      and (
      es.sent $whereForGetSkipped
      
      )
      and (ems.base_body IS NOT NULL OR ems.base_body != '')
      and es.scheduled_from_campaign;
      """

    DBUtils.readOnlyWithExplain(
      query = qry,
      logger = logger
    ) { implicit session =>
      sql""" $qry """
        .map(rs => {
          val fromEmail = rs.string("from_email")
          val followup_data = PreviousFollowUpData.AutoEmailFollowUp(
            email_thread_id = rs.longOpt("email_thread_id"),
            from_name = rs.string("from_name"),
            subject = rs.stringOpt("subject").getOrElse(fromEmail),
            body = rs.string("body"),
            base_body = rs.string("base_body"),
            from_email = fromEmail,
            is_edited_preview_email = rs.booleanOpt("is_edited_preview_email").getOrElse(false)
          )
          PreviousFollowUp(
            channel_follow_up_data = followup_data,
            sent_at = rs.jodaDateTime("sent_at"),
            timezone = rs.string("timezone"),
            step_id = rs.longOpt("step_id"),
            completed_reason = rs.stringOpt("completed_reason"),
          )
        })
        .list
        .apply()
    }
  }


  def findEmailThreadIdsWhileReplyTrackingNew(
                                               replyToHeaders: Seq[String],
                                               gmailThreadIds: Seq[String],
                                               outlookConversationIds: Seq[String],
                                               teamId: TeamId,
                                               inboxEmailSettingId: Long
                                             ): Try[Seq[EmailThreadFoundForCheckingReplies]] = Try {
    // DB
    DB readOnly { implicit session =>

      val rh = replyToHeaders.filterNot(r => r.trim.isEmpty)
      val gh = gmailThreadIds.filterNot(r => r.trim.isEmpty)
      val oh = outlookConversationIds.filterNot(r => r.trim.isEmpty)


      if (rh.isEmpty && gh.isEmpty && oh.isEmpty) {
        Seq()
      } else {

        // val replyHeadersStr = "'" + replyToHeaders.mkString("','") + "'"
        // val gmailThreadIdsStr = "'" + gmailThreadIds.mkString("','") + "'"

        // if the header lists are empty, the " IN () " sql query becomes invalid
        val dummyIdToAvoidEmptyInvalidSQL = "ThIsShOuLdNoTmAtChAnYtHiNgAtAlLThisShouldNotMatchNoNoNo451Fahrenheit"
        val replyToHeadersToCheck = if (rh.nonEmpty) rh else Seq(dummyIdToAvoidEmptyInvalidSQL)
        val gmailThreadIdsToCheck = if (gh.nonEmpty) gh else Seq(dummyIdToAvoidEmptyInvalidSQL)
        val outlookConversationIdsToCheck = if (oh.nonEmpty) oh else Seq(dummyIdToAvoidEmptyInvalidSQL)


        val query = sql"""
          select

            ems.email_thread_id,
            ems.message_id,
            ems.gmail_thread_id,
            ems.outlook_conversation_id,

            es.prospect_id,
            es.prospect_account_id,
            es.step_id,
            es.campaign_id
          from emails_scheduled es
          inner join email_message_data ems on es.id = ems.es_id and es.team_id = ems.team_id

          where
            es.inbox_email_setting_id = $inboxEmailSettingId AND

            (
              ems.message_id IN ${SQLUtils.generateSQLValuesClause(replyToHeadersToCheck)}

              OR

              ems.gmail_thread_id IN ${SQLUtils.generateSQLValuesClause(gmailThreadIdsToCheck)}

              OR

              ems.outlook_conversation_id IN ${SQLUtils.generateSQLValuesClause(outlookConversationIdsToCheck)}
            )

          and es.sent
          and es.sent_at > now() - interval '3 months'
          and es.team_id = ${teamId.id}
          and ems.email_thread_id is not null
          order by es.sent_at desc
          ;
        """

        query
          .map(EmailThreadFoundForCheckingReplies.fromDb)
          .list
          .apply()

      }
    }
  }

//  def findEmailThreadIdsWhileReplyTrackingOld(
//                                               replyToHeaders: Seq[String],
//                                               gmailThreadIds: Seq[String],
//                                               outlookConversationIds: Seq[String],
//                                               teamId: TeamId,
//                                               inboxEmailSettingId: Long
//                                             ): Try[Seq[EmailThreadFoundForCheckingReplies]] = Try {
//    // DB
//    DB readOnly { implicit session =>
//
//      val rh = replyToHeaders.filterNot(r => r.trim.isEmpty)
//      val gh = gmailThreadIds.filterNot(r => r.trim.isEmpty)
//      val oh = outlookConversationIds.filterNot(r => r.trim.isEmpty)
//
//
//      if (rh.isEmpty && gh.isEmpty && oh.isEmpty) {
//        Seq()
//      } else {
//
//        // val replyHeadersStr = "'" + replyToHeaders.mkString("','") + "'"
//        // val gmailThreadIdsStr = "'" + gmailThreadIds.mkString("','") + "'"
//
//        // if the header lists are empty, the " IN () " sql query becomes invalid
//        val dummyIdToAvoidEmptyInvalidSQL = "ThIsShOuLdNoTmAtChAnYtHiNgAtAlLThisShouldNotMatchNoNoNo451Fahrenheit"
//        val replyToHeadersToCheck = if (rh.nonEmpty) rh else Seq(dummyIdToAvoidEmptyInvalidSQL)
//        val gmailThreadIdsToCheck = if (gh.nonEmpty) gh else Seq(dummyIdToAvoidEmptyInvalidSQL)
//        val outlookConversationIdsToCheck = if (oh.nonEmpty) oh else Seq(dummyIdToAvoidEmptyInvalidSQL)
//
//        val query = sql"""
//          select
//
//            es.email_thread_id,
//            es.message_id,
//            es.gmail_thread_id,
//            es.outlook_conversation_id,
//
//            es.prospect_id,
//            es.prospect_account_id,
//            es.step_id,
//            es.campaign_id
//          from emails_scheduled es
//
//          where
//            es.inbox_email_setting_id = $inboxEmailSettingId AND
//
//            (
//              es.message_id IN ${SQLUtils.generateSQLValuesClause(replyToHeadersToCheck)}
//
//              OR
//
//              es.gmail_thread_id IN ${SQLUtils.generateSQLValuesClause(gmailThreadIdsToCheck)}
//
//              OR
//
//              es.outlook_conversation_id IN ${SQLUtils.generateSQLValuesClause(outlookConversationIdsToCheck)}
//            )
//
//          and es.sent
//          and es.sent_at > now() - interval '3 months'
//          and es.team_id = ${teamId.id}
//          and es.email_thread_id is not null
//          order by es.sent_at desc
//          ;
//        """
//        query
//          .map(EmailThreadFoundForCheckingReplies.fromDb)
//          .list
//          .apply()
//
//      }
//    }
//  }


  // pushedtorabbitmq < 1hour
  def findStuckEmailAccountsForMoreThan2Hours(): Try[Seq[StuckScheduledEmailForDeletion]] = Try {
    blocking {

      val common =
        sqls"""
          select (now() - pushed_to_rabbitmq_at) as since,
          e.campaign_id,
          e.sender_email_settings_id,
          e.scheduled_manually,
          e.id,
          e.team_id
          from emails_scheduled e
          WHERE not sent
          AND e.is_manual_task = false
            """

      DB readOnly { implicit session =>
        sql"""

        $common

        AND (
          (e.scheduled_from_campaign AND pushed_to_rabbitmq AND pushed_to_rabbitmq_at < now() - interval '1 hours')
        )

        union all

        $common

        AND (
          (e.scheduled_from_campaign AND NOT pushed_to_rabbitmq AND scheduled_at < now() - interval '3 hours')
        )

        union all

        $common

        AND (
          (e.scheduled_manually AND scheduled_at < now() - interval '3 hours')
        )


    """
          .map(rs =>
            StuckScheduledEmailForDeletion(
              emailScheduledId = rs.long("id"),
              scheduledManually = rs.boolean("scheduled_manually"),
              senderEmailSettingId = rs.longOpt("sender_email_settings_id"),
              teamId = TeamId(id = rs.long("team_id"))
            )
          )
          .list
          .apply()

      }

    }
  }

  def findEmailToReplyToV3(emailThreadId: Long, teamId: TeamId): Try[Option[EmailScheduled]] = Try {
    DB readOnly { implicit session =>

      sql"""
          select
            e.is_manual_task, 
            e.step_id, 
            e.step_name, 
            e.campaign_id, 
            e.campaign_name, 
            e.id, 
            ems.subject, 
            ems.message_id,
            ems.references_header,
            e.prospect_id,
            e.sender_email_settings_id,
            e.email_thread_id,
            ems.outlook_msg_id,
            e.scheduled_at,
            e.sent_at,
            e.account_id,
            e.team_id
            from emails_scheduled e
            inner join email_message_data ems on ems.team_id = e.team_id and e.id = ems.es_id
            where e.email_thread_id = $emailThreadId
              AND e.team_id = ${teamId.id}
              and e.sent

            ORDER BY e.sent_at DESC
            LIMIT 1
        ;"""
        .map(rs => fromDb(rs))
        .headOption
        .apply()
    }
  }


//  def checkIfEmailIsAlreadySavedOld(messageIds: Seq[String],
//                                    inboxEmailSettingId: Long,
//                                    teamId: TeamId
//                                   ): Try[Seq[String]] = Try {
//    // DB
//    DB readOnly { implicit session =>
//
//
//      if (messageIds.isEmpty) {
//        Seq()
//      } else {
//        val query = sql"""
//          select
//            es.message_id
//          from emails_scheduled es
//          where
//             es.message_id IN ${SQLUtils.generateSQLValuesClause(messageIds)}
//             and es.inbox_email_setting_id = $inboxEmailSettingId
//             AND es.team_id = ${teamId.id}
//          ;
//          """
//        query
//          .map(rs => rs.string("message_id"))
//          .list
//          .apply()
//      }
//
//
//    }
//  }
  def checkIfEmailIsAlreadySavedNew(messageIds: Seq[String],
                                    inboxEmailSettingId: Long,
                                    teamId: TeamId
                                   ): Try[Seq[String]] = Try {
    // DB
    DB readOnly { implicit session =>


      if (messageIds.isEmpty) {
        Seq()
      } else {

        val query = sql"""
          select
            ems.message_id
          from email_message_data ems
          where
             ems.message_id IN ${SQLUtils.generateSQLValuesClause(messageIds)}
             and ems.inbox_email_setting_id = $inboxEmailSettingId
             AND ems.team_id = ${teamId.id}
          ;
          """

        query
          .map(rs => rs.string("message_id"))
          .list
          .apply()
      }


    }
  }


//  def findForWebhooksOld(
//                          emailScheduledIds: Seq[Long],
//                          onlyNewReplies: Boolean,
//                          teamId: Long
//                        )(using Logger: SRLogger): Try[Seq[EmailReceivedForWebhook]] = Try {
//    DB readOnly { implicit session =>
//
//      if (emailScheduledIds.isEmpty) Seq()
//      else {
//
//        val onlyNewRepliesCheck = if (onlyNewReplies) sqls""" AND NOT es.scheduled_manually AND NOT es.scheduled_from_campaign """ else sqls""
//
//        val query = sql"""
//      SELECT
//      es.campaign_name AS campaign_name,
//
//      p.id as prospect_id,
//      pe.email,
//      p.first_name,
//      p.last_name,
//      prospect_lists.name AS prospect_list,
//      p.company,
//      p.city,
//      p.country,
//      p.custom_fields,
//
//      es.id,
//      es.subject,
//      es.body,
//      es.text_body,
//      es.email_thread_id,
//      es.from_email,
//      es.sent_at as received_at,
//      es.step_name AS step_name,
//
//      es.sent_at AS sent_at,
//      es.opened_at AS opened_at,
//      es.clicked_at AS clicked_at,
//      es.bounced_at AS bounced_at,
//      es.replied_at AS replied_at,
//      es.auto_reply_at AS auto_reply_at,
//      es.out_of_office_reply_at AS out_of_office_reply_at
//
//      FROM emails_scheduled es
//
//      JOIN prospects p ON (p.id = es.prospect_id)
//      INNER JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
//      LEFT JOIN prospect_lists ON (
//        prospect_lists.id = p.list_id
//        AND
//        prospect_lists.team_id = p.team_id
//      )
//
//      WHERE es.id IN ${SQLUtils.generateSQLValuesClause(emailScheduledIds)}
//        AND es.team_id = $teamId
//        AND es.prospect_id IS NOT NULL
//        $onlyNewRepliesCheck
//      ;
//      """
//
//        query
//          .map(fromDbForWebhook)
//          .list
//          .apply()
//      }
//    }
//  }

  def findForWebhooksNew(
                          emailScheduledIds: Seq[Long],
                          onlyNewReplies: Boolean,
                          teamId: Long
                        )(using Logger: SRLogger): Try[Seq[EmailReceivedForWebhook]] = Try {
    DB readOnly { implicit session =>

      if (emailScheduledIds.isEmpty) Seq()
      else {

        val onlyNewRepliesCheck = if (onlyNewReplies) sqls""" AND NOT es.scheduled_manually AND NOT es.scheduled_from_campaign """ else sqls""

        val query = sql"""
      SELECT
      es.campaign_name AS campaign_name,

      p.id as prospect_id,
      pe.email,
      p.first_name,
      p.last_name,
      prospect_lists.name AS prospect_list,
      p.company,
      p.city,
      p.country,
      p.custom_fields,

      es.id,
      ems.subject,
      ems.body,
      ems.text_body,
      ems.email_thread_id,
      ems.from_email,
      es.sent_at as received_at,
      es.step_name AS step_name,

      es.sent_at AS sent_at,
      es.opened_at AS opened_at,
      es.clicked_at AS clicked_at,
      es.bounced_at AS bounced_at,
      es.replied_at AS replied_at,
      es.auto_reply_at AS auto_reply_at,
      es.out_of_office_reply_at AS out_of_office_reply_at

      FROM emails_scheduled es
      Inner join email_message_data ems on ems.team_id = es.team_id and es.id = ems.es_id

      JOIN prospects p ON (p.id = es.prospect_id)
      INNER JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
      LEFT JOIN prospect_lists ON (
        prospect_lists.id = p.list_id
        AND
        prospect_lists.team_id = p.team_id
      )

      WHERE es.id IN ${SQLUtils.generateSQLValuesClause(emailScheduledIds)}
        AND es.team_id = $teamId
        AND es.prospect_id IS NOT NULL
        $onlyNewRepliesCheck
      ;
      """

        query
          .map(fromDbForWebhook)
          .list
          .apply()
      }
    }
  }

//  def findForWebhooksV2Old(
//                            emailScheduledIds: Seq[Long],
//                            onlyNewReplies: Boolean,
//                            teamId: TeamId
//                          ): Seq[EmailReceivedForWebhookV2] = DB readOnly { implicit session =>
//
//    if (emailScheduledIds.isEmpty) Seq()
//    else {
//
//      val onlyNewRepliesChesk = if (onlyNewReplies) sqls""" AND NOT es.scheduled_manually AND NOT es.scheduled_from_campaign """ else sqls""
//
//      val query =
//        sql"""
//        SELECT
//        es.campaign_name AS campaign_name,
//        es.campaign_id,
//
//        es.prospect_id,
//
//        es.id,
//        es.subject,
//        es.body,
//        es.text_body,
//        es.to_email,
//        es.from_email,
//        es.email_thread_id,
//        es.sent_at as sent_at,
//        es.opened_at as opened_at,
//        es.clicked_at as clicked_at,
//        es.replied_at as replied_at,
//        es.step_name AS step_name,
//        es.campaign_id as campaign_id
//
//        FROM emails_scheduled es
//        WHERE es.id IN ${SQLUtils.generateSQLValuesClause(emailScheduledIds)}
//          AND es.team_id = ${teamId.id}
//          AND es.prospect_id IS NOT NULL
//          $onlyNewRepliesChesk
//        ;
//        """
//
//      query
//        .map(fromDbForWebhookV2)
//        .list
//        .apply()
//    }
//  }

  def findForWebhooksV2New(
                            emailScheduledIds: Seq[Long],
                            onlyNewReplies: Boolean,
                            teamId: TeamId
                          ): Seq[EmailReceivedForWebhookV2] = DB readOnly { implicit session =>

    if (emailScheduledIds.isEmpty) Seq()
    else {

      val onlyNewRepliesChesk = if (onlyNewReplies) sqls""" AND NOT es.scheduled_manually AND NOT es.scheduled_from_campaign """ else sqls""

      val query = sql"""
      SELECT
      es.campaign_name AS campaign_name,
      es.campaign_id,

      es.prospect_id,

      es.id,
      ems.subject,
      ems.body,
      ems.text_body,
      ems.to_email,
      ems.from_email,
      ems.email_thread_id,
      es.sent_at as sent_at,
      es.opened_at as opened_at,
      es.clicked_at as clicked_at,
      es.replied_at as replied_at,
      es.step_name AS step_name,
      es.campaign_id as campaign_id

      FROM emails_scheduled es
      inner join email_message_data ems on ems.team_id = es.team_id and es.id = ems.es_id
      WHERE es.id IN ${SQLUtils.generateSQLValuesClause(emailScheduledIds)}
        AND es.team_id = ${teamId.id}
        AND es.prospect_id IS NOT NULL
        $onlyNewRepliesChesk
      ;
      """


      query
        .map(fromDbForWebhookV2)
        .list
        .apply()
    }
  }

//  def findNewReplyForZapierOld(permittedAccountIds: Seq[Long], teamId: Long)(using Logger: SRLogger): Try[Seq[EmailReceivedForWebhook]] = {
//    //DB readOnly { implicit session =>
//
//    val query =
//      sqls"""
//      SELECT
//      c.name AS campaign_name,
//
//      p.id as prospect_id,
//      pe.email,
//      p.first_name,
//      p.last_name,
//      prospect_lists.name AS prospect_list,
//      p.company,
//      p.city,
//      p.country,
//      p.custom_fields,
//
//      es.id,
//      es.subject,
//      es.body,
//      es.text_body,
//      es.email_thread_id,
//      es.from_email,
//
//      es.sent_at as received_at,
//
//      es.sent_at AS sent_at,
//      es.opened_at AS opened_at,
//      es.clicked_at AS clicked_at,
//      es.bounced_at AS bounced_at,
//      es.replied_at AS replied_at,
//      es.auto_reply_at AS auto_reply_at,
//      es.out_of_office_reply_at AS out_of_office_reply_at,
//
//      steps.label AS step_name
//
//      FROM emails_scheduled es
//
//      JOIN campaigns c ON (c.id = es.campaign_id)
//      JOIN campaign_steps steps ON (steps.id = es.step_id)
//      JOIN prospects p ON (p.id = es.prospect_id)
//      INNER JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
//      LEFT JOIN prospect_lists ON (
//        prospect_lists.id = p.list_id
//        AND
//        prospect_lists.team_id = p.team_id
//      )
//
//      WHERE p.account_id IN ($permittedAccountIds)
//        AND p.team_id = $teamId
//        AND es.replied
//        AND es.step_id IS NOT NULL
//        AND NOT es.scheduled_manually
//        AND NOT es.scheduled_from_campaign
//      ORDER BY es.id DESC
//      LIMIT 1
//        """
//
//    DBUtils.readOnlyWithExplain(
//      query = query,
//      logger = Logger.appendLogRequestId("findNewReplyForZapier")
//    ) { implicit session =>
//      sql""" $query """
//
//        .map(fromDbForWebhook)
//        .list
//        .apply()
//    }
//  }

  def findNewReplyForZapierNew(permittedAccountIds: Seq[Long], teamId: Long)(using Logger: SRLogger): Try[Seq[EmailReceivedForWebhook]] = {
    //DB readOnly { implicit session =>

    val query =
      sqls"""
    SELECT
    c.name AS campaign_name,

    p.id as prospect_id,
    pe.email,
    p.first_name,
    p.last_name,
    prospect_lists.name AS prospect_list,
    p.company,
    p.city,
    p.country,
    p.custom_fields,

    es.id,
    ems.subject,
    ems.body,
    ems.text_body,
    ems.email_thread_id,
    ems.from_email,

    es.sent_at as received_at,

    es.sent_at AS sent_at,
    es.opened_at AS opened_at,
    es.clicked_at AS clicked_at,
    es.bounced_at AS bounced_at,
    es.replied_at AS replied_at,
    es.auto_reply_at AS auto_reply_at,
    es.out_of_office_reply_at AS out_of_office_reply_at,

    steps.label AS step_name

    FROM emails_scheduled es
    inner join email_message_data ems on ems.team_id = es.team_id and es.id = ems.es_id

    JOIN campaigns c ON (c.id = es.campaign_id)
    JOIN campaign_steps steps ON (steps.id = es.step_id)
    JOIN prospects p ON (p.id = es.prospect_id)
    INNER JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
    LEFT JOIN prospect_lists ON (
      prospect_lists.id = p.list_id
      AND
      prospect_lists.team_id = p.team_id
    )

    WHERE p.account_id IN ($permittedAccountIds)
      AND p.team_id = $teamId
      AND es.replied
      AND es.step_id IS NOT NULL
      AND NOT es.scheduled_manually
      AND NOT es.scheduled_from_campaign
    ORDER BY es.id DESC
    LIMIT 1
      """


    DBUtils.readOnlyWithExplain(
      query = query,
      logger = Logger.appendLogRequestId("findNewReplyForZapier")
    ) { implicit session =>
      sql""" $query """

        .map(fromDbForWebhook)
        .list
        .apply()
    }
  }

//  def findThreadMessagesOld(
//                          threadId: Long,
//                          teamId: TeamId,
//                          allTrackingDomains: Seq[String],
//                          findThreadMessageFlow: FindThreadMessageFlow
//                        )(implicit  logger:SRLogger): Try[Seq[MessageObject.EmailMessageObject]] = Try {
//
//
//    DB readOnly { implicit session =>
//
//      val selectClause: SQLSyntax = EmailScheduledDAO.selectClauseEmailMessageObject
//
//      /**
//       * 17-May-2024: if isInboxReplyDraft flow then we want the latest previuos reply only and not the all replies
//       * because it was repeatedly appending same previous threads again and again
//       */
//      val orderByAndLimit: SQLSyntax = findThreadMessageFlow match {
//        case FindThreadMessageFlow.InboxReplyDraft => sqls" order by es.sent_at DESC LIMIT 1 "
//        case FindThreadMessageFlow.GetConversationEmails |
//             FindThreadMessageFlow.InboxForwardEmailDraft => sqls" order by es.sent_at ASC "
//      }
//
//      sql"""
//      $selectClause
//
//      where
//        es.email_thread_id = $threadId
//        AND es.team_id = ${teamId.id}
//
//       $orderByAndLimit;
//    """
//        .map(rs => EmailScheduledDAO.fromDBEmailMessageObject(rs = rs, allTrackingDomains = allTrackingDomains))
//        .list
//        .apply()
//    }
//  }

  def findThreadMessagesNew(
                             threadId: Long,
                             teamId: TeamId,
                             allTrackingDomains: Seq[String],
                             findThreadMessageFlow: FindThreadMessageFlow
                           )(implicit  logger:SRLogger): Try[Seq[MessageObject.EmailMessageObject]] = Try {


    DB readOnly { implicit session =>

      val selectClause: SQLSyntax = EmailScheduledDAO.selectClauseEmailMessageObjectNew

      /**
       * 17-May-2024: if isInboxReplyDraft flow then we want the latest previuos reply only and not the all replies
       * because it was repeatedly appending same previous threads again and again
       */
      val orderByAndLimit: SQLSyntax = findThreadMessageFlow match {
        case FindThreadMessageFlow.InboxReplyDraft => sqls" order by es.sent_at DESC LIMIT 1 "
        case FindThreadMessageFlow.GetConversationEmails |
             FindThreadMessageFlow.InboxForwardEmailDraft => sqls" order by es.sent_at ASC "
      }

      sql"""
      $selectClause

      where
        es.email_thread_id = $threadId
        AND es.team_id = ${teamId.id}

       $orderByAndLimit;
    """
        .map(rs => EmailScheduledDAO.fromDBEmailMessageObject(rs = rs, allTrackingDomains = allTrackingDomains))
        .list
        .apply()
    }
  }


  //usage not found for this function
  /*
  def getInboxCategoriesV2(accountIds: Seq[Long], teamId: Long, campaignId: Option[Long], account: Option[Account], is_v1: Boolean): Seq[InboxCategory] = DB readOnly { implicit session =>

    // FIXME: unread_count

    val campaignClause = if (campaignId.isDefined) sqls" AND et.campaign_id = ${campaignId.get} " else sqls""

    /*val fromDB = sql"""
    SELECT x.prospect_category_id_custom , x.prospect_category as name, count(x.prospect_id) as unread_count
    FROM (
      SELECT
        p.id as prospect_id,
        ROW_NUMBER() OVER (PARTITION BY p.prospect_category_id_custom) AS r,
        p.prospect_category,
        p.prospect_category_id,
        p.prospect_category_id_custom

      FROM email_threads et
      inner join prospects p on p.id = et.prospect_id
      where
        et.latest_reply_at is not null
        and et.account_id IN ($accountIds)
        and not et.sr_read
        and et.latest_email_id is not null
        and not et.archived
        AND et.team_id = $teamId
        $campaignClause
        and et.latest_reply_at > now() - interval '60 days'
      ) x
      WHERE x.r <= 10
      group by x.prospect_category_id_custom, x.prospect_category
    ;
    """*/

    val checkAfterDate = DateTime.now().minusDays(AppConfig.inboxUnreadCountShowForDays)


    val fromDB = sql"""
    SELECT x.prospect_category_id_custom, count(x.prospect_id) as unread_count
    FROM (
      SELECT distinct on (et.id)
        p.id as prospect_id,
        ROW_NUMBER() OVER (PARTITION BY p.prospect_category_id_custom) AS r,
        p.prospect_category_id_custom

      FROM email_threads et
      join email_threads_prospects etp on etp.email_thread_id = et.id
      inner join prospects p on p.id = etp.prospect_id
      where
        et.latest_reply_at is not null
        and et.account_id IN ($accountIds)
        and not et.sr_read
        and et.latest_email_id is not null
        and et.folder_type != ${FolderType.DONE.textId}
        AND et.team_id = $teamId
        $campaignClause
        and et.latest_reply_at > $checkAfterDate
      ) x
      WHERE x.r <= 10
      group by x.prospect_category_id_custom
    ;
    """

      .map(Inbox.fromDb)
      .list
      .apply()

    Inbox.getAllCategories(fromDB, teamId = teamId, account = account, is_v1 = is_v1)
  }
*/

  def findOneForFetchOutlookMsgId(): Option[InternetMsgIDForFetchOutlookMsgId] = DB readOnly { implicit session =>
    sql"""
      select ems.message_id as message_id,
         e.oauth2_refresh_token_enc as refresh_token,
         e.email as email,
         es.id as id,
         es.sender_email_settings_id as email_setting_id
         from emails_scheduled es
         INNER JOIN email_settings e on e.id = es.sender_email_settings_id
         --- change to inner join later
         left join email_message_data ems on ems.team_id = es.team_id and es.id = ems.es_id
         where ems.outlook_conversation_id is not null

           and e.status = ${EmailSettingStatus.Active.toString}

                 and ems.message_id is not null
                 and ems.outlook_msg_id is null
           and es.sent_at < now() - interval '3 minutes'
           and es.sent_at > now() - interval '1 days'
           and (e.paused_till is null or e.paused_till < now())
      limit 1;
    """
      .map { rs =>
        InternetMsgIDForFetchOutlookMsgId(
          email_scheduled_id = rs.long("id"),
          message_id = rs.string("message_id"),
          email = rs.string("email"),
          email_setting_id = rs.long("email_setting_id"),
          refresh_token = EncryptionHelpers.decryptEmailSettingCredential(rs.string("refresh_token"))
        )
      }
      .single
      .apply()
  }

  def findOneForZapierSample(

                              permittedAccountIds: Seq[Long],
                              teamId: Long,
                              eventType: EventType

                            ): Option[ZapierFindOneForEmail] = DB.readOnly { implicit session =>

    val whereClause: Option[SQLSyntax] = if (eventType == EventType.EMAIL_SENT) {

      // send only campaign-sent emails for now
      Some(sqls" AND (es.sent AND es.scheduled_from_campaign) ")

    } else if(eventType == EventType.EMAIL_OPENED) {

      Some(sqls" AND es.scheduled_from_campaign AND es.opened")

    }  else if(eventType == EventType.EMAIL_LINK_CLICKED) {

      Some(sqls" AND es.scheduled_from_campaign AND es.clicked")

    } else {

      logger.error(s"FATAL findOneForZapierSample Invalid eventType: ${eventType.toString}")

      None
    }

    if (whereClause.isEmpty) {

      logger.error(s"ZapierController FATAL get sample EmailScheduled.findOneForZapierSample unknown event type ${eventType.toString} :: ($permittedAccountIds) : $teamId :: returning None")

      None

    } else {
      sql"""
        SELECT
          es.id,
          es.prospect_id
        FROM emails_scheduled es
        WHERE es.account_id IN ($permittedAccountIds)
             AND es.team_id = $teamId
             AND es.prospect_id IS NOT NULL

             ${whereClause.get}

        ORDER BY es.id DESC
        LIMIT 1
      """
        .map(rs => {
          ZapierFindOneForEmail(
            email_scheduled_id = rs.long("id"),
            prospect_id = rs.long("prospect_id")
          )

        })
        .headOption
        .apply()

    }

  }


  def findAllForMQTriggerPublish(
                                  emailScheduledIds: Seq[Long],
                                  teamId: Long
                                ): Try[List[EmailScheduledForMQTriggerPublish]] = Try {

    if (emailScheduledIds.isEmpty) List() else {

      DB readOnly { implicit session =>

        sql"""
      select

        es.id AS email_scheduled_id,
        es.prospect_id,
        p.account_id AS prospect_owner_id

        from emails_scheduled es
        join prospects p on p.id = es.prospect_id

        where es.id IN ${SQLUtils.generateSQLValuesClause(emailScheduledIds)}
        AND es.team_id = $teamId
        ;
    """
          .map(rs => {

            EmailScheduledForMQTriggerPublish(
              email_scheduled_id = rs.long("email_scheduled_id"),
              prospect_id = rs.long("prospect_id"),
              prospect_owner_id = rs.long("prospect_owner_id")
            )
          })
          .list
          .apply()

      }
    }
  }



  def emailScheduleProspectStatusUpdateByAdmin(adminAccountId: Long,
                                               teamId: Long,
                                               emailsToBeUpdated: Seq[CPRepliedEvent],
                                               newStatus: NewProspectStatus,
                                               Logger: SRLogger
                                              ): Try[Seq[CPTuple]] = Try {
    DB autoCommit { implicit session =>

      if (emailsToBeUpdated.isEmpty) {

        Logger.fatal("emailScheduleProspectStatusUpdateByAdmin db call: empty emailsToBeUpdated")

        Seq()

      } else {

        var isReplied = false
        val updateColumns = newStatus match {

          case NewProspectStatus.REPLIED =>

            isReplied = true

            sqls"""
                  replied = es.replied or temp.replied,
                  replied_at = COALESCE(es.replied_at, temp.replied_at::timestamptz),
                  replied_marked_by_adminid = temp.replied_marked_by_adminid
              """

          case NewProspectStatus.PAUSE |
               NewProspectStatus.RESUME_LATER |
               NewProspectStatus.UNPAUSE =>

            throw new Exception(s"Invalid prospect status for update: ${newStatus.toString}")


        }


        var valueParameters = List[Any]()

        val valuePlaceholder: SQLSyntax = emailsToBeUpdated
          .map(e => {


            valueParameters = valueParameters ::: List(

              isReplied,
              if (isReplied) e.replied_at else null,
              adminAccountId,

              e.prospectId,
              e.campaignId,
              teamId
            )

            sqls"""
              (
                ?,
                ?,
                ?,

                ?,
                ?,
                ?
              )

            """

          })
          .reduce((vp1, vp2) => sqls"$vp1, $vp2")

        sql"""
          UPDATE emails_scheduled es
          SET

           $updateColumns

          FROM (VALUES $valuePlaceholder)
          AS temp(
            replied,
            replied_at,
            replied_marked_by_adminid,

            prospect_id,
            campaign_id,
            team_id

          )
          WHERE es.campaign_id = temp.campaign_id
            AND es.prospect_id = temp.prospect_id
            AND es.sent
            AND es.team_id = temp.team_id

          RETURNING es.campaign_id, es.prospect_id
        """
          .bind(valueParameters*)
          .map(rs => {

            CPTuple(
              prospect_id = rs.long("prospect_id"),
              campaign_id = rs.long("campaign_id")
            )

          })
          .list
          .apply()

      }
    }
  }


  def fetchProspectIdByEmailScheduledIdForUpdateLeadStatus(
                                                            emailScheduledIds: Seq[Long],
                                                            onlyNewReplies: Boolean
                                                          ): Seq[EmailReceivedForUpdateLeadStatus] =

    DB readOnly { implicit session =>

      if (emailScheduledIds.isEmpty) Seq()
      else {

        val onlyNewRepliesCheck = if (onlyNewReplies)
          sqls""" AND NOT es.scheduled_manually AND NOT es.scheduled_from_campaign """
        else sqls""

        sql"""
            SELECT
            es.id,
            es.prospect_id
            FROM emails_scheduled es

            WHERE es.id IN ${SQLUtils.generateSQLValuesClause(emailScheduledIds)}
              AND es.prospect_id IS NOT NULL
          $onlyNewRepliesCheck
          ;
        """
          .map(fromDbForUpdateLeadStatus)
          .list
          .apply()
      }
    }

  def getSentEmailCountForACampaignForLast24Hours(
                                                   campaign_id: Long,
                                                   team_id: Long
                                                 ): Try[List[Boolean]] = Try{
    DB readOnly { implicit session =>
      sql"""
           SELECT bounced
           FROM emails_scheduled
           WHERE campaign_id = $campaign_id
           AND sent_at >= NOW() - INTERVAL '24 HOURS'
           AND sent
           AND scheduled_from_campaign
           AND team_id = $team_id;

         """
        .map(rs => rs.boolean("bounced"))
        .list
        .apply()
    }

  }

  def updateProspectIdInEmailsScheduled(
                                         prospects: Seq[ProspectEmailMessage],
                                         team_id: Long
                                       )(implicit session: DBSession): Try[List[AssociateAndUpdateCampaignProspectData]] = Try {
    if(prospects.isEmpty){
      List()

    } else {

      var parameters = List[Any]()

      val valuePlaceholder: SQLSyntax = prospects.map(p => {

          parameters = parameters ::: List(
            p.prospect_id,
            p.email_message_id,
            team_id
          )


          sqls"""
          (
            ?,
            ?,
            ?
          )
         """
        })
        .reduce((vp1, vp2) => sqls" $vp1, $vp2 ")

      sql"""
         UPDATE emails_scheduled es

         SET
            prospect_id = temp.prospect_id

         FROM (VALUES $valuePlaceholder)
          AS temp(
               prospect_id,
               email_message_id,
               team_id
          )

         WHERE es.id = temp.email_message_id
         AND es.team_id = temp.team_id

         RETURNING es.id, es.campaign_id, es.prospect_id;
       """
        .bind(parameters*)
        .map(rs => AssociateAndUpdateCampaignProspectData(
          es_id = rs.long("id"),
          campaign_id  = rs.longOpt("campaign_id").map(CampaignId(_)),
          prospect_id = rs.longOpt("prospect_id").map(ProspectId(_))
        ))
        .list
        .apply()
    }
  }

  def getSentAtFromSecondaryProspectEmail(
                                           prospect_id: Long,
                                           prospect_email: String,
                                           team_id: Long
                                         )(implicit session: DBSession): Try[MessageSentAtByProspect.EmailSentAtByProspect] = Try {

    val contact_type = MessageContactType.From.toString
    val min_date_to_find_sent_at_by_secondary_prospect_email = DateTime.now().minusMonths(1)

    sql"""
          SELECT es.id as email_scheduled_id,  m.max_sent_at, es.prospect_id
          FROM emails_scheduled es

          INNER JOIN (SELECT MAX (es.sent_at) max_sent_at

         FROM emails_scheduled es
         INNER JOIN email_message_contacts emc on es.id = emc.email_message_id and emc.team_id = es.team_id

            WHERE emc.prospect_id = $prospect_id
            AND emc.email = $prospect_email
            AND emc.contact_type = $contact_type

            AND es.team_id = $team_id
            AND NOT es.scheduled_from_campaign
            AND es.sent_at > $min_date_to_find_sent_at_by_secondary_prospect_email

         ) m on m.max_sent_at = es.sent_at

         INNER JOIN email_message_contacts emc on es.id = emc.email_message_id and emc.team_id = es.team_id

            WHERE emc.prospect_id = $prospect_id
            AND emc.email = $prospect_email
            AND emc.contact_type = $contact_type

            AND es.team_id = $team_id
            AND NOT es.scheduled_from_campaign
            AND es.sent_at > $min_date_to_find_sent_at_by_secondary_prospect_email;
      """
      .map(rs =>
        MessageSentAtByProspect.EmailSentAtByProspect(
          sent_at = rs.jodaDateTime("max_sent_at"),
          email_scheduled_id = rs.long("email_scheduled_id"),
          prospect_id = ProspectId(rs.long("prospect_id")),
        )
      )
      .single
      .apply() match {
      case None => throw new Throwable("Email sent_at by secondary prospect email not found")
      case Some(result) => result
    }
  }


  def getEmailScheduledIdForManualEmailSending(
                                                prospect_ids: Seq[ProspectId],
                                                team_id: TeamId,
                                                campaign_id: CampaignId,
                                                step_id: StepId
                                              ): Try[Option[Long]] = Try{

    if(prospect_ids.isEmpty){
      None
    } else {

      DB.readOnly { implicit session => {

        sql"""

           SELECT
              es.id
           FROM emails_scheduled es
           WHERE es.prospect_id in (${prospect_ids.map(_.id)})
           AND es.team_id = ${team_id.id}
           AND es.campaign_id = ${campaign_id.id}
           AND es.step_id = ${step_id.id}
           AND es.is_manual_task
           AND not es.sent
           LIMIT 1
         """
          .map(rs => rs.long("id"))
          .single
          .apply()

      }}

    }


  }

  def getInboxPlacementCheckSentEmailScheduledDetails(
                                                       teamId: TeamId,
                                                       senderEmailSettingId: EmailSettingId
                                                     ): Try[InboxPlacementCheckSentEmailScheduledDetails] = Try {

    DB.readOnly { implicit session =>

      sql"""

          Select es.id, es.sent_at, ems.message_id
          from emails_scheduled es
          inner join email_message_data ems on ems.team_id = es.team_id and es.id = ems.es_id
          where es.sending_flow = ${EmailSendingFlow.InboxPlacementOut.toString}
          and es.team_id = ${teamId.id}
          and es.inbox_email_setting_id = ${senderEmailSettingId.emailSettingId}
          and es.sent_at > now() - interval '60 minutes'  ---reading it as soon as test email is sent
          LIMIT 1
          ;
          """
        .map(rs => InboxPlacementCheckSentEmailScheduledDetails(
          emailScheduledId = rs.long("id"),
          sent_at = rs.jodaDateTime("sent_at"),
          message_id = rs.string("message_id")
        ))
        .single
        .apply()
        .get

    }

  }
  def getEmailDataForProspectRevert(
                                     campaign_id: CampaignId,
                                     prospect_id: ProspectId,
                                     step_id: StepId
                                   )(using Logger: SRLogger, session: DBSession): Try[Option[RevertData.EmailScheduledRevertData]] = Try {
    sql"""
         SELECT
            id,
            scheduled_at,
            sent_at
          FROM emails_scheduled
             where campaign_id = ${campaign_id.id}
          AND prospect_id = ${prospect_id.id}
          AND step_id = ${step_id.id}
          AND scheduled_from_campaign
          ORDER BY scheduled_at DESC
          LIMIT 1
        """
      .map(rs => {

        RevertData.EmailScheduledRevertData(
          last_scheduled_at = rs.jodaDateTime("scheduled_at"),
          emails_scheduled_id = rs.long("id"),
          sent_at = rs.jodaDateTimeOpt("sent_at")
        )

      })
      .single
      .apply()

  }

  def findValidLatestEmailEntry(
                                 campaign_id: CampaignId,
                                 prospect_id: ProspectId,
                                 team_id: TeamId
                               )(using logger: SRLogger, session: DBSession): Try[Option[NewProspectAssociateWithCampaignData]] = Try {

    sql"""
         SELECT
            id,
            step_id,
            sent_at,
            opened_at,
            is_manual_task,
            scheduled_at,
            prospect_id,
            campaign_id

            FROM emails_scheduled
         WHERE

            campaign_id = ${campaign_id.id}
            and prospect_id = ${prospect_id.id}
            and team_id = ${team_id.id}
            and scheduled_from_campaign
            and step_id is not null

         ORDER BY scheduled_at DESC
         LIMIT 1

       """
      .map(rs => {
        NewProspectAssociateWithCampaignData(
          es_id = rs.long("id"),
          step_id = StepId(id = rs.long("step_id")),
          prospect_id = ProspectId(id = rs.long("prospect_id")),
          campaign_id = CampaignId(id = rs.long("campaign_id")),
          sent_at = rs.jodaDateTimeOpt("sent_at"),
          opened_at = rs.jodaDateTimeOpt("opened_at"),
          scheduled_at = rs.jodaDateTime("scheduled_at"),
          is_manual_task = rs.boolean("is_manual_task")
        )
      })
      .single
      .apply()

  }


  def markStepIdAsNull(
                        campaign_id: CampaignId,
                        prospectId: ProspectId,
                        team_id: TeamId,
                        emails_scheduled : Seq[Long]
                      )(implicit session: DBSession): Try[List[Long]] = Try {

    if(emails_scheduled.isEmpty){

      List()


    } else {

      sql"""
            UPDATE emails_scheduled
              SET  step_id = null
            WHERE
              campaign_id = ${campaign_id.id}
            AND
              prospect_id = ${prospectId.id}
            AND
              team_id = ${team_id.id}
            AND
              id in (${emails_scheduled})
            RETURNING id
         """
        .map(rs => rs.long("id"))
        .list
        .apply()

    }
  }

  def getInboxPlacementCheckReceivedEmailData(
                                               inboxPlacementCheckTrackedEmails: InboxPlacementCheckTrackedEmails
                                             ): Try[Option[EmailHeaderForInboxPlacementCheckAnalysis]] = Try {

    DB readOnly { implicit session =>

      sql"""
           SELECT es.id, ems.full_headers

           FROM emails_scheduled es
           inner join email_message_data ems on ems.team_id = es.team_id and es.id = ems.es_id

           WHERE es.inbox_email_setting_id = ${inboxPlacementCheckTrackedEmails.receiverEmailSettingId.emailSettingId}
           and es.team_id  = ${inboxPlacementCheckTrackedEmails.receiverTeamId.id}
           and ems.message_id  = ${inboxPlacementCheckTrackedEmails.emailMessageId}
           ;
         """
        .map(rs => {
          EmailHeaderForInboxPlacementCheckAnalysis(
            emailScheduledId = rs.long("id"),
            emailHeaders = Json.parse(rs.any("full_headers").asInstanceOf[PGobject].getValue)
          )
        })
        .single
        .apply()

    }
  }

  def updateProspectIdForMergeDuplicates(
                                          duplicateProspects: List[ProspectIdAndPotentialDuplicateProspectId],
                                          masterProspectId: ProspectId,
                                          teamId: TeamId
                                        )(implicit session: DBSession): Try[List[EmailScheduledId]] = Try {
    var valueParameters = List[Any]()

    val valuePlaceholder: SQLSyntax = duplicateProspects.map(p => {

        valueParameters = valueParameters ::: List(
          p.prospectId.id,
          p.potentialDuplicateProspectId.id,

          masterProspectId.id,
          teamId.id
        )

        sqls"""
           (
              ?,
              ?,

              ?,
              ?
            )
          """

      })
      .reduce((vp1, vp2) => sqls"$vp1, $vp2")

    sql"""
       UPDATE emails_scheduled es
        SET
          prospect_id = temp.master_prospect_id,
          potential_duplicate_prospect_id = temp.potential_duplicate_prospect_id

          FROM (
              VALUES $valuePlaceholder
            )
            AS temp(
              prospect_id,
              potential_duplicate_prospect_id,

              master_prospect_id,
              team_id
            )
      WHERE
        es.team_id = temp.team_id
        AND es.prospect_id = temp.prospect_id
        RETURNING es.id
        ;
       """
      .bind(valueParameters*)
      .map(rs => EmailScheduledId(rs.long("id")))
      .list
      .apply()
  }

  def getNotSentEmailScheduledIds(): Try[List[Long]] = Try {

    /**
     * we want the index to get picked up using pushed_to_rabbit_mq
     * so we have split the query into pushed_to_rabbitmq true OR false to give a hint to the db query optimiser
     */
    DB readOnly { implicit session =>

      sql"""
          (SELECT esc.id FROM emails_scheduled esc
          WHERE
           esc.pushed_to_rabbitmq
          AND not esc.sent
          AND esc.scheduled_from_campaign
          AND esc.scheduled_at > now() - interval '1 hours'
          AND esc.scheduled_at < now() - interval '10 minutes'
          LIMIT 250)

          UNION ALL

          SELECT esc.id FROM emails_scheduled esc
          WHERE
          NOT esc.pushed_to_rabbitmq
          AND not esc.sent
          AND esc.scheduled_from_campaign
          AND esc.scheduled_at > now() - interval '1 hours'
          AND esc.scheduled_at < now() - interval '10 minutes'
          LIMIT 250
          ;
         """
        .map(_.long("id"))
        .list
        .apply()

    }
  }
}

object EmailScheduledDAO {
  //    def selectEmailScheduledToDeleteExceptManualQuery (
  //      emailSettingIds: Seq[EmailSettingId],
  //      teamId: TeamId
  //    )
  //    (
  //      implicit Logger: SRLogger,
  //      session: DBSession
  //    ): SQL[Nothing, NoExtractor] = {
  //        sql"""
  //              SELECT
  //                es.id,
  //                es.scheduled_manually,
  //                es.inbox_email_setting_id
  //              FROM emails_scheduled es
  //              WHERE inbox_email_setting_id in (${emailSettingIds.map (_.emailSettingId)})
  //                AND team_id = ${teamId.id}
  //                AND sent = false
  //                AND is_manual_task = false
  //                AND scheduled_from_campaign
  //           """
  //  }


  def selectClauseEmailMessageObject: SQLSyntax = {
    sqls"""
      select
      es.uuid,
      es.from_email,
      es.from_name,

      es.to_email AS to_emails,
      es.reply_to_email,

      es.cc_emails,
      es.bcc_emails,

      es.subject,
      es.base_body,
      (case when es.text_body is not null and trim(es.text_body) != '' then left(es.text_body, 200) else es.body end) as body_preview,

      (case when es.scheduled_from_campaign or es.scheduled_manually then true else false end) as sent_by_user,


      es.sent_at

      from emails_scheduled es
        """
  }
  def selectClauseEmailMessageObjectNew: SQLSyntax = {
    sqls"""
       select
        es.uuid,
        ems.from_email,
        ems.from_name,

        ems.to_email AS to_emails,
        ems.reply_to_email,

        ems.cc_emails,
        ems.bcc_emails,

        ems.subject,
        ems.base_body,
        (case when ems.text_body is not null and trim(ems.text_body) != '' then left(ems.text_body, 200) else ems.body end) as body_preview,

        (case when es.scheduled_from_campaign or es.scheduled_manually then true else false end) as sent_by_user,


        es.sent_at

      from emails_scheduled es
      inner join email_message_data ems on ems.team_id = es.team_id and es.id = ems.es_id
          """

  }
  def fromDBEmailMessageObject(
                                rs: WrappedResultSet,
                                allTrackingDomains: Seq[String]
                              )(using Logger: SRLogger): EmailMessageObject = {

    // NOTE: disable tracking linksbefore pushing these to frontend
    val body = EmailHelper.disableTrackingLinks(
      body = rs.stringOpt("base_body").getOrElse(""),
      allTrackingDomains = allTrackingDomains,
      isTextBody = false
    )

    // NOTE: disable tracking linksbefore pushing these to frontend
    val bodyPreview = EmailHelper.disableTrackingLinks(
      body = rs.stringOpt("body_preview").getOrElse(""),
      allTrackingDomains = allTrackingDomains,
      isTextBody = true
    )

    val fromEmail = rs.string("from_email")

    MessageObject.EmailMessageObject(

      uuid = rs.stringOpt("uuid"),

      subject = rs.string("subject"),

      body = body,
      body_preview = bodyPreview,
      sent_at = rs.jodaDateTime("sent_at"),

      from_user = rs.boolean("sent_by_user"),

      from = IEmailAddress.parseWithVeryLooseCheck(email = fromEmail),
      to = IEmailAddress.parse(emailsStr = rs.string("to_emails")).get,

      reply_to = rs.stringOpt("reply_to_email")
        .flatMap(em => IEmailAddress.parse(emailsStr = em).get.headOption),

      cc_emails = rs.stringOpt("cc_emails")
        .map(em => IEmailAddress.parse(emailsStr = em).get),

      bcc_emails = rs.stringOpt("bcc_emails")
        .map(em => IEmailAddress.parse(emailsStr = em).get)

    )
  }


  def selectEmailScheduledIdAndTaskIdToPublishForDelete(
                                                         deleteEmailsScheduledType: DeleteEmailsScheduledType,
                                                         task_deletion_required: Boolean
                                                       ): SQL[Nothing, NoExtractor] = {

    val (email_where_clause, task_where_clause) = deleteEmailsScheduledType match {

      case data: DeleteEmailsScheduledType.DeleteAllUnSentByEmailSettingId =>

        (data.emailSettingData.map { case (teamId, emailSettingIds) =>
          sqls"""
            es.inbox_email_setting_id in (${
            emailSettingIds.map(_.emailSettingId)
          })
            AND es.team_id = ${
            teamId.id
          }
          """
        },

          sqls"""""")

      case data: DeleteEmailsScheduledType.DeleteUnSentByEmailSettingIdNotManual =>

        (data.emailSettingData.map { case (teamId, emailSettingIds) =>

          sqls"""
            es.inbox_email_setting_id in (${
            emailSettingIds.map(_.emailSettingId)
          })
            AND es.team_id = ${
            teamId.id
          }
          AND es.is_manual_task = false
          """
        },sqls"""""")


      case data: DeleteEmailsScheduledType.DeleteUnsentByEmailScheduledId =>

        (sqls"""
          es.id = ${data.emailScheduledId}
          AND es.team_id = ${
          data.teamId.id
        }
        """,
          sqls"""

              """)

      case data: DeleteEmailsScheduledType.DeleteUnsentByProspectId => {
        val (e_whereClause, t_whereClause) = data.replyHandling match {

          case ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY =>


            (
              sqls"""
                  es.campaign_id = ${data.campaignId} AND es.prospect_id IN ${SQLUtils.generateSQLValuesClause(data.prospectIds)}
                  """,

              sqls"""
                    where t.campaign_id = ${data.campaignId} AND t.prospect_id IN ${SQLUtils.generateSQLValuesClause(data.prospectIds)}
                  """
            )


          case ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY =>
            if (
              data.prospectAccountIds.isDefined &&
                data.prospectAccountIds.get.nonEmpty
            ) {

              (
                sqls"""
                    ( es.prospect_account_id IN ${SQLUtils.generateSQLValuesClause(data.prospectAccountIds.get)} OR es.prospect_id IN ${SQLUtils.generateSQLValuesClause(data.prospectIds)} )
                    """,

                sqls"""

                     join prospects p on ( p.id = t.prospect_id and t.team_id = p.team_id )
                     where ( p.prospect_account_id IN ${SQLUtils.generateSQLValuesClause(data.prospectAccountIds.get)} OR t.prospect_id IN ${SQLUtils.generateSQLValuesClause(data.prospectIds)} )
                    """
              )

            } else {

              (
                sqls"""
                      es.prospect_id in ${SQLUtils.generateSQLValuesClause(data.prospectIds)}
                    """,
                sqls"""
                      where t.prospect_id IN ${SQLUtils.generateSQLValuesClause(data.prospectIds)}
                    """
              )

            }

          case ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY =>
            (sqls"""
                   es.prospect_id IN ${SQLUtils.generateSQLValuesClause(data.prospectIds)}

                """,

              sqls"""

                     where t.prospect_id IN ${SQLUtils.generateSQLValuesClause(data.prospectIds)}

                  """)

        }

        (
          sqls"""
                ${e_whereClause}
                AND es.team_id = ${data.teamId.id}
              """
          ,
          sqls"""
                ${t_whereClause}
                AND t.team_id = ${data.teamId.id}
            """
        )
      }
      case data: DeleteEmailsScheduledType.DeleteUnsentByCampaignId =>
        /*
          Note: is Manual was not present previously need to check if we need to add is_manual = false below
        * */

        (
          sqls"""
                es.campaign_id = ${data.campaignId}
                AND es.team_id = ${data.teamId.id}
              """,

          sqls"""
                where
                t.campaign_id = ${data.campaignId}
                AND t.team_id = ${data.teamId.id}
              """
        )

    }

    val email_selection_query =  if (task_deletion_required) {

      sql"""
       WITH selected_emails AS (
          SELECT
              es.id AS es_id,
              es.step_id AS es_step_id,
              es.campaign_id AS es_campaign_id,
              es.sent AS es_sent,
              es.prospect_id as es_prospect_id,
              es.inbox_email_setting_id as es_inbox_setting_id
          FROM
              emails_scheduled es
          WHERE
          ${email_where_clause}
          AND es.sent = false
          AND es.scheduled_from_campaign
      ),
      selected_tasks AS (
          SELECT
              t.task_id AS t_task_id,
              t.step_id AS t_step_id,
              t.campaign_id AS t_campaign_id,
              t.status AS t_status,
              t.prospect_id  as t_prospect_id
          FROM
              tasks t
          ${task_where_clause}
          AND t.status = ${TaskStatusType.Due.toKey}
          AND t.created_via = ${TaskCreatedVia.Scheduler.toKey}
      )


      SELECT
          se.es_id as es_scheduled_id,
          se.es_step_id as es_step_id,
          se.es_campaign_id as es_campaign_id,
          se.es_inbox_setting_id as es_inbox_setting_id,
          se.es_prospect_id as es_prospect_id,
          st.t_task_id as t_task_id,
          st.t_step_id as t_step_id,
          st.t_campaign_id as t_campaign_id,
          st.t_status as t_status,
          st.t_prospect_id as t_prospect_id
      FROM
          selected_emails se
      FULL JOIN
          selected_tasks st
      ON
          st.t_step_id = se.es_step_id
          AND st.t_campaign_id = se.es_campaign_id
          AND st.t_prospect_id = se.es_prospect_id
         """
    } else {

      sql"""
          SELECT
            es.id as es_scheduled_id,
            es.step_id as es_step_id,
            es.campaign_id as es_campaign_id,
            es.inbox_email_setting_id as es_inbox_setting_id,
            es.prospect_id as es_prospect_id
          FROM emails_scheduled es
          WHERE
          ${email_where_clause}
          AND es.sent = false
          AND es.scheduled_from_campaign
          """
    }

    email_selection_query
  }
}