package api.emails

import org.apache.pekko.actor.ActorSystem
import api.APIErrorResponse.ErrorResponseV3
import api.accounts.dao.TeamsDAO
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.{AccountId, OrgId}
import api.accounts.models.AccountId
import api.accounts.{AccountService, PermType, PermissionMethods, PermissionRequest, PermissionUtils, RolePermissionDataDAOV2, TeamId}
import api.campaigns.models.AccountData
import api.campaigns.services.CampaignService
import api.{ApiResponseModuleForPermissionedApis, ApiVersion, AppConfig, CONSTANTS, ErrorType}
import api.tracking_host.services.{CreateCustomTrackingDomainError, CustomTrackingDomainService}
import api.emails.services.{BulkDeleteRequest, CreateEmailError, EmailAccountService, EmailAccountTestGmailASPError, EmailAccountTestService, EmailAccountTestSettingError, EmailSettingError, EmailSettingService, MoveEmailFromGmailApiToGmailASPError, UpdateBasicDetailsError, UpdateEmailSettingsError}
import api.prospects.{CsvQueue, CsvQueueCreateFormDataV2, InferredQueryTimeline}
import api.team.service.TeamService
import io.lemonlabs.uri.Url
import io.smartreach.esp.api.emails.EmailSettingId
import play.api.libs.json.{JsError, JsObject, JsSuccess, JsValue, Json}
import play.api.libs.ws.WSClient
import play.api.mvc.{Action, AnyContent, BaseController, ControllerComponents, Result}
import utils.email.*
import utils.email_notification.service.EmailNotificationService
import utils.{Helpers, SRLogger, StringUtilsV2}
import utils.emailvalidation.EmailValidationService
import utils.helpers.LogHelpers
import utils.mq.emailaccount_deleter.{MqEmailAccountDeleter, MqEmailAccountDeleterMsg}
import utils.uuid.services.SrUuidService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

case class EmailSettingAPIParams(
                                  range: InferredQueryTimeline.Range,
                                  is_first: Boolean
                                 )

class EmailSettingController(
  protected val controllerComponents: ControllerComponents,
  override protected val rolePermissionDataDAOV2: RolePermissionDataDAOV2,
  emailNotificationService: EmailNotificationService,
  emailAccountTestService: EmailAccountTestService,
  emailAccountService: EmailAccountService,
  customTrackingDomainService: CustomTrackingDomainService,
  campaignService: CampaignService,
  mqEmailAccountDeleter: MqEmailAccountDeleter,
  override protected val emailSettingDAO: EmailSettingDAO,
  permissionUtils: PermissionUtils,
  emailSettingService : EmailSettingService,
  implicit val wsClient: WSClient,
  implicit val system: ActorSystem,
//  accountService: AccountService,
  emailSettingPaginationService: EmailSettingPaginationService,
  override protected val srUuidService: SrUuidService
) extends BaseController with SettingUtils {

  private implicit val ec: ExecutionContext = controllerComponents.executionContext

  def findAll(v: ApiVersion, aid: Option[Long], tid: Option[Long], campaign_id: Option[Long]): Action[AnyContent] = permissionUtils.checkPermission(
    version = v.textId,
    permission = PermType.VIEW_CHANNELS,
    tidOpt = tid,
    apiAccess = true
  ) { (request: PermissionRequest[AnyContent]) =>
    given Logger: SRLogger = request.Logger
    val Res = request.Response
    val acting_team_id = request.actingTeamAccount.get.team_id
    val teamId = TeamId(acting_team_id);
    val loggedInAccount = request.loggedinAccount
    val actingAccountId = request.actingTeamAccount.get.user_id
    val isApiCall = request.isApiCall
    val params = request.uri
    implicit val apiVersion: ApiVersion = v
    // while creating / updating campaign emails, show only current user's emails

    if (campaign_id.isDefined && !isApiCall) {
      emailSettingService.getPermittedAccountIdsWhenCampaignDefined(
        actingTeamId = acting_team_id,
        loggedInAccount = loggedInAccount,
        actingAccountId = actingAccountId,
        version = v.textId,
        Logger = Logger,
        campaignId = campaign_id.get
      ) match {
        case Failure(error) =>
          Res.ServerError("Some error occurred", e = Some(error))
        case Success(permittedAccountIds) =>
          val res = emailSettingPaginationService.validateAndExtractEmailSettingResult(
            accountIds = permittedAccountIds.map(pi => AccountId(pi)),
            isApiCall = isApiCall,
            teamId = teamId,
            params = params,
          ) match {
            case Left(emailSettingError) =>
              emailSettingError match {
                case EmailSettingError.ServerError(err) =>
                  Res.ServerError(
                    message = "Server error, please contact support.",
                    e = None
                  )
                case EmailSettingError.BadRequestError(err) =>
                  Res.BadRequestError(
                    message = err,
                    version = apiVersion
                  )
              }
            case Right(emailSettingResponse) =>
              Res.Success(
                message = emailSettingResponse.message,
                data = emailSettingResponse.data,
                apiVersion = emailSettingResponse.apiVersion
              )
          }
          if (request.isSupportAppModeRequest) {

            res

          } else {

            res
              /*
             13 Jun 2023 - added for audit SECAUDIT
             we are just extending the cookie expiry time, using this frequently called api
             */
              .withSession("account_id" -> request.loggedinAccount.internal_id.toString)

          }

      }
    } else if (campaign_id.isDefined && isApiCall) {
      Res.ForbiddenError("You are not authorized to do this. Please contact support.")
    }
    else {
      val res = emailSettingPaginationService.validateAndExtractEmailSettingResult(
        accountIds = request.permittedAccountIds.map(aid => AccountId(aid)),
        isApiCall = isApiCall,
        teamId = teamId,
        params = params,
      ) match {
        case Left(emailSettingError) =>
          emailSettingError match {
            case EmailSettingError.ServerError(err) =>
              Res.ServerError(
                message = "Server error, please contact support.",
                e = None
              )
            case EmailSettingError.BadRequestError(err) =>
              Res.BadRequestError(
                message = err,
                version = apiVersion
              )
          }
        case Right(emailSettingResponse) =>
          Res.Success(
            message = emailSettingResponse.message,
            data = emailSettingResponse.data,
            apiVersion = emailSettingResponse.apiVersion
          )
      }
      if (request.isSupportAppModeRequest) {

        res

      } else {

        res
          /*
         13 Jun 2023 - added for audit SECAUDIT
         we are just extending the cookie expiry time, using this frequently called api
         */
          .withSession("account_id" -> request.loggedinAccount.internal_id.toString)

      }
    }
  }

  def find(v: ApiVersion, id: String, aid: Option[Long], tid: Option[Long]): Action[AnyContent] = (permissionUtils.checkPermission(
    version = v.textId,
    permission = PermType.VIEW_CHANNELS,
    tidOpt = tid,
    apiAccess = true
  ) andThen hasEmailSettingWithUuid(v, id)) { (request: EmailAccountRequest[AnyContent]) =>
    val Logger = request.Logger
    val Res = request.Response

    val emailSettings = emailSettingService.getApiResponseV3(
      emailSettings = Seq(request.emailSetting)
    )
    emailSettings.headOption match {

      case None =>
        Logger.fatal(s"Email setting not found for tid_${request.actingTeamAccount.team_id} emailSettingUuId:: ${id}")
        Res.NotFoundError(
          message = "Email setting not found."
        )
      case Some(headEmailSettings) =>
        Res.Success(
          message = "Email Settings found",
          data = Json.toJson(headEmailSettings),
          apiVersion = ApiVersion.V3.textId
        )
    }
  }

  // DKIM is specfic to the domain, should ignore if DKIM record already exists for the same domain
  def createDKIM(v: String, id: Int, aid: Option[Long], tid: Option[Long]): Action[AnyContent] = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CHANNELS,
    tidOpt = tid
  ) andThen hasEmailSetting(id)) { (request: EmailAccountRequest[AnyContent]) =>
    given Logger: SRLogger = request.Logger
    val Res = request.Response

    val ta = request.actingTeamAccount

    val domain = EmailValidationService.getLowercasedNameAndDomainFromEmail(request.emailSetting.email)._2

    val serviceProvider = request.emailSetting.service_provider

    if (
      serviceProvider == EmailServiceProvider.NAMECHEAP ||
        serviceProvider == EmailServiceProvider.REDIFF_MAIL ||
        serviceProvider == EmailServiceProvider.OTHER ||
        serviceProvider == EmailServiceProvider.EXCHANGE
    ) {

      emailSettingDAO.findDKIMRecord(domain = domain) match {

        case Failure(e) =>
          Logger.error(s"[FATAL] EmailSettingController.createDKIM.findDKIMRecord :: Domain: $domain :: account (${ta.user_id}) :: email: ${request.emailSetting.email} :: ${LogHelpers.getStackTraceAsString(e)}")

          Res.ServerError("There was an error, please try again or contact support", e = Some(e))

        case Success(Some(dkim)) =>
          Logger.error(s"[FATAL] Record Already exists EmailSettingController.createDKIM.findDKIMRecord :: Domain: $domain :: account (${ta.user_id}) :: email: ${request.emailSetting.email}")

          Res.Success("DKIM record created successfully!!", Json.obj("dkim" -> dkim))


        case Success(None) =>

          val DKIMRecordData = Helpers.generateDKIMRecord(domain = domain)

          emailSettingDAO.saveDKIMRecord(dkimRecord = DKIMRecordData, account_id = ta.user_id, account_name = ta.first_name.getOrElse(""), account_email = ta.email) match {

            case Failure(e) =>
              Logger.error(s"[FATAL] EmailSettingController.saveDKIMRecord :: Domain: $domain :: account (${ta.user_id}) :: email: ${request.emailSetting.email} :: ${LogHelpers.getStackTraceAsString(e)}")

              Res.ServerError("Error while saving DKIM Record", e = Some(e))

            case Success(None) =>

              Logger.error(s"[FATAL] EmailSettingController.saveDKIMRecord :: Domain: $domain :: account (${ta.user_id}) :: email: ${request.emailSetting.email} :: case None")

              Res.ServerError("Error while saving DKIM Record", e = None)

            case Success(Some(dkim)) =>
              Logger.info(s"Success EmailSettingController.saveDKIMRecord :: Domain: $domain :: account (${ta.user_id}) :: email: ${request.emailSetting.email} :: DKIMRecord ID: ${id}")

              Res.Success("DKIM record created successfully!!", Json.obj("dkim" -> dkim))

          }

      }
    } else {

      Logger.error(s"[FATAL] EmailSettingController.createDKIM :: Invalid Domain: $domain :: account (${ta.user_id}) :: email: ${request.emailSetting.email}")

      Res.BadRequestError("Invalid domain, we did not support free email service providers for DKIM signature")

    }

  }


  def findDKIM(v: String, id: Int, aid: Option[Long], tid: Option[Long]): Action[AnyContent] = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_CHANNELS,
    tidOpt = tid
  ) andThen hasEmailSetting(id)) { (request: EmailAccountRequest[AnyContent]) =>
    given Logger: SRLogger = request.Logger
    val Res = request.Response

    val domain = EmailValidationService.getLowercasedNameAndDomainFromEmail(request.emailSetting.email)._2

    val serviceProvider = request.emailSetting.service_provider

    if (
      serviceProvider == EmailServiceProvider.NAMECHEAP ||
        serviceProvider == EmailServiceProvider.REDIFF_MAIL ||
        serviceProvider == EmailServiceProvider.OTHER ||
        serviceProvider == EmailServiceProvider.EXCHANGE
    ) {

      emailSettingDAO.findDKIMRecord(domain = domain) match {

        case Failure(e) =>
          Res.ServerError("There was an error, please try again or contact support", e = Some(e))

        case Success(None) =>
          Res.Success("DKIM record not found", Json.obj("dkim" -> Json.obj()))

        case Success(Some(data)) =>
          Res.Success("DKIM record found", Json.obj("dkim" -> data))

      }
    } else {

      Logger.error(s"[FATAL] EmailSettingController.createDKIM :: Invalid Domain: $domain :: account (${request.actingTeamAccount.user_id}) :: email: ${request.emailSetting.email}")

      Res.BadRequestError("Invalid domain, we did not support free email service providers for DKIM signature")

    }

  }


  def verifyDKIM(v: String, id: Int, aid: Option[Long], tid: Option[Long]): Action[AnyContent] = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CHANNELS,
    tidOpt = tid
  ) andThen hasEmailSetting(id)) { (request: EmailAccountRequest[AnyContent]) =>
    given Logger: SRLogger = request.Logger
    val Res = request.Response

    val domain = EmailValidationService.getLowercasedNameAndDomainFromEmail(request.emailSetting.email)._2
    val ta = request.actingTeamAccount

    val serviceProvider = request.emailSetting.service_provider

    if (
      serviceProvider == EmailServiceProvider.NAMECHEAP ||
        serviceProvider == EmailServiceProvider.REDIFF_MAIL ||
        serviceProvider == EmailServiceProvider.OTHER ||
        serviceProvider == EmailServiceProvider.EXCHANGE
    ) {

      emailSettingDAO.findDKIMRecord(domain = domain) match {

        case Failure(e) =>
          Res.ServerError("There was an error, please try again or contact support", e = Some(e))

        case Success(None) =>
          Res.Success("DKIM record not found", Json.obj("dkim" -> Json.obj()))

        case Success(Some(data)) =>

          Helpers.digDKIM(data.domain, data.selector) match {

            case Failure(e) =>

              val dkimJson = Json.toJson(data.copy(record = "v=DKIM1; k=rsa; p=")).as[JsObject]

              val dkimJsonWithError = dkimJson ++ Json.obj("error" -> "dkim_notfound")

              emailSettingDAO.deactivateDKIMRecord(domain = data.domain, email_settings_id = id, error = Option(e.getMessage).getOrElse("").replaceAll("\u0000", "")) match {

                case Failure(e) =>
                  Logger.info("DKIM Deactivation failed")
                  Res.Success("DKIM record not found in your DNS, please update below one", Json.obj("dkim" -> dkimJsonWithError))


                case Success(value) =>

                  //EmailHelper.sendDKIMStatusEmail(domain, request.emailSetting.id.get, "error")

                  Res.Success("DKIM record not found in your DNS, please update below one", Json.obj("dkim" -> dkimJsonWithError))

              }

            case Success(key) =>

              if (key == data.public_key) {

                emailSettingDAO.activateDKIMRecord(domain = data.domain) match {

                  case Failure(e) =>
                    Res.ServerError("There was an error, please try again or contact support", e = Some(e))

                  case Success(None) =>
                    Logger.error(s"[FATAL] EmailSettingController.verifyDKIM.activateDKIMRecord There was an error while activateDKIMRecord: email_setting_id: $id :: aid: $aid :: tid: $tid")

                    Res.NotFoundError("There was an error, please try again or contact support [1]")

                  case Success(Some(data)) =>
                    emailNotificationService.sendDKIMStatusEmail(
                      domain = domain,
                      // FIXME VALUECLASS
                      accountId = AccountId(id = ta.user_id),
                      // FIXME VALUECLASS
                      teamId = TeamId(id = tid.get),
                      email_settings_id = request.emailSetting.id.get,
                      status = "active"
                    )
                    Res.Success("DKIM record found and verified", Json.obj("dkim" -> data))

                }
              } else {

                val dkimJson = Json.toJson(data.copy(

                  record = "v=DKIM1; k=rsa; p=" + key

                )).as[JsObject]

                val dkimJsonWithError = dkimJson ++ Json.obj("error" -> "dkim_mismatch")

                emailSettingDAO.deactivateDKIMRecord(domain = data.domain, email_settings_id = id, error = s"dkim_mismatch: Key: $key") match {

                  case Failure(e) =>
                    Logger.info("DKIM Deactivation failed")
                    Res.Success("Different DKIM record was found, please update below one", Json.obj("dkim" -> dkimJsonWithError))


                  case Success(value) =>
                    //EmailHelper.sendDKIMStatusEmail(domain, request.emailSetting.id.get, "error")
                    Res.Success("Different DKIM record was found, please update below one", Json.obj("dkim" -> dkimJsonWithError))

                }

              }

          }

      }
    } else {

      Logger.error(s"[FATAL] EmailSettingController.createDKIM :: Invalid Domain: $domain :: account (${ta.user_id}) :: email: ${request.emailSetting.email}")

      Res.BadRequestError("Invalid domain, we did not support free email service providers for DKIM signature")

    }
  }



  def create(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CHANNELS,
    tidOpt = tid
  ).async(parse.json) { request =>


    Future {
      given Logger: SRLogger= request.Logger
      val Res = request.Response


      if (request.actingTeamAccount.isEmpty) {

        Res.BadRequestError("Invalid team")

      } else {
        val ta = request.actingTeamAccount.get

        val validateData = request.body.validate[EmailSettingForm]

        validateData match {

          case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

          case JsSuccess(data, _) =>

            emailAccountService
              .createEmail(
                data = data,
                account = request.loggedinAccount,
                ta = ta
              ) match {

              case Left(CreateEmailError.FreeDomainUseError) => Res.BadRequestError(CONSTANTS.API_MSGS.FREE_EMAIL_DOMAIN_ERROR)

              case Left(CreateEmailError.EmailValidationError(e)) =>
                Res.BadRequestError(e.getMessage)

              case Left(CreateEmailError.DuplicateEmailError) =>
                Res.BadRequestError(s"You have already added ${data.email} to your SmartReach account")

              case Left(CreateEmailError.MultipleIntegrationAttemptError) =>
                Res.BadRequestError(
                  "You already have this email account integrated via the Gmail API. Please reconnect the existing email account by: clicking on the email -> 'Edit Email Settings' -> 'Reconnect' -> 'Reconnect with App-specific Password'"
                )

              case Left(CreateEmailError.SQLException(msg, e)) =>
                Res.ServerError("Error while adding email: " + msg, e = Some(e))

              case Left(CreateEmailError.EmailNotAddedError) =>
                Res.ServerError(s"Error while adding email account. Could you try again, or contact support ?", e = None)

              case Left(CreateEmailError.FeatureUsageServiceError(e)) =>
                Res.ServerError("Error while adding email: ", e = Some(e))

              case Right(row) =>
                Res.Success(s"Email account (${row.email}) has been added", Json.obj("email" -> row))
            }
        }
      }
    }

  }


  def update(v: String, id: Int, tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CHANNELS,
      tidOpt = tid
    )
      andThen hasEmailSetting(id)

    ).async(parse.json) { request =>

    Future {
      given Logger: SRLogger= request.Logger
      val Res = request.Response

      request.body.validate[EmailSettingForm] match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>

          Logger.info(s"EmailSettingController.update attempt: eset_$id")

          emailAccountService.updateBasicSettingInput(
            emailSettingId = EmailSettingId(emailSettingId = id),
            orgId = OrgId(id = request.loggedinAccount.org.id),
            data = data,
            teamId = TeamId(tid.get)
          ) match {
            case Left(UpdateBasicDetailsError.BasicDetailsValidationError(e)) => Res.BadRequestError(e.getMessage)

            case Left(UpdateBasicDetailsError.DuplicateEmailError) => Res.BadRequestError(s"You have already added ${data.email} to your SmartReach account")

            case Left(UpdateBasicDetailsError.SQLException(msg, e)) => Res.ServerError("Error while updating email: " + msg, e = Some(e))

            case Left(UpdateBasicDetailsError.EmailNotFoundError) => Res.NotFoundError("Email not found. Could you check and try again ?")

            case Right(emailSetting: EmailSetting) => Res.Success(s"Email account (${emailSetting.email}) has been updated", Json.obj("email" -> emailSetting))

          }
      }

    }
  }

  def updateEmailSetting(v: String, id: Int, tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CHANNELS,
      tidOpt = tid
    )
      andThen hasEmailSetting(id)

    ).async(parse.json) { request =>

    Future {
      given Logger: SRLogger= request.Logger
      val Res = request.Response

      request.body.validate[EmailSettingForm] match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>

          Logger.info(s"EmailSettingController.updateEmailSetting attempt: eset_$id : reqdata: $data")


          emailAccountService.updateEmailSettingInput(
            emailSettingId = EmailSettingId(emailSettingId = id),
            orgId = OrgId(id = request.loggedinAccount.org.id),
            data = data,
            teamId = TeamId(tid.get),
            max_email_sending_quota_per_day = request.loggedinAccount.org.org_metadata.max_email_sending_quota_per_day.getOrElse(200),
            increase_email_delay = request.loggedinAccount.org.org_metadata.increase_email_delay.getOrElse(false)
          ) match {
            case Left(UpdateEmailSettingsError.EmailSettingsValidationError(e)) => Res.BadRequestError(e.getMessage)

            case Left(UpdateEmailSettingsError.DuplicateEmailError) => Res.BadRequestError(s"You have already added ${data.email} to your SmartReach account")

            case Left(UpdateEmailSettingsError.SQLException(msg, e)) => Res.ServerError("Error while updating email: " + msg, e = Some(e))

            case Left(UpdateEmailSettingsError.EmailNotFoundError) => Res.NotFoundError("Email not found. Could you check and try again ?")

            case Right(emailSetting: EmailSetting) => Res.Success(s"Email account (${emailSetting.email}) has been updated", Json.obj("email" -> emailSetting))
          }

      }
    }
  }


  def getCustomDomainTracking(v: String, id: Int, aid: Option[Long], tid: Option[Long]): Action[AnyContent] = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.VIEW_CHANNELS,
      tidOpt = tid
    )
      andThen hasEmailSetting(id)

    ) { (request: EmailAccountRequest[AnyContent]) =>
    val Logger = request.Logger
    val Res = request.Response
    val loggedinAccount = request.loggedinAccount

    val cname_value = customTrackingDomainService.getCustomTrackingDomain(
      custom_tracking_cname_value = request.emailSetting.custom_tracking_cname_value,
      default_tracking_domain = request.emailSetting.default_tracking_domain,
      tracking_domain = request.emailSetting.email_address_host,
      accountId = loggedinAccount.internal_id
    )

    Res.Success("Found custom tracking domain settings", Json.obj(
      "custom_tracking_cname_value" -> cname_value
    ))
  }


  def updateCustomDomainTracking(v: String, id: Int, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.EDIT_CHANNELS,
      tidOpt = tid
    )
      andThen hasEmailSetting(id)

    ).async(parse.json) { request =>
    given Logger: SRLogger= request.Logger
    val Res = request.Response

      request.body.validate[CustomTrackingDomainForm] match {

        case JsError(e) => Future.successful(Res.JsValidationError(errors = e, requestBody = Some(request.body)))

        case JsSuccess(data, _) =>

          val ta = request.actingTeamAccount

          if (customTrackingDomainService.doCreateCustomTrackingDomainOldMethod(
              accountId = AccountId(ta.user_id),
              domain = data.tracking_domain_host.trim).get) {

          val trackingDomainHost = data.tracking_domain_host.trim

          val existingCustomTrackingDomain = request.emailSetting.custom_tracking_domain

          val customTrackingDomain = existingCustomTrackingDomain.getOrElse(EmailHelper.getCustomTrackingDomainFromUserInputTrackingHost(trackingDomain = Some(trackingDomainHost)).get)

          // email account might have already enabled the cname with a older tracking domain, we need to honor that
          val emailHasCustomTrackingCnameEnabledAlready = request.emailSetting.custom_tracking_cname_value

          val srTrackingDomainForEmailAccount = emailHasCustomTrackingCnameEnabledAlready.getOrElse(request.emailSetting.default_tracking_domain)

          if (Helpers.checkCName(
            host = customTrackingDomain,
            srTrackingDomainForEmailAccount = srTrackingDomainForEmailAccount
          ).isEmpty) {
            Future.successful(Res.BadRequestError(CONSTANTS.API_MSGS.CNAME_DNS_NOT_CORRECT))
          } else {

            val accountName = Helpers.getAccountName(request.loggedinAccount)
            val accountEmail = request.loggedinAccount.email

            customTrackingDomainService.createCustomTrackingDomainOld(accountId = request.loggedinAccount.internal_id, accountName = accountName, accountEmail = accountEmail, trackingDomainHost = trackingDomainHost, custom_tracking_cname_value = srTrackingDomainForEmailAccount, customTrackingDomain = customTrackingDomain) match {

              case Failure(e) => Future.successful(Res.ServerError(s"Error while updating email", e = Some(e)))

              case Success(row) =>

                emailNotificationService.sendMailFromAdmin(
                  toEmail = "<EMAIL>",
                  toName = Some("SmartReach Support"),

                  ccEmail = Some("<EMAIL>, <EMAIL>"),

                  subject = s"SR New CNAME SSL Request from : ${accountEmail} : ${accountName}",
                  body =
                    s"""
                       <br/>Name: ${accountName}
                       <br/>Email: ${accountEmail}
                       <br/>Tracking Domain Host: ${trackingDomainHost}
                       <br/>Tracking Domain Host: ${trackingDomainHost}
                       <br/>Custom Tracking CNAME Value: ${srTrackingDomainForEmailAccount}
                       <br/>Custom Tracking Domain: ${customTrackingDomain}
                        """.stripMargin
                )

                val emailSetting = emailSettingDAO.find(id = id)
                Future.successful(Res.Success(s"Email account has been updated", Json.obj("email" -> emailSetting.get)))

              }
            }

          } else {

          val es = request.emailSetting
          val accountName = Helpers.getAccountName(request.loggedinAccount)
          val accountEmail = ta.email


          val createCustomTrackingDomainF = customTrackingDomainService.createCustomTrackingDomain(
            data = data,
            emailSettingId = id,
            accountId = ta.user_id,
            accountName = accountName,
            accountEmail = accountEmail,
            custom_tracking_domain_is_ssl_enabled = es.custom_tracking_domain_is_ssl_enabled,
            logger = request.Logger
          )


          createCustomTrackingDomainF.map {

            case Left(CreateCustomTrackingDomainError.CnameRecordNotFoundError(error)) =>
              Res.BadRequestError(error)

            case Left(CreateCustomTrackingDomainError.CustomTrackingDomainAlreadyConfiguredError(error)) =>
              Res.BadRequestError(error)

            case Left(CreateCustomTrackingDomainError.SettingUpSSLStageError(error)) =>
              Res.BadRequestError(error)

            case Left(CreateCustomTrackingDomainError.ErrorWhileCreatingCustomTrackingDomain(error)) =>
              Res.BadRequestError(error)

            case Right(emailSetting) =>
              Res.Success(s"Email account has been updated", Json.obj("email" -> emailSetting))

          }.recover { case e =>

            Logger.fatal(s"Error while creating custom hostname: data: $data", err = e)

            Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, e = None)

          }
      }

          }

      }


  def updateSignature(v: String, id: Int, aid: Option[Long], tid: Option[Long]) = (permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CHANNELS,
    tidOpt = tid
  )
    andThen hasEmailSetting(id)).async(parse.json) { request =>

    Future {
      given Logger: SRLogger= request.Logger
      val Res = request.Response

      // NOTE: disabling signature route
      // ServerError("Disabled API", e = None)


      request.body.validate[EmailSettingUpdateSignatureForm] match {

        case JsError(e) => Res.JsValidationError(errors = e, requestBody = Some(request.body))

        case JsSuccess(data, _) =>

          Logger.info(s"EmailSettingController.updateSignature attempt: eset_$id : reqdata: $data")

          emailSettingService.updateSignature(emailSettingId = id,signature = data, teamId = TeamId(request.actingTeamAccount.team_id)) match {

            case Failure(e) => Res.ServerError(s"Error while updating email", e = Some(e))

            case Success(None) => Res.NotFoundError("Email not found")

            case Success(Some(row)) => Res.Success("Email updated", Json.obj("email" -> row))

          }

      }
    }

  }


  def deleteEmailSetting(v: String, id: Int, aid: Option[Long], tid: Option[Long]) = (
    permissionUtils.checkPermission(
      version = v,
      permission = PermType.DELETE_CHANNELS,
      tidOpt = tid
    )
      andThen hasEmailSetting(id)

    ).async(parse.json) { request =>

    Future {
      given Logger: SRLogger = request.Logger
      val Res = request.Response
      val team_id = TeamId(request.actingTeamAccount.team_id)

      Logger.info(s"DELETE deleteEmailSetting called by ${request.loggedinAccount.internal_id} :: $id")

      emailSettingService.deleteEmailSetting(
        emailSettingId = EmailSettingId(id),
        teamId = team_id
      ) match {

        case Failure(e) =>

          Logger.error(s"[FATAL] EmailSettingController.deleteEmailSetting :: emailSettingId: $id :: team_id: ${team_id.id} :: ${LogHelpers.getStackTraceAsString(e)}")

          Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, e = Some(e))

        case Success(_) =>

          Res.Success(
            "Your email account has been scheduled for deletion. It may take upto 30 minutes for the deletion to complete."
          )

      }

    }

  }


  def testSettings(v: String, aid: Option[Long], tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CHANNELS,
    tidOpt = tid
  ).async(parse.json) { request =>
    given Logger: SRLogger= request.Logger
    val Res = request.Response
    val account = request.loggedinAccount

    if (request.actingTeamAccount.isEmpty) {

      Future(Res.BadRequestError("Invalid team"))

    } else {
      val ta = request.actingTeamAccount.get
      request.body.validate[EmailSettingForm] match {

        case JsError(e) =>
          Future.successful(Res.JsValidationError(
          e,
          requestBody = Some(request.body)
        ))

        case JsSuccess(data, _) =>

          emailAccountTestService.testSettings(
              data = data,
              account = request.loggedinAccount,
              request = request,
              teamId = TeamId(ta.team_id)
            )
            .map {

              case Left(err) =>

                err match {

                  case EmailAccountTestSettingError.FreeDomainUseError =>
                    Res.BadRequestError (CONSTANTS.API_MSGS.FREE_EMAIL_DOMAIN_ERROR)

                  case EmailAccountTestSettingError.ValidationError(validationErr) =>
                    Res.BadRequestError(validationErr.getMessage)

                  case EmailAccountTestSettingError.TestError(errMsg, helpfulErrorData) =>

                    Res.BadRequestError(
                      message = errMsg,
                      helpfulMessageData = helpfulErrorData
                    )

                  case EmailAccountTestSettingError.EmailExistsAsProspect(errMsg) =>
                    Res.BadRequestError(errMsg)

                }

              case Right(_) =>
                Res.Success("Your email account settings have been successfully tested", Json.obj())


            }
            .recover { case e =>
              Res.ServerError(err = e)
            }


      }
    }
  }


  def moveEmailFromGmailAPIToGmailASP(v: String, esetid: Int, tid: Option[Long]) = (
    permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CHANNELS,
    tidOpt = tid
    ) andThen
      hasEmailSetting(emailSettingId = esetid)

    ).async(parse.json) { request =>

    given Logger: SRLogger= request.Logger
    val Res = request.Response
    val emailSetting = request.emailSetting
    val teamId = TeamId(request.actingTeamAccount.team_id)

    val gmailAppSpecificPasswordInput = (request.body \ "app_password").asOpt[String]

    gmailAppSpecificPasswordInput match {
      case None =>
        Future.successful(
          Res.BadRequestError("Please send the app_password")
        )

      case Some(appPassword) =>

        emailAccountService.moveEmailFromGmailAPIToGmailASP(
          emailSetting = emailSetting,
          appPassword = appPassword,
          account = request.loggedinAccount,
          permissionRequest = request.permissionRequest,
          teamId = teamId
        )
          .map {

            case Left(err) =>

              err match {

                case MoveEmailFromGmailApiToGmailASPError.TestError(
                testGmailAspErr
                ) =>

                  testGmailAspErr match {
                    case EmailAccountTestGmailASPError.TestError(testErr) =>

                      testErr match {

                        case EmailAccountTestSettingError.FreeDomainUseError =>
                          Res.BadRequestError(CONSTANTS.API_MSGS.FREE_EMAIL_DOMAIN_ERROR)

                        case EmailAccountTestSettingError.ValidationError(validationErr) =>
                          Res.BadRequestError(validationErr.getMessage)


                        case EmailAccountTestSettingError.TestError(errMsg,helpfulErrorData) =>
                          Res.BadRequestError(errMsg)

                        case EmailAccountTestSettingError.EmailExistsAsProspect(errMsg) =>
                          Res.BadRequestError(errMsg)

                      }

                    case EmailAccountTestGmailASPError.ESPMustBeGmailAPI =>
                      Res.BadRequestError("Email must be connected via Gmail Api as of now")

                  }

                case MoveEmailFromGmailApiToGmailASPError.SQLError(e) =>

                  Res.ServerError(err = e)

              }

            case Right(_) =>

              Logger.info(s"[MOVED_TO_GMAIL_ASP]: email_${emailSetting.email} : team_${request.actingTeamAccount.team_name} : eset_${emailSetting.id.map(_.emailSettingId).orNull}")

              Res.Success("Your email account has been successfully migrated to Gmail App Password flow!", Json.obj())


          }
          .recover { case e => Res.ServerError(err = e) }
    }


  }

  //Get email_settings owned by logged_in user
  def getEmailSettings(v: String, aid: Option[Long], tid: Option[Long]): Action[AnyContent] = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_CHANNELS,
    tidOpt = tid
  ) { (request: PermissionRequest[AnyContent]) =>
    val Res = request.Response
    val logged_in_account_id = request.loggedinAccount.internal_id

    given Logger: SRLogger = request.Logger

    emailAccountService.getEmailAccountsOwnedByLoggedInAccount(
      logged_in_account_id = logged_in_account_id,
      teamId = tid
    ) match {
      case Left(exception) =>
        Res.ServerError(
          message = "Server error, please contact support.",
          e = None
        )
      case Right(esets) =>
        if (esets.isEmpty) {
          Res.NotFoundError("You can only setup the inbox for the integrated emails owned by you. Please setup new email account or ask other team members to create and share their inbox if they own any email accounts. After creating the inbox, user can edit the permissions for the inbox in Team Settings -> Team Inbox Settings page.")
        } else {
          Res.Success("Email accounts found", Json.obj("emails" -> esets))
        }
    }
  }


  //bulk email upload

//  def saveUploadedBulkEmailCsv

    def saveUploadedBulkEmailCsv(tid: Option[Long],v: api.ApiVersion): Action[JsValue] = permissionUtils.checkPermission(
        version = ApiVersion.V3.toString,
        permission = PermType.EDIT_CHANNELS,
        tidOpt = tid

    ).async(parse.json) { (request: PermissionRequest[JsValue]) =>


        Future {
            val Logger = request.Logger
            val Res = request.Response

            val t = request.actingTeamAccount
            val loggedInAccountId = request.loggedinAccount.internal_id
            implicit val apiVersion: ApiVersion = ApiVersion.V3


            val validateData = request.body.validate[CsvQueueCreateFormDataV2]

            validateData match {

                case JsError(e) =>
                    Res.JsValidationError(errors = e)

                case JsSuccess(data, _) =>

                    Logger.info(s"saveUploadedDncCsv: initiated with request body: data: $data")
                    CsvQueue.createV2(
                        accountId = loggedInAccountId,
                        teamId = t.map(te => te.team_id),
                        ta_id = t.map(te => te.ta_id),
                        loggedin_id = loggedInAccountId,
                        data = data
                    ) match {

                        case Failure(e) =>
                            Res.ServerError(s"There was an error. Please try again or contact support", e = Some(e))

                        case Success(obj) =>
                            Res.Success("CSV will be uploaded in a few minutes", Json.obj("csv_file" -> obj))

                    }

            }
        }

    }

  def bulkUpdateEmailSettings(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.EDIT_CHANNELS,
    tidOpt = tid
  ).async(parse.json) { request =>

      Future {
          given Logger: SRLogger = request.Logger

          val Res = request.Response

          request.body.validate[EmailSettingDailyLimitAndDelay] match {

              case JsError(e) =>
                  Logger.error(s"EmailSettingController.bulkUpdateEmailSettings Json validation failed: teamId :${tid.get} : request : ${request.body}")
                  Res.JsValidationError(errors = e, requestBody = Some(request.body))

              case JsSuccess(data, _) =>

                  Logger.info(s"EmailSettingController.bulkUpdateEmailSettings attempt: teamId :${tid.get} : reqdata: $data")


                  emailAccountService.updateEmailSettingsQuotaAndDelays(
//                      emailSettingIds = data.es_ids.map(esId => EmailSettingId(esId)),
//                      orgId = OrgId(id = request.loggedinAccount.org.id),
                      data = data,
                      teamId = TeamId(tid.get),
                      max_email_sending_quota_per_day = request.loggedinAccount.org.org_metadata.max_email_sending_quota_per_day.getOrElse(200),
                      increase_email_delay = request.loggedinAccount.org.org_metadata.increase_email_delay.getOrElse(false)
                  ) match {
                      case Left(UpdateEmailSettingsError.EmailSettingsValidationError(e)) =>
                          Logger.error(s"EmailSettingController.bulkUpdateEmailSettings email setting validation failed: teamId :${tid.get} : error : ${e.getMessage}", err = e)
                          Res.BadRequestError(e.getMessage)

                      case Left(UpdateEmailSettingsError.DuplicateEmailError) =>
                          Logger.error(s"EmailSettingController.bulkUpdateEmailSettings duplicate email occurred: teamId :${tid.get}")
                          Res.BadRequestError(s"You have already added the email to your SmartReach account")

                      case Left(UpdateEmailSettingsError.SQLException(msg, e)) =>
                          Logger.error(s"EmailSettingController.bulkUpdateEmailSettings sql error occurred: teamId :${tid.get} : error : ${e.getMessage}",err = e)
                          Res.ServerError("Error while updating email: " + msg, e = Some(e))

                      case Left(UpdateEmailSettingsError.EmailNotFoundError) =>
                          Logger.error(s"EmailSettingController.bulkUpdateEmailSettings email not found: teamId :${tid.get}")
                          Res.NotFoundError("Email not found. Could you check and try again ?")

                      case Right(emailSettings) =>
                          Res.Success(s"Email accounts has been updated", Json.obj("emails" -> emailSettings))
                  }

          }
      }
  }

  def getEmailSettingsTags(v: String, tid: Option[Long]): Action[AnyContent] = permissionUtils.checkPermission(
    version = v,
    permission = PermType.VIEW_CHANNELS,
    tidOpt = tid
  ) { request =>

      given Logger: SRLogger = request.Logger

      val Res = request.Response

      emailSettingService.getDistinctTagsForTeam(TeamId(tid.get)) match {
          case Failure(e) =>
              Logger.error(s"EmailSettingController.getEmailSettingsTags fetch failed: teamId :${tid.get} : error : ${e.getMessage}", err = e)
              Res.ServerError("Error while fetching email tags: " + e.getMessage, e = Some(e))

          case Success(tags) =>
              Logger.info(s"EmailSettingController.getEmailSettingsTags success: teamId :${tid.get} : found ${tags.size} tags")
              Res.Success(
                  message = "Email tags retrieved successfully",
                  data = Json.obj("tags" -> tags)
              )
      }
  }

  def bulkDeleteEmailSetting(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
    version = v,
    permission = PermType.DELETE_CHANNELS,
    tidOpt = tid
  ).async(parse.json) { request =>
      
      Future {
          given Logger: SRLogger = request.Logger
          val Res = request.Response
          val team_id = TeamId(request.actingTeamAccount.get.team_id)
          
          // JSON validation using Reads - more robust approach for parsing the email setting IDs
          
          request.body.validate[BulkDeleteRequest] match {
              case JsError(e) =>
                  Logger.error(s"EmailSettingController.bulkDeleteEmailSetting Json validation failed: teamId :${tid.get} : request : ${request.body}")
                  Res.JsValidationError(errors = e, requestBody = Some(request.body))
                  
              case JsSuccess(data, _) =>
                  Logger.info(s"EmailSettingController.bulkDeleteEmailSetting attempt: teamId :${tid.get} : email_settings_count: ${data.es_ids.length}")
                  
                  if (data.es_ids.isEmpty) {
                      Res.Success("No email accounts were specified for deletion", Json.obj("deleted_count" -> 0))
                  } else {
                      val emailSettingIds = data.es_ids.map(id => EmailSettingId(emailSettingId = id))
                      
                      emailSettingService.deleteBulkEmailSettings(
                          emailSettings = emailSettingIds,
                          teamId = team_id
                      ) match {
                          case Failure(exception) =>
                              Logger.error(s"[FATAL] EmailSettingController.bulkDeleteEmailSetting :: emailSettingIds: ${emailSettingIds} :: team_id: ${team_id.id} :: ${LogHelpers.getStackTraceAsString(exception)}")
                              Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, e = Some(exception))
      
                          case Success(results) =>
                              val successCount = results.length
                              Res.Success(
                                  "Your email accounts have been scheduled for deletion. It may take up to 30 minutes for the deletion to complete.",
                                  Json.obj("deleted_count" -> successCount)
                              )
                      }
                  }
          }
      }
  }

    def bulkUpdateEmailSettingsTags(v: String, tid: Option[Long]) = permissionUtils.checkPermission(
        version = v,
        permission = PermType.EDIT_CHANNELS,
        tidOpt = tid
    ).async(parse.json) { request =>

        Future {
            given Logger: SRLogger = request.Logger

            val Res = request.Response

            request.body.validate[EmailSettingBulkTagUpdate] match {

                case JsError(e) =>
                    Logger.error(s"EmailSettingController.bulkUpdateEmailSettingsTags Json validation failed: teamId :${tid.get} : request : ${request.body}")
                    Res.JsValidationError(errors = e, requestBody = Some(request.body))

                case JsSuccess(data, _) =>

                    Logger.info(s"EmailSettingController.bulkUpdateEmailSettingsTags attempt: teamId :${tid.get} : reqdata: $data")

                    emailSettingService.updateEmailSettingsTags(
                        data = data,
                        teamId = TeamId(tid.get)
                    ) match {
                        case Failure(e: IllegalArgumentException) =>
                            Logger.warn(s"EmailSettingController.bulkUpdateEmailSettingsTags validation failed: teamId :${tid.get} : error : ${e.getMessage}")
                            Res.BadRequestError(e.getMessage)
                          
                        case Failure(e) =>
                            Logger.error(s"EmailSettingController.bulkUpdateEmailSettingsTags update failed: teamId :${tid.get} : error : ${e.getMessage}", err = e)
                            Res.ServerError("Error while updating email tags: " + e.getMessage, e = Some(e))

                        case Success(emailSettings) =>
                            Logger.info(s"EmailSettingController.bulkUpdateEmailSettingsTags success: teamId :${tid.get} : updated ${emailSettings.size} email settings")
                            Res.Success(s"Email tags have been updated successfully", Json.obj("emails" -> emailSettings))
                    }

            }
        }
    }

}
