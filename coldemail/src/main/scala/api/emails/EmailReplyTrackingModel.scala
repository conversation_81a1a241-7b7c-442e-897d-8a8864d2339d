package api.emails

import api.accounts.{ReplyHandling, TeamId}
import api.campaigns.CPCompleted
import api.emails.models.{DeletionReason, EmailReplyType}
import api.emails.services.SelectAndPublishForDeletionService
import api.prospects.models.ProspectCategory
import api.prospects.service.ProspectServiceV2
import io.smartreach.esp.api.emails.IEmailAddress
import io.smartreach.esp.utils.email.services.GmailServiceCommon
import org.joda.time.DateTime
import org.postgresql.util.PGobject
import play.api.libs.json.{JsVal<PERSON>, <PERSON><PERSON>}
import scalikejdbc.*
import scalikejdbc.jodatime.JodaWrappedResultSet.*
import utils.dbutils.DBUtils
import utils.email.models.DeleteEmailsScheduledType
import utils.{Helpers, SRLogger}
import utils.email.EmailReplyStatus
import utils.helpers.LogHelpers
import utils.uuid.SrUuidUtils

import scala.util.{Success, Try}

case class DBEmailMessagesSavedResponse(
                                         savedMessages: Seq[EmailReplySavedV3],
                                         emailMessagesFromProspects: Seq[EmailReplySavedV3],
                                         prospectIdsWithHardBouncedEmail: Seq[Long],
                                         prospectIdsWithSoftBouncedEmail: Seq[Long],
                                         emailScheduledIdsWithHardBouncedEmail: Seq[Long],
                                         emailScheduledIdsWithSoftBouncedEmail: Seq[Long],
                                         emailScheduledIdsWithAutoReplyEmail: Seq[Long],
                                         emailScheduledIdsWithOutOfOfficeReplyEmail: Seq[Long],
                                         completedWebhookData: Seq[CPCompleted],
                                         gmailSendingLimitErrorEmailSettingId: Option[Long], // emailSettingId
                                         hardBouncedReplies:  Seq[EmailReplySavedV3],
                                         softBouncedReplies:  Seq[EmailReplySavedV3]
)

case class EmailReplySavedV3(
  message_id: String,
  email_scheduled_id: Long,
  email_thread_id: Long,
  by_account: Boolean,
  sent_at: DateTime,
  campaign_id: Option[Long],
  step_id: Option[Long],
  prospect_id_in_campaign: Option[Long],
  prospect_account_id_in_campaign: Option[Long],
  reply_type: EmailReplyType.Value,
  from_email: IEmailAddress,
  to_emails: Seq[IEmailAddress],

  email_body: String,

  cc_emails: Seq[IEmailAddress],

  reply_to: Option[IEmailAddress],

  inbox_email_setting_id: Long,
  references_header: Option[String],
  in_reply_to_header: Option[String],


  from_prospect: Option[ERIntermediateValidProspect],
  to_prospects: Seq[ERIntermediateValidProspect],
  // cced_prospects: Seq[ERIntermediateValidProspect], todo add this later
  campaign_associated_prospect: Option[ERIntermediateValidProspect],

  // this includes from_prospect, to_prospects, and cced_prospects
  all_prospects_involved: Seq[ERIntermediateValidProspect],

  email_status: EmailReplyStatus,
  subject: String,
  base_body: String,
  text_body: String,
  full_headers: JsValue,

  gmail_msg_id: Option[String],

  gmail_thread_id: Option[String],
  outlook_msg_id: Option[String],
  outlook_conversation_id: Option[String],

  outlook_response_json: Option[JsValue],
  team_id: TeamId,
)


case class EmailReplySavedV3Basic(
  campaign_id: Option[Long],
  prospect_id_in_campaign: Option[Long],
  prospect_account_id_in_campaign: Option[Long]
                            )

case class UpdateEmailStatusOnReplyV3(
  byAccount: Boolean,
  emailStatus: EmailReplyStatus,
  emailScheduledId: Long,
  emailThreadId: Long,
  sentAt: DateTime,
  prospectId: Long,
  campaignId: Long,
  stepId: Option[Long]
)


class EmailReplyTrackingModel(
//  emailThreadDAO: EmailThreadDAO,
  emailScheduledDAO: EmailScheduledDAO,
  emailMessageDataDAO: EmailMessageDataDAO,

  selectAndPublishForDeletionService: SelectAndPublishForDeletionService
//  prospectServiceV2: ProspectServiceV2
) {


  implicit val session: AutoSession.type = AutoSession

  // joda implicit datetime ordering for finally sorting events
  // REF: https://stackoverflow.com/a/9061184
  implicit def dateTimeOrdering: Ordering[DateTime] = Ordering.fromLessThan((a: DateTime, b: DateTime) => a.isBefore(b))


  // newinbox api def
  def _insertTrackedRepliesV3(
    newMsgsWithEmailThreadId: Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])],
    sentManuallyFromSRInbox: Boolean,

  )(using Logger: SRLogger): Try[Seq[EmailReplySavedV3]] = {

    if (newMsgsWithEmailThreadId.isEmpty) Success(Seq())

    else {
    DB localTx { implicit session =>


      val result = newMsgsWithEmailThreadId
        .grouped(100)
        .map(data => {
          for{
            adding_in_scheduled_table: Seq[EmailReplySavedV3] <- emailScheduledDAO._insertTrackedRepliesV3(
              newMsgsWithEmailThreadId = data,
              sentManuallyFromSRInbox = sentManuallyFromSRInbox)
            _: Seq[Long] <- emailMessageDataDAO.addingInEmailMessageDataTableForTracker(
              toUpdateEmailThreadsInternal = adding_in_scheduled_table
            )
          } yield adding_in_scheduled_table

        }).toSeq
      Helpers.seqTryToTrySeq(result).map(_.flatten)
    }

    }

  }

  // NOTE: this is ONLY to be used in the reply tracking code, it searches prospects by emails and ids both at the same time
  // Use case : sendNewManualEmailV2 In inbox when user replies to email thread manually. PROSPECTS_EMAILS_TODO_READ_CLEANED
  def _findIntermediateProspectsForReplyTracking(
    teamId: Long,
    byProspectEmails: Seq[String] = Seq(),
    byProspectIds: Seq[Long] = Seq()
  )(using Logger: SRLogger): Try[List[ERIntermediateValidProspect]] =  {
      //DB.readOnly { implicit session =>

    val _whereClauseForEmails: SQLSyntax = if (byProspectEmails.isEmpty) sqls""
    else {
      sqls" lower(pe.email) IN (${byProspectEmails.map(_.trim.toLowerCase).distinct}) "
    }

    val _whereClauseForId = if (byProspectIds.isEmpty) sqls"" else {
      sqls" prospects.id IN (${byProspectIds.distinct}) "
    }


    val prospectQuery =
      sqls"""
      select
      prospects.id,
      pe.email,
      pe.email_domain,
      prospects.team_id,
      ta_id,
      prospects.account_id,
      prospect_account_id,
      prospect_category_id_custom,
      pm.last_contacted_at
      from prospects
      INNER JOIN prospects_emails pe ON (pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id)
      LEFT JOIN prospects_metadata pm on (prospects.id = pm.prospect_id) AND (prospects.team_id = pm.team_id)
      where prospects.team_id = $teamId

    """

    val queryWithwhereClause: SQLSyntax = (byProspectEmails.nonEmpty, byProspectIds.nonEmpty) match {

      case (false, false) =>

        throw new Exception("boths emails, ids empty")

      case (true, false) =>

        sqls"$prospectQuery AND ${_whereClauseForEmails} "

      case (false, true) =>

        sqls"$prospectQuery AND ${_whereClauseForId} "

      case (true, true) =>

        sqls"($prospectQuery AND ${_whereClauseForEmails}) UNION ALL ($prospectQuery AND ${_whereClauseForId}) "

    }

    DBUtils.readOnlyWithExplain(
      query = queryWithwhereClause,
      logger = Logger.appendLogRequestId("_findIntermediateProspectsForReplyTracking")
    ) { implicit session =>
      sql""" $queryWithwhereClause """
      .map(rs => ERIntermediateValidProspect(
        prospect_id = rs.long("id"),
        email = rs.string("email").toLowerCase.trim,
        email_domain = rs.string("email_domain"),
        team_id = rs.long("team_id"),
        ta_id = rs.long("ta_id"),
        account_id = rs.long("account_id"),
        prospect_account_id = rs.longOpt("prospect_account_id"),
        prospect_category_id_custom = rs.long("prospect_category_id_custom"),
        last_contacted_at = rs.jodaDateTimeOpt("last_contacted_at")

        ))
      .list
      .apply()
    }


  //}
  }

  /**
    * HANDLE GMAIL SENDING LIMIT REACHED ERROR
    *
    * pause gmail address and its aliases
    *
    * NOTE: this should be done irrespective of whether email comes from prospect or not (all saved replies)
    *
    * on gmail sending_limit reached error, resend emails to affected prospects
    *
    * NOTE: sending email notification to admin in EmailService on this error
    * */
  def _getGmailSendingLimitReachedErrorSenderId(
    savedReplies: Seq[EmailReplySavedV3],
    Logger: SRLogger
  ): Option[Long] = {

    val gmailSendingLimitHits = savedReplies
      .filter(em => {

        // todo newinbox donot pause / mark completed for gmail sending limit hits
        em.reply_type == EmailReplyType.DELIVERY_FAILED &&
          (
            GmailServiceCommon
              .isSendingLimitReachedMessage(
                fromEmail = em.from_email.email,
                emailBody = em.email_body
              )
            )
      })
      .sortBy(_.sent_at)

    if (gmailSendingLimitHits.isEmpty) None else {


      val gmailSendingLimitErrorEmailSettingId = gmailSendingLimitHits.head.inbox_email_setting_id
      // val latestHitAt = gmailSendingLimitHits.last.sent_at

      // change fatal to error ofter verifying this is working
      Logger.fatal(s"${gmailSendingLimitHits.size} PAUSING $gmailSendingLimitErrorEmailSettingId")

      // val pausedTill = latestHitAt.now().plusHours(24)

      Some(gmailSendingLimitErrorEmailSettingId)
    }
  }


  // used as part of transaction on email replied
  // INTERNAL USE
  def _deleteUnsentByProspectIdsOnRepliesV3(

    emailsReceived: Seq[EmailReplySavedV3Basic],

    replyHandling: ReplyHandling.Value,
    teamId: TeamId,
    deletion_reason: DeletionReason

  )(using Logger: SRLogger): Int = {


    DB localTx { implicit session =>

      if (emailsReceived.isEmpty) 0
      else {


        val toDeleteCampaignProspectsMap: Seq[(Long, Seq[Long], Seq[Long])] = emailsReceived
          .filter(_.campaign_id.isDefined)
          .groupBy(_.campaign_id.get)
          .map { case (campaignId, replies) =>

            (
              campaignId,
              replies.map(_.prospect_id_in_campaign).filter(_.isDefined).map(_.get).distinct,
              replies.map(_.prospect_account_id_in_campaign).filter(_.isDefined).map(_.get).distinct
            )
          }
          .toSeq


        toDeleteCampaignProspectsMap.map { case (campaignId, prospectIds, prospectAccountIds) =>

          selectAndPublishForDeletionService.selectAndPublishForDeletion(
            deletion_reason = deletion_reason,
              deleteEmailsScheduledType  = DeleteEmailsScheduledType.DeleteUnsentByProspectId(
                campaignId = campaignId,
                teamId = teamId,
                prospectIds = prospectIds,
                prospectAccountIds = if (prospectAccountIds.nonEmpty) Some(prospectAccountIds) else None,
                replyHandling = replyHandling,
              )
            ).get // will never fail as internally we are returning Success(Int)

        }.sum


      }

    }


  }


  // INTERNAL USE
  // track via email_scheduled id itself so exact email can be marked as bounced / auto replied
  def _updateEmailStatusOnReplyV3(
    emailsToBeUpdated: Seq[UpdateEmailStatusOnReplyV3],
    SRLogger: SRLogger
  ): Try[Seq[Long]] = Try {

    DB.localTx { implicit session =>

      val Logger = SRLogger.appendLogRequestId(
        appendLogReqId = s"es._updateEmailStatusOnReplyV3"
      )

      Logger.info(s"Step 1: emailsToBeUpdated: $emailsToBeUpdated")

      if (emailsToBeUpdated.isEmpty) Seq()
      else {

        var valueParameters = List[Any]()

        val valuePlaceholder: String = emailsToBeUpdated
          .map(e => {

            val s = e.emailStatus

            val isReplied = s.isReplied
            val isBounced = s.bouncedData.isDefined
            val isAutoReply = s.isAutoReply
            val isOOFReply = s.isOutOfOfficeReply

            valueParameters = valueParameters ::: List(
              e.sentAt,
              s.replyType.toString,

              isReplied,
              if (isReplied) e.sentAt else null,

              isBounced,
              if (isBounced) e.sentAt else null,

              isAutoReply,
              if (isAutoReply) e.sentAt else null,

              isOOFReply,
              if (isOOFReply) e.sentAt else null,

              e.campaignId,
              e.stepId,
              e.prospectId
            )

            s"""
              (
                ?,
                ?,

                ?,
                ?,

                ?,
                ?,

                ?,
                ?,

                ?,
                ?,

                ?,
                ?,
                ?
              )

            """

          })
          .mkString(", ")

        Logger.info(s"Step 3")

        SQL(
          s"""
         UPDATE emails_scheduled es SET

            replied = es.replied or temp.replied,
            replied_at = COALESCE(es.replied_at, temp.reply_at::timestamptz),
            reply_type = temp.reply_type,

            bounced = es.bounced or temp.bounced,
            bounced_at = COALESCE(es.bounced_at, temp.bounced_at::timestamptz),

            paused = true,
            paused_at = COALESCE(es.paused_at, temp.paused_at::timestamptz),

            auto_reply = es.auto_reply or temp.autoreply,
            auto_reply_at = COALESCE(es.auto_reply_at, temp.autoreply_at::timestamptz),

            out_of_office_reply = es.out_of_office_reply or temp.oofreply,
            out_of_office_reply_at = COALESCE(es.out_of_office_reply_at, temp.oofreply_at::timestamptz)

          FROM (
            VALUES $valuePlaceholder
          )
          AS temp(
          paused_at,
          reply_type,

          replied,
          reply_at,

          bounced,
          bounced_at,

          autoreply,
          autoreply_at,

          oofreply,
          oofreply_at,

          campaign_id,
          step_id,
          prospect_id

          )
          WHERE es.campaign_id = temp.campaign_id
            AND es.prospect_id = temp.prospect_id

            AND (
            	(temp.step_id IS NULL AND es.step_id IS NULL)
            	OR
            	(temp.step_id IS NOT NULL AND es.step_id = temp.step_id::bigint)
            )
          RETURNING es.prospect_id;
        """)
          .bind(valueParameters*)
          .map(rs => rs.long("prospect_id"))
          .list
          .apply()
      }

    }


  }

  def getIdAndNameForSelectedCampaignIds(allCampaignIds: Seq[Long]): Try[Map[Long, (Long, String)]] = Try{

    sql"""select id, name from campaigns where id IN ($allCampaignIds)"""
      .map(rs => (
        rs.long("id"),
        rs.string("name")
      ))
      .list
      .apply()
      .map(r => (r._1 -> r)).toMap

  }

  def getIdAndLabelForSelectedStepIds(allStepIds: Seq[Long]): Try[Map[Long, (Long, String)]] = Try{

    sql"""select id, label from campaign_steps where id IN ($allStepIds)"""
      .map(rs => (
        rs.long("id"),
        rs.string("label")
      ))
      .list
      .apply()
      .map(r => (r._1 -> r))
      .toMap

  }

}
