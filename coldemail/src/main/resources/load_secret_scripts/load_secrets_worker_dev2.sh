
RESOURCES_FOLDER=src/main/resources
SECRETS_PATH=/home/<USER>/smartreach/coldemail/$RESOURCES_FOLDER/load_secret_scripts/load_secrets_worker.sh
source $SECRETS_PATH

export APP_DOMAIN=`gcloud secrets versions access latest --secret=SR_APP_DOMAIN_DEV2`


export RABBITMQ_PREFIX=`gcloud secrets versions access latest --secret=SR_RABBITMQ_PREFIX_DEV2`

export REDIS_KEY_PREFIX=`gcloud secrets versions access latest --secret=SR_REDIS_KEY_PREFIX_DEV2`


#DB
export JDBC_URL=`gcloud secrets versions access latest --secret=SR_GCP_PROD_DB_DEV2_JDBC_URL`
export JDBC_USER=`gcloud secrets versions access latest --secret=SR_GCP_PROD_DB_DEV2_JDBC_USER`
export JDBC_PASS=`gcloud secrets versions access latest --secret=SR_GCP_PROD_DB_DEV2_JD<PERSON>_PASS`

#ENC_KEYS
export SR_ENC_KEY_EMAIL_SETTINGS_CREDENTIAL=`gcloud secrets versions access latest --secret=SR_ENC_KEY_EMAIL_SETTINGS_CREDENTIAL_DEV2`
export SR_ENC_KEY_EMAIL_DATAPLATFORM_CREDENTIAL=`gcloud secrets versions access latest --secret=SR_ENC_KEY_EMAIL_DATAPLATFORM_CREDENTIAL_DEV2`

export SR_ENC_KEY_WORKFLOW_CRM_SETTINGS_CREDENTIAL=`gcloud secrets versions access latest --secret=SR_ENC_KEY_WORKFLOW_CRM_SETTINGS_CREDENTIAL_DEV2`

export SR_ENC_KEY_ACCOUNTS_CREDENTIAL=`gcloud secrets versions access latest --secret=SR_ENC_KEY_ACCOUNTS_CREDENTIAL_DEV2`

export SR_ENC_KEY_DKIM_RECORDS_CREDENTIAL=`gcloud secrets versions access latest --secret=SR_ENC_KEY_DKIM_RECORDS_CREDENTIAL_DEV2`

export SR_ENC_KEY_TEAMS_ACCOUNTS_CREDENTIAL=`gcloud secrets versions access latest --secret=SR_ENC_KEY_TEAMS_ACCOUNTS_CREDENTIAL_DEV2`

export SR_ENC_KEY_ORGANIZATIONS_CREDENTIAL=`gcloud secrets versions access latest --secret=SR_ENC_KEY_ORGANIZATIONS_CREDENTIAL_DEV2`

export SR_ENC_KEY_NATIVE_CALLING_API_KEY=`gcloud secrets versions access latest --secret=SR_ENC_KEY_NATIVE_CALLING_API_KEY_DEV2`

#BASE_API_URL
export SR_API_BASE_URL=`gcloud secrets versions access latest --secret=SR_API_BASE_URL_DEV2`

#COMMON_AUTH_URLS
export SMARTREACH_CLIENT_ID=`gcloud secrets versions access latest --secret=SMARTREACH_CLIENT_ID_DEV2`
export SMARTREACH_CLIENT_SECRET=`gcloud secrets versions access latest --secret=SMARTREACH_CLIENT_SECRET_DEV2`
export STAGING_FRONTEND_URL=`gcloud secrets versions access latest --secret=STAGING_FRONTEND_URL_DEV2`
export STAGING_ID_FRONTEND_URL=`gcloud secrets versions access latest --secret=STAGING_ID_FRONTEND_URL_DEV2`
export STAGING_BACKEND_URL=`gcloud secrets versions access latest --secret=STAGING_BACKEND_URL_DEV2`
export STAGING_AUTH_URL=`gcloud secrets versions access latest --secret=STAGING_AUTH_URL_DEV2`