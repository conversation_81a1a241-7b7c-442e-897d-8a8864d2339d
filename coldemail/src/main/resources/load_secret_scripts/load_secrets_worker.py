import os
import subprocess
import argparse
from google.cloud import secretmanager
import concurrent.futures
import sys

# Get the GCP project ID
def get_project_id():
    project_id = os.environ.get("GCP_PROJECT_ID")
    if project_id:
        return project_id
    try:
        result = subprocess.run(
            ["gcloud", "config", "get-value", "project"],
            capture_output=True,
            text=True,
            check=True
        )
        project_id = result.stdout.strip()
        if not project_id:
            raise ValueError("No project ID returned from gcloud.")
        os.environ["GCP_PROJECT_ID"] = project_id
        return project_id
    except subprocess.CalledProcessError as e:
        print(f"Error: Failed to get project ID from gcloud: {e}", file=sys.stderr)
        sys.exit(1)
    except FileNotFoundError:
        print("Error: 'gcloud' command not found. Ensure gcloud is installed.", file=sys.stderr)
        sys.exit(1)

project_id = get_project_id()
client = secretmanager.SecretManagerServiceClient()

# Base mapping of environment variables to secret names
env_to_secret = {
    "APPLICATION_SECRET": "SR_PLAY_CRYPTO_SECRET",
    "APP_DOMAIN": "SR_APP_DOMAIN",
    "SR_ENC_KEY_EMAIL_SETTINGS_CREDENTIAL": "SR_ENC_KEY_EMAIL_SETTINGS_CREDENTIAL",
    "SR_ENC_KEY_EMAIL_DATAPLATFORM_CREDENTIAL": "SR_ENC_KEY_EMAIL_DATAPLATFORM_CREDENTIAL",
    "SR_ENC_KEY_WORKFLOW_CRM_SETTINGS_CREDENTIAL": "SR_ENC_KEY_WORKFLOW_CRM_SETTINGS_CREDENTIAL",
    "SR_ENC_KEY_ACCOUNTS_CREDENTIAL": "SR_ENC_KEY_ACCOUNTS_CREDENTIAL",
    "SR_ENC_KEY_DKIM_RECORDS_CREDENTIAL": "SR_ENC_KEY_DKIM_RECORDS_CREDENTIAL",
    "SR_ENC_KEY_TEAMS_ACCOUNTS_CREDENTIAL": "SR_ENC_KEY_TEAMS_ACCOUNTS_CREDENTIAL",
    "SR_ENC_KEY_ORGANIZATIONS_CREDENTIAL": "SR_ENC_KEY_ORGANIZATIONS_CREDENTIAL",
    "SR_ENC_KEY_PHANTOMBUSTER_API_KEY": "SR_ENC_KEY_PHANTOMBUSTER_API_KEY",
    "SR_ENC_KEY_PHANTOMBUSTER_WEBHOOK_TEAM_SECRET": "SR_ENC_KEY_PHANTOMBUSTER_WEBHOOK_TEAM_SECRET",
    "SR_ENC_KEY_LINKEDIN_SESSION_COOKIE": "SR_ENC_KEY_LINKEDIN_SESSION_COOKIE",
    "SR_ENC_KEY_PHANTOMBUSTER_PROXY_PASSWORD": "SR_ENC_KEY_PHANTOMBUSTER_PROXY_PASSWORD",
    "SR_ENC_KEY_LINKEDIN_ACCOUNT_PASSWORD": "SR_ENC_KEY_LINKEDIN_ACCOUNT_PASSWORD",
    "SR_ENC_KEY_CALENDLY_SETTINGS_CREDENTIAL":"SR_ENC_KEY_CALENDLY_SETTINGS_CREDENTIAL",
    "AWS_ACCESS_KEY_ID": "SR_AWS_ACCESS_KEY_ID",
    "AWS_SECRET_ACCESS_KEY": "SR_AWS_SECRET_ACCESS_KEY",
    "EMAIL_MESSAGE_ID_SUFFIX": "SR_EMAIL_MESSAGE_ID_SUFFIX",
    "ENCRYPTION_KEY": "SR_APPLICATION_ENCRYPTION_KEY",
    "EMAIL_NOTIFICATION_ENCRYPT_KEY": "SR_EMAIL_NOTIFICATION_ENCRYPT_KEY",
    "GOOGLE_RECAPTCHA_SECRET": "SR_GOOGLE_RECAPTCHA_SECRET",
    "JDBC_URL": "SR_GCP_PROD_DB_01_JDBC_URL",
    "JDBC_USER": "SR_GCP_PROD_DB_01_JDBC_USER",
    "JDBC_PASS": "SR_GCP_PROD_DB_01_JDBC_PASS",
    "MBL_API_KEY": "SR_MBL_API_KEY",
    "PLAY_SESSION_DOMAIN": "SR_PLAY_SESSION_DOMAIN",
    "PLAY_SESSION_MAX_AGE": "SR_PLAY_SESSION_MAX_AGE",
    "PLAY_SESSION_SECURE": "SR_PLAY_SESSION_SECURE",
    "RABBITMQ_HOST": "SR_RABBITMQ_HOST",
    "RABBITMQ_USERNAME": "SR_RABBITMQ_USERNAME",
    "RABBITMQ_PASSWORD": "SR_RABBITMQ_PASSWORD",
    "RABBITMQ_VIRTUALHOST": "SR_RABBITMQ_VIRTUALHOST",
    "RABBITMQ_PREFIX": "SR_RABBITMQ_PREFIX",
    "REDIS_URI": "SR_REDIS_URI",
    "REDIS_KEY_PREFIX": "SR_REDIS_KEY_PREFIX",
    "STRIPE_API_KEY": "SR_STRIPE_API_KEY",
    "STRIPE_PUBLISHABLE_KEY": "STRIPE_PUBLISHABLE_KEY",
    "MS_CLIENT_ID": "SR_MS_CLIENT_ID",
    "MS_CLIENT_SECRET": "SR_MS_CLIENT_SECRET",
    "HUBSPOT_CLIENT_ID": "SR_HUBSPOT_CLIENT_ID",
    "HUBSPOT_CLIENT_SECRET": "SR_HUBSPOT_CLIENT_SECRET",
    "ZOHO_CLIENT_ID": "SR_ZOHO_CLIENT_ID",
    "ZOHO_CLIENT_SECRET": "SR_ZOHO_CLIENT_SECRET",
    "PIPEDRIVE_CLIENT_ID": "SR_PIPEDRIVE_CLIENT_ID",
    "PIPEDRIVE_CLIENT_SECRET": "SR_PIPEDRIVE_CLIENT_SECRET",
    "SALESFORCE_CLIENT_ID": "SR_SALESFORCE_CLIENT_ID",
    "SALESFORCE_CLIENT_SECRET": "SR_SALESFORCE_CLIENT_SECRET",
    "FIRSTPROMOTER_APIKEY": "SR_FIRSTPROMOTER_APIKEY",
    "FIRSTPROMOTER_WID": "SR_FIRSTPROMOTER_WID",
    "FIRSTPROMOTER_REFERRAL_CAMPAIGN_ID": "SR_FIRSTPROMOTER_REFERRAL_CAMPAIGN_ID",
    "ADMITAD_POSTBACK_KEY": "SR_ADMITAD_POSTBACK_KEY",
    "ADMITAD_CAMPAIGN_CODE": "SR_ADMITAD_CAMPAIGN_CODE",
    "ADMITAD_REVISION_SECRET_KEY": "SR_ADMITAD_REVISION_SECRET_KEY",
    "BOUNCER_API_KEY": "SR_BOUNCER_API_KEY",
    "DEBOUNCE_API_KEY": "SR_DEBOUNCE_API_KEY",
    "SR_LISTCLEAN_API_KEY": "SR_LISTCLEAN_API_KEY",
    "SR_USERCHECKDOTCOM_API_KEY": "SR_USERCHECKDOTCOM_API_KEY",
    "MXTOOLBOX_API_KEY": "MXTOOLBOX_API_KEY",
    "BILLING_APP_SMARTREACH_API_KEY": "BILLING_APP_SMARTREACH_API_KEY",
    "SR_BILLING_APP_API_URL": "SR_BILLING_APP_API_URL",
    "SR_WHOIS_APILAYER_API_KEY": "SR_WHOIS_APILAYER_API_KEY",
    "SR_WHOIS_RAPIDAPI_API_KEY": "SR_WHOIS_RAPIDAPI_API_KEY",
    "SR_WHOIS_RAPIDAPI_HOST_KEY": "SR_WHOIS_RAPIDAPI_HOST_KEY",
    "CLOUDFLARE_AUTH_KEY": "SR_CLOUDFLARE_AUTH_KEY",
    "CLOUDFLARE_AUTH_EMAIL": "SR_CLOUDFLARE_AUTH_EMAIL",
    "SR_PUSHER_APP_ID": "SR_PUSHER_APP_ID",
    "SR_PUSHER_KEY": "SR_PUSHER_KEY",
    "SR_PUSHER_SECRET": "SR_PUSHER_SECRET",
    "SR_PUSHER_CLUSTER": "SR_PUSHER_CLUSTER",
    "SR_INTERCOM_ACCESS_TOKEN": "SR_INTERCOM_ACCESS_TOKEN",
    "SR_PROMETHEUS_PUSHGATEWAY": "SR_PROMETHEUS_PUSHGATEWAY",
    "SR_TWILIO_ACCOUNT_SID": "SR_TWILIO_ACCOUNT_SID",
    "SR_TWILIO_TWIML_APP_SID": "SR_TWILIO_TWIML_APP_SID",
    "SR_TWILIO_API_KEY": "SR_TWILIO_API_KEY",
    "SR_TWILIO_API_SECRET": "SR_TWILIO_API_SECRET",
    "SR_TWILIO_AUTH_TOKEN": "SR_TWILIO_AUTH_TOKEN",
    "SR_ENC_KEY_NATIVE_CALLING_API_KEY": "SR_ENC_KEY_NATIVE_CALLING_API_KEY",
    "BRIGHT_DATA_API_KEY": "BRIGHT_DATA_API_KEY",
    "SR_GPT_API_KEY": "SR_GPT_API_KEY",
    "SR_GEMINI_API_KEY": "SR_GEMINI_API_KEY",
    "SMARTREACH_MAILDOSO_API_KEY": "SMARTREACH_MAILDOSO_API_KEY",
    "SR_API_BASE_URL": "SR_API_BASE_URL",
    "SR_COMPANIES_API_API_KEY": "SR_COMPANIES_API_API_KEY",
    "MEETINGS_BASE_URL": "MEETINGS_BASE_URL",
    "SR_CALENDAR_APP_KEY": "SR_CALENDAR_APP_KEY",
    "SR_CALENDAR_LINK_ENC_KEY": "SR_CALENDAR_LINK_ENC_KEY",
    "MEETINGS_CLIENT_ID": "MEETINGS_CLIENT_ID",
    "MEETINGS_CLIENT_SECRET": "MEETINGS_CLIENT_SECRET",
    "DEBOUNCE_BUCKET_SECRET": "DEBOUNCE_BUCKET_SECRET",
    "SMARTREACH_CLIENT_ID": "SMARTREACH_CLIENT_ID",
    "SMARTREACH_CLIENT_SECRET": "SMARTREACH_CLIENT_SECRET",
    "COMMON_AUTH_HYDRA_ADMIN_API": "COMMON_AUTH_HYDRA_ADMIN_API",
    "SR_API_LAYER_CURRENCY_API_KEY": "SR_API_LAYER_CURRENCY_API_KEY",
    "COMMON_AUTH_BILLING_CLIENT_ID": "COMMON_AUTH_BILLING_CLIENT_ID",
    "COMMON_AUTH_BILLING_CLIENT_SECRET": "COMMON_AUTH_BILLING_CLIENT_SECRET",
    "PD_ANY_MAIL_FINDER_API_KEY": "ANY_MAIL_FINDER_API_KEY",
    # Add any additional secrets as needed
    "SR_ZAPMAIL_API_KEY": "SR_ZAPMAIL_API_KEY",
    "SR_ZAPMAIL_USER_ID": "SR_ZAPMAIL_USER_ID",
    "SR_WARMUPHERO_API_KEY":  "SR_WARMUPHERO_API_KEY",
    "SR_CAPTAIN_DATA_API_KEY": "SR_CAPTAIN_DATA_API_KEY",
    "SR_CAPTAIN_DATA_PROJECT_ID": "SR_CAPTAIN_DATA_PROJECT_ID",
    "SR_CD_VIEW_PROFILE_WORKFLOW_UID": "SR_CD_VIEW_PROFILE_WORKFLOW_UID",
    "SR_CD_VIEW_PROFILE_STEP_UID": "SR_CD_VIEW_PROFILE_STEP_UID",
    "SR_CD_SEND_CONNECTION_WORKFLOW_UID": "SR_CD_SEND_CONNECTION_WORKFLOW_UID",
    "SR_CD_SEND_CONNECTION_STEP_UID": "SR_CD_SEND_CONNECTION_STEP_UID",
    "SR_CD_EXTRACT_CONNECTIONS_WORKFLOW_UID": "SR_CD_EXTRACT_CONNECTIONS_WORKFLOW_UID",
    "SR_CD_EXTRACT_CONNECTIONS_STEP_UID": "SR_CD_EXTRACT_CONNECTIONS_STEP_UID",
    "SR_CD_SEND_MESSAGE_WORKFLOW_UID": "SR_CD_SEND_MESSAGE_WORKFLOW_UID",
    "SR_CD_SEND_MESSAGE_STEP_UID": "SR_CD_SEND_MESSAGE_STEP_UID",
    "SR_CD_RETRIEVE_MESSAGE_WORKFLOW_UID": "SR_CD_RETRIEVE_MESSAGE_WORKFLOW_UID",
    "SR_CD_RETRIEVE_MESSAGE_STEP_UID": "SR_CD_RETRIEVE_MESSAGE_STEP_UID",
    "SR_CD_RETRIEVE_CONVERSATIONS_WORKFLOW_UID": "SR_CD_RETRIEVE_CONVERSATIONS_WORKFLOW_UID",
    "SR_CD_RETRIEVE_CONVERSATIONS_STEP_UID": "SR_CD_RETRIEVE_CONVERSATIONS_STEP_UID",
    "SR_CALENDLY_CLIENT_ID":"SR_CALENDLY_CLIENT_ID",
    "SR_CALENDLY_CLIENT_SECRET":"SR_CALENDLY_CLIENT_SECRET",
    "SR_CALENDLY_WEBHOOK_SIGNING_KEY":"SR_CALENDLY_WEBHOOK_SIGNING_KEY"

}

# Parse command-line arguments
parser = argparse.ArgumentParser(description="Fetch secrets from Google Cloud Secret Manager and export them.")
parser.add_argument("--override", action="append", help="Override specific secrets in the form env_var=secret_name")
args = parser.parse_args()

# Apply overrides
if args.override:
    for override in args.override:
        try:
            env_var, secret_name = override.split("=")
            if env_var in env_to_secret:
                env_to_secret[env_var] = secret_name
            else:
                print(f"Warning: {env_var} not found in base mapping.", file=sys.stderr)
        except ValueError:
            print(f"Invalid override format: {override}. Expected env_var=secret_name", file=sys.stderr)

# Function to fetch a secret
def fetch_secret(env_var, secret_name):
    try:
        name = f"projects/{project_id}/secrets/{secret_name}/versions/latest"
        response = client.access_secret_version(name=name)
        value = response.payload.data.decode("UTF-8")
        return env_var, value
    except Exception as e:
        print(f"# Warning: Failed to fetch {secret_name} for {env_var}: {e}", file=sys.stderr)
        return None, None

# Fetch secrets concurrently
with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
    futures = {
        executor.submit(fetch_secret, env_var, secret_name): env_var
        for env_var, secret_name in env_to_secret.items()
    }
    for future in concurrent.futures.as_completed(futures):
        env_var, value = future.result()
        if env_var and value:
            print(f"export {env_var}='{value}'")
