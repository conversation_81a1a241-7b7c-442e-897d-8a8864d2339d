#!/bin/bash
prefix="/tmp/sr_scrt_"

#usage()
#{
#	echo "Usage: load_secrets_worker_v2_1.sh  [ -s | --server main-worker ]"
#	exit 0
#}
#
## getopt - after a paramter - no colon means does not take any addition params
##                             single colon means takes a param compulsory
#PARSED_ARGUMENTS=$(getopt -a -n deploy-build.sh -o s:d --longoptions server:,deploy:  -- "$@")
#VALID_ARGUMENTS=$?




# ======================== start
ulimit -n 5120
echo "set ulimit to 5120 ... it really now is ..."
ulimit -a
declare -A secret_key_values_assoc_arr

function load_secrets_into_assoc_arr {


	#APPLICATION
	secret_key_values_assoc_arr[SR_PLAY_CRYPTO_SECRET]=APPLICATION_SECRET
	secret_key_values_assoc_arr[SR_APP_DOMAIN]=APP_DOMAIN

	#ENC_KEYS
	secret_key_values_assoc_arr[SR_ENC_KEY_EMAIL_SETTINGS_CREDENTIAL]=SR_ENC_KEY_EMAIL_SETTINGS_CREDENTIAL
	secret_key_values_assoc_arr[SR_ENC_KEY_EMAIL_DATAPLATFORM_CREDENTIAL]=SR_ENC_KEY_EMAIL_DATAPLATFORM_CREDENTIAL

	secret_key_values_assoc_arr[SR_ENC_KEY_WORKFLOW_CRM_SETTINGS_CREDENTIAL]=SR_ENC_KEY_WORKFLOW_CRM_SETTINGS_CREDENTIAL

	secret_key_values_assoc_arr[SR_ENC_KEY_ACCOUNTS_CREDENTIAL]=SR_ENC_KEY_ACCOUNTS_CREDENTIAL

	secret_key_values_assoc_arr[SR_ENC_KEY_DKIM_RECORDS_CREDENTIAL]=SR_ENC_KEY_DKIM_RECORDS_CREDENTIAL

	secret_key_values_assoc_arr[SR_ENC_KEY_TEAMS_ACCOUNTS_CREDENTIAL]=SR_ENC_KEY_TEAMS_ACCOUNTS_CREDENTIAL

	secret_key_values_assoc_arr[SR_ENC_KEY_ORGANIZATIONS_CREDENTIAL]=SR_ENC_KEY_ORGANIZATIONS_CREDENTIAL

	secret_key_values_assoc_arr[SR_ENC_KEY_PHANTOMBUSTER_API_KEY]=SR_ENC_KEY_PHANTOMBUSTER_API_KEY

	secret_key_values_assoc_arr[SR_ENC_KEY_PHANTOMBUSTER_WEBHOOK_TEAM_SECRET]=SR_ENC_KEY_PHANTOMBUSTER_WEBHOOK_TEAM_SECRET

	secret_key_values_assoc_arr[SR_ENC_KEY_LINKEDIN_SESSION_COOKIE]=SR_ENC_KEY_LINKEDIN_SESSION_COOKIE
	
	secret_key_values_assoc_arr[SR_ENC_KEY_PHANTOMBUSTER_PROXY_PASSWORD]=SR_ENC_KEY_PHANTOMBUSTER_PROXY_PASSWORD
	
	secret_key_values_assoc_arr[SR_ENC_KEY_LINKEDIN_ACCOUNT_PASSWORD]=SR_ENC_KEY_LINKEDIN_ACCOUNT_PASSWORD
	
	secret_key_values_assoc_arr[SR_ENC_KEY_CALENDLY_SETTINGS_CREDENTIAL]=SR_ENC_KEY_CALENDLY_SETTINGS_CREDENTIAL


	
	#AWS
	secret_key_values_assoc_arr[SR_AWS_ACCESS_KEY_ID]=AWS_ACCESS_KEY_ID
	secret_key_values_assoc_arr[SR_AWS_SECRET_ACCESS_KEY]=AWS_SECRET_ACCESS_KEY

	secret_key_values_assoc_arr[SR_EMAIL_MESSAGE_ID_SUFFIX]=EMAIL_MESSAGE_ID_SUFFIX
	secret_key_values_assoc_arr[SR_APPLICATION_ENCRYPTION_KEY]=ENCRYPTION_KEY
	secret_key_values_assoc_arr[SR_EMAIL_NOTIFICATION_ENCRYPT_KEY]=EMAIL_NOTIFICATION_ENCRYPT_KEY

	#GOOGLE
#	secret_key_values_assoc_arr[SR_GOOGLE_CLIENT_ID]=GOOGLE_CLIENT_ID
#	secret_key_values_assoc_arr[SR_GOOGLE_CLIENT_SECRET]=GOOGLE_CLIENT_SECRET
	secret_key_values_assoc_arr[SR_GOOGLE_RECAPTCHA_SECRET]=GOOGLE_RECAPTCHA_SECRET

	#DB
	secret_key_values_assoc_arr[SR_GCP_PROD_DB_01_JDBC_URL]=JDBC_URL
	secret_key_values_assoc_arr[SR_GCP_PROD_DB_01_JDBC_USER]=JDBC_USER
	secret_key_values_assoc_arr[SR_GCP_PROD_DB_01_JDBC_PASS]=JDBC_PASS

	secret_key_values_assoc_arr[SR_MBL_API_KEY]=MBL_API_KEY

	#PLAY SESSION
	secret_key_values_assoc_arr[SR_PLAY_SESSION_DOMAIN]=PLAY_SESSION_DOMAIN
	secret_key_values_assoc_arr[SR_PLAY_SESSION_MAX_AGE]=PLAY_SESSION_MAX_AGE
	secret_key_values_assoc_arr[SR_PLAY_SESSION_SECURE]=PLAY_SESSION_SECURE

	#RABBITMQ
	secret_key_values_assoc_arr[SR_RABBITMQ_HOST]=RABBITMQ_HOST
	secret_key_values_assoc_arr[SR_RABBITMQ_USERNAME]=RABBITMQ_USERNAME
	secret_key_values_assoc_arr[SR_RABBITMQ_PASSWORD]=RABBITMQ_PASSWORD
	secret_key_values_assoc_arr[SR_RABBITMQ_VIRTUALHOST]=RABBITMQ_VIRTUALHOST
	secret_key_values_assoc_arr[SR_RABBITMQ_PREFIX]=RABBITMQ_PREFIX

	#REDIS
	secret_key_values_assoc_arr[SR_REDIS_URI]=REDIS_URI
	secret_key_values_assoc_arr[SR_REDIS_KEY_PREFIX]=REDIS_KEY_PREFIX

	#STRIPE
	secret_key_values_assoc_arr[SR_STRIPE_API_KEY]=STRIPE_API_KEY
	secret_key_values_assoc_arr[STRIPE_PUBLISHABLE_KEY]=STRIPE_PUBLISHABLE_KEY



	#MICROSOFT
	secret_key_values_assoc_arr[SR_MS_CLIENT_ID]=MS_CLIENT_ID
	secret_key_values_assoc_arr[SR_MS_CLIENT_SECRET]=MS_CLIENT_SECRET

	#HUBSPOT
	secret_key_values_assoc_arr[SR_HUBSPOT_CLIENT_ID]=HUBSPOT_CLIENT_ID
	secret_key_values_assoc_arr[SR_HUBSPOT_CLIENT_SECRET]=HUBSPOT_CLIENT_SECRET

	#ZOHO
	secret_key_values_assoc_arr[SR_ZOHO_CLIENT_ID]=ZOHO_CLIENT_ID
	secret_key_values_assoc_arr[SR_ZOHO_CLIENT_SECRET]=ZOHO_CLIENT_SECRET

	#PIPEDRIVE
	secret_key_values_assoc_arr[SR_PIPEDRIVE_CLIENT_ID]=PIPEDRIVE_CLIENT_ID
	secret_key_values_assoc_arr[SR_PIPEDRIVE_CLIENT_SECRET]=PIPEDRIVE_CLIENT_SECRET

	#SALESFORCE
	secret_key_values_assoc_arr[SR_SALESFORCE_CLIENT_ID]=SALESFORCE_CLIENT_ID
	secret_key_values_assoc_arr[SR_SALESFORCE_CLIENT_SECRET]=SALESFORCE_CLIENT_SECRET
	
	#CALENDLY
	secret_key_values_assoc_arr[SR_CALENDLY_CLIENT_ID]=CALENDLY_CLIENT_ID
  secret_key_values_assoc_arr[SR_CALENDLY_CLIENT_SECRET]=CALENDLY_CLIENT_SECRET
  secret_key_values_assoc_arr[SR_CALENDLY_WEBHOOK_SIGNING_KEY]=CALENDLY_WEBHOOK_SIGNING_KEY


	#FIRSTPROMOTER
	secret_key_values_assoc_arr[SR_FIRSTPROMOTER_APIKEY]=FIRSTPROMOTER_APIKEY
	secret_key_values_assoc_arr[SR_FIRSTPROMOTER_WID]=FIRSTPROMOTER_WID
	secret_key_values_assoc_arr[SR_FIRSTPROMOTER_REFERRAL_CAMPAIGN_ID]=FIRSTPROMOTER_REFERRAL_CAMPAIGN_ID

	# ADMITAD
	secret_key_values_assoc_arr[SR_ADMITAD_POSTBACK_KEY]=ADMITAD_POSTBACK_KEY
	secret_key_values_assoc_arr[SR_ADMITAD_CAMPAIGN_CODE]=ADMITAD_CAMPAIGN_CODE
	secret_key_values_assoc_arr[SR_ADMITAD_REVISION_SECRET_KEY]=ADMITAD_REVISION_SECRET_KEY




	#BOUNCER
	secret_key_values_assoc_arr[SR_BOUNCER_API_KEY]=BOUNCER_API_KEY

	#DEBOUNCE
	secret_key_values_assoc_arr[SR_DEBOUNCE_API_KEY]=DEBOUNCE_API_KEY

	#LISTCLEAN
	secret_key_values_assoc_arr[SR_LISTCLEAN_API_KEY]=SR_LISTCLEAN_API_KEY

	#USERCHECK
	secret_key_values_assoc_arr[SR_USERCHECKDOTCOM_API_KEY]=SR_USERCHECKDOTCOM_API_KEY


	#MXTOOLBOX
	secret_key_values_assoc_arr[MXTOOLBOX_API_KEY]=MXTOOLBOX_API_KEY

	#BILLING_APP
	secret_key_values_assoc_arr[BILLING_APP_SMARTREACH_API_KEY]=BILLING_APP_SMARTREACH_API_KEY
	secret_key_values_assoc_arr[SR_BILLING_APP_API_URL]=SR_BILLING_APP_API_URL

	#WHOIS API LAYER
	secret_key_values_assoc_arr[SR_WHOIS_APILAYER_API_KEY]=SR_WHOIS_APILAYER_API_KEY

	#WHOIS RAPID API LAYER
	secret_key_values_assoc_arr[SR_WHOIS_RAPIDAPI_API_KEY]=SR_WHOIS_RAPIDAPI_API_KEY
	secret_key_values_assoc_arr[SR_WHOIS_RAPIDAPI_HOST_KEY]=SR_WHOIS_RAPIDAPI_HOST_KEY

	#CLOUDFLARE
	secret_key_values_assoc_arr[SR_CLOUDFLARE_AUTH_KEY]=CLOUDFLARE_AUTH_KEY
	secret_key_values_assoc_arr[SR_CLOUDFLARE_AUTH_EMAIL]=CLOUDFLARE_AUTH_EMAIL

	#PUSHER API LAYER
	secret_key_values_assoc_arr[SR_PUSHER_APP_ID]=SR_PUSHER_APP_ID
	secret_key_values_assoc_arr[SR_PUSHER_KEY]=SR_PUSHER_KEY
	secret_key_values_assoc_arr[SR_PUSHER_SECRET]=SR_PUSHER_SECRET
	secret_key_values_assoc_arr[SR_PUSHER_CLUSTER]=SR_PUSHER_CLUSTER


	#SCYLLA

	#secret_key_values_assoc_arr[SR_SCYLLA_KEYSPACE]=SR_SCYLLA_KEYSPACE

	#secret_key_values_assoc_arr[SR_SCYLLA_NODE_AND_PORTS]=SR_SCYLLA_NODE_AND_PORTS

	#secret_key_values_assoc_arr[SR_SCYLLA_USERNAME]=SR_SCYLLA_USERNAME
	#secret_key_values_assoc_arr[SR_SCYLLA_PASSWORD]=SR_SCYLLA_PASSWORD


	#KAFKA
	#secret_key_values_assoc_arr[SR_KAFKA_SERVER_URL]=SR_KAFKA_SERVER_URL


	#INTERCOM
	secret_key_values_assoc_arr[SR_INTERCOM_ACCESS_TOKEN]=SR_INTERCOM_ACCESS_TOKEN


	#PROMETHEUS
	secret_key_values_assoc_arr[SR_PROMETHEUS_PUSHGATEWAY]=SR_PROMETHEUS_PUSHGATEWAY

	#TWILIO
	secret_key_values_assoc_arr[SR_TWILIO_ACCOUNT_SID]=SR_TWILIO_ACCOUNT_SID
	secret_key_values_assoc_arr[SR_TWILIO_TWIML_APP_SID]=SR_TWILIO_TWIML_APP_SID
	secret_key_values_assoc_arr[SR_TWILIO_API_KEY]=SR_TWILIO_API_KEY
	secret_key_values_assoc_arr[SR_TWILIO_API_SECRET]=SR_TWILIO_API_SECRET
	secret_key_values_assoc_arr[SR_TWILIO_AUTH_TOKEN]=SR_TWILIO_AUTH_TOKEN
	secret_key_values_assoc_arr[SR_ENC_KEY_NATIVE_CALLING_API_KEY]=SR_ENC_KEY_NATIVE_CALLING_API_KEY

  #Bright Data
	secret_key_values_assoc_arr[BRIGHT_DATA_API_KEY]=BRIGHT_DATA_API_KEY

	#GPT
	secret_key_values_assoc_arr[SR_GPT_API_KEY]=SR_GPT_API_KEY

	#Google Gemini
  secret_key_values_assoc_arr[SR_GEMINI_API_KEY]=SR_GEMINI_API_KEY

	#MAILDOSO
	secret_key_values_assoc_arr[SMARTREACH_MAILDOSO_API_KEY]=SMARTREACH_MAILDOSO_API_KEY

	#ZAPMAIL
	secret_key_values_assoc_arr[SR_ZAPMAIL_API_KEY]=SR_ZAPMAIL_API_KEY
	secret_key_values_assoc_arr[SR_ZAPMAIL_USER_ID]=SR_ZAPMAIL_USER_ID

	#CAPTAIN_DATA
	secret_key_values_assoc_arr[SR_CAPTAIN_DATA_API_KEY]=SR_CAPTAIN_DATA_API_KEY
	secret_key_values_assoc_arr[SR_CAPTAIN_DATA_PROJECT_ID]=SR_CAPTAIN_DATA_PROJECT_ID
	secret_key_values_assoc_arr[SR_CD_VIEW_PROFILE_WORKFLOW_UID]=SR_CD_VIEW_PROFILE_WORKFLOW_UID
	secret_key_values_assoc_arr[SR_CD_VIEW_PROFILE_STEP_UID]=SR_CD_VIEW_PROFILE_STEP_UID
	secret_key_values_assoc_arr[SR_CD_SEND_CONNECTION_WORKFLOW_UID]=SR_CD_SEND_CONNECTION_WORKFLOW_UID
	secret_key_values_assoc_arr[SR_CD_SEND_CONNECTION_STEP_UID]=SR_CD_SEND_CONNECTION_STEP_UID
	secret_key_values_assoc_arr[SR_CD_EXTRACT_CONNECTIONS_WORKFLOW_UID]=SR_CD_EXTRACT_CONNECTIONS_WORKFLOW_UID
	secret_key_values_assoc_arr[SR_CD_EXTRACT_CONNECTIONS_STEP_UID]=SR_CD_EXTRACT_CONNECTIONS_STEP_UID
	secret_key_values_assoc_arr[SR_CD_SEND_MESSAGE_WORKFLOW_UID]=SR_CD_SEND_MESSAGE_WORKFLOW_UID
	secret_key_values_assoc_arr[SR_CD_SEND_MESSAGE_STEP_UID]=SR_CD_SEND_MESSAGE_STEP_UID
	secret_key_values_assoc_arr[SR_CD_RETRIEVE_MESSAGE_WORKFLOW_UID]=SR_CD_RETRIEVE_MESSAGE_WORKFLOW_UID
	secret_key_values_assoc_arr[SR_CD_RETRIEVE_MESSAGE_STEP_UID]=SR_CD_RETRIEVE_MESSAGE_STEP_UID
	secret_key_values_assoc_arr[SR_CD_RETRIEVE_CONVERSATIONS_WORKFLOW_UID]=SR_CD_RETRIEVE_CONVERSATIONS_WORKFLOW_UID
	secret_key_values_assoc_arr[SR_CD_RETRIEVE_CONVERSATIONS_STEP_UID]=SR_CD_RETRIEVE_CONVERSATIONS_STEP_UID
	

	#BASE_API_URL
	secret_key_values_assoc_arr[SR_API_BASE_URL]=SR_API_BASE_URL

	#THE_COMPANIES_API
	secret_key_values_assoc_arr[SR_COMPANIES_API_API_KEY]=SR_COMPANIES_API_API_KEY


	#SMARTREACH_MEETINGS - Calendar
	secret_key_values_assoc_arr[MEETINGS_BASE_URL]=MEETINGS_BASE_URL
	secret_key_values_assoc_arr[SR_CALENDAR_APP_KEY]=SR_CALENDAR_APP_KEY
	secret_key_values_assoc_arr[SR_CALENDAR_LINK_ENC_KEY]=SR_CALENDAR_LINK_ENC_KEY
	secret_key_values_assoc_arr[MEETINGS_CLIENT_ID]=MEETINGS_CLIENT_ID
	secret_key_values_assoc_arr[MEETINGS_CLIENT_SECRET]=MEETINGS_CLIENT_SECRET

  #WARMUPHERO
  secret_key_values_assoc_arr[SR_WARMUPHERO_API_KEY]=SR_WARMUPHERO_API_KEY


	#DEBOUNCE_BUCKET
	secret_key_values_assoc_arr[DEBOUNCE_BUCKET_SECRET]=DEBOUNCE_BUCKET_SECRET


	secret_key_values_assoc_arr[SMARTREACH_CLIENT_ID]=SMARTREACH_CLIENT_ID
	secret_key_values_assoc_arr[SMARTREACH_CLIENT_SECRET]=SMARTREACH_CLIENT_SECRET
	secret_key_values_assoc_arr[COMMON_AUTH_HYDRA_ADMIN_API]=COMMON_AUTH_HYDRA_ADMIN_API


	#API_LAYER_CURRENCY
	secret_key_values_assoc_arr[SR_API_LAYER_CURRENCY_API_KEY]=SR_API_LAYER_CURRENCY_API_KEY

	#SMARTREACH_USING_COMMON_AUTH not required now
#	secret_key_values_assoc_arr[SMARTREACH_COMMON_AUTH_ENC_KEY]=SMARTREACH_COMMON_AUTH_ENC_KEY
#	secret_key_values_assoc_arr[SMARTREACH_COMMON_AUTH_API_KEY]=SMARTREACH_COMMON_AUTH_API_KEY

  #BILLING_USING_COMMON_AUTH
  secret_key_values_assoc_arr[COMMON_AUTH_BILLING_CLIENT_ID]=COMMON_AUTH_BILLING_CLIENT_ID
  secret_key_values_assoc_arr[COMMON_AUTH_BILLING_CLIENT_SECRET]=COMMON_AUTH_BILLING_CLIENT_SECRET


	#PROSPECTDADDY
	secret_key_values_assoc_arr[ANY_MAIL_FINDER_API_KEY]=PD_ANY_MAIL_FINDER_API_KEY

	# ======================== end


	# clean up all the tmp scrt files before executing - for previous run residue cleanup
	for key in "${!secret_key_values_assoc_arr[@]}"; do
		tmp1=$prefix"${secret_key_values_assoc_arr[$key]}" 
		#export ${secret_key_values_assoc_arr[$key]}=$(< $prefix"{secret_key_values_assoc_arr[$key]}")
		#rm $prefix"{secret_key_values_assoc_arr[$key]}"
		rm $tmp1 || true
	done

	wait

	#export v1="dummy"
	#echo "env before: "
	#env
	COUNTER=1
	for key in "${!secret_key_values_assoc_arr[@]}"; do
		echo "${key} => ${secret_key_values_assoc_arr[$key]}"
		# step 1 pull the secrets in parallel to an named file
		COUNTER=$(( COUNTER + 1 ))
		gcloud secrets versions access latest --secret=${key} > $prefix"${secret_key_values_assoc_arr[$key]}" &
		if [[ $(( COUNTER % 25 )) == 0 ]]; then 
			echo "waiting for secrets load jobs to complete counter $COUNTER"
			wait
		fi
		##1nov2023 APPLICATION_SECRET=$(< $prefix'APPLICATION_SECRET')
		#eval ${secret_key_values_assoc_arr[$key]}=${secret_key_values_assoc_arr[$key]}
		#declare -g ${secret_key_values_assoc_arr[$key]}=${secret_key_values_assoc_arr[$key]}
		#export ${secret_key_values_assoc_arr[$key]}
		#export ${secret_key_values_assoc_arr[$key]}=${secret_key_values_assoc_arr[$key]}
		#declare ${secret_key_values_assoc_arr[$key]}=${secret_key_values_assoc_arr[$key]}
		#echo ${secret_key_values_assoc_arr[$key]}
	done
	# note SPECIAL handling of this secret FS_PRIVATE_KEY - not dumped to a file
	# commented-out on 13-Jan-2024, no longer needed here
	# export FS_PRIVATE_KEY=`gcloud secrets versions access latest --secret=SR_FS_PRIVATE_KEY`
	echo "waiting for secrets load jobs to complete counter $COUNTER"
	wait

	for key in "${!secret_key_values_assoc_arr[@]}"; do
		tmp1=$prefix"${secret_key_values_assoc_arr[$key]}" 
		echo "exporting key $tmp1"
		#export ${secret_key_values_assoc_arr[$key]}=$(<$prefix"${secret_key_values_assoc_arr[$key]}")
		#export ${secret_key_values_assoc_arr[$key]}=$(<$tmp1)
		#read ${secret_key_values_assoc_arr[$key]} < $tmp1
		#export ${secret_key_values_assoc_arr[$key]} 
		#export  ${secret_key_values_assoc_arr[$key]}=`cat $tmp1` 
		export  ${secret_key_values_assoc_arr[$key]}=`tr '\n' ' ' <$tmp1` 
	done

	wait

	# clean up all the tmp scrt files
	for key in "${!secret_key_values_assoc_arr[@]}"; do
		tmp1=$prefix"${secret_key_values_assoc_arr[$key]}" 
		#export ${secret_key_values_assoc_arr[$key]}=$(< $prefix"{secret_key_values_assoc_arr[$key]}")
		#rm $prefix"{secret_key_values_assoc_arr[$key]}"
		rm $tmp1
	done
	#echo "env after: "
	#env

}

load_secrets_into_assoc_arr

