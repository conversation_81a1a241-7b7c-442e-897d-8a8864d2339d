import puppeteer from "puppeteer";
import process from "process";

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

await (async () => {
  try {
    const username = process.argv[2];
    const password = process.argv[3];
    const proxyUrl = process.argv[4];
    const proxyUsername = process.argv[5];
    const proxyPassword = process.argv[6];
    const screenshotFilePath = process.argv[7];
    const userAgent = process.argv[8];

    console.log(`Node process started`);

    const browser = await puppeteer.launch({
      executablePath: '/usr/bin/chromium-browser',
      headless: 'new',
      args: [
        `--proxy-server=${proxyUrl}`,
        '--disable-features=IsolateOrigins,site-per-process', // Helps with iframe handling
        '--no-sandbox',
        '--disable-setuid-sandbox'
      ]
    });

    console.log("Browser Opened");

    const page = await browser.newPage();

    // Increase timeouts
    page.setDefaultNavigationTimeout(90000); // 90 seconds
    page.setDefaultTimeout(30000); // 30 seconds for other operations

    console.log("New Page Created");

    await page.authenticate({
      username: proxyUsername,
      password: proxyPassword,
    });

    console.log("Proxy Authenticated");

    // Set custom user agent
    await page.setUserAgent(userAgent);

    // Add additional headers to appear more browser-like
    await page.setExtraHTTPHeaders({
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
    });

    console.log("User agent and headers set");

    // Wait longer for initial page load
    await page.goto("https://www.linkedin.com/checkpoint/rm/sign-in-another-account", {
      waitUntil: "networkidle2",
      timeout: 60000
    });

    console.log("LinkedIn Page loaded");

    // Wait for form elements to be properly loaded
    await page.waitForSelector('#username', { visible: true });
    await page.waitForSelector('#password', { visible: true });

    // Clear fields before typing
    await page.$eval('#username', el => el.value = '');
    await page.$eval('#password', el => el.value = '');

    // Type with random delays to appear more human-like
    await page.type("#username", username, { delay: Math.floor(Math.random() * 100) + 50 });
    await sleep(1000);
    await page.type("#password", password, { delay: Math.floor(Math.random() * 100) + 50 });
    await sleep(1000);

    // Click the sign-in button instead of pressing Enter
    const signInButton = await page.$('button[type="submit"]');
    if (signInButton) {
      await signInButton.click();
    } else {
      await page.keyboard.press("Enter");
    }

    console.log("Login button clicked");

    // Wait for navigation and check for different possible outcomes
    try {
      await Promise.race([
        page.waitForNavigation({ timeout: 30000 }),
        page.waitForSelector('[data-error-code]', { timeout: 30000 }), // Error message selector
        page.waitForSelector('#captcha-internal', { timeout: 30000 }) // Captcha selector
      ]);
    } catch (error) {
      console.log("Navigation timeout or error:", error.message);
    }

    // Check for error messages
    const errorElement = await page.$('[data-error-code]');
    if (errorElement) {
      const errorMessage = await page.evaluate(el => el.textContent, errorElement);
      console.error(`Login error: ${errorMessage}`);
      await page.screenshot({ path: screenshotFilePath, fullPage: true });
      await browser.close();
      throw new Error(`LinkedIn login failed: ${errorMessage}`);
    }

    // Save cookies
    const cookies = await page.cookies();
    let c_value = "";
    cookies.forEach(c => {
      if (c.name === "li_at") {
        c_value = c.value;
      }
    });

    if (c_value.trim() === "") {
      console.log("Cookie not found - taking screenshot");
      await page.screenshot({ path: screenshotFilePath, fullPage: true });
      throw new Error("LinkedIn cookie 'li_at' not found");
    }

    console.log(`cookie__${c_value}`);
    await browser.close();
    return c_value;

  } catch (error) {
    console.error("Script error:", error.message);
    throw error;
  }
})();