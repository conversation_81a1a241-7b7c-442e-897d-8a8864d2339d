console.log("Get Next Step Node Process Started")

const nodes = JSON.parse(process.argv[2])
const edges = JSON.parse(process.argv[3])
const parent_node_id = process.argv[4]
const previousCondition = process.argv[5]

console.log(nodes, edges, parent_node_id)

function findChildNodesOfParent(node_id, condition) {
    edges.filter(e => e.source === node_id)
    .map(e => {
        const node = nodes.find(n => n.id === e.target)
        if (node.data.type === "step") {
            console.log(`stepWithCondition_____${node.id}____${condition}___${e.label}`)
        }
        else {
            let allConditions = ""
            if (condition.trim().length > 0) {
                allConditions = `${condition}___${e.label}____${node.data.label}`
            }
            else {
                allConditions = node.data.label
            }
            findChildNodesOfParent(node.id, allConditions)
        }
    })
}

findChildNodesOfParent(parent_node_id, previousCondition)