# --- !Ups

ALTER TABLE email_settings DROP CONSTRAINT IF EXISTS email_settings_account_id_email_key;

CREATE UNIQUE INDEX IF NOT EXISTS email_settings_account_id_email_key ON email_settings (account_id, email, service_provider);

# --- !Downs

DROP INDEX IF EXISTS email_settings_account_id_email_key;

CREATE UNIQUE INDEX IF NOT EXISTS email_settings_account_id_email_key ON email_settings (account_id, email);
