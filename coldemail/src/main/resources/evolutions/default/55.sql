# --- !Ups


update accounts acc set company = 'First Team' where company is null;

update teams t set
name = (select a.company from accounts a join teams_accounts ta on ta.account_id = a.id join teams t2 on t2.id = ta.team_id where t2.id = t.id limit 1);

update accounts acc set
first_name = (select left(a.email, strpos(a.email, '@') - 1) as first_name from accounts a where a.id = acc.id),
last_name = ''
where acc.first_name is null;


ALTER TABLE accounts ALTER COLUMN first_name SET NOT NULL;
ALTER TABLE accounts ALTER COLUMN last_name SET NOT NULL;
ALTER TABLE accounts ALTER COLUMN company SET NOT NULL;
ALTER TABLE teams ALTER COLUMN name SET NOT NULL;

# --- !Downs

ALTER TABLE accounts ALTER COLUMN first_name DROP NOT NULL;
ALTER TABLE accounts ALTER COLUMN last_name DROP NOT NULL;
ALTER TABLE accounts ALTER COLUMN company DROP NOT NULL;
ALTER TABLE teams ALTER COLUMN name DROP NOT NULL;

