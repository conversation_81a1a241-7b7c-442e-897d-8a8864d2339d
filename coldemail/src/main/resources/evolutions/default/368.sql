# --- !Ups

CREATE INDEX IF NOT EXISTS sr_emails_scheduled_teamid_toemail_trgm ON emails_scheduled
USING gin (team_id, to_email gin_trgm_ops);

CREATE INDEX IF NOT EXISTS sr_emails_scheduled_teamid_ccemails_trgm ON emails_scheduled
USING gin (team_id, cc_emails gin_trgm_ops);

CREATE INDEX IF NOT EXISTS sr_emails_scheduled_teamid_fromemail_trgm ON emails_scheduled
USING gin (team_id, from_email gin_trgm_ops);

CREATE INDEX IF NOT EXISTS sr_emails_scheduled_teamid_subject_trgm ON emails_scheduled
USING gin (team_id, subject gin_trgm_ops);

# --- !Downs

DROP INDEX IF EXISTS sr_emails_scheduled_teamid_toemail_trgm;

DROP INDEX IF EXISTS sr_emails_scheduled_teamid_ccemails_trgm;

DROP INDEX IF EXISTS sr_emails_scheduled_teamid_fromemail_trgm;

DROP INDEX IF EXISTS sr_emails_scheduled_teamid_subject_trgm;