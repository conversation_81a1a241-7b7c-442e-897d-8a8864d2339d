# ---------- !Ups

CREATE TABLE IF NOT EXISTS notes (

    id BIGSERIAL PRIMARY KEY,
    note TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ,

    added_by BIGINT REFERENCES accounts(id) NOT NULL,
    team_id BIGINT REFERENCES teams(id) ON DELETE CASCADE NOT NULL,

    prospect_id BIGINT REFERENCES prospects(id),
    task_uuid TEXT -- not able to reference it , as task_id doesn't have unique constraint on partitioned tasks table

);

CREATE INDEX IF NOT EXISTS sr_notes_tid_prospect_id ON notes(team_id, prospect_id) WHERE prospect_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS sr_notes_tid_task_uuid ON notes(team_id, task_uuid) WHERE task_uuid IS NOT NULL;
CREATE INDEX IF NOT EXISTS sr_notes_tid_created_at ON notes(team_id, created_at);

# ---------- !Downs

DROP TABLE IF EXISTS notes;