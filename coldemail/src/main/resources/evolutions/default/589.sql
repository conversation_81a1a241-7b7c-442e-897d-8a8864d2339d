
# --- !Ups

CREATE TABLE IF NOT EXISTS call_history_logs (

    id BIGSERIAL PRIMARY KEY,
    uuid TEXT UNIQUE NOT NULL,
    sub_account_uuid TEXT NOT NULL REFERENCES org_calling_credits(sub_account_uuid) ON DELETE CASCADE,
    team_id BIGINT REFERENCES teams(id) ON DELETE SET NULL,-- SO THAT LOGS WILL BE THERE WITH ORGANIZATION

    service_provider TEXT NOT NULL,
    call_sid TEXT NOT NULL,
    is_outgoing BOOLEAN NOT NULL,
    start_time TIMESTAMPTZ,
    end_time TIMESTAMPTZ,
    from_number TEXT NOT NULL,
    to_number TEXT,
    duration INTEGER,
    price INTEGER,
    price_unit TEXT,
    UNIQUE(service_provider, call_sid)
);

CREATE INDEX IF NOT EXISTS sr_call_history_logs_sub_account_uuid_team_id_start_time ON call_history_logs (
    sub_account_uuid, team_id, start_time
);

CREATE INDEX IF NOT EXISTS sr_call_history_logs_team_id ON call_history_logs (team_id);

CREATE INDEX IF NOT EXISTS sr_call_history_logs_sub_account_uuid on call_history_logs (sub_account_uuid);

CREATE INDEX IF NOT EXISTS sr_call_history_logs_from_number ON call_history_logs  (from_number);


# --- !Downs


DROP TABLE IF EXISTS call_history_logs;


