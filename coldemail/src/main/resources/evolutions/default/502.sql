# --- !Ups

CREATE TABLE IF NOT EXISTS org_calling_credits (
    sub_account_uuid TEXT PRIMARY KEY ,
    org_id BIGINT REFERENCES organizations(id) ON DELETE CASCADE NOT NULL,
    twl_sub_account_sid TEXT NOT NULL,
    twl_sub_account_auth_token TEXT NOT NULL,
    twl_sub_account_status TEXT NOT NULL,
    twl_sub_account_name TEXT NOT NULL,
    call_credits_remaining BIGINT NOT NULL,
    call_credits_updated_at TIMESTAMPTZ NOT NULL,
    call_credits BIGINT NOT NULL,
    previous_usage_deducted BIGINT,
    already_subtracted BIGINT,
    twl_check_usage_from TIMESTAMPTZ NOT NULL,
    credit_unit TEXT,
    twl_trigger_id TEXT,
    twl_trigger_name TEXT,
    twl_trigger_by TEXT,
    twl_trigger_value TEXT,
    twl_trigger_webhook TEXT,
    twl_trigger_recurring TEXT,
    twl_trigger_usage_category TEXT,
    twl_trigger_last_fired TEXT,
    in_queue_for_updating_credit BOOLEAN DEFAULT FALSE,
    last_pushed_to_queue_for_updating_credit TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

CREATE UNIQUE INDEX IF NOT EXISTS sr_org_calling_credits_org_id ON org_calling_credits(org_id);

CREATE UNIQUE INDEX IF NOT EXISTS sr_org_calling_credits_twl_sub_account_sid ON org_calling_credits(twl_sub_account_sid);

CREATE UNIQUE INDEX IF NOT EXISTS sr_org_calling_credits_org_id_twl_trigger_id ON org_calling_credits(org_id, twl_trigger_id);

# --- !Downs

DROP INDEX IF EXISTS sr_org_calling_credits_org_id_twl_trigger_id;

DROP INDEX IF EXISTS sr_org_calling_credits_twl_sub_account_sid;

DROP INDEX IF EXISTS sr_org_calling_credits_org_id;

DROP TABLE IF EXISTS org_calling_credits;
