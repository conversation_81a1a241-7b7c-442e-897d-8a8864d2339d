# --- !Ups

CREATE TABLE IF NOT EXISTS campaign_steps_variants
(
    id BIGSERIAL PRIMARY KEY,
    campaign_id BIGINT REFERENCES campaigns (id) ON DELETE CASCADE  NOT NULL,
    step_id BIGINT REFERENCES campaign_steps (id) ON DELETE CASCADE  NOT NULL,
    subject TEXT NOT NULL,
    body TEXT NOT NULL,
    template_id BIGINT REFERENCES templates (id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT now()
);

ALTER TABLE emails_scheduled
  ADD COLUMN IF NOT EXISTS template_id BIGINT REFERENCES templates (id) ON DELETE SET NULL,
  ADD COLUMN IF NOT EXISTS variant_id BIGINT REFERENCES campaign_steps_variants (id) ON DELETE SET NULL;


CREATE INDEX IF NOT EXISTS sr_emails_scheduled_template_id ON emails_scheduled (template_id);
CREATE INDEX IF NOT EXISTS sr_emails_scheduled_variant_id ON emails_scheduled (variant_id);


# --- !Downs

DROP INDEX IF EXISTS sr_emails_scheduled_template_id;
DROP INDEX IF EXISTS sr_emails_scheduled_variant_id;

ALTER TABLE emails_scheduled
  DROP COLUMN IF EXISTS template_id,
  DROP COLUMN IF EXISTS variant_id;

DROP TABLE IF EXISTS campaign_steps_variants;


