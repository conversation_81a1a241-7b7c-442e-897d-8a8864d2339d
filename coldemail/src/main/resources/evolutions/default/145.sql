# --- !Ups

ALTER TABLE prospect_lists
ADD COLUMN IF NOT EXISTS total_prospects INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_emails_sent INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_emails_opened INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_emails_clicked INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_emails_replied INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_emails_bounced INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_emails_opted_out INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS stats_last_updated TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS last_contacted TIMESTAMPTZ
;


# --- !Downs

ALTER TABLE prospect_lists
DROP COLUMN IF EXISTS total_prospects,
DROP COLUMN IF EXISTS total_emails_sent,
DROP COLUMN IF EXISTS total_emails_opened,
DROP COLUMN IF EXISTS total_emails_clicked,
DROP COLUMN IF EXISTS total_emails_replied,
DROP COLUMN IF EXISTS total_emails_bounced,
DROP COLUMN IF EXISTS total_emails_opted_out,
DROP COLUMN IF EXISTS stats_last_updated,
DROP COLUMN IF EXISTS last_contacted
;
