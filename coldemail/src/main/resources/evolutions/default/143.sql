# --- !Ups

ALTER TABLE organizations
ADD COLUMN IF NOT EXISTS payment_gateway TEXT,
ADD COLUMN IF NOT EXISTS stripe_plan_name TEXT,
ADD COLUMN IF NOT EXISTS total_sending_email_accounts INT DEFAULT 1,
ADD COLUMN IF NOT EXISTS billing_address_line1 TEXT,
ADD COLUMN IF NOT EXISTS billing_address_city TEXT,
ADD COLUMN IF NOT EXISTS billing_address_postal_code TEXT,
ADD COLUMN IF NOT EXISTS billing_address_country TEXT,
ADD COLUMN IF NOT EXISTS billing_address_state TEXT,
ADD COLUMN IF NOT EXISTS billing_email TEXT
;


# --- !Downs

ALTER TABLE organizations
DROP COLUMN IF EXISTS total_sending_email_accounts,
DROP COLUMN IF EXISTS stripe_plan_name,
DROP COLUMN IF EXISTS payment_gateway,
DROP COLUMN IF EXISTS billing_address_line1,
DROP COLUMN IF EXISTS billing_address_city,
DROP COLUMN IF EXISTS billing_address_postal_code,
DROP COLUMN IF EXISTS billing_address_country,
DROP COLUMN IF EXISTS billing_address_state,
DROP COLUMN IF EXISTS billing_email
;
