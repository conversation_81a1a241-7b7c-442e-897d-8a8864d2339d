# --- !Ups

ALTER TABLE email_settings ADD COLUMN current_prospect_sent_count_email INT DEFAULT 0;

UPDATE email_settings SET current_prospect_sent_count_email = 0;

ALTER TABLE email_settings ALTER COLUMN current_prospect_sent_count_email SET NOT NULL;



ALTER TABLE organizations
    ADD COLUMN current_prospect_sent_count_org INT DEFAULT 0,
    ADD COLUMN error TEXT,
    ADD COLUMN error_reported_at TIMESTAMPTZ,
    ADD COLUMN paused_till TIMESTAMPTZ,
    ADD COLUMN error_code TEXT,
    ADD COLUMN current_cycle_started_at TIMESTAMPTZ DEFAULT now();

UPDATE organizations SET
    current_prospect_sent_count_org = 0,
    current_cycle_started_at = (CASE WHEN last_payment_on IS NOT NULL THEN last_payment_on ELSE created_at END);

ALTER TABLE organizations ALTER COLUMN current_prospect_sent_count_org SET NOT NULL;
ALTER TABLE organizations ALTER COLUMN current_cycle_started_at SET NOT NULL;



# --- !Downs

ALTER TABLE organizations
    DROP COLUMN current_prospect_sent_count_org,
    DROP COLUMN error,
    DROP COLUMN error_reported_at,
    DROP COLUMN paused_till,
    DROP COLUMN error_code,
    DROP COLUMN current_cycle_started_at
    ;

ALTER TABLE email_settings DROP COLUMN current_prospect_sent_count_email;
