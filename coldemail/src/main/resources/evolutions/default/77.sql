# --- !Ups

-- for deleting campaigns


ALTER TABLE campaign_steps DROP CONSTRAINT campaign_steps_campaign_id_fkey;

ALTER TABLE campaign_steps
  ADD CONSTRAINT campaign_steps_campaign_id_fkey
  FOREIGN KEY (campaign_id)
  REFERENCES campaigns(id)
  ON DELETE CASCADE;




ALTER TABLE campaign_steps_relationships DROP CONSTRAINT campaign_steps_relationships_campaign_id_fkey;

ALTER TABLE campaign_steps_relationships
  ADD CONSTRAINT campaign_steps_relationships_campaign_id_fkey
  FOREIGN KEY (campaign_id)
  REFERENCES campaigns(id)
  ON DELETE CASCADE;




ALTER TABLE campaigns_prospects DROP CONSTRAINT campaigns_prospects_campaign_id_fkey;

ALTER TABLE campaigns_prospects
  ADD CONSTRAINT campaigns_prospects_campaign_id_fkey
  FOREIGN KEY (campaign_id)
  REFERENCES campaigns(id)
  ON DELETE CASCADE;




ALTER TABLE emails_received DROP CONSTRAINT emails_received_campaign_id_fkey;

ALTER TABLE emails_received
  ADD CONSTRAINT emails_received_campaign_id_fkey
  FOREIGN KEY (campaign_id)
  REFERENCES campaigns(id)
  ON DELETE SET NULL;



ALTER TABLE emails_scheduled ALTER COLUMN campaign_id DROP NOT NULL;

ALTER TABLE emails_scheduled DROP CONSTRAINT emails_sent_campaign_id_fkey;

ALTER TABLE emails_scheduled
  ADD CONSTRAINT emails_sent_campaign_id_fkey
  FOREIGN KEY (campaign_id)
  REFERENCES campaigns(id)
  ON DELETE SET NULL;


-- for deleting email settings


ALTER TABLE emails_received DROP CONSTRAINT emails_received_email_settings_id_fkey;

ALTER TABLE emails_received
  ADD CONSTRAINT emails_received_email_settings_id_fkey
  FOREIGN KEY (email_settings_id)
  REFERENCES email_settings(id)
  ON DELETE SET NULL;


# --- !Downs
