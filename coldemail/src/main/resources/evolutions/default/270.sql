# --- !Ups



DROP INDEX IF EXISTS sr_emails_scheduled_pushed_to_rabbitmq;

DROP INDEX IF EXISTS sr_emails_scheduled_has_gmail_fbl_header;

DROP INDEX IF EXISTS emails_scheduled_rep_mailserver_id;

CREATE INDEX IF NOT EXISTS sr_emails_scheduled_cid_pid_senderemailid_stepid ON public.emails_scheduled USING btree (campaign_id, prospect_id, sender_email_settings_id, step_id);



DROP INDEX IF EXISTS sr_cp_opened;

CREATE INDEX IF NOT EXISTS sr_cp_cid_opened ON campaigns_prospects (campaign_id, opened);


DROP INDEX IF EXISTS sr_cp_clicked;

CREATE INDEX IF NOT EXISTS sr_cp_cid_clicked ON campaigns_prospects (campaign_id, clicked);


DROP INDEX IF EXISTS sr_cp_out_of_office;

CREATE INDEX IF NOT EXISTS sr_cp_cid_out_of_office ON campaigns_prospects (campaign_id, out_of_office);


DROP INDEX IF EXISTS sr_cp_active;

CREATE INDEX IF NOT EXISTS sr_cp_cid_active ON campaigns_prospects (campaign_id, active);


DROP INDEX IF EXISTS sr_cp_bounced;

CREATE INDEX IF NOT EXISTS sr_cp_cid_bounced ON campaigns_prospects (campaign_id, bounced);


DROP INDEX IF EXISTS sr_cp_replied;

CREATE INDEX IF NOT EXISTS sr_cp_cid_replied ON campaigns_prospects (campaign_id, replied);


DROP INDEX IF EXISTS sr_cp_invalid_email;

CREATE INDEX IF NOT EXISTS sr_cp_cid_invalid_email ON campaigns_prospects (campaign_id, invalid_email);


DROP INDEX IF EXISTS sr_cp_sent;

CREATE INDEX IF NOT EXISTS sr_cp_cid_sent ON campaigns_prospects (campaign_id, sent);



DROP INDEX IF EXISTS sr_campaigns_prospects_last_contacted_at;

CREATE INDEX IF NOT EXISTS sr_cp_cid_last_contacted_at ON campaigns_prospects (campaign_id, last_contacted_at);



DROP INDEX IF EXISTS sr_cp_completed;

CREATE INDEX IF NOT EXISTS sr_cp_cid_completed ON campaigns_prospects (campaign_id, completed);



DROP INDEX IF EXISTS sr_cp_paused;

CREATE INDEX IF NOT EXISTS sr_cp_cid_paused ON campaigns_prospects (campaign_id, paused);


CREATE INDEX IF NOT EXISTS sr_cp_cid_opted_out ON campaigns_prospects (campaign_id, opted_out);

CREATE INDEX IF NOT EXISTS sr_cp_cid_last_scheduled ON campaigns_prospects (campaign_id, last_scheduled);

CREATE INDEX IF NOT EXISTS sr_cp_cid_to_check ON campaigns_prospects (campaign_id, to_check);

DROP INDEX IF EXISTS sr_prospects_teamid_accountid;

DROP INDEX IF EXISTS sr_prospects_team_account_pkey;


create index if not exists sr_emails_scheduled_inboxid_schdat on emails_scheduled (inbox_email_setting_id, scheduled_at) where not sent;

drop index if exists
sr_emails_scheduled_sesid_cid_sfromc_schat_sentat_getcount
;

drop index if exists email_threads_team_id_inbox_countq_3;

drop index if exists sr_email_threads_campaignid_prospectid_campaignname;


drop index if exists sr_email_threads_sr_read;

drop index if exists sr_email_threads_created_at;

drop index if exists sr_email_threads_latest_reply_at;



create index if not exists sr_email_threads_tid_id on email_threads (team_id, id);
create index if not exists sr_email_threads_tid_latestemid on email_threads (team_id, latest_email_id);

create index if not exists sr_emails_scheduled_tid_id on emails_scheduled (team_id, id);

create index if not exists sr_email_threads_tid_archived on email_threads (team_id, archived);
create index if not exists sr_email_threads_tid_snoozed on email_threads (team_id, snoozed);

create index if not exists sr_email_threads_tid_aid_latestrepnotnull on email_threads (team_id, account_id, latest_reply_at) where latest_reply_at is not null;

drop index if exists sr_email_threads_latestreplyat_accountid_teamid;

# --- !Downs


