# --- !Ups

ALTER TABLE public.email_threads_prospects
        ALTER COLUMN prospect_id SET NOT NULL,
        ALTER COLUMN email_thread_id SET NOT NULL;


SELECT
	create_constraint_if_not_exists (
	'email_threads_prospects',
	'email_threads_prospects_pkey',
	'PRIMARY KEY USING INDEX sr_email_threads_prospects_unique'
	);


SELECT
	create_constraint_if_not_exists (
	'webhook_events',
	'webhook_events_pkey',
	'PRIMARY KEY USING INDEX webhook_events_webhook_id_event'
	);


SELECT
	create_constraint_if_not_exists (
	'phantombuster_agents',
	'phantombuster_agents_pkey',
	'PRIMARY KEY USING INDEX sr_phantombuster_agents_agent_id'
	);
# --- !Downs

Alter table email_threads_prospects
DROP CONSTRAINT email_threads_prospects_pkey;

Alter table webhook_events
DROP CONSTRAINT webhook_events_pkey;

Alter table phantombuster_agents
DROP CONSTRAINT phantombuster_agents_pkey;

ALTER TABLE email_threads_prospects
  ALTER COLUMN prospect_id DROP NOT NULL,
  ALTER COLUMN email_thread_id DROP NOT NULL;