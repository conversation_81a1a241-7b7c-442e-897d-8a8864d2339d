# --- !Ups

ALTER TABLE sr_internal_product_feature_first_usage_log
DROP CONSTRAINT sr_internal_product_feature_first_usage_log_org_id_fkey,
ADD CONSTRAINT sr_internal_product_feature_first_usage_log_org_id_fkey
   FOREIGN KEY (org_id)
   REFERENCES organizations (id)
   ON DELETE CASCADE;

ALTER TABLE sr_internal_product_feature_first_usage_log
DROP CONSTRAINT sr_internal_product_feature_first_org_prospect_id_for_drip_fkey,
ADD CONSTRAINT sr_internal_product_feature_first_org_prospect_id_for_drip_fkey
  FOREIGN KEY (org_prospect_id_for_drip)
  REFERENCES prospects (id)
  ON DELETE CASCADE;

# --- !Downs

ALTER TABLE sr_internal_product_feature_first_usage_log
DROP CONSTRAINT sr_internal_product_feature_first_usage_log_org_id_fkey,
ADD CONSTRAINT sr_internal_product_feature_first_usage_log_org_id_fkey
   FOREIGN KEY (org_id)
   REFERENCES organizations (id);

ALTER TABLE sr_internal_product_feature_first_usage_log
DROP CONSTRAINT sr_internal_product_feature_first_org_prospect_id_for_drip_fkey,
ADD CONSTRAINT sr_internal_product_feature_first_org_prospect_id_for_drip_fkey
  FOREIGN KEY (org_prospect_id_for_drip)
  REFERENCES prospects (id);
