# --- !Ups
ALTER TABLE email_threads
    ADD COLUMN IF NOT EXISTS
    reply_sentiment_uuid text references reply_sentiments_for_teams(uuid) on delete set null;

ALTER TABLE campaigns_prospects
    ADD COLUMN IF NOT EXISTS
    reply_sentiment_uuid text references reply_sentiments_for_teams(uuid) on delete set null;


# --- !Downs

ALTER TABLE email_threads
    DROP COLUMN IF EXISTS reply_sentiment_uuid;

ALTER TABLE campaigns_prospects
    DROP COLUMN IF EXISTS reply_sentiment_uuid;