# --- !Ups

ALTER TABLE campaigns_prospects
ADD COLUMN IF NOT EXISTS total_opens INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_clicks INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_contacted_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS last_opened_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS last_clicked_at TIMESTAMPTZ
;


ALTER TABLE prospects
ADD COLUMN IF NOT EXISTS total_opens INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_clicks INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS first_contacted_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS last_opened_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS last_clicked_at TIMESTAMPTZ

;

create index if not exists sr_prospects_total_opens on prospects(total_opens);
create index if not exists sr_prospects_total_clicks on prospects(total_clicks);

# --- !Downs

drop index if exists sr_prospects_total_opens;
drop index if exists sr_prospects_total_clicks;

ALTER TABLE prospects
DROP COLUMN IF EXISTS total_opens,
DROP COLUMN IF EXISTS total_clicks,
DROP COLUMN IF EXISTS first_contacted_at,
DROP COLUMN IF EXISTS last_opened_at,
DROP COLUMN IF EXISTS last_clicked_at
;

ALTER TABLE campaigns_prospects
DROP COLUMN IF EXISTS total_opens,
DROP COLUMN IF EXISTS total_clicks,
DROP COLUMN IF EXISTS last_contacted_at,
DROP COLUMN IF EXISTS last_opened_at,
DROP COLUMN IF EXISTS last_clicked_at
;
