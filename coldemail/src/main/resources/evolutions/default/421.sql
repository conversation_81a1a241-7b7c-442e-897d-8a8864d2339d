# --- !Ups

drop index if exists email_threads_team_id_inbox_list_1;

drop index if exists sr_email_threads_team_id_latest_reply_at;

drop index if exists sr_email_threads_tid_aid_latestrepnotnull;

drop index if exists sr_email_threads_team_id;



CREATE INDEX
IF NOT EXISTS
sr_email_threads_tid_latestrept_descnulllast_hasprospect
ON email_threads (team_id, latest_reply_at desc NULLS LAST)
WHERE has_prospect
;

# --- !Downs

DROP INDEX IF EXISTS sr_email_threads_tid_latestrept_descnulllast_hasprospect;
