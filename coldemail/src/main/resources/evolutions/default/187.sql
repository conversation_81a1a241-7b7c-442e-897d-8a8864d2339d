# --- !Ups


CREATE TABLE IF NOT EXISTS user_roles
(
      id BIGSERIAL PRIMARY KEY,


      role_name TEXT NOT NULL,
      team_id BIGINT REFERENCES teams (id) ON DELETE CASCADE NOT NULL,


      created_at TIMESTAMPTZ DEFAULT now(),
      updated_at TIMESTAMPTZ,



      manage_billing B<PERSON><PERSON>EAN DEFAULT FALSE NOT NULL,
      user_management BOOLEAN DEFAULT FALSE NOT NULL,
      edit_roles BOOLEAN DEFAULT FALSE NOT NULL,


      view_user_settings BOOLEAN DEFAULT FALSE NOT NULL,
      view_user_settings_ownership TEXT NOT NULL,

      edit_user_settings BOOLEAN DEFAULT FALSE NOT NULL,
      edit_user_settings_ownership TEXT NOT NULL,



      view_prospects BOOLEAN DEFAULT FALSE NOT NULL,
      view_prospects_ownership TEXT NOT NULL,
      
      edit_prospects BOOLEAN DEFAULT FALSE NOT NULL,
      edit_prospects_ownership TEXT NOT NULL,
      
      delete_prospects BOOLEAN DEFAULT FALSE NOT NULL,
      delete_prospects_ownership TEXT NOT NULL,
    
    
    
      view_campaigns BOOLEAN DEFAULT FALSE NOT NULL,
      view_campaigns_ownership TEXT NOT NULL,
      
      edit_campaigns BOOLEAN DEFAULT FALSE NOT NULL,
      edit_campaigns_ownership TEXT NOT NULL,
      
      delete_campaigns BOOLEAN DEFAULT FALSE NOT NULL,
      delete_campaigns_ownership TEXT NOT NULL,
      
      change_campaign_status BOOLEAN DEFAULT FALSE NOT NULL,
      change_campaign_status_ownership TEXT NOT NULL,

    
    
      view_reports BOOLEAN DEFAULT FALSE NOT NULL,
      view_reports_ownership TEXT NOT NULL,
      
      edit_reports BOOLEAN DEFAULT FALSE NOT NULL,
      edit_reports_ownership TEXT NOT NULL,
      
      download_reports BOOLEAN DEFAULT FALSE NOT NULL,
      download_reports_ownership TEXT NOT NULL,
    
    
    
      view_inbox BOOLEAN DEFAULT FALSE NOT NULL,
      view_inbox_ownership TEXT NOT NULL,
    
      edit_inbox BOOLEAN DEFAULT FALSE NOT NULL,
      edit_inbox_ownership TEXT NOT NULL,
    
      send_manual_email BOOLEAN DEFAULT FALSE NOT NULL,
      send_manual_email_ownership TEXT NOT NULL,
    
    
    
      view_templates BOOLEAN DEFAULT FALSE NOT NULL,
      view_templates_ownership TEXT NOT NULL,
    
      edit_templates BOOLEAN DEFAULT FALSE NOT NULL,
      edit_templates_ownership TEXT NOT NULL,
    
      delete_templates BOOLEAN DEFAULT FALSE NOT NULL,
      delete_templates_ownership TEXT NOT NULL,
    
    
    
      view_blacklist BOOLEAN DEFAULT FALSE NOT NULL,
      view_blacklist_ownership TEXT NOT NULL,
    
      edit_blacklist BOOLEAN DEFAULT FALSE NOT NULL,
      edit_blacklist_ownership TEXT NOT NULL,
    
    
    
      view_email_accounts BOOLEAN DEFAULT FALSE NOT NULL,
      view_email_accounts_ownership TEXT NOT NULL,
    
      edit_email_accounts BOOLEAN DEFAULT FALSE NOT NULL,
      edit_email_accounts_ownership TEXT NOT NULL,
    
      delete_email_accounts BOOLEAN DEFAULT FALSE NOT NULL,
      delete_email_accounts_ownership TEXT NOT NULL

);


ALTER TABLE teams_accounts ADD COLUMN IF NOT EXISTS user_role_id BIGINT REFERENCES user_roles (id);

CREATE UNIQUE INDEX IF NOT EXISTS user_role_name_team_id ON user_roles (team_id, lower(role_name));




# --- !Downs

DROP INDEX IF EXISTS user_role_name_team_id;

ALTER TABLE teams_accounts DROP COLUMN IF EXISTS user_role_id;

DROP TABLE IF EXISTS user_roles;

