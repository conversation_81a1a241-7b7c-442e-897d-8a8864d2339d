# --- !Ups

CREATE TABLE IF NOT EXISTS li_prospect_data_additional
(
    id BIGSERIAL PRIMARY KEY,
    li_prospect_data_id BIGSERIAL REFERENCES li_prospect_data(id),
    uuid TEXT,
    account_id BIGINT,
    team_id BIGINT,
    ta_id BIGINT,
    additional_data_no INTEGER,
    certification_end_date TEXT,
    certification_name TEXT,
    certification_organization TEXT,
    certification_start_date TEXT,
    interest TEXT,
    job_title_level TEXT,
    language_name TEXT,
    language_proficiency TEXT,
    location_name TEXT,
    phone_number TEXT,
    profile_id TEXT,
    profile_network TEXT,
    profile_url TEXT,
    profile_username TEXT,
    region TEXT,
    skill TEXT,
    country TEXT,
    email_address TEXT,
    email_type TEXT,
    street_addresses_address_line_2 TEXT,
    street_addresses_continent TEXT,
    street_addresses_country TEXT,
    street_addresses_geo TEXT,
    street_addresses_locality TEXT,
    street_addresses_metro TEXT,
    street_addresses_name TEXT,
    street_addresses_postal_code TEXT,
    street_addresses_region TEXT,
    street_addresses_street_address TEXT,
    additional_data_hash TEXT
)

# --- !Downs

DROP TABLE li_prospect_data_additional;

