# --- !Ups

--adding a set of two previous and two upcoming tables in partition
select create_partition(
    parent_table_name => 'public.audit_event_logs',
    partition_control => 'created_at',
    parition_type => 'native',
    partition_interval => 'monthly',
    partition_premake => 2);


--setting the retention at 2 months to drop the tables after two months
UPDATE partman.part_config
SET infinite_time_partitions = true,
    retention = '2 months',
    retention_keep_table=False
WHERE parent_table = 'public.audit_event_logs';

# --- !Downs