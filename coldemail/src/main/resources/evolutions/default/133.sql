# --- !Ups

ALTER TABLE campaigns_prospects ADD COLUMN IF NOT EXISTS prospect_category_id BIGINT REFERENCES prospect_categories (id) DEFAULT 1;

-- I manually set the prospect_category_id = 1 in existing rows
ALTER TABLE campaigns_prospects ALTER COLUMN prospect_category_id SET NOT NULL;


CREATE INDEX IF NOT EXISTS sr_prospects_email_lowercase ON prospects ((lower(email)));
CREATE INDEX IF NOT EXISTS sr_prospects_email_domain_lowercase ON prospects ((lower(email_domain)));

# --- !Downs

DROP INDEX IF EXISTS sr_prospects_email_lowercase;
DROP INDEX IF EXISTS sr_prospects_email_domain_lowercase;

ALTER TABLE campaigns_prospects DROP COLUMN IF EXISTS prospect_category_id;
