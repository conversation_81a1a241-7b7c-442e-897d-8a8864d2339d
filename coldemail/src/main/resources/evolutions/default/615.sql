# --- !Ups

CREATE TABLE IF NOT EXISTS phantombuster_proxies(
    id BIGSERIAL PRIMARY KEY,
    ip_address TEXT NOT NULL,
    country TEXT NOT NULL,
    username TEXT,
    password_enc TEXT,
    number_of_accounts_using INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS sr_pb_proxies_country_number_of_accounts_using ON phantombuster_proxies(country, number_of_accounts_using);

CREATE UNIQUE INDEX IF NOT EXISTS sr_pb_proxies_ip_address ON phantombuster_proxies(ip_address);

# --- !Downs

DROP TABLE IF EXISTS phantombuster_proxies; 