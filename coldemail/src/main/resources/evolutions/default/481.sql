# --- !Ups

--	creating a new tables with partitions

CREATE TABLE IF NOT EXISTS audit_event_logs
(
    id BIGSERIAL,
    event_log_id TEXT NOT NULL,
    audit_request_log_id TEXT NOT NULL,
    event_type TEXT NOT NULL,
    event_object_type TEXT NOT NULL,
    event_data jsonb NOT NULL,
    team_id BIGINT NOT NULL,
    account_id BIGINT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
        PRIMARY KEY (id, created_at)
) PARTITION BY range (created_at);

# --- !Downs

-- drop table

drop table audit_event_logs;