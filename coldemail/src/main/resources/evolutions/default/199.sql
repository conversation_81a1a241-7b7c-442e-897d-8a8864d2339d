# --- !Ups

CREATE TABLE IF NOT EXISTS campaign_edited_preview_emails
(
    id BIGSERIAL PRIMARY KEY,

    prospect_id BIGINT REFERENCES prospects(id) ON DELETE CASCADE NOT NULL,
    step_id BIGINT REFERENCES campaign_steps(id) ON DELETE CASCADE NOT NULL,
    campaign_id BIGINT REFERENCES campaigns(id) ON DELETE CASCADE,
    edited_by_account_id BIGINT REFERENCES accounts(id) ON DELETE CASCADE NOT NULL,

    edited_subject TEXT NOT NULL,
    edited_body TEXT NOT NULL,

    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

CREATE UNIQUE INDEX IF NOT EXISTS sr_campaign_edited_preview_emails_unique_stepwise ON campaign_edited_preview_emails (prospect_id, step_id);


ALTER TABLE emails_scheduled
		ADD COLUMN IF NOT EXISTS is_edited_preview_email BOOLEAN
	;


-- pending migrations
create index if not exists sr_emails_scheduled_sesid_cid_sfromc_schat_sentat_getcount ON emails_scheduled(sender_email_settings_id, campaign_id, scheduled_at, sent_at) where scheduled_from_campaign;



--optimize feed query
create index if not exists sr_prospect_events_aid_tid_cat on prospect_events (account_id, team_id, created_at);



--optimize feed query
create index if not exists sr_emails_scheduled_aid_tid_repliedat on emails_scheduled (account_id, team_id, replied_at) where replied_at is not null;



-- google authenticator 2fa
alter table accounts add column if not exists gauthkey TEXT;


create index if not exists sr_prospects_email_domain on prospects (email_domain);

create index if not exists sr_email_settings_service_provider on email_settings (service_provider);

create index if not exists sr_emails_scheduled_sentat_where_scheduled_from_campaign on emails_scheduled (sent_at) where sent and scheduled_from_campaign;

DROP INDEX IF EXISTS sr_prospects_customfields_trgm_jumpermedia_bio;

DROP INDEX IF EXISTS sr_prospects_customfields_trgm_jumpermedia_searchname;



# --- !Downs

drop index if exists sr_prospects_email_domain;

drop index if  exists sr_email_settings_service_provider;

drop index if exists sr_emails_scheduled_sentat_where_scheduled_from_campaign;

alter table accounts drop column if exists gauthkey;

drop index if exists sr_emails_scheduled_aid_tid_repliedat;

drop index if exists sr_prospect_events_aid_tid_cat;

drop index if exists sr_emails_scheduled_sesid_cid_sfromc_schat_sentat_getcount;




alter table emails_scheduled
	drop column if exists is_edited_preview_email;

drop index if exists sr_campaign_edited_preview_emails_unique_stepwise;

drop table if exists campaign_edited_preview_emails;
 
