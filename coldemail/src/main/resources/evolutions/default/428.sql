# --- !Ups

CREATE TABLE IF NOT EXISTS campaign_send_start_reports (
    id BIGSERIAL PRIMARY KEY,
    team_id BIGINT REFERENCES teams(id) ON DELETE CASCADE,
    campaign_id BIGINT REFERENCES campaigns(id) ON DELETE CASCADE,
    campaign_analysis_result TEXT NOT NULL,
    analyzed_at TIMESTAMPTZ DEFAULT now(),
    total_sent_prospects INT NOT NULL,
    total_unsent_prospects INT NOT NULL,
    total_unsent_prospects_in_dnc INT NOT NULL,
    total_unsent_prospects_in_same_tz INT NOT NULL
);

ALTER TABLE campaigns
ADD COLUMN IF NOT EXISTS last_email_sent_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS latest_campaign_send_start_reports_id BIGINT REFERENCES campaign_send_start_reports(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS pushed_to_queue_for_analysing_start_report_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS in_queue_for_analyzing_start_report BOOLEAN DEFAULT FALSE;

CREATE INDEX IF NOT EXISTS sr_campaign_id_campaign_send_start_reports ON campaign_send_start_reports(campaign_id);

CREATE INDEX IF NOT EXISTS sr_campaign_id_team_id_send_start_reports ON campaign_send_start_reports(team_id, campaign_id);

CREATE INDEX IF NOT EXISTS sr_campaign_id_campaign_send_start_reports_id ON campaigns(latest_campaign_send_start_reports_id);

CREATE INDEX IF NOT EXISTS sr_team_id_campaign_id_campaign_send_start_reports_id ON campaigns(team_id, id, latest_campaign_send_start_reports_id);

# --- !Downs

DROP INDEX IF EXISTS sr_team_id_campaign_id_campaign_send_start_reports_id;

DROP INDEX IF EXISTS sr_campaign_id_campaign_send_start_reports_id;

DROP INDEX IF EXISTS sr_campaign_id_team_id_send_start_reports;

DROP INDEX IF EXISTS sr_campaign_id_campaign_send_start_reports;

ALTER TABLE campaigns
DROP COLUMN IF EXISTS last_email_sent_at,
DROP COLUMN IF EXISTS latest_campaign_send_start_reports_id,
DROP COLUMN IF EXISTS pushed_to_queue_for_analysing_start_report_at,
DROP COLUMN IF EXISTS in_queue_for_analyzing_start_report;

DROP TABLE IF EXISTS campaign_send_start_reports;