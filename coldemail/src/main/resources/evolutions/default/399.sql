# --- !Ups

CREATE INDEX IF NOT EXISTS
        sr_team_inbox_in_queue_for_i_s_false on
            team_inbox(pushed_to_queue_for_initial_syncing_at) where
            in_queue_for_initial_syncing = false AND
            initial_sync_completed = false AND
            initial_sync_required = true;

CREATE INDEX IF NOT EXISTS
        sr_team_inbox_in_queue_for_i_s_true_pushed_to_queue_for_i_s_at on
            team_inbox(in_queue_for_initial_syncing, pushed_to_queue_for_initial_syncing_at)
            where in_queue_for_initial_syncing = true AND
              initial_sync_completed = false AND
              initial_sync_required = true;


# --- !Downs


DROP INDEX IF EXISTS sr_team_inbox_in_queue_for_i_s_false;

DROP INDEX IF EXISTS sr_team_inbox_in_queue_for_i_s_true_pushed_to_queue_for_i_s_at;
