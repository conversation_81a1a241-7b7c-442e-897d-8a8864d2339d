# --- !Ups

ALTER TABLE organizations
  ADD COLUMN fs_account_id TEXT UNIQUE,
  ADD COLUMN last_payment_on TIMESTAMPTZ,
  ADD COLUMN last_payment_obj jsonb DEFAULT '{}',
  ADD COLUMN plan_type TEXT,
  ADD COLUMN plan_name TEXT,
  ADD COLUMN is_agency BOOLEAN DEFAULT FALSE,
  ADD COLUMN trial_ends_at TIMESTAMPTZ,
  ADD COLUMN subscription_order_obj jsonb DEFAULT '{}',
  ADD COLUMN next_billing_date TIMESTAMPTZ,
  ADD COLUMN max_team_members INTEGER DEFAULT 1,
  ADD COLUMN max_prospects_per_month INTEGER DEFAULT 100,
  ADD COLUMN max_api_calls_per_month INTEGER DEFAULT 1000,
  ADD COLUMN additional_spam_tests INTEGER DEFAULT 0,
  ADD COLUMN additional_sending_email_accounts INTEGER DEFAULT 0,
  ADD COLUMN active BOOLEAN DEFAULT TRUE,
  ADD COLUMN deactivation_message TEXT,
  ADD COLUMN deactivated_at TIMESTAMPTZ
  ;

UPDATE organizations org SET
  plan_type = 'free',
  plan_name = 'basic',
  is_agency = FALSE,
  trial_ends_at = now() + interval '14 days',
  last_payment_obj = '{}',
  subscription_order_obj = '{}',
  max_team_members = (select count(*) from accounts acc where acc.org_id = org.id),
  max_prospects_per_month = 100,
  max_api_calls_per_month = 1000,
  additional_spam_tests = 0,
  additional_sending_email_accounts = 0,
  active = TRUE
  ;


ALTER TABLE organizations ALTER COLUMN plan_type SET NOT NULL;
ALTER TABLE organizations ALTER COLUMN plan_name SET NOT NULL;
ALTER TABLE organizations ALTER COLUMN is_agency SET NOT NULL;
ALTER TABLE organizations ALTER COLUMN trial_ends_at SET NOT NULL;
ALTER TABLE organizations ALTER COLUMN last_payment_obj SET NOT NULL;
ALTER TABLE organizations ALTER COLUMN subscription_order_obj SET NOT NULL;
ALTER TABLE organizations ALTER COLUMN max_team_members SET NOT NULL;
ALTER TABLE organizations ALTER COLUMN max_prospects_per_month SET NOT NULL;
ALTER TABLE organizations ALTER COLUMN max_api_calls_per_month SET NOT NULL;
ALTER TABLE organizations ALTER COLUMN additional_spam_tests SET NOT NULL;
ALTER TABLE organizations ALTER COLUMN additional_sending_email_accounts SET NOT NULL;
ALTER TABLE organizations ALTER COLUMN active SET NOT NULL;



# --- !Downs


ALTER TABLE organizations
  DROP COLUMN fs_account_id,
  DROP COLUMN last_payment_on,
  DROP COLUMN last_payment_obj,
  DROP COLUMN plan_type,
  DROP COLUMN plan_name,
  DROP COLUMN is_agency,
  DROP COLUMN trial_ends_at,
  DROP COLUMN subscription_order_obj,
  DROP COLUMN next_billing_date,
  DROP COLUMN max_team_members,
  DROP COLUMN max_prospects_per_month,
  DROP COLUMN max_api_calls_per_month,
  DROP COLUMN additional_spam_tests,
  DROP COLUMN additional_sending_email_accounts,
  DROP COLUMN active,
  DROP COLUMN deactivation_message,
  DROP COLUMN deactivated_at
  ;
