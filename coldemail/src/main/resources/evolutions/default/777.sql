# --- !Ups

ALTER TABLE blacklist
  ALTER COLUMN team_id DROP NOT NULL,
  ALTER COLUMN ta_id DROP NOT NULL;


ALTER TABLE blacklist
  ADD COLUMN IF NOT EXISTS org_id BIGINT REFERENCES organizations (id) ON DELETE CASCADE,
  ADD COLUMN IF NOT EXISTS is_global BOOLEAN;

  Update blacklist
  set is_global = false
  where is_global is null;

update blacklist b set
org_id = (select t.org_id from teams t where t.id = b.team_id)
where
b.org_id is null
;

  ALTER TABLE blacklist
    ALTER COLUMN is_global SET NOT NULL,
    ALTER COLUMN org_id SET NOT NULL;

ALTER TABLE blacklist
ADD CONSTRAINT sr_blacklist_if_is_global_false_tid_not_null
CHECK (
        (is_global IS FALSE AND team_id IS NOT NULL) OR
        (is_global IS TRUE AND team_id IS NULL)

      );

create index if not exists sr_blacklist_org_id on blacklist(org_id);

# --- !Downs

ALTER TABLE blacklist
  DROP COLUMN IF EXISTS org_id,
  DROP COLUMN IF EXISTS is_global;

ALTER TABLE blacklist
  ALTER COLUMN team_id SET NOT NULL,
  ALTER COLUMN ta_id SET NOT NULL;
