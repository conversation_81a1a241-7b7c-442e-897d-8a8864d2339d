# --- !Ups

ALTER TABLE campaigns
  DROP COLUMN total_sent,
  DROP COLUMN total_opened,
  DROP COLUMN total_replied;


ALTER TABLE emails_sent
  ADD COLUMN opened BOOLEAN DEFAULT FALSE,
  ADD COLUMN opened_at TIMESTAMPTZ,
  ADD COLUMN opted_out BOOLEAN DEFAULT FALSE,
  ADD COLUMN opted_out_at TIMESTAMPTZ;

UPDATE emails_sent SET
  opened = FALSE,
  opted_out = FALSE;

ALTER TABLE emails_sent
  ALTER COLUMN opened SET NOT NULL,
  ALTER COLUMN opted_out SET NOT NULL;


ALTER TABLE emails_received
  ADD COLUMN reply_type TEXT;

UPDATE emails_received SET
  reply_type = 'not_categorized';

ALTER TABLE emails_received
  ALTER COLUMN reply_type SET NOT NULL;

# --- !Downs

ALTER TABLE emails_received
  DROP COLUMN reply_type;


ALTER TABLE emails_sent
  DROP COLUMN opened,
  DROP COLUMN opened_at,
  DROP COLUMN opted_out,
  DROP COLUMN opted_out_at;


ALTER TABLE campaigns
  ADD COLUMN total_sent INTEGER DEFAULT 0,
  ADD COLUMN total_opened INTEGER DEFAULT 0,
  ADD COLUMN total_replied INTEGER DEFAULT 0;
