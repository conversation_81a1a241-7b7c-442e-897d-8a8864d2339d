# --- !Ups

CREATE TABLE IF NOT EXISTS campaign_sending_volume_logs (
    id BIGSERIAL PRIMARY KEY,
	campaign_id bigint REFERENCES campaigns (id) ON DELETE CASCADE NOT NULL,
    team_id BIGINT References teams(id)  on delete cascade NOT NULL,
    date_in_campaign_timezone text not null,
    campaign_timezone text not null,
    consecutive_delay_in_seconds Int not null,
    warmup_is_on boolean not null,
    warmup_started_at timestamptz,
    warmup_starting_email_count int,
    campaign_start_time timestamptz not null,
    pushed_for_checking_at_minutes_since_campaign_start_time Int not null,
    actual_minutes_since_campaign_start_time_during_computing Int not null,
    added_at timestamptz DEFAULT now() NOT NULL,
    current_sent_count Int not null,
    expected_sent_count_till_now Int not null,
    current_sent_percent float not null,
    total_scheduled_for_today_till_now Int not null,
    possible_issue_if_any text not null
);

create unique index if not exists sr_csvl_tid_cid_dict_pfcamscst_unique
on campaign_sending_volume_logs(team_id, campaign_id, date_in_campaign_timezone, pushed_for_checking_at_minutes_since_campaign_start_time);

CREATE INDEX IF NOT EXISTS sr_csvl_tid_cid_id on campaign_sending_volume_logs (team_id, campaign_id, id);

CREATE INDEX IF NOT EXISTS sr_csvl_tid_cid_added_at on campaign_sending_volume_logs (team_id, campaign_id, added_at);

# --- !Downs

DROP TABLE IF EXISTS campaign_sending_volume_logs;