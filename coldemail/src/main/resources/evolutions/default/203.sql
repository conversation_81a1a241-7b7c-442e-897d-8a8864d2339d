# --- !Ups


CREATE TABLE IF NOT EXISTS prospect_accounts
(
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    custom_id TEXT NOT NULL,
    owner_id BIGINT REFERENCES accounts(id) ON DELETE CASCADE  NOT NULL,
    team_id BIGINT REFERENCES teams(id) ON DELETE CASCADE NOT NULL,
    description TEXT,
    source TEXT,
    industry TEXT,
    website TEXT,
    linkedin_url TEXT,
    custom_fields jsonb,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

CREATE UNIQUE INDEX IF NOT EXISTS prospect_accounts_custom_id_team_id ON prospect_accounts (custom_id, team_id);

ALTER TABLE prospects
ADD COLUMN IF NOT EXISTS prospect_account_id BIGINT REFERENCES prospect_accounts (id)  ON DELETE SET NULL
;

ALTER TABLE emails_scheduled
ADD COLUMN IF NOT EXISTS prospect_account_id BIGINT REFERENCES prospect_accounts (id)
;

ALTER TABLE column_defs
ADD COLUMN IF NOT EXISTS type TEXT
;

ALTER TABLE triggers
ADD COLUMN IF NOT EXISTS tp_filter_id TEXT
;

ALTER TABLE email_threads
ADD COLUMN IF NOT EXISTS snoozed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS snoozed_till TIMESTAMPTZ;

# --- !Downs

DROP INDEX IF EXISTS prospect_accounts_custom_id_team_id;

ALTER TABLE prospects
DROP COLUMN IF EXISTS prospect_account_id
;

ALTER TABLE emails_scheduled
DROP COLUMN IF EXISTS prospect_account_id
;

DROP TABLE IF EXISTS prospect_accounts;

ALTER TABLE column_defs
DROP COLUMN IF EXISTS type
;

ALTER TABLE triggers
DROP COLUMN IF EXISTS tp_filter_id
;

ALTER TABLE email_threads
DROP COLUMN IF EXISTS is_snoozed,
DROP COLUMN IF EXISTS snoozed_till
;


 
