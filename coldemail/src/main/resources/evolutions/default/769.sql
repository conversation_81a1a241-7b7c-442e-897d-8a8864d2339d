# --- !Ups

DO $$
BEGIN
  IF EXISTS(SELECT *
    FROM information_schema.columns
    WHERE table_name='lead_validation_batch_request' and column_name='total_frozen_credits')
  THEN
      ALTER TABLE "public"."lead_validation_batch_request" RENAME COLUMN "total_frozen_credits" TO "total_frozen_purchased_credits";;
  END IF;;
END $$;;

DO $$
BEGIN
  IF EXISTS(SELECT *
    FROM information_schema.columns
    WHERE table_name='lead_validation_batch_request' and column_name='total_reverted_credits')
  THEN
      ALTER TABLE "public"."lead_validation_batch_request" RENAME COLUMN "total_reverted_credits" TO "total_reverted_purchased_credits";;
  END IF;;
END $$;;


# --- !Downs

DO $$
BEGIN
  IF EXISTS(SELECT *
    FROM information_schema.columns
    WHERE table_name='lead_validation_batch_request' and column_name='total_reverted_purchased_credits')
  THEN
      ALTER TABLE "public"."lead_validation_batch_request" RENAME COLUMN "total_reverted_purchased_credits" TO "total_reverted_credits";;
  END IF;;
END $$;;

DO $$
BEGIN
  IF EXISTS(SELECT *
    FROM information_schema.columns
    WHERE table_name='lead_validation_batch_request' and column_name='total_frozen_purchased_credits')
  THEN
      ALTER TABLE "public"."lead_validation_batch_request" RENAME COLUMN "total_frozen_purchased_credits" TO "total_frozen_credits";;
  END IF;;
END $$;;
