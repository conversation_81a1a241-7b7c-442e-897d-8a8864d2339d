# --- !Ups

CREATE TABLE IF NOT EXISTS li_education
(
    id BIGSERIAL PRIMARY KEY,
    li_prospect_data_id BIGSERIAL REFERENCES li_prospect_data(id),
    uuid TEXT,
    account_id BIGINT,
    team_id BIGINT,
    ta_id BIGINT,
    education_no INTEGER,
    degrees_0 TEXT,
    degrees_1 TEXT,
    degrees_2 TEXT,
    degrees_3 TEXT,
    end_date TEXT,
    gpa TEXT,
    majors_0 TEXT,
    majors_1 TEXT,
    majors_2 TEXT,
    majors_3 TEXT,
    majors_4 TEXT,
    minors TEXT,
    minors_0 TEXT,
    minors_1 TEXT,
    school_domain TEXT,
    school_facebook_url TEXT,
    school TEXT,
    school_id TEXT,
    school_linkedin_id TEXT,
    school_linkedin_url TEXT,
    school_location_continent TEXT,
    school_location_country TEXT,
    school_location_locality TEXT,
    school_location_name TEXT,
    school_location_region TEXT,
    school_name TEXT,
    school_twitter_url TEXT,
    school_type TEXT,
    school_website TEXT,
    start_date TEXT,
    summary TEXT,
    degrees TEXT,
    majors TEXT,
    school_location TEXT,
    education_data_hash TEXT
)

# --- !Downs

DROP TABLE li_education;

