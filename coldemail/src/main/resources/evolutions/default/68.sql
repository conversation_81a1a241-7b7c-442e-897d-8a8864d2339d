# --- !Ups

ALTER TABLE emails_received
 ADD COLUMN body_raw TEXT,
 ADD COLUMN only_reply_raw TEXT,
 ADD COLUMN references_header TEXT
 ;

UPDATE emails_received er SET
    body_raw = body,
    only_reply_raw = only_reply,
    references_header = ''
    ;

ALTER TABLE emails_received ALTER COLUMN body_raw SET NOT NULL;
ALTER TABLE emails_received ALTER COLUMN only_reply SET NOT NULL;
ALTER TABLE emails_received ALTER COLUMN only_reply_raw SET NOT NULL;
ALTER TABLE emails_received ALTER COLUMN references_header SET NOT NULL;

# --- !Downs

ALTER TABLE emails_received ALTER COLUMN body_raw DROP NOT NULL;
ALTER TABLE emails_received ALTER COLUMN only_reply DROP NOT NULL;
ALTER TABLE emails_received ALTER COLUMN only_reply_raw DROP NOT NULL;
ALTER TABLE emails_received ALTER COLUMN references_header DROP NOT NULL;


ALTER TABLE emails_received
 DROP COLUMN body_raw,
 DROP COLUMN only_reply_raw,
 DROP COLUMN references_header
 ;
