# --- !Ups

ALTER TABLE csv_queue
    ADD COLUMN IF NOT EXISTS tags TEXT
;


CREATE TABLE IF NOT EXISTS email_finder_search_requests_log (
    id SERIAL PRIMARY KEY,
    account_id INTEGER REFERENCES accounts (id) ON DELETE SET NULL,
    team_id INTEGER REFERENCES teams (id) ON DELETE SET NULL,
    dataplatform_type TEXT,
    request_input jsonb,
    request_output jsonb,
    created_at timestamp with time zone DEFAULT now()
);



# --- !Downs

DROP TABLE IF EXISTS email_finder_search_requests_log;

ALTER TABLE csv_queue
    DROP COLUMN IF EXISTS tags
;