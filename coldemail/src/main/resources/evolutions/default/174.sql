# --- !Ups

CREATE TABLE IF NOT EXISTS prospect_saved_filters
(
    id BIGSERIAL PRIMARY KEY,
    label TEXT NOT NULL,
    filters jsonb,
    account_id BIGINT References accounts(id) on delete cascade NOT NULL,
    team_id BIGINT References teams(id)  on delete cascade NOT NULL,
    ta_id BIGINT References teams_accounts(id)  on delete cascade NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now()
);

ALTER TABLE prospect_saved_filters
DROP CONSTRAINT IF EXISTS no_duplicate_label_constraint,

ADD CONSTRAINT no_duplicate_label_constraint UNIQUE (label, account_id, team_id);

CREATE INDEX IF NOT EXISTS sr_prospect_saved_filters_label ON prospect_saved_filters (label);

CREATE INDEX IF NOT EXISTS sr_prospect_saved_filters_account_id ON prospect_saved_filters (account_id);

CREATE INDEX IF NOT EXISTS sr_prospect_saved_filters_team_id ON prospect_saved_filters (team_id);

CREATE INDEX IF NOT EXISTS sr_prospect_saved_filters_ta_id ON prospect_saved_filters (ta_id);


# --- !Downs

DROP INDEX IF EXISTS sr_prospect_saved_filters_ta_id;

DROP INDEX IF EXISTS sr_prospect_saved_filters_team_id;

DROP INDEX IF EXISTS sr_prospect_saved_filters_account_id;

DROP INDEX IF EXISTS sr_prospect_saved_filters_label;



ALTER TABLE prospect_saved_filters
DROP CONSTRAINT no_duplicate_label_constraint;

DROP TABLE IF EXISTS prospect_saved_filters;
