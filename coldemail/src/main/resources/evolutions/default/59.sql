# --- !Ups

ALTER TABLE team_agency_invites ADD COLUMN inviter_account_id BIGINT REFERENCES accounts (id) ON DELETE CASCADE;

UPDATE team_agency_invites tai SET
 inviter_account_id = (select a.id from accounts a join teams_accounts ta on (ta.account_id = a.id and ta.role = 'admin') where ta.team_id = tai.team_id and tai.team_id is not null limit 1);

ALTER TABLE team_agency_invites ALTER COLUMN inviter_account_id SET NOT NULL;

# --- !Downs

ALTER TABLE team_agency_invites DROP COLUMN inviter_account_id;
