# ---------- !Ups

ALTER TABLE spam_tests
ADD COLUMN IF NOT EXISTS email_domain TEXT;

CREATE INDEX IF NOT EXISTS sr_spam_tests_team_id_email_domain ON spam_tests(team_id, email_domain);

UPDATE spam_tests st SET
email_domain = (SELECT lower(es.email_address_host) FROM email_settings es WHERE es.id = st.email_settings_id)
WHERE
st.email_domain IS NULL;


ALTER TABLE spam_tests ALTER COLUMN email_domain SET NOT NULL;


# -------- !Downs

DROP INDEX IF EXISTS sr_spam_tests_team_id_email_domain;

ALTER TABLE spam_tests
DROP COLUMN IF EXISTS email_domain;