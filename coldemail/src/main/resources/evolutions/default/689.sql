
# --- !Ups

CREATE TABLE IF NOT EXISTS email_validation_api_tool_logs
(
    id BIGSERIAL PRIMARY KEY,
    batch_request_id TEXT ,
    emails_sent_count BIGINT DEFAULT 0 ,
    api_tool TEXT NOT NULL ,
    service_status TEXT NOT NULL ,
    batch_created_at TIMESTAMPTZ ,
    went_down_at TIMESTAMPTZ ,
    batch_completed_at TIMESTAMPTZ ,
    error_at TIMESTAMPTZ ,
    api_batch_created_call_response_status INTEGER ,
    api_batch_created_call_error_response JSONB ,
    deliverable_emails_count BIGINT ,
    risky_emails_count BIGINT ,
    unknown_emails_count BIGINT ,
    invalid_emails BIGINT ,
    tool_downtime BIGINT

);


# --- !Downs
DROP TABLE IF EXISTS email_validation_api_tool_logs;
