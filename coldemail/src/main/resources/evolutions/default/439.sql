# --- !Ups

ALTER TABLE user_roles
DROP COLUMN IF EXISTS manage_billing,
DROP COLUMN IF EXISTS user_management,

DROP COLUMN IF EXISTS view_user_settings,
DROP COLUMN IF EXISTS view_user_settings_ownership,

DROP COLUMN IF EXISTS edit_user_settings,
DROP COLUMN IF EXISTS edit_user_settings_ownership,

DROP COLUMN IF EXISTS view_prospects,
DROP COLUMN IF EXISTS view_prospects_ownership,

DROP COLUMN IF EXISTS edit_prospects,
DROP COLUMN IF EXISTS edit_prospects_ownership,

DROP COLUMN IF EXISTS delete_prospects,
DROP COLUMN IF EXISTS delete_prospects_ownership,

DROP COLUMN IF EXISTS view_campaigns,
DROP COLUMN IF EXISTS view_campaigns_ownership,

DROP COLUMN IF EXISTS edit_campaigns,
DROP COLUMN IF EXISTS edit_campaigns_ownership,

DROP COLUMN IF EXISTS delete_campaigns,
DROP COLUMN IF EXISTS delete_campaigns_ownership,

DROP COLUMN IF EXISTS change_campaign_status,
DROP COLUMN IF EXISTS change_campaign_status_ownership,

DROP COLUMN IF EXISTS view_reports,
DROP COLUMN IF EXISTS view_reports_ownership,

DROP COLUMN IF EXISTS edit_reports,
DROP COLUMN IF EXISTS edit_reports_ownership,

DROP COLUMN IF EXISTS download_reports,
DROP COLUMN IF EXISTS download_reports_ownership,

DROP COLUMN IF EXISTS view_inbox,
DROP COLUMN IF EXISTS view_inbox_ownership,

DROP COLUMN IF EXISTS edit_inbox,
DROP COLUMN IF EXISTS edit_inbox_ownership,

DROP COLUMN IF EXISTS send_manual_email,
DROP COLUMN IF EXISTS send_manual_email_ownership,

DROP COLUMN IF EXISTS view_templates,
DROP COLUMN IF EXISTS view_templates_ownership,

DROP COLUMN IF EXISTS edit_templates,
DROP COLUMN IF EXISTS edit_templates_ownership,

DROP COLUMN IF EXISTS delete_templates,
DROP COLUMN IF EXISTS delete_templates_ownership,

DROP COLUMN IF EXISTS view_blacklist,
DROP COLUMN IF EXISTS view_blacklist_ownership,

DROP COLUMN IF EXISTS edit_blacklist,
DROP COLUMN IF EXISTS edit_blacklist_ownership,

DROP COLUMN IF EXISTS view_email_accounts,
DROP COLUMN IF EXISTS view_email_accounts_ownership,

DROP COLUMN IF EXISTS edit_email_accounts,
DROP COLUMN IF EXISTS edit_email_accounts_ownership,

DROP COLUMN IF EXISTS delete_email_accounts,
DROP COLUMN IF EXISTS delete_email_accounts_ownership,

DROP COLUMN IF EXISTS view_user_management,
DROP COLUMN IF EXISTS edit_user_management,

DROP COLUMN IF EXISTS view_team_config,
DROP COLUMN IF EXISTS edit_team_config,

DROP COLUMN IF EXISTS edit_workflows,
DROP COLUMN IF EXISTS edit_workflows_ownership,

DROP COLUMN IF EXISTS view_workflows,
DROP COLUMN IF EXISTS view_workflows_ownership,

DROP COLUMN IF EXISTS edit_prospect_accounts,
DROP COLUMN IF EXISTS edit_prospect_accounts_ownership,

DROP COLUMN IF EXISTS view_prospect_accounts,
DROP COLUMN IF EXISTS view_prospect_accounts_ownership,

DROP COLUMN IF EXISTS edit_webhooks,
DROP COLUMN IF EXISTS edit_webhooks_ownership,

DROP COLUMN IF EXISTS view_webhooks,
DROP COLUMN IF EXISTS view_webhooks_ownership;

# --- !Downs

ALTER TABLE user_roles
ADD COLUMN IF NOT EXISTS manage_billing BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS user_management BOOLEAN DEFAULT FALSE,

ADD COLUMN IF NOT EXISTS view_user_settings BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS view_user_settings_ownership TEXT,

ADD COLUMN IF NOT EXISTS edit_user_settings BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS edit_user_settings_ownership TEXT,

ADD COLUMN IF NOT EXISTS view_prospects BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS view_prospects_ownership TEXT,

ADD COLUMN IF NOT EXISTS edit_prospects BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS edit_prospects_ownership TEXT,

ADD COLUMN IF NOT EXISTS delete_prospects BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS delete_prospects_ownership TEXT,

ADD COLUMN IF NOT EXISTS view_campaigns BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS view_campaigns_ownership TEXT,

ADD COLUMN IF NOT EXISTS edit_campaigns BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS edit_campaigns_ownership TEXT,

ADD COLUMN IF NOT EXISTS delete_campaigns BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS delete_campaigns_ownership TEXT,

ADD COLUMN IF NOT EXISTS change_campaign_status BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS change_campaign_status_ownership TEXT,

ADD COLUMN IF NOT EXISTS view_reports BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS view_reports_ownership TEXT,

ADD COLUMN IF NOT EXISTS edit_reports BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS edit_reports_ownership TEXT,

ADD COLUMN IF NOT EXISTS download_reports BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS download_reports_ownership TEXT,

ADD COLUMN IF NOT EXISTS view_inbox BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS view_inbox_ownership TEXT,

ADD COLUMN IF NOT EXISTS edit_inbox BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS edit_inbox_ownership TEXT,

ADD COLUMN IF NOT EXISTS send_manual_email BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS send_manual_email_ownership TEXT,

ADD COLUMN IF NOT EXISTS view_templates BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS view_templates_ownership TEXT,

ADD COLUMN IF NOT EXISTS edit_templates BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS edit_templates_ownership TEXT,

ADD COLUMN IF NOT EXISTS delete_templates BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS delete_templates_ownership TEXT,

ADD COLUMN IF NOT EXISTS view_blacklist BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS view_blacklist_ownership TEXT,

ADD COLUMN IF NOT EXISTS edit_blacklist BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS edit_blacklist_ownership TEXT,

ADD COLUMN IF NOT EXISTS view_email_accounts BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS view_email_accounts_ownership TEXT,

ADD COLUMN IF NOT EXISTS edit_email_accounts BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS edit_email_accounts_ownership TEXT,

ADD COLUMN IF NOT EXISTS delete_email_accounts BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS delete_email_accounts_ownership TEXT,

ADD COLUMN IF NOT EXISTS view_user_management BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS edit_user_management BOOLEAN DEFAULT FALSE,

ADD COLUMN IF NOT EXISTS view_team_config BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS edit_team_config BOOLEAN DEFAULT FALSE,

ADD COLUMN IF NOT EXISTS edit_workflows BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS edit_workflows_ownership TEXT,

ADD COLUMN IF NOT EXISTS view_workflows BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS view_workflows_ownership TEXT,

ADD COLUMN IF NOT EXISTS edit_prospect_accounts BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS edit_prospect_accounts_ownership TEXT,

ADD COLUMN IF NOT EXISTS view_prospect_accounts BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS view_prospect_accounts_ownership TEXT,

ADD COLUMN IF NOT EXISTS edit_webhooks BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS edit_webhooks_ownership TEXT,

ADD COLUMN IF NOT EXISTS view_webhooks BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS view_webhooks_ownership TEXT;