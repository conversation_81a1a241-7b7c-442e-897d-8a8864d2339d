# --- !Ups

CREATE TABLE IF NOT EXISTS sr_pricing_plans
(
    id INT PRIMARY KEY,
    sr_plan_name TEXT NOT NULL,
    sr_plan_id TEXT NOT NULL,
    is_business BOOLEAN NOT NULL,
    plan_interval TEXT NOT NULL,
    base_licences INT NOT NULL,

    stripe_base_plan_id TEXT NOT NULL,
    stripe_additional_plan_id TEXT,

    fastspring_base_plan_id TEXT,
    fastspring_additional_plan_id TEXT,


    currency TEXT NOT NULL,
    base_price INT NOT NULL,
    additional_licence_price INT,
    active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE UNIQUE INDEX IF NOT EXISTS sr_pricing_plans_sr_plan_name_unique ON sr_pricing_plans ((lower(sr_plan_name)));

CREATE UNIQUE INDEX IF NOT EXISTS sr_pricing_plans_stripe_base_plan_id_unique ON sr_pricing_plans ((lower(stripe_base_plan_id)));

CREATE UNIQUE INDEX IF NOT EXISTS sr_pricing_plans_stripe_additional_plan_id_unique ON sr_pricing_plans ((lower(stripe_additional_plan_id))) where stripe_additional_plan_id is not null;

CREATE UNIQUE INDEX IF NOT EXISTS sr_pricing_plans_fastspring_base_plan_id_unique ON sr_pricing_plans ((lower(fastspring_base_plan_id))) where fastspring_base_plan_id is not null;

CREATE UNIQUE INDEX IF NOT EXISTS sr_pricing_plans_fastspring_additional_plan_id_unique ON sr_pricing_plans ((lower(fastspring_additional_plan_id))) where fastspring_additional_plan_id is not null;


ALTER TABLE organizations
add column if not exists base_licence_count int not null default 1,
add column if not exists additional_licence_count int not null default 0,
add column if not exists sr_pricing_plan_id int references sr_pricing_plans(id),
drop column if exists max_team_members,
drop column if exists additional_sending_email_accounts
;

update organizations set
base_licence_count = total_sending_email_accounts
where base_licence_count != total_sending_email_accounts
;



# --- !Downs

ALTER TABLE organizations
drop column if exists base_licence_count,
drop column if exists additional_licence_count,
drop column if exists sr_pricing_plan_id,
add column if not exists max_team_members INT DEFAULT 1,
add column if not exists additional_sending_email_accounts INT DEFAULT 0
;

DROP INDEX IF EXISTS sr_pricing_plans_sr_plan_name_unique;

DROP INDEX IF EXISTS sr_pricing_plans_stripe_base_plan_id_unique;

DROP INDEX IF EXISTS sr_pricing_plans_stripe_additional_plan_id_unique;

DROP INDEX IF EXISTS sr_pricing_plans_fastspring_base_plan_id_unique;

DROP INDEX IF EXISTS sr_pricing_plans_fastspring_additional_plan_id_unique;

drop table if exists sr_pricing_plans;
