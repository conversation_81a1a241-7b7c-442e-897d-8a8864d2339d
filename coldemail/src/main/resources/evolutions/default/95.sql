# --- !Ups

CREATE TABLE csv_queue
(
    id BIGSERIAL PRIMARY KEY,
    file_name TEXT NOT NULL,
    file_url TEXT NOT NULL,
    account_id INT References accounts(id) on delete cascade NOT NULL,
    team_id INT References teams(id)  on delete cascade NOT NULL,
    ta_id INT NOT NULL,
    campaign_id INT References campaigns(id) on delete cascade,
    created_at TIMESTAMPTZ DEFAULT now(),
    uploaded_at TIMESTAMPTZ,
    has_been_uploaded BOOLEAN DEFAULT FALSE NOT NULL,
    column_map jsonb NOT NULL,
    force_update_prospects BOOLEAN DEFAULT FALSE NOT NULL,
    error TEXT,
    error_at TIMESTAMPTZ
);

# --- !Downs

DROP TABLE csv_queue;