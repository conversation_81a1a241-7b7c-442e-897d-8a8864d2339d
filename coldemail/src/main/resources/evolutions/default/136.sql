# --- !Ups

ALTER TABLE emails_scheduled
    ADD COLUMN IF NOT EXISTS email_thread_id BIGINT REFERENCES email_threads (id) ON DELETE SET NULL,
    ADD COLUMN IF NOT EXISTS receiving_email_settings_id BIGINT REFERENCES email_settings (id) ON DELETE SET NULL,
    ADD COLUMN IF NOT EXISTS full_headers jsonb,
    ADD COLUMN IF NOT EXISTS scheduled_manually boolean,
    ADD COLUMN IF NOT EXISTS sr_inbox_read BOOLEAN,
    ADD COLUMN IF NOT EXISTS gmail_msg_id TEXT,
    ADD COLUMN IF NOT EXISTS gmail_thread_id TEXT,
    ADD COLUMN IF NOT EXISTS original_inbox_folder TEXT,
    ADD COLUMN IF NOT EXISTS cc_emails TEXT,
    ADD COLUMN IF NOT EXISTS bcc_emails TEXT,
    ADD COLUMN IF NOT EXISTS in_reply_to_header TEXT,
    ADD COLUMN IF NOT EXISTS team_id BIGINT REFERENCES teams (id) ON DELETE SET NULL,
    ADD COLUMN IF NOT EXISTS to_name TEXT,
    ADD COLUMN IF NOT EXISTS text_body TEXT,
    ADD COLUMN IF NOT EXISTS internal_tracking_note TEXT,
    ADD COLUMN IF NOT EXISTS reply_to_name TEXT

    ;

-- NOTE: I ran this below statement manually
-- also changed prospect_id column to ON DELETE SET NULL instead of ON DELETE CASCADE for
-- emails_scheduled, emails_received and email_threads columns
-- ALTER TABLE emails_scheduled ALTER COLUMN prospect_id DROP NOT NULL;



ALTER TABLE emails_received
    ADD COLUMN IF NOT EXISTS from_name TEXT,
    ADD COLUMN IF NOT EXISTS to_name TEXT,

    ADD COLUMN IF NOT EXISTS gmail_msg_id TEXT,
    ADD COLUMN IF NOT EXISTS gmail_thread_id TEXT,
    ADD COLUMN IF NOT EXISTS original_inbox_folder TEXT,
    ADD COLUMN IF NOT EXISTS cc_emails TEXT,
    ADD COLUMN IF NOT EXISTS in_reply_to_header TEXT,
    ADD COLUMN IF NOT EXISTS team_id BIGINT REFERENCES teams (id) ON DELETE SET NULL
    ;


ALTER TABLE email_threads
    ADD COLUMN IF NOT EXISTS gmail_msg_id TEXT,
    ADD COLUMN IF NOT EXISTS gmail_thread_id TEXT,
    ADD COLUMN IF NOT EXISTS latest_email_id BIGINT REFERENCES emails_scheduled (id) ON DELETE SET NULL,
    ADD COLUMN IF NOT EXISTS admin_email TEXT,
    ADD COLUMN IF NOT EXISTS prospect_email TEXT,
    ADD COLUMN IF NOT EXISTS internal_tracking_note TEXT,
    ADD COLUMN IF NOT EXISTS temp_thread_id INTEGER,
    ADD COLUMN IF NOT EXISTS team_id BIGINT REFERENCES teams (id) ON DELETE SET NULL,
    ADD COLUMN IF NOT EXISTS sr_read BOOLEAN DEFAULT FALSE,
    ADD COLUMN IF NOT EXISTS archived BOOLEAN DEFAULT FALSE,
    ADD COLUMN IF NOT EXISTS latest_reply_at TIMESTAMPTZ

    ;

CREATE UNIQUE INDEX IF NOT EXISTS prospect_step_once_from_campaign ON emails_scheduled (prospect_id, step_id) WHERE prospect_id IS NOT NULL AND step_id IS NOT NULL AND scheduled_from_campaign = TRUE;

-- for fetchProspects query left join
CREATE INDEX IF NOT EXISTS sr_emails_scheduled_prospect_id_scheduled_from_campaign ON emails_scheduled (prospect_id, scheduled_from_campaign)
WHERE scheduled_from_campaign = TRUE;


CREATE UNIQUE INDEX IF NOT EXISTS sr_emails_scheduled_message_id_team_id_unique ON emails_scheduled (team_id, message_id)
WHERE team_id IS NOT NULL;


CREATE INDEX IF NOT EXISTS sr_email_threads_campaign_id ON email_threads (campaign_id);

CREATE INDEX IF NOT EXISTS sr_csv_queue_campaign_id ON csv_queue (campaign_id);

CREATE INDEX IF NOT EXISTS sr_csv_queue_team_id ON csv_queue (team_id);

CREATE INDEX IF NOT EXISTS sr_csv_queue_account_id ON csv_queue (account_id);

CREATE INDEX IF NOT EXISTS sr_prospect_events_step_id ON prospect_events (step_id);

CREATE INDEX IF NOT EXISTS sr_emails_scheduled_step_id ON emails_scheduled (step_id);

CREATE INDEX IF NOT EXISTS sr_emails_scheduled_receiving_email_settings_id ON emails_scheduled (receiving_email_settings_id);

CREATE INDEX IF NOT EXISTS sr_emails_scheduled_email_thread_id ON emails_scheduled (email_thread_id);

CREATE INDEX IF NOT EXISTS sr_emails_scheduled_team_id ON emails_scheduled (team_id);

create index if not exists sr_prospects_team_id on prospects (team_id);

create index if not exists sr_email_threads_team_id on email_threads(team_id);
create index if not exists sr_email_threads_gmail_thread_id on email_threads(gmail_thread_id);

DROP INDEX IF EXISTS prospect_step_once;


# --- !Downs


CREATE UNIQUE INDEX IF NOT EXISTS prospect_step_once
  ON emails_scheduled (prospect_id, step_id);


DROP INDEX IF EXISTS sr_email_threads_gmail_thread_id;
DROP INDEX IF EXISTS sr_email_threads_team_id;

DROP INDEX IF EXISTS sr_prospects_team_id;

DROP INDEX IF EXISTS sr_emails_scheduled_team_id;
DROP INDEX IF EXISTS sr_emails_scheduled_email_thread_id;
DROP INDEX IF EXISTS sr_emails_scheduled_receiving_email_settings_id;
DROP INDEX IF EXISTS sr_emails_scheduled_step_id;
DROP INDEX IF EXISTS sr_prospect_events_step_id;

DROP INDEX IF EXISTS sr_csv_queue_account_id;
DROP INDEX IF EXISTS sr_csv_queue_team_id;
DROP INDEX IF EXISTS sr_csv_queue_campaign_id;

DROP INDEX IF EXISTS sr_email_threads_campaign_id;


DROP INDEX IF EXISTS sr_emails_scheduled_message_id_team_id_unique;

DROP INDEX IF EXISTS sr_emails_scheduled_prospect_id_scheduled_from_campaign;


DROP INDEX IF EXISTS prospect_step_once_from_campaign;


ALTER TABLE email_threads
    DROP COLUMN IF EXISTS gmail_msg_id,
    DROP COLUMN IF EXISTS gmail_thread_id,
    DROP COLUMN IF EXISTS latest_email_id,
    DROP COLUMN IF EXISTS admin_email,
    DROP COLUMN IF EXISTS prospect_email,
    DROP COLUMN IF EXISTS internal_tracking_note,
    DROP COLUMN IF EXISTS temp_thread_id,
    DROP COLUMN IF EXISTS team_id,
    DROP COLUMN IF EXISTS sr_read,
    DROP COLUMN IF EXISTS archived,
    DROP COLUMN IF EXISTS latest_reply_at
    ;

ALTER TABLE emails_received
    DROP COLUMN IF EXISTS gmail_msg_id,
    DROP COLUMN IF EXISTS gmail_thread_id,
    DROP COLUMN IF EXISTS original_inbox_folder,
    DROP COLUMN IF EXISTS cc_emails,
    DROP COLUMN IF EXISTS in_reply_to_header,
    DROP COLUMN IF EXISTS team_id,

    DROP COLUMN IF EXISTS to_name,
    DROP COLUMN IF EXISTS from_name
    ;



ALTER TABLE emails_scheduled
    DROP COLUMN IF EXISTS email_thread_id,
    DROP COLUMN IF EXISTS receiving_email_settings_id,
    DROP COLUMN IF EXISTS full_headers,
    DROP COLUMN IF EXISTS scheduled_manually,
    DROP COLUMN IF EXISTS sr_inbox_read,
    DROP COLUMN IF EXISTS gmail_msg_id,
    DROP COLUMN IF EXISTS gmail_thread_id,
    DROP COLUMN IF EXISTS original_inbox_folder,
    DROP COLUMN IF EXISTS cc_emails,
    DROP COLUMN IF EXISTS bcc_emails,
    DROP COLUMN IF EXISTS in_reply_to_header,
    DROP COLUMN IF EXISTS team_id,
    DROP COLUMN IF EXISTS to_name,
    DROP COLUMN IF EXISTS text_body,
    DROP COLUMN IF EXISTS reply_to_name
    ;
