# --- !Ups

CREATE INDEX IF NOT EXISTS
sr_email_threads_teamid_latest_sent_by_admin_at_desc_nulllast
ON email_threads (team_id, latest_sent_by_admin_at desc NULLS LAST)
;

CREATE INDEX IF NOT EXISTS
sr_email_threads_teamid_latest_sent_by_admin_at
ON email_threads (team_id, latest_sent_by_admin_at)
WHERE latest_sent_by_admin_at IS NOT NULL
;

# --- !Downs


DROP INDEX IF EXISTS sr_email_threads_teamid_latest_sent_by_admin_at;

DROP INDEX IF EXISTS sr_email_threads_teamid_latest_sent_by_admin_at_desc_nulllast;


