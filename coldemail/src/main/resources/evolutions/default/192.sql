# --- !Ups

ALTER TABLE prospect_saved_filters
  ADD COLUMN IF NOT EXISTS shared_with_team BOOLEAN DEFAULT FALSE;



ALTER TABLE teams_accounts
  ALTER COLUMN max_emails_per_prospect_per_day SET DEFAULT 1,
  ALTER COLUMN max_emails_per_prospect_per_week SET DEFAULT 3;



ALTER TABLE teams
  ADD COLUMN IF NOT EXISTS max_emails_per_prospect_per_day INTEGER,
  ADD COLUMN IF NOT EXISTS max_emails_per_prospect_per_week INTEGER;



UPDATE teams t SET
  max_emails_per_prospect_per_day = (SELECT MAX(ta.max_emails_per_prospect_per_day) FROM teams_accounts ta WHERE ta.team_id = t.id LIMIT 1),
  max_emails_per_prospect_per_week = (SELECT MAX(ta.max_emails_per_prospect_per_week) FROM teams_accounts ta WHERE ta.team_id = t.id LIMIT 1)
  ;


ALTER TABLE teams ALTER COLUMN max_emails_per_prospect_per_day SET NOT NULL;
ALTER TABLE teams ALTER COLUMN max_emails_per_prospect_per_week SET NOT NULL;


# --- !Downs

ALTER TABLE teams
  ALTER COLUMN max_emails_per_prospect_per_day DROP NOT NULL,
  ALTER COLUMN max_emails_per_prospect_per_week DROP NOT NULL;



ALTER TABLE teams
  DROP COLUMN IF EXISTS max_emails_per_prospect_per_day,
  DROP COLUMN IF EXISTS max_emails_per_prospect_per_week;



ALTER TABLE prospect_saved_filters
  DROP COLUMN IF EXISTS shared_with_team;