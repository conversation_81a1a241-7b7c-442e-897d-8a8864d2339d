# --- !Ups

DROP INDEX IF EXISTS sr_emails_scheduled_message_id_team_id_unique;


create index if not exists sr_campaigns_prospects_replied_marked_by_adminid on campaigns_prospects (replied_marked_by_adminid) where replied_marked_by_adminid is not null;

create index if not exists sr_emails_scheduled_replied_marked_by_adminid on emails_scheduled (replied_marked_by_adminid) where replied_marked_by_adminid is not null;


# --- !Downs

drop index if exists sr_campaigns_prospects_replied_marked_by_adminid;

drop index if exists sr_emails_scheduled_replied_marked_by_adminid;
