# --- !Ups


ALTER TABLE accounts
  DROP COLUMN api_key,
  DROP COLUMN max_emails_per_prospect_per_day,
  DROP COLUMN max_emails_per_prospect_per_week;



# --- !Downs


ALTER TABLE teams_accounts
  ADD COLUMN api_key TEXT UNIQUE,
  ADD COLUMN max_emails_per_prospect_per_day INTEGER,
  ADD COLUMN max_emails_per_prospect_per_week INTEGER;


UPDATE accounts a SET
  api_key = (SELECT ta.api_key FROM teams_accounts ta WHERE ta.account_id = a.id LIMIT 1),
  max_emails_per_prospect_per_day = (SELECT ta.max_emails_per_prospect_per_day FROM teams_accounts ta WHERE ta.account_id = a.id LIMIT 1),
  max_emails_per_prospect_per_week = (SELECT ta.max_emails_per_prospect_per_week FROM teams_accounts ta WHERE ta.account_id = a.id LIMIT 1)
  ;


ALTER TABLE accounts ALTER COLUMN api_key SET NOT NULL;
ALTER TABLE accounts ALTER COLUMN max_emails_per_prospect_per_day SET NOT NULL;
ALTER TABLE accounts ALTER COLUMN max_emails_per_prospect_per_week SET NOT NULL;

