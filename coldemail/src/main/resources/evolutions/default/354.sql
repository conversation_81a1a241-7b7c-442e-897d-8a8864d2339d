# --- !Ups
ALTER TABLE custom_tracking_domain_records
    ADD COLUMN IF NOT EXISTS cloudflare_identifier TEXT,
    ADD COLUMN IF NOT EXISTS  last_tried_for_activate TIMESTAMPTZ
    ;

ALTER TABLE rep_tracking_hosts
    ADD COLUMN IF NOT EXISTS cloudflare_zone_id TEXT
    ;



# --- !Downs

ALTER TABLE rep_tracking_hosts
    DROP COLUMN IF EXISTS cloudflare_zone_id
    ;

ALTER TABLE custom_tracking_domain_records
    DROP COLUMN IF EXISTS cloudflare_identifier,
    DROP COLUMN IF EXISTS last_tried_for_activate
    ;