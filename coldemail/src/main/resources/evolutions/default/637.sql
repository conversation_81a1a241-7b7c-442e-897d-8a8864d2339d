# --- !Ups

CREATE UNIQUE INDEX IF NOT EXISTS sr_audit_workflow_attempt_logs_t_id_eli ON audit_workflow_attempt_logs(team_id, event_log_id, created_at);
-- had to add created_at because it is part of the partition key.

CREATE UNIQUE INDEX IF NOT EXISTS sr_audit_workflow_attempt_logs_t_id_wcs_id ON audit_workflow_attempt_logs(team_id, workflow_crm_setting_id, created_at);
-- had to add created_at because it is part of the partition key.

# --- !Downs

DROP INDEX IF EXISTS sr_audit_workflow_attempt_logs_t_id_eli;

DROP INDEX IF EXISTS sr_audit_workflow_attempt_logs_t_id_wcs_id;
