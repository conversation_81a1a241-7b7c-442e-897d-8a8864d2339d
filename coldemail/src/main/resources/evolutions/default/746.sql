# --- !Ups

CREATE TABLE IF NOT EXISTS gdpr_blacklist_requests
(
    id SERIAL PRIMARY KEY,
    email TEXT NOT NULL,
    domain TEXT NOT NULL,
    verification_code TEXT,
    verification_done BOOL DEFAULT false NOT NULL,
    verification_done_at TIMESTAMPTZ,
    added_at TIMESTAMPTZ DEFAULT now() NOT NULL
);
CREATE UNIQUE INDEX IF NOT EXISTS sr_gdpr_blacklist_requests_email_unique ON gdpr_blacklist_requests ((lower(email)));

# --- !Downs

DROP TABLE IF EXISTS gdpr_blacklist_requests;