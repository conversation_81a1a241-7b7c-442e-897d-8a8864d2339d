# --- !Ups

<PERSON><PERSON><PERSON> TABLE campaigns ADD CONSTRAINT unique_campaign_name UNIQUE (name, account_id);

ALTER TABLE email_settings RENAME sender_service_provider TO service_provider;

ALTER TABLE email_settings RENAME sender_oauth2_access_token TO oauth2_access_token;
ALTER TABLE email_settings RENAME sender_oauth2_refresh_token TO oauth2_refresh_token;
ALTER TABLE email_settings RENAME sender_oauth2_token_type TO oauth2_token_type;
ALTER TABLE email_settings RENAME sender_oauth2_token_expires_in TO oauth2_token_expires_in;


ALTER TABLE email_settings
  DROP COLUMN receiver_service_provider,
  DROP COLUMN receiver_oauth2_access_token,
  DROP COLUMN receiver_oauth2_refresh_token,
  DROP COLUMN receiver_oauth2_token_type,
  DROP COLUMN receiver_oauth2_token_expires_in;


ALTER TABLE email_settings
  ALTER COLUMN smtp_host DROP NOT NULL,
  ALTER COLUMN smtp_port DROP NOT NULL,
  ALTER COLUMN smtp_enable_ssl DROP NOT NULL,
  ALTER COLUMN imap_host DROP NOT NULL,
  ALTER COLUMN imap_port DROP NOT NULL,
  ALTER COLUMN imap_enable_ssl DROP NOT NULL;


ALTER TABLE campaigns RENAME email_settings_id TO sender_email_settings_id;

ALTER TABLE campaigns
  ADD COLUMN receiver_email_settings_id INTEGER REFERENCES email_settings (id) NULL;


# --- !Downs

ALTER TABLE campaigns RENAME sender_email_settings_id TO email_settings_id;

ALTER TABLE campaigns
  DROP COLUMN receiver_email_settings_id;



ALTER TABLE email_settings
  ALTER COLUMN smtp_host SET NOT NULL,
  ALTER COLUMN smtp_port SET NOT NULL,
  ALTER COLUMN smtp_enable_ssl SET NOT NULL,
  ALTER COLUMN imap_host SET NOT NULL,
  ALTER COLUMN imap_port SET NOT NULL,
  ALTER COLUMN imap_enable_ssl SET NOT NULL;


ALTER TABLE email_settings
  ADD COLUMN receiver_service_provider TEXT,
  ADD COLUMN receiver_oauth2_access_token TEXT,
  ADD COLUMN receiver_oauth2_refresh_token TEXT,
  ADD COLUMN receiver_oauth2_token_type TEXT,
  ADD COLUMN receiver_oauth2_token_expires_in TEXT;


UPDATE email_settings SET receiver_service_provider = 'gmail';

ALTER TABLE email_settings ALTER COLUMN receiver_service_provider SET NOT NULL;


ALTER TABLE email_settings RENAME oauth2_access_token TO sender_oauth2_access_token;
ALTER TABLE email_settings RENAME oauth2_refresh_token TO sender_oauth2_refresh_token;
ALTER TABLE email_settings RENAME oauth2_token_type TO sender_oauth2_token_type;
ALTER TABLE email_settings RENAME oauth2_token_expires_in TO sender_oauth2_token_expires_in;


ALTER TABLE email_settings RENAME service_provider TO sender_service_provider;

ALTER TABLE campaigns DROP CONSTRAINT unique_campaign_name;
