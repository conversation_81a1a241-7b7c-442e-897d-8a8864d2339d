# --- !Ups

create index if not exists sr_prospects_emails_tid_email_domain_lower on prospects_emails(team_id, lower(email_domain));

create index if not exists sr_prospects_emails_tid_email_checked on prospects_emails(team_id, email_checked);

create index if not exists sr_prospects_emails_tid_email_format_valid on prospects_emails(team_id, email_format_valid);

create index if not exists sr_prospects_emails_tid_accid_invalid_email on prospects_emails(team_id, account_id, invalid_email);


# --- !Downs

drop index if exists sr_prospects_emails_tid_email_domain_lower;

drop index if exists sr_prospects_emails_tid_email_checked;

drop index if exists sr_prospects_emails_tid_email_format_valid;

drop index if exists sr_prospects_emails_tid_accid_invalid_email;