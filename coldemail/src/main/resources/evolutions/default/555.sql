# --- !Ups

CREATE TABLE IF NOT EXISTS pipelines (
	id bigserial PRIMARY KEY,
	uuid text UNIQUE NOT NULL,
	pipeline_name text NOT NULL,
	owner_id bigint REFERENCES accounts (id) NOT NULL,
	currency text NOT NULL,
	team_id bigint REFERENCES teams (id) ON DELETE CASCADE NOT NULL,
	updated_at timestamptz NOT NULL DEFAULT now(),
	created_at timestamptz NOT NULL DEFAULT now()
);

CREATE INDEX IF NOT EXISTS sr_pipelines_owner_id ON pipelines (owner_id);

CREATE UNIQUE INDEX IF NOT EXISTS sr_pipelines_team_id_pipeline_name ON pipelines (team_id, pipeline_name);

# --- !Downs

DROP INDEX IF EXISTS sr_pipelines_team_id_pipeline_name;

DROP INDEX IF EXISTS sr_pipelines_owner_id;

DROP TABLE IF EXISTS pipelines;
