# --- !Ups
ALTER TABLE accounts
ADD COLUMN IF NOT EXISTS calendar_user_id INTEGER;

ALTER TABLE teams
ADD COLUMN IF NOT EXISTS calendar_team_id INTEGER;

-- USING integer type instead of bigInt because was getting this error  TypeError: Do not know how to serialize a BigInt on prisma side
-- refer this for more https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields#working-with-bigint

CREATE UNIQUE INDEX IF NOT EXISTS sr_accounts_calendar_user_id ON accounts(calendar_user_id) WHERE calendar_user_id IS NOT NULL;
CREATE UNIQUE INDEX IF NOT EXISTS sr_accounts_calendar_team_id ON teams(calendar_team_id) WHERE calendar_team_id IS  NOT NULL;

# --- !Downs


DROP INDEX IF EXISTS sr_accounts_calendar_team_id;
DROP INDEX IF EXISTS sr_accounts_calendar_user_id;


ALTER TABLE teams
DROP COLUMN IF EXISTS calendar_team_id;

ALTER TABLE accounts
DROP COLUMN IF EXISTS calendar_user_id;