# --- !Ups
CREATE TYPE campaign_health_type AS ENUM(
	'all_GRN', 
	-- 1 ORG, 2 others GRN
	'dlvrbl_GRN_risky_ORG_unknwn_GRN',
	'dlvrbl_GRN_risky_GRN_unknwn_ORG',
	'dlvrbl_ORG_risky_GRN_unknwn_GRN', -- should rarely if ever occur
	-- 2 ORG, 1 other GRN
	'dlvrbl_GRN_risky_ORG_unknwn_ORG',
	'dlvrbl_ORG_risky_GRN_unknwn_ORG',
	'dlvrbl_ORG_risky_ORG_unknwn_GRN', 
	-- something GRN,ORG,RED
	'dlvrbl_GRN_risky_ORG_unknwn_RED',
	'dlvrbl_GRN_risky_RED_unknwn_ORG',

	'dlvrbl_ORG_risky_GRN_unknwn_RED',
	'dlvrbl_RED_risky_GRN_unknwn_ORG',

	'dlvrbl_ORG_risky_RED_unknwn_GRN',
	'dlvrbl_RED_risky_ORG_unknwn_GRN',
	-- 2 ORG, 1RED
	'dlvrbl_ORG_risky_ORG_unknwn_RED',
	'dlvrbl_ORG_risky_RED_unknwn_ORG',
	'dlvrbl_RED_risky_ORG_unknwn_ORG',
	-- 2 RED, 1 ORG
	'dlvrbl_ORG_risky_RED_unknwn_RED',
	'dlvrbl_RED_risky_RED_unknwn_ORG',
	'dlvrbl_RED_risky_ORG_unknwn_RED',
	-- 3 red
	'all_RED' 
);

# --- !Downs

DROP TYPE            campaign_health_type;
