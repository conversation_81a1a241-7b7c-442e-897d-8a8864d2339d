# --- !Ups
CREATE TABLE dkim_records
(
    id BIGSERIAL PRIMARY KEY,
    domain TEXT NOT NULL,
    dkim_selector TEXT NOT NULL,
    dkim_record TEXT NOT NULL,
    public_key TEXT NOT NULL,
    private_key TEXT NOT NULL,
    account_id INTEGER REFERENCES accounts (id) ON DELETE SET NULL,
    account_name TEXT,
    account_email TEXT,
    active BOOLEAN DEFAULT FALSE,
    activated_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now(),
    error TEXT,
    error_reported_at TIMESTAMPTZ,
    error_produced_email_settings_id INTEGER REFERENCES email_settings (id) ON DELETE SET NULL
);



# --- !Downs

DROP TABLE dkim_records;
