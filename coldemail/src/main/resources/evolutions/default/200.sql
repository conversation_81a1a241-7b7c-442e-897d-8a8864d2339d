# --- !Ups


ALTER TABLE triggers
ALTER COLUMN event DROP NOT NULL,
ALTER COLUMN actions DROP NOT NULL,
ADD COLUMN IF NOT EXISTS shared_with_team BOOLEAN DEFAULT FALSE
;


ALTER TABLE teams
ADD COLUMN IF NOT EXISTS hubspot_field_mapping jsonb,
ADD COLUMN IF NOT EXISTS pipedrive_field_mapping jsonb,
ADD COLUMN IF NOT EXISTS zoho_field_mapping jsonb,
ADD COLUMN IF NOT EXISTS salesforce_field_mapping jsonb
;

ALTER TABLE teams_accounts
ADD COLUMN IF NOT EXISTS salesforce_oauth2_access_token TEXT,
ADD COLUMN IF NOT EXISTS salesforce_oauth2_refresh_token TEXT,
ADD COLUMN IF NOT EXISTS pipedrive_company_id TEXT,
ADD COLUMN IF NOT EXISTS pipedrive_user_id TEXT,
ADD COLUMN IF NOT EXISTS zoho_api_domain TEXT
;


CREATE TABLE IF NOT EXISTS triggers_team_accounts
(
    trigger_id BIGINT REFERENCES triggers(id) ON DELETE CASCADE NOT NULL,
    account_id BIGINT REFERENCES accounts(id) ON DELETE CASCADE  NOT NULL,
    team_id BIGINT REFERENCES teams(id) ON DELETE CASCADE NOT NULL,
    error TEXT,
    error_at TIMESTAMPTZ,
    last_ran_at TIMESTAMPTZ
);

CREATE UNIQUE INDEX IF NOT EXISTS triggers_team_accounts_trigger_id_account_id_team_id ON triggers_team_accounts (trigger_id, account_id, team_id);


# --- !Downs

DROP INDEX IF EXISTS triggers_team_accounts_trigger_id_account_id_team_id;

DROP TABLE IF EXISTS triggers_team_accounts;

ALTER TABLE teams_accounts
DROP COLUMN IF EXISTS salesforce_oauth2_access_token,
DROP COLUMN IF EXISTS salesforce_oauth2_refresh_token,
DROP COLUMN IF EXISTS pipedrive_company_id,
DROP COLUMN IF EXISTS pipedrive_user_id,
DROP COLUMN IF EXISTS zoho_api_domain
;

ALTER TABLE teams
DROP COLUMN IF EXISTS hubspot_field_mapping,
DROP COLUMN IF EXISTS pipedrive_field_mapping,
DROP COLUMN IF EXISTS zoho_field_mapping,
DROP COLUMN IF EXISTS salesforce_field_mapping
;

ALTER TABLE triggers
DROP COLUMN IF EXISTS shared_with_team
;


 
