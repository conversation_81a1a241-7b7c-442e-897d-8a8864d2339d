# --- !Ups

ALTER TABLE audit_workflow_attempt_logs
    ADD COLUMN IF NOT EXISTS crm_type TEXT,
    ADD COLUMN IF NOT EXISTS module_type TEXT,
    ADD COLUMN IF NOT EXISTS workflow_crm_setting_id bigint REFERENCES workflow_crm_settings(id) ON DELETE SET NULL;


CREATE INDEX IF NOT EXISTS sr_audit_workflow_attempt_logs_wf_crm_s_id ON audit_workflow_attempt_logs(workflow_crm_setting_id);

# --- !Downs

DROP INDEX IF EXISTS sr_audit_workflow_attempt_logs_wf_crm_s_id;

ALTER TABLE audit_workflow_attempt_logs
    DROP COLUMN IF EXISTS crm_type,
    DROP COLUMN IF EXISTS module_type,
    DROP COLUMN IF EXISTS workflow_crm_setting_id;
