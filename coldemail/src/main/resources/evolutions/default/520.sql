
# --- !Ups

ALTER TABLE call_conference_logs
DROP COLUMN IF EXISTS feedback_type,
DROP COLUMN IF EXISTS feedback_reason,
DROP COLUMN IF EXISTS feedback_description,
DROP COLUMN IF EXISTS feedback_at;

ALTER TABLE call_participants_logs
ADD COLUMN IF NOT EXISTS feedback_type TEXT,
ADD COLUMN IF NOT EXISTS feedback_reason TEXT,
ADD COLUMN IF NOT EXISTS feedback_description TEXT,
ADD COLUMN IF NOT EXISTS feedback_at TIMESTAMPTZ;

# --- !Downs

ALTER TABLE call_conference_logs
ADD COLUMN IF NOT EXISTS feedback_type TEXT,
ADD COLUMN IF NOT EXISTS feedback_reason TEXT,
ADD COLUMN IF NOT EXISTS feedback_description TEXT,
ADD COLUMN IF NOT EXISTS feedback_at TIMESTAMPTZ;


ALTER TABLE call_participants_logs
DROP COLUMN IF EXISTS feedback_type,
DROP COLUMN IF EXISTS feedback_reason,
DROP COLUMN IF EXISTS feedback_description,
DROP COLUMN IF EXISTS feedback_at;


