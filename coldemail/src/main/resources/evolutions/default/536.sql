# --- !Ups

ALTER TABLE teams
ADD COLUMN IF NOT EXISTS phantombuster_team_secret_key TEXT;

ALTER TABLE teams
DROP COLUMN IF EXISTS phantombuster_linkedin_auto_connect_agent_id,
DROP COLUMN IF EXISTS phantombuster_linkedin_message_sender_agent_id,
DROP COLUMN IF EXISTS phantombuster_linkedin_view_profile_agent_id,
DROP COLUMN IF EXISTS phantombuster_sales_navigator_inmail_agent_id,
DROP COLUMN IF EXISTS phantombuster_linkedin_message_thread_scraper_agent_id,
DROP COLUMN IF EXISTS phantombuster_linkedin_inbox_scraper_agent_id;

# --- !Downs

ALTER TABLE teams
DROP COLUMN IF EXISTS phantombuster_team_secret_key;

ALTER TABLE teams
ADD COLUMN IF NOT EXISTS phantombuster_linkedin_auto_connect_agent_id TEXT,
ADD COLUMN IF NOT EXISTS phantombuster_linkedin_message_sender_agent_id TEXT,
ADD COLUMN IF NOT EXISTS phantombuster_linkedin_view_profile_agent_id TEXT,
ADD COLUMN IF NOT EXISTS phantombuster_sales_navigator_inmail_agent_id TEXT,
ADD COLUMN IF NOT EXISTS phantombuster_linkedin_message_thread_scraper_agent_id TEXT,
ADD COLUMN IF NOT EXISTS phantombuster_linkedin_inbox_scraper_agent_id TEXT;