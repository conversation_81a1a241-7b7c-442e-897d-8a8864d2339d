# --- !Ups

ALTER TABLE reply_sentiments_for_teams
  ADD COLUMN IF NOT EXISTS prospect_category_id BIGINT REFERENCES prospect_categories_custom(id) ON DELETE SET NULL;

CREATE INDEX IF NOT EXISTS sr_reply_sentiments_for_teams_prospect_category_id
ON reply_sentiments_for_teams(prospect_category_id);

CREATE INDEX IF NOT EXISTS sr_reply_sentiments_for_teams_team_id_prospect_category_id
ON reply_sentiments_for_teams(team_id,prospect_category_id);

# --- !Downs

DROP INDEX IF EXISTS sr_reply_sentiments_for_teams_prospect_category_id;

DROP INDEX IF EXISTS sr_reply_sentiments_for_teams_team_id_prospect_category_id ;

ALTER TABLE reply_sentiments_for_teams
DROP COLUMN IF EXISTS prospect_category_id;
