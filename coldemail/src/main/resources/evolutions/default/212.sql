# --- !Ups

CREATE TABLE IF NOT EXISTS tags
(
    id BIGSERIAL PRIMARY KEY,
    account_id BIGSERIAL REFERENCES accounts (id) ON DELETE SET NULL,
    team_id BIGSERIAL REFERENCES teams (id) ON DELETE CASCADE NOT NULL,
    tag TEXT NOT NULL,
    hidden BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now()

);
CREATE UNIQUE INDEX IF NOT EXISTS sr_tags_prospects_team_id_unique_tag ON tags (team_id, lower(tag));

CREATE TABLE IF NOT EXISTS tags_prospects
(
    tag_id BIGSERIAL REFERENCES tags (id) ON DELETE CASCADE NOT NULL,
    prospect_id BIGSERIAL REFERENCES prospects (id) ON DELETE CASCADE NOT NULL,
    account_id BIGSERIAL REFERENCES accounts (id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    PRIMARY KEY(prospect_id, tag_id)

);

CREATE INDEX IF NOT EXISTS sr_tags_prospects_tag_id_prospect_id ON tags_prospects (tag_id, prospect_id);
CREATE INDEX IF NOT EXISTS sr_tags_prospects_prospect_id_tag_id ON tags_prospects (prospect_id, tag_id);
# --- !Downs

DROP INDEX IF EXISTS sr_tags_prospects_team_id_unique_tag;
DROP INDEX IF EXISTS sr_tags_prospects_prospect_id_tag_id;
DROP INDEX IF EXISTS sr_tags_prospects_tag_id_prospect_id;

DROP TABLE IF EXISTS tags_prospects;

DROP TABLE IF EXISTS tags;