# --- !Ups

CREATE TABLE IF NOT EXISTS email_health_check_records (
	id bigserial PRIMARY KEY,

	team_id bigint REFERENCES teams (id) ON DELETE CASCADE NOT NULL,
	email_setting_id bigint REFERENCES email_settings (id) ON DELETE CASCADE NOT NULL,

	spf_auth_status text NOT NULL,
	dkim_auth_status text NOT NULL,
	dmarc_auth_status text NOT NULL,

	dkim_selector text,

	spf_record text,
	dkim_record text,
	dmarc_record text,

	updated_at timestamptz NOT NULL DEFAULT now(),
	created_at timestamptz NOT NULL DEFAULT now()
);

CREATE UNIQUE INDEX IF NOT EXISTS email_health_check_team_id_email_setting_id ON email_health_check_records (team_id, email_setting_id);

# --- !Downs

DROP INDEX IF EXISTS email_health_check_team_id_email_setting_id;

DROP TABLE IF EXISTS email_health_check_records;
