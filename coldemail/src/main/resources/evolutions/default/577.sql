# --- !Ups

ALTER TABLE campaigns
ADD COLUMN IF NOT EXISTS status_changed_by text,
ADD COLUMN IF NOT EXISTS last_checked_for_completed_campaign_cron_at timestamptz;

CREATE INDEX IF NOT EXISTS sr_campaigns_l_c_f_c_c_c_a ON campaigns (last_checked_for_completed_campaign_cron_at);


# --- !Downs

ALTER TABLE campaigns
DROP COLUMN IF EXISTS status_changed_by,
DROP COLUMN IF EXISTS last_checked_for_completed_campaign_cron_at ;


