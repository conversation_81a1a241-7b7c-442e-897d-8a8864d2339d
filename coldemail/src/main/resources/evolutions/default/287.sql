# --- !Ups

CREATE TABLE IF NOT EXISTS audit_event_logs
(
    id BIGSERIAL PRIMARY KEY,
    event_log_id TEXT NOT NULL UNIQUE,
    audit_request_log_id TEXT NOT NULL,
    event_type TEXT NOT NULL,
    event_object_type TEXT NOT NULL,
    event_data jsonb NOT NULL,
    team_id BIGINT NOT NULL,
    account_id BIGINT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);


# --- !Downs

DROP TABLE IF EXISTS audit_event_logs;
