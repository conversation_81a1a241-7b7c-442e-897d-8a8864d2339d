
# --- !Ups

create index if not exists
sr_emails_scheduled_tid_uuid on emails_scheduled (team_id, uuid);

create index if not exists
sr_emails_scheduled_tid_uuid_null on emails_scheduled (team_id, uuid) WHERE uuid IS NULL;

create index if not exists
sr_email_threads_tid_uuid on email_threads (team_id, uuid);


# --- !Downs

drop index if exists sr_email_threads_tid_uuid ;

drop index if exists sr_emails_scheduled_tid_uuid_null;

drop index if exists sr_emails_scheduled_tid_uuid;

