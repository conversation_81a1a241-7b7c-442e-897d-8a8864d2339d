# --- !Ups

alter table emails_scheduled
    add column if not exists inbox_email_setting_id BIGINT REFERENCES email_settings (id) ON DELETE SET NULL
;

create index if not exists sr_emails_scheduled_inbox_email_setting_id on emails_scheduled (inbox_email_setting_id);

CREATE UNIQUE INDEX IF NOT EXISTS sr_emails_scheduled_message_id_inbox_email_setting_id_main on emails_scheduled (message_id, inbox_email_setting_id) where inbox_email_setting_id is not null;


# --- !Downs

drop index if exists sr_emails_scheduled_message_id_inbox_email_setting_id_main;
drop index if exists sr_emails_scheduled_inbox_email_setting_id;

alter table emails_scheduled
    drop column if exists inbox_email_setting_id
;