# --- !Ups

CREATE TABLE IF NOT EXISTS prospects_metadata(
    prospect_id BIGINT REFERENCES prospects(id) ON DELETE CASCADE,
    team_id BIGINT REFERENCES teams(id) ON DELETE CASCADE,
    last_touched_at TIMESTAMPTZ,
    last_contacted_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now(),
    PRIMARY KEY (team_id, prospect_id)
);

CREATE UNIQUE INDEX IF NOT EXISTS sr_prospect_metadata_pid ON prospects_metadata(prospect_id);

# --- !Downs

DROP TABLE IF EXISTS prospects_metadata;