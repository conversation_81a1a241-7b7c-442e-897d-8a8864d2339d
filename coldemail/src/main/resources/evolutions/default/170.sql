# --- !Ups

CREATE TABLE IF NOT EXISTS rep_google_api_keys
(
    id SERIAL PRIMARY KEY,
    cl_id TEXT UNIQUE NOT NULL,
    cl_sec TEXT UNIQUE NOT NULL,
    gcp_admin_email TEXT NOT NULL,
    sr_key TEXT UNIQUE NOT NULL,
    gcp_project_name TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT now()
);


INSERT INTO rep_google_api_keys (
    id,
    cl_id,
    cl_sec,
    gcp_admin_email,
    sr_key
) VALUES

    (1, 'replace_this_id_in_db', 'replace_this_sec_in_db', 'replace_this_email_in_db', 'replace_this_key_in_db')

    ON CONFLICT DO NOTHING
;

ALTER TABLE organizations
    ADD COLUMN IF NOT EXISTS rep_google_api_key_id INT REFERENCES rep_google_api_keys (id) DEFAULT 1 NOT NULL
    ;

ALTER TABLE email_settings
    ADD COLUMN IF NOT EXISTS rep_google_api_key_id INT REFERENCES rep_google_api_keys (id) DEFAULT 1 NOT NULL,
    ADD COLUMN IF NOT EXISTS rep_google_api_key_id_if_reconnect INT REFERENCES rep_google_api_keys (id) ON DELETE SET NULL
    ;

ALTER TABLE emails_scheduled
    ADD COLUMN IF NOT EXISTS rep_google_api_key_id INT REFERENCES rep_google_api_keys (id) ON DELETE SET NULL
    ;

create index if not exists sr_emails_scheduled_outlook_msg_id on emails_scheduled (outlook_msg_id);
create index if not exists sr_prospect_events_created_at on prospect_events (created_at);
create index if not exists sr_emails_scheduled_replied_at on emails_scheduled (replied_at) where replied_at is not null;

# --- !Downs

drop index if exists sr_emails_scheduled_replied_at;
drop index if exists sr_prospect_events_created_at;
drop index if exists sr_emails_scheduled_outlook_msg_id;

ALTER TABLE emails_scheduled
    DROP COLUMN IF EXISTS rep_google_api_key_id
    ;

ALTER TABLE email_settings
    DROP COLUMN IF EXISTS rep_google_api_key_id,
    DROP COLUMN IF EXISTS rep_google_api_key_id_if_reconnect
    ;

ALTER TABLE organizations
    DROP COLUMN IF EXISTS rep_google_api_key_id
    ;

DROP TABLE IF EXISTS rep_google_api_keys
    ;
