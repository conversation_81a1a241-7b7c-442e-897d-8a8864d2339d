# --- !Ups

create unique index if not exists sr_prospects_emails_tm_pid_unique on prospects_emails(team_id, prospect_id, lower(email));

ALTER TABLE prospects_emails
DROP CONSTRAINT prospects_emails_prospect_id_fkey,
ADD CONSTRAINT prospects_emails_prospect_id_fkey
   FOREIGN KEY (prospect_id)
   REFERENCES prospects(id)
   ON DELETE CASCADE;

# --- !Downs

drop index if exists sr_prospects_emails_tm_pid_unique ;