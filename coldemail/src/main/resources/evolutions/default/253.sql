# --- !Ups

-- old missed migrations ---

create index if not exists sr_prospects_email_sent_for_validation_at on prospects (email_sent_for_validation_at);

CREATE TABLE IF NOT EXISTS email_validation_batch_request
(
 id BIGSERIAL PRIMARY KEY,
 service_request_id TEXT NOT NULL,
 checked_via_tool TEXT NOT NULL,
 status_updated_at TIMESTAMPTZ DEFAULT now(),
 status TEXT NOT NULL,
 created_at TIMESTAMPTZ DEFAULT now()
);

alter table email_validations
add column if not exists process_status text,
add column if not exists email_validation_batch_request_id BIGINT REFERENCES email_validation_batch_request (id) ON DELETE SET NULL,
add column if not exists created_at TIMESTAMPTZ DEFAULT now()
;


create index if not exists sr_email_validations_process_status on email_validations (process_status);

create index if not exists sr_email_validations_email_validation_batch_request_id on email_validations (email_validation_batch_request_id);

create index if not exists sr_email_validations_created_at on email_validations (created_at);


alter table accounts add column if not exists weekly_report_emails TEXT;

alter table user_roles alter column team_id drop not null;

alter table campaign_steps_variants add column if not exists active BOOLEAN DEFAULT TRUE;

alter table emails_scheduled add column if not exists is_opening_step boolean;


# --- !Downs



ALTER TABLE emails_scheduled
DROP COLUMN IF EXISTS is_opening_step;

ALTER TABLE campaign_steps_variants
DROP COLUMN IF EXISTS active;

alter table user_roles alter column team_id set not null;

alter table accounts drop column if exists weekly_report_emails;

drop index if exists sr_email_validations_created_at;
drop index if exists sr_email_validations_email_validation_batch_request_id;
drop index if exists sr_email_validations_process_status;


ALTER TABLE email_validations
DROP COLUMN IF EXISTS process_status,
DROP COLUMN IF EXISTS email_validation_batch_request_id,
DROP COLUMN IF EXISTS created_at
;

drop table if exists email_validation_batch_request;

drop index if exists sr_prospects_email_sent_for_validation_at;