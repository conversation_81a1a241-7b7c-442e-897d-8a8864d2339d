# --- !Ups

ALTER TABLE prospects
ADD COLUMN IF NOT EXISTS last_emailed_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS last_called_at TIMESTAMPTZ;


CREATE INDEX IF NOT EXISTS sr_prospects_team_id_last_emailed_at_is_not_null
ON prospects(team_id, last_emailed_at ) WHERE last_emailed_at IS NOT NULL;

CREATE INDEX IF NOT EXISTS sr_prospects_team_id_last_called_at_is_not_null
ON prospects (team_id, last_called_at) WHERE last_called_at IS NOT NULL;

# --- !Downs

DROP INDEX IF EXISTS sr_prospects_team_id_last_called_at_is_not_null;
DROP INDEX IF EXISTS sr_prospects_team_id_last_emailed_at_is_not_null;

ALTER TABLE prospects
DROP COLUMN IF EXISTS last_emailed_at,
DROP COLUMN IF EXISTS last_called_at;