# --- !Ups

CREATE TABLE IF NOT EXISTS template_categories
(
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS templates_public
(
    id BIGSERIAL PRIMARY KEY,
    label TEXT NOT NULL,
    subject TEXT,
    body TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    category_id BIGINT REFERENCES template_categories (id) ON DELETE SET NULL
);

# --- !Downs

DROP TABLE IF EXISTS templates_public;

DROP TABLE IF EXISTS template_categories;
