# --- !Ups

CREATE UNIQUE INDEX IF NOT EXISTS sr_email_settings_tid_id_uniq ON email_settings (team_id, id);

ALTER TABLE email_message_data
    ADD CONSTRAINT email_message_data_tid_ies_id_fkey FOREIGN KEY (team_id, inbox_email_setting_id) REFERENCES email_settings (team_id, id) ON DELETE CASCADE;
# --- !Downs

DROP INDEX IF EXISTS sr_email_settings_tid_id_uniq;

ALTER TABLE email_message_data
DROP CONSTRAINT  email_message_data_tid_ies_id_fkey;
