# --- !Ups

ALTER TABLE organizations
    DROP COLUMN IF EXISTS last_payment_on,
    DROP COLUMN IF EXISTS stripe_plan_name,
    DROP COLUMN IF EXISTS subscription_order_obj,
    DROP COLUMN IF EXISTS last_payment_obj,
    DROP COLUMN IF EXISTS prospect_limit_per_mailbox,
    DROP COLUMN IF EXISTS max_prospects_per_month
    ;

ALTER TABLE campaigns_prospects
    DROP COLUMN IF EXISTS status,
    DROP COLUMN IF EXISTS prospect_category_id
    ;

ALTER TABLE prospects
    DROP COLUMN IF EXISTS in_blacklist,
    DROP COLUMN IF EXISTS prospect_category_id,
    DROP COLUMN IF EXISTS prospect_category
    ;


ALTER TABLE emails_scheduled
    DROP COLUMN IF EXISTS prospect_category_id,
    DROP COLUMN IF EXISTS prospect_category_id_custom
    ;

DROP INDEX IF EXISTS sr_prospects_list_null_btree;
DROP INDEX IF EXISTS sr_prospects_accountid_teamid_list_null;
DROP INDEX IF EXISTS sr_prospects_list;
DROP INDEX IF EXISTS sr_prospects_list_trgm;

# --- !Downs
