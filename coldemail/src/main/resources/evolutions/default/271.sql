# --- !Ups

ALTER TABLE workflow_crm_module_settings
  ADD COLUMN IF NOT EXISTS add_to_do_not_contact_in_sr BOOLEAN DEFAULT FALSE NOT NULL,
  ADD COLUMN IF NOT EXISTS crm_filters_for_add_to_do_not_contact_in_sr jsonb
;

ALTER TABLE workflow_crm_settings
 ADD COLUMN IF NOT EXISTS owner_id BIGINT REFERENCES accounts(id)
;

CREATE INDEX IF NOT EXISTS
 sr_workflow_crm_module_settings_team_id_mkeyid ON
 workflow_crm_module_settings (team_id, id)
;

CREATE INDEX IF NOT EXISTS
  sr_workflow_crm_settings_team_id_active ON
  workflow_crm_settings (team_id, active)
;

CREATE INDEX IF NOT EXISTS
  sr_workflow_crm_module_settings_add_to_do_not_contact_in_sr ON
  workflow_crm_module_settings (add_to_do_not_contact_in_sr)
;



# --- !Downs

DROP INDEX IF EXISTS sr_workflow_crm_module_settings_add_to_do_not_contact_in_sr;

DROP INDEX IF EXISTS sr_workflow_crm_module_settings_team_id_active;

DROP INDEX IF EXISTS sr_workflow_crm_module_settings_team_id_mkeyid;


ALTER TABLE workflow_crm_settings
  DROP COLUMN IF EXISTS owner_id
;

ALTER TABLE workflow_crm_module_settings
  DROP COLUMN IF EXISTS add_to_do_not_contact_in_sr,
  DROP COLUMN IF EXISTS crm_filters_for_add_to_do_not_contact_in_sr
;
