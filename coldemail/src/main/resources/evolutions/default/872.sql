# --- !Ups

ALTER TABLE call_participants_logs
DROP CONSTRAINT call_participants_logs_reply_sentiment_uuid_fkey,
ADD CONSTRAINT call_participants_logs_reply_sentiment_uuid_fkey
    FOREIGN KEY (reply_sentiment_uuid) REFERENCES reply_sentiments_for_teams(uuid) ON DELETE SET NULL;

# --- !Downs

ALTER TABLE call_participants_logs
DROP CONSTRAINT call_participants_logs_reply_sentiment_uuid_fkey,
ADD CONSTRAINT call_participants_logs_reply_sentiment_uuid_fkey
    FOREIGN KEY (reply_sentiment_uuid) REFERENCES reply_sentiments_for_teams(uuid);
