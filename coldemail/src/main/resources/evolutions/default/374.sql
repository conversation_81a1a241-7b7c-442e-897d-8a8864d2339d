# --- !Ups

CREATE TABLE IF NOT EXISTS linkedin_settings(
    id SERIAL PRIMARY KEY NOT NULL,
    uuid TEXT NOT NULL,
    owner_account_id BIGINT REFERENCES accounts (id) ON DELETE CASCADE NOT NULL,
    team_id BIGINT REFERENCES teams (id) ON DELETE CASCADE NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    email TEXT NOT NULL,
    view_profile_limit_per_day INT NOT NULL,
    inmail_limit_per_day INT NOT NULL,
    message_limit_per_day INT NOT NULL,
    connection_request_limit_per_day INT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

CREATE UNIQUE INDEX IF NOT EXISTS sr_linkedin_settings_uuid ON linkedin_settings(uuid);

CREATE UNIQUE INDEX IF NOT EXISTS sr_linkedin_settings_team_id_email ON linkedin_settings(team_id, lower(email));

CREATE INDEX IF NOT EXISTS sr_linkedin_settings_account_id ON linkedin_settings(owner_account_id);

# --- !Downs

DROP INDEX IF EXISTS sr_linkedin_settings_account_id;

DROP INDEX IF EXISTS sr_linkedin_settings_team_id_email;

DROP INDEX IF EXISTS sr_linkedin_settings_uuid;

DROP TABLE IF EXISTS linkedin_settings;
