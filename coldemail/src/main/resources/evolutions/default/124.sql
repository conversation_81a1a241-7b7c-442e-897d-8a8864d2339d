# --- !Ups

ALTER TABLE email_settings
    ADD COLUMN IF NOT EXISTS last_sent_id BIGINT REFERENCES emails_scheduled (id),
    ADD COLUMN IF NOT EXISTS last_sent_scheduled_at TIMESTAMPTZ,
    ADD COLUMN IF NOT EXISTS last_sent_sent_at TIMESTAMPTZ;


create index if not exists sr_campaigns_sender_email_settings_id on campaigns (sender_email_settings_id);

CREATE INDEX IF NOT EXISTS sr_emails_scheduled_sent_at ON emails_scheduled (sent_at);

CREATE INDEX IF NOT EXISTS sr_emails_scheduled_pushed_to_rabbitmq ON emails_scheduled (pushed_to_rabbitmq);

CREATE INDEX IF NOT EXISTS sr_campaigns_status ON campaigns (status);

CREATE INDEX IF NOT EXISTS sr_email_settings_paused_till ON email_settings (paused_till);


# --- !Downs

drop index if exists sr_campaigns_sender_email_settings_id;

drop INDEX if exists sr_emails_scheduled_sent_at;

drop INDEX if exists sr_emails_scheduled_pushed_to_rabbitmq;

drop INDEX if exists sr_campaigns_status;

drop INDEX if exists sr_email_settings_paused_till;




ALTER TABLE email_settings
DROP COLUMN IF EXISTS last_sent_id,
DROP COLUMN IF EXISTS last_sent_scheduled_at,
DROP COLUMN IF EXISTS last_sent_sent_at;
