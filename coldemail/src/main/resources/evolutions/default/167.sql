# --- !Ups

ALTER TABLE prospect_events
  ADD COLUMN IF NOT EXISTS account_id BIGINT REFERENCES accounts (id) ON DELETE SET NULL,
  ADD COLUMN IF NOT EXISTS team_id BIGINT REFERENCES teams (id) ON DELETE CASCADE,
  ADD COLUMN IF NOT EXISTS email_scheduled_id BIGINT REFERENCES emails_scheduled (id) ON DELETE SET NULL
;


CREATE INDEX IF NOT EXISTS sr_prospect_events_account_id ON prospect_events (account_id);

CREATE INDEX IF NOT EXISTS sr_prospect_events_team_id ON prospect_events (team_id);

CREATE INDEX IF NOT EXISTS sr_prospect_events_email_scheduled_id ON prospect_events (email_scheduled_id);

# --- !Downs

DROP INDEX IF EXISTS sr_prospect_events_account_id;

DROP INDEX IF EXISTS sr_prospect_events_team_id;

DROP INDEX IF EXISTS sr_prospect_events_email_scheduled_id;

ALTER TABLE prospect_events
  DROP COLUMN IF EXISTS account_id,
  DROP COLUMN IF EXISTS team_id,
  DROP COLUMN IF EXISTS email_scheduled_id
;
