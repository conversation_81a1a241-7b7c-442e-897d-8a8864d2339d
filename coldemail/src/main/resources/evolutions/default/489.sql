# --- !Ups

CREATE TABLE IF NOT EXISTS call_settings(
    uuid TEXT PRIMARY KEY NOT NULL,
    phone_number TEXT NOT NULL,
    owner_account_id BIGINT REFERENCES accounts (id) ON DELETE CASCADE NOT NULL,
    team_id BIGINT REFERENCES teams (id) ON DELETE CASCADE NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    call_limit_per_day INT NOT NULL,
    phone_sid TEXT NOT NULL,
    is_verified BOOLEAN DEFAULT false,
    verified_at TIMESTAMPTZ,
    phone_type TEXT,
    country TEXT,
    enable_forwarding BOOLEAN DEFAULT false,
    forward_to TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

CREATE UNIQUE INDEX IF NOT EXISTS sr_call_settings_team_id_account_id_uuid_phone_number ON call_settings(team_id,owner_account_id, uuid, phone_number);

CREATE UNIQUE INDEX IF NOT EXISTS sr_call_settings_phone_number ON call_settings(phone_number);

CREATE INDEX IF NOT EXISTS sr_call_settings_account_id ON call_settings(owner_account_id);

# --- !Downs

DROP INDEX IF EXISTS sr_call_settings_team_id_account_id_uuid_phone_number;

DROP INDEX IF EXISTS sr_call_settings_phone_number;

DROP INDEX IF EXISTS sr_call_settings_account_id;

DROP TABLE IF EXISTS call_settings;
