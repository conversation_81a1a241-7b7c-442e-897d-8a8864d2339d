# --- !Ups

CREATE TABLE IF NOT EXISTS voicemails(
    id SERIAL PRIMARY KEY NOT NULL,
    uuid TEXT NOT NULL,
    name TEXT NOT NULL,
    url TEXT NOT NULL,
    type TEXT NOT NULL,
    account_id BIGINT REFERENCES accounts (id) ON DELETE CASCADE NOT NULL,
    team_id BIGINT REFERENCES teams (id) ON DELETE CASCADE NOT NULL,
    campaign_id BIGINT REFERENCES campaigns(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);


# --- !Downs

DROP TABLE IF EXISTS voicemails;
