# --- !Ups


CREATE INDEX IF NOT EXISTS call_conf_name_partial_not_null_column
ON call_conference_logs (conference_sp_name)
WHERE call_type = 'twl_conf' AND is_incoming = false;

ALTER TABLE call_conference_logs
ADD CONSTRAINT call_conf_name_partial_not_null_column
CHECK (conference_sp_name IS NOT NULL OR NOT (call_type = 'twl_conf' AND is_incoming = false));

# --- !Downs

ALTER TABLE call_conference_logs DROP CONSTRAINT call_conf_name_partial_not_null_column;

DROP INDEX IF EXISTS call_conf_name_partial_not_null_column;