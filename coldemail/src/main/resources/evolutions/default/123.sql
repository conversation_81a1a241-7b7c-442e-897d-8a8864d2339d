# --- !Ups

CREATE TABLE IF NOT EXISTS email_threads
(
    id BIGSERIAL PRIMARY KEY,
    email_settings_id BIGINT REFERENCES email_settings(id) ON DELETE SET NULL,
    campaign_id BIGINT REFERENCES campaigns(id) ON DELETE SET NULL,
    campaign_name TEXT,
    prospect_id BIGINT REFERENCES prospects(id) ON DELETE CASCADE,
    subject TEXT NOT NULL,
    latest_email_received_id BIGINT REFERENCES emails_received (id),
    created_at TIMESTAMPTZ DEFAULT now()
);

ALTER TABLE emails_received
    ADD COLUMN IF NOT EXISTS email_thread_id BIGINT REFERENCES email_threads (id);


# --- !Downs

ALTER TABLE emails_received DROP COLUMN IF EXISTS email_thread_id;
DROP TABLE IF EXISTS email_threads;
