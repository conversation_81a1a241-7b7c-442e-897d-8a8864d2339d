# --- !Ups

ALTER TABLE audit_workflow_attempt_logs
ADD COLUMN IF NOT EXISTS  last_attempted_at TIMESTAMPTZ DEFAULT now(),
ADD COLUMN IF NOT EXISTS  last_attempt_tries_log_id text references audit_workflow_attempt_tries_logs(attempt_tries_log_id) on delete set null;

UPDATE audit_workflow_attempt_logs
SET last_attempted_at = created_at
WHERE last_attempted_at is NULL;

 ALTER TABLE audit_workflow_attempt_logs ALTER COLUMN last_attempted_at SET NOT NULL;

CREATE INDEX IF NOT EXISTS sr_a_w_a_logs_status_retry_count_last_attempt_at ON audit_workflow_attempt_logs(attempt_status, attempt_retry_count, last_attempted_at);

CREATE INDEX IF NOT EXISTS sr_a_w_a_logs_last_attempt_tries_log_id ON audit_workflow_attempt_logs(last_attempt_tries_log_id);


# --- !Downs

DROP INDEX IF EXISTS sr_a_w_a_logs_status_retry_count_last_attempt_at;

DROP INDEX IF EXISTS sr_a_w_a_logs_last_attempt_tries_log_id;

ALTER TABLE audit_workflow_attempt_logs
DROP COLUMN IF EXISTS last_attempted_at,
DROP COLUMN IF EXISTS last_attempt_tries_log_id;
