# --- !Ups

CREATE TABLE IF NOT EXISTS column_defs_prospects (
	id bigserial PRIMARY KEY,

	team_id bigint REFERENCES teams (id) ON DELETE CASCADE NOT NULL,
	account_id bigint REFERENCES accounts (id) ON DELETE SET NULL,

	column_id bigint REFERENCES column_defs (id) ON DELETE CASCADE NOT NULL,
	prospect_id bigint REFERENCES prospects (id) ON DELETE CASCADE NOT NULL,

	status text NOT NULL,

	created_at timestamptz NOT NULL DEFAULT now(),
	updated_at timestamptz NOT NULL DEFAULT now()
);

CREATE UNIQUE INDEX IF NOT EXISTS column_defs_prospects_column_id_prospect_id ON column_defs_prospects (column_id, prospect_id);

CREATE INDEX IF NOT EXISTS column_defs_prospects_team_id ON column_defs_prospects (team_id);

CREATE INDEX IF NOT EXISTS column_defs_prospects_account_id ON column_defs_prospects (account_id);

CREATE INDEX IF NOT EXISTS column_defs_prospects_prospect_id ON column_defs_prospects (prospect_id);


# --- !Downs

DROP INDEX IF EXISTS column_defs_prospects_prospect_id;

DROP INDEX IF EXISTS column_defs_prospects_account_id;

DROP INDEX IF EXISTS column_defs_prospects_team_id;

DROP INDEX IF EXISTS column_defs_prospects_column_id_prospect_id;

DROP TABLE IF EXISTS column_defs_prospects;
