# --- !Ups

ALTER TABLE lead_validation_batch_request
	ADD COLUMN IF NOT EXISTS total_frozen_plan_credits BIGINT,
	ADD COLUMN IF NOT EXISTS total_reverted_plan_credits BIGINT;

UPDATE
	lead_validation_batch_request
SET
	total_frozen_plan_credits = 0
WHERE
	total_frozen_plan_credits IS NULL;

UPDATE
	lead_validation_batch_request
SET
	total_reverted_plan_credits = 0
WHERE
	total_reverted_plan_credits IS NULL;

ALTER TABLE lead_validation_batch_request
	ALTER COLUMN total_frozen_plan_credits SET NOT NULL;

ALTER TABLE lead_validation_batch_request
	ALTER COLUMN total_reverted_plan_credits SET NOT NULL;


# --- !Downs

ALTER TABLE lead_validation_batch_request
	DROP COLUMN IF EXISTS total_frozen_plan_credits,
	DROP COLUMN IF EXISTS total_reverted_plan_credits;
