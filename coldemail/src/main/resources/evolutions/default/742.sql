# --- !Ups

ALTER TABLE lead_finder_billing_logs
	ADD COLUMN IF NOT EXISTS lead_validation_batch_request_id BIGINT REFERENCES lead_validation_batch_request (id) ON DELETE SET NULL,
	ADD COLUMN IF NOT EXISTS email_sent_for_validation BOOLEAN NOT NULL DEFAULT FALSE,
	ADD COLUMN IF NOT EXISTS email_sent_for_validation_at TIMESTAMPTZ,
	ADD COLUMN IF NOT EXISTS is_valid BOOLEAN NOT NULL DEFAULT FALSE,
	ADD COLUMN IF NOT EXISTS checked_at TIMESTAMPTZ;

# --- !Downs

ALTER TABLE lead_finder_billing_logs
	DROP COLUMN IF EXISTS lead_validation_batch_request_id,
	DROP COLUMN IF EXISTS email_sent_for_validation,
	DROP COLUMN IF EXISTS email_sent_for_validation_at,
	DROP COLUMN IF EXISTS is_valid,
	DROP COLUMN IF EXISTS checked_at;
