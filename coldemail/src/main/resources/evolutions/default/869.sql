# --- !Ups

ALTER TABLE purchased_domains
    ADD COLUMN IF NOT EXISTS workspace_type text;

UPDATE purchased_domains
SET workspace_type = CASE
    WHEN platform_type = 'maildoso' THEN 'SMTP'
    WHEN platform_type = 'zapmail' THEN 'GOOGLE'
END
WHERE workspace_type IS NULL
  AND platform_type IN ('maildoso', 'zapmail');

# --- !Downs

ALTER TABLE purchased_domains
    DROP COLUMN IF EXISTS workspace_type;
