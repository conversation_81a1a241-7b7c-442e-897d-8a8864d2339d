# --- !Ups

ALTER TABLE user_roles
ADD COLUMN IF NOT EXISTS manage_billing_v2 TEXT,
ADD COLUMN IF NOT EXISTS view_user_management_v2 TEXT,
ADD COLUMN IF NOT EXISTS edit_user_management_v2 TEXT,
ADD COLUMN IF NOT EXISTS view_team_config_v2 TEXT,
ADD COLUMN IF NOT EXISTS edit_team_config_v2 TEXT,
ADD COLUMN IF NOT EXISTS view_user_settings_v2 TEXT,
ADD COLUMN IF NOT EXISTS edit_user_settings_v2 TEXT,
ADD COLUMN IF NOT EXISTS view_prospects_v2 TEXT,
ADD COLUMN IF NOT EXISTS edit_prospects_v2 TEXT,
ADD COLUMN IF NOT EXISTS delete_prospects_v2 TEXT,
ADD COLUMN IF NOT EXISTS view_campaigns_v2 TEXT,
ADD COLUMN IF NOT EXISTS edit_campaigns_v2 TEXT,
ADD COLUMN IF NOT EXISTS delete_campaigns_v2 TEXT,
ADD COLUMN IF NOT EXISTS change_campaign_status_v2 TEXT,
ADD COLUMN IF NOT EXISTS view_reports_v2 TEXT,
ADD COLUMN IF NOT EXISTS edit_reports_v2 TEXT,
ADD COLUMN IF NOT EXISTS download_reports_v2 TEXT,
ADD COLUMN IF NOT EXISTS view_inbox_v2 TEXT,
ADD COLUMN IF NOT EXISTS edit_inbox_v2 TEXT,
ADD COLUMN IF NOT EXISTS send_manual_email_v2 TEXT,
ADD COLUMN IF NOT EXISTS view_templates_v2 TEXT,
ADD COLUMN IF NOT EXISTS edit_templates_v2 TEXT,
ADD COLUMN IF NOT EXISTS delete_templates_v2 TEXT,
ADD COLUMN IF NOT EXISTS view_blacklist_v2 TEXT,
ADD COLUMN IF NOT EXISTS edit_blacklist_v2 TEXT,
ADD COLUMN IF NOT EXISTS view_workflows_v2 TEXT,
ADD COLUMN IF NOT EXISTS edit_workflows_v2 TEXT,
ADD COLUMN IF NOT EXISTS view_webhooks_v2 TEXT,
ADD COLUMN IF NOT EXISTS edit_webhooks_v2 TEXT,
ADD COLUMN IF NOT EXISTS view_prospect_accounts_v2 TEXT,
ADD COLUMN IF NOT EXISTS edit_prospect_accounts_v2 TEXT,
ADD COLUMN IF NOT EXISTS view_email_accounts_v2 TEXT,
ADD COLUMN IF NOT EXISTS edit_email_accounts_v2 TEXT,
ADD COLUMN IF NOT EXISTS delete_email_accounts_v2 TEXT;

UPDATE user_roles
SET manage_billing_v2 = 'no_access' WHERE manage_billing = FALSE AND manage_billing_v2 IS NULL;

UPDATE user_roles
SET manage_billing_v2 = 'all' WHERE manage_billing = TRUE AND manage_billing_v2 IS NULL;

UPDATE user_roles
SET view_user_management_v2 = 'no_access' WHERE view_user_management = FALSE AND view_user_management_v2 IS NULL;

UPDATE user_roles
SET view_user_management_v2 = 'all' WHERE view_user_management = TRUE AND view_user_management_v2 IS NULL;

UPDATE user_roles
SET edit_user_management_v2 = 'no_access' WHERE edit_user_management = FALSE AND edit_user_management_v2 IS NULL;

UPDATE user_roles
SET edit_user_management_v2 = 'all' WHERE edit_user_management = TRUE AND edit_user_management_v2 IS NULL;

UPDATE user_roles
SET view_team_config_v2 = 'no_access' WHERE view_team_config = FALSE AND view_team_config_v2 IS NULL;

UPDATE user_roles
SET view_team_config_v2 = 'all' WHERE view_team_config = TRUE AND view_team_config_v2 IS NULL;

UPDATE user_roles
SET edit_team_config_v2 = 'no_access' WHERE edit_team_config = FALSE AND edit_team_config_v2 IS NULL;

UPDATE user_roles
SET edit_team_config_v2 = 'all' WHERE edit_team_config = TRUE AND edit_team_config_v2 IS NULL;

UPDATE user_roles
SET view_user_settings_v2 = 'no_access' WHERE view_user_settings = FALSE AND view_user_settings_v2 IS NULL;

UPDATE user_roles
SET view_user_settings_v2 = view_user_settings_ownership WHERE view_user_settings = TRUE AND view_user_settings_v2 IS NULL;

UPDATE user_roles
SET edit_user_settings_v2 = 'no_access' WHERE edit_user_settings = FALSE AND edit_user_settings_v2 IS NULL;

UPDATE user_roles
SET edit_user_settings_v2 = edit_user_settings_ownership WHERE edit_user_settings = TRUE AND edit_user_settings_v2 IS NULL;

UPDATE user_roles
SET view_prospects_v2 = 'no_access' WHERE view_prospects = FALSE AND view_prospects_v2 IS NULL;

UPDATE user_roles
SET view_prospects_v2 = view_prospects_ownership WHERE view_prospects = TRUE AND view_prospects_v2 IS NULL;

UPDATE user_roles
SET edit_prospects_v2 = 'no_access' WHERE edit_prospects = FALSE AND edit_prospects_v2 IS NULL;

UPDATE user_roles
SET edit_prospects_v2 = edit_prospects_ownership WHERE edit_prospects = TRUE AND edit_prospects_v2 IS NULL;

UPDATE user_roles
SET delete_prospects_v2 = 'no_access' WHERE delete_prospects = FALSE AND delete_prospects_v2 IS NULL;

UPDATE user_roles
SET delete_prospects_v2 = delete_prospects_ownership WHERE delete_prospects = TRUE AND delete_prospects_v2 IS NULL;

UPDATE user_roles
SET view_campaigns_v2 = 'no_access' WHERE view_campaigns = FALSE AND view_campaigns_v2 IS NULL;

UPDATE user_roles
SET view_campaigns_v2 = view_campaigns_ownership WHERE view_campaigns = TRUE AND view_campaigns_v2 IS NULL;

UPDATE user_roles
SET edit_campaigns_v2 = 'no_access' WHERE edit_campaigns = FALSE AND edit_campaigns_v2 IS NULL;

UPDATE user_roles
SET edit_campaigns_v2 = edit_campaigns_ownership WHERE edit_campaigns = TRUE AND edit_campaigns_v2 IS NULL;

UPDATE user_roles
SET delete_campaigns_v2 = 'no_access' WHERE delete_campaigns = FALSE AND delete_campaigns_v2 IS NULL;

UPDATE user_roles
SET delete_campaigns_v2 = delete_campaigns_ownership WHERE delete_campaigns = TRUE AND delete_campaigns_v2 IS NULL;

UPDATE user_roles
SET change_campaign_status_v2 = 'no_access' WHERE change_campaign_status = FALSE AND change_campaign_status_v2 IS NULL;

UPDATE user_roles
SET change_campaign_status_v2 = change_campaign_status_ownership WHERE change_campaign_status = TRUE AND change_campaign_status_v2 IS NULL;

UPDATE user_roles
SET view_reports_v2 = 'no_access' WHERE view_reports = FALSE AND view_reports_v2 IS NULL;

UPDATE user_roles
SET view_reports_v2 = view_reports_ownership WHERE view_reports = TRUE AND view_reports_v2 IS NULL;

UPDATE user_roles
SET edit_reports_v2 = 'no_access' WHERE edit_reports = FALSE AND edit_reports_v2 IS NULL;

UPDATE user_roles
SET edit_reports_v2 = edit_reports_ownership WHERE edit_reports = TRUE AND edit_reports_v2 IS NULL;

UPDATE user_roles
SET view_blacklist_v2 = 'no_access' WHERE view_blacklist = FALSE AND view_blacklist_v2 IS NULL;

UPDATE user_roles
SET view_blacklist_v2 = view_blacklist_ownership WHERE view_blacklist = TRUE AND view_blacklist_v2 IS NULL;

UPDATE user_roles
SET download_reports_v2 = 'no_access' WHERE download_reports = FALSE AND download_reports_v2 IS NULL;

UPDATE user_roles
SET download_reports_v2 = download_reports_ownership WHERE download_reports = TRUE AND download_reports_v2 IS NULL;

UPDATE user_roles
SET view_inbox_v2 = 'no_access' WHERE view_inbox = FALSE AND view_inbox_v2 IS NULL;

UPDATE user_roles
SET view_inbox_v2 = view_inbox_ownership WHERE view_inbox = TRUE AND view_inbox_v2 IS NULL;

UPDATE user_roles
SET edit_inbox_v2 = 'no_access' WHERE edit_inbox = FALSE AND edit_inbox_v2 IS NULL;

UPDATE user_roles
SET edit_inbox_v2 = edit_inbox_ownership WHERE edit_inbox = TRUE AND edit_inbox_v2 IS NULL;

UPDATE user_roles
SET send_manual_email_v2 = 'no_access' WHERE send_manual_email = FALSE AND send_manual_email_v2 IS NULL;

UPDATE user_roles
SET send_manual_email_v2 = send_manual_email_ownership WHERE send_manual_email = TRUE AND send_manual_email_v2 IS NULL;

UPDATE user_roles
SET view_templates_v2 = 'no_access' WHERE view_templates = FALSE AND view_templates_v2 IS NULL;

UPDATE user_roles
SET view_templates_v2 = view_templates_ownership WHERE view_templates = TRUE AND view_templates_v2 IS NULL;

UPDATE user_roles
SET edit_templates_v2 = 'no_access' WHERE edit_templates = FALSE AND edit_templates_v2 IS NULL;

UPDATE user_roles
SET edit_templates_v2 = edit_templates_ownership WHERE edit_templates = TRUE AND edit_templates_v2 IS NULL;

UPDATE user_roles
SET delete_templates_v2 = 'no_access' WHERE delete_templates = FALSE AND delete_templates_v2 IS NULL;

UPDATE user_roles
SET delete_templates_v2 = delete_templates_ownership WHERE delete_templates = TRUE AND delete_templates_v2 IS NULL;

UPDATE user_roles
SET edit_blacklist_v2 = 'no_access' WHERE edit_blacklist = FALSE AND edit_blacklist_v2 IS NULL;

UPDATE user_roles
SET edit_blacklist_v2 = edit_blacklist_ownership WHERE edit_blacklist = TRUE AND edit_blacklist_v2 IS NULL;

UPDATE user_roles
SET view_workflows_v2 = 'no_access' WHERE view_workflows = FALSE AND view_workflows_v2 IS NULL;

UPDATE user_roles
SET view_workflows_v2 = view_workflows_ownership WHERE view_workflows = TRUE AND view_workflows_v2 IS NULL;

UPDATE user_roles
SET edit_workflows_v2 = 'no_access' WHERE edit_workflows = FALSE AND edit_workflows_v2 IS NULL;

UPDATE user_roles
SET edit_workflows_v2 = edit_workflows_ownership WHERE edit_workflows = TRUE AND edit_workflows_v2 IS NULL;

UPDATE user_roles
SET view_webhooks_v2 = 'no_access' WHERE view_webhooks = FALSE AND view_webhooks_v2 IS NULL;

UPDATE user_roles
SET view_webhooks_v2 = 'all' WHERE view_webhooks = TRUE AND view_webhooks_v2 IS NULL;

UPDATE user_roles
SET edit_webhooks_v2 = 'no_access' WHERE edit_webhooks = FALSE AND edit_webhooks_v2 IS NULL;

UPDATE user_roles
SET edit_webhooks_v2 = 'all' WHERE edit_webhooks = TRUE AND edit_webhooks_v2 IS NULL;

UPDATE user_roles
SET view_prospect_accounts_v2 = 'no_access' WHERE view_prospect_accounts = FALSE AND view_prospect_accounts_v2 IS NULL;

UPDATE user_roles
SET view_prospect_accounts_v2 = view_prospect_accounts_ownership WHERE view_prospect_accounts = TRUE AND view_prospect_accounts_v2 IS NULL;

UPDATE user_roles
SET edit_prospect_accounts_v2 = 'no_access' WHERE edit_prospect_accounts = FALSE AND edit_prospect_accounts_v2 IS NULL;

UPDATE user_roles
SET edit_prospect_accounts_v2 = edit_prospect_accounts_ownership WHERE edit_prospect_accounts = TRUE AND edit_prospect_accounts_v2 IS NULL;

UPDATE user_roles
SET view_email_accounts_v2 = 'no_access' WHERE view_email_accounts = FALSE AND view_email_accounts_v2 IS NULL;

UPDATE user_roles
SET view_email_accounts_v2 = view_email_accounts_ownership WHERE view_email_accounts = TRUE AND view_email_accounts_v2 IS NULL;

UPDATE user_roles
SET edit_email_accounts_v2 = 'no_access' WHERE edit_email_accounts = FALSE AND edit_email_accounts_v2 IS NULL;

UPDATE user_roles
SET edit_email_accounts_v2 = edit_email_accounts_ownership WHERE edit_email_accounts = TRUE AND edit_email_accounts_v2 IS NULL;

UPDATE user_roles
SET delete_email_accounts_v2 = 'no_access' WHERE delete_email_accounts = FALSE AND delete_email_accounts_v2 IS NULL;

UPDATE user_roles
SET delete_email_accounts_v2 = delete_email_accounts_ownership WHERE delete_email_accounts = TRUE AND delete_email_accounts_v2 IS NULL;

# --- !Downs

ALTER TABLE user_roles
DROP COLUMN IF EXISTS manage_billing_v2,
DROP COLUMN IF EXISTS view_user_management_v2,
DROP COLUMN IF EXISTS edit_user_management_v2,
DROP COLUMN IF EXISTS view_team_config_v2,
DROP COLUMN IF EXISTS edit_team_config_v2,
DROP COLUMN IF EXISTS view_user_settings_v2,
DROP COLUMN IF EXISTS edit_user_settings_v2,
DROP COLUMN IF EXISTS view_prospects_v2,
DROP COLUMN IF EXISTS edit_prospects_v2,
DROP COLUMN IF EXISTS delete_prospects_v2,
DROP COLUMN IF EXISTS view_campaigns_v2,
DROP COLUMN IF EXISTS edit_campaigns_v2,
DROP COLUMN IF EXISTS delete_campaigns_v2,
DROP COLUMN IF EXISTS change_campaign_status_v2,
DROP COLUMN IF EXISTS view_reports_v2,
DROP COLUMN IF EXISTS edit_reports_v2,
DROP COLUMN IF EXISTS download_reports_v2,
DROP COLUMN IF EXISTS view_inbox_v2,
DROP COLUMN IF EXISTS edit_inbox_v2,
DROP COLUMN IF EXISTS send_manual_email_v2,
DROP COLUMN IF EXISTS view_templates_v2,
DROP COLUMN IF EXISTS edit_templates_v2,
DROP COLUMN IF EXISTS delete_templates_v2,
DROP COLUMN IF EXISTS view_blacklist_v2,
DROP COLUMN IF EXISTS edit_blacklist_v2,
DROP COLUMN IF EXISTS view_workflows_v2,
DROP COLUMN IF EXISTS edit_workflows_v2,
DROP COLUMN IF EXISTS view_webhooks_v2,
DROP COLUMN IF EXISTS edit_webhooks_v2,
DROP COLUMN IF EXISTS view_prospect_accounts_v2,
DROP COLUMN IF EXISTS edit_prospect_accounts_v2,
DROP COLUMN IF EXISTS view_email_accounts_v2,
DROP COLUMN IF EXISTS edit_email_accounts_v2,
DROP COLUMN IF EXISTS delete_email_accounts_v2;