# --- !Ups

 ALTER TABLE emails_scheduled
   ADD COLUMN IF NOT <PERSON><PERSON><PERSON><PERSON> replied_marked_by_adminid BIGINT REFERENCES accounts (id) ON DELETE SET NULL;


 ALTER TABLE campaigns_prospects
   ADD COLUMN IF NOT <PERSON>XISTS replied_marked_by_adminid BIGINT REFERENCES accounts (id) ON DELETE SET NULL;


# --- !Downs

 ALTER TABLE campaigns_prospects
   DROP COLUMN IF EXISTS replied_marked_by_adminid
  ;

 ALTER TABLE emails_scheduled
   DROP COLUMN IF EXISTS replied_marked_by_adminid
    ;


