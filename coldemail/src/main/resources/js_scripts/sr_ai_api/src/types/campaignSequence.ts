/**
 * Represents the content within a single variant of a campaign step.
 * The specific properties present depend on the parent step's type.
 */
interface BaseStepVariant {
  variant_number: number;
}

/**
 * Represents the content specific to a variant of an Email step.
 */
export interface EmailStepVariantContent extends BaseStepVariant {
  /**
   * The subject of the email.
   * For the first email step, this will be a specific string subject.
   * For subsequent email steps, this will be "{{previous_subject}}".
   */
  subject: string | "{{previous_subject}}";
  /**
   * The body content of the email.
   */
  body: string;
  // Note: 'script' is not present in Email step variants
}

/**
 * Represents the content specific to a variant of a Call step.
 */
export interface CallStepVariantContent extends BaseStepVariant {
  /**
   * The script for the call step.
   * Call steps have no variants, so there will only be one script.
   */
  script: string;
  // Note: 'subject' and 'body' are not present in Call step variants
}

/**
 * Represents a single step in the campaign sequence.
 * Uses a discriminated union based on 'step_type' to define the expected variants.
 */
export type CampaignStep =
  | {
      step_number: number;
      step_type: "email";
      /**
       * An array of variants for an Email step.
       * Contains 2 variants for the first two email steps, 1 variant for subsequent email steps.
       * Each variant is of type EmailStepVariantContent.
       */
      variants: EmailStepVariantContent[];
    }
  | {
      step_number: number;
      step_type: "call";
      /**
       * An array containing the single variant for a Call step.
       * Contains always 1 variant of type CallStepVariantContent.
       */
      variants: CallStepVariantContent[];
    };

// Example Usage (Illustrative - not part of the definition)
/*
const exampleSequence: CampaignStep[] = [
  {
    step_number: 1,
    step_type: "Email",
    variants: [
      {
        variant_number: 1,
        subject: "Quick question about {{company}}",
        body: "Hi {{first_name}},\nSaw that {{company}} is hiring..."
      },
      {
        variant_number: 2,
        subject: "Idea for {{company}} sales",
        body: "Hello {{first_name}},\nNoticed you're a {{prospects_designation}} at {{company}}..."
      }
    ]
  },
  {
    step_number: 2,
    step_type: "Call",
    variants: [
      {
        variant_number: 1,
        script: "Hi {{first_name}}, this is {{my_name}} from {{my_company}}...\n[...call script...]"
      }
    ]
  },
  {
    step_number: 3,
    step_type: "Email",
    variants: [
      {
        variant_number: 1,
        subject: "{{previous_subject}}", // Uses the subject from the previous step's *first* variant
        body: "Hi {{first_name}},\nJust following up on my previous email/call...\n[...body content...]"
      }
    ]
  }
];
*/
