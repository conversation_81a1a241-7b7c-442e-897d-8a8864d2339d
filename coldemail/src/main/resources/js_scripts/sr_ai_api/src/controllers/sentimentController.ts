/**
 * @file sentimentController.ts
 * @description Controller for handling email sentiment analysis requests.
 * <AUTHOR> Name
 * @date 2024-07-27
 */

import { Request, Response, NextFunction } from 'express';
import { createOpenAI } from '@ai-sdk/openai';
import { SentimentAnalysisRequest, SentimentAnalysisResult, EmailMessage } from '../types';
import { generateText } from 'ai';
import { cleanAndParseJson } from '../utils/jsonUtils';
import { sendJsonResponse } from '../utils/responseUtils';

/**
 * Validates if the input message has the required structure
 * @param {any} message - Message to validate
 * @returns {boolean} - True if valid, false otherwise
 */
const isValidMessage = (message: any): message is EmailMessage => {
  return (
    message !== null &&
    typeof message === 'object' &&
    (message.sender === 'User' || message.sender === 'Prospect') &&
    typeof message.content === 'string'
  );
};

/**
 * Validates if the sentiment analysis result has the required structure
 * @param {any} result - Result to validate
 * @returns {boolean} - True if valid, false otherwise
 */
const isValidResult = (result: any): result is SentimentAnalysisResult => {
  return (
    result !== null &&
    typeof result === 'object' &&
    typeof result.sentiment_type === 'string' &&
    typeof result.sentiment === 'string' &&
    typeof result.confidence === 'number' &&
    result.confidence >= 0 &&
    result.confidence <= 1
  );
};

/**
 * Handles the sentiment analysis request
 *
 * @param {Request<{}, {}, SentimentAnalysisRequest>} req - Express request object
 * @param {Response<SentimentAnalysisResult | { error: string }>} res - Express response object
 * @param {NextFunction} next - Express next middleware function
 * @returns {Promise<void>}
 * @throws {Error} - If validation fails or API call fails
 */
export const analyzeSentiment = async (
  req: Request<{}, {}, SentimentAnalysisRequest>,
  res: Response<SentimentAnalysisResult | { error: string }>,
  next: NextFunction
): Promise<void> => {
  try {
    const { conversationMessages, options, replySentimentsForTeam } = req.body;

    // Validate input
    if (!Array.isArray(conversationMessages) || conversationMessages.length === 0) {
      res.status(400).json({ error: 'Conversation messages must be a non-empty array' });
      return;
    }

    // Validate reply sentiments
    if (!Array.isArray(replySentimentsForTeam)) {
      res.status(400).json({ error: 'Reply sentiments must be an array' });
      return;
    }

    // Validate each message in the array
    for (let i = 0; i < conversationMessages.length; i++) {
      if (!isValidMessage(conversationMessages[i])) {
        res.status(400).json({ error: `Invalid message format at index ${i}. Each message must have 'sender' ('User' or 'Prospect') and 'content' properties.` });
        return;
      }
    }

    // Validate API key is provided
    if (!options || !options.apiKey) {
      res.status(400).json({ error: 'API key is required in options' });
      return;
    }

    const {
      model = 'gpt-4-turbo',
      temperature = 0,
      timeout = 30000,
      apiKey,
      apiBaseUrl,
    } = options;

    // Initialize the AI SDK client using the factory function
    const openai = createOpenAI({
      apiKey,
      baseURL: apiBaseUrl,
      compatibility: 'strict',
    });

    // Format the conversation for the prompt
    const formattedConversation = conversationMessages
      .map((msg) => {
        const { sender, content } = msg;
        return `---\n\n**From:** ${sender}\n**Content:** ${content}\n\n---`;
      })
      .join('\n');

    // Format previous reply sentiments
    const formattedReplySentiments = replySentimentsForTeam.length > 0 
      ? `The possible sentiment categories are:\n${replySentimentsForTeam.map(sentiment => `- ${sentiment}`).join('\n')}`
      : '';

    // Use the prompt template from llm_api.js
    const prompt = `
You are an AI assistant tasked with classifying the sentiment of the latest email in a conversation thread between a representative (user) and a prospect for SmartReach.io, a B2B SaaS application that automates cold email, cold calling, LinkedIn etc outreach for sales, marketing and hiring teams. The classification should be based on the content of the latest email and the context provided by the entire email thread.

Below is the email conversation, with each email labeled by the sender (User or Prospect):

${formattedConversation}

${formattedReplySentiments}


Please classify the sentiment of the latest email from the prospect into one of the above categories and provide a confidence score between 0 and 1 indicating how certain you are about your classification. If the latest email is not from the prospect, output \{'sentiment_type': 'none', 'sentiment': 'No prospect reply', 'confidence': 1.0\}.

Additionally, include a 'sentiment_type' field in your output. Determine the sentiment_type as follows:
- If the sentiment is 'No prospect reply', set sentiment_type to 'none'.
- Otherwise, take the part of the sentiment before the colon, convert it to lowercase, and replace any spaces with underscores.

For example:
- "Positive: Wants a demo" → "positive"
- "Do Not Contact: Unsubscribed" → "do_not_contact"
- "Other: Out of office" → "other"
- "No prospect reply" → "none"

Output your answer in JSON format with the keys 'sentiment_type', 'sentiment', and 'confidence'.

This is an example of the output format you should return in:
{
  "sentiment_type": "positive",
  "sentiment": "Wants a demo",
  "confidence": 0.95
}
`;

    // Call the LLM using the provider instance and generateText
    const response = await generateText({
      model: openai(model), // Pass the model ID to the provider instance
      temperature,
      // Timeout is handled by the OpenAI client config now, not here
      system: 'You are a helpful sales email analyzer that returns valid JSON.',
      prompt: prompt, // Use the constructed prompt
      // response_format is implicitly json when using generateObject, 
      // but for generateText with json expectation, rely on prompt instructions.
    });

    // Extract token usage from the response
    const { text: responseText, usage } = response;
    
    // Attempt to parse the JSON response
    let result: SentimentAnalysisResult;
    try {
      result = cleanAndParseJson<SentimentAnalysisResult>(responseText);
      
      // Add token usage metrics to the result
      if (usage) {
        result.token_usage = {
          prompt_tokens: usage.promptTokens,
          completion_tokens: usage.completionTokens,
          total_tokens: usage.totalTokens,
        };
      }
    } catch (parseError) {
      console.error('Error parsing LLM response:', parseError);
      console.error('Raw LLM response content:', responseText);
      res.status(500).json({ error: 'Failed to parse response from LLM' });
      return;
    }

    // Validate the result
    if (!isValidResult(result)) {
      console.error('Invalid response format from LLM. Response:', result);
      res.status(500).json({ error: 'Invalid response format from LLM. Response does not contain the expected structure or has invalid values.' });
      return;
    }

    res.status(200).json(result);

  } catch (error) {
    console.error('Error analyzing email sentiment:', error);
    
    // Assert error as any to handle it
    const err = error as any;

    // Send the complete error data back to the client
    sendJsonResponse(res, err);
  }
}; 