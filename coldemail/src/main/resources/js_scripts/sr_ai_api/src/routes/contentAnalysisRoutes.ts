/**
 * @file contentAnalysisRoutes.ts
 * @description Routes for handling content analysis requests (Spam classification and Moderation).
 */

import express from "express";
import {
  classifySpam,
  moderateText,
} from "../controllers/contentAnalysisController";

const router = express.Router();

/**
 * POST /content-analysis/classify-spam
 * Endpoint for classifying email as spam or not spam
 */
router.post("/classify-spam", classifySpam);

/**
 * POST /content-analysis/moderate
 * Endpoint for moderating text content
 */
router.post("/moderate", moderateText);

export default router;
