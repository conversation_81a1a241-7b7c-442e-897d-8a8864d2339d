{"name": "sr-ai-api", "version": "1.0.0", "description": "Simple Express API with TypeScript", "main": "build/index.js", "scripts": {"build": "tsc", "start": "node build/index.js", "dev": "ts-node src/index.ts", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@ai-sdk/openai": "^1.3.10", "@google-cloud/logging-winston": "^6.0.1", "ai": "^4.3.4", "cors": "^2.8.5", "express": "^4.18.2", "winston": "^3.17.0"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.3", "@types/node": "^20.4.5", "@types/supertest": "^2.0.12", "jest": "^29.6.2", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}}