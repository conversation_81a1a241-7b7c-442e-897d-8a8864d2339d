/**
 * Hello API Endpoint Tests
 * 
 * Integration tests for our hello world API endpoint
 */

import request from 'supertest';
import app from '../src/index';
import { HelloResponse } from '../src/types';
import { describe, it, expect } from "@jest/globals";

describe('Hello World API', () => {
  // Test the POST /hello endpoint
  describe('POST /api/hello', () => {
    it('should return a JSON response with "Hello world!"', async () => {
      // Make a POST request to the endpoint
      const response = await request(app)
        .post('/api/hello')
        .send({})
        .expect('Content-Type', /json/)
        .expect(200);
      
      // Check the response body structure and content
      const responseBody = response.body as HelloResponse;
      expect(responseBody).toHaveProperty('result');
      expect(responseBody.result).toBe('Hello world!');
    });
  });

  // Test error handling
  describe('Error handling', () => {
    it('should return 404 for unknown routes', async () => {
      // Make a request to a non-existent endpoint
      const response = await request(app)
        .get('/api/nonexistent')
        .expect('Content-Type', /json/)
        .expect(404);
      
      // Check error response
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('Route not found');
    });
  });
}); 