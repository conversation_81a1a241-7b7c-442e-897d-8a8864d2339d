# This is the main configuration file for the application.
# https://www.playframework.com/documentation/latest/ConfigFile
# ~~~~~
# Play uses HOCON as its configuration file format.  HOCON has a number
# of advantages over other config formats, but there are two things that
# can be used when modifying settings.
#
# You can include other configuration files in this main application.conf file:
#include "extra-config.conf"
#
# You can declare variables and substitute for them:
#mykey = ${some.value}
#
# And if an environment variable exists when there is no other subsitution, then
# HOCON will fall back to substituting environment variable:
#mykey = ${JAVA_HOME}


play.application.loader = AppApplicationLoader

## Akka
# https://www.playframework.com/documentation/latest/ScalaAkka#Configuration
# https://www.playframework.com/documentation/latest/JavaAkka#Configuration
# ~~~~~
# Play uses Akka internally and exposes Akka Streams and actors in Websockets and
# other streaming HTTP responses.
pekko {
  # "akka.log-config-on-start" is extraordinarly useful because it log the complete
  # configuration at INFO level, including defaults and overrides, so it s worth
  # putting at the very top.
  #
  # Put the following in your conf/logback.xml file:
  #
  # <logger name="akka.actor" level="INFO" />
  #
  # And then uncomment this line to debug the configuration.
  #
  #log-config-on-start = true
}

## Secret key
# http://www.playframework.com/documentation/latest/ApplicationSecret
# ~~~~~
# The secret key is used to sign Play's session cookie.
# This must be changed for production, but we don't recommend you change it in this file.
play.http.secret.key = "6RnraqBzAW2dCBOgOAOo@^Knkoor:6pbdk4nMGJKmEi^coPTMcJMF:V_5O^t6_Vx"

## Modules
# https://www.playframework.com/documentation/latest/Modules
# ~~~~~
# Control which modules are loaded when Play starts.  Note that modules are
# the replacement for "GlobalSettings", which are deprecated in 2.5.x.
# Please see https://www.playframework.com/documentation/latest/GlobalSettings
# for more information.
#
# You can also extend Play functionality by using one of the publically available
# You can also extend Play functionality by using one of the publically available
# Play modules: https://playframework.com/documentation/latest/ModuleDirectory
play.modules {
  # By default, Play will load any class called Module that is defined
  # in the root package (the "app" directory), or you can define them
  # explicitly below.
  # If there are any built-in modules that you want to enable, you can list them here.
  enabled += "scalikejdbc.PlayModule"

  # If there are any built-in modules that you want to disable, you can list them here.

  # scalikejdbc.PlayModule doesn't depend on Play's DBModule
  disabled = [
    "play.api.db.DBModule",
    "play.api.cache.EhCacheModule"
  ]
}

## IDE
# https://www.playframework.com/documentation/latest/IDE
# ~~~~~
# Depending on your IDE, you can add a hyperlink for errors that will jump you
# directly to the code location in the IDE in dev mode. The following line makes
# use of the IntelliJ IDEA REST interface:
#play.editor="http://localhost:63342/api/file/?file=%s&line=%s"

## Internationalisation
# https://www.playframework.com/documentation/latest/JavaI18N
# https://www.playframework.com/documentation/latest/ScalaI18N
# ~~~~~
# Play comes with its own i18n settings, which allow the user's preferred language
# to map through to internal messages, or allow the language to be stored in a cookie.
play.i18n {
  # The application languages
  langs = ["en"]

  # Whether the language cookie should be secure or not
  #langCookieSecure = true

  # Whether the HTTP only attribute of the cookie should be set to true
  #langCookieHttpOnly = true
}

## Play HTTP settings
# ~~~~~
play.http {
  ## Router
  # https://www.playframework.com/documentation/latest/JavaRouting
  # https://www.playframework.com/documentation/latest/ScalaRouting
  # ~~~~~
  # Define the Router object to use for this application.
  # This router will be looked up first when the application is starting up,
  # so make sure this is the entry point.
  # Furthermore, it's assumed your route file is named properly.
  # So for an application router like `my.application.Router`,
  # you may need to define a router file `conf/my.application.routes`.
  # Default to Routes in the root package (aka "apps" folder) (and conf/routes)
  #router = my.application.Router

  ## Action Creator
  # https://www.playframework.com/documentation/latest/JavaActionCreator
  # ~~~~~
  #actionCreator = null

  ## ErrorHandler
  # https://www.playframework.com/documentation/latest/JavaRouting
  # https://www.playframework.com/documentation/latest/ScalaRouting
  # ~~~~~
  # If null, will attempt to load a class called ErrorHandler in the root package,
  #errorHandler = null
  errorHandler = AppErrorHandler

  ## Filters
  # https://www.playframework.com/documentation/latest/ScalaHttpFilters
  # https://www.playframework.com/documentation/latest/JavaHttpFilters
  # ~~~~~
  # Filters run code on every request. They can be used to perform
  # common logic for all your actions, e.g. adding common headers.
  # Defaults to "Filters" in the root package (aka "apps" folder)
  # Alternatively you can explicitly register a class here.
  #filters = Filters

  ## Session & Flash
  # https://www.playframework.com/documentation/latest/JavaSessionFlash
  # https://www.playframework.com/documentation/latest/ScalaSessionFlash
  # ~~~~~
  session {
    # Sets the cookie to be sent only over HTTPS.
    #secure = true

    # Sets the cookie to be accessed only by the server.
    #httpOnly = true

    # Sets the max-age field of the cookie to 5 minutes.
    # NOTE: this only sets when the browser will discard the cookie. Play will consider any
    # cookie value with a valid signature to be a valid session forever. To implement a server side session timeout,
    # you need to put a timestamp in the session and check it at regular intervals to possibly expire it.
    #maxAge = 300

    # Sets the domain on the session cookie.
    #domain = "example.com"
  }

  flash {
    # Sets the cookie to be sent only over HTTPS.
    #secure = true

    # Sets the cookie to be accessed only by the server.
    #httpOnly = true
  }
}

## Netty Provider
# https://www.playframework.com/documentation/latest/SettingsNetty
# ~~~~~
play.server.netty {
  # Whether the Netty wire should be logged
  #log.wire = true

  # If you run Play on Linux, you can use Netty's native socket transport
  # for higher performance with less garbage.
  #transport = "native"
}

## WS (HTTP Client)
# https://www.playframework.com/documentation/latest/ScalaWS#Configuring-WS
# ~~~~~
# The HTTP client primarily used for REST APIs.  The default client can be
# configured directly, but you can also create different client instances
# with customized settings. You must enable this by adding to build.sbt:
#
# libraryDependencies += ws // or javaWs if using java
#
play.ws {
  # Sets HTTP requests not to follow 302 requests
  #followRedirects = false

  # Sets the maximum number of open HTTP connections for the client.
  #ahc.maxConnectionsTotal = 50

  ## WS SSL
  # https://www.playframework.com/documentation/latest/WsSSL
  # ~~~~~
  ssl {
    # Configuring HTTPS with Play WS does not require programming.  You can
    # set up both trustManager and keyManager for mutual authentication, and
    # turn on JSSE debugging in development with a reload.
    #debug.handshake = true
    #trustManager = {
    #  stores = [
    #    { type = "JKS", path = "exampletrust.jks" }
    #  ]
    #}
  }
}

## Cache
# https://www.playframework.com/documentation/latest/JavaCache
# https://www.playframework.com/documentation/latest/ScalaCache
# ~~~~~
# Play comes with an integrated cache API that can reduce the operational
# overhead of repeated requests. You must enable this by adding to build.sbt:
#
# libraryDependencies += cache
#
play.cache {
  # If you want to bind several caches, you can bind the individually
  #bindCaches = ["db-cache", "user-cache", "session-cache"]
}

## Filters
# https://www.playframework.com/documentation/latest/Filters
# ~~~~~
# There are a number of built-in filters that can be enabled and configured
# to give Play greater security.  You must enable this by adding to build.sbt:
#
# libraryDependencies += filters
#
play.filters {
  ## CORS filter configuration
  # https://www.playframework.com/documentation/latest/CorsFilter
  # ~~~~~
  # CORS is a protocol that allows web applications to make requests from the browser
  # across different domains.
  # NOTE: You MUST apply the CORS configuration before the CSRF filter, as CSRF has
  # dependencies on CORS settings.
  cors {
    # Filter paths by a whitelist of path prefixes
    pathPrefixes = ["/api"]


    # The allowed origins. If null, all origins are allowed.
    #allowedOrigins = ["http://www.example.com"]
    allowedOrigins = null

    # The allowed HTTP methods. If null, all methods are allowed
    #allowedHttpMethods = ["GET", "POST"]
    allowedHttpMethods = null

    #allowedHttpHeaders = null
  }

  ## CSRF Filter
  # https://www.playframework.com/documentation/latest/ScalaCsrf#Applying-a-global-CSRF-filter
  # https://www.playframework.com/documentation/latest/JavaCsrf#Applying-a-global-CSRF-filter
  # ~~~~~
  # Play supports multiple methods for verifying that a request is not a CSRF request.
  # The primary mechanism is a CSRF token. This token gets placed either in the query string
  # or body of every form submitted, and also gets placed in the users session.
  # Play then verifies that both tokens are present and match.
  csrf {
    # Sets the cookie to be sent only over HTTPS
    #cookie.secure = true

    # Defaults to CSRFErrorHandler in the root package.
    #errorHandler = MyCSRFErrorHandler
  }

  ## Security headers filter configuration
  # https://www.playframework.com/documentation/latest/SecurityHeaders
  # ~~~~~
  # Defines security headers that prevent XSS attacks.
  # If enabled, then all options are set to the below configuration by default:
  headers {
    # The X-Frame-Options header. disallow attempts to iframe site.
    frameOptions = "DENY"

    # The X-XSS-Protection header. Enables XSS filtering. Rather than sanitizing the page, the browser will prevent rendering of the page if an attack is detected.
    xssProtection = "1; mode=block"

    # The X-Content-Type-Options header. nosniff:Blocks a request if the request destination is of type style and the MIME type is not text/css
    contentTypeOptions = "nosniff"

    # The X-Permitted-Cross-Domain-Policies header. If null, the header is not set.
    #permittedCrossDomainPolicies = "master-only"

    # The Content-Security-Policy header. If null, the header is not set.
    #contentSecurityPolicy = "default-src 'self'"
  }

  ## Allowed hosts filter configuration
  # https://www.playframework.com/documentation/latest/AllowedHostsFilter
  # ~~~~~
  # Play provides a filter that lets you configure which hosts can access your application.
  # This is useful to prevent cache poisoning attacks.
  hosts {
    # Allow requests to example.com, its subdomains, and localhost:9000.
    #allowed = [".example.com", "localhost:9000"]
  }
}

## Evolutions
# https://www.playframework.com/documentation/latest/Evolutions
# ~~~~~
# Evolutions allows database scripts to be automatically run on startup in dev mode
# for database migrations. You must enable this by adding to build.sbt:
#
# libraryDependencies += evolutions
#
play.evolutions {
  # You can disable evolutions for a specific datasource if necessary
  #db.default.enabled = false

  #disable evolutions for read-only read replica srread1
  # 28-Mar-2021: Removed support for srread1 read replica because we anyways ended up using just a single db instance
  #db.srread1.enabled = false


  autoApply = false
  autoApplyDowns = false
  autocommit = false
  useLocks = true
}

## Database Connection Pool
# https://www.playframework.com/documentation/latest/SettingsJDBC
# ~~~~~
# Play doesn't require a JDBC database to run, but you can easily enable one.
#
# libraryDependencies += jdbc
#
play.db {
  # The combination of these two settings results in "db.default" as the
  # default JDBC pool:
  #config = "db"
  #default = "default"

  # Play uses HikariCP as the default connection pool.  You can override
  # settings by changing the prototype:
  prototype {
    # Sets a fixed JDBC connection pool size of 50
    hikaricp.minimumIdle = 50
    hikaricp.maximumPoolSize = 200
  }
}

## JDBC Datasource
# https://www.playframework.com/documentation/latest/JavaDatabase
# https://www.playframework.com/documentation/latest/ScalaDatabase
# ~~~~~
# Once JDBC datasource is set up, you can work with several different
# database options:
#
# Slick (Scala preferred option): https://www.playframework.com/documentation/latest/PlaySlick
# JPA (Java preferred option): https://playframework.com/documentation/latest/JavaJPA
# EBean: https://playframework.com/documentation/latest/JavaEbean
# Anorm: https://www.playframework.com/documentation/latest/ScalaAnorm
#
db {
  # You can declare as many datasources as you want.
  # By convention, the default datasource is named `default`

  # https://www.playframework.com/documentation/latest/Developing-with-the-H2-Database
  default.driver = org.postgresql.Driver
# comment out this while local testing
  default.url = "*********************************************"
  default.username = "heaplabs"
  default.password = "Az2UjfNC2I7"

#     default.url = "*****************************************************************************************/<USER>/Dev/smartreach_backend/coldemail/src/main/resources/server-ca.pem&sslcert=/Users/<USER>/Dev/smartreach_backend/coldemail/src/main/resources/client-cert.pem&sslkey=/Users/<USER>/Dev/smartreach_backend/coldemail/src/main/resources/client-key.pk8"
#     default.username = "vivekg"
#     default.password = "erwreas234nb6dj"

   # default.url = "*********************************************_vivek"
  #  default.username = "vivekg"
  #  default.password = "erwreas234nb6dj"

# comment out this while testing in staging
#   default.url = ${?JDBC_URL}
#   default.username = ${?JDBC_USER}
#   default.password = ${?JDBC_PASS}



  # 28-Mar-2021: Removed support for srread1 read replica because we anyways ended up using just a single db instance
  #srread1.url = "*********************************************"
  #srread1.username = "user"
  #srread1.password = "pass"


  # You can turn on SQL logging for any datasource
  # https://www.playframework.com/documentation/latest/Highlights25#Logging-SQL-statements
  #default.logSql=true
}

# ScalikeJDBC original configuration
# REF: http://scalikejdbc.org/documentation/playframework-support.html
db.default.poolInitialSize = 15
db.default.poolMaxSize = 50
db.default.connectionTimeoutMillis = 25000
db.default.poolValidationQuery = "select 1 as one"

# 28-Mar-2021: Removed support for srread1 read replica because we anyways ended up using just a single db instance
#db.srread1.poolInitialSize = 15
#db.srread1.poolMaxSize = 50
#db.srread1.connectionTimeoutMillis = 25000
#db.srread1.poolValidationQuery = "select 1 as one"



scalikejdbc.global.loggingSQLAndTime.enabled = true
scalikejdbc.global.loggingSQLAndTime.logLevel = info
scalikejdbc.global.loggingSQLAndTime.warningEnabled = true
scalikejdbc.global.loggingSQLAndTime.warningThresholdMillis = 1000
scalikejdbc.global.loggingSQLAndTime.warningLogLevel = warn
scalikejdbc.global.loggingSQLAndTime.singleLineMode = false
scalikejdbc.global.loggingSQLAndTime.printUnprocessedStackTrace = false
scalikejdbc.global.loggingSQLAndTime.stackTraceDepth = 10


redis.keyPrefix = "srlocal"
redis.uri = "redis://localhost:6379"

# Logger
# ~~~~~
# You can also configure logback (http://logback.qos.ch/),
# by providing an application-logger.xml file in the conf directory.

# Root logger:
logger.root = ERROR

# Logger used by the framework:
logger.play = INFO

# Logger provided to your application:
logger.application = DEBUG


#rabbit-mq configuration
rabbitmq.host = "amqp://localhost:5672"
rabbitmq.username = "guest"
rabbitmq.password = "guest"
rabbitmq.virtualHost = "/"
rabbitmq.prefix = "coldemailLocal"


rabbitmq.exchangeType = direct
rabbitmq.durable = true
rabbitmq.autoAck = false
rabbitmq.prefetchCount = 5


rabbitmq.basicQos = true
rabbitmq.retries = 5

rabbitmq.connectionTimeout = 6000000

email.messageid.suffix = "local@smartreachio"

mailtesterUsername = "hl2017hl"

# Google OAuth2
# Note: 13 April 2024
# The below  google.clientID and  google.clientSecret are not getting used any where so commenting it out
# google.clientID = "test-google-clientID"
# google.clientSecret = "test-google-clientSecret"
google.googleAuthApiKeyIdForNewAuthFlow = 1
google.googleAuthApiKeyIdV2 = 4 # Google API Key v2 - 21st May 2022

google.recaptchaSecret = "6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe"

# Microsoft OAuth2
microsoft.clientID = "5e9c6f67-51d5-4321-ac0c-d9afb65aa7a7"
microsoft.clientSecret = "****************************************"

# HubSpot OAuth2
# Live https://app.hubspot.com/developer/6602102/applications - <EMAIL>

hubspot.clientID = "test-hubspot-clientID"
hubspot.clientSecret = "test-hubspot-clientSecret"


# Zoho OAuth2
# LIVE APP https://accounts.zoho.com/developerconsole OR https://api-console.zoho.com/ - <EMAIL>

zoho.clientID = "test-zoho-clientID"
zoho.clientSecret = "test-zoho-clientSecret"

# Piprdrive OAuth2
# LIVE APP https://srio-sandbox-1cf739.pipedrive.com/settings/marketplace_manager#/ - <EMAIL>

pipedrive.clientID = "test-pipedrive-clientID"
pipedrive.clientSecret = "test-pipedrive-clientSecret"

# Salesforce OAuth2
# LIVE APP https://ap16.lightning.force.com/lightning/setup/ConnectedApplication/home - <EMAIL>

salesforce.clientID = "test-salesforce-clientID"
salesforce.clientSecret = "test-salesforce-clientSecret"

calendly.clientID = "test-calendly-clientID"
calendly.clientSecret = "test-calendly-clientSecret"
calendly.webhookSigningKey = "test-calendly-webhookSigningKey"
# application.domain = "http://localhost:9000"
# application.domain = "http://coldemail-develop-env.us-east-1.elasticbeanstalk.com"
# comment out this while local testing
application.domain = "http://localhost:3001"
application.domain = ${?APP_DOMAIN}
# comment out this while testing in staging

application.openTrackingDomain = "http://coldemail-develop-env.us-east-1.elasticbeanstalk.com"
application.isprod = false
application.istest = false

application.encryptionKey = "testencryptionkey"  # changing this will break tests
application.emailnotificationsettingsencryptionKey = "testemailnotificationsettings"


# REF: https://stackoverflow.com/a/36283823
play.http.parser.maxDiskBuffer = 100MB
play.http.parser.maxMemoryBuffer = 50MB
parsers.anyContent.maxLength = 100MB


# Mailgun
mailgun {
  adminEmail = "<EMAIL>"
  adminName = "SmartReach.io"
  domain = "mg.smartreach.io"
  apiKey = "************************************"
}


stripe {
  apiKey = "sk_test_51OhSSUSEnEC02BAgYu8JiZiQ6TRZ5RptfgFS4fqHo9D8W2zlnUHkStHx5UwnVMsrLcv1GZqY1e4P9ERN9BRy3ySW00CUnR8rFC"
  publishableKey= "pk_test_51OhSSUSEnEC02BAgXmz9TcfFlU8s1up4Qd5INUiYV27Vep95Vt0hHAokCLRAfXV9vCqefkfoOpb4V7si52OBE4h500ut4LPrLL"
}

bright_data_apiKey = "test_api_key"

# Assets (getImage caching)
# "assets.cache./public/assets/get_image.png" = "no-cache"

# smtpLocalHostname = "localhost"


# Assets (getImage caching)
"assets.cache./public/assets/get_image.png" = "no-cache"

#NOT USED
mailboxlayerApiKey = "test-mailboxlayerApiKey"

bouncerApiKey = "test-bouncerApiKey"
deBounceApiKey = "test-deBounceApiKey"
listCleanApiKey = "test-listCleanApiKey"
userCheckApiKey = "test-userCheckApiKey"

deBounceBucketName = "test-prod-bucket"

#NOT USED
intercomSec = "test-intercomSec"

#NOT USED
msg91authkey = "test-msg91authkey"

#NOT USED
auth0ipblacklistcheckkey = "test-auth0ipblacklistcheckkey"

firstpromoter {
  apiKey="test"
  wid="test"
  referralCampaignId = "testCampaignId"
}

admitad {
  postbackKey="test"
  campaignCode="test"
  revisionSecretKey="test"
}



thirdpartyApiKeys {

    mxtoolboxApiKey = "test-key"

}

cloudflare {
authKey="test-auth-api-key"
authEmail="<EMAIL>"
}


enc_keys {
  email_settings_credential_enc_key = "test_key_1"
  email_dataplatform_credential_enc_key = "test_key_2"
  workflow_crm_settings_credential_enc_key = "test_key_3"
  accounts_credential_enc_key = "test_key_4"
  dkim_records_credential_enc_key = "test_key_5"
  # comment out this while local testing
    teams_accounts_credential_enc_key = "test_key_6"
  # comment out this while testing in staging
  teams_accounts_credential_enc_key = ${?SR_ENC_KEY_TEAMS_ACCOUNTS_CREDENTIAL}
  organizations_credential_enc_key = "test_key_7"
  phantom_buster_api_enc_key = "test_key_8"
  native_calling_api_enc_key = "test_key_9"
  phantom_buster_webhook_team_secret_enc_key = "test_key_10"
  linkedin_session_cookie_enc_key = "test_key_11"
  phantombuster_proxy_password_enc_key = "test_key_13"
  linkedin_account_password_enc_key = "test_key_14"
  calendly_settings_credential_enc_key = "test_key_15"
}
loginSessionTimeInMillis = *********

emailImageBucketName = "sr-email-message-images-local"
csvBucketName = "sr-prospects-csv-local"
csvBucketNameForLeadFinder = "ld-zip/nxd/flattened"
voicedropBucketNameStaging = "sr-voicemail-recording"
voicedropBucketNameProd = "sr-voicemail-recording-prd"


multichannel_limits {
    linkedin{
        view_profile {
            min = 5
            max = 100
        },
        inmails {
            min = 5
            max = 100
        },
        messages {
            min = 5
            max = 100
        },
        connection_requests {
            min = 5
            max = 20
        }
    },
    whatsapp {
        min = 5
        max = 100
    },
    phone {
        min = 5
        max = 100
    },
    call {
        min = 5
        max = 100
    }
}

billing_app {
    api_key = "fKgbR2Az22%WqwqKkoABDNMZZu$B9n!x"
    api_url = "http://localhost:9001"
    billing_client_id = "test-client-id"
    billing_client_secret= "test-client-secret"
}

whois_apilayer {
    api_key = "********************************"
}

whois_rapidAPI {
    api_key = "api_key_for_api_layer",
    api_host = "api_host_for_api_layer"
}

pusher {
    appId = "*******"
    key = "fc450696a096ce887ee8"
    secret = "f463ef59e4f5865caaee"
    cluster = "ap2"
}

#22-Jan-2024 SCYLLA_COMMENTED_OUT
#scylla {
#keyspace = "srlocal",
#nodeAndPorts = "localhost:9042",
#username = "local",
#password = "test"
#}

#kafka {
#    server_url = "localhost:9092"
#}

intercom_api {
  access_token = "test_token"
}

prometheus {
  push_gateway = "test_ip"
}

gpt_api_key = "********************************************************************************************************************************************************************"

gemini_api_key = "test-gemini-api-key"

calendar_app {
  enc_key = "test-calendar-enc-key"
  api_key = "test_api_key_calendar_app"
  api_url = "http://localhost:3000"
  meetings_client_id="test-client-id"
  meetings_client_secret="test-client-secret"

}

warmuphero {
    api_key = "quSIKjbGTBkbQUkc8bIxV7MQAXwlQBrU"
}

twilio {
    account_sid = "TWILIO_ACCOUNT_SID",
    application_sid = "TWILIO_TWIML_APP_SID",
    api_key = "API_KEY",
    api_secret = "API_SECRET",
    auth_token = "AUTH_TOKEN"
}


gpt_api_key = "test-gpt-api-key"

sr_api_base_url = "test-api-base-url"


theCompaniesApi {
    apiKey = "test-api-key",
}

prospect_daddy{
    any_mail_finder_key = "test_key_for_dev"
}

zapmail {
    api_key = "test-zapmail-api-key"
    user_id = "test-zapmail-user-id"
}

captain_data {
    api_key = "test-api-key"
    project_id = "test-project-id"

    view_linkedin_profile {
        workflow_uid = "test-view-profile-workflow-uid"
        step_uid = "test-view-profile-step-uid"
    }

    send_linkedin_connection_request {
        workflow_uid = "test-send-connection-workflow-uid"
        step_uid = "test-send-connection-step-uid"
    }

    extract_linkedin_connections {
        workflow_uid = "test-extract-connections-workflow-uid"
        step_uid = "test-extract-connections-step-uid"
    }

    send_linkedin_message {
        workflow_uid = "test-send-message-workflow-uid"
        step_uid = "test-send-message-step-uid"
    }

    retrieve_linkedin_message {
        workflow_uid = "test-retrieve-message-workflow-uid"
        step_uid = "test-retrieve-message-step-uid"
    }

    retrieve_linkedin_conversations {
        workflow_uid = "test-retrieve-conversations-workflow-uid"
        step_uid = "test-retrieve-conversations-step-uid"
    }
}

maildoso {
    maildoso_api_key = "748442f0-df15-4548-87b0-2fd595703eea"
}


smartreach_auth {
   client_id="4e31f240-e23f-462b-a26d-3ac2e14a1675"
   client_secret="I0g2iVhQKo6THAa6Re2G4AlcH8"
   common_auth_hydra_admin_api="http://127.0.0.1:4445"
   common_auth_frontend_url="http://localhost:5555"
   post_login_frontend_url="http://localhost:3001"
   common_auth_backend_url="http://localhost:9000"
   common_auth_server_url="http://localhost:4444"
}


api_layer_currency {
    sr_api_layer_currency_api_key = "test-api-layer-api-key"
}