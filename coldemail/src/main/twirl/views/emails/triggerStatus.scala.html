@(first_name: String, trigger_name: String, error_msg: String, triggers_page_url: String)

@__header()


<!-- Email Body -->
<span class="preheader"
      style="display: none !important; visibility: hidden; mso-hide: all; font-size: 1px; line-height: 1px; max-height: 0; max-width: 0; opacity: 0; overflow: hidden;">
SmartReach is getting an error while running your workflow.

</span>
<tr>
    <td class="email-body" width="570" cellpadding="0" cellspacing="0"
        style="word-break: break-word; margin: 0; padding: 0; font-family: &quot;Nunito Sans&quot;, Helvetica, Arial, sans-serif; font-size: 16px; width: 100%; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;">
        <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0"
               role="presentation"
               style="width: 570px; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; margin: 0 auto; padding: 0;"
               bgcolor="#FFFFFF">
            <!-- Body content -->
            <tr>
                <td class="content-cell"
                    style="word-break: break-word; font-family: &quot;Nunito Sans&quot;, Helvetica, Arial, sans-serif; font-size: 16px; padding: 45px;">
                    <div class="f-fallback">
                        <h1 style="margin-top: 0; color: #333333; font-size: 22px; font-weight: bold; text-align: left;"
                            align="left">Hi @first_name!</h1>
                        <p
                                style="font-size: 16px; line-height: 1.625; color: #51545E; margin: .4em 0 1.1875em;">
                            SmartReach is getting an error while running your workflow. This usually happens
                            when the workflow field mapping is incorrect or if there is any integration API failure. Please see the error below for more info.</p>
                        <p
                                style="font-size: 16px; line-height: 1.625; color: #51545E; margin: .4em 0 1.1875em;">
                            Error: @error_msg</p>


                        <!-- Action -->
                        <table class="body-action" align="center" width="100%" cellpadding="0"
                               cellspacing="0" role="presentation"
                               style="width: 100%; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; margin: 30px auto; padding: 0;">
                            <tr>
                                <td align="center"
                                    style="word-break: break-word; font-family: &quot;Nunito Sans&quot;, Helvetica, Arial, sans-serif; font-size: 16px;">

                                    <table width="100%" border="0" cellspacing="0" cellpadding="0"
                                           role="presentation">
                                        <tr>
                                            <td align="center"
                                                style="word-break: break-word; font-family: &quot;Nunito Sans&quot;, Helvetica, Arial, sans-serif; font-size: 16px;">
                                                <a href="@triggers_page_url"
                                                   class="f-fallback button" target="_blank"
                                                   style="color: #FFF; border-color: #3869d4; border-style: solid; border-width: 10px 18px; background-color: #3869D4; display: inline-block; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box;">
                                                    Go to Workflow</a>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </div>
                </td>
            </tr>
        </table>
    </td>
</tr>

@__footer()
