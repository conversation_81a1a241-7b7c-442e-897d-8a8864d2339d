@(
prospect_name: Option[String],
prospect_email: Option[String],
first_name: String,
prospect_number: String,
received_on_number: String
)

@__header()

<!-- Email Body -->
<span class="preheader"
      style="display: none !important; visibility: hidden; mso-hide: all; font-size: 1px; line-height: 1px; max-height: 0; max-width: 0; opacity: 0; overflow: hidden;">You have received an incoming call from @received_on_number.</span>
<tr>
    <td class="email-body" width="570" cellpadding="0" cellspacing="0"
        style="word-break: break-word; margin: 0; padding: 0; font-family: &quot;Nunito Sans&quot;, Helvetica, Arial, sans-serif; font-size: 16px; width: 100%; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;">
        <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0"
               role="presentation"
               style="width: 570px; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; margin: 0 auto; padding: 0;"
               bgcolor="#FFFFFF">
            <!-- Body content -->
            <tr>
                <td class="content-cell"
                    style="word-break: break-word; font-family: &quot;Nunito Sans&quot;, Helvetica, Arial, sans-serif; font-size: 16px; padding: 45px;">
                    <div class="f-fallback">
                        <h1 style="margin-top: 0; color: #333333; font-size: 22px; font-weight: bold; text-align: left;"
                            align="left">Hi @first_name,</h1>
                        <p
                                style="font-size: 16px; line-height: 1.625; color: #51545E; margin: .4em 0 1.1875em;">
                            You've received an incoming call from @prospect_name @prospect_number @prospect_email</p>
                        <p>
                            The call was received on calling number : @received_on_number
                        </p>
                        <p>
                            We suggest you setup call forwarding for @received_on_number,
                            so you receive incoming calls on your mobile phones.
                        </p>
                        <p
                                style="font-size: 16px; line-height: 1.625; color: #51545E; margin: .4em 0 1.1875em;">
                            If you have any questions, feel free to <a
                                href="mailto:support@@smartreach.io" style="color: #3869D4;">email
                            </a> our
                            customer success team (We're lightning quick at replying). We
                            also offer <a href="https://smartreach.io/" target="_blank"
                                          style="color: #3869D4;">live
                            chat</a> during business hours.</p>
                        <p
                                style="font-size: 16px; line-height: 1.625; color: #51545E; margin: .4em 0 1.1875em;">
                            Thanks,
                            <br />SmartReach Team</p>
                        <p
                                style="font-size: 16px; line-height: 1.625; color: #51545E; margin: .4em 0 1.1875em;">
                            <strong>P.S.</strong> Check out our
                            <a href="https://help.smartreach.io" target="_blank"
                               style="color: #3869D4;">help
                                documentation</a>.
                            Or, just reply to this email, the SmartReach.io support team is always
                            ready to help!</p>
                    </div>
                </td>
            </tr>
        </table>
    </td>
</tr>

@__footer()
