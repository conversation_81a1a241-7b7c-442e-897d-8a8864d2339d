package api.call.traits

sealed trait DeleteNumberError

object DeleteNumberError {
  case class ErrorWhileFetchingPhoneSID(err: Throwable) extends DeleteNumberError

  case class ErrorWhileFetchingAccountFromDB(err: Throwable) extends DeleteNumberError

  case class ErrorWhileDeleting<PERSON><PERSON><PERSON><PERSON>lio(err: Throwable) extends DeleteNumberError

  case class NumberCouldNotBeDeletedFromTwilio(err: String) extends DeleteNumberError

  case class SQLExceptionWhileDeleting(err: Throwable) extends DeleteNumberError

  case class AccountNotFoundError(err: String) extends DeleteNumberError

  case class ErrorWhileResettingCache(err: Throwable) extends DeleteNumberError
}