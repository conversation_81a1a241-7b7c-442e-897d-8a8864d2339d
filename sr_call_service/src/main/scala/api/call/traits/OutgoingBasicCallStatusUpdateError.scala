package api.call.traits

sealed trait OutgoingBasicCallStatusUpdateError

object OutgoingBasicCallStatusUpdateError {

  case class ErrorWhileFetchingConfUuid(err: Throwable) extends OutgoingBasicCallStatusUpdateError

  case class ErrorWhileCompletingConference(err: Throwable) extends OutgoingBasicCallStatusUpdateError

  case class ErrorWhileUpdatingParticipantStatus(err: Throwable) extends OutgoingBasicCallStatusUpdateError

  case class ErrorWhileUpdatingCallSID(err: Throwable) extends OutgoingBasicCallStatusUpdateError

  case class ErrorWhileFetchingPhoneDetails(err: Throwable) extends OutgoingBasicCallStatusUpdateError

  case class ErrorWhileFetchingNotificationObjectDetails(err: Throwable) extends OutgoingBasicCallStatusUpdateError

  case class ErrorWhileFetchingOrgId(err: Throwable) extends OutgoingBasicCallStatusUpdateError

  case class ErrorWhileSendingPusherNotification(err: Throwable) extends OutgoingBasicCallStatusUpdateError

}