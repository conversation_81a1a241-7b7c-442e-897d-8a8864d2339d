package api.call.traits

sealed trait UpdateSubAccountCreditError

object UpdateSubAccountCreditError {

  case class ErrorWhileAddingWarning(err: AddOrgCallingCreditWarningError) extends UpdateSubAccountCreditError

  case class ErrorWhileFetchingTotalCallingAccount(e: Throwable) extends UpdateSubAccountCreditError

  case class ErrorWhileGettingSubAccountFromDB(err: Throwable) extends UpdateSubAccountCreditError

  case class ErrorWhileGettingUsageFromTwilio(err: GetSubAccountUsageError) extends UpdateSubAccountCreditError

  case class ErrorWhileParsingLong(err: Throwable) extends UpdateSubAccountCreditError

  case class CreditSaveErrorInDB(err: Throwable) extends UpdateSubAccountCreditError

}