package api.call.traits

import api.call.models.PhoneNumber

sealed trait BuyNumberError

object BuyNumberError {
  case class BuyNumberApiError(err: Throwable) extends BuyNumberError

  case class GetNumberError(error: GetAvailableNumberError) extends BuyNumberError

  case class SQLException(err: Throwable, phoneNumber: PhoneNumber) extends BuyNumberError

  case class ErrorWhileFetchingPriceObject(err: GetNumberPriceError) extends BuyNumberError

  case object InsufficientBalance extends BuyNumberError

  case object PriceObjectNotFound extends BuyNumberError

  case class SubAccountError(err_type: SubAccountCreationError) extends BuyNumberError

  case object MaximumNumberLimitReached extends BuyNumberError

  case class ErrorWhileFetchingTotalCallAccountsInOrg(e: Throwable) extends BuyNumberError
}