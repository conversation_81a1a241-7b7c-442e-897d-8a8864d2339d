package api.call.models

import play.api.libs.json.*

case class ConferenceSid(
  sid: String
) extends AnyVal

object ConferenceSid {
  implicit val reads: Reads[ConferenceSid] = new Reads[ConferenceSid] {
    override def reads(json: JsValue): JsResult[ConferenceSid] = {
      json match {
        case JsString(sid) => JsSuccess(ConferenceSid(sid = sid))
        case randomValue => JsError(s"expected String, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[ConferenceSid] = new Writes[ConferenceSid] {
    override def writes(conf_sid: ConferenceSid): JsValue = JsString(conf_sid.sid)
  }
}