package api.call.models

import api.call.traits.GetCallDetailsError
import play.api.libs.json.*

import scala.util.{Failure, Success, Try}

case class InitiatorCallSid(
  sid: String
) extends AnyVal

object InitiatorCallSid {

  implicit val reads: Reads[InitiatorCallSid] = new Reads[InitiatorCallSid] {
    override def reads(ev: JsValue): JsResult[InitiatorCallSid] = {
      ev match {
        case JsString(id) => JsSuccess(InitiatorCallSid(sid = id))
        case randomValue => JsError(s"expected String, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[InitiatorCallSid] = new Writes[InitiatorCallSid] {
    override def writes(o: InitiatorCallSid): JsValue = JsString(o.sid)
  }

  def extractInitiatorCallSid(params: Map[String, String]): Either[GetCallDetailsError, InitiatorCallSid] = {
    Try {
      params("initiator_call_sid")
    } match {
      case Failure(err) =>

        Left(GetCallDetailsError.InitiatorCallSidNotFound)

      case Success(call_sid) =>
        Right(InitiatorCallSid(sid = call_sid))
    }
  }
}