package api.accounts

import play.api.libs.json.{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, JsString, JsSuc<PERSON>, JsValue}

import scala.util.{Failure, Success, Try}

enum AccountWarningCodeType {
  case ProspectLimit80Percent
  case ProspectLimitExceeded
  case LowCallingCredit
  case CallingFeatureSuspended
  case ProspectSavedWarning

  override def toString: String = this match {
    case ProspectLimit80Percent => AccountWarningCodeType.PROSPECT_LIMIT_80_PERCENT
    case ProspectLimitExceeded => AccountWarningCodeType.PROSPECT_LIMIT_EXCEEDED
    case LowCallingCredit => AccountWarningCodeType.LOW_CALLING_CREDIT
    case CallingFeatureSuspended => AccountWarningCodeType.CALLING_FEATURE_SUSPENDED
    case ProspectSavedWarning =>AccountWarningCodeType.PROSPECT_SAVED_WARNING

  }
}

object AccountWarningCodeType {
  private val PROSPECT_LIMIT_80_PERCENT = "prospect_limit_80_percent"
  private val PROSPECT_LIMIT_EXCEEDED = "prospect_limit_exceeded"
  private val LOW_CALLING_CREDIT = "low_calling_credit"
  private val CALLING_FEATURE_SUSPENDED = "calling_feature_suspended"
  private val PROSPECT_SAVED_WARNING = "prospect_saved_warning"

  def fromString(key: String): Try[AccountWarningCodeType] = Try {
    key match {
      case PROSPECT_LIMIT_80_PERCENT => ProspectLimit80Percent
      case PROSPECT_LIMIT_EXCEEDED => ProspectLimitExceeded
      case LOW_CALLING_CREDIT => LowCallingCredit
      case CALLING_FEATURE_SUSPENDED => CallingFeatureSuspended
      case PROSPECT_SAVED_WARNING => ProspectSavedWarning
      case _ => throw new IllegalArgumentException(s"Invalid AccountWarningCodeType: $key")
    }
  }

  implicit val format: Format[AccountWarningCodeType] = new Format[AccountWarningCodeType] {
    override def writes(o: AccountWarningCodeType): JsValue = {
      JsString(o.toString)
    }

    override def reads(json: JsValue): JsResult[AccountWarningCodeType] = {
      fromString(json.as[String]) match {
        case Success(value) => JsSuccess(value)
        case Failure(_) => JsError(s"Invalid AccountWarningCodeType type :: $json")
      }
    }
  }
}