package api

import api.accounts.*
import play.api.libs.json.Json
import api.accounts.models.OrgId
import api.config.AppConfigCommonAuth
import api.integrations.{CalendlyOAuthSettings, IntegrationTPOAuthSettings}
import com.typesafe.config.{Config, ConfigFactory}
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat
import utils.ISRLogger
import utils.helpers.RandomNumberHelper

import scala.collection.mutable
import scala.util.Random


object AppConfig {

  val  shouldNeverHappenKey = "SHOULD_NEVER_HAPPEN"
  object MultiChannel {

    val sms_min_delay_seconds = 50
    val sms_max_delay_seconds = 100

    val call_min_delay_seconds = 60
    val call_max_delay_seconds = 120

    val whatsapp_min_delay_seconds = 50
    val whatsapp_max_delay_seconds = 100

    val linkedin_min_delay_seconds = 60
    val linkedin_max_delay_seconds = 120
    val linkedin_minConsecutiveEmailDelay = 60

    val linkedin_connection_request_add_note_character_limit = 300 // Actual figure 300  // https://www.linkedin.com/pulse/connecting-linkedin-300-characters-larbi-gallagher/
    val linkedin_message_body_character_limit = 8000 // Actual figure 8000  `self tested `
    val linkedin_in_mail_message_body_character_limit = 1900 // Actual figure 1900  https://www.linkedin.com/help/linkedin/answer/a411986/inmail-character-limits?lang=en#:~:text=InMail%20messages%20can%20have%20up,many%20characters%20you've%20entered.
    val linkedin_in_mail_subject_character_limit = 200 // Actual figure 200  https://www.linkedin.com/help/linkedin/answer/a411986/inmail-character-limits?lang=en#:~:text=InMail%20messages%20can%20have%20up,many%20characters%20you've%20entered.
    val whatsapp_message_body_character_limit = 67000 // Actual figure 67000 `Self tested`
  }

  object MobileApp {
    val ios_version = "1.0.0"
    val android_version = "1.0.5"
  }

  object Notes {
    val timeThresholdOfUpdatingNotesInHours: Int = 2
  }

  object Maildoso {
    val maildoso_api_key: String = config.getString("maildoso.maildoso_api_key")
    val add_variations: Int = 15
    val maildoso_user_id: Long = 2758 // todo: Neil's user id in maildoso, switch to Prateek's user_id when we go live
    val SMTP_HOST: String = "smtp.maildoso.com"
    val SMTP_PORT: Int = 587
    val DELETE_DOMAIN_EMAILS_BEFORE_CANCELLATION_MINUTES: Int = 30
  }

  object ZapMail {
    val api_key: String = config.getString("zapmail.api_key")
    val userId: String = config.getString("zapmail.user_id")
    val domainPurchaseYears = 1
    val mailBoxPurchaseTerm = "MONTHLY"
    val minDomainPriceToDisplay = 20
    val minDomainRenewPriceToDisplay = 50
  }
  val config: Config = ConfigFactory.load()

  val dashboardDomain = config.getString("application.domain")
  val apiDomain = "https://api.smartreach.io"
  val gotoBillingAppUrl = s"$dashboardDomain/dashboard/billing_settings/goto_billing"

  val firstpromoterBaseUrl = "https://firstpromoter.com/api"

  val anyMailFinderApiKey = config.getString("prospect_daddy.any_mail_finder_key")
  val baseSpamTestsPerEmailAccount = 4

  //billing-related
  val min_seats: Int = 2

  val gptApiHitLimit = 6
  val gptApiRateLimitIntervalInSeconds = 60

  val maxRetryCountForEmailHeathCheck = 10

  object SlackIntegration {
    val slackWebhookPath = "https://hooks.slack.com/"

    val username = "smartreach_notifier"

  }

  object Voicemail {
    val maxFileNameLength = 250
    val maxFileDescriptionLength = 500
  }

  val whSecurityApiKey: String = config.getString("warmuphero.api_key")

  val defaultTrackingHost = "via.smartreach-mail.com"

  val defaultCNAMEHostForCustomTrackingDomain = "verimailten.com" //ALL custom tracking domain pointed here by customers from 12 July 2022

  val isDevDomain = dashboardDomain.contains(".sreml")
  val isDev5Domain = dashboardDomain.contains("dev5.sreml")
  val isDev4Domain = dashboardDomain.contains("dev4.sreml")
  val isDev3Domain = dashboardDomain.contains("dev3.sreml")
  val isDev2Domain = dashboardDomain.contains("dev2.sreml")
  val isDev1Domain = dashboardDomain.contains("dev.sreml")
  val isProd = !isDevDomain && config.getBoolean("application.isprod")
  val isTest = config.getBoolean("application.istest")

  // only for logging, do not use this
  val isProdRaw: Boolean = config.getBoolean("application.isprod")

  val isLocalDevDomain = dashboardDomain.contains("localhost") && !config.getBoolean("application.isprod")


  val supportUserClientAccountAccessTimeInSeconds = 1800
  val googleRecaptchaTimeLimitInSeconds = 1800
  val googleRecaptchaAttempts = 3

  val campaignSendReportAnalysisIntervalInMinutes = 60

  val campaignSendReportAnalysisIntervalInMinutesNew = 30

  val orgDataResetFromCacheMaxTimeInSeconds = 5 * 60

  val enable_campaign_ai_sequence_for_org_ids_greater_than = 15000

  /**
   * This is the threshold organization ID used for determining which organizations
   * should use the new reply sentiment structure. Organizations with IDs less than or equal
   * to this value will use the old structure, while newer organizations will use the new structure.
   *
   * This replaces the previous latestTeamId constant.
   */
  val latestOrgIdForReplySentiments = 21106L

  val userLoginTimeInSeconds = config.getInt("loginSessionTimeInMillis")
  
  /*
    Note:
      6 June 2025
      We were Migrating Not_Classified type to old Referral etc type. 
      So below were the teams Ids which are getting Migrated which were previously Not  classified
   */
  val listOfTeamIdsWhichHadNotClassified: List[Long] = List(21622,21623,21624,21625,21626,21627,21628,21629,21630,21631,21633,21634,21635,21636,21637,21638,21639,21640,21641,21642,21643,21644,21645,21646,21647,21648,21649,21650,21651,21652,21653,21654,21655,21656,21657,21658,21659,21660,21661,21662,21663,21664,21665,21666,21667,21668,21669,21670,21671,21672,21673,21674,21675,21676,21677,21678,21679,21680,21681,21682,21683,21684,21685,21686,21687,21688,21689,21690,21691,21692,21693,21694,21695,21696,21697,21698,21699,21700,21701,21702,21703,21704,21705,21706,21707,21708,21709,21711,21712,21713,21714,21715,21716,21717,21718,21719,21720,21721,21722,21723,21724,21725,21726,21727,21728,21729,21730,21732,21733,21734,21735,21736,21737,21738,21739,21740,21741,21742,21743,21744,21745,21746,21747,21748,21749,21750,21751,21752,21753,21754,21755,21756,21757,21758,21759,21760,21761,21762,21763,21764,21765,21766,21767,21768,21769,21770,21771,21772,21773,21774,21775,21776,21777,21778,21779,21780,21781,21782,21783,21784,21785,21786,21787,21788,21789,21790,21791,21792,21793,21794,21795,21796,21797,21798,21799,21800,21801,21802,21803,21804,21805,21806,21807,21808,21809,21810,21811,21812,21813,21814,21815,21816,21817,21818,21819,21820,21821,21822,21823,21824,21825,21827,21828,21829,21830,21831,21832,21833,21834,21835,21838,21839,21840,21841,21842,21843,21844,21845,21846,21847,21848,21849,21850,21851,21852,21853,21854,21855,21856,21857,21858,21859,21860,21861,21862,21863,21864,21865,21866,21867,21868,21869,21870,21871,21872,21873,21874,21875,21876,21877,21878,21879,21880,21881,21882,21883,21884,21885,21886,21887,21888,21889,21890,21891,21892,21893,21894,21895,21896,21897,21898,21899,21900,21901,21902,21903,21904,21905,21906,21907,21908,21909,21910,21911,21912,21913,21914,21915,21916,21917,21918,21919,21920,21921,21922,21923,21924,21925,21926,21927,21928,21929,21930,21931,21932,21933,21934,21935,21936,21937,21938,21939,21940,21941,21942,21943,21944,21945,21946,21947,21948,21949,21950,21951,21952,21953,21954,21955,21956,21957,21958,21959,21960,21961,21962,21963,21964,21969,21970,21971,21972,21973,21974,21975,21976,21977,21978,21979,21980,21981,21982,21983,21984,21985,21986,21987,21988,21989,21990,21991,21992,21993,21994,21995,21996,21997,21998,21999,22000,22001,22002,22003,22004,22005,22006,22007,22008,22009,22010,22011,22012,22013,22014,22015,22016,22017,22018,22019,22020,22021,22022,22023,22024,22025,22026,22027,22028,22029,22030,22031,22032,22033,22034,22035,22036,22037,22038,22039,22040,22041,22042,22043,22044,22045,22046,22047,22048,22049,22050,22051,22052,22053,22054,22055,22056,22057,22058,22059,22060,22061,22062,22063,22064,22065,22066,22067,22068,22069,22070,22071,22072,22073,22074,22075,22076,22077,22078,22079,22080,22081,22082,22083,22084,22085,22086,22087,22088,22089,22090,22091,22092,22093,22094,22095,22096,22097,22098,22099,22100,22101,22102,22103,22104,22105,22106,22107,22108,22109,22110,22111,22112,22113,22114,22115,22116,22117,22118,22119,22120,22121,22122,22123,22124,22125,22126,22127,22128,22129,22130,22131,22132,22133,22134,22135,22136,22137,22138,22139,22140,22141,22142,22143,22144,22145,22146,22147,22148,22149,22150,22151,22152,22153,22154,22155,22156,22157,22158,22159,22160,22161,22162,22163,22164,22165,22166,22167,22168,22169,22170,22171,22172,22173,22174,22175,22176,22177,22178,22179,22180,22181,22182,22183,22184,22185,22186,22187,22188,22189,22190,22191,22192,22193,22194,22195,22196,22197,22198,22199,22200,22201,22202,22203,22204,22205,22206,22207,22208,22209,22210,22211,22212,22213,22214,22215,22216,22217,22218,22219,22220,22221,22222,22223,22224,22225,22226,22227,22228,22229,22230,22231,22232,22233,22234,22235,22236,22237,22238,22239,22240,22241,22242,22243,22244,22245,22246,22247,22248,22249,22250,22251,22252,22253,22254,22255,22256,22257,22258,22259,22260,22261,22262,22263,22264,22265,22266,22267,22268,22269,22270,22271,22272,22273,22274,22275,22276,22277,22278,22279,22280,22281,22282,22283,22284,22285,22286,22287,22288,22289,22290,22291,22292,22293,22294,22295,22296,22297,22298,22299,22300,22301,22302,22303,22304,22305,22306,22307,22308,22309,22310,22311,22312,22313,22314,22315,22316,22317,22318,22319,22320,22321,22322,22323,22324,22325,22326,22327,22328,22329,22330,22331,22332,22333,22334,22335,22336,22337,22338,22339,22340,22341,22342,22343,22344,22345,22346,22347,22348,22349,22350,22351,22352,22353,22354,22355,22356,22357,22358,22359,22360,22361,22362,22363,22364,22365,22366,22367,22368,22369,22370,22371,22372,22373,22374,22375,22376,22377,22378,22379,22380,22381,22382,22383,22384,22385,22386,22387,22388,22389,22390,22391,22392,22393,22394,22395,22396,22397,22398,22399,22400,22401,22402,22403,22404,22405,22406,22407,22408,22409,22410,22411,22412,22413,22414,22415,22416,22417,22418,22419,22420,22421,22422,22423,22424,22425,22426,22427,22428,22429,22430,22431,22432,22433,22434,22435,22436,22437,22438,22439,22440,22441,22442,22443,22444,22445,22446,22447,22448,22449,22450,22451,22452,22453,22454,22455,22456,22457,22458,22459,22460,22461,22462,22463,22464,22465,22466,22467,22468,22469,22470,22471,22472,22473,22474,22475,22476,22477,22479,22480,22481,22482,22483,22489,22490,22491,22492,22493,22494,22495,22496,22497,22498,22499,22500,22501,22502,22503,22504,22505,22506,22507,22508,22509,22510,22511,22512,22513,22514,22515,22516,22517,22518,22519,22520,22521,22522,22523,22524,22525,22526,22527,22528,22529,22530,22531,22532,22533,22534,22535,22536,22537,22538,22539,22540,22541,22542,22543,22544,22545,22546,22547,22548,22549,22550,22551,22552,22553,22554,22555,22556,22557,22558,22559,22560,22561,22562,22563,22564,22565,22566,22567,22568,22569,22570,22571,22572,22573,22574,22575,22576,22577,22578,22579,22580,22581,22582,22583,22584,22585,22586,22587,22588,22589,22590,22591,22592,22593,22594,22595,22596,22597,22598,22599,22600,22601,22602,22603,22604,22605,22606,22607,22608,22609,22610,22611,22612,22613,22614,22615,22616,22617,22618,22619,22620,22621,22622,22623,22624,22625,22626,22627,22628,22629,22630,22631,22632,22633,22634,22635,22636,22637,22638,22639,22640,22641,22642,22643,22645,22646,22647,22648,22649,22650,22651,22652,22653,22654,22655,22656,22657,22658,22659,22660,22661,22662,22663,22664,22665,22666,22667,22668,22669,22670,22671,22672,22673,22674,22675,22676,22677,22678,22679,22680,22681,22682,22683,22684,22685,22686,22687,22688,22689,22690,22691,22692,22693,22694,22695,22696,22697,22698,22699,22700,22701,22702,22703,22704,22705,22706,22707,22708,22709,22710,22711,22712,22713,22714,22715,22716,22717,22718,22719,22720,22721,22722,22723,22724,22725,22726,22727,22728,22729,22730,22731,22732,22733,22734,22735,22736,22737,22738,22739,22740,22741,22742,22743,22744,22745,22746,22747,22748,22749,22750,22751,22752,22753,22754,22755,22756,22757,22758,22759,22760,22761,22762,22763,22764,22765,22766,22767,22768,22769,22770,22771,22772,22773,22774,22775,22776,22777,22778,22779,22780,22781,22782,22783,22784,22785,22786,22787,22788,22789,22790,22791,22792,22793,22794,22795,22796,22797,22798,22799,22800,22801,22802,22803,22804,22805,22806,22807,22808,22809,22810,22811,22812,22813,22814,22815,22816,22817,22818,22819,22820,22821,22822,22823,22824,22825,22826,22827,22828,22829,22830,22831,22832,22833,22834,22835,22836,22837,22838,22839,22840,22841,22842,22843,22844,22845,22846,22847,22848,22849,22850,22851,22852,22853,22854,22855,22856,22857,22858,22859,22860,22861,22862,22863,22864,22865,22866,22867,22868,22869,22870,22871,22872,22873,22874,22875,22876,22877,22878,22879,22880,22881,22882,22883,22884,22885,22886,22887,22888,22889,22890,22891,22892,22893,22894,22895,22896,22897,22898,22899,22900,22901,22902,22903,22904,22905,22906,22907,22908,22909,22910,22911,22912,22913,22914,22915,22916,22917,22918,22919,22920,22921,22922,22923,22924,22925,22926,22927,22928,22929,22930,22931,22932,22933,22934,22935,22936,22937,22938,22939,22940,22941,22942,22943,22944,22945,22946,22947,22948,22949,22950,22951,22952,22953,22954,22955,22956,22957,22958,22959,22960,22961,22962,22963,22964,22965,22966,22967,22968,22969,22970,22971,22972,22973,22974,22975,22976,22978,22979,22980,22981,22982,22983,22984,22985,22986,22987,22988,22989,22990,22991,22992,22993,22994,22995,22996,22997,22998,22999,23000,23001,23002,23003,23004,23005,23006,23007,23008,23009,23010,23011,23012,23013,23014,23015,23016,23017,23018,23019,23020,23021,23022,23023,23024,23025,23026,23027,23028,23029,23030,23031,23032,23033,23034,23035,23036,23037,23038,23039,23040,23041,23042,23043,23044,23045,23046,23047,23048,23049,23050,23051,23052,23053,23054,23055,23056,23057,23058,23059,23060,23061,23062,23063,23064,23065,23066,23067,23068,23069,23070,23071,23072,23073,23074,23075,23076,23077,23078,23079,23080,23081,23082,23083,23084,23085,23086,23087,23088,23089,23090,23091,23092,23093,23094,23095,23096,23097,23098,23099,23100,23101,23102,23103,23104,23105,23106,23107,23108,23109,23110,23111,23112,23113,23114,23115,23116,23117,23118,23119,23120,23121,23122,23123,23124,23125,23126,23127,23128,23129,23130,23131,23132,23133,23134,23135,23136,23137,23138,23139,23140,23141,23142,23143,23144,23145,23146,23147,23148,23149,23150,23151,23152,23153,23154,23155,23156,23157,23158,23159,23160,23161,23162,23163,23164,23165,23166,23167,23168,23169,23170,23171,23172,23173,23174,23175,23176,23177,23178,23179,23180,23181,23183,23184,23185,23186,23187,23188,23189,23190,23191,23193,23194,23195,23196,23197,23198,23199,23200,23201,23202,23203,23204,23205,23206,23207,23208,23209,23210,23211,23212,23213,23214,23215,23216,23217,23218,23219,23220,23221,23222,23223,23224,23225,23226,23227,23228,23229,23230,23231,23232,23234,23261,23262,23480,23508,23523,23524,23525,23526,24596,24648,24662)

  ////////// START: DEFAULT SENDING SERVER SETTINGS ///////// 

  // 29 March 2022: gmail is disallowing sending via the smtp mode
  // 23 Oct 2024: enable send via gmail smtp for new users.
  val DEFAULT_INITIAL_ORG_FORCE_SEND_VIA_GMAIL_SMTP = true
  // 5 - is srmailapp.com - listed in blacklist (https://spamrl.com/)
  // 6 - is srmails.com - test emails going to spam
  // 7 - srmailerapp.com
  // 8 - srmailone.com - very low open rates
  // 7 srmailerapp.com - listed in blacklist: https://app.intercom.com/a/apps/xmya8oga/inbox/inbox/all/conversations/84281100153900
  // 12 - srmailthree.com - add as default on 14th August 2021 - listed in blacklist - (removed as default on 22 Dec 21)
  // def random: Int = math.random.round.toInt // returns 0 OR 1

  /**
   * 24 Oct 202
   *
   * `rep_google_api_keys` table stores client ids,
   * which we can use to authenticate with Google using the OAuth 2.0 flow.
   *
   * Previously in the `organizations` table,
   * the `rep_google_api_key_id` column was set to DEFAULT 1 at the DB level.
   *
   * But as we are now enabling users to integrate
   * Google email accounts with OAuth 2.0 flow, we need to use rep_google_api_key with id 6.
   *
   * So, setting rep_google_api_key_id = 6 in the organizations table for new users.
   *
   * Note:
   * If `rep_google_api_keys` does not have the mentioned key id then account creation will fail.
   */
  val default_rep_google_api_key_id: Int = if (AppConfig.isProd) {

    6

  } else {

    // Cannot use rep_google_api_key with id 6 when env is not prod,
    // because by default in `rep_google_api_keys` we only have 1 key.

    1

  }

  ////////// END: DEFAULT SENDING SERVER SETTINGS /////////


  val emailImageBucketName = config.getString("emailImageBucketName")
  val csvBucketName = config.getString("csvBucketName")
  val csvBucketNameForLeadFinder = config.getString("csvBucketNameForLeadFinder")

  /*
  Note: 12-MAR-2025
  google cloud buckets are supposed to be globally unique
   so we were forced to use a different name for production.
   */
  val audioBucketName = if(AppConfig.isProd){
    config.getString("voicedropBucketNameProd")
  }else{
    config.getString("voicedropBucketNameStaging")
  }

  val milliSecondsInAnHour = 60 * 60 * 1000
  val stuckAutoTaskTimeLimitInMinutes = 60


  //Lead Finder Page Limits
  val perPageLimit = 200
  var perPageLimit411 = 100
  var perPageLimitAp = 100
  val maxTotalDisplay = 2000

  // GMail feedback loop senderId (for promotional senders)
  // REF: https://support.google.com/mail/answer/6254652?hl=en
  val gmailFBLSenderId = "sr"


  val masterAccountIds: Seq[Long] = Seq(42)

  val applicationEncryptionKey = config.getString("application.encryptionKey")
  val ensettingsEncryptionKey = config.getString("application.emailnotificationsettingsencryptionKey")


  val mailgunDomain = config.getString("mailgun.domain")
  val mailgunApiKey = config.getString("mailgun.apiKey")

  val adminEmail = config.getString("mailgun.adminEmail")
  val adminName = config.getString("mailgun.adminName")

  val emailMessageIdSuffix = config.getString("email.messageid.suffix")

  val googleOAuthSettings = GoogleOAuthSettings(
    authorizationURL = "https://accounts.google.com/o/oauth2/auth",
    accessTokenURL = "https://www.googleapis.com/oauth2/v4/token",
    redirectRoute = "/dashboard/account_settings/email_accounts",
    scope = "email profile https://mail.google.com/",
    //    scope = "email profile https://www.googleapis.com/auth/gmail.modify",
    redirectURL = dashboardDomain + "/dashboard/account_settings/email_accounts"
  )
  val googleOAuthSettingsForSignupFlow = googleOAuthSettings.copy(
    redirectURL = AppConfigCommonAuth.commonAuthFrontendUrl + "/auth/oauth-redirect/google",
    scope = "email profile"
  )
  val googleOAuthSettingsForNewKey = googleOAuthSettings.copy(
    scope = "email profile https://www.googleapis.com/auth/gmail.modify"
  )

  val zapmailRedirectUrl = apiDomain+"/api/v2/email-infra/zapmail/oauth/code"

  val googleAuthApiKeyIdForNewAuthFlow = config.getInt("google.googleAuthApiKeyIdForNewAuthFlow")
  val googleAuthApiKeyIdV2 = config.getInt("google.googleAuthApiKeyIdV2")


  /*
  * APPs:
    - [SmartReach.io] - for production
    - [SmartReach.io - Dev] - for staging and localhost
  * APP Dashboard https://app.hubspot.com/developer/6602102/applications
  * Developer account: hubspot-developers-ncrymu.com
  * Account Email: <EMAIL>
*/
  val hubspotOAuthSettings = IntegrationTPOAuthSettings(
    authorizationURL = "https://app.hubspot.com/oauth/authorize",
    accessTokenURL = "https://api.hubapi.com/oauth/v1/token",
    redirectRoute = "/dashboard/account_settings/integrations",
    clientID = config.getString("hubspot.clientID"),
    clientSecret = config.getString("hubspot.clientSecret"),
    scope = "crm.schemas.deals.read crm.objects.owners.read crm.objects.contacts.write crm.objects.companies.write crm.lists.read crm.schemas.contacts.read crm.objects.deals.write crm.objects.contacts.read crm.schemas.companies.read",
    redirectURL = dashboardDomain + "/dashboard/account_settings/integrations",
    allowdedFieldTypes = Seq("text", "number", "date", "textarea", "phonenumber"),
    accessTokenExpiresIn = 600 //hubspot access_token expires in 30 minutes (21600 sec) for safe side we are refreshing after 10 min (3000 sec)
  )

  val zohoOAuthSettings = IntegrationTPOAuthSettings(
    //https://www.zoho.com/crm/developer/docs/api/multi-dc.html api end point reference
    authorizationURL = "https://accounts.zoho.com/oauth/v2/auth",
    accessTokenURL = "https://accounts.zoho.com/oauth/v2/token",
    redirectRoute = "/dashboard/account_settings/integrations",
    clientID = config.getString("zoho.clientID"), //NOTE: clientID and clientSecret are same for both CRM and Recruit in ZOHO
    clientSecret = config.getString("zoho.clientSecret"), //NOTE: clientID and clientSecret are same for both CRM and Recruit in ZOHO
    scope = "ZohoCRM.users.READ ZohoCRM.modules.ALL ZohoCRM.settings.ALL",
    redirectURL = dashboardDomain + "/dashboard/account_settings/integrations",
    allowdedFieldTypes = Seq("text", "email", "textarea", "phone", "website", "integer", "bigint", "double", "picklist"),
    accessTokenExpiresIn = 3000 //zoho access_token expires in 1 hour (3600 sec) for safe side we'r refreshing after 50 min (3000 sec)
  )

  val zohoRecruitOAuthSettings = IntegrationTPOAuthSettings(
    //https://help.zoho.com/portal/en/kb/recruit/developer-guide api end point reference
    authorizationURL = "https://accounts.zoho.com/oauth/v2/auth",
    accessTokenURL = "https://accounts.zoho.com/oauth/v2/token",
    redirectRoute = "/dashboard/account_settings/integrations",
    clientID = config.getString("zoho.clientID"), //NOTE: clientID and clientSecret are same for both CRM and Recruit in ZOHO
    clientSecret = config.getString("zoho.clientSecret"), //NOTE: clientID and clientSecret are same for both CRM and Recruit in ZOHO
    scope = "ZohoRecruit.users.READ ZohoRecruit.modules.ALL ZohoRecruit.settings.ALL",
    redirectURL = dashboardDomain + "/dashboard/account_settings/integrations",
    allowdedFieldTypes = Seq("Text", "Email", "TextArea", "Phone", "Picklist"),
    accessTokenExpiresIn = 3000 //zoho recruit access_token expires in 1 hour (3600 sec) for safe side we'r refreshing after 50 min (3000 sec)
  )
  
  val calendlyOAuthSettings = CalendlyOAuthSettings(
      authorizationURL = "https://auth.calendly.com/oauth/authorize",
      accessTokenURL = "https://auth.calendly.com/oauth/token",
      clientID = config.getString("calendly.clientID"),
      clientSecret = config.getString("calendly.clientSecret"),
      redirectURL = dashboardDomain + "/dashboard/account_settings/integrations",
      webhookSigningKey = config.getString("calendly.webhookSigningKey")
      
  )

  object LinkedinLimits {
    val viewProfileMinLimit = config.getInt("multichannel_limits.linkedin.view_profile.min")
    val viewProfileMaxLimit = config.getInt("multichannel_limits.linkedin.view_profile.max")

    val inmailsMinLimit = config.getInt("multichannel_limits.linkedin.inmails.min")
    val inmailsMaxLimit = config.getInt("multichannel_limits.linkedin.inmails.max")

    val messagesMinLimit = config.getInt("multichannel_limits.linkedin.messages.min")
    val messagesMaxLimit = config.getInt("multichannel_limits.linkedin.messages.max")

    val connectionRequestsMinLimit = config.getInt("multichannel_limits.linkedin.connection_requests.min")
    val connectionRequestsMaxLimit = config.getInt("multichannel_limits.linkedin.connection_requests.max")
  }

  object WhatsappLimits {
    val minLimit = config.getInt("multichannel_limits.whatsapp.min")
    val maxLimit = config.getInt("multichannel_limits.whatsapp.max")
  }

  object SmsLimits {
    val minLimit = config.getInt("multichannel_limits.phone.min")
    val maxLimit = config.getInt("multichannel_limits.phone.max")
  }

  object CallLimits {
    //Will be used for validation
    val minLimit = config.getInt("multichannel_limits.call.min")
    val maxLimit = config.getInt("multichannel_limits.call.max")
  }

  object AcquisitionReportOptionalFieldLimits {
    val minOptionalFieldValue = 0
    val maxOptionalFieldValue = 100
    val maxUserAndNewUsersCountValue = 2000
  }

  object OpportunityConfidenceLimits {
    val minConfidenceFieldValue = 0
    val maxConfidenceFieldValue = 100
  }

  object Opportunities {

    val maxOpportunityStatusesPerPipeline = 15
    val maxPipelineLimitPerTeam = 10
    val minPipelineLimitPerTeam = 1
    val maxOpportunitiesFetchFromDBLimit = 10

    val minUniqueOpportunityStatusTypeCount = 1

    val nonActiveStatusTypeMaxLimit = 2

    val opportunityPosRankOffset = 2000

    val maxCharLimitPipelineName = 15

    val defaultPipelineName = "Pipeline"

    val firstActiveStatusName = "Qualified"
    val firstActiveStatusRank = 1000

    val secondActiveStatusName = "Meeting Booked"
    val secondActiveStatusRank = 2000

    val thirdActiveStatusName = "Demo Completed"
    val thirdActiveStatusRank = 3000

    val fourthActiveStatusName = "Contract Sent"
    val fourthActiveStatusRank = 4000

    val activeStatusColor = "#FCDF84"

    val wonStatusName = "Won"
    val wonStatusRank = 5000
    val wonStatusColor = "#BBF379"

    val lostStatusName = "Lost"
    val lostStatusRank = 6000
    val lostStatusColor = "#DEE1E5"

  }

  object OpportunityServiceErrors {

    val ConfidenceUpdatedInNonActiveStatusErrorMessage = "Cannot update confidence in non-active status"

    val OpportunityStatusNotFoundErrorMessage = "Opportunity status not found."

    val OpportunityNotFoundErrorMessage = "Opportunity not found."

    val OpportunityConfidenceLimitsErrorMessage =
      s"confidence value should be between ${AppConfig.OpportunityConfidenceLimits.minConfidenceFieldValue} and ${AppConfig.OpportunityConfidenceLimits.maxConfidenceFieldValue}"

    val NegativeOpportunityValueError = "Opportunity value cannot be negative"

  }

  object TooltipContents {
    val emails_delivered = "Number of emails sent to prospects"
    val calls_outbound = "Number of call tasks completed"
    val linkedin_touchpoints = "Number of linkedin tasks completed (Auto and manual combined)"
    val tasks_completed = "Number of MANUAL Tasks completed"
    val prospects_assigned = "Number of Prospects assigned in a Linkedin Campaign"
    val tasks_assigned = "Number of Linkedin Tasks Scheduled (Auto and Manual combined)"
    val connection_requests = "Number of Connection Request Tasks Completed (Auto and Manual combined)"
    val linkedin_messages = "Number of Linkedin Messages Sent (Auto and Manual combined)"
    val linkedin_inmails = "Number of Linkedin InMails Sent (Auto and Manual combined)"
    val linkedin_view_profiles = "Number of Linkedin Profiles Viewed (Auto and Manual combined)"
  }

  val pipedriveOAuthSettings = IntegrationTPOAuthSettings(
    authorizationURL = "https://oauth.pipedrive.com/oauth/authorize",
    accessTokenURL = "https://oauth.pipedrive.com/oauth/token",
    redirectRoute = "/dashboard/account_settings/integrations",
    clientID = config.getString("pipedrive.clientID"),
    clientSecret = config.getString("pipedrive.clientSecret"),
    scope = "base users:read contacts:full activities:full deals:full search:read",
    redirectURL = apiDomain + "/tp_integration/pipedrive/oauth",
    allowdedFieldTypes = Seq("text", "int", "double", "phone", "varchar", "date", "org"),
    accessTokenExpiresIn = 3000 //pipedrive access_token expires in 1 hour (3600 sec) for safe side we'r refreshing after 50 min (3000 sec)
  )


  def salesforceOAuthSettings(is_sandbox: Boolean = false) = if (!is_sandbox) IntegrationTPOAuthSettings( //api_version: 47
    authorizationURL = "https://login.salesforce.com/services/oauth2/authorize",
    accessTokenURL = "https://login.salesforce.com/services/oauth2/token",
    redirectRoute = "/dashboard/account_settings/integrations",
    clientID = config.getString("salesforce.clientID"),
    clientSecret = config.getString("salesforce.clientSecret"),
    scope = "full",
    redirectURL = dashboardDomain + "/dashboard/account_settings/integrations",
    allowdedFieldTypes = Seq("string", "email", "id", "datetime", "phone"),
    accessTokenExpiresIn = 600 //salesforce access_token expires in 15 minutes (900 sec) for safe side we'r refreshing after 10 min (600 sec)
  ) else IntegrationTPOAuthSettings( //api_version: 47
    authorizationURL = "https://test.salesforce.com/services/oauth2/authorize",
    accessTokenURL = "https://test.salesforce.com/services/oauth2/token",
    redirectRoute = "/dashboard/account_settings/integrations",
    clientID = config.getString("salesforce.clientID"),
    clientSecret = config.getString("salesforce.clientSecret"),
    scope = "full",
    redirectURL = dashboardDomain + "/dashboard/account_settings/integrations",
    allowdedFieldTypes = Seq("string", "email", "id", "datetime", "phone"),
    accessTokenExpiresIn = 600 //salesforce access_token expires in 15 minutes (900 sec) for safe side we'r refreshing after 10 min (600 sec)
  )

  val maxDaysToMarkProspectCompletedForDoneTaskBeforeAlert = 2

  val alertAtEmailSendingStuckCount: Int = 20
  val ttlForEmailsStuckCountRedisKeyInSeconds: Int = 1200 //20 minutes

  // cron
  // 08-May-2025 reduced from 5 -> 3 for more aggressive scheduling
  val cronPushToRabbitMqIntervalInSeconds = 3
  val cronPushToRabbitMqV2IntervalInSeconds = 5
  val cronOneMinutelyIntervalInSeconds = 60
  val cronFiveMinutelyIntervalInSeconds = 300

  // 12-Jan-23 reduced from 300 -> 30 for more aggressive scheduling
  val cronReadEmailIntervalInSeconds = 30


  // 12-Jan-23 reduced from 300 -> 30 for more aggressive scheduling
  // 27-Nov-23 increased from 30 -> 60 for less aggressive scheduling
  // 1-Dec-23 decreased from 60 -> 20 for less aggressive scheduling
  val cronScheduleEmailIntervalInSecondsV2 = 20


  val cronSpamTestIntervalInSeconds = 30
  val cronUploadCSVIntervalInSeconds = 10
  val cronUploadLeadFinderCsvIntervalInSeconds = 10;
  val cronUploadLeadFinderCsvLinkedinIntervalInSeconds = 10;
  val outlookMsgIdFetchAndUpdateCronServiceIntervalInSeconds = 300
  val emailSpamMonitoringCronIntervalInSeconds = 20
  val campaignForSpamCheckQueueCronIntervalInSeconds = 3600
  val internalLeadFinderDataUpdateCrontIntervalInSeconds = 30
  val cronDummy30SecondsIntervalInSeconds = 30
  val cronDailyCronServiceIntervalInSeconds = 12 * 60 * 60
  val cronTerminateBacklogDBConnectionCronServiceIntervalInSeconds = 60
  val cronHourlyCronServiceIntervalInSeconds = 60 * 60
  val cronOldEmailSyncForTeamInboxIntervalInSeconds = 10
  val cronCampaignSendReportCronServiceInSeconds = 15 * 60
  val cronFifteenMinutesInSeconds = 15 * 60

  val cronCustomerBlackListEmailDomainIntervalInSeconds = 15
  val cronEmailPreValidationIntervalInSeconds = 30 * 60
  val cronUpdateSubAccountCallHistoryCronIntervalInSeconds = 300
  val cronIndependentStepSchedulerCronIntervalInSeconds = 300

  val cronApproveAdmitadCommissionAfterInSeconds = 60 * 60 * 24
  val cronUpdateAcquisitionReportAfterInSeconds = 60 * 60 * 24
  val dayInSeconds = 60 * 60 * 24
  val twelveHoursInSeconds = 60 * 60 * 12

  val referralCouponValidityPeriodInDays = 45
  val cronDataIntegrityCheckAfterInSeconds = 5 * 60

  val cronScheduleMagicColGenerationAfterInSeconds = 30

  val cronScheduleMonitorDripCampaignsAfterInSeconds: Int = 60 * 15 // 15 minutes

  val resetBillingCycleIntervalInMonths = 1

  /*
   May 2020: earlier this was 300 seconds, but there was a rate-limit problem with
   users who were running multiple sync triggers parallely
   (e.g. sparity user has 8 different hubspot_sync triggers that cause the rate limit to be hit when they run parallely)

   so we reduced the interval to 120 seconds, are only triggering one sync of a user-crm-trigger at a time
   */
  val cronIntegrationTriggerSyncIntervalInSeconds = 120

  val cronAttemptRetryCronServiceIntervalInSeconds = 1

  //This is time till which the APIs will be not assigned till the emails reach threshold limit

  val timeToWaitInSeconds = 15 * 60


  val redisKeyPrefix = config.getString("redis.keyPrefix")
  lazy val redisUri = config.getString("redis.uri")

  val mailboxlayerApiKey = config.getString("mailboxlayerApiKey")

  val bouncerApiKey = config.getString("bouncerApiKey")
  val deBounceApiKey = config.getString("deBounceApiKey")
  val listCleanApiKey = config.getString("listCleanApiKey")

  val userCheckApiKey = config.getString("userCheckApiKey")

  object ThirdPartyApiKeys {

    val mxtoolboxKey = config.getString("thirdpartyApiKeys.mxtoolboxApiKey")

    val api_key_for_api_layer = config.getString("whois_apilayer.api_key")

    val api_key_for_whois_rapidAPI = config.getString("whois_rapidAPI.api_key")
    val api_host_for_whois_rapidAPI = config.getString("whois_rapidAPI.api_host")

  }

  object pusher {
    val appId = config.getString("pusher.appId")

    val key = config.getString("pusher.key")

    val secret = config.getString("pusher.secret")

    val cluster = config.getString("pusher.cluster")
    /**
     * added on 20-sept-2024
     * we need to show this client id in the frontend model so the the user can add this in the authorised apps
     * since we are not getting it authorised by google. Allowing the client id to be used to connect a google account through Oauth
     */
    val google_oauth_client_id = "************-co8al3h8afkq2h6hnrd4tve44reon5gn.apps.googleusercontent.com"
  }

  val mailtesterUsername = config.getString("mailtesterUsername")

  val intercomSec = config.getString("intercomSec")

  val intercomAccessToken = config.getString("intercom_api.access_token")

  val msg91AuthKey = config.getString("msg91authkey")

  val auth0ipblacklistcheckkey = config.getString("auth0ipblacklistcheckkey")

  val googleRecaptchaSecret = config.getString("google.recaptchaSecret")


  object EncKeys {

    // used for encrypting email settings credentials
    val emailSettingsCredentialEncKey = config.getString("enc_keys.email_settings_credential_enc_key")

    val emailDataplatformCredentialEncKey = config.getString("enc_keys.email_dataplatform_credential_enc_key")

    val workflowCRMSettingsCredentialEncKey = config.getString("enc_keys.workflow_crm_settings_credential_enc_key")
    val accountsCredentialEncKey = config.getString("enc_keys.accounts_credential_enc_key")
    val dkimRecordsCredentialEncKey = config.getString("enc_keys.dkim_records_credential_enc_key")
    val teamsAccountCredentialEncKey = config.getString("enc_keys.teams_accounts_credential_enc_key")
    val organizationsCredentialEncKey = config.getString("enc_keys.organizations_credential_enc_key")
    val phantomBusterApiEncKey = config.getString("enc_keys.phantom_buster_api_enc_key")
    val nativeCallingApiEncKey = config.getString("enc_keys.native_calling_api_enc_key")

    val phantomBusterWebhookTeamSecretEncKey = config.getString("enc_keys.phantom_buster_webhook_team_secret_enc_key")
    val linkedinSessionCookieEncKey = config.getString("enc_keys.linkedin_session_cookie_enc_key")

    val phantomBusterProxyPasswordEncKey = config.getString("enc_keys.phantombuster_proxy_password_enc_key")
    val linkedinAccountPasswordEncKey = config.getString("enc_keys.linkedin_account_password_enc_key")
    
    
    val calendlySettingCredentialEnckey = config.getString("enc_keys.calendly_settings_credential_enc_key")

  }

  object AuditTrail {

    val maxLockInSecondsForProcessingEventInQueue: Int = 20

    val defaultRatelimitWaitTimeInSeconds: Int = 60

  }

  val phantomBusterCSVBucketName = if (AppConfig.isProd) {
    "phantom-buster-prod-gcs-bucket"
  } else {
    "phantom-buster-staging-gcs-bucket"
  }
  val phantomBusterCsvTtlInMinutes = 10

  val deBounceCSVBucketName = config.getString("deBounceBucketName")

  val deBounceCsvTtlInMinutes = 10

  val gcsBaseUrl = "https://storage.googleapis.com/"

  /// WORKFLOW / TRIGGER RELATED /////

  //NOTE after 6 retries only error will be shown to user, to skip CRM's timeout  error and internal errors
  val triggerErrorRetriesCount = 6

  // Currency Exchange Rates as of 5 June 2025
  val usdInrExchangeRate = 85
  val eurInrExchangeRate = 95
  val gbpInrExchangeRate = 115
  val audInrExchangeRate = 55
  val plnInrExchangeRate = 22


  ////// EMAIL NOTIFICATIONS RELATED /////
  val productionDashboardDomain = "https://app.smartreach.io"

  val emailAccountSettingsUrl = "https://app.smartreach.io/dashboard/account_settings/email_accounts"

  val workflowsPageUrl = "https://app.smartreach.io/dashboard/account_settings/workflows"

  /*def billingPageUrl(isAgency: Boolean): String = if (isAgency) {
    s"$productionDashboardDomain/agency_dashboard/account_settings/billing"
  } else {
    s"$productionDashboardDomain/dashboard/account_settings/billing"
  }*/

  def billingPageUrl(isAgency: Boolean): String = {
    s"$productionDashboardDomain/billing_redirect"
  }

  // val kafkaServerUrl: String = config.getString("kafka.server_url")

  // PAYMENT RELATED //

  val stripeAPIKey: String = config.getString("stripe.apiKey")
  val stripePublishableKey:String = config.getString("stripe.publishableKey")

  object BrightData {
    val apiKey: String = config.getString("bright_data_apiKey")
    val apiUrl: String = "https://api.brightdata.com"
    val usernamePrefix: String = "brd-customer-hl_43344123-zone-" // Usernames of zones have this prefix for our Bright Data account
  }

  val srAiApiBaseUrl = "http://localhost:3000"


  // AFFILIATE RELATED

  // FirstPromoter

  val firstpromoterAPIKey: String = config.getString("firstpromoter.apiKey")
  val firstpromoterWID: String = config.getString("firstpromoter.wid")
  val firstpromoterReferralCampaignId = config.getString("firstpromoter.referralCampaignId")

  // REF: https://docs.firstpromoter.com/#tracking-leads-and-sign-ups
  val firstpromoterCookieName: String = "_fprom_track"
  val firstpromoterReferIdCookieName: String = "_fprom_code"


  val firstPromoterCreatedAtCookieName: String = "fp_created_at"

  // Admitad

  val admitadBaseUrl: String = "https://ad.admitad.com"

  val admitadPostbackKey: String = config.getString("admitad.postbackKey")
  val admitadCampaignCode: String = config.getString("admitad.campaignCode")
  val admitadRevisionSecretKey: String = config.getString("admitad.revisionSecretKey")

  val admitadCookieName: String = "admitad_uid"
  val admitadGclidCookieName: String = "gclid"

  val admitadCreatedAtCookieName: String = "admitad_created_at"


  val maxAllowedDurationForProcessingMagicColumnsInHours = 7
  val stuckMagicColumnsQueryLimit = 26


  // added on 13th June 2022
  val ignoreSmtpImapSameEmailCheckForOrgIds = Seq(
    4443, // Cleverep added on 18th July 2022
    2257, // leadmint
    6861, // mesho kiran
    2165, // andrew kitcher
    2015, // drakedigital
    6071, // zelos
    15054, // Audio Bridge added on 15th July 2024
  )

  object EmailReplyTracker {

    /**
     * 1-Aug-2024: we were marking an email as bad outbound sender even if we found just 1 such bounced email.
     *
     * This was affecting a lot of users, who had one-off such bounces from time to time.
     *
     * Instead now, we will mark such email settings as having been bounced only if atleast 3 such bounced emails were
     * found in last 10 minutes
     */
    val checkForBadOutboundSendErrorInLastXMinutes: Int = 10

    val minBouncedErrorThresholdForBadOutboundSenderError: Int = 3

  }

  // CLOUDFLARE RELATED
  val cloudflareAuthKey: String = config.getString("cloudflare.authKey")
  val cloudflareAuthEmail: String = config.getString("cloudflare.authEmail")

  val generalTaskQuotaPerDay = 50
  val generalTaskMinDelayInSeconds = 60

  // SCHEDULER OPTIONS
  val emailScheduleIntervalInMinutes = 15

  val linkedinScheduleTaskDelay = 60

  val theCompaniesApiKey: String = config.getString("theCompaniesApi.apiKey")

  object SchedulerConfig {
    val acquireLockIfLimitReachedForTimeInSeconds = 1 * 60 * 60 // 2-Mar-2024: reduced from 3 to 1 hour


    // schedule an email account after minimum of 4 minutes
    // 12-Jan-23 reduced from 20mins -> 2 mins for more aggressive scheduling
    // 25-Feb-23 increased from 2mins -> 4 mins to reduce logging
    // 27-Nov-23 increased from 4mins -> 8 mins to reduce db load
    // 27-Nov-23 23:53 increased from 8mins -> 12 mins to reduce db load
    // 9-Jan-24 12:02 AM decreased from 12 mins -> 2 mins so that sender rotation use case works for now, this is temporary
    //  val scheduleEmailAccountIntervalInMinutes = 2
    val doNotScheduleEmailAccountIfAlreadyScheduledEmailsInNextIntervalMinutes = 15

    val pushedToQueueForReplyTrackingIntervalInMinutes = 10 // gmail will block IP if we fetch imap more aggressively than 10 minutes


    // schedule the campaign once every these-many minutes
    // UPDATE 11 Jan 2023: earlier this was 30 minutes, we had to reduce it because some email campaigns were not running
    // because reply_tracking lag was becoming more than 15 mins and this next-scheduling lag was 30 mins
    // update 6-jan-2024: sender rotation getting affected, reducing to 1 from 15, 1 from 5
    // FIXME: this needs to be stored at campaign_channel_settings / campaign_email_settings level
    // 15-Apr-2024: we have added the `last_schedule_done_at` check in the scheduler queries which enforces a min 5-min interval
    //    between consecutive schedule-attempts for the same campaign, making the below limits (3-min / 5-min) irrelevant
    // val CAMPAIGN_NEXT_SCHEDULED_AT_DELAY_IN_MINUTES_IF_SOME_TASKS_FOUND_IN_CURRENT_RUN_OLD_19FEB2024 = 5
    // val CAMPAIGN_NEXT_SCHEDULED_AT_DELAY_IN_MINUTES_IF_NO_TASK_FOUND_IN_CURRENT_RUN_OLD_19FEB2024 = 3
    // 15-Apr-2024: putting a 5-min minimum interval
    val CAMPAIGN_NEXT_SCHEDULED_AT_MIN_DELAY_IN_MINUTES_COULD_NOT_LOCK = 5
    val CAMPAIGN_NEXT_SCHEDULED_AT_MIN_DELAY_IN_MINUTES = 15

    // update 26-mar-2024: after recent optimisations, this higher interval was no longer needed, in fact it was affecting
    // new trial customers - where scheduling was getting delayed
    // val CAMPAIGN_NEXT_SCHEDULED_AT_DELAY_IN_MINUTES_IF_SOME_TASKS_FOUND_IN_CURRENT_RUN = 20
    // val CAMPAIGN_NEXT_SCHEDULED_AT_DELAY_IN_MINUTES_IF_NO_TASK_FOUND_IN_CURRENT_RUN = 10

    def SKIP_EMAIL_SETTING_ID_FOR_SCHEDULER_INTEGRATION_TEST: List[Long] = {
      if (isDev5Domain) {
        List(463)
      } else {
        List()
      }
    }

    def SKIP_LINKEDIN_SETTING_UUID_FOR_SCHEDULER_INTEGRATION_TEST: List[String] = {

      if (isDev5Domain) {

        List("linkedin_account_2dRmknWoAft2jX8XxnSASuyz6Fa")

      } else {

        List()

      }

    }

  }

  object Prometheus {

    val pushGateway: String = config.getString("prometheus.push_gateway")

  }

  //// BILLING APP RELATED ////

  object BillingAppService {
    val apiUrl = config.getString("billing_app.api_url")
    val apiKey = config.getString("billing_app.api_key")

    /**
     * 16-feb-2024: disabling payment due banners for few orgs where we invoice manually
     */
    val disablePaymentDueBannerForOrg: Set[OrgId] = Set(
      OrgId(2573), // hypernova
    )

  }

  object CalendarAppService {
    val apiUrl = config.getString("calendar_app.api_url")
    val apiKey = config.getString("calendar_app.api_key")
  }

  //22-Jan-2024 SCYLLA_COMMENTED_OUT
  //  object ScyllaService {
  //    val keyspace = config.getString("scylla.keyspace")
  //    val nodeAndPorts = config.getString("scylla.nodeAndPorts")
  //    val userName = config.getString("scylla.username")
  //    val password = config.getString("scylla.password")
  //  }

  object Calling {

    val connected_call_time_threshold = 6 // Any call talked over 6 seconds will be marked as a contacted call.
    val call_history_logs_interval_hours = 3
    val call_history_logs_in_stuck_sub_accounts_interval = 24
    val call_logs_per_page = 25
    val release_phone_number_when_trial_expires_after_interval_days = 3
    val customer_label = "customer"
    // val noSubAccountFoundErrorMsg = "no subaccount found"
    val max_verify_caller_id_attempts = 3


  }

  object IndependentStepScheduler {
    val independent_step_scheduler_interval_seconds = 300
    val independent_step_scheduler_in_stuck_interval = 1
  }

  object TwilioService {
    val acctSid: String = config.getString("twilio.account_sid") // TWILIO ACCOUNT SID
    val applicationSid: String = config.getString("twilio.application_sid") // TWILIO TWIML APP SID
    val apiKey: String = config.getString("twilio.api_key") // API KEY
    val apiSecret: String = config.getString("twilio.api_secret") // API SECRET
    val authToken: String = config.getString("twilio.auth_token")
  }

  object ApiLayerCurrencyService {

    val apiKey: String = config.getString("api_layer_currency.sr_api_layer_currency_api_key")

  }

  object partitionCron {
    val intervalTime = 3600
    val retention_time = "3 months"
    val status_not_to_check = List("done", "archived")
  }

  //////////////Phising realted

  val ignorePhishContentCheckForOrgIds: Seq[Long] = Seq(
    1447, // Ensante
  )


  val WHITELISTED_DOMAINS = Seq(

    "na3.docusign.net",
    "docusign.net",

    "dropbox.com",
    "www.dropbox.com",

    // shayan's use case
    "www.docsend.com",
    "docsend.com",

    "linktr.ee",
    "go.pardot.com",

    // GOOGLE LINKS //

    "storage.googleapis.com",

    "drive.google.com",
    "www.drive.google.com",

    "docs.google.com",
    "www.docs.google.com",
    "plus.google.com",

    "google.com.au",
    "www.google.com.au",

    "www.google.de",
    "google.de",

    "www.google.co.uk",
    "google.co.uk",

    "www.google.com.mx",
    "google.com.mx",

    "google.com",
    "www.google.com",

    "goo.gl",

    "forms.gle",

    "twitter.com",

    "event.on24.com",


    "ln.sync.com",

    "box.com",
    "www.box.com",
    "app.box.com",

    "1drv.ms",

    "api.whatsapp.com",
    "www.api.whatsapp.com",

    "share.hsforms.com",

    "form.jotform.com",

    "forms.office.com",

    "m.me", // added on 24 June 2022

    "lnkd.in"
  )

  val SHORT_URL_DOMAINS = Seq(

    // REF: https://developers.googleblog.com/2018/03/transitioning-google-url-shortener.html
    // google has disabled creating short urls by random users
    // links to google maps were getting affected.
    // "goo.gl",

    "bit.ly",
    "bitly.li",
    "tiny.cc"
  )

  val orgIdsUsingTeamInbox: Set[Long] = Set(
    10024, 10160, 10162, 10199, 10201, 10209, 10444, 10469, 10530, 10721, 10725, 10777, 10817, 10870, 10902, 10929, 10958, 11109, 11443, 11546, 11710, 11915, 12355, 12378, 12814, 12953, 13152, 13278, 13371, 13463, 13991, 14221, 14284, 14373, 14414, 14685, 14735, 14764, 14964, 15141, 15312, 15357, 15476, 15756, 16052, 16250, 16419, 16423, 16470, 16553, 16570, 16774, 16799, 16856, 16922, 16967, 17160, 17201, 17382, 18300, 23, 289, 3790, 4443, 4540, 5773, 5896, 6458, 6488, 7054, 7083, 7353, 7593, 7949, 8003, 8084, 9272
  )

  def isWhiteListedDomain(domain: String): Boolean = {
    WHITELISTED_DOMAINS.contains(domain.trim.toLowerCase)
  }

  ////////////

  val inboxRowsPerPage = 50

  // show count only for last 30 days
  val inboxUnreadCountShowForDays = 30

  val showInboxUnreadCountForOrgIds = Seq(
    2215 // leadroll,
    , 23 // heaplabs
  )

  val stepDelayForAISequenceGenerator = 3 * 24 * 60 * 60
  val maxAllowedStepsForAISequenceGenerator = 10
  val maxRetryAIGenerationAttempts = 2

  val GPTBaseUrl = "https://api.openai.com"
  val GPTApiKey: String = "********************************************************************************************************************************************************************"

  val GeminiBaseUrl = "https://generativelanguage.googleapis.com/v1beta/openai"
  val GeminiApiKey: String = "AIzaSyD-4K835RMS_42muBaCVXleAdtAUgxjsEY"

  def reportEntityColors(label: String)(implicit logger: ISRLogger): String = {
    val colorMap = Map(
      "Prospects contacted" -> "#0F69FA",
      "Prospects added" -> "#0F69FA",
      "Total calls" -> "#4C8FFD",
      "Calls connected" -> "#B8DCFF",
      "Calls dialed" -> "#8E68FF",
      "Total call duration" -> "#4C8FFD",
      "Avg. call duration" -> "#B8DCFF",
      "Email" -> "#8E68FF",
      "Call" -> "#FDCA40",
      "Linkedin" -> "#26ADF0",
      "Whatsapp" -> "#7ED321",
      "SMS" -> "#FA898B",
      "General" -> "#64646E",
      "Positive" -> "#62C38A",
      "Referral" -> "#4C8FFD",
      "Objection" -> "#EFD65B",
      "Not classified" -> "#EFD65B",
      "Do Not Contact" -> "#EF726C",
      "Negative" -> "#EF726C",
      "Uncategorized" -> "#64646E",
      "Assigned" -> "#F98080",
      "In queue" -> "#E4C0FD",
      "Completed" -> "#007DFF",
      "Independent" -> "#000000",
      "Other" -> "#64646E",
      "Follow Up Needed" -> "#000000" // Temporary adding this default color
    )

    colorMap.get(label) match {
      case Some(value) => value
      case None =>
        logger.shouldNeverHappen(s"Received label: $label not present in colorMap")
        "#000000" // default color is black.
    }
  }

  val phantomBusterBaseUrl = "https://api.phantombuster.com/api/v2"

  ///////
  ////////// feature flags: start //////////

  val disableListUnsubscribeHeaderForOrgIds: Seq[Long] = Seq[Long](
    // 4649 // imaginate, 2nd Nov 2021
  )

  ////////// feature flags: end /////////////

  // added on 25 April 2022
  val orgIdsForPushToRabbitMqV2Flow = Seq(
    2215, // leadroll
    //    4443, // Cleverep
    //    3448, // terraboost
    //    6180, // transcloud
    //    7162, // bob@leadroad
    2257, // leadmint
    23, // heaplabs
    19 // Dev2 Shashank Testing
  )

  /// IP WHITELIST ///
  val employAStarIpWhitelist = Seq(
    // "************"
  )


  //// SUSPECTED BOT CLICKS /////
  val ignoreClicksFromSusptectedBotForOrgIds = Seq(
    2714, // geniusmktg : added on 16th April 2021
    23 // heaplabs
  )

  val showSendPlainTextOrgIds: Seq[Long] = Seq(
    23,
    // Ashutosh gave these org_ids to enable plain text emails
    // 20-mar-2025
    19639,
    19836,
    19339,
    18453,
    19591,
    14414,
    19944,
    19836,
    16421,

    // Ashutosh gave these 2 new org_ids to enable plain text emails
    // 8-april-2025

    20197,
    19174
  )

  val showCampaignTagsForOrgIds: Seq[Long] = Seq(
    23, // heaplabs,
    3805, // medicalleverage
    5708, // rc mowers https://app.intercom.com/a/apps/xmya8oga/inbox/inbox/conversation/84281100180524
    2 // dev test company
  )

  // added on 10th Nov 21
  val restrictInboxTo45DaysOfRepliesForOrgIds: Seq[Long] = Seq(
    2573, // hypernovamarketing
    3448, // terraboost
  )

  val allowedEmailAddressForNewGoogleAPIKeys: Seq[String] = Seq(

    "<EMAIL>",
    "<EMAIL>",

    // 24-Feb-2023: for va-pt testing
    // Remove after vapt done
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",

  )

  val age_in_days_for_spam_monitor = 15

  val inboxv3_org_ids: Set[Long] = Set(23, 7083,
    5896, 174, 7926, 7353, 4840, 8053, 7678, 8135, 7756, 7583, 6412, 7367, 8568, 7949, 7068, 8178, 7241, 8306,
    8991, // swastik @ smartreach
    7830, 7051, 8137, 8307, 7875, 7536, 8084, 6325, 7975)

  val enable_lead_finder_org_ids: Set[Long] = Set(23)
  /**
   * 30-Mar-2024
   *
   * We check all the the org_ids listed below,
   * none of them have any multichannel campaign running.
   *
   * So, we are commenting out this list (multiChannel_org_ids)
   */
  //  val multiChannel_org_ids: Set[Long] = Set(
  //    10084, 8484, 10434, 9942, 3090, 3615, 7559, 7054, 3676, 5715, 6034, 4959
  //    , 10525, 10444
  //  )


  // Updated on 15th Feb 2023
  val accountIdsRangeForOldCNAMEFlow: Seq[Long] = Seq(
    2250, 2517, 2680, 2843, 2944, 3084, 4312, 4538, 5160, 5165, 5210, 5260, 6112, 6855, 6913, 7053, 7569, 7606, 8021, 8098, 8431, 8442, 8474, 8526, 8803, 8909, 9129
  )


  val org_for_campaign_bounce_rate_cron_seq: Set[Long] = Set(
    23, //SmartReach
    7805, // Scale-UP Investments
    5737 //<EMAIL>
  )

  val olduicheck_org_ids_to_eliminate_the_log: Set[Long] = Set(
    2215,
    8224,
    3615,
    4118,
    1617,
    5356,
    5568,
    6829,
    8211,
    7359,
    9078,
    9081,
    9122,


  )
  val quickstartEnabledForOrgIdGreaterThan = 12981

  val consecutiveProspectsContactedLimitCheckIntervalInMinutes = 15

  val encryptionJsFilePath = if (isProd) {
    if (isTest) {
      "/home/<USER>/smartreach/coldemail/src/main/resources/puppeteer/encrypt.js"
    } else {
      "/home/<USER>/srbuild/resources/puppeteer/encrypt.js"
    }
  }
  else if (isDevDomain) {
    "/home/<USER>/smartreach/coldemail/src/main/resources/puppeteer/encrypt.js"
  }
  else {
    "/home/<USER>/smartreach/coldemail/src/main/resources/puppeteer/encrypt.js"
  }

  object CampaignStatsCacheExpireTimeInSeconds {
    val newCampaigns = 15 * 60
    val runningCampaign = 60 * 60
    val notRunningCampaign = 6 * 60 * 60
  }

  val encryptedTextStartLog = "encrypted_text__"


  val getChildStepsNodeFileName = "get-child-steps.js"
  val getLastStepsNodeFileName = "get-last-steps.js"
  val getHeadStepsNodeFileName = "get-head-steps.js"
  val getDuplicateCampaignFileName = "get-duplicate-campaign-data.js"

  object Linkedin_Automation {
    val maxLinkedinThreadsToScrapeByCron = 20
    val messagesToScrapePerThread = 50
    val isRunningSinceLimitInMinutes = 60
    val executionTimeLimitToAssignTaskInMillis = 50000

    val minCooldownTimeAfterTaskExecutionInMinutes = 65 // PhantomBuster recommends Auto Connect to launch once per hour.
    val maxCooldownTimeAfterTaskExecutionInMinutes = 120

    val minCooldownTimeAfterScrapingInMinutes = 5
    val maxCooldownTimeAfterScrapingInMinutes = 20

    val stuckTaskTimeLimitInMinutes = 60

    val scrapeInboxAfterMinIntervalInMinutes = 2 * 60
    val scrapeInboxAfterMaxIntervalInMinutes = 3 * 60

    val maxLinkedinAccountsUsingAProxy = 5

    val useProxyForTeams = List(6412)

    val createLinkedinSessionNodeFileName = s"create-linkedin-session.js"

    val nodePath = if (isProd) {
      "/home/<USER>/.nvm/versions/node/v16.15.1/bin/node"
    }
    else if (isDevDomain) {
      "/home/<USER>/.nvm/versions/node/v18.17.0/bin/node"
    }
    else {
      "node"
    }

    val afterLoginScreenshotsBucket = if (isProd) {
      "linkedin-after-login-screenshots-prod"
    }
    else {
      "linkedin-after-login-screenshots-stag"
    }

    val maxLinkedinSessionCreateRetriesPerDay = 3

    val minDelayBetweenSessionCreateAttempsInMinutes = 8

    val whiteListedDomainsForLinkedinAutomation = List(
      "www.linkedin.com", "ponf.linkedin.com", "linkedin.com", "static.licdn.com", "fonts.gstatic.com"
    )
  }


  val org_for_campaign_bounce_rate_cron_greater_than: Long = 8776

  val bad_outbound_sender_help_doc_link: String = "https://help.smartreach.io/docs/office365-resolution-for-bad-outbound-senders"

  val ttl_time_in_seconds_for_cache = 300

  // 18-mar-2024: billing webhooks were timing out because of resetCache on large accounts with lots of api keys
  val customExecutionContextThreadsForResetCacheUtil: Int = 50

  val limit_for_old_email_sync: Int = 50000

  val outlook_userPrincipalName_allow_team_id = 10057 //team: qovetia user: sophie

  val internal_sr_usage_data_for_orgs_created_within: Int = 60

  object AllCampaignsPage {
    val rowsPerPage = 10
  }

  val attemptRetryCount = 4

  val temp_thread_id_to_associate_prospect = -5

  val days_limit_to_fetch_inbox_threads = 15

  val campaign_inbox_days_limit_for_emails = 365

  val enable_inboxv3_for_org_id_older_than_prod = 9990
  val enable_inboxv3_for_org_id_older_than_staging = 60


  val db_utils_autocommit_deadlock_retry_thread_sleep_in_millis = 50

  object EmailHealthCheck {

    // Maybe subject should also contain some unique value like Pipl.ai ??
    val emailHealthCheckSubject = "SmartReach.io SPF DKIM DMARC Check"
    val emailHealthCheckTextBody = "This is to check SPF DKIM DMARC"

    val emailHealthCheckDelay = 14 // seconds

    val receiverEmailSettingId_prod: Long = 38808 // <EMAIL>

    // Dev5 - coldemail_dev
    val receiverEmailSettingId_staging: Long = 523 // <EMAIL>

  }

  object MagicColumns {

    def getMagicColMergeTagUsedInAnotherPromptErr(usedMagicColNames: List[String]): String = {

      s"Cannot reference magic column in another magic column prompt - ${usedMagicColNames.mkString("; ")}."

    }

  }

  object CalendarApp {
    val baseUrl = if (isProd) {
      "https://meetings.smartreach.io"
    } else if (isDevDomain) {
      "https://meetings-dev.sreml.com"
    } else {
      "http://localhost:3000"
    }
    val redirectUrl = baseUrl + "/event-types"

    val calendarEncKey = config.getString("calendar_app.enc_key") //FixMe: pass this in application conf


  }

  object CommonAuth {

    val clientId = config.getString("smartreach_auth.client_id")
    val clientSecret = config.getString("smartreach_auth.client_secret")

    val loginSessionTtlInMinutes = 8 // This 8 minutes (ttl) is of smartreach and not  the hydra's login session ttl

    val logoutRedirectUri = s"${config.getString("smartreach_auth.common_auth_server_url")}/oauth2/sessions/logout"

  }

  val getSecondsForDay = 24 * 60 * 60L
  val getSecondsForWeek = 7 * getSecondsForDay
  val getSecondsForMonth = 30 * getSecondsForDay
  val getSecondsForYear = 365 * getSecondsForDay

  val inbox_folder_type_empty_debug_team_ids: Set[Long] = Set(8229, 9267, 9545, 11119, 11393, 11683)


  val srApiBaseUrl: String = config.getString("sr_api_base_url")

  val whApiBaseUrl: String = "http://localhost:9002"


  val smartreachClientNameForRedis = "smartreach" // This is used for the cacheKey which stores the access token , refresh token etc
  val secondsRemovedFromExpires_In = 300 // This is the time which is removed from the expires_in(in seconds) field of the data related to access token ,
  // we do this in order to avoid accessTokenExpiry


  val ttlInSecondsForAccessTokenRedisKey = 3600

  object ApiUrls {
    val action_webhook_api = s"$srApiBaseUrl/api/v2/voice/twilio/call_action"
    val voice_webhook_api = s"$srApiBaseUrl/api/v2/voice/twilio/create"
  }


  val localCsvFilePath: String = "file:///Users/<USER>/Downloads/" //Update according to your local file path where csv file is saved

  // val localCsvParserExecutablePath: String = s"/Users/<USER>/Documents/Projects/csv-parser-flex-bison/build/csv2_macos.exe"
  val localCsvParserExecutablePath: String = s"/Users/<USER>/Documents/projects/coldemail/ColdEmail/src/main/resources/sr_csv_parser/csv2_macos.exe"

  val prospectListingApiRowsPerPage = 100

  val allowedEmailAndUsernameNonMatchingDomains_domain_in_username = Set("icloud.com").map(_.trim.toLowerCase)

  val allowedEmailAndUsernameNonMatchingDomains_domain_in_host = Set("mailhop.org").map(_.trim.toLowerCase)


  object CampaignSchedulingMetadataConstants {
    val refresh_after_hours = 12
    val push_again_after_hours = 1
    val push_again_after_update_in_campaign_in_mins = 5
  }

  val blacklistListingRowsPerPage = 100

  val teamsListingRowsPerPage = 10

  val emailSettingRowsPerPage = 100

  val leadFinderBillingLogsPerPage = 100

  val usersListingRowsPerPage = 100

  val prospectEventsListingRowsPerPage = 100

  val heaplabsTeamId: Long = 24

  // the value will be populated from the db at test run time
  var lead_finder_local_test_email_optional_org: scala.collection.mutable.Set[Long] = scala.collection.mutable.Set()

  val orgs_for_new_URI_change_prod: Set[Long] = Set(
    7949L,
    21192,
    23,
  )

  val orgs_for_new_URI_change_staging: Set[Long] = Set(
    59L
  )

  val maxEmailsInExclusionList: Int = 100

  val prospectStatusChangeProspectsLimit = 500

  val assignProspectLimit = 100

  val maxUsersInTeam = 100

  val notesPaginationPageLimit = 100

  object CypressTestingEmailAccount {
    val prodEmailAccount = "<EMAIL>"
    val stagingEmailAccount: Set[String] = Set("<EMAIL>", "<EMAIL>")
  }

  object NewUserDripCampaign {
    val randomDripCampaigns: Map[String, Int] = Map(
      "Ayush drip campaign" -> 133579,
      "Ashutosh drip campaign" -> 133582,
      "chinmayee drip campaign" -> 138394,
      "shreyas drip campaign" -> 138539
    );
  }
  
  val latestOrgIdToUseNewCategoryies: Long = 10408L //update later

  /**
   * 16-feb-2024:
   *
   * going forward all orgId / teamId / accountId based rolling update flags should be put
   * inside this RollingUpdates object
   */
  object RollingUpdates {

    def isNewCsvFlowWithSrProspectColumnsSelected(teamId: TeamId) = {
      teamId == TeamId(19436)
    }

    def showSendPlainText(orgId: Long): Boolean = {
      if (isProd) {
        showSendPlainTextOrgIds.contains(orgId)
      } else orgId != 1

    }

    // 16743 -> trial account [ <EMAIL> ]
    val prod_sendOutlookEmailViaBase64_orgIds = List(OrgId(id = 16743))
    val dev_sendOutlookEmailViaBase64_orgIds = List(OrgId(id = 20))

    def getOrgIdsForBase64MailAsFirstMail: List[OrgId] = {
      if (isProd) {
        prod_sendOutlookEmailViaBase64_orgIds
      } else dev_sendOutlookEmailViaBase64_orgIds
    }


    def showCampaignTags(orgId: Long): Boolean = {
      if (isProd) {
        showCampaignTagsForOrgIds.contains(orgId)
      } else orgId != 1
    }


    /** NOTE: 15-Apr-2024
     * we are now incrementing both last_contacted in prospects table and prospects_metadata table
     * and also updating last_prospects_touched_count_updated_at in prospects_metadata table when
     * incrementing the prospects_touched count for the org.
     *
     * Going forward we do not need to check last_contacted_at at all for the billing increment check
     *
     * this param / related logic needs to be removed in a months time from now, i.e. 15 May 2024
     */
    val prospectsTouchedCountFromMetadataOnlyIfBillingCycleStartedAfter: DateTime = DateTimeFormat
      .forPattern("dd-MMM-yyyy")
      .parseDateTime("13-Apr-2024")


    /**
     * Added on 20th feb for the new campaign sending report that we want for less sending
     * This is so that we know if a campaign is not sending properly and if it has not sent 80% of the emails that are supposed to go
     */
    val org_id_for_new_report: List[Long] = List(23, 2573, 12147)

    /**
     * added on 9-March-2024
     * the orgs for which we will push the changes last
     */

    val orgIdsToTakeLeastRiskOnWhileReleasingUpdates: Set[OrgId] = Set(
      OrgId(4443), // cleverep
      OrgId(2573) // hypernova
    )

    val teamIdsFromLeastRiskOrgsForESPMatching: List[TeamId] = List(
      TeamId(8384), // hypernova, wayleadr, added on 23-Jul-2024
      TeamId(17119), TeamId(12840), TeamId(16846), TeamId(16349), TeamId(16719), TeamId(15896), //added on 31-Jul-2024
    )

    /**
     * 11-jul-2024
     * adding below to release campaign_inbox to all orgs except least risk orgs
     *
     */

    val orgIdsToTakeLeastRiskOnWhileReleasingCampaignInbox: Set[OrgId] = Set(
      // OrgId(4443), // cleverep // enabled on 17 Jul 2024
      // OrgId(2573), // hypernova // enabled on 14 Dec 2024
      OrgId(289) // e2e
    )


    val orgIdsForIncomingCalls: List[Long] = List(23)


    val orgIdsForNativeCalling: List[Long] = List(
      23, //our org
    )

    val orgIdsForCallerIdVerification: List[Long] = List(
      23, //our org
    )

    def isOrgIdForCallerIdVerification(orgId: Long): Option[Boolean] = {
      if (orgIdsForCallerIdVerification.contains(orgId)) {
        Some(true)
      } else {
        Some(false)
      }
    }

    def getReceiverEmailSettingIdForInboxPlacementCheck(): Long = {
      if (AppConfig.isProd) {
        RandomNumberHelper.getRandomElementFromSet(AppConfig.InboxPlacementChecks.prodReceiverEmailSettingIds)
      } else {
        RandomNumberHelper.getRandomElementFromSet(AppConfig.InboxPlacementChecks.stagingReceiverEmailSettingIds)
      }
    }

    def isReceiverEmailSettingIdForInboxPlacementCheck(eset: Long): Boolean = {
      if (AppConfig.isProd) {
        AppConfig.InboxPlacementChecks.prodReceiverEmailSettingIds.contains(eset)
      } else {
        AppConfig.InboxPlacementChecks.stagingReceiverEmailSettingIds.contains(eset)
      }
    }

    def getReceiverEmailSettingIdForEmailHealthCheck: Long = {
      if (AppConfig.isProd) {

        EmailHealthCheck.receiverEmailSettingId_prod

      } else {

        EmailHealthCheck.receiverEmailSettingId_staging

      }
    }

    def isReceiverEmailSettingIdForEmailHealthCheck(eset: Long): Boolean = {
      if (AppConfig.isProd) {

        eset == EmailHealthCheck.receiverEmailSettingId_prod

      } else {

        eset == EmailHealthCheck.receiverEmailSettingId_staging

      }
    }

    var allowedOrgIdsForEmailValidationForLeadFinder_local: Set[Long] = Set()

    def enableEmailValidationForLeadFinder(orgId: OrgId): Boolean = {
      if (AppConfig.isProd) {
        val allowedOrgIds: Set[Long] = Set(15737, 15806, 23)
        allowedOrgIds.contains(orgId.id)
      } else {
        allowedOrgIdsForEmailValidationForLeadFinder_local.contains(orgId.id)
      }
    }

    val org_id_for_user_level_key_prod = 14679L

    val org_id_new_users_to_show_drip_campaign = 18228L

    val org_id_for_user_level_key_local = 1L

    /**
     * 4-june-2024: added to show simpler perms to new users
     *
     */
    val org_id_for_june_2024_simpler_perms_new_signups: Long = 15042L

    val allowManualTaskCreationFromApp: Seq[Long] = Seq(7949, 23)

    /**
     * Added on 9 Jul 2024
     * adding increased delay for emails for Orgs made after this org id
     */
    val org_id_for_increase_email_delay_prod: OrgId = OrgId(15550)
    val org_id_for_increase_email_delay_dev: OrgId = OrgId(1L)

    val org_ids_for_show_inbp_logs_report_prod: Set[OrgId] = Set(OrgId(23), OrgId(13125))
    val org_ids_for_show_inbp_logs_report_dev: Set[OrgId] = Set(OrgId(1), OrgId(129))

    def getShowInbpLogsReport(orgId: OrgId): Boolean = {
      if (isProd) {
        org_ids_for_show_inbp_logs_report_prod.contains(orgId)
      } else {
        org_ids_for_show_inbp_logs_report_dev.contains(orgId)
      }
    }

    def getInbpFEReportEnabledOrgs(): Set[OrgId] = {
      if (isProd) {
        org_ids_for_show_inbp_logs_report_prod
      } else {
        org_ids_for_show_inbp_logs_report_dev
      }
    }

    def getInboxPlacementEnabledOrgsProd(
      testOrgIds: Set[OrgId] = Set(), // will only come during integration testing
    ): Set[OrgId] = {
      
      if (isTest) {

        testOrgIds

      } else if (isProd) {

        InboxPlacementChecks.inboxPlacementCheckProdOrgs.map(OrgId(_))

      } else {

        InboxPlacementChecks.inboxPlacementCheckStagingOrgs.map(OrgId(_))
      }
    }

    val markCampaignCompletedForSoftBounceOrgsProd: Set[OrgId] = Set(OrgId(23), OrgId(4443),
      OrgId(1648), //SalezGen
      OrgId(13991), //Analytik Jena
      OrgId(16859), //Healthcare REA
    )

    def canMarkCampaignCompletedForSoftBounce(orgId: OrgId): Boolean = {
      if (isProd) {
        markCampaignCompletedForSoftBounceOrgsProd.contains(orgId)
      } else true
    }

    /**
     * Added on 25 Sept 2024
     * we will remove width="1" height="1" style="display: block;" alt="" for selected orgs
     */
    val orgIds_for_getOpenTrackingImgHtml_change: List[OrgId] = List(
      OrgId(23)
    )
  }

  object PublicDNS {
    val number_of_months_after_which_we_update_DNS: Int = 6
  }

  object LeadFinderCharges {
    // NOTE: Values are in cents
    val email = 6
    val linkedin_url = 20
    val phone_number = 30
    val lead_finder_default_trail_credits = 200
    val singleMagicColGeneration = 5
    val singleAiContentGeneration = 1

    // 1 usd = 200 credits is required.
    val adjustSmartReachCreditsFactor = 2
  }

  val prospectAccountRowsPerPage = 100

  val newProspectCategoryRankOffset = 1000

  object InboxPlacementChecks {

    val inboxPlacementCheckProdOrgs: Set[Long] = Set(2573)
    val inboxPlacementCheckStagingOrgs: Set[Long] = Set(129, 1)

    val prodReceiverEmailSettingIds: Set[Long] = Set(26718, 26717, 34569, 31133, 34573, 31135, 34574, 34571,
      34572, 30742, 34575, 31130, 30741, 26726, 31134, 31131, 34424) //from org-23
    val stagingReceiverEmailSettingIds: Set[Long] = Set(165L)

    val lastPushedToQueueForPlacementCheckDurationDays: Int = 1
  }

  val orgToSurpassSomeChecksInGetAccForReport: Long = 4443L

  def checkIfClvrpOrgForReports(orgId: OrgId) = {
    orgId.id == orgToSurpassSomeChecksInGetAccForReport
  }

  object EmailDelay {
    object Old {
      val min_delay = 10
    }

    object New {
      val min_delay = 90
    }
  }

  val criticalSpamBlacklist: List[String] = List(
    "ivmURI",
    "SORBS SPAM",
    "SORBS SMTP",
    "Nordspam DBL",
    "Spamhaus DBL",
    "0SPAM",
    "0SPAM RBL",
    "IMP SPAM",
    "MSRBL Spam",
    "Nordspam BL",
    "NIXSPAM",
    "RATS Spam",
    "SPAMCOP",
    "BARRACUDA",  //18-Apr-2025 : added this blacklist
    // 11-Sept-2024 : commented these two blacklists temporarily
    //                                                 "Spamhaus ZEN",
    //                                                 "UCEPROTECTL3"
  )

  val newSignupReportExcludedDomains: List[String] = List(
    "smartreach.io",
    "heaplabs.com",
    "warmuplabs.com"
  )

  val daysAfterMergedDuplicateProspectsGetDeleted: Int = 30

  object CRMSettings {
    //even though we are locking for an hour the create takes from a few seconds to around a minute,
    // so we need to check if the create has gone through or not every minute
    val delayForContactCreationDelayInCRMInSeconds = 60
    val CreateInCRMJedisDAO_expire_time = 60 * 60
  }

  val delay_to_consider_an_email_auto_reply = 30

  val rep_mail_server_cache_data_time_in_sec = 6 * 60 * 60

  object Debug_Logs_For_Teams {
    val listOfTeams = List(16247L)

    def allowLogging(teamId: Long): Boolean = listOfTeams.contains(teamId)
  }

  object CaptainData {
    val API_BASE_URL = "https://api.captaindata.co/v3"
    val API_KEY: String = config.getString("captain_data.api_key")
    val PROJECT_ID: String = config.getString("captain_data.project_id")
    val taskPerPickupPerAccount = 10

    object ViewLinkedinProfile {
      val WORKFLOW_UID = config.getString("captain_data.view_linkedin_profile.workflow_uid")
      val STEP_UID = config.getString("captain_data.view_linkedin_profile.step_uid")
    }

    object SendLinkedinConnectionRequest {
      val WORKFLOW_UID = config.getString("captain_data.send_linkedin_connection_request.workflow_uid")
      val STEP_UID = config.getString("captain_data.send_linkedin_connection_request.step_uid")
    }
    
    object ExtractLinkedinConnections {
      val WORKFLOW_UID = config.getString("captain_data.extract_linkedin_connections.workflow_uid")
      val STEP_UID = config.getString("captain_data.extract_linkedin_connections.step_uid")
    }

    object SendLinkedinMessage {
      val WORKFLOW_UID = config.getString("captain_data.send_linkedin_message.workflow_uid")
      val STEP_UID = config.getString("captain_data.send_linkedin_message.step_uid")
    }

    object RetrieveLinkedinMessage {
      val WORKFLOW_UID = config.getString("captain_data.retrieve_linkedin_message.workflow_uid")
      val STEP_UID = config.getString("captain_data.retrieve_linkedin_message.step_uid")
    }

    object RetrieveLinkedinConversations {
      val WORKFLOW_UID = config.getString("captain_data.retrieve_linkedin_conversations.workflow_uid")
      val STEP_UID = config.getString("captain_data.retrieve_linkedin_conversations.step_uid")
      val repetitiveIntervalInMinutes = 60
      val extractionIntervalInMinutes = 65
    }
  }

  val email_setting_limit_in_campaign_for_plan_1 = 25
  val email_setting_limit_in_campaign_for_plan_2 = 50
  val email_setting_limit_in_campaign_for_org: List[OrgId] = List(
    OrgId(19836)
  )

  /**
    * 10 Apr 2025
    *
    * Organizations using same api key in multiple WH accounts
    * are not allowed to interact with WH from SR.
    */
  val orgsUsingSameApiKeyInMultipleWHAccounts: Set[Long] = Set(
    10172,
    10434,
    12040,
    14586,
    15437,
    16272,
    16407,
    16501,
    3448,
    4443,
    5385,
    6829,
    7593,
    7783,
    8796,
    9750,
  )

  val orgIdsToRemoveInactiveDomains: List[Int] = List(
    2573,
    1648,
    10458
  )


  val senderCronTestEmailSettingIdList: Seq[Long] = Seq(
    26203 ,
    26717 ,
//    26718 ,
    26726 ,
    30741 ,
    30742 ,
    31130 ,
    31131 ,
    31133 ,
    31134 ,
    31135 ,
//    34569 ,
    34571 ,
    34572 ,
    34573 ,
    34574 ,
    38861 ,
    38864 ,
    42149 ,
//    42169 ,
    42350 ,
    42353 ,
//    43226 ,
//    43238 ,
    43239 ,
    38865 ,
    34575
  )
  
  val stopAutoUpdatecategoryForOrgIdList: Seq[Long] = Seq(
    15476L //7-Jun-25: quantum ga
  )
}
