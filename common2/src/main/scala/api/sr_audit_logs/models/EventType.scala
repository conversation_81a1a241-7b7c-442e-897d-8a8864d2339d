package api.sr_audit_logs.models

import api.AppConfig
import play.api.libs.json.{Json, OFormat}
import utils.enum_sr_utils.SREnumJsonUtils

import scala.util.Try

case class FilterForWebhooks(
                              campaignsFilter : Seq[EventType],
                              replySentimentFilter: Seq[EventType],
                              prospectCategoryFilter: Seq[EventType]
                            )

object FilterForWebhooks {
  implicit val format: OFormat[FilterForWebhooks] = Json.format[FilterForWebhooks]
}

enum EventType(val key: String) {
  override def toString: String = key

  case REPLY_SENTIMENT_UPDATED extends EventType("activity.reply_sentiment_updated")
  case TASK_REPLY_SENTIMENT_UPDATED extends EventType("activity.task_reply_sentiment_updated")
  case EMAIL_OPENED extends EventType("activity.email.opened")
  case PROSPECT_CATEGORY_UPDATED extends EventType("activity.prospect.category_updated")
  case CREATED_PROSPECT_IN_SMARTREACH extends EventType("prospect.created")
  case PROSPECT_CREATED extends EventType("activity.prospect.created")
  case UPDATED_PROSPECT_IN_SMARTREACH extends EventType("prospect.updated")
  case CREATED_OR_UPDATED_PROSPECT_IN_SMARTREACH extends EventType("prospect.created_or_updated")
  case HUBSPOT_PROSPECT_SYNC extends EventType("hubspot_contact_sync")
  case ZOHO_PROSPECT_SYNC extends EventType("zoho_contact_sync")
  case ZOHO_LEAD_SYNC extends EventType("zoho_lead_sync")
  case ZOHO_RECRUIT_PROSPECT_SYNC extends EventType("zoho_recruit_contact_sync")
  case ZOHO_RECRUIT_CANDIDATE_SYNC extends EventType("zoho_recruit_candidate_sync")
  case PIPEDRIVE_PROSPECT_SYNC extends EventType("pipedrive_contact_sync")
  case SALESFORCE_PROSPECT_SYNC extends EventType("salesforce_contact_sync")
  case SALESFORCE_LEAD_SYNC extends EventType("salesforce_lead_sync")
  case EMAIL_SENT extends EventType("activity.email.sent")
  case EMAIL_BOUNCED extends EventType("activity.email.bounced")
  case EMAIL_LINK_CLICKED extends EventType("activity.email.link_clicked")
  case EMAIL_INVALID extends EventType("activity.email.invalid")
  case NEW_REPLY extends EventType("activity.email.replied")
  case AUTO_REPLY extends EventType("activity.email.auto_replied")
  case OUT_OF_OFFICE_REPLY extends EventType("activity.email.out_of_office_replied")
  case PROSPECT_OPTED_OUT extends EventType("activity.prospect.opted_out")
  case PROSPECT_COMPLETED extends EventType("activity.prospect.completed_campaign")
  case ANY_EMAIL_ACTIVITY extends EventType("activity.email.any")
  case ADD_TO_DO_NOT_CONTACT extends EventType("activity.add_to_do_not_contact")
  case AUTO_LINKEDIN_TASK_COMPLETED extends EventType("activity.auto_linkedin_tasks.completed")
  case AUTO_LINKEDIN_TASK_FAILED extends EventType("activity.auto_linkedin_tasks.failed")
  case TEAM_MEMBER_ASSIGNED extends EventType("activity.user.assigned")
  case TASK_CREATED extends EventType("activity.task.created")
  case INTERNAL_TASK_CREATED extends EventType("activity.task.internal_task_created")
  case TASK_DUE extends EventType("activity.task.due")
  case TASK_SNOOZED extends EventType("activity.task.snoozed")
  case TASK_DONE extends EventType("activity.task.done")
  case TASK_SKIPPED extends EventType("activity.task.skipped")
  case TASK_ARCHIVED extends EventType("activity.task.archived")
  case CALL_PLACED extends EventType("activity.call.placed")
  case CALL_RECEIVED extends EventType("activity.call.received")
  case CALENDAR_MEETING_BOOKED extends EventType("activity.calendar.meeting.booked")
  case PROSPECT_ACCOUNT_ADDED extends EventType("activity.prospect_account.added")
  case MARKED_REPLIED_MANUALLY_BY_ADMIN extends EventType("activity.campaign.marked_replied")
  case NOTE_ADDED extends EventType("activity.note.added")
  case MERGED_DUPLICATES extends EventType("activity.merged_duplicates")
  case EVENT_V3_PROSPECT_OPTED_OUT extends EventType("activity.campaign.opted_out")
  case EVENT_V3_PROSPECT_COMPLETED extends EventType("activity.campaign.completed_campaign")
  case EVENT_V3_PROSPECT_ADDED extends EventType("activity.campaign.added_to_campaign")
  case EVENT_V3_PROSPECT_REMOVED extends EventType("activity.campaign.removed_from_campaign")
  case EVENT_V3_PROSPECT_PAUSED extends EventType("activity.campaign.paused")
}

object EventType extends SREnumJsonUtils[EventType] {
  override protected val enumName: String = "EventType"
  
  // Lookup map for fromKey
  private val keyToEventMap: Map[String, EventType] = EventType.values.map(et => et.key -> et).toMap


  def toV2Key(value: EventType): String = {
    value match {
      case EVENT_V3_PROSPECT_ADDED => "assigned_campaign"
      case EVENT_V3_PROSPECT_REMOVED => "unassigned_campaign"
      case TEAM_MEMBER_ASSIGNED => "assigned_member"
      case PROSPECT_CATEGORY_UPDATED => "changed_category"
      case EMAIL_OPENED => "opened"
      case EMAIL_LINK_CLICKED => "clicked"
      case PROSPECT_CREATED => "added_prospect"
      case PROSPECT_ACCOUNT_ADDED => "added_prospect_account"
      case EMAIL_SENT => "sent"
      case EMAIL_BOUNCED => "bounced"
      case NEW_REPLY => "replied"
      case MARKED_REPLIED_MANUALLY_BY_ADMIN => "marked_replied_manually_by_admin"
      case EVENT_V3_PROSPECT_OPTED_OUT => "opted_out"
      case EVENT_V3_PROSPECT_COMPLETED => "completed"
      case EVENT_V3_PROSPECT_PAUSED => "paused_campaign"
      case TASK_CREATED => "task_created"
      case TASK_DUE => "task_due"
      case TASK_SNOOZED => "task_snoozed"
      case TASK_DONE => "task_done"
      case TASK_SKIPPED => "task_skipped"
      case TASK_ARCHIVED => "task_archived"
      case CALENDAR_MEETING_BOOKED => "calendar_meeting_booked"
      case CALL_PLACED => "call_placed"
      case CALL_RECEIVED => "call_received"
      case INTERNAL_TASK_CREATED => "internal_task_created"
      case NOTE_ADDED => "note_added"
      case MERGED_DUPLICATES => "merged_duplicates"
      case _ => value.key
    }
  }

  override def toKey(value: EventType): String = value.key

  override def fromKey(key: String): Try[EventType] = Try {
    keyToEventMap.getOrElse(key, throw new Exception(s"${AppConfig.shouldNeverHappenKey} Unknown event type key: $key"))
  }


  // vise vera function for getV2Key : to get EventType from v2 key
  def getEventFromV2Key(key: String): EventType = {
    key match {
      case "assigned_campaign" => EVENT_V3_PROSPECT_ADDED
      case "unassigned_campaign" => EVENT_V3_PROSPECT_REMOVED
      case "assigned_member" => TEAM_MEMBER_ASSIGNED
      case "changed_category" => PROSPECT_CATEGORY_UPDATED
      case "opened" => EMAIL_OPENED
      case "clicked" => EMAIL_LINK_CLICKED
      case "added_prospect" => PROSPECT_CREATED
      case "added_prospect_account" => PROSPECT_ACCOUNT_ADDED
      case "sent" => EMAIL_SENT
      case "bounced" => EMAIL_BOUNCED
      case "replied" => NEW_REPLY
      case "marked_replied_manually_by_admin" => MARKED_REPLIED_MANUALLY_BY_ADMIN
      case "opted_out" => EVENT_V3_PROSPECT_OPTED_OUT
      case "completed" => EVENT_V3_PROSPECT_COMPLETED
      case "paused_campaign" => EVENT_V3_PROSPECT_PAUSED
      case "task_created" => TASK_CREATED
      case "task_due" => TASK_DUE
      case "task_snoozed" => TASK_SNOOZED
      case "task_done" => TASK_DONE
      case "task_skipped" => TASK_SKIPPED
      case "task_archived" => TASK_ARCHIVED
      case "calendar_meeting_booked" => CALENDAR_MEETING_BOOKED
      case "call_placed" => CALL_PLACED
      case "call_received" => CALL_RECEIVED
      case "internal_task_created" => INTERNAL_TASK_CREATED
      case "note_added" => NOTE_ADDED
      case "merged_duplicates" => MERGED_DUPLICATES
      case _ => EventType.fromKey(key).get  // TODO: remove .get
    }
  }

  val filterOutEventsForV3PublicApi: Seq[EventType] = Seq(NOTE_ADDED, MERGED_DUPLICATES)

  def isBatch(eventType: EventType): Boolean = {
    eventType == EventType.CREATED_PROSPECT_IN_SMARTREACH ||
      eventType == EventType.UPDATED_PROSPECT_IN_SMARTREACH ||
      eventType == EventType.CREATED_OR_UPDATED_PROSPECT_IN_SMARTREACH
  }

  def getFiltersForEvents(): FilterForWebhooks = {
    val campaignFilterEvents = Seq(
      EventType.NEW_REPLY,
      EventType.EMAIL_OPENED,
      EventType.EMAIL_BOUNCED,
      EventType.EMAIL_LINK_CLICKED,
      EventType.PROSPECT_COMPLETED,
      EventType.PROSPECT_OPTED_OUT,
      EventType.EMAIL_SENT)

    val replySentimentFilterEvents = Seq(
      EventType.REPLY_SENTIMENT_UPDATED,
      EventType.TASK_REPLY_SENTIMENT_UPDATED)

    val prospectFilterEvents = Seq(
      EventType.PROSPECT_CATEGORY_UPDATED)

    FilterForWebhooks(
      campaignsFilter = campaignFilterEvents,
      replySentimentFilter = replySentimentFilterEvents,
      prospectCategoryFilter = prospectFilterEvents
    )
  }
}
