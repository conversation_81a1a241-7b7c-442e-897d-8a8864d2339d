package api.config

import com.typesafe.config.{Config, ConfigFactory}

object AppConfigCommonAuth {

  /*Note: Nginx by default don't allow use of underscore in any headers key name, if used it silently drops the header
  * from request header. So Don't use _ in Key unless your nginx is explicitly configured to allow '_' in key name
  *
  * https://serverfault.com/questions/586970/nginx-is-not-forwarding-a-header-value-when-using-proxy-pass
  *
  *
  *
  * */
  val clientIdKeyName = "CLIENT-ID"
  val clientIdKeySecretKeyName = "CLIENT-SECRET"
  val commonAuthApiKeyName = "COMMON-AUTH-API-KEY"
  val decryptedValueForSmartreach = "user_common_auth_smartreach"

  val config: Config = ConfigFactory.load()

  val hydraPublicUrl: String = config.getString("smartreach_auth.common_auth_server_url")

  val hydraAdminUrl: String = config.getString("smartreach_auth.common_auth_hydra_admin_api")

  val commonAuthFrontendUrl: String = config.getString("smartreach_auth.common_auth_frontend_url")

  val idApiBaseUrl: String = config.getString("smartreach_auth.common_auth_backend_url")

  val remember: Boolean = true /*Changing this to true as hydra issues session cookies which then allows it to remember the subject
      because of which it gives us sso kind of functionality , that is if you get logged in to smartreach , you will automatically
      get logged in  other apps connected with CommonAuth.
      */

  val remember_for_duration_in_seconds: Int = 3600

  object Smartreach {
    val clientId = config.getString("smartreach_auth.client_id")
    val clientSecret = config.getString("smartreach_auth.client_secret")
  }


  object CalendarApp {
    val meetingsCommonAuthClientId = config.getString("calendar_app.meetings_client_id")
    val meetingsCommonAuthClientSecret = config.getString("calendar_app.meetings_client_secret")
  }

  object Billing {
    val billingClientId =  config.getString("billing_app.billing_client_id")
    val billingClientSecret = config.getString("billing_app.billing_client_secret")
  }


  def validSmartReachCommonAuthClients = Seq(
    (Smartreach.clientId, Smartreach.clientSecret),
    (CalendarApp.meetingsCommonAuthClientId, CalendarApp.meetingsCommonAuthClientSecret),
    (Billing.billingClientId,Billing.billingClientSecret)
  )
}
