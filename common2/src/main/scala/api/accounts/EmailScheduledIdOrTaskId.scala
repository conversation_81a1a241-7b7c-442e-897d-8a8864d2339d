package api.accounts


sealed trait EmailScheduledIdOrTaskId {
  val scheduleType: ScheduleType
}

sealed trait ScheduleType
object ScheduleType {
  case object EmailSchedule extends ScheduleType
  case object TaskSchedule extends ScheduleType
}

object EmailScheduledIdOrTaskId {
  case class TaskId(id: String) extends EmailScheduledIdOrTaskId {

    override def toString: String = id

    override val scheduleType: ScheduleType = ScheduleType.TaskSchedule

  }

  case class EmailScheduledId(id: Long) extends EmailScheduledIdOrTaskId {

    override def toString: String = id.toString

    override val scheduleType: ScheduleType = ScheduleType.EmailSchedule

  }

}


