package api.campaigns.models

import play.api.Logging
import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, <PERSON>sS<PERSON>, JsSuccess, <PERSON>sV<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>rites}

import scala.util.{Failure, Success, Try}

sealed trait CampaignType

object CampaignType extends Logging {
  private val sequence = "sequence"
  private val email = "email"
  private val drip = "drip"
  private val multichannel = "multichannel"
  private val magicContent = "magic_content"

  case object Email extends CampaignType {
    override def toString: String = email
  }

  case object MultiChannel extends CampaignType {
    override def toString: String = multichannel
  }

  case object Drip extends CampaignType {
    override def toString: String = drip
  }

  case object MagicContent extends CampaignType {
    override def toString: String = magicContent
  }

  def fromString(key: String): Try[CampaignType] = Try {
    key match {
      case `email` => Email
      case `sequence` => MultiChannel  // FIXME_DRIP Converting sequence to multichannel we will remove it later
      case `multichannel` => MultiChannel
      case `drip` => Drip
      case `magicContent` => MagicContent
    }
  }

  implicit def reads: Reads[CampaignType] = {
    case JsString(value) =>
      fromString(key = value) match {

        case Failure(exception) => JsError(exception.getMessage)

        case Success(data) => JsSuccess(value = data)
      }

    case unExpectedValue =>
      logger.error(s"we aren't interested in this as it will be JsString only UnexpectedValue :  ${unExpectedValue}")
      JsError(s"""Expected a campaign-type string, got something else ${unExpectedValue}""")
  }

  implicit def writes: Writes[CampaignType] = new Writes[CampaignType] {
    def writes(ev: CampaignType): JsValue = {

      ev match {
        case Email => Json.toJson(email)

        case MultiChannel => Json.toJson(multichannel)

        case Drip => Json.toJson(drip)

        case MagicContent => Json.toJson(magicContent)

      }

    }
  }
}
