package api.campaigns.models

import play.api.libs.json.{Js<PERSON><PERSON>r, Js<PERSON><PERSON>ber, JsResult, JsSuccess, JsValue, Reads, Writes}

case class CampaignEmailSettingsId(id: Long) extends AnyVal


object CampaignEmailSettingsId{
  implicit val reads: Reads[CampaignEmailSettingsId] = new Reads[CampaignEmailSettingsId] {
    override def reads(ev: JsValue): JsResult[CampaignEmailSettingsId] = {
      ev match {
        case JsNumber(id) => JsSuccess(CampaignEmailSettingsId(id = id.toLong))
        case randomValue => JsError(s"expected number, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[CampaignEmailSettingsId] = new Writes[CampaignEmailSettingsId] {
    override def writes(o: CampaignEmailSettingsId): JsValue = JsNumber(o.id)
  }
}