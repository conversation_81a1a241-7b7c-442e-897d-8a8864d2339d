package api.campaigns.models

import play.api.libs.json.{Js<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Writes}

sealed trait CampaignTypeData {
  def campaign_type: CampaignType
  def head_id: String
}

object CampaignTypeData {
  case class MultiChannelCampaignData(head_step_id: Long) extends CampaignTypeData {
    val campaign_type: CampaignType = CampaignType.MultiChannel
    override def head_id: String = head_step_id.toString
  }
  case class EmailChannelData(head_step_id: Long) extends CampaignTypeData {
    val campaign_type: CampaignType = CampaignType.Email
    override def head_id: String = head_step_id.toString
  }

  case class DripCampaignData(
                               nodes: List[JsValue],
                               edges: List[JsValue],
                               head_node_id: String
                             ) extends CampaignTypeData {
    val campaign_type: CampaignType = CampaignType.Drip
    override def head_id: String = head_node_id
  }

  case class MagicContentData(head_step_id: Long) extends CampaignTypeData {
    val campaign_type: CampaignType = CampaignType.MagicContent
    override def head_id: String = head_step_id.toString
  }

  object DripCampaignData {
    implicit val writes: Writes[DripCampaignData] = Json.writes[DripCampaignData]
  }

  object MagicContentData {
    implicit val writes: Writes[MagicContentData] = Json.writes[MagicContentData]
  }
}