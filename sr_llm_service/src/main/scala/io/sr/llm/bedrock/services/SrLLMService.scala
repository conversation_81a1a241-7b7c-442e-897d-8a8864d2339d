package io.sr.llm.bedrock.services

import io.sr.llm.bedrock.api.{AwsBedrockLlama3Api, Llama3Response}
import utils.ISRLogger

import scala.concurrent.{ExecutionContext, Future}

/**
  * 12-Aug-2024
  *
  * Information required to generate a good email: (Provided by Llama 3 70B Instruct)
  *
  * 1. **Purpose of the email**: What is the main objective of the email? Is it to:
  * Provide information or update?
  * Make a sale or promotion?
  * Build a relationship or introduce oneself?
  * Apologize or resolve an issue?
  *
  * 2. **Target audience**: Who is the email addressed to? Consider their:
  * Job title or role
  * Industry or sector
  * Level of familiarity with the topic
  * Preferred tone and language
  *
  * 3. **Key topic or theme**: What is the central idea or topic of the email? This could be a:
  * Product or service name
  * Event or deadline
  * Question or problem
  * Opportunity or benefit
  *
  * 4. **Tone and personality**: What tone do you want to convey in the subject line? Should it be:
  * Formal and professional
  * Friendly and approachable
  * Urgent or serious
  *
  * 5. **Length and format**: Are there any specific length or format requirements for the subject line? For example:
  * Character limit (e.g., 50-60 characters)
  * Use of keywords or phrases
  * Inclusion of numbers or dates
  *
  * 6. **Uniqueness and creativity**: How can the subject line stand out from other emails in the recipient's inbox? Consider using:
  * A clever play on words
  * A surprising statistic or fact
  * A thought-provoking question
  * A personalized element (e.g., the recipient's name)
  *
  * 9. **Call-to-action (CTA)**: Is there a specific action you want the recipient to take after reading the subject line? Consider including a:
  * Verb (e.g., "Learn more," "Get started")
  * Benefit (e.g., "Boost your productivity")
  * Incentive (e.g., "Limited time offer")
  */


case class GeneratedEmail(
  subject: String,
  subject_prompt_token_count: Int,
  subject_generation_token_count: Int,

  body: String,
  body_prompt_token_count: Int,
  body_generation_token_count: Int,
)

class SrLLMService(
  awsBedrockLlama3Api: AwsBedrockLlama3Api
) {

  private def generatePositiveReplyEmailBodyPrompt(
    emailSubject: String,
  ): String = {

    // NOTE: We can include the original email body to generate better reply email,
    //  but the `prompt_token_count` will be almost double.

    s"""
       |You are an intelligent bot who can write excellent positive email replies
       |
       |We have received an email with the following subject:
       |
       |"$emailSubject"
       |
       |Write an positive reply email body for the provided email subject.
       |
       |Maximum Word Length: 248
       |
       |Do not include the following in the generated reply email body:
       |
       |1. Do not include the subject
       |
       |2. Do not include anything other than the email body
       |
       |3. Do not include the email signature
       |
       |4. Do not include email salutation
       |
       |5. Only include the final reply email body nothing else.
       |
       |Reply Email Body:
       |""".stripMargin

  }

  private def generateEmailBodyPrompt(
    emailPurpose: String,
    emailSubject: String,
    senderPosition: String,
    typeOfEmail: String,
    companyName: String,
    year: Int,
  ): String = {

    s"""
       |You are a $senderPosition at $companyName in the year $year who is responsible for sending $typeOfEmail
       |
       |Purpose of the email: $emailPurpose
       |
       |Email Subject: $emailSubject
       |
       |Use the above mentioned email subject to write an effective email body
       |
       |Don't not use the mentioned company name to generate the email body
       |
       |Maximum Word Length: 248
       |
       |Do not include the following in the generated email body:
       |
       |1. Do not include the subject
       |
       |2. Do not include anything other than the email body
       |
       |3. Do not include the email signature
       |
       |4. Do not include email salutation
       |
       |5. Only include the final email body nothing else.
       |
       |Email Body:
       |""".stripMargin

  }

  private def generateEmailSubjectPrompt(
    senderPosition: String,
    typeOfEmail: String,
    companyName: String,
    year: Int,
  ): String = {

    s"""
       |You are a $senderPosition at $companyName in the year $year who is responsible for sending $typeOfEmail
       |
       |1. Pick a random $typeOfEmail topic
       |
       |2. Write an email subject for that random $typeOfEmail topic.
       |
       |3. Don't not use the mentioned company name to generate the email subject.
       |
       |4. Only include one email subject in the generated output nothing else.
       |
       |Generated Subject:
       |""".stripMargin

  }


  // TODO: Make some of these field Optional.
  def generateEmailSubjectAndBody(
    emailPurpose: String,
    senderPosition: String,
    typeOfEmail: String,
    companyName: String,
    year: Int,
  )(
    implicit logger: ISRLogger,
    ec: ExecutionContext,
  ): Future[GeneratedEmail] = {

    val subjectPrompt = generateEmailSubjectPrompt(
      senderPosition = senderPosition,
      typeOfEmail = typeOfEmail,
      companyName = companyName,
      year = year,
    )

    for {

      generatedEmailSubjectRes: Llama3Response <- awsBedrockLlama3Api.generateResponse(
        prompt = subjectPrompt
      )

      emailBodyPrompt: String = generateEmailBodyPrompt(
        emailPurpose = emailPurpose,
        emailSubject = generatedEmailSubjectRes.generation,
        senderPosition = senderPosition,
        typeOfEmail = typeOfEmail,
        companyName = companyName,
        year = year,
      )

      generatedEmailBodyRes: Llama3Response <- awsBedrockLlama3Api.generateResponse(
        prompt = emailBodyPrompt
      )

    } yield {

      GeneratedEmail(
        subject = generatedEmailSubjectRes.generation,
        subject_generation_token_count = generatedEmailSubjectRes.generation_token_count,
        subject_prompt_token_count = generatedEmailSubjectRes.prompt_token_count,

        body = generatedEmailBodyRes.generation,
        body_generation_token_count = generatedEmailBodyRes.generation_token_count,
        body_prompt_token_count = generatedEmailBodyRes.prompt_token_count,
      )

    }

  }

  def generatePositiveReplyEmailBody(
    emailSubject: String,
  )(
    implicit logger: ISRLogger,
    ec: ExecutionContext,
  ): Future[GeneratedEmail] = {

    val replyEmailBodyPrompt: String = generatePositiveReplyEmailBodyPrompt(
      emailSubject = emailSubject,
    )

    for {

      generatedEmailBodyRes: Llama3Response <- awsBedrockLlama3Api.generateResponse(
        prompt = replyEmailBodyPrompt
      )

    } yield {

      GeneratedEmail(
        subject = emailSubject,
        subject_generation_token_count = 0,
        subject_prompt_token_count = 0,

        body = generatedEmailBodyRes.generation,
        body_generation_token_count = generatedEmailBodyRes.generation_token_count,
        body_prompt_token_count = generatedEmailBodyRes.prompt_token_count,
      )

    }

  }

}
