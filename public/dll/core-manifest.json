{"name": "core", "content": {"./node_modules/process/browser.js": {"id": 0, "meta": {}}, "./node_modules/webpack/buildin/global.js": {"id": 43, "meta": {}}, "./node_modules/es6-promise/dist/es6-promise.js": {"id": 121, "meta": {}}, "./node_modules/lodash/lodash.js": {"id": 122, "meta": {}}, "./node_modules/tslib/tslib.es6.js": {"id": 125, "meta": {"harmonyModule": true}, "exports": ["__extends", "__assign", "__rest", "__decorate", "__param", "__metadata", "__awaiter", "__generator", "__exportStar", "__values", "__read", "__spread", "__await", "__asyncGenerator", "__asyncDelegator", "__asyncValues"]}, "./node_modules/webpack/buildin/module.js": {"id": 264, "meta": {}}}}