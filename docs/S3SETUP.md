```
# install pip
curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py

python get-pip.py

MAC: 
sudo pip install aws-shell --upgrade --ignore-installed six 

OTHERS:
pip install aws-shell

AWS_ACCESS_KEY_ID=******************** AWS_SECRET_ACCESS_KEY=KJf9z4/qQQavpOHUUiYb2MF2JYj9l9PlG3K1NceB aws s3 ls s3://srjars


AWS_ACCESS_KEY_ID=******************** AWS_SECRET_ACCESS_KEY=KJf9z4/qQQavpOHUUiYb2MF2JYj9l9PlG3K1NceB aws s3 cp /Users/<USER>/Documents/projects/coldemail/target/scala-2.11/coldemail.jar s3://srjars/coldemail.jar


date +%s
```