package api.campaigns.models

import sr_scheduler.models.ChannelType

import scala.util.Try


/*
MULTICHANNEL_REFACTOR: add the other step types
 */
sealed trait CampaignStepType {
  def toKey: String

  def channelType: ChannelType

  def channelStepType: ChannelStepType
  def isAuto: <PERSON>olean
}


trait ChannelStepType {

  def channelStepType: ChannelStepType = this

}

object ChannelStepType {

  sealed trait IndependentStepType extends ChannelStepType

  sealed trait LinkedinStepType extends ChannelStepType

  sealed trait EmailStepType extends ChannelStepType

  sealed trait SmsStepType extends ChannelStepType

  sealed trait CallStepType extends  ChannelStepType

  sealed trait WhatsappStepType extends ChannelStepType

  sealed trait GeneralTaskStepType extends ChannelStepType

}



object CampaignStepType {
  val autoEmailStep = "send_email" // 26-June-2022: this was the earlier old name, so retaining it
  val manualEmailStep = "manual_send_email"
  val linkedinConnectionRequest = "send_linkedin_connection_request"
  val linkedinMessage = "send_linkedin_message"
  val linkedinInmail = "send_linkedin_inmail"
  val linkedinViewProfile = "linkedin_view_profile"
  val autoLinkedinConnectionRequest = "auto_send_linkedin_connection_request"
  val autoLinkedinMessage = "auto_send_linkedin_message"
  val autoLinkedinInmail = "auto_send_linkedin_inmail"
  val autoLinkedinViewProfile = "auto_linkedin_view_profile"
  val generalTask = "general_task"
  val whatsappMessage = "send_whatsapp_message"
  val smsMessage = "send_sms" // fixme change this to send_sms
  val callStep = "call"

  val moveToAnotherCampaign = "move_to_another_campaign"

  // Magic Content
  val autoEmailMagicContent = "auto_email_magic_content"
  val manualEmailMagicContent = "manual_email_magic_content"


  case object AutoEmailStep extends CampaignStepType with ChannelStepType.EmailStepType {
    override def toKey: String = autoEmailStep

    override def channelType: ChannelType = ChannelType.EmailChannel

    override def isAuto: Boolean = true
  }

  case object ManualEmailStep extends CampaignStepType with ChannelStepType.EmailStepType {
    override def toKey: String = manualEmailStep

    override def channelType: ChannelType = ChannelType.EmailChannel
    override def isAuto: Boolean = false

  }
    
  case object AutoEmailMagicContent extends CampaignStepType with ChannelStepType.EmailStepType {
    override def toKey: String = autoEmailMagicContent

    override def channelType: ChannelType = ChannelType.EmailChannel
    override def isAuto: Boolean = true
  }

  case object ManualEmailMagicContent extends CampaignStepType with ChannelStepType.EmailStepType {
    override def toKey: String = manualEmailMagicContent

    override def channelType: ChannelType = ChannelType.EmailChannel
    override def isAuto: Boolean = false
  }

  case object LinkedinConnectionRequest extends CampaignStepType with ChannelStepType.LinkedinStepType {
    override def toKey: String = linkedinConnectionRequest

    override def channelType: ChannelType = ChannelType.LinkedinChannel
    override def isAuto: Boolean = false

  }

  case object LinkedinMessage extends CampaignStepType with ChannelStepType.LinkedinStepType {
    override def toKey: String = linkedinMessage

    override def channelType: ChannelType = ChannelType.LinkedinChannel
    override def isAuto: Boolean = false

  }

  case object LinkedinInmail extends CampaignStepType with ChannelStepType.LinkedinStepType {
    override def toKey: String = linkedinInmail

    override def channelType: ChannelType = ChannelType.LinkedinChannel
    override def isAuto: Boolean = false

  }

  case object LinkedinViewProfile extends CampaignStepType with ChannelStepType.LinkedinStepType {
    override def toKey: String = linkedinViewProfile

    override def channelType: ChannelType = ChannelType.LinkedinChannel
    override def isAuto: Boolean = false

  }

  case object AutoLinkedinConnectionRequest extends CampaignStepType with ChannelStepType.LinkedinStepType {
    override def toKey: String = autoLinkedinConnectionRequest

    override def channelType: ChannelType = ChannelType.LinkedinChannel
    override def isAuto: Boolean = true

  }

  case object AutoLinkedinMessage extends CampaignStepType with ChannelStepType.LinkedinStepType {
    override def toKey: String = autoLinkedinMessage

    override def channelType: ChannelType = ChannelType.LinkedinChannel
    override def isAuto: Boolean = true

  }

  case object AutoLinkedinInmail extends CampaignStepType with ChannelStepType.LinkedinStepType {
    override def toKey: String = autoLinkedinInmail

    override def channelType: ChannelType = ChannelType.LinkedinChannel
    override def isAuto: Boolean = true

  }

  case object AutoLinkedinViewProfile extends CampaignStepType with ChannelStepType.LinkedinStepType {
    override def toKey: String = autoLinkedinViewProfile

    override def channelType: ChannelType = ChannelType.LinkedinChannel
    override def isAuto: Boolean = true

  }

  case object GeneralTask extends CampaignStepType with ChannelStepType.GeneralTaskStepType {
    override def toKey: String = generalTask

    override def channelType: ChannelType = ChannelType.GeneralChannel
    override def isAuto: Boolean = false

  }

  case object WhatsappMessage extends CampaignStepType with ChannelStepType.WhatsappStepType {
    override def toKey: String = whatsappMessage

    override def channelType: ChannelType = ChannelType.WhatsappChannel
    override def isAuto: Boolean = false

  }

  case object SmsMessage extends CampaignStepType with ChannelStepType.SmsStepType {
    override def toKey: String = smsMessage

    override def channelType: ChannelType = ChannelType.SmsChannel
    override def isAuto: Boolean = false

  }

  case object CallStep extends CampaignStepType with ChannelStepType.CallStepType {
    override def toKey: String = callStep

    override def channelType: ChannelType = ChannelType.CallChannel
    override def isAuto: Boolean = false

  }

  case object MoveToAnotherCampaignStep extends CampaignStepType with ChannelStepType.IndependentStepType {
    override def toKey: String = moveToAnotherCampaign

    override def channelType: ChannelType = ChannelType.IndependentChannel
    override def isAuto: Boolean = true

  }


  //TODO : add the unit test for the below function
  def fromKey(str: String): Try[CampaignStepType] = Try {

    str match {
      case `autoEmailStep` => AutoEmailStep
      case `manualEmailStep` => ManualEmailStep
      case `autoEmailMagicContent` => AutoEmailMagicContent
      case `manualEmailMagicContent` => ManualEmailMagicContent
      case `linkedinConnectionRequest` => LinkedinConnectionRequest
      case `linkedinMessage` => LinkedinMessage
      case `linkedinInmail` => LinkedinInmail
      case `linkedinViewProfile` => LinkedinViewProfile
      case `autoLinkedinConnectionRequest` => AutoLinkedinConnectionRequest
      case `autoLinkedinMessage` => AutoLinkedinMessage
      case `autoLinkedinInmail` => AutoLinkedinInmail
      case `autoLinkedinViewProfile` => AutoLinkedinViewProfile
      case `generalTask` => GeneralTask
      case `whatsappMessage` => WhatsappMessage
      case `smsMessage` => SmsMessage
      case `callStep` => CallStep
      case `moveToAnotherCampaign` => MoveToAnotherCampaignStep
    }

  }

  def isEmailOnlyCampaign(steps:  Set[CampaignStepType]): Boolean = {

    val hasMultichannelStep = steps
      .exists(p =>  p.channelType != ChannelType.EmailChannel) //checking if there is a step type that is not Email

    !hasMultichannelStep && steps.nonEmpty //Doing a Not so that we can check if its a email only campaign
  }

  val linkedinStepTypes: List[CampaignStepType] = {
    List(
      CampaignStepType.LinkedinInmail,
      CampaignStepType.LinkedinMessage,
      CampaignStepType.LinkedinViewProfile,
      CampaignStepType.LinkedinConnectionRequest,
      CampaignStepType.AutoLinkedinInmail,
      CampaignStepType.AutoLinkedinMessage,
      CampaignStepType.AutoLinkedinViewProfile,
      CampaignStepType.AutoLinkedinConnectionRequest
    )
  }


  val linkedinConnectionRequestStepTypes: List[CampaignStepType] = {
    List(
      CampaignStepType.AutoLinkedinConnectionRequest,
      CampaignStepType.LinkedinConnectionRequest
    )
  }

  val linkedinMessageStepTypes: List[CampaignStepType] = {
    List(
      CampaignStepType.LinkedinMessage,
      CampaignStepType.AutoLinkedinMessage
    )
  }

  val linkedinInmailStepTypes: List[CampaignStepType] = {
    List(
      CampaignStepType.LinkedinInmail,
      CampaignStepType.AutoLinkedinInmail
    )
  }

  val linkedinViewProfileStepTypes: List[CampaignStepType] = {
    List(
      CampaignStepType.LinkedinViewProfile,
      CampaignStepType.AutoLinkedinViewProfile
    )
  }

  val linkedinAutomatedStepTypes: List[CampaignStepType] = {
    List(
      CampaignStepType.AutoLinkedinInmail,
      CampaignStepType.AutoLinkedinConnectionRequest,
      CampaignStepType.AutoLinkedinMessage,
      CampaignStepType.AutoLinkedinViewProfile
    )
  }

  val linkedinManualStepTypes: List[CampaignStepType] = {
    List(
      CampaignStepType.LinkedinInmail,
      CampaignStepType.LinkedinConnectionRequest,
      CampaignStepType.LinkedinViewProfile,
      CampaignStepType.LinkedinMessage
    )
  }
}
