package sr_scheduler.models

import org.joda.time.DateTime

case class LinkedinSettingCreateSchedule (
                                         uuid: String,
                                         team_id: Long,
                                         account_id: Long,
                                         first_name: String,
                                         last_name: String,
                                         email: String,

                                         linkedin_message_limit_per_day: Int,
                                         linkedin_view_profile_limit_per_day: Int,
                                         linkedin_inmail_limit_per_day: Int,
                                         linkedin_connection_request_limit_per_day: Int,

                                         account_timezone: String,
                                         latest_task_scheduled_at: Option[DateTime],
                                         min_delay_seconds: Int,
                                         max_delay_seconds: Int
                                         )
