package sr_scheduler.models

import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepType}
import org.joda.time.DateTime

case class GeneratedContent(
                             subject: String,
                             body: String,
                             base_body: String,
                             text_body: String
                           )

case class EmailScheduledNew3(

                              campaign_id: Option[Long],
                              step_id: Option[Long],
                              is_opening_step: <PERSON><PERSON><PERSON>,
                              prospect_id: Option[Long],
                              prospect_account_id: Option[Long],
                              added_at: DateTime,
                              scheduled_at: DateTime,
                              sender_email_settings_id: Long,
                              template_id: Option[Long],
                              variant_id: Option[Long],
                              rep_mail_server_id: Int,
                              via_gmail_smtp: Option[Boolean],
                              step_type: CampaignStepType,

                              team_id: Long,
                              account_id: Long,
                              rep_tracking_host_id: Long,

                              to_email: String,
                              to_name: Option[String],

                              from_email: String,
                              from_name: String,

                              reply_to_email: Option[String],
                              reply_to_name: Option[String],

                              campaign_name: Option[String],
                              step_name: Option[String],
                              receiver_email_settings_id: Long,

                              scheduled_from_campaign: <PERSON><PERSON>an,
                              scheduled_manually: <PERSON><PERSON><PERSON>,

                              email_thread_id: Option[<PERSON>], 
                              pushed_to_rabbitmq: <PERSON><PERSON>an,
                             
                              campaign_email_settings_id: CampaignEmailSettingsId,
                              generatedContent: Option[GeneratedContent] = None,
                              uuid: String,
                              subject: String,
                              body: String,
                              base_body: String,
                              text_body: String,

                              has_open_tracking: Boolean,
                              has_click_tracking: Boolean,
                              has_unsubscribe_link: Boolean,
                              is_edited_preview_email: Boolean,

                              list_unsubscribe_header: Option[String],
                              gmail_fbl: Option[String]
                            )
