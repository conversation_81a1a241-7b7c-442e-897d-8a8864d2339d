package sr_scheduler.models

import org.joda.time.DateTime

case class WhatsAppSettingCreateWhatsAppSchedule (

  uuid: String,
                                            
  team_id: Long,
                                            
  account_id: Long,

  phone_number: String,
                                            
  sender_name: String,
                                            
  first_name: String,
                                            
  last_name: String,

  whatsapp_message_limit_per_day: Int = 50,

  channelOwnerAccountId: Long,

  latest_task_scheduled_at: Option[DateTime],

  account_timezone: String,

  min_delay_seconds: Int,
                                                 
  max_delay_seconds: Int

  )
