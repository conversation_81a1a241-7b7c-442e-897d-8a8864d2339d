package dao

import api_layer_models.{ConvertedCurrency, CurrencyType}
import play.api.libs.ws.WSClient
import utils.ISRLogger

import scala.concurrent.{ExecutionContext, Future}



class ApiLayerDAO {

  private val baseUrl: String = "https://api.apilayer.com/currency_data"

  def convertCurrency(
                     fromCurrency: CurrencyType,
                     toCurrency: CurrencyType,
                     api_key: String,
                     amount: Float

                     )(
    implicit ws: WSClient,
    ec: ExecutionContext,
    logger: ISRLogger
  ): Future[Option[ConvertedCurrency]] = {

    val response = ws.url(s"${baseUrl}/convert")
      .addQueryStringParameters(
        ("to", toCurrency.toString),
        ("from", fromCurrency.toString),
        ("amount", amount.toString))
      .addHttpHeaders(("apiKey", api_key))
      .get()

    response.flatMap( data => {


      val status = data.status

      if(status != 200){

        Future.failed(
          new Exception(s"UNRECOGNIZED_ERR, json: ${data.json}, body: ${data.body}")
        )

      }else {

        Future.successful(
          data.json.asOpt[ConvertedCurrency]
        )

      }

    })

  }

}
