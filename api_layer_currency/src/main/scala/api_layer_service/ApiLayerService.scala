package api_layer_service

import api_layer_models.{ConvertedCurrency, CurrencyType}
import dao.ApiLayerDAO
import play.api.libs.ws.WSClient
import utils.ISRLogger

import scala.concurrent.{ExecutionContext, Future}




class ApiLayerService(
                     apiLayerDao: ApiLayerDAO
                     ) {

  def convertCurrencyRate(
                           from: CurrencyType,
                           to : CurrencyType,
                           amount: Float,
                           apiKey: String
                         )(

    implicit ws: WSClient,
    ec: ExecutionContext,
    logger: ISRLogger

  ): Future[Option[ConvertedCurrency]] =

    apiLayerDao.convertCurrency(
      fromCurrency = from,
      toCurrency = to,
      amount = amount,
      api_key = apiKey
    )


}
