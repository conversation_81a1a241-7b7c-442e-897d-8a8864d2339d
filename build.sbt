import PublishMore.autoImport.{publishAll, publishResolvers}
import sbt.{Path, Resolver}

// "shell command".!! = execute the command, return result
import scala.sys.process.stringToProcess
/*
Commented out on 10-Dec-2024: we dont need this all the time.
Commenting this out reduced compilation time from ~190 seconds to ~125 seconds on my laptop.

inThisBuild(
  List(

    // REF: https://scalacenter.github.io/scalafix/docs/users/installation.html

    //scalaVersion := "2.12.17", // 2.11.12, 2.13.10, or 3.x
    semanticdbEnabled := true, // enable SemanticDB
    semanticdbVersion := scalafixSemanticdb.revision // only required for Scala 2.x
  )
)
*/


val defaultScalaVersion = "3.6.2"

// 10-Dec-2024: enable parallel compilation to reduce compile times. Uses all cores in the machine.
ThisBuild / parallelExecution := true


val jodaTime = "joda-time" % "joda-time" % "2.12.7"

val logger_dependency = "ch.qos.logback" % "logback-classic" % "1.5.6"

val apache_commons_dependency = "org.apache.commons" % "commons-text" % "1.8" // used in Template renderer to unescape custom_fields jsvalue

//val tastyReaderCompilerOption = "-Ytasty-reader" // https://www.scala-lang.org/blog/2020/11/19/scala-3-forward-compat.html
val removeUnused = "-Wunused:imports"
val xlintUnused = "-Xlint:unused"


lazy val scalaTestDependencies = Seq(
  "org.scalatest" %% "scalatest" % "3.2.19" % "test",
  "org.scalatest" %% "scalatest-flatspec" % "3.2.19" % "test",
  "org.scalamock" %% "scalamock" % "7.3.2" % Test,
)

lazy val playJsonDefault = "org.playframework" %% "play-json" % "3.0.4"
lazy val playJsonJodaDefault = "org.playframework" %% "play-json-joda" % "3.0.4"

lazy val coldemailDependencies = (
  Seq(
  jdbc,
  // cache,
  evolutions,

  filters,


    playJsonDefault,
    playJsonJodaDefault,
  "com.googlecode.libphonenumber" % "libphonenumber" % "8.10.23",
    "de.leanovate.play-mockws" %% "play-mockws-3-0" % "3.0.1" % Test,

    // REF: https://github.com/TakahikoKawasaki/nv-i18n
    // NOTE: 05-OCT-2024: used this to get country ISO code form country name
  "com.neovisionaries" % "nv-i18n" % "1.29",


    //  REF: http://scandilabs.com/technology/knowledge/How_to_search_gmail_accounts_via_JavaMail_and_IMAP
  ("com.sun.mail" % "gimap" % "1.6.7"),

  // NOTE: 27-Jan-2024: upgrading to play 2.9.1, java17
  // REF: https://stackoverflow.com/a/********
  "jakarta.xml.bind" % "jakarta.xml.bind-api" % "4.0.2",



//  "com.google.api-client" % "google-api-client" % "1.35.2",
//  "com.google.apis" % "google-api-services-gmail" % "v1-rev110-1.25.0",

  "software.amazon.awssdk" % "s3" % "2.25.27",

  "commons-io" % "commons-io" % "2.5",

  "com.linkedin.urls" % "url-detector" % "0.1.17",
  //  "org.apache.commons" % "commons-email" % "1.3.1",


  //  "org.quartz-scheduler" % "quartz" % "2.2.3",
  //  "org.quartz-scheduler" % "quartz-jobs" % "2.2.3",


  //  "c3p0" % "c3p0" % "0.9.1.1",


  // "edu.stanford.nlp" % "stanford-corenlp" % "3.5.2" artifacts(Artifact("stanford-corenlp", "models"), Artifact("stanford-corenlp")),


  "org.postgresql" % "postgresql" % "42.7.3",

  "org.scalikejdbc" %% "scalikejdbc" % "4.3.1",
  "org.scalikejdbc" %% "scalikejdbc-config" % "4.3.1",
  "org.scalikejdbc" %% "scalikejdbc-joda-time" % "4.3.1",
  "org.scalikejdbc" %% "scalikejdbc-play-initializer" % "3.0.1-scalikejdbc-4.3",

  logger_dependency,

  //dependencies for google cloud storage to save CSV to gcp bucket
  ("com.google.cloud" % "google-cloud-storage" % "2.4.2"),

  // dependencies for google cloud logging
  // REF: https://cloud.google.com/logging/docs/setup/java
  ("com.google.cloud" % "google-cloud-logging-logback" % "0.123.4-alpha"),
  ("com.google.cloud" % "google-cloud-logging" % "3.6.4"),



  // 21 March 2022: this was the old redis module used in play 2.5
  // "com.typesafe.play.modules" %% "play-modules-redis" % "2.5.0",

  // added on 22 Sep 2021 - CacheServiceJedis
  "redis.clients" % "jedis" % "3.6.3",



  "com.github.tototoshi" %% "scala-csv" % "1.3.10",

  "com.univocity" % "univocity-parsers" % "2.9.1",

  // tika is used to detect csv encoding when users upload csv
//  "org.apache.tika" % "tika-parsers" % "1.21"
//    exclude("com.google.protobuf", "protobuf-java")
//    exclude("org.apache.cxf", "cxf-rt-rs-client")
//    exclude("com.sun.activation", "jakarta.activation")
//    exclude("org.glassfish.jaxb", "jaxb-runtime")
//    exclude("org.ow2.asm", "asm")
//    exclude("org.bouncycastle", "bcmail-jdk15on")
//    exclude("org.bouncycastle", "bcprov-jdk15on")
//    exclude("org.bouncycastle", "bcpkix-jdk15on"),

  "com.rabbitmq" % "amqp-client" % "4.0.0",

  "org.antlr" % "ST4" % "4.0.8",


  // for authentication with play-silhouette library
  "de.svenkubiak" % "jBCrypt" % "0.4.1",

  "com.iheart" %% "ficus" % "1.5.2",

  //  "org.scalatestplus.play" %% "scalatestplus- play" % "2.0.0" % Test,
  // "org.scalatestplus.play" % "scalatestplus-play_2.11" % "2.0.0" % Test,

  "com.lihaoyi" %% "pprint" % "0.9.0",

  // https://mvnrepository.com/artifact/commons-validator/commons-validator
  "commons-validator" % "commons-validator" % "1.7",

  // used to valid mobile numbers in user profiles
  "com.googlecode.libphonenumber" % "libphonenumber" % "8.13.18",

  //owasp html sanitizer
  "com.googlecode.owasp-java-html-sanitizer" % "owasp-java-html-sanitizer" % "20211018.2",

  // for google authenticator 2fa
  "com.warrenstrange" % "googleauth" % "1.4.0",

  apache_commons_dependency, // used in Template renderer to unescape custom_fields jsvalue

  // https://mvnrepository.com/artifact/nl.big-o/liqp
  /**
   * NOTE (18th June 2019) on liqp
   *
   * the newer versions switch to antlr4, which makes it difficult for us to find params used in the template for marking missing-merge tags
   *
   * we need to track all params for missing-merge tags, as well as allow the "default" filter for existing accounts
   *
   * one option was to enable strictVariables, but in that case, it was ignoring the "default" filter and throwing exceptions even when a default value was there
   */
  "nl.big-o" % "liqp" % "0.7.3",

  //required for DKIM sign message (mail)
//  ("net.markenwerk" % "utils-mail-dkim" % "1.4.0"),

  // required for checking CName for custom tracking domain
  "dnsjava" % "dnsjava" % "2.1.8",

  // https://mvnrepository.com/artifact/io.lemonlabs/scala-uri
  "io.lemonlabs" %% "scala-uri" % "4.0.3",

  "com.pusher" % "pusher-http-java" % "1.3.3",

  // "org.apache.kafka" % "kafka-clients" % "3.2.0",
  // Ref to Blog: https://github.com/FrodeRanders/scylladb-demo
  //  "com.datastax.cassandra" % "cassandra-driver-core" % "3.11.1"
  // "com.scylladb" % "scylla-driver-core" % "3.11.2.0",

  // https://github.com/ksuid/ksuid
  "com.github.ksuid" % "ksuid" % "1.1.2",


  "io.prometheus" % "simpleclient" % "0.16.0",
  "io.prometheus" % "simpleclient_hotspot" % "0.16.0",
  "io.prometheus" % "simpleclient_httpserver" % "0.16.0",
  "io.prometheus" % "simpleclient_pushgateway" % "0.16.0",

  // twilio for call / sms
  // https://www.twilio.com/blog/2017/11/getting-started-with-scala-and-twilio.html
  "com.twilio.sdk" % "twilio" % "9.2.5",

  // facing this issue when i updated version of com.twilio.sdk from 7.15.5 to 8.11.0
  // : https://github.com/FasterXML/jackson-module-scala/issues/513
  "com.fasterxml.jackson.module" %% "jackson-module-scala" % "2.14.2",


  ) ++ scalaTestDependencies

).map(_.exclude("commons-logging", "commons-logging"))

/*
val wartRemoverErrorsList =  Warts.allBut(
  Wart.PlatformDefault,
  Wart.PublicInference,
  Wart.ImplicitParameter,
  Wart.StringPlusAny,
  Wart.Enumeration, // definitely bring this back
  Wart.DefaultArguments,
  Wart.AsInstanceOf,
  Wart.Any, // definitely bring this back
  Wart.AnyVal, // fixme: definitely bring this back First
  Wart.IterableOps,
  Wart.FinalCaseClass,
  Wart.Equals, // definitely bring this back
  Wart.TryPartial,
  Wart.OptionPartial,
  Wart.Overloading,
  Wart.Null,
  Wart.NonUnitStatements,
  Wart.Nothing,
  Wart.ExplicitImplicitTypes, // bring this back
  Wart.Var,

Wart.ToString,
Wart.Throw, // should be brought back
Wart.JavaSerializable, // should be brought back
Wart.Option2Iterable,
Wart.ListAppend,
Wart.GetOrElseNull,
Wart.SizeIs,
Wart.LeakingSealed, // bring this back
Wart.GlobalExecutionContext, // definitely bring this back
Wart.ThreadSleep,
Wart.While,
Wart.Product, // bring this back
Wart.Serializable, // bring this back
Wart.RedundantConversions, // bring this back
Wart.FinalVal, // bring this back
Wart.ImplicitConversion,
Wart.Recursion,
Wart.NoNeedImport, // bring this back
Wart.TripleQuestionMark,
Wart.ForeachEntry, // bring this back
Wart.MutableDataStructures, // bring this back
Wart.IsInstanceOf, // bring this back
Wart.SeqApply, // bring this back
Wart.SizeToLength, // bring this back
Wart.MapUnit, // bring this back
Wart.JavaNetURLConstructors, // bring this back
Wart.SeqUpdated, // bring this back
)
*/

val sr_organization = "com.smartreach"
val sr_organization_name = "smartreach"

val sr_api_layer_version = "0.0.3-dev"


val sr_common2_version = "3.2.0"
val sr_companies_api_version = "3.0.0"
val sr_scheduler_version = "3.0.0"
val sr_email_service_providers_version = "3.2.3"
val sr_billing_common_version = "3.2.6"
val sr_llm_service_version = "3.2.0"
val sr_api_utils_version = "3.2.0"
val sr_common_auth_version = "3.2.0"
val sr_logger_version= "3.2.3"


val prodRegistryBase = "artifactregistry://us-central1-maven.pkg.dev"

def prodRegistry(packageName: String) = {
  s"${packageName}-prod" at s"${prodRegistryBase}/smrtcloud/$packageName/"
}

def stagingRegistry(packageName: String) = {
 s"${packageName}-staging" at s"${prodRegistryBase}/sr-staging-341312/${packageName}/"
}

val sr_scheduler_resolver_prod = prodRegistry("sr-scheduler")
val sr_scheduler_resolver_staging = stagingRegistry("sr-scheduler")

val sr_logger_resolver_prod =  prodRegistry("sr-logger")
val sr_logger_resolver_staging =  stagingRegistry("sr-logger")

val sr_common2_resolver_prod =  prodRegistry("sr-common2")
val sr_common2_resolver_staging =  stagingRegistry("sr-common2")

val sr_email_service_providers_resolver_prod = prodRegistry("sr-email-service-providers")
val sr_email_service_providers_resolver_staging = stagingRegistry("sr-email-service-providers")

val sr_billing_common_resolver_prod = prodRegistry("sr-billing-common")
val sr_billing_common_resolver_staging = stagingRegistry("sr-billing-common")

val sr_llm_service_resolver_prod = prodRegistry("sr-llm-service")
val sr_llm_service_resolver_staging = stagingRegistry("sr-llm-service")

val sr_companies_api_resolver_prod = prodRegistry("sr-companies-api")
val sr_companies_api_resolver_staging = stagingRegistry("sr-companies-api")

val sr_api_utils_resolver_prod = prodRegistry("sr-api-utils")
val sr_api_utils_resolver_staging = stagingRegistry("sr-api-utils")

val sr_common_auth_resolver_prod = prodRegistry("sr-common-auth")
val sr_common_auth_resolver_staging = stagingRegistry("sr-common-auth")

val sr_scheduler_dependency = "com.smartreach" %% "sr_scheduler" % sr_scheduler_version
val sr_logger_dependency = "com.smartreach" %% "sr_logger" % sr_logger_version
val sr_common2_dependency ="com.smartreach" %% "common2" % sr_common2_version
val sr_email_service_providers_dependency = "com.smartreach" %% "sr_email_service_providers" % sr_email_service_providers_version
val sr_billing_common_dependency = "com.smartreach" %% "sr_billing_common" % sr_billing_common_version
val sr_llm_service_dependency = "com.smartreach" %% "sr_llm_service" % sr_llm_service_version
val sr_companies_api_dependency = "com.smartreach" %% "sr_companies_api" % sr_companies_api_version
val sr_api_utils_dependency = "com.smartreach" %% "sr_api_utils" % sr_api_utils_version
val sr_common_auth_dependency = "com.smartreach" %% "sr_common_auth" % sr_billing_common_version

lazy val sr_logger_dependencies = Seq(
  "org.playframework" %% "play-ws" % "3.0.6",
  logger_dependency
)

lazy val sr_logger = project
  .settings(
    name := "sr_logger",
    // scalaVersion := "3.2.1",
    organization := sr_organization,
    organizationName := sr_organization_name,
    version := sr_logger_version,
    scalaVersion := defaultScalaVersion,
    settings,
    libraryDependencies ++= sr_logger_dependencies,
    resolvers ++= Seq(
      sr_logger_resolver_prod,
      sr_logger_resolver_staging

    ),

    publishResolvers := Seq(
      sr_logger_resolver_prod,
      sr_logger_resolver_staging
    ),
    publish := publishAll.value,


  )



lazy val commmon2_dependencies = Seq(

  "com.typesafe" % "config" % "1.4.3",

  "org.playframework" %% "play-json" % "3.0.4",


  "redis.clients" % "jedis" % "3.6.3",




  // tika is used to detect csv encoding when users upload csv
  "org.apache.tika" % "tika-parsers" % "1.21"
    exclude("com.google.protobuf", "protobuf-java")
    exclude("org.apache.cxf", "cxf-rt-rs-client")
    exclude("com.sun.activation", "jakarta.activation")
    exclude("org.glassfish.jaxb", "jaxb-runtime")
    exclude("org.ow2.asm", "asm")
    exclude("org.bouncycastle", "bcmail-jdk15on")
    exclude("org.bouncycastle", "bcprov-jdk15on")
    exclude("org.bouncycastle", "bcpkix-jdk15on"),




).map(_.exclude("commons-logging", "commons-logging"))


lazy val common2 = project
  /* comment .dependsOn for Production, use it only for local testing */
//      .dependsOn(
//        sr_logger,
//      )
  .settings(
    name := "common2",
    //REF: https://stackoverflow.com/questions/********/in-play-2-framework-how-can-i-include-git-commit-sha-in-sbt-dist-package-name
    //Appending latest git commit id and time to build version
    organization := sr_organization,
    organizationName := sr_organization_name,
    version := sr_common2_version,
    scalaVersion := defaultScalaVersion,
    settings,
    // Seq(scalacOptions ++= Seq(tastyReaderCompilerOption)),
    assemblySettings,
    publishResolvers := Seq(
      sr_common2_resolver_prod,
      sr_common2_resolver_staging
    ),
    publish := publishAll.value,
    resolvers ++= Seq(
      sr_logger_resolver_prod,
      sr_logger_resolver_staging,

      sr_common2_resolver_prod,
      sr_common2_resolver_staging
    ),
    libraryDependencies ++= commmon2_dependencies
      /* uncomment the sr_logger_dependency for production , comment it only for testing */
      ++ Seq(sr_logger_dependency )
  )



lazy val sr_scheduler = project
  /* comment .dependsOn for Production, use it only for local testing */
  .dependsOn(
//            sr_logger,
    common2
  )
  .settings(
    name := "sr_scheduler",
    organization := sr_organization,
    organizationName := sr_organization_name,
    version := sr_scheduler_version,
    // scalaVersion := "3.2.1",
    scalaVersion := defaultScalaVersion,
    settings,
    publishResolvers := Seq(
      sr_scheduler_resolver_prod,
      sr_scheduler_resolver_staging
    ),
    publish:= publishAll.value,
    resolvers ++= Seq(
      sr_logger_resolver_prod,
      sr_logger_resolver_staging,

      sr_scheduler_resolver_prod,
      sr_scheduler_resolver_staging,
    ),
    libraryDependencies ++= Seq(
      jodaTime,
    )
      /* uncomment the sr_logger_dependency for production , comment it only for testing */
      ++ Seq(sr_logger_dependency,
      //     sr_common2_dependency
    )
  )



addCompilerPlugin("org.psywerx.hairyfotr" %% "linter" % "0.1.17")


lazy val sr_account_service = project
  .settings(
    scalaVersion := defaultScalaVersion,
    libraryDependencies ++= Seq(
      playJsonDefault,
    )
  )


lazy val sr_call_service = project
  .dependsOn(
    api_layer_currency,
    sr_account_service,
  )
  .settings(
    scalaVersion := defaultScalaVersion,
    libraryDependencies ++= Seq(

    )
  )


lazy val coldemail = project
  /* comment .dependsOn for Production, use it only for local testing */
  .dependsOn(
    common2,
    api_layer_currency,
    sr_dns_utils,
    sr_scheduler,
    sr_api_utils,
    sr_common_auth,
    sr_account_service,
    sr_call_service,

    /* comment-out the following dependencies in .dependsOn for Production, use them only for local testing */
//        sr_logger,
//        sr_email_service_providers,
//        sr_companies_api,
//        sr_billing_common,
//        sr_llm_service,
  )
  .enablePlugins(PlayScala)
  .disablePlugins(PlayLayoutPlugin)
  .settings(
    name := "coldemail",
    //REF: https://stackoverflow.com/questions/********/in-play-2-framework-how-can-i-include-git-commit-sha-in-sbt-dist-package-name
    //Appending latest git commit id and time to build version
    version := {
      // will give time in this format : 2022-10-17T13:43:41-in-tz-+0530
      // the in tz is to indicate the timezone it was run in
      // because in the format options i didnt see how to get the utc time
      val build_time = ("date +\"%Y-%m-%dT%T-in-tz-%z\"" #| "tr ':' '_'").!!.trim
      val commit_id = "git rev-parse --short HEAD".!!.trim
      // git show with %cI will give time in this format 2022-10-17T09:58:24+05:30 - without spaces
      val commit_time = ("git show -s --format=%cI" #| "tr ':' '_'").!!.trim
      println("1.0-SNAPSHOT-" + build_time +"--" + commit_id + "--" + commit_time)
      "1.0-SNAPSHOT-" + build_time +"----" + commit_id + "-" + commit_time
    },
    scalaVersion := defaultScalaVersion,
    // wartremoverErrors ++= wartRemoverErrorsList ,
    coldemailSettings,
    assemblySettings,
    resolvers ++= Seq(
      sr_logger_resolver_prod,
      sr_logger_resolver_staging,

      sr_email_service_providers_resolver_prod,
      sr_email_service_providers_resolver_staging,

      sr_companies_api_resolver_prod,
      sr_companies_api_resolver_staging,

      sr_billing_common_resolver_prod,
      sr_billing_common_resolver_staging,

      sr_llm_service_resolver_prod,
      sr_llm_service_resolver_staging,

    ),

    libraryDependencies ++= coldemailDependencies
      /* uncomment the below for production , comment it only for testing */
      ++ Seq(
        sr_logger_dependency,
  //      sr_common2_dependency,
        sr_email_service_providers_dependency,
        sr_companies_api_dependency,
        sr_billing_common_dependency,

        sr_llm_service_dependency,
    )
  )


lazy val sr_email_service_providers = project
  /* comment .dependsOn for Production, use it only for local testing */
  /*    .dependsOn(
      sr_logger,
    common2
    )*/
  .disablePlugins(PlayScala)
  .disablePlugins(PlayLayoutPlugin)
  .settings(
    organization := sr_organization,
    organizationName := sr_organization_name,
    version := sr_email_service_providers_version,
    scalaVersion := defaultScalaVersion,
    // wartremoverErrors ++= wartRemoverErrorsList,
    coldemailSettings,
    assemblySettings,
    publishResolvers := Seq(
      sr_email_service_providers_resolver_prod,
      sr_email_service_providers_resolver_staging
    ),
    publish:= publishAll.value,
    resolvers ++= Seq(
      sr_logger_resolver_prod,
      sr_logger_resolver_staging,

      sr_common2_resolver_prod,
      sr_common2_resolver_staging,

      sr_email_service_providers_resolver_prod,
      sr_email_service_providers_resolver_staging,

    ),
    libraryDependencies ++= (Seq(


      //  "javax.mail" % "javax.mail-api" % "1.6.0",
      "com.sun.mail" % "jakarta.mail" % "1.6.7",

      //  "com.sun.mail" % "javax.mail" % "1.5.6",

      apache_commons_dependency,
      "org.apache.tika" % "tika-parsers" % "1.21"
        exclude("com.google.protobuf", "protobuf-java")
        exclude("org.apache.cxf", "cxf-rt-rs-client")
        exclude("com.sun.activation", "jakarta.activation")
        exclude("org.glassfish.jaxb", "jaxb-runtime")
        exclude("org.ow2.asm", "asm")
        exclude("org.bouncycastle", "bcmail-jdk15on")
        exclude("org.bouncycastle", "bcprov-jdk15on")
        exclude("org.bouncycastle", "bcpkix-jdk15on"),

      "com.google.api-client" % "google-api-client" % "1.35.2",
      "com.google.apis" % "google-api-services-gmail" % "v1-rev110-1.25.0",
      jodaTime,
      ("net.markenwerk" % "utils-mail-dkim" % "1.4.0"),
      "org.playframework" %% "play-ws" % "3.0.6",
      "org.playframework" %% "play-json-joda" % "3.0.4",

      "org.jsoup" % "jsoup" % "1.15.4",
    )
      /* uncomment the below for production , comment it only for testing */
      ++ Seq(
      sr_logger_dependency,
      sr_common2_dependency

    )).map(_.exclude("commons-logging", "commons-logging"))
      ++ scalaTestDependencies
  )


lazy val sr_companies_api = project
  /* comment .dependsOn for Production, use it only for local testing */
//    .dependsOn(
//      sr_logger,
//    )
  .disablePlugins(PlayScala)
  .disablePlugins(PlayLayoutPlugin)
  .settings(
    organization := sr_organization,
    organizationName := sr_organization_name,
    version := sr_companies_api_version,
    scalaVersion := defaultScalaVersion,
    // wartremoverErrors ++= wartRemoverErrorsList,
    coldemailSettings,
    assemblySettings,
    publishResolvers := Seq(
      sr_companies_api_resolver_prod,
      sr_companies_api_resolver_staging
    ),
    publish:= publishAll.value,
    resolvers ++= Seq(
      sr_logger_resolver_prod,
      sr_logger_resolver_staging

    ),
    libraryDependencies ++= Seq(

      "org.playframework" %% "play-ws" % "3.0.6",
      "org.playframework" %% "play-json-joda" % "3.0.4",
    )
      /* uncomment the below for production , comment it only for testing */
      ++ Seq(sr_logger_dependency)
  )



lazy val api_layer_currency = project
  /* comment .dependsOn for Production, use it only for local testing */
//    .dependsOn(
//      sr_logger,
//    )
  .disablePlugins(PlayScala)
  .disablePlugins(PlayLayoutPlugin)
  .settings(
    //REF: https://stackoverflow.com/questions/********/in-play-2-framework-how-can-i-include-git-commit-sha-in-sbt-dist-package-name
    //Appending latest git commit id and time to build version
    organization := sr_organization,
    organizationName := sr_organization_name,
    version := sr_api_layer_version,
    scalaVersion := defaultScalaVersion,
    // wartremoverErrors ++= wartRemoverErrorsList,
    coldemailSettings,
    assemblySettings,
    //    publishTo := Some(sr_common2_resolver_prod),
    resolvers ++= Seq(
      sr_logger_resolver_prod,
      sr_logger_resolver_staging


    ),
    libraryDependencies ++= Seq(
      ws,


      "org.playframework" %% "play-ws" % "3.0.6",
      "org.playframework" %% "play-json" % "3.0.4",

    )
    /* uncomment the sr_logger_dependency for production , comment it only for testing */
              ++ Seq(sr_logger_dependency )

  )



lazy val sr_dns_utils = project
  /* comment .dependsOn for Production, use it only for local testing */
      .dependsOn(
    common2,
//  sr_logger,
      )
  .settings(
    //REF: https://stackoverflow.com/questions/********/in-play-2-framework-how-can-i-include-git-commit-sha-in-sbt-dist-package-name
    //Appending latest git commit id and time to build version
    organization := sr_organization,
    organizationName := sr_organization_name,
    version := sr_api_layer_version,
    scalaVersion := defaultScalaVersion,
    // wartremoverErrors ++= wartRemoverErrorsList,
    coldemailSettings,
    assemblySettings,
    //    publishTo := Some(sr_common2_resolver_prod),
    resolvers ++= Seq(
      sr_logger_resolver_prod,
      sr_logger_resolver_staging

    ),
    libraryDependencies ++= Seq(
      ws,
      jodaTime,

      "org.playframework" %% "play-ws" % "3.0.6",
      "org.playframework" %% "play-json" % "3.0.4",

    )
      /* uncomment the sr_logger_dependency for production , comment it only for testing */
      ++ Seq(sr_logger_dependency )

  )




lazy val sr_api_utils = project
  /* comment .dependsOn for Production, use it only for local testing */
//  .dependsOn(
//    sr_logger,
//    common2
//  )
  .disablePlugins(PlayScala)
  .disablePlugins(PlayLayoutPlugin)
  .settings(
    organization := sr_organization,
    organizationName := sr_organization_name,
    version := sr_api_utils_version,
    scalaVersion := defaultScalaVersion,
    // wartremoverErrors ++= wartRemoverErrorsList,
    publishResolvers := Seq(
      sr_api_utils_resolver_prod,
      sr_api_utils_resolver_staging
    ),
    publish := publishAll.value,
    resolvers ++= Seq(
      sr_logger_resolver_prod,
      sr_logger_resolver_staging,

      sr_api_utils_resolver_prod,
      sr_api_utils_resolver_staging,

      sr_common2_resolver_prod,
      sr_common2_resolver_staging

    ),
    libraryDependencies ++= Seq(


      "org.playframework" %% "play-ws" % "3.0.6",

    )
      /* uncomment the below for production , comment it only for testing */
      ++ Seq(
      sr_logger_dependency,
      sr_common2_dependency

    )
  )


lazy val sr_common_auth = project
  /* comment .dependsOn for Production, use it only for local testing */
//  .dependsOn(
//     sr_logger,
//    common2,
//    sr_api_utils
//  )
  .disablePlugins(PlayScala)
  .disablePlugins(PlayLayoutPlugin)
  .settings(
    organization := sr_organization,
    organizationName := sr_organization_name,
    version := sr_common_auth_version,
    scalaVersion := defaultScalaVersion,
    // wartremoverErrors ++= wartRemoverErrorsList,
    publishResolvers := Seq(
      sr_common_auth_resolver_prod,
      sr_common_auth_resolver_staging
    ),
    publish := publishAll.value,
    resolvers ++= Seq(
      sr_logger_resolver_prod,
      sr_logger_resolver_staging,

      sr_api_utils_resolver_prod,
      sr_api_utils_resolver_staging,

      sr_common2_resolver_prod,
      sr_common2_resolver_staging

    ),
    libraryDependencies ++= Seq(


      "org.playframework" %% "play-ws" % "3.0.6",
      "org.playframework" %% "play-json-joda" % "3.0.4",
      "io.lemonlabs" %% "scala-uri" % "4.0.3",


    )
      /* uncomment the below for production , comment it only for testing */
      ++ Seq(
      sr_logger_dependency,
      sr_common2_dependency,
      sr_api_utils_dependency
    )
  )

lazy val sr_billing_common = project
  /* comment .dependsOn for Production, use it only for local testing */
//   .dependsOn(
//    sr_logger
//   )
  .disablePlugins(PlayScala)
  .disablePlugins(PlayLayoutPlugin)
  .settings(
    organization := sr_organization,
    organizationName := sr_organization_name,
    version := sr_billing_common_version,
    scalaVersion := defaultScalaVersion,
    // wartremoverErrors ++= wartRemoverErrorsList,
    coldemailSettings,
    assemblySettings,
    publishResolvers := Seq(
      sr_billing_common_resolver_prod,
      sr_billing_common_resolver_staging
    ),
    publish := publishAll.value,
    resolvers ++= Seq(
      sr_logger_resolver_prod,
      sr_logger_resolver_staging

    ),
    libraryDependencies ++= Seq(

      jodaTime,


      "org.playframework" %% "play-ws" % "3.0.6",
      "org.playframework" %% "play-json-joda" % "3.0.4",

    )
      ++ scalaTestDependencies

      /* uncomment the below for production , comment it only for testing */
      ++ Seq(sr_logger_dependency)
  )

lazy val sr_llm_service_dependencies = Seq(

  jodaTime,


  "org.playframework" %% "play-ws" % "3.0.6",
  "org.playframework" %% "play-json-joda" % "3.0.4",

  "software.amazon.awssdk" % "bedrock" % "2.26.22",
  "software.amazon.awssdk" % "bedrockruntime" % "2.25.52",


).map(_.exclude("commons-logging", "commons-logging"))


lazy val sr_llm_service = project
  /* comment .dependsOn for Production, use it only for local testing */
//     .dependsOn(
//      sr_logger
//     )
  .disablePlugins(PlayScala)
  .disablePlugins(PlayLayoutPlugin)
  .settings(
    organization := sr_organization,
    organizationName := sr_organization_name,
    version := sr_llm_service_version,
    scalaVersion := defaultScalaVersion,
    // wartremoverErrors ++= wartRemoverErrorsList,
    coldemailSettings,
    assemblySettings,
    publishResolvers := Seq(
      sr_llm_service_resolver_prod,
      sr_llm_service_resolver_staging
    ),
    publish := publishAll.value,
    resolvers ++= Seq(
      sr_logger_resolver_prod,
      sr_logger_resolver_staging
    ),
    libraryDependencies ++= sr_llm_service_dependencies

      /* uncomment the below for production , comment it only for testing */
      ++ Seq(sr_logger_dependency)
  )



/*
lazy val sendworker = project
  .enablePlugins(PlayScala)
  .disablePlugins(PlayLayoutPlugin)
  .settings(
    name := "sendworker",
    scalaVersion := "2.12.16",
    settings,
    assemblySettings,
    libraryDependencies ++= dependencies // ++ Seq(dependencies.monocleCore, dependencies.monocleMacro)
  )
*/

lazy val settings =
  commonSettings ++ Seq(scalacOptions ++= commonCompilerOptions) //++ wartremoverSettings

// scala3 dependency
lazy val coldemailSettings =
  commonSettings ++ Seq(scalacOptions ++= coldemailCompilerOptions) //++ wartremoverSettings

lazy val commonSettings = Seq(

  resolvers ++= Seq(
    // "Scalaz Bintray Repo" at "https://dl.bintray.com/scalaz/releases",
    "Atlassian Releases" at "https://maven.atlassian.com/public/",
    "Sonatype OSS Snapshots" at "https://oss.sonatype.org/content/repositories/snapshots/",

    // REF: https://github.com/travis-ci/travis-ci/issues/5734
    //  "Artima Maven Repository" at "http://repo.artima.com/releases",

    // REF: https://github.com/playframework/play-plugins/tree/master/redis#how-to-install
    "google-sedis-fix" at "https://pk11-scratch.googlecode.com/svn/trunk",
    Resolver.jcenterRepo
  )
)

lazy val commonCompilerOptions = Seq(
  "-unchecked",
  "-feature",
  // "-language:existentials",
  // "-language:higherKinds",
//  "-language:implicitConversions",
  "-language:postfixOps",
  "-deprecation",
  "-encoding", "utf8",
  "-explaintypes", // Explain type errors in more detail.
  "-noindent",
//  "-Ycheck:all",
//  "-source:3.0-migration"

  // commenting out werror on 29-Jan-2024, so we can deploy a hotfix
//  "-Werror", // Fail the compilation if there are any warnings.

)

// scala3
lazy val coldemailCompilerOptions = commonCompilerOptions ++ Seq(
//  tastyReaderCompilerOption,
//  "-Ystatistics",
//  "-Xprint:typer",
  // removeUnused, // TODO: need to uncomment this
  // xlintUnused, // TODO: need to uncomment this
)

/*
lazy val wartremoverSettings = Seq(
  //wartremoverWarnings in (Compile, compile) ++= Warts.allBut(Wart.Throw)
)

 */

lazy val assemblySettings = Seq(
  assembly / logLevel := Level.Error,

  // assemblyJarName in assembly := "coldemail.jar"
  assembly / assemblyJarName := name.value + ".jar",

  //assembly / test := (Test / test).value, // run tests during assembly
  // suggested by grok to preven tests from running again after separate test phase
  // in build-upload-v3.yml , because -DskipTests does not override it
  assembly / test := {},

  assemblyRepeatableBuild := false,

  /*
  // from sbt-multi-project-example
  assemblyMergeStrategy in assembly := {
    case PathList("META-INF", xs @ _*) => MergeStrategy.discard
    case "application.conf"            => MergeStrategy.concat
    case x =>
      val oldStrategy = (assemblyMergeStrategy in assembly).value
      oldStrategy(x)
  }
   */
  assembly / assemblyMergeStrategy := {

    // --- Most important rule: Concatenate all META-INF/services files ---
    // This should correctly handle io.jsonwebtoken.io.Serializer and others.
    case PathList("META-INF", "services", xs @ _*) =>
      // You can add a println here to see which files are being concatenated:
      // println(s"ASSEMBLY: Concatenating service file: META-INF/services/${xs.mkString("/")}")
      MergeStrategy.concat

    // --- Discard common unnecessary META-INF files (comes AFTER services rule) ---
    case PathList("META-INF", "MANIFEST.MF") => MergeStrategy.discard
    case PathList("META-INF", "INDEX.LIST") => MergeStrategy.discard
    // Be careful with wholesale NOTICE/LICENSE discard if any are required for compliance.
    // case PathList("META-INF", "NOTICE" | "LICENSE" | "DEPENDENCIES" | "README") => MergeStrategy.discard
    case PathList("META-INF", name) if name.toLowerCase.endsWith(".sf") || name.toLowerCase.endsWith(".dsa") || name.toLowerCase.endsWith(".rsa") =>
      MergeStrategy.discard

    //  case PathList("META-INF", xs@_*) => MergeStrategy.discard

    /**
      * Added on 23th Aug 2024, to fix the following error that was showing up:
      *
      * [error] 1 error(s) were encountered during the merge:
      * [error] java.lang.RuntimeException:
      * [error] Deduplicate found different file contents in the following:
      * [error]   Jar name = jackson-core-2.14.3.jar, jar org = com.fasterxml.jackson.core, entry target = META-INF/FastDoubleParser-LICENSE
      * [error]   Jar name = third-party-jackson-core-2.27.10.jar, jar org = software.amazon.awssdk, entry target = META-INF/FastDoubleParser-LICENSE
      */
    case x if x.endsWith("META-INF/FastDoubleParser-LICENSE") => MergeStrategy.last
    case x if x.endsWith("META-INF/FastDoubleParser-NOTICE") => MergeStrategy.last

    /**
     * 14th October 2024:
     * clashing during assembly:
     *
     * [error] 1 error(s) were encountered during the merge:
     *  [error] java.lang.RuntimeException:
     *  [error] Deduplicate found different file contents in the following:
     *  [error]   Jar name = gimap-1.6.7.jar, jar org = com.sun.mail, entry target = META-INF/javamail.providers
     *  [error]   Jar name = imap-1.6.7.jar, jar org = com.sun.mail, entry target = META-INF/javamail.providers
     */
    case x if x.endsWith("META-INF/javamail.providers") => MergeStrategy.last

    // these two were added on 28-March-2022 when outlook sending failed on play 2.7.0 branch
    case x if x.endsWith("mailcap.default") => MergeStrategy.last
    case x if x.endsWith("mimetypes.default") => MergeStrategy.last

    case x if x.endsWith(".properties") => MergeStrategy.last

//    case x if x.startsWith("io/jsonwebtoken") => MergeStrategy.last


    /**
    Added 22 Mar 2022: while migrating to play 2.6, the assembly was failing with this error:

      [error] (coldemail/ *:assembly) deduplicate: different file contents found in the following:
      [error] /Users/<USER>/.ivy2/cache/com.typesafe.play/play-ws_2.11/jars/play-ws_2.11-2.6.0.jar:play/api/libs/ws/package$.class
      [error] /Users/<USER>/.ivy2/cache/com.typesafe.play/play-ws-standalone_2.11/jars/play-ws-standalone_2.11-1.0.0.jar:play/api/libs/ws/package$.class
      [error] deduplicate: different file contents found in the following:
      [error] /Users/<USER>/.ivy2/cache/com.typesafe.play/play-ws_2.11/jars/play-ws_2.11-2.6.0.jar:play/api/libs/ws/package.class
      [error] /Users/<USER>/.ivy2/cache/com.typesafe.play/play-ws-standalone_2.11/jars/play-ws-standalone_2.11-1.0.0.jar:play/api/libs/ws/package.class
      [error] deduplicate: different file contents found in the following:
      [error] /Users/<USER>/.ivy2/cache/com.typesafe.play/play_2.11/jars/play_2.11-2.6.0.jar:play/reference-overrides.conf
      [error] /Users/<USER>/.ivy2/cache/com.typesafe.play/play-akka-http-server_2.11/jars/play-akka-http-server_2.11-2.6.0.jar:play/reference-overrides.conf

     */
    case x if (
      // x.startsWith("com/typesafe/play") && // was not working on this check
      x.contains("play") && // was not working on this check
        (
          x.endsWith("package$.class") ||
            x.endsWith("package.class") ||
            x.endsWith("reference-overrides.conf")
          )
      ) => MergeStrategy.last


    /*
    Added on 26th July 2022, to fix the following error that was showing up:

      [error] 1 error was encountered during merge
      [debug] java.lang.RuntimeException: deduplicate: different file contents found in the following:
      [debug] /Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/play/shaded-asynchttpclient/2.1.7/shaded-asynchttpclient-2.1.7.jar:META-INF/native-image/io.netty/transport/reflection-config.json
      [debug] /Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/netty/netty-transport/4.1.75.Final/netty-transport-4.1.75.Final.jar:META-INF/native-image/io.netty/transport/reflection-config.json
     */

    case x if x.endsWith("/native-image/io.netty/transport/reflection-config.json") =>
      MergeStrategy.last

    case x if x.contains("javax") && x.contains("activation") => MergeStrategy.first

    case PathList("org", "bouncycastle", xs@_*) => MergeStrategy.last

    // added this on 28-Mar-2022 to handle duplicate conflicts across google/protobuf dependencies
    case x if x.contains("google/protobuf") => MergeStrategy.last

    // REF: https://stackoverflow.com/a/55557287
    case x if x.endsWith("module-info.class") => MergeStrategy.discard


    case x =>
      val oldStrategy = (assembly / assemblyMergeStrategy).value
      oldStrategy(x)
  },

  assembly / assemblyOption := (assembly / assemblyOption).value.withCacheOutput(false)

)

// dummy line to retrigger tests - 5
