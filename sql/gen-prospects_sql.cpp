
#include <iostream>
#include <string>
#include <ctime>
#include <cstdlib>
#include <vector>

void print_prospects_sql(int email_validation_start_id, int email_start_id) {
	using namespace std;
	string prospects_sql = 
"insert into prospects (first_name, last_name, email, created_at,\
        	account_id, custom_fields, list, company, city, country,\
        	timezone, email_format_valid, email_checked, email_checked_at, \
 	email_did_you_mean, email_user, email_domain,\
        	email_mx_found, email_smtp_check, email_catch_all, email_generic_role, email_disposable, email_free, email_score,\
        	team_id, added_by, email_sent_for_validation, email_sent_for_validation_at, ta_id,\
        	prospect_category_updated_at, prospect_category_updated_manually, invalid_email,\
        	last_contacted_at, last_replied_at, will_delete, will_delete_at,\
        	email_bounced, email_bounced_campaign, email_bounced_at, list_id,\
        	total_opens, total_clicks, first_contacted_at, last_opened_at, last_clicked_at,\
        	force_send_invalid_email, force_send_invalid_email_at, email_soft_bounced, email_soft_bounced_at,\
        	prospect_category_id_custom, updated_at, prospect_source, prospect_account_id,\
        	email_bounced_earlier, is_updated_internal, state, job_title, phone, linkedin_url,\
        	email_validation_id, email_bounce_type, email_bounce_reason) values\
 ('prateek','Samant','<EMAIL>','2021-08-04 20:09:00.77+05:30',\
  '1','{}','neils first local campaign','','','',\
  null,'t','t','2021-08-04 20:10:20.761069+05:30',\
  null,'prateek','heaplabs.com',\
  null,null,null,null,null,null,null,\
  '1','1','t','2021-08-04 20:09:00.925572+05:30','1',\
  '2021-08-04 20:09:00.740366+05:30','f','f',\
  '2021-08-05 21:03:51.098443+05:30',null,'f',null,\
  'f',null,null,'1',\
  '0','0','2021-08-05 21:03:51.098443+05:30',null,null,\
  'f',null,'f',null,\
  null,'2021-09-03 11:02:17.820268+05:30',null,null,\
  null,null,'','','','',\
  %d,null,null);\n";
 	for (int i = 0; i < 100; ++i) {
		printf(prospects_sql.c_str(), email_start_id + i,
			       email_validation_start_id + i);
	}
}

int main(int argc, char *  argv[]) {
	using namespace std;
	if (argc !=  3) {
		cerr << "gen-prospects <email_validation_start_id> <email_start_id>" << endl;
		exit(1);
	}
	int email_validation_start_id = atoi(argv[1]);
	int email_start_id = atoi(argv[2]);
	cout << "-- email_validations start_id: " << email_validation_start_id
		<< " email_id start_id: " << email_start_id
	       	<< endl;
	print_prospects_sql(email_validation_start_id, email_start_id);
}

