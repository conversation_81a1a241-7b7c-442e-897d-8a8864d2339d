#include <iostream>
#include <string>
#include <ctime>
#include <cstdlib>
#include <vector>


void print_email_validation_sql(int acc_id, int team_id, int email_start_index) {
	using namespace std;
	string email_validation_sql = 
"insert into email_validations (email, checked_via_tool, checked_at, \
	full_result, \
	is_valid, initiator_account_id,\
	initiator_team_id) values (\
	'<EMAIL>', 'bcr', '%d-sep-2021 %d:%02d',\
'{\"email\": \"<EMAIL>\", \"domain\": {\"free\": \"no\", \"name\": \"nvt.pl\", \"acceptAll\": \"no\", \"disposable\": \"no\"}, \"reason\": \"accepted_email\", \"status\": \"%s\", \"account\": {\"role\": \"no\", \"disabled\": \"no\", \"fullMailbox\": \"no\"}, \"provider\": \"other\"}',\
	true, %d,\
       	%d);\n";
	//cout << email_validation_sql << endl;
	//printf(email_validation_sql.c_str(), "<EMAIL>");
	srand(time(0));
	vector<string> status_vec;
	status_vec.push_back("deliverable");
	status_vec.push_back("risky");
	status_vec.push_back("unknown");
	for (int i = 0 ; i < 100; ++i ) {
		int hour = rand() % 24;
		int min = rand() % 60;
		int date = (rand() % 3) + 1;
		string status = status_vec[rand() % 3];
		printf(email_validation_sql.c_str(), email_start_index+i,
			date, hour, min, status.c_str(), acc_id, team_id);
	}
}

int main(int argc, char * argv []) {
	using namespace std;
	if (argc !=  4) {
		cerr << "Usage :" << endl
			<< "gen-email_validations <account_id> <team_id> <email_start_index>"
			<< endl;
		exit(1);
	}
	int acc_id = atoi(argv[1]);
	int team_id = atoi(argv[2]);
	int email_start_index = atoi(argv[3]);

	print_email_validation_sql(acc_id, team_id, email_start_index);
}
