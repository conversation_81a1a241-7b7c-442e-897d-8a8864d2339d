package api

import play.api.mvc.{Request, Result}
import utils.ISRLogger

class SRAPIResponse(
                     override protected val Logger: ISRLogger
                   ) extends APIResponseModule[Request[?]] {


  final override protected def shouldLogRequestToDb: Boolean = false // FOR NOW, TODO later


  final override def enableLogRequestToDb(request: Request[?]): APIResponseModule[Request[?]] = {

    this
  }

  final override protected def logRequestToDb(httpResponse: Result): Boolean = {

    Logger.info(s"logRequestToDb: ${httpResponse.header.status}}")

    false
  }
}
