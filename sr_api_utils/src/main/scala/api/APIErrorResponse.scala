package api

import api.campaigns.services.{ProspectAssignErrorType, ProspectUnAssignErrorType}
import api.blacklist.service.BlacklistErrorType
import api.prospects.service.{ProspectErrorType, UpdateProspectStatusErrorType}
import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, JsResult, JsSuccess, JsValue, Json, OWrites, Reads, Writes}

import scala.util.{Failure, Success, Try}

sealed trait APIErrorResponse

object APIErrorResponse {

  case class ErrorResponseProspectsPostApi(
                                            error_type: ProspectErrorType,
                                            message: String,
                                            data: Option[String],
                                            owned_prospects: Option[List[Map[String, String]]]
                                          ) extends APIErrorResponse

  object ErrorResponseProspectsPostApi {

    implicit val writes: Writes[ErrorResponseProspectsPostApi] = new Writes[ErrorResponseProspectsPostApi] {
      override def writes(error_data: ErrorResponseProspectsPostApi): JsValue = {
        Json.obj(
          "error_type" -> error_data.error_type,
          "message" -> error_data.message,
          "data" -> error_data.data,
          "owned_prospects" -> error_data.owned_prospects
        )

      }
    }


    implicit val reads: Reads[ErrorResponseProspectsPostApi] = new Reads[ErrorResponseProspectsPostApi] {
      override def reads(json: JsValue): JsResult[ErrorResponseProspectsPostApi] = Try {
        ErrorResponseProspectsPostApi(
          error_type = (json \ "error_type").as[ProspectErrorType],
          message = (json \ "message").as[String],
          data = (json \ "data").asOpt[String],
          owned_prospects = (json \ "owned_prospects").asOpt[List[Map[String, String]]]
        )
      } match {
        case Failure(e) => JsError(e.getMessage)
        case Success(errorResponseProspectsPostApi) => JsSuccess(errorResponseProspectsPostApi)
      }
    }

  }

  case class ErrorResponseProspectsAssignApi(
                                              error_type: ProspectAssignErrorType,
                                              message: String,
                                              data: Option[String]
                                            ) extends APIErrorResponse

  object ErrorResponseProspectsAssignApi {

    implicit val writes: Writes[ErrorResponseProspectsAssignApi] = new Writes[ErrorResponseProspectsAssignApi] {
      override def writes(error_data: ErrorResponseProspectsAssignApi): JsValue = {
        Json.obj(
          "error_type" -> error_data.error_type,
          "message" -> error_data.message,
          "data" -> error_data.data
        )

      }
    }


    implicit val reads: Reads[ErrorResponseProspectsAssignApi] = new Reads[ErrorResponseProspectsAssignApi] {
      override def reads(json: JsValue): JsResult[ErrorResponseProspectsAssignApi] = Try {
        ErrorResponseProspectsAssignApi(
          error_type = (json \ "error_type").as[ProspectAssignErrorType],
          message = (json \ "message").as[String],
          data = (json \ "data").asOpt[String]
        )
      } match {
        case Failure(e) => JsError(e.getMessage)
        case Success(errorResponseProspectsAssignApi) => JsSuccess(errorResponseProspectsAssignApi)
      }
    }

  }

  case class ErrorResponseProspectsUnAssignApi(
                                                error_type: ProspectUnAssignErrorType,
                                                message: String,
                                                data: Option[String]
                                              ) extends APIErrorResponse

  object ErrorResponseProspectsUnAssignApi {

    implicit val writes: Writes[ErrorResponseProspectsUnAssignApi] = Json.writes[ErrorResponseProspectsUnAssignApi]

    implicit val reads: Reads[ErrorResponseProspectsUnAssignApi] = Json.reads[ErrorResponseProspectsUnAssignApi]

  }

  case class ErrorResponseUpdateProspectStatusApi(
                                                   error_type: UpdateProspectStatusErrorType.Value,
                                                   message: String,
                                                   data: Option[String]
                                                 ) extends APIErrorResponse

  object ErrorResponseUpdateProspectStatusApi {

    implicit val writes: Writes[ErrorResponseUpdateProspectStatusApi] = new Writes[ErrorResponseUpdateProspectStatusApi] {
      override def writes(error_data: ErrorResponseUpdateProspectStatusApi): JsValue = {
        Json.obj(
          "error_type" -> error_data.error_type,
          "message" -> error_data.message,
          "data" -> error_data.data
        )

      }
    }


    implicit val reads: Reads[ErrorResponseUpdateProspectStatusApi] = new Reads[ErrorResponseUpdateProspectStatusApi] {
      override def reads(json: JsValue): JsResult[ErrorResponseUpdateProspectStatusApi] = Try {
        ErrorResponseUpdateProspectStatusApi(
          error_type = (json \ "error_type").as[UpdateProspectStatusErrorType.Value],
          message = (json \ "message").as[String],
          data = (json \ "data").asOpt[String]
        )
      } match {
        case Failure(e) => JsError(e.getMessage)
        case Success(errorResponseUpdateProspectStatusApi) => JsSuccess(errorResponseUpdateProspectStatusApi)
      }
    }
  }

  case class ErrorResponse(
                            message: String,
                            data: Option[String],
                          ) extends APIErrorResponse

  object ErrorResponse {

    implicit val writes: Writes[ErrorResponse] = new Writes[ErrorResponse] {
      override def writes(error_data: ErrorResponse): JsValue = {
        Json.obj(
          "message" -> error_data.message,
          "data" -> error_data.data,
        )

      }
    }


    implicit val reads: Reads[ErrorResponse] = new Reads[ErrorResponse] {
      override def reads(json: JsValue): JsResult[ErrorResponse] = Try {
        ErrorResponse(
          message = (json \ "message").as[String],
          data = (json \ "data").asOpt[String],
        )
      } match {
        case Failure(e) => JsError(e.getMessage)
        case Success(errorResponse) => JsSuccess(errorResponse)
      }
    }
  }

  case class ErrorResponseV3(
                              error_type: ErrorType.Value,
                              message: String,
                          ) extends APIErrorResponse

  object ErrorResponseV3 {
    implicit val writes: Writes[ErrorResponseV3] = Json.writes[ErrorResponseV3]
  }

  case class ErrorResponseCreateUpdateBlacklistApi(
                                                    error_type: BlacklistErrorType,
                                                    message: String,
                                                    data: Option[Seq[String]] = None
                                         ) extends APIErrorResponse

  object ErrorResponseCreateUpdateBlacklistApi {
    implicit val writes: Writes[ErrorResponseCreateUpdateBlacklistApi] = Json.writes[ErrorResponseCreateUpdateBlacklistApi]
    implicit val reads: Reads[ErrorResponseCreateUpdateBlacklistApi] = Json.reads[ErrorResponseCreateUpdateBlacklistApi]
  }


  implicit def writes: Writes[APIErrorResponse] = new Writes[APIErrorResponse] {
    override def writes(c: APIErrorResponse) = {
      c match {
        case data: ErrorResponseProspectsPostApi => Json.toJson(data)
        case data: ErrorResponseProspectsAssignApi => Json.toJson(data)
        case data: ErrorResponseProspectsUnAssignApi => Json.toJson(data)
        case data: ErrorResponseUpdateProspectStatusApi => Json.toJson(data)
        case data: ErrorResponse => Json.toJson(data)
        case data: ErrorResponseV3 => Json.toJson(data)
        case data: ErrorResponseCreateUpdateBlacklistApi => Json.toJson(data)
      }
    }
  }

}

