package api.blacklist.service

import play.api.libs.json._

import scala.util.{Failure, Success, Try}

sealed trait BlacklistErrorType

object BlacklistErrorType {

  private val limit_exceeded = "limit_exceeded"
  private val invalid_emails = "invalid_emails"
  private val empty_list = "empty_list"
  private val bad_request = "bad_request"
  private val email_does_not_belong_to_domain = "email_does_not_belong_to_domain"
  private val emails_already_exists_in_exception_list = "emails_already_exists_in_exception_list"
  private val emails_already_exists_in_blacklist = "emails_already_exists_in_do_not_contact_list"
  private val invalid_ids = "invalid_ids"
  private val invalid_domains = "invalid_domains"
  private val invalid_phone_numbers = "invalid_phone_numbers"
  private val cannot_pass_phone_numbers = "cannot_pass_phone_numbers"
  private val phones_not_allowed_in_global_dnc = "phones_not_allowed_in_global_dnc"


  case object LimitExceeded extends BlacklistErrorType {
    override def toString: String = limit_exceeded
  }

  case object InvalidEmails extends BlacklistErrorType {
    override def toString: String = invalid_emails
  }

  case object EmptyList extends BlacklistErrorType {
    override def toString: String = empty_list
  }

  case object BadRequest extends BlacklistErrorType {
    override def toString: String = bad_request
  }

  case object EmailDoesNotBelongToDomain extends BlacklistErrorType {
    override def toString: String = email_does_not_belong_to_domain
  }

  case object EmailsAlreadyExistsInExceptionList extends BlacklistErrorType {
    override def toString: String = emails_already_exists_in_exception_list
  }

  case object EmailAlreadyExistsInBlacklist extends BlacklistErrorType {
    override def toString: String = emails_already_exists_in_blacklist
  }

  case object InvalidIds extends BlacklistErrorType {
    override def toString: String = invalid_ids
  }

  case object InvalidDomains extends BlacklistErrorType {
    override def toString: String = invalid_domains
  }

  case object InvalidPhoneNumbers extends BlacklistErrorType {
    override def toString: String = invalid_phone_numbers
  }

  case object CannotPassPhoneNumbers extends BlacklistErrorType {
    override def toString: String = cannot_pass_phone_numbers
  }

  case object PhonesNotAllowedInGlobalDNC extends BlacklistErrorType {
    override def toString: String = phones_not_allowed_in_global_dnc
  }
  def fromString(key: String): Try[BlacklistErrorType] = Try {
    key match {
      case `limit_exceeded` => LimitExceeded
      case `invalid_emails` => InvalidEmails
      case `empty_list` => EmptyList
      case `bad_request` => BadRequest
      case `email_does_not_belong_to_domain` => EmailDoesNotBelongToDomain
      case `emails_already_exists_in_exception_list` => EmailsAlreadyExistsInExceptionList
      case `emails_already_exists_in_blacklist` => EmailAlreadyExistsInBlacklist
      case `invalid_ids` => InvalidIds
      case `invalid_domains` => InvalidDomains
      case `invalid_phone_numbers` => InvalidPhoneNumbers
      case `cannot_pass_phone_numbers` => CannotPassPhoneNumbers
      case `phones_not_allowed_in_global_dnc` => PhonesNotAllowedInGlobalDNC
    }
  }

  implicit def writes: Writes[BlacklistErrorType] = new Writes[BlacklistErrorType] {
    def writes(i: BlacklistErrorType): JsString = {
      JsString(i.toString)
    }
  }

  implicit def reads: Reads[BlacklistErrorType] = (json: JsValue) => {

    fromString(key = json.as[String]) match {

      case Failure(e) => JsError(e.toString)

      case Success(value) => JsSuccess(value)

    }
  }
}
