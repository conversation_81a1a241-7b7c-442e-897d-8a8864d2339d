#!/bin/bash

set -e

# Function to start a service and check its status
start_service() {
    local service_name=$1
    echo "Starting $service_name..."
    service $service_name start
    if [ $? -ne 0 ]; then
        echo "Failed to start $service_name"
        exit 1
    fi
}

# Start PostgreSQL
start_service postgresql

# Wait for PostgreSQL to be ready before executing commands
echo "Waiting for PostgreSQL to be ready..."
until pg_isready -U postgres; do
  sleep 1
done
echo "PostgreSQL is ready!"


echo "Updating PostgreSQL max_connections..."
psql -U postgres -c "ALTER SYSTEM SET max_connections = 250;"
echo "Updated max_connections to 250"

# Restart PostgreSQL to apply changes
echo "Restarting PostgreSQL to apply changes..."
service postgresql restart

# Set up PostgreSQL user and database
echo "Initializing PostgreSQL user and database..."

psql -v ON_ERROR_STOP=1 --username "postgres" <<-EOSQL
    CREATE DATABASE ${POSTGRES_DB};
    CREATE USER ${POSTGRES_USER} WITH PASSWORD '${POSTGRES_PASSWORD}';
    ALTER ROLE ${POSTGRES_USER} SUPERUSER;
    GRANT ALL PRIVILEGES ON DATABASE ${POSTGRES_DB} TO ${POSTGRES_USER};

    \c ${POSTGRES_DB};

    CREATE SCHEMA partman;
    CREATE EXTENSION pg_partman SCHEMA partman;

    CREATE ROLE partman_user WITH LOGIN;
    GRANT ALL ON SCHEMA partman TO partman_user;
    GRANT ALL ON ALL TABLES IN SCHEMA partman TO partman_user;
    GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA partman TO partman_user;
    GRANT EXECUTE ON ALL PROCEDURES IN SCHEMA partman TO partman_user;
    GRANT ALL ON SCHEMA partman TO partman_user;
    GRANT TEMPORARY ON DATABASE ${POSTGRES_DB} TO partman_user;
    GRANT CREATE ON DATABASE ${POSTGRES_DB} TO partman_user;

    ALTER USER postgres PASSWORD '${POSTGRES_PASSWORD}';
    SELECT now();
EOSQL

# Start Redis with custom port (7777)
echo "Starting Redis on port 7777..."
redis-server --port 7777 &
until redis-cli -p 7777 ping; do
  sleep 1
done
echo "Redis is running on port 7777!"

# Verify Scala installation
echo "Verifying Scala installation..."
scala -version
if [ $? -ne 0 ]; then
    echo "Scala compiler is not installed correctly."
    exit 1
fi

# Run additional commands if provided, otherwise keep the container running
if [ "$#" -gt 0 ]; then
    echo "Executing provided command: $@"
    exec "$@"
else
    echo "Services are running. Container is idle."
    tail -f /dev/null
fi