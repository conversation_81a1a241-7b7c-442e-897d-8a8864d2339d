openapi: 3.0.0
servers:
  - url: 'https://api.smartreach.io/api/v3'
info:
  description:
    SmartReach's api specs
  version: 1.0.0
  title: SmartReach
  license:
    name: Apache-2.0
    url: 'https://www.apache.org/licenses/LICENSE-2.0.html'
paths:
  '/email_settings':
    get:
      tags:
        - email_settings
      summary: Get all email_settings from Account
      description: Api to find all email_settings from an Account
      parameters:
        - in: query
          name: older_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false

        - in: query
          name: newer_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false
          
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get all email_settings from Account
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailSettingsResponse'
        '400':
          $ref: '#/components/responses/EmailSettingsBadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'
          
  '/email_settings/{email_setting_id}':
    get:
      tags:
        - email_settings
      summary: Get email_setting by id
      description: Api to find  email_settings by id 
      parameters:         
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
        - name: email_setting_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get email_setting by id
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailSettingByIdResponse'
        '400':
          $ref: '#/components/responses/EmailSettingsBadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'

components:

  schemas:
  
    EmailSettingsResponse:
      title: response for Get all email_settings from Account
      description: response for for Get all email_settings from Account
      type: object
      required:
        - email_settings
        - links
      properties:
        email_settings:
          $ref:  '#/components/schemas/GetEmailSettingsArray'
        links:
          $ref: '#/components/schemas/Links'
          
    EmailSettingByIdResponse:
      title: response for Get email_settings by Id
      description: response for for Get email_settings by Id
      type: object
      required:
        - email_setting
      properties:
        email_setting:
          $ref:  '#/components/schemas/EmailSetting'
    
    GetEmailSettingsArray:
      title: Get email settings array
      description: Response array of email_settings
      type: array
      items:
        $ref: '#/components/schemas/EmailSetting'
    
    Links:
      title: Links
      description: Links
      type: object
      required:
        - next
      properties:
        next:
          type: string
          
    EmailSetting:
      title: EmailSetting object
      description: SmartReach's EmailSetting object
      type: object
      required:
        - object
        - id
        - owner_id
        - service_provider
        - first_name
        - last_name
        - min_delay_seconds
        - max_delay_seconds
      properties:
        object:
          type: string
          example: email_setting
        id:
          type: string
        owner_id:
          type: string
        service_provider:
          type: string
          enum:
            - gmailapi
            - gmail_alias
            - gmail_asp
            - mailgun
            - sendgrid
            - office365
            - exchange
            - amazon_ses
            - rediff
            - namecheap
            - other
        first_name:
          type: string
        last_name:
          type: string
        cc_emails:
          type: string
        bcc_emails:
          type: string
        created_at:
          type: string
        min_delay_seconds:
          type: number
        max_delay_seconds:
          type: number
  
  responses:
    
    EmailSettingsBadRequest:
      description: Bad request error responses
      content:
        application/json:
          schema:
            type: object
            properties:
              errors:
                type: array
                items:
                  type: object
                  required:
                    - error_type
                    - message
                  properties:
                    error_type:
                      type: string
                    message: 
                      type: string
    
    ServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            type: object
            properties:
              error_type:
                type: string
              message:
                type: string
          example: 
            - error_type: api_error
              message: Error while finding Email Settings

    Unauthorized:
      description: User not authorized
      content:
        application/json:
          schema:
            type: object
            properties:
              error_type:
                type: string
              message:
                type: string
          example: 
            - error_type: unauthorized
              message: You are not authorized to create prospect
      
        