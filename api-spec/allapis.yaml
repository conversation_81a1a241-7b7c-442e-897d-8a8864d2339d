openapi: 3.0.0
servers:
  - url: 'https://api.smartreach.io/api/v3'
info:
  description: >-
    SmartReach's api specs
  version: 1.0.0
  title: SmartReach
  license:
    name: Apache-2.0
    url: 'https://www.apache.org/licenses/LICENSE-2.0.html'
tags:
  - name: prospects
    description: Prospects related apis
paths:
  /prospects:
    post:
      tags:
        - prospects
      summary: Add/Update new prospects
      description: Api to create/update prospects
      operationId: addProspects
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
            
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.
            
      responses:
        '200':
          description: created n prospects
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Prospects'
        '400':
          description: Bad request error responses
          content:
            application/json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    items:
                      $ref: '#/components/schemas/ErrorResponseProspectsPostApi'
                example:
                  - error_type: internal_emails_found
                    message: Internal emails cannot be added as prospects
                    data: '<EMAIL>,<EMAIL>'
                  - error_type: duplicate_emails_found
                    message: Duplicate prospect emails found
                    data: '<EMAIL>,<EMAIL>'
                  - error_type: custom_field_not_found
                    message: Custom field/s does not exist. Please add it via UI.
                    data: 'address,phone'
                  - error_type: limit_exceeded
                    message: Limit exceeded. Cannot create/update more than 100 prospects.
                  - error_type: multiple_owners_found
                    message: In a single request, Prospects can be added to one specific Owner. Send multiple requests to add prospects to different owners.  #without checking in db
                  - error_type: invalid_source
                    message: Please send valid prospect source.
                    data: 'sales'
                  - error_type: empty_list
                    message: Please send a list of prospects. Empty request found.
                  - error_type: multiple_list_found
                    message: In a single request, Prospects can be added to one specific List. Send multiple requests to add prospects to different Lists.
                    data: 'sales,marketing'
                  - error_type: bad_request
                    message: Error while create/update prospects. Please contact support.
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'
      requestBody:
        $ref: '#/components/requestBodies/ProspectList'
    get:
      tags:
        - prospects
      summary: Get prospects
      description: Api to fetch prospects
      operationId: getProspects
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
            
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.

        - in: query
          name: older_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false

        - in: query
          name: newer_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false
      responses:
        '200':
          description: Fetched n prospects
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProspectsResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'
  /campaigns:
    get:
      tags:
        - campaigns
      summary: Get campaigns
      description: ''
      operationId: getCampaigns
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
            
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.

        - in: query
          name: status
          schema:
            type: string
            enum:
              - "not_started"
              - "scheduled"
              - "running"
              - "stopped"
              - "archived"
              - "under_review"
              - "suspended"
          required: false

        - in: query
          name: name
          schema:
            type: string
          required: false

        - in: query
          name: sender_email
          schema:
            type: string
          description: email of the sending email account
          required: false

        - in: query
          name: receiver_email
          schema:
            type: string
          description: email of the receiving email account
          required: false

        - in: query
          name: older_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false

        - in: query
          name: newer_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false

      responses:
        '200':
          description: Campaigns found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CampaignResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/ServerError'


  /campaigns/{campaign_id}:
    get:
      tags:
        - campaigns
      summary: Get campaign from id
      description: ''
      operationId: getCampaign
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.
        - name: campaign_id
          in: path
          description: ID of campaign to return
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Campaign found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Campaign'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/ServerError'

  '/campaigns/{campaign_id}/prospects':

    get:
      tags:
        - campaigns
      summary: Get prospect/s in campaign
      description: Api to get prospect/s in campaign
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
            
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.

        - name: campaign_id
          in: path
          description: ID of campaign to return
          required: true
          schema:
            type: string

        - in: query
          name: older_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false

        - in: query
          name: newer_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false

      responses:
        '200':
          description: added prospects to campaign
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProspectsInCampaignResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'

    post:
      tags:
        - campaigns
      summary: Add prospects to campaign
      description: Api to add prospects to campaign
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.
        - name: campaign_id
          in: path
          description: ID of campaign to return
          required: true
          schema:
            type: string
      responses:
        '200':
          description: added prospects to campaign
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssignProspectsResponse'
        '400':
          description: Bad request error responses
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ErrorResponseProspectsAssignApi'
                example:
                  - error_type: limit_exceeded
                    message: Limit exceeded. Cannot assign more than 100 prospects.
                  - error_type: empty_list
                    message: Please send a list of prospect ids. Empty request found.
                  - error_type: bad_request
                    message: Error while assigning prospects. Please contact support.
                  - error_type: forbidden
                    message: Given prospects do not exist in your account.
                    data: ["forbiddenUuid1", "forbiddenUuid2"]
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'
      requestBody:
        $ref: '#/components/requestBodies/AssignProspectRequestBody'
        

    put:
      tags:
        - campaigns
      summary: Unassign prospect/s From Campaign
      description: Api to unassign prospect/s From Campaign
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
            
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.

        - name: campaign_id
          in: path
          description: ID of campaign to return
          required: true
          schema:
            type: string

      responses:
        '204':
          description: unassigned prospects to campaign
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'  
      requestBody:
          $ref: '#/components/requestBodies/UnassignProspectRequestBody'   
        
  '/prospects/prospect_status_change':
      put:
        tags:
          - campaigns
        summary: Update prospects status for a campaign
        description: Api to update prospect status for a campaign
        parameters: 
          - name: X-API-KEY
            in: header
            required: true
            schema:
              type: string
            
          - name: team_id
            in: query
            required: true
            schema:
              type: string
              example: team_*********
            description: Id for your team.
            
        responses:
          '200':
            description: updated status of prospects in a campaign
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/UpdateProspectsStatusResponse'
          '400':
            description: Bad request error responses for update prospect status
            content:
              application/json:
                schema:
                  type: array
                  items:
                    $ref: '#/components/schemas/UpdateProspectStatusError'
                  example:
                    - error_type: empty_list
                      message: Please send a list of prospect ids. Empty request found.
                    - error_type: bad_request
                      message: Error while updating prospects status. Please contact support.
                    - error_type: invalid_prospect_status
                      message: provided prospect status does not match with any status update
                    - error_type: prospects_not_assigned_to_campaign
                      message: provided prospects are not assigened in the given campaign
                      data: ["forbiddenUuid1", "forbiddenUuid2"]
          '401':
            $ref: '#/components/responses/Unauthorized'
          '500':
            $ref: '#/components/responses/ServerError'
        requestBody:
          $ref: '#/components/requestBodies/UpdateProspectsStatusRequestBody'

  '/campaigns/{campaign_id}/status':
    put:
      tags:
        - campaigns
      summary: Change campaign status
      description: Start/Stop/Schedule campaign by id
      operationId: startStopCampaign
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.
        - name: campaign_id
          in: path
          description: ID of campaign
          required: true
          schema:
            type: string
            
      requestBody:
        $ref: '#/components/requestBodies/StartStopCampaign'

      responses:
        '200':
          description: Status changed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Campaign'
        '400':
          $ref: '#/components/responses/BadRequestErrorStartStopCampaign'
        '401':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
          
  '/campaigns/{campaign_id}/channel_settings':
    get:
      tags:
        - campaigns
      summary: Get campaign settings by id
      description: Get multichannel settings for a campaign.
      operationId: channelSettings
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.
        - name: campaign_id
          in: path
          description: ID of campaign
          required: true
          schema:
            type: string
      
      responses:
        '200':
          description: Channel settings fetched successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChannelSettings'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
          
  '/email_settings':
    get:
      tags:
        - email_settings
      summary: Get all email_settings from Account
      description: Api to find all email_settings from an Account
      parameters:
        - in: query
          name: older_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false
            
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.

        - in: query
          name: newer_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false
          
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get all email_settings from Account
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailSettingsResponse'
        '400':
          $ref: '#/components/responses/EmailSettingsBadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'
          
  '/email_settings/{email_setting_id}':
    get:
      tags:
        - email_settings
      summary: Get email_setting by id
      description: Api to find  email_settings by id 
      parameters:         
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.
        - name: email_setting_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get email_setting by Id
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailSettingByIdResponse'
        '400':
          $ref: '#/components/responses/EmailSettingsBadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'
          
  /do_not_contact:
    get:
      tags:
        - do_not_contact
      summary: Get do not contact list
      description: ''
      operationId: getDNC
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
            
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.

        - in: query
          name: older_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false

        - in: query
          name: newer_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false

      responses:
        '200':
          description: Do_not_contact list found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetDNCResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/ServerError'
          
    delete:
      tags:
        - do_not_contact
      summary: Remove from do_not_contact list
      description: Remove multiple emails/domains from do_not_contact list using id.
      operationId: deleteDNC
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
            
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.
            
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                dnc_ids:
                  description: Ids of do_not_contact to be deleted
                  type: array
                  items:
                    type: string
            
      responses:
        '204':
          description: Removed from do_not_contact list
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/ServerError'
          
  "/do_not_contact/email":
    post:
      tags:
        - do_not_contact
      summary: Add emails to do_not_contact list
      description: ''
      operationId: addEmailsToDNC
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
            
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.
            
      requestBody:
        $ref: '#/components/requestBodies/CreateEmailDNC'
            
      responses:
        '200':
          description: Added to Do not contact list
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostDNCResponse'
        '400':
          $ref: '#/components/responses/CreateDNCBadRequestError'
        '401':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/ServerError'
          
  "/do_not_contact/domain":
    post:
      tags:
        - do_not_contact
      summary: Add domains to do_not_contact list
      description: ''
      operationId: addDomainsToDNC
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
            
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.
            
      requestBody:
        $ref: '#/components/requestBodies/CreateDomainDNC'
            
      responses:
        '200':
          description: Added to Do not contact list
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostDNCResponse'
        '400':
          $ref: '#/components/responses/CreateDNCBadRequestError'
        '401':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/ServerError'
          
  /teams:
    get:
      tags:
        - teams
      summary: Get teams
      operationId: getTeams
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
            
        - name: active
          in: query
          required: false
          schema:
            type: boolean
            default: true
          description: Filter teams by active = true/false. Default is active=true

        - in: query
          name: older_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false

        - in: query
          name: newer_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false
          
      responses:
        '200':
          description: Teams list found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TeamsListing'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/ServerError'
          
  "/teams/{team_id}":
    get:
      tags:
        - teams
      summary: Get team by Id
      operationId: getTeamById
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string

        - name: team_id
          in: path
          description: ID of team to return.
          required: true
          schema:
            type: string
          
      responses:
        '200':
          description: Team found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Team'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/ServerError'

  '/campaigns/{campaign_id}/stats':
    get:
      tags:
        - campaigns
      summary: get stats of a campaign by id
      description: get campaign stats by id
      operationId: getCampaignStats
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.
        - name: campaign_id
          in: path
          description: ID of campaign
          required: true
          schema:
            type: string

      responses:
        '200':
          description: Got campaign stats succesfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CampaignStats'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'

  /users:
    get:
      tags:
        - user
      summary: Get Users in a team
      description: Api to get users in a team
      operationId: getUsers
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.
        - in: query
          name: older_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false
        - in: query
          name: newer_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false
      responses:
        '200':
          description: Users found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UsersResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/ServerError'
  /users/{userId}:
    get:
      tags:
        - user
      summary: Get User by userId
      description: Api to get user Details using userId
      operationId: getUserById
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.
        - name: userId
          in: path
          description: ID of user to return
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Users found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/ServerError'
  
  '/accounts':
    post:
      tags:
        - accounts
      summary: Create or update account
      description: Api to create or update account
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
            
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.

      responses:
        '200':
          description: Create or update account response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProspectAccount'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'
      requestBody:
        $ref: '#/components/requestBodies/ProspectAccountRequestBody'
        
  '/search/accounts':
    get:
      tags:
        - accounts
      summary: Get list of account for a team
      description: Api to Get list of account for a team
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
            
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.
        
        - in: query
          name: older_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false

        - in: query
          name: newer_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false
          
      responses:
        '200':
          description: get list of account response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProspectAccountListingResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'
          
  '/accounts/{account_id}':
    get:
      tags:
        - accounts
      summary: Get account by id
      description: Api to Get account by id
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
            
        - name: team_id
          in: query
          required: true
          schema:
            type: string
            example: team_*********
          description: Id for your team.
        
        - name: account_id
          in: path
          required: true
          schema:
            type: string
          
      responses:
        '200':
          description: get account for id
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProspectAccount'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'


components:
  requestBodies:

    CreateEmailDNC:
      required: true
      content:
        application/json:
          schema:
            type: object
            description: List of emails to be blacklisted
            properties:
              emails:
                description: Emails to be blacklisted
                type: array
                items:
                  type: string
                  format: email
                        
    CreateDomainDNC:
      required: true
      content:
        application/json:
          schema:
            type: object
            description: List of domains with excluded_emails to be blacklisted
            properties:
              domains:
                description: Domains to be blacklisted with an exclusion list
                type: array
                items:
                  type: object
                  required:
                    - domain
                    - excluded_emails
                  properties:
                    domain:
                      type: string
                    excluded_emails:
                      type: array
                      items:
                        type: string
                        format: email 

    ProspectList:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ProspectCreateFormData'
            
    StartStopCampaign:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/StartStopCampaign'

    AssignProspectRequestBody:
      required: true
      content:
        application/json:
          schema:
            title: Body params
            description: Body parameters to assign prospects to campaign
            required:
              - prospect_ids
            properties:
              prospect_ids:
                type: array
                items:
                  type: string
              ignore_prospects_in_other_campaigns:
                type: string
                enum:
                  - ignore_prospects_active_in_other_campaigns
                  - ignore_prospects_added_in_other_campaigns
                  - do_not_ignore
                description:
                  Provided you have three prospects 'prs-a', 'prs-b' and 'prs-c' where 'prs-a' is added in campaign 'A' but is inactive, 'prs-b' is added in campaign 'B' and is active and 'prs-c' is not added in any campaign.
                  With the field ignore_prospects_in_other_campaigns, </br>
                  if flag is 'ignore_prospects_active_in_other_campaigns' , prs-a and prs-c will be assigned to new campaign, </br>
                  if flag is 'ignore_prospects_added_in_other_campaigns', prs-c will be assigned to new campaign, </br>
                  if flag is 'do_not_ignore', 'prs-a', 'prs-b' and 'prs-c' will be assigned to new campaign. </br>
                  By default the value for this flag will be 'do_not_ignore'.
                  
    UpdateProspectsStatusRequestBody:
      required: true
      content:
        application/json:
          schema:
            title: Body params
            description: Body parameters to update prospects status
            required:
              - prospect_ids
              - prospect_status
              - campaign_ids
            properties:
              campaign_ids:
                type: array
                items:
                  type: string
              prospect_ids:
                type: array
                items:
                  type: string
              prospect_status:
                type: string
                enum:
                  - replied
                  - pause
                  - unpause
                  - resume_later
                description: 
                  Let's assume you have list of prospects to change the status then </br>
                  if "replied" is passed in Api call the status of a prospect for a choosen campaign changes to "replied" </br>
                  if "pause" is passed in Api call the status of a prospect for a choosen campaign changes to "pause" </br>
                  if "upause" is passed in Api call the status of a prospect for a choosen campaign changes to "unpause" </br>
                  if "resume_later"  is passed in Api call  the status of a prospect for a choosen campaign changes to resume later on given date and time
              will_resume_at:
                type: string
                description:
                  \*Required for resume_later for a specific date. The date-time with respective time-zone of schedule start should be converted to Epoch milliseconds and passed. 
              will_resume_at_tz:
                type: string
                description: 
                  \*Required if prospect_status is resume_later time zone format (Asia/kolkata)

    UnassignProspectRequestBody:
      required: true
      content:
        application/json:
          schema:
            title: Body params
            description: Body parameters to update prospects status
            required:
              - prospect_ids
            properties:
              prospect_ids:
                type: array
                items:
                  type: string   
    
    ProspectAccountRequestBody:
      required: true
      content:
        application/json:
          schema:
            title: Body params
            description: Body parameters to create or update account
            required:
              - name
            properties:
              name:
                description: Name of the account
                type: string
              custom_id:
                description: custom_id of the account
                type: string
              description:
                description: description of the account
                type: string
              website:
                description: website of the account
                type: string
              industry:
                description: industry of the account
                type: string
              linkedin_url:
                description: linkedin_url of the account
                type: string
              custom_fields:
                description: custom_fields of the account
                type: object
              update_account:
                description: option for update account
                type: string
                enum:
                  - force_update
                  - ignore_null_or_empty_values  

  schemas:
  
    TeamsListing:
      title: Teams listing response
      description: teams listinng api response
      type: object
      properties:
        teams:
          type: array
          items:
            $ref: '#/components/schemas/Team'
        links:
          $ref: '#/components/schemas/DNCLinks'
          
    ProspectAccountListingResponse:
      title: response for List of Account
      description: SmartReach's Account Response object
      type: object
      required:
        - accounts
        - links
      properties:
        accounts:
          $ref:  '#/components/schemas/ProspectAccountList'
        links:
          $ref: '#/components/schemas/Links'
    
    ProspectAccountList:
      title: ProspectAccountList object
      description: SmartReach's Account List object
      type: array
      items:
          $ref: '#/components/schemas/ProspectAccount'
          
    Team:
      title: team object
      description: SmartReach's team object
      type: object
      required:
        - object
        - id
        - team_name
        - status
        - created_at
        - org_id
        - users
      properties:
        object:
          type: string
          example: team
        id:
          type: string
        team_name:
          type: string
        status:
          type: string
          enum:
            - active
            - inactive
        created_at:
          type: number
          description: Time in millis since Unix epoch
        org_id:
          type: string
        users:
          type: array
          items:
            $ref: '#/components/schemas/User'

    UsersResponse:
      title: response for Get all user details from Account
      description: response for for Get all user details from Account
      type: object
      required:
        - users
        - links
      properties:
        users:
          $ref: '#/components/schemas/UsersArray'
        links:
          $ref: '#/components/schemas/Links'

    UsersArray:
      title: Get users array
      description: Response array of users
      type: array
      items:
        $ref: '#/components/schemas/User'

    User:
      title: User object
      description: SmartReach's User object
      type: object
      required:
        - object
        - id
        - first_name
        - last_name
        - org_id
        - status
        - email
        - created_at
        - timezone
      properties:
        object:
          type: string
          example: user
        id:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        org_id:
          type: string
        status:
          type: string
          enum: 
            - active
            - inactive
        email:
          type: string
        created_at:
          type: integer
          format: int64
          description: timestamp in unix epoch milliseconds
        timezone:
          type: string

    DNCList:
      title: Do not contact list object
      description: SmartReach's do_not_contact list object
      type: array
      items:
        $ref: '#/components/schemas/DNC'

    GetDNCResponse:
      title: Do_not_contact list object
      required:
        - do_not_contacts
        - links
      type: object
      properties:
        do_not_contacts:
          $ref: '#/components/schemas/DNCList'
        links:
          $ref: '#/components/schemas/DNCLinks'
          
    PostDNCResponse:
      title: createDNC response object
      required:
        - do_not_contacts
      type: object
      properties:
        do_not_contacts:
          $ref: '#/components/schemas/DNCList'
          
    DNC:
      title: do_not_contact object
      description: SmartReach's do_not_contact object
      type: object
      required:
        - object
        - id
        - name
        - created_at
        - do_not_contact_type
        - team_id
        - excluded_emails
      properties:
        object:
          type: string
          example: do_not_contact
        id:
          type: string
        name:
          type: string
        created_at:
          type: integer
          format: unix-epoch
        do_not_contact_type:
          type: string
          enum:
            - email
            - domain
        team_id:
          type: string
        excluded_emails:
          type: array
          items:
            type: string
      example:
        "object": "do_not_contact"
        "id": "cmp_aa_2VhLetawyxX4Nz0SUarAlmy2Ua6"
        "name": "<EMAIL>"
        "created_at": 1708497867563
        "do_not_contact_type": "email"
        "team_id": "team_2VeGuZpoYDVfTPseaiRg8up9z19"
        "excluded_emails": [] 
        
    DNCLinks:
      title: Links
      description: Links
      type: object
      properties:
        prev:
          type: string
        next:
          type: string

    ProspectsResponse:
      title: Prospect Response object
      description: SmartReach's Prospect Response object
      required:
        - prospects
        - links
      type: object
      properties:
        prospects:
          $ref: '#/components/schemas/Prospects'
        links:
          $ref: '#/components/schemas/Links'
  
    EmailSettingsResponse:
      title: response for Get all email_settings from Account
      description: response for for Get all email_settings from Account
      type: object
      required:
        - email_settings
        - links
      properties:
        email_settings:
          $ref:  '#/components/schemas/GetEmailSettingsArray'
        links:
          $ref: '#/components/schemas/Links'
          
    EmailSettingByIdResponse:
      title: response for Get email_setting by Id
      description: response for for Get email_setting by Id
      type: object
      required:
        - email_setting
      properties:
        email_setting:
          $ref:  '#/components/schemas/EmailSetting'
          
    GetEmailSettingsArray:
      title: Get email settings array
      description: Response array of email_settings
      type: array
      items:
        $ref: '#/components/schemas/EmailSetting'
    
    EmailSetting:
      title: EmailSetting object
      description: SmartReach's EmailSetting object
      type: object
      required:
        - object
        - id
        - owner_id
        - service_provider
        - first_name
        - last_name
        - email
        - min_delay_seconds
        - max_delay_seconds
      properties:
        object:
          type: string
          example: email_setting
        id:
          type: string
        owner_id:
          type: string
        service_provider:
          type: string
          enum:
            - gmailapi
            - gmail_alias
            - gmail_asp
            - mailgun
            - sendgrid
            - office365
            - exchange
            - amazon_ses
            - rediff
            - namecheap
            - other
        first_name:
          type: string
        last_name:
          type: string
        email:
          type: string
        cc_emails:
          type: string
        bcc_emails:
          type: string
        created_at:
          type: string
        min_delay_seconds:
          type: number
        max_delay_seconds:
          type: number
    
    ProspectCreateFormData:
      title: Prospect list to be created
      type: array
      items:
        type: object
        required:
          - email
        properties:
          email:
            type: string
          owner_id:
            type: string
          first_name:
            type: string
          last_name:
            type: string
          phone_number:
            type: string
          linkedin_url:
            type: string
          city:
            type: string
          company:
            type: string
          state:
            type: string
          country:
            type: string
          list:
            type: string
          tags:
            type: array
            items:
              type: string
          timezone:
            type: string
          custom_fields:
            $ref: '#/components/schemas/CustomFields'
      example:
        email: <EMAIL>
        first_name: John
        last_name: Doe
        phone_number: +91 8190******
        city: Pune
        company: JohnDoeSales&Marketing
        state: Maharashtra
        country: India
        timezone: Asia/Kolkata
        custom_fields:
          website: JohnDoeSales&Marketing.com

    ErrorResponseProspectsPostApi:
      title: Error while creating prospects
      description: Error
      type: object
      required:
        - error_type
        - message
      properties:
        error_type:
          type: string
          enum:
            - limit_exceeded
            - custom_field_not_found
            - invalid_source
            - empty_list
            - multiple_owners_found
            - multiple_list_found
            - bad_request
            - duplicate_emails_found
            - internal_emails_found
        message:
          type: string
        data:
          type: string
        owned_prospects:
          type: array
          items:
            type: string
            additionalProperties:
              type: string
    ErrorResponseProspectsAssignApi:
      title: Error while assigning prospects
      description: Error
      type: object
      required:
        - error_type
        - message
      properties:
        error_type:
          type: string
          enum:
            - limit_exceeded
            - empty_list
            - bad_request
            - forbidden
        message:
          type: string
        data:
          type: string
          
    UpdateProspectStatusError:
      title: Error while updating campaigns prospect status
      description: UpdateProspectStatusError
      type: object
      required:
        - error_type
        - message
      properties:
        error_type:
          type: string
          enum:
            - empty_list
            - bad_request
            - invalid_prospect_status
            - prospects_not_assigned_to_campaign
        message:
          type: string
        data:
          type: string
          
    Prospects:
      title: Prospects array
      description: Response array of create prospects
      type: array
      items:
        $ref: '#/components/schemas/Prospect'
      example:
        - object: prospect
          id: prs_aa_abcksmssijxy
          emails:
            email: <EMAIL>
            is_valid: true
            is_primary: true
          phone_numbers: [+918180******, +9198********]
          linkedin_url: https://in.linkedin.com/company/smartreach
          account_id: acc_aa_abcdefghi
          first_name: John
          last_name: Doe
          list: prospect_list
          tags:
            id: tags_a1bc2defgh6
            tag: leads
          city: Mumbai
          company: JohnDoeSales&Marketing.com
          state: Maharashtra
          country: India
          timezone: Asia/Kolkata
          custom_fields:
            website: JohnDoeSales&Marketing.com
          created_at: 2000-01-23T04:56:07
          updated_at: 2000-01-25T04:56:07
    
    ProspectAccount:
      title: ProspectAccount object
      description: SmartReach's Account object
      type: object
      required:
        - object
        - id
        - name
        - custom_id
        - created_at
        - updated_at
      properties:
        object:
          type: string
          example: account
        id:
          type: string
        name:
          type: string
        custom_id:
          type: string
        description:
          type: string
        source:
          type: string
        website:
          type: string
        industry:
          type: string
        linkedin_url: 
          type: string
        custom_fields:
          type: object
        created_at:
          type: number
        updated_at:
          type: number

    CampaignProspectArray:
      title: Campaign prospect array
      description: Campaign prospect array response
      type: array
      items:
        $ref: '#/components/schemas/CampaignProspect'
        
    Prospect:
      title: Prospect
      description: SmartReach's ProspectObject
      type: object
      required:
        - object
        - id
        - emails
        - account_id
        - created_at
        - updated_at
        - custom_fields
      properties:
        object:
          type: string
          example: prospect
        id:
          type: string
        emails:
          type: array
          items:
            type: object
            properties:
              email:
                type: string
              is_valid:
                type: boolean
              is_primary:
                type: boolean
        account_id:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        phone_numbers:
          type: array
          items:
            type: string
        linkedin_url:
          type: string
        city:
          type: string
        company:
          type: string
        state:
          type: string
        country:
          type: string
        timezone:
          type: string
        list:
          type: string
        tags:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
              tag:
                type: string
        custom_fields:
          $ref: '#/components/schemas/CustomFields'
        updated_at:
          type: string
          format: date-time
        created_at:
          type: string
          format: date-time
    CustomFields:
      type: object

    CampaignProspectData:
      title: Prospect status in campaign object
      type: object
      required:
        - sent
        - opened
        - replied
        - opted_out
        - out_of_office
        - bounced
        - completed
        - clicked
        - invalid_email
        - campaign_id
        - campaign_name
      properties:
        total_opens_in_campaign:
          type: number
        total_clicks_in_campaign:
          type: number
        sent:
          type: boolean
        opened:
          type: boolean
        replied:
          type: boolean
        opted_out:
          type: boolean
        bounced:
          type: boolean
        completed:
          type: boolean
        will_resume_at:
          type: string
          format: date-time
        will_resume_at_tz:
          type: string
        clicked:
          type: boolean
        invalid_email:
          type: boolean
        auto_reply:
          type: boolean
        out_of_office:
          type: boolean
        campaign_name:
          type: string
        campaign_id:
          type: string

    CampaignStats:
      title: Campaign stats object
      description: SmartReach's campaign stats object
      type: object
      required:
        - object
        - total_sent,
        - total_opened,
        - total_clicked,
        - total_replied,
        - total_steps,
        - current_prospects,
        - current_opted_out,
        - current_completed,
        - current_bounced,
        - merge_tag_errors,
        - current_failed_email_validation,
        - current_in_progress,
        - current_unsent_prospects,
        - current_do_not_contact,
        - reply_sentiment_stats
      properties:
        object:
          type: string
          example: campaign
        total_sent:
          type: number
        total_opened:
          type: number
        total_clicked:
          type: number
        total_replied:
          type: number
        total_steps:
          type: number
        current_prospects:
          type: number
        current_opted_out:
          type: number
        current_completed:
          type: number
        current_bounced:
          type: number
        merge_tag_errors:
          type: number
        current_failed_email_validation:
          type: number
        current_in_progress:
          type: number
        current_unsent_prospects:
          type: number
        current_do_not_contact:
          type: number
        reply_sentiment_stats:
          $ref: '#/components/schemas/ReplySentimentStats'

    ReplySentimentStats:
      title: ReplySentimentStats
      description: ReplySentimentStats
      type: object
      required:
        - positive
      properties:
        positive:
          type: number

    CampaignProspect:
      title: Prospect Campaign object
      type: object
      required:
        - object
        - prospect
        - prospect_status_in_campaign

      properties:
        object:
          type: string
          example: campaign_prospect
        prospect:
          $ref : '#/components/schemas/Prospect'
        prospect_status_in_campaign:
          $ref : '#/components/schemas/CampaignProspectData'


    CampaignResponse:
      title: Campaign Response object
      description: SmartReach's Campaign Response object
      required:
        - campaigns
        - links
      type: object
      properties:
        campaigns:
          $ref: '#/components/schemas/CampaignList'
        links:
          $ref: '#/components/schemas/Links'
    CampaignList:
      title: CampaignList object
      description: SmartReach's Campaign List object
      type: array
      items:
        $ref: '#/components/schemas/Campaign'
    Links:
      title: Links
      description: Links
      type: object
      required:
        - next
      properties:
        next:
          type: string

    Campaign:
      title: Campaign object
      description: SmartReach's CampaignObject
      type: object
      required:
        - object
        - id
        - name
        - created_at
        - status
        - team_id
        - settings
      properties:
        object:
          type: string
          example: campaign
        id:
          type: string
        name:
          type: string
        created_at:
          type: string
        status:
          type: string
        team_id:
          type: string
        settings:
          $ref: '#/components/schemas/Settings'
      example:
        "object": "campaign"
        "id": "cmp_aa_2VhLetawyxX4Nz0SUarAlmy2Ua6"
        "name": "dhdskjdsfh"
        "created_at": "2023-06-06T13:06:09.460+05:30"
        "status": "not_started"
        "is_archived": false
        "team_id": "team_2VeGuZpoYDVfTPseaiRg8up9z19"
        "settings": {
          "timezone": "Asia/Kolkata",
          "daily_from_time": 32400,
          "daily_till_time": 64800,
          "days_preference": [
            false,
            true,
            true,
            true,
            true,
            true,
            false
          ],
        }
    Settings:
      title: Campaign Settings object
      description: SmartReach's campaign settings object
      type: object
      required:
        - timezone
        - daily_from_time
        - daily_till_time
        - days_preference
      properties:
        timezone:
          type: string
        daily_from_time:
          type: number
          description: seconds from the start of day in the given timezone
        daily_till_time:
          type: number
          description: seconds from the start of day in the given timezone
        days_preference:
          type: array
          description: days preference starting from Sunday
          items:
            type: boolean


    AssignProspectsResponse:
      title: response for assign prospects to campaign
      description: response for assign prospects to campaign
      type: object
      properties:
        total_assigned:
          type: number
          example: 2
        prospect_data:
          $ref:  '#/components/schemas/Prospects'
        campaign_id:
          type: string
          example: "campaign_id"

    GetProspectsInCampaignResponse:
      title: response for get prospects in campaign
      description: response for get prospects in campaign
      type: object
      properties:
        campaign_prospects:
          $ref: '#/components/schemas/CampaignProspectArray'
        links:
          $ref: '#/components/schemas/Links'

    UpdateProspectsStatusResponse:
      title: response for updating prospects status to a campaign
      description: response for updating prospects status to a campaign
      type: object
      properties:
        total_updated:
          type: number
          example: 2
        prospect_status_updated:
          $ref:  '#/components/schemas/CampaignProspectArray'

    StartStopCampaign:
      title: StartStopCampaign api request structure.
      description: SmartReach's Start/Stop campaign object
      type: object
      required:
        - status

      properties:
        status:
          description: Status of campaign can be changed to 'running', 'scheduled', 'stopped'.
          type: string
          enum:
            - "running"
            - "scheduled"
            - "stopped"
      
        schedule_start_at:
          description: Required for scheduling a campaign for a specific date. The date-time with respective time-zone of schedule start should be converted to Epoch milliseconds and passed. 
          type: number
          
    ChannelSettings:
      title: Channel settings response structure
      type: object
      properties:
        channel_settings:
          type: object
          properties:
            general_settings:
              type: array
              items:
                $ref: '#/components/schemas/GeneralSetting'
            email_settings:
              type: array
              items:
                $ref: '#/components/schemas/CampaignEmailSetting'
            call_settings:
              type: array
              items:
                $ref: '#/components/schemas/CallSetting'
            linkedin_settings:
              type: array
              items:
                $ref: '#/components/schemas/LinkedinSetting'
            whatsapp_settings:
              type: array
              items:
                $ref: '#/components/schemas/WhatsappSetting'
            sms_settings:
              type: array
              items:
                $ref: '#/components/schemas/SmsSetting'
                
    CampaignEmailSetting:
      title: CampaignEmailSetting object
      description: SmartReach's Campaign Email Setting object
      type: object
      required:
        - object
        - id
        - team_id
        - sender_email_setting
        - receiver_email_setting
      properties:
        object:
          type: string
          example: email_settings
        id:
          type: string
        team_id:
          type: string
        sender_email_setting:
          # type: object
          $ref: '#/components/schemas/EmailSetting'
        receiver_email_setting:
          # type: object
          $ref: '#/components/schemas/EmailSetting'

    CallSetting:
      title: CallSetting object
      description: SmartReach's call setting object
      type: object
      required:
        - object
        - id
        - team_id
        - phone_number
        - owner_id
        - first_name
        - last_name
        - call_limit_per_day
      properties:
        object:
          type: string
          example: call_setting
        id:
          type: string
        team_id:
          type: string
        phone_number:
          type: string
          example: +15416314831
        owner_id:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        call_limit_per_day:
          type: number
          
    LinkedinSetting:
      title: LinkedinSetting object
      description: SmartReach's linkedin setting object
      type: object
      required:
        - object
        - id
        - team_id
        - email
        - owner_id
        - first_name
        - last_name
        - view_profile_limit_per_day
        - inmail_limit_per_day
        - message_limit_per_day
        - connection_request_limit_per_day
      properties:
        object:
          type: string
          example: linkedin_setting
        id:
          type: string
        team_id:
          type: string
        email:
          type: string
        owner_id:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        view_profile_limit_per_day:
          type: number
        inmail_limit_per_day:
          type: number
        message_limit_per_day:
          type: number
        connection_request_limit_per_day:
          type: number
        profile_url:
          type: string
    
    SmsSetting:
      title: SmsSetting object
      description: SmartReach's sms setting object
      type: object
      required:
        - object
        - id
        - team_id
        - owner_id
        - phone_number
        - first_name
        - last_name
        - sms_limit_per_day
      properties:
        object:
          type: string
          example: sms_setting
        id:
          type: string
        team_id:
          type: string
        phone_number:
          type: string
        owner_id:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        sms_limit_per_day:
          type: number
    
    GeneralSetting:
      title: GeneralSetting object
      description: SmartReach's general setting object
      type: object
      required:
        - object
        - id
        - team_id
        - owner_id
        - quota_per_day
      properties:
        object:
          type: string
          example: general_setting
        id:
          type: string
        team_id:
          type: string
        owner_id:
          type: string
        quota_per_day:
          type: number
      
    WhatsappSetting:
      title: WhatsappSetting object
      description: SmartReach's whatsapp setting object
      type: object
      required:
        - object
        - id
        - team_id
        - owner_id
        - first_name
        - last_name
        - whatsapp_number
        - whatsapp_message_limit_per_day
      properties:
        object:
          type: string
          example: whatsapp_setting
        id:
          type: string
        team_id:
          type: string
        owner_id:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        whatsapp_number:
          type: string
        whatsapp_message_limit_per_day:
          type: number

  responses:
  
    BadRequest:
      description: Bad request error responses
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              required:
                - error_type
                - message
              properties:
                error_type:
                  type: string
                message: 
                  type: string
                data:
                  type: array
                  items:
                    type: string
                  
    BadRequestErrorStartStopCampaign:
      description: Bad request error responses
      content:
        application/json:
          schema:
            type: object
            properties:
              errors:
                type: array
                items:
                  type: object
                  required:
                    - error_type
                    - message
                  properties:
                    error_type:
                      type: string
                      enum:
                        - "forbidden"
                        - "bad_request"
                        - "invalid_param"
                    message: 
                      type: string

    CreateDNCBadRequestError:
      description: Bad request error response
      content:
        application/json:
          schema:
            type: object
            properties:
              errors:
                type: array
                items:
                  type: object
                  required:
                    - error_type
                    - message
                  properties:
                    error_type:
                      type: string
                      enum:
                        - invalid_param
                        - limit_exceeded
                        - invalid_emails
                        - empty_list
                        - bad_request
                        - email_does_not_belong_to_domain
                        - emails_already_exists_in_exception_list
                        - emails_already_exists_in_do_not_contact_list
                    message: 
                      type: string
                    data: 
                      description: List of invalid emails
                      type: array
                      items:
                        type: string
    
    EmailSettingsBadRequest:
      description: Bad request error responses for EmailSettings
      content:
        application/json:
          schema:
            type: object
            properties:
              errors:
                type: array
                items:
                  type: object
                  required:
                    - error_type
                    - message
                  properties:
                    error_type:
                      type: string
                    message: 
                      type: string
  
    ServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            type: object
            properties:
              error_type:
                type: string
              message:
                type: string
          example: 
            - error_type: api_error
              message: Error while creating prospects
              
    Unauthorized:
      description: User not authorized
      content:
        application/json:
          schema:
            type: object
            properties:
              error_type:
                type: string
              message:
                type: string
          example: 
            - error_type: unauthorized
              message: You are not authorized to create prospect
              
    ForbiddenError:
      description: User not authorized
      content:
        application/json:
          schema:
            type: object
            properties:
              error_type:
                type: string
              message:
                type: string
          example: 
            - error_type: forbidden
              message: Campaign does not belong to the current team. Please switch view to the respective team.

    NotFoundError:
      description: Record not found
      content:
        application/json:
          schema:
            type: object
            properties:
              error_type:
                type: string
              message:
                type: string
          example:
            - error_type: not_found
              message: No Campaign found.