openapi: 3.0.0
servers:
  - url: 'https://api.smartreach.io/api/v3'
info:
  description: >-
    SmartReach's api specs
  version: 1.0.0
  title: SmartReach
  license:
    name: Apache-2.0
    url: 'https://www.apache.org/licenses/LICENSE-2.0.html'
tags:
  - name: campaigns
    description: Campaigns related apis
paths:
  /campaigns:
    get:
      tags:
        - campaigns
      summary: Get campaign/s
      description: ''
      operationId: getCampaigns
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string

        - in: query
          name: status
          schema:
            type: string
            enum:
              - "not_started"
              - "scheduled"
              - "running"
              - "stopped"
              - "archived"
              - "under_review"
              - "suspended"
          required: false

        - in: query
          name: name
          schema:
            type: string
          required: false

        - in: query
          name: sender_email
          schema:
            type: string
          description: email of the sending email account
          required: false

        - in: query
          name: receiver_email
          schema:
            type: string
          description: email of the receiving email account
          required: false

        - in: query
          name: older_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false

        - in: query
          name: newer_than
          schema:
            type: number
            format: int64
          description: timestamp in unix epoch milliseconds
          required: false

      responses:
        '200':
          description: Campaign/s found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CampaignResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/ServerError'


  '/campaigns/{campaign_id}':
    get:
      tags:
        - campaign
      summary: Get campaign from id
      description: ''
      operationId: getCampaign
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
        - name: campaign_id
          in: path
          description: ID of campaign to return
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Campaign found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Campaign'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/ServerError'

  '/campaigns/{campaign_id}/status':
    put:
      tags:
        - campaigns
      summary: Change status of campaign to - 'running', 'scheduled', 'stopped'
      description: Start/Stop a campaign by id.
      operationId: startStopCampaign
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
        - name: campaign_id
          in: path
          description: ID of campaign
          required: true
          schema:
            type: string
            
      requestBody:
        $ref: '#/components/requestBodies/StartStopCampaign'
      
      responses:
        '200':
          description: Status changed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Campaign'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
          
  '/campaigns/{campaign_id}/stats':
    get:
      tags:
        - campaigns
      summary: get stats of a campaign by id
      description: get campaign stats by id 
      operationId: getCampaignStats
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
        - name: campaign_id
          in: path
          description: ID of campaign
          required: true
          schema:
            type: string
      
      responses:
        '200':
          description: Got campaign stats succesfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CampaignStats'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
          
          
  '/campaigns/{campaign_id}/channel_settings':
    get:
      tags:
        - campaigns
      summary: Get campaign settings by id.
      description: Get multichannel settings for a campaign.
      operationId: channelSettings
      parameters:
        - name: X-API-KEY
          in: header
          required: true
          schema:
            type: string
        - name: campaign_id
          in: path
          description: ID of campaign
          required: true
          schema:
            type: string
      
      responses:
        '200':
          description: Channel settings fetched successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChannelSettings'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'

components:

  schemas:

    Error:
      title: Error while fetching campaign/s
      description: Error
      type: object
      properties:
        message:
          type: string
          example: No campaign found

    CampaignList:
      title: CampaignList object
      description: SmartReach's Campaign List object
      type: array
      items:
        $ref: '#/components/schemas/Campaign'

    CampaignResponse:
      title: Campaign Response object
      description: SmartReach's Campaign Response object
      required:
        - campaigns
        - links
      type: object
      properties:
        campaigns:
          $ref: '#/components/schemas/CampaignList'
        links:
          $ref: '#/components/schemas/Links'

    Links:
      title: Links
      description: Links
      type: object
      required:
        - next
      properties:
        next:
          type: string
          
    ReplySentimentStats:
      title: ReplySentimentStats
      description: ReplySentimentStats
      type: object
      required:
        - positive
      properties:
        positive:
          type: number

    Campaign:
      title: Campaign object
      description: SmartReach's CampaignObject
      type: object
      required:
        - object
        - id
        - name
        - created_at
        - status
        - team_id
        - settings
      properties:
        object:
          type: string
          example: campaign
        id:
          type: string
        name:
          type: string
        created_at:
          type: string
        status:
          type: string
        team_id:
          type: string
        settings:
          $ref: '#/components/schemas/Settings'
      example:
        "object": "campaign"
        "id": "cmp_aa_2VhLetawyxX4Nz0SUarAlmy2Ua6"
        "name": "dhdskjdsfh"
        "created_at": "2023-06-06T13:06:09.460+05:30"
        "status": "not_started"
        "is_archived": false
        "team_id": "team_2VeGuZpoYDVfTPseaiRg8up9z19"
        "settings": {
          "timezone": "Asia/Kolkata",
          "daily_from_time": 32400,
          "daily_till_time": 64800,
          "days_preference": [
            false,
            true,
            true,
            true,
            true,
            true,
            false
          ]
        }


    Settings:
      title: Campaign Settings object
      description: SmartReach's campaign settings object
      type: object
      required:
        - timezone
        - daily_from_time
        - daily_till_time
        - days_preference

      properties:
        timezone:
          type: string
        daily_from_time:
          type: number
          description: seconds from the start of day in the given timezone
        daily_till_time:
          type: number
          description: seconds from the start of day in the given timezone
        days_preference:
          type: array
          description: days preference starting from Sunday
          items:
            type: boolean


    CampaignStats:
      title: Campaign stats object
      description: SmartReach's campaign stats object
      type: object
      required:
        - object
        - total_sent,
        - total_opened,
        - total_clicked,
        - total_replied,
        - total_steps,
        - current_prospects,
        - current_opted_out,
        - current_completed,
        - current_bounced,
        - merge_tag_errors,
        - current_failed_email_validation,
        - current_in_progress,
        - current_unsent_prospects,
        - current_do_not_contact,
        - reply_sentiment_stats
      properties:
        object:
          type: string
          example: campaign
        total_sent:
          type: number
        total_opened:
          type: number
        total_clicked:
          type: number
        total_replied:
          type: number
        total_steps:
          type: number
        current_prospects:
          type: number
        current_opted_out:
          type: number
        current_completed:
          type: number
        current_bounced:
          type: number
        merge_tag_errors:
          type: number
        current_failed_email_validation:
          type: number
        current_in_progress:
          type: number
        current_unsent_prospects:
          type: number
        current_do_not_contact:
          type: number
        reply_sentiment_stats:
          $ref: '#/components/schemas/ReplySentimentStats'
          
    StartStopCampaign:
      title: StartStopCampaign api request structure.
      description: SmartReach's Start/Stop campaign object
      type: object
      required:
        - status

      properties:
        status:
          description: Status of campaign can be changed to 'running', 'scheduled', 'stopped'.
          type: string
          enum:
            - "running"
            - "scheduled"
            - "stopped"
      
        schedule_start_at:
          description: Required for scheduling a campaign for a specific date. The date-time with respective time-zone of schedule start should be converted to Epoch milliseconds and passed. 
          type: number
          
    ChannelSettings:
      title: Channel settings response structure
      type: object
      properties:
        channel_settings:
          type: object
          properties:
            general_settings:
              type: array
              items:
                $ref: '#/components/schemas/GeneralSetting'
            email_settings:
              type: array
              items:
                $ref: '#/components/schemas/CampaignEmailSetting'
            call_settings:
              type: array
              items:
                $ref: '#/components/schemas/CallSetting'
            linkedin_settings:
              type: array
              items:
                $ref: '#/components/schemas/LinkedinSetting'
            whatsapp_settings:
              type: array
              items:
                $ref: '#/components/schemas/WhatsappSetting'
            sms_settings:
              type: array
              items:
                $ref: '#/components/schemas/SmsSetting'
          
        
          
          
    CampaignEmailSetting:
      title: CampaignEmailSetting object
      description: SmartReach's Campaign Email Setting object
      type: object
      required:
        - object
        - id
        - team_id
        - sender_email_setting
        - receiver_email_setting
      properties:
        object:
          type: string
          example: email_settings
        id:
          type: string
        team_id:
          type: string
        sender_email_setting:
          # type: object
          $ref: '#/components/schemas/EmailSetting'
        receiver_email_setting:
          # type: object
          $ref: '#/components/schemas/EmailSetting'
          
    EmailSetting:
      title: EmailSetting object
      description: SmartReach's EmailSetting object
      type: object
      required:
        - object
        - id
        - owner_id
        - service_provider
        - first_name
        - last_name
        - email
        - min_delay_seconds
        - max_delay_seconds
      properties:
        object:
          type: string
          example: email_setting
        id:
          type: string
        owner_id:
          type: string
        service_provider:
          type: string
          enum:
            - gmailapi
            - gmail_alias
            - gmail_asp
            - mailgun
            - sendgrid
            - office365
            - exchange
            - amazon_ses
            - rediff
            - namecheap
            - other
        first_name:
          type: string
        last_name:
          type: string
        email:
          type: string
        cc_emails:
          type: string
        bcc_emails:
          type: string
        created_at:
          type: string
        min_delay_seconds:
          type: number
        max_delay_seconds:
          type: number
          
    CallSetting:
      title: CallSetting object
      description: SmartReach's call setting object
      type: object
      required:
        - object
        - id
        - team_id
        - phone_number
        - owner_id
        - first_name
        - last_name
        - call_limit_per_day
      properties:
        object:
          type: string
          example: call_setting
        id:
          type: string
        team_id:
          type: string
        phone_number:
          type: string
          example: +15416314831
        owner_id:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        call_limit_per_day:
          type: number
          
    LinkedinSetting:
      title: LinkedinSetting object
      description: SmartReach's linkedin setting object
      type: object
      required:
        - object
        - id
        - team_id
        - email
        - owner_id
        - first_name
        - last_name
        - view_profile_limit_per_day
        - inmail_limit_per_day
        - message_limit_per_day
        - connection_request_limit_per_day
      properties:
        object:
          type: string
          example: linkedin_setting
        id:
          type: string
        team_id:
          type: string
        email:
          type: string
        owner_id:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        view_profile_limit_per_day:
          type: number
        inmail_limit_per_day:
          type: number
        message_limit_per_day:
          type: number
        connection_request_limit_per_day:
          type: number
        profile_url:
          type: string
    
    SmsSetting:
      title: SmsSetting object
      description: SmartReach's sms setting object
      type: object
      required:
        - object
        - id
        - team_id
        - owner_id
        - phone_number
        - first_name
        - last_name
        - sms_limit_per_day
      properties:
        object:
          type: string
          example: sms_setting
        id:
          type: string
        team_id:
          type: string
        phone_number:
          type: string
        owner_id:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        sms_limit_per_day:
          type: number
    
    GeneralSetting:
      title: GeneralSetting object
      description: SmartReach's general setting object
      type: object
      required:
        - object
        - id
        - team_id
        - owner_id
        - quota_per_day
      properties:
        object:
          type: string
          example: general_setting
        id:
          type: string
        team_id:
          type: string
        owner_id:
          type: string
        quota_per_day:
          type: number
      
    WhatsappSetting:
      title: WhatsappSetting object
      description: SmartReach's whatsapp setting object
      type: object
      required:
        - object
        - id
        - team_id
        - owner_id
        - first_name
        - last_name
        - whatsapp_number
        - whatsapp_message_limit_per_day
      properties:
        object:
          type: string
          example: whatsapp_setting
        id:
          type: string
        team_id:
          type: string
        owner_id:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        whatsapp_number:
          type: string
        whatsapp_message_limit_per_day:
          type: number
      
  requestBodies:
    StartStopCampaign:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/StartStopCampaign'


  responses:
    BadRequest:
      description: Bad request error responses
      content:
        application/json:
          schema:
            type: object
            properties:
              errors:
                type: array
                items:
                  type: object
                  required:
                    - error_type
                    - message
                  properties:
                    error_type:
                      type: string
                    message: 
                      type: string

    ServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            type: object
            properties:
              error_type:
                type: string
              message:
                type: string
          example: 
            - error_type: api_error
              message: Error while fetching data

    ForbiddenError:
      description: User not authorized
      content:
        application/json:
          schema:
            type: object
            properties:
              error_type:
                type: string
              message:
                type: string
          example: 
            - error_type: forbidden
              message: Campaign does not belong to the current team. Please switch view to the respective team.
    
    NotFoundError:
      description: Record not found
      content:
        application/json:
          schema:
            type: object
            properties:
              error_type:
                type: string
              message:
                type: string
          example:
            - error_type: not_found
              message: No Campaign found.